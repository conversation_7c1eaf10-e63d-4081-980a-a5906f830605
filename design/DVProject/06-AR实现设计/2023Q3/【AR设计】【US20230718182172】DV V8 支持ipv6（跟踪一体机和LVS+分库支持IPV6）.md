## 【 story信息】

*<span style="color:#0066cc;">列出本章节涉及的SR列表：SR业务编号、SR标题。</span>*

- Story ID：US20230718182172
- Story 标题：【工程部署】DV V8支持Ipv6（一体机 +LVS+分库场景场景）

## 【需求背景】

<span style="font-size:18px;">**需求编号**</span>	
IR20230706003521

<span style="font-size:18px;">**需求名称**</span>
DV V8 支持ipv6（跟踪一体机和LVS+分库支持IPV6）

<span style="font-size:18px;">**需求背景**</span>	
同  需求  DV V8 支持ipv6， 在本季度需求中补齐 Q1中没有做进去和裁剪的 **IPV6一体机功能** 和 **LVS_IPV6功能（包含大容量和合设集群）**
https://codehub-y.huawei.com/U2020-S/DVProject/wiki/edit/760331

## 【需求说明】

<span style="font-size:18px;">**需求分析**</span>

- 支持**一体机IPV6单双栈**场景
- 支持大容量IPV6单双栈的**性能分库场景+LVS_IPV6**
- 支持合设集群组网IPV6单双栈的**性能分库场景+LVS_IPV6**，支持合设集群IPV6单双栈的扩容场景
- 支持高规格合设集群组网IPV6单双栈的**性能分库场景+LVS_IPV6**，支持高规格合设集群IPV6单双栈的扩容场景

## 【预期用例】

*<span>**用例展示**
</span>*

![image](./3aac35d2-72cb-486f-b153-1b4796c11d08.png)

---

## 【方案】

*<span>

#### 场景一：

一体机IPV6单双栈支持，增加ALL_IN_ONE_IPV6相关四个参数
![image](./4d571a29-1ce7-4934-9b61-db83e7736900.png)

#### 场景二：

大容量 性能分库和PV6支持，增加PM的IPV6两参数和LVS_IPV6的四参数

![image](./da2deb73-e869-47f7-861c-bc214b737c55.png)

单栈场景中LVS配置截图，包含IPV6的VIP和DIP
![image.png](./04a80575-350f-4a70-8150-a79ea2cd2024.png)

双栈场景中LVS配置截图，，包含IPV6和IPV4的VIP和DIP
![image.png](./8e56b1f3-90b1-4f35-b659-43abb8fd0721.png)

#### 场景三：

标准合设集群扩容节点 性能分库和LVS_IPV6支持，增加LVS_IPV6四参数和扩容节点两参数
![image](./73d95a33-bdf2-4fee-a0ac-4d131da3a031.png)
</span>*

IPV6单栈场景中LVS配置截图：
![image](./2fd9a415-0065-4b78-a39e-96f8c14ded35.png)

IPV4-6双栈场景中LVS配置截图：
![image](./6d98e76d-54fc-44b6-af0d-ae2bc9cfe566.png)

#### 场景四：

高规格合设集群 性能分库和LVS_IPV6支持，增加LVS_IPV6四参数
![image](./9e752ccf-b594-4f32-b1b0-2d9925a9fada.png)

IPV6单栈场景中LVS配置截图：
![image](./593b09af-bca9-4b36-b282-bab4c45e2c47.png)

IPV4-6双栈场景中LVS配置截图：

![image](./baa9c015-b13f-4bea-8286-8026b5ebe9d0.png)

#### -  代码逻辑

*<span style="color:#0066cc;">[大容量IPV6 + LVS\_IPV6 的安装代码合入 (huawei.com)](https://codehub-y.huawei.com/U2020-S/DVProject/merge_requests/2987)
</span>*
① 识别当前IPV4的代码并扩容代码内容支持IPV6单双栈（增量代码）
② 增加合设集群IPV6单双栈场景的扩容支持，识别扩容中的IPV4代码并兼容IPV6单双栈（增量代码）
③ 调整LVS的网络信息文件，使其同时兼具IPV4和IPV6的信息，在单栈场景中选择性删除部分信息后再调用模版，在双栈场景中则直接使用LVS的网络信息模版

![image](./24a341fa-cfae-40cc-ac6b-e8b3b9035ac8.png)

#### 定位指南

[虚机版本GLB介绍 - 李得鑫的博客 (huawei.com)](http://3ms.huawei.com/km/blogs/details/12666881?l=zh-cn#13-%E9%80%9A%E8%BF%87%E5%AF%BC%E5%85%A5%E8%A7%84%E5%88%92%E6%95%B0%E6%8D%AE%E7%9A%84%E6%96%B9%E5%BC%8F%E9%85%8D%E7%BD%AEglb)

*<span style="color:#0066cc;"></span>*

#### 范围一的关键代码逻辑顺序图：

#### -  代码实现

*<span style="color:#0066cc;">

</span>*

---

### 【二次开发文档】

### 【约束说明】

*<span>

</span>*

### 【优化点说明】

*<span style="color:#0066cc;">[虚机版本GLB介绍 - 李得鑫的博客 (huawei.com)](http://3ms.huawei.com/km/blogs/details/12666881?l=zh-cn#13-%E9%80%9A%E8%BF%87%E5%AF%BC%E5%85%A5%E8%A7%84%E5%88%92%E6%95%B0%E6%8D%AE%E7%9A%84%E6%96%B9%E5%BC%8F%E9%85%8D%E7%BD%AEglb)</span>*

---

#### 【对外接口（其他模块）】

*<span> 如果涉及到对外接口的变化，新增、修改、删除等，给出接口或变更描述。并刷新接口基线清单和接口变更清单。
</span>*

#### 【外部依赖（其他模块）】

*<span style="color:#0066cc;">如果涉及对外依赖需求，给出依赖方提供的接口、方案或设计文档等。
</span>*

#### 【系统外部接口(和其他产品）】

*<span style="color:#0066cc;">如果因为系统设计涉及到外部接口的变更，需要在这里说明变更项，以及变更原因。
具体的变更直接刷新相应的外部接口文档。
</span>*

#### 【升级、割接、容灾场景要求】

- **安装**  *<span style="color:#0066cc;">支持IPV6单双栈/span>*
- **升级**  *<span style="color:#0066cc;">支持IPV6单双栈</span>*
- **割接**  *<span style="color:#0066cc;">不涉及</span>*
- **容灾**  *<span style="color:#0066cc;">需支持容灾（当前无环境验证）</span>*
- **扩容**  *<span style="color:#0066cc;">支持IPV6单双栈</span>*

#### 【兼容性要求】

*<span></span>*

#### 【可靠性要求】

*<span style="color:#0066cc;">预置支持失败重入，如失败无法解决，要卸载重新预置。
</span>*

#### 【可维护性要求】

*<span style="color:#0066cc;">识别是否满足可维护性的要求或者约束，若有要提炼出来；
</span>*

#### 【安全性要求】

*<span>
重点安全存在场景
1、安装升级扩容过程中的秘钥密码等需严格遵守安全规范

</span>*

#### 【开源引入】

*<span style="color:#0066cc;">无。</span>*


