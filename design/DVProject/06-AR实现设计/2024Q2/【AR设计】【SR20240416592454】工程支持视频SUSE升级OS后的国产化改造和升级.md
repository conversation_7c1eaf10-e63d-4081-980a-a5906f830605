## 【 story信息】

*<span style="color:#0066cc;">列出本章节涉及的SR列表：SR业务编号、SR标题。</span>*

- Story ID：SR20240416592454
- Story 标题： 工程支持视频SUSE升级OS后的国产化改造和升级
  https://api.idp.huawei.com/edit/static/commoneditor/IdpLiteView/editor.html?docId=4e08c356-3b6d-4cbe-a5e4-cb440e7cf569&mode=edit&username=p00803324&from=com.huawei.xingyunplt&tm=20240422215124603&language=zh-cn

## 【需求背景】

融合视频现网进行国产化改造，改造的流程是：

1、使用OS升级工具将OS从SUSE12.5升级到Euler2.10，升级过程中不会动业务应用的数据和配置。 -- 融合视频提供OS升级工具。

2、恢复DV应用和数据库，提供升级或者恢复步骤，使得应用可以在新的Euler2.10操作系统上正常运行。

DV版本：V800R001C60、V800R023C00SPC103

## 【需求说明】

基于R23SPC103版本的基础上进行国产化改造，改造后，使用SUSE12.5 构建的包在欧拉2.10上运行

## 【预期结果】

*<span style="color:#0066cc;">需求预期实现结果，及最终实现效果图、配置使用方法等说明。
</span>*
---

## 【方案】

![image](./img/b0431e62-e5a0-46dc-b4a1-6c7ca8616728.jpg)
-----

*<span style="color:#0066cc;">描述方案内容。
</span>*
工程全景图：https://onebox.huawei.com/v/********************************

#### - 新增一个国产化包

CI 在R23C00SPC107版本出一个国产化改造包：

<pre class="screen" id="screen108661644133512"><p class="p" id="p1653612192331">DigitalView-SW_V800R023C00SPC107_Adapting-SuSE-To-EulerOS_EulerOS-x86-64.zip</p><p class="p" id="p12536151916338">   --- dv_adapting_suse_to_euleros.sh ---国产化改造脚本</p><p class="p" id="p053631903319">   --- osconfig-23.70.116-euler_amd64.zip --- osconfig欧拉X86包</p><p class="p" id="p1253711953319">   --- osconfig-23.70.116.zip --- osconfig SUSE X86包，用于回退</p></pre>

使用方法
将国产化改造包上传到uniep节点，合设集群上传到首节点的“/opt”目录下，解压软件包，并修改权限，参考命令

```
unzip -d  dv_adapting_suse_to_euleros DigitalView-SW_V800R023C00SPC107_Adapting-SuSE-To-EulerOS_EulerOS-x86-64.zip
chmod 755 -R /opt/dv_adapting_suse_to_euleros

```

升级成功的标志

```
upgrade all nodes successful

```

升级成功的标志

```
rollback all nodes successful

```

#### -  代码逻辑设计（存量原始逻辑+你增量描述和上下文描述+你代码逻辑）

*<span style="color:#0066cc;">用简短的文字描述各模块分配需求的变化， 尽量使用结构化的语言。</span>*

##### 1. 脚本校验

只在OS在欧拉，osconfig为SUSE是执行，特殊场景，OS在欧拉，但是osconfig虽然是欧拉，但是没有安装成功，这种情况下脚本也会执行，
判断osconfig的版本使用该目录
/usr/local/osconfig/os

```
[root@host-10-136-239-169 os]# ll
total 48
dr-xr-x--- 2 <USER> <GROUP> 4096 May 13 15:40 bin
drwxr-x--- 2 <USER> <GROUP> 4096 May 13 15:40 conf
dr-xr-x--- 4 <USER> <GROUP> 4096 May 30  2020 default
dr-xr-x--- 3 <USER> <GROUP> 4096 May 30  2020 etc
dr-xr-x--- 3 <USER> <GROUP> 4096 May 13 15:40 EulerOS-2.0SP10
dr-xr-x--- 3 <USER> <GROUP> 4096 May 13 15:40 EulerOS-2.0SP11
dr-xr-x--- 3 <USER> <GROUP> 4096 May 13 15:40 EulerOS-2.0SP12
dr-xr-x--- 3 <USER> <GROUP> 4096 May 13 15:40 EulerOS-2.0SP8
dr-xr-x--- 3 <USER> <GROUP> 4096 May 13 15:40 EulerOS-2.0SP9
dr-xr-x--- 3 <USER> <GROUP> 4096 May 13 15:40 Kylin-V10
dr-x------ 2 <USER> <GROUP> 4096 May 30  2020 lbin
dr-xr-x--- 6 <USER> <GROUP> 4096 May 13 15:40 pyscripts


```

安装成功是否成功使用日志判断
/usr/local/osconfig/logs/install_osconfig.log

##### 2.  升级具体实现内容

|序号| 问题 | 解决办法 |
| --- | --- | --- | 
|1 | alias别名丢失，原因是SUSE升级欧拉后，.bashrc环境变量引用不到| 在DV所有用户的.profile中增加如下语句：[ -f ~/.bashrc ] && . ~/.bashrc | --- |
| 2                                                                                                                                          | haveged没有启动，导致redis异常 | 启动haveged，并且保证haveged自启动正常 |
| 3                                                                                                                                          | 操作系统核心参数被还原 | 将预置的操作系统设置重新进行一遍设置（/etc/sysctl.conf+limits.conf+环境变量），保证升级后DV的信息正常 |
| 4                                                                                                                                          | /usr/bin/newgrp 权限丢失 | chmod 4755 /usr/bin/newgrp |
| 5                                                                                                                                          | osconfig 从suse升级到欧拉失败 | 重装osconfig为欧拉版本，重装方案如下：su - ossadm -c "sh /usr/local/osconfig/os/bin/uninstall.sh"解压新的osconfig到目录，可用/tmp/osconfigchown ossadm:ossgroup -R /tmp/osconfigsu - ossadm -c "sh /tmp/osconfig/install.sh -sudobinpath /usr/local/uniepsudobin"**​ sudoconfig做了下对比，没有差异，不用做重装替换** |
| 6                                                                                                                                          | 因为环境变量等等原来，SUSE升级到欧拉OS后，服务启动异常 | 登录所有节点启动服务：stopnodestartnode**其中数据库节点要先启动，再启动其他节点，为了提高效率，数据库多节点支持并发，其他节点也要支持并发** |
| 7                                                                                                                                          | SUSE版本升级欧拉版本，90%升级失败 | 规避方法（看起来是已知问题，但是也可以是suse升级欧拉引起的）见：https://3ms.huawei.com/km/blogs/details/14574703rm -f /opt/oss/manager/tools/deployapp/postDeploy/97\_post\_rollback\_delete\_dmq\_data.sh​**当前方案中虽然不升级DV大版本，需要进行预埋，需要测试把uniep节点上的这个脚本提前删除**​**，再升个欧拉版本看效果，避免后续视频配套后升级失败** |
| 8                                                                                                                                          | 支持回滚 | 在SUSE升级欧拉回退后，需要支持回滚处理（重装osconfig等） |

##### 3.  回滚实现

和升级逻辑完全一致，只回退升级成功和失败的节点，不处理未升级的节点

##### 4.  支持安全加固场景

脚本运行逻辑为利用ossadm互信，登录到远程服务器，再切换到root执行改造脚本，因本次改造需要修改OS配置，所以root密码是必须的，但是不会直连root

##### 5. 支持重入

脚本逻辑分两部分

1. 升级替换osconfig
2. 重启整个产品的服务

对升级osconfig而言，重入就是重装
升级场景，重启服务的重入是再次重启
回退场景，也有重启服务的步骤，在没有节点不需要回退的场景，重启服务的步骤不会执行
升级场景，如果所有节点都执行成功后，重入重启服务也不会执行

升级 回退后脚本都会检查整个环境的运行状态，如果检查失败，脚本也会报错

##### 6. 支持并行

升级和回退都是并行的，脚本的主要耗时在节点重启上
节点执行的日志不会打印出来，因为是切换到root执行，存在打印出root密码的风险，所有远程执行的屏显都是关闭的，但因为脚本逻辑比较检查，日志也只有一个，脚本在报错的日志会打印出失败节点和需要查看的日志，根据日志可以一路向上查找到报错的日志，脚本设计的退出出口之后一个，不会中途退出

##### 7. OS参数修改

1. 系统参数修改
   
   ```
       adjust_sysctl_item "kernel.shmmax" "4294967296"
       adjust_sysctl_item "net.ipv4.tcp_adv_win_scale" "2"
       adjust_sysctl_item "net.core.somaxconn" "4096"
       adjust_sysctl_item "net.core.netdev_max_backlog" "4096"
       adjust_sysctl_item "fs.nr_open" "2000000"
       adjust_sysctl_item "fs.file-max" "2000000"
       adjust_sysctl_item "net.ipv4.ip_local_port_range" "40000 65000" "0"
       adjust_sysctl_item "vm.nr_hugepages" "0" "0"
       adjust_sysctl_item "vm.max_map_count" "655360"
       adjust_sysctl_item "kernel.pid_max" "240000"
       adjust_sysctl_item "kernel.threads-max" "240000"
                                      fs.protected_hardlinks  0
   ```
   
   基于现网多次出现误改 net.ipv4.ip_forward 的问题，本参数脚本不会修改，该参数只在容器化和LVS场景影响功能，不涉及视频场景
2. limits.conf 修改
   
   ```
   ossuser - nproc 36384
   ossuser soft nofile 1200000
   ossuser hard nofile 1200000
   root soft nofile 1200000
   root hard nofile 1200000
   ```
3. 用户.profile 修改
   下面的用户都处理了，但是我们做了别名的只有ossadm 和ossuser
   
   ```
   "ossadm" "ossuser" "sysomc" "dbuser" "sopuser" "sftpossuser" "sshossuser" "devdata"
   
   ```

##### 8.  升级之后的验证点

1. DV产品全部服务运行正常
2. haveged 在每个节点都是启动的，并且已经设置为开机自启 检查方法
   
   ```
   [root@host-10-136-239-169 ~]# systemctl is-enabled haveged
   enabled
   [root@host-10-136-239-169 ~]# systemctl status  haveged
   ● haveged.service - Entropy Daemon based on the HAVEGE algorithm
      Loaded: loaded (/usr/lib/systemd/system/haveged.service; enabled; vendor preset: disabled)
      Active: active (running) since Sat 2024-05-11 15:08:22 CST; 2 days ago
    Main PID: 52413 (haveged)
       Tasks: 1
      Memory: 3.3M
      CGroup: /system.slice/haveged.service
              └─52413 /usr/sbin/haveged -w 1024 -v 1 --Foreground
   
   Notice: journal has been rotated since unit was started, output may be incomplete.
   
   ```
3. 关注自定义别名  ossadm ossuser 相关别名
   
   ```
   su - ossadm
   [ossadm@host-10-136-239-169 ~]$ alias
   alias ..='cd ..'
   alias ...='cd ../..'
   alias atop='export TERM=xterm-basic;atop'
   alias egrep='egrep --color=auto'
   alias fgrep='fgrep --color=auto'
   alias forbiddendual='sh /opt/oss/manager/apps/HyperHAAgent/bin/hactl freeze node'
   alias grep='grep --color=auto'
   alias l.='ls -d .* --color=auto'
   alias ll='ls -l --color=auto'
   alias ls='ls --color=auto'
   alias lsof='lsof -K i'
   alias restartapp='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd restartapp'
   alias restartdc='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopdc;ipmc_adm -cmd startdc'
   alias restartnode='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopnode;ipmc_adm -cmd startnode'
   alias resumedual='sh /opt/oss/manager/apps/HyperHAAgent/bin/hactl unfreeze node'
   alias startapp='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd startapp'
   alias startdc='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd startdc'
   alias startnode='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd startnode'
   alias statusapp='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd statusapp'
   alias statusdual='sh /opt/oss/manager/apps/HyperHAAgent/bin/hactl status resource'
   alias stopapp='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopapp'
   alias stopdc='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopdc'
   alias stopnode='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopnode'
   alias switchdual='sh /opt/oss/manager/apps/HyperHAAgent/bin/hactl switch resourcegroup'
   alias vi='vim_atae'
   alias view='vim_atae'
   alias vim='vim_atae'
   alias xzegrep='xzegrep --color=auto'
   alias xzfgrep='xzfgrep --color=auto'
   alias xzgrep='xzgrep --color=auto'
   alias zegrep='zegrep --color=auto'
   alias zfgrep='zfgrep --color=auto'
   alias zgrep='zgrep --color=auto'
   su - ossuser
   [ossuser@host-10-136-239-169 ~]$ alias
   alias ..='cd ..'
   alias ...='cd ../..'
   alias atop='export TERM=xterm-basic;atop'
   alias cfg='cd /opt/oss/share/SOP/DVEngineeringService/I2000/run/etc'
   alias egrep='egrep --color=auto'
   alias fgrep='fgrep --color=auto'
   alias grep='grep --color=auto'
   alias l.='ls -d .* --color=auto'
   alias ll='ls -l --color=auto'
   alias log='cd /opt/oss/share/SOP/DVEngineeringService/I2000/run/var'
   alias logr='cd /opt/oss/share/SOP/DVEngineeringService/I2000/run/var/iemp/log'
   alias ls='ls --color=auto'
   alias lsof='lsof -K i'
   alias omcversion='sh /home/<USER>/omcversion.sh'
   alias p='i2kadm status ALL p'
   alias rci2k='source /opt/oss/share/SOP/DVEngineeringService/I2000/bin/rci2k.sh'
   alias re='i2kadm stop ALL && i2kadm start ALL'
   alias run='cd /opt/oss/share/SOP/DVEngineeringService/I2000/bin'
   alias startapp='i2kadm start ALL'
   alias stopapp='i2kadm stop ALL'
   alias sync2hofs='sh /opt/oss/SOP/apps/DVEngineeringService/bin/sync2hofs/sync2hofs.sh'
   alias version='i2kadm -v'
   alias vi='vim_atae'
   alias view='vim_atae'
   alias vim='vim_atae'
   alias xzegrep='xzegrep --color=auto'
   alias xzfgrep='xzfgrep --color=auto'
   alias xzgrep='xzgrep --color=auto'
   alias zegrep='zegrep --color=auto'
   alias zfgrep='zfgrep --color=auto'
   alias zgrep='zgrep --color=auto'
   ```
4. 是否影响后续升级

##### 9.  国产化包使用方法

###### 9.1 安装iDploye

###### 9.2 导入升级包和介质包

###### 9.3 创建升级任务

#### -  代码实现设计（planuml或cloudmodeling画图+描述）

*<span style="color:#0066cc;">贴merge提交链接</span>*

#### -  代码测试设计（CIDA 脑图+描述）

*<span style="color:#0066cc;">贴merge提交链接</span>*

---

### 【二次开发文档】

### 【DFX设计】

1. 支持失败可重入
2. 支持改造成功后，不再继续进行改造
3. 支持回滚且在任何失败节点回滚成功

### 【约束说明】

*<span style="color:#0066cc;">约束描述
</span>*

1. 本需求只应用于suse改造欧拉X86，不适应于通用suse
2. 不涉及IPV6
3. 涉及一体机、多节点单机、合设集群、大容量、标准集群5个组网

### 【优化点说明】

*<span style="color:#0066cc;">后续补充优化功能点说明</span>*

---

#### 【对外接口（其他模块）】

*<span style="color:#0066cc;">如果涉及到对外接口的变化，新增、修改、删除等，给出接口或变更描述。并刷新接口基线清单和接口变更清单。
</span>*

#### 【外部依赖（其他模块）】

*<span style="color:#0066cc;">如果涉及对外依赖需求，给出依赖方提供的接口、方案或设计文档等。
</span>*

#### 【系统外部接口(和其他产品）】

*<span style="color:#0066cc;">如果因为系统设计涉及到外部接口的变更，需要在这里说明变更项，以及变更原因。
具体的变更直接刷新相应的外部接口文档。
</span>*

#### 【升级、割接、容灾场景要求】

- **升级**  *<span style="color:#0066cc;">不涉及</span>*
- **割接**  *<span style="color:#0066cc;">不涉及</span>*
- **容灾**  *<span style="color:#0066cc;">不涉及</span>*

#### 【兼容性要求】

不涉及

*<span style="color:#0066cc;">识别是否存在影响兼容性情况，包括且不限于：API、接口、lib/so 名字，发布包名字，参数结构等。
根据需求分析阶段输出的《版本兼容性分析和说明》，针对每个不兼容点进行方案设计，确保做到后向兼容。
</span>*

#### 【可靠性要求】

不涉及

*<span style="color:#0066cc;">识别是否存在可靠性方面的要求或者约束，若有要提炼出来。
</span>*

#### 【可维护性要求】

不涉及

*<span style="color:#0066cc;">识别是否满足可维护性的要求或者约束，若有要提炼出来；
</span>*

#### 【安全性要求】

不涉及

*<span style="color:#0066cc;">识别安全场景，及安全威胁分析等，安全checklist 自检结果，添加安全checklist归档路径。
</span>*

#### 【开源引入】

不涉及

*<span style="color:#0066cc;">引入开源、三方件、外购件等，需要说明原因、评审结论、参与评审人员。</span>*


