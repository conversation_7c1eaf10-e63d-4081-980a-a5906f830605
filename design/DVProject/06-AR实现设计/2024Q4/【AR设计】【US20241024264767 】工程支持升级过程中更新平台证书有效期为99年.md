## 【 story信息】

- Story ID：US20241024264767
- Story 标题： 工程支持升级过程中更新平台证书有效期为99年

## 【需求背景】

需求设计链接：

https://xingyun.rnd.huawei.com/apps/function-design#/functionDesign/263461466/feature/f20a2633-dec6-43d8-9622-da1bc758ba37/document/f20a2633-dec6-43d8-9622-da1bc758ba37/1

SR20241024258217 工程支持升级过程中更新平台证书有效期为99年

![image](./img/f10251cc-b8fa-43d0-96e3-268286478c86.png)

## 【需求说明】

来源于证书管理SDT，要求公司的所有产品升级的时候可以更新证书有效期为99年，目前经过评估，DV自身无法直接替换，涉及到更换CA，影响外部，只能替换平台相关的证书，更新为99年（仅为满足要求，实际就换了平台的证书）

同时因为替换证书还会增加升级时长，所以经过内部决策讨论，采用开关（默认不开）+仅调度平台的证书有效期延长脚本

## 【预期结果】

采用开关（默认不开）+仅调度平台的证书有效期延长脚本，替换平台相关的证书，更新为99年

## 【方案】

1. 工程预置配置参数新增开关 is_extend_internal_certificate_validity_period ，默认为False
2. 开关开启时，升级调用平台接口，更新证书有效期99年
3. DepCloud流水线中也同步增加对应参数，默认值为False，正常不需要人填写，通过该参数传递到升级流程中

#### -  代码逻辑设计（存量原始逻辑+你增量描述和上下文描述+你代码逻辑）

图1 ​工程支持升级过程中更新平台证书有效期为99年流程图

![](https://resource.idp.huawei.com/idpresource/nasshare/editor/image/206325645204/1_zh-cn_image_0000002057302774.png)

#### -  代码实现设计（planuml或cloudmodeling画图+描述）

1.所有组网配置文件新增配置参数：

```
is_extend_internal_certificate_validity_period
是否扩展内部证书有效期
取值范围[False,True]，忽略大小写
默认值：False
```

涉及的配置文件：
DV_Config_AllInOne.config
DV_Config_Cluster.config
DV_Config_LargeCapacity.config
DV_Config_MergeCluster.config
DV_Config_Test.config

该参数仅升级使用，安装不用，如果安装配了True。则代码还原为False

2.修改所有组网产品信息的 product*.json ，根据新增参数映射对应值。
增加参数：

```
is_extend_internal_certificate_validity_period
取值范围[False,True]，忽略大小写
默认值：False
由DV_Config*.config映射值

B版本升级，由于第一个版本新增了该参数后，值为True时继承。
所以需要判断导出的产品信息如果已经存在参数 is_extend_internal_certificate_validity_period ，并且值为True时继承。如果不存在，则，从 DV_Config*.config映射值

安装，由于模板中新增该参数，所以安装还是带上了。安装这个参数值是默认值。

1）升级前导出的产品信息中参数 is_extend_internal_certificate_validity_period 是True时，继承升级前的。
2）升级前导出的产品信息中参数 is_extend_internal_certificate_validity_period 不存在，根据 DV_Config*.config映射值
3）升级前导出的产品信息中参数 is_extend_internal_certificate_validity_period 是False时，
     DV_Config*.config配置的是True时。
     修改产品信息中参数 is_extend_internal_certificate_validity_period 为True。



```

3.修改 software_define.yaml 添加新增参数。

```
增加参数：
is_extend_internal_certificate_validity_period
取值范围[False,True]，忽略大小写
默认值：False
由product*.json值决定
部署页面隐藏，值从 product*.json 映射得来。
```

4.修改管理面升级脚本。upgrade_manager.sh

```
1. upgrade_uniep函数后增加update_uniep_certificate_days函数
2. 函数中进行如下处理：
2.1  从${CURRENT_PATH}/tmp_product_info/product*.json中获取is_extend_internal_certificate_validity_period的值，如果是True，继续进行，False直接退出
2.2 判断IR证书是否为99年
/opt/oss/manager/etc/ssl/internal/server.cer中的
            Not Before: Oct 23 00:00:00 2024 GMT
            Not After : Oct 17 00:00:00 2054 GMT
时间差是否达到99年，如果已经大于等于，退出，其他场景继续进行
2.3  调度管理面证书更新脚本
${CloudSOP-UniEP安装路径}/apps/UniEPService/tools/common/update_cert_days_for_upgrade.sh -tenant all（脚本具体调度接口见 CloudSOP R25C10最新的集成接口说明书 22.14章节）
2.4 重启uniep服务
source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd restartapp -tenant manager
遍历所有节点进行调度
2.5 重启后等待UniEP服务正常参考DepCloud重启uniep后的IR接口正常判断实现
```

5.一键式升级步骤升级后置定制处理脚本：post_upgrade.sh

```
最后增加一个update_cloudsop_certificate_days函数
1. 导出product*json
2.  从导出的product*.json中获取is_extend_internal_certificate_validity_period的值，如果是True，继续进行，False直接退出
3. 调度业务面证书更新脚本
sudo -u "secuser" sh /opt/oss/SOP/apps/SnbCertMgmtService/bin/localCertUpdate.sh
要求在其中任意一个SnbCertMgmtService节点调度即可，选择为：
一体机：当前节点
合设集群：当前节点
多节点单机：DB节点
集群：DB备节点  --安装时初始备机
大容量集群：SM节点  --SM1即可

4.调用完，需要重启一下服务 SnbCertMgmtService
 A. 只重启 SnbCertMgmtService 服务
 B. 只在调用修改脚本的节点执行重启

  

```

6.资料部分

```
is_extend_internal_certificate_validity_period
升级资料中补充该参数说明，且说明影响，如果开启该参数，预期会增加30分钟左右的升级时长

安装提示给默认值即可。该参数升级使用。安装不用。

```

7.CD流水线升级修改，新增该参数，并根据填写值，配置到 DV_Config*.config

```
DepCloud流水线中也同步增加对应参数，默认值为False，正常不需要人填写，通过该参数传递到升级流程中
```

#### -  代码测试设计（CIDA 脑图+描述）

管理面 查看 uniep节点 （一体机，多节点单机，标准集群，大容量组网，查看节点1，合设集群节点1，节点2）：
/opt/oss/manager/etc/ssl/internal/server.cer

业务面 （ 查看SnbCertMgmtService服务所在节点，一体机：当前节点，合设集群：节点1，节点2 ，多节点单机：DB节点，集群：DB主备节点  ，大容量集群：SM节点  ）：
/opt/oss/SOP/etc/ssl/internal/server.cer

判断IR证书是否为99年
查看对应节点上的目录下的证书文件server.cer 中的
Not Before: Oct 23 00:00:00 2024 GMT
Not After : Oct 17 00:00:00 2054 GMT
时间差是否达到99年，如果大于等于99年，则表示修改成功。满足要求。

### 【相关确认记录】

![image](./img/7d457d6f-71ba-42b1-95c1-19f2582d304b.png)

### 【二次开发文档】

不涉及

### 【约束说明】

DV_Config_AllInOne_Docker.config

DV_Config_MergeCluster_Docker.config

---- 容器化的配置，不在升级场景下使用，不进行对应参数增加

### 【优化点说明】

![image](./img/e30d8973-e446-4522-a895-1efaee604771.png)

升级完管理面，后面升级产品，产品的服务本身就会重启。所以在升级管理面的时候，不需要单独去重启业务服务了

#### 【对外接口（其他模块）】

不涉及

#### 【外部依赖（其他模块）】

不涉及

#### 【系统外部接口(和其他产品）】

不涉及

#### 【升级、割接、容灾场景要求】

- \*\*升级\*\*  \*<span style="color:#0066cc;">不涉及</span>\*
- \*\*割接\*\*  \*<span style="color:#0066cc;">不涉及</span>\*
- \*\*容灾\*\*  \*<span style="color:#0066cc;">不涉及</span>\*

#### 【兼容性要求】

不涉及

#### 【可靠性要求】

不涉及

#### 【可维护性要求】

不涉及

#### 【安全性要求】

不涉及

#### 【开源引入】

不涉及


