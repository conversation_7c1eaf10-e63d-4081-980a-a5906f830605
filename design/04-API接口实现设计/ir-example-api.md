#  【汇聚任务接口设计】

## 特性简介

> 性能汇聚任务按周期执行，对原始数据进行汇聚。

<hr>

## 特性分析

### 特性接口

> 定义接口内部实现方案，结合相关的设计模式，设计代码结构，对接口内部逻辑进行分解和设计

<hr>

- `Api Version:` v1
- `URI:` /api/core/v1
- `Method:` GET/POST/PUT/DELETE
- `Api Designer URL:`

## 软件模型

> 软件实现设计是精确的“施工图纸”，是开发者之间连接的桥梁，设计先行降低技术债务
> 接口实现设计图：包含不限于类图、流程图，对于业务逻辑进行展开描述

<hr>

**类图**

```plantuml
@startuml
skinparam groupInheritance 3

class MarsRoverState{
 + {abstract} void doAction(MarsRover marsRover, Command command);
 + {abstract} MarsRoverState reverse();
 + {abstract} MarsRoverState fast();
 + {abstract} MarsRoverState common();
}
note right: 火星车状态：普通、极速、极速倒车、倒车

class FastState {
}

class ReverseState {
}

class FastReverseState {
}

class NormalState {
}

class MarsRover{
    + status: MarsRoverStatus;
    + Direction executeCommand(Command);
}
note right: 火星车实体，包含坐标，方向，执行命令

class MarsRoverStatus{
    - x: int;
    - y: int;
    - direction: Direction;
}
note right: 火星车状态信息

Enum Direction{
    N,S,E,W
}
note left: 位置枚举，包含东南西北

abstract class Command{
    + {abstract} MarsRoverStatus executeCommand(MarsRover);
}
note left: 命令抽象类, 抽象命令执行返回新方向

class ForwardCommand{
}

class RightCommand {
}

class LeftCommand {
}

MarsRoverState *-down-> MarsRover
MarsRover *--> MarsRoverStatus
MarsRoverStatus o-left-> Direction
Command -up-> MarsRoverStatus : create

FastState -down-|> MarsRoverState
ReverseState -down-|> MarsRoverState
FastReverseState -down-|> MarsRoverState
NormalState -down-|> MarsRoverState

ForwardCommand  -up-|> Command
RightCommand  -up-|> Command
LeftCommand  -up-|> Command
@enduml
```

**时序图**

```plantuml
@startuml

actor commander

participant Direction
participant MarsRover
participant State
control Command
participant MarsRoverStatus

alt  aaa
commander -> Direction #gold: land on Mars with position and direction
activate Direction

Direction -> commander : get direction
deactivate Direction

commander -> MarsRover: create MarsRover with Direction
activate MarsRover

MarsRover -> commander: get MarsRover with Direction
deactivate MarsRover

commander -> State: create Speed Mode MarsRover
activate State

State -> commander: get Fast/Normal/Reverse mode MarsRover
deactivate State

commander -> Command : run command

Command -> MarsRoverStatus :  calc new position and facing
activate MarsRoverStatus

MarsRoverStatus -> State : return new status
deactivate MarsRoverStatus

State -> MarsRover : update status

MarsRover -> commander: done

@enduml
```

## 测试设计

> 描述采用的测试种类，不限于单元测试、契约测试、API测试、E2E测试，已经测试在持续集成中的运行阶段
> :提示: **如果你的团队需要确保设计与实现强一致**: 可以通过单元测试保证设计与实现的一致性

<hr>

### 1) 测试用例

> 明确需求测试用例及验收方式

### 2) UT测试设计

> 开发者测试框架、架构、测试用例、文档化标准及实现等描述

### 3) API测试设计

> API测试框架、架构、测试用例、文档化标准及实现等描述

### 4) E2E测试设计(可选)

> 端到端集成测试测试方案描述

### 5) 接口契约(可选)

> 接口契约用于契约测试，包含接口与上下文系统或功能达成的一致，可作为需求的测试用例及验收标准

## 组件设计(可选)

> 从内部分析如何实现上述对组件或模块的功能、非功能需求，如何对其内部进行分解和设计。

<hr>

- 定义组件或模块内部的逻辑概念和逻辑关系：从组件内部所承载的业务逻辑和特点出发，结合相关的设计模式，对组件内部的逻辑进行分解和设计；
- 基于组件或模块内部逻辑分解和协作关系（模式）设计，定义各子模块的职责边界、接口，以及各子模块内部实现方式，具体到组件内部类图、流程图、状态机的设计。

组件内部设计，定义组件或模块内部的逻辑概念和逻辑关系：从组件内部所承载的业务逻辑和特点出发，结合相关的设计模式，对组件内部的逻辑进行分解和设计，定义子模块的职责和边界；

## 三方件选型(可选)

> 三方件选型报告

## 安全设计(可选)

> 防止命令注入设计、防SQL注入设计、防sudo提权设计、防XXE攻击设计、防URL重定向攻击等实现设计。

<hr>

[公共开发部防Top攻击设计模板](https://open.codehub.huawei.com/OpenBaize/Java/SecurityBaseLine/files?ref=master&filePath=doc)

## 算法设计(可选)

> 结合业务逻辑的重点内容，对关键算法进行展开描述。

## 性能设计(可选)

> 实现设计中基于性能因素做出的实现选择，比如流式JSON序列化组件选择，简要描述可在代码注释中进行说明，复杂的如测试结果等可以使用实现设计文档承载；

## 可靠性设计(可选)

> 实现设计中基于可靠性设计添加的处理，代码中只能看到可靠性设计的片段信息， 可通过实现设计文档承载可靠性设计系统化的信息；

## 需求编号列表（SR/US）

> 通过系统设计分解的软件US/AR编码
