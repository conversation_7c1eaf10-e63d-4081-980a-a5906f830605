## 【Story信息】

| US号| US标题 | 特性范围 |
| --- | --- | --- |
|US20250701895795  | 【分解给应用分析保障组】支持日志服务字段跨模板钻取 | 应用分析保障组 |

## 【需求背景】
该需求在OBP规划中，主要还剩余日志模板的钻取功能，其余部分在C10版本已开发完成
```
日志服务增强，Q2达成如下目标：
1、日志/调用链检索界面优化：
● DV日志查询界面 搜索框支持多选、联想、搜索结果多行展示、模板配置&表格样式持久化等功能；
● DV日志查询界面，打开后Layer search，查询结果集如出现多个类型的日志文件，需按照类型再按照最后更新时间（倒序）排列展示。
● 日志及调用链界面菜单需要支持打开多个页签，可以分别在不同的页签上进行点击查询。
2、UI风格统一：
DV日志与调用链界面风格统一：当前日志和调用链风格差异较大，用户体验感非常不好需要统一界面风格，UI设计（当前需求优先完成“搜索”界面horizon改造）。
```

## 【预期结果】

日志服务支持字段跨模板钻取，具备钻取规则管理和跳转钻取功能


## 【需求说明】

#### 日志模板管理，支持配置模板间的钻取规则

**1)钻取规则**
- 配置表格新增一列**钻取规则**
- 新增编辑、查看按钮
- 编辑状态下，点击编辑按钮配置钻取规则
- 查看状态下，编辑按钮不可用，点击查看按钮查看钻取规则
![image](./img_1.png)


**2)钻取规则配置**
- 编辑
  - 新建模板时可编辑钻取规则
  ![image](./img_3.png)
  - 编辑模板时可编辑钻取规则
  ![image](./img_4.png)
  - 搜索页面编辑时可编辑钻取规则
  ![image](./img_5.png)
- 查看
  - 添加了钻取规则的字段，可以点击查看按钮查看钻取规则
- 编辑规则弹窗:
  - 顶部蓝色提示框，展示规则描述和 已选规则数/最大可选规则数
  - 左侧展示模板，右侧展示字段，均支持搜索
  - 底部展示已经配置的规则列表，操作列支持删除规则

![image](./img_2.png)


**3)钻取**
- 在模板内，配置了钻取的字段会被高亮
- 鼠标点击高亮的字段上面，会出现一个下拉框
  - 下拉框展示 模板 - 字段 的对应关系
  - 点击规则就可以进行跳转
  - 如点击模板1 - 字段1， 就会打开
- 如果配置了钻取的字段，同时也是已选字段，不需要展开单元格，就能点击钻取
- 点击钻取后，会在新的页签打开点击的模板，模板顶部带有字段所对应的参数，规则为**is**
![image](./img_6.png)


## 2 业务场景描述

Feature1:  日志服务目标支持配置钻取字段

```
As a 日志服务配置模板
I want to 给字段配置钻取规则
In order to 点击字段进行钻取

Scenario: 点击日志字段进行钻取
Given 已经配置了日志服务模板
When 配置字段钻取
Then 点击日志字段进行钻取 
```

### 系统上下文与影响范围评估（必选）

*```描述需求特性与周边特性之间的关系，各部件依赖，包含工程能力，报表框架和前端组件依赖依赖，是否依赖IPRAN或者是否需要评估分域影响，以及需求对其他特性的影响。```*

不涉及

### 逻辑架构（可选，特性逻辑架构有变更时提供）

不涉及，无逻辑架构变更

### 消息序列图&说明

![image](./img_7.png)


## 【方案】


**配置钻取流程图**


![image](./img_8.png)

**点击钻取流程图**

![image](./img_9.png)

**配置钻取**
1. `TemplateDetail` 组件中的 `TablePro` 新增一列 钻取规则
2. 钻取规则列的 `render` 方法内渲染编辑和查看两个图标按钮, `EditDirllRuleIcon` 和 `DrillRuleIcon`
3. 点击编辑图标，打开弹窗 `DrillRuleDialog`, 点击查看图标，打开同一个弹窗，但没有勾选框，也没有删除按钮
4. 调用 `/rest/dvlogmatrixwebsite/v1/dvlogmatrixconfigservice/queryConfig` 接口，查询钻取规则数量限制 `field_drill_rule_limit_num`
5. 编辑完成后，点击保存，将钻取规则配置入库

**点击钻取**
1. 查询 `/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryCustomByIndexName` 获取模板配置项
2. 如字段含有钻取规则配置，则对字段进行高亮并用图标标识
3. 点击钻取字段，打开 `DrillRuleDropDown` 下拉框，里面是配置的钻取规则
4. 点击钻取规则，在新的页签打开模板，并带有字段对应的参数


### 关键数据结构和模型设计

*```关键模型类图和数据库表结构描述,和是否涉及业务连续性（例如切换高斯DB）,并做详细分析，是否涉及升级和数据迁移```*

*```如果涉及复杂的配置，需要描述配置模型设计。如果涉及到新模型（非龙井模型方式），需要列出详细描述模型属性和指标```**

### API接口详细设计

*```描述特性对外接口设计和内部模块间设计```*

*```对外服务化接口建议统一按照swagger规范设计。```*

*```内部模块SDK接口提供接口定义代码。```*

*```对外服务化接口建议统一按照swagger规范设计。附带接口涉及文档```*

#### 涉及接口

不涉及接口新增，使用已有接口

##### 1、获取日志服务配置项
```
/rest/dvlogmatrixwebsite/v1/dvlogmatrixconfigservice/queryConfig
```
```js
    post:
      summary: '查看logMatrix后台配置'
      description: '查看logMatrix后台配置'
      tags:
        - DvLogMatrixConfigWebsite
      operationId: queryConfig
      produces:
        - application/json
      consumes:
        - application/json
      parameters:
        - name: configKeyList
          in: body
          required: true
          schema:
            type: array
            items:
              type: string
```


- 入参为配置项名称，返回配置项的值


##### 2、新增模板


```
// 新增接口
/rest/dvlogmatrixwebsite/v1/dvlogretrievalconfigservice/addCustomIndexPattern
```
```js
    post:
      summary: addCustomIndexPattern
      description: addCustomIndexPattern
      tags:
        - DvLogRetrievalConfigWebsite
      operationId: addCustomIndexPattern
      x-imports: "com.huawei.cmp.logmatrix.model.IndexPatternModel"
      produces:
        - application/json
      consumes:
        - application/json
      parameters: 
        - name: indexPattern
          in: body
          required: true
          schema:
            $ref: "#/definitions/IndexPatternModel"
```

##### 3、修改模板

```
// 编辑接口
/rest/dvlogmatrixwebsite/v1/dvlogretrievalconfigservice/updateCustomIndexPattern
```
```js
    post:
      summary: updateCustomIndexPattern
      description: updateCustomIndexPattern
      tags:
        - DvLogRetrievalConfigWebsite
      operationId: updateCustomIndexPattern
      x-imports: "com.huawei.cmp.logmatrix.model.IndexPatternModel"
      produces:
        - application/json
      consumes:
        - application/json
      parameters: 
        - name: indexPattern
          in: body
          required: true
          schema:
            $ref: "#/definitions/IndexPatternModel"
```

##### 4、查询接口

**获取模板配置项**

```
/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryCustomByIndexName
```
```js
    post:
      summary: queryCustomByIndexName
      description: queryCustomByIndexName
      tags:
        - DvLogRetrievalQueryWebsite
      operationId: queryCustomByIndexName
      x-imports: "com.huawei.cmp.logmatrix.model.CustomIndexReq"
      produces:
        - application/json
      consumes:
        - application/json
      parameters:
        - name: customIndexReq
          in: body
          required: true
          schema:
            $ref: "#/definitions/CustomIndexReq"
```

#### 新增配置项
```field_drill_rule_limit_num```

- 钻取规则数限制：默认值为5
- 配置文件路径：/etc/config/cmp.config.properties

#### 新增入参

```drillRules```
- 钻取规则
- 校验规则： 最多配置五个（由配置项决定，默认为5）规则

```json
 "customFields": [
    {
        "displayed": false,
        "fieldName": "SubModule",
        "indexed": true,
        "fieldLabel": "",
        "analyzed": false,
        "presetValue": [],
        "fieldOperator": 0,
        "queried": false,
        "fieldType": "keyword",
        "drillRules": {
          "模板1": "字段1",
          "模板2": "字段2",
          "模板3": "字段3"
        }
    }
]
```

### 【二次开发文档】

### 【约束说明】

*<span style="color:#0066cc;">约束描述
</span>*

- 每个模板只能选择 1 个字段
- 单个字段最多创建 5 个规则 (暂定，可配置)
  - 后台配置项新增 钻取规则数限制 **field_drill_rule_limit_num**， 默认值为5
- 模板不设限制


### 【优化点说明】

*<span style="color:#0066cc;">后续补充优化功能点说明</span>*

---

#### 【对外接口（其他模块）】

*<span style="color:#0066cc;">如果涉及到对外接口的变化，新增、修改、删除等，给出接口或变更描述。并刷新接口基线清单和接口变更清单。
</span>*

#### 【外部依赖（其他模块）】

*<span style="color:#0066cc;">如果涉及对外依赖需求，给出依赖方提供的接口、方案或设计文档等。
</span>*

#### 【系统外部接口(和其他产品）】

*<span style="color:#0066cc;">如果因为系统设计涉及到外部接口的变更，需要在这里说明变更项，以及变更原因。
具体的变更直接刷新相应的外部接口文档。
</span>*

#### 【升级、割接、容灾场景要求】

**升级场景**
1. DFX场景，历史不带钻取字段得模板导入后功能正常（升级场景）

**导入导出场景**
1. 配置页面导入能力，可以导入带钻取字段的模板
2. 配置页面导出能力，可以导出带钻取字段的模板

#### 【安全性要求】

*<span style="color:#0066cc;">识别安全场景，及安全威胁分析等，安全checklist 自检结果，添加安全checklist归档路径。
</span>*

描述下需求的功能可能和安全相关与否，并标注如下各检查项的结果  

1）是否涉及新增界面配置，界面变更： 防止XSS注入、防止CSRF攻击、前后台参数校验 

-- **涉及**
- 前后台参数一致性校验

2）是否涉及新增接口，接口变更：接口基线、流控、鉴权、服务间访问控制、XSS（参数校验以及输出编码） --不涉及

3）是否涉及xml文件解析 ：防止XXE注入（外部实体解析、反序列化漏洞） --不涉及

4）是否涉及文件上传下载 ：参数校验、路径格式校验、签名、验签、zip炸弹 --不涉及  

5）是否涉及命令执行：防止命令注入 --不涉及  

6）是否涉及对外通信：通道加密、通信协议是否安全、是否进行身份认证（双向认证）、认证凭据是否安全、加密算法是否安全 --不涉及  

7）是否涉及后台脚本执行：sudo提权 --不涉及  

8）是否涉及操作本地磁盘：文件清理（即时清理、残留文件清理机制） --不涉及  

9）连接测试、认证类：结果只返回成功或失败，不填写具体失败原因，避免内网探测 --不涉及  

10）是否存在SQL语句执行场景：sql注入 --预编译无安全问题  

11）是否存在新增文件：文件权限基线、文件权限例外场景 --不涉及  

12）是否存在外部URL跳转场景：防URL重定向 --不涉及

13）是否存在新增证书：存量预置证书基线 --不涉及

# 验证

## 开发自测用例

*```1、	给出测试用例Wemind链接```*

---

# 相关责任人

*```SE、开发、测试、外部依赖接口人```*

- SE：钟少强
- 开发：梁程、程澈
- 测试：周艳、赵灿

# 遗留问题

