## 【Story信息】

- Story ID：
- Story 标题：网元告警诊断

## 【需求背景】

对话式运维，通过问答给出网元告警诊断步骤

## 【需求说明】

查询网元告警的定位信息，并结合告警可能原因与处理建议，给出具体的定位步骤

## 【预期结果】

1、语句询问XX网元XX告警，能够给出对应网元告警的诊断告警与处理建议
语句案例：
请问XX网元的告警60210029如何解决？
请问XX网元的告警（主机物理内存使用率过高/Linux主机磁盘分区（文件系统）使用率过高）原因是什么？请诊断

## 【方案】

#### - 方案设计：

时序图：

![](https://cloudmodelingapi.tools.huawei.com/cloudmodelingdrawiosvr/d/6f0a56eb7a4a425ba5c34d3d807e45f4)

1、用户界面提问触发copilot,通过意图识别识别告警诊断意图；提取用户问题的参数{alarm_id,alarm_name,ne_name}
2、根据提取的alarm_id或alarm_name参数，使用RAG检索知识库。若检索结果有rerank得分大于0的结果，返回第一个案例检索结果；若没有,则返回空结果，recipe下一步会走knowledge_search
3、取告警id（alarm_id）与网元名称（ne_name）去dvanalysisext服务查询当前环境的告警信息。如果当前有该网元的告警id的告警，则返回告警信息；若没有，则返回空结果，recipe下一步会走knowledge_search
4、取第2步的案例知识与第3步的告警信息中的告警定位信息，拼接prompt去调用大模型总结信息，并返回前端。


### 告警分析Prompt
```
你是一个故障诊断专家，给出[智能事件]数据，请根据[事件案例]，结合[告警定位信息]分析出故障现象、故障影响、故障原因、故障排查建议，其中故障排查建议需要参考[事件案例]中的处理步骤和脚本，
结合输出具体的脚本样例。最终的输出格式以MarkDown的格式返回；

[事件案例]：
${chunking_knowledge}

[告警定位信息]：
${alarmInfo[0].moi}

```

### 告警分析Prompt案例
```
你是一个故障诊断专家，给出[智能事件]数据，请根据[事件案例]，结合[告警定位信息]分析出故障现象、故障影响、故障原因、故障排查建议，其中故障排查建议需要参考[事件案例]中的处理步骤和脚本，
结合输出具体的脚本样例。最终的输出格式以MarkDown的格式返回；

[事件案例]：
# ALM-60210216 systemd定时器执行失败\n## 告警解释\nservice定时任务执行失败，操作系统会将日志写入/var/log/messages日志中，uniagent检测到这个日志会上报systemd定时器执行失败告警。\n1. 执行命令systemctl list\\-timers \\| grep timer，获取到定时器对应的执行单元链表。\n定时器monitor\\.service和monitor\\_DFAgent\\.service不在监控范围。\n2. 使用grep命令，收集/var/log/messages文件中一分钟以内的日志，检查有无执行失败的信息。如果有，说明执行失败。由于操作系统类型以及版本的不同，/var/log/messages日志记录中的时间格式和报错信息存在差异，所以针对一些不同的操作系统和版本需要执行不同的检查逻辑。采集周期为1分钟。\n* EulerOS/麒麟操作系统，执行如下命令查询关键字 “systemd”, “Failed with result”, “xxx\\.service”。\n**grep \\-E \"\\$\\(LANG=C date \\-d '1 minute ago' \\+'%b %e %H:%M'\\)\" /var/log/messages \\| grep \\-v grep \\| grep systemd \\| grep 'Failed with result' \\| grep \\-w \"xxx\\.service\"**\n* SuSE12\\.2、SuSE12\\.5，执行如下命令查询关键字 “systemd”, “Failed with result”, “xxx\\.service”。\n**grep \\-E \"\\$\\(date \\-d '1 minute ago' \\+'%Y\\-%m\\-%dT%H:%M'\\)\" /var/log/messages \\| grep \\-v grep \\| grep systemd \\| grep 'Failed with result' \\| grep \\-w \"xxx\\.service\"**\n3. 当执行失败达到阈值之后（默认为1次，可以通过阈值下发模板进行修改），上报告警。\n代理容器化场景不支持该告警。

[告警定位信息]：
${alarmInfo[0].moi}
主机名称=*************, 主机IP=*************, 服务名=logrotate.service。
```

### 告警分析Prompt分析效果
1、通过网元告警id检索：
![img.png](images/1740123678_img.png)

2、通过网元告警名称检索：
![img.png](images/174012375982_img.png)


#### -  **Copilot**

查询当前告警信息接口

1、查询告警信息接口：

POST /dvaiagentservice/v1/alarmknowledgesearch/alarm:
  post:
    summary: 'alarm_knowledge_search'
    x-en-summary: 'alarm_knowledge_search'
    description: '告警知识检索'
    x-en-description: 'alarm_knowledge_search'
    tags:
      - AlarmKnowledgeSearchService  # 用于代码生成，声明方法所在类名称
    operationId: alarmKnowledgeSearch  # 用于代码生成，声明方法名称
    x-info:
      apiGroup:
        - dv_boot_recipe
      domain: DV
      scene: DV
      agentName: "DV"
      ownerSystemType: DV
      ownerService: DVAIAgentService
      nameForModelCN: alarmKnowledgeSearch
      nameForModelEN: alarmKnowledgeSearch
      descForModelCN: 'DV 智能运维告警知识检索'
      descForModelEN: 'DV intelligent O&M 告警知识检索'
      serviceName: DVAIAgentService
      accessType: INNER_IR
    consumes:
        - application/json
    parameters:
      - name: queryParam
        description: 'queryParam'
        in: body
        required: true
        schema:
          $ref: '#/definitions/AlarmQueryParam'
    responses:
      200:
        description: '返回状态码200表示服务健康，其他返回码表示服务异常'
        schema: # 声明返回值Schema
          $ref: '#/definitions/AlarmKnowledgeResult'

2、查询告警定位信息接口
POST /dvanalysisengineextservice/v1/alarms/queryAlarmInfoById:
  post:
    summary: 'get_alarm_list_by_ne_alarm'
    x-en-summary: 'get_alarm_list_by_ne_alarm'
    description: '查询告警定位信息'
    x-en-description: 'query alarm moi'
    tags:
      - DVAnalysisEngineExtServiceAlarms  # 用于代码生成，声明方法所在类名称
    operationId: queryAlarmInfoById  # 用于代码生成，声明方法名称
    x-info:
      apiGroup:
        - dv_boot_recipe
      domain: DV
      scene: DV
      agentName: "DV"
      ownerSystemType: DV
      ownerService: DVAnalysisEngineExtServiceAlarms
      nameForModelCN: queryAlarmInfoById
      nameForModelEN: queryAlarmInfoById
      descForModelCN: 'DV 查询告警定位信息参数'
      descForModelEN: 'DV intelligent O&M 查询告警定位信息参数'
      serviceName: DVAnalysisEngineExtService
      accessType: INNER_IR
    consumes:
      - application/json
    parameters:
      - name: QueryAlarmInfoByIdParam
      description: 查询告警信息参数
      in: body
      required: true
    schema:
      $ref: '#/definitions/QueryAlarmInfoByIdParam'
    responses:
      200:
      description: '返回状态码200表示服务健康，其他返回码表示服务异常'
      schema: # 声明返回值Schema
      $ref: '#/definitions/ResponseResult'


#### - 数据库表结构修改

无

#### -  代码实现

*<span style="color:#0066cc;">贴merge提交链接</span>*

---

---

### 【二次开发文档】

### 【约束说明】

*<span style="color:#0066cc;">约束描述
</span>*

### 【优化点说明】

*<span style="color:#0066cc;">后续补充优化功能点说明</span>*

---

#### 【对外接口（其他模块）】

*<span style="color:#0066cc;">如果涉及到对外接口的变化，新增、修改、删除等，给出接口或变更描述。并刷新接口基线清单和接口变更清单。
</span>*

#### 【外部依赖（其他模块）】

*<span style="color:#0066cc;">如果涉及对外依赖需求，给出依赖方提供的接口、方案或设计文档等。
</span>*

#### 【系统外部接口(和其他产品）】

*<span style="color:#0066cc;">如果因为系统设计涉及到外部接口的变更，需要在这里说明变更项，以及变更原因。
具体的变更直接刷新相应的外部接口文档。
</span>*

#### 【升级、割接、容灾场景要求】

- **升级**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **割接**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **容灾**  *<span style="color:#0066cc;">没有请填不涉及</span>*

#### 【兼容性要求】

*<span style="color:#0066cc;">识别是否存在影响兼容性情况，包括且不限于：API、接口、lib/so 名字，发布包名字，参数结构等。
根据需求分析阶段输出的《版本兼容性分析和说明》，针对每个不兼容点进行方案设计，确保做到后向兼容。
</span>*

#### 【可靠性要求】

*<span style="color:#0066cc;">识别是否存在可靠性方面的要求或者约束，若有要提炼出来。
</span>*

#### 【可维护性要求】

*<span style="color:#0066cc;">识别是否满足可维护性的要求或者约束，若有要提炼出来；
</span>*

#### 【安全性要求】

*<span style="color:#0066cc;">识别安全场景，及安全威胁分析等，安全checklist 自检结果，添加安全checklist归档路径。
</span>*
1、需求安全设计分析（请同步更新到云龙桌面对应的SR）
描述下需求的功能可能和安全相关与否，并标注如下各检查项的结果
1）是否涉及新增界面配置，界面变更： 防止XSS注入、防止CSRF攻击、前后台参数校验 --分析结果
2）是否涉及新增接口，接口变更：接口基线、流控、鉴权、服务间访问控制、XSS（参数校验以及输出编码）
3）是否涉及xml文件解析 ：防止XXE注入（外部实体解析、反序列化漏洞）
4）是否涉及文件上传下载 ：参数校验、路径格式校验、签名、验签、zip炸弹
5）是否涉及命令执行：防止命令注入
6）是否涉及对外通信：通道加密、通信协议是否安全、是否进行身份认证（双向认证）、认证凭据是否安全、加密算法是否安全
7）是否涉及后台脚本执行：不涉及
8）是否涉及操作本地磁盘：文件清理（即时清理、残留文件清理机制）
9）连接测试、认证类：结果只返回成功或失败，不填写具体失败原因，避免内网探测
10）是否存在SQL语句执行场景：sql注入
11）是否存在新增文件：文件权限基线、文件权限例外场景
12）是否存在外部URL跳转场景：防URL重定向
13）是否存在新增证书：存量预置证书基线

2、需求转测前，需完成checklist自检，并归档，归档目录联系PL

#### 【开源引入】

*<span style="color:#0066cc;">引入开源、三方件、外购件等，需要说明原因、评审结论、参与评审人员。</span>*