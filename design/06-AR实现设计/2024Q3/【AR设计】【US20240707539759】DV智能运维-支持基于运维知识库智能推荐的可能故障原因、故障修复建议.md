## 【Story信息】

- Story ID：SR20240707539441
- Story 标题：DV智能运维-支持基于运维知识库智能推荐的可能故障原因、故障修复建议——算法

## 【需求背景】

24年智能运维能力演进，需要优化当前的故障定位能力，给出系统确定的定位根因，同时维护cbs领域知识图谱的能力，完成故障恢复手段的推荐。

## 【需求说明】

1. 新增智能推荐算法包，基于运维知识库，在异常关联分析的结果上进行通用处理建议推荐和相似案例推荐；
2. 通用处理建议推荐：基于故障信息中出现的告警ID和错误码，匹配实体知识库中的告警知识（当前错误码知识没有存在实体知识库中），并返回对应的告警知识和错误码信息；
3. 相似案例推荐：检索案例知识向量库，基于故障信息召回相似案例并进行综合排序，基于排序得分返回Top 3相似案例的故障原因和故障修复建议。

## 【预期结果】

1. 后台传入关联分析结果，将关联分析路径中的节点信息进行筛选和组装，整合成故障信息；
2. 基于故障信息，匹配并返回告警和错误码信息（可为空）；
3. 基于故障信息和案例知识向量库，检索相似案例并进行综合排序，返回Top 3相似案例（可为空）；
4. 任务完成后，对存在通用处理建议推荐或相似案例推荐信息的关联分析路径，在指定节点上添加智能推荐节点。如果通用推荐为空，且相似案例为空，则不做智能推荐。

---

## 【方案】

1. 基于关联分析路径上的节点里的最大关联得分，筛选关联分析路径和节点（大节点>=30分，小节点>=70分）。将筛选后的路径的最后一个节点作为新叶子节点，对包含相同叶子节点的路径进行合并。针对叶子节点最大得分进行降序排序，筛选出得分Top 3的叶子节点的所有路径（并列情况下相同得分的都返回。当叶子节点个数超过3个，则将并列得分的节点选中后，不继续选中下一个得分的叶子节点）。
2. 基于筛选后的路径，组装故障信息Query。
   故障信息模板：**{网元A} 发生 {指标B} 异常 / {网元A}发生告警 {告警C ID}{告警C名称}**
   Query示例：

```charginggw发生平均实时响应时长异常
convergedcharging发生服务平均处理时间异常
cbpmdbcloud发生平均实时响应时长异常
cbpmdbcloud发生告警412030167 过期数据清理任务未正常结束
```

3. 通用处理建议推荐：提取故障信息最后一个节点的小节点所包含的Top 1告警ID和Top 1错误码ID（只处理遇到的第一个告警ID和错误码ID），如果小节点没有相关信息则去大节点寻找。在实体知识库中匹配，返回对应的告警知识（当前知识库没有错误码知识，针对错误码只返回指定的泛化信息）。如果告警ID和错误码都有，则二者的信息都展示。
4. 多路检索召回进行相似案例推荐
   
   1. 向量召回：调用知识库后台的向量库检索IR接口，使用故障信息Query检索案例知识向量库，得到Top K（K = 5）的相似案例知识和余弦向量相似度得分`VectorScore`（范围[0, 1]）；
   2. 根因节点相似度计算：调用知识库后台的向量相似度计算IR接口，对故障信息中的根因节点信息和上一步向量库召回的案例的Key，进行余弦向量相似度计算，得到根因节点向量相似度得分`LeafVectorScore`（范围[0, 1]）；
   3. 实体召回：调用知识库后台的实体抽取IR接口，对故障信息中的实体知识（指标、告警、网元）进行抽取，计算Jaccard系数，共同实体数量在案例知识库中的案例的Key的实体数量的占比，得到共同实体占比得分`EntityScore`（范围[0, 1]）;
   4. 基于多路召回的结果进行综合排序，计算出每个相似案例的综合排序得分`RerankScore`（范围[0, 1]），返回Top 3综合排序得分的故障案例知识。
      
      RerankScore = 0.6 * VectorScore + 0.2 * EntityScore + 0.2 * LeafVectorScore（权重需要根据准确率进行调优）
5. 如果通用推荐和案例推荐结果都为空，则不做智能推荐。否则，向后台返回智能推荐节点所挂靠的关联分析节点ID、通用处理建议推荐、相似故障案例推荐及综合排序得分。

![image](./image/77c964ac-cb5f-4f93-ab80-beae82b42a8f.png)


### - 入参

关联分析路径由JSON格式保存在文件中。
每条路径是一个由JSON对象组成的JSON数组。

#### JSON对象结构

关键字段

- `intelligentRecommendationNodeId`：String，关联分析节点ID
- `intelligentRecommendationNodeType`：String，节点类型，主指标或指标或告警
- `intelligentRecommendationNodeInfo`：JSON数组，节点中的小节点信息
- `measKey`：String，指标标识
- `measDesc`：String，指标描述
- `moType`：String，指标所在网元类型
- `extendParams`：String，指标扩展字段
- `alarmId`：String，告警ID
- `alarmName`：String，告警名称
- `productName`：String，告警所在网元类型
- `correlationScore`：Float，指标或告警关联得分

### - 输出

- 类型：CSV格式文件
  
  | 列名称| 数据类型 | 描述 |
| --- | --- | --- | 
| intelligentRecommendationNodeId| String | 智能推荐节点ID
| intelligentRecommendationNodeName | String | 智能推荐节点名称
| correlationNodeId| String | 挂靠关联分析节点ID
| generalRecommendation | String | 通用建议推荐
| caseRecommendation| String | JSON字符串案例知识
  
  
- `caseRecommendation`字段
  JSON数组，里面对象包含案例ID、名称、可能原因、修复措施、智能推荐得分。

```json
[
  {
    "caseId":"...",         
    "caseName":"...",
    "caseCause":"...",
    "caseRepair":"...",
    "caseScore":90.0
  }, ...
]
```

#### -  代码逻辑

*<span style="color:#0066cc;">用简短的文字描述各模块分配需求的变化， 尽量使用结构化的语言。</span>*

#### -  代码实现

*<span style="color:#0066cc;">贴merge提交链接</span>*

#### -  测试用例

[测试设计（新） (huawei.com)](https://integrate-cida.szv.dragon.tools.huawei.com/deskui/project/projectDetail/4746ffbca9c0420ab5e40a7961e05503/30a1f8e581e348ed9e6f93077a3dd7a7/FULL_ASSETS?path=/66815c8d8b15e6118e8b45d5/66815c8fe4b09bb7b935ed0c/66815c8fe4b09bb7b935ed0e/66815c8fe4b09bb7b935ed45/66815c8fe4b09bb7b935ed83/66815c8fe4b09bb7b935ef02/66b0881ae4b02554c4a4d7e9)

---

### 【二次开发文档】

### 【约束说明】

当前只支持中文。

### 【优化点说明】

*<span style="color:#0066cc;">后续补充优化功能点说明</span>*

---

#### 【对外接口（其他模块）】

*<span style="color:#0066cc;">如果涉及到对外接口的变化，新增、修改、删除等，给出接口或变更描述。并刷新接口基线清单和接口变更清单。
</span>*

#### 【外部依赖（其他模块）】

*<span style="color:#0066cc;">如果涉及对外依赖需求，给出依赖方提供的接口、方案或设计文档等。
</span>*

#### 【系统外部接口(和其他产品）】

*<span style="color:#0066cc;">如果因为系统设计涉及到外部接口的变更，需要在这里说明变更项，以及变更原因。
具体的变更直接刷新相应的外部接口文档。
</span>*

#### 【升级、割接、容灾场景要求】

- **升级**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **割接**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **容灾**  *<span style="color:#0066cc;">没有请填不涉及</span>*

#### 【兼容性要求】

*<span style="color:#0066cc;">识别是否存在影响兼容性情况，包括且不限于：API、接口、lib/so 名字，发布包名字，参数结构等。
根据需求分析阶段输出的《版本兼容性分析和说明》，针对每个不兼容点进行方案设计，确保做到后向兼容。
</span>*

#### 【可靠性要求】

*<span style="color:#0066cc;">识别是否存在可靠性方面的要求或者约束，若有要提炼出来。
</span>*

#### 【可维护性要求】

*<span style="color:#0066cc;">识别是否满足可维护性的要求或者约束，若有要提炼出来；
</span>*

#### 【安全性要求】

*<span style="color:#0066cc;">识别安全场景，及安全威胁分析等，安全checklist 自检结果，添加安全checklist归档路径。
</span>*
1、需求安全设计分析（请同步更新到云龙桌面对应的SR）
描述下需求的功能可能和安全相关与否，并标注如下各检查项的结果
1）是否涉及新增界面配置，界面变更： 防止XSS注入、防止CSRF攻击、前后台参数校验 --分析结果
2）是否涉及新增接口，接口变更：接口基线、流控、鉴权、服务间访问控制、XSS（参数校验以及输出编码）
3）是否涉及xml文件解析 ：防止XXE注入（外部实体解析、反序列化漏洞）
4）是否涉及文件上传下载 ：参数校验、路径格式校验、签名、验签、zip炸弹
5）是否涉及命令执行：防止命令注入
6）是否涉及对外通信：通道加密、通信协议是否安全、是否进行身份认证（双向认证）、认证凭据是否安全、加密算法是否安全
7）是否涉及后台脚本执行：sudo提权
8）是否涉及操作本地磁盘：文件清理（即时清理、残留文件清理机制）
9）连接测试、认证类：结果只返回成功或失败，不填写具体失败原因，避免内网探测
10）是否存在SQL语句执行场景：sql注入
11）是否存在新增文件：文件权限基线、文件权限例外场景
12）是否存在外部URL跳转场景：防URL重定向
13）是否存在新增证书：存量预置证书基线

2、需求转测前，需完成checklist自检，并归档，归档目录联系PL

#### 【开源引入】

*<span style="color:#0066cc;">引入开源、三方件、外购件等，需要说明原因、评审结论、参与评审人员。</span>*


