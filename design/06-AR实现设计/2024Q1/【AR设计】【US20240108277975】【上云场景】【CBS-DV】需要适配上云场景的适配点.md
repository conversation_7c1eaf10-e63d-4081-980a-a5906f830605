## 【 story信息】

*<span style="color:#0066cc;">列出本章节涉及的SR列表：SR业务编号、SR标题。</span>*

- Story ID：US20240108277975
- Story 标题：【上云场景】【CBS->DV】需要适配上云场景的适配点

## 【需求背景】

CBS在推动作战平台支持云上进行预安装，在这个过程中，识别到了华为云、AWS只支持单网络平面，需要各业务组件及平台适配网络配置改造及验证

## 【需求说明】

1. 这个预安装上云需求是CBS提给软件作战平台的，因为可能需要DV产品的预安装二次开发包进行适配，分配该需求到DV。
2. 目前与软件作战平台此需求负责开发 刘步高 30026375 对齐开发方案，DV这边负责在 软件作战平台联调验证需求时支撑问题， 并针对联调问题进行适配开发。

## 【预期结果】

*<span style="color:#0066cc;">需求预期实现结果，及最终实现效果图、配置使用方法等说明。
</span>*

---

## 【方案】

*<span style="color:#0066cc;">描述方案内容。
</span>*
工程全景图：https://onebox.huawei.com/v/********************************

#### - 场景一

- Story 设计思路
- Story 业务交互流程

**1. 仅涉及虚机容器化场景: 容器化合设集群，容器化一体机**

**2. 需求场景图**
![image](./img/29275245-cb72-4590-87d0-ebb3f8f753ef.png)

#### -  代码逻辑设计（存量原始逻辑+你增量描述和上下文描述+你代码逻辑）

*<span style="color:#0066cc;">用简短的文字描述各模块分配需求的变化， 尽量使用结构化的语言。</span>*

#### -  代码实现设计（planuml或cloudmodeling画图+描述）

*<span style="color:#0066cc;">贴merge提交链接</span>*

#### -  代码测试设计（CIDA 脑图+描述）

*<span style="color:#0066cc;">贴merge提交链接</span>*

https://integrate-cida.szv.dragon.tools.huawei.com/deskui/project/projectDetail/c7e9bbed38d24acc8a7bb70f33e457cf/993b16fcae4e4a038426a9e688493546/FULL_ASSETS?path=/6593c8107d23a103fd6407cf/659407cf7d23a103fd642478/6594082f5258032dd4af5759/6594082f5258032dd4af577b/6594082f5258032dd4af5797/6594082f5258032dd4af5804/65a4e6c5216a430897a28b67

![image](./img/e83f6344-64fc-47ba-ad4b-125824ca9a14.png)

---

### 【二次开发文档】

### 【约束说明】

*<span style="color:#0066cc;">约束描述
</span>*

### 【优化点说明】

*<span style="color:#0066cc;">后续补充优化功能点说明</span>*

---

#### 【对外接口（其他模块）】

*<span style="color:#0066cc;">如果涉及到对外接口的变化，新增、修改、删除等，给出接口或变更描述。并刷新接口基线清单和接口变更清单。
**不涉及**
</span>*

#### 【外部依赖（其他模块）】

*<span style="color:#0066cc;">如果涉及对外依赖需求，给出依赖方提供的接口、方案或设计文档等。
**不涉及**
</span>*

#### 【系统外部接口(和其他产品）】

*<span style="color:#0066cc;">如果因为系统设计涉及到外部接口的变更，需要在这里说明变更项，以及变更原因。
具体的变更直接刷新相应的外部接口文档。
**不涉及**
</span>*

#### 【升级、割接、容灾场景要求】

- **升级**  *<span style="color:#0066cc;">没有请填不涉及</span>*
  **不涉及**
- **割接**  *<span style="color:#0066cc;">没有请填不涉及</span>*
  **不涉及**
- **容灾**  *<span style="color:#0066cc;">没有请填不涉及</span>*
  **不涉及**

#### 【兼容性要求】

*<span style="color:#0066cc;">识别是否存在影响兼容性情况，包括且不限于：API、接口、lib/so 名字，发布包名字，参数结构等。
根据需求分析阶段输出的《版本兼容性分析和说明》，针对每个不兼容点进行方案设计，确保做到后向兼容。
</span>*
**不涉及**

#### 【可靠性要求】

*<span style="color:#0066cc;">识别是否存在可靠性方面的要求或者约束，若有要提炼出来。
</span>*
**不涉及**

#### 【可维护性要求】

*<span style="color:#0066cc;">识别是否满足可维护性的要求或者约束，若有要提炼出来；
</span>*
**不涉及**

#### 【安全性要求】

*<span style="color:#0066cc;">识别安全场景，及安全威胁分析等，安全checklist 自检结果，添加安全checklist归档路径。
</span>*
**不涉及**

#### 【开源引入】

*<span style="color:#0066cc;">引入开源、三方件、外购件等，需要说明原因、评审结论、参与评审人员。</span>*
**不涉及**


