## 【Story信息】

*<span style="color:#0066cc;">列出本章节涉及的SR列表：SR业务编号、SR标题。</span>*

- Story ID：US20240108274361 US20240108274434
- Story 标题：[DV][智能运维]单指标异常检测支持概念漂移检测并自适应新特征

## 【需求背景】

在容灾切换的场景下，目前指标异常检测训练出的模型对切换后的数据检测为异常并上报告警，需要人为手动停止任务再重新启动。该需求下的指标异常检测需要主动识别出容灾切换场景，并及时更新模型，减少误报。

## 【需求说明】

1、在配置概念漂移检测任务，并选择概念漂移后不触发训练，发生概念漂移后推理成功，无阈值线产生；
2、在配置概念漂移检测任务，并选择概念漂移后触发训练，概念漂移结束后后一段时间后自动重新训练模型，并进行推理；
3、发生概念漂移后，异常分析详情会产生概念漂移记录，记录概念漂移开始时间、结束时间、重训练状态和标记状态；
4、标记概念漂移为正确或未标记，认为该段数据发生概念漂移，概念漂移结束后一段时间后自动重新训练模型；
5、标记概念漂移为错误时，认为该段数据未发生概念漂移，使用之前的模型继续推理

## 【预期结果】

*<span style="color:#0066cc;">需求预期实现结果，及最终实现效果图、配置使用方法等说明。
</span>*
1、配置概念漂移任务
参数：
是否进行概念漂移任务
概念漂移结束后触发训练时间：概念漂移结束一段时间后进行训练
概念漂移后是否触发训练
![](http://resource.idp.huawei.com/idpresource/nasshare/editor/image/205002478460/1_zh-cn_image_0000001770684754.png)
2、展示概念漂移检测区间以及概念漂移详情，包括开始时间、结束时间、重训练状态和标记状态

![](http://resource.idp.huawei.com/idpresource/nasshare/editor/image/205002478460/1_zh-cn_image_0000001817324753.png)

---

## 【方案】

*<span style="color:#0066cc;">描述方案内容。
</span>*

#### - 单指标异常检测支持概念漂移并自适应特征——后台

单指标异常检测后台执行概念漂移过程
![](./image/ad91a752-6c88-4ee7-99db-f6733cec66fd.png)

#### - 单指标异常检测支持概念漂移并自适应特征——算法

- 算法执行概念漂移过程
  ![](./image/cf45701c-f88b-4964-85a2-e95b802eae3f.png)

#### - 数据库表新增和变更

TBL_AIOPS_INDICATOR_THRESHOLD_{taskId}表新增字段

| 字段名| 类型| 说明|
| --- | --- | --- |
| CONCEPT_DRIFT_SCORE  | FLOAT  | 概念漂移得分（0-1之间） |
| CONCEPT_DRIFT_THRESHOLD  | FLOAT  | 概念漂移阈值（0-1之间） |

新增TBL_AIOPS_INDICATOR_CONCEPT_DRIFT_RECORD表

| 字段名| 类型| 说明|
| --- | --- | --- |
| ID | VARCHAR|  |
|TASK_ID | INTEGER | 任务id
|INDICATOR_ID | VARCHAR | 指标名
START_TIME | BIGINT | 概念漂移开始时间
END_TIME | BIGINT | 概念漂移结束时间
CORRECT | NUMERIC | 标注结果
RETAIN | NUMERIC | 重训练状态

#### - 接口API设计

查询概念漂移列表接口

```json
/dvanalysisengineservice/v1/conceptdrift/conceptdriftlist
post：
    parameters： {
        "taskId": 1,
        "indicatorId": "11I2K_OSUsedCPURate7.220.10.4",
        "paging": {
            "pageSize": 10,         
            "pageNumber": 1,     
        } 
    }
    responses：{
        resultCode: 0,
        resultMessage: "",
        data: {
            rows: [{
                "id": 1,
                "taskId": 1,
                "indicatorId": "",
                "startTime": 1698589980000,
                "endTime": 1698590040000,
                "correct": 1,
                "retrain": 1
                },...
            ],
        total: 10
        }
```

标记概念漂移结果

```json
/dvanalysisenginewebsite/v1/conceptdrift/markconceptdrift
post：
    parameters： {
        "id": 1,
        "correct": 1   //1：标记正确，2：标记错误
    }
responses：{
    "resultCode": 0,
    "resultMessage": null,
    "data": null
}{
    "resultCode": -1,
    "resultMessage": "操作失败",
    "data": null
}
```

查询指标异常阈值线

```json
/dvanalysisenginewebsite/synanalysisresult/v1/performancedata
post：{
    paramters： {
        id: "OS=1I2K_OSUsedCPURate7.220.10.4", 
        taskId: 6, 
        timeRangeRadioId: "0", 
        customTimes: [] 
    }
    responses：{
        "resultCode": 0,
        "resultMessage": "200",
        "data": {
            "totalCount": 720,
            "indexName": "CPU占用率(%)",
            "indexUnit": "%",
            "startTime": "1705526196108",
            "endTime": "1705569396108",
            "performanceIndexValueList": [],
            "upperThresholdList": [ ],
            "lowerThresholdList": [],
            "conceptDriftList": [1705532160000, ...]
}

```

查询概念漂移分数

```json
/dvanalysisenginewebsite/v1/conceptdrift/queryconceptdriftscore
post：{
    paramters： {
        id: "OS=1I2K_OSUsedCPURate7.220.10.4", 
        taskId: 6, 
        "recent": 12,
        "unit": "HOUR",
        "aggregateType": 2
    }
    responses：{
        "resultCode": 0,
        "resultMessage": "200",
        "data": {
            "totalCount": 720,
            "indexName": "CPU占用率(%)",
            "indexUnit": "%",
            "startTime": "1705526196108",
            "endTime": "1705569396108",
            "performanceIndexValueList": [],
            "conceptDriftScoreList": [
                {
                    "timestampStr": 1705532160000,
                    "conceptDriftScore": "0.3"
                },...
            ],
            "conceptDriftThresholdList": [],
            "conceptDriftList": [1705532160000, ...]
}

```

#### -  代码逻辑

*<span style="color:#0066cc;">用简短的文字描述各模块分配需求的变化， 尽量使用结构化的语言。</span>*

#### -  代码实现

*<span style="color:#0066cc;">贴merge提交链接</span>*

---

### 【二次开发文档】

### 【约束说明】

*<span style="color:#0066cc;">约束描述
</span>*
1、不支持在快速推理中进行概念漂移检测，但在概念漂移任务中支持快速推理
2、只支持最近一条的概念漂移记录标注
3、检测到概念漂移且在未满足触发训练时间的情况下，未标记或标记成功，页面点击训练或者定时任务触发训练成功，但推理无阈值线产生；

### 【优化点说明】

*<span style="color:#0066cc;">后续补充优化功能点说明</span>*

---

#### 【对外接口（其他模块）】

*<span style="color:#0066cc;">如果涉及到对外接口的变化，新增、修改、删除等，给出接口或变更描述。并刷新接口基线清单和接口变更清单。
</span>*

#### 【外部依赖（其他模块）】

*<span style="color:#0066cc;">如果涉及对外依赖需求，给出依赖方提供的接口、方案或设计文档等。
</span>*

#### 【系统外部接口(和其他产品）】

*<span style="color:#0066cc;">如果因为系统设计涉及到外部接口的变更，需要在这里说明变更项，以及变更原因。
具体的变更直接刷新相应的外部接口文档。
</span>*

#### 【升级、割接、容灾场景要求】

- **升级**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **割接**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **容灾**  *<span style="color:#0066cc;">没有请填不涉及</span>*

#### 【兼容性要求】

*<span style="color:#0066cc;">识别是否存在影响兼容性情况，包括且不限于：API、接口、lib/so 名字，发布包名字，参数结构等。
根据需求分析阶段输出的《版本兼容性分析和说明》，针对每个不兼容点进行方案设计，确保做到后向兼容。
</span>*

#### 【可靠性要求】

*<span style="color:#0066cc;">识别是否存在可靠性方面的要求或者约束，若有要提炼出来。
</span>*

#### 【可维护性要求】

*<span style="color:#0066cc;">识别是否满足可维护性的要求或者约束，若有要提炼出来；
</span>*

#### 【安全性要求】

*<span style="color:#0066cc;">识别安全场景，及安全威胁分析等，安全checklist 自检结果，添加安全checklist归档路径。
</span>*
1、需求安全设计分析（请同步更新到云龙桌面对应的SR）
描述下需求的功能可能和安全相关与否，并标注如下各检查项的结果
1）是否涉及新增界面配置，界面变更： 防止XSS注入、防止CSRF攻击、前后台参数校验 --分析结果
2）是否涉及新增接口，接口变更：接口基线、流控、鉴权、服务间访问控制、XSS（参数校验以及输出编码）
3）是否涉及xml文件解析 ：防止XXE注入（外部实体解析、反序列化漏洞）
4）是否涉及文件上传下载 ：参数校验、路径格式校验、签名、验签、zip炸弹
5）是否涉及命令执行：防止命令注入
6）是否涉及对外通信：通道加密、通信协议是否安全、是否进行身份认证（双向认证）、认证凭据是否安全、加密算法是否安全
7）是否涉及后台脚本执行：sudo提权
8）是否涉及操作本地磁盘：文件清理（即时清理、残留文件清理机制）
9）连接测试、认证类：结果只返回成功或失败，不填写具体失败原因，避免内网探测
10）是否存在SQL语句执行场景：sql注入
11）是否存在新增文件：文件权限基线、文件权限例外场景
12）是否存在外部URL跳转场景：防URL重定向
13）是否存在新增证书：存量预置证书基线

2、需求转测前，需完成checklist自检，并归档，归档目录联系PL

#### 【开源引入】

*<span style="color:#0066cc;">引入开源、三方件、外购件等，需要说明原因、评审结论、参与评审人员。</span>*


