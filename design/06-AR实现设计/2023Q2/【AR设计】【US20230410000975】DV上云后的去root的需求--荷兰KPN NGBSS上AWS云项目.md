## 【 story信息】
*<span style="color:#0066cc;">列出本章节涉及的SR列表：SR业务编号、SR标题。</span>*
-  Story ID：US20230410000975  
-  Story 标题：【分解给基础设施组】【分解给工程】DV上云后的去root的需求--荷兰KPN NGBSS上AWS云项目
US:
https://clouddragon.huawei.com/cloudreq-obp/project/g05ad450200b84082be8e8659b687c9a7/productSpace/reqList/2039299690/addreq/2040114586?fullScreen=false

## 【需求背景】
运营商服务与软件/DigitalView-SW/Docs/V800/2023年度OBP项目/04.V800R023C10/02.C10/01.TR1&TR2&TR3/01.系统/03.设计说明书/DigitalView V800R023C10_功能设计说明书_工程.docx

DV上云部署，客户对网络安全和用户权限的管理要求很严格，针对Linux的root和数据库的sys用户的管理特别严格，当前已经说服客户接受初次安装给root用户和sys用户，但是当环境部署完成后客户会收回root用户和sys用户（修改root和sys密码），不再提供root和sys用户。
去root要求：
1. 安装可以用root用户
2. 升级需要去root
3. 运行、运维（待定，单独审视）、巡检（CBS内部巡检项）都要去root
4. 可以用sudo执行root权限，但是不能用su到root用户
5. 定时任务从root用户搬到普通用户，可以用sudo执行脚本或者命令（不需要提供具体脚本的使用原因）
去sys要求：
1. 首次安装可以用sys用户
2. 升级和运行态不能使用sys用户
对齐后诉求:
![image.png](/vision-file-storage/api/file/download/upload-v2/codehub/yellow/uploads/100001452/202304/1681732287137.png 'image.png')


## 【需求说明】
Q2实现一部分能力：

工程将近期版本需要强制执行的root，利用平台的能力，sudo执行，先做到至少近期版本必选root执行去除（有能力建议做的更多，最终目标是任意路径均不需要root执行）



## 【预期结果】
<span style="color:#0066cc;">
1. 满足荷兰KPN局点非root诉求
2. 在荷兰KPN需求诉求上补充增加非root能力，目标长期做到不用root即可升级
  
![image.png](/vision-file-storage/api/file/download/upload-v2/codehub/yellow/uploads/100001452/202304/1681732337973.png 'image.png')
  
</span>

-------

## 【方案】

二、工程部分实现：
1. DVCommonConfigService工程中增加DVCommonConfigService\deployment\src\main\release\etc\manifest\osconfig_manifest.yaml，osconfig_manifest.yaml内容为
 
2. CI打包的时候，需要对DVCommonConfigService\deployment\src\main\release\etc\manifest\osconfig_manifest.yaml进行签名，生成对应的osconfig_manifest.yaml.cms和osconfig_manifest.yaml.crl
3. DVCommonConfigService工程中增加DVCommonConfigService\deployment\src\main\release\os_app\dv_root_upgrade_preset.sh、DVCommonConfigService\deployment\src\main\release\os_app\os_app.yaml，os_app.yaml样例如下：
 
dv_root_upgrade_preset.sh实现root部分操作，Q2优先完成针对相邻版本必须依赖root的配置（sudoers增加+dbuser增加sshonly组），其余看情况部署，同时root执行完成后，清理对应脚本和sudoers配置

dbuser的需要再预置前做。---否则影响数据库应用程序升级。


4. post_install.sh参考加如下一段代码：
/usr/local/osconfig/os/bin/ostoolcli -module legacy -action exec -type script -contents  '{"appName": "DVCommonConfigService","scriptName": " dv_root_upgrade_preset.sh ","params": [""]}'
if [ $? -ne 0 ];then
    echo "failed to execute dv_root_upgrade_preset.sh by root" >> /opt/oss/log/SOP/DVCommonConfigService/xxx.log
    exit 1
fi
5.针对回滚场景需要覆盖验证

6. 资料相关手工执行部分指导删除。


#### - 场景一
补充测试用例--
升级
一体机
合设集群
标准集群
大容量
多节点单机


#### - 场景二
回退
一体机
合设集群
标准集群
大容量
多节点单机


#### -  代码逻辑
1. DVCommonConfigService工程中增加DVCommonConfigService\deployment\src\main\release\etc\manifest\osconfig_manifest.yaml，
osconfig_manifest.yaml内容为
runUser: ossuser
specs:
  legacy:
    spec:
      - action: exec
        type: script
        contents:
          - dv_root_upgrade_preset.sh
 
2. CI打包的时候，需要对DVCommonConfigService\deployment\src\main\release\etc\manifest\osconfig_manifest.yaml进行签名，生成对应的osconfig_manifest.yaml.cms和osconfig_manifest.yaml.crl
3. DVCommonConfigService工程中增加DVCommonConfigService\deployment\src\main\release\os_app\dv_root_upgrade_preset.sh、DVCommonConfigService\deployment\src\main\release\os_app\os_app.yaml，os_app.yaml样例如下：
name: DVCommonConfigService
version: 1.0.0

 
dv_root_upgrade_preset.sh实现root部分操作，Q2优先完成针对相邻版本必须依赖root的配置（sudoers增加+dbuser增加sshonly组），其余看情况部署，同时root执行完成后，清理对应脚本和sudoers配置

4. post_install.sh参考加如下一段代码：
/usr/local/osconfig/os/bin/ostoolcli -module legacy -action exec -type script -contents  '{"appName": "DVCommonConfigService","scriptName": " dv_root_upgrade_preset.sh ","params": [""]}'
if [ $? -ne 0 ];then
    echo "failed to execute dv_root_upgrade_preset.sh by root" >> /opt/oss/log/SOP/DVCommonConfigService/xxx.log
    exit 1
fi

#### -  代码实现

代码提交记录：
https://codehub-y.huawei.com/U2020-S/DVCommonConfigService/merge_requests/107?sourceProjectId=100659308


----

### 【二次开发文档】
![image.png](/vision-file-storage/api/file/download/upload-v2/codehub/yellow/uploads/100001452/202304/1681816084551.png 'image.png')

### 【约束说明】
升级后，dv_root_upgrade_preset.sh 会删掉。删sudo，再删自己脚本。

运行态添加的sudo，回退由于已经没有权限了，也没有 dv_root_upgrade_preset.sh了，无法回退到升级前的样子。

不支持重入，因为升级后，dv_root_upgrade_preset.sh会删除。

升级后，脚本已经被删除，所以调用不到了，sudo相关的无法回退，调用的是上一个版本的

安全影响已经在安全TMG评审通过，属于工程态影响，可控

### 【优化点说明】
dbuser的不去掉。
资料这部分修改为脚本自动化，减少手工操作。预埋升级需要root权限的处理。
![image.png](/vision-file-storage/api/file/download/upload-v2/codehub/yellow/uploads/100001452/202304/1681799505145.png 'image.png')

root部分是否可以去掉？

验证记录：
/usr/local/uniepsudobin/DVCommonConfigService

![image.png](/vision-file-storage/api/file/download/upload-v2/codehub/yellow/uploads/100001452/202305/1684826347354.png 'image.png')


----
#### 【对外接口（其他模块）】
*<span style="color:#0066cc;">如果涉及到对外接口的变化，新增、修改、删除等，给出接口或变更描述。并刷新接口基线清单和接口变更清单。
</span>*

#### 【外部依赖（其他模块）】
*<span style="color:#0066cc;">如果涉及对外依赖需求，给出依赖方提供的接口、方案或设计文档等。
</span>*

#### 【系统外部接口(和其他产品）】
*<span style="color:#0066cc;">如果因为系统设计涉及到外部接口的变更，需要在这里说明变更项，以及变更原因。
具体的变更直接刷新相应的外部接口文档。
</span>*

#### 【升级、割接、容灾场景要求】
- **升级**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **割接**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **容灾**  *<span style="color:#0066cc;">没有请填不涉及</span>*

#### 【兼容性要求】
*<span style="color:#0066cc;">识别是否存在影响兼容性情况，包括且不限于：API、接口、lib/so 名字，发布包名字，参数结构等。
根据需求分析阶段输出的《版本兼容性分析和说明》，针对每个不兼容点进行方案设计，确保做到后向兼容。
</span>*

#### 【可靠性要求】
*<span style="color:#0066cc;">识别是否存在可靠性方面的要求或者约束，若有要提炼出来。
</span>*

#### 【可维护性要求】
*<span style="color:#0066cc;">识别是否满足可维护性的要求或者约束，若有要提炼出来；
</span>*

#### 【安全性要求】
*<span style="color:#0066cc;">识别安全场景，及安全威胁分析等，安全checklist 自检结果，添加安全checklist归档路径。
</span>*

#### 【开源引入】
*<span style="color:#0066cc;">引入开源、三方件、外购件等，需要说明原因、评审结论、参与评审人员。</span>*



