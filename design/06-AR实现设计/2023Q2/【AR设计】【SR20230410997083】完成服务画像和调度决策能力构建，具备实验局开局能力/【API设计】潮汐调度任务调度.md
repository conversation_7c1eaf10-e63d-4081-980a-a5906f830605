#  潮汐调度任务调度API设计说明书

## 特性简介

<hr>

根据集群的某项指标趋势判断集群应该扩容还是缩容，达成集群资源紧缺时扩容支撑业务，资源过剩时缩容节省能源的目的

## 特性分析

### 特性接口
<hr>

复用原来的训练和推理接口

- `Api Version:` v1
- `URI:` api
- `Method:` POST
- `Api Designer URL:`/dvanalysisengineservice/v1/taskschedule/trainmodel 
``
- `Api Version:` v1
- `URI:` api
- `Method:` POST
- `Api Designer URL:`/dvanalysisengineservice/v1/taskschedule/handletask

## 软件模型
<hr>

**类图**

```plantuml
@startuml

class TaskManager {
  void startTaskByIdImpl()
}

class ScheduleCenter {
   TaskScheduleExecutor taskScheduleExecutor
   void handleTask()
   void pullJobToCronService()
}

class CronServiceUtil {
   void startCronTask()
}

class TaskScheduleExecutor {
   ThreadPoolExecutor trainingExecutor
   ThreadPoolExecutor predictExecutor
   void executeTraining()
   void executePredicing()
}

class TaskHandleThread {
   AbstractTaskHandler taskHandler
   void run()
}

Abstract class AbstractTaskHandler {
   PyExecutor pyExecutor
   TaskScheduleExecutor taskScheduleExecutor
   TaskExecutionResultManager taskExecutionResultManager
   void handle()
   abstract void insertResult()
   abstract void checkAndUpdateModel()
   abstract void updateResult()
   abstract void handleResult()
}

class TidalRegulationTask {
   void insertResult()
   void checkAndUpdateModel()
   void updateResult()
   void handleResult()
}

Abstract class AbstractHandleDto {
}

class ResourceScheduleHandleDto {
}

class DataCenter {
   AbstractDataSourceHandler handler
   DataSourceInfo dispatch()
}

Abstract class AbstractDataSourceHandler {
    abstract DataSourceInfo handle()
}

class TidalRegulationDataSourceHandler {
   DataSourceInfo handle()
}

class PerformanceClient {
   List<HistoryQueryData> getHistoryDataFormPerformance()
}

class PyExecutor {
   String execute()
}

class ResultHandleExecutor {
    ThreadPoolExecutor indicatorExecutor
    ThreadPoolExecutor alarmExecutor
    ThreadPoolExecutor ...
    ThreadPoolExecutor tidalExecutor
    void executeIndicator()
    void executeAlarm()
    void...()
    void executeTidal()
}

class TaskExecutionResultManager {
    ResultHandleExecutor resultHandleExecutor
    void excuteResult()
}

class ResultHandleThread {
    TaskPeriodInfo taskPeriodInfo
    void run()
}

interface AnalysisResultHandler {
    void synAnalysisResult()
}

class TidalRegulationResultHandler {
    void synAnalysisResult()
}

class TaskPeriodInfo {
    int taskId
    long startTime
    String status
    String taskPeriodType
    int taskType
    String destFilePath
    int indicatorPredictScenario
}

TaskManager ..> ScheduleCenter
ScheduleCenter ..> CronServiceUtil
ScheduleCenter -- TaskScheduleExecutor
TaskScheduleExecutor *-- TaskHandleThread
TaskHandleThread -- AbstractTaskHandler
AbstractTaskHandler <|-- TidalRegulationTask
AbstractTaskHandler ..> DataCenter
AbstractTaskHandler ..> HandleDtoFactory
HandleDtoFactory ..> AbstractHandleDto
AbstractHandleDto <|-- ResourceScheduleHandleDto 
DataCenter -- AbstractDataSourceHandler
AbstractDataSourceHandler <|-- TidalRegulationDataSourceHandler
AbstractDataSourceHandler ..> DataSourceInfo
TidalRegulationDataSourceHandler ..> PerformanceClient
AbstractTaskHandler -- PyExecutor
PyExecutor ..> DataSourceInfo
AbstractTaskHandler -- TaskExecutionResultManager
TaskExecutionResultManager -- ResultHandleExecutor
ResultHandleExecutor *-- ResultHandleThread
ResultHandleThread ..> AnalysisResultHandler
AnalysisResultHandler <|-- TidalRegulationResultHandler
ResultHandleThread -- TaskPeriodInfo
DataCenter ..> AbstractHandleDto

@enduml
```

**时序图**

```plantuml
@startuml

ScheduleCenter ->> TaskHandler: 任务信息
activate TaskHandler
TaskHandler -> DataSourceHandler: 数据源信息
activate DataSourceHandler
DataSourceHandler --> TaskHandler: 算法所需数据
deactivate DataSourceHandler
TaskHandler -> PyExecutor: 导入数据给算法
activate PyExecutor
PyExecutor --> TaskHandler: 算法导出计算结果
deactivate PyExecutor
TaskHandler ->> ResultHandler: 处理计算结果
activate ResultHandler
ResultHandler -->> TaskHandler: 处理完毕
deactivate ResultHandler
TaskHandler -->> ScheduleCenter: 调度完毕
deactivate TaskHandler

@enduml
```

## 测试设计

### 1) 测试用例

可以训练、推理成功

### 2) UT测试设计

针对几个核心类进行UT的编写

### 3) API测试设计

无

## 需求编号列表（SR/US）
SR20230410997083 / US20230413116067

