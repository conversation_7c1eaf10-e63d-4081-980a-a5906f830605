## 【Story信息】

- Story ID：SR20230410995154
- Story 标题：【分解给Xops技术组】【Mobile FinTech】智能运维路标需求（同类比较）

## 【需求背景】

多指标异常检测场景下，算法可以配置分组和分组层级，但前台未提供同一分组的指标在同一张图上展示的功能

## 【需求说明】

使用多指标异常检测算法并配置了分组的指标（分组查询开关为yes且分组参考层级不为空），在这里增加一个按钮，用以查看该指标同一分组下的所有指标曲线
![image.png](/vision-file-storage/api/file/download/upload-v2/codehub/yellow/uploads/736165/202304/1682390401319.png 'image.png')

## 【预期结果】

在一张图上展示同一分组下所有指标的曲线（最多同时展示10条）
![image.png](/vision-file-storage/api/file/download/upload-v2/codehub/yellow/uploads/736165/202304/1682390508434.png 'image.png')

---

## 【方案】

![image.png](/vision-file-storage/api/file/download/upload-v2/codehub/yellow/uploads/736165/202304/1682391467769.png 'image.png')

后台储存指标的groupId，并添加groupId和taskId作为指标列表的的过滤条件：

- 指标信息传入算法之前计算出所属分组（原有功能），将分组信息入库（本story实现）
- 后台拓展indicatorinfolist接口入参，允许通过groupId和taskId过滤出同一分组下的指标列表

前台拿到同一分组下的指标列表，允许用户进行复选，把当前选中的指标曲线展示在同一张图上

#### -  代码逻辑

数据库TBL_AIOPS_TASK_INDICATOR表增加字段GROUP_ID VARCHAR(256)，表示该指标所属的分组，无分组时该字段为null

每次计算出分组之后，将groupId信息入库

拓展接口功能：/dvanalysisengineservice/synanalysisresult/v1/indicatorinfolist
增加groupId和taskId入参

修改两个dao和对应的sql，增加taskId和groupId作为入参，可以为null，不为null时表示要通过这两个条件过滤
获取指标列表getIndicatorList
获取指标列表总数getIndicatorListTotal

#### -  代码实现

https://codehub-y.huawei.com/U2020-S/DVAnalysisEngineWebsite/merge_requests/1406

https://codehub-y.huawei.com/U2020-S/DVAnalysisEngineService/merge_requests/1381

---
### 【测试用例】

https://integrate-cida.szv.dragon.tools.huawei.com/deskui/project/projectDetail/a1ae89bb91374cf4b9b46173650cb5b2/b6572fa1d2264acbb0e02c34ba0e7b51/FULL_ASSETS?path=/64365d0834265b058d136c65/64365d0934265b058d136c66/64365d0934265b058d136c68/64365d0a34265b058d136c88/64365d0a34265b058d136cdd/64365d0f34265b058d136d96/6438b7f834265b058d13d39f

---
### 【二次开发文档】

不涉及

### 【约束说明】

不涉及

### 【优化点说明】

不涉及

---

#### 【对外接口（其他模块）】

不涉及

#### 【外部依赖（其他模块）】

不涉及

#### 【系统外部接口(和其他产品）】

不涉及

#### 【升级、割接、容灾场景要求】

- **升级**  数据库字段要支持升级
- **割接**  不涉及
- **容灾**  不涉及

#### 【兼容性要求】

接口需要兼容老代码：/dvanalysisengineservice/synanalysisresult/v1/indicatorinfolist

#### 【可靠性要求】

不涉及

#### 【可维护性要求】

不涉及

#### 【安全性要求】

接口新增了两个参数需要校验，并设置最大值

#### 【开源引入】

不涉及

