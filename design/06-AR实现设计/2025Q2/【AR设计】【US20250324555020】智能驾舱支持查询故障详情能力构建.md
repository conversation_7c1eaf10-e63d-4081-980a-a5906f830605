## 【Story信息】

- Story ID：US20250324555020
- Story 标题：智能驾舱支持查询故障详情能力构建

## 【需求背景】

智能驾舱需求开发\
目的在于能够展示驾舱首页以及详情页面\
包含驾舱首页、业务拓扑、关联分析、故障详情列表等页面\

## 【需求说明】

AiAgentService服务需要为Incident以及Event建模进行补充\
同时提供智能驾舱首页/详情等后端接口;\
1、对Incident与Event模型重新建模，以支持故障Agent\
2、支持对建模后的Incident提供详情接口\
3、支持查询业务拓扑信息能力\
4、支持查询关联分析信息能力\
5、支持提供诊断报告能力\
6、支持提供故障的根因能力\

## 【预期结果】

1、对Incident与Event模型重新建模\
2、提供查询Incident详情接口\
3、提供查询业务拓扑信息能力\
4、提供查询关联分析信息能力\
5、提供提供诊断报告能力\
6、提供提供故障的根因能力\

## 【方案】


### 一、Incident与Event建模：

##### Incident类建模

##### 属性

| 属性名称       | 类型                     | 描述                                                                 |
|----------------|--------------------------|----------------------------------------------------------------------|
| **csn**        | `Long`                   | 故障流水号，通过 `CsnGenerator.get()` 生成。                          |
| **tenantId**   | `String`                 | 租户ID。                                                            |
| **name**       | `String`                 | 故障名称。                                                          |
| **category**   | `String`                 | 故障分类。                                                          |
| **subCategory**| `String`                 | 故障子类。                                                          |
| **incidentType**| `int`                    | 故障类型。                                                          |
| **status**     | `IncidentStatus`         | 故障状态，默认为 `IncidentStatus.OPENED`。                            |
| **ackStatus**  | `Integer`                | 确认状态，默认为 `0`。                                              |
| **clearStatus**| `Integer`                | 清除状态，默认为 `0`。                                              |
| **id**         | `Long`                   | 故障ID。                                                            |
| **diagnosticStatus**| `DiagnosticStatusEnum`| 诊断状态，默认占位符。                                              |
| **diagnosticType**| `DiagnosticTypeEnum`   | 诊断类型，默认占位符。                                              |
| **rootCauses** | `String`                 | 根因列表。                                                          |
| **reasonIds**  | `String`                 | 根因列表ID列表。                                                    |
| **rootEvents** | `List<Event>`            | 根因事件列表，默认为空列表。                                        |
| **extendData** | `String`                 | 扩展数据。                                                          |
| **meType**     | `String`                 | 网元类型。                                                          |
| **createTime** | `long`                   | 创建时间，表示第一条告警达到网管聚合模块，开始聚合的系统时间。      |
| **occurTime**  | `long`                   | 发生时间，表示当前 `Incident` 中 `Event` 的最早发生时间。            |
| **clearTime**  | `long`                   | 清除时间。                                                          |
| **startTime**  | `long`                   | 开始时间。                                                          |
| **endTime**    | `long`                   | 结束时间。                                                          |
| **priority**   | `IncidentPriority`       | 故障优先级，默认为 `IncidentPriority.LOW`。                          |
| **description**| `String`                 | 描述。                                                              |
| **sourceObjType**| `String`                | 源对象类型。                                                        |
| **sourceObjId**| `String`                 | 源对象ID。                                                          |
| **sourceObjName**| `String`               | 源对象名称。                                                        |
| **analysisPaths**| `String`               | 分析路径。                                                          |
| **sourceObjects**| `List<Object>`         | 源对象列表，默认为空列表。                                          |
| **repairAdvice**| `String`                | 修复建议。                                                          |
| **events**     | `List<Event>`            | 事件列表，默认为空列表。                                            |
| **size**       | `int`                    | 大小，默认为 `1`。                                                  |
| **hashKey**    | `String`                 | 哈希键。                                                            |
| **propagationGraph**| `Graph`             | 传播图。                                                            |
| **alarms**     | `List<AlarmRecord>`      | 告警列表，默认为空列表。                                            |

##### 方法

- **Incident()**: 无参构造函数。
- **Incident(Event event)**: 带有`Event`参数的构造函数，用于初始化`Incident`对象。
- **getIncidentPriorityByEvent(EventSeverity eventSeverity)**: 根据事件严重性返回故障优先级。

##### Event类建模

##### 属性

| 属性名称               | 类型                     | 描述                                                                 |
|------------------------|--------------------------|----------------------------------------------------------------------|
| **eventId**            | `String`                 | 事件ID。                                                            |
| **csn**                | `Long`                   | 事件流水号，通过 `CsnGenerator.get()` 生成。                        |
| **tenantId**           | `String`                 | 租户ID。                                                            |
| **incidentCsn**        | `Long`                   | 故障流水号。                                                        |
| **domain**             | `String`                 | 产品领域。                                                          |
| **productName**        | `String`                 | 产品名称。                                                          |
| **type**               | `EventType`              | 事件类型。                                                          |
| **status**             | `EventStatus`            | 事件状态，默认为 `EventStatus.OPENED`。                              |
| **arriveTime**         | `Long`                   | 事件到达时间。                                                      |
| **occurTime**          | `Long`                   | 事件发生时间。                                                      |
| **clearTime**          | `Long`                   | 清除时间，默认为 `0L`。                                            |
| **ackStatus**          | `int`                    | 确认状态，默认为 `0`。                                              |
| **clearStatus**        | `int`                    | 清除状态，默认为 `0`。                                              |
| **severity**           | `EventSeverity`          | 事件优先级。                                                        |
| **name**               | `String`                 | 事件名称。                                                          |
| **sourceObjType**      | `String`                 | 源对象类型。                                                        |
| **sourceObjId**        | `String`                 | 源对象ID。                                                          |
| **sourceObjName**      | `String`                 | 源对象名称。                                                        |
| **description**        | `String`                 | 描述。                                                              |
| **sourceSystem**       | `String`                 | 源对象来源系统。                                                    |
| **sourceCsn**          | `Long`                   | 源流水号。                                                          |
| **extendData**         | `ExtendData`             | 预留扩展字段。                                                      |
| **rootEventProbability**| `Double`                 | 根因事件可能性。                                                   |
| **alarmCsn**           | `Long`                   | 告警流水号。                                                        |
| **meType**             | `String`                 | 类型。                                                              |
| **moDn**               | `String`                 | 唯一标志符。                                                        |
| **nativeMoDn**         | `String`                 | 源系统标志符。                                                      |
| **moName**             | `String`                 | 管理对象名称。                                                      |
| **moType**             | `String`                 | 管理对象类型。                                                      |
| **rawData**            | `String`                 | 原始数据。                                                          |
| **eventNotifyTime**    | `Long`                   | 事件上报时间。                                                      |
| **clearNotifyTime**    | `Long`                   | 事件清除时间。                                                      |

##### 方法

- **Event()**: 无参构造函数。
- **Event(AlarmInfo alarmInfo)**: 带有`AlarmInfo`参数的构造函数，用于初始化`Event`对象。

### 二、智能驾舱支持查询业务拓扑信息方案设计

#### 接口设计：

### 请求方法

POST

### 请求 URL
`/rest/dvaiagentservice/v1/cockpit/getTopoNeTypeByDn`

### 请求参数对象如下

| 参数名称 | 类型 | 是否必选 | 描述 |
|---------|------|----------|------|
| dn | String | 是 | 唯一标识 |
| timeStamp | long | 否 | 时间戳 |

数组最大限制50
### 返回结果

返回结果为一个 `UpperLayerInfo` 对象，包含以下字段：

#### 字段说明

| 字段名称            | 类型              | 描述     |
|-----------------|-----------------|--------|
| dn              | string          | dn     |
| timestamp       | long            | 时间戳    |
| solutionType    | integer (int32) | 解决方案类型 |
| applicationType | integer (int32) | 应用类型   |
| instanceId      | integer (int32) | 实例ID   |
| siteId          | integer (int32) | 站点ID   |
| solutionId      | integer (int32) | 解决方案ID |
| stripeUnit      | string          | 条带单元   |
| dnId            | integer (int32) | 节点名称ID |
| resultCode      | integer (int32) | 结果代码   |
| resultMessage   | string          | 结果消息   |


### 请求示例

```
POST /rest/dvaiagentservice/v1/cockpit/getTopoNeTypeByDn

{
  "paramList":[
	{
	  "dn":"a2cd5d2878a32dfa37185",
	  "timestamp":0
	},
	{
	  "dn":"a2cd5d28e4ffac3df615b",
	  "timestamp":0
	},
	{
	  "dn":"a2cd5d2b09dd6d26be164",
	  "timestamp":0
          }
  ]
}

```

### 响应示例

```json
{
  "resultCode": 0,
  "resultMessage": null,
  "data": [
    {
      "dn": "a2cd5d2878a32dfa37185",
      "timestamp": 0,
      "solutionType": 1,
      "applicationType": 2,
      "instanceId": 101,
      "siteId": 10,
      "solutionId": 1,
      "stripeUnit": null,
      "dnId": 191,
      "resultCode": 0,
      "resultMessage": null,
      "groupId": 22
    },
    {
      "dn": "a2cd5d28e4ffac3df615b",
      "timestamp": 0,
      "solutionType": 1,
      "applicationType": 0,
      "instanceId": 46,
      "siteId": 10,
      "solutionId": 1,
      "stripeUnit": null,
      "dnId": 192,
      "resultCode": 0,
      "resultMessage": null,
      "groupId": 24
    },
    {
      "dn": "a2cd5d2b09dd6d26be164",
      "timestamp": 0,
      "solutionType": 1,
      "applicationType": 1,
      "instanceId": 98,
      "siteId": 10,
      "solutionId": 1,
      "stripeUnit": null,
      "dnId": 193,
      "resultCode": 0,
      "resultMessage": null,
      "groupId": 22
    }
  ]
}
```

### 接口自验测试设计

#### 测试用例编号: TC-001
#### 用例名称: 正常查询业务拓扑信息
##### 测试目的
验证`/rest/dvaiagentservice/v1/cockpit/getTopoNeTypeByDn` POST接口在正常情况下查询到业务拓扑信息

##### 测试步骤
1、准备测试环境，确保测试账号具有调用该接口的权限\
2、准备测试数据，包含所有必填字段和可选字\
3、构造请求，通过PostMan工具发送GET请求\
4、预期结果：能够返回业务拓扑数据\

#### 测试用例编号: TC-002
#### 用例名称: 缺少必填字段测试
##### 测试目的
验证`/rest/dvaiagentservice/v1/cockpit/getTopoNeTypeByDn` POST验证接口在缺少dn必填字段时是否能否返回错误信息

##### 测试步骤
1、准备测试环境，确保测试账号具有调用该接口的权限\
2、准备测试数据，缺少dn字段\
3、构造请求，用瑞士军刀或类似工具发送POST请求\
4、预期结果：能够能够正确返回错误信息\

#### 测试用例编号: TC-003
#### 用例名称: 参数类型错误测试
##### 测试目的
验证`/rest/dvaiagentservice/v1/cockpit/getTopoNeTypeByDn` POST验证接口dn类型错误时是否能否返回错误信息

##### 测试步骤
1、准备测试环境，确保测试账号具有调用该接口的权限\
2、准备测试数据，dn类型参数错误\
3、构造请求，用瑞士军刀或类似工具发送POST请求\
4、预期结果：能够能够正确返回错误信息\

#### 测试用例编号: TC-004
#### 用例名称: 超出参数长度限制测试
##### 测试目的
验证`/rest/dvaiagentservice/v1/cockpit/getTopoNeTypeByDn` POST验证接口dn参数超出长度限制时能否返回错误信息

##### 测试步骤
1、准备测试环境，确保测试账号具有调用该接口的权限\
2、准备测试数据，dn参数超出接口限制\
3、构造请求，用瑞士军刀或类似工具发送POST请求\
4、预期结果：能够能够正确返回错误信息\

#### 测试用例编号: TC-005
#### 用例名称: 无效请求方法测试
##### 测试目的
验证`/rest/dvanalysisengineextservice/knwl/v1/knowledge-index` POST接口验证在使用无效请求方法时是否能够正确返回错误信息。

##### 测试步骤
1、准备测试环境，确保测试账号具有调用该接口的权限\
2、准备测试数据，包含所有必填字段和可选字段\
3、构造请求，用瑞士军刀或类似工具发送POST请求\
4、预期结果：能够能够正确返回错误信息\

### 三、智能驾舱支持查询故障详情方案设计

#### 接口设计

##### 请求方法
POST

##### 请求 URL
`/rest/dvaiagentservice/v1/incident/getincidentbycsn`

##### 请求参数
| 参数名称 | 类型 | 是否必选 | 描述 |
|---------|------|----------|------|
| incidentCsn | Long | 是 | 故障流水号 |

##### 返回结果
返回结果为一个包含以下字段的对象：

###### 字段说明

| 字段名称 | 类型 | 描述 |
|---------|------|------|
| csn | Long | 故障流水号，由 `CsnGenerator` 生成 |
| tenantId | String | 租户ID |
| name | String | 故障名称 |
| category | String | 故障分类 |
| subCategory | String | 故障子类 |
| incidentType | int | 故障类型 |
| status | IncidentStatus | 故障状态，默认为 `OPENED` |
| ackStatus | Integer | 确认状态，默认为 `0` |
| clearStatus | Integer | 清除状态，默认为 `0` |
| id | Long | 故障ID |
| diagnosticStatus | DiagnosticStatusEnum | 诊断状态，默认为 `DIAGNOSTIC_STATUS_PLACEHOLDER` |
| diagnosticType | DiagnosticTypeEnum | 诊断类型，默认为 `DIAGNOSTIC_TYPE_PLACEHOLDER` |
| rootCauses | String | 根因列表 |
| reasonIds | String | 根因列表ID列表 |
| rootEvents | List<Event> | 根因事件列表 |
| extendData | String | 扩展数据 |
| meType | String | 网元类型 |
| createTime | long | 创建时间，第一条告警达到网管聚合模块，开始聚合的系统时间，不为空 |
| occurTime | long | 发生时间，当前 `Incident` 中 `Event` 的最早发生时间，不为空 |
| clearTime | long | 清除时间 |
| startTime | long | 开始时间 |
| endTime | long | 结束时间 |
| priority | IncidentPriority | 故障优先级，默认为 `LOW` |
| description | String | 描述 |
| sourceObjType | String | 源对象类型 |
| sourceObjId | String | 源对象ID |
| sourceObjName | String | 源对象名称 |
| analysisPaths | String | 分析路径 |
| sourceObjects | List | 源对象列表 |
| repairAdvice | String | 修复建议 |
| events | List<Event> | 事件列表 |
| size | int | 大小，默认为 `1` |
| hashKey | String | 哈希键 |
| propagationGraph | Graph | 传播图 |
| alarms | List<AlarmRecord> | 告警列表 |

##### 返回结果示例

```json
{
  "csn": 1234567890,
  "tenantId": "tenant-123",
  "name": "Network Connection Issue",
  "category": "Network",
  "subCategory": "Connectivity",
  "incidentType": 1,
  "status": "OPENED",
  "ackStatus": 0,
  "clearStatus": 0,
  "id": 987654321,
  "diagnosticStatus": "DIAGNOSTIC_STATUS_PLACEHOLDER",
  "diagnosticType": "DIAGNOSTIC_TYPE_PLACEHOLDER",
  "rootCauses": "Network Interface Down",
  "reasonIds": "reason-123,reason-456",
  "rootEvents": [
    {
      "eventId": 1,
      "eventTime": 1625107200000
    }
  ],
  "extendData": "additional information",
  "meType": "Router",
  "createTime": 1625107200000,
  "occurTime": 1625107200000,
  "clearTime": 0,
  "startTime": 1625107200000,
  "endTime": 0,
  "priority": "LOW",
  "description": "Network connection issue detected",
  "sourceObjType": "Device",
  "sourceObjId": "device-123",
  "sourceObjName": "Main Router",
  "analysisPaths": "/path/to/analysis",
  "sourceObjects": [
    {
      "objectId": "device-123",
      "objectName": "Main Router"
    }
  ],
  "repairAdvice": "Check network interface",
  "events": [
    {
      "eventId": 1,
      "eventTime": 1625107200000
    }
  ],
  "size": 1,
  "hashKey": "hash-123",
  "propagationGraph": {
    "nodes": [],
    "edges": []
  },
  "alarms": [
    {
      "alarmId": 1,
      "alarmTime": 1625107200000
    }
  ]
}
```

### 接口自验测试设计

#### 测试用例编号: TC-006
#### 用例名称: 正常查询业务拓扑信息
##### 测试目的
验证`/rest/dvaiagentservice/v1/incident/getincidentbycsn` GET接口在正常情况下查询到Incident详情信息

##### 测试步骤
1、准备测试环境，确保测试账号具有调用该接口的权限\
2、准备测试数据，包含所有必填字段和可选字\
3、构造请求，通过PostMan工具发送GET请求\
4、预期结果：能够返回Incident详情信息\

#### 测试用例编号: TC-007
#### 用例名称: 缺少必填字段测试
##### 测试目的
验证`/rest/dvaiagentservice/v1/incident/getincidentbycsn` GET验证接口在缺少csn必填字段时是否能返回错误信息

##### 测试步骤
1、准备测试环境，确保测试账号具有调用该接口的权限\
2、准备测试数据，缺少csn字段\
3、构造请求，用瑞士军刀或类似工具发送POST请求\
4、预期结果：能够能够正确返回错误信息\

#### 测试用例编号: TC-008
#### 用例名称: 参数类型错误测试
##### 测试目的
验证`/rest/dvaiagentservice/v1/incident/getincidentbycsn` GET验证接口csn类型错误时能否返回错误信息

##### 测试步骤
1、准备测试环境，确保测试账号具有调用该接口的权限\
2、准备测试数据，csn类型参数错误\
3、构造请求，用瑞士军刀或类似工具发送POST请求\
4、预期结果：能够能够正确返回错误信息\

#### 测试用例编号: TC-009
#### 用例名称: 超出参数长度限制测试
##### 测试目的
验证`/rest/dvaiagentservice/v1/incident/getincidentbycsn` GET验证接口csn参数超出长度限制时能否返回错误信息

##### 测试步骤
1、准备测试环境，确保测试账号具有调用该接口的权限\
2、准备测试数据，csn参数超出接口限制\
3、构造请求，用瑞士军刀或类似工具发送POST请求\
4、预期结果：能够能够正确返回错误信息\

#### 测试用例编号: TC-010
#### 用例名称: 无效请求方法测试
##### 测试目的
验证`/rest/dvaiagentservice/v1/incident/getincidentbycsn` GET接口验证在使用无效请求方法时是否能够正确返回错误信息。

##### 测试步骤
1、准备测试环境，确保测试账号具有调用该接口的权限\
2、准备测试数据，包含所有必填字段和可选字段\
3、构造请求，用瑞士军刀或类似工具发送PUT请求\
4、预期结果：能够能够正确返回错误信息\

### 四、智能驾舱关联分析方案设计

1、/rest/dvaiagentservice/v1/incident/addincident接口参数增加extendData字段\

2、extendData存放如下字段：\

```json
{
  "extendData": {
    "indicatorId":"指标id", 
    "correlationResultId":"关联分析结果id",
    "indicatorTaskId": "指标所属任务id",
    "indicatorOutlierId":"指标异常id",
    "indicatorReportAlarmId":"指标异常上报告警id",
    "indicatorOutlierStartTime": "指标异常开始时间",
    "correlationExecutionTime": "关联分析执行时间"
  }
}
```
3、通过extendData字段去像智能运维服务查询对应的关联分析任务

#### 故障概览接口
##### 请求 URL
`/dvaiagentservice/v1/incident/overview`

##### 请求方法
`GET`

##### 请求参数
| 参数名称 | 类型 | 是否必选 | 描述 |
|---------|------|----------|------|
| displayTopo | boolean | 是 | 是否显示拓扑信息 |
| status | array of string | 是 | 状态列表 |
| num | integer | 是 | 数量 |
| unit | string | 是 | 单位 |

##### 返回结果
返回结果为一个包含以下字段的对象：

###### 字段说明

| 字段名称 | 类型 | 描述 |
|---------|------|------|
| resultCode | integer | 结果代码，0表示成功 |
| resultMessage | string | 结果消息，成功时为null |
| data | object | 数据对象，包含事件详情列表 |
| eventDetailDtoList | array of objects | 事件详情列表 |
| csn | long | 事件流水号 |
| diagnosticStatusEnum | string | 诊断状态枚举 |
| occurTime | long | 发生时间 |
| incidentName | string | 事件名称 |
| description | string | 事件描述 |
| eventDtos | array of objects | 事件详细信息列表 |
| startTime | long | 数据起始时间 |
| endTime | long | 数据结束时间 |

### 请求示例

```
GET /rest/dvaiagentservice/v1/incident/overview?displayTopo=true&num=100&unit=day&status=AUTO_COMPLETED&status=PENDING_AUTO
```

### 响应示例

```json
  {
  "eventDetailDtoList": [
    {
      "csn": 1424305728527873,
      "diagnosticStatusEnum": "AUTO_COMPLETED",
      "occurTime": 1747656165000,
      "incidentName": "磁盘占用率过高告警",
      "description": "产生门限=80, 清除门限=75, 容量=206298444kB, 占用率=83, IP地址=*************, 产品别名=DigitalView, 剩余空间产生门限=4000000000, 剩余空间清除门限=4000000000。请到全景监控页面查看磁盘占用率过高的节点。 接入IP=*************。",
      "eventDtos": [
        {
          "nativeMoDn": "OS=1",
          "occurTime": 1747656165000,
          "upperLayerInfo": {
            "solutionType": null,
            "applicationType": null,
            "instanceId": null,
            "siteId": null,
            "solutionId": null,
            "stripeUnit": null,
            "dnId": null,
            "resultCode": -1,
            "resultMessage": "Topology information is null."
          }
        }
      ]
    },
    {
      "csn": 1420820773608962,
      "diagnosticStatusEnum": "AUTO_COMPLETED",
      "occurTime": 1747121400000,
      "incidentName": "test",
      "description": null,
      "eventDtos": []
    },
    {
      "csn": 1422273009425921,
      "diagnosticStatusEnum": "AUTO_COMPLETED",
      "occurTime": 1747413857515,
      "incidentName": "test36",
      "description": "产生门限=80, 清除门限=75, 容量=51487460kB, 占用率=80, IP地址=************, 产品别名=DigitalView, 剩余空间产生门限=4000000000, 剩余空间清除门限=4000000000。请到全景监控页面查看磁盘占用率过高的节点。 接入IP=*************。",
      "eventDtos": [
        {
          "nativeMoDn": "OS=1",
          "occurTime": 1747413857515,
          "upperLayerInfo": {
            "solutionType": null,
            "applicationType": null,
            "instanceId": null,
            "siteId": null,
            "solutionId": null,
            "stripeUnit": null,
            "dnId": null,
            "resultCode": -1,
            "resultMessage": "Topology information is null."
          }
        }
      ]
    },
    {
      "csn": 1422280928271873,
      "diagnosticStatusEnum": "AUTO_COMPLETED",
      "occurTime": 1747414801041,
      "incidentName": "test361",
      "description": "产生门限=80, 清除门限=75, 容量=51487460kB, 占用率=80, IP地址=************, 产品别名=DigitalView, 剩余空间产生门限=4000000000, 剩余空间清除门限=4000000000。请到全景监控页面查看磁盘占用率过高的节点。 接入IP=*************。",
      "eventDtos": [
        {
          "nativeMoDn": "OS=1",
          "occurTime": 1747414801041,
          "upperLayerInfo": {
            "solutionType": null,
            "applicationType": null,
            "instanceId": null,
            "siteId": null,
            "solutionId": null,
            "stripeUnit": null,
            "dnId": null,
            "resultCode": -1,
            "resultMessage": "Topology information is null."
          }
        }
      ]
    },
    {
      "csn": 1422273982504449,
      "diagnosticStatusEnum": "AUTO_COMPLETED",
      "occurTime": 1747413974016,
      "incidentName": "test361",
      "description": "产生门限=80, 清除门限=75, 容量=51487460kB, 占用率=80, IP地址=************, 产品别名=DigitalView, 剩余空间产生门限=4000000000, 剩余空间清除门限=4000000000。请到全景监控页面查看磁盘占用率过高的节点。 接入IP=*************。",
      "eventDtos": [
        {
          "nativeMoDn": "OS=1",
          "occurTime": 1747413974016,
          "upperLayerInfo": {
            "solutionType": null,
            "applicationType": null,
            "instanceId": null,
            "siteId": null,
            "solutionId": null,
            "stripeUnit": null,
            "dnId": null,
            "resultCode": -1,
            "resultMessage": "Topology information is null."
          }
        }
      ]
    }
  ],
  "startTime": 1739175899344,
  "endTime": 1747815899344
}
```
#### 人工接管接口
```
/rest/dvaiagentservice/v1/incident/takeoverincident  POST
```

##### 请求参数
| 参数名称 | 类型 | 是否必选 | 描述 |
|---------|------|----------|------|
| incidentCsn | Long | 是 | 故障流水号 |

#### 请求参数示例：
```
{
    "incidentCsn":1420820723277314
}
```

##### 返回结果示例：
```
{
    "resultCode": -1,
    "resultMessage": "Check failed before take over the incident.",
    "data": null
}
```

### 五、智能驾舱根因故障与故障报告方案设计
#### 流程设计
![img.png](images/addIncident交互流程图.png)


#### - 数据库表结构修改
TBL_INCIDENT新增如下字段：\
ID：故障ID\
DIAGNOSTIC_STAUS：诊断状态\
DIAGNOSTIC_TYPE：诊断类型\
REASON_IDS：故障根因IDs\
ROOT_EVENTS：故障根因\
EXTEND_DATA：扩展字段\
ME_TYPE：网元类型\

TBL_EVENT新增如下字段：\
EXTEND_DATA：扩展字段\
EVENT_NOTIFY_TIME：上报时间\
CLEARED_NOTIFY_TIME：清除上报时间\

#### -  代码实现

https://codehub-y.huawei.com/U2020-S/DVAIAgentService/merge_requests/157
https://codehub-y.huawei.com/U2020-S/DVAIAgentService/merge_requests/174

---

---

### 【二次开发文档】

### 【约束说明】

*<span style="color:#0066cc;">约束描述
</span>*

### 【优化点说明】

*<span style="color:#0066cc;">
1、Incident提交故障诊断待优化为异步多线程\
</span>*

---

#### 【对外接口（其他模块）】

*<span style="color:#0066cc;">如果涉及到对外接口的变化，新增、修改、删除等，给出接口或变更描述。并刷新接口基线清单和接口变更清单。
1、对外新增/rest/dvaiagentservice/v1/incident/getincidentbycsn的ER接口：查询故障详情\
2、对外新增/rest/dvaiagentservice/v1/cockpit/getTopoNeTypeByDn的ER接口：查询业务拓扑信息\
</span>*

#### 【外部依赖（其他模块）】

*<span style="color:#0066cc;">如果涉及对外依赖需求，给出依赖方提供的接口、方案或设计文档等。
1、查询业务拓扑信息依赖拓扑服务\
2、查询关联分析任务信息依赖智能运维服务\
</span>*

#### 【系统外部接口(和其他产品）】

*<span style="color:#0066cc;">如果因为系统设计涉及到外部接口的变更，需要在这里说明变更项，以及变更原因。
具体的变更直接刷新相应的外部接口文档。
</span>*

#### 【升级、割接、容灾场景要求】

- **升级**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **割接**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **容灾**  *<span style="color:#0066cc;">没有请填不涉及</span>*

#### 【兼容性要求】

*<span style="color:#0066cc;">识别是否存在影响兼容性情况，包括且不限于：API、接口、lib/so 名字，发布包名字，参数结构等。
根据需求分析阶段输出的《版本兼容性分析和说明》，针对每个不兼容点进行方案设计，确保做到后向兼容。
</span>*

#### 【可靠性要求】

*<span style="color:#0066cc;">
识别是否存在可靠性方面的要求或者约束，若有要提炼出来。
</span>*

#### 【可维护性要求】

*<span style="color:#0066cc;">
识别是否满足可维护性的要求或者约束，若有要提炼出来；
</span>*

#### 【安全性要求】

*<span style="color:#0066cc;">识别安全场景，及安全威胁分析等，安全checklist 自检结果，添加安全checklist归档路径。
</span>*
1、需求安全设计分析（请同步更新到云龙桌面对应的SR）
描述下需求的功能可能和安全相关与否，并标注如下各检查项的结果
1）是否涉及新增界面配置，界面变更： 防止XSS注入、防止CSRF攻击、前后台参数校验 --所有参数均有校验
2）是否涉及新增接口，接口变更：接口基线、流控、鉴权、服务间访问控制、XSS（参数校验以及输出编码），有
3）是否涉及xml文件解析 ：防止XXE注入（外部实体解析、反序列化漏洞）
4）是否涉及文件上传下载 ：参数校验、路径格式校验、签名、验签、zip炸弹，不涉及
5）是否涉及命令执行：防止命令注入，不涉及
6）是否涉及对外通信：通道加密、通信协议是否安全、是否进行身份认证（双向认证）、认证凭据是否安全、加密算法是否安全，不涉及
7）是否涉及后台脚本执行：不涉及
8）是否涉及操作本地磁盘：文件清理（即时清理、残留文件清理机制）：不涉及
9）连接测试、认证类：结果只返回成功或失败，不填写具体失败原因，避免内网探测，不涉及
10）是否存在SQL语句执行场景：sql注入，不涉及
11）是否存在新增文件：文件权限基线、文件权限例外场景，不涉及
12）是否存在外部URL跳转场景：防URL重定向，不涉及
13）是否存在新增证书：存量预置证书基线，不涉及

2、需求转测前，需完成checklist自检，并归档，归档目录联系PL

#### 【开源引入】

*<span style="color:#0066cc;">引入开源、三方件、外购件等，需要说明原因、评审结论、参与评审人员。</span>*