## 【Story信息】

*<span style="color:#0066cc;">列出本章节涉及的SR列表：SR业务编号、SR标题。</span>*

- Story ID：IR20240407001084
- Story 标题：智能运维指标异常检测针对容器化pod动态变化场景的适配——后台

## 【需求背景】

彩铃咪咕局点有一些关于业务第三方指标，特别是对于容器化的指标，需要进行异常检测。然而，由于自动弹缩或重启等原因，指标经常发生变化，导致有效的指标数量减少。因此，他们希望能够实现指标的自动识别和变化指标的加入，以避免指标的膨胀。此外，他们也希望能够在容器化场景下，自动识别指标的变化，并加入这些变化，而不需要手动添加。

## 【需求说明】

针对指标异常检测和关联分析任务的指标自动更新方法，提供自动更新指标的配置

## 【预期结果】

配置了自动更新指标后可以自动更新所选网元类型的指标信息

## 【方案】

#### - 一 指标异常检测第三方数据源

1）指标异常检测选择Zenith、Oracle
a.创建任务时可以配置是否自动更新指标和指标废弃评估时长
创建任务增加字段 updateIndicatorAuto  indicatorDiscardTime

b.指标数据同步时，判断是否要自动更新，如果配置了自动更新指标，那么重新查询最新的指标，并将指标更新入库（指标表）
指标更新入库时，如果指标数量超过上限，则丢弃超过规格的指标，只保留最大规格数量指标
然后需要清除废弃指标，清除规则是按照数据表中的最新数据时间与当前时间间隔来判断，超过阈值（指标废弃评估时长）时，则表示该指标已废弃，将该指标从指标表中删除，并且将该指标对应的数据表中的数据删除

2）指标异常检测选择Prometheus时
a.创建任务时可以配置是否自动更新指标和指标废弃评估时长
创建任务增加字段 updateIndicatorAuto  indicatorDiscardTime

b.创建任务入库时需要新增一个查询指标入库的操作和高斯数据源一样

c.指标数据同步时，如果配置了自动更新，重新查询最新的指标，并将指标更新入库（指标表）
指标更新入库时，如果指标数量超过上限，则丢弃超过规格的指标，只保留最大规格数量指标
然后需要清除废弃指标，清除规则是按照数据表中的最新数据时间与当前时间间隔来判断，超过阈值（指标废弃评估时长）时，则表示该指标已废弃，将该指标从指标表中删除，并且将该指标对应的数据表中的数据删除

d.指标数据同步时，不再自动更新指标数据

#### - 二 指标异常检测性能数据源

1.前台创建

a.创建任务时可以配置是否自动更新网元增加字段 updateIndicatorAuto

b.后台增加定时任务，定时（5分钟）,配置了自动更新指标的任务自动更新网元指标和删除废弃指标

![image](./image/47607d19-b9d6-43d8-9c57-502f4edce90e.png)

2.导入
1）需要增加是否自动更新网元配置，配置项说明需要补充自动更新的影响：如果启用自动更新网元，则会自动选择所有该网元类型下面的网元实例的指标，同时会自动更新该网元类型下面的实例和指标数量，如果指标总数超过规格限制，则只保留最大规格数量指标

![image](./image/bbf9e4f6-5426-4b7e-a600-6ad82ad16f6f.png)

2）导入任务的指标变更
以前导入的任务在训练时会重新进行指标的实例化和过滤和匹配
现在相当于把这个逻辑从训练时挪到定时任务处理过程中 只要是导入任务就执行一下以前训练时执行的逻辑
如果导入自动更新配置的false则不更新 不配和配true都更新

#### - 三 故障关联分析指标节点选择性能数据源

1.前台创建

创建指标节点时可以配置是否自动更新网元
增加字段updateIndicatorAuto

执行分析任务时，如果配置了自动更新则按照网元类型查询最新网元实例的指标进行分析，如果指标数量超过规格限制，保留最大规格指标，如果指标归属网元实例不存在，则删除该网元对应的指标

2.导入
1）增加是否自动更新网元配置，配置项说明需要补充自动更新的影响：如果启用自动更新网元，则会自动选择所有该网元类型下面的网元实例的指标，同时会自动更新该网元类型下面的实例和指标数量，如果指标总数超过规格限制，则只保留最大规格数量指标

![image](./image/968eff05-2d4f-4925-a30e-1330c1020e03.png)

2）执行时指标变更
如果是导入任务
配置为false不更新
否则就和以前逻辑保持不变都更新网元
如果不是导入任务
配置了自动更新网元为是就更新
配置了自动更新网元为否就不更新

#### - 七 数据库表结构修改

任务管理表
TBL_AIOPS_ANALYSIS_TASK增加字段
UPDATE_INDICATOR_AUTO BOOLEAN 是否自动更新指标
INDICATOR_DISCARD_TIME INTGER 指标废弃评估时间

关联分析节点要增加更新网元配置字段

#### -  接口API设计

```

```

```

```

#### -  代码逻辑

#### -  代码实现

*<span style="color:#0066cc;">贴merge提交链接</span>*

---

### 【二次开发文档】

### 【约束说明】

### 【优化点说明】

*<span style="color:#0066cc;">后续补充优化功能点说明</span>*

---

#### 【对外接口（其他模块）】

*<span style="color:#0066cc;">如果涉及到对外接口的变化，新增、修改、删除等，给出接口或变更描述。并刷新接口基线清单和接口变更清单。
</span>*

#### 【外部依赖（其他模块）】

*<span style="color:#0066cc;">如果涉及对外依赖需求，给出依赖方提供的接口、方案或设计文档等。
</span>*

#### 【系统外部接口(和其他产品）】

*<span style="color:#0066cc;">如果因为系统设计涉及到外部接口的变更，需要在这里说明变更项，以及变更原因。
具体的变更直接刷新相应的外部接口文档。
</span>*

#### 【升级、割接、容灾场景要求】

- **升级**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **割接**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **容灾**  *<span style="color:#0066cc;">没有请填不涉及</span>*

#### 【兼容性要求】

*<span style="color:#0066cc;">
无
</span>*

#### 【可靠性要求】

*<span style="color:#0066cc;">识别是否存在可靠性方面的要求或者约束，若有要提炼出来。
</span>*

#### 【可维护性要求】

*<span style="color:#0066cc;">识别是否满足可维护性的要求或者约束，若有要提炼出来；
</span>*

#### 【安全性要求】

*<span style="color:#0066cc;">识别安全场景，及安全威胁分析等，安全checklist 自检结果，添加安全checklist归档路径。
</span>*
1、需求安全设计分析（请同步更新到云龙桌面对应的SR）
描述下需求的功能可能和安全相关与否，并标注如下各检查项的结果
1）是否涉及新增界面配置，界面变更： 防止XSS注入、防止CSRF攻击、前后台参数校验 --分析结果
2）是否涉及新增接口，接口变更：接口基线、流控、鉴权、服务间访问控制、XSS（参数校验以及输出编码）
3）是否涉及xml文件解析 ：防止XXE注入（外部实体解析、反序列化漏洞）
4）是否涉及文件上传下载 ：参数校验、路径格式校验、签名、验签、zip炸弹
5）是否涉及命令执行：防止命令注入
6）是否涉及对外通信：通道加密、通信协议是否安全、是否进行身份认证（双向认证）、认证凭据是否安全、加密算法是否安全
7）是否涉及后台脚本执行：sudo提权
8）是否涉及操作本地磁盘：文件清理（即时清理、残留文件清理机制）
9）连接测试、认证类：结果只返回成功或失败，不填写具体失败原因，避免内网探测
10）是否存在SQL语句执行场景：sql注入
11）是否存在新增文件：文件权限基线、文件权限例外场景
12）是否存在外部URL跳转场景：防URL重定向
13）是否存在新增证书：存量预置证书基线

2、需求转测前，需完成checklist自检，并归档，归档目录联系PL

#### 【开源引入】

*<span style="color:#0066cc;">引入开源、三方件、外购件等，需要说明原因、评审结论、参与评审人员。</span>*

#### 测试用例

https://wemind.huawei.com/Map/Index?id=398154


