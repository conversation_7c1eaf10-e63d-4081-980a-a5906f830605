# 【story设计模板】

## 【Story信息】

*<span style="color:#0066cc;">列出本章节涉及的SR列表：SR业务编号、SR标题。</span>*

- Story ID：
- Story 标题：

## 【需求背景】

## 【需求说明】

## 【预期结果】

*<span style="color:#0066cc;">需求预期实现结果，及最终实现效果图、配置使用方法等说明。
</span>*

---

## 【方案】

### 方案描述
*<span style="color:#0066cc;">描述方案的设计思想。</span>*

#### - 正常场景描述

*<span style="color:#0066cc;">描述正常业务场景。</span>*

#### - 异常场景描述

*<span style="color:#0066cc;">描述异常业务场景。</span>*

#### -  类图

*<span style="color:#0066cc;">用UML类图描述类结构设计。</span>*

#### -  时序图

*<span style="color:#0066cc;">用时序图描述业务流程中各个实体交互过程。</span>*

#### -  其它视图

*<span style="color:#0066cc;">可以使用其它UML图如：活动图，状态图等进行设计。</span>*

### 【测试设计】
*<span style="color:#0066cc;">使用CIDA进行测试用例设计（使用plantuml描述或插入用例截图）。</span>*

- 示例：
```plantuml
@startmindmap
* DV统计对象名含特殊字符的兼容行能力增强
** 空字串创测试
*** 将多索引测量对象的最后一个索引值置为空字符串
*** 数据正常入库与展示
** 单个或多个空格字符串测试
*** 将多索引测量对象的最后一个索引值置为空格字符串
*** 数据正常入库与展示
** 常规索引测试
*** 将多索引测量对象的所有索引值均为常见字符串
*** 数据正常入库与展示
** 二次开发文档
*** 打开二次开发文档
*** 存在空字符串，单个或多个空格，<,>，<=>限制说明
@endmindmap
```
---

### 【二次开发文档】

### 【约束说明】

*<span style="color:#0066cc;">约束描述
</span>*

### 【优化点说明】

*<span style="color:#0066cc;">后续补充优化功能点说明</span>*

---

#### 【对外接口（其他模块）】

*<span style="color:#0066cc;">如果涉及到对外接口的变化，新增、修改、删除等，给出接口或变更描述。并刷新接口基线清单和接口变更清单。
</span>*

#### 【外部依赖（其他模块）】

*<span style="color:#0066cc;">如果涉及对外依赖需求，给出依赖方提供的接口、方案或设计文档等。
</span>*

#### 【系统外部接口(和其他产品）】

*<span style="color:#0066cc;">如果因为系统设计涉及到外部接口的变更，需要在这里说明变更项，以及变更原因。
具体的变更直接刷新相应的外部接口文档。
</span>*

#### 【升级、割接、容灾场景要求】

- **升级**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **割接**  *<span style="color:#0066cc;">没有请填不涉及</span>*
- **容灾**  *<span style="color:#0066cc;">没有请填不涉及</span>*

#### 【兼容性要求】

*<span style="color:#0066cc;">识别是否存在影响兼容性情况，包括且不限于：API、接口、lib/so 名字，发布包名字，参数结构等。
根据需求分析阶段输出的《版本兼容性分析和说明》，针对每个不兼容点进行方案设计，确保做到后向兼容。
</span>*

#### 【可靠性要求】

*<span style="color:#0066cc;">识别是否存在可靠性方面的要求或者约束，若有要提炼出来。
</span>*

#### 【可维护性要求】

*<span style="color:#0066cc;">识别是否满足可维护性的要求或者约束，若有要提炼出来；
</span>*

#### 【安全性要求】

*<span style="color:#0066cc;">识别安全场景，及安全威胁分析等，安全checklist 自检结果，添加安全checklist归档路径。
</span>*

1、需求安全设计分析（请同步更新到云龙桌面对应的SR）  
描述下需求的功能可能和安全相关与否，并标注如下各检查项的结果  
1）是否涉及新增界面配置，界面变更： 防止XSS注入、防止CSRF攻击、前后台参数校验 --不涉及  
2）是否涉及新增接口，接口变更：接口基线、流控、鉴权、服务间访问控制、XSS（参数校验以及输出编码） --不涉及  
3）是否涉及xml文件解析 ：防止XXE注入（外部实体解析、反序列化漏洞） --不涉及
4）是否涉及文件上传下载 ：参数校验、路径格式校验、签名、验签、zip炸弹 --不涉及  
5）是否涉及命令执行：防止命令注入 --不涉及  
6）是否涉及对外通信：通道加密、通信协议是否安全、是否进行身份认证（双向认证）、认证凭据是否安全、加密算法是否安全 --不涉及  
7）是否涉及后台脚本执行：sudo提权 --不涉及  
8）是否涉及操作本地磁盘：文件清理（即时清理、残留文件清理机制） --不涉及  
9）连接测试、认证类：结果只返回成功或失败，不填写具体失败原因，避免内网探测 --不涉及  
10）是否存在SQL语句执行场景：sql注入 --预编译无安全问题  
11）是否存在新增文件：文件权限基线、文件权限例外场景 --不涉及  
12）是否存在外部URL跳转场景：防URL重定向 --  
13）是否存在新增证书：存量预置证书基线 --不涉及

2、需求转测前，需完成checklist自检，并归档，归档目录联系PL

#### 【开源引入】

*<span style="color:#0066cc;">引入开源、三方件、外购件等，需要说明原因、评审结论、参与评审人员。</span>*
