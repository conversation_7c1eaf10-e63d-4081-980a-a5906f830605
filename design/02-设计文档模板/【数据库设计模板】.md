# 数据库-软件实现设计说明书

## DataBase Schema
数据库架构变更详细描述

## DataBase Migration
自动应用数据库迁移方案详细描述

## UML
包含不限于实体关系图

## 数据图

```plantuml
@startjson
#highlight "lastName"
#highlight "address" / "city"
#highlight "phoneNumbers" / "0" / "number"
{
  "firstName": "<PERSON>",
  "lastName": "<PERSON>",
  "isAlive": true,
  "age": 28,
  "address": {
    "streetAddress": "21 2nd Street",
    "city": "New York",
    "state": "NY",
    "postalCode": "10021-3100"
  },
  "phoneNumbers": [
    {
      "type": "home",
      "number": "************"
    },
    {
      "type": "office",
      "number": "************"
    }
  ],
  "children": [],
  "spouse": null
}
@endjson
```
## 需求编号列表（SR/US）
通过系统设计分解的软件US/AR编码
