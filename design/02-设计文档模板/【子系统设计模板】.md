# 【子系统设计模板】

## 统一语言

> 统一语言（Ubiquitous Language）是提炼领域知识的产出物，获得统一语言就是需求分析的过程，也是团队中各个角色就系统目标、范围与具体功能达成一致的过程。

<hr>

| 中文                 | 英文                       | 解释                                                                                                  |
| ---------------------- |--------------------------| ------------------------------------------------------------------------------------------------------- |
| CloudSOP虚机         | SOP_VM                   |                  基于虚拟机部署场景                                                                                     |
| CloudSOP容器化       | SOP_CONTAINER            |               基于K8s的容器化场景                                                                                        |
| 环境唯一标识         | VIRTUALIZATION           | 1、SOP_VM：CLoudSOP虚机场景 2、SOP_CONTAINER：CloudSOP容器场景。 预留：X_CLOUD_CONTAINER：X云容器场景 |

## 编程框架

> 包含不限于系统/子系统内的编程语言、编程框架、脚手架、建模方案、公共组件等，要求符合公司/部门的整体策略及限制，体现部门软件设计的意图，并确保意图得到执行。

<hr>

### 编程语言选型

> 描述该子系统可选的编程语言及描述

| 分类                 | 可选语言                       |                       描述                                                                            |
| ---------------------- |--------------------------| ------------------------------------------------------------------------------------------------------- |
| 系统开发         | Rust、C++                   |                                                                                                       |
| Back-end应用开发       | Java、仓颉、Scala、NodeJS            |                                                                                                       |
| Front-end应用开发       | React  、Less          |                                                                                                       |
| 胶水/脚本开发        | TypeScript、Python           |  |

### 编程框架及脚手架

> 描述该系统下服务开发，前端开发的脚手架代码地址

<hr>

| 分类                                   |                       框架/脚手架                                                                           |
| ---------------------- | ------------------------------------------------------------------------------------------------------- |
| 系统开发                      |                                                                                                       |
| Back-end应用开发       |                    [SpringBoot](https://start.spring.io/).                                                                     |
| Front-end应用开发            |                                                                                                       |
| 胶水/脚本开发        |  |

> :提示: **可以提供脚手架下载链接或自动生成页面**: 参考Spring-boot
> ![image.png](https://wiki.huawei.com/vision-file-storage/api/file/download/upload-v2/codehub/yellow/uploads/1513301/202210/1666337866847.png "image.png")

## 架构模型（领域模型）

> 定义接口内部实现方案，结合相关的设计模式，设计代码结构，对接口内部逻辑进行分解和设计

<hr>

![img.png](img.png)
## 系统/子系统建模

```plantuml
@startuml

!include http://cloudsop.inhuawei.com/plantUML/design/k8s.puml

actor "Network Manager" as user
left to right direction

Cluster_Boundary(cluster1, "CloudSOP k8s Cluster") {
    Namespace_Boundary(ns, "API Getway") {
        KubernetesSvc(svc, "API Getway", "NBI")
    }

    Namespace_Boundary(ns2, "DTE") {
        KubernetesPod(pod1, "serviceA", "Website")
        KubernetesPod(pod2, "serviceB", "Service")
    }
}

Rel(user,svc,"get resource", "1")
Rel(svc,pod1,"load Balances to Website Pods", "2")
Rel(pod1,pod2,"get Resource", "3")
Rel_U(pod2, svc, "return Content Async", "4")
Rel(svc, user, "return Response", "5")

@enduml
```

"
