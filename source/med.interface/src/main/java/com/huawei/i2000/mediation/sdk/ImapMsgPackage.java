/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.mediation.sdk;

/**
 * 消息包结构，发送到后台的数据单元，包含消息头和数据体
 *
 * <AUTHOR>
 * @version V100R001C00
 * @since iEMP V100R001C00
 */
public class ImapMsgPackage {
    /**
     * 消息体
     */
    private byte[] data;

    /**
     * 消息头
     */
    private LmtMsgHeader msgHeader = null;

    /**
     * 构造函数，根据消息头，数据缓存构造ImapMsgPackage对象
     *
     * @param header LmtMsgHeader 消息头
     * @param buf byte[] 数据缓存
     * @param len int 从缓存中拷贝的数据长度，相对于buf[0]的长度
     */
    public ImapMsgPackage(LmtMsgHeader header, byte[] buf, int len) {
        this(header, buf, 0, len);
    }

    /**
     * 构造函数，根据消息头，数据缓存构造ImapMsgPackage对象
     *
     * @param header LmtMsgHeader 消息头
     * @param buf byte[] 缓存数据
     * @param off int 从缓存中拷贝的起始位置
     * @param len int 从缓存中拷贝数据的长度
     */
    public ImapMsgPackage(LmtMsgHeader header, byte[] buf, int off, int len) {
        if (header == null) {
            return;
        }

        msgHeader = header;

        // 参数检查
        if (buf == null || buf.length == 0) {
            return;
        }

        if (off < 0 || len < 0 || (off + len) > buf.length) {
            StringBuffer msg = new StringBuffer();
            msg.append("off = ");
            msg.append(off);
            msg.append(",len = ");
            msg.append(len);
            msg.append(",buf length = ");
            msg.append(buf.length);
            throw new IndexOutOfBoundsException(msg.toString());
        }
        // 拷贝数据
        data = new byte[len];
        System.arraycopy(buf, off, data, 0, len);

        // 设置数据体的长度
        header.setContentLength(len);
    }

    /**
     * 获得数据体
     *
     * @return byte[] 消息体
     */
    public byte[] getData() {
        return new byte[0];
    }

    /**
     * 获得数据体的长度，不包括消息头的长度
     *
     * @return int 数据体长度
     */
    public int getSize() {
        if (data == null) {
            return 0;
        } else {
            return data.length;
        }
    }

    /**
     * 获得命令执行结果
     *
     * @return int 命令执行结果
     */
    public int getResult() {
        return msgHeader.getResult();
    }

    /**
     * 获得服务访问点ID
     *
     * @return short 服务访问点ID
     */
    public short getSapID() {
        return msgHeader.getSapID();
    }

    /**
     * 获得鉴权网元ID
     *
     * @return short 鉴权网元ID
     */
    public short getNeID() {
        return msgHeader.getNeID();
    }

    /**
     * 获得用户上下文数据
     *
     * @return int 用户上下文数据
     */
    public int getUserContext() {
        return msgHeader.getUserContext();
    }

    /**
     * 获得客户端唯一标识
     *
     * @return short 客户端唯一标识
     */
    public short getSessionID() {
        return msgHeader.getSessionID();
    }

    /**
     * 获得服务访问点上的请求分发ID
     *
     * @return short 服务访问点上的请求分发ID
     */
    public short getDispatchID() {
        return msgHeader.getDispatchID();
    }

    /**
     * 获得请求ID
     *
     * @return int 请求ID
     */
    public int getRequestID() {
        return msgHeader.getRequestID();
    }

    /**
     * 获得任务ID
     *
     * @return short 任务ID
     */
    public short getTaskID() {
        return msgHeader.getTaskID();
    }

    /**
     * 获得操作网元ID
     *
     * @return short 操作网元ID
     */
    public short getOperateNeID() {
        return msgHeader.getOperateNeID();
    }

    /**
     * 获得网元工作区ID
     *
     * @return short 网元工作区ID
     */
    public short getWorkspaceID() {
        return this.msgHeader.getWorkspaceID();
    }

    /**
     * 将消息包转换为byte[]形式
     *
     * @return byte[] 消息包的byte[]形式
     */
    public byte[] toBytes() {
        byte[] buf = null;

        if (data == null) {
            buf = new byte[LmtMsgHeader.LENGTH];
            // 拷贝消息头
            System.arraycopy(msgHeader.toBytes(), 0, buf, 0, LmtMsgHeader.LENGTH);
        } else {
            buf = new byte[getSize() + LmtMsgHeader.LENGTH];
            // 拷贝消息头
            System.arraycopy(msgHeader.toBytes(), 0, buf, 0, LmtMsgHeader.LENGTH);

            // 拷贝消息体
            System.arraycopy(data, 0, buf, LmtMsgHeader.LENGTH, data.length);
        }
        return buf;
    }

    /**
     * requestID优先级最高，其次是task id为负数的通知消息，这2类都放到一个队列中分发
     * task id为正数的消息都放另外一个队列中分发（如告警、跟踪）。
     *
     * @return 校验是否是高优先级队列
     */
    public boolean isHighPriority() {
        if (msgHeader.getRequestID() != 0 || msgHeader.getTaskID() < 0) {
            return true;
        }
        return false;
    }
}
