/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.mediation.stub.connector.itpass.access;

import com.huawei.i2000.mediation.sdk.Listener;
import com.huawei.i2000.mediation.sdk.Report;

import java.util.HashSet;
import java.util.Set;

import javax.ws.rs.ApplicationPath;
import javax.ws.rs.core.Application;

/**
 * 提供给Collector上报参数的消息接口
 *
 * <AUTHOR>
 * @since V800R001C30
 */
@ApplicationPath("itpaas")
public class AccessApplication extends Application {
    private Set<Object> sets;

    private AccessResource accessResource;

    private IpModifyResource ipModifyResource;

    /**
     * Instantiates a new Access application.
     */
    public AccessApplication() {
        super();
        accessResource = new AccessResource();
        ipModifyResource = new IpModifyResource();
    }

    /**
     * Gets classes.
     *
     * @return the classes
     */
    @Override
    public Set<Class<?>> getClasses() {
        return new HashSet<Class<?>>();
    }

    /**
     * Sets report source.
     *
     * @param reportSource the report source
     */
    public void setReportSource(Listener<Report> reportSource) {
        accessResource.setReportSource(reportSource);
    }

    /**
     * Gets singletons.
     *
     * @return the singletons
     */
    @Override
    public Set<Object> getSingletons() {
        if (sets == null) {
            sets = new HashSet<Object>();
            sets.add(accessResource);
            sets.add(ipModifyResource);
        }

        return sets;
    }
}