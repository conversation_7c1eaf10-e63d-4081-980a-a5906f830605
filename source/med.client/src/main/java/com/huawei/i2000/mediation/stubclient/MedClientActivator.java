/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2025. All rights reserved.
 */

package com.huawei.i2000.mediation.stubclient;

import com.huawei.i2000.mediation.client.MediationClient;
import com.huawei.i2000.mediation.sdk.config.MedConfigUtil;
import com.huawei.i2000.mediation.sdk.lifecycle.Bootstrap;
import com.huawei.i2000.mediation.sdk.util.MedLogger;
import com.huawei.i2000.mediation.sdk.util.NamedThreadFactory;
import com.huawei.i2000.mediation.stubclient.impl.MediationClientImpl;
import com.huawei.i2000.mediation.stubclient.impl.MediationProvider;
import com.huawei.i2000.mediation.stubclient.util.MedNodeUtil;
import com.huawei.oms.util.BundleUtils;
import com.huawei.oms.util.OmsConstant;

import org.apache.commons.configuration2.PropertiesConfiguration;
import org.apache.commons.configuration2.builder.FileBasedConfigurationBuilder;
import org.apache.commons.configuration2.builder.fluent.Configurations;
import org.apache.commons.configuration2.ex.ConfigurationException;
import org.apache.commons.lang3.StringUtils;
import org.osgi.framework.BundleActivator;
import org.osgi.framework.BundleContext;
import org.slf4j.event.Level;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ApplicationContextException;

import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * the MedClient Activator
 *
 * <AUTHOR>
 * @version V100R001C00
 * @since iEMP V100R001C00
 */
public class MedClientActivator implements BundleActivator, ApplicationContextAware {
    
    // 配置文件同步后，等待热生效时间，单位毫秒
    private static final int TIME_TO_WAIT = 2000;

    private static final ThreadPoolExecutor MEDCLIENT_PUBLISH = new ThreadPoolExecutor(1, 1, 0,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1),
        new NamedThreadFactory("MedClient-publish"));

    private final Bootstrap bootstrap = new Bootstrap();

    private ApplicationContext applicationContext;

    private boolean useNewMedClient = Boolean.parseBoolean(System.getProperty("useNewMedClient"));

    private boolean isMedStandAlone = Boolean.parseBoolean(System.getProperty("isMedStandAlone", "false"));

    @Override
    public void start(BundleContext bundleContext) throws Exception {
        try {
            if (!isMedStandAlone
                    && useNewMedClient
                    && "true".equals(System.getProperty("is.master.process", "false"))) {
                MedLogger.bootMessage(
                        "{} begin to start... useNewMedClient: {} isMedStandAlone: {}",
                        "Activator",
                        useNewMedClient,
                        isMedStandAlone);
                long start = System.currentTimeMillis();
                fixWrongIp();
                syncProductConfigFromOldConfig();
                syncSoapConfig();
                syncAkskFromOldConfig();
                bootstrap.start();
                MedLogger.successBoot("Activator", System.currentTimeMillis() - start);
            }
        } catch (Throwable e) {
            MedLogger.failBoot("Activator", e);
        }
    }

    @Override
    public void stop(BundleContext bundleContext) throws Exception {
    }

    private void fixWrongIp() throws ConfigurationException {
        String hostAddressIPV6 = MedConfigUtil.getString("mediation.core.meduniqueipv6", "");
        if (StringUtils.isNotEmpty(hostAddressIPV6) && hostAddressIPV6.contains(":")) {
            MedLogger.bootMessage(Level.INFO, "meduniqueipv6 is :{}", hostAddressIPV6);
            String fixedHostAddressIPV6 = hostAddressIPV6.replaceAll("\\\\", "");  // 删除ip地址中的转义符
            if (hostAddressIPV6.equals(fixedHostAddressIPV6)) {
                return;
            }
            MedLogger.bootMessage(Level.WARN, "meduniqueipv6 is wrong, :{}", hostAddressIPV6);
            MedConfigUtil.updateProperties("mediation.properties", "mediation.core.meduniqueipv6",
                fixedHostAddressIPV6);
            MedLogger.bootMessage(Level.INFO, "update meduniqueipv6:{}", fixedHostAddressIPV6);
        }
    }

    private void syncAkskFromOldConfig() {
        String ak = MedConfigUtil.getString("rest.secretKeys.ak", StringUtils.EMPTY);
        String sk = MedConfigUtil.getString("rest.secretKeys.encryptedSk", StringUtils.EMPTY);
        if (!ak.isEmpty() && !sk.isEmpty()) {
            MedLogger.bootMessage("skip sync aksk from mednode.xml ...");
            return;
        }
        try {
            MedLogger.bootMessage("start to sync aksk from mednode.xml ...");
            Map<String, String> aksks = MedNodeUtil.getSecretKeys();
            StringBuilder aks = new StringBuilder();
            StringBuilder sks = new StringBuilder();
            aksks.forEach(
                    (key, value) -> {
                        aks.append(key).append(',');
                        sks.append(value).append(',');
                    });
            if (aks.length() > 0) {
                aks.setLength(aks.length() - 1);
            }
            if (sks.length() > 0) {
                sks.setLength(sks.length() - 1);
            }

            FileBasedConfigurationBuilder<PropertiesConfiguration> builder = getConfigBuilder("rest.properties");
            PropertiesConfiguration config = builder.getConfiguration();
            config.setProperty("rest.secretKeys.ak", aks.toString());
            config.setProperty("rest.secretKeys.encryptedSk", sks.toString());
            builder.save();
            TimeUnit.MILLISECONDS.sleep(TIME_TO_WAIT);
            MedLogger.bootMessage("sync ak {} from mednode.xml", aks.toString());
            MedLogger.bootMessage("finish sync aksk from mednode.xml");
        } catch (ConfigurationException e) {
            MedLogger.bootMessage(Level.ERROR, "failed sync aksk from mednode.xml");
        } catch (InterruptedException e) {
            MedLogger.bootMessage(Level.WARN, "sleep failed after sync aksk");
        }
    }

    private void syncSoapConfig() {
        MedLogger.bootMessage("start to sync soap config");
        String isTrustAnyone = MedNodeUtil.getSoapSecureConfig("trustAnyone");
        String sendClientKeyStore = MedNodeUtil.getSoapSecureConfig("sendClientKeyStore");

        try {
            FileBasedConfigurationBuilder<PropertiesConfiguration> builder = getConfigBuilder("soap.properties");
            PropertiesConfiguration config = builder.getConfiguration();
            config.setProperty("soap.secure.trustAnyone", isTrustAnyone);
            config.setProperty("soap.secure.sendClientKeyStore", sendClientKeyStore);
            builder.save();
            MedLogger.bootMessage(
                    "sync soap config isTrustAnyone:{} ,sendClientKeyStore:{}", isTrustAnyone, sendClientKeyStore);
        } catch (ConfigurationException e) {
            MedLogger.bootMessage(Level.ERROR, "failed sync soap config");
        }
    }

    private void syncProductConfigFromOldConfig() {
        MedLogger.bootMessage(Level.INFO, "Begin to add File Event Listener");
        new ScheduledThreadPoolExecutor(1).scheduleWithFixedDelay(new Runnable() {
            private long lastChangeSms = 0L;

            private long lastChangeSlc = 0L;

            /**
             * 每5s 判断一下产品配置文件是否发生变更，如果发生变更，则进行同步。延迟 1分钟执行。
             * 此处依赖 产品来存放配置，I2000产品化部分代码需要使用业务的配置及证书，存在业务架构上的耦合
             */
            @Override
            public void run() {
                MedLogger.bootMessage(Level.DEBUG, "Begin to check file change ");
                try {
                    File smsFile = MedNodeUtil.getSmsCfgFile();
                    File slcFile = MedNodeUtil.getSlcCfgFile();
                    if (smsFile.exists() && smsFile.lastModified() > lastChangeSms) {
                        MedLogger.bootMessage(Level.WARN, "Begin to reload sms File changeTime {} ",
                            smsFile.lastModified());
                        lastChangeSms = smsFile.lastModified();
                        syncSms();
                    }
                    if (slcFile.exists() && slcFile.lastModified() > lastChangeSlc) {
                        MedLogger.bootMessage(Level.WARN, "Begin to reload slc File changeTime {} ",
                            slcFile.lastModified());
                        lastChangeSlc = slcFile.lastModified();
                        syncSlc();
                    }
                } catch (IOException e) {
                    MedLogger.bootMessage(Level.ERROR, "failed to listen SMS and SLC File", e);
                }
            }
        }, 60, 5, TimeUnit.SECONDS);
    }

    private void syncSms() {
        try {
            MedLogger.bootMessage("start to sync sms config from config.xml ...");

            String storepass = MedNodeUtil.getSmsConfig("secure", "storepass");
            String keypass = MedNodeUtil.getSmsConfig("secure", "keypass");
            String trustAnyone = MedNodeUtil.getSmsConfig("secure", "trustAnyone");
            String certTypes = MedNodeUtil.getSmsConfig("secure", "cacertType");
            String tlsVersion = MedNodeUtil.getSmsConfig("secure", "protocol");
            String charset = MedNodeUtil.getSmsConfig("MMLProtocol", "decode");

            if (storepass == null || keypass == null) {
                MedLogger.bootMessage(Level.WARN, "fail to read old storepass or keypass config from xml");
                return;
            }

            if (trustAnyone == null || tlsVersion == null || certTypes == null) {
                MedLogger.bootMessage(Level.WARN, "fail to read old config from xml");
                return;
            }

            if (charset == null) {
                charset = MedNodeUtil.getSmsConfig("MTProtocol", "MMLdecode");
                if (charset == null) {
                    MedLogger.bootMessage(Level.WARN, "fail to read old config from xml");
                    return;
                }
            }

            FileBasedConfigurationBuilder<PropertiesConfiguration> builder = getConfigBuilder("mtserver.properties");
            PropertiesConfiguration config = builder.getConfiguration();
            config.setProperty("sms.secure.storepass", storepass);
            config.setProperty("sms.secure.keypass", keypass);
            config.setProperty("sms.secure.trustAnyone", trustAnyone);
            config.setProperty("sms.protocol.ca.certTypes", certTypes);
            config.setProperty("sms.protocol.tlsVersion", tlsVersion);
            config.setProperty("sms.protocol.mml.decode.charset", charset);

            builder.save();
            MedLogger.bootMessage("finish sync sms config");
        } catch (ConfigurationException e) {
            MedLogger.bootMessage(Level.ERROR, "failed sync sms config");
        } catch (IOException e) {
            MedLogger.bootMessage(Level.WARN, "sms config file not exist");
        }
    }

    private void syncSlc() {
        try {
            MedLogger.bootMessage("start to sync slc config from config.xml ...");

            String storepass = MedNodeUtil.getSlcConfig("storepass");
            String keypass = MedNodeUtil.getSlcConfig("keypass");
            String trustAnyone = MedNodeUtil.getSlcConfig("trustAnyone");
            String certTypes = MedNodeUtil.getSlcConfig("cacertType");
            String tlsVersion = MedNodeUtil.getSlcConfig("protocol");

            if (storepass == null || keypass == null) {
                MedLogger.bootMessage(Level.WARN, "syncSlc, fail to read old storepass or keypass config from xml");
                return;
            }

            if (trustAnyone == null || tlsVersion == null || certTypes == null) {
                MedLogger.bootMessage(Level.WARN, "syncSlc, fail to read old config from xml");
                return;
            }

            FileBasedConfigurationBuilder<PropertiesConfiguration> builder = getConfigBuilder("slc.properties");
            PropertiesConfiguration config = builder.getConfiguration();
            config.setProperty("slc.secure.storepass", storepass);
            config.setProperty("slc.secure.keypass", keypass);
            config.setProperty("slc.secure.trustAnyone", trustAnyone);
            config.setProperty("slc.protocol.ca.certTypes", certTypes);
            config.setProperty("slc.protocol.tlsVersion", tlsVersion);

            builder.save();
            MedLogger.bootMessage("finish sync slc config");
        } catch (ConfigurationException e) {
            MedLogger.bootMessage(Level.ERROR, "failed sync slc config");
        } catch (IOException e) {
            MedLogger.bootMessage(Level.WARN, "slc config file not exist");
        }
    }

    private FileBasedConfigurationBuilder<PropertiesConfiguration> getConfigBuilder(String configName) {
        String dir = System.getProperty(OmsConstant.OMS_PATH_ETC, "etc");
        dir = dir + File.separator + "mediation";
        File restProperties = new File(dir, configName);

        Configurations configs = new Configurations();
        return configs.propertiesBuilder(restProperties);
    }

    /**
     * the publish Mediation Client Osgi Service
     */
    public void publishMediationClientOsgiService() {
        if (useNewMedClient && "true".equals(System.getProperty("is.master.process", "false"))) {
            // 通过java代码的方式发布osgi服务
            MEDCLIENT_PUBLISH.execute(() -> {
                try {
                    if (applicationContext == null) {
                        MedLogger.bootMessage(Level.ERROR, "Spring ApplicationContext is NULL");
                        throw new ApplicationContextException("Spring ApplicationContext is NULL");
                    }

                    MedLogger.bootMessage("start to publish mediationClient");
                    while (true) {
                        Object service = applicationContext.getBean("mediationClient");
                        if (service != null) {
                            MediationClientImpl client = (MediationClientImpl) service;
                            MediationProvider provider = client.getProvider();
                            if (provider == null) {
                                MedLogger.bootMessage(Level.ERROR, "mediation provider not ready");
                                TimeUnit.SECONDS.sleep(2);
                                continue;
                            }
                            if (isMedStandAlone && provider.getMediationStub() == null) {
                                MedLogger.bootMessage(Level.ERROR, "medstub not ready");
                                TimeUnit.SECONDS.sleep(2);
                                continue;
                            }
                            BundleUtils.getBundle(MedClientActivator.class)
                                .getBundleContext()
                                .registerService("com.huawei.i2000.mediation.client.MediationClient", service, null);
                            MedLogger.bootMessage("publish MediationClient service success. {}",
                                MediationClient.class.getName());
                        } else {
                            MedLogger.bootMessage(Level.WARN,
                                "publish MediationClient service error. get null from applicationContext");
                        }
                        break;
                    }
                } catch (Exception e) {
                    MedLogger.bootMessage(Level.ERROR, "publish MediationClient service error.", e);
                }
            });
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
