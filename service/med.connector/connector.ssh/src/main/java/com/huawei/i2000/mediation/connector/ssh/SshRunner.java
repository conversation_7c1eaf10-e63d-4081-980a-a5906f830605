/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.mediation.connector.ssh;

import com.huawei.i2000.mediation.connector.common.base.MedRunner;
import com.huawei.i2000.mediation.connector.common.task.TaskManager;
import com.huawei.i2000.mediation.connector.common.util.MedUtils;
import com.huawei.i2000.mediation.connector.ssh.sshdclient.SshClient;
import com.huawei.i2000.mediation.sdk.Request;
import com.huawei.i2000.mediation.sdk.Response;
import com.huawei.i2000.mediation.sdk.Target;
import com.huawei.i2000.mediation.sdk.connector.consts.MedConsts;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * Ssh 异步执行接口
 *
 * <AUTHOR>
 * @since V800R001C30
 */
public final class SshRunner extends MedRunner {
    /**
     * The Logger.
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(SshRunner.class);

    /**
     * SSH 客户端
     */
    private SshClient client;

    /**
     * Instantiates a new Ssh runner.
     *
     * @param uuid the uuid
     * @param taskMgr the task mgr
     */
    public SshRunner(String uuid, TaskManager taskMgr) {
        super(uuid, taskMgr);
    }

    /**
     * Login.
     *
     * @param login the login
     * @throws IOException the io exception
     */
    @Override
    protected void login(Request login) throws IOException {
        if (this.client != null) {
            throw new IOException("Ssh client is connected.");
        }
        Target targer = login.getTarget();
        String user = targer.getParam(MedConsts.USER);
        String pvalue = targer.getParam(MedConsts.USERPWD);
        String host = targer.getParam(MedConsts.HOST);
        String strPort = targer.getParam(MedConsts.PORT);
        int port = SshConsts.SSH_PORT;
        if (strPort != null) {
            port = Integer.parseInt(strPort);
        }
        char[] valueChars = pvalue == null ? null : pvalue.toCharArray();
        this.client = SshClient.newBuilder().user(user).password(valueChars).host(host).port(port).build();
        this.client.login();
    }

    /**
     * Logout.
     *
     * @throws Exception the exception
     */
    @Override
    protected void logout() throws Exception {
        if (client == null) {
            return;
        }
        destroy();
        client.logout();
        client = null;
    }

    /**
     * Handle response.
     *
     * @param bean the bean
     * @return the response
     * @throws IOException the io exception
     */
    @Override
    public Response handle(Request bean) throws IOException {
        LOGGER.debug("Header:Request={}", bean);
        Object reply = null;
        String cmd = bean.getTarget().getParam(SshConsts.COMMAND);
        if (SshConsts.CMD_EXEC.equals(cmd)) {
            reply = handleExec(bean);
        } else {
            throw new IOException("Invalid request " + bean.toString());
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("Reply:Reply={},Request={}", reply, bean);
        }
        return MedUtils.newResponse(bean, reply);
    }

    private Object handleExec(Request exec) throws IOException {
        Target targer = exec.getTarget();
        String cmd = targer.getParam(SshConsts.EXEC_KEY);
        if (cmd == null || cmd.isEmpty()) {
            throw new IOException("command is null");
        }
        if (client == null) {
            LOGGER.error("client is null");
            throw new IOException("client is null");
        }
        return client.exec(cmd);
    }
}
