/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2019-2020. All rights reserved.
 */

package com.huawei.i2000.mediation.connector.trace.mech;

import com.huawei.i2000.mediation.connector.trace.SSLProtocolSocketFactory;
import com.huawei.i2000.mediation.connector.trace.UoaSslCertificateUtils;
import com.huawei.i2000.mediation.connector.trace.common.SvcHandler;
import com.huawei.i2000.mediation.connector.trace.message.UOAMsgConstants;
import com.huawei.i2000.mediation.sdk.config.MedConfigUtil;

import lombok.Getter;
import lombok.Setter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.InetAddress;
import java.nio.ByteBuffer;
import java.nio.channels.SelectionKey;
import java.nio.channels.SocketChannel;
import java.util.ArrayList;
import java.util.List;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLEngine;
import javax.net.ssl.SSLEngineResult;
import javax.net.ssl.SSLEngineResult.HandshakeStatus;
import javax.net.ssl.SSLException;
import javax.net.ssl.SSLSession;

/**
 * TCP 连接句柄
 *
 * @since 2020/9/18
 */
public class TCPHandler extends SvcHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(TCPHandler.class);

    private static final int MAX_MSG_SIZE = 5000;

    // 未解密的消息头
    private static final int UNWRAP_HEADER = 5;

    private SocketChannel channel;

    private Object connectionsMutex = new Object();

    @Setter
    private MsgDispatcher dispatcher;

    @Setter
    private ConnStateObservable observable;

    @Getter
    private boolean canWork;

    @Setter
    @Getter
    private int timeNoSend;

    @Getter
    @Setter
    private int timeNoRcv;

    private List<ByteBuffer> queue = new ArrayList<ByteBuffer>();

    private Object queueMutex = new Object();

    // 对端IP
    @Getter
    private String remoteIP;

    // 对端端口
    @Setter
    @Getter
    private int remotePort;

    // uoaversion
    @Setter
    @Getter
    private String uoaVersion = "";

    private ByteBuffer netOutData;

    private ByteBuffer appOutData;

    private ByteBuffer netInData;

    private ByteBuffer appInData;

    private SSLEngine sslEngine;

    private HandshakeStatus hsStatus;

    // 保存解密前剩余消息
    private byte[] unDecryptRemainBuf = new byte[0];

    public byte[] getUnDecryptRemainBuf() {
        return unDecryptRemainBuf.clone();
    }

    public void setUnDecryptRemainBuf(byte[] unDecryptRemainBuf) {
        this.unDecryptRemainBuf = unDecryptRemainBuf.clone();
    }

    // 保存解密后剩余消息
    private byte[] decryptRemainBuf = new byte[0];

    public byte[] getDecryptRemainBuf() {
        return decryptRemainBuf == null ? null : decryptRemainBuf.clone();
    }

    public void setDecryptRemainBuf(byte[] decryptRemainBuf) {
        this.decryptRemainBuf = decryptRemainBuf == null ? null : decryptRemainBuf.clone();
    }

    private String[] cipherSuites;

    private static final String CIPHER_SUITES = "trace.TLS.CIPHER.SUITES";

    /**
     * Constructor for TCPHandler
     */
    public TCPHandler() {
        this.channel = null;
        this.queue.clear();
        this.canWork = false;
        this.timeNoRcv = 0;
        this.timeNoSend = 0;
        this.remoteIP = "";
        this.remotePort = 0;
        this.cipherSuites = getCipherSuites();
    }

    /**
     * 打开SocketChannel
     *
     * @param scoketchannel Channel
     */
    @Override
    public void open(final SocketChannel scoketchannel) {
        synchronized (this.connectionsMutex) {
            this.channel = scoketchannel;

            try {
                scoketchannel.configureBlocking(false);

                this.getReactor().registerHandler(this, this.channel, SelectionKey.OP_READ);

                this.canWork = true;

                InetAddress inetAddress = scoketchannel.socket().getInetAddress();
                if (inetAddress != null) {
                    setRemoteIPValue(inetAddress.toString());
                }

                this.remotePort = scoketchannel.socket().getPort();

                if (this.observable != null) {
                    try {
                        this.observable.notifyOnConnect(this);
                    } catch (Exception e) {
                        LOGGER.error("Exception in TCPHandler.open(1), ", e);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("Exception in TCPHandler.open(2), ", e);
            }
        }
    }

    /**
     * 打开SocketChannel
     *
     * @param scoketchannel Channel
     * @param bidirectional 单向或双向认证
     * @return boolean
     */
    public boolean sslConnectOpen(final SocketChannel scoketchannel, boolean bidirectional) {
        synchronized (this.connectionsMutex) {
            this.channel = scoketchannel;

            try {
                scoketchannel.configureBlocking(false);

                // SSL握手
                if (!doHandshake(bidirectional)) {
                    // 握手失败;
                    LOGGER.error("SSL Handshake fail");
                    return false;
                }
                this.getReactor().registerHandler(this, this.channel, SelectionKey.OP_READ);
                LOGGER.info("SSL Handshake success");
                this.canWork = true;

                InetAddress inetAddress = scoketchannel.socket().getInetAddress();
                if (inetAddress != null) {
                    setRemoteIPValue(inetAddress.toString());
                }

                this.remotePort = scoketchannel.socket().getPort();
                if (!bidirectional) {
                    this.uoaVersion = UOAMsgConstants.SAFE_UOA_TYPE;
                }
                if (this.observable != null) {
                    try {
                        this.observable.notifyOnConnect(this);
                    } catch (Exception e) {
                        LOGGER.error("Exception in TCPHandler.open(1), ", e);
                    }
                }
            } catch (Exception e) {
                LOGGER.error("Exception in TCPHandler.open(2), ", e);
                return false;
            }
            return true;
        }
    }

    private void setRemoteIPValue(String ip) {
        if (ip == null || ip.isEmpty()) {
            this.remoteIP = "";
        } else {
            this.remoteIP = ip.substring(1);
        }
    }

    /**
     * handle
     *
     * @return boolean
     */
    @Override
    public boolean handle() {
        if (this.dispatcher == null) {
            return false;
        }

        if (!this.dispatcher.handleInput(this)) {
            synchronized (this.connectionsMutex) {
                this.canWork = false;
                if (this.observable != null) {
                    LOGGER.debug("[autoUOADisconnet] TCPHandler.handle() close connect.");
                    this.observable.notifyOnDisConnect(this);
                }

                try {
                    this.channel.close();
                } catch (Exception e) {
                    LOGGER.error("Exception in TCPHandler.handle(), ", e);
                }

                LOGGER.error(
                        "ERROR: fail to read data from connection");
            }

            synchronized (this.queueMutex) {
                this.queue.clear();
            }

            return false;
        } else {
            this.setTimeNoRcv(0);
            return true;
        }
    }

    /**
     * 证书握手
     *
     * @param bidirectional 单向还是双向通道
     * @return boolean
     * @throws IOException IOException
     */
    public boolean doHandshake(boolean bidirectional) throws IOException {
        SSLContext sslContext = null;
        if (bidirectional) {
            sslContext = SSLProtocolSocketFactory.getServerContext();
        } else {
            sslContext = UoaSslCertificateUtils.getServerContext();
        }
        if (sslContext == null) {
            return false;
        }
        sslEngine = sslContext.createSSLEngine();
        sslEngine.setUseClientMode(true);
        sslEngine.setEnabledCipherSuites(cipherSuites);
        SSLSession session = sslEngine.getSession();
        appOutData = ByteBuffer.allocate(session.getApplicationBufferSize());
        netOutData = ByteBuffer.allocate(session.getPacketBufferSize());
        appInData = ByteBuffer.allocate(session.getApplicationBufferSize());
        netInData = ByteBuffer.allocate(session.getPacketBufferSize());
        sslEngine.beginHandshake();
        hsStatus = sslEngine.getHandshakeStatus();
        return processHandshake();
    }

    private String[] getCipherSuites() {
        return MedConfigUtil.getString(CIPHER_SUITES, "").split(",");
    }

    private void clearSslBuf() {
        appOutData.clear();
        netOutData.clear();
        appInData.clear();
        netInData.clear();
    }

    private boolean processHandshake() throws IOException {
        SSLEngineResult result;
        while (hsStatus != HandshakeStatus.FINISHED) {
            switch (hsStatus) {
                case NEED_TASK:
                    hsStatus = doTask();
                    break;
                case NEED_UNWRAP:
                    int count = channel.read(netInData);
                    if (count < 0) {
                        clearSslBuf();
                        LOGGER.error("handshake socket read data -1");
                        return false;
                    }
                    netInData.flip();
                    appInData.clear();
                    do {
                        result = sslEngine.unwrap(netInData, appInData);
                        hsStatus = result.getHandshakeStatus();
                    } while (handleResult(result) && hsStatus == HandshakeStatus.NEED_UNWRAP);
                    netInData.compact();
                    break;
                case NEED_WRAP:
                    netOutData.clear();
                    result = sslEngine.wrap(ByteBuffer.allocate(0), netOutData);
                    hsStatus = result.getHandshakeStatus();
                    if (handleResult(result)) {
                        netOutData.flip();
                        while (netOutData.hasRemaining()) {
                            try {
                                channel.write(netOutData);
                            } catch (Exception e) {
                                LOGGER.error("Exception in channel write() ", e);
                                break;
                            }
                        }
                    }
                    break;
                default:
                    break;
            }
        }
        clearSslBuf();
        LOGGER.info("Handshake complete");
        return true;
    }

    private boolean handleResult(SSLEngineResult result) {
        switch (result.getStatus()) {
            case OK:
                return true;
            case BUFFER_OVERFLOW:
                return false;
            case BUFFER_UNDERFLOW:
                return false;
            case CLOSED:
                return false;
            default:
                return false;
        }
    }

    private HandshakeStatus doTask() {
        Runnable task;
        while ((task = sslEngine.getDelegatedTask()) != null) {
            task.run();
        }
        return sslEngine.getHandshakeStatus();
    }

    private int writeData(ByteBuffer buffer) throws IOException {
        if(hsStatus!=null){
            LOGGER.info("handshake status: {} ",hsStatus);
        }
        int count = 0;
        if (channel.isConnected() && buffer != null
            && (hsStatus == HandshakeStatus.NOT_HANDSHAKING || hsStatus == HandshakeStatus.FINISHED)) {
            appOutData.clear();
            appOutData.put(buffer.array());
            appOutData.flip();
            netOutData.clear();
            SSLEngineResult result = sslEngine.wrap(appOutData, netOutData);
            if (handleResult(result)) {
                netOutData.flip();
                while (netOutData.hasRemaining()) {
                    count = channel.write(netOutData);
                }
            }
        }
        return count;
    }

    /**
     * 把dest数组拼接到src后面，返回一个新数组
     *
     * @param src 第一个数组
     * @param dest 第二个数组
     * @return 合并后的数组
     */
    public byte[] addBytes(byte[] src, byte[] dest) {
        byte[] dataRes = new byte[0];
        if (src != null && dest != null) {
            dataRes = new byte[src.length + dest.length];
            System.arraycopy(src, 0, dataRes, 0, src.length);
            System.arraycopy(dest, 0, dataRes, src.length, dest.length);
        }
        return dataRes;
    }

    private byte[] readDataFormChannel(byte[] remainData) throws IOException {
        ByteBuffer readDataBuffer = ByteBuffer.allocate(netInData.capacity());
        byte[] remain = remainData;

        // 循环读取数据
        while (true) {
            int count = channel.read(readDataBuffer);
            if (count < 0) {
                LOGGER.error("error: read data count -1");
                channel.close();
                return null;
            }
            if (count > 0) {
                boolean hasRemain = (readDataBuffer.position() == readDataBuffer.limit());
                byte[] temp = new byte[readDataBuffer.position()];
                readDataBuffer.flip();
                readDataBuffer.get(temp);
                byte[] combine = addBytes(remain, temp);
                remain = combine;
                readDataBuffer.clear();
                LOGGER.debug("count:{}, readData length:{}", count, temp.length);
                if (hasRemain) {
                    continue;
                } else {
                    break;
                }
            }
            // 这种情况好像没见到
            if (readDataBuffer.position() == readDataBuffer.limit()) {
                byte[] temp = new byte[readDataBuffer.position()];
                readDataBuffer.flip();
                readDataBuffer.get(temp);
                byte[] combine = addBytes(remain, temp);
                remain = combine;
                readDataBuffer.clear();
                LOGGER.debug("count:{}, readData length:{}", count, temp.length);
                continue;
            }
        }

        return remain;
    }

    private byte[] unwrapData(byte[] remain) throws SSLException {
        byte[] result = new byte[0];
        // 对未解密的数据进行处理和使用unwrap解密
        int pos = 0;
        int length = remain.length;
        while (length > 0) {
            int len = 0;
            byte[] recvLen = new byte[UNWRAP_HEADER];
            if (length > UNWRAP_HEADER) {
                System.arraycopy(remain, pos, recvLen, 0, UNWRAP_HEADER);
                // 17 03 03 xx xx 后面2位是TLS报文长度
                len = Byte.toUnsignedInt(recvLen[3]) << 8 | Byte.toUnsignedInt(recvLen[4]);
            }
            LOGGER.debug("unwrap len = {}", len);
            if ((len != 0) && ((length - UNWRAP_HEADER) >= len)) {
                pos += UNWRAP_HEADER;
                length -= UNWRAP_HEADER;
                byte[] remainBuf = new byte[len];
                System.arraycopy(remain, pos, remainBuf, 0, len);
                appInData.clear();
                ByteBuffer tmp = ByteBuffer.allocate(len + UNWRAP_HEADER);
                tmp.put(recvLen);
                tmp.put(remainBuf);
                tmp.flip();
                LOGGER.debug("tmp length = {}", tmp.array().length);
                SSLEngineResult engineResult = sslEngine.unwrap(tmp, appInData);
                if (handleResult(engineResult)) {
                    appInData.flip();
                    byte[] bs = new byte[appInData.limit()];
                    appInData.get(bs);
                    result = addBytes(result, bs);
                    pos += len;
                    length -= len;
                }
            } else {
                if (len != 0) {
                    byte[] smllBuf = new byte[length];
                    System.arraycopy(remain, pos, smllBuf, 0, length);
                    unDecryptRemainBuf = smllBuf;
                    LOGGER.debug("not enough data, length={}", length);
                }
                break;
            }
        }
        // 数据刚好够，需要把上次剩余的buf清空
        if (length == 0 && pos == remain.length && unDecryptRemainBuf.length != 0) {
            LOGGER.info(" last length = {},pos={}", length, pos);
            unDecryptRemainBuf = new byte[0];
        }
        return result;
    }

    /**
     * 证书读取数据方法
     *
     * @return 读取到的数据
     * @throws IOException IOException
     */
    public byte[] readData() throws IOException {
        byte[] remainData = unDecryptRemainBuf;
        byte[] remain = readDataFormChannel(remainData);
        byte[] result = null;
        if (remain != null) {
            try {
                result = unwrapData(remain);
            } catch (SSLException e) {
                appInData.clear();
                unDecryptRemainBuf = new byte[0];
                LOGGER.error("unwrap has error, exception={}", e);
                return null;
            }
        }
        return result;
    }

    /**
     * 发送数据
     *
     * @param msg 消息体
     * @return boolean
     */
    public boolean sendData(final ByteBuffer msg) {
        synchronized (this.queueMutex) {
            if (this.queue.size() >= MAX_MSG_SIZE) {
                LOGGER.debug("TRACE: the queue for RemoteIP and RemotePort");
                return false;
            } else {
                queue.add(msg);
                this.timeNoSend = 0;

                return true;
            }
        }
    }

    /**
     * processMsg
     *
     * @return int
     */
    public int processMsg() {
        int ret = 0;
        ByteBuffer msg = null;

        synchronized (this.queueMutex) {
            for (int i = 0; i < 10; ++i) {
                if (this.queue.size() == 0) {
                    // 队列空
                    ret = 0;
                    break;
                }

                if (!this.canWork) {
                    break;
                }

                try {
                        msg = queue.get(0);
                        ret = channel.write(msg);
                        if (msg.remaining() == 0) {
                            queue.remove(0);
                        } else {
                            break;
                        }
                } catch (Exception e) {
                    LOGGER.error("Exception in TCPHandler.processMsg(), ", e);
                    ret = -1;
                    break;
                }
            }
        }

        return ret;
    }

    /**
     * processMsg
     *
     * @return int
     */
    public int processSSLMsg() {
        int retRes = 0;
        ByteBuffer msgBuf = null;

        synchronized (this.queueMutex) {
            for (int i = 0; i < 10; ++i) {
                if (this.queue.size() == 0) {
                    // 队列空
                    retRes = 0;
                    break;
                }

                if (!this.canWork) {
                    break;
                }

                try {
                        msgBuf = queue.get(0);
                        retRes = writeData(msgBuf);
                        if (retRes != 0) {
                            queue.remove(0);
                        } else {
                            break;
                        }
                } catch (Exception e) {
                    LOGGER.error("Exception in TCPHandler.processSSLMsg(), ", e);
                    retRes = -1;
                    break;
                }
            }
        }

        return retRes;
    }


    /**
     * 关闭Channel
     */
    public void shutDownChannel() {
        try {
            this.canWork = false;

            this.channel.socket().shutdownOutput();
            this.channel.socket().shutdownInput();
            LOGGER.info("shutdown RemoteIP and RemotePort successfully");

            if (this.observable != null) {
                LOGGER.debug("[autoUOADisconnet] TCPHandler.shutDownChannel() close connect.");
                this.observable.notifyOnDisConnect(this);
            }

            this.getReactor().closeHandler(this);
        } catch (Exception e) {
            LOGGER.error("ERROR : Fail to shutdown RemoteIP and RemotePort");
            LOGGER.error("Exception in TCPHandler.shutDown(), ", e);

            this.canWork = false;

            if (this.observable != null) {
                this.observable.notifyOnDisConnect(this);
            }

            this.getReactor().closeHandler(this);
        }
    }

    /**
     * Get SocketChannel
     *
     * @return SocketChannel
     */
    public SocketChannel getSocket() {
        return channel;
    }

    /**
     * Get SSLEngine
     *
     * @return SSLEngine
     */
    public SSLEngine getSSLEngine() {
        return sslEngine;
    }
}
