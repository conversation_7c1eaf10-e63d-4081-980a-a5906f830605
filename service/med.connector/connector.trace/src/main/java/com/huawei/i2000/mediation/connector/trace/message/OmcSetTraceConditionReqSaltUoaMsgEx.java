/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2019-2020. All rights reserved.
 */

package com.huawei.i2000.mediation.connector.trace.message;

import com.huawei.i2000.mediation.connector.trace.mech.sxp.util.ConstantFuc;
import com.huawei.i2000.mediation.connector.trace.mech.sxp.util.Offset;

import lombok.Getter;
import lombok.Setter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

/**
 * 扩展任务创建接口
 *
 * @since 2020/9/18
 */
public class OmcSetTraceConditionReqSaltUoaMsgEx extends OmcSetTraceConditionReqSaltUoaMsg {
    private static final Logger logger = LoggerFactory.getLogger(OmcSetTraceConditionReqSaltUoaMsgEx.class);

    /**
     * 空ModuleCode
     */
    private static final byte[] NULL_MODULE_CODE =
        new byte[] {0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0};

    /**
     * 附加信息，由name=value组成的名值对，ENIPCORE任务个数限制
     */
    private String additionalCondition = "";

    /**
     * 给设备侧分配的任务ID
     */
    @Setter
    @Getter
    private long devTaskID;

    /**
     * 设备模块编号
     */
    @Setter
    @Getter
    private List<String> devModules;

    /**
     * 默认构造函数
     */
    public OmcSetTraceConditionReqSaltUoaMsgEx() {
        super();

        this.commandID = UOACommandIDList.UOA_ID_OMCSETTRACECONDITION_REQ;
        this.version = UOAMsgConstants.SALT_UOA_VERSION;

        this.additionalCondition = "";
        this.devTaskID = 0;
        this.devModules = new ArrayList<String>();
    }

    public String getAdditionalCondition() {
        return additionalCondition;
    }

    public void setAdditionalCondition(String additionalCondition) {
        this.additionalCondition = additionalCondition;
    }

    /**
     * 网络编码
     *
     * @param key 消息体
     * @return ByteBuffer
     */
    @Override
    public ByteBuffer encode(byte[] key) {
        if (key == null) {
            logger.error("key null.");
            return null;
        }

        int len = this.getLength();

        ByteBuffer msgBuf = ByteBuffer.allocate(len);

        Offset idx = new Offset();

        // Message
        ConstantFuc.encodeInt(idx, msgBuf, len);
        ConstantFuc.encodeShort(idx, msgBuf, this.version);
        ConstantFuc.encodeShort(idx, msgBuf, this.commandID);
        ConstantFuc.encodeInt(idx, msgBuf, this.sequenceID);

        // OmcSetTraceConditionReqMsg
        ConstantFuc.encodeShort(idx, msgBuf, this.reportMsgBody);
        ConstantFuc.encodeShort(idx, msgBuf, this.traceDepth);
        ConstantFuc.encodeShort(idx, msgBuf, this.traceType);
        ConstantFuc.encodeInt(idx, msgBuf, this.traceTargetNeID);

        if (this.moduleCode == null || this.moduleCode.isEmpty()) {
            ConstantFuc.encodeBytes(idx, msgBuf, NULL_MODULE_CODE, UOAMsgConstants.UOA_PARA_MODULE_CODE_LEN);
        } else {
            ConstantFuc.encodeString(idx, msgBuf, this.moduleCode, UOAMsgConstants.UOA_PARA_MODULE_CODE_LEN);
        }

        ConstantFuc.encodeShort(idx, msgBuf, this.traceProtocol);
        ConstantFuc.encodeShort(idx, msgBuf, this.sendReport);
        ConstantFuc.encodeInt(idx, msgBuf, this.beginTime);
        ConstantFuc.encodeInt(idx, msgBuf, this.endTime);
        ConstantFuc.encodeInt(idx, msgBuf, this.extraTraceCondition.length());
        ConstantFuc.encodeString(idx, msgBuf, this.extraTraceCondition);

        // OmcSetTraceConditionReqMsgExTwo
        if (this.additionalCondition != null && !this.additionalCondition.isEmpty()) {
            // 编码附加条件
            ConstantFuc.encodeInt(idx, msgBuf, this.additionalCondition.length());
            ConstantFuc.encodeString(idx, msgBuf, this.additionalCondition);
        }

        // 编码任务ID
        ConstantFuc.encodeUInt(idx, msgBuf, this.devTaskID);

        // 编码模块数
        if (this.devModules == null || this.devModules.isEmpty()) {
            ConstantFuc.encodeShort(idx, msgBuf, (short) 0);
        } else {
            // size大于0
            ConstantFuc.encodeUShort(idx, msgBuf, this.devModules.size());

            for (String modul : this.devModules) {
                ConstantFuc.encodeUShort(idx, msgBuf, modul.length());
                ConstantFuc.encodeString(idx, msgBuf, modul);
            }
        }

        // h00174746 20140930 加密整改 MD5 -> sha256
        // 1.SHA256加密
        byte[] sha256 = DigestUtils.encodeSHA256(key);
        // 2.BASE64加密
        byte[] base64 = Base64.encode(sha256);
        ConstantFuc.encodeBytes(idx, msgBuf, base64, UOAMsgConstants.UOA_PARA_KEY_LEN);

        return msgBuf;
    }

    @Override
    public String toString() {
        // 安全红线禁止打印用户信息
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(super.toString());
        stringBuffer.append("[INFO][NODE-UOA]OmcSetTraceConditionReqMsgExTwo" + System.lineSeparator());
        stringBuffer.append("Dev task ID = ").append(devTaskID).append(System.lineSeparator());
        stringBuffer.append("Dev task Modules list = ").append(devModules).append(System.lineSeparator());

        if (additionalCondition == null) {
            stringBuffer.append("additionalCondition = null");
        } else {
            stringBuffer.append("additionalCondition.len = ").append(additionalCondition.length());
        }

        return stringBuffer.toString();
    }

    /**
     * 获取总长度
     *
     * @return int
     */
    @Override
    public int getLength() {
        int msgLength = super.getLength();

        // 判断附加信息字段
        if (additionalCondition != null && !additionalCondition.isEmpty()) {
            // 存在附加信息字段
            this.commandID = UOACommandIDList.UOA_ID_OMCSETTRACECONDITION_REQ_EX;

            msgLength += UOAMsgConstants.INT_LEN + additionalCondition.length();
        }

        // 跟踪任务ID
        msgLength += UOAMsgConstants.INT_LEN;

        // 需要创建的跟踪模块个数
        msgLength += UOAMsgConstants.SHORT_LEN;

        if (this.devModules != null && !this.devModules.isEmpty()) {
            for (String modul : this.devModules) {
                msgLength += UOAMsgConstants.SHORT_LEN + modul.length();
            }
        }

        return msgLength;
    }
}
