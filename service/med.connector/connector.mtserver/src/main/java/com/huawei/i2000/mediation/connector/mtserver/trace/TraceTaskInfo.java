/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.mediation.connector.mtserver.trace;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 一个MTServer跟踪任务的信息
 * 可能对应着多个oms的跟踪任务
 *
 * <AUTHOR>
 * @since 2021/7/9
 */
public class TraceTaskInfo {
    private static final Logger LOGGER = LoggerFactory.getLogger(TraceTaskInfo.class);

    // MTServer的跟踪id
    private int mtservTraceId;

    // 跟踪条件生成的key
    private String conditionKey;

    // 一个MTServer跟踪任务对应的OMS跟踪任务。可能是多个，key为oms的跟踪id，value为跟踪状态。
    private Map<Integer, TraceStatus> omsTask;

    /**
     * Instantiates a new Trace task info.
     *
     * @param mtservTraceId the mtserv trace id
     * @param conditionKey the condition key
     */
    protected TraceTaskInfo(int mtservTraceId, String conditionKey) {
        this.mtservTraceId = mtservTraceId;
        this.conditionKey = conditionKey;
        omsTask = new ConcurrentHashMap<>();
    }

    /**
     * 添加一个跟踪任务
     * 添加前要先判断此任务是否可以复用
     *
     * @param omsTraceId the oms trace id
     * <AUTHOR> 2014-3-6
     */
    protected void addOMSTask(int omsTraceId) {
        omsTask.put(omsTraceId, TraceStatus.NORMAL);
    }

    /**
     * 改变跟踪任务的状态，可以对任务进行暂停和回复
     * 如果任务存在返回true，不存在返回false
     *
     * @param omsTraceId the oms trace id
     * @param stat the stat
     * @return the boolean
     */
    protected boolean changeOMSTaskStatus(int omsTraceId, TraceStatus stat) {
        TraceStatus traceStatus = omsTask.get(omsTraceId);
        if (traceStatus != null) {
            omsTask.put(omsTraceId, stat);
            return true;
        }
        return false;
    }

    /**
     * 删除一个oms的跟踪任务信息。如果此mtserver跟踪对应的oms跟踪没有了，返回true
     * 然后由上层负责删除该mtserver的跟踪信息
     *
     * @param traceId the trace id
     * @return the boolean
     */
    protected boolean removeOMSTask(int traceId) {
        omsTask.remove(traceId);

        LOGGER.debug(
            "Remove oms trace task, oms trace Id:{}, mtserver trace Id:{}, there are {} oms trace task using this task.",
            traceId, mtservTraceId, omsTask.size());

        return omsTask.isEmpty();
    }

    /**
     * 获取此跟踪任务中对应的OMS跟踪任务id
     *
     * @return the all oms task id
     */
    protected List<Integer> getAllOMSTaskId() {
        List<Integer> omsIds = new ArrayList<Integer>(omsTask.size());
        omsIds.addAll(omsTask.keySet());
        return omsIds;
    }

    /**
     * 获取mtserver下的一个mtserver跟踪任务中的所有正常的oms跟踪任务id
     * 这个方法获取不到必须返回null。因为上层需要对这个值进行判断，是否为vas的消息跟踪
     * 因为vas全流程跟踪的id可能是其他设备的跟踪id，不在当前的列表中
     *
     * @return the all oms nornal task id
     */
    protected List<Integer> getAllOMSNornalTaskId() {
        List<Integer> omsIds = new ArrayList<Integer>(omsTask.size());
        for (Map.Entry<Integer, TraceStatus> next : omsTask.entrySet()) {
            if (next.getValue() == TraceStatus.NORMAL) {
                omsIds.add(next.getKey());
            }
        }
        return omsIds;
    }

    /**
     * Gets condition key.
     *
     * @return the condition key
     */
    protected String getConditionKey() {
        return conditionKey;
    }

    /**
     * Gets mtserver trace id.
     *
     * @return the mtserver trace id
     */
    protected int getMtserverTraceId() {
        return mtservTraceId;
    }

    @Override
    public String toString() {
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("mtserverTraceId:");
        stringBuffer.append(mtservTraceId);
        stringBuffer.append(", conditionKey");
        stringBuffer.append(conditionKey);
        stringBuffer.append(", oms TraceId:");
        stringBuffer.append(omsTask.values().toString());
        return stringBuffer.toString();
    }
}
