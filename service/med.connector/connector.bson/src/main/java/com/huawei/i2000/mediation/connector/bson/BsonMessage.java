/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.mediation.connector.bson;

import com.huawei.i2000.mediation.connector.bson.bcm.BcmMessage;
import com.huawei.i2000.mediation.sdk.CompositVar;
import com.huawei.i2000.mediation.sdk.DataUnit;
import com.huawei.i2000.mediation.sdk.NVPairVar;
import com.huawei.i2000.mediation.sdk.Report;
import com.huawei.i2000.mediation.sdk.Request;
import com.huawei.i2000.mediation.sdk.Response;
import com.huawei.i2000.mediation.sdk.SimpleVar;

import org.apache.commons.lang3.ArrayUtils;

import java.util.List;
import java.util.Locale;

/**
 * 便于上层处理的BSON消息类
 *
 * <AUTHOR>
 * @since V800R001C30
 */
public class BsonMessage extends BcmMessage {

    /**
     * commandId定义
     *
     * <AUTHOR>
     */
    public static enum CommandId {

        /**
         * HeartBeatReq
         */
        HEART_BEAT_REQ(0x0000FFFF),

        /**
         * HeartBeatRsp
         */
        HEART_BEAT_RSP(0x8000FFFF),

        /**
         * QueryStaticNodeDataReq
         */
        QUERY_STATIC_NODE_DATA_REQ(0x00010001),

        /**
         * QueryStaticNodeDataRsp
         */
        QUERY_STATIC_NODE_DATA_RSP(0x80010001),

        /**
         * QueryTopoReq
         */
        QUERY_TOPO_REQ(0x00010001),

        /**
         * QueryTopoRsp
         */
        QUERY_TOPO_RSP(0x80010001),

        /**
         * QueryDynNodeDataReq
         */
        QUERY_DYN_NODE_DATA_REQ(0x00010002),

        /**
         * QueryDynNodeDataRsp
         */
        QUERY_DYN_NODE_DATA_RSP(0x80010002),

        /**
         * RptDynNodeData
         */
        RPT_DYN_NODE_DATA(0x60010003),

        /**
         * RptEvent
         */
        RPT_EVENT(0x40010004),

        /**
         * SyncEventDataReq
         */
        SYNC_EVENT_DATA_REQ(0x00010005),

        /**
         * SyncEventDataRsp
         */
        SYNC_EVENT_DATA_RSP(0x80010005),

        /**
         * DelEventDataReq
         */
        DEL_EVENT_DATA_REQ(0x00010006),

        /**
         * DelEventDataRsp
         */
        DEL_EVENT_DATA_RSP(0x80010006),

        /**
         * QueryPerfDataReq
         */
        QUERY_PERF_DATA_REQ(0x00010007),

        /**
         * QueryPerfDataRsp
         */
        QUERY_PERF_DATA_RSP(0x80010007),

        /**
         * ACTIVE_ALARM_LIST_REQ
         */
        ACTIVE_ALARM_LIST_REQ(0x00010005),
        /**
         * ACTIVE_ALARM_LIST_RSP
         */
        ACTIVE_ALARM_LIST_RSP(0x80010005),
        /**
         * SysConfigReq
         */
        SYS_CONFIG_REQ(0x00010008),

        /**
         * SysConfigRsp
         */
        SYS_CONFIG_RSP(0x80010008),

        /**
         * SynMonitorItemReq
         */
        SYN_MONITOR_ITEM_REQ(0x00010009),

        /**
         * SynMonitorItemRsp
         */
        SYN_MONITOR_ITEM_RSP(0x80010009),

        /**
         * GetSaltReq
         */
        GET_SALT_REQ(0x0001000a),

        /**
         * GetSaltRsp
         */
        GET_SALT_RSP(0x8001000a),
        /**
         * FaultDetectTaskQryReq
         */
        FAULT_DETECT_TASK_QRY_REQ(0x0001000f),

        /**
         * FaultDetectTaskQryRsp
         */
        FAULT_DETECT_TASK_QRY_RSP(0x8001000f),

        /**
         * FaultDetectManageReq
         */
        FAULT_DETECT_MANAGE_REQ(0x0001000b),

        /**
         * FaultDetectManageRsp
         */
        FAULT_DETECT_MANAGE_RSP(0x8001000b),

        /**
         * MeasureUnitQryReq
         */
        MEASURE_UNIT_QRY_REQ(0x00010010),
        /**
         * MeasureUnitQryRsp
         */
        MEASURE_UNIT_QRY_RSP(0x80010010),
        /**
         * MeasureUnitConfigReq
         */
        MEASURE_UNIT_CONFIG_REQ(0x0001000c),
        /**
         * MeasureUnitConfigRsp
         */
        MEASURE_UNIT_CONFIG_RSP(0x8001000c),

        /**
         * CollectTaskManageReq
         */
        COLLECT_TASK_MANAGE_REQ(0x0001000d),

        /**
         * CollectTaskManageRsp
         */
        COLLECT_TASK_MANAGE_RSP(0x8001000d),

        /**
         * QueryMeasObjReq
         */
        QUERY_MEASOBJ_REQ(0x00010007),

        /**
         * QueryMeasObjRsp
         */
        QUERY_MEASOBJ_RSP(0x80010007),

        /**
         * PmDataRpt
         */
        PM_DATA_RPT(0x6001000e),

        /**
         * BCMAgent上报的topo信息
         */
        SEND_TOPO(0x60010003);

        /**
         * commandId的值
         */
        private int value;

        private CommandId(int value) {
            setValue(value);
        }

        /**
         * Gets value.
         *
         * @return the value
         */
        public final int getValue() {
            return value;
        }

        /**
         * Sets value.
         *
         * @param value the value
         */
        final void setValue(int value) {
            this.value = value;
        }
    }

    /**
     * 字段下标
     *
     * <AUTHOR>
     */
    private static enum FieldInfo {

        /**
         * commandId
         */
        COMMAND_ID("commandId"),

        /**
         * sequenceId
         */
        SEQUENCE_ID("sequenceId"),

        /**
         * securityId
         */
        SECURITY_ID("securityId"),

        /**
         * timestamp
         */
        TIMESTAMP("timestamp"),

        /**
         * msgBody
         */
        MSG_BODY("msgBody");

        /**
         * 字段名
         */
        private String fieldName;

        private FieldInfo(String fieldName) {
            setFieldName(fieldName);
        }

        /**
         * Sets field name.
         *
         * @param fieldName the field name
         */
        public final void setFieldName(String fieldName) {
            this.fieldName = fieldName;
        }

        /**
         * Gets field name.
         *
         * @return the field name
         */
        public final String getFieldName() {
            return fieldName;
        }
    }

    /**
     * 消息体
     */
    private Object body;

    /**
     * Request、Response、Report对象
     */
    private DataUnit dataUnit;

    /**
     * 对端地址
     */
    private String peerAddress;

    /**
     * 对端端口号
     */
    private Integer peerPort;

    /**
     * BSON消息转换器
     */
    private static BsonMessageConvertor convertor = new BsonMessageConvertor();

    /**
     * 默认构造函数.
     */
    public BsonMessage() {
    }

    /**
     * 根据commandId和消息体构造
     *
     * @param commandId commandId
     * @param msgBody 消息体
     */
    public BsonMessage(int commandId, Object msgBody) {
        setCommandId(commandId);
        setBody(msgBody);
    }

    /**
     * 从BSON码流构造
     *
     * @param bytes BSON码流构造
     * @param decodeMsgBody 是否解码消息体
     * @param peerAddress ip
     * @param peerPort port
     */
    public BsonMessage(byte[] bytes, boolean decodeMsgBody, String peerAddress, int peerPort) {
        BcmMessage message = convertor.fromBytes(BcmMessage.class, bytes);
        from(message, decodeMsgBody);
        setPeerAddress(peerAddress);
        setPeerPort(peerPort);
    }

    /**
     * 拷贝函数
     *
     * @return BsonMessage bson message
     */
    public BsonMessage copyBsonMessage() {
        BsonMessage bsonMessage = new BsonMessage();
        bsonMessage.setCommandId(this.getCommandId());
        bsonMessage.setMsgBody(this.getMsgBody());
        bsonMessage.setPkgId(this.getPkgId());
        bsonMessage.setPkgNum(this.getPkgNum());
        bsonMessage.setRetry(this.getRetry());
        bsonMessage.setSecurityId(this.getSecurityId());
        bsonMessage.setSequenceId(this.getSequenceId());
        bsonMessage.setTimestamp(this.getTimestamp());
        bsonMessage.setMsgId(this.getMsgId());
        bsonMessage.body = body;
        bsonMessage.dataUnit = dataUnit;
        bsonMessage.peerAddress = peerAddress;
        bsonMessage.peerPort = peerPort;
        return bsonMessage;
    }

    /**
     * 从BcmMessage对象拷贝
     *
     * @param message 信息
     * @param decodeMsgBody 是否解码
     */
    public void from(BcmMessage message, boolean decodeMsgBody) {
        if (message == null) {
            return;
        }

        // QDI重构，采用内部静态类的转换方法实现
        BsonMessage2BcmMessage.toBsonMessage(message, decodeMsgBody, this);

        // body
        body = null;

        if (!decodeMsgBody) {
            return;
        }
        byte[] msgBody = message.getMsgBody();
        if (msgBody == null) {
            return;
        }

        // 取commandId对应的消息类
        Class<?> cls = BsonMessageMapper.getInstance().getClassByCommandId(message.getCommandId());
        if (cls == null) {
            return;
        }
        // 将消息体码流转换为对象
        body = convertor.fromBytes(cls, message.getMsgBody());
    }

    /**
     * 从Request对象构造
     *
     * @param request Request对象
     * @param decodeMsgBody 是否解码消息体
     */
    public BsonMessage(Request request, boolean decodeMsgBody) {
        setPeerAddress(request.getTarget().getAddress());
        setPeerPort(Integer.parseInt(request.getTarget().getParam("port")));

        List<Object> resultList = request.getParams();
        setByList(resultList, decodeMsgBody);
        setDataUnit(request);

    }

    /**
     * 从Response对象构造
     *
     * @param response Response对象
     */
    public BsonMessage(Response response) {

        setPeerAddress(response.getPeerAddress());
        setPeerPort(response.getPeerPort());

        // 如果是异常响应，则不获取各个字段
        if (response.isExceptional()) {
            setDataUnit(response);
        }

        List<Object> resultList = response.getResults();
        setByList(resultList, true);
        setDataUnit(response);
    }

    /**
     * 从Report对象构造
     *
     * @param report Report对象
     */
    public BsonMessage(Report report) {
        setPeerAddress(report.getSrcAddress());
        setPeerPort(report.getSrcPort());

        List<Object> resultList = report.getReportBody();
        setByList(resultList, true);
        setDataUnit(report);

    }

    /**
     * 从Request、Response、Report的params、results、reportBody构造
     *
     * @param list Response和Report的results和reportBody构造
     * @param decodeMsgBody 是否解码消息体
     */
    private void setByList(List<Object> list, boolean decodeMsgBody) {
        // commandId
        NVPairVar commandIdVar = (NVPairVar) list.get(FieldInfo.COMMAND_ID.ordinal());
        if (commandIdVar != null) {
            setCommandId(Integer.parseInt(commandIdVar.getValue()));
        }

        // sequenceId
        NVPairVar sequenceIdVar = (NVPairVar) list.get(FieldInfo.SEQUENCE_ID.ordinal());
        if (sequenceIdVar != null) {
            setSequenceId(Long.parseLong(sequenceIdVar.getValue()));
        }

        // securityId
        NVPairVar securityIdVar = (NVPairVar) list.get(FieldInfo.SECURITY_ID.ordinal());
        setSecurityId(securityIdVar.getValue());

        // timestamp
        NVPairVar timestampVar = (NVPairVar) list.get(FieldInfo.TIMESTAMP.ordinal());
        setTimestamp(Long.parseLong(timestampVar.getValue()));

        // msgBody
        CompositVar msgBodyVar = (CompositVar) list.get(FieldInfo.MSG_BODY.ordinal());
        setMsgBody((byte[]) msgBodyVar.getExtandData());

        // body
        body = null;

        if (!decodeMsgBody) {
            return;
        }

        if (getMsgBody() == null) {
            return;
        }

        // 取commandId对应的消息类
        Class<?> cls = BsonMessageMapper.getInstance().getClassByCommandId(getCommandId());

        // 将消息体码流转换为对象
        if (cls == null) {
            return;
        }

        body = convertor.fromBytes(cls, getMsgBody());
    }

    /**
     * 填充Request对象
     *
     * @param request Request对象
     */
    public void fillRequest(Request request) {

        if (peerAddress != null) {
            request.getTarget().setAddress(peerAddress);
        }
        if (peerPort != null) {
            request.getTarget().addParam("port", peerPort.toString());
        }
        fillList(request.getParams());
        setDataUnit(request);

    }

    /**
     * 填充Response对象
     *
     * @param response Response对象
     */
    public void fillResponse(Response response) {
        if (peerAddress != null) {
            response.setPeerAddress(peerAddress);
        }
        if (peerPort != null) {
            response.setPeerPort(peerPort.intValue());
        }
        fillList(response.getResults());
        setDataUnit(response);

    }

    /**
     * 填充Report对象
     *
     * @param report Report对象
     */
    public void fillReport(Report report) {
        if (peerAddress != null) {
            report.setSrcAddress(peerAddress);
        }
        if (peerPort != null) {
            report.setSrcPort(peerPort.intValue());
        }
        report.setProtocolType(BsonConsts.PROTOCOL_TYPE);
        report.setTimestamp(getTimestamp());
        fillList(report.getReportBody());
        setDataUnit(report);

    }

    /**
     * 设置Request、Response、Report对象
     *
     * @param list Response和Report的results和reportBody构造
     */
    public void fillList(List<Object> list) {
        list.clear();

        // commandId
        list.add(new NVPairVar(FieldInfo.COMMAND_ID.getFieldName(), "Integer", Integer.toString(getCommandId())));

        // sequenceId
        list.add(new NVPairVar(FieldInfo.SEQUENCE_ID.getFieldName(), "Long", Long.toString(getSequenceId())));

        // securityId
        list.add(new NVPairVar(FieldInfo.SECURITY_ID.getFieldName(), "String", getSecurityId()));

        // timestamp
        list.add(new NVPairVar(FieldInfo.TIMESTAMP.getFieldName(), "Long", Long.toString(getTimestamp())));

        // msgBody
        list.add(new CompositVar(new SimpleVar(FieldInfo.MSG_BODY.getFieldName()), getMsgBody()));
    }

    /**
     * 设置消息体
     *
     * @param body 消息体
     */
    public void setBody(Object body) {
        this.body = body;
        setMsgBody((body == null) ? null : convertor.toBytes(body));
    }

    /**
     * Gets body.
     *
     * @return the body
     */
    public Object getBody() {
        return body;
    }

    /**
     * Sets data unit.
     *
     * @param dataUnit the data unit
     */
    public void setDataUnit(DataUnit dataUnit) {
        this.dataUnit = dataUnit;
    }

    /**
     * Gets data unit.
     *
     * @return the data unit
     */
    public DataUnit getDataUnit() {
        return dataUnit;
    }

    /**
     * 获取编码后的BSON消息
     *
     * @return 编码后的BSON消息 byte [ ]
     */
    public byte[] toBytes() {
        // QDI重构，采用内部静态类的转换方法实现
        BcmMessage message = BsonMessage2BcmMessage.toBcmMessage(this);
        return convertor.toBytes(message);
    }

    @Override
    public void setCommandId(int commandId) {
        super.setCommandId(commandId);

        NVPairVar pair = (NVPairVar) getDataUnitObject(FieldInfo.COMMAND_ID.ordinal());
        if (pair != null) {
            pair.setValue(Integer.toString(getCommandId()));
        }
    }

    @Override
    public void setMsgBody(byte[] msgBody) {
        super.setMsgBody(msgBody);

        CompositVar composit = (CompositVar) getDataUnitObject(FieldInfo.MSG_BODY.ordinal());
        if (composit != null) {
            composit.setExtandData(getMsgBody());
        }
    }

    @Override
    public void setSecurityId(String securityId) {
        super.setSecurityId(securityId);

        NVPairVar pair = (NVPairVar) getDataUnitObject(FieldInfo.SECURITY_ID.ordinal());
        if (pair != null) {
            pair.setValue(securityId);
        }

    }

    @Override
    public void setSequenceId(long sequenceId) {
        super.setSequenceId(sequenceId);

        NVPairVar pair = (NVPairVar) getDataUnitObject(FieldInfo.SEQUENCE_ID.ordinal());
        if (pair != null) {
            pair.setValue(Long.toString(getSequenceId()));
        }
    }

    @Override
    public void setTimestamp(long timestamp) {
        super.setTimestamp(timestamp);

        NVPairVar pair = (NVPairVar) getDataUnitObject(FieldInfo.TIMESTAMP.ordinal());
        if (pair != null) {
            pair.setValue(Long.toString(getTimestamp()));
        }
    }

    /**
     * 获取Request、Response、Response指定下标的参数
     *
     * @param index 下标
     * @return 参数
     */
    private Object getDataUnitObject(int index) {

        if (dataUnit == null) {
            return null;
        }

        if (dataUnit instanceof Request) {
            return ((Request) dataUnit).getParams().get(index);
        }

        if (dataUnit instanceof Response) {
            return ((Response) dataUnit).getResults().get(index);
        }

        if (dataUnit instanceof Report) {
            return ((Report) dataUnit).getReportBody().get(index);
        }

        return null;
    }

    /**
     * 设置对端地址
     *
     * @param peerAddress 对端地址
     */
    public void setPeerAddress(String peerAddress) {
        this.peerAddress = peerAddress;
        if (dataUnit != null) {
            if (dataUnit instanceof Response) {
                ((Response) dataUnit).setPeerAddress(peerAddress);
            } else if (dataUnit instanceof Report) {
                ((Report) dataUnit).setSrcAddress(peerAddress);
            }
        }
    }

    /**
     * Gets peer address.
     *
     * @return the peer address
     */
    public String getPeerAddress() {
        return peerAddress;
    }

    /**
     * 设置对端端口号
     *
     * @param peerPort 对端端口号
     */
    public void setPeerPort(Integer peerPort) {
        this.peerPort = peerPort;
        if (dataUnit != null) {
            if (dataUnit instanceof Response) {
                ((Response) dataUnit).setPeerPort(peerPort.intValue());
            } else if (dataUnit instanceof Report) {
                ((Report) dataUnit).setSrcPort(peerPort.intValue());
            }
        }
    }

    /**
     * Gets peer port.
     *
     * @return the peer port
     */
    public Integer getPeerPort() {
        return peerPort;
    }

    /**
     * 是否异常响应
     *
     * @return 是否异常响应 boolean
     */
    public boolean isExceptional() {
        if (!(dataUnit instanceof Response)) {
            return false;
        }

        return ((Response) dataUnit).isExceptional();
    }

    @Override
    public String toString() {
        StringBuilder builder = new StringBuilder();
        builder.append("BsonMessage [commandId=0x");
        builder.append(String.format(Locale.ROOT, "%08X", getCommandId()));
        builder.append(", resultCode=");
        builder.append(getResultCode());
        builder.append(", sequenceId=");
        builder.append(getSequenceId());
        builder.append(", securityId=");
        builder.append(getSecurityId());
        builder.append(", timestamp=");
        builder.append(getTimestamp());
        builder.append(", pkgId=");
        builder.append(getPkgId());
        builder.append(", pkgNum=");
        builder.append(getPkgNum());
        builder.append(", msgId=");
        builder.append(getMsgId());
        builder.append(", retry=");
        builder.append(getRetry());
        builder.append(", msgBody=");
        builder.append(ArrayUtils.toString(getMsgBody()));
        builder.append(", body=");
        builder.append(body);
        builder.append(", dataUnit=");
        builder.append(dataUnit);
        builder.append(", peerAddress=");
        builder.append(peerAddress);
        builder.append(", peerPort=");
        builder.append(peerPort);
        builder.append(']');
        return builder.toString();
    }

    /**
     * QDI重构，采用内部类实现消息转换 一句话功能简述 描述：
     * <p>
     * 详细说明第一行<br>
     * 详细说明第二行<br>
     *
     * <AUTHOR>
     * @version 1.0, 2014-5-13
     * @since I2000 V500R001C01
     */
    public static class BsonMessage2BcmMessage {
        private BsonMessage2BcmMessage() {
        }

        /**
         * To bcm message bcm message.
         *
         * @param bsonMsg the bson msg
         * @return the bcm message
         */
        public static BcmMessage toBcmMessage(BsonMessage bsonMsg) {
            BcmMessage message = new BcmMessage();
            message.setCommandId(bsonMsg.getCommandId());
            message.setSequenceId(bsonMsg.getSequenceId());
            message.setSecurityId(bsonMsg.getSecurityId());
            message.setTimestamp(bsonMsg.getTimestamp());
            message.setMsgId(bsonMsg.getMsgId());
            message.setPkgId(bsonMsg.getPkgId());
            message.setPkgNum(bsonMsg.getPkgNum());
            message.setRetry(bsonMsg.getRetry());
            message.setMsgBody(bsonMsg.getMsgBody());
            return message;
        }

        /**
         * To bson message bson message.
         *
         * @param message the message
         * @param decodeMsgBody the decode msg body
         * @param bsonMsg the bson msg
         * @return the bson message
         */
        public static BsonMessage toBsonMessage(BcmMessage message, boolean decodeMsgBody, BsonMessage bsonMsg) {
            if (null == bsonMsg) {
                return null;
            }
            bsonMsg.setCommandId(message.getCommandId());
            bsonMsg.setSequenceId(message.getSequenceId());
            bsonMsg.setSecurityId(message.getSecurityId());
            bsonMsg.setTimestamp(message.getTimestamp());
            bsonMsg.setPkgId(message.getPkgId());
            bsonMsg.setPkgNum(message.getPkgNum());
            bsonMsg.setRetry(message.getRetry());
            bsonMsg.setMsgBody(message.getMsgBody());
            bsonMsg.setResultCode(message.getResultCode());
            bsonMsg.setMsgId(message.getMsgId());
            return bsonMsg;
        }
    }
}
