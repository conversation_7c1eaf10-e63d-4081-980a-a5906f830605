/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.mediation.connector.common.util.security;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.cbb.cert.Cert;
import com.huawei.i2000.cbb.cert.ServiceCerts;
import com.huawei.i2000.cbb.cert.SopCertResult;
import com.huawei.i2000.mediation.connector.common.util.security.certmgr.CertMgrUtils;
import com.huawei.i2000.mediation.sdk.config.MedConfigUtil;
import com.huawei.oms.util.OmsConstant;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.security.GeneralSecurityException;
import java.security.KeyStore;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;
import java.util.stream.Stream;

/**
 * SMSF SSL工具类
 *
 * <AUTHOR>
 * @since 2022/4/11
 */
public class SmsfSslUtils extends SSLUtils {
    private static final Logger LOGGER = LoggerFactory.getLogger(SmsfSslUtils.class);

    /**
     * 信任证书文件格式
     */
    @Getter
    private static final String CERT_FILE_TYPE = ".cer,.pem,.crt";

    private static final String CER_FILE_SUFFIX = ".cer";

    private static final String DEFAULT_SSL_PROTOCOL = "TLSv1.2";

    private static final String CRL_FILENAME = "Root.crl";

    private static final String SMSF_SUBFOLDER_NAME = "oms.smsf";

    private static final String UCN_SUBFOLDER_NAME = "oms.ucn";

    /**
     * smsf证书服务id
     */
    private static final String SMSF_CERT_SERVICE_ID = "720dccdd-0836-49d8-888c-9b07643b3104";

    /**
     * ucn证书服务id
     */
    private static final String UCN_CERT_SERVICE_ID = "d3077741-59d3-4442-9a20-814b228de694";

    private static final String ETC_PATH = System.getProperty(OmsConstant.OMS_PATH_ETC, MedConfigUtil.getMedConfigPath());

    private static final String SMSF_CERT_PATH = ETC_PATH + File.separator + SMSF_SUBFOLDER_NAME;

    private static final String UCN_CERT_PATH = ETC_PATH + File.separator + UCN_SUBFOLDER_NAME;

    private static final String SMSF_TRUSTALL_KEY = "smsf.secure.trustAnyone";

    private static final String UCN_TRUSTALL_KEY = "ucn.secure.trustAnyone";

    /**
     * 生成SSL上下文
     *
     * @return SSL上下文
     * @throws GeneralSecurityException 安全异常
     */
    public static SSLContext getSSLContext() throws GeneralSecurityException {
        LOGGER.info("Begin to load certification.");
        KeyStore keyStore = null;
        try {
            keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null);
        } catch (GeneralSecurityException | IOException e) {
            LOGGER.error("init keyStore failed.", e);
        }
        // 加载smsf证书
        if (folderExists(SMSF_CERT_PATH)) {
            loadCert2Local(SMSF_CERT_SERVICE_ID, SMSF_SUBFOLDER_NAME);
            getContext(keyStore, SMSF_SUBFOLDER_NAME);
        }
        // 加载ucn证书
        if (folderExists(UCN_CERT_PATH)) {
            loadCert2Local(UCN_CERT_SERVICE_ID, UCN_SUBFOLDER_NAME);
            getContext(keyStore, UCN_SUBFOLDER_NAME);
        }
        // smsf证书
        String crlPathSmsf = SMSF_CERT_PATH + File.separator + CRL_FILENAME;
        TrustManager[] smsfTrustManagers = getTrustManagersServer(keyStore, crlPathSmsf);

        // ucn证书
        String crlPathUcn = UCN_CERT_PATH + File.separator + CRL_FILENAME;
        TrustManager[] ucnTrustManagers = getTrustManagersServer(keyStore, crlPathUcn);

        // 合并证书
        TrustManager[] trustManagersAll = Stream.concat(Arrays.stream(smsfTrustManagers),
                Arrays.stream(ucnTrustManagers)).toArray(TrustManager[]::new);
        if (isTrustAllOpened(SMSF_TRUSTALL_KEY) || isTrustAllOpened(UCN_TRUSTALL_KEY)) {
            trustManagersAll = null;
        }
        LOGGER.info("end to load certification.");
        return getSSLContext(null, trustManagersAll, DEFAULT_SSL_PROTOCOL);
    }

    private static void getContext(KeyStore keyStore, String subFolderName) {
        loadCert(System.getProperty(OmsConstant.OMS_PATH_ETC, MedConfigUtil.getMedConfigPath()) + File.separator
                + subFolderName, keyStore, CERT_FILE_TYPE);
    }

    /**
     * 生成SSL上下文
     *
     * @param protocolType protocolType
     * @param checkCert    是否校验证书
     * @return SSL上下文
     * @throws GeneralSecurityException 安全异常
     */
    public static SSLContext getSSLContext(CSPProtocolType protocolType, boolean checkCert) throws GeneralSecurityException {
        LOGGER.info("Begin to get ssl context.");
        KeyStore keyStore = null;
        try {
            keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
            keyStore.load(null);
        } catch (GeneralSecurityException | IOException e) {
            LOGGER.error("init keyStore failed.", e);
        }
        if (CSPProtocolType.SMSF.equals(protocolType)) {
            return getSSLContext(keyStore, SMSF_CERT_SERVICE_ID, SMSF_CERT_PATH, SMSF_SUBFOLDER_NAME, checkCert, isTrustAllOpened(SMSF_TRUSTALL_KEY));
        }
        return getSSLContext(keyStore, UCN_CERT_SERVICE_ID, UCN_CERT_PATH, UCN_SUBFOLDER_NAME, checkCert, isTrustAllOpened(UCN_TRUSTALL_KEY));
    }

    private static SSLContext getSSLContext(KeyStore keyStore, String certServiceId, String certPath, String subfolderName, boolean checkCert, boolean trustAll) throws GeneralSecurityException {
        // 加载证书到本地
        if (folderExists(certPath)) {
            loadCert2Local(certServiceId, subfolderName);
            getContext(keyStore, subfolderName);
        }
        String crlPath = certPath + File.separator + CRL_FILENAME;
        TrustManager[] trustManagers = getTrustManagersServer(keyStore, crlPath);
        if (!checkCert || trustAll) {
            trustManagers = null;
        }
        LOGGER.info("end to get ssl context.");
        return getSSLContext(null, trustManagers, DEFAULT_SSL_PROTOCOL);
    }

    /**
     * 判断目录是否存
     *
     * @param folderPath 目录
     * @return 结果
     */
    private static boolean folderExists(String folderPath) {
        File folder = new File(folderPath);
        if (folder.exists() && folder.isDirectory()) {
            LOGGER.info("Folder exists");
            return true;
        } else {
            LOGGER.error("Folder does not exist");
        }
        return false;
    }

    /**
     * 获取KeyStore
     *
     * @param keyStore 证书库
     * @param keyStorePath 证书库路径
     * @param caCertTypes 支持文件类型
     * @return KeyStore 证书库
     */
    public static KeyStore getKeyStore(KeyStore keyStore, String keyStorePath, final String caCertTypes) {
        // 加载证书库
        loadCert(keyStorePath, keyStore, caCertTypes);
        return keyStore;
    }

    /**
     * 从证书管理服务加载证书到本地
     *
     * @param folderName 产品目录
     * @param serviceId 服务id
     */
    private static void loadCert2Local(String serviceId, String folderName) {
        try {
            SopCertResult certResult = CertMgrUtils.queryCertByServiceId(serviceId);
            ServiceCerts serviceCerts = certResult.getData();
            List<Cert> certs = serviceCerts.getCerts();
            clearDisk(folderName);
            for (Cert cert : certs) {
                storeCert(cert.getContent(), cert.getCertId(), folderName);
            }
        } catch (ServiceException e) {
            LOGGER.error("[loadCert2Local] failed to query service certs from cert manager.");
        }
    }

    /**
     * 证书落盘
     *
     * @param certContent 证书内容
     * @param certId 证书名称
     * @param folderName 目录
     */
    private static void storeCert(String certContent, String certId, String folderName) {
        if (StringUtils.isEmpty(certContent)) {
            LOGGER.debug("[storeCert] no cert content has been offer.");
            return;
        }

        try (FileOutputStream os = new FileOutputStream(
                System.getProperty(OmsConstant.OMS_PATH_ETC, MedConfigUtil.getMedConfigPath()) + File.separator + folderName
                        + File.separator + certId + CER_FILE_SUFFIX)) {
            os.write(Base64.getDecoder().decode(certContent));
        } catch (IOException e) {
            LOGGER.debug("[storeCert] write cert to disk failed.");
        }
    }

    private static void clearDisk(String folderName) {
        File filePath = new File(
                System.getProperty(OmsConstant.OMS_PATH_ETC, MedConfigUtil.getMedConfigPath()) + File.separator
                        + folderName);
        if (!filePath.isDirectory()) {
            LOGGER.warn("file path:{} not exist", filePath.getName());
            return;
        }
        File[] cers = filePath.listFiles((dir, name) -> name.endsWith("cer"));
        if (cers == null) {
            return;
        }
        for (File cer : cers) {
            if (cer.delete()) {
                LOGGER.debug("cer:{} delete success", cer.getName());
            } else {
                LOGGER.debug("cer:{} delete failed", cer.getName());
            }
        }
    }
}
