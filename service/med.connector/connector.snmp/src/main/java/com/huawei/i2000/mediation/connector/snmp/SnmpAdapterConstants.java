/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.mediation.connector.snmp;

import com.huawei.i2000.mediation.sdk.config.MedConfigUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.snmp4j.security.SecurityLevel;

/**
 * SnmpAdapter的常量定义
 *
 * <AUTHOR>
 * @version 1.0, 2008-4-29
 * @since V800R001C30
 */
public class SnmpAdapterConstants {
    /**
     * SNMP协议
     */
    public static final String PROTOCOL_TYPE_SNMP = "SNMP";

    /**
     * SNMPv1协议
     */
    public static final String SNMP_V1 = "SNMPv1";

    /**
     * SNMPv2c协议
     */
    public static final String SNMP_V2C = "SNMPv2c";

    /**
     * SNMPv3协议
     */
    public static final String SNMP_V3 = "SNMPv3";

    /**
     * SNMP Get操作
     */
    public static final String GET = "snmpget";

    /**
     * SNMP GetNext操作
     */
    public static final String GETNEXT = "snmpgetnext";

    /**
     * SNMP Set操作
     */
    public static final String SET = "snmpset";

    /**
     * SNMP Walk操作
     */
    public static final String WALK = "snmpwalk";

    /**
     * SNMP GetTable操作
     */
    public static final String GETTABLE = "snmpgettable";

    /**
     * SNMP RemoveEngineID操作，删除snmp4j缓存的EngineID以及对应的安全信息和时钟
     */
    public static final String REMOVEENGINEID = "snmpremoveengineid";

    /**
     * SNMP代理的端口
     */
    public static final String PORT = "MainPort";

    /**
     * SNMPv1v2c版本的公用区名
     */
    public static final String COMMUNITY = "Community";

    /**
     * SNMPv3版本的安全用户名
     */
    public static final String SECURITYNAME = "SecurityName";

    /**
     * 删除SNMPv3用户标志位
     */
    public static final String IS_REMOVE_USER = "IsRemoveSNMPv3User";

    /**
     * SNMPv3版本的安全级别
     */
    public static final String SECURITYLEVEL = "SecurityLevel";

    /**
     * SNMPv3版本的安全级别——无鉴别协议也无加密协议
     */
    public static final String NOAUTH_NOPRIV = SecurityLevel.NOAUTH_NOPRIV + "";

    /**
     * SNMPv3版本的安全级别——有鉴别协议无加密协议
     */
    public static final String AUTH_NOPRIV = SecurityLevel.AUTH_NOPRIV + "";

    /**
     * SNMPv3版本的安全级别——有鉴别协议也有加密协议
     */
    public static final String AUTH_PRIV = SecurityLevel.AUTH_PRIV + "";

    /**
     * SNMPv3版本的上下文名
     */
    public static final String CONTEXTNAME = "ContextName";

    /**
     * SNMPv3版本的上下文引擎ID，即要访问的真实SNMP实体的引擎ID
     */
    public static final String CONTEXTENGINEID = "ContextEngineID";

    /**
     * SNMPv3版本的鉴别协议
     */
    public static final String AUTHPROTOCOL = "AuthProtocol";

    /**
     * SNMPv3版本的鉴别协议密钥
     */
    public static final String AUTHPRIVALUE = "AuthPassword";

    /**
     * SNMPv3版本的鉴别协议——无鉴别协议
     */
    public static final String NOAUTH = "1";

    /**
     * SNMPv3版本的鉴别协议
     */
    public static final String HMACMD5 = "2";

    /**
     * SNMPv3版本的鉴别协议——HMACSHA
     */
    public static final String HMACSHA = "3";

    /**
     * SNMPv3版本的鉴别协议——SHA2_256
     */
    public static final String SHA2_256 = "4";

    /**
     * SNMPv3版本的鉴别协议——SHA2_384
     */
    public static final String SHA2_384 = "5";

    /**
     * SNMPv3版本的鉴别协议——SHA2_512
     */
    public static final String SHA2_512 = "6";

    /**
     * SNMPv3版本的加密协议
     */
    public static final String PRIVPROTOCOL = "PrivProtocol";

    /**
     * SNMPv3版本的加密协议密钥
     */
    public static final String PRIVPWD = "PrivPassword";

    /**
     * SNMPv3版本的加密协议——无加密协议
     */
    public static final String NOPRIV = "1";

    /**
     * SNMPv3版本的加密协议——CBCDES
     */
    public static final String CBCDES = "2";

    /**
     * SNMPv3版本的加密协议——IDEA
     */
    public static final String IDEA = "3";

    /**
     * SNMPv3版本的加密协议——AES_128
     */
    public static final String AES_128 = "4";

    /**
     * SNMPv3版本的加密协议——AES_192
     */
    public static final String AES_192 = "5";

    /**
     * SNMPv3版本的加密协议——AES_256
     */
    public static final String AES_256 = "6";

    /**
     * 版本信息
     */
    public static final String VERSION = "Version";

    /**
     * 使用getTable获取表时返回的行索引
     */
    public static final String TABLE_INDEX = "Instance";

    /**
     * 使用getTable获取表时索引的下限
     */
    public static final String LOWER_BOUND_INDEX = "lowerBoundIndex";

    /**
     * 使用getTable获取表时索引的上限
     */
    public static final String UPPER_BOUND_INDEX = "upperBoundIndex";

    /**
     * getTable、walk操作内部可能使用到getbulk，MAX_REPETITIONS设置getbulk的最大重复次数
     */
    public static final String MAX_REPETITIONS = "maxRepetitions";

    /**
     * getTable、walk操作内部使用的基本操作，不指定时根据协议版本判断，v1使用getNext，其他的使用getBulk
     */
    public static final String INTERNAL_OPERATION = "internalOperation";

    /**
     * SNMP GetBulk操作，仅共getTable、walk操作内部使用
     */
    public static final String GETBULK = "snmpgetbulk";

    /**
     * v1Trap——企业OID
     */
    public static final String V1TRAP_ENTERPRISE = "enterprise";

    /**
     * v1Trap——generic
     */
    public static final String V1TRAP_GENERIC = "generic";

    /**
     * v1Trap——specific
     */
    public static final String V1TRAP_SPECIFIC = "specific";

    /**
     * 我司的企业ID
     */
    public static final int ENTERPRISE_ID = 2011;

    /**
     * SnmpAdapter下发请求的端口
     */
    public static final String SEND_PORT = "sendPort";

    /**
     * SnmpAdapter侦听的端口
     */
    public static final String LISTEN_PORT = "listenPort";

    /**
     * SnmpAdapter消息处理的线程池大小，缺省消息处理不启用线程池，直接使用读取端口数据的线程
     */
    public static final String THREAD_POOL_SIZE = "threadPoolSize";

    /**
     * SnmpAdapter并发的异步请求数
     */
    public static final String ASYNC_REQUEST_COUNT = "asyncRequestCount";

    /**
     * SnmpAdapter异步请求队列的大小
     */
    public static final String ASYNC_QUEUE_SIZE = "asyncQueueSize";

    /**
     * SnmpAdapter异步线程等待时间
     */
    public static final String ASYNC_BLOCK_TIME = "asyncBlockTime";

    /**
     * SNMP请求包最大大小
     */
    public static final String SNMP_PACKET_MAXSIZE = "snmpPacketMaxSize";

    /**
     * SNMP请求包最大大小
     */
    public static final String PRINT_SNMP4J_LOG = "printSnmp4jLog";

    /**
     * 每秒可以下发的请求数量（流控）
     */
    public static final String PER_SECOND_REQUEST_NUM = "perSecReqNum";

    /**
     * 是否接收或发送SNMP V1信息
     */
    public static final String PROCESS_SNMP_V1_INFO = "processSnmpV1Info";

    /**
     * 是否接收或发送SNMP V2信息
     */
    public static final String PROCESS_SNMP_V2_INFO = "processSnmpV2Info";

    /**
     * oms.xml中的SNMP节点
     */
    public static final String CONFIG_SNMP = "snmp";

    /**
     * SNMP RemoveEngineID操作，删除snmp4j缓存的EngineID以及对应的安全信息和时钟
     */
    public static final String CREATEUSM = "CreateUSM";

    /**
     * 在MedCenter timeout之后，定时器主动下发cancel消息到SnmpAdapter取消请求
     */
    public static final String CANCEL_REQUEST = "CancelRequest";

    /**
     * 更新USM的请求
     */
    public static final String UPDATE_USM = "snmpUpdateUSM";

    /**
     * 是否启动检测
     */
    public static final String IS_ATTACK_DETECTION = "isAttackDetection";

    /**
     * 临界值(1-500)
     */
    public static final String ATTACK_THRESHOLD = "AttackThreshold";

    /**
     * 锁定时长 (单位秒),取值范围1-300
     */
    public static final String LOCK_TIME = "LockTime";

    /**
     * 产品名称
     */
    static final byte[] PRODUCT_NAME = new byte[] {'I', '2', '0', '0', '0'};

    private static final Logger logger = LoggerFactory.getLogger(SnmpAdapterConstants.class);

    /**
     * SnmpAdapter下发请求的端口
     */
    static int sendPort = 0;

    /**
     * SnmpAdapter缺省侦听的端口
     */
    static String[] listenPortEntry = null;

    /**
     * SnmpAdapter消息处理的线程池大小，缺省消息处理不启用线程池，直接使用读取端口数据的线程
     */
    static int threadPoolSize = 0;

    /**
     * 缺省的并发异步请求数
     */
    static int asyncRequestCount = 200;

    /**
     * 缺省的异步请求队列大小
     */
    static int asyncQueueSize = 2000;

    /**
     * 缺省的异步线程等待时间（毫秒）
     */
    static int asyncBlockTime = 1000;

    /**
     * 缺省的每秒发送的SNMP请求个数，值为-1表示不做流控
     */
    static int perSecReqNum = 500;

    /**
     * 缺省的SNMP请求包最大大小
     */
    static int snmpPacketMaxSize = 65535;

    /**
     * 缺省的SNMP请求包最大大小
     */
    static boolean printSnmp4jLog = false;

    /**
     * 缺省是否处理SNMP V1信息，按要求，默认为true
     */
    static boolean processSnmpV1Info = true;

    /**
     * 缺省是否处理SNMP V2信息，按要求，默认为true
     */
    static boolean processSnmpV2Info = true;

    /**
     * 是否启动检测
     */
    static boolean isAttackDetection = false;

    /**
     * 临界值(1-500)
     */
    static int attackThreshold = 100;

    /**
     * 锁定时长 (单位秒),取值范围1-300
     */
    static int lockTime = 15;

    /**
     * 从属性集中初始化可配置的常量
     *
     */
    public static void settings() {
        // 获取发送请的端口
        sendPort = MedConfigUtil.getInt("snmp.sendPort", 10162);

        // 获取侦听端口
        final String ports = MedConfigUtil.getString("snmp.listenPort", "10162");
        if ((ports != null) && !ports.isEmpty()) {
            listenPortEntry = ports.trim().split(" *, *");
        }

        // 获取消息处理的线程池大小
        threadPoolSize = MedConfigUtil.getInt("snmp.threadPoolSize", 0);

        // 获取异步请求相关参数
        asyncRequestCount = MedConfigUtil.getInt("snmp.asyncRequestCount", 10);
        asyncQueueSize = MedConfigUtil.getInt("snmp.asyncQueueSize", 5000);
        asyncBlockTime = MedConfigUtil.getInt("snmp.asyncBlockTime", 1000);

        // 获取Snmp包相关参数
        snmpPacketMaxSize = MedConfigUtil.getInt("snmp.snmpPacketMaxSize", 65535);

        // 是否打印snmp4j的日志
        printSnmp4jLog = MedConfigUtil.getBoolean("snmp.printSnmp4jLog", true);
        // 单位时间内允许下发请求的数量值
        perSecReqNum = MedConfigUtil.getInt("snmp.perSecReqNum", 5000);

        setReceiveSNMPTrap();
        setAttackDetection();
    }

    private static void setReceiveSNMPTrap() {
        // 是否处理SNMP V1信息
        processSnmpV1Info = MedConfigUtil.getBoolean("snmp.processSnmpV1Info", true);

        // 是否处理SNMP V2信息
        processSnmpV2Info = MedConfigUtil.getBoolean("snmp.processSnmpV2Info", true);
    }

    private static void setAttackDetection() {
        isAttackDetection = MedConfigUtil.getBoolean("snmp.isAttackDetection", false);

        try {
            attackThreshold = MedConfigUtil.getInt("snmp.AttackThreshold", 200);
            if (attackThreshold < 1 || attackThreshold > 500) {
                attackThreshold = 100;
            }

            lockTime = MedConfigUtil.getInt("snmp.LockTime", 15);
            if (lockTime < 1 || lockTime > 300) {
                lockTime = 15;
            }
        } catch (NumberFormatException e) {
            logger.error("param is invalidate.", e);
        }
    }

    /**
     * 获取临界值
     *
     * @return 临界值
     */
    public static int getAttackThreshold() {
        return attackThreshold;
    }

    /**
     * 获取锁定时长
     *
     * @return 锁定时长
     */
    public static int getLockTime() {
        return lockTime;
    }
}
