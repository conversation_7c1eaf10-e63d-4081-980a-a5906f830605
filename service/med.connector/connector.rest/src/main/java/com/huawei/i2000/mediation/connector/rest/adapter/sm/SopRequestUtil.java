/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.mediation.connector.rest.adapter.sm;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulFactory;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cloudsop.common.tokenhelper.exception.AuthServiceException;
import com.huawei.cloudsop.common.tokenhelper.token.CommonTokenManager;
import com.huawei.cloudsop.common.tokenhelper.token.TokenMapObj;
import com.huawei.cloudsop.common.tokenhelper.util.rpc.RestAdapter;

import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.Response;

/**
 * 请求工具类
 *
 * @since 2020 -11-03
 */
public class SopRequestUtil {
    /**
     * 三方用户登录接口url
     */
    public static final String THIRD_USER_LOGIN = "/rest/plat/smapp/v1/oauth/token";

    private static final Logger LOGGER = LoggerFactory.getLogger(SopRequestUtil.class);

    private static final String TOKEN_HEADER = "accessToken";

    private static final String REAL_ADDR_HEADER = "x-real-client-addr";

    /**
     * 三方用户握手接口url
     */
    private static final String THIRD_HANDSHAKE = "/unisess/v1/tokens";

    /**
     * 三方系统登录接口
     */
    private static final String AUTH_LOGIN_PATH = "/rest/plat/smapp/v1/oauth/token";

    /**
     * 无参构造器
     */
    private SopRequestUtil() {
    }

    /**
     * 调用证书IR接口
     *
     * @param method 请求方法
     * @param url IR url
     * @param rawData 请求参数
     * @return 响应 restful response
     * @throws ServiceException 服务异常
     */
    public static RestfulResponse invokeCertInterface(String method, String url, String rawData)
        throws ServiceException {
        RestfulParametes param = new RestfulParametes();
        param.putHttpContextHeader("content-type", "application/json");
        param.setRawData(rawData);
        RestfulResponse resp;
        if (StringUtils.equalsIgnoreCase(method, "post")) {
            resp = new RestAdapter(RestfulFactory.getRestInstance()).post(url, param);
        } else {
            resp = new RestAdapter(RestfulFactory.getRestInstance()).put(url, param);
        }
        LOGGER.info("response code {}.", resp.getStatus());
        checkResponseCodeIsSuccess(resp);
        return resp;
    }

    /**
     * 检查请求响应是否成功
     *
     * @param response 响应
     * @throws ServiceException 服务异常
     */
    private static void checkResponseCodeIsSuccess(RestfulResponse response) throws ServiceException {
        if (response.getStatus() / 100 != 2) {
            LOGGER.error("Failed to invoke the REST interface. response code: {} response body: {}",
                response.getStatus(), response.getResponseContent());
            throw new ServiceException("Failed to invoke the REST interface.");
        }
    }

    /**
     * 握手接口
     *
     * @param request 上下文
     * @return true 握手成功 false 握手失败
     */
    public static boolean handshake(HttpServletRequest request) {
        return "DV_CERT_DRIVER".equals(getThirdUserName(request));
    }

    /**
     * 解析token，获取南向用户名
     *
     * @param request 上下文
     * @return 南向用户名
     */
    public static String getThirdUserName(HttpServletRequest request) {
        String accessToken = request.getHeader(TOKEN_HEADER);

        try {
            TokenMapObj tokenValue = CommonTokenManager.getInstanse().decryptOMToken(accessToken);

            // token 固定格式，需要按 UCT 时区进行解析
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSS'Z'");
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(tokenValue.getTokenExpireTime(),
                formatter.withZone(ZoneId.of("UTC")));
            Instant instant = zonedDateTime.toInstant();
            Date tokenDate = Date.from(instant);
            Date currentDate = new Date();
            if (currentDate.before(tokenDate)) {
                return tokenValue.getUserName();
            } else {
                LOGGER.error("Invalid Token Current time: {}  token Expire time {} ,token Expire at UTC {}",
                    currentDate.getTime(), tokenDate.getTime(), tokenValue.getTokenExpireTime());
            }
        } catch (AuthServiceException e) {
            LOGGER.error("Get token error ", e);
        }
        return "";
    }

    /**
     * 三方用户登录
     *
     * @param rawDataJson 请求体
     * @return 令牌 string
     * @throws ServiceException 服务异常
     */
    public static String thirdUserLogin(String rawDataJson) throws ServiceException {
        RestfulParametes restfulParametes = new RestfulParametes();
        RestfulResponse response;

        restfulParametes.putHttpContextHeader("Content-Type", "application/json");

        try {
            restfulParametes.setRawData(rawDataJson);
            response = new RestAdapter(RestfulFactory.getRestInstance()).post("/rest/plat/uam/v1/tokens",
                restfulParametes);
        } catch (Exception e) {
            LOGGER.error("third user login error {}", e);
            throw new ServiceException("third user login error.", e);
        }
        if (response == null) {
            LOGGER.error("third user login failed.");
            throw new ServiceException("third user login failed.");
        }
        LOGGER.warn("login response status {}", response.getStatus());

        if (response.getStatus() == Response.Status.BAD_REQUEST.getStatusCode()) {
            throw new ServiceException("user.login.user_or_value_invalid", Response.Status.BAD_REQUEST.getStatusCode());
        }

        if (response.getStatus() != Response.Status.OK.getStatusCode()) {
            LOGGER.error("third user login failed.");
            throw new ServiceException("third user login failed.", response.getStatus());
        }

        JSONObject responseJson = JSONObject.parseObject(response.getResponseContent());

        return responseJson.getString("token");
    }
}
