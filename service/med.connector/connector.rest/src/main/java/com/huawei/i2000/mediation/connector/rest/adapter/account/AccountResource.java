/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.mediation.connector.rest.adapter.account;

import com.huawei.i2000.mediation.sdk.Listener;
import com.huawei.i2000.mediation.sdk.Report;
import com.huawei.i2000.mediation.sdk.connector.consts.RestConnectorConstant;
import com.huawei.i2000.mediation.sdk.exception.CacheOverflowException;
import com.huawei.i2000.mediation.sdk.util.MedLogger;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.PUT;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2021-03-16
 */
@Path("medAccount")
public class AccountResource {
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountResource.class);

    private Listener<Report> reportListener = null;

    /**
     * 设置MED报文处理监听器
     *
     * @param reportListener 监听器
     */
    public void setReportSource(Listener<Report> reportListener) {
        this.reportListener = reportListener;
        LOGGER.info("set listener complete! listener = {}", reportListener);
    }

    /**
     * Do get response.
     *
     * @param restAccountReport the rest account report
     * @param requestContext the request context
     * @return the response
     * @throws CacheOverflowException the cache overflow exception
     */
    @PUT
    @Consumes(MediaType.APPLICATION_JSON)
    public Response doGet(String restAccountReport, @Context HttpServletRequest requestContext)
            throws CacheOverflowException {
        LOGGER.info("Get the RestAccountReport");

        if (StringUtils.isEmpty(restAccountReport)) {
            LOGGER.error("[medAccount doget] the params are invalid !");
            throw new WebApplicationException(Response.Status.BAD_REQUEST);
        }

        if (!handleReport(restAccountReport)) {
            throw new WebApplicationException(Response.Status.INTERNAL_SERVER_ERROR);
        }
        return Response.ok().build();
    }

    /**
     * Do post result message.
     *
     * @param restAccountReport the rest account report
     * @param requestContext the request context
     * @return the result message
     * @throws CacheOverflowException the cache overflow exception
     */
    // 提供uniagent注册账号接口
    @POST
    @Consumes(MediaType.APPLICATION_JSON)
    @Produces(MediaType.APPLICATION_JSON)
    public ResultMessage doPost(String restAccountReport, @Context HttpServletRequest requestContext)
            throws CacheOverflowException {
        LOGGER.info("Get the RestAccountReport from {}", requestContext.getRemoteAddr());

        if (StringUtils.isEmpty(restAccountReport)) {
            LOGGER.error("[medAccount dopost] the params are invalid !");
            throw new WebApplicationException(Response.Status.BAD_REQUEST);
        }

        if (!handleReport(restAccountReport)) {
            throw new WebApplicationException(Response.Status.INTERNAL_SERVER_ERROR);
        }
        return new ResultMessage(200, "Successful");
    }

    private boolean handleReport(String restAccountReport) {
        boolean flag = true;
        Report report = new Report();
        report.setTraceId(MedLogger.getTraceId());
        MedLogger.report(report, MedLogger.PHASE_CONNECTOR, "receive one rest account report");
        LOGGER.info("receive report with traceId: {}", report.getTraceId());
        report.addResult(restAccountReport);
        report.setProtocolType(RestConnectorConstant.ACCOUNT_MED_TYPE);
        try {
            reportListener.handle(report);
        } catch (Exception e) {
            flag = false;
            LOGGER.error("Handle medAccountInfo failed: e = {}", e.getMessage());
        }
        return flag;
    }
}
