/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2017-2021. All rights reserved.
 */

package com.huawei.i2000.mediation.sdk.channel.snmp.util;

import com.huawei.i2000.mediation.sdk.Param;
import com.huawei.i2000.mediation.sdk.Request;
import com.huawei.i2000.mediation.sdk.Target;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Mediation Node定时轮询工具类
 *
 * <AUTHOR> /48422
 * @version 1.0, 2010-11-8
 * @since I2000 V300R002C02
 */
public final class PeriodicPollUtil {
    /**
     * 防止被误构造
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(PeriodicPollUtil.class);

    /**
     * Instantiates a new Periodic poll util.
     */
    private PeriodicPollUtil() {
    }

    /**
     * 构造添加定时任务的请求
     *
     * @param owner 定时任务的owner
     * @param taskId 定时任务的id
     * @param delay 定时任务延迟执行时间
     * @param interval 执行时间间隔
     * @param internalRequest 内部实际执行的请求
     * @param timeout 请求的超时时间
     * @param retries 请求的重试次数
     * @return 构造好的添加定时任务请求 request
     */
    public static Request buildAddTaskRequest(
            final String owner,
            final String taskId,
            final long delay,
            final int interval,
            final Request internalRequest,
            final int timeout,
            final int retries) {
        final Request request =
                buildAddRequest(owner, taskId, delay, interval, internalRequest, timeout, retries, "Start");
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(
                "snmp buildAddTaskRequest request: {},taskid={},timeout={},interval={},delay={},internalRequest:{},retries:{}",
                request,
                taskId,
                timeout,
                interval,
                delay,
                internalRequest,
                retries);
        }
        return request;
    }

    /**
     * 构造修改定时任务的请求
     *
     * @param owner 定时任务的owner
     * @param taskId 定时任务的id
     * @param delay 定时任务延迟执行时间
     * @param interval 执行时间间隔
     * @param internalRequest 内部实际执行的请求
     * @param timeout 请求的超时时间
     * @param retries 请求的重试次数
     * @return 构造好的修改定时任务请求 request
     */
    public static Request buildModifyTaskRequest(
            final String owner,
            final String taskId,
            final long delay,
            final int interval,
            final Request internalRequest,
            final int timeout,
            final int retries) {
        final Request request =
                buildAddRequest(owner, taskId, delay, interval, internalRequest, timeout, retries, "Modify");
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(
                "snmp buildModifyTaskRequest request: {},taskid={},timeout={},interval={},delay={},internalRequest:{},retries:{}",
                request,
                taskId,
                timeout,
                interval,
                delay,
                internalRequest,
                retries);
        }
        return request;
    }

    /**
     * Build add request request.
     *
     * @param owner the owner
     * @param taskId the taskId
     * @param delay the delay
     * @param interval the interval
     * @param internalRequest the internalRequest
     * @param timeout the timeout
     * @param retries the retries
     * @param action the action
     * @return the request
     */
    private static Request buildAddRequest(
            final String owner,
            final String taskId,
            final long delay,
            final int interval,
            final Request internalRequest,
            final int timeout,
            final int retries,
            final String action) {
        final Target target = new Target(internalRequest.getTarget().getAddress());
        target.setProtocolType("Status Poll");
        final Request request = new Request(target);
        // 轮询请求的nodeId使用执行的请求的nodeId
        request.setNodeId(internalRequest.getNodeId());
        // 设置超时时间和重试次数
        request.setTimeout(timeout);
        request.setRetries(retries);

        // 开始任务请求
        if (null != action) {
            request.addParam(new Param("Action", "", action));
        }
        // 任务的所有者
        request.addParam(new Param("Owner", "", owner));
        // 任务的id
        request.addParam(new Param("TaskID", "", taskId));
        // 修改的任务延迟多少时间启动，单位为秒
        request.addParam(new Param("Delay", "", delay + ""));
        // 修改后的任务间隔时间，单位为秒
        request.addParam(new Param("Interval", "", interval + ""));
        // 每次轮询执行的请求
        request.addParam(internalRequest);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(
                "snmp buildAddRequest request: {},taskid={},timeout={},interval={},delay={},internalRequest:{},retries:{}",
                request,
                taskId,
                timeout,
                interval,
                delay,
                internalRequest,
                retries);
        }
        return request;
    }

    /**
     * 构造删除定时任务的请求
     *
     * @param owner 定时任务的owner
     * @param taskId 定时任务的id
     * @param nodeId 内部实际执行的请求的nodeId
     * @param timeout 请求的超时时间
     * @param retries 请求的重试次数
     * @return 构造好的删除定时任务请求 request
     */
    public static Request buildRemoveTaskRequest(
            final String owner, final String taskId, final String nodeId, final int timeout, final int retries) {
        final Request request = buildTaskRequest(owner, taskId, nodeId, timeout, retries, "Stop");
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("snmp buildRemoveTaskRequest request:{},nodeId={},retries:{}", request, nodeId, retries);
        }
        return request;
    }

    /**
     * 构造查询定时任务的请求
     *
     * @param owner 定时任务的owner
     * @param nodeId 内部实际执行的请求的nodeId
     * @param timeout 请求的超时时间
     * @param retries 请求的重试次数
     * @return 构造好的删除定时任务请求 request
     */
    public static Request buildQueryTaskRequest(
            final String owner, final String nodeId, final int timeout, final int retries) {
        final Request request = buildRequest(owner, nodeId, timeout, retries, "List");
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("snmp buildQueryTaskRequest request:{},nodeId={},retries:{}", request, nodeId, retries);
        }
        return request;
    }

    /**
     * 构造激活定时任务的请求
     *
     * @param owner 定时任务的owner
     * @param taskId 定时任务的id
     * @param nodeId 内部实际执行的请求的nodeId
     * @param timeout 请求的超时时间
     * @param retries 请求的重试次数
     * @return 构造好的激活定时任务请求 request
     */
    public static Request buildActivateTaskRequest(
            final String owner, final String taskId, final String nodeId, final int timeout, final int retries) {
        final Request request = buildTaskRequest(owner, taskId, nodeId, timeout, retries, "Restart");
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(
                "snmp buildActivateTaskRequest request:{},taskid={},timeout={},nodeId={},owner={},retries:{}",
                request,
                taskId,
                timeout,
                nodeId,
                owner,
                retries);
        }
        return request;
    }

    /**
     * 构造去激活定时任务的请求
     *
     * @param owner 定时任务的owner
     * @param taskId 定时任务的id
     * @param nodeId 内部实际执行的请求的nodeId
     * @param timeout 请求的超时时间
     * @param retries 请求的重试次数
     * @return 构造好的去激活定时任务请求 request
     */
    public static Request buildDeactivateTaskRequest(
            final String owner, final String taskId, final String nodeId, final int timeout, final int retries) {
        final Request request = buildTaskRequest(owner, taskId, nodeId, timeout, retries, "Pause");
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(
                "snmp buildDeactivateTaskRequest request:{},taskid={},timeout={},nodeId={},owner={},retries:{}",
                request,
                taskId,
                timeout,
                nodeId,
                owner,
                retries);
        }
        return request;
    }

    /**
     * Build request request.
     *
     * @param owner the owner
     * @param nodeId the nodeId
     * @param timeout the timeout
     * @param retries the retries
     * @param action the action
     * @return the request
     */
    private static Request buildRequest(
            final String owner, final String nodeId, final int timeout, final int retries, final String action) {
        final Target target = new Target("");
        target.setProtocolType("Status Poll");
        final Request request = new Request(target);
        // 轮询请求的nodeId使用实际执行的请求的nodeId
        request.setNodeId(nodeId);
        // 设置超时时间和重试次数
        request.setTimeout(timeout);
        request.setRetries(retries);
        if (null != action) {
            request.addParam(new Param("Action", "", action));
        }
        // 任务的所有者
        request.addParam(new Param("Owner", "", owner));
        return request;
    }

    /**
     * Build task request request.
     *
     * @param owner the owner
     * @param taskId the taskId
     * @param nodeId the nodeId
     * @param timeout the timeout
     * @param retries the retries
     * @param action the action
     * @return the request
     */
    private static Request buildTaskRequest(
            final String owner,
            final String taskId,
            final String nodeId,
            final int timeout,
            final int retries,
            final String action) {
        final Request request = buildRequest(owner, nodeId, timeout, retries, action);
        request.addParam(new Param("TaskID", "", taskId));
        return request;
    }
}
