/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.mediation.sdk.client.impl;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulFactory;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.bsp.util.ThreadPoolBuilder;
import com.huawei.cloudsop.common.tokenhelper.util.rpc.RestAdapter;
import com.huawei.i2000.mediation.sdk.BadRequestException;
import com.huawei.i2000.mediation.sdk.Listener;
import com.huawei.i2000.mediation.sdk.Request;
import com.huawei.i2000.mediation.sdk.Response;
import com.huawei.i2000.mediation.sdk.client.Sender;
import com.huawei.i2000.mediation.sdk.config.MedConfigUtil;
import com.huawei.i2000.mediation.sdk.json.MedJsonUtil;
import com.huawei.i2000.mediation.sdk.model.ResourceConstant;

import com.huawei.oms.util.StringUtil;

import com.huawei.oms.util.fortifytool.FortifyUtil;
import lombok.Getter;
import lombok.Setter;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.stereotype.Component;


import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 基于CloudSOP发送Rest请求
 *
 * <AUTHOR>
 * @since 2022-03-6
 */
@Component
public class RestSender implements Sender {
    private static final Logger LOGGER = LoggerFactory.getLogger(RestSender.class);
    private static final int TRANSPORT_DELAY = 1000;
    private static final String MED_SERVICE_PREFIX = "/rest/dvmediationservice/";
    private static final String MED_STUB_SERVICE_PREFIX = "/rest/medstub/";
    private static final String REQUEST_HASHKEY = "dvmedrequest_hashkey";
    private Map<Long, RequestTimeOutTask> pendingRequest = new ConcurrentHashMap<>();
    private CallbackReceiver callbackReceiver;
    private AtomicLong associatedIdGenerator = new AtomicLong(0);

    @Getter
    @Setter
    private String urlPrefix = MED_SERVICE_PREFIX;

    private static ScheduledExecutorService countDownExecutor =
            new ScheduledThreadPoolExecutor(
                    1, new ThreadPoolBuilder.FwThreadFactory("Med-IR_Sender", "request-timeout-countdown", false));

    public RestSender() {
        if (StringUtil.isEmpty(FortifyUtil.getenv("LVS_VIP_Addr")) || FortifyUtil.getenv("LVS_VIP_Addr")
            .contains("LVS_VIP")) {
            urlPrefix = MED_STUB_SERVICE_PREFIX;
            LOGGER.info("get empty LVS_VIP_Addr env,set urlPrefix to {}", urlPrefix);
        }
    }

    public RestSender(boolean isMedStandAlone) {
        if (!isMedStandAlone) {
            urlPrefix = MED_STUB_SERVICE_PREFIX;
            LOGGER.info("isMedStandAlone {}", isMedStandAlone);
        } else {
            urlPrefix = MED_SERVICE_PREFIX;
        }
    }

    @Override
    public Response send(Request request) throws IOException, TimeoutException, BadRequestException {
        LOGGER.debug("send request to med.");
        RestAdapter restAdapter = new RestAdapter(RestfulFactory.getRestInstance());
        RestfulResponse restfulResponse;
        try {
            restfulResponse =
                    restAdapter.post(
                            urlPrefix + ResourceConstant.URI_REQUEST,
                            buildParameters(request),
                            buildOptions(request));
            LOGGER.debug("receive response from med.");
        } catch (ServiceException e) {
            LOGGER.debug("send request failed.", e);
            throw new IOException(e);
        }
        String content = restfulResponse.getResponseContent();
        if (restfulResponse.getStatus() != HttpStatus.OK_200) {
            LOGGER.error("rest response status:{}", restfulResponse.getStatus());
            throw new BadRequestException(restfulResponse.getStatus(), content);
        }
        Response response = MedJsonUtil.unMarshal(content, Response.class);
        LOGGER.debug("response unmarshal.");
        // 判断同步请求的resp中是否有异常，有则抛出
        analyseResponse(response);
        return response;
    }

    private void analyseResponse(final Response response) throws TimeoutException, BadRequestException, IOException {
        if (response == null) {
            throw new TimeoutException("get null response");
        }
        if (response.isExceptional()) {
            String exceptionType = response.getExceptionType();
            final Exception exception = response.getException();
            LOGGER.error("get Exception {} from response {}", exceptionType, response.getId(), exception);
            if (BadRequestException.class.getTypeName().equals(exceptionType)) {
                throw new BadRequestException(response.getErrCode(), exception.getMessage());
            } else if (TimeoutException.class.getTypeName().equals(exceptionType)) {
                throw new TimeoutException(exception.getMessage());
            } else if (IOException.class.getTypeName().equals(exceptionType)) {
                throw new IOException(exception.getMessage(), exception.getCause());
            } else {
                throw new IOException("get exception response from mediation service", exception);
            }
        }
    }

    @Override
    public long asyncSend(Request request, Listener<Response> listener) {
        LOGGER.debug("asyncSend request to med.");
        long id;
        RestAdapter restAdapter = new RestAdapter(RestfulFactory.getRestInstance());
        long associatedId = associatedIdGenerator.incrementAndGet();
        try {
            // 发送异步请求前，先缓存associatedId与listener 的关系
            RequestTimeOutTask task = new RequestTimeOutTask(associatedId, request, listener, this);
            pendingRequest.put(associatedId, task);

            String serviceInstanceId = FortifyUtil.getenv("PROCESS_INSTANCE_ID");
            LOGGER.debug(serviceInstanceId);
            request.setAssociatedId(associatedId);
            RestfulParametes restParam = buildParameters(request);
            Map<String, String> paramMap = new HashMap<>();
            if (serviceInstanceId != null && !serviceInstanceId.isEmpty()) {
                paramMap.put("PROCESS_INSTANCE_ID", serviceInstanceId);
            }
            paramMap.put("callbackurl", "/rest/" + ResourceConstant.URI_CALLBACK);
            restParam.setParamMap(paramMap);
            RestfulResponse restfulResponse;
            restfulResponse =
                    restAdapter.post(
                            urlPrefix + ResourceConstant.URI_ASYREQUEST, restParam, buildOptions(request));
            String content = restfulResponse.getResponseContent();
            // 从medService获取到medcore生成的requestId
            id = Long.parseLong(content);
            request.setId(id);
            LOGGER.debug("receive response from med. id: {}", id);
            // 开始执行超时计时器
            task.startCountDown(getTimeout(request));

        } catch (ServiceException | IOException e) {
            LOGGER.debug("send failed.", e);
            // 下发失败，清理回调关系缓存
            pendingRequest.remove(associatedId);
            id = 0;
        }
        return id;
    }

    private RestfulParametes buildParameters(Request request) throws IOException {
        RestfulParametes restParam = new RestfulParametes();
        restParam.setRawData(MedJsonUtil.marshal(request));
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        restParam.setHeaderMap(header);

        if ("BSON".equals(request.getTarget().getProtocolType()) && !StringUtil.isEmpty(MedConfigUtil.getValue("bson.instance.name"))) {
            // 对bson做特殊处理
            LOGGER.debug("bson request will redirect to ");
            restParam.getHeaderMap().put("XX-Backend-Force", MedConfigUtil.getValue("bson.instance.name"));
        } else if ("true".equals(request.getTarget().getParam("isSession")) && !"medNode_Master".equals(request.getNodeId())) {
            // 对于会话性请求，根绝请求中的nodeId定向发送到某一med节点
            restParam.getHeaderMap().put("XX-Backend", request.getNodeId());
        } else if (request.getTarget() != null && !"SNMP".equals(request.getTarget().getProtocolType())) {
            restParam.getHeaderMap().put(REQUEST_HASHKEY, request.getTarget().getAddress());
        }
        return restParam;
    }

    private RestfulOptions buildOptions(Request request) {
        RestfulOptions options = new RestfulOptions();
        options.setRestTimeout(getTimeout(request));
        return options;
    }

    private int getTimeout(final Request request) {
        if (request.getTotalTimeout() != 0) {
            LOGGER.debug("total timeout: {}", request.getTotalTimeout());
            return request.getTotalTimeout() + TRANSPORT_DELAY;
        }

        return (request.getTimeout() * (request.getRetries() + 1) + TRANSPORT_DELAY);
    }

    public Map<Long, RequestTimeOutTask> getPendingRequest() {
        return pendingRequest;
    }

    public ScheduledExecutorService getCountDownExecutor() {
        return countDownExecutor;
    }

    public CallbackReceiver getCallbackReceiver() {
        return callbackReceiver;
    }

    public void setCallbackReceiver(CallbackReceiver callbackReceiver) {
        this.callbackReceiver = callbackReceiver;
        this.callbackReceiver.setPendingRequest(pendingRequest);
    }

}
