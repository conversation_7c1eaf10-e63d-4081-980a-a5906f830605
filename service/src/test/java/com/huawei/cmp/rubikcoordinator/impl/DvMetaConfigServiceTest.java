/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.cmp.rubikcoordinator.impl;

import com.huawei.baize.servicesimulator.base.bsp.test.junit.BspIntegrationTests;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cmp.foundation.service.exception.CMPException;
import com.huawei.cmp.foundation.service.util.WebUtil;
import com.huawei.cmp.oma.api.topo.model.instance.Condition;
import com.huawei.cmp.oma.api.topo.model.instance.ResourceNodeInstance;
import com.huawei.cmp.oma.api.topo.model.instance.ServiceNodeInstance;
import com.huawei.cmp.rubikcoordinator.api.enums.OperationType;
import com.huawei.cmp.rubikcoordinator.api.enums.ResultStatus;
import com.huawei.cmp.rubikcoordinator.api.model.domain.NodeDispatchStatus;
import com.huawei.cmp.rubikcoordinator.api.utils.RestUtils;
import com.huawei.cmp.rubikcoordinator.api.utils.RubikServiceConfigUtil;
import com.huawei.cmp.rubikcoordinator.base.TestConstants;
import com.huawei.cmp.rubikcoordinator.cert.model.CertObject;
import com.huawei.cmp.rubikcoordinator.cert.model.CertResult;
import com.huawei.cmp.rubikcoordinator.cert.model.CertType;
import com.huawei.cmp.rubikcoordinator.cert.model.CertUpdateMsg;
import com.huawei.cmp.rubikcoordinator.cert.model.CertUpdateResult;
import com.huawei.cmp.rubikcoordinator.cert.model.FileFormat;
import com.huawei.cmp.rubikcoordinator.cert.model.ManifestFile;
import com.huawei.cmp.rubikcoordinator.cert.model.NeServiceInfo;
import com.huawei.cmp.rubikcoordinator.cert.model.PresetedCerts;
import com.huawei.cmp.rubikcoordinator.cert.model.ServiceCertUpdateResult;
import com.huawei.cmp.rubikcoordinator.cert.model.ServiceRegisterReq;
import com.huawei.cmp.rubikcoordinator.cert.task.CertRegisterTask;
import com.huawei.cmp.rubikcoordinator.dao.LogCleanupDao;
import com.huawei.cmp.rubikcoordinator.dispatch.ConfigDelete;
import com.huawei.cmp.rubikcoordinator.dispatch.ResourcePatternDispatch;
import com.huawei.cmp.rubikcoordinator.dispatch.SftpConfigDispatch;
import com.huawei.cmp.rubikcoordinator.dispatch.VsearchConfigDelete;
import com.huawei.cmp.rubikcoordinator.dispatch.VsearchConfigDispatch;
import com.huawei.cmp.rubikcoordinator.exception.MCExceptionCode;
import com.huawei.cmp.rubikcoordinator.facade.LogConfigMapperFacade;
import com.huawei.cmp.rubikcoordinator.facade.NodeStatusMapperFacade;
import com.huawei.cmp.rubikcoordinator.facade.SftpConfigMapperFacade;
import com.huawei.cmp.rubikcoordinator.facade.SolutionStatusMapperFacade;
import com.huawei.cmp.rubikcoordinator.model.Log;
import com.huawei.cmp.rubikcoordinator.model.LogField;
import com.huawei.cmp.rubikcoordinator.model.LogOutPut;
import com.huawei.cmp.rubikcoordinator.model.PkgTaskPollResult;
import com.huawei.cmp.rubikcoordinator.model.RubikPollResult;
import com.huawei.cmp.rubikcoordinator.model.SftpLinkData;
import com.huawei.cmp.rubikcoordinator.model.Solution;
import com.huawei.cmp.rubikcoordinator.model.SolutionConfigStatus;
import com.huawei.cmp.rubikcoordinator.service.DispatchConfigTask;
import com.huawei.cmp.rubikcoordinator.service.MetaConfigTopoService;
import com.huawei.cmp.rubikcoordinator.template.output.BaseOutPut;
import com.huawei.cmp.rubikcoordinator.topo.impl.MatrixTopoServiceImpl;
import com.huawei.cmp.rubikcoordinator.util.CommonMock;
import com.huawei.cmp.rubikcoordinator.util.KafkaTopicUtil;

import io.restassured.RestAssured;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import mockit.Mock;
import mockit.MockUp;
import mockit.Mocked;

import org.apache.kafka.clients.CommonClientConfigs;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.KafkaAdminClient;
import org.apache.kafka.common.security.ssl.DefaultSslEngineFactory;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * DvMetaConfigServiceTest
 *
 * <AUTHOR>
 * @version V8
 * @since 2021/10/11
 */
@PrepareForTest(BaseOutPut.class)

public class DvMetaConfigServiceTest extends BspIntegrationTests {
    private ExecutorService executor;

    @Autowired
    private DvMetaConfigServiceDelegateImpl dvMetaConfigServiceDelegateImpl;

    @Mocked
    @Autowired
    VsearchConfigDispatch vsearchConfigDispatch;

    /**
     * MOCK bean初始化
     */
    @BeforeClass
    public static void mockInit() {
        CommonMock.setUp1();
        CommonMock.setUp2();
        CommonMock.mockUser();
        CommonMock.mockAllTask();
        new MockUp<SftpConfigDispatch>() {
            /**
             * 跳过sftp下发
             *
             * @param solutionId 解决方案id
             */
            @Mock
            public void configDispatch(String solutionId) {
                return;
            }
        };
    }

    /**
     * Mock
     */
    @BeforeClass
    public static void setUp1() {
        new MockUp<NodeStatusMapperFacade>() {
            /**
             * isDeleteAble
             *
             * @param solutionMoType solutionMoType
             * @return boolean
             */
            @Mock
            public boolean isDeleteAble(String solutionMoType) {
                if ("CMP".equals(solutionMoType) || "CMP1021".equals(solutionMoType)) {
                    return true;
                }
                return false;
            }

            /**
             * isImportAble
             *
             * @param solutionMoType solutionMoType
             * @return boolean
             */
            @Mock
            public boolean isImportAble(String solutionMoType) {
                if ("CMP".equals(solutionMoType) || "CMP1021".equals(solutionMoType)) {
                    return true;
                }
                return false;
            }
        };

        new MockUp<ConfigDelete>() {
            /**
             * configDelete
             *
             * @param logTypes logTypes
             * @param nodes nodes
             * @param solutionMotype solutionMotype
             */
            @Mock
            public void configDelete(List<String> logTypes,
                List<com.huawei.cmp.rubikcoordinator.api.model.domain.NodeDispatchStatus> nodes,
                String solutionMotype) {

            }

        };

        new MockUp<VsearchConfigDelete>() {
            /**
             * configDelete
             *
             * @param logType 删除的日志类型
             */
            @Mock
            public void configDelete(String logType) {

            }

        };
        new MockUp<NodeStatusMapperFacade>() {
            /**
             * isImportAble
             *
             * @param solutionMoType 解决方方案类型
             * @param tenantId 租户ID
             * @return boolean
             */
            @Mock
            public boolean isImportAble(String solutionMoType, String tenantId) {
                return true;
            }
        };
        new MockUp<NodeStatusMapperFacade>() {
            /**
             * isImportAble
             *
             * @param solutionMoType 解决方方案类型
             * @param tenantId 租户ID
             * @return 是否可导入的标志
             */
            @Mock
            public boolean isImportAble(String solutionMoType, String tenantId) {
                return true;
            }
        };

        new MockUp<LogCleanupDao>() {
            /**
             * 获得clean表的下一个序号
             *
             * @return cleanup表的下一个序号
             */
            @Mock
            public long nextLogCleanupSeqid() {
                return 11111122;
            }
        };
        new MockUp<NodeStatusMapperFacade>() {
            /**
             * 更新nodestatus
             *
             * @param nodeDispatchStatus  待更新的node
             */
            @Mock
            public void updateStatus(NodeDispatchStatus nodeDispatchStatus) {
                return;
            }
        };
    }

    /**
     * Mock
     */
    @BeforeClass
    public static void setUp2() {
        new MockUp<RestUtils>() {
            /**
             * 构建响应信息
             *
             * @param path path
             * @param paramter paramter
             * @param rawData rawData
             * @return RestfulResponse
             * @throws ServiceException 异常信息
             */
            @Mock
            public RestfulResponse post(String path, Map<String, String> paramter, String rawData)
                throws ServiceException {
                RestfulResponse resp = new RestfulResponse();
                resp.setStatus(200);
                if (path.equals(TestConstants.Topo.TOPO_URL + "queryAllTreeInstanceIdBySolutionType")) {
                    resp.setResponseJson(
                        "{\"solution1\":\"/\",\"com.huawei.cmp.solution=1\":\"solution1\",\"bottomNode\":\"com.huawei.cmp.solution=1\"}");
                } else {
                    resp.setResponseJson(
                        "{\"instances\":[{\"instanceId\":\"com.huawei.cmp.solution=1\",\"type\":\"com.huawei.cmp.solution\",\"displayType\":\"CMP\",\"name\":\"CMP\",\"version\":null,\"parentInstanceId\":\"/\",\"spaceId\":null,\"groupName\":null,\"siteName\":null,\"insExtInfos\":null,\"insExtK8SInfos\":null,\"level\":\"0\",\"childrenIds\":[],\"rootId\":\"com.huawei.cmp.solution=1\",\"referencedBy\":null}],\"total\":1}");
                }
                return resp;
            }
        };

        new MockUp<MatrixTopoServiceImpl>() {
            /**
             * queryRealSolutionId
             *
             * @param instanceId instanceId
             * @return String
             */
            @Mock
            public String queryRealSolutionId(String instanceId) {
                return "CMP";
            }

            /**
             * queryInstanceName
             *
             * @param instanceIds instanceIds
             * @return 从topo获得底层网元的机器与dn对应关系表
             */
            @Mock
            public Map<String, ServiceNodeInstance> queryInstanceName(List<String> instanceIds) {
                Map<String, ServiceNodeInstance> nodeInstanceMap = new HashMap<>();
                ServiceNodeInstance serviceNodeInstance = new ServiceNodeInstance();
                serviceNodeInstance.setName("cmpTestName");
                nodeInstanceMap.put("solution3", serviceNodeInstance);
                ServiceNodeInstance serviceNodeInstance1 = new ServiceNodeInstance();
                serviceNodeInstance1.setName("cmpTestName1");
                nodeInstanceMap.put("solution4", serviceNodeInstance1);
                return nodeInstanceMap;
            }

            ;

        };

        new MockUp<WebUtil>() {
            /**
             * getClientIp
             *
             * @param context context
             * @return String
             */
            @Mock
            public String getClientIp(HttpContext context) {
                return "10.10";
            }
        };
        new MockUp<KafkaTopicUtil>() {
            /**
             * getAdminClient
             *
             * @return 返回kafkaclient
             * @throws IOException IOException
             */
            @Mock
            public AdminClient getAdminClient() throws IOException {

                Properties configs = new Properties();
                configs.put(CommonClientConfigs.BOOTSTRAP_SERVERS_CONFIG, "**************:21007,**************:21007");
                configs.put("security.protocol", "SSL");
                configs.put("listener.name.internal.ssl.endpoint.identification.algorithm", "");
                configs.put("ssl.workkey.name ", "common_shared.ksf");
                configs.put("ssl.keystore.password",
                    "0000000100000001ACAD8AB2F971DF3C56CDE34D93DEF98D640CA34304E6ED2DF2893D8AE62FFA41F9C5B49B01F4AD68B77036F9A37A5218");
                configs.put("ssl.enabled.protocols", "TLSv1.3");
                configs.put("ssl.truststore.type", "JKS");
                configs.put("ssl.truststore.location", "/opt/oss/SOP/etc/ssl/kafka/trust.jks");
                configs.put("ssl.endpoint.identification.algorithm", "");
                configs.put("ssl.truststore.password",
                    "0000000100000001EDFC67790865FA0372CD23210D25D4AF51038EFCF21D967837076906066BDABC33972858B992DE5170358448FF9AD187");
                configs.put("ssl.keystore.type", "PKCS12");
                configs.put("security.storage.cipher", " org.apache.kafka.common.security.DefaultCipher");
                configs.put("ssl.cipher.suites",
                    "TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,TLS_AES_128_GCM_SHA256,TLS_AES_256_GCM_SHA384");
                configs.put("ssl.keystore.location ", "/opt/oss/SOP/etc/ssl/kafka/client.p12");
                configs.put("ssl.certificate.crl.location", "/opt/oss/SOP/apps/MQAgent/etc/ssl/kafka/server.crl");
                configs.put("ssl.key.password",
                    "0000000100000001A4E3F98CE32A50BDDAAC607718075075816D06A0552995CC70272F73018BF82F9D7ABCFD7587F80355451112CADDBE57");
                return AdminClient.create(configs);
            }
        };
        new MockUp<DefaultSslEngineFactory>() {
            /**
             * configure
             *
             * @param configs 待修正的配置
             */
            @Mock
            public void configure(Map<String, ?> configs) {
                return;
            }
        };
        new MockUp<MatrixTopoServiceImpl>() {
            /**
             * queryInstanceIdListByMoType
             *
             * @param clusterMoType 集群类型
             * @param clusterVersion  集群版本
             * @param spaceIdList  隔离id列表
             * @return 返回某个集群类型对用的解决方案实例ids
             */
            @Mock
            public List<String> queryInstanceIdListByMoType(String clusterMoType, String clusterVersion,
                List<String> spaceIdList) {
                String inst1 = "solution1";
                List<String> instList = new ArrayList<>();
                instList.add(inst1);
                return instList;
            }

            /**
             * queryConditionByInstanceId
             *
             * @param instanceId 集群实例id
             * @return 返回条件
             */
            @Mock
            public Condition queryConditionByInstanceId(String instanceId) {
                Condition condition = new Condition();
                condition.setType("CMP");
                return condition;
            }

            /**
             * batchQueryResourceNodeInstance
             *
             * @param clustersIds 集群实例id集
             * @return 返回 批量查询对应子网元关系
             * @throws ServiceException 服务异常
             */
            @Mock
            public Map<String, List<ResourceNodeInstance>> batchQueryResourceNodeInstance(List<String> clustersIds)
                throws ServiceException {
                Map<String, List<ResourceNodeInstance>> map = new HashMap<>();
                List<ResourceNodeInstance> resourceNodeInstances = new ArrayList<>();
                ResourceNodeInstance resourceNodeInstance = new ResourceNodeInstance();
                resourceNodeInstance.setNodePort("333");
                resourceNodeInstance.setInstanceId("bottomNode");
                resourceNodeInstance.setNodeAddress("**********");
                resourceNodeInstances.add(resourceNodeInstance);
                map.put("bottomNode", resourceNodeInstances);
                return map;
            }

        };
    }

    /**
     * test getSolutionTypeByCode
     */
    @Test
    public void getSolutionTypeByCode() {
        Integer code = 1;
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/getSolutionTypeByCode";
        String solutionType = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("code", code)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .asString();
        Assert.assertEquals("test", solutionType);
    }

    /**
     * test getSolutionTypeByCode
     */
    @Test
    public void isInternalLogType() {
        String logType = "avroTemplate";
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/isInternalLogType";
        Boolean result = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("logType", logType)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .as(Boolean.TYPE);
        Assert.assertEquals(true, result);
    }

    /**
     * test queryLogExtracts
     */
    @Test
    public void queryLogExtracts() {
        String solutionType = "CMP";
        String clusterType = "CLUSTERMOTYPE1";
        String logType = "logtest2";
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/queryLogExtracts";
        int statusCode = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParams("solutionType", solutionType, "clusterType", clusterType, "logType", logType)
            .when()
            .post(api)
            .thenReturn()
            .statusCode();
        Assert.assertEquals(200, statusCode);
    }

    /**
     * test querySolutionById
     */
    @Test
    public void querySolutionById() {
        String solutionId = "111";
        String moType = "CMP";
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/querySolutionById";
        Solution solution = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("solutionId", solutionId)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .as(Solution.class);
        String resultMotype = solution.getMotype();
        Assert.assertEquals(moType, resultMotype);
    }

    /**
     * test querySolutionList
     */
    @Test
    public void querySolutionList() {
        String moType = "CMP";
        List<HashMap> querySolutionList;
        String resultMotype = "";
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/querySolutionList";
        List list = RestAssured.given()
            .contentType(ContentType.JSON)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .as(List.class);
        querySolutionList = list;
        for (HashMap querySolution : querySolutionList) {
            if (moType.equals(querySolution.get("motype"))) {
                resultMotype = (String) querySolution.get("motype");
                break;
            }
        }
        Assert.assertEquals(moType, resultMotype);
    }

    /**
     * test querySolution
     */
    @Test
    public void querySolution() {
        String solutionMoType = "CMP";
        String type = "logtest2";
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/querySolution";
        Solution solution = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("solutionMoType", solutionMoType)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .as(Solution.class);
        List<Log> logs = solution.getLogs();
        Assert.assertEquals(type, logs.get(0).getType());
    }

    /**
     * test queryPkAllNodesByStatus
     */
    @Test
    public void queryPkAllNodesByStatus() {
        ManifestFile manifestFile = new ManifestFile();
        Map<String, Map<String, String>> filelist = new HashMap<>();
        manifestFile.setFilelist(filelist);
        NeServiceInfo neServiceInfo = new NeServiceInfo();
        neServiceInfo.setInstanceId("1");
        neServiceInfo.setServiceId("1");
        PresetedCerts presetedCerts = new PresetedCerts();
        List<CertObject> certs = new ArrayList<>();
        presetedCerts.setCerts(certs);
        presetedCerts.setServiceId("1");
        ServiceCertUpdateResult serviceCertUpdateResult = new ServiceCertUpdateResult();
        serviceCertUpdateResult.setResult(new CertResult());
        serviceCertUpdateResult.setServiceId("123");
        serviceCertUpdateResult.setServiceName("test");
        ServiceRegisterReq serviceRegisterReq = new ServiceRegisterReq();
        serviceRegisterReq.setNodeIP("********");
        serviceRegisterReq.setServiceInfoList(new ArrayList<>());
        int status = 6;
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/queryPkAllNodesByStatus";
        Boolean result = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("status", status)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .as(Boolean.TYPE);
        Assert.assertEquals(true, result);
    }

    /**
     * test certManager
     */
    @Test
    public void testcertManager() {
        CertObject certObject = new CertObject();
        certObject.setCertName("server.p12");
        certObject.setCertType(CertType.TRUST.getType());
        certObject.setFileFormat(FileFormat.PKCS12.getCertFormat());
        certObject.setCommonCertId("1");
        certObject.setContent("test");
        certObject.setKeyPass("test");
        certObject.setKeyType("test");
        certObject.setRemark("test");
        certObject.setStorePass("test");
        CertResult certResult = new CertResult();
        certResult.setError("error");
        certResult.setReturnCode(1);
        CertUpdateMsg certUpdateMsg = new CertUpdateMsg();
        certUpdateMsg.setCertId("1");
        certUpdateMsg.setCertName("test");
        certUpdateMsg.setNodeIp("********");
        certUpdateMsg.setServiceId("1");
        certUpdateMsg.setTaskId("123");
        CertUpdateResult certUpdateResult = new CertUpdateResult();
        certUpdateResult.setNodeIP("********");
        List<ServiceCertUpdateResult> serviceCertUpdateResultList = new ArrayList<>();
        certUpdateResult.setServiceCertUpdateResultList(serviceCertUpdateResultList);
        certUpdateResult.setTaskId("123");
        executor = Executors.newSingleThreadExecutor();
        Future<?> submit = executor.submit(new CertRegisterTask());
        executor.shutdown();
        Assert.assertNotNull(submit);
    }

    /**
     * test deleteNodeStatus
     */
    @Test
    public void deleteNodeStatus() {
        String solutionMoType = "testdelete";
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/deleteNodeStatus";
        RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("solutionMoType", solutionMoType)
            .when()
            .post(api)
            .then()
            .statusCode(200);

        String queryApi = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/querySolution";
        Solution solution = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("solutionMoType", solutionMoType)
            .when()
            .post(queryApi)
            .thenReturn()
            .body()
            .as(Solution.class);

        Assert.assertNull(solution);
    }

    /**
     * deleteConfig
     */
    @Test
    public void deleteConfigError() {
        // 测试异常场景
        String solutionId = "333";
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/deleteConfig";
        int statusCode = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("solutionId", solutionId)
            .when()
            .post(api)
            .thenReturn()
            .statusCode();
        Assert.assertEquals(500, statusCode);
    }

    /**
     * deleteConfig
     */

    public void deleteConfig() {

        new MockUp<SftpConfigMapperFacade>() {
            public void deleteSftpConfigRelation(Integer solutionId) {
                return;
            }

        };
        String solutionId = "1021";
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/deleteConfig";
        int statusCode = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("solutionId", solutionId)
            .when()
            .post(api)
            .thenReturn()
            .statusCode();
        Assert.assertEquals(200, statusCode);
    }

    /**
     * dispatchedLogSourcesQuery
     */
    @Test
    public void dispatchedLogSourcesQuery() {
        String ip = "********";
        String logType = "logtest2";
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/dispatchedLogSourcesQuery";
        int statusCode = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParams("ip", ip, "logType", logType)
            .when()
            .post(api)
            .thenReturn()
            .statusCode();
        Assert.assertEquals(200, statusCode);
    }

    /**
     * getCodeBySolutionInsId
     */
    @Test
    public void getCodeBySolutionInsId() {
        String solutionInsId = "CMP";
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/getCodeBySolutionInsId";
        List list = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("solutionInsId", solutionInsId)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .as(List.class);
        Assert.assertEquals(2, list.get(0));
    }

    /**
     * getCodeBySolutionType
     */
    @Test
    public void getCodeBySolutionType() {
        String solutionType = "test";
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/getCodeBySolutionType";
        int code = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("solutionType", solutionType)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .as(new TypeRef<Integer>() { });
        Assert.assertEquals(1, code);
    }

    /**
     * queryLogAnalysisMeta
     */
    @Test
    public void queryLogAnalysisMeta() {
        Boolean isNeedExtract = true;
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/queryLogAnalysisMeta";
        int statusCode = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("isNeedExtract", isNeedExtract)
            .when()
            .post(api)
            .thenReturn()
            .statusCode();
        Assert.assertEquals(200, statusCode);
    }

    /**
     * testqueryPkgStatus
     */
    public void queryPkgStatus() {
        String solutionMoType = "testqueryPkgStatus";
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/queryPkgStatus";
        RubikPollResult queryPkgStatus = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("solutionMoType", solutionMoType)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .as(RubikPollResult.class);
        Assert.assertEquals("SUCCESS", queryPkgStatus.getPkgTaskPollResult().getResult().toString());
    }

    /**
     * dispatch config test
     */
    @Test
    public void dispatchConfigTest() {
        new MockUp<KafkaAdminClient>() {
            /**
             * queryRealSolutionId
             *
             * @param timeout timeout
             */
            @Mock
            public void close(Duration timeout) {
                return;
            }
        };

        new MockUp<DispatchConfigTask>() {
            /**
             * queryRealSolutionId
             *
             * @param solutionId solutionId
             * @param dispatchType dispatchType
             */
            @Mock
            public void addTask(String solutionId, int dispatchType) {
                return;
            }
        };
        new MockUp<LogConfigMapperFacade>() {
            /**
             * 查询解决方案下的日志数据
             *
             * @param solutionId 解决方案ID
             * @return 返回日志列表
             */
            @Mock
            public List<Log> queryLogsBySolutionIdCollector(String solutionId) {
                List<Log> logList = new ArrayList<>();
                Log vsLog = CommonMock.getLog("dsflog2", "VS");
                Log fileLog = CommonMock.getLog("dsflog1", "file");
                logList.add(vsLog);
                logList.add(fileLog);
                return logList;
            }
        };
        new MockUp<LogConfigMapperFacade>() {
            /**
             * 查询解决方案下的日志数据
             *
             * @param solutionId 解决方案id
             * @return 返回日志数据列表
             */
            @Mock
            public List<Log> queryLogsBySolutionId(String solutionId) {
                List<Log> logList = new ArrayList<>();
                Log vsLog = CommonMock.getLog("dsflog2", "VS");
                Log fileLog = CommonMock.getLog("dsflog1", "file");
                logList.add(vsLog);
                logList.add(fileLog);
                return logList;
            }
        };
        new MockUp<BaseOutPut>() {
            /**
             * output基础处理类
             *
             */
            @Mock
            public void logTypeHandler() {
            }
        };

        new MockUp<NodeStatusMapperFacade>() {
            /**
             * 更新nodedispatchstatus方法
             *
             * @param nodeDispatchStatus 待更新的node下发数据
             */
            @Mock
            public void updateStatus(com.huawei.cmp.rubikcoordinator.model.NodeDispatchStatus nodeDispatchStatus) {
                return;
            }
        };

        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/dispatchConfig";
        int statusCode = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParams("solutionId", "1023")
            .when()
            .post(api)
            .thenReturn()
            .statusCode();
        Assert.assertEquals(200, statusCode);

    }

    /**
     * redispatch config test
     */
    @Test
    public void redispatchConfigTest() {
        new MockUp<KafkaAdminClient>() {
            /**
             * queryRealSolutionId
             *
             * @param timeout timeout
             */
            @Mock
            public void close(Duration timeout) {
                return;
            }
        };

        new MockUp<DispatchConfigTask>() {
            /**
             * queryRealSolutionId
             *
             * @param solutionId solutionId
             * @param dispatchType dispatchType
             */
            @Mock
            public void addTask(String solutionId, int dispatchType) {
                return;
            }
        };

        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/redispatchConfig";
        int statusCode = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParams("solutionId", "1023")
            .when()
            .post(api)
            .thenReturn()
            .statusCode();
        Assert.assertEquals(200, statusCode);
    }

    /**
     * is pkg operator able test
     *
     * @throws ServiceException ServiceException
     */
    @Test
    public void isPkgOperatorAbleTest() throws ServiceException {
        String solutionMoType = "pkgTest";
        OperationType operationType = OperationType.INSTALL;
        Boolean isPkgOperator = dvMetaConfigServiceDelegateImpl.isPkgOperatorAble(null, operationType, solutionMoType);
        Assert.assertEquals(true, isPkgOperator);
        operationType = OperationType.UNINSTALL;
        isPkgOperator = dvMetaConfigServiceDelegateImpl.isPkgOperatorAble(null, operationType, solutionMoType);
        Assert.assertEquals(true, isPkgOperator);

        operationType = OperationType.INSTALL;
        solutionMoType = "pkgNotInProgress";
        isPkgOperator = dvMetaConfigServiceDelegateImpl.isPkgOperatorAble(null, operationType, solutionMoType);
        Assert.assertEquals(true, isPkgOperator);

    }

    /**
     * get nodes from solution
     */
    @Test
    public void getNodesFromSolution() {
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/getNodesFromSolution";
        String solutionId = "111";
        Boolean isNodeExist = RestAssured.given()
            .contentType(ContentType.JSON)
            .queryParam("solutionId", solutionId)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .as(Boolean.TYPE);

        Assert.assertEquals(true, isNodeExist);
    }

    /**
     * update pkg status test
     */
    @Test
    public void updatePkgStatusTest() {
        PkgTaskPollResult pkgTaskPollResult = new PkgTaskPollResult();
        Map<String, String> typeKeys = new HashMap<>();
        typeKeys.put("solutionMoType", "pkgNotInProgress");
        pkgTaskPollResult.setTypeKeys(typeKeys);
        pkgTaskPollResult.setResult(ResultStatus.SUCCESS);
        RubikPollResult result = new RubikPollResult();
        result.setPkgTaskPollResult(pkgTaskPollResult);
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/updatePkgStatus";
        int statusCode = RestAssured.given()
            .contentType(ContentType.JSON)
            .body(result)
            .when()
            .post(api)
            .thenReturn()
            .statusCode();
        Assert.assertEquals(500, statusCode);
    }

    /**
     * valid solution config
     */
    @Test
    public void validSolutionConfig() {
        new MockUp<MetaConfigTopoService>() {
            /**
             * topo查询解决方案树
             *
             * @param solution 待查询的解决方案
             */
            @Mock
            public void analysis(Solution solution) {
            }
        };

        List<Log> logsList = new ArrayList<>();
        Log logVs = CommonMock.getLog("dsflogs1", "vs");

        logsList.add(logVs);
        Solution solution = new Solution();
        solution.setLogs(logsList);
        solution.setTenantId("default_tenant");
        solution.setId("111");
        solution.setMotype("CMP");
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/validSolutionConfig";
        int statusCode = RestAssured.given()
            .contentType(ContentType.JSON)
            .body(solution)
            .when()
            .post(api)
            .thenReturn()
            .statusCode();
        Assert.assertEquals(200, statusCode);

        Log logFile = CommonMock.getLog("dsflogs2", "file");
        logsList.add(logFile);
        statusCode = RestAssured.given()
            .contentType(ContentType.JSON)
            .body(solution)
            .when()
            .post(api)
            .thenReturn()
            .statusCode();
        Assert.assertEquals(200, statusCode);

        try {
            logsList.clear();
            Log validLog = CommonMock.getLog("dsflogs1", "vs");
            logsList.add(validLog);
            List<LogField> logFields = new ArrayList<>();
            LogField logField = new LogField();
            logField.setFormula(
                "testFormulatestFormulatestFormulat" + "estFormulatestFormulatestF" + "ormulatestFormulatestFormulatest"
                    + "FormulatestFormulatestFormulatestForm" + "ulatestFormulatestFormulatestFormulatestFo"
                    + "rmulatestFormulatestFormulatestFormulat" + "estFormulatestFormulatestFormulatestFormul"
                    + "atestFormulatestFormulatestForm" + "ulatestFormulatestFormulatestFormulatestForm"
                    + "ulatestFormulatestFormulatestFormulatestFormulatestF"
                    + "ormulatestFormulatestFormulatestFormulatestFormulatestFor"
                    + "mlatestFormulatestFormulatestFormulatestFormulatestForm"
                    + "ulatestFormulatestFormulatestFormulatestFormulatestFormula");
            logFields.add(logField);
            validLog.setFields(logFields);
            RestAssured.given().contentType(ContentType.JSON).body(solution).when().post(api).thenReturn().statusCode();

        } catch (CMPException ex) {
            long errorCode = ex.getExceptionCode();
            Assert.assertEquals(errorCode, MCExceptionCode.MC_XMLPARSE_ERR);
        }
    }

    /**
     * getSolutionConfigStatusWithAllTenant
     */
    @Test
    public void getSolutionConfigStatusWithAllTenant() {
        new MockUp<SolutionStatusMapperFacade>() {
            /**
             * 查询所有过下发任务的租户id
             *
             * @return list
             */
            @Mock
            public List<String> queryAllSolutionTenantId() {
                List<String> list = new ArrayList<>();
                list.add("default_tenant");
                return list;
            }
        };
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/getSolutionConfigStatusWithAllTenant";
        List<SolutionConfigStatus> list = RestAssured.given()
            .contentType(ContentType.JSON)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .as(new TypeRef<List<SolutionConfigStatus>>() { });
        Assert.assertEquals(0, list.size());
    }

    @Test
    public void solutionFileLogTypeValidTest() {
        new MockUp<RubikServiceConfigUtil>() {
            /**
             * 获取配置文件配置项
             *
             * @param key 属性key
             * @param defval key对应的默认值
             * @return 返回对应配置内容
             */
            @Mock
            public String getProperty(String key, String defval) {
                String retStr = "";
                if (key.equals("is_XCluster")) {
                    retStr = "true";
                } else {
                    retStr = RubikServiceConfigUtil.getProperty(key);
                }
                return retStr;
            }
        };
        Solution solution = new Solution();
        solution.setId("4000");
        solution.setMotype("SFTP_TEST");
        List<Log> logs = new ArrayList<>();
        Log log = new Log();
        log.setId("4000");
        log.setType("file_dump_log");
        List<LogOutPut> outPuts = new ArrayList<>();
        LogOutPut outPut = new LogOutPut();
        outPut.setType("file");
        outPut.setStatus(1);
        outPuts.add(outPut);
        log.setLogOutPuts(outPuts);
        logs.add(log);
        solution.setLogs(logs);
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/solutionFileLogTypeValid";
        SftpLinkData sftpLinkData = RestAssured.given()
            .contentType(ContentType.JSON)
            .body(solution)
            .when()
            .post(api)
            .thenReturn()
            .as(SftpLinkData.class);
        Assert.assertEquals(2, (long) sftpLinkData.getSftpId());
    }

    @Test
    public void modifyResourcePatternTest() {
        String agentLevel = "large";
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/modifyResourcePattern";
        int statusCode = RestAssured.given()
            .contentType(ContentType.JSON)
            .body(agentLevel)
            .when()
            .post(api)
            .thenReturn()
            .statusCode();
        Assert.assertEquals(200, statusCode);

        new MockUp<ResourcePatternDispatch>() {
            @Mock
            public void configDispatch(List<String> ipList, String agentLevel) {
                throw new CMPException(MCExceptionCode.MC_CLUSTER_LOG_SETTING_TTL_INVALID);
            }
        };

        statusCode = RestAssured.given()
            .contentType(ContentType.JSON)
            .body(agentLevel)
            .when()
            .post(api)
            .thenReturn()
            .statusCode();
        Assert.assertEquals(200, statusCode);

    }

    @Test
    public void queryResourcePatternTest() {
        String api = "/rest/dvrubikcoordinatorservice/v1/dvmetaconfigservice/queryResourcePattern";
        int statusCode = RestAssured.given().contentType(ContentType.JSON).when().post(api).thenReturn().statusCode();
        Assert.assertEquals(200, statusCode);
    }
}
