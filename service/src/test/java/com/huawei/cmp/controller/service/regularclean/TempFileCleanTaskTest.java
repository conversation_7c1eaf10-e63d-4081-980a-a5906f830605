/*
 *
 *  * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 *
 */

package com.huawei.cmp.controller.service.regularclean;

import com.huawei.bsp.commonlib.hofs.HofsException;
import com.huawei.cmp.TestModule;
import com.huawei.cmp.foundation.hofs.DVHofsUtil;

import mockit.Mock;
import mockit.MockUp;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * TempFileCleanTaskTest
 *
 * <AUTHOR>
 * @since 2021-12-01
 */
public class TempFileCleanTaskTest extends TestModule {
    @Autowired
    TempFileCleanTask tempFileCleanTask;

    private String path =
            "src"
                    + File.separator
                    + "test"
                    + File.separator
                    + "java"
                    + File.separator
                    + "com"
                    + File.separator
                    + "huawei"
                    + File.separator
                    + "cmp"
                    + File.separator
                    + "controller"
                    + File.separator
                    + "impl"
                    + File.separator
                    + "TestFile.txt";

    @Test
    /**
     * pathCheck
     *
     * @throws HofsException HofsException
     */
    public void pathCheck() throws HofsException {
        extracted();
        tempFileCleanTask.pathCheck(path, "remoteDestPath");
        Assert.assertTrue(DVHofsUtil.checkRemoteFile(path));
    }

    /**
     * extracted
     */
    private void extracted() {
        new MockUp<DVHofsUtil>() {
            @Mock
            private boolean checkRemoteFile(String remotePath) {
                return true;
            }

            @Mock
            private void deleteFileOrDir(String remotePath) {}
        };
    }

    @Test
    /**
     * deleteFiles
     */
    public void deleteFiles() {
        new MockUp<DVHofsUtil>() {
            @Mock
            private boolean checkRemoteFile(String remotePath) throws HofsException {
                throw new HofsException("test");
            }
        };
        List<String> filePathList = new ArrayList<>();
        filePathList.add(path);

        Assert.assertFalse(tempFileCleanTask.deleteFiles(filePathList));
    }
}
