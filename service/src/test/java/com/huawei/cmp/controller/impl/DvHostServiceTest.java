/*
 *
 *  * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 *
 */

package com.huawei.cmp.controller.impl;

import com.huawei.cmp.TestModule;
import com.huawei.cmp.agent.controller.api.common.HostStatus;
import com.huawei.cmp.controller.model.Host;
import com.huawei.cmp.controller.model.HostInfos;

import io.restassured.RestAssured;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2021-10.16
 */
public class DvHostServiceTest extends TestModule {
    @Test
    /**
     * queryAllHosts
     */
    public void queryAllHosts() {
        String api = "/rest/dvcontrollerservice/v1/dvhostservice/queryAllHosts";
        Assert.assertNotNull(
                RestAssured.given()
                        .contentType(ContentType.JSON)
                        .when()
                        .post(api)
                        .thenReturn()
                        .as(new TypeRef<List<Host>>() {}));
    }

    @Test
    /**
     * queryHost
     */
    public void queryHost() {
        String ip = "**************";
        String api = "/rest/dvcontrollerservice/v1/dvhostservice/queryHost";
        Assert.assertNull(
                RestAssured.given()
                        .contentType(ContentType.JSON)
                        .body(ip)
                        .when()
                        .post(api)
                        .thenReturn()
                        .body()
                        .as(Host.class));
    }

    @Test
    /**
     * queryAgentDockerHost
     */
    public void queryAgentDockerHost() {
        String ip = "***************";
        String api = "/rest/dvcontrollerservice/v1/dvhostservice/queryHost";
        Assert.assertNotNull(
            RestAssured.given()
                .contentType(ContentType.JSON)
                .body(ip)
                .when()
                .post(api)
                .thenReturn()
                .body()
                .as(Host.class));
    }

    @Test
    /**
     * queryHostStatus
     */
    public void queryHostStatus() {
        String ip = "**************";
        String api = "/rest/dvcontrollerservice/v1/dvhostservice/queryHostStatus";
        Assert.assertTrue(
                HostStatus.NOT_EXIST.equals(
                        RestAssured.given()
                                .contentType(ContentType.JSON)
                                .body(ip)
                                .when()
                                .post(api)
                                .thenReturn()
                                .body()
                                .as(HostStatus.class)));
    }

    @Test
    /**
     * testBatchQueryHostStatus
     */
    public void testBatchQueryHostStatus() {
        List<String> ipList = new ArrayList<>();
        ipList.add("**************");
        ipList.add("123.237.149.183_abcdefghijkl");
        String api = "/rest/dvcontrollerservice/v1/dvhostservice/batchQueryHostStatus";
        Assert.assertTrue(HostStatus.NOT_EXIST.equals(RestAssured.given()
            .contentType(ContentType.JSON)
            .body(ipList)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .as(new TypeRef<HashMap<String, HostStatus>>() { })
            .get("123.237.149.183_abcdefghijkl")));
    }

    @Test
    /**
     * testBatchQueryHostInfos
     */
    public void testBatchQueryHostInfos() {
        List<String> ipList = new ArrayList<>();
        ipList.add("**************");
        ipList.add("123.237.149.183_dockerId");
        String api = "/rest/dvcontrollerservice/v1/dvhostservice/batchQueryHostInfo";
        HostInfos hostInfos = RestAssured.given()
            .contentType(ContentType.JSON)
            .body(ipList)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .as(HostInfos.class);
        Assert.assertNotNull(hostInfos);
    }
}
