/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.cmp.dvcallchainservice.impl;

import com.huawei.baize.servicesimulator.base.bsp.test.junit.BspIntegrationTests;
import com.huawei.bsp.roa.util.clientsdk.JsonUtil;
import com.huawei.cmp.dvcallchainservice.model.CallchainDataViewVO;
import com.huawei.cmp.dvcallchainservice.model.ChainQueryParamModel;
import com.huawei.cmp.dvcallchainservice.model.callchainquery.CallchainDataModel;
import com.huawei.cmp.dvcallchainservice.util.CommonMock;

import com.fasterxml.jackson.core.type.TypeReference;

import io.restassured.RestAssured;
import io.restassured.http.ContentType;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * CallChainQueryService接口测试类
 *
 * @since 2024/7/8
 */
public class CallChainQueryServiceDelegateImplTest extends BspIntegrationTests {

    /**
     * 查询调用链总览数据视图
     *
     * @throws Exception Exception
     */
    @Test
    public void queryCallchainDataViewByFilter() throws Exception {
        CommonMock.mockIsDsf1();

        String api = "/rest/dvcallchainservice/v1/callchainqueryservice/queryCallchainDataViewByFilter";
        List<String> solutionTypes = new ArrayList<>();
        solutionTypes.add("com.huawei.testihealing.solution");
        ChainQueryParamModel chainQueryParamModel = new ChainQueryParamModel();
        chainQueryParamModel.setBeginTime("2024-07-16 16:00:00");
        chainQueryParamModel.setEndTime("2024-07-16 16:00:00");
        chainQueryParamModel.setNeedBussinessLog(false);
        CallchainDataModel callchainDataModel = new CallchainDataModel();
        callchainDataModel.setSolutionTypes(solutionTypes);
        callchainDataModel.setChainQueryParamModel(chainQueryParamModel);

        String str = RestAssured.given()
            .contentType(ContentType.JSON)
            .body(callchainDataModel)
            .when()
            .post(api)
            .thenReturn()
            .body()
            .asString();
        List<CallchainDataViewVO> callchainDataViewVOs = JsonUtil.unMarshal(str,
            new TypeReference<List<CallchainDataViewVO>>() { });
        Assert.assertEquals(0, callchainDataViewVOs.size());
    }
}