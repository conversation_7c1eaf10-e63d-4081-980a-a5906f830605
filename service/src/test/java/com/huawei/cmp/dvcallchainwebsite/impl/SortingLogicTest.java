package com.huawei.cmp.dvcallchainwebsite.impl;

import java.util.Arrays;
import java.util.List;
import java.util.TreeSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 独立测试排序逻辑的类
 */
public class SortingLogicTest {

    private static final String NOT_GROUPED = "NOT_GROUPED";
    private static final Pattern NUMBER_SUFFIX_PATTERN = Pattern.compile("^(.+?)(\\d+)$");

    /**
     * 复制的比较器逻辑
     */
    private static int compareWithNumericSuffix(String s1, String s2) {
        // 特殊处理 NOT_GROUPED
        if (s1.equals(NOT_GROUPED) && !s2.equals(NOT_GROUPED)) {
            return -1;
        } else if (!s1.equals(NOT_GROUPED) && s2.equals(NOT_GROUPED)) {
            return 1;
        } else if (s1.equals(NOT_GROUPED) && s2.equals(NOT_GROUPED)) {
            return 0;
        }

        // 尝试匹配数字后缀
        Matcher matcher1 = NUMBER_SUFFIX_PATTERN.matcher(s1);
        Matcher matcher2 = NUMBER_SUFFIX_PATTERN.matcher(s2);

        if (matcher1.matches() && matcher2.matches()) {
            String prefix1 = matcher1.group(1);
            String prefix2 = matcher2.group(1);

            // 如果前缀相同，按数字大小比较
            if (prefix1.equals(prefix2)) {
                int num1 = Integer.parseInt(matcher1.group(2));
                int num2 = Integer.parseInt(matcher2.group(2));
                return Integer.compare(num1, num2);
            }
        }

        // 默认按字典序比较
        return s1.compareTo(s2);
    }

    public static void main(String[] args) {
        System.out.println("=== Testing Sorting Logic ===\n");

        // Test 1: Same prefix with numeric suffix
        System.out.println("Test 1: Same prefix with numeric suffix");
        testComparison("zzy1", "zzy2", "zzy1 < zzy2");
        testComparison("zzy2", "zzy10", "zzy2 < zzy10");
        testComparison("zzy9", "zzy10", "zzy9 < zzy10");
        testComparison("zzy10", "zzy1", "zzy10 > zzy1");
        System.out.println();

        // Test 2: Different prefix should sort lexicographically
        System.out.println("Test 2: Different prefix lexicographic sorting");
        testComparison("abc1", "zzy1", "abc1 < zzy1 (lexicographic)");
        testComparison("def10", "zzy1", "def10 < zzy1 (lexicographic)");
        testComparison("xyz2", "abc10", "xyz2 > abc10 (lexicographic)");
        System.out.println();

        // Test 3: Mixed scenarios
        System.out.println("Test 3: Mixed scenarios");
        testComparison("abc", "abc1", "abc < abc1");
        testComparison("abc1", "def", "abc1 < def");
        testComparison("def", "def1", "def < def1");
        System.out.println();

        // Test 4: NOT_GROUPED special handling
        System.out.println("Test 4: NOT_GROUPED special handling");
        testComparison("NOT_GROUPED", "abc", "NOT_GROUPED < abc");
        testComparison("NOT_GROUPED", "zzy1", "NOT_GROUPED < zzy1");
        testComparison("abc", "NOT_GROUPED", "abc > NOT_GROUPED");
        testComparison("NOT_GROUPED", "NOT_GROUPED", "NOT_GROUPED == NOT_GROUPED");
        System.out.println();

        // Test 5: Complete sorting example
        System.out.println("Test 5: Complete sorting example");
        List<String> testData = Arrays.asList(
            "zzy10", "abc", "zzy1", "def", "zzy2", "NOT_GROUPED", "abc10", "abc1", "zzy3"
        );

        TreeSet<String> sorted = new TreeSet<>(SortingLogicTest::compareWithNumericSuffix);
        sorted.addAll(testData);

        System.out.println("原始数据: " + testData);
        System.out.println("排序结果:");
        for (String s : sorted) {
            System.out.println("  " + s);
        }

        // Verify expected results
        String[] expected = {
            "NOT_GROUPED",  // Special handling, first
            "abc",          // Lexicographic
            "abc1",         // abc prefix, numeric suffix
            "abc10",        // abc prefix, numeric suffix
            "def",          // Lexicographic
            "zzy1",         // zzy prefix, numeric suffix
            "zzy2",         // zzy prefix, numeric suffix
            "zzy3",         // zzy prefix, numeric suffix
            "zzy10"         // zzy prefix, numeric suffix
        };

        String[] actual = sorted.toArray(new String[0]);
        boolean isCorrect = Arrays.equals(expected, actual);
        System.out.println("\nExpected: " + Arrays.toString(expected));
        System.out.println("Actual: " + Arrays.toString(actual));
        System.out.println("Correct: " + isCorrect);

        if (!isCorrect) {
            System.out.println("\nX Sorting result does not match expected!");
        } else {
            System.out.println("\nV Sorting result matches expected!");
        }

        // Test 6: Edge cases
        System.out.println("\nTest 6: Edge cases");
        testComparison("", "a", "empty < a");
        testComparison("a", "b", "a < b");
        testComparison("1", "2", "1 < 2 (pure numbers lexicographic)");
        testComparison("2", "10", "2 > 10 (pure numbers lexicographic, no prefix)");
    }

    private static void testComparison(String s1, String s2, String description) {
        int result = compareWithNumericSuffix(s1, s2);
        String symbol;
        if (result < 0) {
            symbol = "<";
        } else if (result > 0) {
            symbol = ">";
        } else {
            symbol = "==";
        }
        System.out.println(String.format("  %s %s %s (%s) OK", s1, symbol, s2, description));
    }
}
