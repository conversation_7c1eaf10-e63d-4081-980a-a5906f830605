/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.impl;

import static org.junit.Assert.assertTrue;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.bsp.as.util.PagingIterator;
import com.huawei.bsp.exception.OSSException;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.dvanalysisengineextservice.BaseTest;
import com.huawei.i2000.dvanalysisengineextservice.business.intent.AssociateTypeEnum;
import com.huawei.i2000.dvanalysisengineextservice.business.intent.RecommendHelper;
import com.huawei.i2000.dvanalysisengineextservice.business.intent.TemplatePotentialMatch;
import com.huawei.i2000.dvanalysisengineextservice.business.intent.TemplateRecognizer;
import com.huawei.i2000.dvanalysisengineextservice.model.RecommendReq;
import com.huawei.i2000.dvanalysisengineextservice.model.RecommendRsp;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.oms.eam.mo.ManagedObject;
import com.huawei.oms.persistence.Criterion;

import mockit.Mock;
import mockit.MockUp;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Random;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/4/16
 */
public class KeywordAssociationDelegateImplTest extends BaseTest {
    @Autowired
    private KeywordAssociationDelegateImpl delegate;

    private static final Map<String, String> CHINESE_TEMPLATES_MAP = new HashMap<>();

    static {
        CHINESE_TEMPLATES_MAP.put("QUERY_GUIDE.template", "查询{QUERY_NAME}");
        CHINESE_TEMPLATES_MAP.put("QUERY_GUIDE.regex", "查询\\s*(.+)");

        CHINESE_TEMPLATES_MAP.put("QUERY_NE_CONNECTION_STATUS.template", "查询网元{NE_NAME}的连接状态");
        CHINESE_TEMPLATES_MAP.put("QUERY_NE_CONNECTION_STATUS.regex", "查询网元\\s*(.*?)\\s*(?:的连接状态|$)");

        CHINESE_TEMPLATES_MAP.put("QUERY_NE_HEALTH_STATUS.template", "查询网元{NE_NAME}的健康状态");
        CHINESE_TEMPLATES_MAP.put("QUERY_NE_HEALTH_STATUS.regex", "查询网元\\s*(.*?)\\s*(?:的健康状态|$)");

        CHINESE_TEMPLATES_MAP.put("QUERY_NE_TOPOLOGY.template", "查询网元{NE_NAME}的拓扑信息");
        CHINESE_TEMPLATES_MAP.put("QUERY_NE_TOPOLOGY.regex", "查询网元\\s*(.*?)\\s*(?:的拓扑信息|$)");

        CHINESE_TEMPLATES_MAP.put("QUERY_ALARM_DETAIL.template", "查询告警{ALARM_ID_OR_NAME}");
        CHINESE_TEMPLATES_MAP.put("QUERY_ALARM_DETAIL.regex", "查询告警\\s*(.+)");

        CHINESE_TEMPLATES_MAP.put("EXPORT_INFO.template", "导出{EXPORT_NAME}");
        CHINESE_TEMPLATES_MAP.put("EXPORT_INFO.regex", "导出\\s*(.+)");

        CHINESE_TEMPLATES_MAP.put("QUERY_NE_METRIC_DATA.template",
            "查询网元{NE_NAME}任务{MEASURE_TASK_NAME}测量对象{MEASURE_OBJECT_NAME}测量指标{MEASURE_METRIC_NAME}时间区间{MEASURE_TIME_RANGE}内的数据");
        CHINESE_TEMPLATES_MAP.put("QUERY_NE_METRIC_DATA.regex",
            "查询网元\\s*(.*?)\\s*(?:任务\\s*(.*?)\\s*(?:测量对象\\s*(.*?)\\s*(?:测量指标\\s*(.*?)\\s*(?:时间区间\\s*(.*?)\\s*(?:内的数据|$)|$)|$)|$)|$)");

        CHINESE_TEMPLATES_MAP.put("EXPORT_NE_METRIC_DATA.template",
            "导出网元{NE_NAME}任务{MEASURE_TASK_NAME}测量对象{MEASURE_OBJECT_NAME}测量指标{MEASURE_METRIC_NAME}时间区间{MEASURE_TIME_RANGE}内的数据");
        CHINESE_TEMPLATES_MAP.put("EXPORT_NE_METRIC_DATA.regex",
            "导出网元\\s*(.*?)\\s*(?:任务\\s*(.*?)\\s*(?:测量对象\\s*(.*?)\\s*(?:测量指标\\s*(.*?)\\s*(?:时间区间\\s*(.*?)\\s*(?:内的数据|$)|$)|$)|$)|$)");

        CHINESE_TEMPLATES_MAP.put("DIAGNOSE_ALARM.template",
            "请诊断{NE_NAME}网元的告警{ALARM_ID_OR_NAME}的故障原因是什么？");
        CHINESE_TEMPLATES_MAP.put("DIAGNOSE_ALARM.regex",
            "请?诊断\\s*(.*?)\\s*(?:网元的告警\\s*(.*?)\\s*(?:的故障原因是什么\\??|$)|$)");

        CHINESE_TEMPLATES_MAP.put("QUERY_ERROR_CODE.template", "错误码{ERROR_CODE}");
        CHINESE_TEMPLATES_MAP.put("QUERY_ERROR_CODE.regex", "错误码\\s*【?(\\d+)】?");

        // Add bracket configuration for Chinese localization
        CHINESE_TEMPLATES_MAP.put("recommend.bracket.left", "【");
        CHINESE_TEMPLATES_MAP.put("recommend.bracket.right", "】");
    }

    @Test
    public void test001() throws Exception {
        String neName = generateRandomString(10);

        String input = "导出网元" + neName;
        String intent = "EXPORT_NE_METRIC_DATA";
        String recommend = "导出网元" + neName
            + "任务{MEASURE_TASK_NAME}测量对象{MEASURE_OBJECT_NAME}测量指标{MEASURE_METRIC_NAME}时间区间{MEASURE_TIME_RANGE}内的数据";

        testRecognize(input, intent);
        testRecommend(input, recommend);
    }

    @Test
    public void test002() throws Exception {
        String neName = generateRandomString(10);

        String input = "查询网元" + neName;
        String intent = "QUERY_NE_TOPOLOGY";
        String recommend = "查询网元" + neName + "的拓扑信息";

        testRecognize(input, intent);
        testRecommend(input, recommend);
    }

    @Test
    public void test003() throws Exception {
        String alarmId = generateRandomString(10);

        String input = "查询告警" + alarmId;
        String intent = "QUERY_ALARM_DETAIL";
        String recommend = "查询告警" + alarmId;

        testRecognize(input, intent);
        testRecommend(input, recommend);
    }

    /**
     * Test for Chinese placeholder preservation issue
     * User input: "请诊断【sa】网元的告警【告警ID/名称】的故障原因是什么？"
     * Expected: Recommendations should preserve "【告警ID/名称】" instead of showing "{ALARM_ID_OR_NAME}"
     */
    @Test
    public void testChinesePlaceholderPreservation() throws Exception {
        // Load mock scenario files for REST API calls
        mockServer.loadScenarioFiles("rest_mappings-chinese-placeholder-test.json");

        mockResourceUtil();
        mockMITManagerClient();
        // Don't mock RecommendHelper to test the actual logic

        String input = "请诊断【sa】网元的告警【告警ID/名称】的故障原因是什么？";

        // Given
        RecommendReq req = new RecommendReq();
        req.setUserQuestion(input);
        RecommendRsp rsp = delegate.recommend(new MockHttpContext(), req);

        rsp.getRecommendQuestions().forEach(System.out::println);

        // Then - verify that recommendations preserve Chinese placeholders
        boolean hasChinesePlaceholder = rsp.getRecommendQuestions().stream()
            .anyMatch(question -> question.contains("【告警ID/名称】"));

        boolean hasEnglishPlaceholder = rsp.getRecommendQuestions().stream()
            .anyMatch(question -> question.contains("{ALARM_ID_OR_NAME}"));

        assertTrue("Recommendations should preserve Chinese placeholder 【告警ID/名称】", hasChinesePlaceholder);
        assertTrue("Recommendations should not contain English placeholder {ALARM_ID_OR_NAME}", !hasEnglishPlaceholder);
    }

    public void testRecognize(String input, String intent) throws Exception {
        mockResourceUtil();

        // Given
        TemplateRecognizer templateRecognizer = TemplateRecognizer.getInstance(Locale.ROOT);
        List<TemplatePotentialMatch> potentialMatches = templateRecognizer.recognize(input, 5, 0.2);

        potentialMatches.forEach(System.out::println);

        // Then
        assertTrue(potentialMatches.stream().anyMatch(match -> match.getIntentName().equals(intent)));
    }

    public void testRecommend(String input, String recommend) throws Exception {
        mockResourceUtil();
        mockRecommendHelper();

        // Given
        RecommendReq req = new RecommendReq();
        req.setUserQuestion(input);
        RecommendRsp rsp = delegate.recommend(new MockHttpContext(), req);

        rsp.getRecommendQuestions().forEach(System.out::println);

        // Then
        assertTrue(rsp.getRecommendQuestions().stream().anyMatch(match -> match.equals(recommend)));
    }

    private void mockRecommendHelper() {
        new MockUp<RecommendHelper>() {
            @Mock
            public List<String> getElementRecommendList(String input, AssociateTypeEnum associateType, int maxSize) {
                return Collections.singletonList(input);
            }
        };
    }

    private void mockMITManagerClient() {
        new MockUp<MITManagerClient>() {
            @Mock
            public PagingIterator<ManagedObject> bulkGet(Criterion criterion, int pageSize, PagingIterator<ManagedObject> iterator) throws OSSException {
                // Return empty result to simulate no matching network elements
                // This will cause the method to return the original input, which is what we want for testing
                return new PagingIterator<ManagedObject>() {
                    @Override
                    public boolean hasNext() {
                        return false;
                    }

                    @Override
                    public List<ManagedObject> getResult() {
                        return Collections.emptyList();
                    }

                    @Override
                    public int getPageSize() {
                        return 0;
                    }

                    @Override
                    public int from() {
                        return 0;
                    }

                    @Override
                    public int to() {
                        return 0;
                    }
                };
            }
        };
    }

    private void mockResourceUtil() {
        new MockUp<ResourceUtil>() {
            @Mock
            public String getMessage(String key, Locale locale) {
                return CHINESE_TEMPLATES_MAP.get(key);
            }
        };
    }

    public static String generateRandomString(int length) {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        StringBuilder sb = new StringBuilder(length);
        Random random = new Random();
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(chars.length());
            sb.append(chars.charAt(index));
        }
        return sb.toString();
    }
}