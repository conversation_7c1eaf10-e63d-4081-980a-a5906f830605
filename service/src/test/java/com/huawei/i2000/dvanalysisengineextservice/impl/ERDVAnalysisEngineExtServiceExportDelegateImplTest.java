/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.impl;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvanalysisengineextservice.BaseTest;
import com.huawei.i2000.dvanalysisengineextservice.delegate.ERDVAnalysisEngineExtServiceExportDelegate;
import com.huawei.i2000.dvanalysisengineextservice.model.PmKpiQueryModel;
import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineextservice.util.resource.EamUtil;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.ManagedObject;
import com.huawei.oms.eam.mo.UniAgent;

import mockit.Mock;
import mockit.MockUp;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025-02-11
 */
public class ERDVAnalysisEngineExtServiceExportDelegateImplTest extends BaseTest {
    @Autowired
    private ERDVAnalysisEngineExtServiceExportDelegate delegate;

    @Test
    public void exportPmKpi() throws ServiceException {
        mockEamUtil();
        mockServer.loadScenarioFiles("rest_mappings-export.json");
        PmKpiQueryModel model = new PmKpiQueryModel();
        model.setNeName("10.10.00");
        model.setTimestamp(new BigDecimal(1738824875000L));
        model.setTaskId("JVMMemory");
        ResponseResult result = delegate.exportPmKpi(new MockHttpContext(), "10.10.00", "JVMMemory", null, null, null, null);
        Assert.assertNotNull(result.getResultMessage());
    }

    private void mockEamUtil() {
        new MockUp<EamUtil>() {
            @Mock
            public ManagedObject getMoByNeName(String neName) {
                ManagedObject managedObject = new UniAgent();
                managedObject.setType("inf.os.linux");
                managedObject.setDN(new DN("OS=1"));
                return managedObject;
            }
        };
    }
}