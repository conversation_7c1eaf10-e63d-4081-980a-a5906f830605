/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.model.Business;
import com.huawei.i2000.dvtoposervice.model.BusinessIndicator;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.GroupOfMo;
import com.huawei.i2000.dvtoposervice.model.IndicatorDisplayResults;
import com.huawei.i2000.dvtoposervice.model.IndicatorDistribution;
import com.huawei.i2000.dvtoposervice.model.OverViewGrid;
import com.huawei.i2000.dvtoposervice.model.OverViewIndicatorParam;
import com.huawei.i2000.dvtoposervice.model.OverviewGridParam;
import com.huawei.i2000.dvtoposervice.model.Site;
import com.huawei.i2000.dvtoposervice.model.SiteDistribution;
import com.huawei.i2000.dvtoposervice.model.SiteTPSValue;
import com.huawei.i2000.dvtoposervice.model.SiteTeam;
import com.huawei.i2000.dvtoposervice.model.Solution;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.Host;
import com.huawei.oms.eam.mo.ManagedObject;

import org.apache.commons.collections4.CollectionUtils;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * BusinessTopoOverViewServiceImplTest class
 *
 * <AUTHOR>
 * @since 2024/2/29
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, MITManagerClient.class, BusinessTopoOverViewServiceImpl.class, System.class, AuthUtils.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class BusinessTopoOverViewServiceImplTest extends WebServiceTest {

    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    private static final String INDICATOR_RANGE = "indicatorrange";

    private static final String DISTRIBUTE_INDICATOR_RANGE = "distributeindicatorrange";

    private static final String AGGREGATE_TYPE = "aggregatetype";

    @Autowired
    BusinessTopoOverViewServiceImpl serviceImpl;

    @Rule
    public ExpectedException exceptionRule = ExpectedException.none();

    @Mock
    MITManagerClient client;

    private void mockProperties() {
        MapOper<ConfigData> businessConfigMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("indicatorrange");
        configData.setValue("691200000");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "indicatorrange", configData);
        ConfigData configData1 = new ConfigData();
        configData1.setConfigItemName("distributeindicatorrange");
        configData1.setValue("3600000");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "distributeindicatorrange", configData1);
        ConfigData configData2 = new ConfigData();
        configData2.setConfigItemName("timelinerange");
        configData2.setValue("24");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "timelinerange", configData2);
        ConfigData configData3 = new ConfigData();
        configData3.setConfigItemName("isOpenDatabaseView");
        configData3.setValue("true");
        businessConfigMapOper.put(RedisConstant.BUSINESS_TOPO_CONFIG, "isOpenDatabaseView", configData3);
    }

    private void mockAuthentication() {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
    }

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_overview.sql", false);
        mockProperties();
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void testQuerySolution() {
        try {
            mockAuthDns();
            List<Solution> solutionList = serviceImpl.querySolution(0L);
            Assert.assertTrue(CollectionUtils.isNotEmpty(solutionList));
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test
    public void testCommonQuerySolution() {
        try {
            mockCommonAuthDns();
            List<Solution> solutionList = serviceImpl.querySolution(0L);
            Assert.assertTrue(CollectionUtils.isNotEmpty(solutionList));
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test
    public void tesHistoryQuerySolution() {
        try {
            mockAuthDns();
            List<Solution> solutionList = serviceImpl.querySolution(1721641800000L);
            Assert.assertTrue(CollectionUtils.isNotEmpty(solutionList));
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test
    public void testQueryBusinessList() {
        try {
            mockAuthDns();
            List<Business> businessList = serviceImpl.queryBusinessList(10001, 0L);
            Assert.assertTrue(CollectionUtils.isNotEmpty(businessList));
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test
    public void testHistoryQueryBusinessList() {
        try {
            mockAuthDns();
            List<Business> businessList = serviceImpl.queryBusinessList(10001, 1721641800000L);
            Assert.assertTrue(CollectionUtils.isNotEmpty(businessList));
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test
    public void testQueryIndicatorResults() throws ServiceException {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");
        mockIndicatorPrepare();
        mockAuthentication();

        OverViewIndicatorParam param = new OverViewIndicatorParam();
        param.setBusinessId(10011);
        param.setIndicatorId("OS=1I2K_OSIOWait7.220.172.201");
        param.setSiteId(10031);
        IndicatorDisplayResults results = serviceImpl.queryIndicatorResults(param);
        Assert.assertTrue(CollectionUtils.isNotEmpty(results.getSiteHistoryList()));
        Assert.assertNotNull(results.getSiteHistoryList().get(0).getHistoryTotalCount());
    }

    @Test
    public void testQueryIndicatorAggrResults() throws ServiceException {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");
        mockIndicatorPrepare();
        mockAuthentication();

        OverViewIndicatorParam param = new OverViewIndicatorParam();
        param.setBusinessId(10012);
        param.setIndicatorId("OS=1I2K_DBtablespaceSizeAllocatedDVTOPOSERVICEDB");
        IndicatorDisplayResults results = serviceImpl.queryIndicatorResults(param);
        Assert.assertEquals(10012, (int) results.getBusinessId());
    }

    @Test
    public void testQueryIndicatorResultsNotAdminTest() throws ServiceException {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");
        mockIndicatorPrepare();
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(false);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        Set<String> dns = new HashSet<>();
        dns.add("OS=1");
        PowerMockito.mockStatic(AuthUtils.class);
        PowerMockito.when(AuthUtils.getAuthDns()).thenReturn(dns);

        OverViewIndicatorParam param = new OverViewIndicatorParam();
        param.setBusinessId(10011);
        param.setIndicatorId("OS=1I2K_OSIOWait7.220.172.201");
        param.setSiteId(10031);
        IndicatorDisplayResults results = serviceImpl.queryIndicatorResults(param);
        Assert.assertTrue(CollectionUtils.isNotEmpty(results.getSiteHistoryList()));
        Assert.assertNotNull(results.getSiteHistoryList().get(0).getHistoryTotalCount());
    }

    @Test
    public void testQueryOverViewDistribution() {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");
        mockAuthentication();
        try {
            mockIndicatorPrepare();
            mockMITManger();
            PowerMockito.mockStatic(System.class);
            PowerMockito.when(System.currentTimeMillis()).thenReturn(1721641800000L);
            IndicatorDistribution param = new IndicatorDistribution();
            param.setInstanceId(10011);
            param.setCurrentTime(System.currentTimeMillis());
            SiteDistribution results = serviceImpl.queryOverViewDistribution(param);
            Assert.assertNotNull(results);
            Assert.assertTrue(CollectionUtils.isEmpty(results.getSiteDataList()));
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test
    public void testQueryOverViewDistributionNotAdmin() {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("OS=1");
        contextUtils.setAdmin(false);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        try {
            mockIndicatorPrepare();
            mockMITManger();
            PowerMockito.mockStatic(System.class);
            PowerMockito.when(System.currentTimeMillis()).thenReturn(1717727787606L);
            IndicatorDistribution param = new IndicatorDistribution();
            param.setInstanceId(10011);
            param.setCurrentTime(System.currentTimeMillis());
            SiteDistribution results = serviceImpl.queryOverViewDistribution(param);
            Assert.assertNotNull(results);
            Assert.assertTrue(CollectionUtils.isEmpty(results.getSiteDataList()));
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test
    public void testQueryOverviewGrid() {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(false);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        Set<String> dns = new HashSet<>();
        dns.add("OS=1");
        PowerMockito.mockStatic(AuthUtils.class);
        PowerMockito.when(AuthUtils.getAuthDns()).thenReturn(dns);

        OverviewGridParam param = new OverviewGridParam();
        param.setSolutionId(10001);
        param.setTimestamp(0L);
        OverViewGrid overViewGrid = serviceImpl.queryOverviewGrid(param);
        Assert.assertTrue(CollectionUtils.isNotEmpty(overViewGrid.getBusinessList()));
        Assert.assertTrue(CollectionUtils.isNotEmpty(overViewGrid.getSiteTeamList()));
        List<Site> siteList = overViewGrid.getSiteTeamList().get(0).getSiteList();
        Assert.assertTrue(CollectionUtils.isNotEmpty(siteList));
        List<GroupOfMo> groupOfMoList = siteList.get(0).getGroupList();
        Assert.assertTrue(CollectionUtils.isNotEmpty(groupOfMoList));
    }

    @Test
    public void testOverViewGridSiteTeamSorted() {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = new ContextUtils();
        Set<String> set = new HashSet<>();
        set.add("OS=1");
        contextUtils.setAdmin(true);
        contextUtils.setAuthDns(set);
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);

        OverviewGridParam param = new OverviewGridParam();
        param.setSolutionId(10001);
        param.setTimestamp(0L);
        OverViewGrid overViewGrid = serviceImpl.queryOverviewGrid(param);
        Assert.assertTrue(CollectionUtils.isNotEmpty(overViewGrid.getSiteTeamList()));
        List<Site> siteList = overViewGrid.getSiteTeamList().get(0).getSiteList();
        Assert.assertTrue(CollectionUtils.isNotEmpty(siteList));
        SiteTeam one = overViewGrid.getSiteTeamList().get(0);
        Assert.assertEquals(10021, (int) one.getSiteTeamId());
    }

    private void mockAuthDns() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        contextUtils.setAdmin(true);
        Mockito.when(contextUtils.getAuthDns()).thenReturn(new HashSet<>(Collections.singletonList("OS=1")));
        Mockito.when(contextUtils.getUserId()).thenReturn("user_01");
    }

    private void mockCommonAuthDns() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        contextUtils.setAdmin(false);
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getUserId()).thenReturn("user_01");
        Set<String> dns = new HashSet<>();
        dns.add("OS=1");
        PowerMockito.mockStatic(AuthUtils.class);
        PowerMockito.when(AuthUtils.getAuthDns()).thenReturn(dns);
    }

    private void mockIndicatorPrepare() {
        PowerMockito.mockStatic(PropertiesUtil.class);
        Properties properties = new Properties();
        properties.put(INDICATOR_RANGE, "86400000");
        properties.put(DISTRIBUTE_INDICATOR_RANGE, "86400000");
        properties.put(AGGREGATE_TYPE, "1");
        PowerMockito.when(PropertiesUtil.loadProperties(Mockito.anyString())).thenReturn(properties);
    }

    private void mockMITManger() throws Exception {
        // mock资源查网元和代理的方法
        List<ManagedObject> mos = new ArrayList<>();
        ManagedObject mo = new Host();
        mo.setDN(new DN("OS=1"));
        mo.setType("OMS");
        mo.putClientProperty("solutionId", "site-1");
        mos.add(mo);
        PowerMockito.mockStatic(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(client);
        PowerMockito.doReturn(mos).when(client, "getMOList", Mockito.any());
    }

    @Test
    public void testIsDnMatchedToSite() {
        Map<String, String> indMapSite = new HashMap<>();
        indMapSite.put("1", "Dn1");
        BusinessIndicator siteInd = new BusinessIndicator();
        siteInd.setDn("Dn1");
        BusinessInstanceModelDB selectSiteIns = new BusinessInstanceModelDB();
        BusinessInsExtentAttrDB siteInsExtent = new BusinessInsExtentAttrDB();
        siteInsExtent.setAttrName("siteId");
        siteInsExtent.setAttrValue("1");
        selectSiteIns.getAttrDBList().add(siteInsExtent);
        boolean matchedResult = serviceImpl.isDnMatchedToSite(indMapSite, siteInd, selectSiteIns);
        Assert.assertTrue(matchedResult);

        boolean matchedResultAll = serviceImpl.isDnMatchedToSite(indMapSite, siteInd, null);
        Assert.assertTrue(matchedResultAll);

        boolean nonValue = serviceImpl.isMatchedToSite(null, siteInd, null ,null, null);
        Assert.assertFalse(nonValue);
    }

    @Test
    public void testQuerySolutionNullContext() {
        try {
            List<Solution> solutionList = serviceImpl.querySolution(0L);
            Assert.assertTrue(CollectionUtils.isEmpty(solutionList));
        } catch (Exception e) {
            Assert.fail();
        }
    }

    @Test
    public void renameSiteNameTest() {
        List<SiteTPSValue> tpsValues = new ArrayList<>();
        SiteTPSValue value1 = new SiteTPSValue();
        value1.setSiteName("name1");
        tpsValues.add(value1);
        Set<Integer> siteInsIds = new HashSet<>();
        siteInsIds.add(1);

        serviceImpl.renameSiteName(tpsValues, siteInsIds);

        Assert.assertEquals("sitename1", tpsValues.get(0).getSiteName());
    }
}
