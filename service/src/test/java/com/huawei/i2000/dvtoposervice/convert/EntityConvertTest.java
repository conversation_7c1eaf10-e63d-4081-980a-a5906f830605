/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.convert;

import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.i2000.dvtoposervice.WebServiceTest;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.NonLeafModel;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModelExtentAttr;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB;
import com.huawei.i2000.dvtoposervice.business.pm.PmIndicatorInstanceService;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.GroupOfMo;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.PropertiesUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * GroupOfMoConvertTest
 *
 * <AUTHOR>
 * @since 2024-8-14
 */
@PrepareForTest( {ContextUtils.class, PropertiesUtil.class, MITManagerClient.class, PmIndicatorInstanceService.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class EntityConvertTest extends WebServiceTest {
    private static final AtomicBoolean DATA_LOAD = new AtomicBoolean(false);

    private static final MapOper<ConfigData> BUSINESS_CONFIG_MAP_OPER = new MapOper<>(
        RedisConstant.BUSINESS_TOPO_CONFIG, RedisConstant.REDIS_NAME);

    private static EmbeddedRestClientAndServer mockServer;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATA_LOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvtoposervice_tables_zenith_import.sql", false);
    }

    @Test
    public void groupConvertTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("dvHealthScoreThreshold");
        configData.setValue("10.0");
        BUSINESS_CONFIG_MAP_OPER.put(RedisConstant.BUSINESS_TOPO_CONFIG, configData.getConfigItemName(), configData);

        NonLeafModel nonLeafModel = new NonLeafModel();
        nonLeafModel.setInstanceId(1);
        nonLeafModel.setModelId("4_DigitalView");
        List<BusinessCommonModelExtentAttr> staticAttrDBList = new ArrayList<>();
        BusinessCommonModelExtentAttr extentAttr = new BusinessCommonModelExtentAttr();
        extentAttr.setStaticAttrName("groupName");
        extentAttr.setStaticAttrValue("DigitalView");
        staticAttrDBList.add(extentAttr);
        nonLeafModel.setStaticAttrDBList(staticAttrDBList);
        List<BusinessInsExtentAttrDB> attrDBList = new ArrayList<>();
        BusinessInsExtentAttrDB attrDB = new BusinessInsExtentAttrDB();
        attrDB.setAttrName("nodeIp");
        attrDB.setAttrValue("*********");
        attrDBList.add(attrDB);
        nonLeafModel.setAttrDBList(attrDBList);
        BusinessInsExtentAttrDB attrDBInstance = new BusinessInsExtentAttrDB();
        attrDBInstance.setAttrName("hasInstance");
        attrDBInstance.setAttrValue("1");
        attrDBList.add(attrDBInstance);
        BusinessInsExtentAttrDB attrDBActive = new BusinessInsExtentAttrDB();
        attrDBActive.setAttrName("isActiveDv");
        attrDBActive.setAttrValue("1");
        attrDBList.add(attrDBActive);
        BusinessInsExtentAttrDB attrDBHealth = new BusinessInsExtentAttrDB();
        attrDBHealth.setAttrName("healthScore");
        attrDBHealth.setAttrValue("10.0");
        attrDBList.add(attrDBHealth);
        nonLeafModel.setAttrDBList(attrDBList);

        GroupOfMoConvert convert = new GroupOfMoConvert();
        GroupOfMo result = convert.convert(nonLeafModel);
        Assert.assertEquals(1, (int) result.getIsActiveDv());
    }
}
