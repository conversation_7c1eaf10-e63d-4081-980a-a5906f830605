/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.impl;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.time.Instant;

import static org.junit.Assert.assertEquals;
import com.huawei.i2000.dvaiagentservice.cluster.IncidentManage;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.CALLS_REAL_METHODS;

/**
 * 功能描述
 * 单元测试
 *
 * <AUTHOR>
 * @since 2025/5/10
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({IncidentManage.class, Instant.class})
public class IncidentManageTest {

    private static final long TEST_TIMESTAMP = 1633072800000L; // 用于测试的固定时间戳示例

    private IncidentManage incidentManage;

    @Before
    public void setUp() {
        // 模拟当前时间为固定值，以确保测试结果的一致性
        PowerMockito.mockStatic(Instant.class, CALLS_REAL_METHODS);
        PowerMockito.when(Instant.now()).thenReturn(Instant.ofEpochMilli(TEST_TIMESTAMP));
        incidentManage = new IncidentManage();
    }

    /**
     * 描述：测试将有效的逗号分隔的字符串转换为Long列表
     * <p>
     * 给定：一个有效的逗号分隔的字符串 "1,2,3,4,5"
     * 当：调用convertStringToList方法时
     * 那么：应该返回一个包含Long类型元素的列表 [1, 2, 3, 4, 5]
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testConvertStringToListWhenValidInputThenCorrectList() throws Exception {
        String input = "1,2,3,4,5";
        List<Long> expected = Arrays.asList(1L, 2L, 3L, 4L, 5L);
        List<Long> result = IncidentManage.convertStringToList(input);
        assertEquals(expected, result);
    }

    /**
     * 描述：测试将空字符串转换为Long列表
     * <p>
     * 给定：一个空字符串 ""
     * 当：调用convertStringToList方法时
     * 那么：应该返回一个空列表
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testConvertStringToListWhenEmptyInputThenEmptyList() throws Exception {
        String input = "";
        List<Long> expected = Collections.emptyList();
        List<Long> result = IncidentManage.convertStringToList(input);
        assertEquals(expected, result);
    }

    /**
     * 描述：测试将包含前导和尾随空格的字符串转换为Long列表
     * <p>
     * 给定：一个包含前导和尾随空格的字符串 " 1, 2, 3 "
     * 当：调用convertStringToList方法时
     * 那么：应该返回一个包含Long类型元素的列表 [1, 2, 3]
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testConvertStringToListWhenSpacesInputThenCorrectList() throws Exception {
        String input = "1,2,3";
        List<Long> expected = Arrays.asList(1L, 2L, 3L);
        List<Long> result = IncidentManage.convertStringToList(input);
        assertEquals(expected, result);
    }

    /**
     * 描述：测试将包含非数字值的字符串转换为Long列表
     * <p>
     * 给定：一个包含非数字值的字符串 "1,2,three,4"
     * 当：调用convertStringToList方法时
     * 那么：应该抛出NumberFormatException异常
     *
     * @throws Exception 服务异常
     */
    @Test(expected = NumberFormatException.class)
    public void testConvertStringToListWhenNonNumericInputThenException() throws Exception {
        String input = "1,2,three,4";
        IncidentManage.convertStringToList(input);
    }

    /**
     * 描述：测试将null输入转换为Long列表
     * <p>
     * 给定：一个null输入
     * 当：调用convertStringToList方法时
     * 那么：应该抛出NullPointerException异常
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testConvertStringToListWhenNullInputThenException() throws Exception {
        String input = null;
        List<Long> expected = Collections.emptyList();
        List<Long> result = IncidentManage.convertStringToList(input);
        assertEquals(expected, result);
    }

    /**
     * 描述：测试将空列表转换为字符串
     * <p>
     * 给定：一个空的Long列表
     * 当：调用convertListToString方法
     * 那么：应返回一个空字符串
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testConvertListToStringWhenEmptyListThenEmptyString() throws Exception {
        List<Long> emptyList = Collections.emptyList();
        String result = IncidentManage.convertListToString(emptyList);
        assertEquals("", result);
    }

    /**
     * 描述：测试将包含单个元素的列表转换为字符串
     * <p>
     * 给定：一个包含单个元素的Long列表
     * 当：调用convertListToString方法
     * 那么：应返回包含单个元素的字符串
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testConvertListToStringWhenSingleElement() throws Exception {
        List<Long> singleElementList = Collections.singletonList(1L);
        String result = IncidentManage.convertListToString(singleElementList);
        assertEquals("1", result);
    }

    /**
     * 描述：测试将包含多个元素的列表转换为字符串
     * <p>
     * 给定：一个包含多个元素的Long列表
     * 当：调用convertListToString方法
     * 那么：应返回由逗号分隔的元素组成的字符串
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testConvertListToStringWhenMultiple() throws Exception {
        List<Long> multipleElementsList = Arrays.asList(1L, 2L, 3L);
        String result = IncidentManage.convertListToString(multipleElementsList);
        assertEquals("1,2,3", result);
    }

    /**
     * 描述：测试将包含null元素的列表转换为字符串
     * <p>
     * 给定：一个包含null元素的Long列表
     * 当：调用convertListToString方法
     * 那么：应返回包含"null"值的逗号分隔的字符串
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testConvertListToStringWhenNullElementsList() throws Exception {
        List<Long> nullElementsList = Arrays.asList(1L, null, 3L);
        String result = IncidentManage.convertListToString(nullElementsList);
        assertEquals("1,null,3", result);
    }

    /**
     * 描述：测试将null列表转换为字符串
     * <p>
     * 给定：一个null的Long列表
     * 当：调用convertListToString方法
     * 那么：应抛出NullPointerException
     *
     * @throws Exception 服务异常
     */
    @Test(expected = NullPointerException.class)
    public void testConvertListToStringWhenNullListThenNull() throws Exception {
        List<Long> nullList = null;
        IncidentManage.convertListToString(nullList);
    }

    /**
     * 描述：测试方法在num为24且unit为"hour"时的情况
     *
     * 给定：num = 24, unit = "hour"
     * 当：调用getOccurTimeTimestamp方法
     * 那么：返回的时间戳应该是当前时间戳前24小时
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testGetOccurTimeTimestampWhenNumIsTwentyFourAndUnitIs() throws Exception {
        long expected = TEST_TIMESTAMP - (24 * 60 * 60 * 1000);
        long result = IncidentManage.getOccurTimeTimestamp(24, "hour");
        assertEquals(expected, result);
    }

    /**
     * 描述：测试方法在num为1且unit为"day"时的情况
     *
     * 给定：num = 1, unit = "day"
     * 当：调用getOccurTimeTimestamp方法
     * 那么：返回的时间戳应该是当前时间戳前1天
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testGetOccurTimeTimestampWhenNumIsOne() throws Exception {
        long expected = TEST_TIMESTAMP - (1 * 24 * 60 * 60 * 1000);
        long result = IncidentManage.getOccurTimeTimestamp(1, "day");
        assertEquals(expected, result);
    }

    /**
     * 描述：测试方法在num为0且unit为"day"时的情况
     *
     * 给定：num = 0, unit = "day"
     * 当：调用getOccurTimeTimestamp方法
     * 那么：返回的时间戳应该是当前时间戳
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testGetOccurTimeTimestampWhenNumIsZeroAn() throws Exception {
        long result = IncidentManage.getOccurTimeTimestamp(0, "day");
        assertEquals(TEST_TIMESTAMP, result);
    }

    /**
     * 描述：测试方法在num为负数时的情况
     *
     * 给定：num = -1, unit = "hour"
     * 当：调用getOccurTimeTimestamp方法
     * 那么：应该抛出IllegalArgumentException异常
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testGetOccurTimeTimestampWhenNumIsNegative() throws Exception {
        assertThrows(IllegalArgumentException.class, () -> IncidentManage.getOccurTimeTimestamp(-1, "hour"));
    }

    /**
     * 描述：测试方法在unit为null时的情况
     *
     * 给定：num = 1, unit = null
     * 当：调用getOccurTimeTimestamp方法
     * 那么：应该抛出IllegalArgumentException异常
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testGetOccurTimeTimestampWhenUnitIsNullThenn() throws Exception {
        assertThrows(IllegalArgumentException.class, () -> IncidentManage.getOccurTimeTimestamp(1, null));
    }

    /**
     * 描述：测试方法在unit为空字符串时的情况
     *
     * 给定：num = 1, unit = ""
     * 当：调用getOccurTimeTimestamp方法
     * 那么：应该抛出IllegalArgumentException异常
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testGetOccurTimeTimestampWhenUnitIsEmptyThenThr() throws Exception {
        assertThrows(IllegalArgumentException.class, () -> IncidentManage.getOccurTimeTimestamp(1, ""));
    }

    /**
     * 描述：测试方法在unit为无效值时的情况
     *
     * 给定：num = 1, unit = "minute"
     * 当：调用getOccurTimeTimestamp方法
     * 那么：应该抛出IllegalArgumentException异常
     *
     * @throws Exception 服务异常
     */
    @Test
    public void testGetOccurTimeTimestampWhenUnitIsInva() throws Exception {
        assertThrows(IllegalArgumentException.class, () -> IncidentManage.getOccurTimeTimestamp(1, "minute"));
    }
}
