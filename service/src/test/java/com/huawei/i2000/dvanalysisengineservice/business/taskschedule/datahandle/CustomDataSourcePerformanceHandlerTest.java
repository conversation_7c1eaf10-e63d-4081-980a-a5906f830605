/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle;

import com.huawei.i2000.dvanalysisengineservice.WebServiceTest;
import com.huawei.i2000.dvanalysisengineservice.business.common.EncryptionUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.HofsUtil;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.CustomDataSourcePerformanceHandleDto;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.CustomIndicatorData;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.DataSourceInfo;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.model.TaskIndicator;

import com.alibaba.fastjson2.JSONObject;

import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.test.context.ContextConfiguration;

import java.io.File;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@PrepareForTest({EncryptionUtil.class, HofsUtil.class, CustomDataSourcePerformanceHandler.class,})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class CustomDataSourcePerformanceHandlerTest extends WebServiceTest {
    private static final AtomicBoolean DATALOAD = new AtomicBoolean(false);

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATALOAD.compareAndSet(false, true)) {
            return;
        }
        setSqlScriptEncoding("UTF-8");
        executeSqlScript("classpath:database/dvanalysisengineservice_clear_all_table.sql", false);
        executeSqlScript("classpath:database/dvanalysisengineservice_datahandle_zenith.sql", false);
        PowerMockito.mockStatic(EncryptionUtil.class);
        PowerMockito.doNothing()
            .when(EncryptionUtil.class, "changeFilePermission", Mockito.anyString(), Mockito.anyString());
    }

    /**
     * changeFilePermission 方法无法在windows系统执行，需要mock掉
     *
     * @throws Exception Exception
     */
    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
    }

    /**
     * 分发自定义指标数据源任务并处理
     *
     * @throws Exception Exception
     */
    @Test
    public void testHandle() throws Exception {
        List<TaskIndicator> indicatorList = new ArrayList<>();
        TaskIndicator atest1 = new TaskIndicator();
        atest1.setIndicatorId("atest1");
        TaskIndicator atest2 = new TaskIndicator();
        atest2.setIndicatorId("atest2");
        TaskIndicator atest3 = new TaskIndicator();
        atest3.setIndicatorId("atest3");
        TaskIndicator atest4 = new TaskIndicator();
        atest4.setIndicatorId("atest4");
        TaskIndicator atest5 = new TaskIndicator();
        atest5.setIndicatorId("atest5");
        TaskIndicator atest6 = new TaskIndicator();
        atest6.setIndicatorId("atest6");
        TaskIndicator atest7 = new TaskIndicator();
        atest7.setIndicatorId("atest7");
        TaskIndicator atest8 = new TaskIndicator();
        atest8.setIndicatorId("atest8");
        TaskIndicator atest9 = new TaskIndicator();
        atest9.setIndicatorId("atest9");
        TaskIndicator atest10 = new TaskIndicator();
        atest10.setIndicatorId("atest10");
        indicatorList.add(atest1);
        indicatorList.add(atest2);
        indicatorList.add(atest3);
        indicatorList.add(atest4);
        indicatorList.add(atest5);
        indicatorList.add(atest6);
        indicatorList.add(atest7);
        indicatorList.add(atest8);
        indicatorList.add(atest9);
        indicatorList.add(atest10);
        CustomDataSourcePerformanceHandleDto custom = new CustomDataSourcePerformanceHandleDto(100, 1, "testTask", 1,
            1636008339000L, 5L, "TBL_AIOPS_CUSTOM_INDICATOR_DATA_1", indicatorList, 2, 10, "no");
        CustomDataSourcePerformanceHandler handler = PowerMockito.spy(new CustomDataSourcePerformanceHandler());
        PowerMockito.mockStatic(HofsUtil.class);
        PowerMockito.doReturn(true).when(HofsUtil.class, "uploadFile", Mockito.anyString(), Mockito.anyString());
        DataSourceInfo dataSourceInfo = handler.handle(custom);
        Assert.assertEquals(48, dataSourceInfo.getGridsNumber());
        new File(TaskConstant.TEMP_SRC_PATH + "1_1636008339000.csv").delete();
    }

    @Test
    public void testConvertDataToString() throws Exception {
        CustomIndicatorData customIndicatorData = new CustomIndicatorData();
        customIndicatorData.setIndicatorId("OS=1I2K_OSIOWait10.247.5.114");
        customIndicatorData.setIndicatorValue(45);
        customIndicatorData.setCollectTime(1636008339000L);
        CustomIndicatorData customIndicatorData1 = new CustomIndicatorData();
        customIndicatorData1.setIndicatorId("OS=1I2K_OSIOWait10.247.5.114");
        customIndicatorData1.setIndicatorValue(50);
        customIndicatorData1.setCollectTime(1636008349000L);
        List<CustomIndicatorData> customIndicatorDataList = new ArrayList<>();
        customIndicatorDataList.add(customIndicatorData);
        customIndicatorDataList.add(customIndicatorData1);

        List<TaskIndicator> indicatorList = new ArrayList<>();
        TaskIndicator test1 = new TaskIndicator();
        test1.setIndicatorId("OS=1I2K_OSIOWait10.247.5.114");
        indicatorList.add(test1);
        CustomDataSourcePerformanceHandleDto custom = new CustomDataSourcePerformanceHandleDto(100, 1, "testTask", 1,
            1636008339000L, 5L, "TBL_AIOPS_CUSTOM_INDICATOR_DATA_1", indicatorList, 1, 10, "yes");

        DataSourceInfo dataSourceInfo = new DataSourceInfo();
        JSONObject failureList = new JSONObject();
        Class<CustomDataSourcePerformanceHandler> aClass = CustomDataSourcePerformanceHandler.class;
        Method method = aClass.getDeclaredMethod("convertDataToString", List.class,
            CustomDataSourcePerformanceHandleDto.class, DataSourceInfo.class, JSONObject.class);
        method.setAccessible(true);

        CustomDataSourcePerformanceHandler handler = PowerMockito.spy(new CustomDataSourcePerformanceHandler());

        List<List<String>> result = (List<List<String>>) method.invoke(handler,customIndicatorDataList, custom, dataSourceInfo, failureList);

        Assert.assertEquals(2, result.size());
    }
}
