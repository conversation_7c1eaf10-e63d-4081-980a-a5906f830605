/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
package com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.taskresulthandler;

import com.huawei.bsp.deploy.util.DefaultEnvUtil;
import com.huawei.i2000.dvanalysisengineservice.WebServiceTest;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.TaskPeriodInfo;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.TaskExecutionResultDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskschedule.TaskScheduleDao;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.io.File;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * SingleIndicatorPredictSynAnalysisResultTest
 *
 * <AUTHOR>
 * @since 2023-09-23
 */
@PrepareForTest( {ContextUtils.class, SingleIndicatorPredictLargeResultHandler.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class SingleIndicatorPredictSynAnalysisResultTest extends WebServiceTest {

    private static final AtomicBoolean DATALOAD = new AtomicBoolean(false);

    @Autowired
    @InjectMocks
    SingleIndicatorPredictLargeResultHandler singleIndicatorPredictLargeResultHandler;

    @Autowired
    TaskExecutionResultDao taskExecutionResultDao;

    @Autowired
    TaskScheduleDao taskScheduleDao;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATALOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvanalysisengineservice_clear_all_table.sql", false);
        executeSqlScript("classpath:database/dvanalysisengineservice_single_indicator_predict_large_sys_result_zenith.sql",
            false);
    }

    /**
     * synAnalysisResultTest
     *
     * @throws Exception Exception
     */
    @Test
    public void synAnalysisLargeResultTest() throws Exception {
        String destFile = System.getProperty("user.dir")+"/target/test-classes/mockfile" + File.separator + "analysisdest" + File.separator
            + "2_202309231908_predicting.json";
        singleIndicatorPredictLargeResultHandler.synAnalysisResult(2, 1695311232000L,
            "predicting", destFile);
        TaskPeriodInfo taskPeriodInfo = taskScheduleDao.getRecentOneTaskResult(2);
        Assert.assertEquals("PartialFinished", taskPeriodInfo.getStatus());
    }

}
