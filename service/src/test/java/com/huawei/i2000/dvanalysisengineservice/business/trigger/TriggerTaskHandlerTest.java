/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.trigger;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.cbb.eam.cloudsop.ResourceGroupUtil;
import com.huawei.i2000.cbb.sm.UserRoleUtil;
import com.huawei.i2000.cbb.sm.model.RoleBase;
import com.huawei.i2000.cbb.sm.model.UserInfo;
import com.huawei.i2000.dvanalysisengineservice.WebServiceTest;
import com.huawei.i2000.dvanalysisengineservice.business.algorithmmanage.AlgorithmManageService;
import com.huawei.i2000.dvanalysisengineservice.business.common.ConfigurationUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.EncryptionUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.FileCleanUtils;
import com.huawei.i2000.dvanalysisengineservice.business.common.HofsUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.LocationTriggerTreeNodeCalculation;
import com.huawei.i2000.dvanalysisengineservice.business.common.RestUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.cache.TriggerCacheOper;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.AssociationAnalysisType;
import com.huawei.i2000.dvanalysisengineservice.business.common.pythoncmdexec.PyExecutor;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.model.flow.FlowExecuteStatus;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.model.flow.FlowTask;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.dashboard.dto.AssociationNodeTypeEnum;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.TaskImportUtils;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.TriggerAlgorithmManager;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.AlarmClient;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.HandleDtoFactory;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.LogmatrixClient;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.DataSourceInfo;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.LogFilterRule;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.LogmatrixSolution;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ScrollQueryResult;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.immediate.ImmediatePerformance;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.AssociationFlowDn;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.AssociationFlowParamOne;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.DiagnosisCacheModel;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.FilterMo;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.PreExecutionNode;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TemplateTreeModel;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TemplateTreeNode;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TemplateTreePath;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TriggerAlgorithmResult;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TriggerExecuteParam;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TriggerIndicatorAlgorithmResult;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TriggerTaskCacheModel;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.UnHandledExceptionMessage;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.handlerimpl.TaskManageDao;
import com.huawei.i2000.dvanalysisengineservice.dao.trigger.mapper.AssociationAnalysisDao;
import com.huawei.i2000.dvanalysisengineservice.model.AlarmPoint;
import com.huawei.i2000.dvanalysisengineservice.model.AlarmTriggerResult;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTask;
import com.huawei.i2000.dvanalysisengineservice.model.AnomalyRate;
import com.huawei.i2000.dvanalysisengineservice.model.AssociationCurveQuery;
import com.huawei.i2000.dvanalysisengineservice.model.FilterMoScore;
import com.huawei.i2000.dvanalysisengineservice.model.FlowAction;
import com.huawei.i2000.dvanalysisengineservice.model.IpsDetail;
import com.huawei.i2000.dvanalysisengineservice.model.LogTemplate;
import com.huawei.i2000.dvanalysisengineservice.model.LogTriggerResult;
import com.huawei.i2000.dvanalysisengineservice.model.ModelParameter;
import com.huawei.i2000.dvanalysisengineservice.model.NodeIndicatorQuery;
import com.huawei.i2000.dvanalysisengineservice.model.Relation;
import com.huawei.i2000.dvanalysisengineservice.model.TaskIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerAlarmList;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerExecution;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerExecutionNode;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerRecommendation;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerTaskMessage;
import com.huawei.i2000.dvanalysisengineservice.util.ApplicationContextHelper;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;
import com.huawei.i2000.util.runtime.SystemUtils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.io.File;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * TriggerTaskHandlerTest
 *
 * <AUTHOR>
 * @since 2023-09-20
 */
@PrepareForTest({EncryptionUtil.class, SystemUtils.class, ContextUtils.class, HofsUtil.class, RestUtil.class, AlarmClient.class, UserRoleUtil.class, TriggerCacheOper.class,
    ApplicationContextHelper.class, TriggerTaskHandler.class, ImmediatePerformance.class, HandleDtoFactory.class, FileCleanUtils.class, ResourceGroupUtil.class, TaskImportUtils.class,
    TriggerTaskCacheOper.class, ConfigurationUtil.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class TriggerTaskHandlerTest extends WebServiceTest {
    private static final AtomicBoolean DATALOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    @Autowired
    @InjectMocks
    @Spy
    private TriggerTaskHandler triggerTaskHandler;


    @Mock
    LogmatrixClient logmatrixClient;

    @Mock
    protected PyExecutor pyExecutor;

    @Mock
    AlgorithmManageService algorithmManageService;

    @Autowired
    AssociationAnalysisDao associationAnalysisDao;

    @Autowired
    TemplateTaskManager taskManager;

    @Autowired
    @InjectMocks
    TriggerAlgorithmManager triggerAlgorithmManager;

    @Autowired
    TemplateTaskManager templateTaskManager;

    @Mock
    TaskManageDao taskManageDao;

    Class<TriggerTaskHandler> triggerTaskHandlerClass = TriggerTaskHandler.class;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");

        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        Mockito.when(contextUtils.getUserId()).thenReturn("1");
        // 保证这个只运行一次
        if (!DATALOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvanalysisengineservice_clear_all_table.sql", false);
        executeSqlScript("classpath:database/dvanalysisengineservice_association_analysis_zenith.sql", false);
        PowerMockito.mockStatic(EncryptionUtil.class);
        PowerMockito.doNothing()
            .when(EncryptionUtil.class, "changeFilePermission", Mockito.anyString(), Mockito.anyString());
    }

    /**
     * changeFilePermission 方法无法在windows系统执行，需要mock掉
     *
     * @throws Exception Exception
     */
    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    /**
     * rest server mock stop
     */
    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    /**
     * isConditionSatisfyTest
     *
     * @throws ClassNotFoundException ClassNotFoundException
     * @throws NoSuchMethodException NoSuchMethodException
     * @throws InvocationTargetException InvocationTargetException
     * @throws IllegalAccessException IllegalAccessException
     */
    @Test
    public void isConditionSatisfyTest()
        throws ClassNotFoundException, NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        Class<?> clazz = Class.forName("com.huawei.i2000.dvanalysisengineservice.business.trigger.TriggerTaskHandler");
        Method method = clazz.getDeclaredMethod("isConditionSatisfy", TriggerExecutionNode.class, TemplateTreePath.class);
        method.setAccessible(true);
        TriggerTaskHandler handler = new TriggerTaskHandler();
        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        triggerExecutionNode.setIsGray(true);
        TemplateTreePath templateTreePath = new TemplateTreePath();
        templateTreePath.setCondition("==5");

        Boolean result = (Boolean) method.invoke(handler, triggerExecutionNode, templateTreePath);
        Assert.assertEquals(result, true);

        triggerExecutionNode.setIsGray(false);
        triggerExecutionNode.setExecutionStatus("fail");
        templateTreePath.setConditionSatisfy(null);
        Boolean result1 = (Boolean) method.invoke(handler, triggerExecutionNode, templateTreePath);
        Assert.assertEquals(result1, false);

        triggerExecutionNode.setExecutionStatus("success");
        triggerExecutionNode.setCorrelationDegree(5.0f);
        templateTreePath.setConditionSatisfy(null);
        Boolean result2 = (Boolean) method.invoke(handler, triggerExecutionNode, templateTreePath);
        Assert.assertEquals(result2, true);

        templateTreePath.setCondition(">=5");
        templateTreePath.setConditionSatisfy(null);
        Boolean result3 = (Boolean) method.invoke(handler, triggerExecutionNode, templateTreePath);
        Assert.assertEquals(result3, true);

        templateTreePath.setCondition("<=5");
        templateTreePath.setConditionSatisfy(null);
        Boolean result4 = (Boolean) method.invoke(handler, triggerExecutionNode, templateTreePath);
        Assert.assertEquals(result4, true);

        templateTreePath.setCondition("!=5");
        templateTreePath.setConditionSatisfy(null);
        Boolean result5 = (Boolean) method.invoke(handler, triggerExecutionNode, templateTreePath);
        Assert.assertEquals(false, result5);

        templateTreePath.setCondition("#=5");
        templateTreePath.setConditionSatisfy(null);
        Exception e = null;
        try {
            method.invoke(handler, triggerExecutionNode, templateTreePath);
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNotNull(e);
    }

    /**
     * executeOnceTest
     *
     * @throws Exception Exception
     */
    @Test
    public void executeOnceTest() throws Exception {
        PowerMockito.mockStatic(System.class);
        PowerMockito.when(System.currentTimeMillis()).thenReturn(1695197760133L);
        TriggerAlgorithmResult triggerAlgorithmResult = new TriggerAlgorithmResult();
        triggerAlgorithmResult.setResultFilePath("resultPath");
        triggerAlgorithmResult.setMainRelationScore(100.0f);
        Map<String, Float> scoreMap = new HashMap<>();
        scoreMap.put("id1", 100.0f);
        triggerAlgorithmResult.setIndicatorRelation(scoreMap);
        String result = JSON.toJSONString(triggerAlgorithmResult);
        Mockito.when(pyExecutor.execute(Mockito.anyString(),Mockito.any(),Mockito.anyString())).thenReturn(result);

        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        triggerExecutionNode.setMainIndicatorDataType("Indicator");
        triggerExecutionNode.setTimeType(1);
        triggerExecutionNode.setKpiTimeRange("1|MIN");
        triggerExecutionNode.setKpiTimeType("beforeAfter");
        triggerExecutionNode.setDatasourceId("1");
        triggerExecutionNode.setIsPrimary(0);
        triggerExecutionNode.setMainIndicatorDataType("SQL_PM");
        triggerExecutionNode.setIndicatorDataType("SQL_PM");
        triggerExecutionNode.setNodeId("nodeId1");
        triggerExecutionNode.setNodeName("nodeName");
        TriggerIndicator triggerIndicator = new TriggerIndicator();
        triggerIndicator.setDataSourceId(1);
        triggerIndicator.setIndexId("indexId");
        triggerIndicator.setMoType("moType");
        triggerIndicator.setMeasUnitKey("unitKey");
        triggerIndicator.setMeasTypeKey("typeKey");
        triggerIndicator.setDn("OS=1");
        triggerExecutionNode.setMainIndicator(triggerIndicator);
        List<TriggerIndicator> triggerIndicators = new ArrayList<>();
        triggerIndicators.add(triggerIndicator);
        triggerExecutionNode.setIndicatorList(triggerIndicators);
        triggerExecutionNode.setMoName("moName");
        triggerExecutionNode.getMainIndicator().setSubSite("1");
        triggerExecutionNode.setSolution("solution");
        Map<String, String> map = new HashMap<>();
        map.put("datasourceId", "1");
        triggerExecutionNode.setExtendJson(JSON.toJSONString(map));
        triggerExecutionNode.setAlgorithmTemplateId(1);
        long startTime = 1695197760132L;
        String templateName = "tempName";
        Boolean importTaskStatus = false;
        String nodeFilter = "bySolutionId";

        Exception e = null;
        try {
            triggerTaskHandler.executeOnce(triggerExecutionNode, startTime, templateName, importTaskStatus, nodeFilter, 0);
        } catch (Exception ex) {
            e = ex;
        } finally {
            new File(TaskConstant.TEMP_SRC_PATH + "nodeId11695197760133_1695197760133_main.csv").delete();
            new File(TaskConstant.TEMP_SRC_PATH + "nodeId11695197760133_1695197760133_association.csv").delete();
        }
        Assert.assertNull(e);
    }

    /**
     * executeOnceLogNodeTest
     *
     * @throws Exception Exception
     */
    @Test
    public void executeOnceLogNodeTest() throws Exception {
        PowerMockito.mockStatic(System.class);
        PowerMockito.when(System.currentTimeMillis()).thenReturn(1695197760133L);
        TriggerAlgorithmResult triggerAlgorithmResult = new TriggerAlgorithmResult();
        triggerAlgorithmResult.setResultFilePath("resultPath");
        triggerAlgorithmResult.setMainRelationScore(100.0f);
        Map<String, Float> scoreMap = new HashMap<>();
        scoreMap.put("id1", 100.0f);
        triggerAlgorithmResult.setIndicatorRelation(scoreMap);
        String result = JSON.toJSONString(triggerAlgorithmResult);
        Mockito.when(pyExecutor.execute(Mockito.anyString(),Mockito.any(),Mockito.anyString())).thenReturn(result);

        List<LogmatrixSolution> logmatrixSolutions = new ArrayList<>();
        LogmatrixSolution logmatrixSolution = new LogmatrixSolution();
        logmatrixSolution.setSolutionName("logSolution");
        logmatrixSolution.setMotype("solutionType");
        logmatrixSolutions.add(logmatrixSolution);
        Mockito.when(logmatrixClient.getLogmatrixSolutionList()).thenReturn(logmatrixSolutions);
        List<String> types = new ArrayList<>();
        types.add("logType");
        Mockito.when(logmatrixClient.getLogTypeList(Mockito.anyString())).thenReturn(types);

        List<LogFilterRule> rules = new ArrayList<>();
        LogFilterRule logFilterRule = new LogFilterRule();
        logFilterRule.setFilterName("error");
        logFilterRule.setFilterType("errorType");
        rules.add(logFilterRule);
        Mockito.when(logmatrixClient.getLogFilterRuleList(Mockito.anyString(), Mockito.anyString())).thenReturn(rules);

        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        triggerExecutionNode.setMainIndicatorDataType("Indicator");
        triggerExecutionNode.setTimeType(1);
        triggerExecutionNode.setDatasourceId("1");
        triggerExecutionNode.setIsPrimary(0);
        triggerExecutionNode.setMainIndicatorDataType("SQL_PM");
        triggerExecutionNode.setNodeId("nodeId1");
        triggerExecutionNode.setNodeName("nodeName");
        TriggerIndicator triggerIndicator = new TriggerIndicator();
        triggerExecutionNode.setMainIndicator(triggerIndicator);
        triggerExecutionNode.getMainIndicator().setSubSite("1");
        triggerExecutionNode.setSolution("solution");
        triggerExecutionNode.setIsGray(true);
        triggerExecutionNode.setExtendJson("{\"analysisField\":\"filed\",\"logSolutionName\":\"logSolution\",\"logType\":\"logType\",\"queryFields\":{\"error\":\"errorValue\"},\"solutionType\":\"solutionType\"}");
        triggerExecutionNode.setAlgorithmTemplateId(1);
        long startTime = 1695197760132L;
        String templateName = "tempName";
        Boolean importTaskStatus = false;
        String nodeFilter = "bySolutionId";

        Exception e = null;
        try {
            triggerTaskHandler.executeOnce(triggerExecutionNode, startTime, templateName, importTaskStatus, nodeFilter, 0);
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNotNull(e);
    }

    /**
     * executePreExecutionTest
     *
     * @throws Exception Exception
     */
    @Test
    public void executePreExecutionTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Map<String, TemplateTreeNode> effectiveNodes = new HashMap<>();
        TemplateTreeNode node = new TemplateTreeNode();
        node.setId("nodeID");
        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        List<TriggerIndicator> indicatorList = new ArrayList<>();
        TriggerIndicator indicator = new TriggerIndicator();
        indicator.setMeasUnitKey("measUnitKey");
        indicatorList.add(indicator);
        triggerExecutionNode.setIndicatorList(indicatorList);
        triggerExecutionNode.setIndicatorDataType("DV_SHOW");
        triggerExecutionNode.setDataSourceType("execute");
        triggerExecutionNode.setExtendJson("{}");
        node.setNodeParam(triggerExecutionNode);
        effectiveNodes.put("\"nodeID\"", node);
        List<TemplateTreePath> effectivePaths = new ArrayList<>();
        TemplateTreePath path = new TemplateTreePath();
        path.setSource("\"nodeID\"");
        effectivePaths.add(path);
        Map<String, List<TemplateTreePath>> preExecuteNodeParentIds = new HashMap<>();
        preExecuteNodeParentIds.put("\"nodeID\"", effectivePaths);
        Map<String, TemplateTreeNode> nodesMap = new HashMap<>();
        nodesMap.put("\"nodeID\"", node);
        TriggerExecution triggerExecution = new TriggerExecution();
        triggerExecution.setTaskId(4);
        triggerExecution.setId("123");

        DiagnosisCacheModel diagnosisCacheModel = new DiagnosisCacheModel();
        diagnosisCacheModel.setNodeId("\"nodeID\"");
        triggerExecutionNode.setExecutionStatus("success");
        diagnosisCacheModel.setTriggerExecutionNode(triggerExecutionNode);

        PowerMockito.mockStatic(TriggerTaskCacheOper.class);
        TriggerTaskCacheOper mock = Mockito.mock(TriggerTaskCacheOper.class);
        PowerMockito.when(TriggerTaskCacheOper.getInstance()).thenReturn(mock);
        PowerMockito.when(mock.getExecutedDiagnosisNodeCache(Mockito.anyString())).thenReturn(diagnosisCacheModel);

        Exception e = null;
        try {
            triggerTaskHandler.executePreExecution(effectiveNodes, effectivePaths, preExecuteNodeParentIds, nodesMap, triggerExecution);
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNull(e);
    }

    @Test
    public void executeExecuteFlowNodeTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        PowerMockito.when(contextUtils.getUserId()).thenReturn("1");
        PowerMockito.mockStatic(RestUtil.class);
        RestfulResponse response = new RestfulResponse();
        response.setStatus(200);
        Field field = RestfulResponse.class.getDeclaredField("responseContent");
        field.setAccessible(true);
        FlowTask flowTask = new FlowTask();
        flowTask.setStatus(FlowExecuteStatus.EXECUTING);
        field.set(response, JSON.toJSONString(flowTask));
        RestfulResponse response2 = new RestfulResponse();
        response2.setStatus(200);
        field.set(response2, "{\"taskIds\":[\"10\"],\"targetObj\":\"targetObj\"}");
        PowerMockito.when(RestUtil.sendRestRequest(Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.any())).thenReturn(response);
        PowerMockito.when(RestUtil.sendRestRequest(Mockito.anyString(), Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(response2);
        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        triggerExecutionNode.setDataSourceType(AssociationAnalysisType.EXECUTE);
        triggerExecutionNode.setExecutionId("executionId");
        PreExecutionNode preExecutionNode = new PreExecutionNode();
        FlowAction flowAction = new FlowAction();
        flowAction.setIpsDetails(Collections.singletonList(new IpsDetail()));
        preExecutionNode.setFlowActions(Collections.singletonList(flowAction));
        triggerExecutionNode.setExtendJson(JSON.toJSONString(preExecutionNode));

        TriggerExecution triggerExecution = new TriggerExecution();
        triggerExecution.setCreateUser("admin");

        JSONObject flowParameter = new JSONObject();
        AssociationFlowParamOne associationFlowParamOne = new AssociationFlowParamOne();
        ArrayList<AssociationFlowDn> dnList = new ArrayList<>();
        associationFlowParamOne.setDnList(dnList);
        for (int i = 0; i < 50; i++) {
            dnList.add(new AssociationFlowDn());
        }
        flowParameter.put("autoExecParams", JSON.toJSONString(Collections.singletonList(associationFlowParamOne)));
        triggerTaskHandler.executeExecuteFlowNode(triggerExecutionNode, flowParameter, triggerExecution, "test", true);
        List<Map<String, String>> list = associationAnalysisDao.getExecuteFlowTaskId(triggerExecutionNode.getExecutionId(), "test");

        Assert.assertEquals("10", list.get(0).get("FLOW_TASK_ID"));
    }

    @Test
    public void getAssociationFlowDnsKpiTest() throws Exception {
        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        ArrayList<TriggerIndicator> indicatorList = new ArrayList<>();
        indicatorList.add(new TriggerIndicator());
        triggerExecutionNode.setIndicatorList(indicatorList);
        List<AssociationFlowDn> list = Whitebox.invokeMethod(triggerTaskHandler, "getAssociationFlowDnsKpi",
            triggerExecutionNode, new TemplateTreePath());
        Assert.assertFalse(list.isEmpty());
    }

    @Test
    public void getAssociationFlowDnsAlarmTest() throws Exception {
        AlarmTriggerResult alarmTriggerResult = new AlarmTriggerResult();
        ArrayList<Relation> indicatorRelation = new ArrayList<>();
        indicatorRelation.add(new Relation());
        alarmTriggerResult.setIndicatorRelation(indicatorRelation);
        PowerMockito.mockStatic(Files.class);
        PowerMockito.when(Files.readAllBytes(Mockito.any())).thenReturn(JSON.toJSONString(alarmTriggerResult).getBytes());

        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        ArrayList<TriggerIndicator> indicatorList = new ArrayList<>();
        indicatorList.add(new TriggerIndicator());
        triggerExecutionNode.setIndicatorList(indicatorList);
        triggerExecutionNode.setResultFilePath("/resultFilePath");
        triggerExecutionNode.setExecutionId("testalarm2");
        triggerExecutionNode.setNodeId("testalarmnode2");
        TemplateTreePath templateTreePath = new TemplateTreePath();
        templateTreePath.setCondition(">=0.0");
        Whitebox.invokeMethod(triggerTaskHandler, "getAssociationFlowDnsAlarm",
            triggerExecutionNode, templateTreePath);
        Assert.assertEquals("association_matching", triggerExecutionNode.getAlgorithmFeatureType());
    }

    @Test
    public void getCsnListFromAlarmCurveTest() throws Exception {
        PowerMockito.mockStatic(AlarmClient.class);
        AlarmClient alarmClient = Mockito.mock(AlarmClient.class);
        PowerMockito.when(AlarmClient.getInstance()).thenReturn(alarmClient);
        ScrollQueryResult queryResult = new ScrollQueryResult();
        ArrayList<Map<String, String>> hits = new ArrayList<>();
        HashMap<String, String> map = new HashMap<>();
        map.put("occurUtc", "100");
        hits.add(map);
        queryResult.setHits(hits);
        queryResult.setResCode(1);
        PowerMockito.when(alarmClient.queryAlarm(Mockito.any(), Mockito.any())).thenReturn(queryResult);

        ArrayList<AlarmPoint> alarmPoints = new ArrayList<>();
        AlarmPoint point = new AlarmPoint();
        point.setCollectTime(0L);
        alarmPoints.add(point);
        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        triggerExecutionNode.setExtendJson("[]");
        TriggerAlarmList triggerAlarmList = new TriggerAlarmList();
        triggerAlarmList.setDeviceTypeId("1");
        List<AssociationFlowDn> list = Whitebox.invokeMethod(triggerTaskHandler, "getCsnListFromAlarmCurve",
            alarmPoints, triggerAlarmList, triggerExecutionNode, new HashSet<String>());
        Assert.assertFalse(list.isEmpty());
    }

    @Test
    public void handleAlarmResultTest() throws Exception {
        PowerMockito.mockStatic(FileCleanUtils.class);
        PowerMockito.when(FileCleanUtils.fileSizeOverMax(Mockito.any())).thenReturn(false);
        AlarmTriggerResult alarmTriggerResult = new AlarmTriggerResult();
        ArrayList<Relation> indicatorRelation = new ArrayList<>();
        Relation relation = new Relation();
        relation.setAlarmId("alarmId");
        indicatorRelation.add(relation);
        alarmTriggerResult.setIndicatorRelation(indicatorRelation);
        PowerMockito.mockStatic(Files.class);
        PowerMockito.when(Files.readAllBytes(Mockito.any())).thenReturn(JSON.toJSONString(alarmTriggerResult).getBytes());
        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        triggerExecutionNode.setResultFilePath("path");
        triggerExecutionNode.setExecutionId("executionId");
        triggerExecutionNode.setNodeId("nodeId");
        triggerTaskHandler.handleAlarmResult(triggerExecutionNode);
        NodeIndicatorQuery queryParam = new NodeIndicatorQuery();
        queryParam.setId("executionId");
        queryParam.setNodeId("nodeId");
        List<TriggerAlarmList> nodeAlarmInfo = associationAnalysisDao.getNodeAlarmInfo(queryParam);
        Assert.assertEquals("alarmId", nodeAlarmInfo.get(0).getAlarmId());
    }

    @Test
    public void handleLogResultTest() throws Exception {
        PowerMockito.mockStatic(FileCleanUtils.class);
        PowerMockito.when(FileCleanUtils.fileSizeOverMax(Mockito.any())).thenReturn(false);

        PowerMockito.mockStatic(Files.class);
        LogTriggerResult logTriggerResult = new LogTriggerResult();
        logTriggerResult.setAnomalyRate(Collections.singletonList(new AnomalyRate()));
        LogTemplate template = new LogTemplate();
        template.setTemplateId(11);
        logTriggerResult.setTemplate(Collections.singletonList(template));
        PowerMockito.when(Files.readAllBytes(Mockito.any())).thenReturn(JSON.toJSONString(logTriggerResult).getBytes());
        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        triggerExecutionNode.setResultFilePath("path");
        triggerExecutionNode.setExecutionId("executionId");
        triggerExecutionNode.setNodeId("nodeId");
        triggerTaskHandler.handleLogResult(triggerExecutionNode);
        AssociationCurveQuery queryParam = new AssociationCurveQuery();
        queryParam.setExecutionId("executionId");
        queryParam.setNodeId("nodeId");
        String logCurve = associationAnalysisDao.getLogCurve(queryParam);
        Assert.assertEquals("[{\"anomalyTemplate\":[]}]", logCurve);
    }

    @Test
    public void getAuthDnsByUserIdTest() throws Exception {
        PowerMockito.mockStatic(UserRoleUtil.class);
        UserInfo userInfo = new UserInfo();
        userInfo.setRoles(Collections.singletonList(new RoleBase()));
        PowerMockito.when(UserRoleUtil.queryUserInfoByUserId("1", null)).thenReturn(userInfo);
        PowerMockito.when(UserRoleUtil.queryAllResourceGroupsByUserId(null)).thenReturn(Collections.singletonList("groups"));
        PowerMockito.mockStatic(ResourceGroupUtil.class);
        PowerMockito.when(ResourceGroupUtil.queryDnListByGroupIds(Mockito.any())).thenReturn(Collections.singletonList("dn"));
        Set<String> set = Whitebox.invokeMethod(triggerTaskHandler, "getAuthDnsByUserId", "1");
        Assert.assertEquals(1, set.size());
    }

    @Test
    public void templateErrorHandleTest() throws Exception {
        PowerMockito.mockStatic(TriggerCacheOper.class);
        TriggerCacheOper mock = Mockito.mock(TriggerCacheOper.class);
        PowerMockito.when(TriggerCacheOper.getInstance()).thenReturn(mock);
        PowerMockito.mockStatic(System.class);
        PowerMockito.when(System.currentTimeMillis()).thenReturn(1652146007825L);

        TriggerExecution triggerExecution = new TriggerExecution();
        triggerExecution.setUpdateTime(0L);

        AnalysisTask task = new AnalysisTask();
        TriggerTaskMessage taskMessage = new TriggerTaskMessage();
        taskMessage.setUseTemplate(1);
        taskMessage.setDelay("delay");
        task.setTriggerTaskMessage(taskMessage);
        Whitebox.invokeMethod(triggerTaskHandler, "templateErrorHandle", task, triggerExecution, new ServiceException());
        List<TriggerExecution> triggerExecutions = associationAnalysisDao.getAssocationTask(null, null, 0,
            System.currentTimeMillis(), null, null, null, false);
        Assert.assertEquals(1652146007825L, triggerExecutions.get(0).getUpdateTime().longValue());
    }

    @Test
    public void sendEmailTest() throws Exception {
        Exception exp = null;
        try {
            PowerMockito.mockStatic(ContextUtils.class);
            ContextUtils contextUtils = Mockito.spy(new ContextUtils());
            PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
            PowerMockito.when(contextUtils.getUsername()).thenReturn("1");
            TriggerExecution triggerExecution = new TriggerExecution();
            triggerExecution.setTemplateId(6);
            triggerTaskHandler.sendEmail(triggerExecution, true);
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);
    }

    @Test
    public void throwQueueTooMuchExceptionTest() {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        Mockito.when(contextUtils.getLocale()).thenReturn(new Locale("en", "US"));
        ServiceException ex = new ServiceException();
         try {
             AnalysisTask task = new AnalysisTask();
             TriggerIndicator taskIndicator = new TriggerIndicator();
             task.setTaskName("test");
             taskIndicator.setIndicatorId("111");
             triggerTaskHandler.throwQueueTooMuchException(task, taskIndicator);
         } catch (ServiceException e) {
             ex = e;
         }
        Assert.assertEquals(33, ex.getExceptionArgs().getDetailArgs()[0].length());
    }

    @Test
    public void templateTimeOutHandleTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        String analysisTaskJson = "{\"algorithmModelName\":\"suanfa\",\"algorithmParam\":\"1\",\"datasourceId\":\"1,101,102\",\"indicatorList\":[],\"lastExecutorUserId\":\"1\",\"lastModifyUserId\":\"1\",\"predictCron\":\"trigger\",\"sourceName\":\"!com.huawei.i2000.analysis.service.datasource.name.perfomance!,pppr,zenith\",\"startStatus\":2,\"taskId\":6,\"taskName\":\"muban1\",\"taskType\":4,\"triggerTaskMessage\":{\"indicatorList\":[],\"relationTree\":\"{\\\"nodes\\\":[{\\\"figures\\\":\\\"[{\\\\\\\"type\\\\\\\":\\\\\\\"round\\\\\\\",\\\\\\\"style\\\\\\\":\\\\\\\"left:77px;top:187px;width:50px;height:50px;\\\\\\\",\\\\\\\"context\\\\\\\":{\\\\\\\"strokeStyle\\\\\\\":\\\\\\\"#9fdfff\\\\\\\",\\\\\\\"lineWidth\\\\\\\":2,\\\\\\\"fillStyle\\\\\\\":\\\\\\\"#9fdfff\\\\\\\"},\\\\\\\"options\\\\\\\":{}}]\\\",\\\"id\\\":\\\"flow-diagram_N1\\\",\\\"leaf\\\":false,\\\"location\\\":null,\\\"nodeParam\\\":null,\\\"number\\\":null,\\\"relevance\\\":null,\\\"root\\\":false,\\\"status\\\":null,\\\"title\\\":\\\"Start\\\",\\\"type\\\":\\\"start\\\"},{\\\"figures\\\":\\\"[{\\\\\\\"type\\\\\\\":\\\\\\\"roundrect\\\\\\\",\\\\\\\"style\\\\\\\":\\\\\\\"left:244px;top:108px;width:120px;height:35px;\\\\\\\",\\\\\\\"options\\\\\\\":{\\\\\\\"radius\\\\\\\":5},\\\\\\\"context\\\\\\\":{\\\\\\\"strokeStyle\\\\\\\":\\\\\\\"#00aaff\\\\\\\",\\\\\\\"lineWidth\\\\\\\":1,\\\\\\\"fillStyle\\\\\\\":\\\\\\\"#e8f7ff\\\\\\\"}}]\\\",\\\"id\\\":\\\"flow-diagram_N2\\\",\\\"leaf\\\":false,\\\"location\\\":null,\\\"nodeParam\\\":{\\\"algorithmModelName\\\":null,\\\"algorithmParam\\\":null,\\\"algorithmTemplateId\\\":1,\\\"algorithmTemplateName\\\":\\\"suanfa\\\",\\\"beginTime\\\":null,\\\"columnMapping\\\":null,\\\"correlationDegree\\\":null,\\\"customSourceId\\\":null,\\\"dataSourceName\\\":null,\\\"dataSourceType\\\":\\\"performance\\\",\\\"datasourceId\\\":\\\"1\\\",\\\"endTime\\\":null,\\\"executionId\\\":null,\\\"executionStatus\\\":null,\\\"extendJson\\\":null,\\\"indicatorDataType\\\":\\\"DV_PM\\\",\\\"indicatorList\\\":[{\\\"abnormal\\\":null,\\\"checkedNetId\\\":\\\"/(_)com.huawei.as(_)com.huawei.as.platform(_)com.huawei.as.platform.om(_)com.huawei.as.platform.om.basic(_)OMS\\\",\\\"correlationDegree\\\":0.0,\\\"dataSourceId\\\":null,\\\"dataSourceName\\\":null,\\\"displayValue\\\":\\\"************\\\",\\\"dn\\\":\\\"OS=1\\\",\\\"dnName\\\":\\\"EMS\\\",\\\"executionId\\\":null,\\\"hasMeasObj\\\":\\\"true\\\",\\\"indexId\\\":\\\"I2K_OS~~IOWait\\\",\\\"indexName\\\":\\\"IO等待(%)\\\",\\\"indicatorId\\\":\\\"OS=1I2K_OSIOWait************\\\",\\\"measTypeKey\\\":\\\"IOWait\\\",\\\"measUnitKey\\\":\\\"I2K_OS\\\",\\\"measUnitName\\\":\\\"操作系统监控\\\",\\\"moType\\\":\\\"OMS\\\",\\\"nodeId\\\":null,\\\"originalValue\\\":\\\"ip=************\\\",\\\"pql\\\":null,\\\"resourceTypeKey\\\":\\\"OMS\\\",\\\"taskId\\\":null,\\\"taskName\\\":null,\\\"unit\\\":\\\"%\\\"}],\\\"indicatorSelectType\\\":1,\\\"isPrimary\\\":null,\\\"kpiTimeRange\\\":\\\"1|HOUR\\\",\\\"kpiTimeType\\\":\\\"recently\\\",\\\"mainExtendJson\\\":null,\\\"mainIndicator\\\":null,\\\"mainIndicatorDataType\\\":null,\\\"mainIndicatorJson\\\":null,\\\"moType\\\":null,\\\"nodeId\\\":null,\\\"nodeName\\\":\\\"typedv\\\",\\\"nodeType\\\":null,\\\"parentNodeId\\\":null,\\\"prometheusData\\\":null,\\\"solutionId\\\":\\\"\\\",\\\"solutionName\\\":\\\"\\\",\\\"sourceName\\\":null,\\\"taskName\\\":null,\\\"timeType\\\":null},\\\"number\\\":null,\\\"relevance\\\":null,\\\"root\\\":false,\\\"status\\\":null,\\\"title\\\":\\\"typedv\\\",\\\"type\\\":\\\"Associated_KPI\\\"},{\\\"figures\\\":\\\"[{\\\\\\\"type\\\\\\\":\\\\\\\"roundrect\\\\\\\",\\\\\\\"style\\\\\\\":\\\\\\\"left:248px;top:224px;width:120px;height:35px;\\\\\\\",\\\\\\\"options\\\\\\\":{\\\\\\\"radius\\\\\\\":5},\\\\\\\"context\\\\\\\":{\\\\\\\"strokeStyle\\\\\\\":\\\\\\\"#00aaff\\\\\\\",\\\\\\\"lineWidth\\\\\\\":1,\\\\\\\"fillStyle\\\\\\\":\\\\\\\"#e8f7ff\\\\\\\"}}]\\\",\\\"id\\\":\\\"flow-diagram_N3\\\",\\\"leaf\\\":false,\\\"location\\\":null,\\\"nodeParam\\\":{\\\"algorithmModelName\\\":null,\\\"algorithmParam\\\":null,\\\"algorithmTemplateId\\\":1,\\\"algorithmTemplateName\\\":\\\"suanfa\\\",\\\"beginTime\\\":null,\\\"columnMapping\\\":null,\\\"correlationDegree\\\":null,\\\"customSourceId\\\":null,\\\"dataSourceName\\\":null,\\\"dataSourceType\\\":\\\"prometheus\\\",\\\"datasourceId\\\":null,\\\"endTime\\\":null,\\\"executionId\\\":null,\\\"executionStatus\\\":null,\\\"extendJson\\\":null,\\\"indicatorDataType\\\":\\\"CUSTOM_PM\\\",\\\"indicatorList\\\":[{\\\"abnormal\\\":null,\\\"checkedNetId\\\":null,\\\"correlationDegree\\\":0.0,\\\"dataSourceId\\\":100,\\\"dataSourceName\\\":\\\"pppr\\\",\\\"displayValue\\\":\\\"{\\\\\\\"failure_domain_beta_kubernetes_io_is_baremetal\\\\\\\":\\\\\\\"false\\\\\\\",\\\\\\\"kubernetes_io_eniquota\\\\\\\":\\\\\\\"12\\\\\\\",\\\\\\\"instance\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"kubernetes_io_arch\\\\\\\":\\\\\\\"amd64\\\\\\\",\\\\\\\"os_version\\\\\\\":\\\\\\\"3.10.0-862.14.1.5.h428.eulerosv2r7.x86_64\\\\\\\",\\\\\\\"src_sys\\\\\\\":\\\\\\\"region_operator_service\\\\\\\",\\\\\\\"beta_kubernetes_io_os\\\\\\\":\\\\\\\"linux\\\\\\\",\\\\\\\"kubernetes_io_hostname\\\\\\\":\\\\\\\"**************\\\\\\\",\\\\\\\"beta_kubernetes_io_arch\\\\\\\":\\\\\\\"amd64\\\\\\\",\\\\\\\"kubernetes_io_os\\\\\\\":\\\\\\\"linux\\\\\\\",\\\\\\\"kubernetes_io_subeniquota\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"node_kubernetes_io_subnetid\\\\\\\":\\\\\\\"ac1cce17-ef6b-42b7-8a08-3d69e75155d6\\\\\\\",\\\\\\\"__name__\\\\\\\":\\\\\\\"up\\\\\\\",\\\\\\\"failure_domain_beta_kubernetes_io_zone\\\\\\\":\\\\\\\"cn-south-3d\\\\\\\",\\\\\\\"failure_domain_beta_kubernetes_io_region\\\\\\\":\\\\\\\"cn-south-3\\\\\\\",\\\\\\\"os_name\\\\\\\":\\\\\\\"EulerOS_2.0_SP5\\\\\\\",\\\\\\\"kubernetes_io_availablezone\\\\\\\":\\\\\\\"cn-south-3d\\\\\\\",\\\\\\\"job\\\\\\\":\\\\\\\"k8s-nodes\\\\\\\",\\\\\\\"os_architecture\\\\\\\":\\\\\\\"amd64\\\\\\\"}\\\",\\\"dn\\\":\\\"admin\\\",\\\"dnName\\\":null,\\\"executionId\\\":null,\\\"hasMeasObj\\\":null,\\\"indexId\\\":null,\\\"indexName\\\":null,\\\"indicatorId\\\":\\\"4@_@c074a0666348eed6e0228d7802e9f37b0a841bf0a3509a2146d711e77388c415\\\",\\\"measTypeKey\\\":null,\\\"measUnitKey\\\":null,\\\"measUnitName\\\":null,\\\"moType\\\":null,\\\"nodeId\\\":null,\\\"originalValue\\\":null,\\\"pql\\\":\\\"up\\\",\\\"resourceTypeKey\\\":null,\\\"taskId\\\":4,\\\"taskName\\\":\\\"puluo\\\",\\\"unit\\\":null}],\\\"indicatorSelectType\\\":null,\\\"isPrimary\\\":null,\\\"kpiTimeRange\\\":\\\"1|HOUR\\\",\\\"kpiTimeType\\\":\\\"recently\\\",\\\"mainExtendJson\\\":null,\\\"mainIndicator\\\":null,\\\"mainIndicatorDataType\\\":null,\\\"mainIndicatorJson\\\":null,\\\"moType\\\":null,\\\"nodeId\\\":null,\\\"nodeName\\\":\\\"ppp\\\",\\\"nodeType\\\":null,\\\"parentNodeId\\\":null,\\\"prometheusData\\\":null,\\\"solutionId\\\":null,\\\"solutionName\\\":null,\\\"sourceName\\\":null,\\\"taskName\\\":null,\\\"timeType\\\":null},\\\"number\\\":null,\\\"relevance\\\":null,\\\"root\\\":false,\\\"status\\\":null,\\\"title\\\":\\\"ppp\\\",\\\"type\\\":\\\"Associated_KPI\\\"},{\\\"figures\\\":\\\"[{\\\\\\\"type\\\\\\\":\\\\\\\"roundrect\\\\\\\",\\\\\\\"style\\\\\\\":\\\\\\\"left:250px;top:335px;width:120px;height:35px;\\\\\\\",\\\\\\\"options\\\\\\\":{\\\\\\\"radius\\\\\\\":5},\\\\\\\"context\\\\\\\":{\\\\\\\"strokeStyle\\\\\\\":\\\\\\\"#00aaff\\\\\\\",\\\\\\\"lineWidth\\\\\\\":1,\\\\\\\"fillStyle\\\\\\\":\\\\\\\"#e8f7ff\\\\\\\"}}]\\\",\\\"id\\\":\\\"flow-diagram_N4\\\",\\\"leaf\\\":false,\\\"location\\\":null,\\\"nodeParam\\\":{\\\"algorithmModelName\\\":null,\\\"algorithmParam\\\":null,\\\"algorithmTemplateId\\\":1,\\\"algorithmTemplateName\\\":\\\"suanfa\\\",\\\"beginTime\\\":null,\\\"columnMapping\\\":null,\\\"correlationDegree\\\":null,\\\"customSourceId\\\":null,\\\"dataSourceName\\\":null,\\\"dataSourceType\\\":\\\"zenith\\\",\\\"datasourceId\\\":null,\\\"endTime\\\":null,\\\"executionId\\\":null,\\\"executionStatus\\\":null,\\\"extendJson\\\":null,\\\"indicatorDataType\\\":\\\"CUSTOM_PM\\\",\\\"indicatorList\\\":[{\\\"abnormal\\\":null,\\\"checkedNetId\\\":null,\\\"correlationDegree\\\":0.0,\\\"dataSourceId\\\":101,\\\"dataSourceName\\\":\\\"zenith\\\",\\\"displayValue\\\":\\\"ConvergedCharging\\\",\\\"dn\\\":\\\"admin\\\",\\\"dnName\\\":null,\\\"executionId\\\":null,\\\"hasMeasObj\\\":null,\\\"indexId\\\":null,\\\"indexName\\\":\\\"访问时延_1\\\",\\\"indicatorId\\\":\\\"7@_@访问时延_1@_@ConvergedCharging@_@unit\\\",\\\"measTypeKey\\\":null,\\\"measUnitKey\\\":\\\"unit\\\",\\\"measUnitName\\\":\\\"unit\\\",\\\"moType\\\":null,\\\"nodeId\\\":null,\\\"originalValue\\\":null,\\\"pql\\\":null,\\\"resourceTypeKey\\\":null,\\\"taskId\\\":7,\\\"taskName\\\":\\\"zidingyi\\\",\\\"unit\\\":null}],\\\"indicatorSelectType\\\":null,\\\"isPrimary\\\":null,\\\"kpiTimeRange\\\":\\\"1|HOUR\\\",\\\"kpiTimeType\\\":\\\"recently\\\",\\\"mainExtendJson\\\":null,\\\"mainIndicator\\\":null,\\\"mainIndicatorDataType\\\":null,\\\"mainIndicatorJson\\\":null,\\\"moType\\\":null,\\\"nodeId\\\":null,\\\"nodeName\\\":\\\"zzzz\\\",\\\"nodeType\\\":null,\\\"parentNodeId\\\":null,\\\"prometheusData\\\":null,\\\"solutionId\\\":null,\\\"solutionName\\\":null,\\\"sourceName\\\":null,\\\"taskName\\\":null,\\\"timeType\\\":null},\\\"number\\\":null,\\\"relevance\\\":null,\\\"root\\\":false,\\\"status\\\":null,\\\"title\\\":\\\"zzzz\\\",\\\"type\\\":\\\"Associated_KPI\\\"}],\\\"paths\\\":[{\\\"condition\\\":null,\\\"figures\\\":\\\"{\\\\\\\"edge\\\\\\\":[{\\\\\\\"type\\\\\\\":\\\\\\\"line\\\\\\\",\\\\\\\"context\\\\\\\":{\\\\\\\"strokeStyle\\\\\\\":\\\\\\\"#00aaff\\\\\\\",\\\\\\\"lineWidth\\\\\\\":2}}]}\\\",\\\"id\\\":\\\"flow-diagram_P1\\\",\\\"relevance\\\":null,\\\"source\\\":\\\"flow-diagram_N1\\\",\\\"sourceAnchor\\\":3,\\\"target\\\":\\\"flow-diagram_N2\\\",\\\"targetAnchor\\\":7},{\\\"condition\\\":null,\\\"figures\\\":\\\"{\\\\\\\"edge\\\\\\\":[{\\\\\\\"type\\\\\\\":\\\\\\\"line\\\\\\\",\\\\\\\"context\\\\\\\":{\\\\\\\"strokeStyle\\\\\\\":\\\\\\\"#00aaff\\\\\\\",\\\\\\\"lineWidth\\\\\\\":2}}]}\\\",\\\"id\\\":\\\"flow-diagram_P2\\\",\\\"relevance\\\":null,\\\"source\\\":\\\"flow-diagram_N1\\\",\\\"sourceAnchor\\\":3,\\\"target\\\":\\\"flow-diagram_N3\\\",\\\"targetAnchor\\\":7},{\\\"condition\\\":null,\\\"figures\\\":\\\"{\\\\\\\"edge\\\\\\\":[{\\\\\\\"type\\\\\\\":\\\\\\\"line\\\\\\\",\\\\\\\"context\\\\\\\":{\\\\\\\"strokeStyle\\\\\\\":\\\\\\\"#00aaff\\\\\\\",\\\\\\\"lineWidth\\\\\\\":2}}]}\\\",\\\"id\\\":\\\"flow-diagram_P3\\\",\\\"relevance\\\":null,\\\"source\\\":\\\"flow-diagram_N1\\\",\\\"sourceAnchor\\\":3,\\\"target\\\":\\\"flow-diagram_N4\\\",\\\"targetAnchor\\\":7}]}\",\"taskId\":6,\"triggerType\":1,\"useTemplate\":0,\"userId\":\"1\"},\"updateTime\":1657593360750,\"userId\":\"1\"}";
        String triggerExecutionJson = "{\"createUser\":\"\",\"executionTime\":1657587780000,\"id\":\"cd0a59a579c5413cb5905a72a93facd1\",\"indicatorId\":\"OS=1I2K_OSIOWait************\",\"status\":\"analyzing\",\"taskId\":4,\"templateId\":6,\"updateTime\":1657606131343}";
        TriggerTaskCacheModel taskCacheModel = new TriggerTaskCacheModel();
        taskCacheModel.setTriggerExecution(JSON.parseObject(triggerExecutionJson, TriggerExecution.class));
        taskCacheModel.setAnalysisTask(JSON.parseObject(analysisTaskJson, AnalysisTask.class));
        Exception ex = null;
        try {
            taskManager.templateTimeOutHandle(taskCacheModel);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }

    @Test
    public void indicatorCombinationTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        List<UnHandledExceptionMessage> unHandledExceptionMessageList = new ArrayList<>();
        UnHandledExceptionMessage unHandledExceptionMessage1 = new UnHandledExceptionMessage();
        unHandledExceptionMessage1.setIndicatorId("OS=1I2K_OSIOWait7.220.172.201");
        unHandledExceptionMessage1.setTemplateId(6);
        unHandledExceptionMessage1.setOutlierId("123");
        unHandledExceptionMessage1.setStartTime(1708397820000L);
        unHandledExceptionMessage1.setTaskId(4);
        unHandledExceptionMessageList.add(unHandledExceptionMessage1);
        UnHandledExceptionMessage unHandledExceptionMessage2 = new UnHandledExceptionMessage();
        unHandledExceptionMessage2.setIndicatorId("OS=1I2K_OSIOWait7.220.172.201");
        unHandledExceptionMessage2.setTemplateId(7);
        unHandledExceptionMessage2.setOutlierId("123");
        unHandledExceptionMessage2.setStartTime(1708397820000L);
        unHandledExceptionMessage2.setTaskId(4);
        unHandledExceptionMessageList.add(unHandledExceptionMessage2);
        UnHandledExceptionMessage unHandledExceptionMessage3 = new UnHandledExceptionMessage();
        unHandledExceptionMessage3.setIndicatorId("OS=1I2K_CAPMGRMaxOnlineUsersNum");
        unHandledExceptionMessage3.setTemplateId(6);
        unHandledExceptionMessage3.setOutlierId("123");
        unHandledExceptionMessage3.setStartTime(1708397820000L);
        unHandledExceptionMessage3.setTaskId(4);
        unHandledExceptionMessageList.add(unHandledExceptionMessage3);
        UnHandledExceptionMessage unHandledExceptionMessage4 = new UnHandledExceptionMessage();
        unHandledExceptionMessage4.setIndicatorId("OS=1I2K_CAPMGRAllUsersNum");
        unHandledExceptionMessage4.setTemplateId(6);
        unHandledExceptionMessage4.setOutlierId("123");
        unHandledExceptionMessage4.setStartTime(1708397820000L);
        unHandledExceptionMessage4.setTaskId(4);
        unHandledExceptionMessageList.add(unHandledExceptionMessage4);
        Mockito.when(pyExecutor.execute(Mockito.anyString(), Mockito.any(), Mockito.anyString()))
            .thenReturn("[[\"OS=1I2K_OSIOWait7.220.172.201\",\"OS=1I2K_FMMGRFMAlarmDelayMax\"],[\"OS=1I2K_CAPMGRMaxOnlineUsersNum\"]]");
        long startTime = 1652131680000L;
        triggerAlgorithmManager.indicatorCombination(unHandledExceptionMessageList, startTime);
        Assert.assertEquals(4, unHandledExceptionMessageList.size());
    }

    @Test
    public void timeOutCancelTest() {
        Exception ex = null;
        try {
            Future<?> future = new CompletableFuture<>();
            TriggerTaskCacheOper cacheOper = TriggerTaskCacheOper.getInstance();
            String taskId = "1";
            TriggerTaskCacheModel taskCacheModel = new TriggerTaskCacheModel();
            TriggerExecution execution = new TriggerExecution();
            execution.setUpdateTime(System.currentTimeMillis());
            taskCacheModel.setTriggerExecution(execution);
            AnalysisTask analysisTask = new AnalysisTask();
            TriggerTaskMessage triggerTaskMessage = new TriggerTaskMessage();
            triggerTaskMessage.setUseTemplate(1);
            triggerTaskMessage.setDelay("1|MIN");
            analysisTask.setTriggerTaskMessage(triggerTaskMessage);
            taskCacheModel.setAnalysisTask(analysisTask);
            templateTaskManager.timeOutCancel(future, cacheOper, taskId, taskCacheModel);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }

    @Test
    public void setPreExecutionNodeIfOverSizeTest() {
        Exception ex = null;
        try {
            Whitebox.invokeMethod(triggerTaskHandler, "setPreExecutionNodeIfOverSize", new PreExecutionNode(), null);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertNotNull(ex);
    }

    @Test
    public void getDataSourceInfoTest() throws Exception {
        PowerMockito.mockStatic(HandleDtoFactory.class);
        PowerMockito.when(HandleDtoFactory.getInstance()).thenReturn(PowerMockito.mock(HandleDtoFactory.class));

        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        DataSourceInfo data = Whitebox.invokeMethod(triggerTaskHandler, "getDataSourceInfo",
            triggerExecutionNode, 0L, 0L, false, "nodeFilter", false, 0);
        Assert.assertNull(data);
    }

    @Test
    public void generateAlgorithmParamTest() throws Exception {
        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        triggerExecutionNode.setKpiTimeType("123");
        triggerExecutionNode.setComplementaryValue("345");
        triggerExecutionNode.setIndicatorDataType(AssociationAnalysisType.DV_LOG);

        List<ModelParameter> parametersArray = new ArrayList<>();
        ModelParameter modelParameter = new ModelParameter();
        modelParameter.setParameterName("name");
        modelParameter.setParameterDefaultValue("value");
        parametersArray.add(modelParameter);

        HashMap<String, Object> map = Whitebox.invokeMethod(triggerTaskHandler, "generateAlgorithmParam",triggerExecutionNode, parametersArray);
        Assert.assertFalse((Boolean) map.get("logMindsporeConfig"));
        Assert.assertEquals("123", map.get("data_type"));
        Assert.assertEquals("345", map.get("complementaryValue"));
    }

    /**
     * executeOnceTest
     *
     * @throws Exception Exception
     */
    @Test
    public void executeOnceByGroupTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getperformancedate.json");
        PowerMockito.mockStatic(TaskImportUtils.class);

        TriggerExecutionNode triggerExecutionNode = JSONObject.parseObject(new String(Files.readAllBytes(Paths.get(
                System.getProperty("user.dir") + "/target/test-classes/mockfile" + File.separator + "triggernode.json"))),
            TriggerExecutionNode.class);

        PowerMockito.when(TaskImportUtils.originalMatchedExpand(Mockito.anyList(), // 第一个参数：List
            Mockito.anyBoolean(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
            Mockito.any())).thenAnswer(invocation -> {
            // 打印传入的参数，检查是否正确
            return invocation.getArgument(0);  // 返回传入的第一个参数（List）
        });

        TriggerAlgorithmResult triggerAlgorithmResult = new TriggerAlgorithmResult();
        triggerAlgorithmResult.setResultFilePath("resultPath");
        triggerAlgorithmResult.setMainRelationScore(38.4f);
        Map<String, TriggerIndicatorAlgorithmResult> scoreMap = new HashMap<>();
        TriggerIndicatorAlgorithmResult algorithmResult = new TriggerIndicatorAlgorithmResult();
        algorithmResult.setScore(38.4f);
        algorithmResult.setContinueGrouping(true);
        algorithmResult.setScoreSources("");
        scoreMap.put("1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_24", algorithmResult);
        TriggerIndicatorAlgorithmResult algorithmResultNoExecute = new TriggerIndicatorAlgorithmResult();
        algorithmResultNoExecute.setScore(10f);
        algorithmResultNoExecute.setContinueGrouping(false);
        algorithmResultNoExecute.setScoreSources("");
        scoreMap.put("1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_0", algorithmResultNoExecute);

        triggerAlgorithmResult.setCorrelationAnalysisResult(scoreMap);
        String result = JSON.toJSONString(triggerAlgorithmResult);
        Mockito.when(pyExecutor.execute(Mockito.anyString(),Mockito.any(),Mockito.anyString())).thenReturn(result);

        long startTime = 1695197760132L;
        String templateName = "tempName";
        Boolean importTaskStatus = false;
        String nodeFilter = "bySolutionId";

        Exception e = null;
        try {
            triggerTaskHandler.executeOnce(triggerExecutionNode, startTime, templateName, importTaskStatus, nodeFilter, 0);
        } catch (Exception ex) {
            e = ex;
        } finally {
            File directory = new File(TaskConstant.TEMP_SRC_PATH);
            if (directory.exists() && directory.isDirectory()) {
                // 列出目录下所有文件
                File[] files = directory.listFiles((dir, name) -> (name.contains("_main") || name.contains("_association")) && name.endsWith(".csv"));
                if (files != null) {
                    for (File file : files) {
                        file.delete();
                    }
                }
            }
        }
        Assert.assertNull(e);
    }

    @Test
    public void handleNodeExecuteResultElseTest() throws Exception {
        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        triggerExecutionNode.setIndicatorDataType("SQL_PM");
        List<TriggerIndicator> indicatorList = new ArrayList<>();
        TriggerIndicator indicator = new TriggerIndicator();
        indicator.setIndicatorId("1");
        indicatorList.add(indicator);
        triggerExecutionNode.setIndicatorList(indicatorList);
        TriggerAlgorithmResult triggerAlgorithmResult = new TriggerAlgorithmResult();
        triggerAlgorithmResult.setMainRelationScore(10f);
        Map<String, Float> indicatorRelation = new HashMap<>();
        indicatorRelation.put("1", 11f);
        triggerAlgorithmResult.setIndicatorRelation(indicatorRelation);
        Map<String, TriggerIndicatorAlgorithmResult> correlationAnalysisResult = new HashMap<>();
        TriggerIndicatorAlgorithmResult algorithmResult = new TriggerIndicatorAlgorithmResult();
        algorithmResult.setScore(12f);
        correlationAnalysisResult.put("1", algorithmResult);
        triggerAlgorithmResult.setCorrelationAnalysisResult(correlationAnalysisResult);
        Exception e = null;
        try {
            triggerTaskHandler.handleNodeExcuteResult(JSONObject.toJSONString(triggerAlgorithmResult),
                triggerExecutionNode);
        } catch (Exception ex) {
            e = ex;
        }
        Assert.assertNull(e);
    }

    @Test
    public void addLogNodeAndPruningNodeTest() throws ServiceException {
        PowerMockito.mockStatic(ConfigurationUtil.class);
        PowerMockito.when(ConfigurationUtil.getAddSolutionDnList()).thenReturn(Collections.emptyList());
        ArrayList<TemplateTreePath> allPaths = new ArrayList<>();
        TemplateTreePath path = new TemplateTreePath();
        path.setId("pathId1");
        path.setSource("nodeId1");
        path.setTarget("nodeId2");
        path.setFilterMo(new FilterMo());
        path.setCondition("<=100.0");
        path.getFilterMo().setValue(true);
        allPaths.add(path);
        TemplateTreePath path2 = new TemplateTreePath();
        path2.setId("pathId2");
        path2.setSource("nodeId2");
        path2.setTarget("nodeId3");
        path2.setCondition("<=100.0");
        allPaths.add(path2);

        HashMap<String, TemplateTreeNode> nodeMap = new HashMap<>();
        TemplateTreeNode node1 = new TemplateTreeNode();
        node1.setId("nodeId1");
        node1.setNodeParam(new TriggerExecutionNode());
        node1.getNodeParam().getFilterMoScoreList().add(new FilterMoScore());
        node1.getNodeParam().getFilterMoScoreList().add(new FilterMoScore());
        nodeMap.put("nodeId1", node1);
        TemplateTreeNode node2 = new TemplateTreeNode();
        node2.setId("nodeId2");
        node2.setType(AssociationNodeTypeEnum.LOG.getNodeType());
        node2.setNodeParam(new TriggerExecutionNode());
        nodeMap.put("nodeId2", node2);
        TemplateTreeNode node3 = new TemplateTreeNode();
        node3.setId("nodeId3");
        nodeMap.put("nodeId3", node3);

        TriggerExecuteParam executeParam = new TriggerExecuteParam(new AnalysisTask(), new TemplateTreeNode(), 0L, new TriggerExecution(), nodeMap, allPaths, new TriggerIndicator());
        executeParam.getFinishedNodeIds().add("nodeId1");
        triggerTaskHandler.addLogNodeAndPruningNode(executeParam, node1);
        HashSet<String> newPruningPathIds = new HashSet<>();
        newPruningPathIds.add("pathId1");
        triggerTaskHandler.pruningNodeByLayer(executeParam, newPruningPathIds);
        Assert.assertTrue(executeParam.getPruningNodeIds().contains("nodeId2"));
        Assert.assertTrue(executeParam.getPruningNodeIds().contains("nodeId3"));
    }

    @Test
    public void getEmailContentTest() throws Exception {
        TriggerExecution triggerExecution = new TriggerExecution();
        triggerExecution.setId("123");
        triggerExecution.setTreeResult("{}");

        TemplateTreeNode templateTreeNode = new TemplateTreeNode();
        templateTreeNode.setType("123");
        templateTreeNode.setDemarcationLocating("Locating");
        TemplateTreeNode log = new TemplateTreeNode();
        log.setRelevance("80.0");
        List<TemplateTreeNode> childNodes = new ArrayList<>();
        childNodes.add(log);
        templateTreeNode.setChildNodes(childNodes);
        templateTreeNode.setRelevance("80.0");
        templateTreeNode.setId("123455");

        List<TemplateTreeNode> rootCase = new ArrayList<>();
        rootCase.add(templateTreeNode);

        LocationTriggerTreeNodeCalculation mock = Mockito.mock(LocationTriggerTreeNodeCalculation.class);
        PowerMockito.whenNew(LocationTriggerTreeNodeCalculation.class).withNoArguments().thenReturn(mock);
        PowerMockito.when(mock.sendEmailNodeSearch(Mockito.any(), Mockito.any())).thenReturn(rootCase);

        Method method = triggerTaskHandlerClass.getDeclaredMethod("getEmailContent", TriggerExecution.class,
            TaskIndicator.class);
        method.setAccessible(true);

        TaskIndicator taskIndicator = new TaskIndicator();
        taskIndicator.setDn("");
        taskIndicator.setIndexName("index");
        taskIndicator.setMeasUnitName("dn");
        taskIndicator.setDisplayValue("display");
        taskIndicator.setGroupId("1");
        taskIndicator.setDnName("123");

        String result = (String) method.invoke(triggerTaskHandler, triggerExecution, taskIndicator);

        Assert.assertNotEquals("", result);
    }

    @Test
    public void addIntelligentRecommendationInfoTest() throws Exception {
        TriggerExecution triggerExecution = new TriggerExecution();
        triggerExecution.setId("234");

        TemplateTreeNode templateTreeNode = new TemplateTreeNode();
        templateTreeNode.setType("123");
        templateTreeNode.setDemarcationLocating("Locating");
        TemplateTreeNode log = new TemplateTreeNode();
        log.setRelevance("80.0");
        List<TemplateTreeNode> childNodes = new ArrayList<>();
        childNodes.add(log);
        templateTreeNode.setChildNodes(childNodes);
        templateTreeNode.setRelevance("80.0");
        templateTreeNode.setId("123455");

        StringBuilder content = new StringBuilder();

        List<TriggerRecommendation> recommendationInfos = associationAnalysisDao.getRecommendationInfoById(triggerExecution.getId());

        Method method = triggerTaskHandlerClass.getDeclaredMethod("addIntelligentRecommendationInfo", TemplateTreeNode.class,
            StringBuilder.class, List.class);
        method.setAccessible(true);

        method.invoke(triggerTaskHandler, templateTreeNode, content, recommendationInfos);

        Assert.assertNotEquals("", content);
    }

    @Test
    public void printLeftNodeInfoTest() {
        TriggerExecuteParam executeParam = new TriggerExecuteParam();
        executeParam.setExecution(new TriggerExecution());
        executeParam.setFinishedNodeIds(new HashSet<>(Collections.singleton("nodeId1")));
        ArrayList<TemplateTreeNode> leftNodes = new ArrayList<>();
        TemplateTreeNode node1 = new TemplateTreeNode();
        TemplateTreeNode node2 = new TemplateTreeNode();
        leftNodes.add(node1);
        leftNodes.add(node2);
        node1.setId("nodeId1");
        node2.setId("nodeId2");
        node1.setNodeParam(new TriggerExecutionNode());
        node2.setNodeParam(new TriggerExecutionNode());
        TriggerTaskHandler.printLeftNodeInfo(executeParam, 0L, leftNodes);
        Assert.assertEquals(1, leftNodes.size());
    }

    @Test
    public void setNodeAndPathsTest() throws ServiceException {
        AnalysisTask analysisTask = new AnalysisTask();
        TemplateTreeModel templateTreeModel = new TemplateTreeModel();
        ArrayList<TemplateTreeNode> nodes = new ArrayList<>();
        ArrayList<TemplateTreePath> paths = new ArrayList<>();
        TriggerTaskMessage triggerTaskMessage = new TriggerTaskMessage();
        triggerTaskMessage.setDisplayFilter(true);
        analysisTask.setTriggerTaskMessage(triggerTaskMessage);
        TemplateTreeNode root = new TemplateTreeNode();
        root.setId("rootId");
        root.setRoot(true);
        TemplateTreeNode node1 = new TemplateTreeNode();
        node1.setId("node1Id");
        nodes.add(root);
        nodes.add(node1);
        TemplateTreePath path = new TemplateTreePath();
        path.setId("path1");
        path.setSource("rootId");
        path.setTarget("node1Id");
        paths.add(path);
        node1.setType(AssociationNodeTypeEnum.KPI.getNodeType());
        TriggerExecutionNode nodeParam = new TriggerExecutionNode();
        nodeParam.setCorrelationDegree(15.0F);
        node1.setNodeParam(nodeParam);

        triggerTaskHandler.setNodeAndPaths(analysisTask, templateTreeModel, nodes, paths);
        Assert.assertEquals(1, templateTreeModel.getNodes().size());
    }

    @Test
    public void setPathRelevanceTest() {
        Map<String, String> pathRelevance = new HashMap<>();
        pathRelevance.put("1", "10.0");
        pathRelevance.put("2", "9.3");
        pathRelevance.put("3", "11.4");
        TemplateTreePath path = new TemplateTreePath();
        path.setSource("1");
        path.setTarget("2");
        TemplateTreePath path1 = new TemplateTreePath();
        path1.setSource("2");
        path1.setTarget("3");
        TemplateTreePath path2 = new TemplateTreePath();
        path2.setSource("3");
        path2.setTarget("4");
        TemplateTreePath path3 = new TemplateTreePath();
        path3.setSource("5");
        path3.setTarget("3");
        triggerTaskHandler.setPathRelevance(pathRelevance, path);
        Assert.assertEquals("9.3", path.getRelevance());
        triggerTaskHandler.setPathRelevance(pathRelevance, path1);
        Assert.assertEquals("9.3", path1.getRelevance());
        triggerTaskHandler.setPathRelevance(pathRelevance, path2);
        Assert.assertEquals("11.4", path2.getRelevance());
        triggerTaskHandler.setPathRelevance(pathRelevance, path3);
        Assert.assertEquals("11.4", path3.getRelevance());
    }
}
