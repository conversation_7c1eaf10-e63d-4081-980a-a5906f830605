/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.importpackage;

import com.huawei.digitalview.commons.cms.PkgIntegrityCheck;
import com.huawei.i2000.cbb.security.file.FileSize;
import com.huawei.i2000.cbb.security.file.FileUtils;
import com.huawei.i2000.cbb.security.zip.ZipUtils;
import com.huawei.i2000.dvanalysisengineservice.WebServiceTest;
import com.huawei.i2000.dvanalysisengineservice.business.algorithmmanage.AlgorithmManageServiceImpl;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.common.KnowledgeConstant;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.vector.VectorLibraryManger;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.CapacityTaskImporter;
import com.huawei.i2000.dvanalysisengineservice.model.AlgorithmModel;
import com.huawei.i2000.dvanalysisengineservice.model.AlgorithmResult;
import com.huawei.i2000.dvanalysisengineservice.model.EscapePkgVo;
import com.huawei.i2000.dvanalysisengineservice.model.PkgManageRsp;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * ImportPackageServiceTest
 *
 * <AUTHOR>
 * @since 2023-6-13
 */
@PrepareForTest({ZipUtils.class, FileUtils.class, PkgIntegrityCheck.class, ImportPackageMethod.class, System.class, ImportPackageService.class, FileSize.class, VectorLibraryManger.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class ImportPackageServiceTest extends WebServiceTest {
    private static final AtomicBoolean DATALOAD = new AtomicBoolean(false);

    @Autowired
    ImportPackageService importPackageService;

    @InjectMocks
    @Autowired
    CapacityTaskImporter capacityTaskImporter;

    @Mock
    private AlgorithmManageServiceImpl algorithmManageService;

    @Mock
    private VectorLibraryManger vectorLibraryManger;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATALOAD.compareAndSet(false, true)) {
            return;
        }
        setSqlScriptEncoding("UTF-8");
        executeSqlScript("classpath:database/dvanalysisengineservice_clear_all_table.sql", false);
        executeSqlScript("classpath:database/dvanalysisengineservice_trigger_algorithm_zenith.sql", false);
        executeSqlScript("classpath:database/dvanalysisengineservice_knowledge_repository_zenith.sql", false);
    }

    /**
     * installPkgTest
     *
     * @throws Exception Exception
     */
    @Test
    public void installPkgTest() throws Exception {
        String pkgFile = "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";
        EscapePkgVo escapePkgVo = new EscapePkgVo();
        escapePkgVo.setPkgFile(pkgFile);
        PkgIntegrityCheck check = PowerMockito.mock(PkgIntegrityCheck.class);
        PowerMockito.whenNew(PkgIntegrityCheck.class).withArguments(Mockito.anyString()).thenReturn(check);
        Mockito.doNothing().when(check).verify();
        if (System.getProperty("os.name").startsWith("Windows")) {
            ZipUtils mockit = PowerMockito.spy(ZipUtils.getInst());
            PowerMockito.doNothing().when(mockit, "checkFilePath", Mockito.anyString());
            PowerMockito.mockStatic(ZipUtils.class);
            PowerMockito.when(ZipUtils.getInst()).thenReturn(mockit);
            FileUtils mock = PowerMockito.spy(FileUtils.getInst());
            PowerMockito.doNothing().when(mock, "deleteFile", Mockito.anyString());
            PowerMockito.mockStatic(FileUtils.class);
            PowerMockito.when(FileUtils.getInst()).thenReturn(mock);
        }
        PowerMockito.mockStatic(System.class);
        PowerMockito.when(System.getenv("AIOpsMenuConfig")).thenReturn("faultAnalysis,capacityEvaluation,indicatorPrediction,tidalScheduling");
        List<AlgorithmModel> algorithmList = new ArrayList<>();
        AlgorithmModel model = new AlgorithmModel();
        model.setId(5);
        algorithmList.add(model);
        AlgorithmResult algorithmResult = new AlgorithmResult();
        algorithmResult.setAlgorithmList(algorithmList);
        Mockito.when(algorithmManageService.getAlgorithm(Mockito.any())).thenReturn(algorithmResult);
        Mockito.when(algorithmManageService.defaultParam(Mockito.anyString(), Mockito.anyString())).thenReturn(new ArrayList<>());
        PkgManageRsp rsp = importPackageService.installPkg(escapePkgVo);
        Assert.assertEquals(rsp.getStatus(),"200");
    }

    @Test
    public void test_checkZipSize_cover_branch() throws Exception {
        PowerMockito.mockStatic(FileSize.class);
        PowerMockito.when(FileSize.checkFileSize(Mockito.anyString(), Mockito.anyLong())).thenReturn(false);
        ImportPackageMethod importPackageMethod = new ImportPackageMethod();
        boolean res = importPackageMethod.checkZipSize("str", new File("file"), new PkgManageRsp());
        Assert.assertTrue(res);
    }

    @Test
    public void test_checkFliesCount_cover_branch() {
        ImportPackageMethod importPackageMethod = new ImportPackageMethod();
        boolean res = importPackageMethod.checkFliesCount("tmpFilePath", "tmpPath", new PkgManageRsp());
        Assert.assertTrue(res);
    }

    @Test
    public void test_checkSign_cover_branch() {
        ImportPackageMethod importPackageMethod = new ImportPackageMethod();
        boolean res = importPackageMethod.checkSign("tmpFilePath", "tmpPath", new PkgManageRsp());
        Assert.assertTrue(res);
    }

    @Test
    public void installKnowledgePkgTest() throws Exception {
        String pkgFile = "UEsDBBQAAAAIAAVcdVmLMAQo6BoAAGYlAAAsAAAAQ2FzZUtub3dsZWRnZS9jYXNlX2V4cG9ydF8xNzI1NTQwNTEzMzU0Lnhsc3jtemVUXMnabuPeNK4JLsFdgzTuEDwQ3N3dGwsSPGhw9+DuGoJrgEDQkOAOQT8yc+7JDCfnru/3vfP06r2rd616V6+q5/WtJAsDiw2AByACAAAyAJgbMmYLBQDAwgEAWABEaG1RO1tnE1tnPVUPexMnHSZ3G2vSHFho2iwANOAf/H+NMtUPtt0soDaNbSGxdvWw7NCSEkTiZmJtym8Iye5xmpT1mwS0Nz40pi/UqhC7qF5Yj2lTnzzHL1+gfikivG7NdPVSkVIfpfBNueORwK1Upjgkr0z9TU1MBGcWPtrBAfxUHYJc44IhkyX5PTkVLmctMvtW17HCd5HS10KiVG10X0ugf3zaEfjQ/RJa9pkLviGPe6AyI6HMskZr6ws2dnXKyjSnJ2pMCRdvnmFXKTXVXpFIbw9NYmfpVJkT8ImSCFuUQRXZsJGgsocggapYsY9JzfzhmSmWmoca2ZB7Fty99Xck8ZvT1yvqXfRu+CETMZFqWYoz1W5NlhvRKCcpTie7HIP55WGMQ1omuwoXSHfu+CbeZ3YMeSd4AWPfSDNYY4482oIQnUJAFkuA5eY9jFNip3Lob0hHp+rceId41wnAqGkenuh3C4AkNwO82Aq+M1TYANRdUUfzT+eRxD9KF4FL6TlgkpXEIyDR3YOe3d/DAJT+pqZ1aiwUpw8juYdZlAc11XM0sXZiZvp5/Uc9/8FPlMXJK/awoIYcSN2EDl7MLMWARGTdwFItZKq+/tKf0eve5FISLXXQEINUCQNhJP3afG+GFpeev9keJDtf4yiLwMTmp6h3Kw9JunJwQaCjdkyUcChb7cVwnTmbOU+RlPGgdW2LpittLMqW/fCFQmLqSKwKNMakQYiElU8cgiuQEqDVL/9y2fCyAmpGRGWvPOR9A3fWdvxYmt9cYsF3NKHXvHK7kyk4g06GZvMV3oPWZGc/CNVUzHkt3htmOPKRe+gZW3eeiKV1YN16Myq8G1Qm58C8ZBuTHKDKOvPTquU4+pju0cuN1FM6ePgBEkFStAz1OyW55As4T3zQA3T4h+/DE3drZjc7RytDOzurn34sT1VHcZEF2yez9l7/hwClwgKHWJ65Taj6nqg5iztYxHaNCzcqfR7IKHJzoUMjn30aDJ5eqKp08o46OBLgXVKFXmK3aYwjoJaEkpD7WrLi+HZVKMlMFxJbpYqrSvuB7aYTVfCJ351ztfqVbfb0E2iABdsrLe3CdrvjOLVp4uJU6dcMbyuKgrN1fxTMRuIWGA+rd6MHkp7TD1Htg5RvcVHrXh2/G1FdVpVbAIqxgxnHuakq5aPVN+0CSovarSyZNdTQQChMjT7mYNaSY+f2OJmpYMA8EZsYEVoH/h2lP/WuG2S8Sx1nrI7N0iIasHAgRfKNJkBxcBnDGji2YiMFo1fjte/YaJS/7lYd7qZ5xI6PJBgp0alVrqHM0+qWg+b2WeS54EvHrgq+HXKtuQ7zTTYqFszpT5VwDWWDh4ghliPn1M4qioRMpITAHNreO3ezLac3yPV5b94n6W88fWorQUwSKtqAGXkkrllAVWddkgdHYuviM+3EeF6rumJ/mnZr02hqX9Wpl4WjlVrobDBazPgiVzzE7PsbOB1uFctPbsWzEBx615PuxNND4E0oq8X+9BVpQ+50W0cicNh5Ie3gsyXswck8ulQnXFuos/BkxJ5Neo9H/Df1r3xUDdGsBNRg7L5lhnDqZVICCV1MoKQumzR1zfoELR+wUJFUYsCiwtzoo03BE+cQhJjZwHHF5u8UlFJEus+n3FHro7euNORJvBwhOvIta9Y2GQKrIeZOIybZswTnOcb4kLLQJViHHhei9q/BjOzEVEf63BdvjtSQ3olOMsFe8NsSLK9z7PCkd603mUbH30NUtYUCXi1SzHvedc+oQgcvnO2xGuWpNIRm4OXJG92/VBAHPnVL18av7WpPjL7/LYUDBBOSTh5GzQ80JgQgQj1Q+E9T/1ci/x+zD/Vg9qH+70bh/32UKcvL9j5EJSh3oSxuaa44GPPOzOS7ajvoiB9qny1Sk7H7nn4homvW0ZyVZYaxC/E4GBlwveAErsFgnXXRF4VjwnpiWR/ww9mNnIzAIBrulT+FSMJhYe5u75TuoQBTnbCaBOMGGTKweb5e0lIRhOYWkIudNk2GcFiOsriwWTqNPxl6MHsfazf8r4JUdsshtQ0NpKnPTDfvm0QXz+EzIR4kEycESNLP0TwAE9+myegvu8TPEY9bb0zlPUatunKvUDeJS2WflnAKeIc4k4YOSpL4PYtz2sxJaVE7WHUWuR561rF4zPDx9MB048PAzBfA7ygjRhYSSQoDACQ/WD18wL+tnpO5iYmzE/MfN9afrHkX/8VukRQ08IVmz0+4deCT7UK5+rsSLZxy9PgCF+1MJMEB2ptDwFZowe0Bv9Sa6pCs6kd4b/vMgyfENaHp6sYwo7JWllrGvFT6QTZ5L2dO58b6NtipBO2n7E3Qpp3RMwSHhXyTbcq1r4GkIEp8vSmmoHbD4Qggx0XKXteKnFX6Vyi/pfAfy1B2cfHqZcMn/AVNHKfCRZ85URfrlTvjcQLhsYAghHCEyC9u1Rc0qbhzKxxuO9mZiKVeYgz5cQaZWQnmLwQqI128i1kOdFg3IbvE7VS+3DQpxh1Kk8o8OzZEbJQ1osNJ37/UePX3STUP39PJsZoAudierhCotTPnrbkavzX7NMJv4EbBYJtUCj8/fWrn6039Ca/qw9iIxT5J+hhhGA1xDTlwIVUQdyYnz1pQ9uxEu3u2gyC+M4k5SxhW+A0kTjND1mrOYZU91ZHyqpYUswnREUZPuDjVl56F754mrJdV5v2L9b3LvAYpqtulWHgtwaLDHtsOyCsXqODClx4RTf2Gxam3WO4BauHbL5F1NINB1mGJR/UGsUc68g6lDdVIT7QEqfxYL/XPdiZwKQh9WGY3Am8nCsV4LHWv3lNekPw8+z8IkDAhD+wmBcGtlIKApJsDTIQyOfltCGBMjRls5bNzBRWRgOAw/mQ7zdbQU7ctCFcXbdNzXfPvrH2DQiO7q9N1+G3FLPUtPrUnsyPZAyNhBlR79Z4BOsnujZXhMtV00ta1VrZltWTcuqmj8glcX12ZbIbK5SVI1cL7717DtzzTUhG/NuhRByaAX6ZbIYDFGY28lccKebYip3I7BUC8d3so//6fctGyyNBgVLFKTEeA0OvLLodcIjzICpIWjQJCHRbbi4kpoj4oN486qTYt80SR0YH0Lulwu23kUMQ71ZL3gzJD6WUmzz7pMXFw1srP2lusitBU2o+nkeJzN7XonKZFYQDXmOai2PJTd0UpIqmPgL5+FxvccPIy9VsaCWOg3uRwmugrZLVhxiqKjUG91VQmq83xdCCom03ihoZr65NhmufliwOh3lEb6uNNot9pVc77e3RdBABghPRnXvyHVjmbm9iY/Hn9Q6F2X2pHEHFj+zKfCqGe7F9yrX/EtDviXDTmRnpJkx+tpX1OW0pAIL38BikgTvCtSHZMYiu2jN6VkgwKI0ct+i3zUtz4x+hvsaa7sbOOFWlxiUs6uNE36bm5TlGC6bzNl6PpyZ6SySjdcYl67UrHAu8iPyVTTiX6OfHQcngrSiVEYpgg2f5AYbhxq//iu0O+EIcRVYVwHPZJhFEozrFejSEcA5XCIWDEHvqLPH50bJCkIGMULx98Gyujuky+FqsE5VEbbF/W+YKxiMSn69sJZj/vs8MGdMEQIT8vXsnMbrEwbEt9sR/tqVDaEEJ+QQb6IM3XTJvLGDmMJAVENetk7RQ+0eRkqxGJepNCvg7Xhhg2JiKqtjGcG/BXNPEfYftDcQRoMp+WUaNBWIrqNK44/YdcXnUsV55Z0fsQ+ktQaFpYRpTX6H6HJ5gF5JM8afY6062zXkW7neFMW8y+qjskTNhGnz0MBOO1mq9y0vaFWl2+n9o9tb4lTFizsrr0LF5C2U70RQunlQbOjee/V99j0miByOEvOXSnGbAUabBt8iJXG7zVsQxHkt0oeBUxYdOPAFASDqeUft6UvIpECGZzw6BCeOfeYkwIXhtR6oZFTaLLBxcj38/YZQl9f53DGIlqYYKnST4bkiCQge/AFs94z2Z+hja4LhmtsH/v7nhfGI8u9LzpXoerQd3kw16WseXQ7dlZoZ/fUcDFFrre3dlKjfP2lLvBWqb39UR8pp9Pqqemw374QKbf/cbAAYnQzUa03VOe2gZePpZVUUyjVZ2+gWU03B7oJ+frMHeGmD2IGLLQ4lmL2c3MFCjuLgFFKHNd9Sh2DvoEeIeTN/UmO769W3E8pu2Yfbj20awLBp+/+9NbMvX09HiwuSiOblHvw4tIVjS10FZBf84c89cNkzXh7k5MmXsmQ5m6Biur6DMAZHUt2/cRYejYj8Uwg8jnXQge8b3m0WGjRFAn3xThpwxpZHgFL1LE+GV36Zgvsll9qUeSC7MeLge4HpqsZdsa9BWzj+mtKLbK1GqLlAQTxQWet/HalYhIXmEyW6VQv0ijcYWDZiXGKX0ny/77gwlxa4omylCm9JbXrFjzRqFLiH3shSOUYXK7LWsgZWyPuRTnrovkNtFp9l4oKqIenwh9+s9j1JA04YoCaQ9Ikd3XzxgfZZPnrx3ANIgtmgUjRhE6i+94anLZM7WK2yJYxQfMI6vcTnRC4Z5w1XhnEPnwQqvPd1X0NkfuvlIbQ8L9NOUywie+j2mQvyuYUY12vsuogDEGTcW6lsahF1ykXYxS+C5epnJiIhupVSYLOcjbgW8zFfBEwvGC26LtKdnOrvJYd3olim3fgcc5/u7HReZmZjB8x3tU0LXTtenrIk5wBosoLFxIhC24F9mPKWdUjZHLbUh+Vz4tFAqZonQ5TPp4eG1LdR7rjt8kusWfNxAyUH6pJpMun1DtVNI5H3SNnsgpwKFYYcQjPcu701rCW2l5c++jVmPBnDnHtlU37MG47nntyVgnp9subBLv5uE8RrP86TgFJHOxCVnTyW4RhF4274fBn96jXYp/VQcww/AeJpTDtR2KTTziiT/AINiTNIcvz6621VcQL/CkayXAGmed7GnODVf8PutT6NE60G9RWPNKXWdkOhj6g3crdkV/tiyVinN5htuB+raLkHNJS5ksTMJuBL6BnCnRCxf8F3pAEewM0yDd+RTNCWfNCaXtpGwtCYdNZBYbHBz3zfMV8bbNUPACOMSUOGHLCKewNqU1sgJ5NEXCM569OAVKyZCZnTTaWWW+2cHD0sWiREAyj5Ei0rnmDcskCwRvlYF/JPH6ZX9zSug0m2KTYRORI/v2mzkMK3HywsYAamqV8V5+2x91lohJ9BQcVj+wvuH3DLjDN1FMa+PlqDT6EEhn2F4mGSQS4FWHiiXwvYpXGIJnjU32Suhl1GP0e4vGMUGcY0Ov4Q9a1lxPFaa3S3UAOfMPurN1UViELOG/yFAgCVhVqbD3Le3Te1pGOU/c3UbyJroNhm+LG5SnqclMjxAV5h8R6Lg9whqQ9h4ndvOH27b+kPlca/gkb0IUBV+EkmtOsnFGN2dYxfCEIj0JvuJ8alf2DtPz5BKY87eKS2YkM0NO8BZfzQr66LanneO/7/BvoJUei05X0+Xb3iowOlZYMTUBMWc8ZJ6vT/tEwXSxMGYOs8m/bFXoCa+pebFYy86AiKo4zXVp4yLBI3BUTSM0mD6q5SLMsCOTfnn19XPrjClCQ8BzT3s/nkLlb0rd6ZySSqf53DHPbDpxmyhqCDEEPZwUtO6WlD8Bm9++16bl7PYlK4LPF6Gw1kDZYylLvDZomt5R3e/qIs9tJC5oOIdp7M6K2N3Xja5c+jLwUTsVhQIXL6U9k9my5+zWp2mtuYOkJM3jC4Uwn5vVW3DNwu408oillxKiZ7pE9ny5C3KbVsMy0WmzQS5v45aXJCFxbelS1qwdtuWmbQNoWmboRqJGCZ3urauSHfcV+FpXvb9zSOVq0Btqf8bV5sET/8Ydn0sdvP1ZGVN+cMlof7pjJ2cPaxOnPxJ7tY+2U8LYSxRH6LO7OkKYoZTVOrEFiXQ4SMXihvxVAx+KTl6bUndBVRkFah5jlOnrkJH5hpR4w7tZ0zgU+0/arqXsHPFuLTttUQuW8TjNilnQiUMgMCS8YhvVWYYZWXhbKfxiUVya2MnSYMxaMsInBdEkfQvW+LzaIENifxAfGMUo0Whs6gfEvgx9Iw7RXUWmryqgPNXLsxiRqfb2NTvz6W3AACtOERj/5IaJlLg0YmZyhErPUNQs4UVNcVVMz8nRh1dSwWaoeepgki0FHFTVb6qWssqSdkf2qfTFJhZAwFy8ibSTo/b+IJEHzxcKqOPbGMjgSQ9Rk8Z2hpSPYixn3Xk2B1M+y8KHTZlk2DPhIsauS5FBDfKays7Vc1XCnblRQvbXsIOy66/YionpkJVjycGiRFjJ8V+Hz7Yu5vQ6FjXDeE3SqAkXhG3cvCJVaNvRPNWlKMwUOZpRTCtxazplRI04rzxvEY5e+fw4XF8c+FJ3H1v4VleII0+YYFGNo7YvUJhQdIcgRwyeqFAn0y5x3BnaVlol4OqzkMIYzjKswfSPzlKkKXdqNagGx538unKkfFaK/ABCej69T4Wxspfp0kZZTlONWe5McKazwXt4e9CTH5CMzM6/tjPY6WvkU6g/X/8KOxxKoH+7yEQW6zq/wpnG0R85CkcD7U0+NP6ZSwWoPVkrrAaBMOn13OkbFtLda0DZuyp3xF4qZBm1CIA1kpKEhNoC06bieGpwTELCQyZxXS7g9HFn2iPbXSY+TajguQ9AzzBIhWkMvzvQpTbv8JVAbl5b3Z92ak4sCJEa9/WxXALCJwaqflXt2YMU1ihzgO2iaBQlptYZnEPvjL+de1E6Sk4Yb0vc5n5wTn1zMy7kuUjgj47vd9aqYITPYDSiL1XCNc/VV+PDAE6Xp+jYd4pZmhFERriboj1cKttCsPXzD923H4/4zIDc/T1LiDequihXoHkr+bSN1By+AofkDdpm0IuqM8sdJW5k6q5zZml3bZrW20yDK5TfFoohNxRXD6OAB2XB/peimBs4mhirODta2Jr9qS9xXZZ/VAoD2wKPfFwWerhC1p9CBz7l6l8Q8Jee7gfJZlBFb+5tTeigmPAif78/8xNxvX5CcwaXAmNQOlcqQd+ZEeJfqO5HnHHwtM7wiOcyDKQlALNT3tJWNZxTgts0kyvlyRZyjZGzhUPVucCrHKVrmaIYogIvdIhI6sCWVtDEPYEwXVIImdxuztO/ZBhP30HYY0Aw60N6r0kxWlC1nzTeYeJCmbCkNGVJ5VSSHcPXLYRo846zY6HGV1EldqgA6HIcN/EVOXeUvUiDBC9ldys5aC2OuCXZ2++Ici4WW1fsCCgwYzW2Dfc8CfVG6KmK3uDtbzNnfhs22a6HkQgUAEDxH5nzn3WXX/nzH1WXoOiu8G4WbPELmDtRd6/42WBUURVu8ka1LRjDqwX4xaSYC+xbrxpik87NTfuxTa606+fFRKbAWIbpZujgDSM88/eHXguMHeAqnu3P2EiMz2MMppjaU2EUFGTCBmI0cVCqgl9Q3uL3+PbOV0UhFccIfp2XDvNg+fAjgmi8rMTS5lgPoZZZoe6AYcNHI5BPQPnl3W6UD8lz29yOUanjsSBp3hh9wRwp3kHxvWN/Q/CO23mGlQ8QtsBm+OPJOdLvdqA+qX839WEkBQsA0Py5A/YPNHE2cVQxcXb+yZfHv1mZDC1sj/VpTaSMac2kTAaNlfTtDQbNpODDN6B/eKBSCccCQsizUF+nIxsbQEvgmtbzo/IOjx4QaRIQ8BCYOqTZ57GwsB2yWxvoQ0+CJmO7YCN0HYPxswnBHFigwG/3wv62GRGFekWYgQAjIDtMoPRnLAzvcweREXBRwLyIeGg0IWeRqH8CPh6ADniOdgTtIQWIg5V03oX+7QEr7aaKqTwcrvHDNAYAEcrYzkjJ0c7eidnIztHkX63Nf4poDwiK05HtYcEPPoC7F/3gk+Yr55C9dxgYsGKvL37ERHlQb5kWRFmT1rFcu2QZCknyGPsQ76QbN+rqsukD4crHJgiHgDxIkzfyNc2+NSVfawyyJyIr0D8jFfViMU/MzMyXw6DmZKhTzbJB6neiNEBUIVjQLEXsD7HJKqC2B672vp3mrNsvmtN8cQ6e819nae3l1Bwqzm+Eu7IkzYGJq4Eqrm/eBrwzKMqCN0CLgiDeuzN+W7jxyFaRGG2u9drke7IWNf/ubG1lOcWRhUsei0Mar2cz+nBnbbW4dDspZwkHJqHwHUZBgFbxidWcK8zy8ZFgX65DS3c4YYv9ou7bTrvzH0mf2+2C89n6OsRDdwlPX61kfBS7gpfcpGpqo/9KG93jvhaqItRKm5MsVyEV11pnl/nZjv088CpmRcgQ+4eDahCStCGmgmQvBemBY+ZdxM1vywmlE/vKbx5odvvARNBfmWhgb/8PEX/hXZy8Qg8LKPhC6jPZcQYfXJDSJj6Zby+0WD7ueX8KB27Fk3nRaUdcLoaYRih6WxjMAf2413HQ9K7sJxJjmZmMQFyntGlQzhGTUQ94CNN+Yat/hM2VU2BpwUxabNPRpC0wkpGsGqapiay/SwM0Liw32N0X+fb1zOuTItnkUh7S+VjlSxYNKZtkBz1siZ6qOTcn+8GGKefoHuiuzRg7NXrRWd4oy5tKi32XHrzKwo/h/vk8ZXRWtSLN+a0sfJffUOQUcAnLVnxegJZgT3VYGhOKV27V5Wqr7cX6q5W4ICtfYBzgS5BsyuReK0/zlIBz0aU5LrAXuWRtVYPgaYzqkCV4KctMy60WFtQxAr39PDy9fC7dvXHfndrOJt7O+Nq4br7XyTL0qWL9xiOlSfOOsBbEHFGFfIlw+B79M5tz45PRPNjO/WFBPS8jvu+hF00ODUclqVdGwYc4S1dKcjI4pIW1GhMV8GuRyij1rLx0b9PAd3Un2uZPr5qvRxwbuaXclD17luUOD/zohf7dmoCCZgT8t3dFHuM3b448FvD3LvZfUfgQ9f+lp/144ePO3i8Mw/9Hn+/x4r/3VP6KaKQ/Fv+XDstjOY8L7b/QjfLfy+6PpTwuLP4CPvpvy4yPBTxOhX6hEPdRYvQf+/8oOPyFcYLfh4qPJTwOmX7hhvB/FUA9Fvg4AvmFO6L/dTzyWOjf/f5fsUT88PRxFPB4+d+N9V9hRvKX5f8y3UqycPA/51AfPtQP9KAj/7nkfwBQSwMEFAAAAAgACVJ1WcbtZYLTAAAAXQEAABsAAABFbnRpdHlLbm93bGVkZ2UvZW50aXR5Lmpzb26NkDEKwkAQResI3mGZWmEVbewsg5rC2IlFSMYQiLuyu1FCCHgKCzsPkN7Oy4jgLZxNiGJn+9/7w8wU3Y4DKExi8pmQxxSjGOeJNsAmbF0QJHogvkQtMxWi/gIHhIzQC3YIE9ZYHgUD6LEGm3yPVocgDdQOek1aiytCblTDER/wMR9y3grCTrRkOl/0P5T5y9fp8rhfH7fqea5aOUIdqmRvEin+7igM5QFV7mdxjPpTbXEYZJpOgp+FXbGVdGZR2qzc1I/RMs1s22sXNjQNCFmJlPINUEsDBBQAAAAIAK5KdVl4mSP5VhUAADMcAAALAAAAcGFja2FnZS5jcmy1mHVQVdvfxjl0HBoJQbpzn8OhU+TQjXR3SUkKSqe0SEnXRVpKQkK6uwUvDYJIHQmRePF6Z67zXme8f/zcM2fOPGuvvWbPfp71/ay1gCDkWSDwLTIIBOCiY7GHyYWd4oJAOGhIgAAEB8BCR9FCRSdGvqcCwb+5fyOwidHlPMy8rOwgZMCtbw0oxHjfG2g1nJ3dae/dJcOFQm8ehgIwgJ8Xqn8jeW8kBMIP3Fz6ACNAj0xIaMa2IhxjPumAGIrQ1fEekrrpBYFAbx7i5QWggH4OD8AF0NwMf4cBlRwgDSB56JnciR+fMaXVUJEuv3e6ZNiPXAtgf+tAgoqCDML5fy+PEoQMQtJvK/QQgcSDY9Ll3cJKN8siSCuWk6cLhPrMIvoVHL7IO8ky6lA976HEdYj3Ug2MvnyzDGXBMehcGIc6aQpzFuYwJ6HcN4vFXu7G3iiER20GjZsZeTgVRBypCCob09GmeyenR+7gSaTqZLZyh2RLAGiGX2kQZc8TPJwEgS3I2f59bMzdnQ/FSm9zqGcaTPCGdlIIPAGGatM3rg0spUotu7amhFfM15fsqTAf7l2GZo8txh0Dsbfe/L5G8rCeOSHXSvx6dN2phQ8Gyw+J9juLIPD8MszucKxKTw+/rscGxEYMzw8S6x+OEV73BlWSHz9UbPqk7VPZEeZHmTf+R45bn1seQk2QJozcd0yHbyDYyEs1Eu1TJbmuAwM3C6v0pCvAErVHnrkjFGhPsQJKsSJ702GfJUc2wwXMWouEfAZdHUgYn3PdIu5ha5latNoRSUanvYX6GSI/DZro6BQwN/DWis/edRweCvbj7RjGhEVYqap1khSiKdzxEZBvU11rhI46dWxF7eAKtp1jCr/jS/ToX+jQCyQ/IBE2oaGMYF2uGbZJdWOPLvdWxlkdMAav115PvkKJvsvEwMAv/FTzLkluD6MfDGWCXFHDqP8I7vly1Fxdjbj5QS1Db27GY/nDODu2YwnpZRB8YPcp2ExdBbwNr69Rskjp2k4AP4NyFyHlwba6gCDQCBBY/5No3/9FtGEA9Hu0Of6O9j1nSytaTTsbJzsnG9p7Vg/d7aztLMzcrWjverjbOj+0c39EC/0r6HzfIgzw8gl8z70gBPhb/ivRLObFjVvPUQXkScZBaMKGz1vyRN/+kGjGfyUaBELy5fNijzqYl5VcDyCrAqVhuA5zEQPuCSNtaqm8sdTJu0133lDpMn6YgwfRWZXKCqTxCZ0JYil38EUpvK3ZbM4vnyrdN/bLNh0Jd9zsOBMyvE5JPzdUKW3Qqnck7J0r4Kwqv+MgEFKeTxR3vn5VOOHDMTIEbvNBnYkk8DTus9z6siYWwntZVlKyKJBK+Ooi7zhiEbxUTBsRK/l0MhXxdRMvMbLAPkCrj1JAw2z3sjXV9DiEgbJCqbLzQjG+XtLdO6KIuaUgIZsV5xkaHc6kra2W4Yt2bSmj/uSjNHvcjEwLf93uWLSwEAbc4y8oXL53Z1YOKQIJ9+yYZu8/Rm0xuw8asr1xdBAIrP2Jo+q/cBQC8Hx3lPVvR+/bOVq5uZs5uvzaTggvDPjBzr/kv+y8PS13he5JhIQxVJe+Ty3QgFBd8PrBTsqf2WmzqEL+yPPkieZnD1Fqn4Sq+9eMufvPqU5wpWfIMLLDRe1AVLZkFybO3H4sF71EDDKlsj2lt9+pBkxcKeFqiSlnE7v5iLX4bWiIxFIW0d0hwDZ0giaLv6/n9iKhdkMVNJdUkQwllWBYahFNRs/NA8MMe9weOyoqKSoSRU5zkFOwjggDLmgL+vKQ9rtrGONvccuZ9PVGGNrN3suEb2ur2tQFvPT6Yyxwco8erl3Qw/JUhp86t+62XcatRO1xbbcktKMSpyPkGco7zoORktNWrqtWPD68Xq3WARcJ6R0UQ0I780gH8YmGYnGzV1bTFMhupCKl8V5ilDtbYg4a+0pwoo4zViAINRgIQnbIuflAyMiYokluvZBiNJL/FYgAajJcCB8E+PaD3lzfPPxRAhq/GBgKAN8HZvvPZeAmofQ/vj/oL6vxkW7asZG/ed4FnZRF7RQVM7l0ZeZdWR+NIJfzhLqGoRD3tLwZj95qQQViNS5u3/UEv50ylaXurK9MFcb9uD1GYMrVQy0MIL4G6+FV9Tl5Qc/tDg3zHNsXfeBRY/GC5J5Yk68ewPzwF7rWCWbKfVMrrwyqzwAZu+g1HMO7j/BWEycjZxrUiUcpES8JD/qQDvXx5c0fBoVuqwIr+1Yj5mOlZQX6hy+1Q9bi4JAhH0lnEsFlnzZ77PypHsLj00B0oav5nXqotuJja6/6LFtJpbheN6OMr7lO8UkrjdUIXY869bZ3lPjo2y8tXz03BtX4ObL3Z1XjcPBVG1bxFLaUHok896b0r5V9LR6KjAJCAuV7A56/xjv+tw7EINA1KhqAcvMH4H1rwP/W8G1KoQN3vmk8VFJUknMHqcFqJHgNdxAUIzeYdRjT1s0YIP52mwkVfDMNMTnQQWhoGCgoP1snLK0+IVJ5P9zEeKry+ihQiOlC76Of6ktnH36JWxHNvhcY4rH7B/FMtwyl3/c0IcGUrb/8+bnBgXSmi4OHYmzJhRwkaAwdwzZ4VYR2y3hN/wnRKLrrjNA9RTX4Fxz2Vn9wTvMr/N5CxSYqNKnzoZgPBceVlzqNHXOZ/uWTht7lD8rzcpmY0PQW11RV3xEVT2gZqTofcl+JdI/cLnRTm2VfVMliRQ9fAhdrbyLM67w06UzjZXx21Y21bD+GPzCnYZemC/q0c+eFRTKude891NZjbLyxAEL1BKoZk9Ov7wmH39uoZ07hEq1vt+U7Cyvm1prlC05/gO5GtYuQOBmmpAWaPDg67CM7mkKrGLXjpHug/kG7iMyENpqJtDKb4xEKWZSDcWYE4/a6vbnvqHm+H1dQqAm1JKKqdESCe6N1WDrjdIm66mrAgZaWviZSdJ+S5A2j74z9atW8WMea1DYZi7NkVqjDsdxGg3WLdvdMdLWqVaFAw0CBvwbBU2ttadgwznrHsrgJAYoMQpO5n4fiwV7hlJC/xCmYA25M8/aIcbx8ASt3IntDoWi+UexDuhQ2qUrGCPO5tE/q9JDyYQN9HewJ4tG+xKPyJmhVYD57J3O2ae/qV1klURaL4MUDutZhLLyXWHXH9ZTvnjpMmtVHILzV26HHwb5EsX9s+aKmyraGt+knzfECN3NzCAis+wlVfkOB+AcrvIIQ/h+w8pf8F1Z+Gux/sML/M6xk1WzZEEw4LYrZ8Y4/iXjIxakXKpr7QnGsvipTM//p6VY0aYLZO0cf6/bo+F1NPG+ty+KAbXxwv1mUMzfSmsQdzpFSBD2cx36NSoaTrMFmu6mAKUb5NvvhIyz57RVjS03rI/8OImOz4D8un41kGNjeYcso/QzrQJwWmZLRMYeJqKpsJU/kYhVdmJzlZg05tjrUOeITp5fiLPpFTw2kVFGJZA8U8NOGfOExHqpORcB1WLU63N5cxempAQSNsDeDu2YWZuO5eBBnVtrlcZiKsjlaWYGdwUzOyIf+utKm2kQL5YR6v4OzgMyYRc3TL5tjhLg1btUgOYZ57RzXmagV1bPi4xusBNxgxfZvrLh9CBHXox8n/R1Y4YMBwA9Y+UsCqr8Y+Mbj7wMz/7e1yC+ZsnwYdfFQVcRQTv6JhBO7b+CQYziYXnV8sC5XIXozO+GO0hfOnsHF7ot9DQq8iaV8wlPny1iq6tfqr9HCmfltEI8ABn/T1+K+jP7l2yv51drdQSu6M9Owh6+bvM2oqyWXiyjiL8ORzu3Mi62q+2Y+Gs8Nz6IIt6CtzWN53IoiH5trwi63ugX10NhupHq9P0jtZCj+pXuUIHsqlPm6pbQ3etZVBlFwJ10LHdRWExfLS4FeFtynzy/7cU2dZDJYyqXEgVI5qATLeDpBpHH3Ba5o5BGz28pEY0qTZP8HNPGIHA8G3cd8G/2eY59dxZFxJ6/kL2FRr0+YEBHSbBLTY83ZnS5Rv4cpgHhn0isqisnzWhYrm7B7XZ41mlg/Ywrmz5jSLq/8ULrZsfB8LFDkwE4xggebEoEGWbUmQEoJUZV9zMd9C15I3++OuR7/CX0ly2Xr84Ny9rnNoXQH2bKVJcepJCLPTkypHDrVxLZRi5Ba9hQPo8RMVXeOAQNVGgP8iDiLuoJUvgNV9hi2k/mKgKUnRGu8Q71hCCXa4Hdrz2ZsUniWXw6KHTqQlTHLLAf9mTb9ivdET4jQD/eLJt4fg+ej5L5SFSamuThuz9Ham0s+E+TEB86+cpAmc2e/pvmgzzNnoeHZG5Jz2+fdaunZ08mCT2elYLmc2k2u2Cf0WKk8uBTe0ThVW5+RonUnHJE08e/jkyYRbhl2tqIjfSrNhbMNovdlu8SvKzqehVyFZssUvc/utBFQti+DWPmbTB5vy76+zjduGq+T6t/9FGZOC8e4tSS88XB5sR2VD8YDfhQdRaugpuj29XN9pkR8HEW7u44py6U/SjJGa5Svi+zcoEBqVVB6WY/DUeXCs+UyDrcmV3AvqUtcTHODAiFYNmifq9MaMSOJzQDWimu4h4+6eJ0gp6ym29qxFqiYmeZ/9SyUc0NXUnTktGT9faVLTTMfT4eTlpUvnGokZGSpiQn9eJzsJbNwgGIvSNpGLeiTUDf9asN0hR180bI2Cese3wU/dYLjVlmQxyjVxJEHha6j4LFusckIxGDTsK/RrUsSn3HwsGmmg/xmbvYDgdU/Ycr/ujr8sE8RAgR/3Kd8k/8Cyk9T/Q9QID8DirVO79MSYRr0q3rmMHCYuFoE6iniTGVGCoQxpR+zsWjd/14B396I29dVapJvbNcpBekrRerQyf0FyqE+JnlmHR/n4ysFteM6eaE5CpnqF4qGc+MvIDWuzz6i1G3MIyT9Y670kqTORs7LgVRZfrIoaL2wWWtqYsnjgBMfG0vKdihXor1Ud64gdQEZTovzrUdBlsM4JB970Wg4svhXkxKhwwrWGtLcDJq1Ls25OatbjuLam9lWGZMajtnbyvuW4hZjdaKvdyafljG2ClWiZTl6ZUweMN7t0Pm4vo9PrPeJL36lc5CWHS0YeiiGS/IIZ2P/j4pYN7AuT4Z3Z2V8sUvIoXrZDVBCb4Di+DdQJFeVtfUvr+z+p0ARAvihAoAADPi+T/lR/p7jil9SZaEZg/TR4fj6fRMi80keMzlEIIiyz3dMdej9OUb3cCiyd0d+nF5XuLGZp44UP9byB3qpYBP7k21nwViIFPiUWQTEEEdEOj/yrnbjUcpMod1XfdhKufaaSM1MDIUuNuXJwvy+v56IDYILvoFXdJIBzfuySqBiPTKf3YwvFKo+wdf74WzCyuHRxbXf1wnl2MW11LTZkxcR89rPWqlNSB5LtTqZfcVFUzPAN2p16OYLuj7KJk5b8TIlyM0Yzh3ZA2vXkCNZ6L/xS6qjXn/HdPFgpy/EZXMBqlv4ck6SSrtiwCFO+0krBLsVFcVHVM+jxdOVvofgIocgbAdltLS4XszZMenj76HKT499/utOhYRo0eISI+/59GxduFLkCaZNkZluFIcicf0VZHXOxpn2FLFzCRFYmvTpT9PNNCckpx+0GtFBXtX01gx67nY6oz5gVGpL5xHmzyhrrbHKNh6DiAMLcahT77skmhFZtzF1zXOUJWc3R23fMvQWue75I6LKLs+mFq2REiOGAyEzFXrf8on85wO7V0oVcTDnthK4E19RRCRJ1kCZgh/8hXIleW8UssJx97iu+ta8A76RAuxawT9ZxHT4VqcKyLeVRhhul7m0BC+cUq441coffVGBKVytyZDTTXKY2lTiF3mgsE1nvF1ZQ+vVVG3vsBDv5dQHnlA4WYOeVO+p6HsU5DQdtr69ZyrBqnHvYEmjtOGl7qidkVzjYLqjp3C4+kG1aA1qldcTHPmcAbuOaMUv0vj8AF9pcyvrifUQu8NugTl4VklGfC9pu4uJFtTtdxVsdiz7Ks4PRYpKHYOcOYyWnoJCf28hWl3XPIAwZahnOG1theGwZ0VChJsGPM342CtWjc0jKU9OkC5IJIZkGGT4/oiJel06C1n5dnv61EKNqVlIjfztzM4YFoaIMBd6OedWs7fIuC/9fO8dNLbttbIe0hsQ1fiuvMeahdRzbLHaaj99wp3pRHoq+iQC0dVRKi/SGzbzbL4z7tRy2a7tjESZJRv7UFNImxPHDVJiPbAMK3zy5uFw/E0hCropRPZ/FyLth4JiLk5dsN9QiHhhEKjgP4Xou/wNp2y/rELV2acuTsJB+MkzaoH6o/A/u7f4UqNAQZMoXQFQrwRMlWOqEuHN2WnuDApYNwln1Kes4To9yUYGGafPmTiHJe3ecUqd+7kbiS7ynKKzG9Nn50wQotfVKsMhWPuzmlfme24DVuEuM1TOjjWbhgAnWTG3b9y4oK5Vtz02F9aChWtjT4UldR7XCAi3FeLUw+k25sOcSr38GHPlniEvbdwDPV1mpZJKmoTjezVpL1ixi/Ck0iq1zgE7L9ExjEO6sLjQHpKCTmWZx0XaVAZ37+hZOXIoYk/KUfUXwOd9496EDqszb4zb8vg5Zwiy+aQECosRdlFYEZGHyb2OJmUUQBkH/7mh+Huq0E9PK//r2tYdNvqA9/3aNWgY3h6z2SeCXMbP+H6JLrK40XhZg70r7032soWiXaQh1gtPUaSRoxAxm5bCRmGfOdZw1cLaQ/RkpFgOtgdj1C9M3VRZPxvFDc8aTmmXuEOg6f0kf/jkbLRrzY/Yd05PyZkSjqlrWg45qh+4xq02zl0YLe13ZxrRjR7SgxUbm7Alw3zjx2IvSM3Y+OyrdZuP1vhZcVFcVT7u2dY87OEJutTuS8CwvB9oLcZX97g9ixcl3aoNSxQBy3x+PNpX7ydVkNdOZfr1A9dVd54U8DaxEFmH7V383iA8+NSb/hMHDWdEJNasNDPHYlXm1D71F37srNTaC+S9Yv6hZktFU8xWwcUe9tnYFxUt6pAK3wxTL0XahYpehoR2OIOpP6Fq+oz00fn8bsv8SZKUBHQ+M5uj6BUbxyuMrVhvV6eNiXNxQxvvL0vGi2eX8/5BbhyreE5QMEJW40J4M7+0yaT8WRpkJ617YdyXn+rZuDyPjaa47kp9SiDfKPuu4AxsL0Z9SUdkU+Vl77V7S37anafDFzXSdXtADDKv9+Linx2tM/Yp0ctG/fybCdtFFNMa6HwfacS63Tl0EMpUNTGJEeQMF69GsdA40eyKeNoIWs1CiysJlsjWIgK2R3DZfOcETPClNtVZ3CI/6X5ysuxWSjP3HGjw4CPeYb2+YGaRDH78f1BLAwQUAAAACACuSnVZzc8LW0oBAAABAgAACgAAAHBhY2thZ2UubWZtzzFuGzEQQNF+T7EXkEQOyRlSnSIXbmIYMJA2GA6HwtrSbrBLI9btzcYpAl/gP/yfPE9Vt7b7pes2LfNxtHsznFflpmX3434cH9/5r07jeSk6vkyXmdv7quPzlVtd1tswPPFNj+NpXm58vT9oU2m9c4D967bMw8vjCQLuHqZLR46jgHWxlMJIGVKFyoqOLOWa0SRTncuUKRkOpURjilPRyNa7iBEF4Ys78x+Wqd2fVy2TtEPOU2tdsN+r6KN32LMeFBz7EIwLKRTyJjNb9S4FXyiBlSTiChvFRJIRo1Okf+qyrtrH+99p7rfbtB0+viedZRPRAynZaCu4HBL240TkKzFy9iJiBEzIGiyhqd4AROtApaJ8kX3zjS/6u+gm+4/b9X/HJilQwBqJfcyl7FMWC94YF4liT8UKmjjZQsQBiWxIYiIVECk5DZ9QSwMEFAAAAAgArkp1WZazriuoEAAAAhYAAA4AAABwYWNrYWdlLm1mLmNtc7WWeTyUe/vHzWIf+5BkGZQwlnuYsWux72VNljIYjGXIvhSabCVZylqWI0Kdg0yqEbINskaWJkSJQtmXivSMTudpec7zOs/v93r1x/2aua77+5257u99fT7XGyDybjMwS8cZxG2wgRjBBUTeBYDIOwcGgVAcABsDs1OcAQgHgYJB9HQA67eFoAIi02WACGUAiOD5AggYBAYzOb+PPbGQl7qxs+3rOhArbZsVihVgZoBYQxl4wNpmX36WFrDwMBgEYUNweBQaUNhJQHiQfyYQ2r6uOIQl3p2AJ7gjtHH+gXg3vAs2EIc4HBTo4euPDwxDKADCu9gUFAAVFEC7VAFVO1qI+S4Eztb9+LfcAOdOwARj1g/CEtxdfQnuKC6AYyfHCGOy9MARwmkXSgLY+6U6mNDXYqxwLh4EX29fdzwugFaZnAzCJNBVDiUKiOysY4Xx6+MIOH+sN0IHF4zz9vXzwRECad/9AlEagNqX54Ip/rcl//1J0QARJPb9QdLOH0IEcdDR8ixgIghE98z5smNL7ZnnIy/6l31qV+7fgWe7GOYwY5kyDh0QRam/fZVqZUFqkBGDVomye+GVft9mQdDp0ZVeuFl88KXVFYJx+cANBVkR+QAALRw0276xSOBMTgrRs2dXSR2OfaIZcUPHLPD5ObRFs6FHd1+WPmtKQRnEonlPt9LC2unKgb0YSEzUOZhw6408a4k1xkvC2KHWDpljp5ZK6TwxHZnSGzzvrbpjA9o8pfBFHpig6qyKSuUICGH1SfX2qUxc6w3/QziqpaZAA3SSJNJ0+/AuQxFNi2jlib255W85OkbmqU11pTJAtBCwIMm96BCpEQ1SxIKEGviO1R3cW0m6l161z+s1GAKiA133B/wAIdopC7FD+aDwCcfZmsORvbnue1Y51geekSbI/b1fekCIBwoG6L68MiFxKD/AFw3f71xGfn0FqmwI7wPRqzlcqSvUaADYdxZwgECfoVAImKEB4NmJ90FhAAsDE5IBRE/PCIH81N6QnZfhrPoSlQVPhn+Wils01H+GanBXgv/BDSZI62x1ypdT70qHHRtjogYwCZgpgU7Nyba0qQ2M492UEusq4Gk5zx8m8GzHCxURVuXh14RgXOuVR6UM0rnOCw6y1phl6PLoe5sUkkdUEboNImKWHdztj+dWyVnnD58OCJ7arBvwJY1IjsR+1CUQeTQ9tDK65ZC9VMHNqHSX4JjU5nA0o2aPcSbVpQbELBAFf/MyMqioTF56UP4AVz77ZJaDwNF5E2zLPh4LlXQww1uDjLrPPR4XHsRn7J20uirC26b00T6ExDseqH7MvbblIYGt/0Iid9xgcfnb2I0Gcl20NkdoX9S4pR8hm+YEsTQn8PnqBIdemtrYfdrG/+wEyv/gBLsA3j+dgP2r+Cx8fWkiObwjdpqqlRSUAWWaWyjsiP378BdZzD9JcKSWkS9sqe+V1Ulu5yfyWIOVs6A97RGPj3SNfWSkdMeCQ5uuJx9viT+BDT6mpcQ88UZM69xJz/UZX5VLKC3YhoQ6SDyZm4/a8+zOVFjmUDF+0w794g+bSXXSUNJuW5Y96yPUhajj6u4rsrpT7CXr1xQKP7zkNHProebXcqjGmvdj2t6878d5hW19jtzsN700OpmVPbyek0C1SasXPgk/rVVPwG6y0R+153Cs96JgiJ+X83myX4Q4cf52rfu3nnmYDYmfzsXuQWR6tfCrZ/u2vGfbY/ymRxRsi0ufHhK0Ke/wSrY5U49iqYdCwjWOB9UFnxJr5dwq4IybhfTeKrun6euTPvenBEOB4B+E5R+c0cyRcm3A+n75VcP5jXGHR+A7X3xWiGdHWPQAhPbxo9JADN+J+G+F+T8JEQyig3OPunxiLLwyOFwdb3J+ncm9BGubiDTmubeNevnU3RexsTL7CaU8/iT8UbZtrjMXv1gnrucY+KVlqCXxSsDGkHmH4y0P0aC4qL36bhYvpfqSVpJhqkhz4QW/y1hut4f7WqjI3zPyaxNneB1C1T+33ki4jS90v0NP3sOYhFzRG4i1cj1juLqIrzQpT0b7PrypS8CUJJyH53X8bhSpm2Nawd+WCDZao/TZmr+menE4GqE/G0VlqDt18zabgSLqRdR08bnj47rFA6blG9bXe3PKmdSqLMULKPClrJqbkecXjWZET8xUkBAhNVWeXiMpIYR2WL/R+qTCetW8mV1QUUHNUn2DttNBSQvtxXGLW/dLbXvxjgbkzqs+wWrx5otVGiTo7ZAzrIYFHfimi8YfdDiUAMyt2nrJdbcuaa+3Rc6wYRO9A/PpMy37ECBK5PY57Jp+ZXIkREvQnJFfIg4htnu33fzIRXNb52iuzK7W7uzJF+JLrS8OqsuJwAb3ng65dFQqKL3QQEWUqJ4E7wY5jC3vE36lkwc2FWi8OjBCcsLGkAwFcpuT9osnxPmJGfjWYxvAbKWREdqL5Ifz9ZJLYvbcpIgXY8zDqHvI15IeNhfOyOUS+DY0ziSstDTdMlRvixtKozYnb7hO4B++h5vuz2dZslS1kWENQN1065hAF5954N+dgiKyyQNENiSNXwCnX2MLP4LOd3xUgAf4/92RzBDU97gECH67Q4/ioPmXIoBRQAOqAFpB0Q6Q/3YXihKDIuZs9TePktkxG6i9f5hzlYRnRS5Lohml5pLCLvimxaAu/tT6UCKILsvJ8oM7GcWayaxfuPi4Rtdz43XK4gEKHWIPCoiKPxFpH5QfXZik5N1yulU4cEI7wiN48n1ntFf15kG/vDqB+Pz8dcr8KN3MXO6rCjthi2bGQrM2scrKfnG3RtsAUO2CjqiSmL+aac+8YfGnLQXu92k2QVweKA8xbheHqepXrCxYo1KDtg/WHI2xy0/70CwSIgXwFHvMvq532R9vy7RDKmNq/GKCtD0Ok08ossfcPWzU1Rf4sNy0opZkfdzoGqKnVTQmT5xHykvCNbbwSbZB4/I9k26QjX/lc8E/ZpAfsRpnLahJygcPVRqNCqVPvLZPM/EhGxbOfHg3OmptZ/cSs95R8xuRFQMQWVEMrH+dJCeYHUVkRdCS3w5/h3BZ2WgpZjAI8p+EG/7dbhC0wAuKB9xprcQAo3kOFJRsf9aQNgYA1E/boIgmPnKny4yE8fCGtgx7m7+PQ1gSUeS8XKliwyEFqhiSD8wZKhmi1aJIEbCoeT3kzslSwM+hAPzQAzSg1gKIkPu0MVrwdYyysuUZaW0J/Wx0gPk/tDWK1k9f2lryL4bF++ACArE+fv9A0xgUrSgMGkD/SdPfQsDn/wXTf1UG+6uyr3QN+UbXtMoQljul7ShuR3k4//9h8lLyjs4eGCuzY2o77rZ9tUSD5Ii4BeMfe0fqy6QTY3epgBzsv6v3KEZYFids2evn3+nm4OofMmcgfYLT5TETm3PL9KFiFtNZ+YkISl3glgn7tOeqwOlHWNIV1cZ36jEUS6nt+PBKwv0rZlSUpFYYWkHpKJ+8/rZXXtVA3kkO3a7H9BTH+qOWnJfdr29eFbWSXahZLXw6zsXVfL9vNMBeToJJYL4r0rlANbjnQ5rsPjPXD/BdMg9kRKvRvIrvm2Oe6c2+r820uuEKkBkOStqRub3ql2XfTa7p8CAqceGcdQznCKVmyDjTREN4tyKGZ0BiPdZcNNA/Grtk2DTTQ3klJtR9+Nvk/TY3t2WOmthCVQi3FpvllUQ2r6sUViMA2L/n7s/8KzBosM0QzE3H2FV9dUFY+f7KkZGQ/+Bfvi9jdyf+fvIy/R0CJ6ho2Ns3zHdaO9hGzyvuEjKaWtd4ZXd5Svs+9oHujRhQmTCXehJGuZ+5+DhlaJg+LUcSbWXCFvU2/HaagsWcRpZ6vK5ySJJ4APbwIKd3Y0hTk8/dNP7ZV8/GAIf0eha32OIndwVRbVeOqEwb+yZn7GGTnbNKrhLYDLrdtbm/PaPquVb0hVqc57TeVsfajZw1A0vxWIzMWFZzK7dmnEO+jmu4OOkVu9kRjpKk1cshPZzKvPR2E41N90p0zet1W/LpU2/m5XCb6F4wTxdKzHrR1fqmpCRgeDyFaIx870ZSnZ2+oC4nGsgwXOT4ICdcXS2Kyn2LcJNNfGpZXqxwGFegqRoeRENgIk27nl+1a+OvoulHaEH/AgRWRKMUVL4h8J/hrzCFf1JhVf6GH0GNyJExdPSsXa/uc8prTFYiiPgE0hKtEJLKZLYmeFNtenhQ7tpuNAUuk/gur7v6+CGyuB5hNZd16WZjaLJJ88JvU5f9DGU0hqcG33/ch+K+W2XWHcO8MGy57Twf0IGL9xsS9PUhTTsAMrvK5CKS+1RscRRPFlnmEZdT5NZyV+FC2R4QWz2K0CoT8DhcIkt44jTTC20HRUSy93FbCZObFSKpa9qk7BxJlhJ2rewK648APkTjMeOSaFxybCu8qNlU73SJjaD9YaHjOB+kMcsTA8FHRbrUiOQHsd3mElN9HvKRvtdUpMIzz6ppcrXsxnHzxxncvci3VxnSB3s+Zfxr+Pdvhfk3/PufKqTxbyC611txbPIzqFu3MWm6XR38u9LesXHR82XkExMW0i2FD/InXIzx5x2Yc4I16HqWYzTd64rJauFPJeOPFN9ZYsigu4SU8n4snOMUcERy1TG5e9hhwOZmIErh6iP4jfCCqUZrao9n8+CAgRPXY3NL1y4f88VTyS/JT7ccxxcouY6ivUtiMGNyDcuhuIiUx5e2+LBSGM8q29rlSSVJNsgps7l5D5J/qzzxk017KqOr1Vk3TUz16cY8RchV3ENmjRV07pW13vZ7kVpFhY2CTptvZLcphVpAw+Vi8DGpZynznbrnNkLF3iFFZBLOMw/rSCBHb+cOLAh/UGLJy7qzBZ4vU+qqdTV2YqpXGW2VHr6UU15njiqPuOYUYowYKW8TT23UFXeK4jpydUhn+SP1bR11PV3roAI1Nx9ZUimFrGR8fSn0FGGq/+MBB/fQD+MnRt9/okYRA5Av2QkKsBV9iy216eu3ak7+kZaNms2mjPRFKAmm9RnKu1sesH1xL/Msplf6rcoQej7JfPyY+rRZadvnwLrr2UIXurdIOtXzQBJYMXR09HlT/ZBn5sUJx0dK06kzJbsHLRgwcyKalEDksRVTQVLS5QR+8a3KXmZ6GXp8ifxDznpsbFkF5/iuyYTomR42qYinyic5tKbN9wecf2f7juBKMcl2Du64H4ThmZX8vCWx/9C50ygimMa/4C/86/gLPOFHKPkefs8WAQLf0y/bDyj1f+Rfsq23/LjqcBFe97WdH91UFI+M9R2bYEGCIeIWsVgvCA0of4958ihJgEYWAG1j6gh3E9I2hUNd1txb7XiC6s3rgxbdnivhxQ3dVbJjBSf+Bpwr/DGLGZxjpkbFtopKl8KaQt1BgJzUwtFlg86HOpYQoCujz9SiM7vAef+U12+PTPaJnApbk/Zhe4OcH+NV2mxH6Jam6D/oKBKL3zR4ltIsejbsIIvUYYifBnmVDg7ve/Tcq8YkEbt1eqHBxuaktENGzEVv/MX1hsfxZdNy9zo/q98YLKa7ahfoJ3eDzast+Vih9kKnXtgpwqPSePpwTOeR1oLsmNy1MTvCZecZF1XHcOVpmLU2W3vi2eP9kpeGhrQEC0yTa+bp1fJeO/R0lF3hKqitM04NZLVtrBQetVwlqScNFhrYZDA4RkVTL3z0735id6A5w/h5RXCbxwhvY0qrgcrivwBQSwMEFAAAAAgAEHlQWVzp6AJUAgAAeQMAABAAAABwYWNrYWdlX2Rlc2MueG1sZVNNTxpRFN036X+gs+fDduNiwJhKXdBo01i7NAReKAFmiICWHTSAMzogaQtGROso+NEWKKnagcHwZ+Z9sOIv9A1vFI2zm/fOPefce+7j5z7HorYNsJ4Ii4Kbm3G4OBsQAmIwLITc3IeVN/ZZbs7z/Bn/wm6HrT3UvoaZgaEVYT5L2hqqy/iyj44HdruJifsDEX8IWPBRpkaGW6TTZHWGXiLXOdQvE/XC0FroR5N0TgKxOKoX4baK9Zyh36BSE+absNMjXRX2bkjpH9ytQulPOA6/KvhXB+6eMiVKT4b7hvYTd3V4tAOH+ZGqUwxTMOknTJSekTHYeCDFxGQ6DsYD2dAyZOuKsQliEKzQY09AjDk+pfybIOwIbjgigrgZBcEQ4J33iDvtMtavYHOPesZyD2XOoZJnGqN6hpxlYaNGzbHG0WETHdM5FMYDxbpt14yejKQyki9ht0LLsf4NHeUmbmz0402TPpC2fidHEZBeFoBN8MeAm0uI0VSSJsY9tsw7GephnRmc0odSAVXyo1rdGKoo2zG0nRkXM2mGWVZwLUdbuDfwVNF0xHkWVtd8S8sf33oXFr1rSZBIPlDknVPXE1WpQFOF5SLt27gd4u8XqNWg20MToVJkeEBUxcR0dQqAZ1+QIpPzU1j4i6o9pEp0bIwBt2QWU2hdTMWXqJvHNt7Nv/bNL3p55/TeMkAzaau4XMAVZXR4YmjbdNi40We9WpszsQcPbkf7eahU2f7QlqzFeCLGehamIliWUP33NDnrKXlWZ12u966Xr3jn3Yk5oekL+Q9QSwECFAAUAAAACAAFXHVZizAEKOgaAABmJQAALAAAAAAAAAAAACAAAAAAAAAAQ2FzZUtub3dsZWRnZS9jYXNlX2V4cG9ydF8xNzI1NTQwNTEzMzU0Lnhsc3hQSwECFAAUAAAACAAJUnVZxu1lgtMAAABdAQAAGwAAAAAAAAABACAAAAAyGwAARW50aXR5S25vd2xlZGdlL2VudGl0eS5qc29uUEsBAhQAFAAAAAgArkp1WXiZI/lWFQAAMxwAAAsAAAAAAAAAAAAgAAAAPhwAAHBhY2thZ2UuY3JsUEsBAhQAFAAAAAgArkp1Wc3PC1tKAQAAAQIAAAoAAAAAAAAAAQAgAAAAvTEAAHBhY2thZ2UubWZQSwECFAAUAAAACACuSnVZlrOuK6gQAAACFgAADgAAAAAAAAAAACAAAAAvMwAAcGFja2FnZS5tZi5jbXNQSwECFAAUAAAACAAQeVBZXOnoAlQCAAB5AwAAEAAAAAAAAAAAACAAAAADRAAAcGFja2FnZV9kZXNjLnhtbFBLBQYAAAAABgAGAI4BAACFRgAAAAA=";
        EscapePkgVo escapePkgVo = new EscapePkgVo();
        escapePkgVo.setPkgFile(pkgFile);
        PkgIntegrityCheck check = PowerMockito.mock(PkgIntegrityCheck.class);
        PowerMockito.whenNew(PkgIntegrityCheck.class).withArguments(Mockito.anyString()).thenReturn(check);
        Mockito.doNothing().when(check).verify();
        if (System.getProperty("os.name").startsWith("Windows")) {
            ZipUtils mockit = PowerMockito.spy(ZipUtils.getInst());
            PowerMockito.doNothing().when(mockit, "checkFilePath", Mockito.anyString());
            PowerMockito.mockStatic(ZipUtils.class);
            PowerMockito.when(ZipUtils.getInst()).thenReturn(mockit);
            FileUtils mock = PowerMockito.spy(FileUtils.getInst());
            PowerMockito.doNothing().when(mock, "deleteFile", Mockito.anyString());
            PowerMockito.mockStatic(FileUtils.class);
            PowerMockito.when(FileUtils.getInst()).thenReturn(mock);
        }

        PowerMockito.mockStatic(VectorLibraryManger.class);
        PowerMockito.when(VectorLibraryManger.getVectorProcessFlag()).thenReturn(KnowledgeConstant.STARTED);
        Mockito.doNothing().when(vectorLibraryManger).initVectorProcess();
        PkgManageRsp rsp = importPackageService.installKnowledgePkg(escapePkgVo);
        Assert.assertEquals(rsp.getStatus(),"200");
    }
}
