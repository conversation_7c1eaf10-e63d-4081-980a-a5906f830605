/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.impl;

import static org.mockito.ArgumentMatchers.anyString;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpRequest;
import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpResponse;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.i2000.cbb.common.CommonConfigUtil;
import com.huawei.i2000.cbb.common.model.ConfigResult;
import com.huawei.i2000.cbb.security.osinject.securityexec.ProcessInfo;
import com.huawei.i2000.cbb.security.osinject.securityexec.ProcessResult;
import com.huawei.i2000.cbb.security.osinject.securityexec.SecurityExecutor;
import com.huawei.i2000.dvanalysisengineservice.WebServiceTest;
import com.huawei.i2000.dvanalysisengineservice.business.common.ConfigurationUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.EncryptionUtil;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.common.InstalledBaseInfo;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.common.KnowledgeConstant;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.ImportKnowledgeFileMessage;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.KnowledgeSourceScene;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.KnowledgeType;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.entityknowledge.EntityRelationType;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.entityknowledge.SolutionEntityKnowledge;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.entityknowledge.SolutionEntityKnowledgeExport;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.entityknowledge.SolutionEntityMeta;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.entityknowledge.event.EventMeta;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.entityknowledge.event.EventResourceDetail;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.entityknowledge.ne.NeResourceDetail;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.entityknowledge.relation.RelationNode;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.entityknowledge.relation.RelationResource;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.entityknowledge.relation.RelationResourceDetail;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.service.CaseKnowledgeService;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.service.EntityKnowledgeService;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.vector.VectorLibraryManger;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.vector.VectorPyExecutor;
import com.huawei.i2000.dvanalysisengineservice.dao.knowledgerepository.KnowledgeEntityDao;
import com.huawei.i2000.dvanalysisengineservice.impl.KnowledgeRepositoryServiceDelegateImpl;
import com.huawei.i2000.dvanalysisengineservice.model.AllKnowledgeQueryParam;
import com.huawei.i2000.dvanalysisengineservice.model.CaseKnowledgeEntity;
import com.huawei.i2000.dvanalysisengineservice.model.ComputeParam;
import com.huawei.i2000.dvanalysisengineservice.model.ComputeParamList;
import com.huawei.i2000.dvanalysisengineservice.model.DeleteParam;
import com.huawei.i2000.dvanalysisengineservice.model.KnowledgeUploadParam;
import com.huawei.i2000.dvanalysisengineservice.model.QueryDocsParam;
import com.huawei.i2000.dvanalysisengineservice.model.QueryDocsParamList;
import com.huawei.i2000.dvanalysisengineservice.model.QueryEventKnowledgeParam;
import com.huawei.i2000.dvanalysisengineservice.model.QueryStr;
import com.huawei.i2000.dvanalysisengineservice.model.QueryStrList;
import com.huawei.i2000.dvanalysisengineservice.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineservice.model.SubKnowledgeCreateParam;
import com.huawei.i2000.dvanalysisengineservice.model.SubKnowledgeDeleteParam;
import com.huawei.i2000.dvanalysisengineservice.model.SubKnowledgeQueryParam;
import com.huawei.i2000.dvanalysisengineservice.model.SubKnowledgeUpdateParam;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.oms.ds.mimcache.MoTypeTreeNode;
import com.huawei.oms.eam.mo.CommonAgent;
import com.huawei.oms.eam.mo.MOType;
import com.huawei.oms.eam.util.PIUtil;
import com.huawei.oms.persistence.Criterion;

import com.alibaba.fastjson2.JSON;

import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.io.File;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@PrepareForTest({EncryptionUtil.class, MITManagerClient.class, ContextUtils.class, SecurityExecutor.class, ProcessResult.class, ProcessResult.class, VectorLibraryManger.class, EntityKnowledgeService.class,
    CommonConfigUtil.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class KnowledgeRepositoryServiceDelegateImplTest extends WebServiceTest {
    @Autowired
    KnowledgeRepositoryServiceDelegateImpl knowledgeRepositoryServiceDelegate;

    @Autowired
    EntityKnowledgeService entityKnowledgeService;

    @Autowired
    CaseKnowledgeService caseKnowledgeService;

    @Autowired
    KnowledgeEntityDao knowledgeEntityDao;

    private static final AtomicBoolean DATALOAD = new AtomicBoolean(false);

    @Before
    public void PreparedData() throws Exception {
        // 保证这个只运行一次
        if (!DATALOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvanalysisengineservice_clear_all_table.sql", false);
        executeSqlScript("classpath:database/dvanalysisengineservice_knowledge_repository_zenith.sql", false);
        PowerMockito.mockStatic(EncryptionUtil.class);
        PowerMockito.doNothing().when(EncryptionUtil.class, "changeFilePermission", Mockito.anyString(),
            Mockito.anyString());
        PowerMockito.mockStatic(SecurityExecutor.class);
        ProcessInfo<ProcessResult, ProcessResult> info = PowerMockito.mock(ProcessInfo.class);
        PowerMockito.when(info.exitValue()).thenReturn(0);
        String result = "\"{\\\"status\\\":0}\"";
        ProcessResult processResult = PowerMockito.mock(ProcessResult.class);
        PowerMockito.when(processResult.toString()).thenReturn(result);
        PowerMockito.when(info.getStdOut()).thenReturn(processResult);
        PowerMockito.when(SecurityExecutor.execCommand(anyString(), Mockito.anyMap(), Mockito.any(), Mockito.any(),
            Mockito.anyLong(), Mockito.any(), Mockito.anyBoolean())).thenReturn(info);
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.getContext()).thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        Mockito.when(contextUtils.getUserId()).thenReturn("1");
        Mockito.when(contextUtils.getUsername()).thenReturn("admin");
        String vectorPyWorkPath = "DVIncidentAnalysis_default" + File.separator + "semantic_retrieval";
        String vectorPyFile = "semantic_retrieval_interface.pyc";
        File src1 = new File(
            ConfigurationUtil.algorithmPackagePath() + vectorPyWorkPath + File.separator + vectorPyFile);
        if (!src1.getParentFile().exists()) { // 如果父目录不存在，创建父目录
            src1.getParentFile().mkdirs();
        }
        if (src1.exists()) { // 如果已存在,删除旧文件
            src1.delete();
        }
        src1.createNewFile();
        PowerMockito.mockStatic(VectorLibraryManger.class);
        PowerMockito.when(VectorLibraryManger.getVectorProcessFlag()).thenReturn(KnowledgeConstant.STARTED);
        PowerMockito.mockStatic(VectorLibraryManger.class);
        PowerMockito.when(VectorLibraryManger.isVectorStarted()).thenReturn(true);
    }

    @Test
    public void deleteCaseKnowledgeEntityTest() throws Exception {
        PowerMockito.mockStatic(VectorLibraryManger.class);
        PowerMockito.when(VectorLibraryManger.isVectorStarted()).thenReturn(true);
        DeleteParam deleteParam = new DeleteParam();
        deleteParam.setCaseIds(Arrays.asList("2"));
        ResponseResult result = knowledgeRepositoryServiceDelegate.deleteCaseKnowledgeEntity(null, deleteParam);
        Assert.assertEquals(0, result.getResultCode().intValue());
    }

    @Test
    public void updateCaseKnowledgeEntityTest() throws Exception {
        CaseKnowledgeEntity caseKnowledgeEntity = new CaseKnowledgeEntity();
        caseKnowledgeEntity.setId("1");
        caseKnowledgeEntity.setSolutionName("SOLUTION");
        caseKnowledgeEntity.setAssociateRepositoryId("ASSOCIATE_REPOSITORY_ID");
        caseKnowledgeEntity.setCaseName("test_modify");
        HttpContext context = new MockHttpContext();
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        Mockito.when(contextUtils.getUserId()).thenReturn("1");
        ResponseResult result = knowledgeRepositoryServiceDelegate.updateCaseKnowledgeEntity(context, caseKnowledgeEntity);
        Assert.assertEquals(0, result.getResultCode().intValue());
    }

    @Test
    public void updateCaseKnowledgeEntityErrorTest() throws Exception {
        CaseKnowledgeEntity caseKnowledgeEntity = new CaseKnowledgeEntity();
        caseKnowledgeEntity.setId(null);
        caseKnowledgeEntity.setSolutionName("SOLUTION");
        caseKnowledgeEntity.setAssociateRepositoryId("ASSOCIATE_REPOSITORY_ID");
        caseKnowledgeEntity.setCaseName("test_modify");
        ResponseResult result = knowledgeRepositoryServiceDelegate.updateCaseKnowledgeEntity(null, caseKnowledgeEntity);
        Assert.assertEquals(-1, result.getResultCode().intValue());

        caseKnowledgeEntity.setId("123");
        ResponseResult result1 = knowledgeRepositoryServiceDelegate.updateCaseKnowledgeEntity(null,
            caseKnowledgeEntity);
        Assert.assertEquals(-1, result1.getResultCode().intValue());

        caseKnowledgeEntity.setId("1");
        caseKnowledgeEntity.setCaseName("test");
        ResponseResult result2 = knowledgeRepositoryServiceDelegate.updateCaseKnowledgeEntity(null,
            caseKnowledgeEntity);
        Assert.assertEquals(-1, result2.getResultCode().intValue());
    }

    @Test
    public void exportEntityKnowledgeTest() throws Exception {
        ResponseResult result = knowledgeRepositoryServiceDelegate.exportEntityKnowledge(null);
        Assert.assertEquals(0, result.getResultCode().intValue());
    }

    @Test
    public void importEntityKnowledgeRepositoryTest() throws Exception {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient mitManagerClient = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(mitManagerClient);
        Field field = VectorPyExecutor.class.getDeclaredField("maxWaitTime");
        field.setAccessible(true);
        field.setInt(null, 1);
        MoTypeTreeNode moTypeRootTreeNode = new MoTypeTreeNode();
        MoTypeTreeNode treeNode = new MoTypeTreeNode();
        moTypeRootTreeNode.append(treeNode);
        MOType object = new MOType();
        treeNode.setObject(object);
        object.setCategory("com.huawei.as.application");
        object.setType("CBS");
        PowerMockito.when(mitManagerClient.getTypeTreeByCategory()).thenReturn(moTypeRootTreeNode);

        HttpServletRequest request = new MockHttpRequest();
        HttpServletResponse response = new MockHttpResponse();
        HttpContext context = new HttpContext(request, response);
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        Mockito.when(contextUtils.getUserId()).thenReturn("1");
        KnowledgeUploadParam uploadParam = new KnowledgeUploadParam();
        uploadParam.setFileName("text.json");
        PowerMockito.mockStatic(VectorLibraryManger.class);
        PowerMockito.when(VectorLibraryManger.isVectorStarted()).thenReturn(true);
        ResponseResult result = knowledgeRepositoryServiceDelegate.importEntityKnowledgeRepository(context,
            uploadParam);
        Assert.assertEquals(0, result.getResultCode().intValue());
        Thread.sleep(1000);

        uploadParam.setFileByte(
            "eyJlbnRpdHlLbm93bGVkZ2VMaXN0IjpbeyJldmVudFJlc291cmNlcyI6W3sidHlwZSI6ImFsYXJtIiwiZXZlbnRUeXBlSWQiOiI0MDEwNTAyMDAiLCJuYW1lIjoiQUxNLTQwMTA1MDIwMCBTUumAmuS/oeS4reaWrSIsImRlc2NyaXB0aW9uIjoiQUxNLTQwMTA1MDIwMCBTUumAmuS/oeS4reaWrSIsInJlY292ZXJ5U3VnZ2VzdGlvbiI6IiIsImV2ZW50SW5mbyI6eyJ1cHBlclRocmVzaG9sZCI6IjkwIix9fV0sIm5lUmVzb3VyY2VzIjpbeyJuYW1lIjoiYWNjZXNzZmFjYWRlX2NsdXN0ZXIiLCJtb1R5cGVJZCI6ImNicy5iaWxsaW5nLmFjY2Vzc2ZhY2FkZS5jbHVzdGVyIiwiZGVzY3JpcHRpb24iOiJhY2Nlc3NmYWNhZGVfY2x1c3RlciJ9XSwicmVsYXRpb25SZXNvdXJjZXMiOlt7Imludm9rZVJlbGF0aW9uc2hpcCI6ImhhcHBlbk9uIiwic291cmNlIjoiNDAxMDUwMjAwIiwic291cmNlVHlwZSI6ImFsYXJtIiwidGFyZ2V0VHlwZSI6Im5lIiwidGFyZ2V0IjoiY2JzLmJpbGxpbmcuYWNjZXNzZmFjYWRlLmNsdXN0ZXIiLCJkZXNjcmlwdGlvbiI6ImFjY2Vzc2ZhY2FkZV9jbHVzdGVyIn1dLCJzb2x1dGlvbk5hbWUiOiJDQlMifV19");
        ImportKnowledgeFileMessage importMessage = new ImportKnowledgeFileMessage();
        importMessage.setUserId(ContextUtils.getContext().getUserId());
        importMessage.setKnowledgeType(KnowledgeType.ENTITY.ordinal());
        Exception exp = null;
        try {
            entityKnowledgeService.importKnowledgeHandle(uploadParam, importMessage, contextUtils, "terminal");
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);
        uploadParam.setFileByte(
            "eyJlbnRpdHlLbm93bGVkZ2VMaXN0IjpbeyJldmVudFJlc291cmNlcyI6W3sibm9kZU5hbWUiOiJldmVudE5vZGUxIiwidHlwZSI6ImFsYXJtIiwiZXZlbnRUeXBlSWQiOiI0MDEwNTAyMDAiLCJuYW1lIjoiQUxNLTQwMTA1MDIwMCBTUumAmuS/oeS4reaWrSIsImRlc2NyaXB0aW9uIjoiQUxNLTQwMTA1MDIwMCBTUumAmuS/oeS4reaWrSIsInJlY292ZXJ5U3VnZ2VzdGlvbiI6IiIsImNhdXNlIjoiIiwiZXZlbnRJbmZvIjp7InVwcGVyVGhyZXNob2xkIjoiOTAiLH19XSwibmVSZXNvdXJjZXMiOlt7Im5vZGVOYW1lIjoibmVOb2RlMSIsIm5hbWUiOiJhY2Nlc3NmYWNhZGVfY2x1c3RlciIsIm1vVHlwZUlkIjoiY2JzLmJpbGxpbmcuYWNjZXNzZmFjYWRlLmNsdXN0ZXIiLCJkZXNjcmlwdGlvbiI6ImFjY2Vzc2ZhY2FkZV9jbHVzdGVyIn1dLCJyZWxhdGlvblJlc291cmNlcyI6W3siaW52b2tlUmVsYXRpb25zaGlwIjoiaGFzQWxhcm0iLCJzb3VyY2UiOiJuZU5vZGUyIiwidGFyZ2V0IjoiZXZlbnROb2RlMSIsImRlc2NyaXB0aW9uIjoiYWNjZXNzZmFjYWRlX2NsdXN0ZXIifV0sInNvbHV0aW9uTmFtZSI6IkNCUyJ9XX0=");
        try {
            entityKnowledgeService.importKnowledgeHandle(uploadParam, importMessage, contextUtils, "terminal");
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);

        uploadParam.setFileByte("");
        try {
            entityKnowledgeService.importKnowledgeHandle(uploadParam, importMessage, contextUtils, "terminal");
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);

        uploadParam.setFileByte(
            "eyJlbnRpdHlLbm93bGVkZ2VMaXN0IjpbeyJldmVudFJlc291cmNlcyI6W3sibm9kZU5hbWUiOiJldmVudE5vZGUzIiwidHlwZSI6ImFsYSIsImV2ZW50VHlwZUlkIjoiNDAxMDUwMjAwIiwibmFtZSI6IkFMTS00MDEwNTAyMDAgU1LpgJrkv6HkuK3mlq0iLCJkZXNjcmlwdGlvbiI6IkFMTS00MDEwNTAyMDAgU1LpgJrkv6HkuK3mlq0xMjM0NTY3ODkwMTIzNDU2Nzg5MDEyMzQ1Njc4OTAxMjM0NTY3ODkwMTIzNDU2Nzg5MDEyMzQ1Njc4OTBoc2Z1aWhkZmxndWloZG9zZnVnaG9pYXNkaGZsdWhhc2RvZnVobHNhaGRsY3Zkc2Jhb3ZidWdoYXZiYmhld3B1aXJmMmVpaHB3cGlmbnZsc2N1eGpuYnZ1d2Vob2diZXFsdWZib2VydWdob3BiIiwicmVjb3ZlcnlTdWdnZXN0aW9uIjoiIiwiY2F1c2UiOiIiLCJldmVudEluZm8iOnt9fV0sIm5lUmVzb3VyY2VzIjpbeyJub2RlTmFtZSI6Im5lTm9kZTEiLCJuYW1lIjoiYWNjZXNzZmFjYWRlX2NsdXN0ZXIiLCJtb1R5cGVJZCI6ImNicy5iaWxsaW5nLmFjY2Vzc2ZhY2FkZS5jbHVzdGVyIiwiZGVzY3JpcHRpb24iOiLku4vnu40ifV0sInJlbGF0aW9uUmVzb3VyY2VzIjpbeyJpbnZva2VSZWxhdGlvbnNoaXAiOiJoYXNBbGFybSIsInNvdXJjZSI6Im5lTm9kZTEiLCJ0YXJnZXQiOiJldmVudE5vZGUzIiwiZGVzY3JpcHRpb24iOiLku4vnu40ifV0sInNvbHV0aW9uTmFtZSI6IkkyMDAwX1VPQSJ9XX0=");
        try {
            entityKnowledgeService.importKnowledgeHandle(uploadParam, importMessage, contextUtils, "terminal");
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);
    }


    @Test
    public void computeSimilarityTest() throws Exception {
        ComputeParamList computeParamList = new ComputeParamList();
        ComputeParam computeParam = new ComputeParam();
        computeParam.setStr1("test1");
        computeParam.setStr1("test2");
        List<ComputeParam> paramList = new ArrayList<>();
        paramList.add(computeParam);
        computeParamList.setDataList(paramList);
        ResponseResult result = knowledgeRepositoryServiceDelegate.computeSimilarity(null, computeParamList);
        Assert.assertEquals(-1, result.getResultCode().intValue());
    }

    @Test
    public void queryAlarmKnowledgeTest() throws Exception {
        QueryEventKnowledgeParam queryAlarmKnowledgeParam = new QueryEventKnowledgeParam();
        List<String> alarmIds = new ArrayList<>();
        alarmIds.add("1235671");
        Map<String, List<String>> map = new HashMap<>();
        map.put("alarm", alarmIds);
        queryAlarmKnowledgeParam.setEventQueryMap(map);
        queryAlarmKnowledgeParam.setSolution("test");
        ResponseResult result = knowledgeRepositoryServiceDelegate.queryEventKnowledge(null, queryAlarmKnowledgeParam);
        Assert.assertEquals(0, result.getResultCode().intValue());
    }

    @Test
    public void queryDocsTest() throws Exception {
        QueryDocsParamList queryDocsParamList = new QueryDocsParamList();
        List<QueryDocsParam> dataList = new ArrayList<>();
        QueryDocsParam queryDocsParam = new QueryDocsParam();
        queryDocsParam.setQuery("test1");
        queryDocsParam.setTopK(1);
        queryDocsParam.setScoreThreshold(2f);
        dataList.add(queryDocsParam);
        queryDocsParamList.setDataList(dataList);
        ResponseResult result = knowledgeRepositoryServiceDelegate.queryDocs(null, queryDocsParamList);
        Assert.assertEquals(-1, result.getResultCode().intValue());
    }

    @Test
    public void queryNerEntityTest() throws Exception {
        QueryStrList queryStrList = new QueryStrList();
        QueryStr queryStr = new QueryStr();
        queryStr.setQuery("test11");
        List<QueryStr> dataList = new ArrayList<>();
        dataList.add(queryStr);
        queryStrList.setDataList(dataList);
        ResponseResult result = knowledgeRepositoryServiceDelegate.queryNerEntity(null, queryStrList);
        Assert.assertEquals(-1, result.getResultCode().intValue());
    }

    @Test
    public void solutionEntityMetaEmptyTest() {
        SolutionEntityKnowledgeExport solutionEntityKnowledge = new SolutionEntityKnowledgeExport();
        SolutionEntityMeta solutionEntityMeta = new SolutionEntityMeta(solutionEntityKnowledge, "test111",
            KnowledgeSourceScene.INCREMENT.code);
        Assert.assertNotNull(solutionEntityMeta);
    }

    @Test
    public void solutionMaxErrorTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        Mockito.when(contextUtils.getUserId()).thenReturn("1");
        KnowledgeUploadParam uploadParam = new KnowledgeUploadParam();
        uploadParam.setFileName("text.json");
        uploadParam.setFileByte(
            "eyJlbnRpdHlLbm93bGVkZ2VMaXN0IjpbeyJldmVudFJlc291cmNlcyI6W3sibm9kZU5hbWUiOiJldmVudE5vZGUxIiwidHlwZSI6ImFsYXJtIiwiZXZlbnRUeXBlSWQiOiI0MDEwNTAyMDAiLCJuYW1lIjoiQUxNLTQwMTA1MDIwMCBTUumAmuS/oeS4reaWrSIsImRlc2NyaXB0aW9uIjoiQUxNLTQwMTA1MDIwMCBTUumAmuS/oeS4reaWrSIsInJlY292ZXJ5U3VnZ2VzdGlvbiI6IiIsImV2ZW50SW5mbyI6eyJ1cHBlclRocmVzaG9sZCI6IjkwIix9fV0sIm5lUmVzb3VyY2VzIjpbeyJub2RlTmFtZSI6Im5lTm9kZTEiLCJuYW1lIjoiYWNjZXNzZmFjYWRlX2NsdXN0ZXIiLCJtb1R5cGVJZCI6ImNicy5iaWxsaW5nLmFjY2Vzc2ZhY2FkZS5jbHVzdGVyIiwiZGVzY3JpcHRpb24iOiJhY2Nlc3NmYWNhZGVfY2x1c3RlciJ9XSwicmVsYXRpb25SZXNvdXJjZXMiOlt7Imludm9rZVJlbGF0aW9uc2hpcCI6Imhhc0FsYXJtIiwic291cmNlIjoibmVOb2RlMSIsInRhcmdldCI6ImV2ZW50Tm9kZTEiLCJkZXNjcmlwdGlvbiI6ImFjY2Vzc2ZhY2FkZV9jbHVzdGVyIn1dLCJzb2x1dGlvbk5hbWUiOiJDQlMifV19");
        ImportKnowledgeFileMessage importMessage = new ImportKnowledgeFileMessage();
        importMessage.setUserId(ContextUtils.getContext().getUserId());
        importMessage.setKnowledgeType(KnowledgeType.ENTITY.ordinal());
        Field field = EntityKnowledgeService.class.getDeclaredField("maxSolutionNum");
        field.setAccessible(true);
        field.setInt(null, 0);
        Exception exp = null;
        try {
            entityKnowledgeService.importKnowledgeHandle(uploadParam, importMessage, contextUtils, "terminal");
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);
    }

    @Test
    public void NodeNameUniqueTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        Mockito.when(contextUtils.getUserId()).thenReturn("1");
        KnowledgeUploadParam uploadParam = new KnowledgeUploadParam();
        uploadParam.setFileName("text.json");
        uploadParam.setFileByte(
            "eyJlbnRpdHlLbm93bGVkZ2VMaXN0IjpbeyJldmVudFJlc291cmNlcyI6W3sidHlwZSI6ImFsYXJtIiwiZXZlbnRUeXBlSWQiOiIxMiIsIm5hbWUiOiIxMjM0NTYiLCJkZXNjcmlwdGlvbiI6IiIsInJlY292ZXJ5U3VnZ2VzdGlvbiI6IiIsImV2ZW50SW5mbyI6eyJ1cHBlclRocmVzaG9sZCI6IjkwIiwidXBwZXJUaHJlc2hvbGQiOiIxMjkifX1dLCJuZVJlc291cmNlcyI6W3sibmFtZSI6ImFjY2Vzc2ZhY2FkZV9jbHVzdGVyIiwibW9UeXBlSWQiOiIxMjMiLCJkZXNjcmlwdGlvbiI6IiJ9LHsibmFtZSI6InRlc3QxIiwibW9UeXBlSWQiOiIxMjMiLCJkZXNjcmlwdGlvbiI6IiJ9XSwicmVsYXRpb25SZXNvdXJjZXMiOlt7Imludm9rZVJlbGF0aW9uc2hpcCI6ImhhcHBlbk9uIiwic291cmNlIjoiMTIiLCJzb3VyY2VUeXBlIjoiYWxhcm0iLCJ0YXJnZXRUeXBlIjoibmUiLCJ0YXJnZXQiOiIxMjMiLCJkZXNjcmlwdGlvbiI6IiJ9XSwic29sdXRpb25OYW1lIjoiQ0JTIn1dfQ==");
        ImportKnowledgeFileMessage importMessage = new ImportKnowledgeFileMessage();
        importMessage.setUserId(ContextUtils.getContext().getUserId());
        importMessage.setKnowledgeType(KnowledgeType.ENTITY.ordinal());
        Exception exp = null;
        try {
            entityKnowledgeService.importKnowledgeHandle(uploadParam, importMessage, contextUtils, "terminal");
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);

        uploadParam.setFileByte(
            "eyJlbnRpdHlLbm93bGVkZ2VMaXN0IjpbeyJldmVudFJlc291cmNlcyI6W3sidHlwZSI6ImFsYXJtIiwiZXZlbnRUeXBlSWQiOiIxMiIsIm5hbWUiOiIxMjM0NTYiLCJkZXNjcmlwdGlvbiI6IiIsInJlY292ZXJ5U3VnZ2VzdGlvbiI6IiIsImV2ZW50SW5mbyI6e319LHsidHlwZSI6ImFsYXJtIiwiZXZlbnRUeXBlSWQiOiIxMiIsIm5hbWUiOiIxMjM0NTY0IiwiZGVzY3JpcHRpb24iOiIiLCJyZWNvdmVyeVN1Z2dlc3Rpb24iOiIiLCJldmVudEluZm8iOnt9fV0sIm5lUmVzb3VyY2VzIjpbeyJuYW1lIjoiYWNjZXNzZmFjYWRlX2NsdXN0ZXIiLCJtb1R5cGVJZCI6IjEyMyIsImRlc2NyaXB0aW9uIjoiIn1dLCJyZWxhdGlvblJlc291cmNlcyI6W3siaW52b2tlUmVsYXRpb25zaGlwIjoiaGFwcGVuT24iLCJzb3VyY2UiOiIxMiIsInNvdXJjZVR5cGUiOiJhbGFybSIsInRhcmdldFR5cGUiOiJuZSIsInRhcmdldCI6IjEyMyIsImRlc2NyaXB0aW9uIjoiIn1dLCJzb2x1dGlvbk5hbWUiOiJDQlMifV19");
        try {
            entityKnowledgeService.importKnowledgeHandle(uploadParam, importMessage, contextUtils, "terminal");
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);
    }

    @Test
    public void queryAllEntityTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        Mockito.when(contextUtils.getUserId()).thenReturn("1");
        KnowledgeUploadParam uploadParam = new KnowledgeUploadParam();
        uploadParam.setFileName("text.json");
        PowerMockito.mockStatic(VectorLibraryManger.class);
        PowerMockito.when(VectorLibraryManger.isVectorStarted()).thenReturn(true);
        uploadParam.setFileByte(
            "eyJlbnRpdHlLbm93bGVkZ2VMaXN0IjpbeyJldmVudFJlc291cmNlcyI6W3sidHlwZSI6ImFsYXJtIiwiZXZlbnRUeXBlSWQiOiI0MDEwNTAyMDAiLCJuYW1lIjoiQUxNLTQwMTA1MDIwMCBTUumAmuS/oeS4reaWrSIsImRlc2NyaXB0aW9uIjoiQUxNLTQwMTA1MDIwMCBTUumAmuS/oeS4reaWrSIsInJlY292ZXJ5U3VnZ2VzdGlvbiI6IiIsImV2ZW50SW5mbyI6eyJ1cHBlclRocmVzaG9sZCI6IjkwIix9fV0sIm5lUmVzb3VyY2VzIjpbeyJuYW1lIjoiYWNjZXNzZmFjYWRlX2NsdXN0ZXIiLCJtb1R5cGVJZCI6ImNicy5iaWxsaW5nLmFjY2Vzc2ZhY2FkZS5jbHVzdGVyIiwiZGVzY3JpcHRpb24iOiJhY2Nlc3NmYWNhZGVfY2x1c3RlciJ9LHsibmFtZSI6InRlc3QxIiwibW9UeXBlSWQiOiJ0ZXN0MiIsImRlc2NyaXB0aW9uIjoiYWNjZXNzZmFjYWRlX2NsdXN0ZXIifV0sInJlbGF0aW9uUmVzb3VyY2VzIjpbeyJpbnZva2VSZWxhdGlvbnNoaXAiOiJoYXBwZW5PbiIsInNvdXJjZSI6IjQwMTA1MDIwMCIsInNvdXJjZVR5cGUiOiJhbGFybSIsInRhcmdldFR5cGUiOiJuZSIsInRhcmdldCI6ImNicy5iaWxsaW5nLmFjY2Vzc2ZhY2FkZS5jbHVzdGVyIiwiZGVzY3JpcHRpb24iOiJhY2Nlc3NmYWNhZGVfY2x1c3RlciJ9XSwic29sdXRpb25OYW1lIjoiQ0JTIn1dfSA=");
        ImportKnowledgeFileMessage importMessage = new ImportKnowledgeFileMessage();
        importMessage.setUserId(ContextUtils.getContext().getUserId());
        importMessage.setKnowledgeType(KnowledgeType.ENTITY.ordinal());
        PowerMockito.mockStatic(SecurityExecutor.class);
        ProcessInfo<ProcessResult, ProcessResult> info = PowerMockito.mock(ProcessInfo.class);
        PowerMockito.when(info.exitValue()).thenReturn(0);
        String resultStr = "\"{\\\"status\\\":0}\"";
        ProcessResult processResult = PowerMockito.mock(ProcessResult.class);
        PowerMockito.when(processResult.toString()).thenReturn(resultStr);
        PowerMockito.when(info.getStdOut()).thenReturn(processResult);
        PowerMockito.when(SecurityExecutor.execCommand(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(),
            Mockito.anyLong(), Mockito.any(), Mockito.anyBoolean())).thenReturn(info);
        Exception exp = null;
        try {
            entityKnowledgeService.importKnowledgeHandle(uploadParam, importMessage, contextUtils, "terminal");
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);

        AllKnowledgeQueryParam queryParam = new AllKnowledgeQueryParam();
        queryParam.setSolutionName("CBS");
        ResponseResult result = knowledgeRepositoryServiceDelegate.queryAllKnowledge(null, queryParam);
        Assert.assertEquals(0, result.getResultCode().intValue());
        neEventRelationTest(result);
        relationTest(result);
        eventTest();
        neTest();
    }

    private void neEventRelationTest(ResponseResult result) throws ServiceException {
        SolutionEntityKnowledge solutionEntityKnowledge = (SolutionEntityKnowledge)result.getData();
        SubKnowledgeQueryParam subQueryParam = new SubKnowledgeQueryParam();
        List<String> ids = new ArrayList<>();
        ids.add(solutionEntityKnowledge.getNeResources().get(0).getId());
        subQueryParam.setId(ids);
        subQueryParam.setSolutionName("CBS");
        subQueryParam.setType(1);
        ResponseResult result1 = knowledgeRepositoryServiceDelegate.querySubKnowledge(null, subQueryParam);
        Assert.assertEquals(0, result1.getResultCode().intValue());

        SubKnowledgeQueryParam subQueryParam1 = new SubKnowledgeQueryParam();
        List<String> ids1 = new ArrayList<>();
        ids1.add(solutionEntityKnowledge.getEventResources().get(0).getId());
        subQueryParam1.setId(ids1);
        subQueryParam1.setSolutionName("CBS");
        subQueryParam1.setType(0);
        ResponseResult result2 = knowledgeRepositoryServiceDelegate.querySubKnowledge(null, subQueryParam1);
        Assert.assertEquals(0, result2.getResultCode().intValue());
    }

    private void relationTest(ResponseResult result) throws ServiceException {
        SolutionEntityKnowledge solutionEntityKnowledge = (SolutionEntityKnowledge)result.getData();
        SubKnowledgeCreateParam createParam = new SubKnowledgeCreateParam();
        createParam.setSolutionName("CBS");
        createParam.setType(2);
        List<RelationResourceDetail> relationResourceDetails = new ArrayList<>();
        RelationResourceDetail detail = new RelationResourceDetail();
        detail.setInvokeRelationship("call");
        RelationNode source = new RelationNode();
        source.setNodeId(solutionEntityKnowledge.getNeResources().get(0).getId());
        source.setNodeType("ne");
        RelationNode target = new RelationNode();
        target.setNodeId(solutionEntityKnowledge.getNeResources().get(1).getId());
        target.setNodeType("ne");
        detail.setSource(source);
        detail.setTarget(target);
        relationResourceDetails.add(detail);
        createParam.setParam(JSON.toJSONString(relationResourceDetails));

        ResponseResult result1 =  knowledgeRepositoryServiceDelegate.createSubKnowledge(null, createParam);

        RelationResourceDetail resourceDetail = ((List<RelationResourceDetail>)result1.getData()).get(0);
        resourceDetail.setDescription("test1");
        SubKnowledgeUpdateParam updateParam = new SubKnowledgeUpdateParam();
        updateParam.setParam(JSON.toJSONString(resourceDetail));
        updateParam.setSolutionName("CBS");
        updateParam.setType(2);
        ResponseResult result2 = knowledgeRepositoryServiceDelegate.updateSubKnowledge(null, updateParam);
        Assert.assertEquals(0, result2.getResultCode().intValue());

        SubKnowledgeQueryParam subQueryParam = new SubKnowledgeQueryParam();
        List<String> ids = new ArrayList<>();
        ids.add(resourceDetail.getId());
        subQueryParam.setId(ids);
        subQueryParam.setSolutionName("CBS");
        subQueryParam.setType(2);
        ResponseResult result3 = knowledgeRepositoryServiceDelegate.querySubKnowledge(null, subQueryParam);
        Assert.assertEquals(0, result3.getResultCode().intValue());

        SubKnowledgeDeleteParam deleteParam = new SubKnowledgeDeleteParam();
        List<String> deleteIds = new ArrayList<>();
        deleteIds.add(resourceDetail.getId());
        deleteParam.setId(deleteIds);
        deleteParam.setSolutionName("CBS");
        deleteParam.setType(2);
        ResponseResult result4 = knowledgeRepositoryServiceDelegate.deleteSubKnowledge(null, deleteParam);
        Assert.assertEquals(0, result4.getResultCode().intValue());
    }

    private void eventTest() throws ServiceException {
        SubKnowledgeCreateParam createParam = new SubKnowledgeCreateParam();
        createParam.setSolutionName("CBS");
        createParam.setType(0);
        List<EventResourceDetail> eventResourceDetails = new ArrayList<>();
        EventResourceDetail detail = new EventResourceDetail();
        detail.setEventTypeId("test111");
        detail.setName("test2222");
        detail.setType("alarm");
        eventResourceDetails.add(detail);
        createParam.setParam(JSON.toJSONString(eventResourceDetails));
        ResponseResult result1 = knowledgeRepositoryServiceDelegate.createSubKnowledge(null, createParam);
        Assert.assertEquals(0, result1.getResultCode().intValue());
        EventMeta resourceDetail = ((List<EventMeta>)result1.getData()).get(0);
        resourceDetail.setDescription("test1");

        EventResourceDetail eventDetail = new EventResourceDetail();
        BeanUtils.copyProperties(resourceDetail, eventDetail);
        SubKnowledgeUpdateParam updateParam = new SubKnowledgeUpdateParam();
        updateParam.setParam(JSON.toJSONString(eventDetail));
        updateParam.setSolutionName("CBS");
        updateParam.setType(0);
        ResponseResult result2 = knowledgeRepositoryServiceDelegate.updateSubKnowledge(null, updateParam);
        Assert.assertEquals(0, result2.getResultCode().intValue());

        SubKnowledgeQueryParam subQueryParam = new SubKnowledgeQueryParam();
        List<String> ids = new ArrayList<>();
        ids.add(resourceDetail.getId());
        subQueryParam.setId(ids);
        subQueryParam.setSolutionName("CBS");
        subQueryParam.setType(0);
        ResponseResult result3 = knowledgeRepositoryServiceDelegate.querySubKnowledge(null, subQueryParam);
        Assert.assertEquals(0, result3.getResultCode().intValue());

        SubKnowledgeDeleteParam deleteParam = new SubKnowledgeDeleteParam();
        List<String> deleteIds = new ArrayList<>();
        deleteIds.add(resourceDetail.getId());
        deleteParam.setId(deleteIds);
        deleteParam.setSolutionName("CBS");
        deleteParam.setType(0);
        ResponseResult result4 = knowledgeRepositoryServiceDelegate.deleteSubKnowledge(null, deleteParam);
        Assert.assertEquals(0, result4.getResultCode().intValue());
    }
    private void neTest() throws ServiceException {
        SubKnowledgeCreateParam createParam = new SubKnowledgeCreateParam();
        createParam.setSolutionName("CBS");
        createParam.setType(1);
        List<NeResourceDetail> neResourceDetails = new ArrayList<>();
        NeResourceDetail detail = new NeResourceDetail();
        detail.setMoTypeId("test111");
        detail.setName("test2222");
        neResourceDetails.add(detail);
        createParam.setParam(JSON.toJSONString(neResourceDetails));
        ResponseResult result1 = knowledgeRepositoryServiceDelegate.createSubKnowledge(null, createParam);
        Assert.assertEquals(0, result1.getResultCode().intValue());

        NeResourceDetail resourceDetail = ((List<NeResourceDetail>)result1.getData()).get(0);
        resourceDetail.setDescription("test1");
        SubKnowledgeUpdateParam updateParam = new SubKnowledgeUpdateParam();
        updateParam.setParam(JSON.toJSONString(resourceDetail));
        updateParam.setSolutionName("CBS");
        updateParam.setType(1);
        ResponseResult result2 = knowledgeRepositoryServiceDelegate.updateSubKnowledge(null, updateParam);
        Assert.assertEquals(0, result2.getResultCode().intValue());

        SubKnowledgeQueryParam subQueryParam = new SubKnowledgeQueryParam();
        List<String> ids = new ArrayList<>();
        ids.add(resourceDetail.getId());
        subQueryParam.setId(ids);
        subQueryParam.setSolutionName("CBS");
        subQueryParam.setType(1);
        ResponseResult result3 = knowledgeRepositoryServiceDelegate.querySubKnowledge(null, subQueryParam);
        Assert.assertEquals(0, result3.getResultCode().intValue());

        SubKnowledgeDeleteParam deleteParam = new SubKnowledgeDeleteParam();
        List<String> deleteIds = new ArrayList<>();
        deleteIds.add(resourceDetail.getId());
        deleteParam.setId(deleteIds);
        deleteParam.setSolutionName("CBS");
        deleteParam.setType(1);
        ResponseResult result4 = knowledgeRepositoryServiceDelegate.deleteSubKnowledge(null, deleteParam);
        Assert.assertEquals(0, result4.getResultCode().intValue());
    }

    @Test
    public void saveMaxRelationNumTest() {
        Map<String, SolutionEntityMeta> solutionEntityMetaMap = new HashMap<>();
        SolutionEntityKnowledgeExport entityKnowledge = new SolutionEntityKnowledgeExport();
        List<NeResourceDetail> neResourceDetails = new ArrayList<>();
        NeResourceDetail ne = new NeResourceDetail();
        ne.setMoTypeId("123");
        ne.setName("12345");
        neResourceDetails.add(ne);
        List<EventResourceDetail> eventResourceDetails = new ArrayList<>();
        EventResourceDetail event = new EventResourceDetail();
        event.setEventTypeId("1234");
        event.setType(KnowledgeConstant.ALARM);
        event.setName("12357");
        eventResourceDetails.add(event);
        List<RelationResource> relationResources = new ArrayList<>();
        for(int i = 0; i < 3200; i++) {
            RelationResource relation = new RelationResource();
            relation.setSource("1234");
            relation.setTargetType(KnowledgeConstant.NE);
            relation.setTarget("123");
            relation.setSourceType(KnowledgeConstant.ALARM);
            relation.setInvokeRelationship(EntityRelationType.SUBCLASS_OF);
            relationResources.add(relation);
        }
        entityKnowledge.setRelationResources(relationResources);
        entityKnowledge.setNeResources(neResourceDetails);
        entityKnowledge.setEventResources(eventResourceDetails);
        SolutionEntityMeta solutionEntityMeta = new SolutionEntityMeta(entityKnowledge, "repo", KnowledgeSourceScene.PRESET.code);
        solutionEntityMetaMap.put("repo", solutionEntityMeta);
        Exception exp = null;
        try {
            knowledgeEntityDao.saveSolutionEntityKnowledge(solutionEntityMetaMap, KnowledgeSourceScene.INCREMENT.code);
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);

        try{
            knowledgeEntityDao.queryExistNeByMoTypeIds(new ArrayList<>(), "123");
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);

        try{
            knowledgeEntityDao.queryExistEventByEventIds(new ArrayList<>(), "123");
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);
    }

    @Test
    public void getSolutionVersionTest() throws ServiceException, OSSException {
        PowerMockito.mockStatic(MITManagerClient.class);
        MITManagerClient client = PowerMockito.mock(MITManagerClient.class);
        PowerMockito.when(MITManagerClient.newInstance()).thenReturn(client);
        // 构建mock数据
        List<CommonAgent> list = new ArrayList<>();
        CommonAgent commonAgent = new CommonAgent();
        commonAgent.setAddress("*******");
        list.add(commonAgent);
        PIUtil.DefaultPagingIterator defaultPagingIterator = new PIUtil.DefaultPagingIterator(list, 0, 0, 1, false);
        PowerMockito.when(client.bulkGet(Mockito.any(Criterion.class), Mockito.anyInt(), Mockito.any())).thenReturn(defaultPagingIterator);
        PowerMockito.mockStatic(CommonConfigUtil.class);
        List<ConfigResult> configResults = new ArrayList<>();
        ConfigResult configResult = new ConfigResult();
        List<InstalledBaseInfo> infos = new ArrayList<>();
        InstalledBaseInfo info = new InstalledBaseInfo();
        info.setType("NE");
        info.setVersion("CBS-SW");
        infos.add(info);
        configResult.setValue(JSON.toJSONString(infos));
        configResults.add(configResult);
        PowerMockito.when(CommonConfigUtil.getConfig(Mockito.any())).thenReturn(configResults);
        caseKnowledgeService.scheduleGetVersionTask();

        // 发送请求
        ResponseResult result2 = knowledgeRepositoryServiceDelegate.getKnowledgeVersion(null);
        Assert.assertNotNull(result2.getData());
    }
}
