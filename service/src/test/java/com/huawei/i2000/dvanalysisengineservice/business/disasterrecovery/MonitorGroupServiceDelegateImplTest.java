/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpContext;
import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.i2000.dvanalysisengineservice.WebServiceTest;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskswitch.model.BasicInfo;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskswitch.model.FiredFlow;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.util.DomainUtil;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.util.EamUtil;
import com.huawei.i2000.dvanalysisengineservice.dao.disasterrecovery.DisaterRecoveryTaskDao;
import com.huawei.i2000.dvanalysisengineservice.impl.MonitorGroupServiceDelegateImpl;
import com.huawei.i2000.dvanalysisengineservice.model.AddGroupRequest;
import com.huawei.i2000.dvanalysisengineservice.model.AddMonitorGroupResp;
import com.huawei.i2000.dvanalysisengineservice.model.AuxiliaryModel;
import com.huawei.i2000.dvanalysisengineservice.model.FlowAction;
import com.huawei.i2000.dvanalysisengineservice.model.FlowActionList;
import com.huawei.i2000.dvanalysisengineservice.model.FlowDetail;
import com.huawei.i2000.dvanalysisengineservice.model.GroupMember;
import com.huawei.i2000.dvanalysisengineservice.model.GroupMemberList;
import com.huawei.i2000.dvanalysisengineservice.model.IpsDetail;
import com.huawei.i2000.dvanalysisengineservice.model.KPIColumnModel;
import com.huawei.i2000.dvanalysisengineservice.model.KPIIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.MmlAction;
import com.huawei.i2000.dvanalysisengineservice.model.MonitorAuxiliaryItem;
import com.huawei.i2000.dvanalysisengineservice.model.MonitorGroupList;
import com.huawei.i2000.dvanalysisengineservice.model.MonitorGroupModel;
import com.huawei.i2000.dvanalysisengineservice.model.MonitorItem;
import com.huawei.i2000.dvanalysisengineservice.model.QueryGroupNameRequestList;
import com.huawei.i2000.dvanalysisengineservice.model.QueryGroupNameResponseList;
import com.huawei.i2000.dvanalysisengineservice.model.StaticAlarmInfo;
import com.huawei.i2000.dvanalysisengineservice.util.AuthTokenUtil;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.ManagedObject;
import com.huawei.oms.eam.mo.NetworkElement;

import mockit.Mock;
import mockit.MockUp;

import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * MonitorGroupServiceDelegateImplTest
 *
 * <AUTHOR>
 * @since 2022-09-19
 */
@PrepareForTest({ContextUtils.class, DomainUtil.class, EamUtil.class, AuthTokenUtil.class})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class MonitorGroupServiceDelegateImplTest extends WebServiceTest {
    private static final AtomicBoolean DATALOAD = new AtomicBoolean(false);

    private static EmbeddedRestClientAndServer mockServer;

    @Autowired
    MonitorGroupServiceDelegateImpl monitorGroupServiceDelegateImpl;

    @Autowired
    DisaterRecoveryTaskDao disaterRecoveryTaskDao;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(false);
        Mockito.when(contextUtils.getNoModifyAuthTaskIds()).thenReturn(new HashSet<>());
        Mockito.when(contextUtils.getUserId()).thenReturn("22");

        // mock 分域 方法
        PowerMockito.mockStatic(DomainUtil.class);
        PowerMockito.doReturn(null).when(DomainUtil.class, "getDnsWithPerm", Mockito.any());
        PowerMockito.doReturn(null).when(DomainUtil.class, "getDnsWithPermAnyUser", Mockito.any());

        PowerMockito.mockStatic(AuthTokenUtil.class);
        PowerMockito.when(AuthTokenUtil.isAuthDRTaskAddOrDel()).thenReturn(true);

        // 保证这个只运行一次
        if (!DATALOAD.compareAndSet(false, true)) {
            return;
        }
        setSqlScriptEncoding("UTF-8");
        executeSqlScript("classpath:database/dvanalysisengineservice_clear_all_table.sql", false);
        executeSqlScript("classpath:database/dvanalysisengineservice_disasterrecovery_zenith.sql", false);
    }

    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    /**
     * rest server mock stop
     */
    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void addFlowActionTest0() throws Exception {
        new MockUp<DomainUtil>() {
            @Mock
            public List<FiredFlow> getAllFlowsWithPerm(HttpContext context) throws ServiceException {
                List<FiredFlow> flows = new ArrayList<>();
                FiredFlow firedFlow = new FiredFlow();
                BasicInfo basicInfo = new BasicInfo();
                basicInfo.setName("1");
                basicInfo.setSolution("1");
                basicInfo.setFlowId("1");
                basicInfo.setName("name");
                firedFlow.setBasicInfo(basicInfo);
                flows.add(firedFlow);
                return flows;
            }
        };
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        MmlAction mmlAction = new MmlAction();
        mmlAction.setFlowId("1");
        mmlAction.setFlowGroup("1");
        mmlAction.setFlowName("name");
        mmlAction.setSwitchCommand("BLK SIPTG:TG=1;");
        mmlAction.setSwitchBackCommand("UBL SIPTG:TG=1;");
        FlowActionList flowActionList = new FlowActionList();
        flowActionList.setGroupId("1");
        flowActionList.setMmlAction(mmlAction);
        flowActionList.setSwitchType("1");
        HttpContext context = new MockHttpContext();
        monitorGroupServiceDelegateImpl.addFlowAction(context, flowActionList);
        Assert.assertNotNull(disaterRecoveryTaskDao.queryMmlAction(flowActionList.getGroupId()));
    }

    /**
     * addFlowAction
     *
     * @throws Exception Exception
     */
    @Test
    public void addFlowActionTest1() throws Exception {
        mockServer.loadScenarioFiles("module.disasterrecovery/rest_mapping-flowclient.json");
        new MockUp<DomainUtil>() {
            @Mock
            public List<FiredFlow> getAllFlowsWithPerm(HttpContext context) throws ServiceException {
                List<FiredFlow> flows = new ArrayList<>();
                FiredFlow firedFlow = new FiredFlow();
                BasicInfo basicInfo = new BasicInfo();
                basicInfo.setName("1");
                basicInfo.setSolution("1");
                basicInfo.setFlowId("1");
                basicInfo.setName("name");
                firedFlow.setBasicInfo(basicInfo);
                flows.add(firedFlow);
                return flows;
            }
        };
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        List<IpsDetail> ipsDetailList = new ArrayList<>();
        IpsDetail ipsDetail = new IpsDetail();
        ipsDetail.setIps("2");
        ipsDetail.setNodeId("2");
        ipsDetail.setAtomId("2");
        ipsDetailList.add(ipsDetail);
        FlowAction flowActionSwitch = new FlowAction();
        flowActionSwitch.setFlowId("1");
        flowActionSwitch.setFlowGroup("1");
        flowActionSwitch.setFlowName("name");
        flowActionSwitch.setIpsDetails(ipsDetailList);
        flowActionSwitch.setType("0");
        FlowAction flowActionSwitchBack = new FlowAction();
        flowActionSwitchBack.setFlowId("1");
        flowActionSwitchBack.setFlowGroup("1");
        flowActionSwitchBack.setFlowName("name");
        flowActionSwitchBack.setIpsDetails(ipsDetailList);
        flowActionSwitchBack.setType("1");
        List<FlowAction> list = new ArrayList<>();
        list.add(flowActionSwitch);
        list.add(flowActionSwitchBack);
        FlowActionList flowActionList = new FlowActionList();
        flowActionList.setGroupId("1");
        flowActionList.setFlowActions(list);
        HttpContext context = new MockHttpContext();
        monitorGroupServiceDelegateImpl.addFlowAction(context, flowActionList);
        Assert.assertEquals(1, disaterRecoveryTaskDao.queryFlowActionByType0(flowActionList.getGroupId()).size());
    }

    /**
     * add
     *
     * @throws Exception Exception
     */
    @Test
    public void addTest0() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        AddGroupRequest addGroupRequest = new AddGroupRequest();
        MonitorGroupModel monitorGroupModel = new MonitorGroupModel();
        monitorGroupModel.setTaskId("5");
        monitorGroupModel.setGroupName("5");
        monitorGroupModel.setGroupKey("5");
        addGroupRequest.setMonitorGroup(monitorGroupModel);
        addGroupRequest.setTaskName("Task_XXX");
        addGroupRequest.setTaskTypeValue("1|MIN");
        HttpContext context = new MockHttpContext();
        AddMonitorGroupResp addMonitorGroupResp = monitorGroupServiceDelegateImpl.add(context, addGroupRequest);
        Assert.assertNotNull(addMonitorGroupResp);
    }

    /**
     * add
     *
     * @throws Exception Exception
     */
    @Test
    public void addTest1() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        PowerMockito.mockStatic(EamUtil.class);
        new MockUp<MITManagerClient>() {
            @Mock
            public List<ManagedObject> getMoByDns(List<DN> dns) {
                List<ManagedObject> managedObjectList = new ArrayList<>();
                dns.forEach(dn -> {
                    NetworkElement managedObject = new NetworkElement();
                    managedObject.setDN("dn1");
                    managedObject.setName("dnName1");
                    managedObjectList.add(managedObject);
                });
                return managedObjectList;
            }
        };
        AddGroupRequest addGroupRequest = assembleAddGroupRequest();
        HttpContext context = new MockHttpContext();
        AddMonitorGroupResp addMonitorGroupResp = monitorGroupServiceDelegateImpl.add(context, addGroupRequest);
        Assert.assertNotNull(addMonitorGroupResp);
    }

    private static AddGroupRequest assembleAddGroupRequest() {
        AddGroupRequest addGroupRequest = new AddGroupRequest();
        MonitorGroupModel monitorGroupModel = new MonitorGroupModel();
        monitorGroupModel.setGroupId("1");
        monitorGroupModel.setGroupName("name1");
        monitorGroupModel.setGroupKey("key");
        GroupMember groupMember = new GroupMember();
        groupMember.setDn("dn1");
        groupMember.setDnName("dnName1");
        List<GroupMember> groupMembers = new ArrayList<>();
        groupMembers.add(groupMember);
        monitorGroupModel.setGroupMembers(groupMembers);
        addGroupRequest.setMonitorGroup(monitorGroupModel);
        addGroupRequest.setTaskId("1");
        return addGroupRequest;
    }

    /**
     * addMonitorItem
     *
     * @throws Exception Exception
     */
    @Test
    public void addMonitorItemTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        MonitorItem monitorItem = new MonitorItem();
        monitorItem.setGroupId("3");
        KPIIndicator kpiIndicator = new KPIIndicator();
        kpiIndicator.setGroupMemberId("1");
        List<KPIIndicator> kPIIndicatorList = new ArrayList<>();
        kPIIndicatorList.add(kpiIndicator);
        monitorItem.setKpiIndicators(kPIIndicatorList);
        MonitorAuxiliaryItem monitorAuxiliaryItem = new MonitorAuxiliaryItem();
        monitorAuxiliaryItem.setKey("key");
        monitorAuxiliaryItem.setDisplayNameZH("ZH");
        monitorAuxiliaryItem.setDisplayNameEN("EN");
        AuxiliaryModel auxiliaryModel = new AuxiliaryModel();
        List<String> memberIds = new ArrayList<>();
        memberIds.add("3");
        auxiliaryModel.setGroupMemberId(memberIds);
        auxiliaryModel.setValue("value");
        List<AuxiliaryModel> auxiliaryModelList = new ArrayList<>();
        auxiliaryModelList.add(auxiliaryModel);
        monitorAuxiliaryItem.setAsigningValues(auxiliaryModelList);
        List<MonitorAuxiliaryItem> monitorAuxiliaryItemList = new ArrayList<>();
        monitorAuxiliaryItemList.add(monitorAuxiliaryItem);
        monitorItem.setAuxiliaryList(monitorAuxiliaryItemList);
        StaticAlarmInfo staticAlarmInfo = new StaticAlarmInfo();
        staticAlarmInfo.setAlarmId("1");
        staticAlarmInfo.setAlarmGroupId("1");
        staticAlarmInfo.setAlarmName("name");
        List<StaticAlarmInfo> staticAlarmInfoList = new ArrayList<>();
        staticAlarmInfoList.add(staticAlarmInfo);
        monitorItem.setAlarmInfos(staticAlarmInfoList);
        FlowDetail flowDetail = new FlowDetail();
        flowDetail.setFlowId("1");
        flowDetail.setFlowName("flowName");
        flowDetail.setFlowGroup("1");
        flowDetail.setGroupMemberId("1");
        IpsDetail ipsDetail = new IpsDetail();
        ipsDetail.setNodeId("1");
        ipsDetail.setAtomId("1");
        List<IpsDetail> ipsDetailList = new ArrayList<>();
        ipsDetailList.add(ipsDetail);
        flowDetail.setIpsDetails(ipsDetailList);
        List<FlowDetail> flowDetailList = new ArrayList<>();
        flowDetailList.add(flowDetail);
        monitorItem.setFlowDetails(flowDetailList);
        KPIColumnModel kpiColumnModel = new KPIColumnModel();
        kpiColumnModel.setGroupId("1");
        kpiColumnModel.setColumnDisplayCn("CN");
        kpiColumnModel.setColumnDisplayEn("EN");
        kpiColumnModel.setColumnName("name");
        kpiColumnModel.setColumnType("type");
        List<KPIColumnModel> kpiColumnModelList = new ArrayList<>();
        kpiColumnModelList.add(kpiColumnModel);
        monitorItem.setKpiColumns(kpiColumnModelList);
        HttpContext context = new MockHttpContext();
        try {
            monitorGroupServiceDelegateImpl.addMonitorItem(context, monitorItem);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof ServiceException);
        }
    }

    /**
     * deleteGroup
     *
     * @throws Exception Exception
     */
    @Test
    public void deleteGroupTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        HttpContext context = new MockHttpContext();
        monitorGroupServiceDelegateImpl.deleteGroup(context, "4");
        Assert.assertEquals(0, (int) disaterRecoveryTaskDao.queryMonitorGroupCount("4"));
        Assert.assertEquals(0, disaterRecoveryTaskDao.getStaticAlarms("4").size());
        Assert.assertEquals(0, disaterRecoveryTaskDao.getflowHosts("4").size());
        Assert.assertEquals(0, disaterRecoveryTaskDao.queryFlowActionByType1("4").size());
        Assert.assertEquals(0, (int) disaterRecoveryTaskDao.queryAuxiliaryInfo("4"));
        Assert.assertEquals(0, disaterRecoveryTaskDao.getMemberByGroupId("4").size());
        Assert.assertEquals(0, disaterRecoveryTaskDao.queryColumn("4").size());
        List<String> idLists = new ArrayList<>();
        idLists.add("4");
        Assert.assertEquals(0, disaterRecoveryTaskDao.getThresholdByIndicatorIds(idLists).size());
        Assert.assertEquals(0, disaterRecoveryTaskDao.getIndicatorIds("4", null).size());
        Assert.assertEquals(0, disaterRecoveryTaskDao.getThreshold("9", "DERIVED").size());
        Assert.assertEquals(0, disaterRecoveryTaskDao.getIndicatorIds("9", null).size());
        Assert.assertEquals(0, disaterRecoveryTaskDao.getAuxiliaryKpiInfo("4").size());
        Assert.assertNull(disaterRecoveryTaskDao.getDerivedKpiInfo("4"));
    }

    /**
     * deletMember
     *
     * @throws Exception Exception
     */
    @Test
    public void deletMemberTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        HttpContext context = new MockHttpContext();
        monitorGroupServiceDelegateImpl.deletMember(context, "1");
        List<GroupMember> groupMemberList = disaterRecoveryTaskDao.getMemberByGroupId("1");
        List<IpsDetail> ipsDetailList = disaterRecoveryTaskDao.queryFlowHost("1");
        Assert.assertEquals(0, groupMemberList.size());
        Assert.assertEquals(0, ipsDetailList.size());
        Assert.assertEquals(0, (int) disaterRecoveryTaskDao.queryAuxiliaryInfo("1"));
    }

    /**
     * queryAllGroup
     *
     * @throws ServiceException ServiceException
     */
    @Test
    public void queryAllGroupTest() throws ServiceException {
        HttpContext context = new MockHttpContext();
        MonitorGroupList monitorGroupList = monitorGroupServiceDelegateImpl.queryAllGroup(context, "1");
        Assert.assertEquals(1, monitorGroupList.getRows().size());
    }

    /**
     * queryAllMember
     *
     * @throws Exception Exception
     */
    @Test
    public void queryAllMemberTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        HttpContext context = new MockHttpContext();
        GroupMemberList groupMemberList = monitorGroupServiceDelegateImpl.queryAllMember(context, "1");
        Assert.assertEquals(1, groupMemberList.getGroupMembers().size());
    }

    /**
     * queryFlowAction
     *
     * @throws Exception Exception
     */
    @Test
    public void queryFlowActionTest0() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        HttpContext context = new MockHttpContext();
        FlowActionList flowActionList = monitorGroupServiceDelegateImpl.queryFlowAction(context, "2");
        Assert.assertEquals(2, flowActionList.getFlowActions().size());
    }

    /**
     * queryFlowAction
     *
     * @throws Exception Exception
     */
    @Test
    public void queryFlowActionTest1() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        HttpContext context = new MockHttpContext();
        FlowActionList flowActionList = monitorGroupServiceDelegateImpl.queryFlowAction(context, "2-2");
        Assert.assertNotNull(flowActionList.getMmlAction());
    }

    /**
     * queryGroup
     *
     * @throws Exception Exception
     */
    @Test
    public void queryGroupTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        HttpContext context = new MockHttpContext();
        MonitorGroupModel monitorGroupModel = monitorGroupServiceDelegateImpl.queryGroup(context, "1");
        Assert.assertEquals(1, monitorGroupModel.getGroupMembers().size());
    }

    /**
     * queryGroupname
     *
     * @throws ServiceException ServiceException
     */
    @Test
    public void queryGroupnameTest() throws ServiceException {
        QueryGroupNameRequestList queryGroupNameRequestList = new QueryGroupNameRequestList();
        List<String> dnsList = new ArrayList<>();
        dnsList.add("dn2");
        queryGroupNameRequestList.setDns(dnsList);
        HttpContext context = new MockHttpContext();
        QueryGroupNameResponseList queryGroupNameResponseList =
                monitorGroupServiceDelegateImpl.queryGroupname(context, queryGroupNameRequestList);
        Assert.assertEquals(1, queryGroupNameResponseList.getQueryGroupNameResponseList().size());
    }

    /**
     * queryMonitoritem
     *
     * @throws Exception Exception
     */
    @Test
    public void queryMonitoritemTest() throws Exception {
        PowerMockito.mockStatic(ContextUtils.class);
        ContextUtils contextUtils = Mockito.spy(new ContextUtils());
        PowerMockito.when(ContextUtils.class, "getContext").thenReturn(contextUtils);
        PowerMockito.mockStatic(DomainUtil.class);
        List<String> dnsWithPerm = new ArrayList<>();
        dnsWithPerm.add("1");
        dnsWithPerm.add("dn1");
        PowerMockito.when(DomainUtil.class, "getDnsWithPerm",Mockito.any()).thenReturn(dnsWithPerm);
        Mockito.when(contextUtils.getAdmin()).thenReturn(true);
        HttpContext context = new MockHttpContext();
        MonitorItem monitorItem = monitorGroupServiceDelegateImpl.queryMonitoritem(context, "5");
        Assert.assertEquals(1, monitorItem.getAlarmInfos().size());
        Assert.assertEquals(0, monitorItem.getAuxiliaryList().size());
        Assert.assertEquals(1, monitorItem.getFlowDetails().size());
        Assert.assertEquals(1, monitorItem.getKpiColumns().size());
        Assert.assertEquals(1, monitorItem.getKpiIndicators().size());
    }
}
