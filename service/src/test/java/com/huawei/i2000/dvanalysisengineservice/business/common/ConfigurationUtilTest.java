/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.common;

import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.cache.redis.EmbeddedRedisServer;
import com.huawei.baize.servicesimulator.cache.redis.EmbeddedRedisServerUtils;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.deploy.util.DefaultEnvUtil;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.cbb.common.CommonConfigUtil;
import com.huawei.i2000.cbb.common.ContainerUtil;
import com.huawei.i2000.dvanalysisengineservice.WebServiceTest;
import com.huawei.i2000.dvanalysisengineservice.business.configdata.ConfigDataCacheOper;
import com.huawei.i2000.dvanalysisengineservice.business.configdata.ConfigDataHandler;
import com.huawei.i2000.dvanalysisengineservice.dao.common.MapperFactory;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.model.ConfigData;
import com.huawei.i2000.dvanalysisengineservice.model.ConfigDataResult;

import org.apache.commons.io.FileUtils;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Properties;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-09-22
 */
@ContextConfiguration(locations = "classpath:spring/service.xml")
@PrepareForTest({ConfigurationUtil.class, TaskConstant.class, ContainerUtil.class, CommonConfigUtil.class})
public class ConfigurationUtilTest extends WebServiceTest {
    private static final OssLog LOGGER = OssLogFactory.getLogger(ConfigurationUtilTest.class);

    private static EmbeddedRestClientAndServer mockServer;

    private static EmbeddedRedisServer redisServer;

    final String ANALYSIS_ENGINE_HOT_CONFIG_FILENAME = "hotconfig.properties";

    @Autowired
    private ConfigDataHandler configDataHandler;

    @Autowired
    MapperFactory mapperFactory;


    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {

        redisServer = EmbeddedRedisServerUtils.startGlobalRedisServer();
        Assert.assertNotNull(redisServer);

        executeSqlScript("classpath:database/dvanalysisengineservice_clear_all_table.sql", false);
        executeSqlScript("classpath:database/dvanalysisengineservice_config_data_zenith.sql", false);
    }

    /**
     * changeFilePermission 方法无法在windows系统执行，需要mock掉
     *
     * @throws Exception Exception
     */
    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        File src = new File(System.getProperty("user.dir")+"/target/test-classes/config");
        File dest = new File(DefaultEnvUtil.getAppShareDir() + File.separator + "config");
        dest.mkdirs();
        FileUtils.copyDirectory(src, dest);
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());

        ConfigDataCacheOper.getInstance().removeAll();
    }

    /**
     * rest server mock stop
     */
    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void refreshHotConfigProperties()
        throws NoSuchMethodException, InvocationTargetException, IllegalAccessException, IOException {

        final String HOT_CONFIG_FULL_PATH = DefaultEnvUtil.getAppShareDir() + File.separator + "config"
            + File.separator + ANALYSIS_ENGINE_HOT_CONFIG_FILENAME;

        Properties hotProperties = PropertiesUtil.loadProperties(HOT_CONFIG_FULL_PATH);
        hotProperties.clear();
        hotProperties.setProperty("PREDICT_CURRENT_MAX_INSERT_DATA","null");
        try (OutputStreamWriter fileWriter = new OutputStreamWriter(new FileOutputStream(HOT_CONFIG_FULL_PATH),
            StandardCharsets.UTF_8)) {
            hotProperties.store(fileWriter, "modify hot config");
        } catch (IOException e) {
            LOGGER.error("hot config pass value failed");
        }
        ConfigurationUtil configurationUtil = new ConfigurationUtil();
        Method refreshHotConfigProperties = ConfigurationUtil.class.getDeclaredMethod("refreshHotConfigProperties");
        refreshHotConfigProperties.setAccessible(true);
        refreshHotConfigProperties.invoke(configurationUtil);
        hotProperties = PropertiesUtil.loadProperties(HOT_CONFIG_FULL_PATH);
        Assert.assertEquals("60000, configuration range[60000 - 3600000]", hotProperties.getProperty("configItemValue"));

        hotProperties.clear();
        hotProperties.setProperty("PREDICT_CURRENT_MAX_INSERT_DATA","720000");
        try (OutputStreamWriter fileWriter = new OutputStreamWriter(new FileOutputStream(HOT_CONFIG_FULL_PATH),
            StandardCharsets.UTF_8)) {
            hotProperties.store(fileWriter, "modify hot config");
        } catch (IOException e) {
            LOGGER.error("hot config pass value failed");
        }
        refreshHotConfigProperties.invoke(configurationUtil);
        hotProperties = PropertiesUtil.loadProperties(HOT_CONFIG_FULL_PATH);
        Assert.assertEquals("0", hotProperties.getProperty("_resultCode"));
    }

    @Test
    public void getIndicatorAllTask() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("INDICATOR_ALL_TASK");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        ConfigData configDataNew = new ConfigData();
        configDataNew.setConfigItemName("INDICATOR_ALL_TASK_CONFIG");
        configDataNew.setValue("1");
        ConfigDataCacheOper.getInstance().put(configDataNew);
        Assert.assertEquals(1, (int)ConfigurationUtil.getIndicatorAllTask());
    }

    @Test
    public void getIndicatorOneTaskTest() {
        ConfigDataCacheOper.getInstance().removeAll();
        Assert.assertEquals(100, (int)ConfigurationUtil.getIndicatorOneTask());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("INDICATOR_ONE_TASK");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int)ConfigurationUtil.getIndicatorOneTask());
    }

    @Test
    public void getStartIndicatorTaskNum() {
        ConfigDataCacheOper.getInstance().removeAll();
        Assert.assertEquals(30, (int)ConfigurationUtil.getStartIndicatorTaskNum());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("INDICATOR_TASK_COUNT");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int)ConfigurationUtil.getStartIndicatorTaskNum());
    }

    @Test
    public void getAllUserIndicatorTaskNum() {
        ConfigDataCacheOper.getInstance().removeAll();
        Assert.assertEquals(100, (int)ConfigurationUtil.getAllUserIndicatorTaskNum());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("ALL_USER_INDICATOR_TASK_NUM");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int)ConfigurationUtil.getAllUserIndicatorTaskNum());
    }

    @Test
    public void getIndicatorPredictAggregateTaskNumber() {
        ConfigDataCacheOper.getInstance().removeAll();
        Assert.assertEquals(10, (int)ConfigurationUtil.getIndicatorPredictAggregateTaskNumber());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("INDICATOR_PREDICT_AGGREGATE_TASK_NUMBER");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int)ConfigurationUtil.getIndicatorPredictAggregateTaskNumber());
    }

    @Test
    public void getIndicatorPredictAggregateGroupIndicatorNumber() {
        ConfigDataCacheOper.getInstance().removeAll();
        Assert.assertEquals(10, (int)ConfigurationUtil.getIndicatorPredictAggregateGroupIndicatorNumber());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("INDICATOR_PREDICT_AGGREGATE_GROUP_INDICATOR_NUMBER");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int)ConfigurationUtil.getIndicatorPredictAggregateGroupIndicatorNumber());
    }

    @Test
    public void getAggregateGroupIndicatorTotalNumber() {
        ConfigDataCacheOper.getInstance().removeAll();
        Assert.assertEquals(100, (int)ConfigurationUtil.getAggregateGroupIndicatorTotalNumber());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("AGGREGATE_GROUP_INDICATOR_TOTAL_NUMBER");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int)ConfigurationUtil.getAggregateGroupIndicatorTotalNumber());
    }

    @Test
    public void getMaxIndicatorPredictTaskNumber() {
        ConfigDataCacheOper.getInstance().removeAll();
        Assert.assertEquals(200, (int)ConfigurationUtil.getMaxIndicatorPredictTaskNumber());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MAX_INDICATOR_PREDICT_TASK_NUMBER");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int)ConfigurationUtil.getMaxIndicatorPredictTaskNumber());
    }

    @Test
    public void getMaxQueryNum() {
        ConfigDataCacheOper.getInstance().removeAll();
        Assert.assertEquals(5, (int)ConfigurationUtil.getMaxQueryNum());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MAX_QUERY_NUM");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int)ConfigurationUtil.getMaxQueryNum());
    }

    @Test
    public void getMaxLogQueryAmount() {
        ConfigDataCacheOper.getInstance().removeAll();
        Assert.assertEquals(100000, (int)ConfigurationUtil.getMaxLogQueryAmount());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MAX_LOG_QUERY_AMOUNT");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int)ConfigurationUtil.getMaxLogQueryAmount());
    }

    @Test
    public void getMaxLogCountAssociation() {
        ConfigDataCacheOper.getInstance().removeAll();
        Assert.assertEquals(100000, (int)ConfigurationUtil.getMaxLogCountAssociation());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MAX_LOG_COUNT_ASSOCIATION");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int)ConfigurationUtil.getMaxLogCountAssociation());
    }

    @Test
    public void getMaxLogCountTrain() {
        ConfigDataCacheOper.getInstance().removeAll();
        Assert.assertEquals(1000000, (int)ConfigurationUtil.getMaxLogCountTrain());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MAX_LOG_COUNT_TRAIN");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int)ConfigurationUtil.getMaxLogCountTrain());
    }

    @Test
    public void getMaxLogCountPredict() {
        ConfigDataCacheOper.getInstance().removeAll();
        Assert.assertEquals(1000, (int)ConfigurationUtil.getMaxLogCountPredict());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MAX_LOG_COUNT_PREDICT");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int)ConfigurationUtil.getMaxLogCountPredict());
    }

    @Test
    public void taskScheduleExecutorSize() {
        PowerMockito.mockStatic(System.class);
        PowerMockito.when(System.getenv("NetworkType")).thenReturn("AllInOne");
        PowerMockito.mockStatic(Runtime.class);
        PowerMockito.when( Runtime.getRuntime().availableProcessors()).thenReturn(16);
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("task_schedule_executor_size");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        ConfigurationUtil.initCpuCoreNum();
        Assert.assertEquals(2, (int)ConfigurationUtil.taskScheduleExecutorSize());
    }

    @Test
    public void predictDestFilePath() {
        Assert.assertNotNull(ConfigurationUtil.predictDestFilePath());
    }

    @Test
    public void modelFilePath() {
        Assert.assertNotNull(ConfigurationUtil.modelFilePath());
    }

    @Test
    public void srcFilePath() {
        Assert.assertNotNull(ConfigurationUtil.srcFilePath());
    }

    @Test
    public void i2kFloatIp() {
        Assert.assertEquals("@{I2K_NODE_FLOAT_IP}", ConfigurationUtil.i2kFloatIp());
    }
    @Test
    public void dvSharePassword() {
        Assert.assertEquals("@{DV_SHARE_USER_PASSWORD}", ConfigurationUtil.dvSharePassword());
    }

    @Test
    public void dvShareUserName() {
        Assert.assertEquals("@{DV_SHARE_USER_NAME}", ConfigurationUtil.dvShareUserName());
    }

    @Test
    public void onceTaskCheckSecond() {
        Assert.assertEquals(60000, (int)ConfigurationUtil.onceTaskCheckSecond());
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("task_schedule_executor_size");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2, (int)ConfigurationUtil.taskScheduleExecutorSize());
    }

    @Test
    public void dvShareSize() {
        Assert.assertEquals(60000, (long)ConfigurationUtil.dvShareSize());
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("DV_SHARE_SIZE");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1L, (long)ConfigurationUtil.dvShareSize());

    }

    @Test
    public void indicatorAggregateMethodType() {
        Assert.assertEquals(2, (int)ConfigurationUtil.indicatorAggregateMethodType());
    }

    @Test
    public void dataSourceMaxSize() {
        Assert.assertEquals(50, (int)ConfigurationUtil.dataSourceMaxSize());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("USER_MAX_DATASOURCE_COUNT");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (long)ConfigurationUtil.dataSourceMaxSize());
    }

    @Test
    public void dataSourceMaxSizeTotal() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("TOTAL_MAX_DATASOURCE_COUNT");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (long)ConfigurationUtil.dataSourceMaxSizeTotal());
    }

    @Test
    public void alarmAssociationCount() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MAX_ALARM_ASSOCIATION_COUNT");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int) ConfigurationUtil.alarmAssociationCount());
    }

    @Test
    public void predictAlarmMaxCount() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("PREDICT_MAX_ALARM_COUNT");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int) ConfigurationUtil.predictAlarmMaxCount());
    }

    @Test
    public void resultHandleNotEndAlarmMaxCount() {
        Assert.assertEquals(3000, (int)ConfigurationUtil.resultHandleNotEndAlarmMaxCount());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("RESULT_HANDLE_NOT_END_ALARM_COUNT");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int) ConfigurationUtil.resultHandleNotEndAlarmMaxCount());
    }

    @Test
    public void trainAlarmMaxCount() {
        Assert.assertEquals(200000, (int)ConfigurationUtil.trainAlarmMaxCount());

        ConfigData configData = new ConfigData();
        configData.setConfigItemName("TRAIN_MAX_ALARM_COUNT");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int) ConfigurationUtil.trainAlarmMaxCount());
    }

    @Test
    public void customPerformanceUpdateTime() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("CUSTOM_PERFORMANCE_UPDATE_TIME");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1L, (long) ConfigurationUtil.customPerformanceUpdateTime());
    }

    @Test
    public void customPerformanceUpdateCount() {
        ConfigDataCacheOper.getInstance().removeAll();
        Assert.assertEquals(120, (int)ConfigurationUtil.customPerformanceUpdateCount());
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("CUSTOM_PERFORMANCE_UPDATE_COUNT");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int) ConfigurationUtil.customPerformanceUpdateCount());
    }

    @Test
    public void maxAlarmTableSize() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MAX_ALARM_TABLE_SIZE");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (long) ConfigurationUtil.maxAlarmTableSize());
    }

    @Test
    public void alarmTableDeleteThreshold() {
        Assert.assertEquals(0.7, (float) ConfigurationUtil.alarmTableDeleteThreshold(),0.01);
    }

    @Test
    public void algorithmPackagePath() {
        Assert.assertEquals(".", ConfigurationUtil.algorithmPackagePath());
    }

    @Test
    public void algorithmRunPath() {
        Assert.assertEquals(".", ConfigurationUtil.algorithmRunPath());
    }

    @Test
    public void parameterMaxTime() {
        Assert.assertEquals(10000, (int)ConfigurationUtil.parameterMaxTime());
    }

    @Test
    public void trainMaxTime() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("TRAIN_MAX_TIME");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int) ConfigurationUtil.trainMaxTime());
    }

    @Test
    public void predictMaxTime() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("PREDICT_MAX_TIME");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int) ConfigurationUtil.predictMaxTime());
    }

    @Test
    public void resultHandleIndicatorExecutorSize() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("result_handle_indicator_executor_size");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int) ConfigurationUtil.resultHandleIndicatorExecutorSize());

    }

    @Test
    public void resultHandleAlarmExecutorMaxSize() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("result_handle_alarm_executor_max_size");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int) ConfigurationUtil.resultHandleAlarmExecutorMaxSize());
    }

    @Test
    public void getZenithConnectSocketTimeout() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("zenith_connect_socket_timeout");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals("2", ConfigurationUtil.getZenithConnectSocketTimeout());
    }

    @Test
    public void getPrometheusRequestType() {
        Assert.assertEquals("get", ConfigurationUtil.getPrometheusRequestType());
    }

    @Test
    public void maxAnalysisFileSize() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MAX_ANALYSIS_FILE_SIZE");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2L, (long) ConfigurationUtil.maxAnalysisFileSize());
    }

    @Test
    public void maxAnalysisFileCount() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MAX_ANALYSIS_FILE_COUNT");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2, (int) ConfigurationUtil.maxAnalysisFileCount());
    }

    @Test
    public void singleFileMax() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("SINGLE_FILE_MAX");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2L, (long) ConfigurationUtil.singleFileMax());
    }

    @Test
    public void singlePredictResultLagerFile() {
        Assert.assertEquals(52428800L, (long)ConfigurationUtil.singlePredictResultLagerFile());
    }

    @Test
    public void associationAnalysisIndicatorMax() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("ASSOCIATION_ANALYSIS_INDICATOR_MAX");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2, (int) ConfigurationUtil.associationAnalysisIndicatorMax());
    }

    @Test
    public void associationTaskSameTimeIndicatorMax() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("ASSOCIATION_TASK_SAME_TIME_INDICATOR_MAX");
        configData.setValue("10000");
        ConfigData configData1 = new ConfigData();
        configData.setConfigItemName("IM_CPU_RESOURCE_POOL_SIZE");
        configData.setValue("16");
        ConfigDataCacheOper.getInstance().put(configData1);
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(10000, (int) ConfigurationUtil.associationTaskSameTimeIndicatorMax());
    }

    @Test
    public void associationAnalysisAlarmNodeMax() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("ASSOCIATION_ANALYSIS_ALARM_NODE_MAX");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2, (int) ConfigurationUtil.associationAnalysisAlarmNodeMax());
    }

    @Test
    public void iMCpuResourcePoolSizeTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("IM_CPU_RESOURCE_POOL_SIZE");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2, (int) ConfigurationUtil.iMCpuResourcePoolSize());
    }

    @Test
    public void associationAnalysisLogNodeMax() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("ASSOCIATION_ANALYSIS_LOG_NODE_MAX");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2, (int) ConfigurationUtil.associationAnalysisLogNodeMax());
    }

    @Test
    public void disasterRecoverySwitchMonitorRuntime() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MONITOR_RUNTIME");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2L, (long) ConfigurationUtil.disasterRecoverySwitchMonitorRuntime());
    }

    @Test
    public void disasterRecoverySwitchoverRuntime() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("SWITCH_OVER_RUNTIME");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2L, (long) ConfigurationUtil.disasterRecoverySwitchoverRuntime());
    }

    @Test
    public void disasterRecoverySwitchExecuteRuntime() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("SWITCH_EXECUTE_RUNTIME");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2L, (long) ConfigurationUtil.disasterRecoverySwitchExecuteRuntime());
    }

    @Test
    public void disasterRecoverySwitchBackRuntime() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("SWITCH_BACK_RUNTIME");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2L, (long) ConfigurationUtil.disasterRecoverySwitchBackRuntime());
    }

    @Test
    public void disasterRecoverySwitchIntervalRuntime() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("SWITCH_INTERVAL_RUNTIME");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2L, (long) ConfigurationUtil.disasterRecoverySwitchIntervalRuntime());
    }

    @Test
    public void associationAlarmTaskQueryCount() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("ASSOCIATION_ALARM_TASK_QUERY_COUNT");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(2, (int) ConfigurationUtil.associationAlarmTaskQueryCount());
    }

    @Test
    public void associationNodeExecutorSize() {
        PowerMockito.mockStatic(System.class);
        PowerMockito.when(System.getenv("NetworkType")).thenReturn("AllInOne");
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("ASSOCIATION_NODE_EXECUTOR_SIZE");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        PowerMockito.mockStatic(Runtime.class);
        PowerMockito.when( Runtime.getRuntime().availableProcessors()).thenReturn(16);
        ConfigurationUtil.initCpuCoreNum();
        Assert.assertEquals(1, (int) ConfigurationUtil.associationNodeExecutorSize());
    }

    @Test
    public void associationTemplateExecutorSize() {
        PowerMockito.mockStatic(System.class);
        PowerMockito.when(System.getenv("NetworkType")).thenReturn("AllInOne");
        PowerMockito.mockStatic(Runtime.class);
        PowerMockito.when( Runtime.getRuntime().availableProcessors()).thenReturn(16);
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("ASSOCIATION_TEMPLATE_EXECUTOR_SIZE");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        ConfigurationUtil.initCpuCoreNum();
        Assert.assertEquals(2, (int) ConfigurationUtil.associationTemplateExecutorSize());
    }

    @Test
    public void associationReportSearchLogExecutorSize() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("ASSOCIATION_REPORT_SEARCH_LOG_EXECUTOR_SIZE");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int) ConfigurationUtil.associationReportSearchLogExecutorSize());
    }

    @Test
    public void pmDataExecutorSize() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("pm_data_executor_size");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int) ConfigurationUtil.pmDataExecutorSize());
    }

    @Test
    public void pmDataTrainExecutorSize() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("pm_data_train_executor_size");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int) ConfigurationUtil.pmDataTrainExecutorSize());
    }

    @Test
    public void alarmDataCurrentExecutorSize() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("alarm_data_current_executor_size");
        configData.setValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(1, (int) ConfigurationUtil.alarmDataCurrentExecutorSize());
    }

    @Test
    public void alarmDataHistoryExecutorSize() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("alarm_data_history_executor_size");
        configData.setValue("3");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(3, (int) ConfigurationUtil.alarmDataHistoryExecutorSize());
    }

    @Test
    public void taskAggregateIndicatorMaxSize() {
        Assert.assertEquals(10, (int)ConfigurationUtil.taskAggregateIndicatorMaxSize());
    }

    @Test
    public void indicatorTaskMaxSize() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MAX_INDICATOR_COUNT");
        configData.setValue("3");
        ConfigDataCacheOper.getInstance().put(configData);
        ConfigData configDataNew = new ConfigData();
        configDataNew.setConfigItemName("MAX_INDICATOR_COUNT_CONFIG");
        configDataNew.setValue("3");
        ConfigDataCacheOper.getInstance().put(configDataNew);
        Assert.assertEquals(3, (int) ConfigurationUtil.indicatorTaskMaxSize());
    }

    @Test
    public void sendMessageSwitch() {
        Assert.assertEquals("no", ConfigurationUtil.sendMessageSwitch());
    }

    @Test
    public void sendMessageIncidentLevel() {
        Assert.assertEquals(1, (int)ConfigurationUtil.sendMessageIncidentLevel());
    }

    @Test
    public void sendMessageGroupName() {
        Assert.assertEquals("", ConfigurationUtil.sendMessageGroupName());
    }

    @Test
    public void sendMessageContentAdd() {
        Assert.assertEquals("", ConfigurationUtil.sendMessageContentAdd());
    }

    @Test
    public void sendMessageContentEnd() {
        Assert.assertEquals("", ConfigurationUtil.sendMessageContentEnd());
    }

    @Test
    public void sendMessageContentUpgrade() {
        Assert.assertEquals("", ConfigurationUtil.sendMessageContentUpgrade());
    }

    @Test
    public void sendMessageContentDowngrade() {
        Assert.assertEquals("", ConfigurationUtil.sendMessageContentDowngrade());
    }

    @Test
    public void sendMessageContentAlarmInfo() {
        Assert.assertEquals("", ConfigurationUtil.sendMessageContentAlarmInfo());
    }

    @Test
    public void capacityIndicatorTaskMaxSizeTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("CAPACITY_MAX_INDICATOR_COUNT");
        configData.setTargetValue("2600");
        ConfigDataResult result = configDataHandler.modifyConfigData(configData);
        Assert.assertEquals("0", result.getResultCode());
        Assert.assertEquals(2600, ConfigurationUtil.capacityIndicatorTaskMaxSize());
    }

    @Test
    public void getAggregateIndicatorPredictMaxIndicatorCountTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("AGGREGATE_INDICATOR_PREDICT_MAX_INDICATOR_COUNT");
        configData.setTargetValue("150");
        ConfigDataResult result = configDataHandler.modifyConfigData(configData);
        Assert.assertEquals("0", result.getResultCode());
        Assert.assertEquals(150, ConfigurationUtil.getAggregateIndicatorPredictMaxIndicatorCount().intValue());
    }

    @Test
    public void getBasicCpuConsumptionTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("BASIC_CPU_CONSUMPTION");
        configData.setTargetValue("20");
        ConfigDataResult result = configDataHandler.modifyConfigData(configData);
        Assert.assertEquals("0", result.getResultCode());
        Assert.assertEquals(20, ConfigurationUtil.getBasicCpuConsumption().intValue());
    }

    @Test
    public void getOtherCpuConsumptionTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("OTHER_CPU_CONSUMPTION");
        configData.setTargetValue("20");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(20, ConfigurationUtil.getOtherCpuConsumption().intValue());
    }

    @Test
    public void getOneIndicatorTrainMemoryUsageTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("ONE_INDICATOR_TRAIN_MEMORY_USAGE");
        configData.setTargetValue("20");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(20, ConfigurationUtil.getOneIndicatorTrainMemoryUsage().intValue());
    }

    @Test
    public void getOneIndicatorPredictMemoryUsageTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("ONE_INDICATOR_PREDICT_MEMORY_USAGE");
        configData.setTargetValue("20");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(20, ConfigurationUtil.getOneIndicatorPredictMemoryUsage().intValue());
    }

    @Test
    public void getBasicDbConsumptionTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("BASIC_DB_CONSUMPTION");
        configData.setTargetValue("500000");
        ConfigDataResult result = configDataHandler.modifyConfigData(configData);
        Assert.assertEquals("0", result.getResultCode());
        Assert.assertEquals(500000, ConfigurationUtil.getBasicDbConsumption().intValue());
    }

    @Test
    public void getBasicMemoryConsumptionTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("BASIC_MEMORY_CONSUMPTION");
        configData.setTargetValue("15");
        ConfigDataResult result = configDataHandler.modifyConfigData(configData);
        Assert.assertEquals("0", result.getResultCode());
        Assert.assertEquals(15, ConfigurationUtil.getBasicMemoryConsumption().intValue());
    }

    @Test
    public void getIndicatorExceptionMaxPredictNumTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("INDICATOR_EXCEPTION_MAX_PREDICT_NUM");
        configData.setTargetValue("20");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(20, ConfigurationUtil.getIndicatorExceptionMaxPredictNum().intValue());
    }

    @Test
    public void getIndicatorPredictMaxPredictNumTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("INDICATOR_PREDICT_MAX_PREDICT_NUM");
        configData.setTargetValue("20");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(20, ConfigurationUtil.getIndicatorPredictMaxPredictNum().intValue());
    }

    @Test
    public void getCapacityIndicatorAllTaskTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("CAPACITY_INDICATOR_ALL_TASK");
        configData.setTargetValue("2600");
        ConfigDataResult result = configDataHandler.modifyConfigData(configData);
        Assert.assertEquals("0", result.getResultCode());
        Assert.assertEquals(2600, ConfigurationUtil.getCapacityIndicatorAllTask().intValue());
    }

    @Test
    public void getCpuMaximumTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("CPU_MAXIMUM");
        configData.setTargetValue("90");
        ConfigDataResult result = configDataHandler.modifyConfigData(configData);
        Assert.assertEquals("0", result.getResultCode());
        Assert.assertEquals(90, ConfigurationUtil.getCpuMaximum().intValue());
    }

    @Test
    public void getMemoryMaximumTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MEMORY_MAXIMUM");
        configData.setTargetValue("20");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(20, ConfigurationUtil.getMemoryMaximum().intValue());
    }

    @Test
    public void getCpuOverstepTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("CPU_OVERSTEP");
        configData.setTargetValue("1");
        ConfigDataResult result = configDataHandler.modifyConfigData(configData);
        Assert.assertEquals("-1", result.getResultCode());
        Assert.assertEquals(580000, ConfigurationUtil.getCpuOverstep().intValue());
    }

    @Test
    public void getOtherMemoryConsumptionTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("OTHER_MEMORY_CONSUMPTION");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Long.valueOf(1), ConfigurationUtil.getOtherMemoryConsumption());
    }


    @Test
    public void getOtherDbConsumptionTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("OTHER_DB_CONSUMPTION");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Long.valueOf(1), ConfigurationUtil.getOtherDbConsumption());
    }


    @Test
    public void getTaskTypeIndicatorExceptionPredictCpuTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("TASK_TYPE_INDICATOR_EXCEPTION_PREDICT_CPU");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Integer.valueOf(1), ConfigurationUtil.getTaskTypeIndicatorExceptionPredictCpu());
    }


    @Test
    public void getTaskTypeIndicatorPredictCpuTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("TASK_TYPE_INDICATOR_PREDICT_CPU");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Integer.valueOf(1), ConfigurationUtil.getTaskTypeIndicatorPredictCpu());
    }

    @Test
    public void getCustomDataIndicatorAllTaskTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("CUSTOM_DATA_INDICATOR_ALL_TASK");
        configData.setTargetValue("2500");
        ConfigDataResult result = configDataHandler.modifyConfigData(configData);
        Assert.assertEquals("0", result.getResultCode());
        Assert.assertEquals(2500, ConfigurationUtil.getCustomDataIndicatorAllTask().intValue());
    }

    @Test
    public void getDatabaseMaximumTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("DATABASE_MAXIMUM");
        configData.setTargetValue("90");
        ConfigDataResult result = configDataHandler.modifyConfigData(configData);
        Assert.assertEquals("0", result.getResultCode());
        Assert.assertEquals(90, ConfigurationUtil.getDatabaseMaximum().intValue());
    }

    @Test
    public void getAssociationAnalysisNodeCountMaxTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("ASSOCIATION_ANALYSIS_NODE_COUNT_MAX");
        configData.setTargetValue("400");
        ConfigDataResult result = configDataHandler.modifyConfigData(configData);
        Assert.assertEquals("0", result.getResultCode());
        Assert.assertEquals(400, ConfigurationUtil.getAssociationAnalysisNodeCountMax().intValue());
    }

    @Test
    public void getIndicatorExceptionDbTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("INDICATOR_EXCEPTION_DB");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Float.valueOf(0.001f), ConfigurationUtil.getIndicatorExceptionDb());
    }

    @Test
    public void getIndicatorPredictDbTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("INDICATOR_PREDICT_DB");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Float.valueOf(0.001f), ConfigurationUtil.getIndicatorPredictDb());
    }

    @Test
    public void getSingleIndicatorPredictMaxIndicatorCountTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("SINGLE_INDICATOR_PREDICT_MAX_INDICATOR_COUNT");
        configData.setTargetValue("2000");
        ConfigDataResult result = configDataHandler.modifyConfigData(configData);
        Assert.assertEquals("0", result.getResultCode());
        Assert.assertEquals(2000, ConfigurationUtil.getSingleIndicatorPredictMaxIndicatorCount().intValue());
    }

    @Test
    public void getPrometheusRestMaxBodySizeTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("PROMETHEUS_REST_MAX_BODY_SIZE");
        configData.setTargetValue("200000");
        ConfigDataResult result = configDataHandler.modifyConfigData(configData);
        Assert.assertEquals("0", result.getResultCode());
        Assert.assertEquals(200000, ConfigurationUtil.getPrometheusRestMaxBodySize().intValue());
    }

    @Test
    public void getCustomDataPredictDayLimitTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("CUSTOM_DATA_PREDICT_MINUTE_LIMIT");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Integer.valueOf(1), ConfigurationUtil.getCustomDataPredictDayLimit());
    }

    @Test
    public void getDriftTaskPeriodTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("CONCEPT_DRIFT_EXECUTE_PERIOD");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Integer.valueOf(1), ConfigurationUtil.getDriftTaskPeriod());
    }

    @Test
    public void getDriftDriftScoreIntervalTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("CONCEPT_DRIFT_SCORE_INTERVAL");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Integer.valueOf(1), ConfigurationUtil.getDriftDriftScoreInterval());
    }

    @Test
    public void maxImNumTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MAX_IM_NUM");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Integer.valueOf(1), ConfigurationUtil.maxImNum());
    }

    @Test
    public void getTaskTypeUnitedIndicatorPredictCpuTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("TASK_TYPE_UNITED_INDICATOR_PREDICT_CPU");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Integer.valueOf(1), ConfigurationUtil.getTaskTypeUnitedIndicatorPredictCpu());
    }

    @Test
    public void getTaskTypeAggregateIndicatorPredictCpuTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("TASK_TYPE_AGGREGATE_INDICATOR_PREDICT_CPU");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Integer.valueOf(1), ConfigurationUtil.getTaskTypeAggregateIndicatorPredictCpu());
    }

    @Test
    public void getCorrespondenceBetweenMainAndAuxiliaryIndicatorTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("CORRESPONDENCE_BETWEEN_MAIN_AND_AUXILIARY_INDICATOR");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Integer.valueOf(1), ConfigurationUtil.getCorrespondenceBetweenMainAndAuxiliaryIndicator());
    }

    @Test
    public void getMaxSqlResultLenTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("MAX_SQL_RESULT_LEN");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Integer.valueOf(1), ConfigurationUtil.getMaxSqlResultLen());

        configData.setValue("1a");
        configData.setTargetValue("1a");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(Integer.valueOf(200), ConfigurationUtil.getMaxSqlResultLen());
    }

    @Test
    public void getLogMindsporeConfigTest() {
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("LOG_MINDSPORE_CONFIG");
        configData.setValue("1");
        configData.setTargetValue("1");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertTrue(ConfigurationUtil.getLogMindsporeConfig());

        configData.setValue("1a");
        configData.setTargetValue("1a");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertFalse(ConfigurationUtil.getLogMindsporeConfig());
    }



    @Test
    public void defaultTest() throws Exception {
        PowerMockito.mockStatic(System.class);
        PowerMockito.when(System.getenv("NetworkType")).thenReturn("LargeCapacity");
        PowerMockito.mockStatic(Runtime.class);
        PowerMockito.when( Runtime.getRuntime().availableProcessors()).thenReturn(16);
        ConfigurationUtil.initCpuCoreNum();
        ConfigDataCacheOper instance = ConfigDataCacheOper.getInstance();
        instance.removeAll();

        PowerMockito.spy(ConfigurationUtil.class);
        // 打桩private static方法
        PowerMockito.doNothing().when(ConfigurationUtil.class, "synchronizationRedis", Mockito.any());
        Assert.assertEquals(2000, (int)ConfigurationUtil.getHomogeneousIndicatorNum());
        Assert.assertEquals(1000, (int)ConfigurationUtil.alarmAssociationCount());
        Assert.assertEquals(3600000, (long)ConfigurationUtil.customPerformanceUpdateTime());
        Assert.assertEquals(100000, (int)ConfigurationUtil.maxAlarmTableSize());
        Assert.assertEquals(150, (int)ConfigurationUtil.dataSourceMaxSizeTotal());
        Assert.assertEquals(2500, (int)ConfigurationUtil.predictAlarmMaxCount());
        Assert.assertEquals(4000, (int)ConfigurationUtil.maxAnalysisFileCount());
        Assert.assertEquals(500, (int)ConfigurationUtil.associationAnalysisAlarmNodeMax());
        Assert.assertEquals(5, ConfigurationUtil.getDriftDriftScoreInterval().intValue());
        Assert.assertEquals(5, ConfigurationUtil.getDriftTaskPeriod().intValue());
        Assert.assertEquals(5, ConfigurationUtil.getCustomDataPredictDayLimit().intValue());
        Assert.assertEquals(100000, ConfigurationUtil.getPrometheusRestMaxBodySize().intValue());
        Assert.assertEquals(500, ConfigurationUtil.getSingleIndicatorPredictMaxIndicatorCount().intValue());
        Assert.assertEquals(0, ConfigurationUtil.getIndicatorPredictDb().intValue());
        Assert.assertEquals(0, ConfigurationUtil.getIndicatorExceptionDb().intValue());
        Assert.assertEquals(80, ConfigurationUtil.getDatabaseMaximum().intValue());
        Assert.assertEquals(1500, ConfigurationUtil.getCustomDataIndicatorAllTask().intValue());
        Assert.assertEquals(200, ConfigurationUtil.getTaskTypeIndicatorPredictCpu().intValue());
        instance.removeAll();
        Assert.assertEquals(110, ConfigurationUtil.getTaskTypeIndicatorExceptionPredictCpu().intValue());
        instance.removeAll();
        Assert.assertEquals(2097152, ConfigurationUtil.getOtherDbConsumption().intValue());
        instance.removeAll();
        Assert.assertEquals(2097152, ConfigurationUtil.getOtherMemoryConsumption().intValue());
        Assert.assertEquals(580000, ConfigurationUtil.getCpuOverstep().intValue());
        Assert.assertEquals(80, ConfigurationUtil.getMemoryMaximum().intValue());
        Assert.assertEquals(80, ConfigurationUtil.getCpuMaximum().intValue());
        Assert.assertEquals(2500, ConfigurationUtil.getCapacityIndicatorAllTask().intValue());
        Assert.assertEquals(40320, ConfigurationUtil.getIndicatorPredictMaxPredictNum().intValue());
        Assert.assertEquals(40320, ConfigurationUtil.getIndicatorExceptionMaxPredictNum().intValue());
        Assert.assertEquals(7, ConfigurationUtil.getBasicMemoryConsumption().intValue());
        Assert.assertEquals(400000, ConfigurationUtil.getBasicDbConsumption().intValue());
        Assert.assertEquals(40320, ConfigurationUtil.getOneIndicatorPredictMemoryUsage().intValue());
        instance.removeAll();
        Assert.assertEquals(40320, ConfigurationUtil.getOneIndicatorTrainMemoryUsage().intValue());
        instance.removeAll();
        Assert.assertEquals(120000, ConfigurationUtil.getOtherCpuConsumption().intValue());
        Assert.assertEquals(15, ConfigurationUtil.getBasicCpuConsumption().intValue());
        Assert.assertEquals(100, ConfigurationUtil.getAggregateIndicatorPredictMaxIndicatorCount().intValue());
        Assert.assertEquals(2500, ConfigurationUtil.capacityIndicatorTaskMaxSize());
        Assert.assertEquals(4000, (int) ConfigurationUtil.indicatorTaskMaxSize());
        Assert.assertEquals(1, (int)ConfigurationUtil.alarmDataHistoryExecutorSize());
        Assert.assertEquals(5, (int)ConfigurationUtil.alarmDataCurrentExecutorSize());
        Assert.assertEquals(2, (int)ConfigurationUtil.pmDataTrainExecutorSize());
        Assert.assertEquals(3, (int)ConfigurationUtil.pmDataExecutorSize());
        Assert.assertEquals(10, (int)ConfigurationUtil.associationReportSearchLogExecutorSize());
        Assert.assertEquals(2, (int)ConfigurationUtil.associationTemplateExecutorSize());
        Assert.assertEquals(4, (int)ConfigurationUtil.associationNodeExecutorSize());
        Assert.assertEquals(10000, (int)ConfigurationUtil.associationAlarmTaskQueryCount());
        Assert.assertEquals(180000, (long)ConfigurationUtil.disasterRecoverySwitchIntervalRuntime());
        Assert.assertEquals(300000, (long)ConfigurationUtil.disasterRecoverySwitchBackRuntime());
        Assert.assertEquals(300000, (long)ConfigurationUtil.disasterRecoverySwitchExecuteRuntime());
        Assert.assertEquals(180000, (long)ConfigurationUtil.disasterRecoverySwitchoverRuntime());
        Assert.assertEquals(180000, (long)ConfigurationUtil.disasterRecoverySwitchMonitorRuntime());
        Assert.assertEquals(250, (int)ConfigurationUtil.associationAnalysisLogNodeMax());
        Assert.assertEquals(10000, (int)ConfigurationUtil.associationTaskSameTimeIndicatorMax());
        Assert.assertEquals(800000, (int)ConfigurationUtil.associationAnalysisIndicatorMax());
        Assert.assertEquals(524288000, (long)ConfigurationUtil.singleFileMax());
        Assert.assertEquals(10, (int)ConfigurationUtil.resultHandleAlarmExecutorMaxSize());
        Assert.assertEquals(25, (int)ConfigurationUtil.resultHandleIndicatorExecutorSize());
        Assert.assertEquals(1200000, (int)ConfigurationUtil.predictMaxTime());
        Assert.assertEquals(3600000, (int)ConfigurationUtil.trainMaxTime());
        Assert.assertEquals(2000, (int)ConfigurationUtil.getIndicatorAllTask());
        Assert.assertEquals(Integer.valueOf(4), ConfigurationUtil.maxImNum());
        Assert.assertEquals(500000, ConfigurationUtil.alarmCompressCleanCount());
        Assert.assertEquals(0, ConfigurationUtil.getAssociationAnalysisCustomTaskExecute().intValue());

        Assert.assertEquals(Integer.valueOf(2000), ConfigurationUtil.getTaskTypeUnitedIndicatorPredictCpu());
        Assert.assertEquals(Integer.valueOf(2000), ConfigurationUtil.getTaskTypeAggregateIndicatorPredictCpu());
        Assert.assertEquals(Integer.valueOf(1), ConfigurationUtil.getCorrespondenceBetweenMainAndAuxiliaryIndicator());
    }

    @Test
    public void taskScheduleExecutorSize2() {
        PowerMockito.mockStatic(System.class);
        PowerMockito.when(System.getenv("NetworkType")).thenReturn("AllInOne");
        PowerMockito.mockStatic(Runtime.class);
        PowerMockito.when( Runtime.getRuntime().availableProcessors()).thenReturn(128);
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("task_schedule_executor_size");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        ConfigurationUtil.initCpuCoreNum();
        Assert.assertEquals(6, ConfigurationUtil.taskScheduleExecutorSize());
    }

    @Test
    public void associationTemplateExecutorSize2() {
        PowerMockito.mockStatic(System.class);
        PowerMockito.when(System.getenv("NetworkType")).thenReturn("AllInOne");
        PowerMockito.mockStatic(Runtime.class);
        PowerMockito.when( Runtime.getRuntime().availableProcessors()).thenReturn(128);
        ConfigurationUtil.initCpuCoreNum();
        ConfigData configData = new ConfigData();
        configData.setConfigItemName("ASSOCIATION_TEMPLATE_EXECUTOR_SIZE");
        configData.setValue("2");
        ConfigDataCacheOper.getInstance().put(configData);
        Assert.assertEquals(6, ConfigurationUtil.associationTemplateExecutorSize().intValue());
    }

    @Test
    public void getSameAlarmReportIntervalTimeTest() throws ServiceException {
        long time = ConfigurationUtil.getSameAlarmReportIntervalTime();
        Assert.assertEquals(21600000L, time);

        PowerMockito.mockStatic(ContainerUtil.class);
        PowerMockito.when(ContainerUtil.isContainerMode()).thenReturn(true);
        long timeContainer = ConfigurationUtil.getSameAlarmReportIntervalTime();
        Assert.assertEquals(21600000L, timeContainer);

        PowerMockito.mockStatic(CommonConfigUtil.class);
        PowerMockito.when(CommonConfigUtil.getConfigExt(Mockito.anyList())).thenReturn(new ArrayList<>());
        long emptyContainer = ConfigurationUtil.getSameAlarmReportIntervalTime();
        Assert.assertEquals(21600000L, emptyContainer);
    }
}