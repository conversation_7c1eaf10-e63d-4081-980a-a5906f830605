/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.taskmanage;

import com.huawei.baize.servicesimulator.base.bsp.mock.rest.server.MockHttpRequest;
import com.huawei.baize.servicesimulator.bsp.rest.client.EmbeddedBspRestfulClientModule;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestClientAndServer;
import com.huawei.baize.servicesimulator.rest.integrated.EmbeddedRestUtils;
import com.huawei.baize.servicesimulator.rest.server.handler.BuiltinParamReplaceResponseHandler;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvanalysisengineservice.WebServiceTest;
import com.huawei.i2000.dvanalysisengineservice.business.algorithmmanage.AlgorithmManageServiceImpl;
import com.huawei.i2000.dvanalysisengineservice.business.common.EncryptionUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.HofsUtil;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.util.DomainUtil;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.cbs.AlgorithmParameterModel;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.cbs.ImportTask;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.ImportTaskEntity;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.util.CronServiceUtil;
import com.huawei.i2000.dvanalysisengineservice.business.templatemanage.TriggerTaskImporter;
import com.huawei.i2000.dvanalysisengineservice.business.templatemanage.dto.TemplateNodeInfo;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.PreExecutionNode;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TemplateTreeNode;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TemplateTreePath;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskIndicatorDao;
import com.huawei.i2000.dvanalysisengineservice.model.AlgorithmModel;
import com.huawei.i2000.dvanalysisengineservice.model.AlgorithmResult;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTask;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTaskList;
import com.huawei.i2000.dvanalysisengineservice.model.ColumnMapping;
import com.huawei.i2000.dvanalysisengineservice.model.DataSource;
import com.huawei.i2000.dvanalysisengineservice.model.FlowAction;
import com.huawei.i2000.dvanalysisengineservice.model.ModelParameter;
import com.huawei.i2000.dvanalysisengineservice.model.TaskIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.TaskUploadEntity;
import com.huawei.i2000.dvanalysisengineservice.model.TaskUploadParam;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerExecutionNode;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerIndicator;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;
import com.huawei.i2000.dvanalysisengineservice.util.jdbc.JDBCClient;
import com.huawei.i2000.dvanalysisengineservice.util.jdbc.JDBCClientGenerator;
import com.huawei.i2000.dvanalysisengineservice.util.jdbc.ZenithJdbcClient;
import com.huawei.i2000.mim.json.jackson.MIMMapper;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.collections4.CollectionUtils;
import org.h2.tools.Csv;
import org.junit.AfterClass;
import org.junit.Assert;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.StringReader;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.servlet.http.HttpServletRequest;

@PrepareForTest( {
    MIMMapper.class, EncryptionUtil.class, CronServiceUtil.class, ContextUtils.class, JDBCClient.class,
    JDBCClientGenerator.class, DomainUtil.class, CollectionUtils.class, TaskImporterCacheOper.class, TaskImporter.class, HofsUtil.class
})
@ContextConfiguration(locations = "classpath:spring/service.xml")
public class TaskImportTest extends WebServiceTest {
    private static EmbeddedRestClientAndServer mockServer;

    private static final AtomicBoolean DATALOAD = new AtomicBoolean(false);

    private static String jsonstr = "{\"taskName\":\"Correlation Analysis\",\"taskType\":\"CorrelationAnalysis\","
        + "\"useTemplate\":\"1\",\"delay\":\"1|MIN\",\"correlateTaskName\":\"指标异常检测任务\","
        + "\"nodes\":[{\"nodeType\":\"main\",\"main_task\":\"This is AD task\"},{\"nodeName\":\"节点1\",\"nodeType\":"
        + "\"normal\",\"dataSourceName\":\"Performance\",\"columnMapping\":{\"sql\":\"select * from TBL_AIOPS_SITUATION\","
        + "\"indicatorKey\":\"DATA\",\"dnKey\":\"KPI_ID\",\"ipKey\":\"KPI_UNIT\",\"timeKey\":\"TIME\"},\"algorithmModelName\":"
        + "\"DVCorrelationAnalysis\",\"kpiTimeRange\":\"1|HOUR\",\"kpiTimeType\":\"recently\"},{\"nodeName\":\"节点2\","
        + "\"nodeType\":\"moType\",\"moType\":\"OMS\",\"indicatorList\":[{\"measUnitKey\":\"I2K_Net\",\"measTypeKey\":"
        + "\"NetWorkIn\",\"originalValue\":\"ip=*************,ifDesc=eth0\"}],\"algorithmModelName\":\"DVCorrelationAnalysis\","
        + "\"kpiTimeRange\":\"1|HOUR\",\"kpiTimeType\":\"recently\"},{\"nodeName\":\"节点3\",\"nodeType\":\"normal\",\"indicatorList\":[{\"moType\":\"OMS\",\"measUnitKey\":\"I2K_Net\",\"measTypeKey\":\"NetWorkIn\",\"originalValue\":\"ip=*************,ifDesc=eth0\"}],\"algorithmModelName\":\"DVCorrelationAnalysis1\",\"kpiTimeRange\":\"1|HOUR\",\"kpiTimeType\":\"recently\"}],\"paths\":[{\"source\":\"main\",\"target\":\"节点1\"},{"
        + "\"source\":\"节点1\",\"condition\":\">=0.0\",\"target\":\"节点2\"},{\"source\": \"节点2\",\"target\": \"节点3\",\"condition\": \">=90.0\"}]}";

    private static String jsonstrSplite =
        "{\"taskSplit\":\"bySolutionId\",\"taskName\":\"Correlation Analysis\",\"taskType\":\"CorrelationAnalysis\","
            + "\"useTemplate\":\"1\",\"delay\":\"1|MIN\",\"correlateTaskName\":\"指标异常检测任务\","
            + "\"nodes\":[{\"nodeType\":\"main\",\"main_task\":\"This is AD task\"},{\"nodeName\":\"节点1\",\"nodeType\":"
            + "\"normal\",\"dataSourceName\":\"Performance\",\"columnMapping\":{\"sql\":\"select * from TBL_AIOPS_SITUATION\","
            + "\"indicatorKey\":\"DATA\",\"dnKey\":\"KPI_ID\",\"ipKey\":\"KPI_UNIT\",\"timeKey\":\"TIME\"},\"algorithmModelName\":"
            + "\"DVCorrelationAnalysis\",\"kpiTimeRange\":\"1|HOUR\",\"kpiTimeType\":\"recently\"},{\"nodeName\":\"节点2\","
            + "\"nodeType\":\"moType\",\"moType\":\"OMS\",\"indicatorList\":[{\"measUnitKey\":\"I2K_Net\",\"measTypeKey\":"
            + "\"NetWorkIn\",\"originalValue\":\"ip=*************,ifDesc=eth0\"}],\"algorithmModelName\":\"DVCorrelationAnalysis\","
            + "\"kpiTimeRange\":\"1|HOUR\",\"kpiTimeType\":\"recently\"},{\"nodeName\":\"节点3\",\"nodeType\":\"normal\",\"indicatorList\":[{\"moType\":\"OMS\",\"measUnitKey\":\"I2K_Net\",\"measTypeKey\":\"NetWorkIn\",\"originalValue\":\"ip=*************,ifDesc=eth0\"}],\"algorithmModelName\":\"DVCorrelationAnalysis1\",\"kpiTimeRange\":\"1|HOUR\",\"kpiTimeType\":\"recently\"}],\"paths\":[{\"source\":\"main\",\"target\":\"节点1\"},{"
            + "\"source\":\"节点1\",\"condition\":\">=0.0\",\"target\":\"节点2\"},{\"source\": \"节点2\",\"target\": \"节点3\",\"condition\": \">=90.0\"}]}";

    @InjectMocks
    @Autowired
    TaskImporter taskImporter;

    @Mock
    TaskManager taskManager;

    @Mock
    TaskIndicatorDao taskIndicatorDao;

    @Mock
    private AlgorithmManageServiceImpl algorithmManageService;

    @Autowired
    ImportTaskValidCheker importTaskValidCheker;

    @Autowired
    TriggerTaskImporter triggerTaskImporter;

    Class<TaskImporter> taskImporterClass = TaskImporter.class;

    /**
     * 准备sql
     *
     * @throws Exception Exception
     */
    @Before
    public void PreparedData() throws Exception {
        ContextUtils contextUtils = new ContextUtils();
        contextUtils.setAdmin(true);
        contextUtils.setUserId("1");
        ContextUtils.setContext(contextUtils);

        ZenithJdbcClient jdbcClient = PowerMockito.mock(ZenithJdbcClient.class);
        PowerMockito.mockStatic(JDBCClientGenerator.class);
        Mockito.when(JDBCClientGenerator.getJDBCClient(Mockito.any())).thenReturn(jdbcClient);
        List<String> lines = new ArrayList<>();
        String line = null;
        BufferedReader reader = new BufferedReader(
            new FileReader(System.getProperty("user.dir")+"/target/test-classes/mockfile" + File.separator + "dataSource.csv"));
        while ((line = reader.readLine()) != null) {
            lines.add(line);
        }
        PowerMockito.when(jdbcClient.queryData(Mockito.any(), Mockito.any(), Mockito.any()))
            .thenReturn(new Csv().read(new StringReader(lines.get(0)), new String[] {"TIME", "KPI_ID", "DATA", "KPI_UNIT"}))
            .thenReturn(new Csv().read(new StringReader(lines.get(0)), new String[] {"TIME", "KPI_ID", "DATA", "KPI_UNIT"}));

        // 保证这个只运行一次
        if (!DATALOAD.compareAndSet(false, true)) {
            return;
        }
        executeSqlScript("classpath:database/dvanalysisengineservice_clear_all_table.sql", false);
        executeSqlScript("classpath:database/dvanalysisengineservice_taskmanage_data_zenith.sql", false);

        PowerMockito.mockStatic(EncryptionUtil.class);
        PowerMockito.doNothing()
            .when(EncryptionUtil.class, "changeFilePermission", Mockito.anyString(), Mockito.anyString());
    }

    /**
     * changeFilePermission 方法无法在windows系统执行，需要mock掉
     *
     * @throws Exception Exception
     */
    @BeforeClass
    public static void MockChangeFilePermission() throws Exception {
        mockServer = EmbeddedRestUtils.startRestClientAndServer(new BuiltinParamReplaceResponseHandler());
        EmbeddedBspRestfulClientModule.install(mockServer.getPort());
    }

    @Test
    public void importTaskTest() throws ServiceException, IOException {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getnotrggatorbytype.json");

        ServiceException exp = null;
        try {
            AnalysisTaskList analysisTaskList = new AnalysisTaskList();
            AnalysisTask task = new AnalysisTask();
            task.setTaskId(3);
            task.setTaskName("指标异常检测任务");
            analysisTaskList.setRows(Arrays.asList(task));
            List<TaskIndicator> indicators = new ArrayList<>();
            TaskIndicator indicator = new TaskIndicator();
            indicator.setIndicatorId("11111");
            indicators.add(indicator);
            task.setIndicatorList(indicators);
            Mockito.when(taskManager.getAnalysisTaskList(Mockito.any())).thenReturn(analysisTaskList);
            Mockito.when(taskIndicatorDao.getTaskIndicatorList(3, null)).thenReturn(indicators);
            String algorithmFeature
                = "[{\"description\":\"{}\",\"displayName\":\"{}\",\"parameterDefaultValue\":\"\",\"parameterName\":\"feature_type_info\",\"type\":\"string\",\"valueRange\":{\"type\":\"array\",\"value\":[{\"feature_type\":\"association_matching\",\"feature_description\":\"分析关联指标与主指标的线性相关性\",\"feature_type_name\":\"关联匹配\"},{\"feature_type\":\"range_matching\",\"feature_description\":\"分析指标是否越限\",\"feature_type_name\":\"范围匹配\"},{\"feature_type\":\"fluctuation_matching\",\"feature_description\":\"分析指标数据是否发生波动\",\"feature_type_name\":\"波动匹配\"},{\"feature_type\":\"surge_matching\",\"feature_description\":\"分析指标是否发生突增\",\"feature_type_name\":\"突增匹配\"},{\"feature_type\":\"sudden_drop_matching\",\"feature_description\":\"分析指标是否发生突降\",\"feature_type_name\":\"突降匹配\"},{\"feature_type\":\"change_percentage_matching\",\"feature_description\":\"判断关联指标在主指标异常中的占比\",\"feature_type_name\":\"变化占比匹配\"},{\"feature_type\":\"homogeneous_comparison_matching\",\"feature_description\":\"判断指标是否离群\",\"feature_type_name\":\"同质比较匹配\"}]}}]";
            Mockito.when(algorithmManageService.defaultParam("DVCorrelationAnalysis1", "default", "feature_type"))
                .thenReturn(JSON.parseArray(algorithmFeature, ModelParameter.class));
            String algorithmStr
                = "[{\"description\":\"用于计算关联度的周期偏移值。单位为分钟，用于计算指标关联度时间偏移值，假设偏移周期设置为x，则表示将某时刻前移或后移x分钟，计算与相邻指标的关联度，并将计算结果最大值作为关联度计算结果。\",\"displayName\":\"偏移周期\",\"parameterDefaultValue\":\"5\",\"parameterName\":\"window_size\",\"type\":\"int\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":1,\"max\":60}}},{\"description\":\"关联特征的敏感程度。取值范围为0.0-1.0，小数点后最多支持3位数字。敏感度值越小，算法对指标关联特征越敏感；敏感度值越大，对指标关联特征越不敏感。\",\"displayName\":\"敏感度\",\"parameterDefaultValue\":\"0.2\",\"parameterName\":\"sensitivity\",\"type\":\"float\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":0.0,\"max\":1.0}}},{\"description\":\"{}\",\"displayName\":\"关联匹配（分析关联指标与主指标的线性相关性）\",\"parameterDefaultValue\":\"association_matching\",\"parameterName\":\"feature_type_info\",\"type\":\"string\",\"valueRange\":{\"type\":\"array\",\"value\":[{\"feature_type\":\"association_matching\",\"feature_description\":\"分析关联指标与主指标的线性相关性\",\"feature_type_name\":\"关联匹配\"},{\"feature_type\":\"range_matching\",\"feature_description\":\"分析指标是否越限\",\"feature_type_name\":\"范围匹配\"},{\"feature_type\":\"fluctuation_matching\",\"feature_description\":\"分析指标数据是否发生波动\",\"feature_type_name\":\"波动匹配\"},{\"feature_type\":\"surge_matching\",\"feature_description\":\"分析指标是否发生突增\",\"feature_type_name\":\"突增匹配\"},{\"feature_type\":\"sudden_drop_matching\",\"feature_description\":\"分析指标是否发生突降\",\"feature_type_name\":\"突降匹配\"},{\"feature_type\":\"change_percentage_matching\",\"feature_description\":\"判断关联指标在主指标异常中的占比\",\"feature_type_name\":\"变化占比匹配\"},{\"feature_type\":\"homogeneous_comparison_matching\",\"feature_description\":\"判断指标是否离群\",\"feature_type_name\":\"同质比较匹配\"}]}}]";
            Mockito.when(
                    algorithmManageService.defaultParam("DVCorrelationAnalysis1", "default", "association_matching"))
                .thenReturn(JSON.parseArray(algorithmStr, ModelParameter.class));
            taskImporter.importTask(jsonstr, null, "1", null, null);
            taskImporter.importTask(jsonstrSplite, null, "1", null, null);
        } catch (ServiceException e) {
            exp = e;
        }
        Assert.assertNotNull(exp);
    }

    @Test
    public void requestImportTaskTest() {
        HttpServletRequest request = new MockHttpRequest();
        PowerMockito.mockStatic(TaskImporterCacheOper.class);
        TaskImporterCacheOper taskImporterCacheOper = Mockito.mock(TaskImporterCacheOper.class);
        PowerMockito.when(TaskImporterCacheOper.getInstance()).thenReturn(taskImporterCacheOper);
        Mockito.when(taskImporterCacheOper.importLock(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        Mockito.when(taskImporterCacheOper.importUnlock(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        TaskUploadEntity taskUploadEntity = new TaskUploadEntity();
        taskUploadEntity.setFileName("filename");
        taskUploadEntity.setTaskLimitType("1,2,3,4");

        ArrayList<TaskUploadEntity> fileEntityList = new ArrayList<>();
        fileEntityList.add(taskUploadEntity);

        TaskUploadParam uploadParam = new TaskUploadParam();
        uploadParam.setFileEntityList(fileEntityList);

        Exception exp = null;
        try {
            taskImporter.importTask(request, uploadParam);
            taskUploadEntity.setFileByte(new String(Base64.getEncoder().encode(jsonstr.getBytes())));
            taskImporter.importTask(request, uploadParam);
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);
    }

    @Test
    public void checkSqlTest() {
        ColumnMapping columnMapping = new ColumnMapping();
        columnMapping.setIndicatorKey("indicatorKey");
        columnMapping.setOriginalValueKey("OriginalValueKey");
        columnMapping.setMeasUnitKey("MeasUnitKey");
        columnMapping.setIpKey("IpKey");
        columnMapping.setDnKey("DnKey");
        columnMapping.setTimeKey("TimeKey");
        DataSource dataSource = new DataSource();

        try {
            taskImporter.checkSql(columnMapping, dataSource);
        } catch (ServiceException e) {
            Assert.assertNotNull(e);
        }
    }

    /**
     * checkTest
     *
     * @throws Exception Exception
     */
    @Test
    public void checkTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getnotrggatorbytype.json");
        ImportTaskEntity importTaskEntity = new ImportTaskEntity();
        importTaskEntity.setTaskName("test");
        importTaskEntity.setTaskType("CorrelationAnalysis");
        String nodesJson = "[{\n" + "\t\t\t\"nodeName\": \"节点4\",\n" + "\t\t\t\"nodeType\": \"alarm\",  \n"
            + "\t\t\t\"extendJson\":[ \n"
            + "\t\t\t{\"field\":\"alarmId\",\"operator\":\"in\",\"values\":[\"999999999\"]}\n" + "\t\t\t],\n"
            + "\t\t\t\"algorithmModelName\": \"DVIndicatorAlarmAnalysis\", \n" + "\t\t\t\"kpiTimeRange\": \"5|HOUR\",\n"
            + "\t\t\t\"kpiTimeType\": \"recently\"\n" + "\t\t},{\n" + "\t\t\t\"nodeName\": \"节点5\",\n"
            + "\t\t\t\"nodeType\": \"log\",  \n" + "\t\t\t\"extendJson\":{\n" + "\t\t\t\t\"queryFields\":{ \n"
            + "\t\t\t\t\t\"Message\":\"agentIp\"\n" + "\t\t\t\t},\n"
            + "\t\t\t\t\"solutionType\":\"mpaas.momgr.MPAAS\", \n" + "\t\t\t\t\"logType\":\"dsflogs2\", \n"
            + "\t\t\t\t\"analysisField\":\"Message\" \n" + "\t\t\t},\n"
            + "\t\t\t\"algorithmModelName\": \"DVIndicatorLogAnalysis\",\n" + "\t\t\t\"kpiTimeRange\": \"7|HOUR\",\n"
            + "\t\t\t\"kpiTimeType\": \"recently\"\n" + "\t\t}]";
        List<TemplateNodeInfo> nodes = JSON.parseArray(nodesJson, TemplateNodeInfo.class);
        importTaskEntity.setNodes(nodes);
        importTaskEntity.setPaths(new ArrayList<>());
        Assert.assertTrue(importTaskValidCheker.check(importTaskEntity));
    }

    /**
     * checkTestError
     *
     * @throws Exception Exception
     */
    @Test
    public void checkTestError() throws Exception {
        // 抛异常用例
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getnotrggatorbytype.json");
        ImportTaskEntity importTaskEntity = new ImportTaskEntity();
        importTaskEntity.setTaskName("test");
        importTaskEntity.setTaskType("CorrelationAnalysis");
        String nodesJson = "[{\n" + "\t\t\t\"nodeName\": \"节点4\",\n" + "\t\t\t\"nodeType\": \"alarm\",  \n"
            + "\t\t\t\"extendJson\":[ \n"
            + "\t\t\t{\"field\":\"alarmId\",\"operator\":\"in\",\"values\":[\"101216\",\"101205\",\"999999995\",\"36\",\"999999999\",\"6\",\"7\",\"8\",\"9\",\"10\",\"11\",\"12\",\"13\",\"14\",\"15\",\"16\",\"17\",\"18\",\"19\",\"20\",\"21\",\"22\",\"23\",\"24\",\"25\",\"26\",\"27\",\"28\",\"29\",\"30\",\"31\",\"32\",\"33\",\"34\",\"35\",\"36\",\"37\",\"38\",\"39\",\"40\",\"41\",\"42\",\"43\",\"44\",\"45\",\"46\",\"47\",\"48\",\"49\",\"50\",\"51\",\"52\",\"53\",\"54\",\"55\",\"56\",\"57\",\"58\",\"59\",\"60\",\"61\",\"62\",\"63\",\"64\",\"65\",\"66\",\"67\",\"68\",\"69\",\"70\",\"71\",\"72\",\"73\",\"74\",\"75\",\"76\",\"77\",\"78\",\"79\",\"80\",\"81\",\"82\",\"83\",\"84\",\"85\",\"86\",\"87\",\"88\",\"89\",\"90\",\"91\",\"92\",\"93\",\"94\",\"95\",\"96\",\"97\",\"98\",\"99\",\"100\",\"101\",\"102\",\"103\",\"104\",\"105\",\"106\",\"107\",\"108\",\"109\",\"110\",\"111\",\"112\",\"113\",\"114\",\"115\",\"116\",\"117\",\"118\",\"119\",\"120\",\"121\",\"122\",\"123\",\"124\",\"125\",\"126\",\"127\",\"128\",\"129\",\"130\",\"131\",\"132\",\"133\",\"134\",\"135\",\"136\",\"137\",\"138\",\"139\",\"140\",\"141\",\"142\",\"143\",\"144\",\"145\",\"146\",\"147\",\"148\",\"149\",\"150\",\"151\",\"152\",\"153\",\"154\",\"155\",\"156\",\"157\",\"158\",\"159\",\"160\",\"161\",\"162\",\"163\",\"164\",\"165\",\"166\",\"167\",\"168\",\"169\",\"170\",\"171\",\"172\",\"173\",\"174\",\"175\",\"176\",\"177\",\"178\",\"179\",\"180\",\"181\",\"182\",\"183\",\"184\",\"185\",\"186\",\"187\",\"188\",\"189\",\"190\",\"191\",\"192\",\"193\",\"194\",\"195\",\"196\",\"197\",\"198\",\"199\",\"200\",\"201\",\"202\",\"203\",\"204\",\"205\",\"206\",\"207\",\"208\",\"209\",\"210\",\"211\",\"212\",\"213\",\"214\",\"215\",\"216\",\"217\",\"218\",\"219\",\"220\",\"221\",\"222\",\"223\",\"224\",\"225\",\"226\",\"227\",\"228\",\"229\",\"230\",\"231\",\"232\",\"233\",\"234\",\"235\",\"236\",\"237\",\"238\",\"239\",\"240\",\"241\",\"242\",\"243\",\"244\",\"245\",\"246\",\"247\",\"248\",\"249\",\"250\",\"251\",\"252\",\"253\",\"254\",\"255\",\"256\",\"257\",\"258\",\"259\",\"260\",\"261\",\"262\",\"263\",\"264\",\"265\",\"266\",\"267\",\"268\",\"269\",\"270\",\"271\",\"272\",\"273\",\"274\",\"275\",\"276\",\"277\",\"278\",\"279\",\"280\",\"281\",\"282\",\"283\",\"284\",\"285\",\"286\",\"287\",\"288\",\"289\",\"290\",\"291\",\"292\",\"293\",\"294\",\"295\",\"296\",\"297\",\"298\",\"299\",\"300\",\"301\",\"302\",\"303\",\"304\",\"305\",\"306\",\"307\",\"308\",\"309\",\"310\",\"311\",\"312\",\"313\",\"314\",\"315\",\"316\",\"317\",\"318\",\"319\",\"320\",\"321\",\"322\",\"323\",\"324\",\"325\",\"326\",\"327\",\"328\",\"329\",\"330\",\"331\",\"332\",\"333\",\"334\",\"335\",\"336\",\"337\",\"338\",\"339\",\"340\",\"341\",\"342\",\"343\",\"344\",\"345\",\"346\",\"347\",\"348\",\"349\",\"350\",\"351\",\"352\",\"353\",\"354\",\"355\",\"356\",\"357\",\"358\",\"359\",\"360\",\"361\",\"362\",\"363\",\"364\",\"365\",\"366\",\"367\",\"368\",\"369\",\"370\",\"371\",\"372\",\"373\",\"374\",\"375\",\"376\",\"377\",\"378\",\"379\",\"380\",\"381\",\"382\",\"383\",\"384\",\"385\",\"386\",\"387\",\"388\",\"389\",\"390\",\"391\",\"392\",\"393\",\"394\",\"395\",\"396\",\"397\",\"398\",\"399\",\"400\",\"401\",\"402\",\"403\",\"404\",\"405\",\"406\",\"407\",\"408\",\"409\",\"410\",\"411\",\"412\",\"413\",\"414\",\"415\",\"416\",\"417\",\"418\",\"419\",\"420\",\"421\",\"422\",\"423\",\"424\",\"425\",\"426\",\"427\",\"428\",\"429\",\"430\",\"431\",\"432\",\"433\",\"434\",\"435\",\"436\",\"437\",\"438\",\"439\",\"440\",\"441\",\"442\",\"443\",\"444\",\"445\",\"446\",\"447\",\"448\",\"449\",\"450\",\"451\",\"452\",\"453\",\"454\",\"455\",\"456\",\"457\",\"458\",\"459\",\"460\",\"461\",\"462\",\"463\",\"464\",\"465\",\"466\",\"467\",\"468\",\"469\",\"470\",\"471\",\"472\",\"473\",\"474\",\"475\",\"476\",\"477\",\"478\",\"479\",\"480\",\"481\",\"482\",\"483\",\"484\",\"485\",\"486\",\"487\",\"488\",\"489\",\"490\",\"491\",\"492\",\"493\",\"494\",\"495\",\"496\",\"497\",\"498\",\"499\",\"500\",\"501\",\"502\",\"503\",\"504\",\"505\",\"506\",\"507\",\"508\",\"509\",\"510\",\"511\",\"512\",\"513\",\"514\",\"515\",\"516\",\"517\",\"518\",\"519\",\"520\",\"521\",\"522\",\"523\",\"524\",\"525\",\"526\",\"527\",\"528\",\"529\",\"530\",\"531\",\"532\",\"533\",\"534\",\"535\",\"536\",\"537\",\"538\",\"539\",\"540\",\"541\",\"542\",\"543\",\"544\",\"545\",\"546\",\"547\",\"548\",\"549\",\"550\",\"551\",\"552\",\"553\",\"554\",\"555\",\"556\",\"557\",\"558\",\"559\",\"560\",\"561\",\"562\",\"563\",\"564\",\"565\",\"566\",\"567\",\"568\",\"569\",\"570\",\"571\",\"572\",\"573\",\"574\",\"575\",\"576\",\"577\",\"578\",\"579\",\"580\",\"581\",\"582\",\"583\",\"584\",\"585\",\"586\",\"587\",\"588\",\"589\",\"590\",\"591\",\"592\",\"593\",\"594\",\"595\",\"596\",\"597\",\"598\",\"599\",\"600\",\"601\",\"602\",\"603\",\"604\",\"605\",\"606\",\"607\",\"608\",\"609\",\"610\",\"611\",\"612\",\"613\",\"614\",\"615\",\"616\",\"617\",\"618\",\"619\",\"620\",\"621\",\"622\",\"623\",\"624\",\"625\",\"626\",\"627\",\"628\",\"629\",\"630\",\"631\",\"632\",\"633\",\"634\",\"635\",\"636\",\"637\",\"638\",\"639\",\"640\",\"641\",\"642\",\"643\",\"644\",\"645\",\"646\",\"647\",\"648\",\"649\",\"650\",\"651\",\"652\",\"653\",\"654\",\"655\",\"656\",\"657\",\"658\",\"659\",\"660\",\"661\",\"662\",\"663\",\"664\",\"665\",\"666\",\"667\",\"668\",\"669\",\"670\",\"671\",\"672\",\"673\",\"674\",\"675\",\"676\",\"677\",\"678\",\"679\",\"680\",\"681\",\"682\",\"683\",\"684\",\"685\",\"686\",\"687\",\"688\",\"689\",\"690\",\"691\",\"692\",\"693\",\"694\",\"695\",\"696\",\"697\",\"698\",\"699\",\"700\",\"701\",\"702\",\"703\",\"704\",\"705\",\"706\",\"707\",\"708\",\"709\",\"710\",\"711\",\"712\",\"713\",\"714\",\"715\",\"716\",\"717\",\"718\",\"719\",\"720\",\"721\",\"722\",\"723\",\"724\",\"725\",\"726\",\"727\",\"728\",\"729\",\"730\",\"731\",\"732\",\"733\",\"734\",\"735\",\"736\",\"737\",\"738\",\"739\",\"740\",\"741\",\"742\",\"743\",\"744\",\"745\",\"746\",\"747\",\"748\",\"749\",\"750\",\"751\",\"752\",\"753\",\"754\",\"755\",\"756\",\"757\",\"758\",\"759\",\"760\",\"761\",\"762\",\"763\",\"764\",\"765\",\"766\",\"767\",\"768\",\"769\",\"770\",\"771\",\"772\",\"773\",\"774\",\"775\",\"776\",\"777\",\"778\",\"779\",\"780\",\"781\",\"782\",\"783\",\"784\",\"785\",\"786\",\"787\",\"788\",\"789\",\"790\",\"791\",\"792\",\"793\",\"794\",\"795\",\"796\",\"797\",\"798\",\"799\",\"800\",\"801\",\"802\",\"803\",\"804\",\"805\",\"806\",\"807\",\"808\",\"809\",\"810\",\"811\",\"812\",\"813\",\"814\",\"815\",\"816\",\"817\",\"818\",\"819\",\"820\",\"821\",\"822\",\"823\",\"824\",\"825\",\"826\",\"827\",\"828\",\"829\",\"830\",\"831\",\"832\",\"833\",\"834\",\"835\",\"836\",\"837\",\"838\",\"839\",\"840\",\"841\",\"842\",\"843\",\"844\",\"845\",\"846\",\"847\",\"848\",\"849\",\"850\",\"851\",\"852\",\"853\",\"854\",\"855\",\"856\",\"857\",\"858\",\"859\",\"860\",\"861\",\"862\",\"863\",\"864\",\"865\",\"866\",\"867\",\"868\",\"869\",\"870\",\"871\",\"872\",\"873\",\"874\",\"875\",\"876\",\"877\",\"878\",\"879\",\"880\",\"881\",\"882\",\"883\",\"884\",\"885\",\"886\",\"887\",\"888\",\"889\",\"890\",\"891\",\"892\",\"893\",\"894\",\"895\",\"896\",\"897\",\"898\",\"899\",\"900\",\"901\",\"902\",\"903\",\"904\",\"905\",\"906\",\"907\",\"908\",\"909\",\"910\",\"911\",\"912\",\"913\",\"914\",\"915\",\"916\",\"917\",\"918\",\"919\",\"920\",\"921\",\"922\",\"923\",\"924\",\"925\",\"926\",\"927\",\"928\",\"929\",\"930\",\"931\",\"932\",\"933\",\"934\",\"935\",\"936\",\"937\",\"938\",\"939\",\"940\",\"941\",\"942\",\"943\",\"944\",\"945\",\"946\",\"947\",\"948\",\"949\",\"950\",\"951\",\"952\",\"953\",\"954\",\"955\",\"956\",\"957\",\"958\",\"959\",\"960\",\"961\",\"962\",\"963\",\"964\",\"965\",\"966\",\"967\",\"968\",\"969\",\"970\",\"971\",\"972\",\"973\",\"974\",\"975\",\"976\",\"977\",\"978\",\"979\",\"980\",\"981\",\"982\",\"983\",\"984\",\"985\",\"986\",\"987\",\"988\",\"989\",\"990\",\"991\",\"992\",\"993\",\"994\",\"995\",\"996\",\"997\",\"998\",\"999\",\"1000\",\"1001\",\"1002\",\"1003\"]}\n" + "\t\t\t],\n"
            + "\t\t\t\"algorithmModelName\": \"DVIndicatorAlarmAnalysis\", \n" + "\t\t\t\"kpiTimeRange\": \"5|DAY\",\n"
            + "\t\t\t\"kpiTimeType\": \"recently\"\n" + "\t\t},{\n" + "\t\t\t\"nodeName\": \"节点5\",\n"
            + "\t\t\t\"nodeType\": \"log\",  \n" + "\t\t\t\"extendJson\":{\n" + "\t\t\t\t\"queryFields\":{ \n"
            + "\t\t\t\t\t\"Message\":\"agentIp\"\n" + "\t\t\t\t},\n"
            + "\t\t\t\t\"solutionType\":\"mpaas.momgr.MPAAS\", \n" + "\t\t\t\t\"logType\":\"dsflogs2\", \n"
            + "\t\t\t\t\"analysisField\":\"Message\" \n" + "\t\t\t},\n"
            + "\t\t\t\"algorithmModelName\": \"DVIndicatorLogAnalysis\",\n" + "\t\t\t\"kpiTimeRange\": \"5|DAY\",\n"
            + "\t\t\t\"kpiTimeType\": \"recently\"\n" + "\t\t}]";
        List<TemplateNodeInfo> nodes = JSON.parseArray(nodesJson, TemplateNodeInfo.class);
        importTaskEntity.setNodes(nodes);
        importTaskEntity.setPaths(new ArrayList<>());
        ServiceException exp = null;
        try {
            importTaskValidCheker.check(importTaskEntity);
        } catch (ServiceException e) {
            exp = e;
        }
        Assert.assertNotNull(exp);
    }

    @AfterClass
    public static void after() {
        mockServer.stop();
    }

    @Test
    public void importUnitedIndicatorPredictTaskTest() throws ServiceException {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getnotrggatorbytype.json");
        String jsonTask = "{\n" + "  \"taskType\": \"UnitedIndicatorPredict\", \n"
            + "  \"taskSplit\": \"bySiteId\",              \n" + "  \"taskList\": [\n" + "    {\n"
            + "      \"taskName\": \"UnitedImport\",       \n" + "      \"indicatorList\": [               \n"
            + "        {\n" + "          \"clusterName\": \"OfflineChgHost\",  \n" + "          \"moType\": \"OMS\",\n"
            + "          \"measUnitKey\": \"CPUInterval\",\n"
            + "          \"measTypeKey\": \"hwAMOSCpuUsageAverage\",\n" + "          \"auxiliaryIndicator\": [\n"
            + "            {\n" + "              \"moType\": \"OMS\",\n"
            + "              \"measUnitKey\": \"I2K_FMMGR\",\n" + "              \"measTypeKey\": \"FMAlarmDelay\"\n"
            + "            }\n" + "          ]\n" + "        }\n" + "      ],\n"
            + "      \"taskDetail\": \"用户输入定位信息\",   \n" + "      \"predictCron\": \"10|MIN\",\n"
            + "\t  \"algorithmModel\":{ \n" + "\t\t\"modelName\":\"DVIndicatorForecast\", \n"
            + "\t\t\"version\":\"default\", \n" + "\t\t\"parameter\":[{\n"
            + "\t\t\t\"parameterName\":\"min_train_num\", \n" + "\t\t\t\"parameterDefaultValue\":\"1440\" \n"
            + "\t\t},{\n" + "\t\t\t\"parameterName\":\"missing_percent\",\n"
            + "\t\t\t\"parameterDefaultValue\":\"0.4\"\n" + "\t\t},{\n"
            + "\t\t\"parameterName\":\"union_node_config\",\n" + "\t\t\t\"parameterDefaultValue\":\"avg\"\n"
            + "\t\t}]\n" + "\t }\t  \n" + "    }\n" + "  ]\n" + "}";
        ServiceException exp = null;
        try {
            String algorithmStr
                = "[{\"description\":\"支撑任务训练最小的数据量。参数值为整数，取值范围为10-40320。当数据量较少时无法保证检测质量，请根据实际场景需要配置。\",\"displayName\":\"最小训练数据数量\",\"parameterDefaultValue\":1440,\"parameterName\":\"min_train_num\",\"type\":\"int\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":10,\"max\":40320}}},{\"description\":\"预测阶段预测数据的点数。参数值为整数，取值范围为1-40320。\",\"displayName\":\"预测点数\",\"parameterDefaultValue\":60,\"parameterName\":\"predict_length\",\"type\":\"int\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":1,\"max\":40320}}},{\"description\":\"训练时指标缺失点的比例，如果大于参数值，则数据存在问题，不预测结果。参数值为小数，取值范围为0.0-1.0。\",\"displayName\":\"缺失点比例\",\"parameterDefaultValue\":0.4,\"parameterName\":\"missing_percent\",\"type\":\"float\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":0.0,\"max\":1.0}}},{\"description\":\"联合指标节点数据的汇聚方式，默认为avg，表示取节点数据的平均值进行汇聚；设置为max，表示取节点数据的最大值进行汇聚。\",\"displayName\":\"节点聚合方式\",\"parameterDefaultValue\":\"avg\",\"parameterName\":\"union_node_config\",\"type\":\"string\",\"valueRange\":{\"type\":\"array\",\"value\":[\"max\",\"avg\"]}},{\"description\":\"训练样本中的数据点个数。默认值为10080，用户可以根据实际情况进行配置，为了保证算法的准确率以及展示数据范围完整，建议该参数值大于等于默认值。取值范围为1440-120960。\",\"displayName\":\"训练数据点个数\",\"parameterDefaultValue\":10080,\"parameterName\":\"train_point_need\",\"type\":\"int\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":1440,\"max\":120960}}},{\"description\":\"用于选择推理数据数据点，假设推理数据点个数为x，则表示最多获取当前时间往前的x个采集周期内数据点数作为推理数据。用户可以根据实际情况进行配置。该参数值越大，准确率越高，但是速度越慢，建议取值范围为10-4032。\",\"displayName\":\"推理数据点个数\",\"parameterDefaultValue\":720,\"parameterName\":\"detect_point_need\",\"type\":\"int\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":10,\"max\":4032}}}]";
            Mockito.when(algorithmManageService.defaultParam(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSON.parseArray(algorithmStr, ModelParameter.class));
            List<AlgorithmModel> algorithmList = new ArrayList<>();
            AlgorithmModel model = new AlgorithmModel();
            model.setId(5);
            algorithmList.add(model);
            AlgorithmResult algorithmResult = new AlgorithmResult();
            algorithmResult.setAlgorithmList(algorithmList);
            Mockito.when(algorithmManageService.getAlgorithm(Mockito.any())).thenReturn(algorithmResult);
            taskImporter.importTask(jsonTask, null, "1", null, null);
        } catch (ServiceException e) {
            exp = e;
        }
        Assert.assertNull(exp);
    }

    @Test
    @Ignore
    public void executeNodeHandlerTest() throws NoSuchMethodException, ServiceException {
        FlowAction action = new FlowAction();
        action.setFlowGroup("1");
        List<FlowAction> list = new ArrayList<>();
        list.add(action);
        PreExecutionNode preExecutionNode = new PreExecutionNode();
        preExecutionNode.setFlowActions(list);
        String jsonObject = JSONObject.toJSONString(preExecutionNode);
        TriggerExecutionNode triggerExecutionNode = new TriggerExecutionNode();
        triggerExecutionNode.setDataSourceName("");
        triggerExecutionNode.setIndicatorDataType("DV_FLOW");
        triggerExecutionNode.setExtendJson(jsonObject);
        TemplateTreeNode templateTreeNode = new TemplateTreeNode();

        List<String> list1 = new ArrayList<>();
        list1.add("2");

        PowerMockito.mockStatic(DomainUtil.class);
        PowerMockito.when(DomainUtil.getFlowGroupWithPerm(ContextUtils.getContext().getHeaderMap())).thenReturn(list1);

        Method executeNodeHandler = taskImporter.getClass()
            .getDeclaredMethod("executeNodeHandler", TriggerExecutionNode.class, TemplateTreeNode.class, Map.class,
                List.class);
        executeNodeHandler.setAccessible(true);

        Exception exp1 = null;
        try {
            executeNodeHandler.invoke(taskImporter, triggerExecutionNode, templateTreeNode, new HashMap<>(),
                new ArrayList<>());
        } catch (Exception e) {
            exp1 = e;
        }
        Assert.assertNotNull(exp1);

        FlowAction action1 = new FlowAction();
        action1.setFlowGroup("2");
        List<FlowAction> list2 = new ArrayList<>();
        list2.add(action1);
        PreExecutionNode preExecutionNode1 = new PreExecutionNode();
        preExecutionNode1.setFlowActions(list2);
        String jsonObject1 = JSONObject.toJSONString(preExecutionNode1);
        TriggerExecutionNode triggerExecutionNode1 = new TriggerExecutionNode();
        triggerExecutionNode1.setDataSourceName("");
        triggerExecutionNode1.setIndicatorDataType("DV_FLOW");
        triggerExecutionNode1.setExtendJson(jsonObject1);
        Exception exp2 = null;
        try {
            executeNodeHandler.invoke(taskImporter, triggerExecutionNode1, templateTreeNode, new HashMap<>(),
                new ArrayList<>());
        } catch (Exception e) {
            exp2 = e;
        }
        Assert.assertNotNull(exp2);

        FlowAction action2 = new FlowAction();
        action2.setFlowGroup("2");
        action2.setFlowName("2");
        List<FlowAction> list3 = new ArrayList<>();
        list3.add(action2);
        PreExecutionNode preExecutionNode2 = new PreExecutionNode();
        preExecutionNode2.setFlowActions(list3);
        String jsonObject2 = JSONObject.toJSONString(preExecutionNode2);
        TriggerExecutionNode triggerExecutionNode2 = new TriggerExecutionNode();
        triggerExecutionNode2.setDataSourceName("");
        triggerExecutionNode2.setIndicatorDataType("DV_FLOW");
        triggerExecutionNode2.setExtendJson(jsonObject2);
        List<String> flowIds = new ArrayList<>();
        PowerMockito.when(
                DomainUtil.getFlowWithPermBySolutionAndFlowName(ContextUtils.getContext().getHeaderMap(), "2", "2"))
            .thenReturn(flowIds);
        Exception exp3 = null;
        try {
            executeNodeHandler.invoke(taskImporter, triggerExecutionNode2, templateTreeNode, new HashMap<>(),
                new ArrayList<>());
        } catch (Exception e) {
            exp3 = e;
        }
        Assert.assertNotNull(exp3);

        Exception exp4 = null;
        triggerExecutionNode2.setIndicatorDataType("DV_SHOW");
        try {
            executeNodeHandler.invoke(taskImporter, triggerExecutionNode2, templateTreeNode, new HashMap<>(),
                new ArrayList<>());
        } catch (Exception e) {
            exp4 = e;
        }
        Assert.assertNull(exp4);
    }

    @Test
    public void indicatorGroupByTaskIdTest() throws NoSuchMethodException {

        ImportTask importTask = new ImportTask();
        importTask.setTaskName("name");
        List<TriggerIndicator> clusterIndicator = new ArrayList<>();
        TriggerIndicator triggerIndicator = new TriggerIndicator();
        triggerIndicator.setClusterName("name");
        clusterIndicator.add(triggerIndicator);
        importTask.setIndicatorList(clusterIndicator);
        Method indicatorGroupByTaskId = taskImporter.getClass()
            .getDeclaredMethod("indicatorGroupByTaskId", ImportTask.class);
        indicatorGroupByTaskId.setAccessible(true);

        PowerMockito.mockStatic(CollectionUtils.class);
        PowerMockito.when(CollectionUtils.isEmpty(Mockito.anyCollection())).thenReturn(false);
        Exception ex1 = null;
        try {
            indicatorGroupByTaskId.invoke(taskImporter, importTask);
        } catch (IllegalAccessException | InvocationTargetException e) {
            ex1 = e;
        }
        Assert.assertNull(ex1);
    }

    @Test
    public void regexValidTest() throws NoSuchMethodException {

        ModelParameter modelParameter = new ModelParameter();
        Method regexValid = taskImporter.getClass().getDeclaredMethod("regexValid", ModelParameter.class, String.class);
        regexValid.setAccessible(true);
        Exception ex = null;
        try {
            regexValid.invoke(taskImporter, modelParameter, "()");
        } catch (IllegalAccessException | InvocationTargetException e) {
            ex = e;
        }
        Assert.assertNotNull(ex);

        String algorithmStr
            = "[{\"description\":\"用于计算关联度的周期偏移值。单位为分钟，用于计算指标关联度时间偏移值，假设偏移周期设置为x，则表示将某时刻前移或后移x分钟，计算与相邻指标的关联度，并将计算结果最大值作为关联度计算结果。\",\"displayName\":\"偏移周期\",\"parameterDefaultValue\":\"5\",\"parameterName\":\"window_size\",\"type\":\"int\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":1,\"max\":60}}},{\"description\":\"关联特征的敏感程度。取值范围为0.0-1.0，小数点后最多支持3位数字。敏感度值越小，算法对指标关联特征越敏感；敏感度值越大，对指标关联特征越不敏感。\",\"displayName\":\"敏感度\",\"parameterDefaultValue\":\"0.2\",\"parameterName\":\"sensitivity\",\"type\":\"float\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":0.0,\"max\":1.0}}},{\"description\":\"{}\",\"displayName\":\"关联匹配（分析关联指标与主指标的线性相关性）\",\"parameterDefaultValue\":\"association_matching\",\"parameterName\":\"feature_type_info\",\"type\":\"string\",\"valueRange\":{\"type\":\"array\",\"value\":[{\"feature_type\":\"association_matching\",\"feature_description\":\"分析关联指标与主指标的线性相关性\",\"feature_type_name\":\"关联匹配\"},{\"feature_type\":\"range_matching\",\"feature_description\":\"分析指标是否越限\",\"feature_type_name\":\"范围匹配\"},{\"feature_type\":\"fluctuation_matching\",\"feature_description\":\"分析指标数据是否发生波动\",\"feature_type_name\":\"波动匹配\"},{\"feature_type\":\"surge_matching\",\"feature_description\":\"分析指标是否发生突增\",\"feature_type_name\":\"突增匹配\"},{\"feature_type\":\"sudden_drop_matching\",\"feature_description\":\"分析指标是否发生突降\",\"feature_type_name\":\"突降匹配\"},{\"feature_type\":\"change_percentage_matching\",\"feature_description\":\"判断关联指标在主指标异常中的占比\",\"feature_type_name\":\"变化占比匹配\"},{\"feature_type\":\"homogeneous_comparison_matching\",\"feature_description\":\"判断指标是否离群\",\"feature_type_name\":\"同质比较匹配\"}]}}]";
        Exception ex1 = null;
        try {
            regexValid.invoke(taskImporter, modelParameter, algorithmStr);
        } catch (IllegalAccessException | InvocationTargetException e) {
            ex1 = e;
        }
        Assert.assertNotNull(ex1);

        Exception ex2 = null;
        try {
            regexValid.invoke(taskImporter, modelParameter, "");
        } catch (IllegalAccessException | InvocationTargetException e) {
            ex2 = e;
        }
        Assert.assertNull(ex2);
    }

    @Test
    public void importAnomalyDetectionTaskTest() throws ServiceException {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getnotrggatorbytype.json");
        String jsonTask = "{\n" + "  \"taskType\": \"AnomalyDetection\", \"situationName\":\"123\", \n"
            + "  \"taskSplit\": \"bySiteId\",              \n" + "  \"taskList\": [\n" + "    {\n"
            + "      \"taskName\": \"UnitedImport\",       \n" + "      \"indicatorList\": [{\"moType\": \"OMS\",\n"
            + "              \"measUnitKey\": \"I2K_FMMGR\",\n" + "              \"measTypeKey\": \"FMAlarmDelay\"}\n" + "        \n" + "      ],\n"
            + "      \"taskDetail\": \"用户输入定位信息\",   \n" + "      \"predictCron\": \"10|MIN\",\n"
            + "\t  \"algorithmModel\":{ \n" + "\t\t\"modelName\":\"DVIndicatorForecast\", \n"
            + "\t\t\"version\":\"default\", \n" + "\t\t\"parameter\":[{\n"
            + "\t\t\t\"parameterName\":\"min_train_num\", \n" + "\t\t\t\"parameterDefaultValue\":\"1440\" \n"
            + "\t\t},{\n" + "\t\t\t\"parameterName\":\"missing_percent\",\n"
            + "\t\t\t\"parameterDefaultValue\":\"0.4\"\n" + "\t\t},{\n"
            + "\t\t\"parameterName\":\"union_node_config\",\n" + "\t\t\t\"parameterDefaultValue\":\"avg\"\n"
            + "\t\t}]\n" + "\t }\t  \n" + "    }\n" + "  ]\n" + "}";
        ServiceException exp = null;
        try {
            String algorithmStr
                = "[{\"description\":\"支撑任务训练最小的数据量。参数值为整数，取值范围为10-40320。当数据量较少时无法保证检测质量，请根据实际场景需要配置。\",\"displayName\":\"最小训练数据数量\",\"parameterDefaultValue\":1440,\"parameterName\":\"min_train_num\",\"type\":\"int\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":10,\"max\":40320}}},{\"description\":\"预测阶段预测数据的点数。参数值为整数，取值范围为1-40320。\",\"displayName\":\"预测点数\",\"parameterDefaultValue\":60,\"parameterName\":\"predict_length\",\"type\":\"int\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":1,\"max\":40320}}},{\"description\":\"训练时指标缺失点的比例，如果大于参数值，则数据存在问题，不预测结果。参数值为小数，取值范围为0.0-1.0。\",\"displayName\":\"缺失点比例\",\"parameterDefaultValue\":0.4,\"parameterName\":\"missing_percent\",\"type\":\"float\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":0.0,\"max\":1.0}}},{\"description\":\"联合指标节点数据的汇聚方式，默认为avg，表示取节点数据的平均值进行汇聚；设置为max，表示取节点数据的最大值进行汇聚。\",\"displayName\":\"节点聚合方式\",\"parameterDefaultValue\":\"avg\",\"parameterName\":\"union_node_config\",\"type\":\"string\",\"valueRange\":{\"type\":\"array\",\"value\":[\"max\",\"avg\"]}},{\"description\":\"训练样本中的数据点个数。默认值为10080，用户可以根据实际情况进行配置，为了保证算法的准确率以及展示数据范围完整，建议该参数值大于等于默认值。取值范围为1440-120960。\",\"displayName\":\"训练数据点个数\",\"parameterDefaultValue\":10080,\"parameterName\":\"train_point_need\",\"type\":\"int\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":1440,\"max\":120960}}},{\"description\":\"用于选择推理数据数据点，假设推理数据点个数为x，则表示最多获取当前时间往前的x个采集周期内数据点数作为推理数据。用户可以根据实际情况进行配置。该参数值越大，准确率越高，但是速度越慢，建议取值范围为10-4032。\",\"displayName\":\"推理数据点个数\",\"parameterDefaultValue\":720,\"parameterName\":\"detect_point_need\",\"type\":\"int\",\"valueRange\":{\"type\":\"range\",\"value\":{\"min\":10,\"max\":4032}}}]";
            Mockito.when(algorithmManageService.defaultParam(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(JSON.parseArray(algorithmStr, ModelParameter.class));
            List<AlgorithmModel> algorithmList = new ArrayList<>();
            AlgorithmModel model = new AlgorithmModel();
            model.setId(5);
            algorithmList.add(model);
            AlgorithmResult algorithmResult = new AlgorithmResult();
            algorithmResult.setAlgorithmList(algorithmList);
            Mockito.when(algorithmManageService.getAlgorithm(Mockito.any())).thenReturn(algorithmResult);
            taskImporter.importTask(jsonTask, null, "1", null, null);
        } catch (ServiceException e) {
            exp = e;
        }
        Assert.assertNotNull(exp);
    }

    @Test
    public void importAnomalyDetectionTaskUpdateTest() throws Exception {
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getnotrggatorbytype.json");
        PowerMockito.mockStatic(HofsUtil.class);
        PowerMockito.doReturn(true).when(HofsUtil.class, "uploadFile", Mockito.anyString(), Mockito.anyString());
        AnalysisTask task = JSONObject.parseObject(
            "{\"alarmType\":2,\"algorithmModelId\":\"1\",\"algorithmModelName\":\"DVAnomalyDetection_default\",\"algorithmParam\":\"[{\\\"description\\\":\\\"推荐训练需要的数据点数。\\\",\\\"displayName\\\":\\\"训练数据点个数\\\",\\\"parameterDefaultValue\\\":\\\"1470\\\",\\\"parameterName\\\":\\\"train_point_need\\\",\\\"required\\\":true,\\\"type\\\":\\\"int\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":1440,\\\"max\\\":40320},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"指标连续缺点是否上报告警开关，默认为no。若设置为yes，表示开启缺点上报告警功能。在指标连续缺点大于“指标连续缺点个数”时并且打开任务上报告警开关，可上报缺点异常告警。若设置为no，即不开启缺点上报告警功能。\\\",\\\"displayName\\\":\\\"指标缺点上报开关\\\",\\\"parameterDefaultValue\\\":\\\"no\\\",\\\"parameterName\\\":\\\"report_missing_points_switch\\\",\\\"required\\\":true,\\\"type\\\":\\\"string\\\",\\\"valueRange\\\":{\\\"value\\\":[\\\"yes\\\",\\\"no\\\"],\\\"type\\\":\\\"array\\\"}},{\\\"description\\\":\\\"指标连续缺点个数，参数值为整数，取值范围为3-30。\\\",\\\"displayName\\\":\\\"指标连续缺点个数\\\",\\\"parameterDefaultValue\\\":\\\"5\\\",\\\"parameterName\\\":\\\"missing_points_num\\\",\\\"required\\\":true,\\\"type\\\":\\\"int\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":3,\\\"max\\\":30},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"漂移检测窗口内的数据点数。参数值为整数，取值范围为1-30。\\\",\\\"displayName\\\":\\\"漂移检测窗口点数\\\",\\\"parameterDefaultValue\\\":\\\"5\\\",\\\"parameterName\\\":\\\"drift_detect_window_points\\\",\\\"required\\\":true,\\\"type\\\":\\\"int\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":1,\\\"max\\\":30},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"检测窗口的越限数据量大于该参数值时上报异常。参数值为整数，取值范围为1-30且小于等于“检测窗口数据量”。\\\",\\\"displayName\\\":\\\"上报异常阈值\\\",\\\"parameterDefaultValue\\\":\\\"3\\\",\\\"parameterName\\\":\\\"detect_num\\\",\\\"required\\\":true,\\\"type\\\":\\\"int\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":1,\\\"max\\\":30},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"取值范围为0.0-10000000000，小数点后最多支持3位数字。当手动设置的上阈值小于推理计算出的上阈值时，该参数生效。\\\",\\\"displayName\\\":\\\"手动设置的上阈值\\\",\\\"parameterDefaultValue\\\":\\\"10000.0\\\",\\\"parameterName\\\":\\\"manual_uppervalue\\\",\\\"required\\\":true,\\\"type\\\":\\\"float\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":0.0,\\\"max\\\":10000000000.0},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"手动设置阈值的开关，默认为no。设置为no时，表示关闭；设置为yes时，表示打开；设置为upper时，表示只设置上阈值；设置为lower时，表示只设置下阈值，且需要配置手动设置的上阈值、手动设置的下阈值。\\\",\\\"displayName\\\":\\\"手动设置阈值开关\\\",\\\"parameterDefaultValue\\\":\\\"yes\\\",\\\"parameterName\\\":\\\"manual_switch\\\",\\\"required\\\":true,\\\"type\\\":\\\"string\\\",\\\"valueRange\\\":{\\\"value\\\":[\\\"yes\\\",\\\"no\\\",\\\"upper\\\",\\\"lower\\\"],\\\"type\\\":\\\"array\\\"}},{\\\"description\\\":\\\"若漂移检测窗口内达到“漂移得分阈值”的点数大于等于该参数值且当前点的漂移得分达到“漂移得分阈值”，则将当前点判别为概念漂移点。参数值为整数，取值范围为1-30且小于等于“漂移检测窗口点数”。\\\",\\\"displayName\\\":\\\"漂移检测判别最小点数\\\",\\\"parameterDefaultValue\\\":\\\"3\\\",\\\"parameterName\\\":\\\"concept_drift_detect_num\\\",\\\"required\\\":true,\\\"type\\\":\\\"int\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":1,\\\"max\\\":30},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"检测窗口内的数据数量。参数值为整数，取值范围为1-30。\\\",\\\"displayName\\\":\\\"检测窗口数据量\\\",\\\"parameterDefaultValue\\\":\\\"5\\\",\\\"parameterName\\\":\\\"window_size\\\",\\\"required\\\":true,\\\"type\\\":\\\"int\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":1,\\\"max\\\":30},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"推荐检测需要的数据点数。\\\",\\\"displayName\\\":\\\"推理数据点个数\\\",\\\"parameterDefaultValue\\\":\\\"33\\\",\\\"parameterName\\\":\\\"detect_point_need\\\",\\\"required\\\":true,\\\"type\\\":\\\"int\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":20,\\\"max\\\":50},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"是否开启模型迁移，默认为no。若设置为yes，表示开启模型迁移，在部分指标不满足最小训练数量无法训练出模型的情况下，可以通过模型迁移，将其他指标或已衰退指标的模型迁移到该指标上，训练出模型。若设置为no，即不开启模型迁移。\\\",\\\"displayName\\\":\\\"模型迁移开关\\\",\\\"parameterDefaultValue\\\":\\\"no\\\",\\\"parameterName\\\":\\\"transfer\\\",\\\"required\\\":true,\\\"type\\\":\\\"string\\\",\\\"valueRange\\\":{\\\"value\\\":[\\\"yes\\\",\\\"no\\\"],\\\"type\\\":\\\"array\\\"}},{\\\"description\\\":\\\"波动检测开关，主要针对分钟级突变但没有满足“上报异常阈值”的情况。默认值为no，即使用“上报异常阈值”参数配置的逻辑上报异常，若开关为yes，即指标值前后波动大于等于“波动幅度”且超出阈值线范围时直接上报异常（如果识别为突降，则突降波动幅度最大为1.0），非分钟级突变的情况仍然使用“上报异常阈值”参数配置的逻辑上报异常。\\\",\\\"displayName\\\":\\\"波动检测开关\\\",\\\"parameterDefaultValue\\\":\\\"no\\\",\\\"parameterName\\\":\\\"detect_without_windows\\\",\\\"required\\\":true,\\\"type\\\":\\\"string\\\",\\\"valueRange\\\":{\\\"value\\\":[\\\"yes\\\",\\\"no\\\"],\\\"type\\\":\\\"array\\\"}},{\\\"description\\\":\\\"快速推理开关，默认为no。设置为no时，表示关闭；设置为yes时，表示打开，需要配置快速推理点数。表示在训练的阶段生成历史阈值线，提高推理速度。\\\",\\\"displayName\\\":\\\"快速推理开关\\\",\\\"parameterDefaultValue\\\":\\\"no\\\",\\\"parameterName\\\":\\\"manual_train_detect\\\",\\\"required\\\":true,\\\"type\\\":\\\"string\\\",\\\"valueRange\\\":{\\\"value\\\":[\\\"yes\\\",\\\"no\\\"],\\\"type\\\":\\\"array\\\"}},{\\\"description\\\":\\\"表示是否展示下阈值线，默认为yes。设置为yes时，指标详情中展示该任务相关指标的下阈值线；设置为no时，指标详情中不展示该任务相关指标的下阈值线。\\\",\\\"displayName\\\":\\\"下阈值展示开关\\\",\\\"parameterDefaultValue\\\":\\\"yes\\\",\\\"parameterName\\\":\\\"lowerlimit_flag\\\",\\\"required\\\":true,\\\"type\\\":\\\"string\\\",\\\"valueRange\\\":{\\\"value\\\":[\\\"yes\\\",\\\"no\\\"],\\\"type\\\":\\\"array\\\"}},{\\\"description\\\":\\\"取值范围为0.0-10000000000，小数点后最多支持3位数字。当手动设置的下阈值大于推理计算出的下阈值时，该参数生效。\\\",\\\"displayName\\\":\\\"手动设置的下阈值\\\",\\\"parameterDefaultValue\\\":\\\"10000\\\",\\\"parameterName\\\":\\\"manual_lowervalue\\\",\\\"required\\\":true,\\\"type\\\":\\\"float\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":0.0,\\\"max\\\":10000000000.0},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"被检测数据的数据类型。其中：1表示不确定的数据类型，2表示平稳型，3表示周期型，4表示非平稳型，5表示稀疏型。\\\",\\\"displayName\\\":\\\"数据类型\\\",\\\"parameterDefaultValue\\\":\\\"1\\\",\\\"parameterName\\\":\\\"data_type\\\",\\\"required\\\":true,\\\"type\\\":\\\"int\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":1,\\\"max\\\":5},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"数据波动幅度的阈值。若数据波动幅度超过该阈值，则认为数据前后发生较大波动，默认值为0.1，取值范围为0.0-9999999999.0, 最多三位小数。\\\",\\\"displayName\\\":\\\"波动幅度\\\",\\\"parameterDefaultValue\\\":\\\"0.1\\\",\\\"parameterName\\\":\\\"fluctuation_ratio\\\",\\\"required\\\":true,\\\"type\\\":\\\"float\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":0.0,\\\"max\\\":9999999999.0},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"表示是否展示上阈值线，默认为yes。设置为yes时，指标详情中展示该任务相关指标的上阈值线；设置为no时，指标详情中不展示该任务相关指标的上阈值线。\\\",\\\"displayName\\\":\\\"上阈值展示开关\\\",\\\"parameterDefaultValue\\\":\\\"yes\\\",\\\"parameterName\\\":\\\"upperlimit_flag\\\",\\\"required\\\":true,\\\"type\\\":\\\"string\\\",\\\"valueRange\\\":{\\\"value\\\":[\\\"yes\\\",\\\"no\\\"],\\\"type\\\":\\\"array\\\"}},{\\\"description\\\":\\\"过滤训练数据异常点，默认为yes。设置为yes时，指标中的异常点会被过滤，过滤后的指标作为训练数据；设置为no时，所有指标点作为训练数据。\\\",\\\"displayName\\\":\\\"过滤训练数据异常点\\\",\\\"parameterDefaultValue\\\":\\\"yes\\\",\\\"parameterName\\\":\\\"train_filter\\\",\\\"required\\\":true,\\\"type\\\":\\\"string\\\",\\\"valueRange\\\":{\\\"value\\\":[\\\"yes\\\",\\\"no\\\"],\\\"type\\\":\\\"array\\\"}},{\\\"description\\\":\\\"支撑任务训练最小的数据量。参数值为整数，取值范围为10-40320。当数据量较少时无法保证检测质量，请根据实际场景需要配置。\\\",\\\"displayName\\\":\\\"最小训练数据数量\\\",\\\"parameterDefaultValue\\\":\\\"1440\\\",\\\"parameterName\\\":\\\"min_train_num\\\",\\\"required\\\":true,\\\"type\\\":\\\"int\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":10,\\\"max\\\":40320},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"阈值线与真实曲线的贴近程度，即对异常数据的敏感程度。取值范围为1.0-6.0，小数点后最多支持3位数字。敏感度值越大，阈值上下限曲线越远离真实曲线，对于异常数据越不敏感；敏感度值越小，阈值上下限曲线越贴近真实曲线，对异常数据越敏感。\\\",\\\"displayName\\\":\\\"敏感度\\\",\\\"parameterDefaultValue\\\":\\\"4.0\\\",\\\"parameterName\\\":\\\"sensitivity\\\",\\\"required\\\":true,\\\"type\\\":\\\"float\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":1.0,\\\"max\\\":6.0},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"在训练阶段生成阈值线的数据点数。若快速推理点数设置为n，则表示在训练阶段生成当前时刻往前n个数据点的阈值线。\\\",\\\"displayName\\\":\\\"快速推理点数\\\",\\\"parameterDefaultValue\\\":\\\"300\\\",\\\"parameterName\\\":\\\"train_detect_point\\\",\\\"required\\\":true,\\\"type\\\":\\\"int\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":30,\\\"max\\\":1440},\\\"type\\\":\\\"range\\\"}},{\\\"description\\\":\\\"用于判断当前点的漂移得分是否达到阈值。取值范围为0.0-1.0，小数点后最多支持3位数字。\\\",\\\"displayName\\\":\\\"漂移得分阈值\\\",\\\"parameterDefaultValue\\\":\\\"0.5\\\",\\\"parameterName\\\":\\\"concept_drift_threshold\\\",\\\"required\\\":true,\\\"type\\\":\\\"float\\\",\\\"valueRange\\\":{\\\"value\\\":{\\\"min\\\":0.0,\\\"max\\\":1.0},\\\"type\\\":\\\"range\\\"}}]\",\"beginTime\":1744880896535,\"conceptDriftRetrain\":true,\"conceptDriftSwitch\":true,\"dataSourceType\":\"performance\",\"datasourceId\":\"1\",\"importTaskStatus\":true,\"indicatorList\":[{\"abnormal\":1,\"aggregateMos\":[],\"auxiliaryIndicator\":[],\"displayValue\":\"************\",\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"groupDnList\":[],\"hasMeasObj\":\"true\",\"indexId\":\"I2K_OS~~UsedCPURate\",\"indexName\":\"CPU占用率(%)\",\"indicatorId\":\"OS=1I2K_OSUsedCPURate************\",\"measTypeKey\":\"UsedCPURate\",\"measUnitKey\":\"I2K_OS\",\"measUnitName\":\"操作系统监控\",\"moType\":\"OMS\",\"originalValue\":\"ip=************\",\"resourceTypeKey\":\"OMS\",\"softDelete\":false,\"softDeleteTimeStamp\":0,\"taskId\":7,\"unit\":\"%\"},{\"abnormal\":1,\"aggregateMos\":[],\"auxiliaryIndicator\":[],\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"groupDnList\":[],\"hasMeasObj\":\"false\",\"indexId\":\"I2K_FMMGR~~FMDataPush\",\"indexName\":\"每分钟处理数据(条)\",\"indicatorId\":\"OS=1I2K_FMMGRFMDataPush\",\"measTypeKey\":\"FMDataPush\",\"measUnitKey\":\"I2K_FMMGR\",\"measUnitName\":\"告警监控\",\"moType\":\"OMS\",\"resourceTypeKey\":\"OMS\",\"softDelete\":false,\"softDeleteTimeStamp\":0,\"taskId\":7,\"unit\":\"条\"}],\"indicatorPredictNum\":33,\"indicatorSelectType\":1,\"indicatorTrainNum\":1470,\"lastExecutorUserId\":\"1\",\"lastModifyUserId\":\"1\",\"logAbnormalPattern\":[],\"periodicType\":0,\"predictCron\":\"1|MIN\",\"reportAlarm\":1,\"reportAlarmId\":\"505410002\",\"reportAlarmName\":\"上报告警名称\",\"scheduleConfig\":[],\"sendEvent\":false,\"situationId\":4,\"situationName\":\"lalala\",\"sourceName\":\"默认\",\"startStatus\":2,\"status\":2,\"strategyNames\":[],\"subSite\":\"bySolutionId\",\"taskDesc\":\"描述\",\"taskDetail\":\"用户输入定位信息\",\"taskId\":7,\"taskName\":\"多站点new_subSite_empty\",\"taskType\":1,\"trainCron\":\"1|DAY\",\"updateIndicatorAuto\":true,\"updateTime\":1744881022646,\"userId\":\"1\"}",
            AnalysisTask.class);
        Mockito.when(taskManager.getAnalysisTaskById(Mockito.anyInt())).thenReturn(task);
        String jsonTask = "{\n" + "  \"taskType\": \"AnomalyDetection\", \"situationName\":\"123\", \n"
            + "  \"taskSplit\": \"bySolutionId\",              \n" + "  \"taskList\": [\n" + "    {\n"
            + "      \"taskName\": \"指标异常检测3\",       \n" + "      \"indicatorList\": [{\"moType\": \"OMS\",\n"
            + "              \"measUnitKey\": \"I2K_FMMGR\",\n" + "              \"measTypeKey\": \"FMAlarmDelay\"}\n" + "        \n" + "      ],\n"
            + "      \"taskDetail\": \"用户输入定位信息\",   \n" + "      \"predictCron\": \"10|MIN\",\n"+"\"taskImportConfig\":{\n"
            + "\t\t\t\t\"configUpdateMode\":\"2\"\n" + "\t\t\t},"
            + "\t  \"algorithmModel\":{ \n" + "\t\t\"modelName\":\"DVAnomalyDetection\", \n"
            + "\t\t\"version\":\"default\", \n" + "\t\t\"parameter\":[{\n"
            + "\t\t\t\"parameterName\":\"manual_switch\", \n" + "\t\t\t\"parameterDefaultValue\":\"yes\" \n"
            + "\t\t},{\n" + "\t\t\t\"parameterName\":\"manual_lowervalue\",\n"
            + "\t\t\t\"parameterDefaultValue\":\"10000\"\n" + "\t\t},{\n"
            + "\t\t\"parameterName\":\"union_node_config\",\n" + "\t\t\t\"parameterDefaultValue\":\"avg\"\n"
            + "\t\t}]\n" + "\t }\t  \n" + "    }\n" + "  ]\n" + "}";
        String jsonTask2 = "{\n" + "  \"taskType\": \"AnomalyDetection\", \"situationName\":\"123\", \n"
            + "  \"taskSplit\": \"ddd\",              \n" + "  \"taskList\": [\n" + "    {\n"
            + "      \"taskName\": \"指标异常检测4\",       \n" + "      \"indicatorList\": [{\"moType\": \"OMS\",\n"
            + "              \"measUnitKey\": \"I2K_FMMGR\",\n" + "              \"measTypeKey\": \"FMAlarmDelay\"}\n" + "        \n" + "      ],\n"
            + "      \"taskDetail\": \"用户输入定位信息\",   \n" + "      \"predictCron\": \"10|MIN\",\n"+"\"indicatorPredictNum\":30,\"indicatorTrainNum\":10080,"+"\"taskImportConfig\":{\n"
            + "\t\t\t\t\"configUpdateMode\":\"2\"\n" + "\t\t\t},"
            + "\t  \"algorithmModel\":{ \n" + "\t\t\"modelName\":\"DVAnomalyDetection\", \n"
            + "\t\t\"version\":\"default\", \n" + "\t\t\"parameter\":[{\n"
            + "\t\t\t\"parameterName\":\"manual_switch\", \n" + "\t\t\t\"parameterDefaultValue\":\"yes\" \n"
            + "\t\t},{\n" + "\t\t\t\"parameterName\":\"manual_lowervalue\",\n"
            + "\t\t\t\"parameterDefaultValue\":\"10000\"\n" + "\t\t},{\n"
            + "\t\t\"parameterName\":\"concept_drift_threshold\",\n" + "\t\t\t\"parameterDefaultValue\":\"0.002\"\n"
            + "\t\t}]\n" + "\t }\t  \n" + "    }\n" + "  ]\n" + "}";
        Exception exp = null;
        try {
            String algorithmStr
                = "[{\"description\":\"手动设置阈值的开关，默认为no。设置为no时，表示关闭；设置为yes时，表示打开；设置为upper时，表示只设置上阈值；设置为lower时，表示只设置下阈值，且需要配置手动设置的上阈值、手动设置的下阈值。\",\"displayName\":\"手动设置阈值开关\",\"parameterDefaultValue\":\"no\",\"parameterName\":\"manual_switch\",\"required\":true,\"type\":\"string\",\"valueRange\":{\"value\":[\"yes\",\"no\",\"upper\",\"lower\"],\"type\":\"array\"}},{\"description\":\"取值范围为0.0-10000000000，小数点后最多支持3位数字。当手动设置的上阈值小于推理计算出的上阈值时，该参数生效。\",\"displayName\":\"手动设置的上阈值\",\"parameterDefaultValue\":10000.0,\"parameterName\":\"manual_uppervalue\",\"required\":true,\"type\":\"float\",\"valueRange\":{\"value\":{\"min\":0.0,\"max\":10000000000.0},\"type\":\"range\"}},{\"description\":\"取值范围为0.0-10000000000，小数点后最多支持3位数字。当手动设置的下阈值大于推理计算出的下阈值时，该参数生效。\",\"displayName\":\"手动设置的下阈值\",\"parameterDefaultValue\":0.0,\"parameterName\":\"manual_lowervalue\",\"required\":true,\"type\":\"float\",\"valueRange\":{\"value\":{\"min\":0.0,\"max\":10000000000.0},\"type\":\"range\"}},{\"description\":\"推荐训练需要的数据点数。\",\"displayName\":\"训练数据点个数\",\"parameterDefaultValue\":10080,\"parameterName\":\"train_point_need\",\"required\":true,\"type\":\"int\",\"valueRange\":{\"value\":{\"min\":1440,\"max\":40320},\"type\":\"range\"}},{\"description\":\"推荐检测需要的数据点数。\",\"displayName\":\"推理数据点个数\",\"parameterDefaultValue\":30,\"parameterName\":\"detect_point_need\",\"required\":true,\"type\":\"int\",\"valueRange\":{\"value\":{\"min\":20,\"max\":50},\"type\":\"range\"}},{\"description\":\"xxx。\",\"displayName\":\"xxx\",\"parameterDefaultValue\":0.01,\"parameterName\":\"concept_drift_threshold\",\"required\":true,\"type\":\"float\",\"valueRange\":{\"value\":{\"min\":0,\"max\":1},\"type\":\"range\"}}]";
            List<ModelParameter> parameters = JSON.parseArray(algorithmStr, ModelParameter.class);
            Mockito.when(algorithmManageService.defaultParam(Mockito.anyString(), Mockito.anyString(), Mockito.anyString())).thenReturn(parameters);
            Mockito.when(algorithmManageService.defaultParam(Mockito.anyString(), Mockito.anyString())).thenReturn(parameters);
            List<AlgorithmModel> algorithmList = new ArrayList<>();
            AlgorithmModel model = new AlgorithmModel();
            model.setId(5);
            algorithmList.add(model);
            AlgorithmResult algorithmResult = new AlgorithmResult();
            algorithmResult.setAlgorithmList(algorithmList);
            Mockito.when(algorithmManageService.getAlgorithm(Mockito.any())).thenReturn(algorithmResult);
            taskImporter.importTask(jsonTask, null, "1", null, null);
            taskImporter.importTask(jsonTask2, null, "1", null, null);
        } catch (Exception e) {
            exp = e;
        }
        Assert.assertNull(exp);
    }

    @Test
    public void isPathValidError() throws Exception {
        // 抛异常用例
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getnotrggatorbytype.json");
        ImportTaskEntity importTaskEntity = new ImportTaskEntity();
        importTaskEntity.setTaskName("test");
        importTaskEntity.setTaskType("CorrelationAnalysis");
        String nodesJson = "[{\n" + "\t\t\t\"nodeName\": \"节点4\",\n" + "\t\t\t\"nodeType\": \"alarm\",  \n"
            + "\t\t\t\"extendJson\":[ \n"
            + "\t\t\t{\"field\":\"alarmId\",\"operator\":\"in\",\"values\":[\"101216\"]}\n" + "\t\t\t],\n"
            + "\t\t\t\"algorithmModelName\": \"DVIndicatorAlarmAnalysis\", \n" + "\t\t\t\"kpiTimeRange\": \"5|DAY\",\n"
            + "\t\t\t\"kpiTimeType\": \"recently\"\n" + "\t\t},{\n" + "\t\t\t\"nodeName\": \"节点5\",\n"
            + "\t\t\t\"nodeType\": \"log\",  \n" + "\t\t\t\"extendJson\":{\n" + "\t\t\t\t\"queryFields\":{ \n"
            + "\t\t\t\t\t\"Message\":\"agentIp\"\n" + "\t\t\t\t},\n"
            + "\t\t\t\t\"solutionType\":\"mpaas.momgr.MPAAS\", \n" + "\t\t\t\t\"logType\":\"dsflogs2\", \n"
            + "\t\t\t\t\"analysisField\":\"Message\" \n" + "\t\t\t},\n"
            + "\t\t\t\"algorithmModelName\": \"DVIndicatorLogAnalysis\",\n" + "\t\t\t\"kpiTimeRange\": \"5|DAY\",\n"
            + "\t\t\t\"kpiTimeType\": \"recently\"\n" + "\t\t}]";
        List<TemplateNodeInfo> nodes = JSON.parseArray(nodesJson, TemplateNodeInfo.class);
        importTaskEntity.setNodes(nodes);
        List<TemplateTreePath> paths = new ArrayList<>();
        TemplateTreePath path1 = new TemplateTreePath();
        path1.setSource("节点4");
        path1.setTarget("节点5");
        path1.setCondition(">=100.0");
        TemplateTreePath path2 = new TemplateTreePath();
        path2.setSource("节点4");
        path2.setTarget("节点5");
        path2.setCondition(">=100.0");
        paths.add(path1);
        paths.add(path2);
        importTaskEntity.setPaths(paths);
        ServiceException exp = null;
        try {
            importTaskValidCheker.check(importTaskEntity);
        } catch (ServiceException e) {
            exp = e;
        }
        Assert.assertNotNull(exp);
    }

    @Test
    public void checkAlarmExtendJsonEmptyTest() throws Exception {
        // 抛异常用例
        mockServer.loadScenarioFiles("module.pm/rest_mappings-getnotrggatorbytype.json");
        ImportTaskEntity importTaskEntity = new ImportTaskEntity();
        importTaskEntity.setTaskName("test");
        importTaskEntity.setTaskType("CorrelationAnalysis");
        String nodesJson = "[{\n" + "\t\t\t\"nodeName\": \"节点4\",\n" + "\t\t\t\"nodeType\": \"alarm\",  \n"
            + "\t\t\t\"extendJson\":\"\",\n"
            + "\t\t\t\"algorithmModelName\": \"DVIndicatorAlarmAnalysis\", \n" + "\t\t\t\"kpiTimeRange\": \"5|DAY\",\n"
            + "\t\t\t\"kpiTimeType\": \"recently\"\n" + "\t\t},{\n" + "\t\t\t\"nodeName\": \"节点5\",\n"
            + "\t\t\t\"nodeType\": \"log\",  \n" + "\t\t\t\"extendJson\":{\n" + "\t\t\t\t\"queryFields\":{ \n"
            + "\t\t\t\t\t\"Message\":\"agentIp\"\n" + "\t\t\t\t},\n"
            + "\t\t\t\t\"solutionType\":\"mpaas.momgr.MPAAS\", \n" + "\t\t\t\t\"logType\":\"dsflogs2\", \n"
            + "\t\t\t\t\"analysisField\":\"Message\" \n" + "\t\t\t},\n"
            + "\t\t\t\"algorithmModelName\": \"DVIndicatorLogAnalysis\",\n" + "\t\t\t\"kpiTimeRange\": \"5|DAY\",\n"
            + "\t\t\t\"kpiTimeType\": \"recently\"\n" + "\t\t}]";
        List<TemplateNodeInfo> nodes = JSON.parseArray(nodesJson, TemplateNodeInfo.class);
        importTaskEntity.setNodes(nodes);
        importTaskEntity.setPaths(new ArrayList<>());
        ServiceException exp = null;
        try {
            importTaskValidCheker.check(importTaskEntity);
        } catch (ServiceException e) {
            exp = e;
        }
        Assert.assertNull(exp);
    }

    @Test
    public void validHomogeneousAlgorithmParameterTest() throws Exception {
        AlgorithmParameterModel algorithmModel = new AlgorithmParameterModel();
        algorithmModel.setModelName("DVHomogeneousComparison");
        Map<String, ModelParameter> model = new HashMap<>();
        ModelParameter radiusType = new ModelParameter();
        radiusType.setParameterDefaultValue("0");
        model.put("radius_type", radiusType);
        ModelParameter eps = new ModelParameter();
        eps.setParameterDefaultValue("0.8");
        model.put("eps", eps);

        Method method = taskImporterClass.getDeclaredMethod("validHomogeneousAlgorithmParameter", AlgorithmParameterModel.class, Map.class);
        method.setAccessible(true);

        Exception ex = null;
        try{
            method.invoke(taskImporter, algorithmModel, model);
        } catch (Exception e) {
            ex = e;
        }
        Assert.assertNull(ex);
    }

    @Test
    public void checkAuxIndicatorSizeTest() throws ServiceException {
        TriggerIndicator mainIndicator = new TriggerIndicator();
        ArrayList<TriggerIndicator> auxIndicators = new ArrayList<>();
        auxIndicators.add(mainIndicator);
        auxIndicators.add(mainIndicator);
        auxIndicators.add(mainIndicator);
        auxIndicators.add(mainIndicator);
        auxIndicators.add(mainIndicator);
        TaskImporter.checkAuxIndicatorSize(mainIndicator, auxIndicators);
        auxIndicators.add(mainIndicator);
        Exception exception = null;
        try {
            TaskImporter.checkAuxIndicatorSize(mainIndicator, auxIndicators);
        } catch (ServiceException e) {
            exception = e;
        }
        Assert.assertNotNull(exception);
    }
}
