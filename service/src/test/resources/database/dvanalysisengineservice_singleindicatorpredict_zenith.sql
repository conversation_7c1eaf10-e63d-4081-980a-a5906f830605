/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

DROP TABLE IF EXISTS TBL_AIOPS_ALGORITHM;
create table TBL_AIOPS_ALGORITHM (
    ID             INTEGER AUTO_INCREMENT not null
        constraint TBL_AIOPS_ALGORITHM
        primary key,
    ALGORITHM_NAME VARCHAR(256) not null,
    ALGORITHM_TYPE INTEGER not null,
    UPDATE_TIME    BIGINT not null,
    VERSION        VARCHAR(256) not null,
    STATUS         INTEGER,
    CALL_NUMBER    BIGINT,
    FAILED_NUMBER  BIGINT,
    USERID         VARCHAR(256),
    TENANT         VARCHAR(256),
    FILE_BYTES     CLOB         not null
);

DROP TABLE IF EXISTS TBL_AIOPS_ANALYSIS_TASK;
CREATE TABLE TBL_AIOPS_ANALYSIS_TASK (
                                         TASK_ID INTEGER AUTO_INCREMENT NOT NULL,
                                         TASK_DESC VARCHAR(2048),
                                         TASK_NAME VARCHAR(256) NOT NULL,
                                         SITUATION_ID BIGINT,
                                         TASK_TYPE INTEGER NOT NULL,
                                         PERIODIC_TYPE INTEGER,
                                         PREDICT_CRON VARCHAR(256),
                                         TRAIN_CRON VARCHAR(256),
                                         SOLUTION_ID VARCHAR(256),
                                         SOLUTION_NAME VARCHAR(256),
                                         TRAIN_STATUS INTEGER,
                                         START_STATUS INTEGER,
                                         REPORT_ALARM INTEGER,
                                         ALARM_TYPE INTEGER,
                                         BEGIN_TIME BIGINT,
                                         END_TIME BIGINT,
                                         INDICATOR_SELECT_TYPE INTEGER,
                                         UPDATE_TIME BIGINT NOT NULL,
                                         ALGORITHM_MODEL_ID VARCHAR(256),
                                         ALGORITHM_MODEL_NAME VARCHAR(256),
                                         ALGORITHM_PARAM CLOB,
                                         DATASOURCE_ID VARCHAR(256),
                                         PATH VARCHAR(256),
                                         TASK_DETAIL VARCHAR(1024),
                                         ADDITION VARCHAR(256),
                                         USER_ID VARCHAR(256) NOT NULL,
                                         LAST_MODIFY_USER_ID VARCHAR(256),
                                         LAST_EXECUTOR_USER_ID VARCHAR(256),
                                         ALARM_TYPE_LIST CLOB,
                                         ALARM_SELECT_TYPE INTEGER,
                                         ALARM_SELECT_LIST CLOB,
                                         ALARM_SELECT_GROUP_LIST CLOB,
                                         INDICATOR_PREDICT_NUM BIGINT,
                                         INDICATOR_TRAIN_NUM BIGINT,
                                         ALARM_PREDICT_TIME VARCHAR(50),
                                         ALARM_TRAIN_TIME VARCHAR(50),
                                         LOG_PREDICT_TIME VARCHAR(50),
                                         LOG_TRAIN_TIME VARCHAR(50),
                                         REPORT_ALARM_NAME VARCHAR(256),
                                         REPORT_ALARM_ID VARCHAR(256),
                                         IMPORT_TASK_STATUS BOOLEAN,
                                         GROUP_LEVEL INTEGER,
                                         TRAIN_ADDRESS VARCHAR(256),
                                         PREDICT_ADDRESS VARCHAR(256),
                                         INDICATOR_PREDICT_SCENARIO INTEGER,
                                         SUB_SITE VARCHAR(255),
                                         AGGREGATE_MACHINE_GROUP_LIST VARCHAR(256),
                                         CONSTRAINT TBL_AIOPS_ANALYSIS_TASK PRIMARY KEY (TASK_ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL;
create table TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL (
    ID              INTEGER not null
        constraint TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL
            primary key,
    MODEL_ID        INTEGER not null,
    MODEL_NAME      VARCHAR(256) not null,
    FEATURE_TYPE VARCHAR(256),
    FEATURE_TYPE_NAME VARCHAR(1024),
    ALGORITHM_NAME  VARCHAR(256) not null,
    ALGORITHM_PARAM CLOB         not null,
    USER_ID         VARCHAR(256) not null
);

DROP TABLE IF EXISTS TBL_AIOPS_RESOURCE_SCHEDULE_TASK;
CREATE TABLE TBL_AIOPS_RESOURCE_SCHEDULE_TASK (
                                                  TASK_ID                          INT                NOT NULL,
                                                  IS_AUTO_SCHEDULE                 BOOLEAN,
                                                  SCHEDULE_STATUS_AND_DETAIL       VARCHAR,
                                                  EXECUTE_TYPE                     INT                NOT NULL,
                                                  ASSOCIATE_PRODUCT_PORTRAIT_ID    INT                NOT NULL,
                                                  ASSOCIATE_PRODUCT_PORTRAIT_NAME  VARCHAR  NOT NULL,
                                                  SCHEDULE_DEPENDENCY_INDICATE     VARCHAR,
                                                  EXPAND_DELIVERY_TIME             VARCHAR,
                                                  SCALE_IN_DELIVERY_TIME           VARCHAR,
                                                  SCHEDULE_TIMEOUT                 VARCHAR,
                                                  SCHEDULE_PERIOD                  VARCHAR,
                                                  STRATEGY_IDS                     VARCHAR,
                                                  DETECT_OUTPUT_COUNT              INT,
                                                  SITE_ID                          VARCHAR,
                                                  CONSTRAINT TBL_AIOPS_RESOURCE_SCHEDULE_TASK PRIMARY KEY (TASK_ID)
);
DROP TABLE IF EXISTS TBL_AIOPS_SCHEDULE_CONFIG;
CREATE TABLE TBL_AIOPS_SCHEDULE_CONFIG (
                                           CONFIG_ID                       INT                NOT NULL AUTO_INCREMENT,
                                           SCHEDULE_TASK_ID                INT                NOT NULL,
                                           SUB_PORTRAIT_NAME               VARCHAR  NOT NULL,
                                           INDICATOR_PREDICTION_TASK_ID    INT,
                                           INDICATOR_PREDICTION_TASK_NAME  VARCHAR,
                                           INDICATOR_UPPER                 DOUBLE,
                                           INDICATOR_LOWER                 DOUBLE,
                                           INDICATOR_DISPLAY               VARCHAR NOT NULL,
                                           INDICATOR_RETURN                VARCHAR NOT NULL,
                                           UNITED_ID                       VARCHAR,
                                           SITE_ID                         VARCHAR,
                                           HOST_TYPE                       VARCHAR,
                                           CONSTRAINT TBL_AIOPS_SCHEDULE_CONFIG PRIMARY KEY (CONFIG_ID)
);
DROP TABLE IF EXISTS TBL_AIOPS_TASK_INDICATOR;
create table TBL_AIOPS_TASK_INDICATOR
(
    INDICATOR_ID      VARCHAR(1024) not null,
    TASK_ID           INTEGER(4) not null,
    ABNORMAL          NUMBER(4,1),
    MEAS_UNIT_KEY     VARCHAR(256),
    MEAS_UNIT_NAME    VARCHAR(256),
    MEAS_TYPE_KEY     VARCHAR(256),
    DN                VARCHAR(256),
    INDEX_NAME        VARCHAR(256),
    MO_TYPE           VARCHAR(256),
    DISPLAY_VALUE     VARCHAR(8000),
    ORIGINAL_VALUE    VARCHAR(256),
    UNIT              VARCHAR(256),
    DN_NAME           VARCHAR(256),
    CHECKED_NET_ID    VARCHAR(256),
    HAS_MEAS_OBJ      VARCHAR(5),
    INDEX_ID          VARCHAR(256),
    RESOURCE_TYPE_KEY VARCHAR(256),
    PQL               VARCHAR(8000),
    SOLUTION_ID       VARCHAR(256),
    GROUP_ID          VARCHAR(256),
    RESOURCE_GROUP    VARCHAR(256),
    HISTORY_INDICATOR_ID INTEGER,
    HISTORY_INDICATOR_NAME VARCHAR(1800),
    constraint TBL_AIOPS_TASK_INDICATOR
        primary key (INDICATOR_ID, TASK_ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_IMPORT_HISTORY_INDICATOR;
CREATE TABLE TBL_AIOPS_IMPORT_HISTORY_INDICATOR
(
    ID             INTEGER AUTO_INCREMENT NOT NULL,
    USER_NAME      VARCHAR(256)  NOT NULL,
    FILE_NAME      VARCHAR(256)  NOT NULL,
    INDICATOR_NAME VARCHAR(1800) NOT NULL,
    INFO           VARCHAR(512),
    START_TIME     BIGINT        NOT NULL,
    END_TIME       BIGINT        NOT NULL,
    DATA_NUM       INTEGER       NOT NULL,
    DATA_INTERVAL  BIGINT        NOT NULL,
    MAX_VALUE      DOUBLE,
    MIN_VALUE      DOUBLE,
    CREATE_TIME    BIGINT        NOT NULL,
    CONSTRAINT TBL_AIOPS_IMPORT_HISTORY_INDICATOR PRIMARY KEY (ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9;
CREATE TABLE TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9
(
    COLLECT_TIME BIGINT NOT NULL,
    KPI_VALUE    DOUBLE NOT NULL,
    CONSTRAINT TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 primary key (COLLECT_TIME)
);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83;
CREATE TABLE TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83
(
    INDICATOR_ID VARCHAR(256) NOT NULL,
    COLLECT_TIME BIGINT       NOT NULL,
    KPI_VALUE    DOUBLE       NOT NULL,
    constraint TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83
        primary key (INDICATOR_ID, COLLECT_TIME)
);

DROP TABLE IF EXISTS MY_TABLES;
CREATE TABLE MY_TABLES
(
    TABLE_NAME VARCHAR(256) NOT NULL,
    constraint MY_TABLES
        primary key (TABLE_NAME)
);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_SINGLE_INDICATOR_PREDICT_RESULT_83;
CREATE TABLE TBL_AIOPS_TASK_SINGLE_INDICATOR_PREDICT_RESULT_83
(
    INDICATOR_ID VARCHAR(1024),
    COLLECT_TIME BIGINT,
    INDICATOR_VALUE INTEGER
);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_PREDICT_RESULT_83 (INDICATOR_ID, COLLECT_TIME, INDICATOR_VALUE) VALUES ('OS=1I2K_OSIOWait*************', 1, 1);

DROP TABLE IF EXISTS TBL_AIOPS_INDICATOR_OUTLIER;
CREATE TABLE TBL_AIOPS_INDICATOR_OUTLIER (
     ID VARCHAR(255) NOT NULL ,
     TASK_ID INTEGER NOT NULL ,
     INDICATOR_ID VARCHAR(1024) NOT NULL ,
     START_TIME BIGINT NOT NULL ,
     END_TIME BIGINT,
     DURATION BIGINT,
     CORRECT NUMBER(1) NOT NULL ,
     PROBABLE_CAUSE VARCHAR(1024) ,
     SWITCH_ALARM_FIELD VARCHAR(256) ,
     CONSTRAINT TBL_AIOPS_INDICATOR_OUTLIER PRIMARY KEY (ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_UNITED_INDICATOR;
CREATE TABLE TBL_AIOPS_TASK_UNITED_INDICATOR
(
    UNITED_ID      VARCHAR(256)   NOT NULL,
    TASK_ID        INTEGER NOT NULL,
    CLUSTER_NAME   VARCHAR(256),
    SITE_ID        VARCHAR(256),
    MO_TYPE        VARCHAR(256),
    MEAS_UNIT_KEY  VARCHAR(256),
    MEAS_TYPE_KEY  VARCHAR(256),
    MEAS_UNIT_NAME VARCHAR(256),
    INDEX_ID       VARCHAR(256),
    INDEX_NAME     VARCHAR(256),
    DISPLAY_VALUE  VARCHAR(256),
    ORIGINAL_VALUE VARCHAR(256),
    UNIT           VARCHAR(256),
    HAS_MEAS_OBJ   VARCHAR(256),
    CHECKED_NET_ID VARCHAR(256),
    PRIMARY KEY (UNITED_ID, TASK_ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR;
CREATE TABLE TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR(
                                                           INDICATOR_ID VARCHAR(1024) NOT NULL,
                                                           BELONG_INDICATOR_ID VARCHAR(1024) NOT NULL,
                                                           MEAS_UNIT_KEY VARCHAR(256),
                                                           MEAS_UNIT_NAME VARCHAR(256),
                                                           MEAS_TYPE_KEY VARCHAR(256),
                                                           DN VARCHAR(256),
                                                           DN_NAME VARCHAR(256),
                                                           MO_TYPE VARCHAR(256),
                                                           INDEX_ID VARCHAR(256),
                                                           INDEX_NAME VARCHAR(256),
                                                           DISPLAY_VALUE VARCHAR(256),
                                                           ORIGINAL_VALUE VARCHAR(256),
                                                           UNIT VARCHAR(256),
                                                           HAS_MEAS_OBJ VARCHAR(256),
                                                           CHECKED_NET_ID VARCHAR(256),
                                                           PRIMARY KEY (INDICATOR_ID,BELONG_INDICATOR_ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_CONFIG_DATA;
CREATE TABLE TBL_AIOPS_CONFIG_DATA (
    CONFIG_ID INTEGER,
    CONFIG_NAME VARCHAR(256) NOT NULL,
    DATA_TYPE VARCHAR(256) NOT NULL,
    DEFAULT_VALUE VARCHAR(256) NOT NULL,
    REAL_VALUE VARCHAR(256),
    MAX_VALUE VARCHAR(256),
    MIN_VALUE VARCHAR(256)
);

DROP TABLE IF EXISTS TBL_AIOPS_SCHEDULE_CONFIG;
CREATE TABLE TBL_AIOPS_SCHEDULE_CONFIG (
    CONFIG_ID                       INT                NOT NULL,
    SCHEDULE_TASK_ID                INT                NOT NULL,
    SUB_PORTRAIT_NAME               VARCHAR  NOT NULL,
    INDICATOR_PREDICTION_TASK_ID    INT,
    INDICATOR_PREDICTION_TASK_NAME  VARCHAR,
    INDICATOR_UPPER                 DOUBLE,
    INDICATOR_LOWER                 DOUBLE,
    INDICATOR_DISPLAY               VARCHAR NOT NULL,
    INDICATOR_RETURN                VARCHAR NOT NULL,
    UNITED_ID                       VARCHAR,
    SITE_ID                         VARCHAR,
    HOST_TYPE                       VARCHAR,
    CONSTRAINT TBL_AIOPS_SCHEDULE_CONFIG PRIMARY KEY (CONFIG_ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_CONCEPT_DRIFT_RECORD;
CREATE TABLE TBL_AIOPS_CONCEPT_DRIFT_RECORD
(
    ID           INTEGER AUTO_INCREMENT NOT NULL,
    TASK_ID      INTEGER       NOT NULL,
    INDICATOR_ID VARCHAR(1024) NOT NULL,
    START_TIME   BIGINT        NOT NULL,
    END_TIME     BIGINT,
    CORRECT      INTEGER       NOT NULL,
    RETRAIN      INTEGER       NOT NULL,
    CONSTRAINT TBL_AIOPS_CONCEPT_DRIFT_RECORD PRIMARY KEY (ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_CONCEPT_DRIFT_SCORE_1;
CREATE TABLE TBL_AIOPS_CONCEPT_DRIFT_SCORE_1
(
    INDICATOR_ID VARCHAR(1024) NOT NULL,
    COLLECT_TIME BIGINT        NOT NULL,
    SCORE        DOUBLE        NOT NULL,
    THRESHOLD    DOUBLE        NOT NULL,
    IS_DRIFT     BOOLEAN       NOT NULL,
    CONSTRAINT TBL_AIOPS_CONCEPT_DRIFT_SCORE_1 primary key (INDICATOR_ID, COLLECT_TIME)
);

DROP TABLE IF EXISTS TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL;
CREATE TABLE TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL (
    ID INTEGER AUTO_INCREMENT NOT NULL,
    TASK_ID INTEGER NOT NULL,
    INDICATOR_ID VARCHAR(1024) NOT NULL,
    START_TIME BIGINT NOT NULL,
    END_TIME BIGINT NOT NULL,
    CREATE_TIME BIGINT NOT NULL,
    CONSTRAINT TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL PRIMARY KEY (ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_EXECUTION_RESULT;
CREATE TABLE TBL_AIOPS_TASK_EXECUTION_RESULT (
    TASK_ID INTEGER NOT NULL ,
    START_TIME BIGINT NOT NULL ,
    END_TIME BIGINT,
    STATUS VARCHAR(32) NOT NULL ,
    TASK_PERIOD_TYPE VARCHAR(32) NOT NULL,
    PARTIAL_RECORDS CLOB,
    FAILURE_CAUSE CLOB,
    CONSTRAINT TBL_AIOPS_TASK_EXECUTION_RESULT PRIMARY KEY (TASK_ID,START_TIME,TASK_PERIOD_TYPE)
);

insert into TBL_AIOPS_RESOURCE_SCHEDULE_TASK (TASK_ID, IS_AUTO_SCHEDULE, SCHEDULE_STATUS_AND_DETAIL, EXECUTE_TYPE, ASSOCIATE_PRODUCT_PORTRAIT_ID, ASSOCIATE_PRODUCT_PORTRAIT_NAME, SCHEDULE_DEPENDENCY_INDICATE, EXPAND_DELIVERY_TIME, SCALE_IN_DELIVERY_TIME, SCHEDULE_TIMEOUT, SCHEDULE_PERIOD, STRATEGY_IDS, DETECT_OUTPUT_COUNT, SITE_ID)
values (1, true, null, 1, 1, 'chanpinhuaxiang', '{"displayValue":"com.huawei.IRes.vm~Linux周期内CPU统计~周期内CPU占用率平均值(%)","returnValue":"CPUInterval(_)hwAMOSCpuUsageAverage"}', null, null, 30, 60, '["stname"]', 1440, '1');


INSERT INTO TBL_AIOPS_ANALYSIS_TASK(TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, AGGREGATE_MACHINE_GROUP_LIST) VALUES (1, '3', '异常关联', 1, 4, 0, '1|MIN', '1|DAY', null, null, 3, 1, 0, 2, null, null, 0, 1635405671444, '0', 'indicator1', '[{"description":"支撑任务训练最小的数据量。参数值为整数，取值范围为10-40320。当数据量较少时无法保证检测质量，请根据实际场景需要配置。","displayName":"最小训练数据数量","parameterDefaultValue":1440,"parameterName":"min_train_num","type":"int","valueRange":{"type":"range","value":{"min":10,"max":40320}}},{"description":"预测阶段预测数据的点数。参数值为整数，取值范围为1-40320。","displayName":"预测点数","parameterDefaultValue":60,"parameterName":"predict_length","type":"int","valueRange":{"type":"range","value":{"min":1,"max":40320}}},{"description":"训练或推理时数据缺失点比例的阈值。如果训练或推理数据的缺失点比例大于该阈值，则不会进行训练或推理。取值范围为0.0-1.0，小数点后最多支持3位数字。","displayName":"缺失点比例","parameterDefaultValue":0.4,"parameterName":"missing_percent","type":"float","valueRange":{"type":"range","value":{"min":0.0,"max":1.0}}},{"description":"联合指标节点数据的汇聚方式。默认为avg，表示取节点数据的平均值进行汇聚；设置为max，表示取节点数据的最大值进行汇聚。","displayName":"节点聚合方式","parameterDefaultValue":"avg","parameterName":"union_node_config","type":"string","valueRange":{"type":"array","value":["max","avg"]}},{"description":"训练样本中的数据点个数。默认值为10080，用户可以根据实际情况进行配置，为了保证算法的准确率以及展示数据范围完整，建议该参数值大于等于默认值。取值范围为1440-40320。","displayName":"训练数据点个数","parameterDefaultValue":10080,"parameterName":"train_point_need","type":"int","valueRange":{"type":"range","value":{"min":1440,"max":40320}}},{"description":"用于选择推理数据数据点。假设推理数据点个数为x，则表示最多获取当前时间往前的x个采集周期内数据点数作为推理数据。用户可以根据实际情况进行配置。该参数值越大，准确率越高，但是速度越慢，建议取值范围为10-4032。","displayName":"推理数据点个数","parameterDefaultValue":720,"parameterName":"detect_point_need","type":"int","valueRange":{"type":"range","value":{"min":10,"max":4320}}},{"description":"指标数据的汇聚方式。取值为avg或max，分别表示按平均值或最大值汇聚。","displayName":"汇聚方式","parameterDefaultValue":"avg","parameterName":"aggregation_method","type":"string","valueRange":{"type":"array","value":["max","avg"]}},{"description":"指标数据的汇聚周期。单位为分钟，该参数值仅支持配置为1，5，10，15，20，30，60，720，1440，10080，建议配置该值大于等于指标的采集周期，否则不进行汇聚。","displayName":"汇聚周期","parameterDefaultValue":"1","parameterName":"aggregation_period","type":"string","valueRange":{"type":"array","value":["1","5","10","15","20","30","60","720","1440","10080"]}}]', '1', null, null, null, '1', '1', '1', null, null, null, 30, 10080, null, null, null, null, '[{"clusterName":"test"}]');
INSERT INTO TBL_AIOPS_ANALYSIS_TASK(TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, AGGREGATE_MACHINE_GROUP_LIST, INDICATOR_PREDICT_SCENARIO) VALUES (9, '3', '异常关联', 1, 4, 0, '1|MIN', '1|DAY', null, null, 3, 1, 0, 2, null, null, 0, 1635405671444, '0', 'indicator1', '[{"description":"窗口大小","parameterDefaultValue":5,"parameterName":"window_size","type":"int","valueRange":{"type":"range","value":{"min":1,"max":60}}}]', '1', null, null, null, '1', '1', '1', null, null, null, 30, 10080, null, null, null, null, '[{"clusterName":"test"}]', 2);
INSERT INTO TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL, TRAIN_ADDRESS, PREDICT_ADDRESS, INDICATOR_PREDICT_SCENARIO, SUB_SITE, AGGREGATE_MACHINE_GROUP_LIST) VALUES (83, null, '单机', null, 6, 0, '1|MIN', '1|DAY', null, null, 1, 3, null, null, 1702691611846, 1702694286027, 0, 1702691605369, '10', 'DVIndicatorForecast_default', '[{"description":"支撑任务训练最小的数据量。参数值为整数，取值范围为10-40320。当数据量较少时无法保证检测质量，请根据实际场景需要配置。","displayName":"最小训练数据数量","parameterDefaultValue":10,"parameterName":"min_train_num","type":"int","valueRange":{"type":"range","value":{"min":10,"max":40320}}},{"description":"预测阶段预测数据的点数。参数值为整数，取值范围为1-40320。","displayName":"预测点数","parameterDefaultValue":60,"parameterName":"predict_length","type":"int","valueRange":{"type":"range","value":{"min":1,"max":40320}}},{"description":"训练或推理时数据缺失点比例的阈值。如果训练或推理数据的缺失点比例大于该阈值，则不会进行训练或推理。取值范围为0.0-1.0，小数点后最多支持3位数字。","displayName":"缺失点比例","parameterDefaultValue":0.4,"parameterName":"missing_percent","type":"float","valueRange":{"type":"range","value":{"min":0.0,"max":1.0}}},{"description":"联合指标节点数据的汇聚方式。默认为avg，表示取节点数据的平均值进行汇聚；设置为max，表示取节点数据的最大值进行汇聚。","displayName":"节点聚合方式","parameterDefaultValue":"avg","parameterName":"union_node_config","type":"string","valueRange":{"type":"array","value":["max","avg"]}},{"description":"训练样本中的数据点个数。默认值为10080，用户可以根据实际情况进行配置，为了保证算法的准确率以及展示数据范围完整，建议该参数值大于等于默认值。取值范围为1440-40320。","displayName":"训练数据点个数","parameterDefaultValue":10080,"parameterName":"train_point_need","type":"int","valueRange":{"type":"range","value":{"min":1440,"max":40320}}},{"description":"用于选择推理数据数据点。假设推理数据点个数为x，则表示最多获取当前时间往前的x个采集周期内数据点数作为推理数据。用户可以根据实际情况进行配置。该参数值越大，准确率越高，但是速度越慢，建议取值范围为10-4032。","displayName":"推理数据点个数","parameterDefaultValue":720,"parameterName":"detect_point_need","type":"int","valueRange":{"type":"range","value":{"min":10,"max":4320}}},{"description":"指标数据的汇聚方式。取值为avg或max，分别表示按平均值或最大值汇聚。","displayName":"汇聚方式","parameterDefaultValue":"avg","parameterName":"aggregation_method","type":"string","valueRange":{"type":"array","value":["max","avg"]}},{"description":"指标数据的汇聚周期。单位为分钟，该参数值仅支持配置为1，5，10，15，20，30，60，720，1440，10080，建议配置该值大于等于指标的采集周期，否则不进行汇聚。","displayName":"汇聚周期","parameterDefaultValue":"5","parameterName":"aggregation_period","type":"string","valueRange":{"type":"array","value":["1","5","10","15","20","30","60","720","1440","10080"]}}]', '1', null, null, null, '1', '1', '1', null, null, null, null, 10, 10080, null, null, null, null, null, null, null, null, 'dvanalysisengineservice-3-0 ', null, 0, null, null);
INSERT INTO TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL, TRAIN_ADDRESS, PREDICT_ADDRESS, INDICATOR_PREDICT_SCENARIO, SUB_SITE, AGGREGATE_MACHINE_GROUP_LIST) VALUES (84, null, '单机', null, 6, 0, '1|MIN', '1|DAY', null, null, 1, 3, null, null, 1702691611846, 1702694286027, 0, 1702691605369, '10', 'DVIndicatorForecast_default', '[{"description":"支撑任务训练最小的数据量。参数值为整数，取值范围为10-40320。当数据量较少时无法保证检测质量，请根据实际场景需要配置。","displayName":"最小训练数据数量","parameterDefaultValue":10,"parameterName":"min_train_num","type":"int","valueRange":{"type":"range","value":{"min":10,"max":40320}}},{"description":"预测阶段预测数据的点数。参数值为整数，取值范围为1-40320。","displayName":"预测点数","parameterDefaultValue":60,"parameterName":"predict_length","type":"int","valueRange":{"type":"range","value":{"min":1,"max":40320}}},{"description":"训练或推理时数据缺失点比例的阈值。如果训练或推理数据的缺失点比例大于该阈值，则不会进行训练或推理。取值范围为0.0-1.0，小数点后最多支持3位数字。","displayName":"缺失点比例","parameterDefaultValue":0.4,"parameterName":"missing_percent","type":"float","valueRange":{"type":"range","value":{"min":0.0,"max":1.0}}},{"description":"联合指标节点数据的汇聚方式。默认为avg，表示取节点数据的平均值进行汇聚；设置为max，表示取节点数据的最大值进行汇聚。","displayName":"节点聚合方式","parameterDefaultValue":"avg","parameterName":"union_node_config","type":"string","valueRange":{"type":"array","value":["max","avg"]}},{"description":"训练样本中的数据点个数。默认值为10080，用户可以根据实际情况进行配置，为了保证算法的准确率以及展示数据范围完整，建议该参数值大于等于默认值。取值范围为1440-40320。","displayName":"训练数据点个数","parameterDefaultValue":10080,"parameterName":"train_point_need","type":"int","valueRange":{"type":"range","value":{"min":1440,"max":40320}}},{"description":"用于选择推理数据数据点。假设推理数据点个数为x，则表示最多获取当前时间往前的x个采集周期内数据点数作为推理数据。用户可以根据实际情况进行配置。该参数值越大，准确率越高，但是速度越慢，建议取值范围为10-4032。","displayName":"推理数据点个数","parameterDefaultValue":720,"parameterName":"detect_point_need","type":"int","valueRange":{"type":"range","value":{"min":10,"max":4320}}}]', '1', null, null, null, '1', '1', '1', null, null, null, null, 10, 10080, null, null, null, null, null, null, null, null, 'dvanalysisengineservice-3-0 ', null, 0, null, null);
INSERT INTO TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL, TRAIN_ADDRESS, PREDICT_ADDRESS, INDICATOR_PREDICT_SCENARIO, SUB_SITE, AGGREGATE_MACHINE_GROUP_LIST) VALUES (85, null, '联合-5', null, 6, 0, '1|MIN', '1|DAY', null, null, 1, 2, null, null, null, null, null, 1701249961199, '10', 'DVIndicatorForecast_default', '[{"description":"支撑任务训练最小的数据量。参数值为整数，取值范围为10-40320。当数据量较少时无法保证检测质量，请根据实际场景需要配置。","displayName":"最小训练数据数量","parameterDefaultValue":"10","parameterName":"min_train_num","type":"int","valueRange":{"type":"range","value":{"min":10,"max":40320}}},{"description":"预测阶段预测数据的点数。参数值为整数，取值范围为1-40320。","displayName":"预测点数","parameterDefaultValue":60,"parameterName":"predict_length","type":"int","valueRange":{"type":"range","value":{"min":1,"max":40320}}},{"description":"训练或推理时数据缺失点比例的阈值。如果训练或推理数据的缺失点比例大于该阈值，则不会进行训练或推理。取值范围为0.0-1.0，小数点后最多支持3位数字。","displayName":"缺失点比例","parameterDefaultValue":0.4,"parameterName":"missing_percent","type":"float","valueRange":{"type":"range","value":{"min":0.0,"max":1.0}}},{"description":"联合指标节点数据的汇聚方式。默认为avg，表示取节点数据的平均值进行汇聚；设置为max，表示取节点数据的最大值进行汇聚。","displayName":"节点聚合方式","parameterDefaultValue":"avg","parameterName":"union_node_config","type":"string","valueRange":{"type":"array","value":["max","avg"]}},{"description":"训练样本中的数据点个数。默认值为10080，用户可以根据实际情况进行配置，为了保证算法的准确率以及展示数据范围完整，建议该参数值大于等于默认值。取值范围为1440-40320。","displayName":"训练数据点个数","parameterDefaultValue":10080,"parameterName":"train_point_need","type":"int","valueRange":{"type":"range","value":{"min":1440,"max":40320}}},{"description":"用于选择推理数据数据点。假设推理数据点个数为x，则表示最多获取当前时间往前的x个采集周期内数据点数作为推理数据。用户可以根据实际情况进行配置。该参数值越大，准确率越高，但是速度越慢，建议取值范围为10-4032。","displayName":"推理数据点个数","parameterDefaultValue":720,"parameterName":"detect_point_need","type":"int","valueRange":{"type":"range","value":{"min":10,"max":4320}}},{"description":"指标数据的汇聚周期。单位为分钟，该参数值仅支持配置为1，5，10，15，20，30，60，720，1440，10080，建议配置该值大于等于指标的采集周期，否则不进行汇聚。","displayName":"汇聚周期","parameterDefaultValue":"5","parameterName":"aggregation_period","type":"string","valueRange":{"type":"array","value":["1","5","10","15","20","30","60","720","1440","10080"]}},{"description":"指标数据的汇聚方式。取值为avg或max，分别表示按平均值或最大值汇聚。","displayName":"汇聚方式","parameterDefaultValue":"avg","parameterName":"aggregation_method","type":"string","valueRange":{"type":"array","value":["max","avg"]}}]', '1', null, null, null, '1', '1', '1', null, null, null, null, 10, 10080, null, null, null, null, null, null, null, null, 'dvanalysisengineservice-5-0 ', null, 1, null, null);
INSERT INTO TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL, TRAIN_ADDRESS, PREDICT_ADDRESS, INDICATOR_PREDICT_SCENARIO, SUB_SITE, AGGREGATE_MACHINE_GROUP_LIST) VALUES (86, null, '潮汐调度验收任务', null, 7, 0, '1|MIN', '1|DAY', null, null, 2, 2, null, null, 1702382705465, null, null, 1702382698795, '11', 'DVResourceSchedule_default', '[{"description":"资源节省的偏好度，用于在期望的范围内调整调度的资源量。参数值为浮点数，取值范围为0.0-1.0。 越大的值表示调度越少的资源量，可以获得更大的资源节省率，但更高的资源使用率可能影响系统稳定性。","displayName":"资源节省偏好度","parameterDefaultValue":"1.0","parameterName":"savePreference","type":"float","valueRange":{"type":"range","value":{"min":0.0,"max":1.0}}},{"description":"下发扩容指令的提前时间。参数值为整数，单位为分钟，范围为0-10080。在负载增加前完成扩容能够保证扩容的节点及时具备承载能力。","displayName":"扩容指令提前下发时间","parameterDefaultValue":30,"parameterName":"scaleOutTimeAhead","type":"int","valueRange":{"type":"range","value":{"min":0,"max":10080}}},{"description":"下发缩容指令的延迟时间。参数值为整数，单位为分钟，范围为0-10080。在负载下降后适当延后缩容可以保证服务质量。","displayName":"缩容指令延迟下发时间","parameterDefaultValue":10,"parameterName":"scaleInTimeDelay","type":"int","valueRange":{"type":"range","value":{"min":0,"max":10080}}},{"description":"负载毛刺的最大持续时间，小于该值的负载突变被认为是毛刺，不做扩缩。参数值为整数，单位为分钟，范围为0-10080。","displayName":"毛刺持续时间","parameterDefaultValue":10,"parameterName":"maxPulseInterval","type":"int","valueRange":{"type":"range","value":{"min":0,"max":10080}}},{"description":"业务空闲时资源自身使用率的参考值，用于提升资源评估的准确度。参数取值单位与调度依赖指标单位保持一致， 取值范围为0.0-100.0。越大的值表示越大的自身开销，例如CPU场景下4.0表示包括操作系统等4%的CPU开销。 该参数只作为默认的参考值，如果训练过程计算出实际值，则会使用实际值。","displayName":"空闲时使用率参考值","parameterDefaultValue":"4.0","parameterName":"defaultIdleUtilization","type":"float","valueRange":{"type":"range","value":{"min":0.0,"max":100.0}}},{"description":"支撑任务训练最小的数据量。参数值为整数，取值范围为10-40320。当数据量较少时无法保证调度质量，请根据实际场景需要配置。","displayName":"最小训练数据数量","parameterDefaultValue":10,"parameterName":"minTrainPoint","type":"int","valueRange":{"type":"range","value":{"min":10,"max":40320}}},{"description":"","displayName":"训练数据点个数","parameterDefaultValue":10080,"parameterName":"train_point_need","type":"int","valueRange":{"type":"range","value":{"min":10,"max":40320}}},{"description":"","displayName":"推理数据点个数","parameterDefaultValue":120,"parameterName":"detect_point_need","type":"int","valueRange":{"type":"range","value":{"min":1,"max":10080}}},{"description":"","displayName":"推理输出数据点个数","parameterDefaultValue":1440,"parameterName":"detect_output_point","type":"int","valueRange":{"type":"range","value":{"min":1,"max":40320}}}]', '6', null, null, null, '1', '1', '1', null, null, null, null, 120, 10080, null, null, null, null, null, null, null, null, 'dvanalysisengineservice-5-0 ', null, null, null, null);
INSERT INTO TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL, TRAIN_ADDRESS, PREDICT_ADDRESS, INDICATOR_PREDICT_SCENARIO, SUB_SITE, AGGREGATE_MACHINE_GROUP_LIST) VALUES (87, null, '37', 1, 1, 0, '1|MIN', '1|DAY', null, null, 3, null, 0, 4, null, null, 0, 1701776264870, '1', 'DVAnomalyDetection_default', '[{"description":"推荐训练需要的数据点数。","displayName":"训练数据点个数","parameterDefaultValue":10080,"parameterName":"train_point_need","type":"int","valueRange":{"type":"range","value":{"min":1440,"max":40320}}},{"description":"推荐检测需要的数据点数。","displayName":"推理数据点个数","parameterDefaultValue":30,"parameterName":"detect_point_need","type":"int","valueRange":{"type":"range","value":{"min":20,"max":50}}},{"description":"阈值线与真实曲线的贴近程度，即对异常数据的敏感程度。取值范围为1.0-6.0，小数点后最多支持3位数字。敏感度值越大，阈值上下限曲线越远离真实曲线，对于异常数据越不敏感；敏感度值越小，阈值上下限曲线越贴近真实曲线，对异常数据越敏感。","displayName":"敏感度","parameterDefaultValue":"4.0","parameterName":"sensitivity","type":"float","valueRange":{"type":"range","value":{"min":1.0,"max":6.0}}},{"description":"被检测数据的数据类型。其中：1表示不确定的数据类型，2表示平稳型，3表示周期型，4表示非平稳型，5表示稀疏型。","displayName":"数据类型","parameterDefaultValue":1,"parameterName":"data_type","type":"int","valueRange":{"type":"range","value":{"min":1,"max":5}}},{"description":"检测窗口内的数据数量。参数值为整数，取值范围为1-30。","displayName":"检测窗口数据量","parameterDefaultValue":5,"parameterName":"window_size","type":"int","valueRange":{"type":"range","value":{"min":1,"max":30}}},{"description":"检测窗口的越限数据量大于该参数值时上报异常。参数值为整数，取值范围为1-30且小于等于检测窗口数据量。","displayName":"上报异常阈值","parameterDefaultValue":3,"parameterName":"detect_num","type":"int","valueRange":{"type":"range","value":{"min":1,"max":30}}},{"description":"支撑任务训练最小的数据量。参数值为整数，取值范围为10-40320。当数据量较少时无法保证检测质量，请根据实际场景需要配置。","displayName":"最小训练数据数量","parameterDefaultValue":1440,"parameterName":"min_train_num","type":"int","valueRange":{"type":"range","value":{"min":10,"max":40320}}},{"description":"手动设置阈值的开关，默认为no。设置为no时，表示关闭；设置为yes时，表示打开；设置为upper时，表示只设置上阈值；设置为lower时，表示只设置下阈值，且需要配置手动设置的上阈值、手动设置的下阈值。","displayName":"手动设置阈值开关","parameterDefaultValue":"no","parameterName":"manual_switch","type":"string","valueRange":{"type":"array","value":["no","yes","upper","lower"]}},{"description":"过滤训练数据异常点，默认为yes。设置为yes时，指标中的异常点会被过滤，过滤后的指标作为训练数据；设置为no时，所有指标点作为训练数据。","displayName":"过滤训练数据异常点","parameterDefaultValue":"yes","parameterName":"train_filter","type":"string","valueRange":{"type":"array","value":["no","yes"]}},{"description":"快速推理开关，默认为no。设置为no时，表示关闭；设置为yes时，表示打开，需要配置快速推理点数。表示在训练的阶段生成历史阈值线，提高推理速度。","displayName":"快速推理开关","parameterDefaultValue":"no","parameterName":"manual_train_detect","type":"string","valueRange":{"type":"array","value":["no","yes"]}},{"description":"在训练阶段生成阈值线的数据点数。若快速推理点数设置为n，则表示在训练阶段生成当前时刻往前n个数据点的阈值线。","displayName":"快速推理点数","parameterDefaultValue":300,"parameterName":"train_detect_point","type":"int","valueRange":{"type":"range","value":{"min":30,"max":1440}}},{"description":"取值范围为0.0-10000000000，小数点后最多支持3位数字。当手动设置的上阈值小于推理计算出的上阈值时，该参数生效。","displayName":"手动设置的上阈值","parameterDefaultValue":"10000.0","parameterName":"manual_uppervalue","type":"float","valueRange":{"type":"range","value":{"min":0.0,"max":10000000000.0}}},{"description":"取值范围为0.0-10000000000，小数点后最多支持3位数字。当手动设置的下阈值大于推理计算出的下阈值时，该参数生效。","displayName":"手动设置的下阈值","parameterDefaultValue":"0.0","parameterName":"manual_lowervalue","type":"float","valueRange":{"type":"range","value":{"min":0.0,"max":10000000000.0}}},{"description":"表示是否展示上阈值线，默认为yes。设置为yes时，指标详情中展示该任务相关指标的上阈值线；设置为no时，指标详情中不展示该任务相关指标的上阈值线。","displayName":"上阈值展示开关","parameterDefaultValue":"yes","parameterName":"upperlimit_flag","type":"string","valueRange":{"type":"array","value":["yes","no"]}},{"description":"表示是否展示下阈值线，默认为yes。设置为yes时，指标详情中展示该任务相关指标的下阈值线；设置为no时，指标详情中不展示该任务相关指标的下阈值线。","displayName":"下阈值展示开关","parameterDefaultValue":"yes","parameterName":"lowerlimit_flag","type":"string","valueRange":{"type":"array","value":["yes","no"]}}]', '100', null, null, null, '1', '1', '1', null, null, null, null, 30, 10080, null, null, null, null, '37', '505410037', null, null, null, null, null, null, null);

INSERT INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES (1,'DVIndicatorAnalysis',4,0,'V100',null,null,null,null,null,' ');
INSERT INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES (2,'DVIndicatorAnalysis',4,1,'V100',null,null,null,null,null,' ');
INSERT INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES (3,'DVIndicatorForecast',4,1,'default',null,null,null,null,null,' ');


INSERT INTO TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL(ID, MODEL_ID, MODEL_NAME, ALGORITHM_NAME, ALGORITHM_PARAM, USER_ID) VALUES (1,0,'DVIndicatorAnalysisV100','indicator1','[{"description":"窗口大小","parameterDefaultValue":5,"parameterName":"window_size","type":"int","valueRange":{"type":"range","value":{"min":1,"max":60}}}]',1);
INSERT INTO TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL(ID, MODEL_ID, MODEL_NAME, ALGORITHM_NAME, ALGORITHM_PARAM, USER_ID) VALUES (2,0,'DVIndicatorAnalysisV100','indicator2','[{"description":"窗口大小","parameterDefaultValue":5,"parameterName":"window_size","type":"int","valueRange":{"type":"range","value":{"min":1,"max":60}}}]',1);
INSERT INTO TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL(ID, MODEL_ID, MODEL_NAME, ALGORITHM_NAME, ALGORITHM_PARAM, USER_ID) VALUES (3,0,'DVIndicatorAnalysisV100','indicator3','[{"description":"窗口大小","parameterDefaultValue":5,"parameterName":"window_size","type":"int","valueRange":{"type":"range","value":{"min":1,"max":60}}}]',1);

INSERT INTO TBL_AIOPS_TASK_INDICATOR (INDICATOR_ID, TASK_ID, ABNORMAL, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, DN, INDEX_NAME, MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, PQL, RESOURCE_GROUP) VALUES ('test11', 1, 0, '1', '1', '1', 'test', 'KPI_VALUE', 'OMS', null, null, null, null, null, null, null, null, 'aa', 'test');
INSERT INTO TBL_AIOPS_TASK_INDICATOR (INDICATOR_ID, TASK_ID, ABNORMAL, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, DN, INDEX_NAME, MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, PQL, RESOURCE_GROUP) VALUES ('test99', 9, 0, '1', '1', '1', 'test', 'KPI_VALUE', 'OMS', null, null, null, null, null, null, null, null, null, 'test');
INSERT INTO TBL_AIOPS_TASK_INDICATOR (INDICATOR_ID, TASK_ID, ABNORMAL, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, DN, INDEX_NAME, MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, PQL, SOLUTION_ID, GROUP_ID, RESOURCE_GROUP, HISTORY_INDICATOR_ID, HISTORY_INDICATOR_NAME) VALUES ('OS=1I2K_OSIOWait*************', 83, null, 'I2K_OS', '操作系统监控', 'IOWait', 'OS=1', 'IO等待(%)', 'OMS', '*************', 'ip=*************', '%', 'OSS', '/(_)OS=1', 'true', 'I2K_OS~~IOWait', 'OMS', null, null, null, null, null, null);
INSERT INTO TBL_AIOPS_TASK_INDICATOR (INDICATOR_ID, TASK_ID, ABNORMAL, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, DN, INDEX_NAME, MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, PQL, SOLUTION_ID, GROUP_ID, RESOURCE_GROUP, HISTORY_INDICATOR_ID, HISTORY_INDICATOR_NAME) VALUES ('OS=1I2K_OSUsedCPURate*************', 83, null, 'I2K_OS', '操作系统监控', 'UsedCPURate', 'OS=1', 'CPU占用率(%)', 'OMS', '*************', 'ip=*************', '%', 'OSS', '/(_)OS=1', 'true', 'I2K_OS~~UsedCPURate', 'OMS', null, null, null, null, 9, 'CPU占用率%');
INSERT INTO TBL_AIOPS_TASK_INDICATOR (INDICATOR_ID, TASK_ID, ABNORMAL, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, DN, INDEX_NAME, MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, PQL, SOLUTION_ID, GROUP_ID, RESOURCE_GROUP, HISTORY_INDICATOR_ID, HISTORY_INDICATOR_NAME) VALUES ('OS=1I2K_OSUsedCPURate*************', 84, null, 'I2K_OS', '操作系统监控', 'UsedCPURate', 'OS=1', 'CPU占用率(%)', 'OMS', '*************', 'ip=*************', '%', 'OSS', '/(_)OS=1', 'true', 'I2K_OS~~UsedCPURate', 'OMS', null, null, null, null, null, null);
INSERT INTO TBL_AIOPS_TASK_UNITED_INDICATOR (UNITED_ID, TASK_ID, CLUSTER_NAME, SITE_ID, MO_TYPE, MEAS_UNIT_KEY, MEAS_TYPE_KEY, MEAS_UNIT_NAME, INDEX_ID, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, HAS_MEAS_OBJ, CHECKED_NET_ID) VALUES ('1426CPUIntervalhwAMOSCpuUsageAverageCBPAPP_Host_Group1', 85, 'CBPAPP_Host_Group', '1', 'com.huawei.IRes.vm', 'CPUInterval', 'hwAMOSCpuUsageAverage', 'Linux周期内CPU统计', 'CPUInterval~~hwAMOSCpuUsageAverage', '周期内CPU占用率平均值(%)', null, null, '%', 'false', '/(_)CBPAPP_Host_Group1');

DROP TABLE IF EXISTS TBL_AIOPS_TASK_AGGREGATE_INDICATOR_11;
CREATE TABLE TBL_AIOPS_TASK_AGGREGATE_INDICATOR_11 (
                                                       UNITED_ID VARCHAR(256) NOT NULL,
                                                       COLLECT_TIME BIGINT NOT NULL,
                                                       STATIC_TYPE INTEGER NOT NULL, -- AVG 0; SUM 1; COUNT 2
                                                       IS_PREDICT boolean, -- 历史或预测数据
                                                       STATIC_VALUE DOUBLE NOT NULL
);
INSERT INTO TBL_AIOPS_TASK_AGGREGATE_INDICATOR_11 (UNITED_ID, COLLECT_TIME, STATIC_TYPE, IS_PREDICT, STATIC_VALUE) values ('10I2K_OSUsedCPURate7.220.172.201', 1695197760131, 1, false, 1.1);

INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSUsedCPURate*************', 1702653300000, 17.413999999999998);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSUsedCPURate*************', 1702653000000, 18.036);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSUsedCPURate*************', 1702652700000, 19.078);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSUsedCPURate*************', 1702652400000, 18.662);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSUsedCPURate*************', 1702652100000, 18.84);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSUsedCPURate*************', 1702651800000, 18.65);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSUsedCPURate*************', 1702651500000, 18.684);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSUsedCPURate*************', 1702651200000, 18.854000000000003);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSUsedCPURate*************', 1702650900000, 18.366);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSUsedCPURate*************', 1702650600000, 20.080000000000002);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSIOWait*************', 1702653300000, 0.21000000000000002);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSIOWait*************', 1702653000000, 0.21000000000000002);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSIOWait*************', 1702652700000, 0.21000000000000002);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSIOWait*************', 1702652400000, 0.21000000000000002);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSIOWait*************', 1702652100000, 0.21000000000000002);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSIOWait*************', 1702651800000, 0.21000000000000002);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSIOWait*************', 1702651500000, 0.21000000000000002);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSIOWait*************', 1702651200000, 0.21000000000000002);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSIOWait*************', 1702650900000, 0.21000000000000002);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_AGGREGATE_RESULT_83 (INDICATOR_ID, COLLECT_TIME, KPI_VALUE) VALUES ('OS=1I2K_OSIOWait*************', 1702650600000, 0.21000000000000002);

INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1640966400000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1643644800000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1646064000000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1648742400000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1651334400000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1654012800000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1656604800000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1659283200000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1661961600000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1664553600000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1667232000000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1669824000000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1672502400000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1675180800000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1677600000000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1680278400000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1682870400000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1685548800000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1688140800000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1690819200000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1693497600000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1696089600000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1698768000000, 55);
INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR_VALUE_9 (COLLECT_TIME, KPI_VALUE) VALUES (1701360000000, 55);

INSERT INTO TBL_AIOPS_IMPORT_HISTORY_INDICATOR (ID, USER_NAME, FILE_NAME, INDICATOR_NAME, INFO, START_TIME, END_TIME, DATA_NUM, DATA_INTERVAL, MAX_VALUE, MIN_VALUE, CREATE_TIME) VALUES (9, 'admin', '2.csv', 'CPU占用率%', '导入cpu占用率数据dsd', 1640966400000, 1701360000000, 24, 2419200000, 55, 55, 1701913081948);

INSERT INTO TBL_AIOPS_INDICATOR_OUTLIER (ID, TASK_ID, INDICATOR_ID, START_TIME, END_TIME, DURATION, CORRECT, PROBABLE_CAUSE, SWITCH_ALARM_FIELD) VALUES ('7a4be40a81f149e589906e51dcfdd841', 83, 'OS=1I2K_LOGOPLogCount', 1702657620000, 0, 0, 0, '周期型数据超出上限', null);

INSERT INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES ('CAPACITY_INDICATOR_ALL_TASK','Integer','2500','2500','2500','2500');

insert into TBL_AIOPS_SCHEDULE_CONFIG (config_id, schedule_task_id, sub_portrait_name, indicator_prediction_task_id, indicator_prediction_task_name, indicator_upper, indicator_lower, indicator_display, indicator_return, united_id, site_id, host_type)
values (1, 85, 'name1', 85, 'indicatorName', 5.0, 2.0, 'display', 'return', 'unitedId', '1', 'OnlineChgHost');

INSERT INTO TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL (TASK_ID, INDICATOR_ID, START_TIME, END_TIME, CREATE_TIME) VALUES (83, 'OS=1I2K_OSIOWait*************', 1702653300001, 1702653300002, 1710293553837);
