/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, IF_RESERVE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE,
 RESULT_RELATION_TREE, ASSOCIATED_PREDICT_TASK_ID, TWO_MACHINE_GROUP_NAME, ALARM_NORMAL_LEVEL, ALARM_SEVERITY_LEVEL, ALARM_REPORT_TIME_RANGE)
VALUES (8,0,2,1,0,null,1,'{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":"23.63","lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"40","predictionLowerThreshold":"40","predictionUpperThreshold":"40","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"50ebf9beac23491f8835a6b5cddfbf2f","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"start","uid":"50ebf9beac23491f8835a6b5cddfbf2f"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":null,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":null}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"c9429bba50db4d8bb23deeea56e00753","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"c9429bba50db4d8bb23deeea56e00753"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1688109507719,"predictType":"RUN"}',
        '{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"cd92a4205a734ea4bf33b285fa230989"},"root":true,"title":"主节点","type":"start","uid":"cd92a4205a734ea4bf33b285fa230989"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"929751cae3a342a09a402af686610fe5","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"929751cae3a342a09a402af686610fe5"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"929751cae3a342a09a402af686610fe5"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}',
        '{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":"23.63","lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"40","predictionLowerThreshold":"40","predictionUpperThreshold":"40","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"50ebf9beac23491f8835a6b5cddfbf2f","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"start","uid":"50ebf9beac23491f8835a6b5cddfbf2f"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":"1.0","measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":"2.5"},{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":"1.0","measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":"1.5"}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"c9429bba50db4d8bb23deeea56e00753","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"c9429bba50db4d8bb23deeea56e00753"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1688109507719,"predictType":"RUN"}',
        1, 'name', 1, 2, '1|YEAR');

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, IF_RESERVE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE,
 RESULT_RELATION_TREE, ASSOCIATED_PREDICT_TASK_ID, TWO_MACHINE_GROUP_NAME, ALARM_NORMAL_LEVEL, ALARM_SEVERITY_LEVEL, ALARM_REPORT_TIME_RANGE)
VALUES (9,0,2,1,0,null,1,'{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":"23.63","lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"40","predictionLowerThreshold":"40","predictionUpperThreshold":"40","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"50ebf9beac23491f8835a6b5cddfbf2f","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"start","uid":"50ebf9beac23491f8835a6b5cddfbf2f"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":null,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":null}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"c9429bba50db4d8bb23deeea56e00753","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"c9429bba50db4d8bb23deeea56e00753"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1688109507719,"predictType":"RUN"}',
        '{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"cd92a4205a734ea4bf33b285fa230989"},"root":true,"title":"主节点","type":"start","uid":"cd92a4205a734ea4bf33b285fa230989"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"929751cae3a342a09a402af686610fe5","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"929751cae3a342a09a402af686610fe5"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"929751cae3a342a09a402af686610fe5"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}',
        '{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":"23.63","lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"40","predictionLowerThreshold":"40","predictionUpperThreshold":"40","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"50ebf9beac23491f8835a6b5cddfbf2f","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"start","uid":"50ebf9beac23491f8835a6b5cddfbf2f"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":"1.0","measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":"2.5"},{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":"1.0","measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":"1.5"}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"c9429bba50db4d8bb23deeea56e00753","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"c9429bba50db4d8bb23deeea56e00753"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1688109507719,"predictType":"RUN"}',
        1, 'name', 1, 2, '1|YEAR');

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, IF_RESERVE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE,
 RESULT_RELATION_TREE, ASSOCIATED_PREDICT_TASK_ID, TWO_MACHINE_GROUP_NAME, ALARM_NORMAL_LEVEL, ALARM_SEVERITY_LEVEL, ALARM_REPORT_TIME_RANGE)
VALUES (10,0,2,1,0,null,1,'{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":"23.63","lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"40","predictionLowerThreshold":"40","predictionUpperThreshold":"40","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"50ebf9beac23491f8835a6b5cddfbf2f","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"start","uid":"50ebf9beac23491f8835a6b5cddfbf2f"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":null,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":null}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"c9429bba50db4d8bb23deeea56e00753","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"c9429bba50db4d8bb23deeea56e00753"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1688109507719,"predictType":"RUN"}',
        '{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"cd92a4205a734ea4bf33b285fa230989"},"root":true,"title":"主节点","type":"start","uid":"cd92a4205a734ea4bf33b285fa230989"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"929751cae3a342a09a402af686610fe5","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"929751cae3a342a09a402af686610fe5"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"929751cae3a342a09a402af686610fe5"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}',
        '{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":"23.63","lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"40","predictionLowerThreshold":"40","predictionUpperThreshold":"40","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"50ebf9beac23491f8835a6b5cddfbf2f","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"start","uid":"50ebf9beac23491f8835a6b5cddfbf2f"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":"1.0","measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":"2.5"},{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":"1.0","measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":"1.5"}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"c9429bba50db4d8bb23deeea56e00753","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"c9429bba50db4d8bb23deeea56e00753"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1688109507719,"predictType":"RUN"}',
        1, 'name', 1, 2, '1|YEAR');

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, IF_RESERVE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE,
 RESULT_RELATION_TREE, ASSOCIATED_PREDICT_TASK_ID, TWO_MACHINE_GROUP_NAME, ALARM_NORMAL_LEVEL, ALARM_SEVERITY_LEVEL, ALARM_REPORT_TIME_RANGE)
VALUES (11,0,2,1,0,null,1,'{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":"23.63","lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"40","predictionLowerThreshold":"40","predictionUpperThreshold":"40","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"50ebf9beac23491f8835a6b5cddfbf2f","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"HighAvailable_start","uid":"50ebf9beac23491f8835a6b5cddfbf2f"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":null,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":null}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"c9429bba50db4d8bb23deeea56e00753","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"c9429bba50db4d8bb23deeea56e00753"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1688109507719,"predictType":"RUN"}',
        '{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"cd92a4205a734ea4bf33b285fa230989"},"root":true,"title":"主节点","type":"HighAvailable_start","uid":"cd92a4205a734ea4bf33b285fa230989"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"929751cae3a342a09a402af686610fe5","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"929751cae3a342a09a402af686610fe5"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"929751cae3a342a09a402af686610fe5"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}',
        '{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":"23.63","lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"40","predictionLowerThreshold":"40","predictionUpperThreshold":"40","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"50ebf9beac23491f8835a6b5cddfbf2f","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"HighAvailable_start","uid":"50ebf9beac23491f8835a6b5cddfbf2f"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":"1.0","measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":"2.5"},{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":"1.0","measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":"1.5"}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"c9429bba50db4d8bb23deeea56e00753","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"c9429bba50db4d8bb23deeea56e00753"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1688109507719,"predictType":"RUN"}',
        1, 'name', 1, 2, '1|YEAR');

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, IF_RESERVE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE,
 RESULT_RELATION_TREE, ASSOCIATED_PREDICT_TASK_ID, TWO_MACHINE_GROUP_NAME, ALARM_NORMAL_LEVEL, ALARM_SEVERITY_LEVEL, ALARM_REPORT_TIME_RANGE)
VALUES (12,0,2,1,0,null,1,'{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":"23.63","lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"40","predictionLowerThreshold":"40","predictionUpperThreshold":"40","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"50ebf9beac23491f8835a6b5cddfbf2f","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"HighAvailable_start","uid":"50ebf9beac23491f8835a6b5cddfbf2f"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":null,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":null}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"c9429bba50db4d8bb23deeea56e00753","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"c9429bba50db4d8bb23deeea56e00753"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1688109507719,"predictType":"RUN"}',
        '{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"cd92a4205a734ea4bf33b285fa230989"},"root":true,"title":"主节点","type":"HighAvailable_start","uid":"cd92a4205a734ea4bf33b285fa230989"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"929751cae3a342a09a402af686610fe5","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"929751cae3a342a09a402af686610fe5"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"929751cae3a342a09a402af686610fe5"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}',
        '{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":"23.63","lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"40","predictionLowerThreshold":"40","predictionUpperThreshold":"40","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"50ebf9beac23491f8835a6b5cddfbf2f","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"HighAvailable_start","uid":"50ebf9beac23491f8835a6b5cddfbf2f"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":"1.0","measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":"2.5"},{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":"1.0","measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":"1.5"}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"c9429bba50db4d8bb23deeea56e00753","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"c9429bba50db4d8bb23deeea56e00753"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1688109507719,"predictType":"RUN"}',
        13, 'name', 1, 2, '1|YEAR');

insert into TBL_AIOPS_MULTIPLE_EXECUTION_RESULT (NODE_ID, NODE_NAME, EXECUTION_ID, START_TIME, TASK_ID, UPDATE_TIME,
                                                 NODE_TYPE, EAM_EXECUTION_NODE_ID, STATUS, CREATE_USER, INDICATOR_SELECT_TYPE, SORT_NUM,
                                                 IS_ROOT, TRAIN_STATUS, PREDICT_STATUS, CAN_RETRAIN, IF_NET_CHANGE, HAS_MODEL,
                                                 ERROR_INDICATOR_NUM, WARNING_INDICATOR_NUM,
                                                 NORMAL_INDICATOR_NUM, INDICATOR_NUM, FIGURE)
values ('flow-diagram_N1','主节点','cd92a4205a734ea4bf33b285fa230989',1688109210559,8,null,0,null,'success',1,null,0,null,null,null,false,false,null,0,0,0,0,
        '[{"type":"MainMetricNode","style":"left:30px;top:30px;width:360px;height:135px;","context":{"strokeStyle":"#9fdfff","lineWidth":2,"fillStyle":"#9fdfff"},"options":{}}]');

insert into TBL_AIOPS_MULTIPLE_EXECUTION_RESULT (NODE_ID, NODE_NAME, EXECUTION_ID, START_TIME, TASK_ID, UPDATE_TIME,
                                                 NODE_TYPE, EAM_EXECUTION_NODE_ID, STATUS, CREATE_USER, INDICATOR_SELECT_TYPE, SORT_NUM,
                                                 IS_ROOT, TRAIN_STATUS, PREDICT_STATUS, CAN_RETRAIN, IF_NET_CHANGE, HAS_MODEL,
                                                 ERROR_INDICATOR_NUM, WARNING_INDICATOR_NUM,
                                                 NORMAL_INDICATOR_NUM, INDICATOR_NUM, FIGURE)
values ('flow-diagram_N2','节点1','929751cae3a342a09a402af686610fe5',1688109210559,8,1688109210645,0,null,'success',1,null,1,false,null,null,false,false,null,0,0,1,1,
        '[{"type":"MetricNode","style":"left:30px;top:210px;width:260px;height:135px;","options":{"radius":5},"context":{"strokeStyle":"#00aaff","lineWidth":1,"fillStyle":"#e8f7ff"}}]');

insert into TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (TARGET_EXECUTION_UID, TARGET_NODE_ID, NODE_ID, NODE_NAME, EXECUTION_UID, INDICATOR_ID, TASK_ID, ABNORMAL, DN, MO_TYPE, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, KPI_VALUE, PREDICTION_UPPER_THRESHOLD, PREDICTION_LOWER_THRESHOLD, PREDICTION_KPI_VALUE, UPPER_THRESHOLD, LOWER_THRESHOLD, RESULT_TYPE, TRAIN_RESULT, IS_ROOT, POD_NAME)
values (null,null,'flow-diagram_N1',null,'cd92a4205a734ea4bf33b285fa230989','OS=1I2K_OSUsedCPURate************',8,null,'OS=1','OMS','I2K_OS','操作系统监控','UsedCPURate','CPU占用率(%)','************','ip=************','%','OSS1',null,true,'I2K_OS~~UsedCPURate','OMS',23.63,40,40,40,null,null,null,null,true,null);
insert into TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (TARGET_EXECUTION_UID, TARGET_NODE_ID, NODE_ID, NODE_NAME, EXECUTION_UID, INDICATOR_ID, TASK_ID, ABNORMAL, DN, MO_TYPE, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, KPI_VALUE, PREDICTION_UPPER_THRESHOLD, PREDICTION_LOWER_THRESHOLD, PREDICTION_KPI_VALUE, UPPER_THRESHOLD, LOWER_THRESHOLD, RESULT_TYPE, TRAIN_RESULT, IS_ROOT, POD_NAME)
values (null,null,'flow-diagram_N2',null,'929751cae3a342a09a402af686610fe5','OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task',8,null,'OS=1','OMS','I2K_Top_Task_Insert','性能任务数据量统计','one_min_count','每分钟数据量(条)','OMS,I2K_Task','mo_type=OMS,meas_unit_key=I2K_Task','条','OSS1',null,true,'I2K_Top_Task_Insert~~one_min_count','OMS',2.00,2.00,2.00,2.00,null,null,0,null,false,null);
insert into TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (TARGET_EXECUTION_UID, TARGET_NODE_ID, NODE_ID, NODE_NAME, EXECUTION_UID, INDICATOR_ID, TASK_ID, ABNORMAL, DN, MO_TYPE, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, KPI_VALUE, PREDICTION_UPPER_THRESHOLD, PREDICTION_LOWER_THRESHOLD, PREDICTION_KPI_VALUE, UPPER_THRESHOLD, LOWER_THRESHOLD, RESULT_TYPE, TRAIN_RESULT, IS_ROOT, POD_NAME)
values (null,null,'flow-diagram_N2',null,'929751cae3a342a09a402af686610fe5','OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG',8,null,'OS=1','OMS','I2K_Top_Task_Insert','性能任务数据量统计','one_min_count','每分钟数据量(条)','OMS,I2K_LOG','mo_type=OMS,meas_unit_key=I2K_LOG','条','OSS1',null,true,'I2K_Top_Task_Insert~~one_min_count','OMS',null,null,null,null,null,null,null,null,false,null);

INSERT INTO TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL) VALUES (1, '1', '容量分析1', 1, 5, 0, '1|MIN', '1|DAY', null, null, 3, 1, 0, 2, null, null, 0, 1635405671444, 'DVCapacityPrediction_default', 'DVAnomalyDetectionV100', '[{"parameterDefaultValue":10080,"parameterName":"train_point_need","type":"int","valueRange":{"type":"range","value":{"min":1440,"max":40320}}},{"parameterDefaultValue":30,"parameterName":"detect_point_need","type":"int","valueRange":{"type":"range","value":{"min":20,"max":50}}},{"parameterDefaultValue":"4.0","parameterName":"sensitivity","type":"float","valueRange":{"type":"range","value":{"min":1.0,"max":6.0}}},{"parameterDefaultValue":1,"parameterName":"data_type","type":"int","valueRange":{"type":"range","value":{"min":1,"max":5}}},{"parameterDefaultValue":5,"parameterName":"window_size","type":"int","valueRange":{"type":"range","value":{"min":1,"max":30}}},{"parameterDefaultValue":3,"parameterName":"detect_num","type":"int","valueRange":{"type":"range","value":{"min":1,"max":30}}},{"parameterDefaultValue":"yes","parameterName":"upperlimit_flag","type":"string","valueRange":{"type":"array","value":["yes","no"]}},{"parameterDefaultValue":"yes","parameterName":"lowerlimit_flag","type":"string","valueRange":{"type":"array","value":["yes","no"]}}]', '1', null, null, null, '1', '1', '1', '1', null, null, null, null, 10080, null, null, null, null, '1', '1', false, 1);

INSERT INTO TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL) VALUES (8, '1', '容量分析2', 1, 5, 0, '1|MIN', '1|DAY', null, null, 3, 1, 1, 2, null, null, 0, 1635405671444, 'DVCapacityPrediction_default', 'DVAnomalyDetectionV100', '[{"parameterDefaultValue":10080,"parameterName":"train_point_need","type":"int","valueRange":{"type":"range","value":{"min":1440,"max":40320}}},{"parameterDefaultValue":30,"parameterName":"detect_point_need","type":"int","valueRange":{"type":"range","value":{"min":20,"max":50}}},{"parameterDefaultValue":"4.0","parameterName":"sensitivity","type":"float","valueRange":{"type":"range","value":{"min":1.0,"max":6.0}}},{"parameterDefaultValue":1,"parameterName":"data_type","type":"int","valueRange":{"type":"range","value":{"min":1,"max":5}}},{"parameterDefaultValue":5,"parameterName":"window_size","type":"int","valueRange":{"type":"range","value":{"min":1,"max":30}}},{"parameterDefaultValue":3,"parameterName":"detect_num","type":"int","valueRange":{"type":"range","value":{"min":1,"max":30}}},{"parameterDefaultValue":"yes","parameterName":"upperlimit_flag","type":"string","valueRange":{"type":"array","value":["yes","no"]}},{"parameterDefaultValue":"yes","parameterName":"lowerlimit_flag","type":"string","valueRange":{"type":"array","value":["yes","no"]}}]', '1', null, null, null, '1', '1', '1', '1', null, null, null, null, 10080, null, null, null, null, '', '1', false, 1);

INSERT INTO TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL) VALUES (9, '1', '容量分析2', 1, 5, 0, '1|MIN', '1|DAY', null, null, 3, 1, 1, 2, null, null, 0, 1635405671444, 'DVCapacityPrediction_default', 'DVAnomalyDetectionV100', '[{"parameterDefaultValue":10080,"parameterName":"train_point_need","type":"int","valueRange":{"type":"range","value":{"min":1440,"max":40320}}},{"parameterDefaultValue":30,"parameterName":"detect_point_need","type":"int","valueRange":{"type":"range","value":{"min":20,"max":50}}},{"parameterDefaultValue":"4.0","parameterName":"sensitivity","type":"float","valueRange":{"type":"range","value":{"min":1.0,"max":6.0}}},{"parameterDefaultValue":1,"parameterName":"data_type","type":"int","valueRange":{"type":"range","value":{"min":1,"max":5}}},{"parameterDefaultValue":5,"parameterName":"window_size","type":"int","valueRange":{"type":"range","value":{"min":1,"max":30}}},{"parameterDefaultValue":3,"parameterName":"detect_num","type":"int","valueRange":{"type":"range","value":{"min":1,"max":30}}},{"parameterDefaultValue":"yes","parameterName":"upperlimit_flag","type":"string","valueRange":{"type":"array","value":["yes","no"]}},{"parameterDefaultValue":"yes","parameterName":"lowerlimit_flag","type":"string","valueRange":{"type":"array","value":["yes","no"]}}]', '1', null, null, null, '1', '1', '1', '1', null, null, null, null, 10080, null, null, null, null, '', '1', false, 1);

INSERT INTO TBL_AIOPS_MULTIPLE_DEPENDENCE_INDICATOR_MAP (NODE_ID, EXECUTION_ID, TASK_ID, DEPENDENCE_INDICATOR_ID) VALUES ('flow-diagram_N2', '3c0ca9bcd0264ede939c710adeaf7c4d', 8, 'OS=1I2K_OSUsedCPURate10.137.31.120');

INSERT INTO TBL_AIOPS_MULTIPLE_EAM_ROOT_NODE (TASK_ID, NODE_ID, EAM_EXECUTION_ID, EAM_ROOT_NODE_ID, DN, MO, MO_TYPE, CHILDREN_NUM, PARENT_NODE_ID, IF_IGNORE, IS_ROOT, UPDATE_TIME) VALUES (8, 'flow-diagram_N2', 'flow-diagram_N2', 'flow-diagram_N2', null, null, null, null, null, null, null, null);

INSERT INTO TBL_AIOPS_TASK_INDICATOR (INDICATOR_ID, TASK_ID, ABNORMAL, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, DN, INDEX_NAME, MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, PQL) VALUES ('OS=1I2K_CHANNELhwEventPutAttemptCountlogmatrixStreaming-************-dsflogs2-1-channel11', 8, null, 'I2K_CHANNEL', '流式Channel统计监控', 'hwEventPutAttemptCount', 'OS=1', '尝试放入Channel的事件数(个)', 'OMS', 'logmatrixStreaming-************-dsflogs2-1-channel11', 'channelObj=logmatrixStreaming-************-dsflogs2-1-channel11', '个', 'OSS', '/(_)OS=1', 'true', 'I2K_CHANNEL~~hwEventPutAttemptCount', 'OMS', null);

INSERT INTO TBL_AIOPS_CAPACITY_HISTORY_RESULT (ID, TASK_ID, PREDICT_TYPE, UPDATE_TIME, RELATION_TREE, MAIN_KPI_VALUE, CAPACITY_OCCUR_TIME) VALUES ('51043102bacc45eaba742377c4d89a25', 8, 'HISTORY', 1693452024711, '{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:300px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"TRUE","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":null,"lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"50.00","predictionLowerThreshold":"30.00","predictionUpperThreshold":"70.00","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":8,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"solutionId":null,"solutionName":null,"sortNum":null,"status":null,"taskId":8,"trainStatus":null,"uid":"622fec1ed073464e8002232933e79f0a","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"start","uid":"622fec1ed073464e8002232933e79f0a"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:300px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:300px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1(0)","eamNodeType":null,"hasMeasObj":"TRUE","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"isRoot":false,"kpiValue":null,"lowerThreshold":null,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","podName":null,"predictionKpiValue":"50.00","predictionLowerThreshold":"30.00","predictionUpperThreshold":"70.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":8,"trainResult":null,"unit":"条","upperThreshold":null}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"solutionId":null,"solutionName":null,"sortNum":null,"status":null,"taskId":8,"trainStatus":null,"uid":"eb75250c70254c8bac46d79c3e5ded8c","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"eb75250c70254c8bac46d79c3e5ded8c"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1692062705627,"predictType":"HISTORY"}', null, 1693452024711);

INSERT INTO TBL_AIOPS_CAPACITY_HISTORY_RESULT (ID, TASK_ID, PREDICT_TYPE, UPDATE_TIME, RELATION_TREE, MAIN_KPI_VALUE, CAPACITY_OCCUR_TIME) VALUES ('51043102bacc45eaba742377c4d89a26', 9, 'HISTORY', 1693452024711, '{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:300px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"TRUE","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":null,"lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"50.00","predictionLowerThreshold":"30.00","predictionUpperThreshold":"70.00","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":8,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"solutionId":null,"solutionName":null,"sortNum":null,"status":null,"taskId":8,"trainStatus":null,"uid":"622fec1ed073464e8002232933e79f0a","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"start","uid":"622fec1ed073464e8002232933e79f0a"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:300px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:300px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1(0)","eamNodeType":null,"hasMeasObj":"TRUE","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"isRoot":false,"kpiValue":null,"lowerThreshold":null,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","podName":null,"predictionKpiValue":"50.00","predictionLowerThreshold":"30.00","predictionUpperThreshold":"70.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":8,"trainResult":null,"unit":"条","upperThreshold":null}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"solutionId":null,"solutionName":null,"sortNum":null,"status":null,"taskId":8,"trainStatus":null,"uid":"eb75250c70254c8bac46d79c3e5ded8c","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"eb75250c70254c8bac46d79c3e5ded8c"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1692062705627,"predictType":"HISTORY"}', null, 1693452024711);

DROP TABLE IF EXISTS TBL_AIOPS_CAPACITY_CORRELATION_ALARM;
CREATE TABLE TBL_AIOPS_CAPACITY_CORRELATION_ALARM (
                                                      TASK_ID INTEGER,
                                                      INDICATOR_ID VARCHAR,
                                                      PUSH_ALARM_JSON VARCHAR
);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_SINGLE_INDICATOR_PREDICT_RESULT_1;
CREATE TABLE TBL_AIOPS_TASK_SINGLE_INDICATOR_PREDICT_RESULT_1
(
    INDICATOR_ID VARCHAR(1024),
    COLLECT_TIME BIGINT,
    INDICATOR_VALUE INTEGER
);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_PREDICT_RESULT_1 (INDICATOR_ID, COLLECT_TIME, INDICATOR_VALUE)
VALUES ('OS=1I2K_OSUsedCPURate************', 2, 1);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_AGGREGATE_INDICATOR_1;
CREATE TABLE TBL_AIOPS_TASK_AGGREGATE_INDICATOR_1(
                                                       UNITED_ID VARCHAR(255) NOT NULL ,
                                                       COLLECT_TIME VARCHAR(255) ,
                                                       STATIC_TYPE INTEGER ,
                                                       STATIC_VALUE VARCHAR(255),
                                                       IS_PREDICT INTEGER
);
INSERT INTO TBL_AIOPS_TASK_AGGREGATE_INDICATOR_1 (UNITED_ID, COLLECT_TIME, STATIC_TYPE, STATIC_VALUE, IS_PREDICT)
VALUES ('nameI2K_OSUsedCPURate************', 3600003, 1, 4, 1);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_SINGLE_INDICATOR_PREDICT_RESULT_13;
CREATE TABLE TBL_AIOPS_TASK_SINGLE_INDICATOR_PREDICT_RESULT_13
(
    INDICATOR_ID VARCHAR(1024),
    COLLECT_TIME BIGINT,
    INDICATOR_VALUE INTEGER
);
INSERT INTO TBL_AIOPS_TASK_SINGLE_INDICATOR_PREDICT_RESULT_13 (INDICATOR_ID, COLLECT_TIME, INDICATOR_VALUE)
VALUES ('OS=1I2K_OSUsedCPURate************', 20000000000, 1);