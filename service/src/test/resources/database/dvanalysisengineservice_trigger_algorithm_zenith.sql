DROP TABLE IF EXISTS TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL
;
CREATE TABLE TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL (
    ID INTEGER AUTO_INCREMENT NOT NULL,
    <PERSON><PERSON><PERSON>_ID INTEGER NOT NULL,
    <PERSON><PERSON><PERSON>_NAME VARCHAR(256) NOT NULL,
    FEATURE_TYPE VARCHAR(256),
    FEATURE_TYPE_NAME VARCHAR(1024),
    ALGORITHM_NAME VARCHAR(256) NOT NULL,
    ALGORITHM_PARAM CLOB NOT NULL,
    USER_ID VARCHAR(256) NOT NULL,
    CONSTRAINT TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL PRIMARY KEY (ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_TEMPLATE_TRIGGER_INDICATOR
;
CREATE TABLE TBL_AIOPS_TEMPLATE_TRIGGER_INDICATOR (
    TEMPLATE_ID INTEGER NOT NULL,
    TASK_ID INTEGER NOT NULL,
    INDICATOR_ID VARCHAR(1024) NOT NULL
);

DROP TABLE IF EXISTS TBL_AIOPS_TRIGGER_EXECUTION_RESULT;
CREATE TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT (
                                                    ID VARCHAR(255) NOT NULL,
                                                    START_TIME BIGINT NOT NULL,
                                                    TEMPLATE_ID INTEGER,
                                                    TREE_RESULT CLOB,
                                                    VIEW_RESULT CLOB,
                                                    INDICATOR_ID VARCHAR(256),
                                                    TASK_ID INTEGER NOT NULL,
                                                    UPDATE_TIME BIGINT,
                                                    STATUS VARCHAR(256),
                                                    FAILURE_CAUSE VARCHAR(512),
                                                    CREATE_USER VARCHAR(256),
                                                    CONTINUE_TEMPLATE_ID INTEGER,
                                                    ANALYSIS_TIME_CONSUMPTION VARCHAR(256),
                                                    CONTINUE_ANALYSIS_TIME_CONSUMPTION VARCHAR(256),
                                                    EXECUTION_TIME VARCHAR(256),
                                                    CONSTRAINT TBL_AIOPS_TRIGGER_EXECUTION_RESULT PRIMARY KEY (ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_CORRELATION_TEMPLATE;
CREATE TABLE TBL_AIOPS_CORRELATION_TEMPLATE (
    TASK_ID INTEGER NOT NULL,
    TRIGGER_TYPE INTEGER NOT NULL,
    RELATION_TREE CLOB,
    USER_ID VARCHAR(256) NOT NULL,
    DELAY VARCHAR(100),
    USE_TEMPLATE INTEGER NOT NULL,
    INDICATOR_IDS VARCHAR(8000),
    SEND_EMAIL BOOLEAN,
    USER_GROUP_INFO VARCHAR(1024),
    MANUAL_TRIGGER_EMAIL BOOLEAN,
    TEMPLATE_NODE_COUNT VARCHAR(1024),
    CONSTRAINT TBL_AIOPS_CORRELATION_TEMPLATE PRIMARY KEY (TASK_ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_SEND_EMAIL_RECORD;
CREATE TABLE TBL_AIOPS_SEND_EMAIL_RECORD (
                                             ID VARCHAR(255),
                                             TITLE VARCHAR(255),
                                             SEND_USER VARCHAR(255),
                                             STATUS VARCHAR(64),
                                             UPDATE_TIME LONG,
                                             SEND_CONTENT CLOB,
                                             CONSTRAINT TBL_AIOPS_SEND_EMAIL_RECORD PRIMARY KEY (ID)
);

INSERT INTO TBL_AIOPS_TEMPLATE_TRIGGER_INDICATOR(TEMPLATE_ID, TASK_ID, INDICATOR_ID) VALUES (10, 101, 'OS=1I2K_LOGSYSLogCount');
INSERT INTO TBL_AIOPS_INDICATOR_OUTLIER (ID, TASK_ID, INDICATOR_ID, START_TIME, END_TIME, DURATION, CORRECT, PROBABLE_CAUSE, SWITCH_ALARM_FIELD) VALUES ('d8026b07ae754f7baa1482a2e1982eab', 101, 'OS=1I2K_LOGSYSLogCount', 1633509120000, 0, 60000, 0, '平稳型数据超出上限', 'UUID=c3bafb5f4af5401da9cf7734b4795104');
INSERT INTO TBL_AIOPS_ANALYSIS_TASK(TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME) VALUES (1, '3', '异常关联', 1, 4, 0, '1|MIN', '1|DAY', null, null, 3, 1, 0, 2, null, null, 0, 1635405671444, '0', 'indicator1', '[{"description":"窗口大小","parameterDefaultValue":5,"parameterName":"window_size","type":"int","valueRange":{"type":"range","value":{"min":1,"max":60}}}]', '1', null, null, null, '1', '1', '1', null, null, null, 30, 10080, null, null, null, null);
INSERT INTO TBL_AIOPS_ANALYSIS_TASK(TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME) VALUES (102, '3', '异常检测', 1, 1, 0, '1|MIN', '1|DAY', null, null, 3, 1, 0, 2, null, null, 0, 1635405671444, '0', 'indicator1', '[{"description":"窗口大小","parameterDefaultValue":5,"parameterName":"window_size","type":"int","valueRange":{"type":"range","value":{"min":1,"max":60}}}]', '1', null, null, null, '1', '1', '1', null, null, null, 30, 10080, null, null, null, null);
INSERT INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES (1,'DVIndicatorAnalysis',4,0,'V100',null,null,null,null,null,' ');
INSERT INTO TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL(ID, MODEL_ID, MODEL_NAME, ALGORITHM_NAME, ALGORITHM_PARAM, USER_ID) VALUES (1,0,'DVIndicatorAnalysisV100','indicator1','[{"description":"窗口大小","parameterDefaultValue":5,"parameterName":"window_size","type":"int","valueRange":{"type":"range","value":{"min":1,"max":60}}}]',1);
INSERT INTO TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL(ID, MODEL_ID, MODEL_NAME, ALGORITHM_NAME, ALGORITHM_PARAM, USER_ID) VALUES (2,0,'DVIndicatorAnalysisV100','indicator2','[{"description":"窗口大小","parameterDefaultValue":5,"parameterName":"window_size","type":"int","valueRange":{"type":"range","value":{"min":1,"max":60}}}]',1);
INSERT INTO TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL(ID, MODEL_ID, MODEL_NAME, ALGORITHM_NAME, ALGORITHM_PARAM, USER_ID) VALUES (3,0,'DVIndicatorAnalysisV100','indicator3','[{"description":"窗口大小","parameterDefaultValue":5,"parameterName":"window_size","type":"int","valueRange":{"type":"range","value":{"min":1,"max":60}}}]',1);
INSERT INTO TBL_AIOPS_SEND_EMAIL_RECORD(ID, TITLE, SEND_USER, STATUS, UPDATE_TIME, SEND_CONTENT) values ('03e7998d576f48298aff4ff3ea85fac8','email','admin','success',1708409320619,'4 suspected root causes were found, among which the top5 nodes are as follows:');
INSERT INTO TBL_AIOPS_TRIGGER_EXECUTION_RESULT(ID, START_TIME, TEMPLATE_ID, TREE_RESULT, VIEW_RESULT, INDICATOR_ID, TASK_ID, UPDATE_TIME, STATUS, FAILURE_CAUSE, CREATE_USER) values ('03e7998d576f48298aff4ff3ea85fac8',1708397820000,15,'{"nodes":[{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"round\",\"style\":\"left:94px;top:233px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","gray":false,"id":"03e7998d576f48298aff4ff3ea85fac8","intoDegree":0,"leaf":false,"location":{"xAxis":1.0,"yAxis":1.0},"root":true,"title":"CPU占用率(%)","type":"start"},{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"roundrect\",\"style\":\"left:236px;top:389px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"fillStyle\":\"#e8f7ff\",\"lineWidth\":1}}]","gray":false,"id":"3a982aae9b27451689204342e6d43a8b","intoDegree":1,"leaf":true,"location":{"xAxis":2.0,"yAxis":4.0},"nodeParam":{"algorithmFeatureType":"association_matching","algorithmFeatureTypeName":"关联匹配(分析关联指标与主指标的线性相关性)","algorithmModelName":"DVCorrelationAnalysis_default","algorithmTemplateId":1,"algorithmTemplateName":"11(admin)","beginTime":1708394220000,"correlationDegree":100.0,"dataSourceType":"performance","datasourceId":"1","endTime":1708397820000,"executionId":"03e7998d576f48298aff4ff3ea85fac8","executionStatus":"success","indicatorDataType":"DV_PM","indicatorList":[{"auxiliaryIndicator":[],"checkedNetId":"/(_)OS=1","correlationDegree":100.0,"dataSourceId":1,"displayValue":"************","dn":"OS=1","dnName":"OSS","executionId":"03e7998d576f48298aff4ff3ea85fac8","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"3a982aae9b27451689204342e6d43a8b","originalValue":"ip=************","resourceTypeKey":"OMS","unit":"%"},{"auxiliaryIndicator":[],"checkedNetId":"/(_)OS=1","correlationDegree":27.3,"dataSourceId":1,"displayValue":"************","dn":"OS=1","dnName":"OSS","executionId":"03e7998d576f48298aff4ff3ea85fac8","hasMeasObj":"true","indexId":"I2K_OS~~UsedMemoryRate","indexName":"内存使用率(%)","indicatorId":"OS=1I2K_OSUsedMemoryRate************","measTypeKey":"UsedMemoryRate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"3a982aae9b27451689204342e6d43a8b","originalValue":"ip=************","resourceTypeKey":"OMS","unit":"%"}],"indicatorSelectType":0,"isGray":false,"kpiTimeRange":"1|HOUR","kpiTimeType":"recently","mainIndicator":{"abnormal":1,"auxiliaryIndicator":[],"checkedNetId":"/(_)OS=1","correlationDegree":0.0,"dataSourceId":1,"displayValue":"************","dn":"OS=1","dnName":"OSS","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"03e7998d576f48298aff4ff3ea85fac8","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":13,"unit":"%"},"mainIndicatorDataType":"DV_PM","mainIndicatorJson":"{\"abnormal\":1,\"auxiliaryIndicator\":[],\"checkedNetId\":\"/(_)OS=1\",\"correlationDegree\":0.0,\"dataSourceId\":1,\"displayValue\":\"************\",\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"hasMeasObj\":\"true\",\"indexId\":\"I2K_OS~~UsedCPURate\",\"indexName\":\"CPU占用率(%)\",\"indicatorId\":\"OS=1I2K_OSUsedCPURate************\",\"measTypeKey\":\"UsedCPURate\",\"measUnitKey\":\"I2K_OS\",\"measUnitName\":\"操作系统监控\",\"moType\":\"OMS\",\"nodeId\":\"03e7998d576f48298aff4ff3ea85fac8\",\"originalValue\":\"ip=************\",\"resourceTypeKey\":\"OMS\",\"taskId\":13,\"unit\":\"%\"}","nodeId":"3a982aae9b27451689204342e6d43a8b","nodeName":"333","solutionId":"","solutionName":"","timeType":1},"number":2,"relevance":"100.0","root":false,"status":"success","title":"333","type":"Associated_KPI"},{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"roundrect\",\"style\":\"left:249px;top:182px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","gray":false,"id":"975b275f722644d3a9e8a8bb43c5c642","intoDegree":1,"leaf":false,"location":{"xAxis":2.0,"yAxis":1.0},"nodeParam":{"algorithmFeatureType":"association_matching","algorithmFeatureTypeName":"关联匹配(分析关联指标与主指标的线性相关性)","algorithmModelName":"DVCorrelationAnalysis_default","algorithmTemplateId":1,"algorithmTemplateName":"11(admin)","beginTime":1708394220000,"correlationDegree":100.0,"dataSourceType":"performance","datasourceId":"1","endTime":1708397820000,"executionId":"03e7998d576f48298aff4ff3ea85fac8","executionStatus":"success","indicatorDataType":"DV_PM","indicatorList":[{"auxiliaryIndicator":[],"checkedNetId":"/(_)OS=1","correlationDegree":100.0,"dataSourceId":1,"displayValue":"************","dn":"OS=1","dnName":"OSS","executionId":"03e7998d576f48298aff4ff3ea85fac8","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"975b275f722644d3a9e8a8bb43c5c642","originalValue":"ip=************","resourceTypeKey":"OMS","unit":"%"}],"indicatorSelectType":0,"isGray":false,"kpiTimeRange":"1|HOUR","kpiTimeType":"recently","mainIndicator":{"$ref":"$.nodes[1].nodeParam.mainIndicator"},"mainIndicatorDataType":"DV_PM","mainIndicatorJson":"{\"abnormal\":1,\"auxiliaryIndicator\":[],\"checkedNetId\":\"/(_)OS=1\",\"correlationDegree\":0.0,\"dataSourceId\":1,\"displayValue\":\"************\",\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"hasMeasObj\":\"true\",\"indexId\":\"I2K_OS~~UsedCPURate\",\"indexName\":\"CPU占用率(%)\",\"indicatorId\":\"OS=1I2K_OSUsedCPURate************\",\"measTypeKey\":\"UsedCPURate\",\"measUnitKey\":\"I2K_OS\",\"measUnitName\":\"操作系统监控\",\"moType\":\"OMS\",\"nodeId\":\"03e7998d576f48298aff4ff3ea85fac8\",\"originalValue\":\"ip=************\",\"resourceTypeKey\":\"OMS\",\"taskId\":13,\"unit\":\"%\"}","nodeId":"975b275f722644d3a9e8a8bb43c5c642","nodeName":"111","solutionId":"","solutionName":"","timeType":1},"number":1,"relevance":"100.0","root":false,"status":"success","title":"111","type":"Associated_KPI"},{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"roundrect\",\"style\":\"left:241px;top:319px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"fillStyle\":\"#e8f7ff\",\"lineWidth\":1}}]","gray":false,"id":"e42d054b574d41999ff1eb2cb6205af3","intoDegree":1,"leaf":false,"location":{"xAxis":2.0,"yAxis":2.0},"nodeParam":{"algorithmFeatureType":"association_matching","algorithmFeatureTypeName":"关联匹配(分析关联指标与主指标的线性相关性)","algorithmModelName":"DVCorrelationAnalysis_default","algorithmTemplateId":1,"algorithmTemplateName":"11(admin)","beginTime":1708394220000,"correlationDegree":100.0,"dataSourceType":"performance","datasourceId":"1","endTime":1708397820000,"executionId":"03e7998d576f48298aff4ff3ea85fac8","executionStatus":"success","indicatorDataType":"DV_PM","indicatorList":[{"auxiliaryIndicator":[],"checkedNetId":"/(_)OS=1","correlationDegree":100.0,"dataSourceId":1,"displayValue":"************","dn":"OS=1","dnName":"OSS","executionId":"03e7998d576f48298aff4ff3ea85fac8","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"e42d054b574d41999ff1eb2cb6205af3","originalValue":"ip=************","resourceTypeKey":"OMS","unit":"%"}],"indicatorSelectType":0,"isGray":false,"kpiTimeRange":"1|HOUR","kpiTimeType":"recently","mainIndicator":{"$ref":"$.nodes[1].nodeParam.mainIndicator"},"mainIndicatorDataType":"DV_PM","mainIndicatorJson":"{\"abnormal\":1,\"auxiliaryIndicator\":[],\"checkedNetId\":\"/(_)OS=1\",\"correlationDegree\":0.0,\"dataSourceId\":1,\"displayValue\":\"************\",\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"hasMeasObj\":\"true\",\"indexId\":\"I2K_OS~~UsedCPURate\",\"indexName\":\"CPU占用率(%)\",\"indicatorId\":\"OS=1I2K_OSUsedCPURate************\",\"measTypeKey\":\"UsedCPURate\",\"measUnitKey\":\"I2K_OS\",\"measUnitName\":\"操作系统监控\",\"moType\":\"OMS\",\"nodeId\":\"03e7998d576f48298aff4ff3ea85fac8\",\"originalValue\":\"ip=************\",\"resourceTypeKey\":\"OMS\",\"taskId\":13,\"unit\":\"%\"}","nodeId":"e42d054b574d41999ff1eb2cb6205af3","nodeName":"222","solutionId":"","solutionName":"","timeType":1},"number":1,"relevance":"100.0","root":false,"status":"success","title":"222","type":"Associated_KPI"},{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"roundrect\",\"style\":\"left:242px;top:256px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ffa63e\",\"lineWidth\":1,\"fillStyle\":\"#ffe8c8\"}}]","gray":false,"id":"27c0678e972a4d5e9d3139ee83ba561e","intoDegree":1,"leaf":true,"location":{"xAxis":2.0,"yAxis":3.0},"nodeParam":{"algorithmFeatureType":"association_matching","algorithmFeatureTypeName":"关联匹配(分析告警数量的变化与指标的关联关系)","algorithmModelName":"DVIndicatorAlarmAnalysis_default","algorithmTemplateId":3,"algorithmTemplateName":"gaojin(admin)","beginTime":1708311420000,"correlationDegree":30.8,"dataSourceType":"alarm","datasourceId":"3","endTime":1708397820000,"executionId":"03e7998d576f48298aff4ff3ea85fac8","executionStatus":"success","extendJson":"[{\"field\":\"severity\",\"operator\":\"in\",\"values\":[]}]","indicatorDataType":"DV_ALARM","indicatorList":[],"isGray":false,"kpiTimeRange":"1|DAY","kpiTimeType":"recently","mainIndicator":{"$ref":"$.nodes[1].nodeParam.mainIndicator"},"mainIndicatorDataType":"DV_PM","mainIndicatorJson":"{\"abnormal\":1,\"auxiliaryIndicator\":[],\"checkedNetId\":\"/(_)OS=1\",\"correlationDegree\":0.0,\"dataSourceId\":1,\"displayValue\":\"************\",\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"hasMeasObj\":\"true\",\"indexId\":\"I2K_OS~~UsedCPURate\",\"indexName\":\"CPU占用率(%)\",\"indicatorId\":\"OS=1I2K_OSUsedCPURate************\",\"measTypeKey\":\"UsedCPURate\",\"measUnitKey\":\"I2K_OS\",\"measUnitName\":\"操作系统监控\",\"moType\":\"OMS\",\"nodeId\":\"03e7998d576f48298aff4ff3ea85fac8\",\"originalValue\":\"ip=************\",\"resourceTypeKey\":\"OMS\",\"taskId\":13,\"unit\":\"%\"}","moreThanAlarmMax":false,"nodeId":"27c0678e972a4d5e9d3139ee83ba561e","nodeName":"22","resultFilePath":"787e9918-eade-49b3-acc2-d635ef2a91e2_1708397820000.json","timeType":1},"number":0,"relevance":"80.8","root":false,"status":"success","title":"22","type":"Associated_ALARM"},{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"roundrect\",\"style\":\"left:426px;top:181px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#53c97b\",\"lineWidth\":1,\"fillStyle\":\"#d5eee0\"}}]","gray":false,"id":"d983884b8dd04cf680ad2899493e9a68","intoDegree":1,"leaf":true,"location":{"xAxis":3.0,"yAxis":1.0},"nodeParam":{"algorithmFeatureType":"association_matching","correlationDegree":0.0,"dataSourceType":"execute","datasourceId":"8","executionId":"03e7998d576f48298aff4ff3ea85fac8","executionStatus":"success","extendJson":"{\"flowActions\":[{\"flowId\":\"\",\"flowGroup\":\"\",\"flowName\":\"\",\"ipsDetails\":[]}],\"causeInfo\":\"2\",\"helpInfo\":\"3\",\"ifAutoExecute\":false}","indicatorDataType":"DV_SHOW","indicatorList":[],"isGray":false,"mainIndicator":{"$ref":"$.nodes[1].nodeParam.mainIndicator"},"mainIndicatorDataType":"DV_PM","mainIndicatorJson":"{\"abnormal\":1,\"auxiliaryIndicator\":[],\"checkedNetId\":\"/(_)OS=1\",\"correlationDegree\":0.0,\"dataSourceId\":1,\"displayValue\":\"************\",\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"hasMeasObj\":\"true\",\"indexId\":\"I2K_OS~~UsedCPURate\",\"indexName\":\"CPU占用率(%)\",\"indicatorId\":\"OS=1I2K_OSUsedCPURate************\",\"measTypeKey\":\"UsedCPURate\",\"measUnitKey\":\"I2K_OS\",\"measUnitName\":\"操作系统监控\",\"moType\":\"OMS\",\"nodeId\":\"03e7998d576f48298aff4ff3ea85fac8\",\"originalValue\":\"ip=************\",\"resourceTypeKey\":\"OMS\",\"taskId\":13,\"unit\":\"%\"}","nodeId":"d983884b8dd04cf680ad2899493e9a68","nodeName":"yuna","timeType":1},"number":0,"relevance":"0.0","root":false,"title":"yuna","type":"Contingency_PLAN"},{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"roundrect\",\"style\":\"left:429px;top:326px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"fillStyle\":\"#f0e7ff\",\"lineWidth\":1}}]","gray":false,"id":"2845c3bf1a0c47479bb645b8f9e3c840","intoDegree":1,"leaf":true,"location":{"xAxis":3.0,"yAxis":2.0},"nodeParam":{"algorithmFeatureType":"association_matching","algorithmFeatureTypeName":"关联匹配(判断日志异常率的变化与指标的关联关系)","algorithmModelName":"DVIndicatorLogAnalysis_default","algorithmTemplateId":2,"algorithmTemplateName":"22(admin)","beginTime":1708394220000,"correlationDegree":3.5,"dataSourceType":"log","datasourceId":"4","endTime":1708397820000,"executionId":"03e7998d576f48298aff4ff3ea85fac8","executionStatus":"success","extendJson":"{\"solutionType\":\"com.huawei.testihealing.solution\",\"logType\":\"dsflogs1\",\"logSolutionName\":\"solution_testihealing\",\"analysisField\":\"Message\",\"queryFields\":{}}","indicatorDataType":"DV_LOG","indicatorList":[],"isGray":false,"kpiTimeRange":"1|HOUR","kpiTimeType":"recently","mainIndicator":{"$ref":"$.nodes[1].nodeParam.mainIndicator"},"mainIndicatorDataType":"DV_PM","mainIndicatorJson":"{\"abnormal\":1,\"auxiliaryIndicator\":[],\"checkedNetId\":\"/(_)OS=1\",\"correlationDegree\":0.0,\"dataSourceId\":1,\"displayValue\":\"************\",\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"hasMeasObj\":\"true\",\"indexId\":\"I2K_OS~~UsedCPURate\",\"indexName\":\"CPU占用率(%)\",\"indicatorId\":\"OS=1I2K_OSUsedCPURate************\",\"measTypeKey\":\"UsedCPURate\",\"measUnitKey\":\"I2K_OS\",\"measUnitName\":\"操作系统监控\",\"moType\":\"OMS\",\"nodeId\":\"03e7998d576f48298aff4ff3ea85fac8\",\"originalValue\":\"ip=************\",\"resourceTypeKey\":\"OMS\",\"taskId\":13,\"unit\":\"%\"}","nodeId":"2845c3bf1a0c47479bb645b8f9e3c840","nodeName":"rizhi","resultFilePath":"8846c650-86a3-46c4-9e06-1da6debbeccc_1708397820000.json","timeType":1},"number":1,"relevance":"83.5","root":false,"status":"success","title":"rizhi","type":"Associated_LOG"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","id":"flow-diagram_P2","relevance":"30.8","source":"03e7998d576f48298aff4ff3ea85fac8","sourceAnchor":3,"target":"27c0678e972a4d5e9d3139ee83ba561e","targetAnchor":7},{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","id":"flow-diagram_P1","relevance":"100.0","source":"03e7998d576f48298aff4ff3ea85fac8","sourceAnchor":1,"target":"975b275f722644d3a9e8a8bb43c5c642","targetAnchor":7},{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","id":"flow-diagram_P7","relevance":"100.0","source":"03e7998d576f48298aff4ff3ea85fac8","sourceAnchor":4,"target":"3a982aae9b27451689204342e6d43a8b","targetAnchor":6},{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","id":"flow-diagram_P5","relevance":"100.0","source":"03e7998d576f48298aff4ff3ea85fac8","sourceAnchor":4,"target":"e42d054b574d41999ff1eb2cb6205af3","targetAnchor":7},{"condition":"<=100.0","figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}],\"condition\":[{\"type\":\"\",\"style\":\"left:367.26666666666665px;top:325.73333333333335px;width:60px;height:30px;\",\"options\":{},\"context\":{\"strokeStyle\":\"#333333\",\"fillStyle\":\"white\",\"lineWidth\":1}}]}","id":"flow-diagram_P6","relevance":"3.5","source":"e42d054b574d41999ff1eb2cb6205af3","sourceAnchor":3,"target":"2845c3bf1a0c47479bb645b8f9e3c840","targetAnchor":7},{"condition":"<=100.0","figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}],\"condition\":[{\"type\":\"\",\"style\":\"left:373.3965517241378px;top:184.39655172413796px;width:60px;height:30px;\",\"options\":{},\"context\":{\"strokeStyle\":\"#333333\",\"fillStyle\":\"white\",\"lineWidth\":1}}]}","id":"flow-diagram_P3","relevance":"0.0","source":"975b275f722644d3a9e8a8bb43c5c642","sourceAnchor":3,"target":"d983884b8dd04cf680ad2899493e9a68","targetAnchor":7}]}',null,'OS=1I2K_OSUsedCPURate************',13,1708397947956,'success',null,1);

INSERT INTO TBL_AIOPS_TRIGGER_EXECUTION_RESULT(ID, START_TIME, TEMPLATE_ID, TREE_RESULT, VIEW_RESULT, INDICATOR_ID, TASK_ID, UPDATE_TIME, STATUS, FAILURE_CAUSE, CREATE_USER) values ('77',1708397820000,15,'{"nodes":[{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"round\",\"style\":\"left:94px;top:233px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","gray":false,"id":"03e7998d576f48298aff4ff3ea85fac8","intoDegree":0,"leaf":false,"location":{"xAxis":1.0,"yAxis":1.0},"root":true,"title":"CPU占用率(%)","type":"start"},{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"roundrect\",\"style\":\"left:236px;top:389px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"fillStyle\":\"#e8f7ff\",\"lineWidth\":1}}]","gray":false,"id":"3a982aae9b27451689204342e6d43a8b","intoDegree":1,"leaf":true,"location":{"xAxis":2.0,"yAxis":4.0},"nodeParam":{"algorithmFeatureType":"association_matching","algorithmFeatureTypeName":"关联匹配(分析关联指标与主指标的线性相关性)","algorithmModelName":"DVCorrelationAnalysis_default","algorithmTemplateId":1,"algorithmTemplateName":"11(admin)","beginTime":1708394220000,"correlationDegree":100.0,"dataSourceType":"performance","datasourceId":"1","endTime":1708397820000,"executionId":"03e7998d576f48298aff4ff3ea85fac8","executionStatus":"success","indicatorDataType":"DV_PM","indicatorList":[{"auxiliaryIndicator":[],"checkedNetId":"/(_)OS=1","correlationDegree":100.0,"dataSourceId":1,"displayValue":"************","dn":"OS=1","dnName":"OSS","executionId":"03e7998d576f48298aff4ff3ea85fac8","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"3a982aae9b27451689204342e6d43a8b","originalValue":"ip=************","resourceTypeKey":"OMS","unit":"%"},{"auxiliaryIndicator":[],"checkedNetId":"/(_)OS=1","correlationDegree":27.3,"dataSourceId":1,"displayValue":"************","dn":"OS=1","dnName":"OSS","executionId":"03e7998d576f48298aff4ff3ea85fac8","hasMeasObj":"true","indexId":"I2K_OS~~UsedMemoryRate","indexName":"内存使用率(%)","indicatorId":"OS=1I2K_OSUsedMemoryRate************","measTypeKey":"UsedMemoryRate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"3a982aae9b27451689204342e6d43a8b","originalValue":"ip=************","resourceTypeKey":"OMS","unit":"%"}],"indicatorSelectType":0,"isGray":false,"kpiTimeRange":"1|HOUR","kpiTimeType":"recently","mainIndicator":{"abnormal":1,"auxiliaryIndicator":[],"checkedNetId":"/(_)OS=1","correlationDegree":0.0,"dataSourceId":1,"displayValue":"************","dn":"OS=1","dnName":"OSS","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"03e7998d576f48298aff4ff3ea85fac8","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":13,"unit":"%"},"mainIndicatorDataType":"DV_PM","mainIndicatorJson":"{\"abnormal\":1,\"auxiliaryIndicator\":[],\"checkedNetId\":\"/(_)OS=1\",\"correlationDegree\":0.0,\"dataSourceId\":1,\"displayValue\":\"************\",\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"hasMeasObj\":\"true\",\"indexId\":\"I2K_OS~~UsedCPURate\",\"indexName\":\"CPU占用率(%)\",\"indicatorId\":\"OS=1I2K_OSUsedCPURate************\",\"measTypeKey\":\"UsedCPURate\",\"measUnitKey\":\"I2K_OS\",\"measUnitName\":\"操作系统监控\",\"moType\":\"OMS\",\"nodeId\":\"03e7998d576f48298aff4ff3ea85fac8\",\"originalValue\":\"ip=************\",\"resourceTypeKey\":\"OMS\",\"taskId\":13,\"unit\":\"%\"}","nodeId":"3a982aae9b27451689204342e6d43a8b","nodeName":"333","solutionId":"","solutionName":"","timeType":1},"number":2,"relevance":"100.0","root":false,"status":"success","title":"333","type":"Associated_KPI"},{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"roundrect\",\"style\":\"left:249px;top:182px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","gray":false,"id":"975b275f722644d3a9e8a8bb43c5c642","intoDegree":1,"leaf":false,"location":{"xAxis":2.0,"yAxis":1.0},"nodeParam":{"algorithmFeatureType":"association_matching","algorithmFeatureTypeName":"关联匹配(分析关联指标与主指标的线性相关性)","algorithmModelName":"DVCorrelationAnalysis_default","algorithmTemplateId":1,"algorithmTemplateName":"11(admin)","beginTime":1708394220000,"correlationDegree":100.0,"dataSourceType":"performance","datasourceId":"1","endTime":1708397820000,"executionId":"03e7998d576f48298aff4ff3ea85fac8","executionStatus":"success","indicatorDataType":"DV_PM","indicatorList":[{"auxiliaryIndicator":[],"checkedNetId":"/(_)OS=1","correlationDegree":100.0,"dataSourceId":1,"displayValue":"************","dn":"OS=1","dnName":"OSS","executionId":"03e7998d576f48298aff4ff3ea85fac8","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"975b275f722644d3a9e8a8bb43c5c642","originalValue":"ip=************","resourceTypeKey":"OMS","unit":"%"}],"indicatorSelectType":0,"isGray":false,"kpiTimeRange":"1|HOUR","kpiTimeType":"recently","mainIndicator":{"$ref":"$.nodes[1].nodeParam.mainIndicator"},"mainIndicatorDataType":"DV_PM","mainIndicatorJson":"{\"abnormal\":1,\"auxiliaryIndicator\":[],\"checkedNetId\":\"/(_)OS=1\",\"correlationDegree\":0.0,\"dataSourceId\":1,\"displayValue\":\"************\",\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"hasMeasObj\":\"true\",\"indexId\":\"I2K_OS~~UsedCPURate\",\"indexName\":\"CPU占用率(%)\",\"indicatorId\":\"OS=1I2K_OSUsedCPURate************\",\"measTypeKey\":\"UsedCPURate\",\"measUnitKey\":\"I2K_OS\",\"measUnitName\":\"操作系统监控\",\"moType\":\"OMS\",\"nodeId\":\"03e7998d576f48298aff4ff3ea85fac8\",\"originalValue\":\"ip=************\",\"resourceTypeKey\":\"OMS\",\"taskId\":13,\"unit\":\"%\"}","nodeId":"975b275f722644d3a9e8a8bb43c5c642","nodeName":"111","solutionId":"","solutionName":"","timeType":1},"number":1,"relevance":"100.0","root":false,"status":"success","title":"111","type":"Associated_KPI"},{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"roundrect\",\"style\":\"left:241px;top:319px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"fillStyle\":\"#e8f7ff\",\"lineWidth\":1}}]","gray":false,"id":"e42d054b574d41999ff1eb2cb6205af3","intoDegree":1,"leaf":false,"location":{"xAxis":2.0,"yAxis":2.0},"nodeParam":{"algorithmFeatureType":"association_matching","algorithmFeatureTypeName":"关联匹配(分析关联指标与主指标的线性相关性)","algorithmModelName":"DVCorrelationAnalysis_default","algorithmTemplateId":1,"algorithmTemplateName":"11(admin)","beginTime":1708394220000,"correlationDegree":100.0,"dataSourceType":"performance","datasourceId":"1","endTime":1708397820000,"executionId":"03e7998d576f48298aff4ff3ea85fac8","executionStatus":"success","indicatorDataType":"DV_PM","indicatorList":[{"auxiliaryIndicator":[],"checkedNetId":"/(_)OS=1","correlationDegree":100.0,"dataSourceId":1,"displayValue":"************","dn":"OS=1","dnName":"OSS","executionId":"03e7998d576f48298aff4ff3ea85fac8","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"e42d054b574d41999ff1eb2cb6205af3","originalValue":"ip=************","resourceTypeKey":"OMS","unit":"%"}],"indicatorSelectType":0,"isGray":false,"kpiTimeRange":"1|HOUR","kpiTimeType":"recently","mainIndicator":{"$ref":"$.nodes[1].nodeParam.mainIndicator"},"mainIndicatorDataType":"DV_PM","mainIndicatorJson":"{\"abnormal\":1,\"auxiliaryIndicator\":[],\"checkedNetId\":\"/(_)OS=1\",\"correlationDegree\":0.0,\"dataSourceId\":1,\"displayValue\":\"************\",\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"hasMeasObj\":\"true\",\"indexId\":\"I2K_OS~~UsedCPURate\",\"indexName\":\"CPU占用率(%)\",\"indicatorId\":\"OS=1I2K_OSUsedCPURate************\",\"measTypeKey\":\"UsedCPURate\",\"measUnitKey\":\"I2K_OS\",\"measUnitName\":\"操作系统监控\",\"moType\":\"OMS\",\"nodeId\":\"03e7998d576f48298aff4ff3ea85fac8\",\"originalValue\":\"ip=************\",\"resourceTypeKey\":\"OMS\",\"taskId\":13,\"unit\":\"%\"}","nodeId":"e42d054b574d41999ff1eb2cb6205af3","nodeName":"222","solutionId":"","solutionName":"","timeType":1},"number":1,"relevance":"100.0","root":false,"status":"success","title":"222","type":"Associated_KPI"},{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"roundrect\",\"style\":\"left:242px;top:256px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ffa63e\",\"lineWidth\":1,\"fillStyle\":\"#ffe8c8\"}}]","gray":false,"id":"27c0678e972a4d5e9d3139ee83ba561e","intoDegree":1,"leaf":true,"location":{"xAxis":2.0,"yAxis":3.0},"nodeParam":{"algorithmFeatureType":"association_matching","algorithmFeatureTypeName":"关联匹配(分析告警数量的变化与指标的关联关系)","algorithmModelName":"DVIndicatorAlarmAnalysis_default","algorithmTemplateId":3,"algorithmTemplateName":"gaojin(admin)","beginTime":1708311420000,"correlationDegree":30.8,"dataSourceType":"alarm","datasourceId":"3","endTime":1708397820000,"executionId":"03e7998d576f48298aff4ff3ea85fac8","executionStatus":"success","extendJson":"[{\"field\":\"severity\",\"operator\":\"in\",\"values\":[]}]","indicatorDataType":"DV_ALARM","indicatorList":[],"isGray":false,"kpiTimeRange":"1|DAY","kpiTimeType":"recently","mainIndicator":{"$ref":"$.nodes[1].nodeParam.mainIndicator"},"mainIndicatorDataType":"DV_PM","mainIndicatorJson":"{\"abnormal\":1,\"auxiliaryIndicator\":[],\"checkedNetId\":\"/(_)OS=1\",\"correlationDegree\":0.0,\"dataSourceId\":1,\"displayValue\":\"************\",\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"hasMeasObj\":\"true\",\"indexId\":\"I2K_OS~~UsedCPURate\",\"indexName\":\"CPU占用率(%)\",\"indicatorId\":\"OS=1I2K_OSUsedCPURate************\",\"measTypeKey\":\"UsedCPURate\",\"measUnitKey\":\"I2K_OS\",\"measUnitName\":\"操作系统监控\",\"moType\":\"OMS\",\"nodeId\":\"03e7998d576f48298aff4ff3ea85fac8\",\"originalValue\":\"ip=************\",\"resourceTypeKey\":\"OMS\",\"taskId\":13,\"unit\":\"%\"}","moreThanAlarmMax":false,"nodeId":"27c0678e972a4d5e9d3139ee83ba561e","nodeName":"22","resultFilePath":"787e9918-eade-49b3-acc2-d635ef2a91e2_1708397820000.json","timeType":1},"number":0,"relevance":"30.8","root":false,"status":"success","title":"22","type":"Associated_ALARM"},{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"roundrect\",\"style\":\"left:426px;top:181px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#53c97b\",\"lineWidth\":1,\"fillStyle\":\"#d5eee0\"}}]","gray":false,"id":"d983884b8dd04cf680ad2899493e9a68","intoDegree":1,"leaf":true,"location":{"xAxis":3.0,"yAxis":1.0},"nodeParam":{"algorithmFeatureType":"association_matching","correlationDegree":0.0,"dataSourceType":"execute","datasourceId":"8","executionId":"03e7998d576f48298aff4ff3ea85fac8","executionStatus":"success","extendJson":"{\"flowActions\":[{\"flowId\":\"\",\"flowGroup\":\"\",\"flowName\":\"\",\"ipsDetails\":[]}],\"causeInfo\":\"2\",\"helpInfo\":\"3\",\"ifAutoExecute\":false}","indicatorDataType":"DV_SHOW","indicatorList":[],"isGray":false,"mainIndicator":{"$ref":"$.nodes[1].nodeParam.mainIndicator"},"mainIndicatorDataType":"DV_PM","mainIndicatorJson":"{\"abnormal\":1,\"auxiliaryIndicator\":[],\"checkedNetId\":\"/(_)OS=1\",\"correlationDegree\":0.0,\"dataSourceId\":1,\"displayValue\":\"************\",\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"hasMeasObj\":\"true\",\"indexId\":\"I2K_OS~~UsedCPURate\",\"indexName\":\"CPU占用率(%)\",\"indicatorId\":\"OS=1I2K_OSUsedCPURate************\",\"measTypeKey\":\"UsedCPURate\",\"measUnitKey\":\"I2K_OS\",\"measUnitName\":\"操作系统监控\",\"moType\":\"OMS\",\"nodeId\":\"03e7998d576f48298aff4ff3ea85fac8\",\"originalValue\":\"ip=************\",\"resourceTypeKey\":\"OMS\",\"taskId\":13,\"unit\":\"%\"}","nodeId":"d983884b8dd04cf680ad2899493e9a68","nodeName":"yuna","timeType":1},"number":0,"relevance":"0.0","root":false,"title":"yuna","type":"Contingency_PLAN"},{"childNodes":[],"demarcationLocating":"Demarcation","figures":"[{\"type\":\"roundrect\",\"style\":\"left:429px;top:326px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"fillStyle\":\"#f0e7ff\",\"lineWidth\":1}}]","gray":false,"id":"2845c3bf1a0c47479bb645b8f9e3c840","intoDegree":1,"leaf":true,"location":{"xAxis":3.0,"yAxis":2.0},"nodeParam":{"algorithmFeatureType":"association_matching","algorithmFeatureTypeName":"关联匹配(判断日志异常率的变化与指标的关联关系)","algorithmModelName":"DVIndicatorLogAnalysis_default","algorithmTemplateId":2,"algorithmTemplateName":"22(admin)","beginTime":1708394220000,"correlationDegree":3.5,"dataSourceType":"log","datasourceId":"4","endTime":1708397820000,"executionId":"03e7998d576f48298aff4ff3ea85fac8","executionStatus":"success","extendJson":"{\"solutionType\":\"com.huawei.testihealing.solution\",\"logType\":\"dsflogs1\",\"logSolutionName\":\"solution_testihealing\",\"analysisField\":\"Message\",\"queryFields\":{}}","indicatorDataType":"DV_LOG","indicatorList":[],"isGray":false,"kpiTimeRange":"1|HOUR","kpiTimeType":"recently","mainIndicator":{"$ref":"$.nodes[1].nodeParam.mainIndicator"},"mainIndicatorDataType":"DV_PM","mainIndicatorJson":"{\"abnormal\":1,\"auxiliaryIndicator\":[],\"checkedNetId\":\"/(_)OS=1\",\"correlationDegree\":0.0,\"dataSourceId\":1,\"displayValue\":\"************\",\"dn\":\"OS=1\",\"dnName\":\"OSS\",\"hasMeasObj\":\"true\",\"indexId\":\"I2K_OS~~UsedCPURate\",\"indexName\":\"CPU占用率(%)\",\"indicatorId\":\"OS=1I2K_OSUsedCPURate************\",\"measTypeKey\":\"UsedCPURate\",\"measUnitKey\":\"I2K_OS\",\"measUnitName\":\"操作系统监控\",\"moType\":\"OMS\",\"nodeId\":\"03e7998d576f48298aff4ff3ea85fac8\",\"originalValue\":\"ip=************\",\"resourceTypeKey\":\"OMS\",\"taskId\":13,\"unit\":\"%\"}","nodeId":"2845c3bf1a0c47479bb645b8f9e3c840","nodeName":"rizhi","resultFilePath":"8846c650-86a3-46c4-9e06-1da6debbeccc_1708397820000.json","timeType":1},"number":1,"relevance":"3.5","root":false,"status":"success","title":"rizhi","type":"Associated_LOG"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","id":"flow-diagram_P2","relevance":"30.8","source":"03e7998d576f48298aff4ff3ea85fac8","sourceAnchor":3,"target":"27c0678e972a4d5e9d3139ee83ba561e","targetAnchor":7},{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","id":"flow-diagram_P1","relevance":"100.0","source":"03e7998d576f48298aff4ff3ea85fac8","sourceAnchor":1,"target":"975b275f722644d3a9e8a8bb43c5c642","targetAnchor":7},{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","id":"flow-diagram_P7","relevance":"100.0","source":"03e7998d576f48298aff4ff3ea85fac8","sourceAnchor":4,"target":"3a982aae9b27451689204342e6d43a8b","targetAnchor":6},{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","id":"flow-diagram_P5","relevance":"100.0","source":"03e7998d576f48298aff4ff3ea85fac8","sourceAnchor":4,"target":"e42d054b574d41999ff1eb2cb6205af3","targetAnchor":7},{"condition":"<=100.0","figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}],\"condition\":[{\"type\":\"\",\"style\":\"left:367.26666666666665px;top:325.73333333333335px;width:60px;height:30px;\",\"options\":{},\"context\":{\"strokeStyle\":\"#333333\",\"fillStyle\":\"white\",\"lineWidth\":1}}]}","id":"flow-diagram_P6","relevance":"3.5","source":"e42d054b574d41999ff1eb2cb6205af3","sourceAnchor":3,"target":"2845c3bf1a0c47479bb645b8f9e3c840","targetAnchor":7},{"condition":"<=100.0","figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}],\"condition\":[{\"type\":\"\",\"style\":\"left:373.3965517241378px;top:184.39655172413796px;width:60px;height:30px;\",\"options\":{},\"context\":{\"strokeStyle\":\"#333333\",\"fillStyle\":\"white\",\"lineWidth\":1}}]}","id":"flow-diagram_P3","relevance":"0.0","source":"975b275f722644d3a9e8a8bb43c5c642","sourceAnchor":3,"target":"d983884b8dd04cf680ad2899493e9a68","targetAnchor":7}]}',null,'OS=1I2K_OSUsedCPURate************',13,1708397847956,'analyzing',null,1);

DROP TABLE IF EXISTS TBL_AIOPS_INTELLIGENTR_RECOMMENDATION;
CREATE TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION (
                                                       EXECUTION_ID VARCHAR(128) NOT NULL,
                                                       NODE_ID VARCHAR(128) NOT NULL,
                                                       NODE_NAME VARCHAR(128) NOT NULL,
                                                       NODE_TYPE VARCHAR(128) NOT NULL,
                                                       GENERALERAL_RECOMMENDATION CLOB,
                                                       CASE_RECOMMENDATION CLOB,
                                                       QUERY_INFO VARCHAR(8000),
                                                       CORRELATION_NODE_ID VARCHAR(128),
                                                       CONSTRAINT TBL_AIOPS_INTELLIGENTR_RECOMMENDATION PRIMARY KEY (EXECUTION_ID, NODE_ID)
);

insert into TBL_AIOPS_INTELLIGENTR_RECOMMENDATION (EXECUTION_ID, NODE_ID, NODE_NAME, NODE_TYPE,
                                                   GENERALERAL_RECOMMENDATION, CASE_RECOMMENDATION, QUERY_INFO, CORRELATION_NODE_ID) VALUES ('123', '03e7998d576f48298aff4ff3ea85fac8', '智能推荐', 'INTELLIGENT', 'NULL', '[{"caseId": "1", "caseName": "案例1", "caseCause": "null"}]', '', '123455');
insert into TBL_AIOPS_INTELLIGENTR_RECOMMENDATION (EXECUTION_ID, NODE_ID, NODE_NAME, NODE_TYPE,
                                                   GENERALERAL_RECOMMENDATION, CASE_RECOMMENDATION, QUERY_INFO, CORRELATION_NODE_ID) VALUES ('234', '03e7998d576f48298aff4ff3ea85fac9', '智能推荐', 'INTELLIGENT', 'NULL', '[]', '', '123455');

DROP TABLE IF EXISTS TBL_AIOPS_TASK_INDICATOR;
CREATE TABLE TBL_AIOPS_TASK_INDICATOR (
    INDICATOR_ID VARCHAR(256) NOT NULL,
    TASK_ID INTEGER NOT NULL,
    ABNORMAL NUMBER(1),
    MEAS_UNIT_KEY VARCHAR(256),
    MEAS_UNIT_NAME VARCHAR(256),
    MEAS_TYPE_KEY VARCHAR(256),
    DN VARCHAR(256),
    INDEX_NAME VARCHAR(256),
    MO_TYPE VARCHAR(256),
    DISPLAY_VALUE VARCHAR(8000),
    ORIGINAL_VALUE VARCHAR(256),
    UNIT VARCHAR(256),
    DN_NAME VARCHAR(256),
    CHECKED_NET_ID VARCHAR(256),
    HAS_MEAS_OBJ VARCHAR(5),
    INDEX_ID VARCHAR(256),
    RESOURCE_TYPE_KEY VARCHAR(256),
    PQL VARCHAR(8000),
    CONSTRAINT TBL_AIOPS_TASK_INDICATOR PRIMARY KEY (INDICATOR_ID, TASK_ID)
);
INSERT INTO TBL_AIOPS_TASK_INDICATOR (INDICATOR_ID, TASK_ID, ABNORMAL, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, DN,
                                      INDEX_NAME, MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID,
                                      HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY)
VALUES ('OS=1I2K_OSUsedCPURate*************', 101, 0, 'I2K_OS', '操作系统监控', 'IOWait', 'OS=1', 'IO等待(%)', 'OMS',
        '*************', 'ip=*************', '%', 'OSS', '/(_)OS=1', 'true', 'I2K_OS~~IOWait', 'OMS');

DROP TABLE IF EXISTS TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_RECORD;
CREATE TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_RECORD (
                                                              ID VARCHAR(128) NOT NULL,
                                                              USER_NAME VARCHAR(128)  NOT NULL,
                                                              RECOMMEND_TYPE VARCHAR(100) NOT NULL,
                                                              UPDATE_TIME VARCHAR(128) NOT NULL,
                                                              EXECUTION_ID VARCHAR(128) NOT NULL,
                                                              CONSTRAINT TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_RECORD PRIMARY KEY (ID)
);

INSERT INTO TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_RECORD (ID, USER_NAME, RECOMMEND_TYPE, UPDATE_TIME, EXECUTION_ID) VALUES ('4efba2c8-5254-4eaa-a6e8-3fc06fc30be0', 'admin', 'DV_INTELLIGENT_RECOMMENDATION', '2024-08-05 16:00:44.426', 'bb0d0027742b42d69d5408b19f194975');

DROP TABLE IF EXISTS TBL_AIOPS_KNOWLEDGE_REPOSITORY_INFO;
CREATE TABLE TBL_AIOPS_KNOWLEDGE_REPOSITORY_INFO
(
    ID          VARCHAR(100) NOT NULL,
    NAME        VARCHAR(100),
    SOLUTION    VARCHAR(100),
    CREATE_TIME VARCHAR(50),
    UPDATE_TIME VARCHAR(50),
    PRIMARY KEY (ID)
);
insert into TBL_AIOPS_KNOWLEDGE_REPOSITORY_INFO (ID, NAME, SOLUTION, CREATE_TIME, UPDATE_TIME) values ('testone', 'name', 'CBS', '1', '2');