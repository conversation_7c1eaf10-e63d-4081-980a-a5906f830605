/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */


DROP TABLE IF EXISTS TBL_AIOPS_CAPACITY_CORRELATION;
CREATE TABLE TBL_AIOPS_CAPACITY_CORRELATION (
                                                TASK_ID INTEGER NOT NULL,
                                                TRAIN_STATUS INTEGER,
                                                PREDICT_STATUS INTEGER,
                                                MULTIPLE_TASK_TYPE INTEGER NOT NULL,
                                                IF_RESERVE INTEGER,
                                                IF_REPREDICT INTEGER,
                                                SORT_UPDATE_TIME BIGINT,
                                                USER_ID VARCHAR(256) NOT NULL,
                                                RELATION_TREE CLOB,
                                                EAM_TREE_ADD_RELATION_TREE CLOB,
                                                RESULT_RELATION_TREE CLOB,
                                                ASSOCIATED_PREDICT_TASK_ID INTEGER,
                                                TWO_MACHINE_GROUP_NAME VARCHAR(256),
                                                ALARM_NORMAL_LEVEL INTEGER,
                                                ALARM_SEVERITY_LEVEL INTEGER,
                                                ALARM_REPORT_TIME_RANGE VARCHAR(256),
                                                CONSTRAINT TBL_AIOPS_CAPACITY_CORRELATION PRIMARY KEY (TASK_ID)
);

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE, RESULT_RELATION_TREE)
VALUES (1, 0, 1, 1, 1635405671444, 'pf', 'tree', 'tree', 'tree,width:50px');

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE, RESULT_RELATION_TREE)
VALUES (2, 0, 2, 1, 1635405671444, 'pf', 'tree', 'tree', 'tree');

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE, RESULT_RELATION_TREE)
VALUES (3, 0, 7, 1, 1635405671444,'pf', '{"nodes":[{"id":"1","leaf":false,"nodeParam":{"canRetrain":false,"dependenceIndicatorList":[],"errorIndicatorNum":1,"ifNetChange":false,"ignoreSubTreeNode":[],"indicatorList":[],"indicatorNum":1,"normalIndicatorNum":1,"warningIndicatorNum":1},"root":false,"type":"test type","figures":"width:360px;height:135px;"}],"paths":[{"source":"s","target":"t"}]}', '{"nodes":[{"id":"1","leaf":false,"nodeParam":{"canRetrain":false,"dependenceIndicatorList":[],"errorIndicatorNum":1,"ifNetChange":false,"ignoreSubTreeNode":[],"indicatorList":[],"indicatorNum":1,"normalIndicatorNum":1,"warningIndicatorNum":1},"root":false,"type":"test type","figures":"width:585px;height:195px;"}],"paths":[{"source":"s","target":"t"}]}', '{"nodes":[{"id":"1","leaf":false,"nodeParam":{"canRetrain":false,"dependenceIndicatorList":[],"errorIndicatorNum":1,"ifNetChange":false,"ignoreSubTreeNode":[],"indicatorList":[],"indicatorNum":1,"normalIndicatorNum":1,"warningIndicatorNum":1},"root":false,"type":"test type","figures":"width:585px;height:195px;"}],"paths":[{"source":"s","target":"t"}]}');

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE, RESULT_RELATION_TREE)
VALUES (4, 0, 0, 1, 1635405671444,'pf', '{"nodes":[{"id":"1","leaf":false,"nodeParam":{"canRetrain":false,"dependenceIndicatorList":[],"errorIndicatorNum":1,"ifNetChange":false,"ignoreSubTreeNode":[],"indicatorList":[],"indicatorNum":1,"normalIndicatorNum":1,"warningIndicatorNum":1},"root":false,"type":"test type"}],"paths":[{"source":"s","target":"t"}]}', '{"nodes":[{"id":"1","leaf":false,"nodeParam":{"canRetrain":false,"dependenceIndicatorList":[],"errorIndicatorNum":1,"ifNetChange":false,"ignoreSubTreeNode":[],"indicatorList":[],"indicatorNum":1,"normalIndicatorNum":1,"warningIndicatorNum":1},"root":false,"type":"test type"}],"paths":[{"source":"s","target":"t"}]}', '{"nodes":[{"id":"1","leaf":false,"nodeParam":{"canRetrain":false,"dependenceIndicatorList":[],"errorIndicatorNum":1,"ifNetChange":false,"ignoreSubTreeNode":[],"indicatorList":[],"indicatorNum":1,"normalIndicatorNum":1,"warningIndicatorNum":1},"root":false,"type":"test type"}],"paths":[{"source":"s","target":"t"}]}');

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE, RESULT_RELATION_TREE)
VALUES (5, 0, 7, 1, 1635405671444,'pf', '{"nodes":[{"id":"1","leaf":false,"nodeParam":{"canRetrain":false,"dependenceIndicatorList":[],"errorIndicatorNum":1,"ifNetChange":false,"ignoreSubTreeNode":[],"indicatorList":[],"indicatorNum":1,"normalIndicatorNum":1,"warningIndicatorNum":1},"root":false,"type":"test type"}],"paths":[{"source":"s","target":"t"}]}', '{"nodes":[{"id":"1","leaf":false,"nodeParam":{"canRetrain":false,"dependenceIndicatorList":[],"errorIndicatorNum":1,"ifNetChange":false,"ignoreSubTreeNode":[],"indicatorList":[],"indicatorNum":1,"normalIndicatorNum":1,"warningIndicatorNum":1},"root":false,"type":"test type"}],"paths":[{"source":"s","target":"t"}]}', '{"nodes":[{"id":"1","leaf":false,"nodeParam":{"canRetrain":false,"dependenceIndicatorList":[],"errorIndicatorNum":1,"ifNetChange":false,"ignoreSubTreeNode":[],"indicatorList":[],"indicatorNum":1,"normalIndicatorNum":1,"warningIndicatorNum":1},"root":false,"type":"test type"}],"paths":[{"source":"s","target":"t"}]}');

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE, RESULT_RELATION_TREE)
VALUES (6, 0, 7, 1, 1635405671444,'pf', '{"nodes":[{"id":"1","leaf":false,"nodeParam":{"canRetrain":false,"dependenceIndicatorList":[],"errorIndicatorNum":1,"ifNetChange":false,"ignoreSubTreeNode":[],"indicatorList":[],"indicatorNum":1,"normalIndicatorNum":1,"warningIndicatorNum":1},"root":false,"type":"test type"}],"paths":[{"source":"s","target":"t"}]}', '{"nodes":[{"id":"1","leaf":false,"nodeParam":{"canRetrain":false,"dependenceIndicatorList":[],"errorIndicatorNum":1,"ifNetChange":false,"ignoreSubTreeNode":[],"indicatorList":[],"indicatorNum":1,"normalIndicatorNum":1,"warningIndicatorNum":1},"root":false,"type":"test type"}],"paths":[{"source":"s","target":"t"}]}', '{"nodes":[{"id":"1","leaf":false,"nodeParam":{"canRetrain":false,"dependenceIndicatorList":[],"errorIndicatorNum":1,"ifNetChange":false,"ignoreSubTreeNode":[],"indicatorList":[],"indicatorNum":1,"normalIndicatorNum":1,"warningIndicatorNum":1},"root":false,"type":"test type"}],"paths":[{"source":"s","target":"t"}]}');

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, IF_RESERVE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE, RESULT_RELATION_TREE)
VALUES (7,0,0,1,null,null,'pf','{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"e0449db9863c4e7ba19de5587802d834"},"root":true,"title":"主节点","type":"start","uid":"e0449db9863c4e7ba19de5587802d834"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"a23fd95061774324b62e510480863f4d","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"a23fd95061774324b62e510480863f4d"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"a23fd95061774324b62e510480863f4d"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}',
        '{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"948a4dbe0328426b860488261cdaca4b"},"root":true,"title":"主节点","type":"start","uid":"948a4dbe0328426b860488261cdaca4b"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"79257a89d6a642d3ab9839deaf8b40b4","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"79257a89d6a642d3ab9839deaf8b40b4"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"79257a89d6a642d3ab9839deaf8b40b4"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}', null);

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, IF_RESERVE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE, RESULT_RELATION_TREE)
VALUES (8,0,2,1,0,null,1,'{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"6bd3bfc5dd2d4ae8be2a2960f406a60f"},"root":true,"title":"主节点","type":"start","uid":"6bd3bfc5dd2d4ae8be2a2960f406a60f"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"7e7f28a9596e47c1acacf7ecbe5659e5","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"7e7f28a9596e47c1acacf7ecbe5659e5"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"7e7f28a9596e47c1acacf7ecbe5659e5"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}',
        '{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"cd92a4205a734ea4bf33b285fa230989"},"root":true,"title":"主节点","type":"start","uid":"cd92a4205a734ea4bf33b285fa230989"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"929751cae3a342a09a402af686610fe5","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"929751cae3a342a09a402af686610fe5"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"929751cae3a342a09a402af686610fe5"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}',
        '{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":"23.63","lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"40","predictionLowerThreshold":"40","predictionUpperThreshold":"40","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"50ebf9beac23491f8835a6b5cddfbf2f","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"start","uid":"50ebf9beac23491f8835a6b5cddfbf2f"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":null,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":null}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"c9429bba50db4d8bb23deeea56e00753","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"c9429bba50db4d8bb23deeea56e00753"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1688109507719,"predictType":"RUN"}'
        );

insert into TBL_AIOPS_CAPACITY_CORRELATION
(TASK_ID, TRAIN_STATUS, PREDICT_STATUS, MULTIPLE_TASK_TYPE, IF_RESERVE, SORT_UPDATE_TIME, USER_ID, RELATION_TREE, EAM_TREE_ADD_RELATION_TREE, RESULT_RELATION_TREE)
VALUES (9,0,3,1,0,null,1,'{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"6bd3bfc5dd2d4ae8be2a2960f406a60f"},"root":true,"title":"主节点","type":"start","uid":"6bd3bfc5dd2d4ae8be2a2960f406a60f"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"7e7f28a9596e47c1acacf7ecbe5659e5","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"7e7f28a9596e47c1acacf7ecbe5659e5"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"7e7f28a9596e47c1acacf7ecbe5659e5"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}',
        '{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"cd92a4205a734ea4bf33b285fa230989"},"root":true,"title":"主节点","type":"start","uid":"cd92a4205a734ea4bf33b285fa230989"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"929751cae3a342a09a402af686610fe5","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"929751cae3a342a09a402af686610fe5"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"929751cae3a342a09a402af686610fe5"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}',
        '{"nodes":[{"figures":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MainMetricNode\",\"style\":\"left:30px;top:30px;width:360px;height:135px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"************","dn":"OS=1","dnName":"OSS1","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"isRoot":true,"kpiValue":"23.63","lowerThreshold":null,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","nodeId":"flow-diagram_N1","nodeUid":"cd92a4205a734ea4bf33b285fa230989","originalValue":"ip=************","podName":null,"predictionKpiValue":"40","predictionLowerThreshold":"40","predictionUpperThreshold":"40","resourceTypeKey":"OMS","resultType":null,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"%","upperThreshold":null}],"indicatorNum":0,"indicatorSelectType":0,"isRoot":null,"nodeName":"主节点","nodeType":null,"normalIndicatorNum":0,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"50ebf9beac23491f8835a6b5cddfbf2f","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":true,"status":null,"title":"主节点","type":"start","uid":"50ebf9beac23491f8835a6b5cddfbf2f"},{"figures":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","leaf":false,"location":null,"nodeParam":{"aggregateType":null,"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"eamExecutionNodeId":null,"errorIndicatorNum":0,"executionTime":null,"figure":"[{\"type\":\"MetricNode\",\"style\":\"left:30px;top:210px;width:260px;height:135px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":1,\"fillStyle\":\"#e8f7ff\"}}]","id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"abnormal":null,"checkedNetId":null,"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1(2)","eamNodeType":null,"hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"isRoot":false,"kpiValue":"2.00","lowerThreshold":null,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","nodeId":"flow-diagram_N2","nodeUid":"929751cae3a342a09a402af686610fe5","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","podName":null,"predictionKpiValue":"2.00","predictionLowerThreshold":"2.00","predictionUpperThreshold":"2.00","resourceTypeKey":"OMS","resultType":0,"targetExecutionUId":null,"targetNodeId":null,"taskId":4,"trainResult":null,"unit":"条","upperThreshold":null}],"indicatorNum":1,"indicatorSelectType":0,"isRoot":null,"nodeName":"节点1","nodeType":null,"normalIndicatorNum":1,"predictStatus":null,"sortNum":null,"status":null,"taskId":4,"trainStatus":null,"uid":"c9429bba50db4d8bb23deeea56e00753","updateTime":null,"warningIndicatorNum":0},"number":null,"relevance":null,"root":false,"status":null,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"c9429bba50db4d8bb23deeea56e00753"}],"paths":[{"condition":null,"figures":"[{\"type\": \"line\", \"context\": {\"strokeStyle\": \"#3F4D46\",\"lineWidth\": 2}}]","id":null,"relevance":null,"source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}],"predictTime":1688109507719,"predictType":"RUN"}'
       );

DROP TABLE IF EXISTS TBL_AIOPS_MULTIPLE_EXECUTION_RESULT;
CREATE TABLE TBL_AIOPS_MULTIPLE_EXECUTION_RESULT (
                                                     NODE_ID VARCHAR(255),
                                                     NODE_NAME VARCHAR(255),
                                                     EXECUTION_ID VARCHAR(255) NOT NULL,
                                                     START_TIME LONG,
                                                     TASK_ID INTEGER NOT NULL,
                                                     UPDATE_TIME LONG,
                                                     NODE_TYPE LONG,
                                                     EAM_EXECUTION_NODE_ID VARCHAR(255),
                                                     STATUS VARCHAR(256),
                                                     CREATE_USER VARCHAR(256),
                                                     INDICATOR_SELECT_TYPE LONG,
                                                     SORT_NUM INTEGER NOT NULL,
                                                     IS_ROOT BOOLEAN,
                                                     TRAIN_STATUS INTEGER,
                                                     PREDICT_STATUS INTEGER,
                                                     CAN_RETRAIN BOOLEAN,
                                                     IF_NET_CHANGE BOOLEAN,
                                                     HAS_MODEL BOOLEAN,
                                                     ERROR_INDICATOR_NUM INTEGER,
                                                     WARNING_INDICATOR_NUM INTEGER,
                                                     NORMAL_INDICATOR_NUM INTEGER,
                                                     INDICATOR_NUM INTEGER,
                                                     FIGURE CLOB,
                                                     CONSTRAINT TBL_AIOPS_MULTIPLE_EXECUTION_RESULT PRIMARY KEY (TASK_ID, EXECUTION_ID)
);

insert into TBL_AIOPS_MULTIPLE_EXECUTION_RESULT (NODE_ID, NODE_NAME, EXECUTION_ID, START_TIME, TASK_ID, UPDATE_TIME,
                                                 NODE_TYPE, EAM_EXECUTION_NODE_ID, STATUS, CREATE_USER, SORT_NUM,
                                                 IS_ROOT, TRAIN_STATUS, PREDICT_STATUS, CAN_RETRAIN, IF_NET_CHANGE,
                                                 ERROR_INDICATOR_NUM, WARNING_INDICATOR_NUM,
                                                 NORMAL_INDICATOR_NUM, INDICATOR_NUM, FIGURE)
values ('1', 'name', '1', 1635405671444, 3, 1635405671444, 1, '1', 'na', 'na', 1, false, 1, 7, true, true, 1, 1, 1, 1, null);

insert into TBL_AIOPS_MULTIPLE_EXECUTION_RESULT (NODE_ID, NODE_NAME, EXECUTION_ID, START_TIME, TASK_ID, UPDATE_TIME,
                                                 NODE_TYPE, EAM_EXECUTION_NODE_ID, STATUS, CREATE_USER, SORT_NUM,
                                                 IS_ROOT, TRAIN_STATUS, PREDICT_STATUS, CAN_RETRAIN, IF_NET_CHANGE,
                                                 ERROR_INDICATOR_NUM, WARNING_INDICATOR_NUM,
                                                 NORMAL_INDICATOR_NUM, INDICATOR_NUM, FIGURE)
values ('1', 'name', '1', 1635405671444, 4, 1635405671444, 1, '1', 'na', 'na', 1, false, 1, 7, true, true, 1, 1, 1, 1, null);
insert into TBL_AIOPS_MULTIPLE_EXECUTION_RESULT (NODE_ID, NODE_NAME, EXECUTION_ID, START_TIME, TASK_ID, UPDATE_TIME,
                                                 NODE_TYPE, EAM_EXECUTION_NODE_ID, STATUS, CREATE_USER, SORT_NUM,
                                                 IS_ROOT, TRAIN_STATUS, PREDICT_STATUS, CAN_RETRAIN, IF_NET_CHANGE,
                                                 ERROR_INDICATOR_NUM, WARNING_INDICATOR_NUM,
                                                 NORMAL_INDICATOR_NUM, INDICATOR_NUM, FIGURE)
values ('1', 'name', '1', 1635405671444, 5, 1635405671444, 1, '1', 'na', 'na', 1, false, 1, 7, true, true, 1, 1, 1, 1, null);
insert into TBL_AIOPS_MULTIPLE_EXECUTION_RESULT (NODE_ID, NODE_NAME, EXECUTION_ID, START_TIME, TASK_ID, UPDATE_TIME,
                                                 NODE_TYPE, EAM_EXECUTION_NODE_ID, STATUS, CREATE_USER, SORT_NUM,
                                                 IS_ROOT, TRAIN_STATUS, PREDICT_STATUS, CAN_RETRAIN, IF_NET_CHANGE,
                                                 ERROR_INDICATOR_NUM, WARNING_INDICATOR_NUM,
                                                 NORMAL_INDICATOR_NUM, INDICATOR_NUM, FIGURE)
values ('1', 'name', '1', 1635405671444, 6, 1635405671444, 1, '1', 'na', 'na', 1, false, 1, 7, true, true, 1, 1, 1, 1, null);

insert into TBL_AIOPS_MULTIPLE_EXECUTION_RESULT (NODE_ID, NODE_NAME, EXECUTION_ID, START_TIME, TASK_ID, UPDATE_TIME,
                                                 NODE_TYPE, EAM_EXECUTION_NODE_ID, STATUS, CREATE_USER, SORT_NUM,
                                                 IS_ROOT, TRAIN_STATUS, PREDICT_STATUS, CAN_RETRAIN, IF_NET_CHANGE,
                                                 ERROR_INDICATOR_NUM, WARNING_INDICATOR_NUM,
                                                 NORMAL_INDICATOR_NUM, INDICATOR_NUM, FIGURE)
values ('2', 'name', '1', 1635405671444, 2, 1635405671444, 1, '1', 'na', 'na', 1, false, 1, 7, true, true, 1, 1, 1, 1, null);

insert into TBL_AIOPS_MULTIPLE_EXECUTION_RESULT (NODE_ID, NODE_NAME, EXECUTION_ID, START_TIME, TASK_ID, UPDATE_TIME,
                                                 NODE_TYPE, EAM_EXECUTION_NODE_ID, STATUS, CREATE_USER, INDICATOR_SELECT_TYPE, SORT_NUM,
                                                 IS_ROOT, TRAIN_STATUS, PREDICT_STATUS, CAN_RETRAIN, IF_NET_CHANGE, HAS_MODEL,
                                                 ERROR_INDICATOR_NUM, WARNING_INDICATOR_NUM,
                                                 NORMAL_INDICATOR_NUM, INDICATOR_NUM, FIGURE)
values ('flow-diagram_N1','主节点','cd92a4205a734ea4bf33b285fa230989',1688109210559,8,null,0,null,'success',1,null,0,null,null,null,false,false,null,0,0,0,0,
        '[{"type":"MainMetricNode","style":"left:30px;top:30px;width:360px;height:135px;","context":{"strokeStyle":"#9fdfff","lineWidth":2,"fillStyle":"#9fdfff"},"options":{}}]');

insert into TBL_AIOPS_MULTIPLE_EXECUTION_RESULT (NODE_ID, NODE_NAME, EXECUTION_ID, START_TIME, TASK_ID, UPDATE_TIME,
                                                 NODE_TYPE, EAM_EXECUTION_NODE_ID, STATUS, CREATE_USER, INDICATOR_SELECT_TYPE, SORT_NUM,
                                                 IS_ROOT, TRAIN_STATUS, PREDICT_STATUS, CAN_RETRAIN, IF_NET_CHANGE, HAS_MODEL,
                                                 ERROR_INDICATOR_NUM, WARNING_INDICATOR_NUM,
                                                 NORMAL_INDICATOR_NUM, INDICATOR_NUM, FIGURE)
values ('flow-diagram_N2','节点1','929751cae3a342a09a402af686610fe5',1688109210559,8,1688109210645,0,null,'success',1,null,1,false,null,null,false,false,null,0,0,1,1,
        '[{"type":"MetricNode","style":"left:30px;top:210px;width:260px;height:135px;","options":{"radius":5},"context":{"strokeStyle":"#00aaff","lineWidth":1,"fillStyle":"#e8f7ff"}}]');


DROP TABLE IF EXISTS TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR;
CREATE TABLE TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (
                                                        TARGET_EXECUTION_UID VARCHAR(255),
                                                        TARGET_NODE_ID VARCHAR(255),
                                                        NODE_ID VARCHAR(255),
                                                        NODE_NAME VARCHAR(256),
                                                        EXECUTION_UID VARCHAR(255),
                                                        INDICATOR_ID VARCHAR(255) NOT NULL,
                                                        TASK_ID INTEGER NOT NULL,
                                                        ABNORMAL NUMBER(1),
                                                        DN VARCHAR(256),
                                                        MO_TYPE VARCHAR(256),
                                                        MEAS_UNIT_KEY VARCHAR(256),
                                                        MEAS_UNIT_NAME VARCHAR(256),
                                                        MEAS_TYPE_KEY VARCHAR(256),
                                                        INDEX_NAME VARCHAR(256),
                                                        DISPLAY_VALUE VARCHAR(8000),
                                                        ORIGINAL_VALUE VARCHAR(256),
                                                        UNIT VARCHAR(256),
                                                        DN_NAME VARCHAR(256),
                                                        CHECKED_NET_ID VARCHAR(256),
                                                        HAS_MEAS_OBJ VARCHAR(5),
                                                        INDEX_ID VARCHAR(256),
                                                        RESOURCE_TYPE_KEY VARCHAR(256),
                                                        KPI_VALUE DECIMAL(16,2),
                                                        PREDICTION_UPPER_THRESHOLD DECIMAL(16,2),
                                                        PREDICTION_LOWER_THRESHOLD DECIMAL(16,2),
                                                        PREDICTION_KPI_VALUE DECIMAL(16,2),
                                                        UPPER_THRESHOLD DECIMAL(16,2),
                                                        LOWER_THRESHOLD DECIMAL(16,2),
                                                        RESULT_TYPE INTEGER,
                                                        TRAIN_RESULT INTEGER,
                                                        IS_ROOT BOOLEAN,
                                                        POD_NAME VARCHAR(256),
                                                        CONSTRAINT TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR PRIMARY KEY (TASK_ID, INDICATOR_ID)
);


insert into TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (TARGET_EXECUTION_UID, TARGET_NODE_ID, NODE_ID, NODE_NAME, EXECUTION_UID, INDICATOR_ID, TASK_ID, ABNORMAL, DN, MO_TYPE,
                                                    MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID,
                                                    HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, KPI_VALUE, PREDICTION_UPPER_THRESHOLD, PREDICTION_LOWER_THRESHOLD,
                                                    PREDICTION_KPI_VALUE, UPPER_THRESHOLD, LOWER_THRESHOLD, RESULT_TYPE, TRAIN_RESULT, IS_ROOT)
values ('targetExecutionUid', '1', '1', '1', '1', '1', 1, 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', 5.8, 11, 1, 6, 10, 1, 1, 1, true);
insert into TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (TARGET_EXECUTION_UID, TARGET_NODE_ID, NODE_ID, NODE_NAME, EXECUTION_UID, INDICATOR_ID, TASK_ID, ABNORMAL, DN, MO_TYPE, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, KPI_VALUE, PREDICTION_UPPER_THRESHOLD, PREDICTION_LOWER_THRESHOLD, PREDICTION_KPI_VALUE, UPPER_THRESHOLD, LOWER_THRESHOLD, RESULT_TYPE, TRAIN_RESULT, IS_ROOT)
values ('targetExecutionUid', '1', '2', '1', '1', '2', 1, 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '5.4', '11', '1', '6', '10', '1', 1, 1, true);
insert into TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (TARGET_EXECUTION_UID, TARGET_NODE_ID, NODE_ID, NODE_NAME, EXECUTION_UID, INDICATOR_ID, TASK_ID, ABNORMAL, DN, MO_TYPE, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, KPI_VALUE, PREDICTION_UPPER_THRESHOLD, PREDICTION_LOWER_THRESHOLD, PREDICTION_KPI_VALUE, UPPER_THRESHOLD, LOWER_THRESHOLD, RESULT_TYPE, TRAIN_RESULT, IS_ROOT)
values ('targetExecutionUid', '1', '2', '1', '1', '2', 3, 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '5.4', '11', '1', '6', '10', '1', 1, 1, true);
insert into TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (TARGET_EXECUTION_UID, TARGET_NODE_ID, NODE_ID, NODE_NAME, EXECUTION_UID, INDICATOR_ID, TASK_ID, ABNORMAL, DN, MO_TYPE, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, KPI_VALUE, PREDICTION_UPPER_THRESHOLD, PREDICTION_LOWER_THRESHOLD, PREDICTION_KPI_VALUE, UPPER_THRESHOLD, LOWER_THRESHOLD, RESULT_TYPE, TRAIN_RESULT, IS_ROOT)
values ('targetExecutionUid', '1', '2', '1', '1', '2', 4, 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '5.4', '11', '1', '6', '10', '1', 1, 1, true);
insert into TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (TARGET_EXECUTION_UID, TARGET_NODE_ID, NODE_ID, NODE_NAME, EXECUTION_UID, INDICATOR_ID, TASK_ID, ABNORMAL, DN, MO_TYPE, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, KPI_VALUE, PREDICTION_UPPER_THRESHOLD, PREDICTION_LOWER_THRESHOLD, PREDICTION_KPI_VALUE, UPPER_THRESHOLD, LOWER_THRESHOLD, RESULT_TYPE, TRAIN_RESULT, IS_ROOT)
values ('targetExecutionUid', '1', '2', '1', '1', '2', 5, 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '5.4', '11', '1', '6', '10', '1', 1, 1, true);
insert into TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (TARGET_EXECUTION_UID, TARGET_NODE_ID, NODE_ID, NODE_NAME, EXECUTION_UID, INDICATOR_ID, TASK_ID, ABNORMAL, DN, MO_TYPE, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, KPI_VALUE, PREDICTION_UPPER_THRESHOLD, PREDICTION_LOWER_THRESHOLD, PREDICTION_KPI_VALUE, UPPER_THRESHOLD, LOWER_THRESHOLD, RESULT_TYPE, TRAIN_RESULT, IS_ROOT)
values ('targetExecutionUid', '1', '2', '1', '1', '2', 6, 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '5.4', '11', '1', '6', '10', '1', 1, 1, true);

insert into TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (TARGET_EXECUTION_UID, TARGET_NODE_ID, NODE_ID, NODE_NAME, EXECUTION_UID, INDICATOR_ID, TASK_ID, ABNORMAL, DN, MO_TYPE, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, KPI_VALUE, PREDICTION_UPPER_THRESHOLD, PREDICTION_LOWER_THRESHOLD, PREDICTION_KPI_VALUE, UPPER_THRESHOLD, LOWER_THRESHOLD, RESULT_TYPE, TRAIN_RESULT, IS_ROOT, POD_NAME)
values (null,null,'flow-diagram_N1',null,'cd92a4205a734ea4bf33b285fa230989','OS=1I2K_OSUsedCPURate************',8,null,'OS=1','OMS','I2K_OS','操作系统监控','UsedCPURate','CPU占用率(%)','************','ip=************','%','OSS1',null,true,'I2K_OS~~UsedCPURate','OMS',23.63,40,40,40,null,null,null,null,true,null);
insert into TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (TARGET_EXECUTION_UID, TARGET_NODE_ID, NODE_ID, NODE_NAME, EXECUTION_UID, INDICATOR_ID, TASK_ID, ABNORMAL, DN, MO_TYPE, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, KPI_VALUE, PREDICTION_UPPER_THRESHOLD, PREDICTION_LOWER_THRESHOLD, PREDICTION_KPI_VALUE, UPPER_THRESHOLD, LOWER_THRESHOLD, RESULT_TYPE, TRAIN_RESULT, IS_ROOT, POD_NAME)
values (null,null,'flow-diagram_N2',null,'929751cae3a342a09a402af686610fe5','OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task',8,null,'OS=1','OMS','I2K_Top_Task_Insert','性能任务数据量统计','one_min_count','每分钟数据量(条)','OMS,I2K_Task','mo_type=OMS,meas_unit_key=I2K_Task','条','OSS1',null,true,'I2K_Top_Task_Insert~~one_min_count','OMS',2.00,2.00,2.00,2.00,null,null,0,null,false,null);
insert into TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (TARGET_EXECUTION_UID, TARGET_NODE_ID, NODE_ID, NODE_NAME, EXECUTION_UID, INDICATOR_ID, TASK_ID, ABNORMAL, DN, MO_TYPE, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, RESOURCE_TYPE_KEY, KPI_VALUE, PREDICTION_UPPER_THRESHOLD, PREDICTION_LOWER_THRESHOLD, PREDICTION_KPI_VALUE, UPPER_THRESHOLD, LOWER_THRESHOLD, RESULT_TYPE, TRAIN_RESULT, IS_ROOT, POD_NAME)
values (null,null,'flow-diagram_N2',null,'929751cae3a342a09a402af686610fe5','OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG',8,null,'OS=1','OMS','I2K_Top_Task_Insert','性能任务数据量统计','one_min_count','每分钟数据量(条)','OMS,I2K_LOG','mo_type=OMS,meas_unit_key=I2K_LOG','条','OSS1',null,true,'I2K_Top_Task_Insert~~one_min_count','OMS',null,null,null,null,null,null,null,null,false,null);


DROP TABLE IF EXISTS TBL_AIOPS_MULTIPLE_DEPENDENCE_INDICATOR_MAP;
CREATE TABLE TBL_AIOPS_MULTIPLE_DEPENDENCE_INDICATOR_MAP (
                                                             NODE_ID VARCHAR(255),
                                                             EXECUTION_ID VARCHAR(255) NOT NULL,
                                                             TASK_ID INTEGER NOT NULL,
                                                             DEPENDENCE_INDICATOR_ID VARCHAR(255) NOT NULL,
                                                             CONSTRAINT TBL_AIOPS_MULTIPLE_DEPENDENCE_INDICATOR_MAP PRIMARY KEY (TASK_ID, EXECUTION_ID, DEPENDENCE_INDICATOR_ID)
);

insert into TBL_AIOPS_MULTIPLE_DEPENDENCE_INDICATOR_MAP (NODE_ID, EXECUTION_ID, TASK_ID, DEPENDENCE_INDICATOR_ID)
values ('2', '1', 1, '1');

DROP TABLE IF EXISTS TBL_AIOPS_CAPACITY_HISTORY_RESULT;
CREATE TABLE TBL_AIOPS_CAPACITY_HISTORY_RESULT (
                                                   ID VARCHAR(255) NOT NULL,
                                                   TASK_ID INTEGER NOT NULL,
                                                   PREDICT_TYPE VARCHAR(256),
                                                   UPDATE_TIME BIGINT NOT NULL,
                                                   RELATION_TREE CLOB,
                                                   MAIN_KPI_VALUE VARCHAR(256),
                                                   MAIN_TIME_RANGE VARCHAR(256),
                                                   CAPACITY_OCCUR_TIME BIGINT,
                                                   CAPACITY_PREDICT_STATUS VARCHAR(256),
                                                   CONSTRAINT TBL_AIOPS_CAPACITY_HISTORY_RESULT PRIMARY KEY (ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO;
CREATE TABLE TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO (
                                                        UNITED_ID VARCHAR(256) NOT NULL,
                                                        TASK_ID INTEGER NOT NULL,
                                                        INDICATOR_ID VARCHAR(1024) NOT NULL,
                                                        DN VARCHAR(256),
                                                        DN_NAME VARCHAR(256),
                                                        IF_IGNORE BOOLEAN,
                                                        CLUSTER_NAME VARCHAR(256),
                                                        SITE_ID VARCHAR(256),
                                                        MO_TYPE VARCHAR(256),
                                                        MEAS_UNIT_KEY VARCHAR(256),
                                                        MEAS_TYPE_KEY VARCHAR(256),
                                                        MEAS_UNIT_NAME VARCHAR(256),
                                                        INDEX_ID VARCHAR(256),
                                                        INDEX_NAME VARCHAR(256),
                                                        DISPLAY_VALUE VARCHAR(256),
                                                        ORIGINAL_VALUE VARCHAR(256),
                                                        UNIT VARCHAR(256),
                                                        HAS_MEAS_OBJ VARCHAR(256),
                                                        CHECKED_NET_ID VARCHAR(256),
                                                        AGGREGATION_STATUS INTEGER,
                                                        CONSTRAINT TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO PRIMARY KEY (UNITED_ID,TASK_ID, INDICATOR_ID)
);

DROP TABLE IF EXISTS TBL_AIOPS_ANALYSIS_TASK;
CREATE TABLE TBL_AIOPS_ANALYSIS_TASK (
                                         TASK_ID INTEGER AUTO_INCREMENT NOT NULL,
                                         TASK_DESC VARCHAR(2048),
                                         TASK_NAME VARCHAR(256) NOT NULL,
                                         SITUATION_ID BIGINT,
                                         TASK_TYPE INTEGER NOT NULL,
                                         PERIODIC_TYPE INTEGER,
                                         PREDICT_CRON VARCHAR(256),
                                         TRAIN_CRON VARCHAR(256),
                                         SOLUTION_ID VARCHAR(256),
                                         SOLUTION_NAME VARCHAR(256),
                                         TRAIN_STATUS INTEGER,
                                         START_STATUS INTEGER,
                                         REPORT_ALARM INTEGER,
                                         ALARM_TYPE INTEGER,
                                         BEGIN_TIME BIGINT,
                                         END_TIME BIGINT,
                                         INDICATOR_SELECT_TYPE INTEGER,
                                         UPDATE_TIME BIGINT NOT NULL,
                                         ALGORITHM_MODEL_ID VARCHAR(256),
                                         ALGORITHM_MODEL_NAME VARCHAR(256),
                                         ALGORITHM_PARAM CLOB,
                                         DATASOURCE_ID VARCHAR(256),
                                         PATH VARCHAR(256),
                                         TASK_DETAIL VARCHAR(1024),
                                         ADDITION VARCHAR(256),
                                         USER_ID VARCHAR(256) NOT NULL,
                                         LAST_MODIFY_USER_ID VARCHAR(256),
                                         LAST_EXECUTOR_USER_ID VARCHAR(256),
                                         ALARM_TYPE_LIST CLOB,
                                         ALARM_SELECT_TYPE INTEGER,
                                         ALARM_SELECT_LIST CLOB,
                                         ALARM_SELECT_GROUP_LIST CLOB,
                                         INDICATOR_PREDICT_NUM BIGINT,
                                         INDICATOR_TRAIN_NUM BIGINT,
                                         ALARM_PREDICT_TIME VARCHAR(50),
                                         ALARM_TRAIN_TIME VARCHAR(50),
                                         LOG_PREDICT_TIME VARCHAR(50),
                                         LOG_TRAIN_TIME VARCHAR(50),
                                         REPORT_ALARM_NAME VARCHAR(256),
                                         REPORT_ALARM_ID VARCHAR(256),
                                         IMPORT_TASK_STATUS BOOLEAN,
                                         GROUP_LEVEL INTEGER,
                                         TRAIN_ADDRESS VARCHAR(256),
                                         PREDICT_ADDRESS VARCHAR(256),
                                         INDICATOR_PREDICT_SCENARIO INTEGER,
                                         AGGREGATE_MACHINE_GROUP_LIST VARCHAR(1024),
                                         CONSTRAINT TBL_AIOPS_ANALYSIS_TASK PRIMARY KEY (TASK_ID)
);

insert into TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL)
values (1, '1', '1', 1, 5, 1, '1', '1', '1', '1', 1, 1, null, 1, 1, 1, 1, 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', 1, '1', '1', 1, 1, '1', '1', '1', '1', '1', '1', false, 1);
insert into TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL)
values (2, '1', '2', 1, 5, 1, '1', '1', '1', '1', 1, 1, null, 1, 1, 1, 1, 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', 1, '1', '1', 1, 1, '1', '1', '1', '1', '1', '1', false, 1);
insert into TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL)
values (3, '1', '最大用户量监控1', 1, 5, 7, '1', '1', '1', '1', 1, 1, null, 1, 1, 1, 1, 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', 1, '1', '1', 1, 1, '1', '1', '1', '1', '1', '1', false, 1);
insert into TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL)
values (4, '1', '最大用户量监控', 1, 5, 7, '1', '1', '1', '1', 1, 1, null, 1, 1, 1, 1, 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', 1, '1', '1', 1, 1, '1', '1', '1', '1', '1', '1', false, 1);
insert into TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL)
values (5, '1', '最大用户量监控', 1, 5, 7, '1', '1', '1', '1', 1, 1, null, 1, 1, 1, 1, 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', 1, '1', '1', 1, 1, '1', '1', '1', '1', '1', '1', false, 1);
insert into TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL, INDICATOR_PREDICT_SCENARIO, AGGREGATE_MACHINE_GROUP_LIST)
values (6, '1', '最大用户量监控', 1, 5, 7, '1', '1', '1', '1', 1, 1, null, 1, 1, 1, 1, 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', 1, '1', '1', 1, 1, '1', '1', '1', '1', '1', '1', false, 1, 2, '[{"aggregateMoList":[{"dn":"dn","dnName":"dnName"}],"clusterName":"shuangji"}]');

insert into TBL_AIOPS_ANALYSIS_TASK (TASK_ID, TASK_DESC, TASK_NAME, SITUATION_ID, TASK_TYPE, PERIODIC_TYPE, PREDICT_CRON, TRAIN_CRON, SOLUTION_ID, SOLUTION_NAME, TRAIN_STATUS, START_STATUS, REPORT_ALARM, ALARM_TYPE, BEGIN_TIME, END_TIME, INDICATOR_SELECT_TYPE, UPDATE_TIME, ALGORITHM_MODEL_ID, ALGORITHM_MODEL_NAME, ALGORITHM_PARAM, DATASOURCE_ID, PATH, TASK_DETAIL, ADDITION, USER_ID, LAST_MODIFY_USER_ID, LAST_EXECUTOR_USER_ID, ALARM_TYPE_LIST, ALARM_SELECT_TYPE, ALARM_SELECT_LIST, ALARM_SELECT_GROUP_LIST, INDICATOR_PREDICT_NUM, INDICATOR_TRAIN_NUM, ALARM_PREDICT_TIME, ALARM_TRAIN_TIME, LOG_PREDICT_TIME, LOG_TRAIN_TIME, REPORT_ALARM_NAME, REPORT_ALARM_ID, IMPORT_TASK_STATUS, GROUP_LEVEL, INDICATOR_PREDICT_SCENARIO, AGGREGATE_MACHINE_GROUP_LIST)
values (7, '1', '最大用户量监控', 1, 5, 7, '1', '1', '1', '1', 3, 1, null, 1, 1, 1, 1, 1, '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', '1', 1, '1', '1', 1, 1, '1', '1', '1', '1', '1', '1', false, 1, 2, '[{"aggregateMoList":[{"dn":"dn","dnName":"dnName"}],"clusterName":"shuangji"}]');


insert into TBL_AIOPS_TASK_EXECUTION_RESULT (TASK_ID, START_TIME, END_TIME, STATUS, TASK_PERIOD_TYPE)
values (1, 1, 1, '1', 'Finished');

insert into TBL_AIOPS_TASK_EXECUTION_RESULT (TASK_ID, START_TIME, END_TIME, STATUS, TASK_PERIOD_TYPE)
values (7, 1, 1, '1', 'Finished');

insert into TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO (UNITED_ID, TASK_ID, INDICATOR_ID, DN, DN_NAME, IF_IGNORE, CLUSTER_NAME, SITE_ID, MO_TYPE, MEAS_UNIT_KEY, MEAS_TYPE_KEY, MEAS_UNIT_NAME, INDEX_ID, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGREGATION_STATUS)
values ('flow-diagram_N2',7,'OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG','OS=1','OSS',true,null,null,'OMS','I2K_Top_Task_Insert','one_min_count','性能任务数据量统计','I2K_Top_Task_Insert~~one_min_count','每分钟数据量(条)','OMS,I2K_LOG','mo_type=OMS,meas_unit_key=I2K_LOG','条',true, null, 0);
insert into TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO (UNITED_ID, TASK_ID, INDICATOR_ID, DN, DN_NAME, IF_IGNORE, CLUSTER_NAME, SITE_ID, MO_TYPE, MEAS_UNIT_KEY, MEAS_TYPE_KEY, MEAS_UNIT_NAME, INDEX_ID, INDEX_NAME, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, HAS_MEAS_OBJ,  CHECKED_NET_ID, AGGREGATION_STATUS)
values ('flow-diagram_N2',7,'OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task','OS=1','OSS',false,null,null,'OMS','I2K_Top_Task_Insert','one_min_count','性能任务数据量统计','I2K_Top_Task_Insert~~one_min_count','每分钟数据量(条)','OMS,I2K_Task','mo_type=OMS,meas_unit_key=I2K_Task','条',true, null, 0);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_INDICATOR;
CREATE TABLE TBL_AIOPS_TASK_INDICATOR (
                                          INDICATOR_ID VARCHAR(1024) NOT NULL,
                                          TASK_ID INTEGER NOT NULL,
                                          ABNORMAL NUMBER(1),
                                          MEAS_UNIT_KEY VARCHAR(256),
                                          MEAS_UNIT_NAME VARCHAR(600),
                                          MEAS_TYPE_KEY VARCHAR(256),
                                          DN VARCHAR(256),
                                          INDEX_NAME VARCHAR(600),
                                          MO_TYPE VARCHAR(256),
                                          DISPLAY_VALUE VARCHAR(8000),
                                          ORIGINAL_VALUE VARCHAR(600),
                                          UNIT VARCHAR(256),
                                          DN_NAME VARCHAR(256),
                                          CHECKED_NET_ID VARCHAR(2048),
                                          HAS_MEAS_OBJ VARCHAR(5),
                                          INDEX_ID VARCHAR(256),
                                          RESOURCE_TYPE_KEY VARCHAR(256),
                                          PQL VARCHAR(8000),
                                          SOLUTION_ID VARCHAR(256),
                                          GROUP_ID VARCHAR(256),
                                          RESOURCE_GROUP VARCHAR(256),
                                          HISTORY_INDICATOR_ID INTEGER,
                                          HISTORY_INDICATOR_NAME VARCHAR(1800)
);
INSERT INTO TBL_AIOPS_TASK_INDICATOR (INDICATOR_ID, TASK_ID, ABNORMAL, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, DN, INDEX_NAME, MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT) VALUES ('dnmeasUnitKeymeasTypeKeydisplayValue', 6, null, 'I2K_FMMGR', '告警监控', 'FMAlarmDelayMin', 'OS=1', '告警最小延时(毫秒)', 'OMS', null, null, '毫秒');
INSERT INTO TBL_AIOPS_TASK_INDICATOR (INDICATOR_ID, TASK_ID, ABNORMAL, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, DN, INDEX_NAME, MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT) VALUES ('shuangjimeasUnitKeymeasTypeKeydisplayValue', 6, null, 'I2K_FMMGR', '告警监控', 'FMAlarmDelayMin', 'OS=1', '告警最小延时(毫秒)', 'OMS', null, null, '毫秒');

DROP TABLE IF EXISTS TBL_AIOPS_CAPACITY_CORRELATION_ALARM;
CREATE TABLE TBL_AIOPS_CAPACITY_CORRELATION_ALARM (
     TASK_ID INTEGER NOT NULL,
     INDICATOR_ID VARCHAR(256) NOT NULL,
     PUSH_ALARM_JSON CLOB,
     CONSTRAINT TBL_AIOPS_CAPACITY_CORRELATION_ALARM PRIMARY KEY (TASK_ID,INDICATOR_ID)
);
insert into TBL_AIOPS_CAPACITY_CORRELATION_ALARM (TASK_ID, INDICATOR_ID, PUSH_ALARM_JSON) VALUES ('1', '2', '3');

DROP TABLE IF EXISTS TBL_AIOPS_CAPACITY_HISTORY_RESULT;
CREATE TABLE TBL_AIOPS_CAPACITY_HISTORY_RESULT (
                                                   ID VARCHAR(255) NOT NULL,
                                                   TASK_ID INTEGER NOT NULL,
                                                   PREDICT_TYPE VARCHAR(256),
                                                   UPDATE_TIME BIGINT,
                                                   RELATION_TREE CLOB,
                                                   MAIN_KPI_VALUE VARCHAR(256),
                                                   MAIN_TIME_RANGE VARCHAR(256),
                                                   CAPACITY_OCCUR_TIME BIGINT,
                                                   CAPACITY_PREDICT_STATUS VARCHAR(256),
                                                   CONSTRAINT TBL_AIOPS_CAPACITY_HISTORY_RESULT PRIMARY KEY (ID)
);
insert into TBL_AIOPS_CAPACITY_HISTORY_RESULT (ID, TASK_ID, PREDICT_TYPE, UPDATE_TIME, RELATION_TREE, MAIN_KPI_VALUE, MAIN_TIME_RANGE, CAPACITY_OCCUR_TIME, CAPACITY_PREDICT_STATUS)
values (1, 1, 'REVERSE', 1, '{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"cd92a4205a734ea4bf33b285fa230989"},"root":true,"title":"主节点","type":"start","uid":"cd92a4205a734ea4bf33b285fa230989"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"929751cae3a342a09a402af686610fe5","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"929751cae3a342a09a402af686610fe5"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"929751cae3a342a09a402af686610fe5"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}', null, null, null, 'Finished');
insert into TBL_AIOPS_CAPACITY_HISTORY_RESULT (ID, TASK_ID, PREDICT_TYPE, UPDATE_TIME, RELATION_TREE, MAIN_KPI_VALUE, MAIN_TIME_RANGE, CAPACITY_OCCUR_TIME, CAPACITY_PREDICT_STATUS)
values ('aa', 6, null, 1, '', null, null, null, 'Finished');
insert into TBL_AIOPS_CAPACITY_HISTORY_RESULT (ID, TASK_ID, PREDICT_TYPE, UPDATE_TIME, RELATION_TREE, MAIN_KPI_VALUE, MAIN_TIME_RANGE, CAPACITY_OCCUR_TIME, CAPACITY_PREDICT_STATUS)
values (2, 1, 'RUN', 1, '{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"cd92a4205a734ea4bf33b285fa230989"},"root":true,"title":"主节点","type":"start","uid":"cd92a4205a734ea4bf33b285fa230989"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"929751cae3a342a09a402af686610fe5","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"929751cae3a342a09a402af686610fe5"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"929751cae3a342a09a402af686610fe5"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}', '30', null, null, 'Finished');
insert into TBL_AIOPS_CAPACITY_HISTORY_RESULT (ID, TASK_ID, PREDICT_TYPE, UPDATE_TIME, RELATION_TREE, MAIN_KPI_VALUE, MAIN_TIME_RANGE, CAPACITY_OCCUR_TIME, CAPACITY_PREDICT_STATUS)
values (3, 1, 'RUN', 1, '{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"cd92a4205a734ea4bf33b285fa230989"},"root":true,"title":"主节点","type":"start","uid":"cd92a4205a734ea4bf33b285fa230989"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"929751cae3a342a09a402af686610fe5","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"929751cae3a342a09a402af686610fe5"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"929751cae3a342a09a402af686610fe5"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}', '30', '1|DAY', null, 'Finished');
insert into TBL_AIOPS_CAPACITY_HISTORY_RESULT (ID, TASK_ID, PREDICT_TYPE, UPDATE_TIME, RELATION_TREE, MAIN_KPI_VALUE, MAIN_TIME_RANGE, CAPACITY_OCCUR_TIME, CAPACITY_PREDICT_STATUS)
values (4, 1, 'RUN', 1, '{"nodes":[{"figures":"[{\"type\":\"round\",\"style\":\"left:30px;top:30px;width:50px;height:50px;\",\"context\":{\"strokeStyle\":\"#9fdfff\",\"lineWidth\":2,\"fillStyle\":\"#9fdfff\"},\"options\":{}}]","id":"flow-diagram_N1","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[],"id":"flow-diagram_N1","ifNetChange":false,"indicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","taskId":4,"unit":"%"}],"indicatorSelectType":0,"nodeName":"主节点","taskId":4,"uid":"cd92a4205a734ea4bf33b285fa230989"},"root":true,"title":"主节点","type":"start","uid":"cd92a4205a734ea4bf33b285fa230989"},{"figures":"[{\"type\":\"roundrect\",\"style\":\"left:30px;top:210px;width:120px;height:35px;\",\"options\":{\"radius\":5},\"context\":{\"strokeStyle\":\"#ba8fff\",\"lineWidth\":1,\"fillStyle\":\"#f0e7ff\"}}]","id":"flow-diagram_N2","leaf":false,"nodeParam":{"canRetrain":false,"createUser":"1","dependenceIndicatorList":[{"displayValue":"************","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_OS~~UsedCPURate","indexName":"CPU占用率(%)","indicatorId":"OS=1I2K_OSUsedCPURate************","isAggregate":false,"measTypeKey":"UsedCPURate","measUnitKey":"I2K_OS","measUnitName":"操作系统监控","moType":"OMS","originalValue":"ip=************","resourceTypeKey":"OMS","targetExecutionUId":"929751cae3a342a09a402af686610fe5","targetNodeId":"flow-diagram_N2","taskId":4,"unit":"%"}],"id":"flow-diagram_N2","ifNetChange":false,"indicatorList":[{"displayValue":"OMS,I2K_Task","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_Task","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_Task","resourceTypeKey":"OMS","unit":"条"},{"displayValue":"OMS,I2K_LOG","dn":"OS=1","dnName":"OSS1","hasMeasObj":"true","indexId":"I2K_Top_Task_Insert~~one_min_count","indexName":"每分钟数据量(条)","indicatorId":"OS=1I2K_Top_Task_Insertone_min_countOMSI2K_LOG","isAggregate":true,"measTypeKey":"one_min_count","measUnitKey":"I2K_Top_Task_Insert","measUnitName":"性能任务数据量统计","moType":"OMS","originalValue":"mo_type=OMS,meas_unit_key=I2K_LOG","resourceTypeKey":"OMS","unit":"条"}],"indicatorSelectType":0,"nodeName":"节点1","taskId":4,"uid":"929751cae3a342a09a402af686610fe5"},"root":false,"title":"节点1","type":"HighAvailable_Common_KPI","uid":"929751cae3a342a09a402af686610fe5"}],"paths":[{"figures":"{\"edge\":[{\"type\":\"line\",\"context\":{\"strokeStyle\":\"#00aaff\",\"lineWidth\":2}}]}","source":"flow-diagram_N1","sourceAnchor":5,"target":"flow-diagram_N2","targetAnchor":1}]}', '36', '1|DAY', null, 'Process');

