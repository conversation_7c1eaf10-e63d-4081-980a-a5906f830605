DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_COMMON_MODEL;
CREATE TABLE TBL_TOPO_BUSINESS_COMMON_MODEL
(
    M<PERSON><PERSON>_ID      VARCHAR(256) NOT NULL,
    PARENT_MODEL_ID      VARCHAR(256) NULL,
    MODEL_NAME    VARCHAR(256) NOT NULL,
    M<PERSON>EL_TYPE    INT          NOT NULL,
    VERSION       VARCHAR(256) NOT NULL,
    HAS_MODIFIED  BOOLEAN      NOT NULL,
    PRODUCT_NAME  VARCHAR(256),
    SOLUTION_NAME VARCHAR(256) NOT NULL,
    TEMPLATE_TYPE INT          NOT NULL,
    CONSTRAINT TBL_TOPO_BUSINESS_COMMON_MODEL PRIMARY KEY (MODEL_ID)
);


INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, <PERSON><PERSON><PERSON>_TYPE, VER<PERSON><PERSON>, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('0_cbs', 'cbs', 0, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_REPDB', 'REPDB', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_DataIntegray', 'DataIntegray', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_uvcdb', 'uvcdb', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_tmp', 'tmp', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_vmp', 'vmp', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_vcp', 'vcp', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_InternalAdapter', 'InternalAdapter', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_see', 'see', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_AccessFacade', 'AccessFacade', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_adapterapp', 'adapterapp', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_cgw', 'cgw', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_NSLB', 'NSLB', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_cdfgmdb', 'cdfgmdb', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_adaptergmdb', 'adaptergmdb', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_cbpgmdb', 'cbpgmdb', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Notification', 'Notification', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_ReRating', 'ReRating', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_CDFAPP', 'CDFAPP', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_RecurringRating', 'RecurringRating', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_BizMngCharging', 'BizMngCharging', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_OnlineCharging', 'OnlineCharging', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_ConvergedCharging', 'ConvergedCharging', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_ChargingCache', 'ChargingCache', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_BillShareDB', 'BillShareDB', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_edrdb', 'edrdb', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_billdb', 'billdb', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_usrdb', 'usrdb', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_bmpdb', 'bmpdb', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_AR', 'AR', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_CAS', 'CAS', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_UPC', 'UPC', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_CDRProcess', 'CDRProcess', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_BM', 'BM', 5, 'XX.2024', false, 'cbs', 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('4_bdi', 'bdi', 4, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('4_UVC', 'UVC', 4, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('4_access-type', 'access-type', 4, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('4_real-time-business-class', 'real-time-business-class', 4, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('4_management-and-payment', 'management-and-payment', 4, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('2_siteGroup', 'siteGroup', 2, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_billdb', 'billdb', 7, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_podconvergedcharging', 'podconvergedcharging', 7, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_podadapter', 'podadapter', 7, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('8_vm', 'vm', 8, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('1_Manager business', 'Manager business', 1, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('1_4G GPRS', '4G GPRS', 1, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('1_4G Voice', '4G Voice', 1, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('1_CRM', 'CRM', 1, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_REPDB', 'REPDB', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_DataIntegray', 'DataIntegray', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_InternalAdapter', 'InternalAdapter', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_uvcdb', 'uvcdb', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_tmp', 'tmp', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_vmp', 'vmp', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_vcp', 'vcp', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_see', 'see', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_AccessFacade', 'AccessFacade', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_cgw', 'cgw', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_NSLB', 'NSLB', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_cdfgmdb', 'cdfgmdb', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_adaptergmdb', 'adaptergmdb', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_cbpgmdb', 'cbpgmdb', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Notification', 'Notification', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_ReRating', 'ReRating', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_CDFAPP', 'CDFAPP', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_RecurringRating', 'RecurringRating', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_BizMngCharging', 'BizMngCharging', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_OnlineCharging', 'OnlineCharging', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_ConvergedCharging', 'ConvergedCharging', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_ChargingCache', 'ChargingCache', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_BillShareDB', 'BillShareDB', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_edrdb', 'edrdb', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_billdb', 'billdb', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_usrdb', 'usrdb', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_bmpdb', 'bmpdb', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_CAS', 'CAS', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_UPC', 'UPC', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_CDRProcess', 'CDRProcess', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_BM', 'BM', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_AR', 'AR', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_adapterapp', 'adapterapp', 6, 'XX.2024', false, null, 'cbs', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('3_site', 'site', 3, 'XX.2024', false, null, 'cbs', 0);



DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR;
CREATE TABLE TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR
(
    MODEL_ID          VARCHAR(256) NOT NULL,
    VERSION           VARCHAR(256) NOT NULL,
    STATIC_ATTR_NAME  VARCHAR(256) NOT NULL,
    STATIC_ATTR_TYPE  VARCHAR(256) NOT NULL,
    STATIC_ATTR_VALUE VARCHAR(4096) NOT NULL,
    CONSTRAINT TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR PRIMARY KEY (MODEL_ID, STATIC_ATTR_NAME)
);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('0_MM_MM', null, 'MM', 0, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('101_API_MM', null, 'API', 101, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('101_Portal_MM', null, 'Portal', 101, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('101_SFTP_MM', null, 'SFTP', 101, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('101_STK_MM', null, 'STK', 101, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('101_USSD_MM', null, 'USSD', 101, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('10_container_common_model_MM', null, 'container_common_model', 10, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('1_CBS_MM', null, 'CBS', 1, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('1_KCB_MM', null, 'KCB', 1, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('1_M-Pesa APP_MM', null, 'M-Pesa APP', 1, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('1_Payment AGG_MM', null, 'Payment AGG', 1, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('1_SMSC_MM', null, 'SMSC', 1, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('1_Tibco_MM', null, 'Tibco', 1, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('2_stripeGroup_MM', null, 'stripeGroup', 2, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('3_site_MM', null, 'site', 3, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('401_management group_MM', null, 'management group', 401, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('4_Containerized Access Group_MM', null, 'Containerized Access Group', 4, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('4_DB Group_MM', null, 'DB Group', 4, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('4_Platform Group_MM', null, 'Platform Group', 4, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('4_Real-time Service Group_MM', null, 'Real-time Service Group', 4, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('4_Report Group_MM', null, 'Report Group', 4, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('4_System Scheduled Task Group_MM', null, 'System Scheduled Task Group', 4, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('501_CloudDR Arbitration_MM', null, 'CloudDR Arbitration', 501, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('501_CloudDR_MM', null, 'CloudDR', 501, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('501_DF_MM', null, 'DF', 501, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('501_DV_MM', null, 'DV', 501, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('501_DepCloud_MM', null, 'DepCloud', 501, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('501_OM Portal_MM', null, 'OM Portal', 501, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('501_Venus_MM', null, 'Venus', 501, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_API Fabric Access_MM', null, 'API Fabric Access', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_AccCenter_MM', null, 'AccCenter', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Acctrans_MM', null, 'Acctrans', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Audit Log Service_MM', null, 'Audit Log Service', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_BDI_MM', null, 'BDI', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Bulk Center_MM', null, 'Bulk Center', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_CAS_MM', null, 'CAS', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Clear Recon Center_MM', null, 'Clear Recon Center', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Config Portal_MM', null, 'Config Portal', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Customer Center_MM', null, 'Customer Center', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_DCC_MM', null, 'DCC', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_DSF Gateway_MM', null, 'DSF Gateway', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Data SFTP_MM', null, 'Data SFTP', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_DataSync_MM', null, 'DataSync', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Fin Asset Center_MM', null, 'Fin Asset Center', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_GaussDB_MM', null, 'GaussDB', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_History Data Archive Center_MM', null, 'History Data Archive Center', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_ISA_MM', null, 'ISA', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Identity Business_MM', null, 'Identity Business', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Identity Center_MM', null, 'Identity Center', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_JetMQ_MM', null, 'JetMQ', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Kafka_MM', null, 'Kafka', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_License Center_MM', null, 'License Center', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_NC_MM', null, 'NC', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_NFS Server_MM', null, 'NFS Server', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_NSLB_MM', null, 'NSLB', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_OM Server_MM', null, 'OM Server', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Order Query Center_MM', null, 'Order Query Center', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Organization Center_MM', null, 'Organization Center', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Organization Portal Server_MM', null, 'Organization Portal Server', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Redis_MM', null, 'Redis', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_SP Portal Server_MM', null, 'SP Portal Server', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Settlement Center_MM', null, 'Settlement Center', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_SuperGateway Server_MM', null, 'SuperGateway Server', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_SuperGateway_MM', null, 'SuperGateway', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Task Job Center_MM', null, 'Task Job Center', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Transaction Center_MM', null, 'Transaction Center', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_Transfer Business_MM', null, 'Transfer Business', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_WorkFlow Center_MM', null, 'WorkFlow Center', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_ZK Registry_MM', null, 'ZK Registry', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('5_ZK_MM', null, 'ZK', 5, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('601_CloudDR Arbitration_MM', null, 'CloudDR Arbitration', 601, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('601_CloudDR_MM', null, 'CloudDR', 601, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('601_DF_MM', null, 'DF', 601, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('601_DV_MM', null, 'DV', 601, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('601_DepCloud_MM', null, 'DepCloud', 601, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('601_OM Portal_MM', null, 'OM Portal', 601, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('601_Venus_MM', null, 'Venus', 601, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_API Fabric Access_MM', '5_API Fabric Access_MM', 'API Fabric Access', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_AccCenter_MM', '5_AccCenter_MM', 'AccCenter', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Acctrans_MM', '5_Acctrans_MM', 'Acctrans', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Audit Log Service_MM', '5_Audit Log Service_MM', 'Audit Log Service', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_BDI_MM', '5_BDI_MM', 'BDI', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Bulk Center_MM', '5_Bulk Center_MM', 'Bulk Center', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_CAS_MM', '5_CAS_MM', 'CAS', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Clear Recon Center_MM', '5_Clear Recon Center_MM', 'Clear Recon Center', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Config Portal_MM', '5_Config Portal_MM', 'Config Portal', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Customer Center_MM', '5_Customer Center_MM', 'Customer Center', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_DCC_MM', '5_DCC_MM', 'DCC', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_DSF Gateway_MM', '5_DSF Gateway_MM', 'DSF Gateway', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Data SFTP_MM', '5_Data SFTP_MM', 'Data SFTP', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_DataSync_MM', '5_DataSync_MM', 'DataSync', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Fin Asset Center_MM', '5_Fin Asset Center_MM', 'Fin Asset Center', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_GaussDB_MM', '5_GaussDB_MM', 'GaussDB', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_History Data Archive Center_MM', '5_History Data Archive Center_MM', 'History Data Archive Center', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_ISA_MM', '5_ISA_MM', 'ISA', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Identity Business_MM', '5_Identity Business_MM', 'Identity Business', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Identity Center_MM', '5_Identity Center_MM', 'Identity Center', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_JetMQ_MM', '5_JetMQ_MM', 'JetMQ', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Kafka_MM', '5_Kafka_MM', 'Kafka', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_License Center_MM', '5_License Center_MM', 'License Center', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_NC_MM', '5_NC_MM', 'NC', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_NFS Server_MM', '5_NFS Server_MM', 'NFS Server', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_NSLB_MM', '5_NSLB_MM', 'NSLB', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_OM Server_MM', '5_OM Server_MM', 'OM Server', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Order Query Center_MM', '5_Order Query Center_MM', 'Order Query Center', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Organization Center_MM', '5_Organization Center_MM', 'Organization Center', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Organization Portal Server_MM', '5_Organization Portal Server_MM', 'Organization Portal Server', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Redis_MM', '5_Redis_MM', 'Redis', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_SP Portal Server_MM', '5_SP Portal Server_MM', 'SP Portal Server', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Settlement Center_MM', '5_Settlement Center_MM', 'Settlement Center', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_SuperGateway Server_MM', '5_SuperGateway Server_MM', 'SuperGateway Server', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_SuperGateway_MM', '5_SuperGateway_MM', 'SuperGateway', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Task Job Center_MM', '5_Task Job Center_MM', 'Task Job Center', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Transaction Center_MM', '5_Transaction Center_MM', 'Transaction Center', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_Transfer Business_MM', '5_Transfer Business_MM', 'Transfer Business', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_WorkFlow Center_MM', '5_WorkFlow Center_MM', 'WorkFlow Center', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_ZK Registry_MM', '5_ZK Registry_MM', 'ZK Registry', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('6_ZK_MM', '5_ZK_MM', 'ZK', 6, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_API Fabric Access_MM', '6_API Fabric Access_MM', 'API Fabric Access', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_AccCenter_MM', '6_AccCenter_MM', 'AccCenter', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Acctrans_MM', '6_Acctrans_MM', 'Acctrans', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Audit Log Service_MM', '6_Audit Log Service_MM', 'Audit Log Service', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_BDI_MM', '6_BDI_MM', 'BDI', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Bulk Center_MM', '6_Bulk Center_MM', 'Bulk Center', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_CAS_MM', '6_CAS_MM', 'CAS', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Clear Recon Center_MM', '6_Clear Recon Center_MM', 'Clear Recon Center', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Config Portal_MM', '6_Config Portal_MM', 'Config Portal', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Customer Center_MM', '6_Customer Center_MM', 'Customer Center', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_DCC_MM', '6_DCC_MM', 'DCC', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_DSF Gateway_MM', '6_DSF Gateway_MM', 'DSF Gateway', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_DataSync_MM', '6_DataSync_MM', 'DataSync', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Fin Asset Center_MM', '6_Fin Asset Center_MM', 'Fin Asset Center', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_History Data Archive Center_MM', '6_History Data Archive Center_MM', 'History Data Archive Center', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_ISA_MM', '6_ISA_MM', 'ISA', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Identity Business_MM', '6_Identity Business_MM', 'Identity Business', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Identity Center_MM', '6_Identity Center_MM', 'Identity Center', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_JetMQ_MM', '6_JetMQ_MM', 'JetMQ', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Kafka_MM', '6_Kafka_MM', 'Kafka', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_License Center_MM', '6_License Center_MM', 'License Center', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_NC_MM', '6_NC_MM', 'NC', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_NFS Server_MM', '6_NFS Server_MM', 'NFS Server', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_NSLB_MM', '6_NSLB_MM', 'NSLB', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_OM Server_MM', '6_OM Server_MM', 'OM Server', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Order Query Center_MM', '6_Order Query Center_MM', 'Order Query Center', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Organization Center_MM', '6_Organization Center_MM', 'Organization Center', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Organization Portal Server_MM', '6_Organization Portal Server_MM', 'Organization Portal Server', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Redis_MM', '6_Redis_MM', 'Redis', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_SP Portal Server_MM', '6_SP Portal Server_MM', 'SP Portal Server', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Settlement Center_MM', '6_Settlement Center_MM', 'Settlement Center', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_SuperGateway Server_MM', '6_SuperGateway Server_MM', 'SuperGateway Server', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_SuperGateway_MM', '6_SuperGateway_MM', 'SuperGateway', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Task Job Center_MM', '6_Task Job Center_MM', 'Task Job Center', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Transaction Center_MM', '6_Transaction Center_MM', 'Transaction Center', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_Transfer Business_MM', '6_Transfer Business_MM', 'Transfer Business', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_WorkFlow Center_MM', '6_WorkFlow Center_MM', 'WorkFlow Center', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_ZK Registry_MM', '6_ZK Registry_MM', 'ZK Registry', 7, 'XX.2024', false, 'fintech', 'MM', 0);
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL (MODEL_ID, PARENT_MODEL_ID, MODEL_NAME, MODEL_TYPE, VERSION, HAS_MODIFIED, PRODUCT_NAME, SOLUTION_NAME, TEMPLATE_TYPE) VALUES ('7_ZK_MM', '6_ZK_MM', 'ZK', 7, 'XX.2024', false, 'fintech', 'MM', 0);

INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('0_cbs', 'XX.2024', 'timeLineConfig', 'String.class', '{"aIOpsAlarm":false,"alarmFilterType":0,"alarmList":[{"alarmId":412030116}],"eventList":[{"eventId":412030206}]}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('0_cbs', 'XX.2024', 'classType', 'String.class', 'cbs.billing.cbs');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_usrdb', 'XX.2024', 'appTypeName', 'String.class', 'usrdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_bmpdb', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_bmpdb', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_bmpdb', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_bmpdb', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_bmpdb', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_bmpdb', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_bmpdb', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_bmpdb', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_bmpdb', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_bmpdb', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.bmpdbcloud.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_bmpdb', 'XX.2024', 'appTypeName', 'String.class', 'bmpdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CAS', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CAS', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CAS', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CAS', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CAS', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CAS', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CAS', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CAS', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CAS', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CAS', 'XX.2024', 'moTypeMapping', 'String.class', 'com.huawei.commsvcs');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CAS', 'XX.2024', 'appTypeName', 'String.class', 'CAS');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_UPC', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_UPC', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_UPC', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_UPC', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_UPC', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_UPC', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_UPC', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_UPC', 'XX.2024', 'applicationType', 'Integer.class', '3');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_UPC', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_UPC', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.upcapp.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_UPC', 'XX.2024', 'appTypeName', 'String.class', 'UPC');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDRProcess', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDRProcess', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDRProcess', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDRProcess', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDRProcess', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDRProcess', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDRProcess', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDRProcess', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDRProcess', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDRProcess', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.cdrprocess.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDRProcess', 'XX.2024', 'appTypeName', 'String.class', 'CDRProcess');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BM', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BM', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BM', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BM', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BM', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BM', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BM', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BM', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BM', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BM', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.bmapp.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BM', 'XX.2024', 'appTypeName', 'String.class', 'BM');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AR', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AR', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AR', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AR', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AR', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AR', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AR', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AR', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AR', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AR', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.arapp.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AR', 'XX.2024', 'appTypeName', 'String.class', 'AR');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adapterapp', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adapterapp', 'XX.2024', 'associationAppLinkList', 'String.class', '[{"linkDirection":0,"linkType":0,"modelName":"ChargingCache","protocol":"RESTFUL"},{"linkDirection":0,"linkType":0,"modelName":"ConvergedCharging","protocol":"DSFAGENT"},{"linkDirection":0,"linkType":0,"modelName":"OnlineCharging","protocol":"DSFAGENT"},{"linkDirection":0,"linkType":0,"modelName":"CDFAPP","protocol":"DSFAGENT"},{"linkDirection":0,"linkType":0,"modelName":"cdfgmdb","protocol":"RESTFUL"},{"linkDirection":0,"linkType":0,"modelName":"adaptergmdb","protocol":"RESTFUL"}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adapterapp', 'XX.2024', 'eventList', 'String.class', '[{"eventId":412030006}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adapterapp', 'XX.2024', 'alarmList', 'String.class', '[{"alarmId":412030116}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adapterapp', 'XX.2024', 'alarmFilterType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adapterapp', 'XX.2024', 'indicatorList', 'String.class', '[{"measTypeKey":"hwTPS","measUnitKey":"hwBillingDCCAccessMsgNumberV4ForTopo","needAggr":false},{"measTypeKey":"hwDCCSuccNumber","measUnitKey":"hwBillingDCCAccessMsgNumberV4ForTopo","needAggr":false},{"measTypeKey":"hwResponseAvgTime","measUnitKey":"hwBillingDCCAccessMsgDelayV6ForTopo","needAggr":false}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adapterapp', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adapterapp', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adapterapp', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adapterapp', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.adapterapp.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adapterapp', 'XX.2024', 'appTypeName', 'String.class', 'adapterapp');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('3_site', 'XX.2024', 'siteIdColumn', 'String.class', 'solutionId');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('3_site', 'XX.2024', 'siteMoType', 'String.class', 'cbs.billing.cbs');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('3_site', 'XX.2024', 'classType', 'String.class', 'DigitalCRM');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('3_site', 'XX.2024', 'siteDataType', 'Integer.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_usrdb', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_usrdb', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_usrdb', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_usrdb', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_usrdb', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_usrdb', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_usrdb', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_usrdb', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_usrdb', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.usrdbcloud.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BizMngCharging', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BizMngCharging', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BizMngCharging', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.bizmngcharging.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BizMngCharging', 'XX.2024', 'appTypeName', 'String.class', 'BizMngCharging');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_OnlineCharging', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_OnlineCharging', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_OnlineCharging', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_OnlineCharging', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_OnlineCharging', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_OnlineCharging', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_OnlineCharging', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_OnlineCharging', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_OnlineCharging', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_OnlineCharging', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.onlinecharging.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_OnlineCharging', 'XX.2024', 'appTypeName', 'String.class', 'OnlineCharging');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ConvergedCharging', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ConvergedCharging', 'XX.2024', 'associationAppLinkList', 'String.class', '[{"linkDirection":0,"linkType":0,"modelName":"CDFAPP","protocol":"DSFAGENT"},{"linkDirection":0,"linkType":0,"modelName":"cdfgmdb","protocol":"RESTFUL"},{"linkDirection":0,"linkType":0,"modelName":"adaptergmdb","protocol":"RESTFUL"}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ConvergedCharging', 'XX.2024', 'eventList', 'String.class', '[{"eventId":412030116}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ConvergedCharging', 'XX.2024', 'alarmList', 'String.class', '[{"alarmId":412030116}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ConvergedCharging', 'XX.2024', 'alarmFilterType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ConvergedCharging', 'XX.2024', 'indicatorList', 'String.class', '[{"measTypeKey":"hwMaxDelay","measUnitKey":"hwBillingAgentMsg","needAggr":false,"originalValue":"datasource=USR_MDB_DS_136"},{"measTypeKey":"hwMaxDelay","measUnitKey":"hwBillingAgentMsg","needAggr":false,"originalValue":"datasource=CDF_MDB_DS_134"}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ConvergedCharging', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ConvergedCharging', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ConvergedCharging', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ConvergedCharging', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.convergedcharging.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ConvergedCharging', 'XX.2024', 'appTypeName', 'String.class', 'ConvergedCharging');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ChargingCache', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ChargingCache', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ChargingCache', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ChargingCache', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ChargingCache', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ChargingCache', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ChargingCache', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ChargingCache', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ChargingCache', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ChargingCache', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.chargingcache.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ChargingCache', 'XX.2024', 'appTypeName', 'String.class', 'ChargingCache');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BillShareDB', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BillShareDB', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BillShareDB', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BillShareDB', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BillShareDB', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BillShareDB', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BillShareDB', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BillShareDB', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BillShareDB', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BillShareDB', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.billsharedbcloud.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BillShareDB', 'XX.2024', 'appTypeName', 'String.class', 'BillShareDB');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_edrdb', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_edrdb', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_edrdb', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_edrdb', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_edrdb', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_edrdb', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_edrdb', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_edrdb', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_edrdb', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_edrdb', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.edrdbcloud.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_edrdb', 'XX.2024', 'appTypeName', 'String.class', 'edrdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_billdb', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_billdb', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_billdb', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_billdb', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_billdb', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_billdb', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_billdb', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_billdb', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_billdb', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_billdb', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.billdbcloud.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_billdb', 'XX.2024', 'appTypeName', 'String.class', 'billdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_usrdb', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BizMngCharging', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BizMngCharging', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BizMngCharging', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BizMngCharging', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BizMngCharging', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BizMngCharging', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_BizMngCharging', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_NSLB', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_NSLB', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_NSLB', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_NSLB', 'XX.2024', 'moTypeMapping', 'String.class', 'com.huawei.itpaas.platformservice.nslb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_NSLB', 'XX.2024', 'appTypeName', 'String.class', 'NSLB');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cdfgmdb', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cdfgmdb', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cdfgmdb', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cdfgmdb', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cdfgmdb', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cdfgmdb', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cdfgmdb', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cdfgmdb', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cdfgmdb', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cdfgmdb', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.cdfmdbcloud.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cdfgmdb', 'XX.2024', 'appTypeName', 'String.class', 'cdfgmdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adaptergmdb', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adaptergmdb', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adaptergmdb', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adaptergmdb', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adaptergmdb', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adaptergmdb', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adaptergmdb', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adaptergmdb', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adaptergmdb', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adaptergmdb', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.adptmdbcloud.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_adaptergmdb', 'XX.2024', 'appTypeName', 'String.class', 'adaptergmdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cbpgmdb', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cbpgmdb', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cbpgmdb', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cbpgmdb', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cbpgmdb', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cbpgmdb', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cbpgmdb', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cbpgmdb', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cbpgmdb', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cbpgmdb', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.cbpmdbcloud.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cbpgmdb', 'XX.2024', 'appTypeName', 'String.class', 'cbpgmdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_Notification', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_Notification', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_Notification', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_Notification', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_Notification', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_Notification', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_Notification', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_Notification', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_Notification', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_Notification', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.notification.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_Notification', 'XX.2024', 'appTypeName', 'String.class', 'Notification');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ReRating', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ReRating', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ReRating', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ReRating', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ReRating', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ReRating', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ReRating', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ReRating', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ReRating', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ReRating', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.rerating.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_ReRating', 'XX.2024', 'appTypeName', 'String.class', 'ReRating');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDFAPP', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDFAPP', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDFAPP', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDFAPP', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDFAPP', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDFAPP', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDFAPP', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDFAPP', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDFAPP', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDFAPP', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.cdfapp.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_CDFAPP', 'XX.2024', 'appTypeName', 'String.class', 'CDFAPP');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_RecurringRating', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_RecurringRating', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_RecurringRating', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_RecurringRating', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_RecurringRating', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_RecurringRating', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_RecurringRating', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_RecurringRating', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_RecurringRating', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_RecurringRating', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.recurringrating.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_RecurringRating', 'XX.2024', 'appTypeName', 'String.class', 'RecurringRating');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_NSLB', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_NSLB', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_NSLB', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_NSLB', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_NSLB', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_NSLB', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_InternalAdapter', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_InternalAdapter', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_InternalAdapter', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_InternalAdapter', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_InternalAdapter', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_InternalAdapter', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_InternalAdapter', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_InternalAdapter', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_InternalAdapter', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_InternalAdapter', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.internaladapter.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_InternalAdapter', 'XX.2024', 'appTypeName', 'String.class', 'InternalAdapter');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_uvcdb', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_uvcdb', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_uvcdb', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_uvcdb', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_uvcdb', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_uvcdb', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_uvcdb', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_uvcdb', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_uvcdb', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_uvcdb', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.uvcdbcloud.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_uvcdb', 'XX.2024', 'appTypeName', 'String.class', 'uvcdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_tmp', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_tmp', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_tmp', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_tmp', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_tmp', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_tmp', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_tmp', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_tmp', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_tmp', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_tmp', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.tmapp.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_tmp', 'XX.2024', 'appTypeName', 'String.class', 'tmp');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vmp', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vmp', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vmp', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vmp', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vmp', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vmp', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vmp', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vmp', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vmp', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vmp', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.vmapp.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vmp', 'XX.2024', 'appTypeName', 'String.class', 'vmp');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vcp', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vcp', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vcp', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vcp', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vcp', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vcp', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vcp', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vcp', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vcp', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vcp', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.vcapp.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_vcp', 'XX.2024', 'appTypeName', 'String.class', 'vcp');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_see', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_see', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_see', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_see', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_see', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_see', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_see', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_see', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_see', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_see', 'XX.2024', 'moTypeMapping', 'String.class', 'com.huawei.cbs.omsee_docker.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_see', 'XX.2024', 'appTypeName', 'String.class', 'see');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AccessFacade', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AccessFacade', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AccessFacade', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AccessFacade', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AccessFacade', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AccessFacade', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AccessFacade', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AccessFacade', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AccessFacade', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AccessFacade', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.accessfacade.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_AccessFacade', 'XX.2024', 'appTypeName', 'String.class', 'AccessFacade');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cgw', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cgw', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cgw', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cgw', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cgw', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cgw', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cgw', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cgw', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cgw', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cgw', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.charginggw.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_cgw', 'XX.2024', 'appTypeName', 'String.class', 'cgw');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_DataIntegray', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_DataIntegray', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_DataIntegray', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_DataIntegray', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_DataIntegray', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_DataIntegray', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_DataIntegray', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.mdsbmapp.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_DataIntegray', 'XX.2024', 'appTypeName', 'String.class', 'DataIntegray');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_BM', 'XX.2024', 'appTypeName', 'String.class', '#BM');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('4_bdi', 'XX.2024', 'includeAppTypeList', 'String.class', '[{"name":"DataIntegray"},{"name":"REPDB"},{"name":"CDRProcess"}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('4_bdi', 'XX.2024', 'groupName', 'String.class', '#BDI');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('4_UVC', 'XX.2024', 'includeAppTypeList', 'String.class', '[{"name":"vcp"},{"name":"vmp"},{"name":"tmp"},{"name":"uvcdb"}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('4_UVC', 'XX.2024', 'groupName', 'String.class', '#UVC');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('4_access-type', 'XX.2024', 'includeAppTypeList', 'String.class', '[{"name":"NSLB"},{"name":"cgw"},{"name":"adapterapp"},{"name":"AccessFacade"},{"name":"see"},{"name":"InternalAdapter"}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('4_access-type', 'XX.2024', 'groupName', 'String.class', '#Accessibility');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('4_real-time-business-class', 'XX.2024', 'includeAppTypeList', 'String.class', '[{"name":"ChargingCache"},{"name":"ConvergedCharging"},{"name":"OnlineCharging"},{"name":"BizMngCharging"},{"name":"RecurringRating"},{"name":"CDFAPP"},{"name":"ReRating"},{"name":"Notification"},{"name":"cbpgmdb"},{"name":"adaptergmdb"},{"name":"cdfgmdb"}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('4_real-time-business-class', 'XX.2024', 'groupName', 'String.class', '#Real-time service');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('4_management-and-payment', 'XX.2024', 'includeAppTypeList', 'String.class', '[{"name":"BM"},{"name":"AR"},{"name":"UPC"},{"name":"CAS"},{"name":"bmpdb"},{"name":"usrdb"},{"name":"billdb"},{"name":"edrdb"},{"name":"BillShareDB"}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('4_management-and-payment', 'XX.2024', 'groupName', 'String.class', '#Management and postpaid services');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('2_siteGroup', 'XX.2024', 'TYPE', 'Integer.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('7_billdb', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('7_billdb', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('7_billdb', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.billdbcloud');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('7_billdb', 'XX.2024', 'parentMoTypeName', 'String.class', 'billdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('7_podconvergedcharging', 'XX.2024', 'alarmList', 'String.class', '[{"alarmId":412030116,"moType":"cbs.billing.convergedcharging_docker"}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('7_podconvergedcharging', 'XX.2024', 'indicatorList', 'String.class', '[{"measTypeKey":"hwRecvReply","measUnitKey":"hwBillingDsfConsumerMsg","moType":"cbs.billing.convergedcharging_docker","needAggr":false,"originalValue":"hwServiceName=Consumer_dsfapp.CacheService@182.10.17.7:19773"}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('7_podconvergedcharging', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.convergedcharging');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('7_podconvergedcharging', 'XX.2024', 'parentMoTypeName', 'String.class', 'ConvergedCharging');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('7_podadapter', 'XX.2024', 'alarmList', 'String.class', '[{"alarmId":412030116,"moType":"cbs.billing.adapterapp_docker"}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('7_podadapter', 'XX.2024', 'indicatorList', 'String.class', '[{"measTypeKey":"hwTPS","measUnitKey":"hwBillingDCCAccessMsgNumberV4ForTopo","moType":"cbs.billing.adapterapp_docker","needAggr":false},{"measTypeKey":"hwDCCSuccNumber","measUnitKey":"hwBillingDCCAccessMsgNumberV4ForTopo","moType":"cbs.billing.adapterapp_docker","needAggr":false},{"measTypeKey":"hwResponseAvgTime","measUnitKey":"hwBillingDCCAccessMsgDelayV6ForTopo","moType":"cbs.billing.adapterapp_docker","needAggr":false}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('7_podadapter', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.adapterapp');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('7_podadapter', 'XX.2024', 'parentMoTypeName', 'String.class', 'adapterapp');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('8_vm', 'XX.2024', 'alarmList', 'String.class', '[{"alarmId":412030116}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('8_vm', 'XX.2024', 'moType', 'String.class', 'com.huawei.IRes.vm');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_Manager business', 'XX.2024', 'indicatorList', 'String.class', '[{"aggrType":1,"measTypeKey":"hwMaxDelay","measUnitKey":"hwBillingAgentMsg","moType":"cbs.billing.convergedcharging.cluster","needAggr":true,"originalValue":"datasource=USR_MDB_DS_136"},{"aggrType":1,"measTypeKey":"hwMaxDelay","measUnitKey":"hwBillingAgentMsg","moType":"cbs.billing.convergedcharging.cluster","needAggr":false,"originalValue":"datasource=CDF_MDB_DS_134"}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_Manager business', 'XX.2024', 'mainBusiness', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_Manager business', 'XX.2024', 'businessType', 'String.class', 'north');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_Manager business', 'XX.2024', 'businessName', 'String.class', 'Manager business');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_4G GPRS', 'XX.2024', 'indicatorList', 'String.class', '[{"aggrType":1,"measTypeKey":"hwAMOSCpuUsageAverage","measUnitKey":"CPUInterval","moType":"com.huawei.IRes.vm","needAggr":true}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_4G GPRS', 'XX.2024', 'mainBusiness', 'Boolean.class', 'false');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_4G GPRS', 'XX.2024', 'businessType', 'String.class', 'south');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_4G GPRS', 'XX.2024', 'businessName', 'String.class', '#4G GPRS');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_4G Voice', 'XX.2024', 'indicatorList', 'String.class', '[{"aggrType":1,"measTypeKey":"hwAMOSCpuUsageAverage","measUnitKey":"CPUInterval","moType":"com.huawei.IRes.vm","needAggr":true}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_4G Voice', 'XX.2024', 'mainBusiness', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_4G Voice', 'XX.2024', 'businessType', 'String.class', 'south');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_4G Voice', 'XX.2024', 'businessName', 'String.class', '#4G Voice');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_CRM', 'XX.2024', 'indicatorList', 'String.class', '[{"aggrType":1,"measTypeKey":"hwAMOSCpuUsageAverage","measUnitKey":"CPUInterval","moType":"com.huawei.IRes.vm","needAggr":true}]');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_CRM', 'XX.2024', 'mainBusiness', 'Boolean.class', 'false');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_CRM', 'XX.2024', 'businessType', 'String.class', 'north');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('1_CRM', 'XX.2024', 'businessName', 'String.class', '#CRM');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_REPDB', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_REPDB', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_REPDB', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_REPDB', 'XX.2024', 'alarmList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_REPDB', 'XX.2024', 'alarmFilterType', 'Integer.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_REPDB', 'XX.2024', 'indicatorList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_REPDB', 'XX.2024', 'businessSiteConfig', 'String.class', '{"siteIdColumn":"siteId","type":1}');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_REPDB', 'XX.2024', 'applicationType', 'Integer.class', '0');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_REPDB', 'XX.2024', 'supportGrayUpgrade', 'Boolean.class', 'true');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_REPDB', 'XX.2024', 'moTypeMapping', 'String.class', 'cbs.billing.reportdbcloud.cluster');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_REPDB', 'XX.2024', 'appTypeName', 'String.class', 'REPDB');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_DataIntegray', 'XX.2024', 'podLevel', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_DataIntegray', 'XX.2024', 'associationAppLinkList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('6_DataIntegray', 'XX.2024', 'eventList', 'String.class', 'null');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_CDRProcess', 'XX.2024', 'appTypeName', 'String.class', '#CDRProcess');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_REPDB', 'XX.2024', 'appTypeName', 'String.class', '#REPDB');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_DataIntegray', 'XX.2024', 'appTypeName', 'String.class', '#DataIntegray');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_uvcdb', 'XX.2024', 'appTypeName', 'String.class', '#uvcdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_tmp', 'XX.2024', 'appTypeName', 'String.class', '#tmp');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_vmp', 'XX.2024', 'appTypeName', 'String.class', '#vmp');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_vcp', 'XX.2024', 'appTypeName', 'String.class', '#vcp');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_InternalAdapter', 'XX.2024', 'appTypeName', 'String.class', '#InternalAdapter');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_see', 'XX.2024', 'appTypeName', 'String.class', '#see');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_AccessFacade', 'XX.2024', 'appTypeName', 'String.class', '#AccessFacade');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_adapterapp', 'XX.2024', 'appTypeName', 'String.class', '#adapterapp');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_cgw', 'XX.2024', 'appTypeName', 'String.class', '#cgw');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_NSLB', 'XX.2024', 'appTypeName', 'String.class', '#NSLB');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_cdfgmdb', 'XX.2024', 'appTypeName', 'String.class', '#cdfgmdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_adaptergmdb', 'XX.2024', 'appTypeName', 'String.class', '#adaptergmdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_cbpgmdb', 'XX.2024', 'appTypeName', 'String.class', '#cbpgmdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_Notification', 'XX.2024', 'appTypeName', 'String.class', '#Notification');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_ReRating', 'XX.2024', 'appTypeName', 'String.class', '#ReRating');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_CDFAPP', 'XX.2024', 'appTypeName', 'String.class', '#CDFAPP');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_RecurringRating', 'XX.2024', 'appTypeName', 'String.class', '#RecurringRating');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_BizMngCharging', 'XX.2024', 'appTypeName', 'String.class', '#BizMngCharging');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_OnlineCharging', 'XX.2024', 'appTypeName', 'String.class', '#OnlineCharging');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_ConvergedCharging', 'XX.2024', 'appTypeName', 'String.class', '#ConvergedCharging');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_ChargingCache', 'XX.2024', 'appTypeName', 'String.class', '#ChargingCache');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_BillShareDB', 'XX.2024', 'appTypeName', 'String.class', '#BillShareDB');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_edrdb', 'XX.2024', 'appTypeName', 'String.class', '#edrdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_billdb', 'XX.2024', 'appTypeName', 'String.class', '#billdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_usrdb', 'XX.2024', 'appTypeName', 'String.class', '#usrdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_bmpdb', 'XX.2024', 'appTypeName', 'String.class', '#bmpdb');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_AR', 'XX.2024', 'appTypeName', 'String.class', '#AR');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_CAS', 'XX.2024', 'appTypeName', 'String.class', '#CAS');
INSERT INTO TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR (MODEL_ID, VERSION, STATIC_ATTR_NAME, STATIC_ATTR_TYPE, STATIC_ATTR_VALUE) VALUES ('5_UPC', 'XX.2024', 'appTypeName', 'String.class', '#UPC');

DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_INSTANCE_MODEL;
CREATE TABLE TBL_TOPO_BUSINESS_INSTANCE_MODEL
(
    INSTANCE_ID INT AUTO_INCREMENT NOT NULL,
    MODEL_ID    VARCHAR(256) NOT NULL,
    MODEL_TYPE  INT          NOT NULL,
    DN          VARCHAR(256),
    VERSION     VARCHAR(256),
    CONSTRAINT TBL_TOPO_BUSINESS_INSTANCE_MODEL PRIMARY KEY (INSTANCE_ID)
);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (188, '6_OnlineCharging', 6, '7dc08f723efb1d8990758', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (121, '6_OnlineCharging', 6, '7dc08f723efb1d8990758', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (120, '5_BillShareDB', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (119, '5_edrdb', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (118, '5_billdb', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (117, '5_usrdb', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (116, '5_bmpdb', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (115, '5_CAS', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (114, '5_UPC', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (113, '5_AR', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (112, '5_DigitalView', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (111, '5_cdfgmdb', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (110, '5_adaptergmdb', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (109, '5_cbpgmdb', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (108, '5_Notification', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (107, '5_ReRating', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (106, '5_CDFAPP', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (105, '5_RecurringRating', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (104, '5_BizMngCharging', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (103, '5_OnlineCharging', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (102, '5_ConvergedCharging', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (101, '5_ChargingCache', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (100, '5_CDRProcess', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (99, '5_REPDB', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (98, '5_DataIntegray', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (97, '5_InternalAdapter', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (96, '5_see', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (95, '5_AccessFacade', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (94, '5_adapterapp', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (93, '5_cgw', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (92, '5_NSLB', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (91, '5_uvcdb', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (90, '5_tmp', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (89, '5_vmp', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (88, '5_vcp', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (87, '4_bdi', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (86, '4_UVC', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (85, '4_access-type', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (84, '4_real-time-business-class', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (83, '4_management-and-payment', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (82, '2_siteGroup', 2, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (81, '3_site', 3, '7dc08f7e40fa96b68e6bb-1', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (122, '6_InternalAdapter', 6, '7dc08f7d3eb1a58a4b74d', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (123, '6_adaptergmdb', 6, '7dc08f7df8769c9baa722', 'XX.2023');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (124, '6_cgw', 6, '7dc08f705fdddefc2c75e', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (125, '6_cbpgmdb', 6, '7dc08f728d877619df72f', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (126, '6_ReRating', 6, '7dc08f73748be4250075b', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (127, '6_edrdb', 6, '7dc08f7d33e09656ce741', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (128, '6_cdfgmdb', 6, '7dc08f77840dbbc533733', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (129, '6_BizMngCharging', 6, '7dc08f76f26fdb2f7d72b', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (130, '6_BillShareDB', 6, '7dc08f7d6a0d99255a740', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (131, '6_adapterapp', 6, '7dc08f7973267ef5d6721', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (132, '6_ConvergedCharging', 6, '7dc08f72fc63e8b720737', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (133, '6_CDFAPP', 6, '7dc08f737182b19bf2732', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (134, '6_ChargingCache', 6, '7dc08f704ca06aeadb735', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (135, '6_AccessFacade', 6, '7dc08f7da3efcdc5b2720', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (136, '6_CDRProcess', 6, '7dc08f7484423f6841734', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (137, '6_UPC', 6, '7dc08f7b74402893f7760', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (138, '6_DigitalView', 6, '7dc08f7f2e210fc38972c', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (139, '6_AR', 6, '7dc08f76e9272b39e7727', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (140, '6_billdb', 6, '7dc08f79dd0472414373f', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (141, '6_usrdb', 6, '7dc08f742953dcb423743', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (142, '6_Notification', 6, '7dc08f70533a9cd852755', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (143, '6_RecurringRating', 6, '7dc08f79639df9ac5075a', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (144, '6_bmpdb', 6, '7dc08f783043dbd60773e', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (145, '7_podadapter', 7, '************************************', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (146, '7_podconvergedcharging', 7, 'c031f1a1-56e4-459b-96d2-a757811c917b', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (147, '7_billdb', 7, '2795235d-3bfe-48f5-a7cb-3ee105e6c2df', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (148, '0_cbs', 0, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (149, '1_CRM', 1, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (150, '1_4G Voice', 1, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (151, '1_4G GPRS', 1, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (152, '1_Manager business', 1, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (153, '8_vm', 8, '7dc08f7e51b89a27b877e', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (154, '8_vm', 8, '7dc08f75cab3a79402796', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (155, '8_vm', 8, '7dc08f7e862d0eda01768', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (156, '8_vm', 8, '7dc08f75cab3a79402796', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (157, '8_vm', 8, '7dc08f73bb34d95d81772', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (158, '8_vm', 8, '7dc08f7e62c18d3864798', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (159, '8_vm', 8, '7dc08f7e862d0eda01768', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (160, '8_vm', 8, '7dc08f73bb34d95d81772', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (161, '8_vm', 8, '7dc08f75cab3a79402796', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (162, '8_vm', 8, '7dc08f73bb34d95d81772', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (163, '8_vm', 8, '7dc08f7090a7a4ae9b79a', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (164, '8_vm', 8, '7dc08f7e62c18d3864798', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (165, '8_vm', 8, '7dc08f76ae7f34efec77c', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (166, '8_vm', 8, '7dc08f7e62c18d3864798', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (167, '8_vm', 8, '7dc08f7497625676a1771', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (168, '8_vm', 8, '7dc08f7f87daacab2d790', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (169, '8_vm', 8, '7dc08f787cc04867e778c', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (170, '8_vm', 8, '7dc08f7f87daacab2d790', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (171, '8_vm', 8, '7dc08f76ae7f34efec77c', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (172, '8_vm', 8, '7dc08f7d32d7a27d4477a', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (173, '8_vm', 8, '7dc08f7d32d7a27d4477a', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (174, '8_vm', 8, '7dc08f7e51b89a27b877e', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (175, '8_vm', 8, '7dc08f7497625676a1771', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (176, '8_vm', 8, '7dc08f76ae7f34efec77c', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (177, '8_vm', 8, '7dc08f7497625676a1771', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (178, '8_vm', 8, '7dc08f73bb34d95d81772', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (179, '8_vm', 8, '7dc08f7e51b89a27b877e', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (180, '8_vm', 8, '7dc08f73bb34d95d81772', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (181, '8_vm', 8, '7dc08f7899e348d7f378a', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (182, '8_vm', 8, '7dc08f7f0b4b9753e5788', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (183, '8_vm', 8, '7dc08f7ae50a0dbadd76c', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (184, '8_vm', 8, '7dc08f7e62c18d3864798', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (185, '8_vm', 8, '7dc08f770c4b2a671e776', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186, '4_DigitalView', 4, 'OS=1', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (187, '6_cgw', 6, '7dc08f705fdddefc2c75e2', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186469, '0_MM_MM', 0, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186480, '1_Tibco_MM', 1, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186479, '1_CBS_MM', 1, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186478, '1_KCB_MM', 1, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186477, '1_SMSC_MM', 1, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186476, '1_M-Pesa APP_MM', 1, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186475, '1_Payment AGG_MM', 1, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186485, '2_stripeGroup_MM', 2, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186484, '2_stripeGroup_MM', 2, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186483, '3_site_MM', 3, 'MMSwimLane-2346195-rsu01-b', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186482, '3_site_MM', 3, 'MMSwimLane-2346195-gsu-b', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186481, '3_site_MM', 3, 'MMSwimLane-2346195-csu00-b', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186489, '4_System Scheduled Task Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186503, '4_Containerized Access Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186499, '4_Report Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186491, '4_Containerized Access Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186490, '4_Real-time Service Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186498, '4_DB Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186488, '4_Platform Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186487, '4_Report Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186493, '4_Report Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186486, '4_DB Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186495, '4_System Scheduled Task Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186502, '4_Real-time Service Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186497, '4_Containerized Access Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186501, '4_System Scheduled Task Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186492, '4_DB Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186500, '4_Platform Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186494, '4_Platform Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186496, '4_Real-time Service Group_MM', 4, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186574, '5_DCC_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186625, '5_Config Portal_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186624, '5_Organization Portal Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186623, '5_SP Portal Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186622, '5_SuperGateway Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186621, '5_SuperGateway_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186620, '5_NSLB_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186619, '5_API Fabric Access_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186618, '5_Config Portal_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186617, '5_Organization Portal Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186616, '5_SP Portal Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186615, '5_SuperGateway Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186614, '5_SuperGateway_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186613, '5_NSLB_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186612, '5_API Fabric Access_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186611, '5_Config Portal_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186610, '5_Organization Portal Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186609, '5_SP Portal Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186608, '5_SuperGateway Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186607, '5_SuperGateway_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186541, '5_Customer Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186606, '5_NSLB_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186605, '5_Data SFTP_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186604, '5_GaussDB_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186603, '5_Data SFTP_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186602, '5_GaussDB_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186601, '5_Data SFTP_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186600, '5_GaussDB_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186599, '5_Audit Log Service_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186598, '5_Kafka_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186597, '5_DSF Gateway_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186596, '5_NFS Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186595, '5_NC_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186594, '5_DCC_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186593, '5_Redis_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186592, '5_JetMQ_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186591, '5_ZK Registry_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186590, '5_ZK_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186589, '5_Audit Log Service_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186588, '5_Kafka_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186587, '5_DSF Gateway_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186586, '5_NFS Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186585, '5_NC_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186584, '5_DCC_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186583, '5_Redis_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186582, '5_JetMQ_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186581, '5_ZK Registry_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186580, '5_ZK_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186579, '5_Audit Log Service_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186578, '5_Kafka_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186577, '5_DSF Gateway_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186576, '5_NFS Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186507, '5_BDI_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186575, '5_NC_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186573, '5_Redis_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186572, '5_JetMQ_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186571, '5_ZK Registry_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186570, '5_ZK_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186569, '5_Clear Recon Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186568, '5_Settlement Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186567, '5_WorkFlow Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186566, '5_Bulk Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186565, '5_Task Job Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186564, '5_History Data Archive Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186563, '5_License Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186562, '5_Clear Recon Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186561, '5_Settlement Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186560, '5_WorkFlow Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186559, '5_Bulk Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186558, '5_Task Job Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186557, '5_History Data Archive Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186556, '5_License Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186555, '5_Clear Recon Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186554, '5_Settlement Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186553, '5_WorkFlow Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186504, '5_BDI_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186548, '5_OM Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186544, '5_Fin Asset Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186547, '5_AccCenter_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186550, '5_History Data Archive Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186531, '5_Identity Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186530, '5_Organization Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186529, '5_Customer Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186546, '5_Acctrans_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186626, '5_API Fabric Access_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186528, '5_Transaction Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186527, '5_DataSync_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186526, '5_Identity Business_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186525, '5_Transfer Business_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186524, '5_OM Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186523, '5_AccCenter_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186522, '5_Acctrans_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186535, '5_AccCenter_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186540, '5_Transaction Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186521, '5_Order Query Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186520, '5_Fin Asset Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186519, '5_Identity Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186551, '5_Task Job Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186518, '5_Organization Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186537, '5_Transfer Business_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186517, '5_Customer Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186516, '5_Transaction Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186515, '5_DataSync_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186514, '5_Identity Business_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186543, '5_Identity Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186513, '5_Transfer Business_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186549, '5_License Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186534, '5_Acctrans_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186512, '5_ISA_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186533, '5_Order Query Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186542, '5_Organization Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186511, '5_CAS_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186510, '5_BDI_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186509, '5_ISA_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186508, '5_CAS_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186552, '5_Bulk Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186505, '5_CAS_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186536, '5_OM Server_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186545, '5_Order Query Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186532, '5_Fin Asset Center_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186539, '5_DataSync_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186506, '5_ISA_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186538, '5_Identity Business_MM', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186672, '6_SuperGateway Server_MM', 6, '7e149b7a3fd6ac64d20bc', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186627, '6_AccCenter_MM', 6, '7e14a89f0812dce51c087', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186655, '6_WorkFlow Center_MM', 6, '7e1480edfb343cbc1f078', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (186786, '7_SuperGateway Server_MM', 7, '46ba7c41-9de9-45dc-88bf-cd8757e51c18', 'XX.2024');


DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR;
CREATE TABLE TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR
(
    INSTANCE_ID INT          NOT NULL,
    ATTRNAME    VARCHAR(256) NOT NULL,
    ATTRCLASS   VARCHAR(256) NOT NULL,
    ATTRVALUE   VARCHAR(2048),
    CONSTRAINT TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR PRIMARY KEY (INSTANCE_ID, ATTRNAME)
);


INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (186485, 'hwsGroupKey', 'java.lang.String', 'LSHTLSHT');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (186485, 'hwsStripeGroupName', 'java.lang.String', 'rsu01');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (186627, 'hwsStripe', 'java.lang.String', 'gsu');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (186627, 'nodeVersion', 'java.lang.String', '524.2.0');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (186627, 'appName', 'java.lang.String', 'wallet_LSHT_LSHT_gsu_b_pr1_acccenter');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (186627, 'nodeServiceStatus', 'java.lang.String', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (186627, 'displayType', 'java.lang.String', 'AccCenter');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (186627, 'nodeIp', 'java.lang.String', null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (186627, 'siteName', 'java.lang.String', 'LSHT');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (186627, 'hwsAppSite', 'java.lang.String', 'pr1');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (186627, 'hwsGroupName', 'java.lang.String', 'LSHT');

INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (81, 'siteId', 'String.class', '1');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (112, 'healthScore', 'String.class', '30.2');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (121, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (121, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_onlinecharging');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (122, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (122, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_internaladapter');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (123, 'nodeVersion', 'String.class', '24.03.201ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (123, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_adaptergmdb');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (124, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (124, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_southcgw');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (125, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (125, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_cbpgmdb');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (126, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (126, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_rerating');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (127, 'nodeVersion', 'String.class', '24.03.000ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (127, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_gdbcloud-edrdb');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (128, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (128, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_cdfgmdb');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (129, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (129, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_bizmngcharging');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (130, 'nodeVersion', 'String.class', '24.03.000ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (130, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_gdbcloud-billsharedb');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (131, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (131, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_adapterapp');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (132, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (132, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_convergedcharging');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (133, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (133, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_cdfapp');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (134, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (134, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_chargingcache');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (135, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (135, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_accessfacade');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (136, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (136, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_cdrprocess');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (137, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (137, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_upcapp');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (137, 'AlarmCount', 'String.class', '3');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (138, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (138, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_bmapp');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (139, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (139, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_arapp');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (140, 'nodeVersion', 'String.class', '24.03.000ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (140, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_gdbcloud-billdb');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (141, 'nodeVersion', 'String.class', '24.03.000ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (141, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_gdbcloud-usrdb');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (142, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (142, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_notification');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (143, 'nodeVersion', 'String.class', '24.03.200ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (143, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_recurringrating');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (144, 'nodeVersion', 'String.class', '24.03.000ForSingleChart');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (144, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_gdbcloud');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'podName', 'String.class', 'S5_CBS_BBIT_0424_1_adapterapp-1-5fb5c58864-95d4b');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'nodeCreateTime', 'String.class', '1714267280842');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (146, 'podName', 'String.class', 'S5_CBS_BBIT_0424_1_convergedcharging-1-5b58866b44-zhthx');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (146, 'nodeCreateTime', 'String.class', '1714267291791');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (146, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (147, 'podName', 'String.class', 'S5_CBS_BBIT_0424_1_billdb-1-1-m-0');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (147, 'nodeCreateTime', 'String.class', '1714267297591');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (147, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (153, 'vmName', 'String.class', 'kwephis480365');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (153, 'nodeIP', 'String.class', '************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (153, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (154, 'vmName', 'String.class', 'kwephis450579');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (154, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (154, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (155, 'vmName', 'String.class', 'kwephis1643906');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (155, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (155, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (156, 'vmName', 'String.class', 'kwephis450579');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (156, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (156, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (157, 'vmName', 'String.class', 'kwephis450587');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (157, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (157, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (158, 'vmName', 'String.class', 'kwephis450578');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (158, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (158, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (159, 'vmName', 'String.class', 'kwephis1643906');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (159, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (159, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (160, 'vmName', 'String.class', 'kwephis450587');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (160, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (160, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (161, 'vmName', 'String.class', 'kwephis450579');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (161, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (161, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (162, 'vmName', 'String.class', 'kwephis450587');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (162, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (162, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (163, 'vmName', 'String.class', 'kwephis450585');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (163, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (163, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (164, 'vmName', 'String.class', 'kwephis450578');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (164, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (164, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (165, 'vmName', 'String.class', 'kwephis480370');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (165, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (165, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (166, 'vmName', 'String.class', 'kwephis450578');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (166, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (166, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (167, 'vmName', 'String.class', 'kwephis7656702');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (167, 'nodeIP', 'String.class', '************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (167, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (168, 'vmName', 'String.class', 'kwephis480375');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (168, 'nodeIP', 'String.class', '************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (168, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (169, 'vmName', 'String.class', 'kwephis1643904');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (169, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (169, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (170, 'vmName', 'String.class', 'kwephis480375');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (170, 'nodeIP', 'String.class', '************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (170, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (171, 'vmName', 'String.class', 'kwephis480370');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (171, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (171, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (172, 'vmName', 'String.class', 'kwephis3463929');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (172, 'nodeIP', 'String.class', '************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (172, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (173, 'vmName', 'String.class', 'kwephis3463929');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (173, 'nodeIP', 'String.class', '************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (173, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (174, 'vmName', 'String.class', 'kwephis480365');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (174, 'nodeIP', 'String.class', '************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (174, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (175, 'vmName', 'String.class', 'kwephis7656702');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (175, 'nodeIP', 'String.class', '************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (175, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (176, 'vmName', 'String.class', 'kwephis480370');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (176, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (176, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (177, 'vmName', 'String.class', 'kwephis7656702');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (177, 'nodeIP', 'String.class', '************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (177, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (178, 'vmName', 'String.class', 'kwephis450587');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (178, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (178, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (179, 'vmName', 'String.class', 'kwephis480365');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (179, 'nodeIP', 'String.class', '************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (179, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (180, 'vmName', 'String.class', 'kwephis450587');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (180, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (180, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (181, 'vmName', 'String.class', 'kwephis480371');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (181, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (181, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (182, 'vmName', 'String.class', 'kwephis480372');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (182, 'nodeIP', 'String.class', '***********');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (182, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (183, 'vmName', 'String.class', 'kwephis480420');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (183, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (183, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (184, 'vmName', 'String.class', 'kwephis450578');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (184, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (184, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (185, 'vmName', 'String.class', 'kwephis450586');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (185, 'nodeIP', 'String.class', '************');
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (185, 'nodeServiceStatus', 'String.class', 'Enabled');

DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_INSTANCE_RELATION;
CREATE TABLE TBL_TOPO_BUSINESS_INSTANCE_RELATION
(
    INSTANCE_ID        INT          NOT NULL,
    TARGET_INSTANCE_ID INT          NOT NULL,
    RELATION_TYPE      INT          NOT NULL,
    RELATION_DESC      VARCHAR(256),
    RELATION_STATUS    INT,
    CONSTRAINT TBL_TOPO_BUSINESS_INSTANCE_RELATION PRIMARY KEY (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE)
);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186627, 186483, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186655, 186483, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186485, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186484, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186480, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186479, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186478, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186477, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186476, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186475, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186474, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186473, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186472, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186471, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186470, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (187628, 186469, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186491, 186481, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186486, 186481, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186487, 186481, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186488, 186481, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186489, 186481, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186490, 186481, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186492, 186482, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186493, 186482, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186494, 186482, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186495, 186482, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186496, 186482, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186497, 186482, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186503, 186483, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186498, 186483, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186499, 186483, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186500, 186483, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186501, 186483, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186502, 186483, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186481, 186484, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186482, 186484, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186483, 186485, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186600, 186486, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186601, 186486, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186506, 186487, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186505, 186487, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186504, 186487, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186579, 186488, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186578, 186488, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186577, 186488, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186576, 186488, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186575, 186488, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186574, 186488, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186573, 186488, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186572, 186488, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186571, 186488, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186570, 186488, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186550, 186489, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186555, 186489, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186554, 186489, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186553, 186489, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186552, 186489, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186551, 186489, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186549, 186489, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186513, 186490, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186514, 186490, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186515, 186490, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186516, 186490, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186517, 186490, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186518, 186490, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186519, 186490, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186521, 186490, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186522, 186490, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186523, 186490, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186524, 186490, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186520, 186490, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186611, 186491, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186609, 186491, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186612, 186491, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186608, 186491, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186607, 186491, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186606, 186491, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186610, 186491, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186603, 186492, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186602, 186492, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186507, 186493, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186508, 186493, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186509, 186493, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186581, 186494, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186589, 186494, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186587, 186494, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186582, 186494, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186580, 186494, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186584, 186494, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186585, 186494, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186583, 186494, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186586, 186494, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186588, 186494, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186557, 186495, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186562, 186495, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186556, 186495, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186561, 186495, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186558, 186495, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186559, 186495, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186560, 186495, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186536, 186496, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186535, 186496, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186534, 186496, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186529, 186496, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186528, 186496, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186533, 186496, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186532, 186496, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186530, 186496, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186531, 186496, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186525, 186496, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186527, 186496, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186526, 186496, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186619, 186497, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186614, 186497, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186618, 186497, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186613, 186497, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186617, 186497, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186616, 186497, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186615, 186497, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186605, 186498, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186604, 186498, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186512, 186499, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186511, 186499, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186510, 186499, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186592, 186500, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186594, 186500, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186591, 186500, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186593, 186500, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186596, 186500, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186590, 186500, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186599, 186500, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186598, 186500, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186597, 186500, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186595, 186500, 0, null, null);

INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186627, 186482, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186627, 186469, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186716, 186627, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186627, 186535, 0, null, null);


INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186672, 186483, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186672, 186469, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186786, 186672, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (186672, 186622, 0, null, null);

INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (188, 103, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (121, 103, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (120, 83, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (119, 83, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (118, 83, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (117, 83, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (116, 83, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (115, 83, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (114, 83, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (113, 83, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (112, 83, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (111, 84, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (110, 84, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (109, 84, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (108, 84, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (107, 84, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (106, 84, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (105, 84, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (104, 84, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (103, 84, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (102, 84, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (101, 84, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (100, 87, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (99, 87, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (98, 87, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (97, 85, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (96, 85, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (95, 85, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (94, 85, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (93, 85, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (92, 85, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (91, 86, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (90, 86, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (89, 86, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (88, 86, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (87, 81, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (86, 81, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (85, 81, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (84, 81, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (83, 81, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (81, 82, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (122, 97, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (123, 110, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (123, 115, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (124, 115, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (124, 93, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (125, 109, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (126, 107, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (127, 119, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (128, 111, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (129, 104, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (130, 120, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (145, 121, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (131, 94, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (132, 102, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (133, 106, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (134, 101, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (135, 95, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (136, 100, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (137, 114, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (138, 112, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (139, 113, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (140, 118, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (141, 117, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (142, 108, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (143, 105, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (144, 116, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (145, 131, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (146, 132, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (147, 140, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (82, 148, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (149, 148, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (150, 148, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (151, 148, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (152, 148, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (145, 161, 1, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (164, 145, 1, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (170, 146, 1, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (82, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (81, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (87, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (100, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (136, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (99, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (98, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (86, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (91, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (90, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (89, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (88, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (85, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (97, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (122, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (96, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (95, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (135, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (94, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (131, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (145, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (93, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (124, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (92, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (84, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (111, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (128, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (110, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (123, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (109, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (125, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (108, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (142, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (107, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (126, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (106, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (133, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (105, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (143, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (104, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (129, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (103, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (121, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (102, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (132, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (146, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (101, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (134, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (83, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (120, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (130, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (119, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (127, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (118, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (140, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (147, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (117, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (141, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (116, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (144, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (115, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (114, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (137, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (113, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (139, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (112, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (138, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (149, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (150, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (151, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (152, 148, 2, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (87, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (100, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (136, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (99, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (98, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (86, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (91, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (90, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (89, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (88, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (85, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (97, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (122, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (96, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (95, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (135, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (94, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (131, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (145, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (93, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (124, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (92, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (84, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (111, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (128, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (110, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (123, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (109, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (125, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (108, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (142, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (107, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (126, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (106, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (133, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (105, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (143, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (104, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (129, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (103, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (121, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (102, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (132, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (146, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (101, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (134, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (83, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (120, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (130, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (119, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (127, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (118, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (140, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (147, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (117, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (141, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (116, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (144, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (115, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (114, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (137, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (113, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (139, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (112, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (138, 81, 3, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (112, 186, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_INSTANCE_RELATION (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (187, 81, 3, null, null);

DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_DEPCLOUD_PIPELINE_STEP;
CREATE TABLE TBL_TOPO_BUSINESS_DEPCLOUD_PIPELINE_STEP
(
    ID                INTEGER NOT NULL,
    STEP_ID           INTEGER NOT NULL,
    GRAY_PERCENTAGE   INTEGER NOT NULL,
    GRAY_DISPLAY_FLAG INTEGER,
    CONSTRAINT TBL_TOPO_BUSINESS_DEPCLOUD_PIPELINE_STEP PRIMARY KEY (ID)
);
INSERT INTO TBL_TOPO_BUSINESS_DEPCLOUD_PIPELINE_STEP (ID, STEP_ID, GRAY_PERCENTAGE, GRAY_DISPLAY_FLAG) VALUES (1, 1, 10, 1);

DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1721641800000;
CREATE TABLE TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1721641800000
(
    INSTANCE_ID INT AUTO_INCREMENT NOT NULL,
    MODEL_ID    VARCHAR(256) NOT NULL,
    MODEL_TYPE  INT          NOT NULL,
    DN          VARCHAR(256),
    VERSION     VARCHAR(256),
    CONSTRAINT TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1721641800000 PRIMARY KEY (INSTANCE_ID)
);

INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1721641800000 (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (145, '7_podadapter', 7, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1721641800000 (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (103, '5_OnlineCharging', 5, null, 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1721641800000 (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (121, '6_OnlineCharging', 6, '7dc08f723efb1d8990758', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1721641800000 (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (188, '6_OnlineCharging', 6, '7dc08f723efb1d8990758', 'XX.2024');


DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1721641800000;
CREATE TABLE TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1721641800000
(
    INSTANCE_ID INT          NOT NULL,
    ATTRNAME    VARCHAR(256) NOT NULL,
    ATTRCLASS   VARCHAR(256) NOT NULL,
    ATTRVALUE   VARCHAR(2048),
    CONSTRAINT TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1721641800000 PRIMARY KEY (INSTANCE_ID, ATTRNAME)
);

INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1721641800000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_adapterapp-1-5fb5c58864-95d4b');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1721641800000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'nodeCreateTime', 'String.class', '1714267280842');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1721641800000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1721641800000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'nodeIp', 'String.class', '*******');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1721641800000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'nodeVersion', 'String.class', '2024');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1721641800000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'displayType', 'String.class', 'adapterapp');

DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_RELATION_1721641800000;
CREATE TABLE TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_RELATION_1721641800000
(
    INSTANCE_ID        INT NOT NULL,
    TARGET_INSTANCE_ID INT NOT NULL,
    RELATION_TYPE      INT NOT NULL,
    RELATION_DESC      VARCHAR(256),
    RELATION_STATUS    INT
);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_RELATION_1721641800000 (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (121, 103, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_RELATION_1721641800000 (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (188, 103, 0, null, null);

DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000;
CREATE TABLE TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000
(
    INDICATOR_ID           VARCHAR(1024) NOT NULL,
    INSTANCE_ID            INTEGER       NOT NULL,
    MEAS_UNIT_KEY          VARCHAR(256),
    MEAS_UNIT_NAME         VARCHAR(600),
    MEAS_TYPE_KEY          VARCHAR(256),
    THRESHOLD DOUBLE,
    IP                     VARCHAR(256),
    MO_NAME                VARCHAR(256),
    DN                     VARCHAR(256),
    DN_NAME                VARCHAR(256),
    INDEX_KEY              VARCHAR(256),
    INDEX_NAME             VARCHAR(600),
    MO_TYPE                VARCHAR(256),
    DISPLAY_VALUE          VARCHAR(8000),
    ORIGINAL_VALUE         VARCHAR(600),
    UNIT                   VARCHAR(256),
    RESOURCE_TYPE_KEY      VARCHAR(256),
    INDEX_ID               VARCHAR(256),
    HAS_MEAS_OBJ           VARCHAR(5),
    CHECKED_NET_ID         VARCHAR(2048),
    RESOURCE_GROUP         VARCHAR(256),
    INDICATOR_DISPLAY_TYPE INTEGER,
    AGGR_TYPE              INTEGER,
    INDICATOR_SORT_NUMBER  INTEGER
);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        145, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                         MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                         MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                         INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                         RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        121, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1721641800000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        188, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);

DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1736824200000;
CREATE TABLE TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1736824200000
(
    INSTANCE_ID INT AUTO_INCREMENT NOT NULL,
    MODEL_ID    VARCHAR(256) NOT NULL,
    MODEL_TYPE  INT          NOT NULL,
    DN          VARCHAR(256),
    VERSION     VARCHAR(256),
    CONSTRAINT TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1736824200000 PRIMARY KEY (INSTANCE_ID)
);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1736824200000 (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (145, '7_podadapter', 7, '************************************', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1736824200000 (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (121, '6_OnlineCharging', 6, '7dc08f723efb1d8990758', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1736824200000 (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (146, '9_adapter_docker', 9, '************************************', 'XX.2024');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_MODEL_1736824200000 (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) VALUES (161, '8_vm', 8, '7dc08f75cab3a79402796', 'XX.2024');

DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1736824200000;
CREATE TABLE TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1736824200000
(
    INSTANCE_ID INT          NOT NULL,
    ATTRNAME    VARCHAR(256) NOT NULL,
    ATTRCLASS   VARCHAR(256) NOT NULL,
    ATTRVALUE   VARCHAR(2048),
    CONSTRAINT TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1736824200000 PRIMARY KEY (INSTANCE_ID, ATTRNAME)
);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1736824200000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'appName', 'String.class', 'S5_CBS_BBIT_0424_1_adapterapp-1-5fb5c58864-95d4b');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1736824200000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'nodeCreateTime', 'String.class', '1714267280842');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1736824200000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'nodeServiceStatus', 'String.class', 'Enabled');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1736824200000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'nodeIp', 'String.class', '*******');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1736824200000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'nodeVersion', 'String.class', '2024');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1736824200000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (145, 'displayType', 'String.class', 'adapterapp');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1736824200000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (161, 'vmName', 'String.class', 'kwephis450579');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1736824200000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (161, 'nodeIP', 'String.class', '*************');
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_EXTENT_ATTR_1736824200000 (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) VALUES (161, 'nodeServiceStatus', 'String.class', 'Enabled');

DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_RELATION_1736824200000;
CREATE TABLE TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_RELATION_1736824200000
(
    INSTANCE_ID        INT NOT NULL,
    TARGET_INSTANCE_ID INT NOT NULL,
    RELATION_TYPE      INT NOT NULL,
    RELATION_DESC      VARCHAR(256),
    RELATION_STATUS    INT
);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_RELATION_1736824200000 (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (145, 121, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_RELATION_1736824200000 (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (146, 145, 0, null, null);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_RELATION_1736824200000 (INSTANCE_ID, TARGET_INSTANCE_ID, RELATION_TYPE, RELATION_DESC, RELATION_STATUS) VALUES (145, 161, 1, null, null);

DROP TABLE IF EXISTS TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1736824200000;
CREATE TABLE TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1736824200000
(
    INDICATOR_ID           VARCHAR(1024) NOT NULL,
    INSTANCE_ID            INTEGER       NOT NULL,
    MEAS_UNIT_KEY          VARCHAR(256),
    MEAS_UNIT_NAME         VARCHAR(600),
    MEAS_TYPE_KEY          VARCHAR(256),
    THRESHOLD DOUBLE,
    IP                     VARCHAR(256),
    MO_NAME                VARCHAR(256),
    DN                     VARCHAR(256),
    DN_NAME                VARCHAR(256),
    INDEX_KEY              VARCHAR(256),
    INDEX_NAME             VARCHAR(600),
    MO_TYPE                VARCHAR(256),
    DISPLAY_VALUE          VARCHAR(8000),
    ORIGINAL_VALUE         VARCHAR(600),
    UNIT                   VARCHAR(256),
    RESOURCE_TYPE_KEY      VARCHAR(256),
    INDEX_ID               VARCHAR(256),
    HAS_MEAS_OBJ           VARCHAR(5),
    CHECKED_NET_ID         VARCHAR(2048),
    RESOURCE_GROUP         VARCHAR(256),
    INDICATOR_DISPLAY_TYPE INTEGER,
    AGGR_TYPE              INTEGER
);
INSERT INTO TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_INDICATOR_1736824200000 (INDICATOR_ID, INSTANCE_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME,
                                                                          MEAS_TYPE_KEY, IP, MO_NAME, DN, DN_NAME, INDEX_KEY, INDEX_NAME,
                                                                          MO_TYPE, DISPLAY_VALUE, ORIGINAL_VALUE, UNIT, RESOURCE_TYPE_KEY,
                                                                          INDEX_ID, HAS_MEAS_OBJ, CHECKED_NET_ID, AGGR_TYPE, INDICATOR_DISPLAY_TYPE,
                                                                          RESOURCE_GROUP)
VALUES ('7dc08f7d3eb1a58a4b74dhwBillingAgentMsghwMaxDelay数据源名=GENERAL_SYS_PDB_DS_120',
        145, 'hwBillingAgentMsg', '访问agent消息统计', 'hwMaxDelay', NULL,
        'S5_CBS_BBIT_0424_1_internaladapter', '7dc08f7d3eb1a58a4b74d',
        'S5_CBS_BBIT_0424_1_internaladapter',
        NULL, '最大响应时间（毫秒）(毫秒)', 'cbs.billing.internaladapter.cluster', '数据源名=GENERAL_SYS_PDB_DS_120',
        'datasource=GENERAL_SYS_PDB_DS_120', '毫秒',
        'OMS', 'hwBillingAgentMsg~~hwMaxDelay', 'true', '/(_)OS=1', 1, 1, NULL);