DROP TABLE IF EXISTS TBL_AIOPS_PRODUCT_PORTRAIT;
CREATE TABLE TBL_AIOPS_PRODUCT_PORTRAIT (
    PORTRAIT_ID                       INT AUTO_INCREMENT,
    PORTRAIT_NAME                     VARCHAR NOT NULL,
    PORTRAIT_TYPE                     INT               NOT NULL,
    PORTRAIT_LAST_MODIFY_DATE         BIGINT            NOT NULL,
    PORTRAIT_DESC                     VARCHAR,
    PRODUCT_PORTRAIT_CPU_NUM_BASELINE INT,
    PRODUCT_PORTRAIT_RAM_NUM_BASELINE INT,
    SHEARED_INDICATORS                VARCHAR,
    USER_ID                           VARCHAR NOT NULL
);
insert into TBL_AIOPS_PRODUCT_PORTRAIT(PORTRAIT_ID, PORTRAIT_NAME, PORTRAIT_TYPE, PORTRAIT_LAST_MODIFY_DATE, PORTRAIT_DESC, PRODUCT_PORTRAIT_CPU_NUM_BASELINE, PRODUCT_PORTRAIT_RAM_NUM_BASELINE, SHEARED_INDICATORS, USER_ID)
values (1, 'mockPortrait', 1, ***********, '主机画像', 1, 1, '[{"displayValue":"com.huawei.IRes.vm~Linux周期内CPU统计~周期内CPU占用率平均值(%)","returnValue":"CPUInterval(_)hwAMOSCpuUsageAverage"}]', '1');
insert into TBL_AIOPS_PRODUCT_PORTRAIT(PORTRAIT_ID, PORTRAIT_NAME, PORTRAIT_TYPE, PORTRAIT_LAST_MODIFY_DATE, PORTRAIT_DESC, PRODUCT_PORTRAIT_CPU_NUM_BASELINE, PRODUCT_PORTRAIT_RAM_NUM_BASELINE, SHEARED_INDICATORS, USER_ID)
values (2, 'businessPortrait', 2, ***********, '业务画像', 1, 1, null, '1');

DROP TABLE IF EXISTS TBL_AIOPS_HOST_PORTRAIT;
CREATE TABLE TBL_AIOPS_HOST_PORTRAIT (
    PORTRAIT_ID         INT,
    HOST_PORTRAIT_NAME  VARCHAR   NOT NULL,
    HOST_TYPE           VARCHAR   NOT NULL,
    PRODUCT_PORTRAIT_ID INT                 NOT NULL,
    HOST_PORTRAIT_DESC  VARCHAR,
    CPU_NUM             INT                 NOT NULL,
    RAM_SIZE            INT                 NOT NULL,
    DEPLOY_SERVICES     VARCHAR  NOT NULL,
    HOST_NUM_BASELINE   INT,
    MIN_HOST_NUM        INT,
    MAX_HOST_NUM        INT
);
insert into TBL_AIOPS_HOST_PORTRAIT (portrait_id, host_portrait_name, host_type, product_portrait_id, host_portrait_desc, cpu_num, ram_size, deploy_services, host_num_baseline, min_host_num, max_host_num)
values (1, 'OnlineChgHost', 'OnlineChgHost', 1, 'desc', 1, 1, '["{\"deployNumber\":10,\"serviceName\":\"businessName\"}"]', 1, 1, 1);
insert into TBL_AIOPS_HOST_PORTRAIT (portrait_id, host_portrait_name, host_type, product_portrait_id, host_portrait_desc, cpu_num, ram_size, deploy_services, host_num_baseline, min_host_num, max_host_num)
values (1, 'OfflineChgHost', 'OfflineChgHost', 1, 'desc', 1, 1, '["{\"deployNumber\":10,\"serviceName\":\"businessName\"}"]', 1, 1, 1);

DROP TABLE IF EXISTS TBL_AIOPS_BUSINESS_PORTRAIT;
CREATE TABLE TBL_AIOPS_BUSINESS_PORTRAIT (
    PORTRAIT_ID         INT,
    PORTRAIT_NAME       VARCHAR NOT NULL,
    BUSINESS_NAME       VARCHAR NOT NULL,
    BUSINESS_DESC       VARCHAR,
    PRODUCT_PORTRAIT_ID INT               NOT NULL,
    MIN_CPU_NUM         INT               NOT NULL,
    MAX_CPU_NUM         INT               NOT NULL,
    MIN_RAM_SIZE        INT               NOT NULL,
    MAX_RAM_SIZE        INT               NOT NULL,
    MIN_REPLICA_NUM     INT,
    MAX_REPLICA_NUM     INT,
    SERVICE_DEPLOY_TYPE INT
);
insert into TBL_AIOPS_BUSINESS_PORTRAIT (portrait_id, portrait_name, business_name, business_desc, product_portrait_id, min_cpu_num, max_cpu_num, min_ram_size, max_ram_size, min_replica_num, max_replica_num, service_deploy_type)
values (1, 'portraitName', 'businessName', 'desc', 2, 1, 1, 1, 1, 1, 1, 2);
insert into TBL_AIOPS_BUSINESS_PORTRAIT (portrait_id, portrait_name, business_name, business_desc, product_portrait_id, min_cpu_num, max_cpu_num, min_ram_size, max_ram_size, min_replica_num, max_replica_num, service_deploy_type)
values (2, 'portraitName', 'cannotScaleName', 'desc', 2, 1, 1, 1, 1, 1, 1, 4);

DROP TABLE IF EXISTS TBL_AIOPS_PORTRAIT_INDICATOR;
CREATE TABLE TBL_AIOPS_PORTRAIT_INDICATOR (
    MEAS_UNIT_KEY           VARCHAR  NOT NULL,
    INDICATOR_ID            INT,
    BELONG_PORTRAIT_ID      INT                NOT NULL,
    MEAS_UNIT_NAME          VARCHAR  NOT NULL,
    MEAS_TYPE_KEY           VARCHAR  NOT NULL,
    MEAS_OBJ_DISPLAY_VALUE  VARCHAR,
    MEAS_OBJ_ORIGINAL_VALUE VARCHAR,
    MO_TYPE                 VARCHAR  NOT NULL,
    UPPER_THRESHOLD         DOUBLE,
    LOWER_THRESHOLD         DOUBLE,
    INDEX_NAME              VARCHAR,
    UNIT                    VARCHAR,
    CHECKED_NET_ID          VARCHAR,
    HAS_MEAS_OBJ            VARCHAR,
    INDEX_ID                VARCHAR,
    RESOURCE_TYPE_KEY       VARCHAR
);
insert into TBL_AIOPS_PORTRAIT_INDICATOR (meas_unit_key, indicator_id, belong_portrait_id, meas_unit_name, meas_type_key, meas_obj_display_value, meas_obj_original_value, mo_type, upper_threshold, lower_threshold, index_name, unit, checked_net_id, has_meas_obj, index_id, resource_type_key)
values ('meas_unit_key', 1, 1, 'name', 'type', 'display', 'ori', 'moType', 5.0, 2.0, 'index', 'unit', 'check', 'has', 'indexId', 'key');

DROP TABLE IF EXISTS TBL_AIOPS_SCHEDULE_CONFIG;
CREATE TABLE TBL_AIOPS_SCHEDULE_CONFIG (
    CONFIG_ID                       INT                NOT NULL,
    SCHEDULE_TASK_ID                INT                NOT NULL,
    SUB_PORTRAIT_NAME               VARCHAR  NOT NULL,
    INDICATOR_PREDICTION_TASK_ID    INT,
    INDICATOR_PREDICTION_TASK_NAME  VARCHAR,
    INDICATOR_UPPER                 DOUBLE,
    INDICATOR_LOWER                 DOUBLE,
    INDICATOR_DISPLAY               VARCHAR NOT NULL,
    INDICATOR_RETURN                VARCHAR NOT NULL,
    UNITED_ID                       VARCHAR,
    SITE_ID                         VARCHAR,
    HOST_TYPE                       VARCHAR,
    CONSTRAINT TBL_AIOPS_SCHEDULE_CONFIG PRIMARY KEY (CONFIG_ID)
);
insert into TBL_AIOPS_SCHEDULE_CONFIG (config_id, schedule_task_id, sub_portrait_name, indicator_prediction_task_id, indicator_prediction_task_name, indicator_upper, indicator_lower, indicator_display, indicator_return, united_id, site_id, host_type)
values (1, 2, 'name1', 999, 'indicatorName', 5.0, 2.0, 'display', 'return', 'unitedId', '1', 'OnlineChgHost');
insert into TBL_AIOPS_SCHEDULE_CONFIG (config_id, schedule_task_id, sub_portrait_name, indicator_prediction_task_id, indicator_prediction_task_name, indicator_upper, indicator_lower, indicator_display, indicator_return, united_id, site_id, host_type)
values (2, 2, 'name2', 999, 'indicatorName', 5.0, 2.0, 'display', 'return', 'unitedId2', '1', 'OfflineChgHost');
insert into TBL_AIOPS_SCHEDULE_CONFIG (config_id, schedule_task_id, sub_portrait_name, indicator_prediction_task_id, indicator_prediction_task_name, indicator_upper, indicator_lower, indicator_display, indicator_return, united_id, site_id, host_type)
values (7, 7, 'name2', 999, 'indicatorName', 5.0, 2.0, 'display', 'return', 'unitedId2', '1', 'OfflineChgHost');
insert into TBL_AIOPS_SCHEDULE_CONFIG (config_id, schedule_task_id, sub_portrait_name, indicator_prediction_task_id, indicator_prediction_task_name, indicator_upper, indicator_lower, indicator_display, indicator_return, united_id, site_id, host_type)
values (3, 1, 'name1', 999, 'indicatorName', 5.0, 2.0, 'display', 'return', 'unitedId', '1', 'OnlineChgHost');

DROP TABLE IF EXISTS TBL_AIOPS_RESOURCE_SCHEDULE_STRATEGY;
CREATE TABLE TBL_AIOPS_RESOURCE_SCHEDULE_STRATEGY (
    STRATEGY_ID       INT               NOT NULL,
    STRATEGY_NAME     VARCHAR NOT NULL,
    EXECUTE_PERIOD     VARCHAR NOT NULL,
    EXECUTE_DURATION   VARCHAR NOT NULL,
    STRATEGY_PRIORITY INT               NOT NULL,
    STRATEGY_TYPE     INT,
    USER_ID           VARCHAR NOT NULL,
    CONSTRAINT TBL_AIOPS_RESOURCE_SCHEDULE_STRATEGY PRIMARY KEY (STRATEGY_ID)
);
insert into TBL_AIOPS_RESOURCE_SCHEDULE_STRATEGY (strategy_id, strategy_name, execute_period, execute_duration, strategy_priority, strategy_type, user_id)
values (1, 'stname', 'week|1', '00:00-01:00', 1, 1, '1');

DROP TABLE IF EXISTS TBL_AIOPS_SCHEDULE_STRATEGY_EXCUTE_CONFIG;
CREATE TABLE TBL_AIOPS_SCHEDULE_STRATEGY_EXCUTE_CONFIG (
    CONFIG_ID       INT               NOT NULL,
    STRATEGY_ID     INT               NOT NULL,
    BUSINESS_NAME   VARCHAR NOT NULL,
    SCHEDULE_CONFIG INT               NOT NULL,
    CONSTRAINT TBL_AIOPS_SCHEDULE_STRATEGY_EXCUTE_CONFIG PRIMARY KEY (CONFIG_ID)
);
insert into TBL_AIOPS_SCHEDULE_STRATEGY_EXCUTE_CONFIG (config_id, strategy_id, business_name, schedule_config)
values (1, 1, 'name', 2);
insert into TBL_AIOPS_SCHEDULE_STRATEGY_EXCUTE_CONFIG (config_id, strategy_id, business_name, schedule_config)
values (2, 1, 'businessName', 2);

DROP TABLE IF EXISTS TBL_AIOPS_RESOURCE_SCHEDULE_TASK;
CREATE TABLE TBL_AIOPS_RESOURCE_SCHEDULE_TASK (
    TASK_ID                          INT                NOT NULL,
    IS_AUTO_SCHEDULE                 BOOLEAN,
    SCHEDULE_STATUS_AND_DETAIL       VARCHAR,
    EXECUTE_TYPE                     INT                NOT NULL,
    ASSOCIATE_PRODUCT_PORTRAIT_ID    INT                NOT NULL,
    ASSOCIATE_PRODUCT_PORTRAIT_NAME  VARCHAR  NOT NULL,
    SCHEDULE_DEPENDENCY_INDICATE     VARCHAR,
    EXPAND_DELIVERY_TIME             VARCHAR,
    SCALE_IN_DELIVERY_TIME           VARCHAR,
    SCHEDULE_TIMEOUT                 VARCHAR,
    SCHEDULE_PERIOD                  VARCHAR,
    STRATEGY_IDS                     VARCHAR,
    DETECT_OUTPUT_COUNT              INT,
    SITE_ID                          VARCHAR,
    CONSTRAINT TBL_AIOPS_RESOURCE_SCHEDULE_TASK PRIMARY KEY (TASK_ID)
);
insert into TBL_AIOPS_RESOURCE_SCHEDULE_TASK (TASK_ID, IS_AUTO_SCHEDULE, SCHEDULE_STATUS_AND_DETAIL, EXECUTE_TYPE, ASSOCIATE_PRODUCT_PORTRAIT_ID, ASSOCIATE_PRODUCT_PORTRAIT_NAME, SCHEDULE_DEPENDENCY_INDICATE, EXPAND_DELIVERY_TIME, SCALE_IN_DELIVERY_TIME, SCHEDULE_TIMEOUT, SCHEDULE_PERIOD, STRATEGY_IDS, DETECT_OUTPUT_COUNT, SITE_ID)
values (1, true, null, 1, 1, 'chanpinhuaxiang', '{"displayValue":"com.huawei.IRes.vm~Linux周期内CPU统计~周期内CPU占用率平均值(%)","returnValue":"CPUInterval(_)hwAMOSCpuUsageAverage"}', null, null, 30, 60, '["stname"]', 1440, '1');

insert into TBL_AIOPS_RESOURCE_SCHEDULE_TASK (TASK_ID, IS_AUTO_SCHEDULE, SCHEDULE_STATUS_AND_DETAIL, EXECUTE_TYPE, ASSOCIATE_PRODUCT_PORTRAIT_ID, ASSOCIATE_PRODUCT_PORTRAIT_NAME, SCHEDULE_DEPENDENCY_INDICATE, EXPAND_DELIVERY_TIME, SCALE_IN_DELIVERY_TIME, SCHEDULE_TIMEOUT, SCHEDULE_PERIOD, STRATEGY_IDS, DETECT_OUTPUT_COUNT, SITE_ID)
values (7, true, null, 1, 2, 'chanpinhuaxiang', '{"displayValue":"com.huawei.IRes.vm~Linux周期内CPU统计~周期内CPU占用率平均值(%)","returnValue":"CPUInterval(_)hwAMOSCpuUsageAverage"}', null, null, 30, 60, '["stname"]', 1440, '1');



DROP TABLE IF EXISTS TBL_AIOPS_ANALYSIS_TASK;
CREATE TABLE TBL_AIOPS_ANALYSIS_TASK (
                                         TASK_ID INTEGER AUTO_INCREMENT NOT NULL,
                                         TASK_DESC VARCHAR(2048),
                                         TASK_NAME VARCHAR(256) NOT NULL,
                                         SITUATION_ID BIGINT,
                                         TASK_TYPE INTEGER NOT NULL,
                                         PERIODIC_TYPE INTEGER,
                                         PREDICT_CRON VARCHAR(256),
                                         TRAIN_CRON VARCHAR(256),
                                         SOLUTION_ID VARCHAR(256),
                                         SOLUTION_NAME VARCHAR(256),
                                         TRAIN_STATUS INTEGER,
                                         START_STATUS INTEGER,
                                         REPORT_ALARM INTEGER,
                                         ALARM_TYPE INTEGER,
                                         BEGIN_TIME BIGINT,
                                         END_TIME BIGINT,
                                         INDICATOR_SELECT_TYPE INTEGER,
                                         UPDATE_TIME BIGINT,
                                         ALGORITHM_MODEL_ID VARCHAR(256),
                                         ALGORITHM_MODEL_NAME VARCHAR(256),
                                         ALGORITHM_PARAM CLOB,
                                         DATASOURCE_ID VARCHAR(256),
                                         PATH VARCHAR(256),
                                         TASK_DETAIL VARCHAR(1024),
                                         ADDITION VARCHAR(256),
                                         USER_ID VARCHAR(256),
                                         LAST_MODIFY_USER_ID VARCHAR(256),
                                         LAST_EXECUTOR_USER_ID VARCHAR(256),
                                         ALARM_TYPE_LIST CLOB,
                                         ALARM_SELECT_TYPE INTEGER,
                                         ALARM_SELECT_LIST CLOB,
                                         ALARM_SELECT_GROUP_LIST CLOB,
                                         INDICATOR_PREDICT_NUM BIGINT,
                                         INDICATOR_TRAIN_NUM BIGINT,
                                         ALARM_PREDICT_TIME VARCHAR(50),
                                         ALARM_TRAIN_TIME VARCHAR(50),
                                         LOG_PREDICT_TIME VARCHAR(50),
                                         LOG_TRAIN_TIME VARCHAR(50),
                                         REPORT_ALARM_NAME VARCHAR(256),
                                         REPORT_ALARM_ID VARCHAR(256),
                                         IMPORT_TASK_STATUS BOOLEAN,
                                         GROUP_LEVEL INTEGER,
                                         TRAIN_ADDRESS VARCHAR(255),
                                         PREDICT_ADDRESS VARCHAR(255),
                                         INDICATOR_PREDICT_SCENARIO INTEGER,
                                         SUB_SITE VARCHAR(255),
                                         CONSTRAINT TBL_AIOPS_ANALYSIS_TASK PRIMARY KEY (TASK_ID)
);
insert into TBL_AIOPS_ANALYSIS_TASK (task_id, task_desc, task_name, situation_id, task_type, periodic_type, predict_cron, train_cron, solution_id, solution_name, train_status, start_status, report_alarm, alarm_type, begin_time, end_time, indicator_select_type, update_time, algorithm_model_id, algorithm_model_name, algorithm_param, datasource_id, path, task_detail, addition, user_id, last_modify_user_id, last_executor_user_id, alarm_type_list, alarm_select_type, alarm_select_list, alarm_select_group_list, indicator_predict_num, indicator_train_num, alarm_predict_time, alarm_train_time, log_predict_time, log_train_time, report_alarm_name, report_alarm_id, import_task_status, group_level, train_address, predict_address, indicator_predict_scenario, sub_site)
values (1, null, 'name', 1, 1, 1, null, null, null, null, null, 2, null, null, null, null, null, null, null, null, null, 0, null, null, null, null, '1', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);
insert into TBL_AIOPS_ANALYSIS_TASK (task_id, task_desc, task_name, situation_id, task_type, periodic_type, predict_cron, train_cron, solution_id, solution_name, train_status, start_status, report_alarm, alarm_type, begin_time, end_time, indicator_select_type, update_time, algorithm_model_id, algorithm_model_name, algorithm_param, datasource_id, path, task_detail, addition, user_id, last_modify_user_id, last_executor_user_id, alarm_type_list, alarm_select_type, alarm_select_list, alarm_select_group_list, indicator_predict_num, indicator_train_num, alarm_predict_time, alarm_train_time, log_predict_time, log_train_time, report_alarm_name, report_alarm_id, import_task_status, group_level, train_address, predict_address, indicator_predict_scenario, sub_site)
values (2, null, 'name2', 1, 6, 1, null, null, null, null, null, 2, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '1', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, 0, null);
insert into TBL_AIOPS_ANALYSIS_TASK (task_id, task_desc, task_name, situation_id, task_type, periodic_type, predict_cron, train_cron, solution_id, solution_name, train_status, start_status, report_alarm, alarm_type, begin_time, end_time, indicator_select_type, update_time, algorithm_model_id, algorithm_model_name, algorithm_param, datasource_id, path, task_detail, addition, user_id, last_modify_user_id, last_executor_user_id, alarm_type_list, alarm_select_type, alarm_select_list, alarm_select_group_list, indicator_predict_num, indicator_train_num, alarm_predict_time, alarm_train_time, log_predict_time, log_train_time, report_alarm_name, report_alarm_id, import_task_status, group_level, train_address, predict_address, indicator_predict_scenario, sub_site)
values (3, null, 'name3', 1, 1, 1, null, null, null, null, null, 2, null, null, null, null, null, null, null, null, null, 0, null, null, null, null, '1', null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);
insert into TBL_AIOPS_ANALYSIS_TASK (task_id, task_desc, task_name, situation_id, task_type, periodic_type, predict_cron, train_cron, solution_id, solution_name, train_status, start_status, report_alarm, alarm_type, begin_time, end_time, indicator_select_type, update_time, algorithm_model_id, algorithm_model_name, algorithm_param, datasource_id, path, task_detail, addition, user_id, last_modify_user_id, last_executor_user_id, alarm_type_list, alarm_select_type, alarm_select_list, alarm_select_group_list, indicator_predict_num, indicator_train_num, alarm_predict_time, alarm_train_time, log_predict_time, log_train_time, report_alarm_name, report_alarm_id, import_task_status, group_level, train_address, predict_address, indicator_predict_scenario, sub_site)
values (996, null, 'name2', 1, 6, 1, null, null, null, null, null, 2, null, null, null, null, null, null, null, null, null, null, null, null, null, null, '1', null, null, null, null, null, null, 1, null, null, null, null, null, null, null, null, null, null, 0, null);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_UNITED_INDICATOR;
CREATE TABLE TBL_AIOPS_TASK_UNITED_INDICATOR(
                                                UNITED_ID VARCHAR(256) NOT NULL,
                                                TASK_ID INTEGER NOT NULL,
                                                CLUSTER_NAME VARCHAR(256),
                                                SITE_ID VARCHAR(256),
                                                MO_TYPE VARCHAR(256),
                                                MEAS_UNIT_KEY VARCHAR(256),
                                                MEAS_TYPE_KEY VARCHAR(256),
                                                MEAS_UNIT_NAME VARCHAR(256),
                                                INDEX_ID VARCHAR(256),
                                                INDEX_NAME VARCHAR(256),
                                                DISPLAY_VALUE VARCHAR(256),
                                                ORIGINAL_VALUE VARCHAR(256),
                                                UNIT VARCHAR(256),
                                                HAS_MEAS_OBJ VARCHAR(256),
                                                CHECKED_NET_ID VARCHAR(256),
                                                PRIMARY KEY (UNITED_ID,TASK_ID)
);
insert into TBL_AIOPS_TASK_UNITED_INDICATOR (united_id, task_id, cluster_name, site_id, mo_type, meas_unit_key, meas_type_key, meas_unit_name, index_id, index_name, display_value, original_value, unit, has_meas_obj, checked_net_id)
values ('unitedId', 999, 'OnlineChgHost', '1', 'com.huawei.IRes.vm', 'CPUInterval', 'hwAMOSCpuUsageAverage', 'Linux周期内CPU统计', 'CPUInterval~~hwAMOSCpuUsageAverage', '周期内CPU占用率平均值(%)', null, null, '%', false, '/(_)OnlineChgHost1');
insert into TBL_AIOPS_TASK_UNITED_INDICATOR (united_id, task_id, cluster_name, site_id, mo_type, meas_unit_key, meas_type_key, meas_unit_name, index_id, index_name, display_value, original_value, unit, has_meas_obj, checked_net_id)
values ('unitedId2', 999, 'OfflineChgHost', '1', 'com.huawei.IRes.vm', 'CPUInterval', 'hwAMOSCpuUsageAverage', 'Linux周期内CPU统计', 'CPUInterval~~hwAMOSCpuUsageAverage', '周期内CPU占用率平均值(%)', null, null, '%', false, '/(_)OnlineChgHost1');


DROP TABLE IF EXISTS TBL_AIOPS_SCHEDULE_EXCUTE_DETAIL;
CREATE TABLE TBL_AIOPS_SCHEDULE_EXCUTE_DETAIL (
    ID                   INT                NOT NULL AUTO_INCREMENT,
    SCHEDULE_TASK_ID     INT                NOT NULL,
    INDICATOR_GROUP_ID   VARCHAR  NOT NULL,
    EXCUTE_START_TIME    BIGINT,
    EXCUTE_END_TIME      BIGINT,
    EXCUTE_OBJECT        VARCHAR,
    STATUS               VARCHAR  NOT NULL,
    EXCUTE_PARAMS        VARCHAR,
    EXCUTE_RESULT_DETAIL VARCHAR NOT NULL,
    CONSTRAINT TBL_AIOPS_SCHEDULE_EXCUTE_DETAIL PRIMARY KEY (ID)
);
insert into TBL_AIOPS_SCHEDULE_EXCUTE_DETAIL (id, schedule_task_id, indicator_group_id, excute_start_time, excute_end_time, excute_object, status, excute_params, excute_result_detail)
values (607, 1, 'OfflineChgHost', 1687317097908, 1687317219807, null, 'SUCCESS', null, '{"appScaleDetails":{"a5ab8319b9f41c4c540a7":{"appExecuteStatus":"UNSTART","appId":"a5ab8319b9f41c4c540a7","appName":"recurringrating-1-b9d89bf77-vj4ph"},"a5ab831d77e64f3e6c07f":{"appExecuteStatus":"UNSTART","appId":"a5ab831d77e64f3e6c07f","appName":"bizmngcharging-1-65495d766c-4dmmn"},"a5ab8314b933e3e85c0a5":{"appExecuteMessage":"Scale with strategy success","appExecuteStatus":"SUCCESS","appId":"a5ab8314b933e3e85c0a5","appName":"onlinecharging-1-75d585c6ff-7g5ct","appScaleTaskId":"18f5a085-8571-4564-bd9b-14110836e3a8","endTime":"1687317219798","startTime":"1687317099111"}},"vmCurrentNumber":2,"vmExecuteMessage":"no need to scale vm","vmExecuteStatus":"SUCCESS","vmName":"OnlineChgHost","vmReplicasNumber":2}');
insert into TBL_AIOPS_SCHEDULE_EXCUTE_DETAIL (id, schedule_task_id, indicator_group_id, excute_start_time, excute_end_time, excute_object, status, excute_params, excute_result_detail)
values (608, 2, 'OnlineChgHost', 1687317097908, 1687317219807, null, 'RUNNING', null, '{"appScaleDetails":{"a5ab8319b9f41c4c540a7":{"appExecuteStatus":"RUNNING","appId":"a5ab8319b9f41c4c540a7","appName":"recurringrating-1-b9d89bf77-vj4ph","appScaleTaskId":"11"},"a5ab831d77e64f3e6c07f":{"appExecuteStatus":"UNSTART","appId":"a5ab831d77e64f3e6c07f","appName":"bizmngcharging-1-65495d766c-4dmmn"},"a5ab8314b933e3e85c0a5":{"appExecuteMessage":"Scale with strategy success","appExecuteStatus":"SUCCESS","appId":"a5ab8314b933e3e85c0a5","appName":"onlinecharging-1-75d585c6ff-7g5ct","appScaleTaskId":"18f5a085-8571-4564-bd9b-14110836e3a8","endTime":"1687317219798","startTime":"1687317099111"}},"vmCurrentNumber":3,"vmExecuteMessage":"no need to scale vm","vmExecuteStatus":"SUCCESS","vmName":"OnlineChgHost","vmReplicasNumber":2}');
insert into TBL_AIOPS_SCHEDULE_EXCUTE_DETAIL (id, schedule_task_id, indicator_group_id, excute_start_time, excute_end_time, excute_object, status, excute_params, excute_result_detail)
values (609, 1, 'OnlineChgHost', 1687317097908, 1687317219807, null, 'SUCCESS', null, '{"appScaleDetails":{"a5ab8319b9f41c4c540a7":{"appExecuteStatus":"UNSTART","appId":"a5ab8319b9f41c4c540a7","appName":"recurringrating-1-b9d89bf77-vj4ph"},"a5ab831d77e64f3e6c07f":{"appExecuteStatus":"UNSTART","appId":"a5ab831d77e64f3e6c07f","appName":"bizmngcharging-1-65495d766c-4dmmn"},"a5ab8314b933e3e85c0a5":{"appExecuteMessage":"Scale with strategy success","appExecuteStatus":"SUCCESS","appId":"a5ab8314b933e3e85c0a5","appName":"onlinecharging-1-75d585c6ff-7g5ct","appScaleTaskId":"18f5a085-8571-4564-bd9b-14110836e3a8","endTime":"1687317219798","startTime":"1687317099111"}},"vmCurrentNumber":2,"vmExecuteMessage":"no need to scale vm","vmExecuteStatus":"SUCCESS","vmName":"OnlineChgHost","vmReplicasNumber":2}');
insert into TBL_AIOPS_SCHEDULE_EXCUTE_DETAIL (id, schedule_task_id, indicator_group_id, excute_start_time, excute_end_time, excute_object, status, excute_params, excute_result_detail)
values (610, 2, 'OnlineChgHost', 1687317097908, 1687317219807, null, 'RUNNING', null, '{"appScaleDetails":{"a5ab8319b9f41c4c540a7":{"appExecuteStatus":"UNSTART","appId":"a5ab8319b9f41c4c540a7","appName":"recurringrating-1-b9d89bf77-vj4ph"},"a5ab831d77e64f3e6c07f":{"appExecuteStatus":"UNSTART","appId":"a5ab831d77e64f3e6c07f","appName":"bizmngcharging-1-65495d766c-4dmmn"},"a5ab8314b933e3e85c0a5":{"appExecuteMessage":"Scale with strategy success","appExecuteStatus":"SUCCESS","appId":"a5ab8314b933e3e85c0a5","appName":"onlinecharging-1-75d585c6ff-7g5ct","appScaleTaskId":"18f5a085-8571-4564-bd9b-14110836e3a8","endTime":"1687317219798","startTime":"1687317099111"}},"vmCurrentNumber":3,"vmExecuteMessage":"no need to scale vm","vmExecuteStatus":"SUCCESS","vmName":"OnlineChgHost","vmReplicasNumber":2}');



DROP TABLE IF EXISTS TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_1;
CREATE TABLE TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_1 (
     INDICATOR_ID            VARCHAR(256),
     DATA_TYPE               VARCHAR(256) not null,
     INDICATOR_VALUE         VARCHAR(256) not null,
     MEAS_UNIT_KEY           VARCHAR(256),
     MEAS_TYPE_KEY           VARCHAR(256),
     MO_TYPE                 VARCHAR(256),
     MEAS_OBJ_ORIGINAL_VALUE VARCHAR(256),
     DN                      VARCHAR(256),
     GROUP_ID                VARCHAR(256) not null,
     TASK_ID                 INTEGER,
     RECORD_TIME             VARCHAR(256) not null,
     STRATEGY_ID             INTEGER
);
insert into TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_1 (indicator_id, data_type, indicator_value, meas_unit_key, meas_type_key, mo_type, meas_obj_original_value, dn, group_id, task_id, record_time, strategy_id)
values ('1', '2', 2, 'CPUInterval', 'hwAMOSCpuUsageAverage', 'com.huawei.IRes.vm', null, null, 'OnlineChgHost', 1, 1697317200000, 1);
insert into TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_1 (indicator_id, data_type, indicator_value, meas_unit_key, meas_type_key, mo_type, meas_obj_original_value, dn, group_id, task_id, record_time, strategy_id)
values (null, '2', 0, 'CPUInterval', 'hwAMOSCpuUsageAverage', 'com.huawei.IRes.vm', null, null, 'OfflineChgHost', 1, 1697317200000, 1);
insert into TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_1 (indicator_id, data_type, indicator_value, meas_unit_key, meas_type_key, mo_type, meas_obj_original_value, dn, group_id, task_id, record_time, strategy_id)
values (null, '0', 0, 'CPUInterval', 'hwAMOSCpuUsageAverage', 'com.huawei.IRes.vm', null, null, 'OnlineChgHost', 1, 1687743147620, 1);
insert into TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_1 (indicator_id, data_type, indicator_value, meas_unit_key, meas_type_key, mo_type, meas_obj_original_value, dn, group_id, task_id, record_time, strategy_id)
values (null, '0', 1, 'CPUInterval', 'hwAMOSCpuUsageAverage', 'com.huawei.IRes.vm', null, null, 'OnlineChgHost', 1, 1687743147621, 1);
insert into TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_1 (indicator_id, data_type, indicator_value, meas_unit_key, meas_type_key, mo_type, meas_obj_original_value, dn, group_id, task_id, record_time, strategy_id)
values (null, '0', 1, 'CPUInterval', 'hwAMOSCpuUsageAverage', 'com.huawei.IRes.vm', null, null, 'OnlineChgHost', 1, 1687743147622, 1);
insert into TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_1 (indicator_id, data_type, indicator_value, meas_unit_key, meas_type_key, mo_type, meas_obj_original_value, dn, group_id, task_id, record_time, strategy_id)
values (null, '2', 1, 'CPUInterval', 'hwAMOSCpuUsageAverage', 'com.huawei.IRes.vm', null, null, 'OnlineChgHost', 1, 1687743547620, 1);

DROP TABLE IF EXISTS TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_7;
CREATE TABLE TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_7 (
                                                                   INDICATOR_ID            VARCHAR(256),
                                                                   DATA_TYPE               VARCHAR(256) not null,
                                                                   INDICATOR_VALUE         VARCHAR(256) not null,
                                                                   MEAS_UNIT_KEY           VARCHAR(256),
                                                                   MEAS_TYPE_KEY           VARCHAR(256),
                                                                   MO_TYPE                 VARCHAR(256),
                                                                   MEAS_OBJ_ORIGINAL_VALUE VARCHAR(256),
                                                                   DN                      VARCHAR(256),
                                                                   GROUP_ID                VARCHAR(256) not null,
                                                                   TASK_ID                 INTEGER,
                                                                   RECORD_TIME             VARCHAR(256) not null,
                                                                   STRATEGY_ID             INTEGER
);
insert into TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_7 (indicator_id, data_type, indicator_value, meas_unit_key, meas_type_key, mo_type, meas_obj_original_value, dn, group_id, task_id, record_time, strategy_id)
values ('1', '2', 2, 'CPUInterval', 'hwAMOSCpuUsageAverage', 'com.huawei.IRes.vm', null, null, 'OnlineChgHost', 1, 1697317200000, 1);
insert into TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_7 (indicator_id, data_type, indicator_value, meas_unit_key, meas_type_key, mo_type, meas_obj_original_value, dn, group_id, task_id, record_time, strategy_id)
values (null, '2', 1, 'CPUInterval', 'hwAMOSCpuUsageAverage', 'com.huawei.IRes.vm', null, null, 'OfflineChgHost', 1, 1697317200000, 1);

DROP TABLE IF EXISTS TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_HISTORY_DATA_1;
create table TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_HISTORY_DATA_1 (
    INDICATOR_ID            VARCHAR(256),
    DATA_TYPE               VARCHAR(256) not null,
    INDICATOR_VALUE         VARCHAR(256) not null,
    MEAS_UNIT_KEY           VARCHAR(256),
    MEAS_TYPE_KEY           VARCHAR(256),
    MO_TYPE                 VARCHAR(256),
    MEAS_OBJ_ORIGINAL_VALUE VARCHAR(256),
    DN                      VARCHAR(256),
    GROUP_ID                VARCHAR(256) not null,
    TASK_ID                 INTEGER(4),
    RECORD_TIME             VARCHAR(256) not null
);
insert into TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_HISTORY_DATA_1 (indicator_id, data_type, indicator_value, meas_unit_key, meas_type_key, mo_type, meas_obj_original_value, dn, group_id, task_id, record_time)
values ('1', '2', '2', 'meas', 'type', 'motype', 'ori', 'dn', 'OnlineChgHost', 1, '1687743547620');
insert into TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_HISTORY_DATA_1 (indicator_id, data_type, indicator_value, meas_unit_key, meas_type_key, mo_type, meas_obj_original_value, dn, group_id, task_id, record_time)
values ('1', '1', '2', 'meas', 'type', 'motype', 'ori', 'dn', 'OnlineChgHost', 1, '1687743547620');
insert into TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_HISTORY_DATA_1 (indicator_id, data_type, indicator_value, meas_unit_key, meas_type_key, mo_type, meas_obj_original_value, dn, group_id, task_id, record_time)
values ('1', '0', '2', 'meas', 'type', 'motype', 'ori', 'dn', 'OnlineChgHost', 1, '1687743547620');

DROP TABLE IF EXISTS TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_RESIDUAL_DATA_1;
create table TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_RESIDUAL_DATA_1 (
    INDICATOR_ID            VARCHAR(256),
    DATA_TYPE               VARCHAR(256) not null,
    INDICATOR_VALUE         VARCHAR(256) not null,
    MEAS_UNIT_KEY           VARCHAR(256),
    MEAS_TYPE_KEY           VARCHAR(256),
    MO_TYPE                 VARCHAR(256),
    MEAS_OBJ_ORIGINAL_VALUE VARCHAR(256),
    DN                      VARCHAR(256),
    GROUP_ID                VARCHAR(256) not null,
    TASK_ID                 INTEGER(4),
    RECORD_TIME             VARCHAR(256) not null
);

DROP TABLE IF EXISTS TBL_AIOPS_SCHEDULE_RESOURCE_SAVE_RATE_ASSESSMENT_1;
create table TBL_AIOPS_SCHEDULE_RESOURCE_SAVE_RATE_ASSESSMENT_1 (
    INDICATOR_TYPE           INTEGER(4) not null,
    RESOURCE_SAVE_RATE_VALUE INTEGER(4) not null,
    INDICATOR_BASELINE       INTEGER(4) not null,
    ASSESSMENT_START_TIME    VARCHAR(256) not null
);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_999;
create table TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_999(
    UNITED_ID    VARCHAR(256) not null,
    COLLECT_TIME BIGINT(8) not null,
    STATIC_TYPE  INTEGER(4) not null,
    IS_PREDICT   BOOLEAN      not null,
    STATIC_VALUE DOUBLE(8) not null
);
insert into TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_999 (united_id, collect_time, static_type, is_predict, static_value)
values ('unitedId', 1687744147620, 1, true, 1);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_EXECUTION_RESULT;
CREATE TABLE TBL_AIOPS_TASK_EXECUTION_RESULT (
                                                 TASK_ID INTEGER NOT NULL ,
                                                 START_TIME BIGINT NOT NULL ,
                                                 END_TIME BIGINT,
                                                 STATUS VARCHAR NOT NULL ,
                                                 TASK_PERIOD_TYPE VARCHAR NOT NULL,
                                                 PARTIAL_RECORDS VARCHAR
);
insert into TBL_AIOPS_TASK_EXECUTION_RESULT (task_id, start_time, end_time, status, task_period_type, partial_records)
values (1, '137123674487', '137123674487', 'predicting', 'predicting', null);
insert into TBL_AIOPS_TASK_EXECUTION_RESULT (task_id, start_time, end_time, status, task_period_type, partial_records)
values (2, '1687849440252', '1687849440252', 'predicting', 'predicting', null);
insert into TBL_AIOPS_TASK_EXECUTION_RESULT (task_id, start_time, end_time, status, task_period_type, partial_records)
values (3, '1687854909748', '1687854909748', 'predicting', 'predicting', null);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_SINGLE_INDICATOR_PREDICT_RESULT_2;
create table TBL_AIOPS_TASK_SINGLE_INDICATOR_PREDICT_RESULT_2
(
    INDICATOR_ID    VARCHAR(1024) not null,
    COLLECT_TIME    BIGINT(8) not null,
    INDICATOR_VALUE DOUBLE(8) not null,
    CONSTRAINT TBL_AIOPS_TASK_SINGLE_INDICATOR_PREDICT_RESULT_2 PRIMARY KEY (COLLECT_TIME)
);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_3;
create table TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_3
(
    UNITED_ID    VARCHAR(256) not null,
    COLLECT_TIME BIGINT(8) not null,
    STATIC_TYPE  INTEGER(4) not null,
    IS_PREDICT   BOOLEAN    not null,
    STATIC_VALUE DOUBLE(8) not null,
    CONSTRAINT TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_3 PRIMARY KEY (COLLECT_TIME)
);

DROP TABLE IF EXISTS TBL_AIOPS_DATASOURCE;
create table TBL_AIOPS_DATASOURCE
(
    ID INTEGER AUTO_INCREMENT NOT NULL,
    TYPE            VARCHAR(256),
    NAME            VARCHAR(256),
    DESCRIPTION     VARCHAR(256),
    RECENTLY_TIME   BIGINT(8) not null,
    IP              VARCHAR(256),
    PORT            VARCHAR(256),
    USER_NAME       VARCHAR(256),
    PASSWD          VARCHAR(256),
    SERVICE_ID      VARCHAR(256),
    SID             VARCHAR(256),
    STATUS          BOOLEAN,
    CREATE_USER     VARCHAR(256),
    TENANT_ID       VARCHAR(256),
    DATASOURCE_TYPE INTEGER(4)
);
INSERT INTO TBL_AIOPS_DATASOURCE (ID, TYPE, NAME, DESCRIPTION, RECENTLY_TIME, IP, PORT, USER_NAME, PASSWD, SERVICE_ID, SID, STATUS, CREATE_USER, TENANT_ID, DATASOURCE_TYPE) VALUES (100,'prometheus','pppr',null,1657184179331,'https://**************:30004/prometheus',null,'admin','AAAAHgAAAAAAAAABAhGTYwAAAAmG+1Q9isZnhSbpcDMRL8GyMugbQAWSVGZUMOQ1AAAAAAEAAAEAAAAAAAAAHUO1B7HkRey0wCLPMauZUENOZJ7b7h15EbxQlim/',null,null,true,'1,2',null,null);
INSERT INTO TBL_AIOPS_DATASOURCE (ID, TYPE, NAME, DESCRIPTION, RECENTLY_TIME, IP, PORT, USER_NAME, PASSWD, SERVICE_ID, SID, STATUS, CREATE_USER, TENANT_ID, DATASOURCE_TYPE) VALUES (101,'zenith','pppr',null,1657184179331,'https://**************:30004/prometheus',null,'admin','AAAAHgAAAAAAAAABAhGTYwAAAAmG+1Q9isZnhSbpcDMRL8GyMugbQAWSVGZUMOQ1AAAAAAEAAAEAAAAAAAAAHUO1B7HkRey0wCLPMauZUENOZJ7b7h15EbxQlim/',null,null,true,'1,2',null,null);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR;
CREATE TABLE TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR (
    INDICATOR_ID VARCHAR(1024) NOT NULL,
    BELONG_INDICATOR_ID VARCHAR(1024) NOT NULL,
    MEAS_UNIT_KEY VARCHAR(256),
    MEAS_UNIT_NAME VARCHAR(600),
    MEAS_TYPE_KEY VARCHAR(256),
    DN VARCHAR(256),
    DN_NAME VARCHAR(256),
    MO_TYPE VARCHAR(256),
    INDEX_ID VARCHAR(256),
    INDEX_NAME VARCHAR(600),
    DISPLAY_VALUE VARCHAR(600),
    ORIGINAL_VALUE VARCHAR(600),
    NIT VARCHAR(256),
    HAS_MEAS_OBJ VARCHAR(256),
    CHECKED_NET_ID VARCHAR(2048),
    PRIMARY KEY (INDICATOR_ID,BELONG_INDICATOR_ID)
);

DROP TABLE IF EXISTS MY_TABLES;
CREATE TABLE MY_TABLES (
    TABLE_NAME VARCHAR(256) NOT NULL
);

DROP TABLE IF EXISTS TBL_AIOPS_CONFIG_DATA;
CREATE TABLE TBL_AIOPS_CONFIG_DATA (
                                       CONFIG_ID INTEGER,
                                       CONFIG_NAME VARCHAR(256) NOT NULL,
                                       DATA_TYPE VARCHAR(256) NOT NULL,
                                       DEFAULT_VALUE VARCHAR(256) NOT NULL,
                                       REAL_VALUE VARCHAR(256),
                                       MAX_VALUE VARCHAR(256),
                                       MIN_VALUE VARCHAR(256)
);
INSERT INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES ('INTERVAL_QUERY_DELETED_PM_DATA','Long','7200000','7200000','86400000','1800');

DROP TABLE IF EXISTS TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL;
CREATE TABLE TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL (
    ID INTEGER AUTO_INCREMENT NOT NULL,
    TASK_ID INTEGER NOT NULL,
    INDICATOR_ID VARCHAR(1024) NOT NULL,
    START_TIME BIGINT NOT NULL,
    END_TIME BIGINT NOT NULL,
    CREATE_TIME BIGINT NOT NULL,
    CONSTRAINT TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL PRIMARY KEY (ID)
);

INSERT INTO TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL(ID, TASK_ID, INDICATOR_ID, START_TIME, END_TIME, CREATE_TIME) VALUES (1, 1, 'unitedId', 3, 4, 5);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_AUXILIARY_INDICATOR_AGGREGATE_RESULT_998;
CREATE TABLE TBL_AIOPS_TASK_AUXILIARY_INDICATOR_AGGREGATE_RESULT_998 (
    INDICATOR_ID VARCHAR(1024) NOT NULL,
    COLLECT_TIME BIGINT NOT NULL,
    KPI_VALUE DOUBLE NOT NULL,
    constraint TBL_AIOPS_TASK_AUXILIARY_INDICATOR_AGGREGATE_RESULT_998
    primary key (INDICATOR_ID, COLLECT_TIME)
);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_999;
CREATE TABLE TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_999 (
    UNITED_ID VARCHAR(256) NOT NULL,
    COLLECT_TIME BIGINT NOT NULL,
    STATIC_TYPE INTEGER NOT NULL, -- AVG 0; SUM 1; COUNT 2
    IS_PREDICT boolean, -- 历史或预测数据
    STATIC_VALUE DOUBLE NOT NULL,
    constraint TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_999
    primary key (COLLECT_TIME, UNITED_ID, STATIC_TYPE, IS_PREDICT)
);

insert INTO TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_999 (UNITED_ID,COLLECT_TIME,STATIC_TYPE,IS_PREDICT,STATIC_VALUE) VALUES ('unitedId', 1, 0, false, 1);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_998;
CREATE TABLE TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_998 (
    UNITED_ID VARCHAR(256) NOT NULL,
    COLLECT_TIME BIGINT NOT NULL,
    STATIC_TYPE INTEGER NOT NULL, -- AVG 0; SUM 1; COUNT 2
    IS_PREDICT boolean, -- 历史或预测数据
    STATIC_VALUE DOUBLE NOT NULL,
    constraint TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_998
    primary key (COLLECT_TIME, UNITED_ID, STATIC_TYPE, IS_PREDICT)
);
insert INTO TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_998 (UNITED_ID,COLLECT_TIME,STATIC_TYPE,IS_PREDICT,STATIC_VALUE) VALUES ('unitedId', 1, 0, false, 1);
insert INTO TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_998 (UNITED_ID,COLLECT_TIME,STATIC_TYPE,IS_PREDICT,STATIC_VALUE) VALUES ('unitedId', 1687760344317, 0, false, 1);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_995;
CREATE TABLE TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_995 (
    UNITED_ID VARCHAR(256) NOT NULL,
    COLLECT_TIME BIGINT NOT NULL,
    STATIC_TYPE INTEGER NOT NULL, -- AVG 0; SUM 1; COUNT 2
    IS_PREDICT boolean, -- 历史或预测数据
    STATIC_VALUE DOUBLE NOT NULL,
    constraint TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_995
    primary key (COLLECT_TIME, UNITED_ID, STATIC_TYPE, IS_PREDICT)
);
insert INTO TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_995 (UNITED_ID,COLLECT_TIME,STATIC_TYPE,IS_PREDICT,STATIC_VALUE) VALUES ('unitedId', 1, 0, false, 1);
insert INTO TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_995 (UNITED_ID,COLLECT_TIME,STATIC_TYPE,IS_PREDICT,STATIC_VALUE) VALUES ('unitedId', 1712129136533, 0, false, 1);

DROP TABLE IF EXISTS TBL_AIOPS_TASK_AUXILIARY_INDICATOR_AGGREGATE_RESULT_995;
CREATE TABLE TBL_AIOPS_TASK_AUXILIARY_INDICATOR_AGGREGATE_RESULT_995 (
    INDICATOR_ID VARCHAR(1024) NOT NULL,
    COLLECT_TIME BIGINT NOT NULL,
    KPI_VALUE DOUBLE NOT NULL,
    constraint TBL_AIOPS_TASK_AUXILIARY_INDICATOR_AGGREGATE_RESULT_995
    primary key (INDICATOR_ID, COLLECT_TIME)
);