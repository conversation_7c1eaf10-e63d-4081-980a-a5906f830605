<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.i2000.dvanalysisengineservice.dao.common.mapper.BusinessStrategyMapper">
    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.BusinessStrategy" id="businessStrategyEntity">
        <id column="ID" property="id" javaType="integer" jdbcType="INTEGER"/>
        <result column="STRATEGY_NAME" property="strategyName" javaType="String" jdbcType="VARCHAR"/>
        <result column="DESCRIPTION" property="description" javaType="String" jdbcType="VARCHAR"/>
        <result column="START_TIME" property="startTime" javaType="long" jdbcType="BIGINT"/>
        <result column="END_TIME" property="endTime" javaType="long" jdbcType="BIGINT"/>
        <result column="PRIORITY" property="priority" javaType="integer" jdbcType="INTEGER"/>
        <result column="STATUS" property="status" javaType="boolean" jdbcType="BOOLEAN"/>
        <result column="TERMINATE_TASK_STATUS" property="terminateTaskStatus" javaType="integer" jdbcType="INTEGER"/>
        <collection property="actionEntity" ofType="com.huawei.i2000.dvanalysisengineservice.model.StrategyActionEntity">
            <result column="STRATEGY_ID" property="strategyId" javaType="integer" jdbcType="INTEGER"/>
            <result column="ACTION_TYPE" property="actionType" javaType="integer" jdbcType="INTEGER"/>
            <result column="SELECT_TYPE" property="selectType" javaType="integer" jdbcType="INTEGER"/>
            <result column="TASK_TYPE" property="taskType" javaType="integer" jdbcType="INTEGER"/>
            <result column="TASK_ID" property="taskId" javaType="integer" jdbcType="INTEGER"/>
            <result column="TASK_NAME" property="taskName" javaType="String" jdbcType="VARCHAR"/>
        </collection>
    </resultMap>

    <sql id="paging">
        <choose>
            <when test="param == null" />
            <when test="param.paging == null" />
            <otherwise>
                limit (#{param.paging.pageNumber} - 1) * #{param.paging.pageSize}, #{param.paging.pageSize}
            </otherwise>
        </choose>
    </sql>

    <insert id="insertStrategy" useGeneratedKeys="true" keyProperty="id">
        insert into TBL_AIOPS_BUSINESS_STRATEGY(ID, STRATEGY_NAME, DESCRIPTION, START_TIME, END_TIME, PRIORITY, STATUS)
        values (#{strategy.id}, #{strategy.strategyName}, #{strategy.description}, #{strategy.startTime}, #{strategy.endTime},
                #{strategy.priority}, #{strategy.status})
    </insert>

    <update id="updateStrategy">
        update  TBL_AIOPS_BUSINESS_STRATEGY
        set DESCRIPTION = #{strategy.description},
            START_TIME = #{strategy.startTime},
            END_TIME = #{strategy.endTime},
            PRIORITY = #{strategy.priority},
            STATUS = #{strategy.status}
        where ID = #{strategy.id}
    </update>

    <delete id="deleteStrategyById">
        delete from TBL_AIOPS_BUSINESS_STRATEGY where ID = #{id}
    </delete>

    <select id="getStrategyList" resultMap="businessStrategyEntity">
        select * from TBL_AIOPS_BUSINESS_STRATEGY
        <where>
            <if test="param.strategyName != null and param.strategyName != ''">
                AND STRATEGY_NAME like CONCAT('%', #{param.strategyName}, '%') escape '|'
            </if>
            <if test="param.status != null">
                AND STATUS = #{param.status}
            </if>
            <if test="param.currentTime != null">
                AND START_TIME <![CDATA[ <= ]]> #{param.currentTime}
                AND (END_TIME is null or END_TIME <![CDATA[ >= ]]> #{param.currentTime})
            </if>
            <if test="param.priority != null">
                AND PRIORITY = #{param.priority}
            </if>
        </where>
        order by ID DESC
        <include refid="paging"></include>
    </select>

    <select id="getStrategyCount" resultType="java.lang.Integer">
        select COUNT(ID) from TBL_AIOPS_BUSINESS_STRATEGY
        <where>
            <if test="param.strategyName != null and param.strategyName != ''">
                AND STRATEGY_NAME like CONCAT('%' , #{param.strategyName} , '%') escape '|'
            </if>
            <if test="param.status != null">
                AND STATUS = #{param.status}
            </if>
            <if test="param.currentTime != null">
                AND START_TIME <![CDATA[ <= ]]> #{param.currentTime}
                AND (END_TIME is null or END_TIME <![CDATA[ >= ]]> #{param.currentTime})
            </if>
            <if test="param.priority != null">
                AND PRIORITY = #{param.priority}
            </if>
        </where>
    </select>

    <select id="getStrategyById" resultMap="businessStrategyEntity">
        select * from TBL_AIOPS_BUSINESS_STRATEGY s
                          left join TBL_AIOPS_BUSINESS_STRATEGY_ACTION sa on s.ID = sa.STRATEGY_ID
        where s.ID = #{id}
    </select>

    <select id="getStrategyByName" resultMap="businessStrategyEntity">
        select * from TBL_AIOPS_BUSINESS_STRATEGY s
                          left join TBL_AIOPS_BUSINESS_STRATEGY_ACTION sa on s.ID = sa.STRATEGY_ID
        where s.STRATEGY_NAME = #{strategyName}
    </select>

    <update id="activateStrategy">
        update TBL_AIOPS_BUSINESS_STRATEGY
        set STATUS = #{status}
        where ID = #{id}
    </update>

    <select id="getEffectiveStrategy" resultMap="businessStrategyEntity">
        select * from TBL_AIOPS_BUSINESS_STRATEGY s left join TBL_AIOPS_BUSINESS_STRATEGY_ACTION sa on s.ID = sa.STRATEGY_ID
        where s.STATUS = true
          AND s.START_TIME <![CDATA[ <= ]]> #{currentTime}
          AND (s.END_TIME is null or s.END_TIME <![CDATA[ >= ]]> #{currentTime})
        order by s.PRIORITY DESC
    </select>

    <select id="getTerminateStrategy" resultMap="businessStrategyEntity">
        select * from TBL_AIOPS_BUSINESS_STRATEGY s left join TBL_AIOPS_BUSINESS_STRATEGY_ACTION sa on s.ID = sa.STRATEGY_ID
        where s.STATUS = true
          AND s.START_TIME <![CDATA[ <= ]]> #{currentTime}
          AND (s.END_TIME is null or s.END_TIME <![CDATA[ >= ]]> #{currentTime})
          AND s.TERMINATE_TASK_STATUS = 0
        order by s.PRIORITY DESC
    </select>

    <insert id="insertStrategyAction">
        insert into TBL_AIOPS_BUSINESS_STRATEGY_ACTION(STRATEGY_ID, ACTION_TYPE, SELECT_TYPE, TASK_TYPE, TASK_ID, TASK_NAME) values
        <foreach collection="actions" item="action" separator=",">
            (#{action.strategyId}, #{action.actionType}, #{action.selectType}, #{action.taskType}, #{action.taskId}, #{action.taskName})
        </foreach>
    </insert>

    <delete id="deleteStrategyActionByStrategyId">
        delete from TBL_AIOPS_BUSINESS_STRATEGY_ACTION where STRATEGY_ID = #{strategyId}
    </delete>

    <delete id="deleteStrategyActionByTaskId">
        delete from TBL_AIOPS_BUSINESS_STRATEGY_ACTION where SELECT_TYPE = 2 AND TASK_ID = #{taskId}
    </delete>

    <update id="updateTerminateTaskStatus">
        <foreach collection="pairs" item="pair" index="index" separator="">
            update TBL_AIOPS_BUSINESS_STRATEGY
            <set>
                TERMINATE_TASK_STATUS = #{pair.value}
            </set>
            where ID = #{pair.key};
        </foreach>
    </update>
</mapper>