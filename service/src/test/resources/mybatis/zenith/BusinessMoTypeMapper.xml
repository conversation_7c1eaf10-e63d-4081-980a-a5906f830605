<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.i2000.dvtoposervice.dao.mapper.service.BusinessMoTypeMapper">

    <resultMap type="com.huawei.i2000.dvtoposervice.model.Indicator" id="moTypeIndicatorMap">
        <id property="indicatorId" column="INDICATOR_ID" />
        <result property="measUnitKey" column="MEAS_UNIT_KEY" />
        <result property="measUnitName" column="MEAS_UNIT_NAME" />
        <result property="measTypeKey" column="MEAS_TYPE_KEY" />
        <result property="dn" column="DN" />
        <result property="indexName" column="INDEX_NAME" />
        <result property="moType" column="MO_TYPE" />
        <result property="displayValue" column="DISPLAY_VALUE" />
        <result property="originalValue" column="ORIGINAL_VALUE" />
        <result property="unit" column="UNIT" />
        <result property="dnName" column="DN_NAME" />
        <result property="indexId" column="INDEX_ID" />
        <result property="instanceId" column="INSTANCE_ID"/>
        <result property="indicatorDisplayType" column="INDICATOR_DISPLAY_TYPE"/>
    </resultMap>



    <resultMap type="com.huawei.i2000.dvtoposervice.model.VmData" id="vmDataMap">
        <result property="dnId" column="DN" />
    </resultMap>

    <resultMap type="com.huawei.i2000.dvtoposervice.model.PodData" id="podDataMap">
        <result property="dnId" column="DN" />
    </resultMap>

    <resultMap type="com.huawei.i2000.dvtoposervice.business.database.BusinessStepModelDB"
               id="businessStepResult">
        <result property="id" column="ID"/>
        <result property="stepId" column="STEP_ID"/>
        <result property="grayPercentage" column="GRAY_PERCENTAGE"/>
        <result property="grayDisplayFlag" column="GRAY_DISPLAY_FLAG"/>
    </resultMap>


    <select id="queryMoTypeIndicatorByIndicatorId" resultMap="moTypeIndicatorMap">
        SELECT INDICATOR_ID,MEAS_UNIT_KEY,MEAS_UNIT_NAME,MEAS_TYPE_KEY,DN,INDEX_NAME,MO_TYPE,DISPLAY_VALUE,
               ORIGINAL_VALUE,UNIT,DN_NAME,INDEX_ID,INSTANCE_ID
        FROM TBL_TOPO_BUSINESS_INDICATOR
        WHERE INDICATOR_ID = #{indicatorId}
        AND INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryMoTypeIndicatorByIndicatorIdTimeLine" resultMap="moTypeIndicatorMap" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        SELECT INDICATOR_ID,MEAS_UNIT_KEY,MEAS_UNIT_NAME,MEAS_TYPE_KEY,DN,INDEX_NAME,MO_TYPE,DISPLAY_VALUE,
               ORIGINAL_VALUE,UNIT,DN_NAME,INDEX_ID,INSTANCE_ID
        FROM ^{tableName}
        WHERE INDICATOR_ID = #{indicatorId}
          AND INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryMoTypeIndicatorByInstanceId" resultMap="moTypeIndicatorMap">
        SELECT INDICATOR_ID,MEAS_UNIT_KEY,MEAS_UNIT_NAME,MEAS_TYPE_KEY,DN,INDEX_NAME,MO_TYPE,DISPLAY_VALUE,
               ORIGINAL_VALUE,UNIT,DN_NAME,INDEX_ID,INSTANCE_ID,INDICATOR_DISPLAY_TYPE,INDICATOR_SORT_NUMBER
        FROM TBL_TOPO_BUSINESS_INDICATOR
        WHERE INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryMoTypeIndicatorByInstanceIdTimeLine" resultMap="moTypeIndicatorMap" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        SELECT INDICATOR_ID,MEAS_UNIT_KEY,MEAS_UNIT_NAME,MEAS_TYPE_KEY,DN,INDEX_NAME,MO_TYPE,DISPLAY_VALUE,
               ORIGINAL_VALUE,UNIT,DN_NAME,INDEX_ID,INSTANCE_ID,INDICATOR_DISPLAY_TYPE,INDICATOR_SORT_NUMBER
        FROM ^{tableName}
        WHERE INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryMoTypeIndicatorListByInstanceIdList" resultMap="moTypeIndicatorMap">
        SELECT INDICATOR_ID,MEAS_UNIT_KEY,MEAS_UNIT_NAME,MEAS_TYPE_KEY,DN,INDEX_NAME,MO_TYPE,DISPLAY_VALUE,
               ORIGINAL_VALUE,UNIT,DN_NAME,INDEX_ID,INSTANCE_ID
        FROM TBL_TOPO_BUSINESS_INDICATOR
        WHERE INSTANCE_ID in
        <foreach collection="instanceIdList" open="(" close=")" item="instanceId" separator=",">
            #{instanceId}
        </foreach>
    </select>

    <select id="queryMoTypeIndicatorListByInstanceIdListTimeLine" resultMap="moTypeIndicatorMap" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        SELECT INDICATOR_ID,MEAS_UNIT_KEY,MEAS_UNIT_NAME,MEAS_TYPE_KEY,DN,INDEX_NAME,MO_TYPE,DISPLAY_VALUE,
        ORIGINAL_VALUE,UNIT,DN_NAME,INDEX_ID,INSTANCE_ID
        FROM ^{tableName}
        WHERE INSTANCE_ID in
        <foreach collection="instanceIdList" open="(" close=")" item="instanceId" separator=",">
            #{instanceId}
        </foreach>
    </select>

    <select id="queryVmDnByInstanceId" resultMap="vmDataMap">
        SELECT DN
        FROM TBL_TOPO_BUSINESS_INSTANCE_MODEL
        WHERE INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryPodDnByInstanceId" resultMap="podDataMap">
        SELECT DN
        FROM TBL_TOPO_BUSINESS_INSTANCE_MODEL
        WHERE INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryVmInstanceIdByPodInstanceId" resultType="Integer">
        SELECT TARGET_INSTANCE_ID
        FROM TBL_TOPO_BUSINESS_INSTANCE_RELATION
        WHERE INSTANCE_ID = #{instanceId}
          AND RELATION_TYPE = 1
    </select>

    <select id="queryPodInstanceIdByVmInstanceId" resultType="Integer">
        SELECT INSTANCE_ID
        FROM TBL_TOPO_BUSINESS_INSTANCE_RELATION
        WHERE TARGET_INSTANCE_ID = #{instanceId}
          AND RELATION_TYPE = 1
    </select>

    <select id="queryPodInstanceIdByVmInstanceIdAndTimeLine" resultType="Integer"
            lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        SELECT INSTANCE_ID
        FROM ^{instanceRelationTableName}
        WHERE TARGET_INSTANCE_ID = #{instanceId}
          AND RELATION_TYPE = 1
    </select>

    <update id="savePipelineStatus" parameterType="com.huawei.i2000.dvtoposervice.business.database.BusinessStepModelDB">
        REPLACE
        INTO TBL_TOPO_BUSINESS_DEPCLOUD_PIPELINE_STEP (ID, STEP_ID, GRAY_PERCENTAGE)
        VALUES (
        #{businessStepModelDB.id},
        #{businessStepModelDB.stepId},
        #{businessStepModelDB.grayPercentage}
        )
    </update>

    <update id="savePipelineDisplay">
        UPDATE TBL_TOPO_BUSINESS_DEPCLOUD_PIPELINE_STEP
        SET GRAY_DISPLAY_FLAG = #{grayDisplayFlag}
    </update>

    <select id="queryPipelineStatus" resultMap="businessStepResult">
        SELECT STEP_ID, GRAY_PERCENTAGE, GRAY_DISPLAY_FLAG
        FROM TBL_TOPO_BUSINESS_DEPCLOUD_PIPELINE_STEP
        WHERE ID = 1
    </select>

</mapper>