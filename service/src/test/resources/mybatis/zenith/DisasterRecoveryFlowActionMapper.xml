<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.i2000.dvanalysisengineservice.dao.disasterrecovery.mapper.DisasterRecoveryFlowActionMapper">
    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.FlowActionModel" id="flowAction">
        <id column="FLOW_ID" property="flowId" javaType="String" jdbcType="VARCHAR"/>
        <result column="GROUP_ID" property="groupId" javaType="String" jdbcType="VARCHAR"/>
        <result column="FLOW_GROUP" property="flowGroup" javaType="String" jdbcType="VARCHAR"/>
        <result column="FLOW_NAME" property="flowName" javaType="String" jdbcType="VARCHAR"/>
        <result column="NODE_ID" property="nodeId" javaType="String" jdbcType="VARCHAR"/>
        <result column="ATOM_ID" property="atomId" javaType="String" jdbcType="VARCHAR"/>
        <result column="HOST_CONTENT" property="hostContent" javaType="String" jdbcType="CLOB"/>
        <result column="TYPE" property="type" javaType="String" jdbcType="VARCHAR"/>
    </resultMap>


    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO TBL_AIOPS_DISASTER_RECOVERY_FLOW
        (
        FLOW_ID, GROUP_ID,
        NODE_ID,
        ATOM_ID, FLOW_NAME,FLOW_GROUP,HOST_CONTENT,TYPE
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.flowId,jdbcType=VARCHAR},#{item.groupId,jdbcType=VARCHAR},#{item.nodeId,jdbcType=VARCHAR},#{item.atomId,jdbcType=VARCHAR},#{item.flowName,jdbcType=VARCHAR},#{item.flowGroup,jdbcType=VARCHAR},#{item.hostContent,jdbcType=CLOB},#{item.type,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <select id="queryFlowActionByType0" resultMap="flowAction" >
        SELECT
            FLOW_ID, FLOW_GROUP, FLOW_NAME,
            GROUP_ID,NODE_ID,ATOM_ID,HOST_CONTENT,TYPE
        FROM  TBL_AIOPS_DISASTER_RECOVERY_FLOW
        WHERE
        GROUP_ID = #{groupId}
        AND TYPE = '0'
    </select>

    <select id="queryFlowActionByType1" resultMap="flowAction" >
        SELECT
            FLOW_ID, FLOW_GROUP, FLOW_NAME,
            GROUP_ID,NODE_ID,ATOM_ID,HOST_CONTENT,TYPE
        FROM  TBL_AIOPS_DISASTER_RECOVERY_FLOW
        WHERE
        GROUP_ID = #{groupId}
        AND TYPE = '1'
    </select>

    <delete id="deleteFlowActionById" parameterType="java.lang.String">
        delete from TBL_AIOPS_DISASTER_RECOVERY_FLOW where GROUP_ID= #{groupId}
    </delete>

    <delete id="deleteFlowActionByIds" parameterType="java.util.List">
        delete from TBL_AIOPS_DISASTER_RECOVERY_FLOW
        where
        GROUP_ID IN
        <foreach collection="list" index="index" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>