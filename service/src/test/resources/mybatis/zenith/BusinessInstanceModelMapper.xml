<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.huawei.i2000.dvtoposervice.dao.mapper.service.BusinessInstanceModelMapper">

    <resultMap type="com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB"
               id="businessInstanceResult">
        <result property="instanceId" column="INSTANCE_ID"/>
        <result property="modelId" column="MODEL_ID"/>
        <result property="modelType" column="MODEL_TYPE"/>
        <result property="dn" column="DN"/>
        <result property="version" column="VERSION"/>
        <result property="targetInstanceId" column="TARGET_INSTANCE_ID"/>
        <result property="solutionName" column="SOLUTION_NAME"/>
        <collection property="attrDBList" javaType="java.util.List"
                    ofType="com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB">
            <id property="attrName" column="ATTRNAME"/>
            <result property="attrClass" column="ATTRCLASS"/>
            <result property="attrValue" column="ATTRVALUE"/>
        </collection>
        <collection property="staticAttrDBList" javaType="java.util.List"
                    ofType="com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModelExtentAttr">
            <id property="staticAttrName" column="STATIC_ATTR_NAME"/>
            <result property="staticAttrType" column="STATIC_ATTR_TYPE"/>
            <result property="staticAttrValue" column="STATIC_ATTR_VALUE"/>
        </collection>
    </resultMap>

    <resultMap id="businessCommonModelWithInstance"
               type="com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModelWithInstance">
        <id property="instanceId" column="INSTANCE_ID"/>
        <result property="modelName" column="MODEL_NAME"/>
        <result property="modelId" column="MODEL_ID"/>
        <result property="dn" column="DN"/>
        <result property="modelType" column="MODEL_TYPE"/>
        <result property="version" column="VERSION"/>
    </resultMap>

    <resultMap id="businessExtentAttr" type="com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB">
        <id property="instanceId" column="INSTANCE_ID"/>
        <result property="attrName" column="ATTRNAME"/>
        <result property="attrClass" column="ATTRCLASS"/>
        <result property="attrValue" column="ATTRVALUE"/>
    </resultMap>

    <resultMap id="mapToReservedAttr" type="com.huawei.i2000.dvtoposervice.bean.businesstopo.ReservedAttribute">
        <id property="dn" column="DN"/>
        <association property="extentAttr"
                     javaType="com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB">
            <id property="attrName" column="ATTRNAME"/>
            <result property="attrClass" column="ATTRCLASS"/>
            <result property="attrValue" column="ATTRVALUE"/>
        </association>
    </resultMap>

    <select id="queryHistoryAttrValueByInstanceIds" resultMap="businessExtentAttr"
            lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select INSTANCE_ID,
               ATTRNAME,
               ATTRCLASS,
               ATTRVALUE
        from ^{historyTableName}
        where ATTRNAME = #{attrName}
        and INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
    </select>

    <select id="queryInstanceByInstanceId" resultMap="businessInstanceResult">
        select model.SOLUTION_NAME,
               ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE,
               modelExt.STATIC_ATTR_NAME,
               modelExt.STATIC_ATTR_TYPE,
               modelExt.STATIC_ATTR_VALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
                 left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR modelExt on ins.MODEL_ID = modelExt.MODEL_ID
                 left join TBL_TOPO_BUSINESS_COMMON_MODEL model on ins.MODEL_ID = model.MODEL_ID
        where ins.INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryNextLevelInstance" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE,
               model.STATIC_ATTR_NAME,
               model.STATIC_ATTR_TYPE,
               model.STATIC_ATTR_VALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
                 left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.INSTANCE_ID in
              (select INSTANCE_ID
               from TBL_TOPO_BUSINESS_INSTANCE_RELATION
               where TARGET_INSTANCE_ID = #{instanceId}
                 and RELATION_TYPE = 0)
    </select>

    <select id="queryNextLevelInstanceBatch" resultMap="businessInstanceResult" timeout="120">
        select ins.INSTANCE_ID,
        ins.MODEL_ID,
        ins.MODEL_TYPE,
        ins.DN,
        ins.VERSION,
        attr.ATTRNAME,
        attr.ATTRCLASS,
        attr.ATTRVALUE,
        model.STATIC_ATTR_NAME,
        model.STATIC_ATTR_TYPE,
        model.STATIC_ATTR_VALUE,
        relation.TARGET_INSTANCE_ID
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
        left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        left join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on ins.INSTANCE_ID = relation.INSTANCE_ID
        where
        relation.RELATION_TYPE = 0
        and relation.TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
    </select>

    <select id="queryUpLevelInstanceBatch" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
        ins.MODEL_ID,
        ins.MODEL_TYPE,
        ins.DN,
        ins.VERSION,
        attr.ATTRNAME,
        attr.ATTRCLASS,
        attr.ATTRVALUE,
        model.STATIC_ATTR_NAME,
        model.STATIC_ATTR_TYPE,
        model.STATIC_ATTR_VALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
        left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.INSTANCE_ID in
        (
        select TARGET_INSTANCE_ID
        from TBL_TOPO_BUSINESS_INSTANCE_RELATION relation
        where relation.RELATION_TYPE = 0
        and relation.INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator=", " close=")">
            #{instanceId}
        </foreach>
        )
    </select>

    <select id="queryNextLevelInstanceBySelectType" resultMap="businessInstanceResult" timeout="120">
        select ins.INSTANCE_ID,
        ins.MODEL_ID,
        ins.MODEL_TYPE,
        ins.DN,
        ins.VERSION,
        attr.ATTRNAME,
        attr.ATTRCLASS,
        attr.ATTRVALUE,
        model.STATIC_ATTR_NAME,
        model.STATIC_ATTR_TYPE,
        model.STATIC_ATTR_VALUE,
        relation.TARGET_INSTANCE_ID
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
        left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        left join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on ins.INSTANCE_ID = relation.INSTANCE_ID
        where
        relation.RELATION_TYPE = #{type}
        and relation.TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
    </select>

    <select id="queryNextLevelInstanceByTimeLineBatch" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver" timeout="120">
        select ins.INSTANCE_ID,
        ins.MODEL_ID,
        ins.MODEL_TYPE,
        ins.DN,
        ins.VERSION,
        attr.ATTRNAME,
        attr.ATTRCLASS,
        attr.ATTRVALUE,
        model.STATIC_ATTR_NAME,
        model.STATIC_ATTR_TYPE,
        model.STATIC_ATTR_VALUE,
        relation.TARGET_INSTANCE_ID
        from ^{instanceModelTableName} ins
        left join ^{instanceExternAttrTableName} attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        left join ^{instanceRelationTableName} relation on ins.INSTANCE_ID = relation.INSTANCE_ID
        where
        relation.RELATION_TYPE = 0
        and relation.TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
    </select>

    <select id="queryNextLevelInstanceByDeployRealtion" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
        ins.MODEL_ID,
        ins.MODEL_TYPE,
        ins.DN,
        ins.VERSION,
        attr.ATTRNAME,
        attr.ATTRCLASS,
        attr.ATTRVALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
        left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        where ins.INSTANCE_ID in
        (select INSTANCE_ID from TBL_TOPO_BUSINESS_INSTANCE_RELATION
        where TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
        and RELATION_TYPE = 1
        )
    </select>


    <select id="queryUpLevelInstance" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        where ins.INSTANCE_ID =
              (select TARGET_INSTANCE_ID
               from TBL_TOPO_BUSINESS_INSTANCE_RELATION
               where INSTANCE_ID = #{instanceId}
                 and RELATION_TYPE = 0)
    </select>

    <select id="queryAllInstanceByTopId" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on ins.INSTANCE_ID = relation.INSTANCE_ID
        where relation.RELATION_TYPE = 2
          and TARGET_INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryAllInstanceBySiteId" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on ins.INSTANCE_ID = relation.INSTANCE_ID
        where relation.RELATION_TYPE = 3
          and TARGET_INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryAllInstanceOfType" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE,
               model.STATIC_ATTR_NAME,
               model.STATIC_ATTR_TYPE,
               model.STATIC_ATTR_VALUE,
               cm.SOLUTION_NAME
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
        left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        left join TBL_TOPO_BUSINESS_COMMON_MODEL cm on cm.MODEL_ID = ins.MODEL_ID
        left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.MODEL_TYPE = #{type}
        <if test="solutionName != null">
            and cm.SOLUTION_NAME = #{solutionName}
        </if>
    </select>

    <select id="queryHistoryAllInstanceOfType" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE,
               model.STATIC_ATTR_NAME,
               model.STATIC_ATTR_TYPE,
               model.STATIC_ATTR_VALUE
        from ^{instanceModelTableName} ins
            left join ^{extentTableName} attr on ins.INSTANCE_ID = attr.INSTANCE_ID
            left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.MODEL_TYPE = #{type}
    </select>

    <select id="queryAllInstanceBySiteAndTypeWithoutAttr" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.STATIC_ATTR_NAME,
               attr.STATIC_ATTR_TYPE,
               attr.STATIC_ATTR_VALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on ins.INSTANCE_ID = relation.INSTANCE_ID
                 left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR attr on ins.MODEL_ID = attr.MODEL_ID
        where relation.RELATION_TYPE = 3
          and relation.TARGET_INSTANCE_ID = #{siteInsId}
          and ins.MODEL_TYPE = #{type}
    </select>

    <select id="queryAllInstanceBySiteWithoutAttr" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.STATIC_ATTR_NAME,
               attr.STATIC_ATTR_TYPE,
               attr.STATIC_ATTR_VALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on ins.INSTANCE_ID = relation.INSTANCE_ID
                 left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR attr on ins.MODEL_ID = attr.MODEL_ID
        where relation.RELATION_TYPE = 3
          and relation.TARGET_INSTANCE_ID = #{siteInsId}
    </select>

    <select id="queryHisAllInstanceOfTypeWithOutExtent" resultMap="businessInstanceResult"
            lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select INSTANCE_ID,
               MODEL_ID,
               MODEL_TYPE,
               DN,
               VERSION
        from ^{instanceModelTableName}
        where MODEL_TYPE = #{type}
    </select>

    <select id="queryInstanceByDns" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
        ins.MODEL_ID,
        ins.DN,
        ins.MODEL_TYPE,
        ins.VERSION,
        attr.ATTRNAME,
        attr.ATTRCLASS,
        attr.ATTRVALUE,
        model.STATIC_ATTR_NAME,
        model.STATIC_ATTR_TYPE,
        model.STATIC_ATTR_VALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
        left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        <if test="dns != null and dns.size > 0">
            where ins.DN in
            <foreach collection="dns" item="dn" open="(" close=")" separator=",">
                #{dn}
            </foreach>
        </if>
    </select>

    <select id="queryUnDeployInstanceByDns" resultMap="businessInstanceResult">
        SELECT *
        FROM TBL_TOPO_BUSINESS_INSTANCE_MODEL model
        LEFT JOIN TBL_TOPO_BUSINESS_INSTANCE_RELATION relation
        ON model.INSTANCE_ID = relation.INSTANCE_ID
        AND relation.RELATION_TYPE = 1
        WHERE relation.INSTANCE_ID IS NULL
        <if test="dns != null and dns.size > 0">
            and model.DN in
            <foreach collection="dns" item="dn" open="(" close=")" separator=",">
                #{dn}
            </foreach>
        </if>
    </select>

    <select id="queryInstanceByDnsTimeLine" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ins.INSTANCE_ID,
        ins.MODEL_ID,
        ins.DN,
        ins.MODEL_TYPE,
        ins.VERSION,
        attr.ATTRNAME,
        attr.ATTRCLASS,
        attr.ATTRVALUE,
        model.STATIC_ATTR_NAME,
        model.STATIC_ATTR_TYPE,
        model.STATIC_ATTR_VALUE
        from ^{instanceModelTableName} ins
        left join ^{instanceExternAttrTableName} attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        <if test="dns != null and dns.size > 0">
            where ins.DN in
            <foreach collection="dns" item="dn" open="(" close=")" separator=",">
                #{dn}
            </foreach>
        </if>
    </select>

    <select id="queryInstanceByDnsWithoutAttr" resultMap="businessInstanceResult">
        select INSTANCE_ID,
        MODEL_ID,
        DN,
        MODEL_TYPE,
        VERSION
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL
        <if test="dns != null and dns.size > 0">
            where DN in
            <foreach collection="dns" item="dn" open="(" close=")" separator=",">
                #{dn}
            </foreach>
        </if>
    </select>

    <update id="updateInstanceAttr">
        REPLACE INTO TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR
        (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE)
        VALUES
        <foreach collection="attrList" index="index" item="attr" separator=",">
            (#{attr.instanceId}, #{attr.attrName}, #{attr.attrClass}, #{attr.attrValue})
        </foreach>
    </update>

    <select id="queryInstanceIdsByTypeModelId" resultType="java.lang.Integer">
        select INSTANCE_ID
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL
        where MODEL_ID = #{modelId}
    </select>

    <select id="getSiteIdFromInstanceAttr" resultType="java.lang.String">
        SELECT ATTRVALUE
        FROM TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR
        WHERE INSTANCE_ID = #{instanceId}
          AND ATTRNAME = #{siteKey}
    </select>

    <insert id="insertInstance" useGeneratedKeys="true" keyProperty="businessInstanceModelDB.instanceId">
        insert into TBL_TOPO_BUSINESS_INSTANCE_MODEL (MODEL_ID, MODEL_TYPE, DN, VERSION)
        values (#{businessInstanceModelDB.modelId},
                #{businessInstanceModelDB.modelType},
                #{businessInstanceModelDB.dn},
                #{businessInstanceModelDB.version})
    </insert>

    <insert id="insertInstanceAttr">
        replace into TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR (INSTANCE_ID, ATTRNAME, ATTRCLASS, ATTRVALUE) values
        <foreach collection="attrs" item="attr" separator=",">
            (#{attr.instanceId}, #{attr.attrName}, #{attr.attrClass}, #{attr.attrValue})
        </foreach>
    </insert>

    <select id="queryInstanceListByIdList" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE,
               model.STATIC_ATTR_NAME,
               model.STATIC_ATTR_TYPE,
               model.STATIC_ATTR_VALUE
        from ^{instanceTableName} ins
        left join ^{instanceExternAttrTableName} attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
    </select>

    <select id="queryInstanceByDn" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE,
               model.STATIC_ATTR_NAME,
               model.STATIC_ATTR_TYPE,
               model.STATIC_ATTR_VALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
                 left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.DN = #{dn}
    </select>

    <select id="queryInstanceByDnsWithoutExtent" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.DN,
               ins.MODEL_TYPE,
               ins.VERSION
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
        <if test="dns != null and dns.size > 0">
            where ins.DN in
            <foreach collection="dns" item="dn" open="(" close=")" separator=",">
                #{dn}
            </foreach>
        </if>
    </select>

    <delete id="deleteInstance">
        delete from TBL_TOPO_BUSINESS_INSTANCE_MODEL where INSTANCE_ID in
        <foreach collection="instanceIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteInstanceAttr">
        delete from TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR where INSTANCE_ID in
        <foreach collection="instanceIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteInstanceAttrByName">
        delete from TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR where INSTANCE_ID in
        <foreach collection="instanceIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        and ATTRNAME = #{attrName}
    </delete>

    <delete id="deleteInstanceRelation">
        delete from TBL_TOPO_BUSINESS_INSTANCE_RELATION where INSTANCE_ID in
        <foreach collection="instanceIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        or TARGET_INSTANCE_ID in
        <foreach collection="instanceIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getInstanceIdByDn" resultType="java.lang.Integer">
        select INSTANCE_ID
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL
        where DN = #{dn}
    </select>

    <select id="queryAllCsnByInstanceIds" resultType="java.lang.String">
        select ATTRVALUE from TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR
        where ATTRNAME = 'AlarmCsn'
        <if test="instanceIds != null and instanceIds.size > 0">
            and INSTANCE_ID in
            <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
                #{instanceId}
            </foreach>
        </if>
    </select>

    <select id="queryAttrValueByInstanceIds" resultMap="businessExtentAttr">
        select INSTANCE_ID,
               ATTRNAME,
               ATTRCLASS,
               ATTRVALUE
        from TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR
        where INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
    </select>

    <select id="queryInstanceByRelationTypeRelation" resultMap="businessInstanceResult">
        select relation.INSTANCE_ID
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on relation.TARGET_INSTANCE_ID = ins.INSTANCE_ID
        where relation.RELATION_TYPE = #{relationType}
          AND relation.TARGET_INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryInstanceByRelationTypeRelationByTime" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select relation.INSTANCE_ID
        from ^{instanceTableName} ins
            left join ^{relationTableName} relation on relation.TARGET_INSTANCE_ID = ins.INSTANCE_ID
        where relation.RELATION_TYPE = #{relationType}
          AND relation.TARGET_INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryInsByRelationByLevel" resultMap="businessInstanceResult">
        select relation.INSTANCE_ID
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on relation.INSTANCE_ID = ins.INSTANCE_ID
        where relation.RELATION_TYPE = #{relationType}
          AND relation.TARGET_INSTANCE_ID = #{instanceId}
          AND ins.MODEL_TYPE = #{modelType}
    </select>

    <select id="queryInstancesByRelationType" resultMap="businessInstanceResult">
        select * from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
        left join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on relation.TARGET_INSTANCE_ID = ins.INSTANCE_ID
        where relation.RELATION_TYPE = 1
        and relation.INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
    </select>

    <select id="queryAllInstance" resultMap="businessInstanceResult">
        select *
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR ins_attr
                           on ins.instance_id = ins_attr.instance_id
    </select>

    <select id="queryRealVms" resultMap="businessInstanceResult">
        select * from TBL_TOPO_BUSINESS_INSTANCE_MODEL where MODEL_TYPE = 8
        and INSTANCE_ID in (select TARGET_INSTANCE_ID from TBL_TOPO_BUSINESS_INSTANCE_RELATION where RELATION_TYPE = 1)
    </select>

    <select id="queryInstanceByModelId" resultMap="businessInstanceResult">
        select *
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL
        where MODEL_ID = #{modelId}
    </select>

    <select id="queryHistoryInstanceByModelId" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select *
        from ^{instanceModelTableName}
        where MODEL_ID = #{modelId}
    </select>

    <select id="queryMMSiteInstance" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select attr.*
        from TBL_TOPO_BUSINESS_INSTANCE_RELATION relation
                 left join  TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr
                            on relation.TARGET_INSTANCE_ID = attr.INSTANCE_ID
        where relation.RELATION_TYPE = 3
          AND relation.INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryMMSiteInstanceByTime" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select attr.*
        from ^{relationTableName} relation
            left join ^{attrTableName} attr
        on relation.TARGET_INSTANCE_ID = attr.INSTANCE_ID
        where relation.RELATION_TYPE = 3
          AND relation.INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryInstanceWithAttrByModelId" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE,
               model.STATIC_ATTR_NAME,
               model.STATIC_ATTR_TYPE,
               model.STATIC_ATTR_VALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
                 left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.MODEL_ID = #{modelId}
    </select>

    <select id="queryInstanceWithAttrByModelIdTimeLine" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE,
               model.STATIC_ATTR_NAME,
               model.STATIC_ATTR_TYPE,
               model.STATIC_ATTR_VALUE
        from ^{instanceModelTableName} ins
            left join ^{instanceExternAttrTableName} attr on ins.INSTANCE_ID = attr.INSTANCE_ID
            left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.MODEL_ID = #{modelId}
    </select>

    <select id="queryNextLevelInstanceByInstanceAndModelId" resultMap="businessInstanceResult">
        select * from TBL_TOPO_BUSINESS_INSTANCE_MODEL model join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on relation.INSTANCE_ID = model.INSTANCE_ID
        where relation.TARGET_INSTANCE_ID = #{instanceId}
          and RELATION_TYPE = 0 and model.MODEL_ID = #{modelId}
    </select>

    <select id="queryAllInstanceWithDn" resultMap="businessInstanceResult">
        select distinct ins.INSTANCE_ID,
                        ins.MODEL_ID,
                        ins.MODEL_TYPE,
                        ins.DN,
                        ins.VERSION
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
        where ins.INSTANCE_ID in (select distinct INSTANCE_ID
                                  from TBL_TOPO_BUSINESS_INSTANCE_RELATION
                                  union all
                                  select distinct TARGET_INSTANCE_ID
                                  from TBL_TOPO_BUSINESS_INSTANCE_RELATION)
          and ins.DN is not null
    </select>

    <update id="creatBackUpInstanceModel" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        create table ^{tableName} as select * from TBL_TOPO_BUSINESS_INSTANCE_MODEL
    </update>

    <update id="creatBackUpInstanceExtendAttr" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        create table ^{tableName} as select * from TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR
    </update>

    <update id="creatBackUpInstanceRelation" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        create table ^{tableName} as select * from TBL_TOPO_BUSINESS_INSTANCE_RELATION
    </update>

    <update id="creatBackUpInstanceIndicator" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        create table ^{tableName} as select * from TBL_TOPO_BUSINESS_INDICATOR
    </update>

    <select id="queryBackUpTimeLineTables" resultType="java.lang.String">
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_NAME LIKE 'TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_%'
    </select>

    <delete id="deleteBackUpTimeLineTable" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        DROP TABLE IF EXISTS ^{tableName}
    </delete>


    <delete id="clearInstanceByInstanceIdBatch">
        delete from TBL_TOPO_BUSINESS_INSTANCE_MODEL
        where INSTANCE_ID IN
        <foreach collection="instanceIdList" open="(" close=")" item="instanceId" separator=",">
            #{instanceId}
        </foreach>
    </delete>

    <delete id="clearAllInstanceExt">
        delete from TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR
        where INSTANCE_ID IN
        <foreach collection="instanceIdList" open="(" close=")" item="instanceId" separator=",">
            #{instanceId}
        </foreach>
    </delete>

    <select id="queryHistoryUpLevelInstance" resultMap="businessInstanceResult"
            lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE
        from ^{instanceTableName} ins
            left join ^{extentTableName} attr
        on ins.INSTANCE_ID = attr.INSTANCE_ID
        where ins.INSTANCE_ID =
            (select TARGET_INSTANCE_ID
            from ^{relationTableName}
            where INSTANCE_ID = #{instanceId}
          and RELATION_TYPE = 0)
    </select>


    <select id="queryHistoryInstanceIdsByTypeModelId" resultType="java.lang.Integer"
            lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select INSTANCE_ID
        from ^{instanceTableName}
        where MODEL_ID = #{modelId}
    </select>


    <select id="queryHistoryNextLevelInstances" resultMap="businessInstanceResult"
            lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ins.INSTANCE_ID,
        ins.MODEL_ID,
        ins.MODEL_TYPE,
        ins.DN,
        ins.VERSION,
        attr.ATTRNAME,
        attr.ATTRCLASS,
        attr.ATTRVALUE
        from ^{instanceTableName} ins
        left join ^{extentTableName} attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        where ins.INSTANCE_ID in
        (select INSTANCE_ID from ^{realetionTableName}
        where TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
        and RELATION_TYPE = 0
        )
    </select>


    <select id="queryHistoryAllCsnByInstanceIds" resultType="java.lang.String"
            lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ATTRVALUE from ^{extentTableName}
        where ATTRNAME = 'AlarmCsn'
        and INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
    </select>

    <select id="queryInstanceBeanByDns" resultMap="businessCommonModelWithInstance" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ins.INSTANCE_ID,
        ins.MODEL_ID,
        ins.MODEL_TYPE,
        ins.DN
        from ^{instanceTableName} ins
        where ins.dn in
        <foreach item="dn" collection="dnList" open="(" separator="," close=")">
            #{dn}
        </foreach>
    </select>

    <select id="queryInstanceByInstanceIdAndTimeLine" resultMap="businessInstanceResult"
            lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE,
               model.STATIC_ATTR_NAME,
               model.STATIC_ATTR_TYPE,
               model.STATIC_ATTR_VALUE
        from ^{instanceModelTableName} ins
            left join ^{instanceExternAttrTableName} attr
        on ins.INSTANCE_ID = attr.INSTANCE_ID
            left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryNextLevelInstanceByTimeLine" resultMap="businessInstanceResult"
            lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE,
               model.STATIC_ATTR_NAME,
               model.STATIC_ATTR_TYPE,
               model.STATIC_ATTR_VALUE
        from ^{instanceModelTableName} ins
            left join ^{instanceExternAttrTableName} attr
        on ins.INSTANCE_ID = attr.INSTANCE_ID
            left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.INSTANCE_ID in
            (select INSTANCE_ID from ^{instanceRelationTableName}
            where TARGET_INSTANCE_ID = #{instanceId}
          and RELATION_TYPE = 0)
    </select>

    <select id="getBusinessInstanceDbListByMoTypeId" resultMap="businessInstanceResult">
        select *
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL model
                 left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on model.instance_id = attr.instance_id
        where model.model_id = #{modelId}
    </select>

    <select id="queryAttrValue" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver"
            resultType="string">
        select ATTRVALUE
        from ^{tableName}
        where INSTANCE_ID = #{instanceId}
          and ATTRNAME = #{attrName}
    </select>

    <update id="updateAttrValue">
        update TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR
        set ATTRVALUE = #{attrValue}
        WHERE INSTANCE_ID = #{instanceId}
          AND ATTRNAME = #{attrName}
    </update>

    <insert id="insertInstanceBatch" useGeneratedKeys="true" keyProperty="instanceId" parameterType="java.util.List">
        insert ignore into TBL_TOPO_BUSINESS_INSTANCE_MODEL (INSTANCE_ID, MODEL_ID, MODEL_TYPE, DN, VERSION) values
        <foreach collection="instanceModelDBList" item="item" separator=",">
            (#{item.instanceId}, #{item.modelId}, #{item.modelType}, #{item.dn},#{item.version})
        </foreach>
    </insert>

    <select id="queryInstanceByDnAndTimeline" resultMap="businessInstanceResult"
            lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE,
               model.STATIC_ATTR_NAME,
               model.STATIC_ATTR_TYPE,
               model.STATIC_ATTR_VALUE
        from ^{instanceModelTableName} ins
            left join ^{instanceExternAttrTableName} attr
        on ins.INSTANCE_ID = attr.INSTANCE_ID
            left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.DN = #{dn}
    </select>

    <select id="queryInstanceByExtendAttrValue" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE,
               model.STATIC_ATTR_NAME,
               model.STATIC_ATTR_TYPE,
               model.STATIC_ATTR_VALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
                 left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.INSTANCE_ID IN (select INSTANCE_ID
                                  from TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR
                                  where ATTRNAME = #{attrName}
                                    and ATTRVALUE = #{attrValue})
    </select>

    <select id="getInstanceBySolutionNameAndModelType" resultMap="businessInstanceResult">
        SELECT INS.*, TTBCMEA.*, INS_EXT.*
        FROM TBL_TOPO_BUSINESS_INSTANCE_MODEL INS
        left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR INS_EXT
        ON INS.INSTANCE_ID = INS_EXT.INSTANCE_ID
        LEFT JOIN TBL_TOPO_BUSINESS_COMMON_MODEL CM_MODEL
        ON INS.MODEL_ID = CM_MODEL.MODEL_ID
        LEFT JOIN TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR TTBCMEA
        ON CM_MODEL.MODEL_ID = TTBCMEA.MODEL_ID
        WHERE CM_MODEL.MODEL_TYPE = #{modelType}
        AND CM_MODEL.SOLUTION_NAME = #{solutionName}
        <if test='insExtAttrName != null'>
            AND INS_EXT.ATTRNAME = #{insExtAttrName}
        </if>
    </select>

    <select id="queryInstanceByChildRelationType" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
               ins.MODEL_ID,
               ins.MODEL_TYPE,
               ins.DN,
               ins.VERSION,
               attr.ATTRNAME,
               attr.ATTRCLASS,
               attr.ATTRVALUE,
               model.STATIC_ATTR_NAME,
               model.STATIC_ATTR_TYPE,
               model.STATIC_ATTR_VALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
                 left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.INSTANCE_ID IN
              (select TARGET_INSTANCE_ID
               from TBL_TOPO_BUSINESS_INSTANCE_RELATION
               where INSTANCE_ID = #{instanceId}
                 and RELATION_TYPE = #{relationType})
    </select>

    <select id="getNextLevelInsByCurModelId" resultMap="businessCommonModelWithInstance">
        SELECT T1.*, TTBCM.MODEL_NAME
        FROM TBL_TOPO_BUSINESS_INSTANCE_MODEL T1
                 INNER JOIN TBL_TOPO_BUSINESS_INSTANCE_RELATION T2 ON T1.INSTANCE_ID = T2.INSTANCE_ID
                 INNER JOIN TBL_TOPO_BUSINESS_INSTANCE_MODEL T3 ON T2.TARGET_INSTANCE_ID = T3.INSTANCE_ID
                 inner join "TBL_TOPO_BUSINESS_COMMON_MODEL" TTBCM on T1."MODEL_ID" = TTBCM."MODEL_ID"
        WHERE T3.MODEL_ID = #{modelId}
    </select>

    <select id="queryAllDbInstance" resultMap="businessInstanceResult">
        SELECT DISTINCT T1.*
        FROM TBL_TOPO_BUSINESS_INSTANCE_MODEL T1
                 JOIN TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR T2 ON T1.MODEL_ID = T2.MODEL_ID
        WHERE T2.STATIC_ATTR_NAME = 'applicationType'
          AND T2.STATIC_ATTR_VALUE in (1, 2)
          AND T1.MODEL_TYPE = 6
    </select>

    <select id="queryNextLevelInstances" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
        ins.MODEL_ID,
        ins.MODEL_TYPE,
        ins.DN,
        ins.VERSION,
        attr.ATTRNAME,
        attr.ATTRCLASS,
        attr.ATTRVALUE,
        model.STATIC_ATTR_NAME,
        model.STATIC_ATTR_TYPE,
        model.STATIC_ATTR_VALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
        left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.INSTANCE_ID in
        (select INSTANCE_ID from TBL_TOPO_BUSINESS_INSTANCE_RELATION
        where TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
        and RELATION_TYPE = 0
        )
    </select>

    <select id="querySiteIdByInstanceId" resultType="java.lang.String">
        select ATTRVALUE from TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr
        left join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on relation.TARGET_INSTANCE_ID = attr.INSTANCE_ID
        left join TBL_TOPO_BUSINESS_INSTANCE_MODEL ins on ins.INSTANCE_ID = relation.INSTANCE_ID
        where relation.RELATION_TYPE = 3
          and attr.ATTRNAME = 'siteId'
          and ins.INSTANCE_ID = #{instanceId}
    </select>

    <select id="queryInstanceByRelationType" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
               ins.MODEL_TYPE,
               ins.DN
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on ins.INSTANCE_ID = relation.INSTANCE_ID
        where relation.TARGET_INSTANCE_ID = #{instanceId} and relation.RELATION_TYPE = #{relationType}
    </select>

    <select id="getInstanceByStaticAttrNameAndValue" resultMap="businessInstanceResult">
        SELECT DISTINCT T1.*
        FROM TBL_TOPO_BUSINESS_INSTANCE_MODEL T1
                 JOIN TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR T2 ON T1.MODEL_ID = T2.MODEL_ID
        WHERE T2.STATIC_ATTR_NAME = 'applicationType'
          AND T2.STATIC_ATTR_VALUE = #{dbType}
    </select>

    <select id="getInstanceByStaticAttrNameAndValueForTimeLine"
            lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver"
            resultMap="businessInstanceResult">
        SELECT T1.*
        FROM ^{instanceModelTableName} T1
            JOIN TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR T2 ON T1.MODEL_ID = T2.MODEL_ID
        WHERE T2.STATIC_ATTR_NAME = 'applicationType'
          AND T2.STATIC_ATTR_VALUE = #{dbType}
    </select>

    <select id="getBusinessCommonModelWithInstance" resultMap="businessCommonModelWithInstance">
        select t2.MODEL_NAME, T1.*
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL T1
                 inner join TBL_TOPO_BUSINESS_COMMON_MODEL t2
                            on T1.MODEL_ID = t2.MODEL_ID
        where T1.MODEL_TYPE = #{modelType}
    </select>

    <select id="getBusinessCommonModelWithInstanceForTimeLine"
            lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver"
            resultMap="businessCommonModelWithInstance">
        select t2.MODEL_NAME, T1.*
        from ^{instanceModelTableName} T1
            inner join TBL_TOPO_BUSINESS_COMMON_MODEL t2
        on T1.MODEL_ID = t2.MODEL_ID
        where T1.MODEL_TYPE = #{modelType}
    </select>


    <select id="queryInstancesByModelType" resultMap="businessInstanceResult">
        select INSTANCE_ID,DN
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL
        where MODEL_TYPE = #{modelType}
    </select>

    <select id="queryInstancesByModelTypeByTimeLine" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select INSTANCE_ID,DN
        from ^{instanceModelTableName}
        where MODEL_TYPE = #{modelType}
    </select>

    <select id="queryNextInstanceWithAttrByInstanceIds" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
        ins.DN,
        attr.ATTRNAME,
        attr.ATTRVALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
        left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        where ins.INSTANCE_ID in
        (select INSTANCE_ID from TBL_TOPO_BUSINESS_INSTANCE_RELATION
        where TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
        and RELATION_TYPE = 0
        )
    </select>

    <select id="queryNextInstanceWithAttrByInstanceIdsByTimeLine" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ins.INSTANCE_ID,
        ins.DN,
        attr.ATTRNAME,
        attr.ATTRVALUE
        from ^{instanceModelTableName} ins
        left join ^{instanceExternAttrTableName} attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        where ins.INSTANCE_ID in
        (select INSTANCE_ID from ^{instanceRelationTableName}
        where TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
        and RELATION_TYPE = 0
        )
    </select>


    <select id="queryNextLevelInstanceWithOutExtends" resultMap="businessInstanceResult">
        select INSTANCE_ID,
               MODEL_ID,
               MODEL_TYPE,
               DN,
               VERSION
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL
        where INSTANCE_ID in
              (select INSTANCE_ID from TBL_TOPO_BUSINESS_INSTANCE_RELATION
               where TARGET_INSTANCE_ID = #{instanceId}
                 and RELATION_TYPE = 0)
    </select>

    <select id="queryUpLevelInstanceByTimeLineBatch" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ins.INSTANCE_ID,
        ins.MODEL_ID,
        ins.MODEL_TYPE,
        ins.DN,
        ins.VERSION,
        attr.ATTRNAME,
        attr.ATTRCLASS,
        attr.ATTRVALUE,
        model.STATIC_ATTR_NAME,
        model.STATIC_ATTR_TYPE,
        model.STATIC_ATTR_VALUE
        from ^{instanceModelTableName} ins
        left join ^{instanceExternAttrTableName} attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR model on ins.MODEL_ID = model.MODEL_ID
        where ins.INSTANCE_ID =
        (select TARGET_INSTANCE_ID
        from ^{instanceRelationTableName}
        where
        RELATION_TYPE = 0
        and INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>)
    </select>

    <select id="queryNextLevelInstanceWithOutExtendsByTimeLine" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select INSTANCE_ID,
               MODEL_ID,
               MODEL_TYPE,
               DN,
               VERSION
        from ^{instanceModelTableName}
        where INSTANCE_ID in
            (select INSTANCE_ID from ^{instanceRelationTableName}
            where TARGET_INSTANCE_ID = #{instanceId}
          and RELATION_TYPE = 0)
    </select>

    <select id="queryInstanceByDnsWithoutExtends" resultMap="businessInstanceResult">
        select INSTANCE_ID,
        MODEL_ID,
        DN,
        MODEL_TYPE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL
        <if test="dns != null and dns.size > 0">
            where DN in
            <foreach collection="dns" item="dn" open="(" close=")" separator=",">
                #{dn}
            </foreach>
        </if>
    </select>

    <select id="queryInstanceByDnsWithoutExtendsTimeLine" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select INSTANCE_ID,
        MODEL_ID,
        DN,
        MODEL_TYPE
        from ^{tableName}
        <if test="dns != null and dns.size > 0">
            where DN in
            <foreach collection="dns" item="dn" open="(" close=")" separator=",">
                #{dn}
            </foreach>
        </if>
    </select>

    <select id="queryNextInstanceWithoutAttrByInstanceIds" resultMap="businessInstanceResult">
        select * from TBL_TOPO_BUSINESS_INSTANCE_MODEL
        where INSTANCE_ID in
        (select INSTANCE_ID from TBL_TOPO_BUSINESS_INSTANCE_RELATION
        where TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
        and RELATION_TYPE = 0
        )
    </select>

    <select id="queryDvInstanceBySiteInstanceIds" resultMap="businessInstanceResult">
        select * from TBL_TOPO_BUSINESS_INSTANCE_MODEL
        where MODEL_ID = '6_DigitalView'
        and INSTANCE_ID in
        (select INSTANCE_ID from TBL_TOPO_BUSINESS_INSTANCE_RELATION
        where TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
        and RELATION_TYPE = 3
        )
    </select>

    <select id="queryDbInstanceBySiteInstanceIds" resultMap="businessInstanceResult">
        select * from TBL_TOPO_BUSINESS_INSTANCE_MODEL model
        left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR attr on model.MODEL_ID = attr.MODEL_ID
        where attr.STATIC_ATTR_NAME = 'applicationType' and attr.STATIC_ATTR_VALUE = '3'
        and model.INSTANCE_ID in
        (select INSTANCE_ID from TBL_TOPO_BUSINESS_INSTANCE_RELATION
        where TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="instanceIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
        and RELATION_TYPE = 3
        )
    </select>

    <select id="querySiteBySolutionInsId" resultMap="businessInstanceResult">
        select ins.*,
               relation.TARGET_INSTANCE_ID
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 left join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on ins.INSTANCE_ID = relation.INSTANCE_ID
        where
            relation.RELATION_TYPE = 2
          and ins.MODEL_TYPE = 3
          and relation.TARGET_INSTANCE_ID = #{instanceId}
    </select>

    <select id="getLevelSixModelDBSBySiteInsId" resultMap="businessInstanceResult">
        select ins.*,
        relation.TARGET_INSTANCE_ID
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
        left join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on ins.INSTANCE_ID = relation.INSTANCE_ID
        where
        relation.RELATION_TYPE = 3
        and ins.MODEL_TYPE = 6
        and relation.TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="siteInsIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
    </select>

    <select id="queryAllInstanceBySitesAndModelType" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID,
        ins.MODEL_ID,
        ins.MODEL_TYPE,
        ins.DN,
        ins.VERSION,
        attr.ATTRNAME,
        attr.ATTRCLASS,
        attr.ATTRVALUE
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
        left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        left join TBL_TOPO_BUSINESS_INSTANCE_RELATION relation on ins.INSTANCE_ID = relation.INSTANCE_ID
        where relation.TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="siteInsIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
        and ins.MODEL_TYPE = #{modelType}
    </select>

    <select id="queryAllInstanceBySitesAndModelTypeTimeLine" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ins.INSTANCE_ID,
        ins.MODEL_ID,
        ins.MODEL_TYPE,
        ins.DN,
        ins.VERSION,
        attr.ATTRNAME,
        attr.ATTRCLASS,
        attr.ATTRVALUE
        from ^{instanceModelTableName} ins
        left join ^{instanceExternAttrTableName} attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        left join ^{instanceRelationTableName} relation on ins.INSTANCE_ID = relation.INSTANCE_ID
        where relation.TARGET_INSTANCE_ID in
        <foreach item="instanceId" collection="siteInsIds" open="(" separator="," close=")">
            #{instanceId}
        </foreach>
        and ins.MODEL_TYPE = #{modelType}
    </select>

    <select id="queryInstanceByCondition" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select INSTANCE_ID
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL ins
                 inner join TBL_TOPO_BUSINESS_COMMON_MODEL cm
                            on ins.MODEL_ID = cm.MODEL_ID
        where cm.SOLUTION_NAME = #{condition.solutionName} and cm.MODEL_TYPE = #{condition.modelType}
    </select>

    <select id="queryInstanceIdByModelId" resultType="java.lang.Integer">
        select INSTANCE_ID
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL
        where MODEL_ID IN
        <foreach item="modelId" collection="modelIdList" open="(" separator="," close=")">
            #{modelId}
        </foreach>
    </select>

    <select id="queryExistSiteIdBySolutionName" resultType="java.lang.String">
        SELECT ATTRVALUE FROM TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr
                                  left join TBL_TOPO_BUSINESS_INSTANCE_MODEL model on attr.INSTANCE_ID = model.INSTANCE_ID
                                  left join TBL_TOPO_BUSINESS_COMMON_MODEL common on model.MODEL_ID = common.MODEL_ID
        where common.SOLUTION_NAME = #{solutionName}
          and attr.ATTRNAME = 'siteId'
    </select>

    <select id="getInstanceByInstanceIdsAndAttrName" resultMap="mapToReservedAttr">
        select ins.DN, attr.ATTRNAME, attr.ATTRVALUE, attr.ATTRCLASS
        from (select * from TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR where ATTRNAME = #{attrName}) attr
        left join TBL_TOPO_BUSINESS_INSTANCE_MODEL ins on ins.INSTANCE_ID = attr.INSTANCE_ID
        where ins.INSTANCE_ID in
        <foreach item="insId" collection="instanceIds" open="(" separator="," close=")">
            #{insId}
        </foreach>
    </select>

    <select id="queryInstanceByModelIds" resultMap="businessInstanceResult">
        select ins.INSTANCE_ID, ins.MODEL_ID,attr.ATTRNAME, attr.ATTRVALUE, attr.ATTRCLASS,modelAttr.STATIC_ATTR_NAME,modelAttr.STATIC_ATTR_TYPE,modelAttr.STATIC_ATTR_VALUE from (select INSTANCE_ID,MODEL_ID from TBL_TOPO_BUSINESS_INSTANCE_MODEL where MODEL_ID in
        <foreach collection="modelIds" item="modelId" open="(" separator="," close=")">
            #{modelId}
        </foreach>) ins
        left join TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR attr on ins.INSTANCE_ID = attr.INSTANCE_ID
        left join TBL_TOPO_BUSINESS_COMMON_MODEL_EXTENT_ATTR modelAttr on ins.MODEL_ID = modelAttr.MODEL_ID
    </select>

    <select id="queryStripeGroupName" resultMap="businessExtentAttr">
        select INSTANCE_ID,
               ATTRNAME,
               ATTRCLASS,
               ATTRVALUE
        from TBL_TOPO_BUSINESS_INSTANCE_EXTENT_ATTR
        where ATTRNAME = 'hwsStripeGroupName'
    </select>

    <select id="queryDvInstances" resultMap="businessInstanceResult">
        select * from TBL_TOPO_BUSINESS_INSTANCE_MODEL where DN = 'OS=1'
    </select>

    <select id="queryRealVmInstanceByDns" resultMap="businessInstanceResult">
        select INSTANCE_ID,
        MODEL_ID,
        DN,
        MODEL_TYPE,
        VERSION
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL
        where INSTANCE_ID in (select TARGET_INSTANCE_ID from TBL_TOPO_BUSINESS_INSTANCE_RELATION where RELATION_TYPE = 1)
        <if test="dns != null and dns.size > 0">
            and DN in
            <foreach collection="dns" item="dn" open="(" close=")" separator=",">
                #{dn}
            </foreach>
        </if>
    </select>

    <select id="queryRealVmInstanceByDnsTimeline" resultMap="businessInstanceResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select INSTANCE_ID,
        MODEL_ID,
        DN,
        MODEL_TYPE,
        VERSION
        from ^{instanceModelTableName}
        where INSTANCE_ID in (select TARGET_INSTANCE_ID from ^{instanceRelationTableName} where RELATION_TYPE = 1)
        <if test="dns != null and dns.size > 0">
            and DN in
            <foreach collection="dns" item="dn" open="(" close=")" separator=",">
                #{dn}
            </foreach>
        </if>
    </select>

    <select id="queryInstancePodDockerDuplicateInstances" resultMap="businessInstanceResult">
        select INSTANCE_ID,
               MODEL_ID,
               DN,
               MODEL_TYPE,
               VERSION
        from TBL_TOPO_BUSINESS_INSTANCE_MODEL
        WHERE DN IN (
            SELECT DN
            FROM TBL_TOPO_BUSINESS_INSTANCE_MODEL
            GROUP BY DN
            HAVING COUNT(*) > 1
        )
          and MODEL_TYPE in (6,7,9)
          and DN != 'OS=1'
    </select>

</mapper>
