<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.i2000.dvanalysisengineservice.dao.disasterrecovery.mapper.DisasterRecoveryKPIMapper">
    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.KPIColumnModel" id="baseResultMap">
        <id column="GROUP_ID" property="groupId" javaType="String" jdbcType="VARCHAR"/>
        <result column="COLUMN_KEY" property="columnKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="COLUMN_NAME" property="columnName" javaType="String" jdbcType="VARCHAR"/>
        <result column="COLUMN_DISPLAY_CN" property="columnDisplayCn" javaType="String" jdbcType="VARCHAR"/>
        <result column="COLUMN_DISPLAY_EN" property="columnDisplayEn" javaType="String" jdbcType="VARCHAR"/>
        <result column="COLUMN_TYPE" property="columnType" javaType="String" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.KPIThreshold" id="threshold">
        <id column="INDICATOR_ID" property="indicatorId" javaType="String" jdbcType="VARCHAR"/>
        <result column="SRC_TYPE" property="srcType" javaType="String" jdbcType="VARCHAR"/>
        <result column="COLUMN_KEY" property="columnKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="COLUMN_NAME" property="columnName" javaType="String" jdbcType="VARCHAR"/>
        <result column="THRESHOLD_TYPE" property="thresholdType" javaType="String" jdbcType="VARCHAR"/>
        <result column="EFFECTIVETIME" property="effectiveTime" javaType="String" jdbcType="VARCHAR"/>
        <result column="HEALTH_CONDITION" property="healthCondition" javaType="String" jdbcType="VARCHAR"/>
        <result column="PERIOD_TYPE" property="periodType" javaType="String" jdbcType="VARCHAR"/>
        <result column="SAMPLE_COUNT" property="sampleCount" javaType="integer" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.AuxiliaryKPIInfo" id="auxiliaryKpiInfo">
        <id column="INDICATOR_ID" property="indicatorId" javaType="String" jdbcType="VARCHAR"/>
        <result column="COLUMN_KEY" property="columnKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="COLUMN_NAME" property="columnName" javaType="String" jdbcType="VARCHAR"/>
        <result column="INFO_TYPE" property="infoType" javaType="String" jdbcType="VARCHAR"/>
        <result column="TEXT_VALUE" property="textValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="FORMULA_VALUE" property="formulaValue" javaType="String" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.DerivedKPIInfo" id="derivedKpiInfo">
        <id column="INDICATOR_ID" property="indicatorId" javaType="String" jdbcType="VARCHAR"/>
        <result column="DERIVED_DISPLAY_CN" property="derivedDisplayCn" javaType="String" jdbcType="VARCHAR"/>
        <result column="DERIVED_DISPLAY_EN" property="derivedDisplayEn" javaType="String" jdbcType="VARCHAR"/>
        <result column="KPI_DISPLAY_CN" property="kpiDisplayCn" javaType="String" jdbcType="VARCHAR"/>
        <result column="KPI_DISPLAY_EN" property="kpiDisplayEn" javaType="String" jdbcType="VARCHAR"/>
        <result column="TYPE" property="type" javaType="String" jdbcType="VARCHAR"/>
        <result column="DIFFERENCE_TYPE" property="differenceType" javaType="String" jdbcType="VARCHAR"/>
        <result column="DIFFERENCE_SAME_PERIOD" property="differenceSamePeriod" javaType="String" jdbcType="VARCHAR"/>
        <result column="DIFFERENCE_SAME_SAMPLE" property="differenceSameSample" javaType="integer" jdbcType="INTEGER"/>
        <result column="CALCULATION_FORMULA" property="calculationFormula" javaType="integer" jdbcType="INTEGER"/>
        <result column="STATISTIC_TYPE" property="statisticType" javaType="String" jdbcType="VARCHAR"/>
        <result column="STATISTIC_PERIOD" property="statisticPeriod" javaType="String" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.KPIIndicator" id="kpiMainResult">
        <id column="INDICATOR_ID" property="indicatorId" javaType="String" jdbcType="VARCHAR"/>
        <result column="GROUP_ID" property="groupId" javaType="String" jdbcType="VARCHAR"/>
        <result column="GROUP_MEMBER_ID" property="groupMemberId" javaType="String" jdbcType="VARCHAR"/>
        <result column="VNF_DN" property="vnfDn" javaType="String" jdbcType="VARCHAR"/>
        <result column="VNF_DN_NAME" property="vnfDnName" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_UNIT_KEY" property="measUnitKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_UNIT_NAME" property="measUnitName" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_TYPE_KEY" property="measTypeKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="DN" property="dn" javaType="String" jdbcType="VARCHAR"/>
        <result column="INDEX_NAME" property="indexName" javaType="String" jdbcType="VARCHAR"/>
        <result column="MO_TYPE" property="moType" javaType="String" jdbcType="VARCHAR"/>
        <result column="DISPLAY_VALUE" property="displayValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="ORIGINAL_VALUE" property="originalValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="UNIT" property="unit" javaType="String" jdbcType="VARCHAR"/>
        <result column="DN_NAME" property="dnName" javaType="String" jdbcType="VARCHAR"/>
        <result column="CHECKED_NET_ID" property="checkedNetId" javaType="String" jdbcType="VARCHAR"/>
        <result column="HAS_MEAS_OBJ" property="hasMeasObj" javaType="String" jdbcType="VARCHAR"/>
        <result column="INDEX_ID" property="indexId" javaType="String" jdbcType="VARCHAR"/>
        <result column="RESOURCE_TYPE_KEY" property="resourceTypeKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="IS_DISPLAY" property="isDisplay" javaType="Boolean" jdbcType="BOOLEAN"/>
    </resultMap>

    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.KPIAuxiliaryIndicator" id="kpiAuxiliaryIndicatorResult">
        <id column="INDICATOR_ID" property="indicatorId" javaType="String" jdbcType="VARCHAR"/>
        <result column="GROUP_ID" property="groupId" javaType="String" jdbcType="VARCHAR"/>
        <result column="GROUP_MEMBER_ID" property="groupMemberId" javaType="String" jdbcType="VARCHAR"/>
        <result column="VNF_DN" property="vnfDn" javaType="String" jdbcType="VARCHAR"/>
        <result column="VNF_DN_NAME" property="vnfDnName" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_UNIT_KEY" property="measUnitKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_UNIT_NAME" property="measUnitName" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_TYPE_KEY" property="measTypeKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="DN" property="dn" javaType="String" jdbcType="VARCHAR"/>
        <result column="INDEX_NAME" property="indexName" javaType="String" jdbcType="VARCHAR"/>
        <result column="MO_TYPE" property="moType" javaType="String" jdbcType="VARCHAR"/>
        <result column="DISPLAY_VALUE" property="displayValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="ORIGINAL_VALUE" property="originalValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="UNIT" property="unit" javaType="String" jdbcType="VARCHAR"/>
        <result column="DN_NAME" property="dnName" javaType="String" jdbcType="VARCHAR"/>
        <result column="CHECKED_NET_ID" property="checkedNetId" javaType="String" jdbcType="VARCHAR"/>
        <result column="HAS_MEAS_OBJ" property="hasMeasObj" javaType="String" jdbcType="VARCHAR"/>
        <result column="INDEX_ID" property="indexId" javaType="String" jdbcType="VARCHAR"/>
        <result column="RESOURCE_TYPE_KEY" property="resourceTypeKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="AUXILIARY_KPI_DISPLAY_CN" property="auxiliaryKPIDisplayCN" javaType="String" jdbcType="VARCHAR"/>
        <result column="AUXILIARY_KPI_DISPLAY_EN" property="auxiliaryKPIDisplayEN" javaType="String" jdbcType="VARCHAR"/>
        <result column="BELONG_INDICATOR_ID" property="belongIndicatorId" javaType="String" jdbcType="VARCHAR"/>
        <result column="COLUMN_KEY" property="columnKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="COLUMN_NAME" property="columnName" javaType="String" jdbcType="VARCHAR"/>
        <result column="IS_DISPLAY" property="isDisplay" javaType="Boolean" jdbcType="BOOLEAN"/>
    </resultMap>

    <insert id="batchInsert">
        INSERT INTO TBL_AIOPS_MONITOR_KPI_COLUMN_TITLE
        (
        GROUP_ID,
        COLUMN_KEY,
        COLUMN_NAME,
        COLUMN_DISPLAY_CN,
        COLUMN_DISPLAY_EN,
        COLUMN_TYPE
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.groupId, jdbcType=VARCHAR},
            #{item.columnKey, jdbcType=VARCHAR},
            #{item.columnName, jdbcType=VARCHAR},
            #{item.columnDisplayCn, jdbcType=VARCHAR},
            #{item.columnDisplayEn, jdbcType=VARCHAR},
            #{item.columnType, jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="insertKpiIndicator" parameterType="com.huawei.i2000.dvanalysisengineservice.model.KPIIndicator">
        insert into TBL_AIOPS_MONITOR_KPI
        (
            INDICATOR_ID,GROUP_ID,GROUP_MEMBER_ID,MEAS_UNIT_KEY,MEAS_UNIT_NAME,MEAS_TYPE_KEY,DN,INDEX_NAME,MO_TYPE,DISPLAY_VALUE,ORIGINAL_VALUE,UNIT,
            DN_NAME,CHECKED_NET_ID,HAS_MEAS_OBJ,INDEX_ID,COLUMN_KEY,COLUMN_NAME,AUXILIARY_KPI_DISPLAY_CN,AUXILIARY_KPI_DISPLAY_EN,RESOURCE_TYPE_KEY,BELONG_INDICATOR_ID,IS_DISPLAY
        )
        values
        (
            #{kpiIndicator.indicatorId},#{kpiIndicator.groupId},#{kpiIndicator.groupMemberId},#{kpiIndicator.measUnitKey},#{kpiIndicator.measUnitName},
            #{kpiIndicator.measTypeKey},#{kpiIndicator.dn},#{kpiIndicator.indexName},#{kpiIndicator.moType},
            #{kpiIndicator.displayValue},#{kpiIndicator.originalValue},#{kpiIndicator.unit},#{kpiIndicator.dnName},
            #{kpiIndicator.checkedNetId},#{kpiIndicator.hasMeasObj},#{kpiIndicator.indexId},#{kpiIndicator.columnKey},#{kpiIndicator.columnName},
            #{kpiIndicator.auxiliaryKPIDisplayCN},#{kpiIndicator.auxiliaryKPIDisplayEN},#{kpiIndicator.resourceTypeKey},#{kpiIndicator.belongIndicatorId},#{kpiIndicator.isDisplay}
        )
    </insert>

    <insert id="insertKpiAuxiliaryIndicator"
            parameterType="com.huawei.i2000.dvanalysisengineservice.model.KPIAuxiliaryIndicator">
        insert into TBL_AIOPS_MONITOR_KPI
        (INDICATOR_ID, GROUP_ID, GROUP_MEMBER_ID, MEAS_UNIT_KEY, MEAS_UNIT_NAME, MEAS_TYPE_KEY, DN, INDEX_NAME, MO_TYPE,
         DISPLAY_VALUE, ORIGINAL_VALUE, UNIT,
         DN_NAME, CHECKED_NET_ID, HAS_MEAS_OBJ, INDEX_ID, COLUMN_KEY, COLUMN_NAME, AUXILIARY_KPI_DISPLAY_CN,
         AUXILIARY_KPI_DISPLAY_EN, RESOURCE_TYPE_KEY, BELONG_INDICATOR_ID, IS_DISPLAY)
        values (#{kpiAuxiliaryIndicator.indicatorId}, #{kpiAuxiliaryIndicator.groupId}, #{kpiAuxiliaryIndicator.groupMemberId},
                #{kpiAuxiliaryIndicator.measUnitKey}, #{kpiAuxiliaryIndicator.measUnitName},
                #{kpiAuxiliaryIndicator.measTypeKey}, #{kpiAuxiliaryIndicator.dn}, #{kpiAuxiliaryIndicator.indexName}, #{kpiAuxiliaryIndicator.moType},
                #{kpiAuxiliaryIndicator.displayValue}, #{kpiAuxiliaryIndicator.originalValue}, #{kpiAuxiliaryIndicator.unit},
                #{kpiAuxiliaryIndicator.dnName},
                #{kpiAuxiliaryIndicator.checkedNetId}, #{kpiAuxiliaryIndicator.hasMeasObj}, #{kpiAuxiliaryIndicator.indexId},
                #{kpiAuxiliaryIndicator.columnKey}, #{kpiAuxiliaryIndicator.columnName},
                #{kpiAuxiliaryIndicator.auxiliaryKPIDisplayCN}, #{kpiAuxiliaryIndicator.auxiliaryKPIDisplayEN},
                #{kpiAuxiliaryIndicator.resourceTypeKey}, #{kpiAuxiliaryIndicator.belongIndicatorId}, #{kpiAuxiliaryIndicator.isDisplay})
    </insert>

    <insert id="insertDerivedKpiInfo"
            parameterType="com.huawei.i2000.dvanalysisengineservice.model.DerivedKPIInfo">
        insert into TBL_AIOPS_MONITOR_KPI_DERIVED
        (
         INDICATOR_ID, DERIVED_DISPLAY_CN,
         DERIVED_DISPLAY_EN, KPI_DISPLAY_CN,
         KPI_DISPLAY_EN, TYPE,
         DIFFERENCE_TYPE, DIFFERENCE_SAME_PERIOD,
         DIFFERENCE_SAME_SAMPLE, CALCULATION_FORMULA,
         STATISTIC_TYPE, STATISTIC_PERIOD
         )
        values
        (
            #{derivedKPIInfo.indicatorId}, #{derivedKPIInfo.derivedDisplayCn},
            #{derivedKPIInfo.derivedDisplayEn}, #{derivedKPIInfo.kpiDisplayCn},
            #{derivedKPIInfo.kpiDisplayEn}, #{derivedKPIInfo.type},
            #{derivedKPIInfo.differenceType}, #{derivedKPIInfo.differenceSamePeriod},
            #{derivedKPIInfo.differenceSameSample}, #{derivedKPIInfo.calculationFormula},
            #{derivedKPIInfo.statisticType}, #{derivedKPIInfo.statisticPeriod}
        )
    </insert>

    <insert id="insertKpiThreshold" parameterType="java.util.List">
        INSERT INTO TBL_AIOPS_MONITOR_KPI_THRESHOLD
        (
            INDICATOR_ID,SRC_TYPE,COLUMN_NAME,
            COLUMN_KEY,THRESHOLD_TYPE,
            EFFECTIVETIME,HEALTH_CONDITION,
            PERIOD_TYPE,SAMPLE_COUNT
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
             #{item.indicatorId,jdbcType=VARCHAR},#{item.srcType,jdbcType=VARCHAR},
             #{item.columnName,jdbcType=VARCHAR},#{item.columnKey,jdbcType=VARCHAR},
             #{item.thresholdType,jdbcType=VARCHAR},#{item.effectiveTime,jdbcType=VARCHAR},#{item.healthCondition,jdbcType=VARCHAR},
             #{item.periodType,jdbcType=VARCHAR},#{item.sampleCount,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <insert id="batchInsertAuxiliaryKPIInfo">
        INSERT INTO TBL_AIOPS_MONITOR_KPI_AUXILIARY_INFO
        (
            INDICATOR_ID,COLUMN_NAME,COLUMN_KEY,INFO_TYPE,TEXT_VALUE,FORMULA_VALUE
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.indicatorId,jdbcType=VARCHAR},#{item.columnName,jdbcType=VARCHAR},#{item.columnKey,jdbcType=VARCHAR},#{item.infoType,jdbcType=VARCHAR},#{item.textValue,jdbcType=VARCHAR},#{item.formulaValue,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>


    <delete id="deleteColumns" parameterType="java.lang.String">
        delete from TBL_AIOPS_MONITOR_KPI_COLUMN_TITLE where GROUP_ID= #{groupId}
    </delete>

    <delete id="deleteColumnsByGroupIds" parameterType="java.util.List">
        delete from TBL_AIOPS_MONITOR_KPI_COLUMN_TITLE
        where
        GROUP_ID in
        <foreach collection="list" index="index" item="item" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteMainKpi" parameterType="java.lang.String">
        delete from TBL_AIOPS_MONITOR_KPI where
        GROUP_ID = #{groupId}
    </delete>

    <delete id="deleteMainKpiByIndicatorIds">
        delete from TBL_AIOPS_MONITOR_KPI where INDICATOR_ID in
        <foreach collection="indicatorIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteMainKpiByMemberId" parameterType="java.lang.String">
        delete from TBL_AIOPS_MONITOR_KPI where
            GROUP_MEMBER_ID = #{memberId}
    </delete>

    <delete id="deleteMainKpiByGroupIds" parameterType="java.util.List">
        delete from TBL_AIOPS_MONITOR_KPI where
        GROUP_ID
        in
        <foreach collection="list" index="index" item="item" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteAuxiliaryKpiByIndicatorIds" parameterType="java.util.List">
        DELETE FROM TBL_AIOPS_MONITOR_KPI where
        BELONG_INDICATOR_ID IN
        <foreach collection="list" index="index" item="item" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteAuxiliaryInfos" parameterType="java.util.List">
        DELETE FROM TBL_AIOPS_MONITOR_KPI_AUXILIARY_INFO where
        INDICATOR_ID IN
        <foreach collection="list" index="index" item="item" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteDerivedKpis" parameterType="java.util.List">
        DELETE FROM TBL_AIOPS_MONITOR_KPI_DERIVED where
        INDICATOR_ID IN
        <foreach collection="list" index="index" item="item" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteKpiThresholds" parameterType="java.util.List">
        DELETE FROM TBL_AIOPS_MONITOR_KPI_THRESHOLD where
        INDICATOR_ID IN
        <foreach collection="list" index="index" item="item" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="queryColumns" resultMap="baseResultMap">
        select GROUP_ID,
               COLUMN_KEY,
               COLUMN_NAME,
               COLUMN_DISPLAY_CN,
               COLUMN_DISPLAY_EN,
               COLUMN_TYPE
        from TBL_AIOPS_MONITOR_KPI_COLUMN_TITLE
        where GROUP_ID = #{groupId}
    </select>

    <select id="queryColumnsByName" resultMap="baseResultMap">
        select GROUP_ID,
               COLUMN_KEY,
               COLUMN_NAME,
               COLUMN_DISPLAY_CN,
               COLUMN_DISPLAY_EN,
               COLUMN_TYPE
        from TBL_AIOPS_MONITOR_KPI_COLUMN_TITLE
        where GROUP_ID = #{groupId} and COLUMN_NAME in
        <foreach collection="colNames" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getIndicatorIds" resultType="java.lang.String">
        select INDICATOR_ID
        from TBL_AIOPS_MONITOR_KPI
        where GROUP_ID = #{groupId}
    </select>

    <select id="getIndicatorIdsWithPerm" resultType="java.lang.String">
        select INDICATOR_ID
        from TBL_AIOPS_MONITOR_KPI
        where GROUP_ID = #{groupId}
          and DN in
        <foreach collection="dns" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getIndicatorIdsByMemberId" resultType="java.lang.String">
        select INDICATOR_ID
        from TBL_AIOPS_MONITOR_KPI
        where GROUP_MEMBER_ID = #{memberId}
    </select>

    <select id="queryIndicatorIdsByGroupIds" resultType="java.lang.String">
        select INDICATOR_ID
        from TBL_AIOPS_MONITOR_KPI
        where GROUP_ID
        in
        <foreach collection="list" item="item" index="index" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="queryMainKpi" resultMap="kpiMainResult">
        select member.DN as VNF_DN,member.DN_NAME as VNF_DN_NAME,
               kpi.INDICATOR_ID,kpi.GROUP_ID,
               kpi.GROUP_MEMBER_ID,kpi.MEAS_UNIT_KEY,
               kpi.MEAS_UNIT_NAME,kpi.MEAS_TYPE_KEY,
               kpi.DN,kpi.INDEX_NAME,kpi.MO_TYPE,
               kpi.DISPLAY_VALUE,kpi.ORIGINAL_VALUE,
               kpi.UNIT,kpi.DN_NAME,
               kpi.CHECKED_NET_ID,kpi.HAS_MEAS_OBJ,
               kpi.INDEX_ID,
               kpi.RESOURCE_TYPE_KEY,
               kpi.IS_DISPLAY
        FROM TBL_AIOPS_MONITOR_KPI kpi
        LEFT JOIN TBL_AIOPS_MONITOR_GROUP_MEMBER member
        ON kpi.GROUP_MEMBER_ID = member.GROUP_MEMBER_ID
        where kpi.GROUP_ID = #{groupId}
    </select>

    <select id="getThresholdByIndicatorIds" resultMap="threshold">
        select
        INDICATOR_ID,SRC_TYPE,COLUMN_NAME,
        COLUMN_KEY,THRESHOLD_TYPE,
        EFFECTIVETIME,HEALTH_CONDITION,
        PERIOD_TYPE,SAMPLE_COUNT
        FROM TBL_AIOPS_MONITOR_KPI_THRESHOLD
        WHERE
        INDICATOR_ID IN
        <foreach collection="list" item="item" index="index" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="queryThreshold" resultMap="threshold">
        select
        INDICATOR_ID,SRC_TYPE,COLUMN_NAME,
        COLUMN_KEY,THRESHOLD_TYPE,
        EFFECTIVETIME,HEALTH_CONDITION,
        PERIOD_TYPE,SAMPLE_COUNT
        FROM TBL_AIOPS_MONITOR_KPI_THRESHOLD
        WHERE
        1 = 1
        <if test="srcType !=null">
            AND SRC_TYPE=#{srcType,jdbcType=VARCHAR}
        </if>
        <if test="indicatorId !=null">
            AND INDICATOR_ID=#{indicatorId,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="queryDerivedKpiInfo" resultMap="derivedKpiInfo">
        select
            INDICATOR_ID, DERIVED_DISPLAY_CN,
            DERIVED_DISPLAY_EN, KPI_DISPLAY_CN,
            KPI_DISPLAY_EN, TYPE,
            DIFFERENCE_TYPE, DIFFERENCE_SAME_PERIOD,
            DIFFERENCE_SAME_SAMPLE, CALCULATION_FORMULA,
            STATISTIC_TYPE, STATISTIC_PERIOD
        FROM TBL_AIOPS_MONITOR_KPI_DERIVED
        WHERE
        INDICATOR_ID = #{indicatorId}
    </select>

    <select id="queryAuxiliaryKpiInfo" resultMap="auxiliaryKpiInfo">
        select
            INDICATOR_ID,COLUMN_NAME,
            COLUMN_KEY,INFO_TYPE,
            TEXT_VALUE,FORMULA_VALUE
        FROM TBL_AIOPS_MONITOR_KPI_AUXILIARY_INFO
        WHERE
        INDICATOR_ID = #{indicatorId}
    </select>

    <select id="queryKPIAuxiliaryIndicatorsByBelongIndicatorIds" resultType="java.lang.String">
        SELECT INDICATOR_ID
        FROM TBL_AIOPS_MONITOR_KPI
        WHERE BELONG_INDICATOR_ID
        IN
        <foreach collection="list" item="item" index="index" open="("
                 separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="queryKPIAuxiliaryIndicators" resultMap="kpiAuxiliaryIndicatorResult">
        select member.DN as VNF_DN,member.DN_NAME as VNF_DN_NAME,
               kpi.INDICATOR_ID,kpi.GROUP_ID,
               kpi.GROUP_MEMBER_ID,kpi.MEAS_UNIT_KEY,
               kpi.MEAS_UNIT_NAME,kpi.MEAS_TYPE_KEY,
               kpi.DN,kpi.INDEX_NAME,kpi.MO_TYPE,
               kpi.DISPLAY_VALUE,kpi.ORIGINAL_VALUE,
               kpi.UNIT,kpi.DN_NAME,
               kpi.CHECKED_NET_ID,kpi.HAS_MEAS_OBJ,
               kpi.INDEX_ID,kpi.AUXILIARY_KPI_DISPLAY_CN,
               kpi.AUXILIARY_KPI_DISPLAY_EN,kpi.RESOURCE_TYPE_KEY,
               kpi.COLUMN_NAME,COLUMN_KEY,
               kpi.BELONG_INDICATOR_ID,kpi.IS_DISPLAY
        FROM TBL_AIOPS_MONITOR_KPI kpi
        LEFT JOIN TBL_AIOPS_MONITOR_GROUP_MEMBER member
        ON kpi.GROUP_MEMBER_ID = member.GROUP_MEMBER_ID
        where BELONG_INDICATOR_ID = #{indicatorId}
    </select>

    <select id="queryColumnByType" resultMap="baseResultMap">
        select GROUP_ID,
               COLUMN_KEY,
               COLUMN_NAME,
               COLUMN_DISPLAY_CN,
               COLUMN_DISPLAY_EN,
               COLUMN_TYPE
        from TBL_AIOPS_MONITOR_KPI_COLUMN_TITLE
        where GROUP_ID = #{groupId} AND COLUMN_TYPE = #{columnType}
    </select>


</mapper>