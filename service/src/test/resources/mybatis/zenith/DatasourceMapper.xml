<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.cmp.rubikcoordinator.dao.mapper.DataSourceMapper">

    <resultMap id="queryDataSourceResultMapForOracle" type="com.huawei.cmp.rubikcoordinator.model.DataSource">
        <id property="id" column="ID"/>
        <result property="name" column="NAME"/>
        <result property="type" column="TYPE"/>
        <result property="aliasName" column="ALIAS"/>
        <result property="isExternal" column="ISEXTERNAL"/>
        <result property="properties" column="PROPERTIES" javaType="map" jdbcType="CLOB"
                typeHandler="com.huawei.cmp.rubikcoordinator.handler.LongVarcharMapHandle"/>
    </resultMap>

    <resultMap id="queryDataSourceResultMapForMysql" type="com.huawei.cmp.rubikcoordinator.model.DataSource">
        <id property="id" column="ID"/>
        <result property="name" column="NAME"/>
        <result property="type" column="TYPE"/>
        <result property="aliasName" column="ALIAS"/>
        <result property="isExternal" column="ISEXTERNAL"/>
        <result property="properties" column="PROPERTIES" javaType="map" jdbcType="LONGVARCHAR"
                typeHandler="com.huawei.cmp.rubikcoordinator.handler.LongVarcharMapHandle"/>
    </resultMap>

    <resultMap id="queryTableResultMapForOracle" type="com.huawei.cmp.rubikcoordinator.model.Table">
        <id property="id" column="ID"/>
        <result property="name" column="NAME"/>
        <result property="aliasName" column="ALIAS"/>
        <result property="properties" column="PROPERTIES" javaType="java.util.Map" jdbcType="CLOB"
                typeHandler="com.huawei.cmp.rubikcoordinator.handler.LongVarcharMapHandle"/>
    </resultMap>

    <resultMap id="queryTableResultMapForMysql" type="com.huawei.cmp.rubikcoordinator.model.Table">
        <id property="id" column="ID"/>
        <result property="name" column="NAME"/>
        <result property="aliasName" column="ALIAS"/>
        <result property="properties" column="PROPERTIES" javaType="java.util.Map" jdbcType="LONGVARCHAR"
                typeHandler="com.huawei.cmp.rubikcoordinator.handler.LongVarcharMapHandle"/>
    </resultMap>

    <resultMap id="queryNorthResultMap" type="com.huawei.cmp.rubikcoordinator.model.North">
        <result property="dataSourceId" column="DATASOURCEID"/>
        <result property="outputName" column="OUTPUTNAME"/>
        <result property="encode" column="ENCODE"/>
    </resultMap>

    <resultMap id="queryOutputLogResultMapForOracle" type="com.huawei.cmp.rubikcoordinator.model.OutputLog">
        <id property="id" column="ID"/>
        <result property="logType" column="LOGTYPE"/>
        <result property="properties" column="PROPERTIES" javaType="java.util.Map" jdbcType="CLOB"
                typeHandler="com.huawei.cmp.rubikcoordinator.handler.LongVarcharMapHandle"/>
        <result property="status" column="STATUS"/>
    </resultMap>

    <resultMap id="queryOutputLogResultMapForMysql" type="com.huawei.cmp.rubikcoordinator.model.OutputLog">
        <id property="id" column="ID"/>
        <result property="logType" column="LOGTYPE"/>
        <result property="properties" column="PROPERTIES" javaType="java.util.Map" jdbcType="LONGVARCHAR"
                typeHandler="com.huawei.cmp.rubikcoordinator.handler.LongVarcharMapHandle"/>
        <result property="status" column="STATUS"/>
    </resultMap>

    <resultMap id="queryNorthInfoResultMap" type="com.huawei.cmp.rubikcoordinator.model.NorthInfo">
        <result property="solutionId" column="SOLUTIONID"/>
        <result property="outputName" column="OUTPUTNAME"/>
        <result property="logType" column="LOGTYPE"/>
    </resultMap>


    <sql id="datasourceFields">
        ID, NAME, TYPE, ALIAS, ISEXTERNAL, PROPERTIES
    </sql>

    <sql id="tableFields">
        ID, NAME, ALIAS, PROPERTIES
    </sql>

    <sql id="columnFields">
        ID, NAME,TYPE, LENGTH, ISINDEX, ISPK
    </sql>

    <sql id="northFields">
        DATASOURCEID, OUTPUTNAME, ENCODE
    </sql>

    <sql id="outputFields">
        ID, LOGTYPE, PROPERTIES, FIELDS, STATUS
    </sql>

    <select id="queryDataSource" parameterType="string" resultMap="queryDataSourceResultMapForMysql">
        select
        <include refid="datasourceFields"/>
        from T_CMP_MC_DATASOURCE where NAME = #{dataSourceName}
    </select>

    <select id="queryDataSourceBySolutionIdAndName" resultMap="queryDataSourceResultMapForMysql">
        select
        <include refid="datasourceFields"/>
        from T_CMP_MC_DATASOURCE where SOLUTIONID = #{solutionId} AND NAME = #{dataSourceName}
    </select>

    <insert id="insertDataSource" parameterType="map">
        insert into T_CMP_MC_DATASOURCE (ID, SOLUTIONID, NAME, TYPE, ALIAS, ISEXTERNAL, PROPERTIES)
        <foreach collection="list" item="info" separator="union all">
            (SELECT
            #{info.id, jdbcType=BIGINT},
            #{solutionId, jdbcType=BIGINT},
            #{info.name, jdbcType=VARCHAR},
            #{info.type, jdbcType=VARCHAR},
            #{info.aliasName, jdbcType=VARCHAR},
            #{info.isExternal, jdbcType=VARCHAR},
            #{info.properties, jdbcType=VARCHAR, typeHandler=com.huawei.cmp.rubikcoordinator.handler.MapVarcharHandler}
            )
        </foreach>
    </insert>

    <insert id="insertTable" parameterType="map">
        insert into T_CMP_MC_TABLE (ID, DATASOURCEID, NAME, ALIAS, PROPERTIES)
        <foreach collection="list" item="info" separator="union all">
            (SELECT
            #{info.id, jdbcType=BIGINT},
            #{datasourceId, jdbcType=BIGINT},
            #{info.name, jdbcType=VARCHAR},
            #{info.aliasName, jdbcType=VARCHAR},
            #{info.properties, jdbcType=VARCHAR, typeHandler=com.huawei.cmp.rubikcoordinator.handler.MapVarcharHandler}
            )
        </foreach>
    </insert>

    <insert id="insertColumn">
        insert into T_CMP_MC_COLUMN (ID, TABLEID, NAME, TYPE, LENGTH, ISINDEX, ISPK) VALUES
        <foreach collection="list" item="info" separator=",">
            (
            #{info.id, jdbcType=BIGINT},
            #{tableId, jdbcType=BIGINT},
            #{info.name, jdbcType=VARCHAR},
            #{info.type, jdbcType=VARCHAR},
            #{info.length, jdbcType=INTEGER},
            #{info.isIndex, jdbcType=INTEGER},
            #{info.isPk, jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <insert id="insertNorth">
        insert into T_CMP_MC_NORTH (ID, SOLUTIONID, DATASOURCEID, OUTPUTNAME, ENCODE, LOGTYPE, PROPERTIES, STATUS)
        <foreach collection="list" item="info" separator="union all">
            (select
            #{info.id, jdbcType=BIGINT},
            #{solutionId, jdbcType=BIGINT},
            #{datasourceId, jdbcType=BIGINT},
            #{outputName, jdbcType=VARCHAR},
            #{encode, jdbcType=VARCHAR},
            #{info.logType, jdbcType=VARCHAR},
            #{info.properties, jdbcType=VARCHAR, typeHandler=com.huawei.cmp.rubikcoordinator.handler.MapVarcharHandler},
            #{info.status, jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <select id="queryDataSourceBySolutionId" resultMap="queryDataSourceResultMapForOracle">
        select
        <include refid="datasourceFields"/>
        from T_CMP_MC_DATASOURCE where SOLUTIONID = #{solutionId}
    </select>

    <select id="queryTableByDataSourceId" resultMap="queryTableResultMapForMysql">
        select
        <include refid="tableFields"/>
        from T_CMP_MC_TABLE where DATASOURCEID = #{dataSourceId}
    </select>

    <select id="queryColumnByTableId" resultType="com.huawei.cmp.rubikcoordinator.model.Column">
        select
        <include refid="columnFields"/>
        from T_CMP_MC_COLUMN where TABLEID = #{tableId}
    </select>

    <select id="queryNorthBySolutionId" resultMap="queryNorthResultMap">
        select distinct
        <include refid="northFields"/>
        from T_CMP_MC_NORTH where SOLUTIONID = #{solutionId}
    </select>

    <select id="queryOutputLogByDataSourceId" resultMap="queryOutputLogResultMapForMysql">
        select
        <include refid="outputFields"/>
        from T_CMP_MC_NORTH where DATASOURCEID = #{dataSourceId}
    </select>

    <update id="updateNorthStatusCollector" parameterType="java.util.List">
        BEGIN
        <foreach collection="list" item="item" open="" close=";" separator=";">
            update T_CMP_MC_NORTH
            <set>
                STATUS = 4
            </set>
            where
            ID = #{item}
        </foreach>
        END
    </update>

    <select id="queryOutputByType" resultType="string">
        select OUTPUTNAME
        from T_CMP_MC_NORTH
        where SOLUTIONID = #{solutionId} and LOGTYPE = #{logType}
    </select>

    <select id="queryOutputLog" parameterType="map" resultMap="queryOutputLogResultMapForMysql">
        select
        <include refid="outputFields"/>
        from T_CMP_MC_NORTH where SOLUTIONID = #{solutionId} and OUTPUTNAME = #{outputName} and LOGTYPE = #{logType}
    </select>


    <select id="queryNodeInfoBySolutionId" resultMap="queryNorthInfoResultMap">
        select SOLUTIONID, OUTPUTNAME, LOGTYPE
        from T_CMP_MC_NORTH
        where SOLUTIONID = #{solutionId}
    </select>

    <update id="updateNorthStatus" parameterType="com.huawei.cmp.rubikcoordinator.model.NorthInfo">
        update T_CMP_MC_NORTH SET STATUS = 1 where SOLUTIONID = #{solutionId} and OUTPUTNAME = #{outputName} and LOGTYPE
        = #{logType}
    </update>

</mapper>