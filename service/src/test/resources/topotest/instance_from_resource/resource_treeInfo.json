{"clientProperties": {}, "dn": "", "level": -1, "next": [{"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "siteName": "DefaultSite", "noConnectStatus": "true", "templateId": "icm.instance.7dc08f718fcd51bfd6986", "provinceId": "NA", "groupName": "DefaultGroup", "oriSolutionName": "S5_CBS_BBIT_0601", "action": "deploy", "solutionId": "7dc08f74ab7714fd9b00d", "configurationId": "icm.instance.7dc08f718fcd51bfd6986", "solutionType": "true", "itpassDn": "NE=3092"}, "dn": "7dc08f74ab7714fd9b00d", "level": 1, "moName": "S5_CBS_BBIT_0601", "moTypeMapping": "cbs.billing.cbs", "next": [{"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda36", "provinceId": "NA", "groupName": "DefaultGroup", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda36", "solutionId": "7dc08f74ab7714fd9b00d", "applicationId": "7dc08f7d248ef44f2d08b", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7d248ef44f2d08b", "level": 2, "moName": "S5_CBS_BBIT_0601_daemonset", "moTypeMapping": "cbs.billing.daemonset.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-xdmrz", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "2df54877-3811-45d4-be0f-816223d32da4", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-xdmrz", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-xdmrz", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "2df54877-3811-45d4-be0f-816223d32da4-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_c9ba979ac928(kwephis3463929)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-4ffcv", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "92df17c3-321b-4ff2-a052-e5dafc983927", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-4ffcv", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-4ffcv", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "92df17c3-321b-4ff2-a052-e5dafc983927-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_ad185479c4f6(kwephis480370)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-4jcq5", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "72629234-184e-4e04-bcb3-7da9a3ef1a0a", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-4jcq5", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-4jcq5", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "72629234-184e-4e04-bcb3-7da9a3ef1a0a-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_68c0dce3781f(kwephis450578)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-4pzpm", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "f1987de1-07a3-48f2-8634-2a17c0f5ff4e", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-4pzpm", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "daemonset-1-4pzpm", "hostIP": "*************", "TypeCode": "", "helmContainer": true, "Code": "", "softDelete": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d"}, "dn": "f1987de1-07a3-48f2-8634-2a17c0f5ff4e-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_a73d1a21d705(kwephis450582)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-5gmhl", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "75d8df00-c212-4504-a10e-81f5589518fa", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-5gmhl", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "daemonset-1-5gmhl", "hostIP": "*************", "TypeCode": "", "helmContainer": true, "Code": "", "softDelete": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d"}, "dn": "75d8df00-c212-4504-a10e-81f5589518fa-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_867ab2b885ce(kwephis450576)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-7zhmx", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "8788473d-62ce-48d6-bb19-6c2662526ea6", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-7zhmx", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-7zhmx", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "8788473d-62ce-48d6-bb19-6c2662526ea6-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_b875d048e579(kwephis450584)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-8pjm9", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "603c6a4d-645f-40e1-b45d-e6e187500d1a", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-8pjm9", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-8pjm9", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "603c6a4d-645f-40e1-b45d-e6e187500d1a-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_bf236bde1ba4(kwephis450579)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-8rvqr", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "e59b9c86-72d8-4d51-acf4-a34c917cf717", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-8rvqr", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-8rvqr", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "e59b9c86-72d8-4d51-acf4-a34c917cf717-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_5c1c2ff9eb75(kwephis480374)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-8vzb9", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "c74ce92f-6c37-4206-a1af-91c9836cf46a", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-8vzb9", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-8vzb9", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "c74ce92f-6c37-4206-a1af-91c9836cf46a-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_cc96b61fef8a(kwephis1643906)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "**********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-ck87t", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "e535bfc1-e27b-413d-8775-51c4164a4249", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-ck87t", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-ck87t", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "e535bfc1-e27b-413d-8775-51c4164a4249-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_5e99acb91407(kwephis450573)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-jrmqj", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "060a56c7-44e1-4453-b241-41a2ef0f27b7", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-jrmqj", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-jrmqj", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "060a56c7-44e1-4453-b241-41a2ef0f27b7-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_b7953a95ba67(kwephis450586)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-lbpht", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "7fd68cae-49f8-4647-a93a-78f7f6c92490", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-lbpht", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "daemonset-1-lbpht", "hostIP": "*************", "TypeCode": "", "helmContainer": true, "Code": "", "softDelete": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d"}, "dn": "7fd68cae-49f8-4647-a93a-78f7f6c92490-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_a65c85991f5c(kwephis450577)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-mbvcg", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "67786732-24c1-4432-a5b7-6c3305ad4011", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-mbvcg", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-mbvcg", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "67786732-24c1-4432-a5b7-6c3305ad4011-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_1a7930989f87(kwephis480368)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "**********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-mp8j6", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "ba41fa13-a0d3-43f0-809d-ef11909b30e4", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-mp8j6", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-mp8j6", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "ba41fa13-a0d3-43f0-809d-ef11909b30e4-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_82ca2060d8ea(kwephis480420)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "**********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-mqf7c", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "003c1e71-529b-42bf-ab03-19194375b23c", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-mqf7c", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-mqf7c", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "003c1e71-529b-42bf-ab03-19194375b23c-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_ebe1656cf291(kwephis450580)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-mv84t", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "0682f71f-9bef-4ba4-a4be-5f0c587119e1", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-mv84t", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-mv84t", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "0682f71f-9bef-4ba4-a4be-5f0c587119e1-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_a8a47a5124ad(kwephis450585)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-r57lb", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "1f6a6dcb-2c02-44b1-be2c-c2076798b2f3", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-r57lb", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-r57lb", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "1f6a6dcb-2c02-44b1-be2c-c2076798b2f3-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_2fd9f74ac857(kwephis480372)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "**********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-rz2fd", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "1c12bb36-c02b-4550-8104-48fed0d7a01f", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-rz2fd", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "daemonset-1-rz2fd", "hostIP": "*************", "TypeCode": "", "helmContainer": true, "Code": "", "softDelete": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d"}, "dn": "1c12bb36-c02b-4550-8104-48fed0d7a01f-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_737d5f6c1b67(kwephis450574)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-snn8r", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "06a58d03-80d7-47b8-9430-e53934a6bd24", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-snn8r", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "daemonset-1-snn8r", "hostIP": "*************", "TypeCode": "", "helmContainer": true, "Code": "", "softDelete": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d"}, "dn": "06a58d03-80d7-47b8-9430-e53934a6bd24-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_33ce10d66042(kwephis1071414)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-t4w5c", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "9d437b5e-c007-4e1f-b8fa-97e56aefd30a", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-t4w5c", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-t4w5c", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "9d437b5e-c007-4e1f-b8fa-97e56aefd30a-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_a32e9125269f(kwephis1643904)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-v2rsw", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "dfa312e9-cea9-4e9a-a701-20553dd416a3", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-v2rsw", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-v2rsw", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "dfa312e9-cea9-4e9a-a701-20553dd416a3-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_318870f67c17(kwephis480375)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557624254", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557610263", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "paascoreType": true, "softDelete": true, "oriPodName": "daemonset-1-87hp6", "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "068f1100-a249-4152-939f-5e79ec30b900", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-87hp6", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "daemonset-1-87hp6", "hostIP": "************", "TypeCode": "", "helmContainer": true, "Code": "", "softDelete": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d"}, "dn": "068f1100-a249-4152-939f-5e79ec30b900-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_b1f1b5a70d45(kwephis1055466)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557618523", "nodeIp": "**********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557618092", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "paascoreType": true, "softDelete": true, "oriPodName": "daemonset-1-fc9kg", "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "1d50f725-02ee-44fe-978b-ca5e8f1ac3da", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-fc9kg", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-fc9kg", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "1d50f725-02ee-44fe-978b-ca5e8f1ac3da-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_9f32566c7d0f(kwephis480365)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717557618523", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717557618092", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-27qkm", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "c99800cb-213c-417d-88ea-716630564376", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-27qkm", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-27qkm", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "c99800cb-213c-417d-88ea-716630564376-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_8ecce633d29c(kwephis480371)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1717740596551", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717740502100", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}, {"clientProperties": {"helmPod": true, "oriPodName": "daemonset-1-spk6s", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "beb28796-43e1-4556-aa84-cc5920e042c4", "level": 3, "moName": "S5_CBS_BBIT_0601_daemonset-1-spk6s", "moTypeMapping": "cbs.billing.daemonset", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "softDelete": true, "oriPodName": "daemonset-1-spk6s", "TypeCode": "", "helmContainer": true, "appId": "7dc08f7d248ef44f2d08b", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d", "Code": ""}, "dn": "beb28796-43e1-4556-aa84-cc5920e042c4-daemonset", "level": 4, "moName": "S5_CBS_BBIT_0601_daemonset_0fd1f156d8ad(kwephis7656702)", "moTypeMapping": "cbs.billing.daemonset_docker", "next": [], "nodeCreateTime": "1718338874028", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1718338780974", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_daemonset_suse12:2406000"}], "nodeCreateTime": "1717399223230", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}], "nodeCreateTime": "1717399208967", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "siteName": "DefaultSite", "noConnectStatus": "true", "templateId": "icm.instance.7dc08f718fcd51bfd6986", "provinceId": "NA", "groupName": "DefaultGroup", "oriSolutionName": "S5_CBS_BBIT_0601", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51bfd6986", "solutionId": "7dc08f74ab7714fd9b00d-1", "solutionType": "true", "itpassDn": "NE=3092"}, "dn": "7dc08f74ab7714fd9b00d-1", "level": 1, "moName": "S5_CBS_BBIT_0601_1", "moTypeMapping": "cbs.billing.cbs", "next": [{"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda38", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda38", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7114e695b8d9072", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7114e695b8d9072", "level": 2, "moName": "S5_CBS_BBIT_0601_1_accessfacade", "moTypeMapping": "cbs.billing.accessfacade.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "accessfacade-1-5b9c8d5fd8-5lt8l", "softDelete": true, "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": "", "provinceId": "NA"}, "dn": "0e085ec4-2e1d-4308-b410-0acda07dbfe7", "level": 3, "moName": "S5_CBS_BBIT_0601_1_accessfacade-1-5b9c8d5fd8-5lt8l", "moTypeMapping": "cbs.billing.accessfacade", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "accessfacade-1-5b9c8d5fd8-5lt8l", "TypeCode": "", "helmContainer": true, "cbsApp": "accessfacade", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7114e695b8d9072", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics", "prometheus": true, "prometheusPort": "17001"}, "dn": "0e085ec4-2e1d-4308-b410-0acda07dbfe7-accessfacade", "level": 4, "moName": "S5_CBS_BBIT_0601_1_accessfacade_980bb498f124(kwephis450578)", "moTypeMapping": "cbs.billing.accessfacade_docker", "next": [], "nodeCreateTime": "1717413115624", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_accessfacade_suse12:2406000"}], "nodeCreateTime": "1717399210290", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_accessfacade_suse12:2406000"}], "nodeCreateTime": "1717399209616", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda45", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda45", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f71edac3564c4074", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f71edac3564c4074", "level": 2, "moName": "S5_CBS_BBIT_0601_1_adaptergmdb", "moTypeMapping": "cbs.billing.adptmdbcloud.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "adaptermdb-1-1-m-0", "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "Code": "", "provinceId": "NA", "softDelete": true, "statefulSet": "adaptermdb-1-1-m-0", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1"}, "dn": "652a7ff2-7143-4a73-87f0-c1294c49981b", "level": 3, "moName": "S5_CBS_BBIT_0601_1_adaptermdb-1-1-m-0", "moTypeMapping": "cbs.billing.adptmdbcloud", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "adaptermdb-1-1-m-0", "TypeCode": "", "helmContainer": true, "cbsApp": "GMDBDockerService", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "statefulSet": "adaptermdb-1-1-m-0", "prometheusScrape": "true", "appId": "7dc08f71edac3564c4074", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002,17003"}, "dn": "652a7ff2-7143-4a73-87f0-c1294c49981b-adaptermdb", "level": 4, "moName": "S5_CBS_BBIT_0601_1_adaptermdb_f6e6c88b91da(kwephis450579)", "moTypeMapping": "cbs.billing.adptmdbcloud_docker", "next": [], "nodeCreateTime": "1717413116189", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_adaptermdb_suse12:2406000"}], "nodeCreateTime": "1717399210537", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_adaptermdb_suse12:2406000"}], "nodeCreateTime": "1717399209888", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda1a", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda1a", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7ac103e55fb107a", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7ac103e55fb107a", "level": 2, "moName": "S5_CBS_BBIT_0601_1_bbfapp", "moTypeMapping": "cbs.billing.bbfapp.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "bbfapp-1-74797949c4-w86wg", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "bf9ede7d-e540-49d5-bedf-721bf48cc0e1", "level": 3, "moName": "S5_CBS_BBIT_0601_1_bbfapp-1-74797949c4-w86wg", "moTypeMapping": "cbs.billing.bbfapp", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "oriPodName": "bbfapp-1-74797949c4-w86wg", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "BMPDockerService", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7ac103e55fb107a", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics1,/metrics2,/metrics3,/metrics4,/metrics5,/metrics6,/metrics7,/metrics8,/metrics9,/metrics10,/metrics11,/metrics12,/metrics13,/metrics14", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001"}, "dn": "bf9ede7d-e540-49d5-bedf-721bf48cc0e1-bbfapp", "level": 4, "moName": "S5_CBS_BBIT_0601_1_bbfapp_2895b4fee0d1(kwephis7656702)", "moTypeMapping": "cbs.billing.bbfapp_docker", "next": [], "nodeCreateTime": "1717413629834", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_bbf_ootb_suse12:2406000"}], "nodeCreateTime": "1717413228239", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_bbf_ootb_suse12:2406000"}], "nodeCreateTime": "1717399210252", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda3b", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda3b", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7ccbcdab26b8075", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7ccbcdab26b8075", "level": 2, "moName": "S5_CBS_BBIT_0601_1_aggregation", "moTypeMapping": "cbs.billing.aggregation.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "aggregation-1-987975749-qr6cz", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "67ccd0d7-50e3-49b7-bbcd-7824b2f987a6", "level": 3, "moName": "S5_CBS_BBIT_0601_1_aggregation-1-987975749-qr6cz", "moTypeMapping": "cbs.billing.aggregation", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "aggregation-1-987975749-qr6cz", "TypeCode": "", "helmContainer": true, "cbsApp": "aggregation", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7ccbcdab26b8075", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002"}, "dn": "67ccd0d7-50e3-49b7-bbcd-7824b2f987a6-aggregation", "level": 4, "moName": "S5_CBS_BBIT_0601_1_aggregation_f20125de651b(kwephis3463929)", "moTypeMapping": "cbs.billing.aggregation_docker", "next": [], "nodeCreateTime": "1718191313265", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_aggregation_ootb_suse12:2406000"}], "nodeCreateTime": "1718191246092", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_aggregation_ootb_suse12:2406000"}], "nodeCreateTime": "1717399210893", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda54", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda54", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7e4a12aeca3a079", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7e4a12aeca3a079", "level": 2, "moName": "S5_CBS_BBIT_0601_1_arapp", "moTypeMapping": "cbs.billing.arapp.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "arapp-1-5b44d864f5-vwcf8", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "97e88f2d-1ba1-40db-b5cf-77a09df1ebda", "level": 3, "moName": "S5_CBS_BBIT_0601_1_arapp-1-5b44d864f5-vwcf8", "moTypeMapping": "cbs.billing.arapp", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "arapp-1-5b44d864f5-vwcf8", "TypeCode": "", "helmContainer": true, "cbsApp": "BMPDockerService", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7e4a12aeca3a079", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics1,/metrics2,/metrics3,/metrics4,/metrics5,/metrics6,/metrics7,/metrics8,/metrics9,/metrics10,/metrics11,/metrics12,/metrics13,/metrics14", "prometheus": true, "prometheusPort": "17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001"}, "dn": "97e88f2d-1ba1-40db-b5cf-77a09df1ebda-arapp", "level": 4, "moName": "S5_CBS_BBIT_0601_1_arapp_a59522013179(kwephis7656702)", "moTypeMapping": "cbs.billing.arapp_docker", "next": [], "nodeCreateTime": "1718279778127", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_ar_ootb_suse12:2406000"}], "nodeCreateTime": "1718279588977", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_ar_ootb_suse12:2406000"}], "nodeCreateTime": "1717399211459", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda43", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda43", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7965a5bfdf9b073", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7965a5bfdf9b073", "level": 2, "moName": "S5_CBS_BBIT_0601_1_adapterapp", "moTypeMapping": "cbs.billing.adapterapp.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "adapterapp-1-74b79c46db-h28vh", "softDelete": true, "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": "", "provinceId": "NA"}, "dn": "e475c1ee-eabf-4d9b-b75a-f66c107e38bf", "level": 3, "moName": "S5_CBS_BBIT_0601_1_adapterapp-1-74b79c46db-h28vh", "moTypeMapping": "cbs.billing.adapterapp", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "adapterapp-1-74b79c46db-h28vh", "TypeCode": "", "helmContainer": true, "cbsApp": "AdapterAPP", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7965a5bfdf9b073", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17004,17005,17006"}, "dn": "e475c1ee-eabf-4d9b-b75a-f66c107e38bf-adapterapp", "level": 4, "moName": "S5_CBS_BBIT_0601_1_adapterapp_ab3330bb8fb4(kwephis450578)", "moTypeMapping": "cbs.billing.adapterapp_docker", "next": [], "nodeCreateTime": "1717413116230", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_adptapp_suse12:2406000"}], "nodeCreateTime": "1717399213282", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_adptapp_suse12:2406000"}], "nodeCreateTime": "1717399212227", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda3c", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda3c", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f717205b1298307b", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f717205b1298307b", "level": 2, "moName": "S5_CBS_BBIT_0601_1_billmanagement", "moTypeMapping": "cbs.billing.billmanagement.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "billmanagement-1-5c69b6bddd-t22mb", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "710088a6-a058-447a-a05f-4e8748e4679a", "level": 3, "moName": "S5_CBS_BBIT_0601_1_billmanagement-1-5c69b6bddd-t22mb", "moTypeMapping": "cbs.billing.billmanagement", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "hostIP": "*************", "oriPodName": "billmanagement-1-5c69b6bddd-t22mb", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "BillManagement", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f717205b1298307b", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001"}, "dn": "710088a6-a058-447a-a05f-4e8748e4679a-billmanagement", "level": 4, "moName": "S5_CBS_BBIT_0601_1_billmanagement_1e0b73569936(kwephis1071414)", "moTypeMapping": "cbs.billing.billmanagement_docker", "next": [], "nodeCreateTime": "1717413984346", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_billmanagement_ootb_suse12:2406000"}], "nodeCreateTime": "1717413983879", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_billmanagement_ootb_suse12:2406000"}], "nodeCreateTime": "1717399213063", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda18", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda18", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7dd80229430e07c", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7dd80229430e07c", "level": 2, "moName": "S5_CBS_BBIT_0601_1_billrun", "moTypeMapping": "cbs.billing.billrun.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "billrun-1-cb64d759-f5dc7", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "da497d53-4018-4f6c-a9ee-cbf0ae02f813", "level": 3, "moName": "S5_CBS_BBIT_0601_1_billrun-1-cb64d759-f5dc7", "moTypeMapping": "cbs.billing.billrun", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "oriPodName": "billrun-1-cb64d759-f5dc7", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "<PERSON><PERSON><PERSON>", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7dd80229430e07c", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics,/metrics,/metrics", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001,17002,17003"}, "dn": "da497d53-4018-4f6c-a9ee-cbf0ae02f813-billrun", "level": 4, "moName": "S5_CBS_BBIT_0601_1_billrun_2c1304e6cb93(kwephis3463929)", "moTypeMapping": "cbs.billing.billrun_docker", "next": [], "nodeCreateTime": "1717414044293", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_billrun_ootb_suse12:2406000"}], "nodeCreateTime": "1717413983082", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_billrun_ootb_suse12:2406000"}], "nodeCreateTime": "1717399213915", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda24", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda24", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7bb4afad1f2b07d", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7bb4afad1f2b07d", "level": 2, "moName": "S5_CBS_BBIT_0601_1_bizmngcharging", "moTypeMapping": "cbs.billing.bizmngcharging.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "bizmngcharging-1-57fbf8cd7c-s2m4r", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "dfb8892b-1c96-41a8-b5f6-1365e6879588", "level": 3, "moName": "S5_CBS_BBIT_0601_1_bizmngcharging-1-57fbf8cd7c-s2m4r", "moTypeMapping": "cbs.billing.bizmngcharging", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "oriPodName": "bizmngcharging-1-57fbf8cd7c-s2m4r", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "BizMngCharging", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7bb4afad1f2b07d", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics,/metrics,/metrics,/metrics,/metrics", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001,17002,17003,17004,17005"}, "dn": "dfb8892b-1c96-41a8-b5f6-1365e6879588-bizmngcharging", "level": 4, "moName": "S5_CBS_BBIT_0601_1_bizmngcharging_0f0ed036caf2(kwephis480365)", "moTypeMapping": "cbs.billing.bizmngcharging_docker", "next": [], "nodeCreateTime": "1717413991831", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_bizmngchg_suse12:2406000"}], "nodeCreateTime": "1717413991475", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_bizmngchg_suse12:2406000"}], "nodeCreateTime": "1717399214704", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda4d", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda4d", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7768af0dda0d07e", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7768af0dda0d07e", "level": 2, "moName": "S5_CBS_BBIT_0601_1_bmapp", "moTypeMapping": "cbs.billing.bmapp.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "bmapp-1-6867c8ddbb-grqq5", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "e184656e-d4fa-48fa-b24a-eae4a69459b3", "level": 3, "moName": "S5_CBS_BBIT_0601_1_bmapp-1-6867c8ddbb-grqq5", "moTypeMapping": "cbs.billing.bmapp", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "oriPodName": "bmapp-1-6867c8ddbb-grqq5", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "BMPDockerService", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7768af0dda0d07e", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics1,/metrics2,/metrics3,/metrics4,/metrics5,/metrics6,/metrics7,/metrics8,/metrics9,/metrics10,/metrics11,/metrics12,/metrics13,/metrics14", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001"}, "dn": "e184656e-d4fa-48fa-b24a-eae4a69459b3-bmapp", "level": 4, "moName": "S5_CBS_BBIT_0601_1_bmapp_9f29c3acfc27(kwephis7656702)", "moTypeMapping": "cbs.billing.bmapp_docker", "next": [], "nodeCreateTime": "1717414071278", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_bm_ootb_suse12:2406000"}], "nodeCreateTime": "1717413665272", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_bm_ootb_suse12:2406000"}], "nodeCreateTime": "1717399215315", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda2c", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda2c", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f791ce14ff22d083", "chatType": "true", "itpassDn": "NE=3092"}, "dn": "7dc08f791ce14ff22d083", "level": 2, "moName": "S5_CBS_BBIT_0601_1_cccapp", "moTypeMapping": "cbs.billing.cccapp.cluster", "next": [{"clientProperties": {"helmPod": true, "paascoreType": true, "softDelete": true, "oriPodName": "cccapp-1-66fbb69b74-cnpkv", "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "aaa19202-77a8-4819-afdf-5ffb81db3269", "level": 3, "moName": "S5_CBS_BBIT_0601_1_cccapp-1-66fbb69b74-cnpkv", "moTypeMapping": "cbs.billing.cccapp", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "oriPodName": "cccapp-1-66fbb69b74-cnpkv", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "BMPDockerService", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f791ce14ff22d083", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics1,/metrics2,/metrics3,/metrics4,/metrics5,/metrics6,/metrics7,/metrics8,/metrics9,/metrics10,/metrics11", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001"}, "dn": "aaa19202-77a8-4819-afdf-5ffb81db3269-cccapp", "level": 4, "moName": "S5_CBS_BBIT_0601_1_cccapp_a778b058bbc2(kwephis7656702)", "moTypeMapping": "cbs.billing.cccapp_docker", "next": [], "nodeCreateTime": "1718338859843", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_ccc_ootb_suse12:2406000"}], "nodeCreateTime": "1718338859701", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_ccc_ootb_suse12:2406000"}], "nodeCreateTime": "1717399216018", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda49", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda49", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7c9bc762df85081", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7c9bc762df85081", "level": 2, "moName": "S5_CBS_BBIT_0601_1_cbpgmdb", "moTypeMapping": "cbs.billing.cbpmdbcloud.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "cbpmdb-1-1-m-0", "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "Code": "", "provinceId": "NA", "softDelete": true, "statefulSet": "cbpmdb-1-1-m-0", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1"}, "dn": "1cb39d1e-7a50-44be-897a-225e200699ac", "level": 3, "moName": "S5_CBS_BBIT_0601_1_cbpmdb-1-1-m-0", "moTypeMapping": "cbs.billing.cbpmdbcloud", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "cbpmdb-1-1-m-0", "TypeCode": "", "helmContainer": true, "cbsApp": "GMDBDockerService", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "statefulSet": "cbpmdb-1-1-m-0", "prometheusScrape": "true", "appId": "7dc08f7c9bc762df85081", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002,17003"}, "dn": "1cb39d1e-7a50-44be-897a-225e200699ac-cbpmdb", "level": 4, "moName": "S5_CBS_BBIT_0601_1_cbpmdb_ad774f98f33d(kwephis450579)", "moTypeMapping": "cbs.billing.cbpmdbcloud_docker", "next": [], "nodeCreateTime": "1717413119659", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_cbpmdb_suse12:2406000"}], "nodeCreateTime": "1717399218747", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_cbpmdb_suse12:2406000"}], "nodeCreateTime": "1717399217416", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda51", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "solutionId": "7dc08f74ab7714fd9b00d-1", "configurationId": "icm.instance.7dc08f718fcd51c2dda51", "applicationId": "7dc08f7377f6bc09b1084", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7377f6bc09b1084", "level": 2, "moName": "S5_CBS_BBIT_0601_1_cdfapp", "moTypeMapping": "cbs.billing.cdfapp.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "cdfapp-1-6f559c8bb4-tpcqh", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "a9821f2e-6826-4cce-aeef-40bc39092d5b", "level": 3, "moName": "S5_CBS_BBIT_0601_1_cdfapp-1-6f559c8bb4-tpcqh", "moTypeMapping": "cbs.billing.cdfapp", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "oriPodName": "cdfapp-1-6f559c8bb4-tpcqh", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "CDFAPP", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7377f6bc09b1084", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics,/metrics,/metrics,/metrics,/metrics", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001,17002,17003,17004,17005"}, "dn": "a9821f2e-6826-4cce-aeef-40bc39092d5b-cdfapp", "level": 4, "moName": "S5_CBS_BBIT_0601_1_cdfapp_5a5e7f4136ee(kwephis480370)", "moTypeMapping": "cbs.billing.cdfapp_docker", "next": [], "nodeCreateTime": "1717414137665", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_cdfapp_suse12:2406000"}], "nodeCreateTime": "1717414005046", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_cdfapp_suse12:2406000"}], "nodeCreateTime": "1717399218598", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda31", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda31", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f78f0d355e428085", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f78f0d355e428085", "level": 2, "moName": "S5_CBS_BBIT_0601_1_cdfgmdb", "moTypeMapping": "cbs.billing.cdfmdbcloud.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "cdfmdb-1-1-m-0", "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "Code": "", "provinceId": "NA", "softDelete": true, "statefulSet": "cdfmdb-1-1-m-0", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1"}, "dn": "fe7d79eb-a05a-4c82-a757-53d1eed73f46", "level": 3, "moName": "S5_CBS_BBIT_0601_1_cdfmdb-1-1-m-0", "moTypeMapping": "cbs.billing.cdfmdbcloud", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "cdfmdb-1-1-m-0", "TypeCode": "", "helmContainer": true, "cbsApp": "GMDBDockerService", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "statefulSet": "cdfmdb-1-1-m-0", "prometheusScrape": "true", "appId": "7dc08f78f0d355e428085", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002,17003"}, "dn": "fe7d79eb-a05a-4c82-a757-53d1eed73f46-cdfmdb", "level": 4, "moName": "S5_CBS_BBIT_0601_1_cdfmdb_6aefaccf52fc(kwephis450579)", "moTypeMapping": "cbs.billing.cdfmdbcloud_docker", "next": [], "nodeCreateTime": "1717413121814", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_cdfmdb_suse12:2406000"}], "nodeCreateTime": "1717399219737", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_cdfmdb_suse12:2406000"}], "nodeCreateTime": "1717399219064", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda16", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda16", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7fd8dc6eadf7086", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7fd8dc6eadf7086", "level": 2, "moName": "S5_CBS_BBIT_0601_1_cdrprocess", "moTypeMapping": "cbs.billing.cdrprocess.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "cdrprocess-1-866f6cc466-jdpcx", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "1e347178-ecda-4e2f-998a-89f3d0d6aa02", "level": 3, "moName": "S5_CBS_BBIT_0601_1_cdrprocess-1-866f6cc466-jdpcx", "moTypeMapping": "cbs.billing.cdrprocess", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "cdrprocess-1-866f6cc466-jdpcx", "TypeCode": "", "helmContainer": true, "cbsApp": "cdrProcess", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7fd8dc6eadf7086", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002"}, "dn": "1e347178-ecda-4e2f-998a-89f3d0d6aa02-cdrprocess", "level": 4, "moName": "S5_CBS_BBIT_0601_1_cdrprocess_aa9bc04f794f(kwephis480368)", "moTypeMapping": "cbs.billing.cdrprocess_docker", "next": [], "nodeCreateTime": "1718179363306", "nodeIp": "**********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_cdrprocess_ootb_suse12:2406000"}], "nodeCreateTime": "1718179269170", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_cdrprocess_ootb_suse12:2406000"}], "nodeCreateTime": "1717399219536", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda37", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda37", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7600bb4f16c0088", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7600bb4f16c0088", "level": 2, "moName": "S5_CBS_BBIT_0601_1_chargingcacheb", "moTypeMapping": "cbs.billing.chargingcacheb.cluster", "next": [{"clientProperties": {"helmPod": true, "paascoreType": true, "oriPodName": "chargingcacheb-1-0", "TypeCode": "", "kind": "Pod", "isPod": true, "Code": "", "provinceId": "NA", "softDelete": true, "statefulSet": "chargingcacheb-1-0", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1"}, "dn": "a82973b5-10c9-4463-8084-e7d1f3d77781", "level": 3, "moName": "S5_CBS_BBIT_0601_1_chargingcacheb-1-0", "moTypeMapping": "cbs.billing.chargingcacheb", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "chargingcacheb-1-0", "TypeCode": "", "helmContainer": true, "cbsApp": "ChargingCacheB", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "statefulSet": "chargingcacheb-1-0", "prometheusScrape": "true", "appId": "7dc08f7600bb4f16c0088", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002"}, "dn": "a82973b5-10c9-4463-8084-e7d1f3d77781-chargingca<PERSON>b", "level": 4, "moName": "S5_CBS_BBIT_0601_1_chargingcacheb_83f91c19a485(kwephis480375)", "moTypeMapping": "cbs.billing.chargingcacheb_docker", "next": [], "nodeCreateTime": "1717413123350", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_chgcache_suse12:2406000"}], "nodeCreateTime": "1717399220693", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_chgcache_suse12:2406000"}], "nodeCreateTime": "1717399219955", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda44", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda44", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7b6696d254f3087", "chatType": "true", "itpassDn": "NE=3092"}, "dn": "7dc08f7b6696d254f3087", "level": 2, "moName": "S5_CBS_BBIT_0601_1_chargingcache", "moTypeMapping": "cbs.billing.chargingcache.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "chargingcache-1-0", "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "Code": "", "provinceId": "NA", "softDelete": true, "statefulSet": "chargingcache-1-0", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1"}, "dn": "ff1e4306-f372-497c-a4a6-3bff4274b451", "level": 3, "moName": "S5_CBS_BBIT_0601_1_chargingcache-1-0", "moTypeMapping": "cbs.billing.chargingcache", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "chargingcache-1-0", "TypeCode": "", "helmContainer": true, "cbsApp": "Charging<PERSON><PERSON>", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "statefulSet": "chargingcache-1-0", "prometheusScrape": "true", "appId": "7dc08f7b6696d254f3087", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002"}, "dn": "ff1e4306-f372-497c-a4a6-3bff4274b451-<PERSON><PERSON><PERSON>", "level": 4, "moName": "S5_CBS_BBIT_0601_1_chargingcache_4ce20871a47f(kwephis480420)", "moTypeMapping": "cbs.billing.chargingcache_docker", "next": [], "nodeCreateTime": "1717413121873", "nodeIp": "**********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_chgcache_suse12:2406000"}], "nodeCreateTime": "1717399221705", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_chgcache_suse12:2406000"}], "nodeCreateTime": "1717399220701", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda52", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda52", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f72b9d598a936089", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f72b9d598a936089", "level": 2, "moName": "S5_CBS_BBIT_0601_1_convergedcharging", "moTypeMapping": "cbs.billing.convergedcharging.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "convergedcharging-1-5cc49997-g97zl", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "23899c17-0627-48cd-aa7a-c53890138b7d", "level": 3, "moName": "S5_CBS_BBIT_0601_1_convergedcharging-1-5cc49997-g97zl", "moTypeMapping": "cbs.billing.convergedcharging", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "oriPodName": "convergedcharging-1-5cc49997-g97zl", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "ConvergedCharging", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f72b9d598a936089", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics,/metrics,/metrics,/metrics,/metrics,/metrics", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001,17002,17003,17004,17005,17006"}, "dn": "23899c17-0627-48cd-aa7a-c53890138b7d-convergedcharging", "level": 4, "moName": "S5_CBS_BBIT_0601_1_convergedcharging_b7e10d244cf2(kwephis480375)", "moTypeMapping": "cbs.billing.convergedcharging_docker", "next": [], "nodeCreateTime": "1717581857960", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_cch_suse12:2406000"}], "nodeCreateTime": "1717581857733", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_cch_suse12:2406000"}], "nodeCreateTime": "1717399221400", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda14", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda14", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7e8bc6017dec08c", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7e8bc6017dec08c", "level": 2, "moName": "S5_CBS_BBIT_0601_1_dbmanproxy", "moTypeMapping": "cbs.billing.dbmanproxy.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "dbmanproxy-1-7ff7cf7895-b8kf5", "softDelete": true, "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": "", "provinceId": "NA"}, "dn": "232d4aae-c312-420b-9a4c-64ba7d0b8b41", "level": 3, "moName": "S5_CBS_BBIT_0601_1_dbmanproxy-1-7ff7cf7895-b8kf5", "moTypeMapping": "cbs.billing.dbmanproxy", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "dbmanproxy-1-7ff7cf7895-b8kf5", "hostIP": "*************", "TypeCode": "", "helmContainer": true, "cbsApp": "dbmanproxy", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7e8bc6017dec08c", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheus": true}, "dn": "232d4aae-c312-420b-9a4c-64ba7d0b8b41-dbmanproxy", "level": 4, "moName": "S5_CBS_BBIT_0601_1_dbmanproxy_7bde5a7ed770(kwephis450576)", "moTypeMapping": "cbs.billing.dbmanproxy_docker", "next": [], "nodeCreateTime": "1717413124541", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_dbmanproxy_suse12:2406000"}], "nodeCreateTime": "1717399223258", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_dbmanproxy_suse12:2406000"}], "nodeCreateTime": "1717399222017", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda30", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda30", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7e3512f44d3308d", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7e3512f44d3308d", "level": 2, "moName": "S5_CBS_BBIT_0601_1_dcapp", "moTypeMapping": "cbs.billing.dcapp.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "dcapp-1-78c7cbd695-t8d4h", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "22ab74ac-31d9-4644-8e50-8596d349a9ed", "level": 3, "moName": "S5_CBS_BBIT_0601_1_dcapp-1-78c7cbd695-t8d4h", "moTypeMapping": "cbs.billing.dcapp", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "dcapp-1-78c7cbd695-t8d4h", "TypeCode": "", "helmContainer": true, "cbsApp": "BMPDockerService", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7e3512f44d3308d", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics1,/metrics2,/metrics3,/metrics4,/metrics5,/metrics6,/metrics7,/metrics8,/metrics9,/metrics10,/metrics11,/metrics12,/metrics13,/metrics14", "prometheus": true, "prometheusPort": "17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001"}, "dn": "22ab74ac-31d9-4644-8e50-8596d349a9ed-dcapp", "level": 4, "moName": "S5_CBS_BBIT_0601_1_dcapp_77ca487413da(kwephis7656702)", "moTypeMapping": "cbs.billing.dcapp_docker", "next": [], "nodeCreateTime": "1717486205861", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_dc_ootb_suse12:2406000"}], "nodeCreateTime": "1717486016270", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_dc_ootb_suse12:2406000"}], "nodeCreateTime": "1717399222590", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda4e", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda4e", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7f84889589d4092", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7f84889589d4092", "level": 2, "moName": "S5_CBS_BBIT_0601_1_gdbcloud", "moTypeMapping": "cbs.billing.bmpdbcloud.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "bmpdb-1-1-m-0", "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "Code": "", "provinceId": "NA", "softDelete": true, "statefulSet": "bmpdb-1-1-m-0", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1"}, "dn": "6e9b7db7-b184-4dd1-88ec-b799b7e88ff8", "level": 3, "moName": "S5_CBS_BBIT_0601_1_bmpdb-1-1-m-0", "moTypeMapping": "cbs.billing.bmpdbcloud", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "bmpdb-1-1-m-0", "hostIP": "************", "TypeCode": "", "helmContainer": true, "cbsApp": "GMDBDockerService", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "statefulSet": "bmpdb-1-1-m-0", "prometheusScrape": "true", "appId": "7dc08f7f84889589d4092", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002,17003,17004,17005"}, "dn": "6e9b7db7-b184-4dd1-88ec-b799b7e88ff8-bmpdb", "level": 4, "moName": "S5_CBS_BBIT_0601_1_bmpdb_5798c7691eda(kwephis1055466)", "moTypeMapping": "cbs.billing.bmpdbcloud_docker", "next": [], "nodeCreateTime": "1717413126652", "nodeIp": "**********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/gdbcloud_suse12:2406000"}], "nodeCreateTime": "1717399225476", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/gdbcloud_suse12:2406000"}], "nodeCreateTime": "1717399224024", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fd1bef6d6031", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fd1bef6d6031", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f711511235c27093", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f711511235c27093", "level": 2, "moName": "S5_CBS_BBIT_0601_1_gdbcloud-billdb", "moTypeMapping": "cbs.billing.billdbcloud.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "billdb-1-1-m-0", "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "Code": "", "provinceId": "NA", "softDelete": true, "statefulSet": "billdb-1-1-m-0", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1"}, "dn": "2d187976-4535-4fdd-9cef-6129307133b7", "level": 3, "moName": "S5_CBS_BBIT_0601_1_billdb-1-1-m-0", "moTypeMapping": "cbs.billing.billdbcloud", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "billdb-1-1-m-0", "hostIP": "*************", "TypeCode": "", "helmContainer": true, "cbsApp": "GMDBDockerService", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "statefulSet": "billdb-1-1-m-0", "prometheusScrape": "true", "appId": "7dc08f711511235c27093", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002,17003,17004,17005"}, "dn": "2d187976-4535-4fdd-9cef-6129307133b7-billdb", "level": 4, "moName": "S5_CBS_BBIT_0601_1_billdb_ccd6ea896f7f(kwephis450577)", "moTypeMapping": "cbs.billing.billdbcloud_docker", "next": [], "nodeCreateTime": "1717413127267", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/gdbcloud_suse12:2406000"}], "nodeCreateTime": "1717399225771", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/gdbcloud_suse12:2406000"}], "nodeCreateTime": "1717399224610", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fd1bef60d02b", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fd1bef60d02b", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7e4a6a84db28096", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7e4a6a84db28096", "level": 2, "moName": "S5_CBS_BBIT_0601_1_gdbcloud-usrdb", "moTypeMapping": "cbs.billing.usrdbcloud.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "usrdb-1-1-m-0", "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "Code": "", "provinceId": "NA", "softDelete": true, "statefulSet": "usrdb-1-1-m-0", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1"}, "dn": "76636de3-18b1-4150-9da9-10a32f85ced8", "level": 3, "moName": "S5_CBS_BBIT_0601_1_usrdb-1-1-m-0", "moTypeMapping": "cbs.billing.usrdbcloud", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "usrdb-1-1-m-0", "TypeCode": "", "helmContainer": true, "cbsApp": "GMDBDockerService", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "statefulSet": "usrdb-1-1-m-0", "prometheusScrape": "true", "appId": "7dc08f7e4a6a84db28096", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002,17003,17004,17005"}, "dn": "76636de3-18b1-4150-9da9-10a32f85ced8-usrdb", "level": 4, "moName": "S5_CBS_BBIT_0601_1_usrdb_d13701b1b798(kwephis450586)", "moTypeMapping": "cbs.billing.usrdbcloud_docker", "next": [], "nodeCreateTime": "1717413127622", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/gdbcloud_suse12:2406000"}], "nodeCreateTime": "1717399225987", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/gdbcloud_suse12:2406000"}], "nodeCreateTime": "1717399225283", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fd1bef8ed043", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fd1bef8ed043", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f76051906e838094", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f76051906e838094", "level": 2, "moName": "S5_CBS_BBIT_0601_1_gdbcloud-billsharedb", "moTypeMapping": "cbs.billing.billsharedbcloud.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "billsharedb-1-1-m-0", "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "Code": "", "provinceId": "NA", "softDelete": true, "statefulSet": "billsharedb-1-1-m-0", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1"}, "dn": "ddce29b9-2c30-421b-ac5d-a17e370998fe", "level": 3, "moName": "S5_CBS_BBIT_0601_1_billsharedb-1-1-m-0", "moTypeMapping": "cbs.billing.billsharedbcloud", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "billsharedb-1-1-m-0", "hostIP": "************", "TypeCode": "", "helmContainer": true, "cbsApp": "GMDBDockerService", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "statefulSet": "billsharedb-1-1-m-0", "prometheusScrape": "true", "appId": "7dc08f76051906e838094", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002,17003,17004,17005"}, "dn": "ddce29b9-2c30-421b-ac5d-a17e370998fe-billsharedb", "level": 4, "moName": "S5_CBS_BBIT_0601_1_billsharedb_9e435ff32b6e(kwephis1055466)", "moTypeMapping": "cbs.billing.billsharedbcloud_docker", "next": [], "nodeCreateTime": "1717413127628", "nodeIp": "**********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/gdbcloud_suse12:2406000"}], "nodeCreateTime": "1717399226683", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/gdbcloud_suse12:2406000"}], "nodeCreateTime": "1717399225651", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda1f", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda1f", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f769732576bf0099", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f769732576bf0099", "level": 2, "moName": "S5_CBS_BBIT_0601_1_glapp", "moTypeMapping": "cbs.billing.glapp.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "glapp-1-7b67974db5-sh5z6", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "107bcf07-04c0-4c1d-96dc-57245356ab1a", "level": 3, "moName": "S5_CBS_BBIT_0601_1_glapp-1-7b67974db5-sh5z6", "moTypeMapping": "cbs.billing.glapp", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "oriPodName": "glapp-1-7b67974db5-sh5z6", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "BMPDockerService", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f769732576bf0099", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics1,/metrics2,/metrics3,/metrics4,/metrics5,/metrics6,/metrics7,/metrics8,/metrics9,/metrics10,/metrics11,/metrics12,/metrics13,/metrics14", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001"}, "dn": "107bcf07-04c0-4c1d-96dc-57245356ab1a-glapp", "level": 4, "moName": "S5_CBS_BBIT_0601_1_glapp_70b1055e8f41(kwephis7656702)", "moTypeMapping": "cbs.billing.glapp_docker", "next": [], "nodeCreateTime": "1717413625417", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_gl_ootb_suse12:2406000"}], "nodeCreateTime": "1717413228179", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_gl_ootb_suse12:2406000"}], "nodeCreateTime": "1717399226013", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda20", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda20", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f74d6f7b0799a09a", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f74d6f7b0799a09a", "level": 2, "moName": "S5_CBS_BBIT_0601_1_hugebillgen", "moTypeMapping": "cbs.billing.hugebillgen.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "hugebillgen-1-866f75795b-4fzmm", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "8dc24054-7cff-4377-a582-c70b77df06af", "level": 3, "moName": "S5_CBS_BBIT_0601_1_hugebillgen-1-866f75795b-4fzmm", "moTypeMapping": "cbs.billing.hugebillgen", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "oriPodName": "hugebillgen-1-866f75795b-4fzmm", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "HugeBillGen", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f74d6f7b0799a09a", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001"}, "dn": "8dc24054-7cff-4377-a582-c70b77df06af-hugebillgen", "level": 4, "moName": "S5_CBS_BBIT_0601_1_hugebillgen_139f3b4f509b(kwephis480372)", "moTypeMapping": "cbs.billing.hugebillgen_docker", "next": [], "nodeCreateTime": "1717414056797", "nodeIp": "**********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_hugebillgen_ootb_suse12:2406000"}], "nodeCreateTime": "1717413983762", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_hugebillgen_ootb_suse12:2406000"}], "nodeCreateTime": "1717399226570", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda39", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda39", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7130cb432b2c0a0", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7130cb432b2c0a0", "level": 2, "moName": "S5_CBS_BBIT_0601_1_internaladapter", "moTypeMapping": "cbs.billing.internaladapter.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "internaladapter-1-8b6cf5d4-nksrv", "softDelete": true, "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": "", "provinceId": "NA"}, "dn": "9ff0f16b-9e46-4a43-b7ad-790e6ff4fdb6", "level": 3, "moName": "S5_CBS_BBIT_0601_1_internaladapter-1-8b6cf5d4-nksrv", "moTypeMapping": "cbs.billing.internaladapter", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "internaladapter-1-8b6cf5d4-nksrv", "TypeCode": "", "helmContainer": true, "cbsApp": "internalAdapter", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7130cb432b2c0a0", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17004,17005,17006"}, "dn": "9ff0f16b-9e46-4a43-b7ad-790e6ff4fdb6-internaladapter", "level": 4, "moName": "S5_CBS_BBIT_0601_1_internaladapter_792fca07d368(kwephis450578)", "moTypeMapping": "cbs.billing.internaladapter_docker", "next": [], "nodeCreateTime": "1717413130602", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_adptapp_suse12:2406000"}], "nodeCreateTime": "1717399229207", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_adptapp_suse12:2406000"}], "nodeCreateTime": "1717399227319", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda39", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda39", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7130cb432b2c0a0", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7130cb432b2c0a0", "level": 2, "moName": "S5_CBS_BBIT_0601_1_internaladapter", "moTypeMapping": "cbs.billing.internaladapter.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "internaladapter-1-8b6cf5d4-nksrv", "softDelete": true, "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": "", "provinceId": "NA"}, "dn": "9ff0f16b-9e46-4a43-b7ad-790e6ff4fdb6", "level": 3, "moName": "S5_CBS_BBIT_0601_1_internaladapter-1-8b6cf5d4-nksrv", "moTypeMapping": "cbs.billing.internaladapter", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "internaladapter-1-8b6cf5d4-nksrv", "TypeCode": "", "helmContainer": true, "cbsApp": "internalAdapter", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7130cb432b2c0a0", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17004,17005,17006"}, "dn": "9ff0f16b-9e46-4a43-b7ad-790e6ff4fdb6-internaladapter", "level": 4, "moName": "S5_CBS_BBIT_0601_1_internaladapter_792fca07d368(kwephis450578)", "moTypeMapping": "cbs.billing.internaladapter_docker", "next": [], "nodeCreateTime": "1717413130602", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_adptapp_suse12:2406000"}], "nodeCreateTime": "1717399229207", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_adptapp_suse12:2406000"}], "nodeCreateTime": "1717399227319", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda57", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda57", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f770b075b749a0a1", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f770b075b749a0a1", "level": 2, "moName": "S5_CBS_BBIT_0601_1_invgmdb", "moTypeMapping": "cbs.billing.invmdbcloud.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "invmdb-1-1-m-0", "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "Code": "", "provinceId": "NA", "softDelete": true, "statefulSet": "invmdb-1-1-m-0", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1"}, "dn": "8d331212-67be-46d1-9e1a-292b3509c32e", "level": 3, "moName": "S5_CBS_BBIT_0601_1_invmdb-1-1-m-0", "moTypeMapping": "cbs.billing.invmdbcloud", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "invmdb-1-1-m-0", "TypeCode": "", "helmContainer": true, "cbsApp": "GMDBDockerService", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "statefulSet": "invmdb-1-1-m-0", "prometheusScrape": "true", "appId": "7dc08f770b075b749a0a1", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002,17003"}, "dn": "8d331212-67be-46d1-9e1a-292b3509c32e-invmdb", "level": 4, "moName": "S5_CBS_BBIT_0601_1_invmdb_ca45c8810e09(kwephis1643906)", "moTypeMapping": "cbs.billing.invmdbcloud_docker", "next": [], "nodeCreateTime": "1717413130894", "nodeIp": "**********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_invmdb_suse12:2406000"}], "nodeCreateTime": "1717399229687", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_invmdb_suse12:2406000"}], "nodeCreateTime": "1717399227908", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda2a", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda2a", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7a19177f7f590a6", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7a19177f7f590a6", "level": 2, "moName": "S5_CBS_BBIT_0601_1_nfsapp", "moTypeMapping": "cbs.billing.nfsappcloud.cluster", "next": [{"clientProperties": {"helmPod": true, "paascoreType": true, "softDelete": true, "oriPodName": "nfsapp-1-global-1-0", "TypeCode": "", "statefulSet": "nfsapp-1-global-1-0", "kind": "Pod", "isPod": true, "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "3092aa7a-a268-42af-a45a-868fd3ee37b4", "level": 3, "moName": "S5_CBS_BBIT_0601_1_nfsapp-1-global-1-0", "moTypeMapping": "cbs.billing.nfsappcloud", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "nfsapp-1-global-1-0", "TypeCode": "", "helmContainer": true, "cbsApp": "NFSAPPDockerService", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "statefulSet": "nfsapp-1-global-1-0", "prometheusScrape": "true", "appId": "7dc08f7a19177f7f590a6", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002,17003"}, "dn": "3092aa7a-a268-42af-a45a-868fd3ee37b4-nfsapp", "level": 4, "moName": "S5_CBS_BBIT_0601_1_nfsapp_07cee2f84ded(kwephis480371)", "moTypeMapping": "cbs.billing.nfsappcloud_docker", "next": [], "nodeCreateTime": "1717413131752", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_nfs_suse12:2406000"}], "nodeCreateTime": "1717399230779", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_nfs_suse12:2406000"}], "nodeCreateTime": "1717399228522", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda4b", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda4b", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f75559ca08e080a8", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f75559ca08e080a8", "level": 2, "moName": "S5_CBS_BBIT_0601_1_notification", "moTypeMapping": "cbs.billing.notification.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "notification-1-7f648849c9-9xmfr", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "ba7e86e4-9978-42e6-aff6-416f9da35a4e", "level": 3, "moName": "S5_CBS_BBIT_0601_1_notification-1-7f648849c9-9xmfr", "moTypeMapping": "cbs.billing.notification", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "oriPodName": "notification-1-7f648849c9-9xmfr", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "Notification", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f75559ca08e080a8", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics,/metrics", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001,17002"}, "dn": "ba7e86e4-9978-42e6-aff6-416f9da35a4e-notification", "level": 4, "moName": "S5_CBS_BBIT_0601_1_notification_e68e46e6434f(kwephis480370)", "moTypeMapping": "cbs.billing.notification_docker", "next": [], "nodeCreateTime": "1717414120629", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_notif_suse12:2406000"}], "nodeCreateTime": "1717413983881", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_notif_suse12:2406000"}], "nodeCreateTime": "1717399229132", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda2f", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda2f", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7bec7c2edab20a7", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7bec7c2edab20a7", "level": 2, "moName": "S5_CBS_BBIT_0601_1_northcgw", "moTypeMapping": "cbs.billing.northcgw.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "northcgw-1-74794967c9-sc89c", "softDelete": true, "paascoreType": true, "TypeCode": "", "kind": "Pod", "isPod": true, "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": "", "provinceId": "NA"}, "dn": "22a0d378-836a-4e99-bfc0-f729199b2b74", "level": 3, "moName": "S5_CBS_BBIT_0601_1_northcgw-1-74794967c9-sc89c", "moTypeMapping": "cbs.billing.northcgw", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "northcgw-1-74794967c9-sc89c", "TypeCode": "", "helmContainer": true, "cbsApp": "NorthCGW", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7bec7c2edab20a7", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics", "prometheus": true, "prometheusPort": "17001"}, "dn": "22a0d378-836a-4e99-bfc0-f729199b2b74-northcgw", "level": 4, "moName": "S5_CBS_BBIT_0601_1_northcgw_1ede7d5b059e(kwephis450585)", "moTypeMapping": "cbs.billing.northcgw_docker", "next": [], "nodeCreateTime": "1717413132474", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_cgw_suse12:2406000"}], "nodeCreateTime": "1717399232111", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_cgw_suse12:2406000"}], "nodeCreateTime": "1717399230098", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda2d", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda2d", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7345ae33f50a0aa", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7345ae33f50a0aa", "level": 2, "moName": "S5_CBS_BBIT_0601_1_omgmdb", "moTypeMapping": "cbs.billing.omdbcloud.cluster", "next": [{"clientProperties": {"helmPod": true, "paascoreType": true, "oriPodName": "omgmdb-1-1-m-0", "softDelete": true, "TypeCode": "", "statefulSet": "omgmdb-1-1-m-0", "kind": "Pod", "isPod": true, "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "21979b70-f11e-490b-bb1c-1b717c5fe543", "level": 3, "moName": "S5_CBS_BBIT_0601_1_omgmdb-1-1-m-0", "moTypeMapping": "cbs.billing.omdbcloud", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "omgmdb-1-1-m-0", "TypeCode": "", "helmContainer": true, "cbsApp": "GMDBDockerService", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "statefulSet": "omgmdb-1-1-m-0", "prometheusScrape": "true", "appId": "7dc08f7345ae33f50a0aa", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002,17003"}, "dn": "21979b70-f11e-490b-bb1c-1b717c5fe543-omgmdb", "level": 4, "moName": "S5_CBS_BBIT_0601_1_omgmdb_d293d1060627(kwephis1643906)", "moTypeMapping": "cbs.billing.omdbcloud_docker", "next": [], "nodeCreateTime": "1717413132863", "nodeIp": "**********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_omgmdb_suse12:2406000"}], "nodeCreateTime": "1717399233415", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_integration_omgmdb_suse12:2406000"}], "nodeCreateTime": "1717399230976", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda3a", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda3a", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7205896e37c50ab", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7205896e37c50ab", "level": 2, "moName": "S5_CBS_BBIT_0601_1_onlinecharging", "moTypeMapping": "cbs.billing.onlinecharging.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "onlinecharging-1-76cb58df7-pgdrt", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "dc8dc039-db6d-4c30-9c95-4e9d7cb8b781", "level": 3, "moName": "S5_CBS_BBIT_0601_1_onlinecharging-1-76cb58df7-pgdrt", "moTypeMapping": "cbs.billing.onlinecharging", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "onlinecharging-1-76cb58df7-pgdrt", "TypeCode": "", "helmContainer": true, "cbsApp": "OnlineCharging", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7205896e37c50ab", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics,/metrics,/metrics,/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002,17003,17004,17005,17006"}, "dn": "dc8dc039-db6d-4c30-9c95-4e9d7cb8b781-onlinecharging", "level": 4, "moName": "S5_CBS_BBIT_0601_1_onlinecharging_1a910f3f2f0f(kwephis480365)", "moTypeMapping": "cbs.billing.onlinecharging_docker", "next": [], "nodeCreateTime": "1718191266287", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_onlinechg_suse12:2406000"}], "nodeCreateTime": "1718191168428", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_onlinechg_suse12:2406000"}], "nodeCreateTime": "1717399231483", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda23", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda23", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7e4a30e0e39f0ad", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7e4a30e0e39f0ad", "level": 2, "moName": "S5_CBS_BBIT_0601_1_recurringrating", "moTypeMapping": "cbs.billing.recurringrating.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "recurringrating-1-89fccc9c4-w8nw8", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "238791d3-25fd-4d88-b2ef-db6b945a6cab", "level": 3, "moName": "S5_CBS_BBIT_0601_1_recurringrating-1-89fccc9c4-w8nw8", "moTypeMapping": "cbs.billing.recurringrating", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "oriPodName": "recurringrating-1-89fccc9c4-w8nw8", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "RecurringRating", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7e4a30e0e39f0ad", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics,/metrics,/metrics,/metrics,/metrics,/metrics,/metrics,/metrics", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001,17002,17003,17004,17005,17006,17007,17008"}, "dn": "238791d3-25fd-4d88-b2ef-db6b945a6cab-recurringrating", "level": 4, "moName": "S5_CBS_BBIT_0601_1_recurringrating_3532d2fce756(kwephis480365)", "moTypeMapping": "cbs.billing.recurringrating_docker", "next": [], "nodeCreateTime": "1717414102732", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_recurrating_suse12:2406000"}], "nodeCreateTime": "1717413991020", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_recurrating_suse12:2406000"}], "nodeCreateTime": "1717399232039", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda27", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda27", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7c18e98f61220ae", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7c18e98f61220ae", "level": 2, "moName": "S5_CBS_BBIT_0601_1_rerating", "moTypeMapping": "cbs.billing.rerating.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "rerating-1-c9f6f5fdb-lftnf", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "18c92e42-6188-4a9f-add5-058e89e5868b", "level": 3, "moName": "S5_CBS_BBIT_0601_1_rerating-1-c9f6f5fdb-lftnf", "moTypeMapping": "cbs.billing.rerating", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "oriPodName": "rerating-1-c9f6f5fdb-lftnf", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "ReRating", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7c18e98f61220ae", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics,/metrics", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001,17002"}, "dn": "18c92e42-6188-4a9f-add5-058e89e5868b-rerating", "level": 4, "moName": "S5_CBS_BBIT_0601_1_rerating_bc147ca5708e(kwephis480370)", "moTypeMapping": "cbs.billing.rerating_docker", "next": [], "nodeCreateTime": "1717414105398", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_rerating_suse12:2406000"}], "nodeCreateTime": "1717413983345", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_rerating_suse12:2406000"}], "nodeCreateTime": "1717399232813", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda33", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "solutionId": "7dc08f74ab7714fd9b00d-1", "configurationId": "icm.instance.7dc08f718fcd51c2dda33", "applicationId": "7dc08f7f1d2b9903230b1", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7f1d2b9903230b1", "level": 2, "moName": "S5_CBS_BBIT_0601_1_southcgw", "moTypeMapping": "cbs.billing.charginggw.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "southcgw-1-5899864d7c-lp795", "softDelete": true, "paascoreType": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "b742f646-2c95-4bea-b386-9341e6eb88b6", "level": 3, "moName": "S5_CBS_BBIT_0601_1_southcgw-1-5899864d7c-lp795", "moTypeMapping": "cbs.billing.charginggw", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "paascoreType": true, "uDockerType": true, "oriPodName": "southcgw-1-5899864d7c-lp795", "TypeCode": "", "helmContainer": true, "cbsApp": "SouthCGW", "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7f1d2b9903230b1", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPath": "/metrics,/metrics", "prometheus": true, "prometheusPort": "17001,17002"}, "dn": "b742f646-2c95-4bea-b386-9341e6eb88b6-southcgw", "level": 4, "moName": "S5_CBS_BBIT_0601_1_southcgw_867671f3de16(kwephis450578)", "moTypeMapping": "cbs.billing.charginggw_docker", "next": [], "nodeCreateTime": "1717413134214", "nodeIp": "***********", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_cgw_suse12:2406000"}], "nodeCreateTime": "1717399235529", "nodeIp": "*************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_cgw_suse12:2406000"}], "nodeCreateTime": "1717399233494", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}, {"clientProperties": {"oriSolutionId": "7dc08f74ab7714fd9b00d", "applicationType": "true", "takeover": "false", "siteName": "DefaultSite", "templateId": "icm.instance.7dc08f718fcd51c2dda3d", "provinceId": "NA", "groupName": "DefaultGroup", "siteIdForDv": "1", "action": "deploy", "configurationId": "icm.instance.7dc08f718fcd51c2dda3d", "solutionId": "7dc08f74ab7714fd9b00d-1", "applicationId": "7dc08f7c4858b71e520b3", "itpassDn": "NE=3092", "chatType": "true"}, "dn": "7dc08f7c4858b71e520b3", "level": 2, "moName": "S5_CBS_BBIT_0601_1_upcapp", "moTypeMapping": "cbs.billing.upcapp.cluster", "next": [{"clientProperties": {"helmPod": true, "oriPodName": "upcapp-1-679874998d-96bpm", "paascoreType": true, "softDelete": true, "TypeCode": "", "isPod": true, "kind": "Pod", "namespace": "ns000000000000000000001", "solutionId": "7dc08f74ab7714fd9b00d-1", "Code": ""}, "dn": "910e87f5-2309-4277-8032-1e717209efaf", "level": 3, "moName": "S5_CBS_BBIT_0601_1_upcapp-1-679874998d-96bpm", "moTypeMapping": "cbs.billing.upcapp", "next": [{"clientProperties": {"hwsContaineridSource": "V2", "oriPodName": "upcapp-1-679874998d-96bpm", "paascoreType": true, "uDockerType": true, "TypeCode": "", "cbsApp": "BMPDockerService", "helmContainer": true, "prometheusExporterType": "cbs", "prometheusScheme": "http", "Code": "", "softDelete": true, "prometheusScrape": "true", "appId": "7dc08f7c4858b71e520b3", "namespace": "ns000000000000000000001", "prometheus": true, "prometheusPath": "/metrics1,/metrics2,/metrics3,/metrics4,/metrics5,/metrics6,/metrics7,/metrics8,/metrics9,/metrics10,/metrics11,/metrics12,/metrics13,/metrics14", "solutionId": "7dc08f74ab7714fd9b00d-1", "prometheusPort": "17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001,17001"}, "dn": "910e87f5-2309-4277-8032-1e717209efaf-upcapp", "level": 4, "moName": "S5_CBS_BBIT_0601_1_upcapp_5a5215cee572(kwephis7656702)", "moTypeMapping": "cbs.billing.upcapp_docker", "next": [], "nodeCreateTime": "1717413631105", "nodeIp": "************", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_upc_ootb_suse12:2406000"}], "nodeCreateTime": "1717413232385", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "*************:20202/ns000000000000000000001/cbs_upc_ootb_suse12:2406000"}], "nodeCreateTime": "1717399234017", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}], "nodeCreateTime": "1717399209264", "nodeIp": "", "nodeServiceStatus": "Enabled", "version": "24.06.000ForSingleChart"}], "version": ""}