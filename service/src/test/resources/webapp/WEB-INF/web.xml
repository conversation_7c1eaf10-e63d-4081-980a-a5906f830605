<?xml version="1.0" encoding="UTF-8" standalone="no"?><web-app xmlns="http://java.sun.com/xml/ns/j2ee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" version="2.4" xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee          http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd">
	<servlet>
		<servlet-name>RestServlet</servlet-name>
		<servlet-class>com.huawei.bsp.roa.web.DispatcherServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>RestServlet</servlet-name>
		<url-pattern>/rest/*</url-pattern>
	</servlet-mapping>

	<listener>
		<listener-class>com.huawei.bsp.framework.FrameworkLoader</listener-class>
	</listener>
	<session-config>
		<session-timeout>10000000</session-timeout>
	</session-config>

	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>

	<filter>
		<filter-name>authFilter</filter-name>
		<filter-class>com.huawei.cloudsop.common.tokenhelper.filter.AuthFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>authFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<context-param>
		<param-name>log4jConfigLocation</param-name>
		<param-value>etc/log4j/log4j2.xml</param-value>
	</context-param>
	<context-param>
		<param-name>log4jRefreshInterval</param-name>
		<param-value>30000</param-value>
	</context-param>

	<filter>
		<filter-name>ResponseHeaderFilter</filter-name>
		<filter-class>com.huawei.i2000.cbb.security.filter.ResponseHeaderFilter</filter-class>
		<init-param>
			<param-name>ResponseHeaders</param-name>
			<param-value>etc/config/responseHeaders.json</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>ResponseHeaderFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<filter>
		<filter-name>UnormalizedUrlFilter</filter-name>
		<filter-class>com.huawei.i2000.cbb.security.filter.UnormalizedUrlFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>UnormalizedUrlFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<filter>
		<filter-name>urlNormalizeFilter</filter-name>
		<filter-class>com.huawei.i2000.cbb.security.filter.UnormalizedUrlFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>urlNormalizeFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>

	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>
			classpath*:spring/*.xml
		</param-value>
	</context-param>
</web-app>