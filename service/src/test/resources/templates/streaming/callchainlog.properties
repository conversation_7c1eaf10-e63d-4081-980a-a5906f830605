client.sources=${logtype}-${solutiontype}-source${channel}
client.channels=${logtype}-${solutiontype}-channel${channel}
client.sinks=${logtype}-${solutiontype}-sink${channel}

#######source config ##########
client.sources.${logtype}-${solutiontype}-source${channel}.type = com.huawei.cmp.callchain.flume.server.kafka.KafkaSource
client.sources.${logtype}-${solutiontype}-source${channel}.kafka.bootstrap.servers = ${brokerAddr}
client.sources.${logtype}-${solutiontype}-source${channel}.groupId = ${logtype}-${solutiontype}-streaming
client.sources.${logtype}-${solutiontype}-source${channel}.topic = logmatrix_${logtype}_${solutiontype}
client.sources.${logtype}-${solutiontype}-source${channel}.channels = ${logtype}-${solutiontype}-channel${channel}
client.sources.${logtype}-${solutiontype}-source${channel}.batchSize = 5000
client.sources.${logtype}-${solutiontype}-source${channel}.kafka.zookeeper.connection.timeout.ms = 2147483647
client.sources.${logtype}-${solutiontype}-source${channel}.kafka.fetch.min.bytes = 10240
client.sources.${logtype}-${solutiontype}-source${channel}.kafka.fetch.wait.max.ms = 100
client.sources.${logtype}-${solutiontype}-source${channel}.kafka.zookeeper.principal = zookeeper/hadoop.hadoop.com
client.sources.${logtype}-${solutiontype}-source${channel}.kafka.security.mode = true
client.sources.${logtype}-${solutiontype}-source${channel}.solutiontype = ${solutionMotype}

#######channel config ##########
client.channels.${logtype}-${solutiontype}-channel${channel}.type = memory
client.channels.${logtype}-${solutiontype}-channel${channel}.capacity = 50000
client.channels.${logtype}-${solutiontype}-channel${channel}.transactionCapacity = 10000

#######sink config ##########
client.sinks.${logtype}-${solutiontype}-sink${channel}.type = com.huawei.cmp.callchain.flume.server.sink.CallChainVSearchSink
client.sinks.${logtype}-${solutiontype}-sink${channel}.channel = ${logtype}-${solutiontype}-channel${channel}
client.sinks.${logtype}-${solutiontype}-sink${channel}.hostNames = ${esUrl}
client.sinks.${logtype}-${solutiontype}-sink${channel}.concurrency = 2
client.sinks.${logtype}-${solutiontype}-sink${channel}.batchSize = 5000
client.sinks.${logtype}-${solutiontype}-sink${channel}.bulkSize= 10485760
client.sinks.${logtype}-${solutiontype}-sink${channel}.interval = 5
client.sinks.${logtype}-${solutiontype}-sink${channel}.password = default
client.sinks.${logtype}-${solutiontype}-sink${channel}.index = cmp_${logtype}-${solutiontype}
client.sinks.${logtype}-${solutiontype}-sink${channel}.esType = cmp_${logtype}-${solutiontype}
client.sinks.${logtype}-${solutiontype}-sink${channel}.clientType = VS
client.sinks.${logtype}-${solutiontype}-sink${channel}.period = ${period}
