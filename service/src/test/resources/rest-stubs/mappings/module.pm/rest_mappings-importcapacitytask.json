{"mappings": [{"request": {"method": "GET", "urlPath": "/rest/eammimservice/v1/mo/type"}, "response": {"status": 200, "jsonBody": [{"@moClass": "com.huawei.oms.eam.mo.NetworkElement", "sequenceNo": 1, "dn": "OS=1", "fdn": "1.1", "typeID": 0, "type": "OMS", "version": "", "displayName": "OSS", "name": "OSS", "vendor": "<PERSON><PERSON><PERSON>", "description": null, "owner": null, "createdTime": 1687828427231, "medNodeID": null, "alarmStatus": "Cleared", "clientProperties": {"TypeCode": "", "Code": ""}, "children": [], "parent": "/", "msId": null, "groupId": null, "tenantId": null, "adminStatus": "Unlocked", "availableStatus": "Normal", "usageStatus": "Active", "operationalStatus": "Enabled", "language": "zh_CN", "timeZone": "China Standard Time", "connectStatus": "NoDetect", "location": null, "maintainer": null, "contact": null, "deviceCode": null, "address": "", "code": "", "typeCode": "", "lifecycleStatus": "Unknown", "ipaddress": "", "hastatus": "Single"}]}}, {"request": {"method": "POST", "url": "/rest/pm/bigscreen/i2kQueryPmTask", "bodyPatterns": [{"equalToJson": {"moList": [{"bigVersion": "*", "motype": "OMS"}], "pageSize": 1000, "startPage": 0}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": [{"template": {"object": {"format": "${ip}"}, "indexInfos": [{"globalKey": "CPU占用率", "indexType": "UsedCPURate"}, {"globalKey": "IO等待", "indexType": "IOWait"}], "restype": "OMS"}, "measUnitKey": "I2K_OS", "name": "操作系统监控"}, {"template": {"object": {"format": "${ip}"}, "indexInfos": [{"globalKey": "每分钟数据量", "indexType": "one_min_count"}], "restype": "OMS"}, "measUnitKey": "I2K_Top_Task_Insert", "name": "性能任务数据量统计"}]}}}, {"request": {"method": "POST", "url": "/rest/i2000/pm/getMeasObjByMoType", "bodyPatterns": [{"equalToJson": {"measUnitKey": "I2K_OS"}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"mos": [{"originalValue": "ip=************", "displayValue": "************"}]}}}}, {"request": {"method": "POST", "url": "/rest/i2000/pm/getMeasObjByMoType", "bodyPatterns": [{"equalToJson": {"measUnitKey": "I2K_Top_Task_Insert"}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"mos": [{"originalValue": "mo_type=OMS,meas_unit_key=I2K_Task", "displayValue": "OMS,I2K_Task"}, {"originalValue": "mo_type=OMS,meas_unit_key=I2K_LOG", "displayValue": "OMS,I2K_LOG"}]}}}}, {"request": {"method": "GET", "url": "/rest/i2000/cloudsopadapter/pm/measobj/itpass?measUnitKey=I2K_OS&dns=%5BOS%3D1%5D"}, "response": {"status": 200, "jsonBody": [{"dn": "OS=1", "measObjects": [{"displayValue": "************", "keys": ["ip"], "values": ["************"], "titles": ["************"]}]}]}}, {"request": {"method": "GET", "url": "/rest/i2000/cloudsopadapter/pm/measobj/itpass?measUnitKey=I2K_Top_Task_Insert&dns=%5BOS%3D1%5D"}, "response": {"status": 200, "jsonBody": [{"dn": "OS=1", "measObjects": [{"displayValue": "OMS,I2K_CLUSTER", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_CLUSTER"], "titles": ["OMS", "I2K_CLUSTER"]}, {"displayValue": "OMS,I2K_Net", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Net"], "titles": ["OMS", "I2K_Net"]}, {"displayValue": "OMS,I2K_Task", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Task"], "titles": ["OMS", "I2K_Task"]}, {"displayValue": "OMS,I2K_Databus", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Databus"], "titles": ["OMS", "I2K_Databus"]}, {"displayValue": "OMS,I2K_CAPMGR", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_CAPMGR"], "titles": ["OMS", "I2K_CAPMGR"]}, {"displayValue": "OMS,I2K_LOG", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_LOG"], "titles": ["OMS", "I2K_LOG"]}, {"displayValue": "OMS,I2K_Event", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Event"], "titles": ["OMS", "I2K_Event"]}, {"displayValue": "OMS,I2K_Threshold", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Threshold"], "titles": ["OMS", "I2K_Threshold"]}, {"displayValue": "OMS,I2K_JVM", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_JVM"], "titles": ["OMS", "I2K_JVM"]}, {"displayValue": "OMS,I2K_FLOW", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_FLOW"], "titles": ["OMS", "I2K_FLOW"]}, {"displayValue": "OMS,I2K_OS", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_OS"], "titles": ["OMS", "I2K_OS"]}, {"displayValue": "OMS,I2K_HOSTCAP", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_HOSTCAP"], "titles": ["OMS", "I2K_HOSTCAP"]}, {"displayValue": "OMS,I2K_Top_Task_Insert", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Top_Task_Insert"], "titles": ["OMS", "I2K_Top_Task_Insert"]}, {"displayValue": "OMS,I2K_PMMGR", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_PMMGR"], "titles": ["OMS", "I2K_PMMGR"]}, {"displayValue": "OMS,I2K_Top_TS_Size", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Top_TS_Size"], "titles": ["OMS", "I2K_Top_TS_Size"]}, {"displayValue": "OMS,I2K_Report", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Report"], "titles": ["OMS", "I2K_Report"]}, {"displayValue": "OMS,I2K_FMMGR", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_FMMGR"], "titles": ["OMS", "I2K_FMMGR"]}, {"displayValue": "OMS,I2K_DB", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_DB"], "titles": ["OMS", "I2K_DB"]}, {"displayValue": "OMS,I2K_NBI", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_NBI"], "titles": ["OMS", "I2K_NBI"]}, {"displayValue": "OMS,I2K_Joblite", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Joblite"], "titles": ["OMS", "I2K_Joblite"]}, {"displayValue": "OMS,I2K_Avg_Aggr_Job", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Avg_Aggr_Job"], "titles": ["OMS", "I2K_Avg_Aggr_Job"]}]}]}}, {"request": {"method": "POST", "url": "/rest/i2000apimit/mob/list", "bodyPatterns": [{"equalToJson": ["OS=1"]}]}, "response": {"status": 200, "jsonBody": [{"@moClass": "com.huawei.oms.eam.mo.NetworkElement", "sequenceNo": 1, "dn": "OS=1", "fdn": "1.1", "typeID": 0, "type": "OMS", "version": "", "displayName": null, "name": "OSS", "vendor": "<PERSON><PERSON><PERSON>", "description": null, "owner": null, "createdTime": 1687828427231, "medNodeID": null, "alarmStatus": "Cleared", "clientProperties": {"TypeCode": "", "Code": ""}, "children": [], "parent": "/", "msId": null, "groupId": null, "tenantId": null, "adminStatus": "Unlocked", "availableStatus": "Normal", "usageStatus": "Active", "operationalStatus": "Enabled", "language": "zh_CN", "timeZone": "China Standard Time", "connectStatus": "NoDetect", "location": null, "maintainer": null, "contact": null, "deviceCode": null, "address": "", "code": "", "typeCode": "", "lifecycleStatus": "Unknown", "ipaddress": "", "hastatus": "Single"}]}}, {"request": {"method": "GET", "url": "/rest/dvpmservice/v1/service/catalog/poller/getbymotype?moType=OMS&version=*"}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": [{"taskId": "OMS~~OMS~~I2K_OS", "polledId": 51, "name": "操作系统监控", "period": 60, "measUnitKey": "I2K_OS", "resType": "OMS", "object": {"getter": "osGetter", "formatter": "", "format": "${ip}", "objectIndexs": [{"indexKey": "ip", "globalKey": "IP", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "UsedCPURate", "globalKey": "CPU占用率", "dataType": "FLOAT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "UsedMemoryRate", "globalKey": "内存使用率", "dataType": "FLOAT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "IOWait", "globalKey": "IO等待", "dataType": "FLOAT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "AllDiskSpace", "globalKey": "磁盘大小", "dataType": "FLOAT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "UsedDiskSpace", "globalKey": "磁盘使用大小", "dataType": "FLOAT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "UsedDiskRate", "globalKey": "磁盘使用率", "dataType": "FLOAT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_Report", "polledId": 250, "name": "消息报文采集监控", "period": 60, "measUnitKey": "I2K_Report", "resType": "OMS", "object": {"getter": "ipGetter", "formatter": "", "format": "${ip}", "objectIndexs": [{"indexKey": "ip", "globalKey": "IP", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "ingestion_report_num", "globalKey": "从Kafka消费消息数量", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_Top_TS_Size", "polledId": 258, "name": "性能数据表统计", "period": 3600, "measUnitKey": "I2K_Top_TS_Size", "resType": "OMS", "object": {"getter": "tableSizeGetter", "formatter": "", "format": "${table_space},${table_name}", "objectIndexs": [{"indexKey": "table_space", "globalKey": "表空间", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}, {"indexKey": "table_name", "globalKey": "表名", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "table_size", "globalKey": "表占用空间大小", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_Top_Task_Insert", "polledId": 318, "name": "性能任务数据量统计", "period": 300, "measUnitKey": "I2K_Top_Task_Insert", "resType": "OMS", "object": {"getter": "taskInsertGetter", "formatter": "", "format": "${mo_type},${meas_unit_key}", "objectIndexs": [{"indexKey": "mo_type", "globalKey": "网元类型", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}, {"indexKey": "meas_unit_key", "globalKey": "测量单元", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "one_min_count", "globalKey": "每分钟数据量", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_Threshold", "polledId": 332, "name": "阈值监控", "period": 60, "measUnitKey": "I2K_Threshold", "resType": "OMS", "object": {"getter": "ipGetter", "formatter": "", "format": "${ip}", "objectIndexs": [{"indexKey": "ip", "globalKey": "IP", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "threshold_index_count_num", "globalKey": "计算的指标总数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "threshold_index_count_fin_num", "globalKey": "计算完成的指标总数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "threshold_index_count_ignore_num", "globalKey": "丢弃计算的指标总数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_Task", "polledId": 489, "name": "任务指标监控", "period": 60, "measUnitKey": "I2K_Task", "resType": "OMS", "object": null, "indexInfos": [{"indexType": "task_tps_num", "globalKey": "提交到RHM的任务数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "task_polled_num", "globalKey": "从RHM拉取的任务总数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "task_total_num", "globalKey": "已下发任务数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "polled_measobject_num", "globalKey": "已拉取测量对象数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "handled_measobject_num", "globalKey": "已处理测量对象数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "accepted_task_num", "globalKey": "已接收任务数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "handled_task_num", "globalKey": "已处理任务数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "suc_task_num", "globalKey": "成功任务数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "retry_task_num", "globalKey": "重试任务数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "handled_retry_task_num", "globalKey": "已处理重试任务数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_CHANNEL", "polledId": 501, "name": "流式Channel统计监控", "period": 60, "measUnitKey": "I2K_CHANNEL", "resType": "OMS", "object": {"getter": "channelObjGetter", "formatter": "", "format": "${channelObj}", "objectIndexs": [{"indexKey": "channelObj", "globalKey": "采集通道", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "hwEventPutAttemptCount", "globalKey": "尝试放入Channel的事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "hwEventPutSuccessCount", "globalKey": "成功放入Channel的事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "hwChannelSize", "globalKey": "Channel队列当前事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "hwChannelCapacity", "globalKey": "Channel队列可保存最大事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "hwEventTakeSuccessCount", "globalKey": "从Channel成功获取的事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "hwEventTakeAttemptCount", "globalKey": "从Channel尝试获取事件的次数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "hwChannelFillPercentage", "globalKey": "Channel的充满率", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "hwEventPutSuccessPercentage", "globalKey": "往Channel放入事件的成功率", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_Databus", "polledId": 522, "name": "消息总线监控", "period": 60, "measUnitKey": "I2K_Databus", "resType": "OMS", "object": {"getter": "ipGetter", "formatter": "", "format": "${ip}", "objectIndexs": [{"indexKey": "ip", "globalKey": "IP", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "datapackage_num", "globalKey": "已处理的DataPackage数量", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "cap_max", "globalKey": "databus最大容量", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "cap_remaining", "globalKey": "databus剩余容量", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "databus_cost", "globalKey": "databus耗时", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "batch_insertion_cost", "globalKey": "数据批插耗时", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_Avg_Aggr_Job", "polledId": 526, "name": "汇聚任务平均执行时间监控", "period": 60, "measUnitKey": "I2K_Avg_Aggr_Job", "resType": "OMS", "object": {"getter": "ipGetter", "formatter": "", "format": "${ip}", "objectIndexs": [{"indexKey": "ip", "globalKey": "IP", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "customized_job_cost_1m", "globalKey": "1分钟周期网元降维汇聚任务处理时间", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "customized_job_cost_5m", "globalKey": "5分钟周期网元降维汇聚任务处理时间", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "customized_job_cost_15m", "globalKey": "15分钟周期网元降维汇聚任务处理时间", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "customized_job_cost_30m", "globalKey": "30分钟周期网元降维汇聚任务处理时间", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "customized_job_cost_1h", "globalKey": "1小时周期网元降维汇聚任务处理时间", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "periodic_job_cost_1h", "globalKey": "1小时周期时间降维汇聚任务处理时间", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "periodic_job_cost_1d", "globalKey": "1天周期时间降维汇聚任务处理时间", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_NBI", "polledId": 539, "name": "北向性能统计", "period": 60, "measUnitKey": "I2K_NBI", "resType": "OMS", "object": null, "indexInfos": [{"indexType": "receiveCnt", "globalKey": "接收总数", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "successCnt", "globalKey": "处理成功总数", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "failCnt", "globalKey": "处理失败总数", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "delay", "globalKey": "北向平均数据处理时长", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_FLOW", "polledId": 550, "name": "流式处理数据流量统计监控", "period": 60, "measUnitKey": "I2K_FLOW", "resType": "OMS", "object": {"getter": "flowObjGetter", "formatter": "", "format": "${flowObj}", "objectIndexs": [{"indexKey": "flowObj", "globalKey": "流式处理节点", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "StreamingFlowCount", "globalKey": "数据流量", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_FMMGR", "polledId": 713, "name": "告警监控", "period": 60, "measUnitKey": "I2K_FMMGR", "resType": "OMS", "object": null, "indexInfos": [{"indexType": "FMDataPush", "globalKey": "每分钟处理数据", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "FMDataInsert", "globalKey": "每分钟入库告警", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "FMDataUnhandled", "globalKey": "未处理告警量", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "FMAlarmDelay", "globalKey": "告警平均延时", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "FMAlarmDelayMax", "globalKey": "告警最大延时", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "FMAlarmDelayMin", "globalKey": "告警最小延时", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_CAPMGR", "polledId": 726, "name": "管理容量监控", "period": 60, "measUnitKey": "I2K_CAPMGR", "resType": "OMS", "object": null, "indexInfos": [{"indexType": "MaxUsersNum", "globalKey": "最大用户数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "AllUsersNum", "globalKey": "当前用户数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "MaxOnlineUsersNum", "globalKey": "最大在线用户数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "OnlineUsersNum", "globalKey": "在线用户数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "ApplicationMONum", "globalKey": "业务网元数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "PhysicalMONum", "globalKey": "物理网元数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "CollectTasksNum", "globalKey": "性能采集任务数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "EquivalentMONum", "globalKey": "剩余可管理等效网元数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_PMMGR", "polledId": 783, "name": "性能模块吞吐量", "period": 60, "measUnitKey": "I2K_PMMGR", "resType": "OMS", "object": null, "indexInfos": [{"indexType": "PMDataPush", "globalKey": "性能接收总数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "PMDataInsert", "globalKey": "性能入库总数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "PMDataUnhandled", "globalKey": "性能未处理数据量", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "UnitPeriodPmDelay", "globalKey": "性能数据处理时延", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_HOSTCAP", "polledId": 834, "name": "主机量监控", "period": 60, "measUnitKey": "I2K_HOSTCAP", "resType": "OMS", "object": null, "indexInfos": [{"indexType": "cloudVM", "globalKey": "云虚拟机网元数量", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "physicalVM", "globalKey": "云物理机网元数量", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "docker", "globalKey": "容器网元数量", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_LOG", "polledId": 845, "name": "日志容量监控", "period": 60, "measUnitKey": "I2K_LOG", "resType": "OMS", "object": null, "indexInfos": [{"indexType": "SYSLogCount", "globalKey": "系统日志容量", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "OPLogCount", "globalKey": "操作日志容量", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "SECLogCount", "globalKey": "安全日志容量", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_Net", "polledId": 901, "name": "网络监控", "period": 60, "measUnitKey": "I2K_Net", "resType": "OMS", "object": {"getter": "netGetter", "formatter": "", "format": "${ip},${ifDesc}", "objectIndexs": [{"indexKey": "ip", "globalKey": "IP", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}, {"indexKey": "ifDesc", "globalKey": "网卡", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "NetWorkIn", "globalKey": "网络接收量", "dataType": "FLOAT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "NetWorkOut", "globalKey": "网络发送量", "dataType": "FLOAT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_CLUSTER", "polledId": 955, "name": "流式处理总体流量汇聚", "period": 60, "measUnitKey": "I2K_CLUSTER", "resType": "OMS", "object": {"getter": "clusterObjGetter", "formatter": "", "format": "${clusterObj}", "objectIndexs": [{"indexKey": "clusterObj", "globalKey": "流式处理集群", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "hwStreamingClusterFlowStat", "globalKey": "流式处理总流量", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_Event", "polledId": 975, "name": "事件处理指标统计", "period": 60, "measUnitKey": "I2K_Event", "resType": "OMS", "object": {"getter": "ipGetter", "formatter": "", "format": "${ip}", "objectIndexs": [{"indexKey": "ip", "globalKey": "IP", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "accept_add_evt_num", "globalKey": "接收的网元新增事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "accept_rel_add_evt_num", "globalKey": "接收的网元关系新增事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "accept_del_evt_num", "globalKey": "接收的网元删除事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "accept_truncate_evt_num", "globalKey": "接收的网元硬删除事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "accept_admin_stat_shutdown_evt_num", "globalKey": "接收的网元管理状态变为shutdown事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "accept_admin_stat_resume_evt_num", "globalKey": "接收的网元管理状态变为resume事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "accept_con_chg_evt_num", "globalKey": "接收的网元连接状态变更事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "accept_attr_chg_evt_num", "globalKey": "接收的网元属性变更事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "accept_account_attr_chg_evt_num", "globalKey": "接收的账号属性变更事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "handled_add_evt_num", "globalKey": "已处理的网元新增事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "handled_rel_add_evt_num", "globalKey": "已处理的网元关系新增事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "handled_del_evt_num", "globalKey": "已处理的网元删除事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "handled_truncate_evt_num", "globalKey": "已处理的网元硬删除事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "handled_admin_stat_shutdown_evt_num", "globalKey": "已处理的网元管理状态变为shutdown事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "handled_admin_stat_resume_evt_num", "globalKey": "已处理的网元管理状态变为resume事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "handled_con_chg_evt_num", "globalKey": "已处理的网元连接状态变更事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "handled_attr_chg_evt_num", "globalKey": "已处理的网元属性变更事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "handled_account_attr_chg_evt_num", "globalKey": "已处理的账号属性变更事件数", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_DB", "polledId": 992, "name": "数据库监控", "period": 60, "measUnitKey": "I2K_DB", "resType": "OMS", "object": {"getter": "tableSpaceGetter", "formatter": "", "format": "${tablespace}", "objectIndexs": [{"indexKey": "tablespace", "globalKey": "表空间名", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "tablespaceSizeAllocated", "globalKey": "分配表空间大小", "dataType": "FLOAT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "tablespaceSizeUsed", "globalKey": "使用表空间大小", "dataType": "FLOAT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "tablespaceUsedRate", "globalKey": "表空间使用率", "dataType": "FLOAT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_JVM", "polledId": 1058, "name": "JVM内存监控", "period": 60, "measUnitKey": "I2K_JVM", "resType": "OMS", "object": {"getter": "osGetter", "formatter": "", "format": "${ip}", "objectIndexs": [{"indexKey": "ip", "globalKey": "IP", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "edenMax", "globalKey": "新生代总内存", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "edenUsage", "globalKey": "新生代使用内存", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "oldMax", "globalKey": "老生代总内存", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "oldUsage", "globalKey": "老生代使用内存", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "fgcCount", "globalKey": "FGC次数", "dataType": "LONG", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}, {"taskId": "OMS~~OMS~~I2K_Joblite", "polledId": 1119, "name": "joblite监控", "period": 60, "measUnitKey": "I2K_Joblite", "resType": "OMS", "object": {"getter": "ipGetter", "formatter": "", "format": "${ip}", "objectIndexs": [{"indexKey": "ip", "globalKey": "IP", "column": "1", "dataType": "STRING", "index": true, "id": true, "objType": null}]}, "indexInfos": [{"indexType": "joblite_send_msg_num", "globalKey": "JobLite发送任务数量", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}, {"indexType": "joblite_receive_msg_num", "globalKey": "JobLite接收任务数量", "dataType": "INT", "collectTypeColumn": "1", "collectTypeMax": "0", "collectTypeMin": "**********"}]}], "resultDesc": null}}}, {"request": {"method": "GET", "url": "/rest/dvpmservice/v1/service/collect/measobjects/query?moType=OMS&measUnitKey=I2K_OS"}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": [{"displayValue": "************", "addedManually": false, "measIndexInfos": [{"indexKey": "ip", "indexKeyName": "IP", "indexValue": "************", "indexValueName": "************"}]}], "resultDesc": null}}}, {"request": {"method": "POST", "url": "/rest/dvpmservice/v1/service/catalog/i18n", "bodyPatterns": [{"equalToJson": {"languageType": "zn", "resKeyList": ["OMS.I2K_OS.UsedCPURate.unit"]}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"OMS.I2K_OS.UsedCPURate.unit": "%"}, "resultDesc": null}}}, {"request": {"method": "POST", "url": "/rest/i2000/pm/languageDisplayValue", "bodyPatterns": [{"equalToJson": {"languageType": "zn", "resKeyList": ["OMS.I2K_OS.UsedCPURate.unit"]}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"OMS.I2K_OS.UsedCPURate.unit": "%"}, "resultDesc": null}}}, {"request": {"method": "GET", "url": "/rest/dvpmservice/v1/service/collect/openapi/measobjects?measUnitKey=I2K_OS&dns=%5BOS%3D1%5D"}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": [{"dn": "OS=1", "measObjects": [{"displayValue": "************", "keys": ["ip"], "values": ["************"], "titles": ["************"]}]}], "resultDesc": null}}}, {"request": {"method": "GET", "url": "/rest/dvpmservice/v1/service/collect/measobjects/query?moType=OMS&measUnitKey=I2K_Top_Task_Insert"}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": [{"displayValue": "OMS,I2K_Task", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_Task", "indexValueName": "I2K_Task"}]}, {"displayValue": "OMS,I2K_Report", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_Report", "indexValueName": "I2K_Report"}]}, {"displayValue": "OMS,I2K_Top_Task_Insert", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_Top_Task_Insert", "indexValueName": "I2K_Top_Task_Insert"}]}, {"displayValue": "OMS,I2K_LOG", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_LOG", "indexValueName": "I2K_LOG"}]}, {"displayValue": "OMS,I2K_Avg_Aggr_Job", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_Avg_Aggr_Job", "indexValueName": "I2K_Avg_Aggr_Job"}]}, {"displayValue": "OMS,I2K_Threshold", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_Threshold", "indexValueName": "I2K_Threshold"}]}, {"displayValue": "OMS,I2K_CLUSTER", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_CLUSTER", "indexValueName": "I2K_CLUSTER"}]}, {"displayValue": "OMS,I2K_OS", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_OS", "indexValueName": "I2K_OS"}]}, {"displayValue": "OMS,I2K_FLOW", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_FLOW", "indexValueName": "I2K_FLOW"}]}, {"displayValue": "OMS,I2K_FMMGR", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_FMMGR", "indexValueName": "I2K_FMMGR"}]}, {"displayValue": "OMS,I2K_Event", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_Event", "indexValueName": "I2K_Event"}]}, {"displayValue": "OMS,I2K_HOSTCAP", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_HOSTCAP", "indexValueName": "I2K_HOSTCAP"}]}, {"displayValue": "OMS,I2K_NBI", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_NBI", "indexValueName": "I2K_NBI"}]}, {"displayValue": "OMS,I2K_JVM", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_JVM", "indexValueName": "I2K_JVM"}]}, {"displayValue": "OMS,I2K_DB", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_DB", "indexValueName": "I2K_DB"}]}, {"displayValue": "OMS,I2K_Databus", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_Databus", "indexValueName": "I2K_Databus"}]}, {"displayValue": "OMS,I2K_PMMGR", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_PMMGR", "indexValueName": "I2K_PMMGR"}]}, {"displayValue": "OMS,I2K_CAPMGR", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_CAPMGR", "indexValueName": "I2K_CAPMGR"}]}, {"displayValue": "OMS,I2K_Top_TS_Size", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_Top_TS_Size", "indexValueName": "I2K_Top_TS_Size"}]}, {"displayValue": "OMS,I2K_Net", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_Net", "indexValueName": "I2K_Net"}]}, {"displayValue": "OMS,I2K_Joblite", "addedManually": false, "measIndexInfos": [{"indexKey": "mo_type", "indexKeyName": "网元类型", "indexValue": "OMS", "indexValueName": "OMS"}, {"indexKey": "meas_unit_key", "indexKeyName": "测量单元", "indexValue": "I2K_Joblite", "indexValueName": "I2K_Joblite"}]}], "resultDesc": null}}}, {"request": {"method": "POST", "url": "/rest/dvpmservice/v1/service/catalog/i18n", "bodyPatterns": [{"equalToJson": {"languageType": "zn", "resKeyList": ["OMS.I2K_Top_Task_Insert.one_min_count.unit", "OMS.I2K_Top_Task_Insert.one_min_count.unit"]}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"OMS.I2K_Top_Task_Insert.one_min_count.unit": "条"}, "resultDesc": null}}}, {"request": {"method": "POST", "url": "/rest/i2000/pm/languageDisplayValue", "bodyPatterns": [{"equalToJson": {"languageType": "zn", "resKeyList": ["OMS.I2K_Top_Task_Insert.one_min_count.unit", "OMS.I2K_Top_Task_Insert.one_min_count.unit"]}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"OMS.I2K_Top_Task_Insert.one_min_count.unit": "条"}, "resultDesc": null}}}, {"request": {"method": "GET", "url": "/rest/dvpmservice/v1/service/collect/openapi/measobjects?measUnitKey=I2K_Top_Task_Insert&dns=%5BOS%3D1%5D"}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": [{"dn": "OS=1", "measObjects": [{"displayValue": "OMS,I2K_CLUSTER", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_CLUSTER"], "titles": ["OMS", "I2K_CLUSTER"]}, {"displayValue": "OMS,I2K_Net", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Net"], "titles": ["OMS", "I2K_Net"]}, {"displayValue": "OMS,I2K_Task", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Task"], "titles": ["OMS", "I2K_Task"]}, {"displayValue": "OMS,I2K_Databus", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Databus"], "titles": ["OMS", "I2K_Databus"]}, {"displayValue": "OMS,I2K_CAPMGR", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_CAPMGR"], "titles": ["OMS", "I2K_CAPMGR"]}, {"displayValue": "OMS,I2K_LOG", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_LOG"], "titles": ["OMS", "I2K_LOG"]}, {"displayValue": "OMS,I2K_Event", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Event"], "titles": ["OMS", "I2K_Event"]}, {"displayValue": "OMS,I2K_Threshold", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Threshold"], "titles": ["OMS", "I2K_Threshold"]}, {"displayValue": "OMS,I2K_JVM", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_JVM"], "titles": ["OMS", "I2K_JVM"]}, {"displayValue": "OMS,I2K_FLOW", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_FLOW"], "titles": ["OMS", "I2K_FLOW"]}, {"displayValue": "OMS,I2K_OS", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_OS"], "titles": ["OMS", "I2K_OS"]}, {"displayValue": "OMS,I2K_HOSTCAP", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_HOSTCAP"], "titles": ["OMS", "I2K_HOSTCAP"]}, {"displayValue": "OMS,I2K_Top_Task_Insert", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Top_Task_Insert"], "titles": ["OMS", "I2K_Top_Task_Insert"]}, {"displayValue": "OMS,I2K_PMMGR", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_PMMGR"], "titles": ["OMS", "I2K_PMMGR"]}, {"displayValue": "OMS,I2K_Top_TS_Size", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Top_TS_Size"], "titles": ["OMS", "I2K_Top_TS_Size"]}, {"displayValue": "OMS,I2K_Report", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Report"], "titles": ["OMS", "I2K_Report"]}, {"displayValue": "OMS,I2K_FMMGR", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_FMMGR"], "titles": ["OMS", "I2K_FMMGR"]}, {"displayValue": "OMS,I2K_DB", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_DB"], "titles": ["OMS", "I2K_DB"]}, {"displayValue": "OMS,I2K_NBI", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_NBI"], "titles": ["OMS", "I2K_NBI"]}, {"displayValue": "OMS,I2K_Joblite", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Joblite"], "titles": ["OMS", "I2K_Joblite"]}, {"displayValue": "OMS,I2K_Avg_Aggr_Job", "keys": ["mo_type", "meas_unit_key"], "values": ["OMS", "I2K_Avg_Aggr_Job"], "titles": ["OMS", "I2K_Avg_Aggr_Job"]}]}], "resultDesc": null}}}, {"request": {"method": "GET", "url": "/rest/dvpmservice/v1/service/collect/taskmgr/periods?moType=OMS&measUnitKey=I2K_Top_Task_Insert"}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"defaultPeriod": 900, "period": 300, "supportPeriod": [60, 300, 900, 1800, 3600, 86400]}, "resultDesc": null}}}, {"request": {"method": "POST", "url": "/rest/dvpmservice/v1/service/monitor/history/query-data", "bodyPatterns": [{"equalToJson": {"beginTime": 1685683637267, "endTime": 1687831120914, "historyTimeRange": 0, "isGetAggrData": false, "isGetGraphicGroupData": false, "isQueryOriginal": true, "measUnitKey": "I2K_Top_Task_Insert", "mo2Index": "[{\"displayValue\":\"OMS,I2K_Task\",\"dn\":\"OS=1\",\"indexName\":\"one_min_count\"},{\"displayValue\":\"OMS,I2K_LOG\",\"dn\":\"OS=1\",\"indexName\":\"one_min_count\"}]", "moType": "OMS", "pageIndex": 1, "period": 300, "pmViewPage": "historyPm", "unitedId": "flow-diagram_N2"}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"totalPage": 1, "resultData": [{"dn": "OS=1", "neName": null, "displayValue": "OMS,I2K_Task", "indexId": 6431, "indexName": "每分钟数据量", "indexUnit": "条", "oper": null, "thresholdList": [], "indexValues": [{"timestamp": 1687829100000, "timestampStr": "2023-06-27 09:25", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687829400000, "timestampStr": "2023-06-27 09:30", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687829700000, "timestampStr": "2023-06-27 09:35", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687830000000, "timestampStr": "2023-06-27 09:40", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687830300000, "timestampStr": "2023-06-27 09:45", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687830600000, "timestampStr": "2023-06-27 09:50", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687830900000, "timestampStr": "2023-06-27 09:55", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687831200000, "timestampStr": "2023-06-27 10:00", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687831500000, "timestampStr": "2023-06-27 10:05", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687831800000, "timestampStr": "2023-06-27 10:10", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687832100000, "timestampStr": "2023-06-27 10:15", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687832400000, "timestampStr": "2023-06-27 10:20", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687832700000, "timestampStr": "2023-06-27 10:25", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687833000000, "timestampStr": "2023-06-27 10:30", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687833300000, "timestampStr": "2023-06-27 10:35", "indexValue": "1", "breakpoint": false}], "resourceType": null, "indexGroup": "I2K_Top_Task_Insert", "displayRate": 0, "indexKey": "one_min_count", "successRate": 0.0}, {"dn": "OS=1", "neName": null, "displayValue": "OMS,I2K_LOG", "indexId": 6431, "indexName": "每分钟数据量", "indexUnit": "条", "oper": null, "thresholdList": [], "indexValues": [{"timestamp": 1687829100000, "timestampStr": "2023-06-27 09:25", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687829400000, "timestampStr": "2023-06-27 09:30", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687829700000, "timestampStr": "2023-06-27 09:35", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687830000000, "timestampStr": "2023-06-27 09:40", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687830300000, "timestampStr": "2023-06-27 09:45", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687830600000, "timestampStr": "2023-06-27 09:50", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687830900000, "timestampStr": "2023-06-27 09:55", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687831200000, "timestampStr": "2023-06-27 10:00", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687831500000, "timestampStr": "2023-06-27 10:05", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687831800000, "timestampStr": "2023-06-27 10:10", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687832100000, "timestampStr": "2023-06-27 10:15", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687832400000, "timestampStr": "2023-06-27 10:20", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687832700000, "timestampStr": "2023-06-27 10:25", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687833000000, "timestampStr": "2023-06-27 10:30", "indexValue": "1", "breakpoint": false}, {"timestamp": 1687833300000, "timestampStr": "2023-06-27 10:35", "indexValue": "1", "breakpoint": false}], "resourceType": null, "indexGroup": "I2K_Top_Task_Insert", "displayRate": 0, "indexKey": "one_min_count", "successRate": 0.0}], "groupQueryData": null, "startTime": 0, "endTime": 0, "queryTime": 1687833544188, "timeDisplayInfos": null, "yAxisScaleCount": 0}, "resultDesc": null}}}]}