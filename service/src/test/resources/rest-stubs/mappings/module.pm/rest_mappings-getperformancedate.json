{"mappings": [{"request": {"method": "POST", "url": "/rest/pm/bigscreen/historydata", "bodyPatterns": [{"equalToJson": {"beginTime": "${json-unit.any-number}", "endTime": "${json-unit.any-number}", "historyTimeRange": 0, "isGetAggrData": false, "isGetGraphicGroupData": false, "isQueryOriginal": true, "measUnitKey": "I2K_OS", "mo2Index": "[{\"displayValue\":\"10.247.5.114\",\"dn\":\"OS=1\",\"indexName\":\"IOWait\"}]", "moType": "OMS", "pageIndex": 1, "period": "${json-unit.any-number}", "pmViewPage": "historyPm"}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"totalPage": 1, "resultData": [{"dn": "OS=1", "neName": "OSS", "displayValue": "10.247.5.114", "indexId": 10, "indexName": "IO等待", "indexUnit": "%", "oper": "", "thresholdList": [-1.0, -1.0, -1.0, -1.0], "indexValues": [{"timestamp": 1633997640000, "timestampStr": "2021-10-12 08:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997700000, "timestampStr": "2021-10-12 08:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997760000, "timestampStr": "2021-10-12 08:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997820000, "timestampStr": "2021-10-12 08:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997880000, "timestampStr": "2021-10-12 08:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997940000, "timestampStr": "2021-10-12 08:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998000000, "timestampStr": "2021-10-12 08:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998060000, "timestampStr": "2021-10-12 08:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998120000, "timestampStr": "2021-10-12 08:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998180000, "timestampStr": "2021-10-12 08:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998240000, "timestampStr": "2021-10-12 08:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998300000, "timestampStr": "2021-10-12 08:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998360000, "timestampStr": "2021-10-12 08:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998420000, "timestampStr": "2021-10-12 08:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998480000, "timestampStr": "2021-10-12 08:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998540000, "timestampStr": "2021-10-12 08:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998600000, "timestampStr": "2021-10-12 08:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998660000, "timestampStr": "2021-10-12 08:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998720000, "timestampStr": "2021-10-12 08:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998780000, "timestampStr": "2021-10-12 08:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998840000, "timestampStr": "2021-10-12 08:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998900000, "timestampStr": "2021-10-12 08:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998960000, "timestampStr": "2021-10-12 08:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999020000, "timestampStr": "2021-10-12 08:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999080000, "timestampStr": "2021-10-12 08:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999140000, "timestampStr": "2021-10-12 08:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999200000, "timestampStr": "2021-10-12 08:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999260000, "timestampStr": "2021-10-12 08:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999320000, "timestampStr": "2021-10-12 08:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999380000, "timestampStr": "2021-10-12 08:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999440000, "timestampStr": "2021-10-12 08:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999500000, "timestampStr": "2021-10-12 08:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999560000, "timestampStr": "2021-10-12 08:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999620000, "timestampStr": "2021-10-12 08:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999680000, "timestampStr": "2021-10-12 08:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999740000, "timestampStr": "2021-10-12 08:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999800000, "timestampStr": "2021-10-12 08:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999860000, "timestampStr": "2021-10-12 08:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999920000, "timestampStr": "2021-10-12 08:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999980000, "timestampStr": "2021-10-12 08:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000040000, "timestampStr": "2021-10-12 08:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000100000, "timestampStr": "2021-10-12 08:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000160000, "timestampStr": "2021-10-12 08:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000220000, "timestampStr": "2021-10-12 08:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000280000, "timestampStr": "2021-10-12 08:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000340000, "timestampStr": "2021-10-12 08:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000400000, "timestampStr": "2021-10-12 09:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000460000, "timestampStr": "2021-10-12 09:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000520000, "timestampStr": "2021-10-12 09:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000580000, "timestampStr": "2021-10-12 09:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000640000, "timestampStr": "2021-10-12 09:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000700000, "timestampStr": "2021-10-12 09:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000760000, "timestampStr": "2021-10-12 09:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000820000, "timestampStr": "2021-10-12 09:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000880000, "timestampStr": "2021-10-12 09:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000940000, "timestampStr": "2021-10-12 09:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001000000, "timestampStr": "2021-10-12 09:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001060000, "timestampStr": "2021-10-12 09:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001120000, "timestampStr": "2021-10-12 09:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001180000, "timestampStr": "2021-10-12 09:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001240000, "timestampStr": "2021-10-12 09:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001300000, "timestampStr": "2021-10-12 09:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001360000, "timestampStr": "2021-10-12 09:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001420000, "timestampStr": "2021-10-12 09:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001480000, "timestampStr": "2021-10-12 09:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001540000, "timestampStr": "2021-10-12 09:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001600000, "timestampStr": "2021-10-12 09:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001660000, "timestampStr": "2021-10-12 09:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001720000, "timestampStr": "2021-10-12 09:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001780000, "timestampStr": "2021-10-12 09:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001840000, "timestampStr": "2021-10-12 09:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001900000, "timestampStr": "2021-10-12 09:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001960000, "timestampStr": "2021-10-12 09:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002020000, "timestampStr": "2021-10-12 09:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002080000, "timestampStr": "2021-10-12 09:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002140000, "timestampStr": "2021-10-12 09:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002200000, "timestampStr": "2021-10-12 09:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002260000, "timestampStr": "2021-10-12 09:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002320000, "timestampStr": "2021-10-12 09:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002380000, "timestampStr": "2021-10-12 09:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002440000, "timestampStr": "2021-10-12 09:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002500000, "timestampStr": "2021-10-12 09:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002560000, "timestampStr": "2021-10-12 09:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002620000, "timestampStr": "2021-10-12 09:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002680000, "timestampStr": "2021-10-12 09:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002740000, "timestampStr": "2021-10-12 09:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002800000, "timestampStr": "2021-10-12 09:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002860000, "timestampStr": "2021-10-12 09:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002920000, "timestampStr": "2021-10-12 09:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002980000, "timestampStr": "2021-10-12 09:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003040000, "timestampStr": "2021-10-12 09:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003100000, "timestampStr": "2021-10-12 09:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003160000, "timestampStr": "2021-10-12 09:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003220000, "timestampStr": "2021-10-12 09:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003280000, "timestampStr": "2021-10-12 09:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003340000, "timestampStr": "2021-10-12 09:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003400000, "timestampStr": "2021-10-12 09:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003460000, "timestampStr": "2021-10-12 09:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003520000, "timestampStr": "2021-10-12 09:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003580000, "timestampStr": "2021-10-12 09:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003640000, "timestampStr": "2021-10-12 09:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003700000, "timestampStr": "2021-10-12 09:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003760000, "timestampStr": "2021-10-12 09:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003820000, "timestampStr": "2021-10-12 09:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003880000, "timestampStr": "2021-10-12 09:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003940000, "timestampStr": "2021-10-12 09:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004000000, "timestampStr": "2021-10-12 10:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004060000, "timestampStr": "2021-10-12 10:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004120000, "timestampStr": "2021-10-12 10:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004180000, "timestampStr": "2021-10-12 10:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004240000, "timestampStr": "2021-10-12 10:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004300000, "timestampStr": "2021-10-12 10:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004360000, "timestampStr": "2021-10-12 10:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004420000, "timestampStr": "2021-10-12 10:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004480000, "timestampStr": "2021-10-12 10:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004540000, "timestampStr": "2021-10-12 10:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004600000, "timestampStr": "2021-10-12 10:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004660000, "timestampStr": "2021-10-12 10:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004720000, "timestampStr": "2021-10-12 10:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004780000, "timestampStr": "2021-10-12 10:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004840000, "timestampStr": "2021-10-12 10:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004900000, "timestampStr": "2021-10-12 10:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004960000, "timestampStr": "2021-10-12 10:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005020000, "timestampStr": "2021-10-12 10:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005080000, "timestampStr": "2021-10-12 10:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005140000, "timestampStr": "2021-10-12 10:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005200000, "timestampStr": "2021-10-12 10:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005260000, "timestampStr": "2021-10-12 10:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005320000, "timestampStr": "2021-10-12 10:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005380000, "timestampStr": "2021-10-12 10:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005440000, "timestampStr": "2021-10-12 10:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005500000, "timestampStr": "2021-10-12 10:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005560000, "timestampStr": "2021-10-12 10:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005620000, "timestampStr": "2021-10-12 10:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005680000, "timestampStr": "2021-10-12 10:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005740000, "timestampStr": "2021-10-12 10:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005800000, "timestampStr": "2021-10-12 10:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005860000, "timestampStr": "2021-10-12 10:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005920000, "timestampStr": "2021-10-12 10:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005980000, "timestampStr": "2021-10-12 10:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006040000, "timestampStr": "2021-10-12 10:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006100000, "timestampStr": "2021-10-12 10:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006160000, "timestampStr": "2021-10-12 10:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006220000, "timestampStr": "2021-10-12 10:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006280000, "timestampStr": "2021-10-12 10:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006340000, "timestampStr": "2021-10-12 10:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006400000, "timestampStr": "2021-10-12 10:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006460000, "timestampStr": "2021-10-12 10:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006520000, "timestampStr": "2021-10-12 10:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006580000, "timestampStr": "2021-10-12 10:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006640000, "timestampStr": "2021-10-12 10:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006700000, "timestampStr": "2021-10-12 10:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006760000, "timestampStr": "2021-10-12 10:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006820000, "timestampStr": "2021-10-12 10:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006880000, "timestampStr": "2021-10-12 10:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006940000, "timestampStr": "2021-10-12 10:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007000000, "timestampStr": "2021-10-12 10:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007060000, "timestampStr": "2021-10-12 10:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007120000, "timestampStr": "2021-10-12 10:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007180000, "timestampStr": "2021-10-12 10:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007240000, "timestampStr": "2021-10-12 10:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007300000, "timestampStr": "2021-10-12 10:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007360000, "timestampStr": "2021-10-12 10:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007420000, "timestampStr": "2021-10-12 10:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007480000, "timestampStr": "2021-10-12 10:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007540000, "timestampStr": "2021-10-12 10:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007600000, "timestampStr": "2021-10-12 11:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007660000, "timestampStr": "2021-10-12 11:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007720000, "timestampStr": "2021-10-12 11:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007780000, "timestampStr": "2021-10-12 11:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007840000, "timestampStr": "2021-10-12 11:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007900000, "timestampStr": "2021-10-12 11:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007960000, "timestampStr": "2021-10-12 11:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008020000, "timestampStr": "2021-10-12 11:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008080000, "timestampStr": "2021-10-12 11:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008140000, "timestampStr": "2021-10-12 11:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008200000, "timestampStr": "2021-10-12 11:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008260000, "timestampStr": "2021-10-12 11:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008320000, "timestampStr": "2021-10-12 11:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008380000, "timestampStr": "2021-10-12 11:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008440000, "timestampStr": "2021-10-12 11:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008500000, "timestampStr": "2021-10-12 11:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008560000, "timestampStr": "2021-10-12 11:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008620000, "timestampStr": "2021-10-12 11:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008680000, "timestampStr": "2021-10-12 11:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008740000, "timestampStr": "2021-10-12 11:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008800000, "timestampStr": "2021-10-12 11:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008860000, "timestampStr": "2021-10-12 11:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008920000, "timestampStr": "2021-10-12 11:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008980000, "timestampStr": "2021-10-12 11:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009040000, "timestampStr": "2021-10-12 11:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009100000, "timestampStr": "2021-10-12 11:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009160000, "timestampStr": "2021-10-12 11:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009220000, "timestampStr": "2021-10-12 11:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009280000, "timestampStr": "2021-10-12 11:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009340000, "timestampStr": "2021-10-12 11:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009400000, "timestampStr": "2021-10-12 11:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009460000, "timestampStr": "2021-10-12 11:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009520000, "timestampStr": "2021-10-12 11:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009580000, "timestampStr": "2021-10-12 11:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009640000, "timestampStr": "2021-10-12 11:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009700000, "timestampStr": "2021-10-12 11:35:00", "indexValue": "0.07", "breakpoint": false}], "resourceType": "OMS", "indexGroup": "I2K_OS", "displayRate": 0, "indexKey": "IOWait", "successRate": 28.06}], "groupQueryData": null, "startTime": 1633966514920, "endTime": 1634009714920, "timeDisplayInfos": [{"timestamp": 1633968000000, "timestampStr": "10-12 00:00"}, {"timestamp": 1633975200000, "timestampStr": "10-12 02:00"}, {"timestamp": 1633982400000, "timestampStr": "10-12 04:00"}, {"timestamp": 1633989600000, "timestampStr": "10-12 06:00"}, {"timestamp": 1633996800000, "timestampStr": "10-12 08:00"}, {"timestamp": 1634004000000, "timestampStr": "10-12 10:00"}], "yAxisScaleCount": 7}, "resultDesc": null}}}, {"request": {"method": "POST", "url": "/rest/pm/bigscreen/historydata", "bodyPatterns": [{"equalToJson": {"beginTime": "${json-unit.any-number}", "endTime": "${json-unit.any-number}", "historyTimeRange": 0, "isGetAggrData": false, "isGetGraphicGroupData": false, "isQueryOriginal": true, "measUnitKey": "I2K_FMMGR", "mo2Index": "[{\"dn\":\"OS=1\",\"indexName\":\"FMAlarmDelay\"}]", "moType": "OMS", "pageIndex": 1, "period": "${json-unit.any-number}", "pmViewPage": "historyPm"}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"totalPage": 1, "resultData": [{"dn": "OS=1", "neName": "OSS", "displayValue": "FMAlarmDelay", "indexId": 10, "indexName": "告警平均延时(毫秒)", "indexUnit": "%", "oper": "", "thresholdList": [-1.0, -1.0, -1.0, -1.0], "indexValues": [{"timestamp": 1633997640000, "timestampStr": "2021-10-12 08:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997700000, "timestampStr": "2021-10-12 08:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997760000, "timestampStr": "2021-10-12 08:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997820000, "timestampStr": "2021-10-12 08:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997880000, "timestampStr": "2021-10-12 08:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997940000, "timestampStr": "2021-10-12 08:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998000000, "timestampStr": "2021-10-12 08:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998060000, "timestampStr": "2021-10-12 08:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998120000, "timestampStr": "2021-10-12 08:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998180000, "timestampStr": "2021-10-12 08:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998240000, "timestampStr": "2021-10-12 08:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998300000, "timestampStr": "2021-10-12 08:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998360000, "timestampStr": "2021-10-12 08:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998420000, "timestampStr": "2021-10-12 08:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998480000, "timestampStr": "2021-10-12 08:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998540000, "timestampStr": "2021-10-12 08:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998600000, "timestampStr": "2021-10-12 08:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998660000, "timestampStr": "2021-10-12 08:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998720000, "timestampStr": "2021-10-12 08:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998780000, "timestampStr": "2021-10-12 08:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998840000, "timestampStr": "2021-10-12 08:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998900000, "timestampStr": "2021-10-12 08:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998960000, "timestampStr": "2021-10-12 08:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999020000, "timestampStr": "2021-10-12 08:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999080000, "timestampStr": "2021-10-12 08:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999140000, "timestampStr": "2021-10-12 08:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999200000, "timestampStr": "2021-10-12 08:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999260000, "timestampStr": "2021-10-12 08:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999320000, "timestampStr": "2021-10-12 08:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999380000, "timestampStr": "2021-10-12 08:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999440000, "timestampStr": "2021-10-12 08:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999500000, "timestampStr": "2021-10-12 08:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999560000, "timestampStr": "2021-10-12 08:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999620000, "timestampStr": "2021-10-12 08:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999680000, "timestampStr": "2021-10-12 08:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999740000, "timestampStr": "2021-10-12 08:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999800000, "timestampStr": "2021-10-12 08:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999860000, "timestampStr": "2021-10-12 08:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999920000, "timestampStr": "2021-10-12 08:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999980000, "timestampStr": "2021-10-12 08:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000040000, "timestampStr": "2021-10-12 08:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000100000, "timestampStr": "2021-10-12 08:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000160000, "timestampStr": "2021-10-12 08:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000220000, "timestampStr": "2021-10-12 08:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000280000, "timestampStr": "2021-10-12 08:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000340000, "timestampStr": "2021-10-12 08:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000400000, "timestampStr": "2021-10-12 09:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000460000, "timestampStr": "2021-10-12 09:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000520000, "timestampStr": "2021-10-12 09:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000580000, "timestampStr": "2021-10-12 09:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000640000, "timestampStr": "2021-10-12 09:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000700000, "timestampStr": "2021-10-12 09:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000760000, "timestampStr": "2021-10-12 09:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000820000, "timestampStr": "2021-10-12 09:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000880000, "timestampStr": "2021-10-12 09:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000940000, "timestampStr": "2021-10-12 09:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001000000, "timestampStr": "2021-10-12 09:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001060000, "timestampStr": "2021-10-12 09:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001120000, "timestampStr": "2021-10-12 09:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001180000, "timestampStr": "2021-10-12 09:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001240000, "timestampStr": "2021-10-12 09:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001300000, "timestampStr": "2021-10-12 09:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001360000, "timestampStr": "2021-10-12 09:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001420000, "timestampStr": "2021-10-12 09:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001480000, "timestampStr": "2021-10-12 09:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001540000, "timestampStr": "2021-10-12 09:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001600000, "timestampStr": "2021-10-12 09:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001660000, "timestampStr": "2021-10-12 09:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001720000, "timestampStr": "2021-10-12 09:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001780000, "timestampStr": "2021-10-12 09:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001840000, "timestampStr": "2021-10-12 09:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001900000, "timestampStr": "2021-10-12 09:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001960000, "timestampStr": "2021-10-12 09:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002020000, "timestampStr": "2021-10-12 09:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002080000, "timestampStr": "2021-10-12 09:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002140000, "timestampStr": "2021-10-12 09:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002200000, "timestampStr": "2021-10-12 09:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002260000, "timestampStr": "2021-10-12 09:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002320000, "timestampStr": "2021-10-12 09:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002380000, "timestampStr": "2021-10-12 09:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002440000, "timestampStr": "2021-10-12 09:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002500000, "timestampStr": "2021-10-12 09:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002560000, "timestampStr": "2021-10-12 09:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002620000, "timestampStr": "2021-10-12 09:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002680000, "timestampStr": "2021-10-12 09:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002740000, "timestampStr": "2021-10-12 09:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002800000, "timestampStr": "2021-10-12 09:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002860000, "timestampStr": "2021-10-12 09:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002920000, "timestampStr": "2021-10-12 09:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002980000, "timestampStr": "2021-10-12 09:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003040000, "timestampStr": "2021-10-12 09:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003100000, "timestampStr": "2021-10-12 09:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003160000, "timestampStr": "2021-10-12 09:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003220000, "timestampStr": "2021-10-12 09:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003280000, "timestampStr": "2021-10-12 09:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003340000, "timestampStr": "2021-10-12 09:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003400000, "timestampStr": "2021-10-12 09:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003460000, "timestampStr": "2021-10-12 09:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003520000, "timestampStr": "2021-10-12 09:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003580000, "timestampStr": "2021-10-12 09:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003640000, "timestampStr": "2021-10-12 09:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003700000, "timestampStr": "2021-10-12 09:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003760000, "timestampStr": "2021-10-12 09:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003820000, "timestampStr": "2021-10-12 09:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003880000, "timestampStr": "2021-10-12 09:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003940000, "timestampStr": "2021-10-12 09:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004000000, "timestampStr": "2021-10-12 10:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004060000, "timestampStr": "2021-10-12 10:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004120000, "timestampStr": "2021-10-12 10:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004180000, "timestampStr": "2021-10-12 10:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004240000, "timestampStr": "2021-10-12 10:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004300000, "timestampStr": "2021-10-12 10:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004360000, "timestampStr": "2021-10-12 10:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004420000, "timestampStr": "2021-10-12 10:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004480000, "timestampStr": "2021-10-12 10:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004540000, "timestampStr": "2021-10-12 10:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004600000, "timestampStr": "2021-10-12 10:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004660000, "timestampStr": "2021-10-12 10:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004720000, "timestampStr": "2021-10-12 10:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004780000, "timestampStr": "2021-10-12 10:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004840000, "timestampStr": "2021-10-12 10:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004900000, "timestampStr": "2021-10-12 10:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004960000, "timestampStr": "2021-10-12 10:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005020000, "timestampStr": "2021-10-12 10:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005080000, "timestampStr": "2021-10-12 10:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005140000, "timestampStr": "2021-10-12 10:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005200000, "timestampStr": "2021-10-12 10:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005260000, "timestampStr": "2021-10-12 10:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005320000, "timestampStr": "2021-10-12 10:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005380000, "timestampStr": "2021-10-12 10:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005440000, "timestampStr": "2021-10-12 10:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005500000, "timestampStr": "2021-10-12 10:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005560000, "timestampStr": "2021-10-12 10:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005620000, "timestampStr": "2021-10-12 10:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005680000, "timestampStr": "2021-10-12 10:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005740000, "timestampStr": "2021-10-12 10:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005800000, "timestampStr": "2021-10-12 10:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005860000, "timestampStr": "2021-10-12 10:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005920000, "timestampStr": "2021-10-12 10:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005980000, "timestampStr": "2021-10-12 10:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006040000, "timestampStr": "2021-10-12 10:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006100000, "timestampStr": "2021-10-12 10:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006160000, "timestampStr": "2021-10-12 10:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006220000, "timestampStr": "2021-10-12 10:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006280000, "timestampStr": "2021-10-12 10:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006340000, "timestampStr": "2021-10-12 10:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006400000, "timestampStr": "2021-10-12 10:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006460000, "timestampStr": "2021-10-12 10:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006520000, "timestampStr": "2021-10-12 10:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006580000, "timestampStr": "2021-10-12 10:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006640000, "timestampStr": "2021-10-12 10:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006700000, "timestampStr": "2021-10-12 10:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006760000, "timestampStr": "2021-10-12 10:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006820000, "timestampStr": "2021-10-12 10:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006880000, "timestampStr": "2021-10-12 10:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006940000, "timestampStr": "2021-10-12 10:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007000000, "timestampStr": "2021-10-12 10:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007060000, "timestampStr": "2021-10-12 10:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007120000, "timestampStr": "2021-10-12 10:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007180000, "timestampStr": "2021-10-12 10:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007240000, "timestampStr": "2021-10-12 10:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007300000, "timestampStr": "2021-10-12 10:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007360000, "timestampStr": "2021-10-12 10:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007420000, "timestampStr": "2021-10-12 10:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007480000, "timestampStr": "2021-10-12 10:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007540000, "timestampStr": "2021-10-12 10:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007600000, "timestampStr": "2021-10-12 11:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007660000, "timestampStr": "2021-10-12 11:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007720000, "timestampStr": "2021-10-12 11:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007780000, "timestampStr": "2021-10-12 11:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007840000, "timestampStr": "2021-10-12 11:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007900000, "timestampStr": "2021-10-12 11:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007960000, "timestampStr": "2021-10-12 11:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008020000, "timestampStr": "2021-10-12 11:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008080000, "timestampStr": "2021-10-12 11:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008140000, "timestampStr": "2021-10-12 11:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008200000, "timestampStr": "2021-10-12 11:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008260000, "timestampStr": "2021-10-12 11:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008320000, "timestampStr": "2021-10-12 11:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008380000, "timestampStr": "2021-10-12 11:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008440000, "timestampStr": "2021-10-12 11:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008500000, "timestampStr": "2021-10-12 11:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008560000, "timestampStr": "2021-10-12 11:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008620000, "timestampStr": "2021-10-12 11:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008680000, "timestampStr": "2021-10-12 11:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008740000, "timestampStr": "2021-10-12 11:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008800000, "timestampStr": "2021-10-12 11:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008860000, "timestampStr": "2021-10-12 11:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008920000, "timestampStr": "2021-10-12 11:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008980000, "timestampStr": "2021-10-12 11:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009040000, "timestampStr": "2021-10-12 11:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009100000, "timestampStr": "2021-10-12 11:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009160000, "timestampStr": "2021-10-12 11:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009220000, "timestampStr": "2021-10-12 11:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009280000, "timestampStr": "2021-10-12 11:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009340000, "timestampStr": "2021-10-12 11:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009400000, "timestampStr": "2021-10-12 11:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009460000, "timestampStr": "2021-10-12 11:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009520000, "timestampStr": "2021-10-12 11:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009580000, "timestampStr": "2021-10-12 11:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009640000, "timestampStr": "2021-10-12 11:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009700000, "timestampStr": "2021-10-12 11:35:00", "indexValue": "0.07", "breakpoint": false}], "resourceType": "OMS", "indexGroup": "I2K_OS", "displayRate": 0, "indexKey": "IOWait", "successRate": 28.06}], "groupQueryData": null, "startTime": 1633966514920, "endTime": 1634009714920, "timeDisplayInfos": [{"timestamp": 1633968000000, "timestampStr": "10-12 00:00"}, {"timestamp": 1633975200000, "timestampStr": "10-12 02:00"}, {"timestamp": 1633982400000, "timestampStr": "10-12 04:00"}, {"timestamp": 1633989600000, "timestampStr": "10-12 06:00"}, {"timestamp": 1633996800000, "timestampStr": "10-12 08:00"}, {"timestamp": 1634004000000, "timestampStr": "10-12 10:00"}], "yAxisScaleCount": 7}, "resultDesc": null}}}, {"request": {"method": "POST", "url": "/rest/pm/bigscreen/historydata", "bodyPatterns": [{"equalToJson": {"beginTime": "${json-unit.any-number}", "endTime": "${json-unit.any-number}", "historyTimeRange": 0, "isGetAggrData": false, "isGetGraphicGroupData": false, "isQueryOriginal": true, "measUnitKey": "I2K_PMMGR", "mo2Index": "[{\"dn\":\"OS=1\",\"indexName\":\"PMDataInsert\"}]", "moType": "OMS", "pageIndex": 1, "period": "${json-unit.any-number}", "pmViewPage": "historyPm"}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"totalPage": 1, "resultData": [{"dn": "OS=1", "neName": "OSS", "displayValue": "PMDataInsert", "indexId": 10, "indexName": "入库总数(条)", "indexUnit": "%", "oper": "", "thresholdList": [-1.0, -1.0, -1.0, -1.0], "indexValues": [{"timestamp": 1633997640000, "timestampStr": "2021-10-12 08:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997700000, "timestampStr": "2021-10-12 08:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997760000, "timestampStr": "2021-10-12 08:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997820000, "timestampStr": "2021-10-12 08:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997880000, "timestampStr": "2021-10-12 08:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997940000, "timestampStr": "2021-10-12 08:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998000000, "timestampStr": "2021-10-12 08:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998060000, "timestampStr": "2021-10-12 08:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998120000, "timestampStr": "2021-10-12 08:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998180000, "timestampStr": "2021-10-12 08:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998240000, "timestampStr": "2021-10-12 08:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998300000, "timestampStr": "2021-10-12 08:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998360000, "timestampStr": "2021-10-12 08:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998420000, "timestampStr": "2021-10-12 08:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998480000, "timestampStr": "2021-10-12 08:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998540000, "timestampStr": "2021-10-12 08:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998600000, "timestampStr": "2021-10-12 08:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998660000, "timestampStr": "2021-10-12 08:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998720000, "timestampStr": "2021-10-12 08:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998780000, "timestampStr": "2021-10-12 08:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998840000, "timestampStr": "2021-10-12 08:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998900000, "timestampStr": "2021-10-12 08:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998960000, "timestampStr": "2021-10-12 08:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999020000, "timestampStr": "2021-10-12 08:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999080000, "timestampStr": "2021-10-12 08:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999140000, "timestampStr": "2021-10-12 08:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999200000, "timestampStr": "2021-10-12 08:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999260000, "timestampStr": "2021-10-12 08:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999320000, "timestampStr": "2021-10-12 08:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999380000, "timestampStr": "2021-10-12 08:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999440000, "timestampStr": "2021-10-12 08:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999500000, "timestampStr": "2021-10-12 08:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999560000, "timestampStr": "2021-10-12 08:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999620000, "timestampStr": "2021-10-12 08:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999680000, "timestampStr": "2021-10-12 08:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999740000, "timestampStr": "2021-10-12 08:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999800000, "timestampStr": "2021-10-12 08:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999860000, "timestampStr": "2021-10-12 08:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999920000, "timestampStr": "2021-10-12 08:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999980000, "timestampStr": "2021-10-12 08:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000040000, "timestampStr": "2021-10-12 08:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000100000, "timestampStr": "2021-10-12 08:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000160000, "timestampStr": "2021-10-12 08:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000220000, "timestampStr": "2021-10-12 08:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000280000, "timestampStr": "2021-10-12 08:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000340000, "timestampStr": "2021-10-12 08:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000400000, "timestampStr": "2021-10-12 09:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000460000, "timestampStr": "2021-10-12 09:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000520000, "timestampStr": "2021-10-12 09:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000580000, "timestampStr": "2021-10-12 09:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000640000, "timestampStr": "2021-10-12 09:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000700000, "timestampStr": "2021-10-12 09:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000760000, "timestampStr": "2021-10-12 09:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000820000, "timestampStr": "2021-10-12 09:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000880000, "timestampStr": "2021-10-12 09:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000940000, "timestampStr": "2021-10-12 09:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001000000, "timestampStr": "2021-10-12 09:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001060000, "timestampStr": "2021-10-12 09:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001120000, "timestampStr": "2021-10-12 09:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001180000, "timestampStr": "2021-10-12 09:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001240000, "timestampStr": "2021-10-12 09:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001300000, "timestampStr": "2021-10-12 09:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001360000, "timestampStr": "2021-10-12 09:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001420000, "timestampStr": "2021-10-12 09:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001480000, "timestampStr": "2021-10-12 09:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001540000, "timestampStr": "2021-10-12 09:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001600000, "timestampStr": "2021-10-12 09:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001660000, "timestampStr": "2021-10-12 09:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001720000, "timestampStr": "2021-10-12 09:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001780000, "timestampStr": "2021-10-12 09:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001840000, "timestampStr": "2021-10-12 09:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001900000, "timestampStr": "2021-10-12 09:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001960000, "timestampStr": "2021-10-12 09:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002020000, "timestampStr": "2021-10-12 09:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002080000, "timestampStr": "2021-10-12 09:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002140000, "timestampStr": "2021-10-12 09:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002200000, "timestampStr": "2021-10-12 09:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002260000, "timestampStr": "2021-10-12 09:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002320000, "timestampStr": "2021-10-12 09:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002380000, "timestampStr": "2021-10-12 09:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002440000, "timestampStr": "2021-10-12 09:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002500000, "timestampStr": "2021-10-12 09:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002560000, "timestampStr": "2021-10-12 09:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002620000, "timestampStr": "2021-10-12 09:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002680000, "timestampStr": "2021-10-12 09:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002740000, "timestampStr": "2021-10-12 09:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002800000, "timestampStr": "2021-10-12 09:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002860000, "timestampStr": "2021-10-12 09:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002920000, "timestampStr": "2021-10-12 09:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002980000, "timestampStr": "2021-10-12 09:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003040000, "timestampStr": "2021-10-12 09:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003100000, "timestampStr": "2021-10-12 09:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003160000, "timestampStr": "2021-10-12 09:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003220000, "timestampStr": "2021-10-12 09:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003280000, "timestampStr": "2021-10-12 09:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003340000, "timestampStr": "2021-10-12 09:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003400000, "timestampStr": "2021-10-12 09:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003460000, "timestampStr": "2021-10-12 09:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003520000, "timestampStr": "2021-10-12 09:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003580000, "timestampStr": "2021-10-12 09:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003640000, "timestampStr": "2021-10-12 09:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003700000, "timestampStr": "2021-10-12 09:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003760000, "timestampStr": "2021-10-12 09:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003820000, "timestampStr": "2021-10-12 09:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003880000, "timestampStr": "2021-10-12 09:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003940000, "timestampStr": "2021-10-12 09:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004000000, "timestampStr": "2021-10-12 10:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004060000, "timestampStr": "2021-10-12 10:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004120000, "timestampStr": "2021-10-12 10:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004180000, "timestampStr": "2021-10-12 10:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004240000, "timestampStr": "2021-10-12 10:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004300000, "timestampStr": "2021-10-12 10:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004360000, "timestampStr": "2021-10-12 10:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004420000, "timestampStr": "2021-10-12 10:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004480000, "timestampStr": "2021-10-12 10:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004540000, "timestampStr": "2021-10-12 10:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004600000, "timestampStr": "2021-10-12 10:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004660000, "timestampStr": "2021-10-12 10:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004720000, "timestampStr": "2021-10-12 10:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004780000, "timestampStr": "2021-10-12 10:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004840000, "timestampStr": "2021-10-12 10:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004900000, "timestampStr": "2021-10-12 10:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004960000, "timestampStr": "2021-10-12 10:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005020000, "timestampStr": "2021-10-12 10:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005080000, "timestampStr": "2021-10-12 10:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005140000, "timestampStr": "2021-10-12 10:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005200000, "timestampStr": "2021-10-12 10:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005260000, "timestampStr": "2021-10-12 10:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005320000, "timestampStr": "2021-10-12 10:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005380000, "timestampStr": "2021-10-12 10:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005440000, "timestampStr": "2021-10-12 10:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005500000, "timestampStr": "2021-10-12 10:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005560000, "timestampStr": "2021-10-12 10:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005620000, "timestampStr": "2021-10-12 10:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005680000, "timestampStr": "2021-10-12 10:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005740000, "timestampStr": "2021-10-12 10:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005800000, "timestampStr": "2021-10-12 10:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005860000, "timestampStr": "2021-10-12 10:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005920000, "timestampStr": "2021-10-12 10:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005980000, "timestampStr": "2021-10-12 10:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006040000, "timestampStr": "2021-10-12 10:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006100000, "timestampStr": "2021-10-12 10:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006160000, "timestampStr": "2021-10-12 10:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006220000, "timestampStr": "2021-10-12 10:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006280000, "timestampStr": "2021-10-12 10:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006340000, "timestampStr": "2021-10-12 10:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006400000, "timestampStr": "2021-10-12 10:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006460000, "timestampStr": "2021-10-12 10:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006520000, "timestampStr": "2021-10-12 10:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006580000, "timestampStr": "2021-10-12 10:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006640000, "timestampStr": "2021-10-12 10:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006700000, "timestampStr": "2021-10-12 10:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006760000, "timestampStr": "2021-10-12 10:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006820000, "timestampStr": "2021-10-12 10:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006880000, "timestampStr": "2021-10-12 10:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006940000, "timestampStr": "2021-10-12 10:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007000000, "timestampStr": "2021-10-12 10:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007060000, "timestampStr": "2021-10-12 10:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007120000, "timestampStr": "2021-10-12 10:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007180000, "timestampStr": "2021-10-12 10:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007240000, "timestampStr": "2021-10-12 10:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007300000, "timestampStr": "2021-10-12 10:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007360000, "timestampStr": "2021-10-12 10:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007420000, "timestampStr": "2021-10-12 10:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007480000, "timestampStr": "2021-10-12 10:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007540000, "timestampStr": "2021-10-12 10:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007600000, "timestampStr": "2021-10-12 11:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007660000, "timestampStr": "2021-10-12 11:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007720000, "timestampStr": "2021-10-12 11:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007780000, "timestampStr": "2021-10-12 11:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007840000, "timestampStr": "2021-10-12 11:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007900000, "timestampStr": "2021-10-12 11:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007960000, "timestampStr": "2021-10-12 11:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008020000, "timestampStr": "2021-10-12 11:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008080000, "timestampStr": "2021-10-12 11:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008140000, "timestampStr": "2021-10-12 11:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008200000, "timestampStr": "2021-10-12 11:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008260000, "timestampStr": "2021-10-12 11:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008320000, "timestampStr": "2021-10-12 11:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008380000, "timestampStr": "2021-10-12 11:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008440000, "timestampStr": "2021-10-12 11:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008500000, "timestampStr": "2021-10-12 11:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008560000, "timestampStr": "2021-10-12 11:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008620000, "timestampStr": "2021-10-12 11:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008680000, "timestampStr": "2021-10-12 11:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008740000, "timestampStr": "2021-10-12 11:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008800000, "timestampStr": "2021-10-12 11:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008860000, "timestampStr": "2021-10-12 11:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008920000, "timestampStr": "2021-10-12 11:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008980000, "timestampStr": "2021-10-12 11:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009040000, "timestampStr": "2021-10-12 11:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009100000, "timestampStr": "2021-10-12 11:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009160000, "timestampStr": "2021-10-12 11:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009220000, "timestampStr": "2021-10-12 11:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009280000, "timestampStr": "2021-10-12 11:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009340000, "timestampStr": "2021-10-12 11:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009400000, "timestampStr": "2021-10-12 11:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009460000, "timestampStr": "2021-10-12 11:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009520000, "timestampStr": "2021-10-12 11:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009580000, "timestampStr": "2021-10-12 11:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009640000, "timestampStr": "2021-10-12 11:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009700000, "timestampStr": "2021-10-12 11:35:00", "indexValue": "0.07", "breakpoint": false}], "resourceType": "OMS", "indexGroup": "I2K_OS", "displayRate": 0, "indexKey": "IOWait", "successRate": 28.06}], "groupQueryData": null, "startTime": 1633966514920, "endTime": 1634009714920, "timeDisplayInfos": [{"timestamp": 1633968000000, "timestampStr": "10-12 00:00"}, {"timestamp": 1633975200000, "timestampStr": "10-12 02:00"}, {"timestamp": 1633982400000, "timestampStr": "10-12 04:00"}, {"timestamp": 1633989600000, "timestampStr": "10-12 06:00"}, {"timestamp": 1633996800000, "timestampStr": "10-12 08:00"}, {"timestamp": 1634004000000, "timestampStr": "10-12 10:00"}], "yAxisScaleCount": 7}, "resultDesc": null}}}, {"request": {"method": "GET", "urlPath": "/rest/i2000/pm/periods"}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"defaultPeriod": 900, "period": 60, "supportPeriod": [60, 300, 900, 1800, 3600, 86400]}, "resultDesc": null}}}, {"request": {"method": "POST", "url": "/rest/pm/bigscreen/historydatas", "bodyPatterns": [{"equalToJson": {"beginTimestamp": "${json-unit.any-number}", "dns": ["OS=1"], "endTimestamp": "${json-unit.any-number}", "measObject": ["10.247.5.114"], "measUnitKey": "I2K_OS", "moType": "OMS", "pageIndex": 1, "pageSize": 2000}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"pageIndex": 1, "pageSize": 2000, "totalCount": 9, "datas": [{"dn": "OS=1", "timestamp": 1663874760000, "period": 60, "measObject": {"tablespace": "IDGENDB"}, "displayValue": "IDGENDB", "values": {"IOWait": "0.02", "tablespaceSizeAllocated": "2048", "tablespaceSizeUsed": "0.39"}}, {"dn": "OS=1", "timestamp": 1663874820000, "period": 60, "measObject": {"tablespace": "IDGENDB"}, "displayValue": "IDGENDB", "values": {"IOWait": "0.02", "tablespaceSizeAllocated": "2048", "tablespaceSizeUsed": "0.39"}}, {"dn": "OS=1", "timestamp": 1663874880000, "period": 60, "measObject": {"tablespace": "IDGENDB"}, "displayValue": "IDGENDB", "values": {"IOWait": "0.02", "tablespaceSizeAllocated": "2048", "tablespaceSizeUsed": "0.39"}}, {"dn": "OS=1", "timestamp": 1663874940000, "period": 60, "measObject": {"tablespace": "IDGENDB"}, "displayValue": "IDGENDB", "values": {"IOWait": "0.02", "tablespaceSizeAllocated": "2048", "tablespaceSizeUsed": "0.39"}}, {"dn": "OS=1", "timestamp": 1663875000000, "period": 60, "measObject": {"tablespace": "IDGENDB"}, "displayValue": "IDGENDB", "values": {"IOWait": "0.02", "tablespaceSizeAllocated": "2048", "tablespaceSizeUsed": "0.39"}}, {"dn": "OS=1", "timestamp": 1663875060000, "period": 60, "measObject": {"tablespace": "IDGENDB"}, "displayValue": "IDGENDB", "values": {"IOWait": "0.02", "tablespaceSizeAllocated": "2048", "tablespaceSizeUsed": "0.39"}}, {"dn": "OS=1", "timestamp": 1663875120000, "period": 60, "measObject": {"tablespace": "IDGENDB"}, "displayValue": "IDGENDB", "values": {"IOWait": "0.02", "tablespaceSizeAllocated": "2048", "tablespaceSizeUsed": "0.39"}}, {"dn": "OS=1", "timestamp": 1663875180000, "period": 60, "measObject": {"tablespace": "IDGENDB"}, "displayValue": "IDGENDB", "values": {"IOWait": "0.02", "tablespaceSizeAllocated": "2048", "tablespaceSizeUsed": "0.39"}}, {"dn": "OS=1", "timestamp": 1663875240000, "period": 60, "measObject": {"tablespace": "IDGENDB"}, "displayValue": "IDGENDB", "values": {"IOWait": "0.02", "tablespaceSizeAllocated": "2048", "tablespaceSizeUsed": "0.39"}}]}, "resultDesc": null}}}, {"request": {"method": "POST", "url": "/rest/pm/bigscreen/historydata", "bodyPatterns": [{"equalToJson": {"beginTime": "${json-unit.any-number}", "endTime": "${json-unit.any-number}", "historyTimeRange": 0, "isGetAggrData": false, "isGetGraphicGroupData": false, "isQueryOriginal": true, "measUnitKey": "I2K_OS", "mo2Index": "[{\"displayValue\":\"7.220.134.30\",\"dn\":\"OS=1\",\"indexName\":\"UsedCPURate\"},{\"displayValue\":\"7.220.134.30\",\"dn\":\"OS=1\",\"indexName\":\"UsedMemoryRate\"}]", "moType": "OMS", "pageIndex": 1, "period": 60, "pmViewPage": "historyPm"}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"totalPage": 1, "resultData": [{"dn": "OS=1", "neName": null, "displayValue": "7.220.134.30", "indexId": 5, "indexName": "内存使用率", "indexUnit": "%", "oper": null, "thresholdList": [], "indexValues": [{"timestamp": 1665611880000, "timestampStr": "2022-10-13 05:58", "indexValue": "58", "breakpoint": false}, {"timestamp": 1665611940000, "timestampStr": "2022-10-13 05:59", "indexValue": "61", "breakpoint": false}, {"timestamp": 1665612000000, "timestampStr": "2022-10-13 06:00", "indexValue": "61", "breakpoint": false}, {"timestamp": 1665612060000, "timestampStr": "2022-10-13 06:01", "indexValue": "60", "breakpoint": false}], "resourceType": null, "indexGroup": "I2K_OS", "displayRate": 0, "indexKey": "UsedMemoryRate", "successRate": 0.0}, {"dn": "OS=1", "neName": null, "displayValue": "7.220.134.30", "indexId": 6, "indexName": "CPU占用率", "indexUnit": "%", "oper": null, "thresholdList": [], "indexValues": [{"timestamp": 1665611880000, "timestampStr": "2022-10-13 05:58", "indexValue": "57.87", "breakpoint": false}, {"timestamp": 1665611940000, "timestampStr": "2022-10-13 05:59", "indexValue": "51.67", "breakpoint": false}, {"timestamp": 1665612000000, "timestampStr": "2022-10-13 06:00", "indexValue": "55.01", "breakpoint": false}, {"timestamp": 1665612060000, "timestampStr": "2022-10-13 06:01", "indexValue": "20.06", "breakpoint": false}], "resourceType": null, "indexGroup": "I2K_OS", "displayRate": 0, "indexKey": "UsedCPURate", "successRate": 0.0}], "groupQueryData": null, "startTime": 0, "endTime": 0, "timeDisplayInfos": null, "yAxisScaleCount": 0}, "resultDesc": null}}}, {"request": {"method": "POST", "url": "/rest/pm/bigscreen/historydata", "bodyPatterns": [{"equalToJson": {"beginTime": 1695197700132, "endTime": 1695197820132, "historyTimeRange": 0, "isGetAggrData": false, "isGetGraphicGroupData": false, "isQueryOriginal": true, "measUnitKey": "unitKey", "mo2Index": "[{\"dn\":\"dn\",\"indexName\":\"typeKey\"}]", "moType": "moType", "pageIndex": 1, "period": 0, "pmViewPage": "historyPm"}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"totalPage": 1, "resultData": [{"dn": "OS=1", "neName": null, "displayValue": "7.220.134.30", "indexId": 5, "indexName": "内存使用率", "indexUnit": "%", "oper": null, "thresholdList": [], "indexValues": [{"timestamp": 1665611880000, "timestampStr": "2022-10-13 05:58", "indexValue": "58", "breakpoint": false}, {"timestamp": 1665611940000, "timestampStr": "2022-10-13 05:59", "indexValue": "61", "breakpoint": false}, {"timestamp": 1665612000000, "timestampStr": "2022-10-13 06:00", "indexValue": "61", "breakpoint": false}, {"timestamp": 1665612060000, "timestampStr": "2022-10-13 06:01", "indexValue": "60", "breakpoint": false}], "resourceType": null, "indexGroup": "I2K_OS", "displayRate": 0, "indexKey": "UsedMemoryRate", "successRate": 0.0}, {"dn": "OS=1", "neName": null, "displayValue": "7.220.134.30", "indexId": 6, "indexName": "CPU占用率", "indexUnit": "%", "oper": null, "thresholdList": [], "indexValues": [{"timestamp": 1665611880000, "timestampStr": "2022-10-13 05:58", "indexValue": "57.87", "breakpoint": false}, {"timestamp": 1665611940000, "timestampStr": "2022-10-13 05:59", "indexValue": "51.67", "breakpoint": false}, {"timestamp": 1665612000000, "timestampStr": "2022-10-13 06:00", "indexValue": "55.01", "breakpoint": false}, {"timestamp": 1665612060000, "timestampStr": "2022-10-13 06:01", "indexValue": "20.06", "breakpoint": false}], "resourceType": null, "indexGroup": "I2K_OS", "displayRate": 0, "indexKey": "UsedCPURate", "successRate": 0.0}], "groupQueryData": null, "startTime": 0, "endTime": 0, "timeDisplayInfos": null, "yAxisScaleCount": 0}, "resultDesc": null}}}, {"request": {"method": "POST", "url": "/rest/pm/bigscreen/historydata", "bodyPatterns": [{"equalToJson": {"beginTime": 1695197700132, "endTime": 1695197820132, "historyTimeRange": 0, "isGetAggrData": false, "isGetGraphicGroupData": false, "isQueryOriginal": true, "measUnitKey": "unitKey", "mo2Index": "[{\"dn\":\"OS=1\",\"indexName\":\"typeKey\"}]", "moType": "moType", "pageIndex": 1, "period": 0, "pmViewPage": "historyPm"}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"totalPage": 1, "resultData": [{"dn": "OS=1", "neName": null, "displayValue": "7.220.134.30", "indexId": 5, "indexName": "内存使用率", "indexUnit": "%", "oper": null, "thresholdList": [], "indexValues": [{"timestamp": 1665611880000, "timestampStr": "2022-10-13 05:58", "indexValue": "58", "breakpoint": false}, {"timestamp": 1665611940000, "timestampStr": "2022-10-13 05:59", "indexValue": "61", "breakpoint": false}, {"timestamp": 1665612000000, "timestampStr": "2022-10-13 06:00", "indexValue": "61", "breakpoint": false}, {"timestamp": 1665612060000, "timestampStr": "2022-10-13 06:01", "indexValue": "60", "breakpoint": false}], "resourceType": null, "indexGroup": "I2K_OS", "displayRate": 0, "indexKey": "UsedMemoryRate", "successRate": 0.0}, {"dn": "OS=1", "neName": null, "displayValue": "7.220.134.30", "indexId": 6, "indexName": "CPU占用率", "indexUnit": "%", "oper": null, "thresholdList": [], "indexValues": [{"timestamp": 1665611880000, "timestampStr": "2022-10-13 05:58", "indexValue": "57.87", "breakpoint": false}, {"timestamp": 1665611940000, "timestampStr": "2022-10-13 05:59", "indexValue": "51.67", "breakpoint": false}, {"timestamp": 1665612000000, "timestampStr": "2022-10-13 06:00", "indexValue": "55.01", "breakpoint": false}, {"timestamp": 1665612060000, "timestampStr": "2022-10-13 06:01", "indexValue": "20.06", "breakpoint": false}], "resourceType": null, "indexGroup": "I2K_OS", "displayRate": 0, "indexKey": "UsedCPURate", "successRate": 0.0}], "groupQueryData": null, "startTime": 0, "endTime": 0, "timeDisplayInfos": null, "yAxisScaleCount": 0}, "resultDesc": null}}}, {"request": {"method": "GET", "url": "/rest/eammimservice/v1/mo/type?start=0&type=moType"}, "response": {"status": 200, "jsonBody": [{"@moClass": "com.huawei.oms.eam.mo.NetworkElement", "sequenceNo": 1, "dn": "OS=1", "fdn": "1.1", "typeID": 0, "type": "OMS", "version": "", "displayName": "OSS", "name": "OSS", "vendor": "<PERSON><PERSON><PERSON>", "description": null, "owner": null, "createdTime": 1695177439659, "medNodeID": null, "alarmStatus": "Cleared", "clientProperties": {"TypeCode": "", "Code": "", "solutionId": "1"}, "children": [], "parent": "/", "msId": null, "groupId": null, "tenantId": null, "adminStatus": "Unlocked", "availableStatus": "Normal", "usageStatus": "Active", "operationalStatus": "Enabled", "language": "zh_CN", "timeZone": "China Standard Time", "connectStatus": "NoDetect", "location": null, "maintainer": null, "contact": null, "deviceCode": null, "address": "", "code": "", "typeCode": "", "lifecycleStatus": "Unknown", "ipaddress": "", "hastatus": "Single"}]}}, {"request": {"method": "POST", "url": "/rest/dvihealingservice/v1/dvflowtaskservice/startFlowByDefinition", "bodyPatterns": [{"equalToJson": {}}]}, "response": {"status": 200, "jsonBody": {"taskIds": ["10"], "targetObj": "targetObj"}}}, {"request": {"method": "POST", "url": "/rest/pm/bigscreen/historydata", "bodyPatterns": [{"equalToJson": {"beginTime": "${json-unit.any-number}", "endTime": "${json-unit.any-number}", "historyTimeRange": 0, "isGetAggrData": false, "isGetGraphicGroupData": false, "isQueryOriginal": true, "measUnitKey": "I2K_OS", "mo2Index": "[{\"displayValue\":\"*************\",\"dn\":\"OS=1\",\"indexName\":\"IOWait\"},{\"displayValue\":\"*************\",\"dn\":\"OS=1\",\"indexName\":\"IOWait\"},{\"displayValue\":\"*************\",\"dn\":\"OS=1\",\"indexName\":\"IOWait\"},{\"displayValue\":\"*************\",\"dn\":\"OS=1\",\"indexName\":\"IOWait\"}]", "moType": "OMS", "pageIndex": 1, "period": "${json-unit.any-number}", "pmViewPage": "historyPm"}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"totalPage": 1, "resultData": [{"dn": "OS=1", "neName": "OSS", "displayValue": "10.247.5.114", "indexId": 10, "indexName": "IO等待", "indexUnit": "%", "oper": "", "thresholdList": [-1.0, -1.0, -1.0, -1.0], "indexValues": [{"timestamp": 1633997640000, "timestampStr": "2021-10-12 08:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997700000, "timestampStr": "2021-10-12 08:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997760000, "timestampStr": "2021-10-12 08:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997820000, "timestampStr": "2021-10-12 08:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997880000, "timestampStr": "2021-10-12 08:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997940000, "timestampStr": "2021-10-12 08:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998000000, "timestampStr": "2021-10-12 08:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998060000, "timestampStr": "2021-10-12 08:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998120000, "timestampStr": "2021-10-12 08:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998180000, "timestampStr": "2021-10-12 08:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998240000, "timestampStr": "2021-10-12 08:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998300000, "timestampStr": "2021-10-12 08:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998360000, "timestampStr": "2021-10-12 08:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998420000, "timestampStr": "2021-10-12 08:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998480000, "timestampStr": "2021-10-12 08:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998540000, "timestampStr": "2021-10-12 08:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998600000, "timestampStr": "2021-10-12 08:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998660000, "timestampStr": "2021-10-12 08:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998720000, "timestampStr": "2021-10-12 08:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998780000, "timestampStr": "2021-10-12 08:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998840000, "timestampStr": "2021-10-12 08:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998900000, "timestampStr": "2021-10-12 08:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998960000, "timestampStr": "2021-10-12 08:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999020000, "timestampStr": "2021-10-12 08:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999080000, "timestampStr": "2021-10-12 08:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999140000, "timestampStr": "2021-10-12 08:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999200000, "timestampStr": "2021-10-12 08:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999260000, "timestampStr": "2021-10-12 08:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999320000, "timestampStr": "2021-10-12 08:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999380000, "timestampStr": "2021-10-12 08:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999440000, "timestampStr": "2021-10-12 08:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999500000, "timestampStr": "2021-10-12 08:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999560000, "timestampStr": "2021-10-12 08:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999620000, "timestampStr": "2021-10-12 08:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999680000, "timestampStr": "2021-10-12 08:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999740000, "timestampStr": "2021-10-12 08:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999800000, "timestampStr": "2021-10-12 08:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999860000, "timestampStr": "2021-10-12 08:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999920000, "timestampStr": "2021-10-12 08:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999980000, "timestampStr": "2021-10-12 08:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000040000, "timestampStr": "2021-10-12 08:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000100000, "timestampStr": "2021-10-12 08:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000160000, "timestampStr": "2021-10-12 08:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000220000, "timestampStr": "2021-10-12 08:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000280000, "timestampStr": "2021-10-12 08:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000340000, "timestampStr": "2021-10-12 08:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000400000, "timestampStr": "2021-10-12 09:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000460000, "timestampStr": "2021-10-12 09:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000520000, "timestampStr": "2021-10-12 09:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000580000, "timestampStr": "2021-10-12 09:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000640000, "timestampStr": "2021-10-12 09:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000700000, "timestampStr": "2021-10-12 09:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000760000, "timestampStr": "2021-10-12 09:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000820000, "timestampStr": "2021-10-12 09:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000880000, "timestampStr": "2021-10-12 09:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000940000, "timestampStr": "2021-10-12 09:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001000000, "timestampStr": "2021-10-12 09:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001060000, "timestampStr": "2021-10-12 09:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001120000, "timestampStr": "2021-10-12 09:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001180000, "timestampStr": "2021-10-12 09:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001240000, "timestampStr": "2021-10-12 09:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001300000, "timestampStr": "2021-10-12 09:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001360000, "timestampStr": "2021-10-12 09:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001420000, "timestampStr": "2021-10-12 09:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001480000, "timestampStr": "2021-10-12 09:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001540000, "timestampStr": "2021-10-12 09:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001600000, "timestampStr": "2021-10-12 09:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001660000, "timestampStr": "2021-10-12 09:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001720000, "timestampStr": "2021-10-12 09:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001780000, "timestampStr": "2021-10-12 09:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001840000, "timestampStr": "2021-10-12 09:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001900000, "timestampStr": "2021-10-12 09:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001960000, "timestampStr": "2021-10-12 09:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002020000, "timestampStr": "2021-10-12 09:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002080000, "timestampStr": "2021-10-12 09:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002140000, "timestampStr": "2021-10-12 09:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002200000, "timestampStr": "2021-10-12 09:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002260000, "timestampStr": "2021-10-12 09:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002320000, "timestampStr": "2021-10-12 09:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002380000, "timestampStr": "2021-10-12 09:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002440000, "timestampStr": "2021-10-12 09:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002500000, "timestampStr": "2021-10-12 09:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002560000, "timestampStr": "2021-10-12 09:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002620000, "timestampStr": "2021-10-12 09:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002680000, "timestampStr": "2021-10-12 09:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002740000, "timestampStr": "2021-10-12 09:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002800000, "timestampStr": "2021-10-12 09:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002860000, "timestampStr": "2021-10-12 09:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002920000, "timestampStr": "2021-10-12 09:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002980000, "timestampStr": "2021-10-12 09:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003040000, "timestampStr": "2021-10-12 09:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003100000, "timestampStr": "2021-10-12 09:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003160000, "timestampStr": "2021-10-12 09:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003220000, "timestampStr": "2021-10-12 09:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003280000, "timestampStr": "2021-10-12 09:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003340000, "timestampStr": "2021-10-12 09:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003400000, "timestampStr": "2021-10-12 09:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003460000, "timestampStr": "2021-10-12 09:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003520000, "timestampStr": "2021-10-12 09:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003580000, "timestampStr": "2021-10-12 09:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003640000, "timestampStr": "2021-10-12 09:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003700000, "timestampStr": "2021-10-12 09:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003760000, "timestampStr": "2021-10-12 09:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003820000, "timestampStr": "2021-10-12 09:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003880000, "timestampStr": "2021-10-12 09:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003940000, "timestampStr": "2021-10-12 09:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004000000, "timestampStr": "2021-10-12 10:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004060000, "timestampStr": "2021-10-12 10:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004120000, "timestampStr": "2021-10-12 10:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004180000, "timestampStr": "2021-10-12 10:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004240000, "timestampStr": "2021-10-12 10:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004300000, "timestampStr": "2021-10-12 10:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004360000, "timestampStr": "2021-10-12 10:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004420000, "timestampStr": "2021-10-12 10:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004480000, "timestampStr": "2021-10-12 10:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004540000, "timestampStr": "2021-10-12 10:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004600000, "timestampStr": "2021-10-12 10:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004660000, "timestampStr": "2021-10-12 10:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004720000, "timestampStr": "2021-10-12 10:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004780000, "timestampStr": "2021-10-12 10:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004840000, "timestampStr": "2021-10-12 10:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004900000, "timestampStr": "2021-10-12 10:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004960000, "timestampStr": "2021-10-12 10:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005020000, "timestampStr": "2021-10-12 10:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005080000, "timestampStr": "2021-10-12 10:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005140000, "timestampStr": "2021-10-12 10:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005200000, "timestampStr": "2021-10-12 10:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005260000, "timestampStr": "2021-10-12 10:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005320000, "timestampStr": "2021-10-12 10:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005380000, "timestampStr": "2021-10-12 10:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005440000, "timestampStr": "2021-10-12 10:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005500000, "timestampStr": "2021-10-12 10:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005560000, "timestampStr": "2021-10-12 10:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005620000, "timestampStr": "2021-10-12 10:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005680000, "timestampStr": "2021-10-12 10:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005740000, "timestampStr": "2021-10-12 10:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005800000, "timestampStr": "2021-10-12 10:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005860000, "timestampStr": "2021-10-12 10:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005920000, "timestampStr": "2021-10-12 10:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005980000, "timestampStr": "2021-10-12 10:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006040000, "timestampStr": "2021-10-12 10:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006100000, "timestampStr": "2021-10-12 10:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006160000, "timestampStr": "2021-10-12 10:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006220000, "timestampStr": "2021-10-12 10:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006280000, "timestampStr": "2021-10-12 10:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006340000, "timestampStr": "2021-10-12 10:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006400000, "timestampStr": "2021-10-12 10:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006460000, "timestampStr": "2021-10-12 10:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006520000, "timestampStr": "2021-10-12 10:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006580000, "timestampStr": "2021-10-12 10:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006640000, "timestampStr": "2021-10-12 10:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006700000, "timestampStr": "2021-10-12 10:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006760000, "timestampStr": "2021-10-12 10:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006820000, "timestampStr": "2021-10-12 10:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006880000, "timestampStr": "2021-10-12 10:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006940000, "timestampStr": "2021-10-12 10:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007000000, "timestampStr": "2021-10-12 10:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007060000, "timestampStr": "2021-10-12 10:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007120000, "timestampStr": "2021-10-12 10:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007180000, "timestampStr": "2021-10-12 10:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007240000, "timestampStr": "2021-10-12 10:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007300000, "timestampStr": "2021-10-12 10:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007360000, "timestampStr": "2021-10-12 10:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007420000, "timestampStr": "2021-10-12 10:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007480000, "timestampStr": "2021-10-12 10:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007540000, "timestampStr": "2021-10-12 10:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007600000, "timestampStr": "2021-10-12 11:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007660000, "timestampStr": "2021-10-12 11:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007720000, "timestampStr": "2021-10-12 11:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007780000, "timestampStr": "2021-10-12 11:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007840000, "timestampStr": "2021-10-12 11:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007900000, "timestampStr": "2021-10-12 11:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007960000, "timestampStr": "2021-10-12 11:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008020000, "timestampStr": "2021-10-12 11:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008080000, "timestampStr": "2021-10-12 11:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008140000, "timestampStr": "2021-10-12 11:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008200000, "timestampStr": "2021-10-12 11:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008260000, "timestampStr": "2021-10-12 11:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008320000, "timestampStr": "2021-10-12 11:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008380000, "timestampStr": "2021-10-12 11:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008440000, "timestampStr": "2021-10-12 11:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008500000, "timestampStr": "2021-10-12 11:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008560000, "timestampStr": "2021-10-12 11:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008620000, "timestampStr": "2021-10-12 11:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008680000, "timestampStr": "2021-10-12 11:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008740000, "timestampStr": "2021-10-12 11:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008800000, "timestampStr": "2021-10-12 11:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008860000, "timestampStr": "2021-10-12 11:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008920000, "timestampStr": "2021-10-12 11:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008980000, "timestampStr": "2021-10-12 11:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009040000, "timestampStr": "2021-10-12 11:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009100000, "timestampStr": "2021-10-12 11:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009160000, "timestampStr": "2021-10-12 11:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009220000, "timestampStr": "2021-10-12 11:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009280000, "timestampStr": "2021-10-12 11:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009340000, "timestampStr": "2021-10-12 11:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009400000, "timestampStr": "2021-10-12 11:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009460000, "timestampStr": "2021-10-12 11:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009520000, "timestampStr": "2021-10-12 11:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009580000, "timestampStr": "2021-10-12 11:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009640000, "timestampStr": "2021-10-12 11:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009700000, "timestampStr": "2021-10-12 11:35:00", "indexValue": "0.07", "breakpoint": false}], "resourceType": "OMS", "indexGroup": "I2K_OS", "displayRate": 0, "indexKey": "IOWait", "successRate": 28.06}], "groupQueryData": null, "startTime": 1633966514920, "endTime": 1634009714920, "timeDisplayInfos": [{"timestamp": 1633968000000, "timestampStr": "10-12 00:00"}, {"timestamp": 1633975200000, "timestampStr": "10-12 02:00"}, {"timestamp": 1633982400000, "timestampStr": "10-12 04:00"}, {"timestamp": 1633989600000, "timestampStr": "10-12 06:00"}, {"timestamp": 1633996800000, "timestampStr": "10-12 08:00"}, {"timestamp": 1634004000000, "timestampStr": "10-12 10:00"}], "yAxisScaleCount": 7}, "resultDesc": null}}}, {"request": {"method": "POST", "url": "/rest/pm/bigscreen/historydata", "bodyPatterns": [{"equalToJson": {"beginTime": "${json-unit.any-number}", "endTime": "${json-unit.any-number}", "historyTimeRange": 0, "isGetAggrData": false, "isGetGraphicGroupData": false, "isQueryOriginal": true, "measUnitKey": "I2K_OS", "mo2Index": "[{\"displayValue\":\"7.220.10.4\",\"dn\":\"OS=1\",\"indexName\":\"UsedCPURate\"}]", "moType": "OMS", "pageIndex": 1, "period": "${json-unit.any-number}", "pmViewPage": "historyPm"}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"totalPage": 1, "resultData": [{"dn": "OS=1", "neName": "OSS", "displayValue": "7.220.10.4", "indexId": 10, "indexName": "UsedCPURate", "indexUnit": "%", "oper": "", "thresholdList": [-1.0, -1.0, -1.0, -1.0], "indexValues": [{"timestamp": 1633997640000, "timestampStr": "2021-10-12 08:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997700000, "timestampStr": "2021-10-12 08:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997760000, "timestampStr": "2021-10-12 08:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997820000, "timestampStr": "2021-10-12 08:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997880000, "timestampStr": "2021-10-12 08:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633997940000, "timestampStr": "2021-10-12 08:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998000000, "timestampStr": "2021-10-12 08:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998060000, "timestampStr": "2021-10-12 08:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998120000, "timestampStr": "2021-10-12 08:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998180000, "timestampStr": "2021-10-12 08:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998240000, "timestampStr": "2021-10-12 08:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998300000, "timestampStr": "2021-10-12 08:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998360000, "timestampStr": "2021-10-12 08:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998420000, "timestampStr": "2021-10-12 08:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998480000, "timestampStr": "2021-10-12 08:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998540000, "timestampStr": "2021-10-12 08:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998600000, "timestampStr": "2021-10-12 08:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998660000, "timestampStr": "2021-10-12 08:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998720000, "timestampStr": "2021-10-12 08:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998780000, "timestampStr": "2021-10-12 08:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998840000, "timestampStr": "2021-10-12 08:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998900000, "timestampStr": "2021-10-12 08:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633998960000, "timestampStr": "2021-10-12 08:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999020000, "timestampStr": "2021-10-12 08:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999080000, "timestampStr": "2021-10-12 08:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999140000, "timestampStr": "2021-10-12 08:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999200000, "timestampStr": "2021-10-12 08:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999260000, "timestampStr": "2021-10-12 08:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999320000, "timestampStr": "2021-10-12 08:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999380000, "timestampStr": "2021-10-12 08:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999440000, "timestampStr": "2021-10-12 08:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999500000, "timestampStr": "2021-10-12 08:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999560000, "timestampStr": "2021-10-12 08:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999620000, "timestampStr": "2021-10-12 08:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999680000, "timestampStr": "2021-10-12 08:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999740000, "timestampStr": "2021-10-12 08:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999800000, "timestampStr": "2021-10-12 08:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999860000, "timestampStr": "2021-10-12 08:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999920000, "timestampStr": "2021-10-12 08:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1633999980000, "timestampStr": "2021-10-12 08:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000040000, "timestampStr": "2021-10-12 08:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000100000, "timestampStr": "2021-10-12 08:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000160000, "timestampStr": "2021-10-12 08:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000220000, "timestampStr": "2021-10-12 08:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000280000, "timestampStr": "2021-10-12 08:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000340000, "timestampStr": "2021-10-12 08:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000400000, "timestampStr": "2021-10-12 09:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000460000, "timestampStr": "2021-10-12 09:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000520000, "timestampStr": "2021-10-12 09:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000580000, "timestampStr": "2021-10-12 09:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000640000, "timestampStr": "2021-10-12 09:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000700000, "timestampStr": "2021-10-12 09:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000760000, "timestampStr": "2021-10-12 09:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000820000, "timestampStr": "2021-10-12 09:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000880000, "timestampStr": "2021-10-12 09:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634000940000, "timestampStr": "2021-10-12 09:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001000000, "timestampStr": "2021-10-12 09:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001060000, "timestampStr": "2021-10-12 09:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001120000, "timestampStr": "2021-10-12 09:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001180000, "timestampStr": "2021-10-12 09:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001240000, "timestampStr": "2021-10-12 09:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001300000, "timestampStr": "2021-10-12 09:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001360000, "timestampStr": "2021-10-12 09:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001420000, "timestampStr": "2021-10-12 09:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001480000, "timestampStr": "2021-10-12 09:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001540000, "timestampStr": "2021-10-12 09:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001600000, "timestampStr": "2021-10-12 09:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001660000, "timestampStr": "2021-10-12 09:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001720000, "timestampStr": "2021-10-12 09:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001780000, "timestampStr": "2021-10-12 09:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001840000, "timestampStr": "2021-10-12 09:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001900000, "timestampStr": "2021-10-12 09:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634001960000, "timestampStr": "2021-10-12 09:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002020000, "timestampStr": "2021-10-12 09:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002080000, "timestampStr": "2021-10-12 09:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002140000, "timestampStr": "2021-10-12 09:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002200000, "timestampStr": "2021-10-12 09:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002260000, "timestampStr": "2021-10-12 09:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002320000, "timestampStr": "2021-10-12 09:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002380000, "timestampStr": "2021-10-12 09:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002440000, "timestampStr": "2021-10-12 09:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002500000, "timestampStr": "2021-10-12 09:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002560000, "timestampStr": "2021-10-12 09:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002620000, "timestampStr": "2021-10-12 09:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002680000, "timestampStr": "2021-10-12 09:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002740000, "timestampStr": "2021-10-12 09:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002800000, "timestampStr": "2021-10-12 09:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002860000, "timestampStr": "2021-10-12 09:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002920000, "timestampStr": "2021-10-12 09:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634002980000, "timestampStr": "2021-10-12 09:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003040000, "timestampStr": "2021-10-12 09:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003100000, "timestampStr": "2021-10-12 09:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003160000, "timestampStr": "2021-10-12 09:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003220000, "timestampStr": "2021-10-12 09:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003280000, "timestampStr": "2021-10-12 09:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003340000, "timestampStr": "2021-10-12 09:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003400000, "timestampStr": "2021-10-12 09:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003460000, "timestampStr": "2021-10-12 09:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003520000, "timestampStr": "2021-10-12 09:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003580000, "timestampStr": "2021-10-12 09:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003640000, "timestampStr": "2021-10-12 09:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003700000, "timestampStr": "2021-10-12 09:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003760000, "timestampStr": "2021-10-12 09:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003820000, "timestampStr": "2021-10-12 09:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003880000, "timestampStr": "2021-10-12 09:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634003940000, "timestampStr": "2021-10-12 09:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004000000, "timestampStr": "2021-10-12 10:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004060000, "timestampStr": "2021-10-12 10:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004120000, "timestampStr": "2021-10-12 10:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004180000, "timestampStr": "2021-10-12 10:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004240000, "timestampStr": "2021-10-12 10:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004300000, "timestampStr": "2021-10-12 10:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004360000, "timestampStr": "2021-10-12 10:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004420000, "timestampStr": "2021-10-12 10:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004480000, "timestampStr": "2021-10-12 10:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004540000, "timestampStr": "2021-10-12 10:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004600000, "timestampStr": "2021-10-12 10:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004660000, "timestampStr": "2021-10-12 10:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004720000, "timestampStr": "2021-10-12 10:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004780000, "timestampStr": "2021-10-12 10:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004840000, "timestampStr": "2021-10-12 10:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004900000, "timestampStr": "2021-10-12 10:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634004960000, "timestampStr": "2021-10-12 10:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005020000, "timestampStr": "2021-10-12 10:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005080000, "timestampStr": "2021-10-12 10:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005140000, "timestampStr": "2021-10-12 10:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005200000, "timestampStr": "2021-10-12 10:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005260000, "timestampStr": "2021-10-12 10:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005320000, "timestampStr": "2021-10-12 10:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005380000, "timestampStr": "2021-10-12 10:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005440000, "timestampStr": "2021-10-12 10:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005500000, "timestampStr": "2021-10-12 10:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005560000, "timestampStr": "2021-10-12 10:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005620000, "timestampStr": "2021-10-12 10:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005680000, "timestampStr": "2021-10-12 10:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005740000, "timestampStr": "2021-10-12 10:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005800000, "timestampStr": "2021-10-12 10:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005860000, "timestampStr": "2021-10-12 10:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005920000, "timestampStr": "2021-10-12 10:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634005980000, "timestampStr": "2021-10-12 10:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006040000, "timestampStr": "2021-10-12 10:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006100000, "timestampStr": "2021-10-12 10:35:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006160000, "timestampStr": "2021-10-12 10:36:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006220000, "timestampStr": "2021-10-12 10:37:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006280000, "timestampStr": "2021-10-12 10:38:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006340000, "timestampStr": "2021-10-12 10:39:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006400000, "timestampStr": "2021-10-12 10:40:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006460000, "timestampStr": "2021-10-12 10:41:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006520000, "timestampStr": "2021-10-12 10:42:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006580000, "timestampStr": "2021-10-12 10:43:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006640000, "timestampStr": "2021-10-12 10:44:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006700000, "timestampStr": "2021-10-12 10:45:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006760000, "timestampStr": "2021-10-12 10:46:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006820000, "timestampStr": "2021-10-12 10:47:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006880000, "timestampStr": "2021-10-12 10:48:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634006940000, "timestampStr": "2021-10-12 10:49:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007000000, "timestampStr": "2021-10-12 10:50:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007060000, "timestampStr": "2021-10-12 10:51:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007120000, "timestampStr": "2021-10-12 10:52:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007180000, "timestampStr": "2021-10-12 10:53:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007240000, "timestampStr": "2021-10-12 10:54:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007300000, "timestampStr": "2021-10-12 10:55:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007360000, "timestampStr": "2021-10-12 10:56:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007420000, "timestampStr": "2021-10-12 10:57:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007480000, "timestampStr": "2021-10-12 10:58:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007540000, "timestampStr": "2021-10-12 10:59:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007600000, "timestampStr": "2021-10-12 11:00:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007660000, "timestampStr": "2021-10-12 11:01:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007720000, "timestampStr": "2021-10-12 11:02:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007780000, "timestampStr": "2021-10-12 11:03:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007840000, "timestampStr": "2021-10-12 11:04:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007900000, "timestampStr": "2021-10-12 11:05:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634007960000, "timestampStr": "2021-10-12 11:06:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008020000, "timestampStr": "2021-10-12 11:07:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008080000, "timestampStr": "2021-10-12 11:08:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008140000, "timestampStr": "2021-10-12 11:09:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008200000, "timestampStr": "2021-10-12 11:10:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008260000, "timestampStr": "2021-10-12 11:11:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008320000, "timestampStr": "2021-10-12 11:12:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008380000, "timestampStr": "2021-10-12 11:13:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008440000, "timestampStr": "2021-10-12 11:14:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008500000, "timestampStr": "2021-10-12 11:15:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008560000, "timestampStr": "2021-10-12 11:16:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008620000, "timestampStr": "2021-10-12 11:17:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008680000, "timestampStr": "2021-10-12 11:18:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008740000, "timestampStr": "2021-10-12 11:19:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008800000, "timestampStr": "2021-10-12 11:20:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008860000, "timestampStr": "2021-10-12 11:21:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008920000, "timestampStr": "2021-10-12 11:22:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634008980000, "timestampStr": "2021-10-12 11:23:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009040000, "timestampStr": "2021-10-12 11:24:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009100000, "timestampStr": "2021-10-12 11:25:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009160000, "timestampStr": "2021-10-12 11:26:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009220000, "timestampStr": "2021-10-12 11:27:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009280000, "timestampStr": "2021-10-12 11:28:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009340000, "timestampStr": "2021-10-12 11:29:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009400000, "timestampStr": "2021-10-12 11:30:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009460000, "timestampStr": "2021-10-12 11:31:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009520000, "timestampStr": "2021-10-12 11:32:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009580000, "timestampStr": "2021-10-12 11:33:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009640000, "timestampStr": "2021-10-12 11:34:00", "indexValue": "0.07", "breakpoint": false}, {"timestamp": 1634009700000, "timestampStr": "2021-10-12 11:35:00", "indexValue": "0.07", "breakpoint": false}], "resourceType": "OMS", "indexGroup": "I2K_OS", "displayRate": 0, "indexKey": "UsedCPURate", "successRate": 28.06}], "groupQueryData": null, "startTime": 1633966514920, "endTime": 1634009714920, "timeDisplayInfos": [{"timestamp": 1633968000000, "timestampStr": "10-12 00:00"}, {"timestamp": 1633975200000, "timestampStr": "10-12 02:00"}, {"timestamp": 1633982400000, "timestampStr": "10-12 04:00"}, {"timestamp": 1633989600000, "timestampStr": "10-12 06:00"}, {"timestamp": 1633996800000, "timestampStr": "10-12 08:00"}, {"timestamp": 1634004000000, "timestampStr": "10-12 10:00"}], "yAxisScaleCount": 7}, "resultDesc": null}}}, {"request": {"method": "POST", "url": "/rest/pm/bigscreen/historydata", "bodyPatterns": [{"equalToJson": {"beginTime": "${json-unit.any-number}", "endTime": "${json-unit.any-number}", "groupAggregationList": {"1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_24": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_160": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_72": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_168": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_80": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_176": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_112": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_80": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_184": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_88": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_56": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_192": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_176": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_16": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_88": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_104": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_8": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_176": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_88": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_184": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_32": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_160": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_0": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_80": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_40": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_176": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_168": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_128": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_96": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_48": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_72": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_120": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_16": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_104": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_144": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_88": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_8": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_192": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_96": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_80": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_192": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_0": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_136": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_0": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_112": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_160": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_8": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_24": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_32": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_168": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_112": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_64": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_48": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_24": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_152": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_96": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_16": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_128": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_144": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_184": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_152": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_32": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_120": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_24": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_104": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_56": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_16": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_120": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_32": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_64": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_128": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_144": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_40": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_40": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_48": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_136": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_96": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_152": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_136": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_48": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_24": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_40": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_72": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_136": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_168": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_80": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_120": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_160": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_56": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_144": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_88": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_136": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_128": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_32": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_16": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_56": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_184": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_40": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_104": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_72": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_120": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_64": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_152": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_128": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_152": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_176": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_112": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_56": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_0": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_160": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_64": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_8": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_192": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_144": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_8": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_112": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_72": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_104": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_96": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_192": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_0": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_48": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_168": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_184": ["MIN", "MAX"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_64": ["MIN", "MAX"]}, "groupDnList": {"1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_24": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1354_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1488_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1448_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1468_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD164_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD184_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1018_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1408_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_160": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1004_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1034_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1414_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1424_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1444_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1474_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1494_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1171_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_72": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD101_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1138_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD84_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD168_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD104_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD134_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1168_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_168": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1191_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD421_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD441_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD488_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD24_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD158_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1128_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1114_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_80": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD464_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD478_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1448_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1498_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1418_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1461_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD448_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD414_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_176": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD478_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD411_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD471_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD491_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1004_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1038_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1001_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1428_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_112": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD121_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD141_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD158_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD198_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD8_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD14_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD34_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD118_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_80": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1391_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1034_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1171_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1174_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1348_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD174_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD424_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1364_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_184": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD421_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD408_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD454_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD488_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1421_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD451_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1474_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1488_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_88": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD411_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD491_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD48_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1008_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD61_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1021_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1134_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD64_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_56": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD91_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1138_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1198_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1168_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD41_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1354_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1324_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1374_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_192": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD108_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD24_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD88_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD21_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1158_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1174_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1048_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1171_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_176": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1058_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1184_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1024_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1181_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1198_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1324_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD198_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD448_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_16": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1128_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1151_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1171_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1188_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1108_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1148_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1374_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1394_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_88": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD158_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD88_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD171_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1458_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1474_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD21_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD488_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD408_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_104": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD198_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD178_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1144_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1164_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1104_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD121_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD141_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD161_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_8": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1478_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1498_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1438_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1401_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1421_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1441_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1461_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1481_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_176": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD44_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD74_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD128_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD144_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1178_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1144_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1141_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1111_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_88": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD198_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD118_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1371_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1324_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD131_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1358_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1388_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1374_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_184": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1131_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1321_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1388_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1308_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD61_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1021_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD134_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1431_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_32": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1021_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1034_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1058_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1361_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1431_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1471_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD171_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD424_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_160": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1338_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1301_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1318_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1348_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1331_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1194_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1114_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1134_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_0": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1048_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1011_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD414_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1008_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1031_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1028_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1458_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1418_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_80": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1154_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1101_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1151_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD4_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1321_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1184_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD181_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD184_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_40": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD471_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD484_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1451_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1454_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1468_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1494_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1194_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD44_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_176": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD38_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1014_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1128_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1141_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1034_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD424_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD438_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1408_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_168": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1144_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1164_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1398_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1351_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1444_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1464_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD161_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD458_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_128": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1011_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1051_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1144_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1164_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1104_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1478_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD474_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1444_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_96": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1118_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1198_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1024_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1131_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1351_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1388_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1184_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1331_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_48": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1001_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1004_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1304_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD164_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD4_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1131_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1148_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD61_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_72": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD8_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD451_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD438_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD471_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1394_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1018_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1361_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1048_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_120": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD54_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD138_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1124_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD94_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1328_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1308_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD74_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1031_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_16": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD188_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD111_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1058_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1078_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1301_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1038_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1018_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1468_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_104": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1118_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1138_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1158_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1178_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD434_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1051_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1401_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1121_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_144": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD108_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1301_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1314_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1338_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD151_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1354_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1394_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1491_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_88": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD484_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1341_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1344_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1438_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1454_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD401_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD81_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD404_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_8": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD131_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1134_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1114_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1174_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1321_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1378_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1341_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1318_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_192": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1418_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD64_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1434_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1498_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD131_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD118_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD464_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD461_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_96": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1484_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1181_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1071_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD18_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD431_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD434_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD498_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD34_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_80": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1028_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1011_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1041_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1401_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1044_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1078_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1014_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1451_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_192": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1491_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1414_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1478_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1494_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD191_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD441_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD444_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD428_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_0": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1338_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD188_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD84_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1154_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD171_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD151_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD111_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_136": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1071_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD498_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1484_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1401_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1438_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1404_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1441_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1481_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_0": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1398_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1341_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1194_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD191_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1361_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1321_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1358_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1381_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_112": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1101_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1324_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1161_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1138_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1118_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1198_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1158_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1178_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_160": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD174_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1111_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD71_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1158_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1178_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1188_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1344_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD51_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_8": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD171_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1338_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD151_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1378_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD131_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD168_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD148_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_24": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1454_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD471_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD484_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD444_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1414_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1494_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1411_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1451_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_32": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1044_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD428_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1428_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1414_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD431_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD444_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1411_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD468_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_168": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1334_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1364_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1394_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD124_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD144_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1471_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD58_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1108_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_112": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1141_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD414_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1031_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1071_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD454_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1011_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1421_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1441_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_64": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1318_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1398_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1378_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD74_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1424_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD141_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD121_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD128_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_48": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD484_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD404_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD441_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD401_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD108_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD24_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD44_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD54_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_24": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD138_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD54_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD94_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD154_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD114_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD178_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1121_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1158_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_152": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1381_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD491_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD451_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD38_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD408_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD428_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD438_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1054_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_96": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD154_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD468_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD481_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD11_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD51_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD134_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD31_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD71_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_16": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1358_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1301_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1398_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD128_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD108_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD191_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD168_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD148_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_128": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1384_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1004_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1371_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1161_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1044_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1164_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD468_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1368_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_144": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD418_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD458_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD434_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD481_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD441_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD401_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD28_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD101_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_184": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1054_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1051_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1384_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1191_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1381_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1411_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1368_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD194_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_152": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD68_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD28_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1151_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1028_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1121_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD81_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1314_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD154_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_32": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1428_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1491_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD428_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD404_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD468_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD431_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD128_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD104_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_120": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD51_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD91_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD71_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD31_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD494_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD474_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD4_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1028_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_24": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1381_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1361_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1038_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1058_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1018_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1041_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1078_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD404_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_104": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD114_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD68_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD88_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD48_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD28_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1121_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1304_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1141_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_56": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1124_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1144_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1114_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD458_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD188_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1311_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1331_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1444_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_16": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1391_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD28_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD48_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD68_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD51_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD8_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD88_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD121_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_120": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1451_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1471_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD444_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD464_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD484_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD424_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD21_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD41_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_32": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1124_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1118_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1344_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1368_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1198_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1164_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1311_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1351_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_64": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1184_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1371_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1391_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1378_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1484_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1404_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1424_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD181_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_128": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD111_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD64_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD78_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD148_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD124_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD188_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD164_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1134_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_144": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1444_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1484_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD481_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1478_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD441_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1481_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1371_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1191_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_40": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD44_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD41_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1318_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD84_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1044_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1111_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1114_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1041_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_40": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1411_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD84_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD81_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD41_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD191_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD151_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD111_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD481_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_48": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1001_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1004_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1154_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1151_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1181_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD194_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1324_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1384_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_136": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1048_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1024_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1364_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1388_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1458_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1434_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1474_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1498_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_96": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1008_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD48_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1118_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD64_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1024_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1021_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1328_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD94_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_152": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD68_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1028_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1178_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1138_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1191_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1364_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1334_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1314_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_136": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1304_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1328_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD194_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD154_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD178_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1344_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1414_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1454_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_48": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1051_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD478_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD11_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD91_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD494_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1014_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1024_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD418_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_24": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1448_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1468_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD104_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD124_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1014_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1038_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1001_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1381_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_40": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD194_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1304_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1328_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD161_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1384_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1161_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD454_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD14_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_72": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD438_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD188_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD8_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD418_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD498_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD431_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD451_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_136": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1148_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1108_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1131_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1174_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1378_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD191_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1188_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1341_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_168": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD58_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD51_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD71_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1034_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1141_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1188_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1108_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1014_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_80": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1051_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1418_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD414_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD448_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1448_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD411_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD478_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD464_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_120": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD494_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1441_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1461_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1181_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD474_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1071_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1421_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1481_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_160": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1344_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1394_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD81_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD124_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD144_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD154_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD174_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD38_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_56": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1304_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD91_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1374_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1354_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD4_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD134_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD164_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD184_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_144": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1478_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1438_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD114_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD138_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD428_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD14_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1494_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD34_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_88": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1464_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD494_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1431_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1461_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD491_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1434_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD461_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1498_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_136": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1404_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD401_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1401_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD418_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD434_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD498_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD474_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1441_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_128": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD91_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD434_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD414_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD4_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD454_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1008_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1418_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1031_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_32": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1341_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1194_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1191_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1188_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1301_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1151_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD444_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1041_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_16": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD164_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD144_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1374_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1354_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1488_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD98_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1408_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1428_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_56": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD448_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD401_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD411_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1044_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1434_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1464_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1454_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1404_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_184": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1331_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD171_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1194_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD174_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1361_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD141_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1318_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1364_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_40": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD464_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD421_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD461_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD34_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD118_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD168_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD131_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD138_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_104": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD31_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1001_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1038_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1021_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD404_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1041_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1078_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1018_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_72": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1181_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD31_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD78_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD18_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1071_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD98_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1161_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1101_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_120": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD118_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD138_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD101_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD8_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD181_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD158_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD34_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD14_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_64": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD31_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD78_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD114_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD18_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD11_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1101_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1148_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD98_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_152": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD64_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD74_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD148_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD14_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD178_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD94_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD44_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD141_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_128": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1184_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD54_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD74_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1011_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1054_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1031_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD458_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1438_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_152": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1134_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1104_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1404_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD161_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1154_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1358_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1054_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD478_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_176": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1121_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1128_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1488_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD451_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD454_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD488_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1424_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1474_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_112": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1058_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1411_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1448_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1468_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1428_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1488_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1431_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1408_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_56": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1358_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1308_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1328_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1104_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1321_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1124_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1154_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1174_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_0": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD104_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD21_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD98_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD41_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD61_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD124_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD144_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD78_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_160": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1338_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD94_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD168_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD181_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1464_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD101_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1484_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_64": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD481_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD461_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD468_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD81_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD61_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1104_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD98_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1308_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_8": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD38_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD58_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD18_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1168_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1314_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1131_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1334_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1111_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_192": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1458_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1424_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1391_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD24_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD21_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD88_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1048_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1171_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_144": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD448_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD174_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD198_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD408_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD488_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD128_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD111_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD158_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_8": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1111_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1128_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1148_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1168_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD184_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1314_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1334_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1078_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_112": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1124_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1368_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1331_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1388_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1311_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1348_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1308_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1351_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_72": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1161_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1168_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1054_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1431_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1434_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1464_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD461_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD494_3_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_104": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1371_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1311_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1391_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1348_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD161_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD181_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1368_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD178_1_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_96": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD421_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1481_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD48_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD11_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD28_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1461_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD68_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1101_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_192": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1348_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1398_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1334_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD471_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD474_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD458_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD58_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD71_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_0": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD38_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1491_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD18_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD58_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1108_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD78_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1451_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1471_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_48": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD84_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD54_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD101_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD108_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD24_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD121_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD151_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1311_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_168": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD491_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD148_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD498_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD418_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD431_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD411_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1008_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1351_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_184": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1458_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1421_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1471_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1408_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD408_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD438_1_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD424_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD421_2_1"], "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_64": ["qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1384_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD104_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD194_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD134_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD114_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD184_3_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD1491_2_1", "qwe_asd_Container_qwe_asd_docker_name_chartDockerPOD_chartPOD11_2_1"]}, "groupIndexList": {"1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_24": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_160": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_72": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_168": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_80": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_176": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_112": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_80": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_184": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_88": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_56": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_192": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_176": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_16": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_88": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_104": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_8": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_176": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_88": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_184": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_32": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_160": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_0": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_80": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_40": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_176": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_168": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_128": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_96": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_48": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_72": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_120": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_16": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_104": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_144": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_88": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_8": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_192": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_96": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_80": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_192": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_0": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_136": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_0": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_112": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_160": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_8": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_24": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_32": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_168": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_112": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_64": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_48": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_24": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_152": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_96": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_16": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_128": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_144": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_184": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_152": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_32": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_120": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_24": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_104": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_56": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_16": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_120": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_32": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_64": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_128": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_144": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_40": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_40": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_48": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_136": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_96": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_152": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_136": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_48": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_24": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_40": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_72": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_136": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_168": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_80": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_120": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_160": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_56": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_144": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_88": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_136": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_128": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_32": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_16": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_56": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_184": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_40": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_104": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_72": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_120": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_64": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_152": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_128": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_152": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_176": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_112": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_56": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_0": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_160": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_64": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_8": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_192": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_144": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_8": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_112": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_72": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_104": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_96": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_192": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_0": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_48": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_168": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_184": "hwCCRInit", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_64": "hwCCRInit"}, "groupMeasObjList": {"1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_24": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_160": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_72": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_168": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_80": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_176": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_112": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_80": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_184": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_88": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_56": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_192": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_176": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_16": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_88": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_104": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_8": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_176": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_88": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_184": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_32": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_160": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_0": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_80": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_40": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_176": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_168": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_128": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_96": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_48": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_72": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_120": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_16": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_104": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_144": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_88": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_8": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_192": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_96": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_80": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_192": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_0": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_136": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_0": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_112": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_160": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_8": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_24": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_32": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_168": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_112": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_64": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_48": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_24": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_152": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_96": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_16": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_128": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_144": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_184": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_152": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_32": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_120": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_24": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_104": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_56": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_16": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_120": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_32": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_64": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_128": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_144": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_40": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_40": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_48": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_136": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_96": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_152": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_136": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_48": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_24": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_40": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_72": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_136": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_168": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_80": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_120": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_160": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_56": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_144": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_88": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_136": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_128": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_32": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_16": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_56": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_184": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_40": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_104": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_72": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_120": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_64": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_152": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_128": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_152": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_176": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_112": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_56": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_0": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_160": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_64": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_8": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_192": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_144": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_8": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_112": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_72": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_104": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_96": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event3hwCBPNodeID=ID1hwServiceType=Type1__group_1_subgroup_192": "网络设备=Net1, 事件类型=Event3, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_0": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_48": "网络设备=Net1, 事件类型=Event4, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_4_subgroup_168": "网络设备=Net2, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net2hwEventType=Event5hwCBPNodeID=ID1hwServiceType=Type1__group_2_subgroup_184": "网络设备=Net2, 事件类型=Event5, CBP节点ID=ID1, 事件的源实际类型=Type1", "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event2hwCBPNodeID=ID1hwServiceType=Type1__group_0_subgroup_64": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1"}, "measUnitKey": "hwBillingDCCMsgNumberV2", "moType": "mpaas.momgr.MPAAS.DCCluster.adptapp"}}]}, "response": {"status": 200, "jsonBody": {"resultCode": 0, "result": {"totalPage": 0, "resultData": null, "groupQueryData": null, "startTime": 1740106800000, "endTime": 1740110400000, "queryTime": 1740131503635, "timeDisplayInfos": null, "yAxisScaleCount": 0, "groupAggrQueryData": {"1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_24": [{"dn": null, "neName": null, "displayValue": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "indexId": 10823, "indexName": "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_24_MIN", "indexUnit": null, "oper": null, "thresholdList": [], "indexValues": [{"timestamp": 1740106800000, "timestampStr": null, "indexValue": "15", "breakpoint": false}, {"timestamp": 1740106860000, "timestampStr": null, "indexValue": "36", "breakpoint": false}, {"timestamp": 1740106920000, "timestampStr": null, "indexValue": "13", "breakpoint": false}, {"timestamp": 1740106980000, "timestampStr": null, "indexValue": "21", "breakpoint": false}, {"timestamp": 1740107040000, "timestampStr": null, "indexValue": "18", "breakpoint": false}, {"timestamp": 1740107100000, "timestampStr": null, "indexValue": "23", "breakpoint": false}, {"timestamp": 1740107160000, "timestampStr": null, "indexValue": "5", "breakpoint": false}, {"timestamp": 1740107220000, "timestampStr": null, "indexValue": "4", "breakpoint": false}, {"timestamp": 1740107280000, "timestampStr": null, "indexValue": "28", "breakpoint": false}, {"timestamp": 1740107340000, "timestampStr": null, "indexValue": "6", "breakpoint": false}, {"timestamp": 1740107400000, "timestampStr": null, "indexValue": "13", "breakpoint": false}, {"timestamp": 1740107460000, "timestampStr": null, "indexValue": "3", "breakpoint": false}, {"timestamp": 1740107520000, "timestampStr": null, "indexValue": "3", "breakpoint": false}, {"timestamp": 1740107580000, "timestampStr": null, "indexValue": "24", "breakpoint": false}, {"timestamp": 1740107640000, "timestampStr": null, "indexValue": "9", "breakpoint": false}, {"timestamp": 1740107700000, "timestampStr": null, "indexValue": "6", "breakpoint": false}, {"timestamp": 1740107760000, "timestampStr": null, "indexValue": "5", "breakpoint": false}, {"timestamp": 1740107820000, "timestampStr": null, "indexValue": "7", "breakpoint": false}, {"timestamp": 1740107880000, "timestampStr": null, "indexValue": "1", "breakpoint": false}, {"timestamp": 1740107940000, "timestampStr": null, "indexValue": "28", "breakpoint": false}, {"timestamp": 1740108000000, "timestampStr": null, "indexValue": "9", "breakpoint": false}, {"timestamp": 1740108060000, "timestampStr": null, "indexValue": "14", "breakpoint": false}, {"timestamp": 1740108120000, "timestampStr": null, "indexValue": "10", "breakpoint": false}, {"timestamp": 1740108180000, "timestampStr": null, "indexValue": "10", "breakpoint": false}, {"timestamp": 1740108240000, "timestampStr": null, "indexValue": "12", "breakpoint": false}, {"timestamp": 1740108300000, "timestampStr": null, "indexValue": "11", "breakpoint": false}, {"timestamp": 1740108360000, "timestampStr": null, "indexValue": "4", "breakpoint": false}, {"timestamp": 1740108420000, "timestampStr": null, "indexValue": "18", "breakpoint": false}, {"timestamp": 1740108480000, "timestampStr": null, "indexValue": "9", "breakpoint": false}, {"timestamp": 1740108540000, "timestampStr": null, "indexValue": "2", "breakpoint": false}, {"timestamp": 1740108600000, "timestampStr": null, "indexValue": "23", "breakpoint": false}, {"timestamp": 1740108660000, "timestampStr": null, "indexValue": "23", "breakpoint": false}, {"timestamp": 1740108720000, "timestampStr": null, "indexValue": "20", "breakpoint": false}, {"timestamp": 1740108780000, "timestampStr": null, "indexValue": "10", "breakpoint": false}, {"timestamp": 1740108840000, "timestampStr": null, "indexValue": "2", "breakpoint": false}, {"timestamp": 1740108900000, "timestampStr": null, "indexValue": "2", "breakpoint": false}, {"timestamp": 1740108960000, "timestampStr": null, "indexValue": "44", "breakpoint": false}, {"timestamp": 1740109020000, "timestampStr": null, "indexValue": "6", "breakpoint": false}, {"timestamp": 1740109080000, "timestampStr": null, "indexValue": "1", "breakpoint": false}, {"timestamp": 1740109140000, "timestampStr": null, "indexValue": "13", "breakpoint": false}, {"timestamp": 1740109200000, "timestampStr": null, "indexValue": "3", "breakpoint": false}, {"timestamp": 1740109260000, "timestampStr": null, "indexValue": "20", "breakpoint": false}, {"timestamp": 1740109320000, "timestampStr": null, "indexValue": "34", "breakpoint": false}, {"timestamp": 1740109380000, "timestampStr": null, "indexValue": "19", "breakpoint": false}, {"timestamp": 1740109440000, "timestampStr": null, "indexValue": "2", "breakpoint": false}, {"timestamp": 1740109500000, "timestampStr": null, "indexValue": "3", "breakpoint": false}, {"timestamp": 1740109560000, "timestampStr": null, "indexValue": "14", "breakpoint": false}, {"timestamp": 1740109620000, "timestampStr": null, "indexValue": "26", "breakpoint": false}, {"timestamp": 1740109680000, "timestampStr": null, "indexValue": "4", "breakpoint": false}, {"timestamp": 1740109740000, "timestampStr": null, "indexValue": "19", "breakpoint": false}, {"timestamp": 1740109800000, "timestampStr": null, "indexValue": "26", "breakpoint": false}, {"timestamp": 1740109860000, "timestampStr": null, "indexValue": "6", "breakpoint": false}, {"timestamp": 1740109920000, "timestampStr": null, "indexValue": "3", "breakpoint": false}, {"timestamp": 1740109980000, "timestampStr": null, "indexValue": "19", "breakpoint": false}, {"timestamp": 1740110040000, "timestampStr": null, "indexValue": "10", "breakpoint": false}, {"timestamp": 1740110100000, "timestampStr": null, "indexValue": "17", "breakpoint": false}, {"timestamp": 1740110160000, "timestampStr": null, "indexValue": "9", "breakpoint": false}, {"timestamp": 1740110220000, "timestampStr": null, "indexValue": "13", "breakpoint": false}, {"timestamp": 1740110280000, "timestampStr": null, "indexValue": "31", "breakpoint": false}, {"timestamp": 1740110340000, "timestampStr": null, "indexValue": "37", "breakpoint": false}, {"timestamp": 1740110400000, "timestampStr": null, "indexValue": "7", "breakpoint": false}], "resourceType": null, "indexGroup": "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_24", "displayRate": 0, "indexKey": null, "successRate": 0.0}, {"dn": null, "neName": null, "displayValue": "网络设备=Net1, 事件类型=Event2, CBP节点ID=ID1, 事件的源实际类型=Type1", "indexId": 10823, "indexName": "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_24_MAX", "indexUnit": null, "oper": null, "thresholdList": [], "indexValues": [{"timestamp": 1740106800000, "timestampStr": null, "indexValue": "93", "breakpoint": false}, {"timestamp": 1740106860000, "timestampStr": null, "indexValue": "84", "breakpoint": false}, {"timestamp": 1740106920000, "timestampStr": null, "indexValue": "89", "breakpoint": false}, {"timestamp": 1740106980000, "timestampStr": null, "indexValue": "100", "breakpoint": false}, {"timestamp": 1740107040000, "timestampStr": null, "indexValue": "100", "breakpoint": false}, {"timestamp": 1740107100000, "timestampStr": null, "indexValue": "85", "breakpoint": false}, {"timestamp": 1740107160000, "timestampStr": null, "indexValue": "50", "breakpoint": false}, {"timestamp": 1740107220000, "timestampStr": null, "indexValue": "97", "breakpoint": false}, {"timestamp": 1740107280000, "timestampStr": null, "indexValue": "86", "breakpoint": false}, {"timestamp": 1740107340000, "timestampStr": null, "indexValue": "93", "breakpoint": false}, {"timestamp": 1740107400000, "timestampStr": null, "indexValue": "91", "breakpoint": false}, {"timestamp": 1740107460000, "timestampStr": null, "indexValue": "100", "breakpoint": false}, {"timestamp": 1740107520000, "timestampStr": null, "indexValue": "66", "breakpoint": false}, {"timestamp": 1740107580000, "timestampStr": null, "indexValue": "99", "breakpoint": false}, {"timestamp": 1740107640000, "timestampStr": null, "indexValue": "96", "breakpoint": false}, {"timestamp": 1740107700000, "timestampStr": null, "indexValue": "97", "breakpoint": false}, {"timestamp": 1740107760000, "timestampStr": null, "indexValue": "98", "breakpoint": false}, {"timestamp": 1740107820000, "timestampStr": null, "indexValue": "92", "breakpoint": false}, {"timestamp": 1740107880000, "timestampStr": null, "indexValue": "94", "breakpoint": false}, {"timestamp": 1740107940000, "timestampStr": null, "indexValue": "100", "breakpoint": false}, {"timestamp": 1740108000000, "timestampStr": null, "indexValue": "86", "breakpoint": false}, {"timestamp": 1740108060000, "timestampStr": null, "indexValue": "83", "breakpoint": false}, {"timestamp": 1740108120000, "timestampStr": null, "indexValue": "96", "breakpoint": false}, {"timestamp": 1740108180000, "timestampStr": null, "indexValue": "100", "breakpoint": false}, {"timestamp": 1740108240000, "timestampStr": null, "indexValue": "62", "breakpoint": false}, {"timestamp": 1740108300000, "timestampStr": null, "indexValue": "99", "breakpoint": false}, {"timestamp": 1740108360000, "timestampStr": null, "indexValue": "92", "breakpoint": false}, {"timestamp": 1740108420000, "timestampStr": null, "indexValue": "88", "breakpoint": false}, {"timestamp": 1740108480000, "timestampStr": null, "indexValue": "62", "breakpoint": false}, {"timestamp": 1740108540000, "timestampStr": null, "indexValue": "98", "breakpoint": false}, {"timestamp": 1740108600000, "timestampStr": null, "indexValue": "89", "breakpoint": false}, {"timestamp": 1740108660000, "timestampStr": null, "indexValue": "81", "breakpoint": false}, {"timestamp": 1740108720000, "timestampStr": null, "indexValue": "72", "breakpoint": false}, {"timestamp": 1740108780000, "timestampStr": null, "indexValue": "94", "breakpoint": false}, {"timestamp": 1740108840000, "timestampStr": null, "indexValue": "90", "breakpoint": false}, {"timestamp": 1740108900000, "timestampStr": null, "indexValue": "79", "breakpoint": false}, {"timestamp": 1740108960000, "timestampStr": null, "indexValue": "81", "breakpoint": false}, {"timestamp": 1740109020000, "timestampStr": null, "indexValue": "75", "breakpoint": false}, {"timestamp": 1740109080000, "timestampStr": null, "indexValue": "88", "breakpoint": false}, {"timestamp": 1740109140000, "timestampStr": null, "indexValue": "92", "breakpoint": false}, {"timestamp": 1740109200000, "timestampStr": null, "indexValue": "90", "breakpoint": false}, {"timestamp": 1740109260000, "timestampStr": null, "indexValue": "100", "breakpoint": false}, {"timestamp": 1740109320000, "timestampStr": null, "indexValue": "88", "breakpoint": false}, {"timestamp": 1740109380000, "timestampStr": null, "indexValue": "76", "breakpoint": false}, {"timestamp": 1740109440000, "timestampStr": null, "indexValue": "83", "breakpoint": false}, {"timestamp": 1740109500000, "timestampStr": null, "indexValue": "93", "breakpoint": false}, {"timestamp": 1740109560000, "timestampStr": null, "indexValue": "93", "breakpoint": false}, {"timestamp": 1740109620000, "timestampStr": null, "indexValue": "89", "breakpoint": false}, {"timestamp": 1740109680000, "timestampStr": null, "indexValue": "84", "breakpoint": false}, {"timestamp": 1740109740000, "timestampStr": null, "indexValue": "81", "breakpoint": false}, {"timestamp": 1740109800000, "timestampStr": null, "indexValue": "82", "breakpoint": false}, {"timestamp": 1740109860000, "timestampStr": null, "indexValue": "88", "breakpoint": false}, {"timestamp": 1740109920000, "timestampStr": null, "indexValue": "75", "breakpoint": false}, {"timestamp": 1740109980000, "timestampStr": null, "indexValue": "100", "breakpoint": false}, {"timestamp": 1740110040000, "timestampStr": null, "indexValue": "73", "breakpoint": false}, {"timestamp": 1740110100000, "timestampStr": null, "indexValue": "70", "breakpoint": false}, {"timestamp": 1740110160000, "timestampStr": null, "indexValue": "64", "breakpoint": false}, {"timestamp": 1740110220000, "timestampStr": null, "indexValue": "85", "breakpoint": false}, {"timestamp": 1740110280000, "timestampStr": null, "indexValue": "75", "breakpoint": false}, {"timestamp": 1740110340000, "timestampStr": null, "indexValue": "94", "breakpoint": false}, {"timestamp": 1740110400000, "timestampStr": null, "indexValue": "78", "breakpoint": false}], "resourceType": null, "indexGroup": "1_mpaas.momgr.MPAAS.DCCluster.adptapp_hwBillingDCCMsgNumberV2_hwCCRInit_hwNetDevice=Net1hwEventType=Event4hwCBPNodeID=ID1hwServiceType=Type1__group_3_subgroup_24", "displayRate": 0, "indexKey": null, "successRate": 0.0}]}}, "resultDesc": null}}}]}