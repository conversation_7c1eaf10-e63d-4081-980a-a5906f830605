<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.mapper.AlarmCompressMapper">
    <resultMap type="com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.AlarmIncident"
               id="alarmIncidentEntity">
        <result column="ID" property="incidentId" javaType="String" jdbcType="VARCHAR"/>
        <result column="START_TIME" property="startTime" javaType="long" jdbcType="BIGINT"/>
        <result column="END_TIME" property="endTime" javaType="long" jdbcType="BIGINT"/>
        <result column="ALARM_NUM" property="alarmNum" javaType="int" jdbcType="INTEGER"/>
        <result column="INCIDENT_LEVEL" property="level" javaType="int" jdbcType="INTEGER"/>
        <result column="DESCRIPTION" property="description" javaType="String" jdbcType="VARCHAR"/>
        <result column="INCIDENT_ROOT_ID" property="incidentRootId" javaType="String" jdbcType="VARCHAR"/>
        <result column="DECLINE" property="decline" javaType="boolean" jdbcType="BOOLEAN"/>
        <result column="LATEST_ALARM_OCC_TIME" property="latestAlarmOccTime" javaType="long" jdbcType="BIGINT"/>
        <result column="LATEST_ALARM_CLEAN_TIME" property="latestAlarmCleanTime" javaType="long" jdbcType="BIGINT"/>
        <result column="ROOT_EVENT_ID" property="rootEventId" javaType="int" jdbcType="INTEGER"/>
        <result column="SUMMARY" property="summary" javaType="String" jdbcType="VARCHAR"/>
        <result column="DIAGNOSIS_STATE" property="diagnosisState" javaType="int" jdbcType="INTEGER"/>
        <result column="GRAPH_DATA" property="graphData" javaType="String" jdbcType="CLOB"/>
        <result column="NEED_DIAGNOSIS" property="needDiagnosis" javaType="boolean" jdbcType="BOOLEAN"/>
        <result column="NUMBER_EXCEED_ALARM_SENT" property="numberExceedAlarmSent" javaType="boolean" jdbcType="BOOLEAN"/>
        <result column="TIME_EXCEED_ALARM_SENT" property="timeExceedAlarmSent" javaType="boolean" jdbcType="BOOLEAN"/>
        <result column="COMPRESS_GROUP_RULE" property="compressGroupRule" javaType="int" jdbcType="INTEGER"/>
        <result column="COMPRESS_LEVEL_GROUP" property="compressLevelGroup" javaType="String" jdbcType="VARCHAR"/>
        <result column="COMPRESS_LEVEL_DN" property="compressLevelDn" javaType="String" jdbcType="VARCHAR"/>
        <result column="UNCLEAR_ALARM_NUM" property="unclearAlarmNum" javaType="int" jdbcType="INTEGER"/>
        <result column="CSN" property="csn" javaType="int" jdbcType="INTEGER"/>
        <collection property="records" ofType="com.huawei.i2000.dvanalysisengineservice.model.OperateRecord">
            <result column="RECORD_ID" property="id" javaType="int" jdbcType="INTEGER" />
            <result column="INCIDENT_ID" property="incidentId" javaType="String" jdbcType="VARCHAR" />
            <result column="TIME" property="time" javaType="long" jdbcType="BIGINT" />
            <result column="DETAIL" property="detail" javaType="String" jdbcType="VARCHAR" />
            <result column="STATUS" property="status" javaType="int" jdbcType="INTEGER" />
        </collection>
    </resultMap>

    <resultMap type="com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.AlarmAnalysisResult"
               id="alarmResultInfo">
        <id column="ID" property="alarmSeriesNumber" javaType="int" jdbcType="INTEGER"/>
        <result column="INCIDENT_ID" property="incidentId" javaType="String" jdbcType="VARCHAR"/>
        <result column="ALARM_ID" property="alarmId" javaType="String" jdbcType="VARCHAR"/>
        <result column="ALARM_NAME" property="alarmName" javaType="String" jdbcType="VARCHAR"/>
        <result column="START_TIME" property="startTime" javaType="long" jdbcType="BIGINT"/>
        <result column="END_TIME" property="endTime" javaType="long" jdbcType="BIGINT"/>
        <result column="ACK_TIME" property="ackTime" javaType="Long" jdbcType="BIGINT"/>
        <result column="ALARM_LEVEL" property="level" javaType="int" jdbcType="NUMERIC"/>
        <result column="ALARM_TYPE" property="alarmType" javaType="int" jdbcType="INTEGER"/>
        <result column="ALARM_SOURCE_TYPE" property="alarmSourceType" javaType="String" jdbcType="VARCHAR"/>
        <result column="PROBABLE_CAUSE" property="probableCause" javaType="String" jdbcType="VARCHAR"/>
        <result column="LATEST_UPDATE_TIME" property="latestUpdateTime" javaType="long" jdbcType="BIGINT"/>
        <result column="ALARM_SOURCE" property="alarmSource" javaType="String" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.Event" id="eventResultMap">
        <id column="ID" property="id" javaType="Long" jdbcType="INTEGER"/>
        <result column="INCIDENT_ID" property="incidentId" javaType="String" jdbcType="VARCHAR"/>
        <result column="ALARM_NAME" property="name" javaType="String" jdbcType="VARCHAR"/>
        <result column="ALARM_ID" property="alarmId" javaType="String" jdbcType="VARCHAR"/>
        <result column="EVENT_SOURCE_TYPE" property="type" javaType="int" jdbcType="INTEGER"/>
        <result column="CSN" property="csn" javaType="int" jdbcType="INTEGER"/>
        <result column="ALARM_LEVEL" property="severity" javaType="int" jdbcType="NUMERIC"/>
        <result column="START_TIME" property="occurTime" javaType="long" jdbcType="BIGINT"/>
        <result column="END_TIME" property="clearTime" javaType="long" jdbcType="BIGINT"/>
        <result column="LATEST_UPDATE_TIME" property="updateTime" javaType="long" jdbcType="BIGINT"/>
        <result column="PROBABLE_CAUSE" property="probableCause" javaType="String" jdbcType="VARCHAR"/>
        <result column="ALARM_TYPE" property="alarmType" javaType="int" jdbcType="INTEGER"/>
        <result column="ALARM_SOURCE_TYPE" property="neType" javaType="String" jdbcType="VARCHAR"/>
        <result column="ALARM_SOURCE" property="neDn" javaType="String" jdbcType="VARCHAR"/>
        <result column="ACK_TIME" property="ackTime" javaType="Long" jdbcType="BIGINT"/>
        <result column="ALARM_GROUP_ID" property="alarmGroupId" javaType="String" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="queryUndeclineEvents" resultMap="alarmIncidentEntity" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select * from ^{tableName} where DECLINE = false
    </select>

    <update id="updateClearAlarm" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        BEGIN
        <foreach collection="list" item="item" index="index" separator="">
            update ^{tableName}
            set END_TIME =#{item.clearTime},
            LATEST_UPDATE_TIME=#{item.updateTime},
            CLEAR_ARRIVAL_TIME=#{item.clearArrivalTime}
            where EVENT_SOURCE_TYPE = #{item.type} and CSN=#{item.csn};
        </foreach>
        END
    </update>

    <select id="queryAlarmByCsn" resultMap="eventResultMap" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select ID, INCIDENT_ID, EVENT_SOURCE_TYPE, CSN from ^{tableName} where EVENT_SOURCE_TYPE = #{type} and CSN in
        <foreach item="csn" collection="alarmSeriesNumbers" open="(" separator="," close=")">
            #{csn}
        </foreach>
    </select>

    <select id="queryIncidentById" resultMap="alarmIncidentEntity" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select * from ^{tableName} where ID in
        <foreach item="id" collection="incidentIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="queryUnOpenedIncidentById" resultMap="alarmIncidentEntity" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select * from TBL_AIOPS_ALARMANALYSIS_INCIDENT_^{taskId} where END_TIME = 0
    </select>

    <select id="getUnOpenedAlarmCountByIncident" resultType="java.util.Map" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select i.ID,count(a.ID) as ALARM_COUNT from TBL_AIOPS_ALARMANALYSIS_INCIDENT_^{taskId} i
        left join TBL_AIOPS_ALARMANALYSIS_RESULT_^{taskId} a on i.ID = a.INCIDENT_ID
        where i.END_TIME = 0 AND a.END_TIME = 0 group by i.ID
    </select>

    <insert id="insertAlarmResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        insert into ^{tableName} (ID, INCIDENT_ID, ALARM_ID, ALARM_NAME, START_TIME, END_TIME,
        ALARM_LEVEL, ALARM_TYPE, ALARM_SOURCE_TYPE, PROBABLE_CAUSE, LATEST_UPDATE_TIME, ALARM_SOURCE, EVENT_SOURCE_TYPE, CSN, NEW_ARRIVAL_TIME,ALARM_GROUP_ID)
        values
        <foreach collection="events" index="index" item="event" separator=",">
            (#{event.id}, #{event.incidentId}, #{event.alarmId}, #{event.name}, #{event.occurTime}, #{event.clearTime},
            #{event.severity}, #{event.alarmType}, #{event.neType}, #{event.probableCause}, #{event.updateTime},
            #{event.neDn}, #{event.type}, #{event.csn}, #{event.newArrivalTime}, #{event.alarmGroupId})
        </foreach>
        ON DUPLICATE key update
        INCIDENT_ID=VALUES(INCIDENT_ID),
        ALARM_ID=VALUES(ALARM_ID),
        ALARM_NAME=VALUES(ALARM_NAME),
        START_TIME=VALUES(START_TIME),
        END_TIME=VALUES(END_TIME),
        ALARM_LEVEL=VALUES(ALARM_LEVEL),
        ALARM_TYPE=VALUES(ALARM_TYPE),
        ALARM_SOURCE_TYPE=VALUES(ALARM_SOURCE_TYPE),
        PROBABLE_CAUSE=VALUES(PROBABLE_CAUSE),
        LATEST_UPDATE_TIME=VALUES(LATEST_UPDATE_TIME),
        ALARM_SOURCE=VALUES(ALARM_SOURCE)
    </insert>

    <select id="getIncidentEndStatus" resultType="java.lang.String" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select distinct INCIDENT_ID from TBL_AIOPS_ALARMANALYSIS_RESULT_^{taskId}
        where END_TIME = 0 and INCIDENT_ID IN
        <foreach item="item" index="index" collection="incidentId" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertOrUpdateCompressAlarmIncident" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        replace into ^{tableName} (ID, START_TIME, END_TIME, ALARM_NUM, INCIDENT_LEVEL, DESCRIPTION,
        INCIDENT_ROOT_ID, DECLINE, LATEST_ALARM_OCC_TIME, LATEST_ALARM_CLEAN_TIME, NUMBER_EXCEED_ALARM_SENT, TIME_EXCEED_ALARM_SENT, COMPRESS_GROUP_RULE, UNCLEAR_ALARM_NUM, CSN,COMPRESS_LEVEL_GROUP,COMPRESS_LEVEL_DN)
        values
        <foreach collection="alarmIncidents" index="index" item="alarmIncident" separator=",">
            (#{alarmIncident.incidentId}, #{alarmIncident.startTime}, #{alarmIncident.endTime},
            #{alarmIncident.alarmNum}, #{alarmIncident.level}, #{alarmIncident.description},
            #{alarmIncident.incidentRootId}, #{alarmIncident.decline},
            #{alarmIncident.latestAlarmOccTime},#{alarmIncident.latestAlarmCleanTime},#{alarmIncident.numberExceedAlarmSent},#{alarmIncident.timeExceedAlarmSent},#{alarmIncident.compressGroupRule},#{alarmIncident.unclearAlarmNum},#{alarmIncident.csn},#{alarmIncident.compressLevelGroup},#{alarmIncident.compressLevelDn})
        </foreach>
    </insert>

    <select id="queryCompressAlarmNotEnded" resultType="Integer" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select CSN from ^{tableName} where END_TIME = 0 and EVENT_SOURCE_TYPE = 1
        ORDER BY CSN LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="queryAlarmInTable" resultType="Integer" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select CSN from ^{tableName} where END_TIME = 0
           and CSN in
        <foreach item="id" collection="csns" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <update id="updateIncidentDecline" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        update ^{tableName}
        set DECLINE = true
    </update>
    
    <update id="updateEndIncidentEndTime" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        UPDATE TBL_AIOPS_ALARMANALYSIS_INCIDENT_^{taskId}
        SET END_TIME = GREATEST(#{time}, LATEST_ALARM_CLEAN_TIME)
        WHERE ID in
        <foreach item="incidentId" collection="incidentIds" open="(" separator="," close=")">
            #{incidentId}
        </foreach>
    </update>

    <select id="selectEndIncidentEndTime" resultMap="alarmIncidentEntity" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        SELECT * from TBL_AIOPS_ALARMANALYSIS_INCIDENT_^{taskId} i
        WHERE i.DECLINE = true
          AND i.END_TIME = 0
          AND NOT EXISTS (
            SELECT 1
            FROM TBL_AIOPS_ALARMANALYSIS_RESULT_^{taskId} a
            WHERE a.INCIDENT_ID  = i.ID
          AND a.END_TIME = 0
            )
    </select>

    <select id="getAllAlarmCountByTask" resultType="Integer" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver" timeout="120">
        select COUNT(*) from TBL_AIOPS_ALARMANALYSIS_RESULT_^{taskId}
    </select>

    <select id="getAlarmCountGroupByIncident" resultType="java.util.Map" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select i.ID,count(a.ID) as ALARM_COUNT from TBL_AIOPS_ALARMANALYSIS_INCIDENT_^{taskId} i
        join TBL_AIOPS_ALARMANALYSIS_RESULT_^{taskId} a on i.ID = a.INCIDENT_ID
        where i.END_TIME != 0 group by i.ID,i.END_TIME order by i.END_TIME ASC
    </select>

    <delete id="deleteIncidentById" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver" timeout="120">
        delete from TBL_AIOPS_ALARMANALYSIS_INCIDENT_^{taskId} WHERE ID in
        <foreach collection="incidentIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteOperateRecordByIncidentId" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver" timeout="120">
        delete from TBL_AIOPS_ALARMANALYSIS_OPERATE_RECORD_^{taskId} WHERE INCIDENT_ID in
        <foreach collection="incidentIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteRepairAdviceByIncidentId" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver" timeout="120">
        delete from TBL_AIOPS_ALARMANALYSIS_REPAIR_ADVICE_^{taskId} WHERE INCIDENT_ID in
        <foreach collection="incidentIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteAlarmById" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver" timeout="120">
        delete from TBL_AIOPS_ALARMANALYSIS_RESULT_^{taskId} WHERE INCIDENT_ID in
        <foreach collection="incidentIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteAlarmByEndTime" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver" timeout="120">
        delete from TBL_AIOPS_ALARMANALYSIS_RESULT_^{taskId}
        where END_TIME != 0
        order by END_TIME asc
        LIMIT #{num}
    </delete>

    <update id="upgradeTable" timeout="120">
        DECLARE
        v_taskId VARCHAR2(255);
        BEGIN
            v_taskId := #{taskId};

        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_RESULT_' || v_taskId || ' ADD EVENT_SOURCE_TYPE INTEGER DEFAULT 1';
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_RESULT_' || v_taskId || ' ADD CSN INTEGER';
        EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_ALARMANALYSIS_RESULT_' || v_taskId || ' SET CSN = ID';
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_RESULT_' || v_taskId || ' ADD CONSTRAINT UNIQUE_TYPE_CSN UNIQUE (EVENT_SOURCE_TYPE, CSN)';
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_INCIDENT_' || v_taskId || ' ADD ROOT_EVENT_ID INTEGER';
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_INCIDENT_' || v_taskId || ' ADD SUMMARY VARCHAR(1024)';
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_INCIDENT_' || v_taskId || ' ADD DIAGNOSIS_STATE INTEGER DEFAULT 0';
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_INCIDENT_' || v_taskId || ' ADD DIAGNOSIS_TIME BIGINT DEFAULT 0';
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_INCIDENT_' || v_taskId || ' ADD GRAPH_DATA CLOB';
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_INCIDENT_' || v_taskId || ' ADD NEED_DIAGNOSIS BOOLEAN DEFAULT false';
        END;
    </update>

    <update id="addAlarmField" timeout="120">
        DECLARE
        V_CMT NUMBER;
        v_taskId VARCHAR2(255);
        BEGIN
        v_taskId := #{taskId};
        SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALARMANALYSIS_RESULT_'||v_taskId) AND COLUMN_NAME = UPPER('CLEAR_ARRIVAL_TIME');
        IF V_CMT = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_RESULT_' || v_taskId || ' ADD CLEAR_ARRIVAL_TIME BIGINT';
        END IF;
        SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALARMANALYSIS_RESULT_'||v_taskId) AND COLUMN_NAME = UPPER('NEW_ARRIVAL_TIME');
        IF V_CMT = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_RESULT_' || v_taskId || ' ADD NEW_ARRIVAL_TIME BIGINT';
        END IF;
        SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALARMANALYSIS_INCIDENT_'||v_taskId) AND COLUMN_NAME = UPPER('NUMBER_EXCEED_ALARM_SENT');
        IF V_CMT = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_INCIDENT_' || v_taskId || ' ADD NUMBER_EXCEED_ALARM_SENT BOOLEAN DEFAULT false';
        END IF;
        SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALARMANALYSIS_INCIDENT_'||v_taskId) AND COLUMN_NAME = UPPER('TIME_EXCEED_ALARM_SENT');
        IF V_CMT = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_INCIDENT_' || v_taskId || ' ADD TIME_EXCEED_ALARM_SENT BOOLEAN DEFAULT false';
        END IF;
        SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALARMANALYSIS_RESULT_'||v_taskId) AND COLUMN_NAME = UPPER('AGGR_STATUS');
        IF V_CMT = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_RESULT_' || v_taskId || ' ADD AGGR_STATUS BOOLEAN DEFAULT FALSE';
        END IF;
        SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALARMANALYSIS_INCIDENT_'||v_taskId) AND COLUMN_NAME = UPPER('COMPRESS_GROUP_RULE');
        IF V_CMT = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_INCIDENT_' || v_taskId || ' ADD COMPRESS_GROUP_RULE INTEGER';
        END IF;
        SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALARMANALYSIS_INCIDENT_'||v_taskId) AND COLUMN_NAME = UPPER('COMPRESS_LEVEL_GROUP');
        IF V_CMT = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_INCIDENT_' || v_taskId || ' ADD COMPRESS_LEVEL_GROUP VARCHAR(1024)';
        END IF;
        SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALARMANALYSIS_INCIDENT_'||v_taskId) AND COLUMN_NAME = UPPER('COMPRESS_LEVEL_DN');
        IF V_CMT = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_INCIDENT_' || v_taskId || ' ADD COMPRESS_LEVEL_DN VARCHAR(1024)';
        END IF;
        SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALARMANALYSIS_RESULT_'||v_taskId) AND COLUMN_NAME = UPPER('ALARM_GROUP_ID');
        IF V_CMT = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_RESULT_' || v_taskId || ' ADD ALARM_GROUP_ID VARCHAR(255)';
        END IF;
        SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALARMANALYSIS_INCIDENT_'||v_taskId) AND COLUMN_NAME = UPPER('UNCLEAR_ALARM_NUM');
        IF V_CMT = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_INCIDENT_' || v_taskId || ' ADD UNCLEAR_ALARM_NUM INTEGER';
        END IF;
        SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALARMANALYSIS_INCIDENT_'||v_taskId) AND COLUMN_NAME = UPPER('CSN');
        IF V_CMT = 0 THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMANALYSIS_INCIDENT_' || v_taskId || ' ADD CSN INTEGER';
        END IF;
        UPDATE TBL_AIOPS_ANALYSIS_TASK SET PREDICT_CRON = 'Streaming' where TASK_ID = #{taskId};
        END;
    </update>

    <select id="getMaxEventId" resultType="java.lang.Long" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select max(ID) from TBL_AIOPS_ALARMANALYSIS_RESULT_^{taskId}
    </select>

    <select id="getDiagnosisIncident" resultMap="alarmIncidentEntity" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select incident.*, record.ID as RECORD_ID, record.* from TBL_AIOPS_ALARMANALYSIS_INCIDENT_^{taskId} incident
        left join TBL_AIOPS_ALARMANALYSIS_OPERATE_RECORD_^{taskId} record on record.INCIDENT_ID = incident.ID
        where incident.NEED_DIAGNOSIS = true
    </select>

    <select id="getEventsByIncidentIds" resultMap="eventResultMap" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select * from TBL_AIOPS_ALARMANALYSIS_RESULT_^{taskId}
        where INCIDENT_ID in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
        order by ID ASC
        limit (#{pageNumber} - 1) * #{pageSize}, #{pageSize}
    </select>

    <update id="createOperateRecordTable" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        CREATE TABLE TBL_AIOPS_ALARMANALYSIS_OPERATE_RECORD_^{taskId} (
            ID INTEGER AUTO_INCREMENT NOT NULL,
            INCIDENT_ID VARCHAR(255) NOT NULL,
            `TIME` BIGINT,
            DETAIL VARCHAR(1024),
            STATUS INTEGER,
            CONSTRAINT TBL_AIOPS_ALARMANALYSIS_OPERATE_RECORD_^{taskId} PRIMARY KEY (ID)
        )
    </update>

    <update id="createRepairAdviceTable" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        CREATE TABLE TBL_AIOPS_ALARMANALYSIS_REPAIR_ADVICE_^{taskId} (
            ID INTEGER AUTO_INCREMENT NOT NULL,
            INCIDENT_ID VARCHAR(255) NOT NULL,
            SOURCE_ID INTEGER,
            `SOURCE` VARCHAR(255),
            ZH_CN VARCHAR(1024),
            EN_US VARCHAR(1024),
            CONSTRAINT TBL_AIOPS_ALARMANALYSIS_REPAIR_ADVICE_^{taskId} PRIMARY KEY (ID)
        )
    </update>

    <update id="updateDiagnosisState" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        update TBL_AIOPS_ALARMANALYSIS_INCIDENT_^{taskId}
        <set>
            DIAGNOSIS_STATE = #{state}
        </set>
        <if test="startTime != null and startTime != 0">
            , DIAGNOSIS_TIME = #{startTime}
        </if>
        <if test="needDiagnosis != null">
            , NEED_DIAGNOSIS = #{needDiagnosis}
        </if>
        where ID in
        <foreach collection="ids" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </update>

    <insert id="insertOperateRecord" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        insert into TBL_AIOPS_ALARMANALYSIS_OPERATE_RECORD_^{taskId} (INCIDENT_ID, `TIME`, DETAIL, STATUS) values
        <foreach collection="list" item="record" index="index" separator=",">
            (#{record.incidentId}, #{record.time}, #{record.detail}, #{record.status})
        </foreach>
    </insert>

    <insert id="insertRepairAdvice" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        insert into TBL_AIOPS_ALARMANALYSIS_REPAIR_ADVICE_^{taskId} (INCIDENT_ID, `SOURCE`, SOURCD_ID, ZH_CN, EN_US) values
        <foreach collection="list" item="advice" index="index" separator=",">
            (#{advice.incidentId}, #{advice.source}, #{advice.sourceId}, #{advice.zhCn}, #{advice.enUs})
        </foreach>
    </insert>

    <update id="handleDiagnosisTimeout" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        update TBL_AIOPS_ALARMANALYSIS_INCIDENT_^{taskId}
        set
            DIAGNOSIS_STATE = 3
        where
            DIAGNOSIS_STATE = 1
            and DIAGNOSIS_TIME <![CDATA[ <= ]]> #{startTime}
    </update>

    <update id="updateDiagnosisResult" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        BEGIN
        <foreach collection="list" item="item" index="index" separator="">
            update TBL_AIOPS_ALARMANALYSIS_INCIDENT_^{taskId}
            set
                DIAGNOSIS_STATE = 2,
                ROOT_EVENT_ID = #{item.rootEventId}
            where
                ID = #{item.incidentId};
        </foreach>
        END
    </update>
    <select id="getNoAggrAlarmEvents" resultMap="eventResultMap" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver" timeout="120">
        select ID,INCIDENT_ID,CSN,AGGR_STATUS from TBL_AIOPS_ALARMANALYSIS_RESULT_^{taskId}
        where AGGR_STATUS = false
    </select>
    <update id="updateAggrStatus" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        update TBL_AIOPS_ALARMANALYSIS_RESULT_^{taskId}
        set AGGR_STATUS = true
        where ID in
        <foreach collection="idList" open="(" close=")" separator="," item="id">
            #{id}
        </foreach>
    </update>
    <select id="getNoAggrAlarmEventsByIncidentId" resultMap="eventResultMap" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver" timeout="120">
        select ID,INCIDENT_ID,CSN,AGGR_STATUS from TBL_AIOPS_ALARMANALYSIS_RESULT_^{taskId}
        where AGGR_STATUS = false
        and INCIDENT_ID in
        <foreach collection="incidentIds" open="(" close=")" separator="," item="incidentId">
            #{incidentId}
        </foreach>
    </select>
    <select id="getUnClearAlarmCountByIncidentIds" resultType="java.util.Map" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select i.ID,count(a.ID) as ALARM_COUNT from TBL_AIOPS_ALARMANALYSIS_INCIDENT_^{taskId} i
        left join TBL_AIOPS_ALARMANALYSIS_RESULT_^{taskId} a on i.ID = a.INCIDENT_ID
        where a.END_TIME = 0 and i.ID in
        <foreach collection="incidentIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by i.ID
    </select>
</mapper>