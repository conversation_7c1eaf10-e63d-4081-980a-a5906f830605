<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.huawei.i2000.dvanalysisengineservice.dao.knowledgerepository.mapper.RelationResourceMapper">
    <resultMap type="com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.model.entityknowledge.relation.RelationMeta"
               id="relationResult">
        <id column="ID" property="id" javaType="String" jdbcType="VARCHAR"/>
        <result column="SOURCE" property="source" javaType="String" jdbcType="VARCHAR"/>
        <result column="TARGET" property="target" javaType="String" jdbcType="VARCHAR"/>
        <result column="INVOKE_RELATIONSHIP" property="invokeRelationship" javaType="String" jdbcType="VARCHAR"/>
        <result column="ASSOCIATE_REPOSITORY_ID" property="associateRepositoryId" javaType="String" jdbcType="VARCHAR"/>
        <result column="DESCRIPTION" property="description" javaType="String" jdbcType="VARCHAR"/>
        <result column="SOURCE_SCENE" property="sourceScene" javaType="int" jdbcType="INTEGER"/>
    </resultMap>

    <insert id="insertBatchRelationResource">
        insert into TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE (ID, SOURCE, TARGET, INVOKE_RELATIONSHIP, ASSOCIATE_REPOSITORY_ID, DESCRIPTION, SOURCE_SCENE)
        values
        <foreach collection="relationList" index="index" item="relation" separator=",">
            (#{relation.id}, #{relation.source}, #{relation.target}, #{relation.invokeRelationship}, #{relation.associateRepositoryId}, #{relation.description}, #{relation.sourceScene})
        </foreach>
    </insert>
    <update id="updateRelationInfoById">
        UPDATE TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE
        SET SOURCE = #{info.source.nodeId}, TARGET = #{info.target.nodeId}, INVOKE_RELATIONSHIP = #{info.invokeRelationship}, DESCRIPTION = #{info.description}
        WHERE ID = #{info.id}
    </update>

    <delete id="deleteRelationResourceByIds">
        delete from TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE where ID in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="deleteAllRelationResource">
        delete from TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE where SOURCE_SCENE = #{scene}
    </select>

    <select id="queryAllRelationByRepoId" resultMap="relationResult">
        select * from TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE
        where ASSOCIATE_REPOSITORY_ID = #{id}
    </select>

    <select id="queryRelationById" resultMap="relationResult">
        select * from TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE where ID in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="queryRelationByNodeId" resultMap="relationResult">
        select * from TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE where (SOURCE = #{node1} and TARGET = #{node2}) OR (TARGET = #{node1} and SOURCE = #{node2})
    </select>

    <select id="queryAllRelationByNodeId" resultMap="relationResult">
        select * from TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE where SOURCE = #{id} or TARGET = #{id}
    </select>
    <select id="queryExistRelationByRelationInfo" resultMap="relationResult">
        select * from TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE where SOURCE in
        <foreach item="relation" index="index" collection="relations" open="(" separator="," close=")">
            (#{relation.source})
        </foreach>
        and TARGET in
        <foreach item="relation" index="index" collection="relations" open="(" separator="," close=")">
            (#{relation.target})
        </foreach>
        and ASSOCIATE_REPOSITORY_ID = #{solution}
    </select>
    <select id="queryAllRelationCount" resultType="java.lang.Integer">
        select COUNT(*) from TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE
    </select>

</mapper>