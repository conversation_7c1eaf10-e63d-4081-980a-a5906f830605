<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.mapper.IndicatorPredictResultMapper">

    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.SingleIndicator"
               id="singleIndicator">
        <id column="INDICATOR_ID" property="indicatorId" javaType="String" jdbcType="VARCHAR"/>
        <result column="TASK_ID" property="taskId" javaType="int" jdbcType="INTEGER"/>
        <result column="TASK_NAME" property="taskName" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_UNIT_KEY" property="measUnitKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_UNIT_NAME" property="measUnitName" javaType="String" jdbcType="VARCHAR"/>
        <result column="DN" property="DN" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_TYPE_KEY" property="indexKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="INDEX_NAME" property="indexName" javaType="String" jdbcType="VARCHAR"/>
        <result column="MO_TYPE" property="moType" javaType="String" jdbcType="VARCHAR"/>
        <result column="DISPLAY_VALUE" property="displayValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="ORIGINAL_VALUE" property="originValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="UNIT" property="unit" javaType="String" jdbcType="VARCHAR"/>
        <result column="DN_NAME" property="moName" javaType="String" jdbcType="VARCHAR"/>
        <result column="RESOURCE_GROUP" property="clusterName" javaType="String" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="unitedIndicator" type="com.huawei.i2000.dvanalysisengineservice.model.UnitedIndicator">
        <id column="UNITED_ID" property="unitedId" javaType="String" jdbcType="VARCHAR"/>
        <result column="TASK_ID" property="taskId" javaType="int" jdbcType="INTEGER"/>
        <result column="CLUSTER_NAME" property="clusterName" javaType="String" jdbcType="VARCHAR"/>
        <result column="SITE_ID" property="siteId" javaType="String" jdbcType="VARCHAR"/>
        <result column="MO_TYPE" property="moType" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_UNIT_KEY" property="measUnitKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_TYPE_KEY" property="measTypeKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_UNIT_NAME" property="measUnitName" javaType="String" jdbcType="VARCHAR"/>
        <result column="INDEX_ID" property="indexId" javaType="String" jdbcType="VARCHAR"/>
        <result column="INDEX_NAME" property="indexName" javaType="String" jdbcType="VARCHAR"/>
        <result column="DISPLAY_VALUE" property="displayValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="ORIGINAL_VALUE" property="originalValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="UNIT" property="unit" javaType="String" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="singleDisplayValue" type="com.huawei.i2000.dvanalysisengineservice.model.PerformanceIndexValue">
        <id column="COLLECT_TIME" property="timestampStr" javaType="String" jdbcType="BIGINT"/>
        <result column="INDICATOR_VALUE" property="indexValue" javaType="String" jdbcType="DOUBLE"/>
    </resultMap>

    <resultMap id="UnitedDisplayValue" type="com.huawei.i2000.dvanalysisengineservice.model.PerformanceIndexValue">
        <id column="COLLECT_TIME" property="timestampStr" javaType="String" jdbcType="BIGINT"/>
        <result column="STATIC_VALUE" property="indexValue" javaType="String" jdbcType="DOUBLE"/>
    </resultMap>

    <resultMap id="predictTask" type="com.huawei.i2000.dvanalysisengineservice.model.PredictTask">
        <id column="TASK_ID" property="taskId" javaType="int" jdbcType="INTEGER"/>
        <result column="TASK_NAME" property="taskName" javaType="String" jdbcType="VARCHAR"/>
        <result column="TASK_TYPE" property="taskType" javaType="int" jdbcType="INTEGER"/>
        <result column="INDICATOR_PREDICT_SCENARIO" property="predictScenario" javaType="int" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="exceptionIntervalEntity" type="com.huawei.i2000.dvanalysisengineservice.model.ExceptionInterval">
        <id column="ID" property="id" javaType="integer" jdbcType="INTEGER"/>
        <result column="TASK_ID" property="taskId" javaType="integer" jdbcType="INTEGER"/>
        <result column="INDICATOR_ID" property="indicatorId" javaType="String" jdbcType="VARCHAR"/>
        <result column="START_TIME" property="startTime" javaType="long" jdbcType="BIGINT"/>
        <result column="END_TIME" property="endTime" javaType="long" jdbcType="BIGINT"/>
        <result column="CREATE_TIME" property="createTime" javaType="long" jdbcType="BIGINT"/>
    </resultMap>

    <select id="querySinglePredictTask" resultMap="predictTask">
        SELECT DISTINCT task.TASK_ID, TASK_NAME, TASK_TYPE, INDICATOR_PREDICT_SCENARIO
        FROM TBL_AIOPS_ANALYSIS_TASK task
        LEFT JOIN TBL_AIOPS_TASK_INDICATOR indicator ON task.TASK_ID=indicator.TASK_ID
        WHERE task.TASK_TYPE = 6
        AND (task.INDICATOR_PREDICT_SCENARIO = 0 or task.INDICATOR_PREDICT_SCENARIO = 2)
        <if test="userId != null">
            AND task.USER_ID = #{userId}
        </if>
        <if test="dns != null and dns.size() > 0">
            and indicator.dn in
            <foreach item="dn" collection="dns" open="(" separator="," close=")">
                #{dn}
            </foreach>
        </if>
    </select>

    <select id="queryUnitedPredictTask" resultMap="predictTask">
        SELECT DISTINCT task.TASK_ID, TASK_NAME, TASK_TYPE, INDICATOR_PREDICT_SCENARIO
        FROM TBL_AIOPS_ANALYSIS_TASK task
        LEFT JOIN TBL_AIOPS_TASK_UNITED_INDICATOR united ON task.TASK_ID=united.TASK_ID
        WHERE task.TASK_TYPE = 6
        AND task.INDICATOR_PREDICT_SCENARIO = 1
        <if test="userId != null">
            AND task.USER_ID = #{userId}
        </if>
        <if test="clusters != null and clusters.size() > 0">
            and united.CLUSTER_NAME in
            <foreach item="cluster" collection="clusters" open="(" separator="," close=")">
                #{cluster}
            </foreach>
        </if>
    </select>

    <select id="querySingleIndicatorList" resultMap="singleIndicator">
        SELECT * FROM TBL_AIOPS_TASK_INDICATOR indicator
            LEFT JOIN TBL_AIOPS_ANALYSIS_TASK task ON task.TASK_ID=indicator.TASK_ID
        WHERE task.TASK_TYPE = 6
        AND task.TASK_ID = #{taskId}
        <if test="indicatorId != null and indicatorId !=''">
            AND INDICATOR_ID = #{indicatorId}
        </if>
        <if test="indicatorName != null and indicatorName !=''">
            AND UPPER(INDEX_NAME) LIKE CONCAT('%' , UPPER(#{indicatorName}) , '%')
        </if>
        <if test="dnName != null and dnName !=''">
            AND UPPER(DN_NAME) LIKE CONCAT('%' , UPPER(#{dnName}) , '%')
        </if>
        <if test="measObject != null and measObject !=''">
            AND UPPER(DISPLAY_VALUE) LIKE CONCAT('%' , UPPER(#{measObject}) , '%')
        </if>
        <if test="clusterName != null and clusterName !=''">
            AND UPPER(RESOURCE_GROUP) LIKE CONCAT('%' , UPPER(#{clusterName}) , '%')
        </if>
        <if test="dns != null and dns.size() > 0">
            and indicator.dn in
            <foreach item="dn" collection="dns" open="(" separator="," close=")">
                #{dn}
            </foreach>
        </if>
        <if test="userId != null">
            AND task.USER_ID = #{userId}
        </if>
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="querySingleIndicatorListCount" resultType="Integer">
        SELECT COUNT(*) FROM TBL_AIOPS_TASK_INDICATOR indicator
        LEFT JOIN TBL_AIOPS_ANALYSIS_TASK task ON task.TASK_ID=indicator.TASK_ID
        WHERE task.TASK_TYPE = 6
        AND task.TASK_ID = #{taskId}
        <if test="indicatorId != null and indicatorId !=''">
            AND INDICATOR_ID = #{indicatorId}
        </if>
        <if test="indicatorName != null and indicatorName !=''">
            AND UPPER(INDEX_NAME) LIKE CONCAT('%' , UPPER(#{indicatorName}) , '%')
        </if>
        <if test="dnName != null and dnName !=''">
            AND UPPER(DN_NAME) LIKE CONCAT('%' , UPPER(#{dnName}) , '%')
        </if>
        <if test="measObject != null and measObject !=''">
            AND UPPER(DISPLAY_VALUE) LIKE CONCAT('%' , UPPER(#{measObject}) , '%')
        </if>
        <if test="clusterName != null and clusterName !=''">
            AND UPPER(RESOURCE_GROUP) LIKE CONCAT('%' , UPPER(#{clusterName}) , '%')
        </if>
        <if test="dns != null and dns.size() > 0">
            and indicator.dn in
            <foreach item="dn" collection="dns" open="(" separator="," close=")">
                #{dn}
            </foreach>
        </if>
        <if test="userId != null">
            AND task.USER_ID = #{userId}
        </if>
    </select>

    <select id="queryUnitedIndicatorList" resultMap="unitedIndicator">
        SELECT *
        FROM TBL_AIOPS_TASK_UNITED_INDICATOR united
                 LEFT JOIN TBL_AIOPS_ANALYSIS_TASK task ON task.TASK_ID = united.TASK_ID
        <where>
            task.TASK_TYPE = 6
            AND task.TASK_ID = #{taskId}
            <if test="indicatorId != null and indicatorId !=''">
                AND INDICATOR_ID = #{indicatorId}
            </if>
            <if test="siteId != null and siteId != ''">
                AND united.SITE_ID = #{siteId}
            </if>
            <if test="indicatorName != null and indicatorName != ''">
                AND united.INDEX_NAME LIKE CONCAT('%', #{indicatorName},'%')
            </if>
            <if test="clusterName != null and clusterName != ''">
                AND united.CLUSTER_NAME LIKE CONCAT('%', #{clusterName},'%')
            </if>
            <if test="clusters != null and clusters.size() > 0">
                and united.CLUSTER_NAME in
                <foreach item="cluster" collection="clusters" open="(" separator="," close=")">
                    #{cluster}
                </foreach>
            </if>
            <if test="userId != null">
                AND task.USER_ID = #{userId}
            </if>
        </where>
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="queryUnitedIndicatorByTaskId" resultMap="unitedIndicator">
        SELECT *
        FROM TBL_AIOPS_TASK_UNITED_INDICATOR united
                 LEFT JOIN TBL_AIOPS_ANALYSIS_TASK task ON task.TASK_ID = united.TASK_ID
        WHERE task.TASK_TYPE = 6
        AND task.TASK_ID = #{taskId}
    </select>

    <select id="queryUnitedIndicatorListCount" resultType="Integer">
        SELECT COUNT(*) FROM TBL_AIOPS_TASK_UNITED_INDICATOR united
        LEFT JOIN TBL_AIOPS_ANALYSIS_TASK task ON task.TASK_ID=united.TASK_ID
        WHERE task.TASK_TYPE = 6
        AND task.TASK_ID = #{taskId}
        <if test="indicatorId != null and indicatorId !=''">
            AND INDICATOR_ID = #{indicatorId}
        </if>
        <if test="siteId != null and siteId != ''">
            AND united.SITE_ID = #{siteId}
        </if>
        <if test="indicatorName != null and indicatorName != ''">
            AND united.INDEX_NAME LIKE CONCAT('%', #{indicatorName},'%')
        </if>
        <if test="clusterName != null and clusterName != ''">
            AND united.CLUSTER_NAME LIKE CONCAT('%', #{clusterName},'%')
        </if>
        <if test="clusters != null and clusters.size() > 0">
            and united.CLUSTER_NAME in
            <foreach item="cluster" collection="clusters" open="(" separator="," close=")">
                #{cluster}
            </foreach>
        </if>
        <if test="userId != null">
            AND task.USER_ID = #{userId}
        </if>
    </select>

    <select id="querySingleIndicatorPredictData" resultMap="singleDisplayValue" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        SELECT COLLECT_TIME, INDICATOR_VALUE
        FROM ^{tableName}
        WHERE INDICATOR_ID = #{indicatorId}
          AND #{startTime} <![CDATA[ <= ]]> COLLECT_TIME
          AND #{endTime} <![CDATA[ > ]]> COLLECT_TIME
        ORDER BY COLLECT_TIME ASC
    </select>

    <select id="queryUnitedIndicatorHistoryData" resultMap="UnitedDisplayValue" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        SELECT COLLECT_TIME, STATIC_VALUE
        FROM ^{tableName}
        WHERE #{startTime} <![CDATA[ <= ]]> COLLECT_TIME
          AND #{endTime} <![CDATA[ > ]]> COLLECT_TIME
          AND UNITED_ID = #{unitedId}
          AND IS_PREDICT = false
          AND STATIC_TYPE = #{staticType}
        ORDER BY COLLECT_TIME ASC
    </select>

    <select id="queryUnitedIndicatorPredictData" resultMap="UnitedDisplayValue" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        SELECT COLLECT_TIME, STATIC_VALUE
        FROM ^{tableName}
        WHERE UNITED_ID = #{unitedId}
          AND #{endTime} <![CDATA[ > ]]> COLLECT_TIME
          AND #{startTime} <![CDATA[ <= ]]> COLLECT_TIME
          AND IS_PREDICT = true
          AND STATIC_TYPE = #{staticType}
        ORDER BY COLLECT_TIME ASC
    </select>

    <select id="queryAggregateData" resultMap="UnitedDisplayValue" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        SELECT COLLECT_TIME, STATIC_VALUE
        FROM ^{tableName}
        WHERE UNITED_ID = #{indicatorId}
          AND #{startTime} <![CDATA[ <= ]]> COLLECT_TIME
          AND #{endTime} <![CDATA[ > ]]> COLLECT_TIME
          AND STATIC_TYPE = 1
          AND IS_PREDICT = #{isPredict}
        ORDER BY COLLECT_TIME ASC
    </select>

    <select id="querySingleMaxPredictTime" resultType="Long" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        SELECT MAX(COLLECT_TIME) FROM ^{tableName}
        WHERE INDICATOR_ID = #{indicatorId}
    </select>

    <select id="queryUnitedMaxPredictTime" resultType="Long" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        SELECT MAX(COLLECT_TIME) FROM ^{tableName}
        WHERE UNITED_ID = #{unitedId}
        AND IS_PREDICT = #{isPredict}
        AND STATIC_TYPE = 0
    </select>

    <select id="queryAllMoTypeFromUnitedIndicator" resultType="java.lang.String">
        SELECT DISTINCT(MO_TYPE) FROM TBL_AIOPS_TASK_UNITED_INDICATOR
    </select>

    <select id="queryOneUnitedIndicator" resultMap="unitedIndicator">
        SELECT *
        FROM TBL_AIOPS_TASK_UNITED_INDICATOR united
                 LEFT JOIN TBL_AIOPS_ANALYSIS_TASK task ON task.TASK_ID = united.TASK_ID
        WHERE united.UNITED_ID = #{unitedId}
        <if test="clusters != null and clusters.size() > 0">
            and united.CLUSTER_NAME in
            <foreach item="cluster" collection="clusters" open="(" separator="," close=")">
                #{cluster}
            </foreach>
        </if>
        <if test="userId != null">
            AND task.USER_ID = #{userId}
        </if>
    </select>

    <insert id="insertExceptionInterval" useGeneratedKeys="true" keyProperty="id">
        insert into TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL(TASK_ID, INDICATOR_ID, START_TIME, END_TIME, CREATE_TIME)
        values(#{exceptionInterval.taskId}, #{exceptionInterval.indicatorId}, #{exceptionInterval.startTime},
               #{exceptionInterval.endTime}, #{exceptionInterval.createTime})
    </insert>

    <delete id="deleteExceptionIntervalById">
        delete from TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL where ID = #{id}
    </delete>

    <delete id="deleteExceptionInterval">
        delete from TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL
        <where>
            <if test="taskId != null and taskId != ''">
                AND TASK_ID = #{taskId}
            </if>
            <if test="indicatorIds != null and indicatorIds.size() > 0">
                AND INDICATOR_ID in
                <foreach item="indicatorId" collection="indicatorIds" open="(" separator="," close=")">
                    #{indicatorId}
                </foreach>
            </if>
        </where>
    </delete>

    <select id="getExceptionInterval" resultMap="exceptionIntervalEntity">
        select * from TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL
        <where>
            <if test="taskId != null and taskId != ''">
                AND TASK_ID = #{taskId}
            </if>
            <if test="indicatorId != null and indicatorId != ''">
                AND INDICATOR_ID = #{indicatorId}
            </if>
            <if test="startTime != null">
                AND END_TIME <![CDATA[ >= ]]> #{startTime}
            </if>
            <if test="endTime != null">
                AND START_TIME <![CDATA[ <= ]]> #{endTime}
            </if>
        </where>
        order by START_TIME ASC
    </select>

    <select id="getExceptionIntervalCount" resultType="java.lang.Integer">
        select COUNT(ID) from TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL
        <where>
            <if test="taskId != null and taskId != ''">
                AND TASK_ID = #{taskId}
            </if>
            <if test="indicatorId != null and indicatorId != ''">
                AND INDICATOR_ID = #{indicatorId}
            </if>
        </where>
    </select>

    <delete id="clearExceptionInterval">
        delete from TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL
        where INDICATOR_ID = #{indicatorId} and END_TIME <![CDATA[ < ]]> #{endTime}
    </delete>

    <delete id="deleteAuxiliaryAggregateData" timeout="120" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        delete from TBL_AIOPS_TASK_AUXILIARY_INDICATOR_AGGREGATE_RESULT_^{taskId}
        <where>
            <if test="indicatorIds != null and indicatorIds.size() != 0">
                AND INDICATOR_ID in
                <foreach collection="indicatorIds" item="indicatorId" separator="," open="(" close=")">
                    #{indicatorId}
                </foreach>
            </if>
            <if test="endTime != null">
                AND COLLECT_TIME <![CDATA[ <= ]]> #{endTime}
            </if>
        </where>
    </delete>
</mapper>