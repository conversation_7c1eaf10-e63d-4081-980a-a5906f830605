<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.mapper.TaskAnalysisResultMapper">
    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.IndicatorOutlier"
               id="indicatorOutlier">
        <id column="ID" property="id" javaType="String" jdbcType="VARCHAR"/>
        <result column="TASK_ID" property="taskId" javaType="String" jdbcType="VARCHAR"/>
        <result column="SITUATION_ID" property="situationId" javaType="String" jdbcType="VARCHAR"/>
        <result column="TASK_NAME" property="taskName" javaType="String" jdbcType="VARCHAR"/>
        <result column="INDICATOR_ID" property="indicatorId" javaType="String" jdbcType="VARCHAR"/>
        <result column="START_TIME" property="startTime" javaType="String" jdbcType="BIGINT"/>
        <result column="END_TIME" property="endTime" javaType="String" jdbcType="BIGINT"/>
        <result column="DURATION" property="duration" javaType="String" jdbcType="BIGINT"/>
        <result column="CORRECT" property="correct" javaType="int" jdbcType="NUMERIC"/>
        <result column="ALARM_TYPE" property="alarmType" javaType="int" jdbcType="INTEGER"/>
        <result column="PROBABLE_CAUSE" property="probableCause" javaType="String" jdbcType="VARCHAR"/>
        <result column="reportAlarm" property="reportAlarm"/>
        <result column="SWITCH_ALARM_FIELD" property="switchAlarmField" javaType="String" jdbcType="VARCHAR"/>
        <result column="REPORT_ALARM_ID" property="reportAlarmId" javaType="String" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.Indicator"
               id="indicator">
        <id column="INDICATOR_ID" property="indicatorId" javaType="String" jdbcType="VARCHAR"/>
        <result column="TASK_ID" property="taskId" javaType="int" jdbcType="INTEGER"/>
        <result column="TASK_NAME" property="taskName" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_UNIT_KEY" property="measUnitKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_UNIT_NAME" property="measUnitName" javaType="String" jdbcType="VARCHAR"/>
        <result column="DN" property="DN" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_TYPE_KEY" property="indexKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="INDEX_NAME" property="indexName" javaType="String" jdbcType="VARCHAR"/>
        <result column="MO_TYPE" property="moType" javaType="String" jdbcType="VARCHAR"/>
        <result column="DISPLAY_VALUE" property="displayValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="ORIGINAL_VALUE" property="originValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="UNIT" property="unit" javaType="String" jdbcType="VARCHAR"/>
        <result column="indicatorStatus" property="indicatorStatus"/>
        <result column="lastOutlierTime" property="lastOutlierTime"/>
        <result column="SITUATION_ID" property="situationId" javaType="String" jdbcType="VARCHAR"/>
        <result column="PQL" property="pql" javaType="String" jdbcType="VARCHAR"/>
        <result column="GROUP_ID" property="groupId" javaType="String" jdbcType="VARCHAR"/>
        <result column="SOFT_DELETE" property="softDelete" javaType="boolean" jdbcType="BOOLEAN"/>
        <result column="SOFT_DELETE_TIMESTAMP" property="softDeleteTimeStamp" javaType="long" jdbcType="BIGINT"/>
        <result column="INDICATOR_TASK_TYPE" property="indicatorTaskType" javaType="int" jdbcType="INTEGER"/>
        <result column="MS_GROUP_ID" property="msGroupId" javaType="String" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap type="com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.UpperAndLowerThreshold"
               id="upperAndLowerThreshold">
        <id column="INDICATOR_ID" property="indicatorId" javaType="String" jdbcType="VARCHAR"/>
        <id column="COLLECT_TIME" property="timestampStr" javaType="Long" jdbcType="BIGINT"/>
        <result column="UPPER_THRESHOLD" property="upperThreshold" javaType="String" jdbcType="DOUBLE"/>
        <result column="LOWER_THRESHOLD" property="lowerThreshold" javaType="String" jdbcType="DOUBLE"/>
    </resultMap>
    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.Indicator"
               id="subIndicator">
        <id column="INDICATOR_ID" property="indicatorId" javaType="String" jdbcType="VARCHAR"/>
        <result column="ORIGINAL_VALUE" property="originValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="DN_NAME" property="moName" javaType="String" jdbcType="VARCHAR"/>
    </resultMap>
    <resultMap type="com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.IndicatorThreshold"
               id="indicatorThreshold">
        <id column="INDICATOR_ID" property="indicatorId" javaType="String" jdbcType="VARCHAR"/>
        <result column="COLLECT_TIME" property="collectTime" javaType="Long" jdbcType="BIGINT"/>
        <result column="UPPER_THRESHOLD" property="upperThreshold" javaType="double" jdbcType="DOUBLE"/>
        <result column="LOWER_THRESHOLD" property="lowerThreshold" javaType="double" jdbcType="DOUBLE"/>
    </resultMap>
    <resultMap type="com.huawei.i2000.dvanalysisengineservice.model.AssociationTaskData"
               id="businessTopoAlarm">
        <id column="TASK_ID" property="taskId" javaType="int" jdbcType="INTEGER"/>
        <result column="REPORT_ALARM_ID" property="alarmId"/>
        <collection property="indicatorList" javaType="java.util.List"
                    ofType="com.huawei.i2000.dvanalysisengineservice.model.BusinessTopoIndicator">
            <id property="indicatorId" column="INDICATOR_ID"/>
            <result property="measUnitKey" column="MEAS_UNIT_KEY"/>
            <result property="measTypeKey" column="MEAS_TYPE_KEY"/>
            <result property="dn" column="DN"/>
            <result property="moType" column="MO_TYPE"/>
            <result property="displayValue" column="DISPLAY_VALUE"/>
            <result property="originalValue" column="ORIGINAL_VALUE"/>
        </collection>
    </resultMap>

    <resultMap type="com.huawei.i2000.dvanalysisengineservice.business.aiagent.AbnormalIndicator"
               id="abnormalIndicator">
        <id column="INDICATOR_ID" property="indicatorId" javaType="String" jdbcType="VARCHAR"/>
        <result column="TASK_ID" property="taskId" javaType="int" jdbcType="INTEGER"/>
        <result column="TASK_NAME" property="taskName" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_UNIT_KEY" property="measUnitKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_UNIT_NAME" property="measUnitName" javaType="String" jdbcType="VARCHAR"/>
        <result column="DN" property="dn" javaType="String" jdbcType="VARCHAR"/>
        <result column="DN_NAME" property="moName" javaType="String" jdbcType="VARCHAR"/>
        <result column="MEAS_TYPE_KEY" property="indexKey" javaType="String" jdbcType="VARCHAR"/>
        <result column="INDEX_NAME" property="indexName" javaType="String" jdbcType="VARCHAR"/>
        <result column="MO_TYPE" property="moType" javaType="String" jdbcType="VARCHAR"/>
        <result column="DISPLAY_VALUE" property="displayValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="ORIGINAL_VALUE" property="originValue" javaType="String" jdbcType="VARCHAR"/>
        <result column="UNIT" property="unit" javaType="String" jdbcType="VARCHAR"/>
        <result column="START_TIME" property="startTime" javaType="String" jdbcType="BIGINT"/>
        <result column="END_TIME" property="endTime" javaType="String" jdbcType="BIGINT"/>
        <result column="DURATION" property="duration" javaType="String" jdbcType="BIGINT"/>
        <result column="CORRECT" property="correct" javaType="int" jdbcType="NUMERIC"/>
        <result column="ALARM_TYPE" property="alarmType" javaType="int" jdbcType="INTEGER"/>
        <result column="PROBABLE_CAUSE" property="probableCause" javaType="String" jdbcType="VARCHAR"/>
        <result column="reportAlarm" property="reportAlarm"/>
        <result column="SWITCH_ALARM_FIELD" property="switchAlarmField" javaType="String" jdbcType="VARCHAR"/>
        <result column="REPORT_ALARM_ID" property="reportAlarmId" javaType="String" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getIndicatorList" resultMap="indicator">
        select t2.* from (
        select tati.MEAS_UNIT_NAME,tati.INDEX_NAME,tati.DISPLAY_VALUE,tati.DN,tati.UNIT,tati.GROUP_ID,
        tati.INDICATOR_ID,tati.MEAS_TYPE_KEY,tati.MEAS_UNIT_KEY,tati.MO_TYPE,tati.TASK_ID,taat.TASK_NAME,tati.PQL,tati.SOFT_DELETE,tati.SOFT_DELETE_TIMESTAMP,tati.INDICATOR_TASK_TYPE,
        taio.lastOutlierTime as lastOutlierTime,
        (case when tati.ABNORMAL = 1 then 1 else 0 end) as indicatorStatus
        from TBL_AIOPS_TASK_INDICATOR tati
        left join TBL_AIOPS_ANALYSIS_TASK taat on tati.TASK_ID=taat.TASK_ID
        left join (select taio.INDICATOR_ID, taio.TASK_ID, max(taio.START_TIME) as lastOutlierTime
        from TBL_AIOPS_INDICATOR_OUTLIER taio where ((#{startTime} <![CDATA[ <= ]]> taio.START_TIME and
        #{endTime} <![CDATA[ > ]]> taio.START_TIME) or
        (#{startTime} <![CDATA[ > ]]> taio.START_TIME and
        (#{startTime} <![CDATA[ < ]]> taio.END_TIME or taio.END_TIME=0))) group by taio.INDICATOR_ID, taio.TASK_ID) taio
        on taio.TASK_ID = tati.TASK_ID and taio.INDICATOR_ID = tati.INDICATOR_ID
        where taat.TASK_TYPE=1
        <if test="situationIds != null and situationIds.size() > 0">
            and taat.SITUATION_ID in
            <foreach item="item" index="index" collection="situationIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="taskId != null">
            and tati.TASK_ID=#{taskId}
        </if>
        <if test="groupId != null">
            and tati.GROUP_ID=#{groupId}
        </if>
        <if test="dns != null and dns.size() > 0">
            and tati.dn in
            <foreach item="dn" collection="dns" open="(" separator="," close=")">
                #{dn}
            </foreach>
        </if>
        <if test="startTime != null">
            and (tati.SOFT_DELETE_TIMESTAMP is null or tati.SOFT_DELETE_TIMESTAMP = 0
            or tati.SOFT_DELETE_TIMESTAMP >= #{startTime})
        </if>
        <if test="displaySoftDelete != null and displaySoftDelete == false">
            and (tati.SOFT_DELETE IS NULL OR tati.SOFT_DELETE = false)
        </if>
        ) t2
        <where>
            <if test="abnormalList != null and abnormalList.size() > 0">
                and indicatorStatus in
                <foreach item="item" index="index" collection="abnormalList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="indicatorName != '' and indicatorName != null">
                and (UPPER(t2.INDEX_NAME) like CONCAT('%',UPPER(#{indicatorName}),'%') escape '|' or UPPER(t2.PQL) like CONCAT('%',UPPER(#{indicatorName}),'%') escape '|')
            </if>
            <if test="measObject != null and measObject != ''">
                and UPPER(t2.DISPLAY_VALUE) like CONCAT('%',UPPER(#{measObject}),'%') escape '|'
            </if>
            <if test="taskName != null and taskName != ''">
                and UPPER(t2.TASK_NAME) like CONCAT('%',UPPER(#{taskName}),'%') escape '|'
            </if>
            <if test="measUnitName != null and measUnitName != ''">
                and UPPER(t2.MEAS_UNIT_NAME) like CONCAT('%',UPPER(#{measUnitName}),'%') escape '|'
            </if>
            <if test="indicatorTaskType != null">
                and t2.INDICATOR_TASK_TYPE=#{indicatorTaskType}
            </if>
        </where>
        order by indicatorStatus desc,lastOutlierTime desc nulls last
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="getIndicatorListTotal" resultMap="indicator">
        select t2.* from (select
        (case when tati.ABNORMAL=1 then 1 else 0 end) as indicatorStatus,
        tati.DN,
        tati.INDEX_NAME as indexName,
        tati.INDICATOR_ID,
        tati.MEAS_UNIT_NAME,
        taat.TASK_NAME,
        tati.DISPLAY_VALUE as measObject,tati.PQL,tati.SOFT_DELETE,tati.SOFT_DELETE_TIMESTAMP,tati.INDICATOR_TASK_TYPE
        from TBL_AIOPS_TASK_INDICATOR tati
        left join TBL_AIOPS_ANALYSIS_TASK taat on tati.TASK_ID=taat.TASK_ID
        where taat.TASK_TYPE=1
        <if test="situationIds != null and situationIds.size() > 0">
            and taat.SITUATION_ID in
            <foreach item="item" index="index" collection="situationIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="taskId != null">
            and tati.TASK_ID=#{taskId}
        </if>
        <if test="groupId != null">
            and tati.GROUP_ID=#{groupId}
        </if>
        <if test="dns != null and dns.size() > 0">
            and tati.dn in
            <foreach item="dn" collection="dns" open="(" separator="," close=")">
                #{dn}
            </foreach>
        </if>
        <if test="startTime != null">
            and (tati.SOFT_DELETE_TIMESTAMP is null or tati.SOFT_DELETE_TIMESTAMP = 0
            or tati.SOFT_DELETE_TIMESTAMP >= #{startTime})
        </if>
        <if test="displaySoftDelete != null and displaySoftDelete == false">
            and (tati.SOFT_DELETE IS NULL OR tati.SOFT_DELETE = false)
        </if>
        ) t2
        <where>
            <if test="abnormalList != null and abnormalList.size() > 0">
                and indicatorStatus in
                <foreach item="item" index="index" collection="abnormalList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="indicatorName != '' and indicatorName != null">
                and (UPPER(indexName) like CONCAT('%',UPPER(#{indicatorName}),'%') escape '|' or UPPER(t2.PQL) like CONCAT('%',UPPER(#{indicatorName}),'%') escape '|' )
            </if>
            <if test="measObject != null and measObject != ''">
                and UPPER(measObject) like CONCAT('%',UPPER(#{measObject}),'%') escape '|'
            </if>
            <if test="taskName != null and taskName != ''">
                and UPPER(TASK_NAME) like CONCAT('%',UPPER(#{taskName}),'%') escape '|'
            </if>
            <if test="measUnitName != null and measUnitName != ''">
                and UPPER(MEAS_UNIT_NAME) like CONCAT('%',UPPER(#{measUnitName}),'%') escape '|'
            </if>
            <if test="indicatorTaskType != null">
                and INDICATOR_TASK_TYPE=#{indicatorTaskType}
            </if>
        </where>
    </select>

    <select id="getIndicatorListById" resultMap="indicator">
        select tati.MEAS_UNIT_NAME,tati.INDEX_NAME,tati.DISPLAY_VALUE,tati.DN,tati.UNIT,tati.PQL,
        tati.INDICATOR_ID,tati.MEAS_TYPE_KEY,tati.MEAS_UNIT_KEY,tati.MO_TYPE,tati.TASK_ID,taat.TASK_NAME
        from TBL_AIOPS_TASK_INDICATOR tati
        left join TBL_AIOPS_ANALYSIS_TASK taat on tati.TASK_ID=taat.TASK_ID
        <where>
            <if test="dns != null and dns.size() > 0">
                and tati.DN in
                <foreach item="dn" collection="dns" open="(" separator="," close=")">
                    #{dn}
                </foreach>
            </if>
            <if test="taskIds != null and taskIds.size() > 0">
                and tati.TASK_ID in
                <foreach item="item" index="index" collection="taskIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="indicatorName != '' and indicatorName != null">
                and UPPER(tati.INDEX_NAME) like CONCAT('%',UPPER(#{indicatorName}),'%') escape '|'
            </if>
            and taat.TASK_TYPE=1
        </where>
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <select id="getIndicatorListByIdTotal" resultMap="indicator">
        select * from TBL_AIOPS_TASK_INDICATOR tati left join TBL_AIOPS_ANALYSIS_TASK taat on tati.TASK_ID=taat.TASK_ID
        <where>
            <if test="dns != null and dns.size() > 0">
                and tati.DN in
                <foreach item="dn" collection="dns" open="(" separator="," close=")">
                    #{dn}
                </foreach>
            </if>
            <if test="taskIds != null and taskIds.size() > 0">
                and tati.TASK_ID in
                <foreach item="item" index="index" collection="taskIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="indicatorName != '' and indicatorName != null">
                and UPPER(tati.INDEX_NAME) like CONCAT('%',UPPER(#{indicatorName}),'%') escape '|'
            </if>
            and taat.TASK_TYPE=1
        </where>
    </select>

    <select id="getOutlierByIndicatorId" resultMap="indicatorOutlier">
        select taio.TASK_ID, taio.INDICATOR_ID, taio.END_TIME,taio.DURATION, taio.START_TIME
        from TBL_AIOPS_INDICATOR_OUTLIER taio left join TBL_AIOPS_ANALYSIS_TASK taat
        on taio.TASK_ID=taat.TASK_ID
        where ((#{startTime} <![CDATA[ <= ]]> taio.START_TIME and
        #{endTime} <![CDATA[ > ]]> taio.START_TIME) or
        (#{startTime} <![CDATA[ > ]]> taio.START_TIME and
        (#{startTime} <![CDATA[ < ]]> taio.END_TIME or taio.END_TIME=0)))
        and taat.TASK_TYPE=1
        <if test="situationIds != null and situationIds.size() > 0">
            and taat.SITUATION_ID in
            <foreach item="item" index="index" collection="situationIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="indicatorIds != null and indicatorIds.size() > 0">
            and taio.INDICATOR_ID in
            <foreach item="item" index="index" collection="indicatorIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="taskIds != null and taskIds.size() > 0">
            and taio.TASK_ID in
            <foreach item="item" index="index" collection="taskIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by taio.START_TIME desc
    </select>

    <select id="getIndicatorOutlierListTotal" resultType="Integer">
        select IFNULL(COUNT(distinct ID),0) from TBL_AIOPS_INDICATOR_OUTLIER taio
        left join TBL_AIOPS_ANALYSIS_TASK taat on taio.TASK_ID=taat.TASK_ID
        where ((#{startTime} <![CDATA[ <= ]]> taio.START_TIME and
        #{endTime} <![CDATA[ > ]]> taio.START_TIME) or
        (#{startTime} <![CDATA[ > ]]> taio.START_TIME and
        (#{startTime} <![CDATA[ < ]]> taio.END_TIME or taio.END_TIME=0)))
        and taat.TASK_TYPE=1
        <if test="situationIds != null and situationIds.size() > 0">
            and taat.SITUATION_ID in
            <foreach item="item" index="index" collection="situationIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and taio.INDICATOR_ID=#{id} and taio.TASK_ID=#{taskId}
    </select>

    <select id="getIndicatorOutlierList" resultMap="indicatorOutlier">
        select taio.START_TIME,taio.END_TIME,taio.ID,taio.DURATION,taio.INDICATOR_ID,
        taio.CORRECT,taat.TASK_NAME,taat.ALARM_TYPE,taio.PROBABLE_CAUSE,taio.SWITCH_ALARM_FIELD,taat.REPORT_ALARM_ID
        ,ifnull(taat.REPORT_ALARM,0) as reportAlarm
        from TBL_AIOPS_INDICATOR_OUTLIER taio left join TBL_AIOPS_ANALYSIS_TASK taat
        on taio.TASK_ID=taat.TASK_ID
        where ((#{startTime} <![CDATA[ <= ]]> taio.START_TIME and
        #{endTime} <![CDATA[ > ]]> taio.START_TIME) or
        (#{startTime} <![CDATA[ > ]]> taio.START_TIME and
        (#{startTime} <![CDATA[ < ]]> taio.END_TIME or taio.END_TIME=0)))
        and taat.TASK_TYPE=1
        <if test="situationIds != null and situationIds.size() > 0">
            and taat.SITUATION_ID in
            <foreach item="item" index="index" collection="situationIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        and taio.INDICATOR_ID=#{id} and taio.TASK_ID=#{taskId} order by taio.START_TIME desc
        <if test="pageSize != null and offset != null">
            LIMIT #{pageSize} OFFSET #{offset}
        </if>
    </select>


    <select id="getErrorCount" resultType="Integer">
        select IFNULL(COUNT(*),0) from TBL_AIOPS_INDICATOR_OUTLIER taio
        left join TBL_AIOPS_ANALYSIS_TASK taat on taat.TASK_ID=taio.TASK_ID
        left join TBL_AIOPS_TASK_INDICATOR tati on taio.INDICATOR_ID = tati.INDICATOR_ID and taio.TASK_ID = tati.TASK_ID
        where taio.CORRECT=2 and taat.TASK_TYPE=1
        and ((#{startTime} <![CDATA[ <= ]]> taio.START_TIME and
        #{endTime} <![CDATA[ > ]]> taio.START_TIME) or
        (#{startTime} <![CDATA[ > ]]> taio.START_TIME and
        (#{startTime} <![CDATA[ < ]]> taio.END_TIME or taio.END_TIME=0)))
        <if test="dns != null and dns.size() > 0">
            and tati.dn in
            <foreach item="dn" collection="dns" open="(" separator="," close=")">
                #{dn}
            </foreach>
        </if>
    </select>

    <select id="getLogTotalExceptionCount" resultType="Integer">
        select IFNULL(COUNT(*),0) from TBL_AIOPS_LOG_DETECT_ABNORMAL_RESULT taio
        left join TBL_AIOPS_ANALYSIS_TASK taat on taat.TASK_ID=taio.TASK_ID
        where taat.TASK_TYPE=3
          and ((#{startTime} <![CDATA[ <= ]]> taio.START_TIME and
                #{endTime} <![CDATA[ > ]]> taio.START_TIME) or
               (#{startTime} <![CDATA[ > ]]> taio.START_TIME and
                (#{startTime} <![CDATA[ < ]]> taio.END_TIME or taio.END_TIME=0)))
    </select>
    
    <select id="getTotalExceptionCount" resultType="Integer">
        select IFNULL(COUNT(*),0) from TBL_AIOPS_INDICATOR_OUTLIER taio
        left join TBL_AIOPS_ANALYSIS_TASK taat on taat.TASK_ID=taio.TASK_ID
        left join TBL_AIOPS_TASK_INDICATOR tati on taio.INDICATOR_ID = tati.INDICATOR_ID and taio.TASK_ID = tati.TASK_ID
        where taat.TASK_TYPE=1
        and ((#{startTime} <![CDATA[ <= ]]> taio.START_TIME and
        #{endTime} <![CDATA[ > ]]> taio.START_TIME) or
        (#{startTime} <![CDATA[ > ]]> taio.START_TIME and
        (#{startTime} <![CDATA[ < ]]> taio.END_TIME or taio.END_TIME=0)))
        <if test="dns != null and dns.size() > 0">
            and tati.dn in
            <foreach item="dn" collection="dns" open="(" separator="," close=")">
                #{dn}
            </foreach>
        </if>
    </select>

    <select id="getLogErrorCount" resultType="Integer">
        select IFNULL(COUNT(*),0) from TBL_AIOPS_LOG_DETECT_ABNORMAL_RESULT taio
        left join TBL_AIOPS_ANALYSIS_TASK taat on taat.TASK_ID=taio.TASK_ID
        where taio.CORRECT=2 and taat.TASK_TYPE=3
          and ((#{startTime} <![CDATA[ <= ]]> taio.START_TIME and
                #{endTime} <![CDATA[ > ]]> taio.START_TIME) or
               (#{startTime} <![CDATA[ > ]]> taio.START_TIME and
                (#{startTime} <![CDATA[ < ]]> taio.END_TIME or taio.END_TIME=0)))
    </select>
    
    <update id="markIndicatorOutlier" parameterType="java.util.List">
        BEGIN
        <foreach collection="ids" item="item" index="index" separator="">
            update TBL_AIOPS_INDICATOR_OUTLIER
            <set>
                CORRECT=#{isCorrect}
            </set>
            where ID=#{item};
        </foreach>
        END
    </update>

    <select id="getIndicatorById" resultMap="indicator">
       select * from TBL_AIOPS_TASK_INDICATOR tati
       left join TBL_AIOPS_ANALYSIS_TASK taat on taat.TASK_ID=tati.TASK_ID
       where tati.INDICATOR_ID=#{id} and taat.TASK_TYPE=1 limit 1
    </select>

    <select id="getSingleIndicator" resultMap="indicator">
        select *
        from TBL_AIOPS_TASK_INDICATOR indicator
        left join TBL_AIOPS_ANALYSIS_TASK task ON task.TASK_ID = indicator.TASK_ID
        where indicator.INDICATOR_ID = #{indicatorId}
          and indicator.TASK_ID = #{taskId}
        <if test="dns != null and dns.size() > 0">
            and indicator.dn in
            <foreach item="dn" collection="dns" open="(" separator="," close=")">
                #{dn}
            </foreach>
        </if>
        <if test="userId != null">
            AND task.USER_ID = #{userId}
        </if>
        limit 1
    </select>

    <insert id="insertTaskExectionResult">
        insert into TBL_AIOPS_TASK_EXECUTION_RESULT (
        TASK_ID,START_TIME,TASK_PERIOD_TYPE
        <if test="endTime != null and endTime !=''">
            ,END_TIME
        </if>
        <if test="status != null and status !=''">
            ,STATUS
        </if>
        ) values (
        #{taskId},#{startTime},#{taskPeriodType}
        <if test="endTime != null and endTime !=''">
            ,#{endTime}
        </if>
        <if test="status != null and status !=''">
            ,#{status}
        </if>
        )
    </insert>

    <update id="updateTrainAddress">
        update TBL_AIOPS_ANALYSIS_TASK set TRAIN_ADDRESS = #{trainAddress} where TASK_ID = #{taskId}
    </update>

    <update id="updateResult">
        update TBL_AIOPS_TASK_EXECUTION_RESULT set status = #{status} where TASK_ID = #{taskId} and
        START_TIME = #{startTime} and TASK_PERIOD_TYPE = #{taskPeriodType}
    </update>

    <delete id="deleteTaskExectionResult" timeout="120">
        delete from TBL_AIOPS_TASK_EXECUTION_RESULT where TASK_ID = #{taskId}
    </delete>

    <select id="getUpperAndLowerThresholdList" resultMap="upperAndLowerThreshold" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
       select INDICATOR_ID,COLLECT_TIME,UPPER_THRESHOLD,LOWER_THRESHOLD from ^{tableName}
       where INDICATOR_ID=#{id} and COLLECT_TIME between #{startTime} and #{endTime} order by COLLECT_TIME
    </select>


    <select id="getIndicatorThresholdlist" parameterType="java.util.List" resultMap="indicatorThreshold" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select tati.INDICATOR_ID INDICATOR_ID, MAX(tati.COLLECT_TIME) COLLECT_TIME from ^{indicatorThersholdTableName} tati
        where tati.COLLECT_TIME <![CDATA[ > ]]> #{startTime} and tati.INDICATOR_ID in
        <foreach collection="indicatorIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by tati.INDICATOR_ID
    </select>

    <select id="getAlgorithmParam" resultType="String">
       select ALGORITHM_PARAM from TBL_AIOPS_ANALYSIS_TASK where TASK_ID = #{taskId}
    </select>

    <select id="getTaskIdsByIndicatorId" resultType="Integer">
       select taat.TASK_ID from TBL_AIOPS_ANALYSIS_TASK taat
       left join TBL_AIOPS_TASK_INDICATOR tati on taat.TASK_ID=tati.TASK_ID
       where tati.INDICATOR_ID = #{id} and taat.TASK_TYPE=1
       order by taat.UPDATE_TIME desc
    </select>

    <delete id="deleteHistoricalIndicatorThresholds" timeout="120" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        delete
        from ^{thresholdTableName}
        where COLLECT_TIME <![CDATA[ <= ]]> #{dataOversizeTimeInMillis} limit 50000
    </delete>

    <insert id="insertIndicatorThreshold" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        insert into ^{tableName} (INDICATOR_ID, COLLECT_TIME,UPPER_THRESHOLD, LOWER_THRESHOLD)
        values
        <foreach collection="list" item="info" index="index" separator=",">
            (
            #{info.indicatorId,jdbcType=VARCHAR},
            #{info.collectTime,jdbcType=BIGINT},
            #{info.upperThreshold,jdbcType=DOUBLE},
            #{info.lowerThreshold,jdbcType=DOUBLE}
            )
        </foreach>
    </insert>

    <select id="getIndicatorCustomSource" resultMap="subIndicator">
        select INDICATOR_ID,ORIGINAL_VALUE,DN_NAME from TBL_AIOPS_TASK_INDICATOR
        where INDICATOR_ID in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getIndicatorDnsById" resultType="String">
        select DN from TBL_AIOPS_TASK_INDICATOR where INDICATOR_ID = #{id} limit 1
    </select>

    <select id="selectBatchThreshold" resultMap="indicatorThreshold" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select * from ^{tableName}
        where
        <foreach collection="list" item="info" index="index" open="(" close=")" separator="or">
            INDICATOR_ID = #{info.indicatorId} and
            COLLECT_TIME = #{info.collectTime}
        </foreach>
    </select>

    <select id="getIndicatorCountByTaskId" resultType="Integer">
        select count(distinct INDICATOR_ID) from TBL_AIOPS_TASK_INDICATOR
        where TASK_ID = #{taskId}
    </select>

    <select id="getIndicatorCountByTaskIdFilterSoftDelete" resultType="Integer">
        select count(distinct INDICATOR_ID) from TBL_AIOPS_TASK_INDICATOR
        where TASK_ID = #{taskId}
        and (SOFT_DELETE is null or SOFT_DELETE = false)
    </select>

    <select id="getUnitedIndicatorCountByTaskId" resultType="Integer">
        select count(distinct UNITED_ID) from TBL_AIOPS_TASK_UNITED_INDICATOR
        where TASK_ID = #{taskId}
    </select>

    <select id="getIndicatorOutlierById" resultMap="indicatorOutlier">
        select * from TBL_AIOPS_INDICATOR_OUTLIER where ID = #{id}
    </select>

    <select id="getIndicatorOutlierByTime" resultMap="indicatorOutlier">
        select *
        from TBL_AIOPS_INDICATOR_OUTLIER
        where TASK_ID = #{taskId}
          and INDICATOR_ID = #{indicatorId}
          and #{executeTime,jdbcType=BIGINT} <![CDATA[ >= ]]> START_TIME
          and (#{executeTime,jdbcType=BIGINT} <![CDATA[ <= ]]> END_TIME or END_TIME = 0)
    </select>

    <update id="updateSwitchAlarmField">
        BEGIN
        <foreach collection="list" index="index" item="info" separator="">
            UPDATE
            TBL_AIOPS_INDICATOR_OUTLIER
            SET SWITCH_ALARM_FIELD = #{info.switchAlarmField}
            WHERE ID = #{info.id};
        </foreach>
        END;
    </update>

    <update id="clearSwitchAlarmField">
        update TBL_AIOPS_INDICATOR_OUTLIER set SWITCH_ALARM_FIELD = null where TASK_ID = #{taskId}
    </update>

    <select id="getNotEndIndicatorOutlierByTaskId" resultMap="indicatorOutlier">
        select * from TBL_AIOPS_INDICATOR_OUTLIER where TASK_ID = #{taskId} and END_TIME=0
    </select>

    <delete id="deleteDuplicateIndicatorThresholds" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        delete
        from ^{tableName}
        where
            COLLECT_TIME <![CDATA[ >= ]]> #{startTime,jdbcType=BIGINT} AND
            COLLECT_TIME <![CDATA[ <= ]]> #{endTime,jdbcType=BIGINT}
            <if test="indicatorIds != null and indicatorIds.size() > 0">
                AND INDICATOR_ID in
                <foreach item="indicatorId" collection="indicatorIds" open="(" separator="," close=")">
                    #{indicatorId}
                </foreach>
            </if>
    </delete>

    <select id="getIndicatorListByDisasterRecovery" resultMap="indicator">
        select t2.* from (
        select tati.MEAS_UNIT_NAME,tati.INDEX_NAME,tati.DISPLAY_VALUE,tati.DN,tati.UNIT,tati.GROUP_ID,
        tati.INDICATOR_ID,tati.MEAS_TYPE_KEY,tati.MEAS_UNIT_KEY,tati.MO_TYPE,tati.TASK_ID,taat.TASK_NAME,tati.PQL,
        taio.lastOutlierTime as lastOutlierTime,
        (case when tati.ABNORMAL = 1 then 1 else 0 end) as indicatorStatus
        from TBL_AIOPS_TASK_INDICATOR tati
        left join TBL_AIOPS_ANALYSIS_TASK taat on tati.TASK_ID=taat.TASK_ID
        left join (select taio.INDICATOR_ID, taio.TASK_ID, max(taio.START_TIME) as lastOutlierTime
        from TBL_AIOPS_INDICATOR_OUTLIER taio where ((#{startTime} <![CDATA[ <= ]]> taio.START_TIME and
        #{endTime} <![CDATA[ > ]]> taio.START_TIME) or
        (#{startTime} <![CDATA[ > ]]> taio.START_TIME and
        (#{startTime} <![CDATA[ < ]]> taio.END_TIME or taio.END_TIME=0))) group by taio.INDICATOR_ID, taio.TASK_ID) taio
        on taio.TASK_ID = tati.TASK_ID and taio.INDICATOR_ID = tati.INDICATOR_ID
        <where>
        taat.TASK_TYPE=1 and
        <if test="taskIndicatorIds != null and taskIndicatorIds.size() > 0">
            <foreach item="item" index="index" collection="taskIndicatorIds" open="(" separator="or" close=")">
                (tati.INDICATOR_ID = #{item.indicatorId} and tati.TASK_ID = #{item.taskId})
            </foreach>
        </if>
        </where>) t2
        order by indicatorStatus desc,lastOutlierTime desc nulls last
        <if test="pageSize != null and offset != null">
            LIMIT #{pageSize} OFFSET #{offset}
        </if>
    </select>

    <select id="getIndicatorListTotalByDisasterRecovery" resultType="Integer">
        select count(*) from (select
        (case when tati.ABNORMAL=1 then 1 else 0 end) as indicatorStatus,
        tati.DN,
        tati.INDEX_NAME as indexName,
        tati.INDICATOR_ID,
        tati.DISPLAY_VALUE as measObject
        from TBL_AIOPS_TASK_INDICATOR tati
        left join TBL_AIOPS_ANALYSIS_TASK taat on tati.TASK_ID=taat.TASK_ID
        <where>
        taat.TASK_TYPE=1 and
        <if test="taskIndicatorIds != null and taskIndicatorIds.size() > 0">
            <foreach item="item" index="index" collection="taskIndicatorIds" open="(" separator="or" close=")">
                (tati.INDICATOR_ID = #{item.indicatorId} and tati.TASK_ID = #{item.taskId})
            </foreach>
        </if>
        </where>) t2
    </select>

    <select id="getIndicatorOutlierByAlarm" resultMap="indicatorOutlier">
        select * from TBL_AIOPS_INDICATOR_OUTLIER where SWITCH_ALARM_FIELD IN
        <foreach item="alarmUUID" collection="alarmUUIDs" open="(" separator="," close=")">
            #{alarmUUID}
        </foreach>
    </select>

    <select id="queryRunningAndReportAlarmTaskList" resultMap="businessTopoAlarm">
        select task.TASK_ID,
               task.REPORT_ALARM_ID,
               ind.INDICATOR_ID,
               ind.MEAS_UNIT_KEY,
               ind.MEAS_TYPE_KEY,
               ind.DN,
               ind.MO_TYPE,
               ind.DISPLAY_VALUE,
               ind.ORIGINAL_VALUE
        from TBL_AIOPS_TASK_INDICATOR ind
            left join TBL_AIOPS_ANALYSIS_TASK task on ind.TASK_ID = task.TASK_ID
        where task.TASK_TYPE = 1
            and task.REPORT_ALARM_ID is not null
        order by ind.INDICATOR_ID DESC
        <if test="paging != null and paging.pageSize != null and paging.offset != null">
            limit #{paging.pageSize} offset #{paging.offset}
        </if>
    </select>

    <select id="getAbnormalIndicatorByDn" resultMap="abnormalIndicator">
        SELECT indi.*, outlier.START_TIME, outlier.END_TIME, outlier.DURATION, outlier.CORRECT, outlier.PROBABLE_CAUSE,
               outlier.SWITCH_ALARM_FIELD, task.TASK_NAME
        from (select * from TBL_AIOPS_INDICATOR_OUTLIER where END_TIME = 0) as outlier
                 left join TBL_AIOPS_TASK_INDICATOR indi on outlier.TASK_ID = indi.TASK_ID and outlier.INDICATOR_ID = indi.INDICATOR_ID
                 left join TBL_AIOPS_ANALYSIS_TASK task on outlier.TASK_ID = task.TASK_ID
        where indi.dn = #{dn}
        order by outlier.START_TIME desc
    </select>

    <select id="getThresholdStartTime" resultType="com.huawei.i2000.dvanalysisengineservice.business.common.dto.Pair" lang="com.huawei.baize.security.db.mybatis.AntiInjectionLanguageDriver">
        select INDICATOR_ID as `key`, min(COLLECT_TIME) as `value` from TBL_AIOPS_INDICATOR_THRESHOLD_^{taskId}
        where INDICATOR_ID in
        <foreach item="item" index="index" collection="indicatorIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by INDICATOR_ID
    </select>
</mapper>
