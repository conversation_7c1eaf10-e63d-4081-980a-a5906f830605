#
# Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
#

mo.connect.status.column.neName=名称
mo.connect.status.column.connectStatus=连接状态（来源于代理的连接状态汇聚）
mo.connect.status.column.connectStatus.NoDetect=未检测
mo.connect.status.column.connectStatus.Online=在线
mo.connect.status.column.connectStatus.Offline=离线
mo.connect.status.column.connectStatus.Invalid=无效值
mo.connect.status.column.adminStatus=管理状态
mo.connect.status.column.adminStatus.Init=初始
mo.connect.status.column.adminStatus.Locked=锁定
mo.connect.status.column.adminStatus.Unlocked=未锁定
mo.connect.status.column.adminStatus.ShutDown=关闭
mo.connect.status.column.adminStatus.Invalid=无效值

mo.connect.status.column.location=资源位置
mo.connect.status.column.parentName=父网元

mo.topo.status.graph.name.parent=父子关系拓扑
mo.topo.status.column.parent=父网元
mo.topo.status.column.target=目标网元
mo.topo.status.column.children=子网元
mo.topo.status.graph.name.deploy=部署关系拓扑
mo.topo.status.column.deploySrcMo=部署宿网元
mo.topo.status.column.deployDestMos=部署目标网元
mo.topo.status.column.solution=解决方案

alarm.column.csn=告警流水号
alarm.column.alarmId=告警ID
alarm.column.alarmName=名称
alarm.column.severity=级别
alarm.column.latestOccurUtc=发生时间
alarm.column.dn=告警源DN
alarm.column.neName=告警源名称
alarm.column.productName=告警源类型
alarm.column.additionalInformation=附加信息
alarm.column.moi=定位信息
alarm.column.count=发生次数
alarm.column.cleared=清除状态
alarm.column.probableCause=可能原因
alarm.column.nativeMoDn=资源标识
alarm.column.occurUtc=发生的时间
alarm.column.eventType=类型
alarm.column.deviceTypeId=设备类型标识
alarm.column.meName=资源名称
alarm.column.clearTime=清理时间
alarm.value.severity.1=紧急
alarm.value.severity.2=重要
alarm.value.severity.3=次要
alarm.value.severity.4=提示
alarm.value.cleared.0=未清除
alarm.value.cleared.1=已清除
alarm.value.eventType.1=通信告警
alarm.value.eventType.2=设备告警
alarm.value.eventType.3=处理错误告警
alarm.value.eventType.4=业务质量告警
alarm.value.eventType.5=环境告警
alarm.value.eventType.6=完整性告警
alarm.value.eventType.7=操作告警
alarm.value.eventType.8=物理资源告警
alarm.value.eventType.9=安全告警
alarm.value.eventType.10=时间域告警
alarm.value.eventType.11= 属性值改变
alarm.value.eventType.12=对象创建
alarm.value.eventType.13=对象删除
alarm.value.eventType.14=关系改变
alarm.value.eventType.15= 状态改变
alarm.value.eventType.16=路由改变
alarm.value.eventType.17=保护倒换
alarm.value.eventType.18=越限
alarm.value.eventType.19=文件传输状态
alarm.value.eventType.20=备份状态
alarm.value.eventType.21=心跳

mo.column.dn=网元
mo.column.type=网元类型
mo.column.version=版本
mo.column.displayName=网元名称
mo.column.medNodeID=med节点ID
mo.column.clientProperties=客户端属性
mo.column.children=子网元
mo.column.parent=父网元
mo.column.neStatus=网元状态

abnormal.indicator.column.indexName=测量指标
abnormal.indicator.column.measUnitName=测量单元
abnormal.indicator.column.displayValue=测量对象
abnormal.indicator.column.moType=网元类型
abnormal.indicator.column.moName=网元名称
abnormal.indicator.column.dn=DN
abnormal.indicator.column.taskName=所属任务
abnormal.indicator.column.startTime=最近一次异常发生时间
abnormal.indicator.column.probableCause=异常类型

mo.connect.status.description=共查询到匹配的网元{0}个，其中在线{1}个，不在线{2}个
mo.connect.status.description.single=查询到网元{0}的连接状态如下：

mo.health.alarm.description.not.empty=网元{0}正在发生的告警：
mo.health.alarm.description.empty=网元{0}未查询到正在发生的告警。
mo.health.kpi.description.not.empty=网元{0}的异常指标：
mo.health.kpi.description.empty=网元{0}未查询到异常指标。
current.alarm.description.not.empty=无当前告警信息
history.alarm.description.not.empty=无历史告警信息
alarm.result.empty=没有您要查询的告警信息。

mo.topo.info.description=查询到网元{0}的拓扑信息如下：

pm.kpi.export.mo.notFound.error=无法通过网元名称{0}查询到网元
pm.kpi.export.task.notFound.error=无法通过任务名{1}查询到网元{0}的性能任务
pm.kpi.export.task.notFound.byIndicator.error=无法通过任务名{1}和指标名{2}查询到网元{0}的性能任务
pm.kpi.export.time.range.noCorrect.error=时间格式输入有误。
pm.kpi.export.task.notFound.byMoType.error=无法网元{0}的查询到性能任务
pm.kpi.export.task.nodata.error=任务无数据
pm.kpi.query.comparison_nodata_error=在{0}-{1}区间内任务无数据

export.current.alarm.name=当前告警
export.mo.all.name=全部网元信息
export.pm.kpi.name=性能指标数据

workbench.execute.result.disk.usage.title=主机{0}磁盘占用情况Top{1}
workbench.execute.result.cpu.usage.title=主机{0}的CPU使用率Top{1}
workbench.execute.result.memory.usage.title=主机{0}内存使用率Top{1}
workbench.execute.result.file.store.time.limit=点击导出为文件（*提示: 文件仅保存12-24小时，若导出失败，可使用界面导出能力）。

disk.usage.column.path=路径或文件
disk.usage.column.usage=已使用(MB)

memory.usage.column.pid=进程
memory.usage.column.user=用户
memory.usage.column.usage=内存占用率

cpu.usage.column.pid=进程
cpu.usage.column.user=用户
cpu.usage.column.usage=CPU占用率

pm.kpi.export.managed.object=管理对象
pm.kpi.export.measurement.object=测量对象
pm.kpi.export.performance.counter=指标
pm.kpi.export.data.source=数据源
pm.kpi.export.view.name=视图名称
pm.kpi.chart.x.axis.name=时间
pm.kpi.chart.indicator.not.unique=环比图中测量指标个数不能重复
pm.kpi.chart.meas.object.not.unique=环比图中测量指标对象不能重复
pm.kpi.chart.diff.time.intervals=对比数据中两个时间段间隔不同
pm.kpi.chart.invalids.time.intervals=对比数据中两个时间段必须间隔天周期
pm.kpi.query.miss.parameter=网元和任务名必填
pm.kpi.query.oversize=查询到的数据条数超过可展示上限，当前仅展示最近的30000条数据


QUERY_GUIDE.template=查询{QUERY_NAME}
QUERY_GUIDE.regex=查询\\s*(.+)

QUERY_NE_CONNECTION_STATUS.template=查询网元{NE_NAME}的连接状态
QUERY_NE_CONNECTION_STATUS.regex=查询网元\\s*(.*?)\\s*(?:的连接状态|$)

QUERY_NE_HEALTH_STATUS.template=查询网元{NE_NAME}的健康状态
QUERY_NE_HEALTH_STATUS.regex=查询网元\\s*(.*?)\\s*(?:的健康状态|$)

QUERY_NE_TOPOLOGY.template=查询网元{NE_NAME}的拓扑信息
QUERY_NE_TOPOLOGY.regex=查询网元\\s*(.*?)\\s*(?:的拓扑信息|$)

QUERY_ALARM_DETAIL.template=查询告警{ALARM_ID_OR_NAME}
QUERY_ALARM_DETAIL.regex=查询告警\\s*(.+)

EXPORT_INFO.template=导出{EXPORT_NAME}
EXPORT_INFO.regex=导出\\s*(.+)

QUERY_NE_METRIC_DATA.template=查询网元{NE_NAME}任务{MEASURE_TASK_NAME}测量对象{MEASURE_OBJECT_NAME}测量指标{MEASURE_METRIC_NAME}时间区间{MEASURE_TIME_RANGE}内的数据
QUERY_NE_METRIC_DATA.regex=查询网元\\s*(.*?)\\s*(?:任务\\s*(.*?)\\s*(?:测量对象\\s*(.*?)\\s*(?:测量指标\\s*(.*?)\\s*(?:时间区间\\s*(.*?)\\s*(?:内的数据|$)|$)|$)|$)|$)

EXPORT_NE_METRIC_DATA.template=导出网元{NE_NAME}任务{MEASURE_TASK_NAME}测量对象{MEASURE_OBJECT_NAME}测量指标{MEASURE_METRIC_NAME}时间区间{MEASURE_TIME_RANGE}内的数据
EXPORT_NE_METRIC_DATA.regex=导出网元\\s*(.*?)\\s*(?:任务\\s*(.*?)\\s*(?:测量对象\\s*(.*?)\\s*(?:测量指标\\s*(.*?)\\s*(?:时间区间\\s*(.*?)\\s*(?:内的数据|$)|$)|$)|$)|$)

DIAGNOSE_ALARM.template=请诊断{NE_NAME}网元的告警{ALARM_ID_OR_NAME}的故障原因是什么？
DIAGNOSE_ALARM.regex=请?诊断\\s*(.*?)\\s*(?:网元的告警\\s*(.*?)\\s*(?:的故障原因是什么\\??|$)|$)

QUERY_ERROR_CODE.template=错误码{ERROR_CODE}
QUERY_ERROR_CODE.regex=错误码\\s*【?(\\d+)】?

# Query Names
query.name.biz_resource_management=业务资源管理
query.name.physical_resource_management=物理资源管理
query.name.north_ftp_user_guide=北向FTP用户指南
query.name.north_snmp_alarm_guide=北向SNMP告警接口调测指南
query.name.north_settings=北向设置
query.name.snmp_interface=SNMP接口
query.name.online_help=联机帮助
query.name.current_alarms=当前告警
query.name.history_alarms=历史告警

# Export Names
export.name.current_alarms=当前告警
export.name.network_element_info=网元信息

# Measurement Time Ranges
meas.time.range.last_hour=最近一小时
meas.time.range.last_day=近一天
meas.time.range.last_week=近一周
meas.time.range.last_month=过去一个月

recommend.bracket.left=【
recommend.bracket.right=】

com.huawei.i2000.dvAnalysisEngine.operation.source=智能运维
com.huawei.i2000.analysis.service.secondary.authentication.operation=二次认证
com.huawei.i2000.analysis.secondary.authentication.detail.success=二次认证成功
com.huawei.i2000.analysis.secondary.authentication.detail.failed=二次认证失败
com.huawei.i2000.analysis.service.user.authentication.operation=用户鉴权
com.huawei.i2000.analysis.service.authentication.failed=鉴权失败。
com.huawei.i2000.analysis.service.operation.capacityConfig.operation=修改公共配置配置项

export.alarm.file=导出告警信息
export.alarm.file.detail=文件名称：{0}

