/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.aiagent;

import lombok.Getter;
import lombok.Setter;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/1/9
 */

@Getter
@Setter
public class AbnormalIndicator {
    private String indicatorId;

    private Integer taskId;

    private String taskName;

    private String measUnitKey;

    private String measUnitName;

    private String ip;

    private String moName;

    private String dn;

    private String indexKey;

    private String indexName;

    private String moType;

    private String displayValue;

    private String originValue;

    private String unit;

    private Integer indicatorStatus;

    private String lastOutlierTime;

    private String startTime;

    private String endTime;

    private String duration;

    private Integer correct;

    private Integer alarmType;

    private String probableCause;

    private Integer reportAlarm;

    private String switchAlarmField;

    private String reportAlarmId;
}
