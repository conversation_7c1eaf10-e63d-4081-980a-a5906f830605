/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvanalysisengineservice.business.common.ConfigurationUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.FileCleanUtils;
import com.huawei.i2000.dvanalysisengineservice.business.common.PropertiesUtil;
import com.huawei.i2000.dvanalysisengineservice.business.resourceschedule.util.ResourceScheduleUtil;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.ScheduleConstant;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.AbstractHandleDto;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.DataSourceIndicatorInfo;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.DataSourceInfo;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.HistoryDatas;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.HistoryIndexValue;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.HistoryQueryData;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.HistoryQueryValue;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.PerformanceIndexValueSchedule;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.ProductPortrait;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.ResourceScheduleExecuteDetail;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.ResourceScheduleHandleDto;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.ResourceScheduleHistoryDataDto;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.ScheduleTaskConfig;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.VmPortrait;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.indicatorpredict.PredictConstant;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.ResourceScheduleDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.model.IndicatorDetil;
import com.huawei.i2000.dvanalysisengineservice.model.IndicatorUnit;
import com.huawei.i2000.dvanalysisengineservice.model.PerformanceIndexValue;
import com.huawei.i2000.dvanalysisengineservice.model.TaskIndicator;
import com.huawei.i2000.dvanalysisengineservice.util.ApplicationContextHelper;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.TreeMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 潮汐调度数据源
 *
 * <AUTHOR>
 * @since 2023-05-05
 */
public class ResourceScheduleDataSourceHandler extends AbstractDataSourceHandler {
    private static final OssLog LOGGER = OssLogFactory.getLogger(ResourceScheduleDataSourceHandler.class);

    private static final String SEPARATOR = "_";

    private static final String SUFFIX = ".csv";

    private static final String COMMA = ",";

    private static final String MAX = "MAX";

    private static final String EXTENDED_THRESHOLD = "extendedthreshold";

    private static final String TRAIN_HISTORY_FILE_HEADER = "collect_time,vm_key,sum_kpi,num_value";

    private static final String DETECT_HISTORY_FILE_HEADER = "collect_time,vm_key,sum_kpi,num_value,schedule_num,schedule_status";

    private static final String DETECT_PREDICT_FILE_HEADER = "collect_time,vm_key,sum_kpi";

    private static final String COLLECT_PREDICT_FILE_HEADER = "collect_time,vm_key,sum_kpi,schedule_num";


    @Override
    public DataSourceInfo handle(AbstractHandleDto dataInfo) {
        ResourceScheduleHandleDto task;
        if (dataInfo instanceof ResourceScheduleHandleDto) {
            task = (ResourceScheduleHandleDto) dataInfo;
        } else {
            LOGGER.error("dataInfo type error, dataInfo = {}", dataInfo);
            return null;
        }
        LOGGER.info("handle task start, taskName = {}, taskId = {}", task.getTaskName(), task.getTaskId());

        if (task.getOperationType() == ScheduleConstant.TRAINNING_TASK) {
            return handleTrain(task);
        } else if (task.getOperationType() == ScheduleConstant.PREDICT_TASK) {
            return handlePredict(task);
        } else {
            return handleCollect(task);
        }
    }

    private DataSourceInfo handleTrain(ResourceScheduleHandleDto task) {
        List<HistoryDatas> performanceQueryModel = getPerformanceQueryModel(task);
        String historyFileName = "train_history_data_" + task.getTaskId() + SEPARATOR + task.getTriggerTime() + SUFFIX;
        File historyFile = writeFileHeader(TRAIN_HISTORY_FILE_HEADER, historyFileName);
        if (createFailed(historyFile)) {
            LOGGER.error("create train csv file error");
            return null;
        }
        ResourceScheduleDao resourceScheduleDao = (ResourceScheduleDao) ApplicationContextHelper
            .getBean("resourceScheduleDao");

        // key:时间戳，value:CPU使用总数
        Map<String, Integer> cpuUseMap = new HashMap<>();
        // key:时间戳，value:Memory使用总数
        Map<String, Integer> memoryUseMap = new HashMap<>();

        DataSourceInfo dataSourceInfo = new DataSourceInfo();

        // 对于每个集群，查询性能数据
        for (HistoryDatas historyDatas : performanceQueryModel) {
            // 标志位记录集群是否缺少vm的数据
            boolean clusterLackVmsData = true;

            // 如果集群里面vm太多，会oom，所以需要分割时间段查询，无论多少个vm，一次最多查40320个点，vm越多，查的点数越少
            List<IndicatorDetil> indicators = JSON.parseArray(historyDatas.getMo2Index(), IndicatorDetil.class);
            if (CollectionUtils.isEmpty(indicators)) {
                LOGGER.error("there is no indicators, check the cluster mos");
                continue;
            }
            long timeLength = (PredictConstant.MAX_TRAIN_NEED_POINTS / indicators.size()) * PredictConstant.ONE_MINUTE_MILLISECOND;
            long queryEndTime = 0L;
            long totalEndTime = historyDatas.getEndTime();
            do {
                queryEndTime = Long.min(historyDatas.getBeginTime() + timeLength, totalEndTime);
                historyDatas.setEndTime(queryEndTime);
                List<HistoryQueryData> queryData = getPmData(task, historyDatas);

                // 整理出每个时间点的集群平均、加和、实例数量数据并入库
                ResourceScheduleHistoryDataDto historyDataDto = convertPerformanceData(queryData, task, historyDatas.getClusterName());
                handleAndInsertHistoryData(historyDataDto, task, cpuUseMap, memoryUseMap, resourceScheduleDao);

                // 把性能数据转换成算法需要的格式以写入csv文件
                List<String> convertString = convertDataToStringTrain(historyDataDto, historyDatas.getClusterName());
                if (CollectionUtils.isNotEmpty(convertString)) {
                    clusterLackVmsData = false;
                }
                try {
                    printDataToFile(historyFile, convertString);
                } catch (IOException e) {
                    LOGGER.error("print to file error, e:", e);
                    return null;
                }

                historyDatas.setBeginTime(queryEndTime + 1L);
            } while (historyDatas.getBeginTime() <= totalEndTime);

            if (clusterLackVmsData) {
                dataSourceInfo.getResourceScheduleLackVms().add(historyDatas.getClusterName());
            }
        }
        LOGGER.info("handleTrain, create csv end, fileName={}", historyFile.getName());

        return getDataSourceInfo(task, historyFile, resourceScheduleDao, cpuUseMap, memoryUseMap, dataSourceInfo);
    }

    private DataSourceInfo getDataSourceInfo(ResourceScheduleHandleDto task, File historyFile,
        ResourceScheduleDao resourceScheduleDao, Map<String, Integer> cpuUseMap, Map<String, Integer> memoryUseMap,
        DataSourceInfo dataSourceInfo) {
        if (FileCleanUtils.fileSizeOverMax(historyFile)) {
            LOGGER.error("createAndPushFile error, file size oversize, file={}", historyFile.getName());
            deleteFile(historyFile);
            return null;
        }

        handleAndInsertSaveRate(task, resourceScheduleDao, cpuUseMap, memoryUseMap);

        dataSourceInfo.setSrcFilePath(TaskConstant.TEMP_SRC_PATH);
        dataSourceInfo.setDestFilePath(TaskConstant.TEMP_DEST_PATH);
        LOGGER.info("handle train end, taskName = {}", task.getTaskName());
        return dataSourceInfo;
    }

    private DataSourceInfo handlePredict(ResourceScheduleHandleDto task) {
        List<HistoryDatas> performanceQueryModel = getPerformanceQueryModel(task);
        String historyFileName = "detect_history_data_" + task.getTaskId() + SEPARATOR + task.getTriggerTime() + SUFFIX;
        String predictFileName = "detect_predict_data_" + task.getTaskId() + SEPARATOR + task.getTriggerTime() + SUFFIX;
        File historyFile = writeFileHeader(DETECT_HISTORY_FILE_HEADER, historyFileName);
        File predictFile = writeFileHeader(DETECT_PREDICT_FILE_HEADER, predictFileName);
        if (createFailed(historyFile) || createFailed(predictFile)) {
            LOGGER.error("create detect csv file error");
            return null;
        }
        ResourceScheduleDao resourceScheduleDao = (ResourceScheduleDao) ApplicationContextHelper
            .getBean("resourceScheduleDao");

        // key:时间戳，value:CPU使用总数
        Map<String, Integer> cpuUseMap = new HashMap<>();
        // key:时间戳，value:Memory使用总数
        Map<String, Integer> memoryUseMap = new HashMap<>();

        // 对于每个集群，查询性能数据
        for (HistoryDatas historyDatas : performanceQueryModel) {
            List<HistoryQueryData> queryData = getPmData(task, historyDatas);
            LOGGER.debug("handlePredict, getPmData end, clusterName = {}", historyDatas.getClusterName());

            // 检查集群是否每个实例都有新增性能数据，且上报时间同步
            if (!allVmsNormal(queryData, task.getTriggerTime(), historyDatas.getClusterName(), historyDatas.getPeriod())) {
                LOGGER.error("[ResourceScheduleDataSourceHandler] vms performance data is not complete,"
                        + " taskId = {}, clusterName = {}", task.getTaskId(), historyDatas.getClusterName());
                continue;
            }

            // 整理出每个时间点的集群平均、加和、实例数量数据并入库
            ResourceScheduleHistoryDataDto historyData = convertPerformanceData(queryData, task, historyDatas.getClusterName());
            handleAndInsertHistoryData(historyData, task, cpuUseMap, memoryUseMap, resourceScheduleDao);

            // 根据历史预测指标曲线、历史真实指标曲线，计算预测误差率并入库
            List<PerformanceIndexValue> historyAvgList = resourceScheduleDao.getVmAvgPredict(task.getTaskId(),
                historyDatas.getClusterName(), historyDatas.getBeginTime(), historyDatas.getEndTime());
            handlePredictAccuracy(task.getTaskId(), historyDatas.getClusterName(), historyAvgList,
                historyData.getAvgMap(), resourceScheduleDao);

            // 查询当前扩缩情况
            ResourceScheduleExecuteDetail executeDetail = resourceScheduleDao.getLastScheduleExecuteDetail(task.getTaskId(),
                historyDatas.getClusterName(), historyDatas.getBeginTime(), historyDatas.getEndTime());

            // 查询历史的资源调度线，获取调度目标数量
            Map<String, String> historyScheduleMap = getHistoryScheduleMap(task, resourceScheduleDao, historyDatas);

            // 把性能数据转换成算法需要的格式以写入csv文件
            List<String> convertStringHistory = convertDataToStringDetectHistory(historyData,
                historyDatas.getClusterName(), historyScheduleMap, executeDetail);
            List<String> convertStringPredict = convertDataToStringDetectPredict(task.getTaskId(),
                task.getTriggerTime(), historyDatas, resourceScheduleDao);
            if (CollectionUtils.isEmpty(convertStringHistory) || CollectionUtils.isEmpty(convertStringPredict)) {
                LOGGER.error("convertDataToString error, line is empty, fileName={}", historyFile.getName());
                continue;
            }

            // 输出到文件
            if (!printDataToFile(historyFile, predictFile, convertStringHistory, convertStringPredict)) {
                return null;
            }
        }

        LOGGER.info("handlePredict, create csv end, fileName={} and {}", historyFile.getName(), predictFile.getName());

        handleAndInsertSaveRate(task, resourceScheduleDao, cpuUseMap, memoryUseMap);
        return getDataSourceInfo(task.getTaskName(), historyFile, predictFile);
    }

    private void handleAndInsertSaveRate(ResourceScheduleHandleDto task,
        ResourceScheduleDao resourceScheduleDao, Map<String, Integer> cpuUseMap, Map<String, Integer> memoryUseMap) {
        ProductPortrait productPortrait = resourceScheduleDao.getProductPortrait(task.getTaskId());
        if (productPortrait == null) {
            LOGGER.error("handleAndInsertHistoryData error, productPortrait is null");
            return;
        }
        int baseLineCpuNumber = productPortrait.getBaseLineCpuNumber();
        int baseLineMemoryNumber = productPortrait.getBaseLineMemoryNumber();

        List<PerformanceIndexValue> cpuSaveList = new ArrayList<>();
        List<PerformanceIndexValue> memorySaveList = new ArrayList<>();

        for (Map.Entry<String, Integer> entry : cpuUseMap.entrySet()) {
            if (!memoryUseMap.containsKey(entry.getKey())) {
                continue;
            }
            BigDecimal saveCpu = BigDecimal.valueOf((baseLineCpuNumber - entry.getValue()) /
                (float) baseLineCpuNumber * 100);
            BigDecimal saveMemory = BigDecimal.valueOf((baseLineMemoryNumber - memoryUseMap.get(entry.getKey())) /
                (float) baseLineMemoryNumber * 100);
            PerformanceIndexValue cpu = new PerformanceIndexValue();
            cpu.setTimestampStr(entry.getKey());
            cpu.setIndexValue(String.valueOf(saveCpu.setScale(0, RoundingMode.HALF_UP).floatValue()));
            PerformanceIndexValue memory = new PerformanceIndexValue();
            memory.setTimestampStr(entry.getKey());
            memory.setIndexValue(String.valueOf(saveMemory.setScale(0, RoundingMode.HALF_UP).floatValue()));
            cpuSaveList.add(cpu);
            memorySaveList.add(memory);
        }
        try {
            resourceScheduleDao.updateCpuSaveRate(task.getTaskId(), cpuSaveList);
            resourceScheduleDao.updateMemorySaveRate(task.getTaskId(), memorySaveList);
        } catch (ServiceException e) {
            LOGGER.error("[handleAndInsertHistoryData] insertHistoryData error, e={}", e);
        }
    }

    private Map<String, String> getHistoryScheduleMap(ResourceScheduleHandleDto task,
        ResourceScheduleDao resourceScheduleDao, HistoryDatas historyDatas) {
        List<PerformanceIndexValue> historyScheduleData = resourceScheduleDao.getScheduleData(task.getTaskId(),
            historyDatas.getClusterName(), historyDatas.getBeginTime(), historyDatas.getEndTime());
        Map<String, String> historyScheduleMap = new HashMap<>();
        for (PerformanceIndexValue scheduleData : historyScheduleData) {
            historyScheduleMap.put(scheduleData.getTimestampStr(), scheduleData.getIndexValue());
        }
        return historyScheduleMap;
    }

    private void handlePredictAccuracy(Integer taskId, String clusterName, List<PerformanceIndexValue> historyAvgList,
        Map<Long, Float> historyMap, ResourceScheduleDao resourceScheduleDao) {
        List<PerformanceIndexValue> accuracyList = new ArrayList<>();
        for (PerformanceIndexValue value : historyAvgList) {
            if (historyMap.containsKey(Long.parseLong(value.getTimestampStr()))) {
                // 分母为零时，如果分子不为零，则预测误差率为100%
                if (historyMap.get(Long.parseLong(value.getTimestampStr())) == 0 && Float.parseFloat(value.getIndexValue()) != 0) {
                    PerformanceIndexValue indexValue = new PerformanceIndexValue();
                    indexValue.setTimestampStr(value.getTimestampStr());
                    indexValue.setIndexValue(String.valueOf(100));
                    accuracyList.add(indexValue);
                    continue;
                } else if (historyMap.get(Long.parseLong(value.getTimestampStr())) == 0 && Float.parseFloat(value.getIndexValue()) == 0) {
                    PerformanceIndexValue indexValue = new PerformanceIndexValue();
                    indexValue.setTimestampStr(value.getTimestampStr());
                    indexValue.setIndexValue(String.valueOf(0));
                    accuracyList.add(indexValue);
                    continue;
                }
                BigDecimal accuracy = BigDecimal.valueOf(Math.abs(Float.parseFloat(value.getIndexValue()) -
                    historyMap.get(Long.parseLong(value.getTimestampStr()))) /
                    historyMap.get(Long.parseLong(value.getTimestampStr())) * 100);
                float accuracyValue = accuracy.setScale(0, RoundingMode.HALF_UP).floatValue();
                PerformanceIndexValue indexValue = new PerformanceIndexValue();
                indexValue.setTimestampStr(value.getTimestampStr());
                indexValue.setIndexValue(String.valueOf(accuracyValue));
                accuracyList.add(indexValue);
            }
        }
        resourceScheduleDao.updatePredictAccuracy(taskId, clusterName, accuracyList);
    }

    private DataSourceInfo handleCollect(ResourceScheduleHandleDto task) {
        String historyFileName = "collect_history_data_" + task.getTaskId() + SEPARATOR + task.getTriggerTime() + SUFFIX;
        String predictFileName = "collect_predict_data_" + task.getTaskId() + SEPARATOR + task.getTriggerTime() + SUFFIX;
        File historyFile = writeFileHeader(DETECT_HISTORY_FILE_HEADER, historyFileName);
        File predictFile = writeFileHeader(COLLECT_PREDICT_FILE_HEADER, predictFileName);
        if (createFailed(historyFile) || createFailed(predictFile)) {
            LOGGER.error("create collect csv file error");
            return null;
        }
        ResourceScheduleDao resourceScheduleDao = (ResourceScheduleDao) ApplicationContextHelper
            .getBean("resourceScheduleDao");

        // 对于每个集群，查询性能数据
        for (TaskIndicator taskIndicator : task.getIndicatorList()) {
            int period = getPeriod(taskIndicator.getMoType(), taskIndicator.getMeasUnitKey());
            long startTime = task.getTriggerTime() - (long) period * task.getCollectNum() * 1000;
            long endTime = task.getTriggerTime() + (long) period * task.getCollectNum() * 1000;

            // 查询历史实例数量
            List<PerformanceIndexValue> vmNumberHistory = resourceScheduleDao.getVmNumberHistory(task.getTaskId(),
                taskIndicator.getClusterName(), startTime, task.getTriggerTime());
            if (CollectionUtils.isEmpty(vmNumberHistory)) {
                LOGGER.error("get vmNumberHistory error, taskId = {}", task.getTaskId());
                continue;
            }

            // 查询历史加和
            List<PerformanceIndexValue> vmSumHistory = resourceScheduleDao.getVmSumHistory(task.getTaskId(),
                taskIndicator.getClusterName(), startTime, task.getTriggerTime());
            Map<String, String> vmSumHistoryMap = new HashMap<>();
            for (PerformanceIndexValue sumData : vmSumHistory) {
                vmSumHistoryMap.put(sumData.getTimestampStr(), sumData.getIndexValue());
            }

            // 查询历史的资源调度线
            List<PerformanceIndexValue> vmScheduleHistory = resourceScheduleDao.getScheduleData(task.getTaskId(),
                taskIndicator.getClusterName(), startTime, task.getTriggerTime());
            Map<String, String> vmScheduleHistoryMap = new HashMap<>();
            for (PerformanceIndexValue scheduleData : vmScheduleHistory) {
                vmScheduleHistoryMap.put(scheduleData.getTimestampStr(), scheduleData.getIndexValue());
            }

            // 查询当前扩缩情况
            ResourceScheduleExecuteDetail executeDetail = resourceScheduleDao.getLastScheduleExecuteDetail(
                task.getTaskId(), taskIndicator.getClusterName(), startTime, task.getTriggerTime());

            // 把性能数据转换成算法需要的格式以写入csv文件
            List<String> convertStringHistory = convertDataToStringCollectHistory(taskIndicator.getClusterName(),
                vmNumberHistory, vmSumHistoryMap, vmScheduleHistoryMap, executeDetail);
            List<String> convertStringPredict = convertDataToStringCollectPredict(task.getTaskId(), taskIndicator
                .getClusterName(), taskIndicator.getUnitedId(), task.getTriggerTime(), endTime, resourceScheduleDao);
            if (CollectionUtils.isEmpty(convertStringHistory) || CollectionUtils.isEmpty(convertStringPredict)) {
                LOGGER.error("convertDataToString error, line is empty, fileName={}", historyFile.getName());
                continue;
            }

            // 输出到文件
            if (!printDataToFile(historyFile, predictFile, convertStringHistory, convertStringPredict)) {
                return null;
            }
        }

        LOGGER.info("handleCollect, create csv end, fileName={} and {}", historyFile.getName(), predictFile.getName());

        return getDataSourceInfo(task.getTaskName(), historyFile, predictFile);
    }

    private List<HistoryQueryData> getPmData(ResourceScheduleHandleDto task, HistoryDatas queryCondition) {
        List<HistoryQueryData> queryData;
        Future<List<HistoryQueryData>> future;
        long startTime = System.currentTimeMillis();
        LOGGER.debug("executePm start time={}", startTime);
        if (task.getOperationType() == ScheduleConstant.TRAINNING_TASK) {
            future = performanceDataExecutor.executeTraining(queryCondition);
        } else {
            future = performanceDataExecutor.executeOther(queryCondition);
        }
        try {
            queryData = future.get(TaskConstant.FUTURE_GET_TIME_OUT, TimeUnit.MILLISECONDS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            LOGGER.error("getPmData get data error", e);
            queryData = new ArrayList<>();
        }
        LOGGER.debug("executePm cost time={}", Math.subtractExact(System.currentTimeMillis(), startTime));
        return queryData;
    }

    @Override
    public Map<String, DataSourceIndicatorInfo> multipleHandle(AbstractHandleDto dataInfo) {
        return null;
    }

    private List<HistoryDatas> getPerformanceQueryModel(ResourceScheduleHandleDto task) {
        // 获取查询性能数据的入参
        List<IndicatorUnit> indicatorUnitList = convertToIndicatorUnit(task);
        if (CollectionUtils.isEmpty(indicatorUnitList)) {
            LOGGER.error("indicatorUnitList is empty, taskId = {}, taskName = {}", task.getTaskId(),
                task.getTaskName());
            return new ArrayList<>();
        }
        return getHistoryDatas(task, indicatorUnitList);
    }

    private List<IndicatorUnit> convertToIndicatorUnit(ResourceScheduleHandleDto task) {
        List<IndicatorUnit> result = new ArrayList<>(task.getIndicatorList().size());
        List<TaskIndicator> selectIndicates = task.getIndicatorList();
        if (CollectionUtils.isEmpty(selectIndicates)) {
            LOGGER.error("[ResourceScheduleDataSourceHandler] task indicatorList is empty, indicatorList = {}",
                task.getIndicatorList());
            return result;
        }
        for (TaskIndicator taskIndicator : selectIndicates) {
            IndicatorUnit indicatorUnit = new IndicatorUnit();
            indicatorUnit.setMeasUnitKey(taskIndicator.getMeasUnitKey());
            indicatorUnit.setMoType(taskIndicator.getMoType());
            indicatorUnit.setUnitedId(taskIndicator.getUnitedId());
            indicatorUnit.setClusterName(taskIndicator.getClusterName());
            List<IndicatorDetil> indicatorDetails = getIndicatorDetails(taskIndicator);
            indicatorUnit.setIndicatorDetilList(indicatorDetails);
            result.add(indicatorUnit);
        }
        return result;
    }

    private List<IndicatorDetil> getIndicatorDetails(TaskIndicator taskIndicator) {
        // 查询该集群下的主机实例
        List<ManagedObject> mos = ResourceScheduleUtil.queryMoListByCluster(taskIndicator.getClusterName(),
            taskIndicator.getSiteId());
        if (CollectionUtils.isEmpty(mos)) {
            LOGGER.error("task indicator cannot relate to any mo, indicate = {}", taskIndicator);
            return new ArrayList<>();
        }

        List<IndicatorDetil> result = new ArrayList<>(mos.size());
        for (ManagedObject mo : mos) {
            IndicatorDetil indicatorDetil = new IndicatorDetil();
            indicatorDetil.setDn(mo.getDN().getValue());
            indicatorDetil.setIndexName(taskIndicator.getMeasTypeKey().replace(MAX, ""));
            if (StringUtils.isNotEmpty(taskIndicator.getDisplayValue())) {
                indicatorDetil.setDisplayValue(taskIndicator.getDisplayValue());
            }
            result.add(indicatorDetil);
        }
        return result;
    }

    private List<HistoryDatas> getHistoryDatas(ResourceScheduleHandleDto task, List<IndicatorUnit> indicatorUnitList) {
        List<HistoryDatas> historyDatas = new ArrayList<>(indicatorUnitList.size());
        long endTime = task.getTriggerTime();
        long dataCount;
        if (task.getOperationType() == ScheduleConstant.COLLECT_TASK) {
            dataCount = task.getCollectNum();
        } else {
            dataCount = task.getDataNum();
        }

        Properties properties = PropertiesUtil.loadProperties(PROPERTIES);
        // 计算性能数据区间时多取的百分比，保证能拿到指定数据点数
        float threshold = Float.parseFloat(properties.getProperty(EXTENDED_THRESHOLD));

        for (IndicatorUnit indicatorUnit : indicatorUnitList) {
            int period = getPeriod(indicatorUnit.getMoType(), indicatorUnit.getMeasUnitKey());
            if (period == -1) {
                LOGGER.error("get task periods error, taskName = {}", task.getTaskName());
                continue;
            }
            // 周期*数据点个数*多取多少数据 保证能取到足够的数据点,需要往前查数据
            long startTime = endTime - Math.round(period * dataCount * threshold * 1000);

            // 如果是秒级性能任务，为了取到平齐的数据，endTime往前偏移4个周期
            if (period < 60) {
                endTime = endTime - period * 1000L * 4;
            }
            List<IndicatorDetil> indicators = indicatorUnit.getIndicatorDetilList();
            HistoryDatas historyData = new HistoryDatas();
            historyData.setPeriod(period);
            historyData.setBeginTime(startTime);
            historyData.setEndTime(endTime);
            historyData.setMoType(indicatorUnit.getMoType());
            historyData.setMeasUnitKey(indicatorUnit.getMeasUnitKey());
            historyData.setMo2Index(JSON.toJSONString(indicators));
            historyData.setUnitedId(indicatorUnit.getUnitedId());
            historyData.setClusterName(indicatorUnit.getClusterName());
            historyDatas.add(historyData);
        }

        LOGGER.debug("get query historyDatas end, data = {}", historyDatas);
        return historyDatas;
    }

    private ResourceScheduleHistoryDataDto convertPerformanceData(List<HistoryQueryData> queryDatas,
        ResourceScheduleHandleDto task, String clusterName) {
        Map<Long, Integer> numberMap = new TreeMap<>();
        Map<Long, Float> sumMap = new TreeMap<>();
        Map<Long, Float> avgMap = new TreeMap<>();
        // 第一层是分页
        for (HistoryQueryData historyQueryData : queryDatas) {
            int dataSize = historyQueryData.getResultData().size();
            // 第二层为每个实例的性能曲线
            for (int i = 0; i < dataSize; i++) {
                HistoryQueryValue queryValue = historyQueryData.getResultData().get(i);
                // 性能出问题可能会出现同一时刻多个数据的情况，异常处理这里取最大值
                List<HistoryIndexValue> value = handleMultiValue(queryValue.getIndexValues());
                LOGGER.debug("get data count = {}", value.size());
                if (value.size() > task.getDataNum()) {
                    value = value.subList((int) (value.size() - task.getDataNum()), value.size());
                }
                if (trainCount(task, value.size())) {
                    LOGGER.error("train data is less than requirement,{} {} {}", queryValue.getDisplayValue(),
                        queryValue.getIndexName(), queryValue.getIndexUnit());
                    continue;
                }
                putValueToMap(numberMap, sumMap, avgMap, value);
            }
        }
        ResourceScheduleHistoryDataDto resourceScheduleHistoryDataDto = new ResourceScheduleHistoryDataDto();
        resourceScheduleHistoryDataDto.setNumberMap(numberMap);
        resourceScheduleHistoryDataDto.setSumMap(sumMap);
        resourceScheduleHistoryDataDto.setAvgMap(avgMap);
        resourceScheduleHistoryDataDto.setClusterName(clusterName);

        return resourceScheduleHistoryDataDto;
    }

    private boolean trainCount(ResourceScheduleHandleDto task, int valueSize) {
        if (task.getOperationType() != ScheduleConstant.TRAINNING_TASK) {
            return false;
        }
        if (valueSize == 0 || task.getIndicatorLowestCount() > task.getDataNum()) {
            return true;
        }
        return valueSize < task.getIndicatorLowestCount();
    }

    private void putValueToMap(Map<Long, Integer> numberMap, Map<Long, Float> sumMap, Map<Long, Float> avgMap,
        List<HistoryIndexValue> value) {
        for (HistoryIndexValue valueArray : value) {
            if (numberMap.containsKey(valueArray.getTimestamp())) {
                numberMap.put(valueArray.getTimestamp(), numberMap.get(valueArray.getTimestamp()) + 1);
                sumMap.put(valueArray.getTimestamp(), sumMap.get(valueArray.getTimestamp()) +
                    Float.parseFloat(valueArray.getIndexValue()));
                BigDecimal avgValue = BigDecimal.valueOf(sumMap.get(valueArray.getTimestamp()) /
                    numberMap.get(valueArray.getTimestamp()));
                avgMap.put(valueArray.getTimestamp(), avgValue.setScale(0, RoundingMode.HALF_UP).floatValue());
            } else {
                numberMap.put(valueArray.getTimestamp(), 1);
                sumMap.put(valueArray.getTimestamp(), Float.parseFloat(valueArray.getIndexValue()));
                avgMap.put(valueArray.getTimestamp(), Float.parseFloat(valueArray.getIndexValue()));
            }
        }
    }

    private List<HistoryIndexValue> handleMultiValue(List<HistoryIndexValue> value) {
        List<HistoryIndexValue> result = new ArrayList<>();
        LinkedHashMap<String, List<HistoryIndexValue>> listMapByTime = value.stream()
            .collect(
                Collectors.groupingBy(HistoryIndexValue::getTimestampStr, LinkedHashMap::new, Collectors.toList()));
        try {
            for (Map.Entry<String, List<HistoryIndexValue>> entry : listMapByTime.entrySet()) {
                List<HistoryIndexValue> indexValueList = entry.getValue();
                indexValueList = indexValueList.stream()
                    .filter((HistoryIndexValue indexValue) -> StringUtils.isNotEmpty(indexValue.getIndexValue()))
                    .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(indexValueList)) {
                    // 性能查过来的数据value可能是null
                    continue;
                }
                if (indexValueList.size() > 1) {
                    // 倒序后取第一个值，即为最大值
                    indexValueList = indexValueList.stream()
                        .sorted(Comparator.comparing(HistoryIndexValue::getIndexValue,
                            Comparator.comparingDouble(Double::parseDouble)).reversed())
                        .collect(Collectors.toList());
                    result.add(indexValueList.get(0));
                } else {
                    result.addAll(indexValueList);
                }
            }
        } catch (NumberFormatException exception) {
            // 该指标的性能数据转换异常
            LOGGER.error("[handleMultiValue] format indexValue fail! error is {}", exception);
        }
        return result;
    }

    private void handleAndInsertHistoryData(ResourceScheduleHistoryDataDto historyDataDto, ResourceScheduleHandleDto task,
        Map<String, Integer> cpuUseMap, Map<String, Integer> memoryUseMap, ResourceScheduleDao resourceScheduleDao) {
        List<PerformanceIndexValue> avgList = new ArrayList<>();
        List<PerformanceIndexValue> sumList = new ArrayList<>();
        List<PerformanceIndexValue> numberList = new ArrayList<>();

        VmPortrait vmPortrait = resourceScheduleDao.getVmPortrait(task.getTaskId(), historyDataDto.getClusterName());
        if (vmPortrait == null) {
            LOGGER.error("handleAndInsertHistoryData error, vmPortrait is null");
            return;
        }

        for (Long timeStamp : historyDataDto.getNumberMap().keySet()) {
            // 整理平均，加和，数量数据
            PerformanceIndexValue avg = new PerformanceIndexValue();
            avg.setTimestampStr(timeStamp.toString());
            avg.setIndexValue(historyDataDto.getAvgMap().get(timeStamp).toString());
            avgList.add(avg);
            PerformanceIndexValue sum = new PerformanceIndexValue();
            sum.setTimestampStr(timeStamp.toString());
            sum.setIndexValue(historyDataDto.getSumMap().get(timeStamp).toString());
            sumList.add(sum);
            PerformanceIndexValue number = new PerformanceIndexValue();
            number.setTimestampStr(timeStamp.toString());
            number.setIndexValue(historyDataDto.getNumberMap().get(timeStamp).toString());
            numberList.add(number);

            // 根据每个主机需要消耗的CPU和内存数量，计算当前集群需要消耗的cpu和内存数量，存入map
            int vmCpuNumber = vmPortrait.getVCpu();
            int vmMemoryNumber = vmPortrait.getVMem();
            if (cpuUseMap.containsKey(timeStamp.toString())) {
                cpuUseMap.put(timeStamp.toString(), cpuUseMap.get(timeStamp.toString()) + historyDataDto.getNumberMap()
                    .get(timeStamp) * vmCpuNumber);
            } else {
                cpuUseMap.put(timeStamp.toString(), historyDataDto.getNumberMap().get(timeStamp) * vmCpuNumber);
            }
            if (memoryUseMap.containsKey(timeStamp.toString())) {
                memoryUseMap.put(timeStamp.toString(), memoryUseMap.get(timeStamp.toString()) +
                    historyDataDto.getNumberMap().get(timeStamp) * vmMemoryNumber);
            } else {
                memoryUseMap.put(timeStamp.toString(), historyDataDto.getNumberMap().get(timeStamp) * vmMemoryNumber);
            }
        }

        try {
            resourceScheduleDao.updateVmNumberHistory(task.getTaskId(), historyDataDto.getClusterName(), numberList);
            resourceScheduleDao.updateVmAvgHistory(task.getTaskId(), historyDataDto.getClusterName(), avgList);
            resourceScheduleDao.updateVmSumHistory(task.getTaskId(), historyDataDto.getClusterName(), sumList);
        } catch (ServiceException e) {
            LOGGER.error("[handleAndInsertHistoryData] insertHistoryData error, e={}", e);
        }
    }

    private List<String> convertDataToStringTrain(ResourceScheduleHistoryDataDto dataDto, String clusterName) {
        List<String> result = new ArrayList<>();
        for (Long timestamp : dataDto.getSumMap().keySet()) {
            List<String> indexValue = new ArrayList<>();
            indexValue.add(String.valueOf(timestamp));
            indexValue.add(clusterName);
            indexValue.add(dataDto.getSumMap().get(timestamp).toString());
            indexValue.add(dataDto.getNumberMap().get(timestamp).toString());
            String oneLine = StringUtils.join(indexValue, COMMA);
            result.add(oneLine);
        }
        return result;
    }

    private List<String> convertDataToStringDetectHistory(ResourceScheduleHistoryDataDto dataDto, String clusterName,
        Map<String, String> historyScheduleMap, ResourceScheduleExecuteDetail executeDetail) {
        List<String> result = new ArrayList<>();
        for (Long timestamp : dataDto.getSumMap().keySet()) {
            List<String> indexValue = new ArrayList<>();
            indexValue.add(String.valueOf(timestamp));
            indexValue.add(clusterName);
            indexValue.add(dataDto.getSumMap().get(timestamp).toString());
            indexValue.add(dataDto.getNumberMap().get(timestamp).toString());
            if (historyScheduleMap.containsKey(String.valueOf(timestamp))) {
                indexValue.add(historyScheduleMap.get(String.valueOf(timestamp)));
                if (executeDetail != null
                    && executeDetail.getStatus().equals("RUNNING")
                    && timestamp >= executeDetail.getStartTime()) {
                    indexValue.add("RUNNING");
                } else {
                    indexValue.add("");
                }
            } else {
                indexValue.add("");
                indexValue.add("");
            }
            String oneLine = StringUtils.join(indexValue, COMMA);
            result.add(oneLine);
        }
        return result;
    }

    private List<String> convertDataToStringDetectPredict(Integer taskId, long triggerTime, HistoryDatas historyDatas,
        ResourceScheduleDao resourceScheduleDao) {
        int period = getPeriod(historyDatas.getMoType(), historyDatas.getMeasUnitKey());
        if (period == -1) {
            LOGGER.error("get task periods error, taskId = {}", taskId);
            period = 60;
        }
        ScheduleTaskConfig scheduleTaskConfig = resourceScheduleDao.getScheduleTaskConfig(taskId);
        if (scheduleTaskConfig == null || scheduleTaskConfig.getDetectOutputCount() == null) {
            return new ArrayList<>();
        }
        long endTime = triggerTime + (long) period * scheduleTaskConfig.getDetectOutputCount() * 1000;

        List<PerformanceIndexValueSchedule> predictData = resourceScheduleDao.getIndicatorPredictData(taskId, triggerTime,
            endTime, historyDatas.getClusterName());

        List<String> result = new ArrayList<>();
        for (PerformanceIndexValueSchedule value : predictData) {
            List<String> indexValue = new ArrayList<>();
            indexValue.add(value.getTimestampStr());
            indexValue.add(historyDatas.getClusterName());
            indexValue.add(value.getIndexValue());
            String oneLine = StringUtils.join(indexValue, COMMA);
            result.add(oneLine);
        }
        return result;
    }

    private List<String> convertDataToStringCollectHistory(String clusterName, List<PerformanceIndexValue> vmNumberHistory,
        Map<String, String> vmSumHistoryMap, Map<String, String> vmScheduleHistoryMap, ResourceScheduleExecuteDetail executeDetail) {
        List<String> result = new ArrayList<>();
        for (PerformanceIndexValue data : vmNumberHistory) {
            List<String> indexValue = new ArrayList<>();
            indexValue.add(data.getIndexValue());
            indexValue.add(clusterName);
            indexValue.add(vmSumHistoryMap.getOrDefault(data.getTimestampStr(), ""));
            indexValue.add(data.getIndexValue());
            indexValue.add(vmScheduleHistoryMap.getOrDefault(data.getTimestampStr(), ""));
            if (executeDetail != null && Long.parseLong(data.getTimestampStr()) >= executeDetail.getStartTime()) {
                indexValue.add("RUNNING");
            } else {
                indexValue.add("");
            }
            String oneLine = StringUtils.join(indexValue, COMMA);
            result.add(oneLine);
        }
        return result;
    }

    private List<String> convertDataToStringCollectPredict(Integer taskId, String clusterName, String unitedId,
        Long startTime, Long endTime, ResourceScheduleDao resourceScheduleDao) {
        // 查询指标预测数据
        List<PerformanceIndexValueSchedule> predictData = resourceScheduleDao.getIndicatorPredictData(taskId, startTime,
            endTime, clusterName);

        // 查询预测调度数据
        List<PerformanceIndexValue> vmSchedulePredict = resourceScheduleDao.getScheduleData(taskId, clusterName, startTime, endTime);
        Map<String, String> vmSchedulePredictMap = new HashMap<>();
        for (PerformanceIndexValue scheduleData : vmSchedulePredict) {
            vmSchedulePredictMap.put(scheduleData.getTimestampStr(), scheduleData.getIndexValue());
        }

        List<String> result = new ArrayList<>();
        for (PerformanceIndexValueSchedule value : predictData) {
            List<String> indexValue = new ArrayList<>();
            indexValue.add(value.getTimestampStr());
            indexValue.add(clusterName);
            indexValue.add(value.getIndexValue());
            indexValue.add(vmSchedulePredictMap.getOrDefault(value.getTimestampStr(), ""));
            String oneLine = StringUtils.join(indexValue, COMMA);
            result.add(oneLine);
        }
        return result;
    }

    private DataSourceInfo getDataSourceInfo(String taskName, File historyFile, File predictFile) {
        if (FileCleanUtils.fileSizeOverMax(historyFile) || FileCleanUtils.fileSizeOverMax(predictFile)) {
            LOGGER.error("createAndPushFile error, file size oversize, file={}{}", historyFile.getName()
                , predictFile.getName());
            deleteFile(historyFile);
            deleteFile(predictFile);
            return null;
        }

        DataSourceInfo dataSourceInfo = new DataSourceInfo();
        dataSourceInfo.setSrcFilePath(TaskConstant.TEMP_SRC_PATH);
        dataSourceInfo.setDestFilePath(TaskConstant.TEMP_DEST_PATH);
        LOGGER.info("handle detect or collect end, taskName = {}", taskName);
        return dataSourceInfo;
    }

    private void printDataToFile(File file, List<String> convertString) throws FileNotFoundException {
        // 写入历史数据文件
        try (PrintWriter pw =
            new PrintWriter(new OutputStreamWriter(new FileOutputStream(file, true), StandardCharsets.UTF_8))) {
            convertString.stream().map(this::checkLine).forEach(pw::println);
        } catch (IOException e) {
            LOGGER.error("[ResourceScheduleDataSourceHandler] printDataToFile error, e={}", e);
            deleteFile(file);
            throw e;
        }
        LOGGER.debug("[ResourceScheduleDataSourceHandler] printDataToFile end, filename = {},size = {}",
            file.getName(), convertString.size());
    }

    private boolean printDataToFile(File historyFile, File predictFile, List<String> convertStringHistory,
        List<String> convertStringPredict) {
        try {
            printDataToFile(historyFile, convertStringHistory);
            printDataToFile(predictFile, convertStringPredict);
        } catch (IOException e) {
            LOGGER.error("print to file error, e={}", e);
            return false;
        }
        return true;
    }

    private int getPeriod(String moType, String measUnitKey) {
        int period = PerformanceClient.getInstance()
            .getPeriod(moType, measUnitKey);
        if (period == -1) {
            LOGGER.error("get task periods error, moType = {}, measUnitKey = {} ", moType, measUnitKey);
            period = 60;
        }
        return period;
    }

    private boolean createFailed(File file) {
        return file == null || !file.exists();
    }

    private boolean allVmsNormal(List<HistoryQueryData> queryData, long triggerTime, String clusterName, int period) {
        int delayPeriodCount = ConfigurationUtil.waitPerformancePeriod();
        // 如果是秒级性能任务，为了取到平齐的数据，endTime往前偏移4个周期
        if (period < 60) {
            triggerTime = triggerTime - period * 1000L * 4;
        }
        // 第一层是分页
        for (HistoryQueryData historyQueryData : queryData) {
            // 第二层是每个实例的性能数据，只要其中一个vm的性能采集延迟超过3周期，就判定失败
            List<HistoryQueryValue> queryValues = historyQueryData.getResultData();
            long maxTime = 0L;
            for (HistoryQueryValue queryValue : queryValues) {
                if (CollectionUtils.isEmpty(queryValue.getIndexValues())) {
                    LOGGER.error("[ResourceScheduleDataSourceHandler] query data is empty, clusterName = {}",
                        clusterName);
                    return false;
                }
                List<HistoryIndexValue> values = queryValue.getIndexValues();
                HistoryIndexValue maxTimestampValue = values.stream()
                    .max(Comparator.comparing(HistoryIndexValue::getTimestamp))
                    .orElse(new HistoryIndexValue());
                long thisMaxTime = maxTimestampValue.getTimestamp();
                if (triggerTime > thisMaxTime + period * delayPeriodCount * 1000L) {
                    LOGGER.error("[ResourceScheduleDataSourceHandler]  vms doesn't have new data for {} periods,"
                        + " clusterName = {}", delayPeriodCount, clusterName);
                    return false;
                }
                if (maxTime == 0L) {
                    maxTime = thisMaxTime;
                }
                if (maxTime != thisMaxTime) {
                    LOGGER.error("[ResourceScheduleDataSourceHandler]  vms max times are not sync,"
                        + " clusterName = {}", clusterName);
                    return false;
                }
            }
        }
        return true;
    }
}
