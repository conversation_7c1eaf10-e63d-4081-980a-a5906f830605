/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.dao.disasterrecovery.mapper;

import com.huawei.i2000.dvanalysisengineservice.model.StaticAlarm;

import java.util.List;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2022/8/14
 */
public interface DisasterRecoveryAlarmMapper {

    List<StaticAlarm> queryAlarmById(String groupId);

    void batchInsert(List<StaticAlarm> list);

    void deleteAlarmById(String groupId);

    void deleteAlarmByIds(List<String> groupIds);

}
