/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskswitch.state;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.dto.SwitchActionType;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.dto.SwitchStepRuntime;
import com.huawei.i2000.dvanalysisengineservice.model.DisasterRecoverySwitchResult;
import com.huawei.i2000.dvanalysisengineservice.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineservice.util.ApplicationContextHelper;

import lombok.Data;

import java.util.HashSet;
import java.util.Set;

/**
 * 切换流程
 *
 * <AUTHOR>
 * @since 2023/1/3
 */
@Data
public class DisasterRecoveryTaskSwitchFlow {

    private static Set<String> prepareProcessState = new HashSet<>();

    private static Set<String> errorProcessState = new HashSet<>();

    private static Set<String> executeProcessState = new HashSet<>();

    private DisasterRecoveryTaskState disasterRecoveryTaskState;

    private ResponseResult result;

    private DisasterRecoverySwitchResult disasterRecoverySwitchResult;

    private HttpContext context;

    static {
        prepareProcessState.add(SwitchActionType.PREPARING.getStatus());
        prepareProcessState.add(SwitchActionType.STARTING.getStatus());
        prepareProcessState.add(SwitchActionType.CHECKING.getStatus());

        errorProcessState.add(SwitchActionType.ERROR.getStatus());
        errorProcessState.add(SwitchActionType.START_FLOW_ERROR.getStatus());
        errorProcessState.add(SwitchStepRuntime.MONITOR_RUNTIME_ERROR);
        errorProcessState.add(SwitchStepRuntime.FLOW_RUNTIME_ERROR);
        errorProcessState.add(SwitchStepRuntime.SWITCH_EXECUTE_PROCESS);

        executeProcessState.add(SwitchActionType.EXECUTING.getStatus());
        executeProcessState.add(SwitchActionType.CANCEL_SWITCH.getStatus());
        executeProcessState.add(SwitchActionType.SWITCHED.getStatus());
        executeProcessState.add(SwitchActionType.SWITCH_FAILED.getStatus());
    }

    /**
     * 切换流程初始化
     *
     * @param disasterRecoverySwitchResult 容灾切换记录
     * @param context 上下文
     */
    public DisasterRecoveryTaskSwitchFlow(DisasterRecoverySwitchResult disasterRecoverySwitchResult,
        HttpContext context) {
        this.disasterRecoverySwitchResult = disasterRecoverySwitchResult;
        this.context = context;
    }

    /**
     * 切换流程状态
     *
     * @param flowState 流程状态
     * @throws ServiceException ex
     */
    public void generateStateResponseResult(String flowState) throws ServiceException {
        ResponseResult responseResult = new ResponseResult();
        responseResult.setResultCode(0);

        if (prepareProcessState.contains(flowState)) {
            this.disasterRecoveryTaskState = (DisasterRecoveryPrepareProcessState) ApplicationContextHelper.getBean(
                "disasterRecoveryPrepareProcessState");
        } else if (SwitchActionType.CHECKED.getStatus().equals(flowState)) {
            this.disasterRecoveryTaskState = (DisasterRecoveryChecked) ApplicationContextHelper.getBean(
                "disasterRecoveryChecked");
        } else if (errorProcessState.contains(flowState)) {
            this.disasterRecoveryTaskState = (DisasterRecoveryErrorProcessState) ApplicationContextHelper.getBean(
                "disasterRecoveryErrorProcessState");
        } else if (SwitchActionType.SWITCHING.getStatus().equals(flowState)) {
            this.disasterRecoveryTaskState = (DisasterRecoverySwitching) ApplicationContextHelper.getBean(
                "disasterRecoverySwitching");

        } else if (SwitchActionType.TOBEINTERACTED.getStatus().equals(flowState)) {
            this.disasterRecoveryTaskState = new DisasterRecoveryTobeInteracted();
        } else if (SwitchActionType.WAITING.getStatus().equals(flowState)) {
            this.disasterRecoveryTaskState = (DisasterRecoveryWaiting) ApplicationContextHelper.getBean(
                "disasterRecoveryWaiting");
        } else if (executeProcessState.contains(flowState)) {
            this.disasterRecoveryTaskState = (DisasterRecoveryExecuteProcessState) ApplicationContextHelper.getBean(
                "disasterRecoveryExecuteProcessState");
        }
        this.disasterRecoveryTaskState.setDisasterRecoverySwitchResult(this.disasterRecoverySwitchResult);
        this.disasterRecoveryTaskState.setResponseResult(responseResult);
        this.disasterRecoveryTaskState.queryFlowResult(this);
        if (this.disasterRecoveryTaskState instanceof DisasterRecoveryStateOperation) {
            ((DisasterRecoveryStateOperation) this.disasterRecoveryTaskState).executeOperation();
        }
        this.result = responseResult;
    }

    /**
     * 获取流程状态
     *
     * @return DisasterRecoveryTaskState state
     */
    public DisasterRecoveryTaskState getState() {
        return disasterRecoveryTaskState;
    }

}
