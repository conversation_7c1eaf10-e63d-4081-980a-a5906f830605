/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.taskmanage;

import com.huawei.bsp.cron.api.model.JobJson;
import com.huawei.bsp.cron.api.model.PeriodUnit;
import com.huawei.bsp.cron.api.model.TriggerType;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.cbb.fm.utils.AlarmAddInfoBuilder;
import com.huawei.i2000.dvanalysisengineservice.business.common.AuthUtils;
import com.huawei.i2000.dvanalysisengineservice.business.common.ConfigurationUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.EncryptionUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.HofsUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.InternationalConstant;
import com.huawei.i2000.dvanalysisengineservice.business.common.cache.LockOper;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.AlarmSelectType;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.DataSourceConnectType;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.DataSourceType;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.TaskTypeEnum;
import com.huawei.i2000.dvanalysisengineservice.business.configdata.ConfigDataHandler;
import com.huawei.i2000.dvanalysisengineservice.business.datasource.DataSourceServiceImpl;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.util.EamUtil;
import com.huawei.i2000.dvanalysisengineservice.business.resourceschedule.PortraitManager;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.conceptdrift.ConceptDriftManage;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.constant.SynAnalysisResultConstant;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.historicaldatahandler.HistoricalDataManager;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.AlarmIncident;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.AlarmRecord;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.IndicatorMissDataOutlier;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.IndicatorOutlier;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.taskresulthandler.AlarmRecordsHandler;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.taskresulthandler.IndicatorDetectResultHandler;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.util.TaskAnalysisResultUtil;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.AnalysisTaskEntity;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.PqlsEntity;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.ScheduleCenter;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.ScheduleConstant;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.TrainingMessageCacheOper;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.TrainingTaskManager;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.dto.TrainingIndicatorResult;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.dto.TrainningTaskMessage;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.AlarmClient;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.HandleDtoFactory;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.PerformanceClient;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.AlarmQueryParam;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.CustomIndicatorData;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ManageObject;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.QFilterElement;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.QueryContext;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ScrollQueryResult;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.incident.dto.IncidentTaskInfo;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.taskhandler.alarmhandle.AlarmEventListener;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.taskhandler.alarmhandle.dto.AlarmListenerInfo;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.taskhandler.dto.LogDetectTaskInfo;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.util.CronServiceUtil;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.AssociationAnalysisService;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.TriggerTaskUtil;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.MeasObject4UI;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.MeasObjectEntry;
import com.huawei.i2000.dvanalysisengineservice.dao.common.MapperFactory;
import com.huawei.i2000.dvanalysisengineservice.dao.resourceschedule.PortraitManageDao;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.AlarmCompressDao;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.IndicatorDetectResultDao;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.IndicatorOutlierDao;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.mapper.TaskExecutionResultMapper;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.CustomIndicatorSqlExcutor;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.DataSourceDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.IncidentDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.LogDetectDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.ResourceScheduleDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskIndicatorDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.handlerimpl.AbstractTaskManage;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.handlerimpl.TaskManageDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.mapper.CustomTaskMapper;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.mapper.TaskCustomDataMapper;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.mapper.TaskIndicatorMapper;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.mapper.TaskManageMapper;
import com.huawei.i2000.dvanalysisengineservice.model.AggregateMachineGroup;
import com.huawei.i2000.dvanalysisengineservice.model.AlarmTemplate;
import com.huawei.i2000.dvanalysisengineservice.model.AlarmTemplateObj;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTask;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTaskList;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTaskListQueryParam;
import com.huawei.i2000.dvanalysisengineservice.model.AutoScheduleParam;
import com.huawei.i2000.dvanalysisengineservice.model.ColumnMapping;
import com.huawei.i2000.dvanalysisengineservice.model.CustomColumnsParam;
import com.huawei.i2000.dvanalysisengineservice.model.DataSource;
import com.huawei.i2000.dvanalysisengineservice.model.DataSourceQueryParam;
import com.huawei.i2000.dvanalysisengineservice.model.Event;
import com.huawei.i2000.dvanalysisengineservice.model.IndicatorInstancesParam;
import com.huawei.i2000.dvanalysisengineservice.model.IndicatorValidParameter;
import com.huawei.i2000.dvanalysisengineservice.model.PortraitIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.PqlQueryParam;
import com.huawei.i2000.dvanalysisengineservice.model.PqlResult;
import com.huawei.i2000.dvanalysisengineservice.model.PqlValidateResponse;
import com.huawei.i2000.dvanalysisengineservice.model.ProductPortrait;
import com.huawei.i2000.dvanalysisengineservice.model.ProductPortraitSharedIndicators;
import com.huawei.i2000.dvanalysisengineservice.model.PrometheusData;
import com.huawei.i2000.dvanalysisengineservice.model.QueryAssociatedResourceScheduleTask;
import com.huawei.i2000.dvanalysisengineservice.model.ResScheAsscEntry;
import com.huawei.i2000.dvanalysisengineservice.model.ResScheAsscParam;
import com.huawei.i2000.dvanalysisengineservice.model.Situation;
import com.huawei.i2000.dvanalysisengineservice.model.SituationList;
import com.huawei.i2000.dvanalysisengineservice.model.SituationListQueryParam;
import com.huawei.i2000.dvanalysisengineservice.model.TaskIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.TaskIndicatorResult;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerTaskCheckResult;
import com.huawei.i2000.dvanalysisengineservice.util.AuthTokenUtil;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;
import com.huawei.i2000.dvanalysisengineservice.util.cipher.SHA256Util;
import com.huawei.i2000.dvanalysisengineservice.util.cloudmonitor.PromethusClient;
import com.huawei.i2000.dvanalysisengineservice.util.cloudmonitor.dto.PromethusResponseData;
import com.huawei.i2000.dvanalysisengineservice.util.cloudmonitor.dto.PromethusResponseDataResult;
import com.huawei.i2000.dvanalysisengineservice.util.cloudmonitor.dto.RangeQueryParam;
import com.huawei.i2000.dvanalysisengineservice.util.exception.ExceptionInternationalConstant;
import com.huawei.i2000.dvanalysisengineservice.util.exception.ExceptionUtil;
import com.huawei.i2000.dvanalysisengineservice.util.jdbc.JDBCClient;
import com.huawei.i2000.dvanalysisengineservice.util.jdbc.JDBCClientGenerator;
import com.huawei.i2000.dvanalysisengineservice.util.jdbc.JDBCOpions;
import com.huawei.i2000.dvanalysisengineservice.util.operationlog.LogConstant;
import com.huawei.i2000.dvanalysisengineservice.util.operationlog.OperationLogUtil;
import com.huawei.i2000.util.omc.OmcUtil;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.dao.DataAccessException;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 任务管理中心
 *
 * <AUTHOR>
 * @since 2021/2/26
 */
@Service
public class TaskManager implements ApplicationListener<ContextRefreshedEvent> {
    OssLog LOGGER = OssLogFactory.getLogger(TaskManager.class);

    private static final List<String> ALARM_RETURN_FIELDS = Collections.unmodifiableList(
        Arrays.asList("moi", "clearUtc", "probableCause", "alarmId", "occurUtc", "alarmName", "severity", "csn",
            "eventType", "meName", "deviceTypeId", "cleared", "nativeMoDn", "productName", "category", "nativeMeDn", "moduleName", "alarmGroupId"));

    private static final String BY_SOLUTION_ID = "bySolutionId";

    private final ThreadPoolExecutor syncProExecutor = new ThreadPoolExecutor(2, 2, 10L, TimeUnit.MINUTES,
        new LinkedBlockingQueue<>(100), new CustomizableThreadFactory("Sync-Task-Thread-"), new CustomRejectedExecutionHandler());

    private static final ThreadPoolExecutor COMPRESS_EXECUTOR = new ThreadPoolExecutor(1, 1, 10L, TimeUnit.MINUTES,
        new LinkedBlockingQueue<>(100), new CustomizableThreadFactory("Compress-Task-Thread-"), new CustomRejectedExecutionHandler());

    private static final ThreadPoolExecutor START_ONCE_EXECUTOR = new ThreadPoolExecutor(1, 3, 10L, TimeUnit.MINUTES,
        new LinkedBlockingQueue<>(100), new CustomizableThreadFactory("Start-Once-Task-Thread-"), new CustomRejectedExecutionHandler());

    @Autowired
    TaskManageDao taskManageDao;

    @Autowired
    TaskIndicatorDao taskIndicatorDao;

    @Autowired
    ScheduleCenter scheduleCenter;

    @Autowired
    TrainingTaskManager trainingTaskManager;

    @Autowired
    DataSourceDao dataSourceDao;

    @Autowired
    MapperFactory mapperFactory;

    @Autowired
    LogDetectDao logDetectDao;

    @Autowired
    CustomIndicatorSqlExcutor customIndicatorSqlExcutor;

    @Autowired
    AssociationAnalysisService analysisService;

    @Autowired
    PortraitManageDao portraitManageDao;

    @Autowired
    ConfigDataHandler configDataHandler;

    @Autowired
    IndicatorOutlierDao indicatorOutlierDao;

    @Autowired
    IndicatorDetectResultDao indicatorDetectResultDao;

    @Autowired
    IndicatorDetectResultHandler indicatorDetectResultHandler;

    @Autowired
    ResourceScheduleDao resourceScheduleDao;

    @Autowired
    PortraitManager portraitManager;

    @Autowired
    AlarmEventListener alarmEventListener;

    @Autowired
    HistoricalDataManager historicalDataManager;

    @Autowired
    AlarmCompressDao alarmCompressDao;

    @Autowired
    TaskManagerSub taskManagerSub;

    @Autowired
    private IncidentDao incidentDao;

    private static final Long TWENTY_ONE_DAYS_MILL = 21 * 24 * 60 * 60 * 1000L;

    private static final int LIST_SIZE = 200;

    private static final int SPLIT_INTERVAL = 1440;

    private static final String TEMP_PATH = System.getProperty("java.io.tmpdir") + File.separator;

    private static final String SYNCHRONIZE_CUSTOM_DATA_URL = "/rest/dvanalysisengineservice/v1/taskmanage/synchronizecustomdatazenith?taskId=";

    private static final TypeReference<List<Map<String, String>>> TEMPLATE_FIELD = new TypeReference<List<Map<String, String>>>() { };

    /**
     * 仅供前台调取，新增或者修改任务使用，当taskid为空说明表里无该任务，为新增，不为空更新
     *
     * @param task task
     * @throws ServiceException ServiceException
     */
    public void modifyTask(AnalysisTask task) throws ServiceException {
        task.setUpdateTime(new Date().getTime());
        if (task.getTaskId() != null) {
            // 清除概念漂移第一段数据
            clearConceptFile(getAnalysisTaskById(task.getTaskId()));
            taskManageDao.updateTask(task);
            AnalysisTask targetAnalysisTask = taskManageDao.getAnalysisTaskById(task.getTaskId());
            if (targetAnalysisTask.getTaskType().equals(TaskTypeEnum.INDICATORPREDICTTASK.enumToInt())
                || targetAnalysisTask.getTaskType().equals(TaskTypeEnum.INDICATORTASK.enumToInt())) {
                configDataHandler.updateUsedCapacityForFront(targetAnalysisTask, TaskConstant.MODIFY_ACTION);
                TrainingMessageCacheOper.getInstance().removeOneTaskCache(task.getTaskId());
            }
        } else {
            taskManageDao.insertTask(task);
        }
    }

    /**
     * 删除任务
     *
     * @param taskId taskId
     * @throws ServiceException ServiceException
     */
    public void deleteTaskById(int taskId) throws ServiceException {
        AnalysisTask analysisTask = taskManageDao.getAnalysisTaskById(taskId);
        // 清除概念漂移第一段数据
        clearConceptFile(getAnalysisTaskById(taskId));
        analysisService.deleteAssocationTaskByTaskId(taskId);
        taskManageDao.deleteTaskById(taskId);
        deleteSynchronizeCustomDataCorn(taskId);
        if (analysisTask.getTaskType().equals(TaskTypeEnum.INDICATORPREDICTTASK.enumToInt())
            || analysisTask.getTaskType().equals(TaskTypeEnum.INDICATORTASK.enumToInt())) {
            configDataHandler.updateUsedCapacityForFront(analysisTask, TaskConstant.DELETE_ACTION);
            TrainingMessageCacheOper.getInstance().removeOneTaskCache(taskId);
        }
    }

    /**
     * 删除指标异常检测，自定义数据源任务的定时任务
     *
     * @param taskId taskId
     */
    public void deleteSynchronizeCustomDataCorn(Integer taskId) {
        String jobId = "startSynchronizeCustomDataCorn" + taskId;
        try {
            CronServiceUtil.deleteCronTask(jobId);
            LOGGER.info("[TaskManager] delete synchronize cron job");
        } catch (ServiceException e) {
            LOGGER.error("[TaskManager] delete synchronize cron job failed.", e);
        }
    }

    /**
     * 删除状态有误时创建的任务
     *
     * @param taskId taskId
     * @throws ServiceException ServiceException
     */
    public void deleteTaskCheckById(int taskId) throws ServiceException {
        taskManageDao.deleteTaskChecked(taskId);
    }

    /**
     * 开始任务
     *
     * @param taskId taskId
     * @param isTraining isTraining
     * @param userId userId
     * @throws ServiceException ServiceException
     */
    public void startTaskById(int taskId, Integer isTraining, String userId) throws ServiceException {
        try {
            int taskType = getAnalysisTaskById(taskId).getTaskType();
            boolean lock = false;
            try {
                lock = LockOper.getInstance().lock("startTask_" + taskType);
                if (!lock) {
                    throw ExceptionUtil.createException(ExceptionInternationalConstant.TASK_STARTING);
                }
                startTaskByIdImpl(taskId, isTraining, userId);
            } finally {
                if (lock) {
                    LockOper.getInstance().unlock("startTask_" + taskType);
                }
            }
        } finally {
            AnalysisTask analysisTask = getAnalysisTaskById(taskId);
            int startStatus = analysisTask.getStartStatus();
            if (startStatus == TaskConstant.TASK_STATUS_PRE_START_BY_INIT) {
                taskManageDao.changeTaskStatusById(taskId, userId, TaskConstant.TASK_STATUS_INIT);
            } else if (startStatus == TaskConstant.TASK_STATUS_PRE_START_BY_STOP) {
                taskManageDao.changeTaskStatusById(taskId, userId, TaskConstant.TASK_STATUS_STOP);
            }
        }
    }

    /**
     * 开始任务
     *
     * @param taskId taskId
     * @param isTraining isTraining
     * @param userId userId
     * @throws ServiceException ServiceException
     */
    public void startTaskByIdImpl(int taskId, Integer isTraining, String userId) throws ServiceException {
        AnalysisTask analysisTask = getAnalysisTaskById(taskId);
        if (analysisTask.getTaskType().equals(TaskTypeEnum.ALARMTASK.enumToInt()) && TaskConstant.ALARM_TYPE_COMPRESS.equals(analysisTask.getAlarmTaskType())) {
            startAlarmCompressTask(analysisTask, userId);
            return;
        }
        if (analysisTask.getTaskType().equals(TaskConstant.TASK_TYPE_INDICATOR_PREDICT) &&
            taskManageDao.getTaskListByType(TaskConstant.TASK_TYPE_INDICATOR_PREDICT)
                .stream()
                .filter(indicatorPredictTask -> indicatorPredictTask.getStartStatus() != null
                    && TaskConstant.TASK_STATUS_STARTS.contains(indicatorPredictTask.getStartStatus()))
                .count() >= 10) {
            throw ExceptionUtil.createException(ExceptionInternationalConstant.TOO_MANY_START_INDICATOR_PREDICT_TASK);
        }
        if (analysisTask.getTaskType().equals(TaskConstant.TASK_TYPE_RESOURCE_SCHEDULE) &&
            taskManageDao.getTaskListByType(TaskConstant.TASK_TYPE_RESOURCE_SCHEDULE)
                .stream()
                .filter(resourceScheduleTask -> resourceScheduleTask.getStartStatus() != null
                    && TaskConstant.TASK_STATUS_STARTS.contains(resourceScheduleTask.getStartStatus()))
                .count() >= 10) {
            throw ExceptionUtil.createException(ExceptionInternationalConstant.TOO_MANY_START_RESOURCE_SCHEDULE_TASK);
        }
        // 潮汐调度任务启动前校验画像配置是否完整
        if (analysisTask.getTaskType().equals(TaskConstant.TASK_TYPE_RESOURCE_SCHEDULE)) {
            Integer portraitId = resourceScheduleDao.getProductPortraitId(analysisTask.getTaskId());
            ProductPortrait portrait = portraitManager.getProductPortraitDetail(portraitId);
            scheduleCenter.judgePortraitCompleted(portrait, true);
        }
        // 潮汐调度任务启动前校验关联的指标预测任务是否均已启动
        scheduleCenter.judgeResourceScheduleStart(analysisTask);
        scheduleCenter.judgeIndicatorExceptionStartCount(analysisTask);
        taskManageDao.preStartTaskById(taskId, userId, analysisTask.getStartStatus() == null ? TaskConstant.TASK_STATUS_INIT : analysisTask.getStartStatus());
        // 如果需要训练模型包，则先进行模型包任务，训练完后才能开始
        if (isTraining == ScheduleConstant.TRAINNING_TASK) {
            scheduleCenter.handleTask(analysisTask, ScheduleConstant.TRAINNING_TASK);
        }
        if (analysisTask.getPeriodicType().equals(TaskConstant.PERIODIC_TYPE_ONCE) && !analysisTask.getTaskType().equals(TaskConstant.TASK_TYPE_CAPACITY_EXCEPTION)) {
            // 一次性任务执行需要保证经过训练
            if (isTraining == 0 && (analysisTask.getTrainStatus() == null || analysisTask.getTrainStatus() == TaskConstant.TRAIN_STATUS_FAIL)) {
                throw ExceptionUtil.createException(ExceptionInternationalConstant.ANOMALY_ONCE_TASK_EXECUTE_ERROR);
            }
            startTaskByIdAsync(taskId, userId);
        }
        // 创建任务后，就开始创建模型训练定时任务
        startCronTask(userId, analysisTask);
        if (analysisTask.getTaskType().equals(TaskConstant.TASK_TYPE_INDICATOR_PREDICT) || analysisTask.getTaskType().equals(TaskConstant.TASK_TYPE_INDICATOR_EXCEPTION)) {
            TrainingMessageCacheOper.getInstance().removeOneTaskCache(taskId);
        }
    }

    private void startCronTask(String userId, AnalysisTask analysisTask) throws ServiceException {
        Integer taskId = analysisTask.getTaskId();
        if (analysisTask.getPeriodicType().equals(TaskConstant.PERIODIC_TYPE_CYCLE) || analysisTask.getTaskType().equals(TaskConstant.TASK_TYPE_CAPACITY_EXCEPTION)) {
            scheduleCenter.startPredictTask(analysisTask);
            scheduleCenter.startDriftTask(analysisTask);
            taskManageDao.startTaskById(taskId, userId);
            scheduleCenter.startTrain(analysisTask);
        }
        if (analysisTask.getTaskType().equals(TaskConstant.TASK_TYPE_RESOURCE_SCHEDULE)) {
            scheduleCenter.startCollect(analysisTask);
        }
    }

    public void startTaskByIdAsync(int taskId, String userId) {
        START_ONCE_EXECUTOR.execute(() -> {
            try {
                startTaskByIdOnce(taskId, userId);
            } catch (Throwable throwable) {
                LOGGER.error("startTaskByIdOnce error, e:", throwable);
            }
        });
    }

    private void clearConceptFile(AnalysisTask analysisTask) {
        File file = new File(
            ConfigurationUtil.algorithmPackagePath() + analysisTask.getAlgorithmModelName() + File.separator
                + "trainModel" + File.separator + "model" + File.separator + analysisTask.getTaskId() + ".csv");
        if (file.exists() && !file.delete()){
            LOGGER.error("delete tmp file fail, fileName = {}", file.getName());
        }
    }

    private void startTaskByIdOnce(int taskId, String userId) {
        AnalysisTask analysisTask;
        try {
            do {
                TimeUnit.SECONDS.sleep(5);
                analysisTask = getAnalysisTaskById(taskId);
            } while (analysisTask.getTrainStatus() == null || analysisTask.getTrainStatus()
                .equals(TaskConstant.TRAIN_STATUS_RUNNING_TRANIN));
            if (analysisTask.getTrainStatus().equals(TaskConstant.TRAIN_STATUS_FAIL)) {
                return;
            }
            scheduleCenter.startPredictTask(analysisTask);
            taskManageDao.startTaskById(taskId, userId);
        } catch (ServiceException e) {
            LOGGER.error("startTaskByIdOnce error,e={}", e);
        } catch (InterruptedException e) {
            LOGGER.error("startTaskByIdOnce sleep error,e={}", e);
        }
    }

    /**
     * 手动执行任务
     *
     * @param taskId taskId
     * @param userId userId
     * @throws ServiceException ServiceException
     */
    public void manualExecuteTaskById(int taskId, String userId) throws ServiceException {
        AnalysisTask analysisTask = getAnalysisTaskById(taskId);
        if (analysisTask.getTrainStatus() == null) {
            LOGGER.error("illegal manual execute");
            throw new ServiceException("illegal manual execute");
        } else if (analysisTask.getTrainStatus().equals(TaskConstant.TRAIN_STATUS_RUNNING_TRANIN)
            || analysisTask.getTrainStatus().equals(TaskConstant.TRAIN_STATUS_FAIL)) {
            TrainningTaskMessage taskMessage = TrainingMessageCacheOper.getInstance().getOne(taskId);
            if (taskMessage == null) {
                taskMessage = new TrainningTaskMessage(taskId, 0L);
            }
            taskMessage.setStartTime(0L);
            if (trainingTaskManager.queryTrainingStatus(taskMessage) == ScheduleConstant.RUN_TRAINING) {
                throw new ServiceException("no model");
            }
        }
        scheduleCenter.startPredictTaskOnce(analysisTask);
    }

    /**
     * 停止任务，只针对周期性任务
     *
     * @param taskId taskId
     * @throws ServiceException ServiceException
     */
    public void stopTaskById(int taskId) throws ServiceException {
        AnalysisTask analysisTask = taskManageDao.getAnalysisTaskById(taskId);
        if (analysisTask.getTaskType().equals(TaskTypeEnum.ALARMTASK.enumToInt()) && TaskConstant.ALARM_TYPE_COMPRESS.equals(analysisTask.getAlarmTaskType())) {
            alarmEventListener.stop();
            taskManageDao.stopTaskById(taskId);
            return;
        }
        if (analysisTask.getTaskType().equals(TaskTypeEnum.RESOURCESCHEDULETASK.enumToInt())) {
            scheduleCenter.stopTask(taskId, true);
            taskManageDao.stopTaskById(taskId);
            return;
        }
        if (analysisTask.getTaskType().equals(TaskTypeEnum.INDICATORPREDICTTASK.enumToInt())) {
            scheduleCenter.judgeIndicatorPredictStop(analysisTask);
        }

        boolean driftSwitch = false;
        if (analysisTask.getConceptDriftSwitch()) {
            AbstractTaskManage.finishDriftRecord(analysisTask);
            driftSwitch = true;
        }

        scheduleCenter.stopTask(taskId, false, driftSwitch);
        taskManageDao.stopTaskById(taskId);
        if (analysisTask.getTaskType().equals(TaskTypeEnum.INDICATORTASK.enumToInt())) {
            try {
                // 暂停任务的时候要停止没结束的异常
                stopException(analysisTask);
            } catch (ServiceException e) {
                LOGGER.error("[stopTaskById] stop exception handle error, e=", e);
            }
        }
        if (analysisTask.getTaskType().equals(TaskTypeEnum.INDICATORPREDICTTASK.enumToInt())||analysisTask.getTaskType().equals(TaskTypeEnum.INDICATORTASK.enumToInt())) {
            configDataHandler.updateUsedCapacityForFront(analysisTask,TaskConstant.STOP_ACTION);
        }
        if (analysisTask.getTaskType().equals(TaskTypeEnum.INDICATORTASK.enumToInt()) || analysisTask.getTaskType().equals(TaskTypeEnum.INDICATORPREDICTTASK.enumToInt())) {
            TrainingMessageCacheOper.getInstance().removeOneTaskCache(taskId);
        }
    }

    public void stopException(AnalysisTask analysisTask) throws ServiceException {
        stopException(analysisTask, null);
    }

    public void stopOutlierException(AnalysisTask analysisTask, Set<String> indicatorIdSet) throws ServiceException {
        List<IndicatorOutlier> lastedOutlier = indicatorOutlierDao.getLastedOutlierEveryIndicator(analysisTask.getTaskId());
        if (CollectionUtils.isNotEmpty(indicatorIdSet)) {
            lastedOutlier = lastedOutlier.stream()
                .filter(indicatorOutlier -> indicatorIdSet.contains(indicatorOutlier.getIndicatorId()))
                .collect(Collectors.toList());
        }
        List<IndicatorOutlier> updateIndicatorOutlierList = new ArrayList<>();
        for (IndicatorOutlier outlier : lastedOutlier) {
            if (outlier.getEndTime() == 0) {
                Long endTime = indicatorDetectResultDao.queryLatestCollectTimeAbnormalIndicatorDetectResult(
                    SynAnalysisResultConstant.AnalysisResultTableName.INDICATOR_DETECT_TABLE_NAME
                        + analysisTask.getTaskId(), outlier.getStartTime(), outlier.getIndicatorId());
                if (endTime != null) {
                    indicatorOutlierDao.updateIndicatorOutlierEndTime(outlier.getId(), endTime);
                    updateIndicatorOutlierList.add(outlier);
                    LOGGER.info("stopTaskById update indicatorOutlier endTime,outlier= {},endTime={}",
                        outlier, endTime);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(updateIndicatorOutlierList)) {
            taskIndicatorDao.updateIndicatorStatus(updateIndicatorOutlierList, 0);
            sendClearAlarm(analysisTask, updateIndicatorOutlierList);
        }
    }

    public void stopException(AnalysisTask analysisTask, Set<String> indicatorIdSet) throws ServiceException {
        // 暂停任务的时候要停止没结束的异常
        stopOutlierException(analysisTask, indicatorIdSet);

        // 停止缺点异常告警
        List<IndicatorMissDataOutlier> missDataOutliers = indicatorOutlierDao.getClearMissDataOutlierByTaskId(analysisTask.getTaskId());
        if (CollectionUtils.isNotEmpty(indicatorIdSet)) {
            missDataOutliers = missDataOutliers.stream()
                .filter(indicatorOutlier -> indicatorIdSet.contains(indicatorOutlier.getIndicatorId()))
                .collect(Collectors.toList());
        }
        long endTime = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(missDataOutliers)) {
            List<AlarmRecord> alarmRecords = missDataOutliers.stream().filter(missDataOutlier -> Objects.nonNull(missDataOutlier.getAlarmRecord()))
                .map(missDataOutlier -> {
                    AlarmRecord alarmRecord = JSONObject.parseObject(missDataOutlier.getAlarmRecord(), AlarmRecord.class);
                    alarmRecord.setCategory(SynAnalysisResultConstant.AlarmParameters.CATEGORY_CLEAR);
                    alarmRecord.setCleared(SynAnalysisResultConstant.AlarmParameters.ALARM_CLEARED);
                    alarmRecord.setOccurUtc(0);
                    alarmRecord.setClearUtc(endTime);
                    return alarmRecord;
                }).collect(Collectors.toList());
            indicatorDetectResultHandler.recordSendAlarm(SynAnalysisResultConstant.AlarmParameters.CATEGORY_CLEAR, analysisTask.getTaskId(), alarmRecords, JSONArray.parseArray(JSONObject.toJSONString(missDataOutliers), IndicatorOutlier.class));
        }

        indicatorOutlierDao.stopMissDataOutliersByTaskId(analysisTask.getTaskId(), endTime);
    }

    private void sendClearAlarm(AnalysisTask analysisTask, List<IndicatorOutlier> indicatorOutlierList) {
        List<AlarmRecord> alarmRecordList = new ArrayList<>();
        if (analysisTask.getReportAlarm() == 0) {
            LOGGER.info("[stopTaskById] do not need report alarm ,taskId is : {}!", analysisTask.getTaskId());
            return;
        }
        reportIndicatorAlarm(indicatorOutlierList, alarmRecordList, analysisTask);
        indicatorDetectResultHandler.recordSendAlarm(SynAnalysisResultConstant.AlarmParameters.CATEGORY_CLEAR, analysisTask.getTaskId(), alarmRecordList, indicatorOutlierList);
    }

    private void reportIndicatorAlarm(List<IndicatorOutlier> indicatorOutlierList, List<AlarmRecord> alarmRecordList,
        AnalysisTask analysisTask) {
        int severity = analysisTask.getAlarmType();
        String alarmName = AlarmRecordsHandler.indicatorAlarmNameI18n();

        for (IndicatorOutlier indicatorOutlier : indicatorOutlierList) {
            String probableCause = indicatorDetectResultHandler.generateProbableCause(analysisTask.getTaskId(), indicatorOutlier.getIndicatorId());
            String mergeKey = analysisTask.getTaskName() + indicatorOutlier.getIndicatorId();
            AlarmRecord alarmRecord;
            TaskIndicator indicator = AlarmRecordsHandler.getAlarmSource(taskIndicatorDao, analysisTask, indicatorOutlier.getIndicatorId());
            String switchAlarmField = indicatorOutlier.getSwitchAlarmField();
            if (switchAlarmField == null) {
                LOGGER.error("[stopTaskById] Could not get switchAlarmField, maybe cleared or user replace the alarm info.");
            }
            LOGGER.debug("[stopTaskById] Indicator clear alarm switchAlarmField is {}", switchAlarmField);
            alarmRecord = AlarmRecordsHandler.generateAlarmRecord(SynAnalysisResultConstant.AlarmParameters.CATEGORY_CLEAR, indicatorOutlier.getEndTime(),
                probableCause, SynAnalysisResultConstant.AlarmParameters.ALARM_ID_INDICATOR, severity, mergeKey,
                switchAlarmField, alarmName, indicator.getDn(), indicator.getMoType());
            String additionalInfo = AlarmAddInfoBuilder.init()
                .appendKeyValue("UUID", UUID.randomUUID().toString()
                    .replaceAll(SynAnalysisResultConstant.Separator.SEPARATOR_DASH,
                        SynAnalysisResultConstant.Separator.SEPARATOR_NULL)).build();
            alarmRecord.setAdditionalInformation(additionalInfo);
            indicatorOutlier.setSwitchAlarmField(switchAlarmField);
            // 上报告警根据任务填写的告警id和名称上报
            if (!OmcUtil.isOmcMode()) {
                alarmRecord.setAlarmId(analysisTask.getReportAlarmId());
                alarmRecord.setAlarmName(analysisTask.getReportAlarmName());
            }
            alarmRecordList.add(alarmRecord);
        }
    }

    /**
     * 获取任务列表
     *
     * @param param param
     * @return AnalysisTaskList
     * @throws ServiceException ServiceException
     */
    public AnalysisTaskList getAnalysisTaskList(AnalysisTaskListQueryParam param) throws ServiceException {
        return taskManageDao.getAnalysisTaskList(param);
    }

    /**
     * 获取单个任务信息(除了前台查询用这个功能)
     *
     * @param taskId taskId
     * @return AnalysisTask
     * @throws ServiceException ServiceException
     */
    public AnalysisTask getAnalysisTaskById(int taskId) throws ServiceException {
        return taskManageDao.getAnalysisTaskById(taskId);
    }

    public AnalysisTask getAnalysisTaskByIdFromRedis(int taskId) throws ServiceException {
        return taskManageDao.getAnalysisTaskByIdFromRedis(taskId);
    }

    /**
     * 获取单个任务信息(前台查询专用)
     *
     * @param taskId 任务id
     * @return 结果
     * @throws ServiceException 错误
     */
    public AnalysisTask getAnalysisTaskByIdWithAuth(int taskId) throws ServiceException {

        if (!ContextUtils.getContext().getAdmin() && !ContextUtils.getContext()
            .getQueryAuthTaskIds()
            .contains(taskId)) {
            throw new ServiceException("not in privileage!");
        }

        AnalysisTask task = getAnalysisTaskById(taskId);
        if (task == null) {
            throw new ServiceException("task error");
        }
        task.setCanModify(TaskConstant.CAN_MODIFY);

        if (task.getIndicatorSelectType() != null) {
            List<TaskIndicator> taskIndicatorList = taskIndicatorDao.getTaskIndicatorListWithAuth(taskId, null);

            // 如果是冷双机任务，需要在TaskIndicator里面补充双机网元列表AggregateMos
            if (task.getIndicatorPredictScenario() != null
                && task.getIndicatorPredictScenario() == TaskConstant.INDICATOR_PREDICT_SCENARIO_AGGREGATE) {
                handleAggregateMos(task.getAggregateMachineGroupList(), taskIndicatorList);
            }

            // 按网元类型删掉了
            task.setIndicatorList(taskIndicatorList);

            if (!ContextUtils.getContext().getAdmin() && ContextUtils.getContext()
                .getNoModifyAuthTaskIds()
                .contains(taskId)) {
                task.setCanModify(TaskConstant.CANNOT_MODIFY);
            }
        }

        // 日志异常检测的信息需要从另一张表额外查询 -- 这里可以添加一个特殊处理器，根据任务类型做相关操作
        if (task.getTaskType() == 3) {
            LogDetectTaskInfo logDetectTaskInfo = logDetectDao.queryLogDetectTask(task.getTaskId());
            task.setLogIndexName(logDetectTaskInfo.getIndexName());
            task.setLogSolutionType(logDetectTaskInfo.getSolutionType());
            task.setLogAnalysisField(logDetectTaskInfo.getAnalysisField());
            task.setLogFilterColumns(logDetectTaskInfo.getFilterColumns());
        }

        // incident 信息额外字段需要查询
        if (task.getTaskType() == TaskConstant.TASK_TYPE_INCIDENT) {
            // 查询并设置incident任务的额外字段信息
            IncidentTaskInfo incidentTaskInfo = incidentDao.getIncidentTaskById(task.getTaskId());
            if (incidentTaskInfo.getSoftDeleted() == 1) {
                throw new ServiceException("task error");
            }
            task.setAggregateMaxEventCount(incidentTaskInfo.getAggregateMaxEventCount());
            task.setExecuteInterval(incidentTaskInfo.getExecuteInterval());
            if (!AuthTokenUtil.isAuthIncidentTask()) {
                task.setCanModify(TaskConstant.CANNOT_MODIFY);
            }
        }

        if (task.getDatasourceId() != null && task.getTaskType() != TaskTypeEnum.TRIGGERTASK.enumToInt()
            && Integer.parseInt(task.getDatasourceId()) > 99) {
            task.setIndicatorList(new ArrayList<>());
        }

        return task;
    }

    /**
     * 修改场景
     *
     * @param situation situation
     * @throws ServiceException ServiceException
     */
    public void modifySituation(Situation situation) throws ServiceException {
        if (situation.getId() != null) {
            taskManageDao.updateSituation(situation);
        } else {
            taskManageDao.insertSituation(situation);
        }
    }

    /**
     * 获取场景
     *
     * @param situationListQueryParam situationListQueryParam
     * @return SituationList
     * @throws ServiceException ServiceException
     */
    public SituationList getSituationList(SituationListQueryParam situationListQueryParam) throws ServiceException {
        return taskManageDao.getSituationList(situationListQueryParam);
    }

    /**
     * 查询表字段
     *
     * @param customColumnsParam 查询数据集用sql
     * @return List<String>
     * @throws ServiceException ServiceException
     */
    public List<String> customColumns(CustomColumnsParam customColumnsParam) throws ServiceException {
        DataSourceQueryParam param = new DataSourceQueryParam();
        param.setQueryColumns(DataSourceServiceImpl.COLUMNS_HAS);
        param.setId(customColumnsParam.getDataSourceId());
        List<DataSource> dataSource = dataSourceDao.getDataSource(param);
        if (dataSource == null || dataSource.size() == 0) {
            throw new ServiceException("param error!");
        }
        JDBCClient jdbcClient = JDBCClientGenerator.getJDBCClient(dataSource.get(0));
        List<String> columns = new ArrayList<>();
        try {
            ResultSet resultSet = jdbcClient.queryData(customColumnsParam.getSql(), null, new JDBCOpions(1));
            ResultSetMetaData metaData = resultSet.getMetaData();
            for (int i = 1; i <= metaData.getColumnCount(); i++) {
                columns.add(metaData.getColumnName(i));
            }
        } catch (SQLException throwables) {
            LOGGER.error("JDBC error,sql={}", customColumnsParam.getSql());
            throw ExceptionUtil.createException(ExceptionInternationalConstant.CUSTOM_SQL_ERROR);
        } finally {
            jdbcClient.close();
        }
        return columns;
    }

    /**
     * 启动自定义任务的定时任务
     *
     * @param task task
     * @throws ServiceException ServiceException
     */
    public void startSynchronizeCustomDataCorn(AnalysisTask task) throws ServiceException {
        String jobId = "startSynchronizeCustomDataCorn" + task.getTaskId();
        // 如果已经启动过，不重新启动
        JobJson checkJob = CronServiceUtil.queryCronTask(jobId);
        if (checkJob != null && checkJob.getAction() != null) {
            LOGGER.info("repeat cron job,cronTaskId is {}", jobId);
            return;
        }
        JobJson jobJson = new JobJson();
        String cron;
        if (task.getPeriodicType().equals(TaskConstant.PERIODIC_TYPE_CYCLE)) {
            cron = task.getPredictCron();
        } else {
            cron = "1|MIN";
        }
        if (cron.contains("|")) {
            String[] periodMessage = cron.split("\\|");
            setTimedOrLoopJobJson(jobJson, periodMessage);
        } else {
            return;
        }
        jobJson.setAction(SYNCHRONIZE_CUSTOM_DATA_URL + task.getTaskId());
        try {
            CronServiceUtil.startCronTask(jobJson, jobId);
        } catch (ServiceException e) {
            LOGGER.error("[TaskManager] startSynchronizeCustomDataCorn cron job failed. e: ", e);
        }
    }

    /**
     * 升级后重新启动升级之前的第三方数据源定时任务
     *
     * @param contextRefreshedEvent contextRefreshedEvent
     */
    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        if (contextRefreshedEvent.getApplicationContext().getParent() == null) {
            Runnable runnable = () -> {
                try {
                    LOGGER.info("[AbstractTaskHandler] start check custom task's cron job");
                    TaskManageMapper taskManageMapper = mapperFactory.getMapper(TaskManageMapper.class);
                    List<ColumnMapping> columnMappings = taskManageMapper.getAllColumnMapping();
                    for (ColumnMapping columnMapping : columnMappings) {
                        Integer taskId = columnMapping.getTaskId();
                        AnalysisTask task = getAnalysisTaskById(taskId);
                        if (task.getTrainStatus() != null) {
                            startSynchronizeCustomDataCorn(task);
                        }
                    }
                    TrainingMessageCacheOper.getInstance().removeAllTaskCache();
                } catch (Exception e) {
                    LOGGER.error("[AbstractTaskHandler] check custom task's cron job error, e=", e);
                }
            };
            Executors.newSingleThreadExecutor().execute(runnable);
        }
    }

    /**
     * 设定CronService的任务执行单位
     *
     * @param unit unit
     * @return PeriodUnit
     */
    private PeriodUnit setCronUnit(String unit) {
        switch (unit) {
            case "SEC":
                return PeriodUnit.SEC;
            case "MIN":
                return PeriodUnit.MIN;
            case "HOUR":
                return PeriodUnit.HOUR;
            case "DAY":
                return PeriodUnit.DAY;
            case "WEEK":
                return PeriodUnit.WEEK;
            case "MONTH":
                return PeriodUnit.MONTH;
            default:
                return PeriodUnit.FREE;
        }
    }

    /**
     * 设定按时间点调度或者按周期调度
     * 如果是时分秒，支持周期调度，如果是其他，使用时间点调度
     * 时间点调度，如果是日，API要求preiod为0，如果是周，默认周日训练，如果是月，默认1号训练，时间都为当天的0点
     *
     * @param jobJson jobJson
     * @param periodMessage periodMessage
     */

    private void setTimedOrLoopJobJson(JobJson jobJson, String[] periodMessage) {
        if (periodMessage[1].equals("SEC") || periodMessage[1].equals("MIN") || periodMessage[1].equals("HOUR")) {
            jobJson.setType(TriggerType.LOOP);
            jobJson.setPeriod(Integer.parseInt(periodMessage[0]));
        } else {
            // 间隔为日，月，周时设定按时间点调度，为每天，每周一，每月1日调用，因为间隔调用不支持周和月，而日常常用于训练，白天训练会影响推理性能
            jobJson.setType(TriggerType.TIMED);
            // cronservice不支持23：59：59，如果设定为00：00：00，则在大部分情况下，启动任务会导致立即调用推理（因为推理间隔一般设置为日，
            // 周，月），因为按时间点调度的设定是，注册任务会检测现在的时间是否超过了设定的时间，超过了则会立即调用一次。
            jobJson.setTime("23:59:00");
            if (periodMessage[1].equals("MONTH")) {
                jobJson.setPeriod(1);
            } else {
                jobJson.setPeriod(0);
            }
        }
        jobJson.setPeriodUnit(setCronUnit(periodMessage[1]));
    }

    /**
     * 同步自定义数据源数据
     *
     * @throws ServiceException ServiceException
     */
    public void synchronizeCustomData() throws ServiceException {
        TaskManageMapper taskManageMapper = mapperFactory.getMapper(TaskManageMapper.class);
        LOGGER.info("synchronizeCustomData start");
        List<PrometheusData> prometheusDataList = taskManageMapper.getPrometheusData(null);
        for (PrometheusData prometheusData : prometheusDataList) {
            syncProExecutor.execute(() -> {
                try {
                    long startTime = 0L;
                    if (LOGGER.isDebugEnabled()) {
                        startTime = System.currentTimeMillis();
                    }
                    synchronizePromethuesData(prometheusData);
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.info("synchronizeCustomData, prometheusData = {}, cost time={}",
                            prometheusData.getTaskId(), System.currentTimeMillis() - startTime);
                    }
                } catch (Exception e) {
                    LOGGER.error("synchronizeOnePQLData error {}", e);
                }
            });
        }
    }

    public void synchronizeCustomDataZenith(Integer taskId) {
        TaskManageMapper taskManageMapper = mapperFactory.getMapper(TaskManageMapper.class);
        ColumnMapping columnMapping = taskManageMapper.getColumnMapping(taskId);
        // 普咯米修斯数据源不需要启动
        if (columnMapping == null) {
            LOGGER.info("synchronizeCustomDataZenith columnMapping is null taskID = {}", taskId);
            return;
        }
        LOGGER.info("synchronizeCustomDataZenith start taskID = {}", taskId);
        try {
            synchronizeOneCustomData(columnMapping);
        } catch (Exception e) {
            LOGGER.error("synchronizeOneCustomData error ", e);
        }
    }

    /**
     * 同步单个任务的数据
     *
     * @param prometheusData 对应关系
     * @throws ServiceException 异常
     */
    private void synchronizePromethuesData(PrometheusData prometheusData) throws ServiceException {
        DataSourceQueryParam dataSourceQueryParam = new DataSourceQueryParam();
        dataSourceQueryParam.setQueryColumns(DataSourceServiceImpl.COLUMNS_HAS);
        dataSourceQueryParam.setId(prometheusData.getDataSourceId());
        DataSource dataSource = dataSourceDao.getDataSource(dataSourceQueryParam).get(0);
        if (dataSource == null) {
            LOGGER.error("datasource not exist {}", prometheusData.getDataSourceId());
            return;
        }

        TaskCustomDataMapper taskCustomDataMapper = mapperFactory.getMapper(TaskCustomDataMapper.class);
        int dataNum = taskCustomDataMapper.selectTableDataCount(prometheusData.getTaskId());
        int timeCount = dataNum == 0
            ? TaskConstant.PROMETHEUS_FIRST_TIME_COUNT
            : TaskConstant.PROMETHUS_DEFAULT_TIME_COUNT;

        List<PqlsEntity> pqls = JSONArray.parseArray(prometheusData.getPqls(), PqlsEntity.class);
        List<TaskIndicator> taskIndicators = new ArrayList<>();
        TaskIndicatorMapper mapper = mapperFactory.getMapper(TaskIndicatorMapper.class);
        Map<String, TaskIndicator> existIndicator = mapper.getTaskIndicatorList(prometheusData.getTaskId(),
                null, null, null).stream()
            .collect(Collectors.toMap(TaskIndicator::getIndicatorId, Function.identity(), (v1, v2) -> v2));
        // 查询该任务是否配置了自动更新指标和指标废弃评估时间
        AnalysisTask task = taskManageDao.getTaskUpdateConfigById(prometheusData.getTaskId());
        for (PqlsEntity pql : pqls) {
            long currentTime = System.currentTimeMillis();
            if (timeCount == TaskConstant.PROMETHEUS_FIRST_TIME_COUNT) {
                RangeQueryParam rangeQueryParam = new RangeQueryParam(dataSource.getUrl(), pql.getPql(),
                    prometheusData.getStep(), currentTime, dataSource.getUserName(),
                    EncryptionUtil.decryptByCloudSop(dataSource.getPassWord()), dataSource.getCertId(), null, null,
                    TaskConstant.PROMETHUS_DEFAULT_TIME_TYPE_RECENT, timeCount, ConfigurationUtil.getPrometheusRestMaxBodySize());
                long stepTime = PromethusClient.convertTime(rangeQueryParam.getStep());
                long start = currentTime - stepTime * 1000 * TaskConstant.PROMETHEUS_FIRST_TIME_COUNT;
                while (start < currentTime) {
                    long middleTime = start;
                    middleTime += SPLIT_INTERVAL * stepTime * 1000;
                    if (middleTime > currentTime) {
                        middleTime = currentTime;
                    }
                    rangeQueryParam.setStartTime(start);
                    rangeQueryParam.setEndTime(middleTime);
                    // 分段查询数据
                    handlePromethuesDataSub(rangeQueryParam, prometheusData, pql, taskCustomDataMapper, taskIndicators, existIndicator, task.getUpdateIndicatorAuto());
                    start = middleTime;
                }
            } else {
                RangeQueryParam rangeQueryParam = new RangeQueryParam(dataSource.getUrl(), pql.getPql(),
                    prometheusData.getStep(), currentTime, dataSource.getUserName(),
                    EncryptionUtil.decryptByCloudSop(dataSource.getPassWord()), dataSource.getCertId(), null, null,
                    TaskConstant.PROMETHUS_DEFAULT_TIME_TYPE_RECENT, timeCount, ConfigurationUtil.getPrometheusTestRestMaxBodySize());
                handlePromethuesDataSub(rangeQueryParam, prometheusData, pql, taskCustomDataMapper, taskIndicators, existIndicator, task.getUpdateIndicatorAuto());
            }
        }
        updatePromethuesIndicator(taskIndicators, existIndicator, task);
    }

    private void handlePromethuesDataSub(RangeQueryParam rangeQueryParam, PrometheusData prometheusData, PqlsEntity pql,
        TaskCustomDataMapper taskCustomDataMapper, List<TaskIndicator> taskIndicators, Map<String, TaskIndicator> existIndicator, Boolean updateIndicatorAuto) throws ServiceException {
        List<CustomIndicatorData> data = new ArrayList<>();
        PromethusResponseData promethusResponseData = PromethusClient.queryRangeData(rangeQueryParam);
        if (CollectionUtils.isEmpty(promethusResponseData.getResult())) {
            return;
        }
        for (PromethusResponseDataResult promethusResponseDataResult : promethusResponseData.getResult()) {
            if (CollectionUtils.isEmpty(promethusResponseDataResult.getValues())) {
                continue;
            }
            String indicatorId = prometheusData.getTaskId() + TaskConstant.CUSTOM_INDICATOR_ID_SEPARATOR
                + SHA256Util.decrypt(pql.getPql() + promethusResponseDataResult.getMetric());
            if (updateIndicatorAuto == null || !updateIndicatorAuto) {
                if (!existIndicator.containsKey(indicatorId)) {
                    continue;
                }
            }
            Long lastUpdateTime = taskCustomDataMapper.lastDataUpdateTime(prometheusData.getTaskId(), indicatorId);
            for (List<Object> value : promethusResponseDataResult.getValues()) {
                if (lastUpdateTime != null) {
                    if (Long.parseLong((value.get(0).toString())) * 1000 - lastUpdateTime
                        < PromethusClient.convertTime(prometheusData.getStep()) * 1000) {
                        continue;
                    }
                }
                data.add(genData(indicatorId, Double.parseDouble(value.get(1).toString()),
                    Long.parseLong((value.get(0).toString())) * 1000));
            }
        }
        taskIndicators.addAll(
            PromethusClient.genPrometheusIndicator(promethusResponseData, pql.getPql(), prometheusData.getTaskId(),
                pql.getAlias()));
        updateCustomIndicatorData(data, prometheusData.getTaskId());
    }

    private void updatePromethuesIndicator(List<TaskIndicator> taskIndicatorList, Map<String, TaskIndicator> existIndicator, AnalysisTask task) {
        if (task.getUpdateIndicatorAuto() == null || !task.getUpdateIndicatorAuto() || CollectionUtils.isEmpty(taskIndicatorList)) {
            return;
        }
        long startTime = System.currentTimeMillis();
        long discardTime = task.getIndicatorDiscardTime() * 86400000L;
        long currentTime = System.currentTimeMillis();
        TaskCustomDataMapper taskCustomDataMapper = mapperFactory.getMapper(TaskCustomDataMapper.class);
        List<CustomIndicatorData> latestDataTimeList = taskCustomDataMapper.getTableIndicatorIdsLatestTime(task.getTaskId());
        Map<String, Long> latestDataTimeMap = latestDataTimeList.stream()
            .collect(Collectors.toMap(CustomIndicatorData::getIndicatorId, CustomIndicatorData::getCollectTime));
        Set<String> indicatorIds = taskIndicatorList.stream().map(TaskIndicator::getIndicatorId).collect(Collectors.toSet());
        indicatorIds.addAll(existIndicator.keySet());
        Set<String> discardIndicatorIds = new HashSet<>();
        Set<String> retainedIndicatorIds = new HashSet<>();
        for (String id : indicatorIds) {
            if (latestDataTimeMap.containsKey(id)) {
                if (currentTime - latestDataTimeMap.get(id) > discardTime) {
                    discardIndicatorIds.add(id);
                } else {
                    retainedIndicatorIds.add(id);
                }
            }
        }

        // 新的list集合
        List<TaskIndicator> insertList = new ArrayList<>();
        processTaskIndicatorsPro(taskIndicatorList, existIndicator, retainedIndicatorIds, insertList);
        List<TaskIndicator> discardList = new ArrayList<>();
        try {
            insertIndicators(task, existIndicator.keySet(), discardList, insertList);
        } catch (ServiceException e) {
            LOGGER.error("insertIndicators error,taskId={}", task.getTaskId());
        }
        if (CollectionUtils.isNotEmpty(discardList)) {
            discardIndicatorIds.addAll(discardList.stream().map(TaskIndicator::getIndicatorId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(discardIndicatorIds)) {
            // 删除数据表中数据
            taskCustomDataMapper.deleteData(task.getTaskId(), null, new ArrayList<>(discardIndicatorIds));
        }
        LOGGER.info("updatePromethuesIndicator cost time={}", System.currentTimeMillis() - startTime);
    }

    public void processTaskIndicatorsPro(List<TaskIndicator> taskIndicatorList,
        Map<String, TaskIndicator> existIndicator, Set<String> retainedIndicatorIds, List<TaskIndicator> insertList) {
        // 将 taskIndicatorList 转换为 Map
        Map<String, TaskIndicator> taskIndicatorMap = new HashMap<>();
        for (TaskIndicator indicator : taskIndicatorList) {
            taskIndicatorMap.put(indicator.getIndicatorId(), indicator);
        }

        // 创建一个Set用于检查插入的IndicatorId是否重复
        Set<String> addedIndicatorIds = new HashSet<>();

        for (String id : retainedIndicatorIds) {
            TaskIndicator indicatorToAdd = null;

            if (existIndicator.containsKey(id)) {
                indicatorToAdd = existIndicator.get(id);
            } else if (taskIndicatorMap.containsKey(id)) {
                indicatorToAdd = taskIndicatorMap.get(id);
            }

            if (indicatorToAdd != null && !addedIndicatorIds.contains(indicatorToAdd.getIndicatorId())) {
                insertList.add(indicatorToAdd);
                addedIndicatorIds.add(indicatorToAdd.getIndicatorId());
            }
        }
    }

    private CustomIndicatorData genData(String indicatorId, Double data, Long time) {
        CustomIndicatorData customIndicatorData = new CustomIndicatorData();
        customIndicatorData.setIndicatorId(indicatorId);
        customIndicatorData.setIndicatorValue(data);
        customIndicatorData.setCollectTime(time);
        return customIndicatorData;
    }

    /**
     * 同步单个任务的数据
     *
     * @param columnMapping 对应关系
     * @throws ServiceException 异常
     */
    private void synchronizeOneCustomData(ColumnMapping columnMapping) throws ServiceException {
        DataSource dataSource = taskManageDao.getDataSource(columnMapping.getDataSourceId());
        if (dataSource == null) {
            LOGGER.error("datasource not exist {}", columnMapping.getDataSourceId());
            return;
        }
        TaskCustomDataMapper taskCustomDataMapper = mapperFactory.getMapper(TaskCustomDataMapper.class);
        TaskIndicatorMapper taskIndicatorMapper = mapperFactory.getMapper(TaskIndicatorMapper.class);
        List<TaskIndicator> taskIndicatorList = taskIndicatorMapper.getTaskIndicatorList(columnMapping.getTaskId(), null, null, null);
        handleCustomIndicatorData(dataSource, columnMapping, taskIndicatorList,
            new HashSet<>(taskCustomDataMapper.getTableIndicatorIds(columnMapping.getTaskId())));
    }

    /**
     * 获取第三方数据源查询回来的数据
     *
     * @param dataSource 数据源
     * @param columnMapping 字段对应
     * @param taskIndicatorList 指标
     * @param existIndicatorIds 是否保存所有的数据
     * @throws ServiceException 异常
     */
    private void handleCustomIndicatorData(DataSource dataSource, ColumnMapping columnMapping, List<TaskIndicator> taskIndicatorList,
        Set<String> existIndicatorIds) throws ServiceException {
        JDBCClient jdbcClient = JDBCClientGenerator.getJDBCClient(dataSource);
        Map<String, CustomIndicatorData> insertDataMap = new HashMap<>();
        Set<String> indicatorIds = taskIndicatorList.stream().map(TaskIndicator::getIndicatorId).collect(Collectors.toSet());
        // 查询该任务是否配置了自动更新指标和指标废弃评估时间
        AnalysisTask task = taskManageDao.getTaskUpdateConfigById(columnMapping.getTaskId());
        List<TaskIndicator> newTaskIndicatorList = new ArrayList<>();
        if (task.getUpdateIndicatorAuto() != null && task.getUpdateIndicatorAuto()) {
            newTaskIndicatorList = customIndicatorSqlExcutor.getIndicatorFromSql(columnMapping);
            indicatorIds.addAll(newTaskIndicatorList.stream().map(TaskIndicator::getIndicatorId).collect(Collectors.toSet()));
        }
        try {
            String sql = handleSql(columnMapping, dataSource.getType());
            ResultSet resultSet = jdbcClient.queryData(sql, null, new JDBCOpions(1000));
            ResultSetMetaData metaData = resultSet.getMetaData();
            if (!customIndicatorSqlExcutor.isColumnValidate(metaData, columnMapping)) {
                LOGGER.error("taskId is {} column error,please check sql", columnMapping.getTaskId());
                throw new ServiceException("taskId is {} column error,please check sql");
            }
            int handleNum = 0;

            List<Map<String, Object>> dataMapList = new ArrayList<>();
            long resultLen = 0L;
            while (resultSet.next()) {
                Map<String, Object> dataMap = new HashMap<>();
                for (int i = 1; i < metaData.getColumnCount() + 1; i++) {
                    if (DataSourceConnectType.ORACLE.equals(dataSource.getType())) {
                        dataMap.put(metaData.getColumnName(i), resultSet.getObject(i));
                    } else {
                        resultLen = JDBCClient.checkAndReadData(metaData, i, resultSet, dataMap, resultLen);
                    }
                }
                dataMapList.add(dataMap);
                handleNum++;
                if (handleNum >= 1000) {
                    handleSaveAllData(existIndicatorIds, dataMapList, columnMapping, indicatorIds, insertDataMap);
                    dataMapList.clear();
                    handleNum = 0;
                }
            }
            handleSaveAllData(existIndicatorIds, dataMapList, columnMapping, indicatorIds, insertDataMap);
        } catch (SQLException | ServiceException exception) {
            LOGGER.error("get MetaData Error,sql={}", columnMapping.getSql());
            throw new ServiceException("data error");
        } finally {
            jdbcClient.close();
        }
        if (!insertDataMap.values().isEmpty()) {
            updateCustomIndicatorData(new ArrayList<>(insertDataMap.values()), columnMapping.getTaskId());
        }
        clearAbandonedIndicator(indicatorIds, task, existIndicatorIds, taskIndicatorList, newTaskIndicatorList);
    }

    private void clearAbandonedIndicator(Set<String> indicatorIds, AnalysisTask task, Set<String> existIndicatorIds, List<TaskIndicator> taskIndicatorList, List<TaskIndicator> newTaskIndicatorList) {
        if (task.getUpdateIndicatorAuto() == null || !task.getUpdateIndicatorAuto()) {
            return;
        }
        long startTime = System.currentTimeMillis();
        long discardTime = task.getIndicatorDiscardTime() * 86400000L;
        long currentTime = System.currentTimeMillis();
        TaskCustomDataMapper taskCustomDataMapper = mapperFactory.getMapper(TaskCustomDataMapper.class);
        List<CustomIndicatorData> latestDataTimeList = taskCustomDataMapper.getTableIndicatorIdsLatestTime(task.getTaskId());
        Map<String, Long> latestDataTimeMap = latestDataTimeList.stream()
            .collect(Collectors.toMap(CustomIndicatorData::getIndicatorId, CustomIndicatorData::getCollectTime));

        Set<String> discardIndicatorIds = new HashSet<>();
        Set<String> retainedIndicatorIds = new HashSet<>();
        for (String id : indicatorIds) {
            if (latestDataTimeMap.containsKey(id)) {
                if (currentTime - latestDataTimeMap.get(id) > discardTime) {
                    discardIndicatorIds.add(id);
                } else {
                    retainedIndicatorIds.add(id);
                }
            }
        }
        List<TaskIndicator> discardList = new ArrayList<>();
        // 新的list集合
        List<TaskIndicator> insertList = new ArrayList<>();
        processTaskIndicatorsSql(taskIndicatorList, newTaskIndicatorList, retainedIndicatorIds, insertList);
        try {
            insertIndicators(task, existIndicatorIds, discardList, insertList);
        } catch (ServiceException e) {
            LOGGER.error("insertIndicators error,taskId={}", task.getTaskId());
        }
        if (CollectionUtils.isNotEmpty(discardList)) {
            discardIndicatorIds.addAll(discardList.stream().map(TaskIndicator::getIndicatorId).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(discardIndicatorIds)) {
            // 删除数据表中数据
            taskCustomDataMapper.deleteData(task.getTaskId(), null, new ArrayList<>(discardIndicatorIds));
        }
        LOGGER.info("clearAbandonedIndicator cost time={}", System.currentTimeMillis() - startTime);
    }

    public void processTaskIndicatorsSql(List<TaskIndicator> taskIndicatorList,
        List<TaskIndicator> newTaskIndicatorList, Set<String> retainedIndicatorIds, List<TaskIndicator> insertList) {
        // 将 taskIndicatorList 转换为 Map
        Map<String, TaskIndicator> taskIndicatorMap = new HashMap<>();
        for (TaskIndicator indicator : taskIndicatorList) {
            taskIndicatorMap.put(indicator.getIndicatorId(), indicator);
        }

        Map<String, TaskIndicator> newTaskIndicatorMap = new HashMap<>();
        for (TaskIndicator indicator : newTaskIndicatorList) {
            newTaskIndicatorMap.put(indicator.getIndicatorId(), indicator);
        }

        // 创建一个Set用于检查插入的IndicatorId是否重复
        Set<String> addedIndicatorIds = new HashSet<>();

        for (String id : retainedIndicatorIds) {
            TaskIndicator indicatorToAdd = null;

            if (taskIndicatorMap.containsKey(id)) {
                indicatorToAdd = taskIndicatorMap.get(id);
            } else if (newTaskIndicatorMap.containsKey(id)) {
                indicatorToAdd = newTaskIndicatorMap.get(id);
            }

            if (indicatorToAdd != null && !addedIndicatorIds.contains(indicatorToAdd.getIndicatorId())) {
                insertList.add(indicatorToAdd);
                addedIndicatorIds.add(indicatorToAdd.getIndicatorId());
            }
        }
    }

    private void insertIndicators(AnalysisTask task, Set<String> existIndicatorIds, List<TaskIndicator> discardList,
        List<TaskIndicator> insertList) throws ServiceException {
        TaskIndicatorMapper taskIndicatorMapper = mapperFactory.getMapper(TaskIndicatorMapper.class);
        TaskIndicator taskIndicator = new TaskIndicator();
        taskIndicator.setTaskId(task.getTaskId());
        try {
            taskIndicatorMapper.delete(taskIndicator);
        } catch (DataAccessException e) {
            LOGGER.error("delete indicators error : taskId={},e={}", task.getTaskId(), e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }

        // 舍弃多余的指标
        Integer taskIndicatorListTotal = taskIndicatorMapper.getTaskIndicatorListTotal(null, null);
        int allMax = ConfigurationUtil.indicatorTaskMaxSize() - taskIndicatorListTotal;
        int remainCount;
        if (task.getTaskType().equals(TaskConstant.TASK_TYPE_INDICATOR_EXCEPTION)) {
            int taskNumber = TaskConstant.HOMOGENEOUS_COMPARISON_AGRI_NAME.equalsIgnoreCase(task.getAlgorithmModelName())
                ? ConfigurationUtil.getIndicatorOneTaskHomogeneous()
                : ConfigurationUtil.getIndicatorOneTask();
            remainCount = Math.min(allMax, taskNumber);
        } else {
            remainCount = Math.min(allMax, ConfigurationUtil.getIndicatorOneTask());
        }
        List<TaskIndicator> list = new ArrayList<>();
        if (remainCount < insertList.size()) {
            for (TaskIndicator indicator : insertList) {
                if (existIndicatorIds.contains(indicator.getIndicatorId()) && list.size() < remainCount) {
                    list.add(indicator);
                } else {
                    discardList.add(indicator);
                }
            }
            while (list.size() < remainCount && !discardList.isEmpty()) {
                list.add(discardList.remove(0));
            }
        } else {
            list = insertList;
        }

        try {
            if (CollectionUtils.isNotEmpty(list)) {
                if (task.getTaskType().equals(TaskConstant.TASK_TYPE_INDICATOR_EXCEPTION)) {
                    int indicatorTaskType = TaskConstant.ANOMALYDETECTION_AGRI_NAME.equalsIgnoreCase(task.getAlgorithmModelName())
                        ? TaskTypeEnum.INDICATORTASK.enumToInt()
                        : TaskTypeEnum.HOMOGENEOUSCOMPARISONTASK.enumToInt();
                    list.forEach(indicator -> indicator.setIndicatorTaskType(indicatorTaskType));
                }
                taskIndicatorMapper.insertIndicators(list);
            }
        } catch (Exception e) {
            LOGGER.error("insert indicator error : taskId={}, e={}", task.getTaskId(), e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    /**
     * 增量查询
     *
     * @param columnMapping columnMapping
     * @param dataSourceType 数据源类型
     * @return sql
     */
    public String handleSql(ColumnMapping columnMapping, String dataSourceType) {
        String sql = columnMapping.getSql();
        CustomTaskMapper customTaskMapper = mapperFactory.getMapper(CustomTaskMapper.class);
        int records = customTaskMapper.countRecords(columnMapping.getTaskId());
        long currentTime = System.currentTimeMillis();
        currentTime = String.valueOf(currentTime).length() == 13 ? currentTime : currentTime * 1000;
        String timeKey = columnMapping.getTimeKey();
        if (records > 0) {
            long lastTime = customTaskMapper.getLatestTime(columnMapping.getTaskId());
            sql = "SELECT * FROM (" + sql + ") WHERE " + timeKey + " > " + lastTime + " AND " + timeKey + " <= " + currentTime;
        } else {
            sql = taskManagerSub.getSqlByType(dataSourceType, sql, currentTime, timeKey);
        }
        return sql;
    }

    private void handleSaveAllData(Set<String> existIndicatorIds, List<Map<String, Object>> dataMapList,
        ColumnMapping columnMapping, Set<String> indicatorIds, Map<String, CustomIndicatorData> finalData) {
        List<CustomIndicatorData> tmpData = customIndicatorSqlExcutor.getSqlIndicatorData(dataMapList,
            columnMapping, indicatorIds);
        List<CustomIndicatorData> insertData = new ArrayList<>();
        for (CustomIndicatorData tmpDatum : tmpData) {
            if (!existIndicatorIds.contains(tmpDatum.getIndicatorId())) {
                insertData.add(tmpDatum);
                continue;
            }
            if (finalData.get(tmpDatum.getIndicatorId()) == null) {
                finalData.put(tmpDatum.getIndicatorId(), tmpDatum);
                continue;

            }
            if (finalData.get(tmpDatum.getIndicatorId()).getCollectTime() < tmpDatum.getCollectTime()) {
                finalData.put(tmpDatum.getIndicatorId(), tmpDatum);
            }
        }
        if (!insertData.isEmpty()) {
            updateCustomIndicatorData(insertData, columnMapping.getTaskId());
        }
    }

    /**
     * 更新数据
     *
     * @param data 数据
     * @param taskId 任务ID
     */
    private void updateCustomIndicatorData(List<CustomIndicatorData> data, Integer taskId) {
        TaskCustomDataMapper taskCustomDataMapper = mapperFactory.getMapper(TaskCustomDataMapper.class);
        if (data.size() != 0) {
            List<List<CustomIndicatorData>> dataList = ListUtils.partition(data, LIST_SIZE);
            for (List<CustomIndicatorData> customData : dataList) {
                taskCustomDataMapper.insertData(taskId, customData);
            }
        }
    }

    /**
     * 删出超时的数据
     */
    public void deleteTimeOutCustomData() {
        long twentyOneDaysAgo = System.currentTimeMillis() - TWENTY_ONE_DAYS_MILL;
        TaskManageMapper taskManageMapper = mapperFactory.getMapper(TaskManageMapper.class);
        List<ColumnMapping> columnMappings = taskManageMapper.getAllColumnMapping();
        TaskCustomDataMapper taskCustomDataMapper = mapperFactory.getMapper(TaskCustomDataMapper.class);
        columnMappings.forEach(
            columnMapping -> taskCustomDataMapper.deleteData(columnMapping.getTaskId(), twentyOneDaysAgo, null));
        List<PrometheusData> prometheusDataList = taskManageMapper.getPrometheusData(null);
        TaskIndicatorMapper mapper = mapperFactory.getMapper(TaskIndicatorMapper.class);
        prometheusDataList.forEach(prometheusData -> {
            taskCustomDataMapper.deleteData(prometheusData.getTaskId(), twentyOneDaysAgo, null);
            List<String> tableIndicatorIds = taskCustomDataMapper.getTableIndicatorIds(prometheusData.getTaskId());
            List<String> collect = mapper.getTaskIndicatorList(prometheusData.getTaskId(), null, null, null)
                .stream()
                .map(TaskIndicator::getIndicatorId)
                .collect(Collectors.toList());
            for (String tableIndicatorId : tableIndicatorIds) {
                collect.remove(tableIndicatorId);
            }
            if (collect.size() != 0) {
                TaskIndicator taskIndicator = new TaskIndicator();
                taskIndicator.setTaskId(prometheusData.getTaskId());
                for (String indicatorId : collect) {
                    taskIndicator.setIndicatorId(indicatorId);
                    mapper.delete(taskIndicator);
                }
            }
        });
    }

    /**
     * 校验是否具有该taskid的操作权限
     *
     * @param taskId taskId
     * @throws ServiceException ServiceException
     */
    public void validatePrivilege(Integer taskId) throws ServiceException {
        if (ContextUtils.getContext().getAdmin()) {
            return;
        }
        Set<Integer> taskIdNotAuth = ContextUtils.getContext().getNoModifyAuthTaskIds();
        if (taskIdNotAuth.contains(taskId)) {
            LOGGER.error("user has no privileage of {}", taskId);
            OperationLogUtil.sendAuthFailedSecurityLog(LogConstant.TASK_AUTHENTICATION_FAILED, new Object[]{taskId});
            throw ExceptionUtil.createException(ExceptionInternationalConstant.USER_PERMISSION_ERROR);
        }
    }

    public PqlValidateResponse validatePql(PqlQueryParam param) throws ServiceException {
        String dataSourceId = param.getDataSourceId();
        DataSourceQueryParam dataSourceQueryParam = new DataSourceQueryParam();
        dataSourceQueryParam.setQueryColumns(DataSourceServiceImpl.COLUMNS_HAS);
        dataSourceQueryParam.setId(Integer.valueOf(dataSourceId));
        DataSource dataSource = dataSourceDao.getDataSource(dataSourceQueryParam).get(0);
        PqlValidateResponse pqlValidateResponse = new PqlValidateResponse();
        List<PqlResult> pqlDatas = new ArrayList<>();
        boolean isCheckSuccess = getResultAndCheckPql(param, dataSource, pqlDatas);
        if (isCheckSuccess) {
            pqlValidateResponse.setCode(TaskConstant.PROMETHUS_CHECK_CODE_SUCCESS);
        } else {
            pqlValidateResponse.setCode(TaskConstant.PROMETHUS_CHECK_CODE_FAIL);
        }
        pqlValidateResponse.setResultMessage(pqlDatas);
        return pqlValidateResponse;
    }

    private boolean getResultAndCheckPql(PqlQueryParam param, DataSource dataSource, List<PqlResult> pqlDatas) {
        boolean isCheckSuccess = true;
        for (String pql : param.getPqls()) {
            RangeQueryParam rangeQueryParam = new RangeQueryParam(dataSource.getUrl(), pql, param.getStep(),
                System.currentTimeMillis(), dataSource.getUserName(),
                EncryptionUtil.decryptByCloudSop(dataSource.getPassWord()), dataSource.getCertId(), null, null,
                TaskConstant.PROMETHUS_DEFAULT_TIME_TYPE_RECENT, TaskConstant.PROMETHUS_DEFAULT_TIME_COUNT, ConfigurationUtil.getPrometheusTestRestMaxBodySize());
            PromethusResponseData promethusResponseData;
            PqlResult pqlResult = new PqlResult();
            pqlDatas.add(pqlResult);
            pqlResult.setPql(pql);
            try {
                promethusResponseData = PromethusClient.queryRangeData(rangeQueryParam);
            } catch (ServiceException exception) {
                LOGGER.error("pql execute error", exception);
                pqlResult.setCode(TaskConstant.PROMETHUS_PQL_EXECUTE_FAIL);
                isCheckSuccess = false;
                continue;
            }
            pqlResult.setData(JSONObject.toJSONString(promethusResponseData));
            if (CollectionUtils.isEmpty(promethusResponseData.getResult())) {
                pqlResult.setCode(TaskConstant.PROMETHEUS_PQL_NO_DATA);
                isCheckSuccess = false;
                continue;
            }
            if (CollectionUtils.isEmpty(promethusResponseData.getResult().get(0).getValues())) {
                pqlResult.setCode(TaskConstant.PROMETHEUS_PQL_NO_DATA);
                isCheckSuccess = false;
                continue;
            }
            pqlResult.setCode(TaskConstant.PROMETHEUS_PQL_SUCCESS);
        }
        return isCheckSuccess;
    }

    public void checkIndicator(IndicatorValidParameter param) throws ServiceException {
        TaskIndicatorMapper mapper = mapperFactory.getMapper(TaskIndicatorMapper.class);
        List<TaskIndicator> indicators = mapper.getTaskIndicatorList(null, null, param.getDataSourceType(), null);
        Map<String, TaskIndicator> indicatorMap = new HashMap<>();
        indicators.forEach(taskIndicator -> indicatorMap.put(taskIndicator.getIndicatorId(), taskIndicator));
        List<TriggerIndicator> indicatorList = param.getIndicatorList();
        for (TriggerIndicator triggerIndicator : indicatorList) {
            if (indicatorMap.get(triggerIndicator.getIndicatorId()) == null) {
                throw new ServiceException();
            }
        }
    }

    public void saveTriggerTaskCheckResult(TriggerTaskCheckResult param) {
        TaskManageMapper mapper = mapperFactory.getMapper(TaskManageMapper.class);
        if (param.getResult() == 1) {
            mapper.updateTriggerTask(param.getTaskName(), TaskConstant.TASK_STATUS_START, null);
        } else {
            mapper.updateTriggerTask(param.getTaskName(), TaskConstant.TASK_STATUS_VALIDATE_FAILED, param.getDesc());
        }
    }

    public List<TaskIndicator> queryIndicatorInstanceByType(IndicatorInstancesParam instancesParam) throws ServiceException {
        List<TaskIndicator> indicators = instancesParam.getIndicators();
        List<TaskIndicator> resultList = new ArrayList<>();
        List<ManagedObject> objectList = getMoTypeInstance(instancesParam);
        boolean hasDnRight = false;
        int maxCount;
        if (indicators.size() == 1) {
            maxCount = 2500;
        } else {
            maxCount = 3501;
        }
        Map<String, String> moNameMap = objectList.stream().collect(Collectors.toMap(object -> object.getDN().getValue(), object -> object.getName(), (existing, replacement) -> existing));
        String moType = indicators.get(0).getMoType();
        String measUnitKey = indicators.get(0).getMeasUnitKey();
        List<String> originalValues = new ArrayList<>();
        for (TaskIndicator indicator : indicators) {
            originalValues.add(indicator.getOriginalValue());
        }
        List<Map<String, String>> originalValueMap = TriggerTaskUtil.getDnByOrignalValue(moType, measUnitKey, originalValues);
        if (CollectionUtils.isNotEmpty(originalValueMap)) {
            hasDnRight = getIndicatorOriginalValueNew(indicators, resultList, hasDnRight, moNameMap, originalValueMap, maxCount, instancesParam.getDeploymentMoType());
        } else {
            int count = 0;
            for (ManagedObject mo : objectList) {
                if (count >= maxCount) {
                    break;
                }
                if (!ContextUtils.getContext().getAdmin() && !ContextUtils.getContext().getAuthDns().contains(mo.getDN().getValue())) {
                    continue;
                }
                hasDnRight = true;
                for (TaskIndicator indicator : indicators) {
                    if (count >= maxCount) {
                        break;
                    }
                    createIndicator(resultList, mo.getName(), mo.getDN().getValue(), indicator, instancesParam.getDeploymentMoType());
                    count++;
                }
            }
        }
        if (!hasDnRight) {
            throw ExceptionUtil.createException(ExceptionInternationalConstant.INDICATOR_INSTANCE_ERROR);
        }
        return resultList;
    }

    public List<ManagedObject> getMoTypeInstance(IndicatorInstancesParam instancesParam) {
        List<ManagedObject> objectList = new ArrayList<>();
        if (CollectionUtils.isEmpty(instancesParam.getIndicators())) {
            return objectList;
        }
        String moType = instancesParam.getIndicators().get(0).getMoType();
        if (StringUtils.isNotEmpty(instancesParam.getSolutionId()) && StringUtils.isNotEmpty(instancesParam.getDeploymentMoType())) {
            List<ManagedObject> objects = PerformanceClient.getInstance().filterBySolutionByMoType(instancesParam.getSolutionId(), instancesParam.getDeploymentMoType());
            objectList = PerformanceClient.getInstance().queryDeploymentInstance(objects, moType);
        } else if (StringUtils.isNotEmpty(instancesParam.getSolutionId()) && StringUtils.isEmpty(instancesParam.getDeploymentMoType())) {
            objectList = PerformanceClient.getInstance().filterBySolutionByMoType(instancesParam.getSolutionId(), moType);
        } else if (StringUtils.isEmpty(instancesParam.getSolutionId()) && StringUtils.isNotEmpty(instancesParam.getDeploymentMoType())) {
            List<ManagedObject> objects = PerformanceClient.getInstance().getMoByType(instancesParam.getDeploymentMoType());
            objectList = PerformanceClient.getInstance().queryDeploymentInstance(objects, moType);
        } else if (StringUtils.isEmpty(instancesParam.getSolutionId()) && StringUtils.isEmpty(instancesParam.getDeploymentMoType())) {
            objectList = PerformanceClient.getInstance().getMoByType(moType);
        }
        if (CollectionUtils.isEmpty(objectList)) {
            LOGGER.error("queryIndicatorInstanceByType instance is empty");
        }
        return objectList;
    }

    private boolean getIndicatorOriginalValueNew(List<TaskIndicator> indicators, List<TaskIndicator> resultList, boolean hasDnRight,
        Map<String, String> moNameMap, List<Map<String, String>> originalValueMap, int maxCount, String deploymentMoType) {
        Map<String, TaskIndicator> orignalMap = indicators.stream()
            .collect(Collectors.toMap(TaskIndicator::getOriginalValue, Function.identity()));
        int count = 0;
        for (Map<String, String> measObjectResult : originalValueMap) {
            String dn = measObjectResult.get("dn");
            if (!moNameMap.containsKey(dn)) {
                LOGGER.error("getIndicatorOriginalValueNew filter dn={}", dn);
                continue;
            }
            if (count >= maxCount) {
                break;
            }
            if (!ContextUtils.getContext().getAdmin() && !ContextUtils.getContext().getAuthDns().contains(dn)) {
                continue;
            }
            hasDnRight = true;
            String originalValue = measObjectResult.get("originalValue");
            if (StringUtils.isNotEmpty(originalValue)) {
                originalValue = originalValue.replaceAll("<=>", "=").replaceAll("<,>", ",");
            }
            TaskIndicator indicator = orignalMap.get(originalValue);
            if (indicator != null) {
                createIndicator(resultList, moNameMap.get(dn), dn, indicator, deploymentMoType);
                count++;
            } else {
                LOGGER.error("queryIndicatorInstanceByType get indicator by originalValue error,originalValue={}", originalValue);
            }
        }
        return hasDnRight;
    }

    private void createIndicator(List<TaskIndicator> resultList, String moName, String dn, TaskIndicator indicator, String deploymentMoType) {
        TaskIndicator newIndicator = JSONObject.parseObject(JSONObject.toJSONString(indicator), TaskIndicator.class);
        newIndicator.setDn(dn);
        newIndicator.setDnName(moName);
        newIndicator.setIndicatorId(getIndicatorId(newIndicator));
        if (StringUtils.isNotEmpty(deploymentMoType)) {
            newIndicator.setDeploymentMoType(deploymentMoType);
        }
        resultList.add(newIndicator);
    }

    /**
     * 拼接指标id
     *
     * @param indicator indicator
     * @return String
     */
    public static String getIndicatorId(TaskIndicator indicator) {
        String id;
        if (StringUtils.isEmpty(indicator.getDn())) {
            id = indicator.getMoType() + indicator.getMeasUnitKey() + indicator.getMeasTypeKey();
        } else {
            id = indicator.getDn() + indicator.getMeasUnitKey() + indicator.getMeasTypeKey();
        }
        if (StringUtils.isNotEmpty(indicator.getDisplayValue())) {
            id += indicator.getDisplayValue();
        }
        return id.replace(",", "");
    }

    public List<TaskIndicatorResult> getPartiallyResult(Integer taskId, String queryType) throws ServiceException {
        return getPartiallyResult(taskManageDao.getAnalysisTaskById(taskId), queryType);
    }

    public List<TaskIndicatorResult> getPartiallyResult(AnalysisTask task, String queryType) throws ServiceException {
        Integer taskId = task.getTaskId();
        List<TaskIndicator> taskIndicatorList = taskIndicatorDao.getTaskIndicatorListWithAuth(taskId, null);
        if (CollectionUtils.isEmpty(taskIndicatorList)) {
            LOGGER.error("[getPartiallyResult] taskIndicatorList is empty");
            return new ArrayList<>();
        }
        // 推理过滤掉软删除的指标
        if ("predict".equals(queryType)) {
            taskIndicatorList = taskIndicatorList.stream().filter(indicator -> indicator.getSoftDelete() == null || !indicator.getSoftDelete())
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(taskIndicatorList)) {
                LOGGER.error("[getPartiallyResult] taskIndicatorList after filter soft delete is empty");
                return new ArrayList<>();
            }
        }
        // pql的指标要求把pql放到indexname里
        taskIndicatorList.forEach(indicator -> {
            if (StringUtils.isNotEmpty(indicator.getPql())) {
                indicator.setIndexName(indicator.getPql());
            }
        });
        taskManagerSub.refreshMoName(taskIndicatorList);
        List<TaskIndicatorResult> indicatorResults = JSON.parseArray(JSONObject.toJSONString(taskIndicatorList), TaskIndicatorResult.class);
        if ("train".equals(queryType)) {
            return trainPartiallyResult(task, indicatorResults);
        } else if ("predict".equals(queryType)) {
            TaskExecutionResultMapper mapper = mapperFactory.getMapper(TaskExecutionResultMapper.class);
            String record = mapper.getRecentResultRecord(taskId, TaskConstant.EXECUTION_TYPE_PREDICTING);
            Map<String, String> failureMap = getFailureMap(taskId, TaskConstant.EXECUTION_TYPE_PREDICTING);
            List<String> indicatorIds;
            if (StringUtils.isEmpty(record)) {
                LOGGER.error("[getPartiallyResult] getRecentResultRecord is empty");
                indicatorIds = new ArrayList<>();
            } else {
                indicatorIds = JSON.parseArray(record, String.class);
            }
            for (TaskIndicatorResult indicator : indicatorResults) {
                if (indicatorIds.contains(indicator.getIndicatorId()) || indicatorIds.contains(
                    indicator.getUnitedId())) {
                    indicator.setStatus("success");
                } else {
                    indicator.setStatus("fail");
                    taskManagerSub.setPredictFailureResult(failureMap, indicator);
                }
            }
            indicatorSorting(indicatorResults);
            return indicatorResults;
        }
        return new ArrayList<>();
    }

    private Map<String, String> getFailureMap(Integer taskId, String executionType) {
        TaskExecutionResultMapper mapper = mapperFactory.getMapper(TaskExecutionResultMapper.class);
        String failureCauses = mapper.getRecentFailureCause(taskId, executionType);
        if (StringUtils.isEmpty(failureCauses)) {
            return Collections.emptyMap();
        }
        return JSON.parseObject(failureCauses, new TypeReference<Map<String, String>>() {});
    }

    private List<TaskIndicatorResult> trainPartiallyResult(AnalysisTask task, List<TaskIndicatorResult> indicatorResults) throws ServiceException {
        Integer taskId = task.getTaskId();
        String resFileName = taskId + ".txt";
        if (taskManagerSub.determineTrainingTime(indicatorResults, taskId, resFileName)) {
            return indicatorResults;
        }

        HofsUtil.downloadFile(HofsUtil.HOFS_PATH_MODEL + taskId + File.separator + resFileName, TEMP_PATH + resFileName);
        try {
            String resultLine = new String(Files.readAllBytes(Paths.get(TEMP_PATH + resFileName)), StandardCharsets.UTF_8);
            TrainingIndicatorResult result = null;
            if (StringUtils.isNotEmpty(resultLine)) {
                result = JSONObject.parseObject(resultLine, TrainingIndicatorResult.class);
            }
            if (result == null) {
                LOGGER.error("[getPartiallyResult] {} is empty", resFileName);
                result = new TrainingIndicatorResult();
            }
            // 联合指标的训练结果包含sum与avg两种结果，趋势预测只关注avg训练结果，所以要先过滤掉sum指标，并去除_avg后缀
            removeUnitedIdSuffix(task, result);
            TaskExecutionResultMapper mapper = mapperFactory.getMapper(TaskExecutionResultMapper.class);
            Long recentTrainTime = mapper.getRecentExecutionResultTime(taskId, TaskConstant.EXECUTION_TYPE_TRAINING);
            for (TaskIndicatorResult indicator : indicatorResults) {
                if (result.getIndicatorIds().contains(indicator.getIndicatorId()) || result.getIndicatorIds()
                    .contains(indicator.getUnitedId())) {
                    indicator.setStatus("success");
                } else {
                    indicator.setStatus("fail");
                    Map<String, String> failureMap = result.getFailureCause();
                    if (failureMap == null) {
                        failureMap = new HashMap<>();
                    } else {
                        trimMapKey(failureMap);
                    }
                    taskManagerSub.setTrainFailureResult(failureMap, indicator, task.getUpdateTime(), recentTrainTime);
                }
            }
            indicatorSorting(indicatorResults);
            return indicatorResults;
        } catch (IOException e) {
            LOGGER.error("[getPartiallyResult] read {} error,e=", resFileName, e);
            for (TaskIndicatorResult indicator : indicatorResults) {
                indicator.setStatus("fail");
                indicator.setFailureCause(ResourceUtil.getMessage(InternationalConstant.TRAIN_COMMON_FAILURE_REASON, ContextUtils.getDefaultLocale()));
            }
            return indicatorResults;
        } finally {
            if (!new File(TEMP_PATH + resFileName).delete()) {
                LOGGER.error("[getPartiallyResult] delete {} fail", resFileName);
            }
        }
    }

    private void removeUnitedIdSuffix(AnalysisTask task, TrainingIndicatorResult result) {
        if (!Objects.equals(task.getTaskType(), TaskConstant.TASK_TYPE_INDICATOR_PREDICT)
            || !Objects.equals(task.getIndicatorPredictScenario(), TaskConstant.INDICATOR_PREDICT_SCENARIO_UNITED)) {
            return;
        }

        result.setIndicatorIds(ConceptDriftManage.filterUnitedId(result.getIndicatorIds()));

        if (MapUtils.isEmpty(result.getFailureCause())) {
            return;
        }

        Map<String, String> realFailureCause = new HashMap<>();
        Map<String, String> failureCause = result.getFailureCause();
        if (MapUtils.isNotEmpty(failureCause)) {
            for (Map.Entry<String, String> entry : failureCause.entrySet()) {
                String realIndicatorId = ConceptDriftManage.removeSuffix(entry.getKey(), ConceptDriftManage.AVG_SUFFIX);
                realFailureCause.put(realIndicatorId, entry.getValue());
            }
        }
        result.setFailureCause(realFailureCause);
    }

    private void trimMapKey(Map<String, String> failureMap) {
        Map<String, String> cleanedMap = new HashMap<>();
        for (Map.Entry<String, String> entry : failureMap.entrySet()) {
            String cleanedKey = entry.getKey().trim();
            String cleanedValue = entry.getValue().trim();
            cleanedMap.put(cleanedKey, cleanedValue);
        }
        failureMap.clear();
        failureMap.putAll(cleanedMap);
    }

    private void indicatorSorting(List<TaskIndicatorResult> indicatorResults) {
        // 失败的排前面
        Comparator<TaskIndicatorResult> comparator = new TaskIndicatorResultCompare()
            .thenComparing(TaskIndicatorResult::getMoType, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(TaskIndicatorResult::getDnName, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(TaskIndicatorResult::getMeasUnitName, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(TaskIndicatorResult::getIndexName, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(TaskIndicatorResult::getDisplayValue, Comparator.nullsLast(Comparator.naturalOrder()));
        indicatorResults.sort(comparator);
    }

    public List<ResScheAsscEntry> queryKpiForecastMatrix(ResScheAsscParam param) throws ServiceException {
        List<ResScheAsscEntry> result = new ArrayList<>();
        ProductPortrait productPortrait = portraitManageDao.getProductPortraitDetail(param.getPortraitId());
        productPortrait.getHostPortraitList().forEach(hostPortrait -> {
            ResScheAsscEntry resScheAsscEntry = new ResScheAsscEntry();
            resScheAsscEntry.setHostPortraitName(hostPortrait.getName());
            resScheAsscEntry.setHostType(hostPortrait.getHostType());
            resScheAsscEntry.setIndicatorIdentity(new ProductPortraitSharedIndicators());
            resScheAsscEntry.getIndicatorIdentity().setReturnValue(param.getSharedIndicator());
            PortraitIndicator portraitIndicator = hostPortrait.getPortraitIndicatorList()
                .stream()
                .filter(indicator -> indicatorIdentical(indicator, param.getSharedIndicator()))
                .findFirst()
                .orElse(null);
            if (portraitIndicator != null) {
                // 在修改场景，这里的上下限阈值可能需要从数据库捞一把当前的值，作为一个可服务型要求，这个点可以后续优化
                resScheAsscEntry.setLowerThreshold(portraitIndicator.getLowerThreshold());
                resScheAsscEntry.setUpperThreshold(portraitIndicator.getUpperThreshold());
                resScheAsscEntry.getIndicatorIdentity().setDisplayValue(getIndicatorDisplayToken(portraitIndicator));
                try {
                    if (EamUtil.queryClusterInfoList().contains(resScheAsscEntry.getHostType())) {
                        List<AnalysisTaskEntity> taskList = taskManageDao.getTaskListByType(
                            TaskTypeEnum.INDICATORPREDICTTASK.enumToInt());
                        taskList = taskList.stream()
                            .filter(task -> taskIndicatorDao.getFussUnitedIndicatorBelongTaskIdList(
                                    getFussPatten(portraitIndicator.getMeasUnitKey(), portraitIndicator.getMeasTypeKey(),
                                        hostPortrait.getHostType(), param.getSiteId(), portraitIndicator.getDisplayValue()))
                                .contains(task.getTaskId()))
                            .collect(Collectors.toList());
                        resScheAsscEntry.getForecastTaskList().addAll(taskList);
                    }
                    result.add(resScheAsscEntry);
                } catch (ServiceException e) {
                    LOGGER.error("[queryKpiForecastMatrix] getTaskListByType fail", e);
                }
            }
        });
        return result;
    }

    private boolean indicatorIdentical(PortraitIndicator portraitIndicator, String indicatorIdentical) {
        String[] components = indicatorIdentical.split("\\(_\\)");
        return portraitIndicator.getMeasUnitKey().equals(components[0]) && portraitIndicator.getMeasTypeKey()
            .equals(components[1]) && (components.length <= 2 || portraitIndicator.getDisplayValue()
            .equals(components[2]));
    }

    private String getIndicatorDisplayToken(PortraitIndicator portraitIndicator) {
        String[] components = StringUtils.isEmpty(portraitIndicator.getDisplayValue()) ? new String[] {
            portraitIndicator.getMoType(), portraitIndicator.getMeasUnitName(), portraitIndicator.getIndexName()
        } : new String[] {
            portraitIndicator.getMoType(), portraitIndicator.getMeasUnitName(), portraitIndicator.getIndexName(),
            portraitIndicator.getDisplayValue()
        };
        return String.join("~", components);
    }

    private String getFussPatten(String measUnitKey, String measTypeKey, String hostType, String siteId,
        String displayValue) {
        String result = "%" + measUnitKey + measTypeKey + hostType;
        if (StringUtils.isNotEmpty(siteId)) {
            result += siteId;
        }
        if (StringUtils.isNotEmpty(displayValue)) {
            result += displayValue;
        }
        return result.replace(",", "");
    }

    public void triggerAutoSchedule(AutoScheduleParam param) throws ServiceException {
        taskManageDao.triggerAutoSchedule(param.getTaskId(), param.getIsAutoSchedule());
    }

    public List<String> getAssociatedResourceScheduleTask(QueryAssociatedResourceScheduleTask queryParam) {
        return taskManageDao.getAssociatedResourceScheduleTask(queryParam.getTaskId());
    }

    private void handleAggregateMos(String groupJson, List<TaskIndicator> taskIndicatorList) throws ServiceException {
        List<AggregateMachineGroup> groups;
        try {
            groups = JSON.parseArray(groupJson, AggregateMachineGroup.class);
        } catch (JSONException e) {
            LOGGER.error("[AggregateIndicatorPredictManage] parse AggregateMachineGroup json error.", e);
            throw new ServiceException("parse json error");
        }

        Map<String, AggregateMachineGroup> groupMap = groups.stream().collect(Collectors
            .toMap(AggregateMachineGroup::getClusterName, Function.identity()));

        // 找到对应的双机分组，填充网元列表
        for (TaskIndicator taskIndicator : taskIndicatorList) {
            if (!groupMap.containsKey(taskIndicator.getClusterName())) {
                LOGGER.error("[AggregateIndicatorPredictManage] get aggregateMos error, map not contains clusterName.");
                continue;
            }
            AggregateMachineGroup group = groupMap.get(taskIndicator.getClusterName());
            taskIndicator.setAggregateMos(group.getAggregateMoList());
        }
    }

    public List<AnalysisTask> getActiveStandbyAnalysisTask() {
        return taskManageDao.getActiveStandbyAnalysisTask();
    }

    /**
     * 获取任务列表
     *
     * @param taskType 任务类型
     * @return 任务列表
     * @throws ServiceException ex
     */
    public List<AnalysisTask> getAnalysisTaskByType(int taskType) throws ServiceException {
        List<AnalysisTaskEntity> entities =  taskManageDao.getTaskListByType(taskType);
        List<AnalysisTask> analysisTasks = new ArrayList<>();
        for (AnalysisTaskEntity entity : entities) {
            entity.setIndicatorList(taskIndicatorDao.getTaskIndicatorList(entity.getTaskId(), null));
            AnalysisTask task = JSONObject.parseObject(JSONObject.toJSONString(entity), AnalysisTask.class);
            analysisTasks.add(task);
        }
        return analysisTasks;
    }

    public AlarmTemplate getAlarmTemplate(String alarmTaskType) {
        AlarmTemplate alarmTemplate = new AlarmTemplate();
        AlarmTemplateObj eventTemplate = new AlarmTemplateObj();
        AlarmTemplateObj alarmInfoTemplate = new AlarmTemplateObj();
        if (TaskConstant.ALARM_TYPE_COMPRESS.equals(alarmTaskType)) {
            if (SynAnalysisResultConstant.AlarmParameters.LOCALE_EN.equals(ContextUtils.getContext().getLocale().toString())) {
                String eventFieldEn = "[{\"key\":\"incidentId\",\"name\":\"Incident Id\"},{\"key\":\"alarmNum\",\"name\":\"Alarm Num\"},{\"key\":\"level\",\"name\":\"Level\"},{\"key\":\"description\",\"name\":\"Incident Name\"},{\"key\":\"alarmSummary\",\"name\":\"Alarm Summary\"}]";
                String eventTemplateEn = "Alarm Num:{$alarmNum}|Incident Level:{$level}|Incident Name:{$description}|Alarm Summary:{$alarmSummary}";
                eventTemplate.setFields(JSONObject.parseObject(eventFieldEn, TEMPLATE_FIELD));
                eventTemplate.setTemplate(eventTemplateEn);
                String alarmInfoFieldEn = "[{\"key\":\"alarmId\",\"name\":\"Alarm Id\"},{\"key\":\"severity\",\"name\":\"Level\"},{\"key\":\"alarmName\",\"name\":\"Name\"},{\"key\":\"meName\",\"name\":\"Alarm Source\"},{\"key\":\"moi\",\"name\":\"Location information\"},{\"key\":\"originSystemName\",\"name\":\"Source System\"},{\"key\":\"additionalInformation\",\"name\":\"Additional Information\"},{\"key\":\"probableCause\",\"name\":\"Possible Causes\"}]";
                String alarmInfoTemplateEn = "Alarm Id:{$alarmId}|Alarm Level:{$severity}|Alarm Name:{$alarmName}|Alarm Source:{$meName}|Source System:{$originSystemName}|Possible Causes:{$probableCause}|Location information:{$moi}|Additional Information:{$additionalInformation}";
                alarmInfoTemplate.setFields(JSONObject.parseObject(alarmInfoFieldEn, TEMPLATE_FIELD));
                alarmInfoTemplate.setTemplate(alarmInfoTemplateEn);
            } else {
                String eventFieldCn = "[{\"key\":\"incidentId\",\"name\":\"事件ID\"},{\"key\":\"alarmNum\",\"name\":\"告警数量\"},{\"key\":\"level\",\"name\":\"级别\"},{\"key\":\"description\",\"name\":\"事件名称\"},{\"key\":\"alarmSummary\",\"name\":\"告警摘要\"}]";
                String eventTemplateCn = "告警数量：{$alarmNum}|事件级别：{$level}|事件名称：{$description}|告警摘要：{$alarmSummary}";
                eventTemplate.setFields(JSONObject.parseObject(eventFieldCn, TEMPLATE_FIELD));
                eventTemplate.setTemplate(eventTemplateCn);
                String alarmInfoFieldCn = "[{\"key\":\"alarmId\",\"name\":\"告警ID\"},{\"key\":\"severity\",\"name\":\"级别\"},{\"key\":\"alarmName\",\"name\":\"名称\"},{\"key\":\"meName\",\"name\":\"告警源\"},{\"key\":\"moi\",\"name\":\"定位信息\"},{\"key\":\"originSystemName\",\"name\":\"来源系统\"},{\"key\":\"additionalInformation\",\"name\":\"附加信息\"},{\"key\":\"probableCause\",\"name\":\"可能原因\"}]";
                String alarmInfoTemplateCn = "告警ID：{$alarmId}|告警级别：{$severity}|告警名称：{$alarmName}|告警源：{$meName}|来源系统：{$originSystemName}|可能原因：{$probableCause}|定位信息：{$moi}|附加信息：{$additionalInformation}";
                alarmInfoTemplate.setFields(JSONObject.parseObject(alarmInfoFieldCn, TEMPLATE_FIELD));
                alarmInfoTemplate.setTemplate(alarmInfoTemplateCn);
            }
        } else {
            if (SynAnalysisResultConstant.AlarmParameters.LOCALE_EN.equals(ContextUtils.getContext().getLocale().toString())) {
                String eventFieldEn = "[{\"key\":\"incidentId\",\"name\":\"Incident Id\"},{\"key\":\"alarmNum\",\"name\":\"Alarm Num\"},{\"key\":\"level\",\"name\":\"Level\"},{\"key\":\"description\",\"name\":\"Possible Causes\"},{\"key\":\"comprehensiveScore\",\"name\":\"AI Score\"},{\"key\":\"alarmInfo\",\"name\":\"Top Alarm Information\"}]";
                String eventTemplateEn = "Alarm Num:{$alarmNum}|Incident Level:{$level}|Possible Causes:{$description}|Top Alarm Information:{$alarmInfo}";
                eventTemplate.setFields(JSONObject.parseObject(eventFieldEn, TEMPLATE_FIELD));
                eventTemplate.setTemplate(eventTemplateEn);
                String alarmInfoFieldEn = "[{\"key\":\"alarmId\",\"name\":\"Alarm Id\"},{\"key\":\"severity\",\"name\":\"Level\"},{\"key\":\"alarmName\",\"name\":\"Name\"},{\"key\":\"meName\",\"name\":\"Alarm Source\"},{\"key\":\"moi\",\"name\":\"Location information\"},{\"key\":\"originSystemName\",\"name\":\"Source System\"},{\"key\":\"additionalInformation\",\"name\":\"Additional Information\"},{\"key\":\"probableCause\",\"name\":\"Possible Causes\"}]";
                String alarmInfoTemplateEn = "Alarm Id:{$alarmId}|Alarm Level:{$severity}|Alarm Name:{$alarmName}|Alarm Source:{$meName}|Source System:{$originSystemName}|Location information:{$moi}|Additional Information:{$additionalInformation}|Possible Causes:{$probableCause}";
                alarmInfoTemplate.setFields(JSONObject.parseObject(alarmInfoFieldEn, TEMPLATE_FIELD));
                alarmInfoTemplate.setTemplate(alarmInfoTemplateEn);
            } else {
                String eventFieldCn = "[{\"key\":\"incidentId\",\"name\":\"事件ID\"},{\"key\":\"alarmNum\",\"name\":\"告警数量\"},{\"key\":\"level\",\"name\":\"级别\"},{\"key\":\"description\",\"name\":\"可能原因\"},{\"key\":\"comprehensiveScore\",\"name\":\"AI得分\"},{\"key\":\"alarmInfo\",\"name\":\"TOP告警信息\"}]";
                String eventTemplateCn = "告警数量：{$alarmNum}|事件级别：{$level}|可能原因：{$description}|TOP告警信息：{$alarmInfo}";
                eventTemplate.setFields(JSONObject.parseObject(eventFieldCn, TEMPLATE_FIELD));
                eventTemplate.setTemplate(eventTemplateCn);
                String alarmInfoFieldCn = "[{\"key\":\"alarmId\",\"name\":\"告警ID\"},{\"key\":\"severity\",\"name\":\"级别\"},{\"key\":\"alarmName\",\"name\":\"名称\"},{\"key\":\"meName\",\"name\":\"告警源\"},{\"key\":\"moi\",\"name\":\"定位信息\"},{\"key\":\"originSystemName\",\"name\":\"来源系统\"},{\"key\":\"additionalInformation\",\"name\":\"附加信息\"},{\"key\":\"probableCause\",\"name\":\"可能原因\"}]";
                String alarmInfoTemplateCn = "告警ID：{$alarmId}|告警级别：{$severity}|告警名称：{$alarmName}|告警源：{$meName}|来源系统：{$originSystemName}|定位信息：{$moi}|附加信息：{$additionalInformation}|可能原因：{$probableCause}";
                alarmInfoTemplate.setFields(JSONObject.parseObject(alarmInfoFieldCn, TEMPLATE_FIELD));
                alarmInfoTemplate.setTemplate(alarmInfoTemplateCn);
            }
        }
        alarmTemplate.setEventTemplate(eventTemplate);
        alarmTemplate.setAlarmInfoTemplate(alarmInfoTemplate);
        return alarmTemplate;
    }

    public AnalysisTaskList getIndicatorTaskByDisasterRecovery(String taskName, Integer pageSize, Integer pageIndex) {
        AnalysisTaskList result = new AnalysisTaskList();
        Set<Integer> authTaskIds = ContextUtils.getContext().getQueryAuthTaskIds();
        if (!ContextUtils.getContext().getAdmin() && CollectionUtils.isEmpty(authTaskIds)) {
            result.setRows(new ArrayList<>());
            result.setTotal(0);
            return result;
        }
        Integer offset = TaskAnalysisResultUtil.calOffset(pageIndex, pageSize);
        result.setRows(taskManageDao.getIndicatorTaskByDisasterRecovery(taskName, pageSize, offset, authTaskIds));
        result.setTotal(taskManageDao.getIndicatorTaskCountByDisasterRecovery(taskName, authTaskIds));
        return result;
    }

    public void startAlarmCompressTask(AnalysisTask analysisTask, String userId) throws ServiceException {
        COMPRESS_EXECUTOR.execute(() -> {
            try {
                long startTime = System.currentTimeMillis();
                // 查询数据库未结束的告警更新状态
                historicalDataManager.alarmCompressUpdate(analysisTask);
                // 查询当前所有未结束告警
                long startQueryTime = System.currentTimeMillis();
                List<Event> notEndAlarm = getNotEndAlarm(analysisTask, startQueryTime, 0);
                LOGGER.info("startAlarmCompressTask notEndAlarm size={}", notEndAlarm.size());
                // 进行告警压缩
                Map<String, List<AlarmIncident>> sendMessageIncident = alarmEventListener.executeAlarmCompression(notEndAlarm, analysisTask);
                alarmEventListener.sendNotification(sendMessageIncident, analysisTask);

                long endQueryTime = System.currentTimeMillis();
                List<Event> secondQueryNotEndAlarm = getNotEndAlarm(analysisTask, startQueryTime, endQueryTime);
                LOGGER.info("startAlarmCompressTask second notEndAlarm size={}", secondQueryNotEndAlarm.size());
                // 进行告警压缩
                Map<String, List<AlarmIncident>> secondSendMessageIncident = alarmEventListener.executeAlarmCompression(secondQueryNotEndAlarm, analysisTask);
                alarmEventListener.sendNotification(secondSendMessageIncident, analysisTask);

                alarmEventListener.start(analysisTask);
                LOGGER.info("startAlarmCompressTask costTime={}", System.currentTimeMillis() - startTime);

                taskManagerSub.markSentAlarmIncient(analysisTask);
            } catch (Throwable e) {
                LOGGER.error("alarmCompression execute error:", e);
            }
        });
        taskManageDao.startTaskById(analysisTask.getTaskId(), userId);
    }



    private List<Event> getNotEndAlarm(AnalysisTask task, long firstQueryTime, long endQueryTime) {
        AlarmQueryParam param = new AlarmQueryParam();
        QueryContext condition = new QueryContext();
        condition.setExpression("and");
        List<QFilterElement> filterList = new ArrayList<>();
        condition.setFilters(filterList);
        param.setQuery(condition);

        QFilterElement filter = new QFilterElement();
        List<String> alarmList = JSONArray.parseArray(task.getAlarmTypeList(), String.class);
        if (task.getAlarmSelectType().equals(AlarmSelectType.SELECTBYALARMSOURCETYPE)) {
            if (CollectionUtils.isNotEmpty(alarmList)) {
                // 按告警源类型
                filter.setName("deviceTypeId");
                filter.setField("deviceTypeId");
                filter.setOperator("in");
                filter.setValues(alarmList);
                filterList.add(filter);
            }
        } else if(task.getAlarmSelectType().equals(AlarmSelectType.SELECTBYALARMID)) {
            if (CollectionUtils.isNotEmpty(alarmList)) {
                // 按告警id
                filter.setName("alarmId");
                filter.setField("alarmId");
                filter.setOperator("in");
                filter.setValues(alarmList);
                filterList.add(filter);
            }
        } else {
            if (CollectionUtils.isNotEmpty(alarmList)) {
                // 按告警类型
                filter.setName("eventType");
                filter.setField("eventType");
                filter.setOperator("in");
                filter.setValues(alarmList);
                filterList.add(filter);
            }
        }
        QFilterElement filterStatus = new QFilterElement();
        filterStatus.setName("cleared");
        filterStatus.setField("cleared");
        filterStatus.setOperator("=");
        filterStatus.setValues(Collections.singletonList("0"));
        filterList.add(filterStatus);

        addFilterTime(firstQueryTime, endQueryTime, filterList);
        return queryAlarm(param, task.getTaskId());
    }

    private void addFilterTime(long firstQueryTime, long endQueryTime, List<QFilterElement> filterList) {
        if (firstQueryTime != 0 && endQueryTime != 0) {
            QFilterElement filterTime = new QFilterElement();
            filterTime.setName("occurUtc");
            filterTime.setField("occurUtc");
            filterTime.setOperator("between");
            filterTime.setValues(Arrays.asList(String.valueOf(firstQueryTime), String.valueOf(endQueryTime)));
            filterList.add(filterTime);
        } else {
            QFilterElement filterTime = new QFilterElement();
            filterTime.setName("occurUtc");
            filterTime.setField("occurUtc");
            filterTime.setOperator("<");
            filterTime.setValues(Collections.singletonList(String.valueOf(firstQueryTime)));
            filterList.add(filterTime);
        }
    }

    private List<Event> queryAlarm(AlarmQueryParam param, int taskId) {
        List<Event> result = new ArrayList<>();
        param.setSize(1000);
        param.setFields(ALARM_RETURN_FIELDS);
        param.setIterator(null);
        ScrollQueryResult queryResult = AlarmClient.getInstance().getAlarmInfos(param, "current");
        if (queryResult == null) {
            LOGGER.error("start task queryAlarm queryResult is null");
            return result;
        }
        while (StringUtils.isNotEmpty(queryResult.getIterator()) || CollectionUtils.isNotEmpty(queryResult.getHits())) {
            List<AlarmListenerInfo> alarmData = conertAlarmToAlarmListenerInfo(queryResult.getHits());
            if (CollectionUtils.isNotEmpty(alarmData)) {
                List<Integer> csns = alarmData.stream().map(AlarmListenerInfo::getCsn).collect(Collectors.toList());
                // 去除数据库中已存在的告警
                List<Integer> csnInTable = alarmCompressDao.queryAlarmInTable(taskId, csns);
                result.addAll(alarmEventListener.getEventFromAlarm(alarmData.stream().filter(event -> !csnInTable.contains(event.getCsn())).collect(
                    Collectors.toList())));
            }
            param.setIterator(queryResult.getIterator());
            LOGGER.debug("[queryAlarm] iterator={}, size={}", queryResult.getIterator(),
                queryResult.getHits().size());
            queryResult = AlarmClient.getInstance().getAlarmInfos(param, "current");
            if (queryResult == null) {
                LOGGER.error("[queryAlarm] getAlarmData queryResult is null");
                break;
            }
        }
        return result;
    }

    private List<AlarmListenerInfo> conertAlarmToAlarmListenerInfo(List<Map<String, String>> alarmData) {
        Set<String> dns = ContextUtils.getContext().getAuthDns();
        List<AlarmListenerInfo> result = new ArrayList<>();
        for (Map<String, String> info : alarmData) {
            if (CollectionUtils.isNotEmpty(dns) && !dns.contains(info.get("nativeMoDn"))) {
                continue;
            }
            if (SynAnalysisResultConstant.AlarmParameters.ALARM_REPORT_ID.equals(info.get("alarmId"))) {
                // 告警通知上报的告警不分析
                continue;
            }
            if (StringUtils.isNotEmpty(info.get("moduleName")) && "AlarmCompress".equals(info.get("moduleName"))) {
                // 告警通知上报的告警不分析
                continue;
            }
            taskManagerSub.addAlarmListenerInfo(result, info);
        }
        return result;
    }

    public void updateIndicators() {
        // 查询所有配置了自动更新指标的指标异常检测任务
        List<AnalysisTask> tasks = taskManageDao.getAllUpdateConfigTask();
        if (CollectionUtils.isEmpty(tasks)) {
            return;
        }
        HashMap<Integer, Long> timeMap = new HashMap<>();
        for (AnalysisTask task : tasks) {
            if (String.valueOf(DataSourceType.STARROCK).equals(task.getDatasourceId())) {
                taskManagerSub.handleStarRockUpdateIndicators(task);
                // 自动更新指标之后，更新容量
                configDataHandler.updateUsedCapacityForFront(task, TaskConstant.MODIFY_ACTION);
                continue;
            }
            long startTime = System.currentTimeMillis();
            try {
                if (task.getImportTaskStatus() != null && task.getImportTaskStatus()) {
                    if (task.getUpdateIndicatorAuto() == null || task.getUpdateIndicatorAuto()) {
                        List<TaskIndicator> indicators = taskIndicatorDao.getTaskIndicatorList(task.getTaskId(), null);
                        task.setIndicatorList(indicators);
                        List<TaskIndicator> updateIndicators = taskIndicatorDao.getTaskUpdateIndicatorList(task.getTaskId(), null);
                        HandleDtoFactory.getInstance().importTaskOriginalMatched(task, updateIndicators);
                    }
                    // 需要关闭软删除网元对应指标的异常同时关闭对应告警
                    closeSoftDeletedOutlier(task);
                    LOGGER.info("updateIndicators taskId={}, cost time={}", task.getTaskId(), System.currentTimeMillis() - startTime);
                    // 自动更新指标之后，更新容量
                    configDataHandler.updateUsedCapacityForFront(task, TaskConstant.MODIFY_ACTION);
                    continue;
                }
                if (task.getUpdateIndicatorAuto() != null && task.getUpdateIndicatorAuto()) {
                    List<TaskIndicator> indicators = taskIndicatorDao.getTaskIndicatorList(task.getTaskId(), null);
                    List<TaskIndicator> updateIndicators = taskIndicatorDao.getTaskUpdateIndicatorList(task.getTaskId(), null);
                    List<TaskIndicator> indicatorInstance = instantiationIndicators(indicators, updateIndicators, task.getUserId(), task.getSolutionId());
                    taskManagerSub.updateIndicators(task, indicatorInstance);
                    // 需要关闭软删除网元对应指标的异常同时关闭对应告警
                    closeSoftDeletedOutlier(task);
                    // 自动更新指标之后，更新容量
                    configDataHandler.updateUsedCapacityForFront(task, TaskConstant.MODIFY_ACTION);
                }
            } catch (Exception e) {
                LOGGER.error("updateIndicators error,e:", e);
            }
            timeMap.put(task.getTaskId(), System.currentTimeMillis() - startTime);
        }
        LOGGER.info("updateIndicators , cost time={}", timeMap);
    }

    private void closeSoftDeletedOutlier(AnalysisTask task) {
        if (CollectionUtils.isEmpty(task.getIndicatorList())) {
            return;
        }
        List<String> softDeletedIds = task.getIndicatorList()
            .stream().filter(indicator -> indicator.getSoftDelete() != null && indicator.getSoftDelete())
            .map(TaskIndicator::getIndicatorId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(softDeletedIds)) {
            return;
        }
        try {
            List<IndicatorOutlier> outliers = indicatorOutlierDao.getUnFinishedOutlierByIndicatorId(task.getTaskId(), softDeletedIds);
            if (CollectionUtils.isEmpty(outliers)) {
                return;
            }
            long endTime = System.currentTimeMillis();
            for (IndicatorOutlier outlier : outliers) {
                outlier.setEndTime(endTime);
                indicatorOutlierDao.updateIndicatorOutlierEndTime(outlier.getId(), endTime);
            }
            LOGGER.info("closeSoftDeletedOutlier update indicatorOutlier endTime,outliers= {},endTime={}", outliers);
            if (CollectionUtils.isNotEmpty(outliers)) {
                taskIndicatorDao.updateIndicatorStatus(outliers, 0);
                sendClearAlarm(task, outliers);
            }
            // 停止缺点异常告警
            List<IndicatorMissDataOutlier> missDataOutliers = indicatorOutlierDao.getUnFinishedMissDataOutlierByIndicatorId(task.getTaskId(), softDeletedIds);
            if (CollectionUtils.isEmpty(missDataOutliers)) {
                return;
            }
            LOGGER.info("closeSoftDeletedOutlier missDataOutliers indicatorOutlier, missData={}", missDataOutliers);
            List<AlarmRecord> alarmRecords = missDataOutliers.stream().filter(missDataOutlier -> Objects.nonNull(missDataOutlier.getAlarmRecord()))
                .map(missDataOutlier -> {
                    AlarmRecord alarmRecord = JSONObject.parseObject(missDataOutlier.getAlarmRecord(), AlarmRecord.class);
                    alarmRecord.setCategory(SynAnalysisResultConstant.AlarmParameters.CATEGORY_CLEAR);
                    alarmRecord.setCleared(SynAnalysisResultConstant.AlarmParameters.ALARM_CLEARED);
                    alarmRecord.setOccurUtc(0);
                    alarmRecord.setClearUtc(endTime);
                    return alarmRecord;
                }).collect(Collectors.toList());
            indicatorDetectResultHandler.recordSendAlarm(SynAnalysisResultConstant.AlarmParameters.CATEGORY_CLEAR, task.getTaskId(), alarmRecords, JSONArray.parseArray(JSONObject.toJSONString(missDataOutliers), IndicatorOutlier.class));
            indicatorOutlierDao.stopMissDataOutliersByIndicatorId(task.getTaskId(), softDeletedIds, endTime);
        } catch (ServiceException e) {
            LOGGER.error("closeSoftDeletedOutlier error,e=", e);
        }
    }

    public List<TaskIndicator> instantiationIndicators(List<TaskIndicator> indicatorList, List<TaskIndicator> updateIndicators, String userId, String solutionId) {
        List<TaskIndicator> indicators = new ArrayList<>();
        Map<String, List<ManagedObject>> filterInstanceMap = batchFilterSolution(updateIndicators, solutionId);
        Map<String, List<TaskIndicator>> groupIndicator = updateIndicators.stream().collect(Collectors.groupingBy(TaskIndicator::getMoType));
        List<String> authDns = AuthUtils.getHasRightDn(userId);
        for (Map.Entry<String, List<TaskIndicator>> entry : groupIndicator.entrySet()) {
            List<ManageObject>  moList = getManageObjects(solutionId, filterInstanceMap, entry);
            if (CollectionUtils.isEmpty(moList)) {
                LOGGER.error("[updateIndicators] mo list is empty, motype={}", entry.getKey());
                continue;
            }
            Map<String, ManageObject> moMap = new HashMap<>();
            if (authDns != null) {
                for (ManageObject mo : moList) {
                    if (authDns.contains(mo.getDn())) {
                        moMap.put(mo.getDn(), mo);
                    }
                }
            } else {
                moMap = moList.stream().collect(Collectors.toMap(ManageObject::getDn, Function.identity()));
            }
            addNewIndicator(entry, indicators, moMap);
        }
        saveOldIndicator(indicatorList, indicators);
        List<TaskIndicator> result = new ArrayList<>();
        Iterator<TaskIndicator> iterator = indicators.iterator();
        while (iterator.hasNext()) {
            TaskIndicator indicator = iterator.next();
            if (StringUtils.isEmpty(indicator.getOriginalValue())) {
                result.add(indicator);
                iterator.remove();
            }
        }
        if (CollectionUtils.isEmpty(indicators)) {
            return result;
        }
        Map<String, List<TaskIndicator>> groups = indicators.stream()
            .collect(Collectors.groupingBy(indicator -> indicator.getMoType() + "_" + indicator.getMeasUnitKey()));
        for (List<TaskIndicator> group : groups.values()) {
            result.addAll(queryMeasObject(group, group.get(0).getMeasUnitKey()));
        }
        return result;
    }

    public List<ManageObject> getManageObjects(String solutionId, Map<String, List<ManagedObject>> filterInstanceMap,
        Map.Entry<String, List<TaskIndicator>> entry) {
        List<ManageObject> moList = new ArrayList<>();
        List<String> deploymentMoTypes = entry.getValue()
            .stream().map(TaskIndicator::getDeploymentMoType).filter(StringUtils::isNotEmpty)
            .distinct().collect(Collectors.toList());
        if (StringUtils.isNotEmpty(solutionId) && CollectionUtils.isNotEmpty(deploymentMoTypes)) {
            for (String deploymentMoType : deploymentMoTypes) {
                List<ManagedObject> objects = filterInstanceMap.get(deploymentMoType);
                moList.addAll(PerformanceClient.convertToManageObjectList(PerformanceClient.getInstance().queryDeploymentInstance(objects, entry.getKey())));
            }
        } else if (StringUtils.isNotEmpty(solutionId) && CollectionUtils.isEmpty(deploymentMoTypes)) {
            moList = PerformanceClient.convertToManageObjectList(filterInstanceMap.get(entry.getKey()));
        } else if (StringUtils.isEmpty(solutionId) && CollectionUtils.isNotEmpty(deploymentMoTypes)) {
            for (String deploymentMoType : deploymentMoTypes) {
                List<ManageObject> objects = PerformanceClient.getInstance().getMoByTypeSoftDeleted(deploymentMoType);
                moList.addAll(PerformanceClient.convertToManageObjectList(PerformanceClient.getInstance().queryDeploymentInstanceOtherParam(objects, entry.getKey())));
            }
        } else if (StringUtils.isEmpty(solutionId) && CollectionUtils.isEmpty(deploymentMoTypes)) {
            moList = PerformanceClient.getInstance().getMoByTypeSoftDeleted(entry.getKey());
        }
        return moList;
    }

    private Map<String, List<ManagedObject>> batchFilterSolution(List<TaskIndicator> updateIndicators, String solutionId) {
        Map<String, List<ManagedObject>> filterInstanceMap = new HashMap<>();
        if (StringUtils.isNotEmpty(solutionId)) {
            List<String> moTypes = updateIndicators.stream().map(indicator -> {
                if (StringUtils.isNotEmpty(indicator.getDeploymentMoType())) {
                    return indicator.getDeploymentMoType();
                } else {
                    return indicator.getMoType();
                }
            }).distinct().collect(Collectors.toList());
            filterInstanceMap = PerformanceClient.getInstance().filterBySolutionByMoTypeList(solutionId, moTypes);
        }
        return filterInstanceMap;
    }

    private void saveOldIndicator(List<TaskIndicator> oldIndicators, List<TaskIndicator> newIndicators) {
        // 修改任务时旧的指标的异常状态要保留
        if (CollectionUtils.isEmpty(oldIndicators) || CollectionUtils.isEmpty(newIndicators)) {
            LOGGER.error("update task handlePerformanceIndicators error, oldIndicators or insertList is empty");
            return;
        }

        Map<String, Integer> oldIndicatorAbnormalMap = new HashMap<>();
        for (TaskIndicator indicator : oldIndicators) {
            oldIndicatorAbnormalMap.put(indicator.getIndicatorId(), indicator.getAbnormal());
        }
        List<String> oldIds = oldIndicators.stream().map(TaskIndicator::getIndicatorId).collect(Collectors.toList());
        newIndicators.forEach(indicator -> {
            if (oldIds.contains(indicator.getIndicatorId())) {
                indicator.setAbnormal(oldIndicatorAbnormalMap.get(indicator.getIndicatorId()));
            }
        });
    }

    private void addNewIndicator(Map.Entry<String, List<TaskIndicator>> entry, List<TaskIndicator> indicators,
        Map<String, ManageObject> moMap) {
        if (MapUtils.isNotEmpty(moMap)) {
            for (TaskIndicator indicator : entry.getValue()) {
                for (Map.Entry<String, ManageObject> moEntry : moMap.entrySet()) {
                    ManageObject mo = moEntry.getValue();
                    TaskIndicator newIndicator = JSONObject.parseObject(JSONObject.toJSONString(indicator), TaskIndicator.class);
                    newIndicator.setDn(mo.getDn());
                    newIndicator.setSubSite("");
                    newIndicator.setIndicatorId(getIndicatorId(newIndicator));
                    newIndicator.setSoftDelete(mo.isDeleted());
                    newIndicator.setSoftDeleteTimeStamp(mo.getDeleteTime());
                    if (newIndicator.getSoftDelete() != null && newIndicator.getSoftDelete()) {
                        newIndicator.setDnName(ResourceUtil.getMessage(InternationalConstant.SOFT_DELETE_FLAG) + mo.getDisplayName());
                    } else {
                        newIndicator.setDnName(mo.getDisplayName());
                    }
                    indicators.add(newIndicator);
                }
            }
        }
    }

    private List<TaskIndicator> queryMeasObject(List<TaskIndicator> group, String measUnitKey) {
        List<String> dnList = group.stream().map(TaskIndicator::getDn).distinct().collect(Collectors.toList());
        List<List<String>> subLists = ListUtils.partition(dnList, 10);
        List<MeasObjectEntry> measObjectEntryList = new ArrayList<>();
        List<TaskIndicator> result = new ArrayList<>();
        for (List<String> dns : subLists) {
            measObjectEntryList.addAll(TriggerTaskUtil.getMeasObjects(measUnitKey, dns));
        }
        if (CollectionUtils.isEmpty(measObjectEntryList)) {
            return result;
        }
        Map<String, List<String>> dnMeasObjectMap = measObjectEntryList.stream()
            .collect(Collectors.toMap(MeasObjectEntry::getDn, measObjectEntry -> measObjectEntry.getMeasObjects()
                .stream()
                .map(MeasObject4UI::getDisplayValue)
                .collect(Collectors.toList())));
        for (TaskIndicator indicator : group) {
            List<String> displayValues = dnMeasObjectMap.get(indicator.getDn());
            if (CollectionUtils.isNotEmpty(displayValues) && displayValues.contains(indicator.getDisplayValue())) {
                result.add(indicator);
            }
        }
        return result;
    }
}
