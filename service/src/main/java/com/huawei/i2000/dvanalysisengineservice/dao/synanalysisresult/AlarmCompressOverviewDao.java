/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.i2000.dvanalysisengineservice.dao.common.MapperFactory;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.mapper.AlarmCompressOverviewMapper;
import com.huawei.i2000.dvanalysisengineservice.model.AlarmAndIncidentNameModel;
import com.huawei.i2000.dvanalysisengineservice.model.AlarmAndIncidentNumberModel;
import com.huawei.i2000.dvanalysisengineservice.model.AlarmInfoCommonModel;
import com.huawei.i2000.dvanalysisengineservice.model.AlarmLevelCommonModel;
import com.huawei.i2000.dvanalysisengineservice.model.IncidentAlarmType;
import com.huawei.i2000.dvanalysisengineservice.model.TopNEventInfo;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * description：告警压缩概览页面dao
 *
 * <AUTHOR>
 * @since 2021/09/17
 */
@Component
public class AlarmCompressOverviewDao {
    private static final OssLog LOGGER = OssLogFactory.getLogger(AlarmCompressOverviewDao.class);

    @Autowired
    MapperFactory mapperFactory;

    /**
     * 查询未结束的告警和事件数量
     *
     * @param alarmResultTableList 表名
     * @return 告警/事件数量
     */
    public AlarmAndIncidentNumberModel getCurrentAlarmAndIncident(List<Map<String, Object>> alarmResultTableList) {
        LOGGER.info("entry AlarmCompressOverviewDao.getCurrentAlarmAndIncident()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        AlarmAndIncidentNumberModel result = new AlarmAndIncidentNumberModel();
        try {
            result = mapper.getCurrentAlarmAndIncident(alarmResultTableList);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getCurrentAlarmAndIncident() failed! error is {}", e);
        }
        return result;
    }

    /**
     * 查询TopN得分的事件
     *
     * @param topN n
     * @param alarmTaskIds 告警分析任务ids
     * @return 事件对象list
     */
    public List<TopNEventInfo> getTopNScoreEvent(int topN, List<Map<String, Object>> alarmTaskIds) {
        LOGGER.info("entry AlarmCompressOverviewDao.getTopNScoreEvent()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<TopNEventInfo> eventList = new ArrayList<>();
        try {
            eventList = mapper.getTopNScoreEvent(topN, alarmTaskIds);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmCompressOverviewDao.getTopNScoreEvent() failed. error is {}", e);
        }
        return eventList;
    }

    /**
     * 查询时间范围内所有的告警
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param alarmTableList 告警表名列表
     * @return 结果list
     */
    public List<AlarmInfoCommonModel> getAlarmResultTrend(long startTime, long endTime, List<String> alarmTableList) {
        LOGGER.info("entry AlarmCompressOverviewDao.getAlarmResultTrend(), startTime is {}", startTime);
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<AlarmInfoCommonModel> result = new ArrayList<>();
        try {
            result = mapper.getAlarmResultTrend(startTime, endTime, alarmTableList);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getAlarmResultTrend failed! error is {}", e);
        }
        return result;
    }

    /**
     * 查询所有告警任务id
     *
     * @param alarmTaskType 告警分析任务类型
     * @param alarmType 告警压缩任务
     * @return list
     */
    public List<Integer> getAlarmTaskIdList(int alarmTaskType, String alarmType) {
        LOGGER.info("entry AlarmCompressOverviewDao.getAlarmTaskIdList()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<Integer> alarmTaskIdList = new ArrayList<>();
        try {
            alarmTaskIdList = mapper.getAlarmTaskIdList(alarmTaskType, alarmType);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getAlarmTaskIdList failed! error is {}", e);
        }
        return alarmTaskIdList;
    }

    /**
     * 查询所有告警任务id
     *
     * @param alarmTaskType 告警分析任务类型
     * @param userId 用户ID
     * @param alarmType 告警压缩任务
     * @return list
     */
    public List<Integer> getAlarmTaskIdListByUser(int alarmTaskType, String userId, String alarmType) {
        LOGGER.info("entry AlarmCompressOverviewDao.getAlarmTaskIdListByUser()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<Integer> alarmTaskIdList = new ArrayList<>();
        try {
            alarmTaskIdList = mapper.getAlarmTaskIdListByUser(alarmTaskType, userId, alarmType);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getAlarmTaskIdListByUser failed! error is {}", e);
        }
        return alarmTaskIdList;
    }

    /**
     * 查询某时间之后的告警信息
     *
     * @param startTime 开始时间
     * @param alarmResultTableList 所有的告警表名
     * @return 结果对象list
     */
    public List<AlarmInfoCommonModel> getAlarmInfoListAfterStartTime(long startTime,
        List<String> alarmResultTableList) {
        LOGGER.info("entry AlarmCompressOverviewDao.getAlarmInfoListAfterStartTime()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<AlarmInfoCommonModel> resultList = new ArrayList<>();
        try {
            resultList = mapper.getAlarmInfoListAfterStartTime(startTime, alarmResultTableList);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getAlarmInfoListAfterStartTime() failed! error is {}", e);
        }
        return resultList;
    }

    /**
     * 查询一段时间内的告警级别数量分布
     *
     * @param startTime 开始时间
     * @param alarmResultTableList 所有的告警表名
     * @return 结果对象list
     */
    public List<AlarmLevelCommonModel> getAlarmLevelList(long startTime, List<String> alarmResultTableList) {
        LOGGER.info("entry AlarmCompressOverviewDao.getAlarmLevelList()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<AlarmLevelCommonModel> resultList = new ArrayList<>();
        try {
            resultList = mapper.getAlarmLevelList(startTime, alarmResultTableList);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getAlarmLevelList() failed! error is {}", e);
        }
        return resultList;
    }

    /**
     * 查询当前告警
     *
     * @param alarmResultTableList 表名
     * @param limit 查询条数
     * @return 当前告警list
     */
    public List<AlarmAndIncidentNameModel> getCurrentAlarm(List<Map<String, Object>> alarmResultTableList, int limit) {
        LOGGER.info("entry AlarmCompressOverviewDao.getCurrentAlarm()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<AlarmAndIncidentNameModel> resultList = new ArrayList<>();
        try {
            resultList = mapper.getCurrentAlarmName(alarmResultTableList, limit);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getCurrentAlarm() failed! error is {}", e);
        }
        return resultList;
    }

    /**
     * 查询当前事件
     *
     * @param tableList 表名
     * @param limit 查询条数
     * @return 当前告警list
     */
    public List<AlarmAndIncidentNameModel> getCurrentIncident(List<Map<String, Object>> tableList, int limit) {
        LOGGER.info("entry AlarmCompressOverviewDao.getCurrentIncident()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<AlarmAndIncidentNameModel> resultList = new ArrayList<>();
        try {
            resultList = mapper.getCurrentIncidentName(tableList, limit);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getCurrentIncident() failed! error is {}", e);
        }
        return resultList;
    }

    /**
     * 查询未确认的告警数量
     *
     * @param tableList 表名
     * @return 数量
     */
    public int getUnacknowledgedAlarmCount(List<Map<String, Object>> tableList) {
        LOGGER.info("entry AlarmCompressOverviewDao.getUnacknowledgedAlarmCount()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        int result = 0;
        try {
            List<Integer> count = mapper.getUnacknowledgedAlarmCount(tableList);
            result = count.get(0);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getUnacknowledgedAlarmCount() failed! error is {}", e);
        }
        return result;
    }

    /**
     * 查询某时间之后的告警信息
     *
     * @param startTime 开始时间
     * @param alarmResultTableList 所有的告警表名
     * @return 结果对象list
     */
    public List<AlarmInfoCommonModel> getAlarmByStartTime(long startTime, List<String> alarmResultTableList) {
        LOGGER.info("entry AlarmCompressOverviewDao.getAlarmByStartTime()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<AlarmInfoCommonModel> resultList = new ArrayList<>();
        try {
            resultList = mapper.getAlarmByStartTime(alarmResultTableList, startTime);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getAlarmByStartTime() failed! error is {}", e);
        }
        return resultList;
    }

    /**
     * 根据时间查询告警/事件的数量
     *
     * @param tables 表名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 结果对象
     */
    public AlarmAndIncidentNumberModel getAlarmAndIncidentByTime(List<Map<String, Object>> tables, long startTime,
        long endTime) {
        LOGGER.info("entry AlarmCompressOverviewDao.getAlarmAndIncidentByTime()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        AlarmAndIncidentNumberModel result = new AlarmAndIncidentNumberModel();
        try {
            result = mapper.getAlarmAndIncidentByTime(tables, startTime, endTime);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getAlarmAndIncidentByTime() failed! error is {}", e);
        }
        return result;
    }

    /**
     * 根据根据时间查询事件级别饼图
     * 
     * @param tables 表名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 结果对象
     */
    public List<AlarmLevelCommonModel> queryIncidentLevelStatistics(List<Map<String, Object>> tables, long startTime,
        long endTime) {
        LOGGER.info("entry AlarmCompressOverviewDao.queryIncidentLevelStatistics()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<AlarmLevelCommonModel> result = new ArrayList<>();
        try {
            result = mapper.queryIncidentLevelStatistics(tables, startTime, endTime);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.queryIncidentLevelStatistics() failed! error is {}", e);
        }
        return result;
    }

    /**
     * 通过告警类型查询告警分析概览信息
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tableList 告警和事件表名集合
     * @return list
     */
    public List<IncidentAlarmType> getIncidentByAlarmType(long startTime, long endTime,
        List<Map<String, Object>> tableList) {
        LOGGER.info("entry AlarmCompressOverviewDao.getAlarmListByAlarmType()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<IncidentAlarmType> typeList = new ArrayList<>();
        try {
            typeList = mapper.getIncidentByAlarmType(startTime, endTime, tableList);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getAlarmListByAlarmType() failed! error is {}", e);
        }
        return typeList;
    }

    /**
     * 通过告警源类型查询告警分析概览信息
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tableList 告警和事件表名集合
     * @return list
     */
    public List<IncidentAlarmType> getIncidentByAlarmSourceType(long startTime, long endTime,
        List<Map<String, Object>> tableList) {
        LOGGER.info("entry AlarmCompressOverviewDao.getAlarmListByAlarmSourceType()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<IncidentAlarmType> typeList = new ArrayList<>();
        try {
            typeList = mapper.getIncidentByAlarmSourceType(startTime, endTime, tableList);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getAlarmListByAlarmSourceType() failed! error is {}", e);
        }
        return typeList;
    }

    /**
     * 通过告警类型查询告警分析概览信息（告警压缩）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tableList 告警和事件表名集合
     * @return list
     */
    public List<IncidentAlarmType> getCompressIncidentByAlarmType(long startTime, long endTime,
        List<Map<String, Object>> tableList) {
        LOGGER.info("entry AlarmCompressOverviewDao.getCompressIncidentByAlarmType()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<IncidentAlarmType> typeList = new ArrayList<>();
        try {
            typeList = mapper.getCompressIncidentByAlarmType(startTime, endTime, tableList);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getCompressIncidentByAlarmType() failed! error is {}", e);
        }
        return typeList;
    }

    /**
     * 通过告警源类型查询告警分析概览信息（告警压缩）
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tableList 告警和事件表名集合
     * @return list
     */
    public List<IncidentAlarmType> getCompressIncidentByAlarmSourceType(long startTime, long endTime,
        List<Map<String, Object>> tableList) {
        LOGGER.info("entry AlarmCompressOverviewDao.getCompressIncidentByAlarmSourceType()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<IncidentAlarmType> typeList = new ArrayList<>();
        try {
            typeList = mapper.getCompressIncidentByAlarmSourceType(startTime, endTime, tableList);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getCompressIncidentByAlarmSourceType() failed! error is {}", e);
        }
        return typeList;
    }

    /**
     * 查询任务表告警类型列表
     *
     * @param taskIdList 任务id列表
     * @return list
     */
    public List<String> getAlarmTypeTreeByIds(List<Integer> taskIdList) {
        LOGGER.info("entry AlarmCompressOverviewDao.getAlarmTypeTreeByIds()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<String> alarmTypeTrees = new ArrayList<>();
        try {
            alarmTypeTrees = mapper.getAlarmTypeTreeByIds(taskIdList);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getAlarmTypeTreeByIds() failed! error is {}", e);
        }
        return alarmTypeTrees;
    }

    /**
     * 查询任务表告警源类型列表
     *
     * @param taskIdList 任务id列表
     * @return list
     */
    public List<String> getAlarmSourceTypeTreeByIds(List<Integer> taskIdList) {
        LOGGER.info("entry AlarmCompressOverviewDao.getAlarmSourceTypeTreeByIds()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<String> alarmSourceTypeTrees = new ArrayList<>();
        try {
            alarmSourceTypeTrees = mapper.getAlarmSourceTypeTreeByIds(taskIdList);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getAlarmSourceTypeTreeByIds() failed! error is {}", e);
        }
        return alarmSourceTypeTrees;
    }

    /**
     * 查询清除时间在时间范围内的告警
     *
     * @param alarmTables 告警表名列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 告警list
     */
    public List<AlarmInfoCommonModel> getNoveltyAlarmModelByTime(List<String> alarmTables, long startTime, long endTime) {
        LOGGER.info("entry AlarmCompressOverviewDao.getNoveltyAlarmModelByTime()");
        AlarmCompressOverviewMapper mapper = mapperFactory.getMapper(AlarmCompressOverviewMapper.class);
        List<AlarmInfoCommonModel> result = new ArrayList<>();
        try {
            result = mapper.getNoveltyAlarmModelByTime(alarmTables, startTime, endTime);
        } catch (DataAccessException e) {
            LOGGER.error("AlarmAnalysisOverviewDao.getNoveltyAlarmModelByTime() failed! error is {}", e);
        }
        return result;
    }
}
