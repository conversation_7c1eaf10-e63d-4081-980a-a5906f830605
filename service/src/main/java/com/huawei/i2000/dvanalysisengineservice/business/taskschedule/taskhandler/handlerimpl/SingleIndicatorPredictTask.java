/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.taskschedule.taskhandler.handlerimpl;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvanalysisengineservice.business.common.ConfigurationUtil;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.TaskPeriodInfo;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.AlgorithmModelOper;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.ScheduleConstant;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.dto.AlgorithmModelMessage;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.taskhandler.AbstractTaskHandler;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTask;
import com.huawei.i2000.dvanalysisengineservice.util.EnvUtils;

import org.springframework.dao.DataAccessException;

import java.io.File;
import java.util.List;

/**
 * 异常处理检测任务
 *
 * <AUTHOR>
 * @since 2023/1/18
 */
public class SingleIndicatorPredictTask extends AbstractTaskHandler {
    private static final OssLog LOGGER = OssLogFactory.getLogger(SingleIndicatorPredictTask.class);

    public SingleIndicatorPredictTask(AnalysisTask analysisTask, Integer type) {
        super(analysisTask, type);
    }

    @Override
    protected void insertResult() throws ServiceException {
        if (type == ScheduleConstant.PREDICT_TASK) {
            try {
                // 推理任务，添加一条当前执行时间的推理操作状态为执行中的记录
                taskAnalysisResultDao.insertResult(analysisTask.getTaskId(), timespan, "predicting", "Processing");
            } catch (DataAccessException e) {
                LOGGER.error("insert taskAnalysisResult predicting error : {}", e);
                throw new ServiceException("insert taskAnalysisResult predicting error : " + e.getMessage());
            }
        } else {
            try {
                taskAnalysisResultDao.insertResult(analysisTask.getTaskId(), timespan, TaskConstant.EXECUTION_TYPE_TRAINING, TaskConstant.EXECUTION_STATUS_PROCESSION);
                String runningNodeAddress = EnvUtils.getRunningNodeAddress();
                taskAnalysisResultDao.insertTrainAddress(analysisTask.getTaskId(), runningNodeAddress);
            } catch (ServiceException e) {
                LOGGER.error("[IndicatorPredictTask] save running node address failed! ", e);
            }
        }
    }

    @Override
    protected void checkAndUpdateModel(List<String> fileList) throws ServiceException {
        AlgorithmModelMessage message = AlgorithmModelOper.getInstance().getOne(analysisTask.getTaskId());
        if (message == null) {
            if (fileList.isEmpty()) {
                if (analysisTask.getTrainStatus() == null ||
                    !analysisTask.getTrainStatus().equals(TaskConstant.TRAIN_STATUS_RUNNING_TRANIN)) {
                    scheduleCenter.handleTask(analysisTask, ScheduleConstant.TRAINNING_TASK);
                }
                throw new ServiceException("no model : {}", analysisTask.getTaskName());
            }
            message = new AlgorithmModelMessage(analysisTask.getTaskId(), 0);
            AlgorithmModelOper.getInstance().put(message);
        }

        File file = new File(
            ConfigurationUtil.algorithmPackagePath() + analysisTask.getAlgorithmModelName() + File.separator
                + "trainModel" + File.separator + "model" + File.separator + analysisTask.getTaskId() + File.separator);

        if (!file.exists()) {
            if (!file.mkdirs()) {
                LOGGER.error("mkdir model directory error : {}", analysisTask.getTaskName());
                throw new ServiceException("mkdir model directory error");
            }
            downLoadModel();
            return;
        }
        if (!file.isDirectory()) {
            LOGGER.error("model directory error : {}", analysisTask.getTaskName());
            throw new ServiceException("model directory error");
        }
        if (file.list().length != fileList.size()) {
            downLoadModel();
            return;
        }
        for (File children : file.listFiles()) {
            // 判断文件是否以txt格式，指标可能会增加和删除，模型更新txt文件必会更新
            if (children.getName().endsWith(".txt") && isLatestModel(children)) {
                return;
            }
        }
        downLoadModel();
    }


    @Override
    protected void updateResult() {
        try {
            taskAnalysisResultDao.updateResult(analysisTask.getTaskId(), timespan,
                TaskConstant.EXECUTION_TYPE_PREDICTING, TaskConstant.EXECUTION_STATUS_FAILED);
        } catch (ServiceException exception) {
            LOGGER.error("update task execute result fail", exception);
        }
    }

    @Override
    protected void handleResult(TaskPeriodInfo taskPeriod) {
        taskExecutionResultManager.excuteResultThread(taskPeriod);
    }

}
