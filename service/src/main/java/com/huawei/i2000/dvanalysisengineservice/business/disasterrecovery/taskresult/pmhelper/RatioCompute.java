/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2018-2021. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.pmhelper;

import static java.lang.Math.abs;

import com.huawei.bsp.biz.util.JsonUtil;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.i2000.dvanalysisengineservice.business.common.PerformanceDataExecutor;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.PerformanceClient;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.HistoryDatas;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.HistoryIndexValue;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.HistoryQueryData;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.HistoryQueryValue;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.model.IndicatorDetil;
import com.huawei.i2000.dvanalysisengineservice.model.KPIIndicator;
import com.huawei.i2000.dvanalysisengineservice.util.ApplicationContextHelper;

import com.alibaba.fastjson2.JSON;

import org.apache.commons.collections4.CollectionUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 同环比数据计算
 *
 * <AUTHOR>
 * @since 2022-08-28
 */
public class RatioCompute {
    /**
     * The Milliseconds per day.
     */
    public static final long MILLISECONDS_PER_DAY = 24L * 3600 * 1000;

    /**
     * The Milliseconds per week.
     */
    public static final long MILLISECONDS_PER_WEEK = 7L * MILLISECONDS_PER_DAY;

    /**
     * The Milliseconds per month.
     */
    public static final long MILLISECONDS_PER_MONTH = 30L * MILLISECONDS_PER_DAY;

    /**
     * The constant DEFAULT_VALUE.
     */
    public static final String DEFAULT_VALUE = "NaN";

    /**
     * The constant RATIO_VALUE.
     */
    public static final String RATIO_VALUE = "ratioValue";

    /**
     * The constant CURRENT_VALUE.
     */
    public static final String CURRENT_VALUE = "currentValue";

    /**
     * The constant LAST_VALUE.
     */
    public static final String LAST_VALUE = "LastValue";

    /**
     * The constant LAST_VALUES.
     */
    public static final String LAST_VALUES = "LastValues";

    private static final OssLog LOGGER = OssLogFactory.getLog(RatioCompute.class);

    /**
     * 左括号
     */
    private static final String LEFT_BRACKET = "(";

    /**
     * 右括号
     */
    private static final String RIGHT_BRACKET = ")";

    /**
     * 逗号
     */
    private static final String COMMA = ",";

    /**
     * 取拼接参与指标上一次值运算的值
     *
     * @param values 参与指标上一次值运算的值
     * @return 拼接后的结果
     */
    private static String getLastValues(List<Float> values) {
        StringBuilder lastValues = new StringBuilder();
        lastValues.append(LEFT_BRACKET);
        if (values == null || values.size() < 2) {
            return "";
        } else {
            for (int indexOfList = 0; indexOfList < values.size(); indexOfList++) {
                lastValues.append(values.get(indexOfList));
                if (indexOfList != values.size() - 1) {
                    lastValues.append(COMMA);
                }
            }
        }
        lastValues.append(RIGHT_BRACKET);
        return lastValues.toString();
    }

    /**
     * 取历史数据库进行同环比运算
     *
     * @param kpiIndicator 指标
     * @param kpiValue 指标值
     * @param kpiTime 指标上报时间
     * @param period 指标采集周期
     * @param ruleOperator 阈值规则
     * @return 同环比计算结果 RATIO_VALUE运算后同环比阈值；CURRENT_VALUE当前指标值；LAST_VALUE上一次指标值
     */
    public static Map<String, String> calculateRatioMean(KPIIndicator kpiIndicator, String kpiValue, Long kpiTime,
        int period, RuleOperator ruleOperator) {
        Map<String, String> valueMap = new HashMap<>();
        valueMap.put(RATIO_VALUE, DEFAULT_VALUE);
        valueMap.put(CURRENT_VALUE, DEFAULT_VALUE);
        valueMap.put(LAST_VALUE, DEFAULT_VALUE);
        valueMap.put(LAST_VALUES, DEFAULT_VALUE);
        try {
            Pair<List<Float>, Float> pair = getLastValue(kpiTime, kpiIndicator, period, ruleOperator);
            float value = pair.second();
            List<Float> values = pair.first();
            // 记录参与上一次值计算的值
            valueMap.put(LAST_VALUES, getLastValues(values));
            // 记录上一次值
            valueMap.put(LAST_VALUE, String.format(Locale.ROOT, "%.2f", value));
            // 记录当前值
            valueMap.put(CURRENT_VALUE, kpiValue);
            if (value != 0) {
                // 计算同环比阈值
                float res = 100 * abs((Float.parseFloat(kpiValue) - value) / value);
                valueMap.put(RATIO_VALUE, Float.toString(res));
            } else {
                LOGGER.info("the ratio value is zero, so discard it!");
            }

        } catch (OSSException e) {
            LOGGER.error("catch an exception", e);
        }
        return valueMap;
    }

    /**
     * 查询历史数据获取上一次值
     *
     * @param kpiTime 指标上报时间
     * @param kpiIndicator 指标
     * @param period 采集周期
     * @param ruleOperator 阈值规则运算实体类
     * @return 根据配置文件中设置同环比阈值规则返回最大、最小、平均值其中一个
     * @throws OSSException 异常
     */
    private static Pair<List<Float>, Float> getLastValue(Long kpiTime, KPIIndicator kpiIndicator, int period,
        RuleOperator ruleOperator) throws OSSException {
        Pair<List<Float>, Float> pair = new Pair<>();
        if (kpiTime == null || ruleOperator == null) {
            return pair;
        }
        List<Long> times = getTimeStamp(kpiTime, ruleOperator.getSamplingPeriod(), ruleOperator.getSamplingCount(),
            period);
        List<Float> values = new ArrayList<>(times.size());
        float value = 0f;

        long startTime = System.currentTimeMillis();
        getLastValues(kpiIndicator, times, values);
        pair.first(values);
        pair.second(value);
        if (CollectionUtils.isEmpty(values)) {
            LOGGER.info("last values is empty");
            return pair;
        }
        LOGGER.debug("queryData costTime={},data is {}", System.currentTimeMillis() - startTime, values);
        value = getAVG(values);
        pair.second(value);
        return pair;
    }

    public static void getLastValues(KPIIndicator kpiIndicator, List<Long> times, List<Float> values) {
        int period = PerformanceClient.getInstance().getPeriod(kpiIndicator.getMoType(), kpiIndicator.getMeasUnitKey());
        if (period == -1) {
            LOGGER.error("[CapacityPerformanceDataSourceHandler] get task periods error, taskName = {}");
        }
        HistoryDatas historyDatas = getHistoryDatas(kpiIndicator, times, period);
        LOGGER.info("historyData is {}", historyDatas);
        long beginTime = System.currentTimeMillis();
        LOGGER.info("[RatioCompute] executePm start time={}", beginTime);
        PerformanceDataExecutor performanceDataExecutor = (PerformanceDataExecutor) ApplicationContextHelper.getBean("performanceDataExecutor");
        Future<List<HistoryQueryData>> future = performanceDataExecutor.executeOther(historyDatas);
        List<HistoryQueryData> historyQueryDataList;
        try {
            historyQueryDataList = future.get(TaskConstant.FUTURE_GET_TIME_OUT, TimeUnit.MILLISECONDS);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            LOGGER.info("[RatioCompute] getHistoryPerformanceData historyQueryDataList get data error", e);
            historyQueryDataList = new ArrayList<>();
        }
        LOGGER.info("[RatioCompute] executePm cost time={}", Math.subtractExact(System.currentTimeMillis(), beginTime));
        if (CollectionUtils.isEmpty(historyQueryDataList)) {
            LOGGER.error("getHistoryPerformanceData historyQueryDataList is empty !, historyData is {}", historyDatas);
            return;
        }
        HistoryQueryData historyQueryData = historyQueryDataList.get(0);
        List<HistoryQueryValue> historyQueryValueList = historyQueryData.getResultData();
        if (CollectionUtils.isEmpty(historyQueryValueList)) {
            LOGGER.error("historyQueryDataList is empty ! indicator is {}", kpiIndicator);
            return;
        }
        for (Long time : times) {
            for (HistoryQueryValue historyQueryValue : historyQueryValueList) {
                List<HistoryIndexValue> indexValues = historyQueryValue.getIndexValues();
                for (HistoryIndexValue historyIndexValue : indexValues) {
                    if (historyIndexValue.getTimestamp() <= time
                        && time - historyIndexValue.getTimestamp() < period * 1000L) {
                        values.add(Float.parseFloat(historyIndexValue.getIndexValue()));
                    }
                }
            }
        }
    }

    private static HistoryDatas getHistoryDatas(KPIIndicator kpiIndicator, List<Long> times, int period) {
        HistoryDatas historyDatas = new HistoryDatas();
        historyDatas.setBeginTime(times.get(times.size() - 1) - period * 1000L);
        historyDatas.setEndTime(System.currentTimeMillis());
        Map<Long, Long> timeRangeMap = new HashMap<>();
        for (Long time : times) {
            timeRangeMap.put(time - period * 1000L, time);
        }
        try {
            historyDatas.setTimeRangeMap(JsonUtil.marshal(timeRangeMap));
        } catch (IOException e) {
            LOGGER.error("marshal failed");
        }
        // 这里设置的是测量单元key，不是测量单元名
        historyDatas.setMeasUnitKey(kpiIndicator.getMeasUnitKey());
        IndicatorDetil indicatorDetil = new IndicatorDetil();
        indicatorDetil.setDisplayValue(kpiIndicator.getDisplayValue());
        indicatorDetil.setDn(kpiIndicator.getDn());
        // 这里设置的是测量指标key，不是测量指标名
        indicatorDetil.setIndexName(kpiIndicator.getMeasTypeKey());
        List<IndicatorDetil> indicatorDetilList = new ArrayList<>();
        indicatorDetilList.add(indicatorDetil);
        // 测量指标
        historyDatas.setMo2Index(JSON.toJSONString(indicatorDetilList));
        historyDatas.setMoType(kpiIndicator.getMoType());
        return historyDatas;
    }

    public static float getAVG(List<Float> values) {
        if (CollectionUtils.isEmpty(values)) {
            return 0;
        }
        float total = 0f;
        for (float value : values) {
            total += value;
        }
        return total / values.size();
    }

    /**
     * 获取指定时间戳
     *
     * @param currentTime 当前时间
     * @param comparePeriod 采样时间间隔
     * @param sampleNum 样本点个数
     * @param period 采样周期
     * @return 时间戳列表 time stamp
     */
    public static List<Long> getTimeStamp(long currentTime, String comparePeriod, int sampleNum, int period) {
        List<Long> timeStamps = new ArrayList<>();
        long millisecond;
        switch (comparePeriod) {
            case "DAY":
                millisecond = MILLISECONDS_PER_DAY;
                break;
            default:
                millisecond = 1000L * period;
        }
        for (int i = 1; i < sampleNum + 1; i++) {
            long time = currentTime - (millisecond * i);
            timeStamps.add(time);
        }

        return timeStamps;
    }

}
