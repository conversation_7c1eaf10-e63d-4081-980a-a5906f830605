/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.dao.taskmanage;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvanalysisengineservice.business.capacity.dto.CapacityHistoryResult;
import com.huawei.i2000.dvanalysisengineservice.business.capacity.dto.CapacityIndicatorDependence;
import com.huawei.i2000.dvanalysisengineservice.dao.common.MapperFactory;
import com.huawei.i2000.dvanalysisengineservice.dao.multiple.mapper.CapacityAnalysisMapper;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.mapper.MultipleTaskManageMapper;
import com.huawei.i2000.dvanalysisengineservice.model.CapacityHistoryTimeStamp;
import com.huawei.i2000.dvanalysisengineservice.model.CapacityPredictionTaskQuery;
import com.huawei.i2000.dvanalysisengineservice.model.MultipleExecutionNode;
import com.huawei.i2000.dvanalysisengineservice.model.MultipleRelationIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.MultipleTaskMessage;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.exceptions.PersistenceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * CapacityTaskManageDao CapacityTaskManageDao
 * CapacityTaskManageDao
 *
 * <AUTHOR>
 * @since 2022-08-25
 */
@Repository
public class CapacityTaskManageDao {
    private static final OssLog LOGGER = OssLogFactory.getLog(CapacityTaskManageDao.class);

    private static final int BATCH_SIZE = 200;

    @Autowired
    MapperFactory mapperFactory;

    /**
     * 设置容量任务预测状态
     *
     * @param status status
     * @param taskId taskId
     */
    public void updateCapacityTaskPredictStatus(Integer status, Integer taskId) {
        MultipleTaskManageMapper multipleTaskManageMapper = mapperFactory.getMapper(MultipleTaskManageMapper.class);
        multipleTaskManageMapper.updateCapacityTaskPredictStatus(status, taskId);
    }

    /**
     * insertCapacityTaskPredictHistoryResult
     *
     * @param historyResult historyResult
     */
    public void insertCapacityTaskPredictHistoryResult(CapacityHistoryResult historyResult) {
        MultipleTaskManageMapper multipleTaskManageMapper = mapperFactory.getMapper(MultipleTaskManageMapper.class);
        multipleTaskManageMapper.insertCapacityTaskPredictHistoryResult(historyResult);
    }

    /**
     * 设置容量任务预测状态
     *
     * @param taskId taskId
     * @param runningNodeAddress runningNodeAddress
     */
    public void insertPredictAddress(Integer taskId, String runningNodeAddress) {
        MultipleTaskManageMapper multipleTaskManageMapper = mapperFactory.getMapper(MultipleTaskManageMapper.class);
        multipleTaskManageMapper.updatePredictAddress(taskId, runningNodeAddress);
    }

    /**
     * 更新容量任务结果
     *
     * @param queryParam queryParam
     */
    public void updateCapacityTaskResult(CapacityPredictionTaskQuery queryParam) {
        MultipleTaskManageMapper multipleTaskManageMapper = mapperFactory.getMapper(MultipleTaskManageMapper.class);
        multipleTaskManageMapper.updateCapacityTaskResult(queryParam.getRelationTree(), queryParam.getTaskId());
    }


    /**
     * 设置容量任务优先
     *
     * @param taskId taskId
     * @throws ServiceException ServiceException
     */
    @Transactional(rollbackFor = ServiceException.class)
    public void setCapacityTaskSort(Integer taskId) throws ServiceException {
        MultipleTaskManageMapper multipleTaskManageMapper = mapperFactory.getMapper(MultipleTaskManageMapper.class);
        multipleTaskManageMapper.updateCapacityTaskSort(taskId, System.currentTimeMillis());
    }

    /**
     * 获取容量任务预测状态
     *
     * @param taskId taskId
     * @return predictStatus
     * @throws ServiceException ServiceException
     */
    public Integer getCapacityTaskPredictStatus(Integer taskId) throws ServiceException {
        MultipleTaskManageMapper multipleTaskManageMapper = mapperFactory.getMapper(MultipleTaskManageMapper.class);
        try {
            return multipleTaskManageMapper.queryCapacityTaskPredictStatus(taskId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("getCapacityTaskPredictStatus error", e);
            throw new ServiceException("getCapacityTaskPredictStatus error");
        }
    }

    /**
     * queryCapacityCorrelation
     *
     * @param taskId taskId
     * @return multipleTaskMessage
     */
    public MultipleTaskMessage queryCapacityCorrelation(Integer taskId){
        MultipleTaskManageMapper multipleTaskManageMapper = mapperFactory.getMapper(MultipleTaskManageMapper.class);
        return multipleTaskManageMapper.queryCapacityCorrelation(taskId);
    }

    /**
     * getHistoryRelationTree
     *
     * @param taskId taskId
     * @param historyId historyId
     * @return relationTree
     */
    public CapacityHistoryResult getHistoryRelationTree(Integer taskId, String historyId){
        MultipleTaskManageMapper multipleTaskManageMapper = mapperFactory.getMapper(MultipleTaskManageMapper.class);
        return multipleTaskManageMapper.queryHistoryRelationTree(taskId, historyId);
    }

    /**
     * getHistoryTimeStamp
     *
     * @param taskId taskId
     * @return relationTree
     */
    public List<CapacityHistoryTimeStamp> getHistoryTimeStamp(Integer taskId) {
        MultipleTaskManageMapper multipleTaskManageMapper = mapperFactory.getMapper(MultipleTaskManageMapper.class);
        List<CapacityHistoryTimeStamp> result;
        try {
            result = multipleTaskManageMapper.queryHistoryTimeStamp(taskId);
        } catch (DataAccessException e) {
            LOGGER.error("getHistoryTimeStamp error", e);
            return new ArrayList<>();
        }
        for (CapacityHistoryTimeStamp timeStamp : result) {
            if (StringUtils.isNotEmpty(timeStamp.getMainTimeRange())) {
                timeStamp.setPredictValue(timeStamp.getMainTimeRange());
            } else {
                timeStamp.setPredictValue(timeStamp.getMainKpiValue());
            }
        }
        return result;
    }

    /**
     * getHistoryTimeStamp
     *
     * @param taskId taskId
     * @return List<String>
     */
    public List<String> queryCapacityAssociateTreeAndGroup(Integer taskId) {
        MultipleTaskManageMapper multipleTaskManageMapper = mapperFactory.getMapper(MultipleTaskManageMapper.class);
        List<String> result;
        try {
            result = multipleTaskManageMapper.queryCapacityAssociateTreeAndGroup(taskId);
        } catch (DataAccessException e) {
            LOGGER.error("queryCapacityAssociateTreeAndGroup error", e);
            return new ArrayList<>();
        }
        return result;
    }

    /**
     * getHistoryTimeStamp
     *
     * @param taskId taskId
     * @return relationTree
     */
    public CapacityHistoryResult queryReverseHistoryRelationTree(Integer taskId) {
        MultipleTaskManageMapper multipleTaskManageMapper = mapperFactory.getMapper(MultipleTaskManageMapper.class);
        return multipleTaskManageMapper.queryReverseHistoryRelationTree(taskId);
    }

    /**
     * queryCapacityEamRelationTree
     *
     * @param taskId taskId
     * @return relationTree
     */
    public String queryCapacityEamRelationTree(Integer taskId){
        MultipleTaskManageMapper multipleTaskManageMapper = mapperFactory.getMapper(MultipleTaskManageMapper.class);
        return multipleTaskManageMapper.queryCapacityEamRelationTree(taskId);
    }

    /**
     * getExecutionInfoByTaskId
     *
     * @param taskId 任务id集合
     * @return 分析任务id
     */
    public List<MultipleExecutionNode> getExecutionInfoByTaskId(Integer taskId) {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        return capacityAnalysisMapper.getExecutionInfoByTaskId(taskId);
    }

    /**
     * 获取容量任务节点信息
     *
     * @param taskId taskId
     * @return MultipleRelationIndicatorList
     */
    public List<MultipleRelationIndicator> getExecutionNodeInfo(Integer taskId) {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        return capacityAnalysisMapper.getExecutionNodeInfo(taskId);
    }

    /**
     * 找出双机节点中应该忽略的指标
     *
     * @param taskId taskId
     * @return indicatorIds
     */
    public List<String> getIgnoreIndicators(Integer taskId) {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        try {
            return capacityAnalysisMapper.getIgnoreIndicators(taskId);
        } catch (DataAccessException e) {
            LOGGER.error("getIgnoreIndicators error", e);
            return Collections.emptyList();
        }
    }

    /**
     * queryNodeIndicatorIds
     *
     * @param taskId 任务id集合
     * @param nodeId nodeId
     * @return 分析任务id
     */
    public List<String> queryNodeIndicatorIds(Integer taskId, String nodeId) {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        return capacityAnalysisMapper.queryNodeIndicatorIds(taskId, nodeId);
    }

    /**
     * querySumIndicators
     *
     * @param taskId 任务id集合
     * @return 分析任务id
     */
    public Integer queryCapacityIndicatorNum(Integer taskId) {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        Integer indicatorNum = capacityAnalysisMapper.getExecutionIndicatorByTaskId(taskId).size();
        Integer eamTreeIndicatorNum = capacityAnalysisMapper.getEamNodes(taskId, null).size();
        return indicatorNum + eamTreeIndicatorNum;
    }

    /**
     * queryNodeCountResult
     *
     * @param taskId 任务id集合
     * @return resultRelationTree
     */
    public List<String> queryCapacityResultRelationTree(Integer taskId) {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        return capacityAnalysisMapper.queryCapacityResultRelationTree(taskId);
    }

    /**
     * queryNodeDependenceIndicatorIds
     *
     * @param taskId 任务id集合
     * @param nodeId nodeId
     * @return 分析任务id
     */
    public List<String> queryNodeDependenceIndicatorIds(Integer taskId, String nodeId) {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        return capacityAnalysisMapper.queryNodeDependenceIndicatorIds(taskId, nodeId);
    }

    /**
     * 更新容量任务节点指标预测结果
     *
     * @param multipleRelationIndicator multipleRelationIndicator
     */
    public void updateExecutionAlgorithmResult(MultipleRelationIndicator multipleRelationIndicator) {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        capacityAnalysisMapper.updateExecutionAlgorithmResult(multipleRelationIndicator);
    }

    /**
     * 更新容量任务节点指标预测结果
     *
     * @param multipleRelationIndicators multipleRelationIndicators
     */
    public void updateExecutionAlgorithmResultBatch(List<MultipleRelationIndicator> multipleRelationIndicators) {
        if (CollectionUtils.isEmpty(multipleRelationIndicators)) {
            return;
        }
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        try {
            for (List<MultipleRelationIndicator> list : ListUtils.partition(multipleRelationIndicators, BATCH_SIZE)) {
                capacityAnalysisMapper.updateExecutionAlgorithmResultBatch(list);
            }
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[CapacityTaskManageDao] updateExecutionAlgorithmResultBatch failed! ", e);
        }
    }

    /**
     * 查询除某个任务外指标总数
     *
     * @param taskId taskId
     * @return total
     * @throws ServiceException ServiceException
     */
    public int queryTaskIndicatorNum(Integer taskId) throws ServiceException {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        Integer capacityTotalIndicatorNum = capacityAnalysisMapper.getCapacityIndicatorCount(null);
        if (taskId != null) {
            capacityTotalIndicatorNum -= capacityAnalysisMapper.getCapacityIndicatorCount(taskId);
        }
        return capacityTotalIndicatorNum;
    }

    /**
     * 查看模板是否已经生成
     *
     * @param taskId taskId
     * @return Boolean
     * @throws ServiceException ServiceException
     */
    public Boolean isTemplateCompleted(Integer taskId) throws ServiceException {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        try {
            return capacityAnalysisMapper.isTemplateCompleted(taskId);
        } catch (DataAccessException e) {
            LOGGER.error("queryIsTemplateCompleted error", e);
            // 数据库异常时认为已经生成模板
            return true;
        }
    }

    /**
     * 标记模板已经生成
     *
     * @param taskId taskId
     * @throws ServiceException ServiceException
     */
    public void markTemplateCompleted(Integer taskId) throws ServiceException {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        try {
            capacityAnalysisMapper.markTemplateCompleted(taskId);
        } catch (DataAccessException e) {
            LOGGER.error("markTemplateCompleted error", e);
        }
    }

    /**
     * 查询节点和依赖指标的关系
     *
     * @param taskId taskId
     * @return list
     */
    public List<CapacityIndicatorDependence> queryNodeAndDependenceIndicatorIds(Integer taskId) {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        try {
            return capacityAnalysisMapper.queryNodeAndDependenceIndicatorIds(taskId);
        } catch (DataAccessException e) {
            LOGGER.error("queryNodeDependenceIndicatorIds error", e);
            return new ArrayList<>();
        }
    }

    /**
     * 容量openapi查询历史执行记录
     *
     * @param taskId taskId
     * @param mainTimeRange mainTimeRange
     * @param mainKpiValue mainKpiValue
     * @return CapacityHistoryResult
     */
    public CapacityHistoryResult queryCapacityHistoryResult(Integer taskId, String mainTimeRange, String mainKpiValue) {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        try {
            if (StringUtils.isEmpty(mainTimeRange)) {
                mainTimeRange = null;
            }
            if (StringUtils.isEmpty(mainKpiValue)) {
                mainKpiValue = null;
            }
            return capacityAnalysisMapper.queryCapacityHistoryResult(taskId, mainTimeRange, mainKpiValue);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("queryCapacityHistoryResult error,e=", e);
            return null;
        }
    }
}
