/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.ConfigurationUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.FileCleanUtils;
import com.huawei.i2000.dvanalysisengineservice.business.common.InternationalConstant;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.ScheduleConstant;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.AbstractHandleDto;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.CustomDataSourcePerformanceHandleDto;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.CustomIndicatorData;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.DataSourceIndicatorInfo;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.DataSourceInfo;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.DataSourceHandlerDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.model.TaskIndicator;
import com.huawei.i2000.dvanalysisengineservice.util.ApplicationContextHelper;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;

import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 自定义数据源指标处理
 *
 * <AUTHOR>
 * @since 2021/11/3
 */
public class CustomDataSourcePerformanceHandler extends AbstractDataSourceHandler {
    private static OssLog LOGGER = OssLogFactory.getLogger(CustomDataSourcePerformanceHandler.class);

    private static final String SEPARATOR = "_";

    private static final String COMMA = ",";

    private static final String SUFFIX = ".csv";

    // 最多一次查询10个指标
    private static final int MAX_INDEX_NUM = 10;

    // 训练任务一次查询1个指标
    private static final int MIN_INDEX_NUM = 1;

    private static final String CUSTOM_PERFORMANCE_FILE_HEADER
        = "collect_time,kpi_id,kpi_value,kpi_unit,sampling_period,mo_type";

    private static final String PERFORMANCE_ERROR_MESSAGE_FILE_HEADER = "kpi_id,failure_cause";

    @Override
    public DataSourceInfo handle(AbstractHandleDto dataInfo) {
        CustomDataSourcePerformanceHandleDto task;
        if (dataInfo instanceof CustomDataSourcePerformanceHandleDto) {
            task = (CustomDataSourcePerformanceHandleDto) dataInfo;
        } else {
            LOGGER.error("[CustomDataSourcePerformanceHandler] dataInfo type error, dataInfo = {}", dataInfo);
            return null;
        }
        LOGGER.info("[CustomDataSourcePerformanceHandler] handle task start, taskName = {}, taskId = {}",
            task.getTaskName(), task.getTaskId());

        int indicateNumber = task.getOperationType() == ScheduleConstant.PREDICT_TASK ? MAX_INDEX_NUM : MIN_INDEX_NUM;

        if (CollectionUtils.isEmpty(task.getIndicatorList()) || StringUtils.isEmpty(task.getTableName())) {
            LOGGER.error("[CustomDataSourcePerformanceHandler] indicatorList is empty or tablename is empty");
            return null;
        }

        List<String> idList = task.getIndicatorList()
            .stream()
            .map(TaskIndicator::getIndicatorId)
            .collect(Collectors.toList());

        List<List<String>> indicators = ListUtils.partition(idList, indicateNumber);
        // 查询数据库获取告警关联关系
        DataSourceHandlerDao dataSourceHandlerDao = (DataSourceHandlerDao) ApplicationContextHelper.getBean(
            "dataSourceHandlerDao");
        String fileName = task.getTaskId() + SEPARATOR + task.getTriggerTime() + "_single" + SUFFIX;
        if (StringUtils.isEmpty(fileName)) {
            LOGGER.error("[CustomDataSourcePerformanceHandler] fileName is empty, name = {}", fileName);
            return null;
        }
        JSONObject failureList = new JSONObject();
        DataSourceInfo dataSourceInfo = dataConvertToCsv(indicators, dataSourceHandlerDao, task, failureList, fileName);
        if (!failureList.isEmpty()) {
            dataSourceInfo = createErrorMessageFile(dataSourceInfo, task, failureList);
        }
        LOGGER.info("[CustomDataSourcePerformanceHandler] handle task end, taskName = {}", task.getTaskName());
        return dataSourceInfo;
    }

    @Override
    public Map<String, DataSourceIndicatorInfo> multipleHandle(AbstractHandleDto dataInfo) {
        return null;
    }


    /**
     * 性能接口返回值生成naie需要的csv文件
     *
     * @param indicators 存储指标集合
     * @param dataSourceHandlerDao dataSourceHandlerDao
     * @param task task
     * @param failureList failureList
     * @param fileName fileName
     * @return 生成的csv文件路径
     */
    private DataSourceInfo dataConvertToCsv(List<List<String>> indicators, DataSourceHandlerDao dataSourceHandlerDao,
        CustomDataSourcePerformanceHandleDto task, JSONObject failureList, String fileName) {
        File tempFile = writeFileHeader(CUSTOM_PERFORMANCE_FILE_HEADER, fileName);
        if (tempFile == null || !tempFile.exists()) {
            return null;
        }
        DataSourceInfo dataSourceInfo = new DataSourceInfo();
        long gridsNumberCount = 0L;
        for (List<String> subIndicator : indicators) {
            // 训练需要查所有数据，推理不需要查所有的数据，需要从配置表里读一个开始时间作为起始时间，默认1天;
            long startTime = getStartTimeForQueryCustomData(task);
            List<CustomIndicatorData> customIndicators = dataSourceHandlerDao.getCustomIndicatorData(
                task.getTableName(), subIndicator, task.getTriggerTime(), startTime);
            // 把性能数据转换成NAIE需要的格式便于一起写入csv文件
            List<List<String>> convertString = convertDataToString(customIndicators, task, dataSourceInfo, failureList);
            if (CollectionUtils.isEmpty(convertString)) {
                setEmptyQueryDataCause(subIndicator, failureList);
                LOGGER.error("[CustomDataSourcePerformanceHandler] createPerformanceCsvFile error, line is empty, fileName={}", fileName);
                continue;
            }
            // 增加行数
            gridsNumberCount += convertString.size();

            try (PrintWriter pw = new PrintWriter(new OutputStreamWriter(new FileOutputStream(tempFile, true), StandardCharsets.UTF_8));
                CSVPrinter printer = new CSVPrinter(pw, CSVFormat.DEFAULT)) {
                for (List<String> line : convertString) {
                    printer.printRecord(line);
                }
            } catch (IOException e) {
                LOGGER.error("[CustomDataSourcePerformanceHandler] createPerformanceCsvFile error, e={}", e);
                deleteFile(tempFile);
                return null;
            }
            LOGGER.debug("[CustomDataSourcePerformanceHandler] query performance data, convertStringSize={}", convertString.size());
        }
        if (FileCleanUtils.fileSizeOverMax(tempFile)) {
            LOGGER.error("[CustomDataSourcePerformanceHandler] createAndPushFile error, file size over size, file={}", tempFile.getName());
            deleteFile(tempFile);
            return null;
        }
        // 计算数量传给调度使用
        dataSourceInfo.setGridsNumber(dataSourceInfo.getGridsNumber() + gridsNumberCount * CUSTOM_PERFORMANCE_FILE_HEADER.split(COMMA).length);
        LOGGER.info("[CustomDataSourcePerformanceHandler] createPerformanceCsvFile end, filename = {}, size = {}", fileName, gridsNumberCount);
        if (gridsNumberCount == 0) {
            deleteFile(tempFile);
            return null;
        }
        try {
            dataSourceInfo.setSrcFilePath(tempFile.getCanonicalPath());
            dataSourceInfo.setDestFilePath(TaskConstant.TEMP_DEST_PATH);
        } catch (IOException e) {
            LOGGER.error("[CustomDataSourcePerformanceHandler] pushDefaultSourceFile error, e={}", e.getMessage());
            return null;
        }
        return dataSourceInfo;
    }

    private void setEmptyQueryDataCause(List<String> subIndicator, JSONObject failureList) {
        for (String indicatorId : subIndicator) {
            if (!failureList.containsKey(indicatorId)) {
                failureList.put(indicatorId, ResourceUtil.getMessage(InternationalConstant.NO_PERFORMANCE_DATA_FOUND, ContextUtils.getDefaultLocale()));
            }
        }
    }

    private static long getStartTimeForQueryCustomData(CustomDataSourcePerformanceHandleDto task) {
        long startTime = 0L;
        if (task.getOperationType() == ScheduleConstant.PREDICT_TASK) {
            startTime = task.getTriggerTime() - ConfigurationUtil.getCustomDataPredictDayLimit() * 60 * 1000L;
        }
        return startTime;
    }

    private boolean trainCount(CustomDataSourcePerformanceHandleDto task, int count) {
        if (task.getOperationType() != ScheduleConstant.TRAINNING_TASK || ScheduleConstant.TRANSFER_SWITCH.equals(
            task.getTransferSwitch())) {
            return false;
        }
        if (count == 0 || task.getIndicatorLowestCount() > task.getDataNum()) {
            return true;
        }
        return count < task.getIndicatorLowestCount();
    }

    /**
     * 性能接口返回值生成规定格式
     *
     * @param customIndicators 存储指标信息集合
     * @param task task
     * @param dataSourceInfo dataSourceInfo
     * @param failureList failureList
     * @return List 返回值转成的规定格式的数据
     */
    private List<List<String>> convertDataToString(List<CustomIndicatorData> customIndicators,
        CustomDataSourcePerformanceHandleDto task, DataSourceInfo dataSourceInfo, JSONObject failureList) {
        List<List<String>> dataString = new ArrayList<>();
        List<TaskIndicator> taskIndicatorList = task.getIndicatorList();
        Map<String, List<CustomIndicatorData>> collectMap = customIndicators.stream()
            .collect(Collectors.groupingBy(CustomIndicatorData::getIndicatorId));
        for (List<CustomIndicatorData> list : collectMap.values()) {
            if (list.size() > task.getDataNum()) {
                list = list.subList((int) (list.size() - task.getDataNum()), list.size());
            }
            String indicatorId = list.get(0).getIndicatorId();
            if (!valideDataTime(list, task.getTriggerTime())) {
                LOGGER.info("[CustomDataSourcePerformanceHandler] this indicator not update long time, id = {}", indicatorId);
                failureList.put(indicatorId, ResourceUtil.getMessage(InternationalConstant.CUSTOM_DATA_SOURCE_NO_DATA, ContextUtils.getDefaultLocale()));
                continue;
            }
            if (trainCount(task, list.size())) {
                LOGGER.error("indicator data is less than requirement,indicatorId={}", indicatorId);
                failureList.put(indicatorId, ResourceUtil.getMessage(InternationalConstant.NOT_SATISFIED_TRAINING_POINT, new Object[]{task.getIndicatorLowestCount(), list.size()}, ContextUtils.getDefaultLocale()));
                continue;
            }

            String indicatorMoType = getIndicatorMoType(taskIndicatorList, indicatorId);

            dataSourceInfo.setTrainDataEnoughCount(dataSourceInfo.getTrainDataEnoughCount() + 1);
            for (CustomIndicatorData customIndicatorData : list) {
                List<String> indexValue = new ArrayList<>();
                indexValue.add(" " + customIndicatorData.getCollectTime());
                indexValue.add(" " + customIndicatorData.getIndicatorId());
                indexValue.add(" " + customIndicatorData.getIndicatorValue());
                indexValue.add(StringUtils.EMPTY);
                indexValue.add(StringUtils.EMPTY);
                indexValue.add(indicatorMoType);
                dataString.add(indexValue);
            }
        }
        return dataString;
    }

    public boolean valideDataTime(List<CustomIndicatorData> list, long triggerTime) {
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }

        long lastTime = list.get(list.size() - 1).getCollectTime();
        long noUpdateTime = Math.subtractExact(triggerTime, lastTime);
        long updateTime = ConfigurationUtil.customPerformanceUpdateTime();
        int updateCount = ConfigurationUtil.customPerformanceUpdateCount();
        if (list.size() == 1) {
            return false;
        } else {
            long period = lastTime - list.get(list.size() - 2).getCollectTime();
            if (noUpdateTime >= updateCount * period && noUpdateTime >= updateTime) {
                return false;
            }
        }
        return true;
    }

    private DataSourceInfo createErrorMessageFile(DataSourceInfo dataSourceInfo, CustomDataSourcePerformanceHandleDto task, JSONObject failureList) {
        // 记录指标失败信息传给算法
        String fileName = task.getTaskId() + SEPARATOR + task.getTriggerTime() + SEPARATOR + "failure" + SUFFIX;
        File tempFile = writeFileHeader(PERFORMANCE_ERROR_MESSAGE_FILE_HEADER, fileName);
        if (Objects.isNull(tempFile)) {
            LOGGER.error("[CustomDataSourcePerformanceHandler] fail to writeFileHeader .");
            return combineDataSource(dataSourceInfo, null);
        }
        return getErrorMessageDataSourceInfo(dataSourceInfo, failureList, fileName, tempFile, PERFORMANCE_ERROR_MESSAGE_FILE_HEADER);
    }
}
