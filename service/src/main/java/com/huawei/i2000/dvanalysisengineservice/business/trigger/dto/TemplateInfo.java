/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2019. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.trigger.dto;

import java.util.Date;
import java.util.List;

/**
 * TemplateInfo
 *
 * <AUTHOR>
 * @since 2021/3/16
 */
public class TemplateInfo {
    String id;

    String name;

    String des;

    String restype;

    String createuser;

    Date createtime;

    Date lastmodifytime;

    List<Template2Index> template2Index;

    List<IndexInfo> indexInfos;

    CollectObject object;

    /**
     * TemplateInfo
     */
    public TemplateInfo() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDes() {
        return des;
    }

    public void setDes(String des) {
        this.des = des;
    }

    public String getRestype() {
        return restype;
    }

    public void setRestype(String restype) {
        this.restype = restype;
    }

    public String getCreateuser() {
        return createuser;
    }

    public void setCreateuser(String createuser) {
        this.createuser = createuser;
    }

    public Date getCreatetime() {
        return new Date(createtime.getTime());
    }

    public void setCreatetime(Date createtime) {
        this.createtime = (Date) createtime.clone();
    }

    public Date getLastmodifytime() {
        return new Date(lastmodifytime.getTime());
    }

    public void setLastmodifytime(Date lastmodifytime) {
        this.lastmodifytime = (Date) lastmodifytime.clone();
    }

    public List<Template2Index> getTemplate2Index() {
        return template2Index;
    }

    public void setTemplate2Index(List<Template2Index> template2Index) {
        this.template2Index = template2Index;
    }

    public List<IndexInfo> getIndexInfos() {
        return indexInfos;
    }

    public void setIndexInfos(List<IndexInfo> indexInfos) {
        this.indexInfos = indexInfos;
    }

    public CollectObject getObject() {
        return object;
    }

    public void setObject(CollectObject object) {
        this.object = object;
    }

    /**
     * compare
     *
     * @param template TemplateInfo
     * @return boolean
     */
    public boolean compare(TemplateInfo template) {
        if (template == null) {
            return false;
        } else {

            boolean isCreateTime = template.getCreatetime() == null
                || template.getCreatetime() != null && template.getCreatetime().compareTo(this.createtime) == 0;

            boolean isCreateUser = template.getCreateuser() == null && this.createuser == null
                || template.getCreateuser() != null && template.getCreateuser().equalsIgnoreCase(this.createuser);

            boolean isDes = template.getDes() == null && this.des == null
                || template.getDes() != null && template.getDes().equalsIgnoreCase(this.des);

            boolean isLastModifyTime = template.getLastmodifytime() == null || template.getLastmodifytime() != null
                && template.getLastmodifytime().compareTo(this.lastmodifytime) == 0;

            boolean isName = template.getName() == null && this.name == null
                || template.getName() != null && template.getName().equalsIgnoreCase(this.name);

            boolean isRestType = template.getRestype() == null && this.restype == null
                || template.getRestype() != null && template.getRestype().equalsIgnoreCase(this.restype);

            boolean isFoo = isCreateTime && isCreateUser && isDes;

            return isFoo && isLastModifyTime && isName && isRestType;
        }
    }
}
