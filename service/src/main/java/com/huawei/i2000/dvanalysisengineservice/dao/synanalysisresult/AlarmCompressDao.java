/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.constant.SynAnalysisResultConstant;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.AlarmIncident;
import com.huawei.i2000.dvanalysisengineservice.dao.common.MapperFactory;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.mapper.AlarmCompressMapper;
import com.huawei.i2000.dvanalysisengineservice.model.Event;
import com.huawei.i2000.dvanalysisengineservice.model.OperateRecord;
import com.huawei.i2000.dvanalysisengineservice.model.RepairAdvice;
import com.huawei.i2000.dvanalysisengineservice.util.exception.ExceptionInternationalConstant;
import com.huawei.i2000.dvanalysisengineservice.util.exception.ExceptionUtil;

import javafx.util.Pair;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.exceptions.PersistenceException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 告警分析详情页 数据库交互类
 *
 * <AUTHOR>
 * @since 2021-05-18
 */
@Component
public class AlarmCompressDao {
    private static final OssLog LOGGER = OssLogFactory.getLogger(AlarmCompressDao.class);

    private static final int QUERY_LIST_SIZE = 500;

    @Autowired
    MapperFactory mapperFactory;

    /**
     * AlarmCompressMapper
     *
     * @param taskId taskId
     * @return list
     */
    public List<AlarmIncident> queryUndeclineEvents(int taskId) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        List<AlarmIncident> incidents = new ArrayList<>();
        try {
            incidents = mapper.queryUndeclineEvents(SynAnalysisResultConstant.AnalysisResultTableName.ALARM_ANALYSIS_INCIDENT_TABLE_NAME + taskId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] queryUndeclineEvents failed! e=", e);
        }
        return incidents;
    }

    /**
     * 更新清除告警
     *
     * @param taskId 任务id
     * @param list 清除告警告警
     */
    public void updateClearAlarm(int taskId, List<Event> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        // 对待清理的event根据类型和流水号排序，让其顺序更新，防止出现数据库死锁问题（鹰眼已扫出该问题）
        // begin -- end执行多条sql时，会对其加事务，如果更新行号顺序不一致，就会出现循环等待
        list.sort((o1, o2) -> {
            if (!Objects.equals(o1.getType(), o2.getType())) {
                return o1.getType() - o2.getType();
            }
            return Long.compare(o1.getCsn(), o2.getCsn());
        });

        List<List<Event>> clearLists = ListUtils.partition(list, QUERY_LIST_SIZE);
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            for (List<Event> clears : clearLists) {
                mapper.updateClearAlarm(SynAnalysisResultConstant.AnalysisResultTableName.ALARM_ANALYSIS_RESULT_TABLE_NAME + taskId, clears);
            }
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] updateClearAlarm failed! e=", e);
        }
    }

    /**
     * 根据流水号查询告警
     *
     * @param taskId taskId
     * @param events 流水号
     * @return list 告警
     */
    public List<Event> queryAlarmByCsn(int taskId, List<Event> events) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        List<Event> alarms = new ArrayList<>();
        try {
            Map<Integer, List<Event>> eventMap = events.stream().collect(Collectors.groupingBy(Event::getType));
            String tableName = SynAnalysisResultConstant.AnalysisResultTableName.ALARM_ANALYSIS_RESULT_TABLE_NAME + taskId;
            for (Map.Entry<Integer, List<Event>> entry : eventMap.entrySet()) {
                List<Integer> alarmSeriesNumbers = entry.getValue().stream().map(Event::getCsn).collect(Collectors.toList());
                alarms.addAll(mapper.queryAlarmByCsn(tableName, entry.getKey(), alarmSeriesNumbers));
            }
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] queryAlarmByCsn failed! e=", e);
        }
        return alarms;
    }

    /**
     * 根据id查询事件
     *
     * @param incidentIds 事件id
     * @param taskId taskId
     * @return list 事件
     */
    public List<AlarmIncident> queryIncidentById(int taskId, List<String> incidentIds) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        List<AlarmIncident> incidents = new ArrayList<>();
        try {
            incidents = mapper.queryIncidentById(
                SynAnalysisResultConstant.AnalysisResultTableName.ALARM_ANALYSIS_INCIDENT_TABLE_NAME + taskId,
                incidentIds);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] queryIncidentById failed! e=", e);
        }
        return incidents;
    }

    public List<AlarmIncident> queryUnOpenedIncidentById(int taskId) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        List<AlarmIncident> incidents = new ArrayList<>();
        try {
            incidents = mapper.queryUnOpenedIncidentById(taskId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] queryUnOpenedIncidentById failed! e=", e);
        }
        return incidents;
    }

    public List<Map<String, Object>> getUnOpenedAlarmCountByIncident(int taskId) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        List<Map<String, Object>> incidents = new ArrayList<>();
        try {
            incidents = mapper.getUnOpenedAlarmCountByIncident(taskId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] getUnOpenedAlarmCountByIncident failed! e=", e);
        }
        return incidents;
    }

    /**
     * 插入告警
     *
     * @param taskId id
     * @param events 告警分析结果
     */
    public void insertAlarmResult(int taskId, List<Event> events) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            String tableName = SynAnalysisResultConstant.AnalysisResultTableName.ALARM_ANALYSIS_RESULT_TABLE_NAME + taskId;
            mapper.insertAlarmResult(tableName, events);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] insertAlarmResult failed! e=", e);
        }
    }

    /**
     * 查询事件里面的告警是否都结束了
     *
     * @param taskId id
     * @param incidentId 事件id
     * @return 事件
     */
    public List<Pair<String, Boolean>> getIncidentEndStatus(int taskId, List<String> incidentId){
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        List<Pair<String, Boolean>> incidents = new ArrayList<>();
        try {
            Set<String> incidentIdSet = new HashSet<>(incidentId);
            Set<String> notEndIncidentIds = mapper.getIncidentEndStatus(taskId, incidentIdSet);
            incidentIdSet.forEach(s -> incidents.add(new Pair<>(s, !notEndIncidentIds.contains(s))));
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] getIncidentEndStatus failed! e=", e);
        }
        return incidents;
    }

    /**
     * 插入或更新事件信息
     *
     * @param taskId 任务ID
     * @param alarmIncidents 事件信息
     */
    public void insertOrUpdateCompressAlarmIncident(int taskId, List<AlarmIncident> alarmIncidents) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        if (CollectionUtils.isEmpty(alarmIncidents)) {
            return;
        }
        alarmIncidents.sort((o1, o2) -> StringUtils.compare(o1.getIncidentId(), o2.getIncidentId()));
        try {
            mapper.insertOrUpdateCompressAlarmIncident(
                SynAnalysisResultConstant.AnalysisResultTableName.ALARM_ANALYSIS_INCIDENT_TABLE_NAME + taskId,
                alarmIncidents);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] insertOrUpdateCompressAlarmIncident failed! e=", e);
        }
    }

    /**
     * 查询未结束的告警
     *
     * @param taskId 任务ID
     * @param pageSize pageSize
     * @param offSet offSet
     * @return 告警流水
     */
    public List<Integer> queryCompressAlarmNotEnded(int taskId, int pageSize, int offSet) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        List<Integer> ids = new ArrayList<>();
        try {
            ids = mapper.queryCompressAlarmNotEnded(
                SynAnalysisResultConstant.AnalysisResultTableName.ALARM_ANALYSIS_RESULT_TABLE_NAME + taskId, pageSize,
                offSet);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] queryCompressAlarmNotEnded failed! e=", e);
        }
        return ids;
    }

    /**
     * 查询告警是否在数据库中
     *
     * @param taskId 任务ID
     * @param csns 流水号
     * @return 告警流水
     */
    public List<Integer> queryAlarmInTable(int taskId, List<Integer> csns) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        List<Integer> ids = new ArrayList<>();
        List<List<Integer>> alarmSeriesNumber = ListUtils.partition(csns, QUERY_LIST_SIZE);
        try {
            for (List<Integer> csn : alarmSeriesNumber) {
                ids.addAll(mapper.queryAlarmInTable(
                    SynAnalysisResultConstant.AnalysisResultTableName.ALARM_ANALYSIS_RESULT_TABLE_NAME + taskId, csn));
            }
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] queryAlarmInTable failed! e=", e);
        }
        return ids;
    }

    /**
     * 把所有事件变成衰退
     *
     * @param taskId taskId
     */
    public void updateIncidentDecline(int taskId) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            mapper.updateIncidentDecline(
                SynAnalysisResultConstant.AnalysisResultTableName.ALARM_ANALYSIS_INCIDENT_TABLE_NAME + taskId);
        } catch (DataAccessException e) {
            LOGGER.error("[AlarmCompressDao] updateIncidentDecline failed! e=", e);
        }
    }

    /**
     * 更新衰退且告警都结束的事件的endtime
     *
     * @param taskId id
     * @param time 时间
     * @param incidentIds 事件id
     */
    public void updateEndIncidentEndTime(int taskId, long time, List<String> incidentIds){
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        int result = 0;
        try {
            result = mapper.updateEndIncidentEndTime(taskId, time, incidentIds);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] updateEndIncidentEndTime failed! e=", e);
        }
        LOGGER.info("[AlarmCompressDao] updateEndIncidentEndTime result={}", result);
    }

    /**
     * 查询衰退且告警都结束的事件
     *
     * @param taskId id
     * @return 事件
     */
    public List<AlarmIncident> selectEndIncidentEndTime(int taskId) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        List<AlarmIncident> incidents = new ArrayList<>();
        try {
            incidents = mapper.selectEndIncidentEndTime(taskId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] selectEndIncidentEndTime failed! e=", e);
        }
        return incidents;
    }

    /**
     * 查询告警表的数量
     *
     * @param taskId id
     * @return 数量
     */
    public int getAllAlarmCountByTask(int taskId) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        int result = 0;
        try {
            result = mapper.getAllAlarmCountByTask(taskId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] getAllAlarmCountByTask failed! e=", e);
        }
        return result;
    }

    /**
     * 查询结束事件里的告警数量
     *
     * @param taskId id
     * @return 事件和数量
     */
    public List<Map<String, Object>> getAlarmCountGroupByIncident(int taskId){
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        List<Map<String, Object>> incidents = new ArrayList<>();
        try {
            incidents = mapper.getAlarmCountGroupByIncident(taskId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] getAlarmCountGroupByIncident failed! e=", e);
        }
        return incidents;
    }

    /**
     * 根据事件id删除事件
     *
     * @param taskId id
     * @param incidentIds 事件id
     */
    public void deleteIncidentById(int taskId, List<String> incidentIds) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        int result = 0;
        try {
            result = mapper.deleteIncidentById(taskId, incidentIds);
            mapper.deleteOperateRecordByIncidentId(taskId, incidentIds);
            mapper.deleteRepairAdviceByIncidentId(taskId, incidentIds);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] deleteIncidentById failed! e=", e);
        }
        LOGGER.info("[AlarmCompressDao] deleteIncidentById result={}", result);
    }

    /**
     * 根据事件id删除告警
     *
     * @param taskId id
     * @param incidentIds 事件id
     */
    public void deleteAlarmById(int taskId, List<String> incidentIds) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        int result = 0;
        try {
            result = mapper.deleteAlarmById(taskId, incidentIds);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] deleteAlarmById failed! e=", e);
        }
        LOGGER.info("[AlarmCompressDao] deleteAlarmById result={}", result);
    }

    /**
     * 删除告警表数据
     *
     * @param taskId id
     * @param nums 指定数量
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void deleteAlarmByEndTime(int taskId, int nums) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        int result = 0;
        try {
            result = mapper.deleteAlarmByEndTime(taskId, nums);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] deleteAlarmByEndTime failed! e=", e);
        }
        LOGGER.debug("[AlarmCompressDao] deleteAlarmByEndTime result={}", result);
    }

    public void upgradeTable(Integer taskId) throws ServiceException {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            mapper.upgradeTable(taskId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] upgradeEventId failed! e=", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    public void addAlarmField(Integer taskId) throws ServiceException {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            mapper.addAlarmField(taskId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] addAlarmField failed! e=", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    public long getMaxEventId(Integer taskId) throws ServiceException {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            Long value = mapper.getMaxEventId(taskId);
            return value == null ? 0 : value;
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] getMaxEventId failed! e=", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    public void createOperateRecordTable(Integer taskId) throws ServiceException {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            mapper.createOperateRecordTable(taskId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] create Operate Record Table failed! e:", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    public void createRepairAdviceTable(int taskId) throws ServiceException {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            mapper.createRepairAdviceTable(taskId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] create Repair Advice Table failed! e:", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    public List<AlarmIncident> getDiagnosisIncident(int taskId) throws ServiceException {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            return mapper.getDiagnosisIncident(taskId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] getDiagnosisIncident failed! e:", e);
            return Collections.emptyList();
        }
    }

    public void updateDiagnosisState(int taskId, Set<String> ids, Long startTime, int state, Boolean needDiagnosis) throws ServiceException {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            mapper.updateDiagnosisState(taskId, ids, startTime, state, needDiagnosis);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] updateDiagnosisState failed! e:", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    public List<Event> getEventsByIncidentIds(int taskId, List<String> ids, int pageNumber, int pageSize) throws ServiceException {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            return mapper.getEventsByIncidentIds(taskId, ids, pageNumber, pageSize);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] getEventsByIncidentIds failed! e:", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    public void insertOperateRecord(int taskId, List<OperateRecord> records) throws ServiceException {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        List<List<OperateRecord>> lists = ListUtils.partition(records, QUERY_LIST_SIZE);
        try {
            for (List<OperateRecord> list : lists) {
                mapper.insertOperateRecord(taskId, list);
            }
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] insertOperateRecord failed! e:", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    public void insertRepairAdvice(int taskId, List<RepairAdvice> advices) throws ServiceException {
        if (CollectionUtils.isEmpty(advices)) {
            return;
        }
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        List<List<RepairAdvice>> lists = ListUtils.partition(advices, QUERY_LIST_SIZE);
        try {
            for (List<RepairAdvice> list : lists) {
                mapper.insertRepairAdvice(taskId, list);
            }
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] insertRepairAdvice failed! e:", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    public void handleDiagnosisTimeout(Integer taskId, long startTime) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            mapper.handleDiagnosisTimeout(taskId, startTime);
        } catch (Exception e) {
            LOGGER.error("handleDiagnosisTimeout error", e);
        }
    }

    public void updateDiagnosisResult(int taskId, List<AlarmIncident> incidents) throws ServiceException {
        if (CollectionUtils.isEmpty(incidents)) {
            return;
        }
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            mapper.updateDiagnosisResult(taskId, incidents);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] updateDiagnosisResult failed! e:", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    public List<Event> getNoAggrAlarmEvents(int taskId) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            return mapper.getNoAggrAlarmEvents(taskId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] getNoAggrAlarmEvents failed! e:", e);
            return Collections.emptyList();
        }
    }

    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void updateAggrStatus(int taskId, List<Event> events) {
        if (CollectionUtils.isEmpty(events)) {
            return;
        }
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            List<Long> ids = events.stream().map(Event::getId).sorted().collect(Collectors.toList());
            for (List<Long> idList : ListUtils.partition(ids, QUERY_LIST_SIZE)) {
                mapper.updateAggrStatus(taskId, idList);
            }
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] updateAggrStatus failed! e:", e);
        }
    }

    public List<Event> getNoAggrAlarmEventsByIncidentId(int taskId, List<String> incidentIds) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        try {
            return mapper.getNoAggrAlarmEventsByIncidentId(taskId, incidentIds);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] getNoAggrAlarmEventsByIncidentId failed! e:", e);
            return Collections.emptyList();
        }
    }

    public List<Map<String, Object>> getUnClearAlarmCountByIncidentIds(int taskId, List<String> incidentIds) {
        AlarmCompressMapper mapper = mapperFactory.getMapper(AlarmCompressMapper.class);
        List<Map<String, Object>> incidents = new ArrayList<>();
        try {
            incidents = mapper.getUnClearAlarmCountByIncidentIds(taskId, incidentIds);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AlarmCompressDao] getUnClearAlarmCountByIncidentIds failed! e=", e);
        }
        return incidents;
    }
}
