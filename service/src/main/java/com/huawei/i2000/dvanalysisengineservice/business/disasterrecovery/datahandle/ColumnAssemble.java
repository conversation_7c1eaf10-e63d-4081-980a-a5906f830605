/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.datahandle;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.InternationalConstant;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.dto.KpiColumnTitleMap;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.dto.KpiTransportColumn;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.util.KpiColumnConstant;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.util.TaskShowConstant;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.util.TaskSwitchConstant;
import com.huawei.i2000.dvanalysisengineservice.model.AuxiliaryItem;
import com.huawei.i2000.dvanalysisengineservice.model.DynamicColumn;
import com.huawei.i2000.dvanalysisengineservice.model.DynamicColumnTitle;
import com.huawei.i2000.dvanalysisengineservice.model.FlowRecord;
import com.huawei.i2000.dvanalysisengineservice.model.KPIDetailsData;
import com.huawei.i2000.dvanalysisengineservice.model.MemberDetailsData;
import com.huawei.i2000.dvanalysisengineservice.model.Paging;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;
import com.huawei.security.validator.util.jackson.JSONException;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 处理容灾区域列名格式
 *
 * <AUTHOR>
 * @since 2022/8/10
 */
public class ColumnAssemble {

    private static final OssLog LOGGER = OssLogFactory.getLogger(ColumnAssemble.class);

    private static final String UNKNOWN_STATUS = "UNKNOWN";

    private static final String CHECK_STATUS = "CHECKING";

    /**
     * 处理容灾区域列名格式
     *
     * @param auxiliaryColumns 容灾区域数据
     * @return JSONArray 处理后的列名格式
     */
    public static JSONArray dealColumns(List<AuxiliaryItem> auxiliaryColumns) {
        JSONArray columns = new JSONArray();
        if (CollectionUtils.isEmpty(auxiliaryColumns)) {
            LOGGER.debug("[dealColumns] auxiliaryColumns is empty");
            return columns;
        }
        auxiliaryColumns.forEach(auxiliaryItem -> {
            JSONObject column = new JSONObject();
            column.put(TaskShowConstant.COMMON_KEY, auxiliaryItem.getAuxiliaryKey());
            column.put(TaskShowConstant.DISPLAY_NAME_ZH, auxiliaryItem.getDisplayNameZH());
            column.put(TaskShowConstant.DISPLAY_NAME_EN, auxiliaryItem.getDisplayNameEN());
            columns.add(column);
        });
        return columns;
    }

    /**
     * 处理容灾区域辅助项key-value转换
     *
     * @param detailsData 容灾区域监控数据
     * @param items 容灾区域成员辅助信息值
     * @param auxiliaryColumns 辅助项列名
     * @return JSONObject
     */
    public static JSONObject dealMemberData(MemberDetailsData detailsData, List<AuxiliaryItem> items, List<AuxiliaryItem> auxiliaryColumns) {
        JSONObject member = new JSONObject();
        member.put(TaskShowConstant.GROUP_MEMBER_ID, detailsData.getGroupMemberID());
        member.put(TaskShowConstant.DN_MARK, detailsData.getDN());
        member.put(TaskShowConstant.MEMBER_NAME, detailsData.getDNName());
        member.put(TaskShowConstant.MEMBER_STATUS,
            StringUtils.isEmpty(detailsData.getStatus()) ? UNKNOWN_STATUS : detailsData.getStatus());
        member.put(TaskShowConstant.MEMBER_KPI_STATUS, dealMemberStatus(detailsData.getKPIStatus()));
        member.put(TaskShowConstant.MEMBER_ASSOCIATION_KPI_STATUS, dealMemberStatus(detailsData.getAssociationKpiStatus()));
        member.put(TaskShowConstant.MEMBER_ALARM_NUMBER, dealMemberStatus(detailsData.getAlarmStatus()));
        member.put(TaskShowConstant.MEMBER_MONITOR_FLOW_STATUS, dealMemberStatus(detailsData.getFlowStatus()));
        auxiliaryColumns.forEach(auxiliary -> {
            member.put(auxiliary.getAuxiliaryKey(), TaskSwitchConstant.DEFAULT_VALUE);
            items.stream().filter(item -> item.getAuxiliaryKey().equals(auxiliary.getAuxiliaryKey()))
                .forEach(showItem -> member.put(auxiliary.getAuxiliaryKey(), showItem.getAuxiliaryValue()));
        });
        return member;
    }

    private static String dealMemberStatus(String status) {
        if (StringUtils.isEmpty(status)) {
            return TaskSwitchConstant.DEFAULT_VALUE;
        }
        if (CHECK_STATUS.equals(status)) {
            return UNKNOWN_STATUS;
        }
        return status.trim();
    }

    /**
     * 设置传输格式的kpi信息
     *
     * @param kpiDetailsDataList kpi具体信息
     * @return List<DynamicColumnTitle>
     */
    public static List<KpiTransportColumn> setTransportKpiColumn(List<KPIDetailsData> kpiDetailsDataList) {
        List<KpiTransportColumn> transportColumns = new ArrayList<>();

        kpiDetailsDataList.forEach(kpiDetailsData -> {
            KpiTransportColumn kpiColumn = new KpiTransportColumn();
            kpiColumn.setIndicatorKey(kpiDetailsData.getMeasTypeKey());
            // 从数据库取出的kpi值，可能会带衍生值，需要取空格前面部分值
            String kpiValue = kpiDetailsData.getLastPeriodKPIValue();
            if (kpiValue.contains(" ")) {
                kpiColumn.setIndicatorValue(kpiValue.substring(0, kpiValue.indexOf(" ")));
            } else {
                kpiColumn.setIndicatorValue(kpiValue);
            }
            kpiColumn.setIndicatorTime(kpiDetailsData.getKpiTime());
            kpiColumn.setIndicatorDerivedValue(kpiDetailsData.getLastPeriodDerivedKPIValue());
            kpiColumn.setIndicatorDerivedTime(kpiDetailsData.getDerivedKPITime());

            transportColumns.add(kpiColumn);

            String dynamicColumns = kpiDetailsData.getDynamicColumns();
            List<DynamicColumn> columns = JSONArray.parseArray(dynamicColumns, DynamicColumn.class);

            columns.forEach(column -> {
                KpiTransportColumn kpiDynamicColumn = new KpiTransportColumn();
                kpiDynamicColumn.setIndicatorKey(column.getColumnName());
                kpiDynamicColumn.setIndicatorValue(column.getColumnValue());
                transportColumns.add(kpiDynamicColumn);
            });
        });

        return transportColumns;
    }

    /**
     * 设置KPI固定表头，成员名称，主指标，最近一个周期指标值，阈值
     *
     * @return List<DynamicColumnTitle>
     */
    public static List<DynamicColumnTitle> setupKpiFixedTile() {
        List<DynamicColumnTitle> fixedTitle = new ArrayList<>();
        DynamicColumnTitle memberName = new DynamicColumnTitle();
        memberName.setColumnName(KpiColumnConstant.MEMBER_NAME);
        memberName.setColumnDisplayCN(ResourceUtil.getMessage(InternationalConstant.KPI_COLUMN_FIXED_HEADER_DNNAME, ContextUtils.getContext().getLocale()));
        memberName.setColumnDisplayEN(KpiColumnConstant.MEMBER_NAME_COLUMN_NAME_EN);

        DynamicColumnTitle indicatorNameColumn = new DynamicColumnTitle();
        indicatorNameColumn.setColumnName(KpiColumnConstant.INDICATOR_NAME_COLUMN_KEY);
        indicatorNameColumn.setColumnDisplayCN(ResourceUtil.getMessage(InternationalConstant.KPI_COLUMN_FIXED_HEADER_TITLE, ContextUtils.getContext().getLocale()));
        indicatorNameColumn.setColumnDisplayEN(KpiColumnConstant.INDICATOR_NAME_COLUMN_NAME_EN);

        DynamicColumnTitle indicatorCycleColumn = new DynamicColumnTitle();
        indicatorCycleColumn.setColumnName(KpiColumnConstant.INDICATOR_CYCLE_COLUMN_KEY);
        indicatorCycleColumn.setColumnDisplayCN(ResourceUtil.getMessage(InternationalConstant.KPI_COLUMN_FIXED_HEADER_CYCLE_TITLE, ContextUtils.getContext().getLocale()));
        indicatorCycleColumn.setColumnDisplayEN(KpiColumnConstant.INDICATOR_CYCLE_COLUMN_NAME_EN);

        DynamicColumnTitle indicatorThresholdColumn = new DynamicColumnTitle();
        indicatorThresholdColumn.setColumnName(KpiColumnConstant.INDICATOR_THRESHOLD_COLUMN_KEY);
        indicatorThresholdColumn.setColumnDisplayCN(ResourceUtil.getMessage(InternationalConstant.KPI_COLUMN_FIXED_HEADER_THRESHOLD_TITLE, ContextUtils.getContext().getLocale()));
        indicatorThresholdColumn.setColumnDisplayEN(KpiColumnConstant.INDICATOR_THRESHOLD_COLUMN_NAME_EN);

        fixedTitle.add(memberName);
        fixedTitle.add(indicatorNameColumn);
        fixedTitle.add(indicatorCycleColumn);
        fixedTitle.add(indicatorThresholdColumn);
        return fixedTitle;
    }

    /**
     * 处理kpi监控信息
     *
     * @param pageDisplayColumns kpi表格数据
     * @param pageDisplayRowValue row数据
     * @param paging 分页
     * @return JSONArray
     */
    public static JSONObject setKpiPageDisplayTable(List<DynamicColumnTitle> pageDisplayColumns, HashMap<String, List<KPIDetailsData>> pageDisplayRowValue, Paging paging) {
        JSONObject displayTable = new JSONObject();
        displayTable.put(KpiColumnConstant.KPI_PAGE_DISPLAY_COLUMN_NAME, pageDisplayColumns);

        JSONArray columnValues = new JSONArray();

        pageDisplayRowValue.forEach((dnName, rowValue) -> {
            columnValues.addAll(setTableValue(pageDisplayColumns, rowValue));
        });

        JSONArray displayRows = getDisplayPagingRows(columnValues, paging);
        displayTable.put(TaskShowConstant.COLUMN_VALUE_COUNT, columnValues.size());
        displayTable.put(KpiColumnConstant.KPI_PAGE_DISPLAY_COLUMN_VALUE, displayRows);
        return displayTable;
    }

    private static JSONArray setTableValue(List<DynamicColumnTitle> pageDisplayColumns, List<KPIDetailsData> memberKpiDetailsData) {
        JSONArray memberTotalData = new JSONArray();
        // 前四列为固定列
        String dnName = pageDisplayColumns.get(KpiColumnTitleMap.DN_NAME.getTitleOrder()).getColumnName();
        String mainIndicate = pageDisplayColumns.get(KpiColumnTitleMap.INDICATOR_NAME.getTitleOrder()).getColumnName();
        String indicateCycle = pageDisplayColumns.get(KpiColumnTitleMap.INDICATOR_CYCLE.getTitleOrder())
            .getColumnName();
        String indicatorThreshold = pageDisplayColumns.get(KpiColumnTitleMap.INDICATOR_THRESHOLD.getTitleOrder())
            .getColumnName();
        String indicatorID = KpiColumnConstant.INDICATOR_ID;

        memberKpiDetailsData.forEach(kpiDetailsData -> {
            JSONObject rowValue = new JSONObject();
            rowValue.put(dnName, kpiDetailsData.getDNName());
            rowValue.put(mainIndicate, kpiDetailsData.getKpiName());
            rowValue.put(indicateCycle, kpiDetailsData.getLastPeriodKPIValue());
            rowValue.put(indicatorID, kpiDetailsData.getIndicatorID());
            rowValue.put(indicatorThreshold, kpiDetailsData.getKpiThresholdValue());
            rowValue.put(TaskShowConstant.MAIN_KPI_STATUS, kpiDetailsData.getStatus());

            String dynamicColumns = kpiDetailsData.getDynamicColumns();
            List<DynamicColumn> columns = JSONArray.parseArray(dynamicColumns, DynamicColumn.class);

            for (int i = KpiColumnTitleMap.DYNAMIC_COLUMN.getTitleOrder(); i < pageDisplayColumns.size(); i++) {
                String columnPageName = pageDisplayColumns.get(i).getColumnName();
                rowValue.put(columnPageName, TaskSwitchConstant.DEFAULT_VALUE);
                for (DynamicColumn column : columns) {
                    if (columnPageName.equals(column.getColumnName())) {
                        rowValue.put(column.getColumnName(), column.getColumnValue());
                    }
                }
            }
            memberTotalData.add(rowValue);
        });
        return memberTotalData;
    }

    /**
     * 处理核心进程监控的数据
     *
     * @param monitorMap 容灾区域数组
     * @param paging 分页
     * @param permissionDns 有权限的网元
     * @param permissionIps 有权限的主机
     * @return JSONObject
     */
    public static JSONObject setMonitorFlowResult(HashMap<FlowRecord, String> monitorMap, Paging paging,
        List<String> permissionDns, List<String> permissionIps) {
        if (monitorMap.isEmpty()) {
            LOGGER.info("[setMonitorFlowResult] query monitorFlowResult is empty");
            return null;
        }

        SecureRandom random;
        try {
            random = SecureRandom.getInstanceStrong();
        } catch (NoSuchAlgorithmException e) {
            LOGGER.error("[setMonitorFlowResult] get SecureRandom error", e);
            return null;
        }

        LOGGER.info("start setMonitorFlowResult.");
        // 随机选择一个成员的监控结果作为列名
        String[] monitorValues = monitorMap.values().toArray(new String[0]);
        String selectMonitor = monitorValues[random.nextInt(monitorValues.length)];

        JSONArray monitorArray = JSONArray.parseArray(selectMonitor);
        
        if (monitorArray == null || monitorArray.isEmpty()) {
            LOGGER.debug("[setMonitorFlowResult] parse selectMonitor error, please check the flow output result");
            return null;
        }
        JSONObject receiveMonitor = (JSONObject) monitorArray.get(0);

        // 设置表类型和名称
        String dataViewType = receiveMonitor.getString(TaskShowConstant.DATA_VIEW_TYPE);
        String dataViewName = receiveMonitor.getString(TaskShowConstant.DATA_VIEW_NAME);

        // 设置表头列名
        JSONArray displayColumnName = new JSONArray();
        JSONObject dnCol = new JSONObject();
        dnCol.put(TaskShowConstant.COMMON_KEY, TaskShowConstant.MEMBER_NAME);
        // 国际化
        dnCol.put(TaskShowConstant.COMMON_NAME, ResourceUtil.getMessage(InternationalConstant.KPI_COLUMN_FIXED_HEADER_DNNAME, ContextUtils.getContext().getLocale()));
        displayColumnName.add(dnCol);
        JSONArray receiveColumnName = receiveMonitor.getJSONArray(TaskShowConstant.COLUMN_NAME);
        displayColumnName.addAll(receiveColumnName);

        JSONArray displayColumnValue = new JSONArray();
        // 循环所有columnValue，添加到当前的结果中
        for (Map.Entry<FlowRecord, String> entry : monitorMap.entrySet()) {
            String monitorResult = entry.getValue();
            FlowRecord flowRecord = entry.getKey();
            // 添加有网元权限的记录
            if (permissionDns != null && permissionDns.contains(flowRecord.getDn())) {
                displayColumnValue.addAll(getColumnValue(flowRecord, monitorResult, permissionIps));
            } else if (permissionDns == null) {
                displayColumnValue.addAll(getColumnValue(flowRecord, monitorResult, permissionIps));
            }
        }
        LOGGER.info("setMonitorFlowResult end.");
        // 分页获取
        JSONArray displayRows = getDisplayPagingRows(displayColumnValue, paging);

        return loadDisplayMonitorFlowResult(dataViewType, dataViewName, displayColumnName, displayRows, displayColumnValue.size());
    }

    private static JSONArray getColumnValue(FlowRecord flowRecord, String monitorFlowResult,  List<String> permissionIps) {

        JSONArray filterColumnValue = new JSONArray();
        try {
            JSONArray monitorArray = JSONArray.parseArray(monitorFlowResult);
            if (monitorArray.isEmpty()) {
                LOGGER.error("[getColumnValue] the parse array monitorFlowResult is empty.");
                return filterColumnValue;
            }
            JSONObject receiveMonitor = (JSONObject) monitorArray.get(0);
            JSONArray receiveColumnValue = receiveMonitor.getJSONArray(TaskShowConstant.COLUMN_VALUE);

            // 如果配置了主机，则需要过滤主机权限信息
            for (int i = 0; i < receiveColumnValue.size(); i++) {
                JSONObject columnValue = receiveColumnValue.getJSONObject(i);
                columnValue.put(TaskShowConstant.MEMBER_NAME, flowRecord.getDnName());
                // 如果有hostIP字段，则进行过滤
                if (StringUtils.isNotEmpty(columnValue.getString(TaskShowConstant.HOST_IP))) {
                    if (permissionIps.contains(columnValue.getString(TaskShowConstant.HOST_IP))) {
                        filterColumnValue.add(columnValue);
                    }
                } else {
                    filterColumnValue.add(receiveColumnValue.getJSONObject(i));
                }
            }
        } catch (JSONException e) {
            LOGGER.error("[getColumnValue] parse column value error.");
        }
        return filterColumnValue;
    }

    /**
     * 获取分页数据
     *
     * @param displayColumnValue 展示数据
     * @param paging 分页
     * @return JSONObject
     */
    public static JSONArray getDisplayPagingRows(JSONArray displayColumnValue, Paging paging) {
        JSONArray displayRows = new JSONArray();
        int offset = paging.getOffset();
        int columnSize = displayColumnValue.size();
        int pageSize = paging.getPageSize();
        for (int i = offset; i < offset + pageSize; i++) {
            if (i == columnSize) {
                break;
            }
            displayRows.add(displayColumnValue.get(i));
        }
        return displayRows;
    }

    private static JSONObject loadDisplayMonitorFlowResult(String dataViewType, String dataViewName, JSONArray displayColumnName, JSONArray displayColumnValue, int totalSize) {
        JSONObject displayMonitorFlowResult = new JSONObject();
        displayMonitorFlowResult.put(TaskShowConstant.DATA_VIEW_TYPE, dataViewType);
        displayMonitorFlowResult.put(TaskShowConstant.DATA_VIEW_NAME, dataViewName);
        displayMonitorFlowResult.put(TaskShowConstant.COLUMN_NAME, displayColumnName);
        displayMonitorFlowResult.put(TaskShowConstant.COLUMN_VALUE, displayColumnValue);
        displayMonitorFlowResult.put(TaskShowConstant.COLUMN_VALUE_COUNT, totalSize);
        return displayMonitorFlowResult;
    }

}
