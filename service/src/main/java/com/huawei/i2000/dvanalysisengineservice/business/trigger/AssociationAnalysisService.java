/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.trigger;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.cbb.fm.model.QSortField;
import com.huawei.i2000.cbb.sm.model.LogResult;
import com.huawei.i2000.cbb.sm.model.LogSeverity;
import com.huawei.i2000.dvanalysisengineservice.business.common.ConfigurationUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.LocationTriggerTreeNodeCalculation;
import com.huawei.i2000.dvanalysisengineservice.business.common.TimeUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.AssociationAnalysisAlgorithmFeatureType;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.AssociationAnalysisNodeStatus;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.AssociationAnalysisType;
import com.huawei.i2000.dvanalysisengineservice.business.datasource.DataSourceServiceImpl;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.FlowClient;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.model.flow.BatchCreateTaskResponse;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.model.flow.StartFlowRequest;
import com.huawei.i2000.dvanalysisengineservice.business.knowledgerepository.common.ResponseResultUtil;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.TaskAnalysisResultServiceImpl;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.constant.SynAnalysisResultConstant;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.MoObject;
import com.huawei.i2000.dvanalysisengineservice.business.taskcommon.TaskUtil;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.CustomRejectedExecutionHandler;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.PqlsEntity;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.AlarmClient;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.AlarmQueryParam;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.QFilterElement;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.QueryContext;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.QueryTaskByPageRequest;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ScrollQueryResult;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.immediate.ImmediateAlarm;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.AssociationFlowDn;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.AssociationFlowParamOne;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.FeedbackInfoEntity;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.FeedbackInfoList;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.NodeLocation;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.PreExecuteFlowActionType;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.PreExecutionNode;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.RecommendFeedbackEntity;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.RecommendFeedbackExportEntity;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.RecommendRecordEntity;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.RecommendRecordExportEntity;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TemplateTreeModel;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TemplateTreeNode;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TemplateTreePath;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TriggerCountModel;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TriggerNodeAlgorithmParams;
import com.huawei.i2000.dvanalysisengineservice.dao.common.MapperFactory;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.IntelligentRecommendDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.CustomIndicatorSqlExcutor;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.DataSourceDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskIndicatorDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.handlerimpl.TaskManageDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.mapper.TaskManageMapper;
import com.huawei.i2000.dvanalysisengineservice.dao.trigger.mapper.AssociationAnalysisDao;
import com.huawei.i2000.dvanalysisengineservice.model.AlarmCsnQuery;
import com.huawei.i2000.dvanalysisengineservice.model.AlarmPoint;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTask;
import com.huawei.i2000.dvanalysisengineservice.model.AnomalyRate;
import com.huawei.i2000.dvanalysisengineservice.model.AssocationAnalysisTaskQuery;
import com.huawei.i2000.dvanalysisengineservice.model.AssociationCurveQuery;
import com.huawei.i2000.dvanalysisengineservice.model.ColumnMapping;
import com.huawei.i2000.dvanalysisengineservice.model.CustomIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.CustomIndicatorList;
import com.huawei.i2000.dvanalysisengineservice.model.CustomIndicatorQuery;
import com.huawei.i2000.dvanalysisengineservice.model.DataSource;
import com.huawei.i2000.dvanalysisengineservice.model.DataSourceQueryParam;
import com.huawei.i2000.dvanalysisengineservice.model.ExecutePreNodeParam;
import com.huawei.i2000.dvanalysisengineservice.model.FeedbackExportParam;
import com.huawei.i2000.dvanalysisengineservice.model.FeedbackParam;
import com.huawei.i2000.dvanalysisengineservice.model.FeedbackParams;
import com.huawei.i2000.dvanalysisengineservice.model.FlowHost;
import com.huawei.i2000.dvanalysisengineservice.model.LogTemplate;
import com.huawei.i2000.dvanalysisengineservice.model.LogTemplateQuery;
import com.huawei.i2000.dvanalysisengineservice.model.LogTriggerTemplatePoint;
import com.huawei.i2000.dvanalysisengineservice.model.LogTriggerTemplatePointResult;
import com.huawei.i2000.dvanalysisengineservice.model.ModelParameter;
import com.huawei.i2000.dvanalysisengineservice.model.NodeIndicatorQuery;
import com.huawei.i2000.dvanalysisengineservice.model.Paging;
import com.huawei.i2000.dvanalysisengineservice.model.PrometheusData;
import com.huawei.i2000.dvanalysisengineservice.model.RecommendAccessRecord;
import com.huawei.i2000.dvanalysisengineservice.model.RecommendAccessRecordQueryParam;
import com.huawei.i2000.dvanalysisengineservice.model.RecommendAccessRecords;
import com.huawei.i2000.dvanalysisengineservice.model.RecommendFeedbackQueryParam;
import com.huawei.i2000.dvanalysisengineservice.model.ReportFlowParam;
import com.huawei.i2000.dvanalysisengineservice.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineservice.model.TaskIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerExecution;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerExecutionList;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerExecutionNode;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerIndicatorList;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;
import com.huawei.i2000.dvanalysisengineservice.util.EnvUtils;
import com.huawei.i2000.dvanalysisengineservice.util.JsonUtils;
import com.huawei.i2000.dvanalysisengineservice.util.cloudmonitor.PromethusClient;
import com.huawei.i2000.dvanalysisengineservice.util.exception.ExceptionInternationalConstant;
import com.huawei.i2000.dvanalysisengineservice.util.exception.ExceptionUtil;
import com.huawei.i2000.dvanalysisengineservice.util.operationlog.LogConstant;
import com.huawei.i2000.dvanalysisengineservice.util.operationlog.LogOperationModule;
import com.huawei.i2000.dvanalysisengineservice.util.operationlog.OperationLogUtil;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.exceptions.PersistenceException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

/**
 * 关联分析操作
 *
 * <AUTHOR>
 * @since 2022/3/22
 */
@Service
public class AssociationAnalysisService {

    private static final OssLog LOGGER = OssLogFactory.getLogger(AssociationAnalysisService.class);

    private static final int TIME_INDEX_ZERO = 0;

    private static final int TIME_INDEX_ONE = 1;

    private static final int TIME_INDEX_TWO = 2;

    private static final long MAX_TIME_ONE_MONTH = 2592000000L;

    private static final int LIST_SIZE = 200;

    private static final String EXCEPTION_TOTAL_INDICATOR_COUNT_OVER_MAX = "total indicator count over max";

    private static final String FLOW_PARAM_ID = "F_Id";

    private static final List<String> ALARM_RETURN_FIELDS = Collections.unmodifiableList(Arrays.asList("csn", "alarmName", "severity",
        "meName", "occurUtc", "clearUtc", "moi", "nativeMoDn", "meType"));

    private static final int ITERAT_SIZE = 1000;

    public static final String ASSOCIATED_KPI = "Associated_KPI";

    public static final String ASSOCIATED_ALARM = "Associated_ALARM";

    public static final String ASSOCIATED_LOG = "Associated_LOG";

    private static final int FAIL_CODE_FULL_QUEUE = -3;

    private static final int FAIL_CODE = -1;

    private static final int FAIL_CODE_NOT_SUCCESS = -2;

    private static final int SUCCESS = 0;

    private static final String FLOW_PARAM_LANGUAGE = "F_Language";

    private static final String GROUP_PUBLIC = "PUBLIC";

    private static final String CORRELATION_ANALYSIS = "CorrelationAnalysis";

    private static final String SPACE = " id:";

    private static final String STATUS_SUCCESS = "success";

    private static final String AUTO_EXEC_PARAMS = "autoExecParams";

    private final ThreadPoolExecutor customExecuteOnceExecutor = new ThreadPoolExecutor(5, 10, 10L, TimeUnit.MINUTES,
        new LinkedBlockingQueue<>(100), new CustomizableThreadFactory("customExecuteOnceExecutor-Thread-"), new CustomRejectedExecutionHandler());

    @Autowired
    TaskIndicatorDao taskIndicatorDao;

    @Autowired
    MapperFactory mapperFactory;

    @Autowired
    TriggerTaskHandler triggerTaskHandler;

    @Autowired
    TaskManageDao taskManageDao;

    @Autowired
    CustomIndicatorSqlExcutor customIndicatorSqlExcutor;

    @Autowired
    DataSourceDao dataSourceDao;

    @Autowired
    TaskAnalysisResultServiceImpl taskAnalysisResultService;

    @Autowired
    LogMatrixAdaptor logMatrixAdaptor;

    @Autowired
    AssociationAnalysisDao associationAnalysisDao;

    @Autowired
    IntelligentRecommendDao intelligentRecommendDao;

    @Transactional(rollbackFor = Exception.class)
    public String createAssocationNode(TriggerExecution task) throws ServiceException {
        // 模板任务 否则自定义任务
        if (task.getTemplateId() != null) {
            validTemplateUser(task.getTemplateId(), task);
            return executeTemplate(task);
        }
        // 入库
        task.setUpdateTime(System.currentTimeMillis());
        boolean newTask = false;
        if (task.getId() == null) {
            // 新建任务
            handleNewTask(task);
            newTask = true;
        }
        // 校验权限
        if (!newTask) {
            validUser(task.getId());
        }
        // 处理节点
        handleNode(task);
        // 处理图
        handleTree(task, newTask);
        // 设置完treeResult更新ExecutionResult
        String executionAddress = null;
        try {
            executionAddress = EnvUtils.getRunningNodeAddress();
        } catch (ServiceException e) {
            LOGGER.error("[AssociationAnalysisService] save running node address failed! ", e);
        }
        associationAnalysisDao.updateExecutionResult(task.getId(), task.getTreeResult(), task.getViewResult(),
            task.getUpdateTime(), AssociationAnalysisNodeStatus.ANALYZING, "", "", executionAddress, null);
        // 调用调度获取结果
        excuteOnce(task);
        return task.getId();
    }

    private void validTemplateUser(Integer templateId, TriggerExecution task) throws ServiceException {
        AnalysisTask analysisTask = taskManageDao.getAnalysisTaskById(templateId);
        if (analysisTask == null) {
            LOGGER.error("valid Template user query task is null, templateId = {}", templateId);
            throw new ServiceException("valid Template user query task is null.");
        }
        if (!ContextUtils.getContext().getAdmin()) {
            if (!ContextUtils.getContext().getUserId().equals(analysisTask.getUserId())) {
                LOGGER.error("[AssociationAnalysisService] vaildTemplateUser failed,task user={}",
                    analysisTask.getUserId());
                throw new ServiceException("AssociationAnalysisService vaildTemplateUser failed");
            }
        }
        if (analysisTask.getStartStatus() != 2) {
            LOGGER.error("valid template status error, id={}", templateId);
            throw new ServiceException("AssociationAnalysisService Template status error");
        }
        int totalCount = associationAnalysisDao.getTaskAllIndicatorCount();
        if (ConfigurationUtil.associationAnalysisIndicatorMax() < totalCount) {
            LOGGER.error(
                "[AssociationAnalysisService] validTemplateUser association analysis task indicator count over max!,count={}",
                totalCount);
            throw ExceptionUtil.createExceptionByMessage(EXCEPTION_TOTAL_INDICATOR_COUNT_OVER_MAX);
        }
        if (analysisTask.getTriggerTaskMessage() == null ||
            analysisTask.getTriggerTaskMessage().getRelationTree() == null) {
            LOGGER.error("[AssociationAnalysisService] template is null, taskId = {}", analysisTask.getTaskId());
            throw new ServiceException("association template is empty");
        }
        TemplateTreeModel templateTreeModel = JSONObject.parseObject(
            analysisTask.getTriggerTaskMessage().getRelationTree(), TemplateTreeModel.class);
        validNodeCountByType(templateTreeModel.getNodes(), task.getId());
    }

    private void validNodeCountByType(List<TemplateTreeNode> nodes, String id) throws ServiceException {
        TriggerCountModel countModel = new TriggerCountModel();
        Map<String, Long> countMap = nodes.stream().collect(Collectors.groupingBy(TemplateTreeNode::getType, Collectors.counting()));
        Map<String, Integer> map = associationAnalysisDao.getTaskNodeCountEveryType(id);
        if (countMap.containsKey(AssociationAnalysisType.DV_PM)) {
            int count = Math.toIntExact(countMap.get(AssociationAnalysisType.DV_PM));
            count += map.getOrDefault(AssociationAnalysisType.DV_PM, 0);
            countModel.setIndicatorNodeCount(count);
        }

        if (countMap.containsKey(AssociationAnalysisType.DV_ALARM)) {
            int count = Math.toIntExact(countMap.get(AssociationAnalysisType.DV_ALARM));
            count += map.getOrDefault(AssociationAnalysisType.DV_ALARM, 0);
            countModel.setAlarmNodeCount(count);
        }

        if (countMap.containsKey(AssociationAnalysisType.DV_LOG)) {
            int count = Math.toIntExact(countMap.get(AssociationAnalysisType.DV_LOG));
            count += map.getOrDefault(AssociationAnalysisType.DV_LOG, 0);
            countModel.setLogNodeCount(count);
        }
        countModel.setAllNodeCount(nodes.size());
        TriggerTaskUtil.validCount(countModel);
    }

    private void validUser(String executionId) throws ServiceException {
        // 新增节点
        if (!ContextUtils.getContext().getAdmin()) {
            List<TriggerExecution> excution = associationAnalysisDao.getAssocationTask(executionId, null, 0, 0, null, null,
                null, false);
            String user = excution.get(0).getCreateUser();
            String userId = ContextUtils.getContext().getUserId();
            if (!user.equals(userId)) {
                LOGGER.error("[AssociationAnalysisService] validUser failed, current user id={},task user={}", userId,
                    user);
                throw new ServiceException("AssociationAnalysisService validUser failed");
            }
        }
    }

    private void handleTree(TriggerExecution task, boolean newTask) {
        // 调整树图的结构
        LocationTriggerTreeNodeCalculation calculation = new LocationTriggerTreeNodeCalculation();
        if (newTask) {
            // 新增任务
            // 构建一个根节点和一个node
            TemplateTreeModel model = getTemplateModel(task);
            // 调用构造位置的方法设置坐标
            List<TemplateTreeNode> nodes = calculation.getTriggerNodeLocationWithLocating(model);
            model.setNodes(nodes);
            // setTreeResult
            task.setTreeResult(JSONObject.toJSONString(model));
        } else {
            // 增加节点
            // 根据id查询task对象
            List<TriggerExecution> excution = associationAnalysisDao.getAssocationTask(task.getId(), null, 0, 0, null, null,
                null, true);
            String tree = excution.get(0).getTreeResult();
            TemplateTreeModel model = JSONObject.parseObject(tree, TemplateTreeModel.class);
            // 把新的node加到旧的对象里重新计算位置
            addNewNodeToModel(model, task);
            // 调用构造位置的方法设置坐标
            List<TemplateTreeNode> nodes = calculation.getTriggerNodeLocationWithLocating(model);
            model.setNodes(nodes);
            // 更新treeresult
            task.setTreeResult(JSONObject.toJSONString(model));
        }
    }

    private void handleNewTask(TriggerExecution task) {
        // 设置主键
        task.setId(UUID.randomUUID()
            .toString()
            .replaceAll(SynAnalysisResultConstant.Separator.SEPARATOR_DASH,
                SynAnalysisResultConstant.Separator.SEPARATOR_NULL));
        // 新增任务
        TriggerExecutionNode node = task.getTriggerNode();
        if (node.getMainIndicator().getTaskId() != null) {
            task.setTaskId(node.getMainIndicator().getTaskId());
            task.setIndicatorId(node.getMainIndicator().getIndicatorId());
            TaskManageMapper taskManageMapper = mapperFactory.getMapper(TaskManageMapper.class);
            AnalysisTask analysisTask = taskManageMapper.getAnalysisTaskById(task.getTaskId());
            if (Integer.parseInt(analysisTask.getDatasourceId()) >= 100) {
                node.setMainIndicatorDataType(AssociationAnalysisType.CUSTOM_PM);
            } else {
                node.setMainIndicatorDataType(AssociationAnalysisType.DV_PM);
            }
        }
        task.setCreateUser(ContextUtils.getContext().getUserId());
        associationAnalysisDao.insertExecutionResult(task);
    }

    private void handleNode(TriggerExecution task) throws ServiceException {
        TriggerExecutionNode node = task.getTriggerNode();
        if (node.getMainIndicator().getTaskId() != null) {
            suppleIndicator(task);
        } else {
            // 设置主指标的nodeid为上一个node的id
            node.getMainIndicator().setNodeId(node.getParentNodeId());
        }

        if (AssociationAnalysisType.SQL_PM.equalsIgnoreCase(node.getIndicatorDataType())) {
            // 处理查sql的指标
            handleSqlIndicator(task);
        } else if (AssociationAnalysisType.PROMETHEUS_PM.equalsIgnoreCase(node.getIndicatorDataType())) {
            // 处理查prometheus的指标
            handlePrometheusIndicator(task);
        } else if (AssociationAnalysisType.DV_ALARM.equalsIgnoreCase(node.getIndicatorDataType())
            || AssociationAnalysisType.DV_LOG.equalsIgnoreCase(node.getIndicatorDataType())) {
            task.getTriggerNode().setIndicatorList(new ArrayList<>());
        }
        // DV性能,按网元类型选择拆分指标 删掉了
        // 校验数量
        validAssociationTask(task);
        // 设置主键
        node.setNodeId(UUID.randomUUID()
            .toString()
            .replaceAll(SynAnalysisResultConstant.Separator.SEPARATOR_DASH,
                SynAnalysisResultConstant.Separator.SEPARATOR_NULL));
        node.setExecutionStatus(AssociationAnalysisNodeStatus.ANALYZING);
        node.setExecutionId(task.getId());
        node.setMainIndicatorJson(JSONObject.toJSONString(node.getMainIndicator()));
        associationAnalysisDao.insertExecutionNode(node);
        insertList(node.getIndicatorList(), node.getExecutionId(), node.getNodeId());
    }

    private void insertList(List<TriggerIndicator> indicatorList, String executionId, String nodeId) {
        List<List<TriggerIndicator>> subLists = ListUtils.partition(indicatorList, LIST_SIZE);
        for (List<TriggerIndicator> list : subLists) {
            associationAnalysisDao.insertTriggerIndicator(list, executionId, nodeId);
        }
    }

    private String executeTemplate(TriggerExecution task) throws ServiceException {
        task.getTriggerNode().setTimeType(TaskConstant.TRIGGER_TASK_TIME_TYPE_AUTO);
        TriggerIndicator mainIndicator = task.getTriggerNode().getMainIndicator();
        TriggerIndicator triggerIndicator;
        if (mainIndicator.getTaskId() != null) {
            List<TaskIndicator> taskIndicatorList = taskIndicatorDao.getTaskIndicatorList(mainIndicator.getTaskId(),
                mainIndicator.getIndicatorId());
            if (CollectionUtils.isEmpty(taskIndicatorList)) {
                LOGGER.error("cannot find main indicator, taskId = {}", mainIndicator.getTaskId());
                throw new ServiceException("cannot find main indicator");
            }
            TaskIndicator taskIndicator = taskIndicatorList.get(0);
            triggerIndicator = JSONObject.parseObject(JSONObject.toJSONString(taskIndicator), TriggerIndicator.class);
            AnalysisTask mainIndicatorTask = taskManageDao.getAnalysisTaskById(mainIndicator.getTaskId());
            triggerIndicator.setDataSourceId(Integer.valueOf(mainIndicatorTask.getDatasourceId()));
        } else {
            // 手动分析之后再创建模板，主指标可能不是指标详情里的，前台会传指标信息不需要查询
            triggerIndicator = mainIndicator;
        }
        // 模板任务
        return triggerTaskHandler.executeTemplate(taskManageDao.getAnalysisTaskById(task.getTemplateId()),
            task.getExecutionTime(), triggerIndicator, task, "", false);
    }

    public void handleSqlIndicator(TriggerExecution task) throws ServiceException {
        ColumnMapping mapping = JSONObject.parseObject(task.getTriggerNode().getExtendJson(), ColumnMapping.class);
        mapping.setDataSourceId(Integer.parseInt(task.getTriggerNode().getDatasourceId()));
        List<TaskIndicator> indicatorList = customIndicatorSqlExcutor.getIndicatorFromSql(mapping);
        List<TriggerIndicator> triggerIndicators = JSONObject.parseObject(JSONObject.toJSONString(indicatorList),
            new IndicatorReference());
        for (TriggerIndicator indicator : triggerIndicators) {
            indicator.setDataSourceId(Integer.parseInt(task.getTriggerNode().getDatasourceId()));
        }
        task.getTriggerNode().setIndicatorList(triggerIndicators);
        task.getTriggerNode().setExtendJson(JSONObject.toJSONString(mapping));
    }

    public void handlePrometheusIndicator(TriggerExecution task) throws ServiceException {
        PrometheusData prometheusData = JSONObject.parseObject(task.getTriggerNode().getExtendJson(), PrometheusData.class);
        prometheusData.setDataSourceId(Integer.parseInt(task.getTriggerNode().getDatasourceId()));
        DataSourceQueryParam dataSourceQueryParam = new DataSourceQueryParam();
        dataSourceQueryParam.setQueryColumns(DataSourceServiceImpl.COLUMNS_HAS);
        dataSourceQueryParam.setId(prometheusData.getDataSourceId());
        List<DataSource> dataSourceList = dataSourceDao.getDataSource(dataSourceQueryParam);
        if (CollectionUtils.isEmpty(dataSourceList)) {
            LOGGER.error(
                "[AssociationAnalysisService] handlePrometheusIndicator get data source failed! datasource is not exist, id={}",
                prometheusData.getDataSourceId());
        }
        List<PqlsEntity> pqls = JSONArray.parseArray(prometheusData.getPqls(), PqlsEntity.class);
        List<TaskIndicator> indicatorList = PromethusClient.genPrometheusIndicator(pqls, dataSourceList.get(0),
            prometheusData.getStep(), prometheusData.getTaskId());
        List<TriggerIndicator> triggerIndicators = JSONObject.parseObject(JSONObject.toJSONString(indicatorList),
            new IndicatorReference());
        for (TriggerIndicator indicator : triggerIndicators) {
            indicator.setDataSourceId(prometheusData.getDataSourceId());
        }
        task.getTriggerNode().setIndicatorList(triggerIndicators);
        task.getTriggerNode().setExtendJson(JSONObject.toJSONString(prometheusData));
    }

    public void suppleIndicator(TriggerExecution task) {
        TriggerExecutionNode node = task.getTriggerNode();
        // 第一次创建的时候主指标前台没有全部的信息，需要去数据库查一下
        List<TaskIndicator> indicator = taskIndicatorDao.getTaskIndicatorList(node.getMainIndicator().getTaskId(),
            node.getMainIndicator().getIndicatorId());
        String dataSourceId = associationAnalysisDao.getDataSourceIdByTask(node.getMainIndicator().getTaskId());
        if (indicator.size() == 1) {
            node.setMainIndicator(
                JSONObject.parseObject(JSONObject.toJSONString(indicator.get(0)), TriggerIndicator.class));
            // 第一次新增时设置节点的nodeId
            node.getMainIndicator().setNodeId(task.getId());
            node.getMainIndicator().setDataSourceId(Integer.parseInt(dataSourceId));
        } else {
            LOGGER.error("[AssociationAnalysisService] getTaskIndicatorList error! taskId={},indicatorId={}",
                node.getMainIndicator().getTaskId(), node.getMainIndicator().getIndicatorId());
        }
    }

    public void excuteOnce(TriggerExecution task) {
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                try {
                    LOGGER.info("[AssociationAnalysisService] excuteOnce");
                    task.getTriggerNode().setTimeType(TaskConstant.TRIGGER_TASK_TIME_TYPE_NO_AUTO);
                    task.setUpdateTime(System.currentTimeMillis());
                    // 调用调度获取结果
                    TriggerExecutionNode result = task.getTriggerNode();
                    triggerTaskHandler.executeOnce(result, task.getExecutionTime(), null, false, null, 0);
                    if (AssociationAnalysisType.DV_ALARM.equalsIgnoreCase(result.getIndicatorDataType())) {
                        triggerTaskHandler.handleAlarmResult(result);
                    } else if (AssociationAnalysisType.DV_LOG.equalsIgnoreCase(result.getIndicatorDataType())) {
                        triggerTaskHandler.handleLogResult(result);
                    }
                    associationAnalysisDao.updateExecutionNodeStatus(AssociationAnalysisNodeStatus.SUCCESS, task.getId(),
                        task.getTriggerNode().getNodeId(), result.getCorrelationDegree());
                    if (!AssociationAnalysisType.DV_ALARM.equalsIgnoreCase(result.getIndicatorDataType())) {
                        associationAnalysisDao.updateExecutionIndicatorDegree(result.getIndicatorList(), task.getId(),
                            task.getTriggerNode().getNodeId());
                    }
                    updateTreeStatus(task, AssociationAnalysisNodeStatus.SUCCESS, result.getCorrelationDegree());
                    long costTime = Math.subtractExact(System.currentTimeMillis(), task.getUpdateTime());
                    associationAnalysisDao.updateExecutionResult(task.getId(), task.getTreeResult(), task.getViewResult(),
                        task.getUpdateTime(), AssociationAnalysisNodeStatus.SUCCESS, "", calculateDuration(costTime), "", null);
                } catch (Throwable e) {
                    String failureCause = "";
                    if (e instanceof ServiceException) {
                        failureCause = ((ServiceException) e).getOriginDetailMessage();
                    }
                    LOGGER.error("[AssociationAnalysisService] excuteOnce error", e);
                    associationAnalysisDao.updateExecutionNodeStatus(AssociationAnalysisNodeStatus.FAIL, task.getId(),
                        task.getTriggerNode().getNodeId(), null);
                    updateTreeStatus(task, AssociationAnalysisNodeStatus.FAIL, null);
                    long costTime = Math.subtractExact(System.currentTimeMillis(), task.getUpdateTime());
                    associationAnalysisDao.updateExecutionResult(task.getId(), task.getTreeResult(), task.getViewResult(),
                        task.getUpdateTime(), AssociationAnalysisNodeStatus.FAIL, failureCause, calculateDuration(costTime), "", null);
                }
            }
        };
        customExecuteOnceExecutor.execute(runnable);
    }

    public String calculateDuration(long time) {
        DecimalFormat decimalFormat = new DecimalFormat("0.00");
        if (time < 60000) {
            return decimalFormat.format((double) time / (double) 1000) + "s";
        } else {
            return decimalFormat.format((double) time / (double) 1000 / (double) 60) + "min";
        }
    }

    private void updateTreeStatus(TriggerExecution task, String status, Float correlationDegree) {
        String tree = task.getTreeResult();
        TemplateTreeModel model = JSONObject.parseObject(tree, TemplateTreeModel.class);
        for (TemplateTreeNode node : model.getNodes()) {
            if (task.getTriggerNode().getNodeId().equals(node.getId())) {
                node.setStatus(status);
                if (correlationDegree != null) {
                    node.setRelevance(String.valueOf(correlationDegree));
                }
                if (AssociationAnalysisType.DV_ALARM.equalsIgnoreCase(task.getTriggerNode().getIndicatorDataType())) {
                    NodeIndicatorQuery queryParam = new NodeIndicatorQuery();
                    queryParam.setId(task.getTriggerNode().getExecutionId());
                    queryParam.setNodeId(task.getTriggerNode().getNodeId());
                    node.setNumber(associationAnalysisDao.getNodeAlarmInfoCount(queryParam));
                } else if (AssociationAnalysisType.DV_LOG.equalsIgnoreCase(task.getTriggerNode().getIndicatorDataType())) {
                    node.setNumber(1);
                }
                break;
            }
        }
        for (TemplateTreePath path : model.getPaths()) {
            if (task.getTriggerNode().getNodeId().equals(path.getTarget())) {
                if (correlationDegree != null) {
                    path.setRelevance(String.valueOf(correlationDegree));
                }
                break;
            }
        }
        task.setTreeResult(JSONObject.toJSONString(model));
    }

    private TemplateTreeModel getTemplateModel(TriggerExecution task) {
        List<TemplateTreeNode> nodes = new ArrayList<>();
        // 根节点
        TemplateTreeNode root = new TemplateTreeNode();
        root.setRoot(true);
        root.setId(task.getId());
        if (StringUtils.isEmpty(task.getTriggerNode().getMainIndicator().getIndexName())) {
            // pql作根指标没有名字
            root.setTitle(task.getTriggerNode().getMainIndicator().getPql());
        } else {
            root.setTitle(task.getTriggerNode().getMainIndicator().getIndexName());
        }
        root.setRelevance("0");
        root.setLocation(new NodeLocation());
        root.setType("start");
        nodes.add(root);

        TemplateTreeNode node = new TemplateTreeNode();
        node.setId(String.valueOf(task.getTriggerNode().getNodeId()));
        node.setTitle(task.getTriggerNode().getNodeName());
        node.setStatus(task.getTriggerNode().getExecutionStatus());
        if (AssociationAnalysisType.DV_ALARM.equalsIgnoreCase(task.getTriggerNode().getIndicatorDataType())) {
            node.setType("Associated_ALARM");
        } else if (AssociationAnalysisType.DV_LOG.equalsIgnoreCase(task.getTriggerNode().getIndicatorDataType())) {
            node.setType("Associated_LOG");
        } else {
            node.setType("Associated_KPI");
        }
        node.setNumber(task.getTriggerNode().getIndicatorList().size());
        node.setRelevance(null);
        node.setLocation(new NodeLocation());
        nodes.add(node);

        List<TemplateTreePath> paths = new ArrayList<>();
        TemplateTreePath path = new TemplateTreePath();
        path.setSource(task.getId());
        path.setTarget(String.valueOf(task.getTriggerNode().getNodeId()));
        paths.add(path);

        TemplateTreeModel model = new TemplateTreeModel();
        model.setNodes(nodes);
        model.setPaths(paths);
        return model;
    }

    private void addNewNodeToModel(TemplateTreeModel model, TriggerExecution task) {
        TemplateTreeNode node = new TemplateTreeNode();

        node.setId(String.valueOf(task.getTriggerNode().getNodeId()));
        node.setTitle(task.getTriggerNode().getNodeName());
        node.setStatus(task.getTriggerNode().getExecutionStatus());
        node.setNumber(task.getTriggerNode().getIndicatorList().size());
        node.setNodeParam(task.getTriggerNode());
        if (AssociationAnalysisType.DV_ALARM.equalsIgnoreCase(task.getTriggerNode().getIndicatorDataType())) {
            node.setType("Associated_ALARM");
        } else if (AssociationAnalysisType.DV_LOG.equalsIgnoreCase(task.getTriggerNode().getIndicatorDataType())) {
            node.setType("Associated_LOG");
        } else {
            node.setType("Associated_KPI");
        }

        node.setRelevance(null);
        node.setLocation(new NodeLocation());
        model.getNodes().add(node);

        TemplateTreePath path = new TemplateTreePath();
        path.setSource(String.valueOf(task.getTriggerNode().getParentNodeId()));
        path.setTarget(String.valueOf(task.getTriggerNode().getNodeId()));
        model.getPaths().add(path);
    }

    public void deleteAssocationTask(String id) throws ServiceException {
        try {
            validUser(id);
            associationAnalysisDao.deleteAssocationTask(Collections.singletonList(id));
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AssociationAnalysisService] deleteAssocationTask failed! e=", e);
            throw new ServiceException("deleteAssocationTask failed!");
        }
    }

    /**
     * executePreExecuteNode
     * 手动执行预案节点
     *
     * @param executeParam executeParam
     * @return ResponseResult
     */
    public ResponseResult executePreExecuteNode(ExecutePreNodeParam executeParam) {
        LOGGER.info("[AssociationAnalysisService] start execute PreExecute Node. ");
        ResponseResult result = new ResponseResult();
        try {
            NodeIndicatorQuery queryParam = JSONObject.parseObject(JSON.toJSONString(executeParam), NodeIndicatorQuery.class);
            TriggerExecutionNode triggerExecutionNode = associationAnalysisDao.getNodeInfoById(queryParam);
            setNodeExtendJson(triggerExecutionNode);
            PreExecutionNode preExecutionNode = JSONObject.parseObject(triggerExecutionNode.getExtendJson(), PreExecutionNode.class);

            if (preExecutionNode.getFlowStatus() != null && PreExecuteFlowActionType.EXECUTING.getStatus().equals(preExecutionNode.getFlowStatus())) {
                throw new ServiceException(ResourceUtil.getMessage(ExceptionInternationalConstant.START_EXECUTE_NODE_FLOW_FAIL_1, ContextUtils.getContext().getLocale()));
            }
            JSONObject flowParameter = new JSONObject();
            if (executeParam.getPreExecuteJson() != null && !executeParam.getPreExecuteJson().equals("{}")) {
                flowParameter = JSONObject.parseObject(executeParam.getPreExecuteJson());
                // 手动执行时需要过滤掉无权限的网元dn
                filterAuthDns(flowParameter);
                LOGGER.info("[AssociationAnalysisService] executePreExecuteNode with {} ", flowParameter);
            }
            triggerExecutionNode.setDataSourceType(AssociationAnalysisType.EXECUTE);
            triggerTaskHandler.executeExecuteFlowNode(triggerExecutionNode, flowParameter, null, null, true);
            if (AssociationAnalysisNodeStatus.NOT_START.equals(triggerExecutionNode.getExecutionStatus())) {
                throw new ServiceException(ResourceUtil.getMessage(ExceptionInternationalConstant.START_EXECUTE_NODE_FLOW_FAIL_2, ContextUtils.getContext().getLocale()));
            }
        } catch (ServiceException e) {
            LOGGER.error("[AssociationAnalysisService] executePreExecuteNode cannot start {}", e);
            result.setResultCode(FAIL_CODE_NOT_SUCCESS);
            result.setResultMessage(e.getOriginDetailMessage());
            return result;
        } catch (Throwable te) {
            LOGGER.error("[AssociationAnalysisService] executePreExecuteNode error {}", te);
            result.setResultCode(FAIL_CODE);
            result.setResultMessage(ResourceUtil.getMessage(ExceptionInternationalConstant.CORRELATION_PLAN_EXCEPTION,
                ContextUtils.getContext().getLocale()));
            return result;
        }
        result.setResultCode(SUCCESS);
        result.setResultMessage("success");
        LOGGER.info("[AssociationAnalysisService] Execute PreExecute node success. ");
        return result;
    }

    public TriggerExecutionList getAssocationTask(AssocationAnalysisTaskQuery queryParam) throws ServiceException {
        return getAssocationTask(queryParam, null);
    }

    public TriggerExecutionList getAssocationTask(AssocationAnalysisTaskQuery queryParam, Integer templateId) throws ServiceException {
        Paging paging = queryParam.getPaging();
        if (paging != null && paging.getPageNumber() != null) {
            Integer offset = paging.getPageSize() * (paging.getPageNumber() - 1);
            paging.setOffset(offset);
        }

        String timeRange = queryParam.getTimeRangeRadioId();
        List<Long> customTimes = queryParam.getCustomTimes();
        // 初始化时间
        long startTime = 0L;
        long endTime = System.currentTimeMillis();
        if (StringUtils.isNotEmpty(timeRange)) {
            // 获取开始时间和结束时间
            List<Long> timeList = new TaskAnalysisResultServiceImpl().getStartAndEndTime(timeRange, customTimes);
            if (CollectionUtils.isEmpty(timeList)) {
                LOGGER.error("[AssociationAnalysisService] time param is illegal");
                throw new ServiceException("time param illegal");
            }
            // 如果size=1，则表示时间类型是非自定义的，未传结束时间
            if (timeList.size() == TIME_INDEX_ONE) {
                startTime = timeList.get(TIME_INDEX_ZERO);
                // 如果size=2，则表示时间类型是自定义的，有传结束时间
            } else if (timeList.size() == TIME_INDEX_TWO) {
                endTime = timeList.get(TIME_INDEX_ZERO);
                startTime = timeList.get(TIME_INDEX_ONE);
                if (!TimeUtil.validateMaxTimeRange(startTime, endTime, MAX_TIME_ONE_MONTH)) {
                    LOGGER.error("[AssociationAnalysisService] The time range more than a month");
                    throw new ServiceException("time range more than a month");
                }
            }
        }

        TriggerExecutionList result = new TriggerExecutionList();
        try {
            List<TriggerExecution> tasks;
            int total;
            String userId;
            if (ContextUtils.getContext().getAdmin()) {
                userId = null;
            } else {
                userId = ContextUtils.getContext().getUserId();
            }
            tasks = associationAnalysisDao.getAssocationTask(queryParam, templateId, startTime, endTime, userId);
            total = associationAnalysisDao.getAssocationTaskCount(queryParam, templateId, startTime, endTime, userId);
            updateExecuteStatus(tasks, queryParam.getTreeResultFlag());
            result.setTriggerExecutions(tasks);
            result.setTotal(total);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AssociationAnalysisService] getAssocationTask failed! e=", e);
            throw new ServiceException("getAssocationTask failed!");
        }
        return result;
    }

    private void updateExecuteStatus(List<TriggerExecution> tasks, Boolean treeResultFlag) {
        // 需要查询的合并结果的id
        Map<String, TriggerExecution> mergeExecution = new HashMap<>();
        for (TriggerExecution task : tasks) {
            handleMerged(treeResultFlag, mergeExecution, task);
            String treeResult = task.getTreeResult();
            if (StringUtils.isEmpty(treeResult)) {
                continue;
            }
            boolean statuUpdate = false;
            TemplateTreeModel model = JSONObject.parseObject(treeResult, TemplateTreeModel.class);
            for (TemplateTreeNode node : model.getNodes()) {
                TriggerExecutionNode triggerExecutionNode = node.getNodeParam();
                if (triggerExecutionNode != null && AssociationAnalysisType.DATA_TYPE_FLOW.equalsIgnoreCase(
                    triggerExecutionNode.getIndicatorDataType())) {
                    // 预案节点查询一下状态
                    setNodeExtendJson(triggerExecutionNode);
                    statuUpdate = true;
                }
            }
            if (statuUpdate) {
                task.setTreeResult(JSONObject.toJSONString(model));
                associationAnalysisDao.updateExecutionResult(task.getId(), task.getTreeResult(), task.getViewResult(),
                    null, task.getStatus(), task.getFailureCause(), null, "", null);
            }
        }
    }

    private void handleMerged(Boolean treeResultFlag, Map<String, TriggerExecution> mergeExecution,
        TriggerExecution task) {
        // 合并的结果需要处理一下
        if (task.getMerged() != null && task.getMerged()) {
            if (task.getMergeId() != null && task.getMergeIndicatorId() != null) {
                TriggerExecution execution = null;
                if (mergeExecution.containsKey(task.getMergeId())) {
                    execution = mergeExecution.get(task.getMergeId());
                } else {
                    AssocationAnalysisTaskQuery queryParam = new AssocationAnalysisTaskQuery();
                    queryParam.setId(task.getMergeId());
                    queryParam.setIndicatorId(task.getMergeIndicatorId());
                    queryParam.setTreeResultFlag(treeResultFlag);
                    try {
                        List<TriggerExecution> result = associationAnalysisDao.getAssocationTask(queryParam, null, 0L,
                            0L, null);
                        if (CollectionUtils.isNotEmpty(result)) {
                            execution = result.get(0);
                            mergeExecution.put(execution.getId(), execution);
                        } else {
                            task.setStatus("fail");
                            task.setFailureCause(ResourceUtil.getMessage(ExceptionInternationalConstant.MERGED_ASSOCIATION_TASK_DELETED,
                                    ContextUtils.getDefaultLocale()));
                        }
                    } catch (ServiceException e) {
                        LOGGER.error("[AssociationAnalysisService] query merged error, id={}, e={}", queryParam.getId(),
                            e.getMessage());
                    }
                }
                if (execution != null) {
                    task.setStatus(execution.getStatus());
                    task.setTimeRequired(execution.getTimeRequired());
                    task.setTreeResult(execution.getTreeResult());
                }
            }
        }
    }

    private List<FeedbackExportParam> tranRecommendRecord(List<RecommendFeedbackExportEntity> entities) {
        List<FeedbackExportParam> feedbackParams = new ArrayList<>();
        for (RecommendFeedbackExportEntity entity : entities) {
            FeedbackExportParam feedbackParam = new FeedbackExportParam();
            BeanUtils.copyProperties(entity, feedbackParam);
            feedbackParams.add(feedbackParam);
        }
        return feedbackParams;
    }

    /**
     * 查询推荐结果
     *
     * @param param 查询参数
     * @return 推荐结果
     * @throws ServiceException 服务异常
     */
    public FeedbackParams queryRecommendFeedback(RecommendFeedbackQueryParam param) throws ServiceException {
        FeedbackParams feedbackParams = new FeedbackParams();
        List<RecommendFeedbackExportEntity> entities = new ArrayList<>();
        int count = intelligentRecommendDao.queryAllRecommendFeedbackNum();
        if (count > 0) {
            entities = intelligentRecommendDao.queryExportRecommendFeedback(param);
        }
        feedbackParams.setFeedbackParamList(tranRecommendRecord(entities));
        feedbackParams.setCount(count);

        return feedbackParams;
    }

    /**
     * 查询推荐节点点击记录
     *
     * @param param 查询参数
     * @return 点击记录结果
     * @throws ServiceException 服务异常
     */
    public RecommendAccessRecords queryRecommendAccessRecord(RecommendAccessRecordQueryParam param) throws ServiceException {
        RecommendAccessRecords accessRecords = new RecommendAccessRecords();
        List<RecommendRecordExportEntity> entities = new ArrayList<>();
        int count = intelligentRecommendDao.queryAllAccessRecordNum();
        if (count > 0) {
            entities = intelligentRecommendDao.queryAccessRecord(param);
        }
        accessRecords.setRecommendAccessRecordList(tranAccessRecord(entities));
        accessRecords.setCount(count);
        return accessRecords;
    }

    private List<RecommendAccessRecord> tranAccessRecord(List<RecommendRecordExportEntity> entities) {
        List<RecommendAccessRecord> accessRecords = new ArrayList<>();
        for (RecommendRecordExportEntity entity : entities) {
            RecommendAccessRecord accessRecord = new RecommendAccessRecord();
            BeanUtils.copyProperties(entity, accessRecord);
            accessRecords.add(accessRecord);
        }
        return accessRecords;
    }

    private void buildFeedbackInfo(String feedbackInfo, String feedbackInfoStr, RecommendFeedbackEntity entity){
        FeedbackInfoList infoList = new FeedbackInfoList();
        if (StringUtils.isNotEmpty(feedbackInfo)) {
            infoList = JsonUtils.fromJson(feedbackInfo, FeedbackInfoList.class);
        }
        FeedbackInfoEntity feedbackEntity = new FeedbackInfoEntity();
        feedbackEntity.setTimestamp(Long.toString(System.currentTimeMillis()));
        feedbackEntity.setInfoStr(feedbackInfoStr);
        if (ObjectUtils.isNotEmpty(infoList)) {
            infoList.getInfoList().add(feedbackEntity);
        }
        entity.setFeedbackInfo(JSON.toJSONString(infoList));
    }

    /**
     * 推荐反馈
     *
     * @param userName 用户名
     * @param param 返回参数
     * @return 反馈请求结果
     */
    public ResponseResult recommendFeedback(String userName, FeedbackParam param) {
        ResponseResult result = ResponseResultUtil.buildSuccessResult();
        RecommendFeedbackEntity entity = new RecommendFeedbackEntity();
        // 当反馈为不采纳且内容为空的时候，返回反馈失败
        if (param.getRate() == -1 && StringUtils.isBlank(param.getFeedbackInfo())) {
            result = ResponseResultUtil.buildFailResult();
            return result;
        }
        try {
            // 校验关联分析记录是否存在
            AssocationAnalysisTaskQuery queryParam = new AssocationAnalysisTaskQuery();
            queryParam.setId(param.getExecuteId());
            int associationTaskCount = associationAnalysisDao.getAssocationTaskCount(queryParam, null, 0L, 0L, null);
            if (associationTaskCount < 1) {
                LOGGER.error("The correlation analysis record whose execution record is {} has been deleted!", param.getExecuteId());
                return ResponseResultUtil.buildFailResult(ResourceUtil.getMessage(ExceptionInternationalConstant.ASSOCIATION_RECORD_NOT_EXITS_ERROR, ContextUtils.getContext().getLocale()));
            }
            RecommendFeedbackEntity feedbackEntity = intelligentRecommendDao.queryFeedbackInfoByParam(param, userName);
            if (ObjectUtils.isNotEmpty(feedbackEntity)) {
                BeanUtils.copyProperties(feedbackEntity, entity);
                if (StringUtils.isNotEmpty(param.getFeedbackInfo())) {
                    buildFeedbackInfo(feedbackEntity.getFeedbackInfo(), param.getFeedbackInfo(), entity);
                }
            } else {
                BeanUtils.copyProperties(param, entity);
                entity.setUserId(userName);
                if (StringUtils.isNotEmpty(param.getFeedbackInfo())) {
                    buildFeedbackInfo(null, param.getFeedbackInfo(), entity);
                }
            }
            entity.setRate(param.getRate());
            entity.setUpdateTime(Long.toString(System.currentTimeMillis()));
            intelligentRecommendDao.insertFeedbackInfo(entity);
        } catch (Exception e) {
            LOGGER.error("update recommend feedback info failed. e :", e);
        }
        return result;
    }

    private RecommendRecordEntity buildRecommendRecord(String userName, String nodeId) {
        RecommendRecordEntity record = new RecommendRecordEntity();
        record.setId(UUID.randomUUID().toString());
        record.setUpdateTime(new Timestamp(System.currentTimeMillis()).toString());
        record.setUserName(userName);
        record.setRecommendType(AssociationAnalysisType.DV_INTELLIGENT_RECOMMENDATION);
        record.setNodeId(nodeId);
        return record;
    }

    public TriggerIndicatorList getNodeIndicatorInfo(String userName, NodeIndicatorQuery queryParam) throws ServiceException {
        String mergedExecutionId = getMergedExecutionId(queryParam.getId());
        if (mergedExecutionId != null) {
            queryParam.setId(mergedExecutionId);
        }
        Paging paging = queryParam.getPaging();
        if (paging != null && paging.getPageNumber() != null) {
            Integer offset = paging.getPageSize() * (paging.getPageNumber() - 1);
            paging.setOffset(offset);
        }
        TriggerIndicatorList result = new TriggerIndicatorList();
        try {
            validUser(queryParam.getId());
            TriggerExecutionNode triggerExecutionNode = associationAnalysisDao.getNodeInfoById(queryParam);
            result.setNodeInfo(triggerExecutionNode);
            result.setMainIndicator(
                JSONObject.parseObject(associationAnalysisDao.getNodeMainIndicator(queryParam), TriggerIndicator.class));
            if (AssociationAnalysisType.DATA_TYPE_SHOW.equalsIgnoreCase(result.getNodeInfo().getIndicatorDataType())) {
                LOGGER.debug("[AssociationAnalysisService] getNodeIndicatorInfo {}", AssociationAnalysisType.DATA_TYPE_SHOW);
            } else if (AssociationAnalysisType.DATA_TYPE_FLOW.equalsIgnoreCase(triggerExecutionNode.getIndicatorDataType())) {
                setNodeExtendJson(triggerExecutionNode);
            } else if (AssociationAnalysisType.DV_ALARM.equalsIgnoreCase(result.getNodeInfo().getIndicatorDataType())) {
                result.setTriggerAlarmList(associationAnalysisDao.getNodeAlarmInfo(queryParam));
                result.setTotal(associationAnalysisDao.getNodeAlarmInfoCount(queryParam));
            } else if (AssociationAnalysisType.DV_INTELLIGENT_RECOMMENDATION.equalsIgnoreCase(result.getNodeInfo().getIndicatorDataType())) {
                result.setTriggerRecommendationInfo(associationAnalysisDao.getTriggerRecommendationInfo(userName, queryParam));
                intelligentRecommendDao.insertRecommendRecord(buildRecommendRecord(userName, queryParam.getNodeId()));
            } else {
                result.setTriggerIndicators(associationAnalysisDao.getNodeIndicatorInfo(queryParam));
                result.setTotal(associationAnalysisDao.getNodeIndicatorInfoCount(queryParam));
                if (AssociationAnalysisType.DV_PM.equals(result.getNodeInfo().getMainIndicatorDataType())) {
                    List<TriggerIndicator> indicators = new ArrayList<>();
                    indicators.add(result.getMainIndicator());
                    setMoIp(indicators);
                }
                if (AssociationAnalysisType.DV_PM.equals(result.getNodeInfo().getIndicatorDataType())) {
                    setMoIp(result.getTriggerIndicators());
                }
            }

            addTriggerExecutionNodeFeature(result.getNodeInfo());
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AssociationAnalysisService] getNodeIndicatorInfo failed! e=", e);
            throw new ServiceException("getNodeIndicatorInfo failed!");
        }
        return result;
    }

    /**
     * setNodeExtendJson
     *
     * @param triggerExecutionNode triggerExecutionNode
     */
    public void setNodeExtendJson(TriggerExecutionNode triggerExecutionNode) {
        List<Map<String, String>> flowTaskIds = associationAnalysisDao.getExecuteFlowTaskId(triggerExecutionNode.getExecutionId(), triggerExecutionNode.getNodeId());
        PreExecutionNode preExecutionNode = JSONObject.parseObject(triggerExecutionNode.getExtendJson(),
            PreExecutionNode.class);
        if (CollectionUtils.isEmpty(flowTaskIds)) {
            triggerExecutionNode.setExecutionStatus(AssociationAnalysisNodeStatus.NOT_START);
            preExecutionNode.setFlowStatus(PreExecuteFlowActionType.NOT_STARTED.getStatus());
        } else {
            Map<String, String> lineResult = flowTaskIds.get(0);
            String status = lineResult.get("FLOW_EXE_STATUS");
            String flowTaskId = lineResult.get("FLOW_TASK_ID");
            if (StringUtils.isEmpty(status) || PreExecuteFlowActionType.EXECUTING.getStatus().equals(status)) {
                triggerExecutionNode.setExecutionStatus(triggerTaskHandler.getFlowStatus(flowTaskId, preExecutionNode));
            } else {
                preExecutionNode.setFlowTaskId(flowTaskId);
                preExecutionNode.setFlowStatus(status);
                triggerExecutionNode.setExecutionStatus(triggerTaskHandler.getFlowStatus(flowTaskId, status));
            }
        }
        triggerExecutionNode.setExtendJson(JSONObject.toJSONString(preExecutionNode));
    }

    /**
     * addTriggerExecutionNodeFeature
     * 添加AlgorithmFeatureType 重构考虑插数据库
     * 当前由于数据库结构完备数据缺失需要联表查
     *
     * @param triggerExecutionNode triggerExecutionNode
     * @throws ServiceException ServiceException
     */
    public void addTriggerExecutionNodeFeature(TriggerExecutionNode triggerExecutionNode) throws ServiceException {
        try {
            TriggerNodeAlgorithmParams triggerNodeAlgorithmParams = associationAnalysisDao.
                getTriggerAlgorithmModelFromAlgorithmTemplateId(triggerExecutionNode.getAlgorithmTemplateId());
            if (triggerNodeAlgorithmParams == null) {
                return;
            }

            if (AssociationAnalysisType.DATA_TYPE_FLOW.equalsIgnoreCase(triggerExecutionNode.getIndicatorDataType()) ||
                AssociationAnalysisType.DATA_TYPE_SHOW.equalsIgnoreCase(triggerExecutionNode.getIndicatorDataType()) ||
                AssociationAnalysisType.DV_INTELLIGENT_RECOMMENDATION.equalsIgnoreCase(triggerExecutionNode.getIndicatorDataType())) {
                return;
            }

            //  存过直接读
            if (triggerNodeAlgorithmParams.getFeatureType() != null &&
                triggerNodeAlgorithmParams.getFeatureTypeName() != null) {
                triggerExecutionNode.setAlgorithmFeatureType(triggerNodeAlgorithmParams.getFeatureType());
                triggerExecutionNode.setAlgorithmFeatureTypeName(triggerNodeAlgorithmParams.getFeatureTypeName());
                return;
            }

            //  没存过 从算法参数读
            List<ModelParameter> modelParameters = JSONArray.parseArray(triggerNodeAlgorithmParams.getAlgorithmParams(),
                ModelParameter.class);
            boolean hasFeature = false;
            for (ModelParameter modelParameter : modelParameters) {
                if (modelParameter.getParameterName().equals(AssociationAnalysisAlgorithmFeatureType.PARAMETER_NAME)) {
                    hasFeature = true;
                    triggerExecutionNode.setAlgorithmFeatureType((String) modelParameter.getParameterDefaultValue());
                    triggerExecutionNode.setAlgorithmFeatureTypeName(modelParameter.getDisplayName());
                }
            }
            if (!hasFeature) {
                //  没存过 算法参数也没有 从默认读 兼容
                Map<String, String> defaultFeatureMap = TriggerTaskUtil.getDefaultFeatureMap();
                triggerExecutionNode.setAlgorithmFeatureType(AssociationAnalysisAlgorithmFeatureType.DEFAULT_TYPE);
                triggerExecutionNode.setAlgorithmFeatureTypeName(defaultFeatureMap.get(triggerNodeAlgorithmParams.getAlgorithmBaseName()));
            }

            //  洗数据
            triggerNodeAlgorithmParams.setFeatureType(triggerExecutionNode.getAlgorithmFeatureType());
            triggerNodeAlgorithmParams.setFeatureTypeName(triggerExecutionNode.getAlgorithmFeatureTypeName());
            associationAnalysisDao.insertFeatureType(triggerNodeAlgorithmParams);

        } catch (Throwable t) {
            LOGGER.error("[AssociationAnalysisService] addTriggerExecutionNodeFeature failed! e=", t);
            throw new ServiceException("addTriggerExecutionNodeFeature failed!");
        }
    }

    private void setMoIp(List<TriggerIndicator> indicators) {
        if (CollectionUtils.isEmpty(indicators)) {
            return;
        }
        List<String> dnList = indicators.stream().map(TriggerIndicator::getDn).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dnList)) {
            return;
        }
        List<MoObject> objectList = TaskUtil.getMoListByDnList(dnList);
        Map<String, MoObject> moMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(objectList)) {
            objectList.forEach(perObject -> {
                moMap.put(perObject.getDn(), perObject);
            });
        }
        TriggerTaskUtil.clearHostIpCache();
        indicators.forEach(indicator -> {
            if (moMap.containsKey(indicator.getDn())) {
                MoObject moObject = moMap.get(indicator.getDn());
                indicator.setIp(moObject.getIpaddress());
                indicator.setHostIp(TriggerTaskUtil.locateHostIp(moObject.getDn()));
            }
        });
    }

    public CustomIndicatorList getCustomIndicator(CustomIndicatorQuery queryParam) throws ServiceException {
        Paging paging = queryParam.getPaging();
        if (paging != null && paging.getPageNumber() != null) {
            Integer offset = paging.getPageSize() * (paging.getPageNumber() - 1);
            paging.setOffset(offset);
        }
        CustomIndicatorList result = new CustomIndicatorList();
        try {
            List<CustomIndicator> datas;
            int total;
            String userId;
            if (ContextUtils.getContext().getAdmin()) {
                userId = null;
            } else {
                userId = ContextUtils.getContext().getUserId();
            }
            datas = associationAnalysisDao.getCustomIndicator(queryParam, userId);
            datas.addAll(associationAnalysisDao.getCustomIndicatorPrometheus(queryParam, userId));
            total = associationAnalysisDao.getCustomIndicatorCount(queryParam, userId)
                + associationAnalysisDao.getCustomIndicatorPrometheusCount(queryParam, userId);
            result.setCustomIndicators(datas);
            result.setTotal(total);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AssociationAnalysisService] getCustomIndicatorById failed! e=", e);
            throw new ServiceException("getCustomIndicatorById failed!");
        }
        return result;
    }

    public void deleteAssocationTaskByTaskId(Integer taskId) {
        try {
            List<String> ids = associationAnalysisDao.getExecutionIdByTaskId(taskId);
            if (CollectionUtils.isNotEmpty(ids)) {
                associationAnalysisDao.deleteAssocationTask(ids);
            }
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AssociationAnalysisService] deleteAssocationTask failed! e=", e);
        }
    }

    static class IndicatorReference extends TypeReference<List<TriggerIndicator>> {
        /**
         * 构造方法
         */
        public IndicatorReference() {
        }
    }

    private void validAssociationTask(TriggerExecution task) throws ServiceException {
        // 校验关联分析任务数量
        int newIndicatorCount = task.getTriggerNode().getIndicatorList().size();
        int totalCount = associationAnalysisDao.getTaskAllIndicatorCount() + newIndicatorCount;
        if (ConfigurationUtil.associationAnalysisIndicatorMax() < totalCount) {
            LOGGER.error(
                "[AssociationAnalysisService] validAssociationTask association analysis task indicator count over max!,count={}",
                totalCount);
            throw ExceptionUtil.createExceptionByMessage(EXCEPTION_TOTAL_INDICATOR_COUNT_OVER_MAX);
        }

        Map<String, Integer> map = associationAnalysisDao.getTaskNodeCountEveryType(task.getId());
        TriggerCountModel countModel = new TriggerCountModel();
        countModel.setAllNodeCount(map.values().stream().mapToInt(Integer::intValue).sum() + 1);
        countModel.setIndicatorNodeCount(map.getOrDefault(AssociationAnalysisType.DV_PM, 0) + 1);
        countModel.setAlarmNodeCount(map.getOrDefault(AssociationAnalysisType.DV_ALARM, 0) + 1);
        countModel.setLogNodeCount(map.getOrDefault(AssociationAnalysisType.DV_LOG, 0) + 1);
        countModel.setIndicatorSumCount(associationAnalysisDao.getAllRunningIndicatorCount() + newIndicatorCount);

        TriggerTaskUtil.validCount(countModel);
    }

    public String getAssociationCurve(AssociationCurveQuery queryParam, boolean isOpenApi) throws ServiceException {
        try {
            if (!isOpenApi) {
                validUser(queryParam.getExecutionId());
            }
            if (AssociationAnalysisType.DV_ALARM.equalsIgnoreCase(queryParam.getCurveType())) {
                return getAlarmCurve(queryParam);
            } else if(AssociationAnalysisType.DV_LOG.equalsIgnoreCase(queryParam.getCurveType())){
                String curve = associationAnalysisDao.getLogCurve(queryParam);
                if (StringUtils.isEmpty(curve)) {
                    LOGGER.error("[AssociationAnalysisService] getAssociationCurve log empty");
                    return StringUtils.EMPTY;
                }
                List<AnomalyRate> list = JSON.parseArray(curve, AnomalyRate.class);
                list.forEach(anomalyRate -> {
                    anomalyRate.setAnomalyTemplate(null);
                });
                return JSONObject.toJSONString(list);
            }
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[AssociationAnalysisService] getAssociationCurve failed! e=", e);
            throw new ServiceException("getAssociationCurve failed!");
        }
        return StringUtils.EMPTY;
    }

    public String getAlarmCurve(AssociationCurveQuery queryParam) {
        String curve = associationAnalysisDao.getAlarmCurve(queryParam);
        if (StringUtils.isEmpty(curve)) {
            LOGGER.error("[AssociationAnalysisService] getAssociationCurve alarm empty");
            return StringUtils.EMPTY;
        }
        List<AlarmPoint> list = JSON.parseArray(curve, AlarmPoint.class);
        if (CollectionUtils.isEmpty(list)) {
            LOGGER.error("[AssociationAnalysisService] getAssociationCurve list is empty");
            return StringUtils.EMPTY;
        }
        List<AlarmPoint> result = new ArrayList<>();
        result.add(list.get(0));
        if (list.size() == 1) {
            return JSONObject.toJSONString(result);
        }
        long nextTime = list.get(0).getCollectTime();
        // 拆分出1min一个点的曲线
        for (int i = 0; i < list.size() - 1; ) {
            long nextEndTime = list.get(i + 1).getCollectTime();
            nextTime += 60000;
            if (nextTime < nextEndTime) {
                AlarmPoint tmp = new AlarmPoint();
                tmp.setCollectTime(nextTime);
                tmp.setKpiValue(list.get(i).getKpiValue());
                result.add(tmp);
            } else {
                i++;
                result.add(list.get(i));
            }
        }
        return JSONObject.toJSONString(result);
    }

    public List<Map<String, String>> getAssociationAlarmCsn(AlarmCsnQuery queryParam, boolean isOpenApi, TriggerExecutionNode preExecuteNode) throws ServiceException {
        if (!isOpenApi) {
            validUser(queryParam.getExecutionId());
        }
        TriggerExecutionNode node;
        // 如果是预案执行触发查告警流水，此时表中未插入TriggerExecutionNode，需要直接传参；其他场景查表
        if (preExecuteNode == null) {
            NodeIndicatorQuery query = new NodeIndicatorQuery();
            query.setId(queryParam.getExecutionId());
            query.setNodeId(queryParam.getNodeId());
            node = associationAnalysisDao.getNodeInfoById(query);
        } else {
            node = preExecuteNode;
        }
        AlarmQueryParam param = constructAlarmQueryParam(queryParam, node);
        int alarmNum = getCurveAlarmNum(node.getBeginTime(), queryParam);
        if (alarmNum == 0) {
            return Collections.emptyList();
        }
        List<String> occurTimeRange = Arrays.asList(String.valueOf(node.getBeginTime()), String.valueOf(queryParam.getEndTime()));
        List<String> clearTimeRange = Arrays.asList(String.valueOf(queryParam.getStartTime()), String.valueOf(TimeUtil.MAX_TIMESTAMP));
        ImmediateAlarm.buildAlarmTimeRange(param, occurTimeRange, clearTimeRange);
        int maxSize = ConfigurationUtil.associationAlarmTaskQueryCount();
        List<Map<String, String>> alarms = alarmDataQuery(param, ImmediateAlarm.getAlarmTypes(), maxSize, node.getMoName());
        List<Map<String, String>> result = new ArrayList<>(alarms.stream()
            .collect(Collectors.toMap(map -> map.get("csn"), map -> map, (existing, replacement) -> existing))
            .values());
        result.sort(Comparator.comparingLong(csn -> Long.parseLong(csn.get("occurUtc"))));
        if (result.size() > alarmNum) {
            LOGGER.info("alarm real size = {}, curve size = {}.", result.size(), alarmNum);
            result = result.subList(0, alarmNum);
        }
        return result;
    }

    public int getCurveAlarmNum(Long nodeBeginTime, AlarmCsnQuery queryParam) {
        AssociationCurveQuery param = new AssociationCurveQuery();
        param.setAlarmId(queryParam.getAlarmId());
        param.setNodeId(queryParam.getNodeId());
        param.setExecutionId(queryParam.getExecutionId());
        param.setDeviceTypeId(queryParam.getDeviceTypeId());
        String curve = associationAnalysisDao.getAlarmCurve(param);
        if (StringUtils.isEmpty(curve)) {
            LOGGER.error("get alarm num error.");
            return ConfigurationUtil.associationAlarmTaskQueryCount();
        }

        List<AlarmPoint> points = JSON.parseArray(curve, AlarmPoint.class);
        if (CollectionUtils.isEmpty(points)) {
            LOGGER.error("get alarm num list is empty");
            return ConfigurationUtil.associationAlarmTaskQueryCount();
        }

        points = points.stream().sorted(Comparator.comparingLong(AlarmPoint::getCollectTime)).collect(Collectors.toList());
        for (int i = points.size() - 1; i >= 0; i--) {
            AlarmPoint point = points.get(i);
            Long collectTime = point.getCollectTime();
            if (collectTime <= queryParam.getStartTime()) {
                return point.getKpiValue();
            }
        }
        if (queryParam.getStartTime() >= nodeBeginTime) {
            return 0;
        }
        LOGGER.error("get alarm num failed, curve = {}, startTime = {}", curve, queryParam.getStartTime());
        return ConfigurationUtil.associationAlarmTaskQueryCount();
    }

    private AlarmQueryParam constructAlarmQueryParam(AlarmCsnQuery queryParam, TriggerExecutionNode node) {
        AlarmQueryParam param = new AlarmQueryParam();
        QueryContext condition = new QueryContext();
        condition.setExpression("");
        condition.setFilters(new ArrayList<>());
        QSortField sort = new QSortField();
        sort.setField("occurUtc");
        sort.setOrder("ASC");
        param.setSort(Collections.singletonList(sort));
        param.setQuery(condition);
        List<QFilterElement> filters = JSONArray.parseArray(node.getExtendJson(), QFilterElement.class);
        if (CollectionUtils.isNotEmpty(filters)) {
            filters.removeIf(filter -> "deviceTypeId".equals(filter.getField()) || "alarmId".equals(filter.getField()));
        }
        if (CollectionUtils.isNotEmpty(filters)) {
            ImmediateAlarm alarmHandle = new ImmediateAlarm();
            alarmHandle.constructFilters(param, filters);
        }
        QFilterElement filterId = new QFilterElement();
        filterId.setOperator("=");
        filterId.setName("alarmId");
        filterId.setField("alarmId");
        filterId.setValues(Collections.singletonList(queryParam.getAlarmId()));
        param.getQuery().getFilters().add(filterId);
        param.getQuery().setExpression(param.getQuery().getExpression() + filterId.getField() + " and ");
        QFilterElement filterDeviceTypeId = new QFilterElement();
        filterDeviceTypeId.setOperator("=");
        filterDeviceTypeId.setName("deviceTypeId");
        filterDeviceTypeId.setField("deviceTypeId");
        filterDeviceTypeId.setValues(Collections.singletonList(queryParam.getDeviceTypeId().trim()));
        param.getQuery().getFilters().add(filterDeviceTypeId);
        param.getQuery().setExpression(param.getQuery().getExpression() + filterDeviceTypeId.getField() + " and ");
        return param;
    }

    /**
     * alarmDataQuery
     *
     * @param param param
     * @param alarmTypes alarmTypes
     * @param maxSize 限制数量
     * @param moName moName
     * @return alarms
     * @throws ServiceException ServiceException
     */
    public List<Map<String, String>> alarmDataQuery(AlarmQueryParam param, List<String> alarmTypes, int maxSize, String moName) throws ServiceException {
        List<Map<String, String>> alarms = new ArrayList<>();
        if (CollectionUtils.isEmpty(alarmTypes)) {
            return alarms;
        }
        int count = 0;
        for (String type : alarmTypes) {
            int remain = maxSize - count;
            count += getAlarmData(param, type, alarms, remain, moName);
        }
        return alarms;
    }

    private int getAlarmData(AlarmQueryParam param, String alarmtype, List<Map<String, String>> alarmCsn, int maxSize, String moName) throws ServiceException {
        LOGGER.debug("[AssociationAnalysisService] getAlarmData, query = {}, alarmtype = {}", param, alarmtype);
        int result = 0;
        param.setSize(ITERAT_SIZE);
        param.setFields(ALARM_RETURN_FIELDS);
        param.setIterator(null);
        ScrollQueryResult queryResult = AlarmClient.getInstance().queryAlarm(param, alarmtype);
        if (queryResult == null || queryResult.getResCode() != 1) {
            LOGGER.error("[AssociationAnalysisService] getAlarmData queryResult is null");
            return result;
        }

        long start = System.currentTimeMillis();
        int count = 0;
        while (StringUtils.isNotEmpty(queryResult.getIterator()) || CollectionUtils.isNotEmpty(queryResult.getHits())) {
            if (CollectionUtils.isNotEmpty(queryResult.getHits())) {
                ImmediateAlarm.filterByMoName(queryResult.getHits(), moName);
                count += queryResult.getHits().size();
                if (count > maxSize) {
                    List<Map<String, String>> oneData = new ArrayList<>(
                        queryResult.getHits().subList(0, queryResult.getHits().size() - (count - maxSize)));
                    result += oneData.size();
                    alarmCsn.addAll(oneData);
                    LOGGER.debug("[AssociationAnalysisService] getAlarmData sublist size = {},result = {}",
                        oneData.size(), result);
                    return result;
                }
                result += queryResult.getHits().size();
                alarmCsn.addAll(queryResult.getHits());
            }
            param.setIterator(queryResult.getIterator());
            LOGGER.debug("[AssociationAnalysisService] getAlarmInfos, iterator={}, size={}", queryResult.getIterator(),
                queryResult.getHits().size());
            queryResult = AlarmClient.getInstance().queryAlarm(param, alarmtype);
            if (queryResult == null || queryResult.getResCode() != 1) {
                LOGGER.error("[AssociationAnalysisService] getAlarmData queryResult is null");
                break;
            }
        }
        long end = System.currentTimeMillis();
        // 迭代查询结束
        LOGGER.debug("[AssociationAnalysisService] getAlarmData end result size = {}, cost time = {}", result,
            Math.subtractExact(end, start));
        return result;
    }

    public LogTriggerTemplatePointResult getAssociationLogPointTemplate(LogTemplateQuery queryParam, boolean isOpenApi)
        throws ServiceException {
        if (!isOpenApi) {
            validUser(queryParam.getExecutionId());
        }
        AssociationCurveQuery param = new AssociationCurveQuery();
        param.setExecutionId(queryParam.getExecutionId());
        param.setNodeId(queryParam.getNodeId());
        String curve = associationAnalysisDao.getLogCurve(param);

        if (StringUtils.isEmpty(curve)) {
            LOGGER.error("[AssociationAnalysisService] getAssociationLogPointTemplate log empty");
            return new LogTriggerTemplatePointResult();
        }
        List<AnomalyRate> list = JSON.parseArray(curve, AnomalyRate.class);
        Optional<AnomalyRate> rate = list.stream().filter(anomalyRate -> anomalyRate.getCollectTime().equals(queryParam.getTimeStamp())).findFirst();
        if (!rate.isPresent()) {
            return new LogTriggerTemplatePointResult();
        }
        AnomalyRate anomalyRate = rate.get();
        List<String> ids = anomalyRate.getAnomalyTemplate()
            .stream().map(template -> String.valueOf(template.getTemplateId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            LOGGER.error("[AssociationAnalysisService] getAssociationLogPointTemplate template ids is empty");
            return new LogTriggerTemplatePointResult();
        }
        ids = associationAnalysisDao.filterNonContentTemplateId(queryParam.getExecutionId(), queryParam.getNodeId(), ids);
        List<String> idPage = getListPage(queryParam.getPageNumber(), queryParam.getPageSize(), ids);
        LogTriggerTemplatePointResult result = new LogTriggerTemplatePointResult();
        result.setTotal(ids.size());
        if (CollectionUtils.isEmpty(idPage)) {
            LOGGER.error("[AssociationAnalysisService] getAssociationLogPointTemplate template ids is empty");
            return result;
        }
        List<LogTriggerTemplatePoint> points = new ArrayList<>();
        List<LogTemplate> templateList = associationAnalysisDao.getLogTemplate(queryParam.getExecutionId(),
            queryParam.getNodeId(), idPage);
        Map<Integer, String> templateMap = templateList.stream()
            .filter(logTemplate -> StringUtils.isNotEmpty(logTemplate.getContent()) && StringUtils.isNotEmpty(logTemplate.getContent().trim()))
            .collect(Collectors.toMap(LogTemplate::getTemplateId, LogTemplate::getContent));
        anomalyRate.getAnomalyTemplate().forEach(anomalyTemplate -> {
            if (idPage.contains(String.valueOf(anomalyTemplate.getTemplateId())) && templateMap.containsKey(anomalyTemplate.getTemplateId())) {
                LogTriggerTemplatePoint point = new LogTriggerTemplatePoint();
                point.setTemplateId(anomalyTemplate.getTemplateId());
                point.setContent(templateMap.get(anomalyTemplate.getTemplateId()));
                point.setAnomalyTimes(anomalyTemplate.getAnomalyTimes());
                point.setScore(anomalyTemplate.getScore());
                points.add(point);
            }
        });
        result.setTemplateList(points);
        return result;
    }

    private List<String> getListPage(int page, int pageSize, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            LOGGER.error("[AssociationAnalysisService] getAssociationLogPointTemplate ids is empty");
            return Collections.emptyList();
        }
        int totalCount = ids.size();
        page = page - 1;
        int fromIndex = Math.multiplyExact(page, pageSize);
        if (fromIndex >= totalCount) {
            LOGGER.error("[AssociationAnalysisService] getAssociationLogPointTemplate page is error");
            return Collections.emptyList();
        }
        int toIndex = (page + 1) * pageSize;
        if (toIndex > totalCount) {
            toIndex = totalCount;
        }
        return ids.subList(fromIndex, toIndex);
    }

    /**
     * validateCorrelationReport
     *
     * @param request 客户端用户信息
     * @param queryParam 存放结果的集合
     * @return ResponseResult 存放结果的集合
     */
    public ResponseResult validateCorrelationReport(HttpServletRequest request, AssocationAnalysisTaskQuery queryParam) {
        // 分权分域
        String userId;
        if (ContextUtils.getContext().getAdmin()) {
            userId = null;
        } else {
            userId = ContextUtils.getContext().getUserId();
        }
        LogOperationModule logOperationModule = OperationLogUtil.getLogOperationModule(
            ResourceUtil.getMessage(LogConstant.EXPORT_CORRELATION, ContextUtils.getContext().getLocale()) + SPACE + queryParam.getId(),
            ResourceUtil.getMessage(LogConstant.EXPORT_CORRELATION, ContextUtils.getContext().getLocale()),
            ResourceUtil.getMessage(LogConstant.EXPORT_CORRELATION_RESULT_FAIL_OTHER,
                ContextUtils.getContext().getLocale()) + SPACE + queryParam.getId(), LogResult.FAILURE,
            LogSeverity.MINOR);
        ResponseResult result = new ResponseResult();

        if (isFlowAlreadyRunning()) {
            OperationLogUtil.sendAuditLog(request, logOperationModule);
            result.setResultCode(FAIL_CODE_FULL_QUEUE);
            result.setResultMessage("general report queue is full");
            return result;
        }

        // 查询关联分析结果
        long startTime = 0L;
        long endTime = System.currentTimeMillis();
        List<TriggerExecution> tasks = associationAnalysisDao.getAssocationTask(queryParam.getId(), queryParam.getIndicatorId(),
            startTime, endTime, queryParam.getPaging(), userId, queryParam.getTaskId(), false);
        if (CollectionUtils.isEmpty(tasks)) {
            // 记录越权的审计日志
            logOperationModule.setTargetObj(
                ResourceUtil.getMessage(LogConstant.EXPORT_CORRELATION, ContextUtils.getContext().getLocale()) + SPACE + queryParam.getId());
            logOperationModule.setDetail(
                ResourceUtil.getMessage(LogConstant.EXPORT_CORRELATION_RESULT_FAIL, ContextUtils.getContext().getLocale()) + SPACE + queryParam.getId());

            OperationLogUtil.sendAuditLog(request, logOperationModule);

            result.setResultCode(FAIL_CODE);
            result.setResultMessage("task don't exists or permission denied!");
            return result;
        }

        TriggerExecution triggerExecution = tasks.get(0);
        if (!triggerExecution.getStatus().equals(STATUS_SUCCESS)) {
            OperationLogUtil.sendAuditLog(request, logOperationModule);
            result.setResultCode(FAIL_CODE_NOT_SUCCESS);
            result.setResultMessage("execution task status is not success");
            return result;
        }

        result.setResultCode(SUCCESS);
        result.setResultMessage("success");
        return result;
    }

    /**
     * 启动导出报告的UC流程包
     *
     * @param request 请求
     * @param reportFlowParam 查询分析任务的入参
     * @throws ServiceException Service Exception
     */
    public void startCorrelationReportFlow(HttpServletRequest request, ReportFlowParam reportFlowParam) throws ServiceException {
        List<FlowHost> flowHosts = new ArrayList<>();
        FlowHost flowHost = new FlowHost();
        flowHost.setFlowId(reportFlowParam.getFlowId());
        flowHosts.add(flowHost);

        // 构造流程包需要的参数
        JSONObject flowParams = new JSONObject();
        flowParams.put(FLOW_PARAM_ID, reportFlowParam.getId());
        StartFlowRequest startFlowRequest = FlowClient.getInstance().initStartFlowRequestWithoutIps(flowHosts,
            flowParams, true, reportFlowParam.getUserId());
        startFlowRequest.setFlowName(CORRELATION_ANALYSIS);
        startFlowRequest.setFlowGroup(GROUP_PUBLIC);

        BatchCreateTaskResponse response = FlowClient.getInstance().startFlow(reportFlowParam.getUserId(),
            startFlowRequest);

        if (CollectionUtils.isEmpty(response.getTaskIds())) {
            LOGGER.error("[AssociationAnalysisService]start flow error");
            throw new ServiceException("start flow error, please check flow params!");
        }

        LogOperationModule logOperationModule = OperationLogUtil.getLogOperationModule(
            ResourceUtil.getMessage(LogConstant.EXPORT_CORRELATION_RESULT_ID, ContextUtils.getContext().getLocale()),
            ResourceUtil.getMessage(LogConstant.EXPORT_CORRELATION, ContextUtils.getContext().getLocale()),
            ResourceUtil.getMessage(LogConstant.EXPORT_CORRELATION_RESULT_DETAIL, ContextUtils.getContext().getLocale()), LogResult.SUCCESSFUL, LogSeverity.MINOR);
        logOperationModule.setTargetObj(
            ResourceUtil.getMessage(LogConstant.EXPORT_CORRELATION, ContextUtils.getContext().getLocale()) + SPACE + reportFlowParam.getId());
        logOperationModule.setDetail(
            ResourceUtil.getMessage(LogConstant.EXPORT_CORRELATION_RESULT_SUCCESS, ContextUtils.getContext().getLocale()) + SPACE + reportFlowParam.getId());
        OperationLogUtil.sendAuditLog(request, logOperationModule);
    }

    private boolean isFlowAlreadyRunning() {
        QueryTaskByPageRequest queryTaskByPageRequest = new QueryTaskByPageRequest();
        queryTaskByPageRequest.setFlowName(CORRELATION_ANALYSIS);
        queryTaskByPageRequest.setStatus("EXECUTING");
        return FlowClient.getInstance().isFlowAlreadyRunning(queryTaskByPageRequest);
    }

    private void filterAuthDns(JSONObject flowParameter) {
        // 管理员无需过滤
        if (ContextUtils.getContext().getAdmin()) {
            return;
        }
        Set<String> authDns = ContextUtils.getContext().getAuthDns();
        List<AssociationFlowParamOne> flowParams = JSON.parseArray(flowParameter.get(AUTO_EXEC_PARAMS).toString()
            , AssociationFlowParamOne.class);
        for (AssociationFlowParamOne flowParam :flowParams) {
            List<AssociationFlowDn> associationFlowDns = flowParam.getDnList();
            List<AssociationFlowDn> filteredFlowDns = associationFlowDns.stream()
                .filter(flowDn -> authDns.contains(flowDn.getDn()))
                .collect(Collectors.toList());
            flowParam.setDnList(filteredFlowDns);
        }
    }

    private String getMergedExecutionId(String executionId) {
        AssocationAnalysisTaskQuery queryParam = new AssocationAnalysisTaskQuery();
        queryParam.setId(executionId);
        queryParam.setTreeResultFlag(false);
        try {
            List<TriggerExecution> result = associationAnalysisDao.getAssocationTask(queryParam, null, 0L, 0L, null);
            if (CollectionUtils.isNotEmpty(result)) {
                TriggerExecution execution = result.get(0);
                if (execution.getMerged() != null && execution.getMergeId() != null) {
                    return execution.getMergeId();
                } else {
                    return null;
                }
            }
        } catch (ServiceException e) {
            LOGGER.error("[AssociationAnalysisService] query merged error, id={}, e={}", queryParam.getId(),
                e.getMessage());
        }
        LOGGER.error("[AssociationAnalysisService] getMergedExecutionId error, id is null");
        return null;
    }
}