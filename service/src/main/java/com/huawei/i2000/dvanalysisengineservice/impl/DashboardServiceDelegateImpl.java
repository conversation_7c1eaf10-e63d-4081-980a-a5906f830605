/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.impl;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.dashboard.DashboardConstant;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.dashboard.DashboardManage;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.dashboard.dto.PredictParam;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.AnalysisTaskEntity;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskIndicatorDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.handlerimpl.TaskManageDao;
import com.huawei.i2000.dvanalysisengineservice.delegate.DashboardServiceDelegate;
import com.huawei.i2000.dvanalysisengineservice.delegate.IndicatorPredictResultDelegate;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTask;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTaskListQueryParam;
import com.huawei.i2000.dvanalysisengineservice.model.DashboardQueryParam;
import com.huawei.i2000.dvanalysisengineservice.model.DashboardResult;
import com.huawei.i2000.dvanalysisengineservice.model.IndicatorPredictSingleResult;
import com.huawei.i2000.dvanalysisengineservice.model.Paging;
import com.huawei.i2000.dvanalysisengineservice.model.QuerySingleIndicatorParam;
import com.huawei.i2000.dvanalysisengineservice.model.QueryUnitedIndicatorParam;
import com.huawei.i2000.dvanalysisengineservice.model.TaskIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.UnitedIndicatorResult;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;
import com.huawei.i2000.dvanalysisengineservice.util.ResponseResultHelper;
import com.huawei.i2000.dvanalysisengineservice.util.exception.ExceptionInternationalConstant;

import com.alibaba.fastjson2.JSON;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 智能运维大盘功能类
 *
 * <AUTHOR>
 * @since 2023-08-19
 */
@Component
public class DashboardServiceDelegateImpl implements DashboardServiceDelegate {
    private static final OssLog LOGGER = OssLogFactory.getLogger(DashboardServiceDelegateImpl.class);

    private static final List<DashboardQueryParam.PanelTypeEnum> RE_QUERY_PANEL_TYPES = Arrays.asList(DashboardQueryParam.PanelTypeEnum.ABNORMALCORRELATIONANALYSIS, DashboardQueryParam.PanelTypeEnum.INDICATORTRENDPREDICTION);

    @Autowired
    DashboardManage dashboardManage;

    @Autowired
    TaskManageDao taskManageDao;

    @Autowired
    IndicatorPredictResultDelegate indicatorPredictResultDelegate;

    @Autowired
    TaskIndicatorDao taskIndicatorDao;

    @Override
    public DashboardResult doQuery(HttpContext context, DashboardQueryParam queryParam) throws ServiceException {
        try {
            // MM会将大盘从一个环境中导出，然后导入到另一个环境，两个环境上taskId不同，因此需要根据taskName重新查询taskId
            if (!setTaskIdByTaskName(queryParam)) {
                return ResponseResultHelper.dashboardFailed(DashboardConstant.NO_RESOURCE, "task name is not exist.");
            }
            switch (queryParam.getPanelType()) {
                case ALARMANALYSISANDSTATISTICS:
                    return getAlarmViewData(queryParam);
                case TOPNALARMANALYSISINCIDENT:
                    return getTopNIncident(queryParam);
                case ABNORMALCORRELATIONANALYSIS:
                    return getAssociateResult(queryParam);
                case INDICATORTRENDPREDICTION:
                    return getPredictResult(context, queryParam);
                default:
                    return ResponseResultHelper.dashboardFailed("panelType is not support");
            }
        } catch (ServiceException e) {
            LOGGER.error("dashboard do query ServiceException, param={}, e:", JSON.toJSONString(queryParam), e);
            return ResponseResultHelper.dashboardFailed(e);
        } catch (Throwable e) {
            LOGGER.error("dashboard do query Throwable, param={}, e:", JSON.toJSONString(queryParam), e);
            return ResponseResultHelper.dashboardFailed(ResourceUtil.getMessage(ExceptionInternationalConstant.INNER_ERROR,
                ContextUtils.getContext().getLocale()));
        }
    }

    private boolean setTaskIdByTaskName(DashboardQueryParam queryParam) throws ServiceException {
        // 只有当关联分析和趋势预测查询数据时，才需要用taskName查taskId，告警分析和告警压缩用固定的taskId 1和2
        if (DashboardQueryParam.QueryTypeEnum.DATA.equals(queryParam.getQueryType())
            && RE_QUERY_PANEL_TYPES.contains(queryParam.getPanelType())) {
            Map<String, String> param = queryParam.getQueryParam();
            String taskName = param.get(DashboardConstant.TASK_NAME);
            if (StringUtils.isEmpty(taskName)) {
                LOGGER.warn("taskName is empty when dashboard query.");
                return true;
            }

            int taskType = DashboardQueryParam.PanelTypeEnum.ABNORMALCORRELATIONANALYSIS.equals(queryParam.getPanelType())
                ? TaskConstant.TASK_TYPE_CORRELATION
                : TaskConstant.TASK_TYPE_INDICATOR_PREDICT;
            List<AnalysisTaskEntity> tasks = taskManageDao.getTaskListByType(taskType);
            Optional<AnalysisTaskEntity> task = tasks.stream().filter(t -> taskName.equals(t.getTaskName())).findFirst();
            if (!task.isPresent()) {
                LOGGER.error("query task by taskName failed when dashboard query.");
                return false;
            }

            param.put(DashboardConstant.TASK_ID, String.valueOf(task.get().getTaskId()));
        }
        return true;
    }

    private DashboardResult getAlarmViewData(DashboardQueryParam queryParam) throws ServiceException {
        switch (queryParam.getQueryType()) {
            case DATA:
                int taskId = checkAlarmStaticsInfo(queryParam.getQueryParam());
                if (dashboardManage.alarmTask().stream().noneMatch(map -> Objects.equals(map.get(DashboardConstant.TASK_ID), taskId))){
                    // DTS2024082955323
                    LOGGER.error("alarm data view taskId is not exist, taskId = {}", taskId);
                    return ResponseResultHelper.dashboardFailed(DashboardConstant.NO_RESOURCE, "taskId is not exist.");
                }
                return ResponseResultHelper.dashboardResponse(dashboardManage.alarmStatisticsInfo(taskId));
            case TASK:
                return ResponseResultHelper.dashboardResponse(dashboardManage.alarmTask());
            default:
                return ResponseResultHelper.dashboardFailed("queryType is not support");
        }
    }

    private static int checkAlarmStaticsInfo(Map<String, String> queryParam) throws ServiceException {
        try {
            int taskId = Integer.parseInt(queryParam.getOrDefault("taskId", "0"));
            if (taskId < DashboardConstant.ALARM_TYPE_ANALYSIS || taskId > DashboardConstant.ALARM_TYPE_COMPRESS) {
                throw new ServiceException("task id is illegal");
            }
            return taskId;
        } catch (NumberFormatException e) {
            LOGGER.error("fail to checkAlarmStaticsInfo, e: {}", e.getMessage());
            throw new ServiceException("task id is illegal");
        }
    }

    private DashboardResult getTopNIncident(DashboardQueryParam queryParam) throws ServiceException {
        switch (queryParam.getQueryType()) {
            case TASK:
                return ResponseResultHelper.dashboardResponse(dashboardManage.alarmTask());
            case DATA:
                int taskId = checkAlarmStaticsInfo(queryParam.getQueryParam());
                if (dashboardManage.alarmTask().stream().noneMatch(map -> Objects.equals(map.get(DashboardConstant.TASK_ID), taskId))){
                    LOGGER.error("alarm incident view taskId is not exist, taskId = {}", taskId);
                    return ResponseResultHelper.dashboardFailed(DashboardConstant.NO_RESOURCE, "taskId is not exist.");
                }
                return ResponseResultHelper.dashboardResponse(dashboardManage.getTopNIncident(queryParam.getQueryParam()));
            default:
                return ResponseResultHelper.dashboardFailed("queryType is not support");
        }
    }

    private DashboardResult getAssociateResult(DashboardQueryParam queryParam) throws ServiceException {
        switch (queryParam.getQueryType()) {
            case TASK:
                return ResponseResultHelper.dashboardResponse(getTaskList(TaskConstant.TASK_TYPE_CORRELATION, queryParam.getQueryParam()));
            case DATA:
                int taskId = Integer.parseInt(queryParam.getQueryParam().get(DashboardConstant.TASK_ID));
                if (!taskManageDao.getTaskByType(TaskConstant.TASK_TYPE_CORRELATION).contains(taskId)) {
                    LOGGER.error("associate view taskId is not exist, taskId = {}", taskId);
                    return ResponseResultHelper.dashboardFailed(DashboardConstant.NO_RESOURCE, "taskId is not exist.");
                }
                return dashboardManage.getAssociationResult(taskId);
            default:
                return ResponseResultHelper.dashboardFailed("queryType is not support");
        }
    }

    private List<AnalysisTask> getTaskList(int taskType, Map<String, String> queryParam) throws ServiceException {
        AnalysisTaskListQueryParam param = new AnalysisTaskListQueryParam();
        param.setTaskType(Collections.singletonList(taskType));
        param.setPaging(new Paging());
        param.getPaging().setSortField("UPDATE_TIME");
        param.getPaging().setSortType("DESC");
        param.getPaging().setPageNumber(Integer.valueOf(queryParam.getOrDefault(DashboardConstant.PAGE_NUMBER_STR, DashboardConstant.PAGE_NUMBER_VALUE)));
        param.getPaging().setPageSize(Integer.valueOf(queryParam.getOrDefault(DashboardConstant.PAGE_SIZE_STR, DashboardConstant.PAGE_SIZE_VALUE)));
        return taskManageDao.getAnalysisTaskList(param).getRows();
    }

    private DashboardResult getPredictResult(HttpContext context, DashboardQueryParam queryParam) throws ServiceException {
        switch (queryParam.getQueryType()) {
            case TASK:
                return ResponseResultHelper.dashboardResponse(getTaskList(TaskConstant.TASK_TYPE_INDICATOR_PREDICT, queryParam.getQueryParam()));
            case INDICATOR:
                if (Objects.equals(Integer.parseInt(queryParam.getQueryParam().get("indicatorPredictScenario")), TaskConstant.INDICATOR_PREDICT_SCENARIO_UNITED)) {
                    QueryUnitedIndicatorParam param = new QueryUnitedIndicatorParam();
                    param.setTaskId(Integer.valueOf(queryParam.getQueryParam().get(DashboardConstant.TASK_ID)));
                    param.setPageIndex(Integer.valueOf(queryParam.getQueryParam().getOrDefault(DashboardConstant.PAGE_NUMBER_STR, DashboardConstant.PAGE_NUMBER_VALUE)));
                    param.setPageSize(Integer.valueOf(queryParam.getQueryParam().getOrDefault(DashboardConstant.PAGE_SIZE_STR, DashboardConstant.PAGE_SIZE_VALUE)));
                    UnitedIndicatorResult result = indicatorPredictResultDelegate.queryUnitedIndicator(context, param);
                    if (CollectionUtils.isEmpty(result.getClusterInfoList())) {
                        return ResponseResultHelper.dashboardFailed(DashboardConstant.NO_RESOURCE, "resource is not exist.");
                    }
                    return ResponseResultHelper.dashboardResponse(result);
                } else {
                    QuerySingleIndicatorParam param = new QuerySingleIndicatorParam();
                    param.setTaskId(Integer.valueOf(queryParam.getQueryParam().get(DashboardConstant.TASK_ID)));
                    param.setPageIndex(Integer.valueOf(queryParam.getQueryParam().getOrDefault(DashboardConstant.PAGE_NUMBER_STR, DashboardConstant.PAGE_NUMBER_VALUE)));
                    param.setPageSize(Integer.valueOf(queryParam.getQueryParam().getOrDefault(DashboardConstant.PAGE_SIZE_STR, DashboardConstant.PAGE_SIZE_VALUE)));
                    IndicatorPredictSingleResult result = indicatorPredictResultDelegate.querySingleIndicatorList(context, param);
                    if (CollectionUtils.isEmpty(result.getIndicatorInfoList())) {
                        return ResponseResultHelper.dashboardFailed(DashboardConstant.NO_RESOURCE, "resource is not exist.");
                    }
                    return ResponseResultHelper.dashboardResponse(result);
                }
            case DATA:
                PredictParam param = JSON.parseObject(JSON.toJSONString(queryParam.getQueryParam()), PredictParam.class);
                DashboardResult failedResult = checkPredictParam(context, param);
                if (!Objects.isNull(failedResult)) {
                    return failedResult;
                }
                return ResponseResultHelper.dashboardResponse(dashboardManage.getPredictResult(param));
            default:
                return ResponseResultHelper.dashboardFailed("queryType is not support");
        }
    }

    private DashboardResult checkPredictParam(HttpContext context, PredictParam param) throws ServiceException {
        if (Objects.isNull(param.getTaskId()) || Objects.isNull(param.getIndicatorId())
            || Objects.isNull(param.getUnit()) || Objects.isNull(param.getBefore()) || Objects.isNull(param.getAfter())) {
            LOGGER.error("The input parameter is missing, param = {}", param);
            return ResponseResultHelper.dashboardFailed("The input parameter is missing");
        }

        AnalysisTask task = taskManageDao.getAnalysisTaskById(param.getTaskId());
        if (Objects.isNull(task)) {
            LOGGER.error("predict view task is not exist, taskId = {}", param.getTaskId());
            return ResponseResultHelper.dashboardFailed(DashboardConstant.NO_RESOURCE, "taskId is not exist.");
        }
        TaskIndicator indicator = getIndicator(context, task, param.getIndicatorId());
        if (Objects.isNull(indicator)) {
            LOGGER.error("predict view indicator is not exist, indicatorId = {}", param.getIndicatorId());
            return ResponseResultHelper.dashboardFailed(DashboardConstant.NO_RESOURCE, "taskId is not exist.");
        }

        param.setIndicator(indicator);
        param.setTask(task);
        return null;
    }

    private TaskIndicator getIndicator(HttpContext context, AnalysisTask task, String indicatorId)
        throws ServiceException {
        TaskIndicator indicator = null;
        // 联合指标数据
        if (Objects.equals(task.getIndicatorPredictScenario(), TaskConstant.INDICATOR_PREDICT_SCENARIO_UNITED)) {
            QueryUnitedIndicatorParam indicatorParam = new QueryUnitedIndicatorParam();
            indicatorParam.setTaskId(task.getTaskId());
            indicatorParam.setPageIndex(1);
            indicatorParam.setPageSize(DashboardConstant.INDICATOR_PAGE_SIZE);
            // 查询有权限的指标
            UnitedIndicatorResult unitedIndicatorResult = indicatorPredictResultDelegate.queryUnitedIndicator(context, indicatorParam);
            if (unitedIndicatorResult.getClusterInfoList().stream().anyMatch(
                unitedIndicator -> Objects.equals(indicatorId, unitedIndicator.getUnitedId()))) {
                List<TaskIndicator> indicatorList = taskIndicatorDao.getTaskUnitedIndicatorList(task.getTaskId(), indicatorId);
                if (CollectionUtils.isNotEmpty(indicatorList)) {
                    indicator = indicatorList.get(0);
                    indicator.setIndicatorId(indicatorId);
                }
            }
        } else {
            QuerySingleIndicatorParam indicatorParam = new QuerySingleIndicatorParam();
            indicatorParam.setTaskId(task.getTaskId());
            indicatorParam.setPageIndex(1);
            indicatorParam.setPageSize(DashboardConstant.INDICATOR_PAGE_SIZE);
            // 查询有权限的指标
            IndicatorPredictSingleResult singleResult = indicatorPredictResultDelegate.querySingleIndicatorList(context, indicatorParam);
            if (singleResult.getIndicatorInfoList().stream().anyMatch(
                singleIndicator -> Objects.equals(indicatorId, singleIndicator.getIndicatorId()))) {
                List<TaskIndicator> indicatorList = taskIndicatorDao.getTaskIndicatorList(task.getTaskId(), indicatorId);
                if (CollectionUtils.isNotEmpty(indicatorList)) {
                    indicator = indicatorList.get(0);
                }
            }
        }
        return indicator;
    }
}

