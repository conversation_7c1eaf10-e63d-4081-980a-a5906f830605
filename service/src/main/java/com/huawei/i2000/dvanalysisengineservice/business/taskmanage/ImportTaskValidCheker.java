/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.taskmanage;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.ConfigurationUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.AssociationAnalysisType;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.util.EamUtil;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.cbs.TaskImportConfig;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.Graph;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.ImportCapacityTaskEntity;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.ImportMultipleExecutionNode;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.ImportMultipleRelationIndicator;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.ImportTaskEntity;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.LogmatrixClient;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.LogFilterData;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.LogFilterRule;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.LogmatrixSolution;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.QFilterElement;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.immediate.ImmediateAlarm;
import com.huawei.i2000.dvanalysisengineservice.business.templatemanage.dto.TemplateNodeInfo;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.MultipleTreePath;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.PreExecutionNode;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TemplateTreePath;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;
import com.huawei.i2000.dvanalysisengineservice.util.exception.ExceptionInternationalConstant;
import com.huawei.i2000.dvanalysisengineservice.util.exception.ExceptionUtil;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 导入任务校验
 *
 * <AUTHOR>
 * @since 2022/9/2
 */
@Service
public class ImportTaskValidCheker {
    private static final OssLog LOGGER = OssLogFactory.getLogger(ImportTaskValidCheker.class);

    private static final Pattern BLACK_CHARACTER = Pattern.compile("[#%&+|><';?\"()]");

    private static final int MAX_TASK_NAME_LENGTH = 200;

    private static final int ALARM_FILTER_MAX_LEN = 2048;

    private static final int ALARM_DEVICE_TYPE_FILTER_MAX_LEN = 30000;

    private static final List<String> SQL_BLACK_LIST = Collections.unmodifiableList(
        Arrays.asList("ALTER", "DELETE", "UPDATE", "INSERT", "DROP", "GRANT", "REVOKE", "EXECUTE", "EXEC", "DECLARE",
            "CALL", "PROCEDURE", "TRUNCATE"));

    private static final List<String> ALARM_JSON_FIELDS = Collections.unmodifiableList(
        Arrays.asList("alarmId", "alarmName", "eventType", "deviceTypeId", "nativeMoDn", "moi", "severity"));

    private static final List<String> ALARM_JSON_OPERATOR = Collections.unmodifiableList(Arrays.asList("in", "like", "startwith", "endwith"));

    private static final List<String> ALARM_EVENT_TYPE = Collections.unmodifiableList(
        Arrays.asList("1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18",
            "19", "20", "21"));

    private static final HashSet<String> capacityAlarmTimeRangeSet = new HashSet<>();

    private static final List<String> ALARM_SEVERITY = Collections.unmodifiableList(Arrays.asList("1", "2", "3", "4"));

    private static final Pattern EXECUTE_BLACK_CHARACTER = Pattern.compile("[#%&+|'?\"\\\\]");

    private static final int LEVEL_SEVERITY = 1;

    private static final int LEVEL_INFO = 4;

    private static final String INTELLIGENT_RECOMMENDATION = "true";

    static {
        capacityAlarmTimeRangeSet.add("unLimit");
        capacityAlarmTimeRangeSet.add("1|DAY");
        capacityAlarmTimeRangeSet.add("1|WEEK");
        capacityAlarmTimeRangeSet.add("1|MONTH");
        capacityAlarmTimeRangeSet.add("6|MONTH");
        capacityAlarmTimeRangeSet.add("1|YEAR");
    }

    @Autowired
    TaskImporter taskImporter;

    @Autowired
    TaskManager taskManager;

    @Autowired
    LogmatrixClient logmatrixClient;

    private boolean hasIllegalCharacter(String string) {
        return BLACK_CHARACTER.matcher(string).replaceAll("").trim().length() != string.trim().length();
    }

    public boolean check(ImportTaskEntity importTaskEntity) throws ServiceException {
        if (hasIllegalCharacter(importTaskEntity.getTaskName()) || importTaskEntity.getTaskName().length() > 200) {
            LOGGER.error("[ImportTaskValidCheker] taskName illegal, name={}", importTaskEntity.getTaskName());
            return false;
        }
        if (StringUtils.isNotEmpty(importTaskEntity.getCorrelateTaskName()) && importTaskEntity.getCorrelateTaskName().length() > 1024) {
            LOGGER.error("[ImportTaskValidCheker] correlateTaskName is empty or over size, correlateTaskName={}",
                importTaskEntity.getCorrelateTaskName());
            return false;
        }
        if (!importTaskEntity.getTaskType().equals("CorrelationAnalysis")) {
            return false;
        }
        String nodeFilter = importTaskEntity.getNodeFilter();
        if (StringUtils.isNotEmpty(nodeFilter) && (nodeFilter.length() > 12 || !nodeFilter.matches("^[a-zA-Z]*$"))) {
            return false;
        }
        String intelligentRecommendation = importTaskEntity.getIntelligentRecommendation();
        if (StringUtils.isNotEmpty(intelligentRecommendation) && !"true".equals(intelligentRecommendation) && !"false".equals(intelligentRecommendation)) {
            return false;
        }
        // 开启智能推荐必须有知识管理解决方案类型
        if ((INTELLIGENT_RECOMMENDATION.equals(intelligentRecommendation)) == StringUtils.isEmpty(importTaskEntity.getSolutionTypeForRecommendation())) {
            return false;
        }

        Set<String> nodeName = new HashSet<>();
        checkCycle(importTaskEntity.getPaths());
        checkExecuteNodeStatus(importTaskEntity.getNodes(), importTaskEntity.getPaths());
        return isNodeValidAndSaveNode(importTaskEntity.getNodes(), nodeName) && isPathValid(importTaskEntity.getPaths(),
            nodeName);
    }

    public void checkCycle(List<TemplateTreePath> paths) throws ServiceException {
        Graph graph = convert(paths);
        if (hasCycle(graph)) {
            throw ExceptionUtil.createExceptionByMessage(
                ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_LOCATING_NODE_VALID_ERROR3,
                    ContextUtils.getContext().getLocale()));
        }
    }

    public static Graph convert(List<TemplateTreePath> paths) {
        Graph graph = new Graph();
        for (TemplateTreePath path : paths) {
            graph.addEdge(path.getSource(), path.getTarget());
        }
        return graph;
    }

    public static boolean hasCycle(Graph graph) {
        // 统计每个节点的入度。
        // 将所有入度为 0 的节点加入队列。
        // 依次移除入度为 0 的节点，并减少其邻居的入度。
        // 若最终处理的节点数小于总节点数，则存在环
        Map<String, Integer> inDegree = new HashMap<>();
        Map<String, List<String>> adjList = graph.getAdjList();

        // 初始化入度
        for (String node : adjList.keySet()) {
            inDegree.put(node, 0);
        }

        // 填充入度
        for (Map.Entry<String, List<String>> entry : adjList.entrySet()) {
            List<String> targets = entry.getValue();
            for (String target : targets) {
                inDegree.put(target, inDegree.get(target) + 1);
            }
        }

        Queue<String> queue = new LinkedList<>();
        for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) {
                queue.add(entry.getKey());
            }
        }

        int processedCount = 0;

        while (!queue.isEmpty()) {
            String node = queue.poll();
            processedCount++;

            for (String neighbor : adjList.get(node)) {
                inDegree.put(neighbor, inDegree.get(neighbor) - 1);
                if (inDegree.get(neighbor) == 0) {
                    queue.add(neighbor);
                }
            }
        }

        return processedCount != adjList.size();
    }

    private void checkExecuteNodeStatus(List<TemplateNodeInfo> nodes, List<TemplateTreePath> paths) throws ServiceException {
        // 判断预案节点要是叶子节点且不能连主节点
        List<String> executeNodeName = new ArrayList<>();
        List<String> demarcationLocatingNodeName = new ArrayList<>();
        Map<String, Integer> locatingNodeMap = new HashMap<>();
        for (TemplateNodeInfo node : nodes) {
            if ("execute".equals(node.getNodeType())) {
                executeNodeName.add(node.getNodeName());
            }
            if ("Locating".equals(node.getDemarcationLocating())) {
                demarcationLocatingNodeName.add(node.getNodeName());
                locatingNodeMap.put(node.getNodeName(), 0);
            }
        }
        for (TemplateTreePath path : paths) {
            if (executeNodeName.contains(path.getSource())) {
                throw ExceptionUtil.createExceptionByMessage(
                    ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_EXECUTE_NODE_VALID_ERROR2,
                        ContextUtils.getContext().getLocale()) + path.getSource());
            }
            if (executeNodeName.contains(path.getTarget()) && executeNodeName.contains(path.getSource())) {
                throw ExceptionUtil.createExceptionByMessage(
                    ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_EXECUTE_NODE_VALID_ERROR2,
                        ContextUtils.getContext().getLocale()) + path.getSource());
            }
            if ("main".equals(path.getSource()) && executeNodeName.contains(path.getTarget())) {
                throw ExceptionUtil.createExceptionByMessage(
                    ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_EXECUTE_NODE_VALID_ERROR1,
                        ContextUtils.getContext().getLocale()) + path.getTarget());
            }
            if ("main".equals(path.getSource()) && demarcationLocatingNodeName.contains(path.getTarget())) {
                throw ExceptionUtil.createExceptionByMessage(ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_LOCATING_NODE_VALID_ERROR2, ContextUtils.getContext().getLocale()) + path.getTarget());
            }
            if (demarcationLocatingNodeName.contains(path.getSource())) {
                throw ExceptionUtil.createExceptionByMessage(
                    ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_LOCATING_NODE_VALID_ERROR,
                        ContextUtils.getContext().getLocale()) + path.getSource());
            }
            if (demarcationLocatingNodeName.contains(path.getTarget())) {
                Integer count = locatingNodeMap.get(path.getTarget());
                locatingNodeMap.put(path.getTarget(), count + 1);
            }
        }
        List<String> keys = locatingNodeMap.entrySet().stream()
            .filter(entry -> entry.getValue() > 1).map(Map.Entry::getKey).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(keys)) {
            throw ExceptionUtil.createExceptionByMessage(
                ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_LOCATING_NODE_VALID_ERROR,
                    ContextUtils.getContext().getLocale()) + keys);
        }
    }

    public boolean checkCapacityImport(ImportCapacityTaskEntity importTaskEntity) throws ServiceException {
        if (!importTaskEntity.getTaskType().equals(TaskConstant.IMPORT_TASK_TYPE_CAPACITY_PREDICT)) {
            throw ExceptionUtil.createException(ExceptionInternationalConstant.IMPORT_CAPACITY_VALID_TASK_TYPE);
        }
        // 上报告警相关校验
        if (importTaskEntity.getIsReportAlarm() != null && importTaskEntity.getIsReportAlarm()) {
            if (importTaskEntity.getReportAlarmNormalLevel() != null && (importTaskEntity.getReportAlarmNormalLevel() < LEVEL_SEVERITY || importTaskEntity.getReportAlarmNormalLevel() > LEVEL_INFO)) {
                LOGGER.error("capacity alarm normal level error");
                throw ExceptionUtil.createException(ExceptionInternationalConstant.IMPORT_CAPACITY_VALID_ALARM_LEVEL);
            }
            if (importTaskEntity.getReportAlarmSeverityLevel() < LEVEL_SEVERITY || importTaskEntity.getReportAlarmSeverityLevel() > LEVEL_INFO) {
                LOGGER.error("capacity alarm severity level error");
                throw ExceptionUtil.createException(ExceptionInternationalConstant.IMPORT_CAPACITY_VALID_ALARM_LEVEL);
            }
            if (!validAlarmTimeRange(importTaskEntity.getReportAlarmTimeRange())) {
                LOGGER.error("capacity alarm time range error");
                throw ExceptionUtil.createException(ExceptionInternationalConstant.IMPORT_CAPACITY_VALID_TIME_RANGE);
            }
        }
        // 更新配置相关校验
        if (!checkTaskImportConfig(importTaskEntity.getTaskImportConfig())) {
            LOGGER.error("check task import config error");
            throw ExceptionUtil.createException(ExceptionInternationalConstant.IMPORT_CAPACITY_VALID_UPDATE_CONFIG);
        }

        Set<String> nodeName = new HashSet<>();
        return isCapacityNodeValid(importTaskEntity.getNodes(), nodeName) && isCapacityPathValid(
            importTaskEntity.getPaths(), nodeName);
    }

    public boolean checkTaskName(ImportCapacityTaskEntity importTaskEntity) throws ServiceException {
        if (hasIllegalCharacter(importTaskEntity.getTaskName()) || importTaskEntity.getTaskName().length() > MAX_TASK_NAME_LENGTH) {
            LOGGER.error("[ImportTaskValidChecker] taskName illegal, name={}", importTaskEntity.getTaskName());
            return false;
        }
        return true;
    }

    private boolean isCapacityNodeValid(List<ImportMultipleExecutionNode> nodes, Set<String> nodeName)
        throws ServiceException {
        boolean hasMain = false;
        for (ImportMultipleExecutionNode node : nodes) {
            if (!nodeName.add(node.getNodeName())) {
                throw ExceptionUtil.createException(ExceptionInternationalConstant.IMPORT_CAPACITY_VALID_NODE_NAME_REPEAT);
            }
            if (CollectionUtils.isEmpty(node.getIndicatorList())) {
                throw ExceptionUtil.createException(ExceptionInternationalConstant.IMPORT_CAPACITY_VALID_NODE_INDICATOR_LIST, node.getNodeName());
            }
            // 校验双机节点的指标
            if (node.getIfHighAvailableNode() != null && node.getIfHighAvailableNode().equals("1")) {
                List<ImportMultipleRelationIndicator> indicatorList = node.getIndicatorList();
                ImportMultipleRelationIndicator baseIndicator = indicatorList.get(0);
                // 从第二个指标开始，网元类型、测量单元、测量指标必须与第一个指标相同
                for (int i = 1; i < indicatorList.size(); i++) {
                    if (!indicatorList.get(i).getMoType().equals(baseIndicator.getMoType())
                        || !indicatorList.get(i).getMeasUnitKey().equals(baseIndicator.getMeasUnitKey())
                        || !indicatorList.get(i).getMeasTypeKey().equals(baseIndicator.getMeasTypeKey())) {
                        throw ExceptionUtil.createException(ExceptionInternationalConstant.IMPORT_CAPACITY_VALID_NODE_INDICATOR_LIST, node.getNodeName());
                    }
                    // 后续的指标不能填上下阈值，如果填了需要帮用户去掉
                    indicatorList.get(i).setUpperThreshold("");
                    indicatorList.get(i).setLowerThreshold("");
                }
            }
            switch (node.getNodeType()) {
                case 0: {
                    if (hasMain) {
                        return false;
                    }
                    if (node.getIfHighAvailableNode() == null || node.getIfHighAvailableNode().equals("0")) {
                        if (node.getIndicatorList().size() != 1 || node.getIndicatorList().get(0).getDnName() == null) {
                            throw ExceptionUtil.createException(ExceptionInternationalConstant.IMPORT_CAPACITY_VALID_NODE_INDICATOR_LIST, node.getNodeName());
                        }
                    }
                    hasMain = true;
                    continue;
                }
                case 1: {
                    // break switch
                    break;
                }
                default: {
                    throw new ServiceException(ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_ERROR_NO_NODETYPE, ContextUtils.getContext().getLocale()));
                }
            }
        }
        return true;
    }

    private boolean isCapacityPathValid(List<MultipleTreePath> paths, Set<String> nodeName) throws ServiceException {
        for (MultipleTreePath path : paths) {
            if (!nodeName.contains(path.getSource()) || !nodeName.contains(path.getTarget()) || path.getSource()
                .equals(path.getTarget())) {
                LOGGER.error("[ImportTaskValidChecker] path illegal, path={}", path);
                throw ExceptionUtil.createException(ExceptionInternationalConstant.IMPORT_CAPACITY_VALID_NODE_PATH, JSONObject.toJSONString(path));
            }
        }
        return true;
    }


    private boolean isNodeValidAndSaveNode(List<TemplateNodeInfo> nodes, Set<String> nodeName)
        throws ServiceException {
        boolean hasMain = false;
        for (TemplateNodeInfo node : nodes) {
            switch (node.getNodeType()) {
                case "main": {
                    if (hasMain) {
                        return false;
                    }
                    nodeName.add("main");
                    hasMain = true;
                    continue;
                }
                case "moType": {
                    if (!nodeName.add(node.getNodeName())) {
                        return false;
                    }
                    if (CollectionUtils.isEmpty(node.getIndicatorList())) {
                        return false;
                    }
                    break;
                }
                case "normal":
                    if (!validNomalNode(nodeName, node)) {
                        return false;
                    }
                    break;
                case "alarm":
                    if (!validAlarmNode(nodeName, node)) {
                        return false;
                    }
                    break;
                case "log":
                    if (!validLogNode(nodeName, node)) {
                        return false;
                    }
                    break;
                case "execute":
                    if (!validExecuteNode(nodeName, node)) {
                        return false;
                    }
                    break;
                default: {
                    throw new ServiceException(ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_ERROR_NO_NODETYPE, ContextUtils.getContext().getLocale()));
                }
            }

            if (!isKpiValid(node)) {
                return false;
            }
        }
        return true;
    }

    private boolean validExecuteNode(Set<String> nodeName, TemplateNodeInfo node) throws ServiceException {
        if (!illegalCharacterValid(nodeName, node)) {
            throw new ServiceException(ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_ERROR_ASSOCIATION_CHECK, ContextUtils.getContext().getLocale()) + node.getNodeName());
        }
        if (!executeNodeValid(node)) {
            throw new ServiceException(ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_ERROR_ASSOCIATION_CHECK, ContextUtils.getContext().getLocale()) + node.getNodeName());
        }
        return true;
    }

    private boolean validNomalNode(Set<String> nodeName, TemplateNodeInfo node) throws ServiceException {
        if (!illegalCharacterValid(nodeName, node)) {
            return false;
        }
        return true;
    }

    private boolean illegalCharacterValid(Set<String> nodeName, TemplateNodeInfo node) {
        if (!nodeName.add(node.getNodeName())) {
            return false;
        }
        if (hasIllegalCharacter(node.getNodeName())) {
            return false;
        }
        return true;
    }

    private boolean validLogNode(Set<String> nodeName, TemplateNodeInfo node) throws ServiceException {
        if (!illegalCharacterValid(nodeName, node)) {
            return false;
        }
        return logNodeValid(node);
    }

    private boolean validAlarmNode(Set<String> nodeName, TemplateNodeInfo node) throws ServiceException {
        if (!illegalCharacterValid(nodeName, node)) {
            return false;
        }
        if (!alarmNodeValid(node)) {
            return false;
        }
        return true;
    }

    public boolean executeNodeValid(TemplateNodeInfo node) {
        String extendJson = node.getExtendJson();
        if (StringUtils.isEmpty(extendJson)) {
            node.setExtendJson("{}");
            return true;
        }
        PreExecutionNode executeFlowAction = JSONObject.parseObject(extendJson, PreExecutionNode.class);
        if (AssociationAnalysisType.DATA_TYPE_FLOW.equals(node.getIndicatorDataType())) {
            if (CollectionUtils.isEmpty(executeFlowAction.getFlowActions())) {
                LOGGER.error("[ImportTaskValidCheker] flowActions is empty");
                return false;
            }
        }
        if (validExecuteInfo(executeFlowAction.getHelpInfo())) {
            LOGGER.error("[ImportTaskValidCheker] helpInfo is illegal");
            return false;
        }
        if (validExecuteInfo(executeFlowAction.getCauseInfo())) {
            LOGGER.error("[ImportTaskValidCheker] causeInfo is illegal");
            return false;
        }
        if ("Locating".equals(node.getDemarcationLocating())) {
            // 预案节点不能配置成小节点
            LOGGER.error("[ImportTaskValidCheker] demarcationLocating is illegal");
            return false;
        }
        return true;
    }

    private boolean validExecuteInfo(String string) {
        if (StringUtils.isEmpty(string)) {
            return true;
        }
        if (string.length() > 1024) {
            LOGGER.error("[validExecuteInfo] string is over size,{}", string);
            return true;
        }
        return EXECUTE_BLACK_CHARACTER.matcher(string).replaceAll("").trim().length() != string.trim().length();
    }

    public boolean logNodeValid(TemplateNodeInfo node) throws ServiceException {
        String extendJson = node.getExtendJson();
        if (StringUtils.isEmpty(extendJson)) {
            node.setExtendJson("{}");
            return true;
        }
        LogFilterData logFilterData = JSONObject.parseObject(extendJson, LogFilterData.class);
        List<LogmatrixSolution> solutionTypes = logmatrixClient.getLogmatrixSolutionList();
        LogmatrixSolution logmatrixSolution = solutionTypes.stream()
            .filter(type -> type.getMotype().equals(logFilterData.getSolutionType()))
            .findAny()
            .orElse(null);
        if (logmatrixSolution == null) {
            LOGGER.error("[ImportTaskValidCheker] logNodeValid solutionType is illegal, solutionType={}",
                logFilterData.getSolutionType());
            node.setIsGray(true);
            // 如果没有解决方案，需要将解决方案名称设为空白，如果为null前台会报错
            logFilterData.setLogSolutionName(" ");
        } else {
            logFilterData.setLogSolutionName(logmatrixSolution.getSolutionName());
        }
        if ("*".equals(logFilterData.getLogType())) {
            return true;
        }
        List<String> logTypes = logmatrixClient.getLogTypeList(logFilterData.getSolutionType());
        if (!logTypes.contains(logFilterData.getLogType())) {
            LOGGER.error("[ImportTaskValidCheker] logNodeValid logType is illegal, logType={}",
                logFilterData.getLogType());
            node.setIsGray(true);
        }

        List<LogFilterRule> filters = logmatrixClient.getLogFilterRuleList(logFilterData.getSolutionType(),
            logFilterData.getLogType());
        if (CollectionUtils.isEmpty(filters)) {
            LOGGER.error("[ImportTaskValidCheker] logNodeValid filters is empty");
            node.setIsGray(true);
        }
        List<String> filterName = filters.stream().map(LogFilterRule::getFilterName).collect(Collectors.toList());
        for (String key : logFilterData.getQueryFields().keySet()) {
            if (!filterName.contains(key)) {
                LOGGER.error("[ImportTaskValidCheker] logNodeValid filters is illegal, key={}", key);
                node.setIsGray(true);
            }
        }
        node.setExtendJson(JSON.toJSONString(logFilterData));
        return true;
    }

    private boolean alarmNodeValid(TemplateNodeInfo node) throws ServiceException {
        String extendJson = node.getExtendJson();
        String solutionName = node.getSolutionName();
        List<QFilterElement> filters = StringUtils.isNotEmpty(extendJson) ? JSONArray.parseArray(extendJson, QFilterElement.class) : Collections.emptyList();
        if (StringUtils.isEmpty(extendJson)) {
            node.setExtendJson(JSONObject.toJSONString(Collections.emptyList()));
        }
        if (StringUtils.isEmpty(solutionName) && CollectionUtils.isEmpty(filters)) {
            return true;
        }
        if (validateSolutionName(node, solutionName, filters)) {
            return false;
        }
        for (QFilterElement filter : filters) {
            if (!ALARM_JSON_FIELDS.contains(filter.getField())) {
                LOGGER.error("[ImportTaskValidCheker] alarmNode field illegal, field={}", filter.getField());
                return false;
            }
            if (!ALARM_JSON_OPERATOR.contains(filter.getOperator())) {
                LOGGER.error("[ImportTaskValidCheker] alarmNode operator illegal, operator={}", filter.getOperator());
                return false;
            }
            if (CollectionUtils.isEmpty(filter.getValues())) {
                continue;
            }
            if (checkEventTypeAndSeverity(filter)) {
                return false;
            }
            if ("alarmId".equals(filter.getField())) {
                int limitCount = ConfigurationUtil.associationAnalysisAlarmIdLimit();
                if (filter.getValues().size() > limitCount || filter.getValues().stream().mapToInt(String::length).sum() + (filter.getValues().size() - 1) > limitCount * 10) {
                    LOGGER.error("[ImportTaskValidCheker] alarmNode alarmId value size or total char over max size");
                    throw new ServiceException(ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_ERROR_ASSOCIATION_ALARM_ID_CHECK, ContextUtils.getContext().getLocale()) + node.getNodeName());
                } else {
                    continue;
                }
            }
            int charLenLimit = "deviceTypeId".equals(filter.getField()) ? ALARM_DEVICE_TYPE_FILTER_MAX_LEN : ALARM_FILTER_MAX_LEN;
            // 加上前台的换行符，长度就能和前台保持一致
            if (filter.getValues().size() > 1000 || filter.getValues().stream().mapToInt(String::length).sum() + (filter.getValues().size() - 1) > charLenLimit) {
                LOGGER.error("[ImportTaskValidCheker] alarmNode value size or total char over max size");
                return false;
            }
        }
        return true;
    }

    private boolean validateSolutionName(TemplateNodeInfo node, String solutionName, List<QFilterElement> filters)
        throws ServiceException {
        if (StringUtils.isNotEmpty(solutionName)) {
            Map<String, List<String>> filterMap = filters.stream().collect(Collectors.toMap(QFilterElement::getField, QFilterElement::getValues));
            if (!filterMap.containsKey(ImmediateAlarm.FILTER_FIELD_MO_TYPE)
                || CollectionUtils.isEmpty(filterMap.get(ImmediateAlarm.FILTER_FIELD_MO_TYPE))
                || CollectionUtils.isNotEmpty(filterMap.get(ImmediateAlarm.FILTER_FIELD_DN))) {
                LOGGER.error("If the solution is configured, the alarm filtering condition must be configured with the alarm source type and the operator can only be in, nodeName={}", node.getNodeName());
                throw ExceptionUtil.createException(ExceptionInternationalConstant.ALARM_NODE_SOLUTION, node.getNodeName());
            }
            Map<String, String> solutions = EamUtil.getSolutions();
            if (MapUtils.isEmpty(solutions) || !solutions.containsKey(solutionName)) {
                LOGGER.error("solution name is not exist, solutionName = {}", solutionName);
                throw ExceptionUtil.createException(ExceptionInternationalConstant.SOLUTION_ILLEGAL, solutionName);
            }
            node.setSolutionId(solutions.get(solutionName));
        }
        return false;
    }

    private static boolean checkEventTypeAndSeverity(QFilterElement filter) {
        if ("eventType".equals(filter.getField())) {
            if (filter.getValues().stream().anyMatch(value -> !ALARM_EVENT_TYPE.contains(value))
                || filter.getValues().stream().distinct().count() != filter.getValues().size()) {
                LOGGER.error("[TaskValidChecker] alarmNode validEventType error");
                return true;
            }
        }
        if ("severity".equals(filter.getField())) {
            if (filter.getValues().stream().anyMatch(value -> !ALARM_SEVERITY.contains(value))
                || filter.getValues().stream().distinct().count() != filter.getValues().size()) {
                LOGGER.error("[TaskValidChecker] alarmNode valid severity error");
                return true;
            }
        }
        return false;
    }

    private boolean isKpiValid(TemplateNodeInfo node) {
        if (StringUtils.isEmpty(node.getNodeName()) || node.getNodeName().length() > 320) {
            return false;
        }
        if ((StringUtils.isNotEmpty(node.getMoName()) && node.getMoName().length() > 256) || (StringUtils.isNotEmpty(node.getSolutionName()) && node.getSolutionName().length() > 256)) {
            return false;
        }
        if ("execute".equals(node.getNodeType())) {
            return true;
        }
        return validTimeRange(node);
    }

    private boolean validTimeRange(TemplateNodeInfo nodeParam) {
        String timeRange = nodeParam.getKpiTimeRange();
        String timeType = nodeParam.getKpiTimeType();
        if (StringUtils.isEmpty(timeRange) || StringUtils.isEmpty(timeType)) {
            return false;
        }

        String[] split = timeRange.split("\\|");
        if (split.length != 2) {
            return false;
        }
        int number = Integer.parseInt(split[0]);
        boolean timeResultMinute = split[1].equals("MIN") && number >= 10 && number <= 10080;
        boolean timeResultHour = split[1].equals("HOUR") && number >= 1 && number <= 168;
        boolean timeResultDay = split[1].equals("DAY") && number >= 1 && number <= 7;
        if (!timeResultMinute && !timeResultHour && !timeResultDay) {
            return false;
        }
        return TaskConstant.TRIGGER_TASK_NODE_TIME_TYPE_RECENTLY.equals(timeType)
            || TaskConstant.TRIGGER_TASK_NODE_TIME_TYPE_BEFOREAFTER.equals(timeType);
    }

    public static boolean isSqlValid(String sql) {
        for (String black : SQL_BLACK_LIST) {
            if (sql.toUpperCase(Locale.ROOT).contains(black)) {
                return false;
            }
        }
        return (sql.toUpperCase(Locale.ROOT).trim().startsWith("SELECT ") || sql.toUpperCase(Locale.ROOT)
            .trim()
            .startsWith("WITH ")) && !sql.contains(";");
    }

    private boolean isPathValid(List<TemplateTreePath> paths, Set<String> nodeName) throws ServiceException {
        List<String> sourceTargetList = new ArrayList<>();
        for (TemplateTreePath path : paths) {
            String sourceTarget = path.getSource() + " " + path.getTarget();
            if (!nodeName.contains(path.getSource()) || !nodeName.contains(path.getTarget()) || path.getSource()
                .equals(path.getTarget())) {
                LOGGER.error("[ImportTaskValidCheker] path illegal, path={}", path);
                throw ExceptionUtil.createException(ExceptionInternationalConstant.IMPORT_PATH_VALID_ERROR, path.getSource(), path.getTarget());
            }
            if (sourceTargetList.contains(sourceTarget)) {
                LOGGER.error("[ImportTaskValidCheker] path illegal, source and target repeats ,path={}", path);
                throw ExceptionUtil.createException(ExceptionInternationalConstant.IMPORT_PATH_VALID_ERROR, path.getSource(), path.getTarget());
            } else {
                sourceTargetList.add(sourceTarget);
            }
            if (!path.getSource().equals("main")) {
                String symbol = path.getCondition().substring(0, 2);
                String numberString = path.getCondition().substring(2);
                float number = Float.parseFloat(numberString);
                if (!numberString.contains(".") || number < 0 || number > 100) {
                    return false;
                }
                switch (symbol) {
                    case "==":
                        break;
                    case ">=":
                        break;
                    case "<=":
                        break;
                    case "!=":
                        break;
                    default:
                        return false;
                }
            }
        }
        return true;
    }

    private boolean validAlarmTimeRange(String alarmTimeRange) {
        if (StringUtils.isEmpty(alarmTimeRange)) {
            return false;
        }
        if (!capacityAlarmTimeRangeSet.contains(alarmTimeRange)) {
            LOGGER.error("time range set not contains this time range");
            return false;
        }
        return true;
    }

    private boolean checkTaskImportConfig(TaskImportConfig taskImportConfig) {
        Integer configUpdateMode = taskImportConfig.getConfigUpdateMode();
        Integer nodeUpdateMode = taskImportConfig.getNodeUpdateMode();
        // 1为保持默认配置，2为使用新配置
        if (configUpdateMode != null && configUpdateMode != 1 && configUpdateMode != 2) {
            return false;
        }
        // 1为保持默认配置，2为使用新配置，3为使用新配置但是尝试保留老阈值
        if (nodeUpdateMode != null && nodeUpdateMode != 1 && nodeUpdateMode != 2 && nodeUpdateMode != 3) {
            return false;
        }
        return true;
    }

}
