/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskswitch;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.ConfigurationUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.InternationalConstant;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.DRTaskSwitchType;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.datahandle.ColumnAssemble;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.datahandle.DisasterRecoveryDataHandle;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.datahandle.PermissionsHandle;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.dto.MemberStatus;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.dto.SwitchAction;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.dto.SwitchActionType;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.dto.SwitchHandle;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.FlowClient;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.MmlHelper;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.model.flow.BatchCreateTaskResponse;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.model.flow.FlowExecuteStatus;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.model.flow.FlowTask;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.model.flow.InputNode;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.model.flow.InputParameterRequest;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.model.flow.ParamTemplate;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.model.flow.QueryInputParameterResponse;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.model.mml.AddTaskInfo;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskresult.model.mml.MmlTaskInfo;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskschedule.DisasterRecoveryScheduleCenter;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskswitch.model.DisasterRecoverySwitchTaskInstance;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskswitch.state.DisasterRecoveryTaskSwitchFlow;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskswitch.thread.DisasterRecoveryTaskSwitchHandle;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.taskswitch.thread.DisasterRecoveryTaskSwitchThreadPool;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.util.DomainUtil;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.util.TaskShowConstant;
import com.huawei.i2000.dvanalysisengineservice.business.disasterrecovery.util.TaskSwitchConstant;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.DisasterRecoveryAlarmUniquePos;
import com.huawei.i2000.dvanalysisengineservice.dao.disasterrecovery.DisasterRecoveryTaskExecutionDao;
import com.huawei.i2000.dvanalysisengineservice.dao.disasterrecovery.DisasterRecoveryTaskShowDao;
import com.huawei.i2000.dvanalysisengineservice.dao.disasterrecovery.DisasterRecoveryTaskSwitchDao;
import com.huawei.i2000.dvanalysisengineservice.dao.disasterrecovery.DisaterRecoveryTaskDao;
import com.huawei.i2000.dvanalysisengineservice.model.AlarmDetailsData;
import com.huawei.i2000.dvanalysisengineservice.model.DisasterRecoveryAction;
import com.huawei.i2000.dvanalysisengineservice.model.DisasterRecoverySwitchResult;
import com.huawei.i2000.dvanalysisengineservice.model.FlowHost;
import com.huawei.i2000.dvanalysisengineservice.model.GroupMember;
import com.huawei.i2000.dvanalysisengineservice.model.MemberDetailsData;
import com.huawei.i2000.dvanalysisengineservice.model.MmlActionModel;
import com.huawei.i2000.dvanalysisengineservice.model.MmlSwitchParam;
import com.huawei.i2000.dvanalysisengineservice.model.MonitorGroupModel;
import com.huawei.i2000.dvanalysisengineservice.model.Paging;
import com.huawei.i2000.dvanalysisengineservice.model.QueryResultAlarmInfoParam;
import com.huawei.i2000.dvanalysisengineservice.model.QueryResultInfoParam;
import com.huawei.i2000.dvanalysisengineservice.model.QuerySwitchResultParam;
import com.huawei.i2000.dvanalysisengineservice.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineservice.model.SwitchBackParam;
import com.huawei.i2000.dvanalysisengineservice.model.SwitchCheckParam;
import com.huawei.i2000.dvanalysisengineservice.model.SwitchExecuteParam;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;
import com.huawei.i2000.dvanalysisengineservice.util.exception.ExceptionInternationalConstant;
import com.huawei.i2000.dvanalysisengineservice.util.exception.ExceptionUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 智能容灾任务切换接口实现类
 *
 * <AUTHOR>
 * @since 2022/8/13
 */
@Service
public class DisasterRecoveryTaskSwitchServiceImpl implements DisasterRecoveryTaskSwitchService {

    private static final OssLog LOGGER = OssLogFactory.getLogger(DisasterRecoveryTaskSwitchServiceImpl.class);

    private static final int FAIL_CODE = -1;

    private static final int RUNTIME_ERROR = -2;

    private static final int PERMISSION_ERROR = -3;

    private static final int FLOW_QUERY_ERROR = -4;

    private static final int GROUP_MEMBER_QUERY_ERROR = -5;

    /**
     * MML回切
     */
    private static final String SWITCH_BACK = "SWITCH_BACK";

    /**
     * 保持活跃时间
     */
    private static final long ALIVE_TIME = 10L;

    /**
     * 队列容量
     */
    private static final int QUEUE_CAPACITY = 2048;

    /**
     * sleepTime
     */
    private static final long SLEEP_TIME = 3000L;

    /**
     * MML command timeout
     */
    private static final long MML_COMMAND_TIME_OUT = 180000L;

    private final ThreadPoolExecutor mmlCommandExecutor = new ThreadPoolExecutor(
        ConfigurationUtil.resultHandleIndicatorExecutorSize(), ConfigurationUtil.resultHandleIndicatorExecutorSize(),
        ALIVE_TIME, TimeUnit.MINUTES, new LinkedBlockingQueue<>(QUEUE_CAPACITY),
        new CustomizableThreadFactory("mml-switch-thread-"));

    @Autowired
    DisasterRecoveryTaskShowDao taskShowDao;

    @Autowired
    DisasterRecoveryTaskSwitchDao taskSwitchDao;

    @Autowired
    DisaterRecoveryTaskDao drTaskDao;

    @Autowired
    DisasterRecoveryTaskExecutionDao disasterRecoveryTaskExecutionDao;

    @Autowired
    DisasterRecoveryScheduleCenter scheduleCenter;

    @Override
    public ResponseResult switchCheck(HttpContext context, SwitchCheckParam switchCheckParam) throws ServiceException {
        // 任务检查
        ResponseResult result = new ResponseResult();
        result.setResultCode(0);
        List<String> selectMembers = switchCheckParam.getSelectMembers();
        DisasterRecoveryTaskSwitchHandle switchHandle
            = DisasterRecoveryDataHandle.createDisasterRecoveryTaskSwitchHandler();
        PermissionsHandle permissionsHandle = DisasterRecoveryDataHandle.createPermissionsHandle();

        if (!permissionsHandle.containAllDnPermission(context, selectMembers)) {
            LOGGER.error("[switchCheck] dn permission error.");
            result.setResultCode(PERMISSION_ERROR);
            result.setResultMessage("dn permission error.");
            return result;
        }

        if (!switchHandle.isSelectMemberSwitched(switchCheckParam.getTaskID(), switchCheckParam.getSelectMembers())) {
            result.setResultCode(FAIL_CODE);
            result.setResultMessage(ResourceUtil.getMessage(InternationalConstant.DISASTER_RECOVERY_SWITCH_CHECK_ERROR,
                ContextUtils.getContext().getLocale()));
            return result;
        }

        // 启动检查任务，返回taskInstanceID
        String monitorCheckTaskID = scheduleCenter.handleTask(
            disasterRecoveryTaskExecutionDao.getTaskByTaskId(switchCheckParam.getTaskID()));
        LOGGER.info("[switchCheck] The checkTaskID is {}", monitorCheckTaskID);
        // 数据库插入记录
        String switchTaskID = UUID.randomUUID().toString();

        DisasterRecoverySwitchResult initResult = new DisasterRecoverySwitchResult();
        initInsertSwitchResult(initResult, switchCheckParam.getTaskID(), switchCheckParam.getGroupID(),
            switchCheckParam.getExecutor(), switchTaskID, SwitchActionType.PREPARING.getStatus(),
            switchCheckParam.getSelectMembers(), SwitchAction.SWITCH.getStatus(), monitorCheckTaskID);

        if (!insertInitResult(initResult)) {
            result.setResultCode(FAIL_CODE);
            result.setResultMessage("[switchCheck] insert init data error");
            return result;
        }

        String userID = getUserID(context);
        List<String> permissionDns = DomainUtil.getDnsWithPermAnyUser(context);
        // 异步查询容灾监控项检查任务
        switchCheckExecute(switchCheckParam, monitorCheckTaskID, switchTaskID, userID, permissionDns);

        QuerySwitchResultParam resultParam = new QuerySwitchResultParam();
        resultParam.setSwitchTaskID(switchTaskID);
        result.setData(resultParam);
        return result;
    }

    private void switchCheckExecute(SwitchCheckParam switchCheckParam, String monitorCheckTaskID, String switchTaskID,
        String userID, List<String> permissionDns) {
        DisasterRecoverySwitchTaskInstance taskInstance = new DisasterRecoverySwitchTaskInstance();
        taskInstance.setTaskID(switchCheckParam.getTaskID());
        taskInstance.setGroupID(switchCheckParam.getGroupID());
        taskInstance.setCheckTaskID(monitorCheckTaskID);
        taskInstance.setSwitchTaskID(switchTaskID);
        taskInstance.setExecutor(userID);
        taskInstance.setStartTime(System.currentTimeMillis());
        taskInstance.setSelectMembers(switchCheckParam.getSelectMembers());
        taskInstance.setQueryType(SwitchHandle.MONITOR_QUERY);
        taskInstance.setPermissionDns(permissionDns);
        DisasterRecoveryTaskSwitchThreadPool.executeQueryThread(taskInstance);
    }

    private void initInsertSwitchResult(DisasterRecoverySwitchResult initResult, String taskID, String groupID,
        String executor, String taskInstanceID, String status, List<String> selectMember, String switchAction,
        String checkTaskID) {
        initResult.setTaskID(taskID);
        initResult.setGroupID(groupID);
        initResult.setExecutor(executor);
        initResult.setTaskInstanceID(taskInstanceID);
        initResult.setFlowExeStatus(status);
        initResult.setFlowParameter(JSONObject.toJSONString(selectMember));
        initResult.setHealSucc(switchAction);
        initResult.setCheckTaskID(checkTaskID);
        initResult.setStartTime(System.currentTimeMillis());
    }

    @Override
    public ResponseResult querySwitchResult(HttpContext context, QuerySwitchResultParam querySwitchResultParam) {

        try {
            // 根据switchId获取当前切换任务
            String switchTaskID = querySwitchResultParam.getSwitchTaskID();
            DisasterRecoverySwitchResult disasterRecoverySwitchResult = taskSwitchDao.queryOneActionResult(
                switchTaskID);
            DisasterRecoveryTaskSwitchFlow disasterRecoveryTaskSwitchFlow = new DisasterRecoveryTaskSwitchFlow(
                disasterRecoverySwitchResult, context);

            String flowExeStatus = disasterRecoverySwitchResult.getFlowExeStatus();
            disasterRecoveryTaskSwitchFlow.generateStateResponseResult(flowExeStatus);

            return disasterRecoveryTaskSwitchFlow.getResult();
        } catch (ServiceException e) {
            LOGGER.error("[querySwitchResult] query switch result error", e);
        }

        ResponseResult result = new ResponseResult();
        result.setResultCode(-1);
        return result;
    }

    private String specialHandleFlowResult(FlowTask flowTask, String flowKey) {
        String flowResult = "";
        if (FlowExecuteStatus.INPUT.equals(flowTask.getStatus())) {
            // 查询流程参数
            String flowResultVariables = flowTask.getProcessVariables();
            JSONObject flowObject = JSONObject.parseObject(flowResultVariables);
            if (flowObject != null) {
                flowResult = flowObject.getString(flowKey);
            }
        }

        return flowResult;
    }

    @Override
    public ResponseResult switchExecute(HttpContext context, SwitchExecuteParam switchExecuteParam)
        throws ServiceException {
        ResponseResult result = new ResponseResult();
        result.setResultCode(0);
        String exeAction = switchExecuteParam.getSwitchAction();
        DisasterRecoverySwitchResult disasterRecoverySwitchResult = taskSwitchDao.queryOneActionResult(
            switchExecuteParam.getSwitchTaskID());
        if (disasterRecoverySwitchResult == null || disasterRecoverySwitchResult.getEndTime() != null) {
            taskSwitchDao.updateFlowStatus(SwitchActionType.SWITCH_FAILED.getStatus(),
                switchExecuteParam.getSwitchTaskID());
            return result;
        }
        ResponseResult checkGroupMemberResult = checkGroupMember(
            disasterRecoveryTaskExecutionDao.getGroupMemberByTaskId(disasterRecoverySwitchResult.getTaskID()));
        if (checkGroupMemberResult != null) {
            return checkGroupMemberResult;
        }

        String flowTaskID = disasterRecoverySwitchResult.getFlowTaskID();
        FlowClient instance = FlowClient.getInstance();
        FlowTask flowTask = instance.queryFlowTask(instance.initQueryFlowTaskRequest(flowTaskID));

        if (flowTask == null || StringUtils.isEmpty(flowTask.getFlowId())) {
            dealExecuteErrorResult(result, disasterRecoverySwitchResult, "query flowTask error.");
            return result;
        }

        String flowID = flowTask.getFlowId();

        ResponseResult permissionJudgeResult = permissionJudge(context, result, disasterRecoverySwitchResult);
        if (permissionJudgeResult != null) {
            return permissionJudgeResult;
        }

        // 流程编排交互节点赋值->查询交互节点->获取节点流程参数->赋值修改
        QueryInputParameterResponse queryInputNodeParam = instance.queryInputNodeParam(
            instance.initQueryInputParameterRequest(flowTaskID, flowID));

        switchFlowStatusErrorScene(disasterRecoverySwitchResult, result, queryInputNodeParam);
        if (result.getResultCode() == FAIL_CODE) {
            return result;
        }

        InputParameterRequest inputParameterRequest = assembleInputParameterRequest(queryInputNodeParam, exeAction,
            instance, disasterRecoverySwitchResult);

        if (!instance.setInputParameterRequest(getUserID(context), inputParameterRequest)) {
            dealExecuteErrorResult(result, disasterRecoverySwitchResult, "modify inputParam error");
            return result;
        }

        // mml命令切换
        String disasterTaskID = mmlSwitch(switchExecuteParam, disasterRecoverySwitchResult);

        // 更改状态为执行中
        disasterRecoverySwitchResult.setFlowExeStatus(SwitchActionType.EXECUTING.getStatus());
        disasterRecoverySwitchResult.setDiagSucc(exeAction);
        disasterRecoverySwitchResult.setDisasterTaskID(disasterTaskID);
        taskSwitchDao.updateActionResult(disasterRecoverySwitchResult);

        // 执行定时查询结果线程
        switchResultExecute(switchExecuteParam, disasterRecoverySwitchResult.getTaskID(),
            disasterRecoverySwitchResult.getGroupID(), flowTaskID, flowID,
            JSON.parseArray(disasterRecoverySwitchResult.getFlowParameter(), String.class));

        return result;
    }

    private ResponseResult checkGroupMember(List<GroupMember> members) {
        ResponseResult result = new ResponseResult();
        List<DN> dns = members.stream().map(member -> new DN(member.getDn())).collect(Collectors.toList());
        List<ManagedObject> managedObjectList = MITManagerClient.newInstance().getMoByDns(dns);
        Map<String, String> dnMap = new HashMap<>();
        managedObjectList.forEach(obj -> dnMap.put(obj.getDN().getValue(), obj.getName()));
        for (GroupMember member : members) {
            if (!dnMap.containsKey(member.getDn()) || !dnMap.get(member.getDn()).equals(member.getDnName())) {
                LOGGER.error("DN {} not exist in resources or not match with DN name {}, please check.", member.getDn(),
                    member.getDnName());
                result.setResultCode(GROUP_MEMBER_QUERY_ERROR);
                result.setResultMessage("group member error.");
                return result;
            }
        }

        return null;
    }

    private static InputParameterRequest assembleInputParameterRequest(QueryInputParameterResponse queryInputNodeParam,
        String exeAction, FlowClient instance, DisasterRecoverySwitchResult disasterRecoverySwitchResult) {
        // 修改目标参数
        InputNode inputNode = queryInputNodeParam.getInputNodes().get(0);
        List<ParamTemplate> paramTemplates = inputNode.getInputParameters();
        paramTemplates.forEach(paramTemplate -> {
            if (TaskSwitchConstant.SELECT_SWITCH.equals(paramTemplate.getName())) {
                paramTemplate.setValue(exeAction);
            }
        });

        // 赋值交互节点流程编排请求
        return instance.initSetInputParameterRequest(disasterRecoverySwitchResult.getFlowTaskID(),
            queryInputNodeParam.getInputNodes());
    }

    private String mmlSwitch(SwitchExecuteParam switchExecuteParam,
        DisasterRecoverySwitchResult disasterRecoverySwitchResult) {
        MonitorGroupModel monitorGroupModel = drTaskDao.queryGroup(disasterRecoverySwitchResult.getGroupID());
        if (monitorGroupModel == null) {
            LOGGER.error("group is null");
            return StringUtils.EMPTY;
        }
        if (TaskSwitchConstant.SWITCH_ACTION_EXECUTOR.equals(switchExecuteParam.getSwitchAction())
            && DRTaskSwitchType.MML.equals(monitorGroupModel.getSwitchType())) {
            MmlSwitchParam mmlSwitchParam = new MmlSwitchParam();
            mmlSwitchParam.setTaskID(disasterRecoverySwitchResult.getTaskID());
            mmlSwitchParam.setGroupID(disasterRecoverySwitchResult.getGroupID());
            mmlSwitchParam.setSelectMembers(
                JSON.parseArray(disasterRecoverySwitchResult.getFlowParameter(), String.class));
            return mmlSwitchHandle(mmlSwitchParam);
        }
        return StringUtils.EMPTY;
    }

    private void dealExecuteErrorResult(ResponseResult result,
        DisasterRecoverySwitchResult disasterRecoverySwitchResult, String message) {
        result.setResultCode(FLOW_QUERY_ERROR);
        result.setResultMessage(message);
        // 更改状态为切换失败
        disasterRecoverySwitchResult.setFlowExeStatus(SwitchActionType.SWITCH_FAILED.getStatus());
        taskSwitchDao.updateActionResult(disasterRecoverySwitchResult);
        taskSwitchDao.updateMemberStatusByGroupMemberID(MemberStatus.SWITCH_FAILED.getStatus(),
            JSON.parseArray(disasterRecoverySwitchResult.getFlowParameter(), String.class));
        LOGGER.error("[dealExecuteErrorResult] query or modify flow error.");
    }

    private ResponseResult permissionJudge(HttpContext context, ResponseResult result,
        DisasterRecoverySwitchResult disasterRecoverySwitchResult) throws ServiceException {
        PermissionsHandle permissionsHandle = DisasterRecoveryDataHandle.createPermissionsHandle();
        // 是否具有流程编排权限以及已选择网元的权限
        if (!permissionsHandle.containHealingPermission(context, disasterRecoverySwitchResult.getGroupID())) {
            LOGGER.error("[switchExecute] flow permission error.");
            result.setResultCode(FLOW_QUERY_ERROR);
            result.setResultMessage("flow permission error.");
            return result;
        }

        if (!permissionsHandle.containAllDnPermission(context,
            JSON.parseArray(disasterRecoverySwitchResult.getFlowParameter(), String.class))) {
            LOGGER.error("[switchExecute] dn permission error.");
            result.setResultCode(PERMISSION_ERROR);
            result.setResultMessage("dn permission error.");
            return result;
        }

        return null;
    }

    private void switchFlowStatusErrorScene(DisasterRecoverySwitchResult disasterRecoverySwitchResult,
        ResponseResult result, QueryInputParameterResponse queryInputNodeParam) {
        if (CollectionUtils.isEmpty(queryInputNodeParam.getInputNodes())) {
            LOGGER.error("input node is empty.");
            taskSwitchDao.updateFlowStatus(SwitchActionType.SWITCH_FAILED.getStatus(),
                disasterRecoverySwitchResult.getTaskInstanceID());
            taskSwitchDao.updateMemberStatusByGroupMemberID(MemberStatus.SWITCH_FAILED.getStatus(),
                JSON.parseArray(disasterRecoverySwitchResult.getFlowParameter(), String.class));
            result.setResultCode(FAIL_CODE);
            result.setResultMessage("Contain inputNodeParam error");
        }
    }

    private void switchResultExecute(SwitchExecuteParam switchExecuteParam, String taskID, String groupID,
        String flowTaskID, String flowID, List<String> selectMembers) {
        // 执行结果定时任务查询
        DisasterRecoverySwitchTaskInstance taskInstance = new DisasterRecoverySwitchTaskInstance();
        taskInstance.setSwitchTaskID(switchExecuteParam.getSwitchTaskID());
        taskInstance.setTaskID(taskID);
        taskInstance.setGroupID(groupID);
        taskInstance.setFlowID(flowID);
        taskInstance.setFlowTaskID(flowTaskID);
        taskInstance.setSwitchAction(switchExecuteParam.getSwitchAction());
        taskInstance.setSelectMembers(selectMembers);
        taskInstance.setStartTime(System.currentTimeMillis());
        taskInstance.setQueryType(SwitchHandle.SWITCH_EXECUTE_QUERY);
        DisasterRecoveryTaskSwitchThreadPool.executeQueryThread(taskInstance);
    }

    private boolean insertInitResult(DisasterRecoverySwitchResult initResult) {
        boolean insertInitResult = taskSwitchDao.insertInitCheckResult(initResult);
        if (!insertInitResult) {
            LOGGER.error("[setInitResult] insert init Result error.");
            return false;
        }
        return true;
    }

    private List<FlowHost> setFlowHosts(List<DisasterRecoveryAction> groupActions) {
        List<FlowHost> flowHosts = new ArrayList<>();
        groupActions.forEach(groupAction -> {
            FlowHost flowHost = new FlowHost();
            flowHost.setFlowId(groupAction.getFlowID());
            flowHost.setGroupId(groupAction.getGroupID());
            flowHost.setFlowName(groupAction.getFlowName());
            flowHost.setFlowGroup(groupAction.getFlowGroup());
            flowHost.setNodeId(groupAction.getNodeID());
            flowHost.setAtomId(groupAction.getAtomID());
            flowHost.setHostContent(groupAction.getHostContent());
            flowHosts.add(flowHost);
        });
        return flowHosts;
    }

    @Override
    public ResponseResult switchBack(HttpContext context, SwitchBackParam switchBackParam) throws ServiceException {
        ResponseResult result = new ResponseResult();
        // 回切前置条件检查
        if (!checkSwitchBackCondition(switchBackParam, result)) {
            return result;
        }
        MonitorGroupModel monitorGroupModel = drTaskDao.queryGroup(switchBackParam.getGroupID());
        if ("0".equals(monitorGroupModel.getSwitchType())) {

            // 查询流程ips
            DisasterRecoveryTaskSwitchHandle switchTaskHandle
                = DisasterRecoveryDataHandle.createDisasterRecoveryTaskSwitchHandler();
            List<String> permissionDns = DomainUtil.getDnsWithPermAnyUser(context);
            String userID = getUserID(context);
            switchBackParam.setExecutor(userID);
            JSONObject flowParam = switchTaskHandle.queryBackFlowParameter(switchBackParam, permissionDns);

            // 查询回切流程
            List<DisasterRecoveryAction> groupSwitchAction = taskSwitchDao.queryGroupAction(
                switchBackParam.getGroupID(), SwitchAction.SWITCHBACK.getStatus());
            List<FlowHost> flowHosts = setFlowHosts(groupSwitchAction);

            // 启动流程
            BatchCreateTaskResponse batchCreateTaskResponse = FlowClient.getInstance()
                .startFlow(userID, FlowClient.getInstance().initStartFlowRequest(flowHosts, flowParam, true, null));

            if (CollectionUtils.isEmpty(batchCreateTaskResponse.getTaskIds())) {
                throw ExceptionUtil.createExceptionByMessage(ResourceUtil.getMessage(
                    ExceptionInternationalConstant.DISASTER_RECOVERY_BACK_SWITCH_FAIL_FLOW_ERROR,
                    ContextUtils.getContext().getLocale()));
            }

            String flowTaskID = batchCreateTaskResponse.getTaskIds().get(0);
            String taskInstanceID = UUID.randomUUID().toString();
            // 数据库更新
            if (!updateSwitchActionData(switchBackParam, taskInstanceID, flowTaskID, result)) {
                return result;
            }
            // 设置选中成员未回切中
            taskSwitchDao.updateMemberStatusByGroupMemberID(MemberStatus.SWITCH_BACK_PROCESSING.getStatus(),
                switchBackParam.getSelectMembers());

            // 执行定时查询结果线程
            switchResultBack(switchBackParam, flowTaskID, taskInstanceID);

        } else {
            handleMmlSwitchBack(switchBackParam);
        }
        result.setResultCode(0);
        return result;
    }

    private void handleMmlSwitchBack(SwitchBackParam switchBackParam) {
        MmlActionModel mmlActionModel = drTaskDao.queryMmlAction(switchBackParam.getGroupID());

        List<MemberDetailsData> memberDetailsDataList = taskShowDao.getMemberInfoByGroupMemberIDs(
            switchBackParam.getSelectMembers());
        List<String> dns = new ArrayList<>();
        memberDetailsDataList.forEach(memberDetailsData -> dns.add(memberDetailsData.getDN()));

        // 创建MML任务并执行
        String taskName = "mmlSwitchBack" + UUID.randomUUID();
        String taskId = createTaskAndExec(taskName, mmlActionModel.getSwitchBackCommand(), dns);
        long starTime = System.currentTimeMillis();

        // 异步查询MML执行结果 更新成员状态
        Runnable runnable = () -> {
            while (true) {
                try {
                    Thread.sleep(SLEEP_TIME);
                } catch (InterruptedException e) {
                    LOGGER.error("mml command thread is interrupt!");
                }

                String executeResult = MmlHelper.queryMmlTaskResult(taskId);
                LOGGER.debug("executeResult={}", executeResult);

                if (handleExecuteResult(taskId, dns, executeResult, "SWITCH_BACK")) {
                    break;
                }

                if (System.currentTimeMillis() - starTime > MML_COMMAND_TIME_OUT) {
                    taskSwitchDao.updateMemberStatusByGroupMemberID(MemberStatus.SWITCH_BACK_FAILED.getStatus(),
                        switchBackParam.getSelectMembers());
                    MmlHelper.deleteMmlTasks(Arrays.asList(taskId));
                    break;
                }
            }
        };
        mmlCommandExecutor.execute(runnable);
        // 设置选中成员为回切中
        taskSwitchDao.updateMemberStatusByGroupMemberID(MemberStatus.SWITCH_BACK_PROCESSING.getStatus(),
            switchBackParam.getSelectMembers());
    }

    private boolean handleExecuteResult(String taskId, List<String> dns, String executeResult, String type) {
        if (!"-1".equals(executeResult)) {
            Map<String, String> resultMap = JSONObject.parseObject(executeResult, new MapTypeReference());
            if (resultMap != null) {
                String status = resultMap.get("status");
                if (StringUtils.isNotEmpty(status) && status.equals("completed")) {
                    List<String> healthDn = new ArrayList<>();
                    List<String> unHealthDn = new ArrayList<>();
                    for (String dn : dns) {
                        String dnStatus = resultMap.get(dn);
                        if ("failed".equals(dnStatus) || "part_success".equals(dnStatus)) {
                            unHealthDn.add(dn);
                        }
                        if ("success".equals(dnStatus)) {
                            healthDn.add(dn);
                        }
                    }
                    if (CollectionUtils.isNotEmpty(healthDn)) {
                        taskSwitchDao.updateMemberStatusByDn(type.equals("MML_SWITCH")
                            ? MemberStatus.SWITCHED.getStatus()
                            : MemberStatus.SWITCH_BACK.getStatus(), healthDn);
                    }
                    if (CollectionUtils.isNotEmpty(unHealthDn)) {
                        taskSwitchDao.updateMemberStatusByDn(type.equals("MML_SWITCH")
                            ? MemberStatus.SWITCH_FAILED.getStatus()
                            : MemberStatus.SWITCH_BACK_FAILED.getStatus(), unHealthDn);
                    }

                    if (type.equals(SWITCH_BACK)) {
                        MmlHelper.deleteMmlTasks(Collections.singletonList(taskId));
                    }
                    LOGGER.info("mml command end.");
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public ResponseResult mmlSwitch(HttpContext context, MmlSwitchParam mmlSwitchParam) throws ServiceException {
        // 任务检查
        ResponseResult result = new ResponseResult();
        result.setResultCode(0);
        List<String> selectMembers = mmlSwitchParam.getSelectMembers();
        DisasterRecoveryTaskSwitchHandle switchHandle
            = DisasterRecoveryDataHandle.createDisasterRecoveryTaskSwitchHandler();
        PermissionsHandle permissionsHandle = DisasterRecoveryDataHandle.createPermissionsHandle();

        MonitorGroupModel monitorGroupModel = drTaskDao.queryGroup(mmlSwitchParam.getGroupID());

        if (!"1".equals(monitorGroupModel.getSwitchType())) {
            LOGGER.error("[mmlSwitch] switch Type error.");
            result.setResultCode(FAIL_CODE);
            result.setResultMessage(
                ResourceUtil.getMessage(InternationalConstant.DISASTER_RECOVERY_MML_SWITCH_TYPE_ERROR,
                    ContextUtils.getContext().getLocale()));
            return result;
        }

        if (!permissionsHandle.containAllDnPermission(context, selectMembers)) {
            LOGGER.error("[mmlSwitch] dn permission error.");
            result.setResultCode(PERMISSION_ERROR);
            result.setResultMessage("dn permission error.");
            return result;
        }

        if (!switchHandle.isSelectMemberSwitched(mmlSwitchParam.getTaskID(), mmlSwitchParam.getSelectMembers())) {
            result.setResultCode(FAIL_CODE);
            result.setResultMessage(
                ResourceUtil.getMessage(InternationalConstant.DISASTER_RECOVERY_MML_SWITCH_MEMBER_ERROR,
                    ContextUtils.getContext().getLocale()));
            return result;
        }

        mmlSwitchHandle(mmlSwitchParam);

        // 设置选中成员切换中
        taskSwitchDao.updateMemberStatusByGroupMemberID(MemberStatus.SWITCHING.getStatus(),
            mmlSwitchParam.getSelectMembers());

        result.setResultCode(0);
        return result;
    }

    private String mmlSwitchHandle(MmlSwitchParam mmlSwitchParam) {
        MmlActionModel mmlActionModel = drTaskDao.queryMmlAction(mmlSwitchParam.getGroupID());

        List<MemberDetailsData> memberDetailsDataList = taskShowDao.getMemberInfoByGroupMemberIDs(
            mmlSwitchParam.getSelectMembers());
        List<String> dns = new ArrayList<>();
        memberDetailsDataList.forEach(memberDetailsData -> dns.add(memberDetailsData.getDN()));

        // 创建MML任务并执行
        String taskName = "mmlSwitch" + UUID.randomUUID();
        String taskId = createTaskAndExec(taskName, mmlActionModel.getSwitchCommand(), dns);

        long starTime = System.currentTimeMillis();
        // 异步查询MML执行结果 更新成员状态
        Runnable runnable = () -> {
            while (true) {
                try {
                    Thread.sleep(SLEEP_TIME);
                } catch (InterruptedException e) {
                    LOGGER.error("mml command thread is interrupt!");
                }

                String executeResult = MmlHelper.queryMmlTaskResult(taskId);
                LOGGER.debug("executeResult={}", executeResult);

                if (handleExecuteResult(taskId, dns, executeResult, "MML_SWITCH")) {
                    break;
                }

                if (System.currentTimeMillis() - starTime > MML_COMMAND_TIME_OUT) {
                    taskSwitchDao.updateMemberStatusByGroupMemberID(MemberStatus.SWITCH_FAILED.getStatus(),
                        mmlSwitchParam.getSelectMembers());
                    break;
                }
            }
        };
        mmlCommandExecutor.execute(runnable);

        return taskId;
    }

    private String createTaskAndExec(String taskName, String mmlCommand, List<String> dns) {
        String userName = ContextUtils.getContext().getUsername();
        MmlTaskInfo mmlTaskInfo = new MmlTaskInfo();
        mmlTaskInfo.setTaskName(taskName);
        mmlTaskInfo.setCreator(userName);
        mmlTaskInfo.setDns(dns.toString());
        AddTaskInfo addTaskInfo = new AddTaskInfo();
        addTaskInfo.setMmlText(mmlCommand);
        addTaskInfo.setTaskInfo(mmlTaskInfo);
        String taskId = MmlHelper.addMmlTask(addTaskInfo);
        MmlHelper.executeMmlTasks(userName, Collections.singletonList(taskId));
        return taskId;
    }

    private void switchResultBack(SwitchBackParam switchBackParam, String flowTaskID, String taskInstanceID) {
        DisasterRecoverySwitchTaskInstance taskInstance = new DisasterRecoverySwitchTaskInstance();
        taskInstance.setTaskID(switchBackParam.getTaskID());
        taskInstance.setGroupID(switchBackParam.getGroupID());
        taskInstance.setFlowTaskID(flowTaskID);
        taskInstance.setSwitchTaskID(taskInstanceID);
        taskInstance.setSelectMembers(switchBackParam.getSelectMembers());
        taskInstance.setStartTime(System.currentTimeMillis());
        taskInstance.setQueryType(SwitchHandle.SWITCH_BACK_QUERY);
        DisasterRecoveryTaskSwitchThreadPool.executeQueryThread(taskInstance);
    }

    private boolean checkSwitchBackCondition(SwitchBackParam switchBackParam, ResponseResult result) {
        List<String> selectMember = switchBackParam.getSelectMembers();
        if (CollectionUtils.isEmpty(selectMember)) {
            LOGGER.error("[switchBack] select member is switched.");
            result.setResultCode(FAIL_CODE);
            result.setResultMessage("There is no select member");

        }
        // 检查成员是否已切换
        List<String> membersStatus = taskSwitchDao.querySelectMemberStatus(selectMember);
        for (String status : membersStatus) {
            if (!(MemberStatus.SWITCHED.getStatus().equals(status) || MemberStatus.SWITCH_BACK_FAILED.getStatus()
                .equals(status))) {
                LOGGER.error("[switchBack] select member is not switched or switch back error.");
                result.setResultCode(FAIL_CODE);
                result.setResultMessage("The selected member status is not 'SWITCHED' or 'SWITCH_BACK_FAILED'");
                return false;
            }
        }
        return true;
    }

    private boolean updateSwitchActionData(SwitchBackParam switchBackParam, String taskInstanceID, String flowTaskID,
        ResponseResult result) {
        DisasterRecoverySwitchResult initResult = new DisasterRecoverySwitchResult();
        initInsertSwitchResult(initResult, switchBackParam.getTaskID(), switchBackParam.getGroupID(),
            switchBackParam.getExecutor(), taskInstanceID, SwitchActionType.PREPARING.getStatus(),
            switchBackParam.getSelectMembers(), SwitchAction.SWITCHBACK.getStatus(), "");

        if (!insertInitResult(initResult)) {
            LOGGER.error("[updateSwitchActionData] insertInitResult error");
            result.setResultCode(FAIL_CODE);
            result.setResultMessage("[switchCheck] insert init data error");
            return false;
        }

        boolean updateResult = taskSwitchDao.updateSwitchResultFlowTaskID(flowTaskID,
            SwitchActionType.STARTING.getStatus(), taskInstanceID);
        if (!updateResult) {
            LOGGER.error("[updateSwitchActionData] updateSwitchResultFlowTaskID error");
            result.setResultCode(FAIL_CODE);
            result.setResultMessage("[startSwitchFlow] update flowTaskID error");
            return false;
        }
        return true;
    }

    @Override
    public ResponseResult queryResultInfo(HttpContext context, QueryResultInfoParam queryResultInfoParam)
        throws ServiceException {
        ResponseResult result = new ResponseResult();
        result.setResultCode(0);
        String flowKey = queryResultInfoParam.getFlowKey();
        String switchTaskID = queryResultInfoParam.getSwitchTaskID();
        DisasterRecoverySwitchResult disasterRecoverySwitchResult = taskSwitchDao.queryOneActionResult(switchTaskID);
        String flowTaskID = disasterRecoverySwitchResult.getFlowTaskID();

        if (StringUtils.isEmpty(flowTaskID)) {
            LOGGER.error("[queryResultInfo] The flowTaskID from database is empty.");
            result.setResultCode(FAIL_CODE);
            result.setResultMessage("The flowTaskID from database is empty.");
            return result;
        }
        PermissionsHandle permissionsHandle = DisasterRecoveryDataHandle.createPermissionsHandle();
        if (!permissionsHandle.containHealingPermission(context, queryResultInfoParam.getGroupID())) {
            LOGGER.error("[queryResultInfo] The ihealing permission cant access.");
            result.setResultCode(FLOW_QUERY_ERROR);
            result.setResultMessage("The ihealing permission cant access.");
            return result;
        }

        FlowClient instance = FlowClient.getInstance();
        FlowTask flowTask = instance.queryFlowTask(instance.initQueryFlowTaskRequest(flowTaskID));
        if (!(FlowExecuteStatus.INPUT.equals(flowTask.getStatus()) || FlowExecuteStatus.SUCCESS.equals(
            flowTask.getStatus()) || FlowExecuteStatus.FAILED.equals(flowTask.getStatus()))) {
            LOGGER.info("[queryResultInfo] The switch flow is still running");
            result.setResultCode(0);
            result.setResultMessage("The switch flow is still running");
            return result;
        }

        String flowResult = specialHandleFlowResult(flowTask, flowKey);
        // 解析flowResult
        DisasterRecoveryTaskSwitchHandle switchHandle
            = DisasterRecoveryDataHandle.createDisasterRecoveryTaskSwitchHandler();
        JSONObject parseObject = switchHandle.parseFlowResult(flowResult);
        permissionsHandle.filterCustomTable(context, parseObject, flowKey);
        if (parseObject.isEmpty()) {
            result.setResultCode(FAIL_CODE);
            result.setResultMessage("Parse the flow flowKey error");
            return result;
        }

        JSONObject resultObject = new JSONObject();
        resultObject.put("customContent", parseObject.getJSONArray(flowKey));

        // 用户不为admin权限进行提示
        resultObject.put(TaskShowConstant.USER_PERMISSION_TIPS, !ContextUtils.getContext().getAdmin());
        result.setData(resultObject);
        return result;
    }

    @Override
    public ResponseResult queryResultAlarmInfo(HttpContext context, QueryResultAlarmInfoParam queryResultInfoParam)
        throws ServiceException {
        ResponseResult result = new ResponseResult();
        result.setResultCode(0);
        String switchTaskID = queryResultInfoParam.getSwitchTaskID();

        Paging paging = queryResultInfoParam.getPaging();
        if (paging != null && paging.getPageNumber() != null) {
            Integer offset = paging.getPageSize() * (paging.getPageNumber() - 1);
            paging.setOffset(offset);
        }

        String flowResult = taskSwitchDao.queryFlowResultByTaskInsID(switchTaskID);
        DisasterRecoveryTaskSwitchHandle switchHandle
            = DisasterRecoveryDataHandle.createDisasterRecoveryTaskSwitchHandler();
        JSONObject parseObject = switchHandle.parseFlowResult(flowResult);
        if (parseObject.isEmpty()) {
            return result;
        }

        String transitionMember = parseObject.getString(TaskSwitchConstant.FLOW_RESULTS_TRANSITION_MEMBER);
        if (StringUtils.isEmpty(transitionMember)) {
            result.setResultCode(FAIL_CODE);
            result.setResultMessage("The transitionMember is empty");
            return result;
        }

        List<String> transitionMemberList = JSONArray.parseArray(transitionMember, String.class);

        List<AlarmDetailsData> resultAlarmData = new ArrayList<>();
        List<String> permissionDns = DomainUtil.getDnsWithPermAnyUser(context);

        transitionMemberList.forEach(member -> {
            List<AlarmDetailsData> alarmDataList = taskShowDao.queryResultAlarmInfo(member, null);
            // 过滤有权限的网元
            alarmDataList.stream()
                .filter(alarmFilter -> permissionDns.contains(alarmFilter.getAlarmDN()))
                .forEach(alarmData -> {
                    List<DisasterRecoveryAlarmUniquePos> alarmPosIDs = JSONArray.parseArray(alarmData.getAlarmID(),
                        DisasterRecoveryAlarmUniquePos.class);
                    List<String> occurrenceTime = Arrays.asList(alarmData.getOccurrenceTimes().split(","));

                    HashMap<DisasterRecoveryAlarmUniquePos, String> alarmIDToOccurrence
                        = switchHandle.setAlarmIDOccurrenceMap(alarmPosIDs, occurrenceTime);
                    loadAlarmTableData(resultAlarmData, member, alarmData, alarmPosIDs, alarmIDToOccurrence,
                        queryResultInfoParam.getGroupID());

                });
        });

        JSONArray alarmData = JSONArray.parseArray(JSONObject.toJSONString(resultAlarmData));
        JSONArray alarmDisplayData = ColumnAssemble.getDisplayPagingRows(alarmData, paging);

        JSONObject alarmObject = new JSONObject();
        alarmObject.put(TaskShowConstant.ALARM_ID_LIST, alarmDisplayData);
        alarmObject.put(TaskShowConstant.COLUMN_VALUE_COUNT, resultAlarmData.size());
        alarmObject.put(TaskShowConstant.USER_PERMISSION_TIPS, !ContextUtils.getContext().getAdmin());
        result.setResultCode(0);
        result.setData(alarmObject);
        return result;
    }

    private void loadAlarmTableData(List<AlarmDetailsData> resultAlarmData, String member, AlarmDetailsData alarmData,
        List<DisasterRecoveryAlarmUniquePos> alarmPosIDs,
        HashMap<DisasterRecoveryAlarmUniquePos, String> alarmIDToOccurrence, String groupID) {
        for (DisasterRecoveryAlarmUniquePos alarmPosID : alarmPosIDs) {
            AlarmDetailsData alarmItem = new AlarmDetailsData();
            alarmItem.setAlarmID(alarmPosID.getAlarmId());
            alarmItem.setAlarmName(alarmPosID.getAlarmName());
            alarmItem.setAlarmDN(alarmData.getAlarmDN());
            alarmItem.setGroupMemberName(member);
            alarmItem.setOccurrenceTimes(alarmIDToOccurrence.get(alarmPosID));
            resultAlarmData.add(alarmItem);
        }
    }

    private String getUserID(HttpContext context) {
        // 如果是管理员要设置角色id 1,2因为管理员不一定叫admin
        if (ContextUtils.getContext().getAdmin()) {
            return "1";
        } else {
            return ContextUtils.getContext().getUserId();
        }
    }

    private static class MapTypeReference extends TypeReference<Map<String, String>> { }
}
