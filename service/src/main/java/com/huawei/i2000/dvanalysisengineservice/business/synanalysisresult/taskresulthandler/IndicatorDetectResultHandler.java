/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.taskresulthandler;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.cbb.fm.utils.AlarmAddInfoBuilder;
import com.huawei.i2000.dvanalysisengineservice.business.common.InternationalConstant;
import com.huawei.i2000.dvanalysisengineservice.business.common.TimeUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.businessstrategy.BusinessStrategyManager;
import com.huawei.i2000.dvanalysisengineservice.business.common.businessstrategy.StrategyConstant;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.conceptdrift.ConceptDriftManage;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.constant.SynAnalysisResultConstant;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.constant.SynAnalysisResultConstant.AlarmParameters;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.constant.SynAnalysisResultConstant.AnalysisResultTableName;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.constant.SynAnalysisResultConstant.Separator;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.AlarmRecord;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.IndicatorDetectResult;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.IndicatorMissDataOutlier;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.IndicatorOutlier;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.IndicatorThreshold;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.IndicatorTransferInfo;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.SendAlarmRecord;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.TaskPeriodInfo;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.util.CsvFileUtil;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.IndicatorDetectResultDao;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.IndicatorOutlierDao;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.IndicatorTransferDao;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.SendAlarmDao;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.TaskAnalysisResultDao;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.TaskExecutionResultDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskIndicatorDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.handlerimpl.TaskManageDao;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTask;
import com.huawei.i2000.dvanalysisengineservice.model.ModelParameter;
import com.huawei.i2000.dvanalysisengineservice.model.TaskIndicator;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;
import com.huawei.i2000.dvanalysisengineservice.util.operationlog.LogConstant;
import com.huawei.i2000.util.omc.OmcUtil;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 指标检测结果处理类
 *
 * <AUTHOR>
 * @since 2021/2/23
 */
@Component
public class IndicatorDetectResultHandler implements AnalysisResultHandler {
    private static final OssLog LOGGER = OssLogFactory.getLogger(IndicatorDetectResultHandler.class);

    private static final String NEW_OUTLIER_LIST = "newIndicatorOutlierList";

    private static final String UPDATE_OUTLIER_LIST = "updateIndicatorOutlierList";
    private static final String WINDOW_SIZE = "window_size";

    private static final String DETECT_NUM = "detect_num";

    private static final String MISS_POINT_SWITCH = "report_missing_points_switch";

    private static final String YES = "yes";

    private static final String DEFAULT_NULL = "N/A";

    private static final int MAX_DATA_SIZE = 200;

    private static final String SEND_STATUS_SUC = "success";

    private static final String SEND_STATUS_FAI = "fail";

    @Autowired
    IndicatorDetectResultDao indicatorDetectResultDao;

    @Autowired
    IndicatorOutlierDao indicatorOutlierDao;

    @Autowired
    TaskExecutionResultDao taskExecutionResultDao;

    @Autowired
    TaskIndicatorDao taskIndicatorDao;

    @Autowired
    TaskManageDao taskManageDao;

    @Autowired
    TaskAnalysisResultDao taskAnalysisResultDao;

    @Autowired
    ConceptDriftManage driftManage;

    @Autowired
    BusinessStrategyManager businessStrategyManager;

    @Autowired
    IndicatorTransferDao indicatorTransferDao;

    @Autowired
    SendAlarmDao sendAlarmDao;

    @Override
    public void synAnalysisResult(int taskId, long startTime, String taskPeriodType, String destFilePath) throws ServiceException {
    }

    @Override
    @Transactional(rollbackFor = ServiceException.class)
    public void synAnalysisResult(TaskPeriodInfo taskPeriodInfo) throws ServiceException {
        int taskId = taskPeriodInfo.getTaskId();
        long startTime = taskPeriodInfo.getStartTime();
        String taskPeriodType = taskPeriodInfo.getTaskPeriodType();
        String destFilePath = taskPeriodInfo.getDestFilePath();
        // 下载结果文件
        String fileNamePrefix = taskId + Separator.SEPARATOR_UNDERSCORE + startTime;
        List<File> fileList = CsvFileUtil.newDestFile(destFilePath, fileNamePrefix);
        if (CollectionUtils.isEmpty(fileList)) {
            LOGGER.error("fileList is empty!");
            throw new ServiceException("fileList is empty!");
        }

        if (taskPeriodType.equals(SynAnalysisResultConstant.TaskPeriodType.TASK_PERIOD_TRAINING)) {
            synTrainAnalysisResult(taskPeriodInfo.getTask(), startTime, taskPeriodType, fileList);
            return;
        }

        synDetectAnalysisResult(taskPeriodInfo.getTask(), startTime, taskPeriodType, fileList);
    }

    private void synDetectAnalysisResult(AnalysisTask task, long startTime, String taskPeriodType, List<File> fileList)
        throws ServiceException {
        Integer taskId = task.getTaskId();
        // 读取文件内容
        List<IndicatorDetectResult> indicatorDetectResults = new ArrayList<>();
        Set<String> successIndicatorIds = new HashSet<>();
        List<IndicatorDetectResult> failIndicatorDetectResults = new ArrayList<>();
        try {
            for (File file : fileList) {
                List<IndicatorDetectResult> indicatorDetectResultList;
                try {
                    indicatorDetectResultList = CsvFileUtil.readCsvFile(IndicatorDetectResult.class, file);
                } catch (IOException e) {
                    LOGGER.error("readCsvFile failed!" + e);
                    return;
                }
                if (CollectionUtils.isEmpty(indicatorDetectResultList)) {
                    LOGGER.warn("indicatorDetectResultList is empty!");
                    continue;
                }
                excludeFailedIndicators(successIndicatorIds, failIndicatorDetectResults, indicatorDetectResultList);
                // 根据阈值表数据判断结果里面哪些重复，去掉重复的结果
                judgResultDuplicate(indicatorDetectResultList, taskId);
                if (CollectionUtils.isNotEmpty(indicatorDetectResultList)) {
                    indicatorDetectResults.addAll(indicatorDetectResultList);
                }
            }

            indicatorDetectResults = missDataPointAbnormal(indicatorDetectResults, task);

            // 判断指标id和指标结果表是否为空
            if (judgIndicatorEmpty(successIndicatorIds, indicatorDetectResults, taskId, startTime, taskPeriodType, failIndicatorDetectResults)){
                return;
            }
            // 写异常检测算法详细结果表
            indicatorDetectResults = updateIndicatorAbnormal(indicatorDetectResults, task);
            updateIndicatorDetectResult(indicatorDetectResults, taskId, startTime);

            // 写异常检测算法阈值表
            updateIndicatorThreshold(indicatorDetectResults, taskId);

            // 计算异常区间，写指标异常表、上报/清除告警
            handleIndicatorDetectResultNew(indicatorDetectResults, task);
            // 判断是否所有指标都产生了推理结果
            judgeIndicatorCount(successIndicatorIds, taskId, startTime, taskPeriodType, failIndicatorDetectResults);
            deleteResultFile(fileList);
        } catch (Exception e) {
            LOGGER.error("[synAnalysisResult] synAnalysisResult failed! indicatorDetectResults={}, serviceException e {}",
                indicatorDetectResults, e);
            if (CollectionUtils.isNotEmpty(failIndicatorDetectResults)) {
                taskExecutionResultDao.setTaskPeriodFailureCause(taskId, startTime, taskPeriodType, constructFailureCause(failIndicatorDetectResults));
            }
            // 更新任务结果表
            taskExecutionResultDao.updateTaskPeriod(taskId, startTime, taskPeriodType, TaskConstant.EXECUTION_STATUS_FAILED);
            throw new ServiceException("synAnalysisResult failed! rollback!");
        }
    }

    private void excludeFailedIndicators(Set<String> successIndicatorIds, List<IndicatorDetectResult> failIndicatorDetectResults,
        List<IndicatorDetectResult> indicatorDetectResultList) {
        Set<String> missDataAbnormal = new HashSet<>();
        indicatorDetectResultList.removeIf(indicatorDetectResult -> {
            if (StringUtils.isNotEmpty(indicatorDetectResult.getFailureCause())) {
                failIndicatorDetectResults.add(indicatorDetectResult);
                if (indicatorDetectResult.getAbnormal() != null && indicatorDetectResult.getAbnormal()) {
                    missDataAbnormal.add(indicatorDetectResult.getIndicatorId());
                    return false;
                } else {
                    return true;
                }
            }
            successIndicatorIds.add(indicatorDetectResult.getIndicatorId());
            return false;
        });
        successIndicatorIds.removeAll(missDataAbnormal);
    }

    /**
     * 处理训练时生成的指标检测结果
     *
     * @param task task
     * @param startTime startTime
     * @param taskPeriodType taskPeriodType
     * @param fileList fileList
     * @throws ServiceException 服务异常
     */
    private void synTrainAnalysisResult(AnalysisTask task, long startTime, String taskPeriodType, List<File> fileList)
            throws ServiceException {
        Integer taskId = task.getTaskId();
        // 读取文件内容
        List<IndicatorDetectResult> indicatorDetectResults = new ArrayList<>();

        List<File> transferFileList = fileList.stream()
            .filter(u -> u.getName().endsWith(SynAnalysisResultConstant.PropertyFile.TRANSFER_CSV_FILE_END))
            .collect(Collectors.toList());

        List<File> trainFileList = fileList.stream()
            .filter(u -> !u.getName().endsWith(SynAnalysisResultConstant.PropertyFile.TRANSFER_CSV_FILE_END))
            .collect(Collectors.toList());
        updateIndicatorTransferResult(taskId, transferFileList);

        try {
            // 快速推理把上报的告警全部清除
            clearSentAlarmRecord(taskId);
            for (File file : trainFileList) {
                List<IndicatorDetectResult> indicatorDetectResultList;
                try {
                    indicatorDetectResultList = CsvFileUtil.readCsvFile(IndicatorDetectResult.class, file);
                } catch (IOException e) {
                    LOGGER.error("readCsvFile failed!" + e);
                    return;
                }
                indicatorDetectResults.addAll(indicatorDetectResultList);
            }
            if (indicatorDetectResults.isEmpty()) {
                taskExecutionResultDao.updateTaskPeriod(taskId, startTime, taskPeriodType, null);
                deleteResultFile(fileList);
                return;
            }
            // 将检测结果入库
            indicatorDetectResults = updateTrainIndicatorDetectResult(indicatorDetectResults, task, startTime);

            // 计算异常区间，写指标异常表
            handleTrainIndicatorDetectResult(indicatorDetectResults, taskId);

            deleteResultFile(fileList);
            taskExecutionResultDao.updateTaskPeriod(taskId, startTime, taskPeriodType, null);
        } catch (Exception e) {
            LOGGER.warn("ServiceException e {}", e);
            LOGGER.error("[synAnalysisResult] synAnalysisResult failed! indicatorDetectResults={}",
                    indicatorDetectResults);
            // 更新任务结果表
            taskExecutionResultDao.updateTaskPeriod(taskId, startTime, taskPeriodType,
                    TaskConstant.EXECUTION_STATUS_FAILED);
            throw new ServiceException("synAnalysisResult failed! rollback!");
        }
    }

    public void clearSentAlarmRecord(int taskId) {
        List<SendAlarmRecord> alarmRecords = sendAlarmDao.querySentUnclearedRecord(taskId);
        if (CollectionUtils.isEmpty(alarmRecords)) {
            LOGGER.warn("clearSentAlarmRecord alarmRecords is empty");
            return;
        }
        List<AlarmRecord> clearAlarmRecords = new ArrayList<>();
        for (SendAlarmRecord record : alarmRecords) {
            AlarmRecord clearRecord = JSONObject.parseObject(record.getAlarmRecord(), AlarmRecord.class);
            clearRecord.setCategory(SynAnalysisResultConstant.AlarmParameters.CATEGORY_CLEAR);
            clearAlarmRecords.add(clearRecord);
            record.setCategory(SynAnalysisResultConstant.AlarmParameters.CATEGORY_CLEAR);
        }
        try {
            AlarmRecordsHandler.pushAlarm(clearAlarmRecords, taskId, StrategyConstant.AUTO_REPORT_ALARM);
            setSendAlarmRecordStatus(alarmRecords, SEND_STATUS_SUC);
        } catch (ServiceException e) {
            LOGGER.error("clearSentAlarmRecord error, alarmRecords is {}, e={}", alarmRecords, e.getMessage());
            setSendAlarmRecordStatus(alarmRecords, SEND_STATUS_FAI);
        }
        if (CollectionUtils.isNotEmpty(alarmRecords)) {
            sendAlarmDao.updateClearSendAlarmRecord(alarmRecords);
        }
    }

    private void setSendAlarmRecordStatus(List<SendAlarmRecord> clearRecords, String sendStatus) {
        for (SendAlarmRecord record : clearRecords) {
            record.setUpdateTime(System.currentTimeMillis());
            record.setStatus(sendStatus);
        }
    }


    /**
     * 处理训练产生的指标迁移结果
     *
     * @param taskId 任务名
     * @param transferFileList 指标迁移文件列表
     */
    private void updateIndicatorTransferResult(Integer taskId, List<File> transferFileList) {
        List<IndicatorTransferInfo> indicatorTransferInfos = new ArrayList<>();
        try {
            for (File file : transferFileList) {
                List<IndicatorTransferInfo> indicatorTransferInfoList;
                try {
                    indicatorTransferInfoList = CsvFileUtil.readCsvFile(IndicatorTransferInfo.class, file);
                } catch (IOException e) {
                    LOGGER.error("readCsvFile failed!" + e);
                    indicatorTransferInfoList = Collections.emptyList();
                }
                indicatorTransferInfos.addAll(indicatorTransferInfoList);
            }

            // 迁移结果入库
            indicatorTransferDao.insertTransferInfo(taskId, indicatorTransferInfos);
        } catch (Exception e) {
            LOGGER.warn("ServiceException e {}", e);
            LOGGER.error("[synAnalysisResult] synAnalysisResult failed! indicatorTransferInfos={}",
                indicatorTransferInfos);
        }
    }

    /**
     * 处理训练产生的检测结果
     *
     * @param indicatorDetectResults indicatorDetectResults
     * @param taskId taskId
     * @throws ServiceException 服务异常
     */
    private void handleTrainIndicatorDetectResult(List<IndicatorDetectResult> indicatorDetectResults, int taskId)
            throws ServiceException {
        List<IndicatorOutlier> indicatorOutlierList = new ArrayList<>();
        List<IndicatorOutlier> indicatorsList = new ArrayList<>();
        // 计算异常区间
        int index = 0;
        long currentTime = 0L;
        while (index < indicatorDetectResults.size()) {
            IndicatorDetectResult indicatorDetectResult = indicatorDetectResults.get(index);
            if (currentTime < indicatorDetectResult.getCollectTime()) {
                currentTime = indicatorDetectResult.getCollectTime();
            }
            if (indicatorDetectResult.getAbnormal() != null && indicatorDetectResult.getAbnormal()) {
                while (index < indicatorDetectResults.size() && indicatorDetectResults.get(index).getIndicatorId()
                    .equals(indicatorDetectResult.getIndicatorId()) && indicatorDetectResults.get(index).getAbnormal()
                    != null && indicatorDetectResults.get(index).getAbnormal()) {
                    if (currentTime < indicatorDetectResults.get(index).getCollectTime()) {
                        currentTime = indicatorDetectResults.get(index).getCollectTime();
                    }
                    index++;
                }
                index--;

                IndicatorDetectResult endIndicatorDetectResult = indicatorDetectResults.get(index);
                IndicatorOutlier indicatorOutlier = new IndicatorOutlier();
                indicatorOutlier.setStartTime(indicatorDetectResult.getCollectTime());
                indicatorOutlier.setIndicatorId(indicatorDetectResult.getIndicatorId());
                indicatorOutlier.setEndTime(endIndicatorDetectResult.getCollectTime() + endIndicatorDetectResult.getPeriod());
                indicatorOutlier.setDuration(endIndicatorDetectResult.getCollectTime()
                    - indicatorDetectResult.getCollectTime() + endIndicatorDetectResult.getPeriod());
                indicatorOutlier.setProbableCause(indicatorDetectResult.getAbnormalType());
                indicatorOutlier.setId(
                        UUID.randomUUID().toString().replaceAll(Separator.SEPARATOR_DASH, Separator.SEPARATOR_NULL));
                indicatorOutlier.setTaskId(taskId);
                indicatorOutlierList.add(indicatorOutlier);
            }
            index++;
        }

        List<IndicatorDetectResult> indicators = indicatorDetectResults.stream().collect(Collectors.collectingAndThen(
            Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(object -> object.getIndicatorId()))),
            ArrayList::new));
        for (IndicatorDetectResult indicator : indicators) {
            IndicatorOutlier indicatorOutlier = new IndicatorOutlier();
            indicatorOutlier.setIndicatorId(indicator.getIndicatorId());
            indicatorOutlier.setTaskId(taskId);
            indicatorsList.add(indicatorOutlier);
        }
        if (!indicatorsList.isEmpty()) {
            taskIndicatorDao.updateIndicatorStatus(indicatorsList, 0);
        }

        updateTrainIndicatorOutlier(indicatorOutlierList, indicatorDetectResults.get(0).getCollectTime(), currentTime, taskId);
    }

    /**
     * 更新指标异常表
     *
     * @param indicatorOutlierList 异常结果列表
     * @param startTime 开始时间
     * @param currentTime 当前时间
     * @param taskId 任务id
     * @throws ServiceException 服务异常
     */
    private void updateTrainIndicatorOutlier(List<IndicatorOutlier> indicatorOutlierList, long startTime, long currentTime,
        int taskId) throws ServiceException {
        // 插入数据库之前需要先判断是否需要与数据库中已存在的异常区间合并
        List<IndicatorOutlier> lastedOutlier = indicatorOutlierDao.getLastedOutlierEveryIndicator(taskId);
        for (IndicatorOutlier outlier : lastedOutlier) {
            if (outlier.getEndTime() == 0) {
                indicatorOutlierDao.updateIndicatorOutlierEndTime(outlier.getId(), startTime);
            }
        }
        Map<String, IndicatorOutlier> map = lastedOutlier.stream().collect(
            Collectors.toMap(IndicatorOutlier::getIndicatorId, Function.identity(), (k1, k2) -> k2));
        ArrayList<IndicatorOutlier> newIndicatorOutliers = new ArrayList<>();
        for (IndicatorOutlier indicatorOutlier : indicatorOutlierList) {
            long endTime = indicatorOutlier.getEndTime();
            if (map.containsKey(indicatorOutlier.getIndicatorId()) && map.get(indicatorOutlier.getIndicatorId()).getEndTime() == 0) {
                indicatorOutlierDao.updateIndicatorOutlierEndTime(map.get(indicatorOutlier.getIndicatorId()).getId(), endTime);
                continue;
            }
            if (map.containsKey(indicatorOutlier.getIndicatorId())
                && map.get(indicatorOutlier.getIndicatorId()).getEndTime() >= indicatorOutlier.getStartTime()) {
                // 若异常区间可以与数据库中已存在的异常区间合并，则合并即可
                indicatorOutlierDao.updateIndicatorOutlierEndTime(
                    map.get(indicatorOutlier.getIndicatorId()).getId(), endTime);
            } else {
                indicatorOutlier.setEndTime(endTime);
                newIndicatorOutliers.add(indicatorOutlier);
            }
        }
        if (!newIndicatorOutliers.isEmpty()) {
            int batch = 0;
            while (batch * MAX_DATA_SIZE < newIndicatorOutliers.size()) {
                int startIndex = batch * MAX_DATA_SIZE;
                int endIndex = Integer.min(newIndicatorOutliers.size(), (batch + 1) * MAX_DATA_SIZE);
                List<IndicatorOutlier> subList = newIndicatorOutliers.subList(startIndex, endIndex);
                indicatorOutlierDao.insertNewOutliers(subList);
                batch++;
            }
        }
    }

    /**
     * 处理训练时产生的阈值数据，因为数据可能会很多，一次入库会出现异常，所以分批次将数据入库
     *
     * @param indicatorDetectResults 指标检测结果列表
     * @param task task
     * @param startTime 开始时间
     * @return 更新后的指标异常检测结果
     * @throws ServiceException 服务异常
     */
    private List<IndicatorDetectResult> updateTrainIndicatorDetectResult(
        List<IndicatorDetectResult> indicatorDetectResults, AnalysisTask task, long startTime) throws ServiceException {
        Integer taskId = task.getTaskId();
        // 获取每个指标生成的检测结果的时间区间
        HashMap<String, long[]> map = new HashMap<>();
        for (IndicatorDetectResult indicatorDetectResult : indicatorDetectResults) {
            if (map.containsKey(indicatorDetectResult.getIndicatorId())) {
                long[] range = map.get(indicatorDetectResult.getIndicatorId());
                range[0] = Long.min(range[0], indicatorDetectResult.getCollectTime());
                range[1] = Long.max(range[1], indicatorDetectResult.getCollectTime());
            } else {
                map.put(indicatorDetectResult.getIndicatorId(),
                        new long[]{indicatorDetectResult.getCollectTime(), indicatorDetectResult.getCollectTime()});
            }
        }
        String resultTableName = AnalysisResultTableName.INDICATOR_DETECT_TABLE_NAME + taskId;
        String thresholdTableName = AnalysisResultTableName.INDICATOR_THRESHOLD_TABLE_NAME + taskId;

        for (Map.Entry<String, long[]> entry : map.entrySet()) {
            String indicatorId = entry.getKey();
            long start = entry.getValue()[0];
            long end = entry.getValue()[1];
            // 清除数据库中重复的检测结果
            indicatorDetectResultDao.deleteDuplicateIndicatorDetectResults(resultTableName, Collections.singleton(indicatorId), start, end);
            taskAnalysisResultDao.deleteDuplicateIndicatorThresholds(thresholdTableName, Collections.singleton(indicatorId), start, end);
            indicatorOutlierDao.deleteDuplicateIndicatorOutliers(taskId, indicatorId, start, end);
        }
        List<IndicatorDetectResult> updateindicatorDetectResults = updateIndicatorAbnormal(indicatorDetectResults, task);
        int batch = 0;
        while (batch * MAX_DATA_SIZE < updateindicatorDetectResults.size()) {
            int startIndex = batch * MAX_DATA_SIZE;
            int endIndex = Integer.min(updateindicatorDetectResults.size(), (batch + 1) * MAX_DATA_SIZE);
            List<IndicatorDetectResult> subList = updateindicatorDetectResults.subList(startIndex, endIndex);

            // 更新异常检测算法详细结果表
            updateIndicatorDetectResult(subList, taskId, startTime);
            // 更新异常检测算法阈值表
            updateIndicatorThreshold(subList, taskId);

            batch++;
        }
        return updateindicatorDetectResults;
    }

    private List<IndicatorDetectResult> missDataPointAbnormal(List<IndicatorDetectResult> indicatorDetectResults,
        AnalysisTask task) throws ServiceException {
        // 缺点异常检测
        if (!missDataJudge(task) || CollectionUtils.isEmpty(indicatorDetectResults)) {
            return indicatorDetectResults;
        }
        String indicatorMissDataAlarmNameEn = ResourceUtil.getMessage(LogConstant.INDICATOR_MISS_DATA_ALARM_NAME, Locale.US);
        String indicatorMissDataAlarmNameCn = ResourceUtil.getMessage(LogConstant.INDICATOR_MISS_DATA_ALARM_NAME, Locale.SIMPLIFIED_CHINESE);
        // 过滤出异常里面的缺点型异常
        Map<Boolean, Map<String, List<IndicatorDetectResult>>> groupMap = indicatorDetectResults.stream()
            .collect(Collectors.partitioningBy(
                detect -> indicatorMissDataAlarmNameEn.equals(detect.getAbnormalType())
                    || indicatorMissDataAlarmNameCn.equals(detect.getAbnormalType()),
                Collectors.groupingBy(IndicatorDetectResult::getIndicatorId)
            ));

        // 缺点异常组
        Map<String, List<IndicatorDetectResult>> missDataDetectMap = groupMap.get(true);
        // 其他异常组
        Map<String, List<IndicatorDetectResult>> otherDataDetectMap = groupMap.get(false);

        List<IndicatorMissDataOutlier> indicatorOutlierList = new ArrayList<>();
        // 根据taskid查询表里所有没结束的记录
        List<IndicatorMissDataOutlier> records = indicatorOutlierDao.queryMissDataOutlierByTime(task.getTaskId());
        for (IndicatorMissDataOutlier record : records) {
            if (missDataDetectMap.containsKey(record.getIndicatorId())) {
                // 缺点异常组包含这个指标 继续异常
                missDataDetectMap.remove(record.getIndicatorId());
                continue;
            }
            if (otherDataDetectMap.containsKey(record.getIndicatorId())) {
                // 其他异常组包含这个指标 关闭异常
                record.setEndTime(otherDataDetectMap.get(record.getIndicatorId()).get(0).getCollectTime());
                record.setCategory(AlarmParameters.CATEGORY_CLEAR);
                indicatorOutlierList.add(record);
            }
        }
        // 剩余的缺点异常 新增
        indicatorOutlierList.addAll(structNewOutlier(missDataDetectMap, task.getTaskId()));

        if (CollectionUtils.isNotEmpty(indicatorOutlierList)) {
            pushMissOutlierToAlarm(indicatorOutlierList);
            indicatorOutlierDao.insertOrUpdateOutlier(indicatorOutlierList);
        }
        // 返回其余异常继续处理
        return otherDataDetectMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
    }

    private List<IndicatorMissDataOutlier> structNewOutlier(Map<String, List<IndicatorDetectResult>> missDataDetectMap, int taskId) {
        List<IndicatorMissDataOutlier> records = new ArrayList<>();
        for (List<IndicatorDetectResult> indicatorDetectResult : missDataDetectMap.values()) {
            IndicatorMissDataOutlier newOutlier = new IndicatorMissDataOutlier();
            newOutlier.setStartTime(indicatorDetectResult.get(0).getCollectTime());
            newOutlier.setIndicatorId(indicatorDetectResult.get(0).getIndicatorId());
            newOutlier.setEndTime(0);
            newOutlier.setProbableCause(indicatorDetectResult.get(0).getAbnormalType());
            newOutlier.setId(
                UUID.randomUUID().toString().replaceAll(Separator.SEPARATOR_DASH, Separator.SEPARATOR_NULL));
            newOutlier.setTaskId(taskId);
            newOutlier.setCategory(AlarmParameters.CATEGORY_NEW);
            records.add(newOutlier);
        }
        return records;
    }

    private void pushMissOutlierToAlarm(List<IndicatorMissDataOutlier> indicatorOutlierList) {
        int taskId = indicatorOutlierList.get(0).getTaskId();
        AnalysisTask analysisTask = null;
        try {
            analysisTask = taskManageDao.getAnalysisTaskById(taskId);
        } catch (ServiceException e) {
            LOGGER.error("pushMissOutlierToAlarm get Task error : taskId is {}, e={}", taskId, e.getMessage());
            return;
        }
        if (analysisTask == null) {
            LOGGER.error("pushMissOutlierToAlarm can't find the task ,taskId is : {}", taskId);
            return;
        }
        if (analysisTask.getReportAlarm() == 0) {
            LOGGER.info("pushMissOutlierToAlarm do not need report alarm ,taskId is : {}", taskId);
            return;
        }
        Map<Integer, List<IndicatorMissDataOutlier>> groupMissDataOutlier = indicatorOutlierList.stream()
            .collect(Collectors.groupingBy(IndicatorMissDataOutlier::getCategory));
        for (Map.Entry<Integer, List<IndicatorMissDataOutlier>> entry : groupMissDataOutlier.entrySet()) {
            List<AlarmRecord> alarmRecordList = new ArrayList<>();
            reportIndicatorAlarm(entry.getValue(), alarmRecordList, analysisTask);
            // 这个方法其实只需要SwitchAlarmField和OutlierId 可以转换对象
            recordSendAlarm(entry.getKey(), taskId, alarmRecordList, JSONArray.parseArray(JSONObject.toJSONString(entry.getValue()), IndicatorOutlier.class));
        }
    }

    private void reportIndicatorAlarm(List<IndicatorMissDataOutlier> indicatorOutlierList, List<AlarmRecord> alarmRecordList,
        AnalysisTask analysisTask) {
        int severity = analysisTask.getAlarmType();
        String alarmName = AlarmRecordsHandler.indicatorAlarmNameI18n();
        String missDataName = ResourceUtil.getMessage(LogConstant.INDICATOR_MISS_DATA_ALARM_NAME, ContextUtils.getContext().getLocale());
        long currentTime = System.currentTimeMillis();
        for (IndicatorMissDataOutlier indicatorOutlier : indicatorOutlierList) {
            TaskIndicator indicator = AlarmRecordsHandler.getAlarmSource(taskIndicatorDao, analysisTask, indicatorOutlier.getIndicatorId());
            String probableCause = generateProbableCause(analysisTask.getTaskId(), indicatorOutlier.getIndicatorId());
            String mergeKey = analysisTask.getTaskName() + indicatorOutlier.getIndicatorId() + missDataName;
            AlarmRecord alarmRecord;

            String switchAlarmField;
            int category = indicatorOutlier.getCategory();
            if (category == AlarmParameters.CATEGORY_NEW) {
                switchAlarmField = AlarmAddInfoBuilder.init()
                    .appendKeyValue("UUID", UUID.randomUUID().toString().replaceAll(Separator.SEPARATOR_DASH, Separator.SEPARATOR_NULL)).build();
                alarmRecord = AlarmRecordsHandler.generateAlarmRecord(category, currentTime,
                    probableCause, AlarmParameters.ALARM_ID_INDICATOR, severity, mergeKey,
                    switchAlarmField, alarmName, indicator.getDn(), indicator.getMoType());
                indicatorOutlier.setAlarmRecord(JSONObject.toJSONString(alarmRecord));
            } else {
                // 需要从数据库中获取，否则无法清理
                switchAlarmField = indicatorOutlier.getSwitchAlarmField();
                if (switchAlarmField == null) {
                    LOGGER.error("Could not get switchAlarmField, maybe cleared or user replace the alarm info.");
                }
                if (StringUtils.isEmpty(indicatorOutlier.getAlarmRecord())) {
                    LOGGER.error("indicatorOutlier alarmRecord is empty,taskId={}", analysisTask.getTaskId());
                    continue;
                }
                alarmRecord = JSONObject.parseObject(indicatorOutlier.getAlarmRecord(), AlarmRecord.class);
                alarmRecord.setCategory(AlarmParameters.CATEGORY_CLEAR);
                alarmRecord.setCleared(AlarmParameters.ALARM_CLEARED);
                alarmRecord.setOccurUtc(0);
                alarmRecord.setClearUtc(indicatorOutlier.getEndTime());
            }
            alarmRecord.setAdditionalInformation(switchAlarmField + ", abnormalType=" + missDataName);
            indicatorOutlier.setSwitchAlarmField(switchAlarmField);
            // 上报告警根据任务填写的告警id和名称上报
            if (!OmcUtil.isOmcMode()) {
                alarmRecord.setAlarmId(analysisTask.getReportAlarmId());
                alarmRecord.setAlarmName(analysisTask.getReportAlarmName());
            }
            alarmRecordList.add(alarmRecord);
        }
    }

    private boolean missDataJudge(AnalysisTask analysisTask) {
        List<ModelParameter> modelParameters = new ArrayList<>();
        String algorithmParam = analysisTask.getAlgorithmParam();
        modelParameters = JSONArray.parseArray(algorithmParam, ModelParameter.class);
        return modelParameters.stream()
            .filter(param -> Objects.equals(MISS_POINT_SWITCH, param.getParameterName()))
            .findFirst()
            .map(param -> param.getParameterDefaultValue().toString().trim())
            .map(YES::equalsIgnoreCase)
            .orElse(false);
    }

    private List<IndicatorDetectResult> updateIndicatorAbnormal(List<IndicatorDetectResult> indicatorDetectResults,
        AnalysisTask analysisTask) {
        // 根据检测窗口确定是否是异常点
        List<ModelParameter> modelParameters = new ArrayList<>();
        String algorithmParam = analysisTask.getAlgorithmParam();
        modelParameters = JSONArray.parseArray(algorithmParam, ModelParameter.class);
        int windowSize = 5;
        int detectNum = 3;
        if (CollectionUtils.isNotEmpty(modelParameters)) {
            for (ModelParameter modelParameter : modelParameters) {
                if (modelParameter.getParameterName().equals(WINDOW_SIZE)) {
                    windowSize = Integer.parseInt(modelParameter.getParameterDefaultValue().toString());
                }
                if (modelParameter.getParameterName().equals(DETECT_NUM)) {
                    detectNum = Integer.parseInt(modelParameter.getParameterDefaultValue().toString());
                }
            }
        }

        String resultTableName = AnalysisResultTableName.INDICATOR_DETECT_TABLE_NAME + analysisTask.getTaskId();

        HashMap<String, long[]> map = new HashMap<>();
        Set<String> indicatorIds = indicatorDetectResults.stream()
            .map(IndicatorDetectResult::getIndicatorId)
            .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(indicatorIds)) {
            LOGGER.error("indicatorIds empty, indicatorIds is {}", indicatorIds);
        }

        for (String indicatorId : indicatorIds) {
            IndicatorDetectResult indicatorDetectResult = indicatorDetectResults.stream()
                .filter(v -> v.getIndicatorId().equals(indicatorId))
                .min(Comparator.comparing(IndicatorDetectResult::getCollectTime))
                .orElse(new IndicatorDetectResult());
            map.put(indicatorId,
                new long[] {indicatorDetectResult.getCollectTime(), indicatorDetectResult.getPeriod()});
        }

        for (Map.Entry<String, long[]> entry : map.entrySet()) {
            getIndicatorDetectResults(indicatorDetectResults, windowSize, detectNum, resultTableName, entry);
        }
        return indicatorDetectResults.stream()
            .sorted(Comparator.comparing(IndicatorDetectResult::getIndicatorId)
                .thenComparing(IndicatorDetectResult::getCollectTime))
            .collect(Collectors.toList());
    }

    public void getIndicatorDetectResults(List<IndicatorDetectResult> indicatorDetectResults, int windowSize,
        int detectNum, String resultTableName, Map.Entry<String, long[]> entry) {
        String indicatorId = entry.getKey();
        long start = entry.getValue()[0];
        long period = entry.getValue()[1];
        long windowStartTime = start - (period * windowSize);

        List<IndicatorDetectResult> beforeWindowIndicatorDetectResults
            = indicatorDetectResultDao.queryTimeRangeIndicatorDetectResults(resultTableName, indicatorId,
            windowStartTime, start);

        List<IndicatorDetectResult> afterWindowIndicatorDetectResults = indicatorDetectResults.stream()
            .filter(v -> v.getIndicatorId().equals(indicatorId))
            .collect(Collectors.toList());

        beforeWindowIndicatorDetectResults.addAll(afterWindowIndicatorDetectResults);

        indicatorDetectResults.removeAll(afterWindowIndicatorDetectResults);
        LOGGER.debug("beforeWindowIndicatorDetectResults size = {}", beforeWindowIndicatorDetectResults.size());

        for (IndicatorDetectResult indicatorDetectResult : afterWindowIndicatorDetectResults) {
            if (indicatorDetectResult.getAbnormal() && (indicatorDetectResult.getAbnormalWithoutWindow() == null
                || !indicatorDetectResult.getAbnormalWithoutWindow())) {
                long endTime = indicatorDetectResult.getCollectTime();
                long startTime = endTime - period * windowSize;
                // 找出前一个点是否异常，前一个点为异常，则后续超出阈值线延续异常， 若前一个点缺点，则断开波动异常区间，根据窗口判断逻辑判断
                Optional<IndicatorDetectResult> firstResult  = beforeWindowIndicatorDetectResults.stream()
                    .filter(v -> v.getCollectTime() == endTime - period).findFirst();
                if (firstResult.isPresent() && firstResult.get().getAbnormal()) {
                    continue;
                }

                long count = beforeWindowIndicatorDetectResults.stream()
                    .filter(v -> v.getCollectTime() <= endTime && v.getCollectTime() > startTime && (!(
                        v.getAbnormalType().equals("None") || v.getAbnormalType().equals("null"))))
                    .count();
                if (count < detectNum) {
                    indicatorDetectResult.setAbnormal(false);
                }
            }
        }
        indicatorDetectResults.addAll(afterWindowIndicatorDetectResults);
    }

    private void deleteResultFile(List<File> fileList) {
        // 删除hofs上的文件
        for (File file : fileList) {
            CsvFileUtil.deleteFile(file);
        }
    }

    private void judgeIndicatorCount(Set<String> indicatorIdList, int taskId, long startTime, String taskPeriodType, List<IndicatorDetectResult> failIndicatorDetectResults)
        throws ServiceException {
        Integer count = taskAnalysisResultDao.getIndicatorCountByTaskIdFilterSoftDelete(taskId);
        String result = count == indicatorIdList.size() ? "Finished" : "PartialFinished";
        if ("PartialFinished".equals(result)) {
            taskExecutionResultDao.setTaskPeriodPartRecord(taskId, startTime, taskPeriodType,
                JSONObject.toJSONString(indicatorIdList));
        }
        if (CollectionUtils.isNotEmpty(failIndicatorDetectResults)) {
            taskExecutionResultDao.setTaskPeriodFailureCause(taskId, startTime, taskPeriodType,
                constructFailureCause(failIndicatorDetectResults));
        }
        // 更新任务结果表
        taskExecutionResultDao.updateTaskPeriod(taskId, startTime, taskPeriodType, result);
    }

    private Boolean judgIndicatorEmpty(Set<String> indicatorIds, List<IndicatorDetectResult> indicatorDetectResults,
        int taskId, long startTime, String taskPeriodType, List<IndicatorDetectResult> failIndicatorDetectResults) throws ServiceException {
        if (CollectionUtils.isEmpty(indicatorIds)) {
            taskExecutionResultDao.setTaskPeriodFailureCause(taskId, startTime,
                taskPeriodType, constructFailureCause(failIndicatorDetectResults));
            taskExecutionResultDao.updateTaskPeriod(taskId, startTime, taskPeriodType,
                TaskConstant.EXECUTION_STATUS_FAILED);
            return Boolean.TRUE;
        }
        if (CollectionUtils.isEmpty(indicatorDetectResults)) {
            judgeIndicatorCount(indicatorIds, taskId, startTime, taskPeriodType, failIndicatorDetectResults);
            LOGGER.warn("no latest indicatorDetectResults.");
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    private void judgResultDuplicate(List<IndicatorDetectResult> indicatorDetectResultList, int taskId) {
        if (CollectionUtils.isEmpty(indicatorDetectResultList)) {
            LOGGER.error("judgResultDuplicate indicatorDetectResultList is empty!");
            return;
        }
        String tableName = AnalysisResultTableName.INDICATOR_THRESHOLD_TABLE_NAME + taskId;
        List<List<IndicatorDetectResult>> detectResults = ListUtils.partition(indicatorDetectResultList, MAX_DATA_SIZE);
        List<IndicatorThreshold> thresholds = new ArrayList<>();
        for (List<IndicatorDetectResult> detectResult : detectResults) {
            thresholds.addAll(taskAnalysisResultDao.selectBatchThreshold(tableName, detectResult));
        }
        if (CollectionUtils.isEmpty(thresholds)) {
            LOGGER.debug("judgResultDuplicate no duplicate result");
            return;
        }
        LOGGER.debug("judgResultDuplicate remove duplicate before size={}, indicatorDetectResultList={}",
            indicatorDetectResultList.size(), indicatorDetectResultList);
        String indicatorMissDataAlarmNameEn = ResourceUtil.getMessage(LogConstant.INDICATOR_MISS_DATA_ALARM_NAME, Locale.US);
        String indicatorMissDataAlarmNameCn = ResourceUtil.getMessage(LogConstant.INDICATOR_MISS_DATA_ALARM_NAME, Locale.SIMPLIFIED_CHINESE);
        for (IndicatorThreshold threshold : thresholds) {
            String indicatorId = threshold.getIndicatorId();
            indicatorDetectResultList.removeIf(
                indicatorDetectResult -> indicatorDetectResult.getIndicatorId().equals(indicatorId) && (
                    !indicatorMissDataAlarmNameEn.equals(indicatorDetectResult.getAbnormalType())
                        && !indicatorMissDataAlarmNameCn.equals(indicatorDetectResult.getAbnormalType())) && Objects.equals(threshold.getCollectTime(), indicatorDetectResult.getCollectTime()));
        }
        LOGGER.debug("judgResultDuplicate remove duplicate after size={}, indicatorDetectResultList={}",
            indicatorDetectResultList.size(), indicatorDetectResultList);
    }

    private void updateIndicatorThreshold(List<IndicatorDetectResult> indicatorDetectResultList, int taskId)
        throws ServiceException {
        List<IndicatorThreshold> indicatorThresholdList = new ArrayList<>();
        IndicatorThreshold indicatorThreshold;
        for (IndicatorDetectResult indicatorDetectResult : indicatorDetectResultList) {
            indicatorThreshold = new IndicatorThreshold();
            indicatorThreshold.setIndicatorId(indicatorDetectResult.getIndicatorId());
            indicatorThreshold.setCollectTime(indicatorDetectResult.getCollectTime());
            indicatorThreshold.setUpperThreshold(indicatorDetectResult.getUpperThreshold());
            indicatorThreshold.setLowerThreshold(indicatorDetectResult.getLowerThreshold());
            indicatorThresholdList.add(indicatorThreshold);
        }
        // 更新指标阈值表
        if (CollectionUtils.isNotEmpty(indicatorThresholdList)) {
            String tableName = AnalysisResultTableName.INDICATOR_THRESHOLD_TABLE_NAME + taskId;
            taskAnalysisResultDao.insertIndicatorThreshold(tableName, indicatorThresholdList);
        }
    }

    private void updateIndicatorDetectResult(List<IndicatorDetectResult> indicatorDetectResults, int taskId,
        long startTime) throws ServiceException {
        List<IndicatorDetectResult> abnormalIndicatorDetectResultList = new ArrayList<>();
        for (IndicatorDetectResult indicatorDetectResult : indicatorDetectResults) {
            if (indicatorDetectResult.getAbnormal() != null && !indicatorDetectResult.getAbnormalType().equals("None")) {
                indicatorDetectResult.setStartTime(startTime);
                abnormalIndicatorDetectResultList.add(indicatorDetectResult);
            }
        }
        // 更新指标检测结果详情表
        if (CollectionUtils.isNotEmpty(abnormalIndicatorDetectResultList)) {
            String tableName = AnalysisResultTableName.INDICATOR_DETECT_TABLE_NAME + taskId;
            indicatorDetectResultDao.insertIndicatorDetectResults(tableName, abnormalIndicatorDetectResultList);
        }
    }

    public void handleIndicatorDetectResultNew(List<IndicatorDetectResult> indicatorDetectResults, AnalysisTask task) throws ServiceException {
        Map<String, Map<String, List<IndicatorOutlier>>> indicatorOutlierMap = findNewIndicatorOutlier(indicatorDetectResults, task);
        for (Map.Entry<String, Map<String, List<IndicatorOutlier>>> entry : indicatorOutlierMap.entrySet()) {
            List<IndicatorOutlier> newIndicatorOutlierList = entry.getValue().get(NEW_OUTLIER_LIST);
            List<IndicatorOutlier> updateIndicatorOutlierList = entry.getValue().get(UPDATE_OUTLIER_LIST);
            // 结束异常清除
            if (CollectionUtils.isNotEmpty(updateIndicatorOutlierList)) {
                for (IndicatorOutlier outlier : updateIndicatorOutlierList) {
                    List<IndicatorOutlier> reportAlarmsOutlierList = new ArrayList<>();
                    reportAlarmsOutlierList.add(outlier);
                    Optional<IndicatorDetectResult> result = indicatorDetectResults.stream()
                        .filter(detectResult -> outlier.getIndicatorId().equals(detectResult.getIndicatorId())
                            && outlier.getEndTime() == detectResult.getCollectTime()).findFirst();
                    if (result.isPresent()) {
                        List<IndicatorDetectResult> indicatorDetects = new ArrayList<>();
                        indicatorDetects.add(result.get());
                        taskIndicatorDao.updateIndicatorStatus(updateIndicatorOutlierList, 0);
                        pushOutlierToAlarm(task, reportAlarmsOutlierList, AlarmParameters.CATEGORY_CLEAR, indicatorDetects);
                    } else {
                        LOGGER.error("report alarm can not find first detect result, outlier={}", outlier);
                    }
                    LOGGER.debug("report clear alarm outlier={}", outlier);
                }
                indicatorOutlierDao.updateOutliers(updateIndicatorOutlierList);
            }
            // 新增异常上报
            if (CollectionUtils.isNotEmpty(newIndicatorOutlierList)) {
                // 结束时间距离当前不超过1min的才上报告警
                long currentTime = System.currentTimeMillis();
                for (IndicatorOutlier outlier : newIndicatorOutlierList) {
                    if (currentTime - outlier.getEndTime() < 60000 || outlier.getEndTime() == 0) {
                        List<IndicatorOutlier> reportAlarmsOutlierList = new ArrayList<>();
                        reportAlarmsOutlierList.add(outlier);
                        Optional<IndicatorDetectResult> result = indicatorDetectResults.stream()
                            .filter(detectResult -> outlier.getIndicatorId().equals(detectResult.getIndicatorId())
                                && outlier.getStartTime() == detectResult.getCollectTime()).findFirst();
                        if (result.isPresent()) {
                            List<IndicatorDetectResult> indicatorDetects = new ArrayList<>();
                            indicatorDetects.add(result.get());
                            if (outlier.getEndTime() == 0) {
                                taskIndicatorDao.updateIndicatorStatus(reportAlarmsOutlierList, 1);
                            }
                            pushOutlierToAlarm(task, reportAlarmsOutlierList, AlarmParameters.CATEGORY_NEW, indicatorDetects);
                        } else {
                            LOGGER.error("report alarm can not find first detect result, outlier={}", outlier);
                        }
                        LOGGER.debug("report new alarm outlier={}", outlier);
                    }
                }
                indicatorOutlierDao.insertNewOutliers(newIndicatorOutlierList);
            }
            LOGGER.debug("handleIndicatorDetectResultNew, indicatorId={}, indicatorOutlierMap = {}",entry.getKey(), entry.getValue());
        }
    }


    public Map<String, Map<String, List<IndicatorOutlier>>> findNewIndicatorOutlier(List<IndicatorDetectResult> indicatorDetectResults, AnalysisTask analysisTask)
        throws ServiceException {
        Map<String, List<IndicatorDetectResult>> groupedDetect = indicatorDetectResults.stream()
            .collect(Collectors.groupingBy(IndicatorDetectResult::getIndicatorId));
        Map<String, Map<String, List<IndicatorOutlier>>> result = new HashMap<>();
        for (Map.Entry<String, List<IndicatorDetectResult>> entry : groupedDetect.entrySet()) {
            List<IndicatorDetectResult> points = entry.getValue();
            points.sort(Comparator.comparingLong(IndicatorDetectResult::getCollectTime));
            List<IndicatorOutlier> newOutliers = new ArrayList<>();
            List<IndicatorOutlier> updateOutliers = new ArrayList<>();
            analyzeAbnormalRange(analysisTask, points, newOutliers, updateOutliers);
            Map<String, List<IndicatorOutlier>> indicatorOutlierMap = new HashMap<>();
            indicatorOutlierMap.put(NEW_OUTLIER_LIST, newOutliers);
            indicatorOutlierMap.put(UPDATE_OUTLIER_LIST, updateOutliers);
            result.put(entry.getKey(), indicatorOutlierMap);
        }
        return result;
    }

    private void analyzeAbnormalRange(AnalysisTask analysisTask, List<IndicatorDetectResult> points,
        List<IndicatorOutlier> newOutliers, List<IndicatorOutlier> updateOutliers) throws ServiceException {
        // 查询第一个点是否在异常区间内
        IndicatorOutlier oldOutlier = indicatorOutlierDao.queryOutlierCoverCollectTime(analysisTask.getTaskId(), points.get(0).getIndicatorId(), points.get(0).getCollectTime());
        IndicatorOutlier currentOutlier = null;
        for (int i = 0; i < points.size(); i++) {
            IndicatorDetectResult detectResult = points.get(i);
            if (detectResult.getAbnormal() != null && detectResult.getAbnormal()) {
                if (oldOutlier == null && currentOutlier == null) {
                    currentOutlier = createNewIndicatorOutlier(detectResult, analysisTask.getTaskId());
                }
            } else {
                if (oldOutlier != null) {
                    oldOutlier.setEndTime(detectResult.getCollectTime());
                    oldOutlier.setDuration(oldOutlier.getEndTime() - oldOutlier.getStartTime());
                    updateOutliers.add(oldOutlier);
                    oldOutlier = null;
                    continue;
                }
                if (currentOutlier != null) {
                    currentOutlier.setEndTime(detectResult.getCollectTime());
                    currentOutlier.setDuration(currentOutlier.getEndTime() - currentOutlier.getStartTime());
                    newOutliers.add(currentOutlier);
                    currentOutlier = null;
                    continue;
                }
            }
            if (i == points.size() - 1 && detectResult.getAbnormal() != null && detectResult.getAbnormal()) {
                long collectTime = detectResult.getCollectTime();
                if (analysisTask != null && analysisTask.getStartStatus() == TaskConstant.TASK_STATUS_STOP) {
                    if (oldOutlier != null) {
                        oldOutlier.setEndTime(collectTime);
                        oldOutlier.setDuration(collectTime - oldOutlier.getStartTime());
                        updateOutliers.add(oldOutlier);
                        LOGGER.info("exceptionHandleWhenTaskStopped update indicatorOutlier = {}", oldOutlier);
                    }
                    if (currentOutlier != null) {
                        currentOutlier.setEndTime(collectTime);
                        currentOutlier.setDuration(collectTime - currentOutlier.getStartTime());
                        newOutliers.add(currentOutlier);
                        LOGGER.info("exceptionHandleWhenTaskStopped add newIndicatorOutlier = {}", currentOutlier);
                    }
                } else {
                    if (currentOutlier != null) {
                        newOutliers.add(currentOutlier);
                    }
                }
            }
        }
    }

    public void exceptionHandleWhenTaskStopped(IndicatorOutlier indicatorOutlier, long collectTime,
        List<IndicatorOutlier> newIndicatorOutlierList, List<IndicatorOutlier> updateIndicatorOutlierList, int taskId, IndicatorDetectResult indicatorDetectResult)
        throws ServiceException {
        // 停止任务时要对异常做定制处理
        if (indicatorOutlier == null) {
            IndicatorDetectResult preIndicatorDetectResult
                = indicatorDetectResultDao.queryLastTimeAbnormalIndicatorDetectResult(
                AnalysisResultTableName.INDICATOR_DETECT_TABLE_NAME + taskId, collectTime,
                indicatorDetectResult.getIndicatorId(), taskId);
            if (preIndicatorDetectResult == null) {
                IndicatorOutlier newIndicatorOutlier = createNewIndicatorOutlier(indicatorDetectResult, taskId);
                newIndicatorOutlierList.add(newIndicatorOutlier);
                newIndicatorOutlier.setEndTime(collectTime);
                newIndicatorOutlier.setDuration(collectTime - newIndicatorOutlier.getStartTime());
                updateIndicatorOutlierList.add(newIndicatorOutlier);
                LOGGER.info("exceptionHandleWhenTaskStopped add newIndicatorOutlier = {}", newIndicatorOutlier);
            } else {
                IndicatorOutlier preIndicatorOutlier = indicatorOutlierDao.queryOutlierCoverCollectTime(taskId,
                    preIndicatorDetectResult.getIndicatorId(), preIndicatorDetectResult.getCollectTime());
                if (preIndicatorOutlier != null) {
                    if (preIndicatorOutlier.getEndTime() != 0) {
                        preIndicatorOutlier.setEndTime(collectTime);
                        preIndicatorOutlier.setDuration(collectTime - preIndicatorOutlier.getStartTime());
                        updateIndicatorOutlierList.add(preIndicatorOutlier);
                        LOGGER.info("exceptionHandleWhenTaskStopped update preIndicatorOutlier = {}", preIndicatorOutlier);
                    }
                }
            }
        } else {
            indicatorOutlier.setEndTime(collectTime);
            indicatorOutlier.setDuration(collectTime - indicatorOutlier.getStartTime());
            updateIndicatorOutlierList.add(indicatorOutlier);
            LOGGER.info("exceptionHandleWhenTaskStopped update indicatorOutlier = {}", indicatorOutlier);
        }
    }

    private IndicatorOutlier createNewIndicatorOutlier(IndicatorDetectResult indicatorDetectResult, int taskId) {
        IndicatorOutlier newIndicatorOutlier = new IndicatorOutlier();
        // 通过UUID生成不重复的异常主键ID
        String id = UUID.randomUUID().toString().replaceAll(Separator.SEPARATOR_DASH, Separator.SEPARATOR_NULL);
        newIndicatorOutlier.setId(id);
        newIndicatorOutlier.setTaskId(taskId);
        newIndicatorOutlier.setIndicatorId(indicatorDetectResult.getIndicatorId());
        newIndicatorOutlier.setStartTime(indicatorDetectResult.getCollectTime());
        newIndicatorOutlier.setProbableCause(indicatorDetectResult.getAbnormalType());
        return newIndicatorOutlier;
    }

    public String generateProbableCause(int taskId, String indicatorId) {
        StringBuilder probableCause = new StringBuilder();
        AnalysisTask analysisTask;
        try {
            analysisTask = taskManageDao.getAnalysisTaskById(taskId);
        } catch (ServiceException e) {
            LOGGER.error("get Task error : taskId is " + taskId);
            return probableCause.toString();
        }
        List<TaskIndicator> taskIndicators = taskIndicatorDao.getTaskIndicatorList(taskId, indicatorId);
        if (analysisTask == null || CollectionUtils.isEmpty(taskIndicators)) {
            LOGGER.error("can't find the task or indicator ,taskId is {}, indicatorId is {}", taskId, indicatorId);
            return probableCause.toString();
        }
        TaskIndicator taskIndicator = taskIndicators.get(0);
        return genProbableCause(taskIndicator, analysisTask);
    }

    private String genProbableCause(TaskIndicator taskIndicators, AnalysisTask task) {
        // 拼接定位信息，场景+任务+指标+异常原因
        // CBS要求改成和性能阈值告警格式一样，告警类型=固定阈值告警, measUnit=测量单元名, measType=测量指标名, measObject=测量对象名
        AlarmAddInfoBuilder addInfoBuilder = AlarmAddInfoBuilder.init();
        addInfoBuilder.appendKeyValue(ResourceUtil.getMessage(InternationalConstant.REPORT_ALARM_TYPE, ContextUtils.getContext().getLocale()),
            ResourceUtil.getMessage(InternationalConstant.REPORT_ALARM_TYPE_VALUE, ContextUtils.getContext().getLocale()))
            .appendKeyValue(ResourceUtil.getMessage(InternationalConstant.MEASUNIT_NAME, ContextUtils.getContext().getLocale()),
                taskIndicators.getMeasUnitName())
            .appendKeyValue(ResourceUtil.getMessage(InternationalConstant.INDICATOR_NAME, ContextUtils.getContext().getLocale()),
                taskIndicators.getIndexName());

        if (StringUtils.isNotEmpty(taskIndicators.getDisplayValue()) && !DEFAULT_NULL.equals(
            taskIndicators.getDisplayValue())) {
            addInfoBuilder.appendKeyValue(ResourceUtil.getMessage(InternationalConstant.ORIGINALVALUE, ContextUtils.getContext().getLocale()),
                taskIndicators.getDisplayValue());
        }

        addInfoBuilder.appendKeyValue(ResourceUtil.getMessage(InternationalConstant.USER_INPUT, ContextUtils.getContext().getLocale()), task.getTaskDetail());
        // getTaskDetil存放用户输入的告警定位信息
        return addInfoBuilder.build();
    }

    private void pushOutlierToAlarm(AnalysisTask analysisTask, List<IndicatorOutlier> indicatorOutlierList, int category, List<IndicatorDetectResult> indicatorDetectResults) {
        List<AlarmRecord> alarmRecordList = new ArrayList<>();
        int taskId = indicatorOutlierList.get(0).getTaskId();
        if (analysisTask.getReportAlarm() == 0) {
            LOGGER.info("do not need report alarm ,taskId is : {}!", taskId);
            return;
        }
        reportIndicatorAlarm(indicatorOutlierList, category, indicatorDetectResults, alarmRecordList, taskId, analysisTask);
        recordSendAlarm(category, taskId, alarmRecordList, indicatorOutlierList);
    }

    public void recordSendAlarm(int category, int taskId, List<AlarmRecord> alarmRecordList, List<IndicatorOutlier> indicatorOutlierList) {
        if (CollectionUtils.isEmpty(alarmRecordList)) {
            return;
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug("recordSendAlarm alarms records = {}", alarmRecordList);
        }
        Map<String, IndicatorOutlier> outlierMap = indicatorOutlierList.stream().filter(item -> item.getSwitchAlarmField() != null)
            .collect(Collectors.toMap(IndicatorOutlier::getSwitchAlarmField, Function.identity(), (v1, v2) -> v2));
        if (category == AlarmParameters.CATEGORY_NEW) {
            List<SendAlarmRecord> reportNewRecords = new ArrayList<>();
            try {
                AlarmRecordsHandler.pushAlarm(alarmRecordList, taskId, StrategyConstant.AUTO_REPORT_ALARM);
                handleNewAlarm(reportNewRecords, alarmRecordList, SEND_STATUS_SUC, taskId, outlierMap);
            } catch (ServiceException e) {
                LOGGER.error("indicator send report alarm error, alarmRecordList is {}, e={}", alarmRecordList, e.getMessage());
                handleNewAlarm(reportNewRecords, alarmRecordList, SEND_STATUS_FAI, taskId, outlierMap);
            }
            if (CollectionUtils.isNotEmpty(reportNewRecords)) {
                sendAlarmDao.insertSendAlarmRecords(reportNewRecords);
            }
        } else {
            List<String> outlierIds = indicatorOutlierList.stream().map(IndicatorOutlier::getId).collect(Collectors.toList());
            List<SendAlarmRecord> list = sendAlarmDao.querySendRecordById(taskId, outlierIds);
            Map<String, SendAlarmRecord> recordMap = list.stream()
                .collect(Collectors.toMap(SendAlarmRecord::getIncidentId, Function.identity(), (v1, v2) -> v2));
            List<SendAlarmRecord> clearRecords = new ArrayList<>();
            try {
                AlarmRecordsHandler.pushAlarm(alarmRecordList, taskId, StrategyConstant.AUTO_REPORT_ALARM);
                handleClearAlarm(clearRecords, alarmRecordList, recordMap, SEND_STATUS_SUC, outlierMap);
            } catch (ServiceException e) {
                LOGGER.error("indicator send clear alarm error, alarmRecordList is {}, e={}", alarmRecordList, e.getMessage());
                handleClearAlarm(clearRecords, alarmRecordList, recordMap, SEND_STATUS_FAI, outlierMap);
            }
            if (CollectionUtils.isNotEmpty(clearRecords)) {
                sendAlarmDao.updateClearSendAlarmRecord(clearRecords);
            }
        }
    }

    private void handleClearAlarm(List<SendAlarmRecord> clearRecords, List<AlarmRecord> alarmRecordList, Map<String, SendAlarmRecord> recordMap,
        String sendStatus, Map<String, IndicatorOutlier> outlierMap) {
        if (MapUtils.isEmpty(recordMap)) {
            LOGGER.error("indicator querySendRecordById error, recordMap is empty");
            return;
        }
        for (AlarmRecord record : alarmRecordList) {
            if (StringUtils.isEmpty(record.getMatchKey()) || outlierMap.get(record.getMatchKey()) == null) {
                LOGGER.warn("handle clear alarm record error, matchKey={}, outlier={}", record.getMatchKey(),
                    outlierMap.get(record.getMatchKey()));
                continue;
            }
            String outlierId = outlierMap.get(record.getMatchKey()).getId();
            SendAlarmRecord dbRecord = recordMap.get(outlierId);
            if (dbRecord == null) {
                LOGGER.error("indicator get old record is empty, outlierId={}", outlierId);
                continue;
            }
            if (SEND_STATUS_SUC.equals(dbRecord.getStatus())) {
                SendAlarmRecord clearRecord = new SendAlarmRecord();
                clearRecord.setId(dbRecord.getId());
                clearRecord.setStatus(sendStatus);
                clearRecord.setCategory(SynAnalysisResultConstant.AlarmParameters.CATEGORY_CLEAR);
                clearRecord.setUpdateTime(System.currentTimeMillis());
                clearRecords.add(clearRecord);
                dbRecord.setCategory(SynAnalysisResultConstant.AlarmParameters.CATEGORY_CLEAR);
            }
            if (SEND_STATUS_FAI.equals(dbRecord.getStatus())) {
                SendAlarmRecord clearRecord = new SendAlarmRecord();
                clearRecord.setId(dbRecord.getId());
                clearRecord.setStatus(sendStatus);
                clearRecord.setCategory(SynAnalysisResultConstant.AlarmParameters.CATEGORY_CLEAR);
                clearRecord.setUpdateTime(System.currentTimeMillis());
                clearRecord.setSendAlarmStatus(SEND_STATUS_FAI);
                clearRecords.add(clearRecord);
                dbRecord.setCategory(SynAnalysisResultConstant.AlarmParameters.CATEGORY_CLEAR);
            }
        }
    }

    private void handleNewAlarm(List<SendAlarmRecord> reportNewRecords, List<AlarmRecord> reportAlarmRecords,
        String sendStatus, int taskId, Map<String, IndicatorOutlier> outlierMap) {
        for (AlarmRecord record : reportAlarmRecords) {
            if (StringUtils.isEmpty(record.getMatchKey()) || outlierMap.get(record.getMatchKey()) == null) {
                LOGGER.warn("handle new alarm record error, matchKey={}, outlier={}", record.getMatchKey(),
                    outlierMap.get(record.getMatchKey()));
                continue;
            }
            String outlierId = outlierMap.get(record.getMatchKey()).getId();
            SendAlarmRecord reportRecord = new SendAlarmRecord();
            reportRecord.setTaskId(taskId);
            reportRecord.setLevel(record.getSeverity());
            reportRecord.setStatus(sendStatus);
            reportRecord.setCategory(SynAnalysisResultConstant.AlarmParameters.CATEGORY_NEW);
            reportRecord.setAlarmRecord(JSONObject.toJSONString(record));
            reportRecord.setUpdateTime(System.currentTimeMillis());
            reportRecord.setIncidentId(outlierId);
            reportNewRecords.add(reportRecord);
        }
    }

    private void reportIndicatorAlarm(List<IndicatorOutlier> indicatorOutlierList, int category,
        List<IndicatorDetectResult> indicatorDetectResults, List<AlarmRecord> alarmRecordList, int taskId,
        AnalysisTask analysisTask) {
        int severity = analysisTask.getAlarmType();
        String alarmName = AlarmRecordsHandler.indicatorAlarmNameI18n();

        for (IndicatorOutlier indicatorOutlier : indicatorOutlierList) {
            String probableCause = generateProbableCause(taskId, indicatorOutlier.getIndicatorId());
            String mergeKey = analysisTask.getTaskName() + indicatorOutlier.getIndicatorId();
            AlarmRecord alarmRecord;
            TaskIndicator indicator = AlarmRecordsHandler.getAlarmSource(taskIndicatorDao, analysisTask, indicatorOutlier.getIndicatorId());
            String switchAlarmField;
            if (category == AlarmParameters.CATEGORY_NEW && businessStrategyManager.getStrategyResult(analysisTask, StrategyConstant.ACTION_TYPE_SHIELD_ALARM)) {
                indicatorOutlier.setAlarmShield(true);
                LOGGER.info("Alarm is shield, indicatorOutlier is {}", indicatorOutlier);
            }
            if (category == AlarmParameters.CATEGORY_NEW && !businessStrategyManager.getStrategyResult(analysisTask, StrategyConstant.ACTION_TYPE_SHIELD_ALARM)) {
                switchAlarmField = AlarmAddInfoBuilder.init()
                    .appendKeyValue("UUID",
                        UUID.randomUUID().toString().replaceAll(Separator.SEPARATOR_DASH, Separator.SEPARATOR_NULL))
                    .build();
                alarmRecord = AlarmRecordsHandler.generateAlarmRecord(category, indicatorOutlier.getStartTime(),
                    probableCause, AlarmParameters.ALARM_ID_INDICATOR, severity, mergeKey,
                    switchAlarmField, alarmName, indicator.getDn(), indicator.getMoType());
                LOGGER.info("Indicator report alarm switchAlarmField is {}", switchAlarmField);
            } else {
                // 需要从数据库中获取，否则无法清理
                switchAlarmField = indicatorOutlier.getSwitchAlarmField();
                if (switchAlarmField == null) {
                    LOGGER.error("Could not get switchAlarmField, maybe cleared or user replace the alarm info, taskId={}", taskId);
                }
                LOGGER.info("Indicator clear alarm switchAlarmField is {}", switchAlarmField);
                alarmRecord = AlarmRecordsHandler.generateAlarmRecord(category, indicatorOutlier.getEndTime(),
                    probableCause, AlarmParameters.ALARM_ID_INDICATOR, severity, mergeKey,
                    switchAlarmField, alarmName, indicator.getDn(), indicator.getMoType());
            }
            alarmRecord.setAdditionalInformation(getAdditionInfo(indicatorDetectResults, indicatorOutlier.getIndicatorId(), switchAlarmField, analysisTask));
            indicatorOutlier.setSwitchAlarmField(switchAlarmField);
            // 上报告警根据任务填写的告警id和名称上报
            changeReportAlarmInfo(alarmRecord, analysisTask);
            alarmRecordList.add(alarmRecord);
        }
    }

    private String getAdditionInfo(List<IndicatorDetectResult> indicatorDetectResults, String indicatorId, String switchAlarmField, AnalysisTask analysisTask) {
        // cbs要求附加信息展示阈值相关信息
        List<IndicatorDetectResult> resultList = indicatorDetectResults.stream()
            .filter(result -> result.getIndicatorId().equals(indicatorId))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resultList)) {
            LOGGER.error("getAdditionInfo fail, indicatorId={},indicatorDetectResults={}", indicatorId, indicatorDetectResults);
            return switchAlarmField;
        }
        List<ModelParameter> modelParameters = JSONArray.parseArray(analysisTask.getAlgorithmParam(),
            ModelParameter.class);
        boolean isUpperFlag = false;
        boolean isLowerFlag = false;
        for (ModelParameter modelParameter : modelParameters) {
            if (modelParameter.getParameterName().equals("upperlimit_flag") && "yes".equals(modelParameter.getParameterDefaultValue().toString())) {
                isUpperFlag = true;
            }
            if (modelParameter.getParameterName().equals("lowerlimit_flag") && "yes".equals(modelParameter.getParameterDefaultValue().toString())) {
                isLowerFlag = true;
            }
        }
        AlarmAddInfoBuilder addInfoBuilder = AlarmAddInfoBuilder.init();
        if (isUpperFlag) {
            addInfoBuilder.appendKeyValue(
                ResourceUtil.getMessage(InternationalConstant.UPPER_THRESHOLD, ContextUtils.getContext().getLocale()),
                String.valueOf(resultList.get(0).getUpperThreshold()));
        }
        if (isLowerFlag) {
            addInfoBuilder.appendKeyValue(
                ResourceUtil.getMessage(InternationalConstant.LOWER_THRESHOLD, ContextUtils.getContext().getLocale()),
                String.valueOf(resultList.get(0).getLowerThreshold()));
        }
        addInfoBuilder.appendKeyValue(
                ResourceUtil.getMessage(InternationalConstant.CURRENT_VALUE, ContextUtils.getContext().getLocale()),
                String.valueOf(resultList.get(0).getIndicatorValue()))
            .appendKeyValue(
                ResourceUtil.getMessage(InternationalConstant.OCCURRENCE_TIME, ContextUtils.getContext().getLocale()),
                TimeUtil.getStringByLong(resultList.get(0).getCollectTime()))
            .append(", " + switchAlarmField);
        return addInfoBuilder.build();
    }

    private void changeReportAlarmInfo(AlarmRecord alarmRecord, AnalysisTask analysisTask) {
        if (!OmcUtil.isOmcMode()) {
            alarmRecord.setAlarmId(analysisTask.getReportAlarmId());
            alarmRecord.setAlarmName(analysisTask.getReportAlarmName());
        }
    }

    public void alarmConstruction(AnalysisTask task, List<IndicatorOutlier> indicatorOutlierList, int category) {
        List<TaskIndicator> taskIndicators = task.getIndicatorList();
        if (CollectionUtils.isEmpty(taskIndicators)) {
            taskIndicators = taskIndicatorDao.getTaskIndicatorList(task.getTaskId(), null);
            if (CollectionUtils.isEmpty(taskIndicators)) {
                LOGGER.error("[alarmConstruction]can't find the indicator ,taskId is {}", task.getTaskId());
                return;
            }
        }
        String alarmName = AlarmRecordsHandler.indicatorAlarmNameI18n();
        List<AlarmRecord> alarmRecordList = new ArrayList<>();
        for (IndicatorOutlier indicatorOutlier : indicatorOutlierList) {
            List<TaskIndicator> filterIndicator = taskIndicators.stream()
                .filter(taskIndicator -> taskIndicator.getIndicatorId().equals(indicatorOutlier.getIndicatorId()))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterIndicator)) {
                LOGGER.error("[alarmConstruction]can't find the task or indicator ,taskId is {}, indicatorId is {}",
                    task.getTaskId(), indicatorOutlier.getIndicatorId());
                continue;
            }
            String switchAlarmField;
            if (category == 1) {
                switchAlarmField = AlarmAddInfoBuilder.init()
                    .appendKeyValue("UUID",
                        UUID.randomUUID().toString().replaceAll(Separator.SEPARATOR_DASH, Separator.SEPARATOR_NULL))
                    .build();
            } else {
                // 清除告警使用原来的switchAlarmField
                switchAlarmField = indicatorOutlier.getSwitchAlarmField();
            }

            AlarmRecord alarmRecord = genAlarmRecord(task, filterIndicator.get(0), alarmName, indicatorOutlier,
                category, switchAlarmField);

            alarmRecord.setAdditionalInformation(switchAlarmField);
            indicatorOutlier.setSwitchAlarmField(switchAlarmField);
            // 上报告警根据任务填写的告警id和名称上报
            changeReportAlarmInfo(alarmRecord, task);
            alarmRecordList.add(alarmRecord);
        }
        try {
            List<List<IndicatorOutlier>> subIndicatorOutliers = ListUtils.partition(indicatorOutlierList, MAX_DATA_SIZE);
            for (List<IndicatorOutlier> subIndicatorOutlierList : subIndicatorOutliers) {
                taskAnalysisResultDao.updateSwitchAlarmField(
                    JSON.parseArray(JSONObject.toJSONString(subIndicatorOutlierList),
                        com.huawei.i2000.dvanalysisengineservice.model.IndicatorOutlier.class));
            }
            AlarmRecordsHandler.pushAlarm(alarmRecordList, task.getTaskId(), StrategyConstant.MANUALLY_REPORT_ALARM);
        } catch (ServiceException e) {
            LOGGER.error("[alarmConstruction]push Alarm failed!", e);
        }
    }

    private AlarmRecord genAlarmRecord(AnalysisTask task, TaskIndicator indicator, String alarmName,
        IndicatorOutlier indicatorOutlier, int category, String additionalInfo) {
        String probableCause = genProbableCause(indicator, task);
        String mergeKey = task.getTaskName() + indicatorOutlier.getIndicatorId();
        AlarmRecord alarmRecord;
        if (category == AlarmParameters.CATEGORY_NEW) {
            alarmRecord = AlarmRecordsHandler.generateAlarmRecord(category, indicatorOutlier.getStartTime(),
                probableCause, AlarmParameters.ALARM_ID_INDICATOR, task.getAlarmType(), mergeKey,
                additionalInfo, alarmName, indicator.getDn(),
                indicator.getMoType());
        } else {
            alarmRecord = AlarmRecordsHandler.generateAlarmRecord(category, indicatorOutlier.getEndTime(),
                probableCause, AlarmParameters.ALARM_ID_INDICATOR, task.getAlarmType(), mergeKey,
                additionalInfo, alarmName, indicator.getDn(),
                indicator.getMoType());
        }
        return alarmRecord;
    }

    private String constructFailureCause(List<IndicatorDetectResult> failIndicatorDetectResults) {
        JSONObject failureList = new JSONObject();
        failIndicatorDetectResults.forEach(detectResult -> {
            failureList.put(detectResult.getIndicatorId(), detectResult.getFailureCause());
        });
        return JSONObject.toJSONString(failureList);
    }
}
