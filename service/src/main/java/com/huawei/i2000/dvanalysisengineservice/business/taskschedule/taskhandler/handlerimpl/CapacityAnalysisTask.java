/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.taskschedule.taskhandler.handlerimpl;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvanalysisengineservice.business.capacity.CapacityEamTreeAnalysis;
import com.huawei.i2000.dvanalysisengineservice.business.capacity.CapacityTaskUtils;
import com.huawei.i2000.dvanalysisengineservice.business.capacity.dto.AggregateTaskIndicator;
import com.huawei.i2000.dvanalysisengineservice.business.capacity.dto.MultipleEamNode;
import com.huawei.i2000.dvanalysisengineservice.business.common.ConfigurationUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.HofsUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.RestConstant;
import com.huawei.i2000.dvanalysisengineservice.business.common.RestUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.CapacityAnalysisEnum;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.CapacityAnalysisLocationParam;
import com.huawei.i2000.dvanalysisengineservice.business.common.pythoncmdexec.AlgorithmPyType;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.constant.SynAnalysisResultConstant;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.TaskPeriodInfo;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.ScheduleConstant;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.DataCenter;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.HandleDtoFactory;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.capacity.MultipleNodeMap;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.AbstractHandleDto;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.DataSourceIndicatorInfo;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.DataSourceInfo;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.taskhandler.AbstractTaskHandler;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.taskhandler.dto.CapacityTrainTaskData;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.taskhandler.dto.TaskScheduleRequestData;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.MultipleTreeModel;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.MultipleTreeNode;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.MultipleTreePath;
import com.huawei.i2000.dvanalysisengineservice.dao.multiple.mapper.CapacityAnalysisMapper;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.CapacityTaskManageDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTask;
import com.huawei.i2000.dvanalysisengineservice.model.CapacityHistoryTimeStamp;
import com.huawei.i2000.dvanalysisengineservice.model.CapacityPredictionTaskQuery;
import com.huawei.i2000.dvanalysisengineservice.model.ModelParameter;
import com.huawei.i2000.dvanalysisengineservice.model.MultipleExecutionNode;
import com.huawei.i2000.dvanalysisengineservice.model.MultipleRelationIndicator;
import com.huawei.i2000.dvanalysisengineservice.util.ApplicationContextHelper;
import com.huawei.i2000.dvanalysisengineservice.util.EnvUtils;
import com.huawei.i2000.dvanalysisengineservice.util.cipher.SHA256Util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * CapacityAnalysisTask 容量瓶颈分析
 * 容量瓶颈分析任务处理
 *
 * <AUTHOR>
 * @since 2022-06-02
 */

public class CapacityAnalysisTask extends AbstractTaskHandler {

    private static final String SRC_PATH_SEPARATOR = ";";

    private static final String SEPARATOR = "_";

    private static final String CAPACITY_DATA_SOURCE_ID = "1";

    private static final int SPLIT_LIMIT = 2;

    private static final Integer RESERVE_PREDICT_TAG = 1;

    public static final String GET_CAPACITY_PREDICTION_TASK
        = "/rest/dvanalysisengineservice/v1/multipleanalysis/getcapacitypredictiontask";

    public static final String CHECK_TRAIN = "/rest/dvanalysisengineservice/v1/taskschedule/checktrain";

    private static final int SUCCEED = 7;

    private static final int FAILED = 2;

    Map<String, MultipleNodeMap> nodesMap = new HashMap<>();

    private static final OssLog LOGGER = OssLogFactory.getLogger(IndicatorExceptionTask.class);

    /**
     * CapacityAnalysisTask
     *
     * @param analysisTask analysisTask
     * @param type type
     */
    public CapacityAnalysisTask(AnalysisTask analysisTask, Integer type) {
        super(analysisTask, type);
    }

    /**
     * 容量瓶颈训练实现处理过程
     */
    @Override
    public void handle() {
        try {
            CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
            Integer status = capacityAnalysisMapper.getCapacityTaskAggrStatus(analysisTask.getTaskId());
            long startTime = System.currentTimeMillis();
            while ((status == null || status != 1) && (System.currentTimeMillis() - startTime < 300000)) {
                status = capacityAnalysisMapper.getCapacityTaskAggrStatus(analysisTask.getTaskId());
                TimeUnit.SECONDS.sleep(5);
            }
            LOGGER.info("ready to train capacity task, taskId = {}", analysisTask.getTaskId());
            if (type == ScheduleConstant.PREDICT_TASK) {
                LOGGER.error("[CapacityAnalysisTask] capacity prediction task not provide auto predict : {}", analysisTask.getTaskName());
                return;
            }
            insertResult();
            unzipModel();

            if (TaskConstant.CAPACITY_MULTIPLE_TYPE != analysisTask.getMultipleTaskMessage().getMultipleTaskType()) {
                LOGGER.error("[CapacityAnalysisTask] capacity train type error : {}", analysisTask.getTaskName());
                throw new ServiceException("[CapacityAnalysisTask] capacity train type error");
            }

            // 1.Constructing Data Structures and save
            MultipleTreeModel multipleTreeModel = JSONObject.parseObject(analysisTask.getMultipleTaskMessage().getRelationTree(), MultipleTreeModel.class);

            // 资源树节点裁剪补充
            HashMap<String, MultipleEamNode> multipleEamNodeMap = CapacityEamTreeAnalysis.analysisEamMoTypeTree(multipleTreeModel);

            pretreatmentMultipleNode(analysisTask, nodesMap, multipleTreeModel, false);
            // 局部组网变化判断仅与此处对比 若变化改变子树 indicator表 node表 信息
            handleChangedEamMoTypeTree(multipleEamNodeMap, multipleTreeModel);

            // 2.Train
            executeMultiple(timespan);

        } catch (Throwable e) {
            LOGGER.error("Execute task schedule error : {}", e);
            handleError();
        }

        // 3.反向预测
        reservePredict();

        // 4.训练后重新推理
        rePredictAfterTrain();
    }

    /**
     * reservePredict
     * 执行反向预测
     *
     */
    public void reservePredict() {
        Integer ifReserve = taskAnalysisResultDao.updateCapacityQueryParam(analysisTask.getTaskId());
        if (ifReserve != null && ifReserve.equals(RESERVE_PREDICT_TAG)) {
            try {
                reversePrediction();
            } catch (Throwable e) {
                LOGGER.error("Execute task reverse predict error : {}", e);
            }
        }
    }

    /**
     * rePredictAfterTrain
     * 训练后重推理
     */
    public void rePredictAfterTrain() {
        Integer ifRePredict = taskAnalysisResultDao.queryRePredict(analysisTask.getTaskId());
        if (ifRePredict != null && ifRePredict.equals(RESERVE_PREDICT_TAG)) {
            try {
                AnalysisTask task = taskManager.getAnalysisTaskById(analysisTask.getTaskId());
                while (taskIsTraining(task.getTrainStatus())) {
                    Thread.sleep(10000);
                    task = taskManager.getAnalysisTaskById(analysisTask.getTaskId());
                }
                rePredictAfterTraining(analysisTask.getTaskId());
            } catch (ServiceException | InterruptedException e) {
                LOGGER.error("Execute task re predict error.", e);
            }
        }
    }

    private boolean taskIsTraining(Integer trainStatus) throws ServiceException {
        if (trainStatus == null || trainStatus.equals(TaskConstant.TRAIN_STATUS_RUNNING_TRANIN)) {
            return true;
        }
        if (trainStatus.equals(TaskConstant.TRAIN_STATUS_SUCCESS)
            || trainStatus.equals(TaskConstant.TRAIN_STATUS_PARTIAL_SUCCESS)) {
            return false;
        }
        throw new ServiceException("task train failed!");
    }

    /**
     * handleChangedEamMoTypeTree
     *
     * @param multipleEamNodeMap multipleEamNodeMap
     * @param multipleTreeModel multipleTreeModel
     * @throws ServiceException ServiceException
     */
    public void handleChangedEamMoTypeTree(HashMap<String, MultipleEamNode> multipleEamNodeMap, MultipleTreeModel multipleTreeModel)
        throws ServiceException {
        HashMap<String, List<String>> changedRootNodeIdMap = checkChangedEamMoTypeTree(multipleEamNodeMap, analysisTask.getTaskId());
        if (!changedRootNodeIdMap.isEmpty()) {
            HashMap<String, MultipleEamNode> saveMultipleEamNodeMap = new HashMap<>();
            for (Map.Entry<String, MultipleEamNode> entry: multipleEamNodeMap.entrySet()) {
                if (changedRootNodeIdMap.containsKey(entry.getValue().getEamRootNodeId())) {
                    saveMultipleEamNodeMap.put(entry.getKey(), entry.getValue());
                }
            }

            updateChangedMultipleExecutionNodes(changedRootNodeIdMap, analysisTask, timespan);
            saveEamTreeInfo(analysisTask.getTaskId(), multipleTreeModel, saveMultipleEamNodeMap);
            CapacityTaskUtils.getModelRelationTreeLocation(multipleTreeModel, CapacityAnalysisLocationParam.TRAINING_FIGURES_TYPE);
        }
    }

    private void reversePrediction() throws ServiceException {
        LOGGER.info("[CapacityAnalysisTask] start to reverse prediction ");
        RestfulParametes restfulParametes = new RestfulParametes();
        RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, CHECK_TRAIN, restfulParametes,
            null);
        if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
            LOGGER.error("[CapacityAnalysisTask] check train status error");
        }

        CapacityPredictionTaskQuery queryParam = new CapacityPredictionTaskQuery();
        queryParam.setTaskId(analysisTask.getTaskId());
        queryParam.setQueryType(CapacityAnalysisEnum.PREDICT_REVERSE);
        queryParam.setMainKpiValue("");

        RestfulParametes predictRestfulParameters = new RestfulParametes();
        predictRestfulParameters.setRawData(JSON.toJSONString(queryParam));
        RestfulResponse predictResponse = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, GET_CAPACITY_PREDICTION_TASK, predictRestfulParameters,
            null);

        if (predictResponse == null || predictResponse.getStatus() != RestConstant.STATUS_OK) {
            LOGGER.error("[CapacityAnalysisTask] reverse prediction error");
        }

        CapacityTaskManageDao capacityTaskManageDao = (CapacityTaskManageDao) ApplicationContextHelper.getBean(
            "capacityTaskManageDao");
        // 等待本次预测结束再开始下次预测
        try {
            Integer predictStatus = capacityTaskManageDao.getCapacityTaskPredictStatus(queryParam.getTaskId());
            while (predictStatus != null && predictStatus != SUCCEED && predictStatus != FAILED) {
                Thread.sleep(5000);
                predictStatus = capacityTaskManageDao.getCapacityTaskPredictStatus(queryParam.getTaskId());
            }
        } catch (ServiceException | InterruptedException e) {
            LOGGER.error("[CapacityAnalysisTask] reverse predict get predict status error.", e);
        }
    }

    /**
     * pretreatmentMultipleNode
     *
     * @param analysisTask analysisTask
     * @param nodesMap nodesMap
     * @param multipleTreeModel multipleTreeModel
     * @param ifPreload ifPreload
     * @throws ServiceException ServiceException
     */
    public void pretreatmentMultipleNode(AnalysisTask analysisTask, Map<String, MultipleNodeMap> nodesMap,
        MultipleTreeModel multipleTreeModel, Boolean ifPreload) throws ServiceException {
        List<String> nodeLoop = new ArrayList<String>();
        // 1.loop name
        String rootId = resolvesConstructor(analysisTask, nodesMap, multipleTreeModel, ifPreload);

        // 2.loop node father
        resolvePathInfo(nodesMap, multipleTreeModel);

        // 3.loop node
        getNodesMap(analysisTask, nodesMap, rootId, nodeLoop);

        // 4.build MultipleExecutionNode and set analysisTask MultipleTaskMessage
        getMultipleExecutionNode(analysisTask, nodesMap, nodeLoop);
    }

    public void saveEamTreeInfo(Integer taskId, MultipleTreeModel multipleTreeModel, HashMap<String, MultipleEamNode> multipleEamNodeMaps) {
        taskAnalysisResultDao.saveMultipleEamNode(multipleEamNodeMaps);
        taskAnalysisResultDao.saveEamRelationTree(taskId, JSONObject.toJSONString(multipleTreeModel));
    }

    /**
     * 判断组网变化部分
     *
     * @param multipleEamNodeMap multipleEamNodeMap
     * @param taskId taskId
     * @return changedNodeIds 组网变化的资源树节点
     * @throws ServiceException ServiceException
     */
    public HashMap<String, List<String>> checkChangedEamMoTypeTree(HashMap<String, MultipleEamNode> multipleEamNodeMap, Integer taskId) throws ServiceException {
        HashMap<String, List<String>> changedRootNodeIdMap = new HashMap<>();
        HashMap<String, HashMap<String, MultipleEamNode>> rootEamTreeNodeMap = new HashMap<>();
        for (Map.Entry<String, MultipleEamNode> entry: multipleEamNodeMap.entrySet()) {
            if (!rootEamTreeNodeMap.containsKey(entry.getValue().getEamRootNodeId())) {
                rootEamTreeNodeMap.put(entry.getValue().getEamRootNodeId(), new HashMap<>());
            }
            rootEamTreeNodeMap.get(entry.getValue().getEamRootNodeId()).put(entry.getKey(), entry.getValue());
        }

        for (Map.Entry<String, HashMap<String, MultipleEamNode>> entryRoot: rootEamTreeNodeMap.entrySet()) {
            List<MultipleEamNode> historyEamNodes = taskAnalysisResultDao.getMultipleEamNode(taskId, entryRoot.getKey());

            boolean ifChange = false;
            // 对比
            List<String> historyEamNodeIds = new ArrayList<>();
            for (MultipleEamNode historyEamNode : historyEamNodes) {
                historyEamNodeIds.add(historyEamNode.getNodeId());
                historyEamNode.setParentNodeIds(JSON.parseArray(historyEamNode.getParentNodeIdString(), String.class));
                List<String> list1 = historyEamNode.getParentNodeIds();
                if (!entryRoot.getValue().containsKey(historyEamNode.getNodeId())) {
                    ifChange = true;
                    break;
                }

                List<String> list2 = entryRoot.getValue().get(historyEamNode.getNodeId()).getParentNodeIds();
                if (!list1.containsAll(list2) || !list2.containsAll(list1)) {
                    ifChange = true;
                    break;
                }
            }

            List<String> eamNodeIds = new ArrayList<>(entryRoot.getValue().keySet());
            if (!historyEamNodeIds.containsAll(eamNodeIds) || !eamNodeIds.containsAll(historyEamNodeIds)) {
                ifChange = true;
            }

            if (ifChange) {
                List<String> changedNodeIds = new ArrayList<>();
                for (MultipleEamNode historyEamNode : historyEamNodes) {
                    changedNodeIds.add(historyEamNode.getNodeId());
                }
                changedRootNodeIdMap.put(entryRoot.getKey(), changedNodeIds);
            }
        }

        return changedRootNodeIdMap;
    }

    /**
     * 训练时修改根据组网变化修改数据库表
     *
     * @param changedRootNodeIdMap changedRootNodeIdMap
     * @param analysisTask analysisTask
     * @param timespan timespantotal
     * @throws ServiceException ServiceException
     */
    public void updateChangedMultipleExecutionNodes(HashMap<String, List<String>> changedRootNodeIdMap, AnalysisTask analysisTask, Long timespan)
        throws ServiceException {
        for (Map.Entry<String, List<String>> entryRoot: changedRootNodeIdMap.entrySet()) {
            for (String nodeId : entryRoot.getValue()) {
                taskAnalysisResultDao.deleteMultipleExecution(analysisTask.getTaskId(), nodeId);
            }
        }

        List<MultipleExecutionNode> multipleExecutionList = analysisTask.getMultipleTaskMessage().getMultipleExecutionList();
        for (int i = 1;i < multipleExecutionList.size();i++) {
            if (multipleExecutionList.get(i).getEamExecutionNodeId() != null && changedRootNodeIdMap.containsKey(multipleExecutionList.get(i).getEamExecutionNodeId())) {
                for (MultipleRelationIndicator taskIndicator : multipleExecutionList.get(i).getIndicatorList()) {
                    taskIndicator.setTaskId(analysisTask.getTaskId());
                    taskIndicator.setIsRoot(false);
                    taskIndicator.setNodeId(multipleExecutionList.get(i).getId());
                    taskIndicator.setNodeUid(multipleExecutionList.get(i).getUid());
                }
                saveExecutionNode(i, multipleExecutionList.get(i), timespan);
            }
        }
    }

    /**
     * resolvesConstructor
     * Tree Model 预处理第一步 取主指标Id 虑去双机汇聚指标
     *
     * @param analysisTask analysisTask
     * @param nodesMap nodesMap
     * @param multipleTreeModel multipleTreeModel
     * @param ifPreload ifPreload
     * @return rootId
     * @throws ServiceException ServiceException
     */
    private String resolvesConstructor(AnalysisTask analysisTask, Map<String, MultipleNodeMap> nodesMap, MultipleTreeModel multipleTreeModel, Boolean ifPreload)
        throws ServiceException {
        String rootId = "";
        for (MultipleTreeNode abstractNode : multipleTreeModel.getNodes()) {
            abstractNode.setUid(UUID.randomUUID()
                .toString()
                .replaceAll(SynAnalysisResultConstant.Separator.SEPARATOR_DASH,
                    SynAnalysisResultConstant.Separator.SEPARATOR_NULL));

            MultipleNodeMap multipleNodeMap = new MultipleNodeMap(abstractNode.getId(), abstractNode);
            nodesMap.put(abstractNode.getId(), multipleNodeMap);
            if (CapacityTaskUtils.isMainType(abstractNode) && rootId.isEmpty()) {
                rootId = abstractNode.getId();
            } else if (CapacityTaskUtils.isMainType(abstractNode)) {
                LOGGER.error("[CapacityAnalysisTask]data error too many start : {}", analysisTask.getTaskName());
                throw new ServiceException("[CapacityAnalysisTask]data error too many start");
            }

            if (CapacityTaskUtils.isHighAvailable(abstractNode)) {
                for (MultipleRelationIndicator aggRelationIndicator : abstractNode.getNodeParam().getIndicatorList()) {
                    aggRelationIndicator.setIsAggregate(true);
                }
            }

            // 双机指标抽象化 仅在训练及推理前抽象
            if (!ifPreload && CapacityTaskUtils.isHighAvailable(abstractNode) && abstractNode.getNodeParam().getIndicatorList().size() > 1) {
                resolveAggregateIndicator(abstractNode);
            }
        }
        return rootId;
    }

    /**
     * resolveAggregateIndicator 双机指标抽象化 仅在训练及推理前抽象
     *
     * @param abstractNode abstractNode
     * @throws ServiceException ServiceException
     */
    private void resolveAggregateIndicator(MultipleTreeNode abstractNode) throws ServiceException {
        List<AggregateTaskIndicator> aggregateTaskIndicators = taskAnalysisResultDao.
            getAggregateIndicates(analysisTask.getTaskId(), abstractNode.getId() ,false);
        List<String> indicatorIds = new ArrayList<>();
        for (AggregateTaskIndicator agg : aggregateTaskIndicators) {
            if (!agg.isIfIgnore()) {
                indicatorIds.add(agg.getIndicatorId());
            }
        }
        List<MultipleRelationIndicator> fixIndicatorList = new ArrayList<>();
        for (MultipleRelationIndicator aggRelationIndicator : abstractNode.getNodeParam().getIndicatorList()) {
            if (indicatorIds.contains(aggRelationIndicator.getIndicatorId())) {
                aggRelationIndicator.setIsAggregate(true);
                fixIndicatorList.add(aggRelationIndicator);
            }
        }
        abstractNode.getNodeParam().setIndicatorList(fixIndicatorList);
        LOGGER.debug("abstractNode getNodeParam {} {}", JSONObject.toJSONString(abstractNode.getNodeParam()));
    }

    /**
     * resolvePathInfo
     * 预处理第二步 将path连线关系转化为node父子关系
     *
     * @param nodesMap nodesMap
     * @param multipleTreeModel multipleTreeModel
     */
    private void resolvePathInfo(Map<String, MultipleNodeMap> nodesMap, MultipleTreeModel multipleTreeModel) {
        for (MultipleTreePath abstractPath : multipleTreeModel.getPaths()) {
            String targetId = abstractPath.getTarget();
            String sourceId = abstractPath.getSource();
            nodesMap.get(targetId).addFatherNodeIds(sourceId);
        }
    }

    /**
     * getNodesMap
     * 预处理第三步 根据指标父子关系得到调用链
     *
     * @param analysisTask analysisTask
     * @param nodesMap nodesMap
     * @param rootId rootId
     * @param nodeLoop nodeLoop
     * @throws ServiceException ServiceException
     */
    private void getNodesMap(AnalysisTask analysisTask, Map<String, MultipleNodeMap> nodesMap, String rootId, List<String> nodeLoop)
        throws ServiceException {
        ArrayList<String> leftNodes = new ArrayList<>(nodesMap.keySet());
        nodeLoop.add(rootId);
        leftNodes.remove(rootId);

        while (!leftNodes.isEmpty()) {
            ArrayList<String> lastLeftNodes = new ArrayList<>(leftNodes);
            for (String nodeId : leftNodes) {
                MultipleNodeMap multipleNodeMap = nodesMap.get(nodeId);
                ArrayList<String> getRetainAll = new ArrayList<>(multipleNodeMap.getFatherNodeIds());
                getRetainAll.retainAll(leftNodes);
                if (getRetainAll.isEmpty()) {
                    nodeLoop.add(multipleNodeMap.getNodeId());
                    lastLeftNodes.remove(multipleNodeMap.getNodeId());
                }
            }
            if (lastLeftNodes.equals(leftNodes)) {
                LOGGER.error("[CapacityAnalysisTask]data error existence loop : {}", analysisTask.getTaskName());
                throw new ServiceException("[CapacityAnalysisTask]data error existence loop");
            }
            leftNodes = new ArrayList<>(lastLeftNodes);
        }
    }

    /**
     * getMultipleExecutionNode
     * 预处理第三步 构造每个节点计算用结构体 dependenceIndicatorList
     *
     * @param analysisTask analysisTask
     * @param nodesMap nodesMap
     * @param nodeLoop nodeLoop
     */
    private void getMultipleExecutionNode(AnalysisTask analysisTask, Map<String, MultipleNodeMap> nodesMap, List<String> nodeLoop) {
        List<MultipleExecutionNode> multipleExecutionNodes = new ArrayList<MultipleExecutionNode>();
        for (String nodeId : nodeLoop) {
            MultipleNodeMap multipleNodeMap = nodesMap.get(nodeId);
            for (MultipleRelationIndicator nodeTaskIndicator : multipleNodeMap.getMultipleTreeNode().getNodeParam().getIndicatorList()) {
                nodeTaskIndicator.setIndicatorId(CapacityTaskUtils.getIndicatorId(nodeTaskIndicator));
            }

            List<MultipleRelationIndicator> dependenceIndicatorList = new ArrayList<>();
            List<String> fatherNodeIds = new ArrayList<>(multipleNodeMap.getFatherNodeIds());
            for (String fid : fatherNodeIds) {
                MultipleNodeMap fatherMultipleMap = nodesMap.get(fid);
                for (MultipleRelationIndicator taskIndicator : fatherMultipleMap.getMultipleTreeNode().getNodeParam().getIndicatorList()) {
                    taskIndicator.setTaskId(analysisTask.getTaskId());
                    MultipleRelationIndicator dependenceIndicator = JSON.parseObject(JSON.toJSONString(taskIndicator),
                        MultipleRelationIndicator.class);
                    dependenceIndicator.setTargetExecutionUId(multipleNodeMap.getMultipleTreeNode().getUid());
                    dependenceIndicator.setTargetNodeId(nodeId);
                    dependenceIndicatorList.add(dependenceIndicator);
                }
            }

            MultipleExecutionNode multipleExecutionNode = getNewMultipleExecutionNode(nodeId, dependenceIndicatorList, analysisTask, multipleNodeMap);
            multipleExecutionNodes.add(multipleExecutionNode);
            multipleNodeMap.getMultipleTreeNode().setNodeParam(multipleExecutionNode);
        }
        analysisTask.getMultipleTaskMessage().setMultipleExecutionList(multipleExecutionNodes);
    }

    /**
     * getNewMultipleExecutionNode
     *
     * @param nodeId nodeId
     * @param dependenceIndicatorList dependenceIndicatorList
     * @param analysisTask analysisTask
     * @param multipleNodeMap multipleNodeMap
     * @return MultipleExecutionNode MultipleExecutionNode
     */
    private MultipleExecutionNode getNewMultipleExecutionNode(String nodeId, List<MultipleRelationIndicator> dependenceIndicatorList, AnalysisTask analysisTask, MultipleNodeMap multipleNodeMap) {
        MultipleExecutionNode multipleExecutionNode = new MultipleExecutionNode();
        multipleExecutionNode.setTaskId(analysisTask.getTaskId());
        multipleExecutionNode.setId(nodeId);
        multipleExecutionNode.setCreateUser("1");
        multipleExecutionNode.setDependenceIndicatorList(dependenceIndicatorList);
        multipleExecutionNode.setUid(multipleNodeMap.getMultipleTreeNode().getUid());
        multipleExecutionNode.setIndicatorList(multipleNodeMap.getMultipleTreeNode().getNodeParam().getIndicatorList());
        multipleExecutionNode.setAssociatedPredictTaskId(multipleNodeMap.getMultipleTreeNode().getNodeParam().getAssociatedPredictTaskId());
        multipleExecutionNode.setAssociatedTwoMachineGroupName(multipleNodeMap.getMultipleTreeNode().getNodeParam().getAssociatedTwoMachineGroupName());
        multipleExecutionNode.setAssociatedPredictIndicator(multipleNodeMap.getMultipleTreeNode().getNodeParam().getAssociatedPredictIndicator());
        multipleExecutionNode.setEamExecutionNodeId(multipleNodeMap.getMultipleTreeNode().getNodeParam().getEamExecutionNodeId());
        multipleExecutionNode.setNodeType(multipleNodeMap.getMultipleTreeNode().getNodeParam().getNodeType());
        multipleExecutionNode.setNodeName(multipleNodeMap.getMultipleTreeNode().getTitle());
        multipleExecutionNode.setIndicatorSelectType(multipleNodeMap.getMultipleTreeNode().getNodeParam().getIndicatorSelectType());
        multipleExecutionNode.setIfNetChange(multipleNodeMap.getMultipleTreeNode().getNodeParam().getIfNetChange());
        multipleExecutionNode.setSolutionId(multipleNodeMap.getMultipleTreeNode().getNodeParam().getSolutionId());
        multipleExecutionNode.setSolutionName(multipleNodeMap.getMultipleTreeNode().getNodeParam().getSolutionName());

        return multipleExecutionNode;
    }

    /**
     * executeMultiple 容量训练 一次任务 一次数据拉取(任务上) 多个模型训练
     *
     * @param timespan timespan 时间
     * @throws ServiceException ServiceException
     */
    private void executeMultiple(Long timespan) throws ServiceException  {
        analysisTask.setDatasourceId(CAPACITY_DATA_SOURCE_ID);
        AbstractHandleDto abstractHandleDto = HandleDtoFactory.getInstance().createMultipleHandleDto(Integer.parseInt(analysisTask.getDatasourceId()),
            nodesMap, timespan, analysisTask);

        Map<String, DataSourceIndicatorInfo> dataSourceIndicatorInfoMap = DataCenter.getInstance().dispatchDetail(abstractHandleDto);

        List<MultipleExecutionNode> multipleExecutionList = analysisTask.getMultipleTaskMessage().getMultipleExecutionList();

        List<ModelParameter> parametersArray = JSONArray.parseArray(analysisTask.getAlgorithmParam(),
            ModelParameter.class);
        HashMap<String, Object> param = new HashMap<>();
        for (ModelParameter modelParameter : parametersArray) {
            param.put(modelParameter.getParameterName(), modelParameter.getParameterDefaultValue());
        }

        LOGGER.info("[CapacityAnalysisTask] start to send , taskName = {}", analysisTask.getTaskName());
        // 训练中不保存saveRootNode
        // 存训练状态 模型是否产生 用于判断链路是否进行
        HashMap<String, Integer> ifRouteStopsMap = new HashMap<>();
        for (int i = 1;i < multipleExecutionList.size();i++) {
            for (MultipleRelationIndicator dependenceDataTask : multipleExecutionList.get(i).getDependenceIndicatorList()) {
                if (ifRouteStopsMap.containsKey(dependenceDataTask.getIndicatorId()) &&
                    ifRouteStopsMap.get(dependenceDataTask.getIndicatorId()).equals(CapacityAnalysisEnum.ERROR_INDICATOR_TRAIN_TYPE)) {
                    for (MultipleRelationIndicator indicator : multipleExecutionList.get(i).getIndicatorList()){
                        ifRouteStopsMap.put(indicator.getIndicatorId(), CapacityAnalysisEnum.ERROR_INDICATOR_TRAIN_TYPE);
                    }
                    break;
                }
                if (dependenceDataTask.getIsAggregate()) {
                    break;
                }
            }

            updateMultipleExecutionNode(ifRouteStopsMap, timespan, multipleExecutionList.get(i), dataSourceIndicatorInfoMap, param, analysisTask);
            indicatorNum += multipleExecutionList.get(i).getIndicatorList().size();
        }

        taskAnalysisResultDao.updateCapacityTaskPredictStatus(CapacityAnalysisEnum.PREDICT_STOP_STATUS, analysisTask.getTaskId());
        trainingTaskManager.setTrainingTaskMessage(analysisTask.getTaskId(), indicatorNum);
    }

    public void saveRootNode(AnalysisTask analysisTask, Long timespan, MultipleExecutionNode rootExecutionNode) {
        // 不止一个 双机
        for  (int i=0 ; i < rootExecutionNode.getIndicatorList().size() ; i++ ) {
            MultipleRelationIndicator rootIndicator = rootExecutionNode.getIndicatorList().get(i);
            rootIndicator.setTaskId(analysisTask.getTaskId());
            rootIndicator.setIsRoot(i == 0);
            rootIndicator.setNodeId(rootExecutionNode.getId());
            rootIndicator.setNodeUid(rootExecutionNode.getUid());
        }
        rootExecutionNode.setSortNum(0);
        rootExecutionNode.setStatus(CapacityAnalysisEnum.SUCCESS);
        rootExecutionNode.setExecutionTime(timespan);
        rootExecutionNode.setNodeType(CapacityAnalysisEnum.COMMON_NODE_TYPE);
        taskAnalysisResultDao.saveMultipleExecution(rootExecutionNode);
    }

    private DataSourceInfo getBaseTargetInfo(MultipleExecutionNode multipleExecutionNode,
        Map<String, DataSourceIndicatorInfo> dataSourceIndicatorInfoMap) {
        DataSourceInfo targetInfo = new DataSourceInfo();
        targetInfo.setSrcFilePath("");
        for (MultipleRelationIndicator fatherTaskIndicator : multipleExecutionNode.getDependenceIndicatorList()) {
            if (!dataSourceIndicatorInfoMap.containsKey(fatherTaskIndicator.getIndicatorId())) {
                continue;
            }
            String fatherSrcFilePath = dataSourceIndicatorInfoMap.get(fatherTaskIndicator.getIndicatorId()).getSrcFilePath();
            targetInfo.setSrcFilePath(targetInfo.getSrcFilePath() + fatherSrcFilePath + SRC_PATH_SEPARATOR);
        }
        return targetInfo;
    }

    private void setBaseTaskIndicator(MultipleRelationIndicator taskIndicator, MultipleExecutionNode multipleExecutionNode, AnalysisTask task) {
        taskIndicator.setTaskId(task.getTaskId());
        taskIndicator.setIsRoot(false);
        taskIndicator.setNodeId(multipleExecutionNode.getId());
        taskIndicator.setNodeUid(multipleExecutionNode.getUid());
    }

    public void trainMultipleExecutionNode(HashMap<String, Integer> ifRouteStopsMap, Long timespan, MultipleExecutionNode multipleExecutionNode,
        Map<String, DataSourceIndicatorInfo> dataSourceIndicatorInfoMap, HashMap<String, Object> param, AnalysisTask task)
        throws ServiceException {
        DataSourceInfo targetInfo = getBaseTargetInfo(multipleExecutionNode, dataSourceIndicatorInfoMap);

        for (MultipleRelationIndicator taskIndicator : multipleExecutionNode.getIndicatorList()) {
            // 名称一致
            setBaseTaskIndicator(taskIndicator, multipleExecutionNode, task);
            DataSourceInfo newTargetInfo = JSON.parseObject(JSON.toJSONString(targetInfo), DataSourceInfo.class);
            if (!dataSourceIndicatorInfoMap.containsKey(taskIndicator.getIndicatorId())) {
                continue;
            }
            String targetSrcFilePath = dataSourceIndicatorInfoMap.get(taskIndicator.getIndicatorId()).getSrcFilePath();
            newTargetInfo.setSrcFilePath(newTargetInfo.getSrcFilePath() + targetSrcFilePath + SRC_PATH_SEPARATOR);
            newTargetInfo.setDestFilePath(dataSourceIndicatorInfoMap.get(taskIndicator.getIndicatorId()).getDestFilePath());

            Map<String, String> sourceTypeToSrcPath = new HashMap<>();
            sourceTypeToSrcPath.put(task.getDatasourceId(), newTargetInfo.getSrcFilePath());

            TaskScheduleRequestData data = new TaskScheduleRequestData();
            data.setTaskStartTime(timespan);
            CapacityTrainTaskData dataTaskId = new CapacityTrainTaskData(multipleExecutionNode.getUid(), taskIndicator.getIndicatorId());
            data.setTaskid(task.getTaskId().toString());
            data.setAlgorithmParam(param);
            data.createDataSourceInfo(sourceTypeToSrcPath, newTargetInfo);
            data.clearPassword();

            if (ifRouteStopsMap.containsKey(taskIndicator.getIndicatorId()) &&
                ifRouteStopsMap.get(taskIndicator.getIndicatorId()).equals(CapacityAnalysisEnum.ERROR_INDICATOR_TRAIN_TYPE)) {
                LOGGER.info("[CapacityAnalysisTask] route stop training error with {}", taskIndicator.getIndicatorId());
                handleExecutionError(ifRouteStopsMap, dataTaskId);
            }

            LOGGER.debug("[CapacityAnalysisTask] start to send {}", taskIndicator.getIndicatorId());
            if (sendRequest(ifRouteStopsMap, JSONObject.toJSONString(data), dataTaskId)) {
                uploadHOFS(ifRouteStopsMap,newTargetInfo.getDestFilePath(), dataTaskId);
            }
        }
    }

    public void updateNoTrainingExecutionNode(AnalysisTask analysisTask, Long timespan) {
        List<MultipleExecutionNode> multipleExecutionList = analysisTask.getMultipleTaskMessage().getMultipleExecutionList();
        saveRootNode(analysisTask, timespan, multipleExecutionList.get(0));
        for (int i = 1;i < multipleExecutionList.size();i++) {
            for (MultipleRelationIndicator taskIndicator : multipleExecutionList.get(i).getIndicatorList()) {
                taskIndicator.setTaskId(analysisTask.getTaskId());
                taskIndicator.setIsRoot(false);
                taskIndicator.setNodeId(multipleExecutionList.get(i).getId());
                taskIndicator.setNodeUid(multipleExecutionList.get(i).getUid());
            }
            saveExecutionNode(i, multipleExecutionList.get(i), timespan);
        }
    }

    private void updateMultipleExecutionNode(HashMap<String, Integer> ifRouteStopsMap, Long timespan, MultipleExecutionNode multipleExecutionNode,
        Map<String, DataSourceIndicatorInfo> dataSourceIndicatorInfoMap, HashMap<String, Object> param, AnalysisTask task)
        throws ServiceException {
        trainMultipleExecutionNode(ifRouteStopsMap, timespan, multipleExecutionNode, dataSourceIndicatorInfoMap, param, task);
        // 训练中不保存saveExecutionNode
    }

    public void saveExecutionNode(int executionNodeSort, MultipleExecutionNode multipleExecutionNode, Long timespan) {
        multipleExecutionNode.setSortNum(executionNodeSort);
        multipleExecutionNode.setIsRoot(false);
        multipleExecutionNode.setUpdateTime(System.currentTimeMillis());
        // 有模型success
        multipleExecutionNode.setStatus(CapacityAnalysisEnum.SUCCESS);
        multipleExecutionNode.setExecutionTime(timespan);
        multipleExecutionNode.setNodeType(CapacityAnalysisEnum.COMMON_NODE_TYPE);
        taskAnalysisResultDao.saveMultipleExecution(multipleExecutionNode);
    }

    private Boolean sendRequest(HashMap<String, Integer> ifRouteStopsMap, String param, CapacityTrainTaskData dataTaskId) throws ServiceException {
        try {
            // 查询调用的算法包信息
            String algorithmPath = analysisTask.getAlgorithmModelName();
            // 执行py脚本
            String result;
            result = pyExecutor.execute(algorithmPath, AlgorithmPyType.TRAIN, param);

            // 设置执行时间
            LOGGER.debug("exec result:{}", result);
            handleExecutionSuccess(ifRouteStopsMap, dataTaskId);
            return true;

        } catch (Throwable exception) {
            LOGGER.error("[CapacityAnalysisTask] Execute task py error : {}", param);
            handleExecutionError(ifRouteStopsMap, dataTaskId);
            return false;
        }
    }

    private void uploadHOFS(HashMap<String, Integer> ifRouteStopsMap, String localPath, CapacityTrainTaskData dataTaskId) throws ServiceException {
        String allLocalPath = ConfigurationUtil.algorithmPackagePath() + analysisTask.getAlgorithmModelName()
            + File.separator + "model" + File.separator + localPath;
        LOGGER.info("[CapacityAnalysisTask] start to upload HOFS with : {}", allLocalPath);
        try {
            String fileDir = SHA256Util.decrypt(dataTaskId.getIndicatorId());
            String hofsPath = HofsUtil.HOFS_PATH_MODEL + analysisTask.getTaskId() + File.separator + fileDir;
            File localModelDir = new File(allLocalPath);
            if (Objects.isNull(localModelDir.listFiles())) {
                throw new ServiceException("[CapacityAnalysisTask] upload HOFS error about : no model file");
            }
            for (File modelFile : localModelDir.listFiles()) {
                String modelFileName = allLocalPath + File.separator + modelFile.getName();
                HofsUtil.uploadFile(hofsPath + File.separator + modelFile.getName(), modelFileName);
            }
            deleteModelFile(allLocalPath);
        } catch (Throwable exception) {
            LOGGER.error("[CapacityAnalysisTask] upload HOFS error about : {}", exception);
            handleExecutionError(ifRouteStopsMap, dataTaskId);
        }
    }

    private void deleteModelFile(String allLocalPath) {
        File[] fileList = new File(allLocalPath).listFiles();
        for (File file : fileList) {
            if (file.delete()) {
                LOGGER.error("[deleteModelFile] deleteModel fail, fileName={}", file.getName());
            }
        }
    }

    private void unzipModel() throws ServiceException {
        String[] algorithmModelMessages = analysisTask.getAlgorithmModelName().split(SEPARATOR);
        algorithmManageService.unzipModel(algorithmModelMessages[0], algorithmModelMessages[1]);
    }

    @Override
    protected void insertResult() throws ServiceException{
        deleteHistoryTrain();
        taskAnalysisResultDao.insertResult(analysisTask.getTaskId(), timespan, TaskConstant.EXECUTION_TYPE_TRAINING,
            TaskConstant.EXECUTION_STATUS_PROCESSION);
        try {
            String runningNodeAddress = EnvUtils.getRunningNodeAddress();
            taskAnalysisResultDao.insertTrainAddress(analysisTask.getTaskId(), runningNodeAddress);
        } catch (ServiceException e) {
            LOGGER.error("[CapacityAnalysisTask] save running node address failed! ", e);
        }
    }

    public void deleteHistoryTrain() throws ServiceException {
        // 重训练
        // 训练中不删除 deleteHistoricalCapacityTask
        // 暂时PREDICT_STOP_STATUS 局部组网变化时应该为PREDICT_BUILD_STATUS
        taskManageDao.deleteTaskResult(analysisTask.getTaskId());
        taskAnalysisResultDao.updateExecutionResultReTrain(analysisTask.getTaskId());
        taskAnalysisResultDao.updateCapacityTaskPredictStatus(CapacityAnalysisEnum.PREDICT_STOP_STATUS, analysisTask.getTaskId());
        // 删除HOFS
        String hofsPath = HofsUtil.HOFS_PATH_MODEL + analysisTask.getTaskId();
        HofsUtil.deleteAllFile(hofsPath);
    }

    @Override
    protected void checkAndUpdateModel(List<String> fileList) {
    }

    @Override
    protected void updateResult() {
    }

    @Override
    protected void handleResult(TaskPeriodInfo taskPeriod) {
    }

    private void handleError() {
        if (type == ScheduleConstant.TRAINNING_TASK) {
            LOGGER.error("train task error, taskId = {}", analysisTask.getTaskId());
            taskManageDao.updateTrainStatus(analysisTask.getTaskId(), TaskConstant.TRAIN_STATUS_FAIL);
            trainingTaskManager.deteleTrainingTaskMessage(analysisTask.getTaskId());
            taskAnalysisResultDao.updateCapacityTaskPredictStatus(CapacityAnalysisEnum.PREDICT_STOP_STATUS, analysisTask.getTaskId());
        }
    }

    private void handleExecutionError(HashMap<String, Integer> ifRouteStopsMap, CapacityTrainTaskData dataTaskId) throws ServiceException {
        // 该字段已无用 资源树重训练用的
        taskAnalysisResultDao.updateCapacityExecutionResult(System.currentTimeMillis(), CapacityAnalysisEnum.FAIL,
            dataTaskId.getNodeUid(), analysisTask.getTaskId());
        // 模型训练状态置为失败 位置在指标表 因为除了主指标外一个指标一个模型
        MultipleRelationIndicator tmpMultipleIndicator = new MultipleRelationIndicator();
        tmpMultipleIndicator.setIndicatorId(dataTaskId.getIndicatorId());
        tmpMultipleIndicator.setTaskId(analysisTask.getTaskId());
        tmpMultipleIndicator.setTrainResult(CapacityAnalysisEnum.ERROR_INDICATOR_TRAIN_TYPE);
        taskAnalysisResultDao.updateExecutionAlgorithmResult(tmpMultipleIndicator);
        ifRouteStopsMap.put(dataTaskId.getIndicatorId(), CapacityAnalysisEnum.ERROR_INDICATOR_TRAIN_TYPE);
    }

    private void handleExecutionSuccess(HashMap<String, Integer> ifRouteStopsMap, CapacityTrainTaskData dataTaskId) throws ServiceException {
        taskAnalysisResultDao.updateCapacityExecutionResult(System.currentTimeMillis(), CapacityAnalysisEnum.SUCCESS,
            dataTaskId.getNodeUid(), analysisTask.getTaskId());
        MultipleRelationIndicator tmpMultipleIndicator = new MultipleRelationIndicator();
        tmpMultipleIndicator.setIndicatorId(dataTaskId.getIndicatorId());
        tmpMultipleIndicator.setTaskId(analysisTask.getTaskId());
        tmpMultipleIndicator.setTrainResult(CapacityAnalysisEnum.SUCCESS_INDICATOR_TRAIN_TYPE);
        taskAnalysisResultDao.updateExecutionAlgorithmResult(tmpMultipleIndicator);
        ifRouteStopsMap.put(dataTaskId.getIndicatorId(), CapacityAnalysisEnum.SUCCESS_INDICATOR_TRAIN_TYPE);
    }

    private void rePredictAfterTraining(Integer taskId) throws ServiceException {
        LOGGER.info("[CapacityAnalysisTask] start to re prediction, taskId = {}", taskId);
        CapacityTaskManageDao capacityTaskManageDao = (CapacityTaskManageDao) ApplicationContextHelper.getBean(
            "capacityTaskManageDao");
        // 取最近5条记录重新预测，完成后删除旧的预测记录
        List<CapacityHistoryTimeStamp> results = capacityTaskManageDao.getHistoryTimeStamp(taskId);
        for (CapacityHistoryTimeStamp result : results) {
            CapacityPredictionTaskQuery queryParam = startCapacityPredictTask(taskId, result);
            taskAnalysisResultDao.deleteOneHistoricalCapacityTask(result.getId());
            // 等待本次预测结束再开始下次预测
            try {
                Integer predictStatus = capacityTaskManageDao.getCapacityTaskPredictStatus(queryParam.getTaskId());
                while (predictStatus != null && predictStatus != SUCCEED && predictStatus != FAILED) {
                    Thread.sleep(5000);
                    predictStatus = capacityTaskManageDao.getCapacityTaskPredictStatus(queryParam.getTaskId());
                }
            } catch (ServiceException | InterruptedException e) {
                LOGGER.error("[CapacityAnalysisTask] re prediction get predict status error.", e);
                return;
            }
        }
        LOGGER.info("[CapacityAnalysisTask] end to re prediction, taskId = {}", taskId);
    }

    private CapacityPredictionTaskQuery startCapacityPredictTask(Integer taskId, CapacityHistoryTimeStamp result)
        throws ServiceException {
        CapacityPredictionTaskQuery queryParam = new CapacityPredictionTaskQuery();
        queryParam.setTaskId(analysisTask.getTaskId());
        queryParam.setQueryType(CapacityAnalysisEnum.PREDICT_RUNNING);
        queryParam.setIsAutoTrigger(true);
        if (StringUtils.isNotEmpty(result.getMainTimeRange())) {
            queryParam.setMainTimeRange(result.getMainTimeRange());
            queryParam.setMainKpiValue("");
        } else {
            queryParam.setMainKpiValue(result.getMainKpiValue());
        }
        // 发送请求，启动任务
        RestfulParametes predictRestfulParameters = new RestfulParametes();
        predictRestfulParameters.setRawData(JSON.toJSONString(queryParam));
        RestfulResponse predictResponse = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
            GET_CAPACITY_PREDICTION_TASK, predictRestfulParameters, null);

        if (predictResponse == null || predictResponse.getStatus() != RestConstant.STATUS_OK) {
            LOGGER.error("[CapacityAnalysisTask] re prediction error, taskId = {}, kpiValue = {}, "
                + "timeRange = {}", taskId, result.getMainKpiValue(), result.getMainTimeRange());
        }
        return queryParam;
    }
}