/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.dao.taskmanage;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.ResourceScheduleIndicatorResult;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.ResourceScheduleNumberResult;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.BusinessConfig;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.DependentIndicators;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.PerformanceIndexValueSchedule;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.ProductPortrait;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.ResourceScheduleExecuteDetail;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.ScheduleReplicas;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.ScheduleStrategy;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.ScheduleTaskConfig;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ResourceSchedule.VmPortrait;
import com.huawei.i2000.dvanalysisengineservice.dao.common.MapperFactory;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.mapper.ResourceScheduleMapper;
import com.huawei.i2000.dvanalysisengineservice.model.BusinessPortrait;
import com.huawei.i2000.dvanalysisengineservice.model.PerformanceIndexValue;
import com.huawei.i2000.dvanalysisengineservice.model.ResourceScheduleRecord;
import com.huawei.i2000.dvanalysisengineservice.model.TaskIndicator;

import com.alibaba.fastjson2.JSON;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2023-05-08
 */
@Component
public class ResourceScheduleDao {

    private static final OssLog LOGGER = OssLogFactory.getLogger(ResourceScheduleDao.class);

    private static final int DATA_TYPE_AVG = 0;

    private static final int DATA_TYPE_SUM = 1;

    private static final int DATA_TYPE_NUM = 2;

    @Autowired
    MapperFactory mapperFactory;

    /**
     * 获取调度曲线
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param startTime startTime
     * @param endTime endTime
     * @return List<PerformanceIndexValue>
     */
    public List<PerformanceIndexValue> getScheduleData(Integer taskId, String clusterName, Long startTime, Long endTime) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        String tableName = "TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_" + taskId;
        try {
            return resourceScheduleMapper.getScheduleData(tableName, clusterName, startTime, endTime);
        } catch (DataAccessException e) {
            LOGGER.error("[ResourceScheduleDao] getScheduleData error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询用户配置的调度任务详情
     *
     * @param taskId taskId
     * @return ScheduleTaskConfig
     */
    public ScheduleTaskConfig getScheduleTaskConfig(Integer taskId) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getScheduleTaskConfig(taskId);
        } catch (DataAccessException e) {
            LOGGER.error("[ResourceScheduleDao] getScheduleData error : {}", e);
            return null;
        }
    }

    /**
     * 获取上次执行详情
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param startTime startTime
     * @param endTime endTime
     * @return ResourceScheduleExecuteDetail
     */
    public ResourceScheduleExecuteDetail getLastScheduleExecuteDetail(Integer taskId, String clusterName, Long startTime, Long endTime) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getLastScheduleExecuteDetail(taskId, clusterName, startTime, endTime);
        } catch (DataAccessException e) {
            LOGGER.error("[ResourceScheduleDao] getLastScheduleExecuteDetail error : {}", e);
            return null;
        }
    }


    /**
     * 获取关联的未启动的所有指标预测任务名称
     *
     * @param taskId taskId
     * @return List<String>
     */
    public List<String> getUnStartIndicatorPredictTasks(Integer taskId) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getUnStartIndicatorPredictTasks(taskId);
        } catch (DataAccessException e) {
            LOGGER.error("getUnStartIndicatorPredictTasks error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取关联的正在运行的所有潮汐调度任务
     *
     * @param taskId taskId
     * @return List<String>
     */
    public List<String> getRunningResourceScheduleTasks(Integer taskId) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getRunningResourceScheduleTasks(taskId);
        } catch (DataAccessException e) {
            LOGGER.error("getRunningResourceScheduleTasks error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取指标预测结果
     *
     * @param taskId taskId
     * @param startTime startTime
     * @param endTime endTime
     * @param clusterName clusterName
     * @return List<PerformanceIndexValueSchedule>
     */
    public List<PerformanceIndexValueSchedule> getIndicatorPredictData(Integer taskId, long startTime, long endTime, String clusterName) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        DependentIndicators dependentIndicators = getDependentIndicators(taskId, clusterName);
        int indicatorPredictTaskId = dependentIndicators.getIndicatorPredictTaskId();
        String unitedId = dependentIndicators.getIndicatorId();
        try {
            String tableName = "TBL_AIOPS_TASK_UNITED_INDICATOR_PREDICT_RESULT_" + indicatorPredictTaskId;
            return resourceScheduleMapper.queryUnitedIndicatorPredictData(tableName, unitedId, startTime, endTime);
        } catch (DataAccessException e) {
            LOGGER.error("getIndicatorPredictData error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取依赖指标
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @return DependentIndicators
     */
    public DependentIndicators getDependentIndicators(Integer taskId, String clusterName) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getDependentIndicators(taskId, clusterName);
        } catch (DataAccessException e) {
            LOGGER.error("getDependentIndicators error : {}", e);
            return null;
        }
    }

    /**
     * 获取该任务的所有依赖指标
     *
     * @param taskId taskId
     * @return List<DependentIndicators>
     */
    public List<DependentIndicators> getAllDependentIndicators(Integer taskId) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getAllDependentIndicators(taskId);
        } catch (DataAccessException e) {
            LOGGER.error("getAllDependentIndicators error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取该任务的指标列表
     *
     * @param taskId taskId
     * @return List<TaskIndicator>
     */
    public List<TaskIndicator> getTaskIndicatorList(Integer taskId) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        List<TaskIndicator> result = new ArrayList<>();
        try {
            List<DependentIndicators> dependentIndicatorsList = getAllDependentIndicators(taskId);
            for (DependentIndicators dependentIndicators : dependentIndicatorsList) {
                List<TaskIndicator> tmpList = resourceScheduleMapper.getTaskUnitedIndicatorList(dependentIndicators.
                    getIndicatorPredictTaskId(), dependentIndicators.getIndicatorId());
                result.addAll(tmpList);
            }
            return result;
        } catch (DataAccessException e) {
            LOGGER.error("getTaskIndicatorsByTaskId error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取指标
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @return TaskIndicator
     */
    public TaskIndicator getOneTaskIndicator(Integer taskId, String clusterName) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            DependentIndicators dependentIndicators = getDependentIndicators(taskId, clusterName);
            List<TaskIndicator> taskIndicators = resourceScheduleMapper.getTaskUnitedIndicatorList(dependentIndicators.
                    getIndicatorPredictTaskId(), dependentIndicators.getIndicatorId());
            if (CollectionUtils.isEmpty(taskIndicators)) {
                return new TaskIndicator();
            }
            return taskIndicators.get(0);
        } catch (DataAccessException e) {
            LOGGER.error("getTaskIndicatorsByTaskId error : {}", e);
            return new TaskIndicator();
        }
    }

    /**
     * 获取服务画像id
     *
     * @param taskId taskId
     * @return ProductPortrait
     */
    public Integer getProductPortraitId(Integer taskId) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getProductPortraitId(taskId);
        } catch (DataAccessException e) {
            LOGGER.error("getProductPortraitId error : {}", e);
            return -1;
        }
    }

    /**
     * 获取服务画像
     *
     * @param taskId taskId
     * @return ProductPortrait
     */
    public ProductPortrait getProductPortrait(Integer taskId) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            Integer portraitId =  resourceScheduleMapper.getProductPortraitId(taskId);
            return resourceScheduleMapper.getProductPortrait(portraitId);
        } catch (DataAccessException e) {
            LOGGER.error("getProductPortrait error : {}", e);
            return null;
        }
    }

    /**
     * 获取业务画像
     *
     * @param businessName businessName
     * @return BusinessPortrait
     */
    public BusinessPortrait getBusinessPortrait(String businessName) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getBusinessPortrait(businessName);
        } catch (DataAccessException e) {
            LOGGER.error("getBusinessPortrait error : {}", e);
            return null;
        }
    }

    /**
     * 获取主机画像
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @return VmPortrait
     */
    public VmPortrait getVmPortrait(Integer taskId, String clusterName) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            Integer portraitId =  resourceScheduleMapper.getProductPortraitId(taskId);
            return resourceScheduleMapper.getVmPortrait(portraitId, clusterName);
        } catch (DataAccessException e) {
            LOGGER.error("getVmPortrait error : {}", e);
            return null;
        }
    }

    /**
     * 获取所有主机画像
     *
     * @param taskId taskId
     * @return List<VmPortrait>
     */
    public List<VmPortrait> getAllVmPortrait(Integer taskId) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            Integer portraitId =  resourceScheduleMapper.getProductPortraitId(taskId);
            return resourceScheduleMapper.getAllVmPortrait(portraitId);
        } catch (DataAccessException e) {
            LOGGER.error("getAllVmPortrait error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取所有调度策略
     *
     * @param taskId taskId
     * @return List<ScheduleStrategy>
     */
    public List<ScheduleStrategy> getScheduleStrategy(Integer taskId) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        List<ScheduleStrategy> result = new ArrayList<>();
        try {
            String strategyIds = resourceScheduleMapper.getStrategyIds(taskId);
            if (strategyIds == null) {
                return new ArrayList<>();
            }
            List<String> ids = JSON.parseArray(strategyIds, String.class);
            for (String id :ids) {
                ScheduleStrategy strategy = resourceScheduleMapper.getScheduleStrategy(id);
                if (strategy != null) {
                    result.add(strategy);
                }
            }
            return result;
        } catch (DataAccessException e) {
            LOGGER.error("getScheduleStrategy error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取所有策略配置
     *
     * @param strategyId strategyId
     * @return List<BusinessConfig>
     */
    public List<BusinessConfig> getScheduleStrategyConfig(Integer strategyId) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getScheduleStrategyConfig(strategyId);
        } catch (DataAccessException e) {
            LOGGER.error("getScheduleStrategyConfig error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 更新vm历史数量曲线
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param numberList numberList
     * @throws ServiceException Service Exception
     */
    public void updateVmNumberHistory(Integer taskId, String clusterName, List<PerformanceIndexValue> numberList) throws ServiceException {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            TaskIndicator taskIndicator = getOneTaskIndicator(taskId, clusterName);
            String tableName = "TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_HISTORY_DATA_" + taskId;
            List<List<PerformanceIndexValue>> lists = ListUtils.partition(numberList, 500);
            for (List<PerformanceIndexValue> list : lists) {
                resourceScheduleMapper.updateVmNumberHistory(tableName, taskId, taskIndicator.getUnitedId(), DATA_TYPE_NUM,
                    list, taskIndicator.getMeasUnitKey(), taskIndicator.getMeasTypeKey(),
                    taskIndicator.getMoType(), taskIndicator.getOriginalValue(), clusterName);
            }
        } catch (DataAccessException e) {
            LOGGER.error("updateVmNumberHistory error : {}", e);
        }
    }

    /**
     * 更新vm历史加和曲线
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param sumList sumList
     * @throws ServiceException ServiceException
     */
    public void updateVmSumHistory(Integer taskId, String clusterName, List<PerformanceIndexValue> sumList) throws ServiceException {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            TaskIndicator taskIndicator = getOneTaskIndicator(taskId, clusterName);
            String tableName = "TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_HISTORY_DATA_" + taskId;
            List<List<PerformanceIndexValue>> lists = ListUtils.partition(sumList, 500);
            for (List<PerformanceIndexValue> list : lists) {
                resourceScheduleMapper.updateVmSumHistory(tableName, taskId, taskIndicator.getUnitedId(), DATA_TYPE_SUM,
                    list, taskIndicator.getMeasUnitKey(), taskIndicator.getMeasTypeKey(), taskIndicator.getMoType(),
                    taskIndicator.getOriginalValue(), clusterName);
            }
        } catch (DataAccessException e) {
            LOGGER.error("updateVmSumHistory error : {}", e);
        }
    }

    /**
     * 更新vm历史加和曲线
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param avgList avgList
     * @throws ServiceException Service Exception
     */
    public void updateVmAvgHistory(Integer taskId, String clusterName, List<PerformanceIndexValue> avgList) throws ServiceException {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            TaskIndicator taskIndicator = getOneTaskIndicator(taskId, clusterName);
            String tableName = "TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_HISTORY_DATA_" + taskId;
            List<List<PerformanceIndexValue>> lists = ListUtils.partition(avgList, 500);
            for (List<PerformanceIndexValue> list : lists) {
                resourceScheduleMapper.updateVmAvgHistory(tableName, taskId, taskIndicator.getUnitedId(), DATA_TYPE_AVG,
                    list, taskIndicator.getMeasUnitKey(), taskIndicator.getMeasTypeKey(), taskIndicator.getMoType(),
                    taskIndicator.getOriginalValue(), clusterName);
            }
        } catch (DataAccessException e) {
            LOGGER.error("updateVmAvgHistory error : {}", e);
        }
    }

    /**
     * 更新Cpu节省率曲线
     *
     * @param taskId taskId
     * @param cpuSaveList cpuSaveList
     * @throws ServiceException Service Exception
     */
    public void updateCpuSaveRate(Integer taskId, List<PerformanceIndexValue> cpuSaveList) throws ServiceException {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            String tableName = "TBL_AIOPS_SCHEDULE_RESOURCE_SAVE_RATE_ASSESSMENT_" + taskId;
            Integer baseLineNumber = getProductPortrait(taskId).getBaseLineCpuNumber();
            Iterator<List<PerformanceIndexValue>> iterator = splitListIterator(cpuSaveList, 500);
            while (iterator.hasNext()) {
                resourceScheduleMapper.updateCpuSaveRate(tableName, taskId, 0, iterator.next(), baseLineNumber);
            }
        } catch (DataAccessException e) {
            LOGGER.error("updateCpuSaveRate error : {}", e);
        }
    }

    /**
     * 更新内存节省率曲线
     *
     * @param taskId taskId
     * @param memorySaveList memorySaveList
     * @throws ServiceException Service Exception
     */
    public void updateMemorySaveRate(Integer taskId, List<PerformanceIndexValue> memorySaveList) throws ServiceException {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            String tableName = "TBL_AIOPS_SCHEDULE_RESOURCE_SAVE_RATE_ASSESSMENT_" + taskId;
            Integer baseLineNumber = getProductPortrait(taskId).getBaseLineMemoryNumber();
            Iterator<List<PerformanceIndexValue>> iterator = splitListIterator(memorySaveList, 500);
            while (iterator.hasNext()) {
                resourceScheduleMapper.updateMemorySaveRate(tableName, taskId, 1, iterator.next(), baseLineNumber);
            }
        } catch (DataAccessException e) {
            LOGGER.error("updateMemorySaveRate error : {}", e);
        }
    }

    /**
     * 更新预测误差率曲线
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param accuracyList accuracyList
     */
    public void updatePredictAccuracy(Integer taskId, String clusterName, List<PerformanceIndexValue> accuracyList) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            TaskIndicator taskIndicator = getOneTaskIndicator(taskId, clusterName);
            String tableName = "TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_RESIDUAL_DATA_" + taskId;
            Iterator<List<PerformanceIndexValue>> iterator = splitListIterator(accuracyList, 500);
            while (iterator.hasNext()) {
                resourceScheduleMapper.updatePredictAccuracy(tableName, taskId, taskIndicator.getUnitedId(), 0,
                    iterator.next(), taskIndicator.getMeasUnitKey(), taskIndicator.getMeasTypeKey(),
                    taskIndicator.getMoType(), taskIndicator.getOriginalValue(), clusterName);
            }
        } catch (DataAccessException e) {
            LOGGER.error("updatePredictAccuracy error : {}", e);
        }
    }

    /**
     * 获取历史加和曲线
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param startTime startTime
     * @param endTime endTime
     * @return List<PerformanceIndexValue>
     */
    public List<PerformanceIndexValue> getVmSumHistory(Integer taskId, String clusterName, Long startTime, Long endTime) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            String tableName = "TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_HISTORY_DATA_" + taskId;
            return  resourceScheduleMapper.getVmSumHistory(tableName, taskId, clusterName, startTime, endTime);
        } catch (DataAccessException e) {
            LOGGER.error("getVmSumHistory error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取历史加和曲线
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param startTime startTime
     * @param endTime endTime
     * @return List<PerformanceIndexValue>
     */
    public List<PerformanceIndexValue> getVmNumberHistory(Integer taskId, String clusterName, Long startTime, Long endTime) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            String tableName = "TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_HISTORY_DATA_" + taskId;
            return  resourceScheduleMapper.getVmNumberHistory(tableName, taskId, clusterName, startTime, endTime);
        } catch (DataAccessException e) {
            LOGGER.error("getVmNumberHistory error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取预测平均曲线
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param startTime startTime
     * @param endTime endTime
     * @return List<PerformanceIndexValue>
     */
    public List<PerformanceIndexValue> getVmAvgPredict(Integer taskId, String clusterName, Long startTime, Long endTime) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            String tableName = "TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_" + taskId;
            return  resourceScheduleMapper.getVmAvgHistory(tableName, taskId, clusterName, startTime, endTime);
        } catch (DataAccessException e) {
            LOGGER.error("getVmAvgHistory error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 插入预测平均曲线
     *
     * @param taskId taskId
     * @param indicatorResult indicatorResult
     */
    public void insertVmAvgPredict(Integer taskId, List<ResourceScheduleIndicatorResult> indicatorResult) {
        if (CollectionUtils.isEmpty(indicatorResult)) {
            return;
        }
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            String tableName = "TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_" + taskId;
            Iterator<List<ResourceScheduleIndicatorResult>> iterator = splitListIterator(indicatorResult, 500);
            while (iterator.hasNext()) {
                resourceScheduleMapper.insertVmAvgPredict(tableName, taskId, DATA_TYPE_AVG, iterator.next());
            }
        } catch (DataAccessException e) {
            LOGGER.error("insertVmAvgPredict error : {}", e);
        }
    }

    /**
     * 插入预测数量曲线
     *
     * @param taskId taskId
     * @param numberResult numberResult
     */
    public void insertVmNumberPredict(Integer taskId, List<ResourceScheduleNumberResult> numberResult) {
        if (CollectionUtils.isEmpty(numberResult)) {
            return;
        }
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            String tableName = "TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_" + taskId;
            Iterator<List<ResourceScheduleNumberResult>> iterator = splitListIterator(numberResult, 500);
            while (iterator.hasNext()) {
                resourceScheduleMapper.insertVmNumberPredict(tableName, taskId, DATA_TYPE_NUM, iterator.next());
            }
        } catch (DataAccessException e) {
            LOGGER.error("insertVmNumberPredict error : {}", e);
        }
    }

    /**
     * 获取集群数量
     *
     * @param taskId taskId
     * @return  Integer Integer
     */
    public Integer getClusterCountByTaskId(Integer taskId) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getClusterCountByTaskId(taskId);
        } catch (DataAccessException e) {
            LOGGER.error("getClusterCountByTaskId error : {}", e);
            return 0;
        }
    }

    /**
     * 获取需要df调度的任务
     *
     * @return  List<Integer>
     */
    public List<Integer> getAllNeedDfTaskId() {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getAllNeedDfTaskId();
        } catch (DataAccessException e) {
            LOGGER.error("getAllNeedDfTaskId error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取需要调度的数量
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param timestamp timestamp
     * @return  ScheduleReplicas
     */
    public ScheduleReplicas getScheduleReplicas(Integer taskId, String clusterName, Long timestamp) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        String tableName = "TBL_AIOPS_SCHEDULE_GROUP_INDICATOR_PREDICT_DATA_" + taskId;
        try {
            return resourceScheduleMapper.getScheduleReplicas(tableName, taskId, clusterName, timestamp);
        } catch (DataAccessException e) {
            LOGGER.error("getScheduleReplicas error : {}", e);
            return null;
        }
    }

    /**
     * 插入df扩缩执行记录
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param startTime startTime
     * @param scaleExecuteDetail scaleExecuteDetail
     */
    public void insertScaleExecuteDetail(Integer taskId, String clusterName, Long startTime, String scaleExecuteDetail) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            resourceScheduleMapper.insertScaleExecuteDetail(taskId, clusterName, startTime, scaleExecuteDetail);
        } catch (DataAccessException e) {
            LOGGER.error("insertScaleExecuteDetail error : {}", e);
        }
    }

    /**
     * 更新df扩缩执行记录失败
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param endTime endTime
     */
    public void updateScaleExecuteDetailFailed(Integer taskId, String clusterName, Long endTime) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            resourceScheduleMapper.updateScaleExecuteDetailFailed(taskId, clusterName, endTime);
        } catch (DataAccessException e) {
            LOGGER.error("updateScaleExecuteDetailFailed error : {}", e);
        }
    }

    /**
     * 更新df扩缩执行记录成功
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param endTime endTime
     */
    public void updateScaleExecuteDetailSuccess(Integer taskId, String clusterName, Long endTime) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            resourceScheduleMapper.updateScaleExecuteDetailSuccess(taskId, clusterName, endTime);
        } catch (DataAccessException e) {
            LOGGER.error("updateScaleExecuteDetailSuccess error : {}", e);
        }
    }

    /**
     * 获取df扩缩执行详情
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @return  String
     */
    public String getExecuteDetails(Integer taskId, String clusterName) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getExecuteDetails(taskId, clusterName);
        } catch (DataAccessException e) {
            LOGGER.error("getExecuteDetails error : {}", e);
            return null;
        }
    }

    /**
     * 获取host的部署服务
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @return string
     */
    public String getDeployServiceByTask(Integer taskId, String clusterName) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getDeployServiceByTask(taskId, clusterName);
        } catch (DataAccessException e) {
            LOGGER.error("getDeployServiceByTask error : {}", e);
            return "";
        }
    }

    /**
     * 获取正在执行的df扩缩执行详情
     *
     * @return  List<ResourceScheduleExecuteDetail>
     */
    public List<ResourceScheduleExecuteDetail> getAllRunningExecuteDetails() {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.getAllRunningExecuteDetails();
        } catch (DataAccessException e) {
            LOGGER.error("getAllRunningExecuteDetails error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 更新df扩缩执行详情
     *
     * @param taskId taskId
     * @param clusterName clusterName
     * @param scaleExecuteDetail scaleExecuteDetail
     */
    public void updateExecuteDetails(Integer taskId, String clusterName, String scaleExecuteDetail) {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            resourceScheduleMapper.updateExecuteDetails(taskId, clusterName, scaleExecuteDetail);
        } catch (DataAccessException e) {
            LOGGER.error("getExecuteDetails error : {}", e);
        }
    }

    /**
     * 清理数据库历史数据
     *
     * @param dataOversizeTimeInMillis dataOversizeTimeInMillis
     * @throws ServiceException Service Exception
     */
    public void deleteHistoryData(long dataOversizeTimeInMillis) throws ServiceException {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            resourceScheduleMapper.deleteHistoryData(dataOversizeTimeInMillis);
            resourceScheduleMapper.deletePredictData(dataOversizeTimeInMillis);
            resourceScheduleMapper.deleteSaveRateData(dataOversizeTimeInMillis);
            resourceScheduleMapper.deleteResidualData(dataOversizeTimeInMillis);
            resourceScheduleMapper.deleteExecuteData(dataOversizeTimeInMillis);
        } catch (DataAccessException e) {
            LOGGER.error("[deleteHistoryData] error", e);
            throw new ServiceException("deleteHistoryData failed!");
        }
    }

    /**
     * 服务器重启时继续df执行
     *
     * @return List<ResourceScheduleRecord>
     */
    public List<ResourceScheduleRecord> queryRunningScale() {
        ResourceScheduleMapper resourceScheduleMapper = mapperFactory.getMapper(ResourceScheduleMapper.class);
        try {
            return resourceScheduleMapper.queryRunningScale();
        } catch (DataAccessException e) {
            LOGGER.error("updateAllScaleExecuteFailed error : {}", e);
            return new ArrayList<>();
        }
    }

    /**
     * 把一个List集合按指定长度分割成组
     *
     * @param list        输入的List
     * @param groupSize   指定长度
     * @return 返回分隔后的迭代器
     */
    public <T> Iterator<List<T>> splitListIterator(List<T> list, int groupSize) {
        int length = list.size();
        if (length == 0) {
            return Collections.emptyIterator();
        }
        // 计算可以分成多少组
        int num = (length + groupSize - 1) / groupSize;
        List<List<T>> newList = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            // 开始位置
            int fromIndex = i * groupSize;
            // 结束位置
            int toIndex = Math.min((i + 1) * groupSize, length);
            newList.add(list.subList(fromIndex, toIndex));
        }
        return newList.iterator();
    }
}
