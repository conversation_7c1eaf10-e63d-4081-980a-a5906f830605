/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.util;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.AssociationAnalysisType;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.TaskTypeEnum;
import com.huawei.i2000.dvanalysisengineservice.business.configdata.ConfigDataDao;
import com.huawei.i2000.dvanalysisengineservice.business.configdata.ConfigDataHandler;
import com.huawei.i2000.dvanalysisengineservice.business.datasource.elasticsearch.EsUtils;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.conceptdrift.ConceptDriftManage;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.conceptdrift.DriftTaskMessageCacheOper;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.constant.SynAnalysisResultConstant;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.AlarmRecord;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.taskresulthandler.AlarmRecordsHandler;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.AnalysisTaskEntity;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.AlarmClient;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.AlarmQueryParam;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.QFilterElement;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.QueryContext;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.dto.ScrollQueryResult;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.datahandle.indicatorpredict.IndicatorPredictTaskUpgrade;
import com.huawei.i2000.dvanalysisengineservice.business.templatemanage.TriggerTemplateConverter;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TemplateTreeModel;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.TemplateTreeNode;
import com.huawei.i2000.dvanalysisengineservice.dao.common.MapperFactory;
import com.huawei.i2000.dvanalysisengineservice.dao.disasterrecovery.DisasterRecoveryTaskExecutionDao;
import com.huawei.i2000.dvanalysisengineservice.dao.disasterrecovery.DisaterRecoveryTaskDao;
import com.huawei.i2000.dvanalysisengineservice.dao.multiple.mapper.CapacityAnalysisDao;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.AlarmIncidentDao;
import com.huawei.i2000.dvanalysisengineservice.dao.synanalysisresult.TaskAnalysisLogResultDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskIndicatorDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.handlerimpl.AbstractTaskManage;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.handlerimpl.TaskManageDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.handlerimpl.TaskManageDaoFactory;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.handlerimpl.UpgradeAlgorithmName;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.mapper.TriggerTaskManageMapper;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTask;
import com.huawei.i2000.dvanalysisengineservice.model.ConfigData;
import com.huawei.i2000.dvanalysisengineservice.model.GroupMember;
import com.huawei.i2000.dvanalysisengineservice.model.LogDetectAbnormalData;
import com.huawei.i2000.dvanalysisengineservice.model.TaskIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerExecutionNode;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerTaskMessage;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 服务启动时执行该方法
 *
 * <AUTHOR>
 * @since 2024/1/18
 */
@Component
public class ApplicationListenerTask implements ApplicationListener<ContextRefreshedEvent> {
    private static final OssLog LOGGER = OssLogFactory.getLogger(ApplicationListenerTask.class);

    private static final List<String> ALARM_RETURN_FIELDS = Collections.unmodifiableList(
        Arrays.asList("moi", "category", "alarmId", "severity", "mergeKey", "matchKey", "alarmName", "csn", "cleared",
            "additionalInformation", "nativeMeDn", "nativeMoDn", "originSystem"));

    private static final Pattern MOI_PATTERN = Pattern.compile("IncidentId=(\\S+)\\s+TaskName=(.+)");

    @Autowired
    IndicatorPredictTaskUpgrade indicatorPredictTaskUpgrade;

    @Autowired
    UpgradeAlgorithmName upgradeAlgorithmName;

    @Autowired
    ConceptDriftManage conceptDriftManage;

    @Autowired
    TaskIndicatorDao taskIndicatorDao;

    @Autowired
    TaskManageDao taskManageDao;

    @Autowired
    AlarmIncidentDao alarmIncidentDao;

    @Autowired
    CapacityAnalysisDao capacityAnalysisDao;

    @Autowired
    TaskAnalysisLogResultDao taskAnalysisLogResultDao;

    @Autowired
    DisaterRecoveryTaskDao disaterRecoveryTaskDao;

    @Autowired
    DisasterRecoveryTaskExecutionDao disasterRecoveryTaskExecutionDao;

    @Autowired
    ConfigDataHandler configDataHandler;

    @Autowired
    MapperFactory mapperFactory;

    @Autowired
    ConfigDataDao configDataDao;

    @Autowired
    TriggerTemplateConverter triggerTemplateConverter;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(8, 8, 10, TimeUnit.MINUTES,
        new LinkedBlockingQueue<>(100), new CustomizableThreadFactory("Application-Listener-Task-Thread-"));

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        String contextId = event.getApplicationContext().getId();
        // 测试用例不需要执行该方法
        if (event.getApplicationContext().getParent() == null && StringUtils.isNotEmpty(contextId) && !contextId.contains("baize")) {
            // 旧的指标趋势预测任务升级
            executor.execute(() -> {
                try {
                    indicatorPredictTaskUpgrade.upgrade();
                } catch (Throwable e) {
                    LOGGER.error("upgrade indicator predict task error, e:", e);
                }
            });

            // 更新算法模型信息
            executor.execute(() -> {
                try {
                    upgradeAlgorithmName.upgrade();
                } catch (Throwable e) {
                    LOGGER.error("update algorithm model info error, e:", e);
                }
            });

            // 升级概念漂移任务，创建漂移分数表
            executor.execute(() -> {
                try {
                    conceptDriftManage.upgradeTask();
                } catch (Throwable e) {
                    LOGGER.error("upgrade concept drift task error, e:", e);
                }
            });

            executor.execute(this::clearingAlarms);

            EsUtils.loopClearClient();

            CpuUtils.killStart();

            executor.execute(() -> {
                try {
                    DriftTaskMessageCacheOper.getInstance().removeAll();
                } catch (Throwable e) {
                    LOGGER.error("DriftTaskMessageCacheOper removeAll error, e:", e);
                }
            });
            updateConfigurations();
            indicatorTaskUpgrade();
            executor.shutdown();
        }
    }

    private void indicatorTaskUpgrade() {
        // 升级把指标异常检测和关联分析任务按网元类型的导入任务自动更新都设置成true
        executor.execute(() -> {
            try {
                refreshUpdateIndicatorAuto();
                refreshIndicatorTaskType();
            } catch (Throwable e) {
                LOGGER.error("refreshUpdateIndicatorAuto error, e:", e);
            }
        });
        // 把旧的关联分析任务转成新的关联分析模板
        executor.execute(() -> {
            try {
                migratingOldCorrelationAnalysisTask();
            } catch (Throwable e) {
                LOGGER.error("migratingOldCorrelationAnalysisTask error, e:", e);
            }
        });
    }

    private void updateConfigurations() {
        // 根据资源池总配置动态调整故障关联分析指标数上限
        executor.execute(() -> {
            try {
                configDataHandler.refreshAssiciationIndicatorNum(); // 初始化配置，并启动nodelist.json文件监听
                configDataHandler.loadFromConfigFile();
                List<ConfigData> list = configDataDao.selectAll();
                // 从数据库更新到redis
                for (ConfigData configData : list) {
                    configDataHandler.refreshRedis(configData);
                }
            } catch (Throwable e) {
                LOGGER.error("init configdata error, e:", e);
            }
        });
    }

    public void refreshUpdateIndicatorAuto() {
        LOGGER.info("refreshUpdateIndicatorAuto start");
        List<AnalysisTask> tasks = taskManageDao.getImportOneAndFourTask();
        if (CollectionUtils.isEmpty(tasks)) {
            LOGGER.info("refreshUpdateIndicatorAuto task is empty");
            return;
        }
        List<Integer> indicatorTaskId = new ArrayList<>();
        TriggerTaskManageMapper triggerTaskManageMapper = mapperFactory.getMapper(TriggerTaskManageMapper.class);
        for (AnalysisTask task : tasks) {
            if (task.getTaskType().equals(TaskTypeEnum.INDICATORTASK.enumToInt())) {
                if (task.getUpdateIndicatorAuto() == null) {
                    task.setUpdateIndicatorAuto(true);
                    indicatorTaskId.add(task.getTaskId());
                }
            }
            if (task.getTaskType().equals(TaskTypeEnum.TRIGGERTASK.enumToInt())) {
                TriggerTaskMessage message = triggerTaskManageMapper.queryCorrelationTemplate(task.getTaskId());
                String relationTree = message.getRelationTree();
                TemplateTreeModel templateTreeModel = JSONObject.parseObject(relationTree, TemplateTreeModel.class);
                List<TemplateTreeNode> nodes = templateTreeModel.getNodes();
                boolean update = false;
                for (TemplateTreeNode node : nodes) {
                    TriggerExecutionNode nodeParam = node.getNodeParam();
                    if (nodeParam == null) {
                        continue;
                    }
                    if (AssociationAnalysisType.DV_PM.equalsIgnoreCase(nodeParam.getIndicatorDataType())) {
                        if (nodeParam.getUpdateIndicatorAuto() == null) {
                            nodeParam.setUpdateIndicatorAuto(true);
                            update = true;
                        }
                    }
                }
                if (update) {
                    LOGGER.info("refreshUpdateIndicatorAuto TRIGGER_TASK id is={}", task.getTaskId());
                    message.setRelationTree(JSON.toJSONString(templateTreeModel));
                    triggerTaskManageMapper.updateCorrelationTemplate(message);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(indicatorTaskId)) {
            LOGGER.info("refreshUpdateIndicatorAuto INDICATOR_TASK id is={}", indicatorTaskId);
            taskManageDao.batchUpdateIndicatorAuto(true, indicatorTaskId);
        }
    }

    public void clearingAlarms() {
        try {
            LOGGER.info("clearingAlarms start");
            // 进程启动的时候搞个异步任务去性能那边查询智能运维运维的id
            // 然后check这个id对应的任务资源是否存在&任务是否在运行，如果不存在or已停止 就把这些告警给关闭掉
            indicatorAnomalyDetectionHandle();
            alarmTaskHandle();
            capacityAlarmHandle();
            drTaskAlarmHandle();
        } catch (Exception e) {
            LOGGER.error("clearingAlarms error, e=", e);
        }
    }

    private void alarmTaskHandle() {
        List<Map<String, String>> alarmData = getAlarmInfos("2", "505420001");
        if (CollectionUtils.isEmpty(alarmData)) {
            return;
        }
        List<AlarmRecord> alarmRecords = new ArrayList<>();
        Map<String, Integer> taskRelation = new HashMap<>();
        for (Map<String, String> alarmInfo : alarmData) {
            // 先根据定位信息里的找到事件id和任务名称
            String moi = alarmInfo.get("moi");
            if (StringUtils.isEmpty(moi)) {
                continue;
            }
            Map<String, String> maps = splitMoi(moi);
            if (MapUtils.isEmpty(maps)) {
                continue;
            }
            String taskName = maps.get("taskName");
            Integer taskId = null;
            if (taskRelation.containsKey(taskName)) {
                taskId = taskRelation.get(taskName);
            } else {
                try {
                    ContextUtils.setContext(new ContextUtils());
                    taskId = taskManageDao.queryTaskIdByName(taskName);
                    taskRelation.put(taskName, taskId);
                } catch (ServiceException e) {
                    LOGGER.error("queryTaskIdByName error, taskName={},e={}", taskName, e.getMessage());
                }
            }
            if (taskId == null) {
                // 清除
                addAlarmRecord(alarmInfo, alarmRecords);
            } else {
                // 查询对应事件表的事件存不存在
                Boolean contain = alarmIncidentDao.queryIncidentIdExist(taskId, maps.get("incidentId"));
                if (contain == null || !contain) {
                    // 不存在清除 存在就不清除
                    addAlarmRecord(alarmInfo, alarmRecords);
                }
            }
        }
        clearAlarms(alarmRecords);
    }

    private Map<String, String> splitMoi(String moi) {
        Map<String, String> result = new HashMap<>();
        try {
            // moi的值"IncidentId=xxx TaskName=xxx"
            Matcher matcher = MOI_PATTERN.matcher(moi);
            if (matcher.find()) {
                // 提取IncidentId和TaskName
                // 第一个括号捕获的组
                String incidentId = matcher.group(1);
                // 第二个括号捕获的组
                String taskName = matcher.group(2);
                result.put("incidentId", incidentId);
                result.put("taskName", taskName);
            } else {
                LOGGER.error("splitMoi error, moi={}", moi);
            }
        } catch (Exception e) {
            LOGGER.error("splitMoi error, moi={},e={}", moi, e.getMessage());
        }
        return result;
    }

    private void indicatorAnomalyDetectionHandle() {
        // 指标与日志异常检测都是以50541开始，所以查出来的是日志与指标的告警
        List<Map<String, String>> alarmData = getAlarmInfos("1", "50541");
        alarmData = filterLogAlarm(alarmData);
        if (CollectionUtils.isEmpty(alarmData)) {
            return;
        }
        List<AlarmRecord> alarmRecords = new ArrayList<>();
        List<String> reportAlarmIds = taskIndicatorDao.getIndicatorTaskReportAlarmId();
        for (Map<String, String> alarmInfo : alarmData) {
            // 先看任务的REPORT_ALARM_ID存不存在
            if (reportAlarmIds.contains(alarmInfo.get("alarmId"))) {
                Boolean contain = taskIndicatorDao.switchAlarmFieldExist(alarmInfo.get("matchKey"));
                if (contain == null || !contain) {
                    // 存在就 看告警的matchKey里面包不包含 TBL_AIOPS_INDICATOR_OUTLIER 表的SWITCH_ALARM_FIELD内容
                    // 不包含清除 包含就不清除
                    addAlarmRecord(alarmInfo, alarmRecords);
                }
            } else {
                // 不存在就清除
                addAlarmRecord(alarmInfo, alarmRecords);
            }
        }
        clearAlarms(alarmRecords);
    }

    private void addAlarmRecord(Map<String, String> alarmInfo, List<AlarmRecord> alarmRecords) {
        AlarmRecord alarmRecord = new AlarmRecord();
        alarmRecord.setAlarmId(alarmInfo.get("alarmId"));
        alarmRecord.setMoi(alarmInfo.get("moi"));
        alarmRecord.setCategory(SynAnalysisResultConstant.AlarmParameters.CATEGORY_CLEAR);
        alarmRecord.setSeverity(Integer.parseInt(alarmInfo.get("severity")));
        alarmRecord.setMergeKey(alarmInfo.get("mergeKey"));
        alarmRecord.setMatchKey(alarmInfo.get("matchKey"));
        alarmRecord.setAlarmName(alarmInfo.get("alarmName"));
        alarmRecord.setCsn(Integer.parseInt(alarmInfo.get("csn")));
        alarmRecord.setAdditionalInformation(alarmInfo.get("additionalInformation"));
        alarmRecord.setCleared(SynAnalysisResultConstant.AlarmParameters.ALARM_CLEARED);
        alarmRecord.setOccurUtc(0);
        alarmRecord.setClearUtc(System.currentTimeMillis());
        alarmRecord.setNativeMeDn(alarmInfo.get("nativeMeDn"));
        alarmRecord.setNativeMoDn(alarmInfo.get("nativeMoDn"));
        alarmRecord.setOriginSystem(alarmInfo.get("originSystem"));
        alarmRecords.add(alarmRecord);
    }

    private void clearAlarms(List<AlarmRecord> alarmRecords) {
        if (CollectionUtils.isEmpty(alarmRecords)) {
            return;
        }
        LOGGER.info("clear alarms records = {}", alarmRecords);
        try {
            AlarmRecordsHandler.pushAlarm(alarmRecords);
        } catch (ServiceException e) {
            LOGGER.error("push Alarm failed!,e=", e);
        }
    }

    private List<Map<String, String>> queryAlarm(AlarmQueryParam param) {
        List<Map<String, String>> alarmData = new ArrayList<>();
        param.setSize(1000);
        param.setFields(ALARM_RETURN_FIELDS);
        param.setIterator(null);
        ScrollQueryResult queryResult = AlarmClient.getInstance().getAlarmInfos(param, "current");
        if (queryResult == null) {
            LOGGER.error("start task queryAlarm queryResult is null");
            return alarmData;
        }
        while (StringUtils.isNotEmpty(queryResult.getIterator()) || CollectionUtils.isNotEmpty(queryResult.getHits())) {
            alarmData.addAll(queryResult.getHits());
            param.setIterator(queryResult.getIterator());
            LOGGER.debug("[queryAlarm] iterator={}, size={}", queryResult.getIterator(), queryResult.getHits().size());
            queryResult = AlarmClient.getInstance().getAlarmInfos(param, "current");
            if (queryResult == null) {
                LOGGER.error("[queryAlarm] getAlarmData queryResult is null");
                break;
            }
        }
        return alarmData;
    }

    private List<Map<String, String>> getAlarmInfos(String taskType, String value) {
        AlarmQueryParam param = new AlarmQueryParam();
        QueryContext condition = new QueryContext();
        condition.setExpression("and");
        List<QFilterElement> filterList = new ArrayList<>();
        condition.setFilters(filterList);
        param.setQuery(condition);

        switch (taskType) {
            case "1":
                QFilterElement filter1 = new QFilterElement();
                filter1.setName("alarmId");
                filter1.setField("alarmId");
                filter1.setOperator("startwith");
                filter1.setValues(Collections.singletonList(value));
                filterList.add(filter1);
                break;
            case "2":
            case "5":
                QFilterElement filter2 = new QFilterElement();
                filter2.setName("alarmId");
                filter2.setField("alarmId");
                filter2.setOperator("=");
                filter2.setValues(Collections.singletonList(value));
                filterList.add(filter2);
                break;
            default:
                LOGGER.error("query task type is not supported, taskType={}", taskType);
                return Collections.emptyList();
        }

        QFilterElement filterStatus = new QFilterElement();
        filterStatus.setName("cleared");
        filterStatus.setField("cleared");
        filterStatus.setOperator("=");
        filterStatus.setValues(Collections.singletonList("0"));
        filterList.add(filterStatus);
        return queryAlarm(param);
    }

    /**
     * DTS2024082328122
     * 【OLD】【故障检测分析】套件升级后，指标异常检测上报告警，告警并未结束时回退，该告警无法自动删除。
     * 其他智能运维套件升级后产生的新告警未删除就回退，可能产生该问题问题，请排查。
     */
    public void capacityAlarmHandle() {
        List<Map<String, String>> alarms = getAlarmInfos(String.valueOf(TaskConstant.TASK_TYPE_CAPACITY_EXCEPTION),
            "505420003");
        if (CollectionUtils.isEmpty(alarms)) {
            return;
        }

        List<String> alarmStrList = capacityAnalysisDao.getCapacityReportedAlarmList(null, null);
        ArrayList<AlarmRecord> alarmRecords = new ArrayList<>();
        alarmStrList.forEach(s -> alarmRecords.addAll(JSON.parseArray(s, AlarmRecord.class)));
        Set<String> existMatchKeys = alarmRecords.stream().map(AlarmRecord::getMatchKey).collect(Collectors.toSet());

        List<Map<String, String>> alarmToDelete = alarms.stream()
            .filter(map -> !existMatchKeys.contains(map.get("matchKey")))
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(alarmToDelete)) {
            alarmRecords.clear();
            alarmToDelete.forEach(map -> addAlarmRecord(map, alarmRecords));
            clearAlarms(alarmRecords);
        }
    }

    public List<Map<String, String>> filterLogAlarm(List<Map<String, String>> alarms) {
        if (CollectionUtils.isEmpty(alarms)) {
            return alarms;
        }
        List<Integer> taskIds = Collections.emptyList();
        try {
            taskIds = taskManageDao.getTaskByType(TaskConstant.TASK_TYPE_LOG_EXCEPTION);
        } catch (ServiceException e) {
            LOGGER.error("get task by Type error, taskType={}, e:", TaskConstant.TASK_TYPE_LOG_EXCEPTION, e);
        }
        Set<String> existMatchKeys = new HashSet<>();
        taskIds.forEach(taskId -> {
            List<LogDetectAbnormalData> abnormalData = taskAnalysisLogResultDao.queryNotEndLogAbnormalByTaskId(taskId);
            existMatchKeys.addAll(
                abnormalData.stream().map(LogDetectAbnormalData::getSwitchAlarmField).collect(Collectors.toSet()));
        });
        return alarms.stream()
            .filter(map -> !existMatchKeys.contains(map.get("matchKey")))
            .collect(Collectors.toList());
    }

    private void drTaskAlarmHandle() {
        List<Map<String, String>> alarmData = getAlarmInfos("2", "505420002");
        if (CollectionUtils.isEmpty(alarmData)) {
            return;
        }
        List<AlarmRecord> alarmRecords = new ArrayList<>();
        Map<String, String> taskRelation = new HashMap<>();
        for (Map<String, String> alarmInfo : alarmData) {
            // 先根据定位信息里的找到任务名称和网元名称
            String moi = alarmInfo.get("moi");
            if (StringUtils.isEmpty(moi)) {
                continue;
            }
            Map<String, String> maps = splitDrMoi(moi);
            String taskName = maps.get("taskName");
            String taskId;
            if (taskRelation.containsKey(taskName)) {
                taskId = taskRelation.get(taskName);
            } else {
                taskId = disaterRecoveryTaskDao.getTaskId(taskName);
                taskRelation.put(taskName, taskId);
            }
            if (taskId == null) {
                // 清除
                addAlarmRecord(alarmInfo, alarmRecords);
            } else {
                // 查询是否存在区域成员
                Set<String> dnNames = disasterRecoveryTaskExecutionDao.getGroupMemberByTaskId(taskId)
                    .stream()
                    .map(GroupMember::getDnName)
                    .collect(Collectors.toSet());
                if (!dnNames.contains(maps.get("dnName"))) {
                    // 不存在清除 存在就不清除
                    addAlarmRecord(alarmInfo, alarmRecords);
                }
            }
        }
        clearAlarms(alarmRecords);
    }

    private Map<String, String> splitDrMoi(String moi) {
        Map<String, String> result = new HashMap<>();
        try {
            // moi的值"TaskName=xxx,NE Name=xxx"
            String[] parts = moi.split(", ");
            String taskPart = parts[0];
            String dnPart = parts[1];
            String taskName = taskPart.split("=")[1];
            String dnName = dnPart.split("=")[1];
            result.put("taskName", taskName);
            result.put("dnName", dnName);
        } catch (Exception e) {
            LOGGER.error("splitMoi error, moi={},e={}", moi, e.getMessage());
        }
        return result;
    }

    public void refreshIndicatorTaskType() {
        List<AnalysisTaskEntity> taskList = null;
        try {
            taskList = taskManageDao.getTaskListByType(TaskTypeEnum.INDICATORTASK.enumToInt());
        } catch (ServiceException e) {
            LOGGER.error("refreshIndicatorTaskType getTaskListByType error,e=", e);
            return;
        }
        if (CollectionUtils.isEmpty(taskList)) {
            return;
        }
        for (AnalysisTask task : taskList) {
            if (task.getIndicatorTaskType() != null) {
                continue;
            }
            int indicatorTaskType;
            if (task.getAlgorithmModelName().equalsIgnoreCase(TaskConstant.ANOMALYDETECTION_AGRI_NAME)) {
                indicatorTaskType = TaskTypeEnum.INDICATORTASK.enumToInt();
            } else {
                indicatorTaskType = TaskTypeEnum.HOMOGENEOUSCOMPARISONTASK.enumToInt();
            }
            task.setIndicatorTaskType(indicatorTaskType);
            taskManageDao.updateIndicatorTaskType(task.getTaskId(), indicatorTaskType);
            List<TaskIndicator> indicators = taskIndicatorDao.getTaskIndicatorList(task.getTaskId(), null);
            if (CollectionUtils.isEmpty(indicators)) {
                continue;
            }
            indicators.forEach(indicator -> indicator.setIndicatorTaskType(indicatorTaskType));
            taskIndicatorDao.updateIndicatorTaskType(indicators);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void migratingOldCorrelationAnalysisTask() {
        List<AnalysisTaskEntity> taskList = new ArrayList<>();
        try {
            taskList = taskManageDao.getTaskListByType(TaskTypeEnum.TRIGGERTASK.enumToInt());
        } catch (ServiceException e) {
            LOGGER.error("[migratingOldCorrelationAnalysisTask] getAnalysisTaskList error,e=", e);
        }
        if (CollectionUtils.isEmpty(taskList)) {
            LOGGER.warn("[migratingOldCorrelationAnalysisTask] getAnalysisTaskList taskList is empty");
            return;
        }
        List<AnalysisTaskEntity> handleTask = taskList.stream().filter(task -> task.getTemplateTaskId() == null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(handleTask)) {
            LOGGER.warn("[migratingOldCorrelationAnalysisTask] handleTask is empty");
            return;
        }
        for (AnalysisTaskEntity task : handleTask) {
            try {
                ContextUtils.setContext(new ContextUtils());
                ContextUtils.getContext().setAdmin(true);
                ContextUtils.getContext().setUserId(task.getUserId());
                AbstractTaskManage abstractTaskManage = TaskManageDaoFactory.getTaskManageHandler(task);
                abstractTaskManage.updateTaskMessage(task);
                triggerTemplateConverter.convertAnalysisTasks2New(task);
            } catch (ServiceException e) {
                LOGGER.warn("[migratingOldCorrelationAnalysisTask] convertAnalysisTasks2New error, taskName={},e={}",
                    task.getTaskName(), e.getMessage());
            }
        }
    }
}
