/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.historicaldatahandler;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvanalysisengineservice.business.common.enums.TaskTypeEnum;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.AnalysisTaskEntity;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.TrainPredictAddress;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.TrainingMessageCacheOper;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.dto.TrainningTaskMessage;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTaskListQueryParam;

import org.springframework.stereotype.Component;

import java.util.List;

/**
 * IndicatorHisTaskHandler 重启服务时处理指标任务状态
 * 重启服务时处理指标任务状态
 *
 * <AUTHOR>
 * @since 2022-12-11
 */
@Component
public class IndicatorHisTaskHandler extends AnalysisTaskHisHandler {
    private static final OssLog LOGGER = OssLogFactory.getLogger(
        com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.historicaldatahandler.IndicatorHisTaskHandler.class);

    /**
     * stopIndicatorHisTask
     * 重启服务时 置预测中和训练中的任务为未预测 和失败
     *
     * @throws ServiceException ServiceException
     */
    public void stopIndicatorHisTask() throws ServiceException {
        List<AnalysisTaskEntity> analysisTaskListTotal = taskManageDao.getAnalysisTaskListWithoutDetail(new AnalysisTaskListQueryParam(), null);
        for (AnalysisTaskEntity analysisTask : analysisTaskListTotal) {
            if (analysisTask.getTaskType().equals(TaskTypeEnum.INDICATORTASK.enumToInt())) {
                checkAndStopTask(analysisTask);
            }
        }
        LOGGER.debug("[IndicatorHisTaskHandler] indicator history task clean up finish.");
    }

    private void checkAndStopTask(AnalysisTaskEntity analysisTask) throws ServiceException {
        TrainPredictAddress trainPredictAddress = checkAddress(analysisTask.getTaskId());
        if (analysisTask.getTrainStatus() != null && analysisTask.getTrainStatus().equals(TaskConstant.TRAIN_STATUS_RUNNING_TRANIN)
            && trainPredictAddress.getTrainAddressBoolean()) {
            taskManageDao.updateTrainStatus(analysisTask.getTaskId(), TaskConstant.TRAIN_STATUS_FAIL);
            TrainningTaskMessage message = TrainingMessageCacheOper.getInstance().getOne(analysisTask.getTaskId());
            if (message != null) {
                TrainingMessageCacheOper.getInstance().remove(message.getTaskId());
            }
            LOGGER.info("[CapacityHisTaskHandler] reset training status success with id {}", analysisTask.getTaskId());
        }
    }
}
