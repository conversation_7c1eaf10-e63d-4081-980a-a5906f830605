/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.dao.trigger.mapper;

import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.AssociationFlowNodeInfo;
import com.huawei.i2000.dvanalysisengineservice.business.trigger.dto.FilterMoEntity;
import com.huawei.i2000.dvanalysisengineservice.model.AssocationAnalysisTaskQuery;
import com.huawei.i2000.dvanalysisengineservice.model.AssociationCurveQuery;
import com.huawei.i2000.dvanalysisengineservice.model.CustomIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.CustomIndicatorQuery;
import com.huawei.i2000.dvanalysisengineservice.model.LogTemplate;
import com.huawei.i2000.dvanalysisengineservice.model.NodeIndicatorQuery;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerAlarmList;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerExecution;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerExecutionNode;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerRecommendation;

import javafx.util.Pair;

import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 关联分析操作
 *
 * <AUTHOR>
 * @since 2022/3/23
 */
public interface AssociationAnalysisMapper {
    /**
     * 手动创建关联分析node,插入ExecutionResult表
     *
     * @param node node
     */
    void insertExecutionResult(TriggerExecution node);

    /**
     * 手动增加关联分析node,更新ExecutionResult表
     *
     * @param id id
     * @param treeResult treeResult
     * @param viewResult viewResult
     * @param updateTime updateTime
     * @param status status
     * @param failureCause failureCause
     * @param timeRequired timeRequired
     * @param executionAddress executionAddress
     * @param continueTemplateId 继续分析id
     */
    void updateExecutionResult(@Param("id") String id, @Param("treeResult") String treeResult,
        @Param("viewResult") String viewResult, @Param("updateTime") Long updateTime, @Param("status") String status,
        @Param("failureCause") String failureCause, @Param("timeRequired") String timeRequired,
        @Param("executionAddress") String executionAddress, @Param("continueTemplateId") Integer continueTemplateId);

    /**
     * 更新ExecutionResult表状态
     *
     * @param id id
     * @param status status
     */
    void updateExecutionResultStatus(@Param("id") String id, @Param("status") String status);

    /**
     * 更新分析时间
     *
     * @param id id
     * @param analysisTimeConsumption analysisTimeConsumption
     */
    void updateAnalysisTimeConsumption(@Param("id") String id, @Param("analysisTimeConsumption") Long analysisTimeConsumption);

    /**
     * 更新继续分析时间
     *
     * @param id id
     * @param continueAnalysisTimeConsumption continueAnalysisTimeConsumption
     */
    void updateContinueAnalysisTimeConsumption(@Param("id") String id, @Param("continueAnalysisTimeConsumption") Long continueAnalysisTimeConsumption);

    /**
     * 手动创建关联分析node,插入ExecutionNode表
     *
     * @param node node
     */
    void insertExecutionNode(TriggerExecutionNode node);

    void insertExecutionNodeList(@Param("list") List<TriggerExecutionNode> nodeParamList);

    /**
     * insertExecutionFlow
     *
     * @param flowNodeInfo flowNodeInfo
     */
    void insertExecutionFlow(@Param("flowNodeInfo") AssociationFlowNodeInfo flowNodeInfo);

    /**
     * getExecuteFlowTaskId
     *
     * @param executeId executeId
     * @param nodeId nodeId
     * @return executeFlowTaskIds
     */
    List<Map<String, String>> getExecuteFlowTaskId(@Param("executeId") String executeId, @Param("nodeId") String nodeId);

    /**
     * updateExecutionFlowStatus
     *
     * @param flowTaskId flowTaskId
     * @param status status
     */
    void updateExecutionFlowStatus(@Param("flowTaskId") String flowTaskId, @Param("status") String status);

    /**
     * updateExecutionFlowNodeId
     *
     * @param nodeIds 要更新的nodeid
     * @param executionId executionId
     * @return int
     */
    int updateExecutionFlowNodeId(@Param("nodeIds") List<Map<String, String>> nodeIds, @Param("executionId") String executionId);


    /**
     * 手动创建关联分析node,插入TriggerIndicator表
     *
     * @param list list
     * @param executionId executionId
     * @param nodeId nodeId
     */
    void insertTriggerIndicator(@Param("list") List<TriggerIndicator> list, @Param("executionId") String executionId,
        @Param("nodeId") String nodeId);

    void insertTriggerIndicatorList(@Param("list") List<TriggerIndicator> list);

    /**
     * 删除手动关联分析任务
     *
     * @param ids ids
     */
    void deleteAssocationTask(@Param("ids") List<String> ids);

    /**
     * 删除关联分析发送邮件记录
     */
    void deleteAssociationSendEmailRecord();

    /**
     * 查询关联分析任务
     *
     * @param queryParam queryParam
     * @param templateId templateId
     * @param startTime startTime
     * @param endTime endTime
     * @param createUser createUser
     * @return 结果
     */
    List<TriggerExecution> getAssocationTask(@Param("queryParam") AssocationAnalysisTaskQuery queryParam, @Param("templateId") Integer templateId,
        @Param("startTime") long startTime, @Param("endTime") long endTime, @Param("createUser") String createUser);

    TriggerExecution getExecutionByTemplateId(@Param("templateId") Integer templateId, @Param("userId") String userId);

    /**
     * 查询关联分析任务数量
     *
     * @param queryParam queryParam
     * @param templateId templateId
     * @param startTime startTime
     * @param endTime endTime
     * @param createUser createUser
     * @return 数量
     */
    int getAssocationTaskCount(@Param("queryParam") AssocationAnalysisTaskQuery queryParam, @Param("templateId") Integer templateId,
        @Param("startTime") long startTime, @Param("endTime") long endTime, @Param("createUser") String createUser);

    /**
     * 查询关联分析任务节点指标信息
     *
     * @param queryParam queryParam
     * @return 结果
     */
    List<TriggerIndicator> getNodeIndicatorInfo(NodeIndicatorQuery queryParam);

    /**
     * 查询关联分析任务节点指标信息数量
     *
     * @param queryParam queryParam
     * @return 数量
     */
    int getNodeIndicatorInfoCount(NodeIndicatorQuery queryParam);

    /**
     * 获取node信息
     *
     * @param queryParam queryParam
     * @return 节点信息
     */
    TriggerExecutionNode getNodeInfoById(NodeIndicatorQuery queryParam);

    /**
     * 查询关联分析任务节点主指标信息
     *
     * @param queryParam queryParam
     * @return 结果
     */
    String getNodeMainIndicator(NodeIndicatorQuery queryParam);

    /**
     * 查询自定义指标信息
     *
     * @param queryParam queryParam
     * @param userId userId
     * @return 结果
     */
    List<CustomIndicator> getCustomIndicator(@Param("queryParam") CustomIndicatorQuery queryParam,
        @Param("userId") String userId);

    /**
     * 查询自定义指标信息数量
     *
     * @param queryParam queryParam
     * @param userId userId
     * @return 数量
     */
    int getCustomIndicatorCount(@Param("queryParam") CustomIndicatorQuery queryParam, @Param("userId") String userId);

    /**
     * 查询Prometheus自定义指标信息
     *
     * @param queryParam queryParam
     * @param userId userId
     * @return 结果
     */
    List<CustomIndicator> getCustomIndicatorPrometheus(@Param("queryParam") CustomIndicatorQuery queryParam,
        @Param("userId") String userId);

    /**
     * 查询Prometheus自定义指标信息数量
     *
     * @param queryParam queryParam
     * @param userId userId
     * @return 数量
     */
    int getCustomIndicatorPrometheusCount(@Param("queryParam") CustomIndicatorQuery queryParam,
        @Param("userId") String userId);

    /**
     * 根据任务id查询分析任务id集合
     *
     * @param taskId 任务id集合
     * @return 分析任务id
     */
    List<String> getExecutionIdByTaskId(Integer taskId);

    /**
     * 更新node的执行状态和关联度
     *
     * @param executionStatus 执行状态
     * @param executionId 手动任务id
     * @param correlationDegree 关联度
     * @param nodeId nodeid
     */
    void updateExecutionNodeStatus(@Param("executionStatus") String executionStatus,
        @Param("executionId") String executionId, @Param("nodeId") String nodeId,
        @Param("correlationDegree") Float correlationDegree);

    /**
     * 更新node的指标关联度
     *
     * @param list 指标
     * @param executionId 手动任务id
     * @param nodeId nodeid
     */
    void updateExecutionIndicatorDegree(@Param("list") List<TriggerIndicator> list,
        @Param("executionId") String executionId, @Param("nodeId") String nodeId);

    /**
     * 更新node的mainIndicator
     *
     * @param mainIndicatorJson mainIndicatorJson
     * @param executionId 手动任务id
     * @param nodeId nodeid
     */
    void updateExecutionNodeMainIndicator(@Param("mainIndicatorJson") String mainIndicatorJson,
        @Param("executionId") String executionId, @Param("nodeId") String nodeId);

    /**
     * 根据任务id查询数据源id
     *
     * @param taskId 任务id
     * @return 数据源id
     */
    String getDataSourceIdByTask(Integer taskId);

    /**
     * 查询关联分析任务的所有指标数量
     *
     * @return 数量
     */
    int getTaskAllIndicatorCount();

    /**
     * 根据类型查询关联分析任务的节点数量
     *
     * @param executionId 任务id
     * @param nodeType 节点类型
     * @return 数量
     */
    int getTaskNodeCountByType(@Param("executionId") String executionId, @Param("nodeType") String nodeType);

    List<Pair<String, Long>> getTaskNodeCountEveryType(@Param("executionId") String executionId);

    /**
     * 查询关联分析任务所有运行中的指标数量
     *
     * @return 数量
     */
    int getAllRunningIndicatorCount();

    /**
     * 获取已过期的关联分析任务id
     *
     * @param dataOversizeTimeInMillis 历史数据超期时间
     * @return ids
     */
    List<String> getHistoricalExecutionTaskId(@Param("dataOversizeTimeInMillis") long dataOversizeTimeInMillis);

    /**
     * 创建关联分析node,插入告警类型的表
     *
     * @param list list
     * @param executionId executionId
     * @param nodeId nodeId
     */
    void insertTriggerAlarmList(@Param("list") List<TriggerAlarmList> list, @Param("executionId") String executionId,
        @Param("nodeId") String nodeId);


    /**
     * 查询关联分析任务告警节点信息
     *
     * @param queryParam queryParam
     * @return 结果
     */
    List<TriggerAlarmList> getNodeAlarmInfo(NodeIndicatorQuery queryParam);

    /**
     * 查询关联分析任务中每个节点告警信息
     *
     * @param queryParam queryParam
     * @return 结果
     */
    List<TriggerAlarmList> getExecutionAlarmInfo(NodeIndicatorQuery queryParam);

    /**
     * 创建智能推荐node,插入TriggerRecommendation表
     *
     * @param intelligentRecommendationResults intelligentRecommendationResults
     * @param executionId executionId
     * @param intelligentRecommendationNodeType intelligentRecommendationNodeType
     */
    void insertTriggerRecommendation(@Param("intelligentRecommendationResults") List<TriggerRecommendation> intelligentRecommendationResults,
        @Param("executionId") String executionId,  @Param("intelligentRecommendationNodeType") String intelligentRecommendationNodeType);

    /**
     * 查询关联分析任务智能推荐节点信息
     *
     * @param queryParam queryParam
     * @return 结果
     */
    TriggerRecommendation getTriggerRecommendationInfo(NodeIndicatorQuery queryParam);

    /**
     * 通过执行id查询关联分析任务智能推荐节点信息
     *
     * @param executionId executionId
     * @return 结果
     */
    List<TriggerRecommendation> getRecommendationInfoById(@Param("id") String executionId);

    /**
     * 查询关联分析任务告警节点信息数量
     *
     * @param queryParam queryParam
     * @return 数量
     */
    int getNodeAlarmInfoCount(NodeIndicatorQuery queryParam);

    /**
     * 查询关联分析曲线
     *
     * @param queryParam queryParam
     * @return 曲线
     */
    String getAlarmCurve(AssociationCurveQuery queryParam);


    /**
     * 创建关联分析node,插入日志类型的曲线
     *
     * @param executionId executionId
     * @param nodeId nodeId
     * @param correlationDegree correlationDegree
     * @param logCurveJson logCurveJson
     */
    void insertTriggerLog(@Param("executionId") String executionId, @Param("nodeId") String nodeId,
        @Param("correlationDegree") Float correlationDegree, @Param("logCurveJson") String logCurveJson);

    /**
     * 创建关联分析node,插入日志类型的模板
     *
     * @param executionId executionId
     * @param nodeId nodeId
     * @param templateList templateList
     */
    void insertTriggerLogTemplate(@Param("executionId") String executionId, @Param("nodeId") String nodeId,
        @Param("templateList") List<LogTemplate> templateList);


    /**
     * 查询关联分析曲线日志
     *
     * @param queryParam queryParam
     * @return 曲线
     */
    String getLogCurve(AssociationCurveQuery queryParam);

    /**
     * 查询关联分析日志模板
     *
     * @param executionId executionId
     * @param nodeId nodeId
     * @param ids ids
     * @return 模板
     */
    List<LogTemplate> getLogTemplate(@Param("executionId") String executionId, @Param("nodeId") String nodeId,
        @Param("ids") List<String> ids);

    List<String> filterNonContentTemplateId(@Param("executionId") String executionId, @Param("nodeId") String nodeId,
        @Param("ids") List<String> ids);

    /**
     * 获取状态为分析中的记录
     *
     * @return 关联分析记录
     */
    List<TriggerExecution> getAnalyzingAssocationTask();

    /**
     * 根据异常Id获取关联分析结果
     *
     * @param outlierId 异常Id
     * @return 关联分析记录
     */
    List<TriggerExecution> getAnalyzingAssociationByOutlier(@Param("outlierId") String outlierId);

    int getDeleteHistoricalExecutionTaskCount();

    List<String> getDeleteHistoricalExecutionTaskIdLimitCount(@Param("number") int number);

    void insertFilterMoEntities(@Param("list") List<FilterMoEntity> filterMoEntities);

    List<FilterMoEntity> getFilterMoEntities(@Param("executionId") String executionId, @Param("nodeId") String nodeId);
}
