/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.taskmanage.validate.resourceschedule;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.dvanalysisengineservice.business.common.parametercheck.ParameterChecker;
import com.huawei.i2000.dvanalysisengineservice.business.resourceschedule.dto.DeployService;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.TaskImportUtils;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.cbs.ValidateUtil;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.resourceschedule.HostPortraitEntity;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.resourceschedule.ProductPortraitEntity;
import com.huawei.i2000.dvanalysisengineservice.dao.resourceschedule.PortraitManageDao;
import com.huawei.i2000.dvanalysisengineservice.model.BusinessPortrait;
import com.huawei.i2000.dvanalysisengineservice.model.ParameterBooleanCheckResult;
import com.huawei.i2000.dvanalysisengineservice.model.PortraitIndicator;
import com.huawei.i2000.dvanalysisengineservice.model.TriggerIndicator;
import com.huawei.i2000.dvanalysisengineservice.util.ContextUtils;
import com.huawei.i2000.dvanalysisengineservice.util.exception.ExceptionInternationalConstant;
import com.huawei.i2000.dvanalysisengineservice.util.exception.ExceptionUtil;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 主机画像校验类
 *
 * <AUTHOR>
 * @since 2023/5/9
 */
@Service
public class HostPortraitValidate implements ImportPortraitValidate {

    private static final OssLog LOGGER = OssLogFactory.getLogger(HostPortraitValidate.class);

    @Autowired
    PortraitManageDao portraitManageDao;

    @Autowired
    ParameterChecker parameterChecker;

    @Override
    public boolean doFilter(ProductPortraitEntity productPortrait, TidalScheduleCache tidalScheduleCache)
        throws ServiceException {
        List<HostPortraitEntity> hostPortraits = productPortrait.getHostPortrait();
        if (CollectionUtils.isEmpty(hostPortraits)) {
            LOGGER.info("[HostPortraitValidate] This import operation dont contain host portrait, portraitName is {}",
                productPortrait.getPortraitName());
            return true;
        }
        // 校验是否有重复
        validateDuplicate(hostPortraits);
        // 查询缓存中的业务画像
        List<BusinessPortrait> businessPortraitList = tidalScheduleCache.getBusinessPortrait();
        // 查询数据库中的业务画像
        List<BusinessPortrait> businessPortraitDataList = portraitManageDao.getBusinessPortraitList();
        List<BusinessPortrait> allBusinessPortrait = new ArrayList<>();
        allBusinessPortrait.addAll(businessPortraitList);
        allBusinessPortrait.addAll(businessPortraitDataList);

        for (HostPortraitEntity hostPortrait : hostPortraits) {
            // 校验参数
            validateParam(hostPortrait);
            // 校验每个主机画像依赖业务画像是否存在
            validateDependentBusinessPortrait(hostPortrait, allBusinessPortrait);
            // 校验主机关联指标
            validateAssociationIndicator(hostPortrait);
        }
        return true;
    }

    private void validateParam(HostPortraitEntity hostPortrait) throws ServiceException {
        if (!ValidateUtil.modelValidate(hostPortrait)) {
            LOGGER.error("ValidateUtil fail, param invalid !");
            throw new ServiceException(
                ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_ERROR_VALID_PARAMETER_ERROR, ContextUtils.getContext().getLocale()));
        }
    }

    private void validateDependentBusinessPortrait(HostPortraitEntity hostPortrait,
        List<BusinessPortrait> businessPortraitList) throws ServiceException {
        List<String> businessNames = businessPortraitList.stream()
            .map(BusinessPortrait::getBusinessName)
            .collect(Collectors.toList());
        List<String> deployServiceName = new ArrayList<>();
        List<String> deployServiceList = hostPortrait.getDeployServiceList();
        try {
            for (String deployServiceContent : deployServiceList) {
                DeployService deployService = JSONObject.parseObject(deployServiceContent, DeployService.class);
                deployServiceName.add(deployService.getServiceName());
            }
        } catch (JSONException e) {
            LOGGER.error("[validateDependentBusinessPortrait] parse deployService content error.e=", e);
            throw new ServiceException(ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_ERROR_RESOURCE_SCHEDULE_DEPLOY_SERVICE, ContextUtils.getContext().getLocale()));
        }
        if (!businessNames.containsAll(deployServiceName)) {
            LOGGER.error("[validateDependentBusinessPortrait] No find depend business. Host name is {}", hostPortrait.getHostPortraitName());
            throw new ServiceException(ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_ERROR_RESOURCE_SCHEDULE_NO_BUSINESS_ERROR, ContextUtils.getContext().getLocale()));
        }
    }

    private void validateDuplicate(List<HostPortraitEntity> hostPortraits) throws ServiceException {
        Map<String, List<HostPortraitEntity>> distinctMap = hostPortraits.stream()
            .collect(Collectors.groupingBy(HostPortraitEntity::getHostPortraitName));
        for (Map.Entry<String, List<HostPortraitEntity>> map : distinctMap.entrySet()) {
            if (map.getValue().size() > 1) {
                LOGGER.error("[validateDuplicate] Duplicate config, about {}", map.getKey());
                throw new ServiceException(ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_ERROR_RESOURCE_SCHEDULE_HOST_DUPLICATE_ERROR, ContextUtils.getContext().getLocale()));
            }
        }

        for (HostPortraitEntity hostPortrait : hostPortraits) {
            ParameterBooleanCheckResult repeatHostPortrait = parameterChecker.repeat("hostPortrait", "hostPortraitName",
                hostPortrait.getHostPortraitName());
            ParameterBooleanCheckResult repeatHostType = parameterChecker.repeat("hostPortrait", "hostType",
                hostPortrait.getClusterName());
            if (!repeatHostPortrait.getCheckResult() || !repeatHostType.getCheckResult()) {
                throw ExceptionUtil.createException(
                    ExceptionInternationalConstant.IMPORT_ERROR_RESOURCE_SCHEDULE_HOST_DUPLICATE_ERROR);
            }
        }
    }

    private void validateAssociationIndicator(HostPortraitEntity hostPortrait) throws ServiceException {
        List<TriggerIndicator> indicators = hostPortrait.getPortraitIndicatorList();
        List<TriggerIndicator> filterIndicators = TaskImportUtils.getFilterTriggerIndicators(indicators, false);
        // 校验是否重复
        validDuplicateIndicator(filterIndicators, hostPortrait.getHostPortraitName());
        // 转换为数据库实体类
        if (CollectionUtils.isEmpty(JSONArray.parseArray(JSONObject.toJSONString(filterIndicators), PortraitIndicator.class))) {
            LOGGER.error("[validateAssociationIndicator] error.");
            throw new ServiceException(
                ResourceUtil.getMessage(ExceptionInternationalConstant.IMPORT_ERROR_VALID_PARAMETER_ERROR, ContextUtils.getContext().getLocale()));
        }
    }

    private void validDuplicateIndicator(List<TriggerIndicator> indicators, String name) throws ServiceException {
        // 校验指标里面有没有重复的
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(indicators)) {
            throw new ServiceException(ResourceUtil.getMessage(ExceptionInternationalConstant.EMPTY_INDICATOR, ContextUtils.getContext().getLocale()) + name);
        }
        Map<String, List<TriggerIndicator>> distinctMap = indicators.stream()
            .collect(Collectors.groupingBy(
                indicator -> indicator.getDn() + indicator.getMoType() + indicator.getMeasUnitKey()
                    + indicator.getMeasTypeKey() + indicator.getOriginalValue()));
        for (Map.Entry<String, List<TriggerIndicator>> map : distinctMap.entrySet()) {
            if (map.getValue().size() > 1) {
                throw new ServiceException(ResourceUtil.getMessage(ExceptionInternationalConstant.REPEAT_INDICATOR, ContextUtils.getContext().getLocale()));
            }
        }
    }

}
