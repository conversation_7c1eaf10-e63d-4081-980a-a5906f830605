/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.taskmanage.validate.singlepredict;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvanalysisengineservice.business.taskmanage.dto.singlepredict.SinglePredictImporterEntity;

/**
 * 独立指标任务导入校验链
 *
 * <AUTHOR>
 * @since 2023/5/29
 */
public interface ImportSinglePredictValidate {
    boolean doFilter(SinglePredictImporterEntity singlePredictJSONObject)
        throws ServiceException;
}
