/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core;

import com.huawei.bsp.commonlib.hofs.HofsException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvanalysisengineservice.business.capacity.dto.AggregateTaskIndicator;
import com.huawei.i2000.dvanalysisengineservice.business.common.HofsUtil;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.conceptdrift.ConceptDriftManage;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.conceptdrift.DriftTaskMessage;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.conceptdrift.DriftTaskMessageCacheOper;
import com.huawei.i2000.dvanalysisengineservice.business.synanalysisresult.model.IndicatorPredictTrainResult;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.dto.AlgorithmModelMessage;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.dto.TrainingIndicatorResult;
import com.huawei.i2000.dvanalysisengineservice.business.taskschedule.core.dto.TrainningTaskMessage;
import com.huawei.i2000.dvanalysisengineservice.dao.common.MapperFactory;
import com.huawei.i2000.dvanalysisengineservice.dao.multiple.mapper.CapacityAnalysisMapper;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.TaskConstant;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.handlerimpl.TaskManageDao;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.mapper.TaskIndicatorMapper;
import com.huawei.i2000.dvanalysisengineservice.dao.taskmanage.mapper.TaskManageMapper;
import com.huawei.i2000.dvanalysisengineservice.model.AnalysisTask;
import com.huawei.i2000.dvanalysisengineservice.model.TaskIndicator;

import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 调度功能管理
 *
 * <AUTHOR>
 * @since 2021/6/6
 */
@Service
public class TrainingTaskManager {
    @Autowired
    TaskManageDao taskManageDao;

    @Autowired
    MapperFactory mapperFactory;

    @Autowired
    ConceptDriftManage conceptDriftManage;

    private static final OssLog LOGGER = OssLogFactory.getLogger(TrainingTaskManager.class);

    private static final String TEMP_PATH = System.getProperty("java.io.tmpdir") + File.separator;

    private static final Boolean CONTAINER = Boolean.parseBoolean(System.getenv("CONTAINER"));

    /**
     * 更新任务状态
     */
    public void updateTrainingStatus() {
        TaskManageMapper taskManageMapper = mapperFactory.getMapper(TaskManageMapper.class);
        List<Integer> trainingTaskId = taskManageMapper.getTrainingTaskId();
        Map<String, TrainningTaskMessage> trainningMap = TrainingMessageCacheOper.getInstance().getAll();
        List<Integer> finishedTaskIds = trainningMap.values().stream().filter(TrainningTaskMessage::getPyFinished)
            .map(TrainningTaskMessage::getTaskId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(finishedTaskIds)) {
            LOGGER.info("train task execute finished, taskIds={}", finishedTaskIds);
        }
        List<Integer> deleteIds = new ArrayList<>();
        trainingTaskId.forEach(taskId -> {
            TrainningTaskMessage message = trainningMap.get(String.valueOf(taskId));
            if (message == null) {
                LOGGER.error("taskid is not in training cache, taskid is {}", taskId);
                taskManageDao.updateTrainStatus(taskId, TaskConstant.TRAIN_STATUS_FAIL);
                return;
            }
            try {
                if (isDeleteAndUpdateRecord(message)) {
                    deleteIds.add(message.getTaskId());
                }
            } catch (Exception e) {
                LOGGER.error("training task check error, taskid is {}", message.getTaskId(), e);
                taskManageDao.updateTrainStatus(message.getTaskId(), TaskConstant.TRAIN_STATUS_FAIL);
                deleteIds.add(message.getTaskId());
            }
        });
        deleteIds.forEach(this::deteleTrainingTaskMessage);
    }

    /**
     * 是否需要删除
     *
     * @param entry 训练记录键值对
     * @return 是否删除
     * @throws ServiceException 异常
     */
    private boolean isDeleteAndUpdateRecord(TrainningTaskMessage entry) throws ServiceException {
        if (!entry.getPyFinished()) {
            if (System.currentTimeMillis() - entry.getStartTime() > ScheduleConstant.MAX_TRAINING_WAIT_TIME) {
                taskManageDao.updateTrainStatus(entry.getTaskId(), TaskConstant.TRAIN_STATUS_FAIL);
                return true;
            }
            return false;
        }
        int status = queryTrainingStatus(entry);
        AnalysisTask analysisTaskById = taskManageDao.getAnalysisTaskById(entry.getTaskId());
        long taskIndicatorNum = analysisTaskById.getIndicatorList().stream().filter(indicator -> (indicator.getSoftDelete() == null || !indicator.getSoftDelete())).count();
        if (status == ScheduleConstant.SUCCESS_TRAINING) {
            if ((TaskConstant.TASK_TYPE_INDICATOR_EXCEPTION == analysisTaskById.getTaskType()
                || TaskConstant.TASK_TYPE_INDICATOR_PREDICT == analysisTaskById.getTaskType())
                && taskIndicatorNum != entry.getIndicatorNum()) {
                taskManageDao.updateTrainStatus(entry.getTaskId(), TaskConstant.TRAIN_STATUS_PARTIAL_SUCCESS);
            } else {
                taskManageDao.updateTrainStatus(entry.getTaskId(), TaskConstant.TRAIN_STATUS_SUCCESS);
            }
            AlgorithmModelOper.getInstance()
                .put(new AlgorithmModelMessage(entry.getTaskId(), System.currentTimeMillis()));
        } else if (TaskConstant.TASK_TYPE_CAPACITY_EXCEPTION == analysisTaskById.getTaskType()
            && status == ScheduleConstant.PARTIAL_SUCCESS_TRAINING) {
            taskManageDao.updateTrainStatus(entry.getTaskId(), TaskConstant.TRAIN_STATUS_PARTIAL_SUCCESS);
        } else if (TaskConstant.TASK_TYPE_INDICATOR_PREDICT == analysisTaskById.getTaskType()
            && status == ScheduleConstant.PARTIAL_SUCCESS_TRAINING) {
            taskManageDao.updateTrainStatus(entry.getTaskId(), TaskConstant.TRAIN_STATUS_PARTIAL_SUCCESS);
        } else if (TaskConstant.TASK_TYPE_INDICATOR_EXCEPTION == analysisTaskById.getTaskType()
            && status == ScheduleConstant.PARTIAL_SUCCESS_TRAINING) {
            taskManageDao.updateTrainStatus(entry.getTaskId(), TaskConstant.TRAIN_STATUS_PARTIAL_SUCCESS);
        } else {
            taskManageDao.updateTrainStatus(entry.getTaskId(), TaskConstant.TRAIN_STATUS_FAIL);
            LOGGER.error("Training is fail : {}", entry.getTaskId());
        }

        return true;
    }

    /**
     * 查询训练接口状态
     *
     * @param message taskId
     * @return int
     * @throws ServiceException ServiceException
     */
    public int queryTrainingStatus(TrainningTaskMessage message) throws ServiceException {
        List<String> fileList = HofsUtil.listFiles(HofsUtil.HOFS_PATH_MODEL + message.getTaskId() + File.separator);
        AnalysisTask analysisTaskById = taskManageDao.getAnalysisTaskById(message.getTaskId());
        // 容量任务上传的是文件夹而不是文件，容器化场景查不出来，需要使用独立方法
        if (CONTAINER && analysisTaskById.getTaskType().equals(TaskConstant.TASK_TYPE_CAPACITY_EXCEPTION)) {
            try {
                fileList = com.huawei.digitalview.commons.utils.hofs.HofsUtil.listRemoteDirs(HofsUtil.HOFS_PATH_MODEL + message.getTaskId() + File.separator);
            } catch (HofsException e) {
                LOGGER.error("container queryTrainingStatus hofs error.", e);
                throw new ServiceException("container queryTrainingStatus hofs error");
            }
        }
        switch (analysisTaskById.getTaskType()) {
            case TaskConstant.TASK_TYPE_INDICATOR_EXCEPTION:
                return indicatorExceptionStatus(analysisTaskById, fileList, message);
            case TaskConstant.TASK_TYPE_ALARM_EXCEPTION:
                return alarmStatus(message.getTaskId(), fileList, message.getStartTime());
            case TaskConstant.TASK_TYPE_LOG_EXCEPTION:
                return logStatus(message.getTaskId(), fileList, message.getStartTime());
            case TaskConstant.TASK_TYPE_CAPACITY_EXCEPTION:
                return capacityTaskTrainStatus(analysisTaskById, fileList, message.getStartTime());
            case TaskConstant.TASK_TYPE_INDICATOR_PREDICT:
                return indicatorPredictStatus(analysisTaskById, message.getStartTime());
            case TaskConstant.TASK_TYPE_RESOURCE_SCHEDULE:
                return resourceScheduleStatus(message.getTaskId(), fileList, message.getStartTime());
            default:
                throw new ServiceException("Error type : {}", analysisTaskById.getTaskType());
        }
    }

    private int indicatorPredictStatus(AnalysisTask analysisTaskById, Long startTime) {
        int taskId = analysisTaskById.getTaskId();
        String resultFilePath = HofsUtil.HOFS_PATH_MODEL + taskId + File.separator + taskId + ".txt";
        String resultContent = HofsUtil.getFileContent(resultFilePath);
        try {
            if (StringUtils.isEmpty(resultContent) || getHofsLastModifiedTime(HofsUtil.getFileData(resultFilePath)) < startTime) {
                return ScheduleConstant.RUN_TRAINING;
            }
        } catch (ServiceException e) {
            LOGGER.error("get train result file last modified time error, e=", e);
            return ScheduleConstant.RUN_TRAINING;
        }

        IndicatorPredictTrainResult trainResult = JSONObject.parseObject(resultContent, IndicatorPredictTrainResult.class);
        if (trainResult.getAutoRetrain()) {
            // 更新概念漂移重训练结果
            conceptDriftManage.updateRetrainResult(taskId, trainResult.getRetrainIndicatorIds());
        }
        trainResult.setIndicatorIds(ConceptDriftManage.filterUnitedId(trainResult.getIndicatorIds()));
        int successNum = trainResult.getIndicatorIds().size();
        if (successNum == 0) {
            return ScheduleConstant.RUN_TRAINING;
        } else if (successNum == analysisTaskById.getIndicatorList().size()) {
            return ScheduleConstant.SUCCESS_TRAINING;
        } else {
            return ScheduleConstant.PARTIAL_SUCCESS_TRAINING;
        }
    }

    private int resourceScheduleStatus(Integer taskId, List<String> fileList, Long startTime) {
        List<?> collect = fileList.stream().filter(fileName -> {
            String[] taskAndIndicatorId = fileName.split("\\.");
            if (!taskAndIndicatorId[0].equals(String.valueOf(taskId))) {
                return false;
            }
            String fileData = HofsUtil.getFileData(
                HofsUtil.HOFS_PATH_MODEL + taskId + File.separator + fileName);
            try {
                return getHofsLastModifiedTime(fileData) / 1000 >= startTime / 1000;
            } catch (ServiceException exception) {
                return false;
            }
        }).collect(Collectors.toList());

        if (collect.size() == 0) {
            return ScheduleConstant.RUN_TRAINING;
        } else {
            return ScheduleConstant.SUCCESS_TRAINING;
        }
    }

    /**
     * 容量瓶颈训练判断
     *
     * @param task task
     * @param fileList fileList
     * @param startTime startTime
     * @return 状态
     */
    private int capacityTaskTrainStatus(AnalysisTask task, List<String> fileList, long startTime) {
        CapacityAnalysisMapper capacityAnalysisMapper = mapperFactory.getMapper(CapacityAnalysisMapper.class);
        List<String> ids = capacityAnalysisMapper.getExecutionIndicatorByTaskId(task.getTaskId());
        TaskIndicatorMapper taskIndicatorMapper = mapperFactory.getMapper(TaskIndicatorMapper.class);
        List<AggregateTaskIndicator> taskIndicators = taskIndicatorMapper.getAggregateIndicatorList(task.getTaskId(), null, true);
        List<?> collect = fileList.stream().filter(fileName -> {
            List<String> modelList = HofsUtil.listFiles(HofsUtil.HOFS_PATH_MODEL + task.getTaskId() + File.separator + fileName + File.separator);
            for (String model : modelList) {
                try {
                    String fileData;
                    if (CONTAINER) {
                        fileData = HofsUtil.getFileData(HofsUtil.HOFS_PATH_MODEL + task.getTaskId() + File.separator + fileName + File.separator + model);
                    } else {
                        fileData = HofsUtil.getFileData(model);
                    }
                    if (getHofsLastModifiedTime(fileData) / 1000 < startTime / 1000) {
                        return false;
                    }
                } catch (ServiceException exception) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        if (collect.size() == ids.size() - 1 - taskIndicators.size()) {
            return ScheduleConstant.SUCCESS_TRAINING;
        } else if (collect.size() == 0) {
            return ScheduleConstant.RUN_TRAINING;
        } else {
            LOGGER.error("[capacityTaskTrainStatus] Training is fail about model num is {} file num is {}", ids.size(), collect.size());
            return ScheduleConstant.PARTIAL_SUCCESS_TRAINING;
        }
    }

    /**
     * 告警分析训练判断
     *
     * @param taskId taskId
     * @param fileList fileList
     * @param startTime startTime
     * @return 状态
     */
    private int alarmStatus(int taskId, List<String> fileList, long startTime) {
        String taskIdString = String.valueOf(taskId);
        List<?> collect = fileList.stream().filter(fileName -> {
            String[] taskAndIndicatorId = fileName.split("_");
            if (!taskAndIndicatorId[0].equals(taskIdString)) {
                return false;
            }
            String fileData = HofsUtil.getFileData(HofsUtil.HOFS_PATH_MODEL + taskId +
                File.separator + fileName);
            try {
                return getHofsLastModifiedTime(fileData) / 1000 >= startTime / 1000;
            } catch (ServiceException exception) {
                return false;
            }
        }).collect(Collectors.toList());
        // 跟算法包约定模型会固定生成6个
        if (collect.size() == 6) {
            return ScheduleConstant.SUCCESS_TRAINING;
        } else {
            return ScheduleConstant.RUN_TRAINING;
        }
    }

    /**
     * 日志分析训练判断
     *
     * @param taskId taskId
     * @param fileList fileList
     * @param startTime startTime
     * @return 状态
     */
    private int logStatus(int taskId, List<String> fileList, long startTime) {
        List<?> collect = fileList.stream().filter(fileName -> {
            String[] taskAndIndicatorId = fileName.split("_");
            if (!taskAndIndicatorId[0].equals(String.valueOf(taskId))) {
                return false;
            }
            String fileData = HofsUtil.getFileData(HofsUtil.HOFS_PATH_MODEL + taskId +
                File.separator + fileName);
            try {
                return getHofsLastModifiedTime(fileData) / 1000 >= startTime / 1000;
            } catch (ServiceException exception) {
                return false;
            }
        }).collect(Collectors.toList());
        // 跟算法包约定模型会至少生成2个, tree, lstm
        if (collect.size() >= 2) {
            return ScheduleConstant.SUCCESS_TRAINING;
        } else {
            return ScheduleConstant.RUN_TRAINING;
        }
    }

    private int indicatorExceptionStatus(AnalysisTask analysisTaskById, List<String> fileList,
        TrainningTaskMessage message) {
        String resFileName = analysisTaskById.getTaskId() + ".txt";
        if (fileList.stream().anyMatch(fileName -> fileName.equals(resFileName))) {
            // 新的算法包生成一个taskid.txt文件里面写了一个数，代表几个指标训练成功
            HofsUtil.downloadFile(HofsUtil.HOFS_PATH_MODEL + message.getTaskId() + File.separator + resFileName, TEMP_PATH + resFileName);
            try {
                String resultLine = new String(Files.readAllBytes(Paths.get(TEMP_PATH + resFileName)), StandardCharsets.UTF_8);
                TrainingIndicatorResult result = null;
                if (StringUtils.isNotEmpty(resultLine)) {
                    result = JSONObject.parseObject(resultLine, TrainingIndicatorResult.class);
                }
                numberOfSuccessfulRecalculations(result, analysisTaskById);
                if (result == null || result.getSuccessNum() == 0) {
                    LOGGER.error("[indicatorExceptionStatus] {} is empty", resFileName);
                    return ScheduleConstant.RUN_TRAINING;
                }
                judgeAutoRetrain(analysisTaskById, result);
                if (result.getSuccessNum() != message.getIndicatorNum()) {
                    return ScheduleConstant.PARTIAL_SUCCESS_TRAINING;
                } else {
                    return ScheduleConstant.SUCCESS_TRAINING;
                }
            } catch (IOException e) {
                LOGGER.error("[indicatorExceptionStatus] read {} error,e=", resFileName, e);
                return ScheduleConstant.RUN_TRAINING;
            } finally {
                if (!new File(TEMP_PATH + resFileName).delete()) {
                    LOGGER.error("[indicatorExceptionStatus] delete {} fail", resFileName);
                }
            }
        }
        List<String> indicatorIds = analysisTaskById.getIndicatorList().stream().map(TaskIndicator::getIndicatorId).collect(Collectors.toList());
        String taskIdString = String.valueOf(analysisTaskById.getTaskId());
        List<?> collect = fileList.stream().filter(fileName -> {
            String taskId = fileName.substring(0, fileName.indexOf(" "));
            String indicatorId = fileName.substring(fileName.indexOf(" ") + 1);
            if (!taskId.equals(taskIdString) || !indicatorIds.contains(indicatorId)) {
                return false;
            }
            String fileData = HofsUtil.getFileData(HofsUtil.HOFS_PATH_MODEL + message.getTaskId() + File.separator + fileName);
            try {
                return getHofsLastModifiedTime(fileData) / 1000 >= message.getStartTime() / 1000;
            } catch (ServiceException exception) {
                return false;
            }
        }).collect(Collectors.toList());
        if (collect.size() != message.getIndicatorNum()) {
            return ScheduleConstant.RUN_TRAINING;
        } else {
            return ScheduleConstant.SUCCESS_TRAINING;
        }
    }

    public void numberOfSuccessfulRecalculations(TrainingIndicatorResult result, AnalysisTask analysisTaskById) {
        if (result == null) {
            return;
        }
        List<TaskIndicator> taskIndicators = analysisTaskById.getIndicatorList();
        if (CollectionUtils.isEmpty(taskIndicators)) {
            return;
        }
        // 成功的数量要把软删除的指标去掉
        int successNum = 0;
        List<String> successList = result.getIndicatorIds();
        for (TaskIndicator indicator : taskIndicators) {
            if (successList.contains(indicator.getIndicatorId()) && (indicator.getSoftDelete() == null || !indicator.getSoftDelete())) {
                successNum++;
            }
        }
        result.setSuccessNum(successNum);
    }

    private void judgeAutoRetrain(AnalysisTask analysisTaskById, TrainingIndicatorResult result) {
        if (Objects.equals(result.getAutoRetrain(), true)) {
            List<String> successIndicators = result.getRetrainIndicatorIds();
            conceptDriftManage.updateRetrainResult(analysisTaskById.getTaskId(), successIndicators);
        }
    }

    public long getHofsLastModifiedTime(String fileData) throws ServiceException {
        JSONObject jsonObject = JSONObject.parseObject(fileData);
        String modifyTime = jsonObject.getString("LastModifiedTime");
        // 容器化场景下返回的就是时间戳，无需格式化
        if (CONTAINER) {
            return Long.parseLong(modifyTime);
        }
        // 2022-02-13T23:59:20.2359+08:00 hofs返回的毫秒数位数不一致，需要去掉
        modifyTime = modifyTime.replaceAll("(\\.[0-9]+)", "");
        SimpleDateFormat formatDateTime = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");

        try {
            return formatDateTime.parse(modifyTime).getTime();
        } catch (ParseException e) {
            LOGGER.error("parse Time error", e);
            throw new ServiceException();
        }
    }

    /**
     * 删除训练状态
     *
     * @param taskId taskId
     */
    public void deteleTrainingTaskMessage(int taskId) {
        TrainningTaskMessage message = TrainingMessageCacheOper.getInstance().getOne(taskId);
        if (message != null) {
            TrainingMessageCacheOper.getInstance().remove(message.getTaskId());
        }
    }

    /**
     * 删除漂移任务状态
     *
     * @param taskId taskId
     */
    public void deleteDriftingTaskMessage(int taskId) {
        DriftTaskMessage message = DriftTaskMessageCacheOper.getInstance().getOne(taskId);
        if (message != null) {
            DriftTaskMessageCacheOper.getInstance().remove(message.getTaskId());
        }
    }

    /**
     * 添加训练任务与jobid到map
     *
     * @param taskId taskId
     * @param indicatorNum 指标数量
     * @throws ServiceException ServiceException
     */
    public void setTrainingTaskMessage(int taskId, int indicatorNum) throws ServiceException {
        TrainningTaskMessage message = TrainingMessageCacheOper.getInstance().getOne(taskId);
        if (message == null) {
            LOGGER.error("train message is null, taskId = {}, indicatorNum = {}", taskId, indicatorNum);
            throw new ServiceException("train message is null.");
        }
        message.setPyFinished(true);
        message.setIndicatorNum(indicatorNum);
        TrainingMessageCacheOper.getInstance().put(message);
    }

    /**
     * 放入训练信息
     *
     * @param taskId taskId
     * @param timespan time
     */
    public void putTrainingMessage(int taskId, long timespan) {
        TrainingMessageCacheOper.getInstance().put(new TrainningTaskMessage(taskId, timespan));
    }
}
