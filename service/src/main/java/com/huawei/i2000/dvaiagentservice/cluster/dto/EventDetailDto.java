/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.cluster.dto;

import com.huawei.i2000.dvaiagentservice.diagnose.enums.DiagnosticStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述
 * 代表业务拓扑网元类型层的类
 *
 * <AUTHOR>
 * @since 2025/5/1
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class EventDetailDto {

    /**
     * 故障标识
     */
    private Long csn;

    /**
     * 诊断状态
     */
    private DiagnosticStatusEnum diagnosticStatusEnum;

    /**
     * 发生时间
     */
    private Long occurTime;

    /**
     * 名称
     */
    private String incidentName;

    /**
     * 故障描述
     */
    private String description;

    /**
     * Events
     */
    private List<EventTopoDto> eventDtos = new ArrayList<>();
}

