/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentservice.dao;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvaiagentservice.business.common.MapperFactory;
import com.huawei.i2000.dvaiagentservice.business.keyword.EntityVocabItem;
import com.huawei.i2000.dvaiagentservice.cluster.entity.Keyword;
import com.huawei.i2000.dvaiagentservice.dao.mapper.KeywordMapper;
import com.huawei.i2000.dvaiagentservice.util.exception.ExceptionInternationalConstant;
import com.huawei.i2000.dvaiagentservice.util.exception.ExceptionUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/6/10
 */
@Component
public class KeywordDao {
    private static final OssLog LOGGER = OssLogFactory.getLogger(KeywordDao.class);

    private static final int EXECUTE_BATCH_SIZE = 1000;

    @Autowired
    private MapperFactory mapperFactory;

    /**
     * 根据关键词类型和输入文本查询关键词列表
     *
     * @param keywordType 关键词类型
     * @param input       输入文本（可为空）
     * @param maxSize     最大返回数量
     * @return 关键词列表
     * @throws ServiceException ServiceException
     */
    public List<Keyword> searchKeywords(String keywordType, String input, Integer maxSize) throws ServiceException {
        KeywordMapper mapper = mapperFactory.getMapper(KeywordMapper.class);
        try {
            return mapper.searchKeywords(keywordType, input, maxSize);
        } catch (DataAccessException e) {
            LOGGER.error("search keywords failed:", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    /**
     * 统计匹配的关键词总数
     *
     * @param keywordType 关键词类型
     * @param input 输入文本（可为空）
     * @return 总数
     * @throws ServiceException ServiceException
     */
    public Integer countKeywords(String keywordType, String input) throws ServiceException {
        KeywordMapper mapper = mapperFactory.getMapper(KeywordMapper.class);
        try {
            return mapper.countKeywords(keywordType, input);
        } catch (DataAccessException e) {
            LOGGER.error("count keywords failed:", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    /**
     * 根据名称和类型查询关键词 批量
     *
     * @param vocabItems 关键词对象
     * @return 关键词对象
     * @throws ServiceException ServiceException
     */
    public List<Keyword> getKeywordByNameAndTypeBatch(Collection<EntityVocabItem> vocabItems) throws ServiceException {
        if (CollectionUtils.isEmpty(vocabItems)) {
            return Collections.emptyList();
        }
        KeywordMapper mapper = mapperFactory.getMapper(KeywordMapper.class);
        List<Keyword> res = new ArrayList<>();
        List<EntityVocabItem> items = new ArrayList<>(vocabItems);
        try {
            for (List<EntityVocabItem> itemList : ListUtils.partition(items, EXECUTE_BATCH_SIZE)) {
                List<Keyword> keywords = mapper.selectKeywordByNameAndTypeBatch(itemList);
                if (CollectionUtils.isNotEmpty(keywords)){
                    res.addAll(keywords);
                }
            }
            return res;
        } catch (DataAccessException e) {
            LOGGER.error("get keywords by name and type failed", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
    }

    /**
     * 批量插入关键词
     *
     * @param keywords 关键词列表
     * @return 影响行数
     * @throws ServiceException ServiceException
     */
    public int addKeywordsBatch(List<Keyword> keywords) throws ServiceException {
        if (CollectionUtils.isEmpty(keywords)) {
            return 0;
        }
        KeywordMapper mapper = mapperFactory.getMapper(KeywordMapper.class);
        int count = 0;
        try {
            for (List<Keyword> keywordList : ListUtils.partition(keywords, EXECUTE_BATCH_SIZE)) {
                count += mapper.batchInsertKeywords(keywordList);
            }
        } catch (DataAccessException e) {
            LOGGER.error("addIncident failed:", e);
            throw ExceptionUtil.createException(ExceptionInternationalConstant.DATABASE_ERROR);
        }
        return count;
    }
}
