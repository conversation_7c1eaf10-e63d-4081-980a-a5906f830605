/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.impl;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.i2000.dvanalysisengineextservice.business.intent.AssociateTypeEnum;
import com.huawei.i2000.dvanalysisengineextservice.business.intent.RecommendHelper;
import com.huawei.i2000.dvanalysisengineextservice.business.intent.TemplatePotentialMatch;
import com.huawei.i2000.dvanalysisengineextservice.business.intent.TemplateRecognizer;
import com.huawei.i2000.dvanalysisengineextservice.delegate.KeywordAssociationDelegate;
import com.huawei.i2000.dvanalysisengineextservice.model.RecommendReq;
import com.huawei.i2000.dvanalysisengineextservice.model.RecommendRsp;
import com.huawei.i2000.dvanalysisengineextservice.util.ContextUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 用户输入联想
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
@Component
public class KeywordAssociationDelegateImpl implements KeywordAssociationDelegate {
    private static final OssLog LOGGER = OssLogFactory.getLogger(KeywordAssociationDelegateImpl.class);

    @Override
    public RecommendRsp recommend(HttpContext context, RecommendReq req) throws ServiceException {
        LOGGER.info("[recommend] invoked user question is={}, context is={}", req.getUserQuestion(), req.getContext());
        ContextUtils.init(context.getHttpServletRequest());
        RecommendRsp recommendRsp = new RecommendRsp();
        TemplateRecognizer templateRecognizer = TemplateRecognizer.getInstance(ContextUtils.getDefaultLocale());

        // 1. Recognize potential matches
        List<TemplatePotentialMatch> potentialMatches = templateRecognizer.recognize(req.getUserQuestion(), 5, 0.2);
        if (potentialMatches == null || potentialMatches.isEmpty()) {
            LOGGER.warn("[recommend] No potential matches found for question: {}", req.getUserQuestion());
            return recommendRsp;
        }

        // 3. Process each potential match
        for (int i = 0; i < potentialMatches.size(); i++) {
            processSinglePotentialMatch(potentialMatches.get(i), 10, templateRecognizer, recommendRsp);
        }

        // 4. Final sort
        finalSort(recommendRsp, req.getUserQuestion());

        LOGGER.info("[recommend] finished. Returning {} recommendations.", recommendRsp.getRecommendQuestions().size());
        return recommendRsp;
    }

    private void finalSort(RecommendRsp recommendRsp, String userQuestion) {
        List<String> questions = recommendRsp.getRecommendQuestions();

        if (questions == null || questions.isEmpty()) {
            return;
        }

        Comparator<String> similarityComparator = Comparator.comparingDouble(
                (String question) -> calculateSimilarityScore(userQuestion, question.substring(0, Math.min(question.length(), userQuestion.length())))).reversed();

        List<String> sortedQuestions = questions.stream()
            .sorted(similarityComparator)
            .limit(20)
            .collect(Collectors.toList());

        recommendRsp.setRecommendQuestions(sortedQuestions);
    }

    private double calculateSimilarityScore(String original, String recommended) {
        if (original == null || recommended == null || (original.isEmpty() && recommended.isEmpty())) {
            return 0.0;
        }
        if (original.equals(recommended)) {
            return 1.0;
        }

        int distance = TemplateRecognizer.calculateLevenshteinDistance(original, recommended);
        int maxLength = Math.max(original.length(), recommended.length());
        return 1.0 - (double) distance / maxLength;
    }

    private void processSinglePotentialMatch(TemplatePotentialMatch match, int weight, TemplateRecognizer recognizer,
        RecommendRsp recommendRsp) {

        // 1. Get suggestions for parameters
        Map<String, List<String>> parameterSuggestionsMap = getParameterSuggestions(match, weight,
            recognizer.getLocale());
        if (parameterSuggestionsMap.isEmpty()) {
            LOGGER.info("[recommend][debug] No parameter suggestions generated for potential match '{}'. Skipping.",
                match.getIntentName());
            return; // Skip if no parameters yielded suggestions
        }

        // 2. Calculate Cartesian Product of suggestions
        List<Map<String, String>> combinations = calculateCombinations(parameterSuggestionsMap);
        if (combinations.isEmpty()) {
            LOGGER.info("[recommend][debug] No combinations generated for potential match '{}'. Skipping.",
                match.getIntentName());
            return; // Skip if combinations calculation resulted in empty (e.g., an empty suggestion list)
        }

        // 3. Generate prompt strings from combinations
        List<String> generatedPrompts = generatePromptsFromCombinations(combinations, match.getIntentName(),
            recognizer);

        // 4. Add generated prompts to the response
        recommendRsp.getRecommendQuestions().addAll(generatedPrompts);
    }

    private Map<String, List<String>> getParameterSuggestions(TemplatePotentialMatch match, int weight, Locale locale) {
        Map<String, List<String>> parameterSuggestionsMap = new HashMap<>();
        Map<String, String> extractedParameters = match.getExtractedParameters();

        if (extractedParameters == null || extractedParameters.isEmpty()) {
            LOGGER.info("[recommend][debug] No extracted parameters found for match '{}'.", match.getIntentName());
            return parameterSuggestionsMap; // Return empty map
        }

        RecommendHelper recommendHelper = new RecommendHelper(locale);
        for (Map.Entry<String, String> entry : extractedParameters.entrySet()) {
            String paramName = entry.getKey();
            String extractedValue = entry.getValue();
            AssociateTypeEnum paramType;
            try {
                paramType = AssociateTypeEnum.valueOf(paramName);
            } catch (IllegalArgumentException e) {
                LOGGER.warn("[recommend] Unknown parameter type '{}' for potential match '{}'. Skipping parameter.",
                    paramName, match.getIntentName());
                continue; // Skip this parameter
            }

            List<String> suggestions = recommendHelper.getElementRecommendList(extractedValue, paramType, weight);

            if (suggestions != null && !suggestions.isEmpty()) {
                parameterSuggestionsMap.put(paramName, suggestions);
            }
        }
        return parameterSuggestionsMap;
    }

    private List<Map<String, String>> calculateCombinations(Map<String, List<String>> parameterSuggestionsMap) {
        List<Map<String, String>> combinations = new ArrayList<>();
        combinations.add(new HashMap<>()); // Start with an empty combination

        for (Map.Entry<String, List<String>> entry : parameterSuggestionsMap.entrySet()) {
            String paramName = entry.getKey();
            List<String> suggestionsList = entry.getValue();
            List<Map<String, String>> nextCombinations = new ArrayList<>();

            // Optimization: If any suggestion list is empty, the cartesian product is empty
            if (suggestionsList == null || suggestionsList.isEmpty()) {
                LOGGER.warn(
                    "[calculateCombinations] Empty suggestion list found for param '{}'. Resulting combinations will be empty.",
                    paramName);
                return Collections.emptyList(); // Return empty list immediately
            }

            for (Map<String, String> existingCombination : combinations) {
                for (String suggestion : suggestionsList) {
                    Map<String, String> newCombination = new HashMap<>(existingCombination);
                    newCombination.put(paramName, suggestion);
                    nextCombinations.add(newCombination);
                }
            }
            combinations = nextCombinations; // Update combinations for the next parameter
        }
        return combinations;
    }

    private List<String> generatePromptsFromCombinations(List<Map<String, String>> combinations, String intentName,
        TemplateRecognizer recognizer) {
        List<String> generatedPrompts = new ArrayList<>();
        LOGGER.info("[recommend][debug] Generating prompts for intent '{}' with {} combinations.", intentName,
            combinations.size());

        for (Map<String, String> combination : combinations) {
            String prompt = recognizer.generateInput(intentName, combination);
            if (prompt != null && !prompt.isEmpty()) {
                generatedPrompts.add(prompt);
            } else {
                LOGGER.warn("[recommend] generateInput returned null or empty for intent '{}' with params {}",
                    intentName, combination);
            }
        }
        return generatedPrompts;
    }

    /**
     * 将总和 10 按比例分配到与输入 double 数组大小相同的 int 数组中。
     * 输出 int 数组的和将为 10，每个 int 的值取决于对应 double 值的大小。
     *
     * @param input 输入的 double 数组。假定值都为非负数。
     * @return 输出的 int 数组，其和为 10。
     * @throws IllegalArgumentException 如果输入数组为空，或所有元素的和为 0。
     */
    public static int[] distributeToSum10(double[] input) {
        if (input == null || input.length == 0) {
            return new int[0];
        }

        int n = input.length;
        int[] result = new int[n];
        double[] idealValues = new double[n]; // 存储按比例计算出的理想值（可能是小数）
        final int targetSum = 10;

        // --- 1. 计算输入数组的总和 ---
        double totalSum = 0.0;
        for (double value : input) {
            if (value < 0) {
                value = 0;
            }
            totalSum += value;
        }

        // 如果总和为0（或非常接近0），无法按比例分配
        if (totalSum <= 1e-9) { // 使用容差处理浮点数精度问题
            throw new IllegalArgumentException("cannot work with array of all zero elements");
        }

        // --- 2. 计算每个元素的理想比例值 ---
        for (int i = 0; i < n; i++) {
            double value = (input[i] >= 0) ? input[i] : 0; // 再次确保非负
            double proportion = value / totalSum;
            idealValues[i] = proportion * targetSum;
        }

        // --- 3. 初步整数分配（向下取整）并计算当前和 ---
        int currentSum = 0;
        for (int i = 0; i < n; i++) {
            result[i] = (int) Math.floor(idealValues[i]);
            currentSum += result[i];
        }

        // --- 4. 计算需要补充的差额 ---
        int remainder = targetSum - currentSum;

        // --- 5. 分配差额 ---
        if (remainder > 0) {
            List<Integer> indices = IntStream.range(0, n).boxed().collect(Collectors.toList());

            // 根据 idealValues 的小数部分降序排序索引
            // 小数部分越大，说明它在向下取整时“损失”得越多，应该优先获得补偿
            indices.sort(Comparator.comparingDouble((Integer i) -> idealValues[i] - result[i]).reversed());

            for (int i = 0; i < remainder; i++) {
                int indexToIncrement = indices.get(i % n);
                result[indexToIncrement]++;
            }
        }

        return result;
    }
}

