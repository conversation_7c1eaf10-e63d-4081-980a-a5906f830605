/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow;

import com.huawei.bsp.commonlib.hofs.HofsException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cloudsop.common.tokenhelper.util.json.JsonUtil;
import com.huawei.i2000.cbb.security.zip.ZipUtils;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.download.BuildZipFileRequest;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.download.BuildZipFileResult;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.download.DownloadZipResult;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.download.ExecuteState;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.download.FlowFileDownloadProgressResult;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.download.FlowFileDownloadRequest;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.download.QueryZipProgressResult;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model.BasicInfo;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model.CreateTaskResponse;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model.Definition;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model.FiredFlow;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model.FlowExecuteStatus;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model.FlowInfo;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model.FlowModel;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model.FlowParameter;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model.FlowTask;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model.PageQueryFlowResponse;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model.PageQueryModel;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model.QueryFlowTaskRequest;
import com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model.StartFlowRequest;
import com.huawei.i2000.dvanalysisengineextservice.util.CommonUtils;
import com.huawei.i2000.dvanalysisengineextservice.util.HofsUtils;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.RestConstant;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.RestUtil;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;

import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 流程编排接口调用类
 *
 * <AUTHOR>
 * @since 2022/8/20
 */
public class FlowClient {
    private static final OssLog LOGGER = OssLogFactory.getLogger(FlowClient.class);
    private static final String DEFAULT_USER = "1";
    private static final String DOWNLOAD_FILE_PATH = System.getProperty("java.io.tmpdir") + File.separator;

    private static final String FLOW_RESULT_FILE_NAME = "report_0.zip";

    private static volatile FlowClient flowClient;

    private static final TypeReference<PageQueryFlowResponse> PAGE_QUERY_FLOW_TYPE_REFERENCE
        = new TypeReference<PageQueryFlowResponse>() { };

    private FlowClient() {
    }

    /**
     * 获取告警接口调用类
     *
     * @return 告警接口调用类
     */
    public static FlowClient getInstance() {
        if (flowClient == null) {
            synchronized (FlowClient.class) {
                if (flowClient == null) {
                    flowClient = new FlowClient();
                }
            }
        }
        return flowClient;
    }

    /**
     * 查询FlowInfo
     *
     * @param flowGroup flowGroup
     * @param flowName flowName
     * @return FlowInfo
     * @throws ServiceException 服务异常
     */
    public FlowInfo queryFlowInfo(String flowGroup, String flowName) throws ServiceException {
        FlowInfo flowInfo = new FlowInfo();
        RestfulResponse response;
        LOGGER.info("query flow, flowGroup is {}, flowName is {}", flowGroup, flowName);
        RestfulParametes restfulParametes = new RestfulParametes();
        PageQueryModel pageQueryModel = new PageQueryModel();
        pageQueryModel.setSolution(flowGroup);
        pageQueryModel.setSortKey("createDate");
        pageQueryModel.setSortMethod("desc");
        restfulParametes.setRawData(JSON.toJSONString(pageQueryModel));

        try {
            RestfulResponse restfulResponse = RestUtil.sendRestRequest(DEFAULT_USER, RestConstant.RESTFUL_METHOD_POST, FlowConstant.GET_FLOW_NAEM_URL, restfulParametes, null);
            if (restfulResponse == null || restfulResponse.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("queryFlowInfo response error");
                return flowInfo;
            }

            PageQueryFlowResponse pageQueryFlowResponse = JsonUtil.getMapper()
                .readValue(restfulResponse.getResponseContent(), PAGE_QUERY_FLOW_TYPE_REFERENCE);
            List<FiredFlow> flows = pageQueryFlowResponse.getFlows();
            List<BasicInfo> basicInfos = flows.stream().map(FiredFlow::getBasicInfo).collect(Collectors.toList());
            flowInfo.setFlowName(flowName);
            flowInfo.setFlowGroup(flowGroup);
            flowInfo.setFlowId(basicInfos.stream().filter(a -> a.getName().equals(flowName)).map(BasicInfo::getFlowId).findFirst().orElse(null));
            return flowInfo;
        } catch (JsonProcessingException e) {
            LOGGER.error("getFlowName error, e = {}", e.getMessage());
            return flowInfo;
        }
    }

    /**
     * 查询流程详情
     *
     * @param flowId 流程Id
     * @return FlowModel 流程详情
     */
    public FlowModel queryFlowDetail(String flowId) {
        FlowModel flowModel = null;
        RestfulResponse response;
        try {
            LOGGER.info("query flow, flowId is {}", flowId);
            String url = FlowConstant.QUERY_FLOW_DETAIL;
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(flowId);
            response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, url, restfulParametes, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("query flow detail response error");
                return new FlowModel();
            }

            flowModel = JSONObject.parseObject(response.getResponseContent(), FlowModel.class);
        } catch (ServiceException e) {
            LOGGER.error("query flow detail response error", e);
        }
        return flowModel;
    }

    /**
     * 初始化启动流程接口参数
     *
     * @param flowInfo 流程信息
     * @param flowParameters 流程参数
     * @return 启动流程参数
     */
    public StartFlowRequest initStartFlowRequest(FlowInfo flowInfo, JSONObject flowParameters) {
        StartFlowRequest request = new StartFlowRequest();
        FlowModel flowModel = FlowClient.getInstance().queryFlowDetail(flowInfo.getFlowId());
        if (flowModel == null || flowModel.getDefinition() == null) {
            LOGGER.error("[initStartFlowRequest] flowModel is empty or definition is empty.");
            return request;
        }
        Definition definition = flowModel.getDefinition();
        // 将采集的参数传入请求中
        setFlowParameters(flowModel.getDefinition(), flowParameters);
        request.setDefinition(definition);
        request.setFlowGroup(flowInfo.getFlowGroup());
        request.setFlowName(flowInfo.getFlowName());
        return request;
    }

    private void setFlowParameters(Definition definition, JSONObject flowParameters) {
        // 根据默认 填充空值
        List<FlowParameter> modelFlowParameter = definition.getParas();
        List<FlowParameter> defaultParas = definition.getDefaultParas();
        Map<String, String> valueMap = new HashMap<>();
        for (FlowParameter flowParameter: defaultParas) {
            valueMap.put(flowParameter.getName(), flowParameter.getValue());
        }
        modelFlowParameter.forEach(param -> {
            String paramValue = flowParameters.getString(param.getName());
            if (StringUtils.isEmpty(paramValue)) {
                LOGGER.debug("[setFlowParameters] the query information is empty: {}", param.getName());
                paramValue = valueMap.get(param.getName());
            }
            param.setValue(paramValue);
        });
    }

    /**
     * 启动流程
     *
     * @param request 启动流程入参
     * @return FlowModel 流程详情
     */
    public CreateTaskResponse startFlow(StartFlowRequest request) {
        CreateTaskResponse createTaskResponse = new CreateTaskResponse();
        RestfulResponse response;
        try {
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(JSONObject.toJSONString(request));
            response = RestUtil.sendRestRequest(DEFAULT_USER, RestConstant.RESTFUL_METHOD_POST, FlowConstant.START_FLOW,
                restfulParametes, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                if (response != null) {
                    LOGGER.error("[startFlow] the response status is {}, response content is {}", response.getStatus(),
                        response.getResponseContent());
                } else {
                    LOGGER.error("[startFlow] start flow error, the response is null");
                }
                return new CreateTaskResponse();
            }

            createTaskResponse = JSONObject.parseObject(response.getResponseContent(),
                CreateTaskResponse.class);

        } catch (ServiceException e) {
            LOGGER.error("start flow error, e = {}", e);
        }
        return createTaskResponse;
    }


    /**
     * 初始化查询流程执行任务
     *
     * @param flowTaskId 流程执行Id
     * @return 流程执行任务参数
     */
    public QueryFlowTaskRequest initQueryFlowTaskRequest(String flowTaskId) {
        QueryFlowTaskRequest request = new QueryFlowTaskRequest();
        request.setFlowInsId(flowTaskId);
        return request;
    }

    /**
     * 查询flow结果
     *
     * @param flowTaskRequest flowTaskRequest
     * @return FlowTask
     */
    public FlowTask getFlowTaskResult(QueryFlowTaskRequest flowTaskRequest) {
        int retryCount = 0;
        final int maxRetryTime = 30;
        final long retryInterval = 30 * 1000L;
        while(retryCount < maxRetryTime) {
            FlowTask flowTask = queryFlowTask(flowTaskRequest);
            if (!flowTask.getStatus().equals(FlowExecuteStatus.EXECUTING) && !flowTask.getStatus().equals(FlowExecuteStatus.NOT_STARTED)) {
                return flowTask;
            }
            LOGGER.info("[getFlowTaskResult] flow status is :{}, retry", flowTask.getStatus());
            retryCount++;
            if (retryCount < maxRetryTime) {
                try {
                    Thread.sleep(retryInterval);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt() ;
                    LOGGER.error("[getFlowTaskResult] retry wait interrupted", e);
                    break;
                }
            }
        }
        LOGGER.error("[getFlowTaskResult] all {} attempts failed for taskId {}", maxRetryTime, flowTaskRequest.getFlowInsId());
        return null;
    }


    /**
     * 查询流程执行任务
     *
     * @param request request
     * @return FlowTask flowTask
     */
    public FlowTask queryFlowTask(QueryFlowTaskRequest request) {
        FlowTask flowTask = new FlowTask();
        RestfulResponse response;
        try {
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(JSON.toJSONString(request));
            response = RestUtil.sendRestRequest(DEFAULT_USER, RestConstant.RESTFUL_METHOD_POST, FlowConstant.QUERY_FLOW_TASK,
                restfulParametes, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("query flow task error");
                return new FlowTask();
            }

            flowTask = JSONObject.parseObject(response.getResponseContent(), FlowTask.class);
        } catch (ServiceException e) {
            LOGGER.error("query flow task error, e = {}", e);
        }
        return flowTask;
    }

    /**
     * 下载任务结果文件
     *
     * @param flowInsId flowInsId
     * @return 任务结果文件路径
     * @throws ServiceException 服务异常
     */
    public String downloadTaskResultFile(String flowInsId) throws ServiceException {
        String filePath = "";
        // 请求下载文件
        if (requestDownloadFlowFile(flowInsId)) {
            // 查询下载文件进度
            ExecuteState state = queryDownloadFlowFileProgress(flowInsId);
            if (ExecuteState.SUCCESS.equals(state)) {
                // 打包
                String zipUUId = requestBuildZipFile(flowInsId);
                if (StringUtils.isEmpty(zipUUId)) {
                    LOGGER.error("[downloadTaskResultFile] failed startBuildZipFile. flowInsId {}", flowInsId);
                    return filePath;
                }
                // 查询打包进度
                if(isZipBuildSuccess(zipUUId)) {
                    // 下载文件包
                    String hofsfilePath = downloadZip(flowInsId, zipUUId);
                    // 从hofs下载文件
                    filePath = downloadResultFile(hofsfilePath);
                }
            } else {
                LOGGER.error("downloadTaskResultFile failed. status: {}", state);
            }
        }
        return filePath;
    }

    private String downloadResultFile(String workbenchHofsPath) throws ServiceException {
        if (StringUtils.isEmpty(workbenchHofsPath)) {
            throw new ServiceException("workbenchHofsPath is empty");
        }
        try {
            String fileName = "AlarmDiagnose_"+ System.currentTimeMillis() + ".zip";
            String localFilePath = DOWNLOAD_FILE_PATH + fileName;
            HofsUtils.downloadFileOrDirToSpecified(workbenchHofsPath, localFilePath);
            String unzipPath = DOWNLOAD_FILE_PATH + CommonUtils.getUUID();
            File unzipDir = new File(unzipPath);
            if (!unzipDir.exists() && !unzipDir.mkdirs()) {
                LOGGER.error("create unzip directory failed");
                throw new ServiceException("create unzip directory failed");
            }
            ZipUtils.getInst().unzipSingle(localFilePath, unzipPath, true);
            String zipFilePath = unzipPath + File.separator + FLOW_RESULT_FILE_NAME;
            ZipUtils.getInst().unzipAll(zipFilePath, unzipPath, true);
            return unzipPath;
        } catch (HofsException e) {
            LOGGER.error("download file failed", e);
            throw new ServiceException("download file failed");
        } catch (Exception e) {
            LOGGER.error("get file content failed", e);
            throw new ServiceException(e.getMessage());
        } finally {
            HofsUtils.removeRemoteFile(workbenchHofsPath, false);
            HofsUtils.removeRemoteFile(workbenchHofsPath + ".SHA", false);
        }
    }

    private String downloadZip(String flowInsId, String zipUUId) {
        RestfulResponse response;
        try {
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(zipUUId);
            response = RestUtil.sendRestRequest(DEFAULT_USER, RestConstant.RESTFUL_METHOD_POST, FlowConstant.DOWNLOAD_RESULT_FILE,
                restfulParametes, null);

            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("downloadZip error, response:{}", response);
                return "";
            }
            DownloadZipResult result = JSON.parseObject(response.getResponseContent(), DownloadZipResult.class);
            if (ObjectUtils.isNotEmpty(result)) {
                return result.getFilePath();
            }
        } catch (ServiceException e) {
            LOGGER.error("Query flow task error, flowInsId = {}, zipUUId = {}", flowInsId, zipUUId, e);
        }

        return "";
    }

    /**
     * 查询flow文件下载进度
     *
     * @param flowInsId flowInsId
     * @return 下载状态
     */
    public ExecuteState queryDownloadFlowFileProgress(String flowInsId) {
        // 最大重试次数和重试间隔(毫秒)
        final int maxRetryTime = 30;
        final long retryInterval = 1000L;

        FlowFileDownloadRequest flowFileDownloadRequest = new FlowFileDownloadRequest(flowInsId);
        int retryCount = 0;

        while (retryCount < maxRetryTime) {
            try {
                RestfulParametes restfulParametes = new RestfulParametes();
                restfulParametes.setRawData(JSON.toJSONString(flowFileDownloadRequest));
                RestfulResponse response = RestUtil.sendRestRequest(DEFAULT_USER, RestConstant.RESTFUL_METHOD_POST,
                    FlowConstant.QUERY_DOWNLOAD_FLOW_FILE_PROGRESS, restfulParametes, null);

                if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                    LOGGER.error("response error, {}", response);
                    retryCount++;
                    sleepWithoutInterrupt(retryInterval);
                    continue;
                }
                FlowFileDownloadProgressResult result = JSONObject.parseObject(
                    response.getResponseContent(), FlowFileDownloadProgressResult.class);

                // 检查状态是否仍为进行中
                if (result.getStatus() != ExecuteState.IN_PROGRESS) {
                    return result.getStatus();  // 直接返回最终状态
                }
                retryCount++;
                LOGGER.info("queryDownloadFlowFileProgress continue, retryCount: {}", retryCount);
                sleepWithoutInterrupt(retryInterval);
            } catch (ServiceException e) {
                LOGGER.error("queryDownloadFlowFileProgress error : {}", e);
                retryCount++;
            }
        }

        LOGGER.error("queryDownloadFlowFileProgress request timeout");
        return ExecuteState.TIMEOUT;
    }

    private void sleepWithoutInterrupt(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            LOGGER.warn("sleepWithoutInterrupt error: {}", e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 请求下载文件
     *
     * @param flowInsId flowInsId
     * @return 是否请求成功
     */
    public boolean requestDownloadFlowFile(String flowInsId) {
        FlowFileDownloadRequest flowFileDownloadRequest = new FlowFileDownloadRequest(flowInsId);
        RestfulResponse response;
        try {
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(JSON.toJSONString(flowFileDownloadRequest));
            response = RestUtil.sendRestRequest(DEFAULT_USER, RestConstant.RESTFUL_METHOD_POST, FlowConstant.REQUEST_DOWNLOAD_FLOW_FILE,
                restfulParametes, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("requestDownloadFlowFile error, response:{}", response);
                return false;
            }
        } catch (ServiceException e) {
            LOGGER.error("query flow task error, e = {}", e);
        }
        return true;
    }

    /**
     * 请求构建结果zip包
     *
     * @param flowInsId flowInsId
     * @return zipUUId
     */
    public String requestBuildZipFile(String flowInsId) {
        String zipUUId = "";
        BuildZipFileRequest buildZipFileRequest = new BuildZipFileRequest(flowInsId);
        RestfulResponse response;
        try {
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(JSON.toJSONString(buildZipFileRequest));
            response = RestUtil.sendRestRequest(DEFAULT_USER, RestConstant.RESTFUL_METHOD_POST, FlowConstant.REQUEST_START_BUILD_ZIP_FILE,
                restfulParametes, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("query flow task error");
                return zipUUId;
            }
            BuildZipFileResult result = JSON.parseObject(response.getResponseContent(), BuildZipFileResult.class);
            if (ObjectUtils.isNotEmpty(result)) {
                return result.getPackId();
            }
        } catch (ServiceException e) {
            LOGGER.error("query flow task error, e = {}", e);
        }
        return zipUUId;
    }

    /**
     * zip结果包是否构建完成
     *
     * @param zipUUId zipUUId
     * @return zip结果包是否构建完成
     */
    public boolean isZipBuildSuccess(String zipUUId) {
        // 最大重试次数和重试间隔(毫秒)
        final int maxRetryTime = 30;
        final long retryInterval = 1000L;
        int retryCount = 0;

        while (retryCount < maxRetryTime) {
            try {
                RestfulParametes restfulParametes = new RestfulParametes();
                restfulParametes.setRawData(JSON.toJSONString(zipUUId));
                RestfulResponse response = RestUtil.sendRestRequest(DEFAULT_USER, RestConstant.RESTFUL_METHOD_POST, FlowConstant.QUERY_ZIP_PROGRESS,
                    restfulParametes, null);
                if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                    LOGGER.error("query flow task error");
                    return false;
                }
                QueryZipProgressResult result = JSONObject.parseObject(
                    response.getResponseContent(), QueryZipProgressResult.class);

                // 检查状态是否仍为进行中
                if (StringUtils.isNotEmpty(result.getZipPath())) {
                    return true;
                }
                retryCount++;
                LOGGER.info("isZipBuildSuccess continue, retryCount: {}", retryCount);
                sleepWithoutInterrupt(retryInterval);
            } catch (ServiceException e) {
                LOGGER.error("isZipBuildSuccess error : {}", e);
                retryCount++;
            }
        }

        LOGGER.error("isZipBuildSuccess request timeout");
        return false;
    }
}
