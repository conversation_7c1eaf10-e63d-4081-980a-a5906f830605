/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.workflow.flow.model;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.Min;

/**
 * 功能描述：FiredFlow
 *
 * <AUTHOR>
 * @since 2022
 */
@Getter
@NoArgsConstructor
public class FiredFlow {
    /**
     * Basic info
     */
    @Setter
    @Valid
    private BasicInfo basicInfo;

    /**
     * Status
     */
    @Setter
    private FlowStatus status = FlowStatus.NOT_STARTED;

    /**
     * Latest fired time
     */
    @Setter
    private String latestFiredTime;

    /**
     * Allow manual
     */
    @Setter
    private boolean allowManual;

    /**
     * Trigger type
     */
    @Setter
    private String triggerType;

    /**
     * Available
     */
    @Setter
    @Min(value = 0)
    private int available;

    /**
     * Result
     */
    @Setter
    private String result;

    /**
     * Can trigger manual
     */
    @Setter
    @Min(value = 0)
    private Integer canTriggerManual;

    /**
     * Content
     */
    @Setter
    private String content;

    @Override
    public String toString() {
        return "FiredFlow{" + "basicInfo=" + basicInfo + ", status=" + status + ", latestFiredTime='" + latestFiredTime
            + '\'' + ", allowManual=" + allowManual + ", triggerType='" + triggerType + '\'' + ", available="
            + available + ", result='" + result + '\'' + ", canTriggerManual=" + canTriggerManual + ", content='"
            + content + '\'' + '}';
    }
}
