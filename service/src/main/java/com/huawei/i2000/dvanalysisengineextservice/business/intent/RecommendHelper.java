/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.intent;

import com.alibaba.fastjson2.JSON;
import com.huawei.bsp.as.util.PagingIterator;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.cbb.fm.model.QFilterElement;
import com.huawei.i2000.cbb.fm.model.QSortField;
import com.huawei.i2000.cbb.fm.model.QueryContext;
import com.huawei.i2000.cbb.fm.model.ScrollQueryResult;
import com.huawei.i2000.dvanalysisengineextservice.business.pm.MeasTypeTreeBuilder;
import com.huawei.i2000.dvanalysisengineextservice.business.pm.SearchScopeEnum;
import com.huawei.i2000.dvanalysisengineextservice.model.KeywordSearchRequest;
import com.huawei.i2000.dvanalysisengineextservice.model.KeywordSearchResponse;
import com.huawei.i2000.dvanalysisengineextservice.model.KeywordItem;
import com.huawei.i2000.dvanalysisengineextservice.model.MeasObjectInfo;
import com.huawei.i2000.dvanalysisengineextservice.model.MeasObjectVO;
import com.huawei.i2000.dvanalysisengineextservice.model.MeasType4MV;
import com.huawei.i2000.dvanalysisengineextservice.model.MeasUnit4MV;
import com.huawei.i2000.dvanalysisengineextservice.util.ApplicationContextHelper;
import com.huawei.i2000.dvanalysisengineextservice.util.ContextUtils;
import com.huawei.i2000.dvanalysisengineextservice.business.alarm.AlarmQueryParam;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.RestConstant;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.RestUtil;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.oms.eam.mo.ManagedObject;
import com.huawei.oms.persistence.Criterion;
import com.huawei.oms.persistence.CriterionFactory;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * RecommendHelper
 *
 * <AUTHOR>
 * @since 2025/04/14
 */
public class RecommendHelper {
    private static final OssLog LOGGER = OssLogFactory.getLogger(RecommendHelper.class);

    private static final List<String> ALARM_FIELDS = Arrays.asList("alarmId", "alarmName", "probableCause",
        "nativeMoDn", "moi", "occurUtc", "severity", "eventType", "meName", "deviceTypeId", "cleared", "productName",
        "clearTime", "count", "occurrences");

    private static final String GET_ALARM_INFO_URL = "/rest/fault/v1/current-alarms";

    private static final String GET_CURRENT_ALARM_URL = "/rest/fault/v1/current-alarms/scroll";

    public static final int ALARM_SEARCH_SIZE = 1000;

    private final Map<String, String> evidence;

    private List<MeasUnit4MV> measUnitTree;

    private static final Set<AssociateTypeEnum> DO_NOT_MATCH = Arrays.stream(new AssociateTypeEnum[] {
        AssociateTypeEnum.MEASURE_METRIC_NAME,
        AssociateTypeEnum.MEASURE_OBJECT_NAME,
        AssociateTypeEnum.MEASURE_TIME_RANGE
    }).collect(Collectors.toSet());

    @Getter
    private final Locale locale;

    public RecommendHelper(Locale locale) {
        evidence = new HashMap<>();
        this.locale = locale;
    }

    private static volatile MeasTypeTreeBuilder measTypeTreeBuilder;

    public static synchronized MeasTypeTreeBuilder getMeasTypeTreeBuilder() {
        if (measTypeTreeBuilder == null) {
            try {
                measTypeTreeBuilder = (MeasTypeTreeBuilder) ApplicationContextHelper.getBean("measTypeTreeBuilder");
            } catch (ServiceException e) {
                LOGGER.error("get measTypeTreeBuilder bean failed.", e);
            }
        }
        return measTypeTreeBuilder;
    }

    // Helper method to get a localized string
    private String getLocalizedMessage(String key) {
        return ResourceUtil.getMessage(key, locale);
    }

    public List<String> getElementRecommendList(String input, AssociateTypeEnum associateType) {
        return getElementRecommendList(input, associateType, 10);
    }

    public List<String> getElementRecommendList(String input, AssociateTypeEnum associateType, int maxSize) {
        if (StringUtils.isEmpty(input) && !DO_NOT_MATCH.contains(associateType)) {
            return Collections.singletonList(
                getLocalizedMessage("recommend.bracket.left") + associateType
                    + getLocalizedMessage("recommend.bracket.right"));
        }

        // Check if input is a Chinese localized placeholder (surrounded by 【】)
        // If so, preserve it as-is to maintain proper Chinese localization
        if (StringUtils.isNotEmpty(input) && isChineseLocalizedPlaceholder(input)) {
            return Collections.singletonList(input);
        }

        switch (associateType) {
            case NE_NAME:
                return getNeNameRecommendList(input, maxSize);
            case ERROR_CODE:
                return getErrCodeRecommendList(input, maxSize);
            case ALARM_ID_OR_NAME:
                return getAlarmIdNameRecommendList(input, maxSize);
            case QUERY_NAME:
                return getQueryNameRecommendList(input, maxSize);
            case EXPORT_NAME:
                return getExportNameRecommendList(input, maxSize);
            case MEASURE_TASK_NAME:
                return getMeasTaskNameRecommendList(input, maxSize);
            case MEASURE_OBJECT_NAME:
                return getMeasObectNameRecommendList(input, maxSize);
            case MEASURE_METRIC_NAME:
                return getMeasMetricNameRecommendList(input, maxSize);
            case MEASURE_TIME_RANGE:
                return getMeasTimeRangeRecommendList(input, maxSize);
            default:
                break;
        }
        return Collections.singletonList(input);
    }

    /**
     * Check if the input is a Chinese localized placeholder.
     * Chinese placeholders are surrounded by Chinese brackets 【】 and typically contain
     * descriptive text like "告警ID/名称", "网元名称", etc.
     *
     * @param input the input string to check
     * @return true if the input is a Chinese localized placeholder, false otherwise
     */
    private boolean isChineseLocalizedPlaceholder(String input) {
        if (input == null || input.length() < 3) {
            return false;
        }

        return input.startsWith("【") && input.endsWith("】");
    }

    private List<String> getQueryNameRecommendList(String input, int maxSize) {
        return Stream.of(
                "query.name.biz_resource_management",
                "query.name.physical_resource_management",
                "query.name.north_ftp_user_guide",
                "query.name.north_snmp_alarm_guide",
                "query.name.north_settings",
                "query.name.snmp_interface",
                "query.name.online_help",
                "query.name.current_alarms",
                "query.name.history_alarms"
            )
            .map(this::getLocalizedMessage)
            .filter(str -> str.contains(input))
            .limit(maxSize)
            .collect(Collectors.toList());
    }

    private List<String> getExportNameRecommendList(String input, int maxSize) {
        return Stream.of(
                "export.name.current_alarms",
                "export.name.network_element_info"
            )
            .map(this::getLocalizedMessage)
            .filter(str -> str.contains(input))
            .limit(maxSize)
            .collect(Collectors.toList());
    }

    private List<String> getMeasTimeRangeRecommendList(String input, int maxSize) {
        Stream<String> initialStream = Stream.of(
            input,
            getLocalizedMessage("meas.time.range.last_hour"),
            getLocalizedMessage("meas.time.range.last_day"),
            getLocalizedMessage("meas.time.range.last_week"),
            getLocalizedMessage("meas.time.range.last_month")
        );

        Stream<String> resultStream;

        if (StringUtils.isNotEmpty(input)) {
            resultStream = initialStream.filter(str -> str != null && str.contains(input));
        } else {
            resultStream = initialStream.filter(StringUtils::isNotEmpty);
        }

        return resultStream
            .limit(maxSize)
            .collect(Collectors.toList());
    }

    private List<String> getNeNameRecommendList(String input, int maxSize) {
        List<String> res = new ArrayList<>();
        List<ManagedObject> candidateMo = new ArrayList<>();

        Criterion criterion = CriterionFactory.createCriterion();
        criterion.descend("name").constrain("%" + input + "%").like();
        // 分页查询，避免oom异常
        PagingIterator<ManagedObject> it = null;
        do {
            try {
                it = MITManagerClient.newInstance().bulkGet(criterion, 1000, it);
            } catch (OSSException e) {
                LOGGER.error("failed to bulk get mo", e);
                return res;
            }
            List<ManagedObject> itRes = it.getResult();
            if (CollectionUtils.isEmpty(itRes)) {
                LOGGER.error("page result is empty");
            }
            candidateMo.addAll(itRes);
        } while (it.hasNext() && candidateMo.size() < maxSize);

        // record evidence if meet exact match
        if (!candidateMo.isEmpty()) {
            ManagedObject firstMatch = candidateMo.get(0);
            evidence.put(AssociateEvidenceTypeEnum.DN.name(), firstMatch.getDN().getValue());
            evidence.put(AssociateEvidenceTypeEnum.MO_TYPE.name(), firstMatch.getType());
        }
        res.addAll(candidateMo.stream().map(ManagedObject::getName).collect(Collectors.toList()));

        if (CollectionUtils.isEmpty(res)) {
            return Collections.singletonList(input);
        }
        return res.subList(0, Math.min(res.size(), maxSize));
    }

    private List<String> getErrCodeRecommendList(String input, int maxSize) {
        try {
            // 构建请求参数
            KeywordSearchRequest request = new KeywordSearchRequest();
            request.setKeywordType(KeywordSearchRequest.KeywordTypeEnum.ERROR_CODE);
            request.setInput(input);
            request.setMaxSize(maxSize);

            // 发送REST请求
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(JSON.toJSONString(request));
            RestfulResponse rsp = RestUtil.sendRestRequest(ContextUtils.getContext().getHttpServletRequest(),
                    RestConstant.RESTFUL_METHOD_POST, RestConstant.Url.KEYWORD_SEARCH_URL, restfulParametes, null);

            if (rsp == null || rsp.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("Keyword search request failed, status code: {}", rsp != null ? rsp.getStatus() : "null");
                return Collections.singletonList(input);
            }

            // 解析响应
            KeywordSearchResponse response = JSON.parseObject(rsp.getResponseContent(), KeywordSearchResponse.class);
            if (response == null || response.getResultCode() != 0) {
                LOGGER.error("Keyword search response error, resultCode: {}, message: {}",
                        response != null ? response.getResultCode() : "null",
                        response != null ? response.getResultMessage() : "null");
                return Collections.singletonList(input);
            }

            // 提取关键词列表
            if (response.getData() != null && response.getData().getKeywords() != null) {
                List<String> keywords = response.getData().getKeywords().stream()
                        .map(KeywordItem::getName)
                        .collect(Collectors.toList());

                if (!keywords.isEmpty()) {
                    LOGGER.info("Successfully retrieved {} error code keywords", keywords.size());
                    return keywords;
                }
            }

            LOGGER.warn("No matching error code keywords found");
            return Collections.singletonList(input);

        } catch (Exception e) {
            LOGGER.error("Exception occurred while calling keyword search interface", e);
            return Collections.singletonList(input);
        }
    }

    private List<String> getAlarmIdNameRecommendList(String input, int maxSize) {
        String prefix = "ALM-";
        boolean hasAlm = input.startsWith(prefix);
        if (hasAlm) {
            input = input.substring(prefix.length());
        }
        List<Map<String, String>> hits;
        try {
            hits = getScrollQueryResult(ALARM_SEARCH_SIZE);
        } catch (ServiceException e) {
            LOGGER.error("fail to query current alarm list", e);
            return Collections.singletonList(input);
        }
        Set<String> candidate = new HashSet<>();
        for (Map<String, String> hit : hits) {
            if (hasAlm) {
                candidate.add(prefix + hit.get("alarmId"));
            } else {
                candidate.add(hit.get("alarmId"));
            }
            candidate.add(hit.get("alarmName"));
        }
        String finalInput = input;
        List<String> res = candidate.stream()
            .filter(str -> str.contains(finalInput))
            .limit(maxSize)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(candidate)) {
            return Collections.singletonList(input);
        }
        return res;
    }

    private List<String> getMeasTaskNameRecommendList(String input, int maxSize) {
        if (!initPmInfo(input, SearchScopeEnum.UNIT)) {
            return Collections.singletonList(input);
        }

        List<String> filteredCandidates = measUnitTree.stream()
            .map(MeasUnit4MV::getName)
            .filter(name -> name.contains(input))
            .collect(Collectors.toList());

        String evidenceValue = null;
        if (filteredCandidates.size() == 1) {
            evidenceValue = filteredCandidates.get(0);
        } else if (input != null) {
            evidenceValue = filteredCandidates.stream()
                .filter(name -> Objects.equals(name, input))
                .findFirst()
                .orElse(null);
        }

        if (evidenceValue != null) {
            evidence.put(AssociateEvidenceTypeEnum.MEAS_TASK.name(), evidenceValue);
            return Collections.singletonList(evidenceValue);
        }
        Set<String> res = filteredCandidates.stream().limit(maxSize).collect(Collectors.toSet());

        if (CollectionUtils.isNotEmpty(res)) {
            return new ArrayList<>(res);
        }
        return Collections.singletonList(input);
    }

    private List<String> getMeasObectNameRecommendList(String input, int maxSize) {
        if (!evidence.containsKey(AssociateEvidenceTypeEnum.MEAS_TASK.name())) {
            if (StringUtils.isNotEmpty(input)) {
                return Collections.singletonList(input);
            }
            return Collections.singletonList(
                getLocalizedMessage("recommend.bracket.left") + AssociateTypeEnum.MEASURE_OBJECT_NAME
                    + getLocalizedMessage("recommend.bracket.right"));
        }

        MeasObjectInfo measObjectInfo = null;
        try {
            measObjectInfo = getMeasTypeTreeBuilder().getObjectInfoNew(
                evidence.get(AssociateEvidenceTypeEnum.DN.name()),
                evidence.get(AssociateEvidenceTypeEnum.MEAS_TASK.name()));
        } catch (ServiceException e) {
            LOGGER.error(e.getMessage());
        }

        if (measObjectInfo == null) {
            return Collections.singletonList("NA");
        }

        Set<String> candidate = measObjectInfo.getMos()
            .stream()
            .map(MeasObjectVO::getDisplayValue)
            .limit(maxSize)
            .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(candidate)) {
            return Collections.singletonList("NA");
        }

        if (StringUtils.isNotEmpty(input)) {
            candidate = candidate.stream().filter(str -> str.contains(input)).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(candidate)) {
                return Collections.singletonList(input);
            }
        }

        return new ArrayList<>(candidate);
    }

    private List<String> getMeasMetricNameRecommendList(String input, int maxSize) {
        Set<String> res;
        if (initPmInfo(input, SearchScopeEnum.TYPE)) {
            res = measUnitTree.stream()
                .flatMap(unit -> unit.getChildren().stream().map(MeasType4MV::getName))
                .limit(maxSize)
                .collect(Collectors.toSet());
        } else if (StringUtils.isEmpty(evidence.get(AssociateEvidenceTypeEnum.MEAS_TASK.name()))) {
            if (StringUtils.isNotEmpty(input)) {
                return Collections.singletonList(input);
            }
            return Collections.singletonList(
                getLocalizedMessage("recommend.bracket.left") + AssociateTypeEnum.MEASURE_METRIC_NAME
                    + getLocalizedMessage("recommend.bracket.right"));
        } else {
            res = measUnitTree.stream()
                .filter(unit -> unit.getName().equals(evidence.get(AssociateEvidenceTypeEnum.MEAS_TASK.name())))
                .limit(1)
                .flatMap(unit -> unit.getChildren().stream().map(MeasType4MV::getName))
                .limit(maxSize)
                .collect(Collectors.toSet());
        }
        if (!CollectionUtils.isEmpty(res)) {
            if (StringUtils.isNotEmpty(input)) {
                res = res.stream().filter(str -> str.contains(input)).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(res)) {
                    return Collections.singletonList(input);
                }
            }
            return new ArrayList<>(res);
        }
        return Collections.singletonList(input);
    }

    private List<Map<String, String>> getScrollQueryResult(int alarmSearchSize) throws ServiceException {
        List<Map<String, String>> alarmData = new ArrayList<>();
        RestfulParametes restfulParametes = new RestfulParametes();
        AlarmQueryParam scrollQueryParam = buildScrollQueryParam(alarmSearchSize, true);
        String param = JSON.toJSONString(scrollQueryParam);
        restfulParametes.setRawData(param);
        RestfulResponse rsp = RestUtil.sendRestRequest(ContextUtils.getContext().getHttpServletRequest(),
            RestConstant.RESTFUL_METHOD_POST, GET_CURRENT_ALARM_URL, restfulParametes, null);
        if (rsp == null || rsp.getStatus() != RestConstant.STATUS_OK) {
            LOGGER.error("query response error");
            throw new ServiceException("query response error");
        }
        ScrollQueryResult scrollQueryResult = JSON.parseObject(rsp.getResponseContent(), ScrollQueryResult.class);
        while (StringUtils.isNotEmpty(scrollQueryResult.getIterator()) || CollectionUtils.isNotEmpty(
            scrollQueryResult.getHits())) {
            alarmData.addAll(scrollQueryResult.getHits());
            scrollQueryParam.setIterator(scrollQueryResult.getIterator());
            LOGGER.debug("[queryAlarm] iterator={}, size={}", scrollQueryResult.getIterator(),
                scrollQueryResult.getHits().size());
            param = JSON.toJSONString(scrollQueryParam);
            restfulParametes.setRawData(param);
            rsp = RestUtil.sendRestRequest(ContextUtils.getContext().getHttpServletRequest(),
                RestConstant.RESTFUL_METHOD_POST, GET_CURRENT_ALARM_URL, restfulParametes, null);
            if (rsp == null || rsp.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("query response error");
                throw new ServiceException("query response error");
            }
            scrollQueryResult = JSON.parseObject(rsp.getResponseContent(), ScrollQueryResult.class);
            if (scrollQueryResult == null) {
                LOGGER.error("[queryAlarm] getAlarmData scrollQueryResult is null");
                break;
            }
        }
        LOGGER.info("query response resCode= {}", scrollQueryResult.getResCode());
        return alarmData;
    }

    private AlarmQueryParam buildScrollQueryParam(int searchSize, boolean isCurrentAlarm) {
        AlarmQueryParam scrollQueryParam = new AlarmQueryParam();
        scrollQueryParam.setSize(searchSize);
        scrollQueryParam.setFields(ALARM_FIELDS);
        QueryContext queryContext = new QueryContext();
        String field = "severity";
        QFilterElement filterElement = new QFilterElement();
        filterElement.setName(field);
        filterElement.setField(field);
        filterElement.setOperator("in");
        filterElement.setValues(Arrays.asList("1", "2", "3", "4", "5", "6"));
        List<QFilterElement> elementList = new ArrayList<>();
        elementList.add(filterElement);

        if (isCurrentAlarm) {
            String fieldCleared = "merged";
            QFilterElement filterClearedElement = new QFilterElement();
            filterClearedElement.setName(fieldCleared);
            filterClearedElement.setField(fieldCleared);
            filterClearedElement.setOperator("in");
            filterClearedElement.setValues(Collections.singletonList("1"));
            elementList.add(filterClearedElement);
        }
        queryContext.setFilters(elementList);
        QSortField severitySortField = new QSortField();
        severitySortField.setField("severity");
        QSortField utcSortField = new QSortField();
        utcSortField.setField("occurUtc");
        scrollQueryParam.setSort(Arrays.asList(severitySortField, utcSortField));
        scrollQueryParam.setQuery(queryContext);
        return scrollQueryParam;
    }

    private boolean initPmInfo(String input, SearchScopeEnum searchScope) {
        if (!CollectionUtils.isEmpty(measUnitTree)) {
            return false;
        }
        if (evidence.containsKey(AssociateEvidenceTypeEnum.DN.name())) {
            measUnitTree = getMeasTypeTreeBuilder().getMeasUnitTree(
                evidence.get(AssociateEvidenceTypeEnum.MO_TYPE.name()), "*",
                evidence.get(AssociateEvidenceTypeEnum.DN.name()), input, searchScope);
            return true;
        }
        return false;
    }
}