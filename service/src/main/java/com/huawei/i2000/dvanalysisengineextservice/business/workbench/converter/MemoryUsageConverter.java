/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.business.workbench.converter;

import com.alibaba.fastjson2.JSON;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.dvanalysisengineextservice.business.managedobject.dto.CheckListAnswer;
import com.huawei.i2000.dvanalysisengineextservice.business.managedobject.dto.RenderMultiAnswer;
import com.huawei.i2000.dvanalysisengineextservice.business.recipe.RecipeConstant;
import com.huawei.i2000.dvanalysisengineextservice.business.workbench.vo.MemoryUsageVo;
import com.huawei.i2000.dvanalysisengineextservice.constants.DisplayConstant;
import com.huawei.i2000.dvanalysisengineextservice.model.DownloadFileRsp;
import com.huawei.i2000.dvanalysisengineextservice.util.ContextUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/3/28
 */
@Component
public class MemoryUsageConverter implements Converter {
    private static final Logger LOGGER = LoggerFactory.getLogger(MemoryUsageConverter.class);

    @Override
    public Object convert(DownloadFileRsp src, int resultSize, String extAttrs) {
        Map<String, List<MemoryUsageVo>> data = src.getFileContent().entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry ->
                getDataList(entry.getValue(), resultSize), (oldValue, newValue) -> oldValue));
        return createRenderMultiAnswer(data, resultSize, extAttrs);
    }

    @Override
    public String getCheckListTitle(String... args) {
        return ResourceUtil.getMessage(DisplayConstant.MEM_USAGE_TITLE, args, ContextUtils.getDefaultLocale());
    }

    private List<MemoryUsageVo> getDataList(List<String> lines, int resultSize) {
        TopCommandUtils topCommandUtils = new TopCommandUtils();
        Map<String, String> pidNames = topCommandUtils.initAndGetPidInfo(lines);

        Set<MemoryUsageVo> result = new HashSet<>();
        for (String line : lines) {
            if (TopCommandUtils.PID_TITLE_FLAG.equals(line)) {
                break;
            }
            int userIndex = topCommandUtils.getUserIndex();
            int memIndex = topCommandUtils.getMemIndex();
            String[] split = line.split(" +");
            if (split.length <= Math.max(userIndex, memIndex)) {
                LOGGER.error("top res split error, msg is {}", line);
                continue;
            }
            String pid = split[0].trim();
            if (!pidNames.containsKey(pid)) {
                continue;
            }
            MemoryUsageVo memoryUsageVo = new MemoryUsageVo();
            memoryUsageVo.setUser(split[userIndex].trim());
            memoryUsageVo.setUsage(split[memIndex].trim() + "%");
            memoryUsageVo.setPid(pid + "(" + pidNames.get(pid) + ")");
            result.add(memoryUsageVo);
        }
        return result.stream().sorted((o1, o2) -> {
            double v1 = Double.parseDouble(o1.getUsage().substring(0, o1.getUsage().length() - 1));
            double v2 = Double.parseDouble(o2.getUsage().substring(0, o2.getUsage().length() - 1));
            return Double.compare(v2, v1);
        }).limit(resultSize).collect(Collectors.toList());
    }


    private List<RenderMultiAnswer> createRenderMultiAnswer(Map<String, List<MemoryUsageVo>> data, int resultSize, String extAttrs) {
        List<RenderMultiAnswer> answers = new ArrayList<>();
        CheckListAnswer checkListAnswer = new CheckListAnswer();
        Locale locale = ContextUtils.getContext().getLocale();
        String sourceName = "Memory";
        if (locale.getLanguage().equalsIgnoreCase("zh")) {
            sourceName = "内存";
        }
        data.entrySet().stream().limit(1).forEach(entry -> {
            checkListAnswer.setStatus(-1);
            checkListAnswer.setName(getCheckListTitle(entry.getKey(), String.valueOf(resultSize)));
            checkListAnswer.setDetails(JSON.toJSONString(entry.getValue().stream().map(MemoryUsageVo::toDisplay).collect(Collectors.toList())));
        });
        checkListAnswer.setExtAttrs(ConvertUtils.getReportCheckItem(extAttrs, sourceName));
        answers.add(new RenderMultiAnswer(RecipeConstant.LUI_TYPE_CHECKLIST, Collections.singletonList(checkListAnswer)));
        return answers;
    }
}
