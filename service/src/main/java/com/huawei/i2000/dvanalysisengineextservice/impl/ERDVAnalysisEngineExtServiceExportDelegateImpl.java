/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvanalysisengineextservice.impl;

import com.alibaba.fastjson2.JSON;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.cbb.security.zip.ZipUtils;
import com.huawei.i2000.dvanalysisengineextservice.business.export.pmkpi.PMResult;
import com.huawei.i2000.dvanalysisengineextservice.business.export.pmkpi.PageResult;
import com.huawei.i2000.dvanalysisengineextservice.business.export.pmkpi.PmKpiValues;
import com.huawei.i2000.dvanalysisengineextservice.business.workbench.WorkbenchHandler;
import com.huawei.i2000.dvanalysisengineextservice.cluster.CatalogInfoModel;
import com.huawei.i2000.dvanalysisengineextservice.cluster.KpiDataDiagnosisView;
import com.huawei.i2000.dvanalysisengineextservice.cluster.MeasObjCatalogModel;
import com.huawei.i2000.dvanalysisengineextservice.cluster.MeasTypeKeyCatalogModel;
import com.huawei.i2000.dvanalysisengineextservice.constants.DataExportConstants;
import com.huawei.i2000.dvanalysisengineextservice.constants.DisplayConstant;
import com.huawei.i2000.dvanalysisengineextservice.delegate.ERDVAnalysisEngineExtServiceExportDelegate;
import com.huawei.i2000.dvanalysisengineextservice.model.HistoryAggregateQueryModel;
import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineextservice.util.CommonUtils;
import com.huawei.i2000.dvanalysisengineextservice.util.ContextUtils;
import com.huawei.i2000.dvanalysisengineextservice.util.CsvUtil;
import com.huawei.i2000.dvanalysisengineextservice.util.FileUtil;
import com.huawei.i2000.dvanalysisengineextservice.util.I18nBuildUtils;
import com.huawei.i2000.dvanalysisengineextservice.util.resource.EamUtil;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.ResponseResultHelper;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.RestConstant;
import com.huawei.i2000.dvanalysisengineextservice.util.rest.RestUtil;
import com.huawei.oms.eam.mo.AgentNode;
import com.huawei.oms.eam.mo.ConnectStatus;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.ManagedElement;
import com.huawei.oms.eam.mo.ManagedObject;
import com.huawei.oms.eam.mo.NodeObject;
import com.huawei.oms.persistence.Criterion;
import com.huawei.oms.persistence.CriterionFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.hc.core5.concurrent.DefaultThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2025/1/9
 */
@Component

public class ERDVAnalysisEngineExtServiceExportDelegateImpl implements ERDVAnalysisEngineExtServiceExportDelegate {
    private static final OssLog LOGGER = OssLogFactory.getLogger(ERDVAnalysisEngineExtServiceExportDelegateImpl.class);
    public static final int MAX_QUERY_SIZE = 1000;

    @Autowired
    private WorkbenchHandler workbenchHandler;

    private static final List<String> ALARM_FIELDS = Arrays.asList("alarmId", "alarmName", "probableCause",
        "nativeMoDn", "moi", "occurUtc", "severity", "eventType", "meName", "deviceTypeId", "cleared", "productName",
        "clearTime", "count");

    private static final List<String> MANAGED_OBJECT_EXPORT_ITEM = Arrays.asList("dn", "type", "version", "displayName",
        "medNodeID", "clientProperties", "children", "parent", "neStatus");

    /**
     * 导入文件上传文件临时存放文件夹
     */
    private static final String EXPORT_DIR = DataExportConstants.TMP_FILE_PATH_PREFIX + "/temp/agentExport/";

    private static final String EXPORT_CONTENT_TYPE = "application/" + DataExportConstants.FILE_FORMAT + ";charset="
        + DataExportConstants.ENCODE_UTF8;

    // 当前告警信息查询接口
    private static final String GET_CURRENT_ALARM_URL = "/rest/fault/v1/current-alarms/scroll";

    // 性能服务获取历史数据的接口
    private static final String PM_KPI_URL = "/rest/dvpmservice/v1/service/monitor/history/datas";

    private static final String QUERY_CATALOG_INFO_BY_TASK_NAME = "/rest/dvpmservice/v1/service/agent/catalogInfo/by_task_name";

    private static final String QUERY_ALL_PM_TASK_2_PERIOD_LIST
        = "/rest/dvpmservice/v1/service/agent/taskId2period/by_mo_type";

    private static final List<String> PM_KPI_HEADER = new ArrayList<>(
        Arrays.asList("dn", "timestamp", "date", "period", "measObjects", "displayValue"));

    private static final Integer MAX_DATA_LENGTH = 30000;

    private static final String MO_EXPORT_FILE_NAME = "AllManagedObject";
    private static final String ERROR_INFO = "error";
    private static final String SPACE = ",";

    private static final String UNDERLINE = "_";

    private static final String SEMICOLON = ";";

    private static final String COLON = ":";

    private final ExecutorService thresholdPool = new ThreadPoolExecutor(0, 5, 1, TimeUnit.MINUTES,
        new LinkedBlockingQueue<>(1), new DefaultThreadFactory("pm_kpi_export"));

    // 定义路径非法字符的正则表达式
    private static final String ILLEGAL_CHAR_REGEX = "[<>/\\\\\\\\|?*\\\"',.!@#$%^&\\\\(\\\\) ]";

    private List<List<String>> constructData(List<Map<String, String>> values, List<String> items) {
        List<List<String>> result = new ArrayList<>();
        values.forEach(value -> {
            List<String> row = new ArrayList<>();
            items.forEach(field -> {
                row.add(value.getOrDefault(field, StringUtils.EMPTY));
            });
            result.add(row);
        });
        return result;
    }

    private static void deleteTempFile(String path) {
        // 下载完成之后删除文件
        try {
            FileUtils.deleteDirectory(new File(path));
        } catch (IOException e) {
            LOGGER.error("Fail to delete file.");
        }
    }

    @Override
    public ResponseResult exportManagedObject(HttpContext context, String connectStatus) throws ServiceException {
        // 当前不查询全部，只查1000条
        List<ManagedObject> managedObjectList = null;
        try {
            Criterion criterion = CriterionFactory.createCriterion();
            if (StringUtils.isNotEmpty(connectStatus)) {
                criterion.descend("connectStatus").constrain(ConnectStatus.valueOf(connectStatus)).equal();
            }
            managedObjectList = EamUtil.getMoListByCriterion(criterion);
        } catch (OSSException e) {
            LOGGER.error("query mo from eamcore failed", e);
            return ResponseResultHelper.failResponseEntity("query mo from eamcore failed");
        }
        List<Map<String, String>> values = managedObjectList.stream().filter(Objects::nonNull).map(managedObject -> {
            Map<String, String> moAttribute = new HashMap<>();
            moAttribute.put("dn", managedObject.getDN().getValue());
            moAttribute.put("type", managedObject.getType());
            moAttribute.put("version", managedObject.getVersion());
            moAttribute.put("displayName", managedObject.getDisplayName());
            moAttribute.put("medNodeID", managedObject.getMedNodeID());
            moAttribute.put("clientProperties", managedObject.getClientProperties().toString());
            moAttribute.put("children", managedObject.children()
                .stream()
                .filter(Objects::nonNull)
                .map(DN::getValue)
                .collect(Collectors.joining()));
            moAttribute.put("parent", managedObject.getParent().getValue());
            moAttribute.put("neStatus", getNeStatus(managedObject));
            return moAttribute;
        }).collect(Collectors.toList());
        List<List<String>> alarmData = constructData(values, MANAGED_OBJECT_EXPORT_ITEM);
        String path = EXPORT_DIR + CommonUtils.getUUID();
        String filePath = path + File.separator + MO_EXPORT_FILE_NAME + DisplayConstant.TIME_4_FILE_NAME_FORMATTER.format(Instant.now()) + ".csv";
        if (StringUtils.isNotEmpty(connectStatus)) {
            filePath = filePath.replace(MO_EXPORT_FILE_NAME,  MO_EXPORT_FILE_NAME + "-" + connectStatus + "-");
        }
        return exportData(context, path, filePath, alarmData,
            I18nBuildUtils.getI18n(MANAGED_OBJECT_EXPORT_ITEM, DisplayConstant.MO_EXPORT_COLUMN_PREFIX));
    }

    @Override
    public ResponseResult exportPmKpi(HttpContext context, String neName, String taskName, String neType, String measTypeKey,
        String measObjects, String timeRange) throws ServiceException {
        ManagedObject managedObject = EamUtil.getMoByNeName(neName);
        if (null == managedObject || (StringUtils.isNotEmpty(neType) && !neType.equals(managedObject.getType()))) {
            return ResponseResultHelper.failResponseEntity(
                ResourceUtil.getMessage(DisplayConstant.NE_NOT_FOUND, new Object[] {neName},
                    ContextUtils.getDefaultLocale()));
        }
        List<HistoryAggregateQueryModel> historyDataQueryModels;

        // 查询任务名下指标国际化信息
        CatalogInfoModel catalogInfoModel = queryIndicatorInfo(context, taskName, managedObject);

        if (catalogInfoModel == null) {
            LOGGER.error("query il8n failed.");
            return ResponseResultHelper.failResponseEntity(
                ResourceUtil.getMessage(DisplayConstant.TASK_NOT_FOUND, new Object[] {neName, taskName},
                    ContextUtils.getDefaultLocale()));
        }

        historyDataQueryModels = buildQueryParam(measObjects, measTypeKey, timeRange, managedObject, catalogInfoModel);

        if (CollectionUtils.isEmpty(historyDataQueryModels)) {
            return ResponseResultHelper.failResponseEntity(ResourceUtil.getMessage(DisplayConstant.INDICATOR_NOT_FOUND,
                new Object[] {neName, taskName, measTypeKey}, ContextUtils.getDefaultLocale()));
        }
        List<Map<String, String>> kpiValues = queryKpi(context, historyDataQueryModels, new KpiDataDiagnosisView());
        if (CollectionUtils.isEmpty(kpiValues)) {
            LOGGER.error("query kpi failed");
            return ResponseResultHelper.failResponseEntity(
                ResourceUtil.getMessage(DisplayConstant.TASK_NO_DATA, ContextUtils.getDefaultLocale()));
        }
        HashSet<String> tmpHeader = new HashSet<>(kpiValues.get(0).keySet());
        PM_KPI_HEADER.forEach(tmpHeader::remove);
        List<String> filePaths = getKpiDataPath(taskName, timeRange, catalogInfoModel, kpiValues, tmpHeader);

        return exportZip(context, filePaths);
    }

    private List<String> getKpiDataPath(String taskName, String timeRange, CatalogInfoModel catalogInfoModel,
        List<Map<String, String>> kpiValues, HashSet<String> tmpHeader) {
        List<String> timeRangeList = new ArrayList<>();
        String[] timeRangeArray = timeRange.split(SPACE);
        for (int i = 0; i < timeRangeArray.length / 2; i++) {
            timeRangeList.add(
                DisplayConstant.DATE_TIME_FORMATTER.format(Instant.ofEpochMilli(Long.parseLong(timeRangeArray[2 * i])))
                    + "-" + DisplayConstant.DATE_TIME_FORMATTER.format(
                    Instant.ofEpochMilli(Long.parseLong(timeRangeArray[2 * i + 1]))));
        }

        if (CollectionUtils.isEmpty(timeRangeList)) {
            List<String> timestampList = kpiValues.stream()
                .map(v -> v.get("timestamp"))
                .sorted()
                .collect(Collectors.toList());
            long startTime = Long.parseLong(timestampList.get(0));
            long endTime = Long.parseLong(timestampList.get(timestampList.size() - 1));
            timeRangeList.add(
                DisplayConstant.DATE_TIME_FORMATTER.format(Instant.ofEpochMilli(startTime))
                    + "-" + DisplayConstant.DATE_TIME_FORMATTER.format(Instant.ofEpochMilli(endTime)));
        }

        return getKpiDataSheetLocale(tmpHeader, kpiValues, taskName, timeRangeList, catalogInfoModel);
    }

    private List<String> getKpiDataSheetLocale(HashSet<String> tmpHeader,
        List<Map<String, String>> kpiValues, String taskName, List<String> timeRangeList, CatalogInfoModel catalogInfoModel) {
        List<String> filePaths = new ArrayList<>();
        List<String> measTypeKeysNameList = new ArrayList<>();
        Set<String> measObjectsNameList = kpiValues.stream()
            .map(v -> v.get("displayValue"))
            .collect(Collectors.toSet());
        List<String> items = new ArrayList<>(tmpHeader);
        items.forEach(row -> {
            genMeasTypeKeyName(catalogInfoModel, measTypeKeysNameList, row);
        });

        String path = EXPORT_DIR + CommonUtils.getUUID();
        try {
            FileUtils.forceMkdir(new File(path));
        } catch (IOException e) {
            LOGGER.error("mkdir failed");
        }

        Pattern pattern = Pattern.compile(ILLEGAL_CHAR_REGEX);

        measObjectsNameList.forEach(measObject -> {
            StringBuilder viewNameSb = new StringBuilder();
            viewNameSb.append(taskName)
                .append(UNDERLINE)
                .append(String.join(SEMICOLON, measTypeKeysNameList))
                .append("(")
                .append(String.join(SEMICOLON, timeRangeList))
                .append(")");
            String viewName = viewNameSb.toString();
            List<String> csvHeader = Arrays.asList(
                ResourceUtil.getMessage(DisplayConstant.VIEW_NAME, ContextUtils.getDefaultLocale()), viewName);
            String measObjectsName = genMeasObjectName(catalogInfoModel, measObject);

            List<Map<String, String>> kpiValuesWithMeasObject = kpiValues.stream()
                .filter(v -> measObject == null? v.get("displayValue") == null : measObject.equals(v.get("displayValue")))
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(kpiValuesWithMeasObject)) {
                LOGGER.error("kpi data is empty in {}", measObjectsName);
                return;
            }

            List<List<String>> kpiDataSheet = getKpiDataSheet(kpiValuesWithMeasObject, taskName, measTypeKeysNameList, items, measObjectsName);

            if (CollectionUtils.isEmpty(kpiDataSheet)) {
                LOGGER.error("kpi data is empty with measTypeKeys in {}", measObjectsName);
                return;
            }

            // 将数据写入 CSV 文件
            String csvFilePath = path + File.separator + "PerformanceKPI" + UNDERLINE + pattern.matcher(measObjectsName)
                .replaceAll("_") + UNDERLINE + DisplayConstant.TIME_4_FILE_NAME_FORMATTER.format(Instant.now())
                + ".csv";
            if (writeFile(csvFilePath, csvHeader, kpiDataSheet)) {
                filePaths.add(csvFilePath);
            }
        });

        return filePaths;
    }

    private List<List<String>> getKpiDataSheet(List<Map<String, String>> kpiValues, String taskName, List<String> measTypeKeysNameList,
        List<String> items, String measObjectsName) {
        List<List<String>> kpiDataSheet = new ArrayList<>();
        List<String> newItems = new ArrayList<>(Collections.singletonList("date"));
        List<String> newMeasTypeKeysNameList = new ArrayList<>();
        for (int i = 0; i < items.size(); i++) {
            String displayKey = items.get(i);
            if (kpiValues.stream().anyMatch(v -> StringUtils.isNotEmpty(v.get(displayKey)))) {
                newItems.add(displayKey);
                newMeasTypeKeysNameList.add(measTypeKeysNameList.get(i));
            }
        }
        // 导出文件第一列为date，第二列往后为指标数据，小于两列说明无数据
        if (newItems.size() < 2) {
            return Collections.emptyList();
        }

        // 增加前几行基本信息，网元，测量对象和指标
        List<String> moNameList = new ArrayList<>(Collections.singletonList(
            ResourceUtil.getMessage(DisplayConstant.MANAGED_OBJECT, ContextUtils.getDefaultLocale())));
        List<String> measObjectList = new ArrayList<>(Collections.singletonList(
            ResourceUtil.getMessage(DisplayConstant.MEASUREMENT_OBJECT, ContextUtils.getDefaultLocale())));
        List<String> measTypeKeyList = new ArrayList<>(Collections.singletonList(
            ResourceUtil.getMessage(DisplayConstant.PERFORMANCE_COUNTER, ContextUtils.getDefaultLocale())));
        List<String> dataSourceList = new ArrayList<>(Collections.singletonList(
            ResourceUtil.getMessage(DisplayConstant.DATA_SOURCE, ContextUtils.getDefaultLocale())));

        for (String measTypeKeysName : newMeasTypeKeysNameList) {
            moNameList.add(taskName);
            measObjectList.add(measObjectsName);
            measTypeKeyList.add(measTypeKeysName);
            dataSourceList.add(taskName + COLON + measObjectsName + COLON + measTypeKeysName);
        }

        kpiDataSheet.add(moNameList);
        kpiDataSheet.add(measObjectList);
        kpiDataSheet.add(measTypeKeyList);
        kpiDataSheet.add(dataSourceList);

        kpiDataSheet.addAll(constructData(kpiValues, newItems));

        return kpiDataSheet;
    }

    private static String genMeasObjectName(CatalogInfoModel catalogInfoModel, String measObject) {
        String measObjectsName = StringUtils.EMPTY;
        if (StringUtils.isEmpty(measObject)) {
            return measObjectsName;
        }
        if (Locale.US.equals(ContextUtils.getDefaultLocale())) {
            measObjectsName = catalogInfoModel.getMeasObjects()
                .stream()
                .filter(v -> measObject.equals(v.getDisplayValue()))
                .map(MeasObjCatalogModel::getDisplayValueEn)
                .findFirst()
                .orElse(StringUtils.EMPTY);
        } else {
            measObjectsName = catalogInfoModel.getMeasObjects()
                .stream()
                .filter(v -> measObject.equals(v.getDisplayValue()))
                .map(MeasObjCatalogModel::getDisplayValueZh)
                .findFirst()
                .orElse(StringUtils.EMPTY);
        }
        return measObjectsName;
    }

    private static void genMeasTypeKeyName(CatalogInfoModel catalogInfoModel, List<String> measTypeKeysNameList, String row) {
        if (Locale.US.equals(ContextUtils.getDefaultLocale())) {
            measTypeKeysNameList.add(catalogInfoModel.getMeasTypeKeys()
                .stream()
                .filter(v -> row.equals(v.getMeasTypeKey()))
                .map(MeasTypeKeyCatalogModel::getColumnNameEn)
                .findFirst()
                .orElse(StringUtils.EMPTY));
        } else {
            measTypeKeysNameList.add(catalogInfoModel.getMeasTypeKeys()
                .stream()
                .filter(v -> row.equals(v.getMeasTypeKey()))
                .map(MeasTypeKeyCatalogModel::getColumnNameZh)
                .findFirst()
                .orElse(StringUtils.EMPTY));
        }
    }

    private ResponseResult exportZip(HttpContext context, List<String> srcFiles) {
        HttpServletResponse response = context.getHttpServletResponse();
        // 导出文件
        String fileName = "pm_all_data_" + DisplayConstant.TIME_4_FILE_NAME_FORMATTER.format(Instant.now()) + ".zip";
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        String path = EXPORT_DIR + CommonUtils.getUUID();
        String target = path + File.separator + fileName;
        try {
            FileUtils.forceMkdir(new File(path));
            ZipUtils.getInst().zip(srcFiles, target, true);
        } catch (Throwable e) {
            LOGGER.error("mkdir failed", e);
            return ResponseResultHelper.failResponseEntity("mkdir failed");
        }

        // 导出文件
        try (OutputStream outputStream = response.getOutputStream();
            FileInputStream fileInputStream = new FileInputStream(target)) {
            FileUtils.forceMkdir(new File(path));
            // 把文件列表压缩成zip文件

            LOGGER.info("Output file size is {}", fileInputStream.available());
            response.setHeader("Content-length", String.valueOf(fileInputStream.available()));
            int len;
            byte[] buffer = new byte[1024];
            while ((len = fileInputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len);
            }
            outputStream.flush();
        } catch (Throwable e) {
            LOGGER.error("export metaData error", e);
        } finally {
            deleteTempFile(path);
        }
        return ResponseResultHelper.correctResponseMessage("successfully export data, please download it");
    }

    private static boolean writeFile(String filePath, List<String> items, List<List<String>> data) {
        try (FileOutputStream fileOutputStream = new FileOutputStream(filePath)) {
            // 写UTF-8 BOM码
            FileUtil.writeBomFlagToFile(fileOutputStream);
            CsvUtil.writeLine(items, fileOutputStream);
            if (CollectionUtils.isEmpty(data)) {
                LOGGER.warn("query mateData info failed");
                CsvUtil.writeLine(Collections.singletonList("No data by selected time"), fileOutputStream);
            } else {
                for (List<String> dataList : data) {
                    CsvUtil.writeLine(dataList, fileOutputStream);
                }
            }
            return true;
        } catch (Exception e) {
            LOGGER.error("export mateData fail", e);
            return false;
        }
    }

    public List<Map<String, String>> queryKpi(HttpContext context, List<HistoryAggregateQueryModel> historyDataQueryModels, KpiDataDiagnosisView kpiDataDiagnosisView) {
        Set<Map<String, String>> kpiValues = new HashSet<>();
        for (HistoryAggregateQueryModel historyAggregateQueryModel: historyDataQueryModels) {
            queryKpi(context, historyAggregateQueryModel, kpiValues, 1);
        }
        if (kpiValues.size() > MAX_DATA_LENGTH) {
            // 选取30000条数据，选取规则根据Map中的timestamp从大往小选择
            kpiDataDiagnosisView.setTips(ResourceUtil.getMessage(DisplayConstant.QUERY_KPI_OVERSIZE, ContextUtils.getDefaultLocale()));
        }
        return kpiValues.stream()
            .sorted((o1, o2) -> o2.get("timestamp").compareTo(o1.get("timestamp")))
            .limit(MAX_DATA_LENGTH)
            .collect(Collectors.toList());
    }

    public void queryKpi(HttpContext context, HistoryAggregateQueryModel historyAggregateQueryModel,
        Set<Map<String, String>> kpiValues, int pageIndex) {
        try {
            historyAggregateQueryModel.setPageIndex(BigDecimal.valueOf(pageIndex));
            PageResult pageResult = getPageResults(context, historyAggregateQueryModel);
            if (pageResult == null) {
                return;
            }
            List<PmKpiValues> datas = pageResult.getDatas();
            if (CollectionUtils.isEmpty(datas)) {
                LOGGER.error("query kpi failed");
                return;
            }
            kpiValues.addAll(parseKpi2List(datas));
            if (pageResult.getTotalCount() >= Math.multiplyExact(pageResult.getPageIndex(), pageResult.getPageSize())) {
                queryKpi(context, historyAggregateQueryModel, kpiValues, pageIndex + 1);
            }
        } catch (Throwable e) {
            LOGGER.warn("query data failed, query model is {}", historyAggregateQueryModel);
        }
    }

    private static PageResult getPageResults(HttpContext context,
        HistoryAggregateQueryModel historyAggregateQueryModel) throws ServiceException {
        RestfulParametes restfulParametes = new RestfulParametes();
        restfulParametes.setRawData(JSON.toJSONString(historyAggregateQueryModel));
        RestfulResponse rsp = RestUtil.sendRestRequest(context.getHttpServletRequest(),
            RestConstant.RESTFUL_METHOD_POST, PM_KPI_URL, restfulParametes, null);
        if (rsp == null || rsp.getStatus() != RestConstant.STATUS_OK) {
            LOGGER.error("query measUnitKey failed");
            return null;
        }
        PMResult pmResult = JSON.parseObject(rsp.getResponseContent(), PMResult.class);
        if (null == pmResult) {
            LOGGER.error("query kpi failed");
            return null;
        }
        if (pmResult.getResultCode() != 0) {
            return null;
        }
        return pmResult.getResult();
    }

    private List<Map<String, String>> parseKpi2List(List<PmKpiValues> datas) {
        List<Map<String, String>> kpiValues = new ArrayList<>();
        datas.forEach(date -> {
            Map<String, String> kpiValue = date.getValues();
            kpiValue.put("dn", date.getDn());
            kpiValue.put("timestamp", String.valueOf(date.getTimestamp()));
            kpiValue.put("date", DisplayConstant.DATE_TIME_FORMATTER.format(Instant.ofEpochMilli(date.getTimestamp())));
            kpiValue.put("period", String.valueOf(date.getPeriod()));
            kpiValue.put("measObjects", JSON.toJSONString(date.getMeasObjects()));
            kpiValue.put("displayValue", date.getDisplayValue());
            kpiValue.putAll(date.getValues());
            kpiValues.add(kpiValue);
        });
        return kpiValues;
    }

    public CatalogInfoModel queryIndicatorInfo(HttpContext context, String taskName, ManagedObject managedObject) throws ServiceException {
        RestfulParametes restfulParametes = new RestfulParametes();
        List<String> taskNameList = Stream.of(taskName).collect(Collectors.toList());
        restfulParametes.setRawData(JSON.toJSONString(taskNameList));
        RestfulResponse rsp = RestUtil.sendRestRequest(context.getHttpServletRequest(), RestConstant.RESTFUL_METHOD_POST,
            QUERY_CATALOG_INFO_BY_TASK_NAME, restfulParametes, null);
        if (rsp == null || rsp.getStatus() != RestConstant.STATUS_OK) {
            LOGGER.error("query il8n failed, taskName is {}", taskName);
            return null;
        }

        String result = JSON.parseObject(rsp.getResponseContent(), Map.class).get("result").toString();
        if (StringUtils.isEmpty(result) || !JSON.parseObject(result, Map.class).containsKey(taskName)) {
            return null;
        }

        List<CatalogInfoModel> catalogInfoModels = JSON.parseArray(
            JSON.parseObject(result, Map.class).get(taskName).toString(), CatalogInfoModel.class);

        if (CollectionUtils.isEmpty(catalogInfoModels)) {
            return null;
        }

        return catalogInfoModels.stream()
            .filter(v -> managedObject.getType().equals(v.getMoType()))
            .findFirst()
            .orElse(null);
    }

    public List<HistoryAggregateQueryModel> buildQueryParam(String measObjects, String measTypeKey, String timeRange,
        ManagedObject managedObject, CatalogInfoModel catalogInfoModel) throws ServiceException {
        List<HistoryAggregateQueryModel> historyDataQueryModels = new ArrayList<>();

        if (Objects.isNull(managedObject.getType())) {
            return historyDataQueryModels;
        }

        HistoryAggregateQueryModel historyAggregateQueryModel = getHistoryDataQueryModel(measObjects, measTypeKey, managedObject,
            catalogInfoModel);

        historyAggregateQueryModel.setPageSize(new BigDecimal(3000));
        historyAggregateQueryModel.setAutoLowerDimension(true);

        return setHistoryDataQueryModelTime(timeRange, historyDataQueryModels, historyAggregateQueryModel);
    }

    private static List<HistoryAggregateQueryModel> setHistoryDataQueryModelTime(String timeRange,
        List<HistoryAggregateQueryModel> historyDataQueryModels, HistoryAggregateQueryModel historyAggregateQueryModel) {
        if (StringUtils.isEmpty(timeRange)) {
            // 默认查询1小时
            historyAggregateQueryModel.setBeginTimestamp(System.currentTimeMillis() - 60L * 1000 * 60);
            historyAggregateQueryModel.setEndTimestamp(System.currentTimeMillis());
            historyAggregateQueryModel.setTimeRange4LowerDimension(Arrays.asList(historyAggregateQueryModel.getBeginTimestamp(), historyAggregateQueryModel.getEndTimestamp()));
            historyDataQueryModels.add(historyAggregateQueryModel);
            return historyDataQueryModels;
        }

        String[] timeRangeArray = timeRange.split(SPACE);
        for (int i = 0; i < timeRangeArray.length / 2; i++) {
            HistoryAggregateQueryModel newModel = JSON.parseObject(JSON.toJSONString(historyAggregateQueryModel), HistoryAggregateQueryModel.class);
            newModel.setBeginTimestamp(Long.parseLong(timeRangeArray[2 * i]));
            newModel.setEndTimestamp(Long.parseLong(timeRangeArray[2 * i + 1]));
            newModel.setTimeRange4LowerDimension(Arrays.asList(newModel.getBeginTimestamp(), newModel.getEndTimestamp()));
            historyDataQueryModels.add(newModel);
        }

        return historyDataQueryModels;
    }

    private static HistoryAggregateQueryModel getHistoryDataQueryModel(String measObjects, String measTypeKey, ManagedObject managedObject,
        CatalogInfoModel catalogInfoModel) {
        HistoryAggregateQueryModel historyAggregateQueryModel = new HistoryAggregateQueryModel();
        historyAggregateQueryModel.setMoType(managedObject.getType());
        historyAggregateQueryModel.setMeasUnitKey(catalogInfoModel.getMeasUnitKey());
        historyAggregateQueryModel.setDns(Collections.singletonList(managedObject.getDN().getValue()));

        // 如果测量对象和测量指标都为空，则查全部
        if (StringUtils.isEmpty(measObjects) && StringUtils.isEmpty(measTypeKey)) {
            return historyAggregateQueryModel;
        }

        // 测量对象
        String originalValue = StringUtils.EMPTY;
        String displayValue = StringUtils.EMPTY;
        if (StringUtils.isNotEmpty(measObjects)) {
            Optional<MeasObjCatalogModel> measObjectUnique = catalogInfoModel.getMeasObjects()
                .stream()
                .filter(v -> measObjects.equals(v.getDisplayValueZh()) || measObjects.equals(v.getDisplayValueEn()))
                .findFirst();
            if (measObjectUnique.isPresent()) {
                originalValue = measObjectUnique.get().getOriginalValue();
                displayValue = measObjectUnique.get().getDisplayValue();
            } else {
                LOGGER.error("query measObjects failed, measObjects is {}", measObjects);
                return new HistoryAggregateQueryModel();
            }
        }
        historyAggregateQueryModel.setMeasObjects(Collections.singletonList(displayValue));
        historyAggregateQueryModel.setOriginalValues(Collections.singletonList(originalValue));

        // 测量指标
        if (StringUtils.isEmpty(measTypeKey)) {
            return historyAggregateQueryModel;
        }

        Optional<MeasTypeKeyCatalogModel> measTypeKeyUnique = catalogInfoModel.getMeasTypeKeys()
            .stream()
            .filter(v -> measTypeKey.equals(v.getColumnNameEn()) || measTypeKey.equals(v.getColumnNameZh()))
            .findFirst();

        if (measTypeKeyUnique.isPresent()) {
            historyAggregateQueryModel.setSpecifiedMeasTypes(Collections.singletonList(measTypeKeyUnique.get().getMeasTypeKey()));
        } else {
            LOGGER.error("query measUnitKey failed, measTypeKey is {}", measTypeKey);
            return new HistoryAggregateQueryModel();
        }
        return historyAggregateQueryModel;
    }

    private static ResponseResult exportData(HttpContext context, String path, String filePath,
        List<List<String>> alarmData, List<String> item) {

        try {
            FileUtils.forceMkdir(new File(path));
        } catch (IOException e) {
            LOGGER.error("mkdir failed");
            return ResponseResultHelper.failResponseEntity("export failed");
        }
        writeFile(filePath, item, alarmData);

        HttpServletResponse response = context.getHttpServletResponse();
        // 设置内容类型
        response.setContentType(EXPORT_CONTENT_TYPE);
        String fileName = filePath.substring(path.length() + 1);
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        // 导出文件
        try (OutputStream outputStream = response.getOutputStream();
            FileInputStream fileInputStream = new FileInputStream(filePath)) {
            LOGGER.info("Output file size is {}", fileInputStream.available());
            response.setHeader("Content-length", String.valueOf(fileInputStream.available()));
            int len;
            byte[] buffer = new byte[1024];
            while ((len = fileInputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len);
            }
            outputStream.flush();
        } catch (Throwable e) {
            LOGGER.error("export alarm error", e);
            return ResponseResultHelper.failResponseEntity("export failed");
        } finally {
            deleteTempFile(path);
        }
        return ResponseResultHelper.correctResponseMessage("successfully export data, please download it");
    }

    /**
     * 获取网元状态
     *
     * @param mo 网元实例
     * @return int 网元状态
     */
    private String getNeStatus(ManagedObject mo) {
        if (mo instanceof ManagedElement) {
            return ((ManagedElement) mo).getConnectStatus().name();
        }
        if (mo instanceof AgentNode) {
            return ((AgentNode) mo).getConnectStatus().name();
        }
        if (mo instanceof NodeObject) {
            return ((NodeObject) mo).getAdminStatus().name();
        }
        return "UnKnow";
    }
}

