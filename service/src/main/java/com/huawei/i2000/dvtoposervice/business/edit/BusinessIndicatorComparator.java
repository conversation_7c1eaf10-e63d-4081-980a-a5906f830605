/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.edit;

import com.huawei.i2000.dvtoposervice.model.Indicator;

import org.apache.commons.lang3.StringUtils;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 指标比较器
 *
 * <AUTHOR>
 * @since 2024/9/13
 */
public class BusinessIndicatorComparator {

    public <T extends Indicator, K extends Indicator> Boolean compareIndicatorList(List<T> indicators, List<K> compareIndicators) {
        if (indicators.size() != compareIndicators.size()) {
            return false;
        }
        indicators = indicators.stream()
            .sorted(Comparator.comparing(T::getMoType)
                .thenComparing(T::getMeasUnitKey)
                .thenComparing(T::getMeasTypeKey)
                .thenComparing(ind -> Optional.ofNullable(ind.getOriginalValue()).orElse(StringUtils.EMPTY)))
            .collect(Collectors.toList());

        compareIndicators = compareIndicators.stream()
            .sorted(Comparator.comparing(K::getMoType)
                .thenComparing(K::getMeasUnitKey)
                .thenComparing(K::getMeasTypeKey)
                .thenComparing(ind -> Optional.ofNullable(ind.getOriginalValue()).orElse(StringUtils.EMPTY)))
            .collect(Collectors.toList());

        List<T> finalIndicators = indicators;
        List<K> finalCompareIndicators = compareIndicators;
        return IntStream.range(0, indicators.size())
            .allMatch(i -> compareSingleIndicator(finalIndicators.get(i), finalCompareIndicators.get(i)));
    }

    public <T extends Indicator, K extends Indicator> Boolean compareSingleIndicator(T indicator, K indicatorCompare) {
        return StringUtils.equals(indicator.getMoType(), indicatorCompare.getMoType())
            && StringUtils.equals(indicator.getMeasUnitKey(), indicatorCompare.getMeasUnitKey())
            && StringUtils.equals(indicator.getMeasTypeKey(), indicatorCompare.getMeasTypeKey())
            && StringUtils.equals(indicator.getOriginalValue(), indicatorCompare.getOriginalValue());
    }
}
