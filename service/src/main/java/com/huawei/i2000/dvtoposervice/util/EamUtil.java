/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.util;

import com.huawei.baize.avauger.collect.Lists;
import com.huawei.bsp.as.util.PagingIterator;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvtoposervice.business.pm.entity.MoObject;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.constant.RestConstant;
import com.huawei.i2000.dvtoposervice.util.dto.EamFootPrint;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.i2000.mim.json.jackson.MIMMapper;
import com.huawei.i2000.util.json.JsonDeserializeException;
import com.huawei.oms.eam.mim.MORelation;
import com.huawei.oms.eam.mim.RelationType;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.MOType;
import com.huawei.oms.eam.mo.ManagedObject;
import com.huawei.oms.persistence.Criterion;
import com.huawei.oms.persistence.CriterionFactory;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * EamUtil
 *
 * <AUTHOR>
 * @since 2022/11/14
 */
public class EamUtil {
    public static final String SOURCE_P = "P";

    public static final String SOURCE_S = "S";

    public static final String SOURCE_I = "I";

    private static final String TRUE = "true";

    private static final String PM = "com.huawei.i2000.IRes";

    private static final String VM = "com.huawei.IRes";

    private static final String POD = "com.huawei.URes";

    // 物理层
    private static final String PHYSICAL_LAYER = "com.huawei.hardware";

    private static final String CIE_PHYSICAL_LAYER = "com.huawei.cie.hardware";

    // 平台层
    private static final String PLATFORM_LAYER = "com.huawei.as.platform";

    private static final int PAGE_SIZE = 50;

    private static final String GET_MO_BY_DN_URL = "/rest/i2000apimit/mob/list";

    private static final String GET_MO_TRACE = "/rest/eammimservice/v1/mo/event/content";

    private static final String CHAT_TYPE = "chatType";

    private static final String K8S_TYPE = "K8SType";

    private static final String MO_TYPE = "type";

    private static final int MO_DN_PAGE_SIZE = 1000;

    private static List<MOType> moTypes = new ArrayList<>();

    private static Map<String, String> typeDisplayMap = new HashMap<>();

    private static final OssLog LOGGER = OssLogFactory.getLogger(EamUtil.class);

    public static Map<String, List<String>> getMoCategoryMap(List<DN> dns) throws OSSException {
        Map<String, String> typeMap = new HashMap<>();
        getAllMotype().forEach((type) -> typeMap.put(type.getType(), type.getCategory()));
        Map<String, List<String>> ret = new HashMap<>();
        setMoLocation(typeMap, ret, dns);
        return ret;
    }

    private static void setMoLocation(Map<String, String> typeMap, Map<String, List<String>> ret, List<DN> dns) {
        int times = 0;
        if (dns.size() % PAGE_SIZE == 0) {
            times = dns.size() / PAGE_SIZE;
        } else {
            times = dns.size() / PAGE_SIZE + 1;
            times = dns.size() / PAGE_SIZE + 1;
        }
        for (int i = 0; i < times; i++) {
            int start = i * PAGE_SIZE;
            int pageSize = i == times ? dns.size() - i * PAGE_SIZE : PAGE_SIZE;
            List<DN> lists = dns.stream().skip(start).limit(pageSize).collect(Collectors.toList());
            List<ManagedObject> managedObjectList = MITManagerClient.newInstance().getMoByDns(lists);
            if (managedObjectList != null) {
                for (ManagedObject mo : managedObjectList) {
                    ret.computeIfAbsent(getTypeLocation(typeMap, mo), k -> new ArrayList<>())
                        .add(mo.getDN().getValue());
                }
            }
        }
    }

    public static String getMoCategory(String dn) throws OSSException {
        Map<String, String> typeMap = new HashMap<>();
        ManagedObject mo = MITManagerClient.newInstance().getMO(new DN(dn));
        if (mo == null) {
            LOGGER.error("[getMoCategory]  get mo is null, dn = {}", dn);
            throw new OSSException("getMO error");
        }
        typeMap.put(mo.getType(), MITManagerClient.newInstance().getMOType(mo.getType()).getCategory());
        return getTypeLocation(typeMap, mo);
    }

    public static String getTypeLocation(Map<String, String> map, ManagedObject mo) {
        if (mo.getType().contains(PM) || mo.getType().contains(VM) || mo.getType().contains(POD)) {
            return SOURCE_P;
        }
        String category = map.get(mo.getType());
        if (StringUtils.isEmpty(category)) {
            return SOURCE_S;
        } else if (category.contains(PLATFORM_LAYER)) {
            return SOURCE_P;
        } else if (category.contains(PHYSICAL_LAYER) || category.contains(CIE_PHYSICAL_LAYER)) {
            return SOURCE_I;
        } else {
            return SOURCE_S;
        }
    }

    public static List<MOType> getAllMotype() throws OSSException {
        if (moTypes.size() == 0) {
            moTypes = MITManagerClient.newInstance().getAllMOType();
        }
        return new ArrayList<>(moTypes);
    }

    public static String getMoType(String type) throws OSSException {
        if (typeDisplayMap.containsKey(type)) {
            return typeDisplayMap.get(type);
        } else {
            String displayType = MITManagerClient.newInstance().getMOType(type, "zh-cn").getDisplayType() + ";"
                + MITManagerClient.newInstance().getMOType(type, "en-us").getDisplayType();
            typeDisplayMap.put(type, displayType);
            return displayType;
        }
    }

    public static String getMoTypeDisplayValue(String moType) {
        try {
            MOType moTypeIns =  MITManagerClient.newInstance().getMOType(moType);
            if (moTypeIns == null) {
                return null;
            }
            return moTypeIns.getDisplayType();
        } catch (OSSException e) {
            LOGGER.error("getMoTypeDisplayValue error, moType = {}", moType);
            return null;
        }
    }

    public static Map<String, String> getMoName(List<String> dnList) {
        // 查询网元名称
        List<MoObject> objectList = getMoListByDnList(dnList);
        Map<String, String> moMap = new HashMap<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(objectList)) {
            objectList.forEach(perObject -> {
                moMap.put(perObject.getDn(), perObject.getName());
            });
        }
        return moMap;
    }

    public static List<MoObject> getMoListByDnList(List<String> dnList) {
        if (CollectionUtils.isEmpty(dnList)) {
            LOGGER.error("[getMoByDn]dnList is null");
            return new ArrayList<>();
        }
        // 去重
        HashSet<String> dnSet = new HashSet<>(dnList);
        dnList.clear();
        dnList.addAll(dnSet);

        List<MoObject> moObjectList = new ArrayList<>();
        List<List<String>> subLists = ListUtils.partition(dnList, 100);
        for (List<String> list : subLists) {
            RestfulResponse response;
            try {
                RestfulOptions options = new RestfulOptions();
                options.setRestTimeout(RestfulOptions.REST_OPTIONS_TIMEOUT_MAXTIMEOUT);
                options.setOption("maxBodySize", 200 * 1024 * 1024);
                RestfulParametes parameters = new RestfulParametes();
                parameters.setRawData(JSONObject.toJSONString(list));
                response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, GET_MO_BY_DN_URL, parameters,
                    options);
            } catch (ServiceException e) {
                LOGGER.error("get mo by dn failed.", e);
                continue;
            }
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("get mo by dn failed, response status = {}", response == null ? null : response.getStatus());
                continue;
            }
            moObjectList.addAll(JSONArray.parseArray(response.getResponseContent(), MoObject.class));
        }
        return moObjectList;
    }

    public static ManagedObject getMoByDn(String dn) {
        try {
            return MITManagerClient.newInstance().getMO(dn);
        } catch (OSSException e) {
            LOGGER.error("getMoByDn error.", e);
            return null;
        }
    }

    public static List<ManagedObject> getMoByDns(List<DN> dns) {
        List<ManagedObject> podMos = new ArrayList<>();
        for (List<DN> dnList : Lists.partition(dns, 100)) {
            try {
                List<DN> uniqueList = new ArrayList<>(dnList.stream()
                    .collect(Collectors.toMap(DN::getValue, dn -> dn, (existing, replacement) -> existing)).values());
                List<ManagedObject> partMos = MITManagerClient.newInstance().getMOList(uniqueList);
                if (CollectionUtils.isEmpty(partMos)) {
                    continue;
                }
                podMos.addAll(partMos);
            } catch (OSSException e) {
                LOGGER.error("getMOList error, dns = {}", dnList.stream().map(DN::getValue).collect(Collectors.toList()), e);
            }
        }
        return podMos;
    }

    /**
     * 根据集群网元判断 容器化场景还是虚机场景
     *
     * @param clusterDn 集群Dn
     * @return 是否为容器化环境
     */
    public static Integer isContainerEnvironment(String clusterDn) {
        ManagedObject clusterMo = getMoByDn(clusterDn);
        String chatType;
        if (Objects.nonNull(clusterMo)) {
            // 如果是k8s接入的，必定有K8SType属性且为true，必定是容器化
            if (Objects.nonNull(clusterMo.getClientProperties().get(K8S_TYPE))
                && TRUE.equals(String.valueOf(clusterMo.getClientProperties().get(K8S_TYPE)))) {
                return BusinessTopoConstant.ENVIRONMENT_TYPE_CONTAINER;
            }
            // 如果是DF接入的，有chatType属性说明是容器化，没有则是虚机
            if (Objects.nonNull(clusterMo.getClientProperties().get(CHAT_TYPE))) {
                chatType = String.valueOf(clusterMo.getClientProperties().get(CHAT_TYPE));
                return StringUtils.isNotEmpty(chatType)
                    ? BusinessTopoConstant.ENVIRONMENT_TYPE_CONTAINER
                    : BusinessTopoConstant.ENVIRONMENT_TYPE_VM;
            }
        }
        return BusinessTopoConstant.ENVIRONMENT_TYPE_VM;
    }

    /**
     * 根据网元类型获取网元 - 只能查1000个
     *
     * @param moType 网元类型
     * @return 网元列表
     */
    public static List<ManagedObject> getMoByMoType(String moType) {
        List<ManagedObject> mos = new ArrayList<>();
        try {
            mos = MITManagerClient.newInstance().getMoByType(moType);
        } catch (OSSException e) {
            LOGGER.error("getMoByType error", e);
        }
        return Objects.isNull(mos) ? Collections.emptyList() : mos;
    }

    /**
     * 根据网元类型获取网元 - 资源服务异常时抛出异常
     *
     * @param moType 网元类型
     * @return 网元列表
     * @throws OSSException OSSException
     */
    public static List<ManagedObject> queryMosByMoTypeWhenRefresh(String moType) throws OSSException {
        Criterion criterion = CriterionFactory.createCriterion();
        List<String> moTypeList = new ArrayList<>();
        moTypeList.add(moType);
        criterion.descend(MO_TYPE).constrain(moTypeList).in();
        return bulkGetMo(criterion);
    }

    /**
     * 根据网元类型获取网元
     *
     * @param moType 网元类型
     * @return 网元列表
     */
    public static List<ManagedObject> queryMosByMoType(String moType) {
        Criterion criterion = CriterionFactory.createCriterion();
        List<String> moTypeList = new ArrayList<>();
        moTypeList.add(moType);
        criterion.descend(MO_TYPE).constrain(moTypeList).in();
        List<ManagedObject> mos = null;
        try {
            mos = bulkGetMo(criterion);
        } catch (OSSException e) {
            LOGGER.error("get mos by type error, moType = {}", moType, e);
        }
        return mos == null ? new ArrayList<>() : mos;
    }

    /**
     * 根据多个网元类型获取网元
     *
     * @param moTypes 网元类型
     * @return 网元列表
     */
    public static List<ManagedObject> queryMosByMoTypes(List<String> moTypes) {
        Criterion criterion = CriterionFactory.createCriterion();
        criterion.descend(MO_TYPE).constrain(moTypes).in();
        List<ManagedObject> mos = null;
        try {
            mos = bulkGetMo(criterion);
        } catch (OSSException e) {
            LOGGER.error("get mos by type error, moType = {}", moTypes, e);
        }
        return mos == null ? Collections.emptyList() : mos;
    }

    public static List<ManagedObject> bulkGetMo(Criterion criterion) throws OSSException {
        List<ManagedObject> results = new ArrayList<>();
        PagingIterator<ManagedObject> it = null;
        do {
            it = MITManagerClient.newInstance().bulkGet(criterion, MO_DN_PAGE_SIZE, it);
            if (it != null) {
                List<ManagedObject> result = it.getResult();
                if (result != null) {
                    results.addAll(result);
                }
            }
        } while (it != null && it.hasNext());
        return results;
    }

    /**
     * 根据父网元dn获取子网元
     *
     * @param parentDn 父网元dn
     * @return 网元列表
     */
    public static List<ManagedObject> getChildrenByParentDn(String parentDn) {
        List<ManagedObject> results = new ArrayList<>();
        try {
            results.addAll(MITManagerClient.newInstance().getAllChildren(parentDn));
        } catch (OSSException e) {
            LOGGER.error("getChildrenByParentDn error, parentDn = {}", parentDn, e);
        }
        return results;
    }

    /**
     * 批量查询vm上部署的pod或者container上部署的docker
     *
     * @param dns vm或的dn
     * @return key为pod的dn，value为vm的dn
     */
    public static Map<String, List<String>> getDeployMapByDns(List<String> dns) {
        Set<DN> dnSet = dns.stream().map(DN::new).collect(Collectors.toSet());
        List<MORelation> relations = new ArrayList<>();
        try {
            relations = MITManagerClient.newInstance().getAllRelationsByDns(new ArrayList<>(dnSet), RelationType.Deployment.name(), true);
        } catch (OSSException e) {
            LOGGER.error("get deploy dns by dns error, dns = {}", dns, e);
        }
        Map<String, List<String>> deployRelationMap = new HashMap<>();
        relations.forEach(moRelation ->
            deployRelationMap
                .computeIfAbsent(moRelation.getSrcNode().getValue(), k -> new ArrayList<>())
                .add(moRelation.getDestNode().getValue())
        );
        return deployRelationMap;
    }

    /**
     * 指定dn和时间范围，查询网元足迹
     *
     * @param eventTypes Added或者Removed
     * @param dn dn
     * @param startTime startTime
     * @param endTime endTime
     * @return 消亡的网元信息
     */
    public static List<EamFootPrint> queryFootPrintByDn(List<String> eventTypes, String dn, Long startTime, Long endTime) {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("eventTypes", eventTypes);
        paramMap.put("dn", dn);
        paramMap.put("startTime", startTime);
        paramMap.put("endTime", endTime);
        LOGGER.debug("[EamUtil]getMoList paramMap is {}", paramMap);

        String json;
        RestfulResponse response;
        RestfulParametes parametes = new RestfulParametes();
        parametes.setRawData(JSON.toJSONString(paramMap));
        try {
            response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, GET_MO_TRACE,
                parametes, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("[EamUtil] queryFootPrintByDns response error");
                return Collections.emptyList();
            }
            json = response.getResponseContent();
            JsonNode node = MIMMapper.newInstance().getObject(json, JsonNode.class);
            return JSONArray.parseArray(node.get("list").toString(), EamFootPrint.class);
        } catch (ServiceException | JsonDeserializeException | JSONException e) {
            LOGGER.error("[EamUtil] queryFootPrintByDns EAM request error.", e);
            return Collections.emptyList();
        }
    }
}
