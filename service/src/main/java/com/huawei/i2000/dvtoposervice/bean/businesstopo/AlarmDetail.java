/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.bean.businesstopo;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * AlarmDetail
 *
 * <AUTHOR>
 * @since 2024/2/28
 */
@Data
public class AlarmDetail {

    private Integer csn;

    private Integer instanceId;

    private Integer alarmId;

    private String alarmName;

    private Integer category;

    private Integer severity;

    private String meName;

    private String nativeMeDn;

    private Long occurTime;

    private String latestOccurUtc;

    private Long occurUtc;

    private String moi;

    private String region;

    private String probableCause;

    private String additionalInformation;

    private String meType;

    private String alarmGroupId;

    /**
     * 告警扩展字段，需要从中提取智能运维任务标示
     */
    private Map<Object, String> extParams = new HashMap<>();

    private String indicatorId;

    private Long executionTime;

    private String selectedAssociationTask;

    private String analysisStatus;

    private String alarmUUID;

    private Integer clearCategory;

    /**
     * 消亡告警标示，清理所有为true的告警
     */
    private Boolean isVanished;

    /**
     * 特殊告警标示，10为智能运维告警
     */
    private Integer specialLabel;

    /**
     * 告警是否清理
     */
    private Integer cleared;
}
