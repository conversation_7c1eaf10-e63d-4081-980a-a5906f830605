/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.dao.service;

import com.huawei.baize.avauger.collect.Lists;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceRelationModel;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.dao.AbstractDao;
import com.huawei.i2000.dvtoposervice.dao.TableNameTransfer;
import com.huawei.i2000.dvtoposervice.dao.mapper.service.BusinessInstanceRelationMapper;
import com.huawei.i2000.dvtoposervice.util.TimeLineUtil;

import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.exceptions.PersistenceException;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * BusinessInstanceRelationDao
 *
 * <AUTHOR>
 * @since 2024年3月20日16:49:47
 */
@Component
public class BusinessInstanceRelationDao extends AbstractDao implements TableNameTransfer {

    private static final OssLog LOGGER = OssLogFactory.getLogger(BusinessInstanceRelationDao.class);

    private static final String INSTANCE_RELATION_PREFIX = "TBL_TOPO_BUSINESS_TIME_LINE_INSTANCE_RELATION_";

    private static final String TBL_TOPO_BUSINESS_INSTANCE_MODEL = "TBL_TOPO_BUSINESS_INSTANCE_MODEL";

    private static final String TBL_TOPO_BUSINESS_INSTANCE_RELATION = "TBL_TOPO_BUSINESS_INSTANCE_RELATION";

    public void insertRelationInfo(List<BusinessInstanceRelationModel> relationList) {
        if (CollectionUtils.isEmpty(relationList)) {
            return;
        }
        LOGGER.debug("[BusinessInstanceRelationDao] start insertRelationInfo");
        try (SqlSession sqlSession = getMapperMgr().getSqlSessionBatch().getSqlSessionFactory().openSession(ExecutorType.BATCH)) {
            BusinessInstanceRelationMapper instanceRelationMapper = sqlSession.getMapper(BusinessInstanceRelationMapper.class);
            for (List<BusinessInstanceRelationModel> relationModelList : Lists.partition(relationList, 500)) {
                instanceRelationMapper.insertRelationInfo(relationModelList);
                sqlSession.flushStatements();
            }
            sqlSession.commit(!TransactionSynchronizationManager.isSynchronizationActive());
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] insertRelationInfo failed! e:", e);
        }
    }

    /**
     * 查询对应关系的实例id
     *
     * @param instanceId 当前实例id
     * @param type       关系类型 - 0：父关系 1：部署关系 2 ：顶层关系 3：归属站点关系
     * @return 对应关系的实例id
     * @throws ServiceException ServiceException
     */
    public Integer queryRelationByType(Integer instanceId, Integer type) throws ServiceException {
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            return instanceRelationMapper.queryRelationByType(instanceId, type);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryRelationByType failed!", e);
            throw new ServiceException("queryRelationByType failed!");
        }
    }

    /**
     * 根据实例ID列表和时间戳查询子实例
     *
     * @param instanceIds 实例ID列表
     * @param timeStamp 时间戳
     * @return 返回查询到的子实例ID列表
     * @throws ServiceException 当服务异常时抛出
     */
    public List<Integer> querySonByInsId(List<Integer> instanceIds, long timeStamp) throws ServiceException {
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            String tableName;
            if (timeStamp == 0L) {
                tableName = BusinessTopoConstant.INSTANCE_RELATION;
            } else {
                tableName = INSTANCE_RELATION_PREFIX + TimeLineUtil.backtrackingTimestamp(timeStamp);
            }
            return instanceRelationMapper.querySonByInsIdBatchTimeLine(instanceIds, tableName);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryRelationByType failed!", e);
            throw new ServiceException("queryRelationByType failed!");
        }
    }

    public Map<Integer, Integer> queryRelationByTypeBatchTimeLine(List<Integer> instanceIds, Integer type, Long timestamp) {
        if (CollectionUtils.isEmpty(instanceIds)) {
            return Collections.emptyMap();
        }
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            String tableName;
            if (timestamp == null || timestamp == 0L) {
                tableName = BusinessTopoConstant.INSTANCE_RELATION;
            } else {
                tableName = INSTANCE_RELATION_PREFIX + TimeLineUtil.backtrackingTimestamp(timestamp);
            }
            return instanceRelationMapper.queryRelationByTypeBatchTimeLine(tableName, instanceIds, type).stream()
                .collect(Collectors.toMap(BusinessInstanceRelationModel::getInstanceId,
                    BusinessInstanceRelationModel::getTargetInstanceId, (v1, v2) -> v1));
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryRelationByTypeBatch failed!", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 查询对应关系的实例id
     *
     * @param instanceId 当前实例id
     * @param type       关系类型 - 0：父关系 1：部署关系 2 ：顶层关系 3：归属站点关系
     * @return 对应关系的实例id
     * @throws ServiceException ServiceException
     */
    public Integer queryGrandRelationByType(Integer instanceId, Integer type) throws ServiceException {
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            return instanceRelationMapper.queryGrandRelationByType(instanceId, type);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryRelationByType failed!", e);
            throw new ServiceException("queryRelationByType failed!");
        }
    }

    /**
     * 查询对应关系的实例id
     *
     * @param targetInstanceId 当前实例id
     * @param type             关系类型 - 0：父关系 1：部署关系 2 ：顶层关系 3：归属站点关系
     * @return 对应关系的实例id
     */
    public List<Integer> queryAntiRelationByType(Integer targetInstanceId, Integer type) {
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            return instanceRelationMapper.queryAntiRelationByType(targetInstanceId, type);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryRelationByType failed!", e);
        }
        return Collections.emptyList();
    }

    /**
     * 查询库中没有父关系的Pod
     *
     * @return 实例列表
     */
    public List<BusinessInstanceModelDB> queryOrphanPodDns() {
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            return instanceRelationMapper.queryOrphanPodDns();
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryOrphanPodDns failed!", e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询库中没有对应关系的实例
     *
     * @param modelType 实例类型
     * @param relationType 关系类型
     * @return 实例列表
     */
    public List<BusinessInstanceModelDB> queryNoneRelationInstances(Integer modelType, Integer relationType) {
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            return instanceRelationMapper.queryNoneRelationInstances(modelType, relationType);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryNoneRelationInstances failed!", e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据传入的实例查询其对应关系类型的实例关系
     *
     * @param instanceIds  本层实例列表
     * @param relationType 关系类型
     * @return 实例id列表
     */
    public List<BusinessInstanceRelationModel> queryInstanceByRelationType(List<Integer> instanceIds, Integer relationType) {
        if (CollectionUtils.isEmpty(instanceIds)) {
            return Collections.emptyList();
        }
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            return instanceRelationMapper.queryInstancesByRelationType(instanceIds, relationType);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryDownLevelInstanceIds failed!", e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询对应关系的实例id
     *
     * @param targetInstanceId 当前实例id
     * @param type             关系类型 - 0：父关系 1：部署关系 2 ：顶层关系 3：归属站点关系
     * @param timestamp        时间轴时间
     * @return 对应关系的实例id
     * @throws ServiceException ServiceException
     */
    public Integer queryAntiRelationByTypeAndTimeLine(Integer targetInstanceId, Integer type, long timestamp)
        throws ServiceException {
        long currentTime = System.currentTimeMillis();
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            long timeLineStamp = TimeLineUtil.backtrackingTimestamp(timestamp);
            if (timeLineStamp >= currentTime || timestamp == 0) {
                return queryRelationByType(targetInstanceId, type);
            }
            return instanceRelationMapper.queryAntiRelationByTypeAndTimeLine(targetInstanceId, type,
                INSTANCE_RELATION_PREFIX + timeLineStamp);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryRelationByType failed!", e);
            throw new ServiceException("queryRelationByType failed!");
        }
    }

    /**
     * 删除实例关联表中所有数据记录
     *
     * @param instanceIdList instanceIdList
     */
    public void clearInstanceRelationByInstanceIdBatch(List<Integer> instanceIdList) {
        if (CollectionUtils.isEmpty(instanceIdList)) {
            return;
        }

        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            Lists.partition(instanceIdList, 500).forEach(instanceRelationMapper::clearInstanceRelationByInstanceIdBatch);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] clearInstanceRelationByInstanceIdBatch failed!", e);
        }
    }

    /**
     * 查询库中没有父关系的Docker
     *
     * @return 实例列表
     */
    public List<BusinessInstanceModelDB> queryOrphanDockerDns() {
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            return instanceRelationMapper.queryOrphanDockerDns();
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryOrphanDockerDns failed!", e);
            return new ArrayList<>();
        }
    }


    /**
     * getSiteInstanceInfo
     *
     * @param instanceIds 查询站点关系数据
     * @return 根据实例的instanceId获取其与所属的站点的关联关系
     */
    public List<BusinessInstanceRelationModel> getSiteInstanceRelationInfoByInstanceId(List<Integer> instanceIds) {
        if (CollectionUtils.isNotEmpty(instanceIds)) {
            BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
            try {
                return instanceRelationMapper.getInstanceRelationInfoByInstanceIdAndRelationType(instanceIds,
                    BusinessTopoConstant.RELATION_TYPE_SITE, BusinessTopoConstant.COLUMN_INSTANCE_ID);
            } catch (DataAccessException | PersistenceException e) {
                LOGGER.error("[BusinessInstanceRelationDao] getSiteInstanceRelationInfoByInstanceId failed!", e);
            }
        }
        return Collections.emptyList();
    }

    /**
     * getInstanceRelationInfoByInstanceIdAndRelationType
     *
     * @param instanceIds  查询站点关系数据
     * @param relationType 实例关系类型
     * @param columnName   columnName
     * @return 根据instanceId和relationType获取实例关系
     */
    public List<BusinessInstanceRelationModel> getInsRelByInsIdAndRelationType(List<Integer> instanceIds, Integer relationType, String columnName) {
        if (CollectionUtils.isNotEmpty(instanceIds) && Objects.nonNull(relationType)) {
            BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
            try {
                if (BusinessTopoConstant.COLUMN_INSTANCE_ID.equals(columnName)) {
                    return instanceRelationMapper.getInstanceRelationInfoByInstanceIdAndRelationType(instanceIds, relationType, columnName);
                } else {
                    return instanceRelationMapper.getInstanceRelationInfoByInstanceIdAndRelationTypeDown(instanceIds, relationType, columnName);
                }

            } catch (DataAccessException | PersistenceException e) {
                LOGGER.error("[BusinessInstanceRelationDao] getInstanceRelationInfoByInstanceIdAndRelationType failed!", e);
            }
        }
        return Collections.emptyList();
    }


    public List<BusinessInstanceRelationModel> getInsRelByInsIdAndRelationTypeForTimeLine(List<Integer> instanceIds,
        long timeStamp, Integer relationType, String columnName) {
        long currentTime = System.currentTimeMillis();
        LOGGER.debug("[BusinessInstanceModelDao] start getSiteInstanceRelationInfoByInstanceIdForTimeLine, " +
            "instanceIds={}, timeStamp={}", instanceIds, timeStamp);
        try {
            long timeLineStamp = TimeLineUtil.backtrackingTimestamp(timeStamp);
            if (timeStamp == 0 || timeLineStamp >= currentTime) {
                return getInsRelByInsIdAndRelationType(instanceIds, relationType, columnName);
            } else {
                if (CollectionUtils.isNotEmpty(instanceIds)) {
                    BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
                    if (BusinessTopoConstant.COLUMN_INSTANCE_ID.equals(columnName)) {
                        return instanceRelationMapper.getInsRelByInsIdAndRelationTypeForTimeLine(instanceIds,
                            relationType, BusinessTopoConstant.INSTANCE_RELATION_PREFIX + timeLineStamp, columnName
                        );
                    } else {
                        return instanceRelationMapper.getInsRelByInsIdAndRelationTypeForTimeLineDown(instanceIds,
                            relationType, BusinessTopoConstant.INSTANCE_RELATION_PREFIX + timeLineStamp, columnName
                        );
                    }

                }
            }
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceModelDao] getSiteInstanceRelationInfoByInstanceIdForTimeLine failed! e:", e);
        }
        return Collections.emptyList();
    }

    public List<BusinessInstanceRelationModel> queryAllRelationByType(Integer relationType, List<Integer> insIdNotIn) throws ServiceException {
        try {
            BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
            return instanceRelationMapper.queryAllRelationByType(relationType, insIdNotIn);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceModelDao] queryAllRelationByType failed!");
            throw new ServiceException(e);
        }
    }

    public Integer queryBelongToInsFilterByModelId(int topInsId, String modelId, Long timestamp) {
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            String insModelTableName;
            String relationTableName;
            if (Objects.isNull(timestamp) || timestamp == 0L) {
                insModelTableName = TBL_TOPO_BUSINESS_INSTANCE_MODEL;
                relationTableName = TBL_TOPO_BUSINESS_INSTANCE_RELATION;
            } else {
                insModelTableName = BusinessTopoConstant.INSTANCE_MODEL_PREFIX + TimeLineUtil.backtrackingTimestamp(timestamp);
                relationTableName = BusinessTopoConstant.INSTANCE_RELATION_PREFIX + TimeLineUtil.backtrackingTimestamp(timestamp);
            }
            return instanceRelationMapper.queryBelongToInsFilterByModelId(topInsId, modelId, insModelTableName, relationTableName);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryBelongToInsFilterByModelId failed!", e);
            return null;
        }
    }

    public void deleteRelationByInstanceId(Integer instanceId) {
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            instanceRelationMapper.deleteRelationByInstanceId(instanceId);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] deleteRelationByInstanceId", e);
        }
    }

    public void deleteRelationByInstanceIdAndType(List<Integer> instanceIds, Integer type) {
        if (CollectionUtils.isEmpty(instanceIds)) {
            return;
        }
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            instanceRelationMapper.deleteRelationByInstanceIdAndType(instanceIds, type);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] deleteRelationByInstanceIdAndType", e);
        }
    }

    public void replaceRelationInfo(List<BusinessInstanceRelationModel> relationList) {
        if (CollectionUtils.isEmpty(relationList)) {
            return;
        }
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            instanceRelationMapper.replaceRelationInfo(relationList);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] replaceRelationInfo", e);
        }
    }

    /**
     * queryInsIdOfOneLvFromSub
     *
     * @param upInstanceId  上层实例Id
     * @param relationType 实例关系类型
     * @param modelType 层级
     * @param timestamp 时间戳
     * @return 根据顶层或者站点查询下面某一层实例Id集合
     */
    public List<BusinessInstanceModelDB> queryInsIdOfOneLvFromUp(int upInstanceId, int relationType, int modelType, Long timestamp) {
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            String insModelTableName;
            String relationTableName;
            if (Objects.isNull(timestamp) || timestamp == 0L) {
                insModelTableName = TBL_TOPO_BUSINESS_INSTANCE_MODEL;
                relationTableName = TBL_TOPO_BUSINESS_INSTANCE_RELATION;
            } else {
                insModelTableName = BusinessTopoConstant.INSTANCE_MODEL_PREFIX + TimeLineUtil.backtrackingTimestamp(timestamp);
                relationTableName = BusinessTopoConstant.INSTANCE_RELATION_PREFIX + TimeLineUtil.backtrackingTimestamp(timestamp);
            }
            return instanceRelationMapper.queryInsIdOfOneLvFromUp(upInstanceId, relationType, modelType, insModelTableName, relationTableName);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryInsIdOfOneLvFromUp failed!", e);
        }
        return Collections.emptyList();
    }


    /**
     * 根据当前实例id获取其所属站点的信息
     *
     * @param curInsId  当前实例id
     * @param timeStamp 时间戳
     * @return 当前实例所属的站点信息
     */
    public BusinessInstanceModelDB getSiteInfoByCurInsId(Integer curInsId, Long timeStamp) {
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            return instanceRelationMapper.getSiteInfoByCurInsId(curInsId, instanceModelTableName(timeStamp), instanceRelationTableName(timeStamp));
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] getSiteInfoByCurInsId failed!", e);
            e.printStackTrace();
        }
        return null;
    }

    public List<BusinessInstanceRelationModel> queryAllRelationByInstanceIdsTimeline(List<Integer> instanceIds, Long timestamp) {
        long currentTime = System.currentTimeMillis();
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            long timeLineStamp = TimeLineUtil.backtrackingTimestamp(timestamp);
            if (timeLineStamp >= currentTime || timestamp == 0) {
                return instanceRelationMapper.queryAllRelationByInstanceIds(instanceIds);
            }
            return instanceRelationMapper.queryAllRelationByInstanceIdsTimeline(instanceIds, INSTANCE_RELATION_PREFIX + timeLineStamp);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryAllRelationByInstanceIdsTimeline failed!", e);
            return Collections.emptyList();
        }
    }

    public List<BusinessInstanceRelationModel> queryRelationInsList(Integer instanceId, Long timestamp) {
        long currentTime = System.currentTimeMillis();
        BusinessInstanceRelationMapper instanceRelationMapper = getMapper(BusinessInstanceRelationMapper.class);
        try {
            long timeLineStamp = TimeLineUtil.backtrackingTimestamp(timestamp);
            String tableName = TBL_TOPO_BUSINESS_INSTANCE_RELATION;
            if (timeLineStamp >= currentTime || timestamp == 0) {
                return instanceRelationMapper.queryRelationInsListTimeline(instanceId, tableName);
            }
            return instanceRelationMapper.queryRelationInsListTimeline(instanceId, tableName + timeLineStamp);
        } catch (DataAccessException | PersistenceException e) {
            LOGGER.error("[BusinessInstanceRelationDao] queryRelationInsList failed!", e);
            return Collections.emptyList();
        }
    }
}
