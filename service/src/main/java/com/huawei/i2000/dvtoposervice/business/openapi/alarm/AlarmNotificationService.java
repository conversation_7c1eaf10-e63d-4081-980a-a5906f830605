/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.openapi.alarm;

import com.huawei.bsp.as.util.Pair;
import com.huawei.bsp.deploy.util.DefaultEnvUtil;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.ModelAlarm;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.model.AlarmMoiParseKeys;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessConfigEnum;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.BusinessTopoAlarmStorageService;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.PerformanceThresholdAlarmService;
import com.huawei.i2000.dvtoposervice.constant.AlarmConstant;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessAlarmDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelExtentAttrDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessIndicatorDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceRelationDao;
import com.huawei.i2000.dvtoposervice.model.BusinessIndicator;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.timedtri.AnalysisIndQueryTimer;
import com.huawei.i2000.dvtoposervice.util.ConfigurationUtil;
import com.huawei.i2000.dvtoposervice.util.dto.BusinessTopoIndicator;
import com.huawei.i2000.dvtoposervice.util.dto.ThresholdStaticInfo;

import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 告警通知服务
 *
 * <AUTHOR>
 * @since 2024/3/9
 */
@Component
public class AlarmNotificationService {
    private static final OssLog LOGGER = OssLogFactory.getLogger(AlarmNotificationService.class);

    private static final String ALARM_CSN = "AlarmCsn";

    private static final String EVENT_CSN = "EventCsn";

    private static final String OWN_CSN = "OwnCsn";

    private static final String ALARM_COUNT = "AlarmCount";

    private static final String STRING_TYPE = "java.lang.String";

    private static final String INTEGER_TYPE = "java.lang.Integer";

    private static final String CONTAINER_TYPE = "com.huawei.URes.docker";

    private static final String VM_TYPE = "com.huawei.IRes.vm";

    private static final String SYSTEM_TYPE = "docker||vm";

    private static final String UNKNOWN_STATUS = "0";

    private static final String ACTIVE = "1";

    private static final String STANDBY = "2";

    private static final String REDUNDANCY_ACTIVE = "3";

    private static final String REDUNDANCY_STANDBY = "4";

    private static final String HW_VNODE_ID = "hwVNodeId";

    private static final String HW_CURRENT_NODE_ROLE = "hwCurrentNodeRole";

    private static final String HW_DESIGN_NODE_ROLE = "hwDesignNodeRole";

    private static final String AS_SWITCH_COUNT_DB = "asSwitchCount";

    private static final String AR_SWITCH_COUNT_DB = "arSwitchCount";

    private static final String AS_SWITCH_TIME = "asSwitchTime";

    private static final String AR_SWITCH_TIME = "arSwitchTime";

    private static final String LAST_UPDATE_SWITCH_TIME = "lastSwitchTime";

    private static final String STRING_CLASS = "java.lang.String";

    private static final String PHYSICAL_DATABASE = "physicalDatabase";

    private static final String MEMORY_DATABASE = "memoryDatabase";

    private static final Pattern MDB_NODE_LOCATION = Pattern.compile("VNode=(\\d+),\\s*NodeId=(\\d+)");

    private static final Pattern PDB_NODE_LOCATION = Pattern.compile("node-id=(\\d+),\\s*vnode-id=(\\d+)");

    private static final Pattern MDB_MASTER_NODE_ADDITION = Pattern.compile("master is switched from (\\d+) to (\\d+)");

    private static final Pattern MDB_SWITCH_NODE_ADDITION = Pattern.compile(
        "node (\\d+) is upgraded to master:(.*)");

    private static final Pattern PDB_SWITCH_NODE_ADDITION = Pattern.compile(
        "vnode\\('(\\d+)'\\)'s master failover to node '(\\d+)'");

    private static final Pattern PDB_SWITCH_EVENT_NODE_ADDITION = Pattern.compile(
        "vnode\\('(\\d+)'\\)'s master switch over from '(\\d+)' to '(\\d+)'");

    private static final List<Integer> MDB_ALARM_ID = new ArrayList<>(Arrays.asList(412030115, 412030116));

    private static final List<Integer> PDB_ALARM_ID = new ArrayList<>(Collections.singletonList(412030205));

    private static final List<Integer> PDB_EVENT_ID = new ArrayList<>(Collections.singletonList(412030206));

    private static final List<Integer> CONVERGENCE_LEVEL = Arrays.asList(BusinessTopoConstant.BUSINESS_TYPE_ID,
        BusinessTopoConstant.SITE_TYPE_ID, BusinessTopoConstant.BUSINESS_GROUP_TYPE_ID,
        BusinessTopoConstant.CLUSTER_INSTANCE_ID, BusinessTopoConstant.POD_TYPE_ID, BusinessTopoConstant.VM_TYPE_ID,
        BusinessTopoConstant.DOCKER_TYPE_ID, BusinessTopoConstant.CONTAINER_TYPE_ID,
        BusinessTopoConstant.CHANNEL_TYPE_ID, BusinessTopoConstant.MANAGEMENT_INSTANCE_ID,
        BusinessTopoConstant.MANAGEMENT_INSTANCE_TYPE_ID);

    private static final List<Integer> THRESHOLD_LEVEL = Arrays.asList(BusinessTopoConstant.BUSINESS_TYPE_ID,
        BusinessTopoConstant.CHANNEL_TYPE_ID);

    private String manageObjectColumn = StringUtils.EMPTY;

    private String[] alarmParseKeys = new String[5];

    @Autowired
    BusinessAlarmDao alarmDao;

    @Autowired
    BusinessInstanceModelDao instanceModelDao;

    @Autowired
    BusinessIndicatorDao indicatorDao;

    @Autowired
    BusinessCommonModelExtentAttrDao extentAttrDao;

    @Autowired
    BusinessInstanceRelationDao relationDao;

    @Autowired
    AnalysisIndQueryTimer analysisIndQueryTimer;

    @Autowired
    PerformanceThresholdAlarmService performanceThresholdAlarmService;

    @Autowired
    BusinessTopoAlarmStorageService businessTopoAlarmStorageService;


    /**
     * noticeBusinessTopo
     *
     * @param alarmDetailList alarmDetailList
     */
    public void noticeTimeLineAlarm(List<AlarmDetail> alarmDetailList) {
        if (CollectionUtils.isEmpty(alarmDetailList)) {
            return;
        }
        // subscribe alarm list
        LOGGER.debug("[AlarmNotificationService] noticeBusinessTopo start.");
        // obtain vanished alarm list
        Set<AlarmDetail> vanishedAlarmList = alarmDetailList.stream()
            .filter(alarm -> Objects.nonNull(alarm.getIsVanished()))
            .filter(AlarmDetail::getIsVanished)
            .collect(Collectors.toSet());
        alarmDao.batchInsertAlarmDetail(new ArrayList<>(alarmDetailList));
        // update vanished time alarm
        alarmDao.updateTimeLineAlarm(new ArrayList<>(vanishedAlarmList));
    }

    /**
     * noticeBusinessTopo
     *
     * @param alarmDetailList alarmDetailList
     * @throws ServiceException ServiceException
     */
    @Transactional(rollbackFor = ServiceException.class)
    public void noticeBusinessTopo(List<AlarmDetail> alarmDetailList) throws ServiceException {
        if (CollectionUtils.isEmpty(alarmDetailList)) {
            return;
        }
        LOGGER.debug("[AlarmNotificationService] noticeBusinessTopo start.");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        // subscribe alarm list
        Set<Integer> alarmCsn = alarmDetailList.stream()
            .filter(alarm -> Objects.isNull(alarm.getIsVanished()))
            .map(AlarmDetail::getCsn)
            .collect(Collectors.toSet());
        // obtain vanished alarm list
        Set<AlarmDetail> vanishedAlarmList = alarmDetailList.stream()
            .filter(alarm -> Objects.nonNull(alarm.getIsVanished()))
            .filter(AlarmDetail::getIsVanished)
            .collect(Collectors.toSet());
        LOGGER.debug("[AlarmNotificationService] noticeBusinessTopo alarm csn is {}.", alarmCsn);
        List<BusinessInstanceModelDB> dnInstanceList = instanceModelDao.queryAllInstanceWithDn();
        if (CollectionUtils.isEmpty(dnInstanceList)) {
            LOGGER.debug("[AlarmNotificationService] BusinessTopo does not contain any mo.");
            return;
        }

        Set<String> clearedAlarmList = businessTopoAlarmStorageService.getClearedAlarmList();
        // Alarms that are generated and cleared within a short period of time are not handled.
        Set<AlarmDetail> validateAlarmList = businessTopoAlarmStorageService.filterOccurAndClearedAlarmInShortTime(
            new HashSet<>(alarmDetailList));
        Set<AlarmDetail> filterAlarmList = filterIsInTopoTree(validateAlarmList, dnInstanceList, clearedAlarmList);
        // update instance table
        updateMemberAlarmStatus(new ArrayList<>(filterAlarmList));
        // update vanished alarm
        updateVanishedMemberAlarm(new ArrayList<>(vanishedAlarmList));

        stopWatch.stop();
        LOGGER.debug("[AlarmNotificationService] noticeBusinessTopo end. cost is {} s", stopWatch.getTotalTimeSeconds());
    }

    /**
     * noticeSwitchAlarm 通知容灾切换告警
     *
     * @param switchDetailList alarmDetailList
     * @throws ServiceException ServiceException
     */
    @Transactional(rollbackFor = ServiceException.class)
    public void noticeSwitchAlarm(List<AlarmDetail> switchDetailList) throws ServiceException {
        LOGGER.debug("[AlarmNotificationService] noticeSwitchAlarm start.");
        // ->去除无附加信息的告警
        // ->去除清除告警
        List<AlarmDetail> validateAlarmList = switchDetailList.stream()
            .filter(alarm -> StringUtils.isNotEmpty(alarm.getMoi()) && StringUtils.isNotEmpty(
                alarm.getAdditionalInformation()))
            .filter(alarm -> AlarmConstant.ALARM_OCCUR == alarm.getCategory()
                || AlarmConstant.ALARM_EVENT == alarm.getCategory())
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validateAlarmList)) {
            return;
        }
        Map<String, List<AlarmDetail>> nodeAlarmMap = validateAlarmList.stream()
            .collect(Collectors.groupingBy(this::getVNodeFromLocation));

        for (Map.Entry<String, List<AlarmDetail>> entry : nodeAlarmMap.entrySet()) {
            List<AlarmDetail> switchAlarmList = entry.getValue()
                .stream()
                .filter(this::isNodeSwitchToMaster)
                .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(switchAlarmList)) {
                continue;
            }
            for (AlarmDetail switchAlarm : switchAlarmList) {
                updateDatabaseRoleByAlarm(switchAlarm, entry.getKey());
            }
        }
    }

    /**
     * 根据Topo实例的Dn过滤告警
     *
     * @param alarmDetailList 订阅告警列表
     * @param dnInstanceList 实例列表
     * @param clearedAlarmList 已清理告警列表
     * @return 过滤后的告警列表
     */
    private Set<AlarmDetail> filterIsInTopoTree(
        Set<AlarmDetail> alarmDetailList, List<BusinessInstanceModelDB> dnInstanceList, Set<String> clearedAlarmList) {

        Set<String> wholeDnList = dnInstanceList.stream()
            .map(BusinessInstanceModelDB::getDn)
            .collect(Collectors.toSet());
        // 添加缓存的dn
        if (CollectionUtils.isEmpty(wholeDnList)) {
            LOGGER.debug("[AlarmNotificationService] There is no dn in businessTopo.");
            return Collections.emptySet();
        }
        // 判断告警是否满足条件
        // 1.告警是否在网元范围内 -> 不在范围内，退出
        // 2.告警是否在已清理列表中，并且告警为发生事件-> 在范围内且为发生告警，退出
        return alarmDetailList.stream().filter(alarm -> {
            if (wholeDnList.contains(alarm.getNativeMeDn())) {
                if (!clearedAlarmList.contains(String.valueOf(alarm.getCsn()))) {
                    return true;
                } else {
                    return AlarmConstant.ALARM_CLEARED.equals(alarm.getCleared());
                }
            } else {
                return false;
            }
        }).collect(Collectors.toSet());
    }

    /**
     * getVNodeFromLocation 通过告警的定位信息获取VNode Id
     *
     * @param alarmDetail 告警详情
     * @return VNode Id
     */
    public String getVNodeFromLocation(AlarmDetail alarmDetail) {
        if (MDB_ALARM_ID.contains(alarmDetail.getAlarmId())) {
            // mdb类型
            Matcher nodeMatcher = MDB_NODE_LOCATION.matcher(alarmDetail.getMoi().trim());
            if (nodeMatcher.find()) {
                return nodeMatcher.group(1);
            } else {
                LOGGER.debug("[getVNodeFromLocation] not match the regex, alarm csn is {}, alarm location is {}",
                    alarmDetail.getCsn(), alarmDetail.getMoi());
                return StringUtils.EMPTY;
            }
        } else if (PDB_ALARM_ID.contains(alarmDetail.getAlarmId()) || PDB_EVENT_ID.contains(alarmDetail.getAlarmId())) {
            // pdb类型
            Matcher nodeMatcher = PDB_NODE_LOCATION.matcher(alarmDetail.getMoi().trim());
            if (nodeMatcher.find()) {
                return nodeMatcher.group(2);
            } else {
                LOGGER.debug("[getVNodeFromLocation] not match the regex, alarm csn is {}, alarm location is {}",
                    alarmDetail.getCsn(), alarmDetail.getMoi());
                return StringUtils.EMPTY;
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * isNodeSwitchToMaster 判断告警是否为升主告警
     *
     * @param alarmDetail 告警详情
     * @return 判断告警是否为升主告警
     */
    public boolean isNodeSwitchToMaster(AlarmDetail alarmDetail) {
        if (MDB_ALARM_ID.contains(alarmDetail.getAlarmId())) {
            return MDB_SWITCH_NODE_ADDITION.matcher(alarmDetail.getAdditionalInformation().trim()).matches();
        } else if (PDB_ALARM_ID.contains(alarmDetail.getAlarmId())) {
            return PDB_SWITCH_NODE_ADDITION.matcher(alarmDetail.getAdditionalInformation().trim()).matches();
        } else if (PDB_EVENT_ID.contains(alarmDetail.getAlarmId())) {
            return PDB_SWITCH_EVENT_NODE_ADDITION.matcher(alarmDetail.getAdditionalInformation().trim()).matches();
        }
        return false;
    }

    private void updateDatabaseRoleByAlarm(AlarmDetail toMasterAlarm, String vNodeId) throws ServiceException {
        BusinessInstanceModelDB toMasterIns = instanceModelDao.queryInstanceByDn(toMasterAlarm.getNativeMeDn());
        if (Objects.isNull(toMasterIns)) {
            LOGGER.error("[noticeSwitchAlarm] The dn of master or toSwitch not find in topo tree, toSwitch dn is {}",
                toMasterAlarm.getNativeMeDn());
            return;
        }
        if (!vNodeId.equals(toMasterIns.getExtentAttr(HW_VNODE_ID).getAttrValue())) {
            LOGGER.error("[noticeSwitchAlarm] The alarm vNodeId do not equal the ins vNode, vNodeId is {}, ins vNode is {}",
                vNodeId, toMasterIns.getExtentAttr(HW_VNODE_ID).getAttrValue());
            return;
        }
        if (!canUpdateDatabaseRole(toMasterIns, toMasterAlarm)) {
            return;
        }
        LOGGER.info("[noticeSwitchAlarm] The toMasterAlarm csn is {}, toMasterIns is {}", toMasterAlarm.getCsn(),
            toMasterIns.getInstanceId());
        if (STANDBY.equals(toMasterIns.getExtentAttr(HW_CURRENT_NODE_ROLE).getAttrValue())) {
            LOGGER.info("[noticeSwitchAlarm] toMasterAlarm type is as switch");
            updateAsRoleAndAsCount(vNodeId, toMasterAlarm.getOccurUtc());
        } else if (REDUNDANCY_ACTIVE.equals(toMasterIns.getExtentAttr(HW_CURRENT_NODE_ROLE).getAttrValue())) {
            LOGGER.info("[noticeSwitchAlarm] toMasterAlarm type is ar switch");
            updateArRoleAnsArCount(vNodeId, toMasterIns, toMasterAlarm.getOccurUtc());
        } else if (REDUNDANCY_STANDBY.equals(toMasterIns.getExtentAttr(HW_CURRENT_NODE_ROLE).getAttrValue())) {
            LOGGER.info("[noticeSwitchAlarm] toMasterAlarm type is rars switch");
            updateRaRsRoleAndAsCount(vNodeId, toMasterAlarm.getOccurUtc());
        } else {
            LOGGER.error("[noticeSwitchAlarm] The toMasterAlarm csn is {}, The toMasterIns now role is {}",
                toMasterAlarm.getCsn(), toMasterIns.getExtentAttr(HW_CURRENT_NODE_ROLE).getAttrValue());
        }
    }

    private boolean canUpdateDatabaseRole(BusinessInstanceModelDB toMasterIns, AlarmDetail toMasterAlarm) {
        // 如果节点角色的最后一次更新时间大于告警的发生时间，返回
        String switchTimeValue = toMasterIns.getExtentAttr(LAST_UPDATE_SWITCH_TIME).getAttrValue();
        if (StringUtils.isNotEmpty(switchTimeValue)) {
            long switchTime = Long.parseLong(switchTimeValue);
            if (switchTime > toMasterAlarm.getOccurUtc()) {
                LOGGER.error(
                    "[noticeSwitchAlarm] update alarm is later than last update. "
                        + "alarmOccurTime is {}, last update time is {}",
                    toMasterAlarm.getOccurUtc(), switchTime);
                return false;
            }
        }
        return true;
    }

    private void updateAsRoleAndAsCount(String vNodeId, long occurTime) throws ServiceException {
        List<BusinessInstanceModelDB> vNodeDockerList = instanceModelDao.queryInstanceByExtendAttrValue(HW_VNODE_ID,
            vNodeId);
        // 查找当前切换为主备的
        BusinessInstanceModelDB masterNode = vNodeDockerList.stream()
            .filter(node -> ACTIVE.equals(node.getExtentAttr(HW_CURRENT_NODE_ROLE).getAttrValue()))
            .findFirst()
            .orElse(null);
        BusinessInstanceModelDB standNode = vNodeDockerList.stream()
            .filter(node -> STANDBY.equals(node.getExtentAttr(HW_CURRENT_NODE_ROLE).getAttrValue()))
            .findFirst()
            .orElse(null);
        if (Objects.isNull(masterNode) || Objects.isNull(standNode)) {
            LOGGER.error("[noticeSwitchAlarm] The master or stand node could not find.");
            return;
        }
        // 更新主备角色
        List<BusinessInsExtentAttrDB> attrs = new ArrayList<>();
        attrs.add(new BusinessInsExtentAttrDB(masterNode.getInstanceId(), HW_CURRENT_NODE_ROLE, STRING_CLASS, STANDBY));
        attrs.add(new BusinessInsExtentAttrDB(masterNode.getInstanceId(), AS_SWITCH_TIME, STRING_CLASS,
            String.valueOf(occurTime)));
        attrs.add(new BusinessInsExtentAttrDB(masterNode.getInstanceId(), LAST_UPDATE_SWITCH_TIME, STRING_CLASS,
            String.valueOf(occurTime)));
        attrs.add(new BusinessInsExtentAttrDB(standNode.getInstanceId(), HW_CURRENT_NODE_ROLE, STRING_CLASS, ACTIVE));
        attrs.add(new BusinessInsExtentAttrDB(standNode.getInstanceId(), AS_SWITCH_TIME, STRING_CLASS,
            String.valueOf(occurTime)));
        attrs.add(new BusinessInsExtentAttrDB(standNode.getInstanceId(), LAST_UPDATE_SWITCH_TIME, STRING_CLASS,
            String.valueOf(occurTime)));
        extentAttrDao.updateInstanceAttr(attrs);
        // 更新主备数量
        updateSwitchCount(masterNode, standNode, AS_SWITCH_COUNT_DB);
    }

    private void updateArRoleAnsArCount(String vNodeId, BusinessInstanceModelDB toMasterIns, long occurTime)
        throws ServiceException {
        List<BusinessInstanceModelDB> vNodeDockerList = instanceModelDao.queryInstanceByExtendAttrValue(HW_VNODE_ID,
            vNodeId);
        BusinessInstanceModelDB masterNode = vNodeDockerList.stream()
            .filter(node -> ACTIVE.equals(node.getExtentAttr(HW_CURRENT_NODE_ROLE).getAttrValue()))
            .findFirst()
            .orElse(null);
        BusinessInstanceModelDB standNode = vNodeDockerList.stream()
            .filter(node -> STANDBY.equals(node.getExtentAttr(HW_CURRENT_NODE_ROLE).getAttrValue()))
            .findFirst()
            .orElse(null);
        List<BusinessInstanceModelDB> recoveryNodeList = vNodeDockerList.stream()
            .filter(node -> REDUNDANCY_ACTIVE.equals(node.getExtentAttr(HW_CURRENT_NODE_ROLE).getAttrValue())).collect(
                Collectors.toList());
        Pair<BusinessInstanceModelDB, BusinessInstanceModelDB> toMasterSite = distinguishingMasterAndStandNode(
            recoveryNodeList, toMasterIns);

        BusinessInstanceModelDB recoveryNode = toMasterSite.first();
        BusinessInstanceModelDB recoveryStandNode = toMasterSite.second();
        if (Objects.isNull(recoveryStandNode)) {
            recoveryStandNode = vNodeDockerList.stream()
                .filter(node -> REDUNDANCY_STANDBY.equals(node.getExtentAttr(HW_CURRENT_NODE_ROLE).getAttrValue()))
                .findFirst()
                .orElse(null);
        }
        if (Objects.isNull(masterNode) || Objects.isNull(recoveryNode)) {
            LOGGER.error("[noticeSwitchAlarm] The master or recovery node could not find.");
            return;
        }
        List<BusinessInsExtentAttrDB> attrs = new ArrayList<>();
        loadNodeAttrWhenArSwitch(occurTime, attrs, masterNode, recoveryNode, standNode, recoveryStandNode);
        extentAttrDao.updateInstanceAttr(attrs);
        // 更新主容数量
        updateSwitchCount(masterNode, recoveryNode, AR_SWITCH_COUNT_DB);
    }

    private void updateRaRsRoleAndAsCount(String vNodeId, long occurTime) throws ServiceException {
        List<BusinessInstanceModelDB> vNodeDockerList = instanceModelDao.queryInstanceByExtendAttrValue(HW_VNODE_ID,
            vNodeId);
        // 查找当前切换为主备的
        BusinessInstanceModelDB rMasterNode = vNodeDockerList.stream()
            .filter(node -> REDUNDANCY_ACTIVE.equals(node.getExtentAttr(HW_CURRENT_NODE_ROLE).getAttrValue()))
            .findFirst()
            .orElse(null);
        BusinessInstanceModelDB rStandNode = vNodeDockerList.stream()
            .filter(node -> REDUNDANCY_STANDBY.equals(node.getExtentAttr(HW_CURRENT_NODE_ROLE).getAttrValue()))
            .findFirst()
            .orElse(null);
        if (Objects.isNull(rMasterNode) || Objects.isNull(rStandNode)) {
            LOGGER.error("[noticeSwitchAlarm] The rMaster or rStand node could not find.");
            return;
        }
        // 更新容主容备角色
        List<BusinessInsExtentAttrDB> attrs = new ArrayList<>();
        attrs.add(new BusinessInsExtentAttrDB(rMasterNode.getInstanceId(), HW_CURRENT_NODE_ROLE, STRING_CLASS,
            REDUNDANCY_STANDBY));
        attrs.add(new BusinessInsExtentAttrDB(rMasterNode.getInstanceId(), AS_SWITCH_TIME, STRING_CLASS,
            String.valueOf(occurTime)));
        attrs.add(new BusinessInsExtentAttrDB(rMasterNode.getInstanceId(), LAST_UPDATE_SWITCH_TIME, STRING_CLASS,
            String.valueOf(occurTime)));
        attrs.add(new BusinessInsExtentAttrDB(rStandNode.getInstanceId(), HW_CURRENT_NODE_ROLE, STRING_CLASS,
            REDUNDANCY_ACTIVE));
        attrs.add(new BusinessInsExtentAttrDB(rStandNode.getInstanceId(), AS_SWITCH_TIME, STRING_CLASS,
            String.valueOf(occurTime)));
        attrs.add(new BusinessInsExtentAttrDB(rStandNode.getInstanceId(), LAST_UPDATE_SWITCH_TIME, STRING_CLASS,
            String.valueOf(occurTime)));
        extentAttrDao.updateInstanceAttr(attrs);
        // 更新主备数量
        updateSwitchCount(rMasterNode, rStandNode, AS_SWITCH_COUNT_DB);
    }

    private void loadNodeAttrWhenArSwitch(long occurTime, List<BusinessInsExtentAttrDB> attrs,
        BusinessInstanceModelDB masterNode, BusinessInstanceModelDB recoveryNode, BusinessInstanceModelDB standNode,
        BusinessInstanceModelDB recoveryStandNode) {
        attrs.add(new BusinessInsExtentAttrDB(masterNode.getInstanceId(), HW_CURRENT_NODE_ROLE, STRING_CLASS,
            UNKNOWN_STATUS));
        attrs.add(new BusinessInsExtentAttrDB(masterNode.getInstanceId(), AR_SWITCH_TIME, STRING_CLASS,
            String.valueOf(occurTime)));
        attrs.add(new BusinessInsExtentAttrDB(masterNode.getInstanceId(), LAST_UPDATE_SWITCH_TIME, STRING_CLASS,
            String.valueOf(occurTime)));
        attrs.add(
            new BusinessInsExtentAttrDB(recoveryNode.getInstanceId(), HW_CURRENT_NODE_ROLE, STRING_CLASS, ACTIVE));
        attrs.add(new BusinessInsExtentAttrDB(recoveryNode.getInstanceId(), AR_SWITCH_TIME, STRING_CLASS,
            String.valueOf(occurTime)));
        attrs.add(new BusinessInsExtentAttrDB(recoveryNode.getInstanceId(), LAST_UPDATE_SWITCH_TIME, STRING_CLASS,
            String.valueOf(occurTime)));

        if (!Objects.isNull(standNode)) {
            attrs.add(new BusinessInsExtentAttrDB(standNode.getInstanceId(), HW_CURRENT_NODE_ROLE, STRING_CLASS,
                UNKNOWN_STATUS));
            attrs.add(new BusinessInsExtentAttrDB(standNode.getInstanceId(), AR_SWITCH_TIME, STRING_CLASS,
                String.valueOf(occurTime)));
            attrs.add(new BusinessInsExtentAttrDB(standNode.getInstanceId(), LAST_UPDATE_SWITCH_TIME, STRING_CLASS,
                String.valueOf(occurTime)));
        }
        if (!Objects.isNull(recoveryStandNode)) {
            attrs.add(new BusinessInsExtentAttrDB(recoveryStandNode.getInstanceId(), HW_CURRENT_NODE_ROLE, STRING_CLASS,
                STANDBY));
            attrs.add(new BusinessInsExtentAttrDB(recoveryStandNode.getInstanceId(), AR_SWITCH_TIME, STRING_CLASS,
                String.valueOf(occurTime)));
            attrs.add(
                new BusinessInsExtentAttrDB(recoveryStandNode.getInstanceId(), LAST_UPDATE_SWITCH_TIME, STRING_CLASS,
                    String.valueOf(occurTime)));
        }
    }

    private Pair<BusinessInstanceModelDB, BusinessInstanceModelDB> distinguishingMasterAndStandNode(
        List<BusinessInstanceModelDB> recoveryNodeList, BusinessInstanceModelDB toMasterIns) {
        // 当主容切换且主备状态均为容主状态时处理
        if (recoveryNodeList.size() == 1) {
            return new Pair<>(recoveryNodeList.get(0), null);
        } else if (recoveryNodeList.isEmpty()) {
            return new Pair<>(null, null);
        } else {
            BusinessInstanceModelDB toStandIns = recoveryNodeList.stream()
                .filter(node -> !Objects.equals(node.getInstanceId(), toMasterIns.getInstanceId()))
                .findFirst()
                .orElse(null);
            return new Pair<>(toMasterIns, toStandIns);
        }
    }

    private void updateSwitchCount(BusinessInstanceModelDB masterNode, BusinessInstanceModelDB switchNode, String switchCountType)
        throws ServiceException {
        Integer switchSiteId = relationDao.queryRelationByType(switchNode.getInstanceId(),
            BusinessTopoConstant.RELATION_TYPE_SITE);
        BusinessInstanceModelDB switchSiteIns = instanceModelDao.queryInstanceByInstanceId(switchSiteId);

        String currentRole = switchNode.getExtentAttr(HW_CURRENT_NODE_ROLE).getAttrValue();
        String designRole = switchNode.getExtentAttr(HW_DESIGN_NODE_ROLE).getAttrValue();

        Integer grandRelation = relationDao.queryGrandRelationByType(switchNode.getInstanceId(), 0);
        BusinessInstanceModelDB clusterDb = instanceModelDao.queryInstanceByInstanceId(grandRelation);
        // 1: 物理库 2: 内存库
        String applicationType = clusterDb.getStaticExtentAttr("applicationType").getStaticAttrValue();
        int applicationTypeInt = Integer.parseInt(applicationType);
        if (BusinessTopoConstant.APPLICATION_TYPE_ZERO == applicationTypeInt) {
            return;
        }
        boolean equals = getSwitchCalculateResult(currentRole, designRole);
        if (StringUtils.isNotEmpty(currentRole) && StringUtils.isNotEmpty(designRole)) {
            if (BusinessTopoConstant.PHYSICAL_DATABASE == applicationTypeInt) {
                upDateSwitchCount(switchCountType, switchSiteId, switchSiteIns, equals, PHYSICAL_DATABASE);
            } else if (BusinessTopoConstant.MEMORY_DATABASE == applicationTypeInt) {
                upDateSwitchCount(switchCountType, switchSiteId, switchSiteIns, equals, MEMORY_DATABASE);
            }
        }
        if (AR_SWITCH_COUNT_DB.equals(switchCountType)) {
            Integer masterSiteId = relationDao.queryRelationByType(masterNode.getInstanceId(),
                BusinessTopoConstant.RELATION_TYPE_SITE);
            if (BusinessTopoConstant.PHYSICAL_DATABASE == applicationTypeInt) {
                upDateSwitchCount(switchCountType, masterSiteId, switchSiteIns, equals, PHYSICAL_DATABASE);
            } else if (BusinessTopoConstant.MEMORY_DATABASE == applicationTypeInt) {
                upDateSwitchCount(switchCountType, masterSiteId, switchSiteIns, equals, MEMORY_DATABASE);
            }
        }
    }

    private void upDateSwitchCount(String switchCountType, Integer switchSiteId, BusinessInstanceModelDB switchSiteIns,
        boolean equals, String physicalDatabase) {
        int switchCount;
        String switchCountValue = switchSiteIns.getExtentAttr(physicalDatabase + switchCountType).getAttrValue();
        switchCount = StringUtils.isEmpty(switchCountValue) ? 0 : Integer.parseInt(switchCountValue);
        if (equals) {
            instanceModelDao.updateAttrValue(switchSiteId, physicalDatabase + switchCountType,
                String.valueOf(switchCount + 1));
        } else {
            instanceModelDao.updateAttrValue(switchSiteId, physicalDatabase + switchCountType,
                String.valueOf(Math.max(switchCount - 1, 0)));
        }
    }

    private boolean getSwitchCalculateResult(String currentRole, String designRole) {
        if (STANDBY.equals(currentRole)) {
            switch (designRole) {
                case ACTIVE:
                case REDUNDANCY_ACTIVE:
                    return false;
                case STANDBY:
                case REDUNDANCY_STANDBY:
                    return true;
            }
        } else {
            switch (designRole) {
                case ACTIVE:
                case STANDBY:
                    return false;
                case REDUNDANCY_ACTIVE:
                case REDUNDANCY_STANDBY:
                    return true;
            }
        }
        return false;
    }

    private void updateMemberAlarmStatus(List<AlarmDetail> alarmDetailList) throws ServiceException {
        if (CollectionUtils.isEmpty(alarmDetailList)) {
            return;
        }
        // 更新状态
        Map<Integer, List<AlarmDetail>> dnAlarmMap = alarmDetailList.stream()
            .filter(ins -> Objects.nonNull(ins.getInstanceId()))
            .collect(Collectors.groupingBy(AlarmDetail::getInstanceId));

        for (Map.Entry<Integer, List<AlarmDetail>> entry : dnAlarmMap.entrySet()) {
            Integer instanceId = entry.getKey();
            List<BusinessInsExtentAttrDB> updateList = new ArrayList<>();
            BusinessInstanceModelDB currentIns = instanceModelDao.queryInstanceByInstanceId(instanceId);
            if (Objects.isNull(currentIns)) {
                LOGGER.warn("can't find current instance, instanceId = {}", instanceId);
                continue;
            }
            updateAlarmStatusUntilTop(currentIns, entry.getValue(), updateList, 0);
            instanceModelDao.updateInstanceAttr(updateList);
        }
    }

    /**
     * 处理消亡网元告警
     *
     * @param vanishedAlarmDetailList 消亡网元告警列表
     * @throws ServiceException ex
     */
    private void updateVanishedMemberAlarm(List<AlarmDetail> vanishedAlarmDetailList) throws ServiceException {
        if (CollectionUtils.isEmpty(vanishedAlarmDetailList)) {
            return;
        }
        // 网元消亡告警会传入绑定实例的Id
        Set<Integer> vanishedInsIdList = vanishedAlarmDetailList.stream()
            .map(AlarmDetail::getInstanceId)
            .filter(Objects::nonNull)
            .collect(Collectors.toSet());
        List<BusinessInstanceModelDB> instanceModelList = instanceModelDao.queryInstanceListByIdList(
            new ArrayList<>(vanishedInsIdList), 0L);
        // 更新状态
        Map<Integer, List<AlarmDetail>> dnAlarmMap = vanishedAlarmDetailList.stream()
            .collect(Collectors.groupingBy(AlarmDetail::getInstanceId));

        for (BusinessInstanceModelDB ins : instanceModelList) {
            List<BusinessInsExtentAttrDB> updateList = new ArrayList<>();
            BusinessInstanceModelDB currentIns = instanceModelDao.queryInstanceByInstanceId(ins.getInstanceId());
            if (Objects.isNull(currentIns)) {
                LOGGER.error("can't find current instance, dn = {}", ins.getDn());
                continue;
            }
            List<AlarmDetail> alarmDetails = dnAlarmMap.get(ins.getInstanceId());
            updateAlarmStatusUntilTop(currentIns, alarmDetails, updateList, 0);
            instanceModelDao.updateInstanceAttr(updateList);
        }
    }

    private void updateAlarmStatusUntilTop(BusinessInstanceModelDB instance, List<AlarmDetail> alarmDetails,
        List<BusinessInsExtentAttrDB> updateList, int deep) throws ServiceException {
        // 处理并获取自身告警
        addMemberAlarmStatusList(instance, alarmDetails, updateList, deep);
        // 获取父节点
        BusinessInstanceModelDB parentIns = getParentIns(instance, alarmDetails);
        if (parentIns == null || instance.getModelType() <= BusinessTopoConstant.SITE_TYPE_ID) {
            return;
        }
        updateAlarmStatusUntilTop(parentIns, alarmDetails, updateList, deep + 1);
    }

    /**
     * 网元删除时，清理告警
     *
     * @param instanceId 被删网元的实例id
     */
    public void updateMemberAlarmStatusWhenExtinction(Integer instanceId) {
        BusinessInstanceModelDB businessInstanceModelDB = instanceModelDao.queryInstanceByInstanceId(instanceId);
        if (Objects.isNull(businessInstanceModelDB)) {
            LOGGER.info("businessInstanceModelDB is empty, nothing to update! instanceId: {}", instanceId);
            return;
        }
        List<BusinessInsExtentAttrDB> attrDBList = businessInstanceModelDB.getAttrDBList();
        if (CollectionUtils.isEmpty(attrDBList)) {
            LOGGER.info("attrDBList is empty, nothing to update! instanceId: {}", instanceId);
            return;
        }
        String alarmCsn = businessInstanceModelDB.getExtentAttr(ALARM_CSN).getAttrValue();
        Set<String> alarmCsnList = null;
        if (StringUtils.isNotEmpty(alarmCsn)) {
            alarmCsnList = new HashSet<>(Arrays.asList(alarmCsn.split(",")));
        }
        String eventCsn = businessInstanceModelDB.getExtentAttr(EVENT_CSN).getAttrValue();
        Set<String> eventCsnList = null;
        if (StringUtils.isNotEmpty(eventCsn)) {
            eventCsnList = new HashSet<>(Arrays.asList(eventCsn.split(",")));
        }
        // 清空当前的
        List<Integer> instanceIdList = new ArrayList<>();
        instanceIdList.add(instanceId);
        instanceModelDao.deleteInstanceAttr(instanceIdList);
        // 更新父类的
        recursionUpdateMemberAlarmStatus(instanceId, alarmCsnList, eventCsnList);
    }

    public void recursionUpdateMemberAlarmStatus(Integer instanceId, Set<String> alarmCsnList,
        Set<String> eventCsnList) {
        BusinessInstanceModelDB instance = instanceModelDao.queryUpLevelInstance(instanceId);
        if (instance == null || instance.getModelType() < BusinessTopoConstant.SITE_TYPE_ID) {
            return;
        }
        String existAlarmCsnStr = instanceModelDao.queryAttrValue(instance.getInstanceId(), 0L, ALARM_CSN);
        if (StringUtils.isNotEmpty(existAlarmCsnStr) && CollectionUtils.isNotEmpty(alarmCsnList)) {
            Set<String> existAlarmCsnSet = new HashSet<>(Arrays.asList(existAlarmCsnStr.split(",")));
            existAlarmCsnSet.removeAll(alarmCsnList);
            Integer alarmCount = existAlarmCsnSet.size();
            instanceModelDao.updateAttrValue(instance.getInstanceId(), ALARM_CSN, String.join(",", existAlarmCsnSet));
            instanceModelDao.updateAttrValue(instance.getInstanceId(), ALARM_COUNT, String.valueOf(alarmCount));
        }
        // 更新eventList
        String existEventCsnListStr = instanceModelDao.queryAttrValue(instance.getInstanceId(), 0L, EVENT_CSN);
        if (StringUtils.isNotEmpty(existEventCsnListStr) && CollectionUtils.isNotEmpty(eventCsnList)) {
            Set<String> existEventCsnListSet = new HashSet<>(Arrays.asList(existEventCsnListStr.split(",")));
            existEventCsnListSet.removeAll(eventCsnList);
            instanceModelDao.updateAttrValue(instance.getInstanceId(), EVENT_CSN,
                String.join(",", existEventCsnListSet));
        }
        recursionUpdateMemberAlarmStatus(instance.getInstanceId(), alarmCsnList, eventCsnList);
    }

    private BusinessInstanceModelDB getParentIns(BusinessInstanceModelDB instance, List<AlarmDetail> alarmDetails)
        throws ServiceException {
        AlarmDetail alarmDetail = alarmDetails.get(0);
        String moType = alarmDetail.getAlarmGroupId();
        BusinessInstanceModelDB parentIns = null;
        // 容器告警需要汇聚至Pod层，虚机告警汇聚值集群层
        if (CONTAINER_TYPE.equals(moType)) {
            List<Integer> deployInsIdList = relationDao.queryAntiRelationByType(instance.getInstanceId(), 1);
            Integer deployInsId = CollectionUtils.isEmpty(deployInsIdList)
                ? null
                : deployInsIdList.stream().sorted().collect(Collectors.toList()).get(0);
            if (deployInsId != null) {
                BusinessInstanceModelDB deployIns = instanceModelDao.queryInstanceByInstanceId(deployInsId);
                parentIns = instanceModelDao.queryUpLevelInstance(deployIns.getInstanceId());
                // 需要将所有告警的网元类型进行修改
                alarmDetails.forEach(alarm -> alarm.setAlarmGroupId(SYSTEM_TYPE));
            }
        } else if (VM_TYPE.equals(moType)) {
            // 虚机告警不汇聚至上层
            return parentIns;
        } else {
            parentIns = instanceModelDao.queryUpLevelInstance(instance.getInstanceId());
        }
        return parentIns;
    }

    private void addMemberAlarmStatusList(BusinessInstanceModelDB instance, List<AlarmDetail> alarmDetails,
        List<BusinessInsExtentAttrDB> updateList, int deep) {
        if (!CONVERGENCE_LEVEL.contains(instance.getModelType())) {
            return;
        }

        BusinessInsExtentAttrDB alarmCsnList = instance.getExtentAttr(ALARM_CSN);
        BusinessInsExtentAttrDB eventCsnList = instance.getExtentAttr(EVENT_CSN);
        BusinessInsExtentAttrDB alarmCsnCount = instance.getExtentAttr(ALARM_COUNT);
        if (alarmCsnList.getAttrName() == null) {
            alarmCsnList = initInsAlarmExtentAttr(instance.getInstanceId(), ALARM_CSN);
        }
        if (eventCsnList.getAttrName() == null) {
            eventCsnList = initInsAlarmExtentAttr(instance.getInstanceId(), EVENT_CSN);
        }
        alarmCsnCount = initInsAlarmExtentAttr(instance.getInstanceId(), ALARM_COUNT);
        Set<String> existCsnList = new HashSet<>();
        if (StringUtils.isNotEmpty(alarmCsnList.getAttrValue())) {
            existCsnList.addAll(Arrays.asList(alarmCsnList.getAttrValue().split(",")));
        }
        Set<String> existEventCsnList = new HashSet<>();
        if (StringUtils.isNotEmpty(eventCsnList.getAttrValue())) {
            existEventCsnList.addAll(Arrays.asList(eventCsnList.getAttrValue().split(",")));
        }

        operationCsnList(alarmDetails, existCsnList, existEventCsnList);

        alarmCsnList.setInstanceId(instance.getInstanceId());
        alarmCsnList.setAttrValue(String.join(",", existCsnList));
        eventCsnList.setInstanceId(instance.getInstanceId());
        eventCsnList.setAttrValue(String.join(",", existEventCsnList));
        alarmCsnCount.setInstanceId(instance.getInstanceId());
        alarmCsnCount.setAttrValue(String.valueOf(existCsnList.size()));
        updateList.add(alarmCsnList);
        updateList.add(eventCsnList);
        updateList.add(alarmCsnCount);
        // 为自身层级时更新
        if (deep == 0) {
            updateOwnCsn(instance, alarmDetails, updateList);
        }
    }

    private void updateOwnCsn(BusinessInstanceModelDB instance, List<AlarmDetail> alarmDetails,
        List<BusinessInsExtentAttrDB> updateList) {
        BusinessInsExtentAttrDB ownCsnList = instance.getExtentAttr(OWN_CSN);
        Set<String> existOwnCsnList = new HashSet<>();
        if (Objects.isNull(ownCsnList.getAttrName())) {
            ownCsnList = initInsAlarmExtentAttr(instance.getInstanceId(), OWN_CSN);
        }
        if (StringUtils.isNotEmpty(ownCsnList.getAttrValue())) {
            existOwnCsnList.addAll(Arrays.asList(ownCsnList.getAttrValue().split(",")));
        }
        operationCsnList(alarmDetails, existOwnCsnList, new HashSet<>());
        ownCsnList.setInstanceId(instance.getInstanceId());
        ownCsnList.setAttrValue(String.join(",", existOwnCsnList));
        ownCsnList.setInstanceId(instance.getInstanceId());
        updateList.add(ownCsnList);
    }

    private void businessCalculateCsnList(List<AlarmDetail> alarmDetails, Set<String> csnList) {
        operationCsnList(alarmDetails, csnList, new HashSet<>());
    }

    /**
     * 告警记录以及告警清理
     *
     * @param alarmDetails 告警列表
     * @param csnList 告警流水号列表，包括以前发生的告警
     * @param eventCsnList 事件流水号，包括以前发生的事件
     */
    private void operationCsnList(List<AlarmDetail> alarmDetails, Set<String> csnList, Set<String> eventCsnList) {
        Map<Integer, List<AlarmDetail>> categoryGroup = alarmDetails.stream()
            .collect(Collectors.groupingBy(AlarmDetail::getCategory));
        for (Map.Entry<Integer, List<AlarmDetail>> entry : categoryGroup.entrySet()) {
            List<String> alarmCsnList = entry.getValue()
                .stream()
                .map(AlarmDetail::getCsn)
                .map(String::valueOf)
                .collect(Collectors.toList());
            LOGGER.debug("[operationCsnList], category = {}, alarm csn is {}", entry.getKey(),
                String.join(",", alarmCsnList));
        }
        List<AlarmDetail> addAlarmList = categoryGroup.getOrDefault(AlarmConstant.ALARM_OCCUR, Collections.emptyList());
        if (CollectionUtils.isNotEmpty(addAlarmList)) {
            csnList.addAll(
                addAlarmList.stream().map(AlarmDetail::getCsn).map(String::valueOf).collect(Collectors.toSet()));
        }
        List<AlarmDetail> addEventList = categoryGroup.getOrDefault(AlarmConstant.ALARM_EVENT, Collections.emptyList());
        if (CollectionUtils.isNotEmpty(addEventList)) {
            eventCsnList.addAll(
                addEventList.stream().map(AlarmDetail::getCsn).map(String::valueOf).collect(Collectors.toSet()));
        }
        List<AlarmDetail> removeAlarmList = categoryGroup.getOrDefault(AlarmConstant.ALARM_CLEAR,
            Collections.emptyList());
        if (CollectionUtils.isNotEmpty(removeAlarmList)) {
            csnList.removeAll(
                removeAlarmList.stream().map(AlarmDetail::getCsn).map(String::valueOf).collect(Collectors.toSet()));
        }
    }

    private BusinessInsExtentAttrDB initInsAlarmExtentAttr(Integer instanceId, String attrName) {
        BusinessInsExtentAttrDB attr = new BusinessInsExtentAttrDB();
        attr.setInstanceId(instanceId);
        attr.setAttrName(attrName);
        if (ALARM_COUNT.equals(attrName)) {
            attr.setAttrClass(INTEGER_TYPE);
            attr.setAttrValue("0");
        } else if (ALARM_CSN.equals(attrName)) {
            attr.setAttrClass(STRING_TYPE);
            attr.setAttrValue(null);
        } else if (EVENT_CSN.equals(attrName)) {
            attr.setAttrClass(STRING_TYPE);
            attr.setAttrValue(null);
        } else {
            attr.setAttrClass(STRING_TYPE);
            attr.setAttrValue(null);
            return attr;
        }
        return attr;
    }

    public void noticeBusinessTopoByAlarmId(List<AlarmDetail> alarmDetailList) {
        insertAlarmByMoType(alarmDetailList);
    }

    public void insertAlarmByMoType(List<AlarmDetail> alarmDetailList) {
        List<ModelAlarm> timeLineMoTypeList = alarmDao.queryListeningMoType(2);
        if (CollectionUtils.isEmpty(timeLineMoTypeList)) {
            return;
        }
        List<Integer> timeLineAlarmId = timeLineMoTypeList.stream()
            .map(ModelAlarm::getAlarmId).collect(Collectors.toList());
        List<AlarmDetail> timeLineAlarmListByMoType = alarmDetailList.stream()
            .filter(alarm -> timeLineAlarmId.contains(alarm.getAlarmId()))
            .collect(Collectors.toList());
        timeLineAlarmListByMoType.forEach(alarm -> alarm.setInstanceId(0));
        alarmDao.batchInsertAlarmDetail(new ArrayList<>(timeLineAlarmListByMoType));
    }

    /**
     * noticeBusinessThresholdsAlarm 南北向业务阈值、智能运维告警
     *
     * @param alarmDetails alarmDetailList
     * @throws ServiceException ServiceException
     */
    @Transactional(rollbackFor = ServiceException.class)
    public void noticeBusinessThresholdsAlarm(List<AlarmDetail> alarmDetails) throws ServiceException {
        if (CollectionUtils.isEmpty(alarmDetails)) {
            return;
        }
        LOGGER.debug("[AlarmNotificationService] noticeBusinessThresholdsAlarm start.");
        initParseColumnAndEnv();
        Set<AlarmDetail> alarmOccurList = businessTopoAlarmStorageService.filterOccurAndClearedAlarmInShortTime(new HashSet<>(alarmDetails));
        // 获取所有南北向业务和接入渠道的指标
        List<BusinessInstanceModelDB> instanceThDBList = getListeningIndicatorList();

        List<Integer> instanceIdList = instanceThDBList.stream()
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toList());

        List<BusinessIndicator> indicators = indicatorDao.queryIndicatorByInstanceList(instanceIdList);
        // 南北向业务，匹配到的告警列表
        Map<Integer, List<AlarmDetail>> businessAlarmMap = new HashMap<>();
        // 指标Id, 匹配到的告警列表
        Map<String, List<AlarmDetail>> indicatorAlarmMap = new HashMap<>();

        // 区分阈值告警和智能运维告警，匹配方式不同
        Set<Integer> thresholdsAlarmIds = performanceThresholdAlarmService.getBusinessThresholdAlarmIdList();
        Set<Integer> analysisAlarmIds = analysisIndQueryTimer.queryAnalysisAlarmList();

        List<AlarmDetail> thresholdsDetailList = alarmOccurList.stream()
            .filter(alarm -> thresholdsAlarmIds.contains(alarm.getAlarmId()))
            .collect(Collectors.toList());
        matchThresholdAlarmOfInd(thresholdsDetailList, indicators, businessAlarmMap, indicatorAlarmMap);

        List<AlarmDetail> analysisDetailList = alarmOccurList.stream()
            .filter(alarm -> analysisAlarmIds.contains(alarm.getAlarmId()))
            .collect(Collectors.toList());
        matchAnalysisAlarmOfInd(analysisDetailList, indicators, businessAlarmMap, indicatorAlarmMap);

        // 更新南北向业务实例
        instanceThDBList = instanceThDBList.stream()
            .filter(ins -> THRESHOLD_LEVEL.contains(ins.getModelType()))
            .collect(Collectors.toList());
        List<BusinessInsExtentAttrDB> updateList = getUpdateBusinessInsList(businessAlarmMap, instanceThDBList);
        // 更新指标状态
        for (Map.Entry<String, List<AlarmDetail>> entry : indicatorAlarmMap.entrySet()) {
            BusinessIndicator indicator = indicators.stream()
                .filter(ind -> Objects.equals(ind.getIndicatorId(), entry.getKey()))
                .findFirst()
                .orElse(null);
            if (Objects.isNull(indicator)) {
                continue;
            }

            Set<String> csnList = StringUtils.isEmpty(indicator.getIndicatorCsnList())
                ? new HashSet<>()
                : new HashSet<>(Arrays.asList(indicator.getIndicatorCsnList().split(",")));
            businessCalculateCsnList(entry.getValue(), csnList);
            indicator.setIndicatorStatus(CollectionUtils.isNotEmpty(csnList));
            indicator.setIndicatorCsnList(String.join(",", csnList));
            indicatorDao.updateIndicatorStatus(Collections.singletonList(indicator));
        }
        extentAttrDao.updateInstanceAttr(updateList);
    }

    private List<BusinessInstanceModelDB> getListeningIndicatorList() {
        List<BusinessInstanceModelDB> instanceThDBList = new ArrayList<>();
        List<BusinessInstanceModelDB> businessInstanceList = instanceModelDao.queryAllInstanceOfType(
            BusinessConfigEnum.BUSINESS_CONFIG_ENUM.getModeType());
        List<BusinessInstanceModelDB> channelInstanceList = instanceModelDao.queryAllInstanceOfType(
            BusinessConfigEnum.CHANNEL_CONFIG_ENUM.getModeType());
        List<BusinessInstanceModelDB> siteInstanceList = instanceModelDao.queryAllInstanceOfType(
            BusinessConfigEnum.SITE_DATA_SOURCE_CONFIG_ENUM.getModeType());
        instanceThDBList.addAll(businessInstanceList);
        instanceThDBList.addAll(channelInstanceList);
        instanceThDBList.addAll(siteInstanceList);
        return instanceThDBList;
    }

    private List<BusinessInsExtentAttrDB> getUpdateBusinessInsList(Map<Integer, List<AlarmDetail>> businessAlarmMap,
        List<BusinessInstanceModelDB> businessInstanceList) {
        List<BusinessInsExtentAttrDB> updateList = new ArrayList<>();
        for (Map.Entry<Integer, List<AlarmDetail>> entry : businessAlarmMap.entrySet()) {
            BusinessInstanceModelDB instance = businessInstanceList.stream()
                .filter(business -> Objects.equals(business.getInstanceId(), entry.getKey()))
                .findFirst()
                .orElse(null);
            if (Objects.isNull(instance)) {
                continue;
            }
            addMemberAlarmStatusList(instance, entry.getValue(), updateList, 1);
        }
        return updateList;
    }

    private void matchThresholdAlarmOfInd(List<AlarmDetail> thresholdsDetailList, List<BusinessIndicator> indicators,
        Map<Integer, List<AlarmDetail>> businessAlarmMap, Map<String, List<AlarmDetail>> indicatorAlarmMap) {
        Set<String> indicatorDnList = indicators.stream().map(BusinessIndicator::getDn).collect(Collectors.toSet());
        for (AlarmDetail thresholdsAlarm : thresholdsDetailList) {
            if (!indicatorDnList.contains(thresholdsAlarm.getNativeMeDn())) {
                continue;
            }
            // 查找此告警的阈值指标信息
            ThresholdStaticInfo indicatorInfo = performanceThresholdAlarmService.getIndicatorInfoByAlarmId(
                thresholdsAlarm.getAlarmId());
            if (Objects.isNull(indicatorInfo)) {
                continue;
            }

            // 匹配指标Dn\指标信息
            List<BusinessIndicator> matchedIndicatorList = indicators.stream()
                .filter(ind -> ind.getDn().equals(thresholdsAlarm.getNativeMeDn()))
                .filter(ind -> ind.getMoType().equals(indicatorInfo.getGroupId()))
                .filter(ind -> ind.getMeasUnitKey().equals(indicatorInfo.getIndexGroup()))
                .filter(ind -> ind.getMeasTypeKey().equals(indicatorInfo.getIndexType()))
                .collect(Collectors.toList());

            // 如果测量对象不为空，需要根据告警的定位信息判断是否包含指标的测量对象
            matchedIndicatorList = matchedIndicatorList.stream().filter(ind -> {
                if (StringUtils.isNotEmpty(ind.getOriginalValue())) {
                    if (StringUtils.isNotEmpty(ind.getDisplayValue()) && StringUtils.isNotEmpty(thresholdsAlarm.getMoi())) {
                        String thresholdLocation = extractValueByKeyOrder(thresholdsAlarm.getMoi(), alarmParseKeys, manageObjectColumn);
                        return thresholdLocation.trim().equals(ind.getDisplayValue().trim());
                    } else {
                        return false;
                    }
                }
                return true;
            }).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(matchedIndicatorList)) {
                continue;
            }

            Set<Integer> matchedBusinessIns = matchedIndicatorList.stream()
                .map(BusinessIndicator::getInstanceId)
                .collect(Collectors.toSet());
            // 对于每个匹配到的南北向实例，每个都进行插入告警字段
            matchedBusinessIns.forEach(
                ins -> businessAlarmMap.computeIfAbsent(ins, k -> new ArrayList<>()).add(thresholdsAlarm));

            Set<String> matchedIndicatorIdList = matchedIndicatorList.stream()
                .map(BusinessIndicator::getIndicatorId)
                .collect(Collectors.toSet());
            // 对于每个匹配到指标，都获取告警字段
            matchedIndicatorIdList.forEach(
                ins -> indicatorAlarmMap.computeIfAbsent(ins, k -> new ArrayList<>()).add(thresholdsAlarm));
        }
    }

    private void matchAnalysisAlarmOfInd(List<AlarmDetail> analysisDetailList, List<BusinessIndicator> indicators,
        Map<Integer, List<AlarmDetail>> businessAlarmMap, Map<String, List<AlarmDetail>> indicatorAlarmMap) {
        Set<String> indicatorDnList = indicators.stream().map(BusinessIndicator::getDn).collect(Collectors.toSet());
        for (AlarmDetail analysisAlarm : analysisDetailList) {
            if (!indicatorDnList.contains(analysisAlarm.getNativeMeDn())) {
                continue;
            }
            List<BusinessTopoIndicator> topoIndicators = analysisIndQueryTimer.getMatchedIndicatorListByAlarmId(
                analysisAlarm.getAlarmId());
            if (CollectionUtils.isEmpty(topoIndicators)) {
                continue;
            }
            // 根据告警，通过可能发生的智能运维告警，上报
            List<BusinessIndicator> matchedIndicatorList = indicators.stream()
                .filter(ind -> ind.getDn().equals(analysisAlarm.getNativeMeDn()))
                .filter(ind -> filterIndicator(topoIndicators, ind))
                .collect(Collectors.toList());
            // 如果测量对象不为空，需要根据告警的定位信息判断是否包含指标的测量对象
            matchedIndicatorList = matchedIndicatorList.stream().filter(ind -> {
                if (StringUtils.isNotEmpty(ind.getOriginalValue())) {
                    if (StringUtils.isNotEmpty(ind.getDisplayValue()) && StringUtils.isNotEmpty(analysisAlarm.getMoi())) {
                        String analysisLocation = extractValueByKeyOrder(analysisAlarm.getMoi(), alarmParseKeys, manageObjectColumn);
                        return analysisLocation.trim().equals(ind.getDisplayValue().trim());
                    } else {
                        return false;
                    }
                }
                return true;
            }).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(matchedIndicatorList)) {
                continue;
            }

            Set<Integer> matchedBusinessIns = matchedIndicatorList.stream()
                .map(BusinessIndicator::getInstanceId)
                .collect(Collectors.toSet());
            // 对于每个匹配到的南北向实例，每个都进行插入告警字段
            matchedBusinessIns.forEach(
                ins -> businessAlarmMap.computeIfAbsent(ins, k -> new ArrayList<>()).add(analysisAlarm));

            Set<String> matchedIndicatorIdList = matchedIndicatorList.stream()
                .map(BusinessIndicator::getIndicatorId)
                .collect(Collectors.toSet());
            // 对于每个匹配到指标，都获取告警字段
            matchedIndicatorIdList.forEach(
                ins -> indicatorAlarmMap.computeIfAbsent(ins, k -> new ArrayList<>()).add(analysisAlarm));
        }
    }

    private boolean filterIndicator(List<BusinessTopoIndicator> analysisIndicators,
        BusinessIndicator businessIndicator) {
        return analysisIndicators.stream()
            .anyMatch(ana -> ana.getMoType().equals(businessIndicator.getMoType()) && ana.getMeasTypeKey()
                .equals(businessIndicator.getMeasTypeKey()) && ana.getMeasUnitKey()
                .equals(businessIndicator.getMeasUnitKey()));
    }

    public void initParseColumnAndEnv() {
        // 最大获取24小时的数据
        ConfigData configData = ConfigurationUtil.getConfigDataByName(AlarmConstant.ALARM_PARSE_COLUMN);
        AlarmMoiParseKeys parseKeyValue = new AlarmMoiParseKeys();
        if (Objects.nonNull(configData) && StringUtils.isNotEmpty(configData.getValue())) {
            parseKeyValue = JSONObject.parseObject(configData.getValue(), AlarmMoiParseKeys.class);
        }
        if (DefaultEnvUtil.getOssLocale().getLanguage().equalsIgnoreCase("zh")) {
            manageObjectColumn = parseKeyValue.getManageObjectColumnCh();
            alarmParseKeys = parseKeyValue.getAlarmParseKeysCh();
        } else {
            manageObjectColumn = parseKeyValue.getManageObjectColumnEn();
            alarmParseKeys = parseKeyValue.getAlarmParseKeysEn();
        }
    }

    public String extractValueByKeyOrder(String input, String[] keys, String targetKey) {
        // 确定目标键在键列表中的索引
        int targetIndex = -1;
        for (int i = 0; i < keys.length; i++) {
            if (keys[i].equals(targetKey)) {
                targetIndex = i;
                break;
            }
        }
        if (targetIndex == -1) {
            return StringUtils.EMPTY; // 目标键不在键列表中
        }

        // 查找目标键位置
        int keyPosition = input.indexOf(targetKey);
        // 目标键不存在于输入中
        if (keyPosition == -1) {
            return StringUtils.EMPTY;
        }

        // 确定下一个键的位置
        int valueEnd = -1;
        for (int i = targetIndex + 1; i < keys.length; i++) {
            String nextKey = keys[i];
            int nextPos = input.indexOf(nextKey, keyPosition + targetKey.length());
            if (nextPos != -1) {
                valueEnd = nextPos;
                break;
            }
        }

        // 提取值
        int valueStart = keyPosition + targetKey.length();
        String value;
        if (valueEnd != -1) {
            value = input.substring(valueStart, valueEnd);
        } else {
            value = input.substring(valueStart);
        }

        // 清理值开头和结尾
        return cleanValue(value);
    }

    private static String cleanValue(String value) {
        // 移除开头的逗号和空格
        while (value.startsWith(",") || value.startsWith(" ")) {
            value = value.substring(1);
        }

        // 移除结尾的逗号和空格
        while (value.endsWith(",") || value.endsWith(" ")) {
            value = value.substring(0, value.length() - 1);
        }

        return value;
    }
}
