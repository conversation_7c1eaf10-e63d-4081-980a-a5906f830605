/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.service;

import static com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant.MM_HWS_STRIPE;
import static com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant.MM_HWS_STRIPE_GROUP_NAME;
import static java.util.stream.Collectors.toCollection;

import com.huawei.bsp.as.util.Pair;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.cbb.common.CollectionUtil;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModel;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceRelationModel;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceRelationType;
import com.huawei.i2000.dvtoposervice.business.pm.AlarmRequestService;
import com.huawei.i2000.dvtoposervice.business.pm.PmIndicatorInstanceService;
import com.huawei.i2000.dvtoposervice.business.pm.PmRequestService;
import com.huawei.i2000.dvtoposervice.business.pm.entity.MeasObjectInfo;
import com.huawei.i2000.dvtoposervice.business.pm.entity.PollerModel;
import com.huawei.i2000.dvtoposervice.business.service.bean.JumpingPath;
import com.huawei.i2000.dvtoposervice.business.service.bean.MoInsPath;
import com.huawei.i2000.dvtoposervice.business.service.bean.PresetSearch;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.BusinessThresholdAlarmStorageService;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.BusinessTopoAlarmStorageService;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.PerformanceThresholdAlarmService;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.TopoImportSolutionAdapter;
import com.huawei.i2000.dvtoposervice.constant.AlarmConstant;
import com.huawei.i2000.dvtoposervice.convert.AlarmConvert;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessAlarmDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessIndicatorDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceRelationDao;
import com.huawei.i2000.dvtoposervice.model.AlarmCountResult;
import com.huawei.i2000.dvtoposervice.model.AlarmQueryData;
import com.huawei.i2000.dvtoposervice.model.AlarmQueryRecord;
import com.huawei.i2000.dvtoposervice.model.AlarmStatisticParam;
import com.huawei.i2000.dvtoposervice.model.AlarmTimeCountData;
import com.huawei.i2000.dvtoposervice.model.BusinessIndicator;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.Indicator;
import com.huawei.i2000.dvtoposervice.model.TimelineQueryParam;
import com.huawei.i2000.dvtoposervice.timedtri.AnalysisIndQueryTimer;
import com.huawei.i2000.dvtoposervice.util.AlarmHandleUtil;
import com.huawei.i2000.dvtoposervice.util.AuthUtils;
import com.huawei.i2000.dvtoposervice.util.ConfigurationUtil;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;
import com.huawei.i2000.dvtoposervice.util.DateUtil;
import com.huawei.i2000.dvtoposervice.util.EamUtil;
import com.huawei.i2000.dvtoposervice.util.PagingUtil;
import com.huawei.i2000.dvtopowebsite.model.InsOfOneMomentParam;
import com.huawei.i2000.dvtopowebsite.model.JumpingParam;
import com.huawei.i2000.dvtopowebsite.model.Paging;
import com.huawei.i2000.dvtopowebsite.model.SearchMoParam;
import com.huawei.i2000.dvtopowebsite.model.TimelineAlarmQueryParam;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.ManagedObject;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * BusinessTopoCommonServiceImpl
 *
 * <AUTHOR>
 * @since 2024/3/28
 */
@Service
public class BusinessTopoCommonServiceImpl implements BusinessTopoCommonService {
    private static final OssLog LOGGER = OssLogFactory.getLogger(BusinessTopoCommonServiceImpl.class);

    private static final String UUID = "UUID";

    private static final String TIME_LINE_RANGE = "timelinerange";

    private static final Integer TIMELINE_ANALYSIS_LABEL = 10;

    private static final int TEN_MINUTES = 10;

    private static final long ONE_DAY = 24 * 60 * 60 * 1000L;

    private static final Integer ONE_BATCH_QUERY = 200;

    private static final Integer ONE_BATCH_UPDATE = 1000;

    private static final Integer MAX_ONCE_QUERY_NUMBER = 10000;

    private static final Integer ONE_QUERY_INS_NUMBER = 50;

    private static final Integer ONE_QUERY_TIME_INS_NUMBER = 500;

    private static final Integer MAX_DEAL_CURRENT_ALARM_COUNT = 20000;

    private static final Integer MAX_TIME_LINE_COUNT = 150000;

    private static final String UNDER_LINE = "_";

    private static final String EMPTY = "dn_is_empty";

    private static final List<Integer> REAL_DN_LIST_LEVEL = Collections.unmodifiableList(Arrays.asList(
        BusinessTopoConstant.SITE_TYPE_ID,
        BusinessTopoConstant.CLUSTER_INSTANCE_ID,
        BusinessTopoConstant.POD_TYPE_ID
    ));

    @Autowired
    BusinessInstanceModelDao modelDao;

    @Autowired
    BusinessInstanceRelationDao relationDao;

    @Autowired
    BusinessAlarmDao alarmDao;

    @Autowired
    AlarmRequestService alarmRequestService;

    @Autowired
    AnalysisIndQueryTimer analysisIndQueryTimer;

    @Autowired
    BusinessIndicatorDao businessIndicatorDao;

    @Autowired
    BusinessCommonModelDao businessCommonModelDao;

    @Autowired
    PerformanceThresholdAlarmService thresholdAlarmService;

    @Autowired
    private BusinessTopoAlarmStorageService businessTopoAlarmStorageService;

    @Autowired
    private BusinessThresholdAlarmStorageService businessThresholdAlarmStorageService;

    @Autowired
    PmRequestService pmRequestService;

    @Autowired
    PmIndicatorInstanceService pmIndicatorInstanceService;

    @Override
    public AlarmCountResult queryTimeLine(TimelineQueryParam param) {
        AlarmCountResult result = new AlarmCountResult();
        // 获取实例
        BusinessInstanceModelDB instance = modelDao.queryInstanceByInstanceId(param.getInstanceId());
        if (Objects.isNull(instance)) {
            LOGGER.warn("[queryTimeLine] could not find the ins, ins id is {}", param.getInstanceId());
            return result;
        }
        // 最大获取24小时的数据
        Pair<LocalDateTime, LocalDateTime> timelineRange = getTimeLineRange();
        result.setStartTime(String.valueOf(Timestamp.valueOf(timelineRange.first()).getTime()));
        result.setEndTime(String.valueOf(Timestamp.valueOf(timelineRange.second()).getTime()));

        Set<Integer> viewInstanceIds = getViewInstanceIdList(instance, param.getStripeUnit());

        // 前台给的时间戳只到分钟，这里需要加1分钟用于查询当前分钟的数据
        Map<Long, AlarmTimeCountData> countDataMap = queryAndCalculateTimeLineCount(timelineRange.first(),
            timelineRange.second(), viewInstanceIds);
        if (!countDataMap.isEmpty()) {
            result.setAlarmTimeCountList(countDataMap.values()
                .stream()
                .filter(countData -> countData.getAlarmCount() > 0)
                .collect(Collectors.toList()));
        }
        LOGGER.methodExit(result);
        return result;
    }

    private Pair<LocalDateTime, LocalDateTime> getTimeLineRange() {
        // 最大获取24小时的数据
        ConfigData configData = ConfigurationUtil.getConfigDataByName(TIME_LINE_RANGE);
        long range = Long.parseLong(configData.getValue());
        // 当前时间往后查询20分钟
        LocalDateTime endLocalTime = LocalDateTime.now().withSecond(0).withNano(0).plusMinutes(20L);
        String endTimeClose = DateUtil.convertTimestamp2FloorMin(Timestamp.valueOf(endLocalTime), 20L);
        // 往前查一天
        endLocalTime = new Timestamp(Long.parseLong(endTimeClose)).toLocalDateTime();
        LocalDateTime beginLocalTime = endLocalTime.minusHours(range);
        return new Pair<>(beginLocalTime, endLocalTime);
    }

    /**
     * 批量查询告警，并逐批生成统计结果
     *
     * @param beginLocalTime 起始时间
     * @param endLocalTime 结束时间
     * @param instanceIdList 实例Id集合
     * @return Map<Long, AlarmTimeCountData> 统计结果
     */
    public Map<Long, AlarmTimeCountData> queryAndCalculateTimeLineCount(LocalDateTime beginLocalTime,
        LocalDateTime endLocalTime, Set<Integer> instanceIdList) {
        // 初始准备
        Map<Long, Pair<Long, Long>> timeRangeMap = splitTimeIntoIntervals(beginLocalTime);
        ConcurrentHashMap<Long, AlarmTimeCountData> timeLineCountMap = initAlarmCountMap(timeRangeMap);
        // 查询并计算结果
        calculateTimeLineCount(Timestamp.valueOf(beginLocalTime).getTime(),
            Timestamp.valueOf(endLocalTime.plusMinutes(1L)).getTime(), timeRangeMap, instanceIdList, timeLineCountMap);
        return timeLineCountMap;
    }

    /**
     * 批量查询告警，并逐批生成统计结果
     *
     * @param startTime 起始时间
     * @param endTime 结束时间
     * @param timeRangeMap 时间范围
     * @param instanceIdSet 实例Id集合
     * @param countMap 统计结果
     */
    public void calculateTimeLineCount(Long startTime, Long endTime,
        Map<Long, Pair<Long, Long>> timeRangeMap, Set<Integer> instanceIdSet, ConcurrentHashMap<Long, AlarmTimeCountData> countMap) {
        List<Integer> instanceIdList = new ArrayList<>(instanceIdSet);
        List<List<Integer>> splitInsList = ListUtils.partition(instanceIdList, ONE_QUERY_INS_NUMBER);

        splitInsList.parallelStream().forEach(splitList -> {
            int onceAlarmQueryCount = 0;
            Paging paging = PagingUtil.getInitPaging(MAX_ONCE_QUERY_NUMBER);
            Integer offset = paging.getPageSize() * (paging.getPageNumber() - 1);
            paging.setOffset(offset);

            while (onceAlarmQueryCount < MAX_ONCE_QUERY_NUMBER) {
                // 查询所有在告警范围内的告警
                Set<AlarmDetail> alarmDetailList = alarmDao.queryAlarmByInsIds(splitList, startTime, endTime, paging);

                onceAlarmQueryCount = alarmDetailList.size();
                if (CollectionUtils.isEmpty(alarmDetailList)) {
                    break;
                }
                // 查出来的告警根据时间片段分开
                Map<Long, Set<AlarmDetail>> alarmsByTimeRange = splitAlarmsByTimeRange(alarmDetailList, timeRangeMap);
                alarmsByTimeRange.entrySet().parallelStream().forEach(entry -> {
                    Long periodStartTime = entry.getKey();
                    Set<AlarmDetail> periodAlarmDetailList = entry.getValue();
                    AlarmTimeCountData periodTimeCountData = countMap.get(periodStartTime);
                    buildAlarmCountByRange(periodAlarmDetailList, periodTimeCountData);
                });

                if (onceAlarmQueryCount < MAX_ONCE_QUERY_NUMBER) {
                    break;
                } else {
                    paging.setPageNumber(paging.getPageNumber() + 1);
                    paging.setOffset(paging.getPageSize() * (paging.getPageNumber() - 1));
                    onceAlarmQueryCount = 0;
                }
            }
        });
    }

    private ConcurrentHashMap<Long, AlarmTimeCountData> initAlarmCountMap(Map<Long, Pair<Long, Long>> timeRangeMap) {
        ConcurrentHashMap<Long, AlarmTimeCountData> alarmTimeCountDataMap = new ConcurrentHashMap<>();
        for (Map.Entry<Long, Pair<Long, Long>> entry : timeRangeMap.entrySet()) {
            AlarmTimeCountData alarmTimeCountData = new AlarmTimeCountData();
            alarmTimeCountData.setOccurTime(String.valueOf(entry.getValue().first()));
            alarmTimeCountData.setAlarmCount(0);
            alarmTimeCountData.setAlarmDetailIds(Collections.synchronizedList(new ArrayList<>()));
            alarmTimeCountDataMap.put(entry.getKey(), alarmTimeCountData);
        }
        return alarmTimeCountDataMap;
    }

    /**
     * 将查询到告警根据时间区间切分
     *
     * @param alarmDetailList 告警列表
     * @param timeSegmentMap 以起始时间的为key，区间范围为value
     * @return Map<Long, Set<AlarmDetail>> 每个区间的告警
     */
    public Map<Long, Set<AlarmDetail>> splitAlarmsByTimeRange(Set<AlarmDetail> alarmDetailList,
        Map<Long, Pair<Long, Long>> timeSegmentMap) {

        Map<Long, Set<AlarmDetail>> result = new HashMap<>();
        // 遍历所有时间片段
        for (Pair<Long, Long> segment : timeSegmentMap.values()) {
            Long startTime = segment.first();
            Long endTime = segment.second();

            // 过滤出当前时间片段内的告警
            Set<AlarmDetail> alarmsInSegment = alarmDetailList.stream()
                .filter(alarm -> isInTimeRange(alarm.getOccurTime(), startTime, endTime))
                .collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(alarmsInSegment)) {
                continue;
            }
            result.put(startTime, alarmsInSegment);
        }
        return result;
    }

    private boolean isInTimeRange(Long occurTime, Long start, Long end) {
        return occurTime != null && occurTime.compareTo(start) >= 0 && occurTime.compareTo(end) < 0;
    }

    public void buildAlarmCountByRange(Set<AlarmDetail> alarmDetailList, AlarmTimeCountData alarmTimeCountData) {
        // 设置该区间
        int maxCategory = determinePriority(alarmDetailList);
        alarmTimeCountData.setCategory(Optional.ofNullable(alarmTimeCountData.getCategory()).map(category -> {
            if (category == AlarmConstant.ALARM_CLEAR) {
                return maxCategory;
            } else if (maxCategory == AlarmConstant.ALARM_CLEAR) {
                return category;
            } else {
                return Math.max(category, maxCategory);
            }
        }).orElse(maxCategory));
        // 添加告警并去重
        Set<Integer> currentAlarmCsn = new HashSet<>(alarmTimeCountData.getAlarmDetailIds());
        Set<Integer> toAddAlarmCsn = alarmDetailList.stream().map(AlarmDetail::getCsn).collect(Collectors.toSet());
        currentAlarmCsn.addAll(toAddAlarmCsn);
        alarmTimeCountData.setAlarmDetailIds(new ArrayList<>(currentAlarmCsn));
        // 设置告警数量，每次加所有的
        alarmTimeCountData.setAlarmCount(currentAlarmCsn.size());
    }

    /**
     * 判断告警区间在前台的显示颜色
     *
     * @param alarmDetailList 告警列表
     * @return Map<Long, Pair<Long, Long>> 以起始时间的为key，区间范围为value
     */
    public int determinePriority(Set<AlarmDetail> alarmDetailList) {
        // 第一优先级：TIMELINE_ANALYSIS_LABEL
        if (alarmDetailList.stream()
            .anyMatch(alarm -> Objects.nonNull(alarm.getSpecialLabel()) && TIMELINE_ANALYSIS_LABEL.equals(
                alarm.getSpecialLabel()))) {
            return TIMELINE_ANALYSIS_LABEL;
        }
        // 第二优先级：ALARM_EVENT
        if (alarmDetailList.stream().anyMatch(alarm -> AlarmConstant.ALARM_EVENT == alarm.getCategory())) {
            return AlarmConstant.ALARM_EVENT;
        }
        // 第三优先级：ALARM_OCCUR
        if (alarmDetailList.stream().anyMatch(alarm -> AlarmConstant.ALARM_OCCUR == alarm.getCategory())) {
            return AlarmConstant.ALARM_OCCUR;
        }
        // 默认返回值（根据业务补充）
        return AlarmConstant.ALARM_CLEAR;
    }

    /**
     * 从开始时间计算
     *
     * @param beginLocalTime 开始时间
     * @return Map<Long, Pair<Long, Long>> 以起始时间的为key，区间范围为value
     */
    public Map<Long, Pair<Long, Long>> splitTimeIntoIntervals(LocalDateTime beginLocalTime) {
        Map<Long, Pair<Long, Long>> result = new LinkedHashMap<>();
        // 1天切分72个片段（24h * 60min / 20min = 72）
        LocalDateTime periodBeginTime = beginLocalTime;
        for (int i = 0; i < 72; i++) {
            LocalDateTime periodEndTime = periodBeginTime.plusMinutes(20);
            // 转换起止时间为毫秒级时间戳[1,2]
            long startMillis = Timestamp.valueOf(periodBeginTime).getTime();
            long endMillis = Timestamp.valueOf(periodEndTime).getTime();
            // 存储结果
            Pair<Long, Long> interval = new Pair<>(startMillis, endMillis);
            result.put(startMillis, interval);
            periodBeginTime = periodEndTime;
        }
        return result;
    }

    /**
     * 确认某个实例的下时间轴告警的范围
     *
     * @param instance 实例
     * @param stripeUnit 条带单元
     * @return result
     */
    private Set<Integer> getViewInstanceIdList(BusinessInstanceModelDB instance, String stripeUnit) {
        switch (instance.getModelType()) {
            case BusinessTopoConstant.SOLUTION_TYPE_ID:
                // 查询指向解决方案的实例
                return getViewInstanceIdListByTop(instance);
            case BusinessTopoConstant.SITE_GROUP_TYPE_ID:
                // 查询指向条带的实例 -> 查询结束后再次过滤条带单元
                return getViewInstanceIdListByStripe(instance, stripeUnit);
            case BusinessTopoConstant.SITE_TYPE_ID:
                // 查询指向站点的实例
                return getViewInstanceIdListBySite(instance);
            case BusinessTopoConstant.MOTYPE_CLUSTER_TYPE_ID:
                // 查询指标应用类型的实例
                return getViewInstanceIdListByMo(instance, stripeUnit);
            default:
                return Collections.emptySet();
        }
    }

    private Set<Integer> getViewInstanceIdListByTop(BusinessInstanceModelDB instance) {
        // 查询指向解决方案的实例
        List<Integer> subTopInstances = modelDao.queryInstanceByRelationTypeRelation(instance.getInstanceId(),
                BusinessInstanceRelationType.TOP.enumToInt())
            .stream()
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toList());
        // 添加VM和容器
        List<Integer> subHostInstances = getVMAndContainerIdListByUp(instance, BusinessInstanceRelationType.TOP.enumToInt());
        Set<Integer> matchedInsList = new HashSet<>();
        matchedInsList.addAll(subTopInstances);
        matchedInsList.addAll(subHostInstances);
        // 处理cloudDr未建模的告警，默认为0，只在总览视图存在
        matchedInsList.add(0);
        return matchedInsList;
    }

    private Set<Integer> getViewInstanceIdListBySite(BusinessInstanceModelDB instance) {
        // 查询指向站点的实例
        List<Integer> subSiteInstances = modelDao.queryInstanceByRelationTypeRelation(instance.getInstanceId(),
                BusinessInstanceRelationType.SITE.enumToInt())
            .stream()
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toList());

        // 添加VM和容器
        List<Integer> subHostInstances = getVMAndContainerIdListByUp(instance, BusinessInstanceRelationType.SITE.enumToInt());
        Set<Integer> instanceIds = new HashSet<>();
        instanceIds.addAll(subSiteInstances);
        instanceIds.addAll(subHostInstances);
        return instanceIds;
    }

    private Set<Integer> getViewInstanceIdListByStripe(BusinessInstanceModelDB instance, String stripeUnit) {
        // 查询下层条带解决方案实例
        List<BusinessInstanceModelDB> stripeInsList = modelDao.queryNextLevelInstance(instance.getInstanceId());
        Set<Integer> viewInstanceList = new HashSet<>();
        // 上限2次循环
        for (BusinessInstanceModelDB stripeIns : stripeInsList) {
            List<Integer> subSiteInstanceIds = modelDao.queryInstanceByRelationTypeRelation(stripeIns.getInstanceId(),
                    BusinessInstanceRelationType.SITE.enumToInt())
                .stream()
                .map(BusinessInstanceModelDB::getInstanceId)
                .collect(Collectors.toList());
            List<BusinessInstanceModelDB> stripUnitInstances = modelDao.queryInstanceListByIdList(subSiteInstanceIds, 0L)
                .stream()
                .filter(ins -> {
                    if (StringUtils.isNotEmpty(stripeUnit)) {
                        return stripeUnit.equals(ins.getExtentAttr(BusinessTopoConstant.HWS_APP_SITE).getAttrValue());
                    } else {
                        return true;
                    }
                })
                .collect(Collectors.toList());
            List<Integer> stripUnitInstanceIds = stripUnitInstances.stream()
                .map(BusinessInstanceModelDB::getInstanceId)
                .collect(Collectors.toList());
            Set<Integer> podAndDockerInstances = stripUnitInstances.stream()
                .filter(ins -> BusinessTopoConstant.POD_TYPE_ID == ins.getModelType() || BusinessTopoConstant.DOCKER_TYPE_ID == ins.getModelType())
                .map(BusinessInstanceModelDB::getInstanceId)
                .collect(Collectors.toSet());
            // 添加VM和容器
            Set<Integer> insListOfVmOrContainer = new HashSet<>(
                relationDao.queryRelationByTypeBatchTimeLine(new ArrayList<>(podAndDockerInstances),
                    BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT, 0L).values());
            viewInstanceList.addAll(stripUnitInstanceIds);
            viewInstanceList.addAll(insListOfVmOrContainer);
        }
        return viewInstanceList;
    }

    private Set<Integer> getViewInstanceIdListByMo(BusinessInstanceModelDB instance, String stripeUnit) {
        // 查询指标应用类型的实例
        List<BusinessInstanceModelDB> moTypeSubInstances = modelDao.queryNextLevelInstance(instance.getInstanceId());
        // 兼容mm场景
        if (StringUtils.isNotEmpty(stripeUnit)) {
            moTypeSubInstances = moTypeSubInstances.stream()
                .filter(ins -> stripeUnit.equals(ins.getExtentAttr(BusinessTopoConstant.HWS_APP_SITE).getAttrValue()))
                .collect(Collectors.toList());
        }
        Set<Integer> moSubInstances = moTypeSubInstances.stream()
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toSet());
        List<Integer> podInstances = relationDao.queryInstanceByRelationType(new ArrayList<>(moSubInstances),
                BusinessTopoConstant.RELATION_TYPE_PARENT)
            .stream()
            .map(BusinessInstanceRelationModel::getInstanceId)
            .collect(Collectors.toList());
        Set<Integer> insListOfVm = new HashSet<>(
            relationDao.queryRelationByTypeBatchTimeLine(podInstances, BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT, 0L)
                .values());
        moSubInstances.addAll(podInstances);
        // 添加虚机
        moSubInstances.addAll(insListOfVm);
        Set<Integer> dockerInstances = relationDao.queryInstanceByRelationType(podInstances,
                BusinessTopoConstant.RELATION_TYPE_PARENT)
            .stream()
            .map(BusinessInstanceRelationModel::getInstanceId)
            .collect(Collectors.toSet());
        Set<Integer> insListOfContainer = new HashSet<>(
            relationDao.queryRelationByTypeBatchTimeLine(new ArrayList<>(dockerInstances),
                BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT, 0L).values());
        moSubInstances.addAll(dockerInstances);
        // 添加容器
        moSubInstances.addAll(insListOfContainer);
        return moSubInstances;
    }

    private List<Integer> getVMAndContainerIdListByUp(BusinessInstanceModelDB instance, Integer relationType) {
        // 查询Pod层
        List<Integer> insListOfPodLevel = modelDao.queryInsByRelationByLevel(instance.getInstanceId(),
                relationType, BusinessTopoConstant.POD_TYPE_ID)
            .stream()
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toList());
        // 查询Docker层
        Set<Integer> insListOfVm = new HashSet<>(
            relationDao.queryRelationByTypeBatchTimeLine(insListOfPodLevel, BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT, 0L)
                .values());
        // 查询Container层
        List<Integer> insListOfDockerLevel = modelDao.queryInsByRelationByLevel(instance.getInstanceId(),
                relationType, BusinessTopoConstant.DOCKER_TYPE_ID)
            .stream()
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toList());
        Set<Integer> insListOfContainer = new HashSet<>(
            relationDao.queryRelationByTypeBatchTimeLine(insListOfDockerLevel, BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT,
                0L).values());

        return Stream.concat(insListOfVm.stream(), insListOfContainer.stream())
            .collect(Collectors.toList());
    }

    @Override
    public AlarmCountResult queryAlarmCountStatistic(AlarmStatisticParam param) {

        int instanceId = param.getInstanceId() == null
            ? param.getStripeId() == null ? -1 : param.getStripeId()
            : param.getInstanceId();
        long currentTime = param.getTimestamp() == null ? 0 : param.getTimestamp();
        BusinessInstanceModelDB instanceModel = modelDao.queryInstanceByInstanceIdAndTimeLine(instanceId, currentTime);
        AlarmCountResult alarmCountResult = new AlarmCountResult();
        if (null == instanceModel || instanceModel.getInstanceId() == null) {
            LOGGER.debug("[queryBusinessAlarm] can not find the instanceId, id is {}, time is {}",
                instanceId, param.getTimestamp());
            return alarmCountResult;
        }
        Set<String> alarmCsnList = new HashSet<>();
        Set<String> eventCsnList = new HashSet<>();

        String stripeUnit = param.getStripeUnit();
        Integer stripeId = param.getStripeId();
        if (stripeUnit != null && Objects.equals(stripeId, instanceId)) {
            // 默认条带视图告警,直接查第六层
            setAlarmInfo(instanceId, alarmCsnList, eventCsnList, currentTime, stripeUnit);
        } else {
            List<BusinessInstanceModelDB> appList;
            if (param.getStripeId() != null && param.getDrGroupId() != null) {
                BusinessInstanceModelDB appDrIns = modelDao.queryInstanceByInstanceIdAndTimeLine(param.getDrGroupId(), currentTime);
                String stripeModelId = modelDao.queryInstanceByInstanceIdAndTimeLine(param.getInstanceId(), currentTime).getModelId();
                appList = getAppList(stripeId, currentTime, appDrIns, stripeModelId);
            } else {
                appList = Collections.singletonList(instanceModel);
            }
            // 查询下层级的第一个拥有dn成员
            traversesInstanceTree(appList, alarmCsnList, eventCsnList, currentTime, param);
        }
        if (CollectionUtil.isEmpty(alarmCsnList) && CollectionUtil.isEmpty(eventCsnList)) {
            LOGGER.debug("[queryBusinessAlarm] alarmCsnList and eventCsnList are empty");
        }
        loadAlarmCount(alarmCsnList, eventCsnList, alarmCountResult, currentTime);
        return alarmCountResult;
    }

    private List<BusinessInstanceModelDB> getAppList(Integer stripeId, long currentTime,
        BusinessInstanceModelDB appDrIns, String stripeModelId) {
        List<BusinessInstanceModelDB> appList;
        // 从第三层site开始递归向下查到第五层实例，过滤出同名第四层和第五层，然后汇聚第六层同条带的告警
        appList = modelDao.queryNextLevelInstanceByTimeLineBatch(modelDao.queryNextLevelInstanceByTimeLineBatch(
                    modelDao.queryNextLevelInstanceByTimeLineBatch(Collections.singleton(stripeId), currentTime)
                        .stream()
                        .map(BusinessInstanceModelDB::getInstanceId)
                        .collect(Collectors.toList()), currentTime)
                .stream()
                .filter(a -> Objects.equals(a.getModelId(), appDrIns.getModelId()))
                .map(BusinessInstanceModelDB::getInstanceId)
                .collect(Collectors.toList()), currentTime)
            .stream()
            .filter(a -> Objects.equals(a.getModelId(), stripeModelId))
            .collect(Collectors.toList());
        return appList;
    }

    private void traversesInstanceTree(List<BusinessInstanceModelDB> instanceModelList, Set<String> alarmCsnList,
        Set<String> eventCsnList, Long currentTime, AlarmStatisticParam param) {
        currentTime = Objects.isNull(currentTime) ? Long.valueOf(0L) : currentTime;
        for (BusinessInstanceModelDB instanceModel : instanceModelList) {
            loadMySelfAlarm(instanceModel, alarmCsnList, eventCsnList);
            if (StringUtils.isEmpty(instanceModel.getDn())) {
                List<BusinessInstanceModelDB> subInsList;
                subInsList = modelDao.queryNextLevelInstanceByTimeLine(instanceModel.getInstanceId(), currentTime);
                traversesInstanceTree(subInsList, alarmCsnList, eventCsnList, currentTime, param);
            }
            // 过滤条带单元
            String stripeUnit = param.getStripeUnit();
            if (stripeUnit != null && !stripeUnit.equals(
                instanceModel.getExtentAttr(BusinessTopoConstant.HWS_APP_SITE).getAttrValue())) {
                continue;
            }
            addCsnInfo(alarmCsnList, eventCsnList, instanceModel);
        }
    }

    /**
     * 添加自身告警，不区分是否有网元
     *
     * @param instanceModel 非网元实例元素
     * @param alarmCsnList 告警列表
     * @param eventCsnList 事件列表
     */
    private void loadMySelfAlarm(BusinessInstanceModelDB instanceModel, Set<String> alarmCsnList,
        Set<String> eventCsnList) {
        // 只加载南北向/接入渠道自身
        if (instanceModel.getModelType() == BusinessTopoConstant.BUSINESS_TYPE_ID
            || instanceModel.getModelType() == BusinessTopoConstant.CHANNEL_TYPE_ID) {
            BusinessInsExtentAttrDB alarmCsn = instanceModel.getExtentAttr(BusinessTopoConstant.ATTR_KEY_ALARM_CSN);
            BusinessInsExtentAttrDB eventCsn = instanceModel.getExtentAttr(BusinessTopoConstant.ATTR_KEY_EVENT_CSN);
            alarmCsnList.addAll(getAlarmSplitList(alarmCsn));
            eventCsnList.addAll(getAlarmSplitList(eventCsn));
        }
    }

    private void setAlarmInfo(Integer insId, Set<String> alarmCsnList, Set<String> eventCsnList, Long currentTime, String stripeUnit) {
        List<BusinessInstanceModelDB> stripeSiteIns;
        if (currentTime == null) {
            stripeSiteIns = modelDao.queryNextLevelInstance(insId);
        } else {
            stripeSiteIns = modelDao.queryNextLevelInstanceByTimeLine(insId, currentTime);
        }
        for (BusinessInstanceModelDB instanceModel : stripeSiteIns) {
            List<BusinessInstanceModelDB> clusterInsList;
            if (currentTime == null) {
                clusterInsList = modelDao.queryNextLevelInstanceBySelectTypeAndModelType(
                        Collections.singleton(instanceModel.getInstanceId()), BusinessTopoConstant.RELATION_TYPE_SITE,
                        BusinessTopoConstant.CLUSTER_INSTANCE_ID)
                    .stream()
                    .filter(a -> Objects.equals(stripeUnit,
                        a.getExtentAttr(BusinessTopoConstant.HWS_APP_SITE).getAttrValue()))
                    .collect(Collectors.toList());
            } else {
                clusterInsList = modelDao.queryNextLevelInstanceBySelectTypeAndModelTypeByTimeLine(
                        Collections.singleton(instanceModel.getInstanceId()), BusinessTopoConstant.RELATION_TYPE_SITE,
                        currentTime, BusinessTopoConstant.CLUSTER_INSTANCE_ID)
                    .stream()
                    .filter(a -> Objects.equals(stripeUnit,
                        a.getExtentAttr(BusinessTopoConstant.HWS_APP_SITE).getAttrValue()))
                    .collect(Collectors.toList());
            }
            for (BusinessInstanceModelDB cluster : clusterInsList) {
                if (!addCsnInfo(alarmCsnList, eventCsnList, cluster)) {
                    return;
                }
            }
        }
    }

    private boolean addCsnInfo(Set<String> alarmCsnList, Set<String> eventCsnList, BusinessInstanceModelDB cluster) {
        if (!ContextUtils.getContext().getAdmin() && !AuthUtils.getAuthDns().contains(cluster.getDn())) {
            LOGGER.error("don't have permission of this instance, id = {}", cluster.getInstanceId());
            return false;
        }
        BusinessInsExtentAttrDB alarmCsn = cluster.getExtentAttr("AlarmCsn");
        BusinessInsExtentAttrDB eventCsn = cluster.getExtentAttr("EventCsn");
        alarmCsnList.addAll(getAlarmSplitList(alarmCsn));
        eventCsnList.addAll(getAlarmSplitList(eventCsn));
        return true;
    }

    private void loadAlarmCount(Set<String> alarmCsnList, Set<String> eventCsnList, AlarmCountResult alarmCountResult,
        Long currentTime) {
        AlarmConvert alarmConvert = new AlarmConvert();
        List<AlarmQueryData> alarmQueryData = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(alarmCsnList)) {
            List<AlarmDetail> alarmDetailEntities = alarmRequestService.scrollQueryAlarmCsnResultByTime(
                new ArrayList<>(alarmCsnList), currentTime);
            Pair<List<AlarmDetail>, List<AlarmDetail>> alarmGroup = AlarmHandleUtil.getAnalysisAlarmGroup(
                alarmDetailEntities);
            List<AlarmDetail> analysisAlarmList = alarmGroup.first();
            analysisAlarmList.addAll(alarmGroup.second());
            List<AlarmQueryRecord> alarmList = new ArrayList<>(alarmConvert.convertAlarmStruct(analysisAlarmList));
            if (!ContextUtils.getContext().getAdmin()) {
                alarmList = alarmList.stream().filter(alarm -> AuthUtils.getAuthDns().contains(alarm.getNativeMeDn())).collect(Collectors.toList());
            }
            alarmQueryData.add(alarmConvert.convertToAlarmQueryData(alarmList));
        } else {
            alarmQueryData.add(alarmConvert.convertToAlarmQueryData(new ArrayList<>()));
        }

        if (CollectionUtils.isNotEmpty(eventCsnList)) {
            List<AlarmDetail> eventDetailEntities = alarmRequestService.getEventListByCsn(
                new ArrayList<>(eventCsnList));
            List<AlarmQueryRecord> eventList = alarmConvert.convertAlarmStruct(eventDetailEntities);
            if (!ContextUtils.getContext().getAdmin()) {
                eventList = eventList.stream().filter(event -> AuthUtils.getAuthDns().contains(event.getNativeMeDn())).collect(Collectors.toList());
            }
            alarmQueryData.add(alarmConvert.convertToEventQueryData((eventList)));
        } else {
            alarmQueryData.add(alarmConvert.convertToEventQueryData(new ArrayList<>()));
        }
        alarmCountResult.setAlarmDataList(alarmQueryData);
    }

    private List<String> getAlarmSplitList(BusinessInsExtentAttrDB attr) {
        String attrValue = attr.getAttrValue();
        if (StringUtils.isEmpty(attrValue)) {
            return new ArrayList<>();
        } else {
            return Arrays.asList(attr.getAttrValue().split(","));
        }
    }

    @Override
    public AlarmCountResult queryAlarmCountByLevel(AlarmStatisticParam param) {
        // 查询下层级的第一个拥有dn成员
        long currentTime = param.getTimestamp() == null ? 0 : param.getTimestamp();
        AlarmCountResult alarmCountResult = new AlarmCountResult();
        Integer insLevel = param.getInsLevel();
        Integer solutionId = param.getInstanceId();

        Set<String> alarmCsnList = new HashSet<>();
        Set<String> eventCsnList = new HashSet<>();
        Set<Integer> insListOfOneLevel = modelDao.queryAllInstanceOfTypeByTimeLine(insLevel, currentTime)
            .stream()
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toSet());

        if (Objects.nonNull(solutionId)) {
            // 过滤该解决方案下的所有问题
            Set<Integer> subTopInstances = modelDao.queryNextLevelInstances(Collections.singletonList(solutionId),
                currentTime).stream().filter(subIns -> {
                // 过滤在页面上需要展示的元素
                String isDisplayValue = subIns.getExtentAttr(BusinessTopoConstant.BUSINESS_IS_DISPLAY).getAttrValue();
                if (StringUtils.isNotEmpty(isDisplayValue)) {
                    return Boolean.parseBoolean(
                        subIns.getExtentAttr(BusinessTopoConstant.BUSINESS_IS_DISPLAY).getAttrValue());
                } else {
                    return true;
                }
            }).map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toSet());
            insListOfOneLevel = insListOfOneLevel.stream().filter(subTopInstances::contains).collect(Collectors.toSet());
        }

        List<BusinessInstanceModelDB> insList = modelDao.queryInstanceListByIdList(new ArrayList<>(insListOfOneLevel), currentTime);
        for (BusinessInstanceModelDB ins : insList) {
            BusinessInsExtentAttrDB alarmData = ins.getExtentAttr(BusinessTopoConstant.ATTR_KEY_ALARM_CSN);
            BusinessInsExtentAttrDB eventData = ins.getExtentAttr(BusinessTopoConstant.ATTR_KEY_EVENT_CSN);
            alarmCsnList.addAll(getAlarmSplitList(alarmData));
            eventCsnList.addAll(getAlarmSplitList(eventData));
        }

        if (CollectionUtil.isEmpty(alarmCsnList) && CollectionUtil.isEmpty(eventCsnList)) {
            LOGGER.debug("[queryBusinessAlarm] alarmCsnList and eventCsnList are empty");
        }
        loadAlarmCount(alarmCsnList, eventCsnList, alarmCountResult, currentTime);
        return alarmCountResult;
    }

    /**
     * 备份时间轴数据Cron回调接口
     */
    @Override
    public void backUpTimeLine() {
        // 定时任务触发之后不一定是十分整点，需要修正时间戳
        LOGGER.info("handle back up time line start");
        LocalDateTime localDateTime = LocalDateTime.now();
        int minute = localDateTime.getMinute();
        int nearestMinute = (minute / TEN_MINUTES) * TEN_MINUTES;
        LocalDateTime nearestDateTime = localDateTime.withMinute(nearestMinute).withSecond(0).withNano(0);
        Long timestamp = nearestDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        // 清理已经不存在的指标
        businessIndicatorDao.deleteDeadIndicators();
        modelDao.creatBackUpTimeLineTables(String.valueOf(timestamp));

        // 删除24小时以前的所有时间轴实例表
        List<String> tableNames = modelDao.queryBackUpTimeLineTables();
        List<String> deleteTableNames = tableNames.stream().filter(tableName -> {
            try {
                long thisStamp = Long.parseLong(tableName.substring(tableName.lastIndexOf(UNDER_LINE) + 1));
                return thisStamp < timestamp - ONE_DAY;
            } catch (NumberFormatException e) {
                LOGGER.error("parse timestamp from table name error.", e);
                return false;
            }
        }).collect(Collectors.toList());

        modelDao.deleteBackUpTimeLineTables(deleteTableNames);
        modelDao.deleteOverTimeTimeLineAlarm(timestamp - ONE_DAY);
        // 如果时间轴告警数量超多上限，清理多余时间轴告警
        Integer timelineAlarmCount = alarmDao.queryTimelineAlarmCount();
        if (timelineAlarmCount > MAX_TIME_LINE_COUNT) {
            Integer toClearCount = timelineAlarmCount - MAX_TIME_LINE_COUNT;
            Set<Integer> overLimitAlarmCsnList = alarmDao.queryOverLimitTimelineAlarmCsn(toClearCount);
            alarmDao.clearOverCountAlarm(new ArrayList<>(overLimitAlarmCsnList));
        }

        LOGGER.info("handle back up time line end");
    }

    @Override
    public List<AlarmDetail> queryTimeLineAlarmDetail(TimelineAlarmQueryParam timelineAlarmQueryParam) {

        Integer instanceId = timelineAlarmQueryParam.getInstanceId();
        Long startTime = timelineAlarmQueryParam.getTimestamp();
        String stripeUnit = timelineAlarmQueryParam.getStripeUnit();
        // 获取实例
        BusinessInstanceModelDB instance = modelDao.queryInstanceByInstanceId(instanceId);
        if (Objects.isNull(instance)) {
            LOGGER.warn("[queryTimeLineAlarmDetail] could not find the ins, ins id is {}", instanceId);
            return Collections.emptyList();
        }
        // 获取20min区间
        Pair<Long, Long> timeArea = new Pair<>(startTime, startTime + 20 * 60 * 1000L);
        List<Integer> viewInstanceIds = new ArrayList<>(getViewInstanceIdList(instance, stripeUnit));
        AtomicReference<Integer> areaTotalCount = new AtomicReference<>(0);

        List<List<Integer>> splitInsList = ListUtils.partition(viewInstanceIds, ONE_QUERY_TIME_INS_NUMBER);
        Set<AlarmDetail> priorityAlarmList = alarmDao.queryTimelinePriorityAlarm(viewInstanceIds,
            timeArea.first(), timeArea.second());
        Set<AlarmDetail> clearAlarmList = new HashSet<>();
        areaTotalCount.updateAndGet(v -> v + priorityAlarmList.size());
        splitInsList.forEach(splitList -> {
            if (areaTotalCount.get() >= MAX_ONCE_QUERY_NUMBER) {
                return;
            }
            Set<AlarmDetail> clearedAlarm = alarmDao.queryTimelineClearedAlarm(splitList,
                timeArea.first(), timeArea.second());
            clearAlarmList.addAll(clearedAlarm);
            areaTotalCount.updateAndGet(v -> v + clearedAlarm.size());
        });
        List<AlarmDetail> alarmDetails = Stream.concat(priorityAlarmList.stream(), clearAlarmList.stream())
            .collect(Collectors.toList());
        // 过滤网元权限
        if (!ContextUtils.getContext().getAdmin()) {
            alarmDetails = alarmDetails.stream()
                .filter(alarm -> AuthUtils.getAuthDns().contains(alarm.getNativeMeDn()))
                .collect(Collectors.toList());
        }
        // 针对AlarmCsn 去重
        List<AlarmDetail> distinctList = alarmDetails.stream()
            .collect(Collectors.collectingAndThen(
                toCollection(() -> new TreeSet<>(Comparator.comparing(AlarmDetail::getCsn))), ArrayList::new));
        // 针对智能运维告警和非智能运维告警分别处理
        Pair<List<AlarmDetail>, List<AlarmDetail>> alarmGroup = AlarmHandleUtil.getAnalysisAlarmGroup(distinctList);
        List<AlarmDetail> displayAlarmDetails = alarmGroup.first();
        displayAlarmDetails.addAll(alarmGroup.second());
        // 按照智能运维、时间、已清理告警返回
        AlarmHandleUtil.sortedTimeLineAlarmByRule(displayAlarmDetails);
        return displayAlarmDetails;
    }

    public void updateAlarmStatusTimed() {
        LOGGER.info("update alarm status start");
        List<AlarmDetail> alarms = alarmDao.queryNewAlarmCsn();
        if (CollectionUtils.isEmpty(alarms)) {
            LOGGER.warn("[updateAlarmStatusTimed] there is no occur alarms.");
            return;
        }
        List<String> alarmCsnList = alarms.stream().map(alarm -> String.valueOf(alarm.getCsn())).collect(Collectors.toList());
        List<AlarmDetail> historyAlarm = alarmRequestService.scrollQueryHistoryAlarmCsnResult(alarmCsnList);
        List<String> alarmCsnWithoutHistory;
        if (CollectionUtils.isEmpty(historyAlarm)) {
            alarmCsnWithoutHistory = new ArrayList<>(alarmCsnList);
        } else {
            List<String> historyCsnList = historyAlarm.stream()
                .map(alarm -> String.valueOf(alarm.getCsn()))
                .collect(Collectors.toList());
            alarmCsnWithoutHistory = alarmCsnList.stream().filter(csn -> !historyCsnList.contains(csn)).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(alarmCsnWithoutHistory)) {
            List<AlarmDetail> currentAlarm = alarmRequestService.scrollQueryAlarmCsnResult(alarmCsnWithoutHistory);
            // historyAlarm里面现在存的是所有的告警信息
            historyAlarm.addAll(currentAlarm);
        }
        List<String> clearedCsn = historyAlarm.stream()
            .filter(alarm -> alarm.getCleared() == 1)
            .map(alarm -> String.valueOf(alarm.getCsn()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(clearedCsn)) {
            LOGGER.error("[updateAlarmStatusTimed] cleared csn list is empty.");
            return;
        }
        // 获得已清理告警信息
        List<AlarmDetail> alarmDetailList = clearedCsn.stream().map(csn -> {
            AlarmDetail alarm = new AlarmDetail();
            alarm.setCsn(Integer.parseInt(csn));
            alarm.setCategory(AlarmConstant.ALARM_CLEAR);
            return alarm;
        }).collect(Collectors.toList());
        List<AlarmDetail> toClearAlarmList = historyAlarm.stream()
            .filter(alarm -> clearedCsn.contains(String.valueOf(alarm.getCsn())))
            .collect(Collectors.toList());
        // 成员告警同步接收
        businessTopoAlarmStorageService.receiveAlarmList(toClearAlarmList);
        List<List<AlarmDetail>> alarmDetails = ListUtils.partition(alarmDetailList, ONE_BATCH_UPDATE);
        for (List<AlarmDetail> alarmDetail : alarmDetails) {
            alarmDao.updateTimeLineAlarm(alarmDetail);
        }
        LOGGER.info("update alarm status end");
    }

    /**
     * 查询某个实例，在某个时刻下对应的实例信息
     *
     * @param queryInsOfOneMoment 查询实例Id, 传入的时刻，目标跳转时刻
     * @return Map<Integer, Integer> 传入时刻与目标跳转时刻映射
     * @throws ServiceException ex
     */
    @Override
    public Map<Integer, Integer> queryInsOfOneMoment(InsOfOneMomentParam queryInsOfOneMoment) throws ServiceException {
        List<Integer> instacneIdList = queryInsOfOneMoment.getInstanceIdList();
        long timestamp = Objects.isNull(queryInsOfOneMoment.getTimestamp()) ? 0L : queryInsOfOneMoment.getTimestamp();
        long targetTimestamp = Objects.isNull(queryInsOfOneMoment.getTargetTimestamp())
            ? 0L
            : queryInsOfOneMoment.getTargetTimestamp();
        Map<Integer, Integer> insOfMomentMap = new HashMap<>();

        // 查询当前的
        List<BusinessInstanceModelDB> currentInstanceList = modelDao.queryInstanceListByIdList(instacneIdList,
            timestamp);
        long solutionType = Objects.isNull(queryInsOfOneMoment.getSolutionType())
            ? 0L
            : queryInsOfOneMoment.getSolutionType();
        if (solutionType == 3) {
            return insOfMomentMapForMM(currentInstanceList, targetTimestamp, timestamp);
        }
        for (BusinessInstanceModelDB ins : currentInstanceList) {
            if (Objects.isNull(ins.getDn())) {
                // 无Dn需要查询关联的站点信息,如果为解决方案，根据顶层直接查询
                if (ins.getModelType() == BusinessTopoConstant.SOLUTION_TYPE_ID) {
                    Optional<BusinessInstanceModelDB> topIns = modelDao.queryHistoryInstanceByModelId(ins.getModelId(),
                        targetTimestamp).stream().findFirst();
                    topIns.ifPresent(instance -> insOfMomentMap.put(ins.getInstanceId(), instance.getInstanceId()));
                } else {
                    Integer siteInsIdOfCur = relationDao.queryAntiRelationByTypeAndTimeLine(ins.getInstanceId(), 3,
                        timestamp);
                    if (Objects.isNull(siteInsIdOfCur)) {
                        continue;
                    }
                    BusinessInstanceModelDB siteInsOfCur = modelDao.queryInstanceByInstanceIdAndTimeLine(siteInsIdOfCur,
                        timestamp);
                    BusinessInstanceModelDB siteInsOneMoment = modelDao.queryInstanceByDnAndTimeline(
                        siteInsOfCur.getDn(), targetTimestamp);
                    if (Objects.isNull(siteInsOneMoment)) {
                        continue;
                    }
                    Integer targetInstanceId = relationDao.queryBelongToInsFilterByModelId(
                        siteInsOneMoment.getInstanceId(), ins.getModelId(), targetTimestamp);
                    insOfMomentMap.put(ins.getInstanceId(), targetInstanceId);
                }
            } else {
                BusinessInstanceModelDB insOfOneMoment = modelDao.queryInstanceByDnAndTimeline(ins.getDn(),
                    targetTimestamp);
                if (Objects.isNull(insOfOneMoment)) {
                    continue;
                }
                insOfMomentMap.put(ins.getInstanceId(), insOfOneMoment.getInstanceId());
            }
        }
        return insOfMomentMap;
    }

    private Map<Integer, Integer> insOfMomentMapForMM(List<BusinessInstanceModelDB> currentInstanceList,
        Long targetTimestamp, Long timestamp) throws ServiceException {
        Map<Integer, Integer> insOfMomentMap = new HashMap<>();
        for (BusinessInstanceModelDB ins : currentInstanceList) {
            // 解决方案层
            if (ins.getModelType() == BusinessTopoConstant.SOLUTION_TYPE_ID) {
                Optional<BusinessInstanceModelDB> topIns = modelDao.queryHistoryInstanceByModelId(ins.getModelId(),
                    targetTimestamp).stream().findFirst();
                topIns.ifPresent(instance -> insOfMomentMap.put(ins.getInstanceId(), instance.getInstanceId()));
                continue;
            }
            if (ins.getModelType() == BusinessTopoConstant.SITE_GROUP_TYPE_ID) {
                insofMonentForStrip(targetTimestamp, insOfMomentMap, ins);
                continue;
            }
            if (ins.getModelType() == BusinessTopoConstant.MOTYPE_CLUSTER_TYPE_ID) {
                List<BusinessInstanceModelDB> ins2 = modelDao.queryMMSiteInstanceByTime(ins.getInstanceId(), timestamp);
                if (ins2.size() == 0) {
                    LOGGER.error("Instance is null,id ={} ", ins.getInstanceId());
                    return insOfMomentMap;
                }
                String hwsStripe = ins2.get(0).getExtentAttr(MM_HWS_STRIPE).getAttrValue();
                List<BusinessInstanceModelDB> ins3Model = modelDao.queryHistoryInstanceByModelId(
                    "3_site_MM", targetTimestamp);
                List<BusinessInstanceModelDB> ins3TargetInstanceList = modelDao.queryInstanceListByIdList(
                    ins3Model.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList()),
                    targetTimestamp);
                BusinessInstanceModelDB ins3 = ins3TargetInstanceList.stream()
                    .filter(e -> e.getExtentAttr(MM_HWS_STRIPE).getAttrValue().equals(hwsStripe))
                    .findFirst()
                    .orElse(null);

                if (Objects.isNull(ins3)) {
                    LOGGER.error("now Instance is null,id ={} ", ins2.get(0).getInstanceId());
                    return insOfMomentMap;
                }
                List<BusinessInstanceModelDB> ins4 = modelDao.queryInstanceByRelationTypeRelationByTime(
                    ins3.getInstanceId(), 3, targetTimestamp);
                // 查询当前的
                List<BusinessInstanceModelDB> targetInstanceList = modelDao.queryInstanceListByIdList(
                    ins4.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList()), targetTimestamp);
                Optional<BusinessInstanceModelDB> topIns = targetInstanceList.stream()
                    .filter(e -> e.getModelId().equals(ins.getModelId()))
                    .findFirst();
                topIns.ifPresent(instance -> insOfMomentMap.put(ins.getInstanceId(), instance.getInstanceId()));
            }
        }
        return insOfMomentMap;
    }

    private void insofMonentForStrip(Long targetTimestamp, Map<Integer, Integer> insOfMomentMap, BusinessInstanceModelDB ins) {
        List<BusinessInstanceModelDB> topIns = modelDao.queryHistoryInstanceByModelId(ins.getModelId(), targetTimestamp);
        List<BusinessInstanceModelDB> targetInstanceList = modelDao.queryInstanceListByIdList(
            topIns.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList()), targetTimestamp);

        Optional<BusinessInstanceModelDB> topIns2 = targetInstanceList.stream()
            .filter(e -> e.getExtentAttr(MM_HWS_STRIPE_GROUP_NAME).getAttrValue()
                .equals(ins.getExtentAttr(MM_HWS_STRIPE_GROUP_NAME).getAttrValue()))
            .findFirst();
        topIns2.ifPresent(instance -> insOfMomentMap.put(ins.getInstanceId(), instance.getInstanceId()));
    }

    /**
     * 刷新在导入时缺少的displayValue
     *
     * @throws ServiceException ex
     */
    public void fillDisplayValue() throws ServiceException {
        List<BusinessIndicator> indicatorsWithoutDisplayValue = businessIndicatorDao.queryEmptyDisplayValueIndicators();
        Map<String, List<BusinessIndicator>> moTypeIndicatorsMap = indicatorsWithoutDisplayValue.stream()
            .filter(indicator -> StringUtils.isNotEmpty(indicator.getMoType()))
            .collect(Collectors.groupingBy(BusinessIndicator::getMoType));

        List<BusinessIndicator> filledDisplayValueIndicators = new ArrayList<>();
        for (Map.Entry<String, List<BusinessIndicator>> entry : moTypeIndicatorsMap.entrySet()) {
            String moType = entry.getKey();
            List<PollerModel> pmTask = pmRequestService.getPmTaskNew(moType);
            if (CollectionUtils.isEmpty(pmTask)) {
                LOGGER.error("fill displayValue error, pmTask is empty, moType = {}", moType);
                continue;
            }
            // 收集测量单元，查询测量对象
            Map<String, List<BusinessIndicator>> measUnitKeyIndicatorsMap = entry.getValue().stream()
                .filter(indicator -> StringUtils.isNotEmpty(indicator.getMeasUnitKey()))
                .collect(Collectors.groupingBy(BusinessIndicator::getMeasUnitKey));
            for (Map.Entry<String, List<BusinessIndicator>> measUnitEntry : measUnitKeyIndicatorsMap.entrySet()) {
                List<MeasObjectInfo> objectInfo = pmRequestService.getMeasObjectByMoType(moType, measUnitEntry.getKey());
                for (BusinessIndicator businessIndicator : measUnitEntry.getValue()) {
                    String displayValue = pmIndicatorInstanceService.getDisplayValue(objectInfo, businessIndicator.getOriginalValue(), true);
                    if (StringUtils.isNotEmpty(displayValue)) {
                        businessIndicator.setDisplayValue(displayValue);
                        filledDisplayValueIndicators.add(businessIndicator);
                    }
                }
            }
        }
        businessIndicatorDao.updateIndicatorDisplayValue(filledDisplayValueIndicators);
        if (CollectionUtils.isNotEmpty(filledDisplayValueIndicators)) {
            LOGGER.info("fill display value succeed, instanceIds = {}", filledDisplayValueIndicators.stream().map(
                BusinessIndicator::getInstanceId).collect(
                Collectors.toList()));
        }
    }

    /**
     * MM的第三方和接入渠道刷新在导入或编辑后，可能因为sftp渠道网元不存在而缺少dn，需要定时尝试补齐
     *
     * @throws ServiceException ex
     */
    public void fillEmptyDns() throws ServiceException {
        // 检查第三方，接入渠道，条带分组，条带指标的dn，有变化则刷新
        List<BusinessCommonModel> models = businessCommonModelDao.queryCommonModelByType(
            BusinessTopoConstant.SOLUTION_TYPE_ID);
        for (BusinessCommonModel topModel : models) {
            // CBS,BES解决方案不需要刷新
            if (!BusinessTopoConstant.MM_TYPE.equals(
                topModel.getStaticExtentAttr(BusinessTopoConstant.CLASS_TYPE_ATTR_KEY).getStaticAttrValue())) {
                continue;
            }
            // 旧指标只检查空dn，不检查dn是否与汇聚任务一致，兼容R25C00和以前版本
            handleOldIndicators();
            List<BusinessIndicator> indicators = getBusinessChannelSiteIndicators(topModel);
            Set<String> moTypes = indicators.stream().map(Indicator::getMoType).collect(Collectors.toSet());
            // 找出已经失效的dn
            Set<String> notExistDns = indicators.stream().map(BusinessIndicator::getDn).collect(Collectors.toSet());
            List<DN> dns = notExistDns.stream().map(DN::new).collect(Collectors.toList());
            List<ManagedObject> existMos = EamUtil.getMoByDns(dns);
            Set<String> realDns = existMos.stream().map(ManagedObject::getDN).map(DN::getValue).collect(Collectors.toSet());
            notExistDns.removeAll(realDns);
            notExistDns.remove(EMPTY);
            // 现在有效的dn
            Map<String, Map<String, String>> moTypeMeasUnitKeyMap = new HashMap<>();
            for (String moType : moTypes) {
                Map<String, String> measUnitDnMap = TopoImportSolutionAdapter.getMmMeasUnitDnMap(moType);
                moTypeMeasUnitKeyMap.put(moType, measUnitDnMap);
            }
            List<BusinessIndicator> filledDnIndicators = new ArrayList<>();
            for (BusinessIndicator indicator : indicators) {
                // sftp需要特殊处理
                if (BusinessTopoConstant.MM_CHANNEL_SFTP_MO_TYPE.equals(indicator.getMoType())) {
                    Set<String> mmDns = TopoImportSolutionAdapter.getMmSftpDns(indicator.getMoType());
                    // 如果没找到说明还是没接入
                    if (CollectionUtils.isEmpty(mmDns)) {
                        continue;
                    }
                    // 如果找到了，也只可能有一个
                    String mmDn = new ArrayList<>(mmDns).get(0);
                    if (!mmDn.equals(indicator.getDn())) {
                        indicator.setDn(mmDn);
                        filledDnIndicators.add(indicator);
                    }
                }
                // 如果dn已经失效，需要换成有效的
                if (notExistDns.contains(indicator.getDn())) {
                    String realDn = moTypeMeasUnitKeyMap.get(indicator.getMoType()).get(indicator.getMeasUnitKey());
                    indicator.setDn(realDn);
                    filledDnIndicators.add(indicator);
                }
            }
            businessIndicatorDao.updateIndicatorDn(filledDnIndicators);
            if (CollectionUtils.isNotEmpty(filledDnIndicators)) {
                LOGGER.info("fill dn succeed, dns = {}", filledDnIndicators.stream().map(
                    BusinessIndicator::getDn).collect(Collectors.toList()));
            }
        }
    }

    private List<BusinessIndicator> getBusinessChannelSiteIndicators(BusinessCommonModel topModel) {
        List<BusinessInstanceModelDB> thirdModels = modelDao.queryAllInstanceOfType(BusinessTopoConstant.BUSINESS_TYPE_ID, topModel.getSolutionName());
        List<Integer> instanceIds = thirdModels.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList());
        List<BusinessInstanceModelDB> channelModels = modelDao.queryAllInstanceOfType(BusinessTopoConstant.CHANNEL_TYPE_ID, topModel.getSolutionName());
        instanceIds.addAll(channelModels.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList()));
        List<BusinessInstanceModelDB> siteGroupModels = modelDao.queryAllInstanceOfType(BusinessTopoConstant.SITE_GROUP_TYPE_ID, topModel.getSolutionName());
        instanceIds.addAll(siteGroupModels.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList()));
        List<BusinessInstanceModelDB> siteModels = modelDao.queryAllInstanceOfType(BusinessTopoConstant.SITE_TYPE_ID, topModel.getSolutionName());
        instanceIds.addAll(siteModels.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList()));
        List<BusinessIndicator> indicators = businessIndicatorDao.queryIndicatorByInstanceList(instanceIds);
        indicators = indicators.stream().filter(indicator -> Boolean.TRUE.equals(indicator.getIsAggregateByAllSolution())).collect(Collectors.toList());
        return indicators;
    }

    private void handleOldIndicators() {
        List<BusinessIndicator> indicatorsWithoutDn = businessIndicatorDao.queryEmptyDnIndicators();
        indicatorsWithoutDn = indicatorsWithoutDn.stream().filter(indicatorInfo -> Objects.isNull(indicatorInfo.getIsAggregateByAllSolution())
            || Boolean.FALSE.equals(indicatorInfo.getIsAggregateByAllSolution())).collect(Collectors.toList());
        Map<String, List<BusinessIndicator>> moTypeIndicatorsMap = indicatorsWithoutDn.stream()
            .filter(indicator -> StringUtils.isNotEmpty(indicator.getMoType()))
            .collect(Collectors.groupingBy(BusinessIndicator::getMoType));

        List<BusinessIndicator> filledDnIndicators = new ArrayList<>();
        for (Map.Entry<String, List<BusinessIndicator>> entry : moTypeIndicatorsMap.entrySet()) {
            String moType = entry.getKey();
            Set<String> mmDns = TopoImportSolutionAdapter.getMmSiteDns(moType, true);

            // 如果没找到dn，可能是sftp渠道，走一下sftp定制逻辑
            if (CollectionUtils.isEmpty(mmDns)) {
                mmDns = TopoImportSolutionAdapter.getMmSftpDns(moType);
            }

            // 如果没找到说明还是没接入
            if (CollectionUtils.isEmpty(mmDns)) {
                continue;
            }

            // 如果找到了，也只可能有一个
            String mmDn = new ArrayList<>(mmDns).get(0);
            entry.getValue().forEach(indicator -> indicator.setDn(mmDn));
            filledDnIndicators.addAll(entry.getValue());
        }

        businessIndicatorDao.updateIndicatorDn(filledDnIndicators);
        if (CollectionUtils.isNotEmpty(filledDnIndicators)) {
            List<String> indicatorIds = filledDnIndicators.stream().map(BusinessIndicator::getIndicatorId).collect(Collectors.toList());
            List<String> dns = filledDnIndicators.stream().map(BusinessIndicator::getDn).collect(Collectors.toList());
            LOGGER.info("fill old dn succeed, indicatorIds = {}, dns = {}", indicatorIds, dns);
        }
    }

    @Override
    public void refreshCurrentAlarm() {
        LOGGER.info("[refreshCurrentAlarm] refreshCurrentAlarm start.");
        // 如果告警处理队列积压，先不处理
        if (businessTopoAlarmStorageService.getStorageAlarmCount() > MAX_DEAL_CURRENT_ALARM_COUNT) {
            LOGGER.warn("[refreshCurrentAlarm] The alarm deal list is full");
            return;
        }

        // 获取所有当前告警
        List<String> alarmCsnJoinList = modelDao.queryAllCsnByInstanceIds(new ArrayList<>());
        Set<String> alarmAllCsnList = new HashSet<>();
        for (String join : alarmCsnJoinList) {
            if (StringUtils.isNotEmpty(join)) {
                alarmAllCsnList.addAll(Arrays.asList(join.split(",")));
            }
        }
        // 最多处理5W条
        alarmAllCsnList = alarmAllCsnList.stream().limit(MAX_DEAL_CURRENT_ALARM_COUNT).collect(Collectors.toSet());
        List<List<String>> alarmCsnSplit = ListUtils.partition(new ArrayList<>(alarmAllCsnList), ONE_BATCH_UPDATE);
        for (List<String> alarmCsnList : alarmCsnSplit) {
            List<AlarmDetail> alarmDetailList = alarmRequestService.scrollQueryHistoryAlarmCsnResult(alarmCsnList);
            // 找到所有已清理的告警
            List<AlarmDetail> toClearAlarmList = alarmDetailList.stream()
                .filter(alarm -> AlarmConstant.ALARM_CLEAR == alarm.getCategory() || AlarmConstant.ALARM_CLEARED.equals(
                    alarm.getCleared()))
                .collect(Collectors.toList());
            // 对清理告警插入InsId
            Map<String, List<AlarmDetail>> alarmBatchGroup = toClearAlarmList.stream()
                .collect(Collectors.groupingBy(AlarmDetail::getNativeMeDn));
            Map<String, List<AlarmDetail>> alrOfEachSol = businessTopoAlarmStorageService.getCurrentDnOfSolMap(
                alarmBatchGroup);
            toClearAlarmList = alrOfEachSol.values().stream().flatMap(List::stream).collect(Collectors.toList());
            // 成员告警同步接收
            if (CollectionUtils.isNotEmpty(toClearAlarmList)) {
                toClearAlarmList.forEach(clr -> {
                    clr.setCategory(AlarmConstant.ALARM_CLEAR);
                    clr.setIsVanished(true);
                });
                businessTopoAlarmStorageService.receiveAlarmList(toClearAlarmList);
                businessThresholdAlarmStorageService.receiveAlarmList(toClearAlarmList);
            }
        }
        LOGGER.info("[refreshCurrentAlarm] refreshCurrentAlarm end.");
    }

    public List<MoInsPath> searchDnPath(SearchMoParam searchMoParam) {
        BusinessInstanceModelDB solutionIns = modelDao.queryInstanceByInstanceId(searchMoParam.getSolutionId());
        if (Objects.isNull(solutionIns)) {
            return Collections.emptyList();
        }
        List<MoInsPath> searchMoPathList = new ArrayList<>();
        PresetSearch presetSearch = getPresetSearch(searchMoParam.getSolutionId(), searchMoParam.getTimestamp());
        switch (searchMoParam.getSearchType()) {
            case BusinessTopoConstant.SOLUTION_TYPE_ID:
                searchMoPathList.addAll(getClusterMoInsPath(searchMoParam.getSearchKey(), presetSearch));
                searchMoPathList.addAll(getPodMoInsPath(searchMoParam.getSearchKey(), presetSearch));
                searchMoPathList.addAll(getVmMoInsPath(searchMoParam.getSearchKey(), presetSearch));
                break;
            case BusinessTopoConstant.CLUSTER_INSTANCE_ID:
                searchMoPathList.addAll(getClusterMoInsPath(searchMoParam.getSearchKey(), presetSearch));
                break;
            case BusinessTopoConstant.POD_TYPE_ID:
                searchMoPathList.addAll(getPodMoInsPath(searchMoParam.getSearchKey(), presetSearch));
                break;
            case BusinessTopoConstant.VM_TYPE_ID:
                searchMoPathList.addAll(getVmMoInsPath(searchMoParam.getSearchKey(), presetSearch));
                break;
            default:
                LOGGER.error("[searchDnPath] not support type");
        }
        if (!ContextUtils.getContext().getAdmin()) {
            Set<String> authDnList = AuthUtils.getAuthDns();
            return searchMoPathList.stream()
                .filter(path -> authDnList.contains(path.getDn()))
                .collect(Collectors.toList());
        }
        return searchMoPathList;
    }

    public PresetSearch getPresetSearch(Integer solutionId, Long timeStamp) {
        Long timePassStamp = Objects.isNull(timeStamp) ? Long.valueOf(0L) : timeStamp;
        List<BusinessInstanceModelDB> allInstanceList = modelDao.queryAllInstanceByTopId(solutionId);
        // 查询所有涉及实例
        List<BusinessInstanceModelDB> subInsList = modelDao.queryInstanceListByIdList(allInstanceList.stream()
            .filter(ins -> REAL_DN_LIST_LEVEL.contains(ins.getModelType()))
            .map(BusinessInstanceModelDB::getInstanceId)
            .collect(Collectors.toList()), timePassStamp);

        // 获取所有site实例
        List<BusinessInstanceModelDB> siteInsList = subInsList.stream()
            .filter(ins -> ins.getModelType() == BusinessTopoConstant.SITE_TYPE_ID)
            .collect(Collectors.toList());

        List<BusinessInstanceModelDB> allClusterInsList = subInsList.stream()
            .filter(ins -> BusinessTopoConstant.CLUSTER_INSTANCE_ID == ins.getModelType())
            .collect(Collectors.toList());

        List<BusinessInstanceModelDB> allPodInsList = subInsList.stream()
            .filter(ins -> BusinessTopoConstant.POD_TYPE_ID == ins.getModelType())
            .collect(Collectors.toList());
        // 查询cluster、pod的站点信息
        List<BusinessInstanceRelationModel> subInsOfSiteRelationList
            = relationDao.getInsRelByInsIdAndRelationTypeForTimeLine(
            subInsList.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList()), timePassStamp,
            BusinessTopoConstant.RELATION_TYPE_SITE, BusinessTopoConstant.COLUMN_INSTANCE_ID);
        // 查询pod的应用信息
        List<BusinessInstanceRelationModel> podInsOfAppRelationList
            = relationDao.getInsRelByInsIdAndRelationTypeForTimeLine(
            subInsList.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList()), timePassStamp,
            BusinessTopoConstant.RELATION_TYPE_PARENT, BusinessTopoConstant.COLUMN_INSTANCE_ID);

        // 从pod查询所有虚机
        List<BusinessInstanceRelationModel> podAndVmRelationList
            = relationDao.getInsRelByInsIdAndRelationTypeForTimeLine(
            allPodInsList.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList()), timePassStamp,
            BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT, BusinessTopoConstant.COLUMN_INSTANCE_ID);

        List<BusinessInstanceModelDB> allVmInsList = modelDao.queryInstanceListByIdList(podAndVmRelationList.stream()
            .map(BusinessInstanceRelationModel::getTargetInstanceId)
            .collect(Collectors.toList()), timePassStamp);

        return new PresetSearch(subInsList, siteInsList, allClusterInsList, allPodInsList, allVmInsList, subInsOfSiteRelationList,
            podInsOfAppRelationList, podAndVmRelationList);
    }

    private List<MoInsPath> getClusterMoInsPath(String searchKey, PresetSearch presetSearch) {
        List<BusinessInstanceModelDB> allClusterInsList = presetSearch.getAllClusterInsList();
        List<BusinessInstanceModelDB> allSiteInsList = presetSearch.getAllSiteInsList();
        List<BusinessInstanceRelationModel> subInsOfSiteRelationList = presetSearch.getSubInsOfSiteRelationList();

        List<BusinessInstanceModelDB> clusterSearchInsList = allClusterInsList.stream()
            .filter(ins -> StringUtils.isNotEmpty(ins.getExtentAttr(BusinessTopoConstant.ATTR_KEY_APP_NAME).getAttrValue()))
            .filter(ins -> ins.getExtentAttr(BusinessTopoConstant.ATTR_KEY_APP_NAME).getAttrValue().contains(searchKey))
            .collect(Collectors.toList());

        // 获取站点的关联关系
        Map<Integer, Integer> clusterAndSiteMap = subInsOfSiteRelationList.stream()
            .collect(Collectors.toMap(BusinessInstanceRelationModel::getInstanceId,
                BusinessInstanceRelationModel::getTargetInstanceId, (existing, replacement) -> existing));

        List<MoInsPath> clusterMoPathList = new ArrayList<>();
        for (BusinessInstanceModelDB cluster : clusterSearchInsList) {
            Optional<BusinessInstanceModelDB> siteIns = allSiteInsList.stream().filter(site -> Objects.equals(
                site.getInstanceId(), clusterAndSiteMap.get(cluster.getInstanceId()))).findFirst();
            siteIns.ifPresent(instanceModelDB -> clusterMoPathList.add(new MoInsPath(cluster, cluster, instanceModelDB)));
        }
        return clusterMoPathList;
    }

    private List<MoInsPath> getPodMoInsPath(String searchKey, PresetSearch presetSearch) {
        List<BusinessInstanceModelDB> allPodInsList = presetSearch.getAllPodInsList();
        List<BusinessInstanceModelDB> allClusterInsList = presetSearch.getAllClusterInsList();
        List<BusinessInstanceModelDB> allSiteInsList = presetSearch.getAllSiteInsList();
        List<BusinessInstanceRelationModel> subInsOfSiteRelationList = presetSearch.getSubInsOfSiteRelationList();
        List<BusinessInstanceRelationModel> podInsOfAppRelationList = presetSearch.getPodInsOfAppRelationList();

        // 按照关键字过滤
        List<BusinessInstanceModelDB> podSearchInsList = allPodInsList.stream()
            .filter(
                ins -> StringUtils.isNotEmpty(ins.getExtentAttr(BusinessTopoConstant.ATTR_KEY_POD_NAME).getAttrValue()))
            .filter(ins -> ins.getExtentAttr(BusinessTopoConstant.ATTR_KEY_POD_NAME).getAttrValue().contains(searchKey))
            .collect(Collectors.toList());

        // 获取站点的关联关系
        Map<Integer, Integer> podAndSiteMap = subInsOfSiteRelationList.stream()
            .collect(Collectors.toMap(BusinessInstanceRelationModel::getInstanceId,
                BusinessInstanceRelationModel::getTargetInstanceId, (existing, replacement) -> existing));
        // 获取应用的关联关系
        Map<Integer, Integer> podAndClusterMap = podInsOfAppRelationList.stream()
            .collect(Collectors.toMap(BusinessInstanceRelationModel::getInstanceId,
                BusinessInstanceRelationModel::getTargetInstanceId, (existing, replacement) -> existing));
        List<MoInsPath> podMoPathList = new ArrayList<>();
        for (BusinessInstanceModelDB podIns : podSearchInsList) {
            Optional<BusinessInstanceModelDB> siteIns = allSiteInsList.stream().filter(site -> Objects.equals(
                site.getInstanceId(), podAndSiteMap.get(podIns.getInstanceId()))).findFirst();
            Optional<BusinessInstanceModelDB> clusterIns = allClusterInsList.stream().filter(cluster -> Objects.equals(
                cluster.getInstanceId(), podAndClusterMap.get(podIns.getInstanceId()))).findFirst();
            if (siteIns.isPresent() && clusterIns.isPresent()) {
                podMoPathList.add(new MoInsPath(podIns, clusterIns.get(), siteIns.get()));
            }
        }
        return podMoPathList;
    }

    private List<MoInsPath> getVmMoInsPath(String searchKey, PresetSearch presetSearch) {
        List<BusinessInstanceModelDB> allVmInsList = presetSearch.getAllVmInsList();
        List<BusinessInstanceModelDB> allClusterInsList = presetSearch.getAllClusterInsList();
        List<BusinessInstanceModelDB> allSiteInsList = presetSearch.getAllSiteInsList();
        List<BusinessInstanceRelationModel> subInsOfSiteRelationList = presetSearch.getSubInsOfSiteRelationList();
        List<BusinessInstanceRelationModel> podInsOfAppRelationList = presetSearch.getPodInsOfAppRelationList();
        List<BusinessInstanceRelationModel> podAndVmRelationList = presetSearch.getPodAndVmRelationList();

        // 按照关键字过滤
        List<BusinessInstanceModelDB> vmSearchInsList = allVmInsList.stream()
            .filter(ins -> StringUtils.isNotEmpty(ins.getExtentAttr(BusinessTopoConstant.ATTR_KEY_VM_NAME).getAttrValue()))
            .filter(ins -> ins.getExtentAttr(BusinessTopoConstant.ATTR_KEY_VM_NAME).getAttrValue().contains(searchKey))
            .collect(Collectors.toList());

        // 获取站点的关联关系
        Map<Integer, Integer> podAndSiteMap = subInsOfSiteRelationList.stream()
            .collect(Collectors.toMap(BusinessInstanceRelationModel::getInstanceId,
                BusinessInstanceRelationModel::getTargetInstanceId, (existing, replacement) -> existing));
        // 获取应用的关联关系
        Map<Integer, Integer> podAndClusterMap = podInsOfAppRelationList.stream()
            .collect(Collectors.toMap(BusinessInstanceRelationModel::getInstanceId,
                BusinessInstanceRelationModel::getTargetInstanceId, (existing, replacement) -> existing));
        // 获取pod和Vm的关联关系
        Map<Integer, List<Integer>> targetInstanceIdToInstanceIdsMap = podAndVmRelationList.stream()
            .collect(Collectors.groupingBy(
                BusinessInstanceRelationModel::getTargetInstanceId,
                Collectors.mapping(BusinessInstanceRelationModel::getInstanceId, Collectors.toList())
            ));
        List<MoInsPath> vmMoInsPath = new ArrayList<>();
        for (BusinessInstanceModelDB vmIns : vmSearchInsList) {
            List<Integer> podIdList = targetInstanceIdToInstanceIdsMap.get(vmIns.getInstanceId());
            for (Integer podId : podIdList) {
                Optional<BusinessInstanceModelDB> siteIns = allSiteInsList.stream()
                    .filter(cluster -> Objects.equals(cluster.getInstanceId(), podAndSiteMap.get(podId)))
                    .findFirst();
                Optional<BusinessInstanceModelDB> clusterIns = allClusterInsList.stream()
                    .filter(cluster -> Objects.equals(cluster.getInstanceId(), podAndClusterMap.get(podId)))
                    .findFirst();
                if (siteIns.isPresent() && clusterIns.isPresent()) {
                    vmMoInsPath.add(new MoInsPath(vmIns, clusterIns.get(), siteIns.get()));
                }
            }
        }
        return vmMoInsPath;
    }

    public JumpingPath queryJumpingPath(JumpingParam jumpingParam) {
        Long timeStamp = Objects.isNull(jumpingParam.getTimestamp()) ? Long.valueOf(0) : jumpingParam.getTimestamp();
        BusinessInstanceModelDB toJumpingIns = modelDao.queryInstanceByInstanceIdAndTimeLine(jumpingParam.getInstanceId(), timeStamp);
        BusinessInstanceModelDB clusterIns = modelDao.queryInstanceByInstanceIdAndTimeLine(jumpingParam.getClusterId(), timeStamp);
        if (Objects.isNull(toJumpingIns) || Objects.isNull(clusterIns)) {
            return null;
        }
        // 查询上层应用以及站点、解决方案
        List<BusinessInstanceRelationModel> clusterRelations = relationDao.queryRelationInsList(clusterIns.getInstanceId(), timeStamp);
        List<Integer> relationIdList = clusterRelations.stream()
            .map(BusinessInstanceRelationModel::getTargetInstanceId)
            .collect(Collectors.toList());
        List<BusinessInstanceModelDB> relationInsList = modelDao.queryInstanceListByIdList(relationIdList, timeStamp);

        return new JumpingPath(relationInsList, toJumpingIns, clusterIns);
    }
}
