/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.business.pm;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cloudsop.common.tokenhelper.util.json.JsonUtil;
import com.huawei.cloudsop.sys.util.i18n.MessageUtil;
import com.huawei.i2000.cbb.fm.constant.EventType;
import com.huawei.i2000.cbb.fm.model.ScrollQueryResult;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.model.AlarmQueryParam;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.BusinessTopoAlarmStorageService;
import com.huawei.i2000.dvtoposervice.constant.AlarmConstant;
import com.huawei.i2000.dvtoposervice.constant.DVTopoServiceConstant;
import com.huawei.i2000.dvtoposervice.constant.RestConstant;
import com.huawei.i2000.dvtoposervice.util.RestUtil;
import com.huawei.i2000.dvtopowebsite.model.AlarmDefinition;
import com.huawei.i2000.dvtopowebsite.model.AlarmStaticInfo;
import com.huawei.i2000.dvtopowebsite.model.DefinitionPageQueryResult;
import com.huawei.i2000.dvtopowebsite.model.DefinitionQueryParam;
import com.huawei.i2000.dvtopowebsite.model.ResponseEntity;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 告警接口调用类
 *
 * <AUTHOR>
 * @since 2024/3/20
 */
@Service
public class AlarmRequestService {
    private static final OssLog LOGGER = OssLogFactory.getLogger(AlarmRequestService.class);

    private static final String GET_ALARM_STATIC_INFO_URL = "/rest/fault/v1/alarm-definitions/list";

    private static final String CSNS = "csns";

    private static final String ALARM_ID = "alarmId";

    private static final String ALARM_NAME = "alarmName";

    private static final String SEVERITY = "severity";

    private static final String ALARM_SOURCE = "meName";

    private static final String LATEST_OCCUR_UTC = "latestOccurUtc";

    private static final String NATIVE_ME_DN = "nativeMeDn";

    private static final String CSN = "csn";

    private static final String OCCUR_UTC = "occurUtc";

    private static final String MOI = "moi";

    private static final String CATEGORY = "category";

    private static final String ADDITIONAL_INFORMATION = "additionalInformation";

    private static final String ALARM_GROUP_ID = "alarmGroupId";

    private static final String CLEARED = "cleared";

    private static final String CLEAR_CATEGORY = "clearCategory";

    private static final String EXT_PARAMS = "extParams";

    private static final String MODULE_NAME = "moduleName";

    private static final int MAX_ALARM_NUMBER = 20000;

    private static final int MAX_QUERY_NUMBER_ONE_TIME = 200;

    private static final long WAIT_TIME = 5000L;

    // 国际化前缀
    private static final String MESSAGE_PART = "com.huawei.i2000.alarmtype.";

    @Autowired
    private BusinessTopoAlarmStorageService businessTopoAlarmStorageService;

    /**
     * 迭代查询告警信息
     *
     * @param param 接口请求对象
     * @param alarmType 当前或历史告警
     * @return 告警信息
     */
    public ScrollQueryResult getAlarmInfos(AlarmQueryParam param, String alarmType) {
        LOGGER.debug("[AlarmRequestService] getAlarmInfos start, param={}, alarmType={}", param, alarmType);
        RestfulResponse response = null;
        RestfulParametes restfulParametes = new RestfulParametes();
        restfulParametes.setRawData(JSON.toJSONString(param));
        String url = "current".equals(alarmType)
            ? "/rest/fault/v1/alarm-logs/scroll"
            : "/rest/fault/v1/events/scroll";
        try {
            response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, url, restfulParametes, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("[AlarmRequestService] getAlarmInfos response error");
                return null;
            }

            ScrollQueryResult result = JsonUtil.getMapper()
                .readValue(response.getResponseContent(), new ResultTypeReference());
            if (result == null || result.getResCode() != 1) {
                LOGGER.error("[AlarmRequestService] getAlarmInfos response status error, result = {}", result);
                return null;
            }
            return result;
        } catch (ServiceException | JsonProcessingException e) {
            LOGGER.error("[AlarmRequestService] getAlarmInfos error, e = {}", e.getMessage());
            return null;
        }
    }

    static class ResultTypeReference extends TypeReference<ScrollQueryResult> {
        /**
         * 构造方法
         */
        public ResultTypeReference() {
        }
    }

    public List<AlarmDetail> getAlarmData(AlarmQueryParam param, String type) {
        List<AlarmDetail> result = new ArrayList<>();
        ScrollQueryResult queryResult = getAlarmInfos(param, type);
        if (queryResult == null) {
            LOGGER.info("getAlarmData queryResult is null");
            return result;
        }
        int currentAlarmNumber = 0;
        while (StringUtils.isNotEmpty(queryResult.getIterator()) || CollectionUtils.isNotEmpty(queryResult.getHits())) {
            List<AlarmDetail> alarms = convertAlarm(queryResult.getHits());
            if (alarms.size() + currentAlarmNumber > MAX_ALARM_NUMBER) {
                LOGGER.info("Exceeded the maximum number!");
                int remainQuantity = MAX_ALARM_NUMBER - currentAlarmNumber;
                List<AlarmDetail> remainList = alarms.subList(0, remainQuantity);
                result.addAll(remainList);
                break;
            } else {
                result.addAll(alarms);
                currentAlarmNumber += alarms.size();
            }
            param.setIterator(queryResult.getIterator());
            queryResult = getAlarmInfos(param, type);
            if (queryResult == null) {
                LOGGER.info("getAlarmData queryResult is null");
                break;
            }
        }
        return result;
    }

    public void receiveAlarmToStorage(AlarmQueryParam param, String type) {
        try {
            List<AlarmDetail> result = new ArrayList<>();
            ScrollQueryResult queryResult = getAlarmInfos(param, type);
            if (queryResult == null) {
                LOGGER.info("getAlarmData queryResult is null");
                return;
            }
            int currentAlarmNumber = 0;
            while (StringUtils.isNotEmpty(queryResult.getIterator()) || CollectionUtils.isNotEmpty(
                queryResult.getHits())) {
                List<AlarmDetail> alarms = convertAlarm(queryResult.getHits());
                alarms.forEach(toUpdate -> {
                    if (AlarmConstant.ALARM_EVENT != toUpdate.getCategory()) {
                        toUpdate.setCategory(AlarmConstant.ALARM_OCCUR);
                    }
                });
                businessTopoAlarmStorageService.receiveAlarmList(alarms);
                Thread.sleep(WAIT_TIME);

                if (alarms.size() + currentAlarmNumber > MAX_ALARM_NUMBER) {
                    LOGGER.info("Exceeded the maximum number!");
                    int remainQuantity = MAX_ALARM_NUMBER - currentAlarmNumber;
                    List<AlarmDetail> remainList = alarms.subList(0, remainQuantity);
                    result.addAll(remainList);
                    break;
                } else {
                    result.addAll(alarms);
                    currentAlarmNumber += alarms.size();
                }
                param.setIterator(queryResult.getIterator());
                queryResult = getAlarmInfos(param, type);
                if (queryResult == null) {
                    LOGGER.info("getAlarmData queryResult is null");
                    break;
                }
            }
        } catch (Throwable e) {
            LOGGER.error("[receiveAlarmToStorage] receive alarm error when init full alarm", e);
        }
    }

    private List<AlarmDetail> convertAlarm(List<Map<String, String>> alarmData) {
        List<AlarmDetail> alarmTimeInfos = new ArrayList<>();
        alarmData.forEach(result -> {
            AlarmDetail alarmDetail = new AlarmDetail();
            alarmDetail.setAlarmId(Integer.parseInt(result.get(ALARM_ID)));
            alarmDetail.setSeverity(Integer.parseInt(result.get(SEVERITY)));
            alarmDetail.setAlarmName(result.get(ALARM_NAME));
            alarmDetail.setCategory(Integer.parseInt(result.get(CATEGORY)));
            alarmDetail.setMeName(result.get(ALARM_SOURCE));
            alarmDetail.setLatestOccurUtc(result.get(LATEST_OCCUR_UTC));
            alarmDetail.setNativeMeDn(result.get(NATIVE_ME_DN));
            alarmDetail.setCsn(Integer.parseInt(result.get(CSN)));
            alarmDetail.setOccurUtc(Long.parseLong(result.get(OCCUR_UTC)));
            alarmDetail.setAdditionalInformation(result.get(ADDITIONAL_INFORMATION));
            alarmDetail.setMoi(result.get(MOI));
            alarmDetail.setAlarmGroupId(result.get(ALARM_GROUP_ID));
            alarmDetail.setCleared(Integer.parseInt(result.get(CLEARED)));
            alarmDetail.setClearCategory(Integer.parseInt(result.get(CLEAR_CATEGORY)));
            // 处理扩展参数
            Map<Object, String> extParams = new HashMap<>();
            extParams.put(MODULE_NAME, result.get(MODULE_NAME));
            alarmDetail.setExtParams(extParams);
            alarmTimeInfos.add(alarmDetail);
        });
        return alarmTimeInfos;
    }

    public List<AlarmDetail> scrollQueryAlarmCsnResultByTime(List<String> csnList, Long timeStamp) {
        if (Objects.isNull(timeStamp) || timeStamp == 0) {
            // 不带时间戳，查询当前告警
            return scrollQueryAlarmCsnResult(csnList);
        } else {
            // 带时间戳，查询历史告警
            return scrollQueryHistoryAlarmCsnResult(csnList);
        }
    }

    /**
     * 迭代根据流水号查询告警信息
     *
     * @param csnList 流水号列表
     * @return 告警信息
     */
    public List<AlarmDetail> scrollQueryHistoryAlarmCsnResult(List<String> csnList) {
        int startIndex = 0;
        List<AlarmDetail> queryAlarmResult = new ArrayList<>();
        while (startIndex < csnList.size()) {
            List<AlarmDetail> queryAlarmResultOnce = getHistoryAlarmListByCsn(
                csnList.subList(startIndex, Math.min((startIndex + MAX_QUERY_NUMBER_ONE_TIME), csnList.size())));
            queryAlarmResult.addAll(queryAlarmResultOnce);
            startIndex += MAX_QUERY_NUMBER_ONE_TIME;
        }
        return queryAlarmResult;
    }

    /**
     * 根据流水号查询历史告警信息 流水号数量建议不超过200个
     *
     * @param csnList 流水号列表
     * @return 告警信息列表
     */
    public List<AlarmDetail> getHistoryAlarmListByCsn(List<String> csnList) {
        List<AlarmDetail> result = new ArrayList<>();
        RestfulResponse response;
        try {
            String csnUrl = AlarmConstant.GET_HISTORY_ALARMS_BY_CSN;
            for (String csn : csnList) {
                csnUrl = RestUtil.addUrlParam(csnUrl, CSNS, csn);
            }
            response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_GET, csnUrl, null, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("[AlarmRequestService] getHistoryAlarmDetail response error,response = {}", response);
                return result;
            } else {
                List<AlarmDetail> alarmDetailResults = JSONArray.parseArray(response.getResponseContent(),
                    AlarmDetail.class);
                result.addAll(alarmDetailResults);
            }
            if (CollectionUtils.isEmpty(result)) {
                LOGGER.info("alarmDetailResults is empty");
                return result;
            }
        } catch (ServiceException e) {
            LOGGER.error("[AlarmRequestService] getHistoryAlarmDetail error, e={}", e);
        }
        return result;
    }

    /**
     * 迭代根据流水号查询告警信息
     *
     * @param csnList 流水号列表
     * @return 告警信息
     */
    public List<AlarmDetail> scrollQueryAlarmCsnResult(List<String> csnList) {
        int startIndex = 0;
        List<AlarmDetail> queryAlarmResult = new ArrayList<>();
        while (startIndex < csnList.size()) {
            List<AlarmDetail> queryAlarmResultOnce = getAlarmListByCsn(
                csnList.subList(startIndex, Math.min((startIndex + MAX_QUERY_NUMBER_ONE_TIME), csnList.size())));
            queryAlarmResult.addAll(queryAlarmResultOnce);
            startIndex += MAX_QUERY_NUMBER_ONE_TIME;
        }
        return queryAlarmResult;
    }

    /**
     * 根据流水号查询告警信息 流水号数量建议不超过200个
     *
     * @param csnList 流水号列表
     * @return 告警信息列表
     */
    public List<AlarmDetail> getAlarmListByCsn(List<String> csnList) {
        List<AlarmDetail> result = new ArrayList<>();
        RestfulResponse response;
        try {
            String csnUrl = AlarmConstant.GET_CURRENT_ALARMS_BY_CSN;
            for (String csn : csnList) {
                csnUrl = RestUtil.addUrlParam(csnUrl, CSNS, csn);
            }
            response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_GET, csnUrl, null, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("[AlarmAnalysisResultServiceImpl] getAlarmDetail response error,response = {}", response);
                return result;
            } else {
                List<AlarmDetail> alarmDetailResults = JSONArray.parseArray(response.getResponseContent(),
                    AlarmDetail.class);
                result.addAll(alarmDetailResults);
            }
            if (CollectionUtils.isEmpty(result)) {
                LOGGER.info("alarmDetailResults is empty");
                return result;
            }
        } catch (ServiceException e) {
            LOGGER.error("[AlarmAnalysisResultServiceImpl] getAlarmDetail error, e={}", e);
        }
        return result;
    }

    /**
     * 根据流水号查询事件信息 流水号数量建议不超过200个
     *
     * @param csnList 流水号列表
     * @return 事件信息列表
     */
    public List<AlarmDetail> getEventListByCsn(List<String> csnList) {
        List<AlarmDetail> result = new ArrayList<>();
        RestfulResponse response;
        try {
            String csnUrl = AlarmConstant.GET_EVENTS_CSN;
            for (String csn : csnList) {
                csnUrl = RestUtil.addUrlParam(csnUrl, CSNS, csn);
            }
            response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_GET, csnUrl, null, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("[AlarmAnalysisResultServiceImpl] getEventDetail response error,response = {}", response);
                return result;
            } else {
                List<AlarmDetail> alarmDetailResults = JSONArray.parseArray(response.getResponseContent(),
                    AlarmDetail.class);
                result.addAll(alarmDetailResults);
            }
            if (CollectionUtils.isEmpty(result)) {
                LOGGER.info("eventDetailResults is empty");
                return result;
            }
        } catch (ServiceException e) {
            LOGGER.error("[AlarmAnalysisResultServiceImpl] getEventDetail error, e={}", e);
        }
        return result;
    }

    public ResponseEntity alarmCondition(String param) {
        ResponseEntity responseEntity = new ResponseEntity();
        try {
            String path = AlarmConstant.GET_ALARM_JUMPING;
            int statusCode = 0;
            int count = 0;
            RestfulResponse response = null;
            RestfulParametes parametes = new RestfulParametes();
            parametes.setRawData(param);
            while (statusCode != DVTopoServiceConstant.CommonConstant.SUCCESS_STATUS) {
                if (count > 5) {
                    break;
                }
                LOGGER.warn("start to execute retryTime:{}.", count + 1);
                response = com.huawei.i2000.dvtopowebsite.util.RestUtil.sendRestRequest(
                    com.huawei.i2000.dvtopowebsite.util.RestConstant.RESTFUL_METHOD_POST, path, parametes, null);
                statusCode = response.getStatus();
                LOGGER.info("execute finish. statusCode={},response={}", statusCode, response);
                count++;
            }
            responseEntity.setData(response.getResponseContent());
            responseEntity.setResultCode(com.huawei.i2000.dvtopowebsite.util.RestConstant.SUCCESS_CODE);
            responseEntity.setResultMessage(StringUtil.valueOf(response.getStatus()));
        } catch (ServiceException e) {
            LOGGER.error("alarmCondition failed, error is ", e);
        }
        return responseEntity;
    }

    /**
     * 根据传入的网元类型，告警Id，获取对应的告警静态信息
     *
     * @param context 上下文
     * @param moTypeList 网元类型列表
     * @return 事件信息列表
     */
    public List<AlarmStaticInfo> getAlarmInfoById(HttpContext context, List<String> moTypeList) {
        List<AlarmStaticInfo> alarmStaticInfos = new ArrayList<>();
        // 封装参数
        DefinitionQueryParam definitionQueryParam = new DefinitionQueryParam();
        try {
            // 告警分组标识数组
            if (CollectionUtils.isNotEmpty(moTypeList)) {
                definitionQueryParam.setAlarmGroupIds(moTypeList);
            }
            getAlarmStaticInfoByCondition(definitionQueryParam, alarmStaticInfos);
        } catch (ServiceException e) {
            LOGGER.error("[getAlarmInfoById] get alarmInfo failed, Exception:", e);
        }
        return alarmStaticInfos;
    }

    /**
     * 查询告警自定义列表
     *
     * @param definitionQueryParam 告警自定义列表查询参数
     * @param alarmStaticInfos 结果数组
     *
     * @throws ServiceException ex
     */
    public void getAlarmStaticInfoByCondition(DefinitionQueryParam definitionQueryParam,
        List<AlarmStaticInfo> alarmStaticInfos) throws ServiceException {

        RestfulParametes restfulParametes = new RestfulParametes();
        restfulParametes.setRawData(JSON.toJSONString(definitionQueryParam));
        RestfulResponse restfulResponse = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
            GET_ALARM_STATIC_INFO_URL, restfulParametes, null);
        if (RestConstant.STATUS_OK != restfulResponse.getStatus()) {
            LOGGER.error(
                "[AlarmRequestService] getAlarmStaticInfoByCondition error, the result status is {}, the error message is {}",
                restfulResponse.getStatus(), restfulResponse.getResponseContent());
            return;
        }
        // 解析告警静态信息结果
        DefinitionPageQueryResult definitionPageQueryresult = JSON.parseObject(restfulResponse.getResponseContent(),
            DefinitionPageQueryResult.class);
        List<AlarmDefinition> alarmDefinitions = definitionPageQueryresult.getDefinitions();
        // 接口返回总数目
        int totalCount = definitionPageQueryresult.getTotalCount();
        // 分页查询接口，下一个调用时需要的分页起始位置
        int offset = definitionPageQueryresult.getOffset() + definitionPageQueryresult.getLimit();
        // 循环获得所有静态信息
        while (offset < totalCount) {
            definitionQueryParam.setOffset(offset);
            String definitionQueryParamJson = JSON.toJSONString(definitionQueryParam);
            RestfulParametes definitionQueryParamJsonParam = new RestfulParametes();
            definitionQueryParamJsonParam.setRawData(definitionQueryParamJson);
            RestfulResponse result = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
                GET_ALARM_STATIC_INFO_URL, definitionQueryParamJsonParam, null);
            if (RestConstant.STATUS_OK == result.getStatus()) {
                DefinitionPageQueryResult definitionPage = JSON.parseObject(result.getResponseContent(),
                    DefinitionPageQueryResult.class);
                alarmDefinitions.addAll(definitionPage.getDefinitions());
                offset = definitionPage.getOffset() + definitionPageQueryresult.getLimit();
            } else {
                LOGGER.error(
                    "[AlarmRequestService] getAlarmStaticInfoByCondition error, the result status is {}, the error message is {}",
                    result.getStatus(), result.getResponseContent());
                break;
            }
        }
        // 将最终结果转换
        loadValidateAlarmInfo(alarmStaticInfos, alarmDefinitions);
    }

    private void loadValidateAlarmInfo(List<AlarmStaticInfo> alarmStaticInfos, List<AlarmDefinition> alarmDefinitions) {
        if (CollectionUtils.isEmpty(alarmDefinitions)) {
            return;
        }

        for (AlarmDefinition item : alarmDefinitions) {
            AlarmStaticInfo alarmStaticInfo = new AlarmStaticInfo();
            EventType eventType = EventType.getEventType(item.getEventType());
            String alarmType = MessageUtil.getMessage(MESSAGE_PART + eventType.name());
            alarmStaticInfo.setAlarmId(item.getAlarmId());
            alarmStaticInfo.setAlarmName(item.getAlarmName());
            alarmStaticInfo.setAlarmType(alarmType);
            alarmStaticInfo.setAlarmGroupId(item.getAlarmGroupId());
            alarmStaticInfo.setAlarmGroupName(item.getAlarmGroupName());
            alarmStaticInfo.setCategory(item.getCategory());
            alarmStaticInfos.add(alarmStaticInfo);
        }
    }
}
