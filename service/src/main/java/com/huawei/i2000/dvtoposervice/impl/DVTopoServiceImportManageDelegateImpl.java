/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.impl;

import com.huawei.bsp.deploy.util.DefaultEnvUtil;
import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.i2000.cbb.security.xxe.SecurityInstance;
import com.huawei.i2000.cbb.security.zip.ZipUtils;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmRuleRecord;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.ModelAlarm;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModelWithInstance;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.BusinessAlarmInitService;
import com.huawei.i2000.dvtoposervice.business.service.bean.AlarmInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppInstanceConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppInstanceInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.business.service.bean.ContainerConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.EventInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.GlobalAlarmConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.PodConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.PodType;
import com.huawei.i2000.dvtoposervice.business.service.bean.TimeLineConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.VmConfig;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.business.topo.solutiontemplatevalidate.SolutionTemplateValidator;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.ModelIdGen;
import com.huawei.i2000.dvtoposervice.business.topo.upgrade.CloudDrCommonModelProcessor;
import com.huawei.i2000.dvtoposervice.business.topo.upgrade.ConfigurationImportUpgrade;
import com.huawei.i2000.dvtoposervice.constant.AlarmConstant;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessAlarmDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessAlarmRuleDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelExtentAttrDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessConfigDataDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.delegate.DVTopoServiceImportManageDelegate;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.EscapePkgVo;
import com.huawei.i2000.dvtoposervice.model.FileDetail;
import com.huawei.i2000.dvtoposervice.model.ImportObject;
import com.huawei.i2000.dvtoposervice.model.PkgManageRsp;
import com.huawei.i2000.dvtoposervice.util.CheckImportPackageUtil;
import com.huawei.i2000.dvtoposervice.util.ConfigurationUtil;
import com.huawei.i2000.dvtoposervice.util.ContextUtils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.xml.sax.SAXException;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.ws.rs.core.Response;

/**
 * 包管理导入功能
 *
 * <AUTHOR>
 * @since 2024/4/9
 */
@Component
public class DVTopoServiceImportManageDelegateImpl implements DVTopoServiceImportManageDelegate, ModelIdGen {

    @Autowired
    private ConfigurationImportUpgrade configurationImportUpgrade;

    @Autowired
    BusinessAlarmDao alarmDao;

    @Autowired
    private BusinessAlarmRuleDao alarmRuleDao;

    @Autowired
    BusinessCommonModelDao businessCommonModelDao;

    @Autowired
    private BusinessInstanceModelDao instanceModelDao;

    @Autowired
    BusinessCommonModelExtentAttrDao businessCommonModelExtentAttrDao;

    @Autowired
    BusinessAlarmInitService businessAlarmInitService;

    @Autowired
    private CloudDrCommonModelProcessor cloudDrCommonModelProcessor;

    @Autowired
    private ConfigurationHandlerForQueue configurationHandlerForQueue;

    @Autowired
    private BusinessConfigDataDao configDataDao;

    private static final OssLog LOGGER = OssLogFactory.getLogger(DVTopoServiceImportManageDelegateImpl.class);
    private static final String SUCCESSFUL = "SUCCESSFUL";

    private static final String PARTIAL_SUCCESS = "PARTIAL_SUCCESS";

    private static final String FAILURE = "FAILURE";

    private static final String EXECUTING = "EXECUTING";

    private static final String PACKAGE_DESC = "package_desc.xml";

    private static final String IMPORT_DIR_NAME = "TopoServiceImport";


    private static final int MAX_FILE_NUM = 50;

    private static final Pattern FILE_NAME_REGEX = Pattern.compile("^[0-9a-zA-Z_-]+$");

    private static final String UNDERLINE = "_";

    private static final Integer ALARM_TYPE = 1;

    private static final Integer EVENT_TYPE = 3;

    private static final Integer TIME_LINE_LISTEN_SCOPE = 0;

    private static final Integer MEMBER_LISTEN_SCOPE = 1;

    private static final Integer BASIC_ALARM_ID = 0;

    private static final Integer PLATFORM = 1;

    private static final Integer ALL_ALARMS = 2;


    private static Locale defaultLocale = DefaultEnvUtil.getOssLocale();


    @Override
    public PkgManageRsp installPkg(HttpContext context, EscapePkgVo param) {
        long startTime = System.currentTimeMillis();
        LOGGER.info("installPkg start");
        setContextUtils();
        PkgManageRsp response = new PkgManageRsp();
        String tempZipPath = DefaultEnvUtil.getAppTmpDir() + File.separator + "installpkg" + File.separator + UUID.randomUUID();
        String unzipPath = tempZipPath + File.separator + "unzip";
        try {
            prepareTempPath(tempZipPath);
            File tempZip = new File(tempZipPath, "Topo_Package.zip");
            decodeBase64File(param.getPkgFile(), tempZip.getCanonicalPath());
            LOGGER.info("[installPkg] decode file from request to {}", tempZip.getCanonicalPath());
            if (CheckImportPackageUtil.checkZipSize(tempZipPath, tempZip, response)) {
                return response;
            }
            // 解压到unzipPath
            ZipUtils.getInst().unzipSingle(tempZip.getCanonicalPath(), unzipPath, true);
            if (CheckImportPackageUtil.checkSign(tempZipPath, unzipPath, response)) {
                return response;
            }
            if (CheckImportPackageUtil.checkFliesCount(tempZipPath, unzipPath, response)) {
                return response;
            }
            ImportObject importObject = new ImportObject();
            parseXml(importObject, unzipPath);
            importObject.setResult(EXECUTING);
            importObject.setTime(System.currentTimeMillis());
            go(unzipPath, tempZipPath, importObject);
            while (EXECUTING.equals(importObject.getResult()) && (System.currentTimeMillis() - startTime < 270000)) {
                TimeUnit.SECONDS.sleep(5);
            }
            response.setStatus(String.valueOf(Response.Status.OK.getStatusCode()));
            response.setContent("import finish,result is :" + JSONObject.toJSONString(importObject));
            LOGGER.info("installPkg return");
        } catch (Exception e) {
            LOGGER.error("[installPkg] handle zip fail,e=", e);
            response.setStatus(String.valueOf(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode()));
            response.setContent("handle zip fail");
            FileUtils.deleteQuietly(new File(tempZipPath));
            return response;
        } finally {
            LOGGER.warn("installPkg_end_costTime: {} ms", System.currentTimeMillis() - startTime);
        }
        return response;
    }

    private void setContextUtils() {
        // 包管理调用的接口没有用户信息，设置为管理员
        if (ContextUtils.getContext() == null || ContextUtils.getContext().getUserId() == null) {
            ContextUtils.init(true, "1");
        }
    }

    private void prepareTempPath(String tempZipPath) throws ServiceException {
        try {
            File file = new File(tempZipPath);
            if (file.isDirectory()) {
                FileUtils.deleteDirectory(file);
            }
            if (!file.isDirectory()) {
                boolean result = file.mkdirs();
                if (result) {
                    LOGGER.debug("Making dir succefully!");
                }
            }
        } catch (IOException exception) {
            LOGGER.error("mkdir {} failed", tempZipPath, exception);
            throw new ServiceException("mkdir temp file faile");
        }
    }

    private void decodeBase64File(String base64Code, String targetPath) throws ServiceException {
        try (FileOutputStream out = new FileOutputStream(targetPath)) {
            byte[] buffer = Base64.decodeBase64(base64Code);
            out.write(buffer);
        } catch (IOException e) {
            LOGGER.error("[installPkg]", e);
            throw new ServiceException("write file error");
        }
    }

    private void parseXml(ImportObject importObject, String unzipPath)
            throws SAXException, IOException, DocumentException, ServiceException {
        File file = new File(unzipPath + File.separator + PACKAGE_DESC);
        if (file.exists()) {
            SAXReader saxReader = SecurityInstance.getInst().newSecuritySAXReader();
            saxReader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            saxReader.setFeature("http://xml.org/sax/features/external-general-entities", false);
            saxReader.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            Document document = saxReader.read(file.getCanonicalPath());
            Element elementRoot = document.getRootElement();
            String name = elementRoot.element("name").getText();
            String version = elementRoot.element("version").getText();
            importObject.setName(name);
            importObject.setVersion(version);
        } else {
            LOGGER.error("file dos not exists,path={}", file.getCanonicalPath());
            throw new ServiceException("package_desc.xml dos not exists");
        }
    }


    private void go(String unzipPath, String tempZipPath, ImportObject importObject) {
        try {
            importData(unzipPath, importObject);
            confirmResult(importObject);
        } catch (Exception e) {
            LOGGER.error("[installPkg] importData fail,e=", e);
            importObject.setResult(FAILURE);
        } finally {
            FileUtils.deleteQuietly(new File(tempZipPath));
        }
    }

    private void importData(String unzipPath, ImportObject importObject) {
        Map<String, FileDetail> detail = new HashMap<>();
        importObject.setDetail(detail);
        File dir = new File(unzipPath + File.separator + IMPORT_DIR_NAME);
        if (!dir.isDirectory() || dir.listFiles() == null) {
            LOGGER.error("[installPkg] {} dir is error", IMPORT_DIR_NAME);
            return;
        }
        if (dir.listFiles().length > MAX_FILE_NUM) {
            LOGGER.error("[installPkg] {} dir files is exceeded the maximum", IMPORT_DIR_NAME);
            return;
        }
        // 平台的配置文件先导入
        Map<String, ConfigurationImportEntity> sortedConfiguration = getSortedConfiguration(dir, detail);

        Map<String, List<FileInfoBean>> solutionNameToConfigList = new HashMap<>();
        for (Map.Entry<String, ConfigurationImportEntity> entry : sortedConfiguration.entrySet()) {
            ConfigurationImportEntity configurationImportEntity = entry.getValue();
            solutionNameToConfigList.computeIfAbsent(configurationImportEntity.getSolutionName(),
                    a -> new ArrayList<>()).add(new FileInfoBean(entry.getKey(), configurationImportEntity));
        }
        for (Map.Entry<String, List<FileInfoBean>> entry : solutionNameToConfigList.entrySet()) {
            commonModelProcessing(detail, entry);
            if (isImportFileValidate(detail)) {
                offerTask(entry);
            }
        }
        // 按照解决方案名称来分批执行
        importObject.setDetail(detail);
    }

    public boolean isImportFileValidate(Map<String, FileDetail> detail) {
        boolean isValidate = true;
        for (FileDetail fileDetail : detail.values()) {
            if (fileDetail.getResult().equals(FAILURE)) {
                LOGGER.error("validate import file failed, file name = {}", fileDetail.getName());
                isValidate = false;
                break;
            }
        }
        return isValidate;
    }

    public void offerTask(Map.Entry<String, List<FileInfoBean>> entry) {
        boolean offer = configurationHandlerForQueue.offer(entry);
        if (!offer) {
            LOGGER.error("[Offer_file_info_for_instantiation_failed] fileName: {}", entry.getKey());
        }
    }

    public void configurationGo(Map.Entry<String, List<DVTopoServiceImportManageDelegateImpl.FileInfoBean>> entry) throws ServiceException, OSSException {
        List<ConfigurationImportEntity> configurationImportEntityList = entry.getValue().stream().map(FileInfoBean::getConfigurationImportEntity).collect(Collectors.toList());
        // 将菜单展示开关修改为true
        ConfigData configData = new ConfigData();
        configData.setConfigItemName(BusinessTopoConstant.BUSINESS_TOPO_MENU_OPEN);
        configData.setValue(String.valueOf(Boolean.TRUE));
        ConfigurationUtil.updateConfigDataByName(configData);
        for (ConfigurationImportEntity configurationImportEntity : configurationImportEntityList) {
            configurationImportUpgrade.commonModelUpgrade(configurationImportEntity);
            importTask(configurationImportEntity);
        }
    }

    private void commonModelProcessing(Map<String, FileDetail> detail, Map.Entry<String, List<FileInfoBean>> entry) {
        for (FileInfoBean fileInfoBean : entry.getValue()) {
            String fileName = fileInfoBean.getFileName();
            LOGGER.warn("[importData_] fileName: {}", fileName);
            ConfigurationImportEntity configurationImportEntity = fileInfoBean.getConfigurationImportEntity();
            // 处理基础模型
            LOGGER.info("Start processing the converted object with the file name {}", fileName);
            FileDetail fileDetail = new FileDetail();
            fileDetail.setName(fileName);
            try {
                fileDetail.setResult(EXECUTING);
                long startTime = System.currentTimeMillis();
                if (Objects.nonNull(configurationImportEntity)) {
                    SolutionTemplateValidator solutionTemplateValidator = new SolutionTemplateValidator();
                    solutionTemplateValidator.check(configurationImportEntity);
                }
                LOGGER.warn("[importTask_cost_time] {} ms", System.currentTimeMillis() - startTime);
                // 导入文件，带时间戳参数的
                fileDetail.setResult(SUCCESSFUL);
                LOGGER.info("[installPkg] file import success,name={}", fileName);
            } catch (ServiceException exception) {
                fileDetail.setResult(FAILURE);
                fileDetail.setErrorDesc(exception.getOriginDetailMessage());
                if (StringUtils.isEmpty(exception.getOriginDetailMessage())) {
                    if (exception.getExceptionArgs() != null) {
                        fileDetail.setErrorDesc(exception.getExceptionArgs().getDetailArgs()[0]);
                    }
                }
                LOGGER.error("[installPkg] file import fail,error=", exception);
            } catch (Exception e) {
                fileDetail.setResult(FAILURE);
                fileDetail.setErrorDesc("inner error!");
                LOGGER.error("[installPkg] file import fail, error=", e);
            }
            detail.put(fileName, fileDetail);
        }
    }

    public Map<String, ConfigurationImportEntity> getSortedConfiguration(File dir, Map<String, FileDetail> detail) {
        if (Objects.isNull(dir) || Objects.isNull(dir.listFiles())) {
            return Collections.emptyMap();
        }
        Map<String, ConfigurationImportEntity> res = getStringConfigurationImportEntityLinkedHashMap(dir, detail);
        res = res.entrySet()
                .stream()
                .sorted((e1, e2) -> {
                    int t1 = e1.getValue().getTemplateType();
                    int t2 = e2.getValue().getTemplateType();
                    if (t1 == PLATFORM && t2 != PLATFORM) {
                        return -1; // e1排在前面
                    } else if (t1 != PLATFORM && t2 == PLATFORM) {
                        return 1; // e2排在前面
                    } else {
                        return 0; // 保持原有顺序
                    }
                })
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue,
                        LinkedHashMap::new));
        return res;
    }

    private LinkedHashMap<String, ConfigurationImportEntity> getStringConfigurationImportEntityLinkedHashMap(File dir,
                                                                                                             Map<String, FileDetail> detail) {
        LinkedHashMap<String, ConfigurationImportEntity> res = new LinkedHashMap<>();
        File[] files = dir.listFiles();
        if (Objects.isNull(files)) {
            return res;
        }
        for (File file : files) {
            LOGGER.info("start to handle the file name of {}", file.getName());
            FileDetail fileDetail = new FileDetail();
            fileDetail.setName(file.getName());
            if (fileCheck(detail, file, fileDetail)) {
                continue;
            }
            String configurationImportEntityJson = null;
            try {
                configurationImportEntityJson = FileUtils.readFileToString(file, StandardCharsets.UTF_8);
            } catch (IOException e) {
                LOGGER.error("read file error! e: ", e);
                fileDetail.setResult(FAILURE);
                fileDetail.setErrorDesc("read file error, name=" + file.getName());
                detail.put(file.getName(), fileDetail);
                continue;
            }
            ConfigurationImportEntity configurationImportEntity = null;
            try {
                LOGGER.debug("[Configuration_import_file_content]: {}", configurationImportEntityJson);
                configurationImportEntity = JSON.parseObject(configurationImportEntityJson, ConfigurationImportEntity.class,
                    JSONReader.Feature.SupportSmartMatch);
            } catch (Exception exception) {
                LOGGER.error("json parse ConfigurationImportEntity error! e: ", exception);
                fileDetail.setResult(FAILURE);
                fileDetail.setErrorDesc("json parse error, name=" + file.getName());
                detail.put(file.getName(), fileDetail);
                continue;
            }
            res.put(file.getName(), configurationImportEntity);
        }
        return res;
    }

    private boolean fileCheck(Map<String, FileDetail> detail, File file, FileDetail fileDetail) {
        if (jsonFileCheck(file)) {
            fileDetail.setResult(FAILURE);
            fileDetail.setErrorDesc("file check fail, name=" + file.getName());
            LOGGER.error("[installPkg] file check fail,name={}", file.getName());
            detail.put(file.getName(), fileDetail);
            return true;
        }
        return fileLanguageCheck(file);
    }

    private boolean jsonFileCheck(File jsonFile) {
        if (jsonFile.length() > 1024 * 1024) {
            return true;
        }
        if (!jsonFile.getName().endsWith(".json")) {
            return true;
        }
        if (!FILE_NAME_REGEX.matcher(jsonFile.getName().substring(0, jsonFile.getName().lastIndexOf("."))).matches()) {
            return true;
        }
        return false;
    }

    private boolean fileLanguageCheck(File file) {
        String fileName = file.getName().substring(0, file.getName().lastIndexOf("."));
        return !fileName.endsWith(defaultLocale.getLanguage());
    }

    private void confirmResult(ImportObject importObject) {
        boolean successFlag = false;
        boolean failFlag = false;
        Map<String, FileDetail> details = importObject.getDetail();
        if (MapUtils.isEmpty(details)) {
            LOGGER.error("importObject detail is empty");
            importObject.setResult(FAILURE);
            return;
        }
        for (FileDetail detail : details.values()) {
            if (SUCCESSFUL.equals(detail.getResult())) {
                successFlag = true;
            } else {
                failFlag = true;
            }
        }
        if (successFlag && failFlag) {
            importObject.setResult(PARTIAL_SUCCESS);
        }
        if (successFlag && !failFlag) {
            importObject.setResult(SUCCESSFUL);
        }
        if (!successFlag && failFlag) {
            importObject.setResult(FAILURE);
        }
    }


    private void importTask(ConfigurationImportEntity configurationImportEntity) throws ServiceException, OSSException {
        long initBusinessTopoAlarmStart = System.currentTimeMillis();
        configurationImportUpgrade.instanceUpgrade(configurationImportEntity);
        LOGGER.warn("[importTask_instanceUpgrade_complete] cost_time: {}ms", System.currentTimeMillis() - initBusinessTopoAlarmStart);
        initBusinessTopoAlarmStart = System.currentTimeMillis();
        importAlarmAndEventConfiguration(configurationImportEntity);
        LOGGER.warn("[importTask_importAlarmAndEventConfiguration] cost_time: {}", System.currentTimeMillis() - initBusinessTopoAlarmStart);
    }

    public void alarmInfoInitiation() {
        // 导入告警和事件
        LOGGER.warn("[importTask_initBusinessTopoAlarm]");
        businessAlarmInitService.initImmediately();
        businessAlarmInitService.initUpdateHistoryAlarm();
    }

    /**
     * 导入告警和事件
     *
     * @param configurationImportEntity configurationImportEntity
     * @throws ServiceException 服务异常
     */
    public void importAlarmAndEventConfiguration(ConfigurationImportEntity configurationImportEntity) throws ServiceException {
        long importAlarmAndEventStart = System.currentTimeMillis();
        LOGGER.warn("[before_solutionTemplateValidator_importAlarmAndEvent]");
        Set<ModelAlarm> modelAlarms = new HashSet<>(insertTimelineConfigAlarm(configurationImportEntity));
        modelAlarms.addAll(insertGlobalAlarmConfig(configurationImportEntity));
        modelAlarms.addAll(insertBusinessAppInstanceConfigAlarm(configurationImportEntity));
        modelAlarms.addAll(insertPodConfigAlarm(configurationImportEntity));
        modelAlarms.addAll(insertVmConfigAlarm(configurationImportEntity));
        modelAlarms.addAll(insertContainerConfigAlarm(configurationImportEntity));

        clearAlarmConfigModelInfo(configurationImportEntity);
        // 插入告警规则记录
        insertAlarmRuleWhenNotExist(configurationImportEntity);

        modelAlarms.forEach(molAlr -> molAlr.setAlarmRuleId(AlarmConstant.PRESET_LABELING));
        alarmDao.insertModelAlarms(new ArrayList<>(modelAlarms));
        LOGGER.warn("solutionTemplateValidator_importAlarmAndEvent_cost_time {} ms",
                System.currentTimeMillis() - importAlarmAndEventStart);
    }

    private void insertAlarmRuleWhenNotExist(ConfigurationImportEntity configurationImportEntity) {
        List<AlarmRuleRecord> allAlarmRuleRecordList = alarmRuleDao.queryAlarmRuleRecordBySol(configurationImportEntity.getSolutionName());
        insertAlarmRuleWhenImport(configurationImportEntity, allAlarmRuleRecordList, AlarmConstant.TIMELINE);
        insertAlarmRuleWhenImport(configurationImportEntity, allAlarmRuleRecordList, AlarmConstant.MEMBER);
    }

    private void insertAlarmRuleWhenImport(ConfigurationImportEntity configurationImportEntity,
        List<AlarmRuleRecord> allAlarmRuleRecordList, int listeningScope) {
        List<AlarmRuleRecord> presetAlarmRuleRecordList = allAlarmRuleRecordList.stream()
            .filter(alrRecord -> AlarmConstant.PRESET_LABELING.equals(alrRecord.getAlarmRuleId())
                && listeningScope == alrRecord.getListeningScope() && configurationImportEntity.getSolutionName().equals(
                alrRecord.getSolutionName()))
            .collect(Collectors.toList());

        String modelId = createModelId(configurationImportEntity.getModelType(),
            configurationImportEntity.getModelName(), configurationImportEntity.getSolutionName());

        String alarmRuleName = StringUtils.EMPTY;
        switch (listeningScope) {
            case AlarmConstant.TIMELINE:
                alarmRuleName = "TimeLine";
                TimeLineConfig timeLineConfig = configurationImportEntity.getTimeLineConfig();
                if (Objects.isNull(timeLineConfig) || Objects.isNull(timeLineConfig.getAlarmFilterType())) {
                    return;
                }
                break;
            case AlarmConstant.MEMBER:
                GlobalAlarmConfig globalAlarmConfig = configurationImportEntity.getGlobalAlarmConfig();
                alarmRuleName = "Healthy";
                if (Objects.isNull(globalAlarmConfig) || Objects.isNull(globalAlarmConfig.getAlarmFilterType())) {
                    return;
                }
                break;
        }

        if (CollectionUtils.isEmpty(presetAlarmRuleRecordList)) {
            AlarmRuleRecord alarmRuleTimeLineRecord = new AlarmRuleRecord(AlarmConstant.PRESET_LABELING, alarmRuleName,
                configurationImportEntity.getSolutionName(), modelId, listeningScope);
            List<AlarmRuleRecord> importRecordList = new ArrayList<>();
            importRecordList.add(alarmRuleTimeLineRecord);
            alarmRuleDao.insertAlarmRuleRecord(importRecordList);
        }
    }

    private void clearAlarmConfigModelInfo(ConfigurationImportEntity configurationImportEntity) {
        // 预置的告警模型直接删除再重新导入
        List<ModelAlarm> modelAlarmList = alarmDao.queryModelAlarmBySolution(
            configurationImportEntity.getSolutionName());
        Optional<ModelAlarm> preSetModel = modelAlarmList.stream()
            .filter(alr -> AlarmConstant.PRESET_LABELING.equals(alr.getAlarmRuleId()))
            .findFirst();

        if (preSetModel.isPresent()) {
            ModelAlarm preset = preSetModel.get();
            alarmDao.deleteModelAlarmByModelIdAndScope(preset.getModelId(), AlarmConstant.TIMELINE, preset.getAlarmRuleId());
            alarmDao.deleteModelAlarmByModelIdAndScope(preset.getModelId(), AlarmConstant.MEMBER, preset.getAlarmRuleId());
        } else {
            LOGGER.debug("[importTask_importAlarmAndEventConfiguration] when import package, there is no alarm model, sol is {}", configurationImportEntity.getSolutionName());
        }
    }

    @Setter
    @Getter
    static class InsertAlarmInfoParam {
        private String modelId;
        private Integer modelType;
        private Integer alarmFilterType;
        private String alarmSeverity;
        private Integer alarmClearType;
        private Integer scope;
        private String moType;
        private List<AlarmInfo> alarmInfoList = new ArrayList<>();
        private List<EventInfo> eventInfoList = new ArrayList<>();

        public InsertAlarmInfoParam(String modelId, Integer modelType, Integer alarmFilterType, String alarmSeverity,
                                    Integer alarmClearType) {
            this.modelId = modelId;
            this.modelType = modelType;
            this.alarmFilterType = alarmFilterType;
            this.alarmSeverity = alarmSeverity;
            this.alarmClearType = alarmClearType;
        }
    }

    public Set<ModelAlarm> insertTimelineConfigAlarm(ConfigurationImportEntity configurationImportEntity) {
        if (Objects.isNull(configurationImportEntity.getTimeLineConfig())) {
            return new HashSet<>();
        }

        LOGGER.info("start insertTimelineConfigAlarm");
        TimeLineConfig timeLineConfig = configurationImportEntity.getTimeLineConfig();
        String modelId = createModelId(configurationImportEntity.getModelType(), configurationImportEntity.getModelName(), configurationImportEntity.getSolutionName());
        if (BusinessTopoConstant.PLATFORM_CONFIGURATION_TEMPLATE_CLOUD_DR_TYPE.equals(configurationImportEntity.getTemplateType())) {
            modelId = cloudDrCommonModelProcessor.getTimeLineModelIdForCloudDr(configurationImportEntity);
        }

        InsertAlarmInfoParam param = new InsertAlarmInfoParam(modelId, configurationImportEntity.getModelType(),
                timeLineConfig.getAlarmFilterType(), timeLineConfig.getAlarmSeverity(), timeLineConfig.getAlarmClearType());
        param.setScope(TIME_LINE_LISTEN_SCOPE);
        param.setMoType(configurationImportEntity.getClassType());
        param.setAlarmInfoList(timeLineConfig.getAlarmList());
        param.setEventInfoList(timeLineConfig.getEventList());
        param.setAlarmFilterType(timeLineConfig.getAlarmFilterType());

        Set<ModelAlarm> modelAlarmList = buildAlarmModels(param, ALARM_TYPE);
        eventToAlarmTransfer(param);
        modelAlarmList.addAll(buildAlarmModels(param, EVENT_TYPE));
        return modelAlarmList;
    }

    public Set<ModelAlarm> insertGlobalAlarmConfig(ConfigurationImportEntity configurationImportEntity) {
        if (Objects.isNull(configurationImportEntity.getGlobalAlarmConfig())) {
            return new HashSet<>();
        }
        LOGGER.info("start insertGlobalAlarmConfig");
        GlobalAlarmConfig globalAlarmConfig = configurationImportEntity.getGlobalAlarmConfig();
        String modelId = createModelId(configurationImportEntity.getModelType(), configurationImportEntity.getModelName(), configurationImportEntity.getSolutionName());
        InsertAlarmInfoParam param = new InsertAlarmInfoParam(modelId, configurationImportEntity.getModelType(),
            globalAlarmConfig.getAlarmFilterType(), globalAlarmConfig.getAlarmSeverity(), globalAlarmConfig.getAlarmClearType());
        param.setScope(MEMBER_LISTEN_SCOPE);
        param.setMoType(configurationImportEntity.getClassType());
        param.setAlarmInfoList(globalAlarmConfig.getAlarmList());
        param.setEventInfoList(globalAlarmConfig.getEventList());
        param.setAlarmFilterType(globalAlarmConfig.getAlarmFilterType());

        Set<ModelAlarm> modelAlarmList = buildAlarmModels(param, ALARM_TYPE);
        eventToAlarmTransfer(param);
        modelAlarmList.addAll(buildAlarmModels(param, EVENT_TYPE));
        return modelAlarmList;
    }

    public List<ModelAlarm> insertBusinessAppInstanceConfigAlarm(ConfigurationImportEntity configurationImportEntity) {
        if (Objects.isNull(configurationImportEntity.getBusinessAppInstanceConfig())
                || CollectionUtils.isEmpty(configurationImportEntity.getBusinessAppInstanceConfig().getBusinessAppInstanceList())) {
            return new ArrayList<>();
        }
        LOGGER.info("start insertBusinessAppInstanceConfigAlarm");
        BusinessAppInstanceConfig businessAppInstanceConfig = configurationImportEntity.getBusinessAppInstanceConfig();
        Integer modelType = businessAppInstanceConfig.getModelType();
        List<BusinessAppInstanceInfo> businessAppInstanceList = businessAppInstanceConfig.getBusinessAppInstanceList();

        List<ModelAlarm> modelAlarmList = new ArrayList<>();
        for (BusinessAppInstanceInfo businessAppInstanceInfo : businessAppInstanceList) {
            String modelId = createModelId(modelType, businessAppInstanceInfo.getModelName(), configurationImportEntity.getSolutionName());
            InsertAlarmInfoParam param = new InsertAlarmInfoParam(modelId, modelType,
                    businessAppInstanceInfo.getAlarmFilterType(), businessAppInstanceInfo.getAlarmSeverity(),
                    businessAppInstanceInfo.getAlarmClearType());
            param.setScope(MEMBER_LISTEN_SCOPE);
            param.setMoType(businessAppInstanceInfo.getMoTypeMapping());
            param.setAlarmInfoList(businessAppInstanceInfo.getAlarmList());
            param.setEventInfoList(businessAppInstanceInfo.getEventList());
            modelAlarmList.addAll(buildAlarmModels(param, ALARM_TYPE));
            eventToAlarmTransfer(param);
            modelAlarmList.addAll(buildAlarmModels(param, EVENT_TYPE));
        }
        LOGGER.warn("[insertBusinessAppInstanceConfigAlarm_modelAlarmList]: {}", modelAlarmList.stream()
            .map(ModelAlarm::getModelId).collect(Collectors.toList()));
        return modelAlarmList;
    }


    private Map<String, List<BusinessCommonModelWithInstance>> getPodModelIdToDockerInsListMap(PodConfig podConfig) {
        Map<String, List<BusinessCommonModelWithInstance>> podModelIdToDockerInsListMap = new HashMap<>();
        for (PodType podType : podConfig.getPodTypeList()) {
            String modelId = podConfig.getModelType() + UNDERLINE + podType.getModelName();
            List<BusinessCommonModelWithInstance> dockerInstanceList = instanceModelDao.getNextLevelInsByCurModelId(modelId);
            if (CollectionUtils.isNotEmpty(dockerInstanceList)) {
                podModelIdToDockerInsListMap.put(modelId, dockerInstanceList);
            }
        }
        return podModelIdToDockerInsListMap;
    }

    public List<ModelAlarm> insertPodConfigAlarm(ConfigurationImportEntity configurationImportEntity) {
        if (Objects.isNull(configurationImportEntity.getPodConfig())
                || CollectionUtils.isEmpty(configurationImportEntity.getPodConfig().getPodTypeList())) {
            return new ArrayList<>();
        }

        LOGGER.info("start insertPodConfigAlarm");
        PodConfig podConfig = configurationImportEntity.getPodConfig();
        Integer podModelType = podConfig.getModelType();
        List<PodType> podTypeList = podConfig.getPodTypeList();
        List<ModelAlarm> modelAlarmList = new ArrayList<>();

        // 获取当前配置文件下能获取到的所有podModelId与其下所有的docker实例的映射
        Map<String, List<BusinessCommonModelWithInstance>> podModelIdToDockerInsListMap = getPodModelIdToDockerInsListMap(podConfig);

        for (PodType podType : podTypeList) {
            String podModelId = createModelId(podModelType, podType.getModelName(), configurationImportEntity.getSolutionName());
            // 创建pod的告警信息的时候，将所有无moType和配置了moType与pod的moType相同的部分获取，为其创建pod的告警信息
            List<AlarmInfo> alarmInfoList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(podType.getAlarmList())) {
                alarmInfoList = podType.getAlarmList().stream()
                        .filter(alarmInfo -> Objects.nonNull(alarmInfo)
                                && (Objects.isNull(alarmInfo.getMoType()) || Objects.equals(alarmInfo.getMoType(), podType.getMoTypeMapping())))
                        .collect(Collectors.toList());
            }

            // 创建pod的事件信息的时候，将所有无moType和配置了moType与pod的moType相同的部分获取，为其创建pod的事件信息
            List<EventInfo> eventList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(podType.getEventList())) {
                eventList = podType.getEventList().stream().filter(eventInfo -> Objects.nonNull(eventInfo)
                                && (Objects.isNull(eventInfo.getMoType()) || Objects.equals(eventInfo.getMoType(), podType.getMoTypeMapping())))
                        .collect(Collectors.toList());
            }

            InsertAlarmInfoParam param = new InsertAlarmInfoParam(podModelId, podModelType,
                    podType.getAlarmFilterType(), podType.getAlarmSeverity(), podType.getAlarmClearType());
            param.setScope(MEMBER_LISTEN_SCOPE);
            param.setAlarmInfoList(alarmInfoList);
            param.setEventInfoList(eventList);
            param.setMoType(podType.getMoTypeMapping());
            param.setAlarmFilterType(podType.getAlarmFilterType());
            // 获取pod层的告警
            Set<ModelAlarm> podModelAlarmList = buildAlarmModels(param, ALARM_TYPE);
            modelAlarmList.addAll(podModelAlarmList);
            // 获取pod层事件
            eventToAlarmTransfer(param);
            modelAlarmList.addAll(buildAlarmModels(param, EVENT_TYPE));

            // 处理docker部分的告警信息
            modelAlarmList.addAll(dockerAndContainerAlarmInsertHelper(podType, podModelIdToDockerInsListMap.get(podModelId), ALARM_TYPE));
            // 处理docker部分的事件信息
            poTypeEventToAlarmTransfer(podType);
            modelAlarmList.addAll(dockerAndContainerAlarmInsertHelper(podType, podModelIdToDockerInsListMap.get(podModelId), EVENT_TYPE));
        }
        LOGGER.warn("[insertPodConfigAlarm_modelAlarmList]: {}", modelAlarmList.stream()
            .map(ModelAlarm::getModelId).collect(Collectors.toList()));
        return modelAlarmList;
    }

    public Set<ModelAlarm> insertVmConfigAlarm(ConfigurationImportEntity configurationImportEntity) {
        if (Objects.isNull(configurationImportEntity.getVmConfig())) {
            return new HashSet<>();
        }

        LOGGER.info("start insertVmConfigAlarm");
        VmConfig vmConfig = configurationImportEntity.getVmConfig();
        Integer modelType = vmConfig.getModelType();
        String modelId = createModelId(modelType, vmConfig.getModelName(), configurationImportEntity.getSolutionName());

        InsertAlarmInfoParam param = new InsertAlarmInfoParam(modelId, modelType, vmConfig.getAlarmFilterType(),
                vmConfig.getAlarmSeverity(), vmConfig.getAlarmClearType());
        param.setScope(MEMBER_LISTEN_SCOPE);
        param.setModelType(vmConfig.getModelType());
        param.setMoType(vmConfig.getMoType());
        param.setAlarmInfoList(vmConfig.getAlarmList());
        param.setEventInfoList(vmConfig.getEventList());
        param.setAlarmFilterType(vmConfig.getAlarmFilterType());
        // 处理告警信息
        Set<ModelAlarm> modelAlarmSet = buildAlarmModels(param, ALARM_TYPE);
        // vm层事件
        eventToAlarmTransfer(param);
        modelAlarmSet.addAll(buildAlarmModels(param, EVENT_TYPE));
        LOGGER.warn("[insertPodConfigAlarm_modelAlarmList]: {}", modelAlarmSet);
        return modelAlarmSet;
    }

    public Set<ModelAlarm> insertContainerConfigAlarm(ConfigurationImportEntity configurationImportEntity) {
        ContainerConfig containerConfig = configurationImportEntity.getContainerConfig();
        if (Objects.isNull(containerConfig)) {
            LOGGER.debug("[insertContainerConfigAlarm] the container is not configure.");
            return Collections.emptySet();
        }
        Integer modelType = containerConfig.getModelType();
        String modelId = BusinessTopoConstant.CONTAINER_TYPE_ID + BusinessTopoConstant.DASH + BusinessTopoConstant.CONTAINER_COMMON_MODEL_NAME
            + BusinessTopoConstant.DASH + configurationImportEntity.getSolutionName();
        if (Objects.isNull(modelType) || StringUtils.isEmpty(containerConfig.getModelName())) {
            LOGGER.debug("[insertContainerConfigAlarm] the container data is missing.");
            return Collections.emptySet();
        }
        Integer alarmFilterType = containerConfig.getAlarmFilterType();
        if (Objects.isNull(alarmFilterType)) {
            alarmFilterType = ALL_ALARMS;
        }
        InsertAlarmInfoParam param = new InsertAlarmInfoParam(modelId, modelType, alarmFilterType,
                containerConfig.getAlarmSeverity(), containerConfig.getAlarmClearType());
        param.setScope(MEMBER_LISTEN_SCOPE);
        param.setModelType(containerConfig.getModelType());
        param.setMoType(containerConfig.getMoType());
        param.setAlarmFilterType(containerConfig.getAlarmFilterType());
        param.setAlarmInfoList(containerConfig.getAlarmList());
        param.setEventInfoList(containerConfig.getEventList());
        // containerConfig告警信息
        Set<ModelAlarm> modelAlarmSet = buildAlarmModels(param, ALARM_TYPE);
        eventToAlarmTransfer(param);
        modelAlarmSet.addAll(buildAlarmModels(param, EVENT_TYPE));
        LOGGER.warn("[insertContainerConfigAlarm_modelAlarmList]: {}", modelAlarmSet);
        return modelAlarmSet;
    }

    public Set<ModelAlarm> buildAlarmModels(InsertAlarmInfoParam param, Integer alarmType) {
        // 如果没有配置alarmFilterType则默认取0
        Integer alarmFilterType = Objects.isNull(param.getAlarmFilterType()) ? Integer.valueOf(0) : param.getAlarmFilterType();
        // alarmFilterType为2的情况, 直接为当前模型创建一条默认告警
        if (ALL_ALARMS.equals(alarmFilterType)) {
            return new HashSet<>(Collections.singletonList(alarmConfigToAlarmDb(param, alarmFilterType, BASIC_ALARM_ID, null, alarmType)));
        }
        // alarmFilterType不为2，但是告警级别、或者告警清理类型不为空，告警列表为空
        if ((StringUtils.isNotEmpty(param.getAlarmSeverity()) || Objects.nonNull(param.getAlarmClearType()))
                && CollectionUtils.isEmpty(param.getAlarmInfoList())) {
            return new HashSet<>(Collections.singletonList(
                    alarmConfigToAlarmDb(param, alarmFilterType, BASIC_ALARM_ID, null, alarmType)));
        }
        // 如果alarmFilterType非2，并且没有配置具体的告警信息，则无需生成告警配置
        if (CollectionUtils.isEmpty(param.getAlarmInfoList())) {
            LOGGER.warn("alarmInfoList is empty");
            return Collections.emptySet();
        }
        // alarmFilterType非2情况,则按照实际配置的告警信息进行配置
        return buildAlarmList(param, alarmFilterType, alarmType);
    }

    private Set<ModelAlarm> buildAlarmList(InsertAlarmInfoParam param, Integer alarmFilterType, Integer alarmType) {
        Set<ModelAlarm> modelAlarmSet = new HashSet<>();
        for (AlarmInfo alarmInfo : param.getAlarmInfoList()) {
            ModelAlarm modelAlarm = alarmConfigToAlarmDb(param, alarmFilterType, alarmInfo.getAlarmId(), alarmInfo.getMoType(), alarmType);
            modelAlarmSet.add(modelAlarm);
        }
        return modelAlarmSet;
    }

    private ModelAlarm alarmConfigToAlarmDb(InsertAlarmInfoParam param,
                                            Integer alarmFilterType,
                                            Integer alarmId,
                                            String moType,
                                            Integer alarmType) {
        ModelAlarm modelAlarm = new ModelAlarm();
        modelAlarm.setModelId(param.getModelId());
        modelAlarm.setModelType(param.getModelType());
        modelAlarm.setListeningScope(param.getScope());
        // alarmInfo的配置中可以moType可以为null（只有pod的alarmInfo中可以不为null，则使用当前模型的moType
        modelAlarm.setMoType(StringUtils.isEmpty(moType) ? param.moType : moType);
        modelAlarm.setAlarmType(alarmType);
        modelAlarm.setAlarmId(alarmId);
        modelAlarm.setListeningType(alarmFilterType);
        modelAlarm.setAlarmSeverity(param.getAlarmSeverity());
        modelAlarm.setAlarmClearType(param.getAlarmClearType());
        return modelAlarm;
    }

    public Set<ModelAlarm> dockerAndContainerAlarmInsertHelper(PodType podType,
                                                               List<BusinessCommonModelWithInstance> dockerInsList,
                                                               Integer alarmType) {
        // 如果alarmFilterType非2的情况下，alarmInfoList为空则不进行任何配置
        if (!Objects.equals(ALL_ALARMS, podType.getAlarmFilterType()) && CollectionUtils.isEmpty(podType.getAlarmList())) {
            return Collections.emptySet();
        }

        if (Objects.equals(ALL_ALARMS, podType.getAlarmFilterType()) && (CollectionUtils.isNotEmpty(dockerInsList))) {
            return getAlarmModelByFull(podType, dockerInsList, alarmType);
        }
        // alarmFilterType非2的情况下，获取当前alarmList中配置的docker和表中获取到的交集创建
        return buildNormalAlarmInfoListForDocker(podType, dockerInsList, alarmType);
    }

    private Set<ModelAlarm> getAlarmModelByFull(PodType podType, List<BusinessCommonModelWithInstance> dockerInsList,
                                                Integer alarmType) {
        Set<ModelAlarm> ans = new HashSet<>();
        for (BusinessCommonModelWithInstance instanceModelDB : dockerInsList) {
            ModelAlarm modelAlarm = new ModelAlarm();
            modelAlarm.setModelId(instanceModelDB.getModelId());
            modelAlarm.setModelType(instanceModelDB.getModelType());
            modelAlarm.setListeningScope(MEMBER_LISTEN_SCOPE);
            modelAlarm.setMoType(instanceModelDB.getModelName());
            modelAlarm.setAlarmType(alarmType);
            modelAlarm.setAlarmId(BASIC_ALARM_ID);
            modelAlarm.setListeningType(podType.getAlarmFilterType());
            modelAlarm.setAlarmSeverity(podType.getAlarmSeverity());
            modelAlarm.setAlarmClearType(podType.getAlarmClearType());
            ans.add(modelAlarm);
        }
        return ans;
    }

    private Set<ModelAlarm> buildNormalAlarmInfoListForDocker(PodType podType,
                                                              List<BusinessCommonModelWithInstance> dockerInsList, Integer alarmType) {
        Set<ModelAlarm> ans = new HashSet<>();
        if (CollectionUtils.isNotEmpty(podType.getAlarmList()) && CollectionUtils.isNotEmpty(dockerInsList)) {
            for (AlarmInfo alarmInfo : podType.getAlarmList()) {
                buildDockerAlarmInfoList(podType, alarmType, ans, dockerInsList, alarmInfo);
            }
        }
        return ans;
    }

    /**
     * docker告警模型实例化
     *
     * @param podType       父Pod信息
     * @param alarmType     告警类型
     * @param ans           结果
     * @param dockerInsList docker列表
     * @param alarmInfo     告警信息
     */
    public void buildDockerAlarmInfoList(PodType podType, Integer alarmType, Set<ModelAlarm> ans,
                                         List<BusinessCommonModelWithInstance> dockerInsList, AlarmInfo alarmInfo) {
        if (CollectionUtils.isNotEmpty(dockerInsList)) {
            for (BusinessCommonModelWithInstance modelWithInstance : dockerInsList) {
                ModelAlarm modelAlarm = new ModelAlarm();
                modelAlarm.setModelId(modelWithInstance.getModelId());
                modelAlarm.setModelType(modelWithInstance.getModelType());
                modelAlarm.setListeningScope(MEMBER_LISTEN_SCOPE);
                modelAlarm.setMoType(modelWithInstance.getModelName());
                modelAlarm.setAlarmType(alarmType);
                modelAlarm.setAlarmId(alarmInfo.getAlarmId());
                modelAlarm.setAlarmSeverity(podType.getAlarmSeverity());
                modelAlarm.setAlarmClearType(podType.getAlarmClearType());
                modelAlarm.setListeningType(Objects.isNull(podType.getAlarmFilterType()) ? Integer.valueOf(0) : podType.getAlarmFilterType());
                ans.add(modelAlarm);
            }
        }
    }

    public void eventToAlarmTransfer(InsertAlarmInfoParam param) {
        if (CollectionUtils.isNotEmpty(param.getEventInfoList())) {
            // 由于eventInfo和AlarmInfo结构相同，转换之后复用告警的生成逻辑
            List<AlarmInfo> alarmInfoList = param.getEventInfoList().stream().map(eventInfo -> {
                AlarmInfo alarmInfo = new AlarmInfo();
                alarmInfo.setMoType(eventInfo.getMoType());
                alarmInfo.setAlarmId(eventInfo.getEventId());
                return alarmInfo;
            }).collect(Collectors.toList());
            param.setAlarmInfoList(alarmInfoList);
        } else {
            param.setAlarmInfoList(new ArrayList<>());
        }
    }

    public void poTypeEventToAlarmTransfer(PodType podType) {
        if (CollectionUtils.isNotEmpty(podType.getEventList())) {
            // 由于eventInfo和AlarmInfo结构相同，转换之后复用告警的生成逻辑
            List<AlarmInfo> alarmInfoList = podType.getEventList().stream().map(eventInfo -> {
                AlarmInfo alarmInfo = new AlarmInfo();
                alarmInfo.setMoType(eventInfo.getMoType());
                alarmInfo.setAlarmId(eventInfo.getEventId());
                return alarmInfo;
            }).collect(Collectors.toList());
            podType.setAlarmList(alarmInfoList);
        } else {
            podType.setAlarmList(new ArrayList<>());
        }
    }


    @Getter
    public static class FileInfoBean {
        private final String fileName;
        private final ConfigurationImportEntity configurationImportEntity;

        public FileInfoBean(String fileName, ConfigurationImportEntity configurationImportEntity) {
            this.fileName = fileName;
            this.configurationImportEntity = configurationImportEntity;
        }
    }
}

