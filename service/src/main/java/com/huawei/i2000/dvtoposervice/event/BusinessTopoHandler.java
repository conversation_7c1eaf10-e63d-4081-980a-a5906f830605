/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.event;

import com.huawei.bsp.exception.OSSException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.redis.oper.MapOper;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvtoposervice.bean.businesstopo.AlarmDetail;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCacheInstance;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModel;
import com.huawei.i2000.dvtoposervice.business.database.BusinessCommonModelExtentAttr;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInsExtentAttrDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceModelDB;
import com.huawei.i2000.dvtoposervice.business.database.BusinessInstanceRelationModel;
import com.huawei.i2000.dvtoposervice.business.entity.IndicatorEntity;
import com.huawei.i2000.dvtoposervice.business.openapi.alarm.AlarmNotificationService;
import com.huawei.i2000.dvtoposervice.business.pm.PmIndicatorInstanceService;
import com.huawei.i2000.dvtoposervice.business.service.bean.AlarmInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.AssociationAppLink;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAddEvents;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppGroupConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppGroupInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppInstanceConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppInstanceInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppType;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessAppTypeConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessMoEvent;
import com.huawei.i2000.dvtoposervice.business.service.bean.BusinessSiteConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.ConfigurationImportEntity;
import com.huawei.i2000.dvtoposervice.business.service.bean.EventInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.IncludeAppTypeInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.IndicatorInfo;
import com.huawei.i2000.dvtoposervice.business.service.bean.RelationEvent;
import com.huawei.i2000.dvtoposervice.business.service.bean.SiteDataSourceConfig;
import com.huawei.i2000.dvtoposervice.business.service.bean.SiteGroupConfig;
import com.huawei.i2000.dvtoposervice.business.topo.BusinessTopoDvSelfService;
import com.huawei.i2000.dvtoposervice.business.topo.configrecover.ConfigurationRecoverManager;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessConfigEnum;
import com.huawei.i2000.dvtoposervice.business.topo.constantprops.BusinessTopoConstant;
import com.huawei.i2000.dvtoposervice.business.topo.redis.RedisExOper;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.AddingSiteModelHandler;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.BusinessTopoAlarmStorageService;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.ModelIdGen;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.ResourceInstanceTreeService;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.SiteGroupSlicer;
import com.huawei.i2000.dvtoposervice.business.topo.topofunction.TopoInstanceRelationCreator;
import com.huawei.i2000.dvtoposervice.constant.RedisConstant;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessCommonModelExtentAttrDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessIndicatorDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceModelDao;
import com.huawei.i2000.dvtoposervice.dao.service.BusinessInstanceRelationDao;
import com.huawei.i2000.dvtoposervice.impl.DVTopoServiceDelegateImpl;
import com.huawei.i2000.dvtoposervice.model.ConfigData;
import com.huawei.i2000.dvtoposervice.model.Indicator;
import com.huawei.i2000.dvtoposervice.util.ConfigurationUtil;
import com.huawei.i2000.dvtoposervice.util.EamUtil;
import com.huawei.i2000.dvtoposervice.util.SiteIdUtil;
import com.huawei.i2000.dvtoposervice.util.ThreadPoolMgr;
import com.huawei.i2000.eam.client.mim.MITManagerClient;
import com.huawei.oms.eam.mim.MORelation;
import com.huawei.oms.eam.mim.RelationType;
import com.huawei.oms.eam.mo.DN;
import com.huawei.oms.eam.mo.Host;
import com.huawei.oms.eam.mo.MOType;
import com.huawei.oms.eam.mo.ManagedElement;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.Vector;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 业务Topo业务变更
 *
 * <AUTHOR>
 * @since 2024/3/18
 */
@Component
public class BusinessTopoHandler implements ModelIdGen {

    private static final OssLog LOGGER = OssLogFactory.getLogger(BusinessTopoHandler.class);

    private static final String STRING = "String.class";

    private static final String INTEGER = "Integer.class";

    private static final String INSTANCE = "instance";

    private static final String ALARM_CSN = "AlarmCsn";

    private static final String EVENT_CSN = "EventCsn";

    private static final String DATA_BASE = "DataBase";

    /**
     * 资源事件队列大小
     */
    private static final int DEFAULT_QUEUE_CAPACITY = 40000;

    private static final ConcurrentLinkedQueue<BusinessMoEvent> MO_EVENT_QUEUE = new ConcurrentLinkedQueue<>();

    private static final int MAX_EVENT_SIZE = 200;

    private static final RedisExOper REDIS_EX_OPER = new RedisExOper(RedisConstant.MO_STORE_LIST, RedisConstant.REDIS_NAME);

    private static final String VM = "Vm";

    private static final String CONTAINER = "Container";

    private static final Set<String> MM_DB_TYPE = new HashSet<>(Arrays.asList("com.huawei.gaussdb", "com.huawei.venus.VGSftpService.Sftp"));

    private static final String GSU = "gsu";

    private static final String CSU_00 = "csu00";

    private static final String RSU = "rsu";

    private static final String CSU = "csu";

    private static final Set<Integer> CHECK_SOLUTION_MODEL_TYPE = new HashSet<>(Arrays.asList(BusinessTopoConstant.CLUSTER_INSTANCE_ID, BusinessTopoConstant.POD_TYPE_ID, BusinessTopoConstant.DOCKER_TYPE_ID));

    private static final int ONE = 1;

    private static final String MM_TYPE = "3";

    @Autowired
    BusinessInstanceModelDao businessInstanceModelDao;

    @Autowired
    BusinessInstanceRelationDao businessInstanceRelationDao;

    @Autowired
    BusinessCommonModelDao businessCommonModelDao;

    @Autowired
    BusinessCommonModelExtentAttrDao businessCommonModelExtentAttrDao;

    @Autowired
    AddingSiteModelHandler addingSiteModelHandler;

    @Autowired
    PmIndicatorInstanceService pmIndicatorInstanceService;

    @Autowired
    BusinessIndicatorDao businessIndicatorDao;

    @Autowired
    ResourceInstanceTreeService resourceInstanceTreeService;

    @Autowired
    AlarmNotificationService alarmNotificationService;

    @Autowired
    SiteGroupSlicer siteGroupSlicer;

    @Autowired
    BusinessTopoDvSelfService businessTopoDvSelfService;

    @Autowired
    DVTopoServiceDelegateImpl dvTopoServiceDelegateImpl;

    @Autowired
    TopoInstanceRelationCreator topoInstanceRelationCreator;

    @Autowired
    BusinessTopoAlarmStorageService businessTopoAlarmStorageService;

    @Autowired
    ConfigurationRecoverManager configurationRecoverManager;

    private final MapOper<BusinessCacheInstance> businessTreeMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_RELATION_TREE, RedisConstant.REDIS_NAME);

    private final MapOper<BusinessCommonModel> businessCommonModelMapOper = new MapOper<>(
            RedisConstant.BUSINESS_TOPO_MO_COMMON_MODEL, RedisConstant.REDIS_NAME);

    private static final ExecutorService EXECUTOR = Executors.newFixedThreadPool(1,
        new ThreadPoolMgr.TopoThreadFactory());

    private static final ThreadPoolExecutor INDICATOR_EXECUTOR = new ThreadPoolExecutor(4,
        10, 10, TimeUnit.MINUTES, new LinkedBlockingQueue<>(DEFAULT_QUEUE_CAPACITY));

    {
        Future<?> future = EXECUTOR.submit(new ExecuteMoEvent());
        if (null == future) {
            LOGGER.error("EXECUTOR submit new ExecuteMoEvent() is false");
        }
    }

    private class ExecuteMoEvent implements Runnable {
        @Override
        public void run() {
            while (true) {
                try {
                    List<BusinessMoEvent> moEvents = new ArrayList<>();
                    Long startTime = System.currentTimeMillis();
                    ConfigData configData = ConfigurationUtil.getConfigDataByName(BusinessTopoConstant.HANDLE_ADD_MO_WAIT_TIME);
                    if (configData == null) {
                        Thread.sleep(30 * 1000);
                        LOGGER.error("query handle add mo wait time config is null");
                        continue;
                    }
                    long waitTime = Long.parseLong(configData.getValue());

                    while (moEvents.size() < MAX_EVENT_SIZE) {
                        BusinessMoEvent businessMoEvent = MO_EVENT_QUEUE.poll();
                        if (businessMoEvent == null) {
                            break;
                        }
                        moEvents.add(businessMoEvent);
                        Long endTime = System.currentTimeMillis();
                        if (endTime - startTime > waitTime) {
                            break;
                        }
                    }
                    if (CollectionUtils.isNotEmpty(moEvents)) {
                        handleMoEvents(moEvents);
                    } else {
                        Thread.sleep(waitTime);
                    }
                } catch (Throwable e) {
                    LOGGER.error("mo event queue error", e);
                }
            }
        }
    }

    /**
     * 处理业务拓扑新增事件
     *
     * @param mo       网元
     * @param parentDn 父dn
     */
    public void addMoEvent(ManagedObject mo, String parentDn) {
        // 如果队列超过10000，就不再处理新增事件，打印日志记录
        if (MO_EVENT_QUEUE.size() > DEFAULT_QUEUE_CAPACITY) {
            LOGGER.error("add mo queue over size, mo dn = {}", mo.getDN().getValue());
            return;
        }
        // 判断是否在监听范围内
        List<BusinessCommonModel> models = businessCommonModelMapOper.get(RedisConstant.BUSINESS_TOPO_MO_COMMON_MODEL,
            BusinessCommonModel.class, mo.getType());
        BusinessCommonModel commonModel = models.get(0);
        if (commonModel == null) {
            LOGGER.info("[addMoEvent] there is no mo type mapping, dn = {}", mo.getDN().getValue());
            return;
        }
        // 如果是多解决方案共管环境，可能存在moType被共用，需要确认一下属于哪个解决方案
        List<BusinessCommonModel> siteCommonModels = businessCommonModelDao.queryCommonModelByType(BusinessTopoConstant.SITE_TYPE_ID);
        if (siteCommonModels.size() > ONE && CHECK_SOLUTION_MODEL_TYPE.contains(commonModel.getModelType())) {
            commonModel = getRealCommonModelWhenTogetherManage(mo, siteCommonModels, commonModel);
        }

        if (StringUtils.isNotEmpty(mo.getHwsSiteName()) && StringUtils.isNotEmpty(mo.getHwsGroupName()) && StringUtils.isNotEmpty(mo.getHwsStripe())) {
            String siteName = ConfigurationUtil.getConfigDataByName(BusinessTopoConstant.MM_SITE_NAME_CONFIG).getValue();
            String groupName = ConfigurationUtil.getConfigDataByName(BusinessTopoConstant.MM_GROUP_NAME_CONFIG).getValue();
            if (StringUtils.isEmpty(siteName) || StringUtils.isEmpty(groupName)) {
                LOGGER.info("[addMoEvent] there is no mo config for siteName and groupName, dn = {}", mo.getDN().getValue());
                return;
            }
            if (!siteName.equals(mo.getHwsSiteName()) || !groupName.equals(mo.getHwsGroupName())) {
                LOGGER.info("[addMoEvent] mm mo is not this site or group, dn = {}", mo.getDN().getValue());
                return;
            }
        }
        hanleMoEventByType(mo, parentDn, commonModel);
    }

    private static void hanleMoEventByType(ManagedObject mo, String parentDn, BusinessCommonModel commonModel) {
        switch (commonModel.getModelType()) {
            // 站点
            case BusinessTopoConstant.SITE_TYPE_ID:
                // 应用实例
            case BusinessTopoConstant.CLUSTER_INSTANCE_ID:
                // Vm实例
            case BusinessTopoConstant.VM_TYPE_ID:
                // 容器实例
            case BusinessTopoConstant.CONTAINER_TYPE_ID:
                BusinessMoEvent addMoSolution = new BusinessMoEvent(mo, null, commonModel, BusinessTopoConstant.MO_EVENT_ADD);
                MO_EVENT_QUEUE.add(addMoSolution);
                LOGGER.info("[addMoEvent] add mo to queue, dn = {}", mo.getDN().getValue());
                break;
            // Pod实例
            case BusinessTopoConstant.POD_TYPE_ID:
                // Docker实例
            case BusinessTopoConstant.DOCKER_TYPE_ID:
                BusinessMoEvent addMoPod = new BusinessMoEvent(mo, parentDn, commonModel, BusinessTopoConstant.MO_EVENT_ADD);
                MO_EVENT_QUEUE.add(addMoPod);
                LOGGER.info("[addMoEvent] add mo to queue, dn = {}", mo.getDN().getValue());
                break;
            case BusinessTopoConstant.MANAGEMENT_INSTANCE_ID:
                // mm的管理类实例
                BusinessMoEvent addMoManagement = new BusinessMoEvent(mo, null, commonModel, BusinessTopoConstant.MO_EVENT_ADD);
                MO_EVENT_QUEUE.add(addMoManagement);
                LOGGER.info("[addMoEvent] add management mo to queue, dn = {}", mo.getDN().getValue());
                break;
            default:
                LOGGER.error("[addMoEvent] cannot add modelType, modelType = {}", commonModel.getModelType());
        }
    }

    public BusinessCommonModel getRealCommonModelWhenTogetherManage(ManagedObject mo, List<BusinessCommonModel> siteCommonModels,
        BusinessCommonModel commonModel) {
        // 查出该网元类型所有的模型，数量大于1时需要确认
        List<BusinessCommonModel> moTypeCommonModels = businessCommonModelDao.queryCommonModelByMoType(mo.getType());
        if (moTypeCommonModels.size() > ONE) {
            String type;
            if (StringUtils.isNotEmpty(mo.getHwsSiteName()) && StringUtils.isNotEmpty(mo.getHwsGroupName())
                && StringUtils.isNotEmpty(mo.getHwsStripe())) {
                type = String.valueOf(BusinessTopoConstant.SOLUTION_MOBILE_MONEY);
            } else if (StringUtils.isNotEmpty(mo.getLogicSite())) {
                type = String.valueOf(BusinessTopoConstant.SOLUTION_BES);
            } else {
                // 通过DF接入的CBS或者BES平台网元，其solutionId会指向站点
                String solutionId = mo.getClientProperty(BusinessTopoConstant.SOLUTION_ID);
                if (StringUtils.isEmpty(solutionId)) {
                    LOGGER.error("[addMoEvent] cannot find solutionId, dn = {}", mo.getDN());
                    return commonModel;
                }
                BusinessInstanceModelDB siteModel = businessInstanceModelDao.queryInstanceByDn(solutionId);
                if (siteModel == null) {
                    LOGGER.error("[addMoEvent] cannot find site by solutionId, dn = {}, solutionId = {}", mo.getDN(), solutionId);
                    return commonModel;
                }
                type = siteModel.getStaticExtentAttr(BusinessTopoConstant.SITE_DATA_TYPE_ATTR_KEY).getStaticAttrValue();
                LOGGER.info("[addMoEvent] handle together manage, real common type is {}", type);
            }
            // key是解决方案类型，1cbs2bes3mm，value是模型对应的solutionName
            Map<String, String> typeSolutionMap = siteCommonModels.stream().collect(Collectors.toMap(
                common -> common.getStaticExtentAttr(BusinessTopoConstant.SITE_DATA_TYPE_ATTR_KEY).getStaticAttrValue(),
                BusinessCommonModel::getSolutionName, (existing, replacement) -> existing));
            if (typeSolutionMap.containsKey(type)) {
                String realSolutionName = typeSolutionMap.get(type);
                BusinessCommonModel realCommonModel = moTypeCommonModels.stream()
                    .filter(model -> realSolutionName.equals(model.getSolutionName()))
                    .findFirst()
                    .orElse(null);
                if (realCommonModel != null) {
                    commonModel = realCommonModel;
                }
            }
            LOGGER.info("[addMoEvent] handle together manage, real common model is {}", commonModel.getModelId());
        }
        return commonModel;
    }

    /**
     * 处理业务拓扑删除事件
     *
     * @param dn 被删除的网元dn
     * @param moType 网元类型
     */
    public void deleteMoEvent(String dn, String moType) {
        // 判断是否在监听范围内
        List<BusinessCommonModel> models = businessCommonModelMapOper.get(RedisConstant.BUSINESS_TOPO_MO_COMMON_MODEL,
            BusinessCommonModel.class, moType);
        BusinessCommonModel commonModel = models.get(0);
        if (commonModel == null) {
            LOGGER.info("[deleteMoEvent] there is no mo type mapping, dn = {}", dn);
            return;
        }
        ManagedObject mo = new Host();
        mo.setDN(new DN(dn));
        BusinessMoEvent deleteMoEvent = new BusinessMoEvent(mo, null, null, BusinessTopoConstant.MO_EVENT_DELETE);
        MO_EVENT_QUEUE.add(deleteMoEvent);
        LOGGER.info("[deleteMoEvent] delete mo to queue, dn = {}", dn);
    }

    /**
     * 处理业务拓扑变更事件
     *
     * @param mo 网元
     */
    public void changeMoEvent(ManagedObject mo) {
        // 判断是否在监听范围内
        List<BusinessCommonModel> models = businessCommonModelMapOper.get(RedisConstant.BUSINESS_TOPO_MO_COMMON_MODEL,
            BusinessCommonModel.class, mo.getType());
        BusinessCommonModel commonModel = models.get(0);
        if (commonModel == null) {
            LOGGER.info("[changeMoEvent] there is no mo type mapping, dn = {}", mo.getDN().getValue());
            return;
        }
        BusinessMoEvent changeMoEvent = new BusinessMoEvent(mo, null, commonModel, BusinessTopoConstant.MO_EVENT_CHANGE);
        MO_EVENT_QUEUE.add(changeMoEvent);
        LOGGER.info("[changeMoEvent] change mo to queue, dn = {}", mo.getDN().getValue());
    }

    /**
     * 处理业务拓扑关系新增事件
     *
     * @param moObj 关系变更事件
     */
    public void addRelationMoEvent(JSONObject moObj) {
        RelationEvent relationEvent = JSON.parseObject(JSON.toJSONString(moObj), RelationEvent.class);
        BusinessMoEvent addRelationMoEvent = new BusinessMoEvent(null, null, null, BusinessTopoConstant.MO_EVENT_ADD_RELATION, relationEvent);
        MO_EVENT_QUEUE.add(addRelationMoEvent);
    }

    public void handleMoEvents(List<BusinessMoEvent> moEvents) {
        if (CollectionUtils.isEmpty(moEvents)) {
            return;
        }
        String isImporting = REDIS_EX_OPER.get(BusinessTopoConstant.CONFIGURATION_IMPORT_TASK);
        if (StringUtils.isNotEmpty(isImporting)) {
            LOGGER.error("[handleMoEvents] importing now, don't proceed mo event");
            return;
        }
        // 按删除、新增、属性变更来分类处理
        Map<String, List<BusinessMoEvent>> eventMap = moEvents.stream()
            .collect(Collectors.groupingBy(BusinessMoEvent::getType));

        // 最先处理站点层的变更事件，因为要适配用户给站点添加了siteId，同时还新增了子网元的情况
        if (eventMap.containsKey(BusinessTopoConstant.MO_EVENT_CHANGE)) {
            handleSiteChangeEvents(eventMap);
        }
        if (eventMap.containsKey(BusinessTopoConstant.MO_EVENT_DELETE)) {
            List<BusinessMoEvent> deleteEvents = eventMap.get(BusinessTopoConstant.MO_EVENT_DELETE);
            handleDeleteEvents(deleteEvents);
        }
        if (eventMap.containsKey(BusinessTopoConstant.MO_EVENT_ADD)) {
            List<BusinessMoEvent> addEvents = eventMap.get(BusinessTopoConstant.MO_EVENT_ADD);
            handleAddEvents(addEvents);
        }
        if (eventMap.containsKey(BusinessTopoConstant.MO_EVENT_CHANGE)) {
            List<BusinessMoEvent> changeEvents = eventMap.get(BusinessTopoConstant.MO_EVENT_CHANGE);
            if (CollectionUtils.isNotEmpty(changeEvents)) {
                handleChangeEvents(changeEvents);
            }
        }
        if (CollectionUtils.isNotEmpty(eventMap.get(BusinessTopoConstant.MO_EVENT_ADD))
            || CollectionUtils.isNotEmpty(eventMap.get(BusinessTopoConstant.MO_EVENT_DELETE))) {
            businessTopoAlarmStorageService.refreshDnRange();
        }
        if (eventMap.containsKey(BusinessTopoConstant.MO_EVENT_ADD_RELATION)) {
            List<BusinessMoEvent> addRelationEvents = eventMap.get(BusinessTopoConstant.MO_EVENT_ADD_RELATION);
            handleAddRelationEvents(addRelationEvents);
        }
    }

    private void handleSiteChangeEvents(Map<String, List<BusinessMoEvent>> eventMap) {
        // 将站点新增logicSite场景过滤出来，使用站点新增逻辑
        List<BusinessMoEvent> changeList = eventMap.get(BusinessTopoConstant.MO_EVENT_CHANGE);
        List<BusinessMoEvent> siteChangeList = changeList.stream()
            .filter(event -> event.getCommonModel().getModelType() == BusinessTopoConstant.SITE_TYPE_ID)
            .collect(Collectors.toList());
        List<String> changeSiteDns = siteChangeList.stream()
            .map(event -> event.getMo().getDN().getValue())
            .collect(Collectors.toList());
        List<BusinessInstanceModelDB> existSites = businessInstanceModelDao.queryInstanceByDns(changeSiteDns);
        Map<String, BusinessInstanceModelDB> existSitesMap = existSites.stream().collect(Collectors.toMap(BusinessInstanceModelDB::getDn, db ->db));
        // 找出带站点的mo
        for (BusinessMoEvent event : siteChangeList) {
            String siteId = SiteIdUtil.getSiteIdByMo(event.getMo(), event.getCommonModel().getStaticExtentAttr(BusinessTopoConstant.SITE_ID_COLUMN_ATTR_KEY).getStaticAttrValue());
            String dn = event.getMo().getDN().getValue();
            if (StringUtils.isEmpty(siteId) && existSitesMap.containsKey(dn)) {
                // 站点没siteId，库里有实例，需要把站点从拓扑删除
                handleNoSiteIdButSite(dn, existSitesMap);
            } else if (StringUtils.isNotEmpty(siteId) && existSitesMap.containsKey(dn)) {
                // 站点有siteId，库里有实例，需要刷新站点的siteId
                LOGGER.info("[handleChangeMos] mo has siteId, update siteId, dn = {}, siteId = {}", dn, siteId);
                businessInstanceModelDao.updateAttrValue(existSitesMap.get(dn).getInstanceId(), BusinessTopoConstant.ATTR_SITE_ID, siteId);
            } else if (StringUtils.isNotEmpty(siteId) && !existSitesMap.containsKey(dn)) {
                // 站点有siteId，库里没实例，需要建立整个站点
                handleNewSiteIdAndNoSite(event, siteId, dn);
            } else {
                // 站点没siteId，库里没实例
                LOGGER.info("[handleChangeMos] mo has no siteId, no need to handle, dn = {}", dn);
            }
        }
        Set<String> changeSiteDnsSet = new HashSet<>(changeSiteDns);
        List<BusinessMoEvent> otherChangeList = changeList.stream()
            .filter(event -> !changeSiteDnsSet.contains(event.getMo().getDN().getValue()))
            .collect(Collectors.toList());
        eventMap.put(BusinessTopoConstant.MO_EVENT_CHANGE, otherChangeList);
    }

    private void handleNewSiteIdAndNoSite(BusinessMoEvent event, String siteId, String dn) {
        // 如果有其他站点已使用此siteId，则不建立
        Set<String> existSiteIds = businessInstanceModelDao.queryAllInstanceOfType(BusinessTopoConstant.SITE_TYPE_ID, event.getCommonModel().getSolutionName()).stream()
            .map(modelDb -> modelDb.getExtentAttr(BusinessTopoConstant.ATTR_SITE_ID).getAttrValue())
            .collect(Collectors.toSet());
        if (existSiteIds.contains(siteId)) {
            LOGGER.info("[handleChangeMos] siteId already used, dn = {}, siteId = {}", dn, siteId);
            return;
        }
        // 如果是MM站点的话，需要过滤当前局点和环境的
        ManagedObject mo = event.getMo();
        if (StringUtils.isNotEmpty(mo.getHwsSiteName()) && StringUtils.isNotEmpty(mo.getHwsGroupName()) && StringUtils.isNotEmpty(mo.getHwsStripe())) {
            String siteName = ConfigurationUtil.getConfigDataByName(BusinessTopoConstant.MM_SITE_NAME_CONFIG).getValue();
            String groupName = ConfigurationUtil.getConfigDataByName(BusinessTopoConstant.MM_GROUP_NAME_CONFIG).getValue();
            if (StringUtils.isEmpty(siteName) || StringUtils.isEmpty(groupName)) {
                LOGGER.info("[addMoEvent] there is no mo config for siteName and groupName, dn = {}", mo.getDN().getValue());
                return;
            }
            if (!siteName.equals(mo.getHwsSiteName()) || !groupName.equals(mo.getHwsGroupName())) {
                LOGGER.info("[addMoEvent] mm mo is not this site or group, dn = {}", mo.getDN().getValue());
                return;
            }
        }
        LOGGER.info("[handleChangeMos] mo has no siteId, add this site, dn = {}, siteId = {}", dn, siteId);
        handleAddEvents(Collections.singletonList(event));
        Vector<DN> insDns = event.getMo().children();
        List<ManagedObject> insMos = EamUtil.getMoByDns(insDns);
        addInstanceAndPodAndDocker(insMos);
    }

    private void handleNoSiteIdButSite(String dn, Map<String, BusinessInstanceModelDB> existSitesMap) {
        LOGGER.info("[handleChangeMos] mo has no siteId, delete this site, dn = {}", dn);
        List<BusinessInstanceModelDB> siteSubModelDbs = businessInstanceModelDao.queryAllInstanceBySiteWithoutAttr(
            existSitesMap.get(dn).getInstanceId());
        List<Integer> siteSubInsIds = siteSubModelDbs.stream()
            .map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList());
        try {
            businessInstanceModelDao.deleteInstance(siteSubInsIds);
        } catch (ServiceException e) {
            LOGGER.error("[handleChangeMos] delete instances failed, instanceIds = {}", siteSubInsIds);
        }
        // 从redis中删除
        for (Integer instanceId : siteSubInsIds) {
            businessTreeMapOper.remove(RedisConstant.BUSINESS_TOPO_RELATION_TREE, String.valueOf(instanceId));
        }
    }

    private void handleAddEvents(List<BusinessMoEvent> addEvents) {
        Long startTime = System.currentTimeMillis();
        LOGGER.info("start to handle add mo events");
        // 资源异常场景下会发送两遍新增事件，需要进行去重
        Map<String, BusinessMoEvent> uniqueEventMap = addEvents.stream().collect(
            Collectors.toMap(event -> event.getMo().getDN().getValue(), event -> event, (exist, replace) -> exist));
        List<BusinessMoEvent> uniqueEvents = new ArrayList<>(uniqueEventMap.values());

        // 如果实例表中已经有这些网元的dn，作为网元变更事件处理
        List<String> dns = uniqueEvents.stream()
            .map(event -> event.getMo().getDN().getValue()).collect(Collectors.toList());
        List<BusinessInstanceModelDB> models = businessInstanceModelDao.queryInstanceByDnsWithoutExtends(dns);
        Set<String> existDns = models.stream().map(BusinessInstanceModelDB::getDn).collect(Collectors.toSet());
        List<BusinessMoEvent> existMo = new ArrayList<>();
        List<BusinessMoEvent> addMo = new ArrayList<>();
        for (BusinessMoEvent event : uniqueEvents) {
            if (existDns.contains(event.getMo().getDN().getValue()) && !BusinessTopoConstant.VM_MO_TYPE.equals(event.getMo().getType())) {
                existMo.add(event);
            } else {
                addMo.add(event);
            }
        }
        handleChangeEvents(existMo);
        BusinessAddEvents businessAddEvents = getBusinessAddEvents(addMo);
        try {
            handleAddSolution(businessAddEvents.getSiteEvents());
            handleAddVm(businessAddEvents.getVmEvents(), VM);
            handleAddContainer(businessAddEvents.getContainerEvents(), CONTAINER);
            handleAddInstance(businessAddEvents.getInstanceEvents());
            handleAddPod(businessAddEvents.getPodEvents());
            handleAddDocker(businessAddEvents.getDockerEvents());
            handleAddManagement(businessAddEvents.getManagementEvents());
        } catch (ServiceException e) {
            LOGGER.error("handleAddMos error.", e);
        }
        List<String> siteDns = businessAddEvents.getInstanceEvents().stream()
            .map(event -> event.getMo().getDN().getValue()).collect(Collectors.toList());
        List<String> instanceDns = businessAddEvents.getInstanceEvents().stream()
            .map(event -> event.getMo().getDN().getValue()).collect(Collectors.toList());
        List<String> podDns = businessAddEvents.getPodEvents().stream()
            .map(event -> event.getMo().getDN().getValue()).collect(Collectors.toList());
        List<String> dockerDns = businessAddEvents.getDockerEvents().stream()
            .map(event -> event.getMo().getDN().getValue()).collect(Collectors.toList());
        List<String> managementDns = businessAddEvents.getDockerEvents().stream()
            .map(event -> event.getMo().getDN().getValue()).collect(Collectors.toList());
        Long endTime = System.currentTimeMillis();
        LOGGER.info("end to handle add mo event, cost time = {} ms, siteSize = {}, appSize = {}, podSize = {}, dockerSize = {}, "
                + "siteDns = {}, instanceDns = {}, podDns = {}, dockerDns = {}, managementDns = {}", endTime - startTime, siteDns.size(), instanceDns.size(),
            podDns.size(), dockerDns.size(), siteDns, instanceDns, podDns, dockerDns, managementDns);
    }

    private static BusinessAddEvents getBusinessAddEvents(List<BusinessMoEvent> addMo) {
        BusinessAddEvents businessAddEvents = new BusinessAddEvents();
        for (BusinessMoEvent businessMoEvent : addMo) {
            switch (businessMoEvent.getCommonModel().getModelType()) {
                case BusinessTopoConstant.SITE_TYPE_ID:
                    businessAddEvents.getSiteEvents().add(businessMoEvent);
                    break;
                case BusinessTopoConstant.VM_TYPE_ID:
                    businessAddEvents.getVmEvents().add(businessMoEvent);
                    break;
                case BusinessTopoConstant.CONTAINER_TYPE_ID:
                    businessAddEvents.getContainerEvents().add(businessMoEvent);
                    break;
                case BusinessTopoConstant.CLUSTER_INSTANCE_ID:
                    businessAddEvents.getInstanceEvents().add(businessMoEvent);
                    break;
                case BusinessTopoConstant.POD_TYPE_ID:
                    businessAddEvents.getPodEvents().add(businessMoEvent);
                    break;
                case BusinessTopoConstant.DOCKER_TYPE_ID:
                    businessAddEvents.getDockerEvents().add(businessMoEvent);
                    break;
                case BusinessTopoConstant.MANAGEMENT_INSTANCE_ID:
                    businessAddEvents.getManagementEvents().add(businessMoEvent);
                    break;
                default:
                    LOGGER.error("[handleAddMos] error modelType, event type = {}", businessMoEvent.getType());
            }
        }
        return businessAddEvents;
    }

    private void handleChangeEvents(List<BusinessMoEvent> addEvents) {
        Long startTime = System.currentTimeMillis();
        LOGGER.info("start to handle change mo events");
        Map<String, BusinessMoEvent> dnEventMap = addEvents.stream().collect(Collectors.toMap(event ->
                event.getMo().getDN().getValue(), event -> event, (existingValue, newValue) -> newValue));
        Set<String> dns = addEvents.stream()
            .map(event -> event.getMo().getDN().getValue()).collect(Collectors.toSet());
        List<BusinessInstanceModelDB> instances = businessInstanceModelDao.queryInstanceByDns(new ArrayList<>(dns));
        // mm可能会通过变更事件给数据库应用增加条带，此时要当成新增事件处理
        handleMmDbChangeEvents(addEvents, instances);
        List<BusinessInsExtentAttrDB> updateAttrs = new ArrayList<>();
        Map<String, BusinessCacheInstance> cacheMap = new HashMap<>();
        for (BusinessInstanceModelDB modelDB : instances) {
            // 更新拓展属性
            if (!dnEventMap.containsKey(modelDB.getDn())) {
                continue;
            }
            BusinessMoEvent moEvent = dnEventMap.get(modelDB.getDn());
            List<BusinessInsExtentAttrDB> attrs = getInstanceAttrList(moEvent.getMo(), moEvent.getCommonModel().getModelType(),
                modelDB.getInstanceId(), BusinessTopoConstant.EVENT_TYPE_CHANGE);
            updateAttrs.addAll(attrs);

            // 更新缓存中的拓展属性
            BusinessCacheInstance cacheInstance = businessTreeMapOper.get(RedisConstant.BUSINESS_TOPO_RELATION_TREE,
                BusinessCacheInstance.class, String.valueOf(modelDB.getInstanceId())).get(0);
            if (cacheInstance == null) {
                LOGGER.error("redis doesn't have this instance, instanceId = {}", modelDB.getInstanceId());
                continue;
            }
            List<BusinessInsExtentAttrDB> oldAttrList = cacheInstance.getAttrDBList();
            for (BusinessInsExtentAttrDB businessInsExtentAttrDB : oldAttrList) {
                if (BusinessTopoConstant.ATTR_KEY_ALARM_CSN.equals(businessInsExtentAttrDB.getAttrName())
                    || BusinessTopoConstant.ATTR_KEY_ALARM_COUNT.equals(businessInsExtentAttrDB.getAttrName())) {
                    attrs.add(businessInsExtentAttrDB);
                }
            }
            cacheInstance.setAttrDBList(attrs);
            cacheMap.put(String.valueOf(cacheInstance.getInstanceId()), cacheInstance);
        }
        try {
            businessInstanceModelDao.updateInstanceAttr(updateAttrs);
            businessTreeMapOper.putAll(RedisConstant.BUSINESS_TOPO_RELATION_TREE, cacheMap);
            buildDbAppInsRelations(instances, dnEventMap);
        } catch (ServiceException e) {
            LOGGER.error("handle change mos error", e);
        }
        Long endTime = System.currentTimeMillis();
        LOGGER.info("end to handle change mo event, cost time = {} ms, size = {}, dns = {}", endTime - startTime,
            instances.size(), dns);
    }

    /**
     * 如果是数据库应用示例，且存在站点，则需要构建关系
     *
     * @param instances 应用实例
     * @param dnEventMap dnEventMap
     * @throws ServiceException ServiceException
     */
    private void buildDbAppInsRelations(List<BusinessInstanceModelDB> instances, Map<String, BusinessMoEvent> dnEventMap)
        throws ServiceException {
        Map<String, BusinessCacheInstance> cacheMap = new HashMap<>();
        List<BusinessInstanceRelationModel> relationList = new ArrayList<>();
        // 批量查询部署关系 创建部署关系
        LOGGER.info("[buildDbAppInsRelations] start to deal data and vm.");
        Set<DN> databaseDns = instances.stream().map(BusinessInstanceModelDB::getDn).map(DN::new).collect(Collectors.toSet());
        Map<String, List<Integer>> databaseVmMap = getInstanceMulDeployMap(databaseDns, BusinessTopoConstant.CLUSTER_INSTANCE_ID);

        for (BusinessInstanceModelDB dbInstance : instances) {
            if (!dnEventMap.containsKey(dbInstance.getDn()) || !isDbAppType(dbInstance.getStaticAttrDBList())) {
                continue;
            }

            BusinessMoEvent moInstance = dnEventMap.get(dbInstance.getDn());
            BusinessSiteConfig businessSiteConfig = getBusinessSiteConfig(moInstance.getCommonModel());
            String siteKey = StringUtils.EMPTY;
            if (Objects.nonNull(businessSiteConfig)) {
                siteKey = businessSiteConfig.getSiteIdColumn();
            }

            String siteId = SiteIdUtil.getSiteIdByMo(moInstance.getMo(), siteKey);
            // mm场景下，需要把siteId里面的csu00替换成gsu，然后再把csuXX替换成rsuXX，使csu的gaussDb可以挂载到rsu和gsu上
            if (StringUtils.isNotEmpty(siteId)) {
                siteId = siteId.replace(CSU_00, GSU).replace(CSU, RSU);
            }
            if (StringUtils.isEmpty(siteId)) {
                // 如果没有站点
                businessInstanceRelationDao.deleteRelationByInstanceId(dbInstance.getInstanceId());
                LOGGER.info("siteId lack, delete relation, instanceId = {}", dbInstance.getInstanceId());
            }  else {
                String typeModelId = getAppTypeModelId(moInstance.getCommonModel());
                List<Integer> typeInstanceIds = businessInstanceModelDao.queryInstanceIdsByTypeModelId(typeModelId);
                Map<String, Integer> siteInstanceMap = businessInstanceModelDao.getSiteInstanceIdMap(typeInstanceIds, siteKey);
                LOGGER.info("siteId changed, delete old relation, instanceId = {}", dbInstance.getInstanceId());
                if (!siteInstanceMap.containsKey(siteId)) {
                    continue;
                }
                businessInstanceRelationDao.deleteRelationByInstanceId(dbInstance.getInstanceId());
                BusinessCacheInstance cache = JSON.parseObject(JSON.toJSONString(dbInstance), BusinessCacheInstance.class, JSONReader.Feature.SupportSmartMatch);
                cacheMap.put(String.valueOf(cache.getInstanceId()), cache);
                int parentInstanceId = siteInstanceMap.get(siteId);
                relationList.addAll(getRelationModels(dbInstance.getInstanceId(), parentInstanceId, cache));
                // 建立和vm的部署关系
                if (databaseVmMap.containsKey(dbInstance.getDn())) {
                    List<Integer> vmList = databaseVmMap.get(dbInstance.getDn());
                    vmList.forEach(vm -> {
                        addDeployRelation(relationList, dbInstance, vm);
                    });
                }
            }
        }
        // 分批次入库，每次200条数据
        ListUtils.partition(relationList, 200).forEach(relations -> businessInstanceRelationDao.replaceRelationInfo(relations));
        businessTreeMapOper.putAll(RedisConstant.BUSINESS_TOPO_RELATION_TREE, cacheMap);
    }

    public void handleAddRelationEvents(List<BusinessMoEvent> addRelationEvents) {
        List<RelationEvent> relationEvents = addRelationEvents.stream().map(BusinessMoEvent::getRelationEvent).collect(Collectors.toList());
        List<String> srcDns = relationEvents.stream().map(RelationEvent::getSrcNode).collect(Collectors.toList());
        List<String> destDns = relationEvents.stream().map(RelationEvent::getDestNode).collect(Collectors.toList());
        List<BusinessInstanceModelDB> srcModels = businessInstanceModelDao.queryInstanceByDns(srcDns);
        List<BusinessInstanceModelDB> destModels = businessInstanceModelDao.queryInstanceByDns(destDns);
        Map<String, BusinessInstanceModelDB> srcMaps = srcModels.stream()
            .filter(modelDb -> modelDb.getModelType() == BusinessTopoConstant.POD_TYPE_ID || modelDb.getModelType() == BusinessTopoConstant.DOCKER_TYPE_ID)
            .collect(Collectors.toMap(BusinessInstanceModelDB::getDn, model -> model, (v1, v2) -> v2));
        Map<String, BusinessInstanceModelDB> destMaps = destModels.stream().collect(Collectors.toMap(
            BusinessInstanceModelDB::getDn, model -> model, (v1, v2) -> v2));
        List<BusinessInstanceRelationModel> relations = new ArrayList<>(relationEvents.size());
        for (RelationEvent event : relationEvents) {
            if (srcMaps.containsKey(event.getSrcNode()) && destMaps.containsKey(event.getDestNode()) && event.getType().equals("Deployment")) {
                BusinessInstanceModelDB srcInstance = srcMaps.get(event.getSrcNode());
                BusinessInstanceModelDB destInstance = destMaps.get(event.getDestNode());
                if ((srcInstance.getModelType().equals(BusinessTopoConstant.POD_TYPE_ID) && destInstance.getModelType().equals(BusinessTopoConstant.VM_TYPE_ID))
                    || (srcInstance.getModelType().equals(BusinessTopoConstant.DOCKER_TYPE_ID) && destInstance.getModelType().equals(BusinessTopoConstant.CONTAINER_TYPE_ID))) {
                    BusinessInstanceRelationModel model = new BusinessInstanceRelationModel();
                    model.setInstanceId(srcMaps.get(event.getSrcNode()).getInstanceId());
                    model.setTargetInstanceId(destMaps.get(event.getDestNode()).getInstanceId());
                    model.setRelationType(BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT);
                    relations.add(model);
                }
            }
        }
        List<Integer> instanceIds = relations.stream().map(BusinessInstanceRelationModel::getInstanceId).collect(Collectors.toList());
        businessInstanceRelationDao.deleteRelationByInstanceIdAndType(instanceIds, BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT);
        businessInstanceRelationDao.replaceRelationInfo(relations);
    }

    public void handleDeleteEvents(List<BusinessMoEvent> deleteEvents) {
        Long startTime = System.currentTimeMillis();
        LOGGER.info("start to handle delete mo events");
        Set<String> dns = deleteEvents.stream()
            .map(event -> event.getMo().getDN().getValue()).collect(Collectors.toSet());
        List<BusinessInstanceModelDB> instances = businessInstanceModelDao.queryInstanceByDns(new ArrayList<>(dns));
        List<Integer> deleteIds = instances.stream()
            .filter(Objects::nonNull)
            .map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList());
        handleClearAlarm(instances);
        Set<String> needDeleteIndicatorDns = new HashSet<>(dns);
        Set<String> mmSiteDns = instances.stream()
            .filter(ins -> ins.getModelType() == BusinessTopoConstant.SITE_TYPE_ID
            && MM_TYPE.equals(ins.getStaticExtentAttr(BusinessTopoConstant.SITE_DATA_TYPE_ATTR_KEY).getStaticAttrValue()))
            .map(BusinessInstanceModelDB::getDn).collect(Collectors.toSet());
        needDeleteIndicatorDns.removeAll(mmSiteDns);
        // 站点层实例删除时，还要删除第4层和第5层实例，以及关联的第六层DV实例，以及关联的数据库应用实例
        List<Integer> siteInstanceIds = instances.stream()
            .filter(instance -> instance.getModelType().equals(BusinessTopoConstant.RELATION_TYPE_SITE))
            .map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(siteInstanceIds)) {
            List<BusinessInstanceModelDB> appGroupInstances = businessInstanceModelDao.queryNextInstanceWithoutAttrByInstanceIds(siteInstanceIds);
            List<Integer> appGroups = appGroupInstances.stream()
                .map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList());
            List<BusinessInstanceModelDB> appTypeInstances = businessInstanceModelDao.queryNextInstanceWithoutAttrByInstanceIds(appGroups);
            List<Integer> appTypes = appTypeInstances.stream()
                .map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList());
            List<BusinessInstanceModelDB> dvInstances = businessInstanceModelDao.queryDvInstanceBySiteInstanceIds(siteInstanceIds);
            List<Integer> dvApps = dvInstances.stream()
                .map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList());
            List<BusinessInstanceModelDB> dbInstances = businessInstanceModelDao.queryDbInstanceBySiteInstanceIds(siteInstanceIds);
            List<Integer> dbApps = dbInstances.stream()
                .map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList());
            deleteIds.addAll(appGroups);
            deleteIds.addAll(appTypes);
            deleteIds.addAll(dvApps);
            deleteIds.addAll(dbApps);
        }
        try {
            businessInstanceModelDao.deleteInstance(deleteIds);
            businessIndicatorDao.deleteIndicatorByDns(new ArrayList<>(needDeleteIndicatorDns));
            // 从redis中删除
            for (Integer instanceId : deleteIds) {
                businessTreeMapOper.remove(RedisConstant.BUSINESS_TOPO_RELATION_TREE, String.valueOf(instanceId));
            }
        } catch (ServiceException e) {
            LOGGER.error("handle delete mos error", e);
        }
        Long endTime = System.currentTimeMillis();
        LOGGER.info("end to handle delete mo event, cost time = {} ms, size = {}, dns = {}", endTime - startTime,
            instances.size(), dns);
    }

    public void handleClearAlarm(List<BusinessInstanceModelDB> instances) {
        // 查出所有父关系，构建子id，父id的map
        List<Integer> deleteIds = instances.stream().map(BusinessInstanceModelDB::getInstanceId).collect(Collectors.toList());
        List<BusinessInstanceRelationModel> relationModels = businessInstanceRelationDao.
            getInsRelByInsIdAndRelationType(deleteIds, BusinessTopoConstant.RELATION_TYPE_PARENT, BusinessTopoConstant.COLUMN_INSTANCE_ID);
        Map<Integer, Integer> parentMap = relationModels.stream().collect(Collectors.toMap(
            BusinessInstanceRelationModel::getInstanceId, BusinessInstanceRelationModel::getTargetInstanceId));
        List<AlarmDetail> allAlarmList = new ArrayList<>();
        for (BusinessInstanceModelDB modelDB : instances) {
            List<AlarmDetail> clearAlarmList = new ArrayList<>();
            // 处理自身告警
            List<BusinessInsExtentAttrDB> attrDBList = modelDB.getAttrDBList();
            if (CollectionUtils.isEmpty(attrDBList)) {
                continue;
            }
            handleAlarmList(clearAlarmList, modelDB, ALARM_CSN);
            handleAlarmList(clearAlarmList, modelDB, EVENT_CSN);
            // 处理父实例告警，过滤掉站点实例，如果自己的父实例已经在删除列表中，也过滤掉
            if (modelDB.getModelType().equals(BusinessTopoConstant.RELATION_TYPE_SITE)) {
                continue;
            }
            if (parentMap.containsKey(modelDB.getInstanceId())
                && !deleteIds.contains(parentMap.get(modelDB.getInstanceId()))) {
                Integer fatherInsId = parentMap.get(modelDB.getInstanceId());
                List<AlarmDetail> fatherAlarmList = JSON.parseArray(JSON.toJSONString(clearAlarmList), AlarmDetail.class);
                fatherAlarmList.forEach(alarmDetail -> {
                    alarmDetail.setInstanceId(fatherInsId);
                    alarmDetail.setNativeMeDn(null);
                });
                clearAlarmList.addAll(fatherAlarmList);
            }
            allAlarmList.addAll(clearAlarmList);
        }
        Set<Integer> clearCsns = allAlarmList.stream().map(AlarmDetail::getCsn).collect(Collectors.toSet());
        LOGGER.debug("[handleClearAlarm] delete instanceIds = {}, clear csns = {}", deleteIds, clearCsns);
        businessTopoAlarmStorageService.receiveAlarmList(allAlarmList);
    }

    private void handleAlarmList(List<AlarmDetail> clearAlarmList, BusinessInstanceModelDB modelDB, String alarmCsnAttrName) {
        String alarmCsnAttrs = modelDB.getExtentAttr(alarmCsnAttrName).getAttrValue();
        Set<String> alarmCsnList = new HashSet<>();
        if (StringUtils.isNotEmpty(alarmCsnAttrs)) {
            alarmCsnList = new HashSet<>(Arrays.asList(alarmCsnAttrs.split(",")));
        }
        for (String alarmCsn : alarmCsnList) {
            AlarmDetail alarmDetail = new AlarmDetail();
            alarmDetail.setCsn(Integer.parseInt(alarmCsn));
            alarmDetail.setNativeMeDn(modelDB.getDn());
            alarmDetail.setInstanceId(modelDB.getInstanceId());
            alarmDetail.setIsVanished(true);
            alarmDetail.setCategory(2);
            clearAlarmList.add(alarmDetail);
        }
    }

    public void handleAddSolution(List<BusinessMoEvent> instanceEvent) {
        if (CollectionUtils.isEmpty(instanceEvent)) {
            return;
        }
        try {
            // 新增站点单独构建子树
            Map<String, List<BusinessMoEvent>> moTypeMap = instanceEvent.stream()
                .collect(Collectors.groupingBy(event -> event.getMo().getType()));
            for (Map.Entry<String, List<BusinessMoEvent>> moTypeEvents : moTypeMap.entrySet()) {
                // 如果是mm解决方案，走新增条带逻辑
                if (moTypeEvents.getKey().equals(BusinessTopoConstant.MM_TYPE)) {
                    handleAddSolutionMm(moTypeEvents.getValue());
                    continue;
                }
                String solutionName = moTypeEvents.getValue().get(0).getCommonModel().getSolutionName();
                List<ManagedObject> mos = instanceEvent.stream().map(BusinessMoEvent::getMo).collect(Collectors.toList());
                ConfigurationImportEntity importEntity = getConfigurationImportEntity(solutionName);
                addingSiteModelHandler.addNewSiteInstance(importEntity, mos);
                topoInstanceRelationCreator.buildSiteRelations(importEntity);
                businessTopoDvSelfService.refreshDvHealthAndDr();
                topoInstanceRelationCreator.buildTopRelations(importEntity);
                handleBusinessIndicator(mos);
                handleExistDbInstance(importEntity);
                handleExistPlatformService(importEntity, mos);
            }
        } catch (Exception e) {
            LOGGER.error("handle add site error.", e);
        }
    }

    private void handleBusinessIndicator(List<ManagedObject> mos) {
        INDICATOR_EXECUTOR.submit(() -> {
            try {
                // 插入关联指标
                handleBusinessIndicators(mos);
            } catch (Exception e) {
                LOGGER.info("[handleAddSolution] handle indicators error.", e);
            }
        });
    }

    private void handleAddInstance(List<BusinessMoEvent> instanceEvent) throws ServiceException {
        // key为moType，value为addMoEvent的map
        Map<String, List<BusinessMoEvent>> moTypeMap = instanceEvent.stream()
            .collect(Collectors.groupingBy(event -> event.getMo().getType()));
        List<BusinessInstanceRelationModel> relationList = new ArrayList<>(instanceEvent.size() * 3);
        Map<String, BusinessCacheInstance> cacheMap = new HashMap<>(instanceEvent.size());

        // 区分moType处理应用实例的新增
        for (Map.Entry<String, List<BusinessMoEvent>> entry : moTypeMap.entrySet()) {
            BusinessCommonModel commonModel = entry.getValue().get(0).getCommonModel();
            String typeModelId = getAppTypeModelId(commonModel);
            BusinessSiteConfig businessSiteConfig = getBusinessSiteConfig(commonModel);
            if (businessSiteConfig == null) {
                LOGGER.error("[addMoEvent] get siteIdColumn by config error, modelId = {}", typeModelId);
                continue;
            }
            String siteKey = businessSiteConfig.getSiteIdColumn();
            if (StringUtils.isEmpty(siteKey)) {
                siteKey = BusinessTopoConstant.ATTR_SITE_ID;
            }
            List<Integer> typeInstanceIds = businessInstanceModelDao.queryInstanceIdsByTypeModelId(typeModelId);
            if (CollectionUtils.isEmpty(typeInstanceIds)) {
                LOGGER.error("[addMoEvent] there is no instance of this type, modelId = {}", typeModelId);
                continue;
            }
            Map<String, Integer> siteInstanceMap = businessInstanceModelDao.getSiteInstanceIdMap(typeInstanceIds, siteKey);
            Map<String, Integer> dnInstanceMap = new HashMap<>(entry.getValue().size());
            for (BusinessMoEvent businessMoEvent : entry.getValue()) {
                ManagedObject mo = businessMoEvent.getMo();
                String moSiteId = SiteIdUtil.getSiteIdByMo(mo, siteKey);
                BusinessInstanceModelDB createInstance = getBusinessInstanceModelDB(commonModel, siteInstanceMap, mo, moSiteId);
                if (Objects.isNull(createInstance)) {
                    continue;
                }
                dnInstanceMap.put(createInstance.getDn(), createInstance.getInstanceId());
                // 建立父关系
                BusinessCacheInstance cache = JSON.parseObject(JSON.toJSONString(createInstance), BusinessCacheInstance.class, JSONReader.Feature.SupportSmartMatch);
                cacheMap.put(String.valueOf(cache.getInstanceId()), cache);
                if (StringUtils.isNotEmpty(moSiteId)) {
                    // 数据库应用示例可能没有站点，只需要实例化，有站点的实例才需要建立关系
                    int parentInstanceId = siteInstanceMap.get(moSiteId);
                    relationList.addAll(getRelationModels(createInstance.getInstanceId(), parentInstanceId, cache));
                }
            }
            // 异步处理指标关联
            handleInstanceIndicator(entry.getValue(), commonModel, dnInstanceMap);
            // 新增数据库应用时，关联vm
            handleDbRelationWhenAdd(commonModel, dnInstanceMap, relationList);
        }
        businessInstanceRelationDao.insertRelationInfo(relationList);
        businessTreeMapOper.putAll(RedisConstant.BUSINESS_TOPO_RELATION_TREE, cacheMap);
    }

    private BusinessInstanceModelDB getBusinessInstanceModelDB(BusinessCommonModel commonModel,
        Map<String, Integer> siteInstanceMap, ManagedObject mo, String moSiteId) throws ServiceException {
        // 是数据库应用而且找不到站点的情况下，先入库
        if (isDbAppType(commonModel.getExtentAttrs()) && StringUtils.isEmpty(moSiteId)) {
            BusinessInstanceModelDB model = creatBusinessInstanceModelDB(mo, commonModel);
            if (model == null) {
                LOGGER.error("add db instance error, dn = {}", mo.getDN().getValue());
                return null;
            }
            Map<String, Integer> dnInstanceMap = new HashMap<>();
            dnInstanceMap.put(model.getDn(), model.getInstanceId());
            createAppIndicatorForMm(commonModel.getModelId(), dnInstanceMap, mo.getType());
            BusinessCacheInstance cache = JSON.parseObject(JSON.toJSONString(model), BusinessCacheInstance.class, JSONReader.Feature.SupportSmartMatch);
            Map<String, BusinessCacheInstance> cacheMap = new HashMap<>();
            cacheMap.put(String.valueOf(cache.getInstanceId()), cache);
            businessTreeMapOper.putAll(RedisConstant.BUSINESS_TOPO_RELATION_TREE, cacheMap);
            return null;
        }
        if (StringUtils.isEmpty(moSiteId)) {
            LOGGER.error("there is no client properties of site, dn = {}", mo.getDN().getValue());
            return null;
        }
        if (!siteInstanceMap.containsKey(moSiteId)) {
            LOGGER.error("there is no site that contains this mo, dn = {}", mo.getDN().getValue());
            return null;
        }
        // 插入变更网元实例和拓展属性
        return creatBusinessInstanceModelDB(mo, commonModel);
    }

    public static boolean isDbAppType(List<BusinessCommonModelExtentAttr> extentAttrs) {
        String applicationType = extentAttrs.stream()
            .collect(Collectors.toMap(BusinessCommonModelExtentAttr::getStaticAttrName,
                BusinessCommonModelExtentAttr::getStaticAttrValue))
            .getOrDefault(BusinessTopoConstant.APPLICATION_TYPE, null);

        return Objects.equals(applicationType, String.valueOf(BusinessTopoConstant.DATABASE_APPLICATION_TYPE));
    }

    private String getAppTypeModelId(BusinessCommonModel commonModel) {
        String typeModelName = commonModel.getStaticExtentAttr(BusinessTopoConstant.PARENT_MO_TYPE_NAME_ATTR_KEY).getStaticAttrValue();
        if (StringUtils.isEmpty(typeModelName)) {
            typeModelName = commonModel.getStaticExtentAttr(BusinessTopoConstant.PARENT_MODEL_NAME_ATTR_KEY).getStaticAttrValue();
        }
        return createModelId(BusinessConfigEnum.BUSINESS_APP_TYPE_CONFIG_ENUM.getModeType(), typeModelName, commonModel.getSolutionName());
    }

    private void handleInstanceIndicator(List<BusinessMoEvent> businessMoEvents, BusinessCommonModel commonModel,
        Map<String, Integer> dnInstanceMap) {
        INDICATOR_EXECUTOR.submit(() -> {
            try {
                // 插入关联指标
                List<ManagedObject> mos = businessMoEvents.stream().map(BusinessMoEvent::getMo).collect(Collectors.toList());
                handleBusinessIndicators(mos);
                createAppIndicator(commonModel.getModelId(), dnInstanceMap, businessMoEvents.get(0).getMo().getType(), true);
            } catch (Exception e) {
                LOGGER.info("[handleAddInstance] handle indicators error.", e);
            }
        });
    }

    private BusinessSiteConfig getBusinessSiteConfig(BusinessCommonModel commonModel) {
        // 先查站点字段，根据应用类型的modelId，查出所有应用类型实例id，进而查出key为站点，value为应用类型实例Id的map
        String configJson = commonModel.getStaticExtentAttr(BusinessTopoConstant.BUSINESS_SITE_CONFIG_ATTR_KEY).getStaticAttrValue();
        BusinessSiteConfig businessSiteConfig;
        try {
            businessSiteConfig = JSON.parseObject(configJson, BusinessSiteConfig.class, JSONReader.Feature.SupportSmartMatch);
        } catch (JSONException e) {
            LOGGER.error("get site config parse json error", e);
            return null;
        }
        return businessSiteConfig;
    }

    private void handleAddPod(List<BusinessMoEvent> podEvent) throws ServiceException {
        if (CollectionUtils.isEmpty(podEvent)) {
            return;
        }
        // key为moType，value为addMoEvent的map
        Map<String, List<BusinessMoEvent>> moTypeMap = podEvent.stream()
            .collect(Collectors.groupingBy(event -> event.getMo().getType()));

        List<BusinessInstanceRelationModel> relationList = new ArrayList<>(podEvent.size() * 3);
        Map<String, BusinessCacheInstance> cacheMap = new HashMap<>(podEvent.size());

        // 找到这些pod的父实例，建立key为fatherDn，value为father instanceId的map
        Map<String, Integer> fatherInsMap = getFatherDnInstanceIdMap(podEvent);

        // 找到这些pod的部署关系，建立key为pod dn，value为vm instanceId的map
        Set<DN> podDns = podEvent.stream().map(event -> event.getMo().getDN()).collect(Collectors.toSet());
        Map<String, Integer> podVmMap = getInstanceDeployMap(podDns, BusinessTopoConstant.POD_TYPE_ID);

        for (Map.Entry<String, List<BusinessMoEvent>> entry : moTypeMap.entrySet()) {
            BusinessCommonModel commonModel = entry.getValue().get(0).getCommonModel();
            Map<String, Integer> dnInstanceMap = new HashMap<>(entry.getValue().size());
            for (BusinessMoEvent businessMoEvent : entry.getValue()) {
                ManagedObject mo = businessMoEvent.getMo();
                if (!fatherInsMap.containsKey(businessMoEvent.getParent())) {
                    LOGGER.error("[addMoEvent] pod has no father, pod dn = {}", mo.getDN().getValue());
                    continue;
                }
                // 插入变更网元实例和拓展属性
                BusinessInstanceModelDB createInstance = creatBusinessInstanceModelDB(mo, commonModel);
                if (createInstance == null) {
                    LOGGER.error("[addMoEvent] insert pod error, dn = {}", mo.getDN().getValue());
                    continue;
                }
                dnInstanceMap.put(createInstance.getDn(), createInstance.getInstanceId());
                // 建立父关系和部署关系
                buildFatherAndDeployRelation(businessMoEvent, createInstance, cacheMap, fatherInsMap, relationList, podVmMap, mo);
            }
            // 插入关联指标
            List<ManagedObject> mos = entry.getValue().stream().map(BusinessMoEvent::getMo).collect(Collectors.toList());
            handleBusinessIndicators(mos);
            createAppIndicator(commonModel.getModelId(), dnInstanceMap, entry.getValue().get(0).getMo().getType(), false);
        }
        businessInstanceRelationDao.insertRelationInfo(relationList);
        businessTreeMapOper.putAll(RedisConstant.BUSINESS_TOPO_RELATION_TREE, cacheMap);
    }

    private void buildFatherAndDeployRelation(BusinessMoEvent businessMoEvent, BusinessInstanceModelDB createInstance,
        Map<String, BusinessCacheInstance> cacheMap, Map<String, Integer> fatherInsMap,
        List<BusinessInstanceRelationModel> relationList, Map<String, Integer> podVmMap, ManagedObject mo)
        throws ServiceException {
        // 找到父实例则建立父关系，找不到就成为孤儿pod
        BusinessCacheInstance cache = JSON.parseObject(JSON.toJSONString(createInstance), BusinessCacheInstance.class, JSONReader.Feature.SupportSmartMatch);
        cacheMap.put(String.valueOf(cache.getInstanceId()), cache);
        if (fatherInsMap.containsKey(businessMoEvent.getParent())) {
            relationList.addAll(getRelationModels(createInstance.getInstanceId(), fatherInsMap.get(businessMoEvent.getParent()), cache));
        }
        // 建立和vm的部署关系
        if (podVmMap.containsKey(mo.getDN().getValue())) {
            addDeployRelation(relationList, createInstance, podVmMap.get(mo.getDN().getValue()), cache);
        }
    }

    private Map<String, Integer> getInstanceDeployMap(Set<DN> dnList, Integer type) {
        // 找到这些pod所在的vm，建立key为vm dn，value为vm instanceId的map
        List<MORelation> relations = new ArrayList<>();
        try {
            relations = MITManagerClient.newInstance().getAllRelationsByDns(new ArrayList<>(dnList), RelationType.Deployment.name(), false);
        } catch (OSSException e) {
            LOGGER.error("get pod deploy vm error.", e);
        }
        Set<String> vmDns = filterDeployMoType(relations, type);
        List<BusinessInstanceModelDB> vmInstances = businessInstanceModelDao.queryInstanceByDnsWithoutAttr(new ArrayList<>(vmDns));
        Map<String, Integer> deployVmMap = vmInstances.stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(BusinessInstanceModelDB::getDn, BusinessInstanceModelDB::getInstanceId, (existingValue, newValue) -> newValue));

        // key为pod的dn，value为vm的instanceId
        Map<String, Integer> podVmMap = new HashMap<>();
        for (MORelation relation : relations) {
            String vmDn = relation.getDestNode().getValue();
            if (deployVmMap.containsKey(vmDn)) {
                podVmMap.put(relation.getSrcNode().getValue(), deployVmMap.get(vmDn));
            }
        }
        return podVmMap;
    }

    private Map<String, List<Integer>> getInstanceMulDeployMap(Set<DN> dnList, Integer type) {
        // 找到这些pod/数据库所在的vm，建立key为vm dn，value为vm instanceId的map
        List<MORelation> relations = new ArrayList<>();
        try {
            relations = MITManagerClient.newInstance().getAllRelationsByDns(new ArrayList<>(dnList), RelationType.Deployment.name(), false);
        } catch (OSSException e) {
            LOGGER.error("get dn deploy vm error.", e);
        }
        Set<String> vmDns = filterDeployMoType(relations, type);
        List<BusinessInstanceModelDB> vmInstances = businessInstanceModelDao.queryInstanceByDnsWithoutAttr(new ArrayList<>(vmDns));
        Map<String, Integer> deployVmMap = vmInstances.stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(BusinessInstanceModelDB::getDn, BusinessInstanceModelDB::getInstanceId, (existingValue, newValue) -> newValue));

        // key为pod的dn，value为vm的instanceId
        Map<String, List<Integer>> podVmMap = new HashMap<>();
        relations.forEach(relation -> {
            if (deployVmMap.containsKey(relation.getDestNode().getValue())) {
                podVmMap.computeIfAbsent(relation.getSrcNode().getValue(), k -> new ArrayList<>())
                    .add(deployVmMap.get(relation.getDestNode().getValue()));
            }
        });
        return podVmMap;
    }

    private Set<String> filterDeployMoType(List<MORelation> relations, Integer type) {
        if (CollectionUtils.isEmpty(relations)) {
            return Collections.emptySet();
        }
        if (type != BusinessTopoConstant.POD_TYPE_ID && type != BusinessTopoConstant.CLUSTER_INSTANCE_ID) {
            return relations.stream().map(relation -> relation.getDestNode().getValue()).collect(Collectors.toSet());
        }
        List<DN> vmDns = relations.stream().map(MORelation::getDestNode).collect(Collectors.toList());
        List<ManagedObject> mos = EamUtil.getMoByDns(vmDns);
        return mos.stream()
            .filter(
                mo -> BusinessTopoConstant.VM_MO_TYPE.equals(mo.getType()) || BusinessTopoConstant.PH_MO_TYPE.equals(
                    mo.getType()))
            .map(mo -> mo.getDN().getValue())
            .collect(Collectors.toSet());
    }

    public void handleAddVm(List<BusinessMoEvent> instanceEvent, String type) throws ServiceException {
        if (CollectionUtils.isEmpty(instanceEvent)) {
            return;
        }
        List<BusinessCommonModel> commonModels = businessCommonModelDao.queryCommonModelByType(BusinessTopoConstant.VM_TYPE_ID);
        if (CollectionUtils.isEmpty(commonModels)) {
            LOGGER.error("[handleAddVm] there is no vm common model");
            return;
        }
        for (BusinessCommonModel commonModel : commonModels) {
            // 如果该解决方案已存在该vm的dn，就不新增
            List<String> dns = instanceEvent.stream().map(event -> event.getMo().getDN().getValue()).collect(Collectors.toList());
            List<BusinessInstanceModelDB> existModelDbs = businessInstanceModelDao.queryInstanceByDnsWithoutExtends(dns);
            Set<String> existDns = existModelDbs.stream()
                .filter(model -> commonModel.getModelId().equals(model.getModelId()))
                .map(BusinessInstanceModelDB::getDn)
                .collect(Collectors.toSet());
            List<BusinessMoEvent> filterInstanceEvent = instanceEvent.stream()
                .filter(moEvent -> !existDns.contains(moEvent.getMo().getDN().getValue()))
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterInstanceEvent)) {
                continue;
            }
            Map<String, BusinessCacheInstance> cacheMap = new HashMap<>(filterInstanceEvent.size());
            Map<String, Integer> dnInstanceMap = new HashMap<>(filterInstanceEvent.size());
            for (BusinessMoEvent moEvent : filterInstanceEvent) {
                BusinessInstanceModelDB createInstance = creatBusinessInstanceModelDB(moEvent.getMo(), commonModel);
                if (createInstance == null) {
                    LOGGER.error("[handleAddVm] insert vm error, dn = {}", moEvent.getMo().getDN().getValue());
                    continue;
                }
                dnInstanceMap.put(createInstance.getDn(), createInstance.getInstanceId());
                BusinessCacheInstance cache = JSON.parseObject(JSON.toJSONString(createInstance), BusinessCacheInstance.class, JSONReader.Feature.SupportSmartMatch);
                cacheMap.put(String.valueOf(cache.getInstanceId()), cache);
            }
            // 异步处理指标关联
            INDICATOR_EXECUTOR.submit(() -> {
                try {
                    // 插入关联指标
                    List<ManagedObject> mos = filterInstanceEvent.stream().map(BusinessMoEvent::getMo).collect(Collectors.toList());
                    handleBusinessIndicators(mos);
                    BusinessMoEvent moEvent = filterInstanceEvent.get(0);
                    createAppIndicator(commonModel.getModelId(), dnInstanceMap, moEvent.getMo().getType(), false);
                } catch (Exception e) {
                    LOGGER.info("[handleAddInstance] handle indicators error.", e);
                }
            });
            businessTreeMapOper.putAll(RedisConstant.BUSINESS_TOPO_RELATION_TREE, cacheMap);
            // 处理部署关系
            handleDeployRelation(dnInstanceMap, type, commonModel.getSolutionName());
        }
    }


    public void handleAddContainer(List<BusinessMoEvent> instanceEvent, String type) throws ServiceException {
        if (CollectionUtils.isEmpty(instanceEvent)) {
            return;
        }
        Map<String, BusinessCacheInstance> cacheMap = new HashMap<>(instanceEvent.size());
        Map<String, Integer> dnInstanceMap = new HashMap<>(instanceEvent.size());
        for (BusinessMoEvent moEvent : instanceEvent) {
            BusinessInstanceModelDB createInstance = creatBusinessInstanceModelDB(moEvent.getMo(), moEvent.getCommonModel());
            if (createInstance == null) {
                LOGGER.error("[addMoEvent] insert vm error, dn = {}", moEvent.getMo().getDN().getValue());
                continue;
            }
            dnInstanceMap.put(createInstance.getDn(), createInstance.getInstanceId());
            BusinessCacheInstance cache = JSON.parseObject(JSON.toJSONString(createInstance), BusinessCacheInstance.class, JSONReader.Feature.SupportSmartMatch);
            cacheMap.put(String.valueOf(cache.getInstanceId()), cache);
        }
        // 异步处理指标关联
        INDICATOR_EXECUTOR.submit(() -> {
            try {
                // 插入关联指标
                List<ManagedObject> mos = instanceEvent.stream().map(BusinessMoEvent::getMo).collect(Collectors.toList());
                handleBusinessIndicators(mos);
                BusinessMoEvent moEvent = instanceEvent.get(0);
                createAppIndicator(moEvent.getCommonModel().getModelId(), dnInstanceMap, moEvent.getMo().getType(), false);
            } catch (Exception e) {
                LOGGER.info("[handleAddInstance] handle indicators error.", e);
            }
        });
        businessTreeMapOper.putAll(RedisConstant.BUSINESS_TOPO_RELATION_TREE, cacheMap);
        // 处理部署关系
        handleDeployRelation(dnInstanceMap, type, null);
    }

    private void handleDeployRelation(Map<String, Integer> dnInstanceMap, String type, String solutionName) {
        Map<String, List<String>> deployMap = EamUtil.getDeployMapByDns(new ArrayList<>(dnInstanceMap.keySet()));
        // 查找拓扑侧没有部署关系，且部署在该vm上的实例，建立关系
        List<BusinessInstanceModelDB> insModels = businessInstanceModelDao.queryUnDeployInstanceByDns(new ArrayList<>(deployMap.keySet()), solutionName);
        if (VM.equals(type)) {
            insModels = insModels.stream().filter(model -> model.getModelType() == BusinessTopoConstant.POD_TYPE_ID).collect(Collectors.toList());
        } else if (CONTAINER.equals(type))  {
            insModels = insModels.stream().filter(model -> model.getModelType() == BusinessTopoConstant.DOCKER_TYPE_ID).collect(Collectors.toList());
        } else if (DATA_BASE.equals(type)) {
            insModels = insModels.stream().filter(model -> model.getModelType() == BusinessTopoConstant.CLUSTER_INSTANCE_ID).collect(Collectors.toList());
        }

        List<BusinessInstanceRelationModel> relationList = new ArrayList<>(insModels.size());
        for (BusinessInstanceModelDB podModel : insModels) {
            List<String> deployDnList = deployMap.get(podModel.getDn());
            deployDnList.forEach(deployDn -> {
                BusinessInstanceRelationModel solutionRelation = new BusinessInstanceRelationModel();
                solutionRelation.setInstanceId(podModel.getInstanceId());
                solutionRelation.setTargetInstanceId(dnInstanceMap.get(deployDn));
                solutionRelation.setRelationType(BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT);
                relationList.add(solutionRelation);
            });
        }
        businessInstanceRelationDao.insertRelationInfo(relationList);
    }

    private void handleAddDocker(List<BusinessMoEvent> dockerEvent) throws ServiceException {
        if (CollectionUtils.isEmpty(dockerEvent)) {
            return;
        }
        // key为moType，value为addMoEvent的map
        Map<String, List<BusinessMoEvent>> moTypeMap = dockerEvent.stream()
            .collect(Collectors.groupingBy(event -> event.getMo().getType()));

        // 找到这些docker的父实例，建立key为fatherDn，value为father instanceId的map
        Map<String, Integer> fatherInsMap = getFatherDnInstanceIdMap(dockerEvent);

        // 找到这些docker的部署关系，建立key为docker dn，value为container instanceId的map
        Set<DN> dockerDns = dockerEvent.stream().map(event -> event.getMo().getDN()).collect(Collectors.toSet());
        Map<String, Integer> podVmMap = getInstanceDeployMap(dockerDns, BusinessTopoConstant.DOCKER_TYPE_ID);

        List<BusinessInstanceRelationModel> relationList = new ArrayList<>(dockerEvent.size() * 3);
        Map<String, BusinessCacheInstance> cacheMap = new HashMap<>(dockerEvent.size());

        for (Map.Entry<String, List<BusinessMoEvent>> entry : moTypeMap.entrySet()) {
            BusinessCommonModel commonModel = entry.getValue().get(0).getCommonModel();
            Map<String, Integer> dnInstanceMap = new HashMap<>(entry.getValue().size());
            for (BusinessMoEvent businessMoEvent : entry.getValue()) {
                ManagedObject mo = businessMoEvent.getMo();
                if (!fatherInsMap.containsKey(businessMoEvent.getParent())) {
                    LOGGER.error("[addMoEvent] docker has no father, docker dn = {}", mo.getDN().getValue());
                    continue;
                }
                // 插入变更网元实例和拓展属性
                BusinessInstanceModelDB createInstance = creatBusinessInstanceModelDB(mo, commonModel);
                if (createInstance == null) {
                    LOGGER.error("[addMoEvent] insert docker error, dn = {}", mo.getDN().getValue());
                    continue;
                }
                dnInstanceMap.put(createInstance.getDn(), createInstance.getInstanceId());
                // 建立父关系和部署关系
                buildFatherAndDeployRelation(businessMoEvent, createInstance, cacheMap, fatherInsMap, relationList, podVmMap, mo);
            }
            // 异步处理指标关联
            INDICATOR_EXECUTOR.submit(() -> {
                try {
                    // 插入关联指标
                    List<ManagedObject> mos = entry.getValue().stream().map(BusinessMoEvent::getMo).collect(Collectors.toList());
                    handleBusinessIndicators(mos);
                    createAppIndicator(commonModel.getModelId(), dnInstanceMap, entry.getValue().get(0).getMo().getType(), false);
                } catch (Exception e) {
                    LOGGER.info("[handleAddInstance] handle indicators error.", e);
                }
            });
        }
        businessInstanceRelationDao.insertRelationInfo(relationList);
        businessTreeMapOper.putAll(RedisConstant.BUSINESS_TOPO_RELATION_TREE, cacheMap);
    }

    private Map<String, Integer> getFatherDnInstanceIdMap(List<BusinessMoEvent> podEvent) {
        Set<String> fatherDns = podEvent.stream().map(BusinessMoEvent::getParent).collect(Collectors.toSet());
        List<BusinessInstanceModelDB> instances = businessInstanceModelDao.queryInstanceByDnsWithoutExtent(new ArrayList<>(fatherDns));
        return instances.stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(BusinessInstanceModelDB::getDn, BusinessInstanceModelDB::getInstanceId));
    }

    private void addDeployRelation(List<BusinessInstanceRelationModel> relationList,
        BusinessInstanceModelDB createInstance, Integer vmInstanceId, BusinessCacheInstance cache) {
        cache.setDeployInstanceId(vmInstanceId);
        BusinessInstanceRelationModel solutionRelation = new BusinessInstanceRelationModel();
        solutionRelation.setInstanceId(createInstance.getInstanceId());
        solutionRelation.setTargetInstanceId(vmInstanceId);
        solutionRelation.setRelationType(BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT);
        relationList.add(solutionRelation);
    }

    private void addDeployRelation(List<BusinessInstanceRelationModel> relationList,
        BusinessInstanceModelDB createInstance, Integer vmInstanceId) {
        BusinessInstanceRelationModel solutionRelation = new BusinessInstanceRelationModel();
        solutionRelation.setInstanceId(createInstance.getInstanceId());
        solutionRelation.setTargetInstanceId(vmInstanceId);
        solutionRelation.setRelationType(BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT);
        relationList.add(solutionRelation);
    }

    private BusinessInstanceModelDB creatBusinessInstanceModelDB(ManagedObject mo, BusinessCommonModel commonModel)
            throws ServiceException {
        // 插入变更网元实例
        BusinessInstanceModelDB createInstance = new BusinessInstanceModelDB();
        createInstance.setModelId(commonModel.getModelId());
        createInstance.setModelType(commonModel.getModelType());
        createInstance.setVersion(commonModel.getVersion());
        createInstance.setDn(mo.getDN().getValue());
        businessInstanceModelDao.insertInstance(createInstance);
        if (createInstance.getInstanceId() == null) {
            LOGGER.error("[addMoEvent] insert instance error, dn = {}", createInstance.getDn());
            return null;
        }
        // 插入拓展属性
        List<BusinessInsExtentAttrDB> instanceAttrs = getInstanceAttrList(mo, commonModel.getModelType(),
                createInstance.getInstanceId(), BusinessTopoConstant.EVENT_TYPE_ADD);
        createInstance.setAttrDBList(instanceAttrs);
        businessInstanceModelDao.insertInstanceAttr(instanceAttrs);
        return createInstance;
    }

    private void handleAddManagement(List<BusinessMoEvent> managementEvents) throws ServiceException {
        if (CollectionUtils.isEmpty(managementEvents)) {
            return;
        }
        // key为moType，value为addMoEvent的map
        Map<String, List<BusinessMoEvent>> moTypeMap = managementEvents.stream()
            .collect(Collectors.groupingBy(event -> event.getMo().getType()));
        List<BusinessInstanceRelationModel> relationList = new ArrayList<>(managementEvents.size() * 3);

        // 区分moType处理管理类实例的新增
        for (Map.Entry<String, List<BusinessMoEvent>> entry : moTypeMap.entrySet()) {
            BusinessCommonModel commonModel = entry.getValue().get(0).getCommonModel();
            String typeModelId = commonModel.getModelId()
                .replace(String.valueOf(BusinessTopoConstant.MANAGEMENT_INSTANCE_ID),
                    String.valueOf(BusinessTopoConstant.MANAGEMENT_INSTANCE_TYPE_ID));
            List<BusinessInstanceModelDB> typeInstance = businessInstanceModelDao.queryInstanceByModelId(typeModelId);
            // 管理类的501层有且只有一个
            if (CollectionUtils.isEmpty(typeInstance)) {
                LOGGER.error("[handleAddManagement] cannot find 501 instance, type modelId = {}", typeModelId);
                continue;
            }
            Integer topInstanceId = businessInstanceRelationDao.queryRelationByType(typeInstance.get(0).getInstanceId(), BusinessTopoConstant.RELATION_TYPE_TOP_LEVEL);
            if (topInstanceId == null) {
                LOGGER.error("[handleAddManagement] cannot find top instance, type modelId = {}", typeModelId);
                continue;
            }
            for (BusinessMoEvent event : entry.getValue()) {
                ManagedObject mo = event.getMo();
                BusinessInstanceModelDB createInstance = new BusinessInstanceModelDB();
                createInstance.setModelId(commonModel.getModelId());
                createInstance.setDn(mo.getDN().getValue());
                createInstance.setVersion(commonModel.getVersion());
                createInstance.setModelType(BusinessTopoConstant.MANAGEMENT_INSTANCE_ID);
                businessInstanceModelDao.insertInstance(createInstance);
                if (createInstance.getInstanceId() == null) {
                    LOGGER.error("[addMoEvent] insert instance error, dn = {}", createInstance.getDn());
                    continue;
                }
                BusinessInstanceRelationModel relation = new BusinessInstanceRelationModel();
                relation.setInstanceId(createInstance.getInstanceId());
                relation.setTargetInstanceId(typeInstance.get(0).getInstanceId());
                relation.setRelationType(BusinessTopoConstant.RELATION_TYPE_PARENT);
                relationList.add(relation);
                BusinessInstanceRelationModel relationTop = new BusinessInstanceRelationModel();
                relationTop.setInstanceId(createInstance.getInstanceId());
                relationTop.setTargetInstanceId(topInstanceId);
                relationTop.setRelationType(BusinessTopoConstant.RELATION_TYPE_TOP_LEVEL);
                relationList.add(relationTop);
            }
        }
        businessInstanceRelationDao.insertRelationInfo(relationList);
    }

    /**
     * 根据网元类型和时间类型生成拓展属性
     *
     * @param mo         网元
     * @param type       实例类型
     * @param instanceId 实例id
     * @param eventType  事件类型，ADD或者CHANGE
     * @return 拓展属性列表
     */
    public List<BusinessInsExtentAttrDB> getInstanceAttrList(ManagedObject mo, Integer type, Integer instanceId,
                                                             String eventType) {
        List<BusinessInsExtentAttrDB> attrs = new ArrayList<>();
        if (type == BusinessTopoConstant.CLUSTER_INSTANCE_ID) {
            presetClusterMoData(mo, instanceId, attrs);
        }
        if (type == BusinessTopoConstant.POD_TYPE_ID) {
            presetPodMoData(mo, instanceId, attrs);
        }
        if (type == BusinessTopoConstant.VM_TYPE_ID) {
            presetVmMoData(mo, instanceId, attrs);
        }
        // 网元新增时需要初始化告警信息，变更时不用
        if (BusinessTopoConstant.EVENT_TYPE_ADD.equals(eventType)) {
            attrs.add(new BusinessInsExtentAttrDB(
                    instanceId, BusinessTopoConstant.ATTR_KEY_ALARM_COUNT, INTEGER, null));

            attrs.add(new BusinessInsExtentAttrDB(
                    instanceId, BusinessTopoConstant.ATTR_KEY_ALARM_CSN, STRING, null));
        }

        if (StringUtils.isNotEmpty(mo.getHwsStripe())) {
            attrs.add(new BusinessInsExtentAttrDB(
                instanceId, BusinessTopoConstant.MM_HWS_STRIPE, STRING, mo.getHwsStripe()));
        }
        if (StringUtils.isNotEmpty(mo.getHwsAppSite())) {
            attrs.add(new BusinessInsExtentAttrDB(
                instanceId, BusinessTopoConstant.MM_HWS_UNIT, STRING, mo.getHwsAppSite()));
        }
        if (StringUtils.isNotEmpty(mo.getHwsSiteName())) {
            attrs.add(new BusinessInsExtentAttrDB(
                instanceId, BusinessTopoConstant.MM_SITE_NAME, STRING, mo.getHwsSiteName()));
        }
        if (StringUtils.isNotEmpty(mo.getHwsGroupName())) {
            attrs.add(new BusinessInsExtentAttrDB(
                instanceId, BusinessTopoConstant.MM_HWS_GROUP_NAME, STRING, mo.getHwsGroupName()));
        }
        if (StringUtils.isNotEmpty(mo.getHwsSwimLane())) {
            attrs.add(new BusinessInsExtentAttrDB(instanceId, BusinessTopoConstant.MM_SWIM_LANE, STRING,
                mo.getHwsSwimLane()));
        }

        return attrs;
    }

    private void presetVmMoData(ManagedObject mo, Integer instanceId, List<BusinessInsExtentAttrDB> attrs) {
        attrs.add(new BusinessInsExtentAttrDB(instanceId, BusinessTopoConstant.ATTR_KEY_VM_NAME, STRING, mo.getName()));
        if (mo instanceof ManagedElement) {
            ManagedElement managedElement = (ManagedElement) mo;
            attrs.add(new BusinessInsExtentAttrDB(instanceId, BusinessTopoConstant.ATTR_KEY_NODE_STATUS, STRING, managedElement.getOperationalStatus().name()));
            attrs.add(new BusinessInsExtentAttrDB(instanceId, BusinessTopoConstant.ATTR_KEY_NODE_IP, STRING, managedElement.getIPAddress()));
        }
    }

    private void presetPodMoData(ManagedObject mo, Integer instanceId, List<BusinessInsExtentAttrDB> attrs) {
        attrs.add(new BusinessInsExtentAttrDB(instanceId, BusinessTopoConstant.ATTR_KEY_POD_NAME, STRING, mo.getName()));
        attrs.add(new BusinessInsExtentAttrDB(
            instanceId, BusinessTopoConstant.ATTR_KEY_CREATE_TIME, STRING, String.valueOf(mo.getCreatedTime())));
        if (mo instanceof ManagedElement) {
            ManagedElement managedElement = (ManagedElement) mo;
            attrs.add(new BusinessInsExtentAttrDB(instanceId, BusinessTopoConstant.ATTR_KEY_NODE_STATUS, STRING, managedElement.getOperationalStatus().name()));
            attrs.add(new BusinessInsExtentAttrDB(instanceId, BusinessTopoConstant.ATTR_KEY_NODE_IP, STRING, managedElement.getIPAddress()));
        }
    }

    private void presetClusterMoData(ManagedObject mo, Integer instanceId, List<BusinessInsExtentAttrDB> attrs) {
        attrs.add(new BusinessInsExtentAttrDB(instanceId, BusinessTopoConstant.ATTR_KEY_APP_NAME, STRING, mo.getName()));
        attrs.add(new BusinessInsExtentAttrDB(instanceId, BusinessTopoConstant.ATTR_KEY_VERSION, STRING, mo.getVersion()));
        if (mo instanceof ManagedElement) {
            ManagedElement managedElement = (ManagedElement) mo;
            attrs.add(new BusinessInsExtentAttrDB(instanceId, BusinessTopoConstant.ATTR_KEY_NODE_STATUS, STRING, managedElement.getOperationalStatus().name()));
            attrs.add(new BusinessInsExtentAttrDB(instanceId, BusinessTopoConstant.ATTR_KEY_NODE_IP, STRING, managedElement.getIPAddress()));
        }
        try {
            MOType moTypeData = MITManagerClient.newInstance().getMOType(mo.getType());
            if (!Objects.isNull(moTypeData)) {
                attrs.add(new BusinessInsExtentAttrDB(instanceId, BusinessTopoConstant.ATTR_KEY_DISPLAY_TYPE, STRING, moTypeData.getDisplayType()));
            }
        } catch (OSSException e) {
            LOGGER.error("[creatBusinessInstanceModelDB] query MOType by getMOType error, the mo type is {}", mo.getType());
        }
    }

    public List<BusinessInstanceRelationModel> getRelationModels(Integer creatInstanceId, Integer parentInstanceId,
    BusinessCacheInstance cache)
            throws ServiceException {
        // 父子关系
        List<BusinessInstanceRelationModel> relations = new ArrayList<>();
        BusinessInstanceRelationModel parentRelation = new BusinessInstanceRelationModel();
        parentRelation.setInstanceId(creatInstanceId);
        parentRelation.setTargetInstanceId(parentInstanceId);
        parentRelation.setRelationType(BusinessTopoConstant.RELATION_TYPE_PARENT);
        relations.add(parentRelation);
        cache.setParentInstanceId(parentInstanceId);

        // 站点关系
        BusinessInstanceRelationModel siteRelation = new BusinessInstanceRelationModel();
        Integer siteInstanceId = businessInstanceRelationDao.queryRelationByType(parentInstanceId,
                BusinessTopoConstant.RELATION_TYPE_SITE);
        siteRelation.setInstanceId(creatInstanceId);
        siteRelation.setTargetInstanceId(siteInstanceId);
        siteRelation.setRelationType(BusinessTopoConstant.RELATION_TYPE_SITE);
        relations.add(siteRelation);
        cache.setSiteInstanceId(siteInstanceId);

        // 解决方案关系
        BusinessInstanceRelationModel solutionRelation = new BusinessInstanceRelationModel();
        Integer solutionInstanceId = businessInstanceRelationDao.queryRelationByType(parentInstanceId,
                BusinessTopoConstant.RELATION_TYPE_TOP_LEVEL);
        solutionRelation.setInstanceId(creatInstanceId);
        solutionRelation.setTargetInstanceId(solutionInstanceId);
        solutionRelation.setRelationType(BusinessTopoConstant.RELATION_TYPE_TOP_LEVEL);
        relations.add(solutionRelation);
        cache.setTopInstanceId(solutionInstanceId);

        return relations;
    }

    public void createAppIndicator(String commonModelId, Map<String, Integer> dnInstanceMap, String moType, Boolean isCluster) {
        List<IndicatorEntity> indicatorEntities = getImportIndicatorList(commonModelId);
        if (!isCluster) {
            indicatorEntities = indicatorEntities.stream().filter(entity -> Objects.equals(moType, entity.getMoType())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(indicatorEntities) || CollectionUtils.isEmpty(dnInstanceMap.keySet())) {
            return;
        }
        List<String> dns = new ArrayList<>(dnInstanceMap.keySet());
        List<Indicator> indicators = pmIndicatorInstanceService.getIndicatorWithoutInstance(indicatorEntities, moType, dns);
        indicators.forEach(indicator -> {
            indicator.setInstanceId(dnInstanceMap.get(indicator.getDn()));
            indicator.setIndicatorDisplayType(BusinessTopoConstant.INDICATOR_TYPE_GOLD);
        });
        businessIndicatorDao.insertIndicators(indicators);
    }

    private List<IndicatorEntity> getImportIndicatorList(String commonModelId) {
        String indicatorListJson = businessCommonModelExtentAttrDao.getIndicatorListByModelId(commonModelId);
        if (StringUtils.isEmpty(indicatorListJson) || BusinessTopoConstant.NULL.equals(indicatorListJson)) {
            return new ArrayList<>();
        }
        List<IndicatorInfo> indicatorInfos;
        try {
            indicatorInfos = JSON.parseArray(indicatorListJson, IndicatorInfo.class);
        } catch (JSONException e) {
            LOGGER.error("parse indicator list error, modelId = {}", commonModelId, e);
            return new ArrayList<>();
        }
        return JSON.parseArray(JSON.toJSONString(indicatorInfos), IndicatorEntity.class);
    }

    /**
     * 从数据库还原导入时的配置文件
     *
     * @param solutionName 解决方案名称
     * @return 配置文件对象
     * @throws ServiceException e
     */
    public ConfigurationImportEntity getConfigurationImportEntity(String solutionName) throws ServiceException {
        // 解决方案顶层模型是唯一的
        BusinessCommonModel model = businessCommonModelDao.queryTopModelBySolutionName(solutionName);
        if (model == null) {
            LOGGER.error("flush eam tree failed, cannot find solution model, solution name = {}", solutionName);
            throw new ServiceException("get top common model is empty");
        }
        ConfigurationImportEntity importEntity;
        try {
            importEntity = JSON.parseObject(JSON.toJSONString(model), ConfigurationImportEntity.class, JSONReader.Feature.SupportSmartMatch);
        } catch (JSONException e) {
            LOGGER.error("parse import entity json error.", e);
            throw new ServiceException("parse import entity json error");
        }

        // 从数据库加载导入的配置
        handleSiteDataSourceConfig(importEntity);
        handleSiteGroupConfig(importEntity);
        handleBusinessAppGroupConfig(importEntity);
        handleBusinessAppTypeConfig(importEntity);
        handleBusinessAppInstanceConfig(importEntity);
        return importEntity;
    }

    public void handleSiteDataSourceConfig(ConfigurationImportEntity importEntity) throws ServiceException {
        List<BusinessCommonModel> siteModels = businessCommonModelDao.queryCommonModelByType(
                BusinessTopoConstant.SITE_TYPE_ID);
        siteModels = siteModels.stream()
            .filter(model -> model.getSolutionName().equals(importEntity.getSolutionName()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(siteModels)) {
            LOGGER.error("find site model failed, site model is empty!");
            throw new ServiceException("site model is empty");
        }
        BusinessCommonModel siteModel = siteModels.get(0);
        SiteDataSourceConfig siteDataSourceConfig = new SiteDataSourceConfig();
        siteDataSourceConfig.setModelName(siteModel.getModelName());
        siteDataSourceConfig.setModelType(siteModel.getModelType());
        String modelId = createModelId(siteModel. getModelType(),siteModel.getModelName(), importEntity.getSolutionName());
        List<BusinessCommonModelExtentAttr> extentAttrs = businessCommonModelExtentAttrDao
                .queryCommonModelAttrByModelId(modelId);
        Map<String, String> extentAttrMap = extentAttrs.stream().collect(Collectors.toMap(
                BusinessCommonModelExtentAttr::getStaticAttrName, BusinessCommonModelExtentAttr::getStaticAttrValue));
        siteDataSourceConfig.setSiteMoType(extentAttrMap.get(BusinessTopoConstant.SITE_MO_TYPE_ATTR_KEY));
        siteDataSourceConfig.setSiteIdColumn(extentAttrMap.get(BusinessTopoConstant.SITE_ID_COLUMN_ATTR_KEY));
        siteDataSourceConfig.setSiteDataType(Integer.parseInt(extentAttrMap.get(BusinessTopoConstant.SITE_DATA_TYPE_ATTR_KEY)));

        if (extentAttrMap.get(BusinessTopoConstant.INDICATOR_LIST_ATTR_KEY) != null) {
            try {
                List<IndicatorInfo> indicatorInfos = JSON.parseArray(extentAttrMap.get(
                    BusinessTopoConstant.INDICATOR_LIST_ATTR_KEY), IndicatorInfo.class);
                siteDataSourceConfig.setIndicatorList(indicatorInfos);
            } catch (JSONException e) {
                LOGGER.error("parse site indicator error", e);
            }
        }

        if (extentAttrMap.get(BusinessTopoConstant.LINK_INDICATOR_LIST_ATTR_KEY) != null) {
            try {
                List<IndicatorInfo> indicatorInfos = JSON.parseArray(extentAttrMap.get(
                    BusinessTopoConstant.LINK_INDICATOR_LIST_ATTR_KEY), IndicatorInfo.class);
                siteDataSourceConfig.setLinkIndicatorList(indicatorInfos);
            } catch (JSONException e) {
                LOGGER.error("parse site link indicator error", e);
            }
        }

        importEntity.setSiteDataSourceConfig(siteDataSourceConfig);
    }

    public void handleSiteGroupConfig(ConfigurationImportEntity importEntity) throws ServiceException {
        List<BusinessCommonModel> siteGroupModels = businessCommonModelDao.queryCommonModelByType(
                BusinessTopoConstant.SITE_GROUP_TYPE_ID);
        siteGroupModels = siteGroupModels.stream()
            .filter(model -> model.getSolutionName().equals(importEntity.getSolutionName()))
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(siteGroupModels)) {
            LOGGER.error("find site group model failed, site group model is empty!");
            throw new ServiceException("site group model is empty");
        }
        BusinessCommonModel siteGroupModel = siteGroupModels.get(0);
        SiteGroupConfig siteGroupConfig = new SiteGroupConfig();
        siteGroupConfig.setModelName(siteGroupModel.getModelName());
        siteGroupConfig.setModelType(siteGroupModel.getModelType());
        List<BusinessCommonModelExtentAttr> extentAttrs = businessCommonModelExtentAttrDao
            .queryCommonModelAttrByModelId(siteGroupModel.getModelId());
        Map<String, String> extentAttrMap = extentAttrs.stream().collect(Collectors.toMap(
            BusinessCommonModelExtentAttr::getStaticAttrName, BusinessCommonModelExtentAttr::getStaticAttrValue));
        if (extentAttrMap.get(BusinessTopoConstant.INDICATOR_LIST_ATTR_KEY) != null) {
            try {
                List<IndicatorInfo> indicatorInfos = JSON.parseArray(extentAttrMap.get(
                    BusinessTopoConstant.INDICATOR_LIST_ATTR_KEY), IndicatorInfo.class);
                siteGroupConfig.setIndicatorList(indicatorInfos);
            } catch (JSONException e) {
                LOGGER.error("parse site indicator error", e);
            }
        }
        importEntity.setSiteGroupConfig(siteGroupConfig);
    }

    public void handleBusinessAppTypeConfig(ConfigurationImportEntity importEntity) {
        BusinessAppTypeConfig businessAppTypeConfig = new BusinessAppTypeConfig();
        List<BusinessCommonModel> appTypeModels = businessCommonModelDao.queryCommonModelByType(
                BusinessTopoConstant.MOTYPE_CLUSTER_TYPE_ID);
        appTypeModels = appTypeModels.stream()
            .filter(model -> model.getSolutionName().equals(importEntity.getSolutionName()))
            .collect(Collectors.toList());
        List<BusinessAppType> appTypes = new ArrayList<>(appTypeModels.size());
        for (BusinessCommonModel appTypeModel : appTypeModels) {
            String modelId = createModelId(appTypeModel.getModelType(), appTypeModel.getModelName(), importEntity.getSolutionName());
            List<BusinessCommonModelExtentAttr> appTypeExtentAttrs = businessCommonModelExtentAttrDao
                    .queryCommonModelAttrByModelId(modelId);
            Map<String, String> extentAttrMap = appTypeExtentAttrs.stream().collect(Collectors.toMap(
                    BusinessCommonModelExtentAttr::getStaticAttrName, BusinessCommonModelExtentAttr::getStaticAttrValue));
            BusinessAppType appType = new BusinessAppType();
            appType.setModelName(appTypeModel.getModelName());
            appType.setAppTypeName(extentAttrMap.get(BusinessTopoConstant.APP_TYPE_NAME_ATTR_KEY));
            appTypes.add(appType);
        }
        businessAppTypeConfig.setModelType(BusinessTopoConstant.MOTYPE_CLUSTER_TYPE_ID);
        businessAppTypeConfig.setBusinessAppTypeList(appTypes);
        importEntity.setBusinessAppTypeConfig(businessAppTypeConfig);
    }

    public void handleBusinessAppGroupConfig(ConfigurationImportEntity importEntity) {
        BusinessAppGroupConfig businessAppGroupConfig = new BusinessAppGroupConfig();
        List<BusinessCommonModel> appGroupModels = businessCommonModelDao.queryCommonModelByType(
                BusinessTopoConstant.BUSINESS_GROUP_TYPE_ID);
        appGroupModels = appGroupModels.stream()
            .filter(model -> model.getSolutionName().equals(importEntity.getSolutionName()))
            .collect(Collectors.toList());
        List<BusinessAppGroupInfo> businessAppGroupList = new ArrayList<>(appGroupModels.size());
        for (BusinessCommonModel appGroupModel : appGroupModels) {
            BusinessAppGroupInfo appGroupInfo = new BusinessAppGroupInfo();
            appGroupInfo.setModelName(appGroupModel.getModelName());
            String modelId = createModelId(appGroupModel.getModelType(), appGroupModel.getModelName(), importEntity.getSolutionName());
            List<BusinessCommonModelExtentAttr> appGroupExtentAttrs = businessCommonModelExtentAttrDao
                    .queryCommonModelAttrByModelId(modelId);
            Map<String, String> extentAttrMap = appGroupExtentAttrs.stream().collect(Collectors.toMap(
                    BusinessCommonModelExtentAttr::getStaticAttrName, BusinessCommonModelExtentAttr::getStaticAttrValue));
            List<IncludeAppTypeInfo> includeAppTypeList = new ArrayList<>();
            try {
                includeAppTypeList = JSON.parseArray(extentAttrMap.get(
                        BusinessTopoConstant.INCLUDE_APP_TYPE_LIST), IncludeAppTypeInfo.class);
            } catch (JSONException e) {
                LOGGER.error("parse include app type list array json error");
            }
            appGroupInfo.setGroupName(extentAttrMap.get(BusinessTopoConstant.GROUP_NAME_ATTR_KEY));
            appGroupInfo.setIncludeAppTypeList(includeAppTypeList);
            businessAppGroupList.add(appGroupInfo);
        }
        businessAppGroupConfig.setModelType(BusinessTopoConstant.BUSINESS_GROUP_TYPE_ID);
        businessAppGroupConfig.setBusinessAppGroupList(businessAppGroupList);
        importEntity.setBusinessAppGroupConfig(businessAppGroupConfig);
    }

    public void handleBusinessAppInstanceConfig(ConfigurationImportEntity importEntity) {
        BusinessAppInstanceConfig businessAppInstanceConfig = new BusinessAppInstanceConfig();
        List<BusinessCommonModel> appInstanceModels = businessCommonModelDao.queryCommonModelByType(
                BusinessTopoConstant.CLUSTER_INSTANCE_ID);
        appInstanceModels = appInstanceModels.stream()
            .filter(model -> model.getSolutionName().equals(importEntity.getSolutionName()))
            .collect(Collectors.toList());
        List<BusinessAppInstanceInfo> appInstanceInfos = new ArrayList<>(appInstanceModels.size());
        for (BusinessCommonModel appInstanceModel : appInstanceModels) {
            String modelId = createModelId(appInstanceModel.getModelType(), appInstanceModel.getModelName(), importEntity.getSolutionName());
            List<BusinessCommonModelExtentAttr> appInstanceExtentAttrs = businessCommonModelExtentAttrDao
                    .queryCommonModelAttrByModelId(modelId);
            Map<String, String> extentAttrMap = appInstanceExtentAttrs.stream().collect(Collectors.toMap(
                    BusinessCommonModelExtentAttr::getStaticAttrName, BusinessCommonModelExtentAttr::getStaticAttrValue));
            BusinessAppInstanceInfo appInstanceInfo = new BusinessAppInstanceInfo();
            appInstanceInfo.setMoTypeMapping(extentAttrMap.get(BusinessTopoConstant.MO_TYPE_MAPPING_ATTR_KEY));
            appInstanceInfo.setModelName(appInstanceModel.getModelName());
            handleInstanceConfigList(extentAttrMap, appInstanceInfo);
            if (extentAttrMap.containsKey(BusinessTopoConstant.APPLICATION_TYPE_ATTR_KEY)) {
                appInstanceInfo.setApplicationType(Integer.parseInt(extentAttrMap.get(
                        BusinessTopoConstant.APPLICATION_TYPE_ATTR_KEY)));
            }
            if (extentAttrMap.containsKey(BusinessTopoConstant.ALARM_FILTER_TYPE_ATTR_KEY)
                    && !extentAttrMap.get(BusinessTopoConstant.ALARM_FILTER_TYPE_ATTR_KEY).equals(BusinessTopoConstant.NULL)) {
                appInstanceInfo.setAlarmFilterType(Integer.parseInt(extentAttrMap.get(
                        BusinessTopoConstant.ALARM_FILTER_TYPE_ATTR_KEY)));
            }
            if (StringUtils.isEmpty(extentAttrMap.get(BusinessTopoConstant.PARENT_MO_TYPE_NAME_ATTR_KEY))) {
                appInstanceInfo.setParentModelName(extentAttrMap.get(BusinessTopoConstant.PARENT_MODEL_NAME_ATTR_KEY));
            } else {
                appInstanceInfo.setParentModelName(extentAttrMap.get(BusinessTopoConstant.PARENT_MO_TYPE_NAME_ATTR_KEY));
            }
            appInstanceInfo.setPodLevel(extentAttrMap.get(BusinessTopoConstant.POD_LEVEL_ATTR_KEY));
            appInstanceInfos.add(appInstanceInfo);
        }
        businessAppInstanceConfig.setModelType(BusinessTopoConstant.CLUSTER_INSTANCE_ID);
        businessAppInstanceConfig.setBusinessAppInstanceList(appInstanceInfos);
        importEntity.setBusinessAppInstanceConfig(businessAppInstanceConfig);
    }

    private void handleInstanceConfigList(Map<String, String> extentAttrMap, BusinessAppInstanceInfo appInstanceInfo) {
        handleIndicatorAndAlarmList(extentAttrMap, appInstanceInfo, BusinessAppInstanceInfo.class);
        try {
            appInstanceInfo.setBusinessSiteConfig(JSON.parseObject(extentAttrMap.get(
                    BusinessTopoConstant.BUSINESS_SITE_CONFIG_ATTR_KEY), BusinessSiteConfig.class, JSONReader.Feature.SupportSmartMatch));
        } catch (JSONException e) {
            LOGGER.error("parse business site config json error.", e);
        }
        if (extentAttrMap.containsKey(BusinessTopoConstant.EVENT_LIST_ATTR_KEY)
                && !extentAttrMap.get(BusinessTopoConstant.EVENT_LIST_ATTR_KEY).equals(BusinessTopoConstant.NULL)) {
            try {
                appInstanceInfo.setEventList((JSON.parseArray(extentAttrMap.get(
                        BusinessTopoConstant.EVENT_LIST_ATTR_KEY), EventInfo.class)));
            } catch (JSONException e) {
                LOGGER.error("parse event list json error.", e);
            }
        }
        if (extentAttrMap.containsKey(BusinessTopoConstant.ASSOCIATION_APP_LINK_LIST_ATTR_KEY)
                && !extentAttrMap.get(BusinessTopoConstant.ASSOCIATION_APP_LINK_LIST_ATTR_KEY).equals(BusinessTopoConstant.NULL)) {
            try {
                appInstanceInfo.setAssociationAppLinkList((JSON.parseArray(extentAttrMap.get(
                        BusinessTopoConstant.ASSOCIATION_APP_LINK_LIST_ATTR_KEY), AssociationAppLink.class)));
            } catch (JSONException e) {
                LOGGER.error("parse association app link list json error.", e);
            }
        }
    }

    private <T> void handleIndicatorAndAlarmList(Map<String, String> extentAttrMap, T targetObject, Class<T> targetType) {
        if (extentAttrMap.containsKey(BusinessTopoConstant.INDICATOR_LIST_ATTR_KEY)
                && !extentAttrMap.get(BusinessTopoConstant.INDICATOR_LIST_ATTR_KEY).equals(BusinessTopoConstant.NULL)) {
            try {
                List<IndicatorInfo> indicatorList = JSON.parseArray(extentAttrMap.get(
                        BusinessTopoConstant.INDICATOR_LIST_ATTR_KEY), IndicatorInfo.class);
                targetType.getMethod("setIndicatorList", List.class).invoke(targetObject, indicatorList);
            } catch (JSONException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                LOGGER.error("parse indicator list json error.", e);
            }
        }
        if (extentAttrMap.containsKey(BusinessTopoConstant.ALARM_LIST_ATTR_KEY)
                && !extentAttrMap.get(BusinessTopoConstant.ALARM_LIST_ATTR_KEY).equals(BusinessTopoConstant.NULL)) {
            try {
                List<AlarmInfo> alarmList = JSON.parseArray(extentAttrMap.get(
                        BusinessTopoConstant.ALARM_LIST_ATTR_KEY), AlarmInfo.class);
                targetType.getMethod("setAlarmList", List.class).invoke(targetObject, alarmList);
            } catch (JSONException | NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
                LOGGER.error("parse alarm list json error.", e);
            }
        }
    }

    private void handleBusinessIndicators(List<ManagedObject> mos) {
        if (CollectionUtils.isEmpty(mos)) {
            return;
        }
        Map<Integer, String> indicatorListMap = businessCommonModelExtentAttrDao.getBusinessIndicatorList();
        for (Map.Entry<Integer, String> entry : indicatorListMap.entrySet()) {
            if (StringUtils.isEmpty(entry.getValue()) || BusinessTopoConstant.NULL.equals(entry.getValue())) {
                continue;
            }
            List<IndicatorEntity> indicatorEntities;
            try {
                List<IndicatorInfo> indicatorInfos = JSON.parseArray(entry.getValue(), IndicatorInfo.class);
                indicatorInfos = indicatorInfos.stream()
                        .filter(indicatorInfo -> indicatorInfo.getMoType().equals(mos.get(0).getType()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(indicatorInfos)) {
                    continue;
                }
                indicatorEntities = JSON.parseArray(JSON.toJSONString(indicatorInfos), IndicatorEntity.class);
            } catch (JSONException e) {
                LOGGER.error("parse indicator list error", e);
                continue;
            }
            List<String> dns = mos.stream().map(mo -> mo.getDN().getValue()).collect(Collectors.toList());
            List<Indicator> indicators = pmIndicatorInstanceService.getIndicatorWithoutInstance(indicatorEntities, mos.get(0).getType(), dns);
            for (Indicator indicator : indicators) {
                indicator.setInstanceId(entry.getKey());
                if (indicator.getIndicatorDisplayType() == null) {
                    indicator.setIndicatorDisplayType(BusinessTopoConstant.INDICATOR_TYPE_GOLD);
                }
            }
            businessIndicatorDao.insertIndicators(indicators);
        }
    }

    public void handleExistDbInstance(ConfigurationImportEntity importEntity) {
        List<BusinessAppInstanceInfo> appInstanceInfos = importEntity.getBusinessAppInstanceConfig().getBusinessAppInstanceList();
        List<BusinessAppInstanceInfo> dbInstanceInfos = appInstanceInfos.stream()
            .filter(info -> info.getApplicationType().equals(BusinessTopoConstant.DATABASE_APPLICATION_TYPE))
            .collect(Collectors.toList());
        List<String> dbMoTypes = dbInstanceInfos.stream().map(BusinessAppInstanceInfo::getMoTypeMapping).collect(Collectors.toList());
        List<ManagedObject> mos = new ArrayList<>();
        for (String moType : dbMoTypes) {
            mos.addAll(EamUtil.getMoByMoType(moType));
        }
        List<String> dns = mos.stream().map(mo -> mo.getDN().getValue()).collect(Collectors.toList());
        List<BusinessInstanceModelDB> dbInstances = businessInstanceModelDao.queryInstanceByDnsWithoutExtends(dns);
        Set<String> existDns = dbInstances.stream().map(BusinessInstanceModelDB::getDn).collect(Collectors.toSet());
        // 过滤掉库里已有的数据库应用之后新增
        mos = mos.stream().filter(mo -> !existDns.contains(mo.getDN().getValue())).collect(Collectors.toList());
        mos.forEach(mo -> addMoEvent(mo, null));
    }

    public void handleExistPlatformService(ConfigurationImportEntity importEntity, List<ManagedObject> mos) {
        List<BusinessAppInstanceInfo> appInstanceInfos = importEntity.getBusinessAppInstanceConfig().getBusinessAppInstanceList();
        List<DN> siteDns = mos.stream().map(mo -> new DN(mo.getDN().getValue())).collect(Collectors.toList());
        List<ManagedObject> mosWithChildren = EamUtil.getMoByDns(siteDns);
        Vector<DN> siteAppDns = mosWithChildren.stream().flatMap(mo -> mo.children().stream()).collect(Collectors.toCollection(Vector::new));
        List<ManagedObject> siteAppMos = EamUtil.getMoByDns(siteAppDns);
        Set<String> siteAppMoTypes = siteAppMos.stream().map(ManagedObject::getType).collect(Collectors.toSet());
        Set<String> platformServiceMoTypes = appInstanceInfos.stream()
            .filter(info -> !info.getApplicationType().equals(BusinessTopoConstant.DATABASE_APPLICATION_TYPE))
            .map(BusinessAppInstanceInfo::getMoTypeMapping)
            .filter(moTypeMapping -> !siteAppMoTypes.contains(moTypeMapping)).collect(Collectors.toSet());

        for (String platformService : platformServiceMoTypes) {
            List<ManagedObject> insMos = EamUtil.queryMosByMoType(platformService);
            addInstanceAndPodAndDocker(insMos);
        }
    }

    private void addInstanceAndPodAndDocker(List<ManagedObject> insMos) {
        insMos.forEach(mo -> addMoEvent(mo, null));
        List<DN> podDns = insMos.stream().flatMap(mo -> mo.children().stream()).collect(Collectors.toList());
        List<ManagedObject> podMos = EamUtil.getMoByDns(podDns);
        podMos.forEach(mo -> addMoEvent(mo, mo.getParent().getValue()));
        List<DN> dockerDns = podMos.stream().flatMap(mo -> mo.children().stream()).collect(Collectors.toList());
        List<ManagedObject> dockerMos = EamUtil.getMoByDns(dockerDns);
        dockerMos.forEach(mo -> addMoEvent(mo, mo.getParent().getValue()));
    }

    private void handleAddSolutionMm(List<BusinessMoEvent> mmEvents) {
        try {
            String solutionName = mmEvents.get(0).getCommonModel().getSolutionName();
            List<ManagedObject> mos = mmEvents.stream().map(BusinessMoEvent::getMo).collect(Collectors.toList());
            ConfigurationImportEntity importEntity = getConfigurationImportEntity(solutionName);
            addingSiteModelHandler.addNewMmSiteInstance(importEntity, mos);
            topoInstanceRelationCreator.buildSiteRelations(importEntity);
            topoInstanceRelationCreator.buildTopRelations(importEntity);
            handleExistDbInstance(importEntity);
            handleExistPlatformService(importEntity, mos);
        } catch (Exception e) {
            LOGGER.error("handle add mm solution error", e);
        }
    }

    private void handleMmDbChangeEvents(List<BusinessMoEvent> addEvents, List<BusinessInstanceModelDB> existInstances) {
        Set<String> existDns = existInstances.stream()
            .map(BusinessInstanceModelDB::getDn)
            .collect(Collectors.toSet());
        List<BusinessMoEvent> notExistEvents = addEvents.stream()
            .filter(event -> !existDns.contains(event.getMo().getDN().getValue()))
            .collect(Collectors.toList());
        for (BusinessMoEvent event : notExistEvents) {
            if (MM_DB_TYPE.contains(event.getMo().getType()) && isMmMo(event.getMo())) {
                event.setType(BusinessTopoConstant.MO_EVENT_ADD);
                MO_EVENT_QUEUE.add(event);
            }
        }
    }

    private boolean isMmMo(ManagedObject mo) {
        if (StringUtils.isNotEmpty(mo.getHwsSiteName()) && StringUtils.isNotEmpty(mo.getHwsGroupName()) && StringUtils.isNotEmpty(mo.getHwsStripe())) {
            return true;
        }
        return false;
    }

    public void createAppIndicatorForMm(String commonModelId, Map<String, Integer> dnInstanceMap, String moType) {
        List<IndicatorEntity> indicatorEntities = getImportIndicatorList(commonModelId);
        if (CollectionUtils.isEmpty(indicatorEntities) || CollectionUtils.isEmpty(dnInstanceMap.keySet())) {
            return;
        }
        List<String> dns = new ArrayList<>(dnInstanceMap.keySet());
        List<Indicator> indicators = pmIndicatorInstanceService.getIndicatorWithoutInstance(indicatorEntities, moType, dns);
        indicators.forEach(indicator -> {
            indicator.setInstanceId(dnInstanceMap.get(indicator.getDn()));
            indicator.setIndicatorDisplayType(BusinessTopoConstant.INDICATOR_TYPE_GOLD);
        });
        businessIndicatorDao.insertIndicators(indicators);
    }

    public void handleDbRelationWhenAdd(BusinessCommonModel commonModel, Map<String, Integer> dnInstanceMap,
        List<BusinessInstanceRelationModel> relationList) {
        if (!isDbAppType(commonModel.getExtentAttrs())) {
            return;
        }
        // 找到这些数据库应用的部署关系，建立key为实例dn，value为vm instanceId的map
        Set<DN> dbDns = dnInstanceMap.keySet().stream().map(DN::new).collect(Collectors.toSet());
        Map<String, List<Integer>> dbVmMap = getInstanceMulDeployMap(dbDns, BusinessTopoConstant.CLUSTER_INSTANCE_ID);

        for (Map.Entry<String, Integer> entry : dnInstanceMap.entrySet()) {
            if (!dbVmMap.containsKey(entry.getKey())) {
                continue;
            }
            List<Integer> vmList = dbVmMap.get(entry.getKey());
            vmList.forEach(vm  -> {
                BusinessInstanceRelationModel solutionRelation = new BusinessInstanceRelationModel();
                solutionRelation.setInstanceId(dnInstanceMap.get(entry.getKey()));
                solutionRelation.setTargetInstanceId(vm);
                solutionRelation.setRelationType(BusinessTopoConstant.RELATION_TYPE_DEPLOYMENT);
                relationList.add(solutionRelation);
            });
        }
    }
}