/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.i2000.dvtoposervice.util;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvtoposervice.bean.pm.HistoryDatas;
import com.huawei.i2000.dvtoposervice.bean.pm.HistoryQueryData;
import com.huawei.i2000.dvtoposervice.bean.pm.HistoryQueryValue;
import com.huawei.i2000.dvtoposervice.bean.pm.PmResult;
import com.huawei.i2000.dvtoposervice.bean.pm.TaskPeriodsBean;
import com.huawei.i2000.dvtoposervice.business.entity.MeasObjectInfoNew;
import com.huawei.i2000.dvtoposervice.constant.RestConstant;
import com.huawei.i2000.dvtoposervice.util.dto.ThresholdConditionForTopology;
import com.huawei.i2000.dvtoposervice.util.dto.ThresholdStaticInfo;
import com.huawei.i2000.mim.json.jackson.MIMMapper;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONReader;
import com.alibaba.fastjson2.TypeReference;
import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 性能接口调用类
 *
 * <AUTHOR>
 * @since 2021/2/24
 */
public class PerformanceClient {
    private static OssLog LOGGER = OssLogFactory.getLogger(PerformanceClient.class);

    private static volatile PerformanceClient pmClient;

    private static final String GET_TASK_PERIOD_NEW = "rest/dvpmservice/v1/service/collect/taskmgr/periods";

    private static final String GET_TASK_CPU_NMU = "rest/dvpmservice/v1/service/collect/measobjects/query";

    private static final String POST_RULE_FOR_TOPOLOGY
        = "/rest/dvpmservice/v1/service/monitor/threshold/alarmStaticInfo";

    private static final String GET_TASK_PERIOD = "/rest/i2000/pm/periods";

    private static final String GET_PM_HISTORY_DATA_NEW = "/rest/dvpmservice/v1/service/monitor/history/query-data";

    private static final int MAX_BODY_SIZE = 200 * 1024 * 1024;

    private static final String MO_TYPE = "moType";

    private static final String MO_TYPE_VALUE = "com.huawei.IRes.vm";

    private static final String MEAS_UNIT_KEY = "measUnitKey";

    private static final String MEAS_UNIT_KEY_VALUE = "LogicalCPU";

    private static final String DN = "dn";

    private static final String ORIGINAL_VALUES = "originalValues";

    private static final String THRESHOLD_VALUES = "thresholdConditionForTopologys";

    private static MIMMapper mapper = MIMMapper.newInstance();

    private PerformanceClient() {
    }

    /**
     * 获取性能接口调用类
     *
     * @return 性能接口调用类
     */
    public static PerformanceClient getInstance() {
        if (pmClient == null) {
            synchronized (PerformanceClient.class) {
                if (pmClient == null) {
                    pmClient = new PerformanceClient();
                }
            }
        }
        return pmClient;
    }

    /**
     * 查询虚机cpu数量
     *
     * @param dn 虚机dn
     * @param moType 网元类型
     * @return cpu数量
     */
    public int taskVmCpuNmu(String dn, String moType) {
        String targetUrl = GET_TASK_CPU_NMU;
        try {
            targetUrl = RestUtil.addUrlParam(targetUrl, MEAS_UNIT_KEY, MEAS_UNIT_KEY_VALUE);
            targetUrl = RestUtil.addUrlParam(targetUrl, MO_TYPE, moType);
            targetUrl = RestUtil.addUrlParam(targetUrl, DN, dn);
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_GET, targetUrl, null, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.info("[PerformanceClient] get taskVMCpuNmu error, moType={}, measUnitKey={}.", MO_TYPE_VALUE,
                    MEAS_UNIT_KEY_VALUE);
                return -1;
            }
            PmResult<List<MeasObjectInfoNew>> pmResult = JSONObject.parseObject(response.getResponseContent(),
                new PmResultType(), JSONReader.Feature.SupportSmartMatch);
            if (pmResult == null || pmResult.getResultCode() != PmResult.SUC) {
                LOGGER.error(
                    "[PerformanceClient] get taskVMCpuNmu response status error, moType={}, measUnitKey={}, pmResult = {}",
                    MO_TYPE_VALUE, MEAS_UNIT_KEY_VALUE, pmResult);
                return -1;
            }
            return pmResult.getResult().size();
        } catch (ServiceException e) {
            LOGGER.info("[PerformanceClient] get taskVMCpuNmu error, moType={}, measUnitKey={}, e={}", MO_TYPE_VALUE,
                MEAS_UNIT_KEY_VALUE, e);
            return -1;
        }
    }

    /**
     * 获取采集任务周期
     *
     * @param moType moType
     * @param measUnitKey 测量对象
     * @return 周期时间
     */
    public int getPeriod(String moType, String measUnitKey) {
        String targetUrl = GET_TASK_PERIOD_NEW;
        try {
            targetUrl = RestUtil.addUrlParam(targetUrl, MO_TYPE, moType);
            targetUrl = RestUtil.addUrlParam(targetUrl, MEAS_UNIT_KEY, measUnitKey);
            RestfulResponse response = null;
            LOGGER.debug(
                "[PerformanceClient] get taskperiods by moType & measUnit start.moType is {}, measUnitKey is {}",
                moType, measUnitKey);

            response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_GET, targetUrl, null, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.info("[PerformanceClient] get taskperiods error, moType={}, measUnitKey={}.", moType,
                    measUnitKey);
                return -1;
            }
            PmResult<TaskPeriodsBean> pmResult = JSONObject.parseObject(response.getResponseContent(),
                new PmResultTypeReference(), JSONReader.Feature.SupportSmartMatch);
            if (pmResult == null || pmResult.getResultCode() != PmResult.SUC) {
                LOGGER.error(
                    "[PerformanceClient] get taskperiods response status error, moType={}, measUnitKey={}, pmResult = {}",
                    moType, measUnitKey, pmResult);
                return -1;
            }
            return pmResult.getResult().getPeriod();
        } catch (ServiceException e) {
            LOGGER.info("[PerformanceClient] get taskperiods error, moType={}, measUnitKey={}, e={}", moType,
                measUnitKey, e);
            return -1;
        }
    }

    static class PmResultType extends TypeReference<PmResult<List<MeasObjectInfoNew>>> {
        /**
         * 构造方法
         */
        public PmResultType() {
        }
    }

    static class PmResultTypeReference extends TypeReference<PmResult<TaskPeriodsBean>> {
        /**
         * 构造方法
         */
        public PmResultTypeReference() {
        }
    }

    static class ResultTypeReference extends TypeReference<PmResult<HistoryQueryData>> {
        /**
         * 构造方法
         */
        public ResultTypeReference() {

        }
    }

    /**
     * 获取采集任务周期
     *
     * @param queryCondition 性能接口请求对象
     * @return 历史性能数据结果
     */
    public List<HistoryQueryData> getHistoryDataFormPerformance(HistoryDatas queryCondition) {
        // 性能一次只能查询一个测量单元下面的历史数据
        LOGGER.info("[PerformanceClient] get historydata start, moType={}, measUnitKey={}.", queryCondition.getMoType(),
            queryCondition.getMeasUnitKey());
        List<HistoryQueryData> result = new ArrayList<>();
        String url = GET_PM_HISTORY_DATA_NEW;
        try {
            long startTime = System.currentTimeMillis();
            RestfulOptions options = new RestfulOptions();
            options.setRestTimeout(RestfulOptions.REST_OPTIONS_TIMEOUT_MAXTIMEOUT);
            options.setOption("maxBodySize", MAX_BODY_SIZE);

            int currentPage = queryCondition.getPageIndex();
            HistoryQueryData historyQueryData = sendRequest(RestConstant.RESTFUL_METHOD_POST, url,
                JSON.toJSONString(queryCondition), options);
            if (historyQueryData == null) {
                return result;
            }
            result.add(historyQueryData);
            Map<String, HistoryQueryValue> mergeMap = getOriginalMap(historyQueryData);
            int pageNum = historyQueryData.getTotalPage();
            for (int i = currentPage; i < pageNum; i++) {
                queryCondition.setPageIndex(i + 1);
                HistoryQueryData historyQueryDataTmp = sendRequest(RestConstant.RESTFUL_METHOD_POST, url,
                    JSON.toJSONString(queryCondition), options);
                if (historyQueryDataTmp == null) {
                    continue;
                }
                historyQueryDataMerge(historyQueryData, historyQueryDataTmp, mergeMap);
            }
            long endTime = System.currentTimeMillis();
            LOGGER.debug("[PerformanceClient]getHistoryDataFormPerformance,cost{}ms.", endTime - startTime);
            LOGGER.debug("[PerformanceClient]getHistoryDataFormPerformance,result={}", result);
            return result;
        } catch (ServiceException | JsonProcessingException e) {
            LOGGER.error("[PerformanceClient] get historydata error, moType={}, measUnitKey={}.",
                queryCondition.getMoType(), queryCondition.getMeasUnitKey(), e);
        }
        return result;
    }

    private void historyQueryDataMerge(HistoryQueryData allData, HistoryQueryData pageData,
        Map<String, HistoryQueryValue> mergeMap) {
        // 性能接口改成了分页返回指标数据，需要把指标数据整合起来
        for (HistoryQueryValue data : pageData.getResultData()) {
            String key = data.getDn() + data.getIndexKey() + data.getDisplayValue();
            if (mergeMap.containsKey(key)) {
                mergeMap.get(key).getIndexValues().addAll(data.getIndexValues());
            } else {
                mergeMap.put(key, data);
                allData.getResultData().add(data);
            }
        }
    }

    private Map<String, HistoryQueryValue> getOriginalMap(HistoryQueryData firstData) {
        Map<String, HistoryQueryValue> mergeMap = new HashMap<>();
        for (HistoryQueryValue data : firstData.getResultData()) {
            String key = data.getDn() + data.getIndexKey() + data.getDisplayValue();
            mergeMap.put(key, data);
        }
        return mergeMap;
    }

    private HistoryQueryData sendRequest(String method, String url, String param, RestfulOptions restfulOptions)
        throws ServiceException, JsonProcessingException {
        RestfulParametes restfulParametes = new RestfulParametes();
        restfulParametes.setRawData(param);

        RestfulResponse response = RestUtil.sendRestRequest(method, url, restfulParametes, restfulOptions);
        if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
            LOGGER.error("[PerformanceClient] get historydata error,param = {}, response = {}", param,
                response == null ? "response is null" : JSON.toJSONString(response));
            return null;
        }
        PmResult<HistoryQueryData> pmResult = JSONObject.parseObject(response.getResponseContent(),
            new ResultTypeReference(), JSONReader.Feature.SupportSmartMatch);

        if (pmResult == null || pmResult.getResultCode() != PmResult.SUC) {
            LOGGER.error("[PerformanceClient] get historydata response status error, param = {}, pmResult = {}", param,
                pmResult);
            return null;
        }
        // 记录查到的数据点数
        if (LOGGER.isDebugEnabled()) {
            pmResult.getResult().getResultData().forEach(dataQuery -> {
                LOGGER.debug("[PerformanceClient] get historydata success, indexName = {}, dataSize = {}",
                    dataQuery.getIndexName(), dataQuery.getIndexValues().size());
            });
        }
        return pmResult.getResult();
    }

    public List<ThresholdStaticInfo> getIndicatorThresholdAlarm(List<ThresholdConditionForTopology> queryCondition) {
        LOGGER.info("[PerformanceClient] start to getIndicatorThresholdAlarm");
        try {
            long startTime = System.currentTimeMillis();
            RestfulParametes parameters = new RestfulParametes();
            parameters.setRawData(JSON.toJSONString(queryCondition));
            RestfulResponse response = null;
            response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, POST_RULE_FOR_TOPOLOGY, parameters,
                null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                return new ArrayList<>();
            }
            PmResult<List<ThresholdStaticInfo>> pmResult = JSONObject.parseObject(response.getResponseContent(),
                new ResultThresholdTypeReference(), JSONReader.Feature.SupportSmartMatch);
            List<ThresholdStaticInfo> thresholdResults = pmResult.getResult();
            if (thresholdResults.isEmpty()) {
                LOGGER.error("[getIndicatorThresholdAlarm] The result of query threshold is empty.");
                return new ArrayList<>();
            }
            long endTime = System.currentTimeMillis();
            LOGGER.info("[getIndicatorThresholdAlarm] cost {}", endTime - startTime);
            return thresholdResults;
        } catch (ServiceException e) {
            LOGGER.error("[getIndicatorThresholdAlarm] get indicator threshold alarm error.", e);
            return new ArrayList<>();
        }
    }

    static class ResultThresholdTypeReference extends TypeReference<PmResult<List<ThresholdStaticInfo>>> {
        /**
         * 构造方法
         */
        public ResultThresholdTypeReference() {

        }
    }
}
