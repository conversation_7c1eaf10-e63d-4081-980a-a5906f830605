/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentproxyconfigservice.business.altasnetworkconfig.task;

import com.huawei.i2000.dvaiagentproxyconfigservice.business.altasnetworkconfig.AtlasConfigException;
import com.huawei.i2000.dvaiagentproxyconfigservice.dao.AtlasNetConfigDao;
import com.huawei.i2000.dvaiagentproxyconfigservice.model.AtlasConfig;
import com.huawei.i2000.dvaiagentproxyconfigservice.model.enums.ExceptionEnum;
import com.huawei.i2000.dvaiagentproxyconfigservice.model.enums.RespEnum;

import lombok.Getter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 修改任务模板类
 *
 * <AUTHOR>
 * @since 2025-03-08
 */
public abstract class AtlasConfigTaskTemplate {
    private static final Logger LOGGER = LoggerFactory.getLogger(AtlasConfigTaskTemplate.class);

    /**
     * 任务执行结果
     */
    @Getter
    private RespEnum result;

    /**
     * 回滚任务执行结果
     */
    @Getter
    private RespEnum rollbackResult;

    /**
     * 任务名称
     */
    private final String taskName;

    /**
     * 下一个任务，没有时值为null
     */
    protected AtlasConfigTaskTemplate nextTask;

    /**
     * 前一个任务，没有时值为null
     */
    protected AtlasConfigTaskTemplate preTask;

    /**
     * 任务是否需要执行
     * true - 需要执行，false - 不需要执行
     */
    @Getter
    private boolean needRun;

    /**
     * 回滚任务是否需要执行
     * true - 需要执行回滚任务，false - 不需要执行回滚任务
     */
    @Getter
    private boolean rollbackNeedRun;

    /**
     * 修改前配置对象
     */
    protected AtlasConfig preConfig;

    /**
     * 当前配置对象
     */
    protected AtlasConfig curConfig;

    /**
     * 数据库操作对象
     */
    protected AtlasNetConfigDao atlasNetConfigDao;

    /**
     * 检查是否可以运行,需要执行和回滚操作都需要用这个判断，需要保证两者都能调用成功
     *
     * @param isRollback true 是回滚时判断是否要执行，false - 非回滚时
     * @return true - 可以运行， false - 不可以运行
     */
    public abstract boolean check(boolean isRollback);

    /**
     * 设置信息
     *
     * @param pre - 修改前配置对象
     * @param cur - 当前配置对象
     * @param dao - 数据库操作对象
     */
    public void setPropertyInfo(AtlasConfig pre, AtlasConfig cur, AtlasNetConfigDao dao) {
        this.preConfig = pre;
        this.curConfig = cur;
        this.atlasNetConfigDao = dao;
    }

    /**
     * 设置本任务的后续任务，并且将本任务设置为后续任务的前置任务
     *
     * @param task 后续任务
     * @return 返回后续任务
     */
    public AtlasConfigTaskTemplate setNextTask(AtlasConfigTaskTemplate task) {
        this.nextTask = task;
        this.nextTask.setPropertyInfo(this.preConfig, this.curConfig, this.atlasNetConfigDao);
        this.nextTask.preTask = this;
        return nextTask;
    }

    /**
     * 构造函数
     *
     * @param name 任务名称
     */
    public AtlasConfigTaskTemplate(String name) {
        this.taskName = name;
        this.result = RespEnum.FAIL;
        this.rollbackResult = RespEnum.ROLLBACK_FAIL;
        this.needRun = true;
        this.rollbackNeedRun = false;
    }

    /**
     * 任务处理, 成功正常结束， 失败则抛出异常
     *
     * @throws AtlasConfigException 操作异常
     */
    public abstract void taskHandle() throws AtlasConfigException;

    /**
     * 失败处理,对任务操作进行回滚，成功正常结束， 失败则抛出异常
     *
     * @throws AtlasConfigException 操作异常
     */
    public abstract void rollbackHandle() throws AtlasConfigException;

    /**
     * 执行任务
     *
     * @throws AtlasConfigException 异常信息
     */
    public void execute() throws AtlasConfigException {
        if (!check(false)) {
            LOGGER.warn("{}  does not need to be executed", taskName);
            needRun = false;
            nextTaskExecute();
            return;
        }

        try {
            LOGGER.warn("{}  start to run", taskName);
            needRun = true;
            taskHandle();
        } catch (AtlasConfigException e) {
            LOGGER.error("Atlas config taskHandle run fail, task name: {}, error msg: {}", taskName, e.getMessage());
            result = RespEnum.FAIL;
            throw e;
        } catch (Exception e) {
            LOGGER.error("Atlas config run fail, task name: {},error msg: {}", taskName, e.getMessage());
            result = RespEnum.FAIL;
            throw new AtlasConfigException(ExceptionEnum.INTERNAL_SERVER_ERROR_EXCEPTION);
        }
        result = RespEnum.SUCCESS;
        LOGGER.warn("{}  execution completed", taskName);
        nextTaskExecute();
    }

    /**
     * 执行回滚任务
     *
     * @throws AtlasConfigException 异常信息
     */
    public void rollback() throws AtlasConfigException {
        if (!check(true)) {
            LOGGER.warn("{}  does not need to be rollback", taskName);
            rollbackNeedRun = false;
            preTaskRollback();
            return;
        }

        try {
            LOGGER.warn("{}  start to rollback", taskName);
            rollbackNeedRun = true;
            rollbackHandle();
        } catch (AtlasConfigException e) {
            LOGGER.error("Atlas config taskHandle rollback fail, task name: {}, error msg: {}", taskName,
                e.getMessage());
            rollbackResult = RespEnum.ROLLBACK_FAIL;
            throw e;
        } catch (Exception e) {
            LOGGER.error("Atlas config rollback fail, task name: {}, error msg: {}", taskName, e.getMessage());
            rollbackResult = RespEnum.ROLLBACK_FAIL;
            throw new AtlasConfigException(ExceptionEnum.INTERNAL_SERVER_ERROR_EXCEPTION);
        }
        rollbackResult = RespEnum.SUCCESS;
        LOGGER.warn("{}  rollback completed", taskName);
        preTaskRollback();
    }

    private void nextTaskExecute() throws AtlasConfigException {
        if (this.nextTask != null) {
            LOGGER.warn("The next task is : {}", nextTask.taskName);
            this.nextTask.execute();
        } else {
            LOGGER.warn("All tasks have been completed");
        }
    }

    private void preTaskRollback() throws AtlasConfigException {
        if (this.preTask != null) {
            LOGGER.warn("The pre task is : {}", preTask.taskName);
            this.preTask.rollback();
        } else {
            LOGGER.warn("All tasks have been rollback");
        }
    }
}
