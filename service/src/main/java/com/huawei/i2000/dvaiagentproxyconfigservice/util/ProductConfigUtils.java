/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2025. All rights reserved.
 */

package com.huawei.i2000.dvaiagentproxyconfigservice.util;

import com.huawei.baize.security.cmd.ProcessInfo;
import com.huawei.baize.security.cmd.ProcessResult;
import com.huawei.baize.security.cmd.SecureCmdExecutor;
import com.huawei.baize.security.cmd.exception.CmdValidatorException;
import com.huawei.baize.security.misc.io.FileSecUtils;
import com.huawei.baize.web.base.ipaddress.AddressCheckUtils;
import com.huawei.bsp.deploy.util.DefaultEnvUtil;
import com.huawei.bsp.remoteservice.exception.ExceptionArgs;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.i2000.dvaiagentproxyconfigservice.constants.Constant;
import com.huawei.i2000.dvaiagentproxyconfigservice.constants.ErrorMessage;
import com.huawei.i2000.dvaiagentproxyconfigservice.model.IpConfig;
import com.huawei.i2000.dvaiagentproxyconfigservice.model.NodeIpPortConfig;
import com.huawei.i2000.dvaiagentproxyconfigservice.model.NodeTypeIpPortConfig;
import com.huawei.i2000.dvaiagentproxyconfigservice.model.PeerNetworkConfig;
import com.huawei.i2000.dvaiagentproxyconfigservice.model.enums.ExceptionEnum;
import com.huawei.i2000.dvaiagentproxyconfigservice.model.enums.InstallType;
import com.huawei.i2000.dvaiagentproxyconfigservice.model.enums.ProduceEnvType;
import com.huawei.i2000.dvaiagentproxyconfigservice.util.product.NodeList;
import com.huawei.i2000.dvaiagentproxyconfigservice.util.product.NodeManagement;
import com.huawei.i2000.dvaiagentproxyconfigservice.util.product.ProductHelper;

import com.alibaba.fastjson2.JSON;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 产品配置工具类
 *
 * <AUTHOR>
 * @since 2025-03-08
 */
@Component
public final class ProductConfigUtils {
    public static final String EXPORT_PRUDUCT_CMD1 = "mkdir -p /opt/oss/tmp/product";

    public static final String EXPORT_PRUDUCT_CMD2
        = "/opt/oss/manager/tools/resmgr/queryproduct.sh -pn %s -output %s -fixfilename %s";

    public static final String ADD_USAGE_CMD1 = "echo BER-INTERNAL > /home/<USER>/nicusage_manager.cfg ";

    public static final String ADD_USAGE_CMD2 = "echo BER-INTERNAL2 >> /home/<USER>/nicusage_manager.cfg";

    public static final String ADD_USAGE_CMD_LOCALHA = "echo LOCALHA >> /home/<USER>/nicusage_manager.cfg";

    public static final String ADD_USAGE_CMD_FLOATINGIP = "echo FLOATINGIP >> /home/<USER>/nicusage_manager.cfg";

    public static final String ADD_USAGE_CMD3
        = "bash /opt/oss/manager/tools/sysmt/addnicusage.sh -usage /home/<USER>/nicusage_manager.cfg";

    public static final String BER_TERNAL = "BER-INTERNAL";

    public static final String LOCALHA = "LOCALHA";

    public static final String FLOATINGIP = "FLOATINGIP";

    public static final String LVS = ":lvs";

    public static final String IS_DR = "isDR";

    public static final String QUERY_TASK_LIST_INTERFACE = "/rest/weavemgr/v1/weaves";

    private static final String NIC_USAGE_MANAGER_PATH = "/home/<USER>/nicusage_manager.cfg";

    private static final Logger LOGGER = LoggerFactory.getLogger(ProductConfigUtils.class);

    private final static String CONFIG_IP_SHELL_PATH = DefaultEnvUtil.getAppRoot() + File.separator + "etc"
        + File.separator + "script" + File.separator + "product" + File.separator + "post_expansion_deal_network.sh";

    public static final String CONFIG_IP_CMD = "bash " + CONFIG_IP_SHELL_PATH;

    private static final List<InstallType> HAS_BUSINESS_CONFIG_TYPE = Arrays.asList(InstallType.CARRIER_DISTRIBUTED_VM,
        InstallType.CARRIER_STANDALONE_VM);

    static final String BASE_PATH = File.separator + "opt" + File.separator + "oss";

    public static final String NODE_LIST_PATH = BASE_PATH + "/manager/etc/sysconf/nodelists.json";

    static final String PRODUCT_DIR = BASE_PATH + File.separator + "tmp" + File.separator + "product";

    static final String PRODUCT_PATH = PRODUCT_DIR + File.separator + "product.json";

    static long timeout = 300L;

    static {
        List<String> whiteList = new ArrayList<>();
        whiteList.add(EXPORT_PRUDUCT_CMD1);
        whiteList.add(EXPORT_PRUDUCT_CMD2);
        whiteList.add(ADD_USAGE_CMD1);
        whiteList.add(ADD_USAGE_CMD2);
        whiteList.add(ADD_USAGE_CMD_LOCALHA);
        whiteList.add(ADD_USAGE_CMD_FLOATINGIP);
        whiteList.add(ADD_USAGE_CMD3);
        whiteList.add(CONFIG_IP_CMD);

        try {
            SecureCmdExecutor.getInstance().register(whiteList);
        } catch (CmdValidatorException e) {
            LOGGER.error("register Cmd Exception: {}", e.getMessage());
        }

        exportAllProductFile();
    }

    private static boolean runCmd(String cmd, Object... params) {
        ProcessInfo<ProcessResult, ProcessResult> processInfo = null;
        try {
            processInfo = SecureCmdExecutor.getInstance().execute(timeout, TimeUnit.SECONDS, cmd, params);
            String result = processInfo.getStdOut().toString();
            LOGGER.warn("runCmd ok  exit={} err={} result={} cmd={}", processInfo.exitValue(),
                processInfo.getStdErr().toString(), result, cmd);
        } catch (Exception e) {
            LOGGER.error("runCmd error cmd={},err={}", cmd, e.getMessage());
            return false;
        }
        return processInfo != null && processInfo.exitValue() == 0;
    }

    /**
     * 先导出产品文件，再执行其他命令，执行完操作请删除outDir目录
     *
     * @param productName 产品实例名称
     * @param outDir 输出产品信息文件的绝对目录
     * @param isFixFileName 输出的产品信息文件名称是否包含产品实例名称
     * @return 执行是否成功
     */
    public static boolean exportProductFile(String productName, String outDir, String isFixFileName) {
        // 功能1
        runCmd(EXPORT_PRUDUCT_CMD1);
        return runCmd(EXPORT_PRUDUCT_CMD2, productName, outDir, isFixFileName);
    }

    public static boolean exportAllProductFile() {
        return exportAllProductFile(true);
    }

    public static boolean exportAllProductFile(boolean deleteOld) {
        // 功能1
        File file = FileSecUtils.getFile(PRODUCT_PATH);
        if (deleteOld && file.isFile()) {
            file.delete();
        }
        return exportProductFile("all", PRODUCT_DIR, "true");
    }

    /**
     * 获取产品环境类型
     *
     * @return 产品环境类型。如DV环境ProduceEnvType.DV，大模型环境ProduceEnvType.AI_AGENT
     * @throws ServiceException ex
     */
    public static ProduceEnvType getProduceEnvType() throws ServiceException {
        prepareProductFile();
        String envType = ProductHelper.getEnvironmentType(PRODUCT_PATH);
        if (envType.isEmpty()) {
            throwConfigExceptionOnFailed(ExceptionEnum.PRODUCT_JSON_FILE_ABNORMAL, false);
        }
        return ProduceEnvType.fromDescription(envType);
    }

    /**
     * 获取异地容灾信息
     *
     * @return 是否容灾场景
     * @throws ServiceException ex
     */
    public static boolean isDREnvironment() throws ServiceException {
        prepareProductFile();
        String drInfoStr = ProductHelper.getProductext(PRODUCT_PATH, IS_DR);
        if (StringUtils.isEmpty(drInfoStr)) {
            return true;
        }
        return !drInfoStr.trim().equalsIgnoreCase(Constant.FALSE_STRING);
    }

    private static void prepareProductFile() throws ServiceException {
        exportAllProductFile(false);
        File file = FileSecUtils.getFile(PRODUCT_PATH);
        if (!file.isFile()) {
            LOGGER.error("file not exist when getProduceEnvType");
            throwConfigExceptionOnFailed(ExceptionEnum.PRODUCT_JSON_FILE_ABNORMAL, false);
        }
    }

    /**
     * 获取产品实例名称
     *
     * @return 产品实例名称
     */
    public static String getProductName() {
        return ProductHelper.getProductName(PRODUCT_PATH);
    }

    /**
     * 获取产品安装类型
     *
     * @return 安装类型
     */
    public static InstallType getInstallType() {
        // 功能7：场景判断
        return InstallType.fromDescription(ProductHelper.getInstallType(NODE_LIST_PATH, PRODUCT_PATH));
    }

    /**
     * 判断是否区分业务面和管理面
     *
     * @return true表示区分
     */
    public static boolean hasBusinessConfig() {
        // 分布式VM和单机VM场景才有业务面和管理面，其它场景不区分
        InstallType installType = ProductConfigUtils.getInstallType();
        LOGGER.warn("getInstallType is {}", installType);
        return CollectionUtils.containsAny(HAS_BUSINESS_CONFIG_TYPE, installType);
    }

    /**
     * 判断是否区分业务面和管理面
     *
     * @param peerNetworkConfig ip配置
     * @return true表示区分
     */
    public static boolean hasBusinessConfig(PeerNetworkConfig peerNetworkConfig) {
        if (!StringUtils.isNoneEmpty(peerNetworkConfig.getBusinessIP(), peerNetworkConfig.getAdminIP())) {
            return false;
        }
        return !StringUtils.equals(peerNetworkConfig.getBusinessIP(), peerNetworkConfig.getAdminIP());
    }

    /**
     * 获取本端的命名空间
     * 从/opt/oss/manager/etc/sysconf/nodelists.json里面取assignedToTenancy的值
     *
     * @return 本端的命名空间
     */
    public static String getLocalNameSpace() {
        return ProductHelper.getAssignedToTenancy(NODE_LIST_PATH);
    }

    /**
     * 判断是否是DV侧
     *
     * @return true 表示DV侧
     */
    public static boolean isDV() {
        // 判断是DV侧还是大模型侧
        try {
            return ProduceEnvType.DV.equals(getProduceEnvType());
        } catch (ServiceException e) {
            LOGGER.error("check isDV error, {}", e.getMessage());
            return false;
        }
    }

    public static NodeList readNodeList() {
        try {
            String s = FileUtils.readFileToString(FileSecUtils.getFile(NODE_LIST_PATH), StandardCharsets.UTF_8);
            return JSON.parseObject(s, NodeList.class);
        } catch (IOException e) {
            LOGGER.error("readNodeList error");
            return null;
        }
    }

    private static void throwConfigExceptionOnFailed(ExceptionEnum exceptionEnum, boolean configResult)
        throws ServiceException {
        if (configResult) {
            return;
        }
        StringJoiner detail = new StringJoiner(" ");
        detail.add(ResourceBundleUtil.getMessage(exceptionEnum.getDetail()));
        Optional.ofNullable(NodeManagement.getRequestErrorMessage()).ifPresent(detail::add);
        ExceptionArgs args = new ExceptionArgs();
        args.setDescArgs(new String[] {ResourceBundleUtil.getMessage(ErrorMessage.INTERNAL_SERVER_ERROR)});
        args.setDetailArgs(new String[] {detail.toString()});
        throw new ServiceException(exceptionEnum.getId(), HttpStatus.INTERNAL_SERVER_ERROR_500, args);
    }

    public static List<IpConfig> queryIpAddresses() {
        String uuid = UUID.randomUUID().toString();
        LOGGER.warn("queryIpAddresses start: {}", uuid);
        ProductConfigUtils.exportAllProductFile(false);
        // 业务面：根据部署参数berconnectionusage（需要查询product.json）确定允许使用的网卡用途（没配置参数的话取默认网卡用途范围）
        String berconnectionusage = ProductHelper.getProductext(PRODUCT_PATH, "berconnectionusage");
        if (StringUtils.isEmpty(berconnectionusage)) {
            berconnectionusage
                = "maintenance,Northbound,NorthboundBaseIP,LOCALHA,DRHEARTBEAT,BER-INTERNAL,BER-INTERNAL2,access-external2,access-external,access,access2";
        }

        // 管理面：根据部署参数berconnectionmanagerusage（环境变量中获取）、berconnectionusage确定允许使用的网卡用途（没配置参数的话取默认网卡用途范围）
        String berconnectionmanagerusage = ProductHelper.getProductext(PRODUCT_PATH, "berconnectionmanagerusage");
        if (StringUtils.isEmpty(berconnectionmanagerusage)) {
            berconnectionmanagerusage
                = "maintenance,Northbound,NorthboundBaseIP,DRHEARTBEAT,BER-INTERNAL,LOCALHA,BER-INTERNAL,access,FLOATINGBASEeth3,access2,access-external,access-external2";
        }

        NodeList nodeList = readNodeList();
        if (nodeList == null) {
            return new ArrayList<>();
        }
        List<String> needNodes = findNodeIds(nodeList);
        List<String> businessUsages = Arrays.asList(StringUtils.split(berconnectionusage, ","));
        List<String> ompUsages = Arrays.asList(StringUtils.split(berconnectionmanagerusage, ","));
        // 过滤IP网口
        Map<String, Integer> ipTypes = new HashMap<>();
        Map<String, List<NodeList.IpAddress>> nodeType2IpAddressMap = getNodeType2IpAddressMap(nodeList, needNodes,
            ompUsages, businessUsages, ipTypes);

        // 模型转换
        List<IpConfig> ipConfigs = getIpConfigs(nodeType2IpAddressMap, nodeList, ipTypes);
        if (CollectionUtils.isNotEmpty(ipConfigs) && ipConfigs.size() == 1) {
            ipConfigs.get(0).setConfigType("admin");
        }
        LOGGER.warn("ipConfigs={}", ipConfigs);
        LOGGER.warn("queryIpAddresses end: {}", uuid);
        return ipConfigs;
    }

    public static List<String> findNodeIds(NodeList nodeList) {
        // 存在GW节点，则只找GW节点，否则找HIROBER
        List<String> needNodes = new ArrayList<>();
        CollectionUtils.addAll(needNodes, nodeList.getNodeList()
            .entrySet()
            .stream()
            .filter(x -> x.getValue()
                .getIpAddresses()
                .stream()
                .anyMatch(y -> CollectionUtils.containsAny(y.getUsage(), "LVS-Northbound")))
            .map(Map.Entry::getKey)
            .collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(needNodes)) {
            CollectionUtils.addAll(needNodes, NodeManagement.queryNodeIdByMicroService("HIROBERService"));
            LOGGER.warn("queryNodeIdByMicroService={}", needNodes);
        }
        LOGGER.warn("findNodeIds={}", needNodes);
        return needNodes;
    }

    public static List<IpConfig> getIpConfigs(Map<String, List<NodeList.IpAddress>> nodeType2IpAddressMap,
        NodeList nodeList, Map<String, Integer> ipTypes) {
        Map<String, List<NodeTypeIpPortConfig>> nodeType2IpPortConfigs = new HashMap<>();
        for (Map.Entry<String, List<NodeList.IpAddress>> entry : nodeType2IpAddressMap.entrySet()) {
            NodeTypeIpPortConfig nodeTypeIpPortConfig = new NodeTypeIpPortConfig();
            nodeTypeIpPortConfig.setNodeType(entry.getKey());
            Map<String, NodeIpPortConfig> tmpNodeIpPortConfigs = new HashMap<>();
            entry.getValue().forEach(x -> {
                NodeIpPortConfig portConfig = tmpNodeIpPortConfigs.getOrDefault(x.getIface(), new NodeIpPortConfig());
                portConfig.setPort(x.getIface());
                String ip = x.getIp();
                portConfig.setOuterIpUpdateEnable(2 == ipTypes.getOrDefault(ip, 0) && StringUtils.isEmpty(ip));
                if (AddressCheckUtils.checkIpv4(ip)) {
                    portConfig.setIp(ip);
                } else {
                    portConfig.setIpv6(ip);
                }
                tmpNodeIpPortConfigs.put(x.getIface(), portConfig);
            });
            nodeTypeIpPortConfig.setIpPortConfigs(new ArrayList<>(tmpNodeIpPortConfigs.values()));
            List<NodeTypeIpPortConfig> nodeTypeIpPortConfigs = nodeType2IpPortConfigs.getOrDefault(entry.getKey(),
                new ArrayList<>());
            nodeTypeIpPortConfigs.add(nodeTypeIpPortConfig);
            nodeType2IpPortConfigs.put(entry.getKey(), nodeTypeIpPortConfigs);
        }
        Map<String, String> nodeType2AdminMap = nodeList.getNodeList()
            .values()
            .stream()
            .collect(Collectors.toMap(x -> ProductHelper.matchNodeType(x.getHostname()),
                x -> StringUtils.containsIgnoreCase(x.getAssignedToTenancy(), "manager") ? "admin" : "business",
                (v1, v2) -> v1));
        Map<String, IpConfig> ipConfigMap = new HashMap<>();
        nodeType2IpPortConfigs.forEach((key, value) -> {
            String configType = nodeType2AdminMap.get(key);
            IpConfig ipConfig = ipConfigMap.getOrDefault(configType, new IpConfig());
            ipConfig.setConfigType(nodeType2AdminMap.get(key));
            List<NodeTypeIpPortConfig> configs = ipConfig.getNodeTypeIpPortConfigs();
            if (configs == null) {
                configs = new ArrayList<>();
            }
            configs.addAll(value);
            ipConfig.setNodeTypeIpPortConfigs(configs);
            ipConfigMap.put(configType, ipConfig);
        });
        List<IpConfig> ipConfigs = new ArrayList<>(ipConfigMap.values());
        ipConfigs.stream().flatMap(x -> x.getNodeTypeIpPortConfigs().stream()).forEach(x -> {
            if (StringUtils.equals("Management", x.getNodeType())) {
                x.setNodeType("OMP");
            }
        });
        return ipConfigs;
    }

    private static Map<String, List<NodeList.IpAddress>> getNodeType2IpAddressMap(NodeList nodeList,
        List<String> businessNeedNodes, List<String> ompUsages, List<String> businessUsages,
        Map<String, Integer> ipTypes) {
        // 按节点类型分组Node
        Map<String, List<NodeList.Node>> nodeMap = nodeList.getNodeList()
            .entrySet()
            .stream()
            .filter(x -> StringUtils.equalsAnyIgnoreCase(x.getValue().getAssignedToTenancy(), "manager")
                || businessNeedNodes.contains(x.getKey()))
            .map(Map.Entry::getValue)
            .collect(Collectors.groupingBy(x -> ProductHelper.matchNodeType(x.getHostname())));

        Map<String, List<NodeList.IpAddress>> nodeType2IpAddressMap = new HashMap<>();
        for (Map.Entry<String, List<NodeList.Node>> entry : nodeMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue()) || entry.getValue().get(0) == null) {
                continue;
            }
            boolean isOmp = StringUtils.equals(entry.getValue().get(0).getAssignedToTenancy(), "manager");
            List<NodeList.IpAddress> onenodeIpAddresses = entry.getValue().get(0).getIpAddresses();
            LOGGER.warn("onenodeIpAddresses= {}", onenodeIpAddresses);

            // 根据网卡用途范围，筛选可用平面
            List<String> availusages = isOmp ? ompUsages : businessUsages;
            LOGGER.warn("isOmp={},availusages={}", isOmp, availusages);
            List<NodeList.IpAddress> filterIpAddresses = onenodeIpAddresses.stream()
                .filter(x -> CollectionUtils.containsAny(x.getUsage(), availusages))
                .collect(Collectors.toList());
            LOGGER.warn("filterIpAddresses is {}", filterIpAddresses);

            List<String> filterFixIpIfaces = filterIpAddresses.stream()
                .map(x -> StringUtils.substringBefore(x.getIface(), ":"))
                .collect(Collectors.toList());
            LOGGER.warn("filterFixIpIfaces is {}", filterFixIpIfaces);
            if (CollectionUtils.isEmpty(filterIpAddresses)) {
                continue;
            }
            Integer type = 0;
            if (entry.getValue().size() == 1) {
                // 使用固定IP
                nodeType2IpAddressMap.put(entry.getKey(), filterIpAddresses);
                type = 1;
            } else {
                type = processMulitNode(entry, onenodeIpAddresses, filterFixIpIfaces, nodeType2IpAddressMap);
            }
            final Integer fType = type;
            nodeType2IpAddressMap.get(entry.getKey())
                .stream()
                .map(NodeList.IpAddress::getIp)
                .forEach(x -> ipTypes.put(x, fType));
        }
        return nodeType2IpAddressMap;
    }

    static Integer processMulitNode(Map.Entry<String, List<NodeList.Node>> entry,
        List<NodeList.IpAddress> onenodeIpAddresses, List<String> filterFixIpIfaces,
        Map<String, List<NodeList.IpAddress>> nodeType2IpAddressMap) {
        int type;
        // 使用收敛IP
        List<NodeList.IpAddress> ipAddresses = onenodeIpAddresses.stream()
            .filter(x -> StringUtils.contains(x.getIface(), LVS) && CollectionUtils.containsAny(filterFixIpIfaces,
                StringUtils.substringBefore(x.getIface(), LVS)))
            .collect(Collectors.toList());
        type = 3;
        if (CollectionUtils.isEmpty(ipAddresses)) {
            List<String> existedFloatingNices = onenodeIpAddresses.stream()
                .filter(
                    x -> CollectionUtils.containsAny(filterFixIpIfaces, StringUtils.substringBefore(x.getIface(), ":1"))
                        && StringUtils.contains(x.getIface(), ":1"))
                .map(NodeList.IpAddress::getIface)
                .collect(Collectors.toList());
            for (NodeList.IpAddress ipAddr : onenodeIpAddresses) {
                if (!CollectionUtils.containsAny(filterFixIpIfaces,
                    StringUtils.substringBefore(ipAddr.getIface(), ":1"))) {
                    continue;
                }
                // 修改浮动IP
                if (StringUtils.contains(ipAddr.getIface(), ":1")) {
                    ipAddresses.add(ipAddr);
                } else if (!existedFloatingNices.contains(ipAddr.getIface() + ":1")) {
                    // 新增浮动IP
                    ipAddr.setIface(ipAddr.getIface() + ":1");
                    ipAddr.setIp("");
                    ipAddresses.add(ipAddr);
                }
            }
            type = 2;
        }
        nodeType2IpAddressMap.put(entry.getKey(), ipAddresses);
        return type;
    }
}
