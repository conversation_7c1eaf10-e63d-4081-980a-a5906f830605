/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.cmp.ihealingwebsite.impl;

import static com.huawei.cmp.ihealing.constants.Constant.PAGE_SIZE;

import com.huawei.bsp.biz.audit.ann.AuditResult;
import com.huawei.bsp.biz.audit.ann.LogSeverity;
import com.huawei.bsp.commonlib.hofs.HofsException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.cmp.foundation.hofs.DVHofsUtil;
import com.huawei.cmp.foundation.service.exception.CMPException;
import com.huawei.cmp.foundation.service.log.I2kOperationLog;
import com.huawei.cmp.foundation.service.log.LogUtil;
import com.huawei.cmp.foundation.service.log.OperationLogMsg;
import com.huawei.cmp.foundation.service.util.RequestDispatchHelper;
import com.huawei.cmp.ihealing.delegate.DvIHealingFlowViewServiceDelegate;
import com.huawei.cmp.ihealing.in.api.exception.FlowExceptionCode;
import com.huawei.cmp.ihealing.in.api.flow.model.view.Event;
import com.huawei.cmp.ihealing.in.api.flow.model.view.EventParameter;
import com.huawei.cmp.ihealing.in.api.flow.model.view.FlowParameter;
import com.huawei.cmp.ihealing.in.api.flow.model.view.IhealingPmTreeNode;
import com.huawei.cmp.ihealing.utils.FlowOtherUtil;
import com.huawei.cmp.ihealing.utils.JsonUtils;
import com.huawei.cmp.ihealing.utils.SingleValueValidator;
import com.huawei.cmp.ihealingwebsite.constants.Constant;
import com.huawei.cmp.ihealingwebsite.constants.OperationLogConst;
import com.huawei.cmp.ihealingwebsite.delegate.DvIHealingFlowViewWebsiteDelegate;
import com.huawei.cmp.ihealingwebsite.in.api.FlowViewRsp;
import com.huawei.cmp.ihealingwebsite.in.api.atom.model.atom.QueryEmailGroupRequest;
import com.huawei.cmp.ihealingwebsite.in.api.atom.model.atom.QueryEmailGroupResponse;
import com.huawei.cmp.ihealingwebsite.in.api.atom.model.atom.QueryEmailGroupsRequest;
import com.huawei.cmp.ihealingwebsite.in.api.atom.model.atom.QueryEmailGroupsResponse;
import com.huawei.cmp.ihealingwebsite.in.api.atom.model.atom.QueryEmailRequest;
import com.huawei.cmp.ihealingwebsite.in.api.atom.model.atom.QueryEmailResponse;
import com.huawei.cmp.ihealingwebsite.in.api.atom.model.atom.QueryFlowNamesRequest;
import com.huawei.cmp.ihealingwebsite.in.api.atom.model.atom.QueryFlowNamesResponse;
import com.huawei.cmp.ihealingwebsite.in.api.atom.model.atom.QueryTopMoTypesRequest;
import com.huawei.cmp.ihealingwebsite.in.api.atom.model.atom.QueryTopMoTypesResponse;
import com.huawei.cmp.ihealingwebsite.in.api.flow.model.BatchFlowOperateCarrier;
import com.huawei.cmp.ihealingwebsite.in.api.flow.model.view.BasicInfo;
import com.huawei.cmp.ihealingwebsite.in.api.flow.model.view.FiredFlow;
import com.huawei.cmp.ihealingwebsite.in.api.flow.model.view.FlowAuthReqModel;
import com.huawei.cmp.ihealingwebsite.in.api.flow.model.view.FlowModel;
import com.huawei.cmp.ihealingwebsite.in.api.flow.model.view.PageQueryFlowResponse;
import com.huawei.cmp.ihealingwebsite.in.api.flow.model.view.PageQueryModel;
import com.huawei.cmp.ihealingwebsite.in.api.global.model.GlobalModel;
import com.huawei.cmp.ihealingwebsite.model.FileUploadConfig;
import com.huawei.cmp.ihealingwebsite.model.MeasUnitKeyReq;
import com.huawei.cmp.ihealingwebsite.model.MoMeasDnsReq;
import com.huawei.cmp.ihealingwebsite.model.QueryCategoriesReq;
import com.huawei.cmp.ihealingwebsite.model.QueryCategoriesRsp;
import com.huawei.cmp.ihealingwebsite.servlet.ImgOperation;
import com.huawei.cmp.ihealingwebsite.utils.IhealingServiceConfigUtil;

import com.fasterxml.jackson.core.type.TypeReference;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 流程操作
 *
 * @since 2020-11-30
 */
@Component
public class DvIHealingFlowViewWebsiteDelegateImpl implements DvIHealingFlowViewWebsiteDelegate {
    private static final OssLog LOGGER = OssLogFactory.getLog(DvIHealingFlowViewWebsiteDelegateImpl.class);

    private static final String OPERATION_LOG_OPERATION_CREATEFLOW
        = "dv.ihealing.DefaultIHealingFlowViewService.createFlow";

    private static final String OPERATION_LOG_OPERATION_CREATEFLOW_DETAIL
        = "dv.ihealing.DefaultIHealingFlowViewService.createFlow.detail";

    private static final String OPERATION_LOG_OPERATION_SUBMITFLOW
        = "dv.ihealing.DefaultIHealingFlowViewService.submitFlow";

    private static final String OPERATION_LOG_OPERATION_SUBMITFLOW_DETAIL
        = "dv.ihealing.DefaultIHealingFlowViewService.submitFlow.detail";

    private static final String OPERATION_LOG_OPERATION_MODIFYANDDEPLOYFLOW
        = "dv.ihealing.DefaultIHealingFlowViewService.modifyAndDeployFlow";

    private static final String OPERATION_LOG_OPERATION_MODIFYANDDEPLOYFLOW_DETAIL
        = "dv.ihealing.DefaultIHealingFlowViewService.modifyAndDeployFlow.detail";

    private static final String OPERATION_LOG_OPERATION_MODIFYFLOW
        = "dv.ihealing.DefaultIHealingFlowViewService.modifyFlow";

    private static final String OPERATION_LOG_OPERATION_MODIFYFLOW_DETAIL
        = "dv.ihealing.DefaultIHealingFlowViewService.modifyFlow.detail";

    private static final String OPERATION_LOG_OPERATION_DEPLOYFLOW
        = "dv.ihealing.DefaultIHealingFlowViewService.deployFlow";

    private static final String OPERATION_LOG_OPERATION_DEPLOYFLOW_DETAIL
        = "dv.ihealing.DefaultIHealingFlowViewService.deployFlow.detail";

    private static final String OPERATION_LOG_OPERATION_UNDEPLOYFLOW
        = "dv.ihealing.DefaultIHealingFlowViewService.undeployFlow";

    private static final String OPERATION_LOG_OPERATION_UNDEPLOYFLOW_DETAIL
        = "dv.ihealing.DefaultIHealingFlowViewService.undeployFlow.detail";

    private static final String OPERATION_LOG_OPERATION_DELETEFLOW
        = "dv.ihealing.DefaultIHealingFlowViewService.deleteFlow";

    private static final String OPERATION_LOG_OPERATION_DELETEFLOW_DETAIL
        = "dv.ihealing.DefaultIHealingFlowViewService.deleteFlow.detail";

    private static final String OPERATION_LOG_SOURCE = "dvIhealingService";

    private static final String LOCAL_PATH = Boolean.parseBoolean(System.getenv("CONTAINER"))
        ? "/opt/share/images/healing/cards/"
        : "/opt/oss/SOP/apps/DVIhealingWebsite/webapps/dvihealingwebsite/images/healing/cards/";

    private static final String OPERATION_LOG_OPERATION_COPYIMGFILE
        = "dv.ihealing.DefaultIHealingFlowViewService.copyImgFile";

    private static final String OPERATION_LOG_OPERATION_COPYIMGFILE_DETAIL
        = "dv.ihealing.DefaultIHealingFlowViewService.copyImgFile.detail";

    private static final String OPERATION_LOG_OPERATION_DELETEIMGFILE
        = "dv.ihealing.DefaultIHealingFlowViewService.deleteImgFile";

    private static final String OPERATION_LOG_OPERATION_DELETEIMGFILE_DETAIL
        = "dv.ihealing.DefaultIHealingFlowViewService.deleteImgFile.detail";

    private static final String OPERATION_LOG_OPERATION_UPDATEFLOWPARAS
        = "dv.ihealing.DefaultIHealingGlobalViewService.updateFlowParas";

    private static final String OPERATION_LOG_OPERATION_UPDATEFLOWPARAS_DETAIL
        = "dv.ihealing.DefaultIHealingGlobalViewService.updateFlowParas.detail";

    private static final int IMG_UUID_LENGTH = 37;

    @Autowired
    private DvIHealingFlowViewServiceDelegate dvIHealingFlowViewServiceDelegate;

    /**
     * Create flow string
     *
     * @param context context
     * @param flowModel flow model
     * @return the string
     * @throws ServiceException service exception
     */
    @Override
    public String createFlow(HttpContext context, FlowModel flowModel) throws ServiceException {
        String createFlow = null;
        String flowName = flowModel.getBasicInfo().getName();
        try {
            createFlow = RequestDispatchHelper.dispatch(context, flowModel, new StringTypeReference(), String.class);
        } catch (ServiceException e) {
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_CREATEFLOW, flowName, LogSeverity.MINOR,
                OperationLogConst.OPERATION_LOG_SOURCE, AuditResult.FAILURE, OPERATION_LOG_OPERATION_CREATEFLOW_DETAIL,
                new Object[] {flowName});
            throw e;
        }
        I2kOperationLog.info(context, OPERATION_LOG_OPERATION_CREATEFLOW, createFlow + ":" + flowName,
            LogSeverity.MINOR, OPERATION_LOG_SOURCE, AuditResult.SUCCESSFUL, OPERATION_LOG_OPERATION_CREATEFLOW_DETAIL,
            new Object[] {createFlow + ":" + flowName});
        return createFlow;
    }

    /**
     * Delete flow *
     *
     * @param context context
     * @param request request
     * @throws ServiceException service exception
     */
    @Override
    public void deleteFlow(HttpContext context, BatchFlowOperateCarrier request) throws ServiceException {
        FlowViewRsp response;
        String flowTaskName = null;
        try {
            response = RequestDispatchHelper.dispatch(context, request, new FlowViewRspTypeReference(),
                FlowViewRsp.class);
            String targetObj = response.getTargetObj();
            if (StringUtils.isNotEmpty(targetObj)) {
                flowTaskName = targetObj.substring(targetObj.lastIndexOf(Constant.AND) + 1);
            }
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_DELETEFLOW,
                request.getFlowIds().get(0) + ":" + flowTaskName, LogSeverity.RISK, OPERATION_LOG_SOURCE,
                AuditResult.SUCCESSFUL, OPERATION_LOG_OPERATION_DELETEFLOW_DETAIL,
                new Object[] {request.getFlowIds().get(0) + ":" + flowTaskName});
        } catch (ServiceException e) {
            LOGGER.error("delete flow failed", e.getMessage());
            flowTaskName = request.getFlowIds().get(0);
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_DELETEFLOW, flowTaskName, LogSeverity.RISK,
                OPERATION_LOG_SOURCE, AuditResult.FAILURE, OPERATION_LOG_OPERATION_DELETEFLOW_DETAIL,
                new Object[] {flowTaskName});
            throw e;
        }
    }

    /**
     * Deploy flow map
     *
     * @param context context
     * @param request request
     * @return the map
     * @throws ServiceException service exception
     */
    @Override
    public Map<String, String> deployFlow(HttpContext context, BatchFlowOperateCarrier request)
        throws ServiceException {
        Map<String, String> stringMap;
        try {
            stringMap = RequestDispatchHelper.dispatch(context, request, new MapTypeReference(), null);
        } catch (ServiceException e) {
            OperationLogMsg logMsg = LogUtil.getLogFromMsg(e.getExceptionArgs().getDetailArgs()[0]);
            if (logMsg != null) {
                I2kOperationLog.info(context, OPERATION_LOG_OPERATION_DEPLOYFLOW, logMsg.getTargetObj(),
                    LogSeverity.MINOR, OPERATION_LOG_SOURCE, AuditResult.FAILURE,
                    OPERATION_LOG_OPERATION_DEPLOYFLOW_DETAIL, new Object[] {logMsg.getTargetObj()});
            }
            throw e;
        }
        String flowName = stringMap.get(request.getFlowIds().get(0) + "name");
        I2kOperationLog.info(context, OPERATION_LOG_OPERATION_DEPLOYFLOW, request.getFlowIds().get(0) + ":" + flowName,
            LogSeverity.MINOR, OPERATION_LOG_SOURCE, AuditResult.SUCCESSFUL, OPERATION_LOG_OPERATION_DEPLOYFLOW_DETAIL,
            new Object[] {request.getFlowIds().get(0) + ":" + flowName});
        return stringMap;
    }

    /**
     * Modify and deploy flow *
     *
     * @param context context
     * @param flowModel flow model
     * @throws ServiceException service exception
     */
    @Override
    public void modifyAndDeployFlow(HttpContext context, FlowModel flowModel) throws ServiceException {
        String flowName = flowModel.getBasicInfo().getName();
        try {
            RequestDispatchHelper.dispatch(context, flowModel, new VoidTypeReference(), Void.class);
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_MODIFYANDDEPLOYFLOW, flowName, LogSeverity.MINOR,
                OPERATION_LOG_SOURCE, AuditResult.SUCCESSFUL, OPERATION_LOG_OPERATION_MODIFYANDDEPLOYFLOW_DETAIL,
                new Object[] {flowName});
        } catch (ServiceException e) {
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_MODIFYANDDEPLOYFLOW, flowName, LogSeverity.MINOR,
                OperationLogConst.OPERATION_LOG_SOURCE, AuditResult.FAILURE,
                OPERATION_LOG_OPERATION_MODIFYANDDEPLOYFLOW_DETAIL,
                new Object[] {flowModel.getBasicInfo().getFlowId() + ":" + flowName});
            throw e;
        }
    }

    /**
     * Modify and deploy *
     *
     * @param context context
     * @param flowAuthReqModel flow auth req model
     * @throws ServiceException service exception
     */
    @Override
    public void modifyAndDeploy(HttpContext context, FlowAuthReqModel flowAuthReqModel) throws ServiceException {
        String flowName = flowAuthReqModel.getFlowModel().getBasicInfo().getName();
        try {
            RequestDispatchHelper.dispatch(context, flowAuthReqModel.getFlowModel(), new VoidTypeReference(),
                Void.class);
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_MODIFYANDDEPLOYFLOW, flowName, LogSeverity.MINOR,
                OPERATION_LOG_SOURCE, AuditResult.SUCCESSFUL, OPERATION_LOG_OPERATION_MODIFYANDDEPLOYFLOW_DETAIL,
                new Object[] {flowName});
        } catch (ServiceException e) {
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_MODIFYANDDEPLOYFLOW, flowName, LogSeverity.MINOR,
                OperationLogConst.OPERATION_LOG_SOURCE, AuditResult.FAILURE,
                OPERATION_LOG_OPERATION_MODIFYANDDEPLOYFLOW_DETAIL, new Object[] {flowName});
            throw e;
        }
    }

    /**
     * Modify flow *
     *
     * @param context context
     * @param flowModel flow model
     * @throws ServiceException service exception
     */
    @Override
    public void modifyFlow(HttpContext context, FlowModel flowModel) throws ServiceException {
        String flowName = flowModel.getBasicInfo().getName();
        try {
            RequestDispatchHelper.dispatch(context, flowModel, new VoidTypeReference(), Void.class);
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_MODIFYFLOW,
                flowModel.getBasicInfo().getFlowId() + ":" + flowName, LogSeverity.MINOR, OPERATION_LOG_SOURCE,
                AuditResult.SUCCESSFUL, OPERATION_LOG_OPERATION_MODIFYFLOW_DETAIL,
                new Object[] {flowModel.getBasicInfo().getFlowId() + ":" + flowName});
        } catch (ServiceException e) {
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_MODIFYFLOW,
                flowModel.getBasicInfo().getFlowId() + ":" + flowName, LogSeverity.MINOR,
                OperationLogConst.OPERATION_LOG_SOURCE, AuditResult.FAILURE, OPERATION_LOG_OPERATION_MODIFYFLOW_DETAIL,
                new Object[] {flowModel.getBasicInfo().getFlowId() + ":" + flowName});
            throw e;
        }
    }

    /**
     * Query categories query categories rsp
     *
     * @param context context
     * @param request request
     * @return the query categories rsp
     * @throws ServiceException service exception
     */
    @Override
    public QueryCategoriesRsp queryCategories(HttpContext context, QueryCategoriesReq request) throws ServiceException {
        return RequestDispatchHelper.dispatch(context, request, new QueryCategoriesRspTypeReference(),
            QueryCategoriesRsp.class);
    }

    /**
     * Query flow by page page query flow response
     *
     * @param context context
     * @param request request
     * @return the page query flow response
     * @throws ServiceException service exception
     */
    @Override
    public PageQueryFlowResponse queryFlowByPage(HttpContext context, PageQueryModel request) throws ServiceException {

        if (!PAGE_SIZE.contains(request.getLimit())) {
            LOGGER.error("page size invalid");
            throw new CMPException(FlowExceptionCode.PARAMS_INVALID);
        }
        PageQueryFlowResponse flowResponse = RequestDispatchHelper.dispatch(context, request,
            new PageQueryFlowResponseTypeReference(), PageQueryFlowResponse.class);
        List<BasicInfo> basicInfos = flowResponse.getFlows()
            .stream()
            .map(FiredFlow::getBasicInfo)
            .collect(Collectors.toList());
        for (BasicInfo basicInfo : basicInfos) {
            rushImg(basicInfo.getImgPath());
        }
        return flowResponse;
    }

    /**
     * Rush img *
     *
     * @param imgName img name
     */
    private void rushImg(String imgName) {
        if (imgName == null || imgName.equals("null")) {
            return;
        } else {
            String shareImgPath = Constant.UPLOAD_SHARE + File.separator + imgName;
            String localImgPath = LOCAL_PATH + imgName;
            File file = FileUtils.getFile(localImgPath);
            if (!file.exists()) {
                try {
                    DVHofsUtil.pullFile(shareImgPath, localImgPath);
                } catch (HofsException e) {
                    LOGGER.error("rushImg failed,{}", e.getMessage());
                }
            }
        }
    }

    /**
     * Query flow detail flow model
     *
     * @param context context
     * @param flowId flow id
     * @return the flow model
     * @throws ServiceException service exception
     */
    @Override
    public FlowModel queryFlowDetail(HttpContext context, String flowId) throws ServiceException {
        com.huawei.cmp.ihealing.in.api.flow.model.view.FlowModel flowModel
            = dvIHealingFlowViewServiceDelegate.queryFlowDetail(context, flowId);
        cleanPValue(flowModel);
        FlowOtherUtil.validatePermission(context, flowModel, false);
        return JsonUtils.fromJson(JsonUtils.toJson(flowModel), FlowModel.class);
    }

    private static void cleanPValue(com.huawei.cmp.ihealing.in.api.flow.model.view.FlowModel flowModel) {
        if (flowModel.getEvents() != null) {
            for (Event event : flowModel.getEvents()) {
                List<FlowParameter> flowParameterList = Optional.ofNullable(event.getParameter())
                    .map(EventParameter::getFlowParameters)
                    .orElse(new ArrayList<>());
                for (FlowParameter flowParameter : flowParameterList) {
                    if (flowParameter.getFlowParamType() == FlowParameter.FlowParameterType.PVALUETYPE) {
                        flowParameter.setValue(StringUtils.EMPTY);
                    }
                }
            }
        }
    }

    /**
     * Query flow names by solution query flowNames response
     *
     * @param context context
     * @param groupName group name
     * @return the query flowNames response
     * @throws ServiceException service exception
     */
    @Override
    public QueryFlowNamesResponse queryFlowNamesBySolution(HttpContext context, String groupName)
        throws ServiceException {
        return RequestDispatchHelper.dispatch(context, groupName, new QueryFlowNamesResponseTypeReference(),
            QueryFlowNamesResponse.class);
    }

    @Override
    public Map<String, Map<String, String>> queryDnListByMoTypeMeasUnitKey(HttpContext context, MoMeasDnsReq req)
        throws ServiceException {
        return RequestDispatchHelper.dispatch(context, req, new MesaQueryMapTypeReference(), null);
    }

    @Override
    public IhealingPmTreeNode queryMeasUnitTypeTree(HttpContext context, MeasUnitKeyReq measUnitKeyReq)
        throws ServiceException {
        return RequestDispatchHelper.dispatch(context, measUnitKeyReq, new IhealingPmTreeNodeTypeReference(), null);
    }

    @Override
    public IhealingPmTreeNode queryPmTaskMoTypeTree(HttpContext context, String moTypeList) throws ServiceException {
        return RequestDispatchHelper.dispatch(context, moTypeList, new IhealingPmTreeNodeTypeReference(), null);
    }

    /**
     * Query flow solution query solutions rsp
     *
     * @param context context
     * @param request request
     * @return the query solutions rsp
     * @throws ServiceException service exception
     */
    @Override
    public QueryEmailResponse queryEmailUsers(HttpContext context, QueryEmailRequest request) throws ServiceException {
        return RequestDispatchHelper.dispatch(context, request, new QueryEmailResponseTypeReference(),
            QueryEmailResponse.class);
    }

    @Override
    public QueryEmailGroupResponse queryEmailGroup(HttpContext context, QueryEmailGroupRequest request)
        throws ServiceException {
        return RequestDispatchHelper.dispatch(context, request, new QueryEmailGroupResponseTypeReference(),
            QueryEmailGroupResponse.class);
    }

    @Override
    public QueryEmailGroupsResponse queryEmailGroups(HttpContext httpContext,
        QueryEmailGroupsRequest queryEmailGroupsRequest) throws ServiceException {
        return RequestDispatchHelper.dispatch(httpContext, queryEmailGroupsRequest,
            new QueryEmailGroupsResponseTypeReference(), QueryEmailGroupsResponse.class);
    }

    /**
     * Query flowNames query flowNames response
     *
     * @param context context
     * @param request request
     * @return the query flowNames response
     * @throws ServiceException service exception
     */
    @Override
    public QueryFlowNamesResponse queryFlowNames(HttpContext context, QueryFlowNamesRequest request)
        throws ServiceException {
        return RequestDispatchHelper.dispatch(context, request, new QueryFlowNamesResponseTypeReference(),
            QueryFlowNamesResponse.class);
    }

    /**
     * 查询所有待审核的流程和原子总数量
     *
     * @param context context
     * @return 待审核的流程和原子总数量
     * @throws ServiceException service exception
     */
    @Override
    public String queryReviewedCount(HttpContext context) throws ServiceException {
        return RequestDispatchHelper.dispatch(context, null, new StringTypeReference(), String.class);
    }

    /**
     * Query top mo types query top mo types response
     *
     * @param context context
     * @param request request
     * @return the query top mo types response
     * @throws ServiceException service exception
     */
    @Override
    public QueryTopMoTypesResponse queryTopMoTypes(HttpContext context, QueryTopMoTypesRequest request)
        throws ServiceException {
        return RequestDispatchHelper.dispatch(context, request, new QueryTopMoTypesResponseTypeReference(),
            QueryTopMoTypesResponse.class);
    }

    /**
     * Query top mo types flows query top mo types response
     *
     * @param context context
     * @param request request
     * @return the query top mo types response
     * @throws ServiceException service exception
     */
    @Override
    public QueryTopMoTypesResponse queryTopMoTypesFlows(HttpContext context, QueryTopMoTypesRequest request)
        throws ServiceException {
        return RequestDispatchHelper.dispatch(context, request, new QueryTopMoTypesResponseTypeReference(),
            QueryTopMoTypesResponse.class);
    }

    /**
     * Query upload file config file upload config
     *
     * @param context context
     * @return the file upload config
     * @throws ServiceException service exception
     */
    @Override
    public FileUploadConfig queryUploadFileConfig(HttpContext context) throws ServiceException {
        String maxSize = IhealingServiceConfigUtil.getProperty("flow.upload.file.maxsize", "30");
        String typeStr = IhealingServiceConfigUtil.getProperty("flow.upload.file.type", "txt,properties,csv,log,zip");
        final FileUploadConfig config = new FileUploadConfig();
        config.setMaxSizeStr(maxSize);
        config.setTypeStr(typeStr);
        return config;
    }

    /**
     * Undeploy flow *
     *
     * @param context context
     * @param request request
     * @throws ServiceException service exception
     */
    @Override
    public void undeployFlow(HttpContext context, BatchFlowOperateCarrier request) throws ServiceException {
        FlowViewRsp response = new FlowViewRsp();
        AuditResult auditResult = AuditResult.SUCCESSFUL;
        try {
            response = RequestDispatchHelper.dispatch(context, request, new FlowViewRspTypeReference(),
                FlowViewRsp.class);
        } catch (ServiceException e) {
            auditResult = AuditResult.FAILURE;
            throw e;
        } finally {
            String flowName = null;
            String targetObj = response.getTargetObj();
            if (StringUtils.isNotEmpty(targetObj)) {
                flowName = targetObj.substring(targetObj.lastIndexOf(Constant.AND) + 1);
            }
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_UNDEPLOYFLOW,
                request.getFlowIds().get(0) + ":" + flowName, LogSeverity.MINOR, OPERATION_LOG_SOURCE, auditResult,
                OPERATION_LOG_OPERATION_UNDEPLOYFLOW_DETAIL,
                new Object[] {request.getFlowIds().get(0) + ":" + flowName});
        }
    }

    /**
     * Update flow paras *
     *
     * @param context context
     * @param globalModel global model
     * @throws ServiceException service exception
     */
    @Override
    public void updateFlowParas(HttpContext context, GlobalModel globalModel) throws ServiceException {
        AuditResult auditResult = AuditResult.SUCCESSFUL;
        try {
            RequestDispatchHelper.dispatch(context, globalModel, new VoidTypeReference(), Void.class);
        } catch (ServiceException e) {
            auditResult = AuditResult.FAILURE;
            throw e;
        } finally {
            String globalParamId = Optional.ofNullable(globalModel).map(GlobalModel::getGlobalParamId).orElse("");
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_UPDATEFLOWPARAS, globalParamId, LogSeverity.MINOR,
                OPERATION_LOG_SOURCE, auditResult, OPERATION_LOG_OPERATION_UPDATEFLOWPARAS_DETAIL,
                new Object[] {globalParamId});
        }
    }

    /**
     * Copy img file map
     *
     * @param context context
     * @param flowId img path
     * @return the map
     * @throws ServiceException service exception
     */
    @Override
    public FlowModel submitFlow(HttpContext context, String flowId) throws ServiceException {
        SingleValueValidator.hasIllegalChar(flowId);
        FlowModel flowModel;
        try {
            flowModel = RequestDispatchHelper.dispatch(context, flowId, new FlowModelTypeReference(), FlowModel.class);
        } catch (ServiceException e) {
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_SUBMITFLOW, flowId, LogSeverity.MINOR,
                OperationLogConst.OPERATION_LOG_SOURCE, AuditResult.FAILURE, OPERATION_LOG_OPERATION_SUBMITFLOW_DETAIL,
                new Object[] {flowId});
            throw e;
        }
        String flowName = "";
        if (flowModel != null) {
            flowName = flowModel.getBasicInfo().getName();
        }
        I2kOperationLog.info(context, OPERATION_LOG_OPERATION_SUBMITFLOW, flowId + ":" + flowName, LogSeverity.MINOR,
            OPERATION_LOG_SOURCE, AuditResult.SUCCESSFUL, OPERATION_LOG_OPERATION_SUBMITFLOW_DETAIL,
            new Object[] {flowId + ":" + flowName});
        return flowModel;
    }

    @Override
    public Map<String, String> copyImgFile(HttpContext context, String imgPath) throws ServiceException {
        Map<String, String> imgMap;
        try {
            SingleValueValidator.validateImagePath(imgPath);
            ImgOperation.checkImageLimit();
            imgMap = RequestDispatchHelper.dispatch(context, imgPath, new MapTypeReference(), null);
        } catch (ServiceException e) {
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_COPYIMGFILE, imgPath, LogSeverity.MINOR,
                OperationLogConst.OPERATION_LOG_SOURCE, AuditResult.FAILURE, OPERATION_LOG_OPERATION_COPYIMGFILE_DETAIL,
                new Object[] {imgPath});
            throw e;
        }
        I2kOperationLog.info(context, OPERATION_LOG_OPERATION_COPYIMGFILE, imgPath, LogSeverity.MINOR,
            OPERATION_LOG_SOURCE, AuditResult.SUCCESSFUL, OPERATION_LOG_OPERATION_COPYIMGFILE_DETAIL,
            new Object[] {imgPath});
        return imgMap;
    }

    /**
     * Delete img file *
     *
     * @param context context
     * @param imgPath img path
     * @throws ServiceException service exception
     */
    @Override
    public void deleteImgFile(HttpContext context, String imgPath) throws ServiceException {
        String imgName = "";
        try {
            SingleValueValidator.validateImagePath(imgPath);
            RequestDispatchHelper.dispatch(context, imgPath, new VoidTypeReference(), Void.class);
        } catch (ServiceException e) {
            imgName = imgPath.length() > IMG_UUID_LENGTH ? imgPath.substring(IMG_UUID_LENGTH) : imgPath;
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_DELETEIMGFILE, imgName, LogSeverity.MINOR,
                OperationLogConst.OPERATION_LOG_SOURCE, AuditResult.FAILURE,
                OPERATION_LOG_OPERATION_DELETEIMGFILE_DETAIL, new Object[] {imgName});
            throw e;
        }
        I2kOperationLog.info(context, OPERATION_LOG_OPERATION_DELETEIMGFILE, imgName, LogSeverity.MINOR,
            OPERATION_LOG_SOURCE, AuditResult.SUCCESSFUL, OPERATION_LOG_OPERATION_DELETEIMGFILE_DETAIL,
            new Object[] {imgName});
    }

    private static class StringTypeReference extends TypeReference<String> {}

    private static class QueryCategoriesRspTypeReference extends TypeReference<QueryCategoriesRsp> {}

    private static class PageQueryFlowResponseTypeReference extends TypeReference<PageQueryFlowResponse> {}

    private static class FlowModelTypeReference extends TypeReference<FlowModel> {}

    private static class QueryFlowNamesResponseTypeReference extends TypeReference<QueryFlowNamesResponse> {}

    private static class QueryEmailResponseTypeReference extends TypeReference<QueryEmailResponse> {}

    private static class QueryEmailGroupResponseTypeReference extends TypeReference<QueryEmailGroupResponse> {}

    private static class QueryEmailGroupsResponseTypeReference extends TypeReference<QueryEmailGroupsResponse> {}

    private static class FlowViewRspTypeReference extends TypeReference<FlowViewRsp> {}

    private static class QueryTopMoTypesResponseTypeReference extends TypeReference<QueryTopMoTypesResponse> {}

    private static class VoidTypeReference extends TypeReference<Void> {}

    private static class MapTypeReference extends TypeReference<Map<String, String>> {}

    private static class MesaQueryMapTypeReference extends TypeReference<Map<String, Map<String, String>>> {}

    private static class IhealingPmTreeNodeTypeReference extends TypeReference<IhealingPmTreeNode> {}
}

