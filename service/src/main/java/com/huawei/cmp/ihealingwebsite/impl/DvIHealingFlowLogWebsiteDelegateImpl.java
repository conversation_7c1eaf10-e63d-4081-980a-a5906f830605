/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.cmp.ihealingwebsite.impl;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.cmp.foundation.validation.ValidateException;
import com.huawei.cmp.foundation.validation.ValidateUtils;
import com.huawei.cmp.ihealing.delegate.DvIHealingFlowLogServiceDelegate;
import com.huawei.cmp.ihealing.in.api.flow.model.view.FlowModel;
import com.huawei.cmp.ihealing.in.api.flow.service.IHealingFlowViewService;
import com.huawei.cmp.ihealing.utils.FlowOtherUtil;
import com.huawei.cmp.ihealing.utils.JsonUtils;
import com.huawei.cmp.ihealingwebsite.delegate.DvIHealingFlowLogWebsiteDelegate;
import com.huawei.cmp.ihealingwebsite.in.api.flow.model.log.QueryHealingTaskLogsReq;
import com.huawei.cmp.ihealingwebsite.in.api.flow.model.log.QueryHealingTaskLogsResp;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 流程日志
 *
 * @since 2020-11-30
 */
@Component
public class DvIHealingFlowLogWebsiteDelegateImpl implements DvIHealingFlowLogWebsiteDelegate {

    private static final OssLog LOGGER = OssLogFactory.getLogger(DvIHealingFlowLogWebsiteDelegateImpl.class);

    @Autowired
    private DvIHealingFlowLogServiceDelegate dvIHealingFlowLogServiceDelegate;

    @Autowired
    private IHealingFlowViewService iHealingFlowViewService;

    /**
     * Query healing task logs query healing task logs resp
     *
     * @param context context
     * @param req req
     * @return the query healing task logs resp
     * @throws ServiceException service exception
     */
    @Override
    public QueryHealingTaskLogsResp queryHealingTaskLogs(HttpContext context, QueryHealingTaskLogsReq req)
        throws ServiceException {
        try {
            ValidateUtils.validate(req);
        } catch (ValidateException e) {
            LOGGER.error("queryHealingTaskLogs request ", req.getProcessInstanceId());
            throw new ServiceException("validation failed, {}", e.getMessage());
        }
        String processInstanceId = req.getProcessInstanceId();
        FlowModel flowModel = iHealingFlowViewService.queryFlowByFlowInsId(processInstanceId);
        if (flowModel == null) {
            LOGGER.warn("processInstanceId {} flow not exist", processInstanceId);
            return new QueryHealingTaskLogsResp();
        }
        FlowOtherUtil.validatePermission(context, flowModel, false);
        com.huawei.cmp.ihealing.in.api.flow.model.log.QueryHealingTaskLogsReq request
            = new com.huawei.cmp.ihealing.in.api.flow.model.log.QueryHealingTaskLogsReq();
        request.setLimit(req.getLimit());
        request.setProcessInstanceId(processInstanceId);
        request.setOffset(req.getOffset());
        com.huawei.cmp.ihealing.in.api.flow.model.log.QueryHealingTaskLogsResp queryHealingTaskLogsResp
            = dvIHealingFlowLogServiceDelegate.queryHealingTaskLogs(context, request);
        if (queryHealingTaskLogsResp == null) {
            return new QueryHealingTaskLogsResp();
        }
        return JsonUtils.fromJson(JsonUtils.toJson(queryHealingTaskLogsResp), QueryHealingTaskLogsResp.class);
    }

}

