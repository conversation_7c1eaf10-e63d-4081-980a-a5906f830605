/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.cmp.controller.service.regularclean;

import static com.huawei.cmp.controller.constants.ControllerConstants.CONTROLLER_TEMP_FILE_STORE_CLEAN_EXPRESSION;
import static com.huawei.cmp.controller.constants.ControllerConstants.FAIL_SLEEP_SECONDS;
import static com.huawei.cmp.controller.constants.ControllerConstants.GROUP;
import static com.huawei.cmp.controller.constants.ControllerConstants.TEMP_FILE_CLEAN;

import com.huawei.bsp.commonlib.hofs.HofsException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cmp.agent.controller.manager.bridge.utils.ControllerConfigUtil;
import com.huawei.cmp.agent.controller.server.metadata.model.FileStore;
import com.huawei.cmp.controller.dao.ActionHistoryDao;
import com.huawei.cmp.controller.dao.FileStoreDao;
import com.huawei.cmp.controller.exception.ControllerException;
import com.huawei.cmp.controller.utils.CommonUtils;
import com.huawei.cmp.foundation.hofs.DVHofsUtil;
import com.huawei.cmp.foundation.task.IExecutor;
import com.huawei.cmp.foundation.task.constant.TaskConcurrentLevel;
import com.huawei.cmp.foundation.task.constant.TaskStatus;
import com.huawei.cmp.foundation.task.constant.TaskType;
import com.huawei.cmp.foundation.task.exception.TaskRunTimeException;
import com.huawei.cmp.foundation.task.service.DistributedTask;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.UnableToInterruptJobException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.TimeZone;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import javax.annotation.PostConstruct;

/**
 * Created by y00312208 清理接口调用时产生的临时文件
 *
 * <AUTHOR>
 * @version DigitalView V100R001C20
 * @since 2021-07-27
 */
@Service
public class TempFileCleanTask implements IExecutor {
    /**
     * LOGGER
     */
    private static final OssLog LOGGER = OssLogFactory.getLogger(TempFileCleanTask.class);

    /**
     * DELETE_FILE_NUMBERS_EACH_TIME
     */
    private static final int DELETE_FILE_NUMBERS_EACH_TIME = 1000;

    /**
     * SLEEP_TIME_EACH_BATCH_DELETE
     */
    private static final int SLEEP_TIME_EACH_BATCH_DELETE = 1;

    /**
     * File store dao
     */
    @Autowired
    private FileStoreDao fileStoreDao;

    /**
     * Distributed task
     */
    @Autowired
    private DistributedTask distributedTask;

    /**
     * Action history dao
     */
    @Autowired
    private ActionHistoryDao actionHistoryDao;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        try {
            deleteTempFileBySlice();
            deleteSecurityFileBySlice();
        } catch (ControllerException ex) {
            LOGGER.warn("Temp File clean error:{}", ex);
        }
    }

    /**
     * Delete temp file by slice
     */
    public void deleteTempFileBySlice() {
        LOGGER.warn("begin to clean temp files.");
        List<FileStore> fileStores = fileStoreDao.getFileStoreOutOfDate(System.currentTimeMillis());
        if (CollectionUtils.isEmpty(fileStores)) {
            LOGGER.warn("Has no filestore need to delete.");
            return;
        }
        Iterator<FileStore> it = fileStores.iterator();
        List<FileStore> updateList;
        while (it.hasNext()) {
            List<FileStore> tempFileStores = getCurrentBatchList(it);
            List<String> idList = new ArrayList<>();
            List<String> filePathList = new ArrayList<>();
            updateList = new ArrayList<>();
            for (FileStore fileStore : tempFileStores) {
                LOGGER.warn("Begin to deal file:{}", fileStore.getFileId());
                boolean checkTodayUsedToday = this.checkTodayUsedToday(fileStore);
                // 判断今天,有没有使用
                if (!checkTodayUsedToday) {
                    LOGGER.warn("Delete file {}", fileStore.getFileId());
                    idList.add(fileStore.getFileId());
                    filePathList.add(fileStore.getLocation());
                } else {
                    // 超时时间+1
                    LOGGER.warn("Upate file {} ttl +1", fileStore.getFileId());
                    fileStore.setTtlDay(fileStore.getTtlDay() + 1);
                    updateList.add(fileStore);
                }
            }
            LOGGER.warn("Delete the out-of-date file size {}.", idList.size());
            LOGGER.warn("Update the out-of-date file size. {}", updateList.size());
            if (!updateList.isEmpty()) {
                fileStoreDao.updateFileStore(updateList);
                updateList.clear();
            }
            if (!idList.isEmpty()) {
                if (deleteFiles(filePathList)) {
                    fileStoreDao.deleteByIds(idList);
                }
            }
            // 每次批量删除1000条数据后续暂停5秒钟
            sleep();
        }

        LOGGER.warn("end to clean temp files.");
    }

    /**
     * Delete security file by slice
     */
    public void deleteSecurityFileBySlice() {
        // 删除停留天数为-2的文件，这些文件代表是以安全模式执行的文件，
        // 如果在执行完后因为系统故障等原因没有删除，则在此时的定时任务中删除。
        LOGGER.warn("begin to clean security files.");
        List<FileStore> securityFileStores = fileStoreDao.getSecurityFile();
        if (CollectionUtils.isEmpty(securityFileStores)) {
            LOGGER.warn("All the private scripts have been deleted.");
            return;
        }
        Iterator<FileStore> iter = securityFileStores.iterator();
        while (iter.hasNext()) {
            List<FileStore> securityTempFileStores = getCurrentBatchList(iter);
            List<String> securityIdList = new ArrayList<>();
            List<String> securityFilePathList = new ArrayList<>();
            for (FileStore fileStore : securityTempFileStores) {
                securityIdList.add(fileStore.getFileId());
                securityFilePathList.add(fileStore.getLocation());
            }
            LOGGER.warn("Delete the private file size {}.", securityIdList.size());
            LOGGER.warn("Delete the private file ids. {}", securityIdList);
            if (!securityFilePathList.isEmpty()) {
                if (deleteFiles(securityFilePathList)) {
                    fileStoreDao.deleteByIds(securityIdList);
                }
            }

            // 每次批量删除1000条数据后续暂停5秒钟
            sleep();
        }
        LOGGER.warn("begin to clean security files.");
    }

    /**
     * Gets current batch list *
     *
     * @param <T> parameter
     * @param it it
     * @return the current batch list
     */
    public <T> List<T> getCurrentBatchList(Iterator<T> it) {
        List<T> curList = new ArrayList<>();
        for (int i = 0; i < DELETE_FILE_NUMBERS_EACH_TIME; i++) {
            if (!it.hasNext()) {
                break;
            }
            curList.add(it.next());
        }
        return curList;
    }

    /**
     * Sleep
     */
    private void sleep() {
        try {
            TimeUnit.SECONDS.sleep(SLEEP_TIME_EACH_BATCH_DELETE);
        } catch (InterruptedException e) {
            LOGGER.error("batch delete file sleep failed.", e);
            LOGGER.error("batch delete file sleep failed {}.", e.getMessage());
        }
    }

    /**
     * Delete files boolean
     *
     * @param filePathList file path list
     * @return the boolean
     */
    public boolean deleteFiles(List<String> filePathList) {
        boolean result = true;
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(filePathList)) {
            for (String filePath : filePathList) {
                String remoteDestPath = FileUtils.getFile(filePath).getParentFile().getPath();
                try {
                    LOGGER.warn("Delete the out-of-date file :{}", remoteDestPath);
                    pathCheck(filePath, remoteDestPath);
                } catch (HofsException e) {
                    result = false;
                    LOGGER.error("deleteFiles failed ,{}", e.getMessage());
                    LOGGER.error("Delete the out-of-date file failed :{}", remoteDestPath);
                }
            }
        }
        return result;
    }

    public void pathCheck(String filePath, String remoteDestPath) throws HofsException {
        boolean exist = DVHofsUtil.checkRemoteFile(filePath);
        if (exist) {
            // 删除中心仓文件及其父文件夹
            DVHofsUtil.deleteFileOrDir(remoteDestPath);
        }
    }

    /**
     * Check today used today boolean
     *
     * @param fileStore file store
     * @return the boolean
     */
    private boolean checkTodayUsedToday(FileStore fileStore) {
        LOGGER.warn("checkcheckTodayUsedToday ==>fileId:{}", fileStore.getFileId());

        // 获取最后一次执行时间
        Long time = actionHistoryDao.getLastTimeOfActionsByFileId(fileStore.getFileId());
        if (time == null) {
            LOGGER.warn("checkcheckTodayUsedToday not used ==>fileId:{}", fileStore.getFileId());
            return false;
        }
        LOGGER.warn("checkcheckTodayUsedToday ==>fileId:{},time:{}", fileStore.getFileId(), time);

        // 当前时间毫秒数
        long current = System.currentTimeMillis();

        // 今天零点零分零秒的毫秒数
        long zero = current / (1000 * 3600 * 24) * (1000 * 3600 * 24) - TimeZone.getDefault().getRawOffset();

        // 昨天的这一时间的毫秒数
        long yesterday = zero - 24 * 60 * 60 * 1000;
        LOGGER.warn("checkcheckTodayUsedToday ==>current:{},zero:{},yesterday:{}", current, zero, yesterday);
        if (time > yesterday) {
            LOGGER.warn("checkcheckTodayUsedToday  used ==>fileId:{}", fileStore.getFileId());
            return true;
        }
        LOGGER.warn("checkcheckTodayUsedToday not used ==>fileId:{}", fileStore.getFileId());
        return false;
    }

    @Override
    public void interrupt() throws UnableToInterruptJobException {
    }

    /**
     * Init
     */
    @PostConstruct
    public void init() {
        LOGGER.warn("[INIT] Start to register task controller_clean_temp_file");

        ExecutorService executorService = Executors.newSingleThreadExecutor();
        executorService.execute(() -> {
            boolean regFailFlag = true;
            while (regFailFlag) {
                try {
                    // 每日凌晨0点
                    String expression =
                        ControllerConfigUtil.getStringVal(CONTROLLER_TEMP_FILE_STORE_CLEAN_EXPRESSION, "0 0 0 * * ?");
                    distributedTask.register(TEMP_FILE_CLEAN, GROUP, expression, "tempFileCleanTask", TaskType.CRON, 0,
                        TaskConcurrentLevel.NORMAL, TaskStatus.RUNNING, null);
                    distributedTask.start(TEMP_FILE_CLEAN, GROUP);

                    regFailFlag = false;
                } catch (TaskRunTimeException e) {
                    LOGGER.error("Fail to register controller_download_clean_temp_file.", e);
                    CommonUtils.sleep(TimeUnit.SECONDS, FAIL_SLEEP_SECONDS,
                        "Fail to register controller_download_clean_temp_file.");
                }
            }
            LOGGER.warn("[INIT] Success to register task controller_clean_temp_file");
        });
        executorService.shutdown();
    }
}
