/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.cmp.controller.impl;

import static com.huawei.cmp.controller.constants.ControllerConstants.ENV_PARAM_BLACK_LIST;
import static com.huawei.cmp.controller.constants.ControllerConstants.IHEALING_GROUP;
import static com.huawei.cmp.controller.constants.ControllerConstants.MAX_IP_LIST;
import static com.huawei.cmp.controller.constants.ControllerConstants.PARAMS_PATH;

import com.huawei.bsp.commonlib.hofs.HofsException;
import com.huawei.bsp.deploy.util.DefaultEnvUtil;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.cmp.agent.controller.api.ServerErrorCode;
import com.huawei.cmp.agent.controller.depot.api.utils.HMacSha256Util;
import com.huawei.cmp.agent.controller.manager.bridge.utils.ControllerConfigUtil;
import com.huawei.cmp.agent.controller.server.metadata.model.ActionHistory;
import com.huawei.cmp.agent.controller.server.metadata.model.ActionRegex;
import com.huawei.cmp.agent.controller.server.metadata.model.FileStore;
import com.huawei.cmp.controller.constants.ActionType;
import com.huawei.cmp.controller.constants.ControllerConstants;
import com.huawei.cmp.controller.dao.ActionRegexDao;
import com.huawei.cmp.controller.dao.FileStoreDao;
import com.huawei.cmp.controller.dboperate.ActionHistoryManager;
import com.huawei.cmp.controller.dboperate.FileStoreManager;
import com.huawei.cmp.controller.delegate.DvExecuteScriptServiceDelegate;
import com.huawei.cmp.controller.exception.ControllerException;
import com.huawei.cmp.controller.model.ExecuteParameter;
import com.huawei.cmp.controller.model.RegexInfo;
import com.huawei.cmp.controller.service.action.DefaultAction;
import com.huawei.cmp.controller.service.filedownload.RsaHelper;
import com.huawei.cmp.controller.utils.DvControllerShareUtil;
import com.huawei.cmp.controller.utils.FileUtil;
import com.huawei.cmp.controller.utils.IOUtil;
import com.huawei.cmp.controller.utils.JacksonUtils;
import com.huawei.cmp.controller.utils.PreconditionUtil;
import com.huawei.cmp.controller.zookeeper.ZkClient;
import com.huawei.cmp.foundation.container.util.CmpFileUtil;
import com.huawei.cmp.foundation.hofs.DVHofsUtil;
import com.huawei.cmp.foundation.service.exception.CMPException;
import com.huawei.cmp.utils.ZipUtils;
import com.huawei.digitalview.commons.utils.sign.SignUtils;
import com.huawei.i2000.cbb.common.StringUtil;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipFile;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.utils.ZKPaths;
import org.apache.zookeeper.CreateMode;
import org.apache.zookeeper.data.Stat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.StandardOpenOption;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * Dv execute script service delegate
 *
 * @since V800R001C02
 */
@Component
public class DvExecuteScriptServiceDelegateImpl implements DvExecuteScriptServiceDelegate {
    /**
     * RUN_LOGGER
     */
    private static final OssLog RUN_LOGGER = OssLogFactory.getLogger(DvExecuteScriptServiceDelegateImpl.class);

    /**
     * BOOTSTRAP_SCRIPT_SIGN
     */
    private static final String BOOTSTRAP_SCRIPT_SIGN = "run.sh_sign";

    /**
     * BOOTSTRAP_SCRIPT
     */
    private static final String BOOTSTRAP_SCRIPT = "run.sh";

    /**
     * FILE_MAX_SIZE
     */
    private static final long FILE_MAX_SIZE = 100 * 1024 * 1024L;

    private static final String ENV_BLACK_LIST = ControllerConfigUtil.getStringVal(ENV_PARAM_BLACK_LIST,"`");

    /**
     * FILE_MAX_COUNT
     */
    private static final int FILE_MAX_COUNT = 10000;

    /**
     *
     */
    private static final int MAX_LENGTH = 256;

    /**
     * File md 5
     */
    private final Map<String, String> fileMd5 = new HashMap<>();

    /**
     * Hex array
     */
    String[] hexArray = {"0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F"};

    /**
     * Action instance manager
     */
    @Autowired
    private ActionHistoryManager actionInstanceManager;

    /**
     * File store dao
     */
    @Autowired
    private FileStoreDao fileStoreDao;

    /**
     * Action regex dao
     */
    @Autowired
    private ActionRegexDao actionRegexDao;

    /**
     * Rsa helper
     */
    @Autowired
    private RsaHelper rsaHelper;

    @Override
    public String executeScript(HttpContext context, ExecuteParameter executeParam) throws ServiceException {
        Assert.notNull(executeParam, "executeParam is null");
        List<String> ips = executeParam.getIps();
        RUN_LOGGER.warn("Start to distribute script task by content bytes.[hosts={},param={}]", ips, executeParam);
        byte[] scriptContent = executeParam.getScriptContent();
        Assert.notNull(scriptContent, "scriptContent is null");
        Assert.isTrue(
                scriptContent.length <= FileUtils.ONE_MB * 10 && scriptContent.length > 0, "scriptContent is too long");
        validateIpInParam(executeParam);

        String actionInsId;
        try {
            FileStore fileStore = FileStoreManager.prepareFileStore();
            String remotePath = fileStore.getLocation();
            String localCachePath = DVHofsUtil.getLocalFileUrl(remotePath);
            fileStore.setTempFile(true);
            String fileMd5Str = HMacSha256Util.hmacSha256(scriptContent);
            fileStore.setFileMd5(fileMd5Str);
            File locationFile = FileUtils.getFile(localCachePath);
            FileUtil.writeByteArrayToFile(locationFile, scriptContent);
            FileUtil.setPathPerms(locationFile);

            // 创建上传文件对应的唯一标识文件
            DvControllerShareUtil.insertMD5File(localCachePath, fileMd5Str);
            // 上传到中心仓
            DVHofsUtil.pushFile(localCachePath, remotePath);
            fileStoreDao.insert(fileStore);
            actionInsId = execute(ips, executeParam, fileStore);
        } catch (IOException e) {
            RUN_LOGGER.error("execute script by content failed.", e);
            throw new CMPException(String.valueOf(ServerErrorCode.NFS_IO_ERROR));
        } catch (Exception e) {
            RUN_LOGGER.error("execute script by content failed.", e);
            throw new CMPException(String.valueOf(ServerErrorCode.EXECUTESCRIPT_ERROR));
        }
        RUN_LOGGER.warn(
                "Finished to distribute script task by content bytes.[hosts={},param={}] and actionInsId:{}",
                ips,
                executeParam,
                actionInsId);
        return actionInsId;
    }

    @Override
    public String executeScriptByResourceId(HttpContext context, ExecuteParameter executeParam)
            throws ServiceException {
        Assert.notNull(executeParam, "executeParam is null");
        String resourceId = executeParam.getResourceId();
        RUN_LOGGER.warn("Start to distribute script task by resourceId.[id={}]", resourceId);
        List<String> ips = executeParam.getIps();
        Assert.notNull(ips, "ips is null");
        Assert.isTrue(StringUtils.isNotBlank(resourceId), "resourceId is empty");
        Assert.isTrue(PreconditionUtil.isNotcontainsSpecialChar(resourceId), "resourceId contains special char!");
        Assert.isTrue(resourceId.length() <= MAX_LENGTH, "resourceId is too long!");
        validateIpInParam(executeParam);
        String actionInsId;
        FileStore fileStore = fileStoreDao.getFileById(resourceId);
        if (fileStore == null) {
            RUN_LOGGER.error("resourceId is not exist.");
            throw new ControllerException(ServerErrorCode.ARGUMENT_INVALID);
        }
        if (StringUtils.isNotEmpty(executeParam.getScriptMd5())) {
            try {
                getZipFile(executeParam, fileStore);
            } catch (InterruptedException e) {
                RUN_LOGGER.error("pull script fail, {} ", e.getMessage());
                throw new ControllerException(ServerErrorCode.DOWNLOAD_FAILED);
            }
        }

        actionInsId = execute(ips, executeParam, fileStore);
        RUN_LOGGER.warn(
                "Finished to distribute script task by resourceId.[id={},actionInsId={}]", resourceId, actionInsId);
        return actionInsId;
    }

    public void getZipFile(ExecuteParameter executeParam, FileStore fileStore)
            throws InterruptedException, ServiceException {
        if (executeParam.getZipTemplate()) {
            String remoteUrl = fileStore.getLocation();
            String localFileUrl = DVHofsUtil.getLocalFileUrl(remoteUrl);
            File file = FileUtils.getFile(localFileUrl);
            if (!file.exists()) {
                try {
                    DVHofsUtil.pullFile(remoteUrl, localFileUrl);
                } catch (HofsException e) {
                    RUN_LOGGER.error("getZipFile failed,{}", e.getMessage());
                    throw new ServiceException(e.getMessage());
                }
            }
            String hmac = HMacSha256Util.hmacSha256(file);
            if (!StringUtils.equals(hmac, fileStore.getFileMd5())) {
                throw new ControllerException(ServerErrorCode.MD5_INVALID);
            }
        } else {
            if (!StringUtils.equals(fileStore.getFileMd5(), executeParam.getScriptMd5())) {
                throw new ControllerException(ServerErrorCode.MD5_INVALID);
            }
        }
    }

    @Override
    public String executeScriptRest(HttpContext context, ExecuteParameter executeParam) throws ServiceException {
        Assert.notNull(executeParam, "executeParam is null!");

        List<String> ips = executeParam.getIps();
        RUN_LOGGER.warn("Start to distribute script task by content bytes in rest.[hosts={}]", ips);
        byte[] scriptContent = executeParam.getScriptContent();
        Assert.notNull(scriptContent, "scriptContent is null!");
        Assert.isTrue(
                scriptContent.length <= FileUtils.ONE_MB * 10 && scriptContent.length > 0,
                "scriptContent is too long!");
        validateIpInParam(executeParam);
        String actionInsId;
        try {
            actionInsId = doExecuteScript(scriptContent, executeParam, ips);
        } catch (IOException e) {
            RUN_LOGGER.error("execute script by content failed.", e);
            throw new CMPException(String.valueOf(ServerErrorCode.NFS_IO_ERROR));
        } catch (NoSuchAlgorithmException | ServiceException | InterruptedException e) {
            RUN_LOGGER.error("execute script by content failed.", e);
            throw new CMPException(String.valueOf(ServerErrorCode.EXECUTESCRIPT_ERROR));
        }

        RUN_LOGGER.warn(
                "Finished to distribute script task by content bytes.[hosts={}] and actionInsId:{}", ips, actionInsId);
        return actionInsId;
    }

    private String doExecuteScript(byte[] scriptContent, ExecuteParameter executeParam, List<String> ips)
            throws NoSuchAlgorithmException, ServiceException, InterruptedException, IOException {
        MessageDigest md = MessageDigest.getInstance("SHA-512");
        md.update(scriptContent);
        byte[] rawBit = md.digest();
        String outputMD5 = " ";
        for (int i = 0; i < 16; i++) {
            outputMD5 = outputMD5.concat(hexArray[rawBit[i] >>> 4 & 0x0f]);
            outputMD5 = outputMD5.concat(hexArray[rawBit[i] & 0x0f]);
        }
        outputMD5 = outputMD5.trim();
        FileStore fileStore = FileStoreManager.prepareFileStore();
        String remotePath = fileStore.getLocation();
        String localCachePath = DVHofsUtil.getLocalFileUrl(remotePath);

        // -2代表需要立刻删除
        if (executeParam.isSecurity()) {
            fileStore.setTtlDay(-2);
        }
        fileStore.setTempFile(true);
        if (fileMd5.containsKey(outputMD5)) {
            fileStore.setFileMd5(fileMd5.get(outputMD5));

            // 创建上传文件对应的唯一标识文件
            DvControllerShareUtil.insertMD5File(localCachePath, fileMd5.get(outputMD5));
        } else {
            String md5 = HMacSha256Util.hmacSha256(scriptContent);
            fileStore.setFileMd5(md5);
            fileMd5.put(outputMD5, md5);

            // 创建上传文件对应的唯一标识文件
            DvControllerShareUtil.insertMD5File(localCachePath, md5);
        }
        File locationFile = FileUtils.getFile(localCachePath);
        FileUtil.writeByteArrayToFile(locationFile, scriptContent);
        FileUtil.setPathPerms(locationFile);

        // 上传到中心仓
        try {
            DVHofsUtil.pushFile(localCachePath, remotePath);
        } catch (HofsException e) {
            RUN_LOGGER.error("doExecuteScript failed,{}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
        fileStoreDao.insert(fileStore);
        String actionInsId = execute(ips, executeParam, fileStore);
        if (fileMd5.size() > 1000) {
            fileMd5.clear();
        }
        return actionInsId;
    }

    /**
     * Execute string
     *
     * @param ips ips
     * @param executeParam execute param
     * @param fileStore file store
     * @return the string
     */
    private String execute(List<String> ips, ExecuteParameter executeParam, FileStore fileStore) {
        checkParam(executeParam);
        ActionHistory actionHistory =
                actionInstanceManager.initActionHistory(ips, executeParam, fileStore, ActionType.EXECUTE_SCRIPT);
        actionHistory.setActionParam(executeParam.getScriptParams());
        actionHistory.setSuccessKeyword(executeParam.getSuccessKeyword());
        actionHistory.setSecurity(executeParam.isSecurity() ? "true" : "false");
        if (executeParam.isZipTemplate()) {
            ZipFile check = null;
            try {
                actionHistory.setActionType(ActionType.ZIP.ordinal());
                String localFileUrl = DVHofsUtil.getLocalFileUrl(fileStore.getLocation());
                DVHofsUtil.pullFile(fileStore.getLocation(), localFileUrl);
                // zip炸弹校验
                ZipUtils.checkZipBomb(FileUtils.getFile(localFileUrl), FILE_MAX_COUNT, FILE_MAX_SIZE);
                // 判断签名文件是否存在
                check = ZipFile.builder()
                    .setUseUnicodeExtraFields(true)
                    .setIgnoreLocalFileHeader(false)
                    .setSeekableByteChannel(Files.newByteChannel(new File(localFileUrl).toPath(), StandardOpenOption.READ))
                    .get();
                boolean containsSign = false;
                Enumeration<ZipArchiveEntry> zipEntries = check.getEntries();
                while (zipEntries.hasMoreElements()) {
                    ZipArchiveEntry zipEntry = zipEntries.nextElement();
                    containsSign = getContainSign(containsSign, zipEntry);
                }
                if (!containsSign) {
                    generateSignInZip(fileStore, actionHistory, localFileUrl);
                }
            } catch (IOException e) {
                RUN_LOGGER.error("Fail to generate asc for script, {}", e.getMessage());
            } catch (Exception e) {
                RUN_LOGGER.error("Fail to execute script, {}", e.getMessage());
            } finally {
                if (check != null) {
                    try {
                        check.close();
                    } catch (IOException e) {
                        RUN_LOGGER.error("Fail to close check {}", e.getMessage());
                    }
                }
            }
        } else {
            // 执行脚本前先对脚本文件生成签名
            rsaHelper.signScriptByRsa(fileStore);
        }
        return saveActionHistoryAndGetActionId(executeParam, actionHistory);
    }

    /**
     *  getContainSign
     *
     * @param containsSign containsSign
     * @param zipEntry zipEntry
     * @return ContainSign
     */
    public boolean getContainSign(boolean containsSign, ZipArchiveEntry zipEntry) {
        if (BOOTSTRAP_SCRIPT_SIGN.equals(zipEntry.getName())) {
            containsSign = true;
        }
        return containsSign;
    }

    private String saveActionHistoryAndGetActionId(ExecuteParameter executeParam, ActionHistory actionHistory) {
        actionInstanceManager.insertActionHistory(actionHistory);
        try {
            insertActionRegexInfos(actionHistory.getActionInstanceId(), executeParam.getRegexInfos());
            Map<String, String> ipRelationMap = actionHistory.getIpRelationMap();
            Map<String, Map<String, String>> rebuildIpEnvMap = rebuildIpEnvMap(executeParam, ipRelationMap);
            addIpEnvParam2Zk(actionHistory.getActionInstanceId(), rebuildIpEnvMap);
            // 生成profile文件
            genProfileWithParam(executeParam, actionHistory);
            actionHistory.setUseSecBox(executeParam.getUseSecBox());
            actionHistory.setUseInside(executeParam.getUseInside());
            new DefaultAction(actionHistory).execute();
        } catch (Exception e) {
            RUN_LOGGER.error("Fail to execute script=============, ", e);
            throw new ControllerException(ServerErrorCode.EXECUTESCRIPT_ERROR, e);
        }
        return actionHistory.getActionInstanceId();
    }

    private void genProfileWithParam(ExecuteParameter executeParam, ActionHistory actionHistory) throws Exception {
        // 如果请求下载profile文件，临时生成
        String actionInstanceId = actionHistory.getActionInstanceId();
        String cfgFileId = actionHistory.getCfgFileId();
        Map<String, String> envFromCfgFile = new HashMap<>();
        // 作业控制台会有这个不为空场景
        if (StringUtil.isNotEmpty(cfgFileId)) {
            envFromCfgFile = getEnvFromCfgFile(cfgFileId);
        }
        appendParamInZk(actionInstanceId, envFromCfgFile);
        if (envFromCfgFile.size() == 0) {
            return;
        }
        String profileId = actionInstanceId.concat("_profile");
        FileStore fileStore = new FileStore();
        fileStore.setFileId(profileId);
        fileStore.setCreateTime(System.currentTimeMillis());
        String uuPath = FileUtil.genUUPath(profileId);
        String localLocation =
            DefaultEnvUtil.getAppShareDir()
                        .concat(ControllerConstants.FILE_SEPARATOR)
                        .concat(uuPath)
                        .concat(ControllerConstants.FILE_SEPARATOR)
                        .concat(profileId)
                        .concat(ControllerConstants.FILE_SEPARATOR)
                        .concat(profileId);
        // 记录远程文件路径
        fileStore.setLocation(DVHofsUtil.getRemoteUrl(localLocation));
        int tmpFileStoreDays = ControllerConfigUtil.getIntVal(ControllerConstants.CONTROLLER_TEMP_FILE_STORE_DAYS, 1);
        fileStore.setTtlDay(tmpFileStoreDays);
        writeParamToProfile(envFromCfgFile, fileStore, localLocation);
    }

    private void writeParamToProfile(Map<String, String> envFromCfgFile, FileStore fileStore, String localLocation)
            throws IOException, ServiceException {
        File file = FileUtils.getFile(localLocation);
        if (!file.exists()) {
            final File parent = file.getParentFile();
            if (parent != null) {
                if (!parent.mkdirs() && !parent.isDirectory()) {
                    RUN_LOGGER.error("Fail to create Directory, {}", parent);
                    throw new IOException("Directory '" + parent + "' could not be created");
                }
                CmpFileUtil.setFilePerms(FileUtil.getCanonicalPathQuietly(parent), CmpFileUtil.FilePerm.ORWX_GRX);
            }
        }
        // 把map写入profile文件
        try (FileOutputStream outStream = new FileOutputStream(localLocation);
            OutputStreamWriter osWriter = new OutputStreamWriter(outStream, StandardCharsets.UTF_8);
            BufferedWriter writer = new BufferedWriter(osWriter);) {
            for (Map.Entry<String, String> element : envFromCfgFile.entrySet()) {
                try {
                    String value = element.getValue();
                    for (String specialChar : ENV_BLACK_LIST.split(",")) {
                        if (value.contains(specialChar)) {
                            RUN_LOGGER.error("param contain special char");
                            throw new IOException("param contain special char");
                        }
                    }
                    String lineStr = String.format(Locale.ENGLISH, "export %s=\'%s\' ", element.getKey(), value);
                    writer.write(lineStr);
                    writer.newLine();
                } catch (IOException e) {
                    RUN_LOGGER.error("Fail to write param to profile, {}", e.getMessage());
                    throw new IOException("Fail to write param to profile");
                }
            }
        }
        FileUtil.setPathPerms(file);
        String fileMd5Str = HMacSha256Util.hmacSha256(file);
        // 创建上传文件对应的唯一标识文件
        DvControllerShareUtil.insertMD5File(localLocation, fileMd5Str);
        // 上传到中心仓
        try {
            DVHofsUtil.pushFile(localLocation, DVHofsUtil.getRemoteUrl(localLocation));
        } catch (HofsException e) {
            RUN_LOGGER.error("writeParamToProfile failed,{}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
        fileStore.setFileMd5(fileMd5Str);
        fileStoreDao.insert(fileStore);
        rsaHelper.signScriptByRsa(fileStore);
    }

    /**
     * Map type reference
     */
    static class MapTypeReference extends TypeReference<Map<String, String>> {
        /**
         * Map type reference
         */
        public MapTypeReference() {}
    }

    /**
     * appendParamInZk
     *
     * @param actionInstanceId actionInstanceId
     * @param envFromCfgFile envFromCfgFile
     * @throws Exception Exception
     */
    public void appendParamInZk(String actionInstanceId, Map<String, String> envFromCfgFile) throws Exception {
        CuratorFramework zkClient = ZkClient.getInstance();
        String path = ZKPaths.makePath(PARAMS_PATH, actionInstanceId);
        Stat stat = zkClient.checkExists().forPath(path);
        if (stat == null) {
            return;
        }
        List<String> nodes = zkClient.getChildren().forPath(path);
        if (CollectionUtils.isEmpty(nodes)) {
            return;
        }
        String paramInZk = "";
        for (String node : nodes) {
            String nodePath = ZKPaths.makePath(path, node);
            Stat childNodeStat = zkClient.checkExists().forPath(nodePath);
            if (null == childNodeStat) {
                continue;
            }
            byte[] bytes = zkClient.getData().forPath(nodePath);
            // 同一个actionInstanceId中不同ip中的参数是一致的，第一个读取成功即可返回
            paramInZk = new String(bytes, StandardCharsets.UTF_8);
            break;
        }
        Map<String, String> envMapInZk = JSONObject.parseObject(paramInZk, new MapTypeReference());
        envMapInZk.forEach(
                (key, value) -> {
                    if (!key.contains("IHEALING_FLOW_PASSWD_")) {
                        envFromCfgFile.put(key, value);
                    }
                });
    }

    /**
     * getEnvFromCfgFile
     *
     * @param cfgFileId cfgFileid
     * @return EnvFromCfgFile
     * @throws InterruptedException InterruptedException
     * @throws ServiceException ServiceException
     * @throws IOException IOException
     */
    public Map<String, String> getEnvFromCfgFile(String cfgFileId)
            throws ServiceException, IOException {
        FileStore configFile = fileStoreDao.getFileById(cfgFileId);
        String remoteLocation = configFile.getLocation();
        String localCfgUrl = DVHofsUtil.getLocalFileUrl(remoteLocation);
        try {
            DVHofsUtil.pullFile(remoteLocation, localCfgUrl);
        } catch (HofsException e) {
            RUN_LOGGER.error("getEnvFromCfgFile failed,{}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
        List<String> cfgLines = FileUtils.readLines(FileUtils.getFile(localCfgUrl), StandardCharsets.UTF_8);
        Map<String, String> envMap = new HashMap<>();
        String key;
        String value;
        for (String line : cfgLines) {
            if (StringUtils.isEmpty(line)) {
                continue;
            }
            // 调过注释
            int commentChar = line.indexOf("#");
            if (commentChar == 0) {
                continue;
            }
            int index = line.indexOf("=");
            if (index < 1) {
                continue;
            }
            key = line.substring(0, index).trim();
            value = line.substring(index + 1).trim();
            if (StringUtils.isEmpty(key) || StringUtils.isEmpty(value)) {
                continue;
            }
            envMap.put(key, value);
        }
        return envMap;
    }

    private void checkParam(ExecuteParameter executeParam) {
        if (StringUtils.isNotBlank(executeParam.getScriptParams())) {
            Assert.isTrue(StringUtils.length(executeParam.getScriptParams()) <= 8000, "executeParam is too long");
        }

        if (StringUtils.isNotBlank(executeParam.getSuccessKeyword())) {
            Assert.isTrue(StringUtils.length(executeParam.getSuccessKeyword()) <= 256, "SuccessKeyword is too long");
            Assert.isTrue(
                    PreconditionUtil.isNotcontainsChineseChar(executeParam.getSuccessKeyword()),
                    "SuccessKeyword contains chinese char");
            Assert.isTrue(
                    PreconditionUtil.isNotcontainsSpecialChar(executeParam.getSuccessKeyword()),
                    "SuccessKeyword contains special char");
        }
    }

    // 执行zip包需要解压开对内部的run.sh进行签名
    private void generateSignInZip(FileStore fileStore, ActionHistory actionHistory, String localFileUrl)
            throws IOException, ServiceException {
        ZipFile zip = ZipFile.builder()
            .setUseUnicodeExtraFields(true)
            .setIgnoreLocalFileHeader(false)
            .setSeekableByteChannel(Files.newByteChannel(new File(localFileUrl).toPath(), StandardOpenOption.READ))
            .get();
        boolean hasBootstrapScript = false;
        ZipOutputStream zos = null;
        InputStream ins = null;
        try {
            Enumeration<ZipArchiveEntry> entries = zip.getEntries();
            zos = new ZipOutputStream(new FileOutputStream(localFileUrl.concat("bak")));
            while (entries.hasMoreElements()) {
                ZipArchiveEntry entry = entries.nextElement();
                zos.putNextEntry(new ZipEntry(entry.getName()));
                ins = zip.getInputStream(entry);
                if (StringUtils.equals(entry.getName(), BOOTSTRAP_SCRIPT)) {
                    hasBootstrapScript = true;
                    StringBuilder builder = new StringBuilder();
                    writeSignFile(zos, ins, builder);
                } else {
                    writeFile(zos, ins);
                }
            }
        } finally {
            IOUtil.closeQuietly(ins);
            IOUtil.closeQuietly(zos);
            zip.close();
        }
        // 覆盖本地文件
        FileUtils.copyFile(new File(localFileUrl.concat("bak")), new File(localFileUrl));
        try {
            DVHofsUtil.pushFile(localFileUrl, fileStore.getLocation());
        } catch (HofsException e) {
            RUN_LOGGER.error("generateSignInZip failed,{}", e.getMessage());
            throw new ServiceException(e.getMessage());
        }
        fileStore.setFileMd5(HMacSha256Util.hmacSha256(FileUtils.getFile(localFileUrl)));
        actionHistory.setFileStore(fileStore);
        fileStoreDao.updateFileStore(Collections.singletonList(fileStore));
        // 删除本地临时文件
        FileUtils.deleteQuietly(new File(localFileUrl.concat("bak")));
        if (!hasBootstrapScript) {
            RUN_LOGGER.error(
                    MessageFormat.format(
                            "The zip package does not contain bootstrap script file [{0}].", BOOTSTRAP_SCRIPT));
            throw new ControllerException(ServerErrorCode.DB_DATA_DAMAGE);
        }
    }

    private void writeFile(ZipOutputStream zos, InputStream ins) throws IOException {
        int len;
        long total = 0L;
        byte[] buffer = new byte[1024];
        while ((len = ins.read(buffer)) != -1) {
            zos.write(buffer, 0, len);
            zos.flush();
            total += len;
            if (total > FILE_MAX_SIZE) {
                throw new CMPException("file size is bigger than max");
            }
        }
    }

    private void writeSignFile(ZipOutputStream zos, InputStream ins, StringBuilder builder) throws IOException {
        int len;
        long total = 0L;
        byte[] buffer = new byte[1024];
        while ((len = ins.read(buffer)) != -1) {
            builder.append(new String(buffer, 0, len, StandardCharsets.UTF_8));
            zos.write(buffer, 0, len);
            zos.flush();
            total += len;
            if (total > FILE_MAX_SIZE) {
                throw new CMPException("size is bigger than max");
            }
        }
        zos.closeEntry();
        // 多生成一个sign文件
        String scriptContent = builder.toString();
        String scriptSign = SignUtils.signByRsa(scriptContent);
        zos.putNextEntry(new ZipEntry(BOOTSTRAP_SCRIPT_SIGN));
        zos.write(scriptSign.getBytes(StandardCharsets.UTF_8));
        zos.flush();
    }

    /**
     * Insert action regex infos *
     *
     * @param actionInstanceId action instance id
     * @param regexInfos regex infos
     */
    private void insertActionRegexInfos(String actionInstanceId, List<RegexInfo> regexInfos) {
        if (CollectionUtils.isEmpty(regexInfos)) {
            return;
        }

        // 最多传入10条正则
        Assert.isTrue(CollectionUtils.size(regexInfos) <= 10, "regexInfos is more than 10");

        int maxTtl = ControllerConfigUtil.getIntVal(ControllerConstants.CONTROLLER_REGEX_DATA_MAX_TTL, 30 * 24 * 60);

        List<ActionRegex> actionRegexes = new ArrayList<>();
        Set<String> set = new HashSet<String>();
        for (RegexInfo regexInfo : regexInfos) {
            String regex = regexInfo.getRegex();
            Assert.isTrue(StringUtils.isNotBlank(regex), "regex is blank");
            if (set.contains(regex)) {
                continue;
            }
            set.add(regex);

            int ttl = regexInfo.getTtl();
            Assert.isTrue(ttl >= 0 && ttl <= maxTtl, "ttl is invalid");

            ActionRegex actionRegex = new ActionRegex();
            actionRegex.setId(UUID.randomUUID().toString());
            actionRegex.setActionInstanceId(actionInstanceId);
            actionRegex.setRegex(regex);
            actionRegex.setTtl(ttl);

            actionRegexes.add(actionRegex);
        }

        actionRegexDao.insertActionRegexes(actionRegexes);
    }

    /**
     * Add ip env param 2 zk *
     *
     * @param actionInsId action ins id
     * @param rebuildIpEnvMap rebuild ip env map
     * @throws Exception exception
     */
    private void addIpEnvParam2Zk(String actionInsId, Map<String, Map<String, String>> rebuildIpEnvMap)
            throws Exception {
        RUN_LOGGER.warn("Start to add ip env param to zk and actionInsId:{}.", actionInsId);
        if (MapUtils.isEmpty(rebuildIpEnvMap)) {
            RUN_LOGGER.warn("Ip relation Env Map params is empty");
            return;
        }

        for (Map.Entry<String, Map<String, String>> entry : rebuildIpEnvMap.entrySet()) {
            String ip = entry.getKey();
            Map<String, String> envParams = entry.getValue();
            byte[] value = JacksonUtils.toJson(envParams).getBytes(StandardCharsets.UTF_8);
            String path = ZKPaths.makePath(PARAMS_PATH, actionInsId, ip);
            addIpEnvParam(path, value);
            RUN_LOGGER.warn("Finished to create node path :{} to add ip env param.", path);
        }
    }

    /**
     * addIpEnvParam
     *
     * @param path 路径
     * @param value 值
     * @throws Exception 异常
     */
    public void addIpEnvParam(String path, byte[] value) throws Exception {
        CuratorFramework zkClient = ZkClient.getInstance();
        zkClient.create().creatingParentsIfNeeded().withMode(CreateMode.PERSISTENT).forPath(path, value);
    }

    /**
     * 将ips与实际执行ip进行映射
     *
     * @param executeParam execute param
     * @param ipRelationMap ip relation map
     * @return map map
     */
    private Map<String, Map<String, String>> rebuildIpEnvMap(
            ExecuteParameter executeParam, Map<String, String> ipRelationMap) {
        RUN_LOGGER.warn("Start to rebuild ip and env map and execute Parameter");
        Map<String, Map<String, String>> realIpEnvMap = new HashMap<>();

        Map<String, Map<String, String>> ipEnvMap = executeParam.getIpEnvMap();
        if (MapUtils.isEmpty(ipEnvMap)) {
            RUN_LOGGER.warn("ipEnvMap is empty of execute param.");
            return realIpEnvMap;
        }

        if (MapUtils.isEmpty(ipRelationMap)) {
            RUN_LOGGER.warn("ipRelationMap is empty.");
            return ipEnvMap;
        }

        // 判断参数传入方是否为ihealing，V8C20SPC100起修改ihealing传入的ipEnvMap参数结构
        if (executeParam.getWhoIsCallingController() != null
                && IHEALING_GROUP.equals(executeParam.getWhoIsCallingController())) {
            String[] ips = ipEnvMap.get("ips").get("ipList").split(",");
            Map<String, String> envMapValue = ipEnvMap.get("params");
            for (String ip : ips) {
                String ipRelation = ipRelationMap.get(ip);
                String realIp =
                        StringUtils.isEmpty(ipRelation)
                                ? ip
                                : ipRelation
                                        .replace(ActionHistory.Constants.DO_IP_FLAG, StringUtils.EMPTY)
                                        .replace(ActionHistory.Constants.EXTEND_IP_FLAG, StringUtils.EMPTY);
                realIpEnvMap.put(realIp, envMapValue);
            }
            RUN_LOGGER.warn("Finished to rebuild ip and env map and real ipRelationMap length:{}", realIpEnvMap.size());
            return realIpEnvMap;
        }

        for (Map.Entry<String, Map<String, String>> entry : ipEnvMap.entrySet()) {
            String ip = entry.getKey();
            Map<String, String> envMapValue = entry.getValue();
            String ipRelation = ipRelationMap.get(ip);
            String realIp =
                    StringUtils.isEmpty(ipRelation)
                            ? ip
                            : ipRelation
                                    .replace(ActionHistory.Constants.DO_IP_FLAG, StringUtils.EMPTY)
                                    .replace(ActionHistory.Constants.EXTEND_IP_FLAG, StringUtils.EMPTY);
            realIpEnvMap.put(realIp, envMapValue);
        }
        RUN_LOGGER.warn("Finished to rebuild ip and env map and real ipRelationMap length {}", realIpEnvMap.size());
        return realIpEnvMap;
    }

    /**
     * Validate ip in param *
     *
     * @param executeParam execute param
     */
    private void validateIpInParam(ExecuteParameter executeParam) {
        Map<String, Map<String, String>> ipEnvParams = executeParam.getIpEnvMap();
        if (null == ipEnvParams) {
            return;
        }
        // 判断参数传入方是否为ihealing，V8C20SPC100起修改ihealing传入的ipEnvMap参数结构
        if (executeParam.getWhoIsCallingController() != null
                && IHEALING_GROUP.equals(executeParam.getWhoIsCallingController())) {
            String[] ips = ipEnvParams.get("ips").get("ipList").split(",");
            if (ips.length > MAX_IP_LIST) {
                RUN_LOGGER.error("execute script by content failed. ips is upper limit.");
                throw new CMPException(String.valueOf(ServerErrorCode.EXECUTESCRIPT_ERROR));
            }
            for (String ip : ips) {
                if (!PreconditionUtil.isIpvalid(ip)) {
                    RUN_LOGGER.error("execute script by content failed. {} is not valid ip address.", ip);
                    throw new CMPException(String.valueOf(ServerErrorCode.ARGUMENT_INVALID));
                }
            }
        } else {
            if (ipEnvParams.keySet().size() > MAX_IP_LIST) {
                RUN_LOGGER.error("execute script by content failed. ips is upper limit.");
                throw new CMPException(String.valueOf(ServerErrorCode.EXECUTESCRIPT_ERROR));
            }
            for (Map.Entry<String, Map<String, String>> entry : ipEnvParams.entrySet()) {
                String ip = entry.getKey();
                if (!PreconditionUtil.isIpvalid(ip)) {
                    RUN_LOGGER.error("execute script by content failed. {} is not valid ip address.", ip);
                    throw new CMPException(String.valueOf(ServerErrorCode.ARGUMENT_INVALID));
                }
            }
        }
    }
}
