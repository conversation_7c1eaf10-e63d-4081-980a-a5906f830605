/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.cmp.dvcallchainservice.business.logdrilling;

import static java.util.stream.Collectors.toList;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cmp.dvcallchainservice.model.ReferenceModel;
import com.huawei.cmp.dvcallchainservice.util.DateTimeFormatterUtil;

import com.alibaba.fastjson2.JSON;

import lombok.Setter;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * etrace日志关联查询类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/4/14 9:55
 */
@Component("etraceLogDriller")
public class EtraceLogDriller implements LogDriller<String> {
    private static final OssLog LOG = OssLogFactory.getLogger(EtraceLogDriller.class);

    private static final int MAX_SIZE = 10000;

    @Autowired
    @Setter
    private RestHighLevelClient client;

    @Autowired
    @Setter
    private IndexProvider indexProvider;

    @Override
    public List<String> drill(String param) {
        Map<String, String> paramMap = JSON.parseObject(param, new ReferenceModel.MapReference());

        String index = findIndex(paramMap.get("time"));
        Tuple2<String, String> timeRange = getTime(paramMap.get("time"));
        List<String> logs = drillLog(index, paramMap, timeRange);
        return CollectionUtils.isEmpty(logs) ? Arrays.asList("no data") : logs;
    }

    /**
     * 钻取etace日志上下文
     *
     * @param index 索引
     * @param paramMap paramMap
     * @param timeRange 时间范围
     * @return 日志上下文集合
     */
    public List<String> drillLog(String index, Map<String, String> paramMap, Tuple2<String, String> timeRange) {
        SearchRequest request = new SearchRequest(index);
        SearchSourceBuilder sb = new SearchSourceBuilder();

        // FilePath
        String filePath = paramMap.get("filePath").replace("opentrace", "trace");
        MatchQueryBuilder matchQueryBuilder = new MatchQueryBuilder("FilePath", filePath);

        RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("Time")
            .from(timeRange.getFirst())
            .to(timeRange.getSecond());

        QueryBuilder qb = QueryBuilders.boolQuery().must(matchQueryBuilder).must(rangeQueryBuilder);

        request.source(sb.query(qb).sort("SerialNo").size(MAX_SIZE));

        return queryTuple2List(request);
    }

    private List<String> queryTuple2List(SearchRequest request) {
        List<String> tuple2List = Lists.newArrayList();

        try {
            SearchResponse response = client.search(request, RequestOptions.DEFAULT);
            SearchHits searchHits = response.getHits();
            tuple2List = Stream.of(searchHits.getHits())
                .map(SearchHit::getSourceAsMap)
                .map(hit -> (String) hit.get("Message"))
                .collect(toList());
        } catch (IOException e) {
            LOG.error("failed to get logs");
        }

        return tuple2List;
    }

    private String findIndex(String time) {
        return indexProvider.find("cmp_agilit_trace", time);
    }

    /**
     * getTime
     *
     * @param time time
     * @return tuple2
     */
    public static Tuple2<String, String> getTime(String time) {
        long milliSecond = Long.parseLong(
            DateTimeFormatterUtil.getTimestamp(time, DateTimeFormatterUtil.UTC_FORMATTER_MILLI));

        return new Tuple2<>(
            DateTimeFormatterUtil.convertTimestampToUTCMilli(milliSecond - 10 * 1000),
            DateTimeFormatterUtil.convertTimestampToUTCMilli(milliSecond + 10 * 1000));
    }
}