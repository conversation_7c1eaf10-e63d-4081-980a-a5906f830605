/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.cmp.dvcallchainservice.business.configimport.excelparser;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cmp.dvcallchainservice.constant.CallChainExceptionCode;
import com.huawei.cmp.dvcallchainservice.model.ErrorCodeMapENColumnFormat;
import com.huawei.cmp.dvcallchainservice.model.ErrorCodeMapENModel;
import com.huawei.cmp.dvcallchainservice.util.CallchainStringUtils;
import com.huawei.cmp.foundation.container.excelreader.ExcelFileReader;
import com.huawei.cmp.foundation.service.exception.CMPException;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;

import java.util.List;

/**
 * parser
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2018.7.30
 */
public class ErrorCodeMapENExcelParser extends CallChainAbstractExcelParser {
    private static final OssLog DEBUG_LOGGER = OssLogFactory.getLogger(ErrorCodeMapENExcelParser.class);

    private static final OssLog RUN_LOGGER = OssLogFactory.getLogger(ErrorCodeMapENExcelParser.class);

    private static final String SHEET_NAME = "ErrorCodeMap_en";

    private boolean CHECK_FIRST_ROW_HAS_DATA_FLAG = true;

    public ErrorCodeMapENExcelParser(ExcelFileReader excelFileReader) {
        super(excelFileReader);
    }

    // 用于保存记录中缺省的字段
    private String solutionMotype = "";

    private String solutionVersion = "";

    private String nodeMoType = "";

    private String protocol = "";

    // 临时保存解析每行中每个字段的值
    private String solutionMotypeTemp = "";

    private String solutionVersionTemp = "";

    private String nodeMoTypeTemp = "";

    private String protocolTemp = "";

    private String errorCodeTemp = "";

    private String prefixENTemp = "";

    private String locationENTemp = "";

    /**
     * clear
     */
    public void clear() {
        solutionMotype = "";
        solutionVersion = "";
        nodeMoType = "";
        protocol = "";
        solutionMotypeTemp = "";
        solutionVersionTemp = "";
        nodeMoTypeTemp = "";
        protocolTemp = "";
        errorCodeTemp = "";
        prefixENTemp = "";
        locationENTemp = "";
    }

    /**
     * parseErrorCodeMapENExcel
     *
     * @param errorCodeMapENModels models
     */
    public void parseErrorCodeMapENExcel(List<ErrorCodeMapENModel> errorCodeMapENModels) {
        RUN_LOGGER.info("ErrorCodeMapENExcelParserHandler.parseErrorCodeMapENExcel in.");
        Sheet sheet = this.getSheetByName(SHEET_NAME);
        if (null == sheet) {
            RUN_LOGGER.info("There is no sheet {} in import file.", SHEET_NAME);
            return;
        }
        CHECK_FIRST_ROW_HAS_DATA_FLAG = true;
        getErrorCodeMapENsFromSheet(errorCodeMapENModels, sheet);
    }

    // 解析excel
    private void getErrorCodeMapENsFromSheet(List<ErrorCodeMapENModel> errorCodeMapENModels, Sheet sheet) {
        String sheetName = sheet.getSheetName();
        int firstRow = EXCEL_DATA_BEING_ROW;
        // 控制读取行数最大为5000行
        int lastRow = Math.min(sheet.getLastRowNum(), 5000);

        clear();

        for (int i = firstRow; i <= lastRow; i++) {
            Row row = sheet.getRow(i);

            // 校验是否空行
            if (checkBlankCells(row, ErrorCodeMapENColumnFormat.SOLUTION_MOTYPE.ordinal(),
                ErrorCodeMapENColumnFormat.LOCATION_EN.ordinal())) {
                DEBUG_LOGGER.debug("There is blank data in sheet {} row {}.", sheetName, i + 1);
                continue;
            }

            validateErrorCodeMapENData(row);
            getColumnValue(i, row);

            // 3、加入列表
            ErrorCodeMapENModel errorCodeMapENModel = new ErrorCodeMapENModel();
            errorCodeMapENModel.setId("");
            errorCodeMapENModel.setSolutionMoType(solutionMotypeTemp);
            errorCodeMapENModel.setSolutionVersion(solutionVersionTemp);
            errorCodeMapENModel.setNodeMoType(nodeMoTypeTemp);
            errorCodeMapENModel.setProtocol(protocolTemp);
            errorCodeMapENModel.setErrorCode(errorCodeTemp);
            errorCodeMapENModel.setPrefixEN(prefixENTemp);
            errorCodeMapENModel.setLocationEN(locationENTemp);
            errorCodeMapENModels.add(errorCodeMapENModel);
        }
    }

    private void getColumnValue(int i, Row row) {
        // 解决方案
        solutionMotypeTemp = getCcValueFromCell(row.getCell(ErrorCodeMapENColumnFormat.SOLUTION_MOTYPE.ordinal()));
        if (solutionMotypeTemp.length() == 0) {
            DEBUG_LOGGER.debug("solution motype in sheet {} row {} is empty.", SHEET_NAME, i + 1);
            solutionMotypeTemp = solutionMotype;
        }
        if (!StringUtils.equals(solutionMotype, solutionMotypeTemp)) {
            solutionMotype = solutionMotypeTemp;
        }

        // 解决方案版本
        solutionVersionTemp = getCcValueFromCell(row.getCell(ErrorCodeMapENColumnFormat.SOLUTION_VERSION.ordinal()));
        if (solutionVersionTemp.length() == 0) {
            DEBUG_LOGGER.debug("solution version in sheet {} row {} is empty.", SHEET_NAME, i + 1);
            solutionVersionTemp = solutionVersion;
        }
        solutionVersion = solutionVersionTemp;

        // 节点MOType
        nodeMoTypeTemp = getCcValueFromCell(row.getCell(ErrorCodeMapENColumnFormat.NODE_MOTYPE.ordinal()));
        if (nodeMoTypeTemp.length() == 0) {
            DEBUG_LOGGER.debug("node motype in sheet {} row {} is empty.", SHEET_NAME, i + 1);
            nodeMoTypeTemp = nodeMoType;
        }
        nodeMoType = nodeMoTypeTemp;

        // 协议类型
        protocolTemp = getCcValueFromCell(row.getCell(ErrorCodeMapENColumnFormat.PROTOCOL.ordinal()));
        if (protocolTemp.length() == 0) {
            DEBUG_LOGGER.debug("protocol in sheet {} row {} is empty.", SHEET_NAME, i + 1);
            protocolTemp = protocol;
        }
        protocol = protocolTemp;

        // 错误返回码
        errorCodeTemp = this.getIntegerValueFromCell(row.getCell(ErrorCodeMapENColumnFormat.ERROR_CODE.ordinal()));

        // 页面前缀
        prefixENTemp = getCcValueFromCell(row.getCell(ErrorCodeMapENColumnFormat.PREFIX_EN.ordinal()));

        // 文件地址
        locationENTemp = getCcValueFromCell(row.getCell(ErrorCodeMapENColumnFormat.LOCATION_EN.ordinal()));
    }

    // 校验模板信息
    private void validateErrorCodeMapENData(Row row) {
        if (CHECK_FIRST_ROW_HAS_DATA_FLAG) {
            validateFieldByGivenMinLength(row, 1);
            CHECK_FIRST_ROW_HAS_DATA_FLAG = false;
        } else {
            validateFieldByGivenMinLength(row, 0);
        }

        // 返回码校验 长度、 非数字
        validateLengthAndIllegalChar(SHEET_NAME, row, ErrorCodeMapENColumnFormat.ERROR_CODE.ordinal(),
                1, 64);

        // 错误描述页面前缀 长度校验、特殊字符校验
        validateLength(SHEET_NAME, row, ErrorCodeMapENColumnFormat.PREFIX_EN.ordinal(), 0, 512);
        validatePrefix(row);

        // 文件地址  长度校验、特殊字符校验
        validateLength(SHEET_NAME, row, ErrorCodeMapENColumnFormat.LOCATION_EN.ordinal(), 1, 512);
        validateLocation(row);
    }

    private void validateFieldByGivenMinLength(Row row, int min) {
        // 解决方案版本校验 长度校验、特殊字符校验
        validateLengthAndIllegalChar(SHEET_NAME, row, ErrorCodeMapENColumnFormat.SOLUTION_MOTYPE.ordinal(),
                min, 128);

        validateLengthAndIllegalChar(SHEET_NAME, row, ErrorCodeMapENColumnFormat.SOLUTION_VERSION.ordinal(),
                min, 64);

        validateLengthAndIllegalChar(SHEET_NAME, row, ErrorCodeMapENColumnFormat.NODE_MOTYPE.ordinal(), min,
                256);

        validateLengthAndIllegalChar(SHEET_NAME, row, ErrorCodeMapENColumnFormat.PROTOCOL.ordinal(), min, 64);
    }

    private void validatePrefix(Row row) {
        Cell cell = row.getCell(ErrorCodeMapENColumnFormat.PREFIX_EN.ordinal());
        if (null != cell) {
            String prefix = getValueFromCell(cell);
            // 特殊字符校验
            if (CallchainStringUtils.hasUrlIllegalChar(prefix)) {
                RUN_LOGGER.error("The cellValue {} has illegal character in row {}.", prefix, row.getRowNum() + 1);
                String[] args = new String[] {
                    SHEET_NAME, getCellLocation(row, ErrorCodeMapENColumnFormat.PREFIX_EN.ordinal()), prefix
                };
                throw new CMPException(CallChainExceptionCode.IMPORT_FILE_HAS_INVALID_CHAR, args);
            }
        }
    }

    private void validateLocation(Row row) {
        Cell cell = row.getCell(ErrorCodeMapENColumnFormat.LOCATION_EN.ordinal());
        if (null != cell) {
            String location = getValueFromCell(cell);
            // 特殊字符校验
            if (CallchainStringUtils.hasUrlIllegalChar(location)) {
                RUN_LOGGER.error("The cellValue {} has illegal character in row {}.", location, row.getRowNum() + 1);
                String[] args = new String[] {
                    SHEET_NAME, getCellLocation(row, ErrorCodeMapENColumnFormat.LOCATION_EN.ordinal()), location
                };
                throw new CMPException(CallChainExceptionCode.IMPORT_FILE_HAS_INVALID_CHAR, args);
            }
        }
    }
}
