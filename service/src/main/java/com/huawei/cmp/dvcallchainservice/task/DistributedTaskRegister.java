/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.cmp.dvcallchainservice.task;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cmp.dvcallchainservice.task.util.CallchainTaskUtils;
import com.huawei.cmp.foundation.task.constant.TaskConcurrentLevel;
import com.huawei.cmp.foundation.task.constant.TaskStatus;
import com.huawei.cmp.foundation.task.constant.TaskType;
import com.huawei.cmp.foundation.task.service.DistributedTaskMBean;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.PostConstruct;

/**
 * 任务注册类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/8/24 21:29
 */
@Component
public class DistributedTaskRegister {
    private static final OssLog RUN_LOGGER = OssLogFactory.getLogger(DistributedTaskRegister.class);

    private static final String TASK_GROUP = "callchain";

    private static final String TASK_NAME = "DurationMetric";

    // 默认两分钟执行一次时延统计
    private static final String DEFALUT_EXPRESSION = "0 */2 * * * ?";

    @Autowired
    @Qualifier(value = "distributedTask")
    private DistributedTaskMBean dTask;

    private String expression;

    @Autowired
    @Qualifier(value = "businessDurationAggTask")
    private BusinessDurationAggTask aggTask;

    /**
     * 初始化
     */
    @PostConstruct
    public void initDistributedTask() {
        ExecutorService singleThreadExecutor = Executors.newSingleThreadExecutor();
        singleThreadExecutor.execute(() -> registTask());
        singleThreadExecutor.shutdown();
    }

    private void registTask() {
        Thread.currentThread().setName("Thread-callchain-regist-business-durationTask");
        RUN_LOGGER.info("Begin to regist business duration agg task.");
        expression = CallchainTaskUtils.getConfigByKey("cron");
        if (StringUtils.isEmpty(expression)) {
            expression = DEFALUT_EXPRESSION;
        }
        dTask.register(TASK_NAME, TASK_GROUP, expression, aggTask, TaskType.CRON, 0, TaskConcurrentLevel.NORMAL,
            TaskStatus.RUNNING, null);
        RUN_LOGGER.info("End to regist business duration agg task.");
    }
}
