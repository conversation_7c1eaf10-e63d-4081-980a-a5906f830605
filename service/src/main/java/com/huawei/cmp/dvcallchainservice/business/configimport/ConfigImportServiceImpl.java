/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.cmp.dvcallchainservice.business.configimport;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.cmp.dvcallchainservice.business.configimport.excelparser.ErrorCodeMapCHExcelParser;
import com.huawei.cmp.dvcallchainservice.business.configimport.excelparser.ErrorCodeMapENExcelParser;
import com.huawei.cmp.dvcallchainservice.business.configimport.excelparser.ReturnCodeExcelParser;
import com.huawei.cmp.dvcallchainservice.business.configimport.excelparser.SampleRateExcelParser;
import com.huawei.cmp.dvcallchainservice.business.configimport.excelparser.SearchParamExcelParser;
import com.huawei.cmp.dvcallchainservice.business.configimport.excelparser.ServiceStepExcelParser;
import com.huawei.cmp.dvcallchainservice.business.configimport.excelparser.ServiceTypeExcelParser;
import com.huawei.cmp.dvcallchainservice.constant.CallChainExceptionCode;
import com.huawei.cmp.dvcallchainservice.constant.CallchainConstant;
import com.huawei.cmp.dvcallchainservice.dao.ErrorCodeMapDao;
import com.huawei.cmp.dvcallchainservice.dao.ReturnCodeDao;
import com.huawei.cmp.dvcallchainservice.dao.SampleRateDao;
import com.huawei.cmp.dvcallchainservice.dao.SearchParameterDao;
import com.huawei.cmp.dvcallchainservice.dao.SearchParameterPresetOptionDao;
import com.huawei.cmp.dvcallchainservice.dao.ServiceStepDao;
import com.huawei.cmp.dvcallchainservice.dao.ServiceTypeDao;
import com.huawei.cmp.dvcallchainservice.elasticsearch.IndexRepository;
import com.huawei.cmp.dvcallchainservice.model.ErrorCodeMapCHModel;
import com.huawei.cmp.dvcallchainservice.model.ErrorCodeMapENModel;
import com.huawei.cmp.dvcallchainservice.model.ReturnCodeModel;
import com.huawei.cmp.dvcallchainservice.model.SampleRateModel;
import com.huawei.cmp.dvcallchainservice.model.SearchParameterModel;
import com.huawei.cmp.dvcallchainservice.model.SearchParameterPresetModel;
import com.huawei.cmp.dvcallchainservice.model.ServiceStepModel;
import com.huawei.cmp.dvcallchainservice.model.ServiceTypeModel;
import com.huawei.cmp.dvcallchainservice.util.CallchainServiceConfigUtil;
import com.huawei.cmp.dvcallchainservice.util.FilePathCheckUtil;
import com.huawei.cmp.dvcallchainservice.util.ImportUtil;
import com.huawei.cmp.dvcallchainservice.util.PropertyDictionaryUtil;
import com.huawei.cmp.dvcallchainservice.util.ValidateUtil;
import com.huawei.cmp.dvcallchainservice.util.ZipUtils;
import com.huawei.cmp.foundation.container.download.model.ExportModel;
import com.huawei.cmp.foundation.container.excelreader.ExcelFileReader;
import com.huawei.cmp.foundation.service.exception.CMPException;
import com.huawei.cmp.foundation.service.log.OperationLogMsg;
import com.huawei.cmp.foundation.container.util.CMPConfigUtil;
import com.huawei.cmp.foundation.service.util.NatsComp;
import com.huawei.i2000.cbb.security.file.FileType;

import com.alibaba.fastjson2.JSON;

import io.nats.client.Connection;
import lombok.Setter;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * 属性字典导入实现类
 * <功能详细描述>
 *
 * <AUTHOR>
 * @version DigitalView V100R001C20 , 2016年4月19日
 * @since V100R001C20
 */
@Service
public class ConfigImportServiceImpl implements ConfigImportService {
    private static final OssLog DEBUG_LOGGER = OssLogFactory.getLogger(ConfigImportServiceImpl.class);

    private static final OssLog RUN_LOGGER = OssLogFactory.getLogger(ConfigImportServiceImpl.class);

    private static final Long MAX_FILE_SIZE = 2 * 1024 * 1024L;

    private static String searchParamIndex;

    private static String searchParamType;

    private static Integer searchParamMaxCount;

    private static String dictIndex;

    private static String dictType;

    private static Integer dictPerSearchCount;

    private static Integer sampleRateMaxCount;

    private static String sampleRateMountPath;

    private static final String CONFIG_IMPORT_MAXZIE = CallchainServiceConfigUtil.getProperty("config.import.maxsize");

    private static final int CONFIG_IMPORT_GROUP_MAX = 100;

    // 上传文件类型白名单
    private static final String[] WHITE_LIST = new String[] {"zip", "xls", "xlsx"};

    static {
        searchParamIndex = CMPConfigUtil.getUnionConfig().getString(CallchainConstant.Config.SEARCH_PARAMETER_INDEX);
        searchParamType = CMPConfigUtil.getUnionConfig().getString(CallchainConstant.Config.SEARCH_PARAMETER_TYPE);
        searchParamMaxCount = 100;
        dictIndex = ".cmpdictionary";
        dictType = CMPConfigUtil.getUnionConfig().getString(CallchainConstant.Config.CALLCHAIN_DICTIONARY_TYPE);
        dictPerSearchCount =
                CMPConfigUtil.getUnionConfig().getInt(CallchainConstant.Config.DICTIONARY_NUMBER_PER_SEARCH);
        sampleRateMaxCount = CMPConfigUtil.getUnionConfig().getInt(CallchainConstant.Config.MAX_SAMPLE_RATE_COUNT);
        sampleRateMountPath = CMPConfigUtil.getUnionConfig().getString(CallchainConstant.Config.SAMPLE_RATE_MOUNTPATH);
    }

    @Autowired
    private ReturnCodeDao returnCodeDAO;

    @Autowired
    private SearchParameterDao searchParameterDAO;

    @Autowired
    private SampleRateDao sampleRateDAO;

    @Autowired
    private SearchParameterPresetOptionDao searchParameterPresetOptionDAO;

    @Autowired
    private ServiceTypeDao serviceTypeDAO;

    @Autowired
    private ServiceStepDao serviceStepDAO;

    @Autowired
    private ErrorCodeMapDao errorCodeMapDAO;

    @Autowired
    private PropertyDictionaryUtil propertyDictionaryUtil;

    @Autowired
    @Setter
    private IndexRepository indexRepository;

    @Autowired
    @Setter
    private RestHighLevelClient client;

    private void saveServiceStepToDb(List<ServiceStepModel> serviceSteps) {
        if (CollectionUtils.isNotEmpty(serviceSteps)) {
            RUN_LOGGER.info("service step size is {}", serviceSteps.size());
            serviceStepDAO.saveServiceStep(serviceSteps);
            RUN_LOGGER.info("success to save service step to db.");
        }
    }

    private void saveServiceTypeToDB(List<ServiceTypeModel> serviceTypes) {
        if (CollectionUtils.isNotEmpty(serviceTypes)) {
            RUN_LOGGER.info("service type size is {}", serviceTypes.size());
            serviceTypeDAO.saveServiceType(serviceTypes);
            RUN_LOGGER.info("success to save service type to db.");
        }
    }

    @Override
    public synchronized boolean importConfigurationFromFile(HttpContext context, ExportModel fileInfo) {
        RUN_LOGGER.info("ConfigImportServiceImpl.importDictionaryFromFile in. fileInfo={}", fileInfo);
        String fileName = fileInfo.getFileName();
        String targetObj = String.format(Locale.ENGLISH, "FileName=%s", fileName);
        CMPException exception = new CMPException(CallChainExceptionCode.IMPORT_FILE_ERROR);
        exception.setOperationLog(new OperationLogMsg(targetObj));

        if (StringUtils.isEmpty(fileName)) {
            RUN_LOGGER.error("Failed to import configutation data, file name is empty.");
            throw exception;
        }

        boolean result = false;
        try {
            validateFile(fileInfo, fileName, targetObj);
            File file = new File(fileInfo.getFilePath());
            String fileType = FileType.getInst().getFileType(file);
            if ("zip".equals(fileType)) {
                ZipUtils.checkZipBomb(file, file.getParent());
            }
            if (fileName.endsWith(".xls") || fileName.endsWith(".xlsx")) {
                RUN_LOGGER.info("Import file is excel file.");
                result = importByExcel(fileInfo.getFilePath(), fileInfo.getFileName());
            } else {
                RUN_LOGGER.info("Import file is zip file.");
                result = importByZip(file);
            }
            pubRefreshCache();
        } catch (IOException e) {
            RUN_LOGGER.error("Failed to import configutation data.", e);
            throw exception;
        }
        return result;
    }

    private File validateFile(ExportModel fileInfo, String fileName, String targetObj) throws IOException {
        // 校验是否存在目录遍历攻击
        try {
            FilePathCheckUtil.pathSafeCheck(fileName);
            FilePathCheckUtil.pathSafeCheck(fileInfo.getFilePath());
        } catch (CMPException e) {
            RUN_LOGGER.error("Failed to import configutation data, file name or filePath is invalid.");
            e.setOperationLog(new OperationLogMsg(targetObj));
            throw e;
        }

        File file = new File(fileInfo.getFilePath());

        // 根据文件头校验文件类型
        if (!FileType.getInst().checkFileType(fileInfo.getFilePath(), WHITE_LIST)) {
            RUN_LOGGER.error("Failed to import configutation data, file type is illegal");
            CMPException exception = new CMPException(CallChainExceptionCode.IMPORT_FILE_ERROR);
            exception.setOperationLog(new OperationLogMsg(targetObj));
            throw exception;
        }

        // 校验文件大小
        if (!file.exists() || file.length() > MAX_FILE_SIZE) {
            RUN_LOGGER.error("Failed to import configutation data, file is not exists or file size larger than 2M.");
            CMPException exception = new CMPException(CallChainExceptionCode.IMPORT_FILE_ERROR);
            exception.setOperationLog(new OperationLogMsg(targetObj));
            throw exception;
        }
        return file;
    }

    /**
     * 按包管理的额格式处理导入的包
     *
     * @param file 包管理格式的导入包
     * @return 导入结果
     */
    public boolean importByZip(File file) {
        RUN_LOGGER.info("Begin to import by zip {}", file.getName());
        String targetObj = String.format(Locale.ENGLISH, "FileName=%s", file.getName());
        File excelFile;
        try {
            FilePathCheckUtil.isZipFormat(file);
            excelFile = ImportUtil.getTemplateFromZip(file);
            if (!excelFile.exists()) {
                throw new CMPException("Get template file form zip package failed.");
            }
            String fileType = FileType.getInst().getFileType(excelFile);
            if ("zip".equals(fileType)) {
                ZipUtils.checkZipBomb(excelFile, excelFile.getParent());
            }
        } catch (CMPException e) {
            RUN_LOGGER.error("Get template from install package failed.");
            e.setOperationLog(new OperationLogMsg(targetObj));
            throw e;
        } catch (IOException e) {
            RUN_LOGGER.error("Failed to import configutation data, File={}, e={}", file.getName(), e.getMessage());
            CMPException exception = new CMPException(CallChainExceptionCode.IMPORT_FILE_ERROR);
            exception.setOperationLog(new OperationLogMsg(targetObj));
            throw exception;
        }
        return importByExcel(excelFile.getPath(), excelFile.getName());
    }

    /**
     * 按调用链模板处理导入的数据
     *
     * @param filePath 文件路径
     * @param fileName 文件名
     * @return 处理结果
     */
    public boolean importByExcel(String filePath, String fileName) {
        RUN_LOGGER.info("Begin to import by excel {}", fileName);
        ExcelFileReader reader = new ExcelFileReader(filePath, fileName);

        // 返回码
        parseReturnCode(reader);
        // 查询参数
        parseSearchParameter(reader);
        // 采样率
        parseSampleRate(reader);
        // 业务类型
        parseServiceType(reader);
        // 业务参数
        parseServiceStep(reader);
        // 错误码中文映射文件
        parseErrorCodeMapCH(reader);
        // 错误码英文映射文件
        parseErrorCodeMapEN(reader);

        if (validateOutOfSize()) {
            RUN_LOGGER.error("Meta datas is out of bounds!");
            CMPException e = new CMPException(CallChainExceptionCode.IMPORT_OUT_OF_MAX_SIZE);
            e.setOperationLog(new OperationLogMsg(fileName));
            throw e;
        }

        RUN_LOGGER.info("Import configuration by excel {} successful.", fileName);
        return true;
    }

    private boolean validateOutOfSize() {
        List<SearchParameterModel> searchParameterModels = searchParameterDAO.queryAllSearchParamters();
        long groupNum = searchParameterModels.stream().map(SearchParameterModel::getGroupList).flatMap(Collection::stream).distinct().count();
        if (groupNum > CONFIG_IMPORT_GROUP_MAX) {
            RUN_LOGGER.error("group number is out of bounds!");
            throw new CMPException(CallChainExceptionCode.IMPORT_FILE_GROUP_OVER_LIMIT);
        }

        List<Integer> countList = new ArrayList<>();
        countList.add(returnCodeDAO.getReturnCodes().size());
        countList.add(searchParameterModels.size());
        countList.add(sampleRateDAO.queryAllSampleRate().size());
        countList.add(serviceTypeDAO.queryAllServiceTypes().size());
        countList.add(serviceStepDAO.queryAllServiceSteps().size());
        countList.add(errorCodeMapDAO.queryAddressEHCount());
        countList.add(errorCodeMapDAO.queryAddressCHCount());

        int maxCount = countList.get(0);
        for (Integer count : countList) {
            if (count > maxCount) {
                maxCount = count;
            }
        }
        return maxCount > Integer.parseInt(CONFIG_IMPORT_MAXZIE);
    }

    @Override
    public void importConfigurationByPkgManager(File file) {
        importByZip(file);
        pubRefreshCache();
    }

    private void parseServiceStep(ExcelFileReader reader) {
        List<ServiceStepModel> serviceStepModelList = new ArrayList<>();
        ServiceStepExcelParser serviceStepExcelParserHandler = new ServiceStepExcelParser(reader);
        serviceStepExcelParserHandler.parseExcel(serviceStepModelList);
        if (CollectionUtils.isEmpty(serviceStepModelList)) {
            RUN_LOGGER.info("service step data is empty.");
            return;
        }
        ValidateUtil.validateList(serviceStepModelList);
        deleteExistedSolutionInServiceStep(serviceStepModelList);
        saveServiceStepToDb(serviceStepModelList);
        propertyDictionaryUtil.init();
    }

    private void parseServiceType(ExcelFileReader reader) {
        List<ServiceTypeModel> serviceTypeModelList = new ArrayList<>();
        ServiceTypeExcelParser serviceTypeExcelParserHandler = new ServiceTypeExcelParser(reader);
        serviceTypeExcelParserHandler.parseExcel(serviceTypeModelList);
        if (CollectionUtils.isEmpty(serviceTypeModelList)) {
            RUN_LOGGER.info("service type data is empty.");
            return;
        }
        ValidateUtil.validateList(serviceTypeModelList);
        deleteExistedSolutionInServiceType(serviceTypeModelList);
        saveServiceTypeToDB(serviceTypeModelList);
    }

    // 处理错误码中文详情文件
    private void parseErrorCodeMapCH(ExcelFileReader reader) {
        List<ErrorCodeMapCHModel> errorCodeMapCHModelList = new ArrayList<>();
        ErrorCodeMapCHExcelParser errorCodeMapCHExcelParserHandler = new ErrorCodeMapCHExcelParser(reader);
        // POI类解析excel，并校验长度和字符串
        errorCodeMapCHExcelParserHandler.parseErrorCodeMapCHExcel(errorCodeMapCHModelList);
        if (CollectionUtils.isEmpty(errorCodeMapCHModelList)) {
            RUN_LOGGER.info("error code map ch data is empty.");
            return;
        }
        ValidateUtil.validateList(errorCodeMapCHModelList);
        deleteExistedSolutionInErrorCodeMapCH(errorCodeMapCHModelList);
        saveErrorCodeMapCHToDB(errorCodeMapCHModelList);
    }

    // 处理错误码英文详情文件
    private void parseErrorCodeMapEN(ExcelFileReader reader) {
        List<ErrorCodeMapENModel> errorCodeMapENModelList = new ArrayList<>();
        ErrorCodeMapENExcelParser errorCodeMapENExcelParserHandler = new ErrorCodeMapENExcelParser(reader);
        // POI类解析excel，并校验长度和字符串
        errorCodeMapENExcelParserHandler.parseErrorCodeMapENExcel(errorCodeMapENModelList);
        if (CollectionUtils.isEmpty(errorCodeMapENModelList)) {
            RUN_LOGGER.info("error code map en data is empty.");
            return;
        }
        ValidateUtil.validateList(errorCodeMapENModelList);
        deleteExistedSolutionInErrorCodeMapEN(errorCodeMapENModelList);
        saveErrorCodeMapENToDB(errorCodeMapENModelList);
    }

    private void parseSearchParameter(ExcelFileReader reader) {
        // 查询参数
        List<SearchParameterModel> searchParameterModelList = new ArrayList<>();
        List<SearchParameterPresetModel> searchParameterPresetModelList = new ArrayList<>();
        SearchParamExcelParser searchParamParser = new SearchParamExcelParser(reader);
        searchParamParser.parseSearchParamExcel(searchParameterModelList, searchParameterPresetModelList);
        if (CollectionUtils.isEmpty(searchParameterModelList)) {
            RUN_LOGGER.info("search parameter data is empty.");
            return;
        }
        ValidateUtil.validateList(searchParameterModelList);
        ValidateUtil.validateList(searchParameterPresetModelList);
        deleteExistedSolutionInSearchParameter(searchParameterModelList);
        saveSearchParamsToDB(searchParameterModelList, searchParameterPresetModelList);
    }

    private void parseReturnCode(ExcelFileReader reader) {
        ReturnCodeExcelParser returnCodeParser = new ReturnCodeExcelParser(reader);
        List<ReturnCodeModel> rtcList = returnCodeParser.parseReturnCode();
        if (CollectionUtils.isEmpty(rtcList)) {
            RUN_LOGGER.info("returncode data is empty.");
            return;
        }
        ValidateUtil.validateList(rtcList);
        deleteExistedSolution(rtcList);
        saveReturnCodeToDB(rtcList);
    }

    private void parseSampleRate(ExcelFileReader reader) {
        List<SampleRateModel> sampleRateModelList = new ArrayList<>();
        SampleRateExcelParser sampleRateParser = new SampleRateExcelParser(reader);
        sampleRateParser.parseSampleRateExcel(sampleRateModelList);
        int totalNumber = sampleRateDAO.querySampleRateCountByFilter();
        if (!sampleRateModelList.isEmpty()) {
            if (totalNumber + sampleRateModelList.size() > sampleRateMaxCount) {
                RUN_LOGGER.error("TotalNumber has reached  the max line of {}", sampleRateMaxCount);
                throw new CMPException(CallChainExceptionCode.CALLCHAIN_SAMPLE_RATE_IMPORT_REACH_MAX);
            }
            ValidateUtil.validateList(sampleRateModelList);
            saveSampleRates(sampleRateModelList);
        }
    }

    private void saveSampleRates(List<SampleRateModel> sampleRateModelList) {
        List<SampleRateModel> currentSampleRateModelList = sampleRateDAO.queryAllSampleRate();
        List<SampleRateModel> finallySampleRateModelList = new ArrayList<>();

        if (currentSampleRateModelList.isEmpty()) {
            sampleRateDAO.saveSampleRates(sampleRateModelList);
            return;
        }
        Map<String, Integer> sampleRateMap = new HashMap<>();
        for (SampleRateModel currentSampleRate : currentSampleRateModelList) {
            String key =
                    currentSampleRate.getZookeeperIp()
                            + currentSampleRate.getZookeeperPort().toString()
                            + currentSampleRate.getZookeeperMountPath()
                            + currentSampleRate.getServiceTypeId();
            sampleRateMap.put(key, currentSampleRate.getSampleRate());
        }
        for (SampleRateModel sampleRate : sampleRateModelList) {
            if (!judgeExist(sampleRateMap, sampleRate)) {
                finallySampleRateModelList.add(sampleRate);
                continue;
            }
            if (!judgeSame(sampleRateMap, sampleRate)) {
                sampleRateDAO.updateSampleRate(sampleRate);
            }
        }
        if (!finallySampleRateModelList.isEmpty()) {
            sampleRateDAO.saveSampleRates(finallySampleRateModelList);
        }
    }

    private boolean judgeExist(Map<String, Integer> sampleRatesMap, SampleRateModel currentSampleRate) {
        StringBuilder sampleRatekey = new StringBuilder("");
        sampleRatekey
                .append(currentSampleRate.getZookeeperIp())
                .append(currentSampleRate.getZookeeperPort())
                .append(currentSampleRate.getZookeeperMountPath())
                .append(currentSampleRate.getServiceTypeId());
        return sampleRatesMap.containsKey(sampleRatekey.toString());
    }

    private boolean judgeSame(Map<String, Integer> sampleRatesMap, SampleRateModel currentSampleRate) {
        StringBuffer sampleRatekey = new StringBuffer("");
        sampleRatekey
                .append(currentSampleRate.getZookeeperIp())
                .append(currentSampleRate.getZookeeperPort())
                .append(currentSampleRate.getZookeeperMountPath())
                .append(currentSampleRate.getServiceTypeId());
        int tmpSampleRate = sampleRatesMap.get(sampleRatekey.toString());
        return (tmpSampleRate == currentSampleRate.getSampleRate());
    }

    private void deleteExistedSolution(List<ReturnCodeModel> rtcList) {
        RUN_LOGGER.info("ConfigImportServiceImpl.deleteExistedSolution in. rtcList size is {}", rtcList.size());

        // 用于重复校验
        List<String> solutionMotypes = new ArrayList<>();
        String moType;
        for (ReturnCodeModel returnCodeModel : rtcList) {
            moType = returnCodeModel.getSolutionMoType();
            if (!solutionMotypes.contains(moType)) {
                solutionMotypes.add(moType);
            }
        }

        if (CollectionUtils.isNotEmpty(solutionMotypes)) {
            RUN_LOGGER.info("solution motypes to be deleted are {}.", solutionMotypes);
            returnCodeDAO.deleteExistedSolution(solutionMotypes);
        }
    }

    private void deleteExistedSolutionInSearchParameter(List<SearchParameterModel> searchParameterModelList) {
        RUN_LOGGER.info(
                "ConfigImportServiceImpl.deleteExistedSolutionInSearchParameter in. size = {}",
                searchParameterModelList.size());

        // 用于重复校验
        List<String> solutionMotypes = new ArrayList<>();
        String moType;
        for (SearchParameterModel model : searchParameterModelList) {
            moType = model.getSolutionMoType();
            if (!solutionMotypes.contains(moType)) {
                solutionMotypes.add(moType);
            }
        }

        if (CollectionUtils.isNotEmpty(solutionMotypes)) {
            RUN_LOGGER.info("solution motypes to be deleted are {}.", solutionMotypes);
            searchParameterDAO.deleteExistedSolution(solutionMotypes);
            searchParameterPresetOptionDAO.deleteExistedSolution(solutionMotypes);
        }
    }

    private void deleteExistedSolutionInServiceType(List<ServiceTypeModel> serviceTypeModels) {
        RUN_LOGGER.info(
                "ConfigImportServiceImpl.deleteExistedSolutionInSearchParameter in. size = {}",
                serviceTypeModels.size());

        // 用于重复校验
        List<String> solutionMotypes = new ArrayList<>();
        String moType;
        for (ServiceTypeModel model : serviceTypeModels) {
            moType = model.getSolutionMoType();
            if (!solutionMotypes.contains(moType)) {
                solutionMotypes.add(moType);
            }
        }

        if (CollectionUtils.isNotEmpty(solutionMotypes)) {
            RUN_LOGGER.info("solution motypes to be deleted are {}.", solutionMotypes);
            serviceTypeDAO.deleteExistedSolution(solutionMotypes);
        }
    }

    private void deleteExistedSolutionInServiceStep(List<ServiceStepModel> serviceStepModels) {
        RUN_LOGGER.info(
                "ConfigImportServiceImpl.deleteExistedSolutionInSearchParameter in. size = {]",
                serviceStepModels.size());

        // 用于重复校验
        List<String> solutionMotypes = new ArrayList<>();
        String moType;
        for (ServiceStepModel model : serviceStepModels) {
            moType = model.getSolutionMoType();
            if (!solutionMotypes.contains(moType)) {
                solutionMotypes.add(moType);
            }
        }

        if (CollectionUtils.isNotEmpty(solutionMotypes)) {
            RUN_LOGGER.info("solution motypes to be deleted are {}.", solutionMotypes);
            serviceStepDAO.deleteExistedSolution(solutionMotypes);
        }
    }

    private void deleteExistedSolutionInErrorCodeMapCH(List<ErrorCodeMapCHModel> errorCodeMapCHModels) {
        RUN_LOGGER.info(
                "ConfigImportServiceImpl.deleteExistedSolutionInSearchParameter in. size = {}",
                errorCodeMapCHModels.size());

        // 用于重复校验
        List<String> solutionMotypes = new ArrayList<>();
        String moType;
        for (ErrorCodeMapCHModel model : errorCodeMapCHModels) {
            moType = model.getSolutionMoType();
            if (!solutionMotypes.contains(moType)) {
                solutionMotypes.add(moType);
            }
        }

        if (CollectionUtils.isNotEmpty(solutionMotypes)) {
            RUN_LOGGER.info("solution motypes to be deleted are {}.", solutionMotypes);
            errorCodeMapDAO.deleteExistedSolutionCH(solutionMotypes);
        }
    }

    private void deleteExistedSolutionInErrorCodeMapEN(List<ErrorCodeMapENModel> errorCodeMapENModels) {
        RUN_LOGGER.info(
                "ConfigImportServiceImpl.deleteExistedSolutionInSearchParameter in. size = {}",
                errorCodeMapENModels.size());

        // 用于重复校验
        List<String> solutionMotypes = new ArrayList<>();
        String moType;
        for (ErrorCodeMapENModel model : errorCodeMapENModels) {
            moType = model.getSolutionMoType();
            if (!solutionMotypes.contains(moType)) {
                solutionMotypes.add(moType);
            }
        }

        if (CollectionUtils.isNotEmpty(solutionMotypes)) {
            RUN_LOGGER.info("solution motypes to be deleted are {}.", solutionMotypes);
            errorCodeMapDAO.deleteExistedSolutionEN(solutionMotypes);
        }
    }

    private void saveErrorCodeMapCHToDB(List<ErrorCodeMapCHModel> errorCodeMapCHs) {
        if (CollectionUtils.isNotEmpty(errorCodeMapCHs)) {
            RUN_LOGGER.info("error code map ch size is {}", errorCodeMapCHs.size());
            errorCodeMapDAO.saveErrorCodeMapCH(errorCodeMapCHs);
            RUN_LOGGER.info("success to save error code map ch to db.");
        }
    }

    private void saveErrorCodeMapENToDB(List<ErrorCodeMapENModel> errorCodeMapENs) {
        if (CollectionUtils.isNotEmpty(errorCodeMapENs)) {
            RUN_LOGGER.info("error code map en size is {}", errorCodeMapENs.size());
            errorCodeMapDAO.saveErrorCodeMapEN(errorCodeMapENs);
            RUN_LOGGER.info("success to save error code map en to db.");
        }
    }

    private void saveReturnCodeToDB(List<ReturnCodeModel> rtcList) {
        returnCodeDAO.saveReturnCodes(rtcList);
        RUN_LOGGER.info("Save return code success. Begin to notify nas update returncode.");
        pubReturnCode(rtcList);
    }

    private void pubReturnCode(List<ReturnCodeModel> rtcList) {
        NatsComp callChainNats = NatsComp.getInstance();
        Connection connection = NatsComp.getConnection();
        if (connection.getStatus() == Connection.Status.CONNECTED) {
            RUN_LOGGER.info("nats connect successful.");
            callChainNats.pub(CallchainConstant.ReturnCode.UPDATE_RETURNCODE_TOPIC, JSON.toJSONString(rtcList));
            RUN_LOGGER.info("success to notify nas update returncode.");
        }
    }

    private void saveSearchParamsToDB(
            List<SearchParameterModel> searchParameterModelList,
            List<SearchParameterPresetModel> searchParameterPresetModelList) {
        searchParameterDAO.saveSearchParameters(searchParameterModelList);

        if (CollectionUtils.isEmpty(searchParameterPresetModelList)) {
            RUN_LOGGER.info("preset value of search parameter data is empty.");
        } else {
            searchParameterPresetOptionDAO.saveSearchParametersPreset(searchParameterPresetModelList);
        }
    }

    private void pubRefreshCache() {
        // 发布nats消息更新集群缓存
        DEBUG_LOGGER.debug("begin to notify nats to update configs.");
        NatsComp callChainNats = NatsComp.getInstance();
        callChainNats.pub("com.huawei.cmp.callchain.updatereturncode", "update returncode.");
        DEBUG_LOGGER.debug("end to notify nats update configs.");
    }
}
