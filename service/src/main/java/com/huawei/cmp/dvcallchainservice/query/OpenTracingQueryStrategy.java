/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.cmp.dvcallchainservice.query;

import static java.util.stream.Collectors.toList;

import com.huawei.baize.avauger.util.CollectionUtils;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cmp.dvcallchainservice.constant.CallchainConstant;
import com.huawei.cmp.dvcallchainservice.model.CallChainDetailVO;
import com.huawei.cmp.dvcallchainservice.model.CallChainLogVO;
import com.huawei.cmp.dvcallchainservice.model.ChainQueryParamModel;
import com.huawei.cmp.dvcallchainservice.model.callchainquery.ChainLogInfoQueryDTO;
import com.huawei.cmp.dvcallchainservice.model.callchainquery.SingleChainByTraceIdQueryDTO;
import com.huawei.cmp.dvcallchainservice.model.callchainquery.TraceByFilterQueryDTO;
import com.huawei.cmp.dvcallchainservice.util.CallchainStringUtils;
import com.huawei.cmp.dvcallchainservice.util.DateTimeFormatterUtil;
import com.huawei.cmp.dvcallchainservice.util.IndexHelper;

import lombok.Setter;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 非dsf场景trace查询策略类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020/8/24 17:12
 */
@Component
public class OpenTracingQueryStrategy {
    private static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";

    private static final int SEPARATOR_LENGTH = 10;

    // 映射器常量
    private static final EntityMapper<CallChainLogVO> MAPPER = hit -> {
        Map<String, Object> source = hit.getSourceAsMap();
        CallChainLogVO model = new CallChainLogVO();
        model.setAllProperties(source);
        return model;
    };

    @Autowired
    @Setter
    private IndexHelper indexHelper;

    @Autowired
    @Setter
    private ElasticSearchTemplate elasticSearchTemplate;

    /**
     * querySpanList
     *
     * @param traceID traceId
     * @param solutionIds ids
     * @param tupleList 三元组列表 写入该list 靠产生副作用记忆数据
     * @return 原始数据list
     */
    public List<CallChainDetailVO> querySpanList(String traceID, List<String> solutionIds,
        List<Tuple3<String, String, CallChainDetailVO>> tupleList) {
        String indices = indexHelper.getIndex(null, null, solutionIds);

        SearchCommand searchCommand = new SearchCommand().withIndices(indices)
            .withTermQuery1("TraceID", traceID)
            .withSize(1000);

        EntityMapper<CallChainDetailVO> entityMapper = hit -> {
            Map<String, Object> source = hit.getSourceAsMap();
            CallChainDetailVO model = new CallChainDetailVO();
            model.setAllProperties(source);
            tupleList.add(Tuple3.tuple(hit.getIndex(), hit.getId(), model));
            return model;
        };

        return elasticSearchTemplate.queryForList(searchCommand, entityMapper);
    }

    /**
     * updateCallService
     *
     * @param callChainLogVOList list
     */
    public void updateCallService(List<CallChainDetailVO> callChainLogVOList) {
        updateCallServices(callChainLogVOList);
        callChainLogVOList.stream().filter(vo -> !"httprequestreceive".equals(vo.getFlag())).forEach(item -> {
            String callingNodeID = item.getCallingNodeID();
            String calledNodeID = item.getCalledNodeID();

            item.setCallingService(splitedString(callingNodeID, 1));
            item.setCalledService(splitedString(calledNodeID, 1));

            item.setCallingNodeID(splitedString(callingNodeID, 0));
            item.setCalledNodeID(splitedString(calledNodeID, 0));

        });

        callChainLogVOList.stream().filter(vo -> "httprequestreceive".equals(vo.getFlag())).forEach(item -> {
            String calledNodeID = item.getCalledNodeID();

            item.setCalledService(splitedString(calledNodeID, 1));
            item.setCalledNodeID(splitedString(calledNodeID, 0));

            String[] serviceName = item.getCallingNodeID().split("##");
            item.setAppName(serviceName.length == 2 ? serviceName[1] : "");
            item.setCallingService("");
            item.setCallingNodeID("");
            item.setCalledIP("");
        });
    }

    private void updateCallServices(List<CallChainDetailVO> callChainLogVOList) {
        for (int i = 0; i < callChainLogVOList.size(); i++) {
            for (int j = 0; j < callChainLogVOList.size(); j++) {
                CallChainDetailVO iItem = callChainLogVOList.get(i);
                CallChainDetailVO jItem = callChainLogVOList.get(j);

                String iFlag = iItem.getFlag();
                String jFlag = jItem.getFlag();

                String iSeqNo = iItem.getSeqNo();
                String jSeqNo = jItem.getSeqNo();

                if ("requestsend".equals(iFlag) && jFlag.endsWith("requestreceive")) {
                    String parentSpanID = Arrays.stream(StringUtils.split(iItem.getPlatformExtendInfo(), "&"))
                        .map(info -> Arrays.asList(StringUtils.split(info, '=')))
                        .collect(Collectors.toMap(e -> e.get(0), e -> e.size() == 1 ? "" : e.get(1)))
                        .get("parentSpanID");
                    String spanId = Arrays.stream(StringUtils.split(jItem.getPlatformExtendInfo(), "&"))
                        .map(info -> Arrays.asList(StringUtils.split(info, '=')))
                        .collect(Collectors.toMap(e -> e.get(0), e -> e.size() == 1 ? "" : e.get(1)))
                        .get("spanID");
                    if (parentSpanID != null && spanId != null && parentSpanID.equals(spanId)) {
                        iItem.setCallingNodeID(jItem.getCalledNodeID());
                    }
                }

                if (iSeqNo == null || jSeqNo == null) {
                    continue;
                }
                if ("requestsend".equals(iFlag) && jFlag.equals("requestreceive") && iSeqNo.equals(jSeqNo)) {
                    iItem.setCalledNodeID(jItem.getCalledNodeID());
                    String[] serviceName = jItem.getCallingNodeID().split("##");
                    iItem.setAppName(serviceName.length == 2 ? serviceName[1] : "");
                }
            }
        }
    }

    private String splitedString(String node, int index) {
        if (StringUtils.isEmpty(node)) {
            return "";
        }

        return node.split("##").length == 2 ? node.split("##")[index] : "";
    }

    /**
     * 更新span的seqNo
     *
     * @param tupleList <index, id , model>三元组
     */
    public void updateSpans(List<Tuple3<String, String, CallChainDetailVO>> tupleList) {
        UpdateCommand updateCommand = new UpdateCommand();
        tupleList.stream().findFirst().ifPresent(tuple3 -> updateCommand.setIndex(tuple3.getA()));
        tupleList.stream().forEach(tuple3 -> {
            Map<String, Object> source = new HashMap<>();
            source.put("SeqNo", tuple3.getC().getSeqNo());
            updateCommand.addSource(Tuple2.tuple(tuple3.getB(), source));
        });
        elasticSearchTemplate.bulkUpdate(updateCommand);
    }

    /**
     * 查询单个trace
     *
     * @param dto dto
     * @return list
     */
    public List<CallChainDetailVO> querySpans(SingleChainByTraceIdQueryDTO dto) {
        List<Tuple3<String, String, CallChainDetailVO>> tupleList = Lists.newArrayList();
        List<CallChainDetailVO> callChainLogVOList = querySpanList(dto.getTraceID(), dto.getSolutionIds(), tupleList);
        if (callChainLogVOList.stream().anyMatch(vo -> -1 != vo.getSeqNo().indexOf("."))) {
            updateCallService(callChainLogVOList);
            return filterList(callChainLogVOList);
        }

        Map<String, List<CallChainDetailVO>> groups = callChainLogVOList.stream()
            .collect(Collectors.groupingBy(callChainDetailVO -> callChainDetailVO.getFlag()));

        List<ServiceNode> wrappedSpanList = getServiceNodes(groups);

        // requestreceive都以requestsend为父span 根据此关系构造 <request send span id> -> <request receive span id>
        Map<String, String> requestReceiveParentSpanMap = getParentSpanMap(groups, "requestreceive");

        // requestreceive都以requestsend为父span 根据此关系构造 <request receive span id> -> <request send span id>
        Map<String, String> requestSendParentSpanMap = getParentSpanMap(groups, "requestsend");

        // 为了构造树状的调用关系 先对数据做预处理构造5元组<ServiceNode, SpanId, fromSpanId>列表
        List<Tuple3<ServiceNode, String, String>> spanTupleList = getTuple3s(wrappedSpanList, requestSendParentSpanMap);

        // 补充spanTupleList列表中tuple缺少的toSpanId和toService
        spanTupleList.forEach(spanTuple -> {
            Optional.ofNullable(requestSendParentSpanMap.get(spanTuple.getB())).ifPresent(spanId -> {
                Optional.ofNullable(requestReceiveParentSpanMap.get(spanId)).ifPresent(fromSpanId -> {
                    spanTuple.setC(fromSpanId);
                });
            });
        });

        // 组装ServiceNode为树状结构
        for (int i = 0; i < spanTupleList.size(); i++) {
            for (int j = 0; j < spanTupleList.size(); j++) {
                if (StringUtils.equals(spanTupleList.get(i).getB(), spanTupleList.get(j).getC())) {
                    spanTupleList.get(i).getA().getChildrend().add(spanTupleList.get(j).getA());
                }
            }
        }

        ServiceNode root = wrappedSpanList.stream()
            .filter(span -> "httprequestreceive".equals(span.getCallChainDetailVO().getFlag()))
            .collect(Collectors.toList())
            .get(0);

        travelSpan(root, "1", callChainLogVOList);
        updateSpans(tupleList);
        updateCallService(callChainLogVOList);
        return filterList(callChainLogVOList);
    }

    /**
     * list转化为五元组 携带fromService toService后续使用此二字段解析调用关系
     *
     * @param wrappedSpanList list
     * @param map map
     * @return list
     */
    private List<Tuple3<ServiceNode, String, String>> getTuple3s(List<ServiceNode> wrappedSpanList,
        Map<String, String> map) {
        return wrappedSpanList.stream().map(serviceNode -> {
            CallChainDetailVO callChainDetailVO = serviceNode.getCallChainDetailVO();
            String spanId = Arrays.stream(StringUtils.split(callChainDetailVO.getPlatformExtendInfo(), "&"))
                .map(info -> Arrays.asList(StringUtils.split(info, '=')))
                .collect(Collectors.toMap(e -> e.get(0), e -> e.size() == 1 ? "" : e.get(1)))
                .get("spanID");

            if ("requestsend".equals(serviceNode.getCallChainDetailVO().getFlag())) {
                return Tuple3.tuple(serviceNode, spanId, map.get(spanId));
            } else {
                return Tuple3.tuple(serviceNode, spanId, UUID.randomUUID().toString());
            }
        }).collect(toList());
    }

    /**
     * 将类型为requestsend、httprequestreceive的span组装为ServiceNode 后续组装处理
     *
     * @param groups groups
     * @return list
     */
    private List<ServiceNode> getServiceNodes(Map<String, List<CallChainDetailVO>> groups) {
        List<CallChainDetailVO> requestSendSpanList = Stream.of("requestsend", "httprequestreceive")
            .filter(flag -> groups.keySet().contains(flag))
            .flatMap(flag -> groups.get(flag).stream())
            .collect(toList());

        // httpresonsesend类型的span为最后一个span与httprequestreceive类型的span对应 故而SeqNo为1
        Optional.ofNullable(groups.get("httpresponsesend")).ifPresent(list -> list.forEach(item -> item.setSeqNo("1")));

        // 包装类型为requestsend和httprequestreceive的span为ServiceNode
        return requestSendSpanList.stream().map(span -> {
            ServiceNode serviceNode = new ServiceNode();
            serviceNode.setCallChainDetailVO(span);
            return serviceNode;
        }).collect(toList());
    }

    /**
     * requestreceive都以requestsend为父span 根据此关系构造 <parent span id> -> <parent span id>
     *
     * @param groups groups
     * @param type type
     * @return map
     */
    private Map<String, String> getParentSpanMap(Map<String, List<CallChainDetailVO>> groups, String type) {
        Map<String, String> parentSpanMap = new HashMap<>();
        Optional.ofNullable(groups.get(type)).ifPresent(list -> list.forEach(item -> {
            // spanId
            String spanId = Arrays.stream(StringUtils.split(item.getPlatformExtendInfo(), "&"))
                .map(info -> Arrays.asList(StringUtils.split(info, '=')))
                .collect(Collectors.toMap(e -> e.get(0), e -> e.size() == 1 ? "" : e.get(1)))
                .get("spanID");

            // 父spanId
            String parentSpanId = "";
            if (item.getSeqNo() != null) {
                parentSpanId = item.getSeqNo().substring(item.getSeqNo().indexOf("|CHILD_OF|") + SEPARATOR_LENGTH);
            }
            parentSpanMap.put(spanId, parentSpanId);
        }));
        return parentSpanMap;
    }

    /**
     * 过滤处理后的元素
     *
     * @param voList list
     * @return 过滤后的结果
     */
    private List<CallChainDetailVO> filterList(List<CallChainDetailVO> voList) {
        return voList.stream()
            .filter(vo -> -1 == vo.getSeqNo().indexOf("CHILD"))
            .filter(vo -> "requestsend".equals(vo.getFlag()) || "requestreceive".equals(vo.getFlag()) || "httprequestreceive".equals(vo.getFlag()))
            .sorted(Comparator.comparing(CallChainDetailVO::getSeqNo, CallchainStringUtils::compareSeqNo))
            .collect(Collectors.toList());
    }

    /**
     * <AUTHOR>
     * @version 1.0
     * @description ServiceNode
     * @since 2020/9/10 19:34
     */
    public static class ServiceNode implements Comparable<ServiceNode> {
        private static final OssLog RUN_LOGGER = OssLogFactory.getLogger(ServiceNode.class);

        /**
         * callChainDetailVO模型
         */
        public CallChainDetailVO callChainDetailVO;

        /**
         * childrend
         */
        public List<ServiceNode> childrend = Lists.newArrayList();

        /**
         * getCallChainDetailVO
         *
         * @return vo
         */
        public CallChainDetailVO getCallChainDetailVO() {
            return callChainDetailVO;
        }

        /**
         * setChildrend
         *
         * @param callChainDetailVO c
         */
        public void setCallChainDetailVO(CallChainDetailVO callChainDetailVO) {
            this.callChainDetailVO = callChainDetailVO;
        }

        /**
         * getChildrend
         *
         * @return list
         */
        public List<ServiceNode> getChildrend() {
            return childrend;
        }

        /**
         * setChildrend
         *
         * @param childrend c
         */
        public void setChildrend(List<ServiceNode> childrend) {
            this.childrend = childrend;
        }

        @Override
        public int compareTo(ServiceNode serviceNode) {
            String compareBeginTime = serviceNode.getCallChainDetailVO().getBeginTime();
            String compareTraceID = serviceNode.getCallChainDetailVO().getTraceID();
            String compareSeqNo = serviceNode.getCallChainDetailVO().getSeqNo();
            String beginTime = this.callChainDetailVO.getBeginTime();
            String traceID = this.callChainDetailVO.getTraceID();
            String seqNo = this.callChainDetailVO.getSeqNo();
            if (seqNo != null && traceID != null && beginTime != null) {
                if (traceID.equals(compareTraceID) && seqNo.equals(compareSeqNo) && beginTime.equals(
                    compareBeginTime)) {
                    return 0;
                }
            }

            long compareBeginTimeMills = 0L;
            long beginTimeMills = 0L;
            if (compareBeginTime != null && beginTime != null) {
                try {
                    compareBeginTimeMills = new SimpleDateFormat(DATE_FORMAT).parse(compareBeginTime).getTime();
                    beginTimeMills = new SimpleDateFormat(DATE_FORMAT).parse(beginTime).getTime();
                } catch (ParseException e) {
                    RUN_LOGGER.error("Opentracing, compare to beginTime error");
                }
            }
            return (int) (beginTimeMills - compareBeginTimeMills);
        }

        @Override
        public boolean equals(Object obj) {
            if (!(obj instanceof ServiceNode)) {
                return false;
            }
            ServiceNode serviceNode = (ServiceNode) obj;
            if (this == serviceNode) {
                return true;
            }
            String compareBeginTime = serviceNode.getCallChainDetailVO().getBeginTime();
            String compareTraceID = serviceNode.getCallChainDetailVO().getTraceID();
            String compareSeqNo = serviceNode.getCallChainDetailVO().getSeqNo();
            String beginTime = this.callChainDetailVO.getBeginTime();
            String traceID = this.callChainDetailVO.getTraceID();
            String seqNo = this.callChainDetailVO.getSeqNo();
            if (seqNo != null && traceID != null && beginTime != null) {
                return traceID.equals(compareTraceID) && seqNo.equals(compareSeqNo) && beginTime.equals(
                    compareBeginTime);
            }
            return false;
        }

        @Override
        public int hashCode() {
            String beginTime = this.callChainDetailVO.getBeginTime();
            String traceID = this.callChainDetailVO.getTraceID();
            String seqNo = this.callChainDetailVO.getSeqNo();
            int result = traceID.hashCode();
            result = 17 * result + seqNo.hashCode();
            return 17 * result + beginTime.hashCode();
        }
    }

    /**
     * 递归遍历spans
     *
     * @param serviceNode serviceNode
     * @param seqNo seqNo
     * @param callChainDetailVOList callChainDetailVOList
     */
    public static void travelSpan(ServiceNode serviceNode, String seqNo,
        List<CallChainDetailVO> callChainDetailVOList) {
        CallChainDetailVO callChainDetailVO = serviceNode.getCallChainDetailVO();
        callChainDetailVO.setSeqNo(seqNo);

        if (!"httprequestreceive".equals(callChainDetailVO.getFlag())) {
            // request receive
            String spanId = Arrays.stream(StringUtils.split(callChainDetailVO.getPlatformExtendInfo(), "&"))
                .map(info -> Arrays.asList(StringUtils.split(info, '=')))
                .collect(Collectors.toMap(e -> e.get(0), e -> e.size() == 1 ? "" : e.get(1)))
                .get("spanID");

            callChainDetailVOList.stream()
                .filter(callChainDetailVO1 -> callChainDetailVO1.getSeqNo().endsWith(spanId))
                .forEach(callChainDetailVO1 -> callChainDetailVO1.setSeqNo(seqNo));
        }

        List<ServiceNode> children = serviceNode.getChildrend();
        Collections.sort(children);

        int i = 0;
        Iterator<ServiceNode> iterator = children.iterator();
        while (iterator.hasNext()) {
            travelSpan(iterator.next(), seqNo + "." + (i + 1), callChainDetailVOList);
            i++;
        }
    }

    /**
     * 查询trace root
     *
     * @param queryParamVO vo
     * @return list
     */
    public List<CallChainLogVO> queryTraceRoot(ChainQueryParamModel queryParamVO) {
        String index = indexHelper.getIndex(queryParamVO.getBeginTime(), queryParamVO.getEndTime(),
            queryParamVO.getSolutionIds());

        BoolQueryBuilder qb = queryParamVO.buildQueryBuilder();

        SearchCommand searchCommand = new SearchCommand().withIndices(index)
            .withFrom((queryParamVO.getPage().getPageIndex() - 1) * queryParamVO.getPage().getRowsCount())
            .withSize(queryParamVO.getPage().getRowsCount())
            // 只有Flag为httprequestreceive的span才是首span
            .withMatchQuery1("Flag", "httprequestreceive")
            .withQueryBuilder1(qb);

        String orderType = queryParamVO.getSort().getOrderType();
        String sortField = queryParamVO.getSort().getSortField();
        Conditional.ofNullable(orderType)
            .ifPresent(() -> Conditional.ofBool(orderType.equalsIgnoreCase(CallchainConstant.TraceQuery.ORDER_ASC))
                .ifTrue(() -> searchCommand.withSort(sortField, SortOrder.ASC))
                .ifFalse(() -> searchCommand.withSort(sortField, SortOrder.DESC)));

        return elasticSearchTemplate.queryForList(searchCommand, MAPPER);
    }

    /**
     * 查询条数
     *
     * @param queryParamVO vo
     * @return long
     */
    public long queryTraceCount(ChainQueryParamModel queryParamVO) {
        String index = indexHelper.getIndex(queryParamVO.getBeginTime(), queryParamVO.getEndTime(),
            queryParamVO.getSolutionIds());

        BoolQueryBuilder qb = queryParamVO.buildQueryBuilder();

        SearchCommand searchCommand = new SearchCommand().withIndices(index)
            .withMatchQuery1("Flag", "httprequestreceive")
            .withQueryBuilder1(qb);

        return elasticSearchTemplate.count(searchCommand);
    }

    /**
     * 查询一层的span
     *
     * @param dto dto
     * @return list
     */
    public List<CallChainLogVO> queryOneSpan(ChainLogInfoQueryDTO dto) {
        List<String> solutionIdList = dto.getSolutionIds();
        String index = indexHelper.getIndex(null, null, solutionIdList);

        SearchCommand searchCommand = new SearchCommand().withIndices(index)
            .withMatchQuery1("TraceID", dto.getTraceID())
            .withMatchQuery1("SeqNo", dto.getSeqNo())
            .withMatchQueryList(solutionIdList)
            .withLimit(1000);

        return elasticSearchTemplate.queryForList(searchCommand, MAPPER);
    }

    /**
     * 查询最新的100条trace
     *
     * @param traceByFilterQueryDTO 查询dto
     * @return vo列表
     */
    public List<CallChainLogVO> queryNewestTrace(TraceByFilterQueryDTO traceByFilterQueryDTO) {
        List<String> solutionIds = traceByFilterQueryDTO.getSolutionIds();
        String index = indexHelper.getIndex(null, DateTimeFormatterUtil.getCurrentUTCTime(), solutionIds);

        SearchCommand searchCommand = new SearchCommand().withIndices(index)
            .withMatchQuery1("Flag", "httprequestreceive")
            .withSort("Time", SortOrder.DESC)
            .withMatchQuery0("CallingNodeID", "##")
            .withLimit(100);

        BoolQueryBuilder queryBuilder = searchCommand.getQueryBuilder();
        Conditional.ofNotEmpty(solutionIds).ifTrue(() -> {
            solutionIds.forEach(id -> queryBuilder.should(QueryBuilders.matchQuery("SolutionId", id)));
            queryBuilder.minimumShouldMatch(1);
        });

        String status = traceByFilterQueryDTO.getStatus();
        Conditional.ofNotEmpty(status).ifTrue(() -> searchCommand.withMatchQuery1("Status", status));

        Map<String, String> searchParams = traceByFilterQueryDTO.getSearchParams();
        List<String> selectTraceIDs = traceByFilterQueryDTO.getSelectTraceIDs();
        if (CollectionUtils.isNotEmpty(selectTraceIDs)) {
            searchCommand.withQueryBuilder1(QueryBuilders.termsQuery("TraceID", selectTraceIDs));
        }

        if (null != searchParams) {
            searchParams.forEach((field, value) -> {
                if (!StringUtils.isEmpty(field) && !StringUtils.isEmpty(value)) {
                    searchCommand.withQueryBuilder1(QueryBuilders.queryStringQuery(value).defaultField(field));
                }
            });
        }

        return elasticSearchTemplate.queryForList(searchCommand, MAPPER);
    }
}
