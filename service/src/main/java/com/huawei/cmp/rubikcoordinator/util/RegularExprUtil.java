/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2019. All rights reserved.
 */

package com.huawei.cmp.rubikcoordinator.util;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cmp.rubikcoordinator.constant.CollectorConstants;
import com.huawei.cmp.rubikcoordinator.model.LogSource;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 正则表达式工具类
 *
 * <AUTHOR>
 * @version V8
 * @see RegularExprUtil
 * @since 2019 -09-10
 */
public class RegularExprUtil {
    private static final OssLog LOGGER = OssLogFactory.getLogger(RegularExprUtil.class);

    /**
     * 修改黑白名单兼容(?!)，(?<!)两种正则表达式
     *
     * @param logSource logSource
     */
    public static void solveRegularExpr(LogSource logSource) {
        String whiteLogRegex = logSource.getLogFileWhiteRegex();
        String blackLogRegex = logSource.getLogFileBlackRegex();

        if (StringUtils.isBlank(whiteLogRegex)) {
            LOGGER.debug("WhiteLogRegex is empty, no need to handle with go");
            return;
        }

        LOGGER.debug("Origin whiteLogRegex:{}, blackLogRegex:{}", whiteLogRegex, blackLogRegex);
        // 对黑白名单前后增加^$，保证go在匹配文件正则时，进行全词匹配
        whiteLogRegex = preHandleRegex(whiteLogRegex);
        blackLogRegex = preHandleRegex(blackLogRegex);
        LOGGER.debug("After format with go regex,whilteLogRegex:{},blackLogRegex:{}", whiteLogRegex, blackLogRegex);

        // subAfterString存放截取后的右侧内容用于下次继续进行截取
        String subAfterString = whiteLogRegex;

        // 保存根据两种兼容的正则截取得到的片段，用于最后的正则拼接
        List<String> subRegexs = new ArrayList<>();

        // 用于标识当前用哪种正则进行片段截取
        boolean isSubWithForwardReg = true;

        // 保存当前循环中，两种正则所在index，从而判断用哪种正则进行截取，
        // subIndex保存为截取位置，
        // rightBracketsIndex保存截取正则对应的右括号的index，用于截取括号中的内容
        int forwardIndex;
        int reverseIndex;
        int subIndex;
        int rightBracketsIndex;
        while (StringUtils.isNotBlank(subAfterString)) {
            forwardIndex = subAfterString.indexOf(CollectorConstants.RegexConverter.FORWARDREGEX);
            reverseIndex = subAfterString.indexOf(CollectorConstants.RegexConverter.REVERSEREGEX);

            // 均小于0，说明剩余字符串中没有两种正则内容，结束处理
            if (forwardIndex < 0 && reverseIndex < 0) {
                subRegexs.add(subAfterString);
                break;
            }
            subIndex = forwardIndex;
            if (forwardIndex < 0 || forwardIndex > reverseIndex) {
                isSubWithForwardReg = false;
                subIndex = reverseIndex;
            }

            // 截取正则位置左侧的字符串，左侧的字符串中不包含两种正则，可以直接保存用于最后的正则拼接
            subAfterString = subString(subAfterString, subIndex, subRegexs);

            // 将右侧字符串处理，去除开始位置的正则内容
            subAfterString = getSubStringAfter(subAfterString, isSubWithForwardReg);

            // 查找截取正则对应的右括号位置
            rightBracketsIndex = getRightBracketsIndex(subAfterString);

            // 未找到对应的右括号，处理失败， 直接返回，不做处理
            if (rightBracketsIndex < 0) {
                LOGGER.error(
                    "Failed to get ')' from whiteLogRegex to coorespond to '(', try to find from left string:{}",
                    subAfterString);
                return;
            }

            // 截取右括号左侧内容，直接保存，用于最后拼接
            // subAfterString保存右括号右侧的内容，继续下次处理
            subAfterString = subString(subAfterString, rightBracketsIndex, subRegexs).substring(1);
        }
        LOGGER.debug("Get subRegexs:{}", subRegexs.toString());

        // 拼接黑白名单正则表达式
        whiteLogRegex = getWhiteLogRegex(subRegexs);
        blackLogRegex = getBlackLogRegex(blackLogRegex, subRegexs);
        LOGGER.debug("Get whilteLogRegex:{}, blackLogRegex:{} in go format", whiteLogRegex, blackLogRegex);
        logSource.setLogFileWhiteRegex(whiteLogRegex);
        logSource.setLogFileBlackRegex(blackLogRegex);
    }

    /**
     * 对正则进行预处理:go正则表达式前后加上^$，保证正则黑白名单与java效果相同
     * 输出^(regex)$
     *
     * @param regex regex
     * @return String
     */
    private static String preHandleRegex(String regex) {
        if (StringUtils.isBlank(regex)) {
            return regex;
        }
        return new StringBuffer().append(CollectorConstants.RegexConverter.STARTREGEX)
            .append(CollectorConstants.RegexConverter.LEFTBRACKET)
            .append(handleLogRegexWithRegexOr(regex))
            .append(CollectorConstants.RegexConverter.RIGHTBRACKET)
            .append(CollectorConstants.RegexConverter.ENDREGEX)
            .toString();
    }

    /**
     * 对黑白名单中|进行处理，|左侧加上)$,右侧加上^(
     *
     * @param origin origin
     * @return String
     */
    private static String handleLogRegexWithRegexOr(String origin) {
        // 对白名单正则表达式做处理，当出现|时，当|左侧左右括号个数相等时，则|左侧加上)$,右侧加上^(
        int indexOfOr = origin.indexOf(CollectorConstants.RegexConverter.OR);
        while (indexOfOr > -1) {
            if (equalCountOfLeftAndRightBracket(origin, -1, indexOfOr)) {
                origin = new StringBuffer().append(origin.substring(0, indexOfOr))
                    .append(CollectorConstants.RegexConverter.RIGHTBRACKET)
                    .append(CollectorConstants.RegexConverter.ENDREGEX)
                    .append(CollectorConstants.RegexConverter.OR)
                    .append(CollectorConstants.RegexConverter.STARTREGEX)
                    .append(CollectorConstants.RegexConverter.LEFTBRACKET)
                    .append(origin.substring(indexOfOr + 1))
                    .toString();
            }

            indexOfOr = origin.indexOf(CollectorConstants.RegexConverter.OR, indexOfOr + 3);
        }

        return origin;
    }

    /**
     * 根据索引，将字符串在索引左侧位置截取出并存入subRegexs用于最后拼接字段，
     * 返回index及之后位置的字段
     *
     * @param origin origin
     * @param index index
     * @param subRegexs subRegexs
     * @return String
     */
    private static String subString(String origin, int index, List<String> subRegexs) {
        String subRegex = origin.substring(0, index);
        subRegexs.add(subRegex);

        return origin.substring(index);
    }

    /**
     * 去除字符串开始位置的正则内容，返回不包含正则字段
     * 如:(?!123454,返回123454
     *
     * @param origin origin
     * @param isSubWithForwardReg isSubWithForwardReg
     * @return String
     */
    private static String getSubStringAfter(String origin, boolean isSubWithForwardReg) {
        if (isSubWithForwardReg) {
            return origin.substring(CollectorConstants.RegexConverter.FORWARDREGEX.length());
        }

        return origin.substring(CollectorConstants.RegexConverter.REVERSEREGEX.length());
    }

    /**
     * 获取截取正则对应的右括号所在索引
     *
     * @param subAfterString subAfterString
     * @return int
     */
    private static int getRightBracketsIndex(String subAfterString) {
        int leftBracketsCount = 0;
        int leftBracketsIndex = subAfterString.indexOf(CollectorConstants.RegexConverter.LEFTBRACKET);
        int rightBracketsIndex = subAfterString.indexOf(CollectorConstants.RegexConverter.RIGHTBRACKET);
        while (leftBracketsIndex > -1 && leftBracketsIndex < rightBracketsIndex) {
            leftBracketsCount++;
            leftBracketsIndex = subAfterString.indexOf(CollectorConstants.RegexConverter.LEFTBRACKET,
                leftBracketsIndex + 1);
        }

        for (; leftBracketsCount > 0; leftBracketsCount--) {
            rightBracketsIndex = subAfterString.indexOf(CollectorConstants.RegexConverter.RIGHTBRACKET,
                rightBracketsIndex + 1);
        }
        return rightBracketsIndex;
    }

    /**
     * 根据截取的结果，拼接文件白名单
     * 文件白名单:取索引为偶数位置的内容进行拼接，得到白名单正则表达式
     *
     * @param subRegexs subRegexs
     * @return String
     */
    private static String getWhiteLogRegex(List<String> subRegexs) {
        StringBuffer whilteLogRegex = new StringBuffer();

        String subRegex;
        for (int i = 0; i < subRegexs.size(); i = i + 2) {
            subRegex = subRegexs.get(i);
            if (StringUtils.isNotBlank(subRegex)) {
                whilteLogRegex.append(subRegex);
            }
        }

        return whilteLogRegex.toString();
    }

    /**
     * 根据截取的结果，拼接文件黑名单
     * 文件黑名单:取索引为奇数位置及左右两侧的内容进行拼接，得到黑名单正则表达式，黑名单之间使用"|"拼接，
     * 因为初始白名单开始与结束位置一定有^$，因此不存在outofrange异常
     *
     * @param originBlackLogRegex originBlackLogRegex
     * @param subRegexs subRegexs
     * @return String
     */
    private static String getBlackLogRegex(String originBlackLogRegex, List<String> subRegexs) {
        StringBuffer blackLogRegex = new StringBuffer(
            StringUtils.isNotBlank(originBlackLogRegex) ? originBlackLogRegex : "");

        // 对正则分段进行处理，
        // 第一个分段包含|，且|左侧左右括号个数相等，则第一个分段去除|左侧内容
        // 最后一个分段包含|，且|左侧左右括号个数相等，则一个分段去除|右侧内容
        if (subRegexs.get(0).contains(String.valueOf(CollectorConstants.RegexConverter.OR))
            && equalCountOfLeftAndRightBracket(subRegexs.get(0), -1,
            subRegexs.get(0).lastIndexOf(CollectorConstants.RegexConverter.OR))) {
            subRegexs.set(0, String.valueOf(CollectorConstants.RegexConverter.STARTREGEX) + String.valueOf(
                CollectorConstants.RegexConverter.LEFTBRACKET) + subRegexs.get(0)
                .substring(subRegexs.get(0).lastIndexOf(CollectorConstants.RegexConverter.OR)) + 1);
        }
        if (subRegexs.get(subRegexs.size() - 1).contains(String.valueOf(CollectorConstants.RegexConverter.OR))
            && equalCountOfLeftAndRightBracket(subRegexs.get(subRegexs.size() - 1),
            subRegexs.get(subRegexs.size() - 1).indexOf(CollectorConstants.RegexConverter.OR),
            subRegexs.get(subRegexs.size() - 1).length())) {
            subRegexs.set(subRegexs.size() - 1, subRegexs.get(subRegexs.size() - 1)
                .substring(0, subRegexs.get(subRegexs.size() - 1).indexOf(CollectorConstants.RegexConverter.OR)));
        }

        for (int i = 1; i < subRegexs.size(); i = i + 2) {
            if (StringUtils.isNotBlank(blackLogRegex.toString())) {
                blackLogRegex.append(CollectorConstants.RegexConverter.OR);
            }
            blackLogRegex.append(getPartOfSubRegexByOr(subRegexs.get(i - 1), true))
                .append(CollectorConstants.RegexConverter.LEFTBRACKET)
                .append(subRegexs.get(i))
                .append(CollectorConstants.RegexConverter.RIGHTBRACKET)
                .append(getPartOfSubRegexByOr(subRegexs.get(i + 1), false));
        }

        return blackLogRegex.toString();
    }

    /**
     * 中间分段，索引为偶数位置，包含两个以上|,且第一个与最后一个|中间内容左右括号相等，则去除两个|中间内容，返回最左侧或最右侧内容
     *
     * @param subRegex subRegex
     * @param rightPart rightPart
     * @return String
     */
    private static String getPartOfSubRegexByOr(String subRegex, boolean rightPart) {
        if (subRegex.contains(String.valueOf(CollectorConstants.RegexConverter.OR))
            && subRegex.indexOf(CollectorConstants.RegexConverter.OR) != subRegex.lastIndexOf(
            CollectorConstants.RegexConverter.OR) && equalCountOfLeftAndRightBracket(subRegex,
            subRegex.indexOf(CollectorConstants.RegexConverter.OR),
            subRegex.lastIndexOf(CollectorConstants.RegexConverter.OR))) {
            if (rightPart) {
                return subRegex.substring(subRegex.lastIndexOf(CollectorConstants.RegexConverter.OR) + 1);
            } else {
                return subRegex.substring(0, subRegex.indexOf(CollectorConstants.RegexConverter.OR));
            }
        }
        return subRegex;
    }

    /**
     * 开始与结束索引之间左右括号个数是否相等，判断索引之间是否为单独的正则
     *
     * @param origin origin
     * @param firstIndexOfOr firstIndexOfOr
     * @param lastIndexOfOr lastIndexOfOr
     * @return boolean
     */
    private static boolean equalCountOfLeftAndRightBracket(String origin, int firstIndexOfOr, int lastIndexOfOr) {
        int leftBracketsCount = 0;
        int rightBracketsCount = 0;
        int leftBracketsIndex = origin.indexOf(CollectorConstants.RegexConverter.LEFTBRACKET, firstIndexOfOr);
        int rightBracketsIndex = origin.indexOf(CollectorConstants.RegexConverter.RIGHTBRACKET, firstIndexOfOr);
        while (leftBracketsIndex > firstIndexOfOr && leftBracketsIndex < lastIndexOfOr) {
            leftBracketsCount++;
            leftBracketsIndex = origin.indexOf(CollectorConstants.RegexConverter.LEFTBRACKET, leftBracketsIndex + 1);
        }

        while (rightBracketsIndex > firstIndexOfOr && rightBracketsIndex < lastIndexOfOr) {
            rightBracketsCount++;
            rightBracketsIndex = origin.indexOf(CollectorConstants.RegexConverter.LEFTBRACKET, rightBracketsIndex + 1);
        }

        return leftBracketsCount == rightBracketsCount;
    }
}
