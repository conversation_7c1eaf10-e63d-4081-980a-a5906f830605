/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2019-2019. All rights reserved.
 */

package com.huawei.cmp.ihealing.process.core.schedule;

import static com.huawei.cmp.ihealing.flow.engine.constants.EngineConstants.TASK_ID;

import com.huawei.bsp.log.OssLog;
import com.huawei.cmp.foundation.exception.define.CMPException;
import com.huawei.cmp.foundation.filter.model.LoginUserInfo;
import com.huawei.cmp.foundation.filter.utils.UserInfoUtils;
import com.huawei.cmp.foundation.task.IExecutor;
import com.huawei.cmp.ihealing.common.constant.ReflectConst;
import com.huawei.cmp.ihealing.common.dao.model.TaskDo;
import com.huawei.cmp.ihealing.common.dao.provider.FlowEventProvider;
import com.huawei.cmp.ihealing.common.dao.provider.FlowViewProvider;
import com.huawei.cmp.ihealing.dao.TaskDao;
import com.huawei.cmp.ihealing.flow.engine.constants.EngineConstants;
import com.huawei.cmp.ihealing.in.api.exception.FlowExceptionCode;
import com.huawei.cmp.ihealing.in.api.flow.model.task.FlowType;
import com.huawei.cmp.ihealing.in.api.flow.model.view.BasicInfo;
import com.huawei.cmp.ihealing.in.api.flow.model.view.FlowModel;
import com.huawei.cmp.ihealing.in.api.flow.model.view.FlowParameter;
import com.huawei.cmp.ihealing.process.api.constant.ProcessConstants;
import com.huawei.cmp.ihealing.process.api.define.Process;
import com.huawei.cmp.ihealing.process.api.utils.LogUtil;
import com.huawei.cmp.ihealing.process.core.ActivitiBeanProxy;
import com.huawei.cmp.ihealing.process.core.cluster.DistributeTaskHelper;
import com.huawei.cmp.ihealing.process.core.config.ConfigHolder;
import com.huawei.cmp.ihealing.process.dao.provider.DeploymentExtProvider;
import com.huawei.cmp.ihealing.utils.CommonNotifyUtil;
import com.huawei.cmp.ihealing.utils.JacksonUtils;
import com.huawei.cmp.ihealing.utils.SystemLogUtils;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.runtime.ProcessInstance;
import org.quartz.JobDetail;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.JobKey;
import org.quartz.UnableToInterruptJobException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.annotation.Resource;

/**
 * 功能描述：ProcessAutoStartTrigger类
 *
 * <AUTHOR>
 * @since 2020 -07-30
 */
@Component("processAutoStartTrigger")
public class ProcessAutoStartTrigger implements IExecutor {

    /**
     * DEBLOGGER
     */
    private static final OssLog DEBLOGGER = LogUtil.getDebugLogger(ProcessAutoStartTrigger.class);

    /**
     * RUNLOGGER
     */
    private static final OssLog RUNLOGGER = LogUtil.getRuntimeLogger(ProcessAutoStartTrigger.class);

    /**
     * 日志基本信息
     */
    private static final String LOG_BASEINFO = "dv.ihealing.scheduledTask.baseinfo";

    /**
     * 日志内容
     */
    private static final String LOG_CONTENT = "dv.ihealing.scheduledTask.content";

    /**
     * Deployment ext provider
     */
    @Autowired
    @Qualifier(value = "deploymentExtProvider")
    private DeploymentExtProvider deploymentExtProvider;

    /**
     * Distribute task helper
     */
    @Autowired
    private DistributeTaskHelper distributeTaskHelper;

    /**
     * task dao
     */
    @Resource
    private TaskDao taskDao;

    /**
     * Flow view provider
     */
    @Resource
    private FlowViewProvider flowViewProvider;

    @Autowired
    private FlowEventProvider flowEventProvider;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        JobKey jobKey = null;
        try {
            jobKey = getJobKey(jobExecutionContext);
            String engineName = jobKey.getGroup();
            String processDefinitionKey = jobKey.getName();
            if (!StringUtils.equals(engineName,
                ConfigHolder.getProperty(ProcessConstants.Config.ENGINE_NAME, "Unknown"))) {
                DEBLOGGER.info("The process[{}] is not belong to this engine.", jobKey);
                return;
            }
            RUNLOGGER.info("Start invoke activiti:runtimeService:createProcessInstanceQuery().");
            List<ProcessInstance> processInstances = ActivitiBeanProxy.getRuntimeService()
                .createProcessInstanceQuery()
                .processDefinitionKey(processDefinitionKey)
                .list();
            RUNLOGGER.info("End   invoke activiti:runtimeService:createProcessInstanceQuery().");
            if (CollectionUtils.isNotEmpty(processInstances)) {
                RUNLOGGER.info("The process:[{}] is still running, skip this time.", processDefinitionKey);
                return;
            }
            Process process = getProcess(processDefinitionKey);
            if (process == null || StringUtils.isBlank(process.getCron())) {
                if (distributeTaskHelper.isExistTask(processDefinitionKey)) {
                    distributeTaskHelper.cancelCronTask(processDefinitionKey);
                }
                return;
            }
            // 记录系统日志
            SystemLogUtils.recordSystemLog(SystemLogUtils.buildSystemLogWithParam(null, null, LOG_BASEINFO, LOG_CONTENT,
                new Object[] {process.getName()}));
            Map<String, Object> vars = new HashMap<>(MapUtils.emptyIfNull(process.getProperties()));
            vars.put(EngineConstants.PVALUETYPE, false);
            TaskDo taskDo = taskDao.queryTaskByDeployId(processDefinitionKey);
            FlowModel flowModel = getFlowModel(taskDo, processDefinitionKey, vars);
            if (flowViewProvider.checkLimitedAtomsInProcess(flowModel.getDefinition())) {
                RUNLOGGER.error("Too many flows with limited atoms are in progress. Please try again later");
                return;
            }
            addVars(vars, flowModel);
            RUNLOGGER.info("Start invoke activiti:runtimeService:startProcessInstanceByKey.");
            distributeTaskHelper.updateCronTask(processDefinitionKey, ProcessConstants.Run.START_STATUS_STARTING);
            ActivitiBeanProxy.getRuntimeService().startProcessInstanceByKey(processDefinitionKey, vars);
            distributeTaskHelper.updateCronTask(processDefinitionKey, ProcessConstants.Run.START_STATUS_OK);
            RUNLOGGER.info("End   invoke activiti:runtimeService:startProcessInstanceByKey.");
        } catch (CMPException exception) {
            RUNLOGGER.error("Auto start process:[" + jobKey + "] failed.", exception);
            DEBLOGGER.error("Auto start process:[{}] failed, {}", jobKey, exception.getMessage());
        }
    }

    private void addVars(Map<String, Object> vars, FlowModel flowModel) {
        vars.put(EngineConstants.FLOW_NAME,
            Optional.ofNullable(flowModel).map(FlowModel::getBasicInfo).map(BasicInfo::getName).orElse(""));
        vars.put(FlowType.FLOW_TYPE, FlowType.TIMER);
    }

    private FlowModel getFlowModel(TaskDo taskDo, String processDefinitionKey,
        Map<String, Object> vars) {
        FlowModel flowModel;
        String userId = "";
        if (taskDo != null) {
            // 将任务id和启动人id放入流程环境变量中
            vars.put(TASK_ID, taskDo.getId());
            userId = Optional.ofNullable(taskDo.getUserId()).orElse("");
            flowModel = flowViewProvider.queryFlowDetail(taskDo.getFlowId());
            if (StringUtils.isNotBlank(taskDo.getFlowParameters())) {
                List<FlowParameter> flowParams = JacksonUtils.fromJson(taskDo.getFlowParameters(),
                    ReflectConst.FLOWMODEL_PARAMS_TYPETOKEN);

                modifyParameter(flowModel, flowParams, vars);
            }
        } else {
            // 兼容升级上来的定时流程
            flowModel = flowViewProvider.queryFlowDetailByDeployedId(processDefinitionKey);
            modifyParameter(flowModel, flowModel.getDefinition().getParas(), vars);
            userId = flowModel.getBasicInfo().getUserId();
        }

        vars.put(EngineConstants.USERID, userId);
        putUserName(vars, userId);
        return flowModel;
    }

    private void putUserName(Map<String, Object> vars, String userId) {
        try {
            LoginUserInfo userInfo = UserInfoUtils.fetchUserInfoByUserId(userId);
            vars.put(EngineConstants.EXECUTOR, userInfo.getUserName());
            vars.put(EngineConstants.EXECUTOR_ROLE_NAMES, CommonNotifyUtil.filterNonDefaultRoleNames(userInfo.getRoles()));
        } catch (Exception e) {
            RUNLOGGER.error("Failed to fetch user info by user id. Message: {}", e.getMessage());
            vars.put(EngineConstants.EXECUTOR, "");
            vars.put(EngineConstants.EXECUTOR_ROLE_NAMES, "");
        }
    }

    private Process getProcess(String processDefinitionKey) {
        List<Process> processes = deploymentExtProvider.queryProcessesByDefinitionKey(processDefinitionKey);
        if (CollectionUtils.isEmpty(processes)) {
            RUNLOGGER.info("The process:[{}] is already deleted, skip this time.", processDefinitionKey);
            return null;
        }
        if (flowViewProvider.executeCount()) {
            DEBLOGGER.error(
                "A maximum of 1000 processes can be executed at the same time. The maximum number has been reached");
            return null;
        }
        return  processes.get(0);
    }

    @Override
    public void interrupt() throws UnableToInterruptJobException {
        RUNLOGGER.error("processAutoStartTrigger Timeout.");
    }

    /**
     * Gets job key *
     *
     * @param jobExecutionContext job execution context
     * @return the job key
     */
    private JobKey getJobKey(JobExecutionContext jobExecutionContext) {
        Assert.notNull(jobExecutionContext, "jobExecutionContext is null.");
        JobDetail jobDetail = jobExecutionContext.getJobDetail();
        Assert.notNull(jobDetail, "jobExecutionContext.jobDetail is null.");
        JobKey jobKey = jobDetail.getKey();
        Assert.notNull(jobKey, "jobExecutionContext.jobDetail.jobKey is null.");
        return jobKey;
    }

    private void modifyParameter(FlowModel flowModel, List<FlowParameter> flowParameters, Map<String, Object> vars) {
        if (flowModel == null) {
            return;
        }
        String reviewStatus = flowModel.getBasicInfo().getReviewStatus();
        if (!EngineConstants.APPROVE.equals(reviewStatus)) {
            throw new com.huawei.cmp.foundation.service.exception.CMPException(
                FlowExceptionCode.FLOW_REVIEW_STATUS_ERROR);
        }
        if (CollectionUtils.isEmpty(flowParameters)) {
            return;
        }
        flowParameters.forEach(flowParameter -> {
            if (FlowParameter.FlowParameterType.PVALUETYPE.equals(flowParameter.getFlowParamType())) {
                vars.put(EngineConstants.PVALUETYPE, true);
            }
        });
    }
}
