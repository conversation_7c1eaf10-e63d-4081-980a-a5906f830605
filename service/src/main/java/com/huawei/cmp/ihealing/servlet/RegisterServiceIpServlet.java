/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.cmp.ihealing.servlet;

import static com.huawei.cmp.ihealing.constants.Constant.DEFAULT;
import static com.huawei.cmp.ihealing.constants.Constant.GLOBAL_NAME_UC_SERVICE_IP;
import static com.huawei.cmp.ihealing.constants.Constant.UC_SERVICE_IP;
import static com.huawei.cmp.ihealing.utils.EamClientUtil.batchRegisterMos;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.cmp.foundation.service.exception.CMPException;
import com.huawei.cmp.foundation.validation.ValidateException;
import com.huawei.cmp.ihealing.common.dao.provider.FlowViewProvider;
import com.huawei.cmp.ihealing.common.utils.HealingValidateUtils;
import com.huawei.cmp.ihealing.global.helper.GlobalHelper;
import com.huawei.cmp.ihealing.global.support.DefaultIHealingGlobalViewService;
import com.huawei.cmp.ihealing.in.api.flow.model.view.FlowModel;
import com.huawei.cmp.ihealing.in.api.global.model.GlobalModel;
import com.huawei.cmp.ihealing.out.api.model.RegisterMoRequest;
import com.huawei.cmp.ihealing.pkg.openapi.RegisterServiceIpReq;
import com.huawei.cmp.ihealing.process.core.cluster.DistributeLock;
import com.huawei.cmp.ihealing.utils.CommonNotifyUtil;
import com.huawei.cmp.ihealing.utils.IhealingServiceConfigUtil;
import com.huawei.cmp.ihealing.utils.ServletUtil;
import com.huawei.cmp.ihealing.utils.SpringConfigTool;
import com.huawei.i2000.cbb.security.utils.CleanPWDUtil;
import com.huawei.oms.util.StringUtil;

import com.alibaba.fastjson2.JSONArray;

import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 功能描述 注册uc_service_ip接口
 *
 * <AUTHOR>
 * @since 2022-11-25
 */
public class RegisterServiceIpServlet extends HttpServlet {
    /**
     * serialVersionUID
     */
    private static final long serialVersionUID = 6602200524650128398L;

    /**
     * LOGGER
     */
    private static final OssLog LOGGER = OssLogFactory.getLog(RegisterServiceIpServlet.class);

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) {
        HttpContext context = new HttpContext(req, resp);
        try {
            // 从req中获取需要的对象
            RegisterServiceIpReq registerServiceIpReq = ServletUtil.getParam(req, RegisterServiceIpReq.class);
            Assert.notNull(registerServiceIpReq, "registerServiceIpReq is null");
            LOGGER.warn("RegisterServiceIp is {}", registerServiceIpReq.getServiceIp());
            HealingValidateUtils.validate(registerServiceIpReq);
            // 更新配置文件
            IhealingServiceConfigUtil.setProperty(UC_SERVICE_IP, registerServiceIpReq.getServiceIp());
            // 更新或创建全局参数并更新流程中全局参数值
            updateFlowModelGlobalParam(createOrModifyGlobalModel(context, registerServiceIpReq));

            // 注册虚拟机网元
            CompletableFuture.runAsync(() -> registerVmMo(registerServiceIpReq));
        } catch (ValidateException e) {
            LOGGER.error("RegisterServiceIp is exception ", e);
        }
    }

    private void registerVmMo(RegisterServiceIpReq registerServiceIpReq) {
        DistributeLock lock = new DistributeLock(registerServiceIpReq.getServiceIp());
        String value = "";
        String result = "";
        int count = 10;
        // 重试注册vm10分钟
        while (count > 0) {
            try {
                if (!lock.acquireLock(1, TimeUnit.SECONDS)) {
                    LOGGER.info("registerVmMo {} has been signaled.", registerServiceIpReq.getServiceIp());
                }
                // 获取配置插件密码
                result = CommonNotifyUtil.queryConfig("uniagent_pass", registerServiceIpReq.getServiceIp());
                if (StringUtil.isEmpty(result)) {
                    LOGGER.warn("get common notify is empty ,register ip is {}", registerServiceIpReq.getServiceIp());
                    return;
                }
                value = Optional.ofNullable(JSONArray.parseArray(result))
                    .map(array -> array.getJSONObject(0))
                    .map(jsonObject -> jsonObject.getString("value"))
                    .orElse("");
                if (StringUtil.isEmpty(value)) {
                    LOGGER.error("registerVmMo value is empty");
                    return;
                }
                // 注册虚拟机
                int resultCode = registerMo(registerServiceIpReq, value);
                if (resultCode == 200) {
                    LOGGER.warn("registerVmMo  is success");
                    break;
                }
                throw new CMPException("register mo fail");
            } catch (Exception e) {
                LOGGER.error("registerVmMo is exception ", e);
                try {
                    TimeUnit.SECONDS.sleep(60);
                } catch (InterruptedException ex) {
                    LOGGER.error("registerVmMo is InterruptedException ", ex);
                }
            } finally {
                count--;
                lock.releaseLock();
                CleanPWDUtil.clearStrPwd(value);
                CleanPWDUtil.clearStrPwd(result);
            }
        }
    }

    private int registerMo(RegisterServiceIpReq registerServiceIpReq, String value) {
        List<RegisterMoRequest> registerMoRequestList = new ArrayList<>();
        RegisterMoRequest request = new RegisterMoRequest();
        request.setName("SA_" + registerServiceIpReq.getServiceIp());
        request.setAddress(registerServiceIpReq.getServiceIp());
        request.setUserValueP(value);
        registerMoRequestList.add(request);
        return batchRegisterMos(registerMoRequestList);
    }

    private void updateFlowModelGlobalParam(GlobalModel globalModel) {
        FlowViewProvider flowViewProvider = SpringConfigTool.getBean(FlowViewProvider.class);
        List<FlowModel> flowModels = flowViewProvider.queryAllFlowModelsByParamId(globalModel.getGlobalParamId());
        for (FlowModel flowModel : flowModels) {
            flowViewProvider.settingParams(globalModel, flowModel, flowModel.getDefinition().getParas());
            flowViewProvider.settingParams(globalModel, flowModel, flowModel.getDefinition().getDefaultParas());
            flowViewProvider.modifyFlow(flowModel);
        }
    }

    private static GlobalModel createOrModifyGlobalModel(HttpContext context, RegisterServiceIpReq registerServiceIpReq)
        throws ValidateException {
        DefaultIHealingGlobalViewService defaultIHealingGlobalViewService = SpringConfigTool.getBean(
            DefaultIHealingGlobalViewService.class);
        GlobalHelper helper = SpringConfigTool.getBean(GlobalHelper.class);
        GlobalModel globalModel = helper.queryGlobal(null, DEFAULT, GLOBAL_NAME_UC_SERVICE_IP);
        // 判断是否存在全局参数，存在就修改，不存在就新增
        if (globalModel != null) {
            globalModel.setValue(registerServiceIpReq.getServiceIp());
            defaultIHealingGlobalViewService.modifyGlobal(globalModel, context);
        } else {
            globalModel = GlobalHelper.buildUcServiceIpGlobalModel(registerServiceIpReq.getServiceIp());
            globalModel.setGlobalParamId(defaultIHealingGlobalViewService.createGlobal(globalModel, context));
        }
        return globalModel;
    }

}
