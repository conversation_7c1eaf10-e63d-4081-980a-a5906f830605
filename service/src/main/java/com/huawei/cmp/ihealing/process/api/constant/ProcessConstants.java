/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2019-2019. All rights reserved.
 */

package com.huawei.cmp.ihealing.process.api.constant;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 功能描述：流程引擎常量定义
 *
 * @since 2020 -06-21
 * <AUTHOR>
 */
public interface ProcessConstants {
    /**
     * PROCESS_ENGINE
     */
    String PROCESS_ENGINE = "ProcessEngine";

    /**
     * 运行期间添加的内部变量
     * 
     * @since 2020 -11-04
     */
    interface Run {
        /**
         * LAST_SVC_TASK_RES_KEY
         */
        String LAST_SVC_TASK_RES_KEY = "##LSTR##";

        /**
         * 错误节点的历史变量前缀
         */
        String EXCEP_ACT_DEF_KEY_PRE = "##EADK##";

        /**
         * 标记流程是否是异常
         */
        String IS_EXCEPTION_HAPD_KEY = "##IEHK##";

        /**
         * 原子执行超时时间标识
         */
        String ATOM_EXECUTE_TIMEOUT_KEY = "##AETK##";

        /**
         * 委托异常标识
         */
        String DELEGATOR_EXCEPTION = "##dException##";

        /**
         * 流程实例id
         */
        String PROC_INST_ID = "processInstanceId";

        /**
         * START_STATUS_KEY
         */
        String START_STATUS_KEY = "START_STATUS";

        /**
         * START_STATUS_STARTING
         */
        String START_STATUS_STARTING = "STARTING";

        /**
         * START_STATUS_OK
         */
        String START_STATUS_OK = "START_OK";

        /**
         * 延时,单位s
         */
        int DELAY_TIME = 60;
    }

    /**
     * 返回变量过滤内部变量
     * 
     * @since 2020 -11-04
     */
    interface Filter {
        /**
         * Filtered vars keys
         */
        List<String> FILTERED_VARS_KEYS = Collections.unmodifiableList(
            Arrays.asList(ProcessConstants.Run.LAST_SVC_TASK_RES_KEY, ProcessConstants.Run.EXCEP_ACT_DEF_KEY_PRE));
    }

    /**
     * Interceptor inner vars
     * 
     * @since 2020 -11-04
     */
    interface InterceptorInnerVars {
        /**
         * PROCESS_INSTANCE_ID
         */
        String PROCESS_INSTANCE_ID = "processInstanceId";

        /**
         * INSERT
         */
        String INSERT = "INSERT";

        /**
         * UPDATE
         */
        String UPDATE = "UPDATE";

        /**
         * DELETE
         */
        String DELETE = "DELETE";

        /**
         * EVENT_LISTENERS
         */
        String EVENT_LISTENERS = "eventListeners";
    }

    /**
     * 定义了配置常量
     * 
     * @since 2020 -11-04
     */
    interface Config {
        /**
         * 核心线程数配置Key
         */
        String CORE_THREAD_NUM = "engine_core_thread_number";

        /**
         * 默认核心线程数
         */
        int DEFAULT_CORE_THREAD_NUM = 10;

        /**
         * 自动触发数配置Key
         */
        String AUTO_NEXT_NUM = "activity_auto_next_number";

        /**
         * 默认自动触发数
         */
        int DEFAULT_AUTO_NEXT_NUM = 10;

        /**
         * 自动触发间隔配置Key
         */
        String AUTO_NEXT_INT = "activity_auto_next_interval";

        /**
         * 默认自动触发间隔
         */
        String DEFAULT_AUTO_NEXT_INT = "*/5 * * * * ?";

        /**
         * 引擎名称配置Key
         */
        String ENGINE_NAME = "engine_name";

        /**
         * 默认引擎名称
         */
        String DEFAULT_ENGINE_NAME = "unknown";

        /**
         * 分布式锁ZooKeeper路径
         */
        String DISTRIBUTED_LOCK_PATH = "/cmp/processengine/locks/";

    }

    /**
     * 定义了模块名称
     * 
     * @since 2020 -11-04
     */
    interface Module {
        /**
         * RUNTIME
         */
        String RUNTIME = "RUNTIME";

        /**
         * REPOSITORY
         */
        String REPOSITORY = "REPOSITORY";

        /**
         * USER_TASK
         */
        String USER_TASK = "USER_TASK";

        /**
         * HISTORY
         */
        String HISTORY = "HISTORY";
    }

    /**
     * 定义了模型解析用到的常量
     * 
     * @since 2020 -11-04
     */
    interface Parser {
        /**
         * ServiceTask默认超时时间
         */
        long DEFAULT_TIMEOUT = 86400L;

        /**
         * ReceiveTask默认后缀
         */
        String RECEIVE_SUFFIX = "-Receive";

        /**
         * 部署文件后缀
         */
        String DEPLOY_FILE_SUFFIX = ".bpmn";

        /**
         * ServiceTask实现类型为Bean
         */
        String DELEGATE_EXPRESSION = "delegateExpression";

        /**
         * ServiceTask实现类型为Class
         */
        String CLASS = "class";

        /**
         * 监听事件类型-启动
         */
        String START = "start";

        /**
         * 监听事件类型-结束
         */
        String END = "end";

        /**
         * 内部错误捕获事件后缀
         */
        String ERROR_BOUNDARY_SUFFIX = "-ErrorBoundary";

        /**
         * 内部错误捕获事件连线后缀
         */
        String ERROR_BOUNDARY_SEQ_SUFFIX = "-ErrorSequence";

        /**
         * 定时任务捕获事件后缀
         */
        String TIMER_BOUNDARY_SUFFIX = "-TimerBoundary";

        /**
         * 定时任务捕获事件连线后缀
         */
        String TIMER_BOUNDARY_SEQ_SUFFIX = "-TimerSequence";


        /**
         * 部署名称后缀
         */
        String DEPLOYMENT_SUFFIX = "_Deployment";

        /**
         * 默认的流程启动监听器
         */
        String DEFAULT_PROCESS_BEGIN_LISTENER = "innerProcessBeginListener";
    }

    /**
     * Query
     * 
     * @since 2020 -11-04
     */
    interface Query {
        /**
         * DEFAULT_PAGING_LIMIT
         */
        int DEFAULT_PAGING_LIMIT = 100;

        /**
         * DEFAULT_PAGING_OFFSET
         */
        int DEFAULT_PAGING_OFFSET = 0;

        /**
         * FINISHED
         */
        String FINISHED = "FINISHED";

        /**
         * EXCEPTION
         */
        String EXCEPTION = "EXCEPTION";

        /**
         * UNFINISHED
         */
        String UNFINISHED = "UNFINISHED";

        /**
         * UNLOADED
         */
        String UNLOADED = "UNLOADED";

        /**
         * SERVICE_TASK
         */
        String SERVICE_TASK = "serviceTask";

        /**
         * RECEIVE_TASK
         */
        String RECEIVE_TASK = "receiveTask";

        /**
         * START_EVENT
         */
        String START_EVENT = "startEvent";

        /**
         * PROC_DEF_ID_COLUMN
         */
        String PROC_DEF_ID_COLUMN = "PROC_DEF_ID_";

        /**
         * COUNT_COLUMN
         */
        String COUNT_COLUMN = "COUNT_";
    }
}
