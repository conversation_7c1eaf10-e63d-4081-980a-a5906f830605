/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2019-2019. All rights reserved.
 */

package com.huawei.cmp.ihealing.flow.service;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.cmp.foundation.service.exception.CMPException;
import com.huawei.cmp.foundation.validation.ValidateUtils;
import com.huawei.cmp.ihealing.common.dao.provider.FlowTaskProvider;
import com.huawei.cmp.ihealing.common.dao.provider.FlowViewProvider;
import com.huawei.cmp.ihealing.common.utils.HealingValidateUtils;
import com.huawei.cmp.ihealing.common.utils.IOUtil;
import com.huawei.cmp.ihealing.dao.OpenApiDao;
import com.huawei.cmp.ihealing.flow.engine.constants.EngineConstants;
import com.huawei.cmp.ihealing.flow.engine.conveter.IHealingProcessConverter;
import com.huawei.cmp.ihealing.flow.engine.helper.ProcessContextHelper;
import com.huawei.cmp.ihealing.in.api.flow.model.FlowStatus;
import com.huawei.cmp.ihealing.in.api.flow.model.task.BatchCreateTaskResponse;
import com.huawei.cmp.ihealing.in.api.flow.model.task.FlowType;
import com.huawei.cmp.ihealing.in.api.flow.model.task.HealingFlowTask;
import com.huawei.cmp.ihealing.in.api.flow.model.task.QueryTaskListRequest;
import com.huawei.cmp.ihealing.in.api.flow.model.task.StartFlowRequest;
import com.huawei.cmp.ihealing.in.api.flow.model.view.FlowModel;
import com.huawei.cmp.ihealing.in.api.flow.service.IHealingFlowEngineService;
import com.huawei.cmp.ihealing.in.api.model.EventModel;
import com.huawei.cmp.ihealing.pkg.openapi.FlowInformation;
import com.huawei.cmp.ihealing.process.api.constant.ErrorCode;
import com.huawei.cmp.ihealing.process.api.define.Process;
import com.huawei.cmp.ihealing.process.api.model.validation.ValidationErr;
import com.huawei.cmp.ihealing.process.api.service.RepositoryService;
import com.huawei.cmp.ihealing.process.api.service.RuntimeService;
import com.huawei.cmp.ihealing.process.core.cluster.DistributeLock;
import com.huawei.cmp.ihealing.process.core.parser.ProcessParser;
import com.huawei.cmp.ihealing.process.core.validation.ValidateErrConverter;
import com.huawei.cmp.ihealing.utils.JacksonUtils;
import com.huawei.cmp.ihealing.utils.SpringConfigTool;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.converter.BpmnXMLConverter;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.validation.ProcessValidator;
import org.flowable.validation.ProcessValidatorFactory;
import org.flowable.validation.ValidationError;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.io.Reader;
import java.io.StringReader;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import javax.validation.groups.Default;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamReader;

/**
 * 功能描述：故障自愈流程引擎
 *
 * <AUTHOR>
 * @since 2020-09-03
 */
@Component("defaultIHealingFlowEngineService")
public class DefaultIHealingFlowEngineService implements IHealingFlowEngineService {
    private static final OssLog DEBLOGGER = OssLogFactory.getLogger(DefaultIHealingFlowEngineService.class);

    private static final OssLog RUNLOGGER = OssLogFactory.getLogger(DefaultIHealingFlowEngineService.class);

    private static final OssLog INTLOGGER = OssLogFactory.getLogger(DefaultIHealingFlowEngineService.class);

    private static final String CANCEL_REASON = "Manual";

    private static final int TIMEOUT = 0;


    private static final String NO_AUTH = "NOAUTH";

    @Autowired
    @Qualifier(value = "repositoryService")
    private RepositoryService repositoryService;

    @Autowired
    @Qualifier(value = "runtimeService")
    private RuntimeService runtimeService;

    @Autowired
    private IHealingProcessConverter iHealingProcessConverter;

    @Autowired
    private FlowTaskProvider flowTaskProvider;

    @Autowired
    private FlowViewProvider flowViewProvider;

    @Override
    public String deployProcess(FlowModel flow) throws CMPException {
        INTLOGGER.info("deployProcess request [{}]", flow.getBasicInfo().getFlowId());
        HealingValidateUtils.validate(flow);
        Process process = iHealingProcessConverter.convert(flow);
        RUNLOGGER.info("After convert, process is: [{}] ", process.getName());

        try {
            BpmnModel bpmnModel = convert(process);
            repositoryService.validateProcess(bpmnModel, true);
            String definitionKey = repositoryService.deployProcess(process, bpmnModel);
            INTLOGGER.info("deployProcess response [{}]", definitionKey);
            return definitionKey;
        } catch (com.huawei.cmp.foundation.exception.define.CMPException e) {
            DEBLOGGER.error("Deploy process Failed", e);
            throw new CMPException(e.getErrorCode(), e.getParams());
        } catch (Exception e) {
            RUNLOGGER.error("convert process Failed,", e);
            throw new CMPException(ErrorCode.CONVERT_FLOW_MODEL_FAILED);
        }
    }

    @Override
    public String startProcess(String deployedId, final EventModel event, final Map<String, Object> vars)
        throws CMPException {
        INTLOGGER.info("startProcess request [{}], [{}], [{}]", deployedId, event, vars);

        Map<String, Object> startVars = new HashMap<>(vars);
        flowViewProvider.judgeParameter(deployedId, startVars);
        if (null != event) {
            RUNLOGGER.info("FlowSource: [{}] can be triggered by [{}] bind with alarmId: [{}].", deployedId,
                FlowType.ALARM, event.getAlarmId());

            startVars.put(EngineConstants.Definitions.EVENT_KEY, JacksonUtils.toJson(event));
            startVars.put(FlowType.FLOW_TYPE, FlowType.ALARM);
        }

        try {
            final String processInstanceId = runtimeService.startProcess(deployedId, startVars);
            INTLOGGER.info("startProcess response [{}]", processInstanceId);
            return processInstanceId;
        } catch (com.huawei.cmp.foundation.exception.define.CMPException e) {
            DEBLOGGER.error("Start process Failed", e);
            throw new CMPException(e.getErrorCode(), e.getParams());
        }
    }

    @Override
    public void uninstallProcess(String deployedId) throws CMPException {
        INTLOGGER.info("uninstallProcess request [{}]", deployedId);
        DistributeLock lock = new DistributeLock(deployedId);
        try {
            if (!lock.acquireLock(TIMEOUT, TimeUnit.SECONDS)) {
                return;
            }
            if (isProcessRunning(deployedId)) {
                // 先从runtimeService中取消流程
                List<String> instanceIds = runtimeService.queryProcessInstanceIds(deployedId);
                for (String instance : instanceIds) {
                    runtimeService.cancelProcess(instance, CANCEL_REASON);
                }
            }
            repositoryService.deleteProcess(deployedId);
        } catch (com.huawei.cmp.foundation.exception.define.CMPException e) {
            DEBLOGGER.error("Uninstall process Failed", e);
            INTLOGGER.info("uninstallProcess response [{}]", "FAILED");
            throw new CMPException(e.getErrorCode(), e.getParams());
        } catch (Exception exception) {
            DEBLOGGER.error("Uninstall process Failed", exception);
        } finally {
            lock.releaseLock();
        }
    }

    @Override
    public boolean isProcessRunning(String deployedId) throws CMPException {
        INTLOGGER.info("isProcessRunning request [{}]", deployedId);
        try {
            if (runtimeService.isProcessRunning(deployedId)) {
                return true;
            }
            QueryTaskListRequest queryTaskListRequest = new QueryTaskListRequest();
            queryTaskListRequest.setDeployedId(deployedId);
            queryTaskListRequest.setStatus(FlowStatus.EXECUTING.name());
            List<HealingFlowTask> tasks = flowTaskProvider.queryHealingFlowTasksList(queryTaskListRequest);
            return CollectionUtils.isNotEmpty(tasks);
        } catch (com.huawei.cmp.foundation.exception.define.CMPException e) {
            RUNLOGGER.error("Query is Process Running Failed, {}", e.getMessage());
            DEBLOGGER.error("Query is Process Running Failed", e);
            INTLOGGER.info("isProcessRunning response [{}]", "FAILED");
            throw new CMPException(e.getErrorCode(), e.getParams());
        }
    }

    @Override
    public BatchCreateTaskResponse startFlowByDefinition(HttpContext context, StartFlowRequest request) {
        Assert.notNull(request, "request is null");
        INTLOGGER.info("startFlowByDefinition {}", request);
        String flowName;
        BatchCreateTaskResponse response = new BatchCreateTaskResponse();
        try {
            // 从req中获取需要的对象
            flowName = request.getFlowName();
            String groupName = request.getFlowGroup();
            OpenApiDao openApiDao = SpringConfigTool.getBean(OpenApiDao.class);
            // 获取流程id，只能查询到一个id
            String flowId = ProcessContextHelper.getFlowId(flowName, groupName, openApiDao);
            if (StringUtils.isBlank(flowId) || NO_AUTH.equals(flowId)) {
                RUNLOGGER.error("flow's reviewstatus is not approve or not find flow");
                response.setTargetObj("flow's reviewstatus is not approve or not find flow");
                return response;
            }
            FlowModel flowModel = ProcessContextHelper.getFlowModel(flowId);
            flowModel.getDefinition().setNodes(request.getDefinition().getNodes());
            flowModel.getDefinition().setEdges(request.getDefinition().getEdges());
            flowModel.getDefinition().setParas(request.getDefinition().getParas());
            FlowInformation flowInformation = ProcessContextHelper.validateFlowModel(flowModel,
                ProcessContextHelper.getFlowModel(flowId));
            if (flowInformation.isChange()) {
                RUNLOGGER.error("the process has been modified.");
                response.setTargetObj("the process has been modified.");
                return response;
            }
            Map<String, Object> startVars = new HashMap<>(1 << 4);
            startVars.put(FlowType.FLOW_TYPE, FlowType.INTERNAL);
            startVars.put(EngineConstants.Definitions.EVENT_KEY, request.getFlowType());
            return ProcessContextHelper.startFlowByVars(context, flowModel, startVars);
        } catch (ServiceException e) {
            RUNLOGGER.error("deploy or execute failed.");
            response.setTargetObj("deploy or execute failed.");
        }
        return response;
    }

    private BpmnModel convert(Process process) throws Exception {
        Assert.notNull(process, "process is null");
        DEBLOGGER.debug("Convert process {}.", process.getName());

        // 1.模型校验
        ValidateUtils.validate(process, Default.class);

        // 2.模型转换
        BpmnModel bpmnModel = ProcessParser.parse(process);

        // 3.校验流程
        Reader reader = null;
        XMLStreamReader xmlStreamReader = null;
        try {
            BpmnXMLConverter bpmnXMLConverter = new BpmnXMLConverter();
            String xml = new String(bpmnXMLConverter.convertToXML(bpmnModel), "UTF-8");
            reader = new StringReader(xml);
            XMLInputFactory factory = XMLInputFactory.newInstance();
            // 防止解析xml时引用内外部实体引用
            factory.setProperty(XMLInputFactory.IS_SUPPORTING_EXTERNAL_ENTITIES, false);
            factory.setProperty(XMLInputFactory.IS_REPLACING_ENTITY_REFERENCES, false);
            factory.setProperty(XMLInputFactory.SUPPORT_DTD, false);
            xmlStreamReader = factory.createXMLStreamReader(reader);
            bpmnModel = bpmnXMLConverter.convertToBpmnModel(xmlStreamReader);
        } finally {
            IOUtil.closeQuietly(reader);
            if (xmlStreamReader != null) {
                xmlStreamReader.close();
            }
        }

        ProcessValidatorFactory processValidatorFactory = new ProcessValidatorFactory();
        ProcessValidator processValidator = processValidatorFactory.createDefaultProcessValidator();

        List<ValidationError> errors = processValidator.validate(bpmnModel);
        List<ValidationErr> validationErrs = ValidateErrConverter.convert(errors);
        if (CollectionUtils.isNotEmpty(validationErrs)) {
            RUNLOGGER.error("validationErrs = {}", validationErrs.toString());
            throw new com.huawei.cmp.foundation.exception.define.CMPException(ErrorCode.VALIDATE_PARAMETERS_FAILED);
        }

        return bpmnModel;
    }
}
