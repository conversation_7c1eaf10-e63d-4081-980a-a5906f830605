/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2019-2019. All rights reserved.
 */

package com.huawei.cmp.ihealing.process.api.define.end;

import com.huawei.cmp.foundation.validation.constraint.ByteLength;
import com.huawei.cmp.ihealing.process.api.define.NodeType;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 功能描述：描述了流程图中错误结束节点<br>
 * 使用场景1：位于异常捕获节点下游，任务节点执行异常后，进入异常处理分支，最终到达错误结束节点；<br>
 * 使用场景2：主流程进入错误结束节点，子流程捕获错误结束事件，通过错误开始节点捕获错误结束，启动子流程；<br>
 * 使用场景3：子流程进入错误结束节点，依附于该子流程的错误捕获节点捕获错误结束事件，进入异常处理分支；<br>
 * 当流程执行到错误结束事件，流程的当前分支就会结束，并抛出一个错误,这个错误可以被对应的中间边界错误事件捕获<br>
 * 如果找不到匹配的边界错误事件，就会抛出一个异常<br>
 *
 * <AUTHOR>
 * @since 2020 -06-21
 */
public class ErrorEndNode extends NoneEndNode {
    /**
     * 错误码
     */
    @NotNull
    @ByteLength(min = 1, max = 128)
    @Pattern(regexp = "-?[0-9]\\d*")
    protected String errorCode;

    /**
     * 指定错误码的构造方法
     *
     * @param errorCode 错误码
     */
    public ErrorEndNode(String errorCode) {
        this.errorCode = errorCode;
        this.nodeType = NodeType.END;
    }

    /**
     * 获取节点结束类型
     *
     * @return 结束类型
     */
    @Override
    public EndType getEndType() {
        return EndType.ERROR;
    }

    /**
     * Gets error code *
     * 
     * @return the error code
     */
    public String getErrorCode() {
        return errorCode;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
