/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.cmp.ihealing.process.core.delegate;

import static com.huawei.cmp.ihealing.process.api.constant.ProcessConstants.Run.ATOM_EXECUTE_TIMEOUT_KEY;
import static com.huawei.cmp.ihealing.process.api.constant.ProcessConstants.Run.DELAY_TIME;
import static com.huawei.cmp.ihealing.process.api.constant.ProcessConstants.Run.DELEGATOR_EXCEPTION;

import com.huawei.bsp.log.OssLog;
import com.huawei.cmp.foundation.exception.define.CMPException;
import com.huawei.cmp.ihealing.common.constant.StringConst;
import com.huawei.cmp.ihealing.flow.engine.constants.EngineConstants;
import com.huawei.cmp.ihealing.process.api.constant.ErrorCode;
import com.huawei.cmp.ihealing.process.api.constant.ProcessConstants;
import com.huawei.cmp.ihealing.process.api.utils.LogUtil;
import com.huawei.cmp.ihealing.process.core.parser.NodeDocument;
import com.huawei.cmp.ihealing.utils.CoverPasswordUtil;

import org.flowable.engine.delegate.BpmnError;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.FlowableFutureJavaDelegate;

import java.text.MessageFormat;
import java.util.Optional;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 异步任务执行代理
 *
 * <AUTHOR>
 * @since 2024-10-18
 */
public abstract class AsyncTaskDelegator implements FlowableFutureJavaDelegate<DelegateExecution, ProcessContext> {

    private static final OssLog LOGGER = LogUtil.getRuntimeLogger(AsyncTaskDelegator.class);

    @Override
    public DelegateExecution prepareExecutionData(DelegateExecution delegateExecution) {
        NodeDocument nodeDocument = ExecutionHelper.loadNodeDocument(delegateExecution);
        // 1.获取超时时间
        long timeout = ExecutionHelper.getNodeTimeout(nodeDocument);
        // 2.加载节点属性
        ExecutionHelper.setNodeProperties(delegateExecution, nodeDocument);
        delegateExecution.setVariable(ATOM_EXECUTE_TIMEOUT_KEY, timeout);
        return delegateExecution;
    }


    @Override
    public ProcessContext execute(DelegateExecution execution) {
        String processDefinitionId = execution.getProcessDefinitionId();
        String processInstanceId = execution.getProcessInstanceId();
        String currentActivityId = execution.getCurrentActivityId();

        String behavior = MessageFormat.format(
            "invoke delegate for processDefinitionId:[{0}], processInstanceId:[{1}], activityId:[{2}]",
            processDefinitionId, processInstanceId, currentActivityId);
        Future<String> future = null;
        ProcessContext context = null;
        try {

            LOGGER.debug("Before {}, variables is:[{}], variablesLocal is:[{}]", behavior,
                CoverPasswordUtil.coverPassword(execution.getVariables()),
                CoverPasswordUtil.coverPassword(execution.getVariablesLocal()));

            // 3.构造流程上下文
            context = new ProcessContext(execution.getVariables(), execution.getVariablesLocal()).processDefinitionId(
                    execution.getProcessDefinitionId())
                .processInstanceId(execution.getProcessInstanceId())
                .nodeDefinitionKey(execution.getCurrentActivityId());

            // 4.提交任务执行
            LOGGER.info("Start to {}", behavior);
            future = TaskExecutorPool.getPool().submitTask(new TaskExecutor(this, context));

            // 5.获取结果,增加60秒，避免在超时时间结束时，主线程先关闭，子线程异常退出
            String result = future.get(
                Long.parseLong(execution.getVariable(ATOM_EXECUTE_TIMEOUT_KEY).toString()) + DELAY_TIME,
                TimeUnit.SECONDS);
            LOGGER.info("End to {}", behavior);

            // 6.更新流程上下文
            context.getVariables().put(ProcessConstants.Run.LAST_SVC_TASK_RES_KEY, result);
            LOGGER.debug("After  {}, variables is:[{}], variablesLocal is:[{}]", behavior,
                CoverPasswordUtil.coverPassword(execution.getVariables()),
                CoverPasswordUtil.coverPassword(execution.getVariablesLocal()));
        } catch (Exception t) {
            LOGGER.error("TaskDelegator execute exception.", t);
            if (context == null) {
                context = new ProcessContext();
            }
            context.setExceptionStatus(ProcessContext.ExceptionStatus.EXCEPTION);
            if (!Optional.ofNullable(t.getMessage()).orElse("").contains(StringConst.TASK_STOPPED_FLAG)) {
                // 停止流程时不算异常场景。此处为异常场景
                LOGGER.error("Failed to {},.", behavior, t);
                context.setVariable(DELEGATOR_EXCEPTION, t);
                if (future != null) {
                    future.cancel(true);
                }
            } else {
                context.setExceptionStatus(ProcessContext.ExceptionStatus.STOP_EXCEPTION);
            }
        }
        return context;
    }

    @Override
    public void afterExecution(DelegateExecution execution, ProcessContext context) {
        if (context == null) {
            LOGGER.warn("context is empty");
            return;
        }
        if (context.getExceptionStatus() == ProcessContext.ExceptionStatus.EXCEPTION) {
            // 其他异常场景
            LOGGER.warn("context has exception");
            ExecutionHelper.recordException(execution);
            Object exception = context.getVariable(DELEGATOR_EXCEPTION);
            if (exception != null && exception instanceof Exception) {
                handleException((Exception) exception);
            }
            return;
        }

        if (context.getExceptionStatus() == ProcessContext.ExceptionStatus.STOP_EXCEPTION) {
            // 停止流程
            LOGGER.warn("stop flow  processDefinitionId {},.", execution.getProcessDefinitionId());
            execution.setVariable(EngineConstants.FlowStatus.STOPPED, Boolean.TRUE);
            return;
        }

        ExecutionHelper.updateVariables(execution, context);
        LOGGER.info("Before {}, variables is:[{}], variablesLocal is:[{}]", context.getLocalVariables(),
            CoverPasswordUtil.coverPassword(execution.getVariables()),
            CoverPasswordUtil.coverPassword(execution.getVariablesLocal()));
    }

    /**
     * <p>
     * 异常处理<br>
     *
     * @param exception 异常
     * @throws BpmnError bpmn error
     */
    private void handleException(Exception exception) throws BpmnError {

        if (exception instanceof TimeoutException) {
            throw new BpmnError(ErrorCode.TIME_OUT_ERROR);
        }
        if (exception.getCause() != null && exception.getCause() instanceof CMPException) {
            throw new BpmnError(((CMPException) exception.getCause()).getErrorCode());
        }
        throw new BpmnError(ErrorCode.INTERNAL_ERROR);
    }

    /**
     * <p>
     * 实现任务内容<br>
     *
     * @param context 流程上下文
     * @return the string
     * @throws Exception 执行异常
     */
    public abstract String delegate(ProcessContext context) throws Exception;
}
