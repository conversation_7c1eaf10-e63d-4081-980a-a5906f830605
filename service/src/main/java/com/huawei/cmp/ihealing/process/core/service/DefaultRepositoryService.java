/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2019-2019. All rights reserved.
 */

package com.huawei.cmp.ihealing.process.core.service;

import com.huawei.bsp.log.OssLog;
import com.huawei.cmp.foundation.exception.define.CMPException;
import com.huawei.cmp.foundation.task.TaskException;
import com.huawei.cmp.ihealing.process.api.constant.ErrorCode;
import com.huawei.cmp.ihealing.process.api.constant.ProcessConstants;
import com.huawei.cmp.ihealing.process.api.define.Process;
import com.huawei.cmp.ihealing.process.api.model.validation.ValidationErr;
import com.huawei.cmp.ihealing.process.api.service.RepositoryService;
import com.huawei.cmp.ihealing.process.api.utils.LogUtil;
import com.huawei.cmp.ihealing.process.core.ActivitiBeanProxy;
import com.huawei.cmp.ihealing.process.core.cluster.DistributeTaskHelper;
import com.huawei.cmp.ihealing.process.core.config.ConfigHolder;
import com.huawei.cmp.ihealing.process.core.validation.ExtendValidatorSetFactory;
import com.huawei.cmp.ihealing.process.core.validation.ValidateErrConverter;
import com.huawei.cmp.ihealing.process.dao.provider.DeploymentExtProvider;
import com.huawei.cmp.ihealing.process.dao.provider.HistoricProcDefExtProvider;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.engine.repository.DeploymentQuery;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.validation.ProcessValidator;
import org.flowable.validation.ProcessValidatorFactory;
import org.flowable.validation.ProcessValidatorImpl;
import org.flowable.validation.ValidationError;
import org.flowable.validation.validator.Validator;
import org.flowable.validation.validator.ValidatorSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.text.MessageFormat;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 功能描述：流程管理实现
 *
 * <AUTHOR>
 * @since 2020 -07-30
 */
@Component("repositoryService")
public class DefaultRepositoryService implements RepositoryService {
    /**
     * DEBLOGGER
     */
    private static final OssLog DEBLOGGER = LogUtil.getDebugLogger(DefaultRepositoryService.class);

    /**
     * RUNLOGGER
     */
    private static final OssLog RUNLOGGER = LogUtil.getRuntimeLogger(DefaultRepositoryService.class);

    /**
     * INTLOGGER
     */
    private static final OssLog INTLOGGER = LogUtil.getInterfaceLogger(DefaultRepositoryService.class);

    /**
     * Deployment ext provider
     */
    @Autowired
    @Qualifier(value = "deploymentExtProvider")
    private DeploymentExtProvider deploymentExtProvider;

    @Autowired
    private HistoricProcDefExtProvider historicProcDefExtProvider;

    /**
     * Distribute task helper
     */
    @Autowired
    @Qualifier(value = "distributeTaskHelper")
    private DistributeTaskHelper distributeTaskHelper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deployProcess(Process process, BpmnModel bpmnModel) throws CMPException {
        String behavior = MessageFormat.format("deploy process: [{0}]", process.getName());
        String entry = MessageFormat.format("Begin to {0}.", behavior);
        INTLOGGER.info("deployProcess request [{}]", entry);
        try {
            // 1.转换模型
            String definitionKey = bpmnModel.getProcesses().get(0).getId();
            DEBLOGGER.debug("Success parse bpmnModel: [{}]", bpmnModel);

            // 2.部署流程
            RUNLOGGER.info("Start invoke activiti:repositoryService:createDeployment{}.", process);
            String deploymentId = ActivitiBeanProxy.getRepositoryService()
                .createDeployment()
                .addBpmnModel(process.getBpmnFileName(), bpmnModel)
                .name(process.getDeploymentName())
                .deploy()
                .getId();
            RUNLOGGER.info("End invoke activiti:repositoryService:createDeployment.");
            // 3.插入部署扩展信息
            ProcessDefinition processDefinition = ActivitiBeanProxy.getRepositoryService()
                .createProcessDefinitionQuery()
                .processDefinitionKey(process.getDefinitionKey())
                .deploymentId(deploymentId)
                .singleResult();
            Assert.notNull(processDefinition, "processDefinition is null.");
            String engineName = ConfigHolder.getProperty(ProcessConstants.Config.ENGINE_NAME,
                ProcessConstants.Config.DEFAULT_ENGINE_NAME);
            deploymentExtProvider.deployProcess(engineName, process.getGroup(), processDefinition.getKey(),
                processDefinition.getId(), process);

            // 4.流程是否要分布式定时执行
            processTimerTask(definitionKey, process.getCron());
            String exit = MessageFormat.format("Success to {0}, create process definition key: [{1}].", behavior,
                definitionKey);
            INTLOGGER.info("deployProcess  EXI response {}", exit);

            return definitionKey;
        } catch (Exception t) {
            RUNLOGGER.error("Failed to {}, {}.", behavior, t.getMessage());
            DEBLOGGER.error(MessageFormat.format("Failed to {0}", behavior), t);
            throw new CMPException(ErrorCode.DEPLOY_PROCESS_FAILED,
                SQLErrorParser.isDBException(t) ? "DB error: [" + t.getClass().getName() + "]" : t.getMessage());
        }
    }

    /**
     * 功能描述：定时任务处理
     *
     * @param processDefinitionKey process definition key
     * @param timerCron timer cron
     * @throws CMPException cmp exception
     * <AUTHOR>
     * @since 2020 -07-30
     */
    private void processTimerTask(String processDefinitionKey, String timerCron) throws CMPException {
        if (StringUtils.isNotEmpty(timerCron)) {
            startTimingProcess(processDefinitionKey, timerCron);
            return;
        }

        if (distributeTaskHelper.isExistTask(processDefinitionKey)) {
            stopTimingProcess(processDefinitionKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteProcess(String processDefinitionKey) throws CMPException {
        String behavior = MessageFormat.format("delete process: [{0}].", processDefinitionKey);
        String entry = MessageFormat.format("Begin to {0}.", behavior);
        INTLOGGER.info("deleteProcess request [{}]", entry);
        try {
            Assert.notNull(processDefinitionKey, "processDefinitionKey is null");

            if (distributeTaskHelper.isExistTask(processDefinitionKey)) {
                stopTimingProcess(processDefinitionKey);
            }

            Assert.notNull(processDefinitionKey, "processDefinitionKey is null.");
            List<ProcessDefinition> processDefinitions = ActivitiBeanProxy.getRepositoryService()
                .createProcessDefinitionQuery()
                .processDefinitionKey(processDefinitionKey)
                .list();
            if (CollectionUtils.isEmpty(processDefinitions)) {
                return true;
            }
            for (ProcessDefinition processDefinition : processDefinitions) {
                deploymentExtProvider.deleteProcess(processDefinition.getId());
                RUNLOGGER.info("Start invoke activiti:repositoryService:deleteDeployment processDefinition {},DeploymentId {}.",processDefinition.getId(),processDefinition.getDeploymentId());
                DeploymentQuery deploymentQuery = ActivitiBeanProxy.getRepositoryService()
                    .createDeploymentQuery()
                    .processDefinitionKey(processDefinition.getDeploymentId());
                if (deploymentQuery != null) {
                    ActivitiBeanProxy.getRepositoryService().deleteDeployment(processDefinition.getDeploymentId());
                }
                RUNLOGGER.info("end invoke activiti:repositoryService:deleteDeployment.");
            }

            // 删除历史扩展表
            historicProcDefExtProvider.delete(processDefinitions.stream().map(ProcessDefinition::getId).collect(
                Collectors.toList()));

            String exit = MessageFormat.format("Success to {0}.", behavior);
            INTLOGGER.info("deleteProcess response [{}]", exit);
            return true;
        } catch (Exception t) {
            RUNLOGGER.error("Failed to {}, {}.", behavior, t.getMessage());
            DEBLOGGER.error(MessageFormat.format("Failed to {0}", behavior), t);
            throw new CMPException(ErrorCode.DELETE_PROCESS_FAILED,
                SQLErrorParser.isDBException(t) ? "DB error: [" + t.getClass().getName() + "]" : t.getMessage());
        }
    }

    @Override
    public boolean suspendProcess(String processDefinitionKey) throws CMPException {
        String behavior = MessageFormat.format("suspend process: [{0}].", processDefinitionKey);
        String entry = MessageFormat.format("Begin to {0}.", behavior);
        INTLOGGER.info("suspendProcess", "ENTRY", "BEAN", "request [{}]", entry);
        INTLOGGER.info(ProcessConstants.Module.REPOSITORY, "ENTRY", StringUtils.EMPTY, entry);
        try {
            Assert.notNull(processDefinitionKey, "processDefinitionKey is null");

            RUNLOGGER.info("Start invoke activiti:repositoryService:suspendProcessDefinitionByKey.");
            ActivitiBeanProxy.getRepositoryService().suspendProcessDefinitionByKey(processDefinitionKey);
            RUNLOGGER.info("End   invoke activiti:repositoryService:suspendProcessDefinitionByKey.");
            String exit = MessageFormat.format("Success to {0}.", behavior);
            INTLOGGER.info("suspendProcess", "EXIT", "BEAN", "response [{}]", exit);
            return true;
        } catch (Exception t) {
            RUNLOGGER.error("Failed to {}, {}.", behavior, t.getMessage());
            DEBLOGGER.error(MessageFormat.format("Failed to {0}", behavior), t);
            throw new CMPException(ErrorCode.SUSPEND_ALL_PROCESS_FAILED,
                SQLErrorParser.isDBException(t) ? "DB error: [" + t.getClass().getName() + "]" : t.getMessage());
        }
    }

    @Override
    public boolean resumeProcess(String processDefinitionKey) throws CMPException {
        String behavior = MessageFormat.format("Resume process: [{0}].", processDefinitionKey);
        String entry = MessageFormat.format("Begin to {0}.", behavior);
        INTLOGGER.info("resumeProcess", "ENTRY", "BEAN", "request [{}]", entry);
        try {
            Assert.notNull(processDefinitionKey, "processDefinitionKey is null");

            RUNLOGGER.info("Start invoke activiti:repositoryService:activateProcessDefinitionByKey.");
            ActivitiBeanProxy.getRepositoryService().activateProcessDefinitionByKey(processDefinitionKey);
            RUNLOGGER.info("End   invoke activiti:repositoryService:activateProcessDefinitionByKey.");
            String exit = MessageFormat.format("Success to {0}.", behavior);
            INTLOGGER.info("resumeProcess", "EXIT", "BEAN", "response [{}]", exit);
            return true;
        } catch (Exception t) {
            RUNLOGGER.error("Failed to {}, {}.", behavior, t.getMessage());
            DEBLOGGER.error(MessageFormat.format("Failed to {0}", behavior), t);
            throw new CMPException(ErrorCode.RESUME_ALL_PROCESS_FAILED,
                SQLErrorParser.isDBException(t) ? "DB error: [" + t.getClass().getName() + "]" : t.getMessage());
        }
    }

    @Override
    public List<Process> queryProcessesByDefinitionKey(String processDefinitionKey) throws CMPException {
        String behavior = MessageFormat.format("Query process: [{0}].", processDefinitionKey);
        String entry = MessageFormat.format("Begin to {0}.", behavior);
        INTLOGGER.info("queryProcessesByDefinitionKey", "ENTRY", "BEAN", "request [{}]", entry);
        INTLOGGER.info(ProcessConstants.Module.REPOSITORY, "ENTRY", StringUtils.EMPTY, entry);
        try {
            Assert.notNull(processDefinitionKey, "processDefinitionKey is null");

            List<Process> processes = deploymentExtProvider.queryProcessesByDefinitionKey(processDefinitionKey);
            String exit = MessageFormat.format("Success to {0}.", behavior);
            INTLOGGER.info("queryProcessesByDefinitionKey", "EXIT", "BEAN", "response [{}]", exit);
            return processes;
        } catch (Exception t) {
            RUNLOGGER.error("Failed to {}, {}.", behavior, t.getMessage());
            DEBLOGGER.error(MessageFormat.format("Failed to {0}", behavior), t);
            throw new CMPException(ErrorCode.QUERY_PROCESS_FAILED,
                SQLErrorParser.isDBException(t) ? "DB error: [" + t.getClass().getName() + "]" : t.getMessage());
        }
    }

    @Override
    public boolean validateProcess(BpmnModel bpmnModel, boolean extend, String... validators) throws CMPException {
        try {
            extendValidate(bpmnModel, extend, validators);
            INTLOGGER.info("validateProcess", "EXIT", "BEAN", "response [{}]", true);
            return true;
        } catch (Exception t) {
            RUNLOGGER.error("Validate process Failed, {}.", t.getMessage());
            DEBLOGGER.error("Validate process Failed.", t);
            throw new CMPException(ErrorCode.VALIDATE_PROCESS_FAILED,
                SQLErrorParser.isDBException(t) ? "DB error: [" + t.getClass().getName() + "]" : t.getMessage());
        }
    }

    /**
     * Extend validate *
     * 
     * @param bpmnModel bpmn model
     * @param extend extend
     * @param validators validators
     * @throws CMPException cmp exception
     */
    private void extendValidate(BpmnModel bpmnModel, boolean extend, String... validators) throws CMPException {
        if (!extend) {
            return;
        }

        ProcessValidatorFactory processValidatorFactory = new ProcessValidatorFactory();
        ProcessValidator processValidator = processValidatorFactory.createDefaultProcessValidator();

        // 扩展校验器
        ValidatorSet extendValidatorSet = getExtendValidatorSet(validators);
        ((ProcessValidatorImpl) processValidator).addValidatorSet(extendValidatorSet);

        List<ValidationError> errors = processValidator.validate(bpmnModel);
        List<ValidationErr> validationErrs = ValidateErrConverter.convert(errors);
        if (CollectionUtils.isNotEmpty(validationErrs)) {
            throw new CMPException(ErrorCode.VALIDATE_PROCESS_FAILED, validationErrs.toString());
        }
    }

    /**
     * Gets extend validator set *
     * 
     * @param validators validators
     * @return the extend validator set
     */
    private ValidatorSet getExtendValidatorSet(String... validators) {
        String[] tmpValidators = validators;
        // 默认扩展校验器集
        if (0 >= ArrayUtils.getLength(tmpValidators)) {
            return ExtendValidatorSetFactory.createDefaultExtendValidatorSet();
        }

        // 自定义扩展校验器集
        ValidatorSet extendValidatorSet = new ValidatorSet("activiti-extend-process");
        for (String it : tmpValidators) {
            Validator validator = ExtendValidatorSetFactory.getExtendValidator(it);
            if (null != validator) {
                extendValidatorSet.addValidator(validator);
            }
        }
        return extendValidatorSet;
    }

    @Override
    public void startTimingProcess(String processDefinitionKey, String timerCron) throws CMPException {
        INTLOGGER.info("startTimingProcess", "ENTRY", "BEAN", "request [{}]", processDefinitionKey, timerCron);
        try {
            if (!distributeTaskHelper.isExistTask(processDefinitionKey)) {
                RUNLOGGER.warn("startTimingProcess distributeTask {} is not exist", processDefinitionKey);
                distributeTaskHelper.submitCronClusterTask(processDefinitionKey, "processAutoStartTrigger", timerCron);
            }
        } catch (TaskException e) {
            RUNLOGGER.error("Start timingProcess Failed, {}.", e.getMessage());
            DEBLOGGER.error("Start timingProcess Failed.", e);
            throw new CMPException(ErrorCode.TIMING_TRIGGER_FAILED,
                SQLErrorParser.isDBException(e) ? "DB error: [" + e.getClass().getName() + "]" : e.getMessage());
        }
        INTLOGGER.info("startTimingProcess", "EXIT", "BEAN", "request [{}]", processDefinitionKey, timerCron);
    }

    @Override
    public void stopTimingProcess(String processDefinitionKey) throws CMPException {
        INTLOGGER.info("stopTimingProcess|ENTRY|BEAN|request [{}]", processDefinitionKey);
        distributeTaskHelper.cancelCronTask(processDefinitionKey);
        INTLOGGER.info("stopTimingProcess|EXIT|BEAN|request [{}]", processDefinitionKey);
    }
}
