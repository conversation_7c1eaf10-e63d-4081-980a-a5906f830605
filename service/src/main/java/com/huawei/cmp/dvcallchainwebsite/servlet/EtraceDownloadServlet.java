/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2021. All rights reserved.
 */

package com.huawei.cmp.dvcallchainwebsite.servlet;

import com.huawei.bsp.biz.audit.ann.AuditResult;
import com.huawei.bsp.biz.audit.ann.LogSeverity;
import com.huawei.bsp.commonlib.hofs.HofsException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cmp.dvcallchainservice.model.ReferenceModel;
import com.huawei.cmp.dvcallchainwebsite.business.configimport.FileReader;
import com.huawei.cmp.dvcallchainwebsite.model.ContentType;
import com.huawei.cmp.dvcallchainwebsite.util.FileDownloadHelper;
import com.huawei.cmp.dvcallchainwebsite.util.I2kOperationLog;
import com.huawei.cmp.dvcallchainwebsite.util.RestUtils;
import com.huawei.cmp.foundation.container.download.model.ExportModel;
import com.huawei.cmp.foundation.hofs.DVHofsUtil;

import com.alibaba.fastjson2.JSON;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.Map;

import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * etrace日志下载
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2020-04-21 19:06
 */
public class EtraceDownloadServlet extends HttpServlet {
    private static final long serialVersionUID = 958334754632579575L;

    private static final OssLog LOG = OssLogFactory.getLog(EtraceDownloadServlet.class);

    private static final String CONTENTTYPE = "application/zip";

    private static final String DOWNLOAD_URL = "/rest/dvcallchainservice/v1/logdrillingservice/export";

    private static final String OPERATION_LOG_OPERATION_DOWNLOADETRACELOG =
        "com.huawei.cmp.dvcallchainwebsite.servlet.EtraceDownloadServlet";

    private static final String OPERATION_LOG_OPERATION_DOWNLOADETRACELOG_DETAIL =
        "com.huawei.cmp.dvcallchainwebsite.servlet.EtraceDownloadServlet.detail";

    private static final String OPERATION_LOG_SOURCE = "etraceDownloadServlet";

    @Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) {
        String params = readParam(req);
        Map<String, String> paramMap = JSON.parseObject(params, new ReferenceModel.MapReference());
        String path = paramMap.get("filePath").replace("opentrace", "trace");
        HttpContext context = new HttpContext(req, resp);
        String fileRemotePath = null;
        String etraceFileName = null;
        try {
            RestfulResponse restfulResponse = RestUtils.post(req, DOWNLOAD_URL, null, params);
            ExportModel exportModel = JSON.parseObject(restfulResponse.getResponseContent(), ExportModel.class);
            fileRemotePath = exportModel.getFilePath();
            etraceFileName = exportModel.getFileName();
        } catch (ServiceException e) {
            LOG.error("Failed to get the file path", e);
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_DOWNLOADETRACELOG, etraceFileName,
                LogSeverity.MINOR, OPERATION_LOG_SOURCE, AuditResult.FAILURE,
                OPERATION_LOG_OPERATION_DOWNLOADETRACELOG_DETAIL, new Object[]{etraceFileName, "FAIL"});
            return;
        }

        // 本地文件路径
        String filePath = DVHofsUtil.getLocalFileUrl(fileRemotePath);
        try {
            DVHofsUtil.pullFile(fileRemotePath, filePath);
            FileDownloadHelper fileDownloadHelper = new FileDownloadHelper(resp, filePath, ContentType.XLS);
            fileDownloadHelper.download(path.concat(".tlog"));
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_DOWNLOADETRACELOG, etraceFileName,
                LogSeverity.MINOR, OPERATION_LOG_SOURCE, AuditResult.SUCCESSFUL,
                OPERATION_LOG_OPERATION_DOWNLOADETRACELOG_DETAIL, new Object[]{etraceFileName, "SUCCESS"});
        } catch (HofsException e) {
            LOG.error("Failed to pull the file form remote");
            I2kOperationLog.info(context, OPERATION_LOG_OPERATION_DOWNLOADETRACELOG, etraceFileName,
                LogSeverity.MINOR, OPERATION_LOG_SOURCE, AuditResult.FAILURE,
                OPERATION_LOG_OPERATION_DOWNLOADETRACELOG_DETAIL, new Object[]{etraceFileName, "FAIL"});
        } finally {
            FileReader.delTempFiles(filePath, fileRemotePath);
        }
    }

    private static String readParam(HttpServletRequest request) {
        StringBuilder sb = new StringBuilder();

        try (BufferedReader br = request.getReader()) {
            String str;
            while ((str = br.readLine()) != null) {
                sb.append(str);
            }
        } catch (IOException e) {
            LOG.error("Failed to read request parameters as String.");
        }
        return sb.toString();
    }
}
