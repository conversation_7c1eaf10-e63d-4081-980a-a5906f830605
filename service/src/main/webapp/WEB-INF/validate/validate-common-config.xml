<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
  -->

<common-configs>
    <inputstream maxsize="2048000"/>
    <exclude-parameter>
        <json-exclude-parameter allParameterCheck="true"/>
    </exclude-parameter>
    <exclude-url allUrlCheck="true"
                 errorHandler="com.huawei.cmp.ihealing.validate.handler.ParameterValidateErrorHandler" stateCode="400">

        <!--website请求体单参数校验，改为代码校验 start-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingapiviewservice/deleteApi</url-pattern>

        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/queryAlarmInfoByTaskId</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/queryFlowInRunning</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/queryFlowTaskContent</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/queryHistoryDetailNodes</url-pattern>

        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryAllFlows</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryFlowNamesBySolution</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryFlowDetail</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryFlowDetailByDeployedId</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryFlowDetailIncludePassValue</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/queryAtomInsanceDetailByTaskId</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/stopFlow</url-pattern>

        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingglobalviewservice/deleteGlobal</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingglobalviewservice/queryGlobalDetails</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingglobalviewservice/updateFlowParas</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/copyImgFile</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/deleteImgFile</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/continueFlowTaskByTaskId</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/submitFlow</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryReviewedCount</url-pattern>

        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingipinfosviewservice/queryFlowHostDetail</url-pattern>
        <!--请求体单参数校验，改为代码校验 end-->
        <!--入参为空-->
        <!--无参数-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvauthenticateservice/getLoginUser</url-pattern>
        <!--无参数-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingapiviewservice/queryAllGroups</url-pattern>
        <!--无参数-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/getDeleteIntervalDays</url-pattern>
        <!--无参数-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingglobalviewservice/queryGroups</url-pattern>
        <!--无参数-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingpackageservice/queryPackageSolutions</url-pattern>
        <!--无参数-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvimporttryservice/tryImport</url-pattern>
        <!--无参数-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvuserservice/getLoginUser</url-pattern>
        <!--无参数-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryUploadFileConfig</url-pattern>
        <!--参数校验-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvcronservice/time</url-pattern>
        <!--导入导出-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvimportservice/import</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvflowviewservice/uploadImgFile</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvexportservice/zipdownload</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/flowfile/upload</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/flowfile/delete</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/flowfile/download</url-pattern>
        <!--注解校验接口-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingpackageservice/importPackage</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvatombasicqueryservice/queryTopMoTypes</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvatombasicqueryservice/queryAtoms</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvatombasicqueryservice/queryAtom</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvatominsqueryservice/queryAtomInstances</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvatominsqueryservice/queryAtomInsDetails</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvatominsqueryservice/queryHostInstances</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvatominsqueryservice/queryHostInsDetails</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvatominsqueryservice/queryHostExecuteDetails</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvatominstancequeryservice/queryAtomInstances</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvatominstancequeryservice/queryAtomInsDetails</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvatominstancequeryservice/queryHostInstances</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvatominstancequeryservice/queryHostInsDetails</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvatominstancequeryservice/queryHostExecuteDetails</url-pattern>
        <!--注解校验，但是代码中没有添加校验代码，已添加-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingapiviewservice/createApi</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingapiviewservice/modifyApi</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingapiviewservice/queryPageApis</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowconfirmservice/queryUserTasks</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowconfirmservice/confirmUserTask</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowconfirmservice/queryFailedHost</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowconfirmservice/queryInputParameter</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowconfirmservice/confirmFlowReview</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowconfirmservice/queryAllMoTypeTree</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowconfirmservice/queryDnByType</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowconfirmservice/queryDnTypeCollection</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingfloweventservice/queryflow</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingfloweventservice/queryFlowTimerEvent</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingfloweventservice/modifyFlowTimerEvent</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowdownloadservice/requestDownloadFlowFile</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowdownloadservice/queryDownloadFlowFileProgress</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowlogservice/queryHealingTaskLogs</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowsummaryservice/queryTopHealingFlows</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/create</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/query</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/queryTask</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/deleteFlowTaskByIds</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/queryAccurate</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/queryFlowNames</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/restoresFlow</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/modifyAndDeployFlow</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/modifyAndDeploy</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryTopMoTypesFlows</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryFlowSolution</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryTopMoTypes</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryEmail</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryEmailGroup</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryEmailGroups</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryFlowNames</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/createFlow</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/modifyFlow</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/deployFlow</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/undeployFlow</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/deleteFlow</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/importFlow</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/exportFlows</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryFlowByPage</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/updateFlowParas</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryCategories</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/isDvLite</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowzipservice/startBuildZipFile</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowzipservice/queryZipProgress</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowzipservice/downloadZip</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingglobalviewservice/queryRelationFlows</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingglobalviewservice/createGlobal</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingglobalviewservice/importGlobal</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingglobalviewservice/modifyGlobal</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingglobalviewservice/exportGlobals</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingglobalviewservice/queryPageGlobals</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingipinfosviewservice/queryIpInfos</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingpackageservice/confirmImportPackage</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingpackageservice/exportPackage</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingpackageservice/queryExportSolutions</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingpackageservice/queryPackage</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingpackageservice/importPackageSafety</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvimportservice/queryPackageFlowNames</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryMeasUnitTypeTree</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryDnListByMoTypeMeasUnitKey</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryPmTaskMoTypeTree</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/queryDnListByUserAndMoType</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/queryAccountByDn</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/deployAndExecuteTask</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingtaskinfoservice/queryTaskInfoByTaskIds</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingtaskinfoservice/queryTaskInfoByFlowIds</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingtaskinfoservice/saveOrUpdateTaskInfos</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingtaskinfoservice/deleteTaskInfoByTaskIds</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/deployTask</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/undeployTask</url-pattern>

        <!--无参数-->
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/queryFlowExcutedTime</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvflowviewservice/queryConfig</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowconfirmservice/querymotree</url-pattern>
        <url-pattern>/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/getNeTypeTree</url-pattern>
    </exclude-url>
</common-configs>
