<?xml version="1.0" encoding="UTF-8"?>

<param_validators>
    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/queryFields"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/queryCustomByIndexName"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/queryLog"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="fixedCondition.beginTime" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.endTime" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.count" required="false">
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.timeField" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.preZone" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternInfo.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternInfo.displayName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternInfo.dataSourceList" required="false" maxArraySize="1000">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="queryFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="operatorFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <wildcard>
            <parameter name="queryFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_LENGTH_VALIDATOR" maxLength="10000" errorMessage="WEB.VALIDATOR.RANGE" mark="extend"/>
            </parameter>
            <parameter name="operatorFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            </parameter>
        </wildcard>
        <parameter name="pageModel.pageIndex" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageModel.rowsCount" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="sortModel.sortField" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="sortModel.sortFieldType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="sortModel.orderType" required="false">
            <validator name="REGEXP_VALIDATOR" rule="asc|ASC|desc|DESC"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="exactSearch" required="false">
            <validator name="REGEXP_VALIDATOR" rule="true|false" caseSensitive="true"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="limitCount" required="false">
            <validator name="MAX_VALIDATOR" maxValue="100000" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="limitContent" required="false">
            <validator name="MAX_VALIDATOR" maxValue="1000" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="analysisQuery" required="false">
            <validator name="REGEXP_VALIDATOR" rule="true|false" caseSensitive="true"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/queryDrillingLog"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="time" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="moduleIp" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logPath" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fileKey" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
        </parameter>
        <parameter name="serialNo" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="32" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="drillCount" required="true">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternInfo.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternInfo.displayName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternInfo.dataSourceList" required="false" maxArraySize="1000">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/queryDrillingLogByTime"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="time" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="moduleIp" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logPath" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fileKey" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
        </parameter>
        <parameter name="serialNo" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="32" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="drillCount" required="true">
            <validator name="MAX_VALIDATOR" maxValue="1000" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/queryFieldEvents"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="fixedCondition.beginTime" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.endTime" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.count" required="false">
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.timeField" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.preZone" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternInfo.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternInfo.displayName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternInfo.dataSourceList" required="false" maxArraySize="1000">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="filterFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="operatorFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <wildcard>
            <parameter name="filterFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_LENGTH_VALIDATOR" maxLength="512" errorMessage="WEB.VALIDATOR.RANGE" mark="extend"/>
            </parameter>
            <parameter name="operatorFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            </parameter>
        </wildcard>
        <parameter name="aggregationField" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="aggregationFieldType" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="order" required="true">
            <validator name="REGEXP_VALIDATOR" rule="asc|ASC|desc|DESC"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/queryLogRest"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="fixedCondition.beginTime" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.endTime" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.count" required="true">
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.timeField" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.preZone" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="queryFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <wildcard>
            <parameter name="queryFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.RANGE" mark="extend"/>
            </parameter>
        </wildcard>
        <parameter name="restPageModel.from" required="true">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="restPageModel.size" required="true">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="sorts.sortField" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="sorts.orderType" required="true">
            <validator name="REGEXP_VALIDATOR" rule="asc|ASC|desc|DESC"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/addSolutionIdInQueryBuilder"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/queryIndexPatterns"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="queryFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <wildcard>
            <parameter name="queryFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_LENGTH_VALIDATOR" maxLength="64"  errorMessage="WEB.VALIDATOR.RANGE"/>
            </parameter>
        </wildcard>
        <parameter name="pageModel" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.EMPTY"/>
        </parameter>
        <parameter name="pageModel.pageIndex" required="false">
            <validator name="DIGITS_VALIDATOR" errorMessage="WEB.VALIDATOR.DIGITS"/>
            <validator name="MIN_VALIDATOR" minValue="1" errorMessage="WEB.VALIDATOR.MIN"/>
        </parameter>
        <parameter name="pageModel.rowsCount" required="false">
            <validator name="DIGITS_VALIDATOR" errorMessage="WEB.VALIDATOR.DIGITS"/>
            <validator name="MIN_VALIDATOR" minValue="10" errorMessage="WEB.VALIDATOR.MIN"/>
            <validator name="MAX_VALIDATOR" maxValue="100" errorMessage="WEB.VALIDATOR.MIN"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/queryGroupNameBySiteName"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="siteName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="groupName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/querySolutionIdsByGroupName"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="siteName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="groupName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/queryClusterIdsBySolutionId"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="siteName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="groupName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/addSearchIndexPattern"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.solutionType" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.displayName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.dataSourceList" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/querySearchIndexPattern"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="queryFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <wildcard>
            <parameter name="queryFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"  errorMessage="WEB.VALIDATOR.RANGE"/>
            </parameter>
        </wildcard>
        <parameter name="pageModel" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.EMPTY"/>
        </parameter>
        <parameter name="pageModel.pageIndex" required="false">
            <validator name="DIGITS_VALIDATOR" errorMessage="WEB.VALIDATOR.DIGITS"/>
            <validator name="MIN_VALIDATOR" minValue="1" errorMessage="WEB.VALIDATOR.MIN"/>
        </parameter>
        <parameter name="pageModel.rowsCount" required="false">
            <validator name="DIGITS_VALIDATOR" errorMessage="WEB.VALIDATOR.DIGITS"/>
            <validator name="MIN_VALIDATOR" minValue="10" errorMessage="WEB.VALIDATOR.MIN"/>
            <validator name="MAX_VALIDATOR" maxValue="100" errorMessage="WEB.VALIDATOR.MIN"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/deleteSearchIndexPattern"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.displayName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.dataSourceList" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/modifySearchIndexPattern"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.solutionType" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.displayName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.dataSourceList" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvlogmatrixservice/v1/dvlogretrievalqueryservice/queryAllMoInfos"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternInfo.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternInfo.displayName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternInfo.dataSourceList" required="false" maxArraySize="1000">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryFields"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryCustomByIndexName"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryLog"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="fixedCondition.beginTime" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.endTime" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.count" required="false">
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.timeField" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.preZone" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternInfo.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternInfo.displayName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternInfo.dataSourceList" required="false" maxArraySize="1000">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="queryFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="operatorFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <wildcard>
            <parameter name="queryFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_LENGTH_VALIDATOR" maxLength="2596"  errorMessage="WEB.VALIDATOR.RANGE"/>
            </parameter>
            <parameter name="operatorFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            </parameter>
        </wildcard>
        <parameter name="pageModel.pageIndex" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageModel.rowsCount" required="false">
            <validator name="MAX_VALIDATOR" maxValue="100" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="sortModel.sortField" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="sortModel.sortFieldType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="sortModel.orderType" required="false">
            <validator name="REGEXP_VALIDATOR" rule="asc|ASC|desc|DESC"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="exactSearch" required="false">
            <validator name="REGEXP_VALIDATOR" rule="true|false" caseSensitive="true"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>

        <parameter name="layerDrillConditions" required="false" maxArraySize="10">
        </parameter>

        <parameter name="layerDrillConditions.moduleIp" required="false"  maxArraySize="10">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>

        <parameter name="layerDrillConditions.logPath" required="false"  maxArraySize="10">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>

        <parameter name="layerDrillConditions.fileKey" required="false"  maxArraySize="10">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>

        <parameter name="layerDrillConditions.logName" required="false"  maxArraySize="10">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryLayerLog"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="fixedCondition.beginTime" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.endTime" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.count" required="false">
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.timeField" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.preZone" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="queryFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="operatorFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <wildcard>
            <parameter name="queryFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_LENGTH_VALIDATOR" maxLength="2596"  errorMessage="WEB.VALIDATOR.RANGE"/>
            </parameter>
            <parameter name="operatorFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            </parameter>
        </wildcard>
        <parameter name="pageModel.pageIndex" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageModel.rowsCount" required="false">
            <validator name="MAX_VALIDATOR" maxValue="200" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="exactSearch" required="false">
            <validator name="REGEXP_VALIDATOR" rule="true|false" caseSensitive="true"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pipeLine" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryDrillingLog"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="time" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="moduleIp" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logPath" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fileKey" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
        </parameter>
        <parameter name="serialNo" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="32" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="drillCount" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="forwardDrillCount" required="false">
            <validator name="MAX_VALIDATOR" maxValue="1000" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="MIN_VALIDATOR" minValue="0" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="backwardDrillCount" required="false">
            <validator name="MAX_VALIDATOR" maxValue="1000" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="MIN_VALIDATOR" minValue="0" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternInfo.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternInfo.displayName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternInfo.dataSourceList" required="false" maxArraySize="1000">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryDrillingLogByTime"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="time" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="moduleIp" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logPath" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fileKey" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
        </parameter>
        <parameter name="serialNo" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="32" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="drillCount" required="true">
            <validator name="MAX_VALIDATOR" maxValue="1000" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryFieldEvents"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="fixedCondition.beginTime" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.endTime" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.count" required="false">
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.timeField" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.preZone" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternInfo.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternInfo.displayName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fixedCondition.searchIndexPatternInfo.dataSourceList" required="false" maxArraySize="1000">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="filterFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="operatorFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <wildcard>
            <parameter name="filterFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_LENGTH_VALIDATOR" maxLength="512" errorMessage="WEB.VALIDATOR.RANGE" mark="extend"/>
            </parameter>
            <parameter name="operatorFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            </parameter>
        </wildcard>
        <parameter name="aggregationField" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="REGEXP_VALIDATOR" rule="[^#%&amp;&apos;+&lt;=&gt;?\\/]*"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="aggregationFieldType" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="order" required="true">
            <validator name="REGEXP_VALIDATOR" rule="asc|ASC|desc|DESC"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="layerDrillConditions" required="false" maxArraySize="10">
        </parameter>

        <parameter name="layerDrillConditions.moduleIp" required="false"  maxArraySize="10">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>

        <parameter name="layerDrillConditions.logPath" required="false"  maxArraySize="10">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>

        <parameter name="layerDrillConditions.fileKey" required="false"  maxArraySize="10">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>

        <parameter name="layerDrillConditions.logName" required="false"  maxArraySize="10">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryIndexPatterns"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="queryFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <wildcard>
            <parameter name="queryFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_LENGTH_VALIDATOR" maxLength="64"  errorMessage="WEB.VALIDATOR.RANGE"/>
            </parameter>
        </wildcard>
        <parameter name="pageModel" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.EMPTY"/>
        </parameter>
        <parameter name="pageModel.pageIndex" required="false">
            <validator name="DIGITS_VALIDATOR" errorMessage="WEB.VALIDATOR.DIGITS"/>
            <validator name="MIN_VALIDATOR" minValue="1" errorMessage="WEB.VALIDATOR.MIN"/>
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.MAX"/>
        </parameter>
        <parameter name="pageModel.rowsCount" required="false">
            <validator name="DIGITS_VALIDATOR" errorMessage="WEB.VALIDATOR.DIGITS"/>
            <validator name="MIN_VALIDATOR" minValue="10" errorMessage="WEB.VALIDATOR.MIN"/>
            <validator name="MAX_VALIDATOR" maxValue="100" errorMessage="WEB.VALIDATOR.MIN"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryGroupNameBySiteName"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="siteName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="groupName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/querySolutionIdsByGroupName"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="siteName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="groupName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryClusterIdsBySolutionId"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="siteName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="groupName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/addSearchIndexPattern"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.solutionType" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.displayName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.dataSourceList" required="true" maxArraySize="1000">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/querySearchIndexPattern"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="queryFields" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <wildcard>
            <parameter name="queryFields\.[a-z_A-Z0-9]*" required="false">
                <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"  errorMessage="WEB.VALIDATOR.RANGE"/>
            </parameter>
        </wildcard>
        <parameter name="pageModel" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.EMPTY"/>
        </parameter>
        <parameter name="pageModel.pageIndex" required="false">
            <validator name="DIGITS_VALIDATOR" errorMessage="WEB.VALIDATOR.DIGITS"/>
            <validator name="MIN_VALIDATOR" minValue="1" errorMessage="WEB.VALIDATOR.MIN"/>
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.MAX"/>
        </parameter>
        <parameter name="pageModel.rowsCount" required="false">
            <validator name="DIGITS_VALIDATOR" errorMessage="WEB.VALIDATOR.DIGITS"/>
            <validator name="MIN_VALIDATOR" minValue="10" errorMessage="WEB.VALIDATOR.MIN"/>
            <validator name="MAX_VALIDATOR" maxValue="100" errorMessage="WEB.VALIDATOR.MIN"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/deleteSearchIndexPattern"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.displayName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.dataSourceList" required="false" maxArraySize="1000">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/modifySearchIndexPattern"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.solutionType" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.displayName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexList.dataSourceList" required="true" maxArraySize="1000">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryAllMoInfos"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="indexName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternInfo.solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternInfo.displayName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="searchIndexPatternInfo.dataSourceList" required="false" maxArraySize="1000">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>
</param_validators>