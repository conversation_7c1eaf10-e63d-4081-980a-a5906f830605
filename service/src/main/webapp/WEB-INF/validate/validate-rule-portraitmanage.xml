<?xml version="1.0" encoding="UTF-8"?>

<param_validators>
    <param_validator url="/rest/dvanalysisengineservice/v1/portraitmanage/productportrait"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="name" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="type" required="true">
            <validator name="REGEXP_VALIDATOR" rule="1|2"/>
        </parameter>
        <parameter name="cpuNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1000000"/>
        </parameter>
        <parameter name="ramNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1000000"/>
        </parameter>
        <parameter name="updateTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="922337203685475807"/>
        </parameter>
        <parameter name="description" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="hostPortraitList" required="false" maxArraySize="50">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="hostPortraitList.id" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="hostPortraitList.name" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="hostPortraitList.hostType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="hostPortraitList.description" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="hostPortraitList.cpuNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1000"/>
        </parameter>
        <parameter name="hostPortraitList.ramSize" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1000"/>
        </parameter>
        <parameter name="hostPortraitList.deployServiceList" required="false" maxArraySize="10">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="hostPortraitList.baseHostNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1000"/>
        </parameter>
        <parameter name="hostPortraitList.minHostNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1000"/>
        </parameter>
        <parameter name="hostPortraitList.maxHostNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1000"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList" required="false" maxArraySize="5">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.indicatorId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.belongPortraitId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.measUnitKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.measUnitName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.measTypeKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.indexName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.moType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.displayValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="8000"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.originalValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.unit" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="100"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.checkedNetId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="2048"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.hasMeasObj" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="5"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.resourceTypeKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="250"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.indexId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1000"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.upperThreshold" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_VALIDATOR" minValue="0.0" maxValue="999999999999.99"/>
        </parameter>
        <parameter name="hostPortraitList.portraitIndicatorList.lowerThreshold" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="RANGE_VALIDATOR" minValue="0.0" maxValue="999999999999.99"/>
        </parameter>
        <parameter name="hostPortraitList.isAssociated" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="businessPortraitList" required="false" maxArraySize="50">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="businessPortraitList.id" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="businessPortraitList.portraitName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="businessPortraitList.businessName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="businessPortraitList.desc" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
        <parameter name="businessPortraitList.minCpuNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1000"/>
        </parameter>
        <parameter name="businessPortraitList.maxCpuNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1000"/>
        </parameter>
        <parameter name="businessPortraitList.minRamNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1000"/>
        </parameter>
        <parameter name="businessPortraitList.maxRamNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1000"/>
        </parameter>
        <parameter name="businessPortraitList.minReplicaNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1000"/>
        </parameter>
        <parameter name="businessPortraitList.maxReplicaNum" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="1000"/>
        </parameter>
        <parameter name="businessPortraitList.serviceDeployType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="100"/>
        </parameter>
        <parameter name="businessPortraitList.isAssociated" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="sharedIndicators" required="false" maxArraySize="5">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="sharedIndicators.displayValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="sharedIndicators.returnValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="isAssociated" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
        </parameter>
        <parameter name="ProductPortraitAssociatedTaskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="userId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024"/>
            <validator name="BLACK_VALIDATOR" rule="[#%&amp;+=|&gt;&lt;&apos;;?&quot;/\\()]"/>
        </parameter>
    </param_validator>
</param_validators>
