<?xml version="1.0" encoding="UTF-8"?>

<param_validators>
    <param_validator url="/rest/dvlogmatrixservice/v1/dvmatrixmetaconfigservice/importMetaConfigFile"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="fileName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="filePath" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fileContent" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>


    <param_validator url="/rest/dvlogmatrixservice/v1/dvmatrixmetaconfigservice/importMetaConfigFileWithSftp"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="fileName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="filePath" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fileContent" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>


    <param_validator url="/rest/dvlogmatrixservice/v1/dvmatrixmetaconfigservice/saveMetaConfig"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="fileName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="filePath" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="fileContent" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvmatrixmetaconfigservice/logTypeQuery"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="moType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="moTypeFlag" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>



    <param_validator url="/rest/dvlogmatrixservice/v1/dvmatrixmetaconfigservice/collectStop"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="motype" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="instanceIds" required="false" maxArraySize="2147483647">
            <validator name="REGEXP_VALIDATOR" rule="[^\n\r#%+|&lt;&gt;;?\/\\\\]*"
                       errorMessage="WEB.VALIDATOR.REGEXP" caseSensitive="true"/>
        </parameter>
        <parameter name="instanceFlag" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ip" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageNo" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageSize" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="isPageEnable" required="false">
            <validator name="REGEXP_VALIDATOR" rule="true|false"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageEnable" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvmatrixmetaconfigservice/collectStart"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="motype" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="instanceIds" required="false" maxArraySize="2147483647">
            <validator name="REGEXP_VALIDATOR" rule="[^\n\r#%+|&lt;&gt;;?\/\\\\]*"
                       errorMessage="WEB.VALIDATOR.REGEXP" caseSensitive="true"/>
        </parameter>
        <parameter name="instanceFlag" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ip" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageNo" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageSize" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="isPageEnable" required="false">
            <validator name="REGEXP_VALIDATOR" rule="true|false" caseSensitive="true"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageEnable" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>


    <param_validator url="/rest/dvlogmatrixservice/v1/dvmatrixmetaconfigservice/queryCollectStatus"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="motype" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="instanceIds" required="false" maxArraySize="2147483647">
            <validator name="REGEXP_VALIDATOR" rule="[^\n\r#%+|&lt;&gt;;?\/\\\\]*"
                       errorMessage="WEB.VALIDATOR.REGEXP" caseSensitive="true"/>
        </parameter>
        <parameter name="instanceFlag" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ip" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageNo" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageSize" required="false">
            <validator name="MAX_VALIDATOR" maxValue="200" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="isPageEnable" required="false">
            <validator name="REGEXP_VALIDATOR" rule="true|false"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageEnable" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvsftpconfigservice/addsftpconfig"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="name" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ip" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="port" required="true">
            <validator name="MAX_VALIDATOR" maxValue="65535" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="MIN_VALIDATOR" minValue="1" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="userName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="password" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="36" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="targetDir" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvsftpconfigservice/modifysftpconfig"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="name" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ip" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="port" required="true">
            <validator name="MAX_VALIDATOR" maxValue="65535" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="MIN_VALIDATOR" minValue="1" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="userName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="password" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="36" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="targetDir" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvlogmatrixservice/v1/dvsftpconfigservice/modifysftprelation"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="sftpId" required="true">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionId" required="true">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvlogmatrixservice/v1/dvsftpconfigservice/addsftprelation"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="sftpId" required="true">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionId" required="true">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvsftpconfigservice/testsftpconnection"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="name" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ip" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="port" required="true">
            <validator name="MAX_VALIDATOR" maxValue="65535" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="MIN_VALIDATOR" minValue="1" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="userName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="password" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="36" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="targetDir" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvsftpconfigservice/relatedSolutionBySftpName"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionId" required="true">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="sftpName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>


    <param_validator url="/rest/dvlogmatrixservice/v1/dvmatrixmetaconfigservice/queryClusterLogTypeSetting"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionId" required="true">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>

        <parameter name="offset" required="true">
            <validator name="DIGITS_VALIDATOR" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="MIN_VALIDATOR" minValue="0" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>

        <parameter name="limit" required="true">
            <validator name="DIGITS_VALIDATOR" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="100" errorMessage="WEB.VALIDATOR.RANGE"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixservice/v1/dvmatrixmetaconfigservice/updateClusterLogTypeSetting"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionId" required="true">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="clusterType" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.MAX_LENGTH"/>
        </parameter>
        <parameter name="clusterName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.MAX_LENGTH"/>
        </parameter>
        <parameter name="logType" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logLevel" required="false">
            <validator name="REGEXP_VALIDATOR" rule="INFO|WARN|DEBUG|ERROR|FATAL" caseSensitive="true"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logLevelField" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="512" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="matchRegex" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="512" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ttl" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="16" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="collectionEnable" required="false">
            <validator name="REGEXP_VALIDATOR" rule="OFF|ON" caseSensitive="true"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvmatrixmetaconfigservice/logTypeQuery"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="moType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="moTypeFlag" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>


    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvmatrixmetaconfigservice/collectStop"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="motype" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="instanceIds" required="false" maxArraySize="2147483647">
            <validator name="REGEXP_VALIDATOR" rule="[^\n\r#%+|&lt;&gt;;?\/\\\\]*"
                       errorMessage="WEB.VALIDATOR.REGEXP" caseSensitive="true"/>
        </parameter>
        <parameter name="instanceFlag" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ip" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageNo" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageSize" required="false">
            <validator name="MAX_VALIDATOR" minValue="0" maxValue="100" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="isPageEnable" required="false">
            <validator name="REGEXP_VALIDATOR" rule="true|false"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvmatrixmetaconfigservice/collectStart"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="motype" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="instanceIds" required="false" maxArraySize="2147483647">
            <validator name="REGEXP_VALIDATOR" rule="[^\n\r#%+|&lt;&gt;;?\/\\\\]*"
                       errorMessage="WEB.VALIDATOR.REGEXP" caseSensitive="true"/>
        </parameter>
        <parameter name="instanceFlag" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ip" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageNo" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageSize" required="false">
            <validator name="MAX_VALIDATOR" minValue="0" maxValue="100" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="isPageEnable" required="false">
            <validator name="REGEXP_VALIDATOR" rule="true|false" caseSensitive="true"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvmatrixmetaconfigservice/queryTopoByParentNode"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionMoType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.MAX_LENGTH"/>
        </parameter>
        <parameter name="solutionId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.MAX_LENGTH"/>
        </parameter>
        <parameter name="solutionInstanceId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.MAX_LENGTH"/>
        </parameter>
        <parameter name="clusterType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.MAX_LENGTH"/>
        </parameter>
        <parameter name="clusterVersion" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.MAX_LENGTH"/>
        </parameter>
        <parameter name="level" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1" errorMessage="WEB.VALIDATOR.MAX_LENGTH"/>
        </parameter>
        <parameter name="spaceIds" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.MAX_LENGTH"/>
        </parameter>
    </param_validator>


    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvmatrixmetaconfigservice/queryCollectStatus"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="motype" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="instanceIds" required="false" maxArraySize="2147483647">
            <validator name="REGEXP_VALIDATOR" rule="[^\n\r#%+|&lt;&gt;;?\/\\\\]*"
                       errorMessage="WEB.VALIDATOR.REGEXP" caseSensitive="true"/>
        </parameter>
        <parameter name="instanceFlag" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ip" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logType" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageNo" required="false">
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageSize" required="false">
            <validator name="MAX_VALIDATOR" minValue="0" maxValue="100" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="isPageEnable" required="false">
            <validator name="REGEXP_VALIDATOR" rule="true|false"
                       caseSensitive="true" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvsftpconfigservice/addsftpconfig"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="name" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ip" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="port" required="true">
            <validator name="MAX_VALIDATOR" maxValue="65535" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="MIN_VALIDATOR" minValue="1" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="userName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="password" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="36" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="targetDir" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvsftpconfigservice/deletesftpconfig"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="true">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="name" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ip" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="port" required="false">
            <validator name="MAX_VALIDATOR" maxValue="65535" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="MIN_VALIDATOR" minValue="1" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="userName" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="password" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="36" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="targetDir" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvsftpconfigservice/modifysftpconfig"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="name" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ip" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="port" required="true">
            <validator name="MAX_VALIDATOR" maxValue="65535" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="MIN_VALIDATOR" minValue="1" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="userName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="password" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="36" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="targetDir" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvsftpconfigservice/modifysftprelation"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="sftpId" required="true">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionId" required="true">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvsftpconfigservice/addsftprelation"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="sftpId" required="true">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="solutionId" required="true">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvsftpconfigservice/testsftpconnection"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="name" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ip" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="port" required="true">
            <validator name="MAX_VALIDATOR" maxValue="65535" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="MIN_VALIDATOR" minValue="1" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="userName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="password" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="36" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="targetDir" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM" mark="extend"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="tenantId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="status" required="false">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvsftpconfigservice/relatedSolutionBySftpName"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionId" required="true">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="sftpName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvmatrixmetaconfigservice/queryClusterLogTypeSetting"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionId" required="true">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>

        <parameter name="offset" required="true">
            <validator name="DIGITS_VALIDATOR" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="MIN_VALIDATOR" minValue="0" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="MAX_VALIDATOR" maxValue="2147483647" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>

        <parameter name="limit" required="true">
            <validator name="DIGITS_VALIDATOR" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="100" errorMessage="WEB.VALIDATOR.RANGE"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvlogmatrixwebsite/v1/dvmatrixmetaconfigservice/updateClusterLogTypeSetting"
                     errorHandler="com.huawei.cmp.logmatrix.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="solutionId" required="true">
            <validator name="RANGE_VALIDATOR" minValue="-2147483648" maxValue="2147483647"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="clusterType" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.MAX_LENGTH"/>
        </parameter>
        <parameter name="clusterName" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.MAX_LENGTH"/>
        </parameter>
        <parameter name="logType" required="true">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logLevel" required="false">
            <validator name="REGEXP_VALIDATOR" rule="INFO|WARN|DEBUG|ERROR|FATAL" caseSensitive="true"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="logLevelField" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="512" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="matchRegex" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="512" errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="ttl" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="16" errorMessage="WEB.VALIDATOR.PARAM"/>
            <validator name="USER_DEFINED_VALIDATOR"
                       checkClass="com.huawei.digitalview.commons.wsf.validators.IllegalCharacterValidator"
                       errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="collectionEnable" required="false">
            <validator name="REGEXP_VALIDATOR" rule="OFF|ON" caseSensitive="true"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="refreshInterval" required="false">
            <validator name="REGEXP_VALIDATOR" rule="10s|15s|30s|60s" caseSensitive="true"
                       errorMessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>
</param_validators>