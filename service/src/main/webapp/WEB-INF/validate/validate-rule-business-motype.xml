<?xml version="1.0" encoding="UTF-8"?>
<param_validators>
    <!--Pod详细信息展示-->
    <param_validator url="/rest/dvtopowebsite/v1/business/topo/motype/querypoddeploydata"
                     errorHandler="com.huawei.i2000.dvtoposervice.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="instanceId" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false"/>
        </parameter>
        <parameter name="endTime" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="922337203685475807"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvtoposervice/v1/business/topo/motype/querypoddeploydata"
                     errorHandler="com.huawei.i2000.dvtoposervice.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="instanceId" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false"/>
        </parameter>
        <parameter name="endTime" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="922337203685475807"/>
        </parameter>
    </param_validator>

    <!--vm详细信息展示-->
    <param_validator url="/rest/dvtopowebsite/v1/business/topo/motype/queryvmdeploydata"
                     errorHandler="com.huawei.i2000.dvtoposervice.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="instanceId" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false"/>
        </parameter>
        <parameter name="endTime" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="922337203685475807"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvtoposervice/v1/business/topo/motype/queryvmdeploydata"
                     errorHandler="com.huawei.i2000.dvtoposervice.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="instanceId" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false"/>
        </parameter>
        <parameter name="endTime" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="922337203685475807"/>
        </parameter>
    </param_validator>

    <!--pod指标详情-->
    <param_validator url="/rest/dvtopowebsite/v1/business/topo/motype/querypodindicator"
                     errorHandler="com.huawei.i2000.dvtoposervice.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="instanceId" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false"/>
        </parameter>
        <parameter name="endTime" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="922337203685475807"/>
        </parameter>
        <parameter name="stripeId" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="stripeUnit" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false"/>
        </parameter>
        <parameter name="newIndicatorListFlag" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="applicationType" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvtoposervice/v1/business/topo/motype/querypodindicator"
                     errorHandler="com.huawei.i2000.dvtoposervice.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="instanceId" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false"/>
        </parameter>
        <parameter name="endTime" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="922337203685475807"/>
        </parameter>
        <parameter name="stripeId" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="stripeUnit" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="1024" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false"/>
        </parameter>
        <parameter name="newIndicatorListFlag" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="applicationType" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
    </param_validator>

    <!--查询站点灰度-->
    <param_validator url="/rest/dvtopowebsite/v1/business/topo/motype/grayscale"
                     errorHandler="com.huawei.i2000.dvtoposervice.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="instanceId" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvtoposervice/v1/business/topo/motype/grayscale"
                     errorHandler="com.huawei.i2000.dvtoposervice.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="instanceId" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
    </param_validator>

    <!--查询灰度进度-->
    <param_validator url="/rest/dvtoposervice/v1/business/topo/motype/pipeline"
                     errorHandler="com.huawei.i2000.dvtoposervice.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="form" allParameterCheck="true">
        <parameter name="grayDisplayFlag" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
    </param_validator>
    <!--查询灰度进度-->
    <param_validator url="/rest/dvtopowebsite/v1/business/topo/motype/pipeline"
                     errorHandler="com.huawei.i2000.dvtopowebsite.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="form" allParameterCheck="true">
        <parameter name="grayDisplayFlag" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
    </param_validator>

    <!--vm指标详情-->
    <param_validator url="/rest/dvtopowebsite/v1/business/topo/motype/queryvmindicator"
                     errorHandler="com.huawei.i2000.dvtoposervice.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="instanceId" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false"/>
        </parameter>
        <parameter name="endTime" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="922337203685475807"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvtoposervice/v1/business/topo/motype/queryvmindicator"
                     errorHandler="com.huawei.i2000.dvtoposervice.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="instanceId" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorId" required="false">
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="EMPTY_VALIDATOR" empty="false"/>
        </parameter>
        <parameter name="endTime" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="922337203685475807"/>
        </parameter>
    </param_validator>

    <!--cluster详细信息展示-->
    <param_validator url="/rest/dvtopowebsite/v1/business/topo/motype/queryclusterdeploydata"
                     errorHandler="com.huawei.i2000.dvtoposervice.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="instanceId" required="true">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="2147483647"/>
        </parameter>
        <parameter name="timestamp" required="false">
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="1" maxValue="922337203685475807"/>
        </parameter>
    </param_validator>
</param_validators>
