#!/usr/bin/python3.11
# -*- coding: utf-8 -*-
# Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
from typing import Dict, Any

import networkx as nx
import logging

from utils.topo.topo_graph import TopoGraph

LOGGER = logging.getLogger('Kafka')


class TopoDnChange:
    def __init__(self, receive_input: Dict[str, Any]):
        self.receive_input = receive_input

    def add_dn(self, invoking_graph: nx.DiGraph, topo_mo: Dict[str, Any]) -> tuple[nx.DiGraph, Dict[str, Any]]:
        """网元变更：新增"""
        dn = self.receive_input["dn"]
        parent_dn = self.receive_input.get("parent")
        deploy_dn = self.receive_input["relationEvent"].get("srcNode")

        # 1. 增加网元父子/部署关系
        if dn not in invoking_graph.nodes():
            invoking_graph.add_node(dn)
        if parent_dn is not None and parent_dn not in invoking_graph.nodes():
            invoking_graph.add_node(parent_dn)
        invoking_graph.add_edge(parent_dn, dn)
        if deploy_dn is not None and deploy_dn not in invoking_graph.nodes():
            invoking_graph.add_node(deploy_dn)
        invoking_graph.add_edge(dn, deploy_dn)

        # 2. 增加网元详细信息
        if self.receive_input["layer"] == 2:
            self.receive_input["sourceMoType"] = self.receive_input.get("moType")
            self.receive_input["solutionId"] = self.receive_input.get("parentDn")
        elif self.receive_input["layer"] in [3, 4]:
            parent = topo_mo[self.receive_input["parentDn"]]
            self.receive_input["sourceMoType"] = parent.get("sourceMoType")
            self.receive_input["solutionId"] = parent.get("solutionId")
        topo_mo[dn] = self.receive_input
        return invoking_graph, topo_mo

    def modify_dn(self, invoking_graph: nx.DiGraph, topo_mo: Dict[str, Any]) -> tuple[nx.DiGraph, Dict[str, Any]]:
        """网元变更：修改"""
        modify_dn = self.receive_input["dn"]
        if modify_dn not in invoking_graph.nodes():
            invoking_graph.remove_node(modify_dn)

        topo_mo.pop(modify_dn, None)
        return self.add_dn(invoking_graph, topo_mo)

    def delete_dn(self, invoking_graph: nx.DiGraph, topo_mo: Dict[str, Any]) -> tuple[nx.DiGraph, Dict[str, Any]]:
        """网元变更：删除"""
        dn = self.receive_input["dn"]
        if dn not in invoking_graph.nodes():
            invoking_graph.remove_node(dn)

        topo_mo.pop(dn, None)
        return invoking_graph, topo_mo

    @staticmethod
    def update_all_dn(solution_name) -> tuple[nx.DiGraph, Dict[str, Any]]:
        """网元变更：全量更新"""
        topo_graph = TopoGraph(solution_name)
        return topo_graph.invoking_graph, topo_graph.topo_mo
