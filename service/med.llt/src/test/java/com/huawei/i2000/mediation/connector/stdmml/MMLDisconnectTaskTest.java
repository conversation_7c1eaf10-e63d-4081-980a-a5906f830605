/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.mediation.connector.stdmml;

import com.huawei.i2000.mediation.sdk.Listener;
import com.huawei.i2000.mediation.sdk.Param;
import com.huawei.i2000.mediation.sdk.Request;
import com.huawei.i2000.mediation.sdk.Response;
import com.huawei.i2000.mediation.sdk.Target;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MMLDisconnectTaskTest {

    @Mock
    private Request mockRequest;

    @Mock
    private Listener<Response> mockListener;

    @Mock
    private MMLConnector mockAdapter;

    private MMLDisconnectTask mmlDisconnectTaskUnderTest;

    @Before
    public void setUp() throws Exception {
        mockRequest=new Request();
        mockRequest.addParam(new Param("c","String","3"));
        mockRequest.addParam(new Param("d","String","4"));
        Target target=new Target();
        target.setAddress("localhost");
        mockRequest.setTarget(target);
        mmlDisconnectTaskUnderTest = new MMLDisconnectTask(mockRequest, mockListener, mockAdapter);
    }

    @Test
    public void testExec() {
        mmlDisconnectTaskUnderTest.exec();
    }
}
