/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

package com.huawei.cmp.flume.source.scanner;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cmp.flume.source.config.ConfigProvider;
import com.huawei.cmp.flume.source.config.FileFilterRule;
import com.huawei.cmp.flume.source.config.TraceLogMeta;
import com.huawei.cmp.flume.source.event.DefaultEventReader;
import com.huawei.cmp.flume.source.event.EventReader;
import com.huawei.cmp.flume.source.file.FileStatus;
import com.huawei.cmp.flume.source.model.LogMissingStatus;
import com.huawei.cmp.flume.source.snap.LogMissingRecord;
import com.huawei.cmp.flume.source.snap.SnapManager;
import com.huawei.cmp.flume.source.util.DateUtil;
import com.huawei.cmp.flume.source.util.SourceConstants;
import com.huawei.cmp.flume.source.util.SourceUtil;
import com.huawei.cmp.foundation.log.channel.core.Event;
import com.huawei.cmp.foundation.log.channel.core.channel.ChannelProcessor;
import com.huawei.cmp.foundation.log.channel.exception.LogChannelException;
import com.huawei.cmp.foundation.log.channel.monitor.SourceCounter;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Vector;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;

/**
 * 日志采集线程，扫描每个日志文件，逻辑行读取，然后通过flume的Event发送
 *
 * <AUTHOR>
 * @since 2015年4月16日
 */
public class CollectTask implements Runnable {
    private static final OssLog DEBUG_LOGGER = OssLogFactory.getLogger(CollectTask.class);

    private static final long BACKOFF_SLEEP_INCREMENT = 1000L;

    private static final long MAX_BACKOFF_SLEEP = 5000L;

    private static final Logger log = LoggerFactory.getLogger(CollectTask.class);

    private SourceCounter sourceCounter;

    private ChannelProcessor channelProcessor;

    private SnapManager snapManager;

    private TraceLogMeta traceLogMeta;

    private ConfigProvider configProvider;

    private List<FileStatus> moduleRecords;

    private Set<String> fileFirstTimeScanedSet;

    private Map<String, Vector<FileStatus>> records;

    private CountDownLatch latch;

    private AtomicLong backoffs = new AtomicLong(0L);

    private long collectLossAlarmThreshold;

    /**
     * Constructor
     *
     * @param sourceCounter sourceCounter
     * @param channelProcessor channelProcessor
     * @param snapManager snapManager
     * @param traceLogMeta traceLogMeta
     * @param configProvider configProvider
     * @param moduleRecords moduleRecords
     * @param records records
     * @param latch latch
     * @param fileFirstTimeScanedSet fileFirstTimeScanedSet
     * @param collectLossAlarmThreshold collectLossAlarmThreshold
     */
    public CollectTask(SourceCounter sourceCounter, ChannelProcessor channelProcessor, SnapManager snapManager,
        TraceLogMeta traceLogMeta, ConfigProvider configProvider, Vector<FileStatus> moduleRecords,
        Map<String, Vector<FileStatus>> records, CountDownLatch latch, Set<String> fileFirstTimeScanedSet,
        long collectLossAlarmThreshold) {
        this.sourceCounter = sourceCounter;
        this.channelProcessor = channelProcessor;
        this.snapManager = snapManager;
        this.traceLogMeta = traceLogMeta;
        this.configProvider = configProvider;
        this.moduleRecords = moduleRecords;
        this.records = records;
        this.latch = latch;
        if (null == fileFirstTimeScanedSet) {
            fileFirstTimeScanedSet = new HashSet<>();
        }
        this.fileFirstTimeScanedSet = fileFirstTimeScanedSet;
        this.collectLossAlarmThreshold = collectLossAlarmThreshold;
    }

    @Override
    public void run() {
        try {
            collect();
        } catch (Exception e) {
            DEBUG_LOGGER.error("Failed to collect data of module: ({}). ", traceLogMeta.toString(), e);
        } finally {
            latch.countDown();
            DateUtil.clearThreadLocalDatePattern();
        }
    }

    private void collect() throws InterruptedException {
        String dir = traceLogMeta.getTraceLogDir();

        Set<String> blackLogDirs = traceLogMeta.getBlackLogDir();
        List<File> blackLogDirFiles = getBlackListFiles(blackLogDirs);

        if (StringUtils.isBlank(dir)) {
            DEBUG_LOGGER.warn("Failed to collect data, log dir is blank.");
            return;
        }

        DEBUG_LOGGER.debug("Begin to scan dir: {}, module name is: {}", dir, traceLogMeta.getModuleName());

        File dirFile = new File(dir);

        if (checkDir(dirFile, dir)) {
            // 更新采集进度的记录
            updateRecords(dirFile);
            scanDirectory(dirFile, blackLogDirFiles, configProvider.getMaxDirDepth());
        } else if (!dirFile.exists()) {
            DEBUG_LOGGER.warn("Dir: {} does not exits", dir);
        } else if (!dirFile.canExecute() || !dirFile.canRead()) {
            DEBUG_LOGGER.warn("Do not have execute or read permission for dir: {}", dirFile.toString());
        } else {
            DEBUG_LOGGER.warn("Dir: {} path is not canonical.", dir);
        }

        DEBUG_LOGGER.debug("End to scan dir: {}", dir);
    }

    private List<File> getBlackListFiles(Set<String> blackLogDirs) {
        List<File> blackLogDirFiles = new ArrayList<>();
        if (blackLogDirs != null) {
            for (String blackDir : blackLogDirs) {
                File blackDirFile = new File(blackDir);
                if (checkDir(blackDirFile, blackDir)) {
                    blackLogDirFiles.add(blackDirFile);
                }
            }
        }
        return blackLogDirFiles;
    }

    private void collectExistingFileStatus(File dirFile, Set<FileStatus> existingStatus) {
        if (SourceConstants.TRACE_LINK_DIR.equals(dirFile.getName())) {
            return;
        }

        File[] files = dirFile.listFiles();
        if (null == files) {
            DEBUG_LOGGER.warn("Can not open dir: {}, please check the permission.", dirFile.toString());
            return;
        }

        for (File subFile : files) {
            if (subFile.isDirectory()) {
                collectExistingFileStatus(subFile, existingStatus);
            } else if (subFile.isFile() && validate(subFile.getName())) {
                String key = SourceUtil.getFileKey(subFile);
                existingStatus.add(
                    new FileStatus(key, traceLogMeta.getTraceLogDir(), SourceUtil.getCanonicalPath(subFile),
                        subFile.length(), subFile.length(), subFile.getName()));
            }
        }
    }

    private void updateRecords(File dirFile) {
        DEBUG_LOGGER.debug("before updateRecords {}, the moduleRecords size is: {}", dirFile, moduleRecords.size());

        Set<FileStatus> existingStatus = new HashSet<>();
        // 获取当前存在的文件的fileKey
        collectExistingFileStatus(dirFile, existingStatus);

        // 不采集旧日志场景下，第一次启动或snap文件删除掉重启需要初始化所有文件状态
        if (!configProvider.collectOldLog() && moduleRecords.isEmpty()) {
            DEBUG_LOGGER.debug("collectOldLog is {}, the existingStatus size is: {}",
                Boolean.toString(configProvider.collectOldLog()), existingStatus.size());
            for (FileStatus status : existingStatus) {
                // 如果是首次扫描该文件，则走处理是否采集旧日志的逻辑，并且增加到首次扫描文件名列表中，并保存当前快照位置
                if (!fileFirstTimeScanedSet.contains(status.getFileName())) {
                    DEBUG_LOGGER.debug("reload moduleRecords, it's first time to scan the FileName: {},"
                        + "will not process the old logs", status.getFileName());
                    moduleRecords.add(status);
                    fileFirstTimeScanedSet.add(status.getFileName());

                    // 第一次启动已批量刷新snap，否则每次实时刷新
                    if (!snapManager.firstStart()) {
                        snapManager.saveSnap(status);
                    }
                }
            }
            return;
        }

        List<FileStatus> records2Del = new ArrayList<>();
        for (FileStatus fileStatus : moduleRecords) {
            // 如果fileKey不存在则要删除该条记录
            if (!existingStatus.contains(fileStatus)) {
                DEBUG_LOGGER.debug("remove fileStatus: {}", fileStatus.toString());
                records2Del.add(fileStatus);
            }
        }

        if (!records2Del.isEmpty()) {
            moduleRecords.removeAll(records2Del);
            snapManager.removeSnap(records2Del);
            recordAbnormalStatus(records2Del);
        }

        DEBUG_LOGGER.debug("after updateRecords {}, the moduleRecords size is: {}", dirFile, moduleRecords.size());
    }

    private boolean checkDir(File dirFile, String dir) {
        boolean isCanonical = dirFile.exists() && dirFile.canExecute() && dirFile.canRead();
        String canonicalPath = null;
        try {
            canonicalPath = dirFile.getCanonicalPath();
        } catch (IOException e) {
            isCanonical = false;
            DEBUG_LOGGER.error("Get file path failed.");
        }
        if (!dir.equals(canonicalPath)) {
            DEBUG_LOGGER.error("The configured dir is not canonical {}", canonicalPath);
            isCanonical = false;
        }
        return isCanonical;
    }

    private void scanDirectory(File dirFile, List<File> blackLogDirs, int dirDepth) throws InterruptedException {
        if (dirDepth < 0) {
            DEBUG_LOGGER.warn("Reach maximum depth of dir, unable to scan deeper.");
            return;
        }
        // 过滤掉老的采集方式留下的残余目录
        if (SourceConstants.TRACE_LINK_DIR.equals(dirFile.getName())) {
            return;
        }

        boolean firstStart = snapManager.firstStart();
        File[] sortedFiles = getSortedFiles(dirFile, blackLogDirs);

        for (File value : sortedFiles) {
            if (value.isDirectory()) {
                scanDirectory(value, blackLogDirs, dirDepth - 1);
            } else if (value.isFile()) {
                scanFile(value, firstStart);
            }
        }

        // 第一次启动批量更新采集位置快照，否则，为保证实时性，每采集一个文件更新一次快照
        if (firstStart) {
            DEBUG_LOGGER.debug("Save snap in batch when first start.");
            snapManager.saveSnaps(moduleRecords);
        }
    }

    private static File[] getSortedFiles(File dirFile, List<File> blackLogDirFiles) {
        if (blackLogDirFiles.contains(dirFile)) {
            DEBUG_LOGGER.warn("Directories blacklist contains root Directory {}");
            return new File[] {};
        }
        File[] files = dirFile.listFiles();
        if (null == files) {
            DEBUG_LOGGER.error("Cannot open dir: {}, please check the permission.", dirFile.toString());
            return new File[] {};
        }

        List<File> unsortedFiles = Arrays.asList(files);
        // 去掉目录黑名单中配置的目录
        Stream<File> stream = unsortedFiles.stream();
        Stream<File> fileStream = stream.filter(file -> !blackLogDirFiles.contains(file));
        files = fileStream.toArray(File[]::new);
        int size = files.length;
        // 去除非可读的文件
        files = Arrays.stream(files).filter(File::canRead).toArray(File[]::new);
        if (size - files.length > 0) {
            DEBUG_LOGGER.warn("There is {} unreadable file in dir: {}", size - files.length, dirFile.toString());
        }

        // 按修改时间排序，保证先采集比较旧的数据
        Arrays.sort(files, (file1, file2) -> {
            long lastModified1 = file1.lastModified();
            long lastModified2 = file2.lastModified();
            if (lastModified1 == lastModified2) {
                return 0;
            } else {
                return (lastModified1 > lastModified2) ? 1 : -1;
            }
        });

        return files;
    }

    private void scanFile(File file, boolean firstStart) throws InterruptedException {
        if (validate(file.getName())) {
            String fileKey = SourceUtil.getFileKey(file);
            String filePath = SourceUtil.getCanonicalPath(file);
            // 立即保存文件的长度，防止文件名变化后，长度不准确
            long length = file.length();
            FileStatus matchedStaus = null;

            for (FileStatus fileStatus : moduleRecords) {
                if (StringUtils.equals(fileKey, fileStatus.getFileKey())) {
                    matchedStaus = fileStatus;
                    break;
                }
            }

            DEBUG_LOGGER.debug("whether matchedStaus is not null : {}", Boolean.toString(matchedStaus != null));
            if (matchedStaus != null) {
                processMatchedFile(filePath, matchedStaus, length);
            } else if (fileMetaChanged(filePath, fileKey, length)) {
                DEBUG_LOGGER.info("File Meta has been changed,  {}", traceLogMeta);
            } else {
                processNewFile(filePath, fileKey, firstStart);
            }
        }
    }

    private synchronized boolean fileMetaChanged(String filePath, String fileKey, long length) throws InterruptedException {
        String key = null;
        FileStatus updateStatus = null;
        boolean metaHasChanged = false;
        // 当前的Meta变化，对应的hashcode发生变化，但是采集的文件不变，需要更新全局记录中的key值，使得文件的位置能正确记录
        for (Map.Entry<String, Vector<FileStatus>> entry : records.entrySet()) {
            for (FileStatus status : entry.getValue()) {
                if (status.getFileCanonicalPath().equals(filePath) && status.getFileKey().equals(fileKey)) {
                    key = entry.getKey();
                    updateStatus = status;
                    metaHasChanged = true;
                    break;
                }
            }
        }

        DEBUG_LOGGER.debug("whether metaHasChanged : {}", Boolean.toString(metaHasChanged));
        if (metaHasChanged) {
            List<FileStatus> fileStatuses = records.get(key);
            Iterator<FileStatus> iterator = fileStatuses.iterator();
            while (iterator.hasNext()) {
                if (iterator.next().getFileKey().equals(key)) {
                    iterator.remove();
                }
            }
            String updateMetaKey = String.valueOf(traceLogMeta.getTraceLogDir());
            updateStatus.setMetaKey(updateMetaKey);
            moduleRecords.add(updateStatus);
            DEBUG_LOGGER.debug("The metaKey has been changed from {} to {}", key, updateMetaKey);
            processMatchedFile(filePath, updateStatus, length);
        }
        return metaHasChanged;
    }

    private void processNewFile(String filePath, String fileKey, boolean firstStart) throws InterruptedException {
        DEBUG_LOGGER.debug("ProcessNewFile file:{} fileKey:{} ", filePath, fileKey);
        // 从头采集
        FileStatus status = new FileStatus(fileKey, String.valueOf(traceLogMeta.getTraceLogDir()), filePath, 0, 0, null);
        moduleRecords.add(status);
        doProcess(status);
        // 第一次启动已批量刷新snap
        if (!firstStart) {
            snapManager.saveSnap(status);
        }
    }

    private void processMatchedFile(String filePath, FileStatus matchedStatus, long matchedFileLength)
        throws InterruptedException {
        // 找到上次采集的记录，继续从上次的位置向后采集
        DEBUG_LOGGER.debug("MatchedStatus file:{} fileKey:{} position:{} matchedFileLength:{} ", filePath,
            matchedStatus.getFileKey(), matchedStatus.getPosition(), matchedFileLength);
        // fileKey不变，但是文件可能已经被改名，需要刷新
        matchedStatus.setFileCanonicalPath(filePath);
        // 如果长度没变，则没必要向下处理
        boolean needProcess = matchedFileLength != matchedStatus.getPosition();
        boolean forceUpdate = !StringUtils.equals(filePath, matchedStatus.getFileCanonicalPath());
        DEBUG_LOGGER.debug("whether needProcess : {}", Boolean.toString(needProcess));
        if (needProcess) {
            doProcess(matchedStatus);
        }
        if (forceUpdate || needProcess) {
            snapManager.updateSnap(matchedStatus);
        }
    }

    private void doProcess(FileStatus status) throws InterruptedException {
        EventReader reader = new DefaultEventReader(status, traceLogMeta, configProvider);
        boolean keepOnRead = true;
        int fileThrottle = configProvider.getFileThrottle();
        int batchSize = Math.min(configProvider.getBatchSize(), fileThrottle);
        int collectedEvents = 0;
        String fileName = SourceUtil.getFileName(status.getFileCanonicalPath());
        long oldPos = status.getPosition();
        try {
            reader.open();
            while (keepOnRead) {
                List<Event> events = reader.readEvents(batchSize);
                if (!events.isEmpty()) {
                    DEBUG_LOGGER.debug("Send events from {} , size is {}, position {}, fileLength {}.", fileName,
                        String.valueOf(events.size()), status.getPosition(), status.getFileLength());
                    sourceCounter.addToEventReceivedCount(events.size());
                    sourceCounter.incrementAppendBatchReceivedCount();
                    processEventBatchUntilSuccess(events);
                    reader.commit();
                    sourceCounter.addToEventAcceptedCount(events.size());
                    sourceCounter.incrementAppendBatchAcceptedCount();
                    if (status.getPosition() <= oldPos) {
                        DEBUG_LOGGER.warn("error position, old position {}, position {}.", oldPos,
                            status.getPosition());
                        DEBUG_LOGGER.warn("file status is {}", status.toString());
                    }
                } else {
                    reader.commit();
                    break;
                }
                collectedEvents += events.size();
                // 控制采集线程得采集速率
                if (collectedEvents >= fileThrottle) {
                    TimeUnit.MILLISECONDS.sleep(1000);
                }
                keepOnRead = fileThrottle == -1 || collectedEvents < fileThrottle;
            }
            // 记录采集日志条数
            if (collectedEvents > 0) {
                DEBUG_LOGGER.info("process {} events during this batch from file {}", collectedEvents, fileName);
            }
        } catch (IOException e) {
            DEBUG_LOGGER.error("Failed to collect log ({}), caused by ", status.getFileCanonicalPath(), e);
        } finally {
            reader.close();
        }
    }

    private void processEventBatchUntilSuccess(List<Event> events) throws InterruptedException {
        while (true) {
            try {
                channelProcessor.processEvents(events);
                backoffs.set(0);
                break;
            } catch (LogChannelException e) {
                long count = backoffs.get();
                printWarnLog(e, count);
                // 每次等待时间递增，最大等待5s
                Thread.sleep(Math.min(backoffs.incrementAndGet() * BACKOFF_SLEEP_INCREMENT, MAX_BACKOFF_SLEEP));
            }
        }
    }

    private void printWarnLog(LogChannelException exception, long count) {
        if (count < 10) {
            DEBUG_LOGGER.warn("Process event failed {} times, caused by {}", count, exception);
        } else if (count % 10 == 0) { // 10次以上抑制日志打印次数。
            DEBUG_LOGGER.warn("Process event failed {} times, caused by {}", count * 10, exception);
        }
    }

    private boolean validate(String fileName) {
        Set<FileFilterRule> filterRules = traceLogMeta.getFileFilterRule();
        boolean isValid = false;
        for (FileFilterRule filterRule : filterRules) {
            String logRegex = filterRule.getLogRegex();
            String blackLogRegex = filterRule.getExcludeFileNameRegex();
            boolean tempMatches = fileName.matches(logRegex) && !fileName.matches(blackLogRegex);
            isValid = tempMatches || isValid;
        }
        return isValid;
    }

    private void recordAbnormalStatus(List<FileStatus> records2Del) {
        List<LogMissingStatus> logMissingStatuses = new ArrayList<>();
        long currentTimeMillis = System.currentTimeMillis();
        for (FileStatus status : records2Del) {
            long collectDifference = status.getFileLength() - status.getPosition();
            if (collectDifference > collectLossAlarmThreshold) {
                DEBUG_LOGGER.warn("Log collect missing, FilePath: {}, position: {}, fileLength: {}",
                    status.getFileCanonicalPath(), status.getPosition(), status.getFileLength());
                logMissingStatuses.add(new LogMissingStatus(configProvider.getSourceName(), status.getMetaKey(),
                    status.getFileCanonicalPath(), currentTimeMillis));
            } else if (collectDifference > 0) {
                DEBUG_LOGGER.warn("Incomplete collection, FilePath: {}, position: {}, fileLength: {}",
                    status.getFileCanonicalPath(), status.getPosition(), status.getFileLength());
            }
        }
        if (CollectionUtils.isNotEmpty(logMissingStatuses)) {
            LogMissingRecord.getInstance().addStatus(logMissingStatuses);
        }
    }
}
