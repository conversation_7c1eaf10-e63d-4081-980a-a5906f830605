#!/bin/bash
set +x
CURRENT_PATH=$(cd $(dirname $0); pwd)
PreSet_PATH=$(cd ${CURRENT_PATH}/..; pwd)

. ${PreSet_PATH}/Common/utils_common.sh ${PreSet_PATH}/Common
test $? != 0 && exit 1
. ${PreSet_PATH}/Common/utils_uniep.sh ${PreSet_PATH}/Common
test $? != 0 && exit 1
. ${PreSet_PATH}/Common/utils_os.sh ${PreSet_PATH}/Common
test $? != 0 && exit 1

##modify
function Gen_ProductZip()
{
    . ${PreSet_PATH}/Common/passwords.properties
    log_echo "INFO" "Begin to Gen_ProductZip of AllInOneNetwork."
    
    json_source_path="${PreSet_PATH}/Product_AllInOneNetwork/Product_template"
    json_tmp_path=${PreSet_PATH}/network_O
    
    if [ -d ${json_tmp_path} ];then
        rm -rf ${json_tmp_path:?}
    fi
    mkdir -p ${json_tmp_path}
    
    if [ "X${IPType}" == "X0" ];then
        IPType="ipv4"
    elif [ "X${IPType}" == "X1" ];then
        IPType="ipv6"
    elif [ "X${IPType}" == "X2" ];then
        IPType="ipv4-6"
    else
        log_echo "INFO" "The IPType is invalid."
    fi
    
    #nodes_sop.json
    if [ -f ${json_source_path}/nodes_sop_${IPType}.json ];then
        cp -rpf ${json_source_path}/nodes_sop_${IPType}.json ${json_tmp_path}/nodes_sop.json
        replace_file_macro ${json_tmp_path}/nodes_sop.json ${source_file}
        replace_file_macro ${json_tmp_path}/nodes_sop.json ${PreSet_PATH}/Common/DV_config.properties.tmp
        
        ossadm_pwd_encrypt=$(cat ${PreSet_PATH}/Common/pwfile.json | sed 's/}/\n/g' | grep -w ossadm | awk -F : '{print $3}')
        ossadm_pwd_encrypt=${ossadm_pwd_encrypt#*\"}
        ossadm_pwd_encrypt=${ossadm_pwd_encrypt%%\"*}
        decrypt_password ${ossadm_pwd_encrypt}
        ossadm_pwd_decrypt=${decryptPasswd}
        encrypt_by_uniep "${ossadm_pwd_decrypt}" ""
        ossadm_pwd_encrypt="${pwd_return}"
        if [ "X${ossadm_pwd_encrypt}" == "X" ];then
            log_echo "ERROR" "Get ossadm_userpasswd failed"
            exit 1
        else
            sed -i "s|\"pvalue\": \".*\"|\"pvalue\": \"${ossadm_pwd_encrypt}\"|g" ${json_tmp_path}/nodes_sop.json
        fi
    fi
    
    #product_sop.json
    if [ -f ${json_source_path}/product_sop_${IPType}.json ];then
        cp -rpf ${json_source_path}/product_sop_${IPType}.json ${json_tmp_path}/product_sop.json
        replace_file_macro ${json_tmp_path}/product_sop.json ${source_file}
        replace_file_macro ${json_tmp_path}/product_sop.json ${PreSet_PATH}/Common/passwords.properties
        
        sed -i "s#{{PM_DATA_DB_SIZE}}#${PM_DATA_DB_SIZE}#g" ${json_tmp_path}/product_sop.json
        sed -i "s/{{enableLuceneQueryForHistoryAlarm}}/true/g" ${json_tmp_path}/product_sop.json
        
        sed -i "s#{{DV_SHARE_PATH}}#${INSTALL_PATH}/share/SOP/DVEngineeringService/dvshare#g" ${json_tmp_path}/product_sop.json
        CMP_KERBEROS_HOSTNAMES=$(hostname)
        sed -i "s#{{CMP_KERBEROS_HOSTNAMES}}#${CMP_KERBEROS_HOSTNAMES}#g" ${json_tmp_path}/product_sop.json
        
        if [ "X${ossadm_pwd_encrypt}" == "X" ];then
            log_echo "ERROR" "Get ossadm_userpasswd failed"
            exit 1
        else
            sed -i "s|{{GLOBAL_UNIFIED_OSS_USER_PASSWORD}}|${ossadm_pwd_encrypt}|g" ${json_tmp_path}/product_sop.json
        fi
        
        encrypt_by_uniep "${db_user_pwd}" ""
        db_dbapasswd_encrypt="${pwd_return}"
        if [ "X${db_dbapasswd_encrypt}" == "X" ];then
            log_echo "ERROR" "Get db_dbapasswd_encrypt failed"
            exit 1
        else
            sed -i "s|{{GLOBAL_UNIFIED_OSS_DB_USER_PASSWORD}}|${db_dbapasswd_encrypt}|g" ${json_tmp_path}/product_sop.json
        fi
        
        if [ "X${web_admin_pwd}" == "X" ];then
            log_echo "ERROR" "Get web_admin_pwd failed"
            exit 1
        else
            sed -i "s|{{web_admin_user_value}}|${web_admin_pwd}|g" ${json_tmp_path}/product_sop.json
        fi
        
        set_cmp_key_for_product_json "${json_tmp_path}/product_sop.json"

        if [ "${dv_lite_mode}" == "TRUE" ];then
            sed -i "s#{{featureGroup}}#merge_lite#g" ${json_tmp_path}/product_sop.json
            sed -i "s#{{zenith_paramgroup_file}}#medium#g" ${json_tmp_path}/product_sop.json
            if [ -f /.dockerenv ];then
                sed -i "s#{{featureGroupDisplayList}}#merge_lite;merge_docker#g" ${json_tmp_path}/product_sop.json
            else
                sed -i "s#{{featureGroupDisplayList}}#merge_lite;merge#g" ${json_tmp_path}/product_sop.json
            fi
        elif [ "${dv_deploy_scale_size}" == "small" ];then
            sed -i "s#{{featureGroup}}#merge_mini#g" ${json_tmp_path}/product_sop.json
            sed -i "s#\"IS_INSTALL_ICNFG\":\ \{0,\}\"[^\"]*#\"IS_INSTALL_ICNFG\":\"No#g" ${json_tmp_path}/product_sop.json
            sed -i "s#{{zenith_paramgroup_file}}#medium#g" ${json_tmp_path}/product_sop.json
            if [ -f /.dockerenv ];then
                sed -i "s#{{featureGroupDisplayList}}#merge_mini;merge_docker#g" ${json_tmp_path}/product_sop.json
            else
                sed -i "s#{{featureGroupDisplayList}}#merge_mini;merge#g" ${json_tmp_path}/product_sop.json
            fi
        elif [ -f /.dockerenv ];then
            sed -i "s#{{featureGroup}}#merge_docker#g" ${json_tmp_path}/product_sop.json
            sed -i "s#{{featureGroupDisplayList}}#merge;merge_tool;merge_docker#g" ${json_tmp_path}/product_sop.json
            sed -i "s#{{zenith_paramgroup_file}}#large#g" ${json_tmp_path}/product_sop.json
        else
            sed -i "s#{{featureGroup}}#merge#g" ${json_tmp_path}/product_sop.json
            sed -i "s#{{featureGroupDisplayList}}#merge;merge_tool;merge_docker#g" ${json_tmp_path}/product_sop.json
            sed -i "s#{{zenith_paramgroup_file}}#large#g" ${json_tmp_path}/product_sop.json
        fi

        if [ "${dv_lite_mode}" == "TRUE" ];then
            sed -i "s#\,\\\\\"DVPM\\\\\"##g" ${json_tmp_path}/product_sop.json
            sed -i "s#;DVPM#;NBIService#g" ${json_tmp_path}/product_sop.json
            sed -i "s#\"IS_INSTALL_ICNFG\":.*#\"IS_INSTALL_ICNFG\":\"No\",#g" ${json_tmp_path}/product_sop.json
            sed -i "s#\"dv_deploy_scale_size\":\ \{0,\}\"[^\"]*#\"dv_deploy_scale_size\":\"small#g" ${json_tmp_path}/product_sop.json
        fi
        if [ "`echo ${is_integrated_df_and_service}|tr 'a-z' 'A-Z'`" == "YES" ];then
            chown ossadm:ossgroup -R ${json_tmp_path}
            touch ${PreSet_PATH}/tools/handlejson.log
            chown ossadm:ossgroup ${PreSet_PATH}/tools/handlejson.log
            su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python ${PreSet_PATH}/tools/handlejson.py \"productext_modify\" \"${json_tmp_path}/product_sop.json\" \"integrated_mode\" \"DigitalFoundry-DigitalView-service co-deployment\""
            if [ $? -ne 0 ];then
                log_echo "ERROR" "su - ossadm -c \"source /opt/oss/manager/bin/engr_profile.sh;python ${PreSet_PATH}/tools/handlejson.py \"productext_modify\" \"${json_tmp_path}/product_sop.json\" \"integrated_mode\" \"DigitalFoundry-DigitalView-service co-deployment\"\" failed."
                exit 1
            fi
        fi
        
        local enableNetworkElementLargeCapacity="false"
        if [ "X${dv_deploy_scale_size}" == "Xsmall" ];then
            enableNetworkElementLargeCapacity="true"
        fi
        sed -i "s#{{enableNetworkElementLargeCapacity}}#${enableNetworkElementLargeCapacity}#g" ${json_tmp_path}/product_sop.json

        if [ "X${dv_lite_mode}" != "XTRUE" ];then
            add_PM_features
        fi
        sed -i "s#{{enablePMServiceMode}}#${enablePMServiceMode}#"  ${json_tmp_path}/product_sop.json
        sed -i "s#{{enableNBIServiceMode}}#${enableNBIServiceMode}#"  ${json_tmp_path}/product_sop.json
        sed -i "s#{{VS_PATH}}#${VS_PATH}#"  ${json_tmp_path}/product_sop.json
        sed -i "s#{{KAFKA_PATH}}#${KAFKA_PATH}#"  ${json_tmp_path}/product_sop.json
        sed -i "s#{{OPT_CUSTOM_PATH}},{{HOME_CUSTOM_PATH}},{{TMP_CUSTOM_PATH}}#${custom_path_list}#g" ${json_tmp_path}/product_sop.json

        if [ "X${dv_lite_mode}" != "XFALSE" -o "X${dv_deploy_scale_size}" != "Xdefault" ]; then
          log_echo "INFO" "The env is small or scale. The DVITManagement does not need to be deployed."
        else
          add_DVITManagement_feature
        fi

        if [ -f /.dockerenv ];then
            log_echo "In the large container scenario, EnableLimitCpu must be set to false."
            sed -i "s#{{EnableLimitCpu}}#false#" ${json_tmp_path}/product_sop.json
        else
            log_echo "non-large container scenario,EnableLimitCpu is true"
            sed -i "s#{{EnableLimitCpu}}#true#" ${json_tmp_path}/product_sop.json
        fi

        replace_file_macro ${json_tmp_path}/product_sop.json  ${PreSet_PATH}/Common/DV_config.properties.tmp
        ${PreSet_PATH}/UniEp/python/bin/python  ${PreSet_PATH}/tools/handlejson.py "productext_modify" "${json_tmp_path}/product_sop.json" "NFV_MODE" "${NFV_MODE}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "${PreSet_PATH}/UniEp/python/bin/python  ${PreSet_PATH}/tools/handlejson.py productext_modify ${json_tmp_path}/product_sop.json NFV_MODE \"${NFV_MODE}\" failed."
            exit 1
        fi
    fi
    if [ "${IPType}" != "ipv4" ];then
        cp -rpf ${json_source_path}/networkinfo_om.json ${json_tmp_path}/networkinfo_sop.json
        sed -i "s/{{OM_Node_IPV6_NETMASK}}/${ALL_IN_ONE_IPV6_NETMASK}/g" ${json_tmp_path}/networkinfo_sop.json
        sed -i "s/{{OM_Node_IPV6_IP}}/${ALL_IN_ONE_IPV6}/g" ${json_tmp_path}/networkinfo_sop.json
        sed -i "s/{{OM_Node_IPV6_NIC}}/${ALL_IN_ONE_IPV6_NIC}/g" ${json_tmp_path}/networkinfo_sop.json
        sed -i "s/{{OM_Node_MGR_IPV6_IP}}/${ALL_IN_ONE_MGR_IPV6}/g" ${json_tmp_path}/networkinfo_sop.json
    fi
    rm -f ${PreSet_PATH}/Product_AllInOneNetwork.zip
    zip -j ${PreSet_PATH}/Product_AllInOneNetwork.zip ${json_tmp_path}/*.json
    rm -rf ${json_tmp_path:?}
    chown ossadm: ${PreSet_PATH}/zenith_paramgroup_file.properties
}

function main()
{
    init_pwd_encrypt
    
    Gen_ProductZip
    
    cleanup
    
    rollback_unset_tmout
    
    log_echo "INFO" "Preset finish."
    
    ## auto import product info.

    Check_execute "import_product_information"
    if [ $? -ne 0 ];then
        import_product_information "${PreSet_PATH}" || exit 1
        echo "import_product_information : success" >> ${action_tag}
    fi
    log_echo "INFO" "import_product_information successfully."

    if [ "X${IPType}" == "Xipv4" ];then
        update_keystorepwd_i2k "${ALL_IN_ONE_IPV4}"
    else
        update_keystorepwd_i2k "${ALL_IN_ONE_IPV6}"
    fi
    
    ## auto scan upload software package.
    if [ "X${AUTO_SCAN_UPLOAD_SOFTWARE_PACKAGE}" == "XTRUE" -a "X${SOFTWARE_PACKAGE_PATH}" != "X" ];then
        Check_execute "scanning_package"
        if [ $? -ne 0 ];then
            scanning_package || exit 1
            echo "scanning_package : success" >> ${action_tag}
        fi
        log_echo "INFO" "scanning_package successfully."
    fi
    
    backExtDiskInfo
}

main
