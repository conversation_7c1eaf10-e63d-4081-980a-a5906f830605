#!/bin/bash
CURRENT_PATH=$(cd $(dirname $0); pwd)

if [ ! -f ${LOG_FILE} ];then
    touch ${LOG_FILE}
    chown ossadm:ossgroup ${LOG_FILE}
fi

if [ ! -f ${configParametersFile} ];then
    touch ${configParametersFile}
    chown ossadm:ossgroup ${configParametersFile}
fi

function check_user_group()
{
    local dir_path="$1"
    [ -z "${dir_path}" ] && return 1
    [ ! -d ${dir_path} ] && return 1

    local user_group=$(stat -c "%U:%G" ${dir_path})
    if [ "X${user_group}" == "Xroot:root" ];then
        log_echo "INFO" "The user_group=${user_group} is root."
        return 0
    fi

    log_echo "INFO" "The user_group=${user_group} not root."
    return 1
}

function check_user_permission()
{
    check_user_group "/home/<USER>"
    if [ $? -eq 0 ];then
        chown -R ossuser:ossgroup /home/<USER>
        chown -R root:root /home/<USER>/sudoScripts
    fi

    check_user_group "/home/<USER>"
    if [ $? -eq 0 ];then
        chown -R devdata:ossgroup /home/<USER>
        find /home/<USER>
        chown root:sftponly /home/<USER>
        chown ossuser:ossgroup /home/<USER>/sftp/necert/iCnfgPlatform.cer
        chown ossuser:ossgroup /home/<USER>/sftp/necert/iCnfgPlatformRoot.crt
    fi

    check_user_group "/home/<USER>"
    if [ $? -eq 0 ];then
        find /home/<USER>
        chown root:sftponly /home/<USER>
        chown root:ossgroup /home/<USER>/tmp
    fi

    check_user_group "/home/<USER>"
    if [ $? -eq 0 ];then
        find /home/<USER>
    fi
}

function check_user_ssh()
{
    local user_name="$1"
    if [ -z "${user_name}" ];then
        log_echo "INFO" "The user_name=${user_name} is null.of ${FUNCNAME}"
        return 0
    fi

    local user_home=$(cat /etc/passwd|grep "^${user_name}:"|awk -F':' '{print $6}')
    if [ -z "${user_home}" ];then
        log_echo "INFO" "The user_home=${user_home} is null.of ${FUNCNAME}"
        return 0
    fi

    if [ ! -d ${user_home}/.ssh ];then
        log_echo "INFO" "The ${user_home}/.ssh is not exists.of ${FUNCNAME}"
        return 0
    fi

    local dir_access=$(stat -c %A ${user_home}/.ssh)
    local dir_user=$(stat -c %U ${user_home}/.ssh)
    if [ "${dir_user}" != "${user_name}" ];then
        log_echo "ERROR" "The ${user_home}/.ssh dir_user=${dir_user} is not equals ${user_name}.of ${FUNCNAME}"
        exit 1
    fi

    if [ "${dir_access}" == "drwxrwxrwx" ];then
        log_echo "ERROR" "The ${user_home}/.ssh dir_access=${dir_access}.The permission is too large.of ${FUNCNAME}"
        exit 1
    fi

    if [ "$(echo ${dir_access} |sed 's/.*\(.\{3\}\)$/\1/')" == "rwx" ];then
        log_echo "ERROR" "The ${user_home}/.ssh dir_access=${dir_access}.The other user permission is rwx,is too large.of ${FUNCNAME}"
        exit 1
    fi
    log_echo "INFO" "The user_name=${user_name} check finished.of ${FUNCNAME}"
}

function check_default_gw()
{
    mgr_nic=$(su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;cat /opt/oss/manager/etc/sysconf/nodelists.json |python -c \"import sys;import json;manifest=json.loads(sys.stdin.read());print([ip['iface']  for ip in  [i['IPAddresses'] for i in  manifest['nodeList'].values()][0]  if 'maintenance' in ip['usage'] ][0])\"")

    if [ -d /etc/sysconfig/network-scripts ];then
        if [ -f /etc/sysconfig/network-scripts/ifcfg-${mgr_nic} ];then
            cat /etc/sysconfig/network-scripts/ifcfg-${mgr_nic}|grep -i IPV6_DEFAULTGW
            if [ $? -eq 0 ];then
                log_echo "ERROR" "There is IPV6_DEFAULTGW in /etc/sysconfig/network-scripts/ifcfg-${mgr_nic}, please delete it by referring to the document."
                return 1
            fi
        fi
    fi
     
    default_gw=$(ip -6 route show default|awk '{print $3}'|grep -v dummy |uniq|wc -l)
    if [ ${default_gw} -gt 1 ];then
        log_echo "ERROR" "There are two IPv6 default routes , please delete one by referring to the document. For details about how to handle the error, see section FAQ in the upgrade guide."
        return 1
    fi
    return 0
}

function pre_haveged()
{
    if [ -f "/.dockerenv" ];then
        log_echo "INFO" "Containerized scene installation ,no need to config haveged ."
        return 0
    fi

    ps -ef |grep "haveged" |grep -v grep >/dev/null 2>&1
    if [ $? -ne 0 ];then
        service haveged start
        if [ $? -ne 0 ];then
            die "Start service haveged failed, please check"
        fi
    fi

    chkconfig haveged | grep disabled >/dev/null 2>&1
    if [ $? -ne 0 ];then
        chkconfig haveged on
        if [ $? -ne 0 ];then
            die "chkconfig haveged on failed, please check"
        fi
    fi

    if [ -f /proc/sys/kernel/random/read_wakeup_threshold ];then
        entropy_avail=$(cat /proc/sys/kernel/random/entropy_avail)
        read_wakeup_threshold=$(cat /proc/sys/kernel/random/read_wakeup_threshold)
        log_echo "INFO" "entropy_avail=${entropy_avail} read_wakeup_threshold=${read_wakeup_threshold}."
        
        rd_ret=0
        isNum "${entropy_avail}" || rd_ret=1
        isNum "${read_wakeup_threshold}" || rd_ret=1
        
        if [ ${rd_ret} -eq 0 -a ${entropy_avail} -lt ${read_wakeup_threshold} ];then
            /usr/sbin/haveged -w 1024 -v 1 --Foreground &
        fi
    fi

    return 0
}

function upgrade_env_enhancement()
{
    chmod 755 /opt
    chmod 750 /opt/oss
    chmod 755 /usr/lib
    chmod 755 /usr/lib64
    chmod 755 /usr/share
    chmod 1777 /dev/shm
    chmod 644 /etc/ssh/sshd_config
    chown root:root /root
    chmod 1777 /var/tmp
    chmod 1777 /tmp
    chmod 750  /opt/pub
    chown root:ossgroup /opt/pub
    chown root:root /opt/signtool
    chmod 700 /opt/signtool
    chown ossadm:ossgroup /tmp/oss
    chown ossadm:ossgroup -R /usr/local/osconfig

    if [ -d /opt/oss/log ];then
        chmod 750 /opt/oss/log
        if [ -d "/home/<USER>" ];then
            chown ossadm:ossgroup /opt/oss /opt/oss/log
        fi  
    fi
    if [ -e /opt/oss/uds/nodeagent/nodeagent.sock ];then
        if [ "$(stat -c "%a" /opt/oss/uds/nodeagent/nodeagent.sock)" != "660" ];then
            chmod 660 /opt/oss/uds/nodeagent/nodeagent.sock
        fi
    fi

    if [ -e /opt/oss/uds/nodeagent/configdb.new ];then
        if [ "$(stat -c "%a" /opt/oss/uds/nodeagent/configdb.new)" != "660" ];then
            chmod 660 /opt/oss/uds/nodeagent/configdb.new
        fi
    fi

    if [ -d /opt/zenith/ssl ];then
        chmod 700 /opt/zenith/ssl
        chmod 600 /opt/zenith/ssl/*
    fi
    [ -d /opt/zenith ] && chown dbuser:dbgroup -R /opt/zenith
    if [ -f /root/.gnupg/secring.gpg ];then
        log_echo "INFO" "The /root/.gnupg/secring.gpg  is exists. del it."
        rm -rf /root/.gnupg/secring.gpg
    fi

    setfacl_crl
    
    check_tmp_oss

    pre_haveged
    systemctl start rsyslog
}

function check_sudo_config()
{
    cat /etc/sudoers|grep -e "^[\s]*Defaults\s*secure_path"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "/etc/sudoers'config does not contain 'Defaults secure_path',please check"
        return 1
    fi
    cat /etc/sudoers|grep -e "^[\s]*Defaults\s*use_pty"
    if [ $? -eq 0 ];then
        log_echo "ERROR" "/etc/sudoers'config contain 'Defaults use_pty',please check"
        return 1
    fi
    return 0
}

function check_immutable_file()
{
    local ret=0
    local need_check_path="/etc/sudoers /etc/passwd /etc/shadow"
    for tmp_path in ${need_check_path};do
        lsattr ${tmp_path}|grep "^.\{4\}i"
        if [ $? -eq 0 ];then
            log_echo "ERROR" "${tmp_path} is immutable,please check by 'lsattr ${tmp_path}'"
            ret=1
        fi
    done
    return ${ret}
}

function check_drbd_status()
{
   if [ -d "/opt/oss/SOP/apps/DVEngineeringService/bin/" ];then
      ls /dev/drbd0 > /dev/null 2>&1
      if [ $? -eq 0 ];then
          log_echo "INFO" "Begin to check drbd disk status..."
          drbd_disk=$(grep "disk" /usr/local/drbd/etc/drbd.d/dvRes0.res |grep "/"|awk -F' ' '{print $2}'|sed 's/ //g'|sed 's/;//g'|uniq)
          ls ${drbd_disk} > /dev/null 2>&1
          if [ $? -ne 0 ];then
              log_echo "ERROR" "Get drbd disk failed from /usr/local/drbd/etc/drbd.d/dvRes0.res, please check"
              exit 1
          fi

          dumpe2fs -h ${drbd_disk} | grep "Filesystem state"| grep "error" >> $LOG_FILE 2>&1
          if [ $? -eq 0 ];then
              log_echo "ERROR" "The Filesystem state of drbd disk has error, you should fix it, otherwise it may lead to failure when upgrade product. Before fix it, you should do product data backup first."
              log_echo "WARN" "You should stop app DVEngineeringService with ossadm and stop drbd service with 'systemctl stop drbd.service' first, then fix the drbd disk with operating system command 'fsck'. Maybe you need ask IT for help when using the command 'fsck'."
              log_echo "WARN" "After fixing the disk, you should start drbd service with 'systemctl start drbd.service' and then do product data restore in case of files lost after the commond 'fsck'."
              exit 1
          fi
          drbd_status=$(drbdadm  status |grep "UpToDate" |wc -l)
          if [ "X${drbd_status}" != "X2" ];then
              log_echo "ERROR" "The drbd status is not UpToDate,please run the drbdadm status to check"
              exit 1
          fi
      fi
   fi
}

function upgrade_check_app()
{

    . ${dv_cfg_file}_root.tmp

    upgrade_env_enhancement
    
    check_dirs_io "${check_io_appnode}"
    
    check_user_permission

    check_user_ssh "ossadm"
    check_user_ssh "ossuser"
    check_user_ssh "devdata"


    check_user_pwd_expires "ossadm,dbuser,${i2k_user_name},${icnfg_devdata_user_name},${system_login_user},${system_sftp_user},${dvshare_user_name},${sop_user_name},${system_operation_user}" || exit 1

    if [ -d "${install_path}/share/SOP/DVEngineeringService/I2000/run" ];then
        find ${install_path}/share/SOP/DVEngineeringService/I2000/run -not -user ${i2k_user_name} -exec chown -R ${i2k_user_name}:${i2k_group_name} {} +
    fi

    ## check /opt/oss/share/SOP/DVEngineeringService root file or dir
    for dir in "I2000" "icnfg" "etc" "cie" ;do
        if [ -d ${install_path}/share/SOP/DVEngineeringService/$dir ];then
            local check_ret=$(find ${install_path}/share/SOP/DVEngineeringService/$dir -user root)
            if [ ! -z "${check_ret}" ];then
                log_echo "INFO" "upgrade_check check has root dir or files.check_ret=${check_ret}"
                for dir_file in ${check_ret};do
                    chown ${i2k_user_name}:${i2k_group_name} ${dir_file}
                done
            fi
        fi
    done

    check_drbd_status

    check_sudo_config || check_ret=1
    check_immutable_file || check_ret=1

    local ipv6_flag=$(cat /opt/oss/manager/etc/sysconf/nodelists.json |grep "\"IP\"" |awk -F'"IP"[ ]*:' '{print $2}' | grep ":" | wc -l)
    if [ ${ipv6_flag} -gt 0 ]; then
        log_echo "The IP contains IPV6, the check_default_gw will be executed"
        check_default_gw || check_ret=1
    else
        log_echo "The IP type only contains IPV4, the check_default_gw will be skipped"
    fi

    [ "${check_ret}" == "1" ] && exit 1
}

function setfacl_crl()
{
    if [ -d "/etc/pki/tls/certs" ];then
        setfacl -m g:ossgroup:rx /etc/pki/tls/certs
        setfacl -m g:ossgroup:r /etc/pki/tls/certs/HuaweiRootCAsipCRLs.crl
        setfacl -m g:ossgroup:r /etc/pki/tls/certs/HuaweiRootCAsipCRLs_Release.crl
        setfacl -m m::rwx /etc/pki/tls/certs
        setfacl -m m::rwx /etc/pki/tls/certs/HuaweiRootCAsipCRLs.crl
        setfacl -m m::rwx /etc/pki/tls/certs/HuaweiRootCAsipCRLs_Release.crl
        chmod 640 /etc/pki/tls/certs/HuaweiRootCAsipCRLs.crl /etc/pki/tls/certs/HuaweiRootCAsipCRLs_Release.crl
    fi

    if [ -d "/var/lib/ca-certificates/pem" ];then
        setfacl -m g:ossgroup:rx /var/lib/ca-certificates/pem
        setfacl -m g:ossgroup:r /var/lib/ca-certificates/pem/HuaweiRootCAsipCRLs.crl
        setfacl -m g:ossgroup:r /var/lib/ca-certificates/pem/HuaweiRootCAsipCRLs_Release.crl
        setfacl -m m::rwx /var/lib/ca-certificates/pem
        setfacl -m m::rwx /var/lib/ca-certificates/pem/HuaweiRootCAsipCRLs.crl
        setfacl -m m::rwx /var/lib/ca-certificates/pem/HuaweiRootCAsipCRLs_Release.crl
        chmod 640 /var/lib/ca-certificates/pem/HuaweiRootCAsipCRLs.crl /var/lib/ca-certificates/pem/HuaweiRootCAsipCRLs_Release.crl
    fi
}

function upgrade_check_fstab()
{
    log_echo "INFO" "upgrade_check_fstab start."
    local has_nofail=$(cat -n /etc/fstab |grep -w "DV_Preset\|DV_ATAEextend"|grep -vw "nofail" |awk '{print $1}')
    if [ -z "${has_nofail}" ];then
        log_echo "INFO" "The /etc/fstab of DV_Preset or DV_ATAEextend already has nofail ,of ${FUNCNAME}"
        return 0
    fi

    for line in ${has_nofail};do
        sed -i "${line}s/defaults/nofail,defaults/g" /etc/fstab
    done

    log_echo "INFO" "upgrade_check_fstab end."
}

function upgrade_kube_firewall_iptables()
{
    log_echo "INFO" "upgrade_kube_firewall_iptables start..."
    iptables-save |grep "cloudsop_lvs_31360_" >> ${LOG_FILE} 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "execute cmd: [ iptables-save |grep cloudsop_lvs_31360_ ],select not has it.skip it."
        return 0
    fi

    ## -A PREROUTING -d xx.xx.xx.x/xx -p tcp -m tcp --dport 31360 -m comment --comment cloudsop_lvs_31360_169795392 -j MARK --set-xmark 0xa1edf40/0xffffffff
    local mark_str=$(iptables-save |grep "cloudsop_lvs_31360_" |head -1|awk '{print $NF}'|sed "s# ##g")
    add_and_del_iptables "add" "${mark_str}"

    log_echo "INFO" "upgrade_kube_firewall_iptables End."
}

function copy_i2k_user_home_file_root()
{
    log_echo "INFO" "Begin to copy sudo files."
    chattr -R -i ${i2k_user_home}/sudoScripts
    cp -rf ${CURRENT_PATH}/sudoScripts ${i2k_user_home} > /dev/null 2>&1
    chattr -R -i ${i2k_user_home}/sudoScripts
    chown -R root:root ${i2k_user_home}/sudoScripts
    chmod 700 ${i2k_user_home}/sudoScripts

    ##sudoScripts/backup_restore.sh
    sed -i "s#{{oss_user_group}}#${i2k_user_name}:${i2k_group_name}#g"  ${i2k_user_home}/sudoScripts/backup_restore.sh

    #sudoScripts/kill_installpath.sh
    sed -i "s#{{i2k_install_path}}#${i2k_install_path}#g" ${i2k_user_home}/sudoScripts/kill_installpath.sh

    for file_500 in $(find ${i2k_user_home}/sudoScripts -name "*" -type f | grep -E '*.sh$' ) ;do
        chmod 500 ${file_500}
    done

    chattr -R +i ${i2k_user_home}/sudoScripts
    chmod 600 ${i2k_user_home}/i2k_install.properties
}

function check_rpm_fuse()
{
    rpm -qa | grep "^fuse-[0-9]"
    if [ $? -ne 0 ];then
        log_echo "INFO" "Need fuse rpm for HOFS, begin to install..."
        cd ${CURRENT_PATH}/rpm
        rpm -ivh fuse-*.rpm --nodeps --force
        if [ $? -ne 0 -o -z "`ls /usr/bin/fusermount`" ];then
            log_echo "ERROR" "Install fuse rpm failed, please check."
            exit 1
        fi
        chmod 755 /bin/fusermount
        chmod u+s /bin/fusermount
        chmod 755 /usr/bin/fusermount
        chmod u+s /usr/bin/fusermount
        cd - >/dev/null
    fi
}

function handle_dbuser_harden()
{
    grep "^dbuser:" /etc/passwd | grep "/bin/false" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "dbuser is harden by /bin/false, change to /bin/bash for gauss upgrade"
        cp -pf /etc/passwd /etc/passwd_dvupgrade
        usermod -s /bin/bash dbuser   
    fi

    cat /etc/ssh/sshd_config|grep -i '^[ |\t]*AllowGroups' > /dev/null
    con1=$?
    cat /etc/ssh/sshd_config|grep '^[ |\t]*AllowUsers'|grep   'ossadm' > /dev/null
    con2=$?
    cat /etc/ssh/sshd_config|grep '^[ |\t]*AllowUsers'|grep   'ossuser' > /dev/null
    con3=$?
    if [ $con1 -ne 0 ] && [ $con2 -eq 0 -a $con3 -eq 0 ];then
        cp -pf /etc/ssh/sshd_config /etc/ssh/sshd_config_dvupgrade
        ip_list=$(cat /opt/oss/manager/etc/sysconf/nodelists.json | grep "\"IP\"" | tr -d "\"|,| " | awk -F 'IP:' '{print $2}' | sort -nr | uniq | sed ':a;N;$!ba;s#\n#,#g')
        
        OLD_IFS=${IFS}
        IFS=,
        
        for ip in ${ip_list}
        do
            grep -w  "dbuser@$ip" /etc/ssh/sshd_config  > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i "/ClientAliveInterval[ |\t]\+/i\AllowUsers dbuser@$ip" /etc/ssh/sshd_config
            fi
        done
        
        IFS=${OLD_IFS}
        
    fi
}

function delete_old_sudo()
{
    user_name=$1
    need_delete_cmd_list=$(echo "$2" | sed 's/\\\:/:/g')
    sudo_user=$3
    if [ "X$sudo_user" == "X" ]; then
        sudo_user="root"
    fi
    cur_sudo=$(cat /etc/sudoers | grep "^${user_name} ALL=(${sudo_user})"|grep -v uniepsudobin |tail -1 | sed 's/\\\:/:/g'|awk -F'NOPASSWD:' '{print $2}'|sed 's/^ *//')
    if [ "X$cur_sudo" == "X" ]; then
        return 0
    else
        fina_sudo=$(echo "$cur_sudo" | awk -vnouse_sudo="$need_delete_cmd_list" -vtmp_sudo="" -F',' '{
            for (i = 1; i <= NF; i++)
            {
                if ( index(nouse_sudo",", $i",") == 0 )
                {
                    tmp_sudo=tmp_sudo","$i
                }
            }
            print tmp_sudo
        }' 2>/dev/null)
    fi

    tmp_finalsudo=$(echo "${fina_sudo}"|sed 's/ //g')
    [ "${tmp_finalsudo}" == "" ] && return 0

    fina_sudo=$(echo "${fina_sudo}" | sed 's/^,//g')
    fina_sudo="${user_name} ALL=(${sudo_user}) NOPASSWD:  ${fina_sudo}"
    local fina_sudo_new=$(echo "${fina_sudo}" | sed 's/:/\\\:/g' | sed 's/NOPASSWD\\\:/NOPASSWD:/g')

    local line_num=$(grep -n "^${user_name} ALL=(${sudo_user})" /etc/sudoers |grep -v uniepsudobin |tail -1|awk -F':' '{print $1}')
    if [ -z "${line_num}" ];then
        echo "${fina_sudo_new}" >> /etc/sudoers
    else
        sed -i "${line_num} a ${fina_sudo_new}" /etc/sudoers
        sed -i "${line_num},${line_num}d" /etc/sudoers
    fi
    return 0
}

function check_if_install_icnfg()
{
    if [ "X${is_install_icnfg}" == "XYes" ];then
        return 0
    else
        return 1
    fi
}

function check_sshdconfig()
{
    sshdconfig_file="/etc/ssh/sshd_config"
    grep -w "^PubkeyAcceptedKeyTypes" ${sshdconfig_file} > /dev/null 2>&1
    if [ $? -eq 0 ];then
        if [ -f "/home/<USER>/.ssh/sshagent_privatekey" ] || [ -f "/home/<USER>/sshagent_privatekey" ];then
            grep -w "^PubkeyAcceptedKeyTypes" ${sshdconfig_file} |grep -w "rsa-sha2-256" > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i '/^PubkeyAcceptedKeyTypes/ s/$/,rsa-sha2-256/g' ${sshdconfig_file}
            fi
        else
            grep -w "^PubkeyAcceptedKeyTypes" ${sshdconfig_file} |grep -w "ssh-rsa" > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i '/^PubkeyAcceptedKeyTypes/ s/$/,ssh-rsa/g' ${sshdconfig_file}
            fi
        fi

        if [ -f /.dockerenv ];then
            restart_sshd
        fi
    fi
}

function add_path_profile()
{
    PROFILE=/etc/profile.d/cie.sh
    [ -f $PROFILE ] || touch $PROFILE
    chmod 644 $PROFILE > /dev/null 2>&1
    if [ -n "$1" ] ; then
        pkey=$(echo "$1" | cut -d '=' -f 1)
        pvalue=$(echo "$1" | cut -d '=' -f 2)
        if [ -n "$pkey" ] ; then
            sed -i "/ ${pkey}=/d" $PROFILE
            if [ -n "$pvalue" ] ; then
                echo "export ${pkey}=${pvalue}" >> $PROFILE
                export ${pkey}=${pvalue}
            fi
        fi
    fi
    sed 's/export/setenv/g;s/=/ /' $PROFILE > /etc/profile.d/cie.csh
    chmod 644 /etc/profile.d/cie.csh > /dev/null 2>&1
}

function handle_icnfg_link()
{
    if [ -L ${icnfg_devdata_user_home}/configpkg ];then
        rm -rf ${icnfg_devdata_user_home}/configpkg
        mkdir -p  ${icnfg_devdata_user_home}/configpkg
        cp -a  ${i2k_install_path}/icnfg/webpages/var/data/configpkg/* ${icnfg_devdata_user_home}/configpkg
        touch /tmp/DVPreSet_upgrade_root/handle_icnfg_link${product_version}.tag
    fi
    if [ -L ${icnfg_devdata_user_home}/sftp/necert ];then
        rm -rf ${icnfg_devdata_user_home}/sftp/necert
        mkdir -p   ${icnfg_devdata_user_home}/sftp/necert
        cp -a  ${i2k_install_path}/icnfg/webpages/var/data/necert/* ${icnfg_devdata_user_home}/sftp/necert
    fi
    if [ -L ${icnfg_devdata_user_home}/sftp/jdbc ];then
        rm -rf ${icnfg_devdata_user_home}/sftp/jdbc
        mkdir -p  ${icnfg_devdata_user_home}/sftp/jdbc
        cp -a  ${i2k_install_path}/icnfg/webpages/var/data/jdbc/* ${icnfg_devdata_user_home}/sftp/jdbc
    fi

    devdata_mkdir
    devdata_set_permissions
    find ${icnfg_devdata_user_home}/configpkg -type f | xargs -i chown ossuser:ossgroup {}
    find ${icnfg_devdata_user_home}/sftp -type f | xargs -i chown ossuser:ossgroup {}
}

function adjust_pid_max()
{
    current_pid_max=$(cat /proc/sys/kernel/pid_max|sed 's/ //g')
    if [ ${current_pid_max} -lt 240000 ];then
        item_file_value=$(grep -w "kernel.pid_max" ${SYSCTLFILE}| awk -F"=" '{print $2}' | sed 's/  */ /g' | sed 's/  *$//g' | sed 's/^  *//g')

        if [ "X${item_file_value}" == "X" ];then
            echo "kernel.pid_max = 240000" >> ${SYSCTLFILE}
        elif [ ${item_file_value} -lt 240000 ];then
            sed -i "s/^kernel.pid_max[ ]*=[^&]*/kernel.pid_max = 240000 /" ${SYSCTLFILE}
        fi
    fi

    current_threads_max=$(cat /proc/sys/kernel/threads-max|sed 's/ //g')
    if [ ${current_threads_max} -lt 240000 ];then
        item_file_value=$(grep -w "kernel.threads-max" ${SYSCTLFILE}| awk -F"=" '{print $2}' | sed 's/  */ /g' | sed 's/  *$//g' | sed 's/^  *//g')

        if [ "X${item_file_value}" == "X" ];then
            echo "kernel.threads-max = 240000" >> ${SYSCTLFILE}
        elif [ ${item_file_value} -lt 240000 ];then
            sed -i "s/^kernel.threads-max[ ]*=[^&]*/kernel.threads-max = 240000 /" ${SYSCTLFILE}
        fi
    fi
}

function adjust_sysctl()
{
    local item="$1"
    local value="$2"
    if [ -z "${item}" -o -z "${value}" ];then
        log_echo "ERROR" "adjust_sysctl item=${item} or value=${value} has null.please check..."
        exit 1
    fi
    
    local old_cfg=$(sysctl -e ${item})
    local old_item_value=$(cat /etc/sysctl.conf | grep -w "${item}" | awk -F"=" '{print $2}' | sed 's/  */ /g' | sed 's/  *$//g' | sed 's/^  *//g')
    log_echo "INFO" "old_cfg=${old_cfg} old_item_value=${old_item_value}"
    
    sed -i "/^${item}\s*=/d" /etc/sysctl.conf
    sysctl -w ${item}="${value}" 
    echo  "${item} = ${value}" >> /etc/sysctl.conf
    sysctl -p /etc/sysctl.conf >/dev/null 2>&1
    local new_cfg=$(sysctl -e ${item})
    log_echo "INFO" "The new_cfg=${new_cfg}"
}

function adjust_parameter_sysctl()
{
    log_echo "INFO" "adjust parameter of os."
    SYSCTLFILE=/etc/sysctl.conf

    if [ -f "/.dockerenv"  ];then
        log_echo "INFO" "No need to change system parameters in docker "
        return 0
    fi

    is_integrated_df_and_service=$(cat ${dv_cfg_file}_root.tmp | grep "^is_integrated_df_and_service=" |awk -F'=' '{print $2}')
    if [ "$(echo ${is_integrated_df_and_service}|tr 'a-z' 'A-Z')" == "YES" ];then
        return 0
    fi

    cp -f ${SYSCTLFILE} ${CURRENT_PATH}/sysctl.conf_bak_$(date +%s)


    if [ -d "/proc/sys/net/ipv6/conf" ];then

        for nic in $(ls /proc/sys/net/ipv6/conf);
        do
            echo ${nic}|grep -E "veth|flannel|cni|docker|br_|gw_|vxlan_sys|dummy" >>/dev/null
            if [ $? -eq 0 ];then
                continue
            fi
            keyLine=$(grep "net.ipv6.conf.${nic}.dad_transmits[ ]*=" ${SYSCTLFILE}|wc -l)
            if [ $keyLine -lt 1 ];then
                echo  "net.ipv6.conf.${nic}.dad_transmits = 0" >> ${SYSCTLFILE}
            else
                sed -i "s/^net.ipv6.conf.${nic}.dad_transmits[ ]*=[^&]*/net.ipv6.conf.${nic}.dad_transmits = 0 /" ${SYSCTLFILE}
            fi
        done
    fi

    adjust_pid_max

    adjust_sysctl "vm.nr_hugepages" "0"
    adjust_sysctl "vm.max_map_count" "655360"
    adjust_sysctl "net.ipv4.ip_local_port_range" "40000 65000"

    sysctl -p ${SYSCTLFILE} >/dev/null 2>&1
    log_echo "INFO" "adjust parameter of os finished."
}

function set_acl_permission()
{
    if [ -d ${dvshare_install_path} ];then
        log_echo "INFO" "set acl permission for dvshare"
        setfacl -R -m d:u:${i2k_user_name}:rwx ${dvshare_install_path}
        setfacl -R -m d:g::- ${dvshare_install_path}
        setfacl -R -m d:m::rwx ${dvshare_install_path}
        setfacl -R -m u:${i2k_user_name}:rwx ${dvshare_install_path}
        setfacl -R -m g::- ${dvshare_install_path}
        setfacl -R -m m::rwx ${dvshare_install_path}
        find ${dvshare_install_path} -type f -exec chmod g-x {} +
        find ${dvshare_install_path} -type f -exec chmod 660 {} +
        find ${dvshare_install_path} -type d -exec chmod 770 {} +
    fi

    #change the priviledge for the file
    if [ -d "${i2k_install_path}/icnfg/webpages/var/data" ];then
        chmod 770 ${i2k_install_path}/icnfg/webpages/var/data
        find ${i2k_install_path}/icnfg/webpages/var/data -type d | xargs -i chmod 770 {}
        find ${i2k_install_path}/icnfg/webpages/var/data -type f | xargs -i chmod 660 {}
        setfacl -R -m d:u:ossuser:rwx ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m d:u:devdata:rwx ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m d:g::- ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m d:m::rwx ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m u:ossuser:rwx ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m u:devdata:rwx ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m g::- ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m m::rwx ${i2k_install_path}/icnfg/webpages/var/data
        find ${i2k_install_path}/icnfg/webpages/var/data -type  f | xargs -i chmod g-x {}
    fi

    local dir_list="${i2k_install_path}/backup /tmp/DVPreSet_upgrade /tmp/DVPreSet"
    local file_list="crt pem pfx jks p12"
    for dirTmp in ${dir_list};do
        if [ -d ${dirTmp} ];then
            for fileType in ${file_list};do
                find "${dirTmp}" -type f -a -not -perm 600 -a \( -name "*.${fileType}" \) -print0 | xargs -0 --no-run-if-empty chmod 600
            done
        fi
    done

    return 0
}

function backExtDiskInfo()
{
    current_user=$(whoami)
    if [ "X${current_user}" != "Xroot" ];then
        log_echo "INFO" "Begin to backExtDiskInfo but current_user=${current_user}.is not root."
        return 0
    fi
    if [ -f "/.dockerenv"  ];then
        log_echo "INFO" "No need to backExtDiskInfo in docker "
        return 0
    fi
    ext_disk_info=/etc/backup_ext_info
    echo "---- fdisk -l begin ---" > ${ext_disk_info}
    fdisk -l >> ${ext_disk_info}
    echo "---- fdisk -l end---" >> ${ext_disk_info}

    echo "---- lvdisplay begin ---" >> ${ext_disk_info}
    lvdisplay >> ${ext_disk_info}
    echo "---- lvdisplay end---" >> ${ext_disk_info}

    echo "---- vgdisplay begin ---" >> ${ext_disk_info}
    vgdisplay >> ${ext_disk_info}
    echo "---- vgdisplay end---" >> ${ext_disk_info}

    echo "---- pvdisplay begin ---" >> ${ext_disk_info}
    pvdisplay >> ${ext_disk_info} 2>>${LOG_FILE}
    echo "---- pvdisplay end---" >> ${ext_disk_info}

    echo "---- df -h begin ---" >> ${ext_disk_info}
    df -h  >> ${ext_disk_info}
    echo "---- df -h end---" >> ${ext_disk_info}

    echo "---- lsblk begin ---" >> ${ext_disk_info}
    lsblk >> ${ext_disk_info}
    echo "---- lsblk end---" >> ${ext_disk_info}

}

function modify_tmpdir_authority()
{
    tmp_dir_list="/tmp/DVPreSet /tmp/DVPreSet_upgrade"
    for tmp_dir in ${tmp_dir_list}
    do
        if [ -d "${tmp_dir}" ];then
            find ${tmp_dir} -type f -a \( -name "*.py" -o -name "*.pyc" -o -name "*.bat" -o -name "*.so" \) -print0 | xargs -0 --no-run-if-empty chmod 550
            find ${tmp_dir} -path ${tmp_dir}/backup -prune -o -name "*.sh" -exec chmod 550 {} +
            find ${tmp_dir} -name "*.txt" -exec chmod 640 {} +
        fi
    done

    return 0
}

function persist_system_files_in_container()
{
    if [ ! -f "/.dockerenv" ];then
        log_echo "INFO" "Virtualization scene installation ,no need to persist system files."
        return 0
    fi

    cp -p  /etc/ssh/sshd_config /os_config/sshd_config
    cp -p  /etc/sudoers /os_config/sudoers
    [ -f /etc/snb_sudoers ] && cp -p  /etc/snb_sudoers /os_config/snb_sudoers
    cp -p  /etc/sudoers.d/oss_uniep_sudoers /os_config/oss_uniep_sudoers
    cp -p  /etc/passwd /os_config/passwd
    cp -p  /etc/group  /os_config/group
    cp -p  /etc/shadow /os_config/shadow
    cp -p  /usr/bin/hwcmstool /os_config/hwcmstool
    cp -p  /etc/ssl/certs/HuaweiRootCAsipCRLs.crl  /os_config/HuaweiRootCAsipCRLs.crl
    cp -p  /etc/ssl/certs/HuaweiRootCAsipCRLs_Release.crl  /os_config/HuaweiRootCAsipCRLs_Release.crl
    cp -p  /etc/profile  /os_config/profile
    cp -p  /etc/sudoconfig_version  /os_config/sudoconfig_version
    cp -rp  /etc/uuid  /os_config/uuid
    [ -f "/os_config/root_shadow_bak" ] && rm -f /os_config/root_shadow_bak

    [ -f /etc/profile.d/cie.sh ]  && cp -p /etc/profile.d/cie.sh /os_config/cie.sh
    [ -f /etc/profile.d/cie.csh ]  && cp -p /etc/profile.d/cie.csh  /os_config/cie.csh
    [ -f /etc/profile.d/i2k-env.sh ]  && cp -p /etc/profile.d/i2k-env.sh  /os_config/i2k-env.sh
    [ -f /etc/profile.d/i2k-env.csh ]  && cp -p /etc/profile.d/i2k-env.csh  /os_config/i2k-env.csh

    return 0
}

function change_enforce_for_root()
{
    log_echo "INFO" "Begin to change commonpasswd."
    local commonpasswdfile="/etc/pam.d/common-password"
    sed -i "/pam_pwhistory.so/d" ${commonpasswdfile}

    local in_line=$(cat -n ${commonpasswdfile}|grep -w "pam_unix.so" |head -1 |awk '{print $1}')
    if [ -z "${in_line}" ];then
        in_line=1
    fi

    sed -i "${in_line} i password        required        pam_pwhistory.so        enforce_for_root  remember=5" ${commonpasswdfile}

    cp -rpf /etc/pam.d/common-password-pc /os_config/common-password-pc
    cp -rpf /etc/pam.d/common-password /os_config/common-password
    if [ -f /etc/security/opasswd ];then
        cp -rpf /etc/security/opasswd /os_config/opasswd
    fi
    log_echo "INFO" "Change commonpasswd successfully."
}

function upgrade_app_node()
{

    upgrade_check_fstab

    upgrade_kube_firewall_iptables

    create_readlink

    copy_i2k_user_home_file_root
    
    check_rpm_fuse

    rm -f /usr/bin/dv_python

    ln -s ${i2k_user_home}/python/bin/python /usr/bin/dv_python

    # handbook把dbuser改为/bin/false，但是uniep升级有时候需要登录dbuser
    handle_dbuser_harden

    #=$("
    delete_old_sudo "${i2k_user_name}" "${sudo_upgrade_need_delete_cmd_list}"

    check_if_install_icnfg && delete_old_sudo "${i2k_user_name}" "${sudo_list_devdata_need_delete}" "${icnfg_devdata_user_name}"
    check_if_install_icnfg && sed -i "/^${i2k_user_name} ALL=(${icnfg_devdata_user_name})/d" /etc/sudoers

    check_if_install_icnfg && delete_old_sudo "${i2k_user_name}" "${sudo_list_icnfg_need_delete}"

    delete_old_sudo "${system_operation_user}" "${sudo_list_sysomc_need_delete}"
    delete_old_sudo "${system_login_user}" "${sudo_login_user_need_delete}" "${i2k_user_name}"
    delete_old_sudo "${i2k_user_name}" "${sudo_cmd_list_ossadm_need_delete}" "ossadm"

    delete_old_sudo "${i2k_user_name}" "${install_path}/manager/agent/DeployAgent/bin/ipmc_adm -cmd restartapp -app ERService" "ossadm"

    if [ -f "/home/<USER>/sudoScripts/grep.sh" ];then
        chattr -i /home/<USER>/sudoScripts 2>/dev/null
        chattr -i /home/<USER>/sudoScripts/grep.sh 2>/dev/null
        rm -f /home/<USER>/sudoScripts/grep.sh
    fi
    if [ -f "/home/<USER>/sudoScripts/verify_asc.sh" ];then
        chattr -i /home/<USER>/sudoScripts 2>/dev/null
        chattr -i /home/<USER>/sudoScripts/verify_asc.sh 2>/dev/null
        rm -f /home/<USER>/sudoScripts/verify_asc.sh
    fi

    change_sudo_permission

    if [ -d "/opt/pub/software/repository/lib" ];then
        find "/opt/pub/software/repository/lib" -user root | xargs --no-run-if-empty chown ossadm:ossgroup 2>/dev/null
    fi
    if [ -d "${install_path}/manager/var/installpackages/libs" ];then
        find "${install_path}/manager/var/installpackages/libs" -user root | xargs --no-run-if-empty chown ossadm:ossgroup 2>/dev/null
    fi
    if [ -d "${install_path}/rtsp" ];then
        find "${install_path}/rtsp" -user root | xargs --no-run-if-empty chown ossadm:ossgroup 2>/dev/null
    fi
    if [ -d "/opt/oss/libossadm" ];then
        find /opt/oss/libossadm -user root | xargs --no-run-if-empty chown ossadm:ossgroup 2>/dev/null
    fi
    if [ -d "/opt/oss/libossuser" ];then
        find /opt/oss/libossuser -user root | xargs --no-run-if-empty chown ossuser:ossgroup 2>/dev/null
    fi
    if [ -d "/opt/oss/libsecuser" ];then
        find /opt/oss/libsecuser -user root | xargs --no-run-if-empty chown secuser:ossgroup 2>/dev/null
    fi

    if [ "X${i2k_is_dual}" == "XYes" ]; then
        check_sshdconfig
    fi

    chmod 644 /etc/profile.d/i2k-env.csh /etc/profile.d/i2k-env.sh > /dev/null 2>&1
    chown root:root /etc/profile.d/i2k-env.csh /etc/profile.d/i2k-env.sh > /dev/null 2>&1

    [ -d /opt/oss/ ]  &&  chown  ossadm:ossgroup /opt/oss/
    [ -d /opt/oss/share/ ]  &&  chown  ossadm:ossgroup /opt/oss/share/
    [ -d /opt/oss/share/SOP/ ]  &&  chown  ossadm:ossgroup /opt/oss/share/SOP/
    [ -d /opt/oss/share/SOP/DVEngineeringService/ ]  &&  chown  ossuser:ossgroup /opt/oss/share/SOP/DVEngineeringService/
    [ -d /opt/oss/share/SOP/DVEngineeringService/I2000 ] && chown ossuser:ossgroup -R /opt/oss/share/SOP/DVEngineeringService/I2000

    CIE_HOME="${i2k_install_path}/cie"
    add_path_profile "PYTHONPATH=${CIE_HOME}/dmu/rpackage/common/py:$PYTHONPATH"

    check_if_install_icnfg && handle_icnfg_link

    adjust_parameter_sysctl

    if [ -f "${CURRENT_PATH}/mount_extdisk_finish" ];then
        cp ${CURRENT_PATH}/mount_extdisk_finish ${system_login_userhome}
        chown ${system_login_user}:${i2k_group_name} ${system_login_userhome}/mount_extdisk_finish
        chmod 600 ${system_login_userhome}/mount_extdisk_finish
        if [ -d "${system_login_userhome}" -a -f "${CURRENT_PATH}/new_partition_finish" ];then
            cp ${CURRENT_PATH}/new_partition_finish ${system_login_userhome}/new_partition_finish
            chown ${system_login_user}:${i2k_group_name} ${system_login_userhome}/new_partition_finish
            chmod 600 ${system_login_userhome}/new_partition_finish
        fi
    fi

    [ -d "${VS_PATH}" ] && chown -R ${i2k_user_name}:${i2k_group_name} "${VS_PATH}"

    [ -d "${KAFKA_PATH}" ] && chown -R ${i2k_user_name}:${i2k_group_name} "${KAFKA_PATH}"

    if [ -d ${install_path}/uds_ossuser ];then
        chown -R ${i2k_user_name}:${i2k_group_name}  ${install_path}/uds_ossuser
    fi

    set_acl_permission

    backExtDiskInfo

    modify_tmpdir_authority

    persist_system_files_in_container

    chattr -i -R /home/<USER>/sudoScripts
    rm -rf /home/<USER>/sudoScripts/create_dvshare_path.sh
    rm -rf /home/<USER>/sudoScripts/i2kadm_logcollect.sh
    rm -rf /home/<USER>/sudoScripts/I2000_gatherinfo.sh
    rm -rf /home/<USER>/sudoScripts/I2000_gatherinfo_branch.sh
    chattr +i -R /home/<USER>/sudoScripts

    if [ -f /.dockerenv ];then
        log_echo "INFO" "backup /root to /os_config."
        cp -rpf /root /os_config
        change_enforce_for_root
    fi
}

function config_ssh_for_db_node()
{
    sed -i "s/^[ ]*PasswordAuthentication.*/PasswordAuthentication yes/g" /etc/ssh/sshd_config
    cat /etc/ssh/sshd_config |grep -w "^PasswordAuthentication yes" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        sed -i "1 i PasswordAuthentication yes" /etc/ssh/sshd_config
    fi
    log_echo "INFO" "modify Authentication to yes of /etc/ssh/sshd_config finish."

    sed -i "s/^[ ]*ChallengeResponseAuthentication.*/ChallengeResponseAuthentication no/g" /etc/ssh/sshd_config
    cat /etc/ssh/sshd_config |grep -w "^ChallengeResponseAuthentication no" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        sed -i "1 i ChallengeResponseAuthentication no" /etc/ssh/sshd_config
    fi
    log_echo "INFO" "modify ChallengeResponseAuthentication to no of /etc/ssh/sshd_config finish."

}




function permission_change()
{
    local path="$1"
    local file_suffix="$2"
    local permission="$3"
    if [ -z "${path}" -o -z "${file_suffix}" -o -z "${permission}" ];then
        log_echo "INFO" "The path=${path} or file_suffix=${file_suffix} or permission=${permission} has null."
        return 0
    fi

    if [ ! -d ${path} ];then
        log_echo "INFO" "The path=${path} is not exists."
        return 0
    fi

    log_echo "INFO" "The path=${path} is exists."
    local tmp_suffix_cmd=""
    for tmp_suffix in $(echo "${file_suffix}"|sed "s/,/ /g");do
        if [ -z "${tmp_suffix_cmd}" ];then
            tmp_suffix_cmd=" -name \"${tmp_suffix}\""
            continue
        fi

        tmp_suffix_cmd="${tmp_suffix_cmd} -o -name \"${tmp_suffix}\""

    done
    local find_cmd="find ${path} -type f -a \( ${tmp_suffix_cmd} \) -print0 | xargs -0 --no-run-if-empty chmod ${permission} >> ${LOG_FILE} 2>&1"
    eval ${find_cmd}
    local ret=$?
    log_echo "INFO" "Execute cmd:[ ${find_cmd} ] finished.ret=${ret}"
}


function permission_handle()
{
    permission_change "${install_path}/manager/var/etc/backuprestore" "*.json,*.cfg" "640"
    permission_change "${install_path}/manager/var/etc/common" "*.cfg" "640"
    permission_change "${install_path}/manager/var/etc/sysmt/networkmgmt" "*.properties,*.cfg" "640"

    log_echo "INFO" "handle permission finished."
    return 0
}

function upgrade_permission_handle()
{
    log_echo "INFO" "Begin to handle permission."

    permission_handle
    user_home_list="${ossadm_user_home} ${dbuser_user_home} ${system_sftp_userhome} ${sop_user_home} ${system_login_userhome} ${system_operation_userhome} ${dvshare_user_home} ${icnfg_devdata_user_home}"
    for user_home in ${user_home_list}
    do
        chmod 600 ${user_home}/.bash* >> $LOG_FILE 2>&1
        chmod 600 ${user_home}/.mksh* >> $LOG_FILE 2>&1
        chmod 600 ${user_home}/.ksh*  >> $LOG_FILE 2>&1
        chmod 600 ${user_home}/.zsh*  >> $LOG_FILE 2>&1
        chmod 600 ${user_home}/.csh*  >> $LOG_FILE 2>&1
        if [ "${user_home}" != "${icnfg_devdata_user_home}" ];then
            [ -d "${user_home}/bin" ] && chmod 700 ${user_home}/bin
            [ -d "${user_home}/.fonts" ] && chmod 700 ${user_home}/.fonts
        fi
    done

    if [ -d /home/<USER>/.gnupg ];then
        chown -R ossadm:ossgroup /home/<USER>/.gnupg
        find /home/<USER>/.gnupg -type f |xargs chmod 600
        find /home/<USER>/.gnupg -type d |xargs chmod 700
    fi

    if [ -z "${i2k_user_home}" ];then
        log_echo "INFO" "The i2k_user_home=${i2k_user_home} is null."
    else
        permission_change "${i2k_user_home}/.ssh" "*.pub" "600"
        chmod 600 ${i2k_user_home}/.ssh/known_hosts >/dev/null 2>&1

        permission_change "${i2k_user_home}/etc/ssl" "*.crt,*.jks,*.csr,*.key,*.p12,*.cnf,*.pem,*.json,*.pfx,*.properties,*.keystore,*.bak" "600"
        chmod 600 ${i2k_user_home}/etc/ssl/dv/*_tag >/dev/null 2>&1

        permission_change "${i2k_user_home}/python" "*.pc,*.json,*.xml,*.cfg" "640"
        permission_change "${i2k_user_home}/python" "*.html,*.css,*.ps1" "440"
        permission_change "${i2k_user_home}/python" "*.pem,*.crl" "600"
    fi
    if [ -z "${VS_PATH}" ];then
        log_echo "INFO" "The VS_PATH=${VS_PATH} is null."
    else
        if [ -d ${VS_PATH} ];then
            log_echo "INFO" "The VS_PATH=${VS_PATH} is exists."
            chmod 750 ${VS_PATH} >> ${LOG_FILE} 2>&1
        else
            log_echo "INFO" "The VS_PATH=${VS_PATH} is not exists."
        fi
    fi

    if [ ! -z "${icnfg_devdata_user_home}" -a -d ${icnfg_devdata_user_home} ];then
        log_echo "INFO" "The icnfg_devdata_user_home=${icnfg_devdata_user_home} is exists."
        chmod 750 ${icnfg_devdata_user_home} >> ${LOG_FILE} 2>&1
    fi

    if [ -d "/opt/pub/software/repository/lib" ];then
        find "/opt/pub/software/repository/lib" -user root | xargs --no-run-if-empty chown ossadm:ossgroup
    fi

    if [ -d "${install_path}/manager/var/installpackages/libs" ];then
        find "${install_path}/manager/var/installpackages/libs" -user root | xargs --no-run-if-empty chown ossadm:ossgroup 2>/dev/null
    fi
    if [ -d "${install_path}/rtsp" ];then
        find "${install_path}/rtsp" -user root | xargs --no-run-if-empty chown ossadm:ossgroup 2>/dev/null
    fi
    if [ -d "/opt/oss/libossadm" ];then
        find /opt/oss/libossadm -user root | xargs --no-run-if-empty chown ossadm:ossgroup 2>/dev/null
    fi
    if [ -d "/opt/oss/libossuser" ];then
        find /opt/oss/libossuser -user root | xargs --no-run-if-empty chown ossuser:ossgroup 2>/dev/null
    fi
    if [ -d "/opt/oss/libsecuser" ];then
        find /opt/oss/libsecuser -user root | xargs --no-run-if-empty chown secuser:ossgroup 2>/dev/null
    fi
}

function upgrade_db_node() {
    . ${dv_cfg_file}_root.tmp
    # DV_PreSet_DB
    upgrade_env_enhancement
    
    check_dirs_io "${check_io_dbnode}"
    
    delete_old_sudo "${system_operation_user}" "${sudo_list_sysomc_need_delete}"

    config_ssh_for_db_node

    create_readlink

    handle_dbuser_harden

    if [ -d "${install_path}/manager/var/installpackages/libs" ];then
        find "${install_path}/manager/var/installpackages/libs" -user root | xargs --no-run-if-empty chown ossadm:ossgroup 2>/dev/null
    fi
    if [ -d "${install_path}/rtsp" ];then
        find "${install_path}/rtsp" -user root | xargs --no-run-if-empty chown ossadm:ossgroup 2>/dev/null
    fi
    if [ -d "/opt/oss/libossadm" ];then
        find /opt/oss/libossadm -user root | xargs --no-run-if-empty chown ossadm:ossgroup 2>/dev/null
    fi
    if [ -d "/opt/oss/libossuser" ];then
        find /opt/oss/libossuser -user root | xargs --no-run-if-empty chown ossuser:ossgroup 2>/dev/null
    fi
    if [ -d "/opt/oss/libsecuser" ];then
        find /opt/oss/libsecuser -user root | xargs --no-run-if-empty chown secuser:ossgroup 2>/dev/null
    fi

    adjust_parameter_sysctl
    check_user_ssh "ossadm"
    check_user_ssh "ossuser"
    check_user_pwd_expires "ossadm,dbuser,${i2k_user_name},${system_login_user},${system_sftp_user},${sop_user_name},${system_operation_user}" || exit 1
    upgrade_check_fstab
    upgrade_kube_firewall_iptables
    modify_tmpdir_authority
    backExtDiskInfo
    permission_handle
}


function upgrade_other_node()
{
    . ${dv_cfg_file}_root.tmp
    upgrade_env_enhancement
    check_dirs_io "${check_io_othernode}"

    create_readlink

    handle_dbuser_harden

    if [ -d "${install_path}/manager/var/installpackages/libs" ];then
        find "${install_path}/manager/var/installpackages/libs" -user root | xargs --no-run-if-empty chown ossadm:ossgroup 2>/dev/null
    fi
    if [ -d "${install_path}/rtsp" ];then
        find "${install_path}/rtsp" -user root | xargs --no-run-if-empty chown ossadm:ossgroup 2>/dev/null
    fi
    if [ -d "/opt/oss/libossadm" ];then
        find /opt/oss/libossadm -user root | xargs --no-run-if-empty chown ossadm:ossgroup 2>/dev/null
    fi
    if [ -d "/opt/oss/libossuser" ];then
        find /opt/oss/libossuser -user root | xargs --no-run-if-empty chown ossuser:ossgroup 2>/dev/null
    fi
    if [ -d "/opt/oss/libsecuser" ];then
        find /opt/oss/libsecuser -user root | xargs --no-run-if-empty chown secuser:ossgroup 2>/dev/null
    fi

    check_ret=0
    check_sudo_config || check_ret=1
    check_immutable_file || check_ret=1
    [ "${check_ret}" == "1" ] && exit 1

    delete_old_sudo "${system_operation_user}" "${sudo_list_sysomc_need_delete}"

    adjust_parameter_sysctl
    modify_tmpdir_authority
    check_user_ssh "ossadm"
    check_user_ssh "ossuser"
    check_user_pwd_expires "ossadm,dbuser,${i2k_user_name},${system_login_user},${system_sftp_user},${sop_user_name},${system_operation_user}" || exit 1
    upgrade_check_fstab
    upgrade_kube_firewall_iptables

}

function clean_preset_pkg()
{
    if [ -f /.dockerenv -a -d /opt/DVZIPConfig/scan_softpackage ];then
        rm -rf /opt/DVZIPConfig/scan_softpackage
        log_echo "INFO" "clean /opt/DVZIPConfig/scan_softpackage."
    fi
    
    if [ -f /.dockerenv -a -f /opt/DVZIPConfig/UniEp/UniEPMgrV*R*C*.zip ];then
        rm -rf /opt/DVZIPConfig/UniEp/UniEPMgrV*R*C*.zip
        rm -rf /opt/DVZIPConfig/UniEp/UniEPMgrV*R*C*.zip.cms
        log_echo "INFO" "clean /opt/DVZIPConfig/UniEp/UniEPMgrV*R*C*.zip UniEPMgrV*R*C*.zip.cms."
    fi
    
    if [ -f /.dockerenv -a -d /opt/DVZIPConfig/UniEp/UniEPMgr ];then
        rm -rf /opt/DVZIPConfig/UniEp/UniEPMgr/*.zip
        log_echo "INFO" "clean /opt/DVZIPConfig/UniEp/UniEPMgr/*.zip."
    fi
}

function check_user_pwd_expires()
{
    local user_list="$1"
    local node_info="$2"
    if [ -z "${user_list}" ];then
        log_echo "ERROR" "The user_list=${user_list} is null, please check"
        return 1
    fi
    local check_ret=0
    for user_name in $(echo "${user_list}"|sed "s/,/ /g" );do
        check_user_pwd_chage "${user_name}" "${node_info}"
        check_ret=$(( ${check_ret} + $? ))
    done

    return ${check_ret}
}


function uniep_upgrade_preset()
{
    log_echo "INFO" "uniep_upgrade_preset_check start..."
    Check_execute "uniep_upgrade_preset"
    if [ $? -eq 0 ];then
        return 0
    fi

    clean_preset_pkg
    
    check_user_pwd_expires "ossadm,dbuser,${i2k_user_name},${icnfg_devdata_user_name},${system_login_user},${system_sftp_user},${sop_user_name},${dvshare_user_name},${system_operation_user}" "on this node"|| exit 1


    upgrade_permission_handle
    upgrade_env_enhancement
    check_dirs_io "${check_io_uniepnode}"
    check_ret=0
    check_sudo_config || check_ret=1
    check_immutable_file || check_ret=1
    [ "${check_ret}" == "1" ] && exit 1

    if [ -f "/opt/zenith/zengine_instance_cloudsop_large.ini" ];then
        cp -rp /opt/zenith/zengine_instance_cloudsop_large.ini /opt/zenith/zengine_instance_cloudsop_rm.ini
    fi

    delete_old_sudo "${system_operation_user}" "${sudo_list_sysomc_need_delete}"

    if [ -f "/opt/zenith/app/add-ons/libuuid.so" ];then
        mv -f /opt/zenith/app/add-ons/libuuid.so* ${base_path}/UniEp
    fi

    create_readlink

    adjust_parameter_sysctl

    upgrade_check_fstab

    if [ -d "${install_path}/manager/etc/ssl" ];then
        log_echo "INFO" "The ${install_path}/manager/etc/ssl is exists."
        chown -R ossadm:ossgroup ${install_path}/manager/etc/ssl
        if [ $? -ne 0 ];then
            log_echo "ERROR" "failed chown ${install_path}/manager/etc/ssl to ossadm:ossgroup!please check ..."
            exit 1
        fi
    fi
    
    check_tzdata
    
    adap_profile_for_kylin_enhance
    
    echo "uniep_upgrade_preset : success" >> ${action_tag}
    log_echo "INFO" "uniep_upgrade_preset_check finish."
    return 0
}

function devdata_mkdir()
{
    mkdir -p ${icnfg_devdata_user_home}/sftp/jdbc/
    mkdir -p ${icnfg_devdata_user_home}/sftp/necert/
    mkdir -p ${icnfg_devdata_user_home}/sftp/export/manual/
    mkdir -p ${icnfg_devdata_user_home}/sftp/import/manual/
    mkdir -p ${icnfg_devdata_user_home}/configpkg/
}

function devdata_set_permissions()
{
    if [ -d  ${icnfg_devdata_user_home}  ];then
        chown -R devdata:ossgroup  ${icnfg_devdata_user_home}/configpkg/
        chown -R devdata:ossgroup  ${icnfg_devdata_user_home}/sftp/
        setfacl -R -m d:u:ossuser:rwx ${icnfg_devdata_user_home}/sftp
        setfacl -R -m d:u:devdata:rwx  ${icnfg_devdata_user_home}/sftp
        setfacl -R -m d:g::-  ${icnfg_devdata_user_home}/sftp
        setfacl -R -m d:m::rwx  ${icnfg_devdata_user_home}/sftp
        setfacl -R -m u:ossuser:rwx ${icnfg_devdata_user_home}/sftp
        setfacl -R -m u:devdata:rwx  ${icnfg_devdata_user_home}/sftp
        setfacl -R -m g::-  ${icnfg_devdata_user_home}/sftp
        setfacl -R -m m::rwx  ${icnfg_devdata_user_home}/sftp

        setfacl -R -m d:u:ossuser:rwx ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m d:u:devdata:rwx  ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m d:g::-  ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m d:m::rwx  ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m u:ossuser:rwx ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m u:devdata:rwx  ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m g::-  ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m m::rwx  ${icnfg_devdata_user_home}/configpkg/

        find ${icnfg_devdata_user_home} -type d | xargs -i chmod 770 {}
        find ${icnfg_devdata_user_home} -type f | xargs -i chmod 660 {}
        if [ -d ${icnfg_devdata_user_home}/.ssh  ];then
            chmod 700 ${icnfg_devdata_user_home}/.ssh
            chown -R ${icnfg_devdata_user_name}.${i2k_group_name} ${icnfg_devdata_user_home}/.ssh/
        fi
        if [ -f ${icnfg_devdata_user_home}/.ssh/authorized_keys  ];then
            chmod 640 ${icnfg_devdata_user_home}/.ssh/authorized_keys
        fi
        chmod 750  ${icnfg_devdata_user_home}
    fi
}

function check_tmp_oss()
{
    for i in `ls /opt/oss/manager/apps/`;
    do
        if [ ! -d /tmp/oss/manager/$i ] ;then
            mkdir -p /tmp/oss/manager/$i
            chown ossadm:ossgroup  /tmp/oss/manager/$i
        fi
    done
}

function check_user_pwd_chage()
{
    local user_name="$1"
    local node_info="$2"
    if [ -z "${user_name}" ];then
        log_echo "ERROR" "The username=${user_name} is null, please check"
        return 1
    fi

    id $user_name >/dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "The user_name=${user_name} is not exist,not need to check."
        return 0
    fi
    last_change_date=$(grep -w "$user_name:" /etc/shadow |cut -d: -f3)
    current_date=$(expr $(date '+%s') / 86400 )
    max_date=$(grep -w "$user_name:" /etc/shadow |cut -d: -f5|tr -d '[:space:]')
    if [ "X${max_date}" == "X" ];then
        return 0
    fi
    expires_date=$(expr $max_date  + $last_change_date - $current_date )
    if [ $expires_date -lt 0 ];then
        log_echo "ERROR" "${user_name}'s password has expired ${node_info}."
        return 1
    elif [ $expires_date -lt 10 ];then
        log_echo "ERROR" "${user_name}'s password will expired after $expires_date days ${node_info}."
        return 1
    fi
    log_echo  "INFO" "${user_name}'s passwd expires date is normal"
    return 0
}
function encrypt_by_uniep()
{
    local de_pvalue="$1"
    local encrypt_type=$2

    if [ -f "/proc/sys/kernel/random/entropy_avail" ];then
        random_num=$(cat /proc/sys/kernel/random/entropy_avail)
        log_echo "INFO"  "The value of random/entropy_avail is ${random_num} ..." > /dev/null
        if [ ${random_num} -lt 300 ];then
            haveged_path=$(which haveged 2>/dev/null)
            [ -n "$haveged_path" ] && ${haveged_path} -w 1024 -v 1 > /dev/null 2>&1
        fi
    fi

    local en_pvalue=""
    local get_ret=0
    for try_count in $(seq 1 5);do
        if [ -f "/proc/sys/kernel/random/entropy_avail" ];then
            random_num=$(cat /proc/sys/kernel/random/entropy_avail)
            log_echo "INFO"  "The value of random/entropy_avail is ${random_num} ..." > /dev/null
            if [ ${random_num} -lt 300 ];then
                haveged_path=$(which haveged 2>/dev/null)
                [ -n "$haveged_path" ] && ${haveged_path} -w 1024 -v 1 > /dev/null 2>&1
            fi
        fi
        en_pvalue=$(echo "${de_pvalue} uniep:passwd"| expect ${CURRENT_PATH}/../tools/su_ossadm_exec.exp "${PreSet_PATH}/tools/uniep_encrypt.py" "${encrypt_type}" | grep -vE "source|logout|uniep_encrypt|exit" | tail -1 | sed 's/ //g')
        if [ -z "`echo ${en_pvalue}|grep ^000`" -a -z "`echo ${en_pvalue}|grep ^AAA`" ];then
            log_echo "INFO" "encrypt by uniep failed , try again try_count=${try_count}"
            get_ret=1
        else
            get_ret=0
            break
        fi
    done

    if [ ${get_ret} -eq 1 ];then
        log_echo "ERROR" "encrypt by uniep failed, please check"
        exit 1
    fi
    pwd_return="${en_pvalue}"
    return 0
}

function create_pod_pwfile()
{
    local tmp_user="$1"
    local tmp_pwd="$2"
    local tmp_type="$3"
    if [ -z "${tmp_user}" ];then
        log_echo "ERROR" "The tmp_user=${tmp_user} is null."
        exit 1
    fi
    
    local pod_pwfile=${base_path}/Common/${tmp_type}_pod_pwfile.json
    if [ -z "${tmp_pwd}" -a ! -f ${pod_pwfile} ];then
        log_echo "ERROR" "get ${tmp_user} pwd is null."
        exit 1
    fi
    
    if [ -z "${tmp_pwd}" ];then
        log_echo "INFO" "not need to create_pod_pwfile."
        return 0
    fi
    
    pwd_value_en=$(echo "${tmp_pwd}" |su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python -c \"import sys;from util import ossext;print(ossext.Cipher.encrypt(sys.stdin.read().strip()))\"")
    local docker_pwfile="{\"name\":\"${tmp_user}\",\"value\":\"${pwd_value_en}\",\"encrypt\":\"yes\"}"
    echo "[${docker_pwfile}]" > ${base_path}/Common/${tmp_type}_pod_pwfile.json
    chmod 644 ${base_path}/Common/${tmp_type}_pod_pwfile.json
}

function get_host_ip()
{
    local cfg_file=/os_config/DV_Config_MergeCluster_Docker.config
    if [ ! -f ${cfg_file} ];then
        log_echo "INFO" "The ${cfg_file} is not exists.we need get host ip."
        touch ${cfg_file}
    else
        log_echo "INFO" "The ${cfg_file} is exists."
    fi

    [ ! -f ${base_path}/tools/getInfo.log ] && touch ${base_path}/tools/getInfo.log
    chown ossadm:ossgroup ${base_path}/tools/get_install_info.py
    chown ossadm:ossgroup ${base_path}/tools/getInfo.log

    host_ip_list=$(su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;python ${base_path}/tools/get_install_info.py \"${install_path}/manager/etc/sysconf/nodelists.json\" \"get_merge_cluster_host_ip\" ")
    check_ret=$(echo "${host_ip_list}"|grep "ERROR")
    if [ ! -z "${check_ret}" ];then
        log_echo "ERROR" "get host ip has error.check_ret=${check_ret}"
        exit 1
    fi

    ## print(primary_ip_list + "|" + secondary_ip_list)
    DVPrimary_IPV4=$(echo "${host_ip_list}"|awk -F'|' '{print $1}'|awk -F',' '{print $1}')
    HostPrimary_IPV4=$(echo "${host_ip_list}"|awk -F'|' '{print $1}'|awk -F',' '{print $2}')

    DVSecondary_IPV4=$(echo "${host_ip_list}"|awk -F'|' '{print $2}'|awk -F',' '{print $1}')
    HostSecondary_IPV4=$(echo "${host_ip_list}"|awk -F'|' '{print $2}'|awk -F',' '{print $2}')

    update_config ${cfg_file} "HostPrimary_IPV4" "${HostPrimary_IPV4}" || exit 1
    update_config ${cfg_file} "DVPrimary_IPV4" "${DVPrimary_IPV4}" || exit 1
    update_config ${cfg_file} "HostSecondary_IPV4" "${HostSecondary_IPV4}" || exit 1
    update_config ${cfg_file} "DVSecondary_IPV4" "${DVSecondary_IPV4}" || exit 1
    log_echo "INFO" "The ${cfg_file} update ip successfully."
}

function cleanup_iptables_host()
{
    local peer_host_ip=$1
    local peer_pod_ip=$2
    tmp_peer_host_ip=$(echo "${peer_host_ip}"|sed "s/\./\\\./g")
    tmp_peer_pod_ip=$(echo "${peer_pod_ip}"|sed "s/\./\\\./g")
    check_ret=$(iptables -w -t nat -L -n |grep -w "${tmp_peer_host_ip}" |grep -w "${tmp_peer_pod_ip}")
    log_echo "INFO" "The node_role=primarynode check_ret=${check_ret}."
    if [ ! -z "${check_ret}" ];then
        log_echo "INFO" "execute cmd:[iptables -t nat -D OUTPUT -p tcp -d ${peer_host_ip} --dport 22 -j DNAT --to-destination ${peer_pod_ip}:22]."
        iptables -t nat -D OUTPUT -p tcp -d ${peer_host_ip} --dport 22 -j DNAT --to-destination ${peer_pod_ip}:22
        local iptables_del_ret=$?
        if [ ${iptables_del_ret} -ne 0 ];then
            iptables --wait -t nat -D OUTPUT -p tcp -d ${peer_host_ip} --dport 22 -j DNAT --to-destination ${peer_pod_ip}:22
            iptables_del_ret=$?
        fi

        if [ ${iptables_del_ret} -ne 0 ];then
           log_echo "ERROR" "The iptables on PrimaryNode local del failed."
           exit 1
        fi
    fi
}

function decrypt_pod_pwfile()
{
    local tmp_type="$1"
    if [ -z "${tmp_type}" ];then
        log_echo "ERROR" "The tmp_type=${tmp_type} is null."
        exit 1
    fi
    
    local pod_pwfile=${base_path}/Common/${tmp_type}_pod_pwfile.json
    tmp_user_name=$(cat ${pod_pwfile}| sed 's/}/\n/g' | grep -w "\"name\":" | awk -F : '{print $2}')
    tmp_user_name=${tmp_user_name#*\"}
    tmp_user_name=${tmp_user_name%%\"*}
    
    user_pwd_encrypt=$(cat ${pod_pwfile}| sed 's/}/\n/g' | grep -w "${tmp_user_name}" | awk -F : '{print $3}')
    if [ -z "${user_pwd_encrypt}" ];then
        log_echo "ERROR" "get ${tmp_user_name} password failed.of decrypt_pod_pwfile"
        exit 1
    fi
    user_pwd_encrypt=${user_pwd_encrypt#*\"}
    user_pwd_encrypt=${user_pwd_encrypt%%\"*}
    
    tmp_pwd_de=$(echo "${user_pwd_encrypt}" |su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python -c \"import sys;from util import ossext;print(ossext.Cipher.decrypt(sys.stdin.read().strip()))\"")
    if [ -z "${tmp_pwd_de}" ];then
        log_echo "ERROR" "decrypt tmp_pwd_de password failed.of decrypt_pod_pwfile"
        exit 1
    fi
}

function docker_set_pod_iptables()
{
    log_echo "INFO" "start docker_set_pod_iptables..." 
    local app_ssh_ip=$1
    local app_scp_ip=$1
    local root_pwd=$2
    local node_role="$3"
    local do_type="$4"
    local app_ssh_user="$5"
    local app_ssh_pwd="$6"
    local scene_type="$7"
    
    if [ "X${do_type}" != "Xadd_pod_iptables" -a "X${do_type}" != "Xdel_pod_iptables" ];then
        log_echo "ERROR" "The do_type=${do_type} is error,need is add_pod_iptables or del_pod_iptables, please check set_pod_iptables."
        exit 1
    fi
    
    local isIpv6=$(echo ${app_scp_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        app_scp_ip="\[${app_scp_ip}\]"
    fi
    
    local tmp_dir=/tmp/DVPreSet_upgrade_docker_ossadm
    if [ "X${node_role}" == "XPrimary" ];then
        rm -rf ${tmp_dir}
        mkdir -p ${tmp_dir}
        chmod 755 -R ${tmp_dir}
        
        cp -rpf ${base_path}/Common/DV_PreSet_APP.sh ${tmp_dir}
        if [ ! -f ${base_path}/Common/DV_config.properties.tmp ];then
            cp -rpf ${base_path}/Common/DV_config.properties ${base_path}/Common/DV_config.properties.tmp
        fi
        cp -rpf ${base_path}/Common/DV_config.properties.tmp ${tmp_dir}
        cp -rpf ${base_path}/Common/utils_common.sh ${tmp_dir}
        cp -rpf ${base_path}/Common/utils_os.sh ${tmp_dir}
        cp -rpf ${base_path}/Common/utils_uniep.sh ${tmp_dir}
        
        sh ${tmp_dir}/DV_PreSet_APP.sh ${do_type}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset cmd:[ sh ${tmp_dir}/DV_PreSet_APP.sh ${do_type} ] in DV node ${app_ssh_ip} failed"
            return 1
        fi
        log_echo "INFO" "The node_role=${node_role} ${do_type} finished.ret=$?" 
        return 0
    fi
    
    if [ "X${node_role}" != "XSecondary" ];then
        log_echo "INFO" "The node_role=${node_role}.skip it." 
        return 0
    fi
    
    if [ "X${root_pwd}" == "X" ];then
        log_echo "ERROR" "The root password for ${app_ssh_ip} is null, please check"
        exit 1
    fi
    
    local file_list="DV_config.properties.tmp DV_PreSet_APP.sh utils_common.sh utils_uniep.sh utils_os.sh"
    log_echo "INFO" "Begin to execute set_pod_iptables with DV_PreSet_APP.sh..."
    if [ ! -f ${base_path}/Common/DV_config.properties.tmp ];then
        cp -rpf ${base_path}/Common/DV_config.properties ${base_path}/Common/DV_config.properties.tmp
    fi
    
    ssh_execute "${app_ssh_user}" "${app_ssh_pwd}" "${root_pwd}" "${app_ssh_ip}" "rm -rf ${tmp_dir}"
    ssh_execute "${app_ssh_user}" "${app_ssh_pwd}" "${root_pwd}" "${app_ssh_ip}" "mkdir -p ${tmp_dir}"
    ssh_execute "${app_ssh_user}" "${app_ssh_pwd}" "${root_pwd}" "${app_ssh_ip}" "chown -R ${app_ssh_user}. ${tmp_dir}"
    ssh_execute "${app_ssh_user}" "${app_ssh_pwd}" "${root_pwd}" "${app_ssh_ip}" "chmod -R 755 ${tmp_dir}"
    for tmpfile in ${file_list}
    do
        auto_scp ${app_ssh_pwd} ${base_path}/Common/${tmpfile} "${app_ssh_user}@${app_scp_ip}:${tmp_dir}"
    done
    
    local os_config_tmp=${base_path}/os_config_tmp
    mkdir -p ${os_config_tmp}
    cp -rpf /os_config/DV_Config_MergeCluster_Docker.config ${os_config_tmp}
    chown -R ossadm:ossgroup ${os_config_tmp}
    
    auto_scp ${app_ssh_pwd} ${os_config_tmp}/DV_Config_MergeCluster_Docker.config "${app_ssh_user}@${app_scp_ip}:${tmp_dir}"
    ssh_execute "${app_ssh_user}" "${app_ssh_pwd}" "${root_pwd}" "${app_ssh_ip}" "chmod -R 755 ${tmp_dir}"
    
    ssh_execute "${app_ssh_user}" "${app_ssh_pwd}" "${root_pwd}" "${app_ssh_ip}" "mv ${tmp_dir}/DV_Config_MergeCluster_Docker.config /os_config/DV_Config_MergeCluster_Docker.config"
    
    ssh_execute "${app_ssh_user}" "${app_ssh_pwd}" "${root_pwd}" "${app_ssh_ip}" "sh ${tmp_dir}/DV_PreSet_APP.sh ${do_type}" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Preset dv ${do_type} in DV node ${app_ssh_ip} failed"
        return 1
    fi
    
    log_echo "INFO" "execute DV_PreSet_APP.sh finished."
    log_echo "INFO" "${do_type} finished."
}

function docker_modifyproductinfo()
{
    if [ ! -f ${configParametersFile} ];then
        log_echo "ERROR" "The configParametersFile=${configParametersFile} is not exists."
        exit 1
    fi
    source ${configParametersFile}
    
    if [ ! -f /.dockerenv -o "${netWorkType}" != "M" ];then
        log_echo "INFO" "The netWorkType=${netWorkType},not need do it."
        return 0
    fi
    
    new_json_path="${base_path}/tools/newjson"
    product_json_path=$(ls ${base_path}/tools/expInfo/product_*.json)
    if [ -f "${new_json_path}/product_sop.json" ];then
        product_json_path=${new_json_path}/product_sop.json
    fi
    
    if [ ! -f "${product_json_path}" ];then
        log_echo "ERROR" "The product_json_path=${product_json_path} is not exists."
        exit 1
    fi
    
    decrypt_pod_pwfile "root"
    root_pwd_de=${tmp_pwd_de}
    
    local DVPrimary_IPV4=$(cat /os_config/DV_Config_MergeCluster_Docker.config|grep "^DVPrimary_IPV4=.*"|awk -F'DVPrimary_IPV4=' '{print $2}')
    local DVSecondary_IPV4=$(cat /os_config/DV_Config_MergeCluster_Docker.config|grep "^DVSecondary_IPV4=.*"|awk -F'DVSecondary_IPV4=' '{print $2}')
    
    docker_set_pod_iptables "${DVPrimary_IPV4}" "not_need_pwd" "Primary" "add_pod_iptables" "ossadm" "not_need_pwd" || exit 1
    docker_set_pod_iptables "${DVSecondary_IPV4}" "${root_pwd_de}" "Secondary" "add_pod_iptables" "ossadm" "not_need_pwd" 
    if [ $? -ne 0 ];then
        docker_set_pod_iptables "${DVPrimary_IPV4}" "not_need_pwd" "Primary" "del_pod_iptables" "ossadm" "not_need_pwd" || exit 1
        exit 1
    fi
    
    modify_product_script="/opt/oss/manager/tools/resmgr/modifyproductinfo.sh"
    su - ossadm -c "sh ${modify_product_script} -input ${product_json_path}"
    if [ $? -ne 0 ];then
        docker_set_pod_iptables "${DVPrimary_IPV4}" "not_need_pwd" "Primary" "del_pod_iptables" "ossadm" "not_need_pwd" || exit 1
        docker_set_pod_iptables "${DVSecondary_IPV4}" "${root_pwd_de}" "Secondary" "del_pod_iptables" "ossadm" "not_need_pwd" || exit 1
        log_echo "ERROR" "execute cmd [ su - ossadm -c \"sh ${modify_product_script} -input ${product_json_path}\" ] failed, Fail to modify product info, the file is ${product_json_path}..."
        exit 1
    fi
    
    docker_set_pod_iptables "${DVPrimary_IPV4}" "not_need_pwd" "Primary" "del_pod_iptables" "ossadm" "not_need_pwd" || exit 1
    docker_set_pod_iptables "${DVSecondary_IPV4}" "${root_pwd_de}" "Secondary" "del_pod_iptables" "ossadm" "not_need_pwd" || exit 1
    log_echo "INFO" "docker_modifyproductinfo finish."
    return 0
}

function docker_rollbackproductinfo()
{
    if [ ! -f ${configParametersFile} ];then
        log_echo "ERROR" "The configParametersFile=${configParametersFile} is not exists."
        exit 1
    fi
    source ${configParametersFile}
    
    if [ ! -f /.dockerenv -o "${netWorkType}" != "M" ];then
        log_echo "INFO" "The netWorkType=${netWorkType},not need do it."
        return 0
    fi
    
    product_json_path=$(ls ${base_path}/tools/expInfo/product_*.json)
    if [ -z "${product_json_path}" -o ! -f "${product_json_path}" ];then
        log_echo "INFO" "The product_json_path=${product_json_path} is not exists."
        return 0
    fi
    
    if [ ! -f "${product_json_path}" ];then
        log_echo "ERROR" "The product_json_path=${product_json_path} is not exists."
        exit 1
    fi
    
    decrypt_pod_pwfile "root"
    root_pwd_de=${tmp_pwd_de}
    
    local DVPrimary_IPV4=$(cat /os_config/DV_Config_MergeCluster_Docker.config|grep "^DVPrimary_IPV4=.*"|awk -F'DVPrimary_IPV4=' '{print $2}')
    local DVSecondary_IPV4=$(cat /os_config/DV_Config_MergeCluster_Docker.config|grep "^DVSecondary_IPV4=.*"|awk -F'DVSecondary_IPV4=' '{print $2}')
    
    docker_set_pod_iptables "${DVPrimary_IPV4}" "not_need_pwd" "Primary" "add_pod_iptables" "ossadm" "not_need_pwd" || exit 1
    docker_set_pod_iptables "${DVSecondary_IPV4}" "${root_pwd_de}" "Secondary" "add_pod_iptables" "ossadm" "not_need_pwd" 
    if [ $? -ne 0 ];then
        docker_set_pod_iptables "${DVPrimary_IPV4}" "not_need_pwd" "Primary" "del_pod_iptables" "ossadm" "not_need_pwd" || exit 1
        exit 1
    fi
    
    modify_product_script="/opt/oss/manager/tools/resmgr/modifyproductinfo.sh"
    su - ossadm -c "sh ${modify_product_script} -input ${product_json_path}"
    if [ $? -ne 0 ];then
        docker_set_pod_iptables "${DVPrimary_IPV4}" "not_need_pwd" "Primary" "del_pod_iptables" "ossadm" "not_need_pwd" || exit 1
        docker_set_pod_iptables "${DVSecondary_IPV4}" "${root_pwd_de}" "Secondary" "del_pod_iptables" "ossadm" "not_need_pwd" || exit 1
        log_echo "ERROR" "execute cmd [ su - ossadm -c \"sh ${modify_product_script} -input ${product_json_path}\" ] failed, Fail to modify product info, the file is ${product_json_path}..."
        exit 1
    fi
    
    docker_set_pod_iptables "${DVPrimary_IPV4}" "not_need_pwd" "Primary" "del_pod_iptables" "ossadm" "not_need_pwd" || exit 1
    docker_set_pod_iptables "${DVSecondary_IPV4}" "${root_pwd_de}" "Secondary" "del_pod_iptables" "ossadm" "not_need_pwd" || exit 1
    log_echo "INFO" "docker_modifyproductinfo finish."
    return 0
}

function check_dirs_io()
{
    ## /opt:4:1024,/opt/oss:4:1024
    local check_dir_list="$1"
 
    log_echo "INFO" "Star check_dir_list=${check_dir_list} io..."
 
    if [ "X${check_io}" != "XYes" ];then
        log_echo "INFO" "The check_io=${check_io} not equal Yes.not need to check io."
        return 0
    fi
 
    if [ -z "${check_dir_list}" ];then
        log_echo "ERROR" "check_dir_list=${check_dir_list} is null."
        exit 1
    fi
 
    local check_dir=""
    local check_size=""
    local need_size=""
    local need_check_dir_list=""
    for check_dir_info in $(echo "${check_dir_list}"|sed "s#,# #g");
    do
        check_dir=$(echo "${check_dir_info}"|awk -F':' '{print $1}')
        check_size=$(echo "${check_dir_info}"|awk -F':' '{print $2}')
        need_size=$(echo "${check_dir_info}"|awk -F':' '{print $3}')
        if [ -z "${check_dir}" ];then
            log_echo "INFO" "The check_dir=${check_dir} is null."
            continue
        fi
 
        if [ ! -d ${check_dir} ];then
            log_echo "INFO" "The check_dir=${check_dir} is not exists.of check_dirs_io"
            continue
        fi
 
        mounted_dir=$(df -Pm ${check_dir} |tail -1|awk '{print $NF}' )
        if [ -z "${need_check_dir_list}" ];then
            need_check_dir_list="${mounted_dir}:${check_size}:${need_size}"
            continue
        fi
 
        echo "${need_check_dir_list}" |grep "${mounted_dir}:" >> ${LOG_FILE} 2>&1
        if [ $? -ne 0 ];then
            need_check_dir_list="${need_check_dir_list},${mounted_dir}:${check_size}:${need_size}"
        fi
    done
 
    if [ -z "${need_check_dir_list}" ];then
        log_echo "ERROR" "need_check_dir_list=${need_check_dir_list} is null."
        exit 1
    fi
 
    for check_dir_info in $(echo "${need_check_dir_list}"|sed "s#,# #g");
    do
        check_dir=$(echo "${check_dir_info}"|awk -F':' '{print $1}')
        check_size=$(echo "${check_dir_info}"|awk -F':' '{print $2}')
        need_size=$(echo "${check_dir_info}"|awk -F':' '{print $3}')
        check_io "${check_dir}" "${check_size}" "${need_size}" || exit 1
    done
}

function check_io()
{
    local check_dir="$1"
    local check_size="$2"
    ## need tmp file size unit M.
    local need_size="$3"
 
    log_echo "INFO" "Star check_dir=${check_dir} io..."
 
    if [ -z "${check_dir}" -o -z "${check_size}" -o -z "${need_size}" ];then
        log_echo "ERROR" "check_dir=${check_dir} or check_size=${check_size} or need_size=${need_size} is null."
        return 1
    fi
 
    if [ ! -d ${check_dir} ];then
        log_echo "INFO" "The check_dir=${check_dir} is not exists."
        return 0
    fi
 
    local isNumber=$(echo "${check_size}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        log_echo "ERROR" "The check_size=${check_size} is not number.of check_dir=${check_dir}"
        return 1
    fi
 
    isNumber=$(echo "${need_size}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        log_echo "ERROR" "The need_size=${need_size} is not number.of check_dir=${check_dir}"
        return 1
    fi
 
    local file_name=$(date '+%Y%m%d_%H%M%S').log
    local avail_size=$(df -Pm ${check_dir} |awk -F' ' '{print $4}' |sed "1,1d" |sed 's/M//'|awk -F'.' '{print $1}'|head -n 1)
    local tmp_need_size=$((${need_size} + 2))
    if [ ${avail_size} -lt ${tmp_need_size} ];then
        log_echo "WARN" "The check_dir(${check_dir}) avail size(${avail_size} M) is less than need size(${tmp_need_size} M)."
        return 0
    fi
 
    [ -f ${check_dir}/${file_name} ] && rm -rf ${check_dir}/${file_name}
    [ -f ${check_dir}/check_io.log ] && rm -rf ${check_dir}/check_io.log
    local count_size=$(( 128 * ${need_size}))
    time dd if=/dev/zero of=${check_dir}/${file_name} bs=8k count=${count_size} oflag=direct > ${check_dir}/check_io.log 2>&1
    [ -f ${check_dir}/${file_name} ] && rm -rf ${check_dir}/${file_name}
 
    local io_size=$(cat ${check_dir}/check_io.log |grep -w "copied"|awk -F',' '{print $NF}'|awk -F'/' '{print $1}')
 
    local isOther=$(echo "${io_size}"|grep -i "KB\|M\|G")
    if [ -z "${isOther}" ];then
        log_echo "INFO" "The io_size(${io_size}) is other Unit.not in [KB,M,G].of check_dir=${check_dir}"
        rm -rf ${check_dir}/check_io.log
        return 0
    fi
 
    size_value=0
    tmp_info_KB=$(echo "${io_size}"|grep -i "KB" |awk '{print $1}'|awk -F'.' '{print $1}')
    [ ! -z "${tmp_info_KB}" ] && size_value=$((${tmp_info_KB}/1024))
    tmp_info_M=$(echo "${io_size}"|grep -i "M" |awk '{print $1}'|awk -F'.' '{print $1}')
    [ ! -z "${tmp_info_M}" ] && size_value=${tmp_info_M}
    tmp_info_G=$(echo "${io_size}"|grep -i "G" |awk '{print $1}'|awk -F'.' '{print $1}')
    [ ! -z "${tmp_info_G}" ] && size_value=$((${tmp_info_G}*1024))
 
    local isNumber=$(echo "${size_value}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        rm -rf ${check_dir}/check_io.log
        log_echo "ERROR" "The size_value=${size_value} is not number.of check_dir=${check_dir}"
        return 1
    fi
 
    if [ ${size_value} -lt ${check_size} ];then
        rm -rf ${check_dir}/check_io.log
        log_echo "WARN" "The io_size(${size_value} M) is less than check_size(${check_size} M). of check_dir=${check_dir}"
        return 0
    fi
 
    rm -rf ${check_dir}/check_io.log
    log_echo "INFO" "The check_dir=${check_dir} of io_size(${size_value} M) and check_size(${check_size} M) finished."
    return 0
}

function create_readlink()
{
    log_echo "INFO" "begin to create readlink"
    realpath=$(readlink /etc/localtime | sed 's/\/\//\//g')
    ln -sf $realpath /etc/localtime

    local os_type="$(cat /etc/os-release|grep "EulerOS 2.0 (SP12)")"
    if [ -n "${os_type}" ];then
        ln -sf /lib/ld-linux-aarch64.so.1 /lib64/ld-linux-aarch64.so.1
    fi
}

function check_tzdata()
{
    if [ -f /home/<USER>/update_time.log ];then
        tzdata_file=/opt/tz/tzdata-latest.tar.gz
        if [ ! -f ${tzdata_file} ];then
            log_echo "ERROR" "The daylight saving time has been updated, but ${tzdata_file} is not exit, please check"
            exit 1
        fi
        tzupadte_file=/opt/tz/tzupdater.jar
        if [ ! -f ${tzupadte_file} ];then
            log_echo "ERROR" "The daylight saving time has been updated, ${tzupadte_file} is not exit, please check"
            exit 1
        fi
    fi
}

function adap_profile_for_kylin_enhance()
{
    cat /etc/os-release | grep "Kylin" > /dev/null
    if [ $? -ne 0 ];then
        log "The system is not kylin OS, no need change /etc/profile"
        return 0
    fi
    cat /etc/profile | grep -v "^#" |grep ldconfig
    if [ $? -ne 0 ];then
        log "ldconfig is not exit, skip adap_profile_for_kylin_enhance"
        return 0
    fi
    cat /etc/profile | grep -v "^#" |grep ldconfig | grep /dev/null
    if [ $? -eq 0 ];then
        log "ldconfig has handle, skip adap_profile_for_kylin_enhance"
        return 0
    fi
    sed -i '/^ldconfig.*/d' /etc/profile
    echo "ldconfig -v >/dev/null 2>&1" >> /etc/profile
    return 0
}

