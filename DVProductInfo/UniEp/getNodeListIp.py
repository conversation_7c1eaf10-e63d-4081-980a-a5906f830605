# -*- coding:utf-8 -*-
import os.path
import sys
import time
import json
import stat
import httpclient

script_dir = os.path.dirname(os.path.abspath(__file__))
flags = os.O_RDWR | os.O_CREAT
modes = stat.S_IWUSR | stat.S_IRUSR
log_file = os.fdopen(os.open(os.path.join(script_dir, 'getNodeListIp.log'), flags, modes), 'a+')
httpclient = httpclient.IRHttpClient()

json_file_path = sys.argv[1]
todo_type = sys.argv[2]
if len(sys.argv) <= 3:
    is_all = "false"
else:
    is_all = sys.argv[3]

def log_echo(log_type="INFO", log_str=""):
    time_str = str(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()))
    try:
        if log_file:
            log_file.write("[" + log_type + "][" + time_str + "]:" + log_str + "\n")
    except Exception as e:
        log_echo("ERROR", "write " + str(os.path.join(script_dir, 'getNodeListIp.log')) + " failed: %s" % str(e))
        log_file.close()
        sys.exit(-1)


def get_node_ip(node_map):
    ip_addresses = node_map.get("IPAddresses", False)
    ip_str = ""
    if ip_addresses:
        log_echo("INFO", "IPAddresses is = " + str(ip_addresses))
        for ip_map in ip_addresses:
            log_echo("INFO", "ipMap=" + str(ip_map))
            ## usage  maintenance 
            if "maintenance" in ip_map.get("usage", list()) :
                ip_str = ip_map.get("IP", False)
    if ip_str:
        return ip_str.lower()

    log_echo("ERROR", "get_ip failed.")
    return "ERROR"


def get_ip(node_map):
    extend_cluster_type = ["ExtendCluster", "SMExtend", "OMSAExtend", "SDExtend", "IMExtend"]
    for node_type in node_map.get("type", "").split(","):
        if node_type in extend_cluster_type and is_all == "false":
            return "ExtendCluster"
    
    return get_node_ip(node_map)


def get_nodeip():
    try:
        jsonfile_realpath = os.path.realpath(json_file_path)
        with open(jsonfile_realpath) as f:
            json_load = json.load(f)
    except Exception as e:
        log_echo("ERROR", "Read " + jsonfile_realpath + " failed: %s" % str(e))
        sys.exit(-1)

    log_echo("INFO", "---------------")
    nodeip_list = ""
    if "nodeList" in json_load:
        for key, item in json_load.get("nodeList", dict()).items():
            log_echo("INFO", "get_ip of " + str(key))
            ipaddr = get_ip(item)
            if ipaddr == "ERROR":
                log_echo("ERROR", "Node Ip is null.get ip failed.")
                print("ERROR")
                sys.exit(-1)
            elif ipaddr == "ExtendCluster" and is_all == "false":
                continue         
            nodeip_list = nodeip_list + " " + str(ipaddr)
        if nodeip_list:
            print(nodeip_list)
        else:
            log_echo("ERROR", "Node Ip List is null")
            print("ERROR")
            sys.exit(-1)
    else:
        log_echo("ERROR", "nodeList is null in " + json_file_path)
        log_file.close()
        print("ERROR")
        sys.exit(-1)


def get_all_node_ip():
    try:
        jsonfile_realpath = os.path.realpath(json_file_path)
        with open(jsonfile_realpath) as f:
            json_load = json.load(f)
    except Exception as e:
        log_echo("ERROR", "Read " + jsonfile_realpath + " failed: %s" % str(e))
        sys.exit(-1)

    log_echo("INFO", "---------------")
    all_nodeip_list = ""
    if "nodeList" in json_load:
        for key, item in json_load.get("nodeList", dict()).items():
            log_echo("INFO", "get_ip of " + str(key))
            ipaddr = get_node_ip(item)
            if ipaddr == "ERROR":
                log_echo("ERROR", "Node Ip is null.get ip failed.")
                print("ERROR")
                sys.exit(-1) 
            if all_nodeip_list:
                all_nodeip_list = all_nodeip_list + "," + str(ipaddr)
            else:
                all_nodeip_list = str(ipaddr)
        if all_nodeip_list:
            print(all_nodeip_list)
        else:
            log_echo("ERROR", "Node Ip List is null")
            print("ERROR")
            sys.exit(-1)
    else:
        log_echo("ERROR", "nodeList is null in " + json_file_path)
        log_file.close()
        print("ERROR")
        sys.exit(-1)


def get_manage_status():
    status, response = httpclient.get("/rest/sawwebsite/v1/resource/apps/state?product=manager")
    if status != 200:
        return 1

    response_json = json.loads(response)
    if response_json["data"]["running"] == 1:
        return 0
    return 101


def get_uniep_service_status():
    status, response = httpclient.get("/rest/plat/deployconfigureservice/v1/healthcheck")
    if status != 200:
        return 1
    return 0


def get_backupservice_status():
    status, response = httpclient.get("/rest/backupservice/v1/healthcheck")
    if status != 200:
        return 1
    return 0


def get_brmgr_status():
    status, response = httpclient.get("/rest/plat/brmgr/v1/baseinfo/time")
    if status != 200:
        return 1
    return 0


def get_access_ip():
    try:
        file_realpath = os.path.realpath(json_file_path)
        with open(file_realpath) as f:
            json_load = json.load(f)
    except IOError:
        print("ERROR")
        return 1

    flat_ip = ""

    if "nodeList" in json_load.keys():
        for key in json_load["nodeList"].keys():
            tmp_hostname = json_load["nodeList"][key]["hostname"]
            if tmp_hostname == "DVPrimaryNode" or tmp_hostname == "APP_Primarynode":
                for ipmap in json_load["nodeList"][key]["IPAddresses"]:
                    for item in ipmap["usage"]:
                        if item.lower() == "access":
                            flat_ip = ipmap["IP"]
                        if item.lower().find("access2") != -1:
                            flat_ip = ipmap["IP"]
            if tmp_hostname == "appnode" or tmp_hostname == "ALL_IN_ONE_NODE":
                for ipmap in json_load["nodeList"][key]["IPAddresses"]:
                    for item in ipmap["usage"]:
                        if item.lower() == "maintenance":
                            flat_ip = ipmap["IP"]

        if flat_ip:
            print(flat_ip)
            return 0
        else:
            print("ERROR")
            return 1
    else:
        print("ERROR")
        return 1


if __name__ == '__main__':
    if todo_type == "get_nodeip":
        get_nodeip()
    elif todo_type == "get_access_ip":
        get_access_ip()
    elif todo_type == "get_all_node_ip":
        get_all_node_ip()
    elif todo_type == "get_manage_status":
        ret = get_manage_status()
        sys.exit(ret)
    elif todo_type == "get_uniep_service_status":
        ret = get_uniep_service_status()
        sys.exit(ret)
    elif todo_type == "get_backupservice_status":
        ret = get_backupservice_status()
        sys.exit(ret)
    elif todo_type == "get_brmgr_status":
        ret = get_brmgr_status()
        sys.exit(ret)
    log_file.close()
