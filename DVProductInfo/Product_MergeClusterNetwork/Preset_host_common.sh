#!/bin/bash
set +x

function add_dv_node_to_k8s()
{
    local host_ip="$1"
    local k8s_root_pwd="$2"
    local dv_node_root_pwd="$3"
    local dv_node_paas_pwd="$4"
    local do_type="$5"
    Check_execute "Manage_Host_${host_ip}"
    if [ $? -ne 0 ];then
        get_image_disk "${image_disk}" "${host_ip}" "${dv_node_root_pwd}"
        echo "${k8s_root_pwd} ${dv_node_root_pwd} ${dv_node_paas_pwd}" |sh ${PreSet_PATH}/tools/docker/add_dv_node_to_k8s.sh "${k8s_oper_user}@${K8S_IPV4}" "${tmp_image_disk}" "${RETURN_THINPOOL_SIZE}" "${host_ip}" true "${do_type}"
        if [ $? -ne 0 ];then
            log_echo "INFO" "Manage node ${host_ip} failed, need to del dv node and retry."
            echo "${k8s_root_pwd} ${dv_node_root_pwd}" |sh ${PreSet_PATH}/tools/docker/del_dv_node_to_k8s.sh "${k8s_oper_user}@${K8S_IPV4}" "${host_ip}" true "${do_type}" >>${LOG_FILE} 2>&1
            ## 不需要判断返回值，不影响。
        else
            sed -i "/Manage_Host_${host_ip} /d" ${action_tag}
            echo "Manage_Host_${host_ip} : success" >> ${action_tag}
            return 0
        fi
        
        log_echo "INFO" "Manage node ${host_ip} retry."
        echo "${k8s_root_pwd} ${dv_node_root_pwd} ${dv_node_paas_pwd}" |sh ${PreSet_PATH}/tools/docker/add_dv_node_to_k8s.sh "${k8s_oper_user}@${K8S_IPV4}" "${tmp_image_disk}" "${RETURN_THINPOOL_SIZE}" "${host_ip}" true "${do_type}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Manage node ${host_ip} failed, please check"
            echo "Manage_Host_${host_ip} : failed" >> ${action_tag}
            exit 1
        else
            log_echo "INFO" "Manage node ${host_ip} success."
            sed -i "/Manage_Host_${host_ip} /d" ${action_tag}
            echo "Manage_Host_${host_ip} : success" >> ${action_tag}
        fi
    fi
}

function managed_host()
{
    if [ -z "${K8S_IPV4}" -a  -z "${image_disk}" ];then
        log_echo "INFO" "3rd Caas not need operations related to K8S.Skip it."
        return 0
    fi
    ## 节点纳管 
    add_dv_node_to_k8s "${HostPrimary_IPV4}" "${k8s_root_pwd}" "${DVPrimary_pwd}" "${pass_pwd}" &
    echo "${HostPrimary_IPV4}=$!" >> ${managed_pid_list}
    add_dv_node_to_k8s "${HostSecondary_IPV4}" "${k8s_root_pwd}" "${DVSecondary_pwd}" "${pass_pwd}" &
    echo "${HostSecondary_IPV4}=$!" >> ${managed_pid_list}
    add_dv_node_to_k8s "${HostThird_IPV4}" "${k8s_root_pwd}" "${DVThird_pwd}" "${pass_pwd}" &
    echo "${HostThird_IPV4}=$!" >> ${managed_pid_list}
}

function check_docker()
{
    local execute_type=$1
    local app_node_pwd=$2
    local app_ssh_ip=$3
    
    Check_execute "check_docker_${app_ssh_ip}"
    if [ $? -eq 0 ];then
        return 0
    fi
    
    local isIpv6=$(echo ${app_ssh_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        app_ssh_ip="\[${app_ssh_ip}\]"
    fi
    
    if [ "X${execute_type}" == "Xlocal" ];then
        sh ${PreSet_PATH}/tools/docker/check_docker.sh check_docker
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Execute cmd:[sh ${PreSet_PATH}/tools/docker/check_docker.sh check_docker ] in local node failed, please check..."
            exit 1
        fi
        
        sh ${PreSet_PATH}/tools/docker/check_docker.sh check_hostname
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Execute cmd:[sh ${PreSet_PATH}/tools/docker/check_docker.sh check_hostname ] in local node failed, please check..."
            exit 1
        fi
        
        log_echo "INFO" "check_docker ${app_ssh_ip} success"
        echo "check_docker_${app_ssh_ip} : success" >> ${action_tag}
        return 0
    fi
    
    tmp_dir=${TMP_PATH}/DVPreSetHost
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} mkdir -p ${tmp_dir}"
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} chown -R ${oper_user}: ${tmp_dir}"
    auto_scp_dir ${app_node_pwd} ${PreSet_PATH}/Docker "${oper_user}@${app_ssh_ip}:${tmp_dir}"
    auto_scp ${app_node_pwd} ${PreSet_PATH}/tools/docker/check_docker.sh "${oper_user}@${app_ssh_ip}:${tmp_dir}"
    
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/check_docker.sh check_docker"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ sh ${tmp_dir}/check_docker.sh check_docker ] at node ${app_ssh_ip} failed, please check..."
        exit 1
    fi
    
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/check_docker.sh check_hostname"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ sh ${tmp_dir}/check_docker.sh check_hostname ] at node ${app_ssh_ip} failed, please check..."
        exit 1
    fi
    
    ssh_get_cpu_processor "${app_ssh_ip}" "${app_node_pwd}"
    
    log_echo "INFO" "check_docker ${app_ssh_ip} success"
    echo "check_docker_${app_ssh_ip} : success" >> ${action_tag}
}

function config_pod_max_pids()
{
    local execute_type=$1
    local app_node_pwd=$2
    local app_ssh_ip=$3
    
    Check_execute "config_pod_max_pids_${app_ssh_ip}"
    if [ $? -eq 0 ];then
        return 0
    fi
    
    local isIpv6=$(echo ${app_ssh_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        app_ssh_ip="\[${app_ssh_ip}\]"
    fi
    
    if [ "X${execute_type}" == "Xlocal" ];then
        sh ${PreSet_PATH}/tools/docker/config_pod_max_pids.sh
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Execute cmd:[ sh ${PreSet_PATH}/tools/docker/config_pod_max_pids.sh ] in local node ${app_ssh_ip} failed, please check..."
            exit 1
        fi
        
        echo "config_pod_max_pids_${app_ssh_ip} : success" >> ${action_tag}
        log_echo "INFO" "config_pod_max_pids ${app_ssh_ip} success"
        return 0
    fi
    
    tmp_dir=${TMP_PATH}/DVPreSetHost
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} rm -rf ${tmp_dir:?}"
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} mkdir -p ${tmp_dir}"
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} chown -R ${oper_user}: ${tmp_dir}"

    auto_scp ${app_node_pwd} ${PreSet_PATH}/tools/docker/config_pod_max_pids.sh "${oper_user}@${app_ssh_ip}:${tmp_dir}"
    
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/config_pod_max_pids.sh"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ sh ${tmp_dir}/config_pod_max_pids.sh ] at node ${app_ssh_ip} failed, please check..."
        exit 1
    fi
    echo "config_pod_max_pids_${app_ssh_ip} : success" >> ${action_tag}
    log_echo "INFO" "config_pod_max_pids ${app_ssh_ip} success"
}

function upload_package_to_podnode()
{
    ## 拷贝预置包和产品包到pod node节点上。
    log_echo "INFO" "Begin to execute upload_package_to_podnode..."
    preset_dir="${OPT_PATH}/dv/DVZIPConfig"
    mkdir -p ${preset_dir}
    chmod 755 ${preset_dir}
    [ -f ${PreSet_PATH}/UniEp/pwfile.json ] && rm -rf ${PreSet_PATH:?}/UniEp/pwfile.json
    [ -f ${PreSet_PATH}/Common/pwfile.json ] && rm -rf ${PreSet_PATH:?}/Common/pwfile.json
    [ -f ${dv_cfg_file}.tmp ] && rm -rf ${dv_cfg_file:?}.tmp
    [ -f ${ext_disk_properties}.tmp ] && rm -rf ${ext_disk_properties:?}.tmp
    [ -f ${PreSet_PATH}/install_action.tag ] && mv ${PreSet_PATH}/install_action.tag ${PreSet_PATH}/install_action.tag.docker
    [ -f ${PreSet_PATH}/preset_script.log ] && mv ${PreSet_PATH}/preset_script.log ${PreSet_PATH}/preset_script.log.docker
    [ -f ${PreSet_PATH}/Common/manifest.json ] && mv ${PreSet_PATH}/Common/manifest.json ${PreSet_PATH}/Common/manifest.json.docker
    
    cp -rp ${PreSet_PATH}/* ${preset_dir}

    cputype=$(uname -i)
     if [ "X${cputype}" == "Xaarch64" ]; then
         mv ${preset_dir}/UniEp/python/bin/pythonOld ${preset_dir}/UniEp/python/bin/python
     fi

    [ -d ${preset_dir}/Docker ] && rm -rf ${preset_dir:?}/Docker
    [ -d ${preset_dir}/python_lib ] && rm -rf ${preset_dir:?}/python_lib
    [ -f ${preset_dir}/Common/pwfile.json ] && rm -rf ${preset_dir:?}/Common/pwfile.json
    [ -f ${preset_dir}/DV_BatchPreSet_Docker.sh ] && rm -rf ${preset_dir:?}/DV_BatchPreSet_Docker.sh
    [ -f ${preset_dir}/DV_Preset_Cleanup_host.sh ] && rm -rf ${preset_dir:?}/DV_Preset_Cleanup_host.sh
    [ -f ${preset_dir}/Product_MergeClusterNetwork/Preset_host.sh ] && rm -rf ${preset_dir:?}/Product_MergeClusterNetwork/Preset_host.sh
    [ -f ${preset_dir}/Product_MergeClusterNetwork/Preset_check_before_host.sh ] && rm -rf ${preset_dir:?}/Product_MergeClusterNetwork/Preset_check_before_host.sh
    [ -f ${preset_dir}/Product_MergeClusterNetwork/Preset_rollback_host.sh ] && rm -rf ${preset_dir:?}/Product_MergeClusterNetwork/Preset_rollback_host.sh
    [ -f ${preset_dir}/DV_Config_MergeCluster_Docker.config ] && rm -rf ${preset_dir:?}/DV_Config_MergeCluster_Docker.config
    [ -f ${PreSet_PATH}/install_action.tag.docker ] && mv ${PreSet_PATH}/install_action.tag.docker ${PreSet_PATH}/install_action.tag
    [ -f ${PreSet_PATH}/preset_script.log.docker ] && mv ${PreSet_PATH}/preset_script.log.docker ${PreSet_PATH}/preset_script.log
    [ -f ${PreSet_PATH}/Common/manifest.json.docker ] && mv ${PreSet_PATH}/Common/manifest.json.docker ${PreSet_PATH}/Common/manifest.json

    ## 还原宿主机自定义路径产生的配置
    local PreSet_PATH_TMP="${PreSet_PATH}"
    local dv_cfg_file_tmp="${dv_cfg_file}"
    PreSet_PATH="${preset_dir}"
    dv_cfg_file="${preset_dir}/Common/DV_config.properties"
    update_config "${dv_cfg_file}" "custom_path_list" "\"${OPT_CUSTOM_PATH},${HOME_CUSTOM_PATH},${TMP_CUSTOM_PATH}\"" || exit 1
    update_config "${dv_cfg_file}" "HOME_CUSTOM_PATH" " " || exit 1
    update_config "${dv_cfg_file}" "OPT_CUSTOM_PATH" " " || exit 1
    update_config "${dv_cfg_file}" "TMP_CUSTOM_PATH" " " || exit 1
    restoring_the_default_configuration
    PreSet_PATH="${PreSet_PATH_TMP}"
    dv_cfg_file="${dv_cfg_file_tmp}"
    [ -f ${preset_dir}/Common/netWorkTypeOfDvConfigParameters.config ] && sed -i "/^CUSTOM_PATH_SCENE=.*/d" ${preset_dir}/Common/netWorkTypeOfDvConfigParameters.config

    ## pod 里面不需要自动挂盘，需要把 AUTO_DISK_MOUNT 修改为 FALSE
    update_config "${preset_dir}/DV_Config_MergeCluster.config" "AUTO_DISK_MOUNT" "FALSE" || exit 1

    ## pod 里面使用root安裝，需要把 SUDO_USER 修改为空
    update_config "${preset_dir}/DV_Config_MergeCluster.config" "SUDO_USER" " "

    if [ "X${AUTO_SCAN_UPLOAD_SOFTWARE_PACKAGE}" == "XTRUE" -a "X${SOFTWARE_PACKAGE_PATH}" != "X" ];then
        scan_softpackage_dir="${OPT_PATH}/dv/DVZIPConfig/scan_softpackage"
        if [ -d "${scan_softpackage_dir}" ];then
            rm -rf ${scan_softpackage_dir:?}
        fi
        mkdir -p ${scan_softpackage_dir}
        
        check_copy_package_to_pod "${SOFTWARE_PACKAGE_PATH}" "${scan_softpackage_dir}" || exit 1
        
        update_config "${preset_dir}/DV_Config_MergeCluster.config" "SOFTWARE_PACKAGE_PATH" "/opt/DVZIPConfig/scan_softpackage" || exit 1
    fi
}


function execute_pod_preset()
{
    log_echo "INFO" "Begin to do execute_pod_preset..."
    Check_execute "pod_preset"
    if [ $? -ne 0 ];then
        pod_id=$(docker ps |grep "dv-container" |awk '{print $1}')
        if [ -z "${pod_id}" ];then
            log_echo "INFO" "Failed to obtain pod_id. Please wait a moment."
            local count=1
            while [ ${count} -lt 3 ]
            do
                log_echo "INFO" "wait ${count} times"
                sleep 10
                pod_id=$(docker ps |grep "dv-container" |awk '{print $1}')
                [ "X${pod_id}" != "X" ] && break
                count=$((count+1))
            done
            if [ -z "${pod_id}" ];then
                log_echo "ERROR" "Execute cmd:[ docker ps |grep dv-container |awk '{print \$1}' ] get pod id failed, get pod id is null. please check..."
                exit 1
            fi
        fi
        if [ "X${dv_force_change_os_pwd}" == "XYes" ];then
            all_passwd=$(echo "${all_passwd}" |awk -F " " '{for (i=2;i<=NF;i++)printf("%s ", $i);print ""}')
            all_passwd="root:${new_root_pwd} ${all_passwd}"
        fi
        sed -i "/pod_preset /d" ${action_tag}
        echo "${all_passwd}" | docker exec -i ${pod_id} /bin/bash /opt/DVZIPConfig/PreInstall/DV_Pre_BatchSet.sh M
        if [ $? -ne 0 ];then
            echo "pod_preset : failed" >> ${action_tag}
            log_echo "ERROR" "Execute cmd:[ docker exec -i ${pod_id} /bin/bash /opt/DVZIPConfig/PreInstall/DV_Pre_BatchSet.sh M ] failed, please check..."
            exit 1
        fi
        
        echo "pod_preset : success" >> ${action_tag}
    else
        log_echo "INFO" "The pod preset has been executed,skip it."
    fi
    
    if [ "X${AUTO_SCAN_UPLOAD_SOFTWARE_PACKAGE}" == "XTRUE" -a "X${SOFTWARE_PACKAGE_PATH}" != "X" -a -d "/opt/dv/DVZIPConfig/scan_softpackage" ];then
        rm -rf /opt/dv/DVZIPConfig/scan_softpackage
        log_echo "INFO" "clear dv pkg success."
    fi
    
    log_echo "INFO" "Preset pod successfully."
}

function create_docker_pwfile()
{
    log_echo "INFO" "Begin to create docker_pwfile."
    local docker_pwfile=""
    for user_pwd in ${all_passwd};do 
        user_name=$(echo ${user_pwd} |awk -F":" '{print $1}')
        pwd_value=$(echo ${user_pwd} |awk -F":" '{print $2}')
        encrypt_password ${pwd_value}
        pwd_value_en=${encryptPasswd}
        if [ -z "${docker_pwfile}" ];then
            docker_pwfile="{\"name\":\"${user_name}\",\"value\":\"${pwd_value_en}\",\"encrypt\":\"yes\"}"
        else
            docker_pwfile="${docker_pwfile},{\"name\":\"${user_name}\",\"value\":\"${pwd_value_en}\",\"encrypt\":\"yes\"}"
        fi
    done
    echo "[${docker_pwfile}]" > ${PreSet_PATH}/Common/docker_pwfile.json
    chmod 644 ${PreSet_PATH}/Common/docker_pwfile.json
}

function decrypt_docker_pwfile()
{
    local docker_pwfile=${PreSet_PATH}/Common/docker_pwfile.json
    for line in $(cat ${all_passwd_file}.tmp)
    do
        if [ -z "${line}" ];then
            continue
        fi
        user_name=$(echo "${line}"|awk -F":" '{print $1}')
        user_pwd_encrypt=$(cat ${docker_pwfile}| sed 's/}/\n/g' | grep -w "${user_name}" | awk -F : '{print $3}')
        if [ -z "${user_pwd_encrypt}" ];then
            continue
        fi
        user_pwd_encrypt=${user_pwd_encrypt#*\"}
        user_pwd_encrypt=${user_pwd_encrypt%%\"*}
        decrypt_password ${user_pwd_encrypt}
        user_pwd_de=${decryptPasswd}
        all_passwd="${all_passwd} ${user_name}:${user_pwd_de}"
    done
    
    local need_update=0
    if [ "X${DV_TOOLKIT_INSTALL}" == "XYes" -a -z "$(cat ${docker_pwfile}|grep -w "tool_dms")" ];then
        enter_password "Please input tool_dms password" "db" "plain"
        if [ $? -eq 0 ];then
            tool_dms_pwd=${de_pwd}
            all_passwd="${all_passwd} tool_dms:${tool_dms_pwd}"
        fi
        enter_password "Please input tool_das password" "db" "plain"
        if [ $? -eq 0 ];then
            tool_das_pwd=${de_pwd}
            all_passwd="${all_passwd} tool_das:${tool_das_pwd}"
        fi
        enter_password "Please input tool_easyms password" "db" "plain"
        if [ $? -eq 0 ];then
            tool_easyms_pwd=${de_pwd}
            all_passwd="${all_passwd} tool_easyms:${tool_easyms_pwd}"
        fi
        enter_password "Please input dms_db password" "db" "plain"
        if [ $? -eq 0 ];then
            dms_db_pwd=${de_pwd}
            all_passwd="${all_passwd} dms_db:${dms_db_pwd}"
        fi
        enter_password "Please input easyms_db password" "db" "plain"
        if [ $? -eq 0 ];then
            easyms_db_pwd=${de_pwd}
            all_passwd="${all_passwd} easyms_db:${easyms_db_pwd}"
        fi
        need_update=1
    fi
    
    local flag=0
    for line in $(cat ${all_passwd_file}.tmp)
    do
        if [ -z "${line}" ];then
            continue
        fi
        key=$(echo "${line}"|awk -F":" '{print $1}')
        value=$(echo ${all_passwd} | sed "s/ /\n/g" | awk -F"^${key}:" '{print $2}' | tr -s '\n' | tail -1)
        if [ "X${value}" == "X" ];then
            log_echo "ERROR" "Please confirm whether you have entered ${key} and it's password."
            flag=1
        fi
    done
    [ ${flag} -eq 1 ] && exit 1
    
    if [ ${need_update} -eq 1 ];then
        create_docker_pwfile
    fi
}

function add_host_iptables_lvs()
{
    local app_ssh_ip="$1"
    local app_scp_ip="$1"
    local remote_root_passwd="$2"

    if [ -f "/.dockerenv" ];then
        return 0
    fi

    Check_execute "add_host_iptables_lvs_${app_ssh_ip}"
    if [ $? -eq 0 ];then
        return 0
    fi

    tmp_dir=${TMP_PATH}/DVPreSetHost
    i2k_file_list="DV_config.properties.tmp netWorkTypeOfDvConfigParameters.config utils_common.sh utils_uniep.sh utils_os.sh DV_PreSet_APP.sh"

    if [ "X$2" == "Xlocal" ];then
        mkdir -p ${tmp_dir}
        for i2k_filename in ${i2k_file_list}
        do
            cp -r ${PreSet_PATH}/Common/${i2k_filename} ${tmp_dir}
        done

        sh ${tmp_dir}/DV_PreSet_APP.sh "add_host_iptables_lvs"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Execute cmd:[ sh ${tmp_dir}/DV_PreSet_APP.sh add_host_iptables_lvs ] at node ${app_ssh_ip} failed, please check..."
            exit 1
        fi
        echo "add_host_iptables_lvs_${app_ssh_ip} : success" >> ${action_tag}
        return 0
    fi

    local isIpv6=$(echo ${app_scp_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        app_scp_ip="\[${app_scp_ip}\]"
    fi

    if [ -z "${remote_root_passwd}" ];then
        log_echo "ERROR" "The root password for ${app_ssh_ip} is null, please check"
        exit 1
    fi

    ## ssh node
    auto_smart_ssh ${remote_root_passwd} "root@${app_ssh_ip} mkdir -p ${tmp_dir}"
    for i2k_filename in ${i2k_file_list}
    do
        auto_scp ${remote_root_passwd} ${PreSet_PATH}/Common/${i2k_filename} "${oper_user}@${app_scp_ip}:${tmp_dir}"
    done

    auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh add_host_iptables_lvs"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ sh ${tmp_dir}/DV_PreSet_APP.sh add_host_iptables_lvs ] at node ${app_ssh_ip} failed, please check..."
        exit 1
    fi

    log_echo "INFO" "add_host_iptables_lvs in node ${app_ssh_ip} successfully."
    echo "add_host_iptables_lvs_${app_ssh_ip} : success" >> ${action_tag}
    return 0
}

function check_and_add_node_to_k8s()
{
    local node_ip=$1
    local node_pwd=$2
    log_echo "INFO" "check_and_add_node_to_k8s start..."
    check_docker "ssh" "${node_pwd}" "${node_ip}" 
    add_dv_node_to_k8s "${node_ip}" "${k8s_root_pwd}" "${node_pwd}" "${pass_pwd}"  "extend"
    config_pod_max_pids "ssh" "${node_pwd}" "${node_ip}" 
    log_echo "INFO" "check_and_add_node_to_k8s End"
}


