#!/bin/bash

function local_is_uniep()
{
    local uniep_ip="$1"
    if [ -z "${uniep_ip}" ];then
        log_echo "ERROR" "The uniep_ip=${uniep_ip} is null."
        exit 1
    fi
    
    local uniep_ip_tmp=$(echo "${uniep_ip}" |sed "s/\./\\\./g")
    ip addr |grep -w ${uniep_ip_tmp} >/dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "We need run this at UniEp node, please check it is on the ${uniep_ip} node."
        exit 1
    fi
}

function check_os_version()
{
    log_echo "INFO" "Begin to check os version"
    #TanZu Photon OS relate to Suse.
    cat /etc/os-release | grep -E "SUSE|Photon" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        cat /etc/os-release | grep -E "Server 12 SP5|VERSION_ID=3.0" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Only support 12 SP5 for SuSe, please check"
            exit 1
        fi
    fi
    #麒麟V10SP2、SP3也支持
    cat /etc/os-release | grep -E "Kylin" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        cat /etc/os-release | grep -E "Server V10">/dev/null 2>&1
        if [ $? -ne 0 ];then
          log_echo "ERROR" "Only support V10SP2、SP3 for Kylin, please check"
          exit 1
        fi
    fi
    log_echo "INFO" "Begin to check os version finished."
}

function check_list_ip()
{
    local param_key="$1"
    local param_value="$2"
    if [ -z "${param_key}" ];then
        log_echo "ERROR" "The param_key=${param_key} is null.please check."
        exit 1
    fi
    
    if [ -z "${param_value}" ];then
        log_echo "ERROR" "The param_value=${param_value} is null.please check."
        exit 1
    fi
    
    local rest=0
    for ip in $(echo "${param_value}" |sed "s/,/ /g");
    do
        check_ip "${ip}"
        rest=$(( ${rest} + $? ))
        
        check_ip_type "${ip}" "0" 
        rest=$(( ${rest} + $? ))
    done
    
    if [ ${rest} -ne 0 ];then
        log_echo "ERROR" "The ${param_key}=${param_value} has error ip.please check."
        exit 1
    fi
    
    log_echo "INFO" "The param_key=${param_key} and param_value=${param_value} check finished."
    return 0
}

function check_preset_dir()
{
    local preset_dir="$1"
    if [ -z "${preset_dir}" ];then
        log_echo "ERROR" "The preset_dir=${preset_dir} is null.please check."
        exit 1
    fi
    
    if [ "X${AUTO_DISK_MOUNT}" != "XTRUE" -o -f ${ATAE_extend_script} ];then
        log_echo "INFO" "The AUTO_DISK_MOUNT=${AUTO_DISK_MOUNT} not need to check_uniep_preset_dir."
        return 0
    fi
    
    ## 补充检查执行预置包解压所在路径是不是挂盘路径。如果是，要报错。
    local path_list=""
    if [ ! -z "${UNIEP_PUB_DISK}" ];then
        path_list=" ${path_list} ${OPT_PATH}/pub"
    fi
    
    if [ ! -z "${VSINDEX_DISK}" ];then
        path_list=" ${path_list} ${HOME_PATH}/vsindex"
    fi
    
    if [ ! -z "${HA_DISK}" ];then
        path_list=" ${path_list} ${OPT_PATH}/share"
    fi
    
    if [ ! -z "${DBNode_DISK}" ];then
        path_list=" ${path_list} ${OPT_PATH}/zenith"
    fi
    
    if [ ! -z "${DBNode_BACKUP_TMP_DISK}" ];then
        path_list=" ${path_list} ${OPT_PATH}/oss/backuptmp"
    fi

    if [ ! -z "${HOFS_DISK}" ];then
        path_list=" ${path_list} ${OPT_PATH}/oss/hofs"
    fi

    if [ -z "${path_list}" ];then
        log_echo "INFO" "The path_list=${path_list} is null.not need to check_uniep_preset_dir."
        return 0
    fi
    
    ## ${preset_dir}
    for path_dir in ${path_list};do
        if [ ! -d ${path_dir} ];then
            log_echo "INFO" "The path_dir=${path_dir} is not exists.not need to check."
            continue
        fi
        
        ${lsof_cmd} +D ${path_dir} |grep -v grep |grep -w "${preset_dir}" 
        if [ $? -eq 0 ];then
            log_echo "ERROR" "The preset_dir=${preset_dir} runing in the auto mount disk path_dir=${path_dir}. please check."
            exit 1
        fi
    done
    
    log_echo "INFO" "check_preset_dir check finished."
    return 0
}

function check_and_config_ntp()
{
    log_echo "INFO" "check_and_config_ntp start."
    # HostSecondary_IPV4  HostThird_IPV4
    systemctl status ita-ntpsmart | grep -wF "ITA NTPSmart time synchronization service" >> $LOG_FILE 2>&1
    if [ $? -eq 0 ];then
        if [ -z "${NTP_server_IP}" ];then
            log_echo "INFO" "check_and_config_ntp to local node."
            config_ita_ntpsmart_service "${HostPrimary_IPV4}"
            sed -i "s/^NTP_server_IP=.*/NTP_server_IP=${HostPrimary_IPV4}/g" ${PreSet_PATH}/DV_Config_MergeCluster_Docker.config
            source ${PreSet_PATH}/DV_Config_MergeCluster_Docker.config
        else
            check_itp_ntpsmart_time_consistent "${NTP_server_IP}"
            config_ita_ntpsmart_service "${NTP_server_IP}"
        fi
        log_echo "INFO" "check_and_config_ntp finished."
        return 0
    fi

    local server_number=0
    for ip in $(echo "${NTP_server_IP}"|sed "s/,/ /g");
    do
        (( server_number++ ))
    done
    if [ "X${NTP_server_IP}" != "X" -a $server_number -gt 1 ];then
        log_echo "ERROR" "There have no ita_ntpsmart_service, so only one ip can be configured to NTP_server_IP. There are ${server_number} now. "
        exit 1
    fi

    if [ -z "${NTP_server_IP}" ];then
        log_echo "INFO" "check_and_config_ntp to local node."
        config_ntp_service "${HostPrimary_IPV4}"
        sed -i "s/^NTP_server_IP=.*/NTP_server_IP=${HostPrimary_IPV4}/g" ${PreSet_PATH}/DV_Config_MergeCluster_Docker.config
        source ${PreSet_PATH}/DV_Config_MergeCluster_Docker.config
    fi
    
    ip addr |grep -wF "${NTP_server_IP}" >> $LOG_FILE 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "config_ntp_service start."
        config_ntp_service "${NTP_server_IP}"
        log_echo "INFO" "config_ntp_service end."
    else
        /usr/sbin/sntp -S ${NTP_server_IP}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "check ntp server ip ${NTP_server_IP} status failed.please check:[ /usr/sbin/sntp -S ${NTP_server_IP} ]"
            exit 1
        fi
    fi
    
    log_echo "INFO" "check_and_config_ntp finished."
}

function init_config()
{
    # update os page size
    check_os_page_size
    sed -i "s#{{kernelpagesize}}#${kernelpagesize}#g" ${dv_cfg_file}.tmp
}



