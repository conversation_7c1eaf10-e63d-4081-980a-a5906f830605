{"hostlist": [{"nodemgrip": "{{IPType_OF_SM_NODE_IP}}", "nodetype": ["SM", "APP"], "nodename": "{{SMClusterNode}}", "installpath": "{{INSTALL_PATH}}", "nodeext": {"subsystem": "BASE,PM,DS,FARS,ITF,CME,CORE,CM,FM", "nodetag": "SOP-OM-Global-BUS,SOP-OM-Global-D<PERSON>larm,SOP-OM-Global-DVBigScreen,SOP-OM-Global-DVEamService,SOP-OM-Global-DVSolution,SOP-OM-Global-DVES,SOP-OM-CLOUDSOPA-NBI-Console,SOP-OM-CLOUDSOPA-NBI,SOP-OM-Global-APIGateWayLB,SOP-OM-Global-DVDashBoard,SOP-OM-Global-ZK,SOP-OM-Global-DVZookeeper,SOP-OM-Global-ETCD,SOP-OM-CLOUDSOPA-DIS,SOP-OM-CLOUDSOPA-DISMUL,SOP-OM-Global-APIGateWay,SOP-OM-Global-DriverFramework,SOP-OM-Global-ResourceMgmt,SOP-OM-Global-ResourceMgmt-Console,SOP-OM-Global-Platform,SOP-OM-Global-DriverMgmt,SOP-OM-Global-DriverMgmt-Console,SOP-OM-Global-AlarmMonitor,SOP-OM-Global-AlarmMonitor-Console,SOP-OM-CLOUDSOPA-BASE,SOP-OM-Global-Platform-Console,SOP-OM-Global-SC,SOP-OM-Global-UAinfo,SOP-OM-Global-DVCommonComponent,SOP-OM-Global-DVPMService,SOP-OM-Global-EtcdService,SOP-OM-Global-SnbCertMgmt,SOP-OM-Global-SnbCertMgmt-Console,SOP-OM-Global-Seceye-AgentService,SOP-HiSecLiteCA-SignService,SOP-HiSecLiteCA-SignWebsite,SOP-HiSecLiteCA-AccessService,SOP-OM-Global-HOFS-HOFSOsdFileAgent,SOP-OM-Global-DVNBI", "is_support_dynamicbackup": "no", "dbsubsysname": "{{DB_INSTANCENAME}}", "dbinstname": "{{DB_INSTANCENAME}}", "is_product_installed": "no", "zoneName": "service"}, "uvalue": "ossadm", "pvalue": ""}, {"nodemgrip": "{{IPType_OF_OMSA_NODE_IP}}", "nodetype": ["OMSA", "APP"], "nodename": "{{OMSAClusterNode}}", "installpath": "{{INSTALL_PATH}}", "nodeext": {"subsystem": "BASE,PM,DS,FARS,ITF,CME,CORE,CM,FM", "nodetag": "SOP-OM-Global-DV<PERSON><PERSON><PERSON><PERSON><PERSON>eeper,SOP-OM-Global-DVFoundation,SOP-OM-Global-DVOMA,SOP-OM-Global-DVNats,SOP-OM-Global-DVController,SOP-OM-Global-DVWorkbench,SOP-OM-Global-DVWorkbeans,SOP-OM-Global-DVIhealing,SOP-OM-Global-DVInfocollect,SOP-OM-Global-DVLogmatrix,SOP-OM-Global-DVLogtrace,SOP-OM-Global-DVCallchain,SOP-OM-Global-DVCat,SOP-OM-Global-DVKerberos,SOP-OM-Global-BUS,SOP-OM-Global-DVCommonComponent,SOP-OM-Global-Seceye-AgentService,SOP-OM-Global-HOFS-HOFSOsdFileAgent", "is_support_dynamicbackup": "no", "dbsubsysname": "{{DB_INSTANCENAME}}", "dbinstname": "{{DB_INSTANCENAME}}", "is_product_installed": "no", "zoneName": "service"}, "uvalue": "ossadm", "pvalue": ""}, {"nodemgrip": "{{IPType_OF_Flume_NODE_IP}}", "nodetype": ["FLUME", "APP"], "nodename": "{{FlumeClusterNode}}", "installpath": "{{INSTALL_PATH}}", "nodeext": {"subsystem": "BASE,PM,DS,FARS,ITF,CME,CORE,CM,FM", "nodetag": "SOP-OM-Global-DVLogmatrixStreaming,SOP-Logmatrix-Kafka-MQ,SOP-OM-Global-MQAgent,SOP-OM-Global-DVKafka,SOP-OM-Global-BUS,SOP-OM-Global-DVCommonComponent,SOP-OM-Global-Seceye-AgentService,SOP-OM-Global-HOFS-HOFSOsdFileAgent", "is_support_dynamicbackup": "no", "dbsubsysname": "{{DB_INSTANCENAME}}", "dbinstname": "{{DB_INSTANCENAME}}", "is_product_installed": "no", "zoneName": "service"}, "uvalue": "ossadm", "pvalue": ""}, {"nodemgrip": "{{IPType_OF_VS_NODE_IP}}", "nodetype": ["VS", "APP"], "nodename": "{{VSClusterNode}}", "installpath": "{{INSTALL_PATH}}", "nodeext": {"subsystem": "BASE,PM,DS,FARS,ITF,CME,CORE,CM,FM", "nodetag": "SOP-OM-Global-DVElasticsearch,SOP-OM-Global-BUS,SOP-OM-Global-DVCommonComponent,SOP-OM-Global-Seceye-AgentService,SOP-OM-Global-HOFS-HOFSOsdFileAgent", "is_support_dynamicbackup": "no", "dbsubsysname": "{{DB_INSTANCENAME}}", "dbinstname": "{{DB_INSTANCENAME}}", "is_product_installed": "no", "zoneName": "service"}, "uvalue": "ossadm", "pvalue": ""}], "productname": "SOP"}