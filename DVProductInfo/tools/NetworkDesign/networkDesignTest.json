{"sign": "657d9043589942ed155f666a1232128a20e1be9c1eea3c59570963daeb900074", "cfg": {"common": {"name": "Software NetWork", "product": "SoftwareOSS", "version": "", "vendor": "HUAWEI", "condition": ""}, "basic_info": {"os_arch": "arm", "solution_type": "DigitalView", "solution_version": "V800R001C02(Euler)", "solution_name": "SoftwareOSS"}, "az_list": [{"az_id": "", "az_name": "", "data_phase_type": "new", "vnfs": [{"common": {"name": "DV_UniEP", "product": "DigitalView", "version": "V800R001C02(Euler)", "vendor": "HUAWEI", "condition": ""}, "basic_info": {"physical_ne_type": "DV_UniEP", "vnf_type_vnfd": "DV_UniEP", "vnfd_version": "V800R001C02(Euler)", "location_id": "", "dc": "", "region_id": "", "compute_res_info": [{"ha": " Srv_HA_DV"}], "vdc": "Src_VDC_DV", "vpc": " Src_VPC_DV", "storage_res_info": [{"volume_type_name": "VolumeService01"}], "me_id": "", "site_name": "", "abbreviated_site_name": "", "solution_type": "DigitalView", "solution_version": "V800R001C02(Euler)", "solution_name": "SoftwareOSS"}, "account": [{"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:ossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{uniep_ip}}", "login_type": "SSH", "account": "ossuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:dbuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{uniep_ip}}", "login_type": "SSH", "account": "dbuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:ossadm", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{uniep_ip}}", "login_type": "SSH", "account": "ossadm", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sshossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{uniep_ip}}", "login_type": "SSH", "account": "ssh<PERSON><PERSON>", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sftpossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{uniep_ip}}", "login_type": "SFTP", "account": "sftpossuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sysomc", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{uniep_ip}}", "login_type": "SSH", "account": "sysomc", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sopuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{uniep_ip}}", "login_type": "SSH", "account": "sopuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:dv<PERSON><PERSON>er", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{uniep_ip}}", "login_type": "SSH", "account": "dv<PERSON><PERSON><PERSON>", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:devdata", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{uniep_ip}}", "login_type": "SSH", "account": "devdata", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}], "vm": [{"vm_name": "UniEPNode", "vm_type": "UniEP", "work_mode": "LB", "compute_az_name": "", "storage_az_name": "", "ha": "Srv_HA_DV", "vm_pcpu": "8", "vm_vcpu": "8", "vm_memory": "32", "memory_unit": "GB", "vm_storage": "100", "vm_redundancy": "1", "storage_unit": "GB", "vm_priority": "1", "vm_iops_loading": "1000", "vm_iops_running": "1000"}], "ntp": [{"ip_version": "IPv4", "local_ip": "{{uniep_ip}}", "ntp_server": [{"ip": "", "peer_port": "", "peer_protocol": ""}]}]}, {"common": {"name": "DV_APP", "product": "DigitalView", "version": "V800R001C02(Euler)", "vendor": "HUAWEI", "condition": ""}, "basic_info": {"physical_ne_type": "DV_APP", "vnf_type_vnfd": "DV_APP", "vnfd_version": "V800R001C02(Euler)", "location_id": "", "dc": "", "region_id": "", "compute_res_info": [{"ha": " Srv_HA_DV"}], "vdc": "Src_VDC_DV", "vpc": " Src_VPC_DV", "storage_res_info": [{"volume_type_name": "VolumeService01"}], "me_id": "", "site_name": "", "abbreviated_site_name": "", "solution_type": "DigitalView", "solution_version": "V800R001C02(Euler)", "solution_name": "SoftwareOSS"}, "account": [{"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:ossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{app_ip}}", "login_type": "SSH", "account": "ossuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:dbuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{app_ip}}", "login_type": "SSH", "account": "dbuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:ossadm", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{app_ip}}", "login_type": "SSH", "account": "ossadm", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sshossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{app_ip}}", "login_type": "SSH", "account": "ssh<PERSON><PERSON>", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sftpossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{app_ip}}", "login_type": "SFTP", "account": "sftpossuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sysomc", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{app_ip}}", "login_type": "SSH", "account": "sysomc", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sopuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{app_ip}}", "login_type": "SSH", "account": "sopuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:dv<PERSON><PERSON>er", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{app_ip}}", "login_type": "SSH", "account": "dv<PERSON><PERSON><PERSON>", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:devdata", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{app_ip}}", "login_type": "SSH", "account": "devdata", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}], "vm": [{"vm_name": "appnode", "vm_type": "APP", "work_mode": "LB", "compute_az_name": "", "storage_az_name": "", "ha": "Srv_HA_DV", "vm_pcpu": "8", "vm_vcpu": "8", "vm_memory": "32", "memory_unit": "GB", "vm_storage": "100", "vm_redundancy": "1", "storage_unit": "GB", "vm_priority": "1", "vm_iops_loading": "1000", "vm_iops_running": "1000"}], "ntp": [{"ip_version": "IPv4", "local_ip": "", "ntp_server": [{"ip": "", "peer_port": "", "peer_protocol": ""}]}]}, {"common": {"name": "DV_DB", "product": "DigitalView", "version": "V800R001C02(Euler)", "vendor": "HUAWEI", "condition": ""}, "basic_info": {"physical_ne_type": "DV_DB", "vnf_type_vnfd": "DV_DB", "vnfd_version": "V800R001C02(Euler)", "location_id": "", "dc": "", "region_id": "", "compute_res_info": [{"ha": " Srv_HA_DV"}], "vdc": "Src_VDC_DV", "vpc": " Src_VPC_DV", "storage_res_info": [{"volume_type_name": "VolumeService01"}], "me_id": "", "site_name": "", "abbreviated_site_name": "", "solution_type": "DigitalView", "solution_version": "V800R001C02(Euler)", "solution_name": "SoftwareOSS"}, "account": [{"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:ossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{db_ip}}", "login_type": "SSH", "account": "ossuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:dbuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{db_ip}}", "login_type": "SSH", "account": "dbuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:ossadm", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{db_ip}}", "login_type": "SSH", "account": "ossadm", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sshossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{db_ip}}", "login_type": "SSH", "account": "ssh<PERSON><PERSON>", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sftpossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{db_ip}}", "login_type": "SFTP", "account": "sftpossuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sysomc", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{db_ip}}", "login_type": "SSH", "account": "sysomc", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sopuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{db_ip}}", "login_type": "SSH", "account": "sopuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:dv<PERSON><PERSON>er", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{db_ip}}", "login_type": "SSH", "account": "dv<PERSON><PERSON><PERSON>", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:devdata", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{db_ip}}", "login_type": "SSH", "account": "devdata", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}], "vm": [{"vm_name": "dbnode", "vm_type": "DB", "work_mode": "LB", "compute_az_name": "", "storage_az_name": "", "ha": "Srv_HA_DV", "vm_pcpu": "8", "vm_vcpu": "8", "vm_memory": "32", "memory_unit": "GB", "vm_storage": "100", "vm_redundancy": "1", "storage_unit": "GB", "vm_priority": "1", "vm_iops_loading": "1000", "vm_iops_running": "1000"}], "ntp": [{"ip_version": "IPv4", "local_ip": "", "ntp_server": [{"ip": "", "peer_port": "", "peer_protocol": ""}]}]}, {"common": {"name": "DV_SA", "product": "DigitalView", "version": "V800R001C02(Euler)", "vendor": "HUAWEI", "condition": ""}, "basic_info": {"physical_ne_type": "DV_SA", "vnf_type_vnfd": "DV_SA", "vnfd_version": "V800R001C02(Euler)", "location_id": "", "dc": "", "region_id": "", "compute_res_info": [{"ha": " Srv_HA_DV"}], "vdc": "Src_VDC_DV", "vpc": " Src_VPC_DV", "storage_res_info": [{"volume_type_name": "VolumeService01"}], "me_id": "", "site_name": "", "abbreviated_site_name": "", "solution_type": "DigitalView", "solution_version": "V800R001C02(Euler)", "solution_name": "SoftwareOSS"}, "account": [{"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:ossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{sa_ip}}", "login_type": "SSH", "account": "ossuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:dbuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{sa_ip}}", "login_type": "SSH", "account": "dbuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:ossadm", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{sa_ip}}", "login_type": "SSH", "account": "ossadm", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sshossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{sa_ip}}", "login_type": "SSH", "account": "ssh<PERSON><PERSON>", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sftpossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{sa_ip}}", "login_type": "SFTP", "account": "sftpossuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sysomc", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{sa_ip}}", "login_type": "SSH", "account": "sysomc", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sopuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{sa_ip}}", "login_type": "SSH", "account": "sopuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:dv<PERSON><PERSON>er", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{sa_ip}}", "login_type": "SSH", "account": "dv<PERSON><PERSON><PERSON>", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:devdata", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{sa_ip}}", "login_type": "SSH", "account": "devdata", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}], "vm": [{"vm_name": "sanode", "vm_type": "SA", "work_mode": "LB", "compute_az_name": "", "storage_az_name": "", "ha": "Srv_HA_DV", "vm_pcpu": "8", "vm_vcpu": "8", "vm_memory": "32", "memory_unit": "GB", "vm_storage": "100", "vm_redundancy": "1", "storage_unit": "GB", "vm_priority": "1", "vm_iops_loading": "1000", "vm_iops_running": "1000"}], "ntp": [{"ip_version": "IPv4", "local_ip": "", "ntp_server": [{"ip": "", "peer_port": "", "peer_protocol": ""}]}]}, {"common": {"name": "DV_OM", "product": "DigitalView", "version": "V800R001C02(Euler)", "vendor": "HUAWEI", "condition": ""}, "basic_info": {"physical_ne_type": "DV_OM", "vnf_type_vnfd": "DV_OM", "vnfd_version": "V800R001C02(Euler)", "location_id": "", "dc": "", "region_id": "", "compute_res_info": [{"ha": " Srv_HA_DV"}], "vdc": "Src_VDC_DV", "vpc": " Src_VPC_DV", "storage_res_info": [{"volume_type_name": "VolumeService01"}], "me_id": "", "site_name": "", "abbreviated_site_name": "", "solution_type": "DigitalView", "solution_version": "V800R001C02(Euler)", "solution_name": "SoftwareOSS"}, "account": [{"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:ossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{om_ip}}", "login_type": "SSH", "account": "ossuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:dbuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{om_ip}}", "login_type": "SSH", "account": "dbuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:ossadm", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{om_ip}}", "login_type": "SSH", "account": "ossadm", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sshossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{om_ip}}", "login_type": "SSH", "account": "ssh<PERSON><PERSON>", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sftpossuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{om_ip}}", "login_type": "SFTP", "account": "sftpossuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sysomc", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{om_ip}}", "login_type": "SSH", "account": "sysomc", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:sopuser", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{om_ip}}", "login_type": "SSH", "account": "sopuser", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:dv<PERSON><PERSON>er", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{om_ip}}", "login_type": "SSH", "account": "dv<PERSON><PERSON><PERSON>", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}, {"ne_name": "dc1vU2020_ALL01", "component": "U2020-M", "object": "InterconnectionUser:devdata", "logical_network_name": "OSS_Service", "dc": "dc1", "ip_version": "IPv4", "ip": "{{om_ip}}", "login_type": "SSH", "account": "devdata", "password": "", "password_change": "", "pwd_dead_line": "", "desc": ""}], "vm": [{"vm_name": "omnode", "vm_type": "OM", "work_mode": "LB", "compute_az_name": "", "storage_az_name": "", "ha": "Srv_HA_DV", "vm_pcpu": "8", "vm_vcpu": "8", "vm_memory": "32", "memory_unit": "GB", "vm_storage": "100", "vm_redundancy": "1", "storage_unit": "GB", "vm_priority": "1", "vm_iops_loading": "1000", "vm_iops_running": "1000"}], "ntp": [{"ip_version": "IPv4", "local_ip": "", "ntp_server": [{"ip": "", "peer_port": "", "peer_protocol": ""}]}]}], "vnic_ip_info": [{"vnf_name": "DV_UniEP", "vm_name": "UniEPNode", "vnic": "eth0", "vnic_type": "1", "logic_ne": "DV_UniEP", "service_flow": "OSS_Self_Maintenance;OSS_Service", "work_mode": "Active", "ip_type": "Static IP", "network_plane_type": "inner-private", "network_plane": "DV_Network", "ip_info": [{"ip_version": "IPv4", "ip": "{{uniep_ip}}", "mask": "{{ip_mask}}", "gateway": "{{ip_gateway}}"}], "osmu_use": "", "interface_protocol": "OM:MML/SOAP/SNMP over TCP", "interworking_ne": "（客户北向名称）USM", "description_en": "", "description_cn": "主备虚机各需要一个独立的IP地址", "vrf": "VRF_OM", "vpn": "", "logical_interface_type": "", "application_layer_protocol": "", "transport_layer_protocol": ""}, {"vnf_name": "DV_APP", "vm_name": "appnode", "vnic": "eth0", "vnic_type": "1", "logic_ne": "DV_APP", "service_flow": "OSS_Self_Maintenance;OSS_Service", "work_mode": "Active", "ip_type": "Static IP", "network_plane_type": "inner-private", "network_plane": "DV_Network", "ip_info": [{"ip_version": "IPv4", "ip": "{{app_ip}}", "mask": "{{ip_mask}}", "gateway": "{{ip_gateway}}"}], "osmu_use": "", "interface_protocol": "OM:MML/SOAP/SNMP over TCP", "interworking_ne": "（客户北向名称）USM", "description_en": "", "description_cn": "主备虚机各需要一个独立的IP地址", "vrf": "VRF_OM", "vpn": "", "logical_interface_type": "", "application_layer_protocol": "", "transport_layer_protocol": ""}, {"vnf_name": "DV_DB", "vm_name": "dbnode", "vnic": "eth0", "vnic_type": "1", "logic_ne": "DV_DB", "service_flow": "OSS_Self_Maintenance;OSS_Service", "work_mode": "Active", "ip_type": "Static IP", "network_plane_type": "inner-private", "network_plane": "DV_Network", "ip_info": [{"ip_version": "IPv4", "ip": "{{db_ip}}", "mask": "{{ip_mask}}", "gateway": "{{ip_gateway}}"}], "osmu_use": "", "interface_protocol": "OM:MML/SOAP/SNMP over TCP", "interworking_ne": "（客户北向名称）USM", "description_en": "", "description_cn": "主备虚机各需要一个独立的IP地址", "vrf": "VRF_OM", "vpn": "", "logical_interface_type": "", "application_layer_protocol": "", "transport_layer_protocol": ""}, {"vnf_name": "DV_SA", "vm_name": "sanode", "vnic": "eth0", "vnic_type": "1", "logic_ne": "DV_SA", "service_flow": "OSS_Self_Maintenance;OSS_Service", "work_mode": "Active", "ip_type": "Static IP", "network_plane_type": "inner-private", "network_plane": "DV_Network", "ip_info": [{"ip_version": "IPv4", "ip": "{{sa_ip}}", "mask": "{{ip_mask}}", "gateway": "{{ip_gateway}}"}], "osmu_use": "", "interface_protocol": "OM:MML/SOAP/SNMP over TCP", "interworking_ne": "（客户北向名称）USM", "description_en": "", "description_cn": "主备虚机各需要一个独立的IP地址", "vrf": "VRF_OM", "vpn": "", "logical_interface_type": "", "application_layer_protocol": "", "transport_layer_protocol": ""}, {"vnf_name": "DV_OM", "vm_name": "omnode", "vnic": "eth0", "vnic_type": "1", "logic_ne": "DV_OM", "service_flow": "OSS_Self_Maintenance;OSS_Service", "work_mode": "Active", "ip_type": "Static IP", "network_plane_type": "inner-private", "network_plane": "DV_Network", "ip_info": [{"ip_version": "IPv4", "ip": "{{om_ip}}", "mask": "{{ip_mask}}", "gateway": "{{ip_gateway}}"}], "osmu_use": "", "interface_protocol": "OM:MML/SOAP/SNMP over TCP", "interworking_ne": "（客户北向名称）USM", "description_en": "", "description_cn": "主备虚机各需要一个独立的IP地址", "vrf": "VRF_OM", "vpn": "", "logical_interface_type": "", "application_layer_protocol": "", "transport_layer_protocol": ""}], "vdc_list": [{"vdc_type_id": "Src_VDC_DV", "vdc_instance_id": "Src_VDC_DV", "vdc_name": "Src_VDC_DV", "data_phase_type": "new", "security_domain_type": "TRUST", "co_vdc_type": "intra_solution_group", "vpc_list": [{"vpc_instance_id": "Src_VPC_DV", "data_phase_type": "new", "vpc_name": "Src_VPC_DV", "logic_network_list": [{"logic_network_id": "DV_Network", "network_class": "VM", "data_phase_type": "new", "vlan_transparent": "", "logic_network_name": "DV_Network", "logic_network_outer": "", "bandwidth_existing": "1000", "bandwidth_add": "1000", "traffic_flow_type": "intra-app", "physical_network_id": "physnet2", "sdn_network_type": "", "network_plane": "", "network_plane_type": "inner-private", "address_acquire_method": "", "ip_address_list": [{"vlan_id": "3701", "vlan_id_outer": "", "ipv4_address": "{{uniep_ip}}/{{ip_netmask}}", "ipv4_gateway": "{{ip_gateway}}", "ipv6_address": "", "ipv6_gateway": "", "subnet_ip_address_list": [{"subnet_name_ipv4": "", "subnet_name_ipv6": "", "pool_start_ipv4": "", "pool_end_ipv4": "", "pool_start_ipv6": "", "pool_end_ipv6": ""}]}, {"vlan_id": "3701", "vlan_id_outer": "", "ipv4_address": "{{app_ip}}/{{ip_netmask}}", "ipv4_gateway": "{{ip_gateway}}", "ipv6_address": "", "ipv6_gateway": "", "subnet_ip_address_list": [{"subnet_name_ipv4": "", "subnet_name_ipv6": "", "pool_start_ipv4": "", "pool_end_ipv4": "", "pool_start_ipv6": "", "pool_end_ipv6": ""}]}, {"vlan_id": "3701", "vlan_id_outer": "", "ipv4_address": "{{db_ip}}/{{ip_netmask}}", "ipv4_gateway": "{{ip_gateway}}", "ipv6_address": "", "ipv6_gateway": "", "subnet_ip_address_list": [{"subnet_name_ipv4": "", "subnet_name_ipv6": "", "pool_start_ipv4": "", "pool_end_ipv4": "", "pool_start_ipv6": "", "pool_end_ipv6": ""}]}, {"vlan_id": "3701", "vlan_id_outer": "", "ipv4_address": "{{sa_ip}}/{{ip_netmask}}", "ipv4_gateway": "{{ip_gateway}}", "ipv6_address": "", "ipv6_gateway": "", "subnet_ip_address_list": [{"subnet_name_ipv4": "", "subnet_name_ipv6": "", "pool_start_ipv4": "", "pool_end_ipv4": "", "pool_start_ipv6": "", "pool_end_ipv6": ""}]}, {"vlan_id": "3701", "vlan_id_outer": "", "ipv4_address": "{{om_ip}}/{{ip_netmask}}", "ipv4_gateway": "{{ip_gateway}}", "ipv6_address": "", "ipv6_gateway": "", "subnet_ip_address_list": [{"subnet_name_ipv4": "", "subnet_name_ipv6": "", "pool_start_ipv4": "", "pool_end_ipv4": "", "pool_start_ipv6": "", "pool_end_ipv6": ""}]}]}]}]}], "compute_resource": {"cluster_list": [{"cluster_type_id": "Srv_HA_DV", "cluster_instance_id": "Srv_HA_DV", "cluster_name": "Srv_HA_DV", "os_arch": "arm", "data_phase_type": "new", "security_domain_type": "TRUST", "service_type": "om", "co_cluster_type": "intra_solution_group", "vswitch_type": "OVS", "is_co_cluster_in_ctrl_node": "FALSE", "host_list": [{"host_type": "Taishan 200", "bom": "", "data_phase_type": "new", "host_num": "3"}], "host_group_list": [{"host_group_instance_id": "DigitalView_GRP"}], "deploy_policy": "sequence", "bandwidth_add": "1000", "bandwidth_anti_list": [{"cpu_num_max": "", "cpu_num_min": "", "traffic_flow_mode": "", "bandwidth_anti": ""}]}], "host_group_list": [{"host_group_type_id": "DigitalView_GRP", "host_group_instance_id": "DigitalView_GRP", "host_group_name": "DigitalView_GRP", "data_phase_type": "new", "host_type": "Taishan 200", "bom": "", "cluster_list": [{"cluster_instance_id": "Srv_HA_DV"}], "physical_network_list": [{"physical_network_id": "physnet1", "vswitch_type": "OVS", "vlan_id": "3701", "physical_network_name": "physnet1", "traffic_flow_type": "MGNT"}, {"physical_network_id": "physnet2", "vswitch_type": "OVS", "vlan_id": [""], "physical_network_name": "physnet2", "traffic_flow_type": "intra-app;inter-app;"}], "resource_isolation_info": {"vcpu_num_isolation": "", "memory_isolation": "", "pcpu_num_isolation_evs": "", "memory_isolation_evs": "", "interrupt_binding_policy": "", "interrupt_binding_policy_cn": ""}}]}, "storage_resource": {"volume_type_group_list": [{"volume_type_group_id": "VolumeService", "anti_flag": "true", "is_allow_split": "true", "co_volume_type": "intra_solution_group", "physical_isolate": "volume_isolate", "raid_level_type": "raid6", "domain_func": "TRUST", "volume_type_list": [{"volume_type_id": "VolumeService01", "data_phase_type": "new", "display_level": "solution", "min_disk_num": "0", "storage_az_id": "", "storage_az_name": "", "storage_req_list": [{"data_phase_type": "new", "storage_total": "637", "iops_total": "1000", "disk_type": "SSD", "storage_type": "service"}]}, {"volume_type_id": "VolumeService01", "data_phase_type": "new", "display_level": "solution", "min_disk_num": "0", "storage_az_id": "", "storage_az_name": "", "storage_req_list": [{"data_phase_type": "new", "storage_total": "250", "iops_total": "1000", "disk_type": "SSD", "storage_type": "service"}]}, {"volume_type_id": "VolumeService01", "data_phase_type": "new", "display_level": "solution", "min_disk_num": "0", "storage_az_id": "", "storage_az_name": "", "storage_req_list": [{"data_phase_type": "new", "storage_total": "480", "iops_total": "1000", "disk_type": "SSD", "storage_type": "service"}]}, {"volume_type_id": "VolumeService01", "data_phase_type": "new", "display_level": "solution", "min_disk_num": "0", "storage_az_id": "", "storage_az_name": "", "storage_req_list": [{"data_phase_type": "new", "storage_total": "350", "iops_total": "1000", "disk_type": "SSD", "storage_type": "service"}]}, {"volume_type_id": "VolumeService01", "data_phase_type": "new", "display_level": "solution", "min_disk_num": "0", "storage_az_id": "", "storage_az_name": "", "storage_req_list": [{"data_phase_type": "new", "storage_total": "350", "iops_total": "1000", "disk_type": "SSD", "storage_type": "service"}]}]}]}}]}}