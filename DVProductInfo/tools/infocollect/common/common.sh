#!/bin/bash
set +x
main_path="$1"
common_path=${main_path}/common
log_full_path=${main_path}/log
archive_path=${main_path}/site_archive
exe_log_file=${log_full_path}/infocollect.log
feature_custom_cfg_template=${main_path}/feature_custom_cfg_template
feature_custom_cfg=${main_path}/feature_custom_cfg
select_service_proce=${main_path}/select_service_proce
scenario_description=${common_path}/scenario_description.cfg
export_product_info_path=${main_path}/export_product_info
log_default_day_time=1
nodetag_user_loglist_json=${export_product_info_path}/scene_to_nodetag.json
nodetag_hostinfo_json=${export_product_info_path}/nodetag_to_hostinfo.json
hostinfo_and_user_loglist_json=${export_product_info_path}/hostinfo_and_user_loglist.json
pycommon=${common_path}/pycommon.py

HOME_OSSADM="/home/<USER>"
tmp_dir=${HOME_OSSADM}/infocollect_preset
db_logs_backup_tmp=${HOME_OSSADM}/infocollect_tmp
node_log_file=${tmp_dir}/log/infocollect.log
log_path={{log_path}}
infocollect_log_dir=${log_path}/manager/infocollect

node_infocollect_preset=${main_path}/node_infocollect_preset
logs_size_ret=${node_infocollect_preset}/logs_size_ret.txt

node_ret_file="node_infocollect_result.properties"
node_ret_report="node_logs_report.csv"
node_ret_logs="node_collection_logs.csv"

node_infocollect_result=${main_path}/${node_ret_file}
node_logs_report=${main_path}/${node_ret_report}
node_collection_logs=${main_path}/${node_ret_logs}

node_result_key="node_result"
node_result_message_key="node_result_message"
node_all_log_size_key="all_log_size"

scene_list_other="all clean"
check_user_list="ossadm ossuser dbuser secuser"
execute_user=$(whoami)

if [ ! -f ${main_path}/common/common.sh -a ! -f  ${main_path}/common.sh ];then
    echo "The ${main_path}/common/common.sh and ${main_path}/common.sh is not exist."
    exit 1
fi


[ -f ${main_path}/common/config_common.properties ] && . ${main_path}/common/config_common.properties 2> /dev/null
[ -f ${main_path}/config_common.properties ] && . ${main_path}/config_common.properties 2> /dev/null
backup_default_path=${install_path}/infocollect/data_log
engr_profile=${install_path}/manager/bin/engr_profile.sh
logs_backup_tmp=${install_path}/infocollect_tmp
nodelists_json=${install_path}/manager/etc/sysconf/nodelists.json
[ -f ${engr_profile} ] && source ${engr_profile} 2> /dev/null

ssh_port=22
cat /etc/ssh/sshd_config 2> /dev/null| grep -w '^Port' | grep -v '#' > /dev/null 2>&1
if [ $? -ne 0 ];then
    ssh_port=22
else
    ssh_port=$(cat /etc/ssh/sshd_config | grep -w "^Port" | grep -v "#" |awk -F 'Port' '{print $2}' | sed s/[[:space:]]//g)
fi

ssh_prefix="ssh -q -o StrictHostKeyChecking=no -p ${ssh_port}"

function log_echo()
{
    local log_level=INFO
    if [ $# -ge 2 ]; then
        log_level=$1
        shift
    fi
    if [ ! -d ${log_full_path} ];then
        mkdir -p ${log_full_path}
    fi
    log_level=$(echo ${log_level} | tr 'a-z' 'A-Z')
    local log_date_time=$(date +'%Y-%m-%d %H:%M:%S')
    echo "${log_date_time} [${log_level}] [${execute_user}] $*" >> ${exe_log_file}
    if [ "X${log_level}" == "XINFO" ]; then
        echo -e "\033[49;32m[INFO][${log_date_time}]\033[0m [${log_level}] [${execute_user}] $*" 
    elif [ "X${log_level}" == "XWARN" ]; then
        echo -e "\033[49;33m[WARN][${log_date_time}]\033[0m [${log_level}] [${execute_user}] $*" 
    elif [ "X${log_level}" == "XERROR" ]; then
        echo -e "\033[49;31m[ERROR][${log_date_time}]\033[0m [${log_level}] [${execute_user}] $*" 
    else
        echo -e "\033[49;31m[${log_level}][${log_date_time}]\033[0m [${log_level}] [${execute_user}] $*"
    fi
}

function log_sysnc_error()
{
    local log_info=$1
    log_echo "ERROR" "${log_info}"
    echo "ret=1" >> ${logs_size_ret}
    echo "${log_info}" >> ${logs_size_ret}
}

function update_result()
{
    local key="$1"
    local value="$2"
    if [ ! -f ${node_infocollect_result} ];then
        touch ${node_infocollect_result}
    fi
    
    cat ${node_infocollect_result} |grep "^${key}=" >> ${exe_log_file} 2>&1
    if [ $? -eq 0 ];then
        sed -i "/^${key}=/d" ${node_infocollect_result}
    fi
    echo "${key}=${value}" >> ${node_infocollect_result}
    return $?
}

function get_scene_list()
{
    log_echo "INFO" "${FUNCNAME} start."
    if [ ! -d ${feature_custom_cfg_template} ];then
        log_echo "ERROR" "The feature_custom_cfg_template=${feature_custom_cfg_template} is not exist."
        exit 1
    fi
    
    # no need collect site info when the scenario is all.
    if [ "x${scene_value}" == "xall" ];then
        scene_list=$(ls ${feature_custom_cfg_template}|grep ".ini$" |grep -v "^site_" |sed "s/.ini$/,/g")
    else
        scene_list=$(ls ${feature_custom_cfg_template}|grep ".ini$" |sed "s/.ini$/,/g")
    fi
    if [ -z "${scene_list}" ];then
        log_echo "ERROR" "The scene_list=${scene_list} is null."
        exit 1
    fi
    echo "The scene_list=${scene_list}" >> ${exe_log_file}
    log_echo "INFO" "${FUNCNAME} finish."
}

function usage()
{
    echo "Usage:"
    echo " infocollect -scene <scene> [-backup_path <backup_path>] [-time <time>]"
    echo ""
    echo " scene : [Only the following scenarios are supported.Separate multiple scenarios with commas (,).]"
    echo "       $(cat ${scenario_description})"
    echo ""
    echo " backup_path: "
    echo "          Specifies the path for storing collected logs.The ossadm directory must have the read and write permissions."
    echo "          This parameter is optional."
    echo "          The default path is /opt/oss/infocollect/data_log."
    echo ""
    echo " time:"
    echo "     Collection time (1 to 7 days, optional, one day by default, from the current time to 00:00 a.m."
    echo ""
    echo " Example:"
    echo " infocollect -scene cie"
    echo " infocollect -scene cie,db"
    echo " infocollect -scene db -backup_path /opt/oss/infocollect/data_log"
    echo " infocollect -scene cie -backup_path /opt/oss/infocollect/data_log -time 1"
    echo " infocollect -scene all"
    echo " infocollect -scene clean"
    echo " infocollect -scene redis {-instance_name instancename -redis_key rediskey} [-backup_path /opt/oss/infocollect/data_log]"
    echo "                                               "
    echo "                                               "
    echo "                                               "
}

function isNum()
{
    local inputNum="$1"
    local key="$2"
    if [ -z "${inputNum}" ];then
        log_echo "ERROR" "The ${key} is null."
        return 1
    fi
    
    local isNumber=$(echo "${inputNum}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        log_echo "ERROR" "The ${key}=${inputNum} is not number."  
        return 1
    fi
    
    return 0
}

function check_feature_custom_cfg()
{
    local scene="$1"
    local scene_file=${feature_custom_cfg}/${scene}.ini
    if [ -z "${scene}" ];then
        log_echo "ERROR" "The scene is null.of ${FUNCNAME}"
        return 1
    fi
    
    if [ ! -f ${scene_file} ];then
        log_echo "ERROR" "The ${scene_file} is not exist.of ${FUNCNAME}"
        return 1
    fi
    
    source ${scene_file}
    isNum "${LogCount}" "LogCount" || return 1
    
    local check_ret=0
    local tmp_ret=""
    for index in $(seq 1 ${LogCount});
    do
        if [ -z "${NodeTag[${index}]}" ];then
            log_echo "ERROR" "The NodeTag[$index] is null.of ${scene_file}"
            check_ret=1
        fi
        
        if [ -z "${OwnerUser[${index}]}" ];then
            log_echo "ERROR" "The OwnerUser[$index] is null.of ${scene_file}"
            check_ret=1
        else
            echo "${check_user_list}" |grep -w "${OwnerUser[${index}]}" >> ${exe_log_file} 2>&1
            if [ $? -ne 0 ];then
                log_echo "ERROR" "The OwnerUser[$index]=${OwnerUser[${index}]} is not in [${check_user_list}].of ${scene_file}"
                check_ret=1
            fi
        fi
        
        if [ -z "${LogList[${index}]}" ];then
            log_echo "ERROR" "The LogList[$index] is null.of ${scene_file}"
            check_ret=1
        else
            echo "${LogList[${index}]}" |grep "^/" >> ${exe_log_file} 2>&1
            if [ $? -ne 0 ];then
                log_echo "ERROR" "The LogList[$index]=${LogList[${index}]} first path has invalid path configuration.of ${scene_file}"
                check_ret=1
            fi
            
            echo "${LogList[${index}]}" |grep ".\+,.\+" >> ${exe_log_file}  2>&1
            if [ $? -eq 0 ];then
                tmp_ret=$(echo "${LogList[${index}]}" |sed "s/,/\n/g"|grep -v "^/")
                if [ ! -z "${tmp_ret}" ];then
                    log_echo "ERROR" "The LogList[$index]=${LogList[${index}]} has invalid path configuration.of ${scene_file}"
                    check_ret=1
                fi
            fi
        fi
    done
    
    for index in $(seq 1 ${LogCount});
    do
        NodeTag[${index}]=""
        OwnerUser[${index}]=""
        LogList[${index}]=""
        if [ ! -z "${ProcessName[${index}]}" ];then
            ProcessName[${index}]=""
        fi
    done
    
    [ ${check_ret} -eq 1 ] && return 1
    
    log_echo "INFO" "The ${scene} check ${scene_file} finish."
    return 0
}

function check_time()
{
    local time="$1"
    if [ -z "${time}" ];then
        log_echo "ERROR" "The time is null."
        return 1
    fi
    
    local isNumber=$(echo "${time}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        log_echo "ERROR" "The time=${time} is not number.Only [1, 2, 3] can be entered."
        return 1
    fi
    
    if [ ${time} -lt 1 -o  ${time} -gt 7 ];then
        log_echo "ERROR" "The time=${time} is not in [1~7] day.Only [1~7] can be entered."
        return 1
    fi
    
    log_echo "INFO" "${FUNCNAME} finish."
    collection_log_day_time=${time}
    return 0
}

function check_backup_path()
{
    local backup_path="$1"
    if [ -z "${backup_path}" ];then
        log_echo "ERROR" "The backup_path is null."
        return 1
    fi
    
    echo "${backup_path}" |grep "^/" >> ${exe_log_file} 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "The backup_path=${backup_path} is not a directory."
        return 1
    fi
    
    if [ ! -d ${backup_path} ];then
        log_echo "INFO" "The backup_path=${backup_path} is not exist.to create it."
        mkdir -p ${backup_path} 
        if [ $? -ne 0 ];then
            log_echo "ERROR" "The backup_path=${backup_path} create failed."
            return 1
        fi
    fi
    
    touch ${backup_path}/test.txt 
    if [ $? -ne 0 ];then
        log_echo "ERROR" "touch ${backup_path}/test.txt failed."
        return 1
    fi
    
    rm -rf ${backup_path}/test.txt
    collection_result_backup_path="${backup_path}"
    log_echo "INFO" "${FUNCNAME} finish."
    return 0
}

function check_redis()
{
    local instance_name="$1"
    local redis_key="$2"
    if [ -z "${instance_name}" ] || [ -z "${redis_key}" ];then
        log_echo "ERROR" "The instance_name or the redis_key is null."
        exit 43
    fi

    local ret=$(source ${install_path}/manager/bin/engr_profile.sh;python "${pycommon}" "${main_path}/infocollect-redis.json" "is_in_redis_whitelist" "${instance_name}" "${redis_key}")
    if [ "${ret}" != "True" ];then
        log_echo "ERROR" "The instance_name or the redis_key are error."
        exit 44
    fi

    collect_redis_instance=${instance_name}
    collect_redis_key=${redis_key}
    log_echo "INFO" "${FUNCNAME} finish."
    return 0
}

function check_scene()
{
    local scene_key="$1"
    scene_value=$(echo "$2" | tr 'A-Z' 'a-z')
    local check_result=0
    
    [ -f "${common_path}/stack.tag" ] && rm -rf ${common_path}/stack.tag
    if [[ "${scene_value}" =~ "stack" ]];then
        scene_value=$(echo ${scene_value} |awk -F "_" '{print $2}')
        export IS_COLLECT_STACK=true
        echo IS_COLLECT_STACK=${IS_COLLECT_STACK} > ${common_path}/stack.tag
        if [ $? -ne 0 ];then
            log_echo "ERROR" "This is stack collection job. touch stack.tag failed."
            exit 39
        fi
        if [ "x${scene_value}" == "xall" ];then
            echo ProcessName=$(cat ${feature_custom_cfg_template}/*.ini |grep "^ProcessName" |awk -F "\"" '{print $2}') >> ${common_path}/stack.tag
        else
            echo ProcessName=$(cat ${feature_custom_cfg_template}/${scene_value}.ini |grep "^ProcessName" |awk -F "\"" '{print $2}') >> ${common_path}/stack.tag
        fi
    fi
    if [ "${scene_key}" != "-scene" ];then
        log_echo "ERROR" "check parameter failed.The first parameter is not equal to '-scene'.scene_key=${scene_key}"
        check_result=1
    fi
    
    get_scene_list
    
    local check_same_scene=""
    for tmp_scene_value in $(echo "${scene_value}" |sed "s/,/ /g");do
        echo "${scene_list} ${scene_list_other}" |grep -w "${tmp_scene_value}" >> ${exe_log_file} 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "check parameter failed.The second parameter value has not in the scenario list.tmp_scene_value=${tmp_scene_value}"
            check_result=1
        fi
        
        if [ -z "${check_same_scene}" ];then
            check_same_scene="${tmp_scene_value}"
            continue
        fi
        
        echo "${check_same_scene}" |grep -w "${tmp_scene_value}" >> ${exe_log_file} 2>&1
        if [ $? -eq 0 ];then
            log_echo "ERROR" "check parameter failed.The ${tmp_scene_value} is in the [${check_same_scene}] scenario list.has same scenario.pleack check."
            check_result=1
        else
            check_same_scene="${check_same_scene},${tmp_scene_value}"
        fi
    done
    
    echo "${scene_value}" |grep ".\+,.\+" |grep -w "all\|clean" >> ${exe_log_file} 2>&1
    if [ $? -eq 0 ];then
        log_echo "ERROR" "The scene_value=${scene_value} has 'all' or 'clean' scenario and has more other scenario.'all' and 'clean' can be executed only in this scenario. You do not need to set other scenarios.pleack check."
        check_result=1
    fi
    
    collection_scene=${scene_value}
    if [ "X${scene_value}" == "Xall" ];then
        collection_scene=${scene_list}
    fi
    
    log_echo "INFO" "${FUNCNAME} check_result=${check_result}."
    return ${check_result}
}

function get_parameter_from_input()
{
    local redis_key="";
    local instance_name="";
    local backup_path="";
    local file_name="";
    while [[ $# -gt 0 ]]
    do
        key="$1"
        case $key in
            -redis_key)
                redis_key="$2"
                shift;shift;
                ;;
            -instance_name)
                instance_name="$2"
                shift;shift;
                ;;
            -backup_path)
                backup_path="$2"
                shift;shift;
                ;;
            -file_name)
                file_name="$2"
                shift;shift;
                ;;
            *)
                shift;shift;
                ;;
        esac
    done

    check_redis "${instance_name}" "${redis_key}"
    [ -n "${backup_path}" ] && check_backup_path "${backup_path}"
    [ -n "${file_name}" ] && collection_file_name="${file_name}"
}

function check_log_extend_path()
{
    local dest_path="$1"
    product_sop_json="${export_product_info_path}/product_SOP.json"
    if [ ! -f "${product_sop_json}" ];then
        log_echo "ERROR" "The product_SOP.json is not exits!"
        exit 13
    fi
    local is_log_extend=$(cat ${product_sop_json} |awk -F "detailed_log_printing_mode" '{print $2}' |awk -F "\"" '{print $3}')
    if [ "X${is_log_extend}" == "XYes" ];then
        log_echo "INFO" "The env is extend log disk. need to modify collection_result_backup_path."
        if [ -d "${install_path}/log" ];then
            collection_result_backup_path="${install_path}/log/infocollect/data_log"
        else
            collection_result_backup_path="/var/log/oss/infocollect/data_log"
        fi
    else
        log_echo "INFO" "The env is not extend log disk. no need to modify collection_result_backup_path."
        collection_result_backup_path="${dest_path}"
    fi
}

## infocollect -scene <scene> [-backup_path <backup_path>] [-time <time>]
function check_parameter()
{
    local parameter_count=$#
    if [ ${parameter_count} -ne 2 -a ${parameter_count} -ne 4 -a ${parameter_count} -ne 6 -a ${parameter_count} -ne 8 -a ${parameter_count} -ne 10 ];then
        echo "check parameter failed.The number of entered parameters is incorrect. The number of entered parameters is not in [2,4,6,8,10].parameter_count=${parameter_count}" >> ${exe_log_file}
        usage
        exit 5
    fi
    
    ## check scene and get collection_scene
    check_scene $@
    local check_result=$?
    
    # check and ini collection_result_backup_path default path
    check_log_extend_path "${backup_default_path}"

     ## ini collection_log_day_time default time
    collection_log_day_time=${log_default_day_time}
    
    if [ ${parameter_count} -eq 4 -a -z "$(echo "$3" |grep -w "\-backup_path\|\-time\|\-file_name\|\-instance_name\|\-redis_key")" ];then
        log_echo "ERROR" "check parameter failed.The third parameter is not equal to '-backup_path' or '-time' or '-file_name'.third parameter is ${3}"
        check_result=1
    elif [ ${parameter_count} -eq 6 ] && [ -z "$(echo "$3" |grep -w "\-backup_path\|\-time\|\-file_name\|\-instance_name\|\-redis_key")" -o -z "$(echo "$5" |grep -w "\-backup_path\|\-time\|\-file_name\|\-instance_name\|\-redis_key")" ];then
        log_echo "ERROR" "check parameter failed.The third parameter is not equal to '-backup_path' or '-time' or '-file_name', and the fifth parameter is not equal to '-backup_path' or '-time' or '-file_name'.third parameter is ${3}.fifth parameter is ${5}"
        check_result=1
    elif [ ${parameter_count} -eq 8 ] && [ -z "$(echo "$3" |grep -w "\-backup_path\|\-time\|\-file_name\|\-instance_name\|\-redis_key")" -o -z "$(echo "$5" |grep -w "\-backup_path\|\-time\|\-file_name\|\-instance_name\|\-redis_key")" -o -z "$(echo "$7" |grep -w "\-backup_path\|\-time\|\-file_name\|\-instance_name\|\-redis_key")" ];then
        log_echo "ERROR" "check parameter failed.The third parameter is not equal to '-backup_path' or '-time' or '-file_name', and the fifth parameter is not equal to '-backup_path' or '-time' or '-file_name'.and the fifth parameter is not equal to '-backup_path' or '-time' or '-file_name'.third parameter is ${3}.fifth parameter is ${5}.Seven parameter is ${7}"
        check_result=1
    elif [ ${parameter_count} -eq 10 ] && [ -z "$(echo "$3" |grep -w "\-backup_path\|\-file_name\|\-instance_name\|\-redis_key")" -o -z "$(echo "$5" |grep -w "\-backup_path\|\-file_name\|\-instance_name\|\-redis_key")" -o -z "$(echo "$7" |grep -w "\-backup_path\|\-file_name\|\-instance_name\|\-redis_key")" ];then
        log_echo "ERROR" "check parameter failed.The third parameter is not equal to '-backup_path' or '-time' or '-file_name', and the fifth parameter is not equal to '-backup_path' or '-time' or '-file_name'.and the fifth parameter is not equal to '-backup_path' or '-time' or '-file_name'.third parameter is ${3}.fifth parameter is ${5}.Seven parameter is ${7}"
        check_result=1
    elif [ ${parameter_count} -eq 6 ] && [ "$3" == "$5" ];then
        log_echo "ERROR" "check parameter failed.The third parameter is equal the fifth parameter.third parameter is ${3}.fifth parameter is ${5}."
        check_result=1
    elif [ ${parameter_count} -eq 8 ] && [ "$3" == "$5" -o "$5" == "$7" -o "$3" == "$7" ];then
        log_echo "ERROR" "check parameter failed.Has parameter is same.3=${3} 5=${5} 7=${7}."
        check_result=1
    fi
    
    if [ ${parameter_count} -eq 4 -o ${parameter_count} -eq 6 -o ${parameter_count} -eq 8 -o ${parameter_count} -eq 10 ] && [ "$3" == "-backup_path" ];then
        check_backup_path "$4" || check_result=1
    elif [ ${parameter_count} -eq 4 -o ${parameter_count} -eq 6 -o ${parameter_count} -eq 8 ] && [ "$3" == "-time" ];then
        check_time "$4" || check_result=1
    elif [ ${parameter_count} -eq 4 -o ${parameter_count} -eq 6 -o ${parameter_count} -eq 8 -o ${parameter_count} -eq 10 ] && [ "$3" == "-file_name" ];then
        collection_file_name=$4
        [ -z "$4" ] && check_result=1
    fi
    
    if [ ${parameter_count} -eq 6 -o ${parameter_count} -eq 8 -o ${parameter_count} -eq 10 ] && [ "$5" == "-backup_path" ];then
        check_backup_path "$6" || check_result=1
    elif [ ${parameter_count} -eq 6 -o ${parameter_count} -eq 8 ] && [ "$5" == "-time" ];then
        check_time "$6" || check_result=1
    elif [ ${parameter_count} -eq 6 -o ${parameter_count} -eq 8 -o ${parameter_count} -eq 10 ] && [ "$5" == "-file_name" ];then
        collection_file_name=$6
        [ -z "$6" ] && check_result=1
    fi
    
    if [ ${parameter_count} -eq 8 -a "$7" == "-backup_path" ];then
        check_backup_path "$8" || check_result=1
    elif [ ${parameter_count} -eq 8 -a "$7" == "-time" ];then
        check_time "$8" || check_result=1
    elif [ ${parameter_count} -eq 8 -a "$7" == "-file_name" ];then
        collection_file_name=$8
        [ -z "$8" ] && check_result=1
    fi

    if [ "${collection_scene}" == "redis" ];then
        get_parameter_from_input "$@"
    fi

    if [ ${check_result} -ne 0 ];then
        usage
        exit 6
    fi
    log_echo "INFO" "check parameter finish."
}

function check_scene_cfg()
{
    if [ -z "${collection_scene}" ];then
        log_echo "ERROR" "The collection_scene is null.of ${FUNCNAME}"
        exit 10
    fi
    
    local check_ret=0
    for scene in $(echo "${collection_scene}" |sed "s/,/ /g");
    do
        if [ "${scene}" == "all" -o "${scene}" == "clean" ];then
            continue
        fi
        
        check_feature_custom_cfg "${scene}"
        check_ret=$?
    done
    
    [ ${check_ret} -eq 1 ] && exit 11
    echo "collection_scene=${collection_scene}" >> ${exe_log_file}
    log_echo "INFO" "${FUNCNAME} finish."
}

function check_uniep()
{
    ps -ef | grep "uniepservice-0-0\|uniepservice-1-0\|uniepliteservice-0-0\|uniepliteservice-1-0" |grep -v grep > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Can't find Uiep -Dprocname=uniepservice-0-0 or -Dprocname=uniepservice-1-0 or uniepliteservice-0-0 or uniepliteservice-1-0 process, We need run this at UniEp node, please check ..."
        exit 4
    else
        ##{{install_path}}
        install_path=$(ps -ef | grep "uniepservice-0-0\|uniepservice-1-0\|uniepliteservice-0-0\|uniepliteservice-1-0" |grep -v grep |awk -F'/envs/Product-' '{print $1}' |awk '{print $NF}')
    fi
    
    Uniep_IP=$(cat ${install_path}/manager/var/agent/managerip.conf | grep -w "managerip" | awk -F'=' '{print $2}')
    sed -i "s#Uniep_IP=.*#Uniep_IP=${Uniep_IP}#g" ${main_path}/common/config_common.properties
    
    sed -i "s#install_path=.*#install_path=${install_path}#g" ${main_path}/common/config_common.properties
    source ${main_path}/common/config_common.properties
    backup_default_path=${install_path}/infocollect/data_log
    mkdir -p ${backup_default_path}
    engr_profile=${install_path}/manager/bin/engr_profile.sh
    logs_backup_tmp=${install_path}/infocollect_tmp
    [ -f ${engr_profile} ] && source ${engr_profile} 2> /dev/null
    
    echo "${FUNCNAME} finish." >> ${exe_log_file}
}

function ini_scene_cfg()
{
    if [ -d ${feature_custom_cfg} ];then
        rm -rf ${feature_custom_cfg:?}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "rm -rf ${feature_custom_cfg} failed, please check ..."
            exit 7
        fi
    fi
    
    cp -rpf ${feature_custom_cfg_template} ${feature_custom_cfg}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "cp -rpf ${feature_custom_cfg_template} ${feature_custom_cfg} failed, please check ..."
        exit 8
    fi
    
    sed -i "s#{{install_path}}#${install_path}#g" ${feature_custom_cfg}/*.ini
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute [ sed -i \"s#{{install_path}}#${install_path}#g\" ${feature_custom_cfg}/*.ini ] failed, please check ..."
        exit 9
    fi
    
    log_echo "INFO" "${FUNCNAME} finish."
}

function is_clean()
{
    log_echo "INFO" "collection_scene=${collection_scene} collection_file_name=${collection_file_name}."
    if [ "${collection_scene}" == "clean" -a -d ${collection_result_backup_path} ];then
        tmp_collection_result_backup_path=$(echo ${collection_result_backup_path} | sed 's#/opt/oss/log##g' | sed 's#/var/log/oss##g' | sed 's#/opt/oss##g')
        collection_result_backup_path_remove_list="/var/log/oss/${tmp_collection_result_backup_path} /opt/oss/log/${tmp_collection_result_backup_path} /opt/oss/${tmp_collection_result_backup_path}"

        local backup_path;
        for backup_path in ${collection_result_backup_path_remove_list};
        do
            if [ -z "${collection_file_name}" ];then
                rm -f "${backup_path}"/infocollect_*.zip
                log_echo "INFO" "The tmp_path=${backup_path} ,remove ${backup_path}/infocollect_*.zip, execute ret=$?."
                continue;
            fi

            if [ -f "${backup_path}/${collection_file_name}" ];then
                rm -f "${backup_path}/${collection_file_name}"
                log_echo "INFO" "remove ${tmp_path}/${collection_file_name}, execute ret=$?.."
            else
                log_echo "INFO" "The ${backup_path}/${collection_file_name} not exists,not need to del it."
            fi
        done

        clean_peer_pkg
        
        ## ${main_path}
        rm -rf ${main_path}/common/check_result/check_task_list.ret
        rm -rf ${main_path}/log
        rm -rf ${main_path}/node_infocollect_preset
        update_progress "100"
        log_echo "INFO" "${FUNCNAME} finish."
        exit 0
    fi
}

function export_product_info()
{
    if [ ! -f "${engr_profile}" ];then
        log_echo "ERROR" "${engr_profile} is not exist, please check..."
        exit 12
    fi
    
    ## export_product_info_path
    if [ -d ${export_product_info_path} ];then
        local tmp_exp_path=$(cd ${export_product_info_path}; pwd)
        if [ "X${tmp_exp_path}" !=  "X/" ];then
            log_echo "INFO" "The tmp_exp_path=${tmp_exp_path} ,remove it."
            rm -rf ${tmp_exp_path:?}
        fi
    fi
    
    mkdir -p ${export_product_info_path}
    chown ossadm:ossgroup ${export_product_info_path}
    chmod 750 ${export_product_info_path}
    
    
    sh ${install_path}/manager/tools/resmgr/queryproduct.sh -pn SOP -output ${export_product_info_path} 
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute [ sh ${install_path}/manager/tools/resmgr/queryproduct.sh -pn SOP -output ${export_product_info_path} ] failed, please check..."
        exit 13
    fi
    
    log_echo "INFO" "${FUNCNAME} finish."
}

function analysis_product_info()
{
    local node_json=${export_product_info_path}/nodes*.json
    if [ ! -f ${node_json} ];then
        log_echo "ERROR" "Export product info by uniep failed,of ${node_json} is not exist.please check..."
        return 1
    fi
    
    local nodetag="$1"
    local dest_json="$2"
    if [ -z "${nodetag}" -o -z "${dest_json}" ];then
        log_echo "ERROR" "The nodetag=${nodetag} or dest_json=${dest_json} is null.of ${FUNCNAME}."
        return 1
    fi
    
    echo "ALL,DB,UniEP" | grep -w "${nodetag}" >> ${exe_log_file} 2>&1
    if [ $? -ne 0 ];then
        cat ${node_json} |grep -w "${nodetag}" >> ${exe_log_file} 2>&1
        if [ $? -ne 0 ];then
            log_echo "INFO" "The node_json=${node_json} not found the nodetag=${nodetag} ,The service[${nodetag}] is not installed in the system.skip it. of ${FUNCNAME}."
            return 0
        fi
    fi
    
    local ret=$(python ${pycommon} ${node_json} "get_hostinfo" "${nodetag}" "${dest_json}")
    if [ "X${ret}" != "XSUCCESS" ];then
        log_echo "ERROR" "The ret=${ret} is not equal SUCCESS.of ${FUNCNAME}."
        return 1
    fi
    
    log_echo "INFO" "The nodetag=${nodetag} analysis success.of ${FUNCNAME}."
    return 0
}

function analysis_scene_cfg()
{
    local scene="$1"
    local dest_json="$2"
    if [ -z "${scene}" -o -z "${dest_json}" ];then
        log_echo "ERROR" "The scene=${scene} or dest_json=${dest_json} is null.of ${FUNCNAME}."
        return 1
    fi
    
    local ret=""
    local execute_ret=0
    local scene_ini=${feature_custom_cfg}/${scene}.ini
    source ${scene_ini}
    for index in $(seq 1 ${LogCount});
    do
        if [ "x${IS_COLLECT_STACK}" == "xtrue" ] && [ "x${ProcessName[index]}" == "x" ];then
            log_echo "INFO" "This is stack collection job."
            continue
        elif [ "x${IS_COLLECT_STACK}" != "xtrue" ] && [ "x${ProcessName[index]}" != "x" ];then
            log_echo "INFO" "This is nomal collection job."
            continue
        fi
        ret=$(python ${pycommon} ${dest_json} "nodetag_user_logs" "${NodeTag[${index}]}" "${OwnerUser[${index}]}" "${LogList[${index}]}")
        if [ "X${ret}" != "XSUCCESS" ];then
            log_echo "ERROR" "The ret=${ret} is not equal SUCCESS.of ${FUNCNAME}. The file is: ${scene_ini} , the NodeTag index is : [${index}]"
            execute_ret=1
        fi
    done
    
    for index in $(seq 1 ${LogCount});
    do
        NodeTag[${index}]=""
        OwnerUser[${index}]=""
        LogList[${index}]=""
        if [ ! -z "${ProcessName[${index}]}" ];then
            ProcessName[${index}]=""
        fi
    done
    
    log_echo "INFO" "The scene=${scene} finish.of ${FUNCNAME}."
    return ${execute_ret}
}

function hostinfo_and_user_loglist_set()
{
    local hostinfo_json="$1"
    local user_loglist_json="$2"
    
    if [ ! -f ${hostinfo_json} ];then
        log_echo "ERROR" "The hostinfo_json=${hostinfo_json} is not exists.of ${FUNCNAME}."
        exit 17
    fi
    
    if [ ! -f ${user_loglist_json} ];then
        log_echo "ERROR" "The user_loglist_json=${user_loglist_json} is not exists.of ${FUNCNAME}."
        exit 18
    fi
    
    echo "{}" > ${hostinfo_and_user_loglist_json}
    
    local ret=$(python ${pycommon} "${hostinfo_and_user_loglist_json}" "hostinfo_and_user_loglist" "${hostinfo_json}" "${user_loglist_json}")
    if [ "X${ret}" != "XSUCCESS" ];then
        log_echo "ERROR" "The ret=${ret} is ERROR.of ${FUNCNAME}."
        exit 19
    fi
    
    log_echo "INFO" "nodetag and hostinfo set success.of ${FUNCNAME}."
}

function analysis_all_nodetag_of_scene_list()
{
    local ret=0
    echo "{}" > ${nodetag_user_loglist_json}
    for scene in $(echo "${collection_scene}"|sed "s/,/ /g");do
        analysis_scene_cfg "${scene}" "${nodetag_user_loglist_json}" || ret=1
    done
    
    [ ${ret} -ne 0 ] && exit 14
    
    local nodetag_list=$(python ${pycommon} ${nodetag_user_loglist_json} "get_all_nodetag")
    if [ "X${nodetag_list}" == "XERROR" ];then
        log_echo "ERROR" "The nodetag_list=${nodetag_list} is ERROR.of ${FUNCNAME}."
        exit 15
    fi
    log_echo "INFO" "The nodetag_list=${nodetag_list}.of ${FUNCNAME}."
    ret=0
    echo "{}" > ${nodetag_hostinfo_json}
    for nodetag in $(echo "${nodetag_list}"|sed "s/,/ /g");do
        ## nodetag
        analysis_product_info "${nodetag}" "${nodetag_hostinfo_json}" || ret=1
    done
    
    [ ${ret} -ne 0 ] && exit 16
    
    log_echo "INFO" "The nodetag_list=${nodetag_list} analysis success.of ${FUNCNAME}."
    
    hostinfo_and_user_loglist_set "${nodetag_hostinfo_json}" "${nodetag_user_loglist_json}"
    
    log_echo "INFO" "The nodetag and hostinfo set success.of ${FUNCNAME}."
}

## json_file, key, get_obj_key1, get_obj_key2
function get_obj()
{
    local json_file="$1"
    local key="$2"
    local get_obj_key1="$3"
    local get_obj_key2="$4"
    error_message=""
    if [ -z "${key}" -o -z "${get_obj_key1}" ];then
        log_echo "ERROR" "The key=${key} or get_obj_key1=${get_obj_key1} is null.of ${FUNCNAME}"
        error_message="The key=${key} or get_obj_key1=${get_obj_key1} is null."
        return 1
    fi
    
    obj=$(python ${pycommon} ${json_file} "${key}" "${get_obj_key1}" "${get_obj_key2}")
    if [ -z "${obj}" ];then
        log_echo "ERROR" "The obj=${obj} is null."
        error_message="The obj=${obj} is null."
        return 1
    fi
    
    if [ "X${obj}" == "XERROR" ];then
        log_echo "ERROR" "The obj=${obj} is ERROR."
        error_message="The obj=${obj} is ERROR."
        return 1
    fi
    
    return 0
}

function check_ssh_node()
{
    local ssh_ip=$1
    if [ -z "${ssh_ip}" ];then
        log_echo "ERROR" "The ssh_ip=${ssh_ip} is null.of ${FUNCNAME}."
        return 1
    fi
    
    log_echo "INFO" "check ssh_ip=${ssh_ip} start,of ${FUNCNAME}" 
    local is_local_ip=$(ip addr |grep -iwF "${ssh_ip}")
    if [ "${ssh_ip}" == "127.0.0.1" -o ! -z "${is_local_ip}" ];then
        log_echo "INFO" "check ssh_ip=${ssh_ip} is local ip,not need to check it,of ${FUNCNAME}" 
        return 0
    fi
    
    local node_ossadm_pwd=$(tr -dc \"A-Za-z0-9\" </dev/random| head -c 12)
    echo "${node_ossadm_pwd} host:passwd\n" | ${common_path}/check_connection.exp "${ssh_port}" "${ssh_ip}" "echo Ok" >> ${exe_log_file} 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "check connection to ${ssh_ip} failed. local uniep to ${ssh_ip} not has trust relationship. of ${FUNCNAME}."
        return 1
    fi
    
    log_echo "INFO" "check ssh_ip=${ssh_ip} success,of ${FUNCNAME}" 
    return 0
}


function ssh_node_preset()
{
    local ssh_ip=$1
    local scp_ip=$1
    local todo=$2
    local logs_time=$3
    
    if [ -z "${ssh_ip}" -o -z "${todo}" -o -z "${logs_time}" ];then
        log_echo "ERROR" "The ssh_ip=${ssh_ip} or todo=${todo} or logs_time=${logs_time} is null.of ${FUNCNAME}."
        echo "ret=1" >> ${logs_size_ret}
        echo "ret_message=\"The ssh_ip=${ssh_ip} or todo=${todo} or logs_time=${logs_time} is null.of ${FUNCNAME}.\"" >> ${logs_size_ret}
        return 1
    fi
    
    log_echo "INFO" "preset ssh_ip=${ssh_ip} and todo=${todo} start,of ${FUNCNAME}" 
    local node_name=$(python ${pycommon} ${hostinfo_and_user_loglist_json} "get_nodename" "${ssh_ip}")
    local node_ret_path=${node_infocollect_preset}/${node_name}
    if [ ! -d ${node_ret_path} ];then
        mkdir -p ${node_ret_path}
    fi
    
    local tmp_ip=$(echo "${ssh_ip}" |sed "s/\./\\\./g")
    local is_local_ip=$(ip addr |grep -iw "${tmp_ip}")
    
    if [ "${ssh_ip}" == "127.0.0.1" -o ! -z "${is_local_ip}" ];then
        rm -rf ${tmp_dir:?}
        mkdir -p ${tmp_dir}
        cp -rpf ${common_path}/config_common.properties ${tmp_dir}
        cp -rpf ${common_path}/common.sh ${tmp_dir}
        cp -rpf ${common_path}/pycommon.py ${tmp_dir}
        cp -rpf ${common_path}/node_infocollect.sh ${tmp_dir}
        cp -rpf ${hostinfo_and_user_loglist_json} ${tmp_dir}
        
        chmod 755 -R ${tmp_dir}
        timeout 1800 sh ${tmp_dir}/node_infocollect.sh "${todo}" "${ssh_ip}" "${logs_time}" "local"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "connection to ${ssh_ip} execute cmd:[sh ${tmp_dir}/node_infocollect.sh ${todo} ${ssh_ip} ${logs_time}] failed.check log: ${node_log_file}"
            echo "ret=1" >> ${logs_size_ret}
            echo "ret_message=\"connection to ${ssh_ip} execute cmd:[sh ${tmp_dir}/node_infocollect.sh ${todo} ${ssh_ip} ${logs_time}] failed.check log: ${exe_log_file}\"" >> ${logs_size_ret}
            return 1
        fi
        rm -rf ${tmp_dir}/config_common.properties
        rm -rf ${tmp_dir}/common.sh
        rm -rf ${tmp_dir}/pycommon.py
        rm -rf ${tmp_dir}/node_infocollect.sh
        rm -rf ${tmp_dir}/dv_select_service_proce.sh
        rm -rf ${tmp_dir}/services_proce_result_tmp.csv
        
        cp -rf ${tmp_dir}/* ${node_ret_path}
        rm -rf ${tmp_dir:?}
        echo "ret=0" >> ${logs_size_ret}
        return 0
    fi
    
    echo "${ssh_ip}" | grep ":" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        scp_ip="[${ssh_ip}]"
    fi
    
    local node_ossadm_pwd=$(tr -dc \"A-Za-z0-9\" </dev/random| head -c 12)
    echo "${node_ossadm_pwd} host:passwd\n" | ${common_path}/check_connection.exp "${ssh_port}" "${ssh_ip}" "echo Ok" >> ${exe_log_file} 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "check connection to ${ssh_ip} failed. local uniep to ${ssh_ip} not has trust relationship. of ${FUNCNAME}."
        echo "ret_message=\"check connection to ${ssh_ip} failed. local uniep to ${ssh_ip} not has trust relationship. of ${FUNCNAME}\"" >> ${logs_size_ret}
        echo "ret=1" >> ${logs_size_ret}
        return 1
    fi
    
    ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip} rm -rf ${tmp_dir:?}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip} rm -rf ${tmp_dir}] failed.of ${FUNCNAME}."
        echo "ret=1" >> ${logs_size_ret}
        echo "ret_message=\"Execute cmd:[ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip} rm -rf ${tmp_dir}] failed.of ${FUNCNAME}.\"" >> ${logs_size_ret}
        return 1
    fi
    
    ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip} mkdir -p ${tmp_dir}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip} mkdir -p ${tmp_dir}] failed.of ${FUNCNAME}."
        echo "ret=1" >> ${logs_size_ret}
        echo "ret_message=\"Execute cmd:[ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip} mkdir -p ${tmp_dir}] failed.of ${FUNCNAME}.\"" >> ${logs_size_ret}
        return 1
    fi

    file_list="config_common.properties common.sh pycommon.py node_infocollect.sh stack.tag"
    for filename in ${file_list}
    do
        if [ -f "${common_path}/${filename}" ];then
            scp -o StrictHostKeyChecking=no -P ${ssh_port} ${common_path}/${filename} ossadm@${scp_ip}:${tmp_dir}
        fi
    done
    scp -o StrictHostKeyChecking=no -P ${ssh_port} ${hostinfo_and_user_loglist_json} ossadm@${scp_ip}:${tmp_dir}
    
    ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip} chmod 755 -R ${tmp_dir}
    
    ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip} timeout 1800 sh ${tmp_dir}/node_infocollect.sh "${todo}" "${ssh_ip}" "${logs_time}" "ssh">> ${exe_log_file} 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "connection to ${ssh_ip} execute cmd:[ ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip} sh ${tmp_dir}/node_infocollect.sh ${todo} ${ssh_ip} ${logs_time} ] failed.check log: ${exe_log_file} or login ${ssh_ip} node check log: ${node_log_file}"
        echo "ret=1" >> ${logs_size_ret}
        echo "ret_message=\"connection to ${ssh_ip} execute cmd:[ ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip} sh ${tmp_dir}/node_infocollect.sh ${todo} ${ssh_ip} ${logs_time} ] failed.check log: ${exe_log_file}\"" >> ${logs_size_ret}
        return 1
    fi
    
    need_remove_file="config_common.properties common.sh pycommon.py node_infocollect.sh node_infocollect.sh dv_select_service_proce.sh services_proce_result_tmp.csv services_proce_result_tmp.csv stack.tag"
    for file in ${need_remove_file[@]}
    do
        ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip} rm -rf ${tmp_dir:?}/${file:?}
    done
    
    
    scp -r -o StrictHostKeyChecking=no -P ${ssh_port} ossadm@${scp_ip}:${tmp_dir}/* ${node_ret_path} >> ${exe_log_file} 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "connection to ${ssh_ip} execute cmd:[scp -r -o StrictHostKeyChecking=no -P ${ssh_port} ossadm@${scp_ip}:${tmp_dir}/* ${node_ret_path}] failed.check log: ${exe_log_file}"
        echo "ret=1" >> ${logs_size_ret}
        echo "ret_message=\"connection to ${ssh_ip} execute cmd:[scp -r -o StrictHostKeyChecking=no -P ${ssh_port} ossadm@${scp_ip}:${tmp_dir}/* ${node_ret_path}] failed.check log: ${exe_log_file}\"" >> ${logs_size_ret}
        return 1
    fi
    
    ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip} rm -rf ${tmp_dir:?}
    echo "ret=0" >> ${logs_size_ret}
    return 0
}

function monitor_time_out()
{   
    local node_ip="$1"
    local time_out="$2"
    local monitor_pid="$3"
    if [ -z "${node_ip}" -o -z "${time_out}" -o -z "${monitor_pid}" ];then
        log_echo "INFO" "The node_ip=${node_ip} or time_out=${time_out} or monitor_pid=${monitor_pid} has null."
        return 0
    fi
    
    sleep ${time_out} &
    common_watch_dog_pid=$!
    echo "${common_watch_dog_pid}" >> ${common_path}/common_watch_dog_sleep_pid.list
    wait ${common_watch_dog_pid}
    
    log_echo "ERROR" "The monitor ${node_ip} pid ${monitor_pid} time out(${time_out})."
    touch ${common_path}/monitor_time_out.tag
    kill -9 ${monitor_pid} >/dev/null
    return 0
}

function check_task_status()
{
    local result_path="$1"
    log_echo "INFO" "The check_task_status start."
    if [ -z "${result_path}" ];then
        log_echo "ERROR" "The result_path=${result_path} is null."
        exit 21
    fi
    
    if [ ! -f "${result_path}" ];then
        log_echo "ERROR" "The result_path=${result_path} is not exists."
        exit 22
    fi
    
    echo "" > ${common_path}/common_watch_dog_sleep_pid.list
    rm -rf ${common_path}/monitor_time_out.tag
    ## 15 minutes = 15*60 = 900 Seconds
    local timeOut=900
    local tmp_ip=""
    local tmp_pid=""
    local tmp_pid_ret=0
    while read line;do
        if [ -z "${line}" ];then
            continue
        fi
        
        tmp_ip=$(echo "${line}" |awk -F'=' '{print $1}'|sed "s#[[:blank:]|[:cntrl:]]##g")
        tmp_pid=$(echo "${line}" |awk -F'=' '{print $2}'|sed "s#[[:blank:]|[:cntrl:]]##g")
        
        log_echo "INFO" "The tmp_ip=${tmp_ip} and tmp_pid=${tmp_pid} star wait it."
        ## ip timeout pid
        monitor_time_out "${tmp_ip}" "${timeOut}" "${tmp_pid}" &
        monitor_time_out_pid=$!
        wait ${tmp_pid}
        tmp_pid_ret=$?
        kill -9 ${monitor_time_out_pid} >/dev/null
        log_echo "INFO" "The tmp_ip=${tmp_ip} and tmp_pid=${tmp_pid} and tmp_pid_ret=${tmp_pid_ret} wait finished."
    done < ${result_path}
    
    if [ -f ${PreSet_PATH}/monitor_time_out.tag ];then
        log_echo "ERROR" "The check_task_status monitor has pid time out.please check."
        exit 23
    fi

    if [ "$tmp_pid_ret" == "1" ];then
        tmp_pid_ret=24
    fi
    if [ ${tmp_pid_ret} -ne 0 ];then
        log_echo "ERROR" "The check_task_status monitor has pid return result has error.please check."
        exit ${tmp_pid_ret}
    fi
    
    if [ -f ${common_path}/common_watch_dog_sleep_pid.list ];then
        while read line;do
            if [ -z "${line}" ];then
                continue
            fi
            
            echo "kill sleep pid ${line}." >> ${exe_log_file}
            
            kill -9 ${line} >/dev/null
        done < ${common_path}/common_watch_dog_sleep_pid.list
        log_echo "INFO" "kill ${common_path}/common_watch_dog_sleep_pid.list finish."
    fi
    
    log_echo "INFO" "The check_task_status finish."
}

function ssh_node_get_logs_size()
{
    ## ${hostinfo_and_user_loglist_json}
    local nodeip_list=$(source ${install_path}/manager/bin/engr_profile.sh;python ${pycommon} ${hostinfo_and_user_loglist_json} "get_all_nodeip")

    if [ "X${nodeip_list}" == "XERROR" ];then
        log_echo "ERROR" "The nodeip_list=${nodeip_list} is ERROR.of ${FUNCNAME}."
        exit 20
    fi
    
    local ret=0
    for nodeip in $(echo "${nodeip_list}" |sed "s/,/ /g");do
        check_ssh_node "${nodeip}" || ret=1
    done
    
    if [ ${ret} -ne 0 ];then
        log_echo "ERROR" "check ssh node failed.of ${FUNCNAME}."
        exit 201
    fi
    
    if [ -d ${node_infocollect_preset} ];then
        rm -rf ${node_infocollect_preset:?}
    fi
    
    mkdir -p ${node_infocollect_preset}
    echo "" > ${logs_size_ret}
    
    local check_result_path=${common_path}/check_result
    mkdir -p ${check_result_path}
    local check_ret=${check_result_path}/check_task_list.ret
    echo "" > ${check_ret}

    for nodeip in $(echo "${nodeip_list}" |sed "s/,/ /g");do
        ssh_node_preset "${nodeip}" "get_logs_size" "${collection_log_day_time}" &
        echo "${nodeip}=$!" >> ${check_ret}
    done

    check_task_status "${check_ret}"
    
    ## ${logs_size_ret}
    cat ${logs_size_ret} |grep "^ret=1" >> ${exe_log_file} 2>&1
    if [ $? -eq 0 ];then
        cat ${logs_size_ret} |grep "^ret_message=" |tee -a ${exe_log_file}
        clean_tmpfile
        exit 25
    fi
    
    log_echo "INFO" "ssh node get logs size finished.of ${FUNCNAME}"
}

function check_logs_size()
{
    ## ${hostinfo_and_user_loglist_json}
    local nodeip_list=$(python ${pycommon} ${hostinfo_and_user_loglist_json} "get_all_nodeip")
    if [ "X${nodeip_list}" == "XERROR" ];then
        log_echo "ERROR" "The nodeip_list=${nodeip_list} is ERROR.of ${FUNCNAME}."
        exit 26
    fi
    
    local node_ret_value=""
    local node_ret_message=""
    
    local node_ret=0
    local node_file=""
    local node_name=""
    local node_size=0
    local size_sum=0
    for nodeip in $(echo "${nodeip_list}" |sed "s/,/ /g");do
        node_name=$(python ${pycommon} ${hostinfo_and_user_loglist_json} "get_nodename" "${nodeip}")
        node_file="${node_infocollect_preset}/${node_name}/${node_ret_file}"
        if [ ! -f ${node_file} ];then
            log_echo "ERROR" "The node_file=${node_file} is not exists.of ${FUNCNAME}."
            node_ret=1
            continue
        fi
        
        node_ret_value=$(cat ${node_file}|grep "^${node_result_key}=" |awk -F"${node_result_key}=" '{print $2}')
        node_ret_message=$(cat ${node_file}|grep "^${node_result_message_key}=" |awk -F"${node_result_message_key}=" '{print $2}')
        if [ "X${node_ret_value}" == "XERROR" ];then
            log_echo "ERROR" "${node_ret_message}.of ${node_name}.of ${nodeip}"
            node_ret=1
            continue
        fi
        node_size=$(cat ${node_file}|grep "^${node_all_log_size_key}=" |awk -F"${node_all_log_size_key}=" '{print $2}')
        size_sum=$(( ${size_sum} + ${node_size} ))
        log_echo "INFO" "check node report in the: ${node_infocollect_preset}/${node_name}/${node_ret_report}"
    done
    
    if [ ${node_ret} -ne 0 ];then
        log_echo "ERROR" "The node_ret=${node_ret} is ERROR.of ${FUNCNAME}."
        exit 27
    fi
    
    local need_size=$(( ${size_sum} * 2 ))
    local avail=$(df -Pk ${collection_result_backup_path}|tail -1|awk -F' ' '{print $(NF-2)}')
    if [ ${avail} -lt ${need_size} ];then
        log_echo "ERROR" "The ${collection_result_backup_path} avail is ${avail} KB,but we need size ${need_size} KB."
        clean_tmpfile
        exit 28
    fi
    
    log_echo "INFO" "check logs size finished.of ${FUNCNAME}"
}

function check_file_exists()
{
    local log_path="$1"
    local cmd_title="$2"
    
    local tmp_cmd="${cmd_title} bash -c \"ls -d ${log_path}\" >>${exe_log_file} 2>&1"
    eval ${tmp_cmd}
    if [ $? -ne 0 ];then
        log_echo "INFO" "The path=${log_path} is not exists."
        return 1
    fi
    
    tmp_cmd="${cmd_title} bash -c \"[ -d ${log_path} ] && echo Yes || echo No\""
    local tmp_d_ret=$(eval ${tmp_cmd})
    
    tmp_cmd="${cmd_title} bash -c \"[ -f ${log_path} ] && echo Yes || echo No\""
    local tmp_f_ret=$(eval ${tmp_cmd})
    
    if [ "X${tmp_d_ret}" == "XYes" -o "X${tmp_f_ret}" == "XYes" ];then
        log_echo "INFO" "The path=${log_path} is exists.tmp_d_ret=${tmp_d_ret} tmp_f_ret=${tmp_f_ret}"
        return 0
    fi
    
    log_echo "INFO" "The path=${log_path} is not exists.tmp_d_ret=${tmp_d_ret} tmp_f_ret=${tmp_f_ret}"
    return 1
}

function get_local_log()
{
    local tmp_user="$1"
    local tmp_log="$2"
    log_echo "INFO" "The ${FUNCNAME} start.of tmp_log=${tmp_log}"
    
    local cmd_title=""
    if [ "${tmp_user}" != "${execute_user}" ];then
        cmd_title="sudo -u ${tmp_user}"
    fi
    
    check_file_exists "${tmp_log}" "${cmd_title}" || return 0
    
    local file_name=$(basename ${tmp_log})
    local dir=$(dirname ${tmp_log})
    
    generate_stack_log "${cmd_title}" "${tmp_log}"

    if [ ! -d ${node_logs_backup}${dir} ];then
        mkdir -p ${node_logs_backup}${dir}
    fi
    chmod 770 -R ${node_logs_backup} > /dev/null 2>&1
    
    local cmd=""
    if [ "${tmp_user}" == "dbuser" ];then
        if [ ! -d ${db_logs_backup_tmp}${dir} ];then
            mkdir -p ${db_logs_backup_tmp}${dir}
        fi
        chmod 770 -R ${db_logs_backup_tmp} > /dev/null 2>&1
        
        cmd="${cmd_title} cp -rf ${tmp_log} ${db_logs_backup_tmp}${dir}"
        eval ${cmd}
        
        cmd="${cmd_title} chmod 770 ${db_logs_backup_tmp}${dir}/${file_name} > /dev/null 2>&1"
        eval ${cmd}
        
        cp -rf ${db_logs_backup_tmp}${dir}/${file_name} ${node_logs_backup}${dir}
        chmod 770 ${node_logs_backup}${dir}/${file_name}  > /dev/null 2>&1
        
        cmd="${cmd_title} rm -rf ${db_logs_backup_tmp:?}${dir:?}/${file_name:?}"
        eval ${cmd}
        
        return 0
    fi
    
    if [ "${tmp_user}" == "ossuser" ];then
        cmd="${cmd_title} cp -rf ${tmp_log} ${node_logs_backup}${dir}/${file_name}.tmp_file"
        eval ${cmd}
        cmd="${cmd_title} chmod 770 ${node_logs_backup}${dir}/${file_name}.tmp_file > /dev/null 2>&1"
        eval ${cmd}
        
        cp -rf ${node_logs_backup}${dir}/${file_name}.tmp_file ${node_logs_backup}${dir}/${file_name}
        cmd="${cmd_title} rm -rf ${node_logs_backup}${dir}/${file_name}.tmp_file"
        eval ${cmd}
    else
        cmd="${cmd_title} cp -rf ${tmp_log} ${node_logs_backup}${dir}/${file_name}"
        eval ${cmd}
    fi
    
    cmd="${cmd_title} chmod 770 ${node_logs_backup}${dir}/${file_name} > /dev/null 2>&1"
    eval ${cmd}
    log_echo "INFO" "The ${FUNCNAME} finished.of tmp_log=${tmp_log}"
    return 0
}

function ssh_node_get_logs()
{
    local ssh_ip=$1
    local scp_ip=$1
    
    if [ -z "${ssh_ip}" ];then
        log_echo "ERROR" "The ssh_ip=${ssh_ip} is null.of ${FUNCNAME}."
        echo "ret=1" >> ${logs_size_ret}
        echo "ret_message=\"The ssh_ip=${ssh_ip} is null.of ${FUNCNAME}.\"" >> ${logs_size_ret}
        return 1
    fi
    
    log_echo "INFO" "preset ssh_ip=${ssh_ip} and todo=${todo} start,of ${FUNCNAME}" 
    local node_name=$(python ${pycommon} ${hostinfo_and_user_loglist_json} "get_nodename" "${ssh_ip}")
    local node_ret_path=${node_infocollect_preset}/${node_name}
    local tmp_node_ret_logs=${node_ret_path}/${node_ret_logs}
    if [ ! -d ${node_ret_path} ];then
        log_echo "ERROR" "The node_ret_path=${node_ret_path} is not exists.of ${FUNCNAME}."
        echo "ret=1" >> ${logs_size_ret}
        echo "ret_message=\"The node_ret_path=${node_ret_path} is not exists.of ${FUNCNAME}.\"" >> ${logs_size_ret}
        return 1
    fi
    
    if [ ! -f ${tmp_node_ret_logs} ];then
        log_echo "ERROR" "The tmp_node_ret_logs=${tmp_node_ret_logs} is not exists.of ${FUNCNAME}."
        echo "ret=1" >> ${logs_size_ret}
        echo "ret_message=\"The tmp_node_ret_logs=${tmp_node_ret_logs} is not exists.of ${FUNCNAME}.\"" >> ${logs_size_ret}
        return 1
    fi
    
    node_logs_backup=${collection_result_backup_path}/all_node/${node_name}
    if [ -d ${node_logs_backup} ];then
        rm -rf ${node_logs_backup:?}
    fi
    
    mkdir -p ${node_logs_backup}
    chmod 770 ${node_logs_backup} > /dev/null 2>&1
    
    local tmp_ip=$(echo "${ssh_ip}" |sed "s/\./\\\./g")
    local is_local_ip=$(ip addr |grep -iw "${tmp_ip}")
    
    local get_local_log_pid_list="${common_path}/get_local_log_pid.list"
    local tmp_user=""
    local tmp_log=""
    if [ "${ssh_ip}" == "127.0.0.1" -o ! -z "${is_local_ip}" ];then
        echo "" >> ${get_local_log_pid_list}
        while read line;do
            if [ -z "${line}" ];then
                continue
            fi
            
            tmp_user=$(echo "${line}" |awk -F',' '{print $3}')
            tmp_log=$(echo "${line}" |awk -F',' '{print $4}')
            
            ## is title line,continue.
            if [ "${tmp_user}" == "user" -o "${tmp_log}" == "log" ];then
                continue
            fi
            
            get_local_log "${tmp_user}" "${tmp_log}" &
            get_local_log_pid=$!
            echo "${get_local_log_pid}" >> ${get_local_log_pid_list}
        done < ${tmp_node_ret_logs}
        
        
        while read line;do
            if [ -z "${line}" ];then
                continue
            fi
            echo "start wait ${line}..." >> ${exe_log_file}
            wait ${line}
            echo "wait ${line} finished." >> ${exe_log_file}
        done < ${get_local_log_pid_list}
        
        if [ -d ${db_logs_backup_tmp} ];then
            rm -rf ${db_logs_backup_tmp:?}
        fi
        
        echo "ret=0" >> ${logs_size_ret}
        
        log_echo "INFO" "The get logs finished,of ${FUNCNAME}" 
        return 0
    fi
    
    log_echo "INFO" "to node ${ssh_ip} get logs start,of ${FUNCNAME}" 
    echo "${ssh_ip}" | grep ":" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        scp_ip="[${ssh_ip}]"
    fi
    
    scp -r -o StrictHostKeyChecking=no -P ${ssh_port} ossadm@${scp_ip}:${logs_backup_tmp}/* ${node_logs_backup} >> ${exe_log_file} 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "connection to ${ssh_ip} execute cmd:[scp -r -o StrictHostKeyChecking=no -P ${ssh_port} ossadm@${scp_ip}:${logs_backup_tmp}/* ${node_logs_backup}] failed.check log: ${exe_log_file}"
        echo "ret=1" >> ${logs_size_ret}
        echo "ret_message=\"connection to ${ssh_ip} execute cmd:[scp -r -o StrictHostKeyChecking=no -P ${ssh_port} ossadm@${scp_ip}:${logs_backup_tmp}/* ${node_logs_backup}] failed.check log: ${exe_log_file}\"" >> ${logs_size_ret}
        return 1
    fi
    
    ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip} rm -rf ${logs_backup_tmp:?}
    echo "ret=0" >> ${logs_size_ret}
    log_echo "INFO" "to node ${ssh_ip} get logs finished,of ${FUNCNAME}" 
    return 0
}

function generate_stack_log()
{
    local cmd_title="$1"
    local file_path="$2"
    local is_file_exist=$(${cmd_title} ls ${file_path} 2>/dev/null)
    if [ -z "${is_file_exist}" ];then
        log_echo "INFO" "This node not match ${file_path}."
        return 0
    fi
    if [ "${ssh_ip}" == "127.0.0.1" -o ! -z "${is_local_ip}" ];then
        stack_tag_file=${common_path}/stack.tag
    else
        stack_tag_file=${tmp_dir}/stack.tag
    fi
    if [ -f "${stack_tag_file}" ];then
        IS_COLLECT_STACK=$(cat ${stack_tag_file} |grep "^IS_COLLECT_STACK" |awk -F "=" '{print $2}')
        PROCESS_NAME_LIST=$(cat ${stack_tag_file} |grep "^ProcessName" |awk -F "=" '{print $2}')
    else
        log_echo "INFO" "This Scenario is normaly. No need to collect stack log."
        return 0
    fi

    log_echo "INFO" "Start to collect stack log..."
    source /opt/oss/manager/bin/engr_profile.sh >/dev/null 2>&1

    local non_service_process_list=(I2000 iCnfg I2000_CIE_DMU I2000_CIE_MQ sysmonitor I2000_app_agent)
    for process_name in $(echo "${PROCESS_NAME_LIST}" |sed "s/,/ /g")
    do 
        local service_name=${process_name}
        echo ${non_service_process_list[@]} | grep -wq ${process_name}
        if [ $? -eq 0 ];then
            service_name="DVEngineeringService"
        fi
        if [ -z $(echo ${file_path} |grep "${service_name}") ];then
            continue
        fi
        ipmc_adm -cmd statusapp -quick |grep "RUNNING" |grep "${service_name}"
        if [ $? -eq 0 ];then
            ${cmd_title} rm -rf ${file_path}.dvbak
            ${cmd_title} cp -pf ${file_path} ${file_path}.dvbak
            ${cmd_title} bash -c "echo ' ' > ${file_path}"
            local job_pid=$(${cmd_title} ps -ef |grep "java" |grep -w "\-Dprocname=${process_name}" |awk -F " " '{print $2}')
            [ -z "${job_pid}" ] && job_pid=$(${cmd_title} ps -ef |grep -w "\-DNFW=${process_name}" |grep -v grep |awk -F " " '{print $2}')
            
            if [ -z "${job_pid}" ];then
                log_echo "ERROR" "The job pid is null. Please check."
                exit 40
            fi
            for ((i=1;i<=3;i++))
            do
                ${cmd_title} kill -3 ${job_pid}
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "exec kill command failed. check ${process_name} process is OK or not..."
                    exit 41
                fi
                sleep 3
            done
        else
            log_echo "INFO" "[${process_name}] This service not running."
        fi
    done
    log_echo "INFO" "exec ${FUNCTION_NAME} finish."
    return 0
}

function execute_node_infocollect()
{
    local ssh_ip=$1
    local scp_ip=$1
    local todo=$2
    local command1=$3
    local command2=$4

    local ret=0;

    echo "${scp_ip}" | grep ":" | grep -v "\[" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        scp_ip="[${scp_ip}]"
    fi

    local ssh_prefix_cmd;
    if [ "${ssh_ip}" == "127.0.0.1" -o ! -z "${is_local_ip}" ];then
        ssh_prefix_cmd=""
    else
        ssh_prefix_cmd="ssh -o StrictHostKeyChecking=no -p ${ssh_port} ${ssh_ip}"
        scp_prefix_cmd="scp -r -o StrictHostKeyChecking=no -P ${ssh_port}"
    fi

    ## init tmp dir
    ${ssh_prefix_cmd} rm -rf ${tmp_dir:?}
    if [ $? -ne 0 ];then
        log_sysnc_error "on ${ssh_ip} todo rm -rf ${tmp_dir} failed.of ${FUNCNAME}."
        return 1
    fi
    ${ssh_prefix_cmd} mkdir -p ${tmp_dir}

    ## copy file to tmp dir
    local file_list="config_common.properties common.sh pycommon.py node_infocollect.sh redis_utils.py"
    if [ "${ssh_ip}" == "127.0.0.1" -o ! -z "${is_local_ip}" ];then
        for filename in ${file_list}
        do
            cp -pf "${common_path}/${filename}" "${tmp_dir}"
        done
        cp -rpf "${hostinfo_and_user_loglist_json}" "${tmp_dir}"
        cp -rpf "${archive_path}/site_feature_shell.ini" "${archive_path}/site_config.ini" "${tmp_dir}"

        echo "${collection_scene}" |grep -w "site_base\|site_all" >> ${exe_log_file} 2>&1
        if [ $? -eq 0 ];then
            find ${main_path}/select_service_proce -type f |xargs -i cp -rpf {} ${tmp_dir}
        fi
    else
        for filename in ${file_list}
        do
            ${scp_prefix_cmd} "${common_path}/${filename}" "ossadm@${scp_ip}:${tmp_dir}"
        done
        ${scp_prefix_cmd} "${hostinfo_and_user_loglist_json}" "ossadm@${scp_ip}:${tmp_dir}"
        ${scp_prefix_cmd} "${archive_path}/site_feature_shell.ini" "${archive_path}/site_config.ini" "ossadm@${scp_ip}:${tmp_dir}"

        echo "${collection_scene}" |grep -w "site_base\|site_all" >> ${exe_log_file} 2>&1
        if [ $? -eq 0 ];then
            find ${main_path}/select_service_proce -type f |xargs -i scp -r -o StrictHostKeyChecking=no -P ${ssh_port} {} ossadm@${scp_ip}:${tmp_dir}
        fi
    fi
    ${ssh_prefix_cmd} chmod 755 -R ${tmp_dir}

    if [ "${todo}" == "get_all" ] || [ "${todo}" == "get_sysinfo" ] || [ "${todo}" == "get_feature" ] || [ "${todo}" == "get_config" ];then
        ## excute stack collect
        ${ssh_prefix_cmd} timeout 1800 sh ${tmp_dir}/node_infocollect.sh "${todo}" "${ssh_ip}" "${logs_time}" "local" >> "${exe_log_file}"
        ret=$?
        if [ $ret -ne 0 ];then
            log_sysnc_error "connection to ${ssh_ip} execute cmd:[${ssh_prefix_cmd} sh ${tmp_dir}/node_infocollect.sh ${todo} ${ssh_ip} ${logs_time} local] failed.check log: ${exe_log_file}"
            return $ret
        fi
    elif [ "${todo}" == "collect_redis" ];then
        ## excute redis collect
        ${ssh_prefix_cmd} timeout 1800 sh ${tmp_dir}/node_infocollect.sh "${todo}" "${command1}" "${command2}" "local" >> "${exe_log_file}"
        ret=$?
        if [ $ret -ne 0 ];then
            log_sysnc_error "connection to ${ssh_ip} execute cmd:[${ssh_prefix_cmd} sh ${tmp_dir}/node_infocollect.sh ${todo} ${command1} ${command2} local] failed.check log: ${exe_log_file}"
            return $ret
        fi
    fi

    ## clean tmp_dir and copy log files to target dir
    ${ssh_prefix_cmd} rm -rf ${tmp_dir}/config_common.properties ${tmp_dir}/common.sh ${tmp_dir}/pycommon.py ${tmp_dir}/node_infocollect.sh ${tmp_dir}/dv_select_service_proce.sh ${tmp_dir}/services_proce_result_tmp.csv ${tmp_dir}/log  ${tmp_dir}/redis_utils.py

    if [ "${ssh_ip}" == "127.0.0.1" -o ! -z "${is_local_ip}" ];then
        cp -rf ${tmp_dir}/* "${node_ret_path}"
    else
        ${scp_prefix_cmd} ossadm@${scp_ip}:${tmp_dir}/* "${node_ret_path}" >> "${exe_log_file}" 2>&1
        if [ $? -ne 0 ];then
            log_sysnc_error "connection to ${ssh_ip} execute cmd:[${scp_prefix_cmd} ${ssh_port} ossadm@${scp_ip}:${tmp_dir}/* ${node_ret_path}] failed.check log: ${exe_log_file}"
            return 1
        fi
    fi

    ${ssh_prefix_cmd} rm -rf ${tmp_dir:?}
    echo "ret=0" >> ${logs_size_ret}
    return 0
}

function ssh_node_get_archive()
{
    local ssh_ip=$1
    local todo=$2
    local command1=$3
    local command2=$4

    if [ -z "${ssh_ip}" -o -z "${todo}" ];then
        log_sysnc_error "The ssh_ip=${ssh_ip} or todo=${todo} is null.of ${FUNCNAME}."
        return 1
    fi

    log_echo "INFO" "preset ssh_ip=${ssh_ip} and todo=${todo} start,of ${FUNCNAME}"
    local node_name=$(source ${install_path}/manager/bin/engr_profile.sh;python ${pycommon} ${hostinfo_and_user_loglist_json} "get_nodename" "${ssh_ip}")
    local node_ret_path=${node_infocollect_preset}/${node_name}
    if [ ! -d ${node_ret_path} ];then
        mkdir -p ${node_ret_path}
    fi

    execute_node_infocollect "${ssh_ip}" "${todo}" "${command1}" "${command2}"
    local ret=$?
    if [ $ret -ne 0 ];then
        log_sysnc_error "execute_node_infocollect failed."
        return $ret
    fi

    return 0
}

function get_redis()
{
    log_echo "INFO" "The ${FUNCNAME} start."

    if [ "${collection_scene}" != "redis" ];then
        log_echo "INFO" "No involve ${FUNCNAME}, skip."
        return 0
    fi

    local nodeip_list=$(source ${install_path}/manager/bin/engr_profile.sh;python ${pycommon} ${hostinfo_and_user_loglist_json} "get_all_nodeip")
    if [ "X${nodeip_list}" == "XERROR" ];then
        log_echo "ERROR" "The nodeip_list=${nodeip_list} is ERROR.of ${FUNCNAME}."
        exit 29
    fi

    local ret=0
    for nodeip in $(echo "${nodeip_list}" |sed "s/,/ /g");do
        check_ssh_node "${nodeip}" || ret=1
    done
    if [ ${ret} -ne 0 ];then
        log_echo "ERROR" "check ssh node failed.of ${FUNCNAME}."
        exit 201
    fi

    if [ -d ${main_path}/node_infocollect_preset ];then
        rm -rf ${main_path}/node_infocollect_preset
    fi

    mkdir -p ${main_path}/node_infocollect_preset
    echo "" > ${logs_size_ret}
    local check_result_path=${common_path}/check_result
    mkdir -p ${check_result_path}
    local check_ret=${check_result_path}/check_task_list.ret
    echo "" > ${check_ret}

    local todo;

    [ "${collection_scene}" == "redis" ] && todo="collect_redis"
    for nodeip in $(echo "${nodeip_list}" |sed "s/,/ /g");do
        ssh_node_get_archive "${nodeip}" "${todo}" "${collect_redis_instance}" "${collect_redis_key}" &
        echo "${nodeip}=$!" >> ${check_ret}
    done

    check_task_status "${check_ret}"

    ## ${logs_size_ret}
    grep "^ret=1" "${logs_size_ret}" >> ${exe_log_file} 2>&1
    if [ $? -eq 0 ];then
        grep "^ret_message=" "${logs_size_ret}" |tee -a ${exe_log_file}
        exit 42
    fi
    log_echo "INFO" "ssh node get logs size finished.of ${FUNCNAME}"
}

function get_all_archive()
{
    log_echo "INFO" "The ${FUNCNAME} start."

    if [ "${collection_scene}" != "site_all" ] && [ "${collection_scene}" != "site_base" ] && [ "${collection_scene}" != "site_feature" ] && [ "${collection_scene}" != "site_config" ];then
        log_echo "INFO" "No involve ${FUNCNAME}, skip."
        return 0
    fi

    ## ${hostinfo_and_user_loglist_json}
    local nodeip_list=$(source ${install_path}/manager/bin/engr_profile.sh;python ${pycommon} ${hostinfo_and_user_loglist_json} "get_all_nodeip")
    if [ "X${nodeip_list}" == "XERROR" ];then
        log_echo "ERROR" "The nodeip_list=${nodeip_list} is ERROR.of ${FUNCNAME}."
        exit 100
    fi

    local ret=0
    for nodeip in $(echo "${nodeip_list}" |sed "s/,/ /g");do
        check_ssh_node "${nodeip}" || ret=1
    done

    if [ ${ret} -ne 0 ];then
        log_echo "ERROR" "check ssh node failed.of ${FUNCNAME}."
        exit 101
    fi

    if [ -d ${main_path}/node_infocollect_preset ];then
        rm -rf ${main_path}/node_infocollect_preset
    fi

    mkdir -p ${main_path}/node_infocollect_preset
    echo "" > ${logs_size_ret}

    local check_result_path=${common_path}/check_result
    mkdir -p ${check_result_path}
    local check_ret=${check_result_path}/check_task_list.ret
    echo "" > ${check_ret}

    local todo;
    [ "${collection_scene}" == "site_all" ] && todo="get_all"
    [ "${collection_scene}" == "site_base" ] && todo="get_sysinfo"
    [ "${collection_scene}" == "site_feature" ] && todo="get_feature"
    [ "${collection_scene}" == "site_config" ] && todo="get_config"

    for nodeip in $(echo "${nodeip_list}" |sed "s/,/ /g");do
        ssh_node_get_archive "${nodeip}" "${todo}" "" "" &
        echo "${nodeip}=$!" >> ${check_ret}
    done

    check_task_status "${check_ret}"

    if [ "${collection_scene}" == "site_base" ] || [ "${collection_scene}" == "site_all" ];then
        get_and_collect_basic_site_infomation
    fi

    ## ${logs_size_ret}
    cat ${logs_size_ret} |grep "^ret=1" >> ${exe_log_file} 2>&1
    if [ $? -eq 0 ];then
        cat ${logs_size_ret} |grep "^ret_message=" |tee -a ${exe_log_file}
        exit 25
    fi

    log_echo "INFO" "ssh node get logs size finished.of ${FUNCNAME}"
}

function get_all_node_logs()
{
    log_echo "INFO" "The ${FUNCNAME} start."
    local nodeip_list=$(python ${pycommon} ${hostinfo_and_user_loglist_json} "get_all_nodeip")
    if [ "X${nodeip_list}" == "XERROR" ];then
        log_echo "ERROR" "The nodeip_list=${nodeip_list} is ERROR.of ${FUNCNAME}."
        exit 29
    fi
    
    if [ ! -d ${node_infocollect_preset} ];then
        log_echo "ERROR" "The node_infocollect_preset=${node_infocollect_preset} is not exists.of ${FUNCNAME}."
        exit 30
    fi
    
    echo "" > ${logs_size_ret}
    local check_result_path=${common_path}/check_result
    mkdir -p ${check_result_path}
    local check_ret=${check_result_path}/check_task_list.ret
    echo "" > ${check_ret}
    for nodeip in $(echo "${nodeip_list}" |sed "s/,/ /g");do
        ssh_node_get_logs "${nodeip}" &
        echo "${nodeip}=$!" >> ${check_ret}
    done
    
    check_task_status "${check_ret}"
    cat ${logs_size_ret} |grep "^ret=1" >> ${exe_log_file} 2>&1
    if [ $? -eq 0 ];then
        cat ${logs_size_ret} |grep "^ret_message=" |tee -a ${exe_log_file}
        clean_tmpfile
        exit 31
    fi
    
    log_echo "INFO" "The ${FUNCNAME} finished."
}

function site_archive_path_anonymization()
{
    for nodeName in $(ls ${path});
    do
        if [ -z "${nodeName}" ];then
            continue
        fi

        if [ -d ${path}/${nodeName}/var/log/oss/ ];then
            mv ${path}/${nodeName}/var/log/oss ${path}/${nodeName}/LOG_PATH
        fi
        if [ -d ${path}/${nodeName}/opt/oss/log/ ];then
            mv ${path}/${nodeName}/opt/oss/log ${path}/${nodeName}/LOG_PATH
        fi
        if [ -d ${path}/${nodeName}/opt/oss ];then
            mv ${path}/${nodeName}/opt/oss ${path}/${nodeName}/INSTALL_PATH
        fi

        if [ -d ${path}/${nodeName}/INSTALL_PATH/infocollect/export_product_info/ ];then
            mv ${path}/${nodeName}/INSTALL_PATH/infocollect/* ${path}/${nodeName}/
        fi
        if [ -d ${path}/${nodeName}/LOG_PATH/manager/infocollect/ ];then
            mv ${path}/${nodeName}/LOG_PATH/manager/infocollect/* ${path}/${nodeName}/
        fi
        if [ -d ${path}/${nodeName}/LOG_PATH/select_service_proce/ ];then
            mv ${path}/${nodeName}/LOG_PATH/select_service_proce/ ${path}/${nodeName}/select_service_proce/
        fi
    done

    local tmp_ini_file="${ini_file}.tmp"
    [ -f ${tmp_ini_file} ] && rm ${tmp_ini_file}
    cp -rpf ${ini_file} ${tmp_ini_file}
    ## /opt/oss /opt/zenith /var/log
    sed -i "s#/var/log/oss/#/LOG_PATH/#g" ${tmp_ini_file}
    sed -i "s#/opt/oss/log/#/LOG_PATH/#g" ${tmp_ini_file}
    sed -i "s#/opt/oss/#/INSTALL_PATH/#g" ${tmp_ini_file}

    sed -i "s#/INSTALL_PATH/infocollect/#/#g" ${tmp_ini_file}
    sed -i "s#/LOG_PATH/manager/infocollect/#/#g" ${tmp_ini_file}
    sed -i "s#/LOG_PATH/select_service_proce/#/select_service_proce/#g" ${tmp_ini_file}

    source ${tmp_ini_file}
    log_echo "INFO" "The ${FUNCNAME} finished."
    return 0
}

function path_anonymization()
{
    local path="$1"
    local ini_file="$2"
    if [ -z "${path}" -o -z "${ini_file}" ];then
        log_echo "ERROR" "The scene=${scene} or ini_file=${ini_file} has null.of ${FUNCNAME}."
        return 1
    fi
    
    if [ ! -d ${path} ];then
        log_echo "ERROR" "The path=${path} is not exists.of ${FUNCNAME}."
        return 1
    fi

    if [ "${scene}" == "site_base" ] || [ "${scene}" == "site_all" ];then
        site_archive_path_anonymization
        return 0
    fi

    for nodeName in $(ls ${path});
    do
        if [ -z "${nodeName}" ];then
            continue
        fi
        
        if [ -d ${path}/${nodeName}/opt/oss ];then
            mv ${path}/${nodeName}/opt/oss ${path}/${nodeName}/INSTALL_PATH
        fi
        
        if [ -d ${path}/${nodeName}/opt/zenith ];then
            mv ${path}/${nodeName}/opt/zenith ${path}/${nodeName}/DB_PATH
        fi
        
        if [ -d ${path}/${nodeName}/var/log ];then
            mv ${path}/${nodeName}/var/log ${path}/${nodeName}/LOG_PATH
        fi

        if [ "${scene}" == "redis" ] && [ -d ${path}/${nodeName}/*/*/manager/infocollect/redis ];then
            mv ${path}/${nodeName}/*/*/manager/infocollect/redis ${path}/${nodeName}/REDIS
        fi
    done
    
    local tmp_ini_file="${ini_file}.tmp"
    [ -f ${tmp_ini_file} ] && rm ${tmp_ini_file}
    cp -rpf ${ini_file} ${tmp_ini_file}
    ## /opt/oss /opt/zenith /var/log
    sed -i "s#/opt/oss/#/INSTALL_PATH/#g" ${tmp_ini_file}
    sed -i "s#/opt/zenith/#/DB_PATH/#g" ${tmp_ini_file}
    sed -i "s#/var/log/#/LOG_PATH/#g" ${tmp_ini_file}

    if [ "${scene}" == "redis" ];then
        sed -i "s#/INSTALL_PATH/log/manager/infocollect/redis/#/REDIS/#g" ${tmp_ini_file}
        sed -i "s#/LOG_PATH/oss/manager/infocollect/redis/#/REDIS/#g" ${tmp_ini_file}
    fi

    source ${tmp_ini_file}
    log_echo "INFO" "The ${FUNCNAME} finished."
    return 0
}

function tar_scene_logs_pkg()
{
    local scene="$1"
    if [ -z "${scene}" ];then
        log_echo "ERROR" "The scene=${scene} is null.of ${FUNCNAME}."
        return 1
    fi
    if [ "x${IS_COLLECT_STACK}" == "xtrue" ];then
        local collect_log_type="stack_"
    fi
    local pkg_name="infocollect_${collect_log_type}${scene}_${log_collection_start_time}-${log_collection_end_time}.zip"
    if [ ! -z "${collection_file_name}" ];then
        echo "${collection_file_name}" |grep "_${scene}_" >> ${exe_log_file} 2>&1
        if [ $? -eq 0 ];then
            pkg_name="${collection_file_name}"
        fi
    fi
    
    local zip_path=${collection_result_backup_path}/${pkg_name}
    if [ -f ${zip_path} ];then
        rm -rf ${zip_path:?}
    fi
    
    if [ -z "${collection_file_name}" ];then
        rm -rf ${collection_result_backup_path}/infocollect_${collect_log_type}${scene}_*.zip >> ${exe_log_file} 2>&1
    fi
    
    local ret=""
    local execute_ret=0
    local scene_ini=${feature_custom_cfg}/${scene}.ini
    
    path_anonymization "${collection_result_backup_path}/all_node" "${scene_ini}" || return 1
    
    cd ${collection_result_backup_path}/all_node
    local nodename_list=""
    local tmp_log=""
    for index in $(seq 1 ${LogCount});
    do
        if [ "x${IS_COLLECT_STACK}" == "xtrue" ] && [ "x${ProcessName[index]}" == "x" ];then
            log_echo "INFO" "This is stack collection job. No need to collect nomal log."
            continue
        elif [ "x${IS_COLLECT_STACK}" != "xtrue" ] && [ "x${ProcessName[index]}" != "x" ];then
            log_echo "INFO" "This is nomal collection job. No need to collect stack log."
            continue
        fi
        cat ${nodetag_hostinfo_json} |grep -w "${NodeTag[${index}]}" >> ${exe_log_file} 2>&1
        if [ $? -ne 0 ];then
            log_echo "INFO" "The nodetag_hostinfo_json=${nodetag_hostinfo_json} not found the nodetag=${NodeTag[${index}]} ,The service[${NodeTag[${index}]}] is not installed in the system.skip it. of ${FUNCNAME}."
            continue
        fi
        
        nodename_list=$(python ${pycommon} ${nodetag_hostinfo_json} "get_nodename_of_nodetag" "${NodeTag[${index}]}")
        if [ "X${nodename_list}" == "XERROR" ];then
            log_echo "ERROR" "The nodename_list=${nodename_list} is ERROR.[${nodetag_hostinfo_json} ${NodeTag[${index}]}] of ${FUNCNAME}."
            execute_ret=1
            continue
        fi
        
        for nodename in $(echo "${nodename_list}" |sed "s/,/ /g");do
            for log in $(echo \"${LogList[${index}]}\" |sed 's/,/" "/g');do
                log=$(echo ${log}|sed "s/\"//g")
                ls -d ${nodename}${log} >>${exe_log_file} 2>&1
                if [ $? -ne 0 ];then
                    log_echo "INFO" "The path=${nodename}${log} is not exists."
                    continue
                fi
                
                if [ "${scene}" == "disaster" ];then
                    log_echo "INFO" "The scene=${scene},is disaster,zip it all log."
                    zip -r ${zip_path} ${nodename}${log} >> ${exe_log_file} 2>&1
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "The execute cmd:[cd ${collection_result_backup_path}/all_node;zip -r ${zip_path} ${nodename}${log}] failed.of ${FUNCNAME}. The file is: ${scene_ini} , the index is : [${index}]"
                        execute_ret=1
                    fi
                    continue
                fi
                
                tmp_log=$(ls -d ${nodename}${log})
                for log in ${tmp_log};do
                    zip -r ${zip_path} ${log} >> ${exe_log_file} 2>&1
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "The execute cmd:[cd ${collection_result_backup_path}/all_node;zip -r ${zip_path} ${log}] failed.of ${FUNCNAME}. The file is: ${scene_ini} , the index is : [${index}]"
                        execute_ret=1
                    fi
                    
                    is_file=$([ -f ${log} ] && echo Yes || echo No)
                    if [ "${is_file}" == "Yes" ];then
                        tmp_log_path=$(dirname ${log})
                        tmp_log_file_fullname=$(basename ${log})
                        
                        tmp_log_file_suffix=$(echo "${tmp_log_file_fullname}" | awk -F'.' '{print $NF}')
                        tmp_log_file_name=$(echo "${tmp_log_file_fullname}" | awk -F".${tmp_log_file_suffix}" '{print $1}')
                        
                        dump_files=""
                        ls -d ${tmp_log_path}/${tmp_log_file_name}_*.zip >> ${exe_log_file} 2>&1
                        if [ $? -ne 0 ];then
                            log_echo "INFO" "The ${tmp_log_path}/${tmp_log_file_name}_*.zip is not exists."
                        else
                            dump_files=$(ls -d ${tmp_log_path}/${tmp_log_file_name}_*.zip | grep "${tmp_log_file_name}_[0-9]\{8,\}.zip\$")
                            log_echo "INFO" "The dump_files=${dump_files},log=${log}.of ${FUNCNAME}"
                        fi
                        
                        if [ -z "${dump_files}" ];then 
                            ls -d ${tmp_log_path}/${tmp_log_file_fullname}.*.gz >> ${exe_log_file} 2>&1
                            if [ $? -ne 0 ];then
                                log_echo "INFO" "The ${tmp_log_path}/${tmp_log_file_fullname}.*.gz is not exists."
                            else
                                dump_files=$(ls -d ${tmp_log_path}/${tmp_log_file_fullname}.*.gz)
                                log_echo "INFO" "The dump_files=${dump_files},log=${log}.of ${FUNCNAME}"
                            fi
                        fi
                        
                        if [ -z "${dump_files}" ];then
                            log_echo "INFO" "The dump_files=${dump_files} is null,log=${log}.of ${FUNCNAME}"
                            continue
                        fi
                        
                        for dump_log in ${dump_files};do
                            zip -r ${zip_path} ${dump_log} >> ${exe_log_file} 2>&1
                            if [ $? -ne 0 ];then
                                log_echo "ERROR" "The execute cmd:[cd ${collection_result_backup_path}/all_node;zip -r ${zip_path} ${dump_log}] failed.of ${FUNCNAME}. The file is: ${scene_ini} , the index is : [${index}]"
                                execute_ret=1
                            fi
                        done
                    fi
                done
            done
        done
    done
    
    for index in $(seq 1 ${LogCount});
    do
        NodeTag[${index}]=""
        OwnerUser[${index}]=""
        LogList[${index}]=""
        if [ ! -z "${ProcessName[${index}]}" ];then
            ProcessName[${index}]=""
        fi
    done
    
    if [ -f ${zip_path} ];then
        if [ -z "${pkg_list}" ];then
            pkg_list="${zip_path}"
        else
            pkg_list="${pkg_list},\n${zip_path}"
        fi
    else
        log_echo "INFO" "The zip_path=${zip_path} is not exists.of ${FUNCNAME}."
    fi
    
    log_echo "INFO" "The scene=${scene} finish.of ${FUNCNAME}."
    return ${execute_ret}
}

function uniep_peer_ip()
{
    ## check uniep node num 
    local uniep_node_num=$(echo "${Uniep_IP}"|sed "s/,/\n/g"|wc -l)
    if [ ${uniep_node_num} -ne 2 ];then
        log_echo "INFO" "The uniep_node_num=${uniep_node_num} is not equal 2,not need to copy pkg.of ${FUNCNAME}."
        return 0
    fi
    
    peer_ssh_ip=""
    for ip in $(echo "${Uniep_IP}"|sed "s/,/\n/g");do
        ip addr |grep -wF "${ip}" >> ${exe_log_file} 2>&1
        if [ $? -ne 0 ];then
            peer_ssh_ip="${ip}"
        fi
    done

    peer_scp_ip=${peer_ssh_ip}
    echo "${peer_ssh_ip}" | grep ":" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        peer_scp_ip="[${peer_ssh_ip}]"
    fi
}

function copy_pkg()
{
    uniep_peer_ip
    
    if [ -z "${peer_ssh_ip}" ];then
        log_echo "INFO" "The peer_ssh_ip=${peer_ssh_ip} is null.of ${FUNCNAME}."
        return 0
    fi
    
    ssh -o StrictHostKeyChecking=no -p ${ssh_port} ossadm@${peer_ssh_ip}  mkdir -p ${collection_result_backup_path}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute cmd:[ ssh -o StrictHostKeyChecking=no -p ${ssh_port} ossadm@${peer_ssh_ip}  mkdir -p ${collection_result_backup_path} ] failed.of ${FUNCNAME}."
        exit 33
    fi
    
    ## ${collection_result_backup_path} ## ${pkg_list} ${Uniep_IP}
    for pkg in $(echo -e "${pkg_list}" |sed "s/,//g");do
        scp -o StrictHostKeyChecking=no -P ${ssh_port} ${pkg} ossadm@${peer_scp_ip}:${collection_result_backup_path} 
        if [ $? -ne 0 ];then
            log_echo "ERROR" "execute cmd:[ scp -o StrictHostKeyChecking=no -P ${ssh_port} ${pkg} ossadm@${peer_scp_ip}:${collection_result_backup_path} ] failed.of ${FUNCNAME}."
            exit 34
        fi
    done
    
    log_echo "INFO" "copy pkg finished.of ${FUNCNAME}."
    return 0
}

function clean_peer_pkg()
{
    uniep_peer_ip
    
    if [ -z "${peer_ssh_ip}" ];then
        log_echo "INFO" "The peer_ssh_ip=${peer_ssh_ip} is null.of ${FUNCNAME}."
        return 0
    fi

    local backup_path;
    for backup_path in ${collection_result_backup_path_remove_list};
    do
        if [ -z "${collection_file_name}" ];then
            ssh -o StrictHostKeyChecking=no -p "${ssh_port}" "ossadm@${peer_ssh_ip}" rm -f "${backup_path}"/infocollect_*.zip
            log_echo "INFO" "execute cmd:[ ssh -o StrictHostKeyChecking=no -p ${ssh_port} ossadm@${peer_ssh_ip} rm -f ${backup_path}/infocollect_*.zip ], execute ret=$?."
            continue;
        fi
        ssh -o StrictHostKeyChecking=no -p "${ssh_port}" "ossadm@${peer_ssh_ip}"  rm -f "${backup_path}/${collection_file_name}"
        log_echo "INFO" "execute cmd:[ ssh -o StrictHostKeyChecking=no -p ${ssh_port} ossadm@${peer_ssh_ip} rm -f ${backup_path}/${collection_file_name} ], execute ret=$?."
    done
    
    log_echo "INFO" "clean_peer_pkg finished.of ${FUNCNAME}."
    return 0
}

function tar_all_scene_pkg()
{
    log_collection_start_time=$(date +'%Y%m%d')
    local day_time=$(date +'%Y-%m-%d 00:00:00')
    local day_time_seconds=$(date -d "${day_time}" "+%s")

    local time_period_seconds=$(((${collection_log_day_time}-1)*24*60*60))
    log_collection_start_time="$(date -d @$((${day_time_seconds} - ${time_period_seconds})) +'%Y%m%d')000000"
    log_collection_end_time=$(date +'%Y%m%d%H%M%S')
    
    pkg_list=""
    local ret=0
    for scene in $(echo "${collection_scene}" |sed "s/,/ /g");do
        if [ "${scene}" == "all" -o "${scene}" == "clean" ];then
            continue
        fi
        tar_scene_logs_pkg "${scene}" || ret=1
    done
    
    [ ${ret} -ne 0 ] && exit 32
    
    log_echo "INFO" "The ${FUNCNAME} finished."
    
    if [ -z "${pkg_list}" ];then
        log_echo "ERROR" "Failed to collect files related to this scenario. Check whether the environment is normal and whether the service is installed and running properly."
        clean_tmpfile
        exit 38
    fi

    copy_pkg
    
    log_echo "INFO" "The scene pkg:${pkg_list}"
    check_all_scenario_pkgs
}

function check_all_scenario_pkgs()
{
    if [ "x${scene_value}" != "xall" ];then
        log_echo "INFO" "This Scenario is single collect job. no need to exec this function."
        return 0
    fi
    log_echo "INFO" "start check all scenario pkgs..."
    [ -f "${log_full_path}/tmp.log" ] && rm -rf ${log_full_path}/tmp.log
    local nodeip_list=$(source ${install_path}/manager/bin/engr_profile.sh;python ${pycommon} ${hostinfo_and_user_loglist_json} "get_all_nodeip")
    for nodeip in $(echo "${nodeip_list}" |sed "s/,/ /g")
    do
        local is_local_ip=$(ip addr |grep -iwF "${nodeip}")
        if [ "${nodeip}" == "127.0.0.1" -o ! -z "${is_local_ip}" ];then
            [ -d "${install_path}/SOP/apps" ] && ls ${install_path}/SOP/apps |egrep "^DV|^I2000|^MML|^ENV|^SasS" >> ${log_full_path}/tmp.log
        else
            ssh -o StrictHostKeyChecking=no -p ${ssh_port} ossadm@${nodeip} ls ${install_path}/SOP/apps |egrep "^DV|^I2000|^MML|^ENV|^SasS" >> ${log_full_path}/tmp.log
        fi
    done
    for scene in $(echo "${collection_scene}" |sed "s/,/ /g")
    do
        if [ ! -z $(echo "${pkg_list}" |grep "${scene}") ];then
            continue
        fi
        source ${feature_custom_cfg_template}/${scene}.ini
        cat ${log_full_path}/tmp.log |sort |uniq |while read line
        do
            for index in $(seq 1 ${LogCount});
            do
                if [ "x${IS_COLLECT_STACK}" == "xtrue" ] && [ "x${ProcessName[index]}" == "x" ];then
                    continue
                elif [ "x${IS_COLLECT_STACK}" != "xtrue" ] && [ "x${ProcessName[index]}" != "x" ];then
                    continue
                fi
                if [ $(echo ${LogList[${index}]} |grep -w ${line} |wc -l) -ne 0 ];then
                    log_echo "ERROR" "collect Scenario of ${scene} failed. Please check..."
                    break
                fi
            done
        done
        for index in $(seq 1 ${LogCount});
        do
            NodeTag[${index}]=""
            OwnerUser[${index}]=""
            LogList[${index}]=""
            if [ ! -z "${ProcessName[${index}]}" ];then
                ProcessName[${index}]=""
            fi
        done
    done

    log_echo "INFO" "end to check all scenario pkgs."
}

function clean_tmpfile()
{
    if [ -d ${collection_result_backup_path}/all_node ];then
        rm -rf ${collection_result_backup_path}/all_node
    fi
    
    if [ -d ${main_path} ];then
        find ${main_path} -type d |xargs -i chmod 750 {}
        find /opt/oss/infocollect -type f -a -not -perm 500 -a \( -name "*.sh" -o -name "*.pyc" -o -name "*.py" -o -name "*.exp" \) -print0 | xargs -0 --no-run-if-empty chmod 500
        find ${main_path} -type f -a \( -name "*.txt" -o -name "*.json" -o -name "*.csv" -o -name "*.ini" -o -name "*.properties" -o -name "*.log" -o -name "*.cfg" -o -name "*.gz" -o -name "*.zip" \) -print0 | xargs -0 --no-run-if-empty chmod 600
    fi

    local nodeip_list=$(python ${pycommon} ${hostinfo_and_user_loglist_json} "get_all_nodeip")
    if [ "X${nodeip_list}" == "XERROR" ];then
        log_echo "ERROR" "The nodeip_list=${nodeip_list} is ERROR.of ${FUNCNAME}."
        exit 29
    fi

    for nodeip in $(echo "${nodeip_list}" |sed "s/,/ /g");do
        local is_local_ip=$(ip addr |grep -iwF "${nodeip}")
        if [ -n "${is_local_ip}" ] || [ "${nodeip}" == "127.0.0.1" ];then
            ## clean infocollect_log_dir
            rm -rf "${log_path}/manager/infocollect" 2>/dev/null
            rm -rf "${install_path}/infocollect_tmp" 2>/dev/null
            rm -rf "${HOME_OSSADM}/infocollect_tmp" 2>/dev/null
            rm -rf "${install_path}/infocollect_tmp" 2>/dev/null
            rm -rf "${log_path}/select_service_proce" 2>/dev/null
        else
            ## clean remote node's infocollect_log_dir
            ${ssh_prefix} "ossadm@${nodeip}" "rm -rf ${log_path}/manager/infocollect" 2>/dev/null
            ${ssh_prefix} "ossadm@${nodeip}" "rm -rf ${install_path}/infocollect_tmp" 2>/dev/null
            ${ssh_prefix} "ossadm@${nodeip}" "rm -rf ${HOME_OSSADM}/infocollect_tmp" 2>/dev/null
            ${ssh_prefix} "ossadm@${nodeip}" "rm -rf ${log_path}/select_service_proce" 2>/dev/null
        fi
    done

    [ -f "${common_path}/stack.tag" ] && rm -rf ${common_path}/stack.tag
    [ -f "${log_full_path}/tmp.log" ] && rm -rf ${log_full_path}/tmp.log
}

function get_system_info()
{
    local ssh_ip=$1
    local backup_file=$2

    if [ "X${ssh_ip}" == "Xlocal" ];then
        local cpu_nums=$(cat /proc/cpuinfo | grep -wic processor)
        local memtotal=$(awk '/^MemTotal/ { printf("%d", $2/1048576) }' /proc/meminfo)
        local memavailable=$(awk '/^MemAvailable/ { printf("%d", $2/1048576) }' /proc/meminfo)
        local iostatus=$(iostat -x)
        local diskstatus=$(df -h -x tmpfs -x devtmpfs)
        local osrelease=$(cat /etc/os-release)
    else
        local cpu_nums=$(${ssh_prefix} "${ssh_ip}" cat /proc/cpuinfo | grep -wic processor)
        local memtotal=$(${ssh_prefix} "${ssh_ip}" grep "^MemTotal" /proc/meminfo | awk '/^MemTotal/ { printf("%d", $2/1048576) }')
        local memavailable=$(${ssh_prefix} "${ssh_ip}" grep "^MemAvailable" /proc/meminfo | awk '/^MemAvailable/ { printf("%d", $2/1048576) }')
        local iostatus=$(${ssh_prefix} "${ssh_ip}" iostat -x)
        local diskstatus=$(${ssh_prefix} "${ssh_ip}" df -h -x tmpfs -x devtmpfs)
        local osrelease=$(${ssh_prefix} "${ssh_ip}" cat /etc/os-release)
    fi

    echo -e "NODE IP: \n ${ssh_ip}" >> "${backup_file}"
    echo -e "cpu number: \n ${cpu_nums}" >> "${backup_file}"
    echo -e "total memory: \n ${memtotal}" >> "${backup_file}"
    echo -e "available memory: \n ${memavailable}" >> "${backup_file}"
    echo -e "io status: \n ${iostatus}" >> "${backup_file}"
    echo -e "disk status: \n ${diskstatus}" >> "${backup_file}"
    echo -e "os release: \n ${osrelease}" >> "${backup_file}"
    echo -e "\n" >> "${backup_file}"
}

function call_feature_script()
{
    local site_feature_shell_ini="${main_path}/site_feature_shell.ini"

    if [ ! -f "${site_feature_shell_ini}" ];then
        log_echo "ERROR" "No find ${site_feature_shell_ini}, please check"
        exit 1
    fi
    source "${site_feature_shell_ini}"
    for index in $(seq 1 ${#ShellScript[*]});
    do
        service_name=${ServiceName[index]}
        if [ -z ${service_name} ];then
            log_echo "ERROR" "Not found ${ShellScript[index]} executer servicename, skip"
            continue
        fi
        if sudo -u ossuser test -f "${ShellScript[index]}";then
            is_python=False
            if [ ${ShellScript[index]##*.} == "py" ] || [ ${ShellScript[index]##*.} == "pyc" ];then
                is_python=True
            fi
            ## call script
            if [ "${ShellExecuteUser[index]}" == "ossadm" ];then
                if [ "${is_python}" == "True" ];then
                    source /opt/oss/manager/agent/bin/engr_profile.sh;
                    export SSL_ROOT=/opt/oss/manager/etc/ssl;
                    export CIPHER_ROOT=/opt/oss/manager/etc/cipher;

                    python "${ShellScript[index]}" "${ShellScriptInput[index]}"
                    if [ $? -eq 0 ];then
                        log_echo "INFO" "[${ShellScript[index]}] execute success."
                    else
                        log_echo "WARN" "Execute command [python ${ShellScript[index]} ${ShellScriptInput[index]}] failed, skip this python execute"
                    fi
                else
                    sh "${ShellScript[index]}"  "${ShellScriptInput[index]}"
                    if [ $? -eq 0 ];then
                        log_echo "INFO" "[${ShellScript[index]}] execute success."
                    else
                        log_echo "WARN" "Execute command [sh ${ShellScript[index]} ${ShellScriptInput[index]}] failed, skip this script execute"
                    fi
                fi
            else
                if [ "${is_python}" == "True" ];then
                    log_echo "ERROR" "Current doesn't support ossuser execute python"
                    exit 1
                else
                    sudo -u ossuser sh "${ShellScript[index]}"  "${ShellScriptInput[index]}"
                fi
                if [ $? -eq 0 ];then
                    log_echo "INFO" "[${ShellScript[index]}] execute success."
                else
                    log_echo "WARN" "Execute command [sudo -u ossuesr sh ${ShellScript[index]} ${ShellScriptInput[index]}] failed, skip this script execute"
                fi
            fi

            ## get_and_move_feature_script_logs
            if [ -z "${ShellLogPath[index]}" ];then
                ## Collect specified path log
                log_echo "ERROR" "Not log or output file collect target path, need check"
                exit 1
            fi
            collect_info_output_list="${ShellLogPath[index]}"

            mkdir -p "${infocollect_log_dir}/feature_properties/${service_name}"
            chmod 770 "${infocollect_log_dir}/feature_properties/${service_name}"
            for output_file in $(echo ${collect_info_output_list} |sed "s/,/ /g")
            do
                if sudo -u ossuser test -f "${output_file}";then
                    sudo -u ossuser chmod g+r "${output_file}"
                    # use ossadm copy file to tmp log dir.
                    cp -f "${output_file}" "${infocollect_log_dir}/feature_properties/${service_name}"
                    sudo -u ossuser rm -f "${output_file}"
                    log_echo "INFO" "collect ${output_file} success"
                else
                    log_echo "WARN" "not find ${output_file}, skip collect after execute ${ShellScript[index]}"
                fi
            done
        fi
    done
    find ${infocollect_log_dir}/feature_properties -type f |xargs -i chmod 400 {}
}

function collect_config_properties()
{
    local site_config_ini="${main_path}/site_config.ini"
    log_echo "INFO" "Begin to collect this site's config"

    if [ ! -f "${site_config_ini}" ];then
        log_echo "ERROR" "No find ${site_config_ini}, please check"
        exit 1
    fi
    source ${site_config_ini}
    for index in $(seq 1 ${#ConfigPath[*]});
    do
        if sudo -u ossuser test -f "${ConfigPath[index]}" && test ! -z "${ServiceName[index]}";then
            service_name=${ServiceName[index]}
            config_name=$(basename "${ConfigPath[index]}")

            mkdir -p "${infocollect_log_dir}/feature_properties/${service_name}"
            chmod 770 "${infocollect_log_dir}/feature_properties/${service_name}"
            ## get config file
            if [ ! -z "${BlackKeyList[index]}" ];then
                black_key_list=$(echo "${BlackKeyList[index]}" | sed 's/,/|/g')
                sudo -u ossuser cat "${ConfigPath[index]}" | grep -Ev ${black_key_list} > "${infocollect_log_dir}/feature_properties/${service_name}/${config_name}"
            else
                sudo -u ossuser cat "${ConfigPath[index]}" > "${infocollect_log_dir}/feature_properties/${service_name}/${config_name}"
            fi

            if [ -f "${infocollect_log_dir}/feature_properties/${service_name}/${config_name}" ];then
                chmod 400 "${infocollect_log_dir}/feature_properties/${service_name}/${config_name}"
                log_echo "INFO" "collect ${ConfigPath[index]} success"
            else
                log_echo "ERROR" "collect ${ConfigPath[index]} failed"
                exit 1
            fi
        fi
    done
}

function get_and_collect_basic_site_infomation()
{
    local float_ip=$(source ${install_path}/manager/bin/engr_profile.sh;python "${pycommon}" "${nodelists_json}" "get_access_ip")
    local site_infomation_file=${infocollect_log_dir}/site_archive/basic_site_infomation.properties

    if [ -d "${infocollect_log_dir}/site_archive" ];then
        rm -rf ${infocollect_log_dir}/site_archive
    fi
    mkdir -p "${infocollect_log_dir}/site_archive"
    touch "${site_infomation_file}"

    ## UniEP Version
    local uniep_version=$(grep -w "softwareVersion" "${install_path}/manager/var/share/software_define.yaml" | awk -F ': ' '{print $2}')
    echo -e "UNIEP VERSION: \n ${uniep_version}" >> "${site_infomation_file}"

    ## DV Current Version
    local commoninfo_ini=${install_path}/share/SOP/DVEngineeringService/I2000/common/config/CommonInfo.ini
    local dv_version=$(${ssh_prefix} "${float_ip}" sudo -u ossuser grep "FullVersion" "${commoninfo_ini}" | awk -F '=' '{print $2}')
    echo -e "DigitalView VERSION: \n ${dv_version}" >> "${site_infomation_file}"

    ## DV Install Version
    local installinfo_ini=${install_path}/share/SOP/DVEngineeringService/I2000/common/config/InstallInfo.ini
    local dv_install_time=$(${ssh_prefix} "${float_ip}" sudo -u ossuser grep "InstallTime" "${installinfo_ini}" | awk -F '=' '{print $2}')
    local dv_install_version=$(${ssh_prefix} "${float_ip}" sudo -u ossuser grep "FullVersion" "${installinfo_ini}" | awk -F '=' '{print $2}')
    if [ -z ${dv_install_time} ];then
        dv_install_time=$(${ssh_prefix} "${float_ip}" sudo -u ossuser grep "install\ DV_V8_I2000_Product\ successfully" ${log_path}/SOP/DVEngineeringService/I2k_PRODUCT_INSTALL.log | head -1 | awk -F ' ' '{print $1, $2}')
    fi
    if [ -z ${dv_install_version} ];then
        dv_install_version=$(${ssh_prefix} "${float_ip}" sudo -u ossuser grep "I2000[[:space:]]" "${install_path}/share/SOP/DVEngineeringService/I2000/common/config/UpgradePath.ini" | head -1 | awk -F ' ' '{print $(NF - 1)}')
        if [ -z ${dv_install_version} ];then
            dv_install_version=${dv_version}
        fi
    fi
    echo -e "DigitalView Install VERSION: \n ${dv_install_time}  ${dv_install_version}" >> "${site_infomation_file}"

    ## DV Upgrade Path
    local upgradepath_ini=${install_path}/share/SOP/DVEngineeringService/I2000/common/config/UpgradePath.ini
    local upgradepath=$(${ssh_prefix} "${float_ip}" sudo -u ossuser grep "I2000[[:space:]]" "${upgradepath_ini}")
    echo -e "Upgrade Path: \n ${upgradepath}" >> "${site_infomation_file}"

    ## DV Upgrade Path
    local keyfile_dat=${install_path}/SOP/apps/DVRegisterService/pub/license/DigitalView/keyFile.dat
    local key_dat=$(${ssh_prefix} "${float_ip}" sudo -u ossuser cat "${keyfile_dat}")
    echo -e "KeyFile.dat: \n ${key_dat}" >> "${site_infomation_file}"

    chmod 400 "${site_infomation_file}"
}

function posthandle_logs()
{
    local site_json="${archive_path}/basic_site_infomation.json"

    cd ${collection_result_backup_path}/all_node

    if [ "${collection_scene}" == "site_base" ] || [ "${collection_scene}" == "site_all" ];then
        get_and_collect_basic_site_infomation
    fi
}
