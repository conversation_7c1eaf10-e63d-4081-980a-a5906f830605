#! /usr/bin/expect -f
set command1 [lindex ${argv} 0]
set command2 [lindex ${argv} 1]
set user_ip [lindex ${argv} 2]
set dv_helm_path [lindex ${argv} 3]
set oper_user [lindex ${argv} 4]
set timeout -1

expect " K8S_CORE1_ROOT_PWD" {
            set K8S_CORE1_ROOT_PWD $expect_out(buffer)

            set index [string first "K8S_CORE1_ROOT_PWD" ${K8S_CORE1_ROOT_PWD}]
            set index [expr $index-2]
            if {$index > 0} {
                set K8S_CORE1_ROOT_PWD [string range ${K8S_CORE1_ROOT_PWD} 0 $index]
            }
}

spawn scp -r ${command1} ${command2}
expect {
    "yes/no" {
        send "yes\n"
        expect "assword:"
        send -- $K8S_CORE1_ROOT_PWD\n;
    }
    "assword:"  {
        send -- $K8S_CORE1_ROOT_PWD\n
    }
    eof {
        exit 1
    }
}

expect {
    eof {
        catch wait result
        if {[lindex ${result} 3] != 0} {
            exit 1
        }
    }
    -nocase "*assword:*" {
        exit 1
    }
    eof {
        exit 1
    }
}

spawn ssh ${user_ip}
set SSH_RESULT 100
set timeout 600
expect {
    "yes/no" {
        send "yes\n"
        expect "assword:"
        send -- $K8S_CORE1_ROOT_PWD\n;
    }
    "assword:"  {
        send -- $K8S_CORE1_ROOT_PWD\n
    }
}
expect {
    "assword:" { exit 101 }
    -re ".*\[\$#~>\]" { puts "Login ${user_ip} succeed." }
    timeout { exit 101 }
}

if {${oper_user} != "root"} {
    send -- "env LC_ALL=POSIX sudo su -\n"
    expect {
        -re ".*\[\$#~>\]" { puts "su root succeed." }
        timeout {
            exit 104
        }
    }
}

set EXE_RESULT 0
send "rm -rf /home/<USER>/${dv_helm_path};mv /tmp/${dv_helm_path} /home/<USER>"
send "chown -R paas. /home/<USER>/${dv_helm_path};\n"

log_user 0
send -- "ret=\$?;\[ \$ret -eq 0 \] || ret=1;echo exec_ret=\$ret\n"
expect {
    "exec_ret=0"  {
        set CMD_RET 0
    }
    "exec_ret=1"  {
        puts "the command execute failed..."
        set CMD_RET 1
    }
    timeout {
        exit 105
    }
}

send "exit\n"
send "exit\n"

expect eof
exit $EXE_RESULT

