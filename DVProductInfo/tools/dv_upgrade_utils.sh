#!/bin/bash
#####################################################
# Description : Only For non root upgrade I2000
#####################################################
set +x
base_path="$1"
CUR_PATH=${base_path}/tools
if [ ! -f $CUR_PATH/dv_upgrade_utils.sh ];then
    echo "The CUR_PATH=${CUR_PATH} is error of dv_upgrade_utils.sh"
    exit 1
fi
action_tag="${base_path}/upgrade_action.tag"
if [ "X${i2k_ssh_port}" == "X" ];then
    i2k_ssh_port=22
fi

temp_dir_no_ossadm_file="backup\|add_cert.log"

function modify_ip_type()
{
    grep -i "access2" ${nodelist_json} > /dev/null 2>&1
    if [ $? -eq 0 ];then
        ip_type="2"
    else
        cat ${nodelist_json} |grep "\"IP\"" |awk -F'"IP"[ ]*:' '{print $2}' | grep ":" > /dev/null 2>&1
        if [ $? -eq 0 ];then
            ip_type="1"
        else
            ip_type="0"
        fi
    fi
    sed -i "s#IPType=.*#IPType=${ip_type}#" ${source_file}
    IPType=${ip_type}
    log_echo "INFO" "IPType is ${ip_type}..."
}


function clean_known_hosts()
{
    known_ip=$1
    if [ "X$(whoami)" == "Xroot" ];then
        if [ -f /root/.ssh/known_hosts ];then
            sed -i -e "/.*${known_ip}.*/d" /root/.ssh/known_hosts
        fi
    fi

    if [ -f /home/<USER>/.ssh/known_hosts ];then
        sed -i -e "/.*${known_ip}.*/d" /home/<USER>/.ssh/known_hosts
    fi
}


function upgrade_cfg_init()
{
    nodes_json=${exp_product_path}/nodes*.json
    product_json=${exp_product_path}/product*.json
    nodelist_json=${install_path}/manager/etc/sysconf/nodelists.json
    
    is_install_icnfg=$(cat ${product_json} | sed 's/,/\n/g' |grep "IS_INSTALL_ICNFG" |awk -F'"' '{print $4}' | sed 's/ //g')
    
    [ -f "${PreSet_PATH}/express.tag" ] && is_install_icnfg="No"
    sed -i "s#IS_INSTALL_ICNFG=.*#IS_INSTALL_ICNFG=${is_install_icnfg}#" ${source_file}
    
    modify_ip_type
    
    if [ ! -f ${CUR_PATH}/getInfo.log ];then
        touch ${CUR_PATH}/getInfo.log
    fi
    chown ossadm:ossgroup ${CUR_PATH}/get_install_info.py
    chown ossadm:ossgroup ${CUR_PATH}/getInfo.log
    chmod 755 ${CUR_PATH}/get_install_info.py
    chmod 755 ${CUR_PATH}/getInfo.log
    
    rm -f ${dv_cfg_file}.tmp > /dev/null 2>&1
    cp -pf ${dv_cfg_file} ${dv_cfg_file}.tmp
    sed -i "s#is_install_icnfg=.*#is_install_icnfg=${is_install_icnfg}#" ${dv_cfg_file}.tmp
}

function modify_dv_cfg_common()
{
    sed -i "/is_upgrade=/d" ${dv_cfg_file}.tmp
    echo "is_upgrade=Yes" >> ${dv_cfg_file}.tmp
    
    check_need_radompwd
    
    . ${dv_cfg_file}.tmp
    sed -i "s#{{i2k_user_home}}#$i2k_user_home#g" ${dv_cfg_file}.tmp
    . ${dv_cfg_file}.tmp
}

function modify_dual_app_cfg()
{
    app_float_ip=$(cat ${product_json} | sed 's/,/\n/g' |grep ":31943" |awk -F'"' '{print $4}'| awk -F':31943' '{print $1}' | sed 's/\[\|\]//g' | sed 's/ //g'| grep -v https)
    app_ip1=$(echo ${app_nodeip_list} | awk -F',' '{print $1}')
    app_ip2=$(echo ${app_nodeip_list} | awk -F',' '{print $2}')
    ip_cmd=$(which ip)

    clean_known_hosts "app_float_ip"
    clean_known_hosts "app_ip1"
    clean_known_hosts "app_ip2"

    local float_mac=$(ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${app_float_ip} ${ip_cmd} addr | grep link/ether| head -1)
    local app1_mac=$(ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${app_ip1} ${ip_cmd} addr | grep link/ether| head -1)
    local app2_mac=$(ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${app_ip2} ${ip_cmd} addr | grep link/ether| head -1)

    if [ "X${float_mac}" == "X${app1_mac}" -o "X${float_mac}" == "X${app2_mac}" ];then
        if [ "${IPType}" == "0" ];then
            sed -i "s#APP_Primary_IPV4=.*#APP_Primary_IPV4=${app_ip1}#" ${source_file}
            sed -i "s#APP_Secondary_IPV4=.*#APP_Secondary_IPV4=${app_ip2}#" ${source_file}
            sed -i "s#APP_FloatIP_IPV4=.*#APP_FloatIP_IPV4=${app_float_ip}#" ${source_file}
        else
            sed -i "s#APP_Primary_MGR_IPV6=.*#APP_Primary_MGR_IPV6=${app_ip1}#" ${source_file}
            sed -i "s#APP_Secondary_MGR_IPV6=.*#APP_Secondary_MGR_IPV6=${app_ip2}#" ${source_file}
            sed -i "s#APP_FloatIP_IPV6=.*#APP_FloatIP_IPV6=${app_float_ip}#" ${source_file}
        fi
    else
        log_echo "ERROR" "Can't find floatIP:${app_float_ip}'s mac at appnode1:${app_ip1}'s mac or appnode2:${app_ip2}'s mac, please check..."
        exit 1
    fi

    
    db_ip1=$(echo ${db_nodeip_list} | awk -F',' '{print $1}')
    db_ip2=$(echo ${db_nodeip_list} | awk -F',' '{print $2}')
    
    if [ "${IPType}" == "0" ];then
        sed -i "s#DB_Primary_IPV4=.*#DB_Primary_IPV4=${db_ip1}#" ${source_file}
        sed -i "s#DB_Secondary_IPV4=.*#DB_Secondary_IPV4=${db_ip2}#" ${source_file}
    else
        sed -i "s#DB_Primary_MGR_IPV6=.*#DB_Primary_MGR_IPV6=${db_ip1}#" ${source_file}
        sed -i "s#DB_Secondary_MGR_IPV6=.*#DB_Secondary_MGR_IPV6=${db_ip2}#" ${source_file}
    fi
}

function check_uniep()
{
    local software_dir=/opt/pub/software
    if [ ! -d ${software_dir} ];then
        log_echo "ERROR" "The software_dir=${software_dir} is not exists!please check ..."
        log_echo "ERROR" "The software repository does not exist on the Uniep node ${UNIEP_NODE_IP}. The upgrade cannot be performed. Check and repair the lost software repository and related software packages."
        exit 1
    fi
    log_echo "INFO" "check ${software_dir} is exists."
    
    local find_ret=$(find ${software_dir}  -user root |awk '{printf $1","}')
    if [ ! -z "${find_ret}" ];then
        log_echo "ERROR" "Files or directories with the root permission exist in the ${software_dir} directory.please check on UniEP ${UNIEP_NODE_IP}."
        log_echo "ERROR" "The root permission file or dir is: ${find_ret} please check."
        exit 1
    fi
    log_echo "INFO" "Check whether the permission on the ${software_dir} directory is correct."

    if [ -d "/opt/pub/software/mgr-installdisk/deploy_omp_sandbox" ];then
        log_echo "INFO" "/opt/pub/software/mgr-installdisk/deploy_omp_sandbox should not exist, it may affect uniep_upgrade! start deleting..."
        rm -rf /opt/pub/software/mgr-installdisk/deploy_omp_sandbox
        if [ $? -ne 0 ];then
            log_echo "INFO"  "rm -rf /opt/pub/software/mgr-installdisk/deploy_omp_sandbox failed,please check."
			exit 1
        fi
    fi

    check_jar_path_with_root_jar
    
    log_echo "INFO" "check uniep finish."
}

function input_pwd_upgrade()
{
    local node_ip=$1
    local ssh_user="${upgrade_ssh_user}"
    if [ "X${is_use_root}" == "Xyes" ];then
        ssh_user=${oper_user}
    fi
    rootpwdReturn=""
    
    if [ "X${is_use_root}" == "Xyes" ];then
        sshpwdReturn=""
        local input_times=0
        while [ ${input_times} -lt 3 ]
        do
            echo_pwd "Please input ${ssh_user}\'s password of ${node_ip} "
            sshpwdReturn="$PwdReturn"

            clean_known_hosts "${node_ip}"
            auto_smart_ssh ${sshpwdReturn} "${ssh_user}@${node_ip} echo ssh_ok"
            if [ $? -eq 0 ];then
                break
            elif [ ${input_times} -eq 2 ];then
                log_echo "ERROR" "ssh to ${node_ip} execute command failed, please check."
                exit 1
            else
                log_echo "INFO" "Please try again to ssh to ${node_ip}."
            fi
            input_times=$((${input_times}+1))
        done
        rootpwdReturn="${sshpwdReturn}"
    else
        sshpwdReturn="${node_ssh_pwd}"
        input_times=0
        while [ ${input_times} -lt 3 ]
        do
            echo_pwd "Please input root\'s password of ${node_ip} "
            rootpwdReturn="$PwdReturn"
            sshpwdReturn="$PwdReturn"
            clean_known_hosts "${node_ip}"
            auto_smart_ssh ${sshpwdReturn} "${ssh_user}@${node_ip} echo ssh_ok"
            if [ $? -eq 0 ];then
                break
            elif [ ${input_times} -eq 2 ];then
                log_echo "ERROR" "ssh to ${node_ip} su root to execute command failed, please check."
                exit 1
            else
                log_echo "INFO" "Please try again ."
            fi
            input_times=$((${input_times}+1))
        done
    fi
}

function input_user_pwd_before()
{
    local prompt_message="$1"
    log_echo "INFO" "We need the root permission to ${prompt_message} node."
    if [ "X${SUDO_USER}" != "X" ];then
        is_use_root=yes
        upgrade_ssh_user=${SUDO_USER}
        log_echo "INFO" "This is an sudo upgrade scene,we use ${SUDO_USER} to ssh other node and sudo to root to upgrade"
    else
        read -p "Can we ssh to all the nodes with root?  please choose is_use_root : yes/no  "  is_use_root
        if [ -z "${is_use_root}" ];then
            log_echo "ERROR" "The input is_use_root is null"
            exit 1
        elif [ "${is_use_root}" != "yes" -a "${is_use_root}" != "no" ];then
            log_echo "ERROR" "The input is_use_root is incorrect"
            exit 1
        fi
        if [ "X${is_use_root}" == "Xyes" ];then
            upgrade_ssh_user="root"
        else
            upgrade_ssh_user="ossadm"
        fi

    fi
    
    log_echo "INFO" "is_use_root is ${is_use_root}..."
    update_config ${configParametersFile} "is_use_root" "${is_use_root}" || exit 1
     
    update_config ${configParametersFile} "upgrade_ssh_user" "${upgrade_ssh_user}" || exit 1
}

function input_node_ssh_pwd()
{
    local ssh_user="$1"
    local node_ip="$2"
    nodepwdReturn=""
    log_echo "INFO" "We need ${ssh_user}'s password of all nodes are the same !!!"
    sed -i -e  "/.*${node_ip}.*/d" /root/.ssh/known_hosts
    local input_times=0
    while [ ${input_times} -lt 3 ]
    do
        echo_pwd "Please input ${ssh_user}\'s password of ${node_ip} "
        nodepwdReturn="$PwdReturn"

        auto_smart_ssh ${nodepwdReturn} "${ssh_user}@${node_ip} echo ssh_ok"
        if [ $? -eq 0 ];then
            break
        elif [ ${input_times} -eq 2 ];then
            log_echo "ERROR" "ssh to ${node_ip} execute command failed, please check."
            exit 1
        else
            log_echo "INFO" "Please try again to ssh to ${node_ip}."
        fi
        input_times=$((${input_times}+1))
    done
}

function input_password_for_nodelist()
{
    ipList=$1
    e=0
    Node_ssh_pwd_List=()
    Node_root_pwd_List=()
    for i in $(echo "${ipList}" | sed "s#,# #g")
    do
        Check_execute "Node_PreSet_${i}"
        if [ $? -eq 0 ];then
            log_echo "INFO" "Node_PreSet_${i} has been executed"
            Node_ssh_pwd_List[$e]="noneed"
            Node_root_pwd_List[$e]="noneed"
        else
            input_pwd_upgrade "${i}"
            Node_ssh_pwd_List[$e]="${sshpwdReturn}"
            Node_root_pwd_List[$e]="${rootpwdReturn}"
        fi
        ((e++))
    done
}

function input_app_dual_pwd()
{
    if [ "X${IPType}" != "X0" ];then
        Check_execute "APP_PreSet_${APP_Primary_MGR_IPV6}"
        if [ $? -ne 0 ];then
            input_pwd_upgrade "${APP_Primary_MGR_IPV6}"  && Check_execute "scp_old_sign_dv"
            APP_Primary_ssh_pwd="${sshpwdReturn}"
            APP_Primary_root_pwd="${rootpwdReturn}"
        fi
        Check_execute "APP_PreSet_${APP_Secondary_MGR_IPV6}"  
        if [ $? -ne 0 ];then
            input_pwd_upgrade "${APP_Secondary_MGR_IPV6}"
            APP_Secondary_ssh_pwd="${sshpwdReturn}"
            APP_Secondary_root_pwd="${rootpwdReturn}"
        fi
    else
        Check_execute "APP_PreSet_${APP_Primary_IPV4}"   && Check_execute "scp_old_sign_dv"
        if [ $? -ne 0 ];then
            input_pwd_upgrade "${APP_Primary_IPV4}"
            APP_Primary_ssh_pwd="${sshpwdReturn}"
            APP_Primary_root_pwd="${rootpwdReturn}"
        fi
        Check_execute "APP_PreSet_${APP_Secondary_IPV4}"
        if [ $? -ne 0 ];then
            input_pwd_upgrade "${APP_Secondary_IPV4}"
            APP_Secondary_ssh_pwd="${sshpwdReturn}"
            APP_Secondary_root_pwd="${rootpwdReturn}"
        fi
    fi
    
    log_echo "INFO" "We need the root permission to preset DB node."
    if [ "X${IPType}" != "X0" ];then
        Check_execute "DB_PreSet_${DB_Primary_MGR_IPV6}"
        if [ $? -ne 0 ];then
            input_pwd_upgrade "${DB_Primary_MGR_IPV6}"
            DB_Primary_ssh_pwd="${sshpwdReturn}"
            DB_Primary_root_pwd="${rootpwdReturn}"
        fi
        Check_execute "DB_PreSet_${DB_Secondary_MGR_IPV6}"
        if [ $? -ne 0 ];then
            input_pwd_upgrade "${DB_Secondary_MGR_IPV6}"
            DB_Secondary_ssh_pwd="${sshpwdReturn}"
            DB_Secondary_root_pwd="${rootpwdReturn}"
        fi
    else
        Check_execute "DB_PreSet_${DB_Primary_IPV4}"
        if [ $? -ne 0 ];then
            input_pwd_upgrade "${DB_Primary_IPV4}"
            DB_Primary_ssh_pwd="${sshpwdReturn}"
            DB_Primary_root_pwd="${rootpwdReturn}"
        fi
        Check_execute "DB_PreSet_${DB_Secondary_IPV4}"
        if [ $? -ne 0 ];then
            input_pwd_upgrade "${DB_Secondary_IPV4}"
            DB_Secondary_ssh_pwd="${sshpwdReturn}"
            DB_Secondary_root_pwd="${rootpwdReturn}"
        fi
    fi
}

function check_backup_exist()
{
    if [ "X${is_SD_upgrade}" == "XYes" ];then
        log_echo "INFO" "This is SD upgrade, no need to check backup files."
        return 0
    fi
    [ -f "${base_path}/backupok.tag" ] && return 0
    
    log_echo "INFO" "Begin to check DV's backup"
    i2000_install_path="${install_path}/share/SOP/DVEngineeringService"
    i2000_commonifo="${i2000_install_path}/I2000/common/config/CommonInfo.ini"
    if [ "X${netWorkType}" == "XO" ];then
        I2000_baseversion=$(grep "FullVersion=" ${i2000_commonifo}|awk -F'=' '{print $2}')
        
        if [ -z "`ls ${i2000_install_path}/backup_for_upgrade/I2000_${I2000_baseversion} 2>/dev/null`" ];then
            log_echo "ERROR" "Can't find DV's backup files, you should do the backup first."
            exit 1
        fi
        
        return 0
    fi
    
    if [ "X${netWorkType}" == "XT" ];then
        if [ "X${IPType}" == "X1" ];then
            node_ip="${APP_IPV6}"
        elif [ "X${IPType}" == "X2" ];then
            node_ip="${APP_MGR_IPV6}"
        else
            node_ip="${APP_IPV4}"
        fi
        i2k_ssh_passwd=${APP_ssh_pwd}
        i2k_root_passwd=${APP_root_pwd}
    elif [ "X${netWorkType}" == "XM" ];then
        if [ ! -f ${i2000_commonifo} ];then
            if [ "X${IPType}" == "X1" ];then
                node_ip="${DVSecondary_IPV6}"
            elif [ "X${IPType}" == "X2" ];then
                node_ip="${DVSecondary_MGR_IPV6}"
            else
                node_ip="${DVSecondary_IPV4}"
            fi
            i2k_ssh_passwd=${DVSecondary_ssh_pwd}
            i2k_root_passwd=${DVSecondary_root_pwd}
        else
            I2000_baseversion=$(grep "FullVersion=" ${i2000_commonifo}|awk -F'=' '{print $2}')
            if [ -z "`ls ${i2000_install_path}/backup_for_upgrade/I2000_${I2000_baseversion} 2>/dev/null`" ];then
                log_echo "ERROR" "Can't find DV's backup files, you should do the backup first."
                exit 1
            fi
            return 0
        fi
    else
        if [ "X${IPType}" == "X1" ];then
            node_ip="${APP_Primary_IPV6}"
        elif [ "X${IPType}" == "X2" ];then
            node_ip="${APP_Primary_MGR_IPV6}"
        else
            node_ip="${APP_Primary_IPV4}"
        fi
        i2k_ssh_passwd=${APP_Primary_ssh_pwd}
        i2k_root_passwd=${APP_Primary_root_pwd}
        
        ssh_execute "${upgrade_ssh_user}" "${i2k_ssh_passwd}" "${i2k_root_passwd}" "${node_ip}" "grep 'FullVersion=' \"${i2000_commonifo}\"" > ${base_path}/i2kbaseversion.txt
        I2000_baseversion=$(grep "^FullVersion=" ${base_path}/i2kbaseversion.txt|awk -F'=' '{print $2}')
        ssh_execute "${upgrade_ssh_user}" "${i2k_ssh_passwd}" "${i2k_root_passwd}" "${node_ip}" "ls ${i2000_install_path}/backup_for_upgrade/I2000_${I2000_baseversion}" > ${base_path}/i2kbackup.txt
        
        if [ -z "`grep 'I2000_.*Backup.tar.gz' ${base_path}/i2kbackup.txt`" ];then
            if [ "X${IPType}" == "X1" ];then
                node_ip="${APP_Secondary_IPV6}"
            elif [ "X${IPType}" == "X2" ];then
                node_ip="${APP_Secondary_MGR_IPV6}"
            else
                node_ip="${APP_Secondary_IPV4}"
            fi
            i2k_ssh_passwd=${APP_Secondary_ssh_pwd}
            i2k_root_passwd=${APP_Secondary_root_pwd}
        else
            touch ${base_path}/backupok.tag
            return 0
        fi
    fi
    
    ssh_execute "${upgrade_ssh_user}" "${i2k_ssh_passwd}" "${i2k_root_passwd}" "${node_ip}" "grep 'FullVersion=' \"${i2000_commonifo}\"" > ${base_path}/i2kbaseversion.txt
    I2000_baseversion=$(grep "^FullVersion=" ${base_path}/i2kbaseversion.txt|awk -F'=' '{print $2}')
    ssh_execute "${upgrade_ssh_user}" "${i2k_ssh_passwd}" "${i2k_root_passwd}" "${node_ip}" "ls ${i2000_install_path}/backup_for_upgrade/I2000_${I2000_baseversion}" > ${base_path}/i2kbackup.txt
    
    if [ -z "`grep 'I2000_.*Backup.tar.gz' ${base_path}/i2kbackup.txt`" ];then
        log_echo "ERROR" "Can't find DV's backup files on ${node_ip}, you should do the backup first."
        exit 1
    fi
    
    touch ${base_path}/backupok.tag
}

function Upgrade_APP_PreSet()
{
    tmp_dir=/tmp/DVPreSet_upgrade_ossadm
    Check_execute "APP_PreSet_$1"
    ret=$?
    if [ ${ret} -eq 0 ];then
        return 0
    fi
    
    if [ $# -eq 0 ];then
        log_echo "INFO" "preset AllInOne node." 
        DefaultValues_properties="${install_path}/share/SOP/DVEngineeringService/I2000/run/uninstall/etc/config/DefaultValues.properties"
        $(${TEST_FILE} ${DefaultValues_properties})
        if [ $? -eq 0 ];then
            db_data_path=$(${command_prefix} cat $DefaultValues_properties | grep DBDataPath | awk -F '=' '{print $2}')
            if [ "X${db_data_path}" != "X" ];then
                sed -i "s#DB_DATAPATH=.*#DB_DATAPATH=${db_data_path}#" ${source_file}
                sed -i "s#db_instance_path=.*#db_instance_path=${db_data_path}#" ${dv_cfg_file}.tmp
            fi
        fi
        
        if [ "X${is_SD_upgrade}" == "XYes" ];then
            chown ossadm:ossgroup ${base_path}/SD_upgrade.tag
            chmod 440 ${base_path}/SD_upgrade.tag
            cp -pf ${base_path}/SD_upgrade.tag /home/<USER>
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Copy SD_upgrade.tag to /home/<USER>"
                exit 1
            fi
        fi
        
        rm -rf ${tmp_dir:?} > /dev/null 2>&1
        mkdir -p ${tmp_dir}
        
        i2k_file_list="DV_config.properties.tmp DV_PreSet_APP.sh DV_PreSet_DB.sh utils_common.sh utils_uniep.sh utils_os.sh DV_PreSet_Cleanup.sh fixAutoMount.sh add_cert.py dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service"
        i2k_file_list="${i2k_file_list} obtain_ca_by_rest.py"
        for i2k_filename in ${i2k_file_list}
        do
            cp -rf ${base_path}/Common/${i2k_filename} ${tmp_dir}
        done
        
        cp -rf ${base_path}/Common/i2kuser_root_script ${tmp_dir}

        [ -d ${PreSet_PATH}/Common/certificate/demoCA ] && chmod 700 ${PreSet_PATH}/Common/certificate/demoCA
        find ${base_path}/Common/certificate -type d | xargs -i chmod 700 {} > /dev/null 2>&1
        cp -rf ${base_path}/Common/certificate  ${tmp_dir}
        
        cp -rf ${base_path}/Common/sign_dv  ${tmp_dir}
        
        cp -rf ${base_path}/Common/kafka  ${tmp_dir}
        cp -rf ${base_path}/Common/i2k_cert/nbi_cert  ${tmp_dir}
        cp -rf ${base_path}/Common/core_binding  ${tmp_dir}
        cp -rf ${base_path}/Common/custom_backup  ${tmp_dir}
        
        mkdir ${tmp_dir}/tools
        cp -f ${base_path}/tools/su_ossadm_exec.exp ${tmp_dir}/tools
        cp -f ${base_path}/tools/uniep_encrypt.py ${tmp_dir}/tools
        cp -rf ${base_path}/tools/rpm ${tmp_dir}/tools
        cp -rpf ${base_path}/tools/enable_os_rollback ${tmp_dir}/tools

        cp -rf ${base_path}/tools/exp_imp_dvpmdatadb ${tmp_dir}/tools
        cp -rf ${base_path}/tools/exp_imp_ossuserdb  ${tmp_dir}/tools
        
        
        chmod 755 ${tmp_dir}
        ls ${tmp_dir} |grep -wv "${temp_dir_no_ossadm_file}" |xargs -i chmod -R 755 ${tmp_dir}/{}

        if [ "X${IPType}" == "X0" ];then
            local ALL_IN_ONE_IP=${ALL_IN_ONE_IPV4}
        else
            local ALL_IN_ONE_IP=${ALL_IN_ONE_IPV6}
        fi
        
        sh ${tmp_dir}/DV_PreSet_APP.sh upgrade_check
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset i2k upgrade_check in APP node failed. Please check: ${tmp_dir}/preset_script.log at node ${ALL_IN_ONE_IP}."
            echo "APP_PreSet_ : failed" >> ${action_tag}
            exit 1
        fi
        
        sh ${tmp_dir}/DV_PreSet_DB.sh rollback_os_operation_db
        if [ $? -ne 0 ];then
           log_echo "ERROR" "Preset cmd:[ sh ${tmp_dir}/DV_PreSet_DB.sh rollback_os_operation_db ] in APP node failed. Please check: ${tmp_dir}/preset_script.log at node ${ALL_IN_ONE_IP}."
           echo "APP_PreSet_ : failed" >> ${action_tag} 
           exit 1
        fi

        sh ${tmp_dir}/DV_PreSet_DB.sh check_zenith_backupspace
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset cmd:[ sh ${tmp_dir}/DV_PreSet_DB.sh check_zenith_backupspace ] in APP node failed. Please check: ${tmp_dir}/preset_script.log at node ${ALL_IN_ONE_IP}."
            echo "APP_PreSet_ : failed" >> ${action_tag}
            exit 1
        fi

        sh ${tmp_dir}/DV_PreSet_DB.sh upgrade_preset_common
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset cmd:[ sh ${tmp_dir}/DV_PreSet_DB.sh upgrade_preset_common ] in APP node failed. Please check: ${tmp_dir}/preset_script.log at node ${ALL_IN_ONE_IP}."
            echo "APP_PreSet_ : failed" >> ${action_tag}
            exit 1
        fi

        sh ${tmp_dir}/DV_PreSet_APP.sh "${ALL_IN_ONE_IP}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset i2k in APP node failed. Please check: ${tmp_dir}/preset_script.log at node ${ALL_IN_ONE_IP}."
            echo "APP_PreSet_ : failed" >> ${action_tag}
            exit 1
        fi

        sh ${tmp_dir}/DV_PreSet_APP.sh "create_register_config"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset in create_register_config failed, please check detailed log ${tmp_dir}/preset_script.log on the local host."
            exit 1
        fi

        sed -i "/APP_PreSet_ : failed/d" ${action_tag} 2>/dev/null
        echo "APP_PreSet_ : success" >> ${action_tag}
        
        return 0  
    fi

    log_echo "INFO" "Begin to preset APP node."
    local app_ssh_ip=$1
    local app_scp_ip=$1
    local app_ssh_pwd=$2
    local app_root_pwd=$3
    local node_role=$4
    local peer_node_ip=$5
    
    handle_SD_tag "${app_ssh_ip}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "handle_SD_tag to ${app_ssh_ip} failed, please check: ${tmp_dir}/preset_script.log at node ${app_ssh_ip}. "
        exit 1
    fi
    
    if [ "X${IPType}" == "X1" ];then
        log_echo "INFO" "IPType is 1. is ipv6. of APP_PreSet"
        app_scp_ip="\[${app_scp_ip}\]"
    fi
    
    local isIpv6=$(echo ${app_scp_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        app_scp_ip="\[${app_scp_ip}\]"
    fi
    
    if [ "X${node_role}" != "X" ];then
        sed -i "s#i2k_dual_role=.*#i2k_dual_role=${node_role}#" ${base_path}/Common/DV_config.properties.tmp
    fi
    
    remote_ssh_passwd="${app_ssh_pwd}"
    remote_root_passwd="${app_root_pwd}"

    if [ "X${node_role}" == "XPrimary" ];then
        primary_ssh_pswd="${remote_ssh_passwd}"
        primary_root_pswd="${remote_root_passwd}"
    elif [ "X${node_role}" == "XSecondary" ];then
        secondary_ssh_pswd="${remote_ssh_passwd}"
        secondary_root_pswd="${remote_root_passwd}"
    fi
    
    DefaultValues_properties="${install_path}/share/SOP/DVEngineeringService/I2000/run/uninstall/etc/config/DefaultValues.properties"
    if [ "X${netWorkType}" == "XM" -a "X${node_role}" == "XPrimary" ];then
        get_node_memifo   "${app_ssh_ip}" ""  ""  "Primary"
        $(${TEST_FILE} ${DefaultValues_properties})
        if [ $? -eq 0 ];then
            ${command_prefix} grep "DBDataPath" ${DefaultValues_properties} > ${base_path}/dbpath.txt
        fi
        dos2unix ${base_path}/dbpath.txt > /dev/null 2>&1
        db_data_path=$(grep "^DBDataPath=" ${base_path}/dbpath.txt |awk -F'DBDataPath=' '{print $2}')
        if [ "X${db_data_path}" != "X" ];then
            sed -i "s#DB_DATAPATH=.*#DB_DATAPATH=${db_data_path}#" ${source_file}
            sed -i "s#db_instance_path=.*#db_instance_path=${db_data_path}#" ${dv_cfg_file}.tmp
        fi
        
        if [ ! -f "${base_path}/UniEp/UniEPMgr/software_define.yaml" ];then
            unzip ${base_path}/UniEp/UniEPMgr*.zip software_define.yaml -d ${base_path}/UniEp/UniEPMgr
        fi
        mkdir -p ${tmp_dir}
        cp -rpf ${base_path}/tools/preCheck.sh ${tmp_dir}
        chmod 755 ${tmp_dir}
        ls ${tmp_dir} |grep -wv "${temp_dir_no_ossadm_file}" |xargs -i chmod -R 755 ${tmp_dir}/{}
        new_uniep_version=$(cat ${base_path}/UniEp/UniEPMgr/software_define.yaml|grep "softwareVersion:" | awk -F ':' '{print $2}' |  sed "s# ##g" )
        sh ${tmp_dir}/preCheck.sh ${new_uniep_version}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ssh to ${app_ssh_ip} execute command[sh ${tmp_dir}/preCheck.sh ${new_uniep_version}] failed, please check: ${tmp_dir}/preset_script.log at node ${app_ssh_ip}. "
            exit 1
        fi
        
        i2k_file_list="DV_config.properties.tmp DV_PreSet_APP.sh utils_common.sh utils_uniep.sh utils_os.sh DV_PreSet_Cleanup.sh Common_config.sh fixAutoMount.sh add_cert.py dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service"
        i2k_file_list="${i2k_file_list} obtain_ca_by_rest.py"
        for i2k_filename in ${i2k_file_list}
        do
            cp -rpf ${base_path}/Common/${i2k_filename} ${tmp_dir}  > /dev/null 2>&1
        done
        
        cp -rpf ${base_path}/Common/i2kuser_root_script ${tmp_dir} > /dev/null 2>&1

        chmod 700 ${base_path}/Common/certificate/issue_er_cert > /dev/null 2>&1
        cp -rpf ${base_path}/Common/certificate ${tmp_dir} > /dev/null 2>&1
        
        cp -rpf ${base_path}/Common/sign_dv ${tmp_dir} > /dev/null 2>&1
        
        cp -rpf ${base_path}/Common/kafka ${tmp_dir} > /dev/null 2>&1
        cp -rf  ${base_path}/Common/i2k_cert/nbi_cert  ${tmp_dir} > /dev/null 2>&1
        cp -rf ${base_path}/Common/core_binding  ${tmp_dir} > /dev/null 2>&1
        cp -rf ${base_path}/Common/custom_backup  ${tmp_dir}

        mkdir -p ${tmp_dir}/tools
        cp -pf ${base_path}/tools/su_ossadm_exec.exp ${tmp_dir}/tools  > /dev/null 2>&1
        cp -pf ${base_path}/tools/uniep_encrypt.py ${tmp_dir}/tools > /dev/null 2>&1
        cp -rpf ${base_path}/tools/rpm ${tmp_dir}/tools > /dev/null 2>&1

        chmod 755 ${tmp_dir}
        ls ${tmp_dir} |grep -wv "${temp_dir_no_ossadm_file}" |xargs -i chmod -R 755 ${tmp_dir}/{}
        
        log_echo "INFO" "Begin to execute DV_PreSet_APP.sh upgrade_check at ${app_ssh_ip}..."
        sh ${tmp_dir}/DV_PreSet_APP.sh upgrade_check
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset i2k upgrade_check in APP node failed, please check: ${tmp_dir}/preset_script.log at node ${app_ssh_ip}."
            echo "APP_PreSet_${app_ssh_ip} : failed" >> ${action_tag}
            exit 1
        fi
        
        sh ${tmp_dir}/DV_PreSet_APP.sh "${app_ssh_ip}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset i2k in APP node failed, please check: ${tmp_dir}/preset_script.log at node ${app_ssh_ip}."
            echo "APP_PreSet_${app_ssh_ip} : failed" >> ${action_tag}
            exit 1
        fi

        sh ${tmp_dir}/DV_PreSet_APP.sh "create_register_config"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset in create_register_config failed, please check detailed log ${tmp_dir}/preset_script.log on the local host."
            exit 1
        fi

        sed -i "/APP_PreSet_${app_ssh_ip} : failed/d" ${action_tag} 2>/dev/null
        echo "APP_PreSet_${app_ssh_ip} : success" >> ${action_tag}
        return 0
    fi
    
    if [ "X${netWorkType}" == "XC" -a "X${node_role}" == "XPrimary" ] || [ "X${netWorkType}" == "XT" -a "X${node_role}" == "X" ];then
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "grep \"^MemTotal\" /proc/meminfo" > ${base_path}/primary_memtotal.txt

        primary_memtotal=$(awk '/^MemTotal/ { print $2 }' ${base_path}/primary_memtotal.txt)
        if [ -z "${primary_memtotal}" ];then
            log_echo "ERROR" "The primary_memtotal get is null, please check"
            exit 1
        fi
        
        local isNumber=$(echo "${primary_memtotal}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
        if [ "${isNumber}" == "No" ];then
            log_echo "ERROR" "The primary_memtotal=${primary_memtotal} is not number, please check"
            exit 1
        fi
        
        ## kb to GB  (1048576=1024*1024)
        machine_total_memory=$((${primary_memtotal}/1048576))
        if [ $machine_total_memory -ge 60 ];then
            ## zenith_paramgroup_file  large  largeCapacity
            echo "zenith_paramgroup_file=largeCapacity" > ${base_path}/zenith_paramgroup_file.properties
        else
            echo "zenith_paramgroup_file=large" > ${base_path}/zenith_paramgroup_file.properties
        fi
    fi

    db_data_path=/opt/zenith/data
    if [ "X${db_data_path}" != "X" ];then
        sed -i "s#DB_DATAPATH=.*#DB_DATAPATH=${db_data_path}#" ${source_file}
        sed -i "s#db_instance_path=.*#db_instance_path=${db_data_path}#" ${dv_cfg_file}.tmp
    fi
    
    if [ "X${netWorkType}" == "XM" ];then
        get_node_memifo   ${app_ssh_ip}  "${upgrade_ssh_user}"  "${remote_ssh_passwd}"
        auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${app_ssh_ip} mkdir -p ${tmp_dir}"
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "[ -d ${tmp_dir}/certificate/demoCA ] && chmod 700 ${tmp_dir}/certificate/demoCA"
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "ls ${tmp_dir} |grep -wv \"${temp_dir_no_ossadm_file}\" |xargs -i chown ${upgrade_ssh_user}: -R ${tmp_dir}/{}"
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "chmod 755 ${tmp_dir}"
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "ls ${tmp_dir} |grep -wv \"${temp_dir_no_ossadm_file}\" |xargs -i chmod -R 755 ${tmp_dir}/{}"
        auto_scp "${remote_ssh_passwd}" ${base_path}/tools/preCheck.sh "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}"
        
        new_uniep_version=$(cat ${base_path}/UniEp/UniEPMgr/software_define.yaml|grep "softwareVersion:" | awk -F ':' '{print $2}' |  sed "s# ##g" )
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "sh ${tmp_dir}/preCheck.sh ${new_uniep_version}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ssh to ${app_ssh_ip} execute command[sh ${tmp_dir}/preCheck.sh ${new_uniep_version}] failed, please check: ${tmp_dir}/preset_script.log at node ${app_ssh_ip}."
            exit 1
        fi
    else
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "chmod -R 700 ${tmp_dir}/certificate"
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "rm -rf ${tmp_dir:?} > /dev/null 2>&1"
    fi
    
    auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${app_ssh_ip} mkdir -p ${tmp_dir}/tools"
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "chmod 755 ${tmp_dir}"
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "ls ${tmp_dir} |grep -wv \"${temp_dir_no_ossadm_file}\" |xargs -i chmod -R 755 ${tmp_dir}/{}"
    
    i2k_file_list="DV_config.properties.tmp DV_PreSet_APP.sh utils_common.sh utils_uniep.sh utils_os.sh DV_PreSet_Cleanup.sh Common_config.sh fixAutoMount.sh add_cert.py dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service"
    i2k_file_list="${i2k_file_list} obtain_ca_by_rest.py"
    for i2k_filename in ${i2k_file_list}
    do
        auto_scp "${remote_ssh_passwd}" "${base_path}/Common/${i2k_filename}" "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}"
    done
    
    auto_scp_dir "${remote_ssh_passwd}" "${base_path}/Common/i2kuser_root_script" "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}"

    chmod 700 ${base_path}/Common/certificate/demoCA
    auto_scp_dir "${remote_ssh_passwd}" "${base_path}/Common/certificate" "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}"
    
    auto_scp_dir "${remote_ssh_passwd}" "${base_path}/Common/sign_dv" "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}"
    
    auto_scp_dir "${remote_ssh_passwd}" "${base_path}/Common/kafka" "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}"

    auto_scp_dir "${remote_ssh_passwd}" "${base_path}/Common/i2k_cert/nbi_cert" "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}"
    auto_scp_dir "${remote_ssh_passwd}" "${base_path}/Common/core_binding"  "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}"
    auto_scp_dir "${remote_ssh_passwd}" "${base_path}/Common/custom_backup"  "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}"
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/tools/su_ossadm_exec.exp "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}/tools"
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/tools/uniep_encrypt.py "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}/tools"
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/tools/rpm "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}/tools"

    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "chmod 755 ${tmp_dir}"
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "ls ${tmp_dir} |grep -wv \"${temp_dir_no_ossadm_file}\" |xargs -i chmod -R 755 ${tmp_dir}/{}"

    log_echo "INFO" "Begin to execute DV_PreSet_APP.sh upgrade_check at ${app_ssh_ip}..."
    ssh -q -o StrictHostKeyChecking=no -p "${i2k_ssh_port}" "ossadm@${app_ssh_ip}" "sh ${tmp_dir}/DV_PreSet_APP.sh upgrade_check"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Preset i2k upgrade_check in APP node failed, please check: ${tmp_dir}/preset_script.log at node ${app_ssh_ip}."
        echo "APP_PreSet_${app_ssh_ip} : failed" >> ${action_tag}
        exit 1
    fi
    
    log_echo "INFO" "Begin to execute DV_PreSet_APP.sh at ${app_ssh_ip}..."
    ssh -q -o StrictHostKeyChecking=no -p "${i2k_ssh_port}" "ossadm@${app_ssh_ip}" "sh ${tmp_dir}/DV_PreSet_APP.sh ${app_ssh_ip}" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Preset i2k in APP node failed, please check: ${tmp_dir}/preset_script.log at node ${app_ssh_ip}."
        echo "APP_PreSet_${app_ssh_ip} : failed" >> ${action_tag}
        exit 1
    fi

    ssh -q -o StrictHostKeyChecking=no -p "${i2k_ssh_port}" "ossadm@${app_ssh_ip}" "sh ${tmp_dir}/DV_PreSet_APP.sh create_register_config" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Preset i2k in APP node with create register config failed, please check: ${tmp_dir}/preset_script.log at node ${app_ssh_ip}."
        echo "APP_PreSet_${app_ssh_ip} : failed" >> ${action_tag}
        exit 1
    fi

    if [ "X${netWorkType}" == "XM" -a "X${node_role}" == "XSecondary" ];then
        auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${app_ssh_ip} mkdir -p ${tmp_dir}/tools/infocollect"
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "chmod -R 755 ${tmp_dir}/tools/infocollect"
        auto_scp_dir "${remote_ssh_passwd}" ${base_path}/tools/infocollect "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}/tools"
        auto_scp_dir "${remote_ssh_passwd}" ${base_path}/tools/health_check "${upgrade_ssh_user}@${app_scp_ip}:${tmp_dir}/tools"
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip} "sh ${tmp_dir}/DV_PreSet_APP.sh infocollect"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "sh ${tmp_dir}/DV_PreSet_APP.sh infocollect in node ${app_ssh_ip} failed, please check: ${tmp_dir}/preset_script.log at node ${app_ssh_ip}."
            echo "APP_PreSet_${app_ssh_ip} : failed" >> ${action_tag}
            exit 1
        fi
    fi

    sed -i "/APP_PreSet_${app_ssh_ip} : failed/d" ${action_tag} 2>/dev/null
    echo "APP_PreSet_${app_ssh_ip} : success" >> ${action_tag}
}

function Upgrade_DB_PreSet()
{
    tmp_dir=/tmp/DVPreSet_upgrade_ossadm
    Check_execute "DB_PreSet_$1"
    if [ $? -eq 0 ];then
        return 0
    fi
    
    log_echo "INFO" "We need the root permission to preset DB node." 
    local db_ssh_ip=$1
    local db_scp_ip=$1
    local db_ssh_pwd=$2
    local db_root_pwd=$3
    local node_role=$4
    
    handle_SD_tag "${db_ssh_ip}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "handle_SD_tag to ${db_ssh_ip} failed, please check: ${tmp_dir}/preset_script.log at node ${db_ssh_ip}."
        exit 1
    fi
    
    if [ "X${IPType}" == "X1" ];then
        log_echo "INFO" "IPType is 1. is ipv6. of DB_PreSet"
        db_scp_ip="\[${db_scp_ip}\]"
    fi
    
    local isIpv6=$(echo ${db_scp_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        db_scp_ip="\[${db_scp_ip}\]"
    fi
    
    if [ "X${netWorkType}" == "XM" -a "X${node_role}" == "XPrimary" ];then
        mkdir -p ${tmp_dir}
        cp -rpf ${base_path}/tools/preCheck.sh ${tmp_dir}
        chmod 755 ${tmp_dir}
        ls ${tmp_dir} |grep -wv "${temp_dir_no_ossadm_file}" |xargs -i chmod -R 755 ${tmp_dir}/{}
        new_uniep_version=$(cat ${base_path}/UniEp/UniEPMgr/software_define.yaml|grep "softwareVersion:" | awk -F ':' '{print $2}' |  sed "s# ##g" )
        sh ${tmp_dir}/preCheck.sh ${new_uniep_version}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ssh to ${db_ssh_ip} execute command[sh ${tmp_dir}/preCheck.sh ${new_uniep_version}] failed, please check: ${tmp_dir}/preset_script.log at node ${db_ssh_ip}."
            exit 1
        fi
        
        db_file_list="DV_config.properties.tmp DV_PreSet_DB.sh utils_common.sh utils_uniep.sh utils_os.sh Common_config.sh fixAutoMount.sh add_cert.py dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service"
        for db_filename in ${db_file_list}
        do
            cp -rpf ${base_path}/Common/${db_filename}  ${tmp_dir}
        done
        
        cp -rpf ${base_path}/Common/i2kuser_root_script/sudoScripts/sed.sh  ${tmp_dir}
        cp -rpf ${base_path}/Common/i2kuser_root_script/sudoScripts/modifyCert.sh  ${tmp_dir}
        cp -rpf ${base_path}/Common/i2kuser_root_script/sudoScripts/handle_hofs.sh.sh  ${tmp_dir}
        cp -rpf ${base_path}/Common/i2kuser_root_script/sudoScripts/recover_dbuser.sh ${tmp_dir}

        cp -rpf ${base_path}/Common/certificate  ${tmp_dir}
        cp -rpf ${base_path}/Common/sign_dv  ${tmp_dir}
        mkdir -p ${tmp_dir}/tools
        cp -pf ${base_path}/tools/su_ossadm_exec.exp ${tmp_dir}/tools
        cp -pf ${base_path}/tools/uniep_encrypt.py ${tmp_dir}/tools
        cp -rpf ${base_path}/tools/exp_imp_ossuserdb ${tmp_dir}/tools
        cp -rpf ${base_path}/tools/exp_imp_dvpmdatadb ${tmp_dir}/tools
        cp -rpf ${base_path}/tools/enable_os_rollback ${tmp_dir}/tools
        
        chmod 755 ${tmp_dir}
        ls ${tmp_dir} |grep -wv "${temp_dir_no_ossadm_file}" |xargs -i chmod -R 755 ${tmp_dir}/{} > /dev/null 2>&1

        log_echo "INFO" "Begin to execute DV_PreSet_DB.sh UpgradePreSet at ${db_ssh_ip}..."
        sh ${tmp_dir}/DV_PreSet_DB.sh UpgradePreSet "${db_ssh_ip}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset db in DB node failed. Please check ${tmp_dir}/preset_script.log at DB node ${db_ssh_ip} . "
            exit 1
        fi
        sh ${tmp_dir}/DV_PreSet_DB.sh upgrade_preset_common 
        if [ $? -ne 0 ];then
            log_echo "ERROR" "upgrade_preset_common in DB node failed. Please check ${tmp_dir}/preset_script.log at DB node ${db_ssh_ip} . "
            exit 1
        fi
        echo "DB_PreSet_${db_ssh_ip} : success" >> ${action_tag}
        return 0
    fi
    
    remote_ssh_passwd="${db_ssh_pwd}"
    remote_root_passwd="${db_root_pwd}"

    if [ "X${netWorkType}" == "XM" ];then
        auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${db_ssh_ip} mkdir -p ${tmp_dir}"
        ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ssh_ip} "chmod 755 ${tmp_dir}"
        ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ssh_ip} "ls ${tmp_dir} |grep -wv \"${temp_dir_no_ossadm_file}\" |xargs -i chmod -R 755 ${tmp_dir}/{}"
        auto_scp "${remote_ssh_passwd}" ${base_path}/tools/preCheck.sh "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}"
        
        new_uniep_version=$(cat ${base_path}/UniEp/UniEPMgr/software_define.yaml|grep "softwareVersion:" | awk -F ':' '{print $2}' |  sed "s# ##g" )
        ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ssh_ip} "sh ${tmp_dir}/preCheck.sh ${new_uniep_version}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ssh to ${db_ssh_ip} execute command[sh ${tmp_dir}/preCheck.sh ${new_uniep_version}] failed. Please check ${tmp_dir}/preset_script.log at DB node ${db_ssh_ip} ."
            exit 1
        fi
    else
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ssh_ip} "sudo -u ossuser chmod 770 ${tmp_dir}/backup"
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ssh_ip} "sudo -u ossuser chmod 770 ${tmp_dir}/add_cert.log"
    fi
    
    auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${db_ssh_ip} mkdir -p ${tmp_dir}"
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ssh_ip} "chmod 755 ${tmp_dir}"
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ssh_ip} "ls ${tmp_dir} |grep -wv \"${temp_dir_no_ossadm_file}\" |xargs -i chmod -R 755 ${tmp_dir}/{}"
    auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${db_ssh_ip} mkdir -p ${tmp_dir}/tools"
    auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${db_ssh_ip} chown ${upgrade_ssh_user}: ${tmp_dir}"
    auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${db_ssh_ip} chown ${upgrade_ssh_user}: ${tmp_dir}/tools"
    db_file_list="DV_config.properties.tmp DV_PreSet_DB.sh utils_common.sh utils_uniep.sh utils_os.sh Common_config.sh fixAutoMount.sh add_cert.py dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service"
    for db_filename in ${db_file_list}
    do
        auto_scp "${remote_ssh_passwd}" ${base_path}/Common/${db_filename} "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}"
    done
    
    auto_scp "${remote_ssh_passwd}" ${base_path}/Common/i2kuser_root_script/sudoScripts/sed.sh "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}"
    auto_scp "${remote_ssh_passwd}" ${base_path}/Common/i2kuser_root_script/sudoScripts/modifyCert.sh "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}"
    auto_scp "${remote_ssh_passwd}" ${base_path}/Common/i2kuser_root_script/sudoScripts/handle_hofs.sh "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}"
    auto_scp "${remote_ssh_passwd}" ${base_path}/Common/i2kuser_root_script/sudoScripts/recover_dbuser.sh "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}"

    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ssh_ip} "[ -d ${tmp_dir}/certificate/demoCA ] && chmod 700 ${tmp_dir}/certificate/demoCA"
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/Common/certificate "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}"
    
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/Common/sign_dv "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}"
    
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/Common/kafka "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}"
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/Common/core_binding  "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}"
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/tools/su_ossadm_exec.exp "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}/tools"
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/tools/uniep_encrypt.py "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}/tools"
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/tools/exp_imp_ossuserdb "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}/tools"
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/tools/exp_imp_dvpmdatadb "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}/tools"
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/tools/enable_os_rollback "${upgrade_ssh_user}@${db_scp_ip}:${tmp_dir}/tools"


    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ssh_ip} "chmod 755 ${tmp_dir}"
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ssh_ip} "ls ${tmp_dir} |grep -wv \"${temp_dir_no_ossadm_file}\" |xargs -i chmod -R 755 ${tmp_dir}/{}"

    log_echo "INFO" "Begin to execute DV_PreSet_DB.sh UpgradePreSet at ${db_ssh_ip}..."

    ssh -q -o  StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ssh_ip} "sh ${tmp_dir}/DV_PreSet_DB.sh UpgradePreSet ${db_ssh_ip}"  > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Preset db in DB node failed. Please check ${tmp_dir}/preset_script.log at DB node ${db_ssh_ip} . "
        exit 1
    fi
    ssh -q -f -o  StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ssh_ip} "sh ${tmp_dir}/DV_PreSet_DB.sh upgrade_preset_common"  > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "upgrade_preset_common in DB node failed. Please check ${tmp_dir}/preset_script.log at DB node ${db_ssh_ip} . "
        exit 1
    fi
    echo "DB_PreSet_${db_ssh_ip} : success" >> ${action_tag}
}

function scp_old_sign_dv()
{   
    ssh_user=$1
    APP_ssh_pwd=$2
    APP_root_pwd=$3
    APP_NODE_IP=$4
    APP_NODE_SCP_IP=$5
    #ossadm@

    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${APP_NODE_IP} "sudo -u ossuser test -d /home/<USER>/etc/sign_dv"
    if [ $? -ne 0 ];then
        echo "scp_old_sign_dv : success" >>  ${action_tag}
        return 0
    fi

    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${APP_NODE_IP} "rm -rf ${tmp_dir}/old_sign_dv"
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${APP_NODE_IP} "chmod 775 ${tmp_dir}"
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${APP_NODE_IP} "sudo -u ossuser cp -a /home/<USER>/etc/sign_dv ${tmp_dir}/old_sign_dv"
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${APP_NODE_IP} "chmod 755 ${tmp_dir}"

    rm -rf ${base_path}/Common/old_sign_dv
    mkdir -p  ${base_path}/Common/old_sign_dv

    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${APP_NODE_IP} "sudo -u ossuser chmod -R 770 ${tmp_dir}/old_sign_dv"
    auto_scp_dir  "${APP_ssh_pwd}" "${ssh_user}@${APP_NODE_SCP_IP}:${tmp_dir}/old_sign_dv/*" "${base_path}/Common/old_sign_dv"
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${APP_NODE_IP} "rm -rf  ${tmp_dir}/old_sign_dv"
    echo "scp_old_sign_dv : success" >>  ${action_tag}
}

function Upgrade_Preset_APP_Dual()
{
    if [ "X${IPType}" != "X0" ];then
        sed -i "s#drbd_primary_ip=.*#drbd_primary_ip=${APP_Primary_MGR_IPV6}#" ${dv_cfg_file}.tmp
        sed -i "s#drbd_secondary_ip=.*#drbd_secondary_ip=${APP_Secondary_MGR_IPV6}#" ${dv_cfg_file}.tmp
        OMMHA_switchover "${APP_Primary_MGR_IPV6}"  "${APP_Secondary_MGR_IPV6}"
        Upgrade_APP_PreSet "${APP_Primary_MGR_IPV6}" "${APP_Primary_ssh_pwd}" "${APP_Primary_root_pwd}" "Primary" "${APP_Secondary_MGR_IPV6}"
        Upgrade_APP_PreSet "${APP_Secondary_MGR_IPV6}" "${APP_Secondary_ssh_pwd}" "${APP_Secondary_root_pwd}" "Secondary" "${APP_Primary_MGR_IPV6}"

        local app_scp_ip="\[${APP_Primary_MGR_IPV6}\]"
        local app_ssh_ip=${APP_Primary_MGR_IPV6}
    else
        sed -i "s#drbd_primary_ip=.*#drbd_primary_ip=${APP_Primary_IPV4}#" ${dv_cfg_file}.tmp
        sed -i "s#drbd_secondary_ip=.*#drbd_secondary_ip=${APP_Secondary_IPV4}#" ${dv_cfg_file}.tmp
        OMMHA_switchover  "${APP_Primary_IPV4}" "${APP_Secondary_IPV4}"
        Upgrade_APP_PreSet "${APP_Primary_IPV4}" "${APP_Primary_ssh_pwd}" "${APP_Primary_root_pwd}" "Primary" "${APP_Secondary_IPV4}"
        Upgrade_APP_PreSet "${APP_Secondary_IPV4}" "${APP_Secondary_ssh_pwd}" "${APP_Secondary_root_pwd}" "Secondary" "${APP_Primary_IPV4}"

        local app_scp_ip="${APP_Primary_IPV4}"
        local app_ssh_ip="${APP_Primary_IPV4}"     
    fi
    Check_execute "scp_old_sign_dv"
    if [ $? -ne 0 ];then
        scp_old_sign_dv  "${upgrade_ssh_user}" "${APP_Primary_root_pwd}" "${APP_Primary_root_pwd}"   "${app_ssh_ip}" "${app_scp_ip}"
    fi
    
    ## preset db node 
    if [ "X${IPType}" != "X0" ];then
        Upgrade_DB_PreSet "${DB_Primary_MGR_IPV6}" "${DB_Primary_ssh_pwd}" "${DB_Primary_root_pwd}"
        Upgrade_DB_PreSet "${DB_Secondary_MGR_IPV6}" "${DB_Secondary_ssh_pwd}" "${DB_Secondary_root_pwd}"
        ssh_get_zenith_size "${DB_Primary_MGR_IPV6}"
    else
        Upgrade_DB_PreSet "${DB_Primary_IPV4}" "${DB_Primary_ssh_pwd}" "${DB_Primary_root_pwd}"
        Upgrade_DB_PreSet "${DB_Secondary_IPV4}" "${DB_Secondary_ssh_pwd}" "${DB_Secondary_root_pwd}"
        ssh_get_zenith_size "${DB_Primary_IPV4}"
    fi
    
}

function Preset_otherNode()
{
    tmp_dir=/tmp/DVPreSet_upgrade_ossadm
    Check_execute "Node_PreSet_$1"
    if [ $? -eq 0 ];then
        return 0
    fi
    
    log_echo "INFO" "We need the root permission to preset sa/om node." 
    local node_ssh_ip=$1
    local node_scp_ip=$1
    local node_ssh_pwd=$2
    local node_root_pwd=$3
    local node_role=$4
    
    if [ "X${IPType}" == "X1" ];then
        log_echo "INFO" "IPType is 1. is ipv6. of Preset_otherNode"
        node_scp_ip="\[${node_scp_ip}\]"
    fi

    local isIpv6=$(echo ${node_scp_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        node_scp_ip="\[${node_scp_ip}\]"
    fi
    
    remote_ssh_passwd="${node_ssh_pwd}"
    remote_root_passwd="${node_root_pwd}"
    
    if [ "X${netWorkType}" == "XM" ];then
        get_node_memifo   ${node_ssh_ip}  "${upgrade_ssh_user}"  "${remote_ssh_passwd}"
        auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${node_ssh_ip} mkdir -p ${tmp_dir}"
        if [ "X${node_role}" != "XSecondary" ];then
            ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ssh_ip} "[ -d ${tmp_dir}/certificate/demoCA ] && chmod 700 ${tmp_dir}/certificate/demoCA"
            ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ssh_ip} "ls ${tmp_dir} |grep -wv \"${temp_dir_no_ossadm_file}\" |xargs -i chown ${upgrade_ssh_user}: -R ${tmp_dir}/{}"
        fi

        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ssh_ip} "chmod 755 ${tmp_dir}"
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ssh_ip} "ls ${tmp_dir} |grep -wv \"${temp_dir_no_ossadm_file}\" |xargs -i chmod -R 755 ${tmp_dir}/{}"
        auto_scp "${remote_ssh_passwd}" ${base_path}/tools/preCheck.sh "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}"
        
        new_uniep_version=$(cat ${base_path}/UniEp/UniEPMgr/software_define.yaml|grep "softwareVersion:" | awk -F ':' '{print $2}' |  sed "s# ##g" )
        ssh_execute "${upgrade_ssh_user}" "${remote_ssh_passwd}" "${remote_root_passwd}" "${node_ssh_ip}" "sh ${tmp_dir}/preCheck.sh ${new_uniep_version}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ssh to ${node_ssh_ip} execute command[sh ${tmp_dir}/preCheck.sh ${new_uniep_version}] failed. Please check ${tmp_dir}/preset_script.log at node ${node_ssh_ip}."
            exit 1
        fi
        auto_scp_dir "${remote_ssh_passwd}" ${base_path}/Common/core_binding  "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}"
    fi

    auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${node_ssh_ip} mkdir -p ${tmp_dir}/tools"
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ssh_ip} "chmod 755 ${tmp_dir}"
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ssh_ip} "ls ${tmp_dir} |grep -wv \"${temp_dir_no_ossadm_file}\" |xargs -i chmod -R 755 ${tmp_dir}/{}"

    file_list="DV_config.properties.tmp Common_config.sh utils_common.sh utils_uniep.sh utils_os.sh add_cert.py dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service DV_PreSet_DB.sh"
    for filename in ${file_list}
    do
        auto_scp "${remote_ssh_passwd}" ${base_path}/Common/${filename} "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}"
    done
    
    auto_scp "${remote_ssh_passwd}" ${base_path}/Common/i2kuser_root_script/sudoScripts/sed.sh "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}"
    auto_scp "${remote_ssh_passwd}" ${base_path}/Common/i2kuser_root_script/sudoScripts/modifyCert.sh "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}"
    auto_scp "${remote_ssh_passwd}" ${base_path}/Common/i2kuser_root_script/sudoScripts/handle_hofs.sh "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}"
    auto_scp "${remote_ssh_passwd}" ${base_path}/Common/i2kuser_root_script/sudoScripts/recover_dbuser.sh "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}"

    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/Common/core_binding  "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}"

    chmod 700 ${base_path}/Common/certificate/demoCA
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ssh_ip} "[ -d ${tmp_dir}/certificate/demoCA ] && chmod 700 ${tmp_dir}/certificate/demoCA"
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/Common/certificate "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}"
    
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/Common/sign_dv "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}"
    
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/Common/kafka "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}"
    
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/tools/su_ossadm_exec.exp "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}/tools"
    auto_scp_dir "${remote_ssh_passwd}" ${base_path}/tools/uniep_encrypt.py "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}/tools"
    if [ -d  ${base_path}/Common/old_sign_dv ];then
        auto_scp_dir "${remote_ssh_passwd}" ${base_path}/Common/old_sign_dv "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}"
    fi
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ssh_ip} "chmod 755 ${tmp_dir}"
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ssh_ip} "ls ${tmp_dir} |grep -wv \"${temp_dir_no_ossadm_file}\" |xargs -i chmod -R 755 ${tmp_dir}/{}"

    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ssh_ip} "sh ${tmp_dir}/Common_config.sh ${node_ssh_ip}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Preset at node ${node_ssh_ip} failed. Please check ${tmp_dir}/preset_script.log at node ${node_ssh_ip}. "
        exit 1
    fi

    if [ "X${node_role}" == "XSD" ];then
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ssh_ip} "sh ${tmp_dir}/DV_PreSet_DB.sh check_zenith_backupspace"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "check_zenith_backupspace at node ${node_ssh_ip} failed. Please check ${tmp_dir}/preset_script.log at node ${node_ssh_ip}. "
            exit 1
        fi
    fi
    
    if [ "X${netWorkType}" == "XL" ];then
        if [ "X${node_role}" == "XPM1" ];then
            ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ssh_ip} "grep '^MemTotal' /proc/meminfo" > ${base_path}/PM1_memtotal.txt
            if [ $? -ne 0 ];then
                log_echo "ERROR" "ssh to node ${node_ssh_ip} execute cmd: [ grep '^MemTotal' /proc/meminfo ] failed. "
                exit 1
            fi
            check_total_memory "${base_path}/PM1_memtotal.txt"
        fi
    fi
    
    echo "Node_PreSet_${node_ssh_ip} : success" >> ${action_tag}
}


function su_root_ssh()
{
    local sshuser=$1
    local sshpwd=$2
    local rootpwd=$3
    local sship=$4
    local sshcmd=$5
    
    local last_arg="${@: -1}"
    echo ${last_arg} | grep "dv_user_" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        last_arg=$(echo ${last_arg}|awk -F'dv_user_' '{print $2}')
    else
        last_arg=""
    fi
    
    echo "${sshpwd} host:passwd ${rootpwd} work:passwd ${last_arg} dvuser:passwd\n" |  ${base_path}/tools/su_root_ssh.exp ${sship} ${sshuser} root ${i2k_ssh_port} "${sshcmd}"
    if [ $? -ne 0 ];then
        log_echo "INFO" "su root to execute command ${sshcmd} with return 1 at ${sship}, please check."
        return 1
    fi
}

function update_value_from_new_product_json()
{
    local old_product_json="$1"
    
    local new_product_json="$2"
    
    local key="$3"
    
    old_value=$(cat $old_product_json  | grep $key | awk -F "$key\":" '{print $2}' | awk -F ',' '{print $1}' | sed "s#\"##g")
    new_value=$(cat $new_product_json  | grep $key | awk -F "$key\":" '{print $2}' | awk -F ',' '{print $1}' | sed "s#\"##g")
    
    if [ "X$old_value" != "X$new_value" ];then
        log_echo "INFO" "update $key's value to $new_value"
        need_add_list="${need_add_list} $key"
    fi 
}

function handle_product_json()
{
    log_echo "INFO" "Begin to handle product json..."
    product_json=$(ls ${exp_product_path}/product*.json)
    config_list=$(source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/get_install_info.py "${product_json}" "productjson")
    if [ "${config_list}" == "ERROR" ];then
        log_echo "ERROR" "Faile to get config list from ${product_json}"
        exit 1
    fi
    if [ "X${netWorkType}" == "XT" ];then
        if [ "${IPType}" == "0" ];then
            IPType_OF_DB_NODE_IP=${DB_IPV4}
        else
            IPType_OF_DB_NODE_IP=${DB_MGR_IPV6}
        fi
    fi
    if [ "X${netWorkType}" == "XC" -o "X${netWorkType}" == "XL" -o "X${netWorkType}" == "XM" ];then
        FMDeployMode="Cluster"
    else
        FMDeployMode="SingleProcess"
    fi
    if [ "X${IPType}" == "X0" ];then
        local IPTag="ipv4"
    elif [ "X${IPType}" == "X1" ];then
        local IPTag="ipv6"
    elif [ "X${IPType}" == "X2" ];then
        local IPTag="ipv4-6"
    else
        log_echo "ERROR" "The IPType is invalid."
        exit 1
    fi
    [ -f "${json_source_path}/product_sop_${IPTag}.json" ] && cp -rf ${json_source_path}/product_sop_${IPTag}.json  ${json_source_path}/product_sop.json
    sed -i "s#{{FMDeployMode}}#${FMDeployMode}#g" ${json_source_path}/product_sop.json
    cp -p ${json_source_path}/product_sop.json ${json_tmp_path}/
    chmod 777 ${json_tmp_path}/product_sop.json
    replace_file_macro ${json_tmp_path}/product_sop.json ${source_file}
    replace_file_macro ${json_tmp_path}/product_sop.json ${dv_cfg_file}.tmp
    
    [ "${IPType}" == "0" ] && sed -i "s#{{CMP_LOCAL_IP_LIST}}##g" ${json_tmp_path}/product_sop.json
    new_config_list=$(source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/get_install_info.py "${json_tmp_path}/product_sop.json" "productjson")
    tmp_new_config_list=$(echo ${new_config_list} |sed 's/\[\|\]//g' |sed 's/,/ /g'|sed "s/'//g")
    if [ "X${tmp_new_config_list}" == "X" ];then
        log_echo "ERROR" "Failed to get new_config_list from ${json_tmp_path}/product_sop.json..."
        exit 1
    fi
    need_add_list=""
    for item in ${tmp_new_config_list}
    do
        echo ${config_list} |grep -w "${item}" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            need_add_list="${need_add_list} ${item}"
        fi
    done
    
    update_value_from_new_product_json "${product_json}" "${json_tmp_path}/product_sop.json" "ES_PORT"
    update_value_from_new_product_json "${product_json}" "${json_tmp_path}/product_sop.json" "use_forwarded_ip"

    if [ "X${need_add_list}" == "X" -a "X${new_oracle_pwd}" == "X" -a "X${new_ossadmin_pwd}" == "X" ];then
        log_echo "INFO" "need_add_list and new_pwd is null, no need to add new config..."
        return 0
    fi
    
    log_echo "INFO" "Begin to generate new product json..."
    [ ! -d "${CUR_PATH}/newjson" ] && mkdir ${CUR_PATH}/newjson
    chmod 777 ${CUR_PATH}/newjson
    need_add_list=$(echo ${need_add_list}|sed 's/^ //')
    new_product_jsonfile="${CUR_PATH}/newjson/product_sop.json"
    source_product_jsonfile="${json_tmp_path}/product_sop.json"
    rm -rf ${new_product_jsonfile}
    
    log_echo "INFO" "The need_add_list=${need_add_list}."
    if [ "X${need_add_list}" != "X" ];then
        source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/handlejson.py "product" "${product_json}" "${source_product_jsonfile}" "${need_add_list}"
    else
        cp ${product_json} ${new_product_jsonfile}
        chmod 777 ${new_product_jsonfile}
    fi
    if [ ! -f "${new_product_jsonfile}" ];then
        log_echo "ERROR" "Handle json to generate new product_sop.json failed, please check..."
        exit 1
    fi
    
    if [ "X${new_ossadmin_pwd}" != "X" ];then
        source ${install_path}/manager/bin/engr_profile.sh; echo "pwd:${new_ossadmin_pwd}" | ${PYTHONHOME}/bin/python ${CUR_PATH}/handlejson.py "productpwd" "GLOBAL_UNIFIED_OSS_DB_USER_PASSWORD"
        grep "${new_ossadmin_pwd}" ${new_product_jsonfile} > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Update product json with GLOBAL_UNIFIED_OSS_DB_USER_PASSWORD failed"
            exit 1
        fi
    fi
    cat "${new_product_jsonfile}"  | grep OMConsoleAccessAddress|grep '\['
    if [ $? -ne 0 ];then
        server_ip=$(cat "${new_product_jsonfile}"  | grep OMConsoleAccessAddress | awk -F ':' '{print $2}' | sed "s#[[:space:]]\"##g")
    else
        server_ip="[$(cat ${new_product_jsonfile}  | grep OMConsoleAccessAddress|sed 's#.*\[\(.*\)\].*#\1#g')]"
    fi
    sed -i "s#\"ER_URL\".*31943\"#\"ER_URL\":\"https://${server_ip}:31943\"#g" "${new_product_jsonfile}"     

    chmod 700 ${new_product_jsonfile}
}

function handle_nodetag()
{
    nodemgrip=$1
    log_echo "INFO" "Begin to handle nodetag of ${nodemgrip}..."
    exp_nodetag=$(source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/handlejson.py "getnodetag" "${exp_node_json}" "${nodemgrip}")
    if [ "X${exp_nodetag}" == "X" -o "X${exp_nodetag}" == "XERROR" ];then
        log_echo "ERROR" "Failed to get nodetag from ${exp_node_json}..."
        exit 1
    fi
    new_nodetag=$(source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/handlejson.py "getnodetag" "${source_node_json}" "${nodemgrip}")
    if [ "X${new_nodetag}" == "X" -o "X${new_nodetag}" == "XERROR" ];then
        log_echo "ERROR" "Failed to get nodetag from ${source_node_json}..."
        exit 1
    fi
    
    new_tag=""
    for tag in `echo ${new_nodetag}|sed "s/,/ /g"`
    do
        echo ${exp_nodetag} | grep -w "${tag}" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            new_tag="${new_tag} ${tag}"
        fi
    done
    
    if [ "X${new_tag}" == "X" ];then
        log_echo "INFO" "No need to handle nodetag of ${nodemgrip}..."
        return 0
    fi
    
    need_new="Yes"
    
    [ ! -d "${CUR_PATH}/newjson" ] && mkdir ${CUR_PATH}/newjson
    chmod 777 ${CUR_PATH}/newjson
    [ ! -f ${new_node_json} ] && cp -p ${exp_node_json} ${new_node_json}

    source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/handlejson.py "nodetag" "${nodemgrip}" "${new_nodetag}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "handle nodetag of ${nodemgrip} failed"
        exit 1
    fi
    return 0
}

function modify_nodejson_of_nodename()
{
    local nodenames="$1"
    log_echo "INFO" "Begin to modify node json of nodenames ${nodenames}"
    for nodename in $(echo "${nodenames}"|sed "s#,# #g");do
       	exp_nodetag=$(source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/handlejson.py "getnodetag_of_nodename" "${exp_node_json}" "${nodename}")
        if [ "X${exp_nodetag}" == "X" -o "X${exp_nodetag}" == "XERROR" ];then
            log_echo "ERROR" "Failed to get nodetag from ${exp_node_json}..."
            exit 1
        fi
        new_nodetag=$(source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/handlejson.py "getnodetag_of_nodename" "${source_node_json}" "${nodename}")
        if [ "X${new_nodetag}" == "X" -o "X${new_nodetag}" == "XERROR" ];then
            log_echo "ERROR" "Failed to get nodetag from ${source_node_json}..."
            exit 1
        fi
        
        new_tag=$(echo "${new_nodetag}"|sed "s/,/\n/g" |sort|uniq)
        old_tag=$(echo "${exp_nodetag}"|sed "s/,/\n/g" |sort|uniq)
        if [ "${new_tag}" == "${old_tag}" ];then
            log_echo "INFO" "No need to handle nodetag of ${nodename}.the new tag and old tag is same."
            continue
        fi
        
        [ ! -d "${CUR_PATH}/newjson" ] && mkdir ${CUR_PATH}/newjson 
        chmod 777 ${CUR_PATH}/newjson
        [ ! -f ${new_node_json} ] && cp -p ${exp_node_json} ${new_node_json}
        source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/handlejson.py "update_nodetag_of_nodename" "${nodename}" "${new_nodetag}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "handlejson.py update_nodetag_of_nodename ${nodename} ${new_nodetag} failed"
            exit 1
        fi
    done
}

function modify_nodetype_by_nodename()
{
    local nodenames="$1"
    local nodetypes="$2"
    log_echo "INFO" "Begin to modify_nodetype_by_nodename of ${nodenames}..."
    for nodename in $(echo "${nodenames}"|sed "s#,# #g");do
        exp_nodetype=$(source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/handlejson.py "getnodetype" "${exp_node_json}" "${nodename}")
        if [ "X${exp_nodetype}" == "X" -o "X${exp_nodetype}" == "XERROR" ];then
            log_echo "ERROR" "Failed to get nodetype from ${exp_node_json}..."
            exit 1
        fi
        
        new_nodetype=$(source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/handlejson.py "getnodetype" "${source_node_json}" "${nodename}")
        if [ "X${new_nodetype}" == "X" -o "X${new_nodetype}" == "XERROR" ];then
            log_echo "INFO" "Failed to get nodetype from ${source_node_json}..."
            new_nodetype="${exp_nodetype}"
            local nodetype=""
            local has_key=""
            for nodetype in $(echo "${nodetypes}"|sed "s#,# #g");do
                has_key=$(echo "${new_nodetype}" |grep -w "${nodetype}")
                if [ -z "${has_key}" ];then
                    new_nodetype="${new_nodetype},${nodetype}"
                fi
            done
        fi
        
        old_type=$(echo "${exp_nodetype}"|sed "s/,/\n/g" |sort|uniq)
        new_type=$(echo "${new_nodetype}"|sed "s/,/\n/g" |sort|uniq)
        if [ "${old_type}" == "${new_type}" ];then
            log_echo "INFO" "No need to handle nodetype of ${nodename}.the new node type and old node type is same."
            continue
        fi
        
        [ ! -d "${CUR_PATH}/newjson" ] && mkdir ${CUR_PATH}/newjson 
        chmod 777 ${CUR_PATH}/newjson
        [ ! -f ${new_node_json} ] && cp -p ${exp_node_json} ${new_node_json}
        source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/handlejson.py "nodetype" "${nodename}" "${new_nodetype}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "handlejson.py nodetype ${nodename} ${new_nodetype} failed"
            exit 1
        fi
        log_echo "INFO" "modify_nodetype_by_nodename exp_nodetype=${exp_nodetype} to new_nodetype=${new_nodetype} of ${nodename} End.ret=$?"
    done
    
    log_echo "INFO" "modify_nodetype_by_nodename of ${nodenames} End."
}

function get_nodename()
{
    nodemgrip=$1
    echo "[INFO] Begin to get nodename of ${nodemgrip}..." >> $LOG_FILE
    export_nodename=$(source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/handlejson.py "getnodename" "${exp_node_json}" "${nodemgrip}")
    if [ "X${export_nodename}" == "X" -o "X${export_nodename}" == "XERROR" ];then
        log_echo "ERROR" "Failed to get export_nodename from ${exp_node_json}..."
        exit 1
    fi
    echo ${export_nodename}
}

function handle_node_json()
{
    log_echo "INFO" "Begin to handle node json..."
    exp_node_json=$(ls ${exp_product_path}/nodes*.json)
    exp_product_json=$(ls ${exp_product_path}/product*.json)
    
    if [ "X${IPType}" == "X0" ];then
        local IPTag="ipv4"
    elif [ "X${IPType}" == "X1" ];then
        local IPTag="ipv6"
    elif [ "X${IPType}" == "X2" ];then
        local IPTag="ipv4-6"
    else
        log_echo "ERROR" "The IPType is invalid."
        exit 1
    fi
    [ -f "${json_source_path}/nodes_sop_${IPTag}.json" ] && cp -rf ${json_source_path}/nodes_sop_${IPTag}.json ${json_source_path}/nodes_sop.json
    cp -p ${json_source_path}/nodes_sop*.json ${json_tmp_path}/
    chmod 777 ${json_tmp_path}/nodes_sop*.json
    source_node_json=${json_tmp_path}/nodes_sop.json
    new_node_json="${CUR_PATH}/newjson/node_sop.json"
    [ -f ${new_node_json} ] && rm -f ${new_node_json}
    
    if [ "X${netWorkType}" != "XO" ];then
        modify_nodesop_for_CMP
    fi
    
    CMP_IPList=""
    
    modify_nodesop_json
    
    replace_file_macro ${source_node_json} ${source_file}  
    
    nodename_list=$(source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/handlejson.py "get_all_nodename" "${exp_node_json}")
    if [ -z "${nodename_list}" -o "${nodename_list}" == "ERROR" ];then
        log_echo "ERROR" "Handle json to get all nodename of nodes_SOP.json failed, please check..."
        exit 1
    fi
    modify_nodetype_by_nodename "${nodename_list}" "APP"
    
    handle_node_ip_list="${CMP_IPList}"
    need_new="No"
    for handle_node_ip in ${handle_node_ip_list}
    do
        handle_nodetag ${handle_node_ip}
    done
    
    if [ "${need_new}" != "No" ];then
        if [ ! -f "${new_node_json}" ];then
            log_echo "ERROR" "Handle json to generate new nodes_sop.json failed, please check..."
            exit 1
        fi
    else
        log_echo "INFO" "No need to generate new nodes_sop.json"
        return 0
    fi
}

function customUniep_for_upgrade()
{
    [ "X${is_SD_upgrade}" == "XYes" ] && return 0
    if [ -d "${base_path}/UniEp/ESNCollect" ];then
        mkdir -p ${install_path}/manager/adapter/sysmt
        cp -f ${base_path}/UniEp/ESNCollect/* ${install_path}/manager/adapter/sysmt
        chown -R ossadm:${oss_group_name} ${install_path}/manager/adapter
        chmod 750 -R ${install_path}/manager/adapter
        chmod 550 ${install_path}/manager/adapter/sysmt/ESNInfoCollect.sh
        chmod 550 ${install_path}/manager/adapter/sysmt/getIPInfo.py
    fi

    if [ -f "${base_path}/UniEp/nicusage_DigitalView.cfg" ];then
        mkdir -p ${install_path}/manager/var/etc/sysmt/networkmgmt/local
        cp -f ${base_path}/UniEp/nicusage_DigitalView.cfg ${install_path}/manager/var/etc/sysmt/networkmgmt/
        cp ${base_path}/UniEp/nicusage_DigitalView_en_US.properties ${INSTALL_PATH}/manager/var/etc/sysmt/networkmgmt/local
        cp ${base_path}/UniEp/nicusage_DigitalView_zh_CN.properties ${INSTALL_PATH}/manager/var/etc/sysmt/networkmgmt/local
        chown -R ossadm:${oss_group_name} ${install_path}/manager/var/etc/sysmt
        chmod 750 -R ${install_path}/manager/var/etc/sysmt
        chmod 640 ${INSTALL_PATH}/manager/var/etc/sysmt/networkmgmt/local/*.properties
        chmod 640 ${INSTALL_PATH}/manager/var/etc/sysmt/networkmgmt/nicusage_DigitalView.cfg
    fi
    
    if [ "X${netWorkType}" == "XM" ];then
        log_echo "INFO" "scp custom file to other uniep"
        scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} -r ${install_path}/manager/adapter/sysmt  ${DVSecondary_IPV4}:${install_path}/manager/adapter
        if [ $? -ne 0 ];then
            log_echo "ERROR" "scp -o StrictHostKeyChecking=no -r ${install_path}/manager/adapter/sysmt  ${DVSecondary_IPV4}:${install_path}/manager/adapter failed,please check"
            exit 1
        fi
        
        scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} -r ${install_path}/manager/var/etc/sysmt  ${DVSecondary_IPV4}:${install_path}/manager/var/etc
        if [ $? -ne 0 ];then
            log_echo "ERROR" "scp -o StrictHostKeyChecking=no -r ${install_path}/manager/var/etc/sysmt  ${DVSecondary_IPV4}:${install_path}/manager/var/etc failed,please check"
            exit 1
        fi
        
        scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} -r ${install_path}/manager/adapter/sysmt  ${DVThird_IPV4}:${install_path}/manager/adapter
        if [ $? -ne 0 ];then
            log_echo "ERROR" "scp -o StrictHostKeyChecking=no -r ${install_path}/manager/adapter/sysmt  ${DVThird_IPV4}:${install_path}/manager/adapter failed,please check"
            exit 1
        fi
        
        scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} -r ${install_path}/manager/var/etc/sysmt  ${DVThird_IPV4}:${install_path}/manager/var/etc
        if [ $? -ne 0 ];then
            log_echo "ERROR" "scp -o StrictHostKeyChecking=no -r ${install_path}/manager/var/etc/sysmt  ${DVThird_IPV4}:${install_path}/manager/var/etc failed,please check"
            exit 1
        fi
        
    fi
    
}

function check_redis_instance()
{
    if [ -f "${install_path}/manager/apps/DBAgent/bin/dbsvc_adm" ];then
        log_echo "INFO" "Begin to check instance of redis database..."

       	${install_path}/manager/apps/DBAgent/bin/dbsvc_adm -cmd query-db-instance -tenant SOP | grep -w 'redis' > ${base_path}/redis_instance.list
        if [ $? -ne 0 ];then
            log_echo "ERROR" "dbsvc_adm -cmd query-db-instance execute failed, please check"
            exit 1
        fi
        
        instance_list=$(cat ${base_path}/redis_instance.list|grep -w 'redis'|awk -F' ' '{print $1}'|sort|uniq)
        for instance in ${instance_list}
        do
            instance_name=$(echo ${instance}|awk -F'-' '{print $1}')
            exist_numbers=$(echo ${instance_list}|sed 's/ /\n/g'|grep -w "${instance_name}"|wc -l)
            if [ ${exist_numbers} -gt 1 ];then
                log_echo "ERROR" "Redis database has duplicate instances like ${instance_name} in SOP, please contact Huawei engineer."
                exit 1
            fi
        done
    fi
}

############Upgrade Batch Preset ###################
function check_SD_upgrade()
{
    if [ -f "${base_path}/SD_upgrade.tag" ];then
        log_echo "INFO" "Find SD_upgrade.tag, this is SD upgrade."
        is_SD_upgrade="Yes"
        if [ "X$(whoami)" == "Xroot" ];then
            chown ossadm:ossgroup ${base_path}/SD_upgrade.tag && chmod 440 ${base_path}/SD_upgrade.tag
        else
            chmod 440 ${base_path}/SD_upgrade.tag
        fi
        return 0
    fi
    
    is_SD_upgrade="Y"
    
    if [ "X${is_SD_upgrade}" == "XY" ];then
        log_echo "INFO" "This is SD upgrade."
        is_SD_upgrade="Yes"
        touch ${base_path}/SD_upgrade.tag 
        chown ossadm:ossgroup ${base_path}/SD_upgrade.tag
        chmod 440 ${base_path}/SD_upgrade.tag
    elif [ "X${is_SD_upgrade}" == "XN" ];then
        log_echo "INFO" "This is not SD upgrade."
        is_SD_upgrade="No"
        rm -f /home/<USER>/SD_pwfile.properties /home/<USER>/export_product.json ${base_path}/SD_upgrade.tag
    else
        log_echo "ERROR" "The input for is_SD_upgrade is not correct, please check."
        exit 1
    fi
}


function Check_execute()
{
    local check_key=$1
    cat ${action_tag} 2> /dev/null | grep -w "${check_key} : success" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "${check_key} has been executed"
        return 0
    else
        return 1
    fi
}

function handle_SD_tag()
{
    local remote_ip=$1

    if [ "X${is_SD_upgrade}" == "XYes" ];then
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${remote_ip} rm -f /home/<USER>/SD_upgrade.tag
        echo ${remote_ip} | grep ":" > /dev/null 2>&1
        if [ $? -eq 0 ];then
            remote_ip="[${remote_ip}]"
        fi
        chmod 440 ${base_path}/SD_upgrade.tag
        scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} ${base_path}/SD_upgrade.tag ${remote_ip}:/home/<USER>
    else
        ssh -q  -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${remote_ip} rm -f /home/<USER>/SD_upgrade.tag
    fi
}

function get_current_value_of_cerkey()
{
    local config_name=$1
    json_file_path=$(ls ${base_path}/tools/expInfo/product_*.json)
    if [ "X$(whoami)" == "Xroot" ];then
    	config_value=$(su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;python ${base_path}/tools/handlejson.py \"getinfo_product\" \"${json_file_path}\" \"${config_name}\" ")
    else
    	config_value=$(source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/handlejson.py "getinfo_product" "${json_file_path}" "${config_name}")
    fi
    if [ "X${config_value}" == "XERROR" -o -z "${config_value}" ];then
        if [ "X${config_name}" == "XcountryName" ];then
            echo "CN"
        elif [ "X${config_name}" == "XorganizationName" ];then
            echo "Huawei"
        elif [ "X${config_name}" == "XcommonName" ];then
            echo "HuaweiCA"
        fi
    else
        echo ${config_value}
    fi
}

function get_current_value()
{
    local config_name=$1
    json_file_path=$(ls ${base_path}/tools/expInfo/product_*.json)
    local json_file="${json_file_path}"
    if [ ! -z "$2" ];then
        json_file="$2"
    fi
    
    if [ "X$(whoami)" == "Xroot" ];then
        config_value=$(su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;python ${base_path}/tools/handlejson.py \"getinfo_product\" \"${json_file}\" \"${config_name}\" ")
    else
        config_value=$(source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/handlejson.py "getinfo_product" "${json_file}" "${config_name}")
    fi
    if [ "${config_value}" == "ERROR" ];then
        log_echo "ERROR" "Get the value of ${config_name} from ${json_file} failed, please check"
        exit 1
    fi
    
    echo ${config_value}
}

function productext_modify()
{   
    local file_path=$1
    local config_name=$2
    local config_value=$3
    if [ "X$(whoami)" == "Xroot" ];then
    	su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;python ${base_path}/tools/handlejson.py \"productext_modify\" \"${file_path}\" \"${config_name}\" \"${config_value}\""
    else
    	source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/handlejson.py "productext_modify" "${file_path}" "${config_name}" "${config_value}"
    fi
    if [ $? -ne 0 ];then
        log_echo "ERROR" "productext_modify ${config_name} failed"
        exit 1
    fi
}

function is_need_modify()
{
    local config_name=$1
    IS_HAVE_MACRO=$(cat ${product_json_path} | grep "{{${config_name}}}")
    IS_CONFIGE_NAME_NULL=$(get_current_value "${config_name}")

    if [[ -z ${IS_CONFIGE_NAME_NULL}  || "X${IS_HAVE_MACRO}" != "X" ]];then
        echo "need"
    else
        echo "no_need"
    fi 

}

function handle_web_admin()
{
    grep "{{web_admin_user_value}}" ${product_json_path} > /dev/null 2>&1
    if [ $? -ne 0 ];then
        return 0
    fi
    
    local random_adminpwd="$(tr -dc "\@\#\%\^\*\_\+\=\,\?\.A-Za-z0-9" </dev/random| head -c 16)"
    encrypt_by_uniep "${random_adminpwd}" ""
    local random_adminpwd_encode="${pwd_return}"
    
    sed -i "s|{{web_admin_user_value}}|${random_adminpwd_encode}|g" ${product_json_path}
    
    grep "{{web_admin_user_value}}" ${product_json_path} > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "ERROR" "modify {{web_admin_user_value}} in ${product_json_path} failed, please check"
        exit 1
    fi
}

function create_change_pmdashboard_tag()
{
    log_echo "INFO" "create_change_pmdashboard_tag start..."
    local nodelists_json=/opt/oss/manager/etc/sysconf/nodelists.json
    local app_ip_list=""

   	app_ip_list=$(source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/get_install_info.py ${nodelists_json} get_app_ip)
    if [ "${app_ip_list}" == "ERROR" ];then
        log_echo "ERROR" "get app_ip_list=${app_ip_list} failed, please check"
        exit 1
    fi
    for app_ip in $(echo "${app_ip_list}"|sed "s/,/ /g");do
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${app_ip} "sudo -u ossuser touch /home/<USER>/change_pmdashboard.tag"
    done

    sudo -u ossuser touch /home/<USER>/change_pmdashboard.tag
    log_echo "INFO" "create_change_pmdashboard_tag end."
}

function delete_change_pmdashboard_tag()
{
    log_echo "INFO" "delete_change_pmdashboard_tag start..."
    local nodelists_json=/opt/oss/manager/etc/sysconf/nodelists.json
    local app_ip_list=""

   	app_ip_list=$(source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/get_install_info.py ${nodelists_json} get_app_ip)
    if [ "${app_ip_list}" == "ERROR" ];then
        log_echo "ERROR" "get app_ip_list=${app_ip_list} failed, please check"
        exit 1
    fi
    for app_ip in $(echo "${app_ip_list}"|sed "s/,/ /g");do
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${app_ip} "sudo -u ossuser bash -c \"[ -f /home/<USER>/change_pmdashboard.tag ] && rm /home/<USER>/change_pmdashboard.tag\""
    done

    sudo -u ossuser bash -c "[ -f /home/<USER>/change_pmdashboard.tag ] && rm /home/<USER>/change_pmdashboard.tag"
    log_echo "INFO" "delete_change_pmdashboard_tag end."
}

function modify_upgrade_pmdashboarddb_pwd()
{
    log_echo "INFO" "modify_upgrade_pmdashboarddb_pwd start..."
    
    delete_change_pmdashboard_tag
    
    if [ -f ${base_path}/tools/expInfo/dbinfo_SOP.json ];then
        local is_pmdashboarddb_exist=$(cat ${base_path}/tools/expInfo/dbinfo_SOP.json | grep -w "\"dbname\":\"pmdashboarddb\"")
        if [ ! -z "$is_pmdashboarddb_exist" ];then
            log_echo "INFO" "pmdashboarddb is exist, not need to change user pwd."
            return 0
        fi
    fi
    
    local db_json=/home/<USER>/db_containerlist.json
    local containerlist_json=/opt/oss/manager/var/tenants/SOP/containerlist.json
    local nodelists_json=/opt/oss/manager/etc/sysconf/nodelists.json
    if [ "X$(whoami)" == "Xroot" ];then
        chown ossadm:ossgroup ${CUR_PATH}/get_install_info.py
    fi
    chmod 755 ${CUR_PATH}/get_install_info.py
    touch ${db_json}
    chmod 600 ${db_json}
    cat ${nodelists_json} |grep -w "ALL_IN_ONE_NODE\|DVPrimaryNode" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        ## O or M 
        cp -rpf ${containerlist_json} ${db_json}
    else
        ## "nodename": "dbnode",  "nodename": "DB_Primarynode",
        local db_ip=$(source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/get_install_info.py ${nodelists_json} get_db_ip)
        if [ "${db_ip}" == "ERROR" ];then
            log_echo "ERROR" "get db_ip=${db_ip} failed, please check"
            exit 1
        fi

       	ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${db_ip} "cat ${containerlist_json}" > ${db_json}
    fi
    if [ "X$(whoami)" == "Xroot" ];then
        chown ossadm:ossgroup ${db_json}
    fi

    chmod 440 ${db_json}
    local syspwd=""

    syspwd=$(source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python  ${CUR_PATH}/get_install_info.py ${db_json} get_syspwd)
    if [ "${syspwd}" == "ERROR" ];then
        log_echo "ERROR" "get syspwd is ERROR, please check"
        exit 1
    fi

    syspwd=$(source ${INSTALL_PATH}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python -c "from util import ossext; tmp=ossext.Cipher.decrypt('${syspwd}'); print(tmp)")
    if [ "X${syspwd}" == "X" ];then
        log_echo "ERROR" "get zenith syspwd failed, please check"
        exit 1
    fi
    if [ -f "/opt/oss/manager/etc/cipher/cloudsop_cipher_mode.flag" ];then
        encrypt_by_uniep "${syspwd}" ""
    else    
        encrypt_by_uniep "${syspwd}" "SOP"   
    fi
    new_syspwd="${pwd_return}"
    productext_modify "${product_json_path}" "upgrade_pmdashboarddb_pwd" "${new_syspwd}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "productext_modify ${product_json_path} upgrade_pmdashboarddb_pwd failed, please check"
        exit 1
    fi
    
    create_change_pmdashboard_tag
    
    log_echo "INFO" "productext_modify ${product_json_path} upgrade_pmdashboarddb_pwd successfully."
}

function modify_pm_data_db_size()
{
    local current_PM_DATA_DB_SIZE_1_value=$(get_current_value "PM_DATA_DB_SIZE_1")
    if [ -n "${current_PM_DATA_DB_SIZE_1_value}" ] && [ "${current_PM_DATA_DB_SIZE_1_value}" != "{{PM_DATA_DB_SIZE_1}}" ];then
        log_echo "INFO" "The current_PM_DATA_DB_SIZE_1_value=${current_PM_DATA_DB_SIZE_1_value} is not empty."
        return 1
    fi

    local pm_cluster_num=6
    for (( j = 1; j <= $pm_cluster_num; j++ )); do
        productext_modify "${product_json_path}" "PM_DATA_DB_SIZE_$j" "${PM_NODE_DATA_DB_DEFAULT_SIZE}"
    done
    log_echo "INFO" "modify pm nodes data db size to ${PM_NODE_DATA_DB_DEFAULT_SIZE} successfully."
}

function handle_kafka_retention_time()
{
    local current_kafka_retention_time=$(get_current_value "Kafka_Retention_Time")
    log_echo "INFO" "Get current Kafka_Retention_Time=${current_kafka_retention_time}."

    local kafka_retention_time_cfg=$(cat ${dv_cfg_file}.tmp | grep "^Kafka_Retention_Time=" | awk -F "=" '{print $2}')
    if [ -n "${kafka_retention_time_cfg}" ]; then
        current_kafka_retention_time="${kafka_retention_time_cfg}"
    fi
    productext_modify "${product_json_path}" "Kafka_Retention_Time" "${current_kafka_retention_time}"
    log_echo "INFO" "modify Kafka_Retention_Time to ${current_kafka_retention_time} successfully."
}

function check_uniep_status()
{
    local error_count=0
    local ret=0
    # 检查五分钟
    while true; do
      if [ ${error_count} -gt 60 ]; then
          log_echo "ERROR" "Timeout for waiting the uniEP start! Please Check!"
          exit 1
      fi
      export HISTSIZE=0;
      source ${install_path}/manager/agent/MaintenanceService/lbin/engr_profile.sh;
      python ${base_path}/tools/autoupgrade/tools/call_ir_rest.py get_uniep_service_status
      ret=$?
      if [ ${ret} -eq 0 ]; then
          log_echo "INFO" "UniEP service status check SUCCESS !"
          break
      else
          log_echo "WARN" "The UniEP service is being started. error_count = ${error_count}"
          error_count=$((error_count+1))
          sleep 5
      fi
    done
}

function check_restart_uniep()
{
    log_echo "INFO" "check_restart_uniep start..."
    local dvsecondary_ssh_ip=""
    if [ "X${netWorkType}" == "XM" ];then
        dvsecondary_ssh_ip="${DVSecondary_IPV4}"
        if [ "X${DVSecondary_MGR_IPV6}" != "X" ];then
            dvsecondary_ssh_ip="${DVSecondary_MGR_IPV6}"
        fi
    fi
    log_echo "INFO" "The netWorkType=${netWorkType} dvsecondary_ssh_ip=${dvsecondary_ssh_ip}"
    local uniep_log="/opt/oss/log/manager/UniEPService/uniepservice-*/tomcatlog/catalina.out /opt/oss/log/manager/UniEPLiteService/uniepliteservice-*/tomcatlog/catalina.out /var/log/oss/manager/UniEPService/uniepservice-*/tomcatlog/catalina.out /var/log/oss/manager/UniEPLiteService/uniepliteservice-*/tomcatlog/catalina.out"
    local tmp_log=""
    local tmp_ret=""
    local tmp_ret1=""
    local tmp_ret2=""
    local check_ret=0
    for tmp_log in ${uniep_log};do
        if [ ! -f "${tmp_log}" ];then
            log_echo "INFO" "The ${tmp_log} is not exist.continue."
            continue
        fi
        tmp_ret1=$(grep -w "StackOverflowError" ${tmp_log})
        tmp_ret2=$(grep -w "remoteCommandExecution" ${tmp_log})
        echo "$(date) The tmp_ret1=${tmp_ret1} tmp_ret2=${tmp_ret2} of tmp_log=${tmp_log}" >> ${LOG_FILE} 2>&1
        if [ ! -z "${tmp_ret1}" -a ! -z "${tmp_ret2}" ];then
            log_echo "INFO" "The ${tmp_log} has error : StackOverflowError and remoteCommandExecution."
            check_ret=1
            break
        fi
        if [ "X${netWorkType}" == "XM" ];then
            tmp_ret=$(ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${dvsecondary_ssh_ip} "[ -f ${tmp_log} ] && echo Yes || echo No")
            if [ "${tmp_ret}" == "No" ];then
                log_echo "INFO" "The ${tmp_log} is not exist,continue.at node ${dvsecondary_ssh_ip}"
                continue
            fi
            tmp_ret1=$(ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${dvsecondary_ssh_ip} "grep -w 'StackOverflowError' ${tmp_log}")
            tmp_ret2=$(ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${dvsecondary_ssh_ip} "grep -w 'remoteCommandExecution' ${tmp_log}")
            echo "$(date) The tmp_ret1=${tmp_ret1} tmp_ret2=${tmp_ret2} of tmp_log=${tmp_log} at node ${dvsecondary_ssh_ip}" >> ${LOG_FILE} 2>&1
            if [ ! -z "${tmp_ret1}" -a ! -z "${tmp_ret2}" ];then
                log_echo "INFO" "The ${tmp_log} has error : StackOverflowError and remoteCommandExecution.at node ${dvsecondary_ssh_ip}"
                check_ret=1
                break
            fi
        fi
    done
    if [ ${check_ret} -eq 1 ];then
        . /opt/oss/manager/bin/engr_profile.sh
        if [ -d /opt/oss/manager/apps/UniEPService ];then
            ipmc_adm -cmd restartapp -app UniEPService -tenant manager
            log_echo "INFO" "restart uniep:[ ipmc_adm -cmd restartapp -app UniEPService -tenant manager ],ret=$?"
        else
            ipmc_adm -cmd restartapp -app UniEPLiteService -tenant manager
            log_echo "INFO" "restart uniep:[ ipmc_adm -cmd restartapp -app UniEPLiteService -tenant manager ],ret=$?"
        fi
        if [ "X${netWorkType}" == "XM" ];then
            tmp_ret=$(ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${dvsecondary_ssh_ip} "[ -d /opt/oss/manager/apps/UniEPService ] && echo Yes || echo No")
            if [ "${tmp_ret}" == "Yes" ];then
                ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${dvsecondary_ssh_ip} ". /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd restartapp -app UniEPService -tenant manager"
                log_echo "INFO" "restart uniep:[ ipmc_adm -cmd restartapp -app UniEPService -tenant manager ] at node ${dvsecondary_ssh_ip},ret=$?"
            else
                ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${dvsecondary_ssh_ip} ". /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd restartapp -app UniEPLiteService -tenant manager"
                log_echo "INFO" "restart uniep:[ ipmc_adm -cmd restartapp -app UniEPLiteService -tenant manager ] at node ${dvsecondary_ssh_ip},ret=$?"
            fi
        fi
        check_uniep_status
    fi
    log_echo "INFO" "check_restart_uniep End."
    return ${check_ret}
}

function import_to_uniep()
{
    new_json_path="${base_path}/tools/newjson"
    product_json_path=$(ls ${exp_product_path}/product_*.json)
    if [ -f "${new_json_path}/product_sop.json" ];then
        product_json_path=${new_json_path}/product_sop.json
    fi
    
    if [ "X${netWorkType}" == "XT" -o "X${netWorkType}" == "XC" -o "X${netWorkType}" == "XM" ];then
       
        local zenith_paramgroup_file="large"
        if [ -f ${base_path}/zenith_paramgroup_file.properties ];then
            zenith_paramgroup_file=$(cat ${base_path}/zenith_paramgroup_file.properties |grep -w "zenith_paramgroup_file" |awk -F'=' '{print $2}')
        fi
        
        ## zenith_paramgroup_file  large  largeCapacity
        if [ -z "${zenith_paramgroup_file}" ];then
            zenith_paramgroup_file="large"
        fi
        
        sed -i "s#{{zenith_paramgroup_file}}#${zenith_paramgroup_file}#g" ${product_json_path}
        local CurrentAlarmCapacity="Default"
        if [ "X${netWorkType}" == "XM" ];then
            local dv_deploy_scale_size_old=$(get_current_value "dv_deploy_scale_size")
            if [ -z "${dv_deploy_scale_size_old}" -o "${dv_deploy_scale_size_old}" == "default" ];then
                CurrentAlarmCapacity="100000"
            fi
        fi
        log_echo "INFO" "netWorkType=${netWorkType} dv_deploy_scale_size_old=${dv_deploy_scale_size_old} CurrentAlarmCapacity=${CurrentAlarmCapacity}"
        
        if [ "${zenith_paramgroup_file}" == "largeCapacity" ];then
            CurrentAlarmCapacity="500000"
            if [ "X${netWorkType}" == "XC" ];then
                productext_modify "${product_json_path}"  "topology_scale" "medium"
            elif [ "X${netWorkType}" == "XM" ];then
                productext_modify "${product_json_path}"  "topology_scale" "large"
            fi
        fi  
        log_echo "INFO" "zenith_paramgroup_file=${zenith_paramgroup_file} CurrentAlarmCapacity=${CurrentAlarmCapacity}"
        sed -i "s#{{CurrentAlarmCapacity}}#${CurrentAlarmCapacity}#g" ${product_json_path}
        source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/handlejson.py "productext_modify" "${product_json_path}" "CurrentAlarmCapacity" "${CurrentAlarmCapacity}" && \
        source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/handlejson.py "productext_modify" "${product_json_path}" "zenith_paramgroup_file" "${zenith_paramgroup_file}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "productext_modify CurrentAlarmCapacity or zenith_paramgroup_file failed"
            exit 1
        fi
        chmod 755 ${base_path}/tools/handlejson.py ${base_path}/tools/handlejson.log ${product_json_path}

    fi
    
    modify_product_json
    
    modify_upgrade_pmdashboarddb_pwd

    . ${source_file}
    enablePMServiceMode_now=$(get_current_value "enablePMServiceMode")
    if [ "X${enablePMServiceMode_now}" != "XTRUE" -a "`echo ${enablePMServiceMode}|tr 'a-z' 'A-Z'`" == "TRUE" ];then
        productext_modify "${product_json_path}"  "enablePMServiceMode" ${enablePMServiceMode}
    fi 

    enableNBIServiceMode_old=$(get_current_value "enableNBIServiceMode")
    if [ "X${enableNBIServiceMode_old}" != "XTRUE" -a "$(echo ${enableNBIServiceMode}|tr 'a-z' 'A-Z')" == "TRUE" ];then
        productext_modify "${product_json_path}"  "enableNBIServiceMode" ${enableNBIServiceMode}
    fi
    
    DV_SEC_RUNAS_USER_OLD=$(get_current_value "DV_SEC_RUNAS_USER")
    if [ -z "${DV_SEC_RUNAS_USER_OLD}" ];then
        productext_modify "${product_json_path}"  "DV_SEC_RUNAS_USER" "ossuser"
    fi
    
    CMP_LOCAL_IP_LIST_OLD=$(get_current_value "CMP_LOCAL_IP_LIST")
    if [ -z "${CMP_LOCAL_IP_LIST_OLD}" ];then
        productext_modify "${product_json_path}"  "CMP_LOCAL_IP_LIST" "cmpnodeip"
    fi
    
    ##旧参数获取为空时，表示从无到有，新增参数，使用配置的值，已有能力，不需要做处理。
    ##旧参数获取为True时，继承，保持为True，不支持再改回false.
    ##旧参数获取为False时，如果配置参数配置的为True。修改为True。
    is_extend_internal_certificate_validity_period_old=$(get_current_value "is_extend_internal_certificate_validity_period")
    if [ "${is_extend_internal_certificate_validity_period_old}" == "True" ];then
        log_echo "INFO" "The is_extend_internal_certificate_validity_period_old=${is_extend_internal_certificate_validity_period_old}"
        productext_modify "${product_json_path}"  "is_extend_internal_certificate_validity_period" "True"
    elif [ "${is_extend_internal_certificate_validity_period_old}" == "False" ];then
        log_echo "INFO" "The is_extend_internal_certificate_validity_period_old=${is_extend_internal_certificate_validity_period_old} is_extend_internal_certificate_validity_period=${is_extend_internal_certificate_validity_period}"
        productext_modify "${product_json_path}"  "is_extend_internal_certificate_validity_period" "${is_extend_internal_certificate_validity_period}"
    else
        log_echo "INFO" "The else is_extend_internal_certificate_validity_period=${is_extend_internal_certificate_validity_period}"
    fi
    
    if [ "X${netWorkType}" == "XC" -o "X${netWorkType}" == "XL" -o "X${netWorkType}" == "XM" ];then
        ##旧参数获取为空时，表示从无到有，新增参数，配置为默认值false. 如果不为空，表示已存在参数，继承即可。
        enableNetworkElementLargeCapacity_old=$(get_current_value "enableNetworkElementLargeCapacity")
        log_echo "INFO" "The enableNetworkElementLargeCapacity_old=${enableNetworkElementLargeCapacity_old}"
        if [ -z "${enableNetworkElementLargeCapacity_old}" ];then
            log_echo "INFO" "The enableNetworkElementLargeCapacity_old is null.modify ${product_json_path} the key enableNetworkElementLargeCapacity value to false."
            productext_modify "${product_json_path}"  "enableNetworkElementLargeCapacity" "false"
        fi
    fi
    
    featureRelation_old=$(get_current_value "featureRelation")
    log_echo "INFO" "The old featureRelation_old=${featureRelation_old}"
    local json_file="${json_tmp_path}/product_sop.json"
    local featureRelation=$(source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/handlejson.py getinfo_product ${json_file} featureRelation)

    log_echo "INFO" "The new1 featureRelation=${featureRelation}"
    if [ "X${featureRelation}" == "XERROR" ];then
        log_echo "ERROR" "The json_file=${json_file} get featureRelation is ERROR.please check log: ${base_path}/tools/handlejson.log"
        exit 1
    fi
    
    if [ "X$(whoami)" == "Xroot" ];then
        featureRelation=$(echo ${featureRelation} | sed  's/\"/\\"/g')
    fi
    log_echo "INFO" "The new2 featureRelation=${featureRelation}"
    
    productext_modify "${product_json_path}"  "featureRelation" "${featureRelation}"

    featureRelation=$(source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/handlejson.py getinfo_product ${product_json_path} featureRelation)

    log_echo "INFO" "The modify after featureRelation=${featureRelation}"
    
    if [ "X${sudo_drbd_disable_cmd}" != "X" ];then
        productext_modify "${product_json_path}" "need_drbd" "NO"
    fi
    if [ "`echo ${is_integrated_df_and_service}|tr 'a-z' 'A-Z'`" == "YES" ];then
        productext_modify "${product_json_path}"  "integrated_mode" "DigitalFoundry-DigitalView-service co-deployment"
    fi
    sed -i "s#{{DV_SHARE_PATH}}#${INSTALL_PATH}/share/SOP/DVEngineeringService/dvshare#g" ${product_json_path}
    [ -f "${base_path}/express.tag" ] && productext_modify "${product_json_path}" "IS_INSTALL_ICNFG" "No"
    [ -f "${base_path}/express.tag" ] && productext_modify "${product_json_path}" "is_support_express" "true"
    
    sed -i "s/{{enableLuceneQueryForHistoryAlarm}}/false/g" ${product_json_path}
    
    set_cmp_key_for_product_json "${product_json_path}"
    
    handle_web_admin

    handle_kafka_retention_time
    
    if [ "X${CurrentAlarmCapacity}" == "XDefault" -o "X${CurrentAlarmCapacity}" == "X"  ];then
        ## not need to modify
        source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/handlejson.py "productext_delete" "${product_json_path}" "CurrentAlarmCapacity"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "productext_delete CurrentAlarmCapacity failed"
            exit 1
        fi
    fi

    if [ ! -f "${new_json_path}/node_sop.json" ]; then
        if [ "X${netWorkType}" == "XM" ] || [ "X${netWorkType}" == "XC" ];then
            test -d ${new_json_path} || mkdir -p ${new_json_path}
            cp -rpf ${exp_product_path}/nodes_SOP.json ${new_json_path}/node_sop.json
        fi
    fi

    if [ "X${netWorkType}" == "XM" ];then
        modify_node_and_product_json "upgrade" "${new_json_path}/node_sop.json" "${product_json_path}"
    elif [ "X${netWorkType}" == "XC" ];then
        cluster_upgrade_modify_node_json "${new_json_path}/node_sop.json"
    fi
    
    if [ -f "${new_json_path}/node_sop.json" ];then
        nodes_json_path=${new_json_path}/node_sop.json
        modify_nodesop_script="${install_path}/manager/tools/resmgr/modifynodesinfo.sh"
        sh ${modify_nodesop_script} -input ${nodes_json_path} >> ${LOG_FILE} 2>&1
        if [ $? -ne 0 ];then
            check_restart_uniep
            if [ $? -ne 0 ];then
                log_echo "INFO" "Fail to modify nodesop info, the file is ${nodes_json_path},try again..."
                sh ${modify_nodesop_script} -input ${nodes_json_path} >> ${LOG_FILE} 2>&1
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Fail to try again modify nodesop info, the file is ${nodes_json_path}..."
                    exit 1
                fi
            else
                log_echo "ERROR" "Fail to modify nodesop info, the file is ${nodes_json_path}..."
                exit 1
            fi
        fi
        log_echo "INFO" "modify nodesop info successfully, the file is ${nodes_json_path}..."
    fi
    
    if [ -f "${product_json_path}" ];then
        cat "${CUR_PATH}/expInfo/product_SOP.json" | grep "p12CertPbeMode" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            cat "${product_json_path}" | grep "p12CertPbeMode" > /dev/null 2>&1
            if [ $? -eq 0 ];then
             	source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/handlejson.py "productext_delete" "${product_json_path}" "p12CertPbeMode"
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "productext_delete p12CertPbeMode failed"
                    exit 1
                fi
            fi
            log_echo "The old version has not p12CertPbeMode, delete it from product_SOP.json"
        else
            log_echo "The old version has p12CertPbeMode, not need to modify product_SOP.json"
        fi
        
        if [ -f "/.dockerenv" -a "X${netWorkType}" == "XM" ];then
            log_echo "This dock env and net type is M.need to root modify product."
        else
            modify_product_script="${install_path}/manager/tools/resmgr/modifyproductinfo.sh"
            sh ${modify_product_script} -input ${product_json_path} >> ${LOG_FILE} 2>&1
            if [ $? -ne 0 ];then
                check_restart_uniep
                if [ $? -ne 0 ];then
                    log_echo "INFO" "Fail to modify product info, the file is ${product_json_path},try again..."
                    sh ${modify_product_script} -input ${product_json_path}
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "Fail to modify product info, the file is ${product_json_path}..."
                        exit 1
                    fi
                else
                    log_echo "ERROR" "Fail to modify product info, the file is ${product_json_path}..."
                    exit 1
                fi
            fi
            log_echo "INFO" "modify product info successfully, the file is ${product_json_path}..."
        fi
    fi
}

function handle_product_info()
{
    json_source_path="${netWorkPath}/Product_template"
    json_tmp_path=${base_path}/network_${netWorkType}
    
    mkdir -p ${json_tmp_path}
    chmod 755 ${json_tmp_path}
    [ ! -f "${base_path}/tools/handlejson.log" ] && touch ${base_path}/tools/handlejson.log
    chmod 777 ${base_path}/tools/handlejson.log
    
    Check_execute "handle_product"
    ret=$?
    if [ ${ret} -ne 0 ];then
        handle_product_json
        if [ $? -eq 0 ];then
            echo "handle_product : success" >> ${action_tag}
        fi
    fi
    
    Check_execute "handle_node"
    ret=$?
    if [ ${ret} -ne 0 ];then
        handle_node_json
        if [ $? -eq 0 ];then
            echo "handle_node : success" >> ${action_tag}
        fi
    fi
    
}

function add_login_user()
{   
    . ${dv_cfg_file}.tmp
    id ${system_sftp_user} >/dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "The ${system_sftp_userid} already exited . no creat it ..."
    else
        useradd -g ${i2k_group_id} -u ${system_sftp_userid} -m ${system_sftp_user} -d ${system_sftp_userhome} -s /bin/false || die "Create user failed! Please check by command \"useradd -g ${i2k_group_id} -u ${system_sftp_userid} -m ${system_sftp_user} -d ${system_sftp_userhome} \""
        chage -M 99999 ${system_sftp_user}
        echo_pwd "Please input os(sftpossuser) password"
        sftpossuser_pwd="$PwdReturn"
        echo_pwd "Please input os(sftpossuser) password again"
        sftpossuser_pwd_new="$PwdReturn"
        if [ "X${sftpossuser_pwd}" != "X${sftpossuser_pwd_new}" ];then
            log_echo "ERROR" "The two os user passwords do not match"
            exit 1
        fi
        set_user_passwd "${system_sftp_user}" "${sftpossuser_pwd}"
    fi
   
}

function handle_dr_properties()
{
    if [ -f "${install_path}/manager/apps/DVDRMgrService/dvDRMgr/dr.properties" ];then
        cp -pf ${install_path}/manager/apps/DVDRMgrService/dvDRMgr/dr.properties ${install_path}/share/manager/DVDRMgrService/dr.properties
        chmod 600 ${install_path}/share/manager/DVDRMgrService/dr.properties
        if [ "X${netWorkType}" == "XM" ];then
            log_echo "INFO" "scp dr.properties to other uniep"
            ip_list="${DVSecondary_IPV4} ${DVThird_IPV4}"
            for ip in ${ip_list}
            do
                echo ${ip} |grep ":"
                if [ $? -eq 0 ];then
                    ip="[${ip}]"
                fi
                ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${ip} ls ${install_path}/manager/apps/DVDRMgrService/dvDRMgr/dr.properties 2>/dev/null
                if [ $? -eq 0 ];then
                    scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} ${install_path}/share/manager/DVDRMgrService/dr.properties ${ip}:${install_path}/share/manager/DVDRMgrService/  >>$LOG_FILE  2>&1
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "scp dr.properties to ${ip} failed,please check"
                        exit 1
                    fi
                fi
            done
        fi
    fi
}

function get_use_forwarded_ip()
{
    local HIROER_config_file="/opt/oss/manager/apps/MCHIROERService/envs/env.properties"

    if [ -f ${HIROER_config_file} ];then
        use_forwarded_ip=$(${ssh_cmd} grep -w "useXForwardedIPAddr" "${HIROER_config_file}"| awk -F '=' '{print $2}')

        if [ "X${use_forwarded_ip}" == "Xtrue" ];then
            log_echo "INFO" "check HIROERService in APP node, and use_forwarded_ip equal true"
            return 0
        fi
    fi

    use_forwarded_ip=$(grep -Po 'use_forwarded_ip[" =]+\K[^"]+' ${base_path}/Common/DV_config.properties.tmp)

    if [ -z "${use_forwarded_ip}" ];then
        use_forwarded_ip=false
    fi
    log_echo "INFO" "set use_forwarded_ip as ${use_forwarded_ip}"
}

function UniEp_Upgrade()
{
    cat ${action_tag} 2> /dev/null | grep -w "UniEp_Upgrade : success" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "UniEp_Upgrade has been executed"
        return 0
    fi
    
    check_dir_size
    
    check_redis_instance

    if [ ! -f "${install_path}/manager/etc/sysconf/userpw.json" ];then
        enter_password "Please input os(protect_pvalue) password" "db" "cipher"
        if [ $? -eq 0 ];then
            protect_pwd=${en_pwd}
            echo "protect_password=${protect_pwd}">/home/<USER>/protect_pvalue
         		chmod 600 /home/<USER>/protect_pvalue
        fi
    fi

    log_echo "INFO" "begin to upgrade uniep"
    
    if [ "X${is_SD_upgrade}" != "XYes" ];then
        sudoconfigpath=${base_path}/CloudSop/$(ls ${base_path}/CloudSop | grep sudoconfig)
        osconfigpath=${base_path}/CloudSop/$(ls ${base_path}/CloudSop | grep osconfig)
        if [ ! -f ${sudoconfigpath} -o ! -f ${osconfigpath} ];then
            log_echo "ERROR" "sudoconfigpath or osconfigpath not exit, please check!"
            exit 1
        fi
        
        if [ ! -f ${base_path}/UniEp/UniEPMgr/upgrade.sh -a -f ${base_path}/UniEp/UniEPMgr*.zip ];then
            unzip -o ${base_path}/UniEp/UniEPMgr*.zip -d ${base_path}/UniEp/UniEPMgr
            chown -R ossadm:ossgroup ${base_path}/UniEp
            if [ ! -f ${base_path}/UniEp/UniEPMgr/upgrade.sh ];then
                log_echo "ERROR" "uniep upgrade.sh not exit, please check!"
                exit 1
            fi
        fi
        if [ "X${netWorkType}" == "XO" ];then
            log_echo "INFO" "Begin to customize uniep for all in one."
            cp -rp ${install_path}/manager/var/tenants/SOP/containerlist.json /home/<USER>
        fi
        sed -i "/^UPGRADEWIZARD=open/d" ${install_path}/manager/var/etc/common/custom.cfg
        if [ "X${netWorkType}" == "XL" -o "X${netWorkType}" == "XM" ];then
            sed -i "s/IS_SUPPORT_LVS=.*/IS_SUPPORT_LVS=yes/" ${INSTALL_PATH}/manager/var/etc/common/custom.cfg
        fi
    else
        unzip -o ${base_path}/UniEp/UniEPMgr*.zip software_define.yaml -d ${base_path}/UniEp/UniEPMgr
        grep -w "^UPGRADEWIZARD=open" ${install_path}/manager/var/etc/common/custom.cfg > /dev/null 2>&1
        if [ $? -ne 0 ];then
            sed -i "/^UPGRADEWIZARD=/d" ${install_path}/manager/var/etc/common/custom.cfg
            echo "UPGRADEWIZARD=open" >> ${install_path}/manager/var/etc/common/custom.cfg
            if [ "X${netWorkType}" == "XL" -o "X${netWorkType}" == "XM" ];then
                sed -i "s/IS_SUPPORT_LVS=.*/IS_SUPPORT_LVS=yes/" ${INSTALL_PATH}/manager/var/etc/common/custom.cfg
            fi
        fi
        if [ "X${netWorkType}" == "XM" ];then

            log_echo "INFO" "scp custom.cfg to other uniep"
            if [ "X${IPType}" != "X0" ];then
                scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} -r ${install_path}/manager/var/etc/common/custom.cfg  [${DVSecondary_MGR_IPV6}]:${install_path}/manager/var/etc/common/  >>$LOG_FILE  2>&1
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "scp -o StrictHostKeyChecking=no -r ${install_path}/manager/var/etc/common/custom.cfg [${DVSecondary_MGR_IPV6}]:${install_path}/manager/var/etc/common/ failed,please check"
                    exit 1
                fi
                scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} -r ${install_path}/manager/var/etc/common/custom.cfg  [${DVThird_MGR_IPV6}]:${install_path}/manager/var/etc/common/ >>$LOG_FILE  2>&1
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "scp -o StrictHostKeyChecking=no -r ${install_path}/manager/var/etc/common/custom.cfg  [${DVThird_MGR_IPV6}]:${install_path}/manager/var/etc/common/ failed,please check"
                    exit 1
                fi
            else
                scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} -r ${install_path}/manager/var/etc/common/custom.cfg  ${DVSecondary_IPV4}:${install_path}/manager/var/etc/common/  >>$LOG_FILE  2>&1
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "scp -o StrictHostKeyChecking=no -r ${install_path}/manager/var/etc/common/custom.cfg  ${DVSecondary_IPV4}:${install_path}/manager/var/etc/common/ failed,please check"
                    exit 1
                fi
                scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} -r ${install_path}/manager/var/etc/common/custom.cfg  ${DVThird_IPV4}:${install_path}/manager/var/etc/common/ >>$LOG_FILE  2>&1
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "scp -o StrictHostKeyChecking=no -r ${install_path}/manager/var/etc/common/custom.cfg  ${DVThird_IPV4}:${install_path}/manager/var/etc/common/ failed,please check"
                    exit 1
                fi
            fi
                
        fi
        
        handle_dr_properties
        
        rm -f /home/<USER>/uniep_baseversion
        uniep_baseversion=$(cat ${install_path}/manager/var/share/software_define.yaml |grep "softwareVersion:" | awk -F ':' '{print $2}' |  sed "s# ##g" )
        echo "uniep_baseversion=${uniep_baseversion}" > /home/<USER>/uniep_baseversion
        chmod 600 /home/<USER>/uniep_baseversion
    fi
    

    delete_old_cipher

    if [ "X${is_SD_upgrade}" != "XYes" ];then
    
        old_uniep_verison=$(cat ${install_path}/manager/var/share/software_define.yaml |grep "softwareVersion:" | awk -F ':' '{print $2}' |  sed "s# ##g" )
        
        new_uniep_version=$(cat ${base_path}/UniEp/UniEPMgr/software_define.yaml|grep "softwareVersion:" | awk -F ':' '{print $2}' |  sed "s# ##g" )
        
        rm -rf ${base_path}/uniep_same_version.tag
        if [ "X${old_uniep_verison}" == "X${new_uniep_version}" -a ! -f "${base_path}/uniep_upgrade_error.tag" ];then
            touch ${base_path}/uniep_same_version.tag
            return 0
        fi

        heartBeat_ips=$(grep -Po 'DATA_MGMT_SERVICE_IP[" :]+\K[^"]+' ${exp_product_path}/product*.json)
        get_use_forwarded_ip
        echo "{\"heartBeat_ips\": \"${heartBeat_ips}\",\"IS_SEND_MQ_AUDITLOG\": \"true\",\"IS_ENABLE_CN_AND_EN_AUDITLOGS\": \"true\",\"useXForwardedIPAddr\": \"${use_forwarded_ip}\"}" > ${base_path}/UniEp/UniEPMgr/inputmgr.json
        chown ossadm:ossgroup ${base_path}/UniEp/UniEPMgr/inputmgr.json
        chmod 750 ${base_path}/UniEp/UniEPMgr/inputmgr.json
        
        bash ${base_path}/UniEp/UniEPMgr/upgrade.sh -path ${install_path} -sudoconfigpath ${sudoconfigpath}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "sudoconfig upgrade failed on UniEP Node ${UNIEP_NODE_IP}..."
            touch ${base_path}/uniep_upgrade_error.tag
            exit 1
        fi
        
        bash ${base_path}/UniEp/UniEPMgr/upgrade.sh -path ${install_path} -osconfigpath ${osconfigpath}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "osconfig upgrade failed on UniEP Node ${UNIEP_NODE_IP}..."
            touch ${base_path}/uniep_upgrade_error.tag
            exit 1
        fi
        bash ${base_path}/UniEp/UniEPMgr/upgrade.sh -path ${install_path} -param ${base_path}/UniEp/UniEPMgr/inputmgr.json -pwfile ${base_path}/UniEp/pwfile.json
        if [ $? -ne 0 ];then
            log_echo "ERROR" "uniep upgrade failed on UniEP Node ${UNIEP_NODE_IP}..."
            touch ${base_path}/uniep_upgrade_error.tag
            exit 1
        fi
    fi
    del_outclient_pfx_without_pwd
    echo "UniEp_Upgrade : success" >> ${action_tag}
}

function UniEpDB_Upgrade()
{
    if [ "X${is_SD_upgrade}" == "XYes" ];then
        log_echo "INFO" "SD upgrade , no need to do UniEpDB_Upgrade"
        return 0
    fi

    cat ${action_tag} 2> /dev/null | grep -w "UniEp_UpgradeDB : success" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "UniEp_UpgradeDB has been executed"
        return 0
    fi
    if [ "X${netWorkType}" == "XO" -o "X${netWorkType}" == "XM" ];then
        log_echo "INFO" "Begin to upgrade uniep db for netWorkType=${netWorkType} scene."
        upgrade_db_sh=$(ls ${install_path}/manager/apps/DeployAgent-*/tools/shscript/upgradedb.sh | head -1)

	      bash ${upgrade_db_sh} -timeout 30
        if [ $? -ne 0 ];then
            log_echo "ERROR" "upgrade uniep db failed..."
            exit 1
        fi
    fi
    echo "UniEp_UpgradeDB : success" >> ${action_tag}
}

function Config_Backuprestore()
{
    
    if [ "X${is_SD_upgrade}" != "XYes" ];then
    
        log_echo "INFO" "config backuprestore start."
        mkdir -p ${install_path}/manager/var/etc/backuprestore
        [ $? -ne 0 ] && log_echo "ERROR" "exc cmd [mkdir -p ${install_path}/manager/var/etc/backuprestore] failed."
        
        cp -rpf ${base_path}/UniEp/servicegroup_DigitalView.json ${install_path}/manager/var/etc/backuprestore
        [ $? -ne 0 ] && log_echo "ERROR" "exc cmd [cp -rpf ${base_path}/UniEp/servicegroup_DigitalView.json ${install_path}/manager/var/etc/backuprestore] failed."
        
        cp -rpf ${base_path}/UniEp/servicegroup_DigitalView_separate.json ${INSTALL_PATH}/manager/var/etc/backuprestore
        [ $? -ne 0 ] && log_echo "ERROR" "exc cmd [cp -rpf ${base_path}/UniEp/servicegroup_DigitalView_separate.json ${INSTALL_PATH}/manager/var/etc/backuprestore] failed."
        
        cp -rpf ${base_path}/UniEp/staticapp_DigitalView.json ${install_path}/manager/var/etc/backuprestore
        [ $? -ne 0 ] && log_echo "ERROR" "exc cmd [cp -rpf ${base_path}/UniEp/staticapp_DigitalView.json ${install_path}/manager/var/etc/backuprestore] failed."
        
        cp -rpf ${base_path}/UniEp/backuprestore.cfg ${install_path}/manager/var/etc/backuprestore
        [ $? -ne 0 ] && log_echo "ERROR" "exc cmd [cp -rpf ${base_path}/UniEp/backuprestore.cfg ${install_path}/manager/var/etc/backuprestore] failed."
        
        cp -rpf ${base_path}/UniEp/backupconfigfile_DigitalView.json   ${install_path}/manager/var/etc/backuprestore/
        [ $? -ne 0 ] && log_echo "ERROR" "exc cmd [cp -rpf ${base_path}/UniEp/backupconfigfile_DigitalView.json ${install_path}/manager/var/etc/backuprestore] failed."
        
        chown -R ossadm:${oss_group_name} ${install_path}/manager/var/etc/backuprestore
        [ $? -ne 0 ] && log_echo "ERROR" "exc cmd [chown -R ossadm:${oss_group_name} ${install_path}/manager/var/etc/backuprestore] failed."
        
        chmod 750 -R ${install_path}/manager/var/etc/backuprestore
        [ $? -ne 0 ] && log_echo "ERROR" "exc cmd [chmod 750 -R ${install_path}/manager/var/etc/backuprestore] failed."

        chmod 640 ${install_path}/manager/var/etc/backuprestore/*.json
        chmod 640 ${install_path}/manager/var/etc/backuprestore/backuprestore.cfg
        
        adapter_dv_br=${install_path}/manager/adapter/DigitalView/backuprestore
        if [ ! -d ${adapter_dv_br} ];then
            mkdir -p ${adapter_dv_br}
            chown -R ossadm:${oss_group_name} ${install_path}/manager/adapter
        fi
        
        cp -rpf ${base_path}/UniEp/exclude_list_static_os.properties ${adapter_dv_br}/
        [ $? -ne 0 ] && log_echo "ERROR" "exc cmd [cp -rpf ${base_path}/UniEp/exclude_list_static_os.properties ${adapter_dv_br}/] failed."
        
        chown -R ossadm:${oss_group_name} ${adapter_dv_br}/exclude_list_static_os.properties
        chmod 640  ${adapter_dv_br}/exclude_list_static_os.properties
        
        adapter_mgr_br=${install_path}/manager/adapter/management/backuprestore
        if [ ! -d ${adapter_mgr_br} ];then
            mkdir -p ${adapter_mgr_br}
            chown -R ossadm:${oss_group_name} ${install_path}/manager/adapter/management
        fi
        
        cp -rpf ${base_path}/UniEp/exclude_list_static_os.properties ${adapter_mgr_br}/
        [ $? -ne 0 ] && log_echo "ERROR" "exc cmd [cp -rpf ${base_path}/UniEp/exclude_list_static_os.properties ${adapter_mgr_br}/] failed."
        
        chown -R ossadm:${oss_group_name} ${adapter_mgr_br}/exclude_list_static_os.properties
        chmod 640 ${adapter_mgr_br}/exclude_list_static_os.properties
        
        if [ ! -d ${install_path}/manager/var/etc/common ];then
            mkdir -p ${install_path}/manager/var/etc/common
            chown -R ossadm:${oss_group_name} ${install_path}/manager/var/etc/common
        fi
        
        local files=(timeoutsetting.json SOP_watch_custom_config.json customer_config.json)
        for file in ${files[@]}
        do
            cp -rpf ${base_path}/UniEp/${file}  ${install_path}/manager/var/etc/common
            [ $? -ne 0 ] && log_echo "ERROR" "exc cmd [cp -rpf ${base_path}/UniEp/${file}  ${install_path}/manager/var/etc/common] failed."
            chown -R ossadm:${oss_group_name} ${install_path}/manager/var/etc/common/${file}
            chmod 640  ${install_path}/manager/var/etc/common/${file}
        done
        
        if [ "X${netWorkType}" == "XM" ];then
            log_echo "INFO" "scp custom file to other uniep"
            scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} -r ${install_path}/manager/var/etc/backuprestore  ${DVSecondary_IPV4}:${install_path}/manager/var/etc/
            if [ $? -ne 0 ];then
                log_echo "ERROR" "scp -o StrictHostKeyChecking=no -r ${install_path}/manager/var/etc/backuprestore  ${DVSecondary_IPV4}:${install_path}/manager/var/etc/ failed,please check"
                exit 1
            fi
            scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} -r ${install_path}/manager/adapter  ${DVSecondary_IPV4}:${install_path}/manager/
            if [ $? -ne 0 ];then
                log_echo "ERROR" "scp -o StrictHostKeyChecking=no -r ${install_path}/manager/adapter  ${DVSecondary_IPV4}:${install_path}/manager/ failed,please check"
                exit 1
            fi
            
            scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} -r ${install_path}/manager/var/etc/backuprestore  ${DVThird_IPV4}:${install_path}/manager/var/etc/
            if [ $? -ne 0 ];then
                log_echo "ERROR" "scp -o StrictHostKeyChecking=no -r ${install_path}/manager/var/etc/backuprestore  ${DVThird_IPV4}:${install_path}/manager/var/etc/ failed,please check"
                exit 1
            fi
            
            ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${DVThird_IPV4} chmod 750 -R ${install_path}/manager/adapter
            
            scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} -r ${install_path}/manager/adapter  ${DVThird_IPV4}:${install_path}/manager/
            if [ $? -ne 0 ];then
                log_echo "ERROR" "scp -o StrictHostKeyChecking=no -r ${install_path}/manager/adapter  ${DVThird_IPV4}:${install_path}/manager/ failed,please check"
                exit 1
            fi

            local nodes_ip=(${DVSecondary_IPV4} ${DVThird_IPV4})
            for file in ${files[@]}
            do
                for node_ip in ${nodes_ip[@]}
                do
                    scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} -r ${install_path}/manager/var/etc/common/${file}  ${node_ip}:${install_path}/manager/var/etc/common/
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "scp -o StrictHostKeyChecking=no -r ${install_path}/manager/var/etc/common/${file}  ${node_ip}:${install_path}/manager/var/etc/common/ failed,please check"
                        exit 1
                    fi
                done
            done
        fi
        
        
        log_echo "INFO" "config backuprestore finished."    
    fi
    
    if [ -f ${CA_PATH}/ca.cer ];then
        rm -rf ${CA_PATH}/ca.cer >/dev/null 2>&1 
    fi
    

    
    add_alias "uniepNode"
    
    chmod 775 ${base_path}/UniEp
    sudo -u dbuser bash -c "test -f /opt/zenith/app/add-ons/libuuid.so"
    if [ $? -eq 0 ];then
        sudo -u dbuser bash -c "mv -f /opt/zenith/app/add-ons/libuuid.so* ${base_path}/UniEp"
    fi
    chmod 755 ${base_path}/UniEp
    rm -rf ${base_path}/uniep_upgrade_error.tag
}

function custom_envcheck()
{
    local envcheck_num="$1"
    if [ -z "${envcheck_num}" ];then
        log_echo "INFO" "The envcheck_num=${envcheck_num} is null."
        return 0
    fi
    
    log_echo "INFO" "custom_envcheck_json of ${envcheck_num} ..."
    local custom_envcheck_json=$(ls ${install_path}/share/manager/SwiftDeploy/extend/upgrade/DigitalView/*-${current_version}/custom/custom.json)
    if [ ! -f "${custom_envcheck_json}" ];then
        log_echo "ERROR" "Get ${install_path}/share/manager/SwiftDeploy/extend/upgrade/DigitalView/*-${current_version}/custom/custom.json failed, please check"
        exit 1
    fi
    
    [ ! -f "${base_path}/tools/custom.json_bak" ] && cp -p ${custom_envcheck_json} ${base_path}/tools/custom.json_bak
    cp -p ${custom_envcheck_json} ${base_path}/tools/custom.json
    chmod 750 ${base_path}/tools/custom_sd.py
    chown ossadm:ossgroup ${base_path}/tools/custom_sd.py
    
    grep -w "${envcheck_num}" ${custom_envcheck_json} > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "add custom_envcheck_json ${envcheck_num}..."
        source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/custom_sd.py "${base_path}/tools/custom.json" "custom_envcheck" "add" "${envcheck_num}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "add custom_envcheck_json ${envcheck_num} failed"
            exit 1
        fi
    fi
    
    cp ${base_path}/tools/custom.json ${custom_envcheck_json}
    chown ossadm:ossgroup ${custom_envcheck_json}
    chmod 640 ${custom_envcheck_json}
}

function custom_upgrade_backup()
{
    local features=$(cat ${base_path}/tools/expInfo/product_SOP.json |sed "s/,\"/,\n\"/g" |grep "\"features\"")
    echo ${features} |grep "sysMonitor" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Get features failed, please check"
        exit 1
    fi
    
    if [ -f "/.dockerenv" ];then
        custom_envcheck "0006"
    fi
    
    local custom_backup_dvpm_json=$(ls ${install_path}/share/manager/SwiftDeploy/extend/upgrade/DigitalView/*-${current_version}/plandata/custom_backup.json)
    if [ ! -f "${custom_backup_dvpm_json}" ];then
        log_echo "ERROR" "Get custom_backup.json failed, please check"
        exit 1
    fi

    local custom_backup_binary_json=$(ls ${install_path}/share/manager/SwiftDeploy/extend/upgrade/DigitalView/*-${current_version}/plandata/custom_backup_binary.json)
    if [ ! -f "${custom_backup_binary_json}" ];then
        log_echo "ERROR" "Get custom_backup_binary.json failed, please check"
        exit 1
    fi

    # Default Logical Backup
    local custom_backup_json=${custom_backup_dvpm_json}

    #No find DVPM before upgrade, need change to binary backup
    echo ${features} |grep -E "DVPM" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "No find DVPM features, need change to binary backup."
        custom_backup_json=${custom_backup_binary_json}
    fi

    [ ! -f "${base_path}/tools/custom_backup.json_bak" ] && cp -p ${custom_backup_json} ${base_path}/tools/custom_backup.json_bak
    cp -p ${custom_backup_json} ${base_path}/tools/custom_backup.json
    chmod 750 ${base_path}/tools/custom_sd.py
    chown ossadm:ossgroup ${base_path}/tools/custom_sd.py
    echo ${features} |grep -E "SituationAwareness"  > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "find SituationAwareness in features..."
        [ -f ${base_path}/zenithinstances.txt ] && rm -f ${base_path}/zenithinstances.txt
        /opt/oss/manager/apps/DBAgent/bin/dbsvc_adm  -cmd query-db-instance -type zenith > ${base_path}/zenithinstances.txt
        detectiondbsvr_instance=$(cat ${base_path}/zenithinstances.txt|grep SecurityDetectiondbsvr|head -n 1 |awk '{print $1}')
        securitymonitor_instance=$(cat ${base_path}/zenithinstances.txt|grep SecurityMonitordbsvr|head -n 1 |awk '{print $1}')
        detectiondbsvr_dblist=$(/opt/oss/manager/apps/DBAgent/bin/dbsvc_adm -cmd query-database -instid -instid ${detectiondbsvr_instance}|grep -v DatabaseName|awk '{print $1}'|paste -sd ",")
        securitymonitor_dblist=$(/opt/oss/manager/apps/DBAgent/bin/dbsvc_adm -cmd query-database -instid -instid ${securitymonitor_instance}|grep -v DatabaseName|awk '{print $1}'|paste -sd ",")

        grep -w "SecurityDetectiondbsvr" ${custom_backup_json} > /dev/null 2>&1
        if [ $? -ne 0 ];then
            source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/custom_sd.py "${base_path}/tools/custom_backup.json" "custom_backup" "SecurityDetectiondbsvr" "${detectiondbsvr_dblist}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "custom SecurityDetectiondbsvr backup failed"
                exit 1
            fi
        fi
        grep -w "SecurityMonitordbsvr" ${custom_backup_json} > /dev/null 2>&1
        if [ $? -ne 0 ];then
            source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/custom_sd.py "${base_path}/tools/custom_backup.json" "custom_backup" "SecurityMonitordbsvr" "${securitymonitor_dblist}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "custom SecurityMonitordbsvr backup failed"
                exit 1
            fi
        fi
    fi

    #find DVPM before upgrade, not need change to binary backup but add dbName_list
    echo ${features} |grep -E "DVPM" > /dev/null 2>&1
    if [ $? -eq 0 ];then
       log_echo "INFO" "find DVPM features, start add dbName_list."
       if [ -f "${base_path}/dbName_list" ];then
           local dbName_list=$(grep "dbName=" ${base_path}/dbName_list|awk -F'=' '{print $2}')
           source ${install_path}/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/custom_sd.py "${base_path}/tools/custom_backup.json" "custom_ignore_backup" "${dbName_list}"
           if [ $? -ne 0 ];then
                log_echo "ERROR" "add dbName_list failed"
                exit 1
           fi
       fi
    fi
    
    cp ${base_path}/tools/custom_backup.json ${custom_backup_dvpm_json}
    chown ossadm:ossgroup ${custom_backup_dvpm_json}
    chmod 640 ${custom_backup_dvpm_json}
}

function remove_history_sd_backup_dir()
{
    local upgrade_file_path=${install_path}/share/manager/SwiftDeploy/extend/upgrade/DigitalView
    local file_list=$(ls -lt ${upgrade_file_path} | grep -v ${current_version} | awk -F ' ' '(NR>3){print $9}')

    if [ -z ${file_list} ];then
        log_echo "INFO" "not need to delete history sd_backup_dir"
        return 0;
    fi

    for dir in ${file_list}
    do
        if [ -d ${install_path}/share/manager/SwiftDeploy/extend/upgrade/DigitalView/${dir} ];then
            rm -rf "${install_path}/share/manager/SwiftDeploy/extend/upgrade/DigitalView/${dir:?}"
            log_echo "INFO" "auto clean ${upgrade_file_path}/${dir}"
        fi
    done

    log_echo "INFO" "delete history sd_backup_dir finish"
}

function get_db_list()
{
    if [ "$1" == "local" ];then
        local db_list=$(sudo -u dbuser bash -c "ls /opt/zenith/data/cloudsopdbsvr*/data/tablespace")
    else
        local db_ip="$1"
        local db_dir=$(ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ip} sudo -u dbuser ls /opt/zenith/data/|grep cloudsopdbsvr|sed 's/ //g')
        echo ${db_dir} | grep "cloudsopdbsvr" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Get db directory of cloudsopdbsvr failed at ${db_ip}, please check"
            exit 1
        fi

        local db_list=$(ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${db_ip} sudo -u dbuser ls /opt/zenith/data/${db_dir}/data/tablespace)
    fi
    
    echo ${db_list} | grep "ossuser" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Get db list of cloudsopdbsvr failed, please check"
        exit 1
    fi
    euler_version=$(grep "VERSION=" /etc/os-release|head -1 | awk -F'=' '{print $2}'|sed "s/\"//g")
    OS_ARCH_TMP=$(lscpu |grep "Architecture:"|awk -F ':' '{print $2}' |sed "s/ //g")
    if [ "X${euler_version}" == "X2.0 (SP9x86_64)" -o "X${euler_version}" == "X2.0 (SP10x86_64)" -o "X${euler_version}" == "X2.0 (SP10)" -o "X${euler_version}" == "X2.0 (SP11x86_64)" -o "X${euler_version}" == "X2.0 (SP11)" -o "X${euler_version}" == "X2.0 (SP12x86_64)" -o "X${euler_version}" == "X2.0 (SP12)" ];then
        if [ "X${OS_ARCH_TMP}" == "Xx86_64" ];then
            OS_TYPE="Euler_amd64"
        fi
    fi
    backuprestorecfg="/opt/oss/manager/apps/UniEPService/etc/backuprestore/backuprestorecfg.json"
    if [ -f "/opt/oss/manager/apps/UniEPLiteService/etc/backuprestore/backuprestorecfg.json" ];then
        backuprestorecfg="/opt/oss/manager/apps/UniEPLiteService/etc/backuprestore/backuprestorecfg.json"
    fi
    cat ${backuprestorecfg}|grep SUPPORT_DB_EXTEND_PARAM
    if [ $? -eq 0 ] && [ "X${OS_TYPE}" != "XEuler_amd64" ];then
        no_backup_list="pmdashboarddb cswebdb easymsdb dmsdb dvpmdatadb cmdbcorehistorydb"
    else
        no_backup_list="cmdbcorehistorydb pmdashboarddb cswebdb easymsdb cmdbcoresvrdb dmsdb dvpmdatadb"
    fi 
    for dbname in ${no_backup_list[*]}
    do
        db_list=$(echo $db_list|sed 's/ /\n/g'|sed "s/^$dbname\$//")
    done
    db_list_final=$(echo ${db_list}|tr ' ' ','|sed 's/,,/,/g')

    echo "dbName=${db_list_final}" > ${PreSet_PATH}/dbName_list
    grep "ossuser" ${PreSet_PATH}/dbName_list > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Write dbName_list failed, please check"
        exit 1
    fi
}

function check_docker_opt()
{
    log_echo "INFO" "check_docker_opt $(whoami) start..."
    if [ ! -f "/.dockerenv" ];then
        log_echo "INFO" "The /.dockerenv is not exits.not need check it."
        return 0
    fi
    local nodeip=""
    if [ "X${netWorkType}" == "XM"  ];then
        nodeip=${DVPrimary_IPV4}
    elif [ "X${netWorkType}" == "XO" ];then
        nodeip=${ALL_IN_ONE_IPV4}
    fi
    
    ## 10*1024*1024*1024 = 10G = 10737418240 byte
    local need_space=$((10*1024*1024*1024))
    local tmp_size=$(du -sb /opt/oss/manager/var/tmp |awk '{print $1}')
    need_space=$((${need_space} - ${tmp_size}))
    
    local check_path="/opt"
    local available_space=$(df -P --block-size=1 ${check_path} | sed -n '$p' | awk '{print $4}')
    
    if [ ${available_space} -le ${need_space} ];then
        log_echo "ERROR" "The ${check_path} disk available size(${available_space} bytes) is less than the size(${need_space} bytes). Otherwise, the pod disk of the container node is full."
        exit 1
    else
        log_echo "INFO" "The ${check_path} disk available size(${available_space} bytes) is enough for the size(${need_space} bytes)."
    fi
    
    local total_space=$(df --block-size=1 ${check_path} | sed -n '$p' |awk '{print $2}')
    local used_space=$(df --block-size=1 ${check_path} | sed -n '$p' |awk '{print $3}')
    local plan_used_space=$((${used_space} + ${need_space}))
    local plan_usage=$(echo "scale=2; ret = ${plan_used_space} / ${total_space}; if (length(ret)==scale(ret)) print 0;print ret" | bc)
    
    log_echo "INFO" "The ${check_path} disk total_space=(${total_space} bytes),used_space=(${used_space} bytes),plan_used_space=(${plan_used_space} bytes),plan_usage=${plan_usage}."
    if [ $(echo "${plan_usage} > 0.90" | bc) -eq 1 ];then
        log_echo "ERROR" "The ${check_path} disk total space size(${total_space} bytes), plan used space size(${plan_used_space} bytes).The ${check_path} disk plan usage of $(echo \"${plan_usage}\" |awk -F'.' '{print $2}')% exceeds the 90% usage. Otherwise the pod will terminate abnormally."
        local need_total_space=$(echo "scale=0; ret = ${plan_used_space} / 0.9; if (length(ret)==scale(ret)) print 0;print ret" | bc)
        ## 1G=1073741824 byte
        local need_all_space=$((${need_total_space} + 1073741824 - ${total_space} ))
        log_echo "ERROR" "The ${check_path} disk we need expand to ( ${need_all_space} bytes ) on ${nodeip}"
        exit 1
    fi
    
    log_echo "INFO" "check_docker_opt End."
}

function unzip_pkg()
{
    log_echo "INFO" "unzip_pkg start..."
    ## 解压获取SD升级包中的子包。
    log_echo "INFO" "unzip SwiftDeploy package."
    local pkg=$(ls ${base_path}/UniEp/SwiftDeploy*.zip)
    is_empty "pkg" "${pkg}"
    rm -rf ${base_path}/UniEp/SwiftDeploy
    unzip -o ${pkg} -d ${base_path}/UniEp/SwiftDeploy  >> $LOG_FILE 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "unzip -o ${pkg} -d ${base_path}/UniEp/SwiftDeploy failed."
        exit 1
    fi
    ## SD升级依赖 Uniep的包，检查如果不存在Uniep包，报错退出。
    log_echo "INFO" "Check UniEPMgr*.zip package."
    if [ -z "$(ls ${base_path}/UniEp/UniEPMgr*.zip 2>/dev/null)" ];then
        log_echo "ERROR" "The ${base_path}/UniEp/UniEPMgr*.zip is not exists."
        exit 1
    fi
    ## 解压 Uniep 的包，为下面升级SD做准备。
    log_echo "INFO" "unzip UniEPMgr*.zip package."
    pkg=$(ls ${base_path}/UniEp/UniEPMgr*.zip)
    is_empty "pkg" "${pkg}"
    unzip -o ${pkg} -d ${base_path}/UniEp/UniEPMgr  >> $LOG_FILE 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "unzip -o ${pkg} -d ${base_path}/UniEp/UniEPMgr failed."
        exit 1
    fi
    ## 校验SD的包和签名。
    log_echo "INFO" "Check whether the signature file of the SwiftDeploy package exists."
    pkg=$(ls ${base_path}/UniEp/SwiftDeploy/Services/Base/SwiftDeploy/SwiftDeploy*.zip)
    is_empty "pkg" "${pkg}"
    file_exists "${pkg}.cms"
    log_echo "INFO" "unzip_pkg End."
}

function upgrade_sd_before_operation()
{
    log_echo "INFO" "upgrade_sd_before_operation start."
    ## 把新版本SD包拷贝到/home/<USER>/swiftdeployPkg下，给SD升级前置检查时，存在该包，表示在升级完OS后，需要升级SD包。
    swiftdeploy_pkg_path="/home/<USER>/swiftdeployPkg"
    rm -rf ${swiftdeploy_pkg_path}
    mkdir -p ${swiftdeploy_pkg_path}
    chmod 750 ${swiftdeploy_pkg_path}
    local pkg=$(ls ${base_path}/UniEp/SwiftDeploy/Services/Base/SwiftDeploy/SwiftDeploy*.zip)
    is_empty "pkg" "${pkg}"
    log_echo "INFO" "copy ${pkg}* to ${swiftdeploy_pkg_path}"
    cp -rpf ${pkg}* ${swiftdeploy_pkg_path}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "cp -rpf ${pkg}* ${swiftdeploy_pkg_path} failed."
        exit 1
    fi
    ## 获取SD的包完整路径，并对/home/<USER>/swiftdeployPkg下的包和签名文件修改为640权限。
    swiftdeploy_pkg=$(ls ${swiftdeploy_pkg_path}/SwiftDeploy*.zip)
    log_echo "INFO" "chmod 640 ${swiftdeploy_pkg}*"
    chmod 640 ${swiftdeploy_pkg}*
    if [ $? -ne 0 ];then
        log_echo "ERROR" "chmod 640 ${swiftdeploy_pkg}* failed."
        exit 1
    fi
    
    ## 创建手工升级SD临时目录。
    log_echo "INFO" "create upgrade_swiftdeploy path"
    upgrade_swiftdeploy_path="/home/<USER>/upgrade_swiftdeploy"
    mkdir -p ${upgrade_swiftdeploy_path}
    chmod 750 ${upgrade_swiftdeploy_path}
    ## 获取升级预置目录下，SD的包和Uniep的包解压后的包路径。并写入到配置文件，给后续手工升级脚本获取。拷贝手工升级脚本到临时目录下。
    SwiftDeployPkgPath="${base_path}/UniEp/SwiftDeploy/Services/Base/SwiftDeploy"
    UniEPMgrPkgPath="${base_path}/UniEp/UniEPMgr"
    echo "SwiftDeployPkgPath=${SwiftDeployPkgPath}" > ${upgrade_swiftdeploy_path}/swiftdeployPkg.txt
    echo "UniEPMgrPkgPath=${UniEPMgrPkgPath}" >> ${upgrade_swiftdeploy_path}/swiftdeployPkg.txt
    chmod 600 ${upgrade_swiftdeploy_path}/swiftdeployPkg.txt
    cp -rpf ${base_path}/tools/upgrade_swift_deploy.sh ${upgrade_swiftdeploy_path}
    chmod 700 ${upgrade_swiftdeploy_path}/upgrade_swift_deploy.sh
    log_echo "INFO" "upgrade_sd_before_operation End."
}

function upgrade_SwiftDeploy()
{
    log_echo "INFO" "upgrade_SwiftDeploy start..."
    ## 如果不存在SD单独升级包，表示非X86场景，无需升级
    if [ -z "$(ls ${base_path}/UniEp/SwiftDeploy*.zip 2>/dev/null)" ];then
        log_echo "INFO" "The ${base_path}/UniEp/SwiftDeploy*.zip is not exists.not need upgrade it."
        return 0
    fi
    ## 解压SD包和Uniep包。 
    unzip_pkg
    ## 升级SD前准备操作
    upgrade_sd_before_operation
    ## 检查操作系统 版本 
    log_echo "INFO" "check os version."
    if [ -f /etc/os-release ];then
        local euler_version=$(grep "VERSION=" /etc/os-release|head -1 | awk -F'=' '{print $2}'|sed "s/\"//g")
        ## 如果是欧拉2.5或者2.8，这无法在升级预置升级SD。需要等升级OS后，手工升级。这里就可以退出了。
        if [ "X${euler_version}" == "X2.0 (SP5)" -o "X${euler_version}" == "X2.0 (SP8)" ];then
            log_echo "INFO" "The euler_version=${euler_version},need to upgrade the operating system based on the upgrade guide and then upgrade SwiftDeploy."
            return 0 
        fi
        ## 如果不是，表示可以直接升级SD。打印日志，继续往下执行。
        log_echo "INFO" "check os version.The euler_version=${euler_version}"
    else
        ## 系统判断操作系统文件不存在时，无法判断，需要报错退出。
        log_echo "ERROR" "The /etc/os-release is not exists"
        exit 1
    fi
    ##升级SD前，先停服务。
    log_echo "INFO" "SwiftDeploy stop it."
    sh /opt/oss/swiftdeploy/SwiftDeploy/bin/start.sh stop >> $LOG_FILE 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "/opt/oss/swiftdeploy/SwiftDeploy/bin/start.sh stop failed."
        exit 1
    fi
    ## 升级SD 
    log_echo "INFO" "SwiftDeploy Upgrade it."
    source /opt/oss/manager/bin/engr_profile.sh
    /opt/oss/manager/agent/bin/ipmc_tool -cmd sd -o install -pkgpath ${swiftdeploy_pkg} -installdiskpath ${UniEPMgrPkgPath} >> $LOG_FILE 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute cmd:[ /opt/oss/manager/agent/bin/ipmc_tool -cmd sd -o install -pkgpath ${swiftdeploy_pkg} -installdiskpath ${UniEPMgrPkgPath} ] failed."
        exit 1
    fi
    ## 升级后启动。 
    log_echo "INFO" "SwiftDeploy start it."
    sh /opt/oss/swiftdeploy/SwiftDeploy/bin/start.sh start >> $LOG_FILE 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "/opt/oss/swiftdeploy/SwiftDeploy/bin/start.sh start failed."
        exit 1
    fi
    ## SD升级完成后，不需要手工单独升级了，删除/home/<USER>
    rm -rf /home/<USER>/swiftdeployPkg /home/<USER>/upgrade_swiftdeploy /home/<USER>/backup_sd_old_version_pkg  >> $LOG_FILE 2>&1
    log_echo "INFO" "upgrade_SwiftDeploy End."
}


function scan_Pkg_For_SD()
{
    [ "X${is_SD_upgrade}" != "XYes" -o -f "${base_path}/scan_SD_finish" ] && return 0
    
    log_echo "INFO" "Begin to scan packages for SD upgrade..."
    
    cp -f ${base_path}/UniEp/UniEPMgr*.zip ${install_path}/manager/var/tmp
    [ $? -ne 0 ] && log_echo "ERROR" "Copy UniEPMgr*.zip to manager/var/tmp failed, please check" && exit 1
    cp ${base_path}/UniEp/UniEPMgr*.zip.cms ${install_path}/manager/var/tmp
    [ $? -ne 0 ] && log_echo "ERROR" "Copy UniEPMgr*.zip.cms to manager/var/tmp failed, please check" && exit 1
    
    sd_pkg_name="SwiftDeployExtend"
    cp ${base_path}/tools/SD/${sd_pkg_name}*.zip ${install_path}/manager/var/tmp
    [ $? -ne 0 ] && log_echo "ERROR" "Copy SwiftDeployExtend*.zip to manager/var/tmp failed, please check" && exit 1
    cp ${base_path}/tools/SD/${sd_pkg_name}*.zip.cms ${install_path}/manager/var/tmp
    [ $? -ne 0 ] && log_echo "ERROR" "Copy SwiftDeployExtend*.zip.cms to manager/var/tmp failed, please check" && exit 1
    
    chown ossadm:ossgroup -R /opt/oss/manager/var/tmp
    [ $? -ne 0 ] && log_echo "ERROR" "Execute cmd [chown ossadm:ossgroup -R /opt/oss/manager/var/tmp] failed, please check" && exit 1
    chmod 750 /opt/oss/manager/var/tmp/*
    
    uniep_Package_name=$(cd ${base_path}/UniEp;ls UniEPMgr*.zip)
    if [ -z "${uniep_Package_name}" ];then
        log_echo "ERROR" "Get UniEPMgr packages's name failed, please check"
        exit 1
    fi

    ##连续回退两次会中间版本回退管理面数据库导致软件包会被删除，且重新扫描会有标记文件会扫描不上
    local uniep_directory=$(echo "${uniep_Package_name}" | awk -F'.zip' '{print $1}')
    local uniep_signfile=/opt/pub/software/mgr-installdisk/${uniep_directory}/sign_complete
    if [ -f "${uniep_signfile}" ];then
        log_echo "INFO" "The ${uniep_signfile} file exists."
    fi

    check_docker_opt
    
    if [ -d "/opt/oss/swiftdeploy" ];then
        find /opt/oss/swiftdeploy -type d -exec chmod u+w {} \;
    fi
    
    log_echo "INFO" "netWorkType=${netWorkType}"
    local local_is_master="Yes"
    if [ "X${netWorkType}" == "XM" -a ! -f /opt/oss/manager/apps/HyperHAAgent/bin/hactl -a -f "/opt/oss/manager/var/share/standby_flag_file" ];then
        local_is_master="No"
        log_echo "INFO" "network type is M, and is OMHA, and local_is_master=${local_is_master}"
    fi
    
    local scan_pkg_ret=0
    log_echo "INFO" "The local_is_master=${local_is_master}"
    if [ "X${local_is_master}" == "XNo" ];then
        local secondary_node_ssh_ip="${DVSecondary_IPV4}"
        local secondary_node_scp_ip="${DVSecondary_IPV4}"
        if [ "X${DVSecondary_MGR_IPV6}" != "X" ];then
            secondary_node_ssh_ip="${DVSecondary_MGR_IPV6}"
            secondary_node_scp_ip="[${DVSecondary_MGR_IPV6}]"
        fi
        
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${secondary_node_ssh_ip}  "rm -f ${uniep_signfile}"
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${secondary_node_ssh_ip} "cd ${install_path}/manager/tools/deployapp;bash scanpackage.sh -path ${install_path}/manager/var/tmp -output ${install_path}/manager/tools/deployapp/scanResult.json" > ${base_path}/scan_for_SD.log 2>&1
        scan_pkg_ret=$?
        
        scp -r ossadm@${secondary_node_scp_ip}:${install_path}/manager/tools/deployapp/scanResult.json  ${install_path}/manager/tools/deployapp/scanResult.json
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Execute cmd:[ scp -r ossadm@${secondary_node_scp_ip}:${install_path}/manager/tools/deployapp/scanResult.json  ${install_path}/manager/tools/deployapp/scanResult.json ] failed."
            exit 1
        fi
    else
        rm -f ${uniep_signfile}
        cd ${install_path}/manager/tools/deployapp;bash scanpackage.sh -path ${install_path}/manager/var/tmp -output ${install_path}/manager/tools/deployapp/scanResult.json  > ${base_path}/scan_for_SD.log 2>&1
        scan_pkg_ret=$?
    fi
    
    if [ ${scan_pkg_ret} -ne 0 ];then
        cp -f ${install_path}/manager/tools/deployapp/scanResult.json ${base_path}
        
        sddata_scan_result=$(grep -A2 'SwiftDeployExtend' ${base_path}/scanResult.json | grep resultcode |awk -F':' '{print $NF}'|sed 's/ //g')
        source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/handlejson.py handle_scanret   ${base_path}/scanResult.json
        total_scan_ret=$?

        grep -w "^${uniep_Package_name}" ${base_path}/scan_for_SD.log >> ${LOG_FILE} 2>&1
        if [ $? -eq 0 -a ${sddata_scan_result} -eq 0 -a ${total_scan_ret} -eq 0 ];then
            log_echo "INFO" "${uniep_Package_name} has been scaned successfully, skip this."
        else
            log_echo "ERROR" "Scan packages for SD failed, please check log ${install_path}/manager/tools/deployapp/scanResult.json"
            log_echo "ERROR" "If the error information cannot be determined based on the logs, you can log in to the UniEP management page to view the task failure details. "
            exit 1
        fi
    fi
    
    rm -f ${install_path}/manager/tools/deployapp/scanResult.json
    if [ "X${local_is_master}" == "XNo" ];then
       ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${secondary_node_ssh_ip} "rm -f ${install_path}/manager/tools/deployapp/scanResult.json"
    fi
    
    sd_zip=$(ls ${base_path}/tools/SD/SwiftDeployExtend*.zip)
    current_version=$(basename ${sd_zip} .zip|awk -F'-' '{print $2}')
    echo $current_version |grep "^V8" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Get sd_zip version failed, please check"
        exit 1
    fi
    log_echo "INFO" "current sd_zip version is $current_version"
    upgrade_SwiftDeploy
    custom_upgrade_backup
    custom_pm_features
    custom_upgrade_param
    custom_upgrade_guide_workflow
    custom_rollback_task
    touch ${base_path}/scan_SD_finish
    
    remove_history_sd_backup_dir

    log_echo "INFO" "Scan packages for SD upgrade successfully."
}

function unzip_customFile()
{
    if [ "X${is_SD_upgrade}" != "XYes" ];then
        unzip -o ${base_path}/UniEp/UniEPMgr*.zip DV_custom/* -d ${base_path}/UniEp
        cp -rf ${base_path}/UniEp/DV_custom/* ${base_path}/UniEp
        unzip -o ${base_path}/UniEp/UniEPMgr*.zip osconfig*.zip -d ${base_path}/CloudSop/
        unzip -o ${base_path}/UniEp/UniEPMgr*.zip sudoconfig*.tar.gz -d ${base_path}/CloudSop/
        cp -rf ${base_path}/UniEp/CA ${base_path}/
        chmod 755 -R ${base_path}
    fi
}

function check_is_disaster()
{
    if [ ! -f ${install_path}/manager/tools/drmgr/getsitestatus.sh -o -f "${base_path}/check_disaster_ok" ];then
       return 0
    fi
    
    log_echo "INFO" "Begin to check it is disaster or not..."
    check_json="${base_path}/check_disaster.json"
    rm -f ${check_json}
    if [ "X$(whoami)" == "Xroot" ];then
        su - ossadm -c "source ${install_path}/manager/agent/MaintenanceService/lbin/engr_profile.sh;python -c \"import httpclient; httpclient = httpclient.IRHttpClient(); tmp=httpclient.get(\\\"/rest/plat/drmgrservice/v1/main/drinfo\\\"); print(tmp)\"" >> ${check_json}
    else
        source ${install_path}/manager/agent/MaintenanceService/lbin/engr_profile.sh;${PYTHONHOME}/bin/python -c "import httpclient; httpclient = httpclient.IRHttpClient(); tmp=httpclient.get(\"/rest/plat/drmgrservice/v1/main/drinfo\"); print(tmp)" >> ${check_json}
    fi
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Check disaster status failed, please check"
        exit 1
    fi
    
    cat ${check_json} |grep "200" >/dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Check drmgr rest's rerurn_code is not 200, please check uniep status."
        exit 1
    fi
    
    cat ${check_json} |grep "primary" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "ERROR" "You should delete the disaster relation before upgrade!"
        exit 1
    fi
    
    touch ${base_path}/check_disaster_ok
}

function check_hyperha()
{
    log_echo "INFO" "Begin to check HyperHA status"

    local app_float_ip=$(cat ${product_json} | sed 's/,/\n/g' |grep ":31943" |awk -F'"' '{print $4}'| awk -F':31943' '{print $1}' | sed 's/\[\|\]//g' | sed 's/ //g'| grep -v https)
    
    local is_hyperha=$(ssh -q -o StrictHostKeyChecking=no -p "${i2k_ssh_port}" "ossadm@${app_float_ip}" "[ -f /opt/oss/manager/apps/HyperHAAgent/bin/hactl ] && echo Yes || echo No")
    if [ "${is_hyperha}" == "Yes" ];then
        ssh -q -o StrictHostKeyChecking=no -p "${i2k_ssh_port}" "ossadm@${app_float_ip}" "sh /opt/oss/manager/apps/HyperHAAgent/bin/hactl status resourcegroup" > ${PreSet_PATH}/hyperha.txt
        grep -q "online *normal" ${PreSet_PATH}/hyperha.txt
        local check_online_normal=$?
        grep -q "offline *normal" ${PreSet_PATH}/hyperha.txt
        local check_offline_normal=$?
        if [ ${check_online_normal} -ne 0 ] || [ ${check_offline_normal} -ne 0 ];then
            log_echo "ERROR" "Check HyperHA status abnormally on the node ${app_float_ip} , the check result: $(cat ${PreSet_PATH}/hyperha.txt)"
            exit 1
        fi
        rm -f ${PreSet_PATH}/hyperha.txt
    else
        local ommha_status="/opt/oss/SOP/apps/OMMHAService/bin/status.sh"
        local is_manager_ommha=$(ssh -q -o StrictHostKeyChecking=no -p "${i2k_ssh_port}" "ossadm@${app_float_ip}" "[ -f /opt/oss/manager/apps/OMMHAService/bin/status.sh ] && echo Yes || echo No")
        [ "${is_manager_ommha}" == "Yes" ] && ommha_status="/opt/oss/manager/apps/OMMHAService/bin/status.sh"
        
        ssh -q -o StrictHostKeyChecking=no -p "${i2k_ssh_port}" "ossadm@${app_float_ip}" "sh ${ommha_status}" > ${PreSet_PATH}/ommha_status.txt
        grep -q "active *normal" ${PreSet_PATH}/ommha_status.txt
        local check_active_normal=$?
        grep -q "standby *normal" ${PreSet_PATH}/ommha_status.txt
        local check_standby_normal=$?
        if [ ${check_active_normal} -ne 0 ] || [ ${check_standby_normal} -ne 0 ];then
            log_echo "ERROR" "Check OMMHA status abnormally on the node ${app_float_ip} , the check result: $(cat ${PreSet_PATH}/ommha_status.txt)"
            exit 1
        fi
        rm -f ${PreSet_PATH}/ommha_status.txt
    fi
    
    log_echo "INFO" "Check HyperHA status normal"
}

function check_deploywebsite()
{
    log_echo "INFO" "Begin to check deploywebsite."
    ps -ef |grep -v grep |grep -w "^ossadm" |grep -w "DeployWebsite\|Product-DeployWebsite"
    if [ $? -eq 0 ];then
        log_echo "ERROR" "check the uniep node has DeployWebsite.please check it and Uninstall it." 
        exit 1
    fi
    
    if [ -d ${install_path}/envs/Product-DeployWebsite ];then
        log_echo "ERROR" "The path: ${install_path}/envs/Product-DeployWebsite is exist.please check and Uninstall it." 
        exit 1
    fi
    
    log_echo "INFO" "End to check deploywebsite"
}

######## Rollback #############

function Rollback_APP_Node()
{
    tmp_dir=/tmp/DVPreSet_upgrade_ossadm
    
    local app_ssh_ip=$1
    Check_execute "APP_PreSet_${app_ssh_ip}"
    if [ $? -eq 1 ];then
        return 0
    fi    
    
    if [ $# -eq 0 ];then
        log_echo "INFO" "rollback AllInOne node." 
        
        sh ${tmp_dir}/DV_PreSet_Cleanup.sh rollbackfile
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Rollback i2k preset in APP node failed, please check ${tmp_dir}/preset_script.log at node ${ALL_IN_ONE_IPV4}. "
            touch ${base_path}/rollback_fail.tag
            exit 1
        fi
        
        sed -i "/APP_PreSet_${app_ssh_ip}/d" ${action_tag}
        
        return 0  
    fi
    
    log_echo "INFO" "Begin to rollback APP node."
    
    local app_scp_ip=$1
    local app_ssh_pwd=$2
    local app_root_pwd=$3
    local node_role=$4
    if [ "X${IPType}" == "X1" ];then
        log_echo "INFO" "IPType is 1. is ipv6. of APP_PreSet"
        app_scp_ip="\[${app_scp_ip}\]"
    fi
    
    local isIpv6=$(echo ${app_scp_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        app_scp_ip="\[${app_scp_ip}\]"
    fi

    if [ "X${netWorkType}" == "XM" -a "X${node_role}" == "XPrimary" ];then
        log_echo "INFO" "Begin to execute DV_PreSet_Cleanup.sh at ${app_ssh_ip}..."
        sh ${tmp_dir}/DV_PreSet_Cleanup.sh rollbackfile
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Rollback i2k preset in APP node failed, please check ${tmp_dir}/preset_script.log at node ${app_ssh_ip}."
            touch ${base_path}/rollback_fail.tag
            exit 1
        fi
        sed -i "/APP_PreSet_${app_ssh_ip}/d" ${action_tag}
        return 0
    fi

    remote_ssh_passwd="${app_ssh_pwd}"
    remote_root_passwd="${app_root_pwd}"

    log_echo "INFO" "Begin to execute DV_PreSet_Cleanup.sh at ${app_ssh_ip}..."

    ssh -q -o StrictHostKeyChecking=no -p "${i2k_ssh_port}" "ossadm@${app_ssh_ip}" "sh ${tmp_dir}/DV_PreSet_Cleanup.sh rollbackfile"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Rollback i2k preset in APP node failed, please check ${tmp_dir}/preset_script.log at node ${app_ssh_ip}."
        touch ${base_path}/rollback_fail.tag
        exit 1
    fi
    sed -i "/APP_PreSet_${app_ssh_ip}/d" ${action_tag}
}

function Rollback_Dual_APP()
{
    sed -i "/Start_drbd/d" ${action_tag}
    if [ "X${IPType}" != "X0" ];then
        Rollback_APP_Node "${APP_Primary_MGR_IPV6}" "${APP_Primary_ssh_pwd}" "${APP_Primary_root_pwd}" "Primary"
        Rollback_APP_Node "${APP_Secondary_MGR_IPV6}" "${APP_Secondary_pwd}""${APP_Secondary_ssh_pwd}" "${APP_Secondary_root_pwd}" "Secondary"
    else
        Rollback_APP_Node "${APP_Primary_IPV4}" "${APP_Primary_ssh_pwd}" "${APP_Primary_root_pwd}"
        Rollback_APP_Node "${APP_Secondary_IPV4}" "${APP_Secondary_ssh_pwd}" "${APP_Secondary_root_pwd}"
    fi
}

function UniEp_Rollback()
{  
    log_echo "INFO" "begin to rollback uniep"
    Check_execute "UniEp_Upgrade"
    ret=$?
    if [ ${ret} -eq 1 ];then
        return 0
    fi
    
    
    if [ "X${is_SD_upgrade}" != "XYes" ];then
        etc_available=$(df -PBM /etc |grep "/"|awk -F' ' '{print $4}'|sed "s/M//")
        df -Ph /etc |grep "100%" > /dev/null 2>&1
        if [ $? -eq 0 -o ${etc_available} -lt 100 ];then
            log_echo "ERROR" "The available size of /etc is less than 100M, please check..."
            exit 1
        fi
        
        if [ ! -f ${base_path}/UniEp/UniEPMgr/rollback.sh ];then
            log_echo "ERROR" "uniep rollback.sh not exit, please check!"
            exit 1
        fi
    
        bash ${base_path}/UniEp/UniEPMgr/rollback.sh
        if [ $? -ne 0 ];then
            log_echo "ERROR" "uniep rollback failed..."
            touch ${base_path}/rollback_fail.tag
            exit 1
        fi
        bash ${base_path}/UniEp/UniEPMgr/rollback.sh -path ${install_path} -osconfig True
        if [ $? -ne 0 ];then
            log_echo "ERROR" "uniep rollback osconfig failed..."
            touch ${base_path}/rollback_fail.tag
            exit 1
        fi

        bash ${base_path}/UniEp/UniEPMgr/rollback.sh -path ${install_path} -sudoconfig True
        if [ $? -ne 0 ];then
            log_echo "ERROR" "uniep rollback sudoconfig failed..."
            touch ${base_path}/rollback_fail.tag
            exit 1
        fi
    else
        rm -rf /home/<USER>/uniep_baseversion 
    fi

    del_outclient_pfx_without_pwd
    sed -i "/UniEp_Upgrade/d" ${action_tag}
}

function Rollback_dual_app_input_user_pwd()
{
    if [ "X${IPType}" != "X0" ];then
        Check_execute "APP_PreSet_${APP_Primary_MGR_IPV6}"
        if [ $? -ne 1 ];then
            input_pwd_upgrade "${APP_Primary_MGR_IPV6}"
            APP_Primary_ssh_pwd="${sshpwdReturn}"
            APP_Primary_root_pwd="${rootpwdReturn}"
        fi
        Check_execute "APP_PreSet_${APP_Secondary_MGR_IPV6}"
        if [ $? -ne 1 ];then
            input_pwd_upgrade "${APP_Secondary_MGR_IPV6}"
            APP_Secondary_ssh_pwd="${sshpwdReturn}"
            APP_Secondary_root_pwd="${rootpwdReturn}"
        fi
    else
        Check_execute "APP_PreSet_${APP_Primary_IPV4}"
        if [ $? -ne 1 ];then
            input_pwd_upgrade "${APP_Primary_IPV4}"
            APP_Primary_ssh_pwd="${sshpwdReturn}"
            APP_Primary_root_pwd="${rootpwdReturn}"
        fi
        Check_execute "APP_PreSet_${APP_Secondary_IPV4}"
        if [ $? -ne 1 ];then
            input_pwd_upgrade "${APP_Secondary_IPV4}"
            APP_Secondary_ssh_pwd="${sshpwdReturn}"
            APP_Secondary_root_pwd="${rootpwdReturn}"
        fi
    fi
}

function Rollback_input_user_pwd()
{ 
    input_user_pwd_before "rollback APP"
    if [ "X${upgrade_ssh_user}" != "X${oper_user}" ];then
        get_node_ssh_pwd
        node_ssh_pwd="${nodepwdReturn}"
    fi
}

function rollback_nodes_common()
{
    tmp_dir=/tmp/DVPreSet_upgrade_ossadm
    app_ssh_ip=$1
    Check_execute "Node_PreSet_${app_ssh_ip}" 
    ret1=$?
    Check_execute "DB_PreSet_${app_ssh_ip}"
    ret2=$?

    if [ $ret1 -eq 1  -a  $ret2 -eq 1   ];then
        return 0
    fi 

    log_echo "INFO" "We need the root permission to rollback_nodes_common." 
    local node_ssh_ip=$1
    local node_scp_ip=$1
    local node_ssh_pwd=$2
    local node_root_pwd=$3
    
    if [ "X${IPType}" == "X1" ];then
        log_echo "INFO" "IPType is 1. is ipv6. of rollback_nodes_common"
        node_scp_ip="\[${node_scp_ip}\]"
    fi
    
    local isIpv6=$(echo ${node_scp_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        node_scp_ip="\[${node_scp_ip}\]"
    fi

    remote_ssh_passwd="${node_ssh_pwd}"
    remote_root_passwd="${node_root_pwd}"
    ssh -q -o StrictHostKeyChecking=no -p "${i2k_ssh_port}" "ossadm@${node_ssh_ip}" "rm -rf ${tmp_dir}/common_rollback.sh"
    auto_scp "${remote_ssh_passwd}" ${base_path}/Common/common_rollback.sh "${upgrade_ssh_user}@${node_scp_ip}:${tmp_dir}"

    log_echo "INFO" "Begin to execute common_rollback.sh at ${node_ssh_ip}..."
    ssh -q -o StrictHostKeyChecking=no -p "${i2k_ssh_port}" "ossadm@${node_ssh_ip}" "sh ${tmp_dir}/common_rollback.sh"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Rollback at node ${node_ssh_ip} failed, please check ${tmp_dir}/preset_script.log at node ${node_ssh_ip}."
        touch ${base_path}/rollback_fail.tag
        exit 1
    fi

    sed -i "/_PreSet_${node_ssh_ip}/d" ${action_tag}
}

function input_password_for_node_rollback()
{
    ipList=$1
    e=0
    Node_ssh_pwd_List=()
    Node_root_pwd_List=()
    for i in $(echo "${ipList}" | sed "s#,# #g")
    do
        Check_execute "Node_PreSet_${i}"
        if [ $? -eq 1 ];then
            log_echo "INFO" "Node_PreSet_${i} no need to rollback"
            Node_ssh_pwd_List[$e]="noneed"
            Node_root_pwd_List[$e]="noneed"
        else
            input_pwd_upgrade "${i}"
            Node_ssh_pwd_List[$e]="${sshpwdReturn}"
            Node_root_pwd_List[$e]="${rootpwdReturn}"
        fi
        ((e++))
    done
}

function check_need_radompwd()
{
    base_version=$(cat ${base_path}/tools/expInfo/product_SOP.json | sed 's/,/\n/g' |grep "product_version" |awk -F'"' '{print $4}' | sed 's/ //g' |tr '[a-z]' '[A-Z]')
    
    echo ${base_version} |grep "V8" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Get base version failed."
        exit 1
    fi
    
    if [[ "${base_version}" > "V800R001C30" ]];then
        return 0
    fi
    
    sed -i "/need_radompwd=/d" ${dv_cfg_file}.tmp
    echo "need_radompwd=Yes" >> ${dv_cfg_file}.tmp
}

function clean_knownhosts_ossadm()
{
    local ip_list=$(cat ${install_path}/manager/etc/sysconf/nodelists.json | grep "\"IP\"" | tr -d "\"|,| " | awk -F 'IP:' '{print $2}' | sort -nr | uniq | sed ':a;N;$!ba;s#\n# #g')
    
    for ip in ${ip_list}
    do
        sed -i "/.*${ip}.*/d" /home/<USER>/.ssh/known_hosts
    done
}

function clean_preset_pkg()
{
    if [ -f /.dockerenv -a -d /opt/DVZIPConfig/scan_softpackage ];then
        rm -rf /opt/DVZIPConfig/scan_softpackage
        log_echo "INFO" "clean /opt/DVZIPConfig/scan_softpackage."
    fi
    
    if [ -f /.dockerenv -a -f /opt/DVZIPConfig/UniEp/UniEPMgrV*R*C*.zip ];then
        rm -rf /opt/DVZIPConfig/UniEp/UniEPMgrV*R*C*.zip
        rm -rf /opt/DVZIPConfig/UniEp/UniEPMgrV*R*C*.zip.cms
        log_echo "INFO" "clean /opt/DVZIPConfig/UniEp/UniEPMgrV*R*C*.zip UniEPMgrV*R*C*.zip.cms."
    fi
    
    if [ -f /.dockerenv -a -d /opt/DVZIPConfig/UniEp/UniEPMgr ];then
        rm -rf /opt/DVZIPConfig/UniEp/UniEPMgr/*.zip
        log_echo "INFO" "clean /opt/DVZIPConfig/UniEp/UniEPMgr/*.zip."
    fi
}

function add_drbd_sudo_cfg()
{
    local float_IP=$1
    if [ "X${float_IP}" == "X" ];then
        echo "can not get the float ip"
        exit 1
    fi
    timeout -s KILL 60  ssh -q -o NumberOfPasswordPrompts=0 -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${float_IP}  date > /dev/null  2>&1
    if [ $? -ne 0 ];then
        echo "can not use ossadm ssh to the float ip ${float_IP}"
        exit 1
    fi
    drbd_info=$(ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${float_IP} df -h /dev/drbd0|grep drbd)
    if [ $? -eq 0 ];then
        sed -i "/sudo_drbd_disable_cmd=/d" ${dv_cfg_file}.tmp
        echo "sudo_drbd_disable_cmd=\"/home/<USER>/sudoScripts/drbd_disable.sh\"" >> ${dv_cfg_file}.tmp
        sed -i "/sudo_drbd_enable_cmd=/d" ${dv_cfg_file}.tmp   
        echo  "sudo_drbd_enable_cmd=\"/home/<USER>/sudoScripts/drbd_enable.sh\"" >> ${dv_cfg_file}.tmp
    fi
}
function custom_pm_features()
{   
    add_features=""
    features=$(get_current_value "features")
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Get features failed"
        exit 1
    fi

    featureGroup=$(get_current_value "featureGroup")
    echo ${featureGroup}|grep tool
    if [ $? -ne 0 ];then
        echo $features|grep -w DVPM
        if [ $? -ne 0 ];then
            add_features="\"DVPM\""   
        fi
    fi

    echo $features|grep -w DVCertService
    if [ $? -ne 0 ];then
        add_features="\"DVCertService\",${add_features}"   
    fi
    echo $features|grep -w SituationAwareness
    if [ $? -eq 0 ];then
        echo $features|grep -w BasicSecurityOM
        if [ $? -ne 0 ];then
            add_features="\"BasicSecurityOM\",${add_features}"   
        fi
    fi
    if [ "X${netWorkType}" == "XM" ];then
        . ${base_path}/zenith_paramgroup_file.properties
        if [ "${zenith_paramgroup_file}" == "largeCapacity" ] && [ "X${PHYSICAL_PM_DB_DISK1}" != "X" ];then
            echo $features|grep -w DVPM1
            if [ $? -ne 0 ];then
                add_features="\"DVPM1\",${add_features}"
            fi
            echo $features|grep -w DVPM2
            if [ $? -ne 0 ];then
                add_features="\"DVPM2\",${add_features}"
            fi
        fi
    fi

    # 设置标志位，判断是不是小型化或轻量化的一体机或者小型化的合设集群，如果是则不加，如果不是则要加
    add_flag=1
    # 先判断原来有没有安装DVITManagement
    echo $features|grep -w DVITManagement
    if [ $? -ne 0 ];then
        # 原来没有安装，则判断是不是一体机或者合设集群
        if [ "X${netWorkType}" == "XO" -o "X${netWorkType}" == "XM" ]; then
            deploy_mode=$(get_current_value "dv_deploy_scale_size")
            # 判断安装的类型是不是标准的类型，如果不是标准类型，则不添加，如果是则添加
            if [ "X${deploy_mode}" != "default" ]; then
                add_flag=0
            else
                add_flag=1
            fi
        else
            # 不是一体机或者合设集群的情况下，不存在轻量化或者小型化，都需要增加
            add_flag=1
        fi
    else
        add_flag=0
    fi

    if [ ${add_flag} -eq 1 ]; then
        if [ "X${add_features}" == "X" ]; then
            add_features="\"DVITManagement\""
        else
            add_features="\"DVITManagement\",${add_features}"
        fi
    fi

    if [ "X${dv_lite_mode}" == "XTRUE" ];then
        add_features=""
    fi

    local custom_upgrade_yaml=$(ls ${install_path}/share/manager/SwiftDeploy/extend/upgrade/DigitalView/*-${current_version}/plandata/upgrade_plan.yaml)
    [ ! -f "${base_path}/tools/upgrade_plan.yaml_bak" ] && cp -p ${custom_upgrade_yaml} ${base_path}/tools/upgrade_plan.yaml_bak
    
    sed -i "s#addFeatures: \[.*\]#addFeatures: \[${add_features}\]#" ${custom_upgrade_yaml}
    if [ "X${netWorkType}" == "XM" ];then
        if [ "X${DVSecondary_MGR_IPV6}" != "X" ];then
            scp ${custom_upgrade_yaml} ossadm@[${DVSecondary_MGR_IPV6}]:${custom_upgrade_yaml}
        else
            scp ${custom_upgrade_yaml} ossadm@${DVSecondary_IPV4}:${custom_upgrade_yaml}
        fi
    fi
       
    
}

function custom_upgrade_param()
{
    log_echo "INFO" "custom_upgrade_param start..."
    if [ "X${netWorkType}" != "XM" ];then
        log_echo "INFO" "The netWorkType=${netWorkType} is not equal M.not need to custom it."
        return 0
    fi
    
    if [ ! -f ${install_path}/manager/etc/sysconf/nodelists.json ];then
        log_echo "ERROR" "The ${install_path}/manager/etc/sysconf/nodelists.json is not exists."
        exit 1
    fi
    
    ## find the key "hostname": "ExtendClusterNode1"
    grep "\"hostname\"[[:blank:]]*:[[:blank:]]*\"ExtendClusterNode1\|SMExtendCluster1\"" ${install_path}/manager/etc/sysconf/nodelists.json 
    if [ $? -ne 0 ];then
        log_echo "INFO" "The netWorkType=${netWorkType} is M but nodelists.json not found ExtendClusterNode1 and SMExtendCluster1.not need to custom it."
        return 0
    fi
    
    log_echo "INFO" "The netWorkType=${netWorkType} is M and nodelists.json has ExtendClusterNode1.need to custom it."
    local custom_upgrade_yaml=$(ls ${install_path}/share/manager/SwiftDeploy/extend/upgrade/DigitalView/*-${current_version}/plandata/upgrade_plan.yaml)
    [ ! -f "${base_path}/tools/upgrade_plan.yaml_bak" ] && cp -p ${custom_upgrade_yaml} ${base_path}/tools/upgrade_plan.yaml_bak
    
    ## set partialCombine: true
    sed -i "s#partialCombine:[[:blank:]]*false#partialCombine: true#g" ${custom_upgrade_yaml}
    log_echo "INFO" "set partialCombine: true.ret=$?"
    
    if [ "X${DVSecondary_MGR_IPV6}" != "X" ];then
        scp -P ${i2k_ssh_port} ${custom_upgrade_yaml} ossadm@[${DVSecondary_MGR_IPV6}]:${custom_upgrade_yaml}
        log_echo "INFO" "scp yaml file to ${DVSecondary_MGR_IPV6}.ret=$?"
    else
        scp -P ${i2k_ssh_port} ${custom_upgrade_yaml} ossadm@${DVSecondary_IPV4}:${custom_upgrade_yaml}
        log_echo "INFO" "scp yaml file to ${DVSecondary_IPV4}.ret=$?"
    fi
    log_echo "INFO" "custom_upgrade_param finished."
}


function custom_upgrade_guide_workflow()
{
    log_echo "INFO" "custom_upgrade_guide_workflow start..."
    local upgrade_guide_workflow=/opt/oss/swiftdeploy/SwiftDeploy/etc/workflow/upgrade_guide_workflow.xml
    if [ -f ${upgrade_guide_workflow} ];then
        sed -i "/start_product/d" ${upgrade_guide_workflow}
        log_echo "INFO" "delete start_product,ret=$?"
    else
        log_echo "INFO" "The upgrade_guide_workflow=${upgrade_guide_workflow} is not exists."
        return 0
    fi
    
    if [ "X${netWorkType}" != "XM" ];then
        log_echo "INFO" "The netWorkType=${netWorkType} is not equal M.not need to copy it."
        return 0
    fi
    
    log_echo "INFO" "The netWorkType=${netWorkType} is M and has upgrade_guide_workflow,need to copy it."
    if [ "X${DVSecondary_MGR_IPV6}" != "X" ];then
        scp -P ${i2k_ssh_port} ${upgrade_guide_workflow} ossadm@[${DVSecondary_MGR_IPV6}]:${upgrade_guide_workflow}
        log_echo "INFO" "scp upgrade_guide_workflow file to ${DVSecondary_MGR_IPV6}.ret=$?"
    else
        scp -P ${i2k_ssh_port} ${upgrade_guide_workflow} ossadm@${DVSecondary_IPV4}:${upgrade_guide_workflow}
        log_echo "INFO" "scp upgrade_guide_workflow file to ${DVSecondary_IPV4}.ret=$?"
    fi
    log_echo "INFO" "custom_upgrade_guide_workflow finished."
}

function deleted_rollbak_step()
{
    local rollback_workflow=$1
    local job_name=$2
    local step_name=$3
    local custom_rollback_py=$(ls ${install_path}/share/manager/SwiftDeploy/extend/upgrade/DigitalView/*-${current_version}/script/custom_rollback_workflow.py)
    if [ ! -f "${custom_rollback_py}" ];then
        log_echo "ERROR" "Get custom_rollback_workflow.py failed,the ${custom_rollback_py} is not exist. please check"
        exit 1
    fi
    
    source ${install_path}/manager/bin/engr_profile.sh;
    python ${custom_rollback_py} "${rollback_workflow}" "${job_name}" "${step_name}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "custom_rollback_task ${job_name} ${step_name} failed, please check"
        exit 1
    fi
    
    grep "${step_name}" ${rollback_workflow} >> $LOG_FILE 2>&1
    if [ $? -eq 0 ];then
        log_echo "ERROR" "check custom_rollback_task ${step_name} failed, please check"
        exit 1
    fi
}

function custom_rollback_task()
{
    log_echo "INFO" "begin to custom_rollback_task"
    local rollback_workflow="/opt/oss/swiftdeploy/SwiftDeploy/etc/workflow/rollback_guide_workflow.yaml"
    if [ ! -f ${rollback_workflow} ];then
        log_echo "ERROR" "The rollback_workflow=${rollback_workflow} is not exist."
        exit 1
    fi
    
    local custom_backup_dvpm_json=$(ls ${install_path}/share/manager/SwiftDeploy/extend/upgrade/DigitalView/*-${current_version}/plandata/custom_backup.json)
    if [ ! -f "${custom_backup_dvpm_json}" ];then
        log_echo "ERROR" "Get custom_backup.json failed,the ${custom_backup_dvpm_json} is not exist. please check"
        exit 1
    fi
    
    local delete_tag=0
    grep "\"mode\"\: \"logicalbin\"" ${custom_backup_dvpm_json} >> $LOG_FILE 2>&1
    if [ $? -eq 0  -a ! -f /home/<USER>/restore_db_app.tag ];then
        log_echo "INFO" "custom_backup.json logicalbin."
        delete_tag=1
    fi
    
    log_echo "INFO" "The delete_tag=${delete_tag}"
    grep "backup_db_software" ${rollback_workflow} >> $LOG_FILE 2>&1
    if [ $? -eq 0 -a ${delete_tag} -eq 1 ];then
        deleted_rollbak_step  ${rollback_workflow} "BACKUP" "backup_db_software"
    fi
    
    cat /opt/oss/manager/etc/sysconf/nodelists.json | grep hostname | grep -w "DVPrimaryNode" >> $LOG_FILE 2>&1
    if [ $? -eq 0 -a ${delete_tag} -eq 1 ];then
        grep "upgrade_uniep_db_software" ${rollback_workflow}
        if [ $? -eq 0 ];then
            deleted_rollbak_step  ${rollback_workflow} "UPGRADE" "upgrade_uniep_db_software"
        fi
    fi
    log_echo "INFO" "custom deleted finish."
    return 0
}

function clean_ossadm_history()
{
    if [ -f "/home/<USER>/.bash_history" ];then
        sed -i "/tools\/uniep_encrypt.py/d" /home/<USER>/.bash_history
    fi

    if [ "X$(whoami)" == "Xroot" ];then
        su - ossadm -c "history -r"
    else
        history -r
    fi

}
function alter_undo_retention_time()
{
    local result=0

    chown ossadm:ossgroup ${base_path}/tools/dv_zenith_tool.py
    db_list=$(source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${base_path}/tools/dv_zenith_tool.py get_instance_ip cloudsop)
    if  [ "X${db_list}" == "X" ];then
        result=1
        log_echo "ERROR" "get cloudsopdb ip failed" 
    fi
    for db_ip in ${db_list[*]};do
        echo $db_ip|grep ":"
        if [ $? -eq 0 ];then
            db_scp_ip="[$db_ip]"
        else
            db_scp_ip=$db_ip		
        fi
        scp  -o StrictHostKeyChecking=no -P ${i2k_ssh_port} "${base_path}/tools/dv_zenith_tool.py" "${db_scp_ip}:/home/<USER>/" >>$LOG_FILE  2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "scp dv_zenith_tool.py to ${db_ip} failed" 
            exit 1
        fi
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${db_ip} 'source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python /home/<USER>/dv_zenith_tool.py excute_sql  cloudsop  sys "alter system set undo_retention_time = 300"'
        if [ $? -ne 0 ];then
            result=1
            log_echo "ERROR" "alter undo_retention_time at node ${db_ip} failed" 
        fi
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${db_ip} 'source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python /home/<USER>/dv_zenith_tool.py drop_pm_data_role'
        if [ $? -ne 0 ];then
            result=1
            log_echo "ERROR" "drop_pm_data_role at node ${db_ip} failed" 
        fi
        ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${db_ip} 'source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python /home/<USER>/dv_zenith_tool.py create_cswebdb_tablespace'
        if [ $? -ne 0 ];then
            result=1
            log_echo "ERROR" "create_cswebdb_tablespace at node ${db_ip} failed" 
        fi
        ssh -q -o StrictHostKeyChecking=no ${db_ip}  'rm  -f /home/<USER>/dv_zenith_tool.py'
        if [ ${result} -eq 1 ];then
            exit 1
        fi
    done
}

function custom_eamdb()
{
    masterid=$(bash /opt/oss/manager/apps/DBAgent/bin/dbsvc_adm -cmd query-db-instance -tenant SOP -type zenith|grep cloudsopdbsvr|head -n 1 |awk '{print $1}')
    bash /opt/oss/manager/apps/DBAgent/bin/dbsvc_adm -cmd query-database -instid ${masterid}|grep cswebdb
    if [ $? -ne 0 ];then
        log_echo "INFO" "There is no cswebdb,create it before upgread."
        bash /opt/oss/manager/apps/DBAgent/bin/dbsvc_adm -cmd add-database -instid ${masterid} -dbnames cswebdb -appnames DVEngineeringService -datasizes 1024
    fi
    dbinfo=$(bash /opt/oss/manager/apps/DBAgent/bin/dbsvc_adm -cmd query-database -instid ${masterid}|grep eamdb)
    if [ "X${dbinfo}" == "X" ];then
        log_echo "INFO" "There is no eamdb"
        return 0
    fi

    service_name=$(echo ${dbinfo}|awk '{print $2}')
    if [ "X${service_name}" == "XDTENEManagerService" ];then
        return 0
    fi
    dbsize=$(echo ${dbinfo}|awk '{print $3}')
    bash /opt/oss/manager/apps/DBAgent/bin/dbsvc_adm dbsvc_adm -cmd modify-database -instid ${masterid} -dbname eamdb -appname DTENEManagerService -datasize ${dbsize}
}

function custom_i2k_version()
{
    local i2k_version_file="/opt/oss/share/SOP/DVEngineeringService/I2000/common/config/CommonInfo.ini"
    local i2k_default_version_file="/opt/oss/SOP/apps/DVEngineeringService/script/conf/i2k_default.properties"
    local nodelists_json=/opt/oss/manager/etc/sysconf/nodelists.json

    local app_ip_list=$(source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${CUR_PATH}/get_install_info.py ${nodelists_json} get_app_ip)
    if [ "${app_ip_list}" == "ERROR" ];then
        log_echo "ERROR" "get app_ip_list=${app_ip_list} failed, please check"
        exit 1
    fi

    for app_ip in $(echo "${app_ip_list}"|sed "s/,/ /g");do
        ssh_cmd="ssh  -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${app_ip} sudo -u ossuser"

        if ${ssh_cmd} test -f "${i2k_version_file}";then
            if ${ssh_cmd} test ! -f "${i2k_default_version_file}";then
                ${ssh_cmd} unzip -oq "/opt/oss/SOP/apps/DVEngineeringService/I2000_*_ProductINSTALL_*.zip" "script/conf/i2k_default.properties" -d "/home/<USER>"
                i2k_default_version_file="/home/<USER>/script/conf/i2k_default.properties"
                log_echo "INFO" "unzip /opt/oss/SOP/apps/DVEngineeringService/I2000_*_ProductINSTALL_*.zip/script/conf/i2k_default.properties to ${i2k_default_version_file}"
            fi

            plat_version=$(${ssh_cmd} grep PRODUCT_PLATVERSION ${i2k_default_version_file} | awk -F '=' '{print $2}')
            i2k_version=$(${ssh_cmd} grep FullVersion ${i2k_version_file} | awk -F '=' '{print $2}')

            if [ -z "${plat_version}" ] || [ -z "${i2k_version}" ];then
                log_echo "ERROR" "Get plat_version or i2k_version equal null, please check"
                exit 1
            fi

            log_echo "recover version i2k_version ${i2k_version} to ${plat_version} in node ${app_ip}"

            ${ssh_cmd} sed -i "s/^FullVersion=.*/FullVersion=${plat_version}/g" ${i2k_version_file} > /dev/null 2>&1

            ${ssh_cmd} rm -rf "/home/<USER>/script" 2>/dev/null
        fi
    done
}

function execute_py_script()
{
    local commond="export HISTSIZE=0;source /opt/oss/manager/agent/MaintenanceService/lbin/engr_profile.sh >/dev/null 2>&1;python $@"
    if [ "X$(whoami)" == "Xroot" ];then
        su - ossadm -c "$commond"
    else
        bash -c "$commond"
    fi
}

function start_product()
{
    log_echo "INFO" "begin to check start_product"
    execute_py_script $CUR_PATH/autoupgrade/tools/call_ir_rest.py get_productstatus
    ret=$?
    if [ $ret -eq 0 ];then
        log_echo "INFO" "Product is running, no need to start"
        return 0
    elif [ $ret -eq 1 ];then
        log_echo "ERROR" "Get product status failed, please check"
        exit 1
    fi

    log_echo "INFO" "try to start_product"
    ##get_productstatus return result 101 , start product
    if [ "X$(whoami)" == "Xroot" ];then
        su - ossadm -c "/opt/oss/manager/tools/monitor/startproduct.sh -pn SOP"
    else
        /opt/oss/manager/tools/monitor/startproduct.sh -pn SOP
    fi
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute start_product failed, please check."
        exit 1
    fi
}

function bss_check()
{
    execute_py_script $CUR_PATH/autoupgrade/tools/check_bss.py
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Check backup server get failed, for details about how to handle the error, see section FAQ in the upgrade guide."
        exit 1
    else
        log_echo "INFO" "Check backup server success"
    fi
}

function OMMHA_switchover()
{
    if [ "X${sudo_drbd_disable_cmd}" == "X" ];then
        return 0
    fi
    ossadm_ssh_alias="ssh -q -o StrictHostKeyChecking=no -o ServerAliveInterval=60 -o ServerAliveCountMax=3 -p ${i2k_ssh_port}"
    primary_ssh_ip=$1
    secondary_ssh_ip=$2
    ${ossadm_ssh_alias} ${primary_ssh_ip} "ls /opt/oss/manager/var/share/standby_flag_file" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        ${ossadm_ssh_alias}  ${secondary_ssh_ip}   -n "sh /opt/oss/SOP/apps/OMMHAService/bin/forbidden_switch.sh  cancel"
        ${ossadm_ssh_alias}  ${secondary_ssh_ip}   -n "sh /opt/oss/SOP/apps/OMMHAService/bin/switchover.sh"
        local num=0
        local max_num=20
        while [ ${num} -lt ${max_num} ]
        do
            log_echo "INFO" "OMMHA is switching"
            sleep 60
            ((num++))
            ${ossadm_ssh_alias} ${secondary_ssh_ip} "ls /opt/oss/manager/var/share/standby_flag_file"
            if [ $? -eq 0 ];then
                output=$(${ossadm_ssh_alias} ${secondary_ssh_ip} "sh /opt/oss/SOP/apps/OMMHAService/bin/status.sh")
                echo $output|grep -qw Inactive  && echo $output|grep -qw Active  && \
                log_echo "INFO" "switchover successfully. The float ip is in local." && \
                ${ossadm_ssh_alias}  ${primary_ssh_ip} "sh /opt/oss/SOP/apps/OMMHAService/bin/forbidden_switch.sh  forbidden 1440"  && \
                return 0
            fi
        done
    else
        return 0
    fi
    log_echo "ERROR" "OMMHA switch fail"
    exit 1
}

function upgrade_LVS_config()
{
    current_LVS_VIP_Addr=$(get_current_value "LVS_VIP_Addr")
    current_LVS_VIP_Addr_Ipv6=$(get_current_value "LVS_VIP_Addr_Ipv6")
    if [ "X${current_LVS_VIP_Addr}" == "XERROR" ] || [ "X${current_LVS_VIP_Addr_Ipv6}" == "XERROR" ];then
        log_echo "ERROR" "get current_LVS_VIP_Addr failed"
        exit 1
    fi

    if [[ $current_LVS_VIP_Addr_Ipv6 =~ ^\{\{ ]]; then
        current_LVS_VIP_Addr_Ipv6=""
    fi
    productext_modify "${product_json_path}" "LVS_VIP_Addr" "${current_LVS_VIP_Addr}"
    productext_modify "${product_json_path}" "LVS_VIP_Addr_Ipv6" "${current_LVS_VIP_Addr_Ipv6}"
}

function check_kafka_mount()
{
    local node_ip="$1"

    if [ "X${node_ip}" == "Xlocal" ]; then
        lsblk -Pb | grep -Ew "MOUNTPOINT[S]*=\"/opt/oss/share/SOP/MQAgent/MessagingBrokeService/data/kafka-logs\""
        if [ $? -eq 0 ]; then
            log_echo "INFO" "Using lsblk -Pb grep /opt/oss/share/SOP/MQAgent/MessagingBrokeService/data/kafka-logs mount point success."
            return 0
        fi
        log_echo "INFO" "Using lsblk -Pb grep /opt/oss/share/SOP/MQAgent/MessagingBrokeService/data/kafka-logs can not find mount point, return 1."
        return 1
    fi
    local grep_cmd="lsblk -Pb | grep -Ew \"MOUNTPOINT[S]*=\\\"/opt/oss/share/SOP/MQAgent/MessagingBrokeService/data/kafka-logs\\\"\""
    ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ip} "${grep_cmd}"
    if [ $? -eq 0 ]; then
        log_echo "INFO" "To DB node=${node_ip} using lsblk -Pb grep /opt/oss/share/SOP/MQAgent/MessagingBrokeService/data/kafka-logs mount point success."
        return 0
    fi
    log_echo "INFO" "To DB node=${node_ip} using lsblk -Pb grep /opt/oss/share/SOP/MQAgent/MessagingBrokeService/data/kafka-logs can not find mount point, return 1."
    return 1
}

function build_ossuser_trust_relationships()
{
    base_version=$(cat ${CUR_PATH}/expInfo/product_SOP.json | sed 's/,/\n/g' |grep "product_version" |awk -F'"' '{print $4}' | sed 's/ //g' |tr '[a-z]' '[A-Z]')

    echo ${base_version} |grep "V8" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Get base version failed."
        exit 1
    fi

    if [ -z "$(echo ${base_version} | grep -i 'V800R001C60')" ]; then
        log_echo "INFO" "base_version=${base_version},not need build ossuser trust relationships."
        return 0
    fi

    local SSH="ssh -q -o StrictHostKeyChecking=no -p ${i2k_ssh_port}"
    local first_ip=$1
    local secondary_ip=$2
    ret=0


    first_pub=$(${SSH} ${first_ip} "sudo -u ossuser grep 'ssh-rsa' /home/<USER>/.ssh/id_rsa.pub")
    secondary_pub=$(${SSH} ${secondary_ip} "sudo -u ossuser grep 'ssh-rsa' /home/<USER>/.ssh/id_rsa.pub")

    first_pub_tmp=$(echo ${first_pub}|awk '{print $2}')
    secondary_pub_tmp=$(echo ${secondary_pub}|awk '{print $2}')

    ${SSH} ${first_ip} "sudo -u ossuser grep \"${secondary_pub_tmp}\" /home/<USER>/.ssh/authorized_keys"
    if [ $? -ne 0 ];then
        if [ -z "${secondary_pub}" ];then
            log_echo "ERROR" "get ssh-rsa failed in ${secondary_ip},maybe /home/<USER>/.ssh/id_rsa.pub isn't exist,please check."
            exit 1
        fi

        ${SSH} ${first_ip} "echo ${secondary_pub} | sudo -u ossuser tee -a  /home/<USER>/.ssh/authorized_keys" > /dev/null 2>&1
        [ $? -ne 0 ] && ret=1
    fi

    ${SSH} ${secondary_ip} "sudo -u ossuser grep \"${first_pub_tmp}\" /home/<USER>/.ssh/authorized_keys"
    if [ $? -ne 0 ];then
        if [ -z "${first_pub}" ];then
            log_echo "ERROR" "get ssh-rsa failed in ${first_ip},maybe /home/<USER>/.ssh/id_rsa.pub isn't exist,please check."
            exit 1
        fi

        ${SSH} ${secondary_ip} "echo ${first_pub} | sudo -u ossuser tee -a  /home/<USER>/.ssh/authorized_keys" > /dev/null 2>&1
         [ $? -ne 0 ] && ret=1
    fi

    if [ $ret -ne 0 ];then
        log_echo "ERROR" "Failed to create ossuser trust for hofs between ${first_ip} and ${secondary_ip}."
        exit 1
    fi
}

function check_jar_path_with_root_jar()
{
    local check_root_path_list="${install_path}/manager/var/installpackages/libs ${install_path}/rtsp /opt/oss/libossadm /opt/oss/libossuser /opt/oss/libsecuser"
    local check_root_path;
    for check_root_path in ${check_root_path_list}
    do
        if [ ! -d "${check_root_path}" ];then
            continue
        fi
        find_ret=$(find "${check_root_path}" -user root 2> /dev/null |awk '{printf $1","}')
        if [ ! -z "${find_ret}" ];then
            log_echo "ERROR" "Files or directories with the root permission exist in the ${check_root_path} directory.please check on UniEP ${UNIEP_NODE_IP}."
            log_echo "ERROR" "The root permission file or dir is: ${find_ret}"
            exit 1
        fi
    done
}

function modify_aIOpsMenuConfig()
{
    current_aIOpsMenuConfig_value=$(get_current_value "AIOpsMenuConfig")
    if [ -z "${current_aIOpsMenuConfig_value}" ] || [[ "${current_aIOpsMenuConfig_value}" =~ "{{" ]];then
        productext_modify "${product_json_path}" "AIOpsMenuConfig" "faultAnalysis,capacityEvaluation,indicatorPrediction,disasterRecovery"
    fi
}

function rollbackproductinfo()
{
    log_echo "INFO" "rollbackproductinfo start..."
    product_json_path=$(ls ${base_path}/tools/expInfo/product_*.json)
    if [ -z "${product_json_path}" -o ! -f "${product_json_path}" ];then
        log_echo "INFO" "The product_json_path=${product_json_path} is not exists."
        return 0
    fi
    
    log_echo "INFO" "The product_json_path=${product_json_path}"
    if [ ! -f "${product_json_path}" ];then
        log_echo "ERROR" "The product_json_path=${product_json_path} is not exists."
        exit 1
    fi
    
    modify_product_script="/opt/oss/manager/tools/resmgr/modifyproductinfo.sh"
    sh ${modify_product_script} -input ${product_json_path}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute cmd [ sh ${modify_product_script} -input ${product_json_path} ] failed, Fail to modify product info, the file is ${product_json_path}..."
        exit 1
    fi
    
    log_echo "INFO" "rollbackproductinfo End."
    return 0
}


function del_file_is_exist()
{
    local file_name="$1"
    if [ -f "${file_name}" ]; then
        rm -f ${file_name} 2>/dev/null
        [ $? -eq 0 ] && log_echo "INFO" "delete file=${file_name} success."

    fi
}

function clear_redundancy_rtsp()
{
    log_echo "INFO" "Begin to clear redundancy rtsp..."
    local AppController_Path="$install_path/manager/apps/AppController/"
    if [ -f "$AppController_Path/SMP/clustermanager/clear_rtsp.sh" ]; then
        log_echo "INFO" "Rtsp clear script is exist."
        local exec_time=0
        local success_tag=1
        while [ $exec_time -lt 3 ]; do
            sh ${base_path}/Common/rtsp_clear.sh "$AppController_Path" >> $LOG_FILE
            if [ $? -eq 0 ]; then
                success_tag=0
                break
            fi
            log_echo "INFO" "Clear redundancy rtsp is not success, try again time=${exec_time}..."
            ((exec_time++))
        done
        if [ $success_tag -eq 0 ]; then
            log_echo "INFO" "End to clear redundancy rtsp."
        else
            log_echo "WARN" "Clear redundancy rtsp is not success, need manually clear the rtsp..."
        fi
    fi
}

function check_tzdata()
{
    if [ -f /home/<USER>/update_time.log ];then
        tzdata_file=/opt/tz/tzdata-latest.tar.gz
        if [ ! -f ${tzdata_file} ];then
            log_echo "ERROR" "The daylight saving time has been updated, but ${tzdata_file} is not exit, please check"
            exit 1
        fi
        tzupadte_file=/opt/tz/tzupdater.jar
        if [ ! -f ${tzupadte_file} ];then
            log_echo "ERROR" "The daylight saving time has been updated, ${tzupadte_file} is not exit, please check"
            exit 1
        fi
    fi
}
