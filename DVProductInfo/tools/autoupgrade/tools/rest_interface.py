#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# Copyright © Huawei Technologies Co., Ltd. 2022-2030. All rights reserved.
import math
import os
import re
import sys
import time
import typing
import types
import json
import logging
import subprocess

from urllib.parse import urlencode, quote, urljoin, urlparse, urlunparse
from tornado import httputil, httpclient
from tornado import simple_httpclient
from tornado import ioloop

fake_response = types.SimpleNamespace()

script_path = os.path.dirname(os.path.abspath(__file__))

LOGGER = logging.Logger("log1")
TASK_STEP_MAPPING = {"ENV_CHECK": 1, "BACKUP": 2, "UPGRADE": 3, "VERIFY": 4}
TASK_STATE_MAPPING = {"FINISHED": 0, "PENDING": 1, "RUNNING": 2, "FAILED": 3}
SWAP_TASK_STATE_MAPPING = {value: key for key, value in TASK_STATE_MAPPING.items()}


class RESTConnectionException(ConnectionRefusedError):
    def __init__(self,
                 response: typing.Union[httpclient.HTTPResponse, types.SimpleNamespace] = None):
        super(RESTConnectionException, self).__init__()
        self.body = "" if not response else response.body

    def __str__(self):
        return self.body.decode("utf-8") if isinstance(self.body, bytes) else self.body


class HTTPClient:
    def __init__(
            self,
            url,
            method: str,
            headers: typing.Union[typing.Dict[str, str], httputil.HTTPHeaders] = None,
            body: str = None,
            get_response_instance: bool = False,
            **kwargs
    ):
        self.url = url
        self.method = method
        self.headers = headers
        self.body = body
        self.get_response_instance = get_response_instance
        self.kwargs = kwargs
        self.client = httpclient.HTTPClient()
        

    def __enter__(self):
        self.client = httpclient.HTTPClient()
        follow_redirects = self.kwargs.pop("follow_redirects", True)
        allow_nonstandard_methods = self.kwargs.pop("allow_nonstandard_methods", False)
        if self.method.upper() in ["POST", "PATCH", "PUT"] and self.body is None:
            raise ValueError
        self.kwargs.setdefault("request_timeout", 600)
        try:
            response = self.client.fetch(
                self.url,
                method=self.method,
                headers=self.headers,
                body=self.body,
                validate_cert=False,
                follow_redirects=follow_redirects,
                allow_nonstandard_methods=allow_nonstandard_methods,
                **self.kwargs
            )
        except ConnectionRefusedError as error:
            fake_response.body = "request %r refused, error: %r" % (self.url, error)
            raise RESTConnectionException(fake_response) from error
        except (httpclient.HTTPError, simple_httpclient.HTTPTimeoutError) as error:
            print("request url: (%s %s), http_response: %s",
                  self.url, self.method, getattr(error, "response", ""))
            raise RESTConnectionException(error.response) from error
        if self.get_response_instance:
            return response
        if response.headers.get("Content-Type", "").lower().startswith("application/json"):
            return json.loads(response.body)
        return response.body.decode("utf-8")

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.client.close()


def http_get(
        url: str,
        headers: typing.Union[typing.Dict[str, str], httputil.HTTPHeaders] = None,
        get_response_instance: bool = False,
        **kwargs
):
    return do_request(url, "GET", headers, get_response_instance=get_response_instance, **kwargs)


def http_post(
        url: str,
        body: dict,
        headers: typing.Union[typing.Dict[str, str], httputil.HTTPHeaders] = None,
        get_response_instance: bool = False,
        **kwargs
):
    if not isinstance(headers, httputil.HTTPHeaders):
        headers = httputil.HTTPHeaders(headers or {})
    if headers.get("content-type", "").startswith("application/json"):
        body = json.dumps(body)
    else:
        body = urlencode(body)
    return do_request(url, "POST", headers, body, get_response_instance=get_response_instance,
                      **kwargs)


def do_request(
        url,
        method,
        headers: typing.Union[typing.Dict[str, str], httputil.HTTPHeaders] = None,
        body: str = None,
        get_response_instance: bool = False,
        **kwargs
) -> typing.Union[httpclient.HTTPResponse, str, dict]:
    with HTTPClient(url, method, headers, body, get_response_instance, **kwargs) as f:
        return f


class SwiftDepoyHTTPClient:
    """HTTPClient for SwiftDeploy."""
    attempt_number = 0

    @classmethod
    def client(cls, host_ip, username, security_code: str, port: int = 31050, check_service=True):
        """Factory function for initialization."""
        client = cls(host_ip, username, security_code, port)
        client.login()
        return client

    def __init__(self, host_ip, username, security_code: str, port: int = 31050):
        self._csrf_token = None
        self._username = username
        self._security_code = quote(security_code)
        host_ip = f"[{host_ip}]" if ":" in host_ip else host_ip
        self._host_port = "https://" + host_ip + ":" + str(port)
        self.cookies = None

    def system_current_config(self):
        """Get current configuration."""
        try:
            response = http_get(
                urljoin(self._host_port, "/system/currentconfig"),
                headers={"X-CSRF-TOKEN": self._csrf_token, "Cookie": self.cookies},
            )
        except RESTConnectionException:
            return False
        return isinstance(response, dict)

    def get(self, api_path: str, url_params: dict = None, request_params: dict = None, **kwargs):
        """HTTP GET method."""
        if not self.system_current_config():
            self.login()

        request_params = {} if request_params is None else request_params
        for key in re.findall(r"{\w+[-\w]+}", api_path):
            request_params.pop(key, None)
        if isinstance(url_params, dict):
            api_path = api_path.format(**url_params)
        parsed = urlparse(self._host_port, allow_fragments=True)
        # Refactor: `api_path` contains query_parameters
        url = urlunparse(
            [parsed.scheme, parsed.netloc, api_path, "", urlencode(request_params), parsed.fragment]
        )
        response = http_get(
            url, headers={"X-CSRF-TOKEN": self._csrf_token, "Cookie": self.cookies}, **kwargs
        )
        return response

    def post(self, api_path: str, url_params: dict = None, request_params: dict = None, **kwargs):
        """HTTP POST method."""
        if not self.system_current_config():
            self.login()

        if request_params is None:
            request_params = {}
        for key in re.findall(r"{\w+[-\w]+}", api_path):
            request_params.pop(key, None)
        if isinstance(url_params, dict):
            api_path = api_path.format(**url_params)
        response = http_post(
            urljoin(self._host_port, api_path),
            headers={
                "Content-Type": "application/json; charset=utf-8", "X-CSRF-TOKEN": self._csrf_token,
                "Cookie": self.cookies,
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, \
                 like Gecko) Chrome/********* Safari/537.36"
            },
            body=request_params,
            **kwargs,
        )
        return response

    def _get_task_progress(self, api_path, workflow_id):
        try:
            response = self.get(api_path, url_params={"workflow-id": workflow_id})
        except RESTConnectionException as error:
            LOGGER.error("request error: %r", getattr(error, "body", ""))
            raise
        try:
            env_check_task_info = response.get("entity", {}).get("stepProgressList", [])
        except AttributeError as error:
            fake_response.body = "origin response %r, error: %r" % (response, error)
            raise RESTConnectionException(fake_response) from error
        return env_check_task_info

    @classmethod
    def _log_task_progress(cls, task_index, env_check_task_info, is_rollback, task_value):
        try:
            max_percent = min(
                math.floor(task_index / (len(env_check_task_info["taskList"]) + 1) * 100), 90
            )
        except ZeroDivisionError as _ex:
            raise ZeroDivisionError(f"{_ex!s}") from _ex
        print(
            "{} for {!r} | {} |  {}/100".format(
                "Upgrading" if not is_rollback else "Rollbacking", task_value["taskName"],
                "=" * max(math.floor(max_percent / 10), 1),
                max_percent
            ), flush=True,
        )
        LOGGER.info(
            "task %r current state %r progress %r",
            task_value["taskName"], SWAP_TASK_STATE_MAPPING.get(task_value["state"]),
            task_value["progress"]
        )

    def _check_task_progress(self, workflow_id, api_path, is_rollback, **kwargs) -> list:
        check_item_index, sleep_time = kwargs.get("check_item_index"), kwargs.get("sleep_time")
        max_time, total_time, request_flag = 3 * 60 * 60, 0, True
        max_timeout, total_timeout = 60 * 30, 0
        while True:
            try:
                env_check_task_info = self._get_task_progress(api_path, workflow_id)
            except Exception as error:
                print("wait for get_task_progress request, continue to try")
                time.sleep(10)
                total_timeout += 10
                if total_timeout > max_timeout:
                    fake_response.body = "get_task_progress request timeout, please check"
                    raise RESTConnectionException(fake_response) from error
                continue

            total_timeout = 0
            if len(env_check_task_info) < check_item_index + 1:
                fake_response.body = "not find required data in %s element" % (check_item_index + 1)
                raise RESTConnectionException(fake_response)
            env_check_task_info, sub_task_state = env_check_task_info[check_item_index], []
            for task_index, task_value in enumerate(env_check_task_info["taskList"], start=1):
                try:
                    self._log_task_progress(task_index, env_check_task_info, is_rollback, task_value)
                except ZeroDivisionError as _ex:
                    fake_response.body = f"{_ex!s}"
                    raise RESTConnectionException(fake_response) from _ex
                if SWAP_TASK_STATE_MAPPING.get(task_value["state"]) == "FAILED":
                    request_flag = False
                    break
                elif SWAP_TASK_STATE_MAPPING.get(task_value["state"]) == "FINISHED":
                    sub_task_state.append(task_value)
                    continue
                elif SWAP_TASK_STATE_MAPPING.get(task_value["state"]) == "RUNNING":
                    LOGGER.info("detect task %r current state is %r and progress %r", task_value["taskName"],
                                SWAP_TASK_STATE_MAPPING.get(task_value["state"]), task_value["progress"])
                    LOGGER.info("remain time about %r minutes, sleep %r seconds", task_value["remainTime"], sleep_time)
                    time.sleep(sleep_time)
                    total_time += sleep_time
                    break
            if not request_flag or len(sub_task_state) == len(env_check_task_info["taskList"]):
                break
            if total_time > max_time:
                fake_response.body = "task %r state is abnormal, poll %r seconds" % (
                    workflow_id, total_time
                )
                raise RESTConnectionException(fake_response)
        return env_check_task_info["taskList"]

    def check_task_progress(
            self, workflow_id: str, *, check_item_index: int, sleep_time: int = 30, is_rollback=False
    ) -> list:
        """Check SwiftDeploy task progress."""
        if check_item_index < 0:
            fake_response.body = "not find required response in %s element" % (check_item_index + 1)
            raise RESTConnectionException(fake_response)

        api_path = (
            "/rest/swiftdeploy/v1/workflow/{workflow-id}/rollback/progress"
            if is_rollback
            else "/rest/swiftdeploy/v1/workflow/{workflow-id}/progress"
        )
        task_list = self._check_task_progress(
            workflow_id, api_path, is_rollback, check_item_index=check_item_index,
            sleep_time=sleep_time
        )
        sys.stdout.write("\r")
        action_word = "Upgrading" if not is_rollback else "Rollbacking"
        print("{} | {} |  100/100".format(action_word, "=" * 10, 100), flush=True)
        return task_list

    def start_target_step(
            self, workflow_id, step_name: str, *, request_payload=None, is_rollback=False
    ) -> bool:
        """Start SwiftDeploy someone step."""
        max_timeout, total_timeout = 60 * 5, 0
        while True:
            try:
                response = self.post(
                    "/rest/swiftdeploy/v1/workflow/{workflow-id}/steps/{step-name}",
                    url_params={"workflow-id": workflow_id, "step-name": step_name},
                    raise_error=False,
                ) if not is_rollback else self.post(
                    "/rest/swiftdeploy/v1/workflow/{workflow-id}/rollback",
                    url_params={"workflow-id": workflow_id},
                    request_params={"rollback": request_payload},
                )
            except RESTConnectionException as error:
                if b"swiftdeploy.exist.running.task" in getattr(error, "body", ""):
                    LOGGER.warning("detect step_name: %s running already", step_name)
                    return True
                LOGGER.error("request error: %r", getattr(error, "body", ""))
                return False

            if isinstance(response, dict):
                break

            if total_timeout > max_timeout:
                LOGGER.error("wait request for start_target_step timeout, please check")
                return False
            print("try to get response for start_target_step again")
            time.sleep(10)
            total_timeout += 10

        result = response.get("result", "FAILED")
        if result == "OK":
            LOGGER.info("start task successfully and response result %s", result)
            LOGGER.info("start task %r successfully and response %r", workflow_id, response)
        else:
            LOGGER.error("start task failed since response result %s", result)
            LOGGER.error("start task %r failed since response %r", workflow_id, response)
        return result == "OK"

    def login(self):
        """SwiftDeploy login."""
        response = http_get(urljoin(self._host_port, "/servlet/swiftdeploy/serverutil/encrypt"), )
        hex_exponent, hex_modulus = response.get("exponent"), response.get("modulus")
        encrypt_pwd = exe_nodejs_encrypt(self._security_code, hex_exponent, hex_modulus)

        response = self.login_with_res(encrypt_pwd, hex_modulus)
        response_cookies = response.headers.get("Set-Cookie", "").split(";")
        self.cookies = response_cookies[0] if len(response_cookies) > 0 else ""
        response = http_get(urljoin(self._host_port, "/"), headers={"Cookie": self.cookies})
        self._csrf_token = re.search('token:\s*\"(.*)\"', response)[1]

        if not self.system_current_config():
            encrypt_pwd = rsa_nop_adding_encrypt_payload(hex_exponent, hex_modulus,
                                                         self._security_code)
            response = self.login_with_res(encrypt_pwd, hex_modulus)
            response_cookies = response.headers.get("Set-Cookie", "").split(";")
            self.cookies = response_cookies[0] if len(response_cookies) > 0 else ""
            response = http_get(urljoin(self._host_port, "/"), headers={"Cookie": self.cookies})
            self._csrf_token = re.search('token:\s*\"(.*)\"', response)[1]


    def login_with_res(self, encrypt_pwd, modulus):
        try:
            response = http_post(
                urljoin(self._host_port, "/login"),
                body={"username": self._username, "password": encrypt_pwd + ":" + modulus},
                follow_redirects=False,
                get_response_instance=True,
                raise_error=False,
            )
        except RESTConnectionException as error:
            fake_response.body = "Login failure."
            raise RESTConnectionException(fake_response) from error
        if "Set-Cookie" not in response.headers:
            fake_response.body = "not find 'Set-Cookie' headers in response"
            raise RESTConnectionException(fake_response)
        return response


def rsa_nop_adding_encrypt_payload(hex_c, hex_d, d_string: str) -> str:
    """RSA RFC2313."""
    decimal_exponent, decimal_modulus = int(hex_c, 16), int(hex_d, 16)

    target_key_length = (
        decimal_exponent.bit_length() // 8 + 1
        if decimal_exponent.bit_length() % 8
        else decimal_exponent.bit_length() // 8
    )
    padding_string = b""
    data_bytes = d_string.encode("utf-8", "ignore")
    for _ in range(target_key_length - len(data_bytes) - 3):
        padding_string += b"\x00"
    encryption_block_padding = b"".join([b"\x00\x00", padding_string, b"\x00", data_bytes[::-1]])
    payload = int.from_bytes(encryption_block_padding, "big", signed=False)
    encrypted: int = pow(payload, decimal_exponent, decimal_modulus)
    block: bytes = encrypted.to_bytes(max(1, math.ceil(encrypted.bit_length() / 8)), "big")
    return block.hex()


def check_credentials(host_ip, username, security_code: str) -> bool:
    """Check SwiftDeploy login credentials."""
    print("check {} swiftdeploy login credentials whether correct or not".format(host_ip))
    max_time, total_time = 300, 0
    while True:
        try:
            client = SwiftDepoyHTTPClient.client(host_ip, username, security_code, check_service=False)

            if client.system_current_config():
                print("check login SD success")
                return True
            if total_time > max_time:
                LOGGER.error("check login SD timeout, please check")
                return False
            time.sleep(5)
            total_time += 5
        except Exception as error:
            if total_time > max_time:
                LOGGER.error(error)
                LOGGER.error("check login SD timeout, please check")
                return False
            print("try to check login SD again")
            time.sleep(5)
            total_time += 5


def exe_nodejs_encrypt(pwd, exponent, modulus):
    shell_command = ("ls {script_path}/*/bin/node | grep nodejs | sort | tail -1".format(
                                                                                        script_path=script_path))
    nodejs_engine = subprocess.run(shell_command, shell=True, capture_output=True, text=True).stdout.strip()

    if not nodejs_engine:
        shell_command = ("ls /opt/oss/rtsp/*/bin/node | grep nodejs | sort | tail -1")
        nodejs_engine = subprocess.run(shell_command, shell=True, capture_output=True, text=True).stdout.strip()

    lib_path = nodejs_engine.replace("/bin/node", "") + "/lib"

    node_lib_path = "export LD_LIBRARY_PATH={lib_path}:$LD_LIBRARY_PATH".format(lib_path=lib_path)

    js_script = script_path + "/encrypt_sd_login_pwd.js"
    shell_command = ("{node_lib_path};{nodejs} {script} {exponent} {modulus} {pwd}".format(
                                                                                         node_lib_path=node_lib_path,
                                                                                         nodejs=nodejs_engine,
                                                                                         script=js_script,
                                                                                         exponent=exponent,
                                                                                         modulus=modulus,
                                                                                         pwd=pwd))

    return subprocess.run(shell_command, shell=True, capture_output=True, text=True).stdout.strip()


