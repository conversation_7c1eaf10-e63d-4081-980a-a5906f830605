#!/bin/bash
config_yamal="$1"
CURRENT_PATH=$(cd $(dirname $0); pwd)
. ${CURRENT_PATH}/utils.sh

function encrypt_by_uniep()
{
    local de_pvalue="$1"
    local encrypt_type=$2

    if [ -f "/proc/sys/kernel/random/entropy_avail" ];then
        random_num=$(cat /proc/sys/kernel/random/entropy_avail)
        log_echo "INFO"  "The value of random/entropy_avail is ${random_num} ..." > /dev/null
        if [ ${random_num} -lt 300 ];then
            haveged_path=$(which haveged 2>/dev/null)
            [ -n "$haveged_path" ] && ${haveged_path} -w 1024 -v 1 > /dev/null 2>&1
        fi
    fi

    local en_pvalue=""
    local get_ret=0
    for try_count in $(seq 1 5);do
        if [ -f "/proc/sys/kernel/random/entropy_avail" ];then
            random_num=$(cat /proc/sys/kernel/random/entropy_avail)
            log_echo "INFO"  "The value of random/entropy_avail is ${random_num} ..." > /dev/null
            if [ ${random_num} -lt 300 ];then
                haveged_path=$(which haveged 2>/dev/null)
                [ -n "$haveged_path" ] && ${haveged_path} -w 1024 -v 1 > /dev/null 2>&1
            fi
        fi
        if [ "X$(whoami)" == "Xroot" ];then
            en_pvalue=$(echo "${de_pvalue} uniep:passwd"| expect ${CURRENT_PATH}/../su_ossadm_exec.exp "${CURRENT_PATH}/../uniep_encrypt.py" "${encrypt_type}" | grep -vE "source|logout|uniep_encrypt|exit" | tail -1 | sed 's/ //g')
        else
            en_pvalue=$(export HISTSIZE=0;source /opt/oss/manager/bin/engr_profile.sh;echo "${de_pvalue}" | ${PYTHONHOME}/bin/python "${CURRENT_PATH}/../uniep_encrypt.py" "${encrypt_type}" | grep -vE "source|logout|uniep_encrypt|exit" | sed 's/ //g')
        fi
        if [ -z "$(echo ${en_pvalue}|grep ^000)" -a -z "$(echo ${en_pvalue}|grep ^AAA)" ];then
            log_echo "INFO" "encrypt by uniep failed , try again try_count=${try_count}"
            get_ret=1
        else
            get_ret=0
            break
        fi
    done
    
    if [ ${get_ret} -eq 1 ];then
        log_echo "ERROR" "encrypt by uniep failed, please check"
        exit 1
    fi
    pwd_return="${en_pvalue}"
    return 0  
}

function execute_py_script()
{
    local commond="export HISTSIZE=0;source ${install_path}/manager/agent/MaintenanceService/lbin/engr_profile.sh;python $@"
    if [ "X$(whoami)" == "Xroot" ];then
        su - ossadm -c "$commond"
    else
        bash -c "$commond"
    fi
    ##调用该函数的地方会判断返回值。
}
function execute_sd_script()
{
    local commond="export HISTSIZE=0;source ${install_path}/manager/bin/engr_profile.sh;python $@"
    if [ "X$(whoami)" == "Xroot" ];then
        su - ossadm -c "$commond"
    else
        bash -c "$commond"
    fi
    ##调用该函数的地方会判断返回值。
}


function init_pwd()
{
    for param in $(echo ${params}|tr ';' ' ')
    do
        key_value=$(echo "$param" | base64 -d)
        key=$(echo "${key_value}" | awk -F "=" '{print $1}')
        value=$(echo ${key_value}| sed -e "s#${key}=##")

        case "${key}" in
        'root_pwd')
            root_pwd="${value}"
            ;;
        'ossadm_pwd')
            ## 通过自定义插件拿的密码为密文，需要解密
            ossadm_pwd="${value}"
            is_encrypt_pwd "${ossadm_pwd}"
            if [ $? -eq 0 ]; then
                decrypt_by_uniep ${ossadm_pwd} "decrypt"
                ossadm_pwd=${decryptPasswd}
            fi
            ;;
        'sd_login_pwd')
            sd_login_pwd="${value}"
            is_encrypt_pwd "${sd_login_pwd}"
            if [ $? -eq 0 ]; then
                sd_pwd_encode="${sd_login_pwd}"
            else
                encrypt_by_uniep "${sd_login_pwd}" ""
                sd_pwd_encode="${pwd_return}"
            fi
            sed -i "s#^sd_login_pwd:.*#sd_login_pwd: ${sd_pwd_encode}#" ${config_yaml_path}
            ;;
        'peer_ossadm_pwd')
            peer_ossadm_pwd="${value}"
            ;;
        esac
    done
}

function init()
{
    cp -f ${config_yamal} ${CURRENT_PATH}/config.yaml
    config_yaml_path=${CURRENT_PATH}/config.yaml

    LOG_FILE=${CURRENT_PATH}/auto_upgrade.log
    action_tag=${CURRENT_PATH}/auto_upgrade.tag

    touch ${CURRENT_PATH}/auto_upgrade.tag

    if [ "X$(whoami)" == "Xroot" ];then
        chown ossadm:ossgroup ${CURRENT_PATH}/auto_upgrade.tag
    fi

    install_path=/opt/oss
    package_path=$(grep "package_path" ${CURRENT_PATH}/config.yaml|awk -F':' '{print $2}'|sed 's/ //')
    
    [ -f "/opt/oss/dv_autoupgrade.ini_finish" ] && dv_version=$(cat /opt/oss/dv_autoupgrade.ini_finish|grep "dv_version="|awk -F'=' '{print $2}')
    [ -f "/opt/oss/dv_autoupgrade.ini" ] && dv_version=$(cat /opt/oss/dv_autoupgrade.ini|grep "dv_version="|awk -F'=' '{print $2}')

    #获取产品版本等配置项写入upgrade.yaml
    upgrade_yaml=${CURRENT_PATH}/upgrade.yaml
    
    product_version=$(ls ${CURRENT_PATH}/../SD/SwiftDeployExtend_DV*.zip|awk -F'-' '{print $2}'|awk -F'.' '{print $1}')
    if [ "X${product_version}" == "X" ];then
        log_echo "ERROR" "Get product_version from SwiftDeployExtend failed, please check"
        exit 1
    else
        sed -i "/product_version:/d" ${upgrade_yaml} >/dev/null 2>&1
        echo "product_version: ${product_version}" >> ${upgrade_yaml}
    fi

    chmod 644 ${config_yaml_path} ${upgrade_yaml}
    
    step_name=$(cat ${config_yaml_path}|grep "upgrade_step"|awk -F'upgrade_step:' '{print $2}'|sed 's/ //g')
    
    init_pwd
    
    [ "X${ossadm_pwd}" == "X" ] && ossadm_pwd=$(cat ${config_yaml_path}|grep "^ossadm_pwd"|awk -F'ossadm_pwd:' '{print $2}'|sed 's/ //g')
    [ "X${root_pwd}" == "X" ] && root_pwd=$(cat ${config_yaml_path}|grep "^root_pwd"|awk -F'root_pwd:' '{print $2}'|sed 's/ //g')
    [ "X${peer_ossadm_pwd}" == "X" ] && peer_ossadm_pwd=$(cat ${config_yaml_path}|grep "^peer_ossadm_pwd"|awk -F'peer_ossadm_pwd:' '{print $2}'|sed 's/ //g')
    disaster_role=$(cat ${config_yaml_path}|grep "^disaster_role"|awk -F'disaster_role:' '{print $2}'|sed 's/ //g')
    preset_execute_path="/opt/oss/dv_upgrade_${dv_version}"
    sed -i "s/{{//g" ${config_yaml_path}
    sed -i "s/}}//g" ${config_yaml_path}
}

function get_uniep_ip()
{   
    uniep_access_ip=$(execute_py_script ${CURRENT_PATH}/tools/get_uniep_info.py access_ip)
    if [ "X${uniep_access_ip}" == "X" -o "X${uniep_access_ip}" == "XERROR" ];then
        log_echo "ERROR" "Get uniep access ip failed, please check"
        exit 1
    fi
    
    uniep_manage_ip=$(execute_py_script ${CURRENT_PATH}/tools/get_uniep_info.py manage_ip)
    if [ "X${uniep_manage_ip}" == "X" -o "X${uniep_manage_ip}" == "XERROR" ];then
        log_echo "ERROR" "Get uniep manage ip failed, please check"
        exit 1
    fi
}

function check_is_disaster()
{
   
    log_echo "INFO" "Begin to check it is disaster or not..."
    drinfo_json="${CURRENT_PATH}/drinfo.json"
    sitestatus_json="${CURRENT_PATH}/sitestatus.json"
    upgrade_yaml=${CURRENT_PATH}/upgrade.yaml

    rm -f ${drinfo_json} ${sitestatus_json}
    touch ${drinfo_json} ${sitestatus_json}
    touch ${upgrade_yaml}

    if [ "X$(whoami)" == "Xroot" ];then
        chown ossadm:ossgroup ${drinfo_json} ${sitestatus_json} ${upgrade_yaml}
    fi
    chmod 700 ${drinfo_json} ${sitestatus_json}
    execute_py_script ${CURRENT_PATH}/tools/call_ir_rest.py get_drinfo ${drinfo_json}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Get drinfo failed, please check"
        exit 1
    fi
    
    execute_py_script ${CURRENT_PATH}/tools/call_ir_rest.py get_sitestatus ${sitestatus_json}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Get sitestatus failed, please check"
        exit 1
    fi
    
    sed -i "/local_hb_ip:/d" ${upgrade_yaml} >/dev/null 2>&1
    sed -i "/local_uniep_ip:/d" ${upgrade_yaml} >/dev/null 2>&1
    
    cat ${drinfo_json} |grep "primary" >/dev/null 2>&1
    
    if [ $? -eq 0 ];then
        log_echo "INFO" "This is disaster."
        
        cp -a ${CURRENT_PATH}/drinfo.json  ${CURRENT_PATH}/drinfo_bak.json
        cp -a  ${CURRENT_PATH}/sitestatus.json ${CURRENT_PATH}/sitestatus_bak.json
        local_hb_ip=$(cat ${drinfo_json}|sed 's/,/\n/g'|grep "hbIP"|awk -F'"' '{print $4}')
        echo "local_hb_ip: ${local_hb_ip}" >> ${upgrade_yaml}
        
        get_uniep_ip
        echo "local_uniep_ip: ${uniep_access_ip}" >> ${upgrade_yaml}

        sed -i "/peer_dr_heartbeat_ip:/d" ${upgrade_yaml} >/dev/null 2>&1

        local local_site_role=$(cat ${CURRENT_PATH}/drinfo_bak.json | sed 's/,/\n/g'|grep "localSiteRole\":"|awk -F'"' '{print $4}')
        if [ "X$(whoami)" == "Xroot" ];then
            (su - ossadm -c "export HISTSIZE=0;source /opt/oss/manager/bin/engr_profile.sh;python ${CURRENT_PATH}/tools/get_peer_hb.py ${local_site_role}")
        else    
            (export HISTSIZE=0;source /opt/oss/manager/bin/engr_profile.sh;python ${CURRENT_PATH}/tools/get_peer_hb.py ${local_site_role})
        fi

        if [ $? -ne 0 ];then
            log_echo "ERROR" "get peer hb ip failed"
        fi
        
        if [ "X${local_site_role}" == "Xprimary" ]; then
            transform_disaster_information_to_dr "${peer_ossadm_pwd}"
        fi
        site_role=$(cat ${sitestatus_json}|sed 's/,/\n/g'|grep "\"local\""|awk -F'"' '{print $4}')
        disaster_role=${site_role}
        log_echo "INFO" "This site is ${site_role}."
    else
        log_echo "INFO" "This is not disaster."
        get_uniep_ip
        echo "local_hb_ip: ${uniep_manage_ip}" >> ${upgrade_yaml}
        echo "local_uniep_ip: ${uniep_access_ip}" >> ${upgrade_yaml}
    fi
}

function wait_for_del_dr()
{
    rm -f ${CURRENT_PATH}/check_deldr_status.json
    touch ${CURRENT_PATH}/check_deldr_status.json
    if [ "X$(whoami)" == "Xroot" ];then
        chown ossadm:ossgroup ${CURRENT_PATH}/check_deldr_status.json
    fi
    local wait_times=0
    
    while true
    do
        log_echo "INFO" "wait for remove disaster"
        execute_py_script ${CURRENT_PATH}/tools/call_ir_rest.py get_sitestatus ${CURRENT_PATH}/check_deldr_status.json
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Get sitestatus failed, please check"
            exit 1
        fi
        
        cat ${CURRENT_PATH}/check_deldr_status.json | grep "nodr" >/dev/null
        if [ $? -eq 0 ];then
            break
        fi
        
        if [ ${wait_times} -ge 900 ];then
            log_echo "ERROR" "Wait for remove dsisaster timeout, please check."
            exit 1
        fi
        
        sleep 20
        wait_times=$((wait_times+20))
    done
}

function delete_disaster()
{
    check_is_disaster
    
    grep "nodr" ${sitestatus_json} >/dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "No need to do delete_disaster"
        return 0
    fi

    if [ ${DELETE_DISASTER_CONFLICT_TIMES} -gt 5 ]; then
        log_echo "ERROR" "Execute delete DR task has been execute 5 times, but the task is still unsuccessful, please check..."
        exit 1
    fi

    log_echo "INFO" "begin to delete disaster"
    execute_py_script ${CURRENT_PATH}/tools/call_ir_rest.py delete_dr > ${CURRENT_PATH}/delete_dr_respones.txt
    if [ $? -ne 0 ];then
        ## 生产与容灾分开后，存在并发解除容灾场景,通过接口返回内容判断接口调用冲突
        cat ${CURRENT_PATH}/delete_dr_respones.txt | grep "task is being executed"
        if [ $? -eq 0 ]; then
            log_echo "WARN" "The task delete DR is conflict. will sleep 2 minutes and retry the task."
            sleep 120
            ((DELETE_DISASTER_CONFLICT_TIMES++))
            delete_disaster
        else
            ## 删除容灾失败
            log_echo "ERROR" "delete disaster failed, please check"
            exit 1
        fi
    fi
    
}

function start_product()
{
    log_echo "INFO" "begin to check start_product"
    execute_py_script ${CURRENT_PATH}/tools/call_ir_rest.py get_productstatus
    ret=$?
    if [ $ret -eq 0 ];then
        log_echo "INFO" "Product is running, no need to start"
        return 0
    elif [ $ret -eq 1 ];then
        log_echo "ERROR" "Get product status failed, please check"
        exit 1
    fi

    log_echo "INFO" "try to start_product"
    ##get_productstatus返回值101，启动产品
    if [ "X$(whoami)" == "Xroot" ];then
        su - ossadm -c "${install_path}/manager/tools/monitor/startproduct.sh -pn SOP"
    else
        ${install_path}/manager/tools/monitor/startproduct.sh -pn SOP
    fi
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute start_product failed, please check."
        exit 1
    fi
}

function bss_check()
{
    execute_py_script ${CURRENT_PATH}/tools/check_bss.py
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Check backup server get failed, for details about how to handle the error, see section FAQ in the upgrade guide."
        exit 1
    else
        log_echo "INFO" "Check backup server success"
    fi
}

function upgrade_preset()
{
    check_action "upgrade_preset"
    if [ $? -eq 0 ];then
        return 0
    fi

    ### 软件包备份目录
    local dv_upgrade_pkg_backup_dir=/opt/pub/software

    if [ ! -f "${preset_execute_path}/scan_SD_finish" ];then
        pkg_info=$(execute_py_script ${CURRENT_PATH}/tools/call_ir_rest.py show_package_info)
        echo ${pkg_info}|grep  -Po "DigitalView-SW_${dv_version}_ProductINSTALL.*?zip"
        if [ $? -ne 0 ];then
            if [ -z "$(ls ${package_path}/DigitalView-SW_${dv_version}_ProductINSTALL*.zip)" ];then
                log_echo "ERROR" "Need ProductINSTALL package, please check"
                exit 1
            fi
            
            if [ -z "$(ls ${package_path}/DigitalView-SW_${dv_version}_ProductINSTALL*.zip.p7s 2>/dev/null)" -a -z "$(ls ${package_path}/DigitalView-SW_${dv_version}_ProductINSTALL*.zip.asc 2>/dev/null)" ];then
                log_echo "ERROR" "Need ProductINSTALL package's sign file, please check"
                exit 1
            fi
            cp -f ${package_path}/DigitalView-SW_${dv_version}_ProductINSTALL*.zip* ${dv_upgrade_pkg_backup_dir}
            if [ "X$(whoami)" == "Xroot" ];then
                chown ossadm:ossgroup ${dv_upgrade_pkg_backup_dir}/DigitalView-SW_${dv_version}_ProductINSTALL*.zip*
            fi
        fi
        echo ${pkg_info}|grep -Po "DigitalView-SW_${dv_version}_ZenithDBpatch.*?tar"
        if [ $? -ne 0 ];then
            if [ -z "$(ls ${package_path}/DigitalView-SW_${dv_version}_ZenithDBpatch_*.tar)" ];then
                log_echo "ERROR" "Need ZenithDBpatch package, please check"
                exit 1
            fi
            if [ -z "$(ls ${package_path}/DigitalView-SW_${dv_version}_ZenithDBpatch_*.tar.p7s 2>/dev/null)" -a -z "$(ls ${package_path}/DigitalView-SW_${dv_version}_ZenithDBpatch_*.tar.asc 2>/dev/null)" ];then
                log_echo "ERROR" "Need ZenithDBpatch package's sign file, please check"
                exit 1
            fi
            cp -f ${package_path}/DigitalView-SW_${dv_version}_ZenithDBpatch_*.tar* ${dv_upgrade_pkg_backup_dir}
            if [ "X$(whoami)" == "Xroot" ];then
                chown ossadm:ossgroup ${dv_upgrade_pkg_backup_dir}/DigitalView-SW_${dv_version}_ZenithDBpatch_*.tar*
            fi
        fi
    fi
    
    wait_for_del_dr
    start_product
    
    grep "local_uniep_ip" ${upgrade_yaml} > /dev/null 2>&1
    if [ $? -ne 0 ];then
        get_uniep_ip
        sed -i "/local_hb_ip:/d" ${upgrade_yaml} >/dev/null 2>&1
        sed -i "/local_uniep_ip:/d" ${upgrade_yaml} >/dev/null 2>&1
        echo "local_hb_ip: ${uniep_manage_ip}" >> ${upgrade_yaml}
        echo "local_uniep_ip: ${uniep_access_ip}" >> ${upgrade_yaml}
    fi
    
    if [ "${package_path}" != "/opt/oss/manager/var/tmp" ];then
        cp -f ${package_path}/DigitalView-SW_*_ProductINSTALL*.zip* ${package_path}/DigitalView-SW_*_ZenithDBpatch_*.tar* /opt/oss/manager/var/tmp
        rm -f ${package_path}/DigitalView-SW_*_BatchPreSet*.zip*
    fi

    if [ "X$(whoami)" == "Xroot" ];then
        find /opt/oss/manager/var/tmp -type f -exec chown ossadm:ossgroup {} +
    fi

    find /opt/oss/manager/var/tmp -type f -exec chmod 750 {} +
    rm -f /opt/oss/manager/var/tmp/DigitalView-SW_*_BatchPreSet*.zip*

  
    echo "${ossadm_pwd} ossadm:passwd ${root_pwd} root:passwd" | ${CURRENT_PATH}/tools/upgrade_preset.exp "${preset_execute_path}/DV_Upgrade_BatchPreSet.sh"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute DV_Upgrade_BatchPreSet.sh failed, please check."
        exit 1
    fi
    if [ "X$(whoami)" == "Xroot" ];then
        chown ossadm:ossgroup -R ${preset_execute_path}
    fi

    ## 执行成功后，删除备份包目录
    rm -f "${dv_upgrade_pkg_backup_dir:?}"/DigitalView-SW_${dv_version}_ProductINSTALL*.zip*
    rm -f "${dv_upgrade_pkg_backup_dir:?}"/DigitalView-SW_${dv_version}_ZenithDBpatch_*.tar*


    echo "upgrade_preset: success" >> ${action_tag}
    
    if [ "${package_path}" != "/opt/oss/manager/var/tmp" ];then
        rm -f ${package_path}/DigitalView-SW_*_ProductINSTALL*.zip* ${package_path}/DigitalView-SW_*_ZenithDBpatch_*.tar*
    fi
}

function start_sd()
{
    log_echo "INFO" "Begin to start swiftDeploy service.."
    if [ "X$(whoami)" == "Xroot" ];then
        su - ossadm -c "${install_path}/swiftdeploy/SwiftDeploy/bin/start.sh restart"
        su - ossadm -c "${install_path}/swiftdeploy/SwiftDeploy/bin/start.sh status" |grep running > /dev/null 2>&1
    else
        sh ${install_path}/swiftdeploy/SwiftDeploy/bin/start.sh restart
        sh ${install_path}/swiftdeploy/SwiftDeploy/bin/start.sh status|grep running
    fi
    
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Start swiftDeploy service failed, please check"
        exit 1
    fi
    execute_sd_script "${CURRENT_PATH}/tools/call_sd_rest.py" "check_login"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Login swiftDeploy failed, please check"
        exit 1
    fi
}

function create_upgrade_task()
{
    check_action "create_upgrade_task"
    if [ $? -eq 0 ];then
        return 0
    fi
    workflow_id=$(execute_sd_script ${CURRENT_PATH}/tools/call_sd_rest.py create_task)
    echo ${workflow_id}|grep FAILED
    if [ $? -eq 0 ];then
        log_echo "ERROR" "create upgrade task failed, please check"
        exit 1
    fi
    
    echo "workflow_id: ${workflow_id}" >> ${upgrade_yaml}
    
    echo "create_upgrade_task: success" >> ${action_tag}
}

function upgrade_pre_envcheck()
{
    check_action "upgrade_pre_envcheck"
    if [ $? -eq 0 ];then
        return 0
    fi
    bss_check
    execute_sd_script ${CURRENT_PATH}/tools/call_sd_rest.py ENV_CHECK
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute_step pre ENV_CHECK failed, please check"
        exit 1
    fi
    
    echo "upgrade_pre_envcheck: success" >> ${action_tag}
}

function upgrade_bakckup()
{
    check_action "upgrade_bakckup"
    if [ $? -eq 0 ];then
        return 0
    fi

    execute_sd_script ${CURRENT_PATH}/tools/call_sd_rest.py BACKUP
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute_step BACKUP failed, please check"
        exit 1
    fi
    
    echo "upgrade_bakckup: success" >> ${action_tag}
}

function upgrade_product()
{
    check_action "upgrade_product"
    if [ $? -eq 0 ];then
        return 0
    fi

    execute_sd_script ${CURRENT_PATH}/tools/call_sd_rest.py UPGRADE
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute_step UPGRADE failed, please check"
        exit 1
    fi
    
    echo "upgrade_product: success" >> ${action_tag}
}

function upgrade_post_envcheck()
{
    check_action "upgrade_post_envcheck"
    if [ $? -eq 0 ];then
        return 0
    fi

    execute_sd_script ${CURRENT_PATH}/tools/call_sd_rest.py VERIFY
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute_step post VERIFY failed, please check"
        exit 1
    fi
    
    echo "upgrade_post_envcheck: success" >> ${action_tag}
}

function sync_dr_verify()
{
    check_action "sync_dr_verify"
    if [ $? -eq 0 ];then
        return 0
    fi

    log_echo "INFO" "Begin to sync_dr_verify"
    if [ "X$(whoami)" == "Xroot" ];then
        echo "${peer_ossadm_pwd} ossadm:passwd" | su - ossadm -c "expect ${CURRENT_PATH}/tools/sync_dr_verification.exp \"${peer_dr_heartbeat_ip}\""
    else
        echo "${peer_ossadm_pwd} ossadm:passwd" |expect ${CURRENT_PATH}/tools/sync_dr_verification.exp "${peer_dr_heartbeat_ip}"
    fi
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute sync_dr_verification failed, please check."
        exit 1
    fi
    
    echo "sync_dr_verify: success" >> ${action_tag}
}

function check_before_build_dr()
{
    rm -f ${CURRENT_PATH}/check_dr_status.json
    touch ${CURRENT_PATH}/check_dr_status.json
    if [ "X$(whoami)" == "Xroot" ];then
        chown ossadm:ossgroup ${CURRENT_PATH}/check_dr_status.json
    fi
    
    rm -f ${CURRENT_PATH}/check_dr_info.json
    touch ${CURRENT_PATH}/check_dr_info.json
    if [ "X$(whoami)" == "Xroot" ];then
        chown ossadm:ossgroup ${CURRENT_PATH}/check_dr_info.json
    fi

    local wait_times=0
    
    while true
    do
        log_echo "INFO" "check before build dr"
        execute_py_script ${CURRENT_PATH}/tools/call_ir_rest.py get_sitestatus ${CURRENT_PATH}/check_dr_status.json
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Get sitestatus failed, please check"
            exit 1
        fi
        
        execute_py_script ${CURRENT_PATH}/tools/call_ir_rest.py get_drinfo ${CURRENT_PATH}/check_dr_info.json
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Get sitestatus failed, please check"
            exit 1
        fi


        cat ${CURRENT_PATH}/check_dr_status.json |grep deleting
        if [ $? -eq 0 ];then
            # 正在删除容灾
            log_echo "INFO" "DR relationship is deleting"
            sleep 20
            continue
        fi

        cat ${CURRENT_PATH}/check_dr_status.json |grep building
        if [ $? -eq 0 ];then
            # 正在创建容灾
            log_echo "INFO" "DR relationship is creating"
            sleep 20
            continue
        fi

        cat ${CURRENT_PATH}/check_dr_status.json | sed 's#,#\n#g'|grep secondary|grep -E 'standby|active'
        if [ $? -eq 0 ];then
            # 已有容灾关系
            log_echo "INFO" "There's already a disaster relationship."
            clean_config_yaml
            exit 0
        fi


        cat ${CURRENT_PATH}/check_dr_info.json|grep drid
        dr_status=$?

        nodr_count=$(cat ${CURRENT_PATH}/check_dr_status.json | sed 's#,#\n#g'|grep -c nodr)
        if [ ${nodr_count} -eq 3 ];then
            if [ $dr_status -ne 0 ];then
                # 没有容灾
                return 0
            else
                # uniep有容灾 但没有增加产品 需先删除容灾关系
                execute_py_script ${CURRENT_PATH}/tools/call_ir_rest.py delete_dr
            fi
        fi

        if [ ${wait_times} -ge 900 ];then
            log_echo "ERROR" "check before build dr timeout."
            exit 1
        fi
        
        sleep 20
        wait_times=$((wait_times+20))
    done
}

function build_disaster()
{
    check_before_build_dr

    peer_dr_heartbeat_ip=$(cat  ${CURRENT_PATH}/upgrade.yaml|grep "peer_dr_heartbeat_ip"|awk -F'peer_dr_heartbeat_ip:' '{print $2}'|awk -F',' '{print $1}'|sed 's/ //g')

    if [ -f ${install_path}/pr_upgrade.yaml ]; then
        mv -f ${install_path}/pr_upgrade.yaml ${CURRENT_PATH}
        mv -f ${install_path}/drinfo_bak.json ${CURRENT_PATH}
    fi

    ## 若在生产解除容灾，需要从${CURRENT_PATH}/pr_upgrade.yaml中获取对端ip
    if [ -f ${CURRENT_PATH}/pr_upgrade.yaml ]; then
        peer_dr_heartbeat_ip=$(cat  ${CURRENT_PATH}/pr_upgrade.yaml|grep "local_hb_ip"|awk -F'local_hb_ip:' '{print $2}'|awk -F',' '{print $1}'|sed 's/ //g')
    fi
    
    ## 仅能获取到生产站点ip的条件下检查生产是否升级完成
    if [ -n "${peer_dr_heartbeat_ip}" ]; then
        cleanup_know_host
        check_pr_upgrade_complete "${peer_dr_heartbeat_ip}" "${peer_ossadm_pwd}"
    fi

    sync_dr_verify

    execute_py_script ${CURRENT_PATH}/tools/call_ir_rest.py build_dr
    if [ $? -ne 0 ];then
        log_echo "ERROR" "build disaster failed, please check"
        exit 1
    fi
   
}
function rollback_product()
{
    check_action "rollback_product"
    if [ $? -eq 0 ];then
        return 0
    fi
    
    execute_sd_script ${CURRENT_PATH}/tools/call_sd_rest.py ROLLBACK
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute_step ROLLBACK failed, please check"
        exit 1
    fi
    
    echo "rollback_product: success" >> ${action_tag}
}

function delete_version_in_record()
{
    version=$(cat /opt/oss/dv_autoupgrade.ini_last | grep "dv_version" | awk -F '=' '{print $2}')
    sed -i "/${version}/d" /opt/oss/CD_upgrade_record
}

function rollback_preset()
{
    check_action "rollback_preset"
    if [ $? -eq 0 ];then
        return 0
    fi

    echo "${ossadm_pwd} ossadm:passwd ${root_pwd} root:passwd" | expect ${CURRENT_PATH}/tools/upgrade_preset.exp "${preset_execute_path}/DV_Preset_Rollback.sh"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute DV_Preset_Rollback.sh failed, please check."
        exit 1
    fi
    echo "rollback_preset: success" >> ${action_tag}
    
    mv -f ${action_tag} ${action_tag}_last
    mv -f /opt/oss/dv_autoupgrade.ini_finish /opt/oss/dv_autoupgrade.ini_last
    rm -f /opt/oss/dv_autoupgrade.ini

    delete_version_in_record
}

function clean_config_yaml()
{
    if [ -f "${config_yaml_path}" ];then
        sed -i "s/_pwd:.*/_pwd: /g" ${config_yaml_path}
    fi
}

function add_version_to_record()
{
    version=$(cat /opt/oss/dv_autoupgrade.ini|grep "dv_version="|awk -F'=' '{print $2}')
    echo "dv_version=${version}, full" >> /opt/oss/CD_upgrade_record
    if [ "X$(whoami)" == "Xroot" ]; then
        chown ossadm: /opt/oss/CD_upgrade_record
    fi
}

function main()
{
    read -t 10 -sr params
    log_echo "INFO" "The upgrade type is full"
    init

    case "${step_name}" in
    'disaster_remove')
        ## 容灾解除任务冲突次数，超过5次报错退出
        DELETE_DISASTER_CONFLICT_TIMES=1

        delete_disaster

        start_product
        ;;
    'full_upgrade')
        upgrade_preset

        start_sd

        create_upgrade_task
    
        upgrade_pre_envcheck
        
        upgrade_bakckup
        
        upgrade_product
        
        upgrade_post_envcheck

        add_version_to_record

        mv -f /opt/oss/dv_autoupgrade.ini /opt/oss/dv_autoupgrade.ini_finish
        ;;
    'precheck')
        upgrade_preset
        
        start_sd
        
        create_upgrade_task
        
        upgrade_pre_envcheck
        ;;
    'backup')
        start_sd
        upgrade_bakckup
        ;;
    'upgrade')
        start_sd

        upgrade_product
    
        upgrade_post_envcheck

        add_version_to_record

        touch ${CURRENT_PATH}/product_upgrade.finish
        if [ "X$(whoami)" == "Xroot" ]; then
            chown ossadm: ${CURRENT_PATH}/product_upgrade.finish
        fi

        mv -f /opt/oss/dv_autoupgrade.ini /opt/oss/dv_autoupgrade.ini_finish
        ;;
    'disaster_rebuild')
        grep "local_uniep_ip" ${upgrade_yaml} > /dev/null 2>&1
        if [ $? -ne 0 ];then
            get_uniep_ip
            sed -i "/local_hb_ip:/d" ${upgrade_yaml} >/dev/null 2>&1
            sed -i "/local_uniep_ip:/d" ${upgrade_yaml} >/dev/null 2>&1
            echo "local_hb_ip: ${uniep_manage_ip}" >> ${upgrade_yaml}
            echo "local_uniep_ip: ${uniep_access_ip}" >> ${upgrade_yaml}
        fi
        build_disaster
        ;;
    'rollback')
        start_sd

        rollback_product

        rm -f "${CURRENT_PATH:?}"/product_upgrade.finish

        rollback_preset
        ;;
    *)
        log_echo "ERROR" "The upgrade_step in config.yaml is incorrect, please check"
        exit 1
        ;;
    esac
    
    clean_config_yaml
}

main