#!/bin/bash
CURRENT_PATH=$(cd $(dirname $0); pwd)
LOG_FILE="${CURRENT_PATH}/execute_upgrade_swift_deploy.log"
install_path="/opt/oss"
sd_install_path="/opt/oss/swiftdeploy"
backup_path="/home/<USER>/backup_sd_old_version_pkg"
swiftdeploy_pkg_path="/home/<USER>/swiftdeployPkg"
upgrade_swiftdeploy_path="/home/<USER>/upgrade_swiftdeploy"
execute_user=$(whoami)
if [ "X${execute_user}" != "Xossadm" ];then
    echo "[ERROR] The execute user is ${execute_user}, need ossadm execute it."
    exit 1
fi

echo "" > $LOG_FILE

function log_echo()
{
    log_level=INFO
    if [ $# -ge 2 ]; then
        log_level=$1
        shift
    fi
    log_level=$(echo ${log_level} | tr 'a-z' 'A-Z')
    log_time=$(date +'%Y-%m-%d %H:%M:%S')
    echo "${log_time} [${log_level}] $*" >> $LOG_FILE
    if [ "X${log_level}" == "XINFO" ]; then
        echo -e "\033[49;32m[INFO][${log_time}]\033[0m $*" 
    elif [ "X${log_level}" == "XWARN" ]; then
        echo -e "\033[49;33m[WARN][${log_time}]\033[0m $*" 
    elif [ "X${log_level}" == "XERROR" ]; then
        echo -e "\033[49;31m[ERROR][${log_time}]\033[0m $*" 
    else
        echo -e "\033[49;31m[${log_level}][${log_time}]\033[0m $*"
    fi
}

function is_empty()
{
    local tmp_key="$1"
    local tmp_value="$2"
    if [ -z "${tmp_value}" ];then
        log_echo "ERROR" "The ${tmp_key} value is null."
        exit 1
    fi
}

function path_not_exists()
{
    local path="$1"
    local log_type="$2"
    if [ ! -d ${path} ];then
        log_echo "${log_type}" "The ${path} is not exist."
        [ "${log_type}" == "ERROR" ] && exit 1 || return 1
    fi
    log_echo "INFO" "The ${path} is exist."
    return 0
}

function upgrade()
{
    log_echo "INFO" "upgrade SwiftDeploy start..."
    ##swiftdeployPkg.txt文件是升级预置的时候生成的，如果没有 swiftdeployPkg.txt ，表示还没做升级预置，不需要升级SD。 
    if [ ! -f ${upgrade_swiftdeploy_path}/swiftdeployPkg.txt ];then
        log_echo "INFO" "The ${upgrade_swiftdeploy_path}/swiftdeployPkg.txt is not exists.not need upgrade it."
        return 0
    fi
    ## swiftdeployPkg.txt 获取文件中升级预置记录的Uniep的包路径，sd的包路径。 
    SwiftDeployPkgPath=$(cat ${upgrade_swiftdeploy_path}/swiftdeployPkg.txt |grep "SwiftDeployPkgPath=" |awk -F'=' '{print $2}')
    UniEPMgrPkgPath=$(cat ${upgrade_swiftdeploy_path}/swiftdeployPkg.txt |grep "UniEPMgrPkgPath="|awk -F'=' '{print $2}')
    ## 对获取到的值 是否为空检查 
    is_empty "SwiftDeployPkgPath" "${SwiftDeployPkgPath}"
    is_empty "UniEPMgrPkgPath" "${UniEPMgrPkgPath}"
    ## 检查路径是否存在。 
    path_not_exists "${SwiftDeployPkgPath}" "ERROR"
    path_not_exists "${UniEPMgrPkgPath}" "ERROR"
    ## 升级SD前需要先停SD
    log_echo "INFO" "stop SwiftDeploy ..."
    sh ${sd_install_path}/SwiftDeploy/bin/start.sh stop
    if [ $? -ne 0 ];then
        log_echo "ERROR" "stop SwiftDeploy failed."
        exit 1
    fi
    ## 升级前需要先备份SD
    log_echo "INFO" "backup SwiftDeploy ..."
    ## 获取要升级的SD版本目录。
    local new_swiftdeploy_pkg=$(basename ${SwiftDeployPkgPath}/SwiftDeploy*.zip |sed "s/.zip$//g")
    is_empty "new_swiftdeploy_pkg" "${new_swiftdeploy_pkg}"
    ## 获取要备份的SD版本目录。
    old_sd_pkg=$(basename ${sd_install_path}/SwiftDeploy-*)
    is_empty "old_sd_pkg" "${old_sd_pkg}"
    ## 如果老版本和新版本是一样的，表示同版本，不需要备份。例如升级成功，然后重入执行，同版本就不需要备份了。
    if [ "${old_sd_pkg}" == "${new_swiftdeploy_pkg}" ];then
        log_echo "INFO" "The old_sd_pkg=${old_sd_pkg} and new_swiftdeploy_pkg=${new_swiftdeploy_pkg} is same.not need backup it."
    else
        ## 老版本和新版本不同时，先清理备份目录。然后创建，然后拷贝老版本的目录到备份目录下。进行备份。
        [ -d /home/<USER>/backup_sd_old_version_pkg ] && rm -rf /home/<USER>/backup_sd_old_version_pkg
        mkdir -p ${backup_path}
        cp -rpf ${sd_install_path}/${old_sd_pkg} ${backup_path}/
        if [ $? -ne 0 ];then
            log_echo "ERROR" "backup SwiftDeploy failed.execute cmd:[ cp -rpf ${sd_install_path}/${old_sd_pkg} ${backup_path}/ ] failed."
            exit 1
        fi
    fi
    ## 开始升级SD 
    log_echo "INFO" "upgrade SwiftDeploy ..."
    source ${install_path}/manager/bin/engr_profile.sh
    ## 由于升级SD包不管升级成功还是失败，一旦触发升级SD，SD的包会被平台删除，所以每次需要从升级预置解压的路径下拷贝一份。
    cp -rpf ${SwiftDeployPkgPath}/SwiftDeploy*.zip* ${swiftdeploy_pkg_path}/
    chmod 640 ${swiftdeploy_pkg_path}/SwiftDeploy*.zip*
    ## 获取要升级的包所在完整路径。并开始升级，SD包依赖Uniep的包，需要传uniep的包解压后的路径。SD会从uniep中获取相关依赖的jar包。
    local swiftdeploy_pkg=$(ls ${swiftdeploy_pkg_path}/SwiftDeploy*.zip)
    log_echo "INFO" "${install_path}/manager/agent/bin/ipmc_tool -cmd sd -o install -pkgpath ${swiftdeploy_pkg} -installdiskpath ${UniEPMgrPkgPath}"
    ${install_path}/manager/agent/bin/ipmc_tool -cmd sd -o install -pkgpath ${swiftdeploy_pkg} -installdiskpath ${UniEPMgrPkgPath}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute cmd:[ ${install_path}/manager/agent/bin/ipmc_tool -cmd sd -o install -pkgpath ${swiftdeploy_pkg} -installdiskpath ${UniEPMgrPkgPath} ] failed."
        exit 1
    fi
    
    log_echo "INFO" "custom_upgrade_guide_workflow start..."
    local upgrade_guide_workflow=/opt/oss/swiftdeploy/SwiftDeploy/etc/workflow/upgrade_guide_workflow.xml
    if [ -f ${upgrade_guide_workflow} ];then
        sed -i "/start_product/d" ${upgrade_guide_workflow}
        log_echo "INFO" "delete start_product,ret=$?"
    else
        log_echo "INFO" "The upgrade_guide_workflow=${upgrade_guide_workflow} is not exists."
    fi
    
    ## 升级后，开始启动SD
    log_echo "INFO" "start SwiftDeploy ..."
    sh ${sd_install_path}/SwiftDeploy/bin/start.sh start
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute cmd:[ sh ${sd_install_path}/SwiftDeploy/bin/start.sh start ] failed."
        exit 1
    fi
    log_echo "INFO" "rm -f ${swiftdeploy_pkg_path}/SwiftDeploy*.zip"
    rm -f ${swiftdeploy_pkg_path}/SwiftDeploy*.zip >> $LOG_FILE 2>&1
    log_echo "INFO" "upgrade SwiftDeploy successfully."
}

function rollback()
{
    log_echo "INFO" "rollback SwiftDeploy start..."
    ## 检查备份路径下是否存在备份，不存在备份无法回退，报错退出
    path_not_exists "${backup_path}" "INFO"
    if [ $? -ne 0 ];then
        log_echo "INFO" "The backup file is not exists.not need rollback."
        return 0
    fi
    old_sd_pkg=$(ls ${backup_path} |sort |tail -1)
    is_empty "old_sd_pkg" "${old_sd_pkg}"
    ## 恢复前，先停SD 
    log_echo "INFO" "stop SwiftDeploy ..."
    sh ${sd_install_path}/SwiftDeploy/bin/start.sh stop
    if [ $? -ne 0 ];then
        log_echo "ERROR" "stop SwiftDeploy failed."
        exit 1
    fi
    ## 恢复前，先清理当前安装路径下的SD，因为SD下有只读文件，直接删除无法删除，需要先修改权限750，然后再删除。 
    log_echo "INFO" "clean up SwiftDeploy ..."
    chmod -R 750 ${sd_install_path}/SwiftDeploy-*
    rm -f ${sd_install_path}/SwiftDeploy
    rm -rf ${sd_install_path}/SwiftDeploy-*
    if [ $? -ne 0 ];then
        log_echo "ERROR" "clean up SwiftDeploy failed."
        exit 1
    fi
    ## 开始从备份中拷贝旧版本进行回退。
    log_echo "INFO" "rollback SwiftDeploy ..."
    cp -rpf ${backup_path}/${old_sd_pkg} ${sd_install_path}/
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute cmd:[ cp -rpf ${backup_path}/${old_sd_pkg} ${sd_install_path}/ ] failed."
        exit 1
    fi
    ## 回退后，需要重建软连接。
    ln -s ${sd_install_path}/${old_sd_pkg} ${sd_install_path}/SwiftDeploy
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute cmd:[ ln -s ${sd_install_path}/${old_sd_pkg} ${sd_install_path}/SwiftDeploy ] failed."
        exit 1
    fi
    ## 恢复后，启动。
    log_echo "INFO" "start SwiftDeploy ..."
    sh ${sd_install_path}/SwiftDeploy/bin/start.sh start
    if [ $? -ne 0 ];then
        log_echo "ERROR" "start SwiftDeploy failed."
        exit 1
    fi
    ## 恢复成功完成后，清理掉备份，同样，备份中SD下有只读文件，直接删除无法删除，需要先修改权限750，然后再删除
    log_echo "INFO" "clean up backup SwiftDeploy ..."
    chmod -R 750 /home/<USER>/backup_sd_old_version_pkg
    rm -rf /home/<USER>/backup_sd_old_version_pkg
    log_echo "INFO" "rollback SwiftDeploy successfully."
}

function main()
{
    local operation="$1"
    ## 校验参数，只支持升级和回退。
    if [ -z "$(echo ${operation} | grep -wE 'upgrade|rollback')" ];then
        log_echo "ERROR" "Only support [ upgrade_swift_deploy.sh upgrade ] or [ upgrade_swift_deploy.sh rollback ].If the parameter is incorrect, check and modify it."
        exit 1
    fi
    log_echo "INFO" "The operation=${operation}"
    ## 根据参数不同，执行不同操作
    if [ "${operation}" == "upgrade" ];then
        upgrade
    elif [ "${operation}" == "rollback" ];then
        rollback
    else
        log_echo "ERROR" "This is else.check operation"
        exit 1
    fi
}

main "$@"
