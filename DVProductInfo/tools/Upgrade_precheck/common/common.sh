#!/bin/bash
set +x
main_path=$1
common_path=${main_path}/common
exe_log_file=${main_path}/precheck.log
nodelists_file=/opt/oss/manager/etc/sysconf/nodelists.json
tmp_dir=/tmp/precheck
feature_properties_path=${common_path}/feature.properties
result_file=${common_path}/result

cat /etc/ssh/sshd_config 2> /dev/null| grep -w '^Port' | grep -v '#' > /dev/null 2>&1
if [ $? -ne 0 ];then
    ssh_port=22
else
    ssh_port=$(cat /etc/ssh/sshd_config | grep -w "^Port" | grep -v "#" |awk -F 'Port' '{print $2}' | sed s/[[:space:]]//g)
fi

> ${exe_log_file}
chmod -R 755 ${main_path}
chmod 777 ${exe_log_file}

function log_echo()
{
    local log_level=INFO
    if [ $# -ge 2 ]; then
        log_level=$1
        shift
    fi
    if [ ! -f ${exe_log_file} ];then
        touch ${exe_log_file}
    fi
    log_level=$(echo ${log_level} | tr 'a-z' 'A-Z')
    [ -z "${hostname}" ] && hostname="local"
    local log_date_time=$(date +'%Y-%m-%d %H:%M:%S')
    echo "${log_date_time} [${log_level}] [${hostname}] $*" >> ${exe_log_file}
    if [ "X${log_level}" == "XINFO" ]; then
        echo -e "\033[49;32m[INFO][${log_date_time}]\033[0m [${hostname}] $*"
    elif [ "X${log_level}" == "XWARN" ]; then
        echo -e "\033[49;33m[WARN][${log_date_time}]\033[0m [${hostname}] $*"
    elif [ "X${log_level}" == "XERROR" ]; then
        echo -e "\033[49;31m[ERROR][${log_date_time}]\033[0m [${hostname}] $*"
    else
        echo -e "[${log_level}][${log_date_time}] [${hostname}] $*"
    fi
}

function check_uniep()
{
    ps -ef | grep "uniepservice-0-0\|uniepliteservice-0-0" |grep -v grep > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Can't find Uiep -Dprocname=uniepservice-0-0 process, We need run this at UniEp node, please check ..."
        exit 1
    fi
}

function get_all_host()
{
    if [ ! -f ${nodelists_file} ];then
        log_echo "ERROR" "The ${nodelists_file} not exist, can't get nodes IP"
        exit 1
    fi

    local py_getip=${common_path}/getNodeIPList.py
    node_ip_list=$(su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python ${py_getip} ${nodelists_file} ${exe_log_file} 'get_host_ip' ")
    if [ "X${node_ip_list}" == "XERROR" ];then
        log_echo "ERROR" "execute [source /opt/oss/manager/bin/engr_profile.sh;python ${py_getip} ${nodelists_file} ${exe_log_file}] get nodes ip failed, please check"
        exit 1
    fi
    node_ip_list=$(echo "${node_ip_list}" | sed 's/^[ \t]*//g' | sed 's/[ \t]*$//g')
    ip_list=""
    for node_ip in ${node_ip_list}
    do
        local ip=${node_ip#*@}
        ip_list="${ip_list} ${ip}"
    done
}

function get_node_pwd_list()
{
    ## get all node_ip node_hostname node_pwd
    get_all_host
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Can't get nodes IP"
        exit 1
    fi

    local time=0
    host_ip_pwd_list=""
    for host_ip in ${node_ip_list}
    do
        local ssh_ip=$(echo ${host_ip} | awk -F '@' '{print $2}')

        local is_local_ip=$(ip addr |grep -iFw "${ssh_ip}")
        if [ -n "${is_local_ip}" ];then
            continue
        fi

        get_root_pwd ${ssh_ip}
        host_ip_pwd_list="${host_ip_pwd_list} ${host_ip}@${rootpwdReturn}"
    done
}

function scp_package()
{
    rm -rf /tmp/precheck
    mkdir -p ${tmp_dir}
    cp -rpf ${common_path} ${tmp_dir}
    cp -rpf ${main_path}/config ${tmp_dir}
    cp -rpf ${main_path}/feature ${tmp_dir}
    chmod 755 -R ${tmp_dir}

    for host_ip_pwd in ${host_ip_pwd_list};
    do
        sim_ssh "${host_ip_pwd}" "rm -rf /tmp/precheck"

        sim_scp "${host_ip_pwd}" "${tmp_dir}" "${tmp_dir}"
    done
}

function is_check_in_node()
{
    local target_hostname=$1
    local local_hostname=$hostname

    if [ "${local_hostname}" == "DVPrimaryNode" ] || [ "${local_hostname}" == "DVSecondaryNode" ];then
        if [ "${target_hostname}" == "UNIEP" ] || [ "${target_hostname}" == "APP" ] || [ "${target_hostname}" == "DB" ];then
            return 0
        else
            return 1
        fi
    fi

    if [[ "${local_hostname^^}" =~ "${target_hostname^^}" ]];then
        return 0
    else
        return 1
    fi
}

function exp_product_info()
{
    local exp_path="$1"
    if [ -z "${exp_path}" ];then
        log_echo "ERROR" "The exp_path=${exp_path} is null. please check..."
        exit 1
    fi

    if [ -d ${exp_path} ];then
        exp_path=$(cd ${exp_path}; pwd)
        if [ "X${exp_path}" !=  "X/" ];then
            rm -rf ${exp_path}
        fi
    fi

    mkdir -p ${exp_path}
    chown ossadm:ossgroup ${exp_path}
    chmod 750 ${exp_path}

    su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;sh /opt/oss/manager/tools/resmgr/queryproduct.sh -pn SOP -output ${exp_path}" > /dev/null 2>&1

    if [ ! -f ${exp_path}/nodes*.json -o ! -f ${exp_path}/product*.json ];then
        log_echo "ERROR" "Export product info by uniep failed,of ${exp_path} please check..."
        exit 1
    fi
}

function get_network_type()
{
    local exp_product_path=${common_path}/expInfo
    mkdir -p "${exp_product_path}"
    exp_product_info "${exp_product_path}"

    ## "NetworkType":"MergeCluster",
    cat ${exp_product_path}/product*.json | grep -w "\"NetworkType\":[[:space:]]*\"MiniCluster\"" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        netWorkType="M"
        return 0
    fi
    cat ${exp_product_path}/nodes*.json | grep "SOP-OM-Global" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        cat ${exp_product_path}/nodes*.json | grep "OMMHA" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            if [ -n "`grep \"ALL_IN_ONE_NODE\" ${exp_product_path}/nodes*.json`" ];then
                netWorkType="O"
            else
                netWorkType="T"
            fi
        else
            if [ -n "`grep \"SMClusterNode\" ${exp_product_path}/nodes*.json`" ];then
                netWorkType="L"
            else
                netWorkType="C"
            fi
        fi
    else
        log_echo "ERROR" "Can't find 'SOP-OM-Global' in ${exp_product_path}/nodes*.json, please check..."
        exit 1
    fi
}