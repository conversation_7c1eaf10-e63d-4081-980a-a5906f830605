# -*- coding:utf-8 -*-
import os.path
import sys
import time
import json
import stat

script_dir = os.path.dirname(os.path.abspath(__file__))
flags = os.O_RDWR | os.O_CREAT
modes = stat.S_IWUSR | stat.S_IRUSR
log_file = os.fdopen(os.open(os.path.join(script_dir, 'handlejson.log'), flags, modes), 'a+')
json_to_handle = sys.argv[1]


def get_key_value(key_value, spilt_num):     
    key_value_list = key_value.split()
    if spilt_num > len(key_value_list):
        log_echo("Warning", "Get key value failed")
        return ""       
    return key_value_list[spilt_num].split("pwd:")[1]  
    

def log_echo(logtype="INFO", logstr=""):
    timestr = str(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()))
    try:
        if log_file:
            log_file.write("[" + logtype + "][" + timestr + "]:" + logstr + "\n")
    except Exception as e:
        log_echo("ERROR", "write " + str(os.path.join(script_dir, 'handlejson.log')) + " failed: %s" % str(e))
        log_file.close()
        print("ERROR")
        sys.exit(-1)


def read_json(file_name):
    try:
        file_realpath = os.path.realpath(file_name)
        with open(file_realpath, encoding="utf-8") as f:
            json_obj = json.load(f)
    except Exception as e:
        log_echo("ERROR", "Read " + file_realpath + " failed: %s" % str(e))
        print("ERROR")
        sys.exit(-1)
    return json_obj


def write_json(filepath, json_data):
    file_realpath = os.path.realpath(filepath)

    with os.fdopen(os.open(file_realpath, flags, modes), 'w') as f:
        f.seek(0)
        f.truncate()
        json.dump(json_data, f, indent=4)
    f.close()


def handle_product():
    exp_product_json = sys.argv[2]
    new_product_json = sys.argv[3]
    config_str = sys.argv[4]
    config_list = config_str.split(" ")

    exp_json_load = read_json(exp_product_json)
    new_json_load = read_json(new_product_json)

    for item in config_list:
        temp_value = new_json_load["productext"][item]
        exp_json_load["productext"][item] = temp_value

    new_json_path = os.path.realpath(os.path.join(script_dir, "newjson/product_sop.json"))
    write_json(new_json_path, exp_json_load)


def handle_productpwd():
    json_path = os.path.join(script_dir, "newjson/product_sop.json")
    pwd_key = sys.argv[2]
    key_value = sys.stdin.read()
    pwd_value = get_key_value(key_value, 0)

    json_load = read_json(json_path)

    json_load["productext"][pwd_key] = pwd_value

    write_json(json_path, json_load)


def get_nodetype():
    node_json = sys.argv[2]
    nodename = sys.argv[3]

    json_load = read_json(node_json)
    nodetype = ""
    for nodeinfo in json_load["hostlist"]:
        tmp_nodename = nodeinfo["nodename"]
        if tmp_nodename == nodename:
            nodetype_list = nodeinfo["nodetype"]
            nodetype = ",".join(nodetype_list)
            break

    if nodetype:
        print(nodetype)
    else:
        log_echo("ERROR", "nodetype List is null with nodename: " + nodename)
        print("ERROR")


# 该函数不建议使用在APP或DB的nodetype上
def get_node_num_by_nodetype():
    node_json = sys.argv[2]
    nodename = sys.argv[3]

    json_load = read_json(node_json)
    node_num = 0
    for nodeinfo in json_load["hostlist"]:
        if nodename != "APP" and nodename != "DB":
            if nodename in nodeinfo["nodetype"]:
                node_num += 1
    print(node_num)


def get_nodetag():
    node_json = sys.argv[2]
    nodemgrip = sys.argv[3]

    json_load = read_json(node_json)

    for ipgroup in json_load["hostlist"]:
        mgrip = ipgroup["nodemgrip"]
        if mgrip == nodemgrip:
            nodetag = ipgroup["nodeext"]["nodetag"]
            break

    if nodetag:
        print(nodetag)
    else:
        log_echo("ERROR", "nodetag List is null with ip: " + nodemgrip)
        print("ERROR")
        sys.exit(1)


def getnodetag_of_nodename():
    node_json = sys.argv[2]
    nodename = sys.argv[3]
    nodetag = ""

    json_load = read_json(node_json)

    for node_info in json_load["hostlist"]:
        tmp_nodename = node_info["nodename"]
        if tmp_nodename == nodename:
            nodetag = node_info["nodeext"]["nodetag"]
            break

    if nodetag:
        print(nodetag)
    else:
        log_echo("ERROR", "nodetag List is null with nodename: " + nodename)
        print("ERROR")
        sys.exit(1)


def get_nodename():
    node_json = sys.argv[2]
    nodemgrip = sys.argv[3]

    json_load = read_json(node_json)

    for ipgroup in json_load["hostlist"]:
        mgrip = ipgroup["nodemgrip"]
        if mgrip == nodemgrip:
            nodename = ipgroup["nodename"]
            break

    if nodename:
        print(nodename)
    else:
        log_echo("ERROR", "nodename is null with ip: " + nodemgrip)
        print("ERROR")
        sys.exit(1)


def get_all_nodename():
    node_json = sys.argv[2]
    json_load = read_json(node_json)
    all_nodename_list = ""
    for hostinfo in json_load["hostlist"]:
        nodename = hostinfo["nodename"]
        if nodename:
            if all_nodename_list:
                all_nodename_list = all_nodename_list + "," + nodename
            else:
                all_nodename_list = nodename
            
    if all_nodename_list:
        print(all_nodename_list)
    else:
        log_echo("ERROR", "all_nodename_list: " + all_nodename_list)
        print("ERROR")


def handle_nodetype():
    nodename = sys.argv[2]
    new_nodetype = sys.argv[3]
    new_nodetype_list = new_nodetype.split(",")
    try:
        new_node_json = sys.argv[4]
    except IndexError:
        new_node_json = os.path.join(script_dir, "newjson/node_sop.json")

    new_json_load = read_json(new_node_json)

    for nodeinfo in new_json_load["hostlist"]:
        tmp_nodename = nodeinfo["nodename"]
        if tmp_nodename == nodename:
            new_json_load["hostlist"].remove(nodeinfo)
            nodeinfo["nodetype"] = new_nodetype_list
            new_json_load["hostlist"].append(nodeinfo)
            break
    write_json(new_node_json, new_json_load)


def handle_nodetag():
    nodemgrip = sys.argv[2]
    new_nodetag = sys.argv[3]
    try:
        new_node_json = sys.argv[4]
    except IndexError:
        new_node_json = os.path.join(script_dir, "newjson/node_sop.json")

    new_json_load = read_json(new_node_json)

    for ipgroup in new_json_load["hostlist"]:
        mgrip = ipgroup["nodemgrip"]
        if mgrip == nodemgrip:
            new_json_load["hostlist"].remove(ipgroup)
            ipgroup["nodeext"]["nodetag"] = new_nodetag
            new_json_load["hostlist"].append(ipgroup)
            break
    write_json(new_node_json, new_json_load)


def update_nodetag_of_nodename():
    nodename = sys.argv[2]
    new_nodetag = sys.argv[3]
    try:
        new_node_json = sys.argv[4]
    except IndexError:
        new_node_json = os.path.join(script_dir, "newjson/node_sop.json")

    new_json_load = read_json(new_node_json)

    for node_info in new_json_load["hostlist"]:
        tmp_nodename = node_info["nodename"]
        if tmp_nodename == nodename:
            new_json_load["hostlist"].remove(node_info)
            node_info["nodeext"]["nodetag"] = new_nodetag
            new_json_load["hostlist"].append(node_info)
            break
    write_json(new_node_json, new_json_load)


def handle_pvalue():
    node_json_path = sys.argv[2]
    p_value = sys.argv[3]
    json_load = read_json(node_json_path)
    tmp_jsonload = read_json(node_json_path)

    for ipgroup in json_load["hostlist"]:
        tmp_jsonload["hostlist"].remove(ipgroup)
        ipgroup["uvalue"] = "ossadm"
        ipgroup["pvalue"] = p_value
        tmp_jsonload["hostlist"].append(ipgroup)

    write_json(node_json_path, tmp_jsonload)


def productext_modify():
    product_json = sys.argv[2]
    modify_key = sys.argv[3]
    modify_value = sys.argv[4]

    json_load = read_json(product_json)

    json_load["productext"][modify_key] = modify_value

    write_json(product_json, json_load)


def map_has_key(map_data, key):
    if key in map_data.keys():
        return True
    return False


def productext_delete():
    product_json = sys.argv[2]
    delete_key = sys.argv[3]

    json_load = read_json(product_json)

    if map_has_key(json_load["productext"], delete_key):
        json_load["productext"].pop(delete_key)

    write_json(product_json, json_load)


def getinfo_product():
    jsonfile_export = sys.argv[2]
    config_name = sys.argv[3]
    try:
        json_load = read_json(jsonfile_export)
        config_value = json_load["productext"].get(config_name, "")
        print(config_value)
    except OSError as e:
        log_echo("ERROR", "Get value from  " + jsonfile_export + " failed: %s" % str(e))
        print("ERROR")
        sys.exit(-1)


def add_nodetag_by_nodename():
    product_json = sys.argv[2]
    nodename = sys.argv[3]
    tag_name = sys.argv[4]

    new_json_load = read_json(product_json)
    for node_info in new_json_load["hostlist"]:
        if node_info["nodename"] == nodename.strip():
            tag_list = node_info["nodeext"]["nodetag"].split(",")
            if tag_name in tag_list:
                break
            else:
                tag_list.append(tag_name)
                node_info["nodeext"]["nodetag"] = ",".join(tag_list)
                write_json(product_json, new_json_load)
                break
    else:
        print("The nodename does not exsit")
        sys.exit(-1)


def del_nodetag_by_nodename():
    product_json = sys.argv[2]
    nodename = sys.argv[3].strip()
    tag_name = sys.argv[4].strip()

    new_json_load = read_json(product_json)
    for node_info in new_json_load["hostlist"]:
        if node_info["nodename"] == nodename.strip():
            tag_list = node_info["nodeext"]["nodetag"].split(",")
            if tag_name in tag_list:
                tag_list.remove(tag_name)
                node_info["nodeext"]["nodetag"] = ",".join(tag_list)
                write_json(product_json, new_json_load)
                break
            else:
                break
    else:
        print("The nodename does not exsit")


def add_nodetype_by_nodename():
    product_json = sys.argv[2]
    nodename = sys.argv[3].strip()
    type_name = sys.argv[4].strip()

    new_json_load = read_json(product_json)
    for node_info in new_json_load["hostlist"]:
        if node_info["nodename"] == nodename.strip():
            type_list = node_info["nodetype"]
            if type_name in type_list:
                return    
            else:
                type_list.append(type_name)
                write_json(product_json, new_json_load)
                break
    else:
        print("The nodename does not exsit")


def get_maintenance_ipaddr():
    nodelist_json = sys.argv[2].strip()
    nodename = sys.argv[3].strip()

    nodelist_load = read_json(nodelist_json)
    for node in nodelist_load["nodeList"].values():
        if node['hostname'] == nodename:
            return [ipaddr for ipaddr in node['IPAddresses'] if "maintenance" in ipaddr['usage']][0]
    else:
        print("The nodename does not exsit")
        return 1


def get_maintenance_mask():
    print(get_maintenance_ipaddr()["mask"])
    

def get_maintenance_ip():
    print(get_maintenance_ipaddr()["IP"])


def handle_scanret():
    scanret_json = sys.argv[2].strip()
    scanret_load = read_json(scanret_json)
    for pkg_ret in scanret_load.get("result", []):
        if pkg_ret.get("resultcode") not in [108, 0]:
            return 1
    return 0


def get_ip_list(node_map):
    ip_addresses = None
    if "IPAddresses" not in node_map.keys():
        log_echo("ERROR", "nodeMap not has IPAddresses.")
        return "ERROR"
    
    ip_addresses = node_map["IPAddresses"]
    if not ip_addresses:
        log_echo("ERROR", "IPAddresses is None.")
        return "ERROR"
    
    ip_str_list = ""
    log_echo("INFO", "IPAddresses is = " + str(ip_addresses))
    for ip_map in ip_addresses:
        log_echo("INFO", "ipMap=" + str(ip_map)) 
        if "IP" in ip_map.keys():
            ip_str = ip_map["IP"]
            if ip_str:
                ip_str_list = ip_str_list + "," + str(ip_str)
    
    if ip_str_list:
        log_echo("INFO", "ip_str_list=" + ip_str_list)
        return ip_str_list.lower()
    else:
        log_echo("ERROR", "ipStr is none. ip_str_list=" + ip_str_list)
        return "ERROR"


def get_node_ip_list_by_nodename():
    node_json = sys.argv[2]
    nodename = sys.argv[3]
    node_ip_list = ""
    json_load = read_json(node_json)
    
    if "nodeList" in json_load.keys():
        for key in json_load["nodeList"].keys():
            if json_load["nodeList"][key]["hostname"] == nodename:
                node_ip_list = get_ip_list(json_load["nodeList"][key])
                if node_ip_list == "ERROR":
                    log_echo("ERROR", "Node Ip is null.get ip failed.")
                    print("ERROR")
                    return 1
                else:
                    break
    
    if node_ip_list:
        print(node_ip_list)
        return 0
    else:
        log_echo("ERROR", "Node Ip List is null")
        print("ERROR")
        return 1


if __name__ == '__main__':
    if json_to_handle == "product":
        handle_product()
    elif json_to_handle == "productpwd":
        handle_productpwd()
    elif json_to_handle == "getnodetype":
        get_nodetype()
    elif json_to_handle == "getnodetag":
        get_nodetag()
    elif json_to_handle == "getnodetag_of_nodename":
        getnodetag_of_nodename()
    elif json_to_handle == "nodetype":
        handle_nodetype()
    elif json_to_handle == "nodetag":
        handle_nodetag()
    elif json_to_handle == "update_nodetag_of_nodename":
        update_nodetag_of_nodename()
    elif json_to_handle == "pvalue":
        handle_pvalue()
    elif json_to_handle == "getnodename":
        get_nodename()
    elif json_to_handle == "get_all_nodename":
        get_all_nodename()
    elif json_to_handle == "productext_modify":
        productext_modify()
    elif json_to_handle == "productext_delete":
        productext_delete()
    elif json_to_handle == "getinfo_product":
        getinfo_product()
    elif json_to_handle == "add_nodetag_by_nodename":
        add_nodetag_by_nodename()
    elif json_to_handle == "del_nodetag_by_nodename":
        del_nodetag_by_nodename()
    elif json_to_handle == "add_nodetype_by_nodename":
        add_nodetype_by_nodename()
    elif json_to_handle == "get_maintenance_mask":
        try:
            get_maintenance_mask()
        except (KeyError, TypeError):
            sys.exit(1)
    elif json_to_handle == "get_maintenance_ip":
        try:
            get_maintenance_ip()
        except (KeyError, TypeError):
            sys.exit(1)
    elif json_to_handle == "handle_scanret":
        ret = handle_scanret()
        if ret != 0:
            sys.exit(1)
    elif json_to_handle == "get_node_num_by_nodetype":
        get_node_num_by_nodetype()
    elif json_to_handle == "get_node_ip_list_by_nodename":
        get_node_ip_list_by_nodename()
    log_file.close()
