#!/bin/bash
#####################################################
# Description : For Upgrade BatchPreset ALL Nodes 
#####################################################
CURRENT_PATH=$(cd $(dirname $0); pwd)
. ${CURRENT_PATH}/utils_common.sh ${CURRENT_PATH}
test $? != 0 && exit 1
. ${CURRENT_PATH}/utils_uniep.sh ${CURRENT_PATH}
test $? != 0 && exit 1
. ${CURRENT_PATH}/utils_os.sh ${CURRENT_PATH}
test $? != 0 && exit 1

. ${CURRENT_PATH}/../tools/dv_upgrade_utils.sh ${CURRENT_PATH}/..
CURRENT_PATH=${CURRENT_PATH}/..

command_prefix=""
if [ "X$(whoami)" == "Xroot" ];then
		command_prefix=""
else
		command_prefix="sudo -u ossuser "
fi
TEST_FILE="${command_prefix}test -f "
TEST_DIR="${command_prefix}test -d "

function upgrade_backup_product_info()
{
    log_echo "INFO" "upgrade_backup_product_info start..."
    ## check and get old product info
    local upgrade_history_version_path=/home/<USER>/upgrade_history_version
    [ ! -d ${upgrade_history_version_path} ] && mkdir -p ${upgrade_history_version_path}
    local upgrade_history_version_count=$(ls ${upgrade_history_version_path}|sort |wc -l)
    ## 只支持连续回退两跳，由于产品信息文件不大，考虑到多次升级预置等特殊场景，保险起见，保留5份。
    local save_count=5
    if [ ${upgrade_history_version_count} -gt ${save_count} ];then
        local need_del_count=$(( ${upgrade_history_version_count} - ${save_count} ))
        local need_to_del_version_list=$(ls ${upgrade_history_version_path} |sort | head -n ${need_del_count})
        for version_dir in ${need_to_del_version_list};do
            log_echo "INFO" "del ${upgrade_history_version_path}/${version_dir}"
            rm -rf ${upgrade_history_version_path:?}/${version_dir:?}
        done
    fi
    
    local current_product_version=$(ls ${CURRENT_PATH}/tools/SD/SwiftDeployExtend_DV*.zip|awk -F'-' '{print $2}'|awk -F'.' '{print $1}')
    log_echo "INFO" "current_product_version=${current_product_version}"
    if [ ! -d ${upgrade_history_version_path}/${current_product_version} ];then
        mkdir -p ${upgrade_history_version_path}/${current_product_version}
    fi
    
    ## product_SOP.json nodes_SOP.json
    local backup_product_path="${upgrade_history_version_path}/${current_product_version}"
    if [ ! -f ${backup_product_path}/product_SOP.json -o ! -f ${backup_product_path}/nodes_SOP.json ];then
        local upgrade_exp_product_path="${CURRENT_PATH}/tools/expInfo"
        local product_file=$(basename ${upgrade_exp_product_path}/product*.json)
        local nodes_file=$(basename ${upgrade_exp_product_path}/nodes*.json)
        
        file_exists "${upgrade_exp_product_path}/${product_file}"
        file_exists "${upgrade_exp_product_path}/${nodes_file}"
        
        cp -rpf ${upgrade_exp_product_path}/${product_file} ${backup_product_path}/product_SOP.json &&\
        cp -rpf ${upgrade_exp_product_path}/${nodes_file} ${backup_product_path}/nodes_SOP.json
        log_echo "INFO" "upgrade_backup_product_info copy the path ${upgrade_exp_product_path} file ${product_file} and ${nodes_file} to ${backup_product_path},ret=$?"
    fi
    
    log_echo "INFO" "upgrade_backup_product_info End."
}


function main()
{
    check_hostname

    check_SD_upgrade

    install_path=$(ps -ef | grep "uniepservice-0-0\|uniepliteservice-0-0" |grep -v grep |awk -F'/envs/Product-' '{print $1}' |awk '{print $NF}')

    check_is_disaster

    check_deploywebsite
    
    unzip_customFile

    clean_ossadm_history
    
    check_modify_userpw_json
    
    check_tzdata

    if [ -f "${CURRENT_PATH}/rollback_fail.tag" ];then
        log_echo "ERROR" "Rollback failed, need fix the rollback problem first before upgrade"
        exit 1
    fi

    if [ ! -d ${CURRENT_PATH}/UniEp/python ];then
        unzip -q ${CURRENT_PATH}/UniEp/python*.zip -d ${CURRENT_PATH}/UniEp/python
    fi
    availd_size=$(df -Pk ${CURRENT_PATH}|grep /|awk '{print $4}')
    if [ ${availd_size} -lt 1048576 ];then
        log_echo "ERROR" "${CURRENT_PATH}'s availd size is less than 1G "
        exit 1
    fi
    Get_netType "Upgrade"
    #${netWorkType}
    if [ "X${netWorkType}" == "XT" ];then
        netWorkPath="${CURRENT_PATH}/Product_TestNetwork"
        source_file="${CURRENT_PATH}/DV_Config_Test.config"
    elif [ "X${netWorkType}" == "XC" ];then
        netWorkPath="${CURRENT_PATH}/Product_ClusterNetwork"
        source_file="${CURRENT_PATH}/DV_Config_Cluster.config"
    elif [ "X${netWorkType}" == "XO" ];then
        netWorkPath="${CURRENT_PATH}/Product_AllInOneNetwork"
        source_file="${CURRENT_PATH}/DV_Config_AllInOne.config"
    elif [ "X${netWorkType}" == "XL" ];then
        netWorkPath="${CURRENT_PATH}/Product_LargeCapacityNetwork"
        source_file="${CURRENT_PATH}/DV_Config_LargeCapacity.config"
    elif [ "X${netWorkType}" == "XM" ];then
        netWorkPath="${CURRENT_PATH}/Product_MergeClusterNetwork"
        source_file="${CURRENT_PATH}/DV_Config_MergeCluster.config"
    else
        log_echo "ERROR" "netWorkType is incorrect, the value is ${netWorkType}"
        exit 1
    fi
    
    local product_json=${exp_product_path}/product*.json
    if [ ! -f ${product_json} ];then
        log_echo "ERROR" "Export product info by uniep failed,of ${product_json} is not exist.please check..."
        exit 1
    fi
    if [ ! -f ${CURRENT_PATH}/tools/handlejson.log ];then
        touch ${CURRENT_PATH}/tools/handlejson.log
    fi
    chmod 755 ${CURRENT_PATH}/tools/handlejson.py 
    chmod 640 ${CURRENT_PATH}/tools/handlejson.log 
    
    local countryNameUpgrade=$(get_current_value_of_cerkey "countryName")
    local organizationNameUpgrade=$(get_current_value_of_cerkey "organizationName")
    local commonNameUpgrade=$(get_current_value_of_cerkey "commonName")

    local enablepmservicemode=$(get_current_value "enablePMServiceMode")
    local cfg_enablepmservicemode=$(cat ${CURRENT_PATH}/Common/DV_config.properties |grep "^enablePMServiceMode="|awk -F '=' '{print $2}'|sed "s/[[:blank:]]//g")
    if [ "X${enablepmservicemode}" == "XTRUE" -a "X${cfg_enablepmservicemode}" == "XFALSE" ];then
        log_echo "ERROR" "Do not support enablePMServiceMode[TRUE] uprade to enablePMServiceMode[FALSE],please check enablePMServiceMode in ${source_file}."
        exit 1
    fi
    
    upgrade_backup_product_info
    
    local is_extend_internal_certificate_validity_period_old=$(get_current_value "is_extend_internal_certificate_validity_period")
    local is_extend_internal_certificate_validity_period_old_tag="${CURRENT_PATH}/tools/is_extend_internal_certificate_validity_period_old.tag"
    log_echo "INFO" "The is_extend_internal_certificate_validity_period_old=${is_extend_internal_certificate_validity_period_old} is_extend_internal_certificate_validity_period_old_tag=${is_extend_internal_certificate_validity_period_old_tag}"
    if [ ! -f ${is_extend_internal_certificate_validity_period_old_tag} ];then
        [ -z "${is_extend_internal_certificate_validity_period_old}" ] && is_extend_internal_certificate_validity_period_old="False"
        log_echo "INFO" "The ${is_extend_internal_certificate_validity_period_old_tag} is not exist.create it.and set is_extend_internal_certificate_validity_period_old=${is_extend_internal_certificate_validity_period_old}"
        echo "is_extend_internal_certificate_validity_period_old=${is_extend_internal_certificate_validity_period_old}" > ${is_extend_internal_certificate_validity_period_old_tag}
    fi
    
    if [ "X${cfg_enablepmservicemode}" == "XTRUE" ];then
        touch /home/<USER>/dv_pmmode
        chmod 600 /home/<USER>/dv_pmmode
    else
        rm -f /home/<USER>/dv_pmmode
    fi
    
    if [ -f ${configParametersFile} ];then
        rm -rf ${configParametersFile:?}
    fi
    
    if [ -f "${CURRENT_PATH}/express.tag" ];then
        log_echo "INFO" "Find express.tag, it's express situation for upgrade"
        sed -i "s/is_express=.*/is_express=Yes/g" ${CURRENT_PATH}/Common/DV_config.properties
        sed -i "s/is_express=.*/is_express=Yes/g" ${CURRENT_PATH}/Common/DV_config.properties.tmp > /dev/null 2>&1
        sed -i "s/IS_INSTALL_ICNFG=.*/IS_INSTALL_ICNFG=No/g" ${source_file}
        sed -i "/is_support_express/ s/false/true/g" ${netWorkPath}/Product_template/product*.json
        grep "is_support_express" ${netWorkPath}/Product_template/product*.json | grep "false"
        if [ $? -eq 0 ];then
            log_echo "ERROR" "Modify is_support_express to be true in product*.json failed, please check"
            exit 1
        fi
    fi
    
    local tmp_value=""
    local config_yaml_path=${CURRENT_PATH}/tools/autoupgrade/config.yaml
    if [ -f ${config_yaml_path} ];then
        tmp_value=$(cat ${config_yaml_path}|grep "is_extend_internal_certificate_validity_period:"|awk -F'is_extend_internal_certificate_validity_period:' '{print $2}'|sed 's/ //g')
        log_echo "INFO" "In ${config_yaml_path},the is_extend_internal_certificate_validity_period=${tmp_value}"
    else
        tmp_value=$(cat ${source_file}|grep "^[[:blank:]]*is_extend_internal_certificate_validity_period="|awk -F'is_extend_internal_certificate_validity_period=' '{print $2}'|sed 's/ //g')
        log_echo "INFO" "In ${source_file},the is_extend_internal_certificate_validity_period=${tmp_value}"
    fi
    
    if [ "$(echo ${tmp_value}|tr 'a-z' 'A-Z')" == "TRUE" ];then
        log_echo "INFO" "The is_extend_internal_certificate_validity_period is True"
        sed -i "s/is_extend_internal_certificate_validity_period=.*/is_extend_internal_certificate_validity_period=True/g" ${source_file}
    else
        log_echo "INFO" "The is_extend_internal_certificate_validity_period is False"
        sed -i "s/is_extend_internal_certificate_validity_period=.*/is_extend_internal_certificate_validity_period=False/g" ${source_file}
    fi
    
    product_new_version=$(ls ${CURRENT_PATH}/tools/SD/SwiftDeployExtend_DV*.zip|awk -F'-' '{print $2}'|awk -F'.' '{print $1}')
    [[ ! -f /home/<USER>/dv_loop_monitor ]] && mkdir -p /home/<USER>/dv_loop_monitor
    touch /home/<USER>/dv_loop_monitor/upgrade_${product_new_version}.tag
    touch ${configParametersFile}
    
    update_config ${source_file} "countryName" "\"${countryNameUpgrade}\"" || exit 1
    update_config ${source_file} "organizationName" "\"${organizationNameUpgrade}\"" || exit 1
    update_config ${source_file} "commonName" "\"${commonNameUpgrade}\"" || exit 1
    update_config ${configParametersFile} "netWorkPath" "${netWorkPath}" || exit 1
    update_config ${configParametersFile} "netWorkType" "${netWorkType}" || exit 1
    update_config ${configParametersFile} "source_file" "${source_file}" || exit 1
    update_config ${configParametersFile} "is_SD_upgrade" "${is_SD_upgrade}" || exit 1
    update_config ${configParametersFile} "exp_product_path" "${exp_product_path}" || exit 1
    update_config ${configParametersFile} "install_path" "${install_path}" || exit 1
    update_config ${configParametersFile} "is_upgrade" "Yes" || exit 1
    update_config "${CURRENT_PATH}/Common/DV_config.properties" "netWorkType" "${netWorkType}" || exit 1
    update_config "${CURRENT_PATH}/Common/DV_config.properties" "product_new_version" "${product_new_version}" || exit 1
    . ${source_file}
    . ${configParametersFile}
    
    clean_knownhosts_ossadm
    
    log_echo "INFO" "execute sh ${netWorkPath}/Upgrade_check_before.sh start"
    sh ${netWorkPath}/Upgrade_check_before.sh
    test $? != 0 && exit 1
    log_echo "INFO" "execute sh ${netWorkPath}/Upgrade_check_before.sh end"

    log_echo "INFO" "execute sh ${netWorkPath}/Upgrade_node.sh start"
    sh ${netWorkPath}/Upgrade_node.sh
    test $? != 0 && exit 1
    log_echo "INFO" "execute sh ${netWorkPath}/Upgrade_node.sh end"

    log_echo "INFO" "execute sh ${netWorkPath}/Upgrade_post_handle.sh start"
    sh ${netWorkPath}/Upgrade_post_handle.sh
    test $? != 0 && exit 1
    log_echo "INFO" "execute sh ${netWorkPath}/Upgrade_post_handle.sh end"
    
    install_infocollect "${install_path}"
    install_health_check "${install_path}"
    install_uniep_tool "${INSTALL_PATH}" "log_level_manager"
    install_uniep_tool "${INSTALL_PATH}" "daylight_saving_time_update"
    collect_network_config "${source_file}" "Upgrade"
    
    if [ -d /opt/oss/delete_hofs_routes ];then
        rm -rf /opt/oss/delete_hofs_routes
    fi
    cp -rf ${CURRENT_PATH}/tools/delete_hofs_routes /opt/oss
    chmod 550 /opt/oss/delete_hofs_routes/delete_hofs_routes.py
    chmod 550 /opt/oss/delete_hofs_routes/delete_hofs_routes.sh

    permission_handle
    if [ -f ${CURRENT_PATH}/Common/pwfile.json ];then
        rm -f ${CURRENT_PATH}/Common/pwfile.json
    fi

    if [ "X${enable_os_rollback}" == "XTRUE" ];then
        log_echo "INFO" "touch /home/<USER>/restore_db_app.tag"
        touch /home/<USER>/restore_db_app.tag
        if [ $? -ne 0 ];then
           log_echo "ERROR" "touch /home/<USER>/restore_db_app.tag failed, please check"
           exit 1
        fi
    else
        log_echo "INFO" "enable_os_rollback is false, deleted restore_db_app.tag"
        rm -f /home/<USER>/restore_db_app.tag
    fi

    custom_eamdb

    bash -c "source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python  ${CURRENT_PATH}/tools/dv_zenith_tool.py alterTblBackupRestoreTask"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "alterTblBackupRestoreTask failed" 
        exit 1
    fi
    custom_i2k_version
    
    sudo -u ossuser bash -c "[ -d /opt/oss/share/SOP/DVEngineeringService/I2000 ] && touch /opt/oss/share/SOP/DVEngineeringService/I2000/not_need_start.tag"

    clear_redundancy_rtsp

    log_echo "INFO" "DV_Upgrade_Non_Root_Preset execute successfully."
}

main