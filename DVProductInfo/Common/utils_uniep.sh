#!/bin/bash
set +x
if [ $# -lt 1 ]; then
    UTILS_PATH=$(cd $(dirname $0); pwd)
else
    UTILS_PATH="$1"
fi

if [ ! -f $UTILS_PATH/utils_uniep.sh ];then
    echo "The UTILS_PATH=${UTILS_PATH} is error of utils_uniep.sh"
    exit 1
fi

##Common
PreSet_PATH=$(cd ${UTILS_PATH};pwd)
isInCommon=$(basename ${PreSet_PATH})
if [ "${isInCommon}" == "Common" ];then
    PreSet_PATH=$(cd ${UTILS_PATH}/..;pwd)
fi

configParametersFile=${UTILS_PATH}/netWorkTypeOfDvConfigParameters.config
dv_cfg_file=${UTILS_PATH}/DV_config.properties
[ -f ${dv_cfg_file} ] && . ${dv_cfg_file} 2> /dev/null
[ -f ${dv_cfg_file}.tmp ] && . ${dv_cfg_file}.tmp 2> /dev/null
[ -f ${configParametersFile} ] && . ${configParametersFile} 2> /dev/null
if [ "X${source_file}" != "X" ];then
    source_file_name=$(basename ${source_file})
    [ -f ${configParametersFile} ] && [ -f "${PreSet_PATH}/${source_file_name}" ] && . ${PreSet_PATH}/${source_file_name} 2> /dev/null
fi

CA_PATH=${PreSet_PATH}/CA
action_tag="${PreSet_PATH}/install_action.tag"
os_version=$(grep "VERSION=" /etc/os-release|head -1 | awk -F'=' '{print $2}'|sed "s/\"//g")
if [ "X${os_version}" == "X2.0 (SP9x86_64)" -o "X${os_version}" == "X2.0 (SP10x86_64)" -o "X${os_version}" == "X2.0 (SP10)" -o "X${os_version}" == "X12-SP5"  -o "X${euler_version}" == "X2.0 (SP11x86_64)" -o "X${euler_version}" == "X2.0 (SP11)" -o "X${euler_version}" == "X2.0 (SP12x86_64)" -o "X${euler_version}" == "X2.0 (SP12)" ];then
  lsof_cmd=$(which lsof |grep -v "alias" |grep "/lsof" |awk '{print $1}')' -K i'
else
  lsof_cmd=$(which lsof |grep -v "alias" |grep "/lsof" |awk '{print $1}')
fi
if [ "X$(whoami)" == "Xroot" ];then
    which python &> /dev/null || alias python=python3
fi

command_prefix=""
if [ "X$(whoami)" == "Xroot" ];then
    command_prefix=""
else
    command_prefix="sudo -u ossuser "
fi
TEST_FILE="${command_prefix}test -f "
TEST_DIR="${command_prefix}test -d "
export LANG=en_US.UTF-8

function is_uniep_node()
{
    if [ "X${netWorkType}" == "XO" ];then
        UNIEP_LOCAL_IP=${ALL_IN_ONE_IPV4}
        if [ "X${IPType}" != "X0" ];then
            UNIEP_LOCAL_IP=${ALL_IN_ONE_IPV6}
        fi
    elif [ "X${netWorkType}" == "XM" ];then 
        UniEP_ExtendDisk_isMounted=FALSE
        update_config ${configParametersFile} "UniEP_ExtendDisk_isMounted" "${UniEP_ExtendDisk_isMounted}" || exit 1
        UNIEP_LOCAL_IP=${DVPrimary_IPV4}
        if [ "X${IPType}" == "X1" ];then
            UNIEP_LOCAL_IP=${DVPrimary_IPV6}
        fi
    else
        UNIEP_LOCAL_IP=${UNIEP_IPV4}
        if [ "X${IPType}" == "X1" ];then
            UNIEP_LOCAL_IP=${UNIEP_IPV6}
        fi
    fi
    
    ip addr |grep -w ${UNIEP_LOCAL_IP} >/dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "We need run this at UniEp node, please check it is on the ${UNIEP_LOCAL_IP} node."
        exit 1
    fi
}

function check_uniep_preset_dir()
{
    local preset_dir="$1"
    if [ -z "${preset_dir}" ];then
        log_echo "ERROR" "The preset_dir=${preset_dir} is null.please check."
        return 1
    fi
    
    if [ "X${AUTO_DISK_MOUNT}" != "XTRUE" -o -f ${ATAE_extend_script} ];then
        log_echo "INFO" "The AUTO_DISK_MOUNT=${AUTO_DISK_MOUNT} not need to check_uniep_preset_dir."
        return 0
    fi
    
    local path_list=""
    if [ "X${netWorkType}" == "XM" ];then
        if [ ! -z "${VSINDEX_DISK}" ];then
            path_list=" ${path_list} ${VS_PATH}"
        fi
        
        if [ ! -z "${DBNode_DISK}" ];then
            path_list=" ${path_list} ${DB_INSTALLPATH}"
        fi
        
        if [ ! -z "${HA_DISK}" ];then
            path_list=" ${path_list} ${INSTALL_PATH}/share/SOP/DVEngineeringService"
        fi
    fi
    
    local ext_disk_cfg=${UTILS_PATH}/ext_disk.properties
    
    if [ "X${UniEP_ExtendDisk_isMounted}" != "XTRUE" -a ! -z "${EXTEND_DISK}" ];then
        log_echo "INFO" "Begin to check extend disk..."
        
        for line in `cat ${ext_disk_cfg}.tmp`
        do
            if [ "X$line" == "X" ];then
                continue
            fi
            path_name=$(echo $line |awk -F'=' '{print $1}')
            
            if [ "${path_name}" != "swap" ];then
                path_list=" ${path_list} ${path_name} "
            fi
        done
    fi
    
    if [ ! -z "${UNIEP_PUB_DISK}" ];then
        path_list=" ${path_list} /opt/pub"
    fi
    
    if [ -z "${path_list}" ];then
        log_echo "INFO" "The path_list=${path_list} is null.not need to check_uniep_preset_dir."
        return 0
    fi
    
    ## ${preset_dir}
    for path_dir in ${path_list};do
        if [ ! -d ${path_dir} ];then
            log_echo "INFO" "The path_dir=${path_dir} is not exists.not need to check."
            continue
        fi
        
        ${lsof_cmd} +D ${path_dir} |grep -v grep |grep -w "${preset_dir}" 
        if [ $? -eq 0 ];then
            log_echo "ERROR" "The preset_dir=${preset_dir} runing in the auto mount disk path_dir=${path_dir}. please check."
            return 1
        fi
    done
    
    log_echo "INFO" "check_uniep_preset_dir check finished."
    return 0
}

function check_osType_of_pkg()
{
    OS_TYPE=""
    if [ -f /etc/SuSE-release ];then
        suse_version=$(grep "VERSION" /etc/SuSE-release | awk '{print $NF}')
        if [ $suse_version -eq 12 ] ; then
            OS_TYPE="SUSE12"
            ostype=$(uname -i)
            if [ "X${ostype}" == "Xaarch64" ] ;then
                OS_TYPE="SUSE_arm64"
            fi
        else
            OS_TYPE="SUSE"
        fi
    elif [ -f /etc/os-release ];then
        euler_version=$(grep "VERSION=" /etc/os-release|head -1 | awk -F'=' '{print $2}'|sed "s/\"//g")
        pretty_name=$(grep "PRETTY_NAME=" /etc/os-release|head -1 | awk -F'=' '{print $2}'|sed "s/\"//g")
        OS_ARCH_TMP=$(lscpu |grep "Architecture:"|awk -F ':' '{print $2}' |sed "s/ //g")

        grep -qi "ctyunos" /etc/os-release
        if [ $? -eq 0 ] || [[ "${pretty_name}" =~ "EulerOS 2.0" ]];then
            if [ "X${OS_ARCH_TMP}" == "Xaarch64" ];then
                OS_TYPE="Euler_arm64"
            elif [ "X${OS_ARCH_TMP}" == "Xx86_64" ];then
                OS_TYPE="Euler_amd64"
            fi
        fi

        if [ "X${euler_version}" == "X2.0 (SP9x86_64)" -o "X${euler_version}" == "X2.0 (SP10x86_64)" -o "X${euler_version}" == "X2.0 (SP10)" -o "X${euler_version}" == "X2.0 (SP11x86_64)" -o "X${euler_version}" == "X2.0 (SP11)" -o "X${euler_version}" == "X2.0 (SP12x86_64)" -o "X${euler_version}" == "X2.0 (SP12)" ];then
            if [ "X${OS_ARCH_TMP}" == "Xaarch64" ];then
                OS_TYPE="Euler_arm64"
            elif [ "X${OS_ARCH_TMP}" == "Xx86_64" ];then
                OS_TYPE="Euler_amd64"
            fi
        else
            cat /etc/os-release | grep "Kylin" > /dev/null
            if [ $? -eq 0 ];then
                ostype=$(uname -i)
                if [ "X${ostype}" == "Xx86_64" ];then
                    OS_TYPE="Euler_amd64"
                elif [ "X${ostype}" == "Xaarch64" ];then
                    OS_TYPE="Euler_arm64"
                fi
            fi
            
            cat /etc/os-release | grep "BigCloud Enterprise Linux For Euler 21.10" > /dev/null
            if [ $? -eq 0 ];then
                ostype=$(uname -i)
                if [ "X${ostype}" == "Xx86_64" ];then
                    OS_TYPE="Euler_amd64"
                elif [ "X${ostype}" == "Xaarch64" ];then
                    OS_TYPE="Euler_arm64"
                fi
            fi
        fi
        grep -rin "platform:uel" /etc/os-release > /dev/null
        if [ $? -eq 0 ];then
            [ -f  /etc/profile.d/system-info.sh ] && mv /etc/profile.d/system-info.sh /etc/profile.d/system-info.sh_bak 
            chmod +4755 /usr/bin/ping
            OS_ARCH_TMP=$(lscpu |grep "Architecture:"|awk -F ':' '{print $2}' |sed "s/ //g")
            if [ "X${OS_ARCH_TMP}" == "Xaarch64" ];then
                OS_TYPE="Euler_arm64"
                sed -i '/PRETTY_NAME/d' /etc/os-release
                echo  'PRETTY_NAME="EulerOS 2.0 (SP10)"' >> /etc/os-release
                echo "EulerOS release 2.0 (SP10)" > /etc/euleros-release
                chmod 644 /etc/euleros-release
            fi 
        fi
        ## UnionTech
        grep -rin "UnionTech OS Server 20" /etc/os-release > /dev/null
        if [ $? -eq 0 ]; then
            uname -m | grep "x86_64" > /dev/null
            if [ $? -eq 0 ]; then
                log_echo "INFO" "The OS is UnionTech_X86"
                OS_TYPE="UnionTech_X86"
            else
                log_echo "INFO" "The OS is UnionTech_ARM"
                OS_TYPE="UnionTech_ARM"
            fi
        fi
    fi
    log_echo "INFO" "check the OS_TYPE=${OS_TYPE}."
    
    if [ "X${OS_TYPE}" == "X" ];then
        log_echo "ERROR" "Get os type and os version failed. Check whethe /etc/SuSE-release or /etc/os-release exists? Or the os type is not support.of function check_osType_of_pkg."
        return 1
    fi
    
    if [ "${OS_TYPE}" == "SUSE12" -o  "${OS_TYPE}" == "SUSE" ];then
        OSCONFIG_PATH=$(ls ${PreSet_PATH}/CloudSop/osconfig-*.zip 2>/dev/null |grep -v euler_amd64 |grep -v euler_arm64 |head -1)
    elif [ "${OS_TYPE}" == "SUSE_arm64" ]; then
        OSCONFIG_PATH=$(ls ${PreSet_PATH}/CloudSop/osconfig-*.zip 2>/dev/null |grep euler_arm64 |head -1)
    elif [ "${OS_TYPE}" == "Euler_arm64" -o "${OS_TYPE}" == "UnionTech_ARM" ];then
        OSCONFIG_PATH=$(ls ${PreSet_PATH}/CloudSop/osconfig-*.zip 2>/dev/null |grep euler_arm64 |head -1)
    elif [ "${OS_TYPE}" == "Euler_amd64" -o "${OS_TYPE}" == "UnionTech_X86" ];then
        OSCONFIG_PATH=$(ls ${PreSet_PATH}/CloudSop/osconfig-*.zip 2>/dev/null |grep euler_amd64 |head -1)
    fi
    
    if [ "X${OSCONFIG_PATH}" == "X" ];then
        log_echo "ERROR" "The OSCONFIG_PATH=${OSCONFIG_PATH}, The os type is OS_TYPE=${OS_TYPE}, but in path ${PreSet_PATH} no osconfig package matching the os type is found. Check whether the current preset package matches the os type."
        return 1
    fi
    log_echo "INFO" "check the OSCONFIG_PATH=${OSCONFIG_PATH}."
    return 0
}

function create_uniep_pwfile()
{
    if [[ ! -f ${PreSet_PATH}/Common/pwfile.json ]];then
        log_echo "INFO" "Begin to create pwfile"
        echo "[{\"name\":\"sopuser\",\"value\":\"${sopuser_pwd}\",\"encrypt\":\"yes\"},{\"name\":\"ossuser\",\"value\":\"${ossuser_pwd}\",\"encrypt\":\"yes\"},{\"name\":\"ossadm\",\"value\":\"${ossadm_pwd}\",\"encrypt\":\"yes\"},{\"name\":\"web_admin_user_name\",\"value\":\"${WEB_ADMIN_USER_NAME}\",\"encrypt\":\"no\"},{\"name\":\"web_admin_user_value\",\"value\":\"${web_admin_pwd}\",\"encrypt\":\"yes\"},{\"name\":\"protect_pvalue\",\"value\":\"${protect_pwd}\",\"encrypt\":\"yes\"},{\"name\":\"redis-dbuser\",\"value\":\"${redis_dbuser_pwd}\",\"encrypt\":\"yes\"},{\"name\":\"zenith-sys\",\"value\":\"${zenith_sys_pwd}\",\"encrypt\":\"yes\"}]" > ${PreSet_PATH}/Common/pwfile.json
        chmod 644 ${PreSet_PATH}/Common/pwfile.json
        cp -f ${PreSet_PATH}/Common/pwfile.json ${PreSet_PATH}/UniEp/
        
        #os_user_pwd
        decrypt_password ${ossuser_pwd}
        os_user_pwd=${decryptPasswd}
        #ossadm_user_pwd
        decrypt_password ${ossadm_pwd}
        ossadm_user_pwd=${decryptPasswd}
        #db_user_pwd
        decrypt_password ${redis_dbuser_pwd}
        db_user_pwd=${decryptPasswd}
        #web_admin_pwd
        decrypt_password ${web_admin_pwd}
        web_admin_pwd=${decryptPasswd}
        #protect_pwd
        decrypt_password ${protect_pwd}
        protect_pwd=${decryptPasswd}
    fi
    
    if [ "X${is_SD_upgrade}" == "XYes" ];then
        if [ ! -f "${SD_pwfile}" ];then
            echo "install_path=${install_path}" > ${SD_pwfile}
            
            encrypt_password ${os_user_pwd}
            os_user_pwd=${encryptPasswd}
            echo "os_pwd=${os_user_pwd}" >> ${SD_pwfile}
            
            encrypt_password ${ossadm_user_pwd}
            ossadm_user_pwd=${encryptPasswd}
            echo "ossadm_pwd=${ossadm_user_pwd}" >> ${SD_pwfile}
            
            encrypt_password ${db_user_pwd}
            db_user_pwd=${encryptPasswd}
            echo "db_pwd=${db_user_pwd}" >> ${SD_pwfile}
            
            encrypt_password ${web_admin_pwd}
            web_admin_pwd=${encryptPasswd}
            echo "web_pwd=${web_admin_pwd}" >> ${SD_pwfile}
            
            encrypt_password ${protect_pwd}
            protect_pwd=${encryptPasswd}
            echo "protect_encrypt_pwd=${protect_pwd}" >> ${SD_pwfile}
        fi
        
        cp -f ${SD_pwfile} /home/<USER>/SD_pwfile.properties
        chown ossadm:ossgroup /home/<USER>/SD_pwfile.properties
        chmod 600 /home/<USER>/SD_pwfile.properties

        return 0
    fi
}

function init_pwd_encrypt()
{
    log_echo "INFO" "Begin to obtain passwords from pwfile.json file."
    if [ ! -f ${PreSet_PATH}/Common/pwfile.json ];then
        log_echo "ERROR" "The ${PreSet_PATH}/Common/pwfile.json not exists."
        exit 1
    fi
    
    os_user_pwd_encrypt=$(cat ${PreSet_PATH}/Common/pwfile.json| sed 's/}/\n/g' | grep -w ossuser | awk -F : '{print $3}')
    os_user_pwd_encrypt=${os_user_pwd_encrypt#*\"}
    os_user_pwd_encrypt=${os_user_pwd_encrypt%%\"*}
    decrypt_password ${os_user_pwd_encrypt}
    os_user_pwd=${decryptPasswd}
    
    ossadm_user_pwd_encrypt=$(cat ${PreSet_PATH}/Common/pwfile.json| sed 's/}/\n/g' | grep -w ossadm | awk -F : '{print $3}')
    ossadm_user_pwd_encrypt=${ossadm_user_pwd_encrypt#*\"}
    ossadm_user_pwd_encrypt=${ossadm_user_pwd_encrypt%%\"*}
    decrypt_password ${ossadm_user_pwd_encrypt}
    ossadm_user_pwd=${decryptPasswd}
    
    db_user_pwd_encrypt=$(cat ${PreSet_PATH}/Common/pwfile.json| sed 's/}/\n/g' | grep -w redis-dbuser | awk -F : '{print $3}')
    db_user_pwd_encrypt=${db_user_pwd_encrypt#*\"}
    db_user_pwd_encrypt=${db_user_pwd_encrypt%%\"*}
    decrypt_password ${db_user_pwd_encrypt}
    db_user_pwd=${decryptPasswd}
    
    web_admin_pwd_encrypt=$(cat ${PreSet_PATH}/Common/pwfile.json| sed 's/}/\n/g' | grep -w web_admin_user_value | awk -F : '{print $3}')
    web_admin_pwd_encrypt=${web_admin_pwd_encrypt#*\"}
    web_admin_pwd_encrypt=${web_admin_pwd_encrypt%%\"*}
    decrypt_password ${web_admin_pwd_encrypt}
    web_admin_pwd=${decryptPasswd}
    
    protect_pwd_encrypt=$(cat ${PreSet_PATH}/Common/pwfile.json| sed 's/}/\n/g' | grep -w protect_pvalue | awk -F : '{print $3}')
    protect_pwd_encrypt=${protect_pwd_encrypt#*\"}
    protect_pwd_encrypt=${protect_pwd_encrypt%%\"*}
    decrypt_password ${protect_pwd_encrypt}
    protect_pwd=${decryptPasswd}
    
    if [ "X${DV_UNIFIED_PWD}" == "XYes" ];then
        unified_pwd="${os_user_pwd}"
        unified_pwd_encrypt="${os_user_pwd_encrypt}"
    fi
}

function init_weak_passwd_array()
{
    weak_passwd_array=()
    local weak_passwd_i=0
    while read weak_passwd_encrypt
    do
        weak_passwd_array[weak_passwd_i]=${weak_passwd_encrypt}
        weak_passwd_i=$((${weak_passwd_i}+1))
    done <${PreSet_PATH}/Common/weakvalue.txt

}

function enter_password()
{
    if [ "X${unified_pwd}" != "X" ] && [[ ! "$1" =~ .*"new root".* ]] &&  [[ ! "$1" =~ .*"new sshusr".*  ]];then
        de_pwd="${unified_pwd}"
        en_pwd="${unified_pwd_encrypt}"
        return 0
    fi
    message=$1
    pwd_type=$2
    plain_or_cipher=$3
    db_user_type=$4
    local input_times=0
    
    init_weak_passwd_array

    while [ ${input_times} -lt 3 ]
    do
        echo_pwd "${message}"
        enter_pwd="$PwdReturn"
        echo_pwd "${message} again"
        enter_pwd_new="$PwdReturn"
        if [ "X${enter_pwd}" != "X${enter_pwd_new}" ];then
            input_times=$((${input_times}+1))
            if [ ${input_times} -eq 3 ];then
                log_echo "ERROR" "The two passwords do not match"
                exit 1
            fi
            log_echo "ERROR" "The two passwords do not match, retry again"
            continue
        fi

        is_weak=0
        for  tmp_passwd in ${weak_passwd_array[@]};do
            [[ "${enter_pwd}" =~ .*"${tmp_passwd}".* ]]
            if [ $? -eq 0 ];then
                is_weak=1
                input_times=$((${input_times}+1))
                if [ ${input_times} -eq 3 ];then
                    log_echo "ERROR" "Password is weak password"
                    exit 1
                fi
                log_echo "ERROR" "Password is weak password,retry again"
                break
            fi
        done
        if [ ${is_weak} -eq 1 ];then
            continue
        fi
        if [ "X${pwd_type}" == "Xos" ];then
            check_os_weakpasswd "${enter_pwd}"
            if [ $? -eq 1 ];then
                log_echo "ERROR" "BAD PASSWORD: it is based on a dictionary word,retry again"
                input_times=$((${input_times}+1))
                if [ ${input_times} -eq 3 ];then
                    log_echo "ERROR" "BAD PASSWORD: it is based on a dictionary word"
                    exit 1
                fi
                continue
            fi
        fi
        check_pwd "${enter_pwd}" "${pwd_type}" "${db_user_type}"
        if [ $? -ne 1 ];then
            if [ "X${plain_or_cipher}" == "Xplain" ];then
                de_pwd=${enter_pwd}
            elif [ "X${plain_or_cipher}" == "Xcipher" ];then
                encrypt_password ${enter_pwd}
                en_pwd=${encryptPasswd}
            else
                de_pwd=${enter_pwd}
                encrypt_password ${enter_pwd}
                en_pwd=${encryptPasswd}
            fi
            break
        else
            input_times=$((${input_times}+1))
            if [ ${input_times} -eq 3 ];then
                log_echo "ERROR" "Password do not match the rule"
                exit 1
            fi
            log_echo "ERROR" "Password do not match the rule, retry again "
            continue
        fi
    done
}

function pwd_of_passwords_properties()
{
    enter_password "Please input os(sysomc) password" "db" "plain"
    if [ $? -eq 0 ];then
        sysomc_pwd=${de_pwd}
    fi
    enter_password "Please input os(sftpossuser) password" "db" "plain"
    if [ $? -eq 0 ];then
        sftpossuser_pwd=${de_pwd}
    fi
    enter_password "Please input os(devdata) password" "db" "plain"
    if [ $? -eq 0 ];then
        devdata_pwd=${de_pwd}
    fi
    enter_password "Please input paasinter password" "db" "plain"
    if [ $? -eq 0 ];then
        paasinter_pwd=${de_pwd}
    fi
    enter_password "Please input I2kNfv password" "db" "plain"
    if [ $? -eq 0 ];then
        I2kNfv_pwd=${de_pwd}
    fi
    enter_password "Please input cie_snmp password" "db" "plain"
    if [ $? -eq 0 ];then
        cie_snmp_pwd=${de_pwd}
    fi
    enter_password "Please input sftp_admin password" "db" "plain"
    if [ $? -eq 0 ];then
        sftp_admin_pwd=${de_pwd}
    fi
    enter_password "Please input sftp_nbiuser password" "db" "plain"
    if [ $? -eq 0 ];then
        sftp_nbiuser_pwd=${de_pwd}
    fi
    enter_password "Please input sftp_mmluser password" "db" "plain"
    if [ $? -eq 0 ];then
        sftp_mmluser_pwd=${de_pwd}
    fi
    enter_password "Please input sftp_assetuser password" "db" "plain"
    if [ $? -eq 0 ];then
        sftp_assetuser_pwd=${de_pwd}
    fi
    enter_password "Please input DV_CERT_DRIVER password" "db" "plain"
    if [ $? -eq 0 ];then
        DV_CERT_DRIVER_pwd=${de_pwd}
    fi
    enter_password "Please input tool_mainast password" "db" "plain"
    if [ $? -eq 0 ];then
        tool_mainast_pwd=${de_pwd}
    fi
    enter_password "Please input tool_ideploy password" "db" "plain"
    if [ $? -eq 0 ];then
        tool_ideploy_pwd=${de_pwd}
    fi
    enter_password "Please input csweb_db password" "db" "plain"
    if [ $? -eq 0 ];then
        csweb_db_pwd=${de_pwd}
    fi
    enter_password "Please input pmdashboard_db password" "db" "plain"
    if [ $? -eq 0 ];then
        pmdashboard_db_pwd=${de_pwd}
    fi
    enter_password "Please input cs_service password" "db" "plain"
    if [ $? -eq 0 ];then
        cs_service_pwd=${de_pwd}
    fi
    if [ "X${DV_TOOLKIT_INSTALL}" == "XYes" ];then
        enter_password "Please input tool_dms password" "db" "plain"
        if [ $? -eq 0 ];then
            tool_dms_pwd=${de_pwd}
        fi
        enter_password "Please input tool_das password" "db" "plain"
        if [ $? -eq 0 ];then
            tool_das_pwd=${de_pwd}
        fi
        enter_password "Please input tool_easyms password" "db" "plain"
        if [ $? -eq 0 ];then
            tool_easyms_pwd=${de_pwd}
        fi
        enter_password "Please input dms_db password" "db" "plain"
        if [ $? -eq 0 ];then
            dms_db_pwd=${de_pwd}
        fi
        enter_password "Please input easyms_db password" "db" "plain"
        if [ $? -eq 0 ];then
            easyms_db_pwd=${de_pwd}
        fi
    fi
    enter_password "Please input tool_scp password" "db" "plain"
    if [ $? -eq 0 ];then
        tool_scp_pwd=${de_pwd}
    fi
    if [ ! -f "/.dockerenv" ] && [ "X${dv_force_change_os_pwd}" == "XYes" ];then
        if [ "X${new_root_pwd}" == "X" ];then
            enter_password "Please input dv new root password" "os"
            if [ $? -eq 0 ];then
                new_root_pwd="${de_pwd}"
                new_root_pwd_encrypt="${en_pwd}"
            fi
        fi
        if [ "X${new_sshusr_pwd}" == "X" ];then
            enter_password "Please input dv new sshusr password" "os"
            if [ $? -eq 0 ];then
                new_sshusr_pwd="${de_pwd}"
                new_sshusr_pwd_encrypt="${en_pwd}"
            fi
        fi
    fi
}

function check_os_weakpasswd()
{
    local need_check_passwd=$1
    which cracklib-check >/dev/null 2<&1
    if [ $? -ne 0 ];then
        return 0
    fi
    echo "${need_check_passwd}"|cracklib-check|grep -o "it is based on a dictionary word" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        return 1
    fi
}

function enter_ca_pwd()
{
    local input_times=0
    while [ ${input_times} -lt 3 ]
    do
        echo_pwd "Please input the DigitalView_CA.key's password"
        ca_cert_pwd="$PwdReturn"
        openssl rsa -noout -in ${PreSet_PATH}/tools/sub_ca/DigitalView_CA.key -passin stdin<<EOF
${ca_cert_pwd}
EOF
        if [ $? -ne 0 ];then
            log_echo "INFO" "Your ca cert password is wrong, please try again"
        else
            break
        fi
        input_times=$((${input_times}+1))
    done
    if [ ${input_times} -eq 3 ];then
        log_echo "ERROR" "Your ca cert password is wrong"
        exit 1
    fi
    return 0
}

function input_preset_pwd()
{
    if [ "X${pre_install_key}" == "XPreInstall" ];then
        log_echo "INFO" "It is PreInstall or SILENCE_INSTALLMODE is TRUE, get password from pipe."
        ## 用户名列表，用于日志打印那个用户密码有误
        local user_name_list="sopuser ossuser ossadm web_admin protect redis_dbuser zenith_sys sysomc sftpossuser devdata paasinter I2kNfv cie_snmp sftp_admin sftp_nbiuser sftp_mmluser sftp_assetuser DV_CERT_DRIVER tool_mainast tool_ideploy csweb_db pmdashboard_db cs_service"
        local need_check_value_list="${sopuser} ${ossuser} ${ossadm} ${web_admin} ${protect} ${redis_dbuser} ${zenith_sys} ${sysomc}  ${sftpossuser}  ${devdata} ${paasinter} ${I2kNfv} ${cie_snmp} ${sftp_admin} ${sftp_nbiuser} ${sftp_mmluser} ${sftp_assetuser} ${DV_CERT_DRIVER} ${tool_mainast} ${tool_ideploy} ${csweb_db} ${pmdashboard_db} ${cs_service}"
        while read line || [[ -n ${line} ]]
        do
            if [ -z "${line}" ];then
                continue
            fi
            
            key=${line}
            value=$(eval echo \$${key})
            if [ -z "${value}" ];then
                log_echo "INFO" "The ${key} of value is null."
                continue
            fi
            user_name_list="${user_name_list} ${key}"
            need_check_value_list="${need_check_value_list} ${value}"
        done < ${PreSet_PATH}/Common/optional_parameter.properties
        
        local pwd_index=1
        init_weak_passwd_array
        for pre_pwd in ${need_check_value_list};do
            for  tmp_passwd in ${weak_passwd_array[@]};do
                if [[ "${pre_pwd}" =~ .*${tmp_passwd}.* ]];then
                    log_echo "ERROR" "There is weak passwd,check your configuration again"
                    exit 1
                fi
            done
            
            check_pwd "${pre_pwd}" "db" "plain"
            if [ $? -ne 0 ];then
                local key_name=$(echo ${user_name_list} | awk "{print \$$pwd_index}")
                if [[ ! "$key_name" =~ .*_pwd$ ]]; then
                    key_name="${key_name}_pwd"
                fi
                log_echo "ERROR" "$key_name password do not match the rule"
                exit 1
            fi
            ((pwd_index++))
        done
        pre_root_pwd=${root}
        
        decrypt_sopuser_pwd=${sopuser}
        encrypt_password ${decrypt_sopuser_pwd}
        sopuser_pwd=${encryptPasswd}
        
        decrypt_ossuser_pwd=${ossuser}
        encrypt_password ${decrypt_ossuser_pwd}
        ossuser_pwd=${encryptPasswd}
        
        decrypt_ossadm_pwd=${ossadm}
        encrypt_password ${decrypt_ossadm_pwd}
        ossadm_pwd=${encryptPasswd}
        
        
        decrypt_web_admin_pwd=${web_admin}
        encrypt_password ${decrypt_web_admin_pwd}
        web_admin_pwd=${encryptPasswd}
        
        decrypt_protect_pwd=${protect}
        encrypt_password ${decrypt_protect_pwd}
        protect_pwd=${encryptPasswd}
        
        decrypt_redis_dbuser_pwd=${redis_dbuser}
        encrypt_password ${decrypt_redis_dbuser_pwd}
        redis_dbuser_pwd=${encryptPasswd}
        
        decrypt_zenith_sys_pwd=${zenith_sys}
        encrypt_password ${decrypt_zenith_sys_pwd}
        zenith_sys_pwd=${encryptPasswd}
        
        
        sysomc_pwd=${sysomc}
        
        sftpossuser_pwd=${sftpossuser}
        
        dvshareuser_pwd=${dvshareuser}
        
        devdata_pwd=${devdata}
        
        paasinter_pwd=${paasinter}
        
        I2kNfv_pwd=${I2kNfv}
        
        cie_snmp_pwd=${cie_snmp}
        
        sftp_admin_pwd=${sftp_admin}
        
        sftp_nbiuser_pwd=${sftp_nbiuser}

        sftp_mmluser_pwd=${sftp_mmluser}
        
        sftp_assetuser_pwd=${sftp_assetuser}
        
        DV_CERT_DRIVER_pwd=${DV_CERT_DRIVER}
        
        tool_mainast_pwd=${tool_mainast}
        
        tool_ideploy_pwd=${tool_ideploy}

        cs_service_pwd=${cs_service}
        
        tool_dms_pwd=${tool_dms}
        tool_das_pwd=${tool_das}
        tool_easyms_pwd=${tool_easyms}
        export dms_db_pwd=${dms_db}
        export easyms_db_pwd=${easyms_db}
        if [ "X${DV_TOOLKIT_INSTALL}" == "XYes" ];then
            user_name_list="tool_dms tool_das tool_easyms dms_db easyms_db"
            pwd_index=1
            for pre_pwd in "${tool_dms}" "${tool_das}" "${tool_easyms}" "${dms_db}" "${easyms_db}";do
                for  tmp_passwd in ${weak_passwd_array[@]};do
                    if [[ "${pre_pwd}" =~ .*${tmp_passwd}.* ]];then
                        log_echo "ERROR" "There is weak passwd,check your configuration again"
                        exit 1
                    fi
                done
                
                check_pwd "${pre_pwd}" "db" "plain"
                if [ $? -ne 0 ];then
                    local key_name=$(echo ${user_name_list} | awk "{print \$$pwd_index}")
                    log_echo "ERROR" "${key_name}_pwd password do not match the rule"
                    exit 1
                fi
                ((pwd_index++))
            done
        fi
        
        export csweb_db_pwd=${csweb_db}
        if [ -z "${csweb_db}" ];then
            export csweb_db_pwd=${easyms_db}
        fi
        
        export pmdashboard_db_pwd=${pmdashboard_db}
        if [ -z "${pmdashboard_db}" ];then
            export pmdashboard_db_pwd=${easyms_db}
        fi

        if [ ! -z "${ca_cert}" ];then
            encrypt_password ${ca_cert}
        else
            encryptPasswd=""
        fi
        
        echo "ca_cert_pwd=${encryptPasswd}" > ${PreSet_PATH}/pre_install.properties
        
        echo "MED_ACCESS_KEY=${MED_ACCESS_KEY}" >> ${PreSet_PATH}/pre_install.properties
        
        encrypt_password ${MED_SECRET_KEY}
        
        echo "MED_SECRET_KEY=${encryptPasswd}" >> ${PreSet_PATH}/pre_install.properties
        
        echo "UTM_ACCESS_KEY=${UTM_ACCESS_KEY}" >> ${PreSet_PATH}/pre_install.properties
        
        encrypt_password ${UTM_SECRET_KEY}
        
        echo "UTM_SECRET_KEY=${encryptPasswd}" >> ${PreSet_PATH}/pre_install.properties
        
        echo "dv_ca_crt=${dv_ca_crt}" >> ${PreSet_PATH}/pre_install.properties
        
        echo "dv_ca_key=${dv_ca_key}" >> ${PreSet_PATH}/pre_install.properties
    elif [ -f ${PreSet_PATH}/Common/pwfile.json ];then
        cp -f ${PreSet_PATH}/Common/pwfile.json ${PreSet_PATH}/UniEp/
        if [ "X${is_upgrade}" == "XYes" ];then
            chown ossadm:ossgroup ${PreSet_PATH}/UniEp/pwfile.json
        fi
        init_pwd_encrypt
        if [ ! -f ${PreSet_PATH}/Common/passwords.properties ];then
            pwd_of_passwords_properties
        fi
        if [ -d ${PreSet_PATH}/tools/sub_ca ];then
            if [ ! -f ${PreSet_PATH}/tools/sub_ca/ca_cert_pwd.properties ];then
                enter_ca_pwd
                if [ $? -eq 0 ];then
                    encrypt_password ${ca_cert_pwd}
                    echo "ca_cert_pwd=${encryptPasswd}" > ${PreSet_PATH}/tools/sub_ca/ca_cert_pwd.properties
                fi
            fi
        fi
    else
        if [ "X${DV_UNIFIED_PWD}" == "XYes" ];then
            enter_password "Please input dv unified password" "db"
            if [ $? -eq 0 ];then
                unified_pwd="${de_pwd}"
                unified_pwd_encrypt="${en_pwd}"
            fi
        fi
        if [ ! -f "/.dockerenv" ] && [ "X${dv_force_change_os_pwd}" == "XYes" ];then
            enter_password "Please input dv new root password" "os"
            if [ $? -eq 0 ];then
                new_root_pwd="${de_pwd}"
                new_root_pwd_encrypt="${en_pwd}"
            fi
            enter_password "Please input dv new sshusr password" "os"
            if [ $? -eq 0 ];then
                new_sshusr_pwd="${de_pwd}"
                new_sshusr_pwd_encrypt="${en_pwd}"
            fi
        fi

        if [ -d ${PreSet_PATH}/tools/sub_ca ];then
            if [ ! -f ${PreSet_PATH}/tools/sub_ca/ca_cert_pwd.properties ];then
                enter_ca_pwd
                if [ $? -eq 0 ];then
                    encrypt_password ${ca_cert_pwd}
                    echo "ca_cert_pwd=${encryptPasswd}" > ${PreSet_PATH}/tools/sub_ca/ca_cert_pwd.properties
                fi
            fi
        fi
        enter_password "Please input os(sopuser) password" "db" "cipher"
        if [ $? -eq 0 ];then
            sopuser_pwd=${en_pwd}
        fi
        enter_password "Please input os(ossuser) password" "db" "cipher"
        if [ $? -eq 0 ];then
            ossuser_pwd=${en_pwd}
        fi
        enter_password "Please input os(ossadm) password" "db" "cipher"
        if [ $? -eq 0 ];then
            ossadm_pwd=${en_pwd}
        fi
        enter_password "Please input web login user(admin) password" "db" "cipher"
        if [ $? -eq 0 ];then
            web_admin_pwd=${en_pwd}
        fi
        enter_password "Please input protect password" "db" "cipher"
        if [ $? -eq 0 ];then
            protect_pwd=${en_pwd}
        fi
        enter_password "Please input db(redis-dbuser) password" "db" "cipher" "dbuser"
        if [ $? -eq 0 ];then
            redis_dbuser_pwd=${en_pwd}
        fi
        enter_password "Please input db(zenith-sys) password" "db" "cipher" "sys"
        if [ $? -eq 0 ];then
            zenith_sys_pwd=${en_pwd}
        fi
        
        pwd_of_passwords_properties
    fi
    
    SD_pwfile="${PreSet_PATH}/SD_pwfile.properties"
    if [ -f "${SD_pwfile}" ];then
        log_echo "INFO" "SD_pwfile has been exist."
    fi
    
    create_uniep_pwfile
}

function init_common()
{
    if [ "X${pre_install_key}" == "XPreInstall" ];then
        create_toolkit_user_dict
        pre_pipe_parameters=$(cat)
        local flag=0
        while read line || [[ -n ${line} ]]
        do
            if [ -z "${line}" ];then
                continue
            fi
            key=${line}
            value=$(echo ${pre_pipe_parameters} | sed "s/ /\n/g" | awk -F"^${key}:" '{print $2}' | tr -s '\n' | tail -1)
            if [ "X${value}" == "X" ];then
                if [ "X${DV_TOOLKIT_INSTALL}" != "XYes" ] && [[ -v toolkit_user_dict[$key] ]]; then
                    ## 更新密码为随机密码
                    value="${toolkit_user_dict[$key]}"
                else
                    if [ -f "/.dockerenv" ];then
                        log_echo "INFO" "This pod preset.Please confirm whether you have entered ${key} and it's password."
                    else
                        log_echo "ERROR" "Please confirm whether you have entered ${key} and it's password."
                    fi
                    flag=1
                fi
            fi
            export "${key}=${value}"
        done < ${PreSet_PATH}/Common/user_name.properties
        
        if [ -f "/.dockerenv" ];then
            log_echo "INFO" "The is pod preset.of init_common"
        else
            [ ${flag} -eq 1 ] && exit 1
        fi
        
        while read line || [[ -n ${line} ]]
        do
            if [ -z "${line}" ];then
                continue
            fi
            
            key=${line}
            value=$(echo ${pre_pipe_parameters} | sed "s/ /\n/g" | awk -F"^${key}:" '{print $2}' | tr -s '\n' | tail -1)
            if [ "X${value}" == "X" ];then
                log_echo "INFO" "This preset ${key} is null or not has ${key}."
                continue
            fi
            export "${key}=${value}"
        done < ${PreSet_PATH}/Common/optional_parameter.properties
    fi
    
    if [ "X${SILENCE_INSTALLMODE}" == "XTRUE" -a "X${pre_root_pwd}" == "X" ];then
        log_echo "INFO" "The SILENCE_INSTALLMODE=${SILENCE_INSTALLMODE} pre_root_pwd is null.need to get it."
        if [ -z "${ROOT_PWD}" ];then
            log_echo "ERROR" "The SILENCE_INSTALLMODE=${SILENCE_INSTALLMODE} but ROOT_PWD is null."
            exit 1
        fi
        
        decrypt_password "${ROOT_PWD}"
        pre_root_pwd=${decryptPasswd}
    fi

    chmod 1777 /var/tmp
    chmod 1777 /tmp
    touch ${action_tag}
    chmod 777 ${action_tag}
    sed -i "s#{{i2k_user_home}}#$i2k_user_home#g" ${dv_cfg_file}.tmp
    
    if [ "X${changeUUidToDisk}" == "XYes" ];then
        sh ${PreSet_PATH}/tools/uuidToDisk/changeUUidToDisk.sh "change"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "changeUUidToDisk failed."
            exit 1
        fi
    fi
    if [ -d ${PreSet_PATH}/tools/sub_ca ];then
        cd   ${PreSet_PATH}/tools/sub_ca
        if [ ! -f "${PreSet_PATH}/tools/sub_ca/DigitalView_CA.csr" ];then
            log_echo "ERROR" "{PreSet_PATH}/tools/sub_ca/DigitalView_CA.csr does not exist, please check"
            exit 1
        fi
        if [ ! -f "${PreSet_PATH}/tools/sub_ca/DigitalView_CA.key" ];then
            log_echo "ERROR" "{PreSet_PATH}/tools/sub_ca/DigitalView_CA.key does not exist, please check"
            exit 1
        fi
        if [ ! -f "${PreSet_PATH}/tools/sub_ca/sub_ca.cer" ];then
            log_echo "ERROR" "{PreSet_PATH}/tools/sub_ca/sub_ca.cer does not exist, please check"
            exit 1
        fi
        if [ ! -f "${PreSet_PATH}/tools/sub_ca/root_ca.pem" ];then
            log_echo "ERROR" "{PreSet_PATH}/tools/sub_ca/root_ca.pem does not exist, please check"
            exit 1
        fi
        openssl x509 -inform DER -in ${PreSet_PATH}/tools/sub_ca/sub_ca.cer -out tmp.crt
        if [ $? -eq 0 ];then
            openssl verify -CAfile root_ca.pem tmp.crt >/dev/null
            if [ $? -ne 0 ];then
                log_echo "ERROR" "{PreSet_PATH}/tools/sub_ca/root_ca.pem can not verify sub_ca.cer"
                rm tmp.crt
                exit 1
            fi 
            rm tmp.crt     
        else
            log_echo "ERROR" "{PreSet_PATH}/tools/sub_ca/root_ca.pem can not convert to X509 format"
            exit 1
        fi
        cd -
    fi
    #for os\db\cert password preset
    input_preset_pwd

    local dvca_home=/home/<USER>/etc/ssl/dvca
    sed -i 's/SUBJECT_C=.*/SUBJECT_C='"${countryName}"'/g' ${PreSet_PATH}/Common/dv-ca.properties
    sed -i "s/SUBJECT_O=.*/SUBJECT_O=${organizationName}/g" ${PreSet_PATH}/Common/dv-ca.properties 
    sed -i "s/SUBJECT_CN=.*/SUBJECT_CN=${commonName}/g" ${PreSet_PATH}/Common/dv-ca.properties
    rm -rf ${PreSet_PATH}/Common/dvca
    mkdir -p ${PreSet_PATH}/Common/dvca
    cp -f ${PreSet_PATH}/Common/dv-ca.properties  ${PreSet_PATH}/Common/dvca

    if [ -d ${PreSet_PATH}/tools/sub_ca ];then
        log_echo "INFO" "Connecting to the DigitalView V8 and V8 Two NMSs"
        cp -f ${PreSet_PATH}/tools/sub_ca/DigitalView_CA.csr ${PreSet_PATH}/Common/dvca
        cp -f ${PreSet_PATH}/tools/sub_ca/DigitalView_CA.key ${PreSet_PATH}/Common/dvca
        cp -f ${PreSet_PATH}/tools/sub_ca/root_ca.pem ${PreSet_PATH}/Common/dvca
        cp -f ${PreSet_PATH}/tools/sub_ca/sub_ca.cer ${PreSet_PATH}/Common/dvca

        sed -i "s#CREATE_MODE=.*#CREATE_MODE=1#" ${PreSet_PATH}/Common/dvca/dv-ca.properties
        sed -i "s#CA_KEY_FILE=.*#CA_KEY_FILE=${dvca_home}/DigitalView_CA.key#" ${PreSet_PATH}/Common/dvca/dv-ca.properties
        sed -i "s#CA_CERT_FILE=.*#CA_CERT_FILE=${dvca_home}/sub_ca.cer#" ${PreSet_PATH}/Common/dvca/dv-ca.properties
        sed -i "s#CA_CHAIN_FILE=.*#CA_CHAIN_FILE=${dvca_home}/root_ca.pem#" ${PreSet_PATH}/Common/dvca/dv-ca.properties
        ca_cert_pwd=$(cat ${PreSet_PATH}/tools/sub_ca/ca_cert_pwd.properties |awk -F'=' '{print $2}')
        decrypt_password ${ca_cert_pwd}
        ca_cert_pwd=${decryptPasswd}
        ## ca_cert_pwd 编码后暂存,会在安装DVCommonConfigService时进行SOP加密
        local tmp_ca_cert_pwd=$(echo "${ca_cert_pwd}" | base64 | sed "s/#/\\\#/g")
        sed -i "s#^CA_KEY_PWD=.*#CA_KEY_PWD=${tmp_ca_cert_pwd}#" ${PreSet_PATH}/Common/dvca/dv-ca.properties
        openssl x509 -inform der -in ${PreSet_PATH}/Common/dvca/sub_ca.cer -outform pem -out  ${PreSet_PATH}/Common/dvca/sub_ca.pem
        mv ${PreSet_PATH}/Common/dvca/sub_ca.pem ${PreSet_PATH}/Common/dvca/sub_ca.cer
    else
        log_echo "INFO" "${PreSet_PATH}/tools/sub_ca is not exist,CREATE_MODE=0."
        sed -i "s#CREATE_MODE=.*#CREATE_MODE=0#" ${PreSet_PATH}/Common/dvca/dv-ca.properties
    fi

    init_register_config_file

    return 0
}

function init_register_config_file()
{
    ## 初始化registerConfigInfo.properties文件
    local register_config_file="${PreSet_PATH}/Common/registerConfigInfo.properties"
    [ -f $register_config_file ] && rm -f "${register_config_file:?}"
    touch ${register_config_file}
    local opt_custom_path=$(echo "${custom_path_list}" | awk -F "," '{print $1}')
    if [ "X${opt_custom_path}" != "X" -a "X${opt_custom_path}" != "X{{OPT_CUSTOM_PATH}}" -a "X${opt_custom_path}" != "X/opt" ]; then
        add_config "OPT_CUSTOM_PATH" "${opt_custom_path}" "${register_config_file}"
    fi
    local home_custom_path=$(echo "${custom_path_list}" | awk -F "," '{print $2}')
    if [ "X${home_custom_path}" != "X" -a "X${home_custom_path}" != "X{{HOME_CUSTOM_PATH}}" -a "X${home_custom_path}" != "X/home" ]; then
        add_config "HOME_CUSTOM_PATH" "${home_custom_path}" "${register_config_file}"
        add_config "VAR_CUSTOM_PATH" "${home_custom_path}/var" "${register_config_file}"
    fi
    local tmp_custom_path=$(echo "${custom_path_list}" | awk -F "," '{print $3}')
    if [ "X${tmp_custom_path}" != "X" -a "X${tmp_custom_path}" != "X{{TMP_CUSTOM_PATH}}" -a "X${tmp_custom_path}" != "X/tmp" ]; then
        add_config "TMP_CUSTOM_PATH" "${tmp_custom_path}" "${register_config_file}"
    fi
    log_echo "end to update config to $register_config_file."
}

function check_get_root_pwd()
{
    if [ "X${pre_install_key}" == "XPreInstall" -o "X${SILENCE_INSTALLMODE}" == "XTRUE" ];then
        log_echo "INFO" "It is PreInstall or SILENCE_INSTALLMODE is TRUE, no need to input root pwd."
        if [ "X${pre_root_pwd}" == "X" ];then
            log_echo "ERROR" "the pre_root_pwd is null."
            exit 1
        fi
        root_password="${pre_root_pwd}"
        
        return 0
    fi
    
    return 1
}

function uneip_auto_mount()
{
    Check_execute "UniEp_AUTO_MOUNT"
    if [ $? -eq 0 ];then
        return 0
    fi
    
    if [ "X${netWorkType}" != "XO" ];then
        if [ "${AUTO_DISK_MOUNT}" != "TRUE" -o "X${EXTEND_98G_DISK}" == "X" -o "X${UniEP_ExtendDisk_isMounted}" == "XTRUE" -o "X${EXTEND_98G_DISK}" == "X{{EXTEND_98G_DISK}}" ];then
            sed -i "s/lv_opt=.*/lv_opt=15/" ${PreSet_PATH}/Common/ext_disk.properties.tmp
        fi
    fi
    
    check_dir_size
    
    log_echo "INFO" "Begin to check extend disk at UniEp node."
    check_ext_disk "uniep"
    ext_check_ret=$?

    if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ] && [ "${AUTO_DISK_MOUNT}" == "TRUE" ]; then
        rebuild_tmp_cfg_file_disk ${PreSet_PATH}/Common/DV_config.properties.tmp
        if [ $? -ne 0 ]; then
            log_echo "ERROR" "Rebuild disk symbol in ${PreSet_PATH}/Common/DV_config.properties.tmp failed, please check."
            exit 1
        fi
    fi
    check_local_disk "$(dirname ${INSTALL_PATH})/pub" "${UNIEP_PUB_DISK}" "${UNIEP_PUB_least_size}" "vg_uniep"
    local_check_ret=$?
    if [ ${ext_check_ret} -ne 0 -o ${local_check_ret} -ne 0 ];then
        log_echo "ERROR" "Check disk space failed at uniep node, please check..."
        exit 1
    fi
    
    systemctl daemon-reload
    if [ "${AUTO_DISK_MOUNT}" == "TRUE" ];then
        if [ "X${netWorkType}" == "XM" ];then
            tmp_dir=/tmp/DVPreSet
            mkdir -p ${tmp_dir}
            db_file_list="DV_config.properties.tmp DV_PreSet_APP.sh DV_PreSet_DB.sh utils_common.sh utils_uniep.sh utils_os.sh ext_disk.properties.tmp add_cert.py repair_iptables.sh repair_iptables_lvs.sh dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service pre_get_disk_symbol.properties netWorkTypeOfDvConfigParameters.config nonstandard_ext_disk.properties"
            for db_filename in ${db_file_list}
            do
                cp -rf ${PreSet_PATH}/Common/${db_filename} ${tmp_dir}
            done
            mkdir -p ${tmp_dir}/tools
            cp -rf ${PreSet_PATH}/tools/ATAEExtend ${tmp_dir}/tools
            source   ${PreSet_PATH}/zenith_paramgroup_file.properties
        fi
        
        if [ "X${UniEP_ExtendDisk_isMounted}" != "XTRUE" ];then
            if [ -f "${ATAE_extend_script}" ];then
                ATAE_Extend_Disk
            fi
            
            if [ "X${EXTEND_98G_DISK}" != "X" -a "X${EXTEND_98G_DISK}" != "X{{EXTEND_98G_DISK}}" -a ! -f "${UTILS_PATH}/mount_extdisk_finish" ];then
                auto_mount_ext
                sed -i "/^current_extdisk_name=/d" ${dv_cfg_file}.tmp
            fi
        fi

        check_log_disk_has_extend
        if [ $? -ne 0 ]; then
            if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ];then
                preinstall_config_disk_symbol  "vg_log_extend" "${dv_cfg_file}.tmp"
            fi
            if [ "X${LOG_EXTEND_DISK}" != "X" -a "X${LOG_EXTEND_DISK}" != "X{{LOG_EXTEND_DISK}}" ] && [ -f "${ATAE_extend_script}" ]; then
                auto_extend_log_disk
            fi
        fi

        if [ "X${UNIEP_PUB_DISK}" != "X"  ];then
            auto_mount_local "$(dirname ${INSTALL_PATH})/pub" "${UNIEP_PUB_DISK}" "${UNIEP_PUB_least_size}" "vg_uniep" "lv_uniep"
        fi
        
        if [ "X${netWorkType}" == "XO" -o "X${netWorkType}" == "XM" ];then
            tmp_dir=/tmp/DVPreSet
            rm -rf ${tmp_dir:?}
            mkdir -p ${tmp_dir}/tools
            db_file_list="DV_config.properties.tmp DV_PreSet_DB.sh utils_common.sh utils_uniep.sh utils_os.sh ext_disk.properties.tmp add_cert.py dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service pre_get_disk_symbol.properties netWorkTypeOfDvConfigParameters.config nonstandard_ext_disk.properties"
            for db_filename in ${db_file_list}
            do
                cp -rf ${PreSet_PATH}/Common/${db_filename} ${tmp_dir}
            done

            cp -rf ${PreSet_PATH}/tools/ATAEExtend ${tmp_dir}/tools
            
            sh ${tmp_dir}/DV_PreSet_DB.sh auto_mount all_in_one
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Auto mount DBNode_DISK disk at all_in_one node failed, please check detailed log ${tmp_dir}/preset_script.log on the local host."
                exit 1
            fi
        fi
    fi
    
    IS_INSTALL_CMP=$(echo "${IS_INSTALL_CMP}" | tr 'a-z' 'A-Z')
    
    if [ "X${netWorkType}" == "XO" -a "X${IS_INSTALL_CMP}" == "XYES" -a "X${dv_lite_mode}" != "XTRUE" ];then
        check_local_disk "${VS_PATH}" "${VSINDEX_DISK}" "${VS_DISK_least_size}" "vg_vsindex"
        local_check_vsindex=$?

        local_check_kafka=0
        check_empty_param_of_disk "SRVBigData_DISK" "${SRVBigData_DISK}"
        if [ $? -eq 0 ]; then
            log_echo "INFO" "SRVBigData_DISK is null, not check it."
        else
            check_local_disk "${KAFKA_PATH}" "${SRVBigData_DISK}" "${KAFKA_DISK_least_size}" "vg_kafka"
            local_check_kafka=$?
        fi


        if [ ${local_check_vsindex} -ne 0 -o ${local_check_kafka} -ne 0 ];then
            log_echo "ERROR" "Check disk space failed at uniep node, please check..."
            exit 1
        fi
        if [ "${AUTO_DISK_MOUNT}" == "TRUE" ];then
            if [ "X${VSINDEX_DISK}" != "X" -a "X${VSINDEX_DISK}" != "X{{VSINDEX_DISK}}" ];then
                auto_mount_local "${VS_PATH}" "${VSINDEX_DISK}" "${VS_DISK_least_size}" "vg_vsindex" "lv_vsindex"
            fi
            if [ "X${SRVBigData_DISK}" != "X" -a "X${SRVBigData_DISK}" != "X{{SRVBigData_DISK}}" ];then
                auto_mount_local "${KAFKA_PATH}" "${SRVBigData_DISK}" "${KAFKA_DISK_least_size}" "vg_kafka" "lv_kafka"
            fi
        fi
    else
        mkdir -p "${VS_PATH}"
        chmod 755 "${VS_PATH}"
        mkdir -p "${KAFKA_PATH}"
        chmod 755 "${KAFKA_PATH}"
    fi

    [ -d "$(dirname ${INSTALL_PATH})/pub" ] && chmod 755 $(dirname ${INSTALL_PATH})/pub && chown root:ossgroup  $(dirname ${INSTALL_PATH})/pub
    
    if [ -d "${INSTALL_PATH}/share" ];then
        share_uid=$(stat -c %u ${INSTALL_PATH}/share)
        if [ "X${share_uid}" != "X3001" ];then
            chmod 755 -R ${INSTALL_PATH}/share
        fi
    fi
    
    if [ "X$(whoami)" == "Xroot" ];then
        add_etc_hosts
    fi
    echo "UniEp_AUTO_MOUNT : success" >> ${action_tag}
}

function UniEp_install()
{
    uneip_auto_mount
    
    check_dirs_io "${check_io_uniepnode}"
    
    Check_execute "UniEp_install"
    if [ $? -eq 0 ];then
        return 0
    fi
    
    log_echo "INFO" "Begin to preset UniEp node."
    grep "^::1.*[[:blank:]]localhost" /etc/hosts || (echo "" >> /etc/hosts && echo "::1    localhost" >> /etc/hosts)
    grep "^127.0.0.1.*[[:blank:]]localhost" /etc/hosts | grep -wF "127.0.0.1" || (echo "" >> /etc/hosts && echo "127.0.0.1   localhost" >> /etc/hosts)
    
    echo "${ossadm_user_pwd}" | sh ${PreSet_PATH}/UniEp/uniEP_install.sh "${source_file}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Preset UniEp node failed"
        mv /opt/pub/UniEPMgr*.zip ${PreSet_PATH}/UniEp/
        exit 1
    fi
    mv /opt/pub/UniEPMgr*.zip ${PreSet_PATH}/UniEp/
    [ -d "$(dirname ${INSTALL_PATH})/pub" ] && chmod 750 $(dirname ${INSTALL_PATH})/pub
    echo "UniEp_install : success" >> ${action_tag}
    
    if [ -f ${CA_PATH}/ca.cer ];then
        rm -rf ${CA_PATH}/ca.cer >/dev/null 2>&1 
    fi
    
    if [ -d "${i2k_user_home}" -a -f "${UTILS_PATH}/mount_extdisk_finish" ];then
        cp ${UTILS_PATH}/mount_extdisk_finish ${i2k_user_home}
        chown ${i2k_user_name}:${i2k_group_name} ${i2k_user_home}/mount_extdisk_finish
        chmod 600 ${i2k_user_home}/mount_extdisk_finish
        if [ -f "${UTILS_PATH}/new_partition_finish" ];then
            cp ${UTILS_PATH}/new_partition_finish ${i2k_user_home}/new_partition_finish
            chown ${i2k_user_name}:${i2k_group_name} ${i2k_user_home}/new_partition_finish
            chmod 600 ${i2k_user_home}/new_partition_finish
        fi
    fi
    
    if [ "X${netWorkType}" != "XO" -a ! -f /.dockerenv ];then
        sed -i "s/lv_opt=.*/lv_opt=40/" ${PreSet_PATH}/Common/ext_disk.properties.tmp
    fi
    
    if [ -d "${INSTALL_PATH}/backuptmp" ];then
        chown ossadm:${i2k_group_name} ${INSTALL_PATH}/backuptmp
        chmod 770 ${INSTALL_PATH}/backuptmp
    fi
    
    config_sudo  sysomc "${sudo_sysomc_list}"
    set_osuser_passwd_for_uniep

    config_ssh_sftp_seperate

    grep -w "^UPGRADEWIZARD=open" ${INSTALL_PATH}/manager/var/etc/common/custom.cfg > /dev/null 2>&1
    if [ $? -ne 0 ];then
        sed -i "/^UPGRADEWIZARD=/d" ${INSTALL_PATH}/manager/var/etc/common/custom.cfg
        echo "UPGRADEWIZARD=open" >> ${INSTALL_PATH}/manager/var/etc/common/custom.cfg
    fi
    if [ "X${netWorkType}" == "XM" ];then
        if [ "X${IPType}" != "X1" ];then
            su - ossadm -c "scp -o NumberOfPasswordPrompts=0 -o StrictHostKeyChecking=no -P ${i2k_ssh_port}  ${INSTALL_PATH}/manager/var/etc/common/custom.cfg ${DVSecondary_IPV4}:${INSTALL_PATH}/manager/var/etc/common/"
        else
            su - ossadm -c "scp -o NumberOfPasswordPrompts=0 -o StrictHostKeyChecking=no -P ${i2k_ssh_port}  ${INSTALL_PATH}/manager/var/etc/common/custom.cfg [${DVSecondary_MGR_IPV6}]:${INSTALL_PATH}/manager/var/etc/common/"
        fi
        if [ $? -ne 0 ];then
            log_echo "ERROR" "scp ${INSTALL_PATH}/manager/var/etc/common/custom.cfg to secondary node failed"
        fi
    fi
}

function update_certificate_password()
{
    #reentry problem handling
    if [ -d ${UTILS_PATH}/certificate.bak ];then
        cp -rf ${UTILS_PATH}/certificate.bak/* ${UTILS_PATH}/certificate/
    else
        cp -rf ${UTILS_PATH}/certificate/ ${UTILS_PATH}/certificate.bak/
    fi
    
    new_pwd=$(generate_random_pwd 16)
    new_pwd=$(su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python -c \"from random import shuffle;pwd_list = list('$new_pwd');shuffle(pwd_list);print(''.join(pwd_list))\"")
    
    cert_lists="controller_server.p12 outserver.p12 foreign_client.p12 caTrustStore.jks nodeKeyStore.jks uniagent_client.jks trustcerts.jks openapi_server.p12"
    for cert_name in ${cert_lists}
    do
        store_pass=$(cat "${UTILS_PATH}/certificate/manifest.json" | su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python -c \"import json;import sys;datas = json.load(sys.stdin);print(datas['filelist']['${cert_name}']['storePass'])\"")
        store_pass=$(su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('$store_pass'); print(tmp)\"")
        if [ "${cert_name##*.}" == "p12" ];then
            change_pwd_pkcs12 "$store_pass" "$new_pwd" "${UTILS_PATH}/certificate/${cert_name}"
        elif [ "${cert_name##*.}" == "jks" ];then
            change_pwd_jks "$store_pass" "$new_pwd" "${UTILS_PATH}/certificate/${cert_name}"
        fi
    done
    
    replace_json_value "storePass" "$new_pwd" "${UTILS_PATH}/certificate/manifest.json"
    replace_json_value "keyPass" "$new_pwd" "${UTILS_PATH}/certificate/manifest.json"
    if [ -d ${UTILS_PATH}/certificate.bak ];then
        rm -rf ${UTILS_PATH}/certificate.bak
    fi
}

function set_uniep_sysomc_pwd()
{
    . ${dv_cfg_file}.tmp
    sysomc_pwd=$(cat ${PreSet_PATH}/Common/passwords.properties | grep -w sysomc_pwd | awk -F 'sysomc_pwd=' '{print $2}')
    sysomc_pwd=$(su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('${sysomc_pwd}'); print(tmp)\"")
    if [ "X${sysomc_pwd}" == "X" ]; then
        log_echo "ERROR" "Get password of ${system_operation_user} failed, please check!" 
        exit_script
    fi
    sysomc_user_home=$(cat ${dv_cfg_file}.tmp |grep "^system_operation_userhome=" |awk -F'=' '{print $2}')
    [ -f "${PreSet_PATH}/UniEp/sysomc_need_del" ] && set_user_passwd "${system_operation_user}" "${sysomc_pwd}"
    [ -f "${sysomc_user_home}/sysomc_by_virtual" ] && set_user_passwd "${system_operation_user}" "${sysomc_pwd}"
    sysomcpwd_shadow=$(grep "^${system_operation_user}:" /etc/shadow |awk -F':' '{print$2}')
    [[ ${sysomcpwd_shadow} =~ ^\$.* ]] || set_user_passwd "${system_operation_user}" "${sysomc_pwd}"
}

function init_firstlogin_passwd()
{
    if [ ! -f "/.dockerenv" ] && [ "X${dv_force_change_os_pwd}" == "XYes" ];then
        new_sshusr_pwd=$(cat ${PreSet_PATH}/Common/passwords.properties| grep -w new_sshusr_pwd | awk -F 'new_sshusr_pwd=' '{print $2}')
        new_sshusr_pwd=$(su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('$new_sshusr_pwd'); print(tmp)\"")
        if [ "X${new_sshusr_pwd}" == "X" ]; then
            log_echo "ERROR" "Init password for sshuser failed, please check!" 
            exit_script
        else
            log_echo "INFO" "Init password for sshuser successed." 
        fi

        new_root_pwd=$(cat ${PreSet_PATH}/Common/passwords.properties| grep -w new_root_pwd | awk -F 'new_root_pwd=' '{print $2}')
        new_root_pwd=$(su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('$new_root_pwd'); print(tmp)\"")
        if [ "X${new_root_pwd}" == "X" ]; then
            log_echo "ERROR" "Init password for root failed, please check!" 
            exit_script
        else
            log_echo "INFO" "Init password for root successed." 
        fi
    fi
}

function init_passwd()
{
    log_echo "INFO" "Begin to initial password."
    if [ "X${pre_install_key}" == "XPreInstall" -o "X${SILENCE_INSTALLMODE}" == "XTRUE" ];then
        if [ "X${pre_root_pwd}" == "X" ];then
            log_echo "ERROR" "the pre_root_pwd is null."
            exit 1
        fi
        root_password="${pre_root_pwd}"
        if [ "X${root_password}" == "X" ];then
            log_echo "ERROR" "the root_password decrypt failed. root_password is null."
            exit 1
        fi
    fi
    
    i2k_userpasswd=$(cat ${PreSet_PATH}/Common/pwfile.json| sed 's/}/\n/g' | grep -w ossuser | awk -F : '{print $3}')
    i2k_userpasswd=${i2k_userpasswd#*\"}
    i2k_userpasswd=${i2k_userpasswd%%\"*}
    decrypt_password ${i2k_userpasswd}
    i2k_userpasswd=${decryptPasswd}
    if [ "X${i2k_userpasswd}" == "X" ]; then
        log_echo "ERROR" "Init password for ${i2k_user_name} failed, please check!" 
        exit_script
    else
        log_echo "INFO" "Init password for ${i2k_user_name} successed." 
    fi
    
    
    
    sysomc_pwd=$(cat ${PreSet_PATH}/Common/passwords.properties| grep -w sysomc_pwd | awk -F 'sysomc_pwd=' '{print $2}')
    sysomc_pwd=$(su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('$sysomc_pwd'); print(tmp)\"")
    if [ "X${sysomc_pwd}" == "X" ]; then
        log_echo "ERROR" "Init password for ${system_operation_user} failed, please check!" 
        exit_script
    else
        log_echo "INFO" "Init password for ${system_operation_user} successed." 
    fi
    

    
    sftpossuser_pwd=$(cat ${PreSet_PATH}/Common/passwords.properties| grep -w sftpossuser_pwd | awk -F 'sftpossuser_pwd=' '{print $2}')
    sftpossuser_pwd=$(su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('$sftpossuser_pwd'); print(tmp)\"")
    if [ "X${sftpossuser_pwd}" == "X" ]; then
        log_echo "ERROR" "Init password for ${system_sftp_user} failed, please check!" 
        exit_script
    else
        log_echo "INFO" "Init password for ${system_sftp_user} successed." 
    fi
    
    init_firstlogin_passwd 
    sopuser_userpasswd=$(cat ${PreSet_PATH}/Common/pwfile.json| sed 's/}/\n/g' | grep -w sopuser | awk -F : '{print $3}')
    sopuser_userpasswd=${sopuser_userpasswd#*\"}
    sopuser_userpasswd=${sopuser_userpasswd%%\"*}
    decrypt_password ${sopuser_userpasswd}
    sopuser_userpasswd=${decryptPasswd}
    if [ "X${sopuser_userpasswd}" == "X" ]; then
        log_echo "ERROR" "Init password for ${sop_user_name} failed, please check!" 
        exit_script
    else
        log_echo "INFO" "Init password for ${sop_user_name} successed." 
    fi
    
    devdata_pwd=$(cat ${PreSet_PATH}/Common/passwords.properties| grep -w devdata_pwd | awk -F 'devdata_pwd=' '{print $2}')
    devdata_pwd=$(su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('$devdata_pwd'); print(tmp)\"")
    if [ "X${devdata_pwd}" == "X" ]; then
        log_echo "ERROR" "Init password for ${icnfg_devdata_user_name} failed, please check!" 
        exit_script
    else
        log_echo "INFO" "Init password for ${icnfg_devdata_user_name} successed." 
    fi
    
    ossadm_userpasswd=$(cat ${PreSet_PATH}/Common/pwfile.json| sed 's/}/\n/g' | grep -w ossadm | awk -F : '{print $3}')
    ossadm_userpasswd=${ossadm_userpasswd#*\"}
    ossadm_userpasswd=${ossadm_userpasswd%%\"*}
    decrypt_password ${ossadm_userpasswd}
    ossadm_userpasswd=${decryptPasswd}
    if [ "X${ossadm_userpasswd}" == "X" ]; then
        log_echo "ERROR" "Init password for ${ossadm_user_name} failed, please check!" 
        exit_script
    else
        log_echo "INFO" "Init password for ${ossadm_user_name} successed." 
    fi
    
    passwd_list="${i2k_userpasswd}DVPreSet:Flag${dvshareuser_pwd}DVPreSet:Flag${sysomc_pwd}DVPreSet:Flag${sshossuser_pwd}DVPreSet:Flag${sftpossuser_pwd}DVPreSet:Flag${sopuser_userpasswd}DVPreSet:Flag${devdata_pwd}DVPreSet:Flag${new_sshusr_pwd}DVPreSet:Flag${new_root_pwd}"
}

function add_sshpwd_to_pwdlist()
{
    local install_type="$1"
    local random_sshpwd="$(tr -dc "\@\#\%\^\*\_\+\=\,\?\.A-Za-z0-9" </dev/random| head -c 16)"
    encrypt_by_uniep "${random_sshpwd}" ""
    local random_sshpwd_encode="${pwd_return}"
    if [ "X${install_type}" == "Xextend" ];then
        encrypt_by_uniep "${random_sshpwd}" "SOP"
        random_sshpwd_encode="${pwd_return}"
    fi
    
    if [ -f "${PreSet_PATH}/get_kafka/kafka.tar.gz" ];then
        log_echo "INFO" "get kafka for extend."
        tar -zxvf "${PreSet_PATH}/get_kafka/kafka.tar.gz" -C "${PreSet_PATH}/Common/"
        rm -rf "${PreSet_PATH}/Common/kafka/server.p12"
        rm -rf "${PreSet_PATH}/Common/kafka/client.p12"
    fi
    
    local kafka_ca_encode_cert_pwd=$(grep -A 3 trust.jks ${PreSet_PATH}/Common/kafka/manifest.json | grep keyPass | awk -F ':' '{print $2}' | tr -d " |\"|,")

    local kafka_ca_cert_pwd=$(su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('$kafka_ca_encode_cert_pwd'); print(tmp)\"")
    if [ $? -ne 0  -o "X${kafka_ca_cert_pwd}" == "X" ];then
        log_echo "ERROR" "get kafka_ca_cert_pwd fail"
        exit 1
    fi
    if [ "X${install_type}" == "Xextend" ];then
        encrypt_by_uniep "${kafka_ca_cert_pwd}" "SOP"
    else
        encrypt_by_uniep "${kafka_ca_cert_pwd}" ""
    fi

    local kafka_store_encode_password="${pwd_return}"
    local kafka_key_encode_password="${pwd_return}"
    
    passwd_list_tmp="${passwd_list}DVPreSet:Flag${random_sshpwd}DVPreSet:Flag${random_sshpwd_encode}DVPreSet:Flag${kafka_ca_cert_pwd}DVPreSet:Flag${kafka_store_encode_password}DVPreSet:Flag${kafka_key_encode_password}"
}

function record_all_nic_by_ip()
{
    local ssh_ip=$1
    log_echo "INFO" "record_all_nic_by_ip ssh_ip=${ssh_ip} start..."
    if [ ! -f ${PreSet_PATH}/tmp_nic/${ssh_ip}/nic.txt ];then
        log_echo "INFO" "The ${PreSet_PATH}/tmp_nic/${ssh_ip}/nic.txt is not exists."
        return 0
    fi
    
    local nic_name=$(grep "inet" ${PreSet_PATH}/tmp_nic/${ssh_ip}/nic.txt |grep -v "ip addr" |grep -w "${ssh_ip}" | awk '{print $NF}' | awk -F':' '{print $1}' )
    if [ "X${nic_name}" == "X" ];then
        log_echo "INFO" "The nic_name=${nic_name} is null."
        return 0
    fi
    
    sed -i "/^${ssh_ip}=/d" ${PreSet_PATH}/ip_nic.txt
    echo "${ssh_ip}=${nic_name}" >> ${PreSet_PATH}/ip_nic.txt
    log_echo "INFO" "record_all_nic_by_ip ssh_ip=${ssh_ip} End."
}

function get_nic_by_IP()
{
    local root_passwd=$1
    local ssh_ip=$2
    log_echo "INFO" "get_nic_by_IP ssh_ip=${ssh_ip} start..."
    mkdir -p ${PreSet_PATH}/tmp_nic/${ssh_ip}/
    auto_smart_ssh ${root_passwd} "root@${ssh_ip} ip addr | grep inet |grep -wF ${ssh_ip}" > ${PreSet_PATH}/tmp_nic/${ssh_ip}/nic.txt
    
    local nic_name=$(grep "inet" ${PreSet_PATH}/tmp_nic/${ssh_ip}/nic.txt |grep -v "ip addr" |grep -w "${ssh_ip}" | awk '{print $NF}' | awk -F':' '{print $1}' )
    if [ "X${nic_name}" == "X" ];then
        log_echo "ERROR" "Get nic by ${ssh_ip} failed."
        exit 1
    fi
    
    record_all_nic_by_ip "${ssh_ip}"
    log_echo "INFO" "get_nic_by_IP ssh_ip=${ssh_ip} End."
}

function get_nicinfo()
{
    local app_type=$1
    local app_scp_ip=$2
    local app_ssh_ip=$3
    mkdir -p "${PreSet_PATH}/${app_type}_nicinfo"
    auto_scp ${remote_root_passwd} ${PreSet_PATH}/tools/get_networkinfo.sh "${oper_user}@${app_scp_ip}:${tmp_dir}/tools/"
    auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/tools/get_networkinfo.sh ${app_ssh_ip} nic" | grep -w "nic" > ${PreSet_PATH}/${app_type}_nicinfo/${app_ssh_ip}_nic_mask_info.txt
    if [ $? -ne 0 ];then
        log_echo "ERROR" "sh ${tmp_dir}/tools/get_networkinfo.sh ${app_ssh_ip} nic failed.of node: ${app_ssh_ip}"
        exit 1
    fi
    auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/tools/get_networkinfo.sh ${app_ssh_ip} mask" | grep -w "mask" >> ${PreSet_PATH}/${app_type}_nicinfo/${app_ssh_ip}_nic_mask_info.txt
    if [ $? -ne 0 ];then
        log_echo "ERROR" "sh ${tmp_dir}/tools/get_networkinfo.sh ${app_ssh_ip} mask failed.of node: ${app_ssh_ip}"
        exit 1
    fi
    dos2unix ${PreSet_PATH}/${app_type}_nicinfo/${app_ssh_ip}_nic_mask_info.txt
}

function check_total_memory()
{
    local total_memory_file="$1"
    log_echo "INFO" "The total_memory_file=${total_memory_file}"
    if [ -z "${total_memory_file}" ];then
        log_echo "ERROR" "The total_memory_file is Null, please check"
        exit 1
    fi
    
    if [ ! -f "${total_memory_file}" ];then
        log_echo "ERROR" "The ${total_memory_file} is not exists, please check"
        exit 1
    fi
    
    dos2unix ${total_memory_file}
    total_memory=$(awk '/^MemTotal/ { print $2 }' ${total_memory_file})
    if [ -z "${total_memory}" ];then
        log_echo "ERROR" "The total_memory get is null, please check file: ${total_memory_file}"
        exit 1
    fi
    
    local isNumber=$(echo "${total_memory}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        log_echo "ERROR" "The total_memory=${total_memory} is not number, please check"
        exit 1
    fi
    log_echo "INFO" "The total_memory=${total_memory}"
}

function APP_PreSet()
{
    tmp_dir=/tmp/DVPreSet
    need_cleanup="No"
    
    if [ "X${netWorkType}" != "XO" ];then
        Check_execute "APP_PreSet_$1"
        ret=$?
    else
        if [ "X${IPType}" == "X1" ];then
            Check_execute "APP_PreSet_$1"
            ret=$?        
        else
            Check_execute "APP_PreSet_$1"
            ret=$? 
        fi
    fi
    
    if [ ${ret} -eq 0 ];then
        return 0
    elif [ ${ret} -eq 2 ];then
        need_cleanup="Yes"
    fi
    log_echo "INFO" "We need the root permission to preset DV node." 
    local app_ssh_ip=$1
    local app_scp_ip=$1
    local app_node_pwd=$2
    
    local node_tag=""
    echo "$3" | grep "type:"
    if [ $? -eq 0 ];then
        local node_type=$(echo $3 | awk -F":" '{print $2}')
        node_tag="${node_type}"
    else
        local node_role=$3
        node_tag="${node_role}"
    fi
    log_echo "INFO" "The node_tag=${node_tag}" 
    add_sshpwd_to_pwdlist
    local listenIpList=""
    if [ "X${app_ssh_ip}" == "X" -a "X${app_node_pwd}" == "X" ];then
        if [ "X${need_cleanup}" == "XYes" ];then
            sh ${tmp_dir}/DV_PreSet_Cleanup.sh
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Cleanup databse in ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                exit 1
            fi
            sed -i "/APP_PreSet_${app_ssh_ip} : failed/d" ${action_tag} 
        fi
        
        tmp_dir=/tmp/DVPreSet
        rm -rf ${tmp_dir:?}       
        mkdir -p ${tmp_dir}

        i2k_file_list="DV_config.properties.tmp DV_PreSet_APP.sh utils_common.sh utils_uniep.sh utils_os.sh DV_PreSet_Cleanup.sh ext_disk.properties.tmp sshagent.exp add_cert.py repair_iptables.sh repair_iptables_lvs.sh dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service pre_get_disk_symbol.properties netWorkTypeOfDvConfigParameters.config nonstandard_ext_disk.properties"
        i2k_file_list="${i2k_file_list} registerConfigInfo.properties"
        for i2k_filename in ${i2k_file_list}
        do
            cp -rf ${PreSet_PATH}/Common/${i2k_filename} ${tmp_dir}
        done
        cp -rf ${PreSet_PATH}/Common/i2kuser_root_script ${tmp_dir}
        cp -rf ${PreSet_PATH}/Common/certificate ${tmp_dir}
        cp -rf ${PreSet_PATH}/Common/sign_dv ${tmp_dir}
        cp -rf ${PreSet_PATH}/Common/kafka ${tmp_dir}
        cp -rf ${PreSet_PATH}/Common/i2k_cert ${tmp_dir}
        mkdir ${tmp_dir}/tools
        cp -rf ${PreSet_PATH}/tools/rpm ${tmp_dir}/tools
        if [ "$(echo ${is_integrated_df_and_service}|tr 'a-z' 'A-Z')" == "YES" ];then
            cp -rf  ${PreSet_PATH}/Common/core_binding   ${tmp_dir}
        fi

        sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount "all_in_one"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Auto mount APPNode_DISK disk at all_in_one node failed, please check detailed log ${tmp_dir}/preset_script.log on the local host."
            exit 1
        fi

        if [ "X${IPType}" == "X0" ];then
            local ALL_IN_ONE_IP=${ALL_IN_ONE_IPV4}
        else
            local ALL_IN_ONE_IP=${ALL_IN_ONE_IPV6}
        fi
        echo "${passwd_list_tmp}" | sh ${tmp_dir}/DV_PreSet_APP.sh "${ALL_IN_ONE_IP}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset i2k in all_in_one node failed, please check detailed log ${tmp_dir}/preset_script.log on the local host."
            echo "APP_PreSet_${app_ssh_ip} : failed" >> ${action_tag}
            exit 1
        fi
        sh ${tmp_dir}/DV_PreSet_APP.sh "create_register_config"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset in create_register_config failed, please check detailed log ${tmp_dir}/preset_script.log on the local host."
            exit 1
        fi
    else
        if [ "X${IPType}" == "X1" ];then
            log_echo "INFO" "IPType is 1. is ipv6. of APP_PreSet"
            app_scp_ip="\[${app_scp_ip}\]"
        fi
        
        local isIpv6=$(echo ${app_scp_ip} |grep ":" |grep -v "]")
        if [ "X${isIpv6}" != "X" ];then
            app_scp_ip="\[${app_scp_ip}\]"
        fi
        
        if [ "X${node_role}" != "X" ];then
            sed -i "s#i2k_dual_role=.*#i2k_dual_role=${node_role}#" ${PreSet_PATH}/Common/DV_config.properties.tmp
        fi
        
        if [ "X${netWorkType}" == "XM" -a "X${node_role}" == "XPrimary" ];then
            hostname > ${PreSet_PATH}/hostname.txt
            if [ "X${need_cleanup}" == "XYes" ];then
                sh ${PreSet_PATH}/Common/DV_PreSet_Cleanup.sh unset_tmout
                sh ${PreSet_PATH}/Common/DV_PreSet_Cleanup.sh
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Cleanup in ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                    exit 1
                fi
                
                sh ${tmp_dir}/DV_PreSet_Cleanup.sh rollback_unset_tmout
                su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd startnode >/dev/null 2>&1" &
                sed -i "/APP_PreSet_${app_ssh_ip} : failed/d" ${action_tag}
            fi
            
            primary_hostname=$(tail -1 ${PreSet_PATH}/hostname.txt |sed 's/\r//')
            
            tmp_dir=/tmp/DVPreSet
            mkdir -p ${tmp_dir}

            db_file_list="DV_config.properties.tmp DV_PreSet_APP.sh DV_PreSet_DB.sh utils_common.sh utils_uniep.sh utils_os.sh ext_disk.properties.tmp sshagent.exp add_cert.py repair_iptables_lvs.sh repair_iptables.sh dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service pre_get_disk_symbol.properties netWorkTypeOfDvConfigParameters.config nonstandard_ext_disk.properties"
            db_file_list="${db_file_list} registerConfigInfo.properties"
            for db_filename in ${db_file_list}
            do
                cp -rf ${PreSet_PATH}/Common/${db_filename} ${tmp_dir}
            done
            
            mkdir -p ${tmp_dir}/tools
            cp -rf ${PreSet_PATH}/tools/ATAEExtend ${tmp_dir}/tools
            cp -rf ${PreSet_PATH}/tools/rpm ${tmp_dir}/tools
            if ( ! is_empty_param "${LVS_VIP_IPV4}" ) || ( ! is_empty_param "${LVS_VIP_IPV6}" );then
                cp -rf ${PreSet_PATH}/LVS ${tmp_dir}
            fi
            cp -rpf ${source_file} ${tmp_dir}
            cp -rpf ${PreSet_PATH}/Common/i2kuser_root_script ${tmp_dir}
            cp -rpf ${PreSet_PATH}/Common/certificate ${tmp_dir}
            cp -rpf ${PreSet_PATH}/Common/sign_dv ${tmp_dir}
            cp -rpf ${PreSet_PATH}/Common/kafka ${tmp_dir}
            cp -rf ${PreSet_PATH}/Common/i2k_cert ${tmp_dir}
            if [ "$(echo ${is_integrated_df_and_service}|tr 'a-z' 'A-Z')" == "YES" ];then
                cp -rpf  ${PreSet_PATH}/Common/core_binding   ${tmp_dir}
            fi

            ## uneip already auto mount it. EXTEND_98G_DISK
            ## auto mount SA 
            if [ "X${AUTO_DISK_MOUNT}" == "XTRUE" ];then
                sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Auto mount i2k disk at DV node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                    exit 1
                fi
                sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount "SA"
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Auto mount SA disk at DV node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                    exit 1
                fi
            fi
            
            ## copy cloudsop files.
            cd ${PreSet_PATH}/CloudSop
            osconfig_list=$(ls osconfig-*.zip)
            sudoconfig_list=$(ls sudoconfig*.tar.gz)
            cd -
            cs_file_list="cloudsop_prepareconfig.sh ${osconfig_list} ${sudoconfig_list} cert*.zip"
            for cs_filename in ${cs_file_list}
            do
                cp -rpf ${PreSet_PATH}/CloudSop/${cs_filename} ${tmp_dir}
            done
            
            chmod 755 -R ${tmp_dir}
            
            sh ${tmp_dir}/DV_PreSet_APP.sh unset_tmout
            if [ "X${IPType}" == "X1" ];then
                echo "${passwd_list_tmp}" | sh ${tmp_dir}/DV_PreSet_APP.sh "${DVFloatIP_IPV6}" "${node_type}" "${DVPrimary_IPV6}"
            else
                listenIpList="${DVFloatIP_IPV4},${DVFloatIP_IPV6}"
                if [ "${isAppNbiIpExtend}" == "Yes" ];then
                    listenIpList="${listenIpList},${APP_EXTERNAL_NBI_IPV4}"
                fi
                echo "${passwd_list_tmp}" | sh ${tmp_dir}/DV_PreSet_APP.sh "${listenIpList}" "${node_type}" "${DVPrimary_IPV4}"
            fi

            if [ $? -ne 0 ];then
                log_echo "ERROR" "Preset dv in DV node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                echo "APP_PreSet_${app_ssh_ip} : failed" >> ${action_tag}
                exit 1
            fi
            sh ${tmp_dir}/DV_PreSet_APP.sh "create_register_config"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Preset in create_register_config failed, please check detailed log ${tmp_dir}/preset_script.log on the local host."
                exit 1
            fi
            sh ${tmp_dir}/DV_PreSet_APP.sh rollback_unset_tmout
            sed -i "/APP_PreSet_${app_ssh_ip} : failed/d" ${action_tag}
            echo "APP_PreSet_${app_ssh_ip} : success" >> ${action_tag}
            return 0
            
        else
            if [ "X${root_password}" == "X" ];then
                if [ "X${app_node_pwd}" == "X" ];then
                    log_echo "ERROR" "The root password for ${app_ssh_ip} is null, please check"
                    exit 1
                fi
                remote_root_passwd="${app_node_pwd}"
            else
                remote_root_passwd="${root_password}"
            fi
        fi
        
        if [ "X${netWorkType}" != "XO" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} hostname" > ${PreSet_PATH}/hostname.txt
        fi
        
        if [ "X${need_cleanup}" == "XYes" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_Cleanup.sh unset_tmout"
            
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_Cleanup.sh"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Cleanup databse in ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                exit 1
            fi
            if [ "X${netWorkType}" == "XM" ];then
                su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${app_ssh_ip}  'source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd startnode >/dev/null 2>&1'"
            fi
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_Cleanup.sh rollback_unset_tmout"
            
            sed -i "/APP_PreSet_${app_ssh_ip} : failed/d" ${action_tag}
        fi
        
        if [ "X${node_role}" == "XPrimary" ];then
            primary_hostname=$(tail -1 ${PreSet_PATH}/hostname.txt |sed 's/\r//')
            primary_root_pswd=${remote_root_passwd}
        elif [ "X${node_role}" == "XSecondary" ];then
            secondary_hostname=$(tail -1 ${PreSet_PATH}/hostname.txt |sed 's/\r//')
            secondary_root_pswd=${remote_root_passwd}
        fi
        
        if [ "X${node_type}" == "XOM" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} hostname" > ${PreSet_PATH}/cmp_hostname.txt
        fi
        
        if [ "X${node_type}" == "XOM1" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} hostname" > ${PreSet_PATH}/om1_hostname.txt
        fi
        
        if [ "X${node_type}" == "XOM2" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} hostname" > ${PreSet_PATH}/om2_hostname.txt
        fi
        
        if [ "X${node_type}" == "XOMSA1" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} hostname" > ${PreSet_PATH}/omsa1_hostname.txt
        fi
        
        if [ "X${node_type}" == "XOMSA2" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} hostname" > ${PreSet_PATH}/omsa2_hostname.txt
        fi
        
        if [[ "${node_type}" =~ ExtendCluster[0-9] ]];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} hostname" > ${PreSet_PATH}/${node_type}_hostname.txt
        fi
        
        if [ "X${netWorkType}" == "XL" -o "X${netWorkType}" == "XC" ] && [ "X${node_type}" == "XPM1" -o "X${node_type}" == "XPM2" ];then
            check_lvs_mask_nic "${app_ssh_ip}" "${oper_user}" "${remote_root_passwd}" "${node_type}"
            if [ "X${IPType}" == "X2" -a "X${node_type}" == "XPM1" ];then
                local isIpv6=$(echo "${app_ssh_ip}" |grep ":")
                if [ ! -z "${isIpv6}" ];then
                    ## The value of app_ssh_ip is IPv6. IPv6 has been checked. IPv4 needs to be checked.
                    check_lvs_mask_nic_config "$(echo ${PM_Cluster_Node_IPV4_List} | awk -F',' '{print $1}')" "${node_type}" "0"
                else
                    ## The value of app_ssh_ip is IPv4. IPv4 has been checked. IPv6 needs to be checked.
                    check_lvs_mask_nic_config "$(echo ${PM_Cluster_Node_IPV6_List} | awk -F',' '{print $1}')" "${node_type}" "1"
                fi
            fi
        fi
        
        tmp_dir=/tmp/DVPreSet
        
        auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} rm -rf ${tmp_dir:?}"
        
        auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} mkdir -p ${tmp_dir}/tools"

        auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} chown -R ${oper_user}: ${tmp_dir}"
        
        [ -f ${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp ] && rm -f ${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp
        [ -f ${PreSet_PATH}/tmp_file/${node_tag}/netWorkTypeOfDvConfigParameters.config ] && rm -f ${PreSet_PATH}/tmp_file/${node_tag}/netWorkTypeOfDvConfigParameters.config
        mkdir -p ${PreSet_PATH}/tmp_file/${node_tag}/
        cp -rpf ${PreSet_PATH}/Common/DV_config.properties.tmp ${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp
        cp -rpf ${PreSet_PATH}/Common/netWorkTypeOfDvConfigParameters.config ${PreSet_PATH}/tmp_file/${node_tag}/netWorkTypeOfDvConfigParameters.config
        
        if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ] && [ "${AUTO_DISK_MOUNT}" == "TRUE" ]; then
            rebuild_tmp_cfg_file_disk ${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp
            if [ $? -ne 0 ]; then
                log_echo "ERROR" "Rebuild disk symbol in ${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp failed, please check."
                exit 1
            fi
        fi

        i2k_file_list="DV_config.properties.tmp DV_PreSet_APP.sh utils_common.sh utils_uniep.sh utils_os.sh DV_PreSet_Cleanup.sh ext_disk.properties.tmp sshagent.exp add_cert.py netWorkTypeOfDvConfigParameters.config repair_iptables.sh repair_iptables_lvs.sh dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service pre_get_disk_symbol.properties nonstandard_ext_disk.properties"
        i2k_file_list="${i2k_file_list} registerConfigInfo.properties"
        for i2k_filename in ${i2k_file_list}
        do
            auto_scp ${remote_root_passwd} ${PreSet_PATH}/Common/${i2k_filename} "${oper_user}@${app_scp_ip}:${tmp_dir}"
        done
        
        if [ -f ${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp ];then
            auto_scp "${remote_root_passwd}" ${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp "${oper_user}@${app_scp_ip}:${tmp_dir}"
        fi
        
        if [ "X${netWorkType}" == "XL" ] && [[ "X${node_type}" =~ XPM[1-9] ]]; then
            auto_scp ${remote_root_passwd} ${PreSet_PATH}/DV_Config_LargeCapacity.config "${oper_user}@${app_scp_ip}:${tmp_dir}"
        fi
        
        if [ "X${netWorkType}" == "XC" ] && [[ "X${node_type}" =~ XPM[1-9] ]]; then
            auto_scp ${remote_root_passwd} ${PreSet_PATH}/DV_Config_Cluster.config "${oper_user}@${app_scp_ip}:${tmp_dir}"
        fi
        
        if [ "X${changeUUidToDisk}" == "XYes" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} mkdir -p ${tmp_dir}/uuidToDisk"
            changeUUid_file_list="change_utils.sh changeUUidToDisk.sh rollbackDiskToUUid.sh"
            for filename in ${changeUUid_file_list}
            do
                auto_scp ${remote_root_passwd} ${PreSet_PATH}/tools/uuidToDisk/${filename} "${oper_user}@${app_scp_ip}:${tmp_dir}/uuidToDisk"
            done
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} chmod 755 -R ${tmp_dir}/uuidToDisk"
        else
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} rm -rf ${tmp_dir}/uuidToDisk"
        fi
        
        local I2kNodeRole=""
        if [ "X${netWorkType}" == "XM" -a "X${node_role}" == "XSecondary" ];then
            I2kNodeRole="Secondary"
        elif [ "X${netWorkType}" == "XM" -a "X${node_type}" == "XOMSA" ];then
            auto_scp ${remote_root_passwd} ${source_file} "${oper_user}@${app_scp_ip}:${tmp_dir}"
        elif [ "X${netWorkType}" == "XM" ] && [[ "${node_type}" =~ ExtendCluster[0-9] ]];then
            auto_scp ${remote_root_passwd} ${source_file} "${oper_user}@${app_scp_ip}:${tmp_dir}"
        else
            getI2kNodeRoleOfIp "${app_ssh_ip}"
            I2kNodeRole="${RETURN[0]}"
        fi
        
        if [ "X${netWorkType}" == "XC" -o "X${netWorkType}" == "XL" ] && [ "X${node_type}" == "XPM1" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} grep '^MemTotal' /proc/meminfo" > ${PreSet_PATH}/PM1_memtotal.txt
            check_total_memory "${PreSet_PATH}/PM1_memtotal.txt"
        fi
        
        if [ "X${netWorkType}" == "XC" -a "X${I2kNodeRole}" == "XPrimary" ] || [ "X${netWorkType}" == "XT" -a "X${I2kNodeRole}" == "XSingle" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} grep '^MemTotal' /proc/meminfo" > ${PreSet_PATH}/primary_memtotal.txt
            check_total_memory "${PreSet_PATH}/primary_memtotal.txt"
            ## kb to GB  (1048576=1024*1024)
            machine_total_memory=$((${total_memory}/1048576))
            if [ $machine_total_memory -ge 60 ];then
                ## zenith_paramgroup_file  large  largeCapacity
                echo "zenith_paramgroup_file=largeCapacity" > ${PreSet_PATH}/zenith_paramgroup_file.properties
            else
                echo "zenith_paramgroup_file=large" > ${PreSet_PATH}/zenith_paramgroup_file.properties
            fi
        fi
        
        ## Single Primary Secondary
        if [ "X${I2kNodeRole}" == "XSingle" -o "X${I2kNodeRole}" == "XPrimary" -o "X${I2kNodeRole}" == "XSecondary" ];then
            auto_scp ${remote_root_passwd} ${source_file} "${oper_user}@${app_scp_ip}:${tmp_dir}"
        fi
        
        if [ "X${node_type}" == "X" ];then
            auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/Common/i2kuser_root_script "${oper_user}@${app_scp_ip}:${tmp_dir}"
            auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/Common/i2k_cert "${oper_user}@${app_scp_ip}:${tmp_dir}"
            auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/tools/rpm "${oper_user}@${app_scp_ip}:${tmp_dir}/tools"
        else
            auto_scp ${remote_root_passwd} ${PreSet_PATH}/Common/i2kuser_root_script/sudoScripts/sed.sh "${oper_user}@${app_scp_ip}:${tmp_dir}"
            auto_scp ${remote_root_passwd} ${PreSet_PATH}/Common/i2kuser_root_script/sudoScripts/modifyCert.sh "${oper_user}@${app_scp_ip}:${tmp_dir}"
            auto_scp ${remote_root_passwd} ${PreSet_PATH}/Common/i2kuser_root_script/sudoScripts/handle_hofs.sh "${oper_user}@${app_scp_ip}:${tmp_dir}"
            auto_scp ${remote_root_passwd} ${PreSet_PATH}/Common/i2kuser_root_script/sudoScripts/recover_dbuser.sh "${oper_user}@${app_scp_ip}:${tmp_dir}"

            if [ "X${node_type}" == "XPM1" -o "X${node_type}" == "XPM2" ];then
                auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/LVS "${oper_user}@${app_scp_ip}:${tmp_dir}"
            fi
            if [[ "X${node_type}" =~ XPM[0-9] ]] || [[ "${node_type}" =~ ExtendCluster[0-9] ]];then
                get_nic_by_IP "${remote_root_passwd}" "${app_ssh_ip}"
            fi
        fi
        
        
        auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/Common/certificate "${oper_user}@${app_scp_ip}:${tmp_dir}"
        auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/Common/sign_dv "${oper_user}@${app_scp_ip}:${tmp_dir}"
        auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/Common/kafka "${oper_user}@${app_scp_ip}:${tmp_dir}"
        auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/tools/ATAEExtend "${oper_user}@${app_scp_ip}:${tmp_dir}/tools"
        auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/Common/core_binding   "${oper_user}@${app_scp_ip}:${tmp_dir}"
        
        if [[ "X${node_type}" =~ X(SM[0-9]|SMExtendCluster[0-9]|DVExtendCluster[0-9]|AE[0-9]) ]];then
            auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/Common/dvca "${oper_user}@${app_scp_ip}:${tmp_dir}"
        fi
        ## ext 98G 
        if [ "X${netWorkType}" != "XM" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount ${node_type}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Auto mount disk at DV node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                exit 1
            fi          
        fi
        
        if [ "X${netWorkType}" == "XM" ] && [[ "${node_type}" =~ ExtendCluster[0-9] ]];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount ${node_type}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Auto mount disk at DV node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                exit 1
            fi
            
            if [[ "X${node_type}" =~ XDVExtendCluster[0-9] ]];then
                auto_smart_ssh "${remote_root_passwd}" "${oper_user}@${app_ssh_ip} cat ${tmp_dir}/local_check_disk.txt" > ${PreSet_PATH}/${node_type}_local_check_disk.txt
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Get local disk failed on ${app_ssh_ip}"
                    exit 1
                fi
            fi
        fi
        
        if [ "X${netWorkType}" == "XM" ] && [[ "X${node_role}" == "XSecondary" || "X${node_type}" == "XOMSA" || "${node_type}" =~ ExtendCluster[0-9] ]];then
            if [ "X${pre_install_key}" == "X" -a "X${Digitalization_Disk_Param_Tag}" != "XYES" ];then
                if [ "X${node_role}" == "XSecondary" ];then
                    sed -i "s#^SRVBigData_DISK=.*#SRVBigData_DISK=${DVSecondary_SRVBigData_DISK}#g" ${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp
                elif [ "X${node_type}" == "XOMSA" ];then
                    sed -i "s#^SRVBigData_DISK=.*#SRVBigData_DISK=${DVThird_SRVBigData_DISK}#g" ${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp
                elif [[ "${node_type}" =~ OMSAExtendCluster[0-9] ]];then
                    sed -i "s#^SRVBigData_DISK=.*#SRVBigData_DISK=${OMSAExtendClusterNode_SRVBigData_DISK}#g" ${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp
                elif [[ "X${node_type}" =~ XDVExtendCluster[0-9] ]];then
                    sed -i "s#^SRVBigData_DISK=.*#SRVBigData_DISK=${ExtendClusterNode_SRVBigData_DISK}#g" ${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp
                else
                    log_echo "INFO" "The node_tag=${node_tag} of ${app_ssh_ip}"
                fi
                auto_scp ${remote_root_passwd} ${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp "${oper_user}@${app_scp_ip}:${tmp_dir}"
            fi
            
            if [ "X${Digitalization_Disk_Param_Tag}" == "XYES" ]; then
                if [ "X${node_role}" == "XSecondary" ];then
                    append_fisrv_to_digital_disk_param_configstring "Secondary" "${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp"
                    append_fisrv_to_digital_disk_param_configstring "Secondary" "${PreSet_PATH}/tmp_file/${node_tag}/netWorkTypeOfDvConfigParameters.config"
                elif [ "X${node_type}" == "XOMSA" ];then
                    append_fisrv_to_digital_disk_param_configstring "Third" "${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp"
                    append_fisrv_to_digital_disk_param_configstring "Third" "${PreSet_PATH}/tmp_file/${node_tag}/netWorkTypeOfDvConfigParameters.config"
                elif [[ "X${node_type}" =~ XOMSAExtendCluster[0-9] ]];then
                    append_fisrv_to_digital_disk_param_configstring "ExtendCluster" "${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp"
                    append_fisrv_to_digital_disk_param_configstring "ExtendCluster" "${PreSet_PATH}/tmp_file/${node_tag}/netWorkTypeOfDvConfigParameters.config"
                elif [[ "X${node_type}" =~ XDVExtendCluster[0-9] ]];then
                    append_fisrv_to_digital_disk_param_configstring "ExtendCluster" "${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp"
                    append_fisrv_to_digital_disk_param_configstring "ExtendCluster" "${PreSet_PATH}/tmp_file/${node_tag}/netWorkTypeOfDvConfigParameters.config"
                else
                    log_echo "INFO" "The node_type=${node_type}."
                fi
                auto_scp "${remote_root_passwd}" ${PreSet_PATH}/tmp_file/${node_tag}/DV_config.properties.tmp "${oper_user}@${app_scp_ip}:${tmp_dir}"
                auto_scp "${remote_root_passwd}" ${PreSet_PATH}/tmp_file/${node_tag}/netWorkTypeOfDvConfigParameters.config "${oper_user}@${app_scp_ip}:${tmp_dir}"
            fi
            
            if ( ! is_empty_param "${LVS_VIP_IPV4}" ) || ( ! is_empty_param "${LVS_VIP_IPV6}" );then
                auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/LVS "${oper_user}@${app_scp_ip}:${tmp_dir}"
            fi
            ## auto mount OM SA
            if [ "X${AUTO_DISK_MOUNT}" == "XTRUE" ];then
                if [ "X${node_role}" == "XSecondary" ];then
                    auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount"
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "Auto mount i2k disk at DV node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                        exit 1
                    fi
                fi
                
                if [[ "X${node_role}" == "XSecondary" || "X${node_type}" == "XOMSA" || "${node_type}" =~ OMSAExtendCluster[0-9] || "${node_type}" =~ DVExtendCluster[0-9] ]];then
                    auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount OM"
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "Auto mount disk at DV node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                        exit 1
                    fi
                    
                    auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount SA"
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "Auto mount disk at DV node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                        exit 1
                    fi
                fi
            fi
            
        fi
        
        if [ "X${netWorkType}" != "XM" ] || [[ "${node_type}" =~ ExtendCluster[0-9] ]];then
            cd ${PreSet_PATH}/CloudSop
            osconfig_list=$(ls osconfig-*.zip)
            sudoconfig_list=$(ls sudoconfig*.tar.gz)
            cd -
            cs_file_list="cloudsop_prepareconfig.sh ${osconfig_list} ${sudoconfig_list} cert*.zip"
            for cs_filename in ${cs_file_list}
            do
                auto_scp ${remote_root_passwd} ${PreSet_PATH}/CloudSop/${cs_filename} "${oper_user}@${app_scp_ip}:${tmp_dir}"
            done
        fi

        if [ "X${IPType}" != "X0" ]  && [ "X${netWorkType}" == "XC" ];then
            if [[ "X${node_type}" =~ XOM[0-9] ]];then
                get_nicinfo "om" "${app_scp_ip}" "${app_ssh_ip}"
            elif [[ "X${node_role}" =~ "XSecondary" ]] || [[ "X${node_role}" =~ "XPrimary" ]];then
                get_nicinfo "app" "${app_scp_ip}" "${app_ssh_ip}"
            fi
        fi

        if [ "X${IPType}" != "X0" ] && [ "X${netWorkType}" != "XC" ];then
            if [[ "X${node_type}" =~ "XOM" ]] || [[ "X${node_type}" =~ "XOMSA" ]] || [[ "X${node_type}" =~ "XFLUME" ]] || [[ "X${node_role}" =~ "XSecondary" ]] || [[ "${node_type}" =~ ExtendCluster[0-9] ]];then
                get_nicinfo "om" "${app_scp_ip}" "${app_ssh_ip}"
            fi
        fi

        auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} chmod 755 -R ${tmp_dir}"
        
        ## ## Single Primary Secondary
        if [ "X${I2kNodeRole}" == "XSingle" -o "X${I2kNodeRole}" == "XPrimary" -o "X${I2kNodeRole}" == "XSecondary" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh checkNetworkConfig ${I2kNodeRole} ${netWorkType}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Preset dv in DV node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                echo "APP_PreSet_${app_ssh_ip} : failed" >> ${action_tag}
                exit 1
            fi
        fi
        
        if [ "X${netWorkType}" == "XM" -a "X${node_type}" == "XOMSA" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh checkNetworkConfig ${node_type} ${netWorkType}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Preset dv in DV node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                echo "APP_PreSet_${app_ssh_ip} : failed" >> ${action_tag}
                exit 1
            fi
        fi
        
        if [ "X${netWorkType}" != "XM" ] || [[ "${node_type}" =~ ExtendCluster[0-9] ]];then
            log_echo "INFO" "Begin to execute cloudsop_prepareconfig.sh..."
            if [ "X${SUDO_USER}" != "X" ];then
                auto_smart_ssh ${remote_root_passwd} "-t ${oper_user}@${app_ssh_ip} sh ${tmp_dir}/cloudsop_prepareconfig.sh install |sudo tee ${tmp_dir}/preset_script.log\\;exit  \\\${PIPESTATUS\\[0\\]}" "dv_user_${ossadm_userpasswd}" > /dev/null 2>&1
            else
                auto_smart_ssh ${remote_root_passwd} "-t ${oper_user}@${app_ssh_ip} sh ${tmp_dir}/cloudsop_prepareconfig.sh install >> ${tmp_dir}/preset_script.log" "dv_user_${ossadm_userpasswd}"  > /dev/null 2>&1
            fi

            if [ $? -ne 0 ];then
                log_echo "ERROR" "Preset cloudsop in DV node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                exit 1
            fi
        fi
        
        listenIpList="${app_ssh_ip}"
        if [ "X${netWorkType}" == "XM" -a "X${node_role}" == "XSecondary" ];then
            listenIpList="${DVFloatIP_IPV4},${DVFloatIP_IPV6}"
            if [ "${isAppNbiIpExtend}" == "Yes" ];then
                listenIpList="${listenIpList},${APP_EXTERNAL_NBI_IPV4}"
            fi
        elif [ "X${netWorkType}" == "XC" -o "X${netWorkType}" == "XL" ];then
            local tmp_app_ssh_ip=$(echo "${app_ssh_ip}"|sed "s/\./\\\./g")
            local primary_ip_list="${APP_Primary_IPV4},${APP_Primary_IPV6},${APP_Primary_MGR_IPV6}"
            local secondary_ip_list="${APP_Secondary_IPV4},${APP_Secondary_IPV6},${APP_Secondary_MGR_IPV6}"
            local hasIpInIt=$(echo "${primary_ip_list},${secondary_ip_list}"|grep -w "${tmp_app_ssh_ip}" )
            if [ ! -z "${hasIpInIt}" ];then
                if [ "X${APP_FloatIP_IPV4}" != "X{{APP_FloatIP_IPV4}}" -a "X${APP_FloatIP_IPV4}" != "X" ];then
                    listenIpList="${listenIpList},${APP_FloatIP_IPV4}"
                fi

                if [ "X${APP_FloatIP_IPV6}" != "X{{APP_FloatIP_IPV6}}" -a "X${APP_FloatIP_IPV6}" != "X" ];then
                    listenIpList="${listenIpList},${APP_FloatIP_IPV6}"
                fi
            fi
        fi
        
        auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh unset_tmout"
        
        log_echo "INFO" "Begin to execute DV_PreSet_APP.sh..."
        #please ensure the last parameter is node ip

        if [ "X${SUDO_USER}" != "X" ];then
            auto_smart_ssh ${remote_root_passwd} "-t ${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh \\\"${listenIpList}\\\" '${node_type}' \\\"${app_ssh_ip}\\\" |sudo tee ${tmp_dir}/preset_script.log\\;exit  \\\${PIPESTATUS\\[0\\]}" "dv_user_${passwd_list_tmp}" > /dev/null 2>&1
        else
            auto_smart_ssh ${remote_root_passwd} "-t ${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh \\\"${listenIpList}\\\" \\\"${node_type}\\\" \\\"${app_ssh_ip}\\\"   >> ${tmp_dir}/preset_script.log" "dv_user_${passwd_list_tmp}" > /dev/null 2>&1
        fi
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset dv in DV node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
            echo "APP_PreSet_${app_ssh_ip} : failed" >> ${action_tag}
            exit 1
        fi
        if [ ! -f "/.dockerenv" ] && [ "X${dv_force_change_os_pwd}" == "XYes" ];then
            remote_root_passwd=${new_root_pwd}
        fi

        if [ "X${netWorkType}" == "XM" ] && [[ "${node_type}" =~ ExtendCluster[0-9] ]];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh extend_pod_preset"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Preset dv in DV node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                echo "APP_PreSet_${app_ssh_ip} : failed" >> ${action_tag}
                exit 1
            fi
        fi
        ## 仅app节点传输
        if [ -z "${node_tag}" ] || [ "X${node_tag}" == "XPrimary" -o "X${node_tag}" == "XSecondary" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh create_register_config"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Preset dv in DV node ${app_ssh_ip} create register config failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                echo "APP_PreSet_${app_ssh_ip} : failed" >> ${action_tag}
                exit 1
            fi
        fi
        auto_smart_ssh ${remote_root_passwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh rollback_unset_tmout"
    fi

    sed -i "/APP_PreSet_${app_ssh_ip} : failed/d" ${action_tag}
    echo "APP_PreSet_${app_ssh_ip} : success" >> ${action_tag}
}

function mount_pm_kafka(){
    
    check_empty_param_of_disk "PM_KAFKA_DISK" "${PM_KAFKA_DISK}"
    if [ $? -ne 0 ];then
        if [ "$1" == "local" ];then
            sh ${tmp_dir}/DV_PreSet_DB.sh auto_mount_kafka
        else
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} ${tmp_dir}/DV_PreSet_DB.sh auto_mount_kafka"
        fi
    else
        return 0
    fi
    if [ $? -ne 0 ];then
        log_echo "ERROR" "auto_mount_kafka in DB node ${db_ssh_ip} failed"
        exit 1
    fi
}
function DB_PreSet()
{
    if [ "X${netWorkType}" != "XO" ];then
        Check_execute "DB_PreSet_$1"
        ret=$?
    else
        if [ "X${IPType}" == "X1" ];then
            Check_execute "DB_PreSet_$1"
            ret=$?        
        else
            Check_execute "DB_PreSet_$1"
            ret=$? 
        fi
    fi   
    if [ $ret -eq 0 ];then
        return 0
    fi
    
    log_echo "INFO" "We need the root permission to preset DB node." 
    local db_ssh_ip=$1
    local db_scp_ip=$1
    local db_node_pwd=$2
    local node_role=$3
    
    add_sshpwd_to_pwdlist
    
    local preinstall_zenith_size=0
    ## check DBNode_EXTEND_DISK=  Configuration_String=
    if [ -f ${PreSet_PATH}/${source_file_name} ];then
        local tmp_dbnode_extend_disk=$(cat ${PreSet_PATH}/${source_file_name} |grep "^DBNode_EXTEND_DISK=" |awk -F'=' '{print $2}')
        local tmp_configuration_string=$(cat ${PreSet_PATH}/${source_file_name} |grep "^Configuration_String=" |awk -F'=' '{print $2}')
    fi
    
    if [ -z "${tmp_dbnode_extend_disk}" -o "${tmp_dbnode_extend_disk}" == "{{DBNode_EXTEND_DISK}}" -o -z "${tmp_configuration_string}" -o "${tmp_configuration_string}" == "{{Configuration_String}}" ];then
        log_echo "INFO" "The tmp_dbnode_extend_disk=${tmp_dbnode_extend_disk} or tmp_configuration_string=${tmp_configuration_string} has Null."
    else
        ## Configuration_String
        local get_db_disk_size=$(echo "${tmp_configuration_string}" | grep -ow "db:\([0-9]\+\)" | sed "s#.*db:\([0-9]\+\).*#\1#g" | tail -1)
        if [ -z "${get_db_disk_size}" ];then
            log_echo "ERROR" "The db key in tmp_configuration_string=${tmp_configuration_string} is not found."
            exit 1
        fi
        
        local isNumber=$(echo "${get_db_disk_size}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
        if [ "${isNumber}" == "No" ];then
            log_echo "ERROR" "The get_db_disk_size=${get_db_disk_size} is not number."
            exit 1
        fi
        preinstall_zenith_size=$(echo ${get_db_disk_size}| awk '{print int($1* 1024*0.95)}' )
        log_echo "INFO" "this preinstall."
    fi
    log_echo "INFO" "The preinstall_zenith_size=${preinstall_zenith_size}"
    
    if [ "X${db_ssh_ip}" == "X" -a "X${db_node_pwd}" == "X" ];then
        tmp_dir=/tmp/DVPreSet        
        rm -rf ${tmp_dir:?}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ssh to ${db_ssh_ip} execute command failed, please check."
            exit 1
        fi
        mkdir -p ${tmp_dir}
        if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ] && [ "${AUTO_DISK_MOUNT}" == "TRUE" ]; then
            rebuild_tmp_cfg_file_disk ${PreSet_PATH}/Common/DV_config.properties.tmp
            if [ $? -ne 0 ]; then
                log_echo "ERROR" "Rebuild disk symbol in ${PreSet_PATH}/Common/DV_config.properties.tmp failed, please check."
                exit 1
            fi
        fi
        db_file_list="DV_config.properties.tmp DV_PreSet_DB.sh utils_common.sh utils_uniep.sh utils_os.sh ext_disk.properties.tmp sshagent.exp add_cert.py dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service pre_get_disk_symbol.properties netWorkTypeOfDvConfigParameters.config nonstandard_ext_disk.properties"
        for db_filename in ${db_file_list}
        do
            cp -rf ${PreSet_PATH}/Common/${db_filename} ${tmp_dir}
        done
        mkdir -p ${tmp_dir}/tools
        cp -rf ${PreSet_PATH}/tools/exp_imp_dvpmdatadb ${tmp_dir}/tools/
        cp -rf ${PreSet_PATH}/Common/certificate ${tmp_dir}
        cp -rf ${PreSet_PATH}/Common/sign_dv ${tmp_dir}
        cp -rf ${PreSet_PATH}/Common/kafka ${tmp_dir}
        cp -rf ${PreSet_PATH}/Common/dvca ${tmp_dir}
        echo "${passwd_list_tmp}" | sh ${tmp_dir}/DV_PreSet_DB.sh osuser "${ALL_IN_ONE_IPV4},${ALL_IN_ONE_IPV6},${ALL_IN_ONE_MGR_IPV6} " "${ALL_IN_ONE_IPV4}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset db in all_in_one node failed, please check detailed log ${tmp_dir}/preset_script.log on the local host."
            exit 1
        fi   
        sh ${tmp_dir}/DV_PreSet_DB.sh  auto_mount_backuptmp
        if [ $? -ne 0 ];then
            log_echo "ERROR" "sh ${tmp_dir}/DV_PreSet_DB.sh  auto_mount_backuptmp in all_in_one node failed, please check detailed log ${tmp_dir}/preset_script.log on the local host."
            exit 1
        fi
        if [ ${preinstall_zenith_size} -eq 0 ];then
            echo "zenith_size=$(df -Pm /opt/zenith |awk -F' ' '{print $4}' |sed '1,1d' |sed 's/M//'|awk -F'.' '{print $1}'|head -n 1|sed 's| ||g')" > ${PreSet_PATH}/zenith_size.txt
        else
            echo "zenith_size=${preinstall_zenith_size}" > ${PreSet_PATH}/zenith_size.txt
        fi
    else    
        if [ "X${IPType}" == "X1" ];then
            log_echo "INFO" "IPType is 1. is ipv6. of DB_PreSet"
            db_scp_ip="\[${db_scp_ip}\]"
        fi
        
        local isIpv6=$(echo ${db_scp_ip} |grep ":" |grep -v "]")
        if [ "X${isIpv6}" != "X" ];then
            db_scp_ip="\[${db_scp_ip}\]"
        fi
        
        if [ "X${netWorkType}" == "XM" -a "X${node_role}" == "XPrimary" ];then
            log_echo "INFO" "the netWorkType=${netWorkType} node_role=${node_role} not need get remote root ssh."
        else
            if [ "X${root_password}" == "X" ];then
                if [ "X${db_node_pwd}" == "X" ];then
                    log_echo "ERROR" "The root password for ${db_ssh_ip} is null, please check"
                    exit 1
                fi
                remote_root_passwd="${db_node_pwd}"
            else
                remote_root_passwd="${root_password}"
            fi
        fi
            
        tmp_dir=/tmp/DVPreSet
        
        if [ "X${netWorkType}" == "XM" -a "X${node_role}" == "XPrimary" ];then
            mkdir -p ${tmp_dir}
            mkdir -p ${tmp_dir}/tools
            if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ] && [ "${AUTO_DISK_MOUNT}" == "TRUE" ]; then
                cp -rf ${PreSet_PATH}/Common/pre_get_disk_symbol.properties ${tmp_dir}
                cp -rf ${configParametersFile} ${tmp_dir}
                rebuild_tmp_cfg_file_disk ${PreSet_PATH}/Common/DV_config.properties.tmp
                if [ $? -ne 0 ]; then
                    log_echo "ERROR" "Rebuild disk symbol in ${PreSet_PATH}/Common/DV_config.properties.tmp failed, please check."
                    exit 1
                fi
            fi
            ## DV_config.properties.tmp DV_PreSet_DB.sh utils_common.sh utils_uniep.sh utils_os.sh ext_disk.properties.tmp
            cp -rpf ${PreSet_PATH}/Common/ext_disk.properties.tmp ${tmp_dir}
            cp -rpf ${PreSet_PATH}/Common/utils_common.sh ${tmp_dir}
            cp -rpf ${PreSet_PATH}/Common/utils_uniep.sh ${tmp_dir}
            cp -rpf ${PreSet_PATH}/Common/utils_os.sh ${tmp_dir}
            cp -rpf ${PreSet_PATH}/Common/DV_config.properties.tmp ${tmp_dir}
            cp -rpf ${PreSet_PATH}/Common/DV_PreSet_APP.sh ${tmp_dir}
            cp -rpf ${PreSet_PATH}/Common/DV_PreSet_DB.sh ${tmp_dir}
            cp -rpf ${PreSet_PATH}/Common/sshagent.exp ${tmp_dir}
            cp -rf ${PreSet_PATH}/Common/dvca ${tmp_dir}
            cp -rpf ${PreSet_PATH}/tools/ATAEExtend ${tmp_dir}/tools
            cp -rpf ${PreSet_PATH}/tools/exp_imp_dvpmdatadb ${tmp_dir}/tools
            
            sh ${tmp_dir}/DV_PreSet_DB.sh unset_tmout
            if [ "X${IPType}" == "X1" ]; then
                echo "${passwd_list_tmp}" | sh ${tmp_dir}/DV_PreSet_DB.sh osuser "${DVFloatIP_IPV6}" "${DVPrimary_IPV6}"
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Preset db in DB node ${db_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                    exit 1
                fi
            else
                echo "${passwd_list_tmp}" | sh ${tmp_dir}/DV_PreSet_DB.sh osuser "${DVFloatIP_IPV4},${DVFloatIP_IPV6}" "${DVPrimary_IPV4}"
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Preset db in DB node ${db_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                    exit 1
                fi
            fi
            mount_pm_kafka "local"  
            
            if [ ${preinstall_zenith_size} -eq 0 ];then
                echo "zenith_size=$(df -Pm /opt/zenith |awk -F' ' '{print $4}' |sed '1,1d' |sed 's/M//'|awk -F'.' '{print $1}'|head -n 1|sed 's| ||g')" > ${PreSet_PATH}/zenith_size.txt
            else
                echo "zenith_size=${preinstall_zenith_size}" > ${PreSet_PATH}/zenith_size.txt
            fi
            
            sh ${tmp_dir}/DV_PreSet_DB.sh rollback_unset_tmout
            echo "DB_PreSet_${db_ssh_ip} : success" >> ${action_tag}
            return 0
            
        elif [ "X${netWorkType}" == "XM" -a "X${node_role}" == "XSecondary" ];then

            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} mkdir -p ${tmp_dir}"
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} chown -R ${oper_user}: ${tmp_dir}"
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} chmod 755 -R  ${tmp_dir}"

            if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ] && [ "${AUTO_DISK_MOUNT}" == "TRUE" ]; then
                rebuild_tmp_cfg_file_disk ${PreSet_PATH}/Common/DV_config.properties.tmp
                if [ $? -ne 0 ]; then
                    log_echo "ERROR" "Rebuild disk symbol in ${PreSet_PATH}/Common/DV_config.properties.tmp failed, please check."
                    exit 1
                fi
            fi
            ## DV_config.properties.tmp DV_PreSet_DB.sh utils_common.sh utils_uniep.sh utils_os.sh ext_disk.properties.tmp
            db_file_list="DV_config.properties.tmp DV_PreSet_APP.sh DV_PreSet_DB.sh utils_common.sh utils_uniep.sh utils_os.sh ext_disk.properties.tmp sshagent.exp add_cert.py repair_iptables.sh repair_iptables_lvs.sh dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service pre_get_disk_symbol.properties netWorkTypeOfDvConfigParameters.config nonstandard_ext_disk.properties"
            for db_filename in ${db_file_list}
            do
                auto_scp ${remote_root_passwd} ${PreSet_PATH}/Common/${db_filename} "${oper_user}@${db_scp_ip}:${tmp_dir}"
            done
            
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} mkdir -p ${tmp_dir}/tools"
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} chown -R ${oper_user}: ${tmp_dir}/tools"
            auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/tools/ATAEExtend "${oper_user}@${db_scp_ip}:${tmp_dir}/tools"
            auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/tools/exp_imp_dvpmdatadb "${oper_user}@${db_scp_ip}:${tmp_dir}/tools"
            auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/Common/dvca "${oper_user}@${db_scp_ip}:${tmp_dir}"
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} sh ${tmp_dir}/DV_PreSet_DB.sh unset_tmout"
            
            log_echo "INFO" "Begin to execute DV_PreSet_DB.sh..."
            if [ "X${IPType}" == "X1" ]; then
                auto_smart_ssh ${remote_root_passwd} "-t ${oper_user}@${db_ssh_ip} sh ${tmp_dir}/DV_PreSet_DB.sh osuser ${DVFloatIP_IPV6} "${DVPrimary_IPV6}" >> ${tmp_dir}/preset_script.log" "dv_user_${passwd_list_tmp}" > /dev/null
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Preset db in DB node ${db_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                    exit 1
                fi
            else
                auto_smart_ssh ${remote_root_passwd} "-t ${oper_user}@${db_ssh_ip} sh ${tmp_dir}/DV_PreSet_DB.sh osuser ${DVFloatIP_IPV4},${DVFloatIP_IPV6} "${DVPrimary_IPV4}" >> ${tmp_dir}/preset_script.log" "dv_user_${passwd_list_tmp}" > /dev/null
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Preset db in DB node ${db_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                    exit 1
                fi
            fi
            if [ ! -f "/.dockerenv" ] && [ "X${dv_force_change_os_pwd}" == "XYes" ];then
                remote_root_passwd=${new_root_pwd}
            fi 
            mount_pm_kafka remote
            
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} sh ${tmp_dir}/DV_PreSet_DB.sh rollback_unset_tmout"
            echo "DB_PreSet_${db_ssh_ip} : success" >> ${action_tag}
            return 0
        fi
        
        auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} rm -rf ${tmp_dir:?}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ssh to ${db_ssh_ip} execute command failed, please check."
            exit 1
        fi
        auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} mkdir -p ${tmp_dir}/tools"
        auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} chown -R ${oper_user}: ${tmp_dir}"
        if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ] && [ "${AUTO_DISK_MOUNT}" == "TRUE" ]; then
            rebuild_tmp_cfg_file_disk ${PreSet_PATH}/Common/DV_config.properties.tmp
            if [ $? -ne 0 ]; then
                log_echo "ERROR" "Rebuild disk symbol in ${PreSet_PATH}/Common/DV_config.properties.tmp failed, please check."
                exit 1
            fi
        fi
        db_file_list="DV_config.properties.tmp DV_PreSet_DB.sh utils_common.sh utils_uniep.sh utils_os.sh ext_disk.properties.tmp sshagent.exp add_cert.py dv_loop_monitor.sh dv_loop_monitor.cfg dv_loop_monitor.timer dv_loop_monitor.service pre_get_disk_symbol.properties netWorkTypeOfDvConfigParameters.config nonstandard_ext_disk.properties"
        for db_filename in ${db_file_list}
        do
            auto_scp ${remote_root_passwd} ${PreSet_PATH}/Common/${db_filename} "${oper_user}@${db_scp_ip}:${tmp_dir}"
        done
        
        if [ "X${changeUUidToDisk}" == "XYes" ];then
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} mkdir -p ${tmp_dir}/uuidToDisk"
            changeUUid_file_list="change_utils.sh changeUUidToDisk.sh rollbackDiskToUUid.sh"
            for filename in ${changeUUid_file_list}
            do
                auto_scp ${remote_root_passwd} ${PreSet_PATH}/tools/uuidToDisk/${filename} "${oper_user}@${db_scp_ip}:${tmp_dir}/uuidToDisk"
            done
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} chmod 755 -R ${tmp_dir}/uuidToDisk"
        else
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} rm -rf ${tmp_dir}/uuidToDisk"
        fi
        
        auto_scp ${remote_root_passwd} ${PreSet_PATH}/Common/i2kuser_root_script/sudoScripts/sed.sh "${oper_user}@${db_scp_ip}:${tmp_dir}"
        auto_scp ${remote_root_passwd} ${PreSet_PATH}/Common/i2kuser_root_script/sudoScripts/modifyCert.sh "${oper_user}@${db_scp_ip}:${tmp_dir}"
        auto_scp ${remote_root_passwd} ${PreSet_PATH}/Common/i2kuser_root_script/sudoScripts/handle_hofs.sh "${oper_user}@${db_scp_ip}:${tmp_dir}"
        auto_scp ${remote_root_passwd} ${PreSet_PATH}/Common/i2kuser_root_script/sudoScripts/recover_dbuser.sh "${oper_user}@${db_scp_ip}:${tmp_dir}"
        auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/tools/ATAEExtend "${oper_user}@${db_scp_ip}:${tmp_dir}/tools"
        auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/tools/exp_imp_dvpmdatadb "${oper_user}@${db_scp_ip}:${tmp_dir}/tools"
        
        auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} sh ${tmp_dir}/DV_PreSet_DB.sh auto_mount"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Auto mount disk at DB node ${db_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
            exit 1
        fi

        get_nicinfo "db" "${db_scp_ip}" "${db_ssh_ip}"

        if [ "X${netWorkType}" != "XM" ];then
            cd ${PreSet_PATH}/CloudSop
            osconfig_list=$(ls osconfig-*.zip)
            sudoconfig_list=$(ls sudoconfig*.tar.gz)
            cd -
            cs_file_list="cloudsop_prepareconfig.sh ${osconfig_list} ${sudoconfig_list} cert*.zip"

            for cs_filename in ${cs_file_list}
            do
                auto_scp ${remote_root_passwd} ${PreSet_PATH}/CloudSop/${cs_filename} "${oper_user}@${db_scp_ip}:${tmp_dir}"
            done
        fi
        
        auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} chmod 755 -R ${tmp_dir}"
        
        if [ "X${netWorkType}" != "XM" ];then
            log_echo "INFO" "Begin to execute cloudsop_prepareconfig.sh..."
            if [ "X${SUDO_USER}" != "X" ];then
                auto_smart_ssh ${remote_root_passwd} "-t ${oper_user}@${db_ssh_ip} sh ${tmp_dir}/cloudsop_prepareconfig.sh install |sudo tee ${tmp_dir}/preset_script.log\\;exit  \\\${PIPESTATUS\\[0\\]}" "dv_user_${ossadm_userpasswd}" > /dev/null
            else
                auto_smart_ssh ${remote_root_passwd} "-t ${oper_user}@${db_ssh_ip} sh ${tmp_dir}/cloudsop_prepareconfig.sh install >> ${tmp_dir}/preset_script.log" "dv_user_${ossadm_userpasswd}" > /dev/null
            fi

            if [ $? -ne 0 ];then
                log_echo "ERROR" "Preset cloudsop in DB node ${db_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
                exit 1
            fi
        fi
        
        auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/Common/certificate "${oper_user}@${db_scp_ip}:${tmp_dir}"
        auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/Common/sign_dv "${oper_user}@${db_scp_ip}:${tmp_dir}"
        auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/Common/kafka "${oper_user}@${db_scp_ip}:${tmp_dir}"
        auto_scp_dir ${remote_root_passwd} ${PreSet_PATH}/Common/dvca "${oper_user}@${db_scp_ip}:${tmp_dir}"
        
        auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} sh ${tmp_dir}/DV_PreSet_DB.sh unset_tmout"
        
        log_echo "INFO" "Begin to execute DV_PreSet_DB.sh..."
        if [ "X${SUDO_USER}" != "X" ];then
            auto_smart_ssh ${remote_root_passwd} "-t ${oper_user}@${db_ssh_ip} sh ${tmp_dir}/DV_PreSet_DB.sh osuser ${db_ssh_ip} |sudo tee ${tmp_dir}/preset_script.log\\;exit  \\\${PIPESTATUS\\[0\\]}" "dv_user_${passwd_list_tmp}" > /dev/null
        else
            auto_smart_ssh ${remote_root_passwd} "-t ${oper_user}@${db_ssh_ip} sh ${tmp_dir}/DV_PreSet_DB.sh osuser ${db_ssh_ip} >> ${tmp_dir}/preset_script.log" "dv_user_${passwd_list_tmp}" > /dev/null
        fi
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset db in DB node ${db_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
            exit 1
        fi
        if [ ! -f "/.dockerenv" ] && [ "X${dv_force_change_os_pwd}" == "XYes" ];then
            remote_root_passwd=${new_root_pwd}
        fi 
        mount_pm_kafka remote
        
        if [ ${preinstall_zenith_size} -eq 0 ];then
            ## get zenith size
            auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} echo zenith_size=\\\$(df -Pm /opt/zenith |awk -F' ' '{print \\\$4}' |sed '1,1d' |sed 's/M//'|awk -F'.' '{print \\\$1}'|head -n 1|sed 's| ||g')" > ${PreSet_PATH}/zenith_size.txt
        else
            echo "zenith_size=${preinstall_zenith_size}" > ${PreSet_PATH}/zenith_size.txt
        fi
        
        auto_smart_ssh ${remote_root_passwd} "${oper_user}@${db_ssh_ip} sh ${tmp_dir}/DV_PreSet_DB.sh rollback_unset_tmout"
    fi
    echo "DB_PreSet_${db_ssh_ip} : success" >> ${action_tag}
}

function create_cloudsop_cipher()
{
    file_dir=$1
    if [ ! -d ${PreSet_PATH}/UniEp/python ];then
        unzip -q ${PreSet_PATH}/UniEp/python*.zip -d ${PreSet_PATH}/UniEp/python
    fi
    tmp_python=$(ls -d ${PreSet_PATH}/UniEp/python/lib/python*)
    if [ -f ${tmp_python}/site-packages/ossutil/ossext.pyc ];then
        rm -f ${tmp_python}/site-packages/ossutil/ossext.pyc
    fi
    if [ ! -f ${tmp_python}/site-packages/ossutil/ossext.py ];then
        cp -f ${PreSet_PATH}/UniEp/ossext.py ${tmp_python}/site-packages/ossutil
    fi
    if [ ! -d ${file_dir} ];then
        mkdir -p ${file_dir}
        cd ${PreSet_PATH}/UniEp/python/bin/
        ./python -c "from ossutil import ossext; ossext.OSSUtils.write_file('${file_dir}/base.ksf', ossext.KeyGenerator._newkey('\x00\x00\x00\x01', None, 2)); ossext.OSSUtils.write_file('${file_dir}/common_shared.ksf', ossext.KeyGenerator._newkey('\x00\x00\x00\x01', 'common_share', 2))" > /dev/null 2>&1
        chmod 755 ${file_dir}/base.ksf
        chmod 755 ${file_dir}/common_shared.ksf
    fi
    cp -rpf ${file_dir} ${PreSet_PATH}/UniEp/cloudsop_cipher_uniep 
}

function modify_python()
{
    check_os_suse_arm
    if [ $? -eq 0 ]; then
        [ ! -f ${PreSet_PATH}/UniEp/python/bin/pythonOld ] && mv ${PreSet_PATH}/UniEp/python/bin/python ${PreSet_PATH}/UniEp/python/bin/pythonOld
        if [ ! -f ${PreSet_PATH}/UniEp/python/bin/python  ];then
            cp -rpf ${PreSet_PATH}/UniEp/python.sh  ${PreSet_PATH}/UniEp/python/bin/python
            sed -i "s#^lib_path=.*#lib_path=${PreSet_PATH}/python_lib/lib#g"  ${PreSet_PATH}/UniEp/python/bin/python
            sed -i "s#^python_path=.*#python_path=${PreSet_PATH}/UniEp/python/bin/pythonOld#g"  ${PreSet_PATH}/UniEp/python/bin/python
            chmod -R 755 ${PreSet_PATH}/UniEp/python/bin/python
        fi
    fi
}

function decrypt_password()
{
    decryptPasswd=""
    if [ ! -d ${PreSet_PATH}/UniEp/python ];then
        unzip -q ${PreSet_PATH}/UniEp/python*.zip -d ${PreSet_PATH}/UniEp/python
    fi
    tmp_python=$(ls -d ${PreSet_PATH}/UniEp/python/lib/python*)
    if [ -f ${tmp_python}/site-packages/ossutil/ossext.pyc ];then
        rm -f ${tmp_python}/site-packages/ossutil/ossext.pyc
    fi
    if [ ! -f ${tmp_python}/site-packages/ossutil/ossext.py ];then
        cp -f ${PreSet_PATH}/UniEp/ossext.py ${tmp_python}/site-packages/ossutil
    fi
    if [ ! -d ${PreSet_PATH}/UniEp/cloudsop_cipher_bak ];then
        cp -rpf ${PreSet_PATH}/UniEp/cloudsop_cipher ${PreSet_PATH}/UniEp/cloudsop_cipher_bak
    fi
    cd ${PreSet_PATH}/UniEp/python/bin/
    modify_python
    decryptPasswd=$(./python -c 'from ossutil import ossext; print(ossext.Cipher.decrypt("'$1'"))')
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Failed to decrypt the password."
        exit 1
    fi
    cd - > /dev/null 2>&1
    return 0
}

function encrypt_password()
{
    encryptPasswd=""
    if [ "X${is_upgrade}" != "XYes" ];then
        if [ ! -d ${PreSet_PATH}/UniEp/python ];then
            unzip -q ${PreSet_PATH}/UniEp/python*.zip -d ${PreSet_PATH}/UniEp/python
        fi
        tmp_python=$(ls -d ${PreSet_PATH}/UniEp/python/lib/python*)
        if [ -f ${tmp_python}/site-packages/ossutil/ossext.pyc ];then
            rm -f ${tmp_python}/site-packages/ossutil/ossext.pyc
        fi
        if [ ! -f ${tmp_python}/site-packages/ossutil/ossext.py ];then
            cp -f ${PreSet_PATH}/UniEp/ossext.py ${tmp_python}/site-packages/ossutil
        fi
        if [ ! -d ${PreSet_PATH}/UniEp/cloudsop_cipher_bak ];then
            cp -rpf ${PreSet_PATH}/UniEp/cloudsop_cipher ${PreSet_PATH}/UniEp/cloudsop_cipher_bak
        fi
        cd ${PreSet_PATH}/UniEp/python/bin/
        modify_python
        encryptPasswd=$(echo "$1"|./python ${PreSet_PATH}/UniEp/encrypt.py "")
    else
        encrypt_by_uniep "$1" ""
        encryptPasswd="${pwd_return}"
    fi
    if [ "${encryptPasswd}" == "" ];then
        log_echo "ERROR" "Failed to encrypt the password."
        exit 1
    fi
    cd - > /dev/null 2>&1
    return 0
}

function getI2kNodeRoleOfIp()
{
    local app_ssh_ip="$1"
    RETURN[0]=""
    if [ -z "${app_ssh_ip}" ];then
        log_echo "INFO" "Excute getI2kNodeRoleOfIp the app_ssh_ip is null." 
        return 0
    fi
    
    echo "X${app_ssh_ip}" |grep -iEw "X${APP_IPV4}|X${APP_IPV6}|X${APP_MGR_IPV6}"
    if [ $? -eq 0 ];then
        RETURN[0]="Single"
        log_echo "INFO" "Excute getI2kNodeRoleOfIp Single." 
        return 0
    fi
    
    echo "X${app_ssh_ip}" |grep -iEw "X${APP_Primary_IPV4}|X${APP_Primary_IPV6}|X${APP_Primary_MGR_IPV6}"
    if [ $? -eq 0 ];then
        RETURN[0]="Primary"
        log_echo "INFO" "Excute getI2kNodeRoleOfIp Primary." 
        return 0
    fi
    
    echo "X${app_ssh_ip}" |grep -iEw "X${APP_Secondary_IPV4}|X${APP_Secondary_IPV6}|X${APP_Secondary_MGR_IPV6}"
    if [ $? -eq 0 ];then
        RETURN[0]="Secondary"
        log_echo "INFO" "Excute getI2kNodeRoleOfIp Secondary." 
        return 0
    fi
    
    log_echo "INFO" "Excute getI2kNodeRoleOfIp finish." 
    return 0
}

function cleanup()
{
    sed -i "/passwd=/d" ${dv_cfg_file}.tmp
    find ${PreSet_PATH} -name "*.log" -print0| xargs -0 --no-run-if-empty chmod 640 > /dev/null 2>&1
    chmod 600 ${PreSet_PATH}/Common/certificate/*
    find ${PreSet_PATH} -type f -a \( -name "*.crt" -o -name "*.pem" -o -name "*.csr" \) -print0 | xargs -0 --no-run-if-empty chmod 600
}


function set_pod_iptables()
{
    log_echo "INFO" "start set_pod_iptables..." 
    local app_ssh_ip=$1
    local app_scp_ip=$1
    local root_pwd=$2
    local node_role="$3"
    local do_type="$4"
    local app_ssh_user="$5"
    local app_ssh_pwd="$6"
    local scene_type="$7"
    
    if [ "X${do_type}" != "Xadd_pod_iptables" -a "X${do_type}" != "Xdel_pod_iptables" ];then
        log_echo "ERROR" "The do_type=${do_type} is error,need is add_pod_iptables or del_pod_iptables, please check set_pod_iptables."
        return 1
    fi
    
    if [ "X${node_role}" == "XAllInOne" ];then
        log_echo "INFO" "The node_role=${node_role} ,skip it." 
        return 0
    fi
    
    local isIpv6=$(echo ${app_scp_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        app_scp_ip="\[${app_scp_ip}\]"
    fi
    
    tmp_dir=/tmp/DVPreSet
    if [ ! -z "${app_ssh_user}" ];then
        tmp_dir=/tmp/DVPreSet_upgrade
    fi
    
    if [ "X${scene_type}" == "Xextend" ];then
        tmp_dir=/tmp/DVPreSet_extend
    fi
    
    if [ "X${node_role}" == "XPrimary" ];then
        if [ "X${scene_type}" == "Xextend" ];then
            mkdir -p ${tmp_dir}
            chmod 755 -R ${tmp_dir}
        fi
        
        cp -rpf ${PreSet_PATH}/Common/DV_PreSet_APP.sh ${tmp_dir}
        if [ ! -f ${PreSet_PATH}/Common/DV_config.properties.tmp ];then
            cp -rpf ${PreSet_PATH}/Common/DV_config.properties ${PreSet_PATH}/Common/DV_config.properties.tmp
        fi
        cp -rpf ${PreSet_PATH}/Common/DV_config.properties.tmp ${tmp_dir}
        cp -rpf ${PreSet_PATH}/Common/utils_common.sh ${tmp_dir}
        cp -rpf ${PreSet_PATH}/Common/utils_os.sh ${tmp_dir}
        cp -rpf ${PreSet_PATH}/Common/utils_uniep.sh ${tmp_dir}
        
        sh ${tmp_dir}/DV_PreSet_APP.sh ${do_type}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset cmd:[ sh ${tmp_dir}/DV_PreSet_APP.sh ${do_type} ] in DV node ${app_ssh_ip} failed"
            return 1
        fi
        log_echo "INFO" "The node_role=${node_role} ${do_type} finished.ret=$?" 
        return 0
    fi
    
    if [ "X${node_role}" != "XSecondary" ];then
        log_echo "INFO" "The node_role=${node_role}.skip it." 
        return 0
    fi
    
    if [ "X${root_pwd}" == "X" ];then
        log_echo "ERROR" "The root password for ${app_ssh_ip} is null, please check"
        return 1
    fi
    
    local file_list="DV_config.properties.tmp DV_PreSet_APP.sh utils_common.sh utils_uniep.sh utils_os.sh repair_iptables.sh repair_iptables_lvs.sh"
    log_echo "INFO" "Begin to execute set_pod_iptables with DV_PreSet_APP.sh..."
    if [ ! -f ${PreSet_PATH}/Common/DV_config.properties.tmp ];then
        cp -rpf ${PreSet_PATH}/Common/DV_config.properties ${PreSet_PATH}/Common/DV_config.properties.tmp
    fi
    
    if [ ! -z "${app_ssh_user}" -a "X${scene_type}" != "Xextend" ];then
        for tmpfile in ${file_list}
        do
            auto_smart_ssh ${app_ssh_pwd} "${app_ssh_user}@${app_ssh_ip} chmod u+w ${tmp_dir}/${tmpfile}" > /dev/null 2>&1
            auto_scp ${app_ssh_pwd} ${PreSet_PATH}/Common/${tmpfile} "${app_ssh_user}@${app_scp_ip}:${tmp_dir}"
        done
        
        auto_scp ${app_ssh_pwd} /os_config/DV_Config_MergeCluster_Docker.config "${app_ssh_user}@${app_scp_ip}:${tmp_dir}"
        
        ssh_execute "${app_ssh_user}" "${app_ssh_pwd}" "${root_pwd}" "${app_ssh_ip}" "mv ${tmp_dir}/DV_Config_MergeCluster_Docker.config /os_config/DV_Config_MergeCluster_Docker.config"
        
        ssh_execute "${app_ssh_user}" "${app_ssh_pwd}" "${root_pwd}" "${app_ssh_ip}" "sh ${tmp_dir}/DV_PreSet_APP.sh ${do_type}" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Preset dv ${do_type} in DV node ${app_ssh_ip} failed"
            return 1
        fi
    else
        if [ "X${scene_type}" == "Xextend" ];then
            ssh_execute "${app_ssh_user}" "${app_ssh_pwd}" "${root_pwd}" "${app_ssh_ip}" "mkdir -p ${tmp_dir}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Execute cmd:[ mkdir -p ${tmp_dir} ] at node ${app_ssh_ip} failed, please check..."
                return 1
            fi
            ssh_execute "${app_ssh_user}" "${app_ssh_pwd}" "${root_pwd}" "${app_ssh_ip}" "chown -R ${app_ssh_user}. ${tmp_dir}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Execute cmd:[ chown -R ${app_ssh_user}. ${tmp_dir} ] at node ${app_ssh_ip} failed, please check..."
                return 1
            fi
            ssh_execute "${app_ssh_user}" "${app_ssh_pwd}" "${root_pwd}" "${app_ssh_ip}" "chmod 755 -R ${tmp_dir}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Execute cmd:[ chmod 755 -R ${tmp_dir} ] at node ${app_ssh_ip} failed, please check..."
                return 1
            fi
            
            for tmpfile in ${file_list}
            do
                auto_scp ${app_ssh_pwd} ${PreSet_PATH}/Common/${tmpfile} "${app_ssh_user}@${app_scp_ip}:${tmp_dir}"
            done
            
            ssh_execute "${app_ssh_user}" "${app_ssh_pwd}" "${root_pwd}" "${app_ssh_ip}" "sh ${tmp_dir}/DV_PreSet_APP.sh ${do_type}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Execute cmd:[ sh ${tmp_dir}/DV_PreSet_APP.sh ${do_type} ] at node ${app_ssh_ip} failed, please check..."
                return 1
            fi
        else
            for tmpfile in ${file_list}
            do
                auto_scp ${root_pwd} ${PreSet_PATH}/Common/${tmpfile} "root@${app_scp_ip}:${tmp_dir}"
            done
            
            auto_smart_ssh ${root_pwd} "root@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh ${do_type}" > /dev/null 2>&1
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Preset dv ${do_type} in DV node ${app_ssh_ip} failed"
                return 1
            fi
        fi
    fi
    log_echo "INFO" "execute DV_PreSet_APP.sh finished."
    log_echo "INFO" "${do_type} finished."
}


function decrypt_pod_pwfile()
{
    local tmp_type="$1"
    if [ -z "${tmp_type}" ];then
        local pod_pwfile=${PreSet_PATH}/Common/pod_pwfile.json
        user_pwd_encrypt=$(cat ${pod_pwfile}| sed 's/}/\n/g' | grep -w "root" | awk -F : '{print $3}')
        if [ -z "${user_pwd_encrypt}" ];then
            log_echo "ERROR" "get root password failed.of decrypt_pod_pwfile"
            exit 1
        fi
        user_pwd_encrypt=${user_pwd_encrypt#*\"}
        user_pwd_encrypt=${user_pwd_encrypt%%\"*}
        decrypt_password ${user_pwd_encrypt}
        root_pwd_de=${decryptPasswd}
        return 0
    fi
    
    local pod_pwfile=${PreSet_PATH}/Common/${tmp_type}_pod_pwfile.json
    tmp_user_name=$(cat ${pod_pwfile}| sed 's/}/\n/g' | grep -w "\"name\":" | awk -F : '{print $2}')
    tmp_user_name=${tmp_user_name#*\"}
    tmp_user_name=${tmp_user_name%%\"*}
    
    user_pwd_encrypt=$(cat ${pod_pwfile}| sed 's/}/\n/g' | grep -w "${tmp_user_name}" | awk -F : '{print $3}')
    if [ -z "${user_pwd_encrypt}" ];then
        log_echo "ERROR" "get ${tmp_user_name} password failed.of decrypt_pod_pwfile"
        exit 1
    fi
    user_pwd_encrypt=${user_pwd_encrypt#*\"}
    user_pwd_encrypt=${user_pwd_encrypt%%\"*}
    
    tmp_pwd_de=$(su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('${user_pwd_encrypt}'); print(tmp)\"")
    
    if [ -z "${tmp_pwd_de}" ];then
        log_echo "ERROR" "decrypt tmp_pwd_de password failed.of decrypt_pod_pwfile"
        exit 1
    fi
}

function modify_pod_ssh_port()
{
    log_echo "INFO" "start modify_pod_ssh_port..."
    local app_ssh_ip=$1
    local app_scp_ip=$1
    local root_pwd=$2
    local do_type="$3"
 
    local isIpv6=$(echo ${app_scp_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        app_scp_ip="\[${app_scp_ip}\]"
    fi
 
    tmp_dir=/tmp/DVPreSet
    if [ "X${root_pwd}" == "X" ];then
        log_echo "ERROR" "The root password for ${app_ssh_ip} is null, please check"
        return 1
    fi
 
    local file_list="DV_config.properties.tmp DV_PreSet_APP.sh utils_common.sh utils_uniep.sh utils_os.sh"
    log_echo "INFO" "Begin to execute modify_pod_ssh_port with DV_PreSet_APP.sh..."
    if [ ! -f ${PreSet_PATH}/Common/DV_config.properties.tmp ];then
        cp -rpf ${PreSet_PATH}/Common/DV_config.properties ${PreSet_PATH}/Common/DV_config.properties.tmp
    fi
 
    for tmpfile in ${file_list}
    do
        auto_scp ${root_pwd} ${PreSet_PATH}/Common/${tmpfile} "root@${app_scp_ip}:${tmp_dir}"
    done
 
    auto_smart_ssh ${root_pwd} "root@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh modify_ssh_port ${do_type}" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Preset dv ${do_type} in DV node ${app_ssh_ip} failed"
        return 1
    fi
    log_echo "INFO" "execute DV_PreSet_APP.sh finished."
    log_echo "INFO" "modify_pod_ssh_port finished."
}

function flush_ssh_port()
{
    cat /etc/ssh/sshd_config 2> /dev/null| grep -w '^Port' | grep -v '#' > /dev/null 2>&1
    if [ $? -ne 0 ];then
        i2k_ssh_port=22
    else
        i2k_ssh_port=$(cat /etc/ssh/sshd_config | grep -w "^Port" | grep -v "#" |awk -F 'Port' '{print $2}' | sed s/[[:space:]]//g)
    fi
}

function restart_uniep_service()
{
    log_echo "INFO" "Begin to restart uniep service..."
    local uniep_service_dir="UniEPService"
    if [ -d "${INSTALL_PATH}/manager/apps/UniEPLiteService" ]; then
        uniep_service_dir="UniEPLiteService"
    fi

    su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopapp -app ${uniep_service_dir} -nodeip global -tenant manager"
    su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd startapp -app ${uniep_service_dir} -nodeip global -tenant manager"
}

function del_pod_iptables_after_import_product_information()
{
    ## del iptables
    if [ -f "/.dockerenv" -a "X${netWorkType}" == "XM" ];then
        flush_ssh_port
        set_pod_iptables "${DVPrimary_IPV4}" "not_need_pwd" "Primary" "del_pod_iptables" || exit 1
        decrypt_pod_pwfile
        set_pod_iptables "${DVSecondary_IPV4}" "${root_pwd_de}" "Secondary" "del_pod_iptables" || exit 1
        if [ "X${host_pre_install_key}" == "XHost_PreInstall" ]; then
            if [ -z "${root_pwd_de}" ];then
                log_echo "ERROR" "root_pwd_de is null."
                exit 1
            fi
            local tmp_node_ip=""
            local tmp_ip_list="${DVThird_IPV4},${SMExtend_Cluster_Node_IPV4_List},${OMSAExtend_Cluster_Node_IPV4_List},${IMExtend_Cluster_Node_IPV4_List},${SDExtend_Cluster_Node_IPV4_List}"
            if [ "${is_old_extend_mode}" == "Yes" ];then
                tmp_ip_list="${DVThird_IPV4},${Extend_Cluster_Node_IPV4_List}"
            fi
            for tmp_node_ip in $(echo "${tmp_ip_list}" |sed "s/,/ /g");do
                [ -z "${tmp_node_ip}" ] && continue
                modify_pod_ssh_port "${tmp_node_ip}" "${root_pwd_de}" "rollback"
            done
        fi
        flush_ssh_port
    fi
}

function import_product_information()
{
    local DV_BatchSet_Path="$1"
    local product_path=${DV_BatchSet_Path}/product_info
    local product_pkg_name=${netWorkPath}.zip
    
    log_echo "INFO" "Start import_product_information."
    if [ -z "${DV_BatchSet_Path}" ];then
        log_echo "ERROR" "The DV_BatchSet_Path=${DV_BatchSet_Path} is null."
        return 1
    fi
    
    if [ ! -f ${product_pkg_name} ];then
        log_echo "ERROR" "The pkg ${product_pkg_name} is not exist."
        return 1
    fi
    
    ## The dir is exist and path is not /. 
    if [ -d ${product_path}  -a  "X$(echo ${product_path}|sed 's#/##g' |sed 's/ //g')" != "X" ];then
        rm -rf ${product_path:?}
    fi
    
    mkdir -p ${product_path}
    unzip -o ${product_pkg_name} -d ${product_path}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute cmd[unzip -o ${product_pkg_name} -d ${product_path}]failed."
        return 1
    fi
    
    chown ossadm:ossgroup ${product_path}/* 
    chmod -R 755 ${product_path}
    
    local setproductinfo_parameters=" -product ${product_path}/product_sop.json -nodes ${product_path}/nodes_sop.json"
    if [ "X${DB_TYPE}" != "Xzenith" ];then
        log_echo "INFO" "DB_TYPE=${DB_TYPE} update setproductinfo_parameters"
        setproductinfo_parameters="${setproductinfo_parameters} -dbinfo ${product_path}/dbinfo_sop.json"
    fi
    
    if [ -f ${product_path}/networkinfo_sop.json  ];then
        log_echo "INFO" "the ${product_path}/networkinfo_sop.json is exist.set add parameters."
        setproductinfo_parameters="${setproductinfo_parameters} -networkinfo  ${product_path}/networkinfo_sop.json"
    fi
    
    ## add iptbales
    if [ -f "/.dockerenv" -a "X${netWorkType}" == "XM" ];then
        set_pod_iptables "${DVPrimary_IPV4}" "not_need_pwd" "Primary" "add_pod_iptables" || exit 1
        decrypt_pod_pwfile
        set_pod_iptables "${DVSecondary_IPV4}" "${root_pwd_de}" "Secondary" "add_pod_iptables"
        if [ $? -ne 0 ];then
            set_pod_iptables "${DVPrimary_IPV4}" "not_need_pwd" "Primary" "del_pod_iptables" || exit 1
            exit 1
        fi
        if [ "X${host_pre_install_key}" == "XHost_PreInstall" ]; then
            if [ -z "${root_pwd_de}" ];then
                log_echo "ERROR" "root_pwd_de is null."
                exit 1
            fi
            local tmp_node_ip=""
            local tmp_ip_list="${DVThird_IPV4},${SMExtend_Cluster_Node_IPV4_List},${OMSAExtend_Cluster_Node_IPV4_List},${IMExtend_Cluster_Node_IPV4_List},${SDExtend_Cluster_Node_IPV4_List}"
            if [ "${is_old_extend_mode}" == "Yes" ];then
                tmp_ip_list="${DVThird_IPV4},${Extend_Cluster_Node_IPV4_List}"
            fi
            for tmp_node_ip in $(echo "${tmp_ip_list}" |sed "s/,/ /g");do
                [ -z "${tmp_node_ip}" ] && continue
                modify_pod_ssh_port "${tmp_node_ip}" "${root_pwd_de}" "modify"
            done
        fi
    fi
    
    if [ "X${host_pre_install_key}" == "XHost_PreInstall" ]; then
        restart_uniep_service
        check_uniep_service_by_param "12" "5"
    fi
    
    log_echo "INFO" "start execute cmd[su - ossadm -c \"cd ${INSTALL_PATH}/manager/tools/resmgr;./setproductinfo.sh ${setproductinfo_parameters}\"]"
    su - ossadm -c "cd ${INSTALL_PATH}/manager/tools/resmgr;./setproductinfo.sh ${setproductinfo_parameters}"  >> ${LOG_FILE} 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "import_product_information failed.check log: ${LOG_FILE} "
        log_echo "ERROR" "If the error information cannot be determined based on the logs, you can log in to the UniEP management page to view the task failure details. "
        del_pod_iptables_after_import_product_information
        return 1
    fi
    flush_ssh_port
    update_product_pwd "${product_path}/product_sop.json"

    
    update_ossuser_sshkey
    
    del_pod_iptables_after_import_product_information

    if [ "X${host_pre_install_key}" == "XHost_PreInstall" ]; then
        restart_uniep_service
        check_uniep_service_by_param "12" "5"
    fi

    log_echo "INFO" "import_product_information successfully."
    return 0
}

function er_pwd_to_sop_by_ip()
{
    local node_ip="$1"
    local ossadm_preset_dir=/home/<USER>/DVPreSet
    log_echo "INFO" "[er_pwd_to_sop_by_ip] begin sed sop pwd in $node_ip and i2k_ssh_port=${i2k_ssh_port}..."
    ip addr | grep -wF "${node_ip}" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        local node_scp_ip=${node_ip}
        local isIpv6=$(echo ${node_ip} |grep ":" |grep -v "]")
        if [ "X${isIpv6}" != "X" ];then
            node_scp_ip="[${node_ip}]"
        fi

        ## mkdir temporary directory
        su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ip} mkdir -p ${ossadm_preset_dir}"
        su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ip} chmod 770 ${ossadm_preset_dir}"
        
        local product_info_path=""
        local product_info_name=""
        ## copy cert file to temporary directory
        for product_info_path in ${product_info_path_list}
        do
            ## get file name
            product_info_name=$(basename $product_info_path)

            su - ossadm -c "scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} ${product_info_path} ossadm@${node_scp_ip}:${ossadm_preset_dir}"
            ## remove remote old files
            su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ip} rm -f ${product_info_path} 2>/dev/null"
            su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ip} sudo -u ossuser rm -f ${product_info_path} 2>/dev/null"
            ## switch ossuser to move cert file
            su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ip} sudo -u ossuser cp -f ${ossadm_preset_dir}/${product_info_name} ${product_info_path}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "copy ${ossadm_preset_dir}/${product_info_name} to ${product_info_path} occurred an exception, please check."
                exit 1
            fi
            su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ip} sudo -u ossuser chmod 600 ${product_info_path}"
        done

        su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ip} rm -rf /home/<USER>/DVPreSet"
    fi
    log_echo "INFO" "[er_pwd_to_sop_by_ip] begin sed sop pwd in $node_ip and i2k_ssh_port=${i2k_ssh_port} End."
}

function update_er_pwd_to_SOP()
{
    product_info_path_list="${i2k_user_home}/issue_er_cert/server.p12_storePass ${i2k_user_home}/issue_er_cert/server.p12_keyPass ${i2k_user_home}/issue_er_cert/server_key.pem_keyPass ${i2k_user_home}/issue_er_cert/ca_cert_pwd"
    for product_info_path in ${product_info_path_list}
    do
        log_echo "INFO" "Begin to update er pwd to SOP for kmc in $product_info_path"
        local old_pwd=$(cat $product_info_path)
        local decrypt_pwd=$(echo "$old_pwd" |su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python -c \"import sys;from util import ossext;print(ossext.Cipher.decrypt(sys.stdin.read().strip()))\"" 2>&1)
        encrypt_by_uniep "${decrypt_pwd}" "SOP"
        local new_pwd="${pwd_return}"
        sed -i "s#$old_pwd#$new_pwd#g" "${product_info_path}"

        ## change cert permission mode for the next scp operations
        chmod 660 "${product_info_path}"
    done
    chmod 750 ${i2k_user_home}/issue_er_cert
    if [ $(stat -c %a ${i2k_user_home}) -lt 750 ];then
        ## avoid /home/<USER>
        chmod 750 ${i2k_user_home}
    fi

    local node_lists=$(su - ossadm -c 'source /opt/oss/manager/bin/engr_profile.sh;nodelists=$(python -c "from deployment import nodelist;print(\",\".join([node.get_mgmt_ip_addr() for node in nodelist.NodeListMgr().get_all_nodes()]))");echo ${nodelists}' 2>&1)

    if [ "X${node_lists}" == "X" ];then
        log_echo "ERROR" "Get node_lists for update_er_pwd_to_SOP failed, please check"
        exit 1
    fi
    log_echo "INFO" "[er_pwd_to_sop_by_ip] node_lists=${node_lists}"
    ## Temporary directory. Used for solve the problem that no root permission during the installation on the other node.
    node_preset_concurrent "er_pwd_to_sop_by_ip" "${node_lists}"

    for product_info_path in ${product_info_path_list}
    do
        ## recover cert permission mode
        chmod 600 "${product_info_path}"
    done
    chmod 700 ${i2k_user_home}/issue_er_cert
    
    ## delete certificate copy
    [ -f "${PreSet_PATH}/ca_cert_pwd" ] && rm -f "${PreSet_PATH}/ca_cert_pwd"
}

function check_copy_package_to_pod()
{
    local source_dir="$1"
    local dest_dir="$2"
    log_echo  "INFO" "check_copy_package_to_pod start..." 
    if [ -z "${source_dir}" ];then
        log_echo "ERROR" "The source_dir=${source_dir} is null."
        return 1
    fi
    
    if [ ! -d ${source_dir} ];then
        log_echo "ERROR" "The package path ${source_dir} is not exist."
        return 1
    fi
    
    local pkg_list=$(ls ${source_dir}/DigitalView*_ProductINSTALL*.zip)
    if [ -z "${pkg_list}" ];then
        log_echo "ERROR" "The pkg_list=${pkg_list} is null.The path ${source_dir} not found package DigitalView*_ProductINSTALL*.zip"
        return 1
    fi
    
    local check_asc=0
    for pkg in ${pkg_list};
    do
        if [ ! -f ${pkg}.asc -a ! -f ${pkg}.p7s ];then
            log_echo "ERROR" "Not found the ${pkg}.asc and ${pkg}.p7s file."
            check_asc=1
        fi
    done
    
    [ ${check_asc} -ne 0 ] && return 1
    
    local pkg_size_sum=0
    for pkg in ${pkg_list};
    do
        pkg_size=$(du -smP ${pkg}|awk '{print $1}')
        pkg_size_sum=$((${pkg_size_sum} + ${pkg_size}))
    done 
    
    local pkg_tmp_disk_size=$(df -Pm ${dest_dir} |tail -1|awk -F' ' '{print $(NF-2)}')
    if [ -z "${pkg_tmp_disk_size}" ];then
        log_echo "ERROR" "get pkg_tmp_disk_size=${pkg_tmp_disk_size} is null.get failed."
        return 1
    fi
    
    local need_pkg_size=$((${pkg_size_sum} + 1024))
    if [ ${pkg_tmp_disk_size} -lt ${need_pkg_size} ];then
        log_echo "ERROR" "The ${INSTALL_PATH}/manager/var/tmp available size is ${pkg_tmp_disk_size}M, but we need ${need_pkg_size}M at least..."
        return 1
    fi
    
    for pkg in ${pkg_list};
    do
        cp -rpf ${pkg} ${dest_dir}
        
        if [ -f ${pkg}.p7s ];then
            cp -rpf ${pkg}.p7s ${dest_dir}
        elif [ -f ${pkg}.asc ];then
            cp -rpf ${pkg}.asc ${dest_dir}
        else
            log_echo "ERROR" "Not found the ${pkg}.asc and ${pkg}.p7s file.cp failed."
            return 1
        fi
    done
    
    log_echo  "INFO"  "check_copy_package_to_pod finished."
    return 0 
}


function scanning_package()
{
    log_echo  "INFO" "Scanning package." 
    if [ -z "${SOFTWARE_PACKAGE_PATH}" ];then
        log_echo "ERROR" "The SOFTWARE_PACKAGE_PATH=${SOFTWARE_PACKAGE_PATH} is null."
        return 1
    fi
    
    if [ ! -d ${SOFTWARE_PACKAGE_PATH} ];then
        log_echo "ERROR" "The package path ${SOFTWARE_PACKAGE_PATH} is not exist."
        return 1
    fi
    
    local pkg_list=$(ls ${SOFTWARE_PACKAGE_PATH}/DigitalView*_ProductINSTALL*.zip)
    if [ -z "${pkg_list}" ];then
        log_echo "ERROR" "The pkg_list=${pkg_list} is null.The path ${SOFTWARE_PACKAGE_PATH} not found package DigitalView*_ProductINSTALL*.zip"
        return 1
    fi
    
    local check_asc=0
    for pkg in ${pkg_list};
    do
        if [ ! -f ${pkg}.asc -a ! -f ${pkg}.p7s ];then
            log_echo "ERROR" "Not found the ${pkg}.asc and ${pkg}.p7s file."
            check_asc=1
        fi
    done
    
    [ ${check_asc} -ne 0 ] && return 1
    
    local pkg_size_sum=0
    for pkg in ${pkg_list};
    do
        pkg_size=$(du -smP ${pkg}|awk '{print $1}')
        pkg_size_sum=$((${pkg_size_sum} + ${pkg_size}))
    done 
    
    local pkg_tmp_disk_size=$(df -Pm ${INSTALL_PATH}/manager/var/tmp |tail -1|awk -F' ' '{print $(NF-2)}')
    if [ -z "${pkg_tmp_disk_size}" ];then
        log_echo "ERROR" "get pkg_tmp_disk_size=${pkg_tmp_disk_size} is null.get failed."
        return 1
    fi
    
    local need_pkg_size=$((${pkg_size_sum} * 2))
    if [ ${pkg_tmp_disk_size} -lt ${need_pkg_size} ];then
        log_echo "ERROR" "The ${INSTALL_PATH}/manager/var/tmp available size is ${pkg_tmp_disk_size}M, but we need ${need_pkg_size}M at least..."
        return 1
    fi
    
    for pkg in ${pkg_list};
    do
        cp -rpf ${pkg} ${INSTALL_PATH}/manager/var/tmp
        
        if [ -f ${pkg}.p7s ];then
            cp -rpf ${pkg}.p7s ${INSTALL_PATH}/manager/var/tmp
        elif [ -f ${pkg}.asc ];then
            cp -rpf ${pkg}.asc ${INSTALL_PATH}/manager/var/tmp
        else
            log_echo "ERROR" "Not found the ${pkg}.asc and ${pkg}.p7s file.cp failed."
            return 1
        fi
    done
    
    chown -R ossadm:ossgroup ${INSTALL_PATH}/manager/var/tmp/*
    
    su - ossadm -c "cd ${INSTALL_PATH}/manager/tools/deployapp;bash scanpackage.sh -path ${INSTALL_PATH}/manager/var/tmp -output ${INSTALL_PATH}/manager/tools/deployapp/scanResult.json"  >> ${LOG_FILE} 2>&1
    local scanpackage_result=$?
    log_echo "INFO" "execute cmd[su - ossadm -c \"cd ${INSTALL_PATH}/manager/tools/deployapp;bash scanpackage.sh -path ${INSTALL_PATH}/manager/var/tmp -output ${INSTALL_PATH}/manager/tools/deployapp/scanResult.json\"] scanpackage_result=${scanpackage_result}." 
    if [ ${scanpackage_result} -ne 0 ];then
        log_echo "ERROR" "execute cmd[su - ossadm -c \"cd ${INSTALL_PATH}/manager/tools/deployapp;bash scanpackage.sh -path ${INSTALL_PATH}/manager/var/tmp -output ${INSTALL_PATH}/manager/tools/deployapp/scanResult.json\"] failed.check ${INSTALL_PATH}/manager/tools/deployapp/scanResult.json" 
        log_echo "ERROR" "If the error information cannot be determined based on the logs, you can log in to the UniEP management page to view the task failure details. "
        return 1
    fi
    rm -rf ${INSTALL_PATH}/manager/tools/deployapp/scanResult.json
    log_echo  "INFO"  "Successfully scanned product package"
    return 0 
}

function sed_key_value()
{
    local key="$1"
    local value="$2"
    local file="$3"
    
    if [ -z "${key}" -o -z "${value}" -o -z "${file}" ];then
        log_echo "ERROR" "key=${key} or value=${value} or file=${file} is null."
        exit 1
    fi
    
    if [ ! -f ${file} ];then
        log_echo "ERROR" "The file=${file} is not exists."
        exit 1
    fi
    
    sed -i "s#{{${key}}}#${value}#g" ${file} 
    if [ $? -ne 0 ];then
        log_echo "ERROR" "modify key=${key} failed."
        exit 1
    fi
    
}

function cfg_MCNetwork_product_json()
{
    local json_file="$1"
    if [ -z "${json_file}" ];then
        log_echo "INFO" "json_file=${json_file} is null."
        return 0
    fi
    
    if [ ! -f ${json_file} ];then
        log_echo "INFO" "json_file=${json_file} is not exists."
        return 0
    fi
    declare -A KEY_LIST_MAP=()
    KEY_LIST_MAP[IPType_OF_DVPrimary_IP]="${DVPrimary_IPV4}"
    KEY_LIST_MAP[IPType_OF_DVSecondary_IP]="${DVSecondary_IPV4}"
    KEY_LIST_MAP[IPType_OF_DVThird_IP]="${DVThird_IPV4}"
    
    KEY_LIST_MAP[IPType_OF_DVPrimary_IP_Url]="${DVPrimary_IPV4}"
    KEY_LIST_MAP[IPType_OF_DVSecondary_IP_Url]="${DVSecondary_IPV4}"
    KEY_LIST_MAP[IPType_OF_DVThird_IP_Url]="${DVThird_IPV4}"
    
    KEY_LIST_MAP[IPType_OF_DVFloatIP]="${DVFloatIP_IPV4}"
    KEY_LIST_MAP[IPType_OF_DVFloatIP_Url]="${DVFloatIP_IPV4}"
    KEY_LIST_MAP[IPType_OF_DVFloatIP_List]="${DVFloatIP_IPV4}"
    KEY_LIST_MAP[IPType_OF_DVFloatIP_NetMask_List]="${DVFloatIP_IPV4_NETMASK}"
    KEY_LIST_MAP[IPType_OF_DVFloatIP_NIC_List]="${DVPrimary_IPV4_NIC}:1"
    KEY_LIST_MAP[IPType_OF_DV_NODE_IP_LIST]="${DVPrimary_IPV4},${DVSecondary_IPV4},${DVThird_IPV4}"
    KEY_LIST_MAP[IPType_OF_DVSecondary_DVThird_IP]="${DVSecondary_IPV4},${DVThird_IPV4}"
    
    if [ "X${IPType}" == "X1" ];then
        KEY_LIST_MAP[IPType_OF_DVPrimary_IP]="${DVPrimary_IPV6}"
        KEY_LIST_MAP[IPType_OF_DVSecondary_IP]="${DVSecondary_IPV6}"
        KEY_LIST_MAP[IPType_OF_DVThird_IP]="${DVThird_IPV6}"
        
        KEY_LIST_MAP[IPType_OF_DVPrimary_IP_Url]="[${DVPrimary_IPV6}]"
        KEY_LIST_MAP[IPType_OF_DVSecondary_IP_Url]="[${DVSecondary_IPV6}]"
        KEY_LIST_MAP[IPType_OF_DVThird_IP_Url]="[${DVThird_IPV6}]"
        
        KEY_LIST_MAP[IPType_OF_DVFloatIP]="${DVFloatIP_IPV6}"
        KEY_LIST_MAP[IPType_OF_DVFloatIP_Url]="[${DVFloatIP_IPV6}]"
        KEY_LIST_MAP[IPType_OF_DVFloatIP_List]="${DVFloatIP_IPV6}"
        KEY_LIST_MAP[IPType_OF_DVFloatIP_NetMask_List]="${DVFloatIP_IPV6_NETMASK}"
        KEY_LIST_MAP[IPType_OF_DVFloatIP_NIC_List]="${DVFloatIP_IPV6_NIC}"
        
        KEY_LIST_MAP[IPType_OF_DV_NODE_IP_LIST]="${DVPrimary_IPV6},${DVSecondary_IPV6},${DVThird_IPV6}"
        KEY_LIST_MAP[IPType_OF_DVSecondary_DVThird_IP]="${DVSecondary_IPV6},${DVThird_IPV6}"
        
    elif [ "X${IPType}" == "X2" ];then
        log_echo "INFO" "IPType=${IPType} is not suport."
    fi
    
    local keys="IPType_OF_DVPrimary_IP IPType_OF_DVSecondary_IP IPType_OF_DVThird_IP"
    keys="${keys} IPType_OF_DVPrimary_IP_Url IPType_OF_DVSecondary_IP_Url IPType_OF_DVThird_IP_Url"
    keys="${keys} IPType_OF_DVFloatIP IPType_OF_DVFloatIP_Url IPType_OF_DVFloatIP_List"
    keys="${keys} IPType_OF_DVFloatIP_NetMask_List IPType_OF_DVFloatIP_NIC_List"
    keys="${keys} IPType_OF_DV_NODE_IP_LIST IPType_OF_DVSecondary_DVThird_IP"
    
    ## sed iptype of dv cfg 
    for key in ${keys};do
        ## key value file
        sed_key_value "${key}" "${KEY_LIST_MAP[${key}]}" "${json_file}"
    done
    
    ##   GLOBAL_UNIFIED_OSS_DB_USER_PASSWORD  GLOBAL_UNIFIED_OSS_USER_PASSWORD
    local db_os_userpasswd_encrypt=""
    if [ "X${DB_TYPE}" == "Xoracle" ];then
        #${remote_oracle_passwd}
        if [ "X${remote_oracle_passwd}" == "X" ];then
            log_echo "ERROR" "The remote_oracle_passwd is null."
            exit 1
        fi
        
        encrypt_by_uniep "${remote_oracle_passwd}" ""
        db_os_userpasswd_encrypt="${pwd_return}"
        if [ "X${db_os_userpasswd_encrypt}" == "X" ];then
            log_echo "ERROR" "Encrypt db_os_userpasswd_encrypt failed"
            exit 1
        fi
    fi
    
    encrypt_by_uniep "${db_user_pwd}" ""
    local db_dbapasswd_encrypt="${pwd_return}"
    
    if [ "X${db_dbapasswd_encrypt}" == "X" ];then
        log_echo "ERROR" "Get db_dbapasswd_encrypt failed"
        exit 1
    fi
    
    if [ "X${web_admin_pwd}" == "X" ];then
        log_echo "ERROR" "Get web_admin_pwd failed"
        exit 1
    fi
    
    ## GLOBAL_UNIFIED_OSS_DB_USER_PASSWORD
    sed -i "s|{{GLOBAL_UNIFIED_OSS_DB_USER_PASSWORD}}|${db_dbapasswd_encrypt}|g" ${json_file}
    sed -i "s|{{web_admin_user_value}}|${web_admin_pwd}|g" ${json_file}
    
    ossadm_pwd_encrypt=$(cat ${PreSet_PATH}/Common/pwfile.json | sed 's/}/\n/g' | grep -w ossadm | awk -F : '{print $3}')
    ossadm_pwd_encrypt=${ossadm_pwd_encrypt#*\"}
    ossadm_pwd_encrypt=${ossadm_pwd_encrypt%%\"*}
    if [ "X${ossadm_pwd_encrypt}" == "X" ];then
        log_echo "ERROR" "Get ossadm_userpasswd failed"
        exit 1
    fi
    ## GLOBAL_UNIFIED_OSS_USER_PASSWORD
    sed -i "s|{{GLOBAL_UNIFIED_OSS_USER_PASSWORD}}|${ossadm_pwd_encrypt}|g" ${json_file}
    
    ## DV_SHARE_PATH 
    sed -i "s#{{DV_SHARE_PATH}}#${INSTALL_PATH}/share/SOP/DVEngineeringService/dvshare#g" ${json_file}
    
    ## NetworkType --MergeCluster 
    sed -i "s#{{NetworkType}}#MiniCluster#g" ${json_file}
    
    ## FMDeployMode --Cluster
    sed -i "s#{{FMDeployMode}}#Cluster#g" ${json_file}
    
    ## isAppNbiIpExtend --No
    sed -i "s#{{isAppNbiIpExtend}}#No#g" ${json_file}
    
    ## APP_EXTERNAL_NBI_IPV4 --127.0.0.1
    sed -i "s#{{APP_EXTERNAL_NBI_IPV4}}#127.0.0.1#g" ${json_file}
    
      

    log_echo "INFO" "cfg_MCNetwork_product_json finish."
    
}
function get_es_node_ip_list()
{
    ES_NODE_IP_LIST=$(${PreSet_PATH}/UniEp/python/bin/python ${PreSet_PATH}/tools/handlejson.py "getinfo_product" "${json_tmp_path}/product_sop.json" "ES_NODE_IP_LIST")
    local pm1_total_memory=0
    if [ "X${PM_Cluster_Node_MGR_IPV6_List}" != "X" -a "X${PM_Cluster_Node_MGR_IPV6_List}" != "X{{PM_Cluster_Node_MGR_IPV6_List}}" ] || [ "X${PM_Cluster_Node_IPV4_List}" != "X" -a "X${PM_Cluster_Node_IPV4_List}" != "X{{PM_Cluster_Node_IPV4_List}}" ];then
        check_total_memory "${PreSet_PATH}/PM1_memtotal.txt"
        ## kb to GB  (1048576=1024*1024)
        pm1_total_memory=$((${total_memory}/1048576))
        log_echo "INFO" "The pm1_total_memory=${pm1_total_memory}G"
    fi
    
    local need_add_ip_list=""
    if [ "X${IPType}" != "X0"  ];then
        ## ipv6单栈  ipv4-6双栈 
        if [ "X${APP_Extend_Node_MGR_IPV6_List}" != "X" ];then
            need_add_ip_list="${APP_Extend_Node_MGR_IPV6_List}"
        fi
        if [ "X${PM_Cluster_Node_MGR_IPV6_List}" != "X" -a "X${PM_Cluster_Node_MGR_IPV6_List}" != "X{{PM_Cluster_Node_MGR_IPV6_List}}" ];then
            if [ ${pm1_total_memory} -ge 60 ];then
                log_echo "INFO" "The pm node total memory is greater than or equal to 60 GB, need to add ip list:${PM_Cluster_Node_MGR_IPV6_List} to ES_NODE_IP_LIST."
                if [ -z "${need_add_ip_list}" ];then
                    need_add_ip_list="${PM_Cluster_Node_MGR_IPV6_List}"
                else
                    need_add_ip_list="${need_add_ip_list},${PM_Cluster_Node_MGR_IPV6_List}"
                fi
            fi
        fi
    else
        ## ipv4 单栈
        if [ "X${APP_Extend_Node_IPV4_List}" != "X" ];then
            need_add_ip_list="${APP_Extend_Node_IPV4_List}"
        fi
        if [ "X${PM_Cluster_Node_IPV4_List}" != "X" -a "X${PM_Cluster_Node_IPV4_List}" != "X{{PM_Cluster_Node_IPV4_List}}" ];then
            if [ ${pm1_total_memory} -ge 60 ];then
                log_echo "INFO" "The pm node total memory is greater than or equal to 60 GB, need to add ip list:${PM_Cluster_Node_IPV4_List} to ES_NODE_IP_LIST."
                if [ -z "${need_add_ip_list}" ];then
                    need_add_ip_list="${PM_Cluster_Node_IPV4_List}"
                else
                    need_add_ip_list="${need_add_ip_list},${PM_Cluster_Node_IPV4_List}"
                fi
            fi
        fi
    fi
    log_echo "INFO" "The need_add_ip_list=${need_add_ip_list} ES_NODE_IP_LIST=${ES_NODE_IP_LIST}"
    
    if [ ! -z "${need_add_ip_list}" ];then
        ES_NODE_IP_LIST="${ES_NODE_IP_LIST},${need_add_ip_list}"
        log_echo "INFO" "Add ${need_add_ip_list} to ES_NODE_IP_LIST"
    fi
    log_echo "INFO" "The ES_NODE_IP_LIST=${ES_NODE_IP_LIST}"
    return 0
}

function update_product_pwd()
{
    log_echo "INFO" "Begin to update product pwd for kmc"
    
    local product_info_path="$1"
    
    local pwd_list=$(cat "${product_info_path}" | grep "AAAAHgAAAAAAAAA")
    local OLD_IFS=$IFS
    local IFS=$'\n'
    for pwd in ${pwd_list}
    do
        local old_pwd=$(echo $pwd | awk -F '"' '{print $4}')
        local decrypt_pwd=$(su - ossadm -c "source ${INSTALL_PATH}/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('$old_pwd'); print(tmp)\"" 2>&1)
        encrypt_by_uniep "${decrypt_pwd}" "SOP"
        local new_pwd="${pwd_return}"
        sed -i "s#$old_pwd#$new_pwd#g" "${product_info_path}"
    done
    local IFS=$OLD_IFS
    su - ossadm -c "cd ${INSTALL_PATH}/manager/tools/resmgr;./modifyproductinfo.sh -input ${product_info_path}"  >> ${LOG_FILE} 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "update password in product info failed.check log: ${LOG_FILE} "
        return 1
    fi
    
}

function Start_drbd_primary()
{
    primary_ip=$1
    tmp_dir=/tmp/DVPreSet
    if [ "X${primary_root_pswd}" == "X" ];then
        if [ "X${root_password}" == "X" ];then
            echo_pwd "Please input root\'s password of ${primary_ip} "
            primary_root_pswd="$PwdReturn"
        else
            primary_root_pswd="${root_password}"
        fi
    fi
    auto_smart_ssh ${primary_root_pswd} "root@${primary_ip} ${tmp_dir}/DRBD/drbd_install.sh start_primary"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Start the drbd at primary node ${primary_ip} failed, please check"
        exit 1
    fi
}

function Start_drbd()
{
    Check_execute "Start_drbd"
    if [ $? -eq 0 ];then
        return 0
    fi
    
    primary_app_ip=$1
    secondary_app_ip=$2
    
    if [ "X${netWorkType}" != "XM" ];then
        if [ "X${primary_root_pswd}" == "X" ];then
            if [ "X${root_password}" == "X" ];then
                echo_pwd "Please input root\'s password of ${primary_app_ip} "
                primary_root_pswd="$PwdReturn"
            else
                primary_root_pswd="${root_password}"
            fi
        fi
    fi
    
    if [ "X${secondary_root_pswd}" == "X" ];then
        if [ "X${root_password}" == "X" ];then
            echo_pwd "Please input root\'s password of ${secondary_app_ip} "
            secondary_root_pswd="$PwdReturn"
        else
            secondary_root_pswd="${root_password}"
        fi
    fi
    
    cat /etc/euleros-release 2>/dev/null | grep -i "EulerOS"
    if [ $? -eq 0 ];then
        log_echo "INFO" "stop drbd service at secondary"
        auto_smart_ssh ${secondary_root_pswd} "root@${secondary_app_ip} systemctl stop drbd.service"
        
        log_echo "INFO" "stop drbd service at primary"
        [ "X${netWorkType}" != "XM" ] && auto_smart_ssh ${primary_root_pswd} "root@${primary_app_ip} systemctl stop drbd.service"
        [ "X${netWorkType}" == "XM" ] && systemctl stop drbd.service
        
        log_echo "INFO" "start drbd service at secondary"
        auto_smart_ssh ${secondary_root_pswd} "root@${secondary_app_ip} systemctl start drbd.service" &
        
        log_echo "INFO" "start drbd service at primary"
        [ "X${netWorkType}" != "XM" ] && auto_smart_ssh ${primary_root_pswd} "root@${primary_app_ip} systemctl start drbd.service"
        [ "X${netWorkType}" == "XM" ] && systemctl start drbd.service
        
    else
        log_echo "INFO" "start drbd service at secondary"
        auto_smart_ssh ${secondary_root_pswd} "root@${secondary_app_ip} /etc/init.d/drbd start" &
        
        log_echo "INFO" "start drbd service at primary"
        [ "X${netWorkType}" != "XM" ] && auto_smart_ssh ${primary_root_pswd} "root@${primary_app_ip} /etc/init.d/drbd start"
        [ "X${netWorkType}" == "XM" ] && /etc/init.d/drbd start
    fi
    
    log_echo "INFO" "start drbd at primary"
    if [ "X${netWorkType}" == "XM" ];then
        tmp_dir=/tmp/DVPreSet
        sh ${tmp_dir}/DRBD/drbd_install.sh start_primary
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Start the drbd at primary node ${primary_app_ip} failed, please check"
            exit 1
        fi
    else
        Start_drbd_primary "${primary_app_ip}"
    fi
    
    auto_smart_ssh ${secondary_root_pswd} "root@${secondary_app_ip} drbdadm secondary $drbd_resource"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Set secondary_app_ip as secondary ${secondary_app_ip} failed, please check"
        exit 1
    fi
        
    log_echo "INFO" "disconnect drbd at primary"
    if [ "X${netWorkType}" == "XM" ];then
        drbdadm disconnect all
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Excute drbddadm disconnect all at ${primary_app_ip} failed, please check"
            exit 1
        fi
    else
        auto_smart_ssh ${primary_root_pswd} "root@${primary_app_ip} drbdadm disconnect all"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Excute drbddadm disconnect all at ${primary_app_ip} failed, please check"
            exit 1
        fi
    fi
    
    check_failed=""
    local ret=0
    if [ "X${netWorkType}" == "XM" ];then
        systemctl list-unit-files |grep drbd|grep enabled
        ret=$?
    else
        auto_smart_ssh ${primary_root_pswd} "root@${primary_app_ip} systemctl list-unit-files |grep drbd|grep enabled"
        ret=$?
    fi
    
    if [ ${ret} -ne 0 ];then
        [ "X${netWorkType}" != "XM" ] && auto_smart_ssh ${primary_root_pswd} "root@${primary_app_ip} systemctl enable drbd.service"
        [ "X${netWorkType}" == "XM" ] && systemctl enable drbd.service
        
        sleep 5
        
        if [ "X${netWorkType}" == "XM" ];then
            systemctl list-unit-files |grep drbd|grep enabled
            ret=$?
        else
            auto_smart_ssh ${primary_root_pswd} "root@${primary_app_ip} systemctl list-unit-files |grep drbd|grep enabled"
            ret=$?
        fi
        
        if [ ${ret} -ne 0 ];then
            check_failed="${primary_app_ip}"
        fi
    fi
    
    auto_smart_ssh ${secondary_root_pswd} "root@${secondary_app_ip} systemctl list-unit-files |grep drbd|grep enabled"
    if [ $? -ne 0 ];then
        auto_smart_ssh ${secondary_root_pswd} "root@${secondary_app_ip} systemctl enable drbd.service"
        sleep 5
        auto_smart_ssh ${secondary_root_pswd} "root@${secondary_app_ip} systemctl list-unit-files |grep drbd|grep enabled"
        if [ $? -ne 0 ];then
            check_failed="${check_failed} ${secondary_app_ip}"
        fi
    fi
    
    if [ "X${check_failed}" != "X" ];then
        log_echo "ERROR" "Check drbd auto-starting of ${check_failed} failed, please check"
        exit 1
    fi
     
    echo "Start_drbd : success" >> ${action_tag}
}

function Check_drbddisk()
{
    local drbd_primary_ip=$1
    local drbd_secondary_ip=$2
    local drbd_disk=$3
    
    Check_execute "APP_PreSet_${drbd_primary_ip}"
    ret1=$?
    Check_execute "APP_PreSet_${drbd_secondary_ip}"
    ret2=$?
    if [ ${ret1} -eq 0 -a ${ret2} -eq 0 ];then
        log_echo "INFO" "APP_PreSet all successed, no need to check drbd disk..."
        return 0
    fi
    
    if [ "X${netWorkType}" == "XM" ];then
        log_echo "INFO" "netWorkType=${netWorkType} not need to get primary root ."
        if [ "X${root_password}" == "X" ];then
            secondary_root_pwd="${DVSecondary_pwd}"
        else
            secondary_root_pwd="${root_password}"
        fi
    else
        if [ "X${root_password}" == "X" ];then
            primary_root_pwd="${APP_Primary_pwd}"
        else
            primary_root_pwd="${root_password}"
        fi
        
        if [ "X${root_password}" == "X" ];then
            secondary_root_pwd="${APP_Secondary_pwd}"
        else
            secondary_root_pwd="${root_password}"
        fi
    fi
    
    local check_ret1=0  
    local check_ret2=0 
    if [ ${ret1} -ne 0 ];then
        if [ "X${netWorkType}" == "XM" ];then
            lsblk -pl ${drbd_disk} > ${PreSet_PATH}/drbd_disk1_info.txt
            pvcreate -y ${drbd_disk} > ${PreSet_PATH}/pvcreate_drbd.txt
            if [ $? -ne 0 ];then
                log_echo "ERROR" "execute command 'pvcreate -y ${drbd_disk}' failed, please check."
                check_ret1=1
            fi
            
            cat ${PreSet_PATH}/pvcreate_drbd.txt |grep -v "spawn" |grep "successfully" > /dev/null
            if [ $? -ne 0 ];then
                log_echo "ERROR" "DRBD_DISK ${drbd_disk} maybe have been used at ${drbd_primary_ip}, please check..."
                check_ret1=1
            else
                pvremove -f ${drbd_disk} > ${PreSet_PATH}/pvremove_drbd.txt
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Pvremove ${drbd_disk} failed at ${drbd_primary_ip}, please check..."
                    check_ret1=1
                fi
            fi
        else
            auto_smart_ssh ${primary_root_pwd} "root@${drbd_primary_ip} lsblk -pl ${drbd_disk}" > ${PreSet_PATH}/drbd_disk1_info.txt
            auto_smart_ssh ${primary_root_pwd} "root@${drbd_primary_ip} pvcreate -y ${drbd_disk}" > ${PreSet_PATH}/pvcreate_drbd.txt
            if [ $? -ne 0 ];then
                log_echo "ERROR" "ssh to ${drbd_primary_ip} execute command 'pvcreate -y ${drbd_disk}' failed, please check."
                check_ret1=1
            fi
            cat ${PreSet_PATH}/pvcreate_drbd.txt |grep -v "spawn" |grep "successfully" > /dev/null
            if [ $? -ne 0 ];then
                log_echo "ERROR" "DRBD_DISK ${drbd_disk} maybe have been used at ${drbd_primary_ip}, please check..."
                check_ret1=1
            else
                auto_smart_ssh ${primary_root_pwd} "root@${drbd_primary_ip} pvremove -f ${drbd_disk}" > ${PreSet_PATH}/pvremove_drbd.txt
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Pvremove ${drbd_disk} failed at ${drbd_primary_ip}, please check..."
                    check_ret1=1
                fi
            fi
        fi
    fi
    
    if [ ${ret2} -ne 0 ];then
        auto_smart_ssh ${secondary_root_pwd} "root@${drbd_secondary_ip} lsblk -pl ${drbd_disk}" > ${PreSet_PATH}/drbd_disk2_info.txt
        auto_smart_ssh ${secondary_root_pwd} "root@${drbd_secondary_ip} pvcreate -y ${drbd_disk}" > ${PreSet_PATH}/pvcreate_drbd2.txt
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ssh to ${drbd_secondary_ip} execute command 'pvcreate -y ${drbd_disk}' failed, please check."
            check_ret2=1
        fi
        cat ${PreSet_PATH}/pvcreate_drbd2.txt |grep -v "spawn" |grep "successfully" > /dev/null
        if [ $? -ne 0 ];then
            log_echo "ERROR" "DRBD_DISK ${drbd_disk} maybe have been used at ${drbd_secondary_ip}, please check..."
            check_ret2=1
        else
            auto_smart_ssh ${secondary_root_pwd} "root@${drbd_secondary_ip} pvremove -f ${drbd_disk}" > ${PreSet_PATH}/pvremove_drbd2.txt
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Pvremove ${drbd_disk} failed at ${drbd_secondary_ip}, please check..."
                check_ret2=1
            fi
        fi
    fi
    
    if [ ${ret1} -ne 0 -a ${ret2} -ne 0 ];then
        check_drbd_disk_size
    fi
    
    if [ "${check_ret1}" -ne 0 -o "${check_ret2}" -ne 0 ];then
        log_echo "ERROR" "Check DRBD_DISK at DV nodes failed, please check above..."
        exit 1
    fi
    
    log_echo "INFO" "Check drbd disk success..."
}

function check_drbd_disk_size()
{
    drbd_disk1_size=$(grep -w "^${drbd_disk}" ${PreSet_PATH}/drbd_disk1_info.txt|awk -F' ' '{print $4}')
    drbd_disk2_size=$(grep -w "^${drbd_disk}" ${PreSet_PATH}/drbd_disk2_info.txt|awk -F' ' '{print $4}')
    
    if [ "X${drbd_disk1_size}" != "X${drbd_disk2_size}" ];then
        log_echo "ERROR" "The drbd disk size of two app nodes are not the same, please check"
        exit 1
    fi
}

function check_Uniepservice()
{
    local query_times=0
    while [ ${query_times} -lt 6  ]
    do
        su - ossadm -c "sh ${INSTALL_PATH}/manager/tools/common/queryversioninfo.sh  -output /tmp/version.json" > /dev/null 2>&1
        if [ $? -eq 0 ];then
            log_echo "INFO" "Uniepservice is starting success."
             break
        elif [ ${query_times} -eq 6 ];then
            log_echo "ERROR" "Uniepservice start failed, please check."
            exit 1
        else
            log_echo "INFO" "Uniepservice is starting now."
        fi
        sleep 30s
        query_times=$((${query_times}+1))
    done
}

function check_uniep_service_by_param()
{
    local max_query_times="$1"
    ## unit: s
    local sleep_interval="$2"
    log_echo "INFO" "Check uniep service by parameters max_query_times=${max_query_times} and sleep_interval=${sleep_interval}."
    local query_times=0
    while [ ${query_times} -lt ${max_query_times}  ]
    do
        su - ossadm -c "sh ${INSTALL_PATH}/manager/tools/common/queryversioninfo.sh  -output /tmp/version.json" > /dev/null 2>&1
        if [ $? -eq 0 ];then
            log_echo "INFO" "Uniepservice is starting success."
            break
        elif [ ${query_times} -eq ${max_query_times} ];then
            log_echo "ERROR" "Uniepservice start failed, please check."
            exit 1
        else
            log_echo "INFO" "Uniepservice is starting now."
        fi
        sleep $sleep_interval
        query_times=$((${query_times}+1))
    done
}

function exp_product_info()
{
    local exp_path="$1"
    local do_type="$2"
    if [ -z "${exp_path}" ];then
        log_echo "ERROR" "The exp_path=${exp_path} is null. please check..."
        exit 1
    fi
    
    if [ -d ${exp_path} ];then
        exp_path=$(cd ${exp_path}; pwd)
        if [ "X${exp_path}" !=  "X/" ];then
            log_echo "INFO" "The exp_path=${exp_path}"
        fi
    fi
    
    mkdir -p ${exp_path}
    if [ "X$(whoami)" == "Xroot" ];then
        chown ossadm:ossgroup ${exp_path}
    fi
    chmod 777 ${exp_path}

    if [ "X${do_type}" == "XExtend" ];then
        ## check whether the repeated extend-preset
        local tmp_output_path="${exp_path}_tmp"
        rm -rf ${tmp_output_path:?}
        mkdir -p ${tmp_output_path}
        if [ "X$(whoami)" == "Xroot" ];then
            chown ossadm:ossgroup ${tmp_output_path}
        fi
        chmod 777 ${tmp_output_path}
        if [ "X$(whoami)" == "Xroot" ];then
            su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;sh ${install_path}/manager/tools/resmgr/queryproduct.sh -pn SOP -output ${tmp_output_path}"
        else
            source ${install_path}/manager/bin/engr_profile.sh;sh ${install_path}/manager/tools/resmgr/queryproduct.sh -pn SOP -output ${tmp_output_path}
        fi
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ossadm to Execute cmd:[ sh ${install_path}/manager/tools/resmgr/queryproduct.sh -pn SOP -output ${tmp_output_path} ] failed.of uniep."
            exit 1
        fi
        local ip_list;
        if [ "X${IPType}" == "X0" ];then
            ip_list=$(grep "IPV4_List=" ${PreSet_PATH}/DV_Config_Extend.config |awk -F '=' '{print $2}' |tr "\r\n" "," |sed 's/,\+/,/g' |sed -E "s/^,|,$//g")
        else
            ip_list=$(grep "MGR_IPV6_List=" ${PreSet_PATH}/DV_Config_Extend.config |awk -F '=' '{print $2}' |tr "\r\n" "," |sed 's/,\+/,/g' |sed -E "s/^,|,$//g")
        fi

        touch ${PreSet_PATH}/tools/getInfo.log
        chown ossadm:ossgroup ${PreSet_PATH}/tools/getInfo.log
        local is_repeated_extend_preset=$(su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;python ${PreSet_PATH}/tools/get_install_info.py \"${tmp_output_path}/nodes_SOP.json\" \"is_repeated_extend_preset\" \"${ip_list}\" ")

        if [ "${is_repeated_extend_preset}" == "True" ];then
            log_echo "ERROR" "The extend node information has been registered, do not extend repeat, please logged in to the CloudSOP-UniEP web client to delete the extended node and execute [ DV_Extend_Cleanup.sh ] first."
            exit 1
        fi
        ## check repeated extend-preset pass, Copy files to the correct directory.
        rm -f "${exp_path:?}/*"
        cp -rpf "${tmp_output_path}"/* "${exp_path}"
        rm -rf "${exp_path}_tmp"
    else
        rm -f "${exp_path:?}/*"
        if [ "X$(whoami)" == "Xroot" ];then
            su - ossadm -c "source ${install_path}/manager/bin/engr_profile.sh;sh ${install_path}/manager/tools/resmgr/queryproduct.sh -pn SOP -output ${exp_path}"
        else
            source ${install_path}/manager/bin/engr_profile.sh;sh ${install_path}/manager/tools/resmgr/queryproduct.sh -pn SOP -output ${exp_path}
        fi
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ossadm to Execute cmd:[ sh ${install_path}/manager/tools/resmgr/queryproduct.sh -pn SOP -output ${exp_path} ] failed.of uniep."
            exit 1
        fi
    fi

    if [ ! -f ${exp_path}/nodes*.json -o ! -f ${exp_path}/product*.json ];then
        log_echo "ERROR" "Export product info by uniep failed,of ${tmp_output_path} please check..."
        exit 1
    fi

    if [ "X${is_SD_upgrade}" == "XYes" ];then
        cp ${exp_path}/product*.json /home/<USER>/export_product.json
        chmod 600 /home/<USER>/export_product.json
        if [ "X$(whoami)" == "Xroot" ];then
            chown ossadm:ossgroup /home/<USER>/export_product.json
        fi
    fi
}

function Get_netType()
{
    local do_type="$1"
    ps -ef | grep "uniepservice-0-0\|uniepliteservice-0-0" | grep -v grep > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Can't find Uiep -Dprocname=uniepservice-0-0 process, We need run this at UniEp node, please check ..."
        exit 1
    else
        install_path=$(ps -ef | grep "uniepservice-0-0\|uniepliteservice-0-0" |grep -v grep |awk -F'/envs/Product-' '{print $1}' |awk '{print $NF}')

    fi

    if [ ! -f "${install_path}/manager/bin/engr_profile.sh" ];then
        log_echo "ERROR" "${install_path}/manager/bin/engr_profile.sh is not exist, please check..."
        exit 1
    fi

    exp_product_path=""
    if [ "X${do_type}" == "XExtend" ];then
        exp_product_path="${PreSet_PATH}/expInfo_extend"
    elif [ "X${do_type}" == "XExtend_cleanup" ];then
        exp_product_path="${PreSet_PATH}/expInfo_extend_cleanup"
    elif [ "X${do_type}" == "XUpgrade" ];then
        exp_product_path="${PreSet_PATH}/tools/expInfo"
    else
        log_echo "ERROR" "the do_type=${do_type} not support."
        exit 1
    fi

    if [ "X${do_type}" == "XUpgrade" -a ! -f ${exp_product_path}/nodes*.json -a ! -f ${exp_product_path}/product*.json ];then
        exp_product_info "${exp_product_path}" "${do_type}"
    fi

    if [ "X${do_type}" == "XExtend" -o "X${do_type}" == "XExtend_cleanup" ];then
        exp_product_info "${exp_product_path}" "${do_type}"
    fi

    ## "NetworkType":"MergeCluster",
    cat ${exp_product_path}/product*.json | grep -w "\"NetworkType\":[[:space:]]*\"MiniCluster\"" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "This is MergeCluster network..."
        netWorkType="M"
        return 0
    fi

    cat ${exp_product_path}/nodes*.json | grep "SOP-OM-Global" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        cat ${exp_product_path}/nodes*.json | grep "OMMHA" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            if [ -n "`grep \"ALL_IN_ONE_NODE\" ${exp_product_path}/nodes*.json`" ];then
                log_echo "INFO" "This is AllInOne network..."
                netWorkType="O"
            else
                log_echo "INFO" "This is Test network..."
                netWorkType="T"
            fi
        else
            if [ -n "`grep \"SMClusterNode\" ${exp_product_path}/nodes*.json`" ];then
                log_echo "INFO" "This is LargeCapacity network..."
                netWorkType="L"
            else
                log_echo "INFO" "This is Cluster network..."
                netWorkType="C"
            fi
        fi
    else
        log_echo "ERROR" "Can't find 'SOP-OM-Global' in ${exp_product_path}/nodes*.json, please check..."
        exit 1
    fi
}

function modify_extend_rollback_product_json()
{
    log_echo "INFO" "start modify_extend_rollback_product_json ..."
    local product_json="$1"
    local cfg_file="$2"
    
    if [ -z "${product_json}" -o -z "${cfg_file}" ];then
        log_echo "ERROR" "product_json=${product_json} or cfg_file=${cfg_file} has null. please check..."
        exit 1
    fi
    
    if [ ! -f ${product_json} ];then
        log_echo "ERROR" "The product_json=${product_json} does not exist. please check..."
        exit 1
    fi
    
    if [ ! -f ${cfg_file} ];then
        log_echo "ERROR" "The cfg_file=${cfg_file} does not exist. please check..."
        exit 1
    fi
    log_echo "INFO" "The product_json=${product_json}, cfg_file=${cfg_file}"
    
    ## 排除不需要获取的参数。
    local exclude_list="^DB_IPV6=\|^APP_Secondarynode_IPV6=\|^DB_Primarynode_IPV6=\|^DB_Secondarynode_IPV6=\|^LVS_IPV4_NETMASK=\|^LVS_IPV4_NIC=\|^LVS_IPV6_NETMASK=\|^LVS_IPV6_NIC="
    local extend_ip_key="IPV4\|IPV6"
    local tmp_result=""
    local extend_all_ip=$(cat ${cfg_file}|grep -v "^[[:blank:]]*#" |grep -v "${exclude_list}"|grep "${extend_ip_key}"|awk -F'=' '{print $2}'|sed 's/,/ /g')
    local python_script="${PreSet_PATH}/Common/modify_extend_rollback_product_json.py"
    for extend_ip in ${extend_all_ip};
    do
        [ -z "${extend_ip}" ] && continue
        log_echo "INFO" "The extend_ip=${extend_ip} modify product_json=${product_json}."
        [ ! -f ${PreSet_PATH}/UniEp/python/bin/python ] && unzip -q ${PreSet_PATH}/UniEp/python*.zip -d ${PreSet_PATH}/UniEp/python
        tmp_result=$(${PreSet_PATH}/UniEp/python/bin/python ${python_script} "${product_json}" "${extend_ip}")
        execute_ret=$?
        if [ "${tmp_result}" == "ERROR" -o ${execute_ret} -ne 0 ];then
            log_echo "ERROR" "The extend_ip=${extend_ip} modify product_json failed.check log: ${PreSet_PATH}/Common/rollback_product_json.log"
            exit 1
        fi
    done
    
    log_echo "INFO" "The product_json=${product_json} modify finished."
}

function set_osuser_passwd_for_uniep()
{
    ossuser_pwd_encrypt=$(cat ${PreSet_PATH}/Common/pwfile.json | sed 's/}/\n/g' | grep -w ${i2k_user_name} | awk -F : '{print $3}')
    ossuser_pwd_encrypt=${ossuser_pwd_encrypt#*\"}
    ossuser_pwd_encrypt=${ossuser_pwd_encrypt%%\"*}
    decrypt_password ${ossuser_pwd_encrypt}
    ossuser_pwd=${decryptPasswd}
    set_user_passwd "${i2k_user_name}" "${ossuser_pwd}"

    sopuser_pwd_encrypt=$(cat ${PreSet_PATH}/Common/pwfile.json | sed 's/}/\n/g' | grep -w ${sop_user_name} | awk -F : '{print $3}')
    sopuser_pwd_encrypt=${sopuser_pwd_encrypt#*\"}
    sopuser_pwd_encrypt=${sopuser_pwd_encrypt%%\"*}
    decrypt_password ${sopuser_pwd_encrypt}
    sopuser_pwd=${decryptPasswd}
    set_user_passwd "${sop_user_name}" "${sopuser_pwd}"

    set_user_passwd "${system_sftp_user}" "${sftpossuser_pwd}"
}

function DV_BatchPreSet_Check_Before()
{
    local ssh_ip="$1"
    local user_name="$2"
    local user_pwd="$3"
    local network_type="$4"
    local node_role="$5"

    log_echo "INFO" "The DV_BatchPreSet_Check_Before start..."
    if [ -z "${ssh_ip}" -o -z "${user_name}" -o -z "${network_type}" -o -z "${node_role}" ];then
        log_echo "ERROR" "The ssh_ip=${ssh_ip} or user_name=${user_name} or network_type=${network_type} or node_role=${node_role} is null."
        exit 1
    fi

    if [ ! -z "${root_password}" ];then
        log_echo "INFO" "root_password is not null.modify user_pwd to root_password."
        user_pwd="${root_password}"
    fi

    if [ -z "${user_pwd}" ];then
        cat ${action_tag} | grep "${ssh_ip} : success" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "The ssh_ip=${ssh_ip} and node_role=${node_role} of user_pwd is null."
            exit 1
        fi
        log_echo "INFO" "The ssh_ip=${ssh_ip} has been executed.not need to check.skip it."
        return 0
    fi

    ## Check whether the root password is correct.
    if [ ! -z "${root_password}" ];then
        log_echo "INFO" "Check whether the root password is correct."
        check_ssh "${ssh_ip}" "${user_name}" "${user_pwd}"
    fi

    local scp_ip="${ssh_ip}"
    local isIpv6=$(echo ${scp_ip} |grep ":" |grep -v "]")
    if [ ! -z "${isIpv6}" ];then
        scp_ip="\[${scp_ip}\]"
    fi

    ## custom tmp path need tp mkdir the path
    if [ "${TMP_PATH}" != "/tmp" ]; then
        auto_smart_ssh ${user_pwd} "${user_name}@${ssh_ip} mkdir -p ${TMP_PATH} && chmod 1777 ${TMP_PATH}"
    fi

    ## rm tmp file.
    auto_smart_ssh ${user_pwd} "${user_name}@${ssh_ip} rm -rf ${TMP_PATH}/Common_node_check.sh 2>/dev/null"
    auto_smart_ssh ${user_pwd} "${user_name}@${ssh_ip} rm -rf ${TMP_PATH}/checkNodePermissions.properties 2>/dev/null"
    ## scp tmp file.
    auto_scp ${user_pwd} ${UTILS_PATH}/Common_node_check.sh "${user_name}@${scp_ip}:${TMP_PATH}"
    auto_scp ${user_pwd} ${UTILS_PATH}/checkNodePermissions.properties "${user_name}@${scp_ip}:${TMP_PATH}"
    ## chmod tmp file.
    auto_smart_ssh ${user_pwd} "${user_name}@${ssh_ip} chmod 750 ${TMP_PATH}/Common_node_check.sh"
    auto_smart_ssh ${user_pwd} "${user_name}@${ssh_ip} chmod 640 ${TMP_PATH}/checkNodePermissions.properties"
    ## sh tmp script.
    echo $node_role | grep -E "^Host" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        auto_smart_ssh ${user_pwd} "${user_name}@${ssh_ip} sh ${TMP_PATH}/Common_node_check.sh \"${network_type}\" \"${node_role}\" \"install\" \"check_time\" " > ${PreSet_PATH}/${network_type}_check_result/${ssh_ip}.check_result
    else
        auto_smart_ssh ${user_pwd} "${user_name}@${ssh_ip} sh ${TMP_PATH}/Common_node_check.sh \"${network_type}\" \"${node_role}\" \"install\" \"check_time,check_permissions,check_disk_byid\" \"$6\" " > ${PreSet_PATH}/${network_type}_check_result/${ssh_ip}.check_result
    fi
    if [ $? -ne 0 ];then
        log_echo "INFO" "Execute  sh ${TMP_PATH}/Common_node_check.sh failed.of node: ${ssh_ip}"
        exit 1
    fi
    ## rm tmp file.
    auto_smart_ssh ${user_pwd} "${user_name}@${ssh_ip} rm -rf ${TMP_PATH}/Common_node_check.sh"
    auto_smart_ssh ${user_pwd} "${user_name}@${ssh_ip} rm -rf ${TMP_PATH}/checkNodePermissions.properties"

    log_echo "INFO" "The DV_BatchPreSet_Check_Before finished."
}

function monitor_time_out()
{
    local node_ip="$1"
    local time_out="$2"
    local monitor_pid="$3"
    if [ -z "${node_ip}" -o -z "${time_out}" -o -z "${monitor_pid}" ];then
        log_echo "INFO" "The node_ip=${node_ip} or time_out=${time_out} or monitor_pid=${monitor_pid} has null."
        return 0
    fi

    sleep ${time_out}
    log_echo "ERROR" "The monitor ${node_ip} pid ${monitor_pid} time out(${time_out})."
    touch ${PreSet_PATH}/monitor_time_out.tag
    kill -9 ${monitor_pid} >/dev/null
    return 0
}

function check_task_status()
{
    local result_path="$1"
    local log_path="$2"
    ## unit Seconds
    local timeOut=$3
    if [ -z "${timeOut}" ];then
        ## 5 minutes = 5*60 = 300 Seconds
        ## 2024/7/19 有的环境K8S特别慢，安装要6-7分钟，timeOut参数要调整为10min 10*60=600
        timeOut=600
    fi
    
    local isNumber=$(echo "${timeOut}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        ## 5 minutes = 5*60 = 300 Seconds
        ## 2024/7/19 有的环境K8S特别慢，安装要6-7分钟，timeOut参数要调整为10min 10*60=600
        timeOut=600
    fi
    
    log_echo "INFO" "The check_task_status start."
    if [ -z "${result_path}" ];then
        log_echo "INFO" "The result_path=${result_path} is null."
        return 0
    fi

    if [ ! -f "${result_path}" ];then
        log_echo "INFO" "The result_path=${result_path} is not exists."
        return 0
    fi

    rm -rf ${PreSet_PATH}/monitor_time_out.tag
    
    local tmp_ip=""
    local tmp_pid=""
    local tmp_pid_ret=0
    error_ip_list=""
    while read line;do
        if [ -z "${line}" ];then
            continue
        fi

        tmp_ip=$(echo "${line}" |awk -F'=' '{print $1}'|sed "s#[[:blank:]|[:cntrl:]]##g")
        tmp_pid=$(echo "${line}" |awk -F'=' '{print $2}'|sed "s#[[:blank:]|[:cntrl:]]##g")

        log_echo "INFO" "The tmp_ip=${tmp_ip} and tmp_pid=${tmp_pid} star wait it."
        ## ip timeout pid
        monitor_time_out "${tmp_ip}" "${timeOut}" "${tmp_pid}" &
        monitor_time_out_pid=$!
        wait ${tmp_pid}
        if [ $? -ne 0 ];then
            tmp_pid_ret=$((tmp_pid_ret+1))
            error_ip_list="${error_ip_list} ${tmp_ip}"
        fi
        
        kill ${monitor_time_out_pid} >/dev/null
        log_echo "INFO" "The tmp_ip=${tmp_ip} and tmp_pid=${tmp_pid} and tmp_pid_ret=${tmp_pid_ret} wait finished."
    done < ${result_path}

    if [ ! -z "${log_path}" ] && [ -f ${log_path} ] && [ -f ${PreSet_PATH}/monitor_time_out.tag ];then
        return 1
    fi

    if [ ! -z "${log_path}" ] && [ -f ${log_path} ] && [ ${tmp_pid_ret} -ne 0 ];then
        return 2
    fi
    
    if [ -f ${PreSet_PATH}/monitor_time_out.tag ];then
        log_echo "ERROR" "The check_task_status monitor has pid time out.please check log: $LOG_FILE"
        exit 1
    fi

    if [ ${tmp_pid_ret} -ne 0 ];then
        log_echo "ERROR" "The check_task_status monitor ${error_ip_list} execute failed.Look up for detailed error information."
        exit 1
    fi

    log_echo "INFO" "The check_task_status finish."
}

function checkOsTime()
{
    local uniep_now_time=$(date +"%s")
    local result_path="$1"
    if [ -z "${result_path}" ];then
        log_echo "INFO" "The result_path=${result_path} is null."
        return 0
    fi

    if [ ! -d "${result_path}" ];then
        log_echo "INFO" "The result_path=${result_path} is not exists."
        return 0
    fi

    ## ${PreSet_PATH}/{xxx}_check_result/{ip}.check_result
    local check_result_list=$(ls ${result_path} |grep -w ".*.check_result$")
    if [ -z "${check_result_list}" ];then
        log_echo "INFO" "The check_result_list=${check_result_list} is null."
        return 0
    fi

    ## 900 Seconds
    local times=900
    local tmpIp=""
    local tmpOsTime=""
    local check_result=0
    local hasError=0
    for file in ${check_result_list};do
        tmpIp=$(echo "${file}" |awk -F'.check_result' '{print $1}')
        tmpOsTime=$(grep  "OsTime=" ${result_path}/${file} |awk -F'OsTime=' '{print $2}')
        if [ -z "${tmpIp}" -o -z "${tmpOsTime}" ];then
            log_echo "INFO" "The tmpIp=${tmpIp} or tmpOsTime=${tmpOsTime} is null."
            continue
        fi
        tmpOsTime=$(echo "${tmpOsTime}" |sed "s#[[:blank:]|[:cntrl:]]##g")
        isNum "${tmpOsTime}" || continue

        log_echo "INFO" "The tmpIp=${tmpIp} and tmpOsTime=${tmpOsTime}."
        check_result=$((${tmpOsTime} - ${uniep_now_time}))
        if [ ${check_result} -ge ${times} ];then
            log_echo "ERROR" "The time of node ${tmpIp} is more than ${times} seconds bigger than that of Uniep node."
            hasError=1
            continue
        fi

        check_result=$((${uniep_now_time} - ${tmpOsTime}))
        if [ ${check_result} -ge ${times} ];then
            log_echo "ERROR" "The time of node ${tmpIp} is more than ${times} seconds less than that of the Uniep node."
            hasError=1
            continue
        fi

    done

    if [ ${hasError} -eq 1 ];then
        log_echo "ERROR" "The preceding check result shows that the time has nodes is different from that of the uniep node and exceeds ${times} seconds. For details about the error information, see the preceding error information. Rectify the fault before continuing."
        exit 1
    fi
    log_echo "INFO" "The checkOsTime finish."
}


function check_uniep_disk_byid()
{
    local disk="$1"
    if [ -z "${disk}" ];then
        echo "disk is Null.of 1"
        return 2
    fi

    if [ "X${disk}" == "XDB_AUTO_DISK_MOUNT_IS_FALSE" ];then
        disk=$(df -Ph /opt/zenith |sed "1,1d" |awk '{print $1}')
    else
        disk_uuid=$(blkid ${disk} |grep -w "UUID")
        if [ -z "${disk_uuid}" ];then
            echo "y"|mkfs.ext4 ${disk} >/dev/null 2>&1
        fi
    fi

    if [ -z "${disk}" ];then
        echo "disk is Null.of 2"
        return 2
    fi

    local disk_id_list=$(blkid ${disk} |awk -F'UUID=' '{print $2}'|awk '{print $1}'|head -1|awk -F'"|"' '{print $2}'|sed "s/[[:blank:]]//g")
    if [ -z "${disk_id_list}" ];then
        echo "The /dev/disk not found disk id.of ${disk}"
        return 2
    fi

    echo "${disk}=${disk_id_list}"
    return 0
}

function check_disk_id()
{
    local id_list1="$1"
    local id_list2="$2"

    if [ -z "${id_list1}" -o -z "${id_list2}" ];then
        log_echo "INFO" "The id_list1=${id_list1} or id_list2=${id_list2} has null.not need check it."
        return 0
    fi

    log_echo "INFO" "start check_disk_id..."
    ## disk_id_list="${disk_id_list},/dev/disk/${tmp_path}/${disk_name}=${UUID}"
    for diskid1 in $(echo "${id_list1}"|sed "s/,/ /g");do
        tmp_idkey1=$(echo "${diskid1}"|awk -F'=' '{print $1}')
        tmp_idvalue1=$(echo "${diskid1}"|awk -F'=' '{print $2}')
        if [ -z "${tmp_idkey1}" ];then
            continue
        fi

        for diskid2 in $(echo "${id_list2}"|sed "s/,/ /g");do
            tmp_idkey2=$(echo "${diskid2}"|awk -F'=' '{print $1}')
            tmp_idvalue2=$(echo "${diskid2}"|awk -F'=' '{print $2}')
            if [ "${tmp_idkey1}" == "${tmp_idkey2}" ];then
                log_echo "INFO" "The tmp_idkey1=${tmp_idkey1} tmp_idvalue1=${tmp_idvalue1} and tmp_idkey2=${tmp_idkey2} tmp_idvalue2=${tmp_idvalue2}"
                if [ "${tmp_idvalue1}" == "${tmp_idvalue2}" ];then
                    log_echo "ERROR" "The ${tmp_idkey1}=${tmp_idvalue1} and ${tmp_idkey2}=${tmp_idvalue2} is same."
                    return 1
                fi
                break
            fi
        done
    done

    log_echo "INFO" "end check_disk_id."
    return 0
}

function is_share_disk()
{
    local netWorkType="$1"
    local result_path="$2"
    if [ -z "${result_path}" ];then
        log_echo "INFO" "The result_path=${result_path} is null."
        return 0
    fi

    if [ -z "${netWorkType}" ];then
        log_echo "INFO" "The netWorkType=${netWorkType} is null."
        return 0
    fi

    if [ ! -d "${result_path}" ];then
        log_echo "INFO" "The result_path=${result_path} is not exists."
        return 0
    fi

    log_echo "INFO" "The netWorkType=${netWorkType}.start is_share_disk check..."

    ## C  and L  netWorkType
    ## ${PreSet_PATH}/{xxx}_check_result/{ip}.check_result
    local check_result_list=$(ls ${result_path} |grep -w ".*.check_result$")
    if [ -z "${check_result_list}" ];then
        log_echo "INFO" "The check_result_list=${check_result_list} is null."
        return 0
    fi

    local tmpIp=""
    local hasError=0
    for file in ${check_result_list};do
        tmpIp=$(echo "${file}" |awk -F'.check_result' '{print $1}')
        if [ -z "${tmpIp}" ];then
            log_echo "INFO" "The tmpIp=${tmpIp} is null."
            continue
        fi

        if [ "${netWorkType}" == "M" ];then
            ## M_App_Secondary_UUID_RET  M_DB_Secondary_UUID_RET
            uuid_ret_app=$(grep "M_App_Secondary_UUID_RET=" ${result_path}/${file} |awk -F'_UUID_RET=' '{print $2}'|sed "s#[[:blank:]|[:cntrl:]]##g")
            uuid_ret_db=$(grep "M_DB_Secondary_UUID_RET=" ${result_path}/${file} |awk -F'_UUID_RET=' '{print $2}'|sed "s#[[:blank:]|[:cntrl:]]##g")

            if [ -z "${uuid_ret_app}" -o -z "${uuid_ret_db}" ];then
                log_echo "INFO" "The tmpIp=${tmpIp} of uuid_ret_app=${uuid_ret_app} or uuid_ret_db=${uuid_ret_db} is null."
                continue
            fi

            if [ ${uuid_ret_app} -eq 2 -o ${uuid_ret_db} -eq 2 ];then
                log_echo "INFO" "The tmpIp=${tmpIp} of uuid_ret_app=${uuid_ret_app} or uuid_ret_db=${uuid_ret_db} has ret is 2.Is disk is Null,not need to check it."
                continue
            fi

            uuid_ret=$((${uuid_ret_app} + ${uuid_ret_db}))
            ## M_App_Secondary_UUID  M_DB_Secondary_UUID
            uuid_ret_key_app="M_App_Secondary_UUID"
            uuid_ret_key_db="M_DB_Secondary_UUID"

            uuid_ret_mes_app=$(grep "${uuid_ret_key_app}=" ${result_path}/${file} |awk -F'_UUID=' '{print $2}'|sed "s#[[:blank:]|[:cntrl:]]##g")
            uuid_ret_mes_db=$(grep "${uuid_ret_key_db}=" ${result_path}/${file} |awk -F'_UUID=' '{print $2}'|sed "s#[[:blank:]|[:cntrl:]]##g")

            log_echo "INFO" "The tmpIp=${tmpIp} and uuid_ret=${uuid_ret}."
            if [ ${uuid_ret} -ne 0 ];then
                log_echo "ERROR" "The node ${tmpIp} check uuid failed.message is: uuid_ret_mes_app=${uuid_ret_mes_app}.of uuid_ret_key_app=${uuid_ret_key_app} and uuid_ret_mes_db=${uuid_ret_mes_db}.of uuid_ret_key_db=${uuid_ret_key_db}"
                hasError=1
                continue
            fi

            app_secondary_uuid=${uuid_ret_mes_app}
            db_secondary_uuid=${uuid_ret_mes_db}
            continue
        else
            uuid_ret=$(grep "_UUID_RET=" ${result_path}/${file} |awk -F'_UUID_RET=' '{print $2}'|sed "s#[[:blank:]|[:cntrl:]]##g")
            if [ -z "${uuid_ret}" ];then
                log_echo "INFO" "The tmpIp=${tmpIp} of uuid_ret=${uuid_ret} is null."
                continue
            fi

            if [ ${uuid_ret} -eq 2 ];then
                log_echo "INFO" "The tmpIp=${tmpIp} of uuid_ret=${uuid_ret} has ret is 2.Is disk is Null,not need to check it."
                continue
            fi

            uuid_ret_key=$(grep "_UUID=" ${result_path}/${file} |awk -F'_UUID=' '{print $1}'|sed "s#[[:blank:]|[:cntrl:]]##g")
            uuid_ret_mes=$(grep "_UUID=" ${result_path}/${file} |awk -F'_UUID=' '{print $2}'|sed "s#[[:blank:]|[:cntrl:]]##g")
            log_echo "INFO" "The tmpIp=${tmpIp} and uuid_ret=${uuid_ret}."
            if [ ${uuid_ret} -ne 0 ];then
                log_echo "ERROR" "The node ${tmpIp} check uuid failed.message is: ${uuid_ret_mes}.of uuid_ret_key=${uuid_ret_key}"
                hasError=1
                continue
            fi
        fi

        ## ${network_type}_${node_role}_UUID=${UUID}  network_type is in "C L M" . node_role is in "App_Primary App_Secondary DB_Primary DB_Secondary"
        if [ "${uuid_ret_key}_UUID" == "${netWorkType}_App_Primary_UUID" ];then
            app_primary_uuid=${uuid_ret_mes}
        elif [ "${uuid_ret_key}_UUID" == "${netWorkType}_App_Secondary_UUID" ];then
            app_secondary_uuid=${uuid_ret_mes}
        elif [ "${uuid_ret_key}_UUID" == "${netWorkType}_DB_Primary_UUID" ];then
            db_primary_uuid=${uuid_ret_mes}
        elif [ "${uuid_ret_key}_UUID" == "${netWorkType}_DB_Secondary_UUID" ];then
            db_secondary_uuid=${uuid_ret_mes}
        fi
    done

    if [ ${hasError} -eq 1 ];then
        log_echo "ERROR" "Failed to check whether the application and database node disks share disks.For details about the error information, see the preceding error information. Rectify the fault before continuing."
        exit 1
    fi

    if [ "${netWorkType}" == "M" ];then
        local app_primary_uuid=$(check_uniep_disk_byid "${HA_DISK}")
        local app_primary_uuid_ret=$?

        local db_primary_uuid=""
        if [ "X${AUTO_DISK_MOUNT}" == "XTRUE" ];then
            db_primary_uuid=$(check_uniep_disk_byid "${DBNode_DISK}")
        else
            db_primary_uuid=$(check_uniep_disk_byid "DB_AUTO_DISK_MOUNT_IS_FALSE")
        fi
        local db_primary_uuid_ret=$?

        if [ ${app_primary_uuid_ret} -eq 2 -o ${db_primary_uuid_ret} -eq 2 ];then
            log_echo "INFO" "The node is M_primary.The app_primary_uuid_ret=${app_primary_uuid_ret} or db_primary_uuid_ret=${db_primary_uuid_ret} has eq 2.The app_primary_uuid=${app_primary_uuid} and  db_primary_uuid=${db_primary_uuid} . not get disk id."
            return 0
        fi

        if [ ${app_primary_uuid_ret} -eq 1 -o ${db_primary_uuid_ret} -eq 1 ];then
            log_echo "ERROR" "The node is M_primary.The app_primary_uuid=${app_primary_uuid} and db_primary_uuid=${db_primary_uuid}.please check."
            exit 1
        fi
    fi

    local check_uuid_ret=0
    check_disk_id "${app_primary_uuid}" "${app_secondary_uuid}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "app_primary_uuid=${app_primary_uuid} and app_secondary_uuid=${app_secondary_uuid} has same disk id,They are shared disks.please check."
        check_uuid_ret=1
    fi

    check_disk_id "${db_primary_uuid}" "${db_secondary_uuid}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "db_primary_uuid=${db_primary_uuid} and db_secondary_uuid=${db_secondary_uuid} has same disk id,They are shared disks.please check."
        check_uuid_ret=1
    fi

    [ ${check_uuid_ret} -ne 0 ] && exit 1
    log_echo "INFO" "The app_primary_uuid=${app_primary_uuid} and app_secondary_uuid=${app_secondary_uuid}."
    log_echo "INFO" "The db_primary_uuid=${db_primary_uuid} and db_secondary_uuid=${db_secondary_uuid}."
    log_echo "INFO" "The is_share_disk finish."
}

function checkUniEpNodePermissions()
{
    local uniep_ip="$1"
    local network_type="$2"
    if [ -z "${uniep_ip}" -o -z "${network_type}" ];then
        log_echo "INFO" "The uniep_ip=${uniep_ip} or network_type=${network_type} is null."
        return 0
    fi

    ## uniep node check.
    chmod 750 ${UTILS_PATH}/Common_node_check.sh
    chmod 640 ${UTILS_PATH}/checkNodePermissions.properties
    sh ${UTILS_PATH}/Common_node_check.sh "${network_type}" "UniEP" "install" "check_permissions" > ${PreSet_PATH}/${network_type}_check_result/${uniep_ip}.check_result
    if [ $? -ne 0 ];then
        log_echo "ERROR" "sh ${UTILS_PATH}/Common_node_check.sh \"${network_type}\" \"UniEP\" \"install\" \"check_permissions\" failed.in uniep local node."
        exit 1
    fi
    return 0
}

function checkNodePermissions()
{
    local result_path="$1"
    if [ -z "${result_path}" ];then
        log_echo "INFO" "The result_path=${result_path} is null."
        return 0
    fi

    if [ ! -d "${result_path}" ];then
        log_echo "INFO" "The result_path=${result_path} is not exists."
        return 0
    fi

    ## ${PreSet_PATH}/{xxx}_check_result/{xxx}.check_result
    local check_result_list=$(ls ${result_path} |grep -w ".*.check_result$")
    if [ -z "${check_result_list}" ];then
        log_echo "INFO" "The check_result_list=${check_result_list} is null."
        return 0
    fi

    local tmpIp=""
    local check_result=""
    local hasError=0
    for file in ${check_result_list};do
        tmpIp=$(echo "${file}" |awk -F'.check_result' '{print $1}')
        check_result=$(grep "check_permissions=" ${result_path}/${file} |awk -F'check_permissions=' '{print $2}'|sed "s#[[:blank:]|[:cntrl:]]##g")
        if [ -z "${tmpIp}" -o -z "${check_result}" ];then
            log_echo "INFO" "The tmpIp=${tmpIp} or check_result=${check_result} is null. not need to check.skip it."
            continue
        fi

        for result in ${check_result};do
            error_result=$(echo "${result}"|awk -F',' '{print $1}')
            true_result=$(echo "${result}"|awk -F',' '{print $2}')
            log_echo "ERROR" "The node ${tmpIp} permission is incorrect [${error_result}]. The correct permission is [${true_result}]."
            hasError=1
        done
    done

    if [ ${hasError} -eq 1 ];then
        log_echo "ERROR" "The preceding check result shows that the node permission is incorrect. For details about the error information, see the preceding error information. Please troubleshoot the problem before continuing."
        exit 1
    fi
    log_echo "INFO" "The checkNodePermissions finish."
}

function check_preset()
{
    ## ${PreSet_PATH}
    ## The old version preset path is not deleted. and new version preset package is decompressed to old version preset path. --As a result, packages of different versions exist, which will cause preconfigured exceptions. Add a check.
    #a. UniEp/UniEPMgr*.zip has more pkg.
    #b. CloudSop/  osconfig-*.zip and sudoconfig_*.tar.gz has more pkg.
    local check_ret=0
    local check_uniep_pkg=$(ls ${PreSet_PATH}/UniEp/UniEPMgr*.zip|wc -l)
    if [ ${check_uniep_pkg} -ne 1 ];then
        log_echo "ERROR" "${PreSet_PATH}/UniEp/UniEPMgr*.zip pkg is not one.please check."
        check_ret=1
    fi

    local check_osconfig_pkg=$(ls ${PreSet_PATH}/CloudSop/osconfig-*.zip|wc -l)
    if [ ${check_osconfig_pkg} -ne 1 ];then
        log_echo "ERROR" "${PreSet_PATH}/CloudSop/osconfig-*.zip pkg is not one.please check."
        check_ret=1
    fi

    local check_sudoconfig_pkg=$(ls ${PreSet_PATH}/CloudSop/sudoconfig_*.tar.gz|wc -l)
    if [ ${check_sudoconfig_pkg} -ne 1 ];then
        log_echo "ERROR" "${PreSet_PATH}/CloudSop/sudoconfig_*.tar.gz pkg is not one.please check."
        check_ret=1
    fi

    if [ ! -z "${source_file}" -a -f "${source_file}" ];then
        grep -w "EXTEND_98G_DISK" ${source_file} >> $LOG_FILE 2>&1
        if [ $? -eq 0 ];then
            log_echo "ERROR" "The ${source_file} is old config.has old config parameter 'EXTEND_98G_DISK'.please check."
            check_ret=1
        fi
    fi

    if [ ${check_ret} -eq 0 ];then
        log_echo "INFO" "The check_preset finish."
    else
        if ( ! is_empty_param ${UNIEP_NODE_IP} );then
            log_echo "ERROR" "Check UniEP and CloudSOP package number failed on the ${UNIEP_NODE_IP}"
        else
            log_echo "ERROR" "Check UniEP and CloudSOP package number failed on the UniEP Node"
        fi

        exit 1
    fi
}

function create_sign_key()
{
    local type="$1"

    cd ${PreSet_PATH}/Common/sign_dv/

    log_echo "INFO" "Begin to create ${type} sign key."

    local sign_pwd=$(tr -dc "\!\@\#\%\^\&\*\(\)\_\+\-\=\{\}\[\]\|\>\<\,\?\.\:\;A-Za-z0-9" </dev/random| head -c 16)

    openssl genpkey -algorithm RSA -outform PEM -out "${type}_data_sign.pem" -pkeyopt rsa_keygen_bits:4096 -pkeyopt rsa_keygen_pubexp:65537 -aes-256-cbc -pass stdin <<EOF
${sign_pwd}
EOF

    openssl rsa -in ${type}_data_sign.pem -pubout -out ${type}_data_verify.pem -passin stdin -passout stdin <<EOF
${sign_pwd}
${sign_pwd}
EOF

    if [ ! -f "/opt/oss/manager/etc/cipher/cloudsop_cipher_mode.flag" -a  "X${is_upgrade}" == "XYes" ];then
        encrypt_by_uniep "${sign_pwd}" "SOP"
    else
        encrypt_by_uniep "${sign_pwd}" ""
    fi
    local encode_sign_pwd="${pwd_return}"

    echo "passphrase=${encode_sign_pwd}" > ${type}_data_sign.cfg
    echo "key_id=$(sha256sum ${type}_data_verify.pem  | cut -b '1-8')" >> ${type}_data_sign.cfg

    [ -f "${PreSet_PATH}/Common/passwords.properties" ] && add_config "${type}_sign_pwd" "${encode_sign_pwd}" "${PreSet_PATH}/Common/passwords.properties"

    log_echo "INFO" "Create uniagent ${type} key end."
}

function create_server_certificate()
{
    local cert_name="$1"

    local subject="$2"

    local cert_type="$3"

    local cert_middle_name=$(echo $cert_name | awk -F '.' '{print $1}')

    log_echo "INFO" "Begin to create ${cert_name} certificate."

    local cert_pwd=$(tr -dc "\!\@\#\%\^\&\*\(\)\_\+\-\=\{\}\[\]\|\>\<\,\?\.\:\;A-Za-z0-9" </dev/random| head -c 16)

    local ca_cert_pwd="$4"

    local output_path="$5"

    local node_float_ip_list="$6"

    encrypt_by_uniep "${cert_pwd}" ""
    local store_encode_password="${pwd_return}"

    encrypt_by_uniep "${cert_pwd}" ""
    local key_encode_password="${pwd_return}"

    encrypt_by_uniep "${cert_pwd}" ""
    local keyPem_encode_password="${pwd_return}"

    encrypt_by_uniep "${ca_cert_pwd}" ""
    local er_ca_encode_password="${pwd_return}"

    openssl genpkey -algorithm RSA -outform PEM -out "${cert_middle_name}.key" -pkeyopt rsa_keygen_bits:4096 -pkeyopt rsa_keygen_pubexp:65537 -aes-256-cbc -pass stdin <<EOF
${cert_pwd}
EOF
    if [ $? -ne 0 ];then
        log_echo "ERROR" "create ${cert_middle_name}.key failed"
        exit 1
    fi

    if [ "X${cert_middle_name}" == "Xclient" ];then
        openssl rsa -in "${cert_middle_name}.key" -out "tmp.key" -aes256 -passin stdin -passout stdin <<EOF
${cert_pwd}
${cert_pwd}
${cert_pwd}
EOF
        mv "tmp.key" "${cert_middle_name}.key"
    fi

    openssl req -new -sha256 -sigopt rsa_padding_mode:pss -sigopt rsa_pss_saltlen:-1  -keyform PEM -key ${cert_middle_name}.key  -out  ${cert_middle_name}.csr  -subj "${subject}"  -reqexts v3_req  -passin stdin <<EOF
${cert_pwd}
EOF
    if [ $? -ne 0 ];then
        log_echo "ERROR" "create ${cert_middle_name}.csr failed"
        exit 1
    fi

    chmod -R 700 demoCA

    if [ -d "demoCA" ]
    then
      rm -r "demoCA"
    fi

    mkdir -p "demoCA/newcerts"
    chmod -R 700 demoCA

    touch "demoCA/index.txt"
    touch "demoCA/index.txt.attr"
    date +%s > "demoCA/serial"

    if [ ! -z ${node_float_ip_list} ];then
        cp -pf DV_CA.cnf DV_CA_tmp.cnf
        sed -i "/\[ alt_names \]/d" DV_CA_tmp.cnf
        sed -i "/DNS./d" DV_CA_tmp.cnf
        sed -i "/IP./d" DV_CA_tmp.cnf

        echo "[ alt_names ]" >> DV_CA_tmp.cnf
        i=1
        for node_float_ip in ${node_float_ip_list}
        do
            echo "IP.${i} = ${node_float_ip}" >> DV_CA_tmp.cnf
            ((i=i+1))
        done

        openssl ca -sigopt rsa_padding_mode:pss -sigopt rsa_pss_saltlen:-1  \
            -config DV_CA_tmp.cnf  -in ${cert_middle_name}.csr -out ${cert_middle_name}.crt -cert dv_ca.crt \
            -keyfile dv_ca.key -extensions v3_server_san \
            -passin stdin <<EOF
${ca_cert_pwd}
y
y
EOF
        rm -f DV_CA_tmp.cnf
    else
        openssl ca -sigopt rsa_padding_mode:pss -sigopt rsa_pss_saltlen:-1  -config DV_CA.cnf  -in ${cert_middle_name}.csr -out ${cert_middle_name}.crt -cert dv_ca.crt -keyfile dv_ca.key -extensions v3_server -passin stdin <<EOF
${ca_cert_pwd}
y
y
EOF
    fi
    if [ $? -ne 0 ];then
        log_echo "ERROR" "create ${cert_middle_name}.crt failed"
        exit 1
    fi

    if [ -z $output_path ] && [ -d ${PreSet_PATH}/tools/sub_ca ];then
        cat dv_ca.crt >> ${cert_middle_name}.crt
    fi

    if [ "X${cert_type}" == "XCRT" ];then
        echo "pwd:${key_encode_password}" | python ${PreSet_PATH}/Common/add_cert.py "null" "manifest.json" "${cert_middle_name}.key" &&\
        python ${PreSet_PATH}/Common/add_cert.py "X509" "manifest.json" "${cert_name}" "PEM"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "python ${PreSet_PATH}/Common/add_cert.py failed,please check..."
            exit 1
        fi
        add_config "$(echo ${cert_middle_name}.key | sed "s#\.#_#g")_key_pwd" "${key_encode_password}" "${PreSet_PATH}/Common/passwords.properties"

        return 0
    fi

    pbe_option=""
    [ "X${cert_type}" != "XJKS" ] && pbe_option="-keypbe AES-256-CBC -certpbe AES-256-CBC -macalg sha256"
    openssl pkcs12 ${pbe_option} -export -in ${cert_middle_name}.crt -inkey ${cert_middle_name}.key -out ${cert_middle_name}.p12  -CAfile dv_ca.crt  -passin stdin  -passout stdin<<EOF
${cert_pwd}
${cert_pwd}
EOF
    if [ $? -ne 0 ];then
        log_echo "ERROR" "create ${cert_middle_name}.p12 failed"
        exit 1
    fi


    if [ "X${cert_type}" == "XJKS" ];then

        keytool_path=$(find /opt/oss/rtsp/ -name keytool | head -1)

        ${keytool_path} -importkeystore -v -srckeystore ${cert_middle_name}.p12 -srcstoretype pkcs12  -destkeystore ${cert_name} -deststoretype jks <<EOF
${cert_pwd}
${cert_pwd}
${cert_pwd}
EOF
        if [ $? -ne 0 ];then
            log_echo "ERROR" "create ${cert_name} failed"
            exit 1
        fi
        echo "pwd:${store_encode_password} pwd:${key_encode_password}" | python ${PreSet_PATH}/Common/add_cert.py "JKS" "manifest.json" "${cert_name}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "python ${PreSet_PATH}/Common/add_cert.py \"JKS\" \"manifest.json\" \"${cert_name}\" failed"
            exit 1
        fi
    elif [ "X${cert_type}" == "XPKCS12" ];then
        cp -f ${cert_middle_name}.p12 ${cert_name}
        if [ ! -z $output_path ];then
            mkdir -p $output_path
            cp -pf ${cert_middle_name}.crt $output_path/${cert_middle_name}.cer
            cp -pf ${cert_middle_name}.key $output_path/${cert_middle_name}_key.pem
            cp -pf ${cert_middle_name}.p12 $output_path
            cp -pf ${cert_middle_name}.crt $output_path/${cert_middle_name}_chain.cer
            rm -f ${cert_middle_name}.crt ${cert_middle_name}.key ${cert_middle_name}.p12 ${cert_middle_name}.csr
            cat iwsTrustStore.pem >> $output_path/${cert_middle_name}_chain.cer
            cp -pf iwsTrustStore.pem  $output_path/trust.cer
            cp -pf caTrustStore.jks  $output_path/trust.jks

            echo -n "${store_encode_password}" > "$output_path/${cert_middle_name}.p12_storePass"
            echo -n "${key_encode_password}" > "$output_path/${cert_middle_name}.p12_keyPass"
            echo -n "${keyPem_encode_password}" > "$output_path/${cert_middle_name}_key.pem_keyPass"
            echo -n "${er_ca_encode_password}" > "$output_path/ca_cert_pwd"

            chmod 700 $output_path

            chmod -R 600 $output_path/*

            chown ${i2k_user_name}:${i2k_group_name} -R $output_path

            cp -rpf $output_path ${PreSet_PATH}/Common/certificate/

            log_echo "INFO" "Create ${cert_name} certificate to $output_path end."

            return 0
        else
            echo "pwd:${store_encode_password} pwd:${key_encode_password}" | python ${PreSet_PATH}/Common/add_cert.py "PKCS12" "manifest.json" "${cert_name}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "python ${PreSet_PATH}/Common/add_cert.py \"PKCS12\" \"manifest.json\" \"${cert_name}\" failed."
                exit 1
            fi
        fi
    fi

    add_config "$(echo ${cert_name} | sed "s#\.#_#g")_store_pwd" "${store_encode_password}" "${PreSet_PATH}/Common/passwords.properties"
    add_config "$(echo ${cert_name} | sed "s#\.#_#g")_key_pwd" "${key_encode_password}" "${PreSet_PATH}/Common/passwords.properties"

    find -name "${cert_middle_name}.*" | grep -v "${cert_name}" | xargs -i rm -f {}


    log_echo "INFO" "Create ${cert_name} certificate end."
}

function create_key_pair()
{
    local key_type_name="$1"
    local key_name="$2"

    log_echo "INFO" "Begin to create ${key_type_name} key."

    local key_pwd=$(tr -dc "\!\@\#\%\^\&\*\(\)\_\+\-\=\{\}\[\]\|\>\<\,\?\.\:\;A-Za-z0-9" </dev/random| head -c 16)

    openssl genpkey -aes-256-cbc -algorithm RSA -out private_${key_type_name}.pem -pkeyopt rsa_keygen_bits:3072 -pass stdin <<EOF
${key_pwd}
${key_pwd}
EOF

    encrypt_by_uniep "${key_pwd}" ""
    local key_encode_password="${pwd_return}"

    echo  "pwd:${key_encode_password}" | python ${PreSet_PATH}/Common/add_cert.py "null"  "manifest.json" "${key_name}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "python ${PreSet_PATH}/Common/add_cert.py \"null\"  \"manifest.json\" \"${key_name}\" failed."
        exit 1
    fi
    add_config "$(echo ${key_name} | sed "s#\.#_#g")_key_pwd" "${key_encode_password}" "${PreSet_PATH}/Common/passwords.properties"

    openssl rsa -pubout -in private_${key_type_name}.pem -out public_${key_type_name}.pem -passin stdin <<EOF
${key_pwd}
EOF

    log_echo "INFO" "Create ${key_type_name} key end."

}

function write_file_to_json()
{
    local cert_name="$1"
    local cert_type="$2"
    local cert_pwd="$3"

    cipher_mode_now=cloudsop
    if [ ! -f "${install_path}/manager/etc/cipher/cloudsop_cipher_mode.flag" -a  "X${is_upgrade}" == "XYes" ];then
        if [ "X${CURRENT_USER}" == "Xroot" ];then
            cipher_mode_now=$(su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;echo \$CIPHER_MODE")
        else
            cipher_mode_now=$(source /opt/oss/manager/bin/engr_profile.sh;echo $CIPHER_MODE)
        fi
    fi
    if [ "X${cipher_mode_now}" != "XKMC" ];then
        encrypt_by_uniep "${cert_pwd}" ""
    else
        encrypt_by_uniep "${cert_pwd}" "SOP"
    fi

    local store_encode_password="${pwd_return}"
    if [ "X${cipher_mode_now}" != "XKMC" ];then
        encrypt_by_uniep "${cert_pwd}" ""
    else
        encrypt_by_uniep "${cert_pwd}" "SOP"
    fi
    local key_encode_password="${pwd_return}"

    echo "pwd:${store_encode_password} pwd:${key_encode_password}" | python ${PreSet_PATH}/Common/add_cert.py "${cert_type}" "manifest.json" "${cert_name}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "python ${PreSet_PATH}/Common/add_cert.py \"${cert_type}\" \"manifest.json\" \"${cert_name}\" failed."
        exit 1
    fi
    add_config "$(echo ${cert_name} | sed "s#\.#_#g")_store_pwd" "${store_encode_password}" "${PreSet_PATH}/Common/passwords.properties"

    add_config "$(echo ${cert_name} | sed "s#\.#_#g")_key_pwd" "${key_encode_password}" "${PreSet_PATH}/Common/passwords.properties"
}

function add_config()
{
    local key="$1"
    local value="$2"
    local config="$3"
    
    cat "${config}" | grep -w "${key}"  > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "${key} exist in ${config}, not need to add"
        return 0
    fi
    log_echo "INFO" "add ${key} to ${config}"
    
    echo "" >> "${config}"
    echo "${key}=${value}" >> "${config}"
    if [ "X$(whoami)" == "Xroot" ];then
        chown "${i2k_user_name}:${i2k_group_name}" "${config}"
    fi
    return 0
}

function add_cert_to_register_info()
{
    local key="$1"
    local cert="$2"

    local cert_base64_code=$(cat ${cert} | base64 --wrap=0)

    add_config "${key}" "${cert_base64_code}" "registerConfigInfo.properties"

    local cert_pwd=$(grep -A 3 ${cert} manifest.json | grep "keyPass" | awk -F ":" '{print $2}' | sed "s#\"##g" | sed "s#,##g" | tr -d ' ')

    if [ -z "${cert_pwd}" ];then
        log_echo "INFO" "cert_pwd is null,not need to add"
        return 0
    fi

    add_config "${key}_PWD" "${cert_pwd}" "registerConfigInfo.properties"

    add_config "${key}_PWD" "${cert_pwd}" "${PreSet_PATH}/Common/passwords.properties"

}
function create_certificate()
{
    log_echo "INFO" "Begin to create certificate."

    if [ -f /.dockerenv -a -f ${PreSet_PATH}/Common/certificate/manifest.json.dockerenv ];then
        cp -p ${PreSet_PATH}/Common/certificate/manifest.json.dockerenv ${PreSet_PATH}/Common/certificate/manifest.json
    fi
    if [ -f /.dockerenv -a -f ${PreSet_PATH}/Common/kafka/manifest.json.dockerenv ];then
        cp -p ${PreSet_PATH}/Common/kafka/manifest.json.dockerenv ${PreSet_PATH}/Common/kafka/manifest.json
    fi

    cd ${PreSet_PATH}/Common/certificate/

    if [ -f  "create_certificate_tag" ];then
        log_echo "INFO" "already create certificate."
        return 0
    fi

    if [ -f "manifest.json.bak" ];then
        log_echo "INFO" "resume manifest.json."
        cp -f manifest.json.bak manifest.json
    else
        log_echo "INFO" "backup manifest.json for first time."
        cp -f manifest.json manifest.json.bak
    fi

    ls | grep -v manifest.json | grep -v DV_CA.cnf |  grep -v southbound_controller_ca.pem  | xargs rm -rf

    log_echo "INFO" "create ca and trust certificate."

    local OU=$(tr -dc "A-Za-z0-9" </dev/random | head -c 16)


    if [ "X${pre_install_key}" != "XPreInstall" ] && [ ! -d ${PreSet_PATH}/tools/sub_ca ];then

        local ca_cert_pwd=$(tr -dc "\!\@\#\%\^\&\*\(\)\_\+\-\=\{\}\[\]\|\>\<\,\?\.\:\;A-Za-z0-9" </dev/random| head -c 16)
        openssl genpkey -algorithm RSA -outform PEM -out "dv_ca.key" -pkeyopt rsa_keygen_bits:4096 -pkeyopt rsa_keygen_pubexp:65537 -aes-256-cbc -pass stdin <<EOF
${ca_cert_pwd}
EOF

        openssl req -new -x509 -sha256 -days 18250 -keyform PEM -key dv_ca.key -sigopt rsa_padding_mode:pss -subj "/C=${countryName}/O=${organizationName}/CN=${commonName}/OU=${OU}" -sigopt rsa_pss_saltlen:-1 -outform PEM -out dv_ca.crt -config DV_CA.cnf -extensions v3_ca -passin stdin <<EOF
${ca_cert_pwd}
EOF

    elif [ "X${pre_install_key}" == "XPreInstall" ];then
        dv_ca_crt=$(cat ${PreSet_PATH}/pre_install.properties | grep "dv_ca_crt" | awk -F '=' '{print $2}')
        dv_ca_key=$(cat ${PreSet_PATH}/pre_install.properties | grep "dv_ca_key" | awk -F '=' '{print $2}')
        ca_cert_pwd=$(cat ${PreSet_PATH}/pre_install.properties | grep "ca_cert_pwd" | awk -F '=' '{print $2}')
        if [ -z "${dv_ca_crt}" -o -z "${dv_ca_key}" -o -z "${ca_cert_pwd}" ];then
            log_echo "INFO" "The dv_ca_crt dv_ca_key ca_cert_pwd is null.need to create it."
            local ca_cert_pwd=$(tr -dc "\!\@\#\%\^\&\*\(\)\_\+\-\=\{\}\[\]\|\>\<\,\?\.\:\;A-Za-z0-9" </dev/random| head -c 16)
            openssl genpkey -algorithm RSA -outform PEM -out "dv_ca.key" -pkeyopt rsa_keygen_bits:4096 -pkeyopt rsa_keygen_pubexp:65537 -aes-256-cbc -pass stdin <<EOF
${ca_cert_pwd}
EOF

            openssl req -new -x509 -sha256 -days 18250 -keyform PEM -key dv_ca.key -sigopt rsa_padding_mode:pss -subj "/C=${countryName}/O=${organizationName}/CN=${commonName}/OU=${OU}" -sigopt rsa_pss_saltlen:-1 -outform PEM -out dv_ca.crt -config DV_CA.cnf -extensions v3_ca -passin stdin <<EOF
${ca_cert_pwd}
EOF
        else
            log_echo "INFO" "The dv_ca_crt dv_ca_key ca_cert_pwd is pre_install get it."
            echo ${dv_ca_crt} | base64 -d > dv_ca.crt
            echo ${dv_ca_key} | base64 -d > dv_ca.key
            cp dv_ca.crt ${PreSet_PATH}/Common/dvca
            cp dv_ca.key ${PreSet_PATH}/Common/dvca
            sed -i "s#^CREATE_MODE=.*#CREATE_MODE=1#" ${PreSet_PATH}/Common/dvca/dv-ca.properties
            sed -i "s#^CA_KEY_FILE=.*#CA_KEY_FILE=/home/<USER>/etc/ssl/dvca/dv_ca.key#" ${PreSet_PATH}/Common/dvca/dv-ca.properties
            sed -i "s#^CA_CERT_FILE=.*#CA_CERT_FILE=/home/<USER>/etc/ssl/dvca/dv_ca.crt#" ${PreSet_PATH}/Common/dvca/dv-ca.properties
            decrypt_password ${ca_cert_pwd}
            ca_cert_pwd=${decryptPasswd}
            ## ca_cert_pwd 编码后暂存,会在安装DVCommonConfigService时进行SOP加密
            local tmp_ca_cert_pwd=$(echo "${ca_cert_pwd}" | base64 | sed "s/#/\\\#/g")
            sed -i "s#^CA_KEY_PWD=.*#CA_KEY_PWD=${tmp_ca_cert_pwd}#" ${PreSet_PATH}/Common/dvca/dv-ca.properties
        fi
    elif [ -d ${PreSet_PATH}/tools/sub_ca ];then
        cp ${PreSet_PATH}/tools/sub_ca/root_ca.pem   dv_root_ca.pem
        openssl x509 -inform DER -in ${PreSet_PATH}/tools/sub_ca/sub_ca.cer -out dv_ca.crt
        cp -a ${PreSet_PATH}/tools/sub_ca/DigitalView_CA.csr dv_ca.csr
        cp -a ${PreSet_PATH}/tools/sub_ca/DigitalView_CA.key dv_ca.key
        cat dv_root_ca.pem >> dv_ca.crt

        ca_cert_pwd=$(cat ${PreSet_PATH}/tools/sub_ca/ca_cert_pwd.properties | grep "ca_cert_pwd" | awk -F '=' '{print $2}')
        decrypt_password ${ca_cert_pwd}
        ca_cert_pwd=${decryptPasswd}
    fi


    openssl pkcs12 -certpbe aes-256-cbc -keypbe aes-256-cbc -macalg sha256 -export -in dv_ca.crt -passin stdin  -out dv_ca.p12 -passout stdin  -inkey dv_ca.key <<EOF
${ca_cert_pwd}
${ca_cert_pwd}
EOF

    write_file_to_json "dv_ca.p12" "PKCS12" "${ca_cert_pwd}"

    keytool_path=$(find ${install_path}/rtsp/ -name keytool | head -1)
    ${keytool_path} -import -alias my_ca_root -file dv_ca.crt  -keystore caTrustStore.jks -noprompt <<EOF
${ca_cert_pwd}
${ca_cert_pwd}
EOF

    write_file_to_json "caTrustStore.jks" "JKS" "${ca_cert_pwd}"

    cp -f caTrustStore.jks servertrust.keystore

    write_file_to_json "servertrust.keystore" "JKS" "${ca_cert_pwd}"

    add_cert_to_register_info "UNIAGENT_TRUSTSTORE" "servertrust.keystore"

    cp -f caTrustStore.jks uniagent_client_trust.jks

    write_file_to_json "uniagent_client_trust.jks" "JKS" "${ca_cert_pwd}"

    add_cert_to_register_info "UNIAGENT_TRACE_TRUSTSTORE" "uniagent_client_trust.jks"

    python ${PreSet_PATH}/Common/add_cert.py "X509" "manifest.json" "dv_ca.crt" "PEM"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "python ${PreSet_PATH}/Common/add_cert.py X509 manifest.json dv_ca.crt PEM failed."
        exit 1
    fi
    cp -f dv_ca.crt ca.crt

    python ${PreSet_PATH}/Common/add_cert.py "X509" "manifest.json" "ca.crt" "PEM"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "python ${PreSet_PATH}/Common/add_cert.py X509 manifest.json ca.crt PEM failed."
        exit 1
    fi
    cp -f dv_ca.crt uniagent_ca.pem

    python ${PreSet_PATH}/Common/add_cert.py "X509" "manifest.json" "uniagent_ca.pem" "PEM"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "python ${PreSet_PATH}/Common/add_cert.py X509 manifest.json uniagent_ca.pem PEM failed."
        exit 1
    fi
    cp -f dv_ca.crt iwsTrustStore.pem


    python ${PreSet_PATH}/Common/add_cert.py "X509" "manifest.json" "iwsTrustStore.pem" "PEM"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "python ${PreSet_PATH}/Common/add_cert.py X509 manifest.json iwsTrustStore.pem PEM failed."
        exit 1
    fi
    add_cert_to_register_info "MED_TRUST_PEM" "iwsTrustStore.pem"

    cp -f dv_ca.crt outserver_ca.pem

    python ${PreSet_PATH}/Common/add_cert.py "X509" "manifest.json" "outserver_ca.pem" "PEM"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "python ${PreSet_PATH}/Common/add_cert.py X509 manifest.json outserver_ca.pem PEM failed."
        exit 1
    fi
    log_echo "INFO" "Create ca and trust certificate end."

    create_server_certificate "uniagent_client.jks" "/C=CN/O=Huawei/OU=DigitalView/CN=client.uniagent.digitalview.huawei.com" "JKS" "${ca_cert_pwd}"

    create_server_certificate "serverkey.keystore" "/C=CN/O=Huawei/OU=DigitalView/CN=uniagent.rest" "JKS" "${ca_cert_pwd}"

    add_cert_to_register_info "UNIAGENT_KEYSTORE" "serverkey.keystore"

    create_server_certificate "openapi_server.p12" "/C=CN/O=Huawei/OU=DigitalView/CN=OpenApi" "PKCS12" "${ca_cert_pwd}"

    create_server_certificate "outserver.p12" "/C=CN/O=Huawei/OU=DigitalView/CN=OutServer" "PKCS12" "${ca_cert_pwd}"

    create_server_certificate "outclient.pfx" "/C=CN/O=Huawei/OU=DigitalView/CN=OutClient" "PKCS12" "${ca_cert_pwd}"

    create_server_certificate "nodeKeyStore.jks" "/C=CN/O=Huawei/OU=DigitalView/CN=nodeKeyStore" "JKS" "${ca_cert_pwd}"

    create_server_certificate "client.crt" "/C=CN/O=Huawei/OU=DigitalView/CN=com.huawei.cmp" "CRT" "${ca_cert_pwd}"

    create_server_certificate "controller_server.p12" "/C=CN/O=Huawei/OU=DigitalView/CN=com.huawei.cmp" "PKCS12" "${ca_cert_pwd}"

    create_server_certificate "foreign_client.p12" "/C=CN/O=Huawei/OU=DigitalView/CN=outclient.digitalview.huawei.com" "PKCS12" "${ca_cert_pwd}"

    #Custom independent cert and output_path.

    create_key_pair "sftp" "sftp_privateKey"

    rm -rf ${PreSet_PATH}/Common/sign_dv/*

    mkdir -p ${PreSet_PATH}/Common/sign_dv

    create_sign_key "uniagent"

    create_sign_key "pkg"

    cp -rf ${PreSet_PATH}/Common/release_integrity ${PreSet_PATH}/Common/sign_dv/

    create_kafka_ca_certificate

    touch ${PreSet_PATH}/Common/certificate/create_certificate_tag

    if [ -f /.dockerenv ];then
        cp -p ${PreSet_PATH}/Common/certificate/manifest.json ${PreSet_PATH}/Common/certificate/manifest.json.dockerenv
    fi
}

function preset_sign_dv_key()
{
    if [ -f  ${PreSet_PATH}/Common/sign_dv/create_certificate_upgrade_tag ];then
        log_echo "INFO" "already create upgrade certificate."
        return 0
    fi

    rm -rf ${PreSet_PATH}/Common/sign_dv/*

    mkdir -p ${PreSet_PATH}/Common/sign_dv

    create_sign_key "uniagent"

    create_sign_key "pkg"

    cp -rf ${PreSet_PATH}/Common/release_integrity ${PreSet_PATH}/Common/sign_dv/

    [ -d ${PreSet_PATH}/Common/certificate/demoCA ] && chmod 700 ${PreSet_PATH}/Common/certificate/demoCA
    find ${PreSet_PATH}/Common/certificate -type d | xargs -i chmod 700 {} > /dev/null 2>&1

    touch ${PreSet_PATH}/Common/sign_dv/create_certificate_upgrade_tag
}

function create_certificate_for_upgrade()
{
    log_echo "INFO" "create ca and trust certificate for upgrade."

    cd ${PreSet_PATH}/Common/certificate/

    if [ -f  "create_certificate_upgrade_tag" ];then
        log_echo "INFO" "already create upgrade certificate."
        return 0
    fi

    if [ -f "manifest.json.bak" ];then
        log_echo "INFO" "resume manifest.json."
        cp -f manifest.json.bak manifest.json
    else
        log_echo "INFO" "backup manifest.json for first time."
        cp -f manifest.json manifest.json.bak
    fi

    chmod 700 demoCA 2> /dev/null
    chmod 700 issue_er_cert 2> /dev/null

    ls | grep -v manifest.json | grep -v DV_CA.cnf | grep -v southbound_controller_ca.pem  | xargs rm -rf

    local OU=$(tr -dc "A-Za-z0-9" </dev/random | head -c 16)
    
    local ca_cert_pwd=$(tr -dc "\!\@\#\%\^\&\*\(\)\_\+\-\=\{\}\[\]\|\>\<\,\?\.\:\;A-Za-z0-9" </dev/random| head -c 16)
    
    openssl genpkey -algorithm RSA -outform PEM -out "dv_ca.key" -pkeyopt rsa_keygen_bits:4096 -pkeyopt rsa_keygen_pubexp:65537 -aes-256-cbc -pass stdin <<EOF
${ca_cert_pwd}
EOF

    openssl req -new -x509 -sha256 -days 18250 -keyform PEM -key dv_ca.key -sigopt rsa_padding_mode:pss -subj "/C=${countryName}/O=${organizationName}/CN=${commonName}/OU=${OU}" -sigopt rsa_pss_saltlen:-1 -outform PEM -out dv_ca.crt -config DV_CA.cnf -extensions v3_ca -passin stdin <<EOF
${ca_cert_pwd}
EOF

    openssl pkcs12 -certpbe aes-256-cbc -keypbe aes-256-cbc -macalg sha256 -export  -in dv_ca.crt -passin stdin  -out dv_ca.p12 -passout stdin  -inkey dv_ca.key <<EOF
${ca_cert_pwd}
${ca_cert_pwd}
EOF
    
    keytool_path=$(find ${install_path}/rtsp/ -name keytool | head -1)
    ${keytool_path} -import -alias my_ca_root -file dv_ca.crt  -keystore caTrustStore.jks -noprompt <<EOF
${ca_cert_pwd}
${ca_cert_pwd}
EOF
    create_server_certificate "serverkey.keystore" "/C=CN/O=Huawei/OU=DigitalView/CN=uniagent.rest" "JKS" "${ca_cert_pwd}"
    
    
    create_server_certificate "client.crt" "/C=CN/O=Huawei/OU=DigitalView/CN=com.huawei.cmp" "CRT" "${ca_cert_pwd}"
    
    add_cert_to_register_info "UNIAGENT_KEYSTORE" "serverkey.keystore"
    
    cp caTrustStore.jks servertrust.keystore  
    
    write_file_to_json "servertrust.keystore" "JKS" "${ca_cert_pwd}"
    
    add_cert_to_register_info "UNIAGENT_TRUSTSTORE" "servertrust.keystore"
    
    cp -f caTrustStore.jks uniagent_client_trust.jks
    
    write_file_to_json "uniagent_client_trust.jks" "JKS" "${ca_cert_pwd}"
    
    add_cert_to_register_info "UNIAGENT_TRACE_TRUSTSTORE" "uniagent_client_trust.jks"

    create_kafka_ca_certificate
    
    cd ${PreSet_PATH}/Common/certificate/
    create_server_certificate "uniagent_client.jks" "/C=CN/O=Huawei/OU=DigitalView/CN=client.uniagent.digitalview.huawei.com" "JKS" "${ca_cert_pwd}"
    i2k_float_ip=$(cat ${PreSet_PATH}/tools/expInfo/product*.json | sed 's/,/\n/g' |grep ":31943" |awk -F'"' '{print $4}'| awk -F':31943' '{print $1}' | sed 's/\[\|\]//g' | sed 's/ //g'| grep -v https)
    create_nbi_cert "${i2k_float_ip}" "${ca_cert_pwd}"
    
    touch ${PreSet_PATH}/Common/certificate/create_certificate_upgrade_tag
}

function create_kafka_ca_certificate()
{
    log_echo "INFO" "create kafka ca and trust certificate."
    
    cd ${PreSet_PATH}/Common/kafka/

    if [ -f  "create_kafka_certificate_tag" ];then
        log_echo "INFO" "already create kafka certificate."
        return 0
    fi
    
    if [ -f "manifest.json.bak" ];then
        log_echo "INFO" "resume manifest.json."
        cp -f manifest.json.bak manifest.json
    else
        log_echo "INFO" "backup manifest.json for first time."
        cp -f manifest.json manifest.json.bak
    fi
    
    ls | grep -v manifest.json | grep -v Kakfa_CA.cnf  | xargs rm -rf
    
    local kafka_ca_cert_pwd="$(tr -dc "\@\#\%\^\*\_\+\=\,\?\.A-Za-z0-9" </dev/random| head -c 16)"
    
    openssl genpkey -algorithm RSA -outform PEM -out "dv_ca.key" -pkeyopt rsa_keygen_bits:4096 -pkeyopt rsa_keygen_pubexp:65537 -aes-256-cbc -pass stdin <<EOF
${kafka_ca_cert_pwd}
EOF

    openssl req -new -x509 -sha256 -days 18250 -keyform PEM -key dv_ca.key -sigopt rsa_padding_mode:pss -subj "/C=${countryName}/O=${organizationName}/CN=${commonName}/OU=${OU}" -sigopt rsa_pss_saltlen:-1 -outform PEM -out dv_ca.crt -config Kakfa_CA.cnf -extensions v3_ca -passin stdin <<EOF
${kafka_ca_cert_pwd}
EOF


    keytool_path=$(find ${install_path}/rtsp/ -name keytool | head -1)
    ${keytool_path} -import -alias my_ca_root -file dv_ca.crt  -keystore trust.jks -noprompt <<EOF
${kafka_ca_cert_pwd}
${kafka_ca_cert_pwd}
EOF

    write_file_to_json "trust.jks" "JKS" "${kafka_ca_cert_pwd}"
    
    touch create_kafka_certificate_tag
    if [ -f /.dockerenv ];then
        cp -p ${PreSet_PATH}/Common/kafka/manifest.json ${PreSet_PATH}/Common/kafka/manifest.json.dockerenv
    fi
    
    cd -
}
function set_swap_cfg()
{
    local node_memtotal=$(awk '/^MemTotal/ { printf("%d", $2/1048576) }' /proc/meminfo)
    local isNumber=$(echo "${node_memtotal}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ -z "${node_memtotal}" ] || [ "${isNumber}" == "No" ];then
        log_echo "ERROR" "Can't get MemTotal using 'cat /proc/meminfo', please check"
        exit 1
    fi
    if [ "$(echo ${is_integrated_df_and_service}|tr 'a-z' 'A-Z')" == "YES"  -o "X${K8S_IPV4}" != "X" ] || [ ${node_memtotal} -gt 190 ];then
        if [ ! -f "${PreSet_PATH}/tools/ATAEExtend/disk_default.cfg.bak" ];then
            cp  ${PreSet_PATH}/tools/ATAEExtend/disk_default.cfg  ${PreSet_PATH}/tools/ATAEExtend/disk_default.cfg.bak
        fi
        if [ ! -f "${PreSet_PATH}/Common/ext_disk.properties.bak" ];then
            cp  ${PreSet_PATH}/Common/ext_disk.properties  ${PreSet_PATH}/Common/ext_disk.properties.bak
        fi
        sed -i "/^swap=/d"  ${PreSet_PATH}/tools/ATAEExtend/disk_default.cfg
        sed -i "/^swap=/d"  ${PreSet_PATH}/Common/ext_disk.properties
        sed -i "/^swap=/d"  ${PreSet_PATH}/Common/ext_disk.properties.tmp
    fi
}

function get_new_privatekey()
{
    if [ "X${old_privatekey}" == "X" ];then
        log_echo "ERROR" "Get ossuser ssh_privatekey of ${node_ip} failed, please check"
        exit 1
    fi
    decrypt_privatekey=$(su - ossadm -c "export HISTSIZE=0;source ${INSTALL_PATH}/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('$old_privatekey'); print(tmp)\"")
    if [ "X${decrypt_privatekey}" == "X" ];then
        log_echo "ERROR" "Get decrypt_privatekey of ossuser ssh_privatekey for ${node_ip} failed, please check"
        exit 1
    fi
    encrypt_by_uniep "${decrypt_privatekey}" "SOP"
    new_privatekey="${pwd_return}"
    if [ "X${new_privatekey}" == "X" ];then
        log_echo "ERROR" "Get new_privatekey of ossuser ssh_privatekey for ${node_ip} failed, please check"
        exit 1
    fi
}

function get_ossuser_sshkey_by_ip()
{
    local node_ip="$1"
    log_echo "INFO" "Begin to get_ossuser_sshkey_by_ip node_ip=${node_ip}"
    ip addr | grep -wF "${node_ip}" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        return 0
    fi
    
    local ossadm_preset_dir=/home/<USER>/DVPreSet
    local node_scp_ip=${node_ip}
    local isIpv6=$(echo ${node_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        node_scp_ip="[${node_ip}]"
    fi
    local sshagent_privatekey_path=${PreSet_PATH}/tmp_sshagent_privatekey/${node_ip}
    mkdir -p ${sshagent_privatekey_path} > /dev/null 2>&1
    ## Obtaining and Decrypting the Password
    su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ip} sudo -u ossuser cat /home/<USER>/.ssh/sshagent_privatekey" > "${sshagent_privatekey_path}/sshagent_privatekey" 2>&1
    log_echo "INFO" "get_ossuser_sshkey_by_ip node_ip=${node_ip} End"
}

function update_ossuser_sshkey_by_ip()
{
    local node_ip="$1"
    log_echo "INFO" "Begin to update_ossuser_sshkey_by_ip node_ip=${node_ip}"
    ip addr | grep -wF "${node_ip}" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        return 0
    fi
    
    local ossadm_preset_dir=/home/<USER>/DVPreSet
    local node_scp_ip=${node_ip}
    local isIpv6=$(echo ${node_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        node_scp_ip="[${node_ip}]"
    fi
    local sshagent_privatekey_path=${PreSet_PATH}/tmp_sshagent_privatekey/${node_ip}
    ## mkdir temporary directory
    su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ip} mkdir -p ${ossadm_preset_dir}"
    su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ip} chmod 770 ${ossadm_preset_dir}"
    su - ossadm -c "scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} "${sshagent_privatekey_path}/sshagent_privatekey" ossadm@${node_scp_ip}:${ossadm_preset_dir}"
    su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ip} sudo -u ossuser cp -f ${ossadm_preset_dir}/sshagent_privatekey /home/<USER>/.ssh/sshagent_privatekey"
    su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${node_ip} rm -rf /home/<USER>/DVPreSet"
    log_echo "INFO" "update_ossuser_sshkey_by_ip node_ip=${node_ip} End"
}

function update_ossuser_sshkey()
{
    if [ "X${open_ssh_encrypt}" != "XYes" ];then
        return 0
    fi

    if [ -f "/home/<USER>/.ssh/sshagent_privatekey" ];then
        old_privatekey=$(grep "privatekey=" /home/<USER>/.ssh/sshagent_privatekey |awk -F'privatekey=' '{print $2}')
        get_new_privatekey
        sed -i "s#privatekey=.*#privatekey=${new_privatekey}#" /home/<USER>/.ssh/sshagent_privatekey
    fi

    local node_lists=$(su - ossadm -c 'source /opt/oss/manager/bin/engr_profile.sh;nodelists=$(python -c "from deployment import nodelist;print(\",\".join([node.get_mgmt_ip_addr() for node in nodelist.NodeListMgr().get_all_nodes()]))");echo ${nodelists}')
    
    if [ "X${node_lists}" == "X" ];then
        log_echo "ERROR" "Get node_lists for update_ossuser_sshkey failed, please check"
        exit 1
    fi

    ## Temporary directory. Used for solve the problem that no root permission during the installation on the other node.
    node_preset_concurrent "get_ossuser_sshkey_by_ip" "${node_lists}"
    
    chown -R ossadm:ossgroup "${PreSet_PATH}/tmp_sshagent_privatekey"
    ## A copy is required due to certificate permission restrictions.
    for node_ip in $(echo "${node_lists}" | sed "s#,# #g");
    do
        ip addr | grep -wF "${node_ip}" > /dev/null 2>&1
        if [ $? -eq 0 ];then
            continue
        fi
        old_privatekey=$(grep "privatekey=" ${PreSet_PATH}/tmp_sshagent_privatekey/${node_ip}/sshagent_privatekey |awk -F'privatekey=' '{print $2}')
        ## Re-encrypt and replace the file
        get_new_privatekey
        sed -i "s#privatekey=.*#privatekey=${new_privatekey}#" "${PreSet_PATH}/tmp_sshagent_privatekey/${node_ip}/sshagent_privatekey"
    done
    
    node_preset_concurrent "update_ossuser_sshkey_by_ip" "${node_lists}"
    log_echo "INFO" "update_ossuser_sshkey End"
}

function create_i2k_cert()
{
    local i2k_server_ip=$1
    sh ${PreSet_PATH}/Common/i2k_cert/create_i2k_cert.sh "${i2k_server_ip}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "create_i2k_cert failed, please check"
        exit 1
    fi
}

function update_keystorepwd_i2k()
{
    local i2k_iplist="$1"
    log_echo "INFO" "Begin to update_keystorepwd for i2k"
    old_keystorepwd=$(grep "keystore_pwd=" ${PreSet_PATH}/Common/i2k_cert/keystore_pwd |awk -F'keystore_pwd=' '{print $2}')
    decrypt_keystorepwd=$(su - ossadm -c "export HISTSIZE=0;source ${INSTALL_PATH}/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('$old_keystorepwd'); print(tmp)\"")
    if [ "X${decrypt_keystorepwd}" == "X" ];then
        log_echo "ERROR" "Get decrypt_keystorepwd for i2k failed, please check"
        exit 1
    fi
    encrypt_by_uniep "${decrypt_keystorepwd}" "SOP"
    new_keystorepwd="${pwd_return}"
    if [ "X${new_keystorepwd}" == "X" ];then
        log_echo "ERROR" "Get new_keystorepwd for i2k failed, please check"
        exit 1
    fi

    cp -f ${PreSet_PATH}/Common/i2k_cert/keystore_pwd /tmp/keystore_pwd_tmp
    chown ossadm:ossgroup /tmp/keystore_pwd_tmp
    sed -i "s#keystore_pwd=.*#keystore_pwd=${new_keystorepwd}#" /tmp/keystore_pwd_tmp
    
    for i2k_ip in ${i2k_iplist}
    do
        scp_i2k_ip=${i2k_ip}
        echo ${i2k_ip}|grep ":"
        if [ $? -eq 0 ];then
            scp_i2k_ip="\[${i2k_ip}\]"
        fi
        ip addr | grep -wF "${i2k_ip}" > /dev/null 2>&1
        if [ $? -eq 0 ];then
            if [ -f "/home/<USER>/i2k_cert/keystore_pwd" ];then
                sed -i "s#keystore_pwd=.*#keystore_pwd=${new_keystorepwd}#" /home/<USER>/i2k_cert/keystore_pwd
            fi
        else
            su - ossadm -c "scp -o StrictHostKeyChecking=no -P ${i2k_ssh_port} /tmp/keystore_pwd_tmp ossadm@${scp_i2k_ip}:/tmp/"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Scp keystore_pwd_tmp to ${i2k_ip} failed, please check"
                exit 1
            fi
            
            su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${i2k_ip} chmod 640 /tmp/keystore_pwd_tmp"
            
            su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${i2k_ip} sudo -u ossuser cp -f /tmp/keystore_pwd_tmp /home/<USER>/i2k_cert/keystore_pwd"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Update keystore_pwd of ${i2k_ip} failed, please check"
                exit 1
            fi
            
            su - ossadm -c "ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${i2k_ip} rm -f /tmp/keystore_pwd_tmp"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "remove /tmp/keystore_pwd_tmp of ${i2k_ip} failed, please check"
                exit 1
            fi
        fi
    done
    
    rm -f /tmp/keystore_pwd_tmp
    if [ -f "/tmp/keystore_pwd_tmp" ];then
        log_echo "ERROR" "remove /tmp/keystore_pwd_tmp failed at this node, please check"
        exit 1
    fi
}

function create_nbi_cert()
{
    local i2k_server_ip=$1
    local ca_pwd="$2"
    echo "${ca_pwd}" | sh ${PreSet_PATH}/Common/i2k_cert/create_i2k_cert.sh "${i2k_server_ip}" "nbi"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "create_nbi_cert failed, please check"
        exit 1
    fi
}

function add_nodetag_by_nodename()
{
    local json_path=$1
    local nodename=$2
    local nodetag=$3
    if [ "X$(whoami)" == "Xroot" ];then
        ${PreSet_PATH}/UniEp/python/bin/python ${PreSet_PATH}/tools/handlejson.py "add_nodetag_by_nodename"   "${json_path}" "${nodename}" "${nodetag}"
    else
        source /opt/oss/manager/bin/engr_profile.sh;python ${PreSet_PATH}/tools/handlejson.py "add_nodetag_by_nodename"   "${json_path}" "${nodename}" "${nodetag}"
    fi
    if [ $? -ne 0 ];then
        log_echo "ERROR" "add nodetag to ${nodename} failed"
        exit 1
    fi
}

function del_nodetag_by_nodename()
{
    local json_path=$1
    local nodename=$2
    local nodetag=$3
    if [ "X$(whoami)" == "Xroot" ];then
        ${PreSet_PATH}/UniEp/python/bin/python ${PreSet_PATH}/tools/handlejson.py "del_nodetag_by_nodename"   "${json_path}" "${nodename}" "${nodetag}"
    else
        source /opt/oss/manager/bin/engr_profile.sh;python ${PreSet_PATH}/tools/handlejson.py "del_nodetag_by_nodename"   "${json_path}" "${nodename}" "${nodetag}"
    fi
    if [ $? -ne 0 ];then
        log_echo "ERROR" "del nodetag to ${nodename} failed"
        exit 1
    fi
}

## 检查参数是否为空
function is_empty()
{
    local tmp_key="$1"
    local tmp_value="$2"
    local result_not_exit="$3"
    if [ -z "${tmp_value}" ];then
        log_echo "ERROR" "The ${tmp_key} value is null."
        if [ -z "${result_not_exit}" ];then
            exit 1
        fi
        return 1
    fi
    return 0
}

## 检查文件是否存在 
function file_exists()
{
    local file_path="$1"
    is_empty "file_path" "${file_path}"
    
    if [ ! -f ${file_path} ];then
        log_echo "ERROR" "The ${file_path} is not exist."
        exit 1
    fi
    log_echo "INFO" "The ${file_path} is exist."
    return 0
}

## 根据节点名称关键字，获取节点数量。
function get_node_total_number_by_nodename()
{
    local node_name_key="$1"
    log_echo "INFO" "get_node_total_number_by_nodename start.node_name_key=${node_name_key}"
    is_empty "node_name_key" "${node_name_key}"
    
    local node_list_file="/opt/oss/manager/etc/sysconf/nodelists.json"
    file_exists "${node_list_file}"
    
    local total_num=$(cat ${node_list_file} | sed 's/,/\n/g' | grep "hostname" |grep "${node_name_key}" | wc -l)
    log_echo "INFO" "get_node_total_number_by_nodename End.total_num=${total_num}"
    return ${total_num}
}

## 根据节点名称，删除TAG列表对应的TAG。 
function del_nodetag_list()
{
    local node_json_path="$1"
    local nodename="$2"
    local nodetag_list="$3"
    log_echo "INFO" "del_nodetag_list start.node_json_path=${node_json_path} nodename=${nodename} nodetag_list=${nodetag_list}"
    is_empty "node_json_path" "${node_json_path}"
    is_empty "nodename" "${nodename}"
    is_empty "nodetag_list" "${nodetag_list}"
    
    local tmp_node_tag=""
    for tmp_node_tag in $(echo "${nodetag_list}" |sed "s#,# #g");
    do
        ## 循环删除指定节点名称对应的TAG，修改指定的node json 文件。
        del_nodetag_by_nodename "${node_json_path}" "${nodename}" "${tmp_node_tag}"
    done
    log_echo "INFO" "del_nodetag_list end."
}

function check_item_value()
{
    local index="$1"
    is_empty "node_ipv4_key[${index}]" "${node_ipv4_key[${index}]}"
    is_empty "node_ipv6_key[${index}]" "${node_ipv6_key[${index}]}"
    is_empty "extend_node_ipv4_key[${index}]" "${extend_node_ipv4_key[${index}]}"
    is_empty "extend_node_ipv6_key[${index}]" "${extend_node_ipv6_key[${index}]}"
    
    is_empty "other_check_key[${index}]" "${other_check_key[${index}]}"
    is_empty "already_installed_node_name_key[${index}]" "${already_installed_node_name_key[${index}]}"
    is_empty "node_greater_than_number[${index}]" "${node_greater_than_number[${index}]}"
    is_empty "node_name[${index}]" "${node_name[${index}]}"
    is_empty "delete_nodetag_list[${index}]" "${delete_nodetag_list[${index}]}"
}

## 根据定制删除TAG的配置文件规则，删除对应TAG
function custom_del_nodetag()
{
    log_echo "INFO" "custom_del_nodetag Start..."
    ## 定制删除TAG的配置文件
    local custom_config_file="$1"
    ## 操作类型，install 安装，extend 扩容 ，upgrade 升级 
    local operation_type="$2"
    ## 是否和DF合设 ，Yes 表示合设，No 表示不合设。 
    local is_integrated_df_and_service="$3"
    ## 安装和扩容场景配置安装IP参数和扩容IP参数的配置文件，永远获取配置文件IP数量。
    local cfg_file_path="$4"
    ## 需要修改的node json文件，根据条件判断后删除对应节点的node Tag.并修改该文件。
    local node_json_path="$5"
    ## ip 类型 
    local ip_type="$6"
    
    ## 参数是否为空检查。
    is_empty "custom_config_file" "${custom_config_file}"
    is_empty "operation_type" "${operation_type}"
    is_empty "is_integrated_df_and_service" "${is_integrated_df_and_service}"
    is_empty "cfg_file_path" "${cfg_file_path}"
    is_empty "node_json_path" "${node_json_path}"
    is_empty "ip_type" "${ip_type}"
    
    ## 文件格式转换和引用配置文件参数。
    dos2unix ${custom_config_file}
    source ${custom_config_file}
    
    ## 获取当前环境内存 1G=1048576 byte ,内存byte转换单位为G 
    local node_memtotal=$(awk '/^MemTotal/ { printf("%d", $2/1048576) }' /proc/meminfo)
    is_empty "node_memtotal" "${node_memtotal}"
    
    local isNumber=$(echo "${node_memtotal}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        log_echo "ERROR" "The node_memtotal is not number, please check"
        exit 1
    fi
    
    ## 初始化临时参数
    local other_check_ret=0
    local node_key=""
    local node_total_num=0
    local already_installed_node_num=0
    local index=1
    ## 总内存大小 190G ，单位G。 超过190G表示物理机场景。 
    local total_memory_size=190
    ## nodetag_custom_count 来着于 ${custom_config_file} 中配置的总数，根据配置的定制参数循环。
    for index in $(seq 1 ${nodetag_custom_count});
    do
        check_item_value "${index}"
        
        ## 循环判断每个定制集，是否需要判断其他检查项，比如，是否需要检查是否和DF合设的场景。
        ## 如果配置了 other_check_key[x]=is_integrated_df_and_service,表示要检查
        ## 并且 is_integrated_df_and_service=Yes 时，表示合设。 满足要删除TAG的条件之一。
        ## 如果 other_check_key[x]=No 或者 is_integrated_df_and_service=No ,表示不需要检查该场景或者非合设场景。
        ## 综上，初始其他检查项临时变量 other_check_ret=0 ,表示为True.  other_check_ret=1 表示为False.
        
        ## other_check_key[x]=No 时，other_check_ret=0
        other_check_ret=0
        ## other_check_key[x]=is_integrated_df_and_service 需要判断是否物理机独占。
        if [ "${other_check_key[${index}]}" == "is_integrated_df_and_service" ];then
            ## ${node_memtotal} > 190 表示物理机场景， is_integrated_df_and_service 不等于Yes ,表示独占。 不需要删除TAG
            if [ ${node_memtotal} -gt ${total_memory_size} -a "${is_integrated_df_and_service}" != "Yes" ];then
                other_check_ret=1
            fi
            ## 其他场景一： ${node_memtotal} > 190 并且 is_integrated_df_and_service=Yes, 表示物理机场景并且和DF合设， other_check_ret=0 要删TAG
            ## 其他场景二： ${node_memtotal} < 190 表示非物理机场景，并且 is_integrated_df_and_service=Yes 或者 No , other_check_ret=0 要删TAG
        fi
        
        log_echo "INFO" "The index=${index} other_check_ret=${other_check_ret}"
        
        if [ "${operation_type}" == "install" ];then
            ## 安装场景 
            log_echo "INFO" "this install scenarios."
            ##获取定制需要判断节点数的参数，后面会根据这个参数获取对应配置的IP数量。默认获取ipv4类型的key，因为ipv4单栈和双栈都有ipv4。
            node_key="${node_ipv4_key[${index}]}"
            ## 如果ip type为 1, 表示ipv6单栈，获取IPV6的参数。
            [ "${ip_type}" == "1" ] && node_key="${node_ipv6_key[${index}]}"
        elif [ "${operation_type}" == "extend" ];then
            ## 扩容场景 
            log_echo "INFO" "this extend scenarios."
            ##获取定制需要判断节点数的参数，后面会根据这个参数获取对应配置的IP数量。默认获取ipv4类型的key，因为ipv4单栈和双栈都有ipv4。
            node_key="${extend_node_ipv4_key[${index}]}"
            ## 如果ip type为 1, 表示ipv6单栈，获取IPV6的参数。
            [ "${ip_type}" == "1" ] && node_key="${extend_node_ipv6_key[${index}]}"
        else
            log_echo "INFO" "this ${operation_type} scenarios."
        fi
        
        ## 初始节点数为0
        node_total_num=0
        if [ "${operation_type}" == "install" -o "${operation_type}" == "extend" ];then
            ## 安装，扩容，根据上面ip类型和定制要查找的配置参数，获取对应参数配置的IP数量。
            node_total_num=$(cat ${cfg_file_path} |grep -v "^[[:blank:]]*#" |grep "^${node_key}=" |awk -F'=' '{print $2}' |sed "s#,# #g"| awk '{print NF}')
            log_echo "INFO" "node_key=${node_key} node_total_num=${node_total_num} other_check_ret=${other_check_ret}"
        fi
        
        if [ "${operation_type}" == "extend" -o "${operation_type}" == "upgrade" ];then
            ## 扩容场景和升级场景，需要获取指定节点名称的已安装节点数
            get_node_total_number_by_nodename "${already_installed_node_name_key[${index}]}"
            already_installed_node_num=$?
            ## 扩容需要根据已安装节点数 + 要扩容的节点数，计算总节点。升级node_total_num初始是0.所以主要是获取已安装的节点数。
            node_total_num=$(( ${node_total_num} + ${already_installed_node_num} ))
            log_echo "INFO" "already_installed_node_num=${already_installed_node_num} node_total_num=${node_total_num}"
        fi
        
        ## 定制要判断的节点数大于配置的节点数，并且有其他条件other_check_ret=0时， 删除配置的TAG。
        if [ "${node_total_num}" -gt "${node_greater_than_number[${index}]}" -a ${other_check_ret} -eq 0 ];then
            log_echo "INFO" "The ${operation_type} index=${index} delete node tag list."
            ## 根据节点名称删除指定tag列表到指定json文件
            del_nodetag_list "${node_json_path}" "${node_name[${index}]}" "${delete_nodetag_list[${index}]}"
        else
            ## 条件不成立，不满足时，记录日志。不处理。
            log_echo "INFO" "The ${operation_type} index=${index} not need modify it."
        fi
    done
    log_echo "INFO" "custom_del_nodetag end."
}

## 根据不同场景，不同json文件，删除定制规则指定的TAG。
function custom_nodetag_json()
{
    ## 操作类型，install 安装，extend 扩容 ，upgrade 升级 
    local operation_type="$1"
    ## 需要修改的node json文件，根据条件判断后删除对应节点的node Tag.并修改该文件。
    local node_json_path="$2"
    ## 定制删除TAG的配置文件
    local custom_config_file="${PreSet_PATH}/Product_MergeClusterNetwork/custom_config/nodetag_custom_config.properties"
    log_echo "INFO" "custom_nodetag_json start..."
    log_echo "INFO" "operation_type=${operation_type} node_json_path=${node_json_path}"
    
    ## 参数是否为空检查。
    is_empty "operation_type" "${operation_type}"
    is_empty "node_json_path" "${node_json_path}"
    
    ## 参数值是否合法检查。
    if [ -z "$(echo ${operation_type} | grep -wE 'install|extend|upgrade')" ];then
        log_echo "ERROR" "operation_type=${operation_type} not in [ install|extend|upgrade ]."
        exit 1
    fi
    
    ## 文件是否存在检查 
    file_exists "${node_json_path}"
    file_exists "${custom_config_file}"
    
    ##安装和升级的配置文件
    local cfg_file_path="${PreSet_PATH}/DV_Config_MergeCluster.config"
    if [ "${operation_type}" == "extend" ];then
        ## 扩容的配置文件
        cfg_file_path="${PreSet_PATH}/DV_Config_Extend.config"
    fi
    
    ## 检查文件是否存在
    file_exists "${cfg_file_path}"
    
    ## 获取IP类型，并检查值是否合法。 ip_type = [ 0,1,2 ] ,get ip type
    local ip_type=$(cat ${cfg_file_path} |grep -v "^[[:blank:]]*#" |grep "^IPType=" |awk -F'=' '{print $2}')
    if [ -z "$(echo ${ip_type} | grep -wE '0|1|2')" ];then
        log_echo "ERROR" "ip_type=${ip_type} not in [ 0|1|2 ]."
        exit 1
    fi
    
    ##获取是否和DF合设参数值 
    local is_integrated_df_and_service=$(cat ${cfg_file_path} |grep -v "^[[:blank:]]*#" |grep "^is_integrated_df_and_service=" |awk -F'=' '{print $2}')
    if [ "${operation_type}" != "install" ];then
        ## 扩容和升级从导出的产品信息获取 
        local product_json=""
        [ "${operation_type}" == "extend" ] && product_json="${PreSet_PATH}/expInfo_extend/product_SOP.json"
        [ "${operation_type}" == "upgrade" ] && product_json="${PreSet_PATH}/tools/expInfo/product_SOP.json"
        ## 检查导出的产品信息json文件是否存在。
        file_exists "${product_json}"
        ## 从导出的产品信息json文件中获取是 否和DF合设参数值 
        is_integrated_df_and_service=$(cat ${product_json} |sed "s/,\"/,\n\"/g" |grep "\"is_integrated_df_and_service\""|awk -F'"' '{print $4}')
    fi
    
    log_echo "INFO" "operation_type=${operation_type} ip_type=${ip_type} node_json_path=${node_json_path} is_integrated_df_and_service=${is_integrated_df_and_service} cfg_file_path=${cfg_file_path}"
    
    ## 根据 ${custom_config_file} 文件定制内容，判断满足要求进行删除指定节点上的TAG。
    custom_del_nodetag "${custom_config_file}" "${operation_type}" "${is_integrated_df_and_service}" "${cfg_file_path}" "${node_json_path}" "${ip_type}"
    log_echo "INFO" "custom_nodetag_json End."
}

## 根据节点名称 获取节点的TAG列表
function get_nodetag_by_nodename()
{
    ## node json 文件路径。
    local json_path=$1
    ## 要获取的节点名称
    local nodename=$2
    
    log_echo "INFO" "get_nodetag_by_nodename start..."
    ## 文件是否存在检查
    file_exists "${json_path}"
    ## 参数检查，节点名称是否为空
    is_empty "nodename" "${nodename}"
    
    ## 节点TAG列表全局变量，初始为空。
    node_tag_value=""
    if [ "X$(whoami)" == "Xroot" ];then
        ## root场景，调用预置解压的自身python解析获取。
        node_tag_value=$(${PreSet_PATH}/UniEp/python/bin/python ${PreSet_PATH}/tools/handlejson.py "getnodetag_of_nodename" "${json_path}" "${nodename}")
    else
        ## 非root场景，使用环境平台已安装的python
        node_tag_value=$(source /opt/oss/manager/bin/engr_profile.sh;python ${PreSet_PATH}/tools/handlejson.py "getnodetag_of_nodename" "${json_path}" "${nodename}")
    fi
    ## 根据获取结果判断值是否为空或者ERROR。
    if [ -z "${node_tag_value}" -o "X${node_tag_value}" == "XERROR" ];then
        log_echo "ERROR" "get_nodetag_by_nodename by ${nodename} failed."
        exit 1
    fi
}

## 检查节点的TAG列表中是否存在要查找的TAG
function check_node_tag_exist()
{
    ## 要检查的节点TAG列表
    local node_tags="$1"
    ## 需要查找的TAG
    local find_tag="$2"
    
    log_echo "INFO" "check_node_tag_exist start."
    ## 参数是否为空检查。
    is_empty "node_tags" "${node_tags}"
    is_empty "find_tag" "${find_tag}"
    
    ## 查找节点TAG列表中是否包含要查找的TAG。有，返回对应值，没有返回空。
    local check_ret=$(echo "${node_tags}" |sed "s/,/\n/g" |grep "^${find_tag}$")
    log_echo "INFO" "The check_ret=${check_ret},of check_node_tag_exist."
    ## 空时，表示没找到，打印日志，返回1，表示false.
    if [ -z "${check_ret}" ];then
        log_echo "INFO" "The ${find_tag} node tag not exist."
        return 1
    fi
    ## 不为空，表示找到了，打印日志，返回0，表示true.
    log_echo "INFO" "The ${find_tag} node tag exist."
    return 0
}

## 根据节点名称，从节点信息json文件中，获取该节点的所有IP。
function get_node_ip_list_by_nodename()
{
    ## 节点名称 
    local node_name="$1"
    log_echo "INFO" "get_node_ip_list_by_nodename start.node_name=${node_name}"
    ## 参数是否为空检查。
    is_empty "node_name" "${node_name}"
    
    ## 节点信息json文件
    local node_list_json="/opt/oss/manager/etc/sysconf/nodelists.json"
    file_exists "${node_list_json}"
    
    ## 初始为空。
    node_ip_list=""
    if [ "X$(whoami)" == "Xroot" ];then
        ## root场景，调用预置解压的自身python解析获取。
        node_ip_list=$(${PreSet_PATH}/UniEp/python/bin/python ${PreSet_PATH}/tools/handlejson.py "get_node_ip_list_by_nodename" "${node_list_json}" "${node_name}")
    else
        ## 非root场景，使用环境平台已安装的python
        node_ip_list=$(source /opt/oss/manager/bin/engr_profile.sh;python ${PreSet_PATH}/tools/handlejson.py "get_node_ip_list_by_nodename" "${node_list_json}" "${node_name}")
    fi
    
    ## 根据获取结果判断值是否为空或者ERROR。
    if [ -z "${node_ip_list}" -o "${node_ip_list}" == "ERROR" ];then
        log_echo "ERROR" "get ${node_name} node_ip_list failed.of get_node_ip_list_by_nodename"
        exit 1
    fi
    log_echo "INFO" "get_node_ip_list_by_nodename End.node_ip_list=${node_ip_list}"
    return 0
}

## 根据传进去要查找的IP，检查IP是否包含在节点的IP列表中。
function is_contained()
{
    ## 某个节点的所有IP列表。
    local node_ip_list="$1"
    ## 要查询的IP 
    local check_ip="$2"
    log_echo "INFO" "is_contained start..."
    ## 参数是否为空检查。
    is_empty "node_ip_list" "${node_ip_list}"
    is_empty "check_ip" "${check_ip}"
    
    ## 对IPV6的IP做转换统一缩进格式，然后再查找匹配。
    ## 初始化临时参数
    local tmp_ipv6_simple=""
    local is_ipv6=""
    local tmp_ip=""
    local new_ip_list=""
    
    ## 循环该节点的IP列表。
    for tmp_ip in $(echo "${node_ip_list}"|sed "s#,# #g");
    do
        ## 判断IP是否为IPV6
        is_ipv6=$(echo "${tmp_ip}" |grep -F ":")
        if [ ! -z "${is_ipv6}" ];then
            ## 不为空，表示IPV6, 使用python对IP进行转换统一精简格式
            tmp_ipv6_simple=$(${PreSet_PATH}/UniEp/python/bin/python -c "import ipaddress;print(str(ipaddress.ip_address('${tmp_ip}')))")
            ## 转换获得新值，检查新值是否正确。
            echo ${tmp_ipv6_simple}|grep ":" > /dev/null
            if [ $? -ne 0 ];then
                log_echo "ERROR" "transform ipv6 to simple failed"
                exit 1
            fi
            ## 如果新IP列表变量为空，表示第一个，直接赋值。并继续 。
            [ -z "${new_ip_list}" ] && new_ip_list="${tmp_ipv6_simple}" && continue
            ## 如果 新IP列表变量不为空，表示不是第一个，后面的需要加逗号，然后追加。
            new_ip_list="${new_ip_list},${tmp_ipv6_simple}"
            continue
        fi
        
        ## 非IPV6的，即IPV4的走下面这里，ipv4的不需要转换。如果是第一个，直接赋值，然后继续。 
        [ -z "${new_ip_list}" ] && new_ip_list="${tmp_ip}" && continue
        ## 第二个开始，后面的需要加逗号，然后追加。
        new_ip_list="${new_ip_list},${tmp_ip}"
    done
    log_echo "INFO" "The new_ip_list=${new_ip_list}"
    
    ## 对要查找的IP也做相同处理，如果是IPV6，进行转换统一精简格式
    is_ipv6=$(echo "${check_ip}" |grep -F ":")
    if [ ! -z "${is_ipv6}" ];then
        ## 不为空，表示IPV6, 使用python对IP进行转换统一精简格式
        tmp_ipv6_simple=$(${PreSet_PATH}/UniEp/python/bin/python -c "import ipaddress;print(str(ipaddress.ip_address('${check_ip}')))")
        ## 转换获得新值，检查新值是否正确。
        echo ${tmp_ipv6_simple}|grep ":" > /dev/null
        if [ $? -ne 0 ];then
            log_echo "ERROR" "transform ipv6 to simple failed"
            exit 1
        fi
        ## 更新转换后的值。
        check_ip="${tmp_ipv6_simple}"
    fi
    ## IPv4不需要转换，直接使用。 
    log_echo "INFO" "The check_ip=${check_ip}"
    
    ## 检查IP是否包含在节点的IP列表中。
    local tmp_ip=$(echo "${new_ip_list}"|sed "s#,#\n#g" |grep "^${check_ip}$")
    if [ -z "${tmp_ip}" ];then
        ## 为空表示没找到，打印日志，返回1，表示false
        log_echo "INFO" "The ${check_ip} not in ${new_ip_list}."
        return 1
    fi
    ## 不为空，表示找到了，打印日志，返回0，表示true
    log_echo "INFO" "The ${check_ip} in ${new_ip_list}."
    return 0
}

## 根据不同场景，修改产品参数信息
function modify_product_json_by_key()
{
    ## 节点TAG列表
    local node_tag_list="$1"
    ## 节点的IP列表
    local node_ip_list="$2"
    ## 要查找的TAG
    local find_node_tag="$3"
    ## 要修改的参数key
    local parameter_key="$4"
    ## 要修改的产品信息json文件
    local product_json="$5"
    ## 操作类型 
    local operation_type="$6"
    log_echo "INFO" "modify_product_json_by_key start.node_tag_list=${node_tag_list} find_node_tag=${find_node_tag} parameter_key=${parameter_key} product_json=${product_json}"
    
    ## 参数是否为空检查。
    is_empty "node_tag_list" "${node_tag_list}"
    is_empty "find_node_tag" "${find_node_tag}"
    is_empty "parameter_key" "${parameter_key}"
    is_empty "product_json" "${product_json}"
    is_empty "node_ip_list" "${node_ip_list}"
    is_empty "operation_type" "${operation_type}"
    
    ## 检查节点TAG列表中是否还存在要查找的TAG
    check_node_tag_exist "${node_tag_list}" "${find_node_tag}"
    if [ $? -eq 0 ];then
        ## 返回0，表示true，表示存在，不需要修改该参数的IP 
        log_echo "INFO" "${node_tag_list} has ${find_node_tag},not need modify it."
        return 0
    fi
    
    ## 检查文件是否存在 
    file_exists "${product_json}"
    
    ## 是否删除参数值中匹配的IP, 0 表示要删除， 1 表示不删除。 
    local is_del=1
    ## 修改的新值，初始为空。
    local new_value=""
    ## 先获取该参数旧值。
    local tmp_value=$(get_current_value "${parameter_key}" "${product_json}")
    ## 初始化临时参数
    local tmp_ip_str=""
    local tmp_ip=""
    ## 根据获取到的这个参数值，循环判断每个IP是否匹配，是否需要删除。 
    for tmp_ip_str in $(echo "${tmp_value}"|sed "s#,# #g");do
        ## IPV6 加端口号 ，例如： [a:b:c]:11
        tmp_ip=$(echo "${tmp_ip_str}" |grep "\[\|\]")
        if [ ! -z "${tmp_ip}" ];then
            ## 不为空，表示IPV6 + 端口，包含中括号。通过中括号获取中间的IP.
            tmp_ip=$(echo "${tmp_ip_str}" |awk -F'[][]' '{print $2}')
            ## 判断IP是否为空 
            is_empty "tmp_ip" "${tmp_ip}"
            ## 判断IP是否为要查找的节点IP,是否包含在该节点的IP列表中，包含，返回0，表示true。不包含，返回1，表示false。
            is_contained "${node_ip_list}" "${tmp_ip}"
            is_del=$?
        else
            ## ipv4 或 ipv4 加端口号 , 例如：127.0.0.1 或 127.0.0.1:22
            tmp_ip=$(echo "${tmp_ip_str}" |grep -F ".")
            if [ ! -z "${tmp_ip}" ];then
                ## 如果有带端口号，只获取IP。如果没有端口号，也可以正确获取IP。
                tmp_ip=$(echo "${tmp_ip_str}" |awk -F':' '{print $1}')
                tmp_ip=$(echo "${tmp_ip}"|sed "s/\./\\\./g")
                ## 判断IP是否为要查找的节点IP
                tmp_ip=$(echo "${node_ip_list}"|sed "s#,#\n#g" |grep "^${tmp_ip}$")
                if [ ! -z "${tmp_ip}" ];then
                    ## 不为空，表示包含，表示找到了这个IP，就是要删除这个IP。直接跳过即可，不需要添加到保留列表的变量。
                    is_del=0
                else
                    is_del=1
                fi
            else
                ## ipv6 ，例如： a:b:c 
                tmp_ip=$(echo "${tmp_ip_str}" |grep -F ":")
                if [ ! -z "${tmp_ip}" ];then
                    ## 不为空，表示IPV6 
                    ## 判断IP是否为要查找的节点IP,是否包含在该节点的IP列表中，包含，返回0，表示true。不包含，返回1，表示false。
                    is_contained "${node_ip_list}" "${tmp_ip}"
                    is_del=$?
                else
                    ## 以上都不是，表示异常退出。
                    log_echo "ERROR" "This else.The tmp_ip_str=${tmp_ip_str}"
                    exit 1
                fi
            fi
        fi
        
        ## 判断IP是否需要删除。0，表示true，要删除。1，表示false，不删除。
        if [ ${is_del} -eq 0 ];then
            ## is_del=0，表示包含，表示找到了这个IP，就是要删除这个IP。直接跳过即可，不需要添加到保留列表的变量。
            log_echo "INFO" "The ${tmp_ip_str} delete it."
            continue
        fi
        
        ## is_del=非0，表示没找到，这个IP需要保留，添加到保留列表的变量中。
        log_echo "INFO" "The ${tmp_ip_str} add it."
        if [ -z "${new_value}" ];then
            ## 新值变量为空时，表示第一个，直接赋值就行。 
            new_value="${tmp_ip_str}"
            continue
        fi
        ## 新值不为空，表示第二个以上，需要加逗号，再追加。
        new_value="${new_value},${tmp_ip_str}"
    done
    log_echo "INFO" "The new_value=${new_value}"
    
    ## 根据不同场景和不同json文件，更新对应场景的json文件，对应参数更新为修改后的新值
    if [ "${operation_type}" == "extend" ];then
        update_value_product "${parameter_key}" "${new_value}"
    elif [ "${operation_type}" == "upgrade" ];then
        productext_modify "${product_json}" "${parameter_key}" "${new_value}"
    else
        log_echo "INFO" "The operation_type=${operation_type}"
    fi
    return 0
}

## 扩容和升级场景，对节点信息和产品信息参数修改。
function modify_node_and_product_json()
{
    log_echo "INFO" "modify_node_and_product_json start..."
    local operation_type="$1"
    local node_json="$2"
    local product_json="$3"
    
    ## 参数是否为空检查。
    is_empty "operation_type" "${operation_type}"
    is_empty "node_json" "${node_json}"
    is_empty "product_json" "${product_json}"
    
    ## 检查文件是否存在 
    file_exists "${node_json}"
    file_exists "${product_json}"
    
    ## 根据定制配置场景，删除节点1-3上的TAG。
    custom_nodetag_json "${operation_type}" "${node_json}"
    
    ## 对定制配置的参数，找对应节点有没有TAG，没有，就在参数中删除对应节点IP。定制配置文件
    local custom_config_file="${PreSet_PATH}/Product_MergeClusterNetwork/custom_config/parameter_of_nodetag_config.properties"
    ## 检查文件是否存在 
    file_exists "${custom_config_file}"
    
    ## 文件格式转换和引用配置文件参数。
    dos2unix ${custom_config_file}
    source ${custom_config_file}
    
    ## 初始化临时参数
    local index=1
    local node_name=""
    local node_tags=""
    local node_ips=""
    ## custom_count 来着于 ${custom_config_file} 中配置的总数，根据配置的定制参数循环。
    for index in $(seq 1 ${custom_count});
    do
        ## 判断对应索引配置参数是否为空，为空报错退出。
        is_empty "parameter_key[${index}]" "${parameter_key[${index}]}"
        is_empty "nodetag_name[${index}]" "${nodetag_name[${index}]}"
        is_empty "node_name_list[${index}]" "${node_name_list[${index}]}"
        ## 根据配置的节点名称，循环修改。
        for node_name in $(echo "${node_name_list[${index}]}" | sed "s/,/ /g");
        do
            [ -z "${node_name}" ] && continue
            ## 获取修改后的节点的TAG
            get_nodetag_by_nodename "${node_json}" "${node_name}"
            node_tags=${node_tag_value}
            log_echo "INFO" "The node_name=${node_name} node_tags=${node_tags} of node_json=${node_json}"
            
            ## 获取指定节点的IP列表，获取对应参数，如果存在该IP，则删除。 
            ## 根据节点的名称，获取节点的所有IP 
            get_node_ip_list_by_nodename "${node_name}"
            node_ips="${node_ip_list}"
            log_echo "INFO" "The node_name=${node_name} node_ips=${node_ips}"
            
            ## 根据节点TAG是否存在，不存在时，删除对应这个参数的对应这个节点的IP，IP因为有多个，需要查找匹配。
            modify_product_json_by_key "${node_tags}" "${node_ips}" "${nodetag_name[${index}]}" "${parameter_key[${index}]}" "${product_json}" "${operation_type}"
        done
    done
    log_echo "INFO" "modify_node_and_product_json End."
}

function add_PM_features()
{
    if [ "`echo ${enablePMServiceMode}|tr 'a-z' 'A-Z'`" == "TRUE" ];then
        features=$(${PreSet_PATH}/UniEp/python/bin/python ${PreSet_PATH}/tools/handlejson.py "getinfo_product" "${json_tmp_path}/product_sop.json" "features")
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Get features failed"
            exit 1
        fi
        echo $features|grep DVPM
        if [ $? -ne 0 ];then
            ${PreSet_PATH}/UniEp/python/bin/python ${PreSet_PATH}/tools/handlejson.py "productext_modify" "${json_tmp_path}/product_sop.json" "features" "${features};DVPM"
        fi
        if [ $? -ne 0 ];then
            log_echo "ERROR" "add PM features failed"
            exit 1
        fi
    fi
}
function mount_local_pm_db()
{
    local config_file_tmp="$1"
    if [ -n "${config_file_tmp}" ]; then
        cp -rpf ${config_file_tmp}  ${tmp_dir}
    else
        cp -rpf ${PreSet_PATH}/Common/DV_config.properties.tmp  ${tmp_dir}
    fi

    sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount PMNODE0DB1 
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Auto mount disk at Primary node failed, please check..."
        exit 1
    fi
    sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount PMNODE0DB2
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Auto mount disk at Primary node  failed, please check..."
        exit 1
    fi
    
    sh ${tmp_dir}/DV_PreSet_APP.sh modify_hang_handler_conf "${is_integrated_df_and_service}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "modify_hang_handler_conf at Primary node  failed, please check..."
        exit 1
    fi
    
    cat ${tmp_dir}/local_check_disk.txt > ${PreSet_PATH}/PMNODE0_local_check_disk.txt
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Get instance disk failed on local, please check"
        exit 1
    fi
}
function mount_remote_pm_db()
{
    local node_ip=$1
    local node_pwd=$2
    local node_number=$3
    local tmp_file=$4
    if [ "X${node_pwd}" == "X" -a "X${root_password}" != "X" ];then
        log_echo "INFO" "PreInstall get ${oper_user} pwd of mount_remote_pm_db"
        node_pwd="${root_password}"
    fi
    ###Copy the required configuration files and scripts.
    echo ${node_ip}|grep ":" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        local scp_remote_ip="\[${node_ip}\]"
    else
        local scp_remote_ip=${node_ip}
    fi
    
    if [ -z "${tmp_file}" ];then
        tmp_file="${PreSet_PATH}/Common/DV_config.properties.tmp"
    fi
    if [ ! -f ${tmp_file} ];then
        cp -prf ${PreSet_PATH}/Common/DV_config.properties  ${tmp_file}
        replace_file_macro ${tmp_file} ${source_file}
    fi
    
    auto_scp "${node_pwd}" "${tmp_file}"  "${oper_user}@${scp_remote_ip}:${tmp_dir}"
    
    local scene_type=""
    if [ ${node_number} -ge 2 ];then
        scene_type="SMExtendCluster$((${node_number} - 1))"
        [ "${is_old_extend_mode}" == "Yes" ] && scene_type="DVExtendCluster$((${node_number} - 1))"
    fi
    
    ###Run the automatic mounting command.
    auto_smart_ssh "${node_pwd}" "${oper_user}@${node_ip} sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount PMNODE${node_number}DB1 ${scene_type}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Auto mount disk to instance PMNODE${node_number}DB1 at node ${node_ip} failed, please check..."
        exit 1
    fi
    auto_smart_ssh "${node_pwd}" "${oper_user}@${node_ip} sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount PMNODE${node_number}DB2 ${scene_type}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Auto mount disk to instance PMNODE${node_number}DB2 at node ${node_ip} failed, please check..."
        exit 1
    fi
    
    auto_smart_ssh "${node_pwd}" "${oper_user}@${node_ip} sh ${tmp_dir}/DV_PreSet_APP.sh modify_hang_handler_conf ${is_integrated_df_and_service}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "modify_hang_handler_conf at node ${node_ip} failed, please check..."
        exit 1
    fi
    
    auto_smart_ssh "${node_pwd}" "${oper_user}@${node_ip} cat ${tmp_dir}/local_check_disk.txt" > ${PreSet_PATH}/PMNODE${node_number}_local_check_disk.txt
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Get instance disk failed on ${node_ip}, please check"
        exit 1
    fi
    
}
function mount_merge_pm_db()
{
    ip_list=$1
    pwd_list=$2
    value=$3
    ####mount database instance disk of extended node. Extend node index is start from 2.
    if [ "X${ip_list}" != "X" -a "X${ip_list}" != "X{{${value}}}" ];then
        x=1
        y=0
        for a in $(echo "${ip_list}" | sed "s#,# #g")
        do
            ((x++))
            mount_remote_pm_db "${a}" "${pwd_list[$y]}" "${x}"
            ((y++))
        done
    fi  
}


function get_node_memifo {

    local ssh_ip="$1"
    local user_name="$2"
    local user_pwd="$3"
    local node_role="$4"
    log_echo "INFO" "The ${ssh_ip} node_role=${node_role}"
    grep -Ew "${ssh_ip}=[0-9]+"  ${PreSet_PATH}/nodes_meminfo.txt > /dev/null 2>&1
    if [ $? -eq 0 ];then
        return 0
    fi
    ## kb to GB  (1048576=1024*1024)
    if [ "X${node_role}" == "XPrimary" ];then
        node_memtotal=$(awk '/^MemTotal/ { printf("%d", $2/1048576) }' /proc/meminfo)
    else
        if [ "X${user_pwd}" == "X" ];then
            if [ "X${root_password}" == "X" ];then
                log_echo "ERROR" "The ssh password for ${ssh_ip} is null, please check"
                exit 1
            fi
            user_pwd=${root_password}
        fi
        
        if [ -f /.dockerenv ];then
            local retry_times=30
            local try_count=1
            for try_count in $(seq 1 ${retry_times});do
                auto_smart_ssh ${user_pwd} "${user_name}@${ssh_ip} echo ssh_ok"
                if [ $? -ne 0 ];then
                    log_echo "INFO" "ssh ${user_name}@${ssh_ip} failed , try again try_count=${try_count}"
                    sleep 2
                else
                    log_echo "INFO" "ssh ${user_name}@${ssh_ip} successfully"
                    break
                fi
            done
            
            if [ ${try_count} -eq ${retry_times} ];then
                log_echo "ERROR" "ssh ${user_name}@${ssh_ip} failed, please check."
                exit 1
            fi
        fi

        auto_smart_ssh "${user_pwd}" "${user_name}@${ssh_ip} grep --color=none '^MemTotal' /proc/meminfo" > ${PreSet_PATH}/node_memtotal.txt
        node_memtotal=$(awk '/^MemTotal/ { printf("%d", $2/1048576) }' ${PreSet_PATH}/node_memtotal.txt)
    fi
    if [ -z "${node_memtotal}" ];then
        log_echo "ERROR" "The ${ssh_ip}'s node_memtotal get is null, please check"
        exit 1
    fi
    local isNumber=$(echo "${node_memtotal}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        log_echo "ERROR" "The ${ssh_ip}'s node_memtotal is not number, please check"
        exit 1
    fi
    log_echo "INFO" "The ${ssh_ip} node_memtotal=${node_memtotal}"
    echo "${ssh_ip}=${node_memtotal}" >> ${PreSet_PATH}/nodes_meminfo.txt

}

function rebuild_tmp_cfg_file_disk()
{
    local tmp_cfg_file=$1
    local pre_disk_map_file=${UTILS_PATH}/pre_get_disk_symbol.properties
    if [ ! -f ${tmp_cfg_file} ]; then
        log_echo "ERROR" "The tmp_cfg_file=${tmp_cfg_file} is not exist, please check."
        return 1
    fi

    if [ "X${pre_install_key}" == "X" -a "X${Digitalization_Disk_Param_Tag}" != "XYES" ]; then
        log_echo "INFO" "The install scene is not preinstall, not need to rebuild ${dv_cfg_file}.tmp disk."
        return 0
    fi
    log_echo "INFO" "Begin to rebuild disk parameters to placeholders..."
    local disk_name_val=""
    while read -r line; do
        disk_name_val=$(echo "${line}" | awk -F"=" '{print $2}')
        if [ -z "${disk_name_val}" ]; then
            continue
        fi
        if [ "X${disk_name_val}" == "XEXTEND_DISK" ]; then
            continue
        fi
        grep "^${disk_name_val}=" ${tmp_cfg_file}
        if [ $? -ne 0 ]; then
            continue
        fi
        update_config "${tmp_cfg_file}" "${disk_name_val}" "{{${disk_name_val}}}" || exit 1
    done < ${pre_disk_map_file}
    source ${tmp_cfg_file}
    if [ $? -ne 0 ]; then
        log_echo "ERROR" "Source file=${tmp_cfg_file} failed, please check."
        return 1
    fi
    return 0
}

function modify_features()
{
    local pm_nodes_number=$1
    local modify_key=$2
    local json_file=$3       
    
    log_echo "INFO" "Begin to modify_features..."
    if [ -z "${pm_nodes_number}" -o -z "${modify_key}" -o -z "${json_file}" ];then
        log_echo "ERROR" "The pm_nodes_number=${pm_nodes_number} or modify_key=${modify_key} or json_file=${json_file} has null."
        exit 1
    fi
    
    if [ ! -f ${json_file} ];then
        log_echo "ERROR" "The json_file=${json_file} is not exists."
        exit 1
    fi

    local is_number=$(echo "${pm_nodes_number}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${is_number}" == "No" ];then
        log_echo "ERROR" "The pm_nodes_number=${pm_nodes_number} is not number."
        exit 1
    fi
    
    log_echo "INFO" "The pm_nodes_number=${pm_nodes_number} modify_key=${modify_key} json_file=${json_file}"
    local tmp_number=$(expr ${pm_nodes_number} / 2)

    if [ ${tmp_number} -ge 1 ];then
        local pms_str=""
        local check_ret=$(grep -w "features" ${json_file} |grep -w "DVMediation")
        if [ -z "${check_ret}" ];then
            pms_str="DVMediation"
        fi
        
        ## 物理机大规格场景下，DVPM1和DVPM2默认勾选，因此物理机需要加上2.从2以上开始。
        local tmp_star_number=2
        ## 两个节点为一个集群，需要从1开始。
        local tmp_pm_index=1
        for tmp_pm_index in $(seq 1 ${tmp_number});do
            check_ret=$(echo "${pms_str}" |grep -w "DVPM${tmp_pm_index}")
            if [ ! -z "${check_ret}" -a "X${zenith_paramgroup_file}" != "XlargeCapacity" ];then
                continue
            fi
            
            if [ "${is_old_extend_mode}" == "Yes" ];then
                local node1=$((${tmp_pm_index} * 2-1))  ## node1
                local node2=$(( ${node1} +1))   ## node2 
                local node1_disk_exit=1
                local node2_disk_exit=1
                local pmnode1=$((${tmp_pm_index} * 2))
                local pmnode2=$(( ${pmnode1} +1))
                local tmp_file_exists=0
                if [ -f ${PreSet_PATH}/DVExtendCluster${node1}_local_check_disk.txt -a -f ${PreSet_PATH}/DVExtendCluster${node2}_local_check_disk.txt ];then
                    dos2unix ${PreSet_PATH}/DVExtendCluster${node1}_local_check_disk.txt
                    dos2unix ${PreSet_PATH}/DVExtendCluster${node2}_local_check_disk.txt
                    node1_disk_exit=$(grep "local_check_node_disk_ret" ${PreSet_PATH}/DVExtendCluster${node1}_local_check_disk.txt | awk -F "=" '{print $2}') 
                    node2_disk_exit=$(grep "local_check_node_disk_ret" ${PreSet_PATH}/DVExtendCluster${node2}_local_check_disk.txt  | awk -F "=" '{print $2}')
                    tmp_file_exists=1
                fi
                
                if [ -f ${PreSet_PATH}/PMNODE${pmnode1}_local_check_disk.txt -a -f ${PreSet_PATH}/PMNODE${pmnode2}_local_check_disk.txt ];then
                    dos2unix ${PreSet_PATH}/PMNODE${pmnode1}_local_check_disk.txt
                    dos2unix ${PreSet_PATH}/PMNODE${pmnode2}_local_check_disk.txt
                    node1_disk_exit=$(grep "local_check_instance_disk_ret" ${PreSet_PATH}/PMNODE${pmnode1}_local_check_disk.txt | awk -F "=" '{print $2}')  
                    node2_disk_exit=$(grep "local_check_instance_disk_ret" ${PreSet_PATH}/PMNODE${pmnode2}_local_check_disk.txt | awk -F "=" '{print $2}')
                    tmp_file_exists=1
                fi
                
                ## 1. 合设集群， 文件有其中1个存在， 自动挂盘开启(AUTO_DISK_MOUNT 容器化pod里面模式是关的)，  PM_DB_DISK 或 PHYSICAL_PM_DB_DISK1 其中一个不为空。 
                if [ "X${netWorkType}" == "XM" -a ${tmp_file_exists} -eq 0 ] && ([ ! -z "${PM_DB_DISK}" -a "X${PM_DB_DISK}" != "X{{PM_DB_DISK}}" ] || [ ! -z "${PHYSICAL_PM_DB_DISK1}" -a "X${PHYSICAL_PM_DB_DISK1}" != "X{{PHYSICAL_PM_DB_DISK1}}" ]);then
                    log_echo "ERROR" "${PreSet_PATH}/DVExtendCluster${node1}_local_check_disk.txt or ${PreSet_PATH}/DVExtendCluster${node2}_local_check_disk.txt or ${PreSet_PATH}/PMNODE${pmnode1}_local_check_disk.txt or ${PreSet_PATH}/PMNODE${pmnode2}_local_check_disk.txt is not exist, please check"
                    exit 1
                fi
                
                log_echo "INFO" "node1_disk_exit is ${node1_disk_exit},node2_disk_exit is ${node2_disk_exit}. "
            fi
            
            log_echo "INFO" "netWorkType=${netWorkType}"
            if [ "X${netWorkType}" == "XM" ];then
                ## 虚拟机场景，tmp_pm_index 超过6不需要勾选特性了。可以退出循环。
                if [ ${tmp_pm_index} -gt 6 ];then
                    log_echo "INFO" "tmp_pm_index=${tmp_pm_index},Virtual Machine Scenario.Only support PM1 to PM6, skip adding PMx. "
                    break
                fi
                ##虚拟机或物理机合设场景下，安装场景从DVPM1开始
                tmp_features_list="DVPM${tmp_pm_index}"
                if [ "X${zenith_paramgroup_file}" == "XlargeCapacity" ];then
                    ## 物理机大规格场景下，tmp_pm_index 超过2不需要勾选特性了。可以退出循环。
                    if [ ${tmp_pm_index} -gt 2 ];then
                        log_echo "INFO" "tmp_pm_index=${tmp_pm_index},Large-specification physical machine.Only support PM1 to PM6, skip adding PMx. "
                        break
                    fi
                    
                    ## 物理机大规格场景下，DVPM1和DVPM2默认勾选，因此此处要从DVPM3开始
                    ## tmp_pm_index=1  tmp_star_number=3  tmp_end_number=4
                    ## tmp_pm_index=2  tmp_star_number=5  tmp_end_number=6
                    tmp_star_number=$(( ${tmp_star_number} + ${tmp_pm_index} ))
                    tmp_end_number=$(( ${tmp_star_number} + 1 ))
                    log_echo "INFO" "tmp_star_number=${tmp_star_number} tmp_end_number=${tmp_end_number}"
                    tmp_features_list="DVPM${tmp_star_number};DVPM${tmp_end_number}"
                fi
                
                ## 旧安装模式。
                if [ "${is_old_extend_mode}" == "Yes" ];then
                    log_echo "INFO" "The node1_disk_exit=${node1_disk_exit} node2_disk_exit=${node2_disk_exit}"
                    if [ ${node1_disk_exit} -eq 0 -a ${node2_disk_exit} -eq 0 ];then
                        log_echo "INFO" "The is_old_extend_mode=${is_old_extend_mode} and node1_disk_exit=${node1_disk_exit} node2_disk_exit=${node2_disk_exit}"
                    else
                        # 安装，扩容自动勾选问题。通过判断是否有磁盘来判断是否勾选。
                        # 场景1： 所有节点都找到对应磁盘。勾选。
                        # 场景2： 所有节点都没找到磁盘，不勾选。 
                        # 场景3： 前2个没找到，后2个找到了。 全部不勾选，后面的依赖前面。前面没找到就都不勾选。但是后2个找到的盘会挂载。
                        # 场景4： 前面2个找到了，后面2个没找到。 只勾选前面找到的2个。后面没找到的2个不勾选。
                        # 场景5： （虚拟机）前面2个找到了，中间2个没找到，后面2个找到了， 只勾选前面找到的2个。后面依赖中间的，中间没找到后就退出。同3.
                        log_echo "INFO" "has node disk not exists.not need select features."
                        break
                    fi
                fi
                
                for tmp_features in $(echo "${tmp_features_list}"|sed "s/;/ /g");
                do
                    check_ret=$(echo "${pms_str}" |grep -w "${tmp_features}")
                    if [ -z "${check_ret}" ];then
                        pms_str="${pms_str};${tmp_features}"
                    fi
                done
            else
                if [ ${tmp_pm_index} -gt 6 ];then
                    log_echo "INFO" "tmp_pm_index=${tmp_pm_index},Only support PM1 to PM6, skip adding PMx"
                else
                    log_echo "INFO" "tmp_pm_index=${tmp_pm_index}"
                    pms_str="${pms_str};DVPM${tmp_pm_index}"
                fi
            fi
        done
        
        if [ ! -z "${pms_str}" ];then
            sed -i "/features/ s/${modify_key}/${modify_key};${pms_str}/" ${json_file}
            check_ret=$(grep -w "features" ${json_file} |grep -w "${pms_str}")
            log_echo "INFO" "The check_ret=${check_ret}"
            if [ -z "${check_ret}" ];then
                log_echo "ERROR" "modify ${json_file} add pms_str=${pms_str} to features failed."
                exit 1
            fi
        fi
    fi
    log_echo "INFO" "modify_features successfully."
}

function concurrent_number()
{
    local node_ip_list="$1"
    log_echo "INFO" "concurrent_number node_ip_list=${node_ip_list}"
    all_node_list=""
    node_ip_list=$(echo "${node_ip_list}"| sed "s# ##g")
    local node_num=$(echo "${node_ip_list}"| sed "s#,# #g"| awk '{print NF}')
    local concurrent_node_number=15
    if [ ${node_num} -le ${concurrent_node_number} ];then
        all_node_list="${node_ip_list}"
        log_echo "INFO" "The all_node_list=${all_node_list}."
        return 1
    fi
    
    local count=2
    local remainder_number=$(( ${node_num} % ${count} ))
    local tmp_number=$(( ${node_num} / ${count} ))
    local str_len=$(echo "${node_ip_list}"| awk '{print length($1)}')
    local last_str=""
    local last_end=1
    if [ ${remainder_number} -ge 1 ];then
        local start_index=$(echo "${node_ip_list}" | grep -b -o , |tail -${remainder_number} |head -1 |awk -F':' '{print $1}')
        start_index=$(( ${start_index} + 2 ))
        [ ${start_index} -gt ${str_len} ] && str_len=${start_index}
        last_str=$(echo "${node_ip_list}" | cut -c ${start_index}-${str_len})
    else
        last_end=$(echo "${node_ip_list}" | grep -b -o , |tail -1 |awk -F':' '{print $1}')
    fi
    
    local tmp_index=0
    start_index=1
    local end_index=1
    local tmp_ip=""
    local count_num=0
    while [ ${count_num} -lt ${count} ];
    do
        tmp_index=$((${tmp_index} + ${tmp_number}))
        end_index=$(echo "${node_ip_list}" | grep -b -o , |head -${tmp_index} |tail -1 |awk -F':' '{print $1}' )
        [ ${last_end} -eq ${end_index} ] && end_index=${str_len}
        [ ${start_index} -gt ${end_index} ] && end_index=${start_index}
        tmp_ip=$(echo "${node_ip_list}" | cut -c ${start_index}-${end_index})
        ((count_num++))
        start_index=$(( ${end_index} + 2 ))
        if [ -z "${all_node_list}" ];then
            all_node_list="${tmp_ip}"
            continue
        fi
        all_node_list="${all_node_list} ${tmp_ip}"
    done
    [ ! -z "${last_str}" ] && all_node_list="${all_node_list},${last_str}"
    log_echo "INFO" "The all_node_list=${all_node_list} tmp_number=${tmp_number}"
    return ${tmp_number}
}

function node_list_preset()
{
    local operation="$1"
    local node_ip_list=$2
    index_x=$3
    index_y=$4
    local node_type="$5"
    log_echo "INFO" "node_list_preset operation=${operation} node_ip_list=${node_ip_list} index_x=${index_x} index_y=${index_y} start..."
    
    if [ "${operation}" == "extend_Preset_Host" -o "${operation}" == "Preset_Host" ];then
        local podextend_ip_list=($6)
    fi
    
    if [ -f ${PRESET_NODE_TMP_LOG} ];then
        CONCURRENT_LOG=${PRESET_NODE_TMP_LOG}
    else
        CONCURRENT_LOG=${PreSet_PATH}/node_preset_concurrent.log
        TEMP_LOG_FILE=${LOG_FILE}
        LOG_FILE=${CONCURRENT_LOG}
        echo "" > ${CONCURRENT_LOG}
    fi
    echo "" > ${pid_list_file}
    
    for tmp_ip in $(echo "${node_ip_list}" | sed "s#,# #g")
    do
        ((index_x++))
        if [ "${operation}" == "APP_PreSet" ];then
            APP_PreSet "${tmp_ip}" "${extend_node_pwd_list[$index_y]}" "${node_type}${index_x}" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        elif [ "${operation}" == "Extend_APP_PreSet" ];then
            Extend_APP_PreSet "${tmp_ip}" "${extend_node_pwd_list[$index_y]}" "${node_type}${index_x}" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        elif [ "${operation}" == "pre_handle_managed_host" ];then
            pre_handle_managed_host "${node_type}${index_x}" "${tmp_ip}" "${extend_node_pwd_list[$index_y]}" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        elif [ "${operation}" == "mount_merge_pm_db" ];then
            mount_remote_pm_db "${tmp_ip}" "${extend_node_pwd_list[$index_y]}" "${index_x}" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        elif [ "${operation}" == "check_and_add_node_to_k8s" ];then
            check_and_add_node_to_k8s "${tmp_ip}" "${extend_node_pwd_list[$index_y]}" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        elif [ "${operation}" == "extend_first_login" ];then
            Preset_Host "DVExtendCluster${index_x}" "${tmp_ip}" "${extend_node_pwd_list[$index_y]}" "" "extend_first_login" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        elif [ "${operation}" == "extend_Preset_Host" ];then
            log_echo "INFO" "extend_Preset_Host operation=${operation} ${node_type}${index_x} hostip=${tmp_ip} podip=${podextend_ip_list[$index_y]} index_y=${index_y}"
            Preset_Host "${node_type}${index_x}" "${tmp_ip}" "${extend_node_pwd_list[$index_y]}" "${podextend_ip_list[$index_y]}" "Preset_Host" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        elif [ "${operation}" == "Preset_Host" ];then
            log_echo "INFO" "extend_Preset_Host operation=${operation} ${node_type}${index_x} hostip=${tmp_ip} podip=${podextend_ip_list[$index_y]} index_y=${index_y}"
            Preset_Host "${node_type}${index_x}" "${tmp_ip}" "${extend_node_pwd_list[$index_y]}" "${podextend_ip_list[$index_y]}" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        elif [ "${operation}" == "check_tag_and_wait" ];then
            Preset_Host "${node_type}${index_x}" "${tmp_ip}" "${extend_node_pwd_list[$index_y]}" "" "check_tag_and_wait" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        elif [ "${operation}" == "er_pwd_to_sop_by_ip" ];then
            er_pwd_to_sop_by_ip "${tmp_ip}" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        elif [ "${operation}" == "get_ossuser_sshkey_by_ip" ];then
            get_ossuser_sshkey_by_ip "${tmp_ip}" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        elif [ "${operation}" == "update_ossuser_sshkey_by_ip" ];then
            update_ossuser_sshkey_by_ip "${tmp_ip}" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        elif [ "${operation}" == "check_ip_ping_concurrent" ];then
            check_ip_ping_concurrent "${tmp_ip}" "${node_type}" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        elif [ "${operation}" == "add_docker_user" ];then
            add_docker_user "${tmp_ip}" "${extend_node_pwd_list[$index_y]}" &
            echo "${tmp_ip}=$!" >> ${pid_list_file}
        else
            log_echo "INFO" "This else, operation=${operation}"
        fi
        ((index_y++))
    done
    
    check_task_status "${pid_list_file}" "${CONCURRENT_LOG}" "1800"
    local check_task_status_ret=$?
    if [ ! -f ${PRESET_NODE_TMP_LOG} ];then
        LOG_FILE=${TEMP_LOG_FILE}
        cat ${CONCURRENT_LOG} >> ${LOG_FILE}
    fi
    
    if [ ${check_task_status_ret} -ne 0 ];then
        local startLine=$(grep -n "node_list_preset operation" ${CONCURRENT_LOG} |head -1 |awk -F':' '{print $1}')
        local endLine=$(wc -l < ${CONCURRENT_LOG})
        sed -n "${startLine},${endLine}p" ${CONCURRENT_LOG} |grep -w "ERROR" |tee -a ${LOG_FILE}
        [ ${check_task_status_ret} -eq 1 ] && log_echo "ERROR" "The check_task_status monitor has pid time out.For more details, see log:${CONCURRENT_LOG}"
        [ ${check_task_status_ret} -eq 2 ] && log_echo "ERROR" "The check_task_status monitor ${error_ip_list} execute failed.For more details, see log:${CONCURRENT_LOG}"
        exit 1
    fi
    
    if [ "${operation}" == "APP_PreSet" -o "${operation}" == "Extend_APP_PreSet" ];then
        for tmp_ip in $(echo "${node_ip_list}" | sed "s#,# #g")
        do
            record_all_nic_by_ip "${tmp_ip}"
        done
    fi
    log_echo "INFO" "node_list_preset operation=${operation} node_ip_list=${node_ip_list} End."
}

function node_preset_concurrent()
{
    local operation="$1"
    local extend_node_ip_list=$2
    extend_node_pwd_list=($3)
    log_echo "INFO" "node_preset_concurrent operation=${operation} start..."
    
    if [ -z "${extend_node_ip_list}" ];then
        log_echo "INFO" "node_preset_concurrent extend_node_ip_list=${extend_node_ip_list} is Null,not need do it.."
        return 0
    fi
    
    node_list_preset_dir=${PreSet_PATH}/node_list_preset
    pid_list_file=${node_list_preset_dir}/node_list_pid_list.txt
    mkdir -p ${node_list_preset_dir}
    
    concurrent_number "${extend_node_ip_list}"
    
    if [ "${operation}" == "Extend_APP_PreSet" ];then
        local node_type="$4"
        local start_idx=$5
        init_local_memtotal
        index_x=0
        [ ! -z "${start_idx}" ] && index_x=$start_idx
        index_y=0
        for node_list in ${all_node_list};
        do
            node_list_preset "${operation}" "${node_list}" "${index_x}" "${index_y}" "${node_type}"
        done
        log_echo "INFO" "node_preset_concurrent operation=${operation} End."
        return 0
    fi
    
    if [ "${operation}" == "mount_merge_pm_db" ];then
        index_x=1
        index_y=0
        for node_list in ${all_node_list};
        do
            node_list_preset "${operation}" "${node_list}" "${index_x}" "${index_y}"
        done
        log_echo "INFO" "node_preset_concurrent operation=${operation} End."
        return 0
    fi
    
    if [ "${operation}" == "extend_Preset_Host" ];then
        local node_type="$4"
        local podextend_ip_list=($5)
        local start_idx=$6
        index_x=0
        [ ! -z "${start_idx}" ] && index_x=$start_idx
        index_y=0
        for node_list in ${all_node_list};
        do
            node_list_preset "${operation}" "${node_list}" "${index_x}" "${index_y}" "${node_type}" "${podextend_ip_list[*]}"
        done
        log_echo "INFO" "node_preset_concurrent operation=${operation} End."
        return 0
    fi
    
    if [ "${operation}" == "Preset_Host" ];then
        local node_type="$4"
        local pod_ip_list=($5)
        index_x=0
        index_y=0
        for node_list in ${all_node_list};
        do
            node_list_preset "${operation}" "${node_list}" "${index_x}" "${index_y}" "${node_type}" "${pod_ip_list[*]}"
        done
        log_echo "INFO" "node_preset_concurrent operation=${operation} End."
        return 0
    fi
    
    local node_type="$4"
    index_x=0
    index_y=0
    for node_list in ${all_node_list};
    do
        node_list_preset "${operation}" "${node_list}" "${index_x}" "${index_y}" "${node_type}"
    done
    log_echo "INFO" "node_preset_concurrent operation=${operation} End."
    return 0
}


function config_ntp_segregate_node()
{
    if [ "X${NFV_MODE}" == "XON" ];then
        log_echo "INFO" "config ntp segregate node for app node"
        echo '{"nodeID" : ["1","2"]}' > ${UTILS_PATH}/custom_ntp_node.json 
        chown ossadm:ossgroup ${UTILS_PATH}/custom_ntp_node.json
        su - ossadm -c "/opt/oss/manager/apps/UniEPAgent/bin/uniep_tool osconfig-ntp set_custom_ntp_node -file ${UTILS_PATH}/custom_ntp_node.json"
    fi  
}

function get_disk_size_from_config_string() {
    local key="$1"

    local config_string="${Configuration_String}"
    if [ "X${Digitalization_Disk_Param_Tag}" == "XYES" ]; then
        config_string="${digital_disk_param_configstring}"
    fi

    RETURN_DISK_SIZE=$(echo "${config_string}" | grep -ow "${key}:\([0-9]\+\)" | sed "s#.*${key}:\([0-9]\+\).*#\1#g" | tail -1)
    if [ -z "${RETURN_DISK_SIZE}" ];then
        log_echo "INFO" "The key=${key} is not found at config_string."
        return 0
    fi
    ## unit G to M
    RETURN_DISK_SIZE=$((RETURN_DISK_SIZE*1024))
    log_echo "INFO" "get RETURN_DISK_SIZE=$RETURN_DISK_SIZE from config_string."
}

function summarize_all_node_size()
{
    local ip_list="$1"
    local pwd_list=($2)
    local pm_exist_num="$3"
    [ -z "$pm_exist_num" ] && pm_exist_num=0

    log_echo "INFO" "begin to summarize all pm nodes /opt/zenith size..."
    local index=0
    local index_ip=""
    for index_ip in $(echo ${ip_list} | sed 's#,# #g');
    do
        ((index++))
        if [ `expr $index % 2` -eq 0 ]; then
            continue
        fi
        local pm_cluster_num=`expr $(($index + 1)) / 2`
        pm_cluster_num=$(($pm_exist_num + $pm_cluster_num))
        auto_smart_ssh "${pwd_list[$index]}" "${oper_user}@${index_ip} df -Pm /opt/zenith |awk -F' ' '{print \\\$4}' |sed '1,1d' |sed 's/M//'|awk -F'.' '{print \\\$1}'|head -n 1|sed 's| ||g' | xargs -i echo pm$pm_cluster_num={}" >>$pm_zenith_size_file
    done
}

function get_pm_data_db_size()
{
    local pm_key="$1"
    local pm_index="$2"
    RETURN_DATA_SIZE=""
    if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ];then
        if [ "X${zenith_paramgroup_file}" == "XlargeCapacity" ];then
            if [ `expr $pm_index % 2` -eq 0 ]; then
                RETURN_DATA_SIZE=$CONFIG_STRING_PM_DB_2_SIZE
            else
                RETURN_DATA_SIZE=$CONFIG_STRING_PM_DB_1_SIZE
            fi
        else
           RETURN_DATA_SIZE="$CONFIG_STRING_PM_DB_SIZE"
        fi
    else
        RETURN_DATA_SIZE=$(grep -ow "^${pm_key}=\([0-9]\+\)" $pm_zenith_size_file 2>/dev/null| tail -1 |awk -F "=" '{print $2}')
    fi
    ## pmnode like pmnode1 contains dvpmdatadb1，dvpmdatadb10，dvpmdatadb11，dvpmdatadb12, the dvpmdatadb size need divided by 4.
    if (is_num "${RETURN_DATA_SIZE}");then
        ## if total size more than 1.6T(rounding 1677721MB), reserve 1/8 of the space for archive_log.
        if [ $RETURN_DATA_SIZE -ge 1677721 ];then
            RETURN_DATA_SIZE=$(( ($RETURN_DATA_SIZE * 7 / 8) / 4 ))
        else
            RETURN_DATA_SIZE=$(($RETURN_DATA_SIZE/4))
        fi
    else
        RETURN_DATA_SIZE=""
    fi
}

function set_pm_data_db_size()
{
    local pm_cluster_num=6
    for (( i = 1; i <= $pm_cluster_num; i++ )); do
        get_pm_data_db_size "pm$i" "$i"
        [ -n "${RETURN_DATA_SIZE}" ] && update_config $val_store_file "PM_DATA_DB_SIZE_$i" "${RETURN_DATA_SIZE}"
    done
}

function get_pm_db_size_from_config_string()
{
    if [ "X${zenith_paramgroup_file}" == "XlargeCapacity" ];then
        CONFIG_STRING_PM_DB_1_SIZE=""
        CONFIG_STRING_PM_DB_2_SIZE=""
        get_disk_size_from_config_string "pmdb1"
        [ -n "${RETURN_DISK_SIZE}" ] && CONFIG_STRING_PM_DB_1_SIZE=${RETURN_DISK_SIZE}
        get_disk_size_from_config_string "pmdb2"
        [ -n "${RETURN_DISK_SIZE}" ] && CONFIG_STRING_PM_DB_2_SIZE=${RETURN_DISK_SIZE}
        log_echo "INFO" "The is largeCapacity scene, get CONFIG_STRING_PM_DB_1_SIZE=${CONFIG_STRING_PM_DB_1_SIZE} and CONFIG_STRING_PM_DB_2_SIZE=${CONFIG_STRING_PM_DB_2_SIZE}."
        return 0
    fi

    CONFIG_STRING_PM_DB_SIZE=""
    get_disk_size_from_config_string "pmdb"
    [ -n "${RETURN_DISK_SIZE}" ] && CONFIG_STRING_PM_DB_SIZE=${RETURN_DISK_SIZE}
    log_echo "INFO" "get CONFIG_STRING_PM_DB_SIZE=${CONFIG_STRING_PM_DB_SIZE}."
}

function set_pm_node_zenith_size()
{
    local ip_list="$1"
    local ip_pwd_list=($2)
    local operation_type="$3"
    local exist_node_num="$4"

    if [ -z "$ip_list" ] && [ "X${zenith_paramgroup_file}" != "XlargeCapacity" ]; then
        log_echo "INFO" "The ip list of set_pm_node_zenith_size is empty."
        return 0
    fi

    log_echo "INFO" "set pm nodes zenith size with ip_list=${ip_list} and exist_node_num=${exist_node_num}..."

    pm_zenith_size_file=${PreSet_PATH}/pm_zenith_size.txt
    val_store_file=${dv_cfg_file}.tmp
    if [ "${operation_type}" == "extend" ]; then
        pm_zenith_size_file=${PreSet_PATH}/pm_extend_zenith_size.txt
        val_store_file=${configParametersFile}
    fi
    rm -f ${pm_zenith_size_file} 2>/dev/null

    if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ];then
        log_echo "INFO" "The pm node db size is config as a number."
        get_pm_db_size_from_config_string
    else
        summarize_all_node_size "${ip_list}" "${ip_pwd_list[*]}" "$exist_node_num"
    fi
    set_pm_data_db_size
    log_echo "INFO" "set pm nodes zenith size successfully."
}

function ssh_check_nbi_extend_ip_config()
{
    local app_ssh_ip=$1
    local app_node_pwd=$2
    
    ## 检查是否扩展北向平面，如果不扩展，不需要检查参数。
    if [ "X${isAppNbiIpExtend}" != "XYes" ];then
        log_echo "INFO" "The isAppNbiIpExtend=${isAppNbiIpExtend}, not need to check it."
        return 0
    fi
    
    if [ -z "${app_node_pwd}" -a ! -z "${root_password}" ];then
        app_node_pwd="${root_password}"
    fi
    
    if [ -z "${app_node_pwd}" ];then
        log_echo "INFO" "The parameter app_node_pwd is null.not need to check it.of ${app_ssh_ip}"
        return 0
    fi
    
    ## 检查 APP_EXTERNAL_NBI_IPV4_NETMASK 和 APP_EXTERNAL_NBI_IPV4_NIC 配置的子掩码和网卡名称与主机IP DVPrimary_EXTERNAL_NBI_IPV4 所在网卡和子掩码一致。否则报错。
    ## 检查网卡是否存在。不存在报错退出。
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} ifconfig ${APP_EXTERNAL_NBI_IPV4_NIC}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ ifconfig ${APP_EXTERNAL_NBI_IPV4_NIC} ] at node ${app_ssh_ip} failed, please check..."
        log_echo "ERROR" "The APP_EXTERNAL_NBI_IPV4_NIC=${APP_EXTERNAL_NBI_IPV4_NIC} not found.Execute cmd [ ifconfig ${APP_EXTERNAL_NBI_IPV4_NIC} ] failed. at node ${app_ssh_ip}"
        exit 1
    fi
    
    ##检查网卡是否存在配置的IP，不存在报错退出。
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} ifconfig ${APP_EXTERNAL_NBI_IPV4_NIC} |grep -wF \\\"${DVSecondary_EXTERNAL_NBI_IPV4}\\\""
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ ifconfig ${APP_EXTERNAL_NBI_IPV4_NIC} |grep -wF \"${DVSecondary_EXTERNAL_NBI_IPV4}\" ] at node ${app_ssh_ip} failed, please check..."
        log_echo "ERROR" "The nic ${APP_EXTERNAL_NBI_IPV4_NIC} not found ip ${DVSecondary_EXTERNAL_NBI_IPV4}, at node ${app_ssh_ip}"
        exit 1
    fi
    
    ## 检查网卡所在IP对应的子掩码是否匹配，不匹配，报错退出。 
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} ifconfig ${APP_EXTERNAL_NBI_IPV4_NIC} |grep -wF \\\"${DVSecondary_EXTERNAL_NBI_IPV4}\\\" |grep -wF \\\"${APP_EXTERNAL_NBI_IPV4_NETMASK}\\\""
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ ifconfig ${APP_EXTERNAL_NBI_IPV4_NIC} |grep -wF \"${DVSecondary_EXTERNAL_NBI_IPV4}\" |grep -wF \"${APP_EXTERNAL_NBI_IPV4_NETMASK}\" ] at node ${app_ssh_ip} failed, please check..."
        log_echo "ERROR" "The subnet mask of the IP address (${DVSecondary_EXTERNAL_NBI_IPV4}) corresponding to the network adapter ${APP_EXTERNAL_NBI_IPV4_NIC} does not match ${APP_EXTERNAL_NBI_IPV4_NETMASK}.At node ${app_ssh_ip}"
        exit 1
    fi
    log_echo "INFO" "ssh_check_nbi_extend_ip_config ${app_ssh_ip} success"
}

function check_nbi_extend_ip_config()
{
    log_echo "INFO" "check_nbi_extend_ip_config start."
    ## 检查是否扩展北向平面，如果不扩展，不需要检查参数。
    if [ "X${isAppNbiIpExtend}" != "XYes" ];then
        log_echo "INFO" "The isAppNbiIpExtend=${isAppNbiIpExtend}, not need to check it."
        return 0
    fi
    log_echo "INFO" "The isAppNbiIpExtend=${isAppNbiIpExtend}"
    ## 执行到下面，表示扩展北向平面，检查IP类型是否正确。只支持ipv4. 其他ip类型报错退出。
    local ip_type="$1"
    if [ "X${ip_type}" != "X0" ];then
        log_echo "ERROR" "This scenario is not supported when ip_type is set to ${ip_type} and isAppNbiIpExtend is set to ${isAppNbiIpExtend}. isAppNbiIpExtend can be set to Yes only when ip_type is set to 0."
        exit 1
    fi
    
    ## 扩展北向平面参数是否为空。其中有一个参数为空则报错退出。
    local check_result=0
    local ip_parameter_keys="DVPrimary_EXTERNAL_NBI_IPV4,DVSecondary_EXTERNAL_NBI_IPV4,APP_EXTERNAL_NBI_IPV4"
    local check_parameter_keys="${ip_parameter_keys},APP_EXTERNAL_NBI_IPV4_NETMASK,APP_EXTERNAL_NBI_IPV4_NIC"
    for config_key in $(echo "${check_parameter_keys}" |sed "s/,/ /g");do
        is_empty "${config_key}" "$(eval echo \$${config_key})" "not_exit"
        if [ $? -ne 0 ];then
            check_result=1
        fi
    done
    [ ${check_result} -ne 0 ] && exit 1
    
    is_empty "source_file" "${source_file}"
    
    if [ ! -f "${source_file}" ];then
        log_echo "ERROR" "The source_file=${source_file} not exist."
        exit 1
    fi
    
    ## 检查IP是否合法，如果IP非IPV4,则报错退出。
    check_ip_list "${ip_parameter_keys}"
    
    ## 检查配置参数配置的IP是否冲突，是否配置和和其他参数相同的IP。存在相同IP时报错退出。
    for config_key in $(echo "${ip_parameter_keys}" |sed "s/,/ /g");do
        local tmp_ip="$(eval echo \$${config_key})"
        [ -z "${tmp_ip}" ] && continue
        local same_value_count=$(cat ${source_file}|grep -v "^[[:blank:]]*#" |grep -v "NTP_server_IP"|grep -wF "${tmp_ip}" |awk -F'=' '{print $2}'|sed "s#,#\n#g"|wc -l)
        if [ ${same_value_count} -gt 1 ];then
            log_echo "ERROR" "The same IP address(${tmp_ip}) is configured in ${same_value_count} places.Please check:${source_file} for parameters:"
            cat ${source_file}|grep -v "^[[:blank:]]*#"|grep -v "NTP_server_IP"|grep -wF "${tmp_ip}" |tee -a ${LOG_FILE}
            check_result=1
        fi
    done
    [ ${check_result} -ne 0 ] && exit 1
    
    ## 检查主机IP是否ping通，如果ping不通，表示不存在或网络不通，报错退出。
    noping_list=""
    check_ip_ping "${DVPrimary_EXTERNAL_NBI_IPV4}" "DVPrimary_EXTERNAL_NBI_IPV4"
    check_ip_ping "${DVSecondary_EXTERNAL_NBI_IPV4}" "DVSecondary_EXTERNAL_NBI_IPV4"
    if [ ! -z "${noping_list}" ];then
        log_echo "ERROR" "Following IP can't be reached ,please check : ${noping_list}"
        exit 1
    fi
    
    ## 检查浮动IP是否ping通，如果ping通，则表示被占用。报错退出。
    check_ip_ping_bool "${APP_EXTERNAL_NBI_IPV4}"
    [ $? -ne 0 ] && exit 1
    
    ## 检查 APP_EXTERNAL_NBI_IPV4_NETMASK 和 APP_EXTERNAL_NBI_IPV4_NIC 配置的子掩码和网卡名称与主机IP DVPrimary_EXTERNAL_NBI_IPV4 所在网卡和子掩码一致。否则报错。
    ## 检查网卡是否存在。不存在报错退出。
    ifconfig "${APP_EXTERNAL_NBI_IPV4_NIC}" 
    if [ $? -ne 0 ];then
        log_echo "ERROR" "The APP_EXTERNAL_NBI_IPV4_NIC=${APP_EXTERNAL_NBI_IPV4_NIC} not found.Execute cmd [ ifconfig ${APP_EXTERNAL_NBI_IPV4_NIC} ] failed."
        exit 1
    fi
    
    ##检查网卡是否存在配置的IP，不存在报错退出。
    ifconfig "${APP_EXTERNAL_NBI_IPV4_NIC}" |grep -wF "${DVPrimary_EXTERNAL_NBI_IPV4}" >> $LOG_FILE 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "The nic ${APP_EXTERNAL_NBI_IPV4_NIC} not found ip ${DVPrimary_EXTERNAL_NBI_IPV4}"
        exit 1
    fi
    
    ## 检查网卡所在IP对应的子掩码是否匹配，不匹配，报错退出。 
    ifconfig "${APP_EXTERNAL_NBI_IPV4_NIC}" |grep -wF "${DVPrimary_EXTERNAL_NBI_IPV4}" |grep -wF "${APP_EXTERNAL_NBI_IPV4_NETMASK}" >> $LOG_FILE 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "The subnet mask of the IP address (${DVPrimary_EXTERNAL_NBI_IPV4}) corresponding to the network adapter ${APP_EXTERNAL_NBI_IPV4_NIC} does not match ${APP_EXTERNAL_NBI_IPV4_NETMASK}."
        exit 1
    fi
    log_echo "INFO" "check_nbi_extend_ip_config finished."
}

#除小型化和轻量化以外，都要将集中任务管理作为必选特性
function add_DVITManagement_feature()
{
    features=$(${PreSet_PATH}/UniEp/python/bin/python ${PreSet_PATH}/tools/handlejson.py "getinfo_product" "${json_tmp_path}/product_sop.json" "features")
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Get features in the add_DVITManagement_feature failed"
        exit 1
    fi
    echo $features|grep DVITManagement
    if [ $? -ne 0 ];then
        ${PreSet_PATH}/UniEp/python/bin/python ${PreSet_PATH}/tools/handlejson.py "productext_modify" "${json_tmp_path}/product_sop.json" "features" "${features};DVITManagement"
    fi
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Add DVITManagement failed in the add_DVITManagement_feature"
        exit 1
    fi
}

function check_os_suse_arm()
{
    cat /etc/os-release | grep "SUSE" >/dev/null
    if [ $? -eq 0 ]; then
        suse_version=$(grep "VERSION=" /etc/os-release|head -1 | awk -F'=' '{print $2}'|sed "s/\"//g")
        if [ "X${suse_version}" == "X12-SP5" ]; then
            cputype=$(uname -i)
            if [ "X${cputype}" == "Xaarch64" ]; then
                return 0
            fi
        fi
    fi
    return 1
}
