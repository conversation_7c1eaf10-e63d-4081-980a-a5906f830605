#!/bin/bash
set +x
if [ $# -lt 1 ]; then
    UTILS_PATH=$(cd $(dirname $0); pwd)
else
    UTILS_PATH="$1"
fi

if [ ! -f $UTILS_PATH/utils_common.sh ];then
    echo "The UTILS_PATH=${UTILS_PATH} is error of utils_common.sh"
    exit 1
fi

cat /etc/ssh/sshd_config 2> /dev/null| grep -w 'Port' | grep -v '#' > /dev/null 2>&1
if [ $? -ne 0 ];then
    i2k_ssh_port=22
else
    i2k_ssh_port=$(cat /etc/ssh/sshd_config | grep -w "Port" | grep -v "#" |awk -F 'Port' '{print $2}' | sed s/[[:space:]]//g)
fi
rm -f /tmp/.sshPort

if [ "X$(whoami)" == "Xroot" ];then
    which python &> /dev/null || alias python=python3
fi

##Common
PreSet_PATH=$(cd ${UTILS_PATH};pwd)
isInCommon=$(basename ${PreSet_PATH})
if [ "${isInCommon}" == "Common" ];then
    PreSet_PATH=$(cd ${UTILS_PATH}/..;pwd)
fi

PRESET_NODE_TMP_LOG=${PreSet_PATH}/tmp_preset_node.log
LOG_FILE=${PreSet_PATH}/preset_script.log
configParametersFile=${UTILS_PATH}/netWorkTypeOfDvConfigParameters.config
dv_cfg_file=${UTILS_PATH}/DV_config.properties
ext_disk_properties=${UTILS_PATH}/ext_disk.properties
disk_map_file=${UTILS_PATH}/pre_get_disk_symbol.properties
ATAE_extend_script="/var/adm/autoinstall/scripts/extend_disk.sh"
os_version=$(grep "VERSION=" /etc/os-release|head -1 | awk -F'=' '{print $2}'|sed "s/\"//g")
if [ "X${os_version}" == "X2.0 (SP9x86_64)" -o "X${os_version}" == "X2.0 (SP10x86_64)" -o "X${os_version}" == "X2.0 (SP10)" -o "X${os_version}" == "X12-SP5"  -o "X${euler_version}" == "X2.0 (SP11x86_64)" -o "X${euler_version}" == "X2.0 (SP11)"  -o "X${euler_version}" == "X2.0 (SP12x86_64)" -o "X${euler_version}" == "X2.0 (SP12)" ];then
  lsof_cmd=$(which lsof |grep -v "alias" |grep "/lsof" |awk '{print $1}')' -K i'
else
  lsof_cmd=$(which lsof |grep -v "alias" |grep "/lsof" |awk '{print $1}')
fi

[ -f ${dv_cfg_file} ] && . ${dv_cfg_file} 2> /dev/null
[ -f ${dv_cfg_file}.tmp ] && . ${dv_cfg_file}.tmp 2> /dev/null
[ -f ${configParametersFile} ] && . ${configParametersFile} 2> /dev/null
if [ "X${source_file}" != "X" ];then
    source_file_name=$(basename ${source_file})
    [ -f ${configParametersFile} ] && [ -f "${PreSet_PATH}/${source_file_name}" ] && . ${PreSet_PATH}/${source_file_name} 2> /dev/null
fi

command_prefix=""
if [ "X$(whoami)" == "Xroot" ];then
    command_prefix=""
else
    command_prefix="sudo -u ossuser "
fi
TEST_FILE="${command_prefix}test -f "
TEST_DIR="${command_prefix}test -d "
export LANG=en_US.UTF-8

function log_echo()
{
    log_level=INFO
    if [ $# -ge 2 ]; then
        log_level=$1
        shift
    fi
    log_level=$(echo ${log_level} | tr 'a-z' 'A-Z')
    log_time=$(date +'%Y-%m-%d %H:%M:%S')
    echo "${log_time} [${log_level}] $*" >> $LOG_FILE
    if [ "X${log_level}" == "XINFO" ]; then
        echo -e "\033[49;32m[INFO][${log_time}]\033[0m $*" 
    elif [ "X${log_level}" == "XWARN" ]; then
        echo -e "\033[49;33m[WARN][${log_time}]\033[0m $*" 
    elif [ "X${log_level}" == "XERROR" ]; then
        echo -e "\033[49;31m[ERROR][${log_time}]\033[0m $*" 
    else
        echo -e "\033[49;31m[${log_level}][${log_time}]\033[0m $*"
    fi
}

## config_file  key  value
function update_config()
{
    local config_file="$1"
    local key="$2"
    local value="$3"
    local FUNCTION_NAME="[update_config]"
    
    if [ -z "${config_file}" -o -z "${key}" ];then
        log_echo "ERROR" "${FUNCTION_NAME}" "The config_file=${config_file} or key=${key} has null." | tee -a ${LOG_FILE}
        return 1
    fi
    
    if [ ! -f "${config_file}" ];then
        log_echo "ERROR" "${FUNCTION_NAME}" "The config_file=${config_file} is not exist." | tee -a ${LOG_FILE}
        return 1
    fi
    
    local isExist=$(cat ${config_file} |grep -v "^[ ]*#"|grep -w "${key}")
    if [ -z "${isExist}" ];then
        echo "${key}=${value}" >> ${config_file}
    else
        sed -i "s#^${key}=.*#${key}=${value}#g" ${config_file}
    fi
    local config_ret=$?
    if [ ${config_ret} -ne 0 ];then
        log_echo "ERROR" "${FUNCTION_NAME}" "update_config key=${key}  and value to ${config_file} failed." | tee -a ${LOG_FILE}
        return 1
    fi
    log_echo "INFO" "${FUNCTION_NAME}" "update_config key=${key} and value successfully." | tee -a ${LOG_FILE}
    return 0
}

function die()
{
    log_echo "ERROR" "$1"
    exit_script
}

## cfg_file cfg_key cfg_value
function check_and_corrected_cfg_value()
{
    local cfg_file="$1"
    local cfg_key="$2"
    local cfg_value="$3"
    
    if [ -z "${cfg_file}" -o -z "${cfg_key}" -o -z "${cfg_value}" ];then
        log_echo "INFO" "The cfg_file=${cfg_file} or cfg_key=${cfg_key} or cfg_value=${cfg_value} has null."
        return 0
    fi
    
    if [ ! -f ${cfg_file} ];then
        log_echo "INFO" "The cfg_file=${cfg_file} is not exists."
        return 0
    fi
    
    local cfg_key_value=$(eval echo '$'"${cfg_key}")
    if [ -z "${cfg_key_value}" ];then
        log_echo "INFO" "The cfg_key_value=${cfg_key_value} is null."
        return 0
    fi
    
    if [ "${cfg_key_value}" == "${cfg_value}" ];then
        log_echo "INFO" "The cfg_key_value=${cfg_key_value} and cfg_value=${cfg_value} is same."
        return 0
    fi
    
    local toLowercase1=$(echo "${cfg_key_value}" |tr '[A-Z]' '[a-z]')
    local toLowercase2=$(echo "${cfg_value}" |tr '[A-Z]' '[a-z]')
    if [ "${toLowercase1}" == "${toLowercase2}" ];then
        sed -i "s#^${cfg_key}=.*#${cfg_key}=${cfg_value}#g" ${cfg_file}
    fi
    
    return 0
}

function check_and_conversion_space_config()
{
    log_echo "INFO" "check_and_conversion_space_config start."
    local key="$1"
    if [ -z "${key}" ];then
        log_echo "ERROR" "The key=${key} is null.of check_and_conversion_space_config"
        exit 1
    fi
    
    local value=$(eval echo \$${key})
    if [ -z "${value}" ];then
        log_echo "ERROR" "The ${key} parameter is null,Please check this parameter in config: ${source_file}"
        exit 1
    fi
    
    if [ "X${key}" == "XcountryName" ];then
        echo ${value} | grep "^[A-Z][A-Z]$"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "${key}=${value} must be two uppercase letters,please fill in a correct country code in ${source_file}."
            exit 1
        fi
    fi
    
    local tmp_value=$(cat ${source_file} |grep -w "^${key}=" |awk -F"${key}=" '{print $2}' | sed "s/\"//g"|sed 's/^[ ]*\|[ ]*$//g')
    sed -i "s/^${key}=.*/${key}=\"${tmp_value}\"/g" ${source_file}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "The key=${key} modify failed.please check.."
        exit 1
    fi
    
    log_echo "INFO" "The key=${key} modify successfully."
}

function check_config_number_value()
{
    log_echo "INFO" "check_config_number_value start."
    local keylist="$1"
    if [ -z "${keylist}" ];then
        log_echo "ERROR" "The keylist=${keylist} is null.of check_config_number_value"
        exit 1
    fi
    not_null_list=""
    number=0
    for key in ${keylist}
    do  
        local value=$(eval echo \$${key})
        if [ -z "${value}" -o "X${value}" == "X{{$key}}" ];then
            log_echo "INFO" "The ${key} parameter is null"
            return 0
        fi
        
        if [ "X${pre_install_key}" != "XPreInstall" ];then
            echo ${value} | grep "^[0-9]*$"
            if [ $? -ne 0 ];then
                not_null_list="$not_null_list ${key}=${value}"
                number=$(( $number +1 ))
            fi
        fi
    done
    if [ $number -ne 0 ];then
        log_echo "ERROR" "${not_null_list} must be a number."
        exit 1
    fi
    return 0
}

function check_and_corrected_config()
{
    log_echo "INFO" "The check_and_corrected_config start."
    source ${source_file}
    check_and_corrected_cfg_value "${source_file}" "isWebNetworkExtend" "Yes"
    check_and_corrected_cfg_value "${source_file}" "isAppNbiIpExtend" "Yes"
    check_and_corrected_cfg_value "${source_file}" "isAppNfvIpExtend" "Yes"
    check_and_corrected_cfg_value "${source_file}" "IS_INSTALL_CMP" "Yes"
    check_and_corrected_cfg_value "${source_file}" "IS_INSTALL_ICNFG" "Yes"
    check_and_corrected_cfg_value "${source_file}" "LANGUAGE" "zh_CN"
    check_and_corrected_cfg_value "${source_file}" "LANGUAGE" "en_US"
    check_and_corrected_cfg_value "${source_file}" "AUTO_DISK_MOUNT" "TRUE"
    check_and_corrected_cfg_value "${source_file}" "UniEP_ExtendDisk_isMounted" "TRUE"
    check_and_corrected_cfg_value "${source_file}" "UniEP_ExtendDisk_isMounted" "FALSE"
    check_and_corrected_cfg_value "${source_file}" "SILENCE_INSTALLMODE" "TRUE"
    check_and_corrected_cfg_value "${source_file}" "AUTO_SCAN_UPLOAD_SOFTWARE_PACKAGE" "TRUE"
    check_and_corrected_cfg_value "${source_file}" "changeUUidToDisk" "Yes"
    check_and_corrected_cfg_value "${source_file}" "is_integrated_df_and_service" "Yes"
    check_and_corrected_cfg_value "${source_file}" "dv_lite_mode" "TRUE"
    check_and_corrected_cfg_value "${source_file}" "dv_lite_mode" "FALSE"
    check_and_corrected_cfg_value "${source_file}" "dv_deploy_scale_size" "default"
    check_and_corrected_cfg_value "${source_file}" "dv_deploy_scale_size" "small"
    check_and_corrected_cfg_value "${source_file}" "dv_deploy_scale_size" "venus"
    check_and_corrected_cfg_value "${source_file}" "NFV_MODE" "ON"
    check_and_corrected_cfg_value "${source_file}" "NFV_MODE" "OFF"
    
    check_and_conversion_space_config "organizationName"
    check_and_conversion_space_config "countryName"
    check_and_conversion_space_config "commonName"  
    
    source ${source_file}
    
    if [ ! -z "${dv_deploy_scale_size}" ] && [ "X${dv_deploy_scale_size}" != "X{{dv_deploy_scale_size}}" ];then
        local tmp_ret=$(echo "${dv_deploy_scale_size}" | grep -wE "default|small|venus")
        if [ -z "${tmp_ret}" ];then
            log_echo "ERROR" "The dv_deploy_scale_size=${dv_deploy_scale_size} is not in [default|small|venus]."
            exit 1
        fi
    fi
    
    log_echo "INFO" "The check_and_corrected_config finished."
    return 0
}

function check_language()
{
    if [ -z "${LANGUAGE}" -o -z "`echo ${LANGUAGE} | grep -wiE "zh_CN|en_US"`" ];then
        read -p "please input the language for dv(1. Chinese 2. English):"  dvLanguage
        if [ "X${dvLanguage}" == "X1" ];then
            dvLanguage="zh_CN"
        elif [ "X${dvLanguage}" == "X2" ];then
            dvLanguage="en_US"
        else
            log_echo "ERROR" "The dvLanguage=${dvLanguage} is incorrect. need change (1. Chinese 2. English) or source_file=${source_file} config LANGUAGE set zh_CN or en_US. please check and select language."
            exit 1
        fi
        
        sed -i "s/LANGUAGE=.*/LANGUAGE=${dvLanguage}/g" ${source_file}
        . ${source_file}
    fi
}


function check_unified_pwd()
{
    if [ -z "${DV_UNIFIED_PWD}" ];then
        read -p "Check whether the DigitalView unified password function is enabled. If the function is enabled, all passwords entered manually in the DigitalView preset use the same password, which poses security risks. If the function is disabled, you need to enter multiple passwords in the DigitalView(1. Yes 2. No):"  dv_unified_pwd
        if [ "X${dv_unified_pwd}" == "X1" ];then
            dv_unified_pwd="Yes"
        elif [ "X${dv_unified_pwd}" == "X2" ];then
            dv_unified_pwd="No"
        else
            log_echo "ERROR" "The Input ${dv_unified_pwd} is incorrect. need change (1. Yes 2. No).please check."
            exit 1
        fi
        
        [ -f ${dv_cfg_file} ] && sed -i "s/DV_UNIFIED_PWD=.*/DV_UNIFIED_PWD=${dv_unified_pwd}/g" ${dv_cfg_file} 2> /dev/null
        [ -f ${dv_cfg_file}.tmp ] && sed -i "s/DV_UNIFIED_PWD=.*/DV_UNIFIED_PWD=${dv_unified_pwd}/g" ${dv_cfg_file}.tmp 2> /dev/null
        [ -f ${dv_cfg_file} ] && . ${dv_cfg_file} 2> /dev/null
        [ -f ${dv_cfg_file}.tmp ] && . ${dv_cfg_file}.tmp 2> /dev/null
    fi
}
function check_extend_unified_pwd()
{
    if [ -z "${DV_EXTEND_UNIFIED_PWD}" ];then
        read -p "Check whether the DigitalView extend os unified password function is enabled. If the function is enabled, all passwords entered manually in the DigitalView preset use the same password, which poses security risks. If the function is disabled, you need to enter multiple passwords in the DigitalView extend(1. Yes 2. No):"  dv_extend_unified_pwd
        if [ "X${dv_extend_unified_pwd}" == "X1" ];then
            dv_extend_unified_pwd="Yes"
        elif [ "X${dv_extend_unified_pwd}" == "X2" ];then
            dv_extend_unified_pwd="No"
        else
            log_echo "ERROR" "The Input ${dv_extend_unified_pwd} is incorrect. need change (1. Yes 2. No).please check."
            exit 1
        fi

        [ -f ${dv_cfg_file} ] && sed -i "s/^DV_EXTEND_UNIFIED_PWD=.*/DV_EXTEND_UNIFIED_PWD=${dv_extend_unified_pwd}/g" ${dv_cfg_file} 2> /dev/null
        [ -f ${dv_cfg_file}.tmp ] && sed -i "s/^DV_EXTEND_UNIFIED_PWD=.*/DV_EXTEND_UNIFIED_PWD=${dv_extend_unified_pwd}/g" ${dv_cfg_file}.tmp 2> /dev/null
        [ -f ${dv_cfg_file} ] && . ${dv_cfg_file} 2> /dev/null
        [ -f ${dv_cfg_file}.tmp ] && . ${dv_cfg_file}.tmp 2> /dev/null

        if [ "X$1" == "Xdocker" ];then
            tmp_dir=${TMP_PATH}/DVPreSet_extend_host
            [ -f ${tmp_dir}/DV_config.properties.tmp ] && sed -i "s/^DV_EXTEND_UNIFIED_PWD=.*/DV_EXTEND_UNIFIED_PWD=${dv_extend_unified_pwd}/g" ${tmp_dir}/DV_config.properties.tmp 2> /dev/null
        fi
    fi
}
function check_force_change_os_pwd()
{
    change_os_type=$1
    if [ -z "${dv_force_change_os_pwd}" ];then
        read -p "Need to Change the Passwords of the root and sshusr Users on All Nodes? If select Yes, the passwords of the root and sshusr user of all DigitalView nodes will be changed to the password you entered. Otherwise, no processing will be performed. (If the passwords of the two users are the default factory passwords, security risks are involved, advise to change the passwords.)(1. Yes 2. No):"  dv_force_change_os_pwd
        if [ "X${dv_force_change_os_pwd}" == "X1" ];then
            dv_force_change_os_pwd="Yes"
        elif [ "X${dv_force_change_os_pwd}" == "X2" ];then
            dv_force_change_os_pwd="No"
        else
            log_echo "ERROR" "The Input ${dv_force_change_os_pwd} is incorrect. need change (1. Yes 2. No).please check."
            exit 1
        fi
        
        [ -f ${dv_cfg_file} ] && sed -i "s/dv_force_change_os_pwd=.*/dv_force_change_os_pwd=${dv_force_change_os_pwd}/g" ${dv_cfg_file} 2> /dev/null
        [ -f ${dv_cfg_file}.tmp ] && sed -i "s/dv_force_change_os_pwd=.*/dv_force_change_os_pwd=${dv_force_change_os_pwd}/g" ${dv_cfg_file}.tmp 2> /dev/null
        if [ "X${change_os_type}" == "XEXTEND" ];then
            [ -f /tmp/DVPreSet_extend/DV_config.properties.tmp ] && sed -i "s/dv_force_change_os_pwd=.*/dv_force_change_os_pwd=${dv_force_change_os_pwd}/g"  /tmp/DVPreSet_extend/DV_config.properties.tmp 2> /dev/null
            [ -f /tmp/DVPreSet_extend_host/DV_config.properties.tmp ] && sed -i "s/dv_force_change_os_pwd=.*/dv_force_change_os_pwd=${dv_force_change_os_pwd}/g"  /tmp/DVPreSet_extend_host/DV_config.properties.tmp 2> /dev/null
        else
            [ -f ${dv_cfg_file} ] && . ${dv_cfg_file} 2> /dev/null
            [ -f ${dv_cfg_file}.tmp ] && . ${dv_cfg_file}.tmp 2> /dev/null
        fi
        
    fi
}

function is_current_root_user()
{
    log_echo "INFO" "Begin to check current user." 
    current_user=$(whoami)
    if [ "X${current_user}" != "Xroot" ];then
        log_echo "ERROR" "Current user is ${current_user}, but this script need root to run !"
        exit_script
    fi
}

function exit_script()
{
    if [ "X${uninstall_if_fail}" == "XYes" ];then
        log_echo "WARN" "Begin to uninstall prehandle scripts." 
        bash ${UTILS_PATH}/nonroot_uninstall_prehandle.sh
        if [ $? -ne 0 ];then
            log_echo "ERROR" "bash ${UTILS_PATH}/nonroot_uninstall_prehandle.sh failed." 
            exit 1
        fi
    fi
    exit 1
}

function compare_and_set_minmax()
{
    local work=$1
    [[ $(echo "${work} > ${MAX_OFFSET}" | bc) == "1" ]] && MAX_OFFSET="${work}"
    [[ $(echo "${work} < ${MIN_OFFSET}" | bc) == "1" ]] && MIN_OFFSET="${work}"
    return 0
}

function check_itp_ntpsmart_time_consistent()
{
    local server_ip=$(echo "$@" | sed s/,/\\n/)
    local server_num=$(echo "${server_ip}" | wc -l)
    (( server_num <= 1 )) && return 0
    local time_gap=""
    while read line;
    do
        ntpdate -q "${line}" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "The server's [${line}] ntpd service is not working, please check by executing cmd [ systemctl status ntpd.service ] in service [${line}]."
            exit 1
        fi
        time_gap=$(ntpdate -q "${line}" 2>&1 | sed -n "1p" | grep "offset " | awk -F "offset " '{print $2}' | awk -F"," '{print $1}' | sed "s/+//g")
        [[ "${time_gap}" == "" ]] && continue
        if [[ "${MIN_OFFSET}" == "" ]]; then
            MIN_OFFSET="${time_gap}"
        fi
        if [[ "${MAX_OFFSET}" == "" ]]; then
            MAX_OFFSET="${time_gap}"
        fi
        compare_and_set_minmax "${time_gap}"
    done <<< "${server_ip}"

    [[ $(echo "${MAX_OFFSET} - ${MIN_OFFSET} < 1" | bc) == "1" && \
            $(echo "${MAX_OFFSET} - ${MIN_OFFSET} > -1" | bc) == "1" ]] && exit_flag="0"
    if [ "${exit_flag}" != "0" ];then
        log_echo "ERROR" "Multiple clock sources are inconsistent. Please check ita-ntpsmart server. "
        exit 1
    fi
    log_echo "INFO" "Check itp ntpsmart_time consistent finished. "
}

function checkValue()
{
    local key="$1"
    local value="$2"
    local type="$3"
    
    if [ -z "${value}" ];then
        log_echo "ERROR" "The ${key}=${value} is null.Please check."
        return 1
    fi
    
    local isIPV6=$(echo "${value}" |grep ":")
    if [ "X${type}" == "X0" -a "X${isIPV6}" != "X" ];then
        ## ip type is ipv4 but ip is ipv6 
        log_echo "ERROR" "The ip type of this configuration item is ipv4, But ${key}=${value} The configured ip is not ipv4.Please check."
        return 1
    elif [ "X${type}" == "X1" -a "X${isIPV6}" == "X" ];then
        ## ip type is ipv6 but ip is ipv4 
        log_echo "ERROR" "The ip type of this configuration item is ipv6, But ${key}=${value} The configured ip is not ipv6.Please check."
        return 1
    fi
    
    return 0
}

function checkKeyListValue()
{
    local key_list="$1"
    local type="$2"
    log_echo "INFO" "Begin to checkKeyListValue of source_file=${source_file}"
    if [ -z "${key_list}" -o -z "${type}" ];then
        log_echo "ERROR" "The key_list=${key_list} or type=${type} has null.please check."
        return 1
    fi
    
    local rest=0
    local value=""
    for cfg_key in $(echo "${key_list}" |sed "s/,/ /g");do
        value=$(cat ${source_file}|grep -v "^[ ]*#"|grep "^[ ]*${cfg_key}=" |tail -n 1 |awk -F'=' '{print $2}')
        ## checkValue key value type
        checkValue "${cfg_key}" "${value}" "${type}"
        rest=$(( ${rest} + $? ))
    done
    
    if [ ${rest} -ne 0 ];then
        return 1
    fi
    log_echo "INFO" "checkKeyListValue finished."
    return 0
}

function check_ip_exists()
{
    local ip_key="$1"
    local ip_value="$2"
    
    if [ -z "${ip_key}" -o -z "${ip_value}" ];then
        log_echo "ERROR" "check_ip_exists ip_key=${ip_key} or ip_value=${ip_value} is null."
        exit 1
    fi
    
    ip_value=$(echo "${ip_value}"|sed "s/\./\\\./g")
    grep -iw "${ip_value}" ${source_file} |grep -v "^[ ]*#" |grep -v "${ip_key}=${ip_value}"
    if [ $? -eq 0 ];then
        log_echo "ERROR" "check_ip_exists ${ip_key}=${ip_value} in ${source_file} exists same config."
        exit 1
    fi
    log_echo "INFO" "check_ip_exists ${ip_key}=${ip_value} finished."
}

function check_ip_list()
{
    local ipv4_list="$1"
    local ipv6_list="$2"
    log_echo "INFO" "ipv4_list=${ipv4_list} ipv6_list=${ipv6_list}"
    local rest=0
    if [ ! -z "${ipv4_list}" ];then
        checkKeyListValue "${ipv4_list}" "0"
        rest=$?
    fi
    
    if [ ! -z "${ipv6_list}" ];then
        checkKeyListValue "${ipv6_list}" "1"
        rest=$(( ${rest} + $? ))
    fi
    
    if [ ${rest} -ne 0 ];then
        exit 1
    fi
}

function simplify_ipv6_ip()
{
    local ipv6_ip=$1
    
    while true
    do
        echo ${ipv6_ip}|grep -E ":0[0-9|a-f]" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            break
        fi
        ipv6_ip=$(echo ${ipv6_ip}|sed "s/:0\([0-9|a-f]\)/:\1/g")
    done
    
    echo ${ipv6_ip} |grep -v "::" | grep ":0" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        tmp_ip=$(echo ${ipv6_ip}|sed "s/:0/:/")
        while true
        do
            echo ${tmp_ip} | grep ":::" > /dev/null 2>&1
            if [ $? -eq 0 ];then
                tmp_ip=$(echo ${tmp_ip}|sed "s/:::/::/")
            fi
            
            tmp_ip=$(echo ${tmp_ip}|sed "s/::0:/::/")
            
            echo ${tmp_ip} | grep -v "::0:"|grep -E "::" > /dev/null 2>&1
            if [ $? -eq 0 ];then
                break
            fi
            
            echo ${tmp_ip} | grep -E ":0|:::" > /dev/null 2>&1
            if [ $? -ne 0 ];then
                break
            fi
        done
        echo ${tmp_ip}
    else
        echo ${ipv6_ip}
    fi
}

## ip=xxx,netmask=xx|***************,nic=ethX
function checkNetworkConfig()
{
    local networkConfig="$1"
    
    log_echo "INFO" "The checkNetworkConfig start."
    if [ -z "${networkConfig}" ];then
        log_echo "INFO" "The networkConfig=${networkConfig} is null.not need to check."
        return 0
    fi
    
    local ipaddr=""
    local netmask=""
    local nic=""
    local tmp_str=""
    for cfg in $(echo ${networkConfig}|sed "s/ //g"|sed "s/,/ /g");
    do
        tmp_str=$(echo "${cfg}" |awk -F'=' '{print $1}')
        if [ "${tmp_str}" == "ip" ];then
            ipaddr=$(echo "${cfg}" |awk -F'=' '{print $2}')
        elif [ "${tmp_str}" == "netmask" ];then
            netmask=$(echo "${cfg}" |awk -F'=' '{print $2}')
        elif [ "${tmp_str}" == "nic" ];then
            nic=$(echo "${cfg}" |awk -F'=' '{print $2}')
        else
            log_echo "ERROR" "The tmp_str=${tmp_str} is not in [ip,netmask,nic]"
            return 1
        fi
    done
    
    if [ -z "${ipaddr}" ];then
        log_echo "INFO" "The ipaddr is null.not need to check."
        return 0
    fi
    
    local tmp_ipaddr="${ipaddr}"
    local isIPV6=$(echo "${ipaddr}" |grep ":")
    if [ "X${isIPV6}" != "X" ];then
        ipaddr=$(echo "${ipaddr}" | tr '[A-Z]' '[a-z]')
        ifconfig |grep -w "${ipaddr}" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            local simplify_ipv6=$(simplify_ipv6_ip "${ipaddr}")
            ## Uppercase to lowercase
            ipaddr=$(echo "${simplify_ipv6}" | tr '[A-Z]' '[a-z]')
            
            ifconfig |grep -w "${ipaddr}" >/dev/null 2>&1
            if [ $? -ne 0 ];then
                log_echo "ERROR" "The tmp_ipaddr:[${tmp_ipaddr}] Uppercase to lowercase ipaddr:[${ipaddr}] is not exist.The ip address does not exist in the environment, and a match cannot be found."
                log_echo "ERROR" "If it is an ipv6 type IP, the system displays all compressed IPs, please keep the same as the environment check display, otherwise the check cannot find a match."
                return 1
            fi
        fi
    fi
    
        
    if [ -z "${netmask}" -a -z "${nic}" ];then
        log_echo "INFO" "The netmask,nic is null.not need to check."
        return 0
    fi
    
    if [ -f "/.dockerenv" -a  ! -z "${nic}" ];then
        nic="${nic}:0"
    fi
    
    ## netmask is null 
    if [ -z "${netmask}" ];then 
        ifconfig "${nic}" |grep -w "${ipaddr}" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "The nic:[${nic}] not has ipaddr:[${ipaddr}] tmp_ipaddr:[${tmp_ipaddr}].The network card and IP cannot match on the host."
            return 1
        fi
        
        return 0
    fi
    
    ## nic is null 
    if [ -z "${nic}" ];then 
        ifconfig |grep -w "${ipaddr}" |grep -w "${netmask}" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "The tmp_ipaddr:[${tmp_ipaddr}] ipaddr:[${ipaddr}] not has netmask:[${netmask}].The IP and netmask cannot match."
            return 1
        fi
        
        return 0
    fi
    
    
    ## netmask and nic is not null
    ifconfig "${nic}" |grep -w "${ipaddr}" |grep -w "${netmask}" >/dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "The nic:[${nic}] not has ipaddr:[${ipaddr}] tmp_ipaddr:[${tmp_ipaddr}] not has netmask:[${netmask}].The IP and netmask cannot match."
        return 1
    fi
    
    log_echo "INFO" "The checkNetworkConfig End."
    return 0
}

function replace_file_macro()
{
    local file_name=$1
    local source_file=$2
    local replace_tag="$3"
    if [ ! -f $file_name ]; then
        echo "The file $file_name not exist, do not replace macro key."  >> $LOG_FILE
        return 1
    fi

    dos2unix $source_file
    
    for key in $(cat $file_name | grep {{ | grep }} | sed 's/{{/\n{{/g' | sed -rn 's/.*\{\{(.*)\}\}.*/\1/p' | grep -v "^$" | sort | uniq)
    do
        if [ "X${key}" == "Xoracle_character" ];then
            value=$(grep -h "^${key}[ ]*=" $source_file | sed -n '$p' | sed -rn "s/[^=]*=[ ]*(\S*)/\1/p" )
        elif  [ "X${key}" == "XcountryName" ];then
            value=$(grep -h "^${key}[ ]*=" $source_file | sed -n '$p' | sed -rn "s/[^=]*=[ ]*(\S*)/\1/p"|sed "s/\"//g" )
        elif  [ "X${key}" == "XorganizationName" ];then
            value=$(grep -h "^${key}[ ]*=" $source_file | sed -n '$p' | sed -rn "s/[^=]*=[ ]*(\S*)/\1/p"|sed "s/\"//g" )
        elif  [ "X${key}" == "XcommonName" ];then
            value=$(grep -h "^${key}[ ]*=" $source_file | sed -n '$p' | sed -rn "s/[^=]*=[ ]*(\S*)/\1/p"|sed "s/\"//g" )
        elif  [ "X${key}" == "XConfiguration_String" ];then
            value=$(grep -h "^${key}[ ]*=" $source_file | sed -n '$p' | sed -rn "s/[^=]*=[ ]*(.*).*/\1/p" )
        else
            value=$(grep -h "^${key}[ ]*=" $source_file | sed -n '$p' | sed -rn "s/[^=]*=[ ]*(\S*).*/\1/p" )
        fi

        if [ "X${replace_tag}" == "XEXTEND" ]; then
            local grep_result=$(grep -h "^${key}[ ]*=" $source_file)
            [ -z "${grep_result}" ] || sed -i "s#^$key=.*#$key=$value#g" $file_name
            continue
        fi
        if [ "X${replace_tag}" == "XEXTEND_EXCLUDE_IP" ]; then
            if  [[ "${key}" =~ IPV4|IPV6 ]];then
                echo "[WARN] find ${key} include IPV4 or IPV6 keywords in the $source_file in expansion scenarios, skip"  >> $LOG_FILE
                continue
            fi
        fi
        if [ "X$value" != "X" ]; then
            sed -i "s#{{$key}}#$value#g" $file_name
            continue
        elif [ "X$key" == "Xdv_site_info_list" ];then
            sed -i "s#{{$key}}#$value#g" $file_name
            continue
        else
            echo "[WARN] Can not find The value of Key:${key} in $source_file"  >> $LOG_FILE
        fi
    done
}

function init_diskmount_parameter()
{
    local auto_disk_mount=$(cat ${source_file} | grep "AUTO_DISK_MOUNT=" | awk -F '=' '{print $2}' | tr 'a-z' 'A-Z' )
    sed -i "s#AUTO_DISK_MOUNT=.*#AUTO_DISK_MOUNT=${auto_disk_mount}#g" ${source_file}
    
    if [ "X${auto_disk_mount}" == "XTRUE" ];then
        local extend_disk_name=$(cat ${source_file} | grep "^EXTEND_DISK=" | awk -F '=' '{print $2}')
        if [ ! -f "${ATAE_extend_script}" ] || [ "$CUSTOM_PATH_SCENE" == "TRUE" ];then
            log_echo "INFO" "This is 98G extend situation"
            sed -i "s#EXTEND_98G_DISK=.*#EXTEND_98G_DISK=${extend_disk_name}#g" ${UTILS_PATH}/DV_config.properties.tmp
            EXTEND_98G_DISK=${extend_disk_name}
        fi
    fi
}

function  transform_ipv6_to_simple()
{
    tmp_ipv6=$1
    tmp_ipv6_simple=$(${PreSet_PATH}/UniEp/python/bin/python -c "import ipaddress;print(str(ipaddress.ip_address('$tmp_ipv6')))")
    local config_file_tmp=$2

    echo ${tmp_ipv6_simple}|grep ":" > /dev/null
    if [ $? -ne 0 ];then
        log_echo "ERROR" "transform ipv6 to simple failed"
        exit 1
    fi
    sed  -i -e "s#\(.*[=,]\)\(${tmp_ipv6}\)[ ]*\$#\1${tmp_ipv6_simple}#g" -e "s#\(.*[=,]\)\(${tmp_ipv6},\)#\1${tmp_ipv6_simple},#g"  ${config_file_tmp}
    [ -f ${dv_cfg_file}.tmp ]  &&  sed  -i -e "s#\(.*[=,]\)\(${tmp_ipv6}\)[ ]*\$#\1${tmp_ipv6_simple}#g" -e "s#\(.*[=,]\)\(${tmp_ipv6},\)#\1${tmp_ipv6_simple},#g"   ${dv_cfg_file}.tmp

}

function modify_ipv6()
{
    local config_file_tmp=${source_file}
    if [ "X$1" != "X" ];then
        config_file_tmp=$1
    fi
    for tmp_ipv6 in $(cat ${config_file_tmp} |grep IPV6|grep -v NIC|grep -v MASK|awk -F '=' '{print $2}'|grep :|sed 's#,#\n#g');do
        if [ "X${tmp_ipv6}" != "X" ];then
            transform_ipv6_to_simple ${tmp_ipv6} ${config_file_tmp}
        fi
    done
    . ${config_file_tmp}
}

function check_duplicate_ip()
{
    local config_file_tmp=${source_file}
    if [ "X$1" != "X" ];then
        config_file_tmp=$1
    fi
    
    log_echo "INFO" "check_duplicate_ip ${config_file_tmp} start..."
    local tmp_all_ip_list=""
    local duplicate_ip_list=""
    for tmp_ip in $(cat ${config_file_tmp} |grep -v "^[[:blank:]]*#" |grep "IPV6\|IPV4"|grep -v NIC|grep -v MASK|awk -F '=' '{print $2}'|sed 's#,#\n#g');do
        if [ ! -z "${tmp_ip}" ];then
            echo "$(date) tmp_ip=${tmp_ip}" >> $LOG_FILE 2>&1
            if [ -z "${tmp_all_ip_list}" ];then
                echo "$(date) tmp_all_ip_list is null, add to tmp_all_ip_list" >> $LOG_FILE 2>&1
                tmp_all_ip_list="${tmp_ip}"
                continue
            fi
            
            echo "${tmp_all_ip_list}" |sed 's#,#\n#g' |grep -wF "${tmp_ip}" >> $LOG_FILE 2>&1
            if [ $? -ne 0 ];then
                echo "$(date) add to tmp_all_ip_list" >> $LOG_FILE 2>&1
                tmp_all_ip_list="${tmp_all_ip_list},${tmp_ip}"
                continue
            fi
            
            if [ -z "${duplicate_ip_list}" ];then
                duplicate_ip_list="${tmp_ip}"
                continue
            fi
            
            echo "${duplicate_ip_list}" |sed 's#,#\n#g' |grep -wF "${tmp_ip}" >> $LOG_FILE 2>&1
            if [ $? -ne 0 ];then
                duplicate_ip_list="${duplicate_ip_list},${tmp_ip}"
                continue
            fi
        fi
    done
    
    if [ ! -z "${duplicate_ip_list}" ];then
        log_echo "ERROR" "The duplicate ip list [ ${duplicate_ip_list} ], has more same config in ${config_file_tmp},please check it."
        exit 1
    fi
    log_echo "INFO" "check_duplicate_ip End"
}

function check_ip_parameter_by_key()
{
    ## 参数前缀关键字
    local tmp_before_str="$1"
    ## 参数后缀关键字
    local tmp_after_key="$2"
    local config_file_tmp=${source_file}
    ## 根据后缀类型 拼接完整 参数 key 
    ## 临时变量，获取要检查的参数key
    local tmp_ipv4_key=""
    local tmp_ipv6_key=""
    local tmp_mgr_ipv6_key=""
    if [ "${tmp_after_key}" == "_IPV6" ];then
        tmp_ipv4_key="${tmp_before_str}_IPV4"
        tmp_ipv6_key="${tmp_before_str}_IPV6"
        tmp_mgr_ipv6_key="${tmp_before_str}_MGR_IPV6"
    elif [ "${tmp_after_key}" == "_IPV6_List" ];then
        tmp_ipv4_key="${tmp_before_str}_IPV4_List"
        tmp_ipv6_key="${tmp_before_str}_IPV6_List"
        tmp_mgr_ipv6_key="${tmp_before_str}_MGR_IPV6_List"
    else
        log_echo "INFO" "The tmp_after_key=${tmp_after_key}.not need check it.continue"
        return 0
    fi
    
    ## 获取配置文件是否存在该key
    local ipv4_key=$(cat ${config_file_tmp} |grep -v "^[[:blank:]]*#" |grep "^${tmp_ipv4_key}=")
    local ipv6_key=$(cat ${config_file_tmp} |grep -v "^[[:blank:]]*#" |grep "^${tmp_ipv6_key}=")
    local mgr_ipv6_key=$(cat ${config_file_tmp} |grep -v "^[[:blank:]]*#" |grep "^${tmp_mgr_ipv6_key}=")
    
    ## 根据key获取到的键值对，获取对应值，并去掉空格。
    local ipv4_key_value=$(echo "${ipv4_key}" |awk -F'=' '{print $2}' |sed "s/[[:blank:]]*//g")
    local ipv6_key_value=$(echo "${ipv6_key}" |awk -F'=' '{print $2}' |sed "s/[[:blank:]]*//g")
    local mgr_ipv6_key_value=$(echo "${mgr_ipv6_key}" |awk -F'=' '{print $2}' |sed "s/[[:blank:]]*//g")
    
    local not_null_key=""
    local null_key=""
    ## ipv6单栈，需要检查ipv6的两个配置参数 ，如果两个参数都存在，检查值是否都填或者都不填，否则报错。
    if [ "X${IPType}" == "X1" -a ! -z "${ipv6_key}" -a ! -z "${mgr_ipv6_key}" ];then
        if [ ! -z "${ipv6_key_value}" -a ! -z "${mgr_ipv6_key_value}" ];then
            log_echo "INFO" "The ${tmp_ipv6_key} and ${tmp_mgr_ipv6_key} is not null. is ok."
        elif [ -z "${ipv6_key_value}" -a -z "${mgr_ipv6_key_value}" ];then
            log_echo "INFO" "The ${tmp_ipv6_key} and ${tmp_mgr_ipv6_key} is null. is ok."
        else
            [ -z "${ipv6_key_value}" ] && null_key="${tmp_ipv6_key}" || not_null_key="${tmp_ipv6_key}"
            [ -z "${mgr_ipv6_key_value}" ] && null_key="${tmp_mgr_ipv6_key}" || not_null_key="${tmp_mgr_ipv6_key}"
            check_parameter_by_ip_type_result="${check_parameter_by_ip_type_result}[ERROR] The IPType=${IPType},The ${not_null_key} is not null, but ${null_key} is null.\n"
            return 1
        fi
        return 0
    fi
    
    ## 双栈，需要检查1个ipv4 + 2个ipv6的参数，如果三个参数都存在，检查值是否都填或者都不填，否则报错。
    if [ "X${IPType}" == "X2" -a ! -z "${ipv4_key}" -a ! -z "${ipv6_key}" -a ! -z "${mgr_ipv6_key}" ];then
        if [ ! -z "${ipv4_key_value}" -a ! -z "${ipv6_key_value}" -a ! -z "${mgr_ipv6_key_value}" ];then
            log_echo "INFO" "The ${tmp_ipv4_key},${tmp_ipv6_key},${tmp_mgr_ipv6_key} all is not null. is ok."
        elif [ -z "${ipv4_key_value}" -a -z "${ipv6_key_value}" -a -z "${mgr_ipv6_key_value}" ];then
            log_echo "INFO" "The ${tmp_ipv4_key},${tmp_ipv6_key},${tmp_mgr_ipv6_key} all is null. is ok."
        else
            [ -z "${ipv4_key_value}" ] && null_key="${null_key},${tmp_ipv4_key}" || not_null_key="${not_null_key},${tmp_ipv4_key}"
            [ -z "${ipv6_key_value}" ] && null_key="${null_key},${tmp_ipv6_key}" || not_null_key="${not_null_key},${tmp_ipv6_key}"
            [ -z "${mgr_ipv6_key_value}" ] && null_key="${null_key},${tmp_mgr_ipv6_key}" || not_null_key="${not_null_key},${tmp_mgr_ipv6_key}"
            null_key=$(echo "${null_key}" |sed "s/^,//g")
            not_null_key=$(echo "${not_null_key}" |sed "s/^,//g")
            check_parameter_by_ip_type_result="${check_parameter_by_ip_type_result}[ERROR] The IPType=${IPType},The ${not_null_key} is not null, but ${null_key} is null.\n"
            return 1
        fi
        return 0
    fi
}

function check_parameter_by_ip_type()
{
    local config_file_tmp=${source_file}
    log_echo "INFO" "check_parameter_by_ip_type ${config_file_tmp} start..."
    local ip_parameter_keys=""
    ## ipv6单栈，需要检查ipv6的配置参数 
    if [ "X${IPType}" == "X1" ];then
        ip_parameter_keys="IPV6"
    fi
    
    ## 双栈，需要检查ipv4 + ipv6的参数 
    if [ "X${IPType}" == "X2" ];then
        ip_parameter_keys="IPV6\|IPV4"
    fi
    
    ## ipv4单栈 只有一个参数，不需要检查。 
    if [ -z "${ip_parameter_keys}" ];then
        log_echo "INFO" "The ip_parameter_keys is null.not need check it."
        return 0
    fi
    
    ## 汇总检查错误信息
    check_parameter_by_ip_type_result=""
    
    ## 参数前缀关键字
    local tmp_before_str=""
    ## 参数后缀关键字
    local tmp_after_key=""
    ## 临时变量缓存上一次的前缀关键字。用于判断后面是否有相同的跳过。
    local tmp_str=""
    for tmp_key in $(cat ${config_file_tmp} |grep -v "^[[:blank:]]*#" |grep "${ip_parameter_keys}"|grep -v NIC|grep -v MASK|sort|awk -F '=' '{print $1}');do
        ## 每次循环都需要先置空，初始化临时变量。
        tmp_before_str=""
        tmp_after_key=""
        ## xxx_IPV4 xxx_IPV6 xxx_MGR_IPV6
        echo "${tmp_key}" |grep ".*_IPV6$" >> $LOG_FILE 2>&1
        if [ $? -eq 0 ];then
            tmp_before_str=$(echo "${tmp_key}" |sed "s/_IPV6$//g")
            tmp_after_key="_IPV6"
        fi
        
        ## yyy_IPV4_List yyy_IPV6_List yyy_MGR_IPV6_List
        echo "${tmp_key}" |grep ".*_IPV6_List$" >> $LOG_FILE 2>&1
        if [ $? -eq 0 ];then
            tmp_before_str=$(echo "${tmp_key}" |sed "s/_IPV6_List$//g")
            tmp_after_key="_IPV6_List"
        fi
        
        ## 前缀一样已经统一处理，所以判断相同的直接跳过，或者 获取不到前缀，即不满足xxx_IPV6 和  yyy_IPV6_List两种情况的。不处理。
        if [ "X${tmp_str}" == "X${tmp_before_str}" -o -z "${tmp_after_key}" ];then
            log_echo "INFO" "The tmp_str=${tmp_str} tmp_before_str=${tmp_before_str} tmp_after_key=${tmp_after_key} of tmp_key=${tmp_key}.not need check it.continue"
            continue
        fi
        
        ## 前缀不一样的时候，替换上一次的前缀。对当前前缀进行检查。
        tmp_str="${tmp_before_str}"
        check_ip_parameter_by_key "${tmp_before_str}" "${tmp_after_key}"
    done
    
    if [ ! -z "${check_parameter_by_ip_type_result}" ];then
        log_echo "ERROR" "There are following problems with checking parameters, please check:"
        echo -e "${check_parameter_by_ip_type_result}" |tee -a $LOG_FILE
        exit 1
    fi
    log_echo "INFO" "check_parameter_by_ip_type End"
}

function replace_init_tmpfile()
{
    cp -prf ${dv_cfg_file} ${dv_cfg_file}.tmp
    cp -prf ${ext_disk_properties} ${ext_disk_properties}.tmp
    replace_file_macro ${dv_cfg_file}.tmp ${source_file}
    replace_file_macro ${ext_disk_properties}.tmp ${source_file}
    
    init_diskmount_parameter
    
    . ${source_file}
}

function Check_execute()
{
    local check_key=$1
    cat ${action_tag} | grep -w "${check_key} : success" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "${check_key} has been executed"
        return 0
    else
        cat ${action_tag} | grep -w "${check_key} : failed" > /dev/null 2>&1
        if [ $? -eq 0 ];then
            log_echo "INFO" "Execute ${check_key} failed, need cleanup first."
            return 2  
        else
            return 1
        fi
    fi
}

function getchar()
{
    stty cbreak -echo
    dd if=/dev/tty bs=1 count=1 2>/dev/null
    stty -cbreak echo
}

function echo_pwd()
{
    PwdReturn=""
    printf "$1:"
    while : ;do
        ret=$(getchar)
        #for escape character
        #exit for space character
        if [[ "X${ret}" == "X" ]];then
            echo
            break
        fi
        PwdReturn="$PwdReturn$ret"
        printf "*"
    done
}

function input_pwd()
{
    local node_ip=$1
    local pwd_user="root"
    [ "X$2" != "X" ] && pwd_user="$2"
    pwdReturn=""
    
    local input_times=0
    while [ ${input_times} -lt 3 ]
    do
        echo_pwd "Please input ${pwd_user}\'s password of ${node_ip} "
        pwdReturn="$PwdReturn"

        auto_smart_ssh ${pwdReturn} "${pwd_user}@${node_ip} echo ssh_ok"
        if [ $? -eq 0 ];then
            break
        elif [ ${input_times} -eq 2 ];then
            log_echo "ERROR" "ssh to ${node_ip} execute command failed, please check."
            exit 1
        else
            log_echo "INFO" "Please try again to ssh to ${node_ip}."
        fi
        input_times=$((${input_times}+1))
    done
}

function get_input_password()
{
    ipList=$1
    node=$2
    e=0
    if [ "X${node}" == "XSA" ];then
        for a in $(echo "${ipList}" | sed "s#,# #g")
        do
            input_pwd "${a}" ${oper_user}
            SA_Cluster_Node_pwd_list[$e]="${pwdReturn}"
            ((e++))
        done
    elif [ "X${node}" == "XOM" ];then
        for a in $(echo "${ipList}" | sed "s#,# #g")
        do
            input_pwd "${a}" ${oper_user}
            OM_Cluster_Node_pwd_list[$e]="${pwdReturn}"
            ((e++))
        done
    elif [ "X${node}" == "XSM" ];then
        for a in $(echo "${ipList}" | sed "s#,# #g")
        do
            input_pwd "${a}" ${oper_user}
            SM_Cluster_Node_pwd_list[$e]="${pwdReturn}"
            ((e++))
        done
    elif [ "X${node}" == "XOMSA" ];then
        for a in $(echo "${ipList}" | sed "s#,# #g")
        do
            input_pwd "${a}"  ${oper_user}
            OMSA_Cluster_Node_pwd_list[$e]="${pwdReturn}"
            ((e++))
        done
    elif [ "X${node}" == "XFLUME" ];then
        for a in $(echo "${ipList}" | sed "s#,# #g")
        do
            input_pwd "${a}" ${oper_user}
            Flume_Cluster_Node_pwd_list[$e]="${pwdReturn}"
            ((e++))
        done
    elif [ "X${node}" == "XVS" ];then
        for a in $(echo "${ipList}" | sed "s#,# #g")
        do
            input_pwd "${a}"  ${oper_user}
            VS_Cluster_Node_pwd_list[$e]="${pwdReturn}"
            ((e++))
        done
    elif [ "X${node}" == "XDVExtendCluster" ];then
        for a in $(echo "${ipList}" | sed "s#,# #g")
        do
            input_pwd "${a}"  ${oper_user}
            Extend_Cluster_Node_pwd_List[$e]="${pwdReturn}"
            ((e++))
        done
    elif [ "X${node}" == "XIM" ];then
        for a in $(echo "${ipList}" | sed "s#,# #g")
        do
            input_pwd "${a}" ${oper_user}
            IM_Cluster_Node_pwd_list[$e]="${pwdReturn}"
            ((e++))
        done
    elif [ "X${node}" == "XPM" ];then
        for a in $(echo "${ipList}" | sed "s#,# #g")
        do
            input_pwd "${a}"  ${oper_user}
            PM_Cluster_Node_pwd_list[$e]="${pwdReturn}"
            ((e++))
        done
    elif [ "X${node}" == "XSD" ];then
        for a in $(echo "${ipList}" | sed "s#,# #g")
        do
            input_pwd "${a}"  ${oper_user}
            SD_Cluster_Node_pwd_list[$e]="${pwdReturn}"
            ((e++))
        done
    elif [ "X${node}" == "XAE" ];then
        for a in $(echo "${ipList}" | sed "s#,# #g")
        do
            input_pwd "${a}" ${oper_user}
            APP_Extend_Node_pwd_list[$e]="${pwdReturn}"
            ((e++))
        done
    fi
}

function check_dir_size()
{
    log_echo "INFO" "Begin to check avail space of some directory..."
    check_result=0
    
    var_size=$(df -PBG /var | grep "/" |awk -F' ' '{print $4}'|sed "s#G##"|awk -F'.' '{print $1}')
    if [ ${var_size} -lt 1 ];then
        log_echo "ERROR" "/var's avail space is less than 1G, please check"
        check_result=1
    fi
    
    local i=0
    local TIMES=3
    while [ $i -lt $TIMES ]
    do
        timeout -s KILL 90 rpm -qa >/dev/null
        if [ $? -eq 0 ];then
            break
        elif [ ${i} -eq 2 ];then
            log_echo "ERROR" "Execute command \"rpm -qa\" on UniEP Node ${UNIEP_NODE_IP} failed or timeout, please check"
            check_result=1
        else
            log_echo "INFO" "Try again to execute \"rpm -qa\"."
        fi
        (( i++ ))
    done
    
    [ $check_result -ne 0 ] && exit 1
    
    return 0
}

function ATAE_Extend_Disk()
{
    if [ "X${EXTEND_DISK}" != "X" -a "X${EXTEND_DISK}" != "X{{EXTEND_DISK}}" ];then
        log_echo "INFO" "Begin to do ATAE_Extend_Disk..."
        sh ${PreSet_PATH}/tools/ATAEExtend/extend_disk.sh "${EXTEND_DISK}" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Execute tools/ATAEExtend/extend_disk.sh failed, please check"
            exit 1
        fi
    fi
}

function handles_special_char()
{
    echo $1|sed -r 's/(\{|\}|\$|\[|;|\\)/\\\1/g'
}

function check_remote_user_pwd()
{
    local remote_user_pwd="$1"
    if [ -z "${remote_user_pwd}" ];then
        log_echo "ERROR" "The remote_user_pwd is null, please check."
        return 1
    fi
    
    local invalid_characters="\x1b|\x7f"
    echo "${remote_user_pwd}" |grep -iE "${invalid_characters}" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "ERROR" "The remote_user_pwd string contains invalid characters, please check."
        return 1
    fi
    
    return 0
}

function su_root_ssh_scp()
{
    local passwd="$1"

    local username_and_ip="$2"
    ## sudo到root场景，不需要加-t参数创建一个伪终端会话
    username_and_ip=$(echo "${username_and_ip}" | sed "s/^-t[ ]*//")

    local exec_cmd="$3"
    local args="$4"
    local last_arg="${@: -1}"
    echo ${last_arg} | grep "dv_user_" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        args=""
        dv_user_pwd=$(echo ${last_arg}|awk -F'dv_user_' '{print $2}')
    else
        dv_user_pwd=""
    fi
    echo "${passwd} host:passwd ${dv_user_pwd} dvuser:passwd\n" | ${UTILS_PATH}/su_root_ssh_scp.exp "${i2k_ssh_port}" "${username_and_ip}" "${exec_cmd}" "${args}"
}

function ssh_scp()
{
    option="$1"
    passwd="$2"
    comand1="$3"
    comand2="$4"
    local last_arg="${@: -1}"
    echo ${last_arg} | grep "dv_user_" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        comand2=""
        last_arg=$(echo ${last_arg}|awk -F'dv_user_' '{print $2}')
    fi

    if [ "X$(whoami)" == "Xroot" ];then
        echo "${passwd} host:passwd ${last_arg} dvuser:passwd\n" | ${UTILS_PATH}/ssh_scp.exp "${option}" "${i2k_ssh_port}" "${comand1}" "${comand2}"
    else
        eval "${option} ${i2k_ssh_port} -o StrictHostKeyChecking=no ${comand1} ${comand2}"
    fi
}

##############################
# when ssh is using the same port of ssh, seperate it to a customize port
##############################
function auto_smart_ssh () {
    if [ "${is_upgrade}" != "Yes" ];then
        check_remote_user_pwd "$1"
    fi
    if [ "X${SUDO_USER}" != "X" ];then
        comand1=$(echo "$2"|${PreSet_PATH}/UniEp/python/bin/python -c "import sys;print(sys.stdin.readline().strip().split(' ',1)[0],end='')")
        comand2=$(echo "$2"|${PreSet_PATH}/UniEp/python/bin/python -c "import sys;tmp_str=sys.stdin.readline().strip();b = tmp_str.split(' ',1)[1] if len(tmp_str.split(' ',1)) == 2 else ''; print( b ,end='')")
        if [ "X${comand1}" == "X-t" ];then
            comand1=$(echo "$2"|${PreSet_PATH}/UniEp/python/bin/python -c "import sys;print(sys.stdin.readline().strip().split(' ',2)[1],end='')")
            comand1="-t ${comand1}"
            comand2=$(echo "$2"|${PreSet_PATH}/UniEp/python/bin/python -c "import sys;tmp_str=sys.stdin.readline().strip();b = tmp_str.split(' ',2)[2] if len(tmp_str.split(' ',2)) == 3 else ''; print( b ,end='')")
        fi
        echo "$2 $3" |grep -w ${SUDO_USER} >> /dev/null 2>&1
        if [ $? -eq 0 ];then
            ## 容器化场景，需要先sudo su - 切换为root用户，再执行命令
            if [ "X${SUDO_USER_COMMAND_IS_SU}" == "XYES" ]; then
                ## 统一去掉command2开头结尾的引号
                echo "${comand2}" | grep "^\"" >/dev/null 2>&1
                if [ $? -eq 0 ]; then
                    comand2=$(echo "${comand2}" | sed 's#^"##1' | sed 's#"$##1')
                fi
                su_root_ssh_scp "$1" "${comand1}" "${comand2}" "$3"
                return $?
            else
                ssh_scp "ssh -q -p" "$1" "${comand1} sudo ${comand2} " "$3"
                return $?
            fi
        fi
    fi
    ssh_scp "ssh -q -p" "$1" "$2" "$3"
    return $?
}

##############################
# when scp is using the same port of ssh, seperate it to a customize port
##############################
function auto_scp () {
    ssh_scp "scp -P" "$1" "$2" "$3"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "scp $2 $3 failed , please check."
        exit 1
    fi
}

function auto_scp_dir () {
    ssh_scp "scp -P" "$1" "-r $2" "$3"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "scp $2 $3 failed , please check."
        exit 1
    fi
}

function add_back_flash_to_special_char()
{
    local passwd_to_add=$1
    local npsdwCount=$(echo "$passwd_to_add" | wc -c | awk '{print $1}')
    local str='`"\$'
    local strCount=$(echo $str | wc -c | awk '{print $1}')
    local tmpstr=""
    local char=""
    for (( i=1; i <"$npsdwCount"; i++ ))
    do
            let c=$i-1
            tmpstr["$c"]=$(echo "$passwd_to_add" | cut -c"$i")
            for (( k=1; k <"$strCount"; k++ ))
            do
                    char=$(echo $str | cut -c"$k")
                    if [ "${tmpstr[$c]}" == "$char" ]; then
                            tmpstr[$c]="\\""${tmpstr[$c]}"
                    fi
            done
    done
    local indCount=${#tmpstr[@]}
    local pwdstr="${tmpstr[0]}"
    for (( j=1; j <"$indCount"; j++ ))
    do
            pwdstr="$pwdstr""${tmpstr[$j]}"
    done
    local passwd_to_add="$pwdstr"
    local passwd_added=$(echo $passwd_to_add | sed "s/'/\\\'/g")
    echo "${passwd_added}"
}

function import_properties()
{
    local cfg_file="${UTILS_PATH}/DV_config.properties.tmp"
    
    log_echo "INFO" "Begin to import ${cfg_file}." 
    if [ ! -f ${cfg_file} ];then
        log_echo "ERROR" "${cfg_file} not exist, please check!"
        exit_script
    fi
    
    . ${cfg_file}
    if [ $? -ne 0 ]; then
        log_echo "ERROR" "import ${cfg_file} error, pls check by command \". ${cfg_file}\""
        exit_script
    fi
}

function verify_pwd_rules() {
    upper_letter=0
    lower_letter=0
    digit=0
    special_letter=0
    password=$1
    echo "${password}" | grep "[[:upper:]]" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        upper_letter=1
    fi
    echo "${password}" | grep "[[:lower:]]" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        lower_letter=1
    fi
    echo "${password}" | grep "[[:digit:]]" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        digit=1
    fi
    echo "${password}" | grep "\[" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        special_letter=1
    fi
    echo "${password}" | grep -E '>|<|]|-' > /dev/null 2>&1
    if [ $? -eq 0 ];then
        special_letter=1
    fi
    echo "${password}" | grep '[!@#%^*()_+={}|,?.:;]' > /dev/null 2>&1
    if [ $? -eq 0 ];then
        special_letter=1
    fi
    flag=$((upper_letter+lower_letter+digit+special_letter))
    if [ $flag -eq 4 ];then
        return 0
    else
        return 1
    fi
}

function generate_random_pwd() {
    pwd_len=$1
    if [[ $pwd_len -lt 16 ]];then
        log_echo "ERROR" "the length of random password is ${pwd_len}, must be great than or equal 16."
    else
        for i in {1..20}
        do
            random_pwd=`tr -dc "\!\@\#\%\^\&\*\(\)\_\+\-\=\{\}\[\]\|\>\<\,\?\.\:\;A-Za-z0-9" </dev/random| head -c ${pwd_len}`
            verify_pwd_rules $random_pwd
            if [ $? -eq 0 ];then
                echo "$random_pwd"
                break
            elif [ $i -eq 20 ];then
                log_echo "ERROR" "Random password cannot pass the password verification."
                exit 1
            fi
        done
    fi
}

function upgrade_manifest_value()
{
    local cert_file_list=$1
    local new_pwd=$2
    local json_file=$3

    local storePass_line;
    local keyPass_line;

    for cert_name in ${cert_file_list}
    do
        encrypt_by_uniep "${new_pwd}" ""
        storePass_line="${pwd_return}"
        encrypt_by_uniep "${new_pwd}" ""
        keyPass_line="${pwd_return}"

        if [ "X${cert_name##*.}" == "Xp12" ];then
            update_cert_upgrade "PKCS12" "${json_file}" "${cert_name}" "${storePass_line}" "${keyPass_line}"
        elif [ "X${cert_name##*.}" == "Xjks" ];then
            update_cert_upgrade "JKS" "${json_file}" "${cert_name}" "${storePass_line}" "${keyPass_line}"
        fi
    done
}

function replace_json_value() {
    key=$1
    pwd=$2
    json_file=$3
    cycle_number=1
   
    for line in $( (${command_prefix} cat $json_file) | sed 's/\"//g;s/,//g;s/ //g' | grep "$key" | awk -F: '{print $2}'); do
        encrypt_by_uniep "${pwd}" ""
        pwd_encrypt="${pwd_return}"
        ${command_prefix} bash -c "sed -i \"s#$line#$pwd_encrypt#g\" $json_file"
    done  
}


function change_pwd_pkcs12() {
    old_store_pass=$1
    export new_pwd=$2
    file_name=$3
    ${command_prefix} bash -c "openssl pkcs12 -in ${file_name} -passin stdin -out ${file_name}.tmp -passout stdin<<EOF
${old_store_pass}
${new_pwd}
EOF
"
    ${command_prefix} bash -c "openssl pkcs12 -keypbe AES-256-CBC -certpbe AES-256-CBC -macalg sha256 -export -out ${file_name} -in ${file_name}.tmp -passin stdin -passout stdin<<EOF
${new_pwd}
${new_pwd}
EOF
"
    [[ $? -eq 0 ]] || exit 1
    ${command_prefix} rm "${file_name}.tmp"
    log_echo "INFO" "change password success!"
}

function change_pwd_jks() {
    old_key_password="$1"
    new_key_password="$2"
    keystore="$3"
    
    export LC_ALL=en_US.UTF8

    cd $(ls -d /opt/oss/rtsp/jre*/bin | head -1)
    alias_line=$(${command_prefix} bash -c "echo \"\" | ./keytool -v -list -keystore \"${keystore}\"  | grep PrivateKeyEntry -B 3 | grep \"Alias name\"")
    if [ "X${alias_line}" != "X" ];then
        alias_name=${alias_line#*:}
        ${command_prefix} bash -c "./keytool -keypasswd -keystore ${keystore} -alias ${alias_name}  <<EOF
$old_key_password
$new_key_password
$new_key_password
EOF
"
        [[ $? -eq 0 ]] || exit 1
    fi

    ${command_prefix} bash -c "./keytool -storepasswd -keystore ${keystore} <<EOF
$old_key_password
$new_key_password
$new_key_password
EOF
"
    [[ $? -eq 0 ]] || exit 1

    log_echo "INFO" "change password success!"
}

function copy_file()
{
    local source_path="$1"
    local dest_path="$2"
    log_echo "INFO" "start copy_file..."
    if [ -z "${source_path}" -o -z "${dest_path}" ];then
        log_echo "INFO" "source_path=${source_path} or dest_path=${dest_path} has null."
        return 0
    fi

    eval "$TEST_DIR ${source_path}"
    if [ $? -ne 0 ];then
        log_echo "INFO" "The path source_path=${source_path} is not exists.not need to copy it."
        return 0
    fi
    
    if [ -z "$(ls ${source_path})" ];then
        log_echo "INFO" "No file exists in the ${source_path} directory.not need to copy it."
        return 0
    fi

    eval "$TEST_DIR ${dest_path}"
    if [ $? -ne 0 ];then
        log_echo "INFO" "The path dest_path=${dest_path} is not exists.create it."
        ${command_prefix} mkdir -p ${dest_path}
    fi
    
    local dest_path_dir=${dest_path}
    for dir in $(${command_prefix} find ${source_path} -type d |sed "s#^${source_path}\(.*\)#${dest_path}\1#g");
    do
        eval "$TEST_DIR ${dir}"
        if [ $? -ne 0 ];then
            log_echo "INFO" "The dir ${dir} is not exists. mkdir ${dir}"
            ${command_prefix}mkdir -p ${dir}
        fi
    done
    
    local dest_path_file=""
    for file in $(${command_prefix}find ${source_path} -type f);
    do
        dest_path_file=$(echo "${file}" |sed "s#^${source_path}\(.*\)#${dest_path}\1#g")
        eval "$TEST_FILE ${dest_path_file}"
        if [ $? -ne 0 ];then
            log_echo "INFO" "Copy ${file} file to ${dest_path_file}"
            ${command_prefix}cp -rf ${file} ${dest_path_file}
        else
            log_echo "INFO" "The ${dest_path_file} is exists.not need copy."
        fi
    done
    log_echo "INFO" "copy_file end."
}

function preset_sign()
{
    log_echo "INFO" "start preset_sign..."
    chmod 770 ${install_path}/SOP/etc/
    chmod 770  -R ${UTILS_PATH}/sign_dv
    $TEST_FILE  ${i2k_user_home}/etc/sign_dv/uniagent_data_sign.pem
    local ret=$?
    if [ "X${is_upgrade}" == "XYes" -a $ret -eq 0 ];then
        ${TEST_DIR}  /opt/oss/share/SOP/DVEngineeringService/I2000/mttools/etc/i2000.pkg
        if [ $? -eq 0 ];then
            $TEST_FILE  /opt/oss/share/SOP/DVEngineeringService/I2000/mttools/etc/i2000.pkg/pkg_data_sign.pem
            if [ $? -ne 0 ];then
                cp_with_result "${UTILS_PATH}/sign_dv/pkg_data_sign.cfg" "${i2k_user_home}/etc/sign_dv/"
                cp_with_result "${UTILS_PATH}/sign_dv/pkg_data_sign.pem" "${i2k_user_home}/etc/sign_dv/"
                cp_with_result "${UTILS_PATH}/sign_dv/pkg_data_verify.pem" "${i2k_user_home}/etc/sign_dv/"
            fi
        fi

        log_echo "INFO" "Copy ossuser's sign_dv file to SOP to make sure the same when upgrade... pwd=$(pwd)"
        ${command_prefix} mkdir -p ${install_path}/SOP/etc/sign_dv
        cd /home
        for file in $(${command_prefix} find ${i2k_user_home}/etc/sign_dv -type f);
        do
            dest_path_file=$(echo "${file}" |sed "s#^${i2k_user_home}/etc/sign_dv\(.*\)#${install_path}/SOP/etc/sign_dv\1#g")
            ${command_prefix} cp -rpf ${file} ${dest_path_file}
        done
        cd -
    else
        
        ${command_prefix} mkdir -p ${install_path}/SOP/etc/sign_dv
        ${command_prefix} chmod 770 ${install_path}/SOP/etc/sign_dv
        ${command_prefix} cp -rf ${UTILS_PATH}/sign_dv/* ${install_path}/SOP/etc/sign_dv
        
        if [ "X$(whoami)" == "Xroot" ];then
            chown -R ${i2k_user_name}:${i2k_group_name} ${install_path}/SOP/etc/sign_dv
        fi

        ${command_prefix} mkdir -p ${i2k_user_home}/etc/sign_dv
        ${command_prefix} chmod 770 ${i2k_user_home}/etc/sign_dv
        ${command_prefix} cp -rf ${UTILS_PATH}/sign_dv/* ${i2k_user_home}/etc/sign_dv


        if [ "X$(whoami)" == "Xroot" ];then
            chown -R ${i2k_user_name}:${i2k_group_name} ${i2k_user_home}/etc/sign_dv
        fi
        
    fi


    ${command_prefix} chmod 700  ${install_path}/share/SOP/DVEngineeringService/etc/ssl/dv

    ${command_prefix} sed -i "s#uniagent_data_passphrase#passphrase#g" ${install_path}/SOP/etc/sign_dv/uniagent_data_sign.cfg
    ${command_prefix} sed -i "s#uniagent_data_key_id#key_id#g" ${install_path}/SOP/etc/sign_dv/uniagent_data_sign.cfg
    ${command_prefix} sed -i "s#uniagent_data_passphrase#passphrase#g" ${i2k_user_home}/etc/sign_dv/uniagent_data_sign.cfg
    ${command_prefix} sed -i "s#uniagent_data_key_id#key_id#g" ${i2k_user_home}/etc/sign_dv/uniagent_data_sign.cfg
    
    uniagent_date_verify_pem=$(${command_prefix} cat ${i2k_user_home}/etc/sign_dv/uniagent_data_verify.pem | base64 --wrap=0)
    if [ "X${is_upgrade}" == "XYes" ];then
        add_config_for_upgrapde "UNIAGENT_DATA_VERIFY_PEM" "${uniagent_date_verify_pem}" "${install_path}/SOP/etc/ssl/dv/registerConfigInfo.properties"
        add_config_for_upgrapde "UNIAGENT_DATA_VERIFY_PEM" "${uniagent_date_verify_pem}" "${i2k_user_home}/etc/ssl/dv/registerConfigInfo.properties"
    else
        log_echo "INFO" "install no need to create."
    fi
    

    if [ "X${is_upgrade}" == "XYes" ];then
        ${command_prefix} sed -i "s/^UNIAGENT_DATA_VERIFY_PEM=.*/UNIAGENT_DATA_VERIFY_PEM=${uniagent_date_verify_pem}/g" "${install_path}/SOP/etc/ssl/dv/registerConfigInfo.properties" > /dev/null 2>&1
        ${command_prefix} sed -i "s/^UNIAGENT_DATA_VERIFY_PEM=.*/UNIAGENT_DATA_VERIFY_PEM=${uniagent_date_verify_pem}/g" "${i2k_user_home}/etc/ssl/dv/registerConfigInfo.properties" > /dev/null 2>&1
        ${command_prefix} sed -i "s/^UNIAGENT_DATA_VERIFY_PEM=.*/UNIAGENT_DATA_VERIFY_PEM=${uniagent_date_verify_pem}/g" "${install_path}/share/SOP/DVEngineeringService/etc/ssl/dv/registerConfigInfo.properties" > /dev/null 2>&1
    fi

    
    ${command_prefix} chmod 700 ${install_path}/SOP/etc/sign_dv
    ${command_prefix} chmod 700  ${install_path}/SOP/etc/sign_dv/release_integrity
    ${command_prefix} bash -c "find ${install_path}/SOP/etc/sign_dv -type f -exec chmod 600 {} \; " > /dev/null 2>&1
    ${command_prefix} bash -c "find ${install_path}/SOP/etc/sign_dv -type d -exec chmod 700 {} \;" > /dev/null 2>&1
  


    ${command_prefix} chmod 700 ${i2k_user_home}/etc/sign_dv
    ${command_prefix} chmod 700  ${i2k_user_home}/etc/sign_dv/release_integrity
    ${command_prefix} bash -c "find ${i2k_user_home}/etc/sign_dv -type f -exec chmod 600 {} \; " > /dev/null 2>&1
    ${command_prefix} bash -c "find ${i2k_user_home}/etc/sign_dv -type d -exec chmod 700 {} \; " > /dev/null 2>&1
    
    chmod 750 ${install_path}/SOP/etc/

    log_echo "INFO" "preset_sign end."
    return 0
    exit 0
}

function preset_dvca()
{
    uniagent_date_verify_pem=$(${command_prefix} cat ${i2k_user_home}/etc/sign_dv/uniagent_data_verify.pem | base64 --wrap=0)
    if [ "X${is_upgrade}" != "XYes" ];then
        if [ ! -d ${i2k_user_home}/etc/ssl/dvca ];then
            log_echo "INFO" "create dvca to for cmp:${i2k_user_home}/etc/ssl/dvca"
            ${command_prefix} mkdir -p ${i2k_user_home}/etc/ssl/dvca
        fi

        local CREATE_MODE=$(cat ${PreSet_PATH}/dvca/dv-ca.properties|grep CREATE_MODE |awk -F'=' '{print $2}')
        local CA_CHAIN_FILE=$(cat ${PreSet_PATH}/dvca/dv-ca.properties|grep CA_CHAIN_FILE |awk -F'=' '{print $2}')
        ##二级网管证书CA_CHAIN_FILE不会为空，预安装场景CA_CHAIN_FILE为空
        if [ "X${CREATE_MODE}" == "X1" -a -n "${CA_CHAIN_FILE}" ];then
            log_echo "INFO" "${CREATE_MODE}=1 and ${CA_CHAIN_FILE} is not empty,level-2 NMS Scenario."
            cp_with_result "${PreSet_PATH}/dvca/DigitalView_CA.key" "${i2k_user_home}/etc/ssl/dvca"
            cp_with_result "${PreSet_PATH}/dvca/sub_ca.cer" "${i2k_user_home}/etc/ssl/dvca"
            cp_with_result "${PreSet_PATH}/dvca/root_ca.pem" "${i2k_user_home}/etc/ssl/dvca"
        elif [ "X${CREATE_MODE}" == "X1" -a -z "${CA_CHAIN_FILE}" ];then
            log_echo "INFO" "preinstallation scenario."
            cp_with_result "${PreSet_PATH}/dvca/dv_ca.crt" "${i2k_user_home}/etc/ssl/dvca"
            cp_with_result "${PreSet_PATH}/dvca/dv_ca.key" "${i2k_user_home}/etc/ssl/dvca"
        else
            log_echo "INFO" "not need to copy the certificate."
        fi

        cp_with_result "${PreSet_PATH}/dvca/dv-ca.properties" "${i2k_user_home}/etc/ssl/dvca"
        cp_with_result "${PreSet_PATH}/certificate/registerConfigInfo.properties" "${i2k_user_home}/etc/ssl/dvca"

        add_config "UNIAGENT_DATA_VERIFY_PEM" "${uniagent_date_verify_pem}" "${i2k_user_home}/etc/ssl/dvca/registerConfigInfo.properties"

        ${command_prefix} chmod 750  ${i2k_user_home}/etc
        ${command_prefix} chmod 750  ${i2k_user_home}/etc/ssl
        ${command_prefix} chmod -R 750  ${i2k_user_home}/etc/ssl/dvca
        ${command_prefix}  chown -R ${i2k_user_name}:${i2k_group_name} ${i2k_user_home}/etc/ssl/dvca
        log_echo "INFO" "preset_dvca end."
    else
        log_echo "INFO" "upgrade no need to preset_dvca."
    fi
}

function add_config_for_upgrapde()
{
    local key="$1"
    local value="$2"
    local config="$3"
    
    ${command_prefix} cat "${config}" | grep -w "${key}"  > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "${key} exist in ${config}, not need to add"
        return 0
    fi
    log_echo "INFO" "add ${key} to ${config}"
    
    ${command_prefix} bash -c "echo ' ' >> ${config}"
    ${command_prefix} bash -c "echo ${key}=${value} >> ${config}"
    return 0
}

function add_config()
 
{
    local key="$1"
    local value="$2"
    local config="$3"
    
    ${command_prefix} cat "${config}" | grep "${key}"  > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "${key} exist in ${config}, not need to add"
        return 0
    fi
    log_echo "INFO" "add ${key} to ${config}"
 
    ${command_prefix} bash -c "echo \"\" >> \"${config}\""
    ${command_prefix} bash -c "echo \"${key}=${value}\" >> \"${config}\""
    if [ "X$(whoami)" == "Xroot" ];then
        chown "${i2k_user_name}:${i2k_group_name}" "${config}"
    fi
 
    return 0
}

function preset_cipher_dv()
{
    chmod 770 ${install_path}/SOP/etc
    if [ ! -d ${install_path}/SOP/etc/cipher_dv ];then
        log_echo "INFO" "create cipher_dv to for cmp:${install_path}/SOP/etc/cipher_dv"
        ${command_prefix} mkdir -p ${install_path}/SOP/etc/cipher_dv
    fi
    ${command_prefix} chmod 700 ${install_path}/SOP/etc/cipher_dv
    if [ "X$(whoami)" == "Xroot" ];then
        chown -R ${i2k_user_name}:${i2k_group_name} ${install_path}/SOP/etc/cipher_dv
    fi

    
    if [ ! -d ${i2k_user_home}/etc/cipher_dv ];then
        log_echo "INFO" "create cipher_dv to for cmp:${i2k_user_home}/etc/cipher_dv"
        ${command_prefix} mkdir -p ${i2k_user_home}/etc/cipher_dv
    fi
    ${command_prefix} chmod 700 ${i2k_user_home}/etc/cipher_dv
    if [ "X$(whoami)" == "Xroot" ];then
        chown -R ${i2k_user_name}:${i2k_group_name} ${i2k_user_home}/etc/cipher_dv
    fi
    chmod 750 ${install_path}/SOP/etc
    return 0
}

function random_pwd()
{
    eval "${TEST_FILE} ${i2k_user_home}/etc/ssl/dv/random_pwd_success"
    if [ $? -eq 0 -o "X${need_radompwd}" != "XYes" ];then
        log_echo "INFO" "random pwd already execute,not need to do again"
        return 0
    fi    

    log_echo "INFO" "random pwd for certificate"
    
    new_pwd=$(generate_random_pwd 16)
    if [ "X$(whoami)" == "Xroot" ];then
          new_pwd=$(su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python -c \"from random import shuffle;pwd_list = list('$new_pwd');shuffle(pwd_list);print(''.join(pwd_list))\"")
    else
          new_pwd=$(source /opt/oss/manager/bin/engr_profile.sh;python -c "from random import shuffle;pwd_list = list('$new_pwd');shuffle(pwd_list);print(''.join(pwd_list))")
    fi

    cert_file_list="controller_server.p12 outserver.p12 caTrustStore.jks nodeKeyStore.jks openapi_server.p12 uniagent_client.jks foreign_client.p12"
    for cert_name in ${cert_file_list}
    do
        eval "${TEST_FILE} ${install_path}/SOP/etc/ssl/dv/${cert_name}"
        if [ $? -eq 0 ];then

            if [ "X$(whoami)" == "Xroot" ];then
                store_pass=$(cat "${install_path}/SOP/etc/ssl/dv/manifest.json" | su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python -c \"import json;import sys;datas = json.load(sys.stdin);print(datas['filelist']['${cert_name}']['storePass'])\"")
                store_pass_decrypt=$(su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('$store_pass'); print(tmp)\"")
            else
                store_pass=$(${command_prefix} cat "${install_path}/SOP/etc/ssl/dv/manifest.json" | python -c "import json;import sys;datas = json.load(sys.stdin);print(datas['filelist']['${cert_name}']['storePass'])")
                store_pass_decrypt=$(source /opt/oss/manager/bin/engr_profile.sh;python -c "from util import ossext; tmp=ossext.Cipher.decrypt('$store_pass'); print(tmp)")
            fi

            if [ "${cert_name##*.}" == "p12" ];then
                change_pwd_pkcs12 "$store_pass_decrypt" "$new_pwd" "${install_path}/SOP/etc/ssl/dv/${cert_name}"
            elif [ "${cert_name##*.}" == "jks" ];then
                change_pwd_jks "$store_pass_decrypt" "$new_pwd" "${install_path}/SOP/etc/ssl/dv/${cert_name}"
            fi
        fi
        eval "${TEST_FILE} ${i2k_user_home}/etc/ssl/dv/${cert_name}"
        if [ $? -eq 0 ];then
            if [ "X$(whoami)" == "Xroot" ];then
                store_pass=$(cat "${i2k_user_home}/etc/ssl/dv/manifest.json" | su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python -c \"import json;import sys;datas = json.load(sys.stdin);print(datas['filelist']['${cert_name}']['storePass'])\"")
                store_pass_decrypt=$(su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('$store_pass'); print(tmp)\"")
            else
                store_pass=$(${command_prefix} cat "${i2k_user_home}/etc/ssl/dv/manifest.json" | python -c "import json;import sys;datas = json.load(sys.stdin);print(datas['filelist']['${cert_name}']['storePass'])")
                store_pass_decrypt=$(source /opt/oss/manager/bin/engr_profile.sh;python -c "from util import ossext; tmp=ossext.Cipher.decrypt('$store_pass'); print(tmp)")
            fi
            if [ "${cert_name##*.}" == "p12" ];then
                change_pwd_pkcs12 "$store_pass_decrypt" "$new_pwd" "${i2k_user_home}/etc/ssl/dv/${cert_name}"
            elif [ "${cert_name##*.}" == "jks" ];then
                change_pwd_jks "$store_pass_decrypt" "$new_pwd" "${i2k_user_home}/etc/ssl/dv/${cert_name}"
            fi
        fi
    done
    
    replace_json_value "storePass" "$new_pwd" "${install_path}/SOP/etc/ssl/dv/manifest.json"
    replace_json_value "keyPass" "$new_pwd" "${install_path}/SOP/etc/ssl/dv/manifest.json"
    replace_json_value "storePass" "$new_pwd" "${i2k_user_home}/etc/ssl/dv/manifest.json"
    replace_json_value "keyPass" "$new_pwd" "${i2k_user_home}/etc/ssl/dv/manifest.json"

    ${command_prefix} touch "${i2k_user_home}/etc/ssl/dv/random_pwd_success"
}
function delete_old_cipher()
{
    if [ "X$(whoami)" == "Xroot" ];then
          cipher_mode_now=$(su - ossadm  -c "source /opt/oss/manager/bin/engr_profile.sh; printenv CIPHER_MODE")
    else
          cipher_mode_now=$(source /opt/oss/manager/bin/engr_profile.sh; printenv CIPHER_MODE)
    fi
    if [ "X${cipher_mode_now}" == "XKMC" ];then
        if [ ! -f ${install_path}/manager/etc/cipher/redis_shared.ksf  -a ! -f  ${install_path}/manager/etc/cipher/service_shared.ksf  ];then
            log_echo "INFO" "begin to delete the old cipher"
            [ ! -d ${install_path}/SOP/etc/cipher.bak ] &&  chmod 770 ${install_path}/SOP/etc && ${command_prefix} cp -a ${install_path}/SOP/etc/cipher ${install_path}/SOP/etc/cipher.bak;chmod 750 ${install_path}/SOP/etc
            ${command_prefix} rm -f  ${install_path}/SOP/etc/cipher/base.ksf
            ${command_prefix} rm -f  ${install_path}/SOP/etc/cipher/common_shared.ksf
            [ ! -d ${install_path}/manager/etc/cipher.bak ] && cp -a ${install_path}/manager/etc/cipher ${install_path}/manager/etc/cipher.bak
            rm -f  ${install_path}/manager/etc/cipher/base.ksf
            rm -f  ${install_path}/manager/etc/cipher/common_shared.ksf
        fi
    fi
}

function random_pwd_for_upgrade()
{

    eval "${TEST_FILE} ${i2k_user_home}/etc/ssl/dv/random_pwd_success"
    if [ $? -eq 0 -o "X${need_radompwd}" != "XYes" ];then
        log_echo "INFO" "random pwd already execute,not need to do again"
        return 0
    fi    

    log_echo "INFO" "random pwd for certificate"
    
    new_pwd=$(generate_random_pwd 16)
    new_pwd=$(source /opt/oss/manager/bin/engr_profile.sh ;${PYTHONHOME}/bin/python -c "from random import shuffle;pwd_list = list('$new_pwd');shuffle(pwd_list);print(''.join(pwd_list))")
   

    cert_file_list="controller_server.p12 outserver.p12 caTrustStore.jks nodeKeyStore.jks openapi_server.p12 uniagent_client.jks foreign_client.p12"
    for cert_name in ${cert_file_list}
    do
        eval "${TEST_FILE} ${install_path}/SOP/etc/ssl/dv/${cert_name}"
        if [ $? -eq 0 ];then

           
            store_pass=$(source /opt/oss/manager/bin/engr_profile.sh;${command_prefix} cat "${install_path}/SOP/etc/ssl/dv/manifest.json" | ${PYTHONHOME}/bin/python -c "import json;import sys;datas = json.load(sys.stdin);print(datas['filelist']['${cert_name}']['storePass'])")
            store_pass_decrypt=$(source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python -c "from util import ossext; tmp=ossext.Cipher.decrypt('$store_pass'); print(tmp)")
            

            if [ "${cert_name##*.}" == "p12" ];then
                change_pwd_pkcs12 "$store_pass_decrypt" "$new_pwd" "${install_path}/SOP/etc/ssl/dv/${cert_name}"
            elif [ "${cert_name##*.}" == "jks" ];then
                change_pwd_jks "$store_pass_decrypt" "$new_pwd" "${install_path}/SOP/etc/ssl/dv/${cert_name}"
            fi
        fi
        eval "${TEST_FILE} ${i2k_user_home}/etc/ssl/dv/${cert_name}"
        if [ $? -eq 0 ];then
           
            store_pass=$(source /opt/oss/manager/bin/engr_profile.sh;${command_prefix} cat "${i2k_user_home}/etc/ssl/dv/manifest.json" | ${PYTHONHOME}/bin/python -c "import json;import sys;datas = json.load(sys.stdin);print(datas['filelist']['${cert_name}']['storePass'])")
            store_pass_decrypt=$(source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python -c "from util import ossext; tmp=ossext.Cipher.decrypt('$store_pass'); print(tmp)")
        
            if [ "${cert_name##*.}" == "p12" ];then
                change_pwd_pkcs12 "$store_pass_decrypt" "$new_pwd" "${i2k_user_home}/etc/ssl/dv/${cert_name}"
            elif [ "${cert_name##*.}" == "jks" ];then
                change_pwd_jks "$store_pass_decrypt" "$new_pwd" "${i2k_user_home}/etc/ssl/dv/${cert_name}"
            fi
        fi
    done
    
    upgrade_manifest_value "${cert_file_list}" "$new_pwd" "${install_path}/SOP/etc/ssl/dv/manifest.json"
    upgrade_manifest_value "${cert_file_list}" "$new_pwd" "${i2k_user_home}/etc/ssl/dv/manifest.json"
    
    ${command_prefix} touch "${i2k_user_home}/etc/ssl/dv/random_pwd_success"

}

function cp_with_result()
{
    ${command_prefix} bash -c "cp -rf $1 $2"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "cp -rf $1 $2 failed"
        exit 1
    else
        log_echo "INFO" "cp -rf $1 $2 successful"
    fi
}

function add_cert_upgrade()
{
    if [  "X$1" == "XJKS" -o "X$1" == "XPKCS12" ];then
        ${command_prefix} bash -c "echo \"pwd:$4 pwd:$5\" | ${OSSUSER_PYTHON_HOME}/bin/python ${UTILS_PATH}/add_cert.py \"$1\" \"$2\" \"$3\" "
    else
        ${command_prefix} bash -c "echo \"pwd:$4\" | ${OSSUSER_PYTHON_HOME}/bin/python ${UTILS_PATH}/add_cert.py \"$1\" \"$2\" \"$3\""
    fi
    if [ $? -ne 0 ];then
        log_echo "ERROR" "add $3 to $2 failed"
        exit 1
    else
        log_echo "INFO" "add $3 to $2 successful"
    fi

}

function update_cert_upgrade()
{
    ${command_prefix} bash -c "echo \"pwd:$3 pwd:$4 pwd:$5\" | ${OSSUSER_PYTHON_HOME}/bin/python ${UTILS_PATH}/add_cert.py "update_jks_or_pkcs12_cert" \"$1\" \"$2\" "
    if [ $? -ne 0 ];then
        log_echo "ERROR" "add $3 to $2 failed"
        exit 1
    else
        log_echo "INFO" "add $3 to $2 successful"
    fi

}

function initial_certificate()
{
    if [ "X${is_upgrade}" != "XYes" ];then
        log_echo "INFO" "initial certificate to ${install_path}/SOP/etc/ssl/dv"
        
        rm -rf ${UTILS_PATH}/certificate/demoCA
        
        if [ ! -d "${install_path}/SOP/apps" ];then
        
            chown -R ossadm:${i2k_group_name} ${install_path}/SOP
            
        fi
        
        chown -R ${i2k_user_name}:${i2k_group_name} ${install_path}/SOP/etc/ssl
        
        chmod 750 ${install_path}/SOP/etc/ssl
        
        chmod 750 ${install_path}/SOP
        
        chmod 750 ${install_path}/SOP/etc

        chmod -R ossadm:${i2k_group_name} ${install_path}/SOP

        chmod -R ossadm:${i2k_group_name} ${install_path}/SOP/etc
        
        chown -R ${i2k_user_name}:${i2k_group_name} ${i2k_user_home}/etc 
        
        chmod -R 700 ${i2k_user_home}/etc
    else
        log_echo "INFO" "begin to initial_certificate"
        OSSUSER_PYTHON_HOME=$(ls -d /opt/oss/rtsp/python-*|head -1)

        eval "${TEST_DIR} ${i2k_user_home}/etc/ssl/dv"
        if [ $? -ne 0 ];then
            log_echo "INFO" "${i2k_user_home}/etc/ssl/dv is not exist,do not generate a certificate during the upgrade."
            return 0
        fi

        eval "${TEST_DIR} ${install_path}/SOP/etc/ssl/dv"
        if [ $? -ne 0 ];then
            ${command_prefix} mkdir -p ${install_path}/SOP/etc/ssl/dv
        fi
        eval "${TEST_FILE} ${i2k_user_home}/etc/ssl/dv/registerConfigInfo.properties"
        if [ $? -ne 0 ];then
            log_echo "INFO" "begin to cp registerConfigInfo.properties"
            cp_with_result  ${UTILS_PATH}/certificate/registerConfigInfo.properties ${i2k_user_home}/etc/ssl/dv/
            cp_with_result  ${UTILS_PATH}/certificate/registerConfigInfo.properties ${install_path}/SOP/etc/ssl/dv/
        fi

        eval "${TEST_FILE} ${i2k_user_home}/etc/ssl/dv/serverkey.keystore"
        if [ $? -ne 0 ];then
            log_echo "INFO" "begin to cp serverkey.keystore"
            cp_with_result ${UTILS_PATH}/certificate/serverkey.keystore ${i2k_user_home}/etc/ssl/dv/
            cp_with_result ${UTILS_PATH}/certificate/serverkey.keystore ${install_path}/SOP/etc/ssl/dv/
            cp_with_result ${UTILS_PATH}/certificate/servertrust.keystore ${i2k_user_home}/etc/ssl/dv/
            cp_with_result ${UTILS_PATH}/certificate/servertrust.keystore ${install_path}/SOP/etc/ssl/dv/
            ${command_prefix} bash -c "cat ${UTILS_PATH}/certificate/dv_ca.crt >> ${i2k_user_home}/etc/ssl/dv/uniagent_ca.pem"
            ${command_prefix} bash -c "cat ${UTILS_PATH}/certificate/dv_ca.crt >> ${install_path}/SOP/etc/ssl/dv/uniagent_ca.pem"
        fi

        eval "${TEST_FILE} ${i2k_user_home}/etc/ssl/dv/uniagent_client_trust.jks"
        if [ $? -ne 0 ];then
            log_echo "INFO" "begin to cp uniagent_client_trust.jks"
            chmod 660 ${UTILS_PATH}/certificate/uniagent_client_trust.jks
            cp_with_result ${UTILS_PATH}/certificate/uniagent_client_trust.jks ${install_path}/SOP/etc/ssl/dv/
            cp_with_result ${UTILS_PATH}/certificate/uniagent_client_trust.jks ${i2k_user_home}/etc/ssl/dv/
        fi

        eval "${TEST_FILE} ${i2k_user_home}/etc/ssl/dv/dv_ca.crt"
        log_echo "INFO" "begin to cp dv_ca.crt"
        chmod 660 ${UTILS_PATH}/certificate/dv_ca*
        cp_with_result "${UTILS_PATH}/certificate/dv_ca.crt" "${i2k_user_home}/etc/ssl/dv/"
        cp_with_result "${UTILS_PATH}/certificate/dv_ca.p12" "${i2k_user_home}/etc/ssl/dv/"
        cp_with_result "${UTILS_PATH}/certificate/dv_ca.crt"  "${install_path}/SOP/etc/ssl/dv/"
        cp_with_result "${UTILS_PATH}/certificate/dv_ca.p12"  "${install_path}/SOP/etc/ssl/dv/"

        ${command_prefix} bash -c "find ${i2k_user_home}/etc/ssl/dv -name dv_ca* -exec chmod 600 {} \; " > /dev/null 2>&1
        ${command_prefix} bash -c "find ${install_path}/SOP/etc/ssl/dv -name dv_ca* -exec chmod 600 {} \; " > /dev/null 2>&1
        chmod 600 ${UTILS_PATH}/certificate/dv_ca*

        servertrust_key_pass=$(grep -A 3 servertrust.keystore ${UTILS_PATH}/certificate/manifest.json | grep keyPass | awk -F ':' '{print $2}' | tr -d " |\"|,")
        local servertrust_store_pass=$(grep -A 3 servertrust.keystore ${UTILS_PATH}/certificate/manifest.json | grep storePass | awk -F ':' '{print $2}' | tr -d " |\"|,")
        if [ -f "${install_path}/SOP/etc/ssl/dv/manifest.json" ];then
           add_cert_upgrade "PKCS12" "${install_path}/SOP/etc/ssl/dv/manifest.json" "dv_ca.p12" "${servertrust_store_pass}" "${servertrust_key_pass}"
        fi
        add_cert_upgrade "PKCS12" "${i2k_user_home}/etc/ssl/dv/manifest.json" "dv_ca.p12" "${servertrust_store_pass}" "${servertrust_key_pass}"

        ${command_prefix} grep -wq "dv_ca.p12" "${i2k_user_home}/etc/ssl/dv/manifest.json"
        if [ $? -ne 0 ];then
            log_echo "INFO" "begin to cp dv_ca.crt"
            chmod 660 ${UTILS_PATH}/certificate/dv_ca*
            cp_with_result "${UTILS_PATH}/certificate/dv_ca.crt" "${i2k_user_home}/etc/ssl/dv/"
            cp_with_result "${UTILS_PATH}/certificate/dv_ca.p12" "${i2k_user_home}/etc/ssl/dv/"
            cp_with_result "${UTILS_PATH}/certificate/dv_ca.crt"  "${install_path}/SOP/etc/ssl/dv/"
            cp_with_result "${UTILS_PATH}/certificate/dv_ca.p12"  "${install_path}/SOP/etc/ssl/dv/"

            ${command_prefix} bash -c "find ${i2k_user_home}/etc/ssl/dv -name dv_ca* -exec chmod 600 {} \; " > /dev/null 2>&1
            ${command_prefix} bash -c "find ${install_path}/SOP/etc/ssl/dv -name dv_ca* -exec chmod 600 {} \; " > /dev/null 2>&1
            chmod 600 ${UTILS_PATH}/certificate/dv_ca*

            servertrust_key_pass=$(grep -A 3 servertrust.keystore ${UTILS_PATH}/certificate/manifest.json | grep keyPass | awk -F ':' '{print $2}' | tr -d " |\"|,")
            local servertrust_store_pass=$(grep -A 3 servertrust.keystore ${UTILS_PATH}/certificate/manifest.json | grep storePass | awk -F ':' '{print $2}' | tr -d " |\"|,")
            add_cert_upgrade "PKCS12" "${i2k_user_home}/etc/ssl/dv/manifest.json" "dv_ca.p12" "${servertrust_store_pass}" "${servertrust_key_pass}"
            add_cert_upgrade "PKCS12" "${install_path}/SOP/etc/ssl/dv/manifest.json" "dv_ca.p12" "${servertrust_store_pass}" "${servertrust_key_pass}"
        fi

        eval "${TEST_FILE} ${i2k_user_home}/etc/ssl/dv/ca.crt"
        if [ $? -ne 0 ];then
            log_echo "INFO" "begin to cp ca.crt"
            cp_with_result ${i2k_user_home}/etc/ssl/dv/dv_ca.crt     ${i2k_user_home}/etc/ssl/dv/ca.crt
            cp_with_result ${install_path}/SOP/etc/ssl/dv/dv_ca.crt  ${install_path}/SOP/etc/ssl/dv/ca.crt
        fi
        
        eval "${TEST_DIR} ${install_path}/SOP/etc/ssl/dv.bak"
        if [ $? -eq 0 ];then
            local dv_bak_file_list=$(${command_prefix} bash -c "ls ${install_path}/SOP/etc/ssl/dv.bak")
            [ -z "${dv_bak_file_list}" ] && ${command_prefix} rm -rf ${install_path}/SOP/etc/ssl/dv.bak
        fi
        
        ${TEST_DIR} /home/<USER>/etc/ssl/dv
        if [ $? -eq 0 ];then
            ${command_prefix} bash -c "[ -f /home/<USER>/etc/ssl/dv/DV_CA.cnf ] && rm -f /home/<USER>/etc/ssl/dv/DV_CA.cnf"
            ${command_prefix} bash -c "[ -f /home/<USER>/etc/ssl/dv/dv_ca.crt ] && rm -f /home/<USER>/etc/ssl/dv/dv_ca.crt"
            ${command_prefix} bash -c "[ -f /home/<USER>/etc/ssl/dv/dv_ca.csr ] &&  rm -f /home/<USER>/etc/ssl/dv/dv_ca.csr"
            ${command_prefix} bash -c "[ -f /home/<USER>/etc/ssl/dv/dv_ca.key ] &&  rm -f /home/<USER>/etc/ssl/dv/dv_ca.key"
            ${TEST_FILE} "/home/<USER>/i2k_install.properties"
            if [ $? -ne 0 ];then
                ${command_prefix} bash -c "[ -f /home/<USER>/etc/ssl/dv/dv_ca.p12 ] &&  rm -f /home/<USER>/etc/ssl/dv/dv_ca.p12"
            fi
        fi

        ${TEST_DIR} /opt/oss/SOP/etc/ssl/dv
        if [ $? -eq 0 ];then
            ${command_prefix} bash -c "[ -f /opt/oss/SOP/etc/ssl/dv/DV_CA.cnf ] &&  rm -f /opt/oss/SOP/etc/ssl/dv/DV_CA.cnf"
            ${command_prefix} bash -c "[ -f /opt/oss/SOP/etc/ssl/dv/dv_ca.crt ] &&  rm -f /opt/oss/SOP/etc/ssl/dv/dv_ca.crt"
            ${command_prefix} bash -c "[ -f /opt/oss/SOP/etc/ssl/dv/dv_ca.csr ] &&  rm -f /opt/oss/SOP/etc/ssl/dv/dv_ca.csr"
            ${command_prefix} bash -c "[ -f /opt/oss/SOP/etc/ssl/dv/dv_ca.key ] &&  rm -f /opt/oss/SOP/etc/ssl/dv/dv_ca.key"
            ${command_prefix} bash -c "[ -f /opt/oss/SOP/etc/ssl/dv/dv_ca.p12 ] &&  rm -f /opt/oss/SOP/etc/ssl/dv/dv_ca.p12"
        fi

        #reentry problem handling
        eval "${TEST_DIR} ${install_path}/SOP/etc/ssl/dv.bak"
        if [ $? -eq 0 ];then
            cp_with_result  "${install_path}/SOP/etc/ssl/dv.bak/*" "${install_path}/SOP/etc/ssl/dv/"
        else
            cp_with_result  "${install_path}/SOP/etc/ssl/dv/"   "${install_path}/SOP/etc/ssl/dv.bak/"
        fi
        eval "${TEST_DIR} ${i2k_user_home}/etc/ssl/dv.bak"
        if [ $? -eq 0 ];then
            cp_with_result  "${i2k_user_home}/etc/ssl/dv.bak/*" "${i2k_user_home}/etc/ssl/dv/"
        else
            cp_with_result   "${i2k_user_home}/etc/ssl/dv/"  "${i2k_user_home}/etc/ssl/dv.bak/"
        fi

        random_pwd_for_upgrade
        chmod 775 ${UTILS_PATH}
        local client_key_pass=$(${command_prefix} grep -A 3 client.key ${i2k_user_home}/etc/ssl/dv/manifest.json | grep keyPass | awk -F ':' '{print $2}' | tr -d ' |\"'|tr -d ' |\,')
        eval "${TEST_FILE} ${i2k_user_home}/etc/ssl/dv/client.crt"
        if [ $? -ne 0 -o "X${client_key_pass}" == "X" ];then
            log_echo "INFO" "begin to cp client.crt"
            chmod 660 ${UTILS_PATH}/certificate/client*
            cp_with_result "${UTILS_PATH}/certificate/client*" "${i2k_user_home}/etc/ssl/dv/"
            cp_with_result "${UTILS_PATH}/certificate/client*"  "${install_path}/SOP/etc/ssl/dv/"
            chmod 600 ${UTILS_PATH}/certificate/client*
            client_key_pass=$(grep -A 3 client.key ${UTILS_PATH}/certificate/manifest.json | grep keyPass | awk -F ':' '{print $2}' | tr -d ' |\"|,')
            # add_cert.py
            add_cert_upgrade "null" "${i2k_user_home}/etc/ssl/dv/manifest.json"   "client.key" "${client_key_pass}"
            add_cert_upgrade "null" "${install_path}/SOP/etc/ssl/dv/manifest.json" "client.key" "${client_key_pass}"

        fi
        local uniagent_client_key_pass=$(${command_prefix} grep -A 3 uniagent_client.jks ${i2k_user_home}/etc/ssl/dv/manifest.json | grep keyPass | awk -F ':' '{print $2}' | tr -d ' |\"|,')
        eval "${TEST_DIR} ${install_path}/SOP/apps/DVEngineeringService"
        local ret=$?
        eval "${TEST_FILE} ${i2k_user_home}/etc/ssl/dv/uniagent_client.jks"
        if [ $? -ne 0 -o  "X${uniagent_client_key_pass}" == "X" ] && [ $ret -eq 0 ];then
            log_echo "INFO" "begin to cp uniagent_client.jks"
            cp_with_result ${UTILS_PATH}/certificate/uniagent_client.jks ${i2k_user_home}/etc/ssl/dv/
            cp_with_result ${UTILS_PATH}/certificate/uniagent_client.jks ${install_path}/SOP/etc/ssl/dv/
            uniagent_client_key_pass=$(grep -A 3 uniagent_client.jks ${UTILS_PATH}/certificate/manifest.json | grep keyPass | awk -F ':' '{print $2}' | tr -d " |\"|,")
            local uniagent_client_store_pass=$(grep -A 3 uniagent_client.jks ${UTILS_PATH}/certificate/manifest.json | grep storePass | awk -F ':' '{print $2}' | tr -d " |\"|,")
            add_cert_upgrade "JKS" "${i2k_user_home}/etc/ssl/dv/manifest.json" "uniagent_client.jks" "${uniagent_client_store_pass}" "${uniagent_client_key_pass}"
            add_cert_upgrade "JKS" "${install_path}/SOP/etc/ssl/dv/manifest.json" "uniagent_client.jks" "${uniagent_client_store_pass}" "${uniagent_client_key_pass}"
        fi

        cp_with_result   "${i2k_user_home}/etc/ssl/dv/*"  "${install_path}/SOP/etc/ssl/dv/"
        ${command_prefix} bash -c "find ${i2k_user_home}/etc/ssl/dv -type f -exec chmod 600 {} \; " > /dev/null 2>&1
        ${command_prefix} bash -c "find ${install_path}/SOP/etc/ssl/dv  -type f -exec chmod 600 {} \; " > /dev/null 2>&1

        eval "${TEST_DIR} \"${install_path}/SOP/etc/ssl/dv.bak\""
        if [ $? -eq 0 ];then
            ${command_prefix} rm -rf ${install_path}/SOP/etc/ssl/dv.bak
        fi
        eval "${TEST_DIR} \"${i2k_user_home}/etc/ssl/dv.bak\""
        if [ $? -eq 0 ];then
            ${command_prefix} rm -rf ${i2k_user_home}/etc/ssl/dv.bak
        fi
        delete_old_cipher
    fi
    
    chmod 600 ${UTILS_PATH}/certificate/*
    
    initial_kafka_certificate "$1"
    log_echo "INFO" "initial certificate end"
    
    return 0
}

function create_kafka_server_certificate()
{
    local cert_name="$1"
    
    local subject="$2"
    
    local cert_type="$3"

    local cert_middle_name=$(echo $cert_name | awk -F '.' '{print $1}')
    
    log_echo "INFO" "Begin to create ${cert_name} certificate."
    
    local cert_pwd="$4"
    
    local ca_cert_pwd="$4"
    
    local node_ip="$5"
   
    openssl genpkey -algorithm RSA -outform PEM -out "${cert_middle_name}.key" -pkeyopt rsa_keygen_bits:4096 -pkeyopt rsa_keygen_pubexp:65537 -aes-256-cbc -pass stdin <<EOF
${cert_pwd}
EOF
    if [ $? -ne 0 ];then
        log_echo "ERROR" "create ${cert_middle_name}.key failed"
        
    fi
    
    if [ "X${cert_middle_name}" == "Xclient" ];then
        openssl rsa -in "${cert_middle_name}.key" -out "tmp.key" -aes256 -passin stdin -passout stdin <<EOF
${cert_pwd}
${cert_pwd}
${cert_pwd}
EOF
        mv "tmp.key" "${cert_middle_name}.key"
    fi

    openssl req -new -sha256 -sigopt rsa_padding_mode:pss -sigopt rsa_pss_saltlen:-1  -keyform PEM -key ${cert_middle_name}.key  -out  ${cert_middle_name}.csr  -subj "${subject}"  -reqexts v3_req  -passin stdin <<EOF
${cert_pwd}
EOF
    if [ $? -ne 0 ];then
        log_echo "ERROR" "create ${cert_middle_name}.csr failed"
        exit 1
    fi

    if [ -d "demoCA" ]
    then
      rm -r "demoCA"
    fi

    mkdir -p "demoCA/newcerts"
    chmod -R 700 demoCA

    touch "demoCA/index.txt"
    touch "demoCA/index.txt.attr"
    date +%s > "demoCA/serial"
    
    cat Kakfa_CA.cnf | grep "${node_ip}" 
    if [ $? -ne 0 ];then
        sed -i "/subjectAltName/d" Kakfa_CA.cnf
        sed -i "/alt_names/d" Kakfa_CA.cnf
        sed -i "/DNS.1/d" Kakfa_CA.cnf
        sed -i "/IP.1/d" Kakfa_CA.cnf
        
        echo "subjectAltName = @alt_names" >> Kakfa_CA.cnf
        echo "[ alt_names ]" >> Kakfa_CA.cnf
        echo "IP.1 = ${node_ip}" >> Kakfa_CA.cnf
    fi

    openssl ca -sigopt rsa_padding_mode:pss -sigopt rsa_pss_saltlen:-1 -config Kakfa_CA.cnf  -in ${cert_middle_name}.csr -out ${cert_middle_name}.crt -cert dv_ca.crt -keyfile dv_ca.key -passin stdin <<EOF
${ca_cert_pwd}
y
y
EOF

    if [ $? -ne 0 ];then
        log_echo "ERROR" "create ${cert_middle_name}.crt failed"
        exit 1
    fi

    pbe_option=""
    [ "X${cert_type}" != "XJKS" ] && pbe_option="-keypbe AES-256-CBC -certpbe AES-256-CBC -macalg sha256"
    openssl pkcs12 ${pbe_option} -export -in ${cert_middle_name}.crt -inkey ${cert_middle_name}.key -out ${cert_middle_name}.p12  -CAfile dv_ca.crt  -passin stdin  -passout stdin<<EOF
${cert_pwd}
${cert_pwd}
EOF
    if [ $? -ne 0 ];then
        log_echo "ERROR" "create ${cert_middle_name}.p12 failed"
        exit 1
    fi

    
    if [ "X${cert_type}" == "XJKS" ];then
    
        keytool_path=$(find /opt/oss/rtsp/ -name keytool | head -1)

        ${keytool_path} -importkeystore -v -srckeystore ${cert_middle_name}.p12 -srcstoretype pkcs12  -destkeystore ${cert_name} -deststoretype jks <<EOF
${cert_pwd}
${cert_pwd}
${cert_pwd}
EOF
        if [ $? -ne 0 ];then
            log_echo "ERROR" "create ${cert_name} failed"
            
        fi
    fi
    
    if [ "X${cert_type}" == "XJKS" ];then
        echo "pwd:${kafka_store_encode_password} pwd:${kafka_key_encode_password}" | python ${UTILS_PATH}/add_cert.py "JKS" "manifest.json" "${cert_name}"
    elif [ "X${cert_type}" == "XPKCS12" ];then
        echo "pwd:${kafka_store_encode_password} pwd:${kafka_key_encode_password}" | python ${UTILS_PATH}/add_cert.py "PKCS12" "manifest.json" "${cert_name}"
    fi
    
    find -name "${cert_middle_name}*" | grep -v "${cert_name}" | xargs -i rm -f {}

    rm -rf demoCA
    
    log_echo "INFO" "Create ${cert_name} certificate end."
}

function initial_kafka_certificate()
{
    eval "$TEST_FILE ${i2k_user_home}/etc/ssl/kafka/server.p12"
    if [ $? -eq 0 ];then
        log_echo "INFO" "kafka certificate already exist"
        ${command_prefix} chmod 700 ${install_path}/SOP/etc/ssl/kafka
        ${command_prefix} chmod 700 ${i2k_user_home}/etc/ssl/kafka
        ${command_prefix} bash -c "chmod -R 600 ${install_path}/SOP/etc/ssl/kafka/*"
        ${command_prefix} bash -c "chmod -R 600 ${i2k_user_home}/etc/ssl/kafka/*"
        return 0
    fi

    log_echo "INFO" "initial kafka certificate to ${install_path}/SOP/etc/ssl/kafka"
    
    local node_ip="$1"
    
    cd ${UTILS_PATH}/kafka/
    
    cat "manifest.json.bak" | grep "trust.jks" >/dev/null 2>&1
    if [ $? -ne 0 ];then
        rm -rf manifest.json.bak
    fi
    
    if [ -f "manifest.json.bak" ];then
        log_echo "INFO" "resume manifest.json."
        cp -f manifest.json.bak manifest.json
    else
        log_echo "INFO" "backup manifest.json for first time."
        cp -f manifest.json manifest.json.bak
    fi
    
    if [ -f "Kakfa_CA.cnf.bak" ];then
        log_echo "INFO" "resume Kakfa_CA.cnf."
        cp -f Kakfa_CA.cnf.bak Kakfa_CA.cnf.json
    else
        log_echo "INFO" "backup Kakfa_CA.cnf for first time."
        cp -f Kakfa_CA.cnf Kakfa_CA.cnf.bak
    fi
    
    if [ "X${is_upgrade}" == "XYes" ];then
        local kafka_ca_encode_cert_pwd=$(grep -A 3 trust.jks manifest.json | grep keyPass | awk -F ':' '{print $2}' | tr -d " |\"|,")

        if [ "X$(whoami)" == "Xroot" ];then
            local kafka_ca_cert_pwd=$(su - ossadm -c "source /opt/oss/manager/bin/engr_profile.sh;python -c \"from util import ossext; tmp=ossext.Cipher.decrypt('$kafka_ca_encode_cert_pwd'); print(tmp)\"")
        else
            local kafka_ca_cert_pwd=$(source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python -c "from util import ossext; tmp=ossext.Cipher.decrypt('$kafka_ca_encode_cert_pwd'); print(tmp)")
        fi

        encrypt_by_uniep "${kafka_ca_cert_pwd}" ""
        local kafka_store_encode_password="${pwd_return}"
        
        encrypt_by_uniep "${kafka_ca_cert_pwd}" ""
        local kafka_key_encode_password="${pwd_return}"
    fi
    
    create_kafka_server_certificate "server.p12"  "/C=CN/O=Huawei/OU=DigitalView/CN=KakfaServer" "PKCS12"  "${kafka_ca_cert_pwd}" "${node_ip}"
    create_kafka_server_certificate "client.p12"  "/C=CN/O=Huawei/OU=DigitalView/CN=KakfaClient" "PKCS12"  "${kafka_ca_cert_pwd}" "${node_ip}"

    chmod -R 770 ${UTILS_PATH}/kafka
    ${command_prefix} mkdir -p ${install_path}/SOP/etc/ssl/kafka
    
    cp_with_result ${UTILS_PATH}/kafka/trust.jks ${install_path}/SOP/etc/ssl/kafka/ 
    cp_with_result ${UTILS_PATH}/kafka/server.p12 ${install_path}/SOP/etc/ssl/kafka/ 
    cp_with_result ${UTILS_PATH}/kafka/client.p12 ${install_path}/SOP/etc/ssl/kafka/ 
    cp_with_result ${UTILS_PATH}/kafka/manifest.json ${install_path}/SOP/etc/ssl/kafka/ 
    cp_with_result ${UTILS_PATH}/kafka/dv_ca.crt ${install_path}/SOP/etc/ssl/kafka/ 
    cp_with_result ${UTILS_PATH}/kafka/dv_ca.key ${install_path}/SOP/etc/ssl/kafka/ 
    cp_with_result ${UTILS_PATH}/kafka/Kakfa_CA.cnf ${install_path}/SOP/etc/ssl/kafka/ 

    if [ "X$(whoami)" == "Xroot" ];then
          chown -R ${i2k_user_name}:${i2k_group_name} ${install_path}/SOP/etc/ssl
    fi

    ${command_prefix} chmod 750 ${install_path}/SOP/etc/ssl
    
    ${command_prefix} chmod 700 ${install_path}/SOP/etc/ssl/kafka

    ${command_prefix} bash -c "chmod -R 600 ${install_path}/SOP/etc/ssl/kafka/*"

    chmod 750 ${install_path}/SOP
    
    chmod 750 ${install_path}/SOP/etc

    chown ossadm:ossgroup ${install_path}/SOP

    chown ossadm:ossgroup  ${install_path}/SOP/etc
    
    ${command_prefix} mkdir -p ${i2k_user_home}/etc/ssl/kafka
    
    cp_with_result ${UTILS_PATH}/kafka/trust.jks  ${i2k_user_home}/etc/ssl/kafka 
    cp_with_result ${UTILS_PATH}/kafka/server.p12  ${i2k_user_home}/etc/ssl/kafka 
    cp_with_result ${UTILS_PATH}/kafka/client.p12  ${i2k_user_home}/etc/ssl/kafka 
    cp_with_result ${UTILS_PATH}/kafka/manifest.json  ${i2k_user_home}/etc/ssl/kafka
    cp_with_result ${UTILS_PATH}/kafka/dv_ca.crt  ${i2k_user_home}/etc/ssl/kafka/ 
    cp_with_result ${UTILS_PATH}/kafka/dv_ca.key ${i2k_user_home}/etc/ssl/kafka/
    cp_with_result ${UTILS_PATH}/kafka/Kakfa_CA.cnf  ${i2k_user_home}/etc/ssl/kafka 

    if [ "X$(whoami)" == "Xroot" ];then
          chown -R ${i2k_user_name}:${i2k_group_name} ${i2k_user_home}/etc
    fi

    ${command_prefix} bash -c "chmod  700 ${i2k_user_home}/etc"

    ${command_prefix} chmod 700 ${i2k_user_home}/etc/ssl/kafka

    ${command_prefix} bash -c "chmod -R 600 ${i2k_user_home}/etc/ssl/kafka/*"

    chmod -R 600 ${UTILS_PATH}/kafka/*
    
    cd -
    
    return 0
}

function set_acl_permission()
{
    if [ -d ${dvshare_install_path} ];then
        log_echo "INFO" "set acl permission for dvshare"
        setfacl -R -m d:u:${i2k_user_name}:rwx ${dvshare_install_path}
        setfacl -R -m d:g::- ${dvshare_install_path}
        setfacl -R -m d:m::rwx ${dvshare_install_path}
        setfacl -R -m u:${i2k_user_name}:rwx ${dvshare_install_path}
        setfacl -R -m g::- ${dvshare_install_path}
        setfacl -R -m m::rwx ${dvshare_install_path}
        find ${dvshare_install_path} -type f -exec chmod g-x {} +
        find ${dvshare_install_path} -type f -exec chmod 660 {} +
        find ${dvshare_install_path} -type d -exec chmod 770 {} +
    fi
    
    #change the priviledge for the file
    if [ -d "${i2k_install_path}/icnfg/webpages/var/data" ];then
        chmod 770 ${i2k_install_path}/icnfg/webpages/var/data
        find ${i2k_install_path}/icnfg/webpages/var/data -type d | xargs -i chmod 770 {}
        find ${i2k_install_path}/icnfg/webpages/var/data -type f | xargs -i chmod 660 {}
        setfacl -R -m d:u:ossuser:rwx ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m d:u:devdata:rwx ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m d:g::- ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m d:m::rwx ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m u:ossuser:rwx ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m u:devdata:rwx ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m g::- ${i2k_install_path}/icnfg/webpages/var/data
        setfacl -R -m m::rwx ${i2k_install_path}/icnfg/webpages/var/data
        find ${i2k_install_path}/icnfg/webpages/var/data -type  f | xargs -i chmod g-x {}
    fi
    
    local dir_list="${i2k_install_path}/backup /tmp/DVPreSet_upgrade /tmp/DVPreSet"
    local file_list="crt pem pfx jks p12"
    for dirTmp in ${dir_list};do
        if [ -d ${dirTmp} ];then
            for fileType in ${file_list};do
                find "${dirTmp}" -type f -a -not -perm 600 -a \( -name "*.${fileType}" \) -print0 | xargs -0 --no-run-if-empty chmod 600
            done
        fi
    done
    
    return 0
}

function kill_path_process()
{
    dest_path=$1
    ps -ef |grep -w ${dest_path} |grep -v grep >/dev/null 2>&1
    if [ $? -eq 0 ];then
        PIDS=$(${lsof_cmd} +D ${dest_path} 2>/dev/null|awk '{print $2}'|uniq|grep -vi PID)
        for pid in $PIDS
        do
            kill -9 $pid
        done
        PIDS=$(${lsof_cmd} ${dest_path} 2>/dev/null|awk '{print $2}'|uniq|grep -vi PID)
        if [ ! -f /.dockerenv ];then
            for pid in $PIDS
            do
                kill -9 $pid
            done
        fi
        ps -ef |grep -w ${dest_path} |grep -v grep | awk -F ' ' '{print $2}' | xargs -i kill -9 {}
    fi
}

function iplist_add_brackets()
{
    local iplist=$1
    local tmplist=""
    mkdir -p ${UTILS_PATH}/exetmp
    cd ${UTILS_PATH}/exetmp
    for ip in $(echo ${iplist}|sed 's/,/ /g')
    do
        tmplist="${tmplist} [$ip]"
    done

    newlist=$(echo ${tmplist}|sed 's/ /,/g')
    
    echo ${newlist}
    cd -  > /dev/null 2>&1
    rm -rf  ${UTILS_PATH}/exetmp
}

function skip_optcheck_for_virtualization()
{
    is_skip="No"
    df -Ph | grep -w /dev/mapper/vg_optoss-lv_optoss |grep -w /opt/oss > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "The uniepNode is mounted by virtualization, skip checking /opt for this."
        is_skip="Yes"
    fi
}

function skip_optcheck_for_uniep_independent_node()
{
    is_skip="No"
    if [ ! -z "`echo ${netWorkType} | grep -E "T|C|L"`" ];then
        log_echo "INFO" "The uniep is independent node, skip checking /opt for this."
        is_skip="Yes"
    fi
}

function check_ATAE_extend_disk()
{
    log_echo "INFO" "Begin to check ATAE_extend_disk."
    local nodetype="$1"
    local oper_type="$2"
    local size_not_enough_dir_list=""
    for line in `cat ${PreSet_PATH}/tools/ATAEExtend/disk_default.cfg`
    do
        if [ "X$line" == "X" ];then
            continue
        fi
        local dir_name=$(echo $line |awk -F'=' '{print $1}')
        if [ "${oper_type}" == "available_size" ]; then
            dir_default_size=$(echo $line |awk -F'=' '{print $2}')
        else
            dir_default_size=$(echo $line |awk -F'=' '{print $NF}')
        fi

        if [ "${dir_name}" == "swap" ];then
            local current_swap_size=$(free -g |grep -i "Swap:" |awk '{print $2}')
            local current_swap_size=$((current_swap_size+1))
            if [ ${current_swap_size} -lt ${dir_default_size} ] ; then
                [ "${oper_type}" == "available_size" ] && log_echo "ERROR" "The swap size is ${current_swap_size}G, but we need ${dir_default_size}G at least..."
                size_not_enough_dir_list="${size_not_enough_dir_list} swap"
            fi
            continue
        fi

        if [ "${dir_name}" == "/opt/oss/log" ];then
            df -hT | grep -w "/opt/oss/log" > /dev/null 2>&1
            if [ $? -ne 0 ];then
                log_echo "INFO" "No find /opt/oss/log with df -h, skip it"
                continue
            fi
        fi

        if [ "${dir_name}" == "/opt" -a "X${nodetype}" == "Xuniep" ];then
            skip_optcheck_for_virtualization
            [ "X${is_skip}" == "XYes" ] && continue
            skip_optcheck_for_uniep_independent_node
            [ "X${is_skip}" == "XYes" ] && continue
        fi
        local dir_compare_size=0
        if [ "${oper_type}" == "available_size" ]; then
            dir_compare_size=$(df -PBG ${dir_name} |grep "/" |awk '{print $4}'|sed 's/G//g')
            dir_compare_size=$((dir_compare_size+3))
        else
            dir_compare_size=$(df -PBG ${dir_name} |grep "/" |awk '{print $2}'|sed 's/G//g')
            dir_compare_size=$((dir_compare_size+2))
        fi

        if [ ${dir_compare_size} -lt ${dir_default_size} ] ; then
            [ "${oper_type}" == "available_size" ] && log_echo "ERROR" "The ${dir_name}'s available size is ${dir_compare_size}G, but we need ${dir_default_size}G at least..."
            size_not_enough_dir_list="${size_not_enough_dir_list} ${dir_name}"
        fi

    done

    if [ -z "${size_not_enough_dir_list}" ];then
      log_echo "INFO" "Check system directories size are enough."
      return 0
    fi

    if [ "${oper_type}" == "available_size" ]; then
        log_echo "ERROR" "Check following path's size failed : ${size_not_enough_dir_list}, please check"
        exit 1
    fi

    log_echo "INFO" "Check the system disks size, there are need_extend_dir_list=${size_not_enough_dir_list} need to extend."
    return 1
}

function check_Not_ATAE_extend_disk()
{
    log_echo "INFO" "Begin to check not_ATAE_extend_disk."
    local oper_type="$1"
    local size_not_enough_dir_list=""
    while read line
    do
        if [ "X$line" == "X" ];then
            continue
        fi
        local path_name=$(echo $line |awk -F'=' '{print $1}')
        local path_size=$(echo $line |awk -F'=' '{print $3}')
        if [ "${oper_type}" == "available_size" ]; then
           ## 校验剩余空间，取挂载大小的3/4校验
           path_size=$(($path_size * 3 / 4))
        fi

        if [ "${path_name}" == "swap" ];then
            local current_swap_size=$(free -g |grep -i "Swap:" |awk '{print $2}')
            local current_swap_size=$((current_swap_size+1))
            if [ ${current_swap_size} -lt ${path_size} ] ; then
                [ "${oper_type}" == "available_size" ] &&log_echo "ERROR" "The swap size is ${current_swap_size}G, but we need ${path_size}G at least..."
                size_not_enough_dir_list="${size_not_enough_dir_list} ${path_name}"
            fi
            continue
        fi

        local dir_compare_size=0
        if [ "${path_name}" != "swap" ];then
            umask 022
            mkdir -p ${path_name}
            if [ "${oper_type}" == "available_size" ]; then
                dir_compare_size=$(df -PBG ${path_name} |grep "/" |awk '{print $4}'|sed 's/G//g')
            else
                dir_compare_size=$(df -PBG ${path_name} |grep "${path_name}" |awk '{print $2}'|sed 's/G//g')
            fi
            [ -z "${dir_compare_size}" ] && dir_compare_size=0
            dir_compare_size=$((dir_compare_size+2))
            if [ ${dir_compare_size} -lt ${path_size} ] ; then
                [ "${oper_type}" == "available_size" ] && log_echo "ERROR" "The ${path_name}'s available size is ${dir_compare_size}G, but we need ${path_size}G at least..."
                size_not_enough_dir_list="${size_not_enough_dir_list} ${path_name}"
            fi
        fi
    done < ${ext_disk_cfg}

    if [ -z "${size_not_enough_dir_list}" ];then
        log_echo "INFO" "Check system directories size are enough."
        return 0
    fi

    if [ "${oper_type}" == "available_size" ]; then
        log_echo "ERROR" "Check following path's size failed : ${size_not_enough_dir_list}, please check"
        exit 1
    fi

    log_echo "INFO" "Check the system disks size, there are need_extend_dir_list=${size_not_enough_dir_list} need to extend."
    return 1
}

function config_alias()
{
    local alias_data="$1"
    local grep_key="$1"
    
    echo "$2" |grep "^special=" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        grep_key=$(echo "$2"|awk -F'special=' '{print $2}')
    fi
    
    grep -w "${grep_key}" /home/<USER>/.bashrc >/dev/null 2>&1
    if [ $? -ne 0 ];then
        echo "alias ${alias_data}" >> /home/<USER>/.bashrc
    fi
}

function add_alias(){
    #get the nodeType:dbNode, appNode, uniepNode, otherNode
    nodeType=$1
    log_echo "Begin to add alias to ossadm."
    #${command_prefix}
    if [ ! -f "/home/<USER>/.bashrc" ];then
        if [ "X$(whoami)" == "Xroot" ];then
            su - ossadm -c "touch /home/<USER>/.bashrc"
        else
            touch /home/<USER>/.bashrc
        fi
    fi
    
    if [ -f "/home/<USER>/.bashrc" ];then
        config_alias "statusapp='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd statusapp'"
        config_alias "restartapp='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd restartapp'"
        config_alias "startapp='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd startapp'"
        config_alias "stopapp='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopapp'"
        config_alias "stopnode='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopnode'"
        config_alias "startnode='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd startnode'"
        config_alias "restartnode='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopnode;ipmc_adm -cmd startnode'"
        config_alias "stopdc='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopdc'"
        config_alias "startdc='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd startdc'"
        config_alias "restartdc='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopdc;ipmc_adm -cmd startdc'"
        if [ -f /opt/oss/manager/apps/DBAgent/bin/dbsvc_adm ];then
           config_alias "queryinstance='sh /opt/oss/manager/apps/DBAgent/bin/dbsvc_adm -cmd query-db-instance -type zenith'"
        fi
        if [ -f /opt/oss/manager/apps/CloudbMgmtService/bin/switchtool.sh ];then
            config_alias "forbiddendb='sh /opt/oss/manager/apps/CloudbMgmtService/bin/switchtool.sh -cmd set-ignore-nodes -nodes all'"
        fi
        if [ -f /opt/oss/manager/apps/CloudbMgmtService/bin/switchtool.sh ];then
            config_alias "resumedb='sh /opt/oss/manager/apps/CloudbMgmtService/bin/switchtool.sh -cmd del-ignore-nodes'"
        fi
        #M

        config_alias "statusdual='sh /opt/oss/manager/apps/HyperHAAgent/bin/hactl status resource'"
        config_alias "forbiddendual='sh /opt/oss/manager/apps/HyperHAAgent/bin/hactl freeze node'"
        config_alias "resumedual='sh /opt/oss/manager/apps/HyperHAAgent/bin/hactl unfreeze node'"
        config_alias "switchdual='sh /opt/oss/manager/apps/HyperHAAgent/bin/hactl switch resourcegroup'"



        
        if [ -f "/opt/oss/manager/var/share/software_define.yaml" ];then
            config_alias "restartnodeall='source /opt/oss/manager/bin/engr_profile.sh;nodelists=\$(python -c \"from deployment import nodelist;print(\\\" \\\".join([node.get_mgmt_ip_addr() for node in nodelist.NodeListMgr().get_all_nodes()]))\");for node in \${nodelists};do ssh \${node} -o StrictHostKeyChecking=no -t bash -ic \"restartnode\";done'" "special=restartnodeall="
            
            config_alias "restartapp_m='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd restartapp -tenant manager'"
            config_alias "startapp_m='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd startapp -tenant manager'"
            config_alias "stopapp_m='source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopapp -tenant manager'"
        fi
    else
        log_echo "ERROR"  "add alias to ossadm failed."
        return 1
    fi
    
    return 0
}

function init_user_passwd()
{
    log_echo "INFO" "Begin to initialize the passwd of os user..."
    read -sp "input dv_user pwd:" pwd_list
    
    i2k_userpasswd=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $1}')
    dvshare_userpasswd=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $2}')
    sysomc_userpasswd=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $3}')
    sftpossuser_userpasswd=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $5}')
    sopuser_userpasswd=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $6}')
    devdata_userpasswd=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $7}')
    if [ ! -f "/.dockerenv" ] && [ "X${dv_force_change_os_pwd}" == "XYes" ];then
        new_sshusr_pwd=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $8}')
        new_root_pwd=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $9}')
    fi
    sshagent_passwd=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $(NF-4)}')
    sshagent_passwd_encode=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $(NF-3)}')
    
    kafka_ca_cert_pwd=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $(NF-2)}')
    kafka_store_encode_password=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $(NF-1)}')
    kafka_key_encode_password=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $NF}')
}

function set_firstlogin_pwd()
{
    local username=$1
    local new_password=$2
    firstloginfile=$ROOT_CUSTOM_PATH/firstlogin.tag
    grep -w "${username}:success" ${firstloginfile} > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "begin to change the ${username} password"
        set_user_passwd  ${username}  ${new_password}
        if [ $? -eq 0 ];then
            echo "${username}:success" >> ${firstloginfile}
        else
            log_echo "ERROR" "change the ${username} password failed"
            exit 1
        fi
    fi
}

function config_firstlogin()
{   
    if [ ! -f "/.dockerenv" ] && [ "X${dv_force_change_os_pwd}" == "XYes" ];then
        id sshusr > /dev/null 2>&1
        if [ $? -eq 0 ];then
            set_firstlogin_pwd  sshusr ${new_sshusr_pwd}
        fi
        set_firstlogin_pwd root ${new_root_pwd}
    fi
    return 0
}

function config_uniep_firstlogin()
{   
    init_firstlogin_passwd
    config_firstlogin
}

function check_pwd() {
    local python_alias="python3"
    which python &> /dev/null && python_alias="python"
    echo "$1:$2:$3" | "${python_alias}" "${UTILS_PATH}/getpvalue.py"
    local ret_code="$?"
    if [[ ${ret_code} -ne 0 ]];then
        return 1
    else
        log_echo "INFO" "check_pwd successfully"
    fi
}

function isNum()
{
    local inputNum="$1"
    if [ -z "${inputNum}" ];then
        log_echo "ERROR" "The inputNum=${inputNum} is null."  
        return 1
    fi
    
    local isNumber=$(echo "${inputNum}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        log_echo "ERROR" "The inputNum=${inputNum} is not number."  
        return 1
    fi
    
    return 0
}

function permission_change()
{
    local path="$1"
    local file_suffix="$2"
    local permission="$3"
    if [ -z "${path}" -o -z "${file_suffix}" -o -z "${permission}" ];then
        log_echo "INFO" "The path=${path} or file_suffix=${file_suffix} or permission=${permission} has null."
        return 0
    fi
    
    if [ ! -d ${path} ];then
        log_echo "INFO" "The path=${path} is not exists."
        return 0
    fi
    
    log_echo "INFO" "The path=${path} is exists."
    local tmp_suffix_cmd=""
    for tmp_suffix in $(echo "${file_suffix}"|sed "s/,/ /g");do
        if [ -z "${tmp_suffix_cmd}" ];then
            tmp_suffix_cmd=" -name \"${tmp_suffix}\""
            continue
        fi
        
        tmp_suffix_cmd="${tmp_suffix_cmd} -o -name \"${tmp_suffix}\""
        
    done
    local find_cmd="find ${path} -type f -a \( ${tmp_suffix_cmd} \) -print0 | xargs -0 --no-run-if-empty chmod ${permission} >> ${LOG_FILE} 2>&1"
    eval ${find_cmd}
    local ret=$?
    log_echo "INFO" "Execute cmd:[ ${find_cmd} ] finished.ret=${ret}"
}

function permission_handle()
{
    log_echo "INFO" "Begin to handle permission."
    if [ "X${is_upgrade}" != "XYes" -o "X$(whoami)" == "Xroot" ];then
        user_home_list="${ossadm_user_home} ${dbuser_user_home} ${system_sftp_userhome} ${sop_user_home} ${system_login_userhome} ${system_operation_userhome} ${dvshare_user_home} ${icnfg_devdata_user_home}"

        for user_home in ${user_home_list}
        do
            chmod 600 ${user_home}/.bash* >> $LOG_FILE 2>&1
            chmod 600 ${user_home}/.mksh* >> $LOG_FILE 2>&1
            chmod 600 ${user_home}/.ksh*  >> $LOG_FILE 2>&1
            chmod 600 ${user_home}/.zsh*  >> $LOG_FILE 2>&1
            chmod 600 ${user_home}/.csh*  >> $LOG_FILE 2>&1
            if [ "${user_home}" != "${icnfg_devdata_user_home}" ];then
                [ -d "${user_home}/bin" ] && chmod 700 ${user_home}/bin
                [ -d "${user_home}/.fonts" ] && chmod 700 ${user_home}/.fonts
            fi
        done
    
        if [ -d /home/<USER>/.gnupg ];then
            chown -R ossadm:ossgroup /home/<USER>/.gnupg
            find /home/<USER>/.gnupg -type f |xargs chmod 600
            find /home/<USER>/.gnupg -type d |xargs chmod 700
        fi
    
        if [ -z "${i2k_user_home}" ];then
            log_echo "INFO" "The i2k_user_home=${i2k_user_home} is null."
        else
            permission_change "${i2k_user_home}/.ssh" "*.pub" "600"
            chmod 600 ${i2k_user_home}/.ssh/known_hosts >/dev/null 2>&1

            permission_change "${i2k_user_home}/etc/ssl" "*.crt,*.jks,*.csr,*.key,*.p12,*.cnf,*.pem,*.json,*.pfx,*.properties,*.keystore,*.bak" "600"
            chmod 600 ${i2k_user_home}/etc/ssl/dv/*_tag >/dev/null 2>&1

            permission_change "${i2k_user_home}/python" "*.pc,*.json,*.xml,*.cfg" "640"
            permission_change "${i2k_user_home}/python" "*.html,*.css,*.ps1" "440"
            permission_change "${i2k_user_home}/python" "*.pem,*.crl" "600"
            chmod 600 ${i2k_user_home}/.profile >/dev/null 2>&1
        fi
    fi
    permission_change "${install_path}/manager/var/etc/backuprestore" "*.json,*.cfg" "640"
    permission_change "${install_path}/manager/var/etc/common" "*.cfg" "640"
    permission_change "${install_path}/manager/var/etc/sysmt/networkmgmt" "*.properties,*.cfg" "640"
    
    if [ "X${is_upgrade}" != "XYes" -o "X$(whoami)" == "Xroot" ];then
        if [ -z "${VS_PATH}" ];then
            log_echo "INFO" "The VS_PATH=${VS_PATH} is null."
        else
            if [ -d ${VS_PATH} ];then
                log_echo "INFO" "The VS_PATH=${VS_PATH} is exists."
                chmod 750 ${VS_PATH} >> ${LOG_FILE} 2>&1
            else
                log_echo "INFO" "The VS_PATH=${VS_PATH} is not exists."
            fi
        fi

        if [ ! -z "${icnfg_devdata_user_home}" -a -d ${icnfg_devdata_user_home} ];then
            log_echo "INFO" "The icnfg_devdata_user_home=${icnfg_devdata_user_home} is exists."
            chmod 750 ${icnfg_devdata_user_home} >> ${LOG_FILE} 2>&1
        fi
    fi
    if [ -d /var/log/oss ];then
        log_echo "INFO" "The node has /var/log/oss."
    else
        log_echo "INFO" "not has /var/log/oss, need to mkdir ${OPT_PATH}/oss/log"
        mkdir -p ${OPT_PATH}/oss/log
        if [ -d "/home/<USER>" -a "X$(whoami)" == "Xroot" ];then
            chown ossadm:ossgroup /opt/oss ${OPT_PATH}/oss/log
        fi
        chmod 750 ${OPT_PATH}/oss ${OPT_PATH}/oss/log
    fi
    if [ -e /opt/oss/uds/nodeagent/nodeagent.sock ];then
        if [ "$(stat -c "%a" /opt/oss/uds/nodeagent/nodeagent.sock)" != "660" ];then
            chmod 660 /opt/oss/uds/nodeagent/nodeagent.sock
        fi
    fi
    if [ -e /opt/oss/uds/nodeagent/configdb.new ];then
        if [ "$(stat -c "%a" /opt/oss/uds/nodeagent/configdb.new)" != "660" ];then
            chmod 660 /opt/oss/uds/nodeagent/configdb.new
        fi
    fi


    log_echo "INFO" "handle permission finished."
    return 0
}

function handle_redis_whitelist_for_dvregister()
{
    local command_prefix=""
    command_prefix="sudo -u dbuser"
    dvregisterdb_dir=$(${command_prefix} bash -c "ls -d /opt/redis/data/dvregisterservicerdb*")
    if [ -n "${dvregisterdb_dir}" ];then
        ${command_prefix} bash -c "test -f ${dvregisterdb_dir}/redis_common.conf"
        if [ $? -eq 0 ];then
            ${command_prefix} grep -w "EVAL" ${dvregisterdb_dir}/redis_common.conf > /dev/null 2>&1
            if [ $? -eq 0 ];then
                log_echo "INFO" "Find EVAL in redis_common.conf of dvregisterservicerdb."
                if [ "X$(whoami)" == "Xroot" ];then
                        su - ossadm -c "${install_path}/manager/apps/DBAgent/bin/dbsvc_adm -cmd query-db-instance | grep  dvregisterservicerdb | head -1" > ${UTILS_PATH}/dvregisterid
                    instid=$(cat ${UTILS_PATH}/dvregisterid | awk -F' ' '{print $1}')
                    su - ossadm -c "${install_path}/manager/apps/DBAgent/bin/dbsvc_adm -cmd modify-param-instance -instid ${instid} -paramname redis_command_whitelist -paramvalue \"KEYS,EVAL,FLUSHDB\""
                else
                        $(${install_path}/manager/apps/DBAgent/bin/dbsvc_adm -cmd query-db-instance | grep  dvregisterservicerdb | head -1) > ${UTILS_PATH}/dvregisterid
                    instid=$(cat ${UTILS_PATH}/dvregisterid | awk -F' ' '{print $1}')
                    $(${install_path}/manager/apps/DBAgent/bin/dbsvc_adm -cmd modify-param-instance -instid ${instid} -paramname redis_command_whitelist -paramvalue "KEYS,EVAL,FLUSHDB")
                fi
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Set redis_command_whitelist for dvregister failed."
                fi
            fi
        fi
    fi
}

function check_if_install_icnfg()
{
    if [ "X${is_install_icnfg}" == "XYes" ];then
        return 0
    else
        return 1
    fi
}

function build_icnfg_trust()
{
    log_echo "INFO" "Begin to build icnfg trust for app  node."
    trust_ip="$1"
    rm -rf ${icnfg_devdata_user_home}/.ssh
    mkdir -p ${icnfg_devdata_user_home}/.ssh
    cat ${i2k_user_home}/.ssh/id_rsa.pub > ${icnfg_devdata_user_home}/.ssh/authorized_keys
    chown -R ${icnfg_devdata_user_name}.${i2k_group_name} ${icnfg_devdata_user_home}/.ssh/
    chmod 700 ${icnfg_devdata_user_home}/.ssh/
    chmod 640 ${icnfg_devdata_user_home}/.ssh/authorized_keys 
    check_ssh_sftp_seperate
    if [ $? -ne 0 ];then
        su - "${i2k_user_name}" -c "ssh -o ServerAliveInterval=60  -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${icnfg_devdata_user_name}@${trust_ip} \"echo test\""
    fi
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Build icnfg trust for app  node  failed."
        exit 1
    fi
}

function handle_icnfg_link()
{
    if [ -L ${icnfg_devdata_user_home}/configpkg ];then
        rm -rf ${icnfg_devdata_user_home}/configpkg
        mkdir -p  ${icnfg_devdata_user_home}/configpkg
        cp -a  ${i2k_install_path}/icnfg/webpages/var/data/configpkg/* ${icnfg_devdata_user_home}/configpkg 
        touch /tmp/DVPreSet_upgrade/handle_icnfg_link.tag
    fi
    if [ -L ${icnfg_devdata_user_home}/sftp/necert ];then
        rm -rf ${icnfg_devdata_user_home}/sftp/necert
        mkdir -p   ${icnfg_devdata_user_home}/sftp/necert
        cp -a  ${i2k_install_path}/icnfg/webpages/var/data/necert/* ${icnfg_devdata_user_home}/sftp/necert
    fi
    if [ -L ${icnfg_devdata_user_home}/sftp/jdbc ];then
        rm -rf ${icnfg_devdata_user_home}/sftp/jdbc
        mkdir -p  ${icnfg_devdata_user_home}/sftp/jdbc
        cp -a  ${i2k_install_path}/icnfg/webpages/var/data/jdbc/* ${icnfg_devdata_user_home}/sftp/jdbc
    fi
    
    devdata_mkdir
    devdata_set_permissions
    find ${icnfg_devdata_user_home}/configpkg -type f | xargs -i chown ossuser:ossgroup {}
    find ${icnfg_devdata_user_home}/sftp -type f | xargs -i chown ossuser:ossgroup {}
}

function modify_iptables()
{
    if [ -f "/.dockerenv" ];then
        log_echo "INFO" "Containerized scene installation ,no need to config iptables."
        return 0
    fi

    local option="$1"
    
    if [ "${option}" == "delete" ];then
        log_echo "INFO" "delete rules for iptables"
        sed -i "/${dv_device_trap_port}/d" /etc/rc.d/rc.local
        sed -i "/${dv_device_trap_port}/d" /etc/rc.d/after.local
        iptables -w -t nat -D PREROUTING -p udp -m udp --dport "${dv_device_trans_port}" -j REDIRECT --to-ports "${dv_device_trap_port}"
        iptables -w -t nat -D PREROUTING -p udp --dport "${dv_device_trap_port}" -j REDIRECT --to-ports "${dv_middleware_trap_port}"
        netcard_list=$(ip addr  | grep "BROADCAST" | awk -F ':' '{print $2}'|awk -F '@' '{print $1}'  | tr -d ' ')
        for netcard in ${netcard_list}
        do
            iptables -w -D PREROUTING -t mangle  -i "${netcard}" -p udp ! -s "127.0.0.1" --dport "${dv_device_trap_port}" -j TEE --gateway "127.0.0.1"
        done
        return 0
    fi    
    
    iptables -L -t nat | grep "${dv_device_trap_port}" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "add rules for iptables"
        iptables -w -t nat -A PREROUTING -p udp -m udp --dport "${dv_device_trans_port}" -j REDIRECT --to-ports "${dv_device_trap_port}"
    fi
    
    iptables -L -t mangle | grep "${dv_device_trap_port}" | grep "TEE" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "add rules for iptables"
        netcard_list=$(ip addr  | grep "BROADCAST" | awk -F ':' '{print $2}'|awk -F '@' '{print $1}' | tr -d ' ')
        for netcard in ${netcard_list}
        do
            iptables -w -A PREROUTING -t mangle  -i "${netcard}" -p udp ! -s "127.0.0.1" --dport "${dv_device_trap_port}" -j TEE --gateway "127.0.0.1"
            if [ -f "/etc/rc.d/rc.local" ];then
                cat /etc/rc.d/rc.local | grep "${dv_device_trap_port}" | grep "${netcard}" |grep "mangle"  > /dev/null 2>&1
                if [ $? -ne 0 ];then
                    sed -i "\$a\iptables -A PREROUTING -t mangle  -i ${netcard} -p udp ! -s 127.0.0.1 --dport ${dv_device_trap_port} -j TEE --gateway 127.0.0.1" /etc/rc.d/rc.local > /dev/null 2>&1
                fi
            fi
            
            if [ -f "/etc/rc.d/after.local" ];then
                cat /etc/rc.d/after.local | grep "${dv_device_trap_port}" | grep "${netcard}" |grep "mangle"  > /dev/null 2>&1
                if [ $? -ne 0 ];then
                    sed -i "\$a\iptables -A PREROUTING -t mangle  -i ${netcard} -p udp ! -s 127.0.0.1 --dport ${dv_device_trap_port} -j TEE --gateway 127.0.0.1" /etc/rc.d/after.local > /dev/null 2>&1
                fi
            fi
        done
    fi
    
    iptables -L -t nat | grep "${dv_middleware_trap_port}" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        iptables -w -t nat -A PREROUTING -p udp --dport "${dv_device_trap_port}" -j REDIRECT --to-ports "${dv_middleware_trap_port}"
        if [ -f "/etc/rc.d/rc.local" ];then
            cat /etc/rc.d/rc.local | grep "${dv_middleware_trap_port}" > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i "\$a\iptables -t nat -A PREROUTING -p udp --dport ${dv_device_trap_port} -j REDIRECT --to-ports ${dv_middleware_trap_port}" /etc/rc.d/rc.local > /dev/null 2>&1
            fi
        fi
            
        if [ -f "/etc/rc.d/after.local" ];then
            cat /etc/rc.d/after.local | grep "${dv_middleware_trap_port}" > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i "\$a\iptables -t nat -A PREROUTING -p udp --dport ${dv_device_trap_port} -j REDIRECT --to-ports ${dv_middleware_trap_port}" /etc/rc.d/after.local > /dev/null 2>&1
            fi
        fi
    fi
    
    if [ -f "/etc/rc.d/rc.local" ];then
        cat /etc/rc.d/rc.local | grep "\-\-dport ${dv_device_trans_port} \-j REDIRECT \-\-to\-ports ${dv_device_trap_port}" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            sed -i "\$a\iptables -t nat -A PREROUTING -p udp -m udp --dport ${dv_device_trans_port} -j REDIRECT --to-ports ${dv_device_trap_port}" /etc/rc.d/rc.local > /dev/null 2>&1
        fi
    fi
    
    #for suse
    if [ -f "/etc/rc.d/after.local" ];then
        cat /etc/rc.d/after.local | grep "\-\-dport ${dv_device_trans_port} \-j REDIRECT \-\-to\-ports ${dv_device_trap_port}" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            sed -i "\$a\iptables -t nat -A PREROUTING -p udp -m udp --dport ${dv_device_trans_port} -j REDIRECT --to-ports ${dv_device_trap_port}" /etc/rc.d/after.local > /dev/null 2>&1
        fi
    fi
    
    return 0
}

function start_sshagent()
{
    ps -ef | grep -v grep | grep -w "ssh-agent" | grep "ossuser" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        rm -f ${i2k_user_home}/.ssh/sshagent_sock
        su - ossuser -c "ssh-agent -s -a ${i2k_user_home}/.ssh/sshagent_sock"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "start_sshagent failed"
            exit 1
        fi
    fi
    
    su - ossuser -c "export SSH_AUTH_SOCK=${i2k_user_home}/.ssh/sshagent_sock;ssh-add -l" | grep "${i2k_user_home}" >/dev/null 2>&1
    if [ $? -ne 0 ];then
        su - ossuser -c "export SSH_AUTH_SOCK=/home/<USER>/.ssh/sshagent_sock;echo \"${sshagent_passwd} sshagent:passwd\" | ${PreSet_PATH}/sshagent.exp 'ssh-add'"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ssh-add failed"
            exit 1
        fi
    fi
}

function create_ossuser_idrsa()
{
    [ "X${is_upgrade}" == "XYes" ] && return 0
    
    if [ "X${open_ssh_encrypt}" != "XYes" ];then
        if [ ! -f "${i2k_user_home}/.ssh/id_rsa.pub" ];then
            log_echo "INFO" "Begin to create id_rsa.pub for ossuser"
            su - "${i2k_user_name}" -c "ssh-keygen -m PEM -t rsa -P '' -f ~/.ssh/id_rsa"
        fi
        return 0
    fi
    chmod 755 ${PreSet_PATH}
    chmod 755 ${PreSet_PATH}/sshagent.exp
    if [ ! -f "${i2k_user_home}/.ssh/id_rsa.pub" -o ! -S "${i2k_user_home}/.ssh/sshagent_sock" ];then
        log_echo "INFO" "Begin to create id_rsa.pub for ossuser"
        if [ "X${sshagent_passwd}" == "X" ];then
            log_echo "ERROR" "passwd for ssh agent is null."
            exit 1
        fi
        ps -ef |grep ssh-agent |grep -v grep|grep ossuser|awk -F' ' '{print $2}'|xargs -i kill -9 {}
        rm -f ${i2k_user_home}/.ssh/sshagent_sock
        rm -f ${i2k_user_home}/.ssh/id_rsa
        
        su - ossuser -c "echo \"${sshagent_passwd} sshagent:passwd\" | ${PreSet_PATH}/sshagent.exp 'ssh-keygen'"
        
        grep -i "ENCRYPTED" ${i2k_user_home}/.ssh/id_rsa > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Create ssh id_rsa for ossuser failed"
            exit 1
        fi

        echo "privatekey=${sshagent_passwd_encode}" > ${i2k_user_home}/.ssh/sshagent_privatekey
        chown ossuser:ossgroup ${i2k_user_home}/.ssh/sshagent_privatekey
        chmod 600 ${i2k_user_home}/.ssh/sshagent_privatekey
        
        start_sshagent
    fi

    sed -i "/DVCommonConfigService/d" /home/<USER>/.bashrc
    sed -i "/sshagent_sock/d" /home/<USER>/.bashrc
    echo "[ -f /opt/oss/SOP/apps/DVCommonConfigService/tools/sshagent_manager.sh ] && sh /opt/oss/SOP/apps/DVCommonConfigService/tools/sshagent_manager.sh" >>  /home/<USER>/.bashrc
    echo "export SSH_AUTH_SOCK=\"/home/<USER>/.ssh/sshagent_sock\"" >>  /home/<USER>/.bashrc
    chown ossuser:ossgroup ${i2k_user_home}/.bashrc
    chmod 640 ${i2k_user_home}/.bashrc
}

function modify_tmpdir_authority()
{
    tmp_dir_list="${TMP_PATH}/DVPreSet ${TMP_PATH}/DVPreSet_upgrade"
    for tmp_dir in ${tmp_dir_list}
    do
        if [ -d "${tmp_dir}" ];then
            find ${tmp_dir} -type f -a \( -name "*.py" -o -name "*.pyc" -o -name "*.bat" -o -name "*.so" \) -print0 | xargs -0 --no-run-if-empty chmod 550
            find ${tmp_dir} -path ${tmp_dir}/backup -prune -o -name "*.sh" -exec chmod 550 {} +
            find ${tmp_dir} -name "*.txt" -exec chmod 640 {} +
        fi
    done
    
    return 0
}

function check_ssh()
{
    local ssh_ip="$1"
    local user_name="$2"
    local user_pwd="$3"
    
    auto_smart_ssh ${user_pwd} "${user_name}@${ssh_ip} echo ssh_ok"
    if [ $? -eq 0 ];then
        return 0
    else
        log_echo "ERROR" "ssh to ${ssh_ip} execute command failed, please check ip, user and passwd are correct."
        exit 1
    fi
}

function isExistSameIp()
{
    local ip_key_list="$1"
    log_echo "INFO" "ip_key_list=${ip_key_list}"
    if [ -z "${ip_key_list}" ];then
        log_echo "INFO" "The ip_key_list=${ip_key_list} is null."
        return 0
    fi
    
    log_echo "INFO" "ip_key_list key of source_file=${source_file}"
    local all_ip_values=""
    local value=""
    for cfg_key in $(echo "${ip_key_list}" |sed "s/,/ /g");do
        value=$(cat ${source_file}|grep -v "^[ ]*#"|grep "^[ ]*${cfg_key}=" |tail -n 1 |awk -F'=' '{print $2}')
        if [ -z "${value}" ];then
            continue
        fi
        all_ip_values="${all_ip_values},${value}"
    done
    
    if [ -z "${all_ip_values}" ];then
        log_echo "INFO" "The all_ip_values=${all_ip_values} is null."
        return 0
    fi
    
    local tmp_ip_list=""
    local tmp_ip=""
    local same_ip_list=""
    for ip_value in $(echo "${all_ip_values}" |sed "s/,/ /g");do
        tmp_ip=$(echo "${ip_value}"|sed "s/\./\\\./g")
        echo "${tmp_ip_list}" |grep -iw "${tmp_ip}" >> ${LOG_FILE}
        if [ $? -eq 0 ];then
            echo "${same_ip_list}" |grep -iw "${tmp_ip}" >> ${LOG_FILE}
            if [ $? -eq 0 ];then
                continue
            fi
            
            if [ -z "${same_ip_list}" ];then
                same_ip_list="${ip_value}"
            else
                same_ip_list="${same_ip_list},${ip_value}"
            fi
            continue
        fi
        tmp_ip_list="${tmp_ip_list},${ip_value}"
    done
    
    if [ ! -z "${same_ip_list}" ];then
        log_echo "ERROR" "The ip:[${same_ip_list}] is has some same config ip in the ${source_file}.please check"
        exit 1
    fi
    log_echo "INFO" "The same IP address does not exist."
}

function set_product_json()
{
    local product_json="$1"
    local key_name="$2"
    local value="$3"
    local need_encrypt="$4"
    if [ -z "${product_json}" ];then
        log_echo "ERROR" "The product_json is null.of function set_product_json"
        exit 1
    fi
    
    if [ -z "${key_name}" ];then
        log_echo "ERROR" "The key_name is null.of function set_product_json"
        exit 1
    fi
    
    if [ -z "${value}" ];then
        log_echo "ERROR" "The ${key_name} value is null.of function set_product_json"
        exit 1
    fi
    
    if [ ! -f ${product_json} ];then
        log_echo "ERROR" "The product_json=${product_json} is not exist."
        exit 1
    fi
    
    local tmp_value="${value}"
    if [ "X${need_encrypt}" == "XEncrypt" ];then
        encrypt_by_uniep "${value}" ""
        tmp_value="${pwd_return}"
    fi
    
    if [ -z "${tmp_value}" ];then
        log_echo "ERROR" "The ${key_name} tmp_value is null.of function set_product_json"
        exit 1
    fi
    
    sed -i "s#{{${key_name}}}#${tmp_value}#g" ${product_json}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "The product_json=${product_json} set ${key_name} failed."
        exit 1
    fi
    
    log_echo "INFO" "The product_json=${product_json} set ${key_name} successfully."
}

function get_random_value()
{
    local random_cmd="$1"
    if [ -z "${random_cmd}" ];then
        log_echo "ERROR" "The random_cmd is null.of function get_random_value"
        exit 1
    fi
    RANDOM_VALUE=$(eval ${random_cmd})
    check_value=$(echo "${RANDOM_VALUE}" |grep "#")
    if [ -z "${check_value}" ];then
        return 0
    fi
    
    local num=10
    for n in $(seq 1 ${num});do
        RANDOM_VALUE=$(eval ${random_cmd})
        check_value=$(echo "${RANDOM_VALUE}" |grep "#")
        if [ -z "${check_value}" ];then
            return 0
        fi
    done
    
    log_echo "ERROR" "get_random_value of random_cmd=${random_cmd}. get 10 times has mark '#' and to sed comand mark is same."
    return 1
}

function set_cmp_key_for_product_json()
{
    local product_json="$1"
    get_random_value "head -c 32 /dev/random | base64" || exit 1
    CMP_ROOT_KEY_FACTORY_FIRST="${RANDOM_VALUE}"
    
    get_random_value "head -c 32 /dev/random | base64" || exit 1
    CMP_ROOT_KEY_FACTORY_SECOND="${RANDOM_VALUE}"
    
    get_random_value "head -c 32 /dev/random | base64" || exit 1
    CMP_WORK_KEY_FACTORY="${RANDOM_VALUE}"
    
    get_random_value "head -c 32 /dev/random | base64" || exit 1
    CMP_HMAC_SHARE_KEY="${RANDOM_VALUE}"
    
    get_random_value "head -c 16 /dev/random | base64" || exit 1
    CMP_AUTH_SALT="${RANDOM_VALUE}"
    
    get_random_value "tr -dc \"\!\@\#\%\^\&\*\(\)\_\+\-\=\{\}\[\]\|\>\<\,\?\.\:\;A-Za-z0-9\" </dev/random| head -c 16" || exit 1
    CMP_AUTH_PASS="${RANDOM_VALUE}"
    
    if [ "X${pre_install_key}" != "XPreInstall" ];then
        get_random_value "head -c 12 /dev/random | base64" || exit 1
        MED_ACCESS_KEY="${RANDOM_VALUE}"
        
        get_random_value "head -c 32 /dev/random | base64" || exit 1
        MED_SECRET_KEY="${RANDOM_VALUE}"
        
        get_random_value "head -c 12 /dev/random | base64" || exit 1
        UTM_ACCESS_KEY="${RANDOM_VALUE}"
        
        get_random_value "head -c 32 /dev/random | base64" || exit 1
        UTM_SECRET_KEY="${RANDOM_VALUE}"
    else
        MED_ACCESS_KEY="$(cat ${PreSet_PATH}/pre_install.properties | grep "MED_ACCESS_KEY="  | awk -F 'MED_ACCESS_KEY=' '{print $2}')"
        MED_SECRET_KEY="$(cat ${PreSet_PATH}/pre_install.properties | grep "MED_SECRET_KEY=" | awk -F 'MED_SECRET_KEY=' '{print $2}')"
        if [ -z "${MED_ACCESS_KEY}" -o -z "${MED_SECRET_KEY}" ];then
            log_echo "INFO" "The MED_ACCESS_KEY MED_SECRET_KEY is null.need to random it."
            get_random_value "head -c 12 /dev/random | base64" || exit 1
            MED_ACCESS_KEY="${RANDOM_VALUE}"
            get_random_value "head -c 32 /dev/random | base64" || exit 1
            MED_SECRET_KEY="${RANDOM_VALUE}"
        else
            log_echo "INFO" "The MED_ACCESS_KEY MED_SECRET_KEY is get of pre_install."
            decrypt_password ${MED_SECRET_KEY}
            MED_SECRET_KEY=${decryptPasswd}
        fi
        
        UTM_ACCESS_KEY="$(cat ${PreSet_PATH}/pre_install.properties | grep "UTM_ACCESS_KEY=" | awk -F 'UTM_ACCESS_KEY=' '{print $2}')"
        UTM_SECRET_KEY="$(cat ${PreSet_PATH}/pre_install.properties | grep "UTM_SECRET_KEY=" | awk -F 'UTM_SECRET_KEY=' '{print $2}')"
        if [ -z "${UTM_ACCESS_KEY}" -o -z "${UTM_SECRET_KEY}" ];then
            log_echo "INFO" "The UTM_ACCESS_KEY UTM_SECRET_KEY is null.need to random it."
            get_random_value "head -c 12 /dev/random | base64" || exit 1
            UTM_ACCESS_KEY="${RANDOM_VALUE}"
            get_random_value "head -c 32 /dev/random | base64" || exit 1
            UTM_SECRET_KEY="${RANDOM_VALUE}"
        else
            log_echo "INFO" "The UTM_ACCESS_KEY UTM_SECRET_KEY is get of pre_install."
            decrypt_password ${UTM_SECRET_KEY}
            UTM_SECRET_KEY=${decryptPasswd}
        fi
    fi
    
    set_product_json "${product_json}" "CMP_ROOT_KEY_FACTORY_FIRST" "${CMP_ROOT_KEY_FACTORY_FIRST}"
    set_product_json "${product_json}" "CMP_ROOT_KEY_FACTORY_SECOND" "${CMP_ROOT_KEY_FACTORY_SECOND}"
    set_product_json "${product_json}" "CMP_WORK_KEY_FACTORY" "${CMP_WORK_KEY_FACTORY}" "Encrypt"
    set_product_json "${product_json}" "CMP_HMAC_SHARE_KEY" "${CMP_HMAC_SHARE_KEY}" "Encrypt"
    set_product_json "${product_json}" "CMP_AUTH_SALT" "${CMP_AUTH_SALT}"  "Encrypt"
    set_product_json "${product_json}" "CMP_AUTH_PASS" "${CMP_AUTH_PASS}"  "Encrypt"
    set_product_json "${product_json}" "MED_ACCESS_KEY" "${MED_ACCESS_KEY}"
    set_product_json "${product_json}" "MED_SECRET_KEY" "${MED_SECRET_KEY}" "Encrypt"
    set_product_json "${product_json}" "UTM_ACCESS_KEY" "${UTM_ACCESS_KEY}"
    set_product_json "${product_json}" "UTM_SECRET_KEY" "${UTM_SECRET_KEY}" "Encrypt"
}

function store_passwords()
{
    encrypt_by_uniep "${sysomc_pwd}" ""
    sysomc_pwd="${pwd_return}"
    echo "sysomc_pwd=${sysomc_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${sftpossuser_pwd}" ""
    sftpossuser_pwd="${pwd_return}"
    echo "sftpossuser_pwd=${sftpossuser_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${dvshareuser_pwd}" ""
    dvshareuser_pwd="${pwd_return}"
    echo "dvshareuser_pwd=${dvshareuser_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${devdata_pwd}" ""
    devdata_pwd="${pwd_return}"
    echo "devdata_pwd=${devdata_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${paasinter_pwd}" ""
    paasinter_pwd="${pwd_return}"
    echo "paasinter_pwd=${paasinter_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${I2kNfv_pwd}" ""
    I2kNfv_pwd="${pwd_return}"
    echo "I2kNfv_pwd=${I2kNfv_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${cie_snmp_pwd}" ""
    cie_snmp_pwd="${pwd_return}"
    echo "cie_snmp_pwd=${cie_snmp_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${sftp_admin_pwd}" ""
    sftp_admin_pwd="${pwd_return}"
    echo "sftp_admin_pwd=${sftp_admin_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${sftp_nbiuser_pwd}" ""
    sftp_nbiuser_pwd="${pwd_return}"
    echo "sftp_nbiuser_pwd=${sftp_nbiuser_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${sftp_mmluser_pwd}" ""
    sftp_mmluser_pwd="${pwd_return}"
    echo "sftp_mmluser_pwd=${sftp_mmluser_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${sftp_assetuser_pwd}" ""
    sftp_assetuser_pwd="${pwd_return}"
    echo "sftp_assetuser_pwd=${sftp_assetuser_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${DV_CERT_DRIVER_pwd}" ""
    DV_CERT_DRIVER_pwd="${pwd_return}"
    echo "DV_CERT_DRIVER_pwd=${DV_CERT_DRIVER_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${tool_mainast_pwd}" ""
    tool_mainast_pwd="${pwd_return}"
    echo "tool_mainast_pwd=${tool_mainast_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    
    while read line || [[ -n ${line} ]]
    do
        if [ -z "${line}" ];then
            continue
        fi
        
        key=${line}
        value=$(eval echo \$${key})
        if [ -z "${value}" ];then
            log_echo "INFO" "The ${key} of value is null.of store_passwords"
            continue
        fi
        
        encrypt_by_uniep "${value}" ""
        echo "${key}=${pwd_return}" >> ${PreSet_PATH}/Common/passwords.properties
    done < ${PreSet_PATH}/Common/optional_parameter.properties
    
    encrypt_by_uniep "${tool_ideploy_pwd}" ""
    tool_ideploy_pwd="${pwd_return}"
    echo "tool_ideploy_pwd=${tool_ideploy_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${tool_dms_pwd}" ""
    tool_dms_pwd="${pwd_return}"
    echo "tool_dms_pwd=${tool_dms_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${tool_das_pwd}" ""
    tool_das_pwd="${pwd_return}"
    echo "tool_das_pwd=${tool_das_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${tool_easyms_pwd}" ""
    tool_easyms_pwd="${pwd_return}"
    echo "tool_easyms_pwd=${tool_easyms_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${dms_db_pwd}" ""
    dms_db_pwd="${pwd_return}"
    echo "dms_db_pwd=${dms_db_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${easyms_db_pwd}" ""
    easyms_db_pwd="${pwd_return}"
    echo "easyms_db_pwd=${easyms_db_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${csweb_db_pwd}" ""
    csweb_db_pwd="${pwd_return}"
    echo "csweb_db_pwd=${csweb_db_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    
    encrypt_by_uniep "${pmdashboard_db_pwd}" ""
    pmdashboard_db_pwd="${pwd_return}"
    echo "pmdashboard_db_pwd=${pmdashboard_db_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    encrypt_by_uniep "${cs_service_pwd}" ""
    cs_service_pwd="${pwd_return}"
    echo "cs_service_pwd=${cs_service_pwd}" >> ${PreSet_PATH}/Common/passwords.properties


    if [ ! -f "/.dockerenv" ] && [ "X${dv_force_change_os_pwd}" == "XYes" ];then
        encrypt_by_uniep "${new_sshusr_pwd}" ""
        new_sshusr_pwd="${pwd_return}"
        echo "new_sshusr_pwd=${new_sshusr_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
        encrypt_by_uniep "${new_root_pwd}" ""
        new_root_pwd="${pwd_return}"
        echo "new_root_pwd=${new_root_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    fi
    web_admin_pwd=$(cat ${PreSet_PATH}/Common/pwfile.json| sed 's/}/\n/g' | grep -w web_admin_user_value | awk -F : '{print $3}')
    web_admin_pwd=${web_admin_pwd#*\"}
    web_admin_pwd=${web_admin_pwd%%\"*}
    decrypt_password ${web_admin_pwd}
    web_admin_pwd=${decryptPasswd}
    encrypt_by_uniep "${web_admin_pwd}" ""
    web_admin_pwd="${pwd_return}"
    echo "web_admin_pwd=${web_admin_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    
    zenith_sys_pwd=$(cat ${PreSet_PATH}/Common/pwfile.json| sed 's/}/\n/g' | grep -w zenith-sys | awk -F : '{print $3}')
    zenith_sys_pwd=${zenith_sys_pwd#*\"}
    zenith_sys_pwd=${zenith_sys_pwd%%\"*}
    decrypt_password ${zenith_sys_pwd}
    zenith_sys_pwd=${decryptPasswd}
    encrypt_by_uniep "${zenith_sys_pwd}"
    zenith_sys_pwd="${pwd_return}"
    echo "zenith_sys_pwd=${zenith_sys_pwd}" >> ${PreSet_PATH}/Common/passwords.properties
    
    echo "DV_TOOLKIT_INSTALL=${DV_TOOLKIT_INSTALL}" >> ${PreSet_PATH}/Common/passwords.properties
}

function get_version_num()
{
    local version=$1

    V_version=$(echo ${version} | sed -rn 's/V(.*)R.*/\1/p' | sed 's/^0/1/g' )
    R_version=$(echo ${version} | sed -rn 's/.*R([0-9]{1,})C.*/\1/p')
    C_version=$(echo ${version} | sed -rn 's/.*R[0-9]{1,}C([0-9]{1,})[SPC|CP|B|T].*/\1/p')
    CP_version=$(echo ${version} | sed -rn 's/.*CP([0-9]{1,})B.*/\1/p')
    SPC_version=$(echo ${version} | sed -rn 's/.*SPC([0-9]{1,})B.*/\1/p')
    test -z $CP_version && CP_version="${SPC_version}0"
    test "$CP_version" = "0" && CP_version=0000 
    B_version=$(echo ${version} | sed -rn 's/.*B([0-9]{1,}).*/\1/p')
    test -z $B_version && B_version=000
    echo "${V_version}${R_version}${C_version}${CP_version}${B_version}"
    
}

function backup_dv_certificate()
{
    local filebackup_path=${CURRENT_PATH}/backup/${product_new_version}
    if [ -f ${CURRENT_PATH}/${product_new_version}.tag ];then
        log_echo "INFO" "backup dv certificate has been completed"
        return 0
    fi

    ${command_prefix} bash -c "mkdir -p ${filebackup_path} && chmod 750 ${filebackup_path}"
    if [ "X$(whoami)" == "Xroot" ];then
        chown ossuser:ossgroup ${filebackup_path}
    fi
    eval "${TEST_DIR} ${i2k_user_home}/etc/ssl/dv"
    if [ $? -eq 0 ];then
        ${command_prefix} rm -rf ${filebackup_path}/ossuser_dv
        ${command_prefix} bash -c "mkdir -p ${filebackup_path}/ossuser_dv && chmod 750 ${filebackup_path}/ossuser_dv"
        cp_with_result "${i2k_user_home}/etc/ssl/dv/*" "${filebackup_path}/ossuser_dv/"
    fi
    eval "${TEST_DIR} ${install_path}/SOP/etc/ssl/dv"
    if [ $? -eq 0 ];then
        ${command_prefix} rm -rf ${filebackup_path}/SOP_dv
        ${command_prefix} bash -c "mkdir -p ${filebackup_path}/SOP_dv && chmod 750 ${filebackup_path}/SOP_dv"
        if [ "X$(whoami)" == "Xroot" ];then
            chown ossuser:ossgroup ${filebackup_path}/SOP_dv
        fi
        cp_with_result "${install_path}/SOP/etc/ssl/dv/*" "${filebackup_path}/SOP_dv/"
    fi
    eval "${TEST_DIR} ${i2k_install_path}/etc/ssl/dv"
    if [ $? -eq 0 ];then
        ${command_prefix} rm -rf ${filebackup_path}/DVEngineeringService_dv
        ${command_prefix} bash -c "mkdir -p ${filebackup_path}/DVEngineeringService_dv && chmod 750 ${filebackup_path}/DVEngineeringService_dv"
        if [ "X$(whoami)" == "Xroot" ];then
            chown ossuser:ossgroup ${filebackup_path}/DVEngineeringService_dv
        fi
        cp_with_result "${i2k_install_path}/etc/ssl/dv/*" "${filebackup_path}/DVEngineeringService_dv/"
    fi
    
    backup_sign_dv
    touch ${CURRENT_PATH}/${product_new_version}.tag
    backup_versions=($(ls ${CURRENT_PATH}/backup/|grep V8))
    if [ ${#backup_versions[*]} -gt 3 ];then
        declare -A backup_dirs
        version_num_array=()
        for backup_version in $(ls ${CURRENT_PATH}/backup/|grep V8);
        do
            version_num=$(get_version_num ${backup_version})
            backup_dirs["${version_num}"]=${backup_version}
            version_num_array+=("${version_num}")
        done
        version_num_array=($(echo ${version_num_array[*]}|sed 's# #\n#g'|sort -r))
        for old_dir in "${version_num_array[@]:3}";
        do
            if [ -d ${CURRENT_PATH}/backup/${backup_dirs["${old_dir}"]} ];then
                ${command_prefix} rm -rf ${CURRENT_PATH}/backup/${backup_dirs["${old_dir}"]} >/dev/null 2>&1
                log_echo "INFO" "remove ${CURRENT_PATH}/backup/${backup_dirs["${old_dir}"]}"
            fi
        done
    fi
}

function rollback_dv_certificate()
{
    local product_new_version=$(cat ${CURRENT_PATH}/DV_config.properties.tmp | grep "product_new_version" | awk -F'=' '{print $2}')
    if [ -z ${product_new_version} ];then
        log_echo "ERROR" "product_new_version is null,please check."
        exit 1
    fi
    backup_filepath="${CURRENT_PATH}/backup/${product_new_version}"
    local protect_current_version=$(ls -d ${CURRENT_PATH}/backup/${product_new_version}/ossuser_python_*| awk -F "_" '{print $NF}')
    log_echo "INFO" "product_new_version=${product_new_version},protect_current_version=${protect_current_version}."
    log_echo "INFO" "backup_filepath="${CURRENT_PATH}/backup/${product_new_version}""
    log_echo "INFO" "start to roll back the certificate directory."

    if [ -d "${backup_filepath}/SOP_dv" ];then
        log_echo "INFO" "rollback ${install_path}/SOP/etc/ssl/dv."
        if [ ! -d ${install_path}/SOP/etc/ssl/dv ];then
            sudo -u ossuser bash -c "mkdir -p ${install_path}/SOP/etc/ssl/dv"
            sudo -u ossuser bash -c "chown ossuser:ossgroup ${install_path}/SOP/etc/ssl/dv"
            sudo -u ossuser bash -c "chmod 700 ${install_path}/SOP/etc/ssl/dv"
        fi
        sudo -u ossuser bash -c "rm -rf ${install_path}/SOP/etc/ssl/dv/*"
        sudo -u ossuser bash -c "cp -rp ${backup_filepath}/SOP_dv/* ${install_path}/SOP/etc/ssl/dv/"
    else
        log_echo "INFO" "${backup_filepath}/SOP_dv is not exist,no need rollback."
    fi

    if [ -d "${backup_filepath}/ossuser_dv" ];then
        log_echo "INFO" "rollback /home/<USER>/etc/ssl/dv."
        if [ ! -d /home/<USER>/etc/ssl/dv ];then
            sudo -u ossuser bash -c "mkdir -p /home/<USER>/etc/ssl/dv"
            sudo -u ossuser bash -c "chown ossuser:ossgroup /home/<USER>/etc/ssl/dv"
            sudo -u ossuser bash -c "chmod 700 /home/<USER>/etc/ssl/dv"
        fi
        sudo -u ossuser bash -c "rm -rf /home/<USER>/etc/ssl/dv/*"
        sudo -u ossuser bash -c "cp -rf ${backup_filepath}/SOP_dv/* /home/<USER>/etc/ssl/dv"
    else
        log_echo "INFO" "${backup_filepath}/ossuser_dv is not exist,no need rollback."
    fi


    if [ -d "${backup_filepath}/DVEngineeringService_dv" ];then
        log_echo "INFO" "rollback ${i2k_install_path}/etc/ssl/dv."
        sudo -u ossuser bash -c "rm -rf ${i2k_install_path}/etc/ssl/dv/*"
        sudo -u ossuser bash -c "cp -rp ${backup_filepath}/DVEngineeringService_dv/* ${i2k_install_path}/etc/ssl/dv/"
    else
        log_echo "INFO" "${backup_filepath}/DVEngineeringService_dv is not exist,no need rollback."
    fi


    if [ -d "${backup_filepath}/SOP_sign_dv" ];then
        log_echo "INFO" "rollback ${install_path}/SOP/etc/sign_dv."
        sudo -u ossuser bash -c "rm -rf ${install_path}/SOP/etc/sign_dv/*"
        sudo -u ossuser bash -c "cp -rpf ${backup_filepath}/SOP_sign_dv/* ${install_path}/SOP/etc/sign_dv/"
    else
        log_echo "INFO" "${backup_filepath}/SOP_sign_dv is not exist,no need rollback."
    fi
    
    log_echo "INFO" "delete ${backup_filepath}."
    sudo -u ossuser bash -c "rm -rf ${backup_filepath:?}"
}


function stop_docker_process()
{

    is_integrated_df_and_service=$(cat ${dv_cfg_file}.tmp | grep "^is_integrated_df_and_service=" |awk -F'=' '{print $2}')

    if [ "X$(echo ${is_integrated_df_and_service}|tr 'a-z' 'A-Z')" != "XYES" ];then
        systemctl stop docker >> ${LOG_FILE} 2>&1
    
        systemctl disable docker >> ${LOG_FILE} 2>&1
    fi
    
    return 0
}

function backup_sign_dv()
{
    local filebackup_path=${CURRENT_PATH}/backup/${product_new_version}
    ${command_prefix} bash -c "mkdir -p ${filebackup_path} && chmod 750 ${filebackup_path}"
    eval "${TEST_DIR} ${i2k_user_home}/etc/sign_dv"
    if [ $? -eq 0 ];then
        ${command_prefix} rm -rf ${filebackup_path}/ossuser_sign_dv
        ${command_prefix} bash -c "mkdir -p ${filebackup_path}/ossuser_sign_dv && chmod 750 ${filebackup_path}/ossuser_sign_dv"
        cp_with_result "${i2k_user_home}/etc/sign_dv/." "${filebackup_path}/ossuser_sign_dv/"
    fi
    eval "${TEST_DIR} ${install_path}/SOP/etc/sign_dv"
    if [ $? -eq 0 ];then
        ${command_prefix} rm -rf ${filebackup_path}/SOP_sign_dv
        ${command_prefix} bash -c "mkdir -p ${filebackup_path}/SOP_sign_dv && chmod 750 ${filebackup_path}/SOP_sign_dv"
        cp_with_result  "${install_path}/SOP/etc/sign_dv/."  "${filebackup_path}/SOP_sign_dv/"
    fi
}

function rollback_sign_dv()
{
    cp -rp ${i2k_user_home}/etc/sign_dv ${CURRENT_PATH}/backup/ossuser_sign_dv_new
    if [ -d "${CURRENT_PATH}/backup/ossuser_sign_dv" ];then
        rm -rf ${i2k_user_home}/etc/sign_dv/*
        cp -rp ${CURRENT_PATH}/backup/ossuser_sign_dv/* ${i2k_user_home}/etc/sign_dv/
        find ${i2k_user_home}/etc/sign_dv -type f 2>/dev/null |xargs chmod 600 2>/dev/null
    else
        rm -rf ${i2k_user_home}/etc/sign_dv
    fi

    cp -rp ${install_path}/SOP/etc/sign_dv ${CURRENT_PATH}/backup/SOP_sign_dv_new
    if [ -d "${CURRENT_PATH}/backup/SOP_sign_dv" ];then
        rm -rf ${install_path}/SOP/etc/sign_dv/*
        cp -rp ${CURRENT_PATH}/backup/SOP_sign_dv/* ${install_path}/SOP/etc/sign_dv/
        find ${install_path}/SOP/etc/sign_dv -type f 2>/dev/null |xargs chmod 600 2>/dev/null
    else
        rm -rf ${install_path}/SOP/etc/sign_dv
    fi
}

function init_infocollect_i18n_file()
{
    local log_path=""
    local is_log_extend=""
    if [ "X${is_upgrade}" == "XYes" ];then
         is_log_extend=$(cat ${PreSet_PATH}/Upgrade_Root_Preset/expInfo/product_SOP.json | awk -F "detailed_log_printing_mode" '{print $2}' |awk -F "\"" '{print $3}')
         if [ "X${is_log_extend}" == "XYes" ]; then
             log_path="LOG_PATH"
         else
             log_path="INSTALL_PATH"
         fi
    else
         is_log_extend=$(grep "^detailed_log_printing_mode=.*$" ${dv_cfg_file}.tmp | awk -F "=" '{print $2}')
         if [ "X${is_log_extend}" == "XYes" ]; then
             log_path="LOG_PATH"
         else
             log_path="INSTALL_PATH"
         fi
    fi
    
    local file_name_list="infocollect_en_US.properties infocollect_zh.properties infocollect_zh_CN.properties"
    for file_name in ${file_name_list};
    do
        sed -i "s/{{LOG_DIR_PATH}}/${log_path}/g" ${PreSet_PATH}/tools/infocollect/i18n/${file_name}
    done
}

function install_infocollect()
{
    local dest_path="$1"
    if [ -z "${dest_path}" ];then
        log_echo "ERROR" "dest_path=${dest_path} is null.of ${FUNCNAME}"
        exit 1
    fi
    
    if [ ! -d "${dest_path}" ];then
        log_echo "ERROR" "dest_path=${dest_path} is not exists.of ${FUNCNAME}"
        exit 1
    fi

    ## import uniep_version
    uniep_version=$(grep -w "softwareVersion" "${install_path}/manager/var/share/software_define.yaml" | awk -F ': ' '{print $2}')

    ## diff
    backup_file_name="infocollect_bak_${uniep_version}.zip"

    local infocollect_path=${dest_path}/infocollect
    if [ -d "${infocollect_path}" ] && [ ! -f "${infocollect_path}/backup/${backup_file_name}" ];then
        ## backup previous infocollect module
        log_echo "INFO" "backup infocollect module"
        cd "${infocollect_path}" || return
        mkdir "${PreSet_PATH}/tools/infocollect/backup"
        [ -f ${PreSet_PATH}/tools/infocollect/backup/${backup_file_name} ] && rm -f ${PreSet_PATH}/tools/infocollect/backup/${backup_file_name}
        zip -r "${PreSet_PATH}/tools/infocollect/backup/${backup_file_name}" "." -x "*.zip"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Execute [cd ${infocollect_path}; zip -r ${PreSet_PATH}/tools/infocollect/backup/${backup_file_name} .] failed"
            exit 1
        fi
        cd -
    fi

    if [ -f "${infocollect_path}/backup/${backup_file_name}" -a ! -f "${PreSet_PATH}/tools/infocollect/backup/${backup_file_name}" ];then
        cp -rpf ${infocollect_path}/backup/${backup_file_name} ${PreSet_PATH}/tools/infocollect/backup/${backup_file_name}
    fi

    init_infocollect_i18n_file

    find ${dest_path}/infocollect -maxdepth 1 -mindepth 1 ! \( -name "data_ui" -o -name "data_log" \) |xargs rm -rf
    cp -rf ${PreSet_PATH}/tools/infocollect  ${dest_path}
    chown -R ossadm:ossgroup ${infocollect_path}
    if [ -d "/var/log/oss/manager/UniEPService" ];then
        find ${infocollect_path}/feature_custom_cfg_template -type f |xargs -i sed -i "s#{{log_path}}#/var/log/oss#g" {}
        sed -i "s#{{log_path}}#/var/log/oss#g" ${infocollect_path}/select_service_proce/dv_select_service_proce.sh
        sed -i "s#{{log_path}}#/var/log/oss#g" ${infocollect_path}/common/common.sh
        sed -i "s#{{log_path}}#/var/log/oss#g" ${infocollect_path}/common/node_infocollect.sh
    else
        find ${infocollect_path}/feature_custom_cfg_template -type f |xargs -i sed -i "s#{{log_path}}#/opt/oss/log#g" {}
        sed -i "s#{{log_path}}#/opt/oss/log#g" ${infocollect_path}/select_service_proce/dv_select_service_proce.sh
        sed -i "s#{{log_path}}#/opt/oss/log#g" ${infocollect_path}/common/common.sh
        sed -i "s#{{log_path}}#/opt/oss/log#g" ${infocollect_path}/common/node_infocollect.sh
    fi
    
    find ${infocollect_path} -type d |xargs -i chmod 750 {} >> ${LOG_FILE} 2>&1
    find ${infocollect_path} -type f -a -not -perm 500 -a \( -name "*.sh" -o -name "*.pyc" -o -name "*.py" -o -name "*.exp" -o -name "*.sql" \) -print0 | xargs -0 --no-run-if-empty chmod 500 >> ${LOG_FILE} 2>&1
    find ${infocollect_path} -type f -a \( -name "*.txt" -o -name "*.json" -o -name "*.csv" -o -name "*.ini" -o -name "*.properties" -o -name "*.log" -o -name "*.cfg" -o -name "*.tar.gz" -o -name "*.zip" -o -name "*.config" \) -print0 | xargs -0 --no-run-if-empty chmod 600 >> ${LOG_FILE} 2>&1
    find ${infocollect_path} -type f |xargs -i dos2unix {} >> ${LOG_FILE} 2>&1

    ## 不需要执行的sql文件，权限不大于640
    chmod 400 ${infocollect_path}/select_pm_db_disk_mount_point/lib/sql_file.sql

    if [ ! -f /home/<USER>/.bashrc ];then
        if [ "X$(whoami)" == "Xroot" ];then
            su - ossadm -c "touch /home/<USER>/.bashrc"
        else
            touch /home/<USER>/.bashrc
        fi

    fi
    
    if [ -f /home/<USER>/.bashrc ];then
        cat /home/<USER>/.bashrc | grep "alias infocollect=" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            echo "alias infocollect='sh ${infocollect_path}/infocollect.sh'" >> /home/<USER>/.bashrc
        fi
    fi
}

function rollback_infocollect()
{
    local dest_path=$1
    local tmp_dir=/tmp/DVPreSet_upgrade_ossadm

    if [ -z "${dest_path}" ];then
        log_echo "ERROR" "dest_path=${dest_path} is null.of ${FUNCNAME}"
        exit 1
    fi

    if [ ! -d ${dest_path} ];then
        log_echo "ERROR" "dest_path=${dest_path} is not exists.of ${FUNCNAME}"
        exit 1
    fi

    ## import uniep_version
    uniep_version=$(grep -w "softwareVersion" "${install_path}/manager/var/share/software_define.yaml" | awk -F ': ' '{print $2}')

    ## diff
    backup_file_name="infocollect_bak_${uniep_version}.zip"

    local infocollect_path=${dest_path}/infocollect
    if [ -d ${infocollect_path} ];then
        if [ -f "${infocollect_path}/backup/${backup_file_name}" ];then
            log_echo "INFO" "rollback infocollect module"
            cp -pf "${infocollect_path}/backup/${backup_file_name}" "${dest_path}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Execute [cp -pf ${infocollect_path}/backup/${backup_file_name} ${dest_path}] failed"
                exit 1
            fi
            find "${dest_path}/infocollect" -maxdepth 1 -mindepth 1 ! \( -name "data_ui" -o -name "data_log" \) |xargs rm -rf
            unzip -o "${dest_path}/${backup_file_name}" -d "${infocollect_path}"
            rm -f "${dest_path}/${backup_file_name}"
            source "${install_path}/manager/bin/engr_profile.sh";ipmc_adm -cmd restartapp -tenant manager -app DVOMMgrService
        fi
    fi
}

function install_log_level_manager()
{
    dest_path=$1

    local log_manager_path=${dest_path}/log_level_manager

    rm -rf ${dest_path}/log_level_manager
    cp -rf ${PreSet_PATH}/tools/log_level_manager  ${dest_path}
    chown -R ossadm:ossgroup ${log_manager_path}

    find ${log_manager_path} -type d |xargs -i chmod 750 {} >> ${LOG_FILE} 2>&1
    find ${log_manager_path} -type f -a -not -perm 500 -a \( -name "*.sh" -o -name "*.pyc" -o -name "*.py" -o -name "*.exp" \) -print0 | xargs -0 --no-run-if-empty chmod 500 >> ${LOG_FILE} 2>&1
    find ${log_manager_path} -type f -a \( -name "*.txt" -o -name "*.json" -o -name "*.csv" -o -name "*.ini" -o -name "*.properties" -o -name "*.log" -o -name "*.cfg" -o -name "*.tar.gz" -o -name "*.zip" \) -print0 | xargs -0 --no-run-if-empty chmod 600 >> ${LOG_FILE} 2>&1
    find ${log_manager_path} -type f |xargs -i dos2unix {} >> ${LOG_FILE} 2>&1

    if [ ! -f /home/<USER>/.bashrc ];then
        if [ "X$(whoami)" == "Xroot" ];then
            su - ossadm -c "touch /home/<USER>/.bashrc"
        else
            touch /home/<USER>/.bashrc
        fi
    fi

    if [ -f /home/<USER>/.bashrc ];then
        cat /home/<USER>/.bashrc | grep "alias log_level_manager=" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            echo "alias log_level_manager='sh ${log_manager_path}/log_level_manager.sh'" >> /home/<USER>/.bashrc
        fi
    fi
}

function install_uniep_tool()
{
    local dest_path="$1"
    local install_tool_name="$2"
    
    if [ -z "${dest_path}" ];then
        log_echo "ERROR" "dest_path=${dest_path} is null.of ${FUNCNAME}"
        exit 1
    fi

    if [ ! -d "${dest_path}" ];then
        log_echo "ERROR" "dest_path=${dest_path} is not exists.of ${FUNCNAME}"
        exit 1
    fi
    
    if [ "X$install_tool_name" == "Xlog_level_manager" ];then
        tool_install_path=${dest_path}/log_level_manager
        
    elif [ "X$install_tool_name" == "Xdaylight_saving_time_update" ];then
        tool_install_path=${dest_path}/daylight_saving_time_update
    else
        log_echo "ERROR" "$install_tool_name is not supported, please check"
        exit 1
    fi
    
    rm -rf ${tool_install_path:?}
    cp -rf ${PreSet_PATH}/tools/${install_tool_name}  ${dest_path}
    chown -R ossadm:ossgroup ${tool_install_path}
    
    find ${tool_install_path} -type d |xargs -i chmod 750 {} >> ${LOG_FILE} 2>&1
    find ${tool_install_path} -type f -a -not -perm 500 -a \( -name "*.sh" -o -name "*.pyc" -o -name "*.py" -o -name "*.exp" \) -print0 | xargs -0 --no-run-if-empty chmod 500 >> ${LOG_FILE} 2>&1
    find ${tool_install_path} -type f -a \( -name "*.txt" -o -name "*.json" -o -name "*.csv" -o -name "*.ini" -o -name "*.properties" -o -name "*.log" -o -name "*.cfg" -o -name "*.tar.gz" -o -name "*.zip" \) -print0 | xargs -0 --no-run-if-empty chmod 600 >> ${LOG_FILE} 2>&1
    find ${tool_install_path} -type f |xargs -i dos2unix {} >> ${LOG_FILE} 2>&1
    
    if [ ! -f /home/<USER>/.bashrc ];then
        if [ "X$(whoami)" == "Xroot" ];then
            su - ossadm -c "touch /home/<USER>/.bashrc"
        else
            touch /home/<USER>/.bashrc
        fi
    fi

    if [ -f /home/<USER>/.bashrc ];then
        cat /home/<USER>/.bashrc | grep "alias ${install_tool_name}=" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            echo "alias ${install_tool_name}='sh ${tool_install_path}/${install_tool_name}.sh'" >> /home/<USER>/.bashrc
        fi
    fi
    
}

function collect_network_config()
{
    local source_config=$1
    local scene=$2

    if [ ! -f "${source_config}" ];then
        log_echo "INFO" "can not get network_config_file for infocollect"
        return 0
    fi

    mkdir -p /opt/oss/infocollect/dv_config_collect/ 2> /dev/null

    local base_name=$(basename ${source_config})

    cp -f "${source_config}" "/opt/oss/infocollect/dv_config_collect/${base_name}_${scene}"
    chmod 400 "/opt/oss/infocollect/dv_config_collect/${base_name}_${scene}"
    chown ossadm:ossgroup -R /opt/oss/infocollect/dv_config_collect
    log_echo "INFO" "cp ${source_config} to /opt/oss/infocollect/dv_config_collect"
}

function install_health_check()
{
    local dest_path="$1"
    if [ -z "${dest_path}" ];then
        log_echo "ERROR" "dest_path=${dest_path} is null.of ${FUNCNAME}"
        exit 1
    fi

    if [ ! -d "${dest_path}" ];then
        log_echo "ERROR" "dest_path=${dest_path} is not exists.of ${FUNCNAME}"
        exit 1
    fi

    ## import uniep_version
    uniep_version=$(grep -w "softwareVersion" "${install_path}/manager/var/share/software_define.yaml" | awk -F ': ' '{print $2}')

    ## diff
    backup_file_name="health_check_bak_${uniep_version}.zip"

    local health_check_path=${dest_path}/health_check
    if [ -d "${health_check_path}" ] && [ ! -f "${health_check_path}/backup/${backup_file_name}" ];then
        ## backup previous health_check module
        log_echo "INFO" "backup health_check module"
        cd "${health_check_path}" || return
        [ ! -d "${PreSet_PATH}/tools/health_check/backup" ] && mkdir "${PreSet_PATH}/tools/health_check/backup"
        [ -f ${PreSet_PATH}/tools/health_check/backup/${backup_file_name} ] && rm -f ${PreSet_PATH}/tools/health_check/backup/${backup_file_name}
        zip -r "${PreSet_PATH}/tools/health_check/backup/${backup_file_name}" "." -x "backup/*" -x "log/*" >> ${LOG_FILE}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Execute [cd ${health_check_path}; zip -r ${PreSet_PATH}/tools/health_check/backup/${backup_file_name} .] failed"
            exit 1
        fi
        cd -
    fi

    if [ -f "${health_check_path}/backup/${backup_file_name}" -a ! -f "${PreSet_PATH}/tools/health_check/backup/${backup_file_name}" ];then
        cp -rpf ${health_check_path}/backup/${backup_file_name} ${PreSet_PATH}/tools/health_check/backup/${backup_file_name}
    fi

    find ${dest_path}/health_check -maxdepth 1 -mindepth 1 ! \( -name "health_check_result" -o -name "log" \) |xargs rm -rf
    cp -rf ${PreSet_PATH}/tools/health_check  ${dest_path}
    chown -R ossadm:ossgroup ${health_check_path}

    find ${health_check_path} -type d |xargs -i chmod 750 {} >> ${LOG_FILE} 2>&1
    find ${health_check_path} -type f -a -not -perm 550 -a \( -name "*.sh" -o -name "*.pyc" -o -name "*.py" -o -name "*.exp" -o -name "*.sql" \) -print0 | xargs -0 --no-run-if-empty chmod 550 >> ${LOG_FILE} 2>&1
    find ${health_check_path} -type f -a \( -name "*.txt" -o -name "*.json" -o -name "*.csv" -o -name "*.ini" -o -name "*.properties" -o -name "*.log" -o -name "*.cfg" -o -name "*.tar.gz" -o -name "*.zip" -o -name "*.config" \) -print0 | xargs -0 --no-run-if-empty chmod 600 >> ${LOG_FILE} 2>&1
    find ${health_check_path} -type f |xargs -i dos2unix {} >> ${LOG_FILE} 2>&1
}

function rollback_health_check()
{
    local dest_path=$1
    local tmp_dir=/tmp/DVPreSet_upgrade_ossadm

    if [ -z "${dest_path}" ];then
        log_echo "ERROR" "dest_path=${dest_path} is null.of ${FUNCNAME}"
        exit 1
    fi

    if [ ! -d ${dest_path} ];then
        log_echo "ERROR" "dest_path=${dest_path} is not exists.of ${FUNCNAME}"
        exit 1
    fi

    ## import uniep_version
    uniep_version=$(grep -w "softwareVersion" "${install_path}/manager/var/share/software_define.yaml" | awk -F ': ' '{print $2}')

    ## diff
    backup_file_name="health_check_bak_${uniep_version}.zip"

    local health_check_path=${dest_path}/health_check
    if [ -d ${health_check_path} ];then
        if [ -f "${health_check_path}/backup/${backup_file_name}" ];then
            log_echo "INFO" "rollback health_check module"
            cp -pf "${health_check_path}/backup/${backup_file_name}" "${dest_path}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Execute [cp -pf ${health_check_path}/backup/${backup_file_name} ${dest_path}] failed"
                exit 1
            fi
            find "${dest_path}/health_check" -maxdepth 1 -mindepth 1 ! \( -name "backup" -o -name "log" \) |xargs rm -rf
            unzip -o "${dest_path}/${backup_file_name}" -d "${health_check_path}" >> ${LOG_FILE}
            rm -f "${dest_path}/${backup_file_name}"
            source "${install_path}/manager/bin/engr_profile.sh";ipmc_adm -cmd restartapp -tenant manager -app DVOMMgrService
        fi
    fi
}

function check_dirs_io()
{
    ## /opt:4:1024,/opt/oss:4:1024
    local check_dir_list="$1"
    
    log_echo "INFO" "Star check_dir_list=${check_dir_list} io..."
    
    if [ "X${check_io}" != "XYes" ];then
        log_echo "INFO" "The check_io=${check_io} not equal Yes or this is upgrading. Not need to check io."
        return 0
    fi
    
    if [ -z "${check_dir_list}" ];then
        log_echo "ERROR" "check_dir_list=${check_dir_list} is null."
        exit 1
    fi
    
    local check_dir=""
    local check_size=""
    local need_size=""
    local need_check_dir_list=""
    for check_dir_info in $(echo "${check_dir_list}"|sed "s#,# #g");
    do
        check_dir=$(echo "${check_dir_info}"|awk -F':' '{print $1}')
        check_size=$(echo "${check_dir_info}"|awk -F':' '{print $2}')
        need_size=$(echo "${check_dir_info}"|awk -F':' '{print $3}')
        if [ -z "${check_dir}" ];then
            log_echo "INFO" "The check_dir=${check_dir} is null."
            continue
        fi
        
        if [ ! -d ${check_dir} ];then
            log_echo "INFO" "The check_dir=${check_dir} is not exists.of check_dirs_io"
            continue
        fi
        
        mounted_dir=$(df -Pm ${check_dir} |tail -1|awk '{print $NF}' )
        if [ -z "${need_check_dir_list}" ];then
            need_check_dir_list="${mounted_dir}:${check_size}:${need_size}"
            continue
        fi
        
        echo "${need_check_dir_list}" |grep "${mounted_dir}:" >> ${LOG_FILE} 2>&1
        if [ $? -ne 0 ];then
            need_check_dir_list="${need_check_dir_list},${mounted_dir}:${check_size}:${need_size}"
        fi
    done
    
    if [ -z "${need_check_dir_list}" ];then
        log_echo "ERROR" "need_check_dir_list=${need_check_dir_list} is null."
        exit 1
    fi
    
    for check_dir_info in $(echo "${need_check_dir_list}"|sed "s#,# #g");
    do
        check_dir=$(echo "${check_dir_info}"|awk -F':' '{print $1}')
        check_size=$(echo "${check_dir_info}"|awk -F':' '{print $2}')
        need_size=$(echo "${check_dir_info}"|awk -F':' '{print $3}')
        
        check_io "${check_dir}" "${check_size}" "${need_size}" || exit 1
    done
}

function check_io()
{
    local check_dir="$1"
    local check_size="$2"
    ## need tmp file size unit M.
    local need_size="$3"
    
    log_echo "INFO" "Star check_dir=${check_dir} io..."
    
    if [ -z "${check_dir}" -o -z "${check_size}" -o -z "${need_size}" ];then
        log_echo "ERROR" "check_dir=${check_dir} or check_size=${check_size} or need_size=${need_size} is null."
        return 1
    fi
    
    if [ ! -d ${check_dir} ];then
        log_echo "INFO" "The check_dir=${check_dir} is not exists."
        return 0
    fi
    
    local isNumber=$(echo "${check_size}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        log_echo "ERROR" "The check_size=${check_size} is not number.of check_dir=${check_dir}"
        return 1
    fi
    
    isNumber=$(echo "${need_size}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        log_echo "ERROR" "The need_size=${need_size} is not number.of check_dir=${check_dir}"
        return 1
    fi
    
    local file_name=$(date '+%Y%m%d_%H%M%S').log
    local avail_size=$(df -Pm ${check_dir} |awk -F' ' '{print $4}' |sed "1,1d" |sed 's/M//'|awk -F'.' '{print $1}'|head -n 1)
    local tmp_need_size=$((${need_size} + 2))
    if [ ${avail_size} -lt ${tmp_need_size} ];then
        log_echo "WARN" "The check_dir(${check_dir}) avail size(${avail_size} M) is less than need size(${tmp_need_size} M)."
        return 0
    fi
    
    [ -f ${check_dir}/${file_name} ] && rm -rf ${check_dir:?}/${file_name:?}
    [ -f ${check_dir}/check_io.log ] && rm -rf ${check_dir}/check_io.log
    local count_size=$(( 128 * ${need_size}))
    time dd if=/dev/zero of=${check_dir}/${file_name} bs=8k count=${count_size} oflag=direct > ${check_dir}/check_io.log 2>&1
    [ -f ${check_dir}/${file_name} ] && rm -rf ${check_dir:?}/${file_name:?}
    
    local io_size=$(cat ${check_dir}/check_io.log |grep -w "copied"|awk -F',' '{print $NF}'|awk -F'/' '{print $1}')
    
    local isOther=$(echo "${io_size}"|grep -i "KB\|M\|G")
    if [ -z "${isOther}" ];then
        log_echo "INFO" "The io_size(${io_size}) is other Unit.not in [KB,M,G].of check_dir=${check_dir}"
        rm -rf ${check_dir}/check_io.log
        return 0
    fi
    
    size_value=0
    tmp_info_KB=$(echo "${io_size}"|grep -i "KB" |awk '{print $1}'|awk -F'.' '{print $1}')
    [ ! -z "${tmp_info_KB}" ] && size_value=$((${tmp_info_KB}/1024))
    tmp_info_M=$(echo "${io_size}"|grep -i "M" |awk '{print $1}'|awk -F'.' '{print $1}')
    [ ! -z "${tmp_info_M}" ] && size_value=${tmp_info_M}
    tmp_info_G=$(echo "${io_size}"|grep -i "G" |awk '{print $1}'|awk -F'.' '{print $1}')
    [ ! -z "${tmp_info_G}" ] && size_value=$((${tmp_info_G}*1024))
    
    local isNumber=$(echo "${size_value}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        rm -rf ${check_dir}/check_io.log
        log_echo "ERROR" "The size_value=${size_value} is not number.of check_dir=${check_dir}"
        return 1
    fi
    
    if [ ${size_value} -lt ${check_size} ];then
        rm -rf ${check_dir}/check_io.log
        log_echo "WARN" "The io_size(${size_value} M) is less than check_size(${check_size} M). of check_dir=${check_dir}"
        return 0
    fi
    
    rm -rf ${check_dir}/check_io.log
    log_echo "INFO" "The check_dir=${check_dir} of io_size(${size_value} M) and check_size(${check_size} M) finished."
    return 0
}

function check_hostname()
{
    local node_ips=$1
    local local_hostname=$(hostname)

    if [ "${local_hostname}" == "localhost" ];then
        log_echo "ERROR" "The node host name is ${local_hostname}.hostname cannot be 'localhost',please modify it and modify /etc/hosts ."
        exit 1
    fi
    
    # 检查 /etc/hosts 文件中是否有最后字段为 localhost 且不符合标准 IP 映射的行
    local hosts_check1=$(cat /etc/hosts |grep -w "localhost" |grep -wv "127\.0\.0\.1"|grep -wv "::1" |grep -v "^[[:blank:]]*#" |awk '{if ($NF=="localhost") print $0}')
    # 检查包含 'localhost' 且两侧有空格的行，前面的 IP 是否为 127.0.0.1 或者 为 ::1
    local hosts_check2=$(cat /etc/hosts |grep -w "localhost" |grep -wv "127\.0\.0\.1"|grep -wv "::1" |grep -v "^[[:blank:]]*#" |grep "[[:blank:]]localhost[[:blank:]]")

    if [ ! -z "${hosts_check1}" ]; then
        log_echo "ERROR" "The /etc/hosts file contains invalid entries for 'localhost': ${hosts_check1}\nEnsure that 'localhost' maps only to '127.0.0.1' or '::1' and modify /etc/hosts."
        exit 1
    fi

    if [ ! -z "${hosts_check2}" ]; then
        log_echo "ERROR" "The /etc/hosts file contains an incorrect IP address for 'localhost': ${hosts_check2}. Please ensure the IP address is either '127.0.0.1' or '::1' and modify /etc/hosts."
        exit 1
    fi

    if [ "X${is_upgrade}" != "XYes" ];then
        local hostname_ret=$( hostname | grep "_" )
        if [ -z "${hostname_ret}" ] ;then
            log_echo "INFO" "The host name is normal.The hostname=$(hostname) ,of ${FUNCNAME}"
        else
            
            log_echo "ERROR" "Invalid host name. The host name cannot contain underscores (_).The hostname_ret=${hostname_ret} ,of ${FUNCNAME}"
            exit 1
        fi
        
        if [ "X${node_ips}" != "X" ];then
            for node_ip in $(echo "${node_ips}" | sed "s#,# #g")
            do
                grep -wF ${node_ip} /etc/hosts|grep -w $(hostname)
                if [ $? -ne 0 ];then
                    echo "" >> /etc/hosts
                    echo "${node_ip}    $(hostname)" >> /etc/hosts
                fi
            done
        fi
    fi
    hostname -i >/dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "hostname -i cannot get any result, please check the /etc/hosts, add the local ip and hostname to /etc/hosts"
        exit 1
    fi

}

function check_magic_number()
{
    ls [0-9a-fA-F] > /dev/null 2> /dev/null
    if [ $? -eq 0 ];then
        log_echo "WARN" "Finding magic number file [0-9a-fA-F] exist in the current directory, will delete it auto"
        rm -f [0-9a-fA-F]
    fi
}

function upgrade_check_fstab()
{
    log_echo "INFO" "upgrade_check_fstab start."
    local has_nofail=$(cat -n /etc/fstab |grep -w "DV_Preset\|DV_ATAEextend"|grep -vw "nofail" |awk '{print $1}')
    if [ -z "${has_nofail}" ];then
        log_echo "INFO" "The /etc/fstab of DV_Preset or DV_ATAEextend already has nofail ,of ${FUNCNAME}"
        return 0
    fi
    
    for line in ${has_nofail};do
        sed -i "${line}s/defaults/nofail,defaults/g" /etc/fstab
    done
    
    log_echo "INFO" "upgrade_check_fstab end."
}

function adjust_pid_max()
{
    current_pid_max=$(cat /proc/sys/kernel/pid_max|sed 's/ //g')
    if [ ${current_pid_max} -lt 240000 ];then
        item_file_value=$(grep -w "kernel.pid_max" ${SYSCTLFILE}| awk -F"=" '{print $2}' | sed 's/  */ /g' | sed 's/  *$//g' | sed 's/^  *//g')
        
        if [ "X${item_file_value}" == "X" ];then
            echo "kernel.pid_max = 240000" >> ${SYSCTLFILE}
        elif [ ${item_file_value} -lt 240000 ];then
            sed -i "s/^kernel.pid_max[ ]*=[^&]*/kernel.pid_max = 240000 /" ${SYSCTLFILE}
        fi
    fi
    
    current_threads_max=$(cat /proc/sys/kernel/threads-max|sed 's/ //g')
    if [ ${current_threads_max} -lt 240000 ];then
        item_file_value=$(grep -w "kernel.threads-max" ${SYSCTLFILE}| awk -F"=" '{print $2}' | sed 's/  */ /g' | sed 's/  *$//g' | sed 's/^  *//g')
        
        if [ "X${item_file_value}" == "X" ];then
            echo "kernel.threads-max = 240000" >> ${SYSCTLFILE}
        elif [ ${item_file_value} -lt 240000 ];then
            sed -i "s/^kernel.threads-max[ ]*=[^&]*/kernel.threads-max = 240000 /" ${SYSCTLFILE}
        fi
    fi
}

function adjust_parameter_sysctl()
{
    log_echo "INFO" "adjust parameter of os."
    SYSCTLFILE=/etc/sysctl.conf
    
    is_integrated_df_and_service=$(cat ${dv_cfg_file}.tmp | grep "^is_integrated_df_and_service=" |awk -F'=' '{print $2}')
    grep -w "^[[:blank:]]*vm.nr_hugepages[[:blank:]]*=.*" ${SYSCTLFILE} > /dev/null 2>&1
    if [ $? -eq 0 ];then
        sed -i "s/^[[:blank:]]*vm.nr_hugepages[[:blank:]]*=.*/vm.nr_hugepages = 0/g" ${SYSCTLFILE}
    else
        echo "vm.nr_hugepages = 0" >> ${SYSCTLFILE}
    fi
    if [ -n "`iptables -L PREROUTING -t nat | grep -E \":${med_df_port}|:${med_snmp_port}\"`" ];then
        ip_forward="1"
    elif [ -d "/usr/lib64/dvlvslib" ];then
        ip_forward="1"
    elif  [ "X$(echo ${is_integrated_df_and_service}|tr 'a-z' 'A-Z')" == "XYES" ];then
        ip_forward="1"
    else
        ip_forward="0"
    fi

    grep -w "^[[:blank:]]*net.ipv4.ip_forward[[:blank:]]*=.*" ${SYSCTLFILE} > /dev/null 2>&1
    if [ $? -eq 0 ];then
        sed -i "s/^[[:blank:]]*net.ipv4.ip_forward[[:blank:]]*=.*/net.ipv4.ip_forward = ${ip_forward}/g" ${SYSCTLFILE}
    else
        echo "net.ipv4.ip_forward = ${ip_forward}" >> ${SYSCTLFILE}
    fi
    
    
    adjust_pid_max

    sysctl -p ${SYSCTLFILE} >/dev/null 2>&1
    log_echo "INFO" "adjust parameter of os finished."
}

function create_hofs_dir()
{
    log_echo "INFO" "create hofs dir."
    
    local node_type="$1"
    
    if [ ! -d /opt/oss/hofs/hofs_store ];then
        chmod 770 /opt/oss/
        ${command_prefix} mkdir -p /opt/oss/hofs/hofs_store
        chmod 750 /opt/oss/
    fi
    
    if [ "X${node_type}" == "X" ];then
        ${command_prefix} mkdir -p /opt/oss/hofs/hofs_fuse >/dev/null 2>&1
        if [ "X$(whoami)" == "Xroot" ];then
            chown ossuser:ossgroup /opt/oss/hofs/hofs_fuse >/dev/null 2>&1
        fi
        ${command_prefix} chmod 700 /opt/oss/hofs/hofs_fuse >/dev/null 2>&1
    fi
    if [ "X$(whoami)" == "Xroot" ];then
        chown ossuser:ossgroup /opt/oss/hofs
        chown ossuser:ossgroup /opt/oss/hofs/hofs_store
    fi
    ${command_prefix}chmod 700 /opt/oss/hofs
    ${command_prefix}chmod 700 /opt/oss/hofs/hofs_store
    log_echo "INFO" "create hofs dir finished."
}

function setfacl_crl()
{
    if [ -d "/etc/pki/tls/certs" ];then
        setfacl -m g:ossgroup:rx /etc/pki/tls/certs
        setfacl -m g:ossgroup:r /etc/pki/tls/certs/HuaweiRootCAsipCRLs.crl
        setfacl -m g:ossgroup:r /etc/pki/tls/certs/HuaweiRootCAsipCRLs_Release.crl
        setfacl -m m::rwx /etc/pki/tls/certs
        setfacl -m m::rwx /etc/pki/tls/certs/HuaweiRootCAsipCRLs.crl
        setfacl -m m::rwx /etc/pki/tls/certs/HuaweiRootCAsipCRLs_Release.crl
        chmod 640 /etc/pki/tls/certs/HuaweiRootCAsipCRLs.crl /etc/pki/tls/certs/HuaweiRootCAsipCRLs_Release.crl
    fi
    
    if [ -d "/var/lib/ca-certificates/pem" ];then
        setfacl -m g:ossgroup:rx /var/lib/ca-certificates/pem
        setfacl -m g:ossgroup:r /var/lib/ca-certificates/pem/HuaweiRootCAsipCRLs.crl
        setfacl -m g:ossgroup:r /var/lib/ca-certificates/pem/HuaweiRootCAsipCRLs_Release.crl
        setfacl -m m::rwx /var/lib/ca-certificates/pem
        setfacl -m m::rwx /var/lib/ca-certificates/pem/HuaweiRootCAsipCRLs.crl
        setfacl -m m::rwx /var/lib/ca-certificates/pem/HuaweiRootCAsipCRLs_Release.crl
        chmod 640 /var/lib/ca-certificates/pem/HuaweiRootCAsipCRLs.crl /var/lib/ca-certificates/pem/HuaweiRootCAsipCRLs_Release.crl
    fi
}

function merge_network_pm()
{
    if [ -f /.dockerenv ];then
        log_echo "INFO" "cp -p ${json_source_path}/networkinfo_pm_docker.json ${json_tmp_path}/networkinfo_sop_tmp.json"
        cp -p ${json_source_path}/networkinfo_pm_docker.json ${json_tmp_path}/networkinfo_pm.json
    else
        log_echo "INFO" "cp -p ${json_source_path}/networkinfo_pm.json ${json_tmp_path}/networkinfo_sop_tmp.json"
        cp -p ${json_source_path}/networkinfo_pm.json ${json_tmp_path}/networkinfo_pm.json
    fi
    cp -f ${PreSet_PATH}/Common/merge_json.py ${json_tmp_path}/
    
    local tmp_pm_host_extend_cluster_node_ipv4_list="${SMHostExtend_Cluster_Node_IPV4_List}"
    local tmp_pm_extend_cluster_node_ipv4_list="${SMExtend_Cluster_Node_IPV4_List}"
    local tmp_pm_extend_cluster_node_ipv6_list="${SMExtend_Cluster_Node_IPV6_List}"
    local tmp_pm_extend_cluster_node_mgr_ipv6_list="${SMExtend_Cluster_Node_MGR_IPV6_List}"
    if [ "${is_old_extend_mode}" == "Yes" ];then
        tmp_pm_host_extend_cluster_node_ipv4_list="${HostExtend_Cluster_Node_IPV4_List}"
        tmp_pm_extend_cluster_node_ipv4_list="${Extend_Cluster_Node_IPV4_List}"
        tmp_pm_extend_cluster_node_ipv6_list="${Extend_Cluster_Node_IPV6_List}"
        tmp_pm_extend_cluster_node_mgr_ipv6_list="${Extend_Cluster_Node_MGR_IPV6_List}"
    fi
    
    if [ "X${RS_LVS_IPV4_NIC}" == "X" ];then
        sed -i "s#RS_LVS_IPV4_NIC#LVS_IPV4_NIC#g"  ${json_tmp_path}/networkinfo_pm.json
        sed -i "s#RS_LVS_IPV6_NIC#LVS_IPV6_NIC#g"  ${json_tmp_path}/networkinfo_pm.json
        if [ "${zenith_paramgroup_file}" == "large" ] && [ "X${netWorkType}" == "XM" ];then
            if ( ! is_empty_param "${tmp_pm_extend_cluster_node_ipv4_list}" ) || ( ! is_empty_param "${tmp_pm_extend_cluster_node_ipv6_list}" );then
                local ExtendNode1=$(echo ${tmp_pm_extend_cluster_node_ipv4_list}|awk -F',' '{print $1}')
                local ExtendNode2=$(echo ${tmp_pm_extend_cluster_node_ipv4_list}|awk -F',' '{print $2}')
                local ExtendNode1_IPV6=$(echo ${tmp_pm_extend_cluster_node_ipv6_list}|awk -F',' '{print $1}')
                local ExtendNode2_IPV6=$(echo ${tmp_pm_extend_cluster_node_ipv6_list}|awk -F',' '{print $2}')
                local ExtendNode1_MGR_IPV6=$(echo ${tmp_pm_extend_cluster_node_mgr_ipv6_list}|awk -F',' '{print $1}')
                local ExtendNode2_MGR_IPV6=$(echo ${tmp_pm_extend_cluster_node_mgr_ipv6_list}|awk -F',' '{print $2}')
                sed -i "s#{{DVPrimary_IPV4}}#${ExtendNode1}#g"  ${json_tmp_path}/networkinfo_pm.json
                sed -i "s#{{DVSecondary_IPV4}}#${ExtendNode2}#g"  ${json_tmp_path}/networkinfo_pm.json
                sed -i "s#{{DVPrimary_IPV6}}#${ExtendNode1_IPV6}#g"  ${json_tmp_path}/networkinfo_pm.json
                sed -i "s#{{DVSecondary_IPV6}}#${ExtendNode2_IPV6}#g"  ${json_tmp_path}/networkinfo_pm.json
                sed -i "s#{{DVPrimary_MGR_IPV6}}#${ExtendNode1_MGR_IPV6}#g"  ${json_tmp_path}/networkinfo_pm.json
                sed -i "s#{{DVSecondary_MGR_IPV6}}#${ExtendNode2_MGR_IPV6}#g"  ${json_tmp_path}/networkinfo_pm.json

                if [ "X${IPType}" == "Xipv4" ];then
                    sed -i "s#{{DVPrimary_IP}}#${ExtendNode1}#g"  ${json_tmp_path}/networkinfo_pm.json
                    sed -i "s#{{DVSecondary_IP}}#${ExtendNode2}#g"  ${json_tmp_path}/networkinfo_pm.json
                else
                    sed -i "s#{{DVPrimary_IP}}#${ExtendNode1_MGR_IPV6}#g"  ${json_tmp_path}/networkinfo_pm.json
                    sed -i "s#{{DVSecondary_IP}}#${ExtendNode2_MGR_IPV6}#g"  ${json_tmp_path}/networkinfo_pm.json
                fi
            else
                log_echo "ERROR" "Need extend node to config network info, please check."
                exit 1
            fi
        else
            if [ "X${IPType}" == "Xipv4" ];then
                sed -i "s#{{DVPrimary_IP}}#{{DVPrimary_IPV4}}#g"  ${json_tmp_path}/networkinfo_pm.json
                sed -i "s#{{DVSecondary_IP}}#{{DVSecondary_IPV4}}#g"  ${json_tmp_path}/networkinfo_pm.json
            else
                sed -i "s#{{DVPrimary_IP}}#{{DVPrimary_MGR_IPV6}}#g"  ${json_tmp_path}/networkinfo_pm.json
                sed -i "s#{{DVSecondary_IP}}#{{DVSecondary_MGR_IPV6}}#g"  ${json_tmp_path}/networkinfo_pm.json
            fi
        fi
    else
        if [ "${zenith_paramgroup_file}" == "large" ] && ( ! is_empty_param "${tmp_pm_extend_cluster_node_ipv4_list}" ) ;then
            HostExtendNode1=$(echo ${tmp_pm_host_extend_cluster_node_ipv4_list}|awk -F',' '{print $1}')
            HostExtendNode2=$(echo ${tmp_pm_host_extend_cluster_node_ipv4_list}|awk -F',' '{print $2}')
            PodExtendNode1=$(echo ${tmp_pm_extend_cluster_node_ipv4_list}|awk -F',' '{print $1}')
            PodExtendNode2=$(echo ${tmp_pm_extend_cluster_node_ipv4_list}|awk -F',' '{print $2}')
            
            sed -i "s#{{HostPrimary_IPV4}}#${HostExtendNode1}#g"  ${json_tmp_path}/networkinfo_pm.json
            sed -i "s#{{HostSecondary_IPV4}}#${HostExtendNode2}#g"  ${json_tmp_path}/networkinfo_pm.json
            
            sed -i "s#{{DVPrimary_IPV4}}#${PodExtendNode1}#g"  ${json_tmp_path}/networkinfo_pm.json
            sed -i "s#{{DVSecondary_IPV4}}#${PodExtendNode2}#g"  ${json_tmp_path}/networkinfo_pm.json
            
            sed -i "s#{{HostPrimary_NETMASK}}#${LVS_IPV4_NETMASK}#g"  ${json_tmp_path}/networkinfo_pm.json
            sed -i "s#{{HostSecondary_NETMASK}}#${LVS_IPV4_NETMASK}#g"  ${json_tmp_path}/networkinfo_pm.json
            
            sed -i "s#{{DVPrimary_IP}}#${PodExtendNode1}#g"  ${json_tmp_path}/networkinfo_pm.json
            sed -i "s#{{DVSecondary_IP}}#${PodExtendNode2}#g"  ${json_tmp_path}/networkinfo_pm.json
        else
            timeout -s KILL 60  su - ossadm -c "ssh -o NumberOfPasswordPrompts=0 -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ossadm@${DVSecondary_IPV4}  date " > /dev/null  2>&1
            if [ $? -ne 0 ];then
                echo "can not use ossadm ssh to the float ip ${DVSecondary_IPV4}"
                exit 1
            fi
            if [ -f /etc/SuSE-release ];then
                HostPrimary_NETMASK=$(ifconfig |grep -F -w  ${HostPrimary_IPV4}|awk -F 'Mask:' '{print $2}')
                HostSecondary_NETMASK=$(su - ossadm -c "ssh ${DVSecondary_IPV4} ifconfig"| grep -F -w ${HostSecondary_IPV4}|awk -F 'Mask:' '{print $2}')
            else
                HostPrimary_NETMASK=$(ifconfig |grep -F -w  ${HostPrimary_IPV4}|awk  '{print $4}')
                HostSecondary_NETMASK=$(su - ossadm -c "ssh ${DVSecondary_IPV4} ifconfig"| grep -F -w ${HostSecondary_IPV4}|awk  '{print $4}')
            fi
            sed -i "s#{{HostPrimary_NETMASK}}#${HostPrimary_NETMASK}#g"  ${json_tmp_path}/networkinfo_pm.json
            sed -i "s#{{HostSecondary_NETMASK}}#${HostSecondary_NETMASK}#g"  ${json_tmp_path}/networkinfo_pm.json
        fi
        local is_docker=True
    fi
    replace_file_macro ${json_tmp_path}/networkinfo_pm.json ${source_file}
    chown ossadm:ossgroup ${json_tmp_path} ${json_tmp_path}/networkinfo_pm.json ${json_tmp_path}/merge_json.py ${json_tmp_path}/networkinfo_sop.json
    chmod 600 ${json_tmp_path}/networkinfo_pm.json ${json_tmp_path}/networkinfo_sop.json
    su - ossadm -c ". ${install_path}/manager/bin/engr_profile.sh;python ${json_tmp_path}/merge_json.py  --network_type=${netWorkType} --node_file=${json_tmp_path}/networkinfo_pm.json  --nodeSOP_file=${json_tmp_path}/networkinfo_sop.json --is_docker=\"${is_docker}\""
    if [ $? -ne 0 ];then
        log_echo "ERROR" "merge pmnode to network_sop.json failed, please check"
        exit 1
    fi
}

function networkinfo_rsnode()
{
    local func="$1"
    cp -f ${PreSet_PATH}/tools/add_rsnode.py ${json_tmp_path}/
    chown ossadm: ${json_tmp_path}/add_rsnode.py
    chmod 500 ${json_tmp_path}/add_rsnode.py
    su - ossadm -c ". ${install_path}/manager/bin/engr_profile.sh;python ${json_tmp_path}/add_rsnode.py --func=${func} --network_file=${json_tmp_path}/networkinfo_sop.json --iptype=${IPType}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute add_rsnode.py function: ${func} in network_sop.json failed, please check"
        exit 1
    fi
}

function add_rsnode_list()
{
    local func="$1"
    local mgr_ip_list=$2
    local node_ipv4_list=$3
    local node_ipv6_list=$4
    local lvs_nic=$5
    local lvs_nic_ipv6=$6
    local iptype=$7
    cp -f ${PreSet_PATH}/tools/add_rsnode.py ${json_tmp_path}/
    chown ossadm: ${json_tmp_path}/add_rsnode.py
    chmod 500 ${json_tmp_path}/add_rsnode.py
    su - ossadm -c ". ${install_path}/manager/bin/engr_profile.sh;python ${json_tmp_path}/add_rsnode.py --func=${func} --network_file=${json_tmp_path}/networkinfo_sop.json --mgr_ip_list=\"${mgr_ip_list}\" --node_ipv4_list=\"${node_ipv4_list}\" --node_ipv6_list=\"${node_ipv6_list}\" --lvs_nic=${lvs_nic}  --lvs_nic_ipv6=${lvs_nic_ipv6}  --iptype=${iptype}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "add_rsnode to network_sop.json failed, please check"
        exit 1
    fi
}

function modify_vips_network()
{
    networkinfo_rsnode "mod_lvs"
    if [ ! -f /.dockerenv ];then
        networkinfo_rsnode "add_node"
    fi
}

function merge_network_om()
{
    local OM_Node_IPV6_IP=$1
    local OM_Node_MGR_IPV6_IP=$2

    local tmp_dir="/tmp/DVPreSet"

    dos2unix ${PreSet_PATH}/om_nicinfo/${OM_Node_IPV6_IP}_nic_mask_info.txt > /dev/null 2>&1
    local OM_Node_IPV6_NETMASK=$(cat ${PreSet_PATH}/om_nicinfo/${OM_Node_IPV6_IP}_nic_mask_info.txt | grep "mask=" | awk -F"=" '{print $2}')
    local OM_Node_IPV6_NIC=$(cat ${PreSet_PATH}/om_nicinfo/${OM_Node_IPV6_IP}_nic_mask_info.txt | grep "nic=" | awk -F"=" '{print $2}')

    cp -f ${json_source_path}/networkinfo_om.json ${PreSet_PATH}/Common/merge_json.py ${json_tmp_path}/
    
    sed -i "s/{{OM_Node_IPV6_NETMASK}}/${OM_Node_IPV6_NETMASK}/g" ${json_tmp_path}/networkinfo_om.json
    sed -i "s/{{OM_Node_IPV6_IP}}/${OM_Node_IPV6_IP}/g" ${json_tmp_path}/networkinfo_om.json
    sed -i "s/{{OM_Node_IPV6_NIC}}/${OM_Node_IPV6_NIC}/g" ${json_tmp_path}/networkinfo_om.json
    sed -i "s/{{OM_Node_MGR_IPV6_IP}}/${OM_Node_MGR_IPV6_IP}/g" ${json_tmp_path}/networkinfo_om.json

    chown ossadm:ossgroup ${json_tmp_path} ${json_tmp_path}/networkinfo_om.json ${json_tmp_path}/merge_json.py ${json_tmp_path}/networkinfo_sop.json
    chmod 600 ${json_tmp_path}/networkinfo_om.json ${json_tmp_path}/networkinfo_sop.json
    su - ossadm -c ". ${install_path}/manager/bin/engr_profile.sh;python ${json_tmp_path}/merge_json.py  --network_type=${netWorkType} --node_file=${json_tmp_path}/networkinfo_om.json  --nodeSOP_file=${json_tmp_path}/networkinfo_sop.json --is_docker=\"${is_docker}\""
    if [ $? -ne 0 ];then
        log_echo "ERROR" "merge omnode to network_sop.json failed, please check"
        exit 1
    fi

    rm -f ${json_tmp_path}/networkinfo_om.json
}

function merge_network_nbi()
{
    local networkinfo_json=$1
    local networkinfo_nbi_json=$2
    cp -f ${PreSet_PATH}/Common/merge_json.py ${json_tmp_path}/
    chown ossadm:ossgroup ${json_tmp_path} ${networkinfo_json} ${networkinfo_nbi_json} ${json_tmp_path}/merge_json.py
    chmod 600 ${networkinfo_json} ${networkinfo_nbi_json}
    su - ossadm -c ". ${install_path}/manager/bin/engr_profile.sh;python ${json_tmp_path}/merge_json.py --network_file=${networkinfo_nbi_json}  --network_SOP_file=${networkinfo_json}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "merge ${networkinfo_nbi_json} to ${networkinfo_json} failed, please check"
        exit 1
    fi
    log_echo "INFO" "merge_network_nbi finished."
}

function add_rsnode_network()
{
    local node_ip=$1
    local nic_name=$2
    cp -f ${PreSet_PATH}/tools/add_rsnode.py ${json_tmp_path}/
    chown ossadm: ${json_tmp_path}/add_rsnode.py
    chmod 500 ${json_tmp_path}/add_rsnode.py
    su - ossadm -c ". ${install_path}/manager/bin/engr_profile.sh;python ${json_tmp_path}/add_rsnode.py --func=add_rsnode --network_file=${json_tmp_path}/networkinfo_sop.json --node_ip=${node_ip} --nic_name=${nic_name}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "add_rsnode to network_sop.json failed, please check"
        exit 1
    fi
}

function replace_file_same_key_value()
{
    local source_file=$1
    local dest_file=$2
    
    if [ ! -f $source_file ]; then
        log_echo "ERROR" "The file $source_file not exist, do not replace it." 
        exit 1
    fi
    
    if [ ! -f $dest_file ]; then
        log_echo "ERROR" "The file $dest_file not exist, do not replace it." 
        exit 1
    fi
    
    dos2unix $dest_file
    
    while read line || [[ -n ${line} ]]
    do
        key_value=$(echo "${line}" |grep -v "^[[:blank:]]*#"|grep "=")
        key=$(echo "${key_value}" |awk -F'=' '{print $1}')
        value=$(echo "${key_value}" |awk -F"${key}=" '{print $2}')
        sed -i "s#^[[:blank:]]*${key}=.*#${key}=${value}#g" $dest_file
    done < $source_file
}

function get_nic_of_ip()
{
    local ip="$1"
    if [ -z "${ip}" ]; then
        log_echo "ERROR" "The ip is null." 
        exit 1
    fi
    local tmp_ip=$(echo "${ip}" |sed "s/\./\\\./g")
    local ipInLocal=$(ip addr |grep -w "${tmp_ip}")
    local nic=$(ip addr |grep -w "${tmp_ip}" |awk '{print $NF}' | uniq)
    
    if [ -z "${ipInLocal}" ]; then
        log_echo "ERROR" "The ${ip} is not in the node. the ip not find.of get_nic_of_ip" 
        exit 1
    fi
    
    if [ -z "${nic}" ]; then
        log_echo "ERROR" "The ${ip} get nic is null.get nic failed." 
        exit 1
    fi
    nic_name=${nic}
    return 0
}

function get_netmask_of_ip()
{
    local ip="$1"
    if [ -z "${ip}" ]; then
        log_echo "ERROR" "The ip is null." 
        exit 1
    fi
    
    local tmp_ip=$(echo "${ip}" |sed "s/\./\\\./g")
    local ipInLocal=$(ip addr |grep -w "${tmp_ip}")

    ### 根据操作系统类型取netmask的值
    local is_suse=$(cat /etc/os-release | grep -E "SUSE|Photon")
    local netmask=$(ifconfig |grep -w "${tmp_ip}" |awk '{print $NF}'|awk -F':' '{print $2}')
    if [ "X${is_suse}" != "X" ];then
      netmask=$(ifconfig |grep -w "${tmp_ip}" |awk '{print $NF}'|awk -F':' '{print $2}')
    else
      netmask=$(ifconfig |grep -w "${tmp_ip}" |awk '{print $4}')
    fi

    if [ -z "${ipInLocal}" ]; then
        log_echo "ERROR" "The ${ip} is not in the node. the ip not find.of get_netmask_of_ip" 
        exit 1
    fi
    
    if [ -z "${netmask}" ]; then
        log_echo "ERROR" "The ${ip} get net mask is null.get net mask failed." 
        exit 1
    fi
    net_mask=${netmask}
    return 0
}

function encrypt_by_uniep()
{
    local de_pvalue="$1"
    local encrypt_type=$2

    if [ -f "/proc/sys/kernel/random/entropy_avail" ];then
        random_num=$(cat /proc/sys/kernel/random/entropy_avail)
        log_echo "INFO"  "The value of random/entropy_avail is ${random_num} ..." > /dev/null
        if [ ${random_num} -lt 300 ];then
            haveged_path=$(which haveged 2>/dev/null)
            [ -n "$haveged_path" ] && ${haveged_path} -w 1024 -v 1 > /dev/null 2>&1
        fi
    fi

    local en_pvalue=""
    local get_ret=0
    for try_count in $(seq 1 5);do
        if [ -f "/proc/sys/kernel/random/entropy_avail" ];then
            random_num=$(cat /proc/sys/kernel/random/entropy_avail)
            log_echo "INFO"  "The value of random/entropy_avail is ${random_num} ..." > /dev/null
            if [ ${random_num} -lt 300 ];then
                haveged_path=$(which haveged 2>/dev/null)
                [ -n "$haveged_path" ] && ${haveged_path} -w 1024 -v 1 > /dev/null 2>&1
            fi
        fi
        if [ "X$(whoami)" == "Xroot" ];then
            en_pvalue=$(echo "${de_pvalue} uniep:passwd"| expect ${PreSet_PATH}/tools/su_ossadm_exec.exp "${PreSet_PATH}/tools/uniep_encrypt.py" "${encrypt_type}" | grep -vE "source|logout|uniep_encrypt|exit" | tail -1 | sed 's/ //g')
        else
            en_pvalue=$(export HISTSIZE=0;source /opt/oss/manager/bin/engr_profile.sh;echo "${de_pvalue}" | ${PYTHONHOME}/bin/python "${PreSet_PATH}/tools/uniep_encrypt.py" "${encrypt_type}" | grep -vE "source|logout|uniep_encrypt|exit" | tail -1  | sed 's/ //g')
        fi
        if [ -z "`echo ${en_pvalue}|grep ^000`" -a -z "`echo ${en_pvalue}|grep ^AAA`" ];then
            log_echo "INFO" "encrypt by uniep failed , try again try_count=${try_count}"
            get_ret=1
        else
            get_ret=0
            break
        fi
    done
    
    if [ ${get_ret} -eq 1 ];then
        log_echo "ERROR" "encrypt by uniep failed, please check"
        exit 1
    fi
    pwd_return="${en_pvalue}"
    return 0  
}

function config_pam_loginuid()
{
    local ret1=$(cat /etc/pam.d/sshd |grep -w "pam_loginuid.so")
    local ret2=$(cat /etc/pam.d/sshd |grep -w "pam_loginuid.so" |grep "^[[:blank:]]*#")
    if [ ! -z "${ret1}" -a -z "${ret2}" ];then
        sed -i "/pam_loginuid.so/d" /etc/pam.d/sshd 
        echo "#session     required    pam_loginuid.so" >> /etc/pam.d/sshd
    else
        log_echo "INFO" "not need to do it."
    fi
}

function sync_sign_dv(){
    if [ ! -d "${UTILS_PATH}/old_sign_dv" ];then
        return 0
    fi
    ${command_prefix} mkdir -p ${i2k_user_home}/etc/sign_dv/
    $TEST_FILE ${i2k_user_home}/etc/sign_dv/uniagent_data_verify.pem
    if [ $? -ne 0 ];then
        return 0
    fi

    sign_now=$(${command_prefix} md5sum ${i2k_user_home}/etc/sign_dv/uniagent_data_verify.pem|awk '{print $1}')
    sign_app=$(md5sum ${UTILS_PATH}/old_sign_dv/uniagent_data_verify.pem|awk '{print $1}')
    if [ "X${sign_now}" == "X{sign_app}" ];then
        return 0
    fi
    log_echo "INFO" "need sync app sign_dv to this node."
    chmod 770  -R ${UTILS_PATH}/old_sign_dv/
    cp_with_result ${UTILS_PATH}/old_sign_dv/uniagent_data_verify.pem  ${i2k_user_home}/etc/sign_dv/
    cp_with_result ${UTILS_PATH}/old_sign_dv/uniagent_data_sign.pem    ${i2k_user_home}/etc/sign_dv/
    cp_with_result ${UTILS_PATH}/old_sign_dv/uniagent_data_sign.cfg    ${i2k_user_home}/etc/sign_dv/
    cp_with_result ${UTILS_PATH}/old_sign_dv/pkg_data_verify.pem    ${i2k_user_home}/etc/sign_dv/
    cp_with_result ${UTILS_PATH}/old_sign_dv/release_integrity         ${i2k_user_home}/etc/sign_dv/
    if [ "X$(whoami)" == "Xroot" ];then
        chown ossuser:ossgroup -R  ${i2k_user_home}/etc/sign_dv/
    fi
    ${command_prefix} chmod 600 ${i2k_user_home}/etc/sign_dv/uniagent_data_verify.pem
    ${command_prefix} chmod 600 ${i2k_user_home}/etc/sign_dv/uniagent_data_sign.pem
    ${command_prefix} chmod 600 ${i2k_user_home}/etc/sign_dv/uniagent_data_sign.cfg
    ${command_prefix} chmod 600 ${i2k_user_home}/etc/sign_dv/pkg_data_verify.pem
    ${command_prefix} chmod 700 ${i2k_user_home}/etc/sign_dv/release_integrity
    ${command_prefix} chmod 600 ${i2k_user_home}/etc/sign_dv/release_integrity/release_crls.crl
    chmod 700  -R ${UTILS_PATH}/old_sign_dv/
    log_echo "INFO" "sync sign_dv done."
}

function remove_ca_privatekey()
{
    eval "$TEST_DIR /home/<USER>/etc/ssl/dv"
    if [ $? -eq 0 ];then
        ${command_prefix}rm -f /home/<USER>/etc/ssl/dv/DV_CA.cnf
        ${command_prefix}rm -f /home/<USER>/etc/ssl/dv/dv_ca.crt
        ${command_prefix}rm -f /home/<USER>/etc/ssl/dv/dv_ca.csr
        ${command_prefix}rm -f /home/<USER>/etc/ssl/dv/dv_ca.key

        eval "$TEST_FILE /home/<USER>/i2k_install.properties"
        if [ $? -ne 0 ];then
            ${command_prefix}rm -f /home/<USER>/etc/ssl/dv/dv_ca.p12
        fi
    fi
    
    if [ -d "/opt/oss/SOP/etc/ssl/dv" ];then
        ${command_prefix} rm -f /opt/oss/SOP/etc/ssl/dv/DV_CA.cnf
        ${command_prefix} rm -f /opt/oss/SOP/etc/ssl/dv/dv_ca.crt
        ${command_prefix} rm -f /opt/oss/SOP/etc/ssl/dv/dv_ca.csr
        ${command_prefix} rm -f /opt/oss/SOP/etc/ssl/dv/dv_ca.key
        ${command_prefix} rm -f /opt/oss/SOP/etc/ssl/dv/dv_ca.p12
    fi
}

function handle_ossuser_sshagent_privatekey()
{
    if (${command_prefix} test -f /home/<USER>/sshagent_privatekey) && (${command_prefix} test ! -f /home/<USER>/.ssh/sshagent_privatekey);then
        ${command_prefix} cp -pf /home/<USER>/sshagent_privatekey /home/<USER>/.ssh/sshagent_privatekey
    fi
}

function generate_random_password() {
    local password=""
    local special_chars="~@#%^*_+-=[]{}:./?"
    local all_chars="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789$special_chars"

    # 确保密码至少包含一种大写字母、小写字母、数字和特殊字符
    password+=$(echo $all_chars | fold -w1 | shuf | head -n1)
    password+=$(echo $all_chars | fold -w1 | grep "[a-z]" | shuf | head -n1)
    password+=$(echo $all_chars | fold -w1 | grep "[A-Z]" | shuf | head -n1)
    password+=$(echo $all_chars | fold -w1 | grep "[0-9]" | shuf | head -n1)
    password+=$(echo $all_chars | fold -w1 | grep "[$special_chars]" | shuf | head -n1)

    # 剩余部分随机填充
    while [ ${#password} -lt 8 ]; do
        password+=$(echo $all_chars | fold -w1 | shuf | head -n1)
    done

    # 如果密码长度超过32，则截断到32个字符
    if [ ${#password} -gt 32 ]; then
        password=${password:0:32}
    fi

    RETURN_RANDOM_PWD="${password}"
}

function create_toolkit_user_dict()
{
    declare -g -A toolkit_user_dict

    generate_random_password
    toolkit_user_dict[tool_dms]="${RETURN_RANDOM_PWD}"

    generate_random_password
    toolkit_user_dict[tool_das]="${RETURN_RANDOM_PWD}"

    generate_random_password
    toolkit_user_dict[tool_easyms]="${RETURN_RANDOM_PWD}"

    generate_random_password
    toolkit_user_dict[dms_db]="${RETURN_RANDOM_PWD}"

    generate_random_password
    toolkit_user_dict[easyms_db]="${RETURN_RANDOM_PWD}"
}

function init_preinstall_pwd()
{
    local all_passwd="${1}"
    if [ -z "${all_passwd}" ];then
        log_echo "ERROR" "The all_passwd is null.please check it."
        exit 1
    fi
    
    if [ ! -f ${PreSet_PATH}/PreInstall/pre_user_name.properties ];then
        log_echo "ERROR" "The ${PreSet_PATH}/PreInstall/pre_user_name.properties is not exists."
        exit 1
    fi

    create_toolkit_user_dict

    local flag=0
    while read line || [[ -n ${line} ]]
    do
        if [ -z "${line}" ];then
            continue
        fi
        key=${line}
        if [ "X${DV_TOOLKIT_INSTALL}" != "XYes" ] && [[ -v toolkit_user_dict[$key] ]]; then
            continue
        fi
        value=$(echo ${all_passwd} | sed "s/ /\n/g" | awk -F"^${key}:" '{print $2}' | tr -s '\n' | tail -1)
        if [ "X${value}" == "X" ];then
            log_echo "ERROR" "Please confirm whether you have entered ${key} and it's password."
            flag=1
        fi
        export "${key}=${value}"
    done < ${PreSet_PATH}/PreInstall/pre_user_name.properties
    
    [ ${flag} -eq 1 ] && exit 1
}

function config_ossuser_sudo_uniep()
{
    [ -d "/home/<USER>/sudoScripts" ] && chattr -i -R /home/<USER>/sudoScripts > /dev/null 2>&1
    mkdir -p /home/<USER>/sudoScripts > /dev/null 2>&1
    cp -f ${CURRENT_PATH}/Common/i2kuser_root_script/sudoScripts/recover_dbuser.sh /home/<USER>/sudoScripts
     
    user_name="ossuser"
    sudo_cmd_list="/home/<USER>/sudoScripts/recover_dbuser.sh"
    sudo_user="root"

    cur_sudo=$(cat /etc/sudoers | grep "^${user_name} ALL=(${sudo_user})"|grep -v uniepsudobin |tail -1 | sed 's/\\\:/:/g')
    if [ "X$cur_sudo" == "X" ]; then
        fina_sudo="$user_name ALL=(${sudo_user}) NOPASSWD: $sudo_cmd_list"
    else
        fina_sudo=$(echo "$sudo_cmd_list" | awk -vcur_sudo="$cur_sudo" -F',' '{
            for (i = 1; i <= NF; i++)
            {
                if ( index(cur_sudo",", $i",") == 0 )
                {
                    cur_sudo=cur_sudo","$i
                }
            }
            print cur_sudo
        }' 2>/dev/null)
    fi
    sed -i "/^${user_name} ALL=(${sudo_user})/d" /etc/sudoers
    echo "${fina_sudo}" | sed 's/:/\\\:/g' | sed 's/NOPASSWD\\\:/NOPASSWD:/g' >> /etc/sudoers
    
    find /home/<USER>/sudoScripts -type f -exec chmod 500 {} +
    chmod 700 /home/<USER>/sudoScripts > /dev/null 2>&1
    chown -R root:root /home/<USER>/sudoScripts > /dev/null 2>&1
    chattr -R +i /home/<USER>/sudoScripts > /dev/null 2>&1
}

function handle_dbuser_harden()
{
    grep "^dbuser:" /etc/passwd | grep "/bin/false" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "dbuser is harden by /bin/false, change to /bin/bash for gauss upgrade"
        cp -pf /etc/passwd /etc/passwd_dvupgrade
        usermod -s /bin/bash dbuser   
    fi

    sshd -T|grep allowgroups
    if [ $? -ne 0 ];then
        cp -pf /etc/ssh/sshd_config /etc/ssh/sshd_config_dvupgrade
        ip_list=$(cat /opt/oss/manager/etc/sysconf/nodelists.json | grep "\"IP\"" | tr -d "\"|,| " | awk -F 'IP:' '{print $2}' | sort -nr | uniq | sed ':a;N;$!ba;s#\n#,#g')
        
        OLD_IFS=${IFS}
        IFS=,
        
        for ip in ${ip_list}
        do
            grep -w  "dbuser@$ip" /etc/ssh/sshd_config  > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i "/ClientAliveInterval[ |\t]\+/i\AllowUsers dbuser@$ip" /etc/ssh/sshd_config
            fi
        done
        
        IFS=${OLD_IFS}
        
        service sshd restart
    fi
}

function check_user_group()
{
    local dir_path="$1"
    [ -z "${dir_path}" ] && return 1
    [ ! -d ${dir_path} ] && return 1
    
    local user_group=$(stat -c "%U:%G" ${dir_path})
    if [ "X${user_group}" == "Xroot:root" ];then
        log_echo "INFO" "The user_group=${user_group} is root." 
        return 0
    fi
    
    log_echo "INFO" "The user_group=${user_group} not root." 
    return 1
}

function check_user_permission()
{
    check_user_group "/home/<USER>"
    if [ $? -eq 0 ];then
        chown -R ossuser:ossgroup /home/<USER>
        chown -R root:root /home/<USER>/sudoScripts
    fi
    
    check_user_group "/home/<USER>"
    if [ $? -eq 0 ];then
        chown -R devdata:ossgroup /home/<USER>
        find /home/<USER>
        chown root:sftponly /home/<USER>
        chown ossuser:ossgroup /home/<USER>/sftp/necert/iCnfgPlatform.cer 
        chown ossuser:ossgroup /home/<USER>/sftp/necert/iCnfgPlatformRoot.crt
    fi
    
    check_user_group "/home/<USER>"
    if [ $? -eq 0 ];then
        find /home/<USER>
        chown root:sftponly /home/<USER>
        chown root:ossgroup /home/<USER>/tmp
    fi
    

}

function delete_iptables_for_med()
{
    log_echo "delete iptables for med ${med_df_port}..."
    if [ "X${APP_FloatIP_IPV4}" != "X" -a "X${APP_FloatIP_IPV4}" != "X{{APP_FloatIP_IPV4}}" ];then
        i2k_float_ip=${APP_FloatIP_IPV4}
    elif [ "X${DVFloatIP_IPV4}" != "X" -a "X${DVFloatIP_IPV4}" != "X{{DVFloatIP_IPV4}}" ];then
        i2k_float_ip=${DVFloatIP_IPV4}
    else
        log_echo "INFO" "no i2k float IP ,no need delete_iptables_for_med"
        return 0
    fi
    
    iptables -L PREROUTING -t nat --line-numbers -n | grep -w "${med_df_port}" | grep -w "tcp" |grep -wF "${i2k_float_ip}"
    if [ $? -eq 0 ];then
        number_list=$(iptables -L PREROUTING -t nat --line-numbers -n | grep -w "${med_df_port}" |grep -w "tcp"|grep -wF "${i2k_float_ip}"|awk -F' ' '{print $1}'|sort -nr)
        
        for number in ${number_list}
        do
            iptables -w -t nat -D PREROUTING ${number}
        done
    fi
    
    iptables -L PREROUTING -t nat --line-numbers -n | grep -w "${med_df_port}" | grep -w "tcp" |grep -wF "${i2k_float_ip}"
    if [ $? -eq 0 ];then
        log_echo "ERROR" "delete PREROUTING for med ${med_df_port} failed, please check"
        exit 1
    fi
    
    iptables -L POSTROUTING -t nat --line-numbers -n | grep -w "${med_df_port}" | grep -w "tcp" |grep -wF "${i2k_float_ip}"
    if [ $? -eq 0 ];then
        number_list=$(iptables -L POSTROUTING -t nat --line-numbers -n | grep -w "${med_df_port}" |grep -w "tcp"|grep -wF "${i2k_float_ip}"|awk -F' ' '{print $1}'|sort -nr)
        
        for number in ${number_list}
        do
            iptables -w -t nat -D POSTROUTING ${number}
        done
    fi
    
    iptables -L POSTROUTING -t nat --line-numbers -n | grep -w "${med_df_port}" | grep -w "tcp" |grep -wF "${i2k_float_ip}"
    if [ $? -eq 0 ];then
        log_echo "ERROR" "delete POSTROUTING for med ${med_df_port} failed, please check"
        exit 1
    fi
    
    if [ -f "/etc/rc.d/rc.local" ];then
        sed -i "/iptables -t nat -A PREROUTING .* ${i2k_float_ip} -p tcp .* ${med_df_port} .*/d" /etc/rc.d/rc.local
        sed -i "/iptables -t nat -A POSTROUTING .* -p tcp .* ${med_df_port} .* ${i2k_float_ip}/d" /etc/rc.d/rc.local

        cat /etc/rc.d/rc.local | grep -wE "PREROUTING|POSTROUTING" | grep -w "${med_df_port}" | grep -w "tcp"|grep -wF "${i2k_float_ip}"
        if [ $? -eq 0 ];then
            log_echo "ERROR" "remove PREROUTING and POSTROUTING for med:${med_df_port} in /etc/rc.d/rc.local failed, please check"
            exit 1
        fi
    fi
    
    if [ -f "/etc/rc.d/after.local" ];then
        sed -i "/iptables -t nat -A PREROUTING .* ${i2k_float_ip} -p tcp .* ${med_df_port} .*/d" /etc/rc.d/after.local
        sed -i "/iptables -t nat -A POSTROUTING .* -p tcp .* ${med_df_port} .* ${i2k_float_ip}/d" /etc/rc.d/after.local

        cat /etc/rc.d/after.local | grep -wE "PREROUTING|POSTROUTING" | grep -w "${med_df_port}" | grep -w "tcp"|grep -wF "${i2k_float_ip}"
        if [ $? -eq 0 ];then
            log_echo "ERROR" "remove PREROUTING and POSTROUTING for med:${med_df_port} in /etc/rc.d/after.local failed, please check"
            exit 1
        fi
    fi
}

function build_trust_relation(){
    local APP_Primary_IP=$1
    local APP_Secondary_IP=$2

    if [ ! -f "/.dockerenv" ] && [ "X${dv_force_change_os_pwd}" == "XYes" ];then
        APP_Primary_pwd=${new_root_pwd}
        APP_Secondary_pwd=${new_root_pwd}
        primary_root_pswd=${new_root_pwd}
        secondary_root_pswd=${new_root_pwd}
        DVSecondary_pwd=${new_root_pwd}
    fi
    
    build_ossuser_trust_relation  ${APP_Primary_IP} ${APP_Secondary_IP}
    if [ -z "${dv_deploy_scale_size}" -o "X${dv_deploy_scale_size}" == "Xdefault" ];then
        build_trust_relation_devdata  ${APP_Primary_IP} ${APP_Secondary_IP}
    fi
}
function init_firstlogin_pwd()
{
    if [ ! -f "/.dockerenv" ] && [ "X${dv_force_change_os_pwd}" == "XYes" ];then
        docker_pwfile=${PreSet_PATH}/Common/docker_pwfile.json
        new_sshusr_pwd=$(cat ${docker_pwfile}| sed 's/}/\n/g' | grep -w new_sshuser | awk -F : '{print $3}'|awk -F , '{print $1}')
        new_sshusr_pwd=${new_sshusr_pwd#*\"}
        new_sshusr_pwd=${new_sshusr_pwd%%\"*}
        decrypt_password  ${new_sshusr_pwd}
        new_sshusr_pwd=${decryptPasswd}
        if [ "X${new_sshusr_pwd}" == "X" ]; then
            log_echo "ERROR" "Init password for sshuser failed, please check!" 
            exit_script
        else
            log_echo "INFO" "Init password for sshuser successed." 
        fi
        new_root_pwd=$(cat ${docker_pwfile}| sed 's/}/\n/g' | grep -w new_root | awk -F : '{print $3}'|awk -F , '{print $1}')
        new_root_pwd=${new_root_pwd#*\"}
        new_root_pwd=${new_root_pwd%%\"*}
        decrypt_password  ${new_root_pwd}
        new_root_pwd=${decryptPasswd}
        if [ "X${new_root_pwd}" == "X" ]; then
            log_echo "ERROR" "Init password for root failed, please check!" 
            exit_script
        else
            log_echo "INFO" "Init password for root successed." 
        fi
        first_login_pwdlist="${new_root_pwd}DVPreSet:Flag${new_sshusr_pwd}"
    fi
}

function check_ip_num()
{
    local param_key="$1"
    local param_value="$2"
    if [ -z "${param_key}" ];then
        log_echo "ERROR" "The param_key=${param_key} is null.please check."
        exit 1
    fi
    
    if [ -z "${param_value}" ];then
        log_echo "INFO" "The ${param_key} is null.not need to check."
        return 0
    fi
    
    ip_num=$(echo "${param_value}" |sed "s/,/\n/g" |wc -l)
    log_echo "INFO" "The ${param_key} ip_num=${ip_num}"
    return ${ip_num}
}

function clean_root_known_hosts()
{
    if [ -f /root/.ssh/known_hosts ];then
        rm -rf /root/.ssh/known_hosts
    fi
}

function delete_iptables_lvs()
{
    if [ -f "/.dockerenv" ];then
        log_echo "INFO" "Containerized scene installation ,no need to config iptables."
        return 0
    fi

    med_port="$1"
    local protocol_type="$2"
    
    if [ "X${lvs_vip_ipv4}" != "X" -a "X${lvs_vip_ipv4}" != "X{{LVS_VIP_IPV4}}" ];then
        if [ "X${APP_FloatIP_IPV4}" != "X" -a "X${APP_FloatIP_IPV4}" != "X{{APP_FloatIP_IPV4}}" ];then
            i2k_float_ip=${APP_FloatIP_IPV4}
        elif [ "X${DVFloatIP_IPV4}" != "X" -a "X${DVFloatIP_IPV4}" != "X{{DVFloatIP_IPV4}}" ];then
            i2k_float_ip=${DVFloatIP_IPV4}
        fi

        iptables -t nat -D PREROUTING --dst ${i2k_float_ip} -p ${protocol_type} --dport ${med_port} -j DNAT --to-destination ${lvs_vip_ipv4}:${med_port} > /dev/null 2>&1
        iptables -t nat -D POSTROUTING --dst ${lvs_vip_ipv4} -p ${protocol_type} --dport ${med_port} -j SNAT --to-source ${i2k_float_ip} > /dev/null 2>&1
        
        sed -i "/${med_df_port}/d" /etc/rc.d/rc.local > /dev/null 2>&1
        sed -i "/${med_df_port}/d" /etc/rc.d/after.local > /dev/null 2>&1

        if [ "$(echo ${is_integrated_df_and_service}|tr 'a-z' 'A-Z')" != "YES" ];then
            adjust_sysctl "net.ipv4.ip_forward"  "0"
        fi
    fi

    if ( ! is_empty_param "${LVS_VIP_IPV6}" );then
        if ( ! is_empty_param "${APP_FloatIP_IPV6}" );then
            i2k_float_ip=${APP_FloatIP_IPV6}
        elif ( ! is_empty_param "${DVFloatIP_IPV6}" );then
            i2k_float_ip=${DVFloatIP_IPV6}
        else
            log_echo "INFO" "not need to clean cleanup prerouting iptables lvs"
            return 0
        fi

        ip6tables -t nat -D PREROUTING --dst ${i2k_float_ip} -p ${protocol_type} --dport ${med_port} -j DNAT --to-destination [${LVS_VIP_IPV6}]:${med_port}

        sed -i "/${med_port}/d" /etc/rc.d/rc.local > /dev/null 2>&1
        sed -i "/${med_port}/d" /etc/rc.d/after.local > /dev/null 2>&1

        adjust_sysctl "net.ipv6.conf.all.forwarding"  "0"
    fi
}

function in_pod_ossadm_execute()
{
    log_echo "INFO" "in_pod_ossadm_execute start..."
    local cmd="$1"
    if [ -z "${cmd}" ];then
        log_echo "ERROR" "The cmd=${cmd} is null. please check..."
        exit 1
    fi
    
    local pod_id=$(docker ps |grep "dv-container" |awk '{print $1}')
    if [ -z "${pod_id}" ];then
        log_echo "ERROR" "Execute cmd:[ docker ps |grep dv-container |awk '{print \$1}' ] get pod id failed, get pod id is null. please check..."
        exit 1
    fi
    
    docker exec -i ${pod_id} su - ossadm -c "${cmd}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ docker exec -i ${pod_id} su - ossadm -c \"${cmd}\" ] failed, please check..."
        exit 1
    fi
    log_echo "INFO" "in_pod_ossadm_execute End."
}

function check_parameter_of_disk()
{   
    local check_result=0
    if [ "X${AUTO_DISK_MOUNT}" == "XTRUE" ];then
        if [ "X${APPNode_DISK}" != "X" -a "X${TRACE_DISK}" != "X" ] && [ "X${APPNode_DISK}" == "X${TRACE_DISK}" ];then
            log_echo "ERROR" "The parameter of APPNode_DISK and TRACE_DISK must be different."
            check_result=1
        fi

        if [ "X${EXTEND_DISK}" != "X" -a ! -n "$(echo ${EXTEND_DISK}|grep '{{')" ];then
            if [ "X${EXTEND_DISK}" == "X${UNIEP_PUB_DISK}" ];then
                log_echo "ERROR" "EXTEND_DISK and UNIEP_PUB_DISK must be diffrent,because they're all on the uniep node"
                check_result=1
            fi
            if [ "X${EXTEND_DISK}" == "X${DBNode_DISK}" ];then
                log_echo "ERROR" "EXTEND_DISK and DBNode_DISK must be diffrent,because they're all on the DB node"
                check_result=1
            fi
            if [ "X${EXTEND_DISK}" == "X${SDNode_DB_DISK}" ];then
                log_echo "ERROR" "EXTEND_DISK and SDNode_DB_DISK must be diffrent,because they're all on the same node"
                check_result=1
            fi
            if [ "X${EXTEND_DISK}" == "X${DBNode_BACKUP_TMP_DISK}" ];then
                log_echo "ERROR" "EXTEND_DISK and DBNode_BACKUP_TMP_DISK must be diffrent,because they're all on the same node"
                check_result=1
            fi
            if [ "X${EXTEND_DISK}" == "X${SRVBigData_DISK}" ];then
                log_echo "ERROR" "EXTEND_DISK and SRVBigData_DISK must be diffrent,because they're all on the same node"
                check_result=1
            fi
            if [ "X${EXTEND_DISK}" == "X${VSINDEX_DISK}" ];then
                log_echo "ERROR" "EXTEND_DISK and VSINDEX_DISK must be diffrent,because they're all on the same node"
                check_result=1
            fi
            if [ "X${EXTEND_DISK}" == "X${PM_KAFKA_DISK}" ];then
                log_echo "ERROR" "EXTEND_DISK and PM_KAFKA_DISK must be diffrent,because they're all on the same node"
                check_result=1
            fi
            if [ "X${EXTEND_DISK}" == "X${TRACE_DISK}" ];then
                log_echo "ERROR" "EXTEND_DISK and TRACE_DISK must be diffrent,because they're all on the same node"
                check_result=1
            fi
            if [ "X${EXTEND_DISK}" == "X${APPNode_DISK}" ];then
                log_echo "ERROR" "EXTEND_DISK and APPNode_DISK must be diffrent,because they're all on the same node"
                check_result=1
            fi
        fi

        if [ "X${DBNode_DISK}" != "X" ];then
            if [ "X${DBNode_DISK}" == "X${DBNode_EXTEND_DISK}" ];then
                log_echo "ERROR" "The parameter of DBNode_DISK and DBNode_EXTEND_DISK must be different."
                check_result=1
            fi

            if [ "X${DBNode_DISK}" == "X${DBNode_BACKUP_TMP_DISK}" ];then
                log_echo "ERROR" "The parameter of DBNode_DISK and DBNode_BACKUP_TMP_DISK must be different."
                check_result=1
            fi
        fi

        if [ "X${DBNode_EXTEND_DISK}" != "X" -a "X${DBNode_BACKUP_TMP_DISK}" != "X" ] && [ "X${DBNode_EXTEND_DISK}" == "X${DBNode_BACKUP_TMP_DISK}" ];then
            log_echo "ERROR" "The parameter of DBNode_BACKUP_TMP_DISK and DBNode_EXTEND_DISK must be different."
            check_result=1
        fi
    fi

    lsblk | grep /opt/oss/backuptmp
    if [ $? -eq 0 ];then
        log_echo "INFO" "Dir /opt/oss/backuptmp has been mounted."
    elif [ "X${DBNode_BACKUP_TMP_DISK}" == "X" -o "X${DBNode_BACKUP_TMP_DISK}" == "X{{DBNode_BACKUP_TMP_DISK}}"  ] && [ ! -f "/.dockerenv" ] && [ "X${check_backuptmp}" == "XYes" ] && [ "${AUTO_DISK_MOUNT}" == "TRUE" ];then
        log_echo "ERROR" "must config DBNode_BACKUP_TMP_DISK"
        check_result=1
    fi

    if [ "X${check_result}" != "X0" ];then
        return 1
    else
        return 0
    fi

}

function update_pm_data_db_size()
{
    local db_size="$1"
    local is_upgrade="$2"
    log_echo "INFO" "start update_pm_data_db_size.db_size=${db_size}"
    if [ -z "${db_size}" ];then
        log_echo "ERROR" "The db_size=${db_size} is null."
        exit 1
    fi
    isNum "${db_size}" || exit 1
    
    if [ ! -f ${dv_cfg_file}.tmp ];then
        log_echo "ERROR" "The ${dv_cfg_file}.tmp is not exists."
        exit 1
    fi
    
    if [ "X${is_upgrade}" == "XYes" ];then
        PM_DATA_DB_SIZE="${db_size}"
    else
        ## get default size.
        PM_DATA_DB_SIZE=$(cat ${dv_cfg_file}.tmp |grep "^PM_DATA_DB_DEFAULT_SIZE=.*"|awk -F'PM_DATA_DB_DEFAULT_SIZE=' '{print $2}')
        if [ -z "${PM_DATA_DB_SIZE}" ];then
            log_echo "ERROR" "The PM_DATA_DB_SIZE=${PM_DATA_DB_SIZE} is null."
            exit 1
        fi
        isNum "${PM_DATA_DB_SIZE}" || exit 1
        
        ## If /opt/zenith is less than or equal to 350 GB, the default value is 30 GB.
        ## 350 GB = 358400 M
        local tmp_zenith_size=358400
        log_echo "INFO" "The db_size=${db_size} and tmp_zenith_size=${tmp_zenith_size}"
        ## If the size of /opt/zenith is greater than 350 GB, the space size (GB) - 320 GB
        if [ ${db_size} -gt ${tmp_zenith_size} ];then
            ## units MB, db_size is MB, 320 GB = 327680 MB
            PM_DATA_DB_SIZE=$((${db_size} - 327680 ))
            isNum "${PM_DATA_DB_SIZE}" || exit 1
            if [ ${PM_DATA_DB_SIZE} -gt 2097152 ];then
                PM_DATA_DB_SIZE=2097152
            fi
        fi
    fi
    sed -i "s/^PM_DATA_DB_SIZE=.*/PM_DATA_DB_SIZE=${PM_DATA_DB_SIZE}/g" ${dv_cfg_file}
    sed -i "s/^PM_DATA_DB_SIZE=.*/PM_DATA_DB_SIZE=${PM_DATA_DB_SIZE}/g" ${dv_cfg_file}.tmp
    log_echo "INFO" "End update_pm_data_db_size PM_DATA_DB_SIZE=${PM_DATA_DB_SIZE}"
}

function set_zenith_size()
{
    local zenith_size_file=${PreSet_PATH}/zenith_size.txt
    if [ ! -f ${zenith_size_file} ];then
        log_echo "ERROR" "The ${zenith_size_file} is not exists."
        exit 1
    fi
    
    local db_size=$(cat ${zenith_size_file} |grep -v "echo zenith_size=" |grep "^zenith_size=" |awk -F'zenith_size=' '{print $2}'|sed "s#[[:blank:]|[:cntrl:]]##g")
    isNum "${db_size}" || exit 1
    update_pm_data_db_size "${db_size}"
}

function local_get_zenith_size()
{
    log_echo "INFO" "local_get_zenith_size start..."
    local db_json=/opt/zenith/ResourcePool.json
    if [ ! -f ${db_json} ];then
        log_echo "ERROR" "The ${db_json} is not exists."
        exit 1
    fi
    
    sudo -u dbuser cat ${db_json} |grep -w "dvpmdatadb" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "This has dvpmdatadb.not need do it."
        return 0
    fi
    
    local db_instance=$(sudo -u dbuser cat ${db_json}|grep -w "\"db.zenith.cloudsopdbsvr-.*ossuser\":"|awk -F'"|"' '{print $2}')
    if [ -z "${db_instance}" ];then
        log_echo "ERROR" "The db_instance=${db_instance} is null."
        exit 1
    fi
    
    sudo -u dbuser bash -c "chmod g+r ${db_json}"
    local db_size=$(source /opt/oss/manager/bin/engr_profile.sh;${PYTHONHOME}/bin/python ${PreSet_PATH}/tools/get_install_info.py ${db_json} get_db_data_size "${db_instance}")
    sudo -u dbuser bash -c "chmod g-r ${db_json}"
    if [ -z "${db_size}" ];then
        log_echo "ERROR" "The db_size=${db_size} is null."
        exit 1
    fi
    
    if [ "${db_size}" == "ERROR" ];then
        log_echo "ERROR" "The db_size=${db_size} is ERROR.please check it."
        exit 1
    fi
    isNum "${db_size}" || exit 1
    
    update_pm_data_db_size "${db_size}" "Yes"
    
    log_echo "INFO" "local_get_zenith_size End."
}

function ssh_get_zenith_size()
{
    log_echo "INFO" "ssh_get_zenith_size start..."
    local ssh_ip="$1"
    if [ -z "${ssh_ip}" ];then
        log_echo "ERROR" "The ssh_ip=${ssh_ip} is null."
        exit 1
    fi
    local scp_ip="$1"
    local isIpv6=$(echo ${scp_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        scp_ip="[${scp_ip}]"
    fi

    if [ "X$(whoami)" == "Xroot" ];then
        su - ossadm -c "ssh -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'rm -f /home/<USER>/get_db_data_size.sh'" > /dev/null 2>&1
        su - ossadm -c "ssh -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'rm -f /home/<USER>/get_install_info.py'" > /dev/null 2>&1

        su - ossadm -c "scp -o NumberOfPasswordPrompts=0 -o StrictHostKeyChecking=no -P ${i2k_ssh_port} ${PreSet_PATH}/tools/get_db_data_size.sh ossadm@${scp_ip}:/home/<USER>"
        su - ossadm -c "scp -o NumberOfPasswordPrompts=0 -o StrictHostKeyChecking=no -P ${i2k_ssh_port} ${PreSet_PATH}/tools/get_install_info.py ossadm@${scp_ip}:/home/<USER>"

        su - ossadm -c "ssh -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'chmod 750 /home/<USER>/get_db_data_size.sh'"
        su - ossadm -c "ssh -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'chmod 750 /home/<USER>/get_install_info.py'"

        su - ossadm -c "ssh -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'sh /home/<USER>/get_db_data_size.sh'" > ${PreSet_PATH}/ssh_get_zenith_size.txt
        if [ $? -ne 0 ];then
            log_echo "ERROR" "sh /home/<USER>/get_db_data_size.sh failed.at node:${ssh_ip}"
            exit 1
        fi
        su - ossadm -c "ssh -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'rm -f /home/<USER>/get_db_data_size.sh'"
        su - ossadm -c "ssh -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'rm -f /home/<USER>/get_install_info.py'"
        su - ossadm -c "ssh -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'rm -f /home/<USER>/getInfo.log'"
    else
        ssh -q -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'rm -f /home/<USER>/get_db_data_size.sh' > /dev/null 2>&1
        ssh -q -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'rm -f /home/<USER>/get_install_info.py' > /dev/null 2>&1

        scp -o NumberOfPasswordPrompts=0 -o StrictHostKeyChecking=no -P ${i2k_ssh_port} ${PreSet_PATH}/tools/get_db_data_size.sh "ossadm@${scp_ip}:/home/<USER>"
        scp -o NumberOfPasswordPrompts=0 -o StrictHostKeyChecking=no -P ${i2k_ssh_port} ${PreSet_PATH}/tools/get_install_info.py "ossadm@${scp_ip}:/home/<USER>"

        ssh -q -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'chmod 750 /home/<USER>/get_db_data_size.sh'
        ssh -q -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'chmod 750 /home/<USER>/get_install_info.py'

        ssh -q -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'sh /home/<USER>/get_db_data_size.sh' > ${PreSet_PATH}/ssh_get_zenith_size.txt
        if [ $? -ne 0 ];then
            log_echo "ERROR" "sh /home/<USER>/get_db_data_size.sh failed.at node:${ssh_ip}"
            exit 1
        fi
        ssh -q -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'rm -f /home/<USER>/get_db_data_size.sh'
        ssh -q -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'rm -f /home/<USER>/get_install_info.py'
        ssh -q -o StrictHostKeyChecking=no ossadm@${ssh_ip} -p ${i2k_ssh_port} 'rm -f /home/<USER>/getInfo.log'
    fi

    
    cat ${PreSet_PATH}/ssh_get_zenith_size.txt|grep "^get_db_data_size_error="
    if [ $? -eq 0 ];then
        log_echo "ERROR" "The ${PreSet_PATH}/ssh_get_zenith_size.txt has error.please check it."
        exit 1
    fi
    
    local db_size=$(cat ${PreSet_PATH}/ssh_get_zenith_size.txt|grep "^get_db_data_size="|awk -F'get_db_data_size=' '{print $2}'|sed "s#[[:blank:]|[:cntrl:]]##g")
    
    isNum "${db_size}" || exit 1
    
    update_pm_data_db_size "${db_size}" "Yes"
    
    log_echo "INFO" "ssh_get_zenith_size End."
}

function add_and_del_rcd()
{
    local do_type="$1"
    local iptables_cmd="$2"
    log_echo "INFO" "add_and_del_rcd ${do_type} iptables_cmd=${iptables_cmd} start..."
    if [ -z "${do_type}" -o -z "${iptables_cmd}" ];then
        log_echo "ERROR" "do_type=${do_type} or iptables_cmd=${iptables_cmd} has null."
        exit 1
    fi
    
    local rc_file=""
    if [ -f "/etc/rc.d/rc.local" ];then
        ## EulerOS X86 and ARM aarch64
        rc_file="/etc/rc.d/rc.local"
        log_echo "INFO" "${rc_file} is exists."
    elif [ -f "/etc/rc.d/after.local" ];then
        ##suse 
        rc_file="/etc/rc.d/after.local"
        log_echo "INFO" "${rc_file} is exists."
    else
        log_echo "INFO" "/etc/rc.d/rc.local and /etc/rc.d/after.local is not exists."
        return 0
    fi
    
    grep -w "let lvs pass" ${rc_file} > /dev/null 2>&1
    local rc_ret=$?
    if [ ${rc_ret} -eq 0 -a "X${do_type}" == "Xdel" ];then
        sed -i '/"let lvs pass"/d' ${rc_file} > /dev/null 2>&1
        log_echo "INFO" "rc del ret=$?."
    fi
    log_echo "INFO" "add_and_del_rcd ${do_type} End."
}

function add_and_del_iptables()
{
    local do_type="$1"
    local mark_str="$2"
    log_echo "INFO" "add_and_del_iptables ${do_type} mark_str=${mark_str} start..."
    if [ -z "${do_type}" -o -z "${mark_str}" ];then
        log_echo "ERROR" "do_type=${do_type} or mark_str=${mark_str} has null."
        exit 1
    fi
    
    #iptables -L
    iptables -nL  |grep -w "kubernetes firewall for dropping marked packets" >> ${LOG_FILE} 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "execute cmd: [ iptables -nL  |grep -w \"kubernetes firewall for dropping marked packets\" ],select not has it.skip it."
        return 0
    fi
    
    iptables -nL  |grep -w "let lvs pass" >> ${LOG_FILE} 2>&1
    local let_lvs_pass=$?
    
    if [ "X${do_type}" == "Xadd" -a ${let_lvs_pass} -eq 0 ];then
        log_echo "INFO" "execute cmd: [ iptables -nL  |grep -w \"let lvs pass\" ],the iptables already exists."
        return 0
    fi
    
    if [ "X${do_type}" == "Xdel" -a ${let_lvs_pass} -ne 0 ];then
        log_echo "INFO" "execute cmd: [ iptables -nL  |grep -w \"let lvs pass\" ],let lvs pass is not exists.skip it."
        return 0
    fi
    
    local iptables_cmd=""
    if [ "X${do_type}" == "Xadd" ];then
        #add iptables
        log_echo "INFO" "add iptables KUBE-FIREWALL lvs."
        iptables_cmd="iptables -w -I KUBE-FIREWALL -m comment --comment \"let lvs pass\" -m mark --mark ${mark_str} -j ACCEPT"
    elif [ "X${do_type}" == "Xdel" ];then
        #del iptables
        log_echo "INFO" "del iptables KUBE-FIREWALL lvs."
        iptables_cmd="iptables -w -D KUBE-FIREWALL -m comment --comment \"let lvs pass\" -m mark --mark ${mark_str} -j ACCEPT"
    else
        log_echo "ERROR" "kube_firewall_iptables do_type=${do_type} is error."
        exit 1
    fi
    
    eval ${iptables_cmd}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute cmd: [ ${iptables_cmd} ] failed."
        exit 1
    fi
    
    add_and_del_rcd "${do_type}" "${iptables_cmd}"
    
    log_echo "INFO" "add_and_del_iptables ${do_type} End."
}

function kube_firewall_iptables()
{
    local do_type="$1"
    log_echo "INFO" "kube_firewall_iptables ${do_type} start..."
    if [ "X${lvs_dip_ipv4}" == "X" -o "X${lvs_dip_ipv4}" == "X{{LVS_DIP_IPV4}}" ];then
        log_echo "INFO" "lvs_dip_ipv4=${lvs_dip_ipv4} is null."
        return 0
    fi
    
    local a=$(echo ${lvs_dip_ipv4} |awk -F'.' '{print $1}')
    local b=$(echo ${lvs_dip_ipv4} |awk -F'.' '{print $2}')
    local c=$(echo ${lvs_dip_ipv4} |awk -F'.' '{print $3}')
    local d=$(echo ${lvs_dip_ipv4} |awk -F'.' '{print $4}')
    local ret=$(echo "$(((a<<24)|(b<<16)+(c<<8)+d))")
    
    add_and_del_iptables "${do_type}" "${ret}/0xffffffff"
    log_echo "INFO" "kube_firewall_iptables ${do_type} End."
}

function upgrade_kube_firewall_iptables()
{
    log_echo "INFO" "upgrade_kube_firewall_iptables start..."
    iptables-save |grep "cloudsop_lvs_31360_" >> ${LOG_FILE} 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "execute cmd: [ iptables-save |grep cloudsop_lvs_31360_ ],select not has it.skip it."
        return 0
    fi
    
    ## -A PREROUTING -d xx.xx.xx.x/xx -p tcp -m tcp --dport 31360 -m comment --comment cloudsop_lvs_31360_169795392 -j MARK --set-xmark 0xa1edf40/0xffffffff
    local mark_str=$(iptables-save |grep "cloudsop_lvs_31360_" |head -1|awk '{print $NF}'|sed "s# ##g")
    add_and_del_iptables "add" "${mark_str}"
    
    log_echo "INFO" "upgrade_kube_firewall_iptables End."
}

function mkdir_jdbc()
{
    log_echo "INFO" "mkdir_jdbc start,The i2k_user_name=${i2k_user_name} or i2k_group_name=${i2k_group_name} or i2k_user_home=${i2k_user_home}"
    #i2k_user_name=ossuser
    #i2k_group_name=ossgroup
    #i2k_user_home=/home/<USER>
    if [ -z "${i2k_user_name}" -o -z "${i2k_group_name}" -o -z "${i2k_user_home}" ];then
        log_echo "ERROR" "i2k_user_name=${i2k_user_name} or i2k_group_name=${i2k_group_name} or i2k_user_home=${i2k_user_home} has null."
        exit 1
    fi
    if [ "X$(whoami)" == "Xroot" ];then
        mkdir -p ${i2k_user_home}/jdbc
        chown ${i2k_user_name}:${i2k_group_name} ${i2k_user_home}/jdbc
        chmod 700 ${i2k_user_home}/jdbc
    else
        ${command_prefix} mkdir -p ${i2k_user_home}/jdbc
        ${command_prefix} chmod 700 ${i2k_user_home}/jdbc
    fi

    log_echo "INFO" "mkdir_jdbc End.ret=$(ls -l ${i2k_user_home} |grep jdbc)"
}
function add_nodjs_path_for_tzdata()
{
    if [ ! -d /home/<USER>/tzdata ];then
        mkdir -p /home/<USER>/tzdata

        chown -R ossuser:ossgroup /home/<USER>

        chmod -R 700 /home/<USER>
        
        log_echo "INFO" "mkdir_nodejs End.ret=$(ls -l /home/<USER>"
    fi
    
}

function check_lvs_mask_nic()
{
    Check_execute "check_lvs_mask_nic"
    if [ $? -eq 0 ];then
        return 0
    fi
    local app_ssh_ip="$1"
    local app_ssh_user="$2"
    local app_ssh_pwd="$3"
    local node_type="$4"
    
    local isIpv6=$(echo ${app_ssh_ip} |grep ":" |grep -v "]")
    
    log_echo "INFO" "check LVS config parameter of ${app_ssh_ip}"
    if [ "X${app_ssh_pwd}" == "X" -a "X${root_password}" != "X" ];then
        log_echo "INFO" "PreInstall get root pwd"
        app_ssh_pwd="${root_password}"
    fi
    
    if [ -z "${app_ssh_ip}" -o -z "${app_ssh_user}" -o -z "${app_ssh_pwd}" -o -z "${node_type}" ];then
        log_echo "ERROR" "The app_ssh_ip=${app_ssh_ip} or app_ssh_user=${app_ssh_user} or node_type=${node_type} or app_ssh_pwd has null.please check."
        exit 1
    fi
    
    if [ "${node_type}" == "Primary" ];then
        ifconfig > ${PreSet_PATH}/${node_type}_ifconfig.txt
        ip addr > ${PreSet_PATH}/${node_type}_ip_addr.txt
    else
        auto_smart_ssh ${app_ssh_pwd} "${app_ssh_user}@${app_ssh_ip} ifconfig" > ${PreSet_PATH}/${node_type}_ifconfig.txt
        auto_smart_ssh ${app_ssh_pwd} "${app_ssh_user}@${app_ssh_ip} ip addr" > ${PreSet_PATH}/${node_type}_ip_addr.txt
    fi
    
    if [ ! -z "${isIpv6}" ];then
        check_lvs_mask_nic_config "${app_ssh_ip}" "${node_type}" "1"
    else
        check_lvs_mask_nic_config "${app_ssh_ip}" "${node_type}" "0"
    fi
    echo "check_lvs_mask_nic : success" >> ${action_tag} 
}

function check_lvs_mask_nic_config()
{
    local app_ssh_ip="$1"
    local node_type="$2"
    local ip_type="$3"
    log_echo "INFO" "check_lvs_mask_nic_config LVS config parameter of ${app_ssh_ip}"
    local need_to_check=0
    if [ "X${ip_type}" == "0" ];then
        [ ! -z "${LVS_VIP_IPV4}" -a ! -z "${LVS_DIP_IPV4}" ] || need_to_check=1
    elif [ "X${ip_type}" == "1" ];then
        [ ! -z "${LVS_VIP_IPV6}" -a ! -z "${LVS_DIP_IPV6}" ] || need_to_check=1
    elif [ "X${ip_type}" == "2" ];then
        [ ! -z "${LVS_VIP_IPV4}" -a ! -z "${LVS_DIP_IPV4}" ] || need_to_check=1
        [ ! -z "${LVS_VIP_IPV6}" -a ! -z "${LVS_DIP_IPV6}" ] || need_to_check=1
    fi
    [ -f ${PreSet_PATH}/${node_type}_ifconfig.txt -a -f ${PreSet_PATH}/${node_type}_ip_addr.txt ] || need_to_check=1
    if [ ${need_to_check} -eq 1 ];then
        log_echo "INFO" "check_lvs_mask_nic_config LVS config parameter of ${app_ssh_ip},not need to check it."
        return 0
    fi

    local check_ret=0
    local ip_mask=""
    local ip_nic=""
    ## ipv4
    if [ "X${ip_type}" == "X0" ];then
        ## suse
        ip_mask=$(cat ${PreSet_PATH}/${node_type}_ifconfig.txt |grep -wF ${app_ssh_ip} |grep "inet"|grep "Mask:" |sed "s/.*Mask:\(.*\)/\1/g" |sed "s#[[:blank:]|[:cntrl:]]##g")
        if [ -z "${ip_mask}" ];then
            ## x86 ARM
            ip_mask=$(cat ${PreSet_PATH}/${node_type}_ifconfig.txt |grep -wF ${app_ssh_ip} |grep "inet"|grep "netmask" |awk '{print $4}' |sed "s#[[:blank:]|[:cntrl:]]##g")
            if [ -z "${ip_mask}" ];then
                log_echo "ERROR" "The ip_mask=${ip_mask} is null,suse,x86,arm get ip mask failed, please check of ${app_ssh_ip}"
                check_ret=1
            fi
        fi
        
        if [ "X${LVS_IPV4_NETMASK}" != "X${ip_mask}" ];then
            log_echo "ERROR" "The ip_mask=${ip_mask} and LVS_IPV4_NETMASK=${LVS_IPV4_NETMASK} is not same, please check parameter LVS_IPV4_NETMASK."
            check_ret=1
        fi
        
        ip_nic=$(cat ${PreSet_PATH}/${node_type}_ip_addr.txt |grep -wF ${app_ssh_ip} |grep "inet" |awk '{print $NF}'|sed "s#[[:blank:]|[:cntrl:]]##g")
        ## check net nic
        if [ "X${LVS_IPV4_NIC}" != "X${ip_nic}" ];then
            log_echo "ERROR" "The ip_nic=${ip_nic} and LVS_IPV4_NIC=${LVS_IPV4_NIC} is not same, please check parameter LVS_IPV4_NIC."
            check_ret=1
        fi
        
    else
        ## ipv6 
        ## ipv6需要用python转义后，再检查 ${app_ssh_ip}
        local app_ssh_ip_simple=$(${PreSet_PATH}/UniEp/python/bin/python -c "import ipaddress;print(str(ipaddress.ip_address('$app_ssh_ip')))")
        log_echo "INFO" "check_lvs_mask_nic_config app_ssh_ip_simple=${app_ssh_ip_simple}"
        ip_mask=$(cat ${PreSet_PATH}/${node_type}_ip_addr.txt |grep -wF ${app_ssh_ip_simple} |grep -w "inet6" | awk -F ' ' '{print $2}' | sed "s/.*\/\(.*\)/\1/g" |sed "s#[[:blank:]|[:cntrl:]]##g")
        if [ -z "${ip_mask}" ];then
            log_echo "ERROR" "The ip_mask=${ip_mask} is null,suse,x86,arm get ip mask failed, please check of ${app_ssh_ip}"
            check_ret=1
        fi

        ## check net mask
        if [ "X${LVS_IPV6_NETMASK}" != "X${ip_mask}" ];then
            log_echo "ERROR" "The ip_mask=${ip_mask} and LVS_IPV6_NETMASK=${LVS_IPV6_NETMASK} is not same, please check parameter LVS_IPV6_NETMASK."
            check_ret=1
        fi
        
        local ip_line=$(grep  -nwF ${app_ssh_ip_simple} ${PreSet_PATH}/${node_type}_ip_addr.txt | grep "inet" |awk -F ':' '{print $1}')
        local all_nic_line=$(grep -ni "BROADCAST" ${PreSet_PATH}/${node_type}_ip_addr.txt | awk -F ':' '{print $1}' |sort -rn)
        local nic_line=""
        local ip_nic_line=""
        for nic_line in ${all_nic_line}
        do
            if [[ $ip_line -gt $nic_line ]];then
                ip_nic_line=$nic_line
                break;
            fi
        done
        
        ip_nic=$(grep -ni "BROADCAST" ${PreSet_PATH}/${node_type}_ip_addr.txt | grep "^${ip_nic_line}:" |awk -F  ':' '{print $3}'|sed 's/ //g'|head -1);
        ## check inet6 nic
        if [ "X${LVS_IPV6_NIC}" != "X${ip_nic}" ];then
            log_echo "ERROR" "The ip_nic=${ip_nic} and LVS_IPV4_NIC=${LVS_IPV6_NIC} is not same, please check parameter LVS_IPV6_NIC."
            check_ret=1
        fi
    fi
    [ ${check_ret} -eq 1 ] && exit 1
    log_echo "INFO" "check_lvs_mask_nic_config LVS config parameter of ${app_ssh_ip} End."
}


function delete_iptables_dnat() {
    local destination_ip=$1
    local dnat_ip=$2
 
    if [ "X${destination_ip}" == "X" -o "X${dnat_ip}" == "X" ];then
        log_echo "ERROR" "destination_ip or dnat_ip is null"
        exit 1
    fi
 
    local tmp_ret=$(iptables -t nat -L -n | grep -w 'DNAT' | grep -wF ${destination_ip} | grep -wF ${dnat_ip})
    if [ ! -z "${tmp_ret}" ];then
        local tmp_ret_port=$(echo $tmp_ret | awk -F ":" '{print $NF}')
        if [ -z "${tmp_ret_port}" ]; then
            tmp_ret_port=22
        fi
        log_echo "INFO" "execute cmd:[iptables -t nat -D OUTPUT -p tcp -d ${destination_ip} --dport ${tmp_ret_port} -j DNAT --to-destination ${dnat_ip}:${tmp_ret_port}]."
        iptables --wait -t nat -D OUTPUT -p tcp -d ${destination_ip} --dport "${tmp_ret_port}" -j DNAT --to-destination ${dnat_ip}:${tmp_ret_port}
    else
        log_echo "INFO" "do not exist relative DNAT need to delete, skip"
        return 0
    fi
}

function check_iptype()
{
    if [ -z "${IPType}" -o -z "$(echo ${IPType} | grep -wE "0|1|2")" ];then
        read -p "please input the iptype for dv(0. ipv4, 1. ipv6, 2. ipv4 and ipv6):"  dviptype
        if [ $(echo ${dviptype} | wc -L) -eq 1 ] && [ ! -z $(echo ${dviptype} | grep -E "0|1|2") ];then
            sed -i "s/^IPType=.*/IPType=${dviptype}/g" ${source_file}
        else
            log_echo "ERROR" "The dvIptype=${dviptype} is incorrect. need change (0,1,2) or source_file=${source_file} config IPType set (0,1,2). please check and select IPType."
            exit 1
        fi
        . ${source_file}
    fi
}

function set_loop_monitor()
{
    if [ -f "/.dockerenv"  ];then
        return 0
    fi
    script_path=$1
    service_path=$2
    timer_path=$3

    mkdir -p $ROOT_CUSTOM_PATH/dv_loop_monitor
    cp -f ${script_path} $ROOT_CUSTOM_PATH/dv_loop_monitor/
    local cfg_path=$(dirname ${script_path})
    cp -f ${cfg_path}/dv_loop_monitor.cfg $ROOT_CUSTOM_PATH/dv_loop_monitor/
    
    if [ "X${service_path}" != "X" ] &&  [ -f ${service_path} ];then
            cp  -f ${service_path}  /etc/systemd/system/
            chmod 644 /etc/systemd/system/dv_loop_monitor.service
    else
        log_echo "ERROR" "${UTILS_PATH}/dv_loop_monitor.service does not exists"
        exit 1
    fi

    if [ "X${timer_path}" != "X" ] &&  [ -f ${timer_path} ];then
        cp  -f  ${timer_path}  /etc/systemd/system/
        chmod 644 /etc/systemd/system/dv_loop_monitor.timer
    else
        log_echo "ERROR" "${UTILS_PATH}/dv_loop_monitor.timer does not exists"
        exit 1
    fi
    chmod 500  $ROOT_CUSTOM_PATH/dv_loop_monitor/dv_loop_monitor.sh
    chmod 600  $ROOT_CUSTOM_PATH/dv_loop_monitor/dv_loop_monitor.cfg
    chmod 700  $ROOT_CUSTOM_PATH/dv_loop_monitor
    chown root:root  $ROOT_CUSTOM_PATH/dv_loop_monitor/dv_loop_monitor.sh
    chown root:root  $ROOT_CUSTOM_PATH/dv_loop_monitor/dv_loop_monitor.cfg
    chown root:root  /etc/systemd/system/dv_loop_monitor.service
    chown root:root  /etc/systemd/system/dv_loop_monitor.timer
    systemctl daemon-reload
    systemctl enable dv_loop_monitor.service
    systemctl start dv_loop_monitor.timer
    if [ $? -ne 0 ];then
        log_echo "ERROR" "dv_loop_monitor.timer start failed use 'systemctl start dv_loop_monitor.timer'"
        exit 1
    fi
    systemctl enable dv_loop_monitor.timer
    systemctl start dv_loop_monitor.service
}

function delete_loop_monitor()
{
    if [ -f "/.dockerenv"  ];then
        return 0
    fi
    rm -rf ${ROOT_CUSTOM_PATH}/dv_loop_monitor
    if [ -f "/etc/rc.d/after.local" ];then
        sed -i "/dv_loop_monitor/d" /etc/rc.d/after.local >/dev/null
        
    fi
    if [ -f "/etc/rc.d/rc.local" ];then
        sed -i "/dv_loop_monitor/d" /etc/rc.d/rc.local >/dev/null
    fi
    systemctl disable dv_loop_monitor.timer >/dev/null 2>&1
    systemctl disable dv_loop_monitor.service >/dev/null 2>&1
    rm -f  /etc/systemd/system/dv_loop_monitor.service >/dev/null 2>&1
    rm -f  /etc/systemd/system/dv_loop_monitor.timer >/dev/null 2>&1
    systemctl daemon-reload
} 

function set_loop_monitor_for_upgrade()
{
    if [ -f "/.dockerenv"  ];then
        return 0
    fi
    if [ ! -f /root/dv_loop_monitor/dv_loop_monitor.sh ];then
        mkdir -p /root/dv_loop_monitor
        touch /root/dv_loop_monitor/upgrade_${product_new_version}.tag
    fi
    set_loop_monitor "$@"
}

function delete_loop_monitor_for_upgrade()
{
    if [ -f /root/dv_loop_monitor/dv_loop_monitor.sh ] && [ -f /root/dv_loop_monitor/upgrade_${product_new_version}.tag ];then
        delete_loop_monitor 
    fi
}

function backup_controller_server()
{
    if [ ! -d /opt/oss/SOP/apps/DVControllerService ];then
        return 0
    fi
    chmod 775 /tmp/DVPreSet_upgrade_ossadm
    sudo -u ossuser test -f /home/<USER>/etc/ssl/dv/random_pwd_success
    if [ $? -ne 0 ] && [ "X${need_radompwd}" == "XYes" ];then
        log_echo "INFO" "Begin backup the controller_server.p12 certificate."
        sudo -u ossuser mkdir -p /tmp/DVPreSet_upgrade_ossadm/backup_controller_server
        sudo -u ossuser cp /home/<USER>/etc/ssl/dv/controller_server.p12  /tmp/DVPreSet_upgrade_ossadm/backup_controller_server
        sudo -u ossuser cp /home/<USER>/etc/ssl/dv/manifest.json  /tmp/DVPreSet_upgrade_ossadm/backup_controller_server/
        sudo -u ossuser cp  /tmp/DVPreSet_upgrade_ossadm/add_cert.py    /tmp/DVPreSet_upgrade_ossadm/backup_controller_server/
        sudo -u ossuser touch /tmp/DVPreSet_upgrade_ossadm/backup_controller_server/backup_controller_server_${product_new_version}.tag
        log_echo "INFO" "Backup the controller_server.p12 certificate finish."
    else
        if [ -d /tmp/DVPreSet_upgrade_ossadm/backup_controller_server ] && [ ! -f /tmp/DVPreSet_upgrade_ossadm/backup_controller_server/backup_controller_server_${product_new_version}.tag ];then
            sudo -u ossuser rm -rf /tmp/DVPreSet_upgrade_ossadm/backup_controller_server
        fi
    fi
    chmod 755 /tmp/DVPreSet_upgrade_ossadm
}

function check_must_config_disk()
{
    if [ "X${AUTO_DISK_MOUNT}" != "XTRUE" ]; then
        log_echo "INFO" "The AUTO_DISK_MOUNT=${AUTO_DISK_MOUNT}, not need to check must config disk."
        return 0
    fi

    local null_list=""
    if [ "X${HA_DISK}" == "X" ];then
        log_echo "ERROR" "This is cluster network, we need HA_DISK."
        null_list="${null_list} HA_DISK"
    fi
    if [ "X${HOFS_DISK}" == "X" -o -n "`echo ${HOFS_DISK}|grep '{{'`" ];then
        log_echo "ERROR" "This is cluster network, we need HOFS_DISK."
        null_list="${null_list} HOFS_DISK"
    fi

    if [ -n "${null_list}" ]; then
        log_echo "ERROR" "Following parameter is null ,please check : ${null_list}"
        exit 1
    fi

    ## 配置为磁盘（预安装也是配置磁盘，需要排除预安装场景），校验不能相同
    echo "${HA_DISK}" | grep "^/dev"
    if [ $? -eq 0 ] && [ "X${pre_install_key}" != "XPreInstall" ]; then
        if [ "X${HA_DISK}" == "X${HOFS_DISK}" ];then
            log_echo "ERROR" "HA_DISK and HOFS_DISK must be diffrent,because they're all on the app node"
            exit 1
        fi
    fi
    return 0
}

function check_digital_param_of_disk()
{
    local todo_type="$1"
    all_disk_param_key="EXTEND_DISK"
    local disk_name_val=""
    while read -r line; do
        disk_name_val=$(echo "${line}" | awk -F "=" '{print $2}')
        if [ -z "${disk_name_val}" ]; then
            continue
        fi
        if [ "X${disk_name_val}" == "XAPPNode_DISK" ]; then
            if [ "X${netWorkType}" == "XC" -o "X${netWorkType}" == "XL" -o "X${netWorkType}" == "XM" ]; then
                disk_name_val="HA_DISK"
            fi
        fi
        
        if [ "X${disk_name_val}" == "XSRVBigData_DISK" -a "X${todo_type}" != "XExtend" ]; then
            if [ "X${netWorkType}" == "XM" ]; then
                disk_name_val="DVSecondary_SRVBigData_DISK DVThird_SRVBigData_DISK OMSAExtendClusterNode_SRVBigData_DISK ExtendClusterNode_SRVBigData_DISK"
            fi
        fi
        all_disk_param_key="${all_disk_param_key} ${disk_name_val}"
    done < ${disk_map_file}

    check_is_all_digtal_val "${all_disk_param_key}" "${todo_type}" "${2}"
}

function add_digital_disk_config()
{
    Digitalization_Disk_Param_Tag="YES"
    update_config ${configParametersFile}  "Digitalization_Disk_Param_Tag" "${Digitalization_Disk_Param_Tag}"
    . ${configParametersFile} || exit 1

    if [ "X${EXTEND_DISK}" != "X" -a "X${EXTEND_DISK}" != "X{{EXTEND_DISK}}" ]; then
        local extend_disk_without_space=$(echo "${EXTEND_DISK}" | sed "s#[[:space:]]*\$##")
        update_config ${configParametersFile} "extend_disk_need_size" "${extend_disk_without_space}" || exit 1
    fi
    concatenate_digital_disk_param_configstring "${all_disk_param_key}" "${disk_map_file}" "$1" "$2"
}

function check_is_all_digtal_val()
{
    local key_list="$1"
    local todo_type="$2"
    local key_val=""
    local val=""
    local number_count=0
    local symbol_count=0
    local tmp_cfg_file="${source_file}"
    if [ "X${todo_type}" == "XExtend" ];then
        tmp_cfg_file="$3"
    fi
    
    local tmp_num_key_list=""
    local tmp_symbol_key_list=""
    for key_val in ${key_list}; do
        val=$(grep -w "^${key_val}=.*" ${tmp_cfg_file} | awk -F "=" '{print $2}' | sed "s#[[:space:]]*\$##")
        if [ -z "${val}" ]; then
            continue
        fi
        local is_number=$(echo "${val}" | grep -w "^[1-9][0-9]*$")
        if [ ! -z "$is_number" ]; then
            tmp_num_key_list="${tmp_num_key_list} ${key_val}=${val}"
            ((number_count++))
            continue
        fi
        
        tmp_symbol_key_list="${tmp_symbol_key_list} ${key_val}=${val}"
        ((symbol_count++))
    done

    if [ $number_count -ne 0 ] && [ $symbol_count -ne 0 ]; then
        log_echo "ERROR" "The disk parameters must be all traditional disk symbols or all effective numbers.please check cfg file:${tmp_cfg_file} of key list: ${tmp_num_key_list} but ${tmp_symbol_key_list}"
        exit 1
    fi
    
    if [ $number_count -eq 0 ]; then
        log_echo "INFO" "The disk parameters are not numerical, execute before mount disk logic."
        return 1
    fi

    log_echo "INFO" "The disk parameters are all numerical, begin to concatenate Digital_Disk_Param_ConfigString..."
    return 0
}

function concatenate_digital_disk_param_configstring()
{
    local key_list="$1"
    local map_file="$2"
    local todo_type="$3"
    
    local index_val=""
    local key=""
    local val=""
    
    local tmp_cfg_file="${source_file}"
    if [ "X${todo_type}" == "XExtend" ];then
        tmp_cfg_file="$4"
    fi
    ## digital_disk_param_configstring is not contain EXTEND_DISK, it mounted by another way.
    local digital_disk_param_configstring=""
    for index_val in ${key_list}; do
        val=$(grep -w "^${index_val}=.*" ${tmp_cfg_file} | awk -F "=" '{print $2}' | sed "s#[[:space:]]*\$##")
        if [ -z "${val}" ]; then
            continue
        fi
        
        if [ "X${index_val}" == "XEXTEND_DISK" ]; then
            log_echo "INFO" "EXTEND_DISK do not need to add to the digital disk param configstring."
            continue
        fi
        
        if [ "X${index_val}" == "XHA_DISK" ]; then
            index_val="APPNode_DISK"
        elif [ "X${index_val}" == "XDVSecondary_SRVBigData_DISK" -o "X${index_val}" == "XDVThird_SRVBigData_DISK" -o "X${index_val}" == "XOMSAExtendClusterNode_SRVBigData_DISK" -o "X${index_val}" == "XExtendClusterNode_SRVBigData_DISK" ]; then
            index_val="SRVBigData_DISK"
        fi

        key=$(grep ".*=${index_val}=.*" ${map_file} | awk -F "=" '{print $3}')
        if [ -z "${key}" ]; then
            continue
        fi
        if [ -z "${digital_disk_param_configstring}" ]; then
            digital_disk_param_configstring="${key}:${val}"
        else
            digital_disk_param_configstring="${digital_disk_param_configstring},${key}:${val}"
        fi
    done
    if [ -z "${digital_disk_param_configstring}" ]; then
        log_echo "WARN" "The digital_disk_param_configstring is null."
        return 1
    fi
    log_echo "INFO" "The digital_disk_param_configstring=${digital_disk_param_configstring}, add to the ${configParametersFile}."
    update_config ${configParametersFile}  "digital_disk_param_configstring" "\"${digital_disk_param_configstring}\"" || exit 1
    return 0
}

function add_val2tmpfile_for_digital_disk_param() {
    . ${configParametersFile}
    local todo_type="$1"
    local tmp_cfg_file=${dv_cfg_file}.tmp
    if [ "X${todo_type}" == "XExtend" ];then
        tmp_cfg_file=${tmp_dir}/DV_config.properties.tmp
    fi
    local extend_disk_without_space=$(echo "${EXTEND_DISK}" | sed "s#[[:space:]]*\$##")
    update_config ${tmp_cfg_file} "extend_disk_need_size" "${extend_disk_without_space}" || exit 1
    update_config ${tmp_cfg_file}  "Digitalization_Disk_Param_Tag" "${Digitalization_Disk_Param_Tag}" || exit 1
    local exe_ret=$(echo ${digital_disk_param_configstring} | grep "^\"")
    if [ -z "${exe_ret}" ]; then
        update_config ${tmp_cfg_file} "digital_disk_param_configstring" "\"${digital_disk_param_configstring}\"" || exit 1
    else
        update_config ${tmp_cfg_file} "digital_disk_param_configstring" "${digital_disk_param_configstring}" || exit 1
    fi
}

function check_and_config_digital_disk()
{
    log_echo "INFO" "Begin to check is digital disk of config..."
    check_digital_param_of_disk "$1" "$2"
    if [ $? -eq 0 ]; then
        log_echo "INFO" "This is digital disk parameter, next we will config values."
        add_digital_disk_config "$1" "$2"
        add_val2tmpfile_for_digital_disk_param "$1"
    fi
}

function check_modify_userpw_json()
{
    log_echo "INFO" "check_modify_userpw_json start..."
    local python_file="${UTILS_PATH}/add_cert.py"
    if [ ! -f ${python_file} ];then
        log_echo "ERROR" "The python_file=${python_file} is not exists."
        exit 1
    fi
    
    local json_file="/opt/oss/manager/etc/sysconf/userpw.json"
    if [ ! -f ${json_file} ];then
        log_echo "INFO" "The json_file=${json_file} is not exists."
        return 0
    fi
    
    local execute_user=$(whoami)
    log_echo "INFO" "The execute_user=${execute_user}"
    if [ "${execute_user}" != "ossadm" ];then
        log_echo "ERROR" "The execute_user=${execute_user} not equal ossadm."
        exit 1
    fi
    
    source /opt/oss/manager/bin/engr_profile.sh
    local get_tmp_pwd=$(python ${python_file} "get_userpw_protect_pvalue" "${json_file}")
    if [ -z "${get_tmp_pwd}" -o "${get_tmp_pwd}" == "ERROR" ];then
        log_echo "INFO" "The get_tmp_pwd=${get_tmp_pwd} not has protect_pvalue."
        return 0
    fi
    
    local get_tmp_pwd1=$(echo "${get_tmp_pwd}" |python -c "import sys;from util import ossext;print(ossext.Cipher.decrypt(sys.stdin.read().strip()))")
    local get_tmp_pwd2=$(echo "${get_tmp_pwd1}" |python -c "import sys;from util import ossext;print(ossext.Cipher.decrypt(sys.stdin.read().strip()))")
    
    if [ -z "${get_tmp_pwd2}" ];then
        log_echo "INFO" "The get_tmp_pwd2=${get_tmp_pwd2} is null,not need to modify."
        return 0
    fi

    echo "pwd:${get_tmp_pwd1}" | python ${python_file} "modify_userpw" "${json_file}" 

    log_echo "INFO" "check_modify_userpw_json finished."
}

function check_empty_param_of_disk()
{
    local tmp_disk_name="$1"
    local tmp_disk_val="$2"
    log_echo "INFO" "Begin to check empty disk parameter..."
    if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ]; then
        log_echo "INFO" "The scenarios is PreInstall or Digitalization_Disk_Param."
        local key=$(cat $disk_map_file | grep "=${tmp_disk_name}=" | awk -F "=" '{print $3}')
        local config_str="${Configuration_String}"
        if [ "X${Digitalization_Disk_Param_Tag}" == "XYES" ]; then
             config_str=${digital_disk_param_configstring}
        fi
        local grep_result=$(echo "${config_str}" | grep -ow "${key}:\([0-9]\+\)")
        if [ -z "${grep_result}" ]; then
            log_echo "INFO" "The ${tmp_disk_name} is not config at config file."
            return 0
        fi
        return 1
    fi
    log_echo "INFO" "The scenarios is normal install..."
    if [ "X${tmp_disk_val}" == "X" -o "X${tmp_disk_val}" == "X{{${tmp_disk_name}}}" ]; then
        return 0
    fi
    log_echo "INFO" "The disk has config, not a empty value, return 1."
    return 1
}

function set_node_key_map() {
    local Cluster_Node_List=$1;
    local Cluster_Node_pwd_List=($2);
    local Cluster_Node_Type=$3;
    local index=0;

    if [ "X${Cluster_Node_List}" != "X" ] && [[ ! "${Cluster_Node_List}" =~ "{{" ]];then
        for node_ssh_ip in $(echo "${Cluster_Node_List}" | sed "s#,# #g")
        do
            NODE_KEY_MAP["${Cluster_Node_Type}_${index}@${node_ssh_ip}"]="${Cluster_Node_pwd_List[${index}]}"
            ((index++))
        done
    fi
}

function modify_to_euleros11()
{
    if [ -f /etc/euleros-release ];then
        euler_version=$(grep "VERSION=" /etc/os-release|head -1 | awk -F'=' '{print $2}'|sed "s/\"//g")
        os_architecture=$(lscpu |grep -w  "Architecture"|awk -F':' '{print $2}' |sed "s/ //g")
        if [ "X${os_architecture}" == "Xaarch64" -a "X${euler_version}" == "X2.0 (SP12)" ];then
            if [ ! -e /lib64/ld-linux-aarch64.so.1 ];then
                ln -s /lib/ld-linux-aarch64.so.1 /lib64/ld-linux-aarch64.so.1
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Failed to create the /lib64/ld-linux-aarch64.so.1 soft link,please check."
                    exit 1
                fi
            fi
        fi
    fi
    
    grep -E "BigCloud Enterprise Linux|bclinux" /etc/os-release
    if [ $? -eq 0 ];then
        if [ ! -f /etc/resolv.conf ];then
            log_echo "INFO" "The /etc/resolv.conf is not exists.create it."
            touch /etc/resolv.conf
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Failed to create the /etc/resolv.conf"
                exit 1
            fi
            chmod 644 /etc/resolv.conf
            chown root:root /etc/resolv.conf
        else
            log_echo "INFO" "The /etc/resolv.conf is exists."
        fi
        
        ##need to Check cmd list: expect haveged dos2unix  lsof  killall
        local check_cmd_list="expect haveged dos2unix lsof killall"
        for tmp_cmd in ${check_cmd_list};
        do
            which ${tmp_cmd} >> $LOG_FILE 2>&1
            if [ $? -ne 0 ];then
                log_echo "ERROR" "The ${tmp_cmd} : command not found,please check."
                exit 1
            fi
        done
        log_echo "INFO" "bclinux Check end."
    fi
}

function is_empty_param() {
    local param=$1

    if [ "X${param}" == "X" ] || [[ "${param}" =~ "{{" ]];then
        return 0
    else
        return 1
    fi
}

function get_log_dir()
{
    local extend_type="$1"
    log_echo "INFO" "Begin to find logs directory of extend_type=${extend_type}..."
    local_log_dir=${OPT_PATH}/oss/log
    if [ -d ${local_log_dir} ]; then
        log_echo "INFO" "The local_log_dir=${local_log_dir}..."
        return 0
    fi
    if [ "X${extend_type}" == "Xextend" -a "X${is_use_varlog}" == "XYes" ]; then
        local_log_dir=/var/log
        log_echo "INFO" "The install action is extend and is_use_varlog is Yes, the logs dir_name=${local_log_dir}..."
        return 0
    fi
    return 1
}

function check_available_extend()
{
    local tmp_dir_name="$1"
    log_echo "INFO" "Begin to check tmp_dir_name=${tmp_dir_name} is available to extend..."
    get_vg_lv_name_by_dir "${tmp_dir_name}"
    if [ $? -ne 0 ]; then
        log_echo "ERROR" "The directory name=${tmp_dir_name} is not available to extend, please check."
        return 1
    fi
}

function get_vg_lv_name_by_dir()
{
    local dir_name="$1"
    if [ ! -d "${dir_name}" ]; then
        log_echo "WARN" "The input parameter is not a directory, please check..."
        return 1
    fi

    local is_lvm=$(lsblk -Pb | grep -wE "TYPE=\".*\"[ ]*(MOUNTPOINT|MOUNTPOINTS)=\"${dir_name}\"$" | awk '{print $6}' | uniq | sed 's#TYPE="\(.*\)"#\1#g')
    if [ "${is_lvm}" != "lvm" ]; then
        log_echo "WARN" "The dir_name=${dir_name} is mounted at TYPE=${is_lvm}."
        return 1
    fi
    log_dir_lv_name=$(df -Ph ${dir_name} | grep ${dir_name}| awk '{print $1}' | xargs -i lvdisplay {} | grep -w "^[ ]*LV Name" | awk '{print $3}')
    log_dir_vg_name=$(df -Ph ${dir_name} | grep ${dir_name}| awk '{print $1}' | xargs -i lvdisplay {} | grep -w "^[ ]*VG Name" | awk '{print $3}')
    if [ -z "${log_dir_vg_name}" -o -z "${log_dir_lv_name}" ]; then
        log_echo "WARN" "Use command df -h get vg and lv name failed..."
        return 1
    fi

    RETURN_VG_NAME="${log_dir_vg_name}"
    RETURN_LV_NAME="${log_dir_lv_name}"

    log_echo "INFO" "The ${dir_name} is mounted on a lvm, and vgname=${log_dir_vg_name} lvname=${log_dir_lv_name}."
}

function check_disk_available_size()
{
    local tmp_disk="$1"
    local expect_size="$2"
    log_disk_size=$(fdisk -l ${tmp_disk} | grep -w "^Disk[ ]*${tmp_disk}:"| awk -F ',' '{print $2}' | awk '{print $1}')
    if [ -z "${log_disk_size}" ]; then
        log_echo "ERROR" "Use fdisk -l ${tmp_disk} to get disk size failed, please check..."
        return 1
    fi
    echo "$log_disk_size" | grep -E "^[0-9]+$" >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        log_echo "ERROR" "Use fdisk -l ${tmp_disk} to get disk size is not a number, please check..."
        return 1
    fi

    ## compare unit: byte
    expect_size=$(($expect_size*1024*1024*1024))
    ## reduce 50M
    expect_size=$(($expect_size-50*1024*1024))
    if [ $log_disk_size -lt $expect_size ]; then
        log_echo "ERROR" "The ${tmp_disk} size=$log_disk_size is little then $expect_size, please check..."
        return 1
    fi
    log_echo "INFO" "Check disk available size=${log_disk_size} bytes"
}

function write_config_to_ATAE_and_extend()
{
    local disk_name="$1"
    local vg_name="$2"
    local lv_name="$3"
    local mount_point="$4"
    local extend_size="$5"
    log_echo "INFO" "Input parameters disk_name=${disk_name} vg_name=${vg_name} lv_name=${lv_name} mount_point=${mount_point} extend_size=${extend_size}..."
    extend_cfg=${CURRENT_PATH}/extend.cfg
    ## 1MB = 1048576 byte
    extdisk_size=$(( ${extend_size} / 1048576 ))
    ## Disk needs to subtract 50M
    extdisk_size=$(( ${extdisk_size} - 50 ))
    echo "${disk_name} ${vg_name}" > ${extend_cfg}
    echo "${vg_name} ${lv_name} ${mount_point} 0 ${extdisk_size}" >> ${extend_cfg}

    local extended_disk_record="/var/adm/autoinstall/logs/extended_disk.record"
    if [ -f "${extended_disk_record}" ]; then
        grep -q -E "${vg_name}[[:space:]]+${lv_name}[[:space:]]+${mount_point}[[:space:]]+${extdisk_size}" "${extended_disk_record}"
        if [ $? -eq 0 ]; then
            log_echo "INFO" "The ${vg_name} ${lv_name} ${mount_point} ${extdisk_size} is in record file=${extended_disk_record}, first extend we will delete the same record."
            local tmp_mount_point=$(echo "${mount_point}" | sed "s#\/#\\\/#g")
            sed -i "/${vg_name}[ ]\+${lv_name}[ ]\+${tmp_mount_point}[ ]\+${extdisk_size}/d" ${extended_disk_record}
            if [ $? -ne 0 ]; then
                log_echo "ERROR" "Delete the same record from ${extended_disk_record} failed， please check."
                exit 1
            fi
        fi
    fi

    local extend_script_path="/var/adm/autoinstall/scripts"
    if [ -f "${extend_cfg}" ];then
        cp ${extend_cfg} ${extend_script_path}
        sh ${extend_script_path}/extend_disk.sh >/dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Execute ${extend_script_path}/extend_disk.sh failed, please check..."
            exit 1
        fi
    fi

    local fstabFile="/etc/fstab"
    local filetype="ext4"
    log_echo "INFO" "Set log directory dev auto mount in ${fstabFile}..."
    echo "" >> /etc/fstab
    mount_point_tmp=$(echo ${mount_point} |sed "s#\/#\\\/#g")
    if [ -z "`grep -w \"/dev/mapper/${vg_name}-${lv_name}\" ${fstabFile} | grep -v \"/dev/mapper/${vg_name}-${lv_name}/\" | grep -w \"${mount_point}\"`" ];then
        [ "X${mount_point_tmp}" != "X" ] && sed -i "/[ |\t]\+${mount_point_tmp}[ |\t]\+/d" ${fstabFile}
        echo "/dev/mapper/${vg_name}-${lv_name}   ${mount_point}   ${filetype}    nofail,defaults     0   0  #DV_Preset" >> ${fstabFile}
    fi
    ## remove dev uuid
    grep "^UUID=.*${mount_point_tmp}" ${fstabFile} >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        sed -i "/^UUID=.*${mount_point_tmp}/d" ${fstabFile}
    fi

    ## remove mount /opt/oss/log by ATAE scripts.
    grep "^/dev/${vg_name}/${lv_name} ${mount_point}" ${fstabFile} >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        sed -i "/^\/dev\/${vg_name}\/${lv_name} ${mount_point_tmp}/d" ${fstabFile}
    fi
}

function auto_extend_log_disk()
{
    local scene_type="$1"
    log_echo "INFO" "Begin to execute auto_extend_log_disk, the scene type=${scene_type} ..."

    get_log_dir "${scene_type}"
    if [ -z "${local_log_dir}" ]; then
        log_echo "ERROR" "The log dir ${OPT_PATH}/oss/log or /var/log is not exists, please check..."
        exit 1
    fi
    check_available_extend "${local_log_dir}"
    if [ $? -ne 0 ]; then
        log_echo "ERROR" "The local_log_dir=${local_log_dir} isn't mounted on logical volume, not available to extend."
        exit 1
    fi
    pv_remove "${LOG_EXTEND_DISK}"

    check_disk_available_size "${LOG_EXTEND_DISK}" "$LOG_EXTEND_DISK_least_size"
    if [ $? -ne 0 ]; then
        log_echo "ERROR" "The LOG_EXTEND_DISK=${LOG_EXTEND_DISK} available size is not enough or error."
        exit 1
    fi

    ## add system disk vg remaining capacity to log directory, unit G
    local sys_vg_remain_cap=$(vgdisplay ${log_dir_vg_name} | grep "^[ ]*Free[ ]*PE" | awk -F "/" '{print $NF}' | grep -w "G.*" | grep -ow "[0-9]*\.*[0-9]*" | awk -F "." '{print $1}')
    if [  -n "${sys_vg_remain_cap}" ]; then
        log_disk_size=$(( $log_disk_size + $sys_vg_remain_cap*1024*1024*1024 ))
    fi
    write_config_to_ATAE_and_extend "${LOG_EXTEND_DISK}" "${log_dir_vg_name}" "${log_dir_lv_name}" "${local_log_dir}" "$log_disk_size"

    log_echo "INFO" "Extend log directory=${local_log_dir} size success."
}

function is_num()
{
    local input_num="$1"
    if [ -z "${input_num}" ];then
        log_echo "WARN" "The input_num=${input_num} is null."
        return 1
    fi

    local is_number=$(echo "${input_num}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${is_number}" == "No" ];then
        log_echo "WARN" "The input_num=${input_num} is not number."
        return 1
    fi
    return 0
}

function check_log_disk_has_extend()
{
    local scene_type="$1"
    log_echo "INFO" "Begin to check log disk has been extended..."
    get_log_dir "${scene_type}"
    local dir_mount_point_count=$(lsblk -Pb | grep -E "TYPE=\"lvm\"[ ]*MOUNTPOINT[S]*=\"${local_log_dir}\"" | wc -l)
    if [ "X${local_log_dir}" == "X${OPT_PATH}/oss/log" ]; then
         if [ $dir_mount_point_count -ge 2 ]; then
             log_echo "INFO" "The directory ${OPT_PATH}/oss/log has been extended, return 0"
             return 0
         fi
         log_echo "INFO" "The directory ${OPT_PATH}/oss/log has not been extended, return 1"
    fi

    if [ "X${local_log_dir}" == "X/var/log" ]; then
        if [ $dir_mount_point_count -ge 3 ]; then
           log_echo "INFO" "The directory /var/log has been extended, return 0"
           return 0
        fi
        log_echo "INFO" "The directory /var/log has not been extended, return 1"
    fi
    return 1
}


function wait_sshd_sftpd()
{
    local wait_key="$1"
    log_echo "INFO" "wait_sshd_sftpd ${wait_key} start..."
    touch /os_config/need_docker_monitor.tag
    chmod 600 /os_config/need_docker_monitor.tag
    local input_times=0
    while [ ${input_times} -lt 5 ];
    do
        tmp_pid=$(ps -ef | grep "/usr/sbin/${wait_key} -D -f /etc/ssh/${wait_key}_config" | grep -v grep |awk '{print $2}')
        if [ ! -z "${tmp_pid}" ];then
            log_echo "INFO" "The ${wait_key} is start."
            [ -f /os_config/need_docker_monitor.tag ] && rm -f /os_config/need_docker_monitor.tag
            return 0
        else
            log_echo "INFO" "The ${wait_key} not start,try again ${input_times}."
            sleep 3
        fi
        input_times=$((${input_times}+1))
    done
    
    /usr/sbin/${wait_key} -D -f /etc/ssh/${wait_key}_config -E /var/log/${wait_key}.log &
    [ -f /os_config/need_docker_monitor.tag ] && rm -f /os_config/need_docker_monitor.tag
    log_echo "INFO" "wait_sshd_sftpd ${wait_key} to start finished."
    return 0
}

function stop_sshd_sftpd()
{
    local stop_key="$1"
    log_echo "INFO" "stop_sshd_sftpd ${stop_key} to stop."
    [ -f /os_config/need_docker_monitor.tag ] && rm -f /os_config/need_docker_monitor.tag
    
    local input_times=0
    while [ ${input_times} -lt 3 ];
    do
        tmp_pid=$(ps -ef | grep "/usr/sbin/${stop_key} -D -f /etc/ssh/${stop_key}_config" | grep -v grep |awk '{print $2}')
        if [ ! -z "${tmp_pid}" ];then
            log_echo "INFO" "The ${stop_key} to stop pid: ${tmp_pid}."
            kill -9 ${tmp_pid} >/dev/null 2>&1
            sleep 1
        fi
        input_times=$((${input_times}+1))
    done
    log_echo "INFO" "stop_sshd_sftpd ${stop_key} to stop finished."
    return 0
}

function pod_restart_sshd_sftpd()
{
    log_echo "INFO" "pod_restart_sshd_sftpd start..."
    if [ -f /etc/ssh/sshd_config -a ${is_modify_sshd} -eq 1 ];then
        log_echo "INFO" "pod_restart_sshd_sftpd the /etc/ssh/sshd_config is exists."
        stop_sshd_sftpd "sshd"
        wait_sshd_sftpd "sshd"
    fi
    
    if [ -f /etc/ssh/sftpd_config -a ${is_modify_sftpd} -eq 1 ];then
        log_echo "INFO" "pod_restart_sshd_sftpd the /etc/ssh/sftpd_config is exists."
        stop_sshd_sftpd "sftpd"
        wait_sshd_sftpd "sftpd"
    fi
    
    log_echo "INFO" "pod_restart_sshd_sftpd finished."
    return 0
}

function config_file()
{
    local config_file="$1"
    local modify_key="$2"
    local modify_value="$3"
    log_echo "INFO" "config_file ${config_file} ${modify_key} start..."
    if [ ! -f "${config_file}" ];then
        log_echo "INFO" "The config_file=${config_file} is not exists."
        return 0
    fi
    
    if [ -z "${modify_key}" -o -z "${modify_value}" ];then
        log_echo "ERROR" "The modify_key=${modify_key} or modify_value=${modify_value} has null."
        exit 1
    fi
    
    local get_value=$(grep "^[[:blank:]]*${modify_key}[[:blank:]]\+.*" ${config_file} |sed "s/^[[:blank:]]*${modify_key}[[:blank:]]\+\(.*\)/\1/g"|sed "s#[[:blank:]|[:cntrl:]]##g")
    if [ -z "${get_value}" ];then
        sed -i "1 i ${modify_key} ${modify_value}" ${config_file}
        log_echo "INFO" "add ${modify_key} ${modify_value} for ${config_file},ret=$?"
        is_modify_file=1
        return 0
    fi
    
    local isNumber=$(echo "${get_value}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        sed -ri -e "s/(.*${modify_key}.*)/#\1/g" -e "1 i ${modify_key} ${modify_value}" ${config_file}
        log_echo "INFO" "The get_value=${get_value} is not number,modify it ${modify_key} ${modify_value} for ${config_file},ret=$?"
        is_modify_file=1
        return 0
    fi
    
    if [ ${get_value} -lt ${modify_value} ];then
        sed -ri -e "s/(.*${modify_key}.*)/#\1/g" -e "1 i ${modify_key} ${modify_value}" ${config_file}
        log_echo "INFO" "The get_value=${get_value} is less than modify_value=${modify_value},modify it ${modify_key} ${modify_value} for ${config_file},ret=$?"
        is_modify_file=1
        return 0
    fi
    
    if [ ${get_value} -gt ${modify_value} -a "${modify_key}" == "MaxAuthTries" ];then
        sed -ri -e "s/(.*${modify_key}.*)/#\1/g" -e "1 i ${modify_key} ${modify_value}" ${config_file}
        log_echo "INFO" "The get_value=${get_value} is biger than modify_value=${modify_value},modify it ${modify_key} ${modify_value} for ${config_file},ret=$?"
        is_modify_file=1
        return 0
    fi
    
    log_echo "INFO" "The get_value=${get_value} is greater than or equal to modify_value=${modify_value},not need to modify it."
    return 0
}

function config_sshd_and_sftpd()
{
    log_echo "INFO" "config_sshd_and_sftpd start..."
    #modify file key MaxStartups 50 MaxSessions 100  MaxAuthTries 3 
    is_modify_file=0
    config_file "/etc/ssh/sshd_config" "MaxStartups" "50"
    config_file "/etc/ssh/sshd_config" "MaxSessions" "100"
    config_file "/etc/ssh/sshd_config" "MaxAuthTries" "3"
    is_modify_sshd=${is_modify_file}
    log_echo "INFO" "sshd_config_check=$(grep '^MaxStartups\|^MaxSessions\|^MaxAuthTries' /etc/ssh/sshd_config) is_modify_sshd=${is_modify_sshd}"
    is_modify_file=0
    config_file "/etc/ssh/sftpd_config" "MaxStartups" "50"
    config_file "/etc/ssh/sftpd_config" "MaxSessions" "100"
    config_file "/etc/ssh/sftpd_config" "MaxAuthTries" "3"
    is_modify_sftpd=${is_modify_file}
    [ -f /etc/ssh/sftpd_config ] && log_echo "INFO" "sftpd_config_check=$(grep '^MaxStartups\|^MaxSessions\|^MaxAuthTries' /etc/ssh/sftpd_config) is_modify_sftpd=${is_modify_sftpd}"
    # restart the server.
    if [ -f /.dockerenv ];then
        pod_restart_sshd_sftpd
    else
        if [ ${is_modify_sshd} -eq 1 ];then
            service sshd restart >>$LOG_FILE 2>&1
            if [ $? -ne 0 ];then
                /bin/systemctl reset-failed sshd.service
                /bin/systemctl restart sshd.service
            fi
        fi
        
        [ -f /etc/ssh/sftpd_config -a ${is_modify_sftpd} -eq 1 ] && service sftpd restart >>$LOG_FILE 2>&1
    fi
    log_echo "INFO" "config_sshd_and_sftpd end."
    return 0
}

function createscheduletask_uniep()
{
    log_echo "INFO" "createscheduletask_uniep start..."
    local output_file="/home/<USER>/uniep_backup_list.json"
    rm -f ${output_file}
    su - ossadm -c "sh /opt/oss/manager/agent/BackupService/bin/queryScheduleTask.sh -pn manager -output ${output_file}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "queryScheduleTask cmd:[ su - ossadm -c \"sh /opt/oss/manager/agent/BackupService/bin/queryScheduleTask.sh -pn manager -output ${output_file}\" ] failed."
        exit 1
    fi
    
    local queryscheduletask_ret=$(grep "\"productName\":[[:blank:]]*\"manager\"" ${output_file})
    if [ -z "${queryscheduletask_ret}" ];then
        log_echo "INFO" "not create manager schedule task,need create it."
        local input_file="/home/<USER>/createscheduletask_uniep.json"
        rm -f ${input_file}
        cp -rpf ${PreSet_PATH}/UniEp/createscheduletask_uniep.json ${input_file}
        local start_date=$(date +%F -d "+1day")
        log_echo "INFO" "The start_date=${start_date}"
        sed -i "s/{{start_date}}/${start_date}/g" ${input_file}
        chown ossadm:ossgroup ${input_file}
        chmod 640 ${input_file}
        su - ossadm -c "sh /opt/oss/manager/tools/backuprestore/createscheduletask.sh -input ${input_file}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Execute cmd:[ su - ossadm -c \"sh /opt/oss/manager/tools/backuprestore/createscheduletask.sh -input ${input_file}\" ] failed."
            exit 1
        fi
        rm -f ${output_file}
        rm -f ${input_file}
        log_echo "INFO" "Create manager schedule task finished."
        return 0
    fi
    rm -f ${output_file}
    log_echo "INFO" "The uniep scheduled task already exists and does not need to be created."
    return 0
}

function append_fisrv_to_digital_disk_param_configstring()
{
    local node_type="$1"
    local tmp_file="$2"
    log_echo "INFO" "Begin to append fisrv to digital disk_param_configstring, the node_type=${node_type} tmp_file=${tmp_file}."
    local fisrv_of_node_type=""
    local fisrv_pos=0
    if [ "X${node_type}" == "XSecondary" ]; then
        fisrv_pos=1
    elif [ "X${node_type}" == "XThird" ]; then
        fisrv_pos=2
    elif [ "X${node_type}" == "XExtendCluster" ]; then
        fisrv_pos=3
    fi

    local append_firsv_val=$(echo "$digital_disk_param_configstring" | grep -ow "fisrv:\([0-9]\+\)" | sed -n "${fisrv_pos}p")
    if [ -z "${append_firsv_val}" ]; then
        log_echo "INFO" "Can not get the keyword fisrv from digital_disk_param_configstring, with fisrv location=$fisrv_pos."
        return 1
    fi
    local digital_disk_param_configstring="${digital_disk_param_configstring},${append_firsv_val}"
    update_config "${tmp_file}" "digital_disk_param_configstring" "\"${digital_disk_param_configstring}\""
    log_echo "INFO" "Append ${append_firsv_val} to digital_disk_param_configstring=${digital_disk_param_configstring} successfully."
}

function del_ip_from_ipaddr()
{
    local ip=$1

    local is_ipv6="False"
    echo "${ip}" | grep ":" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        local is_ipv6="True"
    fi

    if ( ! is_empty_param "${ip}" );then
        ip addr |grep -wF "${ip}" > /dev/null 2>&1
        if [ $? -eq 0 ];then
            ip_info=$(ip addr |grep -wF "${ip}"|awk -F' ' '{print $2}')
            if [ "${is_ipv6}" == "False" ];then
                ip_nic=$(ip addr |grep -wF "${ip}"|awk -F' ' '{print $NF}')
            else
                ip_nic=$(get_ipv6_nic "${ip}")
            fi
            ip addr del "${ip_info}" dev "${ip_nic}"
            ip addr |grep -wF "${ip}" > /dev/null 2>&1
            if [ $? -eq 0 ];then
                return 1
            else
                log_echo "INFO" "delete IP:${ip} success"
            fi
        fi
    fi

    return 0
}

function get_ipv6_nic()
{
    local ipv6=$1

    ip_line=$(ip addr | grep -nwF "${ipv6}" | grep "inet" |awk -F ':' '{print $1}')
    all_nic_line=$(ip addr | grep -ni "BROADCAST" | awk -F ':' '{print $1}'|awk -F '@' '{print $1}' |sort -rn)
    for i in ${all_nic_line}
    do
        if [[ $ip_line -gt $i ]];then
            ip_nic_line=$i
            break;
        fi
    done
    ipv6_nic=$(ip addr | grep -ni "BROADCAST" | grep "^${ip_nic_line}:" |awk -F  ':' '{print $3}'|awk -F '@' '{print $1}'|sed 's/ //g'|head -1);

    ## return ipv6_nic
    echo "${ipv6_nic}"
}

function check_preinstall_in_pod()
{
    if [ ! -f /.dockerenv ]; then
        log_echo "INFO" "Not a docker env, not need to check whether is preinstall."
        return 0
    fi
    if [ "X${netWorkType}" != "XM" ]; then
        log_echo "INFO" "Not a merger cluster network, not need to check whether is preinstall."
        return 0
    fi
 
    cat ${source_file} | grep "^Configuration_String=" | grep "[0-9]" >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        log_echo "INFO" "This is merger cluster network and docker env, and Configuration_String is not null."
        update_config ${configParametersFile} "host_pre_install_key" "Host_PreInstall"
        return 0
    fi
    return 1
}

function get_node_num_by_nodetype()
{
    local nodeinfo_file=$1
    local nodetype=$2

    local node_num=$("${PreSet_PATH}/UniEp/python/bin/python"  "${PreSet_PATH}/tools/handlejson.py" get_node_num_by_nodetype "${nodeinfo_file}" "${nodetype}")

    echo "${node_num}"
}

function check_cfg_nodes()
{
    local cfg_file="$1"
    local key_list="$2"
    if [ ! -f "${cfg_file}" ];then
        log_echo "ERROR" "The cfg_file=${cfg_file} not exists."
        return 1
    fi

    if [ -z "${key_list}" ];then
        log_echo "ERROR" "The key_list is null."
        return 1
    fi

    local tmp_key=$(echo "${key_list}" |awk -F',' '{print $1}')
    local tmp_value=$(grep "^${tmp_key}=" ${cfg_file} |awk -F'=' '{print $2}')
    local tmp_node_num=$(echo "${tmp_value}" | sed "s#,# #g" |awk '{print NF}')
    local ret=0
    local tmp_value_num=0
    local message=""
    for tmp_key in $(echo "${key_list}" |sed "s/,/ /g");do
        tmp_value=$(grep "^${tmp_key}=" ${cfg_file} |awk -F'=' '{print $2}')
        tmp_value_num=$(echo "${tmp_value}" | sed "s#,# #g" |awk '{print NF}')
        if [ -z "${message}" ];then
            message="The number of nodes for ${tmp_key} is ${tmp_value_num}"
        else
            message="${message} ,${tmp_key} is ${tmp_value_num}"
        fi

        if [ ${tmp_value_num} -ne ${tmp_node_num} ];then
            ret=1
        fi
    done

    if [ ${ret} -eq 1 ];then
        log_echo "ERROR" "${message}.The number of nodes is different."
        return 1
    else
        log_echo "INFO" "${message}"
    fi
    return 0
}

function ping_ip_check()
{
    local ip="$1"
    [ -z "${ip}" ] && return 1

    local ping_cmd="ping"
    echo "${ip}" | grep ":" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        ping_cmd="ping6"
    fi

    ${ping_cmd} -w 3 ${ip} | grep "icmp_seq=" | grep "time="  > /dev/null 2>&1
    if [ $? -ne 0 ];then
        sleep 3
        ${ping_cmd} -w 3 ${ip} | grep "icmp_seq=" | grep "time="  > /dev/null 2>&1
        if [ $? -ne 0 ];then
            return 2
        fi
    fi

    return 0
}

function import_db()
{
    enable_os_rollback=$(grep "enable_os_rollback" ${UTILS_PATH}/DV_config.properties.tmp |awk -F'=' '{print $2}')
    log_echo "INFO" "enable_os_rollback=${enable_os_rollback}, begin execute import_db"
    local db_path=$(sudo -u dbuser bash -c "ls -d /opt/zenith/data/cloudsopdbsvr* 2>/dev/null")
    if [ "X${enable_os_rollback}" == "XTRUE"  -a  -n  "${db_path}" ];then
        source ${CURRENT_PATH}/tools/enable_os_rollback/db.cfg
        source /opt/oss/manager/bin/engr_profile.sh
    
        for service_name in ${dbname_service_list}
        do
            ipmc_adm -cmd stopapp -app ${service_name} -nodeip  global -tenant SOP > /dev/null 2>&1
            if [ $? -ne 0 ];then
                log_echo "ERROR" "stop ${service_name} failed, please check."
                exit 1
            fi
        done

        log_echo "INFO" "Begin import database"
        sh ${UTILS_PATH}/tools/enable_os_rollback/enable_os_rollback.sh "import"
        if [ $? -eq 0 ];then
            log_echo "INFO" "import database successful."
            for service_name in ${dbname_service_list}
            do
                ipmc_adm -cmd startapp -app ${service_name} -nodeip  global -tenant SOP > /dev/null 2>&1
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "start ${service_name} failed, please check."
                    exit 1
                fi
            done
        else
            log_echo "ERROR" "import database failed, please check ${UTILS_PATH}/tools/enable_os_rollback/pre_enable_os_rollback.log"
            exit 1
        fi
    else
        log_echo "INFO" "no need import database."
    fi
}

function config_sysctl_set(){
    local pattern="$1"
    local value=$2
    sysctl_items=$(sysctl -a |grep -E "${pattern}"|awk '{print $1}')
    for sysctl_item in ${sysctl_items};
    do
        echo ${sysctl_item}|grep -E "veth|flannel|cni|docker|br_|gw_|dummy" >>/dev/null
        if [ $? -eq 0 ];then
            continue
        fi
        adjust_sysctl  ${sysctl_item}  "$value"
    done
}

function custome_lvs_sysconf(){

    adjust_sysctl "net.core.netdev_max_backlog" "102400"
    adjust_sysctl "net.ipv4.conf.all.arp_announce" "2"
    adjust_sysctl "net.ipv4.conf.all.arp_ignore" "1"
    adjust_sysctl "net.ipv4.conf.all.rp_filter" "2"
    adjust_sysctl "net.ipv4.conf.all.src_valid_mark" "1"
    adjust_sysctl "net.ipv4.conf.default.src_valid_mark" "1"
    adjust_sysctl "net.ipv4.conf.lo.arp_announce" "2"
    adjust_sysctl "net.ipv4.conf.lo.arp_ignore" "1"
    adjust_sysctl "net.ipv4.ip_forward" "1"
    adjust_sysctl "net.ipv4.vs.expire_nodest_conn" "1"
    config_sysctl_set "net.*.conf.*\.forwarding" "1"
    cat <<EOF | sudo tee /etc/modprobe.d/ip_vs.conf
options ip_vs conn_tab_bits=20
EOF
 
}

function load_ip_vs()
{
    cat <<EOF | sudo tee /etc/modules-load.d/ip_vs.conf
ip_vs
ip_vs_rr 
ip_vs_ftp 
EOF
    log_echo "INFO" "load ip_vs by 'modprobe ip_vs_rr ip_vs_ftp ip_vs'"
    modprobe ip_vs_rr ip_vs_ftp ip_vs
    lsmod |grep ip_vs >/dev/null
    if [ $? -ne 0 ];then
        log_echo "ERROR" "'lsmod |grep ip_vs' can't find ip_vs module"
        exit 1
    fi
}

function del_outclient_pfx()
{
    local del_path="$1"
    sudo -u ossuser bash -c "ls ${del_path}" >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        log_echo "INFO" "the delete path is not a directory."
        return 1
    fi

    sudo -u ossuser bash -c "grep -w \"outclient.pfx\" ${del_path}/manifest.json" >/dev/null 2>&1
    if [ $? -eq 0 ]; then
         log_echo "INFO" "there is outclient.pfx password in manifest.json."
         return 1
    fi

    sudo -u ossuser bash -c "ls ${del_path}/outclient.pfx" >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        log_echo "INFO" "there is no outclient.pfx password in manifest.json, we will delete ${del_path}/outclient.pfx."
        sudo -u ossuser bash -c "rm -f ${del_path}/outclient.pfx"
    fi
}

function del_outclient_pfx_without_pwd()
{
    log_echo "INFO" "begin to delete outclient.pfx with password..."

    del_outclient_pfx "${i2k_user_home}/etc/ssl/dv"
    del_outclient_pfx "${INSTALL_PATH}/SOP/etc/ssl/dv"
    return 0
}

function check_lvs_ip_ping()
{
    local lvs_vip="$1"
    local lvs_dip="$2"

    local check_result=0
    ping_ip_check "${lvs_vip}"
    if [ $? -eq 0 ];then
        log_echo "ERROR" "LVS_VIP=${lvs_vip}, The IP address already exists and is in use. Please check."
        check_result=1
    fi
    ping_ip_check "${lvs_dip}"
    if [ $? -eq 0 ];then
        log_echo "ERROR" "LVS_DIP=${lvs_dip}, The IP address already exists and is in use. Please check."
        check_result=1
    fi

    [ $check_result -eq 0 ] || exit 1
    log_echo "INFO" "check LVS_VIP=${lvs_vip} and LVS_DIP=${lvs_dip} are available."
}

function modify_sudoconfig_in_upgrade_preset()
{
    file_name=$1
    if [ -f /.dockerenv -a -f /etc/${file_name} ];then
        cat /etc/${file_name} |grep "SOP_OS_PATH=/bin:/sbin:/usr/bin:/usr/sbin"
        if [ $? -ne 0 ];then
            SOP_OS_PATH=/bin:/sbin:/usr/bin:/usr/sbin
            log_echo "INFO" "Add extra Sudoconfig for SOP_OS_PATH."
            echo -e "\n# Sudoconfig: Start adding records.\nSOP_OS_PATH=${SOP_OS_PATH}\nPATH=\"\${PATH}:\${SOP_OS_PATH}\"\nexport PATH\n# Sudoconfig: End adding record. Editing in DV_Upgrade_BatchPreSet.sh\n" >> /etc/${file_name}
            log_echo "INFO" "Add extra Sudoconfig for SOP_OS_PATH SUCCESS !"
        else
            log_echo "The Sudoconfig has been existed ! Skip the config !"
        fi
    fi
}

function modify_conf_file()
{
    local cfg_file="$1"
    local cfg_key="$2"
    local cfg_value="$3"
    
    if [ ! -f ${cfg_file} ];then
        log_echo "The cfg_file=${cfg_file} do not exist. skip."
        return 0
    fi
    
    if [ -z "${cfg_key}" ];then
        log_echo "The cfg_file=${cfg_file} ,but group_name=${group_name} or cfg_key=${cfg_key} has null. skip."
        return 0
    fi
    
    grep "^${cfg_key}=" ${cfg_file} >>$LOG_FILE 2>&1
    if [ $? -ne 0 ];then
        log_echo "Not found ${cfg_key} in ${cfg_file}."
        return 0
    fi
    
    sed -i "s#^${cfg_key}=.*#${cfg_key}=${cfg_value}#g" ${cfg_file} >>$LOG_FILE 2>&1
    local result=$?
    log_echo "modify ${cfg_file} the ${cfg_key}=${cfg_value}, result=${result},check_ret=$(cat ${cfg_file} |grep ${cfg_key})"
    return 0
}

function unique_dir()
{
    local old_dir_list="$1"
    local add_dir_list="$2"
    new_dir_list=""
    if [ -z "${old_dir_list}" ];then
        log_echo "The old_dir_list is null."
        new_dir_list="${add_dir_list}"
        return 0
    fi
    
    if [ -z "${add_dir_list}" ];then
        log_echo "The add_dir_list is null."
        new_dir_list="${old_dir_list}"
        return 0
    fi
    
    new_dir_list="${old_dir_list}"
    local tmp_dir=""
    for tmp_dir in $(echo "${add_dir_list}" |sed "s#,# #g");
    do
        echo "${new_dir_list}"|sed "s#,#\n#g" |grep "^${tmp_dir}$" >>$LOG_FILE 2>&1
        if [ $? -eq 0 ];then
            log_echo "${tmp_dir} already exists in ${new_dir_list}.continue"
            continue
        fi
        
        new_dir_list="${new_dir_list},${tmp_dir}"
        log_echo "${tmp_dir} add to ${new_dir_list}"
    done
    log_echo "The new_dir_list=${new_dir_list}"
    return 0
}

function hang_handler_cfg()
{
    log_echo "hang_handler_cfg start."
    local cfg_file="$1"
    if [ ! -f ${cfg_file} ];then
        log_echo "The cfg_file=${cfg_file} do not exist. skip."
        return 0
    fi
    
    local node_mount_dir=""
    local dv_dir_list="${OPT_PATH}/zenith,${HOME_PATH}/vsindex,${OPT_PATH}/oss/share/SOP/DVEngineeringService,/srv/BigData,${OPT_PATH}/pub"
    local local_mount_dirs=""
    for dir in $(echo "${dv_dir_list}" |sed "s#,# #g");
    do
        local_mount_dirs=$(df -Ph |grep "${dir}" |awk '{print $NF}' |grep "^${dir}$")
        if [ "X${dir}" == "X${OPT_PATH}/zenith" ];then
            local_mount_dirs=$(df -Ph |grep "${dir}" |awk '{print $NF}'|head -1)
        fi
        
        if [ ! -z "${local_mount_dirs}" ];then
            node_mount_dir="${local_mount_dirs}"
            break
        fi
    done
    
    modify_conf_file "${cfg_file}" "Disk_Guard_Enable" "Yes"
    modify_conf_file "${cfg_file}" "Disk_User_Kdump" "Yes"
    modify_conf_file "${cfg_file}" "Disk_Check_Interval" "120"
    modify_conf_file "${cfg_file}" "Disk_Failure_Threshold" "10"
    
    local Disk_User_Partition=$(cat ${cfg_file} |grep "^Disk_User_Partition=" |awk -F'=' '{print $2}')
    if [ -z "${Disk_User_Partition}" ];then
        Disk_User_Partition="${node_mount_dir}"
    else
        unique_dir "${Disk_User_Partition}" "${node_mount_dir}"
        Disk_User_Partition="${new_dir_list}"
    fi
    modify_conf_file "${cfg_file}" "Disk_User_Partition" "${Disk_User_Partition}"
    
    modify_conf_file "${cfg_file}" "FS_Guard_Enable" "Yes"
    modify_conf_file "${cfg_file}" "FS_User_Kdump" "Yes"
    modify_conf_file "${cfg_file}" "FS_Check_Interval" "120"
    modify_conf_file "${cfg_file}" "FS_Failure_Threshold" "10"
    
    modify_conf_file "${cfg_file}" "FS_Inode_Enable" "No"
    modify_conf_file "${cfg_file}" "FS_Space_Enable" "No"
    modify_conf_file "${cfg_file}" "FS_Read_Only_Enable" "Yes"
    
    local FS_User_Partition=$(cat ${cfg_file} |grep "^FS_User_Partition=" |awk -F'=' '{print $2}')
    if [ -z "${FS_User_Partition}" ];then
        FS_User_Partition="${node_mount_dir}"
    else
        unique_dir "${FS_User_Partition}" "${node_mount_dir}"
        FS_User_Partition="${new_dir_list}"
    fi
    modify_conf_file "${cfg_file}" "FS_User_Partition" "${FS_User_Partition}"
    
    log_echo "restart hang-handler.service"
    systemctl restart hang-handler.service >> $LOG_FILE 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "run systemctl restart hang-handler.service failed."
        exit 1
    fi
    
    log_echo "check hang-handler.service status."
    systemctl status hang-handler.service  >> $LOG_FILE 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "run systemctl status hang-handler.service failed."
        exit 1
    fi
    
    log_echo "The hang-handler.service status is normal."
    log_echo "hang_handler_cfg End."
    return 0
}

function modify_hang_handler_conf()
{
    log_echo "modify_hang_handler_conf start."
    local is_integrated_df_and_service="$1"
    if [ "X${is_integrated_df_and_service}" == "XYes"  ];then
        log_echo "The is_integrated_df_and_service=${is_integrated_df_and_service} ,is integrated df,skip."
        return 0
    fi
    
    if [ -f "/.dockerenv" ];then
        log_echo "INFO" "/.dockerenv is exist, skip."
        return 0
    fi
    
    local cfg_file1="/etc/ras/hang-handler.conf"
    local cfg_file2="/home/<USER>/hang-handler/conf/hang-handler.conf"
    if [ ! -f ${cfg_file1} -a ! -f ${cfg_file2} ];then
        log_echo "The cfg_file1=${cfg_file1} and cfg_file2=${cfg_file2} do not exist. skip."
        return 0
    fi
    
    hang_handler_cfg "${cfg_file1}"
    hang_handler_cfg "${cfg_file2}"
    log_echo "modify_hang_handler_conf End."
    return 0
}

function check_custom_path_val()
{
    local path_name="$1"
    local path_val="$2"
    ## 去除空格前后空格，去除尾部/
    RETURN_VAL=$(echo "${path_val}" | sed 's# ##g' | sed 's#/\+$##g')

    ## 必须以/开头
    echo "$RETURN_VAL" | grep "^/" >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        log_echo "ERROR" "The custom path=${path_name} of value=${path_val} must start with /"
        exit 1
    fi

    ## 不能连续出现两次以上的/
    echo "$RETURN_VAL" | grep "//" >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        log_echo "ERROR" "The custom path=${path_name} of value=${path_val} cannot appear two or more times /."
        exit 1
    fi

    ## 限制路径名称必须是数字，字母，下划线
    echo "$RETURN_VAL" | grep "/[A-Za-z0-9_]\+$" >/dev/null 2>&1
    if [ $? -ne 0 ]; then
        log_echo "ERROR" "The custom path=${path_name} of value=${path_val} must only contain /, digits, letters, and underscores (_)."
        exit 1
    fi

    local dir_depth_reduce="${RETURN_VAL}"
    ## 限制目录层级为custom_path_depth
    for i in $(seq 1 $custom_path_depth) ; do
        dir_depth_reduce=$(dirname ${dir_depth_reduce})
    done
    if [ "$dir_depth_reduce" != "/" ]; then
        log_echo "ERROR" "The custom path=${path_name} of value=${path_val} depth must be $custom_path_depth."
        exit 1
    fi

    ## 目录黑名单校验
    local dir_block_name=""
    for dir_block_name in $(echo "$custom_path_blacklist" | sed "s#,# #g"); do
         if [ "${dir_block_name}" == "$RETURN_VAL" ]; then
             log_echo "ERROR" "The custom path=${path_name} of value=${path_val} can not be same with this path=$custom_path_blacklist."
             exit 1
         fi
    done
}

function validate_custom_path()
{
    if [ -n "${HOME_CUSTOM_PATH}" ]; then
        check_custom_path_val "HOME_CUSTOM_PATH" "${HOME_CUSTOM_PATH}"
        update_config ${dv_cfg_file} "HOME_CUSTOM_PATH" "${RETURN_VAL}" || exit 1
    fi

    if [ -n "${OPT_CUSTOM_PATH}" ]; then
        check_custom_path_val "OPT_CUSTOM_PATH" "${OPT_CUSTOM_PATH}"
        update_config ${dv_cfg_file} "OPT_CUSTOM_PATH" "${RETURN_VAL}" || exit 1
    fi

    if [ -n "${TMP_CUSTOM_PATH}" ]; then
        check_custom_path_val "TMP_CUSTOM_PATH" "${TMP_CUSTOM_PATH}"
        update_config ${dv_cfg_file} "TMP_CUSTOM_PATH" "${RETURN_VAL}" || exit 1
    fi
    
    . ${dv_cfg_file}
    ### 三个自定义路径名不能相同校验
    local custom_val=""
    local custom_path_list="${HOME_CUSTOM_PATH} ${OPT_CUSTOM_PATH} ${TMP_CUSTOM_PATH}"
    for custom_val in ${custom_path_list}; do
        if [ -z "${custom_val}" ]; then
            continue
        fi
        local match_count=$( echo "${custom_path_list}" | grep -ow "${custom_val}" | wc -l)
        if [ $match_count -gt 1 ]; then
            log_echo "ERROR" "The custom path HOME_CUSTOM_PATH,OPT_CUSTOM_PATH,TMP_CUSTOM_PATH cat not have the same value in $custom_path_list"
            exit 1
        fi
    done
    log_echo "INFO" "Validate the custom path HOME_CUSTOM_PATH,OPT_CUSTOM_PATH,TMP_CUSTOM_PATH successfully."
}


function back_script() {
    local backup_file_list=""
    backup_file_list="${PreSet_PATH}/tools/docker/dv_container_monitor.service"
    backup_file_list="${backup_file_list} ${PreSet_PATH}/tools/docker/dv_container_monitor.sh"
    backup_file_list="${backup_file_list} ${PreSet_PATH}/Common/dv_loop_monitor.service"
    backup_file_list="${backup_file_list} ${PreSet_PATH}/Common/dv_loop_monitor.sh"
    backup_file_list="${backup_file_list} ${PreSet_PATH}/Common/dv_loop_monitor.cfg"

    local file=""
    for file in ${backup_file_list}; do
        if [ -f "${file}_bak" ]; then
            cp "${file}_bak" ${file}
        else
            cp ${file} "${file}_bak"
        fi
    done
}

function update_script_path()
{
    back_script

    if [ "${ROOT_CUSTOM_PATH}" != "/root"  ]; then
        sed -i "s#/root/dv_pod_monitor/dv_container_monitor.sh#${ROOT_CUSTOM_PATH}/dv_pod_monitor/dv_container_monitor.sh#g" ${PreSet_PATH}/tools/docker/dv_container_monitor.service
        sed -i "s#/root/dv_loop_monitor/dv_loop_monitor.sh#${ROOT_CUSTOM_PATH}/dv_loop_monitor/dv_loop_monitor.sh#g" ${PreSet_PATH}/Common/dv_loop_monitor.service
    fi

    if [ "${OPT_PATH}" != "/opt" ]; then
        sed -i "s#/opt/dv_os_config#${OPT_PATH}/dv_os_config#g" ${PreSet_PATH}/tools/docker/dv_container_monitor.sh
        sed -i "s#^OPT_PATH=.*#OPT_PATH=${OPT_PATH}#g" ${PreSet_PATH}/Common/dv_loop_monitor.sh
    fi

    local var_log_path="/var/log/dv_docker"
    if [ "${HOME_PATH}" != "/home" ]; then
        var_log_path="${HOME_PATH}/var/log/dv_docker"
        sed -i "s#^HOME_PATH=.*#HOME_PATH=${HOME_PATH}#g" ${PreSet_PATH}/Common/dv_loop_monitor.sh
    fi
    sed -i "s#^log_path=.*#log_path=${var_log_path}#g" ${PreSet_PATH}/tools/docker/add_dv_node_to_k8s.sh
    sed -i "s#^log_path=.*#log_path=${var_log_path}#g" ${PreSet_PATH}/tools/docker/del_dv_node_to_k8s.sh
    sed -i "s#^log_path=.*#log_path=${var_log_path}#g" ${PreSet_PATH}/tools/docker/helm_install_at_k8s.sh
    sed -i "s#^log_path=.*#log_path=${var_log_path}#g" ${PreSet_PATH}/tools/docker/load_docker_image.sh
}

function restoring_the_default_configuration()
{
    local store_tag="$1"
    update_config ${dv_cfg_file} "OPT_PATH" "/opt" || exit 1
    update_config ${dv_cfg_file} "HOME_PATH" "/home" || exit 1
    update_config ${dv_cfg_file} "TMP_PATH" "/tmp" || exit 1
    update_config ${dv_cfg_file} "ROOT_CUSTOM_PATH" "/root" || exit 1
    update_config ${dv_cfg_file} "DEV_CUSTOM_PATH" "/dev" || exit 1
    update_config ${dv_cfg_file} "VAR_CUSTOM_PATH" "/var" || exit 1

    ## 重置系统盘挂载配置文件为空
    rm -f ${PreSet_PATH}/Common/custom_ext_disk.properties
    touch ${PreSet_PATH}/Common/custom_ext_disk.properties

    ## 还原被修改的脚本
    back_script

}

function check_custom_path()
{
    ## 重新引一遍，防止重入时被 ${dv_cfg_file}.tmp变量覆盖
    . ${dv_cfg_file}

    ## 还原默认配置
    restoring_the_default_configuration

    ### 自定义路径场景表量设置为FALSE
    update_config ${configParametersFile} "CUSTOM_PATH_SCENE" "FALSE" || exit 1

    if [ -z "${HOME_CUSTOM_PATH}" -a -z "${OPT_CUSTOM_PATH}" -a -z "${TMP_CUSTOM_PATH}" ]; then
        log_echo "INFO" "The custom path are null, not need to check."
        return 0
    fi

    ## 校验自定义路径是否合法
    validate_custom_path


    ## 默认opt路径是/opt
    OPT_PATH="/opt"
    if [ -n "${OPT_CUSTOM_PATH}" ]; then
        OPT_PATH="${OPT_CUSTOM_PATH}"
        update_config ${dv_cfg_file} "OPT_PATH" "${OPT_PATH}" || exit 1
        update_config ${dv_cfg_file} "PHYSICAL_PM_DB_NODE_PATH_LIST" "${OPT_PATH}/zenith/data/dvpmdbsvr1-0-1160,${OPT_PATH}/zenith/data/dvpmdbsvr2-0-1163,${OPT_PATH}/zenith/data/dvpmdbsvr1-1-1159,${OPT_PATH}/zenith/data/dvpmdbsvr2-1-1162,${OPT_PATH}/zenith/data/dvpmdbsvr3-3-1166,${OPT_PATH}/zenith/data/dvpmdbsvr4-3-1169,${OPT_PATH}/zenith/data/dvpmdbsvr3-4-1165,${OPT_PATH}/zenith/data/dvpmdbsvr4-4-1168,${OPT_PATH}/zenith/data/dvpmdbsvr5-5-1172,${OPT_PATH}/zenith/data/dvpmdbsvr6-5-1175,${OPT_PATH}/zenith/data/dvpmdbsvr5-6-1171,${OPT_PATH}/zenith/data/dvpmdbsvr6-6-1174" || exit 1
        update_config ${dv_cfg_file} "DOCKER_PM_KAFKA_PATH" "${OPT_PATH}/dv/oss/share/SOP/MQAgent/MessagingBrokeService/data/kafka-logs" || exit 1
        echo "${OPT_PATH}=lv_opt=50" >> ${PreSet_PATH}/Common/custom_ext_disk.properties
        echo "${OPT_PATH}/oss/log=lv_oss_log=20" >> ${PreSet_PATH}/Common/custom_ext_disk.properties
    fi

    ## 默认home路径是/home
    HOME_PATH="/home"
    if [ -n "${HOME_CUSTOM_PATH}" ]; then
        HOME_PATH="${HOME_CUSTOM_PATH}"
        update_config ${dv_cfg_file} "HOME_PATH" "${HOME_PATH}" || exit 1
        echo "${HOME_PATH}=lv_home=15" >> ${PreSet_PATH}/Common/custom_ext_disk.properties
        ## 更新 DV_config.properties中的ROOT_CUSTOM_PATH和DEV_CUSTOM_PATH
        update_config ${dv_cfg_file} "ROOT_CUSTOM_PATH" "${HOME_PATH}/root" || exit 1
        update_config ${dv_cfg_file} "DEV_CUSTOM_PATH" "${HOME_PATH}/dev" || exit 1
        update_config ${dv_cfg_file} "VAR_CUSTOM_PATH" "${HOME_PATH}/var" || exit 1
    fi

    ## 默认tmp路径是/tmp
    TMP_PATH="/tmp"
    if [ -n "${TMP_CUSTOM_PATH}" ]; then
        TMP_PATH="${TMP_CUSTOM_PATH}"
        update_config ${dv_cfg_file} "TMP_PATH" "${TMP_PATH}" || exit 1
    fi

    update_config ${dv_cfg_file} "custom_path_list" "\"${OPT_PATH},${HOME_PATH},${TMP_PATH}\""

    ### 自定义路径场景变量设置为TRUE
    update_config ${configParametersFile} "CUSTOM_PATH_SCENE" "TRUE" || exit 1

    . ${dv_cfg_file}
    . ${configParametersFile}

    update_script_path
}

function set_sudo_user_config()
{

    oper_user=root
    k8s_oper_user=root
    if [ "X${SUDO_USER}" != "X" ];then
        oper_user=${SUDO_USER}
        k8s_oper_user="${SUDO_USER}"
    fi

    update_config ${configParametersFile} "oper_user" "${oper_user}"
    
    update_config ${configParametersFile} "k8s_oper_user" "${k8s_oper_user}"

    if [ "X${oper_user}" == "Xroot" ]; then
        update_config ${configParametersFile} "SUDO_USER_COMMAND_IS_SU" "NO"
        return 0
    fi
    update_config ${configParametersFile} "SUDO_USER_COMMAND_IS_SU" "YES"
}

function check_architecture_type()
{
    auto_smart_ssh ${k8s_root_pwd} "${k8s_oper_user}@${K8S_IPV4} echo uname_m=\`uname -m\`"> ${CURRENT_PATH}/architecture_type.txt
    local k8s_architecture_type=$(cat ${CURRENT_PATH}/architecture_type.txt | grep "^uname_m="  | awk -F "uname_m=" '{print $2}' | tail -1 | sed "s/\r//g")
    local local_architecture_type=$(uname -m)
    if [ "X${local_architecture_type}" == "X${k8s_architecture_type}" ]; then
        log_echo "INFO" "The architectures are the same. Continue."
        rm -rf ${CURRENT_PATH}/architecture_type.txt
    else
        log_echo "ERROR" "The K8S's architecture is not same at the host's. Please unify the system architectures of them."
        exit 1
    fi
}