#!/bin/bash
set +x
if [ $# -lt 1 ]; then
    UTILS_PATH=$(cd $(dirname $0); pwd)
else
    UTILS_PATH="$1"
fi

if [ ! -f $UTILS_PATH/utils_os.sh ];then
    echo "The UTILS_PATH=${UTILS_PATH} is error of utils_os.sh"
    exit 1
fi

##Common
PreSet_PATH=$(cd ${UTILS_PATH};pwd)
isInCommon=$(basename ${PreSet_PATH})
if [ "${isInCommon}" == "Common" ];then
    PreSet_PATH=$(cd ${UTILS_PATH}/..;pwd)
fi

os_version=$(grep "VERSION=" /etc/os-release|head -1 | awk -F'=' '{print $2}'|sed "s/\"//g")
if [ "X${os_version}" == "X2.0 (SP9x86_64)" -o "X${os_version}" == "X2.0 (SP10x86_64)" -o "X${os_version}" == "X2.0 (SP10)" -o "X${os_version}" == "X12-SP5" -o "X${euler_version}" == "X2.0 (SP12x86_64)" -o "X${euler_version}" == "X2.0 (SP12)" ];then
  lsof_cmd=$(which lsof |grep -v "alias" |grep "/lsof" |awk '{print $1}')' -K i'
else
  lsof_cmd=$(which lsof |grep -v "alias" |grep "/lsof" |awk '{print $1}')
fi

command_prefix=""
if [ "X$(whoami)" == "Xroot" ];then
    command_prefix=""
else
    command_prefix="sudo -u ossuser "
fi
TEST_FILE="${command_prefix}test -f "
TEST_DIR="${command_prefix}test -d "
export LANG=en_US.UTF-8

function restart_sshd_service()
{
    /bin/systemctl reset-failed sshd.service
    /bin/systemctl restart sshd.service
}

function unset_tmout()
{
    local profile=/etc/profile
    local sshd_cfg=/etc/ssh/sshd_config
    
    rollback_unset_tmout "not_need_source"
    
    local unset_tmout_rest=0
    if [ -f ${profile} ];then
        cat ${profile} |grep  "TMOUT=" > ${UTILS_PATH}/profile_TMOUT
        cat ${profile} |grep  "readonly[ ]\+TMOUT" >> ${UTILS_PATH}/profile_TMOUT
        
        sed -i "/TMOUT=/d" ${profile}
        sed -i "/readonly[ ]\+TMOUT/d" ${profile}
        cp -rf ${UTILS_PATH}/profile_TMOUT ${UTILS_PATH}/profile_TMOUT.tmp
        sed -i "s/^[ ]*export[ ]\+TMOUT=.*/export TMOUT=0/g" ${UTILS_PATH}/profile_TMOUT.tmp
        unset_tmout_rest=$?
        log_echo "INFO" "unset tmout unset_tmout_rest1=${unset_tmout_rest}."  
        
        sed -i "s/^[ ]*TMOUT=.*/TMOUT=0/g" ${UTILS_PATH}/profile_TMOUT.tmp
        unset_tmout_rest=$?
        log_echo "INFO" "unset tmout unset_tmout_rest2=${unset_tmout_rest}."  
        
        cat ${UTILS_PATH}/profile_TMOUT.tmp >> ${profile}
        unset_tmout_rest=$?
        log_echo "INFO" "unset tmout unset_tmout_rest3=${unset_tmout_rest}." 
        
        source ${profile}
        unset_tmout_rest=$?
        log_echo "INFO" "unset tmout unset_tmout_rest4=${unset_tmout_rest}."
        
        unset TMOUT
        unset_tmout_rest=$?
        log_echo "INFO" "unset tmout unset_tmout_rest5=${unset_tmout_rest}."
    fi
    log_echo "INFO" "unset tmout of ${profile} finish."  
    
    local unset_sshd_rest=0
    if [ -f ${sshd_cfg} ];then
        ##ClientAliveInterval
        cat ${sshd_cfg} |grep -w "^ClientAliveInterval" > ${UTILS_PATH}/sshd_config_ClientAliveInterval
        sed -i "s/\(.*ClientAliveInterval[ ]\+.*\)/#\1/g" ${sshd_cfg}
        unset_sshd_rest=$?
        log_echo "INFO" "unset sshd cfg unset_sshd_rest1=${unset_sshd_rest}."  
        
        ## ClientAliveCountMax
        cat ${sshd_cfg} |grep -w "^ClientAliveCountMax" > ${UTILS_PATH}/sshd_config_ClientAliveCountMax
        sed -i "s/\(.*ClientAliveCountMax[ ]\+.*\)/#\1/g" ${sshd_cfg}
        unset_sshd_rest=$?
        log_echo "INFO" "unset sshd cfg unset_sshd_rest2=${unset_sshd_rest}."  
        
        ## TCPKeepAlive 
        cat ${sshd_cfg} |grep -w "^TCPKeepAlive" > ${UTILS_PATH}/sshd_config_TCPKeepAlive
        sed -i "s/.*TCPKeepAlive[ ]\+.*/TCPKeepAlive yes/g" ${sshd_cfg}
        unset_sshd_rest=$?
        log_echo "INFO" "unset sshd cfg unset_sshd_rest3=${unset_sshd_rest}." 
        
        cat ${sshd_cfg} |grep -w "^TCPKeepAlive" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            sed -i "1 i TCPKeepAlive yes" ${sshd_cfg}
            unset_sshd_rest=$?
            log_echo "INFO" "unset sshd cfg unset_sshd_rest4=${unset_sshd_rest}." 
        fi
        
        if [ -f /etc/SuSE-release ];then
            /sbin/service sshd restart
            unset_sshd_rest=$?
        else
            /bin/systemctl restart sshd.service 
            unset_sshd_rest=$?
        fi
        
        if [ $unset_sshd_rest -ne 0 ];then
            restart_sshd_service
        fi
        log_echo "INFO" "unset sshd cfg unset_sshd_rest6=${unset_sshd_rest}."  
    fi
    log_echo "INFO" "unset ClientAliveInterval,ClientAliveCountMax,TCPKeepAlive of ${sshd_cfg} finish."  
}

function adap_config_for_kylin()
{
    cat /etc/os-release | grep "Kylin" > /dev/null
    if [ $? -ne 0 ];then
        log_echo "INFO" "The system is not kylin OS, no need change scp file"
        return 0
    fi
    ssh -V 2>&1 |grep "OpenSSH_9"
    if [ $? -ne 0 ];then
        log_echo "INFO" "The ssh version is earlier than 9, no need change scp file"
        return 0
    fi
    log_echo "INFO" "Begin to change scp file in kylin OS when ssh version is greater than 9"
    scp_file_path=$(which scp)
    scp_dir_path=$(dirname $scp_file_path)
    if [ ! -f ${scp_dir_path}/scp_org ];then
        cp -pf ${scp_file_path} ${scp_dir_path}/scp_org
        if [ $? -ne 0 ];then
            log_echo "ERROR" "copy scp file ${scp_file_path} to ${scp_dir_path}/scp_org fai1ed, please check"
            exit 1
        fi
        echo '#!/bin/bash' > ${scp_file_path}
        echo "scp_org -O \$*" >>${scp_file_path}
    fi
    log_echo "INFO" "Change scp file ${scp_file_path} success"
    
     ####iconfig need ssh-rsa
    grep -rn "^HostKeyAlgorithms" /etc/ssh/sshd_config 
    if [ $? -ne 0 ];then
        sed -i "1 i HostKeyAlgorithms +ssh-rsa" /etc/ssh/sshd_config 
    else
        grep -w "^HostKeyAlgorithms" /etc/ssh/sshd_config  |grep -w "ssh-rsa" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            sed -i '/^HostKeyAlgorithms/ s/$/,ssh-rsa/g' /etc/ssh/sshd_config 
        fi
    fi
    
}

function rollback_unset_tmout()
{
    local profile=/etc/profile
    local sshd_cfg=/etc/ssh/sshd_config
    local tag="$1"
    
    local rollback_profile_rest=0
    if [ -f ${profile} -a -f ${UTILS_PATH}/profile_TMOUT ];then
        sed -i "/TMOUT=/d" ${profile}
        sed -i "/readonly[ ]\+TMOUT/d" ${profile}
        cat ${UTILS_PATH}/profile_TMOUT >> ${profile}
        rollback_profile_rest=$?
        log_echo "INFO" "The rollback_profile_rest1=${rollback_profile_rest}." 
        
        if [ "X${tag}" == "Xnot_need_source" ];then
            log_echo "INFO" "The rollback profile not need source." 
        else
            source ${profile}
            rollback_profile_rest=$?
            log_echo "INFO" "The rollback_profile_rest2=${rollback_profile_rest}." 
        fi
    fi
    log_echo "INFO" "rollback unset_tmout of ${profile} finish."  
    
    local rollback_sshd_rest=0
    if [ -f ${sshd_cfg} -a -f ${UTILS_PATH}/sshd_config_ClientAliveInterval -a -f ${UTILS_PATH}/sshd_config_ClientAliveCountMax -a -f ${UTILS_PATH}/sshd_config_TCPKeepAlive ];then
        ##ClientAliveInterval
        local ClientAliveInterval=$(cat ${UTILS_PATH}/sshd_config_ClientAliveInterval)
        if [ -z "${ClientAliveInterval}" ];then
            sed -i "/ClientAliveInterval/d" ${sshd_cfg}
        else
            sed -i "s/^#\(.*ClientAliveInterval[ ]\+.*\)/\1/g" ${sshd_cfg}
            rollback_sshd_rest=$?
            log_echo "INFO" "The rollback_sshd_rest1=${rollback_sshd_rest}." 
        fi
        
        ## ClientAliveCountMax
        local ClientAliveCountMax=$(cat ${UTILS_PATH}/sshd_config_ClientAliveCountMax)
        if [ -z "${ClientAliveCountMax}" ];then
            sed -i "/ClientAliveCountMax/d" ${sshd_cfg}
        else
            sed -i "s/^#\(.*ClientAliveCountMax[ ]\+.*\)/\1/g" ${sshd_cfg}
            rollback_sshd_rest=$?
            log_echo "INFO" "The rollback_sshd_rest2=${rollback_sshd_rest}." 
        fi
        
        ## TCPKeepAlive
        local TCPKeepAlive=$(cat ${UTILS_PATH}/sshd_config_TCPKeepAlive)
        if [ -z "${TCPKeepAlive}" ];then
            sed -i "/TCPKeepAlive/d" ${sshd_cfg}
        else
            cat ${UTILS_PATH}/sshd_config_TCPKeepAlive |grep "^[ ]*TCPKeepAlive[ ]\+.*"
            if [ $? -ne 0 ];then
                sed -i "s/\(.*TCPKeepAlive[ ]\+.*\)/#\1/g" ${sshd_cfg}
                rollback_sshd_rest=$?
                log_echo "INFO" "The rollback_sshd_rest3=${rollback_sshd_rest}." 
            fi
        fi
        
        if [ -f /etc/SuSE-release ];then
            /sbin/service sshd restart
            rollback_sshd_rest=$?
        else
            /bin/systemctl restart sshd.service 
            rollback_sshd_rest=$?
        fi
        
        if [ $rollback_sshd_rest -ne 0 ];then
            restart_sshd_service
        fi
        log_echo "INFO" "The rollback_sshd_rest4=${rollback_sshd_rest}."  
    fi
    log_echo "INFO" "rollback ClientAliveInterval,ClientAliveCountMax,TCPKeepAlive of ${sshd_cfg} finish."  
}

function check_ip_ping_concurrent()
{
    local ip=$1
    local info_type="$2"
    
    if [ "X${ip}" == "X" ];then
        log_echo "INFO" "check_ip_ping_concurrent ip=${ip} is null.not need check it."
        return 0
    fi
    log_echo "INFO" "check_ip_ping_concurrent ip=${ip} check it."
    local ping_cmd="ping"
    echo "${ip}" | grep ":" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        ping_cmd="ping6"
    fi
    
    ${ping_cmd} -w 4 ${ip} | grep "icmp_seq=" | grep "time="  > /dev/null 2>&1
    local ping_ret=$?
    if [ "${info_type}" == "check_noping_ip" -a ${ping_ret} -ne 0 ];then
        ## Check whether the IP address can not be pinged. An error needs to be reported.
        log_echo "ERROR" "check_ip_ping_concurrent ,The IP[ ip=${ip} ] can't be reached."
        return 1
    elif [ "${info_type}" == "check_ping_ip" -a ${ping_ret} -eq 0 ];then
        ## Check whether the IP address can be pinged. but occupied, an error needs to be reported.
        log_echo "ERROR" "check_ip_ping_concurrent check whether the IP[ ip=${ip} ] address has been used."
        return 1
    else
        log_echo "INFO" "check_ip_ping_concurrent ip=${ip} info_type=${info_type}, Check whether the IP address is correct."
    fi
    return 0
}

function check_ip_ping()
{
    local ip=$1
    if [ "X${ip}" == "X" ];then
        null_list="${null_list}  $2"
        return 0
    fi
    
    ping_cmd="ping"
    echo "${ip}" | grep ":" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        ping_cmd="ping6"
    fi
    
    ${ping_cmd} -w 4 ${ip} | grep "icmp_seq=" | grep "time="  > /dev/null 2>&1
    if [ $? -ne 0 ];then
        sleep 5
        ${ping_cmd} -w 4 ${ip} | grep "icmp_seq=" | grep "time="  > /dev/null 2>&1
        if [ $? -ne 0 ];then
            noping_list="${noping_list}  ${ip}"
            return 0
        fi
    fi
    
    return 0
}

function convert_ip_to_binary()
{
    local ipv6_ip=$1
    num=0
    for i in `echo $ipv6_ip|sed 's/:/ /g'`
    do
        if [ "$i" != "" ];then
            num=$((num+1))
        fi
    done

    size=$((8-num))
    tmp=""
    for ((i=1;i<=${size};i++))
    do
        tmp="$tmp:0000"
    done

    tmp="${tmp}:"
    entire_ip=$(echo $ipv6_ip | sed "s/::/${tmp}/")

    ip_binary=""
    for i in `echo $entire_ip |sed 's/:/ /g'`
    do
        tmp_i=$(printf "%04x\n" 0x${i})
        tmp_i=$(echo ${tmp_i}|tr [a-z] [A-Z])
        tmp_i=$(echo "ibase=16;obase=2;${tmp_i}"|bc)
        tmp_i=$(printf "%016x\n" 0x${tmp_i})
        ip_binary="${ip_binary}${tmp_i}"
    done

    echo ${ip_binary}
}

function check_ipv6_diff_net()
{
    local ip1=$1
    local ip2=$2
    local mask=$3

    ### 防止出现填写网络类型为1或者2未填写IPV6地址
    if [ "X${ip1}" == "X" -o "X${ip2}" == "X" ]; then
        log_echo "ERROR" "The IPv6 address is empty. Please check the config_file!"
        exit 1
    fi

    ### 防止出现填写网络类型为1或者2未填写IPV6地址
    if [ "X${ip1}" == "X" -o "X${ip2}" == "X" ]; then
        log_echo "ERROR" "The IPv6 address is empty. Please check the config_file!"
        exit 1
    fi

    if [ "X${mask}" == "X" ]; then
        log_echo "ERROR" "The netmask is empty. Please check the config_file!"
        exit 1
    fi

    local binary_ip1=$(convert_ip_to_binary "${ip1}")
    local binary_ip2=$(convert_ip_to_binary "${ip2}")
    
    maskpart_ip1=$(echo ${binary_ip1:0:${mask}})
    maskpart_ip2=$(echo ${binary_ip2:0:${mask}})
    
    if [ "X${maskpart_ip1}" == "X" -o "X${maskpart_ip2}" == "X" ];then
        log_echo "ERROR" "Convert ${ip1} or ${ip2} to binary type has mistake, please check"
        exit 1
    fi
    
    ##0: different net    1:same net
    if [ "X${maskpart_ip1}" != "X${maskpart_ip2}" ];then
        return 0
    fi
    
    return 1
}

function check_suse_version()
{
    log_echo "INFO" "Begin to check os version"
    #TanZu Photon OS relate to Suse.
    cat /etc/os-release | grep -E "SUSE|Photon" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        cat /etc/os-release | grep -E "Server 12 SP5|VERSION_ID=3.0" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Only support SuSe 12 SP5, please check"
            exit 1
        fi
    fi
    #麒麟V10SP2、SP3也支持
    cat /etc/os-release | grep -E "Kylin" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        cat /etc/os-release | grep -E "Server V10">/dev/null 2>&1
        if [ $? -ne 0 ];then
          log_echo "ERROR" "Only support V10SP2、SP3 for Kylin, please check"
          exit 1
        fi
    fi
    log_echo "INFO" "Begin to check os version finished."
}

function check_ipv6_floatip()
{
    local floatip_ipv6="$1"
    local netmask="$2"
    local nic="$3"
    local cfg_key="$4"
    
    if [ -z "${floatip_ipv6}" -o -z "${netmask}" -o -z "${nic}" -o -z "${cfg_key}" ];then
        log_echo "ERROR" "The floatip_ipv6=${floatip_ipv6} or netmask=${netmask} or nic=${nic} or cfg_key=${cfg_key} is null.please check."
        return 1
    fi
    
    local tmp=$(echo "${floatip_ipv6}" |grep ":" |grep "\." )
    if [ -z "${tmp}" ];then
        log_echo "INFO" "The floatip_ipv6=${floatip_ipv6}.This IP does not need to be checked and modified."
        return 0
    fi
    
    ping6 -w 4 ${floatip_ipv6} | grep "icmp_seq=" | grep "time="  > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "ERROR" "The floatip_ipv6=${floatip_ipv6} already exist.please check."
        return 1
    fi
    
    local old_ipv6_list=$(ifconfig ${nic} |grep "inet6" |awk '{print $2}')
    
    ifconfig ${nic} add ${floatip_ipv6}/${netmask} >/dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "The floatip_ipv6=${floatip_ipv6} add failed.please check.the ${floatip_ipv6} is invalid."
        return 1
    fi
    
    local new_ipv6_list=$(ifconfig ${nic} |grep "inet6" |awk '{print $2}')
    
    ifconfig ${nic} del ${floatip_ipv6}/${netmask} >/dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "The floatip_ipv6=${floatip_ipv6} del failed.please check."
        return 1
    fi
    
    local get_add_ipv6=""
    local isExist=""
    for ipv6_ip in ${new_ipv6_list};do
        isExist=$(echo "${old_ipv6_list}" |grep -iw "${ipv6_ip}")
        if [ -z "${isExist}" ];then
            get_add_ipv6="${ipv6_ip}"
            break
        fi
    done
    
    if [ -z "${get_add_ipv6}" ];then
        log_echo "ERROR" "The get_add_ipv6=${get_add_ipv6} is null.get add ipv6 failed."
        return 1
    fi
    
    if [ -z "${source_file}" -o ! -f ${source_file} ];then
        log_echo "ERROR" "The source_file=${source_file} is null or the file is not exist."
        return 1
    fi
    
    ## ${cfg_key}=
    isExist=$(grep -w "^#${cfg_key}=${floatip_ipv6}" ${source_file})
    if [ -z "${isExist}" ];then
        local line_index=$(grep -n "^${cfg_key}=.*" ${source_file} |awk -F':' '{print $1}')
        sed -i "${line_index} i #${cfg_key}=${floatip_ipv6}" ${source_file}
    fi
    
    sed -i "s#^${cfg_key}=.*#${cfg_key}=${get_add_ipv6}#g" ${source_file}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "The modify ${source_file} config ${cfg_key} failed.please check."
        return 1
    fi
    
    return 0
}

function pre_haveged()
{
    if [ -f "/.dockerenv" ];then
        log_echo "INFO" "Containerized scene installation ,no need to config haveged ."
        return 0
    fi

    ps -ef |grep "haveged" |grep -v grep >/dev/null 2>&1
    if [ $? -ne 0 ];then
        service haveged start
        if [ $? -ne 0 ];then
            die "Start service haveged failed, please check"
        fi
    fi
    
    chkconfig haveged | grep disabled >/dev/null 2>&1
    if [ $? -ne 0 ];then
        chkconfig haveged on
        if [ $? -ne 0 ];then
            die "chkconfig haveged on failed, please check"
        fi
    fi
    
    if [ -f "/etc/UnionTech-release" ];then
        return 0
    fi

    if [ -f /proc/sys/kernel/random/read_wakeup_threshold ];then
        entropy_avail=$(cat /proc/sys/kernel/random/entropy_avail)
        read_wakeup_threshold=$(cat /proc/sys/kernel/random/read_wakeup_threshold)
        log_echo "INFO" "entropy_avail=${entropy_avail} read_wakeup_threshold=${read_wakeup_threshold}."
        
        rd_ret=0
        isNum "${entropy_avail}" || rd_ret=1
        isNum "${read_wakeup_threshold}" || rd_ret=1
        
        if [ ${rd_ret} -eq 0 -a ${entropy_avail} -lt ${read_wakeup_threshold} ];then
            /usr/sbin/haveged -w 1024 -v 1 --Foreground &
        fi
    fi
    
    return 0
}

function checkDNS()
{
    log_echo "INFO" "start check dns config ip..."
    if [ "X${NEED_CHECK_DNS}" != "XYes" -o "X${is_integrated_df_and_service}" == "XYes" -o -f /.dockerenv -o "X${is_upgrade}" == "XYes" ];then
        log_echo "INFO" "not need to check dns config ip.skip it."
        return 0
    fi
    
    local dnsCfg="/etc/resolv.conf"
    local key="nameserver"
    if [ -f ${dnsCfg} ];then
        local dnsIpList=$(cat ${dnsCfg} |grep -w "${key}" |grep -v "^[ ]*#" |sed "s/${key}//g" |awk -F'#' '{printf " "$1}')
        if [ -z "${dnsIpList}" ];then
            log_echo "INFO" "The dnsIpList=${dnsIpList} is null.not has config dns ip."
            return 0
        fi
        
        for dnsIp in ${dnsIpList};do
            if [ "X${dnsIp}" == "X" ];then
                continue
            fi
            
            local ping_cmd="ping"
            echo "${dnsIp}" | grep ":" >/dev/null 2>&1
            if [ $? -eq 0 ];then
                ping_cmd="ping6"
            fi
            
            ${ping_cmd} -w 4 ${dnsIp} | grep "icmp_seq=" | grep "time="  > /dev/null 2>&1
            if [ $? -ne 0 ];then
                log_echo "INFO" "The dnsCfg=${dnsCfg} , Execute cmd[${ping_cmd} -w 4 ${dnsIp} | grep icmp_seq= | grep time= ]failed."
                nslookup ${dnsIp} 
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "The dnsCfg=${dnsCfg} has a ip:${dnsIp} Execute cmd[${ping_cmd} -w 4 ${dnsIp} | grep icmp_seq= | grep time= ] and execute cmd[ nslookup ${dnsIp} ] failed.Please check."
                    return 1
                fi
            fi
        done
    else
        log_echo "INFO" "The dnsCfg=${dnsCfg} is not exist."
    fi
    
    log_echo "INFO" "Check dns config ip finished."
    return 0
}

function stop_system_service()
{
    service sysmonitor stop >> ${LOG_FILE} 2>&1
    systemctl stop syslog.socket >> ${LOG_FILE} 2>&1
    service rsyslog stop >> ${LOG_FILE} 2>&1
    service syslog stop >> ${LOG_FILE} 2>&1
    service tuned stop >> ${LOG_FILE} 2>&1
    service auditd stop >> ${LOG_FILE} 2>&1
    if [ -f "/etc/UnionTech-release" ];then
        systemctl stop sssd >> ${LOG_FILE} 2>&1
        systemctl stop chronyd >> ${LOG_FILE} 2>&1
        systemctl stop rsyslog >> ${LOG_FILE} 2>&1
    fi
}

function start_system_service()
{
    service tuned start >> ${LOG_FILE} 2>&1
    service syslog start >> ${LOG_FILE} 2>&1
    service sysmonitor start >> ${LOG_FILE} 2>&1
    service rsyslog restart >> ${LOG_FILE} 2>&1
    systemctl start syslog.socket >> ${LOG_FILE} 2>&1
    service auditd start >> ${LOG_FILE} 2>&1
    if [ -f "/etc/UnionTech-release" ];then
        mkdir -p /var/log/sssd
        mkdir -p /var/log/chrony
        systemctl start sssd >> ${LOG_FILE} 2>&1
        systemctl start chronyd >> ${LOG_FILE} 2>&1
        systemctl start rsyslog >> ${LOG_FILE} 2>&1
    fi
}

function create_vg_lv()
{
    log_echo "INFO" "Begin to create vg and lv of extend disk..."
    vgdisplay ${vg_Name} >/dev/null 2>&1
    if [ $? -ne 0 ];then
        vgcreate -v ${vg_Name} ${disk_name} >> ${LOG_FILE} 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Create VG ${vg_Name} failed, please check..."
            exit 1
        fi
    fi

    for line in `cat ${ext_disk_cfg}`
    do
        if [ "X$line" == "X" ];then
            continue
        fi
        path_name=$(echo $line |awk -F'=' '{print $1}')
        lv_name=$(echo $line |awk -F'=' '{print $2}')
        lv_size=$(echo $line |awk -F'=' '{print $3}')
        
        lvdisplay /dev/${vg_Name}/${lv_name} >/dev/null 2>&1
        if [ $? -ne 0 ];then
            lvcreate -y -L ${lv_size}G -n ${lv_name} ${vg_Name} >> ${LOG_FILE} 2>&1
            if [ $? -ne 0 ] ; then
                log_echo "ERROR" "Create LV ${lv_name} failed, please check..."
                exit 1
            else
                log_echo "INFO" "Create LV ${lv_name} success..."
            fi
            if [ "${path_name}" != "swap" ];then
                df -Ph | grep -w "/dev/mapper/${vg_Name}-${lv_name}" > /dev/null 2>&1
                if [ $? -eq 0 ];then
                    mount_dir=$(df -Ph | grep -w "/dev/mapper/${vg_Name}-${lv_name}"|awk -F' ' '{print $NF}')
                    kill_disk_process "${mount_dir}"
                    umount /dev/mapper/${vg_Name}-${lv_name}
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "Umount /dev/mapper/${vg_Name}-${lv_name} failed before mkfs, please check..."
                        exit 1
                    fi
                fi
                mkfs_logic_volume "/dev/${vg_Name}/${lv_name}"
            fi
        fi
    done
}

function mount_lv()
{
    log_echo "INFO" "Begin to mount extend disk..."
    vgchange -a y ${vg_Name} >> ${LOG_FILE} 2>&1
    if [ $? -ne 0 ] ; then
        log_echo "ERROR" "Vgchange -a y VG ${vg_Name} failed, please check..."
        exit 1
    fi

    while read line
    do
        if [ "X$line" == "X" ];then
            continue
        fi
        path_name=$(echo $line |awk -F'=' '{print $1}')
        lv_name=$(echo $line |awk -F'=' '{print $2}')
        
        if [ "${path_name}" != "swap" ];then
            umask 022
            mkdir -p ${path_name}
            df -Ph ${path_name} | grep -w "/dev/mapper/${vg_Name}-${lv_name}" > /dev/null 2>&1
            if [ $? -ne 0 ] ; then
                kill_disk_process "${path_name}"
                mount /dev/${vg_Name}/${lv_name} ${path_name}
                if [ $? -ne 0 ] ; then
                    df -Ph ${path_name} | grep -w "/dev/mapper/${vg_Name}-${lv_name}" > /dev/null 2>&1
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "Mount /dev/${vg_Name}/${lv_name} to ${path_name} failed, please check..."
                        exit 1
                    else
                        df -Ph ${path_name} | grep -w "/dev/mapper/${vg_Name}-${lv_name}" | grep -w "64Z" > /dev/null 2>&1
                        if [ $? -eq 0 ];then
                            log_echo "ERROR" "Mount /dev/${vg_Name}/${lv_name} to ${path_name} get 64Z, please check..."
                            exit 1
                        else
                            log_echo "INFO" "Mount /dev/${vg_Name}/${lv_name} to ${path_name} success..."
                        fi
                    fi
                else
                    log_echo "INFO" "Mount /dev/${vg_Name}/${lv_name} to ${path_name} success..."
                fi
                chmod 755 ${path_name}
            fi
        else
            dm_name=$(readlink -f /dev/${vg_Name}/${lv_name})
            swapon -s |grep -w ${dm_name} > /dev/null 2>&1
            if [ $? -ne 0 ] ; then
                mkswap /dev/${vg_Name}/${lv_name} >> ${LOG_FILE} 2>&1
                if [ $? -ne 0 ] ; then
                    log_echo "ERROR" "Failed to mkswap /dev/${vg_Name}/${lv_name}, please check..."
                    exit 1
                fi
                swapon /dev/${vg_Name}/${lv_name} >> ${LOG_FILE} 2>&1
                if [ $? -ne 0 ] ; then
                    log_echo "ERROR" "Failed to swapon /dev/${vg_Name}/${lv_name}, please check..."
                    exit 1
                fi
                log_echo "INFO" "Swapon /dev/${vg_Name}/${lv_name} success..."
            fi
        fi
    done < ${ext_disk_cfg}
}

function set_dev_auto_mount()
{
    local type=$1
    local fstabFile="/etc/fstab"
    local filetype="ext4"
    log_echo "INFO" "Set dev auto mount in ${fstabFile}..."
    echo "" >> /etc/fstab

    if [ "${type}" == "ext" ];then
        while read line
        do
            if [ "X$line" == "X" ];then
                continue
            fi
            path_name=$(echo $line |awk -F'=' '{print $1}')
            lv_name=$(echo $line |awk -F'=' '{print $2}')
            if [ "${path_name}" != "swap" ];then
                if [ -z "`grep -w \"/dev/mapper/${vg_Name}-${lv_name}\" ${fstabFile} | grep -v \"/dev/${vg_Name}/${lv_name}/\" | grep -w \"${path_name}\"`" ];then
                    path_tmp=$(echo ${path_name} |sed "s#\/#\\\/#g")
                    [ "X${path_tmp}" != "X" ] && sed -i "/[ |\t]\+${path_tmp}[ |\t]\+/d" ${fstabFile}
                    echo "/dev/mapper/${vg_Name}-${lv_name}   ${path_name}   ${filetype}    nofail,defaults     0   0  #DV_Preset" >> ${fstabFile}
                fi
            else
                if [ -z "`grep -w \"/dev/mapper/${vg_Name}-${lv_name}\" /etc/fstab | grep -v \"/dev/mapper/${vg_Name}-${lv_name}/\" | grep -w 'swap'`" ];then
                    sed -i "/[ |\t]\+swap[ |\t]\+/d" ${fstabFile}
                    echo "/dev/mapper/${vg_Name}-${lv_name}   swap   swap    nofail,defaults     0   0  #DV_Preset" >> ${fstabFile}
                fi
            fi
        done < ${ext_disk_cfg}
    else
        if [ -z "`grep -w \"/dev/mapper/${local_vg_name}-${local_lv_name}\" ${fstabFile} | grep -v \"/dev/mapper/${local_vg_name}-${local_lv_name}/\" | grep -w \"${mount_point}\"`" ];then
            mount_point_tmp=$(echo ${mount_point} |sed "s#\/#\\\/#g")
            [ "X${mount_point_tmp}" != "X" ] && sed -i "/[ |\t]\+${mount_point_tmp}[ |\t]\+/d" ${fstabFile}
            echo "/dev/mapper/${local_vg_name}-${local_lv_name}   ${mount_point}   ${filetype}    nofail,defaults     0   0  #DV_Preset" >> ${fstabFile}
        fi 
    fi
}

function check_non_standard_mirror()
{
    log_echo "INFO" "start check mirror is standard or not..."
    ## 仅宿主机修改
    if [ ! -f /.dockerenv ]; then
        ## 自定义路径场景
        if [ "X${CUSTOM_PATH_SCENE}" == "XTRUE" ]; then
            log_echo "INFO" "This is CUSTOM_PATH_SCENE=${CUSTOM_PATH_SCENE}."
            [ -f "${ext_disk_cfg}" ] && rm -rf ${ext_disk_cfg:?}
            cp -rpf ${UTILS_PATH}/custom_ext_disk.properties ${ext_disk_cfg} >/dev/null 2>&1
            return 0
        fi

        if [ ! -f /var/adm/autoinstall/scripts/extend_disk.sh ];then
            log_echo "INFO" "This mirror is not standard, start modify ext_disk config file..."
            [ -f "${ext_disk_cfg}" ] && rm -rf ${ext_disk_cfg:?}
            local nonstandard_cfg=${UTILS_PATH}/nonstandard_ext_disk.properties
            cp -rpf ${nonstandard_cfg} ${ext_disk_cfg} >/dev/null 2>&1
            return 0
        fi
    fi
    return 1
}

function auto_mount_ext()
{
    rm -f ${UTILS_PATH}/mount_extdisk_finish
    disk_name=${EXTEND_98G_DISK}
    vg_Name="vg_ext"
    file_type="ext4"
    ext_disk_cfg=${ext_disk_properties}.tmp
    
    current_extdisk_name=$(grep "^current_extdisk_name=" ${dv_cfg_file}.tmp|awk -F'=' '{print $2}')
    if [ "X${current_extdisk_name}" != "X" ];then
        disk_name=${current_extdisk_name}
    fi
    
    log_echo "INFO" "Begin to do auto_mount_ext..."
    
    check_non_standard_mirror

    stop_system_service
    
    create_vg_lv

    mount_lv
    
    start_system_service
    
    set_dev_auto_mount "ext"
    
    touch ${UTILS_PATH}/mount_extdisk_finish
    if [ "X${current_extdisk_name}" != "X" ];then
        grep "current_extdisk_name=" ${UTILS_PATH}/mount_extdisk_finish > /dev/null 2>&1
        if [ $? -ne 0 ];then
            echo "current_extdisk_name=${current_extdisk_name}" >> ${UTILS_PATH}/mount_extdisk_finish
        fi
    fi
}

function create_local_lv()
{
    log_echo "INFO" "Begin to create ${local_vg_name}..."
    vgdisplay ${local_vg_name} >/dev/null 2>&1
    if [ $? -ne 0 ];then
        echo "y"|vgcreate -v ${local_vg_name} ${local_disk_name} >> ${LOG_FILE} 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Create VG ${local_vg_name} failed, please check..."
            exit 1
        fi
    fi
    
    lvdisplay /dev/${local_vg_name}/${local_lv_name} >/dev/null 2>&1
    if [ $? -ne 0 ];then
        lvcreate -y -l 100%FREE -n ${local_lv_name} ${local_vg_name} >> ${LOG_FILE} 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Create LV ${local_lv_name} failed, please check..."
            exit 1
        fi
        
        df -Ph | grep -w "/dev/mapper/${local_vg_name}-${local_lv_name}" > /dev/null 2>&1
        if [ $? -eq 0 ];then
            mount_dir=$(df -Ph | grep -w "/dev/mapper/${local_vg_name}-${local_lv_name}"|awk -F' ' '{print $NF}')
            kill_disk_process "${mount_dir}"
            umount /dev/mapper/${local_vg_name}-${local_lv_name}
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Umount /dev/mapper/${local_vg_name}-${local_lv_name} failed before mkfs, please check..."
                exit 1
            fi
        fi

        mkfs_logic_volume "/dev/${local_vg_name}/${local_lv_name}"
    fi
}

function mount_local_lv()
{
    log_echo "INFO" "Begin to mount ${mount_point}..."
    vgchange -a y ${local_vg_name} >> ${LOG_FILE} 2>&1
    if [ $? -ne 0 ] ; then
        log_echo "ERROR" "Vgchange -a y VG ${local_vg_name} failed, please check..."
        exit 1
    fi
    
    umask 022
    mkdir -p ${mount_point}
    
    df -Ph ${mount_point} | grep -w "/dev/mapper/${local_vg_name}-${local_lv_name}" > /dev/null 2>&1
    if [ $? -ne 0 ] ; then
        kill_disk_process "${mount_point}"
        mount -o nosuid /dev/${local_vg_name}/${local_lv_name} ${mount_point}
        if [ $? -ne 0 ] ; then
            df -Ph ${mount_point} | grep -w "/dev/mapper/${local_vg_name}-${local_lv_name}" > /dev/null 2>&1
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Mount /dev/${local_vg_name}/${local_lv_name} to ${mount_point} failed, please check..."
                exit 1
            else
                df -Ph ${mount_point} | grep -w "/dev/mapper/${local_vg_name}-${local_lv_name}" | grep -w "64Z" > /dev/null 2>&1
                if [ $? -eq 0 ];then
                    log_echo "ERROR" "Mount /dev/${local_vg_name}/${local_lv_name} to ${mount_point} get 64Z, please check..."
                    exit 1
                else
                    log_echo "INFO" "Mount /dev/${local_vg_name}/${local_lv_name} to ${mount_point} success..."
                fi
            fi
        fi
    fi
}

function auto_mount_local()
{
    mount_point=$1
    local_disk_name=$2
    least_disk_size=$3
    local_vg_name=$4
    local_lv_name=$5
    file_type="ext4"
    log_echo "INFO" "Begin to auto mount ${mount_point}..."

    create_local_lv
    
    mount_local_lv
    
    set_dev_auto_mount "local"
}

function add_etc_hosts()
{
    local local_ip_list=$(ip addr |grep  -E "inet|inet6" |grep -v "127.0.0.1" |grep -v " ::1/" |awk '{print $2}'|awk -F'/' '{print $1}')
    host_name=$(hostname)
    for listenIp in ${local_ip_list}
    do
        grep -w "${listenIp}" /etc/hosts 2>/dev/null| grep -w "${host_name}" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            echo "" >> /etc/hosts
            echo "${listenIp}    ${host_name}" >> /etc/hosts
        fi
    done
    
    return 0
}

function set_user_passwd()
{
    ## 3. Set user password
    user_name=$1
    user_pwd=$2
    echo " ... begin to set users [${user_name}] passwd ... " >> $LOG_FILE
    
    if [ -f "/etc/security/opasswd" ];then
        sed -i "/^${user_name}:/d" /etc/security/opasswd   
    fi
    
    if [ -f /.dockerenv -a -f /etc/pam.d/common-password ];then
        log_echo "INFO"  "del pam_pwhistory.so for /etc/pam.d/common-password"
        sed -i "/pam_pwhistory.so/d" /etc/pam.d/common-password
    fi
    
    suse_version=0
    if [  -f /etc/SuSE-release ];then
        suse_version=$(cat /etc/SuSE-release | grep VERSION | awk -F'=' '{print $2}' | sed -r 's/ //g')
    fi
    if [ ${suse_version} -eq 11 ]; then
        cp -f /etc/default/passwd /etc/default/passwd.bakByCie
        echo -e "CRYPT=sha256\nCRYPT_FILES=sha256" > /etc/default/passwd        
    fi
    log_echo "INFO"  "Set password for user ${user_name}"
    passwd -d ${user_name}
    local ret="0"
    if [ ${suse_version} -eq 12 ]; then
        echo "${user_name}":"${user_pwd}" | chpasswd >>$LOG_FILE 2>&1 || ret="1"
    else
        echo "${user_pwd}" | passwd ${user_name} --stdin >>$LOG_FILE 2>&1
        if [ $? -ne 0 ];then
            echo "${user_name}":"${user_pwd}" | chpasswd >>$LOG_FILE 2>&1 || ret="1"
        fi
    fi
    
    if [ ${suse_version} -eq 11 ]; then
        mv /etc/default/passwd.bakByCie /etc/default/passwd
    fi
    
    if [ -f /.dockerenv -a "X$(whoami)" == "Xroot" ];then
        change_enforce_for_root
    fi
    
    if [ "X$ret" == "X1" ];then
        log_echo "ERROR" "Set password of ${user_name} failed, please check!"
        exit 1
    fi
}

function restore_sshd_config()
{
    local sshd_file=$1
    local bak_sshd_file=$2

    sed -i "/^ListenAddress.*DV_PreSet_ListenAddress$/d" ${sshd_file}
    sed -i "/AllowUsers sysomc/d" ${sshd_file}
    sed -i "/AllowUsers sopuser/d" ${sshd_file}
    sed -i "/AllowUsers sshossuser/d" ${sshd_file}
    sed -i "/AllowUsers dvshareuser/d" ${sshd_file}
    sed -i "/AllowUsers devdata/d" ${sshd_file}

    config_list="MaxStartups MaxSessions ChallengeResponseAuthentication PasswordAuthentication TCPKeepAlive AllowGroups"

    for config in ${config_list}
    do
        bak_config=$(cat "${bak_sshd_file}" | grep -w "^[[:space:]]*${config}" | tail -1)
        if [ -z "${bak_config}" ];then
            cat "${sshd_file}" | grep -w "${config}" > /dev/null 2>&1
            if [ $? -eq 0 ];then
                sed -i "/^[[:space:]]*${config}/d" "${sshd_file}"
                log_echo "INFO" "restore ${config} in sshd_config"
            fi
            continue
        fi

        value=$(echo ${bak_config} | sed "s/^[[:space:]]*${config}[ ]*\(.*\)/\1/g")
        sed -i "s/^[[:space:]]*${config}.*/${config} ${value}/g" "${sshd_file}"
        log_echo "INFO" "restore ${config} in sshd_config"
    done

    unset_tmout_config="ClientAliveInterval ClientAliveCountMax"
    for config in ${unset_tmout_config}
    do
        bak_config_tmout=$(grep -w "${config}" "${bak_sshd_file}")
        [ -z "${bak_config_tmout}" ] && continue;
        sed -i "s/.*${config}.*/${bak_config_tmout}/g" "${sshd_file}"
    done
}

function delete_ip_to_sshd_file()
{
    log_echo "INFO" "exec delete_ip_to_sshd_file!"
    local sshd_file="/etc/ssh/sshd_config"
    local back_sshd_file="${sshd_file}.DV_PreSet_bak"
    
    if [ ! -f ${sshd_file} ];then
        log_echo "INFO" "The ${sshd_file} is not exist!"
        return 0
    fi
    
    if [ ! -f ${back_sshd_file} ];then
        log_echo "INFO" "The ${back_sshd_file} is not exist!"
        return 0
    fi

    sed -i '/DV_PreSet_ListenAddress/d' $back_sshd_file

    restore_sshd_config ${sshd_file} ${back_sshd_file}

    if [ "X$(whoami)" == "Xroot" ];then
        rollback_unset_tmout
    fi

    service sshd restart >> $LOG_FILE
    if [ $? -ne 0 ];then
        restart_sshd_service
        if [ $? -ne 0 -a ! -f "/.dockerenv" ];then
            log_echo "ERROR" "service sshd restart failed!"
            exit 1
        fi
    fi

    rm -f "${sshd_file}.DV_PreSet_bak"
}

function check_ip()
{
    local ip="$1"
    if [ -z "${ip}" ];then
        log_echo "INFO" "The ip=${ip} is None!"
        return 1
    fi

    echo "${ip}" |grep "\.\|:" >> $LOG_FILE 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "The ip=${ip} is true!"
        return 0
    fi

    log_echo "INFO" "The ip=${ip} is false!"
    return 1
}

function check_ip_type()
{
    local ip="$1"
    local ip_type="$2"
    if [ -z "${ip}" -o -z "${ip_type}" ];then
        log_echo "INFO" "The ip=${ip} or ip_type=${ip_type} is None!"
        return 1
    fi

    local isIPV6=$(echo "${ip}" |grep ":")
    if [ "X${ip_type}" == "X0" -a "X${isIPV6}" != "X" ];then
        ## ip type is ipv4 but ip is ipv6
        log_echo "ERROR" "check we need ip type is ipv4, But ${ip} is not ipv4.Please check."
        return 1
    elif [ "X${ip_type}" == "X1" -a "X${isIPV6}" == "X" ];then
        ## ip type is ipv6 but ip is ipv4
        log_echo "ERROR" "check we need ip type is ipv6, But ${ip} is not ipv6.Please check."
        return 1
    fi

    log_echo "INFO" "The ip=${ip} type is true!"
    return 0
}

function restart_sshd()
{
    cat /etc/ssh/sshd_config | grep "^ListenAddress" >> ${LOG_FILE} 2>&1
    if [ $? -eq 0 ];then
        sshd_pid=$(ps -ef | grep "/usr/sbin/sshd -D -f /etc/ssh/sshd_config" | grep -v grep |awk '{print $2}')
        if [ ! -z "${sshd_pid}" ];then
            kill -9 ${sshd_pid} >> ${LOG_FILE} 2>&1
        fi
        log_echo "INFO" "restart ssh service"
        /usr/sbin/sshd -D -f /etc/ssh/sshd_config -E /var/log/sshd.log &
    fi
}

##############################
# config /etc/ssh/sshd_config
# parameter ip1,ip2,ip3.....
##############################
function add_sshd_file()
{
    log_echo "INFO" "exec add_sshd_file!"
    local listenIpList="$1"
    local scene_type="$2"
    local sshd_file="/etc/ssh/sshd_config"

    if [ ! -f ${sshd_file} ];then
        log_echo "ERROR" "The ${sshd_file} is not exist!"
        exit 1
    fi

    if [ ! -f "${sshd_file}.DV_PreSet_bak" ];then
        cp -rf ${sshd_file} ${sshd_file}.DV_PreSet_bak > /dev/null 2>&1
    fi

    grep "^[ ]*ListenAddress[ ]\+0.0.0.0.*" ${sshd_file} >/dev/null 2>&1
    if [ $? == 0 ];then
        sed -i "s/\(^[ ]*ListenAddress[ ]\+0.0.0.0.*\)/#\1/g" ${sshd_file}
    fi

    grep "^[ ]*ListenAddress[ ]\+::.*" ${sshd_file} >/dev/null 2>&1
    if [ $? == 0 ];then
        sed -i "s/\(^[ ]*ListenAddress[ ]\+::.*\)/#\1/g" ${sshd_file}
    fi

    local lastLine=$(grep -n "ListenAddress.*" ${sshd_file} |tail -n 1|awk -F':' '{print $1}')
    if [ -z "${lastLine}" ];then
        lastLine=1
    fi

    local isNumber=$(echo "${lastLine}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        lastLine=1
    fi

    local exist_ip=""
    if [ -z "${listenIpList}" ];then
        log_echo "INFO" "The listenIpList=${listenIpList} is None!"
    else
        for listenIp in $(echo "${listenIpList}"|sed "s|,| |g")
        do
            if [ -z "${listenIp}" ];then
                continue
            fi

            check_ip "${listenIp}" || continue

            listenIp=$(echo "${listenIp}"|sed "s/\./\\\./g")
            exist_ip=$(cat ${sshd_file} |grep -w "${listenIp}" |grep "^[ ]*ListenAddress" )
            if [ "X${exist_ip}" == "X" ]; then
                sed -i "${lastLine} a ListenAddress ${listenIp} #DV_PreSet_ListenAddress" ${sshd_file}
            fi
        done
    fi

    log_echo "INFO" "The is_integrated_df_and_service=${is_integrated_df_and_service}"
    local local_ip_list=$(ip addr |grep  -E "inet|inet6" |grep -v "127.0.0.1" |grep -v " ::1/" |awk '{print $2}'|awk -F'/' '{print $1}')
    if [ "X${is_integrated_df_and_service}" == "XYes" -o "X${scene_type}" == "Xdocker" -o "X${scene_type}" == "Xpod" ];then
        log_echo "INFO" "The is_integrated_df_and_service=${is_integrated_df_and_service} scene_type=${scene_type}"
        local_ip_list=""
    fi

    if [ -f /.dockerenv ];then
        log_echo "INFO" "The node is pod node. is_integrated_df_and_service=${is_integrated_df_and_service} scene_type=${scene_type}"
        local_ip_list=""
    fi

    if [ ! -z "${local_ip_list}" ];then
        exist_ip=""
        for listenIp in ${local_ip_list};
        do
            if [ -z "${listenIp}" ];then
                continue
            fi
            listenIp=$(echo "${listenIp}"|sed "s/\./\\\./g")
            exist_ip=$(cat ${sshd_file} |grep -w "${listenIp}" |grep "^[ ]*ListenAddress" )
            if [ "X${exist_ip}" == "X" ]; then
                sed -i "${lastLine} a ListenAddress ${listenIp} #DV_PreSet_ListenAddress" ${sshd_file}
            fi
        done
    fi

    if [ -f /.dockerenv ];then
        restart_sshd
    else
        ## restart sshd
        service sshd restart  >> $LOG_FILE
        if [ $? -ne 0 ];then
            restart_sshd_service
        fi
    fi

    return $?
}

function check_ssh_sftp_seperate()
{
    grep -E "^sftponly" /etc/group > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        return 1
    fi
    if [ ! -f "/etc/ssh/sftpd_config" ]; then
        return 1
    fi

    if [ ! -f "/etc/init.d/sftpd" ] && [ ! -f "/usr/lib/systemd/system/sftpd.service" ] && [ ! -f "/usr/sbin/sftpd" ]; then
        return 1
    fi

    return 0
}
function config_sftp_user(){
    user=$1
    local sftpd_file="/etc/ssh/sftpd_config"
    grep -E "^sftponly" /etc/group|grep "${user}" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        usermod_add_group sftponly  ${user}
    fi
    if [ -d /home/<USER>
        chmod    750 /home/<USER>
        chown   root:sftponly  /home/<USER>
        chmod 600 /home/<USER>/.bash* >> $LOG_FILE 2>&1
        chmod 600 /home/<USER>/.mksh* >> $LOG_FILE 2>&1
        chmod 600 /home/<USER>/.ksh*  >> $LOG_FILE 2>&1
        chmod 600 /home/<USER>/.zsh*  >> $LOG_FILE 2>&1
        chmod 600 /home/<USER>/.csh*  >> $LOG_FILE 2>&1
    fi

    grep "Match User ${user}" ${sftpd_file} >/dev/null 2>&1
    if [ $? -ne 0 ];then
        echo "" >> ${sftpd_file}
        echo "Match User ${user}" >> ${sftpd_file}
        echo "    ForceCommand internal-sftp -l VERBOSE -f AUTH" >> ${sftpd_file}
        echo "    ChrootDirectory /home/<USER>" >> ${sftpd_file}
        echo "    AllowTcpForwarding no" >> ${sftpd_file}
        echo "    AllowAgentForwarding no" >> ${sftpd_file}
        echo "    PasswordAuthentication no" >> ${sftpd_file}
        echo "    X11Forwarding no" >> ${sftpd_file}
        echo "" >> ${sftpd_file}
    fi
}
function config_ssh_sftp_seperate()
{
    check_ssh_sftp_seperate
    if [ $? -ne 0 ];then
        return 0
    fi
    grep -E "^sftponly" /etc/group|grep "root" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        usermod_add_group sftponly  root
    fi
    grep -E "^sftponly" /etc/group|grep -w "ossuser" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        usermod_add_group sftponly  ossuser
    fi

    config_sftp_user sftpossuser
    if [ "X${is_upgrade}" != "XYes" ];then
        sed -i "s/.*Subsystem\s\+.*/#Subsystem sftp internal-sftp -l VERBOSE -f AUTH/g" /etc/ssh/sshd_config
    fi
    id devdata >/dev/null 2>&1
    if [ $? -eq 0 ];then
        config_sftp_user  devdata
        if [ ! -f /home/<USER>/.bash_history ];then
            touch /home/<USER>/.bash_history
        fi
        chown devdata:ossgroup /home/<USER>/.bash_history
        if [ ! -f /home/<USER>/.bashrc ];then
            cp /etc/skel/.bashrc  /home/<USER>/.bashrc
        fi
        chown devdata:ossgroup /home/<USER>/.bashrc
        if [ ! -f /home/<USER>/.bash_logout ];then
            cp /etc/skel/.bash_logout  /home/<USER>/.bash_logout
        fi
        chown devdata:ossgroup /home/<USER>/.bash_logout
        if [ ! -f /home/<USER>/.bash_profile ];then
            cp /etc/skel/.bash_profile  /home/<USER>/.bash_profile
        fi
        chown devdata:ossgroup /home/<USER>/.bash_profile
        grep -rn "^HostKeyAlgorithms" /etc/ssh/sftpd_config
        if [ $? -ne 0 ];then
            sed -i "1 i HostKeyAlgorithms +ssh-rsa" /etc/ssh/sftpd_config
        else
            grep -w "^HostKeyAlgorithms" /etc/ssh/sftpd_config |grep -w "ssh-rsa" > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i '/^HostKeyAlgorithms/ s/$/,ssh-rsa/g' /etc/ssh/sftpd_config
            fi
        fi
    fi
    grep "DenyUsers ossuser"  /etc/ssh/sftpd_config
    if [ $? -ne 0 ];then
        echo "DenyUsers ossuser" >> /etc/ssh/sftpd_config
    fi

    sed -i "s/.*MaxStartups.*/MaxStartups 100/g" /etc/ssh/sftpd_config
    cat /etc/ssh/sftpd_config |grep -w "^MaxStartups" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        sed -i "1 i MaxStartups 100" /etc/ssh/sftpd_config
    fi

    /bin/systemctl restart sshd
    if [ $? -ne 0 ];then
        restart_sshd_service
    fi
    /bin/systemctl enable sftpd
    /bin/systemctl restart sftpd

}

function restart_sftpd()
{
    cat /etc/ssh/sftpd_config | grep "^ListenAddress" >> ${LOG_FILE} 2>&1
    if [ $? -eq 0 ];then
        sftpd_pid=$(ps -ef | grep "/usr/sbin/sftpd -D -f /etc/ssh/sftpd_config" | grep -v grep |awk '{print $2}')
        if [ ! -z "${sftpd_pid}" ];then
            kill -9 ${sftpd_pid} >> ${LOG_FILE} 2>&1
        fi
        log_echo "INFO" "restart sftpd service"
        /usr/sbin/sftpd -D -f /etc/ssh/sftpd_config -E /var/log/sftpd.log &
    fi
}

function add_sftpd_file()
{
    check_ssh_sftp_seperate
    if [ $? -eq 0 ];then
        log_echo "INFO" "ssh and sftp seperate , need to add_sftpd_file"
    else
        log_echo "INFO" "ssh and sftp not seperate , skip add_sftpd_file"
        return 0
    fi

    local listenIpList="$1"
    local scene_type="$2"
    local sftpd_file="/etc/ssh/sftpd_config"

    grep "ListenAddress.*0.0.0.0" ${sftpd_file} >/dev/null 2>&1
    if [ $? == 0 ];then
        sed -ri "/.*ListenAddress.*0.0.0.0[ ]*$/d" ${sftpd_file}
        echo "#ListenAddress 0.0.0.0" >> ${sftpd_file}
    fi

    grep "ListenAddress.*::" ${sftpd_file} >/dev/null 2>&1
    if [ $? == 0 ];then
        sed -ri "/.*ListenAddress.*::[ ]*$/d" ${sftpd_file}
        echo "#ListenAddress ::" >> ${sftpd_file}
    fi

    sed -i "/ListenAddress .* #DV_PreSet_ListenAddress[ ]*$/d" ${sftpd_file}
    line=$(sed -n '/^Port/=' /etc/ssh/sftpd_config|head -n 1)

    local exist_ip=""
    if [ -z "${listenIpList}" ];then
        log_echo "INFO" "The listenIpList=${listenIpList} is None!"
    else
        for listenIp in $(echo "${listenIpList}"|sed "s|,| |g")
        do
            if [ -z "${listenIp}" ];then
                continue
            fi

            check_ip "${listenIp}" || continue

            listenIp=$(echo "${listenIp}"|sed "s/\./\\\./g")
            exist_ip=$(cat ${sftpd_file} |grep -w "${listenIp}" |grep "^[ ]*ListenAddress" )
            if [ "X${exist_ip}" == "X" ]; then
                if [ -n $line ];then
                    log_echo "info" "${FUNCTION_NAME}" "excuting sed -i \"$line i\ListenAddress $listenIp #DV_PreSet_ListenAddress\" ${sftpd_file} ..."
                    sed -i "$line a\ListenAddress $listenIp #DV_PreSet_ListenAddress" ${sftpd_file}
                else
                    log_echo "INFO" "excuting echo \"ListenAddress $listenIp #DV_PreSet_ListenAddress\" >> ${sftpd_file} ..."
                    echo "ListenAddress $listenIp #DV_PreSet_ListenAddress" >> ${sftpd_file}
                fi
            fi
        done
    fi

    log_echo "INFO" "The is_integrated_df_and_service=${is_integrated_df_and_service}"
    local local_ip_list=$(ip addr |grep  -E "inet|inet6" |grep -v "127.0.0.1" |grep -v " ::1/" |awk '{print $2}'|awk -F'/' '{print $1}')
    if [ "X${is_integrated_df_and_service}" == "XYes" -o "X${scene_type}" == "Xdocker" -o "X${scene_type}" == "Xpod" ];then
        log_echo "INFO" "The is_integrated_df_and_service=${is_integrated_df_and_service} scene_type=${scene_type}"
        local_ip_list=""
    fi

    if [ ! -z "${local_ip_list}" ];then
        for listenIp in ${local_ip_list};
        do
            if [ -z "${listenIp}" ];then
                continue
            fi
            listenIp=$(echo "${listenIp}"|sed "s/\./\\\./g")
            exist_ip=$(cat ${sftpd_file} |grep -w "${listenIp}" |grep "^[ ]*ListenAddress" )
            if [ "X${exist_ip}" == "X" ]; then
                sed -i "${line} a ListenAddress ${listenIp} #DV_PreSet_ListenAddress" ${sftpd_file}
            fi
        done
    fi

    if [ -f /.dockerenv ];then
        restart_sftpd
    else
        ## restart sftpd
        service sftpd restart  >> $LOG_FILE 2>&1
    fi

    return $?
}

function config_sudo_env_reset()
{
    sed -i "s#^Defaults .*env_reset#Defaults env_reset#g" /etc/sudoers
    sed -i "s#^Defaults .*\!visiblepw#Defaults visiblepw#g" /etc/sudoers
    sed -i "s#^Defaults .*requiretty#\#Defaults requiretty#g" /etc/sudoers
}

function config_sudo()
{
    user_name=$1
    sudo_cmd_list=$(echo "$2" | sed 's/\\\:/:/g')
    sudo_user=$3
    if [ "X$sudo_user" == "X" ]; then
        sudo_user="root"
    fi
    cur_sudo=$(cat /etc/sudoers | grep "^${user_name} ALL=(${sudo_user})"|grep -v uniepsudobin |tail -1 | sed 's/\\\:/:/g')
    if [ "X$cur_sudo" == "X" ]; then
        fina_sudo="$user_name ALL=(${sudo_user}) NOPASSWD: $sudo_cmd_list"
        echo "${fina_sudo}" | sed 's/:/\\\:/g' | sed 's/NOPASSWD\\\:/NOPASSWD:/g' >> /etc/sudoers
    else
        fina_sudo=$(echo "$sudo_cmd_list" | awk -vcur_sudo="$cur_sudo" -F',' '{
            for (i = 1; i <= NF; i++)
            {
                if ( index(cur_sudo",", $i",") == 0 )
                {
                    cur_sudo=cur_sudo","$i
                }
            }
            print cur_sudo
        }' 2>/dev/null)

        local line_num=$(grep -n "^${user_name} ALL=(${sudo_user})" /etc/sudoers |grep -v uniepsudobin |tail -1|awk -F':' '{print $1}')
        if [ -z "${line_num}" ];then
            echo "${fina_sudo}" | sed 's/:/\\\:/g' | sed 's/NOPASSWD\\\:/NOPASSWD:/g' >> /etc/sudoers
        else
            local fina_sudo_new=$(echo "${fina_sudo}" | sed 's/:/\\\:/g' | sed 's/NOPASSWD\\\:/NOPASSWD:/g')
            sed -i "${line_num} a ${fina_sudo_new}" /etc/sudoers
            sed -i "${line_num},${line_num}d" /etc/sudoers
        fi
    fi

    config_sudo_env_reset

}

function clean_env()
{
    log_echo "INFO" "Begin to clean env."
    cd /tmp;rm -rf *.sh;rm -f /tmp/user_stat.txt;cd -;
    rm -f /tmp/com.springsource.oracle.jdbc-********.jar /tmp/slf4j-api-*.jar /tmp/slf4j-log4j12-*.jar
    rm -rf /tmp/create_oss_admin.log
    rm -rf /tmp/OMU.cfg
    rm -rf /tmp/alterOraProfile.sql
    rm -rf /tmp/grant_dbsu_priv.log
    rm -rf /tmp/revoke_dbsu_priv.log
    rm -rf /tmp/build_relationshiip.log
    rm -rf /tmp/build_trust_relation.exp
    rm -rf /tmp/build_relationship.log
}

function devdata_mkdir(){
    mkdir -p ${icnfg_devdata_user_home}/sftp/jdbc/
    mkdir -p ${icnfg_devdata_user_home}/sftp/necert/
    mkdir -p ${icnfg_devdata_user_home}/sftp/export/manual/
    mkdir -p ${icnfg_devdata_user_home}/sftp/import/manual/
    mkdir -p ${icnfg_devdata_user_home}/configpkg/
}

function devdata_set_permissions()
{
    if [ -d  ${icnfg_devdata_user_home}  ];then
        chown -R devdata:ossgroup  ${icnfg_devdata_user_home}/configpkg/
        chown -R devdata:ossgroup  ${icnfg_devdata_user_home}/sftp/
        setfacl -R -m d:u:ossuser:rwx ${icnfg_devdata_user_home}/sftp
        setfacl -R -m d:u:devdata:rwx  ${icnfg_devdata_user_home}/sftp
        setfacl -R -m d:g::-  ${icnfg_devdata_user_home}/sftp
        setfacl -R -m d:m::rwx  ${icnfg_devdata_user_home}/sftp
        setfacl -R -m u:ossuser:rwx ${icnfg_devdata_user_home}/sftp
        setfacl -R -m u:devdata:rwx  ${icnfg_devdata_user_home}/sftp
        setfacl -R -m g::-  ${icnfg_devdata_user_home}/sftp
        setfacl -R -m m::rwx  ${icnfg_devdata_user_home}/sftp

        setfacl -R -m d:u:ossuser:rwx ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m d:u:devdata:rwx  ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m d:g::-  ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m d:m::rwx  ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m u:ossuser:rwx ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m u:devdata:rwx  ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m g::-  ${icnfg_devdata_user_home}/configpkg/
        setfacl -R -m m::rwx  ${icnfg_devdata_user_home}/configpkg/

        find ${icnfg_devdata_user_home} -type d | xargs -i chmod 700 {}
        find ${icnfg_devdata_user_home} -type f | xargs -i chmod 600 {}

        find ${icnfg_devdata_user_home}/configpkg ${icnfg_devdata_user_home}/sftp -type d | xargs -i chmod 770 {}
        find ${icnfg_devdata_user_home}/configpkg ${icnfg_devdata_user_home}/sftp -type f | xargs -i chmod 660 {}

        if [ -d ${icnfg_devdata_user_home}/.ssh  ];then
            chmod 700 ${icnfg_devdata_user_home}/.ssh
            chown -R ${icnfg_devdata_user_name}.${i2k_group_name} ${icnfg_devdata_user_home}/.ssh/
        fi
        if [ -f ${icnfg_devdata_user_home}/.ssh/authorized_keys  ];then
            chmod 640 ${icnfg_devdata_user_home}/.ssh/authorized_keys
        fi
        chmod 750  ${icnfg_devdata_user_home}
    fi
}

function create_icnfg_user_and_group()
{
    id ${icnfg_devdata_user_name} >/dev/null 2>&1
    if [ $? -eq 0 ];then
        userdel -r ${icnfg_devdata_user_name} >/dev/null 2>&1
    fi
    log_echo "INFO" "Begin to create iCnfg user and group."
    mount_loop_device "/home/<USER>" "1024" "1024000" "/home/<USER>"
    useradd -g ${i2k_group_id} -u ${icnfg_devdata_user_id} -m ${icnfg_devdata_user_name} -d ${icnfg_devdata_user_home} || die "Create user failed! Please check by command \"useradd -g ${i2k_group_id} -u ${icnfg_devdata_user_id} -m ${icnfg_devdata_user_name} -d ${icnfg_devdata_user_home} \""
    chage -M 99999 ${icnfg_devdata_user_name}
    chown devdata:ossgroup  /home/<USER>
    set_user_passwd "${icnfg_devdata_user_name}" "${devdata_userpasswd}"
    devdata_mkdir

}

function delete_old_sudo()
{
    user_name=$1
    need_delete_cmd_list=$(echo "$2" | sed 's/\\\:/:/g')
    sudo_user=$3
    if [ "X$sudo_user" == "X" ]; then
        sudo_user="root"
    fi
    cur_sudo=$(cat /etc/sudoers | grep "^${user_name} ALL=(${sudo_user})"|grep -v uniepsudobin |tail -1 | sed 's/\\\:/:/g'|awk -F'NOPASSWD:' '{print $2}'|sed 's/^ *//')
    if [ "X$cur_sudo" == "X" ]; then
        return 0
    else
        fina_sudo=$(echo "$cur_sudo" | awk -vnouse_sudo="$need_delete_cmd_list" -vtmp_sudo="" -F',' '{
            for (i = 1; i <= NF; i++)
            {
                if ( index(nouse_sudo",", $i",") == 0 )
                {
                    tmp_sudo=tmp_sudo","$i
                }
            }
            print tmp_sudo
        }' 2>/dev/null)
    fi

    tmp_finalsudo=$(echo "${fina_sudo}"|sed 's/ //g')
    [ "${tmp_finalsudo}" == "" ] && return 0

    fina_sudo=$(echo "${fina_sudo}" | sed 's/^,//g')
    fina_sudo="${user_name} ALL=(${sudo_user}) NOPASSWD:  ${fina_sudo}"
    local fina_sudo_new=$(echo "${fina_sudo}" | sed 's/:/\\\:/g' | sed 's/NOPASSWD\\\:/NOPASSWD:/g')

    local line_num=$(grep -n "^${user_name} ALL=(${sudo_user})" /etc/sudoers |grep -v uniepsudobin |tail -1|awk -F':' '{print $1}')
    if [ -z "${line_num}" ];then
        echo "${fina_sudo_new}" >> /etc/sudoers
    else
        sed -i "${line_num} a ${fina_sudo_new}" /etc/sudoers
        sed -i "${line_num},${line_num}d" /etc/sudoers
    fi
    return 0
}

function usermod_add_group()
{
    local group_name=$1
    local username=$2
    local cmdline="usermod -a -G"
    if [ -f "/etc/SuSE-release" ];then
        OS_VERSION=$(cat /etc/SuSE-release|grep VERSION |awk -F'=' '{print $2}' |sed "s/ //g")
        if [ "X${OS_VERSION}" != "X12" ];then
            cmdline="usermod -A"
        fi
    fi
    log_echo "INFO" "The cmd is [ ${cmdline} ${group_name} ${username} ]"
    ${cmdline} ${group_name} ${username}
}

function modify_os()
{
    local pretty_name=$(grep "PRETTY_NAME=" /etc/os-release|head -1 | awk -F'=' '{print $2}'|sed "s/\"//g")

    echo "${pretty_name}" | grep -qi "ctyunos"
    if [ $? -ne 0 ];then
        return 0
    fi

    local OS_ARCH_TMP=$(lscpu |grep "Architecture:"|awk -F ':' '{print $2}' |sed "s/ //g")

    if [ "X${OS_ARCH_TMP}" == "Xaarch64" ];then
        sed -i 's/PRETTY_NAME=.*/PRETTY_NAME="EulerOS 2.0 (SP10)"/g' /etc/os-release
        echo "EulerOS release 2.0 (SP10)" > /etc/euleros-release
        chmod 644 /etc/euleros-release
    elif [ "X${OS_ARCH_TMP}" == "Xx86_64" ];then
        sed -i 's/PRETTY_NAME=.*/PRETTY_NAME="EulerOS 2.0 (SP10x86_64)"/g'   /etc/os-release
        echo "EulerOS release 2.0 (SP10x86_64)" > /etc/euleros-release
        chmod 644 /etc/euleros-release
    fi
}

function check_disk_exist()
{
    local diskname=$1

    local execute_tag=0
    for execute_time in {1..5} ; do
        fdisk -l "${diskname}" >/dev/null 2>&1
        if [ $? -eq 0 ];then
            execute_tag=1
            break
        fi
        log_echo "INFO" "Can not find ${diskname} in the host, execute in ${execute_time} times."
        sleep 2
    done
    if [ $execute_tag -eq 0 ]; then
        log_echo "ERROR" "Can not find ${diskname} in the host, please check."
        exit 1
    fi

    df -Ph | grep -w "${diskname}" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "ERROR" "${diskname} has been mounted, please check."
        exit 1
    fi

    which pvdisplay >/dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Can not find command 'pvdisplay' in the host, please check."
        exit 1
    fi
}

function caculate_lv_needSpace()
{
    local disk_name=$1
    local all_needSpace=0
    local size_list=$(cat ${ext_disk_cfg} |awk -F'=' '{print $3}')

    for i in ${size_list}
    do
        all_needSpace=$((all_needSpace+i+1))
    done

    pvdisplay ${disk_name} > /dev/null 2>&1
    if [ $? -ne 0 ];then
        pvcreate -y ${disk_name} >>${LOG_FILE} 2>&1
        if [ $? -ne 0 ] ; then
            log_echo "ERROR" "Pvcreate ${diskname} failed, please check."
            return 1
        fi
    fi

    pv_space_num=$(pvdisplay ${disk_name} |grep "PV Size"|awk '{print $3}'|sed "s/ //g"|awk -F'.' '{print $1}')
    pv_space_unit=$(pvdisplay ${disk_name} |grep "PV Size"|awk '{print $4}'|sed "s/ //g")
    echo ${pv_space_num} |grep "<" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        pv_space_num=$(echo ${pv_space_num}|sed 's/<//')
        all_needSpace=$((all_needSpace-1))
    fi

    check_ret=0
    if [ "${pv_space_unit}" == "GB" -o "${pv_space_unit}" == "GiB" ];then
        if [ ${all_needSpace} -gt ${pv_space_num} ];then
            log_echo "ERROR" "The space of ${disk_name} is ${pv_space_num} G, but we need ${all_needSpace} G at least..."
            check_ret=1
        fi
    else
        if [ "${pv_space_unit}" == "TB" -o "${pv_space_unit}" == "TiB" ];then
            tmp_pv_space_num=$((pv_space_num*1024))
        else
            tmp_pv_space_num=$((pv_space_num/1024))
        fi
        if [ ${all_needSpace} -gt ${tmp_pv_space_num} ];then
            log_echo "ERROR" "The space of ${disk_name} is ${tmp_pv_space_num}G, but we need ${all_needSpace}G at least..."
            check_ret=1
        fi
    fi

    if [ ${check_ret} -eq 1 ];then
        pvremove ${disk_name} >> ${LOG_FILE} 2>&1
        return 1
    fi
}

function auto_create_system_disk_part()
{
    check_disk_exist "${ext_disk_name}"

    current_extdisk_name=$(grep "^current_extdisk_name=" ${dv_cfg_file}.tmp|awk -F'=' '{print $2}')
    lsblk -pl ${ext_disk_name} |grep -E "${ext_disk_name}[0-9]" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        if [ "X${current_extdisk_name}" == "X" -o -z "`lsblk -pl ${ext_disk_name}|grep -w ${current_extdisk_name} 2>/dev/null`" ];then
            if [ ! -f "${UTILS_PATH}/new_partition_finish" -a ! -f "/tmp/new_partition_finish" ];then
                log_echo "INFO" "The disk ${ext_disk_name} has other partition, so we will create a new partition for auto_mount_ext..."
                tail_diskname_before=$(lsblk -Pb ${ext_disk_name}| grep -wE "MOUNTPOINT=\"\"|MOUNTPOINTS=\"\"" | awk -F "\"" '{print $2}' | sort | tail -1 | xargs -i echo "/dev/{}")
                fdisk -l ${ext_disk_name} | grep "label type" | grep -w "gpt" >/dev/null 2>&1
                if [ $? -eq 0 ];then
                    fdisk ${ext_disk_name} << HERE  >> ${LOG_FILE} 2>&1
n



w
HERE
                else
                    fdisk -l ${ext_disk_name} | grep -i "Extended" >> ${LOG_FILE} 2>&1
                    if [ $? -ne 0 ];then
                        fdisk ${ext_disk_name} << HERE  >> ${LOG_FILE} 2>&1
n
p



w
HERE
                    else
                        fdisk ${ext_disk_name} << HERE  >> ${LOG_FILE} 2>&1
n



w
HERE
                    fi
                fi
                if [ $? -ne 0 ];then
                    partprobe >>${LOG_FILE} 2>&1
                    tail_diskname_after=$(lsblk -Pb ${ext_disk_name}| grep -wE "MOUNTPOINT=\"\"|MOUNTPOINTS=\"\"" | awk -F "\"" '{print $2}' | sort | tail -1 | xargs -i echo "/dev/{}")
                    if [ "X${tail_diskname_before}" == "X${tail_diskname_after}" ];then
                        log_echo "ERROR" "Fdisk new partition of ${ext_disk_name} failed, please check..."
                        return 1
                    fi
                fi
                touch ${UTILS_PATH}/new_partition_finish
                cp -f ${UTILS_PATH}/new_partition_finish /tmp/new_partition_finish
                partprobe >>${LOG_FILE} 2>&1
            fi
        fi
        partprobe >>${LOG_FILE} 2>&1
        ext_disk_name=$(lsblk -Pb ${ext_disk_name}| grep -wE "MOUNTPOINT=\"\"|MOUNTPOINTS=\"\"" | awk -F "\"" '{print $2}' | sort | tail -1 | xargs -i echo "/dev/{}")

        sed -i "/^current_extdisk_name=/d" ${dv_cfg_file}.tmp
        [ -f "${dv_cfg_file}" ] && sed -i "/^current_extdisk_name=/d" ${dv_cfg_file}
        echo "current_extdisk_name=${ext_disk_name}" >> ${dv_cfg_file}.tmp
        echo "current_extdisk_name=${ext_disk_name}" > ${UTILS_PATH}/new_partition_finish
    else
        sed -i "/^current_extdisk_name=/d" ${dv_cfg_file}.tmp
        [ -f "${dv_cfg_file}" ] && sed -i "/^current_extdisk_name=/d" ${dv_cfg_file}

        pvdisplay | grep -w "${ext_disk_name}" >/dev/null 2>&1
        if [ $? -eq 0 ];then
            exist_vg_name=$(pvdisplay ${ext_disk_name} | grep "VG Name" |awk '{print $3}')
            if [ "X${exist_vg_name}" != "X" -a "X${exist_vg_name}" != "X${ext_vg_name}" ];then
                log_echo "ERROR" "${ext_disk_name} has been used for other vg ${exist_vg_name}, please check."
                return 1
            fi
        fi
    fi

    caculate_lv_needSpace "${ext_disk_name}"
    if [ $? -ne 0 ];then
        current_extdisk_name=$(grep "^current_extdisk_name=" ${dv_cfg_file}.tmp|awk -F'=' '{print $2}')
        partprobe >>${LOG_FILE} 2>&1
        tmp_tail_name=$(lsblk -Pb ${EXTEND_98G_DISK}| grep -wE "MOUNTPOINT=\"\"|MOUNTPOINTS=\"\"" | awk -F "\"" '{print $2}' | sort | tail -1 | xargs -i echo "/dev/{}")
        if [ "X${current_extdisk_name}" != "X" ];then
            if [ "${current_extdisk_name}" == "${tmp_tail_name}" ];then
                df -h |grep -w "${tmp_tail_name}"
                if [ $? -ne 0 ];then
                    fdisk ${EXTEND_98G_DISK} <<HERE >> ${LOG_FILE} 2>&1
d

w
HERE
                fi
            else
                log_echo "ERROR" "The disk ${current_extdisk_name} we used is not the last partition of ${EXTEND_98G_DISK}, you need to delete it by yourself.."
            fi
            partprobe
        fi
        log_echo "ERROR" "Check EXTEND_98G_DISK size failed, please check"
        exit 1
    fi
}

function is_need_extend() {
    local is_need_extend_tag=1
    if [ -f "${ATAE_extend_script}" ]; then
        if [ "X${CUSTOM_PATH_SCENE}" == "XTRUE" ]; then
            check_Not_ATAE_extend_disk "total_size"
        else
            check_ATAE_extend_disk "total_size"
        fi
    else
        check_Not_ATAE_extend_disk "total_size"
    fi
    [ $? -eq 1 ] && is_need_extend_tag=0
    return $is_need_extend_tag
}

function find_disk_for_extend_disk() {
    RETURN_VAl=""
    if [ -z "${extend_disk_need_size}" ]; then
        log_echo "INFO" "The EXTEND_DISK is not configured with a number."
        return 1
    fi

    is_need_extend
    if [ $? -ne 0 ]; then
        log_echo "INFO" "The directories of EXTEND_DISK have enough size, not need to extend."
        not_need_check_available_size="YES"
        return 1
    fi
    
    get_extend_disk_symbol
    RETURN_VAl="${get_disk_name}"
    log_echo "INFO" "find extend disk is ${RETURN_VAl}."
}

function extend_disk_for_bare_metal()
{
    get_size_of_key "optlog"
    if [ -z "${get_disk_size}" ]; then
        log_echo "INFO" "Not a bare metal scene for opt log."
        return 0
    fi

    local get_extend_disk_name=""
    df -hT | grep -w "/opt/oss/log" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        update_extend_disk_2_tmp_properties
        preinstall_config_disk_symbol  "vg_log" "${dv_cfg_file}.tmp"
        get_extend_disk_name="${get_disk_name}"
    else
        not_need_check_available_size="YES"
    fi
    update_extend_disk_2_tmp_properties "${get_extend_disk_name}"

}

function check_ext_disk()
{
    local nodetype=$1

    if [ -f /.dockerenv ]; then
        log_echo "INFO" "This is a docker env, skip check extend system disk."
        return 0
    fi

    local need_ext_mount="Yes"
    if [ "X${nodetype}" == "Xuniep" -a "X${UniEP_ExtendDisk_isMounted}" == "XTRUE" ];then
        need_ext_mount="No"
    fi

    partprobe >>${LOG_FILE} 2>&1

    ext_vg_name="vg_ext"
    ext_disk_cfg=${ext_disk_properties}.tmp
    check_non_standard_mirror
    if [ "X${need_ext_mount}" == "XYes" -a "X${AUTO_DISK_MOUNT}" == "XTRUE" ]; then
        local not_need_check_available_size="NO"
        if [ "X${Digitalization_Disk_Param_Tag}" == "XYES" ]; then
            log_echo "INFO" "Digitalization disk scene to find disk for extend disk ."
            find_disk_for_extend_disk
            update_extend_disk_2_tmp_properties "${RETURN_VAl}"
        elif [ "X${pre_install_key}" == "XPreInstall" ]; then
            log_echo "INFO" "Bare metal scene to find disk for extend disk ."
            extend_disk_for_bare_metal  "vg_log" "${dv_cfg_file}.tmp"
        fi
        if [ "${not_need_check_available_size}" == "YES" ]; then
            log_echo "INFO" "The system disk has been extended."
            return 0
        fi
    fi

    ## 非标准OS场景 和 自定义路径挂盘场景
    if [ ! -f "${ATAE_extend_script}" ] || [ "$CUSTOM_PATH_SCENE" == "TRUE" ]; then
        ext_disk_name=${EXTEND_98G_DISK}
        if [ "${AUTO_DISK_MOUNT}" == "TRUE" -a "X${EXTEND_98G_DISK}" != "X" -a "X${need_ext_mount}" == "XYes" -a "X${EXTEND_98G_DISK}" != "X{{EXTEND_98G_DISK}}" ];then
            auto_create_system_disk_part
        else
            check_Not_ATAE_extend_disk "available_size"
        fi
        return 0
    fi

    if [ -f "${ATAE_extend_script}" ];then
        [ "X${need_ext_mount}" == "XNo" -o "${AUTO_DISK_MOUNT}" != "TRUE" -o "X${EXTEND_DISK}" == "X" -o "X${EXTEND_DISK}" == "X{{EXTEND_DISK}}" ] && check_ATAE_extend_disk "${nodetype}" "available_size"
    fi
    return 0
}

function create_pv_and_check_size()
{
    log_echo "INFO" "Begin to check ${local_disk_name} size..."
    pvdisplay ${local_disk_name} > /dev/null 2>&1
    if [ $? -ne 0 ];then
        pvcreate -y ${local_disk_name} >> ${LOG_FILE} 2>&1
        if [ $? -ne 0 ] ; then
            log_echo "ERROR" "Pvcreate ${local_disk_name} failed, please check."
            exit 1
        fi
    fi

    pv_size_num=$(pvdisplay ${local_disk_name} |grep "PV Size"|awk '{print $3}'|sed "s/ //g"|awk -F'.' '{print $1}'|sed 's/<//g')
    pv_size_unit=$(pvdisplay ${local_disk_name} |grep "PV Size"|awk '{print $4}'|sed "s/ //g")
    tmp_least_disk_size=${least_disk_size}
    check_ret=0

    if [ "${pv_size_unit}" == "GB" -o "${pv_size_unit}" == "GiB" ];then
        if [ ${tmp_least_disk_size} -gt ${pv_size_num} ];then
            log_echo "ERROR" "The space of ${local_disk_name} is ${pv_size_num} G, but we need ${tmp_least_disk_size} G at least..."
            check_ret=1
        fi
    else
        if [ "${pv_size_unit}" == "TB" -o "${pv_size_unit}" == "TiB" ];then
            tmp_pv_size_num=$((pv_size_num*1024))
        else
            tmp_pv_size_num=$((pv_size_num/1024))
        fi
        if [ ${tmp_least_disk_size} -gt ${tmp_pv_size_num} ];then
            log_echo "ERROR" "The space of ${local_disk_name} is ${tmp_pv_size_num} G, but we need ${tmp_least_disk_size} G at least..."
            check_ret=1
        fi
    fi

    if [ ${check_ret} -eq 1 ];then
        pvremove ${local_disk_name}
        exit 1
    fi
}

function get_disk_name_and_map_name()
{
    local vg_name="$1"
    local disk_symbol_map_file=${UTILS_PATH}/pre_get_disk_symbol.properties
    if [ ! -f ${disk_symbol_map_file} ]; then
        log_echo "ERROR" "The disk_symbol_map_file=${disk_symbol_map_file} is not exist, please check..."
        return 1
    fi
    local vg_name_index=""
    disk_name=""
    pre_disk_name=""
    while read -r line; do
        vg_name_index=$(echo "${line}" | awk -F"=" '{print $1}')
        if [ -z ${vg_name_index} ]; then
            continue
        fi
        if [ "X${vg_name_index}" == "X${vg_name}" ]; then
            disk_name=$(echo "${line}" | awk -F"=" '{print $2}')
            pre_disk_name=$(echo "${line}" | awk -F"=" '{print $3}')
            break
        fi
    done < $disk_symbol_map_file
    if [ "X${disk_name}" == "X" -o "X${pre_disk_name}" == "X" ]; then
        log_echo "ERROR" "The disk_name=${disk_name} or pre_disk_name=${pre_disk_name} is null, current vg_name=${vg_name}, please check disk_symbol_map_file=${disk_symbol_map_file}."
        return 1
    fi
}

function preinstall_config_disk_symbol()
{
    local tmp_vg_name="$1"
    local source_file_name="$2"
    local is_exit_tag=$3
    get_disk_name_and_map_name "${tmp_vg_name}"
    if [ $? -ne 0 ]; then
        log_echo "ERROR" "Get disk name and map name by vg_name=${tmp_vg_name} failed."
        exit 1
    fi

    if [ "X${Digitalization_Disk_Param_Tag}" == "XYES" ]; then
        log_echo "INFO" "This is digital disk parameter config, will use get_size_of_digital_disk_key to find key=${pre_disk_name}."
        get_size_of_digital_disk_key "${pre_disk_name}"
    else
        get_size_of_key "${pre_disk_name}"
    fi
    if [ $? -eq 0 ]; then
        if [ -z "${get_disk_size}" ]; then
            log_echo "INFO" "The get size of key=${pre_disk_name} is null, return 0."
            return 0
        fi
        log_echo "INFO" "The get size of key=${pre_disk_name} is ${get_disk_size}, begin to find the suitable disk."
    else
        log_echo "ERROR" "The get size of key=${pre_disk_name} is error,maybe is not a number, please check the value..."
        return 1
    fi
    
    local disk_size_add_one=$(($get_disk_size+1))
    if [ "X${Digitalization_Disk_Param_Tag}" == "XYES" ]; then
        get_disk_of_size "${get_disk_size}" "${disk_name}" "${is_exit_tag}"
        if [ $? -ne 0 ]; then
            log_echo "WARN" "Can not find disk part's size=${get_disk_size}."
            return 1
        fi
    else
        get_disk_of_size "${disk_size_add_one}" "${disk_name}" "not_exit"
        if [ $? -ne 0 ]; then
            log_echo "INFO" "Can not find disk part's size=${disk_size_add_one}, begin to find size=${get_disk_size}..."
            get_disk_of_size "${get_disk_size}" "${disk_name}" "${is_exit_tag}"
            if [ $? -ne 0 ]; then
                log_echo "WARN" "Can not find disk part's size=${get_disk_size}."
                return 1
            fi
        fi
    fi
    
    update_config "${source_file_name}" "${disk_name}" "${get_disk_name}"
    if [ $? -ne 0 ]; then
        log_echo "ERROR" "The preinstall_config_disk_symbol of ${tmp_vg_name}=${disk_name}=${pre_disk_name}=${get_disk_name} failed, please check."
        return 1
    fi
    source ${source_file_name}
    log_echo "INFO" "The preinstall_config_disk_symbol of ${tmp_vg_name}=${disk_name}=${pre_disk_name}=${get_disk_name} success."
    return 0
}

function trans_num_2_disk()
{
    local is_exit="$1"

    local_disk_name=""

    ## vg存在条件下，找创建vg的磁盘名称
    vgdisplay ${local_vg_name} >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        local_disk_name=$(pvscan | grep -w "${local_vg_name}" | awk '{print $2}' | awk '{if (NR==1) {print $0}}')
        log_echo "INFO" "The local_vg_name=${local_vg_name} has been created by disk=${local_disk_name}."
        return 0
    fi

    preinstall_config_disk_symbol  "${local_vg_name}" "${dv_cfg_file}.tmp" "${is_exit}"
    if [ $? -ne 0 ];then
        log_echo "WARN" "The get_disk_name=${get_disk_name} is null when tag_key=${tag_key}"
        return 1
    fi
    log_echo "INFO" "The preinstall_config_disk_symbol is excuted of tag_key=${tag_key}"
    if [ -n "${get_disk_size}" ]; then
        local_disk_name=${get_disk_name}
    fi
    return 0
}

function check_local_disk()
{
    mount_point=$1
    local_disk_name=$2
    least_disk_size=$3
    local_vg_name=$4
    local is_exit_tag=$5

    if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ] && [ "${AUTO_DISK_MOUNT}" == "TRUE" ];then
        trans_num_2_disk "$is_exit_tag"
        ## trans_num_2_disk的非0返回值需要外抛
        local return_code=$?
        [ $return_code -ne 0 ] && return ${return_code}
     fi

    log_echo "INFO" "Begin to check ${mount_point}..."
    partprobe >>${LOG_FILE} 2>&1

    if [ "${AUTO_DISK_MOUNT}" == "TRUE" -a "X${local_disk_name}" != "X" -a -z "`echo ${local_disk_name}|grep {{`" ];then
        check_disk_exist "${local_disk_name}"

        lsblk -pl ${local_disk_name} |grep -E "${local_disk_name}[0-9]" >/dev/null 2>&1
        if [ $? -eq 0 ];then
            log_echo "ERROR" "${local_disk_name} has been used for other disk partition, we need all of it, please check..."
            return 1
        fi

        pvdisplay "${local_disk_name}" >/dev/null 2>&1
        if [ $? -eq 0 ];then
            exist_vg_name=$(pvdisplay ${local_disk_name} | grep "VG Name" |awk '{print $3}')
            if [ "X${exist_vg_name}" != "X" -a "X${exist_vg_name}" != "X${local_vg_name}" ];then
                log_echo "ERROR" "${local_disk_name} has been used for other vg ${exist_vg_name}, please check."
                return 1
            fi
        fi

        create_pv_and_check_size
    else
        if [ -z "`echo ${mount_point}|grep {{`" ];then
            umask 022
            mkdir -p ${mount_point}

            available_size=$(df -PBG ${mount_point} |grep "/" |awk '{print $4}'|sed 's/G//g')
            available_size=$((available_size+3))
            if [ ${available_size} -lt ${least_disk_size} ] ; then
                log_echo "ERROR" "The ${mount_point}'s available size is ${available_size}G, but we need ${least_disk_size}G at least..."
                return 1
            fi
        fi
        return 0
    fi
}

function kill_disk_process()
{
    local mount_path=$1
    local TIMES=10
    local i=0

    [ "X${mount_path}" == "X" ] && return 0
    
    while [ $i -lt $TIMES ]
    do
        lsof_d_pids=$(${lsof_cmd} +D ${mount_path} 2>/dev/null|awk '{print $2}'|uniq|grep -vi PID)
        lsof_pids=$(${lsof_cmd} ${mount_path} 2>/dev/null|awk '{print $2}'|uniq|grep -vi PID)
        [ -z "$lsof_d_pids" ] && [ -z "$lsof_pids" ] && break
        
        for pid in $lsof_d_pids
        do
            kill -9 $pid
        done
        
        for pid in $lsof_pids
        do
            kill -9 $pid
        done
        (( i++ ))
        fuser -k ${mount_path} 2>/dev/null
    done
}

function umount_disk_ext()
{
    log_echo "INFO" "Begin to umount extend disk..."
    local lv_tmp=$(grep "lv_opt=" ${ext_disk_cfg} 2>/dev/null)
    if [ -n "$lv_tmp" ];then
        sed -i "/lv_opt=/d" ${ext_disk_cfg}
        echo "${lv_tmp}" >> ${ext_disk_cfg}
        log_echo "INFO" "umount ${OPT_PATH}/oss/log frist."
    fi
    
    for line in `cat ${ext_disk_cfg} 2>/dev/null|sort`
    do
        if [ "X$line" == "X" ];then
            continue
        fi
        path_name=$(echo $line |awk -F'=' '{print $1}')
        lv_name=$(echo $line |awk -F'=' '{print $2}')

        log_echo "INFO" "Begin to umount ${path_name}..."

        if [ "${path_name}" != "swap" ];then
            local subdirectory=$(df -Ph |awk '{print $NF}'|grep "${path_name}/" |grep -v "${path_name}$" | sort -r)
            if [ -n "${subdirectory}" ];then
                for subdir in ${subdirectory};do
                    if [ -z "${subdir}" ];then
                        log_echo "The subdir=${subdir} is null."
                        continue
                    fi

                    kill_disk_process "${subdir}"
                    umount ${subdir}
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "Umount ${subdir} failed, please check..."
                        exit 1
                    fi
                done
            fi

            df -Ph ${path_name} 2>/dev/null| grep -w "/dev/mapper/${vg_Name}-${lv_name}" > /dev/null 2>&1
            if [ $? -eq 0 ];then
                kill_disk_process "${path_name}"
                umount ${path_name}
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Umount ${path_name} failed, please check..."
                    exit 1
                fi

                ## 自定义路径场景，因为目录是代码创建的，需要代码删除
                if [ "X${CUSTOM_PATH_SCENE}" == "XTRUE" ]; then
                    rm -rf ${path_name:?} 2>/dev/null
                fi
            fi
        else
            dm_name=$(readlink -f /dev/${vg_Name}/${lv_name})
            swapon -s |grep -w ${dm_name} > /dev/null 2>&1
            if [ $? -eq 0 ] ; then
                swapoff /dev/${vg_Name}/${lv_name} >> ${LOG_FILE} 2>&1
                if [ $? -ne 0 ] ; then
                    log_echo "ERROR" "Failed to swapoff /dev/${vg_Name}/${lv_name}, please check..."
                    exit 1
                fi
            fi
        fi
    done
}

function remove_vg_ext()
{
    log_echo "INFO" "Begin to remove vg of extend disk..."
    if [ -f "${ext_disk_cfg}" ]; then
        while read line
        do
            if [ "X$line" == "X" ];then
                continue
            fi
            path_name=$(echo $line |awk -F'=' '{print $1}')
            lv_name=$(echo $line |awk -F'=' '{print $2}')

            if [ "${path_name}" != "swap" ];then
                lvdisplay /dev/${vg_Name}/${lv_name} >/dev/null 2>&1
                if [ $? -eq 0 ];then
                    kill_disk_process "${path_name}"
                    umount /dev/${vg_Name}/${lv_name} >> ${LOG_FILE} 2>&1
                    lvremove -f /dev/${vg_Name}/${lv_name} >> ${LOG_FILE} 2>&1
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "Lvremove /dev/${vg_Name}/${lv_name} failed, please check..."
                        exit 1
                    fi
                fi
            fi
        done < ${ext_disk_cfg}
    fi

    vgdisplay ${vg_Name} >/dev/null 2>&1
    if [ $? -eq 0 ];then
        vgchange -an ${vg_Name} >> ${LOG_FILE} 2>&1
        vgremove -f ${vg_Name} >> ${LOG_FILE} 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Vgremove ${vg_Name} failed, please check..."
            exit 1
        fi
    fi
}

function pv_remove()
{
    local pv_disk_name=$1
    if [ "X${pv_disk_name}" != "X" ];then
        pvdisplay ${pv_disk_name} > /dev/null 2>&1
        if [ $? -eq 0 ];then
            pvremove -f ${pv_disk_name}
            if [ $? -eq 0 ];then
                log_echo "INFO" "Remove volume group ${pv_disk_name} success."
            else
                dmsetup status |grep "^${vg_Name}"> /dev/null 2>&1
                if [ $? -eq 0 ];then

                    dm_lv_list=$(dmsetup status |grep "^${vg_Name}" |awk -F':' '{print $1}')
                    for dm_lv in $dm_lv_list
                    do
                        kill -9 `${lsof_cmd} /dev/mapper/${dm_lv}|awk '{if (NR>1){print $2}}'|grep -vi PID`
                        dmsetup remove $dm_lv
                    done
                    pvremove -f ${pv_disk_name}
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "Remove physical volume ${pv_disk_name} fail after dmstatus remove, please check"
                        exit 1
                    fi
                else
                    log_echo "ERROR" "Remove physical volume ${pv_disk_name} fail with nothing in dmsetup status, please check"
                    exit 1
                fi
            fi
        fi
    fi
}

function remove_pv_ext()
{
    log_echo "INFO" "Begin to remove pv of extend disk..."

    current_extdisk_name=$(grep "^current_extdisk_name=" ${dv_cfg_file}.tmp|awk -F'=' '{print $2}')
    if [ "X${current_extdisk_name}" == "X" ];then
        if [ -f "${UTILS_PATH}/mount_extdisk_finish" ];then
            current_extdisk_name=$(grep "^current_extdisk_name=" ${UTILS_PATH}/mount_extdisk_finish|awk -F'=' '{print $2}')
        elif [ -f "${UTILS_PATH}/new_partition_finish" ];then
            current_extdisk_name=$(grep "^current_extdisk_name=" ${UTILS_PATH}/new_partition_finish|awk -F'=' '{print $2}')
        fi
    fi
    if [ "X${current_extdisk_name}" != "X" -a "X${EXTEND_98G_DISK}" != "X" ];then
        echo ${current_extdisk_name} |grep "${EXTEND_98G_DISK}" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Current pv_name of ${current_extdisk_name} doesn't belong to EXTEND_98G_DISK ${EXTEND_98G_DISK}, please check..."
            exit 1
        fi

        pv_remove ${current_extdisk_name}
    else
        pv_remove ${EXTEND_98G_DISK}
    fi

    if [ "X${current_extdisk_name}" != "X" -a "X${EXTEND_98G_DISK}" != "X" ];then
        fdisk -l ${current_extdisk_name} > /dev/null 2>&1
        if [ $? -eq 0 ];then
            if [ "${current_extdisk_name}" != "${EXTEND_98G_DISK}" ];then
                partition_part=$(echo ${current_extdisk_name} |sed  "s|${EXTEND_98G_DISK}||g")
                if [ "X${partition_part}" == "X" ];then
                    log_echo "ERROR" "Get partition number of ${current_extdisk_name} failed, please check..."
                    exit 1
                fi
                partprobe >>${LOG_FILE} 2>&1
                tmp_tail_name=$(lsblk -Pb ${EXTEND_98G_DISK}| grep -wE "MOUNTPOINT=\"\"|MOUNTPOINTS=\"\"" | awk -F "\"" '{print $2}' | sort | tail -1 | xargs -i echo "/dev/{}")
                if [ "${current_extdisk_name}" != "${tmp_tail_name}" ];then
                    log_echo "ERROR" "The disk ${current_extdisk_name} we used is not the last partition of ${disk_name}, you need to delete it by yourself.."
                    return 0
                fi
                df -h |grep -w "${tmp_tail_name}"
                if [ $? -ne 0 ];then
                    fdisk ${EXTEND_98G_DISK} <<HERE
d

w
HERE
                    if [ $? -ne 0 ];then
                        partprobe >>${LOG_FILE} 2>&1
                        sleep 2
                        fdisk -l ${current_extdisk_name}
                        if [ $? -eq 0 ];then
                            log_echo "ERROR" "Delete ${current_extdisk_name} failed, please check..."
                            exit 1
                        fi
                    fi
                fi
                rm -f ${UTILS_PATH}/new_partition_finish
                partprobe
            fi
        fi
    fi
    rm -f /tmp/new_partition_finish
    [ -f "${dv_cfg_file}" ] && sed -i "/current_extdisk_name=/d" ${dv_cfg_file}
    sed -i "/current_extdisk_name=/d" ${dv_cfg_file}.tmp 2>/dev/null
}

function rollback_auto_mount()
{
    local rollback_type="$1"
    if [ "${AUTO_DISK_MOUNT}" == "TRUE" ];then
        if [ "${rollback_type}" == "ext" ]; then
            [ "X${EXTEND_98G_DISK}" != "X" -a "X${EXTEND_98G_DISK}" != "X{{EXTEND_98G_DISK}}" ] && rollback_ext_auto_mount
        else
            sed -i "/^\/dev\/mapper\/$local_vg_name[-].*#DV_Preset$/d" /etc/fstab 2>/dev/null
        fi
        systemctl daemon-reload
        return 0
    fi
}

function rollback_ext_auto_mount()
{
    log_echo "INFO" "rollback extend disk auto mount..."
    while read line
    do
        if [ "X$line" == "X" ];then
            continue
        fi
        local lv_name=$(echo $line |awk -F'=' '{print $2}')
        sed -i "/^\/dev\/mapper\/${vg_Name}-${lv_name}.*#DV_Preset$/d" /etc/fstab 2>/dev/null
    done < ${ext_disk_cfg}
}

function get_extend_disk_by_vg()
{
    RETURN_DISK=""
    local is_dv_operation=(grep "^/dev/mapper/${vg_Name}[-].*\#DV_Preset$" /etc/fstab)
    if [ -z "${is_dv_operation}" ]; then
        log_echo "INFO" "the extend disk vg ${vg_Name} is not dv operation."
        return 1
    fi

    get_system_disk
    local extend_disk_need_size=$(echo "${EXTEND_DISK}" | sed "s#[[:space:]]*\$##")
    if [ "X${USE_SYSTEM_DISK}" == "XYes" ]; then
        local extend_disk_min_need_size=$(($extend_disk_need_size-1))
        local extend_disk_max_need_size=$(($extend_disk_need_size+1))
        extend_disk_min_need_size=$(($extend_disk_min_need_size*1024*1024))
        extend_disk_max_need_size=$(($extend_disk_max_need_size*1024*1024))
        ## fdisk -s /dev/vda1, unit=k
        local system_disk_size=$(fdisk -s "/dev/${system_disk_name}")
        if [ $system_disk_size -gt $extend_disk_min_need_size ] && [ $system_disk_size -lt $extend_disk_max_need_size ]; then
            log_echo "INFO" "The RETURN_DISK of get_extend_disk_by_vg is ${RETURN_DISK}. It is a system disk."
            RETURN_DISK="/dev/${system_disk_name}"
            return 0
        fi
    fi

    local ext_disk=$(pvscan | grep -w "${vg_Name}" | awk '{print $2}'|awk '{if (NR==1) {print $0}}')
    if [ -z "${ext_disk}" ]; then
        log_echo "INFO" "use extend vg ${vg_Name} can not find extend disk."
        return 1
    fi
    RETURN_DISK="${ext_disk}"
    log_echo "INFO" "The RETURN_DISK of get_extend_disk_by_vg is ${RETURN_DISK}."
}

function EXTEND_98G_DISK_number_2_disk()
{
    if [ "${Digitalization_Disk_Param_Tag}" != "YES" ]; then
        log_echo "INFO" "This is not a digitalization disk parameters scenario."
        return 1
    fi
    get_extend_disk_by_vg
    update_extend_disk_2_tmp_properties "${RETURN_DISK}"
}

function lvm_rollback_ext()
{
    log_echo "INFO" "Begin to excute lvm_rollback_ext..."
    vg_Name="vg_ext"
    ext_disk_cfg=${ext_disk_properties}.tmp

    EXTEND_98G_DISK_number_2_disk

    disk_name=${EXTEND_98G_DISK}

    check_non_standard_mirror

    stop_system_service

    umount_disk_ext

    remove_vg_ext

    remove_pv_ext

    rollback_auto_mount "ext"

    start_system_service

    [ -d "${install_path}" -a "X${install_path}" != "X/" ] && rm -rf ${install_path:?}
    rm -f ${UTILS_PATH}/mount_extdisk_finish
}

function umount_disk_local()
{
    log_echo "INFO" "Begin to umount ${mount_point}..."
    df -Ph ${mount_point} 2>/dev/null | grep -w "/dev/mapper/${local_vg_name}-${local_lv_name}" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        kill_disk_process "${mount_point}"
        umount ${mount_point}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Umount ${mount_point} failed, please check..."
            exit 1
        fi
    fi
}

function umount_disk()
{
    local mount_point=$1
    local vgname=$2
    local lvname=$3
    log_echo "INFO" "Begin to umount ${mount_point}..."
    df -Ph ${mount_point} 2>/dev/null | grep -w "/dev/mapper/${vgname}-${lvname}" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        kill_disk_process "${mount_point}"
        umount ${mount_point}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Umount ${mount_point} failed, please check..."
            exit 1
        fi
    fi

    sed -i "/^\/dev\/mapper\/$vgname[-].*#DV_Preset$/d" /etc/fstab
    systemctl daemon-reload
}

function remove_vg_local()
{
    log_echo "INFO" "Begin to remove vg ${local_vg_name}..."
    lvdisplay /dev/${local_vg_name}/${local_lv_name} >/dev/null 2>&1
    if [ $? -eq 0 ];then
        kill_disk_process "${mount_point}"
        umount /dev/${local_vg_name}/${local_lv_name} >> ${LOG_FILE} 2>&1
        local exe_count=0
        while [ $exe_count -lt 5 ]; do
            lvremove -f /dev/${local_vg_name}/${local_lv_name} >> ${LOG_FILE} 2>&1
            if [ $? -ne 0 ]; then
                ((exe_count++))
                log_echo "WARN" "try again to execute [ lvremove -f /dev/${local_vg_name}/${local_lv_name} ] with times=${exe_count}."
                sleep 1
                continue
            fi
            log_echo "INFO" "lvremove ${lv_name} success."
            break
        done
        if [ $exe_count -ge 5 ] ; then
            log_echo "ERROR" "Lvremove /dev/${local_vg_name}/${local_lv_name} is not success, please use command=[ lvremove -f /dev/${local_vg_name}/${local_lv_name} ] to remove the lv, and try to execute the cleanup script again."
            exit 1
        fi
    fi

    vgdisplay ${local_vg_name} >/dev/null 2>&1
    if [ $? -eq 0 ];then
        vgchange -an ${local_vg_name}
        vgremove -f ${local_vg_name}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Vgremove ${local_vg_name} failed, please check..."
            exit 1
        fi
    fi
}

function remove_pv_local()
{
    log_echo "INFO" "Begin to remove pv ${local_disk_name}..."
    pvdisplay ${local_disk_name} > /dev/null 2>&1
    if [ $? -eq 0 ];then
        pvremove -f ${local_disk_name}
        if [ $? -eq 0 ];then
            log_echo "INFO" "Remove volume group ${local_disk_name} success."
        else
            dmsetup status |grep "^${local_vg_name}"> /dev/null 2>&1
            if [ $? -eq 0 ];then

                dm_lv_list=$(dmsetup status |grep "^${local_vg_name}" |awk -F':' '{print $1}')
                for dm_lv in $dm_lv_list
                do
                    kill -9 `${lsof_cmd} /dev/mapper/${dm_lv}|awk '{if (NR>1){print $2}}'|grep -vi PID`
                    dmsetup remove $dm_lv
                done
                pvremove -f ${local_disk_name}
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Remove physical volume ${local_disk_name} fail after dmstatus remove, please check"
                    exit 1
                fi
            else
                log_echo "ERROR" "Remove physical volume ${local_disk_name} fail with nothing in dmsetup status, please check"
                exit 1
            fi
        fi
    fi
}

function lvm_rollback_local()
{
    log_echo "INFO" "Begin to excute lvm_rollback_local..."
    mount_point=$1
    local_disk_name=$2
    local_vg_name=$3
    local_lv_name=$4

    umount_disk_local

    remove_vg_local

    remove_pv_local

    rollback_auto_mount

    rm -f ${UTILS_PATH}/mount_localdisk_finish
}

function add_sop_user()
{
    id ${sop_user_name} > /dev/null 2>&1
    if [ $? -ne 0 ];then
        useradd -g ${sop_group_id} -u ${sop_user_id} -m ${sop_user_name} -d ${sop_user_home} || die "Create user failed! Please check by command \"useradd -g ${sop_group_id} -u ${sop_user_id} -m ${sop_user_name} -d ${sop_user_home}  \""
        touch ${sop_user_home}/sopuser_need_del
        chown ${sop_user_name}:${sop_group_id} ${sop_user_home}/sopuser_need_del
        chmod 600 ${sop_user_home}/sopuser_need_del
    fi
    chage -M 365 ${sop_user_name}
    grep -E "^${sshonly_group}:" /etc/group > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        usermod_add_group ${sshonly_group} ${sop_user_name}
    fi

    grep -E "^wheel:" /etc/group > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        usermod_add_group wheel ${sop_user_name}
    fi
}

function del_sop_user()
{
    killall -9 -u ${sop_user_name} >> $LOG_FILE 2>&1
    killall -9 -u ${sop_user_name} >> $LOG_FILE 2>&1
    userdel -rf ${sop_user_name} >> $LOG_FILE 2>&1

    return 0
}

function del_dvshare_user()
{
    killall -9 -u ${dvshare_user_name} >> $LOG_FILE 2>&1
    killall -9 -u ${dvshare_user_name} >> $LOG_FILE 2>&1
    userdel -rf ${dvshare_user_name} >> $LOG_FILE 2>&1

    return 0
}

function ssh_execute()
{
    local last_arg="${@: -1}"
    if [ "X${is_use_root}" != "Xyes" -a "X${is_extend}" == "XYes" ];then
        su_root_ssh "$1" "$2" "$3" "$4" "$5" "${last_arg}"
        return $?
    fi

    if [ "X${is_use_root}" == "Xyes" -o "X${is_upgrade}" != "XYes" ];then
        local root_pwd="$2"
        local root_pwd=${root_pwd// /}
        local remote_ip="$4"
        local ssh_cmd="$5"
        echo ${last_arg} | grep "dv_user_" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            last_arg=""
        fi
        if [ "X${SUDO_USER}" != "X" ];then
            auto_smart_ssh "${root_pwd}" "-t ${SUDO_USER}@${remote_ip} ${ssh_cmd}" "${last_arg}"
        else
            auto_smart_ssh "${root_pwd}" "-t root@${remote_ip} ${ssh_cmd}" "${last_arg}"
        fi
    else
        if [ "X$(whoami)" == "Xroot" ];then
            su_root_ssh "$1" "$2" "$3" "$4" "$5" "${last_arg}"
        else
            echo ${last_arg} | grep "dv_user_" >/dev/null 2>&1
            if [ $? -ne 0 ];then
                last_arg=""
            fi
            auto_smart_ssh "$2" "ossadm@${4} \"${5}\"" "${last_arg}"
        fi
    fi
}

function get_pub()
{
    local remote_sshpwd=$1
    local remote_rootpwd=$2
    local remote_ip=$3
    local pub_path=$4
    local user_home=$5

    if [ "X${is_use_root}" == "Xyes" -o "X${is_upgrade}" != "XYes" ];then
        remote_sshpwd=${remote_rootpwd}
    fi

    touch ${pub_path}
    chmod 770 ${pub_path}
    ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}" "${command_prefix} cat ${user_home}/.ssh/id_rsa.pub" > ${pub_path}
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Get id_rsa.pub of ${user_name} from ${remote_ip} failed , please check."
        exit 1
    fi
    dos2unix ${pub_path}
    pub=$(grep "ssh-rsa" ${pub_path})
    if [ "X${pub}" == "X" ];then
        log_echo "ERROR" "Get ssh-rsa from ${pub_path} failed, please check"
       chmod 640 ${pub_path}
        exit 1
    fi
    echo ${pub}
    chmod 640 ${pub_path}
}

function send_pub()
{
    local remote_sshpwd=$1
    local remote_rootpwd=$2
    local remote_ip=$3
    local pub=$4
    local user_name=$5
    local user_home=$6
    local pub=$(echo ${pub}|sed 's/\r//g')
    local tmp_pub=$(echo $pub|awk '{print $2}')

    if [ "X${is_use_root}" == "Xyes" -o "X${is_upgrade}" != "XYes" ];then
        if [ "X${user_name}" == "Xdevdata" ];then
            auto_smart_ssh ${remote_rootpwd} "${oper_user}@${remote_ip} mkdir -p  ${user_home}/.ssh"
            auto_smart_ssh ${remote_rootpwd} "${oper_user}@${remote_ip} touch ${user_home}/.ssh/authorized_keys"
        fi
        auto_smart_ssh ${remote_rootpwd} "${oper_user}@${remote_ip} sed \-i \'#${tmp_pub}#d\' ${user_home}/.ssh/authorized_keys"
        if [ "X${SUDO_USER}" != "X" ];then
            auto_smart_ssh ${remote_rootpwd} "${oper_user}@${remote_ip} echo \"${pub}\" |sudo tee -a ${user_home}/.ssh/authorized_keys"
        else
            auto_smart_ssh ${remote_rootpwd} "${oper_user}@${remote_ip} echo \"${pub}\" >> ${user_home}/.ssh/authorized_keys"
        fi
        auto_smart_ssh ${remote_rootpwd} "${oper_user}@${remote_ip} chown ${user_name}:ossgroup ${user_home}/.ssh/authorized_keys"
        if [ "X${user_name}" == "Xdevdata" ];then
            auto_smart_ssh ${remote_rootpwd} "${oper_user}@${remote_ip} chmod 700 ${user_home}/.ssh"
            auto_smart_ssh ${remote_rootpwd} "${oper_user}@${remote_ip} chmod 600 ${user_home}/.ssh/authorized_keys"
        else
            auto_smart_ssh ${remote_rootpwd} "${oper_user}@${remote_ip} chmod 700 ${user_home}/.ssh"
            auto_smart_ssh ${remote_rootpwd} "${oper_user}@${remote_ip} chmod 600 ${user_home}/.ssh/authorized_keys  ${user_home}/.ssh/id_rsa"
            auto_smart_ssh ${remote_rootpwd} "${oper_user}@${remote_ip} chmod 644 ${user_home}/.ssh/id_rsa.pub ${user_home}/.ssh/known_hosts"
        fi

    else
        if [ "X${user_name}" == "Xdevdata" ];then
            ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}"  "mkdir -p  ${user_home}/.ssh"
            ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}"  "touch ${user_home}/.ssh/authorized_keys"
        fi
        ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}"  "${command_prefix} chmod 750 ${user_home}/.ssh;"
        ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}"  "${command_prefix} chmod 660 ${user_home}/.ssh/authorized_keys;"
        ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}"  "${command_prefix} sed -i '#${tmp_pub}#d' ${user_home}/.ssh/authorized_keys"
        ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}"  "echo ${pub} >> ${user_home}/.ssh/authorized_keys"
        ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}"  "${command_prefix} chown ${user_name}:ossgroup ${user_home}/.ssh/authorized_keys"
        ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}"  "${command_prefix} chmod 700 ${user_home}/.ssh;"

        if [ "X${user_name}" == "Xdevdata" ];then
            ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}"  "chmod 640 ${user_home}/.ssh/authorized_keys;"
        else
            ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}"  "${command_prefix} chmod 600 ${user_home}/.ssh/authorized_keys;"
            ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}"  "${command_prefix} chmod 600 ${user_home}/.ssh/id_rsa;"
            ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}"  "${command_prefix} chmod 644 ${user_home}/.ssh/id_rsa.pub"
        fi

        ssh_execute "${upgrade_ssh_user}" "${remote_sshpwd}" "${remote_rootpwd}" "${remote_ip}" "${command_prefix} chmod 644 ${ossuser_home}/.ssh/known_hosts > /dev/null 2>&1"
    fi
}

function build_trust_relation_devdata(){
    local primary_ip=$1
    local secondary_ip=$2
    local result=0
    if [ "X${IS_INSTALL_ICNFG}" != "XYes" ];then
        return 0
    fi
    if [ -f ${UTILS_PATH}/check_devdata_trust_ok ];then
        log_echo "INFO" "devdata's trust relation is ok."
        return 0
    fi

    ## get password and pub key
    if [ "X${secondary_root_pswd}" == "X" ];then
        if [ "X${DVSecondary_pwd}" != "X" ];then
            secondary_remote_pswd=${DVSecondary_pwd}
        elif [ "X${DVSecondary_root_pwd}" != "X" ];then
            secondary_remote_pswd=${DVSecondary_root_pwd}
        elif [ "X${root_password}" != "X" ]; then
            secondary_remote_pswd=${root_password}
        else
            input_pwd "${secondary_ip}" "${oper_user}"
            secondary_remote_pswd="$PwdReturn"
        fi
    else
        secondary_remote_pswd=${secondary_root_pswd}
    fi
    sed -i "s/log_user.*/log_user 1/g" ${UTILS_PATH}/../tools/su_root_ssh.exp
    secondary_ossuser_pub=$(get_pub "${node_ssh_pwd}" "${secondary_remote_pswd}" "${secondary_ip}" "${UTILS_PATH}/secondary_ossuser_pub" "/home/<USER>")
    cat ${UTILS_PATH}/secondary_ossuser_pub|grep ERROR|grep failed > /dev/null
    if [ $? -eq 0 ];then
        log_echo "ERROR" "get_pub from ${secondary_ip} failed"
        exit 1
    fi

    if [ "X${netWorkType}" == "XM" ];then

        primary_ossuser_pub=$(cat /home/<USER>/.ssh/id_rsa.pub)

        mkdir -p /home/<USER>/.ssh
        chmod 700 /home/<USER>/.ssh
        touch /home/<USER>/.ssh/authorized_keys
        chmod 640 /home/<USER>/.ssh/authorized_keys
        chown devdata:ossgroup -R  /home/<USER>/.ssh

        local primary_hostname_tag=$(echo ${primary_ossuser_pub}|sed 's/\r//g'|awk '{print $3}')
        local secondary_hostname_tag=$(echo ${secondary_ossuser_pub}|sed 's/\r//g'|awk '{print $3}')
        sed -i "/${primary_hostname_tag}/d" /home/<USER>/.ssh/authorized_keys
        sed -i "/${secondary_hostname_tag}/d" /home/<USER>/.ssh/authorized_keys

        (echo ${primary_ossuser_pub}|sed 's/\r//g') >> /home/<USER>/.ssh/authorized_keys
        (echo ${secondary_ossuser_pub}|sed 's/\r//g') >> /home/<USER>/.ssh/authorized_keys
    else
        if [ "X${primary_root_pswd}" == "X" ];then
            if [ "X${APP_Primary_root_pwd}" != "X" ];then
                primary_remote_pswd="${APP_Primary_root_pwd}"
            else
                input_pwd "${primary_ip}" "${oper_user}"
                primary_remote_pswd="$PwdReturn"
            fi
        else
            primary_remote_pswd=${primary_root_pswd}
        fi
        primary_ossuser_pub=$(get_pub "${node_ssh_pwd}" "${primary_remote_pswd}" "${primary_ip}" "${UTILS_PATH}/primary_ossuser_pub" "/home/<USER>")
        cat ${UTILS_PATH}/primary_ossuser_pub|grep ERROR|grep failed > /dev/null
        if [ $? -eq 0 ];then
            log_echo "ERROR" "get_pub from ${primary_ip} failed"
            exit 1
        fi
    fi
    sed -i "s/log_user.*/log_user 0/g" ${UTILS_PATH}/../tools/su_root_ssh.exp
    ## send pub key
    if [ "X${netWorkType}" != "XM" ];then
        send_pub "${node_ssh_pwd}" "${primary_remote_pswd}" "${primary_ip}" "${primary_ossuser_pub}" "devdata" "/home/<USER>"
        send_pub "${node_ssh_pwd}" "${primary_remote_pswd}" "${primary_ip}" "${secondary_ossuser_pub}" "devdata" "/home/<USER>"
    fi
    send_pub "${node_ssh_pwd}" "${secondary_remote_pswd}" "${secondary_ip}" "${primary_ossuser_pub}" "devdata" "/home/<USER>"
    send_pub "${node_ssh_pwd}" "${secondary_remote_pswd}" "${secondary_ip}" "${secondary_ossuser_pub}" "devdata" "/home/<USER>"



    ## check trust
    if [ "X${is_use_root}" == "Xyes" -o "X${is_upgrade}" != "XYes" ];then
        if [ "X${netWorkType}" == "XM" ];then
            sh ${tmp_dir}/DV_PreSet_APP.sh checktrust devdata ${primary_ip} ${secondary_ip}
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Checktrust use ossuser from primary_ip to secondary_ip failed"
                result=1
            fi
        else
            auto_smart_ssh "${primary_remote_pswd}" "${oper_user}@${primary_ip} sh ${tmp_dir}/DV_PreSet_APP.sh checktrust devdata ${primary_ip} ${secondary_ip}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Checktrust use ossuser from primary_ip to secondary_ip failed"
                result=1
            fi
        fi

        auto_smart_ssh "${secondary_remote_pswd}" "${oper_user}@${secondary_ip} sh ${tmp_dir}/DV_PreSet_APP.sh checktrust devdata ${secondary_ip} ${primary_ip}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Checktrust use ossuser from primary_ip to secondary_ip failed"
            result=1
        fi
    else
        if [ "X${netWorkType}" == "XM" ];then
            sh ${tmp_dir}/DV_PreSet_APP.sh checktrust devdata ${primary_ip} ${secondary_ip}
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Checktrust use ossuser from primary_ip to secondary_ip failed"
                result=1
            fi
        else
            ssh_execute "${upgrade_ssh_user}" "${node_ssh_pwd}" "${primary_remote_pswd}" "${primary_ip}"  "sh ${tmp_dir}/DV_PreSet_APP.sh checktrust devdata ${primary_ip} ${secondary_ip}"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Checktrust use ossuser from primary_ip to secondary_ip failed"
                result=1
            fi
        fi

        ssh_execute "${upgrade_ssh_user}" "${node_ssh_pwd}" "${secondary_remote_pswd}" "${secondary_ip}" "sh ${tmp_dir}/DV_PreSet_APP.sh checktrust devdata ${secondary_ip} ${primary_ip} "
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Checktrust use ossuser from primary_ip to secondary_ip failed"
            result=1
        fi
    fi

    if [ ${result} -ne 0 ];then
        exit 1
    fi
    touch ${UTILS_PATH}/check_devdata_trust_ok

}

function build_ossuser_trust_relation()
{
    log_echo "INFO" "Begin to check and config ossuser's trust relation ..."
    if [ "X${netWorkType}" == "XM" -a -f ${UTILS_PATH}/check_ossuser_trust_ok ];then
        log_echo "INFO" "ossuser's trust relation is ok."
        return 0
    fi

    primary_ip=$1
    secondary_ip=$2
    user_name="ossuser"
    ossuser_home=/home/<USER>
    
    if [ -z "${primary_root_pswd}" -a ! -z "${root_password}" ];then
        primary_root_pswd="${root_password}"
    fi
    if [ -z "${secondary_root_pswd}" -a ! -z "${root_password}" ];then
        secondary_root_pswd="${root_password}"
    fi
    
    sed -i "s/log_user.*/log_user 1/g" ${UTILS_PATH}/../tools/su_root_ssh.exp
    if [ "X${netWorkType}" == "XM" ];then
        primary_pub=$(${command_prefix} grep "ssh-rsa" ${ossuser_home}/.ssh/id_rsa.pub)
        if [ "X${primary_pub}" == "X" ];then
            log_echo "ERROR" "Get ssh-rsa from ${ossuser_home}/.ssh/id_rsa.pub failed, please check"
            exit 1
        fi
    else
        if [ "X${primary_root_pswd}" == "X" ];then
            input_pwd  "${primary_ip}" ${oper_user}
            primary_root_pswd="$PwdReturn"
        fi
        primary_pub=$(get_pub "${node_ssh_pwd}" "${primary_root_pswd}" "${primary_ip}" "${UTILS_PATH}/primary_pub" "/home/<USER>")
    fi
    if [ "X${is_use_root}" == "Xyes" -o "X${is_upgrade}" != "XYes" ];then
        if [ "X${secondary_root_pswd}" == "X" ];then
            input_pwd "${secondary_ip}" ${oper_user}
            secondary_root_pswd="$PwdReturn"
        fi
    fi
    secondary_pub=$(get_pub "${node_ssh_pwd}" "${secondary_root_pswd}" "${secondary_ip}" "${UTILS_PATH}/secondary_pub" "/home/<USER>")
    sed -i "s/log_user.*/log_user 0/g" ${UTILS_PATH}/../tools/su_root_ssh.exp

    send_pub "${node_ssh_pwd}" "${secondary_root_pswd}" "${secondary_ip}" "${primary_pub}" "ossuser" "/home/<USER>"

    local ret=0
    if [ "X${netWorkType}" == "XM" ];then
        local tmp_pub=$(echo ${secondary_pub}|awk '{print $3}')
        ${command_prefix} sed -i "/${tmp_pub}/d" ${ossuser_home}/.ssh/authorized_keys
        ${command_prefix} bash -c "echo \"${secondary_pub}\" >> ${ossuser_home}/.ssh/authorized_keys"
        ${command_prefix} chown ossuser:ossgroup ${ossuser_home}/.ssh/authorized_keys
        ${command_prefix} chmod 700 ${ossuser_home}/.ssh
        ${command_prefix} chmod 600 ${ossuser_home}/.ssh/authorized_keys
        ${command_prefix} chmod 600 ${ossuser_home}/.ssh/id_rsa
        ${command_prefix} chmod 644 ${ossuser_home}/.ssh/id_rsa.pub
        ${command_prefix} chmod 644 ${ossuser_home}/.ssh/known_hosts

        sh ${tmp_dir}/DV_PreSet_APP.sh checktrust
        ret=$?
    else
        send_pub "${node_ssh_pwd}" "${primary_root_pswd}" "${primary_ip}" "${secondary_pub}"  "ossuser"  "/home/<USER>"
        if [ "X${is_use_root}" == "Xyes" -o "X${is_upgrade}" != "XYes" ];then
            node_ssh_pwd=${primary_root_pswd}
        fi
        ssh_execute "${upgrade_ssh_user}" "${node_ssh_pwd}" "${primary_root_pswd}" "${primary_ip}" "sh ${tmp_dir}/DV_PreSet_APP.sh checktrust"
        ret=$?
    fi

    if [ ${ret} -ne 0 ];then
        log_echo "ERROR" "check the ${user_name} trust relation ${primary_ip} to ${secondary_ip} failed, please check"
        exit 1
    fi

    if [ "X${is_use_root}" == "Xyes" -o "X${is_upgrade}" != "XYes" ];then
        node_ssh_pwd=${secondary_root_pswd}
    fi
    ssh_execute "${upgrade_ssh_user}" "${node_ssh_pwd}" "${secondary_root_pswd}" "${secondary_ip}" "sh ${tmp_dir}/DV_PreSet_APP.sh checktrust"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "check the ${user_name} trust relation ${secondary_ip} to ${primary_ip} failed, please check"
        exit 1
    fi

    touch ${UTILS_PATH}/check_ossuser_trust_ok
}

function config_common_sudo()
{
    unset_chattr

    mkdir -p ${i2k_user_home}/sudoScripts > /dev/null 2>&1
    [ -f "${PreSet_PATH}/sed.sh" ] && cp ${PreSet_PATH}/sed.sh ${i2k_user_home}/sudoScripts
    [ -f "${PreSet_PATH}/modifyCert.sh" ] && cp ${PreSet_PATH}/modifyCert.sh ${i2k_user_home}/sudoScripts
    [ -f "${PreSet_PATH}/handle_hofs.sh" ] && cp ${PreSet_PATH}/handle_hofs.sh ${i2k_user_home}/sudoScripts
    [ -f "${PreSet_PATH}/recover_dbuser.sh" ] && cp ${PreSet_PATH}/recover_dbuser.sh ${i2k_user_home}/sudoScripts

    config_sudo "${i2k_user_name}" "${common_sudo_list}"
    config_sudo "${system_operation_user}" "${sudo_sysomc_list}"
    change_sudo_permission
}

function change_sudo_permission()
{
    log_echo "INFO" "Begin to change sudo files permission."
    sodu_owner_list_other="I2000_gatherinfo.sh|log.sh"

    i2k_user_sudo=$(cat /etc/sudoers | grep "^${i2k_user_name} " | awk -F':' '{i=2;while(i<NF){printf $i":";i++}printf $NF}')
    for cmd in $(echo "$i2k_user_sudo" | sed 's|,|\n|g' | awk -F' ' '{print $1}' | sort | uniq)
    do
        if [ ! -f $cmd ]; then
            continue
        fi

        echo $cmd | grep -E "^${i2k_user_home}/|${i2k_install_path}/|/tmp/" > /dev/null 2>&1
        if [ $? -ne 0 ]; then
            continue
        fi

        file_prp=$(ls -l $cmd)
        echo $cmd | grep -E "$sodu_owner_list_other" > /dev/null 2>&1
        if [ $? -eq 0 -a "x$sodu_owner_list_other" != "x" ]; then
            file_chm=550
            file_og=$(echo $file_prp | awk '{print $4}')
        else
            file_chm=500
            file_og="root"
        fi

        chown root:$file_og $cmd
        chmod $file_chm $cmd
    done

    if [ "X$i2k_user_home" != "X" -a -d ${i2k_user_home} ]; then
        if [ -d ${i2k_user_home}/sudoScripts ];then
            mydos2unix ${i2k_user_home}/sudoScripts > /dev/null 2>&1
            find ${i2k_user_home}/sudoScripts -type f -exec chmod 500 {} +
            chmod 700 ${i2k_user_home}/sudoScripts > /dev/null 2>&1
            chown -R root:root ${i2k_user_home}/sudoScripts > /dev/null 2>&1
            chattr -R +i ${i2k_user_home}/sudoScripts > /dev/null 2>&1
        fi
    fi
}

function unset_chattr()
{
    if [ "X$(whoami)" == "Xroot" ];then
        chattr -i -R ${i2k_install_path}/cie/sudoScripts > /dev/null 2>&1
        chattr -i -R ${i2k_install_path}/I2000 > /dev/null 2>&1
        chattr -i -R $i2k_user_home > /dev/null 2>&1
        chattr -i -R $i2k_user_home > /dev/null 2>&1
        chattr -i -R $i2k_user_home > /dev/null 2>&1
    else
        sudo -u ossuser chattr -i -R ${i2k_install_path}/cie/sudoScripts > /dev/null 2>&1
        sudo -u ossuser chattr -i -R ${i2k_install_path}/I2000 > /dev/null 2>&1
        sudo -u ossuser chattr -i -R $i2k_user_home > /dev/null 2>&1
        sudo -u ossuser chattr -i -R $i2k_user_home > /dev/null 2>&1
        sudo -u ossuser chattr -i -R $i2k_user_home > /dev/null 2>&1
    fi
}

function modify_bootlocal_to_fstab()
{
    sh ${UTILS_PATH}/fixAutoMount.sh
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Fix automount failed, please check"
        exit 1
    fi
    return 0
}

function clean_opasswd()
{
    if [ -f "/etc/security/opasswd" ];then
        sed -i "/^${i2k_user_name}:/d" /etc/security/opasswd
        sed -i "/^ossadm:/d" /etc/security/opasswd
        sed -i "/^${dvshare_user_name}:/d" /etc/security/opasswd
        sed -i "/^${system_operation_user}:/d" /etc/security/opasswd
        sed -i "/^${system_login_user}:/d" /etc/security/opasswd
        sed -i "/^${sftpossuser}:/d" /etc/security/opasswd
        sed -i "/^${sop_user_name}:/d" /etc/security/opasswd
        sed -i "/^${icnfg_devdata_user_name}:/d" /etc/security/opasswd
    fi
}

function backExtDiskInfo()
{
    current_user=$(whoami)
    if [ "X${current_user}" != "Xroot" ];then
        log_echo "INFO" "Begin to backExtDiskInfo but current_user=${current_user}.is not root."
        return 0
    fi
    if [ -f "/.dockerenv"  ];then
        log_echo "INFO" "No need to backExtDiskInfo in docker "
        return 0
    fi
    ext_disk_info=/etc/backup_ext_info
    echo "---- fdisk -l begin ---" > ${ext_disk_info}
    fdisk -l >> ${ext_disk_info}
    echo "---- fdisk -l end---" >> ${ext_disk_info}

    echo "---- lvdisplay begin ---" >> ${ext_disk_info}
    lvdisplay >> ${ext_disk_info}
    echo "---- lvdisplay end---" >> ${ext_disk_info}

    echo "---- vgdisplay begin ---" >> ${ext_disk_info}
    vgdisplay >> ${ext_disk_info}
    echo "---- vgdisplay end---" >> ${ext_disk_info}

    echo "---- pvdisplay begin ---" >> ${ext_disk_info}
    pvdisplay >> ${ext_disk_info} 2>>${LOG_FILE}
    echo "---- pvdisplay end---" >> ${ext_disk_info}

    echo "---- df -h begin ---" >> ${ext_disk_info}
    df -h  >> ${ext_disk_info}
    echo "---- df -h end---" >> ${ext_disk_info}

    echo "---- lsblk begin ---" >> ${ext_disk_info}
    lsblk >> ${ext_disk_info}
    echo "---- lsblk end---" >> ${ext_disk_info}

}

function check_user_pwd_chage()
{
    local user_name="$1"
    if [ -z "${user_name}" ];then
        log_echo "ERROR" "The username=${user_name} is null, please check"
        return 1
    fi

    id $user_name >/dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "The user_name=${user_name} is not exist,not need to check."
        return 0
    fi
    last_change_date=$(grep -w "$user_name:" /etc/shadow |cut -d: -f3)
    current_date=$(expr $(date '+%s') / 86400 )
    max_date=$(grep -w "$user_name:" /etc/shadow |cut -d: -f5|tr -d '[:space:]')
    if [ "X${max_date}" == "X" ];then
        return 0
    fi
    expires_date=$(expr $max_date  + $last_change_date - $current_date )
    if [ $expires_date -lt 0 ];then
        log_echo "ERROR" "${user_name}'s password has expired."
        return 1
    elif [ $expires_date -lt 10 ];then
        log_echo "ERROR" "${user_name}'s password will expired after $expires_date days."
        return 1
    fi
    log_echo  "INFO" "${user_name}'s passwd expires date is normal"
    return 0
}

## user_list="user1,user2,user3...."
function check_user_pwd_expires()
{
    local user_list="$1"
    if [ -z "${user_list}" ];then
        log_echo "ERROR" "The user_list=${user_list} is null, please check"
        return 1
    fi
    local check_ret=0
    for user_name in $(echo "${user_list}"|sed "s/,/ /g" );do
        check_user_pwd_chage "${user_name}"
        check_ret=$(( ${check_ret} + $? ))
    done

    return ${check_ret}
}

#####not use####
function checkHostsConfig()
{
    log_echo "INFO" "start check /etc/hosts config..."
    local hostsCfg="/etc/hosts"
    ## ip1,ip2,ip3....
    local ipList="$1"
    if [ -f ${hostsCfg} ];then
        if [ -z "${ipList}" ];then
            log_echo "INFO" "The ipList=${ipList} is null."
            return 0
        fi

        local markKey="#"
        local isExistMark=""
        for hostIp in $(echo ${ipList} |sed "s/,/ /g");do
            isExistMark=$(cat ${hostsCfg} |grep -v "^[ ]*#" |grep -w "${hostIp}" |grep "${markKey}")
            if [ ! -z "${isExistMark}" ];then
                log_echo "ERROR" "Execute cmd[cat ${hostsCfg} |grep -v \"^[ ]*#\" |grep -w \"${hostIp}\" |grep \"${markKey}\"] failed."
                log_echo "ERROR" "The isExistMark=${isExistMark}. The hostsCfg=${hostsCfg} config hostIp=${hostIp} line has mark \"#\".Cloudsop does not support this configuration.Please check."
                return 1
            fi
        done
    else
        log_echo "INFO" "The hostsCfg=${hostsCfg} is not exist."
    fi

    log_echo "INFO" "Check /etc/hosts config finished."
    return 0
}

function set_user_chage()
{
    import_properties

    log_echo "INFO" "Start to set the Password Expiration Time..."
    local user_array=(${i2k_user_name} ${dvshare_user_name} ${system_operation_user} ${system_login_user} ${system_sftp_user} ${sop_user_name} ${ossadm_user_name} ${icnfg_devdata_user_name})

    for chage_user in ${user_array[*]};do
        id ${chage_user} >/dev/null 2>&1
        if [ $? -eq 0 ];then
            chage --inactive 30 ${chage_user}
            if [ "X${chage_user}" == "X${sop_user_name}" ];then
                chage -M 365 ${chage_user}
            else
                chage -M 99999 -W 7 ${chage_user}
            fi
            chage -m 7 ${chage_user}
        else
            log_echo "INFO" "The user ${chage_user} is not exist."
        fi
    done

    log_echo "INFO" "Set the Password Expiration Time finished."
    return 0
}

function check_user_ssh()
{
    local user_name="$1"
    if [ -z "${user_name}" ];then
        log_echo "INFO" "The user_name=${user_name} is null.of ${FUNCNAME}"
        return 0
    fi

    local user_home=$(cat /etc/passwd|grep "^${user_name}:"|awk -F':' '{print $6}')
    if [ -z "${user_home}" ];then
        log_echo "INFO" "The user_home=${user_home} is null.of ${FUNCNAME}"
        return 0
    fi

    if [ ! -d ${user_home}/.ssh ];then
        log_echo "INFO" "The ${user_home}/.ssh is not exists.of ${FUNCNAME}"
        return 0
    fi

    local dir_access=$(stat -c %A ${user_home}/.ssh)
    local dir_user=$(stat -c %U ${user_home}/.ssh)
    if [ "${dir_user}" != "${user_name}" ];then
        log_echo "ERROR" "The ${user_home}/.ssh dir_user=${dir_user} is not equals ${user_name}.of ${FUNCNAME}"
        exit 1
    fi

    if [ "${dir_access}" == "drwxrwxrwx" ];then
        log_echo "ERROR" "The ${user_home}/.ssh dir_access=${dir_access}.The permission is too large.of ${FUNCNAME}"
        exit 1
    fi

    if [ "$(echo ${dir_access} |sed 's/.*\(.\{3\}\)$/\1/')" == "rwx" ];then
        log_echo "ERROR" "The ${user_home}/.ssh dir_access=${dir_access}.The other user permission is rwx,is too large.of ${FUNCNAME}"
        exit 1
    fi
    log_echo "INFO" "The user_name=${user_name} check finished.of ${FUNCNAME}"
}

function update_maxstartups_sshd_config()
{
    log_echo "INFO" "Start Config /etc/ssh/sshd_config MaxStartups"
    ## 防止/etc/ssh/sshd_config 文件末尾有Match块，直接追加在配置文件末尾会导致ssh服务启动失败
    sed -i "/.*MaxStartups.*/d" /etc/ssh/sshd_config
    local match_line=$(grep -n "^Match User[ ]*" /etc/ssh/sshd_config | awk -F ":" '{print $1}' | sort | head -1)
    if [ -z "${match_line}" ]; then
        echo "MaxStartups 100" >> /etc/ssh/sshd_config
    else
        sed -i "${match_line}i\MaxStartups 100" /etc/ssh/sshd_config
    fi
    return 0
}
function set_swap_off()
{
    swapoff -a
    if [ $? -ne 0 ];then
        log_echo "ERROR" "execute swapoff -a failed; Please check...."
    fi
    for line  in $(cat /etc/fstab|awk '$0!~/^\s*#/{if($3=="swap"){print NR}}')
    do
        sed -i   "${line}s/^/#/" /etc/fstab
    done
}

function config_corebinding_crontab()
{
    log_echo "INFO" "Config crontab to core_binding."
    crontab -l > /tmp/crontab.tmp
    cat /tmp/crontab.tmp |grep "/root/core_binding/core_binding.sh" >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        sed -i "/\/root\/core_binding\/core_binding.sh/d" /tmp/crontab.tmp >/dev/null 2>&1
    fi
    cat /tmp/crontab.tmp |grep "ip neighbor flush" >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        sed -i "/ip neighbor flush/d" /tmp/crontab.tmp >/dev/null 2>&1
    fi
    if [ "X$1" != "Xremove" ];then
        echo "*/5 * * * * /root/core_binding/core_binding.sh >/dev/null 2>&1" >> /tmp/crontab.tmp
    fi
    crontab  /tmp/crontab.tmp >/dev/null 2>&1
    rm  /tmp/crontab.tmp
}

function add_core_binding_timer()
{
    if [ ! -f "/.dockerenv" ];then

        config_sudo sysomc "${sudo_core_binding}"
        config_corebinding_crontab  remove

        mkdir -p /root/core_binding/
        cp -rf  ${UTILS_PATH}/core_binding/core_binding.sh  /root/core_binding/
        chmod 500 /root/core_binding/core_binding.sh

        if [ -f ${UTILS_PATH}/core_binding/core_binding.service ];then
            cp  -f ${UTILS_PATH}/core_binding/core_binding.service  /etc/systemd/system/
            chmod 644 /etc/systemd/system/core_binding.service
        else
            log_echo "ERROR" "${UTILS_PATH}/core_binding.service does not exists"
            exit 1
        fi

        if [ -f ${UTILS_PATH}/core_binding/core_binding.timer ];then
            cp  -f  ${UTILS_PATH}/core_binding/core_binding.timer  /etc/systemd/system/
            chmod 644 /etc/systemd/system/core_binding.timer
        else
            log_echo "ERROR" "${UTILS_PATH}/core_binding.timer does not exists"
            exit 1
        fi
        chown root:root  /root/core_binding/core_binding.sh
        chown root:root  /etc/systemd/system/core_binding.timer
        chown root:root  /etc/systemd/system/core_binding.service

        systemctl daemon-reload
        systemctl start core_binding.service

        systemctl enable core_binding.service

        systemctl start core_binding.timer
        if [ $? -ne 0 ];then
            log_echo "ERROR" "core_binding.service start failed use 'systemctl start core_binding.timer'"
            exit 1
        fi
        systemctl enable core_binding.timer
    fi
}

function remove_core_binding_timer()
{
    if [ ! -f "/.dockerenv" ];then
        if [ -f /etc/systemd/system/core_binding.service ];then
            systemctl stop core_binding.service
            systemctl disable core_binding.service
            rm -f /etc/systemd/system/core_binding.service
        fi

        if [ -f /etc/systemd/system/core_binding.timer ];then
            systemctl stop core_binding.timer
            systemctl disable core_binding.timer
            rm -f  /etc/systemd/system/core_binding.timer
        fi
        systemctl daemon-reload
        delete_old_sudo sysomc "${sudo_core_binding}"
    fi
}

function get_core_binding_tag()
{
    if [ ! -f "/.dockerenv" ];then
        if [ ! -f /root/core_binding/core_binding.sh ];then
            touch ${UTILS_PATH}/core_binding/no_corebinding.tag
        else
            if [ -f /etc/systemd/system/core_binding.service ];then
                touch ${UTILS_PATH}/core_binding/already_have_timer.tag
            fi
            crontab -l|grep core_binding.sh >/dev/null
            if [ $? -eq 0 ];then
                touch ${UTILS_PATH}/core_binding/have_crontab.tag
            fi
        fi
    fi
}

function modify_limits_conf()
{
    if [ -f "/.dockerenv" ];then
        log_echo "INFO" "Containerized scene installation ,no need to config system parameter."
        return 0
    fi

    log_echo "INFO" "Config limits conf."

    sed -i "/^${i2k_user_name}/d" /etc/security/limits.conf
    sed -i "/^root soft nofile 1200000/d" /etc/security/limits.conf
    sed -i "/^root hard nofile 1200000/d" /etc/security/limits.conf
    echo "" >> /etc/security/limits.conf
    echo "${i2k_user_name} - nproc 36384" >> /etc/security/limits.conf
    echo "${i2k_user_name} soft nofile 1200000" >> /etc/security/limits.conf
    echo "${i2k_user_name} hard nofile 1200000" >> /etc/security/limits.conf
    echo "root soft nofile 1200000" >> /etc/security/limits.conf
    echo "root hard nofile 1200000" >> /etc/security/limits.conf

}

function config_ntp_service()
{
    log_echo "INFO" "Begin to config_ntp_service."
    local ntp_service_ip="$1"
    if [ ! -z "${ntp_service_ip}" ];then
        if [ -f "/etc/ntp.conf" ];then
            if [ ! -f "/etc/ntp.conf.bak_dv" ];then
                log_echo "INFO" "Backup ntp.conf to /etc/ntp.conf.bak_dv"
                cp -rf /etc/ntp.conf /etc/ntp.conf.bak_dv
            fi

            line=$(sed -n '/restrict/=' /etc/ntp.conf | tail -n 1)
            if [ -n $line ]; then
                line=$(cat /etc/ntp.conf | wc -l)
            fi
            grep "restrict ::1" /etc/ntp.conf >/dev/null 2>&1
            if [ $? != 0 ]; then
                sed -i "$line a\restrict ::1" /etc/ntp.conf
            fi
            grep "restrict 127.0.0.1" /etc/ntp.conf >/dev/null 2>&1
            if [ $? != 0 ]; then
                sed -i "$line a\restrict 127.0.0.1" /etc/ntp.conf
            fi

            local tmp_ntp_ip=$(echo "${ntp_service_ip}"|sed "s/\./\\\./g")
            ip addr |grep -w "${tmp_ntp_ip}" >> $LOG_FILE 2>&1
            if [ $? -eq 0 ];then
                echo "" >> /etc/ntp.conf
                sed -i "/dv_ntpserver/d" /etc/ntp.conf >> $LOG_FILE 2>&1
                echo "server *********** minpoll 2 maxpoll 4 prefer #dv_ntpserver" >> /etc/ntp.conf
                echo "fudge *********** stratum 10 #dv_ntpserver" >> /etc/ntp.conf
            fi
            rcntpd restart >> $LOG_FILE 2>&1
            systemctl enable ntpd >> $LOG_FILE 2>&1
            ntpq -p >> $LOG_FILE 2>&1
        else
            log_echo "INFO" "/etc/ntp.conf  is not exist,just skip the step ..."
        fi
    fi
    log_echo "INFO" "config_ntp_service end."
    return 0
}

function config_ita_ntpsmart_service()
{
    log_echo "INFO" "Begin to config_ita_ntpsmart_service."

    local external_ntp_server_ip="$1"

    local server_number=0
    for ip in $(echo "${external_ntp_server_ip}"|sed "s/,/ /g");
    do
        echo $ip | grep -E "^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "The NTP_server_IP is illegal. Please check IP:${ip}. "
            exit 1
        fi
        (( server_number++ ))
    done
    if [ ${server_number} -gt 5 ];then
        log_echo "ERROR" "The maximum number of IP addresses is 5 when configured the NTP service."
        exit 1
    fi

    if [ $(echo $external_ntp_server_ip | sed "s/,/\n/g" | sort -n | uniq | wc -l) -ne ${server_number} ];then
        log_echo "ERROR" "The NTP_server_IP $external_ntp_server_ip have duplicate ip addresses. Please check. "
        exit 1
    fi

    if [ -f "/etc/ntp.conf" ];then
        if [ ! -f "/etc/ntp.conf.bak_dv" ];then
            log_echo "INFO" "Backup ntp.conf to /etc/ntp.conf.bak_dv"
            cp -rf /etc/ntp.conf /etc/ntp.conf.bak_dv
        fi
        grep -E "^server ***********" /etc/ntp.conf >/dev/null 2>&1
        if [ $? -ne 0 ]; then
            sed -i /".*server ***********.*"/d /etc/ntp.conf
            sed -i "\$a server ***********" /etc/ntp.conf
        fi
        grep -E "^fudge  *********** stratum 10" /etc/ntp.conf >/dev/null 2>&1
        if [ $? -ne 0 ]; then
            sed -i /".*fudge  *********** stratum 10.*"/d /etc/ntp.conf
            sed -i "\$a fudge  *********** stratum 10" /etc/ntp.conf
        fi
        systemctl status ntpd | grep -wF "active (running)" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            local i=0
            local TIMES=3
            while [ $i -lt $TIMES ]
            do
                systemctl start ntpd
                systemctl status ntpd | grep -wF "active (running)" >/dev/null 2>&1
                if [ $? -eq 0 ];then
                    break
                fi
                (( i++ ))
            done
            if [ ${i} -eq 3 ];then
                log_echo "ERROR" "Can't start NTP service by executing [systemctl status ntpd]. Please check. "
                exit 1
            fi
        fi
    else
        log_echo "ERROR" "/etc/ntp.conf  is not exist, can not start NTP service ita-ntpsmart. "
        exit 1
    fi

    local i=1
    for server_ip in $(echo "${external_ntp_server_ip}"|sed "s/,/ /g");
    do
        sed -i s#^"EXTERNALNTPSERVER_${i}=".*#"EXTERNALNTPSERVER_${i}=${server_ip}"# /var/adm/autoinstall/scripts/ita_ntpsmart/conf/ita-ntpsmart.conf
        (( i++ ))
    done
    sed -i s#^"ONSERVERCLUSTER=".*#"ONSERVERCLUSTER=1"# /var/adm/autoinstall/scripts/ita_ntpsmart/conf/ita-ntpsmart.conf
    systemctl enable ita-ntpsmart.service >> $LOG_FILE 2>&1
    systemctl start ita-ntpsmart >> $LOG_FILE 2>&1
    systemctl status ita-ntpsmart | grep -wF "active (running)" >/dev/null 2>&1
    if [ $? -ne 0 ];then
        local i=0
        local TIMES=3
        while [ $i -lt $TIMES ]
        do
            systemctl start ita-ntpsmart
            systemctl status ita-ntpsmart | grep -wF "active (running)" >/dev/null 2>&1
            if [ $? -eq 0 ];then
                break
            fi
            (( i++ ))
        done
        if [ ${i} -eq 3 ];then
            log_echo "ERROR" "Can't start NTP service by executing [systemctl start ita-ntpsmart]. Please check. "
            exit 1
        fi
    fi

    log_echo "INFO" "config_ita_ntpsmart_service end."
    return 0
}

function persist_system_files_in_container()
{
    if [ ! -f "/.dockerenv" ];then
        log_echo "INFO" "Virtualization scene installation ,no need to persist system files."
        return 0
    fi

    cp -p  /etc/ssh/sshd_config /os_config/sshd_config
    cp -p  /etc/sudoers /os_config/sudoers
    [ -f /etc/snb_sudoers ] && cp -p  /etc/snb_sudoers /os_config/snb_sudoers
    cp -p  /etc/sudoers.d/oss_uniep_sudoers /os_config/oss_uniep_sudoers
    cp -p  /etc/passwd /os_config/passwd
    cp -p  /etc/group  /os_config/group
    cp -p  /etc/shadow /os_config/shadow
    cp -p  /usr/bin/hwcmstool /os_config/hwcmstool
    cp -p  /etc/ssl/certs/HuaweiRootCAsipCRLs.crl  /os_config/HuaweiRootCAsipCRLs.crl
    cp -p  /etc/ssl/certs/HuaweiRootCAsipCRLs_Release.crl  /os_config/HuaweiRootCAsipCRLs_Release.crl
    cp -p  /etc/profile  /os_config/profile
    cp -p  /etc/sudoconfig_version  /os_config/sudoconfig_version
    cp -rp  /etc/uuid  /os_config/uuid
    [ -f "/os_config/root_shadow_bak" ] && rm -f /os_config/root_shadow_bak

    [ -f /etc/profile.d/cie.sh ]  && cp -p /etc/profile.d/cie.sh /os_config/cie.sh
    [ -f /etc/profile.d/cie.csh ]  && cp -p /etc/profile.d/cie.csh  /os_config/cie.csh
    [ -f /etc/profile.d/i2k-env.sh ]  && cp -p /etc/profile.d/i2k-env.sh  /os_config/i2k-env.sh
    [ -f /etc/profile.d/i2k-env.csh ]  && cp -p /etc/profile.d/i2k-env.csh  /os_config/i2k-env.csh

    return 0
}
function check_tmp_mount_option()
{
    mount |awk '{if($3=="/tmp")print $6}'|grep noexec
    if [ $? -eq 0 ];then
        log_echo "ERROR" "/tmp'mount option can't contain noexec,please use mount|grep -w /tmp|grep noexec check"
        return 1
    fi
    if [ "X${is_upgrade}" != "XYes" ];then
        mount |awk '{if($3=="/tmp")print $1}' |grep -w "tmpfs" >/dev/null 2>&1
        if [ $? -eq 0 ];then
            log_echo "ERROR" "the /tmp'mount disk is tmpfs. Please check and modify the mount disk according to the plan instructions."
            return 1
        fi
    fi
    return 0
}

function common_env_enhancement()
{
    chmod 755 /opt
    chmod 755 /usr/lib
    chmod 755 /usr/lib64
    chmod 755 /usr/share
    chmod 1777 /dev/shm
    chmod 644 /etc/ssh/sshd_config
    chown root:root /root
    systemctl start rsyslog
}

function upgrade_env_enhancement()
{
    common_env_enhancement
    chown root:root /opt/signtool
    chmod 700 /opt/signtool
    chown ossadm:ossgroup /tmp/oss
    adjust_sysctl "net.ipv4.ip_local_port_range" "40000 65000"
    if [ -d /opt/zenith/ssl ];then
        chmod 700 /opt/zenith/ssl
        chmod 600 /opt/zenith/ssl/*
    fi
    [ -d /opt/zenith ] && chown dbuser:dbgroup -R /opt/zenith
    check_tmp_oss
}
function install_common_check()
{
    common_env_enhancement
    check_ret=0
    check_tmp_mount_option  || check_ret=1
    check_sudo_config || check_ret=1
    check_inode || check_ret=1
    check_immutable_file || check_ret=1
    [ "${check_ret}" == "1" ] && exit 1
}

function port_common_check()
{
    local process_num;

    if [ "X${listen_ports_check}" == "X" ];then
        return 0
    fi

    if [ "X${netWorkType}" != "XM" ] && [ "X${netWorkType}" != "XO" ];then
        for port in ${listen_ports_check}
        do
            process_num=$(netstat -ntlp | grep -w ${port} | wc -l)
            if [ ${process_num} -gt 0 ];then
                log_echo "ERROR" "There are still ${process_num} remaining processes on port ${port}, please check"
                exit 1
            fi
        done
    fi
}

function upgrade_common_check()
{
    if [ "X${is_upgrade}" != "XYes" ];then
        upgrade_env_enhancement
    fi
    local check_ret=0
    fix_cert_pwd || check_ret=1
    check_tmp_mount_option  || check_ret=1
    if [ "X${is_upgrade}" != "XYes" ];then
        check_sudo_config || check_ret=1
        check_immutable_file || check_ret=1
    fi
    check_inode || check_ret=1
    check_disk_space || check_ret=1
    check_hauwei_cers || check_ret=1
    check_default_gw  || check_ret=1

    while read line; do
        if [ "X${line}" != "X" ];then
            log_echo "ERROR" "'set' command '${line}' found in /etc/profile. Please check."
            check_ret=1
        fi
    done << EOF
    $(grep -E '^\s*set\b' /etc/profile)
EOF

    if [ ${check_ret} -ne 0 ];then
        if ( ! is_empty_param ${UNIEP_NODE_IP} );then
            log_echo "ERROR" "Common check in ${UNIEP_NODE_IP} failed"
        else
            log_echo "ERROR" "Common check in UniEP Node failed"
        fi

        exit 1
    fi
}
function check_sudo_config()
{
    cat /etc/sudoers|grep -e "^[\s]*Defaults\s*secure_path"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "/etc/sudoers'config does not contain 'Defaults secure_path',please check"
        return 1
    fi
    cat /etc/sudoers|grep -e "^[\s]*Defaults\s*use_pty"
    if [ $? -eq 0 ];then
        log_echo "ERROR" "/etc/sudoers'config contain 'Defaults use_pty',please check"
        return 1
    fi
    return 0
}

function check_inode()
{
    local ret=0
    for path_inode in $(df -i|grep -v IUse|awk '{print $1 "####" $5}');
    do 
        path=$(echo $path_inode|awk -F "####" '{print $1}')
        inode_rate=$(echo $path_inode|awk -F "####" '{print substr ($2, 1, length ($2)-1)}')
        if [ "X${inode_rate}" == "X" ];then
            continue
        fi
        if [ ${inode_rate} -ge 95 ];then
            log_echo "ERROR" "${path}'s inode use rate is large than 95%"
            ret=1 
        fi
    done
    return ${ret}
}

function check_disk_space()
{
    local ret=0
    for path_space in $(df -h|grep -v Use|awk '{print $1 "####" $5}');
    do 
        path=$(echo $path_space|awk -F "####" '{print $1}')
        space_rate=$(echo $path_space|awk -F "####" '{print substr ($2, 1, length ($2)-1)}')
        if [ "X${space_rate}" == "X" ];then
            continue
        fi
        if [ ${space_rate} -ge 95 ];then
            log_echo "ERROR" "${path}'s space use rate is large than 95%"
            ret=1 
        fi
    done
    return ${ret}
}

function check_immutable_file()
{
    local ret=0
    local need_check_path="/etc/sudoers /etc/passwd /etc/shadow"
    for tmp_path in ${need_check_path};do
        lsattr ${tmp_path}|grep "^.\{4\}i"
        if [ $? -eq 0 ];then
            log_echo "ERROR" "${tmp_path} is immutable,please check by 'lsattr ${tmp_path}'"
            ret=1
        fi
    done
    return ${ret}
}

function check_tmp_oss()
{   
    for i in `ls /opt/oss/manager/apps/`;
    do 
        if [ ! -d /tmp/oss/manager/$i ] ;then
            mkdir -p /tmp/oss/manager/$i
            chown ossadm:ossgroup  /tmp/oss/manager/$i
        fi
    done
}

function check_ip_ping_bool()
{
    local tmp_ip=$1
    ping_cmd=ping
    echo ${tmp_ip} |grep ":"
    if [ $? -eq 0 ];then
        ping_cmd=ping6
    fi
    ${ping_cmd} -c 1 ${tmp_ip} > /dev/null 2>&1
    if [ $? -ne 0 ];then
        ${ping_cmd} -c 1 ${tmp_ip} > /dev/null 2>&1
        if [ $? -ne 0 ];then
            return 0
        fi
    fi  
    log_echo "ERROR" "${tmp_ip}'s has been used "
    return 1
}
function check_floatip()
{   
    local ret=0
    if [ "X${IPType}" == "X0" ];then
        check_ip_ping_bool ${APP_FloatIP_IPV4}
        [ $? -eq 0 ] && return 0
    elif [ "X${IPType}" == "X1" ];then
        check_ipv6_floatip "${APP_FloatIP_IPV6}" "${APP_FloatIP_IPV6_NETMASK}" "${APP_FloatIP_IPV6_NIC}" "APP_FloatIP_IPV6" || exit 1
        [ $? -eq 0 ] && return 0
    else
        check_ip_ping_bool ${APP_FloatIP_IPV4}
        [ $? -ne 0 ] && ret=1
        check_ipv6_floatip "${APP_FloatIP_IPV6}" "${APP_FloatIP_IPV6_NETMASK}" "${APP_FloatIP_IPV6_NIC}" "APP_FloatIP_IPV6" || exit 1
        [ $? -ne 0 ] && ret=1
        return ${ret}
    fi 
}
function check_hauwei_cers() {
    if [[ ! -f /etc/ssl/certs/HuaweiRootCAsipCRLs.crl  ]];then 
        if [ -f '/.dockerenv'  -a  -f '/os_config/HuaweiRootCAsipCRLs.crl' ];then
            cp /os_config/HuaweiRootCAsipCRLs.crl /etc/ssl/certs/HuaweiRootCAsipCRLs.crl
            return 0
        fi
        log_echo "ERROR" "/etc/ssl/certs/HuaweiRootCAsipCRLs.crl  not exist"
        return 1
    fi 
    return 0
}
function  change_enforce_for_root()
{
    log_echo "INFO" "Begin to change commonpasswd."
    cat /etc/os-release | grep -E "SUSE|Photon" >/dev/null 2>&1
    [ $? -eq 0 ] && is_suse="yes" || is_suse="no"

    if [ "X${is_suse}" == "Xyes" ];then
        commonpasswdfile="common-password"
        commonpasswdfile_local="common-password-pc"
    else
        commonpasswdfile="system-auth"
        commonpasswdfile_local="password-auth-local"
    fi


    sed -i "/pam_pwhistory.so/d" /etc/pam.d/${commonpasswdfile}
    
    local in_line=$(cat -n /etc/pam.d/${commonpasswdfile}|grep -w "pam_unix.so" |head -1 |awk '{print $1}')
    if [ -z "${in_line}" ];then
        in_line=1
    fi
    
    sed -i "${in_line} i password        required        pam_pwhistory.so        enforce_for_root  remember=5" /etc/pam.d/${commonpasswdfile}
    
    cp -rpf /etc/pam.d/${commonpasswdfile_local} /os_config/${commonpasswdfile_local}
    cp -rpf /etc/pam.d/${commonpasswdfile} /os_config/${commonpasswdfile}
    if [ -f /etc/security/opasswd ];then
        cp -rpf /etc/security/opasswd /os_config/opasswd
    fi
    log_echo "INFO" "Change commonpasswd successfully."
}
function  rollback_enforce_for_root()
{
    log_echo "INFO" "Begin to copy passwordfile."
    cp -rpf /os_config/common-password-pc /etc/pam.d/common-password-pc 
    cp -rpf /os_config/common-password /etc/pam.d/common-password 
    cp -rpf /os_config/opasswd /etc/security/opasswd 
    log_echo "INFO" "Copy passwordfile successfully."
}
function adjust_sysctl()
{
    local item=$1
    local value=$2
    sysctl ${item} >/dev/null 2>&1
    if [ $? -ne 0 ];then
        return 0
    fi
    sed -i "/^${item}\s*=/d" /etc/sysctl.conf
    sysctl -w ${item}="${value}" 
    echo  "${item} = ${value}" >> /etc/sysctl.conf
    if [ $? -ne 0 ]; then
        log_echo "ERROR" "Append parameter ${item} to /etc/sysctl.conf failed, please check whether the file /etc/sysctl.conf contains attributes that cannot be modified."
        exit 1
    fi
    sysctl -p /etc/sysctl.conf >/dev/null 2>&1
}

function ssh_get_cpu_processor()
{
    local app_ssh_ip=$1
    local app_node_pwd=$2
    
    if [ -z "${app_ssh_ip}" ];then
        log_echo "ERROR" "The app_ssh_ip=${app_ssh_ip} is null.please check.of ssh_get_cpu_processor"
        exit 1
    fi
    
    if [ -z "${app_node_pwd}" ];then
        log_echo "ERROR" "The app_node_pwd is null.please check.of ssh_get_cpu_processor"
        exit 1
    fi
    
    local isIpv6=$(echo ${app_ssh_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        app_ssh_ip="\[${app_ssh_ip}\]"
    fi
    
    if [ ! -d ${cpu_processor_path} ];then
        mkdir -p ${cpu_processor_path}
    fi
    
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} echo cpu_processor=\`cat /proc/cpuinfo | grep processor | wc -l\`" > ${cpu_processor_path}/${app_ssh_ip}_cpu_processor.txt
    
}

function check_cpu()
{
    log_echo "INFO" "The check_cpu start..."
    
    need_cpu_processor=8
    if [ "X${dv_deploy_scale_size}" == "Xsmall" ];then
        need_cpu_processor=6
    fi
    log_echo "INFO" "The dv_deploy_scale_size=${dv_deploy_scale_size} need_cpu_processor=${need_cpu_processor}"
    ## ${cpu_processor_path}
    if [ ! -d "${cpu_processor_path}" ];then
        log_echo "INFO" "The cpu_processor_path=${cpu_processor_path} is not exists."
        return 0
    fi
    
    ## ${cpu_processor_path}/${ip}_cpu_processor.txt
    local check_result_list=$(ls ${cpu_processor_path} |grep -w ".*._cpu_processor.txt")
    if [ -z "${check_result_list}" ];then
        log_echo "INFO" "The check_result_list=${check_result_list} is null."
        return 0
    fi
    
    local tmp_ip=""
    local tmp_cpu_processor=""
    local check_result=0
    for file in ${check_result_list};do
        tmp_ip=$(echo "${file}" |awk -F'_cpu_processor.txt' '{print $1}')
        tmp_cpu_processor=$(grep  "cpu_processor=" ${cpu_processor_path}/${file} |grep -v "/proc/cpuinfo" |awk -F'cpu_processor=' '{print $2}')
        if [ -z "${tmp_ip}" -o -z "${tmp_cpu_processor}" ];then
            log_echo "INFO" "The tmp_ip=${tmp_ip} or tmp_cpu_processor=${tmp_cpu_processor} is null."
            continue
        fi
        
        tmp_cpu_processor=$(echo "${tmp_cpu_processor}" |sed "s#[[:blank:]|[:cntrl:]]##g")
        isNum "${tmp_cpu_processor}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "The node ${tmp_ip} of tmp_cpu_processor=${tmp_cpu_processor} is not number."
            check_result=1
            continue
        fi
        
        log_echo "INFO" "The tmp_ip=${tmp_ip} and tmp_cpu_processor=${tmp_cpu_processor}."
        if [ ${tmp_cpu_processor} -lt ${need_cpu_processor} ];then
            log_echo "ERROR" "The node ${tmp_ip} get tmp_cpu_processor=${tmp_cpu_processor} less than need_cpu_processor=${need_cpu_processor}."
            check_result=1
            continue
        fi
    done
    
    if [ ${check_result} -eq 1 ];then
        exit 1
    fi
    log_echo "INFO" "The check_cpu finish."
}

function del_deleted_process()
{
    local del_list=$(${lsof_cmd} -w "/opt" | grep -w "(deleted)" | grep -w "ossuser\|ossadm" | awk -F ' ' '{print $2,$3,$(NF-1)}' | sed 's/ /,/g')
    for process in ${del_list}
    do
        local process_pid=$(echo ${process} | awk -F ',' '{print $1}')
        local process_user=$(echo ${process} | awk -F ',' '{print $2}')
        local process_path=$(echo ${process} | awk -F ',' '{print $3}')

        if [ "X${process_user}" == "Xossadm" ];then
            su - ossadm -c "kill -9 ${process_pid}" 2> /dev/null
        elif [ "X${process_user}" == "Xossuser" ];then
            su - ossuser -c "kill -9 ${process_pid}" 2> /dev/null
        fi

        log_echo "INFO" "kill process PID ${process_pid}, name ${process_path}"
    done
    log_echo "INFO" "Clean process with trailer (deleted) finish"
}

function create_loop_device()
{
    local loop_dev=$(losetup -f)
    local loop_num=$(echo ${loop_dev}| awk -F 'loop' '{print $2}')
    log_echo "create_loop_device start.loop_dev=${loop_dev} loop_num=${loop_num}"
    if [ ! -e ${loop_dev} ];then
        log_echo "The ${loop_dev} not exist,create it."
        mknod -m 0660 ${loop_dev} b 7 ${loop_num}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "mknod -m 0660 ${loop_dev} b 7 ${loop_num} failed."
            exit 1
        fi
    else
        log_echo "The ${loop_dev} is exist,not need create it."
    fi
    log_echo "create_loop_device End."
}

function mount_loop_device()
{
    local image_path=$1
    local image_size=$2
    local bssize=$3
    local local_path=$4
    local fstabFile=/etc/fstab
    if [ -f "/.dockerenv" ];then
        log_echo "Containerized scene installation ,no need to config loop_device ."
        return 0
    fi
    log_echo "INFO" "mount_loop_device start..."
    if [ ! -f ${image_path} ];then
        dd if=/dev/urandom of=${image_path} count=${image_size} bs=${bssize}
        chmod 400 ${image_path}
        echo y | mkfs.ext4 ${image_path} >> $LOG_FILE 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "mkfs.ext4 ${image_path} failed."
            exit 1
        fi
    fi
    
    local is_mount=$(mount | grep "[[:blank:]]\+${local_path}[[:blank:]]\+")
    if [ "X${is_mount}" == "X" ]; then
        mkdir -p ${local_path}
        create_loop_device
        echo y | mount -t ext4 -o nosuid -o loop ${image_path} ${local_path} >> $LOG_FILE 2>&1
        if [ $? -ne 0 ];then
            log_echo "INFO" "mount -t ext4 -o nosuid -o loop ${image_path} ${local_path} failed,try again."
            modprobe -r loop
            create_loop_device
            echo y | mount -t ext4 -o nosuid -o loop ${image_path} ${local_path} >> $LOG_FILE 2>&1
            if [ $? -ne 0 ];then
                log_echo "ERROR" "echo y | mount -t ext4 -o nosuid -o loop ${image_path} ${local_path} failed.check log:$LOG_FILE"
                exit 1
            fi
        fi
    fi
    
    local check_fstab=$(grep "[[:blank:]]\+${local_path}[[:blank:]]\+" ${fstabFile})
    if [ ! -z "${check_fstab}" ];then
        mount_point_tmp=$(echo ${local_path} |sed "s#\/#\\\/#g")
        [ "X${mount_point_tmp}" != "X" ] && sed -i "/[ |\t]\+${mount_point_tmp}[ |\t]\+/d" ${fstabFile}
        log_echo "INFO" "clean the ${local_path} from ${fstabFile},ret=$?"
    fi
    log_echo "INFO" "mount_loop_device End."
}


function umount_dir_subdirectory()
{
    local local_path=$1
    log_echo "umount_dir_subdirectory start."
    if [ -f "/.dockerenv" ];then
        log_echo "Containerized scene installation ,no need to config loop_device ."
        return 0
    fi
    
    if [ -z "${local_path}" ];then
        log_echo "The local_path=${local_path} is null."
        return 0
    fi
    
    local subdirectory=$(df -Ph |awk '{print $NF}'|grep ${local_path} |grep -v "${local_path}$")
    if [ -z "${subdirectory}" ];then
        log_echo "The subdirectory=${subdirectory} is null."
        return 0
    fi
    
    for subdir in ${subdirectory};do
        if [ -z "${subdir}" ];then
            log_echo "The subdir=${subdir} is null."
            continue
        fi
        
        kill_disk_process "${subdir}"
        umount ${subdir}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Umount ${subdir} failed, please check..."
            exit 1
        fi
        mount_point_tmp=$(echo ${subdir} |sed "s#\/#\\\/#g")
        sed -i "/[[:blank:]]\+${mount_point_tmp}[[:blank:]]\+/d" /etc/fstab
    done
    log_echo "umount_dir_subdirectory finished."
    return 0
}

function umount_loop_device()
{
    local image_path=$1
    local local_path=$2
    local scene_type=$3
    
    if [ -f "/.dockerenv" ];then
        log_echo "Containerized scene installation ,no need to config loop_device ."
        return 0
    fi
    log_echo "umount_loop_device start. image_path=${image_path} local_path=${local_path} scene_type=${scene_type}"
    mount_point_tmp=$(echo ${local_path} |sed "s#\/#\\\/#g")
    sed -i "/${mount_point_tmp}.*DV_Preset/d" /etc/fstab

    local check_ret=1
    df -Ph "${local_path}" 2>/dev/null |grep "[[:blank:]]\+${local_path}$" 2>/dev/null
    [ $? -eq 0 ] && check_ret=0
    mount | grep "[[:blank:]]\+${local_path}[[:blank:]]\+" 2>/dev/null
    [ $? -eq 0 ] && check_ret=0
    if [ ${check_ret} -eq 0 ];then
        log_echo "INFO" "The local_path-${local_path} is mount, begin to umount it."
        kill_disk_process "${local_path}"
    fi
    
    local loop_name=$(losetup -l | grep -w "${image_path}" |awk -F ' ' '{print $1}')
    log_echo "The loop_name=${loop_name}"
    if [ -e "${loop_name}" ];then
        /usr/sbin/losetup -d ${loop_name}
    fi
    [ -e ${image_path} ] && rm -f ${image_path}
    
    local dv_loop_mounted=$(mount | grep "[[:blank:]]\+${local_path}[[:blank:]]\+" |awk '{print $1}')
    log_echo "The dv_loop_mounted=${dv_loop_mounted}"
    if [ -e "${dv_loop_mounted}" ];then
        echo "${dv_loop_mounted}" |grep "/dv/"
        [ $? -eq 0 ] && rm -f ${dv_loop_mounted}
    fi
    
    umount_remain_disk "${image_path}" "${local_path}"
    # 非标系统卸载loop后，存在loop挂载残留，会导致后续卸载与loop涉及到的目录挂载盘失败
    local loop_mounted=$(mount | grep -w "${image_path}" | grep -w "(deleted)")
    log_echo "The loop_mounted=${loop_mounted}"
    if [ -n "${loop_mounted}" ];then
        local loop_name=$(echo "${loop_mounted}" | awk '{print $4}')
        umount ${loop_name}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Umount ${local_path} failed, please check..."
            exit 1
        fi
    fi
    
    local check_loop_ret=0
    for tmp_i in $(seq 1 5);
    do
        check_loop_ret=0
        log_echo "clean image ${image_path} loop,loop_list=$(/usr/sbin/losetup -l |grep -w ${image_path})"
        /usr/sbin/losetup -l |grep -w "${image_path}" |awk '{print $1}' |xargs -i /usr/sbin/losetup -d {} >> $LOG_FILE 2>&1
        log_echo "delete loop,ret=$?"
        log_echo "The check loop"
        /usr/sbin/losetup -l | grep -w "${image_path}" >> $LOG_FILE 2>&1
        local loop_image_ret=$?
        mount | grep -w "${local_path}" >> $LOG_FILE 2>&1
        local loop_mount_ret=$?
        if [ ${loop_image_ret} -eq 0 -o ${loop_mount_ret} -eq 0 ];then
            log_echo "INFO" "The loop_image_ret=${loop_image_ret} loop_mount_ret=${loop_mount_ret} has equals 0,If the value is 0, the uninstallation is not complete.please check."
            check_loop_ret=1
            sleep 2
        fi
        
        if [ ${check_loop_ret} -eq 0 ];then
            log_echo "The check_loop_ret:${check_loop_ret} Umount loop device successfully"
            break
        fi
    done
    
    if [ ${check_loop_ret} -ne 0 ];then
        log_echo "The check_loop_ret:${check_loop_ret}"
    fi
    log_echo "INFO" "Umount loop device local_path=${local_path} successfully."
}

function umount_remain_disk()
{
    local image_path="$1"
    local path_name="$2"
    log_echo "INFO" "Begin to umount remain disk=${path_name}...."
    local exe_time=1
    local max_exe_times=5
    while [ $exe_time -le $max_exe_times ] ;do
        kill_disk_process "${path_name}"
        umount ${path_name}
        lsblk -Pb | grep -wE "MOUNTPOINT[S]*=\"${path_name}\"" >/dev/null 2>&1
        if [ $? -ne 0 ]; then
            break
        fi
        ((exe_time++))
    done

    if [ $exe_time -gt $max_exe_times ]; then
        log_echo "ERROR" "Umount remain disk=${path_name} failed, please check..."
        exit 1
    fi

    # 非标OS卸载loop后，存在loop挂载残留，会导致后续卸载与loop涉及到的目录挂载盘失败
    local loop_mounted=$(mount | grep -w "${image_path}" | grep -w "(deleted)")
    if [ -n "${loop_mounted}" ];then
        local loop_name=$(echo "${loop_mounted}" | awk '{print $4}')
        umount ${loop_name}
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Umount ${path_name} failed, please check..."
            exit 1
        fi
    fi
}

function get_disk_of_size()
{
    ## size unit G
    local find_size="$1"
    local tag_key="$2"
    local is_exit_tag="$3"
    if [ -z "${find_size}" ];then
        log_echo "ERROR" "The find_size=${find_size} is null.of tag_key=${tag_key}"
        exit 1
    fi
    
    log_echo "INFO" "start get_disk_of_size.of tag_key=${tag_key}"
    get_part_disk
    ## size unit Bytes
    local all_disk_size=$(lsblk -Pb|grep -wE "MOUNTPOINT=\"\"|MOUNTPOINTS=\"\""|grep -w "RM=\"0\""|grep -w "TYPE=\"disk\"\|TYPE=\"part\""|awk '{print $1 $4}'|awk -F'"|"' '{print $2"="$4}')
    if [ ! -z "${has_part_disks}" ];then
        all_disk_size=$(lsblk -Pb|grep -wE "MOUNTPOINT=\"\"|MOUNTPOINTS=\"\""|grep -w "RM=\"0\""|grep -w "TYPE=\"disk\"\|TYPE=\"part\""|grep -v "${has_part_disks}"|awk '{print $1 $4}'|awk -F'"|"' '{print $2"="$4}')
    fi
    log_echo "INFO" "The all_disk_size=${all_disk_size}."

    ## 1 G=1024 MB=1048576 KB=1073741824 Bytes
    ## size unit Bytes
    local find_disk_size=$(( ${find_size} * 1073741824 ))
    local min_disk_size=$(( ${find_disk_size} - 536870912 ))
    local max_disk_size=$(( ${find_disk_size} + 536870912))
    log_echo "INFO" "The find_disk_size=${find_disk_size} min_disk_size=${min_disk_size} max_disk_size=${max_disk_size}."

    get_disk_name=""
    local disk_name=""
    local disk_size=""
    local is_in_use=""
    local cfg_file="${UTILS_PATH}/DV_config.properties.tmp"
    for disk_and_size in ${all_disk_size};
    do
        disk_name=$(echo "${disk_and_size}"|awk -F'=' '{print $1}')
        disk_size=$(echo "${disk_and_size}"|awk -F'=' '{print $2}')

        fdisk -l /dev/${disk_name}  > /dev/null 2>&1
        if [ $? -ne 0 ]; then
            log_echo "INFO" "The disk /dev/${disk_name} is not available."
            continue
        fi

        is_in_use=$(cat ${cfg_file} | grep -w "/dev/${disk_name}")
        if [ ! -z "${is_in_use}" ]; then
            log_echo "INFO" "The /dev/${disk_name} has config at cfg_file=${cfg_file}."
            continue
        fi

        is_in_use=$(lsblk -Pb /dev/${disk_name} |grep -vE "MOUNTPOINT=\"\"|MOUNTPOINTS=\"\"")
        if [ ! -z "${is_in_use}" ];then
            log_echo "INFO" "The /dev/${disk_name} has a mount point, continue."
            continue
        fi

        is_in_use=$(pvscan | grep -w "/dev/${disk_name}" | awk '{print $3}' | grep -w "^VG$")
        if [ ! -z "${is_in_use}" ];then
            log_echo "INFO" "The /dev/${disk_name} has been used to create volume group, continue."
            continue
        fi

        if [ ${disk_size} -eq ${find_disk_size} ];then
            get_disk_name="/dev/${disk_name}"
            log_echo "INFO" "The ${get_disk_name} ${disk_size} is ${find_disk_size}."
            break
        fi

        if [ ${disk_size} -ge ${min_disk_size} -a ${disk_size} -le ${max_disk_size} ];then
            get_disk_name="/dev/${disk_name}"
            log_echo "INFO" "The ${get_disk_name} ${disk_size} is in [${min_disk_size},${max_disk_size}]."
            break
        fi
    done
    if [ -z "${get_disk_name}" ];then
        if [ "X${is_exit_tag}" == "X" ]; then
            log_echo "ERROR" "The get_disk_name=${get_disk_name} is null.of tag_key=${tag_key}"
            exit 1
        fi
        log_echo "WARN" "The get_disk_name=${get_disk_name} is null.of tag_key=${tag_key}"
        return 1
    fi

    log_echo "INFO" "End get_disk_of_size get_disk_name=${get_disk_name} of size=${disk_size} Bytes.of tag_key=${tag_key}"
    return 0
}

function get_size_of_key()
{
    local key="$1"
    if [ -z "${key}" ];then
        log_echo "ERROR" "The key=${key} is null."
        exit 1
    fi
    local Configuration_String_Backup="${Configuration_String}"
    if [ -n "${CURRENT_NODE_TYPE}" ]; then
        local node_config_str=$(cat ${configParametersFile} | grep "^${CURRENT_NODE_TYPE}String" | awk -F "=" '{print $2}')
        if [ -n "${node_config_str}" ]; then
            Configuration_String="${node_config_str}"
        fi
    fi

    local is_change_db_key=0
    if [ "X${key}" == "Xdb_extend" -o "X${key}" == "Xpmdb1_extend" -o "X${key}" == "Xpmdb2_extend" -o "X${key}" == "Xpmdb_extend" ]; then
        is_change_db_key=1
        key=$(echo "${key}" | awk -F"_" '{print $1}')
    fi

    #Configuration_String="xxx uniep_pub:50,sm:50,trace:50,hofs:50,db:867,backuptmp:129,vs:50,fisrv:50,backup:100,sddb:300"
    get_disk_size=""
    log_echo "INFO" "The key=${key}."
    if [ -z "${Configuration_String}" -o "X${Configuration_String}" == "X{{Configuration_String}}" ];then
        log_echo "ERROR" "The Configuration_String=${Configuration_String} is null."
        exit 1
    fi

    echo "${Configuration_String}" |grep -w "${key}" >> ${LOG_FILE} 2>&1
    if [ $? -ne 0 ];then
        Configuration_String="$Configuration_String_Backup"
        echo "${Configuration_String}" |grep -w "${key}" >> ${LOG_FILE} 2>&1
        if [ $? -ne 0 ];then
            log_echo "INFO" "The key=${key} is not in Configuration_String."
            return 0
        fi
    fi

    get_disk_size=$(echo "${Configuration_String}" | grep -ow "${key}:\([0-9]\+\)" | sed "s#.*${key}:\([0-9]\+\).*#\1#g" | tail -1)
    if [ -z "${get_disk_size}" ];then
        log_echo "ERROR" "The key=${key} not found,get_disk_size=${get_disk_size} is null."
        return 1
    fi

    local isNumber=$(echo "${get_disk_size}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        log_echo "ERROR" "The get_disk_size=${get_disk_size} is not number.of key=${key}"
        exit 1
    fi

    if [ "X${key}" == "Xdb" -o "X${key}" == "Xpmdb1" -o "X${key}" == "Xpmdb2" -o "X${key}" == "Xpmdb" ]; then
        if [ $is_change_db_key -eq 0 ]; then
            [ $get_disk_size -gt $min_db_size ] && get_disk_size=$min_db_size
        else
            [ $get_disk_size -gt $min_db_size ] && get_disk_size=$(($get_disk_size-$min_db_size)) || get_disk_size=""
        fi
        if [ -z "${get_disk_size}" ]; then
                return 0
        fi
    fi

    log_echo "INFO" "The key=${key} get_disk_size=${get_disk_size}."
    return 0
}

function get_size_of_digital_disk_key()
{
    local key="$1"
    if [ -z "${key}" ];then
        log_echo "ERROR" "The key=${key} is null."
        exit 1
    fi

    Configuration_String=${digital_disk_param_configstring}

    #Configuration_String="xxx uniep_pub:50,sm:50,trace:50,hofs:50,db:867,backuptmp:129,vs:50,fisrv:50,backup:100,sddb:300"
    get_disk_size=""
    log_echo "INFO" "The key=${key}."
    echo "${Configuration_String}" |grep -w "${key}" >> ${LOG_FILE} 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "The key=${key} is not in Configuration_String."
        return 0
    fi

    get_disk_size=$(echo "${Configuration_String}" | grep -ow "${key}:\([0-9]\+\)" | sed "s#.*${key}:\([0-9]\+\).*#\1#g" | tail -1)
    if [ -z "${get_disk_size}" ];then
        log_echo "ERROR" "The key=${key} not found,get_disk_size=${get_disk_size} is null."
        return 1
    fi

    local isNumber=$(echo "${get_disk_size}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        log_echo "ERROR" "The get_disk_size=${get_disk_size} is not number.of key=${key}"
        exit 1
    fi

    log_echo "INFO" "The key=${key} get_disk_size=${get_disk_size}."
    return 0
}

function update_extend_disk_2_tmp_properties() {
    local extend_disk_val="$1"
    if [ -f "${ATAE_extend_script}" ]; then
        if [ "$CUSTOM_PATH_SCENE" == "TRUE" ]; then
            update_config ${dv_cfg_file}.tmp "EXTEND_98G_DISK" "${extend_disk_val}"
        else
            update_config ${dv_cfg_file}.tmp "EXTEND_DISK" "${extend_disk_val}"
        fi
    else
        update_config ${dv_cfg_file}.tmp "EXTEND_98G_DISK" "${extend_disk_val}"
    fi
    import_properties
}

function del_swap_extend_record()
{
    local vg_name="$1"
    local lv_name="lv_swap"
    local extended_disk_record="/var/adm/autoinstall/logs/extended_disk.record"
    if [ -f "${extended_disk_record}" ]; then
        cat "${extended_disk_record}" | grep -q -E "^${vg_name}[[:space:]]+${lv_name}"
        if [ $? -eq 0 ]; then
            log_echo "INFO" "The ${vg_name} ${lv_name} is in record file=${extended_disk_record}, first extend we will delete the same record."
            sed -i "/${vg_name}[ ]\+${lv_name}[ ].*/d" ${extended_disk_record}
            if [ $? -ne 0 ]; then
                log_echo "ERROR" "Delete the same record from ${extended_disk_record} failed， please check."
                exit 1
            fi
        fi
    fi
}

function find_extend_disk_without_swap() {
    RES_VAL=""

    if [ ! -f "${ATAE_extend_script}" ]; then
         log_echo "INFO" "Not a ATAE extend disk scene."
         return 1
    fi
    get_vg_lv_name_by_dir "${OPT_PATH}"
    if [ $? -ne 0 ]; then
         log_echo "INFO" "Can not get the vg name of mount directory ${OPT_PATH}."
         return 1
    fi
    local system_vg="${log_dir_vg_name}"
    ## grep -v /home is for the difference of eulerosv2r12 and suse os lsblk print.
    local config_mount_dir_list=$(cat ${PreSet_PATH}/tools/ATAEExtend/disk_default.cfg | awk -F "=" '{print $1}' | grep -v "/home" | grep "^/" | sort | tr '\n' ' ')
    log_echo "INFO" "Begin to find system extend disk without swap..."
    local disk_name=""
    for disk_name in $(pvscan | grep -w "${system_vg}" | awk '{print $2}')
    do
        if [ "X$disk_name" == "X" ];then
            continue
        fi
        local mount_dir_list=$(lsblk -pl ${disk_name} | awk '{print $7}' | grep -v "/home" | grep "^/" | sort | tr '\n' ' ')
        if [ "X${config_mount_dir_list}" == "X${mount_dir_list}" ]; then
            log_echo "INFO" "The disk=${disk_name} has been mounted at directory list=${mount_dir_list}."
            del_swap_extend_record "${system_vg}"
            RES_VAL="${disk_name}"
            break
        fi
    done
}

function get_extend_disk_symbol()
{
    get_disk_name=""
    get_system_disk
    if [ "X${USE_SYSTEM_DISK}" == "XYes" ]; then
        log_echo "INFO" "The USE_SYSTEM_DISK=${USE_SYSTEM_DISK} and system_disk_name=${system_disk_name}."
        local extend_disk_min_need_size=$(($extend_disk_need_size-1))
        local extend_disk_max_need_size=$(($extend_disk_need_size+1))
        extend_disk_min_need_size=$(($extend_disk_min_need_size*1024*1024))
        extend_disk_max_need_size=$(($extend_disk_max_need_size*1024*1024))
        ## fdisk -s /dev/vda1, unit=k
        local system_disk_size=$(fdisk -s "/dev/${system_disk_name}")
        if [ $system_disk_size -gt $extend_disk_min_need_size ] && [ $system_disk_size -lt $extend_disk_max_need_size ]; then
            log_echo "INFO" "The system disk size=${system_disk_size} is proper."
            get_disk_name="/dev/${system_disk_name}"
            return 0
        fi
    fi

    find_extend_disk_without_swap
    if [ -n "${RES_VAL}" ]; then
        get_disk_name="${RES_VAL}"
        return 0
    fi

    log_echo "Begin to find disk to extend system disk..."
    update_extend_disk_2_tmp_properties
    get_disk_of_size $extend_disk_need_size "EXTEND_DISK"
}

function trans_disk_size_to_G()
{
    ##disk_size eg: 10T, 2048M, 12G
    local disk_size="$1"
    size_value="0"
    local tmp_info_M=$(echo "${disk_size}"|grep "M" |awk '{print $1}'|awk -F'M' '{print $1}')
    [ ! -z "${tmp_info_M}" ] && size_value=$((tmp_info_M/1024))
    tmp_info_G=$(echo "${disk_size}"|grep "G" |awk '{print $1}'|awk -F'G' '{print $1}')
    [ ! -z "${tmp_info_G}" ] && size_value=${tmp_info_G}
    tmp_info_T=$(echo "${disk_size}"|grep "T" |awk '{print $1}'|awk -F'T' '{print $1}')
    [ ! -z "${tmp_info_T}" ] && size_value=$((tmp_info_T*1024))
    RETURN_SIZE="$size_value"
}

function get_image_disk()
{
   get_image_disk_with_thinpool_size "$@"

   RETURN_THINPOOL_SIZE="50G"
   if [[ "${lv_thinpool_size}" =~ [0-9]+ ]]; then
       RETURN_THINPOOL_SIZE="${lv_thinpool_size}G"
   fi
   log_echo "INFO" "Obtain RETURN_THINPOOL_SIZE=${RETURN_THINPOOL_SIZE}"
}

function get_image_disk_with_thinpool_size()
{
    local image_disk_size="$1"
    local app_ssh_ip="$2"
    local app_node_pwd="$3"
    tmp_image_disk="${image_disk_size}"
    if [ -n "${image_disk_size}" ];then
        lv_thinpool_size="${image_disk_size}"
        ## image_disk_size use a definite number for preinstall scene
        if [ "X${pre_install_key}" == "XPreInstall" ]; then
            image_disk_size=$preinstall_image_disk_size
            ## 预安装使用大小要比规划小1G
            lv_thinpool_size=$(($preinstall_image_disk_size - 1))
        else
            local isNumber=$(echo "${image_disk_size}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
            if [ "${isNumber}" == "No" ];then
                ## 配置盘符, 获取磁盘大小
                local size=$(lsblk -pl ${image_disk} | grep "^${image_disk}" | grep -E "disk|part|lvm"|awk -F' ' '{print $4}')
                trans_disk_size_to_G "$size"
                ## 单位为G
                lv_thinpool_size="${RETURN_SIZE}"
                log_echo "INFO" "The image_disk_size=${image_disk_size} is not number."
                return 0
            fi
        fi
    fi

    local get_image_disk_tmp_path=${TMP_PATH}/get_image_disk
    local local_get_image_disk=${PreSet_PATH}/get_image_disk
    mkdir -p ${local_get_image_disk}
    ip addr |grep -wF "${app_ssh_ip}" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        if [ -f ${get_image_disk_tmp_path} -o -d ${get_image_disk_tmp_path} ];then
            rm -rf ${get_image_disk_tmp_path:?}
        fi

        mkdir -p ${get_image_disk_tmp_path}
        cp -rpf ${PreSet_PATH}/Common/DV_config.properties  ${get_image_disk_tmp_path}
        cp -rpf ${PreSet_PATH}/Common/utils_common.sh  ${get_image_disk_tmp_path}
        cp -rpf ${PreSet_PATH}/Common/utils_os.sh  ${get_image_disk_tmp_path}
        cp -rpf ${PreSet_PATH}/Common/get_image_disk.sh  ${get_image_disk_tmp_path}
        chmod -R 755 ${get_image_disk_tmp_path}
        tmp_image_disk_info=$(sh ${get_image_disk_tmp_path}/get_image_disk.sh "${image_disk_size}")

        tmp_image_disk=$(echo "${tmp_image_disk_info}"|grep "IMAGE_DISK=" |awk -F'=' '{print $2}' |sed "s#[[:blank:]|[:cntrl:]]##g")
        if [ -z "${tmp_image_disk}" ];then
            log_echo "ERROR" "The tmp_image_disk=${tmp_image_disk} is null.check node:${app_ssh_ip} the log file:${get_image_disk_tmp_path}/preset_script.log"
            exit 1
        fi
        log_echo "INFO" "The tmp_image_disk=${tmp_image_disk}"

        if [ -z "${image_disk_size}" ];then
            lv_thinpool_size=$(echo "${tmp_image_disk_info}"|grep "IMAGE_DISK=" |awk -F'=' '{print $3}' |sed "s#[[:blank:]|[:cntrl:]]##g")
            log_echo "INFO" "The tmp_image_disk_size=${lv_thinpool_size}"
        fi

        echo "${tmp_image_disk}" > ${local_get_image_disk}/${app_ssh_ip}_image_disk.txt
        return 0
    fi

    local app_scp_ip="${app_ssh_ip}"
    local isIpv6=$(echo ${app_ssh_ip} |grep ":" |grep -v "]")
    if [ "X${isIpv6}" != "X" ];then
        app_scp_ip="\[${app_ssh_ip}\]"
    fi

    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} rm -rf ${get_image_disk_tmp_path:?}"
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} mkdir -p ${get_image_disk_tmp_path}"
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} chown -R ${oper_user}: ${get_image_disk_tmp_path}"
    auto_scp ${app_node_pwd} ${PreSet_PATH}/Common/DV_config.properties "${oper_user}@${app_scp_ip}:${get_image_disk_tmp_path}"
    auto_scp ${app_node_pwd} ${PreSet_PATH}/Common/utils_common.sh "${oper_user}@${app_scp_ip}:${get_image_disk_tmp_path}"
    auto_scp ${app_node_pwd} ${PreSet_PATH}/Common/utils_os.sh "${oper_user}@${app_scp_ip}:${get_image_disk_tmp_path}"
    auto_scp ${app_node_pwd} ${PreSet_PATH}/Common/get_image_disk.sh "${oper_user}@${app_scp_ip}:${get_image_disk_tmp_path}"
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} chmod -R 755 ${get_image_disk_tmp_path}"
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} sh ${get_image_disk_tmp_path}/get_image_disk.sh ${image_disk_size}" > ${local_get_image_disk}/${app_ssh_ip}_image_disk.txt.tmp
    if [ $? -ne 0 ];then
        log_echo "ERROR" "sh ${get_image_disk_tmp_path}/get_image_disk.sh ${image_disk_size} fai1ed. For more details, check the log=${get_image_disk_tmp_path}/preset_script.log at node:${app_ssh_ip}"
        exit 1
    fi
    tmp_image_disk=$(cat ${local_get_image_disk}/${app_ssh_ip}_image_disk.txt.tmp |grep "IMAGE_DISK=" |awk -F'=' '{print $2}' |sed "s#[[:blank:]|[:cntrl:]]##g")
    if [ -z "${tmp_image_disk}" ];then
        log_echo "ERROR" "The tmp_image_disk=${tmp_image_disk} is null.check node:${app_ssh_ip} the log file:${get_image_disk_tmp_path}/preset_script.log"
        exit 1
    fi
    log_echo "INFO" "The tmp_image_disk=${tmp_image_disk}"

    if [ -z "${image_disk_size}" ];then
        lv_thinpool_size=$(cat ${local_get_image_disk}/${app_ssh_ip}_image_disk.txt.tmp |grep "IMAGE_DISK=" |awk -F'=' '{print $3}' |sed "s#[[:blank:]|[:cntrl:]]##g")
        log_echo "INFO" "The tmp_image_disk_size=${lv_thinpool_size}"
    fi
    echo "${tmp_image_disk}" > ${local_get_image_disk}/${app_ssh_ip}_image_disk.txt
    return 0
}

function get_part_disk()
{
    log_echo "INFO" "Start to get disk has part..."
    local type_equal_disk_list=$(lsblk -Pb | grep -w "TYPE=\"disk\"" | awk '{print $1}'|awk -F "=" '{print $2}' | sed 's#"##g')
    local cur_disk_name=""
    has_part_disks=""
    for cur_disk_name in ${type_equal_disk_list} ; do
        if [ -z "${cur_disk_name}" ]; then
            continue
        fi
        local disk_part_count=$(lsblk -Pb | grep "^NAME=\"${cur_disk_name}.*TYPE=\"part\"" | wc -l)
        if [ $disk_part_count -eq 0 ]; then
            continue
        fi
        if [ -z "${has_part_disks}" ]; then
            has_part_disks="NAME=\"${cur_disk_name}\""
        else
            has_part_disks="${has_part_disks}\|NAME=\"${cur_disk_name}\""
        fi
    done
    log_echo "INFO" "The get disk has part=${has_part_disks}"
}

function get_system_disk()
{
    log_echo "INFO" "Start to get system disk..."
    local type_equal_disk_list=$(lsblk -Pb | grep -w "TYPE=\"disk\"" | awk '{print $1}'|awk -F "=" '{print $2}' | sed 's#"##g')
    local cur_disk_name=""
    system_disk_name=""
    for cur_disk_name in ${type_equal_disk_list}; do
        if [ -z "${cur_disk_name}" ]; then
            continue
        fi
        local grep_result=$(lsblk -Pb /dev/${cur_disk_name} | grep -E  "(MOUNTPOINT|MOUNTPOINTS)=\"/\"$")
        if [ ! -z "${grep_result}" ]; then
            system_disk_name=${cur_disk_name}
            log_echo "INFO" "Get system disk name=/dev/${system_disk_name}"
            return 0
        fi
    done
    log_echo "ERROR" "Can't get system disk name by part which mount at /."
    exit 1
}

function add_dbuser_to_sshonly()
{
    log_echo "INFO" "start add_dbuser_to_sshonly" 
    id dbuser >/dev/null 2>&1 
    if [ $? -eq 0 -a "X$(whoami)" == "Xroot" ];then
        log_echo "INFO" "start add dbuser to sshonly group.check dbuser=$(id dbuser)."
        usermod_add_group sshonly dbuser
        log_echo "INFO" "end check dbuser=$(id dbuser)." 
    fi
    log_echo "INFO" "add_dbuser_to_sshonly End."
}

function set_limit_memory()
{
    local limit_cpu_line=$1
    local deployment_yaml=$2
    local is_integrated_df_and_service=$3
    log_echo "INFO" "set_limit_memory start..."
    log_echo "INFO" "The limit_cpu_line=${limit_cpu_line} deployment_yaml=${deployment_yaml}"
    local limit_memory_line=$((${limit_cpu_line}+1))
    local limit_memory_str=$(sed -n "${limit_memory_line},${limit_memory_line}p" ${deployment_yaml} |grep -w "memory:")
    log_echo "INFO" "The limit_memory_line=${limit_memory_line} limit_memory_str=${limit_memory_str} is_integrated_df_and_service=${is_integrated_df_and_service}"
    ## In the co-deployment scenario, set 96G = 98304Mi
    if [ "X${is_integrated_df_and_service}" == "XYes" ];then
        ## memory: 98304Mi
        local limit_memory=98304
        local memory_total=$(free -m |grep Mem |awk '{print $2}')
        if [ ${memory_total} -lt ${limit_memory} ];then
            limit_memory=${memory_total}
        fi
        
        if [ -z "${limit_memory_str}" ];then
            ## new add to it.
            sed -i "${limit_cpu_line} a {{limit_memory}}" ${deployment_yaml}
            log_echo "INFO" "add {{limit_memory}},ret=$?"
            local limit_memory_new=$(sed -n "${limit_cpu_line},${limit_cpu_line}p" ${deployment_yaml}|sed "s#cpu: \"[0-9]\+\"#memory: ${limit_memory}Mi#")
            sed -i "s#{{limit_memory}}#${limit_memory_new}#g" ${deployment_yaml}
            log_echo "INFO" "modify {{limit_memory}},ret=$?"
        else
            ## modify it.
            sed -i "${limit_memory_line}s#memory: [0-9]\+Mi#memory: ${limit_memory}Mi#" ${deployment_yaml}
            log_echo "INFO" "modify limit memory,ret=$?"
        fi
        
        log_echo "INFO" "The limit_memory_str=$(sed -n "${limit_memory_line},${limit_memory_line}p" ${deployment_yaml})"
        if [ -z "$(grep -w 'memory:' ${deployment_yaml})" ];then
            log_echo "ERROR" "modify_label in helm configure imit memory failed, please check"
            exit 1
        fi
    else
        if [ ! -z "${limit_memory_str}" ];then
            sed -i "${limit_memory_line},${limit_memory_line}d" ${deployment_yaml}
            log_echo "INFO" "del limit_memory_str=${limit_memory_str} in line ${limit_memory_line},ret=$?"
        fi
    fi
    log_echo "INFO" "set_limit_memory End"
}

function set_requests_memory()
{
    local requests_cpu_line=$1
    local deployment_yaml=$2
    local is_integrated_df_and_service=$3
    log_echo "INFO" "set_requests_memory start..."
    log_echo "INFO" "The requests_cpu_line=${requests_cpu_line} deployment_yaml=${deployment_yaml}"
    local requests_memory_line=$((${requests_cpu_line}+1))
    local requests_memory_str=$(sed -n "${requests_memory_line},${requests_memory_line}p" ${deployment_yaml} |grep -w "memory:")
    log_echo "INFO" "The requests_memory_line=${requests_memory_line} requests_memory_str=${requests_memory_str} is_integrated_df_and_service=${is_integrated_df_and_service}"
    ## In the co-deployment scenario, set 40G = 40960Mi
    if [ "X${is_integrated_df_and_service}" == "XYes" ];then
        ## memory: 40960Mi
        local requests_memory=40960
        if [ -z "${requests_memory_str}" ];then
            ## new add to it.
            sed -i "${requests_cpu_line} a {{requests_memory}}" ${deployment_yaml}
            log_echo "INFO" "add {{requests_memory}},ret=$?"
            local requests_memory_new=$(sed -n "${requests_cpu_line},${requests_cpu_line}p" ${deployment_yaml}|sed "s#cpu: \"[0-9]\+\"#memory: ${requests_memory}Mi#")
            sed -i "s#{{requests_memory}}#${requests_memory_new}#g" ${deployment_yaml}
            log_echo "INFO" "modify {{requests_memory}},ret=$?"
        else
            ## modify it.
            sed -i "${requests_memory_line}s#memory: [0-9]\+Mi#memory: ${requests_memory}Mi#" ${deployment_yaml}
            log_echo "INFO" "modify requests memory,ret=$?"
        fi
        
        log_echo "INFO" "The requests_memory_str=$(sed -n "${requests_memory_line},${requests_memory_line}p" ${deployment_yaml})"
        if [ -z "$(grep -w 'memory:' ${deployment_yaml})" ];then
            log_echo "ERROR" "modify_label in helm configure requests memory failed, please check"
            exit 1
        fi
    else
        if [ ! -z "${requests_memory_str}" ];then
            sed -i "${requests_memory_line},${requests_memory_line}d" ${deployment_yaml}
            log_echo "INFO" "del requests_memory_str=${requests_memory_str} in line ${requests_memory_line},ret=$?"
        fi
    fi
    log_echo "INFO" "set_requests_memory End"
}


function update_cpu_check()
{
    local old_cpu="$1"
    local new_cpu="$2"
    local old_memory="$3"
    local new_memory="$4"
    local tag_key="$5"
    
    ## new_memory is null,is Exclusive Scenario need to set old. 
    if [ -z "${new_memory}" ];then
        log_echo "INFO" "The new_memory=${new_memory} is null,need to update ${tag_key} cpu of old.This Exclusive Scenario"
        return 1
    fi
    
    ##Non-exclusive scenario
    if [ ! -z "${old_memory}" ];then
        ## new to new
        log_echo "INFO" "The new_memory=${new_memory} and old_memory=${old_memory} is not null,need to update ${tag_key} cpu of old.This Non-exclusive scenario new to new."
        return 1
    fi
    
    local tmp_old=$(echo "${old_cpu}" |awk -F'"' '{print $2}')
    local tmp_new=$(echo "${new_cpu}" |awk -F'"' '{print $2}')
    if [ "${tmp_old}" == "${tmp_new}" ];then
        log_echo "INFO" "The tmp_old=${tmp_old} and tmp_new=${tmp_new} is same.not need update it.of ${tag_key}."
        return 0
    fi
    
    ## is requests and old and new is not same. 
    if [ "${tag_key}" == "requests" ];then
        log_echo "INFO" "The tag_key=${tag_key} and tmp_old=${tmp_old} and tmp_new=${tmp_new} is not same.need to update ${tag_key} cpu of old."
        return 1
    fi
    
    ## tag_key is limit, and old , new is not same
    processor=$(cat /proc/cpuinfo | grep processor | wc -l)
    if [ "${processor}" != "${tmp_old}" ];then
        log_echo "INFO" "The tmp_old=${tmp_old} and processor=${processor} is not same.not need update it.of ${tag_key}."
        return 1
    fi
    
    log_echo "INFO" "The tmp_old=${tmp_old} and processor=${processor} is same.not need update it.of ${tag_key}."
    return 0
}

function update_cpu_memory()
{
    local tag_key="$1"
    local deployment_yaml="$2"
    local old_deployment_yaml="$3"
    
    if [ -z "${tag_key}" -o -z "${deployment_yaml}" -o -z "${old_deployment_yaml}" ];then
        log_echo "ERROR" "The tag_key=${tag_key} or deployment_yaml=${deployment_yaml} or old_deployment_yaml=${old_deployment_yaml} has null."
        exit 1
    fi
    
    if [ ! -f ${deployment_yaml} ];then
        log_echo "ERROR" "The deployment_yaml=${deployment_yaml} is not exits."
        exit 1
    fi
    
    if [ ! -f ${old_deployment_yaml} ];then
        log_echo "ERROR" "The old_deployment_yaml=${old_deployment_yaml} is not exits."
        exit 1
    fi
    
    log_echo "INFO" "The tag_key=${tag_key} deployment_yaml=${deployment_yaml} old_deployment_yaml=${old_deployment_yaml}"
    ## get old_deployment_yaml
    local old_tag_line=$(grep -n  "${tag_key}" ${old_deployment_yaml}  |cut -d ":" -f 1)
    local old_cpu_line=$((${old_tag_line} + 1))
    local old_memory_line=$((${old_tag_line} + 2))
    
    local old_cpu=$(sed -n "${old_cpu_line},${old_cpu_line}p" ${old_deployment_yaml} |grep -w "cpu:")
    local old_memory=$(sed -n "${old_memory_line},${old_memory_line}p" ${old_deployment_yaml} |grep -w "memory:")
    log_echo "INFO" "The old_tag_line=${old_tag_line} old_cpu_line=${old_cpu_line} old_memory_line=${old_memory_line} old_cpu=${old_cpu} old_memory=${old_memory}"
    
    ## get deployment_yaml
    local new_tag_line=$(grep -n  "${tag_key}" ${deployment_yaml}  |cut -d ":" -f 1)
    local new_cpu_line=$((${new_tag_line} + 1))
    local new_memory_line=$((${new_tag_line} + 2))
    
    local new_cpu=$(sed -n "${new_cpu_line},${new_cpu_line}p" ${deployment_yaml} |grep -w "cpu:")
    local new_memory=$(sed -n "${new_memory_line},${new_memory_line}p" ${deployment_yaml} |grep -w "memory:")
    log_echo "INFO" "The new_tag_line=${new_tag_line} new_cpu_line=${new_cpu_line} new_memory_line=${new_memory_line} new_cpu=${new_cpu} new_memory=${new_memory}"
    
    log_echo "INFO" "update_cpu_memory tag_key=${tag_key} start..."
    update_cpu_check "${old_cpu}" "${new_cpu}" "${old_memory}" "${new_memory}" "${tag_key}"
    if [ $? -eq 1 ];then
        sed -i "${new_cpu_line}s#.*cpu: \"[0-9]\+\"#${old_cpu}#" ${deployment_yaml}
        log_echo "INFO" "update ${tag_key} cpu End.ret=$?"
    fi
    
    if [ -z "${old_memory}" ];then
        log_echo "INFO" "The ${tag_key} of old_memory=${old_memory} is null.not need to update it."
    else
        log_echo "INFO" "update ${tag_key} memory start..."
        local memory_str=$(sed -n "${new_memory_line},${new_memory_line}p" ${deployment_yaml} |grep -w "memory:")
        if [ -z "${memory_str}" ];then
            ## new add to it.
            sed -i "${new_cpu_line} a {{${tag_key}_memory}}" ${deployment_yaml}
            log_echo "INFO" "add {{${tag_key}_memory}},ret=$?"
            sed -i "s#{{${tag_key}_memory}}#${old_memory}#g" ${deployment_yaml}
            log_echo "INFO" "modify {{${tag_key}_memory}},ret=$?"
        else
            ## modify it.
            sed -i "${new_memory_line}s#.*memory: [0-9]\+Mi#${old_memory}#" ${deployment_yaml}
            log_echo "INFO" "modify ${tag_key} memory,ret=$?"
        fi
        log_echo "INFO" "update ${tag_key} memory End."
    fi
    log_echo "INFO" "update_cpu_memory tag_key=${tag_key} End."
}

function fix_cert_pwd()
{
    if [ ! -f /opt/oss/SOP/etc/ssl/er/manifest.json ];then
        return 0
    fi
    sudo -u ossuser test -f /opt/oss/SOP/etc/ssl/er/cert_pwd
    if [ $? -ne 0 ];then
        local keyPass=$(source /opt/oss/manager/bin/engr_profile.sh;sudo -u ossuser cat /opt/oss/SOP/etc/ssl/er/manifest.json|python -c "import sys;import json;manifest=json.loads(sys.stdin.read());print(manifest['filelist']['server_key.pem']['keyPass'])")
        if [ "X${keyPass}" == "X" ];then
            log_echo "ERROR" "get keyPass from  /opt/oss/SOP/etc/ssl/er/manifest.json failed"
            exit 1
        fi
        sudo -u ossuser touch /opt/oss/SOP/etc/ssl/er/cert_pwd
        sudo -u ossuser echo -n "${keyPass}"|sudo -u ossuser tee  /opt/oss/SOP/etc/ssl/er/cert_pwd >/dev/null
        sudo -u ossuser chmod 600  /opt/oss/SOP/etc/ssl/er/cert_pwd

    fi
    return 0
}

function check_default_gw()
{

    default_gw=$(ip -6 route show default|awk '{print $3}'|grep -v dummy |uniq|wc -l)
    if [ ${default_gw} -gt 1 ];then
        log_echo "ERROR" "There are two IPv6 default routes , please delete one by referring to the document. For details about how to handle the error, see section FAQ in the upgrade guide."
        return 1
    fi
    return 0
}
function backup_ntp_conf()
{
    local os_type=$(cat /etc/os-release | grep "Kylin")
    if [ "X${NFV_MODE}" == "XON" ] || [ "X${NFV_MODE}" == "XOFF" -a -n "${os_type}" ]; then
        log_echo "INFO" "backup ntp.conf and chrony.conf before install"
        if [ -f /etc/ntp.conf  ];then
            cp -a /etc/ntp.conf /etc/ntp.conf_dvbak 
            sed -i '/server/d' /etc/ntp.conf
            if [ $? -ne 0 ];then
                log_echo "ERROR" "delete server failed from /etc/ntp.conf"
            fi
            systemctl restart ntpd
        fi 
        if [ -f /etc/chrony.conf  ];then
            cp -a /etc/chrony.conf  /etc/chrony.conf_dvbak 
            sed -i '/server/d' /etc/chrony.conf
            if [ $? -ne 0 ];then
                log_echo "ERROR" "delete server failed from /etc/chrony.conf"
            fi
            systemctl restart chronyd
        fi
    fi
}

function recover_ntp_conf()
{
    local os_type=$(cat /etc/os-release | grep "Kylin")
    if [ "X${NFV_MODE}" == "XON" ] || [ "X${NFV_MODE}" == "XOFF" -a -n "${os_type}" ]; then
        log_echo "INFO" "recover ntp.conf and chrony.conf after uninstall"
        [ -f /etc/ntp.conf_dvbak  ] && mv  /etc/ntp.conf_dvbak  /etc/ntp.conf
        [ -f  /etc/chrony.conf_dvbak  ] && mv   /etc/chrony.conf_dvbak  /etc/chrony.conf
    fi
    return 0
}

function delete_uniep_ntp()
{
    app_ip=$1
    app_scp_ip=$2

    su - ossadm -c "scp -P ${i2k_ssh_port} -o StrictHostKeyChecking=no  ${UTILS_PATH}/input_ntp.json  ossadm@${app_scp_ip}:/tmp"
    su - ossadm -c "scp -P ${i2k_ssh_port} -o StrictHostKeyChecking=no  ${UTILS_PATH}/input_chrony.json  ossadm@${app_scp_ip}:/tmp"
    su - ossadm -c "ssh -o StrictHostKeyChecking=no  ossadm@${app_ip}  /usr/local/osconfig/os/bin/modifyntp.sh  -input /tmp/input_ntp.json -output /tmp/output.json" 
    if [ $? -ne 0 ];then
        log_echo "ERROR" "delete ntp ip at ${app_ip} failed"
        exit 1
    fi
    if [ -f "/etc/chrony.conf" ];then
        su - ossadm -c "ssh -o StrictHostKeyChecking=no  ossadm@${app_ip}  /usr/local/osconfig/os/bin/modifyntp.sh  -input /tmp/input_chrony.json -output /tmp/output.json"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "delete ntp ip at ${app_ip} failed"
            exit 1
        fi
    fi
}
function add_ntp()
{
    if [ -f "/.dockerenv" ];then
        return 0
    fi
    
    if [ "X${NFV_MODE}" == "XON" ];then
        if [ "X${IPType}" == "X0" -o "X${IPType}" == "Xipv4" ];then
            app1_ip=$APP_Primary_IPV4
            app1_scp_ip=$APP_Primary_IPV4
            app2_ip=$APP_Secondary_IPV4
            app2_scp_ip=$APP_Secondary_IPV4
            uniep_ip=${UNIEP_IPV4}
        else
            app1_ip=$APP_Primary_IPV6
            app1_scp_ip="[$APP_Primary_IPV6]"
            app2_ip=$APP_Secondary_IPV6
            app2_scp_ip="[$APP_Secondary_IPV6]"
            uniep_ip=${UNIEP_MGR_IPV6}
        fi

        echo  "{\"ntpservers\": [{\"no\": \"0\",\"ip\": \"${uniep_ip}\",\"isprefer\": \"yes\",\"action\": \"delete\"},{\"no\": \"0\",\"ip\": \"***********\",\"isprefer\": \"no\",\"action\": \"add\"}],\"ntptype\": \"ntpd\"}" > ${UTILS_PATH}/input_ntp.json
        chown ossadm:ossgroup ${UTILS_PATH}/input_ntp.json

        echo  "{\"ntpservers\": [{\"no\": \"0\",\"ip\": \"${uniep_ip}\",\"isprefer\": \"yes\",\"action\": \"delete\"},{\"no\": \"0\",\"ip\": \"***********\",\"isprefer\": \"no\",\"action\": \"add\"}],\"ntptype\": \"chrony\"}" > ${UTILS_PATH}/input_chrony.json
        chown ossadm:ossgroup ${UTILS_PATH}/input_chrony.json

        delete_uniep_ntp ${app1_ip} ${app1_scp_ip}
        delete_uniep_ntp ${app2_ip} ${app2_scp_ip}
        
        su - ossadm -c "/opt/oss/manager/apps/UniEPAgent/bin/uniep_tool osconfig-ntp reconfig_ntp"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "su - ossadm -c \"/opt/oss/manager/apps/UniEPAgent/bin/uniep_tool osconfig-ntp reconfig_ntp\" fai1ed."
            exit 1
        fi
        grep -Fw  "${app1_ip}"  /etc/ntp.conf   /etc/chrony.conf
        if [ $? -ne 0 ];then
            echo "{\"actionkey\": \"addNTP\", \"datalist\": [{\"NTPAddress\": \"${app1_ip}\", \"authMode\": \"default\", \"encryptAlgorism\": \"\", \"keyIndex\": \"\", \"keyValue\": \"\", \"prefer\": \"true\"}]}" > ${UTILS_PATH}/add_ntp.json
            chown ossadm:ossgroup  ${UTILS_PATH}/add_ntp.json
            su - ossadm -c "/opt/oss/manager/tools/sysmt/addntp.sh -input ${UTILS_PATH}/add_ntp.json"  
            if [ $? -ne 0 ];then
                log_echo "ERROR" "add ntp ${app1_ip} failed"
                exit 1
            fi
        fi
        grep -Fw  "${app2_ip}"  /etc/ntp.conf   /etc/chrony.conf
        if [ $? -ne 0 ];then
            echo "{\"actionkey\": \"addNTP\", \"datalist\": [{\"NTPAddress\": \"${app2_ip}\", \"authMode\": \"default\", \"encryptAlgorism\": \"\", \"keyIndex\": \"\", \"keyValue\": \"\", \"prefer\": \"false\"}]}" > ${UTILS_PATH}/add_ntp.json
            chown ossadm:ossgroup  ${UTILS_PATH}/add_ntp.json
            su - ossadm -c "/opt/oss/manager/tools/sysmt/addntp.sh -input ${UTILS_PATH}/add_ntp.json"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "add ntp ${app2_ip} failed"
                exit 1
            fi
        fi
    fi
    return 0
}

function umount_dir()
{
    local dir_mount_point="$1"
    local local_vg_name="$2"
    local local_lv_name="$3"
    log_echo "INFO" "Begin to umount ${dir_mount_point}..."
    df -Ph ${dir_mount_point} 2>/dev/null | grep -w "/dev/mapper/${local_vg_name}-${local_lv_name}" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        kill_disk_process "${dir_mount_point}"
        ps -ef|grep "${dir_mount_point}" |grep -v grep >> ${LOG_FILE}
        umount ${dir_mount_point}
        if [ $? -ne 0 ];then
            log_echo "INFO" "Umount dir_mount_point=${dir_mount_point} try again."
            sleep 1
            ps -ef|grep "${dir_mount_point}" |grep -v grep >> ${LOG_FILE}
            kill_disk_process "${dir_mount_point}"
            umount ${dir_mount_point}
            if [ $? -ne 0 ];then
                ps -ef|grep "${dir_mount_point}" |grep -v grep >> ${LOG_FILE}
                ${lsof_cmd} +D ${dir_mount_point} >> ${LOG_FILE}
                ${lsof_cmd} ${dir_mount_point} >> ${LOG_FILE}
                log_echo "ERROR" "Umount ${dir_mount_point} failed, please check..."
                return 1
            fi
        fi
    fi
    log_echo "INFO" "Umount dir_mount_point=${dir_mount_point} success."
}

function lv_remove()
{
    local lv_name="$1"
    log_echo "INFO" "Begin to remove lv ${lv_name}..."
    lvdisplay ${lv_name} >/dev/null 2>&1
    if [ $? -eq 0 ];then
        umount ${lv_name} >> ${LOG_FILE} 2>&1
        lvremove -f ${lv_name} >> ${LOG_FILE} 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Lvremove ${lv_name} failed, please check..."
            return 1
        fi
    fi
}

function mkfs_logic_volume()
{
    local lv_name="$1"
    local exe_count=0
    while [ $exe_count -lt 5 ]; do
        echo y | mkfs -t ext4 ${lv_name} >> ${LOG_FILE} 2>&1
        if [ $? -ne 0 ]; then
            ((exe_count++))
            log_echo "WARN" "mkfs LV ${lv_name} failed, try again times=${exe_count}."
            sleep 1
            continue
        fi
        log_echo "INFO" "mkfs LV ${lv_name} success."
        break
    done
    if [ $exe_count -ge 5 ] ; then
        log_echo "ERROR" "mkfs LV ${lv_name} failed, please check..."
        exit 1
    fi
}

function log_dir_lv_remove() {
    local mount_point="$1"
    local log_dir_vg_name="$2"
    local log_dir_lv_name="$3"
    local disk_name="$4"
    log_echo "INFO" "Begin to remove log directory logic volume..."
    check_log_disk_has_extend
    if [ $? -ne 0 ]; then
        return 1
    fi

    log_echo "INFO" "The ${mount_point} is extended, need to reduce the lvm."
    umount_dir "${mount_point}" "${log_dir_vg_name}" "${log_dir_lv_name}" || exit 1
    lv_remove "/dev/${log_dir_vg_name}/${log_dir_lv_name}" || exit 1
    pvscan | grep -w "^[ ]*PV[ ]*${disk_name}[ ]*VG[ ]*${log_dir_vg_name}" >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        vgreduce -f ${log_dir_vg_name} ${disk_name} >/dev/null 2>&1
        if [ $? -ne 0 ]; then
            log_echo "ERROR" "Vgreduce -f ${log_dir_vg_name} ${disk_name} failed, please check."
            exit 1
        fi
        pv_remove "${disk_name}"
    fi
    return 0
}

function auto_mount_log_dir()
{
    local mount_point="$1"
    local log_dir_vg_name="$2"
    local log_dir_lv_name="$3"
    local log_dir_default_size="$4"

    log_echo "INFO" "Begin to mount ${mount_point} for lv size=${log_dir_default_size}"

    local lv_name="/dev/${log_dir_vg_name}/${log_dir_lv_name}"
    lvdisplay ${lv_name} >/dev/null 2>&1
    if [ $? -ne 0 ];then
        lvcreate -y -L ${log_dir_default_size}G -n ${log_dir_lv_name} ${log_dir_vg_name} >> ${LOG_FILE} 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Create LV ${log_dir_lv_name} failed, please check..."
            exit 1
        fi

        df -Ph | grep -w "/dev/mapper/${log_dir_vg_name}-${log_dir_lv_name}" >> ${LOG_FILE} 2>&1
        if [ $? -eq 0 ];then
            mount_dir=$(df -Ph | grep -w "/dev/mapper/${log_dir_vg_name}-${log_dir_lv_name}"|awk -F' ' '{print $NF}')
            kill_disk_process "${mount_dir}"
            umount /dev/mapper/${log_dir_vg_name}-${log_dir_lv_name}
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Umount /dev/mapper/${log_dir_vg_name}-${log_dir_lv_name} failed before mkfs, please check..."
                exit 1
            fi
        fi

        mkfs_logic_volume "${lv_name}"
    fi

    umask 022
    mkdir -p ${mount_point}

    df -Ph ${mount_point} | grep -w "/dev/mapper/${log_dir_vg_name}-${log_dir_lv_name}" > /dev/null 2>&1
    if [ $? -ne 0 ] ; then
        kill_disk_process "${mount_point}"
        mount ${lv_name} ${mount_point}
        if [ $? -ne 0 ]; then
            log_echo "INFO" "Maybe lv_name=${lv_name} is not mkfs.ext4, try to mkfs.ext4 ${lv_name}."
            echo y | mkfs -t ext4 ${lv_name} >> ${LOG_FILE} 2>&1
            mount ${lv_name} ${mount_point}
        fi

        if [ $? -ne 0 ] ; then
            df -Ph ${mount_point} | grep -w "/dev/mapper/${log_dir_vg_name}-${log_dir_lv_name}" > /dev/null 2>&1
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Mount ${lv_name} to ${mount_point} failed, please check..."
                exit 1
            else
                df -Ph ${mount_point} | grep -w "/dev/mapper/${log_dir_vg_name}-${log_dir_lv_name}" | grep -w "64Z" > /dev/null 2>&1
                if [ $? -eq 0 ];then
                    log_echo "ERROR" "Mount ${lv_name} to ${mount_point} get 64Z, please check..."
                    exit 1
                else
                    log_echo "INFO" "Mount ${lv_name} to ${mount_point} success..."
                fi
            fi
        fi
    fi

    local fstabFile="/etc/fstab"
    local filetype="ext4"
    log_echo "INFO" "Set log directory dev auto mount in ${fstabFile}..."
    echo "" >> /etc/fstab
    mount_point_tmp=$(echo ${mount_point} |sed "s#\/#\\\/#g")
    if [ -z "`grep -w \"/dev/mapper/${log_dir_vg_name}-${log_dir_lv_name}\" ${fstabFile} | grep -v \"/dev/mapper/${log_dir_vg_name}-${log_dir_lv_name}/\" | grep -w \"${mount_point}\"`" ];then
        [ "X${mount_point_tmp}" != "X" ] && sed -i "/[ |\t]\+${mount_point_tmp}[ |\t]\+/d" ${fstabFile}
        echo "/dev/mapper/${log_dir_vg_name}-${log_dir_lv_name}   ${mount_point}   ${filetype}    nofail,defaults     0   0  #DV_Preset" >> ${fstabFile}
    fi
    ## remove dev uuid
    grep "UUID=.*${mount_point_tmp}" ${fstabFile} >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        sed -i "/UUID=.*${mount_point_tmp}/d" ${fstabFile}
    fi
    log_echo "INFO" "Remount ${mount_point} success."
}

function get_log_dir_vg_lv()
{
    get_vg_lv_name_by_dir "/opt"
    if [ $? -eq 0 ]; then
        log_dir_lv_name="lv_oss_log"
        return 0
    fi
    return 1
}

function lvm_reduce_logdir()
{
    local mount_point="$1"
    local disk_name="$2"
    local log_dir_default_size="$3"
    log_echo "INFO" "Begin to reduce lvm mount_point=${mount_point} to size=${log_dir_default_size}..."

    get_vg_lv_name_by_dir "${mount_point}"
    if [ $? -ne 0 ]; then
        get_log_dir_vg_lv
        if [ $? -ne 0 ]; then
            log_echo "WRAN" "Get log directory=${mount_point} vg and lv failed."
            return 1
        fi
    fi

    declare node_memtotal
    node_memtotal=$(awk '/^MemTotal/ { printf("%d", $2/1048576) }' /proc/meminfo)
    if [ ${node_memtotal} -gt 190 ];then
        log_echo "INFO" "physical machine scene, need to delete log lvm."
        local pv_except_log_extend=$(pvscan | grep "[ ]vg_oss_log[ ]" | awk '{print $2}' | grep -v "${LOG_EXTEND_DISK}" | tr '\n' ' ' | sed "s# ##g")
        lvm_rollback_local "${mount_point}" "${LOG_EXTEND_DISK}" "vg_oss_log" "lv_oss_log"
        pvremove -f "${pv_except_log_extend}"
    else
        log_dir_lv_remove "${mount_point}" "${log_dir_vg_name}" "${log_dir_lv_name}" "${disk_name}"
        if [ $? -ne 0 ]; then
            log_echo "INFO" "Log directory is not removed by this script, we will not auto mount it."
            return 1
        fi
        auto_mount_log_dir "${mount_point}" "${log_dir_vg_name}" "${log_dir_lv_name}" "${log_dir_default_size}"
    fi

    log_echo "INFO" "Reduce log directory size to log_dir_default_size=${log_dir_default_size} success."
}

function init_extend_pwd()
{
    local node_type="$1"
    local host_node_ip_list="$2"
    local pod_node_ip_list="$3"
    local check_execute_tag="$4"
    log_echo "INFO" "init_extend_pwd node_type=${node_type} start..."
    if [ -z "${host_node_ip_list}" ];then
        log_echo "INFO" "node_type=${node_type} ip list is null,not need to init it."
        return 0
    fi
    
    local index=0
    local tmp_host_ip_list=()
    local host_pwd_list=()
    for tmp_ip in $(echo "${host_node_ip_list}" | sed "s#,# #g")
    do
        tmp_host_ip_list[${index}]="${tmp_ip}"
        if [ "X${pre_install_key}" == "XPreInstall" ];then
            host_pwd_list[${index}]="${root}"
        else
            if [ ! -z "${check_execute_tag}" ];then
                Check_execute "Preset_Host_${tmp_ip}"
                if [ $? -ne 0 ];then
                    input_pwd "${tmp_ip}" "${oper_user}"
                fi
            else
                input_pwd "${tmp_ip}" "${oper_user}"
            fi
            host_pwd_list[${index}]="${pwdReturn}"
        fi
        ((index++))
    done
    
    index=0
    local pod_ip_list=()
    for tmp_ip in $(echo "${pod_node_ip_list}" | sed "s#,# #g")
    do
        pod_ip_list[${index}]="${tmp_ip}"
        ((index++))
    done
    
    if [ "${node_type}" == "SM" ];then
        SMHostExtend_Cluster_Node_List=(${tmp_host_ip_list[*]})
        SMExtend_Cluster_Node_pwd_List=(${host_pwd_list[*]})
        SMPodExtend_Cluster_Node_List=(${pod_ip_list[*]})
    elif [ "${node_type}" == "OMSA" ];then
        OMSAHostExtend_Cluster_Node_List=(${tmp_host_ip_list[*]})
        OMSAExtend_Cluster_Node_pwd_List=(${host_pwd_list[*]})
        OMSAPodExtend_Cluster_Node_List=(${pod_ip_list[*]})
    elif [ "${node_type}" == "IM" ];then
        IMHostExtend_Cluster_Node_List=(${tmp_host_ip_list[*]})
        IMExtend_Cluster_Node_pwd_List=(${host_pwd_list[*]})
        IMPodExtend_Cluster_Node_List=(${pod_ip_list[*]})
    elif [ "${node_type}" == "SD" ];then
        SDHostExtend_Cluster_Node_List=(${tmp_host_ip_list[*]})
        SDExtend_Cluster_Node_pwd_List=(${host_pwd_list[*]})
        SDPodExtend_Cluster_Node_List=(${pod_ip_list[*]})
    else
        log_echo "INFO" "node_type=${node_type} is other,not need to init it."
        return 0
    fi
    log_echo "INFO" "init_extend_pwd node_type=${node_type} End."
}

function pre_handle_managed_host()
{
    local node_type="$1"
    local app_ssh_ip="$2"
    local app_scp_ip="$2"
    local app_node_pwd="$3"

    ## 纳管前的公共处理操作
    if [ "X${node_type}" == "Xallinone" -o "X${node_type}" == "Xprimary" ]; then
        ## /tmp路径权限改为1777
        chmod 1777 ${TMP_PATH}
        ## 删除k8s残留在宿主机上的文件，防止非root场景因文件权限不足传输失败
        rm -f ${TMP_PATH}/install_kubeagent.tar.gz
        rm -f ${TMP_PATH}/uninstall_kubeagent.sh

    else
        auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} chmod 1777 ${TMP_PATH}"
        auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} rm -f ${TMP_PATH}/install_kubeagent.tar.gz"
        auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} rm -f ${TMP_PATH}/uninstall_kubeagent.sh"
    fi

    ## 仅扩容场景处理
    if [[ "X${node_type}" =~ Xextend[0-9] ]]; then
        mount_disk_before_managed_host "${node_type}" "${app_ssh_ip}" "${app_node_pwd}"
    fi
}

function mount_disk_before_managed_host()
{
    local node_type="$1"
    local app_ssh_ip="$2"
    local app_scp_ip="$2"
    local app_node_pwd="$3"

    log_echo "INFO" "Begin to mount disk before managed host for ${node_type}..."
    Check_execute "Mount_Disk_${app_ssh_ip}"
    if [ $? -eq 0 ];then
        return 0
    fi

    local tmp_dir=${TMP_PATH}/DVPreSet_extend_host
    local node_type_tmp_path=${tmp_dir}/tmp_file/${node_type}
    [ -f ${node_type_tmp_path}/DV_config.properties.tmp ] && rm -f ${node_type_tmp_path}/DV_config.properties.tmp
    mkdir -p ${node_type_tmp_path}
    cp -rpf ${tmp_dir}/DV_config.properties.tmp ${node_type_tmp_path}/DV_config.properties.tmp

    ## 刷新.tmp文件中的磁盘为占位符
    if [ "X${Digitalization_Disk_Param_Tag}" == "XYES" ] && [ "${AUTO_DISK_MOUNT}" == "TRUE" ]; then
        rebuild_tmp_cfg_file_disk ${node_type_tmp_path}/DV_config.properties.tmp
        if [ $? -ne 0 ]; then
            log_echo "ERROR" "Rebuild disk symbol in rebuild_tmp_cfg_file_disk ${node_type_tmp_path}/DV_config.properties.tmp failed, please check."
            exit 1
        fi
    fi

    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} mkdir -p ${tmp_dir}"
    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} chown ${oper_user}: ${tmp_dir}"

    local i2k_file_list="netWorkTypeOfDvConfigParameters.config DV_config.properties.tmp DV_PreSet_APP.sh DV_PreSet_DB.sh utils_common.sh utils_uniep.sh utils_os.sh DV_PreSet_Cleanup.sh pre_get_disk_symbol.properties custom_ext_disk.properties"
    for i2k_filename in ${i2k_file_list}
    do
        if [[ "X${node_type}" =~ Xextend[0-9] ]] && [[ "X${i2k_filename}" == "XDV_config.properties.tmp" ]]; then
            auto_scp ${app_node_pwd} ${node_type_tmp_path}/DV_config.properties.tmp "${oper_user}@${app_scp_ip}:${tmp_dir}"
            continue
        fi
        auto_scp ${app_node_pwd} ${PreSet_PATH}/Common/${i2k_filename} "${oper_user}@${app_scp_ip}:${tmp_dir}"
    done
    auto_scp ${app_node_pwd} ${node_type_tmp_path}/DV_config.properties.tmp "${oper_user}@${app_scp_ip}:${tmp_dir}"

    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount \\\"${node_type}\\\" \"paaslib\""
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ sh ${tmp_dir}/DV_PreSet_APP.sh auto_mount \\\"${node_type}\\\" \"paaslib\" ] at node ${app_ssh_ip} failed, please check detailed log ${tmp_dir}/preset_script.log on this node."
        exit 1
    fi

    auto_smart_ssh ${app_node_pwd} "${oper_user}@${app_ssh_ip} rm -rf ${tmp_dir:?}"

    echo "Mount_Disk_${app_ssh_ip} : success" >> ${action_tag}

    log_echo "INFO" "Mount disk before managed host success."
}

function check_and_set_pm_node_exist()
{
    local pm_node_num="$1"
    pm_node_exist="No"
    update_config ${configParametersFile} "pm_node_exist" "${pm_node_exist}" || exit 1
    if [ -z "${pm_node_num}" ];then
        log_echo "INFO" "pm_node_exist=${pm_node_exist} pm_node_num=${pm_node_num} is null."
        return 0
    fi
    
    if [ ${pm_node_num} -gt 0 ];then
        pm_node_exist="Yes"
        update_config ${configParametersFile} "pm_node_exist" "${pm_node_exist}" || exit 1
    fi
    log_echo "INFO" "pm_node_exist=${pm_node_exist}"
}

function check_os_page_size()
{
    log_echo "INFO" "check the os page size..."
    kernelpagesize="4K"
    if [ $(getconf PAGESIZE) -eq 65536 ];then
        kernelpagesize="64K"
    fi
    log_echo "INFO" "the os page size is [${kernelpagesize}]"
}

function add_at_allow()
{
    id ${i2k_user_name} >/dev/null 2>&1
    if [ $? -eq 0 ] && [ -f "/etc/at.allow" ] && [ -z `grep ${i2k_user_name} /etc/at.allow` ];then
        echo ${i2k_user_name} >> /etc/at.allow
    fi
}

function docker_host_add_users()
{
    log_echo "INFO" "docker_host_add_users start..."
    local group_name=""
    local group_id=""
    ## docker_host_group_list =  groupName1:groupID1,groupName2:groupID2,...
    for group_info in $(echo "${docker_host_group_list}"| sed "s/,/ /g");
    do
        group_name=$(echo "${group_info}"|awk -F':' '{print $1}')
        group_id=$(echo "${group_info}"|awk -F':' '{print $2}')
        cat /etc/group |grep "^${group_name}:" |grep ":${group_id}:" >> $LOG_FILE 2>&1
        if [ $? -eq 0 ];then
            log_echo "INFO" "The group_info=${group_info} is exists.continue."
            continue
        fi
        
        groupadd -g ${group_id} ${group_name}
        log_echo "INFO" "execute cmd:[ groupadd -g ${group_id} ${group_name} ],ret=$?"
    done
    
    ## sftponly:7002
    cat /etc/group |grep "^sftponly:" |grep ":7002:" >> $LOG_FILE 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "The sftponly not exists.create it."
        groupadd -g 7002 sftponly
        log_echo "INFO" "execute cmd:[ groupadd -g 7002 sftponly ],ret=$?"
    fi
    
    local user_name=""
    local user_id=""
    local group_id=""
    local user_home=""
    local login_shell=""
    ## docker_host_user_list = userName1:userID1:groupID1:userHome1:loginShell,userName2:userID2:groupID2:userHome2:loginShell,...
    for user_info in $(echo "${docker_host_user_list}"| sed "s/,/ /g");
    do
        user_name=$(echo "${user_info}"|awk -F':' '{print $1}')
        user_id=$(echo "${user_info}"|awk -F':' '{print $2}')
        group_id=$(echo "${user_info}"|awk -F':' '{print $3}')
        user_home=$(echo "${user_info}"|awk -F':' '{print $4}')
        login_shell=$(echo "${user_info}"|awk -F':' '{print $5}')
        if [ ! -z "${HOME_CUSTOM_PATH}" ];then
            user_home="${HOME_CUSTOM_PATH}/${user_name}"
        fi
        cat /etc/passwd |grep "^${user_name}:" |grep ":${user_id}:" >> $LOG_FILE 2>&1
        if [ $? -eq 0 ];then
            log_echo "INFO" "The user_info=${user_info} is exists.continue."
            continue
        fi
        
        useradd -g ${group_id} -u ${user_id} -m ${user_name} -d ${user_home} -s ${login_shell}  >> $LOG_FILE 2>&1
        log_echo "INFO" "execute cmd:[ useradd -g ${group_id} -u ${user_id} -m ${user_name} -d ${user_home} -s ${login_shell} ],ret=$?"
    done
    log_echo "INFO" "docker_host_add_users End"
}

function docker_host_del_users()
{
    log_echo "INFO" "docker_host_del_users start..."
    ## docker_host_user_list = userName1:userID1:groupID1:userHome1,userName2:userID2:groupID2:userHome2,...
    for user_info in $(echo "${docker_host_user_list}"| sed "s/,/ /g");
    do
        local user_name=$(echo "${user_info}"|awk -F':' '{print $1}')
        log_echo "INFO" "The user_info=${user_info}"
        killall -9 -u ${user_name} >> $LOG_FILE 2>&1
        killall -9 -u ${user_name} >> $LOG_FILE 2>&1
        userdel -rf ${user_name} >> $LOG_FILE 2>&1
        log_echo "INFO" "execute cmd:[ userdel -rf ${user_name} ],ret=$?"
        log_echo "INFO" "check_user=$(id ${user_name})"
    done
    
    ## docker_host_group_list =  groupName1:groupID1,groupName2:groupID2,...
    for group_info in $(echo "${docker_host_group_list}"| sed "s/,/ /g");
    do
        local group_name=$(echo "${group_info}"|awk -F':' '{print $1}')
        log_echo "INFO" "The group_info=${group_info}"
        groupdel ${group_name} >> $LOG_FILE 2>&1
        log_echo "INFO" "execute cmd:[ groupdel ${group_name} ],ret=$?"
    done
    log_echo "INFO" "docker_host_del_users End"
}
