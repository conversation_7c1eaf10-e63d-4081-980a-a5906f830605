#!/bin/bash
#####################################################
# Description : Only For non root install I2000
#####################################################
CURRENT_PATH=$(cd $(dirname $0); pwd)
. ${CURRENT_PATH}/utils_common.sh ${CURRENT_PATH}
test $? != 0 && exit 1
. ${CURRENT_PATH}/utils_uniep.sh ${CURRENT_PATH}
test $? != 0 && exit 1
. ${CURRENT_PATH}/utils_os.sh ${CURRENT_PATH}
test $? != 0 && exit 1

function uninstall_user()
{
    if [ -d ${i2k_user_home} ];then
        chattr -Ri ${i2k_user_home} >/dev/null 2>&1
    fi
    for i in {1..3};do
        killall -9 -u ossadm >/dev/null 2>&1
        killall -9 -u ${i2k_user_name} >/dev/null 2>&1
        killall -9 -u dbuser >> $LOG_FILE 2>&1
    done
    userdel -rf ${i2k_user_name} >> $LOG_FILE 2>&1
    rm -rf ${i2k_user_home:?} >/dev/null 2>&1
    userdel -rf dbuser >> ${LOG_FILE} 2>&1
    userdel -rf ${system_login_user} >> $LOG_FILE 2>&1
    if [ ! -f "/home/<USER>/sftpossuser_need_partition" -a ! -f "/home/<USER>/sftpossuser_by_virtual" ];then
        df -h|grep -w "/home/<USER>"
        if [ $? -eq 0 ];then
            umount_dir_subdirectory "/home/<USER>"
        fi
        userdel -rf ${system_sftp_user} >> $LOG_FILE 2>&1
        umount_loop_device "/home/<USER>" "/home/<USER>"
    fi
    
    umount_loop_device "/home/<USER>" "/tmp/dv_secbox"

    check_if_install_icnfg 
    if [ $? -eq 0 ];then
        umount_loop_device "/home/<USER>" "/home/<USER>"
        userdel -rf ${icnfg_devdata_user_name} >> $LOG_FILE 2>&1
    fi
    if [ ! -f "${CURRENT_PATH}/sysomc_need_del" ];then
        [ -f "${system_operation_userhome}/sysomc_need_del" ] && cp ${system_operation_userhome}/sysomc_need_del ${CURRENT_PATH}/sysomc_need_del
    fi
    [ -f "${CURRENT_PATH}/sysomc_need_del" ] && userdel -rf ${system_operation_user} >> $LOG_FILE 2>&1
    
    del_sop_user
    
    del_dvshare_user
    
    rm -rf ${CURRENT_PATH}/check_ossuser_trust_ok
}

function unsintall_dir()
{
    rm -rf ${i2k_install_path}/I2000
    rm -rf ${i2k_install_path}/breeze
    rm -rf  ${i2k_install_path}/cie
    rm -rf  ${i2k_install_path}/installlogs
    rm -rf  /opt/ntptools
    rm -rf  /opt/openssh
    [ "X${VS_PATH}" != "X" -a "X${VS_PATH}" != "X{{VS_PATH}}"  ] && [ -d ${VS_PATH} ] && rm -rf ${VS_PATH}
    return 0
}

function user_kill_session()
{
    log_echo "INFO" "Begin to kill session of $1"
    rm -rf /tmp/DVPreSet_DB/tmp_sql.sql /tmp/DVPreSet_DB/tmp_sqlfile_drop_DBdata.sql /tmp/DVPreSet_DB/sqlfile_for_clearI2000.sql /tmp/DVPreSet_DB/drop_sql.sql
    oracle_user=$1

    i2kdbname=${db_instance_name}
    
    echo "#!/bin/bash -l" > /tmp/DVPreSet_DB/tmp_sql.sql
    echo "export ORACLE_SID=${i2kdbname};sqlplus / as sysdba << EOF" >> /tmp/DVPreSet_DB/tmp_sql.sql
    echo "spool /tmp/DVPreSet_DB/tmp_sqlfile_drop_DBdata.sql" >> /tmp/DVPreSet_DB/tmp_sql.sql
    echo "SELECT 'ALTER SYSTEM KILL SESSION '||''''||SID||','||SERIAL#||''''||';' as KILLER FROM V\\\$SESSION WHERE USERNAME='${oracle_user}';" >> /tmp/DVPreSet_DB/tmp_sql.sql
    echo "spool off" >> /tmp/DVPreSet_DB/tmp_sql.sql
    echo "exit" >> /tmp/DVPreSet_DB/tmp_sql.sql
    echo "EOF" >> /tmp/DVPreSet_DB/tmp_sql.sql
    chmod 755 /tmp/DVPreSet_DB/tmp_sql.sql
    
    /tmp/DVPreSet_DB/tmp_sql.sql >> ${LOG_FILE} 2>&1
    
    cat /tmp/DVPreSet_DB/tmp_sqlfile_drop_DBdata.sql 2>/dev/null | grep -v "^SQL" | egrep "DROP|ALTER" > /tmp/DVPreSet_DB/sqlfile_for_clearI2000.sql
    
    if [ -z "`cat /tmp/DVPreSet_DB/sqlfile_for_clearI2000.sql | egrep "DROP|ALTER"`" ];then
        return 0
    fi

    echo "#!/bin/bash -l" > /tmp/DVPreSet_DB/drop_sql.sql
    echo "export ORACLE_SID=${i2kdbname};sqlplus / as sysdba << EOF" >> /tmp/DVPreSet_DB/drop_sql.sql
    echo "spool /tmp/DVPreSet_DB/tmp_sqlfile_drop_DBdata.sql" >> /tmp/DVPreSet_DB/drop_sql.sql
    cat /tmp/DVPreSet_DB/sqlfile_for_clearI2000.sql >> /tmp/DVPreSet_DB/drop_sql.sql
    echo "spool off" >> /tmp/DVPreSet_DB/drop_sql.sql
    echo "exit" >> /tmp/DVPreSet_DB/drop_sql.sql
    echo "EOF" >> /tmp/DVPreSet_DB/drop_sql.sql
    
    /tmp/DVPreSet_DB/drop_sql.sql >> ${LOG_FILE} 2>&1
}

function kill_user_session()
{
    user_list="icnfgappdb omssys omsmodel iempeam omssm omsfm omspm omspm_index omscm omscmon omuuser ossuser ossadmin dveamservicedb dvalarmservicedb dvsmservicedb dvcommonnotifyservicedb dvpkgadapterdb dvregisterservicedb mmlmaintenanceservicedb dvnfvadapterdb i2000pmdashboardadpservicedb i2000pmdashboardcfgservicedb i2000pmdashboardsrcservicedb adminhomedb apigateway_am_db apigovernancedb cmdbcoresvrdb eamdb fmdb idgendb invmetadatadb lifecycledb logdb meresgrpdb mouiservicedb nbicommondb omcdb rcaccessconfigdb rmcoordinatedb rmtaskmgmtdb smdb sysfensdb syspreferencesdb topodb dvnbiservicedb dvmenuutmadapterdb"

    for username in ${user_list}
    do
        user_kill_session ${username}
    done
}

function unsintall_db()
{
    log_echo "INFO" "Begin to uninstall db user and tablespace." 
    
    kill_user_session
    
    sql_tmp_file="/tmp/DVPreSet_DB/${i2k_user_name}_uninstall_sqlcommand"
    sql_src_file="${CURRENT_PATH}/uninstall_oracle.sql"
    sql_icnfg_file="${CURRENT_PATH}/uninstall_icnfg_oracle.sql"
    sql_log="${sql_tmp_file}.log"
    rm -rf ${sql_tmp_file:?}
    rm -rf ${sql_log:?}
    touch ${sql_tmp_file}
    touch  ${sql_log}
    chmod 755 ${sql_tmp_file}
    chmod 777 ${sql_log}

    echo "#!/bin/bash -l" >${sql_tmp_file}
    echo "export ORACLE_SID=${db_instance_name};sqlplus -s / as sysdba << EOF">>${sql_tmp_file}
    echo "set linesize 200;" >>${sql_tmp_file}
    echo "spool ${sql_tmp_file}.log" >>${sql_tmp_file}
    echo "" >> ${sql_tmp_file}
    cat ${sql_src_file} >> ${sql_tmp_file}
    check_if_install_icnfg && echo "" >> ${sql_tmp_file} && cat ${sql_icnfg_file} >> ${sql_tmp_file} && echo "" >> ${sql_tmp_file}
    echo "" >> ${sql_tmp_file}
    echo "spool off " >> ${sql_tmp_file}
    echo "exit" >> ${sql_tmp_file}
    echo "EOF" >> ${sql_tmp_file}
    
    ${sql_tmp_file}  > /dev/null 2>&1
    [ $? -ne 0 ] && die "Execute drop db user and tablespace failed, please check."
    cat ${sql_log}  | grep "^ORA-" | grep -v "^ORA-28002" | grep -v "^ORA-28001"| grep -v "^ORA-01918"| grep -v "^ORA-01919"| grep -v "^ORA-00959"| grep -v grep > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        log_echo "ERROR" "Drop db user and tablespace failed, please check ${sql_log}"
        exit 1
    fi
    rm -f ${sql_tmp_file}
    chmod 600 ${sql_log}
}

function down_float_ip()
{
    local float_ip=$1
    local is_ipv6="No"
    
    echo ${float_ip} | grep ":"
    if [ $? -eq 0 ];then
        ip addr | grep -w "${float_ip}" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            float_ip=$(simplify_ipv6_ip ${float_ip})
            float_ip=$(echo "${float_ip}" | tr '[A-Z]' '[a-z]')
        fi
        is_ipv6="Yes"
    fi

    if [ "X${is_ipv6}" == "XNo" ];then
        for float_ip_tmp in `echo ${float_ip} | sed 's/,/ /g'`
        do
            ip addr | grep -w "${float_ip_tmp}" >/dev/null 2>&1
            if [ $? -ne 0 ];then
                continue
            fi

            local nic_name=$(ip addr | grep -w "${float_ip_tmp}" | awk '{print $NF}')
            echo ${nic_name} | grep ":"
            if [ $? -eq 0 ];then
                ifconfig ${nic_name} down >> $LOG_FILE 2>&1
            fi
        done
    else
        ip addr | grep -w "${float_ip}" >/dev/null 2>&1
        if [ $? -eq 0 ];then
            ipconfig=$(ip addr |grep -iw ${float_ip}|awk '{print $2}')
            ifconfig ${i2k_float_ip_nic_ipv6} inet6 del ${ipconfig} >> $LOG_FILE 2>&1 
        fi
    fi
}

function uninstall_env()
{
    clean_env
    sed -i "/^${i2k_user_name} ALL=/d" /etc/sudoers
    sed -i "/^${system_login_user} ALL=/d" /etc/sudoers
    sed -i "/^${system_sftp_user} ALL=/d" /etc/sudoers
    sed -i "/^${system_operation_user} ALL=/d" /etc/sudoers
    check_if_install_icnfg && sed -i "/^${icnfg_devdata_user_name} ALL=/d" /etc/sudoers
    
    if [ -f /etc/profile.d/cie.sh ];then
        if [ -n "`grep 'BMU_HOME=' /etc/profile.d/cie.sh`" ];then
            sed -i "/SOP\/DVEngineeringService/d" /etc/profile.d/cie.sh
            sed -i "/USE_ROOT=/d" /etc/profile.d/cie.sh
            chown root:root /etc/profile.d/cie.sh
            chmod 644 /etc/profile.d/cie.sh
        else
            rm -f /etc/profile.d/cie.sh
        fi
    fi
    if [ -f /etc/profile.d/cie.csh ];then
        if [ -n "`grep 'BMU_HOME' /etc/profile.d/cie.csh`" ];then
            sed -i "/SOP\/DVEngineeringService/d" /etc/profile.d/cie.csh
            sed -i "/USE_ROOT/d" /etc/profile.d/cie.csh
            chown root:root /etc/profile.d/cie.csh
            chmod 644 /etc/profile.d/cie.csh
        else
            rm -f /etc/profile.d/cie.csh
        fi
    fi
    [ -f /etc/profile.d/i2k-env.csh ] && rm -r /etc/profile.d/i2k-env.csh
    [ -f /etc/profile.d/i2k-env.sh ] && rm -r /etc/profile.d/i2k-env.sh
    
    sed -i "/^${i2k_user_name}/d" /etc/security/limits.conf
    
    [ -d "/opt/zenith" ] && rm -rf /opt/zenith
    
    if [ "X${i2k_is_dual}" == "XYes" ];then
        down_float_ip "${i2k_plat_dual_float_ip_ipv4}"
        down_float_ip "${i2k_plat_dual_float_ip_ipv6}"
    fi
}

function uninstall_drbd()
{
    i2k_is_dual=$(cat ${CURRENT_PATH}/DV_config.properties.tmp |grep "^i2k_is_dual="| awk -F'=' '{print $2}')
    if [ "${i2k_is_dual}" != "Yes" -o "${need_drbd}" == "NO" ];then
        return 0
    fi
    lsmod | grep -w drbd > /dev/null
    if [ $? -eq 0 ];then
        log_echo "INFO" "Begin to uninstall drbd."
        sh ${CURRENT_PATH}/DRBD/drbd_uninstall.sh
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Uninstall drbd in DV node failed"
            exit 1
        fi
    else
        log_echo "INFO" "Begin to remove drbd disk."
        if [ -f ${CURRENT_PATH}/DRBD/drbd_uninstall.sh ];then
            sh ${CURRENT_PATH}/DRBD/drbd_uninstall.sh removelv
            if [ $? -ne 0 ];then
                log_echo "ERROR" "remove drbd disk in DV node failed"
                exit 1
            fi
        fi
    fi
    
    rm -rf /opt/share
}

function Revoke_ossadmin()
{
    log_echo "INFO" "Begin to revoke ossadmin."
    sql_tmp_file="/tmp/DVPreSet_DB_upgrade/revoke_db.sh"    
    echo "#!/bin/bash -l" >$sql_tmp_file
    echo "export ORACLE_SID=${db_instance_name};sqlplus / as sysdba << EOF" >> $sql_tmp_file
    echo "spool /tmp/DVPreSet_DB_upgrade/revoke_db.log;" >>$sql_tmp_file
    echo "revoke dba from OSSADMIN_role;" >>$sql_tmp_file
    echo "grant unlimited tablespace to OSSADMIN;" >>$sql_tmp_file
    echo "revoke select on dba_roles from OSSADMIN_role;" >>$sql_tmp_file
    echo "revoke select on dba_profiles from OSSADMIN_role;" >>$sql_tmp_file
    echo "revoke select on V_$asm_diskgroup from OSSADMIN_role;" >>$sql_tmp_file
    echo "revoke select on V_$instance from OSSADMIN_role;" >>$sql_tmp_file
    echo "revoke select on nls_database_parameters from OSSADMIN_role;" >>$sql_tmp_file
    echo "revoke select on dba_constraints from OSSADMIN_role;" >>$sql_tmp_file
    echo "revoke create any table,alter any table,select any table,insert any table,delete any table,update any table,drop any table,create any index,alter any index,drop any index,create any view,create any sequence,select any sequence,drop any sequence,create any procedure,create any trigger,create any directory,create procedure from OSSADMIN_role;" >>$sql_tmp_file
    echo "revoke OSSADMIN_role from OSSADMIN;" >>$sql_tmp_file
    echo "grant OSSADMIN_role to OSSADMIN;" >>$sql_tmp_file
    echo "COMMIT;"  >> $sql_tmp_file
    echo "spool off " >> ${sql_tmp_file}
    echo "quit"  >> $sql_tmp_file
    echo "EOF"  >> $sql_tmp_file
    chmod 755 ${sql_tmp_file}
    
    ${sql_tmp_file} > /dev/null 2>&1
    exec_sql_result=$?
    if [ ${exec_sql_result} -ne 0 ];then
        log_echo "ERROR" "Revoke OSSADMIN failed, please check at database node"
        exit 1
    fi
    log_echo "INFO" "Revoke ossadmin end."
}

function drop_new_user()
{
    new_user=$(cat ${CURRENT_PATH}/DV_config.properties.tmp |grep "^new_user_list="| awk -F'=' '{print $2}')
    if [ "X${new_user}" == "X" ];then
        log_echo "INFO" "No new user need to drop..."
        return 0
    fi
    
    log_echo "INFO" "Begin need to drop new db user..."
    tmp_dir=/tmp/DVPreSet_DB_upgrade
    sql_tmp_file="${tmp_dir}/rollback_drop_sqlcommand"
    sql_log="${sql_tmp_file}.log"
    rm -rf ${sql_tmp_file:?}
    rm -rf ${sql_log:?}
    touch ${sql_tmp_file}
    touch  ${sql_log}
    chmod 755 ${sql_tmp_file}
    chmod 777 ${sql_log}
    
    echo "#!/bin/bash -l" >${sql_tmp_file}
    echo "export ORACLE_SID=${db_instance_name};sqlplus -s / as sysdba << EOF">>${sql_tmp_file}
    echo "set linesize 200;" >>${sql_tmp_file}
    echo "spool ${sql_tmp_file}.log" >>${sql_tmp_file}
    
    for username in `echo ${new_user_list}|sed "s/,/ /g"`
    do
        echo "drop user ${username} cascade;" >>${sql_tmp_file}
    done
    
    echo "" >> ${sql_tmp_file}
    echo "spool off " >> ${sql_tmp_file}
    echo "exit" >> ${sql_tmp_file}
    echo "EOF" >> ${sql_tmp_file}
    ${sql_tmp_file}  > /dev/null 2>&1
    exec_sql_result=$?
    [ ${exec_sql_result} -ne 0 ] && die "Drop db user failed, please check at database node."
    cat ${sql_log}  | grep "^ORA-" | grep -v "^ORA-01918" | grep -v grep > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        log_echo "ERROR" "Drop new db user failed, please check ${sql_log} at database node."
        exit_script
    fi
}

function user_grp_rollback()
{
    filebackup_path=${CURRENT_PATH}/backup
    
    echo "Rollback user and group ..." 
    all_users=${i2k_user_name},$system_login_user,$system_sftp_user,devdata
    all_grps=$i2k_group_name
    
    for user_name in $(echo $all_users | sed 's/,/ /g')
    do
        user_home_tmp=$(cat /etc/passwd | grep "^${user_name}:" | awk -F':' '{print $6}')
        [ -d "$filebackup_path/user_grp/${user_name}" ] && rm -rf ${user_home_tmp:?}
        [ -f "$filebackup_path/user_grp/user_pwd.backup" ] && sed -i "/^${user_name}:/d" /etc/passwd
        [ -f "$filebackup_path/user_grp/user_shadow.backup" ] && sed -i "/^${user_name}:/d" /etc/shadow
        [ -f "$filebackup_path/user_grp/user_sudo.backup" ] && sed -i "/^${user_name} /d" /etc/sudoers        
    done
    
    for grp_name in $(echo $all_grps | sed 's/,/ /g')
    do
        [ -f "$filebackup_path/user_grp/group.backup" ] && sed -i "/^${grp_name}:/d" /etc/group
    done
    
    while read line
    do
        grp_name=$(echo $line | awk -F':' '{print $1}')
        sed -i "/$line/d" /etc/group
        echo $line >> /etc/group
    done < $filebackup_path/user_grp/group.backup

    while read line
    do
        user_name=$(echo $line | awk -F':' '{print $1}')
        echo $line >> /etc/passwd
        
        cat /etc/shadow | grep "^${user_name}:" > /dev/null 2>&1
        echo $(cat $filebackup_path/user_grp/user_shadow.backup | grep "^${user_name}:") >> /etc/shadow 
        
        user_sudo=$(cat $filebackup_path/user_grp/user_sudo.backup | grep "^${user_name} ")
        if [ "X$user_sudo" != "X" ]; then
            echo "$user_sudo" >> /etc/sudoers
        fi
        
        if [ -d $filebackup_path/user_grp/${user_name} ]; then
            user_home=$(cat /etc/passwd | grep "^${user_name}:" | awk -F':' '{print $6}')
            cp -rfp $filebackup_path/user_grp/${user_name} $user_home
        fi
    done < $filebackup_path/user_grp/user_pwd.backup
    
    check_user_permission
    
    [ -S "/home/<USER>/.ssh/sshagent_sock" ] && rm -f /home/<USER>/.ssh/sshagent_sock
    
    while read line
    do
        user_name=$(echo $line | awk -F"uid=" '{print $2}' | awk -F'(' '{print $2}' | awk -F')' '{print $1}')
        groups=$(echo $line | awk -F"groups=" '{print $2}' | sed 's/,/\n/g' | awk -F'(' '{print $2}' | awk -F')' '{print $1}')
        for group_name in $groups
        do
            usermod_add_group $group_name $user_name
        done
    done < $filebackup_path/user_grp/user_group.backup
}

function restore_sudoScripts()
{
    log_echo "INFO" "restore sudoScripts"
    chattr -i -R /root/restore
    ${i2k_user_home}/sudoScripts/backup_restore.sh /root ${i2k_user_home} restore
    if [ $? -ne 0 ];then
        log_echo "ERROR" "${i2k_user_home}/sudoScripts/backup_restore.sh /root ${i2k_user_home} restore failed"
        exit 1
    fi
    
    if [ -f /root/restore/recover_i2k_privilege.sh ];then
        log_echo "INFO" "restore recover_i2k_privilege.sh to ${i2k_user_home}."
        chattr -i /root/restore/recover_i2k_privilege.sh
        cp -rpf /root/restore/recover_i2k_privilege.sh ${i2k_user_home}/recover_i2k_privilege.sh
    fi
    
    chattr -i -R /root/restore/
}

function Rollback_File()
{
    if [ -z "`ls ${CURRENT_PATH}/backup`" ];then
        log_echo "ERROR" "Can not get the backup dir ${CURRENT_PATH}/backup at app node, please check"
        exit 1
    fi
    log_echo "INFO" "Begin to rollback fileset."

    chattr -R -i $i2k_user_home > /dev/null 2>&1
    chattr -R -i $i2k_user_home > /dev/null 2>&1
    chattr -R -i $i2k_user_home > /dev/null 2>&1

    user_grp_rollback

    if [ -f "${CURRENT_PATH}/backup"/attr_bak ]; then
        while read line
        do
            file_name=$(echo $line | sed "s/^[^ ]* //g")
            if [ "X$file_name" != "X" ]; then
                chattr +i "$file_name"
            fi
        done < "${CURRENT_PATH}/backup"/attr_bak
    fi

    restore_sudoScripts

    find ${i2k_user_home}/etc/ssl/dv -type f 2>/dev/null | xargs chmod 600 2>/dev/null

    cp -rp ${install_path}/SOP/etc/ssl/dv ${CURRENT_PATH}/backup/SOP_dv_new
    if [ -d "${CURRENT_PATH}/backup/SOP_dv" ];then
        rm -rf ${install_path}/SOP/etc/ssl/dv/*
        cp -rp ${CURRENT_PATH}/backup/SOP_dv/* ${install_path}/SOP/etc/ssl/dv/
    fi

    cp -rp ${i2k_install_path}/etc/ssl/dv ${CURRENT_PATH}/backup/DVEngineeringService_dv_new
    if [ -d "${CURRENT_PATH}/backup/DVEngineeringService_dv" ];then
        rm -rf ${i2k_install_path}/etc/ssl/dv/*
        cp -rp ${CURRENT_PATH}/backup/DVEngineeringService_dv/* ${i2k_install_path}/etc/ssl/dv/
    else
        rm -rf ${i2k_install_path}/etc
    fi

    cp -rp ${install_path}/SOP/etc/sign_dv ${CURRENT_PATH}/backup/SOP_sign_dv_new
    if [ -d "${CURRENT_PATH}/backup/SOP_sign_dv" ];then
        rm -rf ${install_path}/SOP/etc/sign_dv/*
        cp -rp ${CURRENT_PATH}/backup/SOP_sign_dv/* ${install_path}/SOP/etc/sign_dv/
    else
        rm -rf ${install_path}/SOP/etc/sign_dv
    fi
}

function rollback_app_lvm()
{
    node_type=$1
    scene_type=$2

    if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ] && [ "${AUTO_DISK_MOUNT}" == "TRUE" ]; then
       import_properties
    fi

    if [ "${AUTO_DISK_MOUNT}" == "TRUE" ];then
        if [ "X${node_type}" == "XPrimary" -o "X${node_type}" == "XSecondary" ];then
            node_type=""
        fi

        if [ "X${TRACE_DISK}" != "X" -a "X${TRACE_DISK}" != "X{{TRACE_DISK}}" -a "X${node_type}" == "X" ] && [ "X${i2k_is_dual}" != "XYes" -o "${need_drbd}" == "NO" ];then
            if [ "${scene_type}" == "docker" ];then
                lvm_rollback_local "${OPT_PATH}/share/trace" "${TRACE_DISK}" "vg_trace" "lv_trace"
            else
                lvm_rollback_local "${install_path}/share/SOP/DVEngineeringService/trace" "${TRACE_DISK}" "vg_trace" "lv_trace"
            fi
        fi

        if [ "X${PAAS_LIB_DISK}" != "X" -a "X${PAAS_LIB_DISK}" != "X{{PAAS_LIB_DISK}}" -a "X${scene_type}" == "Xpaaslib" ]; then
            which kubectl >/dev/null 2>&1
            if [ $? -ne 0 ]; then
                log_echo "INFO"  "dv is not shared with k8s, umount paaslib."
                umount_disk "${OPT_PATH}/paaslib" "vg_paaslib" "lv_paaslib"
            fi
        fi

        if [ "X${APPNode_DISK}" != "X" -a "X${APPNode_DISK}" != "X{{APPNode_DISK}}" -a "X${node_type}" == "X" ] && [ "X${i2k_is_dual}" != "XYes" -o "${need_drbd}" == "NO" ];then
            if [ "${scene_type}" == "docker" ];then
                umount_remain_disk "${OPT_PATH}/share/I2000/run/var/iemp/data/disk_ftp.img" "${OPT_PATH}/share/I2000/run/var/iemp/data/ftp"
                lvm_rollback_local "${OPT_PATH}/share" "${APPNode_DISK}" "vg_dv" "lv_dv"
            else
                lvm_rollback_local "${i2k_install_path}" "${APPNode_DISK}" "vg_dv" "lv_dv"
            fi
        fi

        if [ "X${HOFS_DISK}" != "X" -a "X${HOFS_DISK}" != "X{{HOFS_DISK}}" -a "X${node_type}" == "X" ];then
            lvm_rollback_local "${OPT_PATH}/oss/hofs" "${HOFS_DISK}" "vg_hofs" "lv_hofs"
            rm -rf ${OPT_PATH}/oss/hofs
        fi

        if [ "X${UNIEP_PUB_DISK}" != "X" -a "X${UNIEP_PUB_DISK}" != "X{{UNIEP_PUB_DISK}}" -a "X${node_type}" == "XM:UNIEP:PUB" ];then
            PUB_PATH=${OPT_PATH}/pub
            lvm_rollback_local "${PUB_PATH}" "${UNIEP_PUB_DISK}" "vg_uniep" "lv_uniep"
            [ "X${PUB_PATH}" != "X" -a "X${PUB_PATH}" != "X{{PUB_PATH}}" ] && rm -rf ${PUB_PATH:?}
        fi

        if [ "X${VSINDEX_DISK}" != "X" -a "X${VSINDEX_DISK}" != "X{{VSINDEX_DISK}}" -a "X${node_type}" == "XSA" ];then
            lvm_rollback_local "${HOME_PATH}/vsindex" "${VSINDEX_DISK}" "vg_vsindex" "lv_vsindex"
            rm -rf ${HOME_PATH}/vsindex 2>/dev/null
        fi

        if [ "X${SDNode_DB_DISK}" != "X" -a "X${SDNode_DB_DISK}" != "X{{SDNode_DB_DISK}}" -a "X${node_type}" == "XSD" ];then
            lvm_rollback_local "${OPT_PATH}/zenith" "${SDNode_DB_DISK}" "vg_sd_db" "lv_sd_db"
            rm -rf /opt/zenith
            if [ "X${SDNode_BACKUP_TMP_DISK}" != "X" -a "X${SDNode_BACKUP_TMP_DISK}" != "X{{SDNode_BACKUP_TMP_DISK}}" ] ;then
                lvm_rollback_local "/opt/oss/backuptmp" "${SDNode_BACKUP_TMP_DISK}" "vg_sd_dvbackuptmp" "lv_sd_dvbackuptmp"
            fi
        fi

        extended_disk_record="/var/adm/autoinstall/logs/extended_disk.record"
        ## clean up disk for pm node
        if [ "X${PM_DB_BACKUP_TMP_DISK}" != "X" -a "X${PM_DB_BACKUP_TMP_DISK}" != "X{{PM_DB_BACKUP_TMP_DISK}}" ] && [[ "X${node_type}" == "XPM" || ${node_type} =~ ^DVExtendCluster.* || ${node_type} =~ ^SMExtendCluster.* ]];then
            lvm_rollback_local "${OPT_PATH}/oss/backuptmp" "${PM_DB_BACKUP_TMP_DISK}" "vg_pm_dvbackuptmp" "lv_pm_dvbackuptmp"
        fi
        if [ "X${PM_DB_DISK}" != "X" -a "X${PM_DB_DISK}" != "X{{PM_DB_DISK}}" ] && [[ "X${node_type}" == "XPM" || ${node_type} =~ ^DVExtendCluster.* || ${node_type} =~ ^SMExtendCluster.* ]];then
            lvm_rollback_local "${OPT_PATH}/zenith" "${PM_DB_DISK}" "vg_pm_db" "lv_pm_db"
            rm -rf ${OPT_PATH}/zenith
            if [ "X${PM_DB_EXTEND_DISK}" != "X" -a "X${PM_DB_EXTEND_DISK}" != "X{{PM_DB_EXTEND_DISK}}"  ];then
                pv_remove "${PM_DB_EXTEND_DISK}"
                if [ -f ${extended_disk_record} ];then
                    sed -i "/vg_pm_db/d" ${extended_disk_record}
                fi
            fi
        fi
        ###Unmounting the Database Disk
        local pm_db_node_path_array=(${PHYSICAL_PM_DB_NODE_PATH_LIST//,/ }) 
        local path_list_number=${#pm_db_node_path_array[@]}
        if [[ "${node_type}" =~ PMNODE[0-9] ]];then         
            local node_number=$( echo ${node_type} |sed "s/^PMNODE\(.*\)DB.*/\1/g")
            local path_index=$[node_number*2]
            if [[ "${node_type}" == "PMNODE${node_number}DB1" ]];then
                if [ "X${PHYSICAL_PM_DB_DISK1}" != "X" -a "X${PHYSICAL_PM_DB_DISK1}" != "X{{PHYSICAL_PM_DB_DISK1}}"  ];then
                    lvm_rollback_local "${pm_db_node_path_array[$path_index]}" "${PHYSICAL_PM_DB_DISK1}" "vg_pm_db1" "lv_pm_db"
                fi
                if [ "X${PHYSICAL_PM_DB_EXTEND_DISK1}" != "X" -a "X${PHYSICAL_PM_DB_EXTEND_DISK1}" != "X{{PHYSICAL_PM_DB_EXTEND_DISK1}}"  ];then
                    pv_remove "${PHYSICAL_PM_DB_EXTEND_DISK1}"
                    if [ -f ${extended_disk_record} ];then
                        sed -i "/vg_pm_db1/d" ${extended_disk_record}
                    fi
                fi
                if [ "X${PM_DB_BACKUP_TMP_DISK}" != "X" -a "X${PM_DB_BACKUP_TMP_DISK}" != "X{{PM_DB_BACKUP_TMP_DISK}}" ];then
                    lvm_rollback_local "${OPT_PATH}/oss/backuptmp" "${PM_DB_BACKUP_TMP_DISK}" "vg_pm_dvbackuptmp" "lv_pm_dvbackuptmp"
                fi
            fi
            if [[ "${node_type}" == "PMNODE${node_number}DB2" ]];then
                if [ "X${PHYSICAL_PM_DB_DISK2}" != "X" -a "X${PHYSICAL_PM_DB_DISK2}" != "X{{PHYSICAL_PM_DB_DISK2}}"  ];then
                    lvm_rollback_local "${pm_db_node_path_array[$path_index+1]}" "${PHYSICAL_PM_DB_DISK2}" "vg_pm_db2" "lv_pm_db"
                fi
                if [ "X${PHYSICAL_PM_DB_EXTEND_DISK2}" != "X" -a "X${PHYSICAL_PM_DB_EXTEND_DISK2}" != "X{{PHYSICAL_PM_DB_EXTEND_DISK2}}"  ];then
                    pv_remove "${PHYSICAL_PM_DB_EXTEND_DISK2}"
                    if [ -f ${extended_disk_record} ];then
                        sed -i "/vg_pm_db2/d" ${extended_disk_record}
                    fi
                fi
                
            fi
        fi
        if [[ "X${node_type}" == "XPMNODE" ]];then          
            pm_db_node_path_list=$(df -h | grep "${OPT_PATH}/zenith/data/dvpmdbsvr" |awk -F ' ' "{print $6}" )
            for a in ${pm_db_node_path_list}
            do
                
                if [ $( df -h | grep $a | grep "vg_pm_db1" | wc -l) -ne 0 ];then
                    if [ "X${PHYSICAL_PM_DB_DISK1}" != "X" -a "X${PHYSICAL_PM_DB_DISK1}" != "X{{PHYSICAL_PM_DB_DISK1}}" ];then
                        lvm_rollback_local "${a}" "${PHYSICAL_PM_DB_DISK1}" "vg_pm_db1" "lv_pm_db"
                    fi
                    if [ "X${PHYSICAL_PM_DB_EXTEND_DISK1}" != "X" -a "X${PHYSICAL_PM_DB_EXTEND_DISK1}" != "X{{PHYSICAL_PM_DB_EXTEND_DISK1}}"  ];then
                        pv_remove "${PHYSICAL_PM_DB_EXTEND_DISK1}"
                        if [ -f ${extended_disk_record} ];then
                            sed -i "/vg_pm_db1/d" ${extended_disk_record}
                        fi
                    fi
                    
                fi
                if [ $( df -h | grep $a | grep "vg_pm_db2" | wc -l) -ne 0 ];then
                    if  [ "X${PHYSICAL_PM_DB_DISK2}" != "X" -a "X${PHYSICAL_PM_DB_DISK2}" != "X{{PHYSICAL_PM_DB_DISK2}}" ];then
                        lvm_rollback_local "${a}" "${PHYSICAL_PM_DB_DISK2}" "vg_pm_db2" "lv_pm_db"
                    fi
                    if [ "X${PHYSICAL_PM_DB_EXTEND_DISK2}" != "X" -a "X${PHYSICAL_PM_DB_EXTEND_DISK2}" != "X{{PHYSICAL_PM_DB_EXTEND_DISK2}}"  ];then
                        pv_remove "${PHYSICAL_PM_DB_EXTEND_DISK2}"
                        if [ -f ${extended_disk_record} ];then
                            sed -i "/vg_pm_db2/d" ${extended_disk_record}
                        fi
                    fi
                    
                fi
                if [ "X${PM_DB_BACKUP_TMP_DISK}" != "X" -a "X${PM_DB_BACKUP_TMP_DISK}" != "X{{PM_DB_BACKUP_TMP_DISK}}" ];then
                    lvm_rollback_local "${OPT_PATH}/oss/backuptmp" "${PM_DB_BACKUP_TMP_DISK}" "vg_pm_dvbackuptmp" "lv_pm_dvbackuptmp"
                fi
            done
            
        fi
                

        if [ "X${SRVBigData_DISK}" != "X" -a "X${SRVBigData_DISK}" != "X{{SRVBigData_DISK}}" -a "X${node_type}" == "XOM" ];then
            if  [ "X${netWorkType}" == "XC" ];then
                [ "X${VSINDEX_DISK}" != "X" ] && lvm_rollback_local "${VS_PATH}" "${VSINDEX_DISK}" "vg_vsindex" "lv_vsindex"
                [ "X${VS_PATH}" != "X" -a "X${VS_PATH}" != "X{{VS_PATH}}" ] && rm -rf ${VS_PATH:?}
            fi
            lvm_rollback_local "${KAFKA_PATH}" "${SRVBigData_DISK}" "vg_kafka" "lv_kafka"
            [ "X${KAFKA_PATH}" != "X" -a "X${KAFKA_PATH}" != "X{{KAFKA_PATH}}" ] && rm -rf ${KAFKA_PATH:?}
        fi

        IS_INSTALL_CMP=$(echo "${IS_INSTALL_CMP}" | tr 'a-z' 'A-Z')

        if [ "X${VSINDEX_DISK}" != "X" -a "X${VSINDEX_DISK}" != "X{{VSINDEX_DISK}}" -a "X${IS_INSTALL_CMP}" == "XYES" ];then
            lvm_rollback_local "${HOME_PATH}/vsindex" "${VSINDEX_DISK}" "vg_vsindex" "lv_vsindex"
            rm -rf ${HOME_PATH}/vsindex 2>/dev/null
        fi

        if [ "X${SRVBigData_DISK}" != "X" -a "X${SRVBigData_DISK}" != "X{{SRVBigData_DISK}}" -a "X${IS_INSTALL_CMP}" == "XYES" ];then
            lvm_rollback_local "${KAFKA_PATH}" "${SRVBigData_DISK}" "vg_kafka" "lv_kafka"
            [ "X${KAFKA_PATH}" != "X" -a "X${KAFKA_PATH}" != "X{{KAFKA_PATH}}" ] && rm -rf ${KAFKA_PATH:?}
        fi

        Flume_nodes_number=$(echo ${Flume_Cluster_Node_IPV4_List} | awk -F',' '{print NF}')
        VS_nodes_number=$(echo ${VS_Cluster_Node_IPV4_List} | awk -F',' '{print NF}')

        if [[ ${node_type} =~ ^FLUME.* ]];then
            for u in $(seq 1 $(echo "${Flume_Cluster_Node_IPV4_List}" | sed "s/,/\n/g" |wc -l) | xargs);
            do
                if [ "X${SRVBigData_DISK}" != "X{{SRVBigData_DISK}}" ];then
                    [ "X${SRVBigData_DISK}" != "X" ] && lvm_rollback_local "${KAFKA_PATH}" "${SRVBigData_DISK}" "vg_kafka" "lv_kafka"
                    [ "X${KAFKA_PATH}" != "X" -a "X${KAFKA_PATH}" != "X{{KAFKA_PATH}}" ] && rm -rf ${KAFKA_PATH:?}
                fi
            done
        fi

        if [[ ${node_type} =~ ^VS.* ]];then
            for v in $(seq 1 $(echo "${VS_Cluster_Node_IPV4_List}" | sed "s/,/\n/g" |wc -l) | xargs);
            do
                if [ "X${VSINDEX_DISK}" != "X{{VSINDEX_DISK}}" ];then
                    [ "X${VSINDEX_DISK}" != "X" ] && lvm_rollback_local "${VS_PATH}" "${VSINDEX_DISK}" "vg_vsindex" "lv_vsindex"
                    [ "X${VS_PATH}" != "X" -a "X${VS_PATH}" != "X{{VS_PATH}}" ] && rm -rf ${VS_PATH:?}
                fi
            done
        fi

        if [ "X${SA_Cluster_Node_IPV4_List}" != "X" -a "X${SA_Cluster_Node_IPV4_List}" != "X{{SA_Cluster_Node_IPV4_List}}" ];then
            for i in $(seq 1 $(echo "${SA_Cluster_Node_IPV4_List}" | sed "s/,/\n/g" |wc -l) | xargs);
            do
                if [ "X${node_type}" == "XSA${i}" -a "X${VSINDEX_DISK}" != "X{{VSINDEX_DISK}}" ];then
                    [ "X${VSINDEX_DISK}" != "X" ] && lvm_rollback_local "${VS_PATH}" "${VSINDEX_DISK}" "vg_vsindex" "lv_vsindex"
                    [ "X${VS_PATH}" != "X" -a "X${VS_PATH}" != "X{{VS_PATH}}" ] && rm -rf ${VS_PATH:?}
                fi
            done
        fi

        if [ "X${OM_Cluster_Node_IPV4_List}" != "X" -a "X${OM_Cluster_Node_IPV4_List}" != "X{{OM_Cluster_Node_IPV4_List}}" ];then
            for j in $(seq 1 $(echo "${OM_Cluster_Node_IPV4_List}" | sed "s/,/\n/g" |wc -l) | xargs);
            do
                if [ "X${node_type}" == "XOM${j}" -a "X${VSINDEX_DISK}" != "X{{VSINDEX_DISK}}" -a "X${SRVBigData_DISK}" != "X{{SRVBigData_DISK}}" ];then
                    [ "X${VSINDEX_DISK}" != "X" ] && lvm_rollback_local "${VS_PATH}" "${VSINDEX_DISK}" "vg_vsindex" "lv_vsindex"
                    [ "X${SRVBigData_DISK}" != "X" ] && lvm_rollback_local "${KAFKA_PATH}" "${SRVBigData_DISK}" "vg_kafka" "lv_kafka"
                    [ "X${VS_PATH}" != "X" -a "X${VS_PATH}" != "X{{VS_PATH}}" ] && rm -rf ${VS_PATH:?}
                    [ "X${KAFKA_PATH}" != "X" -a "X${KAFKA_PATH}" != "X{{KAFKA_PATH}}" ] && rm -rf ${KAFKA_PATH:?}
                fi
            done
        fi

        if [ "${scene_type}" == "extend" -a -f "${ATAE_extend_script}" ];then
            ## 自定义路径场景，需要EXTEND_98G_DISK有值来卸载自定义系统盘
            if [ "X${CUSTOM_PATH_SCENE}" != "XTRUE" ]; then
                sed -i "s#EXTEND_98G_DISK=.*#EXTEND_98G_DISK=#g" ${CURRENT_PATH}/DV_config.properties.tmp
                EXTEND_98G_DISK=""
            fi
        fi
        if [ "X${EXTEND_98G_DISK}" != "X" -a "X${EXTEND_98G_DISK}" != "X{{EXTEND_98G_DISK}}" ];then
            lvm_rollback_ext
        fi
        if [ "X${LOG_EXTEND_DISK}" != "X" -a "X${LOG_EXTEND_DISK}" != "X{{LOG_EXTEND_DISK}}" ]; then
            lvm_reduce_logdir "${OPT_PATH}/oss/log" "${LOG_EXTEND_DISK}" "20"
        fi
    fi

    if [ "${scene_type}" == "docker" ];then
        rm -rf ${OPT_PATH}/dv ${OPT_PATH}/dv_log ${OPT_PATH}/oss/log ${HOME_PATH}/dv ${TMP_PATH}/dv ${OPT_PATH}/pub ${OPT_PATH}/share ${OPT_PATH}/oss/backuptmp ${OPT_PATH}/zenith ${HOME_PATH}/vsindex /srv/BigData ${OPT_PATH}/dv_sudoers.d ${OPT_PATH}/dv_osconfig ${OPT_PATH}/dv_uniepsudobin ${OPT_PATH}/dv_os_tool ${OPT_PATH}/dv_signtool ${OPT_PATH}/dv_os_config ${OPT_PATH}/dv_osconfigserver >/dev/null 2>&1
    fi

    [ "${install_path}" != "/" ] && rm -rf ${install_path:?}
    if [  -f /etc/SuSE-release ];then
        reboot_script=/etc/init.d/boot.local
    else
        reboot_script=/etc/rc.d/rc.local
    fi
    dv_mount_path=/opt/oss/share/SOP/DVEngineeringService
    dv_mount_path=${dv_mount_path//\//\\/}
    sed '/^vgchange -ay$/d'  ${reboot_script}  >/dev/null 2>&1
    sed -i "/${dv_mount_path}/d"   ${reboot_script} >/dev/null 2>&1

    return 0
}

function rollback_backuptmp_lvm()
{

    if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ] && [ "${AUTO_DISK_MOUNT}" == "TRUE" ]; then
       import_properties
    fi
    log_echo "INFO" "Start rollback_backuptmp_lvm ..."
    if [ "${AUTO_DISK_MOUNT}" == "TRUE" ];then
        log_echo "INFO" "The AUTO_DISK_MOUNT=${AUTO_DISK_MOUNT}."
        if [ "X${DBNode_BACKUP_TMP_DISK}" != "X" -a "X${DBNode_BACKUP_TMP_DISK}" != "X{{DBNode_BACKUP_TMP_DISK}}" ];then
            log_echo "INFO" "The DBNode_BACKUP_TMP_DISK=${DBNode_BACKUP_TMP_DISK}."
            lvdisplay /dev/vg_toolkitbackup/lv_toolkitbackup >/dev/null 2>&1
            if [ $? -ne 0 ];then
                log_echo "INFO" "lvm_rollback_local /opt/oss/backuptmp ${DBNode_BACKUP_TMP_DISK} vg_dvbackuptmp lv_dvbackuptmp start."
                lvm_rollback_local "/opt/oss/backuptmp" "${DBNode_BACKUP_TMP_DISK}" "vg_dvbackuptmp" "lv_dvbackuptmp"
            fi
        fi
    fi

    log_echo "INFO" "End rollback_backuptmp_lvm."
}

function rollback_db_lvm()
{

    if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ] && [ "${AUTO_DISK_MOUNT}" == "TRUE" ]; then
       import_properties
    fi
    extended_disk_record="/var/adm/autoinstall/logs/extended_disk.record"
    if [ "${AUTO_DISK_MOUNT}" == "TRUE" ];then
        if [ "X${PM_KAFKA_DISK}" != "X" -a "X${PM_KAFKA_DISK}" != "X{{PM_KAFKA_DISK}}" ];then
            df -h |grep ${DOCKER_PM_KAFKA_PATH}
            if [ $? -eq 0 ];then
                lvm_rollback_local "${DOCKER_PM_KAFKA_PATH}" "${PM_KAFKA_DISK}" "vg_dv_kafka" "lv_dv_kafka"
                rm -rf ${OPT_PATH}/dv
            else
                lvm_rollback_local "${PM_KAFKA_PATH}" "${PM_KAFKA_DISK}" "vg_dv_kafka" "lv_dv_kafka"
                rm -rf  ${PM_KAFKA_PATH:?}
            fi
        fi

        if [ "X${DBNode_DISK}" != "X" -a "X${DBNode_DISK}" != "X{{DBNode_DISK}}" ];then
            lvm_rollback_local "${OPT_PATH}/zenith" "${DBNode_DISK}" "vg_zenith" "lv_zenith"
        fi
        if [ "X${DBNode_EXTEND_DISK}" != "X" -a "X${DBNode_EXTEND_DISK}" != "X{{DBNode_EXTEND_DISK}}" ];then
            pv_remove "${DBNode_EXTEND_DISK}"
            if [ -f ${extended_disk_record} ];then
                sed -i "/vg_zenith/d" ${extended_disk_record}
            fi
        fi

        if [ "X${DBNode_BACKUP_TMP_DISK}" != "X" -a "X${DBNode_BACKUP_TMP_DISK}" != "X{{DBNode_BACKUP_TMP_DISK}}" ];then
            lvdisplay /dev/vg_toolkitbackup/lv_toolkitbackup >/dev/null 2>&1
            if [ $? -ne 0 ];then
                lvm_rollback_local "${OPT_PATH}/oss/backuptmp" "${DBNode_BACKUP_TMP_DISK}" "vg_dvbackuptmp" "lv_dvbackuptmp"
            fi
        fi

        if [ "X${EXTEND_98G_DISK}" != "X" -a "X${EXTEND_98G_DISK}" != "X{{EXTEND_98G_DISK}}" ];then
            lvm_rollback_ext
        fi
        if [ "X${LOG_EXTEND_DISK}" != "X" -a "X${LOG_EXTEND_DISK}" != "X{{LOG_EXTEND_DISK}}" ]; then
            lvm_reduce_logdir "${OPT_PATH}/oss/log" "${LOG_EXTEND_DISK}" "20"
        fi
    fi
    rm -rf ${OPT_PATH}/zenith
    rm -rf ${OPT_PATH}/oss/backuptmp
    return 0

}

function delete_iptables_DOCKER_USER_lvs()
{

    if [ "X${lvs_vip_ipv4}" != "X" -a "X${lvs_vip_ipv4}" != "X{{LVS_VIP_IPV4}}" ];then
        iptables -L DOCKER-USER | grep -w "${lvs_vip_ipv4}"  > /dev/null 2>&1
        if [ $? -ne 0 ];then
            return 0
        fi

        iplist_del="${lvs_vip_ipv4}"

        if [ -f "${CURRENT_PATH}/DV_Config_LargeCapacity.config" ];then
            local PM_Node_IP_List=$(grep "^PM_Cluster_Node_IPV4_List=" ${CURRENT_PATH}/DV_Config_LargeCapacity.config | awk -F "=" '{print $2}' | sed 's#,# #g')
            iplist_del="${iplist_del} ${PM_Node_IP_List}"
        fi

        if [ -f "${CURRENT_PATH}/DV_Config_MergeCluster.config" ];then
            Merge_NODE_IP1=$(grep "^DVPrimary_IPV4=" ${CURRENT_PATH}/DV_Config_MergeCluster.config|awk -F'=' '{print $2}')
            Merge_NODE_IP2=$(grep "^DVSecondary_IPV4=" ${CURRENT_PATH}/DV_Config_MergeCluster.config|awk -F'=' '{print $2}')
            Merge_Extend_List=$(grep "^SMExtend_Cluster_Node_IPV4_List=" ${CURRENT_PATH}/DV_Config_MergeCluster.config|awk -F'=' '{print $2}')
            if [ -z "${Merge_Extend_List}" ];then
                Merge_Extend_List=$(grep "^Extend_Cluster_Node_IPV4_List=" ${CURRENT_PATH}/DV_Config_MergeCluster.config|awk -F'=' '{print $2}')
            fi
            if [ ! -z "${Merge_Extend_List}" ];then
                Merge_NODE_IP1=$(echo "${Merge_Extend_List}"|awk -F',' '{print $1}')
                Merge_NODE_IP2=$(echo "${Merge_Extend_List}"|awk -F',' '{print $2}')
            fi
            iplist_del="${iplist_del} ${Merge_NODE_IP1} ${Merge_NODE_IP2}"
        fi

        if [ "X${netWorkType}" == "XM" ] && [ "X${SMPodExtend_Cluster_Node_IPV4_List}" != "X" -o "X${PodExtend_Cluster_Node_IPV4_List}" != "X" ];then
            local tmp_pod_ip_list=""
            [ "X${PodExtend_Cluster_Node_IPV4_List}" != "X" ] && tmp_pod_ip_list="${PodExtend_Cluster_Node_IPV4_List}"
            [ "X${SMPodExtend_Cluster_Node_IPV4_List}" != "X" ] && tmp_pod_ip_list="${SMPodExtend_Cluster_Node_IPV4_List}"
            
            Merge_NODE_IP1=$(echo "${tmp_pod_ip_list}"|awk -F',' '{print $1}')
            Merge_NODE_IP2=$(echo "${tmp_pod_ip_list}"|awk -F',' '{print $2}')
            iplist_add="${iplist_add} ${Merge_NODE_IP1} ${Merge_NODE_IP2}"
        fi
        
        if [ "X${netWorkType}" == "XM" -a "X${merge_extend_ip_list}" != "X" ];then
            Merge_NODE_IP1=$(echo "${merge_extend_ip_list}"|awk -F',' '{print $1}')
            Merge_NODE_IP2=$(echo "${merge_extend_ip_list}"|awk -F',' '{print $2}')
            iplist_add="${iplist_add} ${Merge_NODE_IP1} ${Merge_NODE_IP2}"
        fi
        local rc_file=""
        if [ -f "/etc/rc.d/rc.local" ];then
            rc_file="/etc/rc.d/rc.local"
        fi
        if [ -f "/etc/rc.d/after.local" ];then
            rc_file="/etc/rc.d/after.local"
        fi

        for ip in ${iplist_del}
        do
            iptables -w -D DOCKER-USER -p all -s ${ip} -j ACCEPT
            iptables -w -D DOCKER-USER -p all -d ${ip} -j ACCEPT

            sed -i "/iptables -I DOCKER-USER -p all -s ${ip} -j ACCEPT/d" ${rc_file} > /dev/null 2>&1
            sed -i "/iptables -I DOCKER-USER -p all -d ${ip} -j ACCEPT/d" ${rc_file} > /dev/null 2>&1

        done
        rm -rf /root/repair_iptables  > /dev/null 2>&1
        sed -i "/\/root\/repair_iptables\/repair_iptables.sh/d" ${rc_file}
    fi
}

function delete_lvs()
{
    log_echo "INFO" "begin to delete lvs"

    ps -ef | grep -w keepalived | grep -v grep | awk '{print $2}' |xargs -i kill -9 {} > /dev/null 2>&1
    ps -ef | grep -w keepalived | grep -v grep | awk '{print $2}' |xargs -i kill -9 {} > /dev/null 2>&1
    ps -ef | grep -w ipvsadm | grep -v grep | awk '{print $2}' |xargs -i kill -9 {} > /dev/null 2>&1

    ipvsadm -l > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "Cleanup all ipvsadm rule..."
        ipvsadm -C >/dev/null 2>&1
        [ $? -ne 0 ] && log_echo "ERROR" "ipvsadm -C execute failed." && exit 1
    fi

    if [ -d "/usr/lib64/dvlvslib" ];then
        rm -rf /usr/lib64/dvlvslib /usr/sbin/keepalived /usr/sbin/genhash /usr/sbin/ipvsadm
        sed -i "/#dv_lvs/d" /etc/ld.so.conf
        [ -f /etc/ld.so.conf.d/dv_lvs.conf ] && rm -f /etc/ld.so.conf.d/dv_lvs.conf
        ldconfig
    fi

    [ -d "/etc/lvsconf" ] && cp -rpf /etc/lvsconf /etc/lvsconf_bak
    rm -rf /etc/lvsconf

    del_ip_from_ipaddr "${lvs_vip_ipv4}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "delete lvs_vip_ipv4:${lvs_vip_ipv4} failed"
        exit 1
    fi

    del_ip_from_ipaddr "${lvs_dip_ipv4}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "delete lvs_dip_ipv4:${lvs_dip_ipv4} failed"
        exit 1
    fi

    del_ip_from_ipaddr "${LVS_VIP_IPV6}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "delete LVS_VIP_IPV6:${LVS_VIP_IPV6} failed"
        exit 1
    fi

    del_ip_from_ipaddr "${LVS_DIP_IPV6}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "delete LVS_DIP_IPV6:${LVS_DIP_IPV6} failed"
        exit 1
    fi

    delete_iptables_lvs "${med_df_port}" "tcp"

    delete_iptables_lvs "${med_snmp_port}" "udp"

    delete_iptables_DOCKER_USER_lvs
}

function main()
{
    log_echo "INFO" "Begin to uninstall preset."
    if [ ! -f "${CURRENT_PATH}/mount_extdisk_finish" ];then
        [ -f "${system_login_userhome}/mount_extdisk_finish" ] && cp ${system_login_userhome}/mount_extdisk_finish ${CURRENT_PATH}/mount_extdisk_finish
        [ -f "${system_login_userhome}/new_partition_finish" ] && cp ${system_login_userhome}/new_partition_finish ${CURRENT_PATH}/new_partition_finish
    fi
    
    delete_loop_monitor
    
    uninstall_user

    unsintall_dir

    uninstall_env

    uninstall_drbd

    delete_ip_to_sshd_file

    modify_iptables "delete"

    delete_lvs

    cleanup_prerouting_iptables_lvs "${med_snmp_port}" "udp"

    config_corebinding_crontab  remove
    if [ -d "/root/core_binding/" ];then
        rm -rf  /root/core_binding/
    fi
    remove_core_binding_timer

    if [ -f "$ROOT_CUSTOM_PATH/firstlogin.tag" ];then
        rm -rf  $ROOT_CUSTOM_PATH/firstlogin.tag
    fi
    
    umount_loop_device "/opt/oss/share/SOP/DVEngineeringService/I2000/run/var/iemp/data/disk_ftp.img" "/opt/oss/share/SOP/DVEngineeringService/I2000/run/var/iemp/data/ftp"
    umount_loop_device "/home/<USER>" "/tmp/dv_secbox"
    delete_loop_monitor
    log_echo "INFO" "Uninstall preset finished."
}

function cleanup_sshd()
{
    delete_ip_to_sshd_file
    log_echo "INFO" "cleanup_sshd finished."

    if [ ! -f "${CURRENT_PATH}/mount_extdisk_finish" ];then
        [ -f "${i2k_user_home}/mount_extdisk_finish" ] && cp ${i2k_user_home}/mount_extdisk_finish ${CURRENT_PATH}/mount_extdisk_finish
        [ -f "${i2k_user_home}/new_partition_finish" ] && cp ${i2k_user_home}/new_partition_finish ${CURRENT_PATH}/new_partition_finish
    fi

    del_sop_user

    if [ ! -f "${CURRENT_PATH}/sysomc_need_del" ];then
        [ -f "${system_operation_userhome}/sysomc_need_del" ] && cp ${system_operation_userhome}/sysomc_need_del ${CURRENT_PATH}/sysomc_need_del
    fi
    [ -f "${CURRENT_PATH}/sysomc_need_del" ] && userdel -rf ${system_operation_user} >/dev/null 2>&1

    for i in {1..3};do
        killall -9 -u ossadm >> $LOG_FILE 2>&1
        killall -9 -u ossuser >> $LOG_FILE 2>&1
        killall -9 -u dbuser >> $LOG_FILE 2>&1
        pgrep -u 3004 | xargs kill -9 >> $LOG_FILE 2>&1
    done
    kill_path_process "${OPT_PATH}/zenith"
    kill_path_process "${OPT_PATH}/redis"
    rm -rf ${OPT_PATH}/zenith
    rm -rf ${OPT_PATH}/redis

    grep "^dbgroup:" /etc/group >/dev/null 2>&1
    if [ $? -eq 0 ];then
        groupdel dbgroup >> $LOG_FILE 2>&1
    fi

    killall -9 -u ${system_login_user}
    userdel -rf ${system_login_user}
    if [ ! -f "${HOME_PATH}/sftpossuser/sftpossuser_need_partition" -a ! -f "${HOME_PATH}/sftpossuser/sftpossuser_by_virtual" ];then
        killall -9 -u ${system_sftp_user}
        df -h|grep -w "${HOME_PATH}/sftpossuser"
        if [ $? -eq 0 ];then
            umount_dir_subdirectory "${HOME_PATH}/sftpossuser"
        fi
        userdel -rf ${system_sftp_user}
        umount_loop_device "${HOME_PATH}/disk_sftpossuser.img" "${HOME_PATH}/sftpossuser"
    fi
    umount_loop_device "${HOME_PATH}/dv_tmp.img" "${TMP_PATH}/dv_secbox"
    

    clean_opasswd

    if [ -f "${ROOT_CUSTOM_PATH}/firstlogin.tag" ];then
        rm -rf  ${ROOT_CUSTOM_PATH}/firstlogin.tag
    fi
    if [ -d "${ROOT_CUSTOM_PATH}/core_binding/" ];then
        rm -rf  ${ROOT_CUSTOM_PATH}/core_binding/
    fi
    remove_core_binding_timer
    
    delete_loop_monitor
    return 0
}

function rollback_ntp_config()
{
    log_echo "INFO" "Begin to rollback_ntp_config."
    if [ -f "/etc/ntp.conf.bak_dv" ];then
        log_echo "INFO" "rollback ntp config /etc/ntp.conf.bak_dv to /et/ntp.conf"
        cp -rf /etc/ntp.conf.bak_dv /etc/ntp.conf

        rcntpd restart >> $LOG_FILE 2>&1
        systemctl enable ntpd >> $LOG_FILE 2>&1
        ntpq -p >> $LOG_FILE 2>&1
    else
        log_echo "INFO" "The /etc/ntp.conf.bak_dv is not exist,just skip the step ..."
    fi
    log_echo "INFO" "rollback_ntp_config end."
    return 0
}

function rollback_ita_ntpsmart_config()
{
    log_echo "INFO" "Begin to rollback_ita_ntpsmart_config."
    local ntpsmart_config_file="/var/adm/autoinstall/scripts/ita_ntpsmart/conf/ita-ntpsmart.conf"
    if [ -f ${ntpsmart_config_file} ];then
        for ip in $(echo "${NTP_server_IP}"|sed "s/,/ /g");
        do
            echo $ip | grep -E "^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$" >/dev/null 2>&1
            if [ $? -ne 0 ];then
                log_echo "ERROR" "The NTP_server_IP is illegal. Please check. "
                exit 1
            fi
            sed -i "s/${ip}//g" ${ntpsmart_config_file}
        done

        systemctl restart ita-ntpsmart >> $LOG_FILE 2>&1
    else
        log_echo "INFO" "The ${ntpsmart_config_file} is not exist,just skip the step ..."
    fi
    log_echo "INFO" "rollback_ita_ntpsmart_config end."
    return 0
}

function start_all_service()
{
    log_echo "INFO" "start_all_service start."
    if [ "X$(whoami)" == "Xroot" ];then
        rm -rf /tmp/dv_i2k_cleanup.log
        su - ossadm -c "${install_path}/manager/agent/DeployAgent/bin/ipmc_adm -cmd startnode"  > /tmp/dv_i2k_cleanup.log
    else
        rm -rf /tmp/dv_i2k_cleanup_ossadm.log
        ${install_path}/manager/agent/DeployAgent/bin/ipmc_adm -cmd startnode  > /tmp/dv_i2k_cleanup_ossadm.log
    fi
    if [ $? -ne 0 ];then
        log_echo "INFO" "start_all_service result=1 not 0.check log:/tmp/dv_i2k_cleanup.log or /tmp/dv_i2k_cleanup_ossadm.log"
    fi
    log_echo "INFO" "start_all_service finished."
}
function delete_upgrade_preSet_tag()
{
    log_echo "INFO" "start exc delete_upgrade_preSet_tag..."
    local tag_file=${install_path}/share/SOP/DVEngineeringService/I2000/not_need_start.tag
    sudo -u ossuser test -f ${tag_file}
    local ret=$?
    if [ -d ${install_path}/share/SOP/DVEngineeringService/I2000 -a $ret -eq 0 ];then
        sudo -u ossuser rm -rf ${tag_file:?} > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "rm -rf ${tag_file} failed."
            exit 1
        fi
    fi
    log_echo "INFO" "exc delete_upgrade_preSet_tag finished."
}

function rollback_devdata_home(){
    sftp_conf=/etc/ssh/sftpd_config
    if [ -f ${sftp_conf} ];then
        sed -i '/devdata/d' ${sftp_conf}
        if [ -d ${icnfg_devdata_user_home}/configpkg ];then
            mv ${icnfg_devdata_user_home}/configpkg{,bak$(date +%s)}
            ln -s ${i2k_install_path}/icnfg/webpages/var/data/configpkg ${icnfg_devdata_user_home}/configpkg 
        fi
        if [ -d ${icnfg_devdata_user_home}/sftp/necert  ];then
            mv ${icnfg_devdata_user_home}/sftp/necert{,bak$(date +%s)}
            ln -s  ${i2k_install_path}/icnfg/webpages/var/data/necert ${icnfg_devdata_user_home}/sftp/necert
        fi
        if [ -d ${icnfg_devdata_user_home}/sftp/jdbc ];then
            mv ${icnfg_devdata_user_home}/sftp/jdbc{,bak}
            ln -s ${i2k_install_path}/icnfg/webpages/var/data/jdbc ${icnfg_devdata_user_home}/sftp/jdbc
        fi
        check_ssh_sftp_seperate
        if [ $? -ne 0 ];then
        sed -i "s/.*Subsystem[ ]\+sftp/#Subsystem sftp/g" /etc/ssh/sshd_config
        sed -i "0,/#Subsystem sftp/s/#Subsystem sftp[ ]\+.*/Subsystem sftp internal-sftp -l VERBOSE -f AUTH/" /etc/ssh/sshd_config
            /bin/systemctl restart sshd
            /bin/systemctl restart sftpd
        fi

    fi
    chown -R ${icnfg_devdata_user_name}:${i2k_group_name} ${icnfg_devdata_user_home}
    chmod 700 ${icnfg_devdata_user_home}/sftp/import/manual ${icnfg_devdata_user_home}/sftp/export/manual
}

function del_group()
{
    local group_name="$1"
    local group_id="$2"
    if [ -z "${group_name}" -o -z "${group_id}" ];then
        log_echo "ERROR" "The group_name=${group_name} or group_id=${group_id} has null."
        exit 1
    fi
    
    cat /etc/group |grep "^${group_name}:" |grep ":${group_id}:" >> ${LOG_FILE} 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "The group name: ${group_name} and group id: ${group_id} is not exists."
        return 0
    fi
    
    groupdel ${group_name}
    if [ $? -ne 0 ];then
        log_echo "INFO" "Execute cmd:[ groupdel ${group_name} ] failed."
        log_echo "INFO" "The group name of users: $(cat /etc/group |grep \"^${group_name}:\")."
        return 1
    fi
    
    log_echo "INFO" "del group ${group_name}:${group_id} success."
}

function del_dv_container_monitor_crontab()
{
    log_echo "INFO" "start del_dv_container_monitor_crontab..."
    local monitor_dir=${ROOT_CUSTOM_PATH}/dv_pod_monitor
    if [ -d ${monitor_dir} ];then
        rm -rf ${monitor_dir:?}
        log_echo "INFO" "del ${monitor_dir}/dv_container_monitor.sh file success."
    fi
    
    log_echo "INFO" "del dv_pod_monitor crontab."
    crontab -l > ${TMP_PATH}/crontab.tmp
    cat ${TMP_PATH}/crontab.tmp |grep "${monitor_dir}/dv_container_monitor.sh" >>${LOG_FILE} 2>&1
    if [ $? -eq 0 ]; then
        log_echo "INFO" "need to del dv_pod_monitor crontab."
        sed -i "/\/root\/dv_pod_monitor\/dv_container_monitor.sh/d" ${TMP_PATH}/crontab.tmp >>${LOG_FILE} 2>&1
        crontab  ${TMP_PATH}/crontab.tmp >>${LOG_FILE} 2>&1
        rm -rf ${TMP_PATH}/crontab.tmp
    fi

    systemctl stop dv_container_monitor.service
    systemctl disable dv_container_monitor.service
    systemctl stop dv_container_monitor.timer
    systemctl disable dv_container_monitor.timer

    [ -f /etc/systemd/system/dv_container_monitor.service ] && rm /etc/systemd/system/dv_container_monitor.service
    [ -f /etc/systemd/system/dv_container_monitor.timer ] && rm /etc/systemd/system/dv_container_monitor.timer
    systemctl daemon-reload
    delete_old_sudo sysomc "${ROOT_CUSTOM_PATH}/dv_pod_monitor/dv_container_monitor.sh"
    log_echo "INFO" "del dv_pod_monitor crontab finished."
}

function del_lvs_ip()
{
    log_echo "INFO" "del_lvs_ip start..."
    ## lvs_vip_ipv4 lvs_dip_ipv4
    local lvs_ip_list=""
    if [ -z "${lvs_vip_ipv4}" -o "X${lvs_vip_ipv4}" == "X{{LVS_VIP_IPV4}}" ];then
        log_echo "INFO" "The lvs_vip_ipv4=${lvs_vip_ipv4} is null."
    else
        lvs_ip_list="${lvs_vip_ipv4}"
    fi
    
    if [ -z "${lvs_dip_ipv4}" -o "X${lvs_dip_ipv4}" == "X{{LVS_DIP_IPV4}}" ];then
        log_echo "INFO" "The lvs_dip_ipv4=${lvs_dip_ipv4} is null."
    else
        lvs_ip_list="${lvs_ip_list} ${lvs_dip_ipv4}"
    fi
    
    if [ -z "${lvs_ip_list}" ];then
        log_echo "INFO" "The lvs_ip_list=${lvs_ip_list} is null.not need to del."
        return 0
    fi
    
    local lvs_ip=""
    local ip_mask=""
    local nic=""
    for lvs_ip in ${lvs_ip_list};
    do
        ip addr |grep -wF "${lvs_ip}" >>${LOG_FILE} 2>&1
        if [ $? -eq 0 ];then
            ip_mask=$(ip addr |grep -wF "${lvs_ip}"|awk '{print $2}')
            nic=$(ip addr |grep -wF "${lvs_ip}"|awk '{print $NF}'|awk -F':' '{print $1}')
            if [ -z "${ip_mask}" -o -z "${nic}" ];then
                log_echo "ERROR" "ip_mask=${ip_mask} nic=${nic} has null."
                exit 1
            fi
            
            log_echo "INFO" "ip_mask=${ip_mask} nic=${nic}"
            ip addr del ${ip_mask} dev ${nic} >>${LOG_FILE} 2>&1
            if [ $? -ne 0 ];then
                log_echo "ERROR" "ip addr del ${ip_mask} dev ${nic} failed.check log:${LOG_FILE}"
                exit 1
            fi
            log_echo "INFO" "del_lvs_ip ${lvs_ip} success."
        else
            log_echo "INFO" "The ${lvs_ip} is not exist,not need to del."
        fi
    done
    log_echo "INFO" "del_lvs_ip End."
}

function cleanup_pod()
{
    log_echo "INFO" "start cleanup_pod..."

    ## cleanup_pod_filelist.properties会从容器化的部署模板 Docker\helm\dv\templates\deployment.yaml里查找宿主机映射路径，然后将其删除来清理宿主机目录。
    if [ -f ${CURRENT_PATH}/cleanup_pod_filelist.properties ];then
        log_echo "INFO" "start cleanup file list."
        for file_path in $(cat ${CURRENT_PATH}/cleanup_pod_filelist.properties);do
            if [ -z "${file_path}" ];then
                continue
            fi
            
            if [ ! -f ${file_path} -a ! -d ${file_path}  ];then
                continue
            fi
            
            if [ -f ${file_path} ];then
                log_echo "INFO" "del ${file_path}"
                chattr -Ri ${file_path} >> ${LOG_FILE} 2>&1
                rm -rf ${file_path:?} >> ${LOG_FILE} 2>&1
                continue
            fi
            
            check_path=$(cd ${file_path};pwd)
            if [ "X${check_path}" == "X/" ];then
                log_echo "INFO" "The ${check_path} not need to del."
                continue
            fi
            log_echo "INFO" "del ${file_path}"
            chattr -Ri ${check_path}  >> ${LOG_FILE} 2>&1
            rm -rf ${check_path:?}/*  >> ${LOG_FILE} 2>&1
        done
    fi

    ## 删除所有宿主机的使用目录
    rm -rf ${OPT_PATH}/dv ${OPT_PATH}/dv_log ${OPT_PATH}/oss/log ${HOME_PATH}/dv ${TMP_PATH}/dv ${OPT_PATH}/pub ${OPT_PATH}/share ${OPT_PATH}/oss/backuptmp ${OPT_PATH}/zenith ${HOME_PATH}/vsindex /srv/BigData ${OPT_PATH}/dv_sudoers.d ${OPT_PATH}/dv_osconfig ${OPT_PATH}/dv_uniepsudobin ${OPT_PATH}/dv_os_tool ${OPT_PATH}/dv_signtool ${OPT_PATH}/dv_os_config ${OPT_PATH}/dv_osconfigserver >/dev/null 2>&1

    id sysomc
    if [ $? -eq 0 ];then
        userdel -rf sysomc >/dev/null 2>&1
    fi
    del_group "ossgroup" "2000"
    
   
    del_dv_container_monitor_crontab
    delete_loop_monitor
    
    del_lvs_ip
    
    local dev_log=$(ls -al /dev/log |grep "/run/systemd/journal/dev-log")
    local journal_dev_log=$(ls /run/systemd/journal/dev-log 2>/dev/null)
    if [ -z "${dev_log}" -a ! -z "${journal_dev_log}" ];then
        rm -f /dev/log
        ln -s /run/systemd/journal/dev-log /dev/log
        log_echo "INFO" "execute cmd:[ ln -s /run/systemd/journal/dev-log /dev/log ].ret=$?"
    fi
    log_echo "INFO" "check_ret=$(ls -al /dev/log)"

    ## kill docker process
    pod_id=$(docker ps |grep "dv-container" |awk '{print $1}')
    if [ "X${pod_id}" != "X" ];then
        docker kill ${pod_id}
    fi
    log_echo "INFO" "cleanup_pod finished."
}

function cleanup_tmp_file()
{
    if [ -f /tmp/uninstall_kubeagent.sh ];then
        log_echo "INFO" "cleanup uninstall_kubeagent.sh"
        rm -rf /tmp/uninstall_kubeagent.sh
    fi
}
function rollback_pm_lvm()
{
    if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ] && [ "${AUTO_DISK_MOUNT}" == "TRUE" ]; then
       import_properties
    fi
    node_type=$1
    ###Unmounting the Database Disk, 如果磁盘不存在，需要能兼容不报错，不冗余卸载，或者磁盘不存在时跳过卸载步骤
    local pm_db_node_path_array=(${PHYSICAL_PM_DB_NODE_PATH_LIST//,/ }) 
    local path_list_number=${#pm_db_node_path_array[@]}
    if [[ "${node_type}" =~ PMNODE[0-9] ]];then
        local node_number=$( echo ${node_type} |sed "s/^PMNODE\(.*\)DB.*/\1/g")
        local path_index=$[node_number*2]
        if [[ "${node_type}" == "PMNODE${node_number}DB1" ]];then
            if [ "X${PHYSICAL_PM_DB_DISK1}" != "X" -a "X${PHYSICAL_PM_DB_DISK1}" != "X{{PHYSICAL_PM_DB_DISK1}}"  ];then
                lvm_rollback_local "${pm_db_node_path_array[$path_index]}" "${PHYSICAL_PM_DB_DISK1}" "vg_pm_db1" "lv_pm_db"
                rm -rf ${PHYSICAL_PM_DB_NODE0_PATH1:?}
            fi
        fi
        if [[ "${node_type}" == "PMNODE${node_number}DB2" ]];then
            if [ "X${PHYSICAL_PM_DB_DISK2}" != "X" -a "X${PHYSICAL_PM_DB_DISK2}" != "X{{PHYSICAL_PM_DB_DISK2}}"  ];then
                lvm_rollback_local "${pm_db_node_path_array[$path_index+1]}" "${PHYSICAL_PM_DB_DISK2}" "vg_pm_db1" "lv_pm_db"
                rm -rf ${PHYSICAL_PM_DB_NODE0_PATH2:?}
            fi
        fi
    fi
    
    if [[ "${node_type}" == DBKAFKA ]];then
        scene_type=$2
        local tmp_pm_kafka_path="${PM_KAFKA_PATH}"
        if [ "${scene_type}" == "docker" ];then
            tmp_pm_kafka_path="${DOCKER_PM_KAFKA_PATH}"
        fi
        
        kafka_parent_dir=$(dirname ${tmp_pm_kafka_path})
        if [ ! -f ${kafka_parent_dir}/kafka_extend.tag ];then
            return 0
        fi

        if [ "X${PM_KAFKA_DISK}" != "X" -a "X${PM_KAFKA_DISK}" != "X{{PM_KAFKA_DISK}}"  ];then
            if [ "${scene_type}" == "docker" ];then
                in_pod_ossadm_execute "source /opt/oss/manager/bin/engr_profile.sh; ipmc_adm -cmd stopapp -app  MQAgent"
            else
                su - ossadm -c 'source /opt/oss/manager/bin/engr_profile.sh; ipmc_adm -cmd stopapp -app  MQAgent'
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "stop MQAgent failed"
                    exit 1
                fi 
            fi
            lvm_rollback_local "${tmp_pm_kafka_path}" "${PM_KAFKA_DISK}"  "vg_dv_kafka" "lv_dv_kafka"
            if [ "${scene_type}" == "docker" ];then
                in_pod_ossadm_execute "source /opt/oss/manager/bin/engr_profile.sh; ipmc_adm -cmd startapp -app  MQAgent"
            else
                su - ossadm -c 'source /opt/oss/manager/bin/engr_profile.sh; ipmc_adm -cmd startapp -app  MQAgent' 
            fi
        fi
    fi
    
}

function umount_db()
{
    local db_dir="/opt/zenith"
    local db_user="dbuser"
    
    log_echo "INFO" "Begin to umount_db ${db_dir} ..."
    killall -9 -u ${db_user} >> ${LOG_FILE} 2>&1
    userdel -rf ${db_user} >> ${LOG_FILE} 2>&1
    ps -ef|grep -w ${db_dir}|grep -v grep|awk '{print $2}'|xargs -i kill -9 {} >> ${LOG_FILE} 2>&1
    
    local time=5
    for i in $(seq 1 ${time});do
        kill_disk_process "${db_dir}"
        umount ${db_dir}
        if [ $? -ne 0 ];then
            log_echo "INFO" "Umount ${db_dir} failed, try again time=$i"
        else
            log_echo "INFO" "Umount ${db_dir} success."
            return 0
        fi
    done
}

function cleanup_prerouting_iptables_lvs()
{
    local med_port="$1"
    local protocol_type="$2"
    
    if ( ! is_empty_param "${APP_FloatIP_IPV6}" );then
        i2k_float_v6_ip=${APP_FloatIP_IPV6}
    elif ( ! is_empty_param "${DVFloatIP_IPV6}" );then
        i2k_float_v6_ip=${DVFloatIP_IPV6}
    else
        i2k_float_v6_ip=""
    fi

    if ( ! is_empty_param "${APP_FloatIP_IPV4}" );then
        i2k_float_v4_ip=${APP_FloatIP_IPV4}
    elif ( ! is_empty_param "${DVFloatIP_IPV4}" );then
        i2k_float_v4_ip=${DVFloatIP_IPV4}
    else
        i2k_float_v4_ip=""
    fi

    OLD_IFS=$IFS
    IFS=$'\n'
    if [ "X${i2k_float_v4_ip}" != "X" ];then
        if [ "X${lvs_vip_ipv4}" != "X" -a "X${lvs_vip_ipv4}" != "X{{LVS_VIP_IPV4}}" ];then
            iptables -w -t nat -D PREROUTING --dst ${i2k_float_v4_ip} -p ${protocol_type} --dport ${med_port} -j DNAT --to-destination ${lvs_vip_ipv4}:${med_port} > /dev/null 2>&1
        else
            prerouting_rules=$(iptables-save |grep -wF ${i2k_float_v4_ip}|grep ${protocol_type}|grep DNAT|grep "${med_port}"|sed 's#-A#-D#')
            for prerouting_rule in ${prerouting_rules};do
                eval iptables -t nat ${prerouting_rule}
            done
        fi
    fi
    if [ "X${i2k_float_v6_ip}" != "X" ];then
        if [ "X${LVS_VIP_IPV6}" != "X" -a "X${LVS_VIP_IPV6}" != "X{{LVS_VIP_IPV6}}" ];then
            ip6tables -t nat -D PREROUTING --dst ${i2k_float_v6_ip} -p ${protocol_type} --dport ${med_port} -j DNAT --to-destination [${LVS_VIP_IPV6}]:${med_port}
        else
            prerouting_rules=$(ip6tables-save |grep ${i2k_float_v6_ip}|grep ${protocol_type}|grep DNAT|grep "${med_port}"|sed 's#-A#-D#')
            for prerouting_rule in ${prerouting_rules};do
                eval ip6tables -t nat ${prerouting_rule}
            done
        fi
    fi
    IFS=$OLD_IFS
    sed -i "/${med_port}/d" /etc/rc.d/rc.local > /dev/null 2>&1
    sed -i "/${med_port}/d" /etc/rc.d/after.local > /dev/null 2>&1
}

function get_log_extend_disk()
{
    log_echo "INFO" "Begin to get_log_extend_disk..."
    DISK_RETURN="{{LOG_EXTEND_DISK}}"

    check_log_disk_has_extend
    if [ $? -ne 0 ]; then
        return 1
    fi
    get_vg_lv_name_by_dir "${local_log_dir}"
    if [ $? -ne 0 ]; then
        return 1
    fi
    ### The last one as the extended disk.
    local grep_result=$(pvscan | grep -w "${log_dir_vg_name}" | awk '{print $2}' | tail -1)
    if [ -z "${grep_result}" ]; then
        log_echo "WARN" "Use pvscan | grep -w "${log_dir_vg_name}" get the last disk failed."
        return 1
    fi
    DISK_RETURN=${grep_result}
}

function pre_config_disk_symbol_clean()
{
    local cfg_file=$1
    local vg_disk_file=$2

    if [ ! -f ${cfg_file} -o ! -f ${vg_disk_file} ]; then
        log_echo "ERROR" "The cfg_file=${cfg_file} or vg_disk_file=${vg_disk_file} is not exists, please check."
        return 1
    fi
    log_echo "INFO" "Begin to find mount disk symbol to ${cfg_file}..."
    local mount_file=/etc/fstab
    if [ ! -f $mount_file ]; then
        log_echo "ERROR" "The /etc/fstab is not exist."
        return 1
    fi
    local vg_name_val=""
    local disk_symbol_key=""
    local pv_name_val=""
    local is_extend_key=0
    while read -r line; do
        vg_name_val=$(echo ${line} | awk -F"=" '{print $1}')
        disk_symbol_key=$(echo ${line} | awk -F"=" '{print $2}')
        if [ -z ${vg_name_val} ]; then
            continue
        fi

        ## EXTEND_DISK not need to handle
        if [ "X${vg_name_val}" == "Xvg_log" ]; then
            continue
        fi

        if [ "X${vg_name_val}" == "Xvg_log_extend" ]; then
            get_log_extend_disk
            update_config ${cfg_file} "${disk_symbol_key}" "${DISK_RETURN}"
            continue
        fi
        
        is_extend_key=0
        if [ "X${vg_name_val}" == "Xvg_zenith_extend" ]; then
            vg_name_val="vg_zenith"
            is_extend_key=1
        elif [ "X${vg_name_val}" == "Xvg_pm_db_extend1" ]; then
            vg_name_val="vg_pm_db1"
            is_extend_key=1
        elif [ "X${vg_name_val}" == "Xvg_pm_db_extend2" ]; then
            vg_name_val="vg_pm_db2"
            is_extend_key=1
        elif [ "X${vg_name_val}" == "Xvg_pm_db_extend" ]; then
            vg_name_val="vg_pm_db"
            is_extend_key=1
        fi

        grep "^/dev/mapper/${vg_name_val}[-].*\#DV_Preset$" ${mount_file}
        if [ $? -ne 0 ]; then
            update_config ${cfg_file} "${disk_symbol_key}" "{{${disk_symbol_key}}}"
            continue
        fi

        if [ $is_extend_key -eq 1 ]; then
            pv_name_val=$(pvscan | grep -w "${vg_name_val}" | awk '{print $2}' | awk '{if (NR==2) {print $0}}')
        else
            pv_name_val=$(pvscan | grep -w "${vg_name_val}" | awk '{print $2}' | awk '{if (NR==1) {print $0}}')
        fi

        if [ -z "${pv_name_val}" ]; then
            update_config ${cfg_file} "${disk_symbol_key}" "{{${disk_symbol_key}}}"
            continue
        fi
        update_config ${cfg_file} "${disk_symbol_key}" "${pv_name_val}"
    done < $vg_disk_file
    
    source ${cfg_file}
    [ $? -eq 0 ] || return 1

}

function remove_device_mapper_of_docker()
{
    which kubectl >/dev/null 2>&1
    if [ $? -eq 0  ]; then
        log_echo "INFO"  "dv is shared with k8s, skip del node after."
        return 0
    fi

    log_echo "INFO"  "del node after cleanup docker."
    log_echo "INFO"  "umount dir list=$(df -Ph |awk '{print $NF}' |grep "${OPT_PATH}/paaslib/")."
    df -Ph |awk '{print $NF}' |grep "${OPT_PATH}/paaslib/" |xargs -i umount {}  >> ${LOG_FILE}  2>&1
    log_echo "INFO"  "umount dir ret=$?"
    log_echo "INFO"  "dmsetup ls = $(dmsetup ls)"
    dmsetup ls |grep "docker-" | grep -v "docker-thinpool" |awk '{print $1}' |xargs -i dmsetup remove {} >> ${LOG_FILE}  2>&1
    log_echo "INFO"  "dmsetup remove,ret=$?"
    log_echo "INFO"  "del node after finish."

    ps -ef | grep docker | grep -v grep >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        log_echo "INFO" "before umount ${OPT_PATH}/paaslib we will stop docker service."
        service docker stop 2>/dev/null
    fi
    remove_overlay2_thinpool
    return 0
}

## 删除k8s残留的overlay2存储的docker逻辑卷
function remove_overlay2_thinpool()
{
    if [ -d ${OPT_PATH}/paaslib/docker/overlay2 ]; then
        log_echo "INFO" "begin to remove docker lvm of overlay2."
        local vg_docker_disks=$(pvscan | grep "[ ]docker[ ]" | awk '{print $2}')
        lvm_rollback_local "${OPT_PATH}/paaslib" "${vg_docker_disks}" "docker" "thinpool"
        local tmp_opt_path=$(echo "${OPT_PATH}" | sed "s#/#\\\/#g")
        sed -i "/[ ]\+$tmp_opt_path\/paaslib[ ]\+/d" /etc/fstab
        systemctl daemon-reload
        log_echo "INFO" "remove docker lvm of overlay2 successfully."
    fi
    return 0
}


function rollback_ossuser_python()
{
    log_echo "INFO" "rollback_ossuser_python start..."
    local filebackup_path=${CURRENT_PATH}/backup/${product_new_version}
    local dv_version_file="/opt/oss/share/SOP/DVEngineeringService/I2000/common/config/CommonInfo.ini"
    sudo -u ossuser bash -c "ls ${dv_version_file}" 2>/dev/null
    if [ $? -ne 0 ];then
        log_echo "INFO" "The dv_version_file=${dv_version_file} is not exist.not need do it."
        return 0
    fi
    
    local dv_version=$(sudo -u ossuser bash -c "cat ${dv_version_file}" |grep "FullVersion" |awk -F'=' '{print $2}'|sed "s/ //g")
    if [ -z "${dv_version}" ];then
        log_echo "INFO" "The dv_version=${dv_version} is null.not need do it."
        return 0
    fi
    
    log_echo "INFO" "The dv_version=${dv_version}"
    sudo -u ossuser bash -c "ls ${filebackup_path}/ossuser_python_${dv_version}/python/bin/python" 2>/dev/null
    local backup_ret=$?
    log_echo "INFO" "execute cmd:[ ls ${filebackup_path}/ossuser_python_${dv_version}/python/bin/python ],backup_ret=$backup_ret"
    sudo -u ossuser bash -c "ls /home/<USER>/python/bin/python" 2>/dev/null
    if [ $? -eq 0 -a ${backup_ret} -eq 0 ];then
        sudo -u ossuser bash -c "rm -rf /home/<USER>/python;cp -rpf ${filebackup_path}/ossuser_python_${dv_version}/python /home/<USER>/"
        log_echo "INFO" "execute cmd:[ rm -rf /home/<USER>/python;cp -rpf ${filebackup_path}/ossuser_python_${dv_version}/python /home/<USER>/ ],ret=$?"
    fi
    log_echo "INFO" "rollback_ossuser_python finished."
}

function cleanup_ipvsadm()
{

    cp_lib_ldlinux

    ipvsadm -l > /dev/null 2>&1
    if [ $? -ne 0 ];then
        cd ${CURRENT_PATH}/LVS/ipvsadm
        unzip -o ipvsadm-*.jar -d ipvsadm
        chmod 755 ${CURRENT_PATH}/LVS/ipvsadm/ipvsadm/bin/ipvsadm/ipvsadm

        ${CURRENT_PATH}/LVS/ipvsadm/ipvsadm/bin/ipvsadm/ipvsadm -l > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ipvsadm does not work, please check"
            exit 1
        fi
        log_echo "INFO" "Cleanup all ipvsadm rule..."
        ${CURRENT_PATH}/LVS/ipvsadm/ipvsadm/bin/ipvsadm/ipvsadm -C >/dev/null 2>&1
        [ $? -ne 0 ] && log_echo "ERROR" "ipvsadm -C execute failed." && exit 1
        rm -rf ${CURRENT_PATH}/LVS/ipvsadm/ipvsadm
    else
        log_echo "INFO" "Cleanup all ipvsadm rule..."
        ipvsadm -C >/dev/null 2>&1
        [ $? -ne 0 ] && log_echo "ERROR" "ipvsadm -C execute failed." && exit 1
    fi
    log_echo "INFO" "cleanup ipvsadm success."
}

function cleanup_fstab()
{
    log_echo "/etc/fstab cleanup now !"
    if [ -f "/etc/fstab" ];then
        [ -f "/etc/fstab_dvbackup" ] && cp -p /etc/fstab_dvbackup /etc/fstab
        rm -f /etc/fstab_dvbackup
        sed -i '/#DV_Preset/{/\/opt\/oss\/log/!d}' /etc/fstab
    fi
    log_echo "INFO" "The /etc/fstab has been cleared! Result = $(grep '.*#DV_Preset$' /etc/fstab)"
    return 0
}

function cleanup_lvs_ip_list()
{
    local lvs_ip_list="$1"
    log_echo "INFO" "cleanup_lvs_ip_list start ... lvs_ip_list=${lvs_ip_list}"
    local tmp_ip=""
    for tmp_ip in ${lvs_ip_list};do
        if ( is_empty_param "${tmp_ip}" );then
            log_echo "INFO" "The tmp_ip=${tmp_ip} is empty.continue"
            continue
        fi
        
        ip addr |grep -wFi "${tmp_ip}" >> ${LOG_FILE} 2>&1
        if [ $? -ne 0 ];then
            log_echo "INFO" "The tmp_ip=${tmp_ip} is not in node.continue"
            continue
        fi
        
        local ip_and_mask=$(ip addr |grep -wFi "${tmp_ip}"|grep "lvs"|awk '{print $2}')
        local nic_name=$(ip addr |grep -wFi "${tmp_ip}"|grep "lvs"|awk '{print $NF}')
        if [ -z "${ip_and_mask}" -o -z "${nic_name}" ];then
            log_echo "INFO" "The ip_and_mask=${ip_and_mask} or nic_name=${nic_name} has null.continue"
            continue
        fi
        
        log_echo "INFO" "The ip_and_mask=${ip_and_mask} and nic_name=${nic_name} del it."
        ip addr del ${ip_and_mask} dev ${nic_name}
        log_echo "INFO" "The ip_and_mask=${ip_and_mask} and nic_name=${nic_name} finished.ret=$?"
    done
    log_echo "INFO" "cleanup_lvs_ip_list End"
}

function cleanup_lvs_ip()
{
    cleanup_lvs_ip_list "${lvs_vip_ipv4} ${lvs_dip_ipv4} ${LVS_VIP_IPV6} ${LVS_DIP_IPV6}"
    local lvs_ip_list=$(ip addr |grep ":lvs$\|:lvs_dip$" |awk '{print $2}'|awk -F'/' '{print $1}')
    cleanup_lvs_ip_list "${lvs_ip_list}"
    lvs_ip_list=$(ip addr |grep ":lvs$\|:lvs_dip$")
    log_echo "INFO" "cleanup_lvs_ip End,lvs_ip_list=${lvs_ip_list}"
}

function rm_network_cfg()
{
    cat /etc/os-release | grep -E "SUSE|Photon" > /dev/null
    if [ $? -eq 0 ]; then
        log_echo "INFO" "SUSE do not need to delete the network cfg"
        return 0
    fi

    for file in $(find /etc/sysconfig/network-scripts -name "*:0.dvtag" ); do
       log_echo "INFO" " The ${file} will be removed"
       rm -rf ${file:?}
       temp_file=$(echo ${file} |awk -F '.' '{print $1}')
       log_echo "INFO" "The ${temp_file} will be removed"
       rm -rf ${temp_file:?}
    done

    systemctl restart network
}

function operation_the_networkmanager()
{
    log_echo "INFO" "Firstly check the networkmanager status"
    systemctl status NetworkManager | grep Active | grep running > /dev/null
    if [ $? -eq 0 ];then
        log_echo "INFO" "The networkmanager is running. Need to stop it."
        systemctl stop NetworkManager
        if [ $? -eq 0 ];then
          log_echo "INFO" "The networkmanager stop SUCCESS."
          return 0
        else
          log_echo "ERROR" "The networkmanager stop FAILED! Please check."
          exit 1
        fi
    else
        log_echo "INFO" "The networkmanager is not running or not installed. No need to operate it."
        return 0
    fi
}

function cp_lib_ldlinux() {
    log_echo "INFO" "Check the /lib64/ld-linux exists"
    ls -l /lib64/ld-linux* > /dev/null
    if [ $? -eq 0 ]; then
        log_echo "INFO" "There has the target file. Skip."
    else
        log_echo "INFO" "There doesnot has the target file. Need to copy."
        cp /lib/ld-linux* /lib64
        if [ $? -eq 0 ]; then
            log_echo "INFO" "The file has been copied."
            return 0
        else
            log_echo "ERROR" "cp /lib/ld-linux* /lib64 failed, please check."
            exit 1
        fi
    fi
}


if [ "X$1" == "Xunset_tmout" ];then
    unset_tmout
    exit 0
elif [ "X$1" == "Xrollback_unset_tmout" ];then
    rollback_unset_tmout
    exit 0
elif [ "X$1" == "Xcleanup_prerouting_iptables_lvs" ];then
    if [ "X$2" != "Xhost" ];then
        if [ -f "/opt/oss/share/SOP/DVEngineeringService/I2000/run/etc/oms.xml" ] ;then
            if ( is_empty_param "${APP_FloatIP_IPV4}" );then
                APP_FloatIP_IPV4=$(grep -n '<param name="ip"' /opt/oss/share/SOP/DVEngineeringService/I2000/run/etc/oms.xml|tail -1|awk -F'>' '{print $2}'|awk -F'<' '{print $1}')
            fi
            if ( is_empty_param "${APP_FloatIP_IPV6}" );then
                APP_FloatIP_IPV6=$(grep -n '<param name="ipv6"' /opt/oss/share/SOP/DVEngineeringService/I2000/run/etc/oms.xml|tail -1|awk -F'>' '{print $2}'|awk -F'<' '{print $1}')
            fi
            cleanup_prerouting_iptables_lvs "${med_snmp_port}" "udp"
            exit 0
        fi
    else
        source ${CURRENT_PATH}/netWorkTypeOfDvConfigParameters.config
        cleanup_prerouting_iptables_lvs "${med_snmp_port}" "udp"
        exit 0
    fi
elif [ "X$1" == "Xrollback_infocollect_file" ];then
    rollback_infocollect "$2"
    exit 0
elif [ "X$1" == "Xrollback_health_checke" ];then
    rollback_health_checke "$2"
    exit 0
fi

if [ "X$1" == "Xpod_ip_clean_for_kylin" ]; then
    operation_the_networkmanager
    rm_network_cfg
    exit 0
fi

if [ "X$1" == "Xremove_device_mapper_of_docker" ]; then
    remove_device_mapper_of_docker
    exit 0
fi

if [ "X$1" == "Xpre_config_disk" ]; then
    pre_config_disk_symbol_clean ${dv_cfg_file}.tmp ${CURRENT_PATH}/pre_get_disk_symbol.properties
    exit 0
fi

if [ "X${is_upgrade}" != "XYes" ];then
    if [ "X$1" == "Xdatabase" ];then
        unsintall_db
    elif [ "X$1" == "Xsshd" ];then
        cleanup_sshd
    elif [ "X$1" == "Xumount_db" ];then
        umount_db
    elif [ "X$1" == "Xrollback_app_lvm" ];then
        rollback_app_lvm "$2" "$3"
    elif [ "X$1" == "Xrollback_db_lvm" ];then
        rollback_db_lvm
    elif [ "X$1" == "Xrollback_backuptmp_lvm" ];then
        rollback_backuptmp_lvm
    elif [ "X$1" == "Xrollback_ntp_config" ];then
        rollback_ntp_config
        rollback_ita_ntpsmart_config
    elif [ "X$1" == "Xcleanup_pod" ];then
        cleanup_pod 
    elif [ "X$1" == "Xcleanup_tmp_file" ];then
        cleanup_tmp_file
        umount_loop_device "${HOME_PATH}/dv/disk_sftpossuser.img" "${HOME_PATH}/dv/sftpossuser"
        umount_loop_device "${OPT_PATH}/share/I2000/run/var/iemp/data/disk_ftp.img"  "${OPT_PATH}/share/I2000/run/var/iemp/data/ftp"
        umount_loop_device "${HOME_PATH}/dv/disk_devdata.img"  "${HOME_PATH}/dv/devdata"
        umount_loop_device "${HOME_PATH}/dv_tmp.img" "${TMP_PATH}/dv_secbox"
        docker_host_del_users
    elif [ "X$1" == "Xrollback_pm_lvm" ];then
        rollback_pm_lvm "$2" "$3"
    elif [ "X$1" == "delete_iptables_dnat" ];then
        delete_iptables_dnat "$2" "$3"
    elif [ "X$1" == "Xkube_firewall_iptables" ];then
        kube_firewall_iptables "$2"
        delete_iptables_DOCKER_USER_lvs
    elif [ "X$1" == "Xcleanup_ipvsadm" ];then
        cleanup_ipvsadm
    elif [ "X$1" == "Xcleanup_fstab" ];then
        cleanup_fstab
    elif [ "X$1" == "Xcleanup_lvs_ip" ];then
        cleanup_lvs_ip
    else
        main $@
    fi
else
    if [ "X$1" == "Xrollbackdatabase" ];then
        Revoke_ossadmin
        drop_new_user
    elif [ "X$1" == "Xrollbackfile" ];then
        sudo -u ossuser test -f  /home/<USER>/jdbc/copy_ojdbc8.tag
        if [ $? -eq 0 -a -d /opt/oss/SOP/apps/DVEngineeringService ];then
            log_echo "INFO" "/home/<USER>/jdbc/copy_ojdbc8.tag is exists."
            sudo -u ossuser rm /home/<USER>/jdbc/copy_ojdbc8.tag
            sudo -u ossuser rm /home/<USER>/jdbc/ojdbc8*jar
        fi
        if [ -f /home/<USER>/product_new_version.tag ];then
            rm -f /home/<USER>/product_new_version.tag
        fi 
        if [ -f /home/<USER>/SD_upgrade.tag ];then
            rm -f /home/<USER>/SD_upgrade.tag
        fi 
        rollback_ossuser_python
        delete_upgrade_preSet_tag
        import_db
        del_outclient_pfx_without_pwd
        rollback_dv_certificate
    elif [ "X$1" == "Xrollback_pm_lvm" ];then
        rollback_pm_lvm "$2" "$3"
        exit 0
    fi
    start_all_service
fi