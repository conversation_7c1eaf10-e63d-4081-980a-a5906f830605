#!/bin/bash
#####################################################
# Description : Only For Preset DV App Node
#####################################################
set +x
CURRENT_PATH=$(cd $(dirname $0); pwd)
. ${CURRENT_PATH}/utils_common.sh ${CURRENT_PATH}
test $? != 0 && exit 1
. ${CURRENT_PATH}/utils_uniep.sh ${CURRENT_PATH}
test $? != 0 && exit 1
. ${CURRENT_PATH}/utils_os.sh ${CURRENT_PATH}
test $? != 0 && exit 1

function init()
{
    import_properties

    root_path=${i2k_install_path}/I2000
    
    checkDNS
    if [ $? -ne 0 ];then
        log_echo "ERROR" "checkDNS failed."
        exit 1
    fi
}

function create_login_user()
{
    id ${system_sftp_user} >/dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "The ${system_sftp_userid} already exited . no creat it ..."
        [ -f "${system_sftp_userhome}/sftpossuser_need_partition" ] && set_user_passwd "${system_sftp_user}" "${sftpossuser_userpasswd}"
    else
        df -h |grep /home/<USER>
        if [ $? -ne 0 ];then
            mount_loop_device "/home/<USER>" "1024" "102400" "/home/<USER>"
        fi
        useradd -g ${i2k_group_id} -u ${system_sftp_userid} -m ${system_sftp_user} -d ${system_sftp_userhome} -s /bin/false || die "Create user failed! Please check by command \"useradd -g ${i2k_group_id} -u ${system_sftp_userid} -m ${system_sftp_user} -d ${system_sftp_userhome} \""
        chage -M 99999 ${system_sftp_user}
        chown sftpossuser:ossgroup /home/<USER>
        mkdir -p /home/<USER>/upload
        chown  sftpossuser:ossgroup  /home/<USER>/upload
        chmod 750 /home/<USER>/upload
        set_user_passwd "${system_sftp_user}" "${sftpossuser_userpasswd}"
    fi
}

function create_user_and_group()
{
    id ${i2k_user_name} >/dev/null 2>&1
    if [ $? -eq 0 ];then
        userdel -rf ${i2k_user_name} >/dev/null 2>&1
    fi
    log_echo "INFO" "Begin to create user and group." 
    
    useradd -g ${i2k_group_id} -u ${i2k_user_id} -m ${i2k_user_name} -d ${i2k_user_home} || die "Create user failed! Please check by command \"useradd -g ${i2k_group_id} -u ${i2k_user_id} -m ${i2k_user_name} -d ${i2k_user_home}  \""
    chage -M 99999 ${i2k_user_name}
    set_user_passwd "${i2k_user_name}" "${i2k_userpasswd}"
    [ ! -f ${i2k_user_home}/.bash_profile -a -f /etc/skel/.bash_profile ] && cp -f /etc/skel/.bash_profile  ${i2k_user_home}/.bash_profile && chown ossuser:ossgroup  ${i2k_user_home}/.bash_profile
    [ ! -f ${i2k_user_home}/.bashrc -a -f /etc/skel/.bashrc ] && cp -f /etc/skel/.bashrc  ${i2k_user_home}/.bashrc && chown ossuser:ossgroup  ${i2k_user_home}/.bashrc

    rm -rf "${i2k_user_home:?}"/.ssh
    create_ossuser_idrsa 
    modify_limits_conf
    
    echo "PATH=\$PATH:/sbin:/usr/sbin" >> ${i2k_user_home}/.profile
    chown ${i2k_user_name}:${i2k_group_name} ${i2k_user_home}/.profile
    if [ "X${node_type}" == "X" ];then
        check_if_install_icnfg && create_icnfg_user_and_group
    fi
    create_login_user
    config_ssh_sftp_seperate
    mkdir -p ${system_sftp_userhome}/tmp
    chown ${i2ksftpuser}:${i2k_group_name} ${system_sftp_userhome}/tmp
    chmod 750 ${system_sftp_userhome}/tmp

    add_sop_user
    set_user_passwd "${sop_user_name}" "${sopuser_userpasswd}"
    
    grep -E "^${onip_group_name}:" /etc/group > /dev/null 2>&1
    if [ $? -ne 0 ];then
        groupadd -g ${onip_group_id} ${onip_group_name}
    fi
    usermod_add_group ${onip_group_name} ${i2k_user_name}
    chmod 750 ${i2k_user_home} 
}

function delete_drbd_sudo()
{
    if [ -z "$(ls -l /dev/drbd0 2>/dev/null)" -a -f "/opt/oss/share/SOP/DVEngineeringService/I2000/bin/i2kadm" ];then
        grep "drbd" /etc/sudoers > /dev/null 2>&1
        if [ $? -eq 0 ];then
            log_echo "INFO" "Delete old drbd sudo."
            chattr -i /home/<USER>/sudoScripts
            chattr -i /home/<USER>/sudoScripts/drbdMonitor.sh
            chattr -i /home/<USER>/sudoScripts/drbd_enable.sh
            chattr -i /home/<USER>/sudoScripts/drbd_disable.sh
            
            rm -f /home/<USER>/sudoScripts/drbdMonitor.sh /home/<USER>/sudoScripts/drbd_enable.sh /home/<USER>/sudoScripts/drbd_disable.sh
            
            delete_old_sudo "${i2k_user_name}" "${sudo_drbd_cmd_list}"
            delete_old_sudo "${i2k_user_name}" "${sudo_drbd_mount_safe}"
        fi
    fi
}

function set_sudo()
{
    log_echo "INFO" "Begin to set sudoers for ${i2k_user_name}." 
    sed -i "s#{{i2k_user}}#$i2k_user_name#g" ${CURRENT_PATH}/DV_config.properties.tmp
    sed -i "s#{{dvshare_user_name}}#$dvshare_user_name#g" ${CURRENT_PATH}/DV_config.properties.tmp
    sed -i "s#{{i2k_group}}#$i2k_group_name#g" ${CURRENT_PATH}/DV_config.properties.tmp
    sed -i "s#{{root_path}}#$i2k_install_path/I2000#g" ${CURRENT_PATH}/DV_config.properties.tmp
    sed -i "s#{{i2k_user_home}}#$i2k_user_home#g" ${CURRENT_PATH}/DV_config.properties.tmp
    sed -i "s#{{i2k_plat_install_path}}#$i2k_install_path#g" ${CURRENT_PATH}/DV_config.properties.tmp
    sed -i "s#{{dev_user}}#${icnfg_devdata_user_name}#g" ${CURRENT_PATH}/DV_config.properties.tmp
    

    . ${CURRENT_PATH}/DV_config.properties.tmp

    if [ "X${is_upgrade}" != "XYes" ]; then
       config_sudo "${i2k_user_name}" "${sudo_cmd_list}"
    fi

    if [ "X${i2k_is_dual}" == "XYes" ]; then
        if [ "X${is_upgrade}" != "XYes" ]; then
            if [ "X${i2k_dual_role}" == "XSecondary" ]; then
                check_if_install_icnfg && build_icnfg_trust "${drbd_secondary_ip}"
            elif [ "X${i2k_dual_role}" == "XPrimary" ]; then
                check_if_install_icnfg && build_icnfg_trust "${drbd_primary_ip}"
            fi
        fi
    else
        if [ "X${is_upgrade}" != "XYes" ]; then
            check_if_install_icnfg && build_icnfg_trust "$1"
        fi
    fi

    if [ "X${is_upgrade}" == "XYes" -a "X${is_install_icnfg}" == "XYes" ];then
        eval "${TEST_FILE} ${i2k_install_path}/icnfg/webpages/WEB-INF/config/config.properties"
        if [ $? -eq 0 ];then
            ${command_prefix} bash -c "grep ^SFTP_SERVICE_PORT ${i2k_install_path}/icnfg/webpages/WEB-INF/config/config.properties > ${i2k_install_path}/I2000/icnfg_old_port"
            ${command_prefix} chown ossuser:ossgroup  ${i2k_install_path}/I2000/icnfg_old_port
        fi
    fi

    if [ "X${i2k_is_dual}" == "XYes" -a "X${is_upgrade}" != "XYes" ]; then
        config_sudo "${i2k_user_name}" "${sudo_cmd_list_ossadm}" "ossadm"
    fi
    
    if [ "X${is_upgrade}" != "XYes" ]; then
        config_sudo "${i2k_user_name}" "${install_path}/manager/agent/DeployAgent/bin/ipmc_adm -cmd restartapp -app HIROERService" "ossadm"

        config_sudo "${i2k_user_name}" "${common_sudo_list}"
        config_sudo "${system_operation_user}" "${sudo_sysomc_list}"
        if [ -f "/.dockerenv"  ];then
            config_sudo "${i2k_user_name}" "${sudo_cmd_in_docker}"
        fi

        if [ -f "/home/<USER>/sudoScripts/grep.sh" ];then
            chattr -i /home/<USER>/sudoScripts 2>/dev/null
            chattr -i /home/<USER>/sudoScripts/grep.sh 2>/dev/null
            rm -f /home/<USER>/sudoScripts/grep.sh
        fi
        if [ -f "/home/<USER>/sudoScripts/verify_asc.sh" ];then
            chattr -i /home/<USER>/sudoScripts 2>/dev/null
            chattr -i /home/<USER>/sudoScripts/verify_asc.sh 2>/dev/null
            rm -f /home/<USER>/sudoScripts/verify_asc.sh
        fi
    fi

    if [ "X${is_upgrade}" != "XYes" ];then
        rm -f /home/<USER>/sudoScripts/drbd_enable.sh /home/<USER>/sudoScripts/drbd_disable.sh /home/<USER>/sudoScripts/kill_installpath.sh

        delete_old_sudo "${i2k_user_name}" "/home/<USER>/sudoScripts/kill_installpath.sh"
    fi

    if [ "X${is_upgrade}" != "XYes" ];then
        change_sudo_permission
    fi
}

function create_dir()
{
    if [ "X${node_type}" == "X" ];then
        log_echo "INFO" "Begin to create dir." 
        mkdir -p /opt/openssh
        touch /etc/profile.d/cie.sh
        touch /etc/profile.d/cie.csh
        touch /etc/profile.d/i2k-env.sh
        touch /etc/profile.d/i2k-env.csh
    fi
}


function change_permission()
{
    log_echo "INFO" "Begin to change permission." 
    
    chown root:root /etc/profile.d/cie.sh /etc/profile.d/cie.csh > /dev/null 2>&1
    
    chmod 644 /etc/profile.d/cie.sh /etc/profile.d/cie.csh > /dev/null 2>&1
    
    chown -R ${i2k_user_name}:${i2k_group_name} /opt/openssh > /dev/null 2>&1

    
    chmod 755 /opt
    chmod 1777 /var/tmp;rm -rf /var/tmp/HSS
    chmod 1777 /tmp
    chmod 755 /var > /dev/null 2>&1
    mkdir -p /var/tmp/.oracle > /dev/null 2>&1
    mkdir -p /tmp/.oracle > /dev/null 2>&1
    chown -R oracle:oinstall /var/tmp/.oracle /tmp/.oracle  > /dev/null 2>&1
    chmod -R 01777 /var/tmp/.oracle /tmp/.oracle > /dev/null 2>&1
}



function set_env_params()
{
    log_echo "INFO" "Begin to set env parameters." 
    echo "export I2000_HOME=${i2k_install_path}" > /etc/profile.d/i2k-env.sh
    echo "export I2000_USER_HOME=${i2k_user_home}" >> /etc/profile.d/i2k-env.sh
    echo "setenv I2000_HOME ${i2k_install_path}" > /etc/profile.d/i2k-env.csh
    echo "setenv I2000_USER_HOME ${i2k_user_home}" >> /etc/profile.d/i2k-env.csh
    chmod 644 /etc/profile.d/i2k-env.csh
    chmod 644 /etc/profile.d/i2k-env.sh
    chmod 644 /etc/sysctl.conf
    chown root:root /etc/profile.d/i2k-env.csh /etc/profile.d/i2k-env.sh
}

function set_system_operation_user()
{
    log_echo "INFO" "Begin to create user ${system_operation_user} ." 
    id ${system_operation_user} > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        log_echo "INFO" "User ${system_operation_user} exist, no need to create . "
        [ -f "${system_operation_userhome}/sysomc_need_partition" ] && set_user_passwd "${system_operation_user}" "${sysomc_userpasswd}"
        if [ ! -d ${system_operation_userhome} ];then
            log_echo "INFO" "${system_operation_userhome} not exist,need to create . "
            mkdir -p ${system_operation_userhome}
            chown ${system_operation_user}:${i2k_group_name} ${system_operation_userhome}
        fi        
    else
        useradd -g ${i2k_group_id} -u ${system_operation_userid} -m ${system_operation_user} -d ${system_operation_userhome}
        chage -M 99999 ${system_operation_user}
        touch ${CURRENT_PATH}/sysomc_need_del
        set_user_passwd "${system_operation_user}" "${sysomc_userpasswd}"
        cp ${CURRENT_PATH}/sysomc_need_del ${system_operation_userhome}
        chown ${system_operation_user}:${i2k_group_name} ${system_operation_userhome}/sysomc_need_del
        chmod 600 ${system_operation_userhome}/sysomc_need_del
    fi
}

function add_path_profile()
{
    PROFILE=/etc/profile.d/cie.sh
    [ -f $PROFILE ] || touch $PROFILE
    chmod 644 $PROFILE > /dev/null 2>&1
    if [ -n "$1" ] ; then
        pkey=$(echo "$1" | cut -d '=' -f 1)
        pvalue=$(echo "$1" | cut -d '=' -f 2)
        if [ -n "$pkey" ] ; then
            sed -i "/ ${pkey}=/d" $PROFILE
            if [ -n "$pvalue" ] ; then
                echo "export ${pkey}=${pvalue}" >> $PROFILE
                export ${pkey}=${pvalue}
            fi
        fi
    fi
    sed 's/export/setenv/g;s/=/ /' $PROFILE > /etc/profile.d/cie.csh
    chmod 644 /etc/profile.d/cie.csh > /dev/null 2>&1
}

function config_profile()
{
    CIE_HOME="${i2k_install_path}/cie"

    add_path_profile "CIE_HOME=${CIE_HOME}"
    add_path_profile "DMU_HOME=${CIE_HOME}/dmu"
    add_path_profile "PKG_HOME=${CIE_HOME}/pkg"
    add_path_profile "SOLUTION_HOME=$PKG_HOME/solution"
    add_path_profile "CIE_BACKUP=${CIE_HOME}/cie_backup"
    add_path_profile "MQ_HOME=${CIE_HOME}/mq"
    add_path_profile "OMU_HOME=${root_path}"
    add_path_profile "BMU_PKG_HOME=$PKG_HOME/solution/pkg/UniAgent"
    add_path_profile "SOLUTION_SRC_HOME=$PKG_HOME/solution/src"
    add_path_profile "PYTHONPATH=${CIE_HOME}/dmu/rpackage/common/py:$PYTHONPATH"
}

function set_ssh_knowns()
{
    if [ -f "${i2k_user_home}/.ssh/id_rsa.pub" ] && [ -f ${system_login_userhome}/.ssh/authorized_keys ];then
        cat ${i2k_user_home}/.ssh/id_rsa.pub >> ${system_login_userhome}/.ssh/authorized_keys
        chown -R ${system_login_user}:${i2k_group} ${system_login_userhome}
        find ${system_login_userhome}/.ssh -type f -exec chmod 600 {} +
    fi
}

function init_user_login()
{
    id ${i2k_user_name} > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        grep -E "^${sshonly_group}:" /etc/group > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            usermod_add_group ${sshonly_group} ${i2k_user_name}
            usermod_add_group ${sshonly_group} ${system_operation_user}
            usermod_add_group ${sshonly_group} ossadm
            if [ $? -ne 0 ]; then
                die "usermod ${sshonly_group} ${i2k_user_name} failed, please check."
            fi
        fi
        
        grep -E "^${sftponly_group}:" /etc/group > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            usermod_add_group ${sftponly_group} ${i2k_user_name}
            if [ $? -ne 0 ]; then
                die "usermod ${sftponly_group} ${i2k_user_name} failed, please check."
            fi
        fi
    fi
    
    
    id ${system_login_user} > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        grep -E "^${sshonly_group}:" /etc/group > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            usermod_add_group ${sshonly_group} ${system_login_user}
            if [ $? -ne 0 ]; then
                die "usermod ${sshonly_group} ${system_login_user} failed, please check."
            fi
        fi
    fi 
    

    id ${icnfg_devdata_user_name} > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        grep -E "^${sshonly_group}:" /etc/group > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            usermod_add_group ${sshonly_group} ${icnfg_devdata_user_name}
            if [ $? -ne 0 ]; then
                die "usermod ${sshonly_group} ${icnfg_devdata_user_name} failed, please check."
            fi
        fi
    fi

}

function copy_i2k_user_home_file()
{
    log_echo "INFO" "Begin to copy sudo files."
    cp -ar ${CURRENT_PATH}/i2kuser_root_script/* ${i2k_user_home} > /dev/null 2>&1

    if [ -d "${CURRENT_PATH}/i2k_cert" -a "X${is_upgrade}" != "XYes" ];then
        mkdir ${i2k_user_home}/i2k_cert
        cp -rf ${CURRENT_PATH}/i2k_cert/openssl_cert ${i2k_user_home}/i2k_cert/
        cp -rf ${CURRENT_PATH}/i2k_cert/autoaccess_cert ${i2k_user_home}/i2k_cert/
        cp -f ${CURRENT_PATH}/i2k_cert/keystore_pwd ${i2k_user_home}/i2k_cert/
        chown ossuser:ossgroup -R ${i2k_user_home}/i2k_cert
        chmod 700 ${i2k_user_home}/i2k_cert ${i2k_user_home}/i2k_cert/openssl_cert ${i2k_user_home}/i2k_cert/autoaccess_cert
        find ${i2k_user_home}/i2k_cert -type f -exec chmod 600 {} +
    fi
    eval "${TEST_DIR} ${i2k_user_home}/nbi_cert"
    local ret=$?
    if [ -d "${CURRENT_PATH}/nbi_cert" -a "X${is_upgrade}" == "XYes" -a $ret -ne 0 ];then
        ${command_prefix} mkdir ${i2k_user_home}/nbi_cert
        sudo -u ossuser bash -c "cp -rf ${CURRENT_PATH}/nbi_cert/nbi* ${i2k_user_home}/nbi_cert"
        if [ "X$(whoami)" == "Xroot" ];then
            chown ossuser:ossgroup -R ${i2k_user_home}/nbi_cert
        fi
        ${command_prefix} chmod 700 ${i2k_user_home}/nbi_cert
        sudo -u ossuser bash -c "find ${i2k_user_home}/nbi_cert -type f -exec chmod 600 {} +"
    fi
    
    if [ "X${is_upgrade}" != "XYes" ]; then
        touch ${i2k_user_home}/i2k_install.properties
        echo "db_instance_path=${db_instance_path}" > ${i2k_user_home}/i2k_install.properties
        sed -i '/_ipv6/d' ${i2k_user_home}/i2k_install.properties
        if [ -n "${APP_Primary_IPV6}" -a "${APP_Primary_IPV6}" != "{{APP_Primary_IPV6}}" ];then
            echo "primary_ipv6=${APP_Primary_IPV6}" >> ${i2k_user_home}/i2k_install.properties
            echo "secondary_ipv6=${APP_Secondary_IPV6}" >> ${i2k_user_home}/i2k_install.properties
        elif [ -n "${DVPrimary_IPV6}" -a "${DVPrimary_IPV6}" != "{{DVPrimary_IPV6}}" ];then
            echo "primary_ipv6=${DVPrimary_IPV6}" >> ${i2k_user_home}/i2k_install.properties
            echo "secondary_ipv6=${DVSecondary_IPV6}" >> ${i2k_user_home}/i2k_install.properties
        fi
        chown ${i2k_user_name}:${i2k_group_name} ${i2k_user_home}/i2k_install.properties
    fi
    chmod 600 ${i2k_user_home}/i2k_install.properties
}

function replace_macro()
{
    ##sudoScripts/backup_restore.sh
    sed -i "s#{{oss_user_group}}#${i2k_user_name}:${i2k_group_name}#g"  ${i2k_user_home}/sudoScripts/backup_restore.sh

    #sudoScripts/kill_installpath.sh
    sed -i "s#{{i2k_install_path}}#${i2k_install_path}#g" ${i2k_user_home}/sudoScripts/kill_installpath.sh

    
    for file_500 in $( find ${i2k_user_home} -name "*" -type f | grep -E '*.sh$' ) ;do
        chmod 500 ${file_500}
    done 
}

function config_ssh()
{
    local listenIpList="$1"

    add_sshd_file "${listenIpList}" "${scene_type}"

    add_sftpd_file "${listenIpList}" "${scene_type}"

    local scene_type=""
    if [ -f /.dockerenv ];then
        scene_type="pod"
        listen_ssh_iplist="${listen_ssh_iplist},127.0.0.1"
        listenIpList="${listen_ssh_iplist}"
    fi

    if [ "X${is_integrated_df_and_service}" == "XYes" ];then
        add_sshd_file "${listen_ssh_iplist}" "${scene_type}"
        add_sftpd_file "${listen_ssh_iplist}" "${scene_type}"
    fi

    update_maxstartups_sshd_config
    
    check_ssh_sftp_seperate
    if [ $? -ne 0 ];then
        sed -i "s/.*Subsystem[ ]\+sftp/#Subsystem sftp/g" /etc/ssh/sshd_config
        sed -i "0,/#Subsystem sftp/s/#Subsystem sftp[ ]\+.*/Subsystem sftp internal-sftp -l VERBOSE -f AUTH/" /etc/ssh/sshd_config
    fi
    
    check_if_install_icnfg
    if [ $? -eq 0 ];then
        sed -i "s/#MACs /MACs /g" /etc/ssh/sshd_config
        cat /etc/ssh/sshd_config | grep "MACs "
        if [ $? -eq 0 ];then
            cat /etc/ssh/sshd_config | grep "MACs " | grep "hmac-sha1"
            if [ $? -ne 0 ];then
                macs_value=$(cat /etc/ssh/sshd_config | grep "MACs " | awk -F ' ' '{print $2}')
                sed -i "s#${macs_value}#hmac-sha1,${macs_value}#g" /etc/ssh/sshd_config
            fi 
        fi
    fi
    
    
    ciphers=$(cat /etc/ssh/ssh_config | grep "^Ciphers " | awk -F ' ' '{print $2}' | head -1)
    if [ "X${ciphers}" != "X" ];then
        echo "${ciphers}" | grep  aes128-cbc
        if [ $? -ne 0 ];then
            log_echo "INFO" "add Cipher aes128-cbc to /etc/ssh/ssh_config"
            sed -i "s#${ciphers}#aes128-cbc,${ciphers}#g" /etc/ssh/ssh_config
        fi
    fi
    
    service sshd restart >/dev/null 2>&1
    if [ $? -ne 0 ];then
        restart_sshd_service
    fi
}

function config_drbd()
{
    log_echo "INFO" "Begin to config drbd."
    sh ${CURRENT_PATH}/DRBD/drbd_install.sh
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Preset drbd in DV node failed"
        exit 1
    fi
}

function create_backup_path()
{
   log_echo "INFO" "Create backup path."
   
   mkdir -p ${install_path}/backuptmp
   [ $? -ne 0 ] &&  log_echo "ERROR" "exc cmd[mkdir -p ${install_path}/backuptmp] failed."
   
   chown ossadm:${i2k_group_name} ${install_path}
   [ $? -ne 0 ] &&  log_echo "ERROR" "exc cmd[chown ossadm:${i2k_group_name} ${install_path}] failed."
   
   chown -R ${i2k_user_name}:${i2k_group_name} ${install_path}/backuptmp
   [ $? -ne 0 ] &&  log_echo "ERROR" "exc cmd[chown -R ${i2k_user_name}:${i2k_group_name} ${install_path}/backuptmp] failed."
   
   chmod 770 ${install_path}/backuptmp
   [ $? -ne 0 ] &&  log_echo "ERROR" "exc cmd[chmod 770 ${install_path}/backuptmp] failed."
   
   log_echo "INFO" "Create backup path finished."
   
}

function adjust_parameter()
{
    log_echo "INFO" "adjust parameter."
    SYSCTLFILE=/etc/sysctl.conf
    [ -d "${TMP_PATH}/DVPreSet" ] && cp ${SYSCTLFILE} ${TMP_PATH}/DVPreSet/sysctl.conf_bak
    [ -d "${TMP_PATH}/DVPreSet_extend" ] && cp ${SYSCTLFILE} ${TMP_PATH}/DVPreSet_extend/sysctl.conf_bak
    
    if [ -d "/proc/sys/net/ipv6/conf" ];then
    
        for nic in $(ls /proc/sys/net/ipv6/conf); 
        do 
            echo ${nic}|grep -E "veth|flannel|cni|docker|br_|gw_|vxlan_sys|dummy" >>/dev/null
            if [ $? -eq 0 ];then
                continue
            fi
            keyLine=$(grep "net.ipv6.conf.${nic}.dad_transmits[ ]*=" ${SYSCTLFILE}|wc -l)
            if [ $keyLine -lt 1 ];then
                echo  "net.ipv6.conf.${nic}.dad_transmits = 0" >> ${SYSCTLFILE}
            else
                sed -i "s/^net.ipv6.conf.${nic}.dad_transmits[ ]*=[^&]*/net.ipv6.conf.${nic}.dad_transmits = 0 /" ${SYSCTLFILE}
            fi
        done
    fi
    
    grep -w "^vm.max_map_count" ${SYSCTLFILE} > /dev/null 2>&1
    if [ $? -eq 0 ];then
        sed -i "s/vm.max_map_count[ ]*=[^&]*/vm.max_map_count = 655360/" ${SYSCTLFILE}
    else
        echo "vm.max_map_count = 655360" >> ${SYSCTLFILE}
    fi
    
    grep -w "^[[:blank:]]*vm.nr_hugepages[[:blank:]]*=.*" ${SYSCTLFILE} > /dev/null 2>&1
    if [ $? -eq 0 ];then
        sed -i "s/^[[:blank:]]*vm.nr_hugepages[[:blank:]]*=.*/vm.nr_hugepages = 0/g" ${SYSCTLFILE}
    else
        echo "vm.nr_hugepages = 0" >> ${SYSCTLFILE}
    fi
    
    if [ -n "`iptables -L PREROUTING -t nat | grep -E \":${med_df_port}|:${med_snmp_port}\"`" ];then
        ip_forward="1"
    elif [ -d "/usr/lib64/dvlvslib" ];then
        ip_forward="1"
    elif  [ "X$(echo ${is_integrated_df_and_service}|tr 'a-z' 'A-Z')" == "XYES" ];then
        ip_forward="1"
    else
        ip_forward="0"
    fi

    grep -w "^[[:blank:]]*net.ipv4.ip_forward[[:blank:]]*=.*" /etc/sysctl.conf > /dev/null 2>&1
    if [ $? -eq 0 ];then
        sed -i "s/^[[:blank:]]*net.ipv4.ip_forward[[:blank:]]*=.*/net.ipv4.ip_forward = ${ip_forward}/g" /etc/sysctl.conf
    else
        echo "net.ipv4.ip_forward = ${ip_forward}" >> /etc/sysctl.conf
    fi
    
    adjust_pid_max

    sysctl -p >/dev/null 2>&1
}

function backup_files()
{
    filebackup_path=${CURRENT_PATH}/backup

    log_echo "INFO" "Begin to backup user and group"
    all_users=${i2k_user_name},$system_login_user,$system_sftp_user,devdata
    all_grps=$i2k_group_name
    mkdir -p ${CURRENT_PATH}/backup
    chown ossuser:ossgroup ${CURRENT_PATH}/backup
    rm -rf $filebackup_path/user_grp
    mkdir -p $filebackup_path/user_grp
    
    for user_name in $(echo $all_users | sed 's/,/ /g')
    do
        id ${user_name} >/dev/null 2>&1
        if [ $? -eq 0 ];then
            cat /etc/passwd | grep "^${user_name}:" >> $filebackup_path/user_grp/user_pwd.backup
            cat /etc/shadow | grep "^${user_name}:" >> $filebackup_path/user_grp/user_shadow.backup
            cat /etc/sudoers | grep "^${user_name} " >> $filebackup_path/user_grp/user_sudo.backup
            user_home=$(cat /etc/passwd | grep "^${user_name}:" | awk -F':' '{print $6}')
            rm -rf $filebackup_path/user_grp/${user_name}
            cp -rfp $user_home $filebackup_path/user_grp/${user_name} > /dev/null 2>&1
            id ${user_name} >> $filebackup_path/user_grp/user_group.backup
        fi
    done
    
    for grp_name in $(echo $all_grps | sed 's/,/ /g')
    do
        cat /etc/group | grep "^${grp_name}:" |uniq >> $filebackup_path/user_grp/group.backup
    done
    
    cat /etc/sudoers | grep "^sysomc " >> $filebackup_path/user_grp/user_sudo.backup
    
    log_echo "INFO" "Begin to backup attr"
    > "${filebackup_path}"/attr_bak
    for dir_name in "${i2k_user_home}" "${root_path}" "${root_path}/../cie"
    do
        lsattr -R "${dir_name}"  2>/dev/null | grep -v "^$" | grep -v "^${dir_name}" | grep -v "^---------------" | grep -v "^....[^i]" >> ${filebackup_path}/attr_bak
    done
    
    if [ -d "${install_path}/SOP/etc/ssl/dv" ];then
        rm -rf ${filebackup_path}/SOP_dv
        cp -rp ${install_path}/SOP/etc/ssl/dv ${filebackup_path}/SOP_dv
    fi
    
    if [ -d "${i2k_install_path}/etc/ssl/dv" ];then
        rm -rf ${filebackup_path}/DVEngineeringService_dv
        cp -rp ${i2k_install_path}/etc/ssl/dv ${filebackup_path}/DVEngineeringService_dv
    fi
    
    if [ -d "${install_path}/SOP/etc/sign_dv" ];then
        rm -rf ${filebackup_path}/SOP_sign_dv
        cp -rp ${install_path}/SOP/etc/sign_dv ${filebackup_path}/SOP_sign_dv
    fi
}

function check_ossuser_trust()
{
    ip addr | grep -w "${drbd_primary_ip}" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        peer_node_ip=${drbd_secondary_ip}
    fi
    ip addr | grep -w "${drbd_secondary_ip}" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        peer_node_ip=${drbd_primary_ip}
    fi
    
    if [ "X${peer_node_ip}" == "X" ];then
        log_echo "ERROR" "peer_node_ip is null , please check"
        exit 1
    fi

    user="ossuser"
    expect << EOF  >/dev/null  2>&1
set timeout -1
spawn sudo -u ${user} bash -c ". /home/<USER>/.bashrc;ssh -o StrictHostKeyChecking=no -p ${i2k_ssh_port} ${user}@${peer_node_ip} echo oOK"
expect   {
"yes/no*" {send "yes\r";exp_continue}
        "*word:" {exit 1}
"oOK"  {exit 0}
eof
}
exit 1
EOF
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Check ${user}'s trust relation to ${peer_node_ip} failed, please check"
        exit 1
    fi
    
    return 0
}

function config_ntp()
{
    ## 宿主机配置NTP
    log_echo "INFO" "Begin to config_ntp." 
    if [ ! -z "${NTP_server_IP}" ];then
        if [ -f "/etc/ntp.conf" ];then
            if [ ! -f "/etc/ntp.conf.bak_dv" ];then
                log_echo "INFO" "Backup ntp.conf to /etc/ntp.conf.bak_dv"
                cp -rf /etc/ntp.conf /etc/ntp.conf.bak_dv
            fi
            
            line=$(sed -n '/restrict/=' /etc/ntp.conf | tail -n 1)
            if [ -n $line ]; then
                line=$(cat /etc/ntp.conf | wc -l)
            fi
            grep "restrict ::1" /etc/ntp.conf >/dev/null 2>&1
            if [ $? != 0 ]; then
                sed -i "$line a\restrict ::1" /etc/ntp.conf
            fi
            grep "restrict 127.0.0.1" /etc/ntp.conf >/dev/null 2>&1
            if [ $? != 0 ]; then
                sed -i "$line a\restrict 127.0.0.1" /etc/ntp.conf
            fi
            
            local tmp_ntp_ip=$(echo "${NTP_server_IP}"|sed "s/\./\\\./g")
            ip addr |grep -w "${tmp_ntp_ip}" >> $LOG_FILE 2>&1
            if [ $? -eq 0 ];then
                log_echo "INFO" "This ntp server node.not need to config_ntp.skip it." 
                return 0
            fi
            
            echo "" >> /etc/ntp.conf
            sed -i "/dv_ntpserver/d" /etc/ntp.conf >> $LOG_FILE 2>&1
            echo "server ${NTP_server_IP} minpoll 2 maxpoll 4 prefer #dv_ntpserver" >> /etc/ntp.conf
            
            rcntpd restart >> $LOG_FILE 2>&1
            systemctl enable ntpd >> $LOG_FILE 2>&1
            ntpq -p >> $LOG_FILE 2>&1
        else
            log_echo "INFO" "/etc/ntp.conf  is not exist,just skip the step ..." 
        fi
    fi
    log_echo "INFO" "config_ntp end." 
    return 0
}

function config_ssh_sftp()
{
    log_echo "INFO" "Begin to config_ssh_sftp."
    local node_type=$1
    if [ "X${node_type}" == "XPrimaryNode" ];then
        add_sshd_file "${primary_ip_list}" "docker"
        add_sftpd_file "${primary_ip_list}" "docker"
    elif [ "X${node_type}" == "XSecondaryNode" ];then
        add_sshd_file "${secondary_ip_list}" "docker"
        add_sftpd_file "${secondary_ip_list}" "docker"
    elif [ "X${node_type}" == "XThirdNode" ];then
        add_sshd_file "${third_ip_list}" "docker"
        add_sftpd_file "${third_ip_list}" "docker"
    elif [ "X${node_type}" == "XAllinONE" ];then
        add_sshd_file "${DVnode_ip_list}" "docker"
        add_sftpd_file "${DVnode_ip_list}" "docker"
    elif [[ "${node_type}" =~ ExtendCluster[0-9] ]];then
        local extend_ip="$2"
        add_sshd_file "${extend_ip}" "docker"
        add_sftpd_file "${extend_ip}" "docker"
    fi
    
    return 0
}

function config_sshd_sftpd_file()
{
    log_echo "INFO" "config_sshd_sftpd_file start..."
    local sshd_file="${OPT_PATH}/dv_os_config/sshd_config"
    local sftpd_file="${OPT_PATH}/dv_os_config/sftpd_config"
    local pod_ip_list="$1"
    local node_type="$2"

    if [ "X${pre_install_key}" == "XPreInstall" ] && [ "X${node_type}" == "XPrimaryNode" -o "X${node_type}" == "XSecondaryNode" ]; then
        log_echo "INFO" "The pre_install_key=${pre_install_key}, config ssh file with DenyUsers ossuser."
        echo "DenyUsers ossuser #DV_PreSet_Pod_DenyUsers" >> ${sshd_file} 2>/dev/null 2>&1
    fi

    sed -i "/^ListenAddress .*/d" ${sshd_file} 2>/dev/null 2>&1
    sed -i "/^ListenAddress .*/d" ${sftpd_file} 2>/dev/null 2>&1
    
    sed -i "/DV_PreSet_Pod_ListenAddress/d" ${sshd_file} 2>/dev/null 2>&1
    sed -i "/DV_PreSet_Pod_ListenAddress/d" ${sftpd_file} 2>/dev/null 2>&1

    sed -i "s/.*Subsystem\s\+.*/#Subsystem sftp internal-sftp -l VERBOSE -f AUTH/g" ${sshd_file}
    
    local sshd_lastLine=$(grep -n "ListenAddress.*" ${sshd_file} |tail -n 1|awk -F':' '{print $1}')
    if [ -z "${sshd_lastLine}" ];then
        sshd_lastLine=1
    fi
    local isNumber=$(echo "${sshd_lastLine}" |grep [^0-9] >/dev/null  && echo "No" || echo "Yes")
    if [ "${isNumber}" == "No" ];then
        sshd_lastLine=1
    fi
    
    sed -i "s/.*MaxStartups.*/MaxStartups 100/g" ${sftpd_file}
    cat ${sftpd_file} |grep -w "^MaxStartups" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        sed -i "1 i MaxStartups 100" ${sftpd_file}
    fi

    sftpd_line=$(sed -n '/^Port/=' ${sftpd_file}|head -n 1)
    
    if [ -z "${pod_ip_list}" ];then
        log_echo "INFO" "The pod_ip_list=${pod_ip_list} is None!"
    else
        for listenIp in $(echo "${pod_ip_list}"|sed "s|,| |g")
        do
            if [ -z "${listenIp}" ];then
                continue
            fi
            
            check_ip "${listenIp}" || continue
            
            listenIp=$(echo "${listenIp}"|sed "s/\./\\\./g")
            exist_ip=$(cat ${sshd_file} |grep -w "${listenIp}" |grep "^[ ]*ListenAddress" )
            if [ "X${exist_ip}" == "X" ]; then
                sed -i "${sshd_lastLine} a ListenAddress ${listenIp} #DV_PreSet_Pod_ListenAddress" ${sshd_file}
            fi
            
            exist_ip=$(cat ${sftpd_file} |grep -w "${listenIp}" |grep "^[ ]*ListenAddress" )
            if [ "X${exist_ip}" == "X" ]; then
                if [ -n $sftpd_line ];then
                    sed -i "$sftpd_line a\ListenAddress $listenIp #DV_PreSet_Pod_ListenAddress" ${sftpd_file}
                else
                    echo "ListenAddress $listenIp #DV_PreSet_Pod_ListenAddress" >> ${sftpd_file}
                fi
            fi
        done
    fi

    sshusr_start_line=$(sed -n '/Match user sshusr/='  ${sftpd_file})
    if [ "X${sshusr_start_line}" != "X" ];then
        next_lines=0
        sed -n "/Match user sshusr/{n;p}"  ${sftpd_file}|grep -E "ForceCommand|ChrootDirectory"
        if [ $? -eq 0 ];then
            next_lines=1
        fi
        sed -n "/Match user sshusr/{n;n;p}" ${sftpd_file}|grep -E "ForceCommand|ChrootDirectory"
        if [ $? -eq 0 ];then
            next_lines=$((next_lines+1))
        fi
        sed -i "/Match user sshusr/,+${next_lines}d"  ${sftpd_file}
    fi

    local ret=$(cat ${sshd_file}|grep "ListenAddress")
    log_echo "INFO" "ret=${ret}"
    log_echo "INFO" "config_sshd_sftpd_file finished."
}

function config_ssh_sftp_for_pod()
{
    log_echo "INFO" "Begin to config_ssh_sftp_for_pod."
    local node_type=$1
    if [ "X${node_type}" == "XPrimaryNode" ];then
        log_echo "INFO" "Begin to config_ssh_sftp_for_pod.PrimaryNode"
        config_sshd_sftpd_file "${pod_primary_ip_list}" "PrimaryNode"
    elif [ "X${node_type}" == "XSecondaryNode" ];then
        log_echo "INFO" "Begin to config_ssh_sftp_for_pod.SecondaryNode"
        config_sshd_sftpd_file "${pod_secondary_ip_list}" "SecondaryNode"
    elif [ "X${node_type}" == "XThirdNode" ];then
       log_echo "INFO" "Begin to config_ssh_sftp_for_pod.ThirdNode"
        config_sshd_sftpd_file "${pod_third_ip_list}"
    elif [ "X${node_type}" == "XAllinONE" ];then
        log_echo "INFO" "Begin to config_ssh_sftp_for_pod.AllinONE"
        config_sshd_sftpd_file "${pod_ip_list}"
    elif [[ "${node_type}" =~ ExtendCluster[0-9] ]];then
        local pod_ip_list="$2"
        log_echo "INFO" "Begin to config_ssh_sftp_for_pod.${node_type}"
        config_sshd_sftpd_file "${pod_ip_list}"
    fi
}

function pod_start_before()
{
    log_echo "INFO" "pod_start_before start..."
    mkdir -p ${OPT_PATH}/dv_os_config
    chmod 750 ${OPT_PATH}/dv_os_config
    if [ -f ${CURRENT_PATH}/repair_iptables.sh -a -f ${CURRENT_PATH}/repair_iptables_docker.sh ];then
        log_echo "INFO" "Begin to copy repair_iptables.sh repair_iptables_docker.sh."
        cp -rfp ${CURRENT_PATH}/repair_iptables.sh ${OPT_PATH}/dv_os_config/repair_iptables.sh >> $LOG_FILE 2>&1
        cp -rfp ${CURRENT_PATH}/repair_iptables_docker.sh ${OPT_PATH}/dv_os_config/repair_iptables_docker.sh >> $LOG_FILE 2>&1
        chmod 700 ${OPT_PATH}/dv_os_config/repair_iptables.sh
        chmod 700 ${OPT_PATH}/dv_os_config/repair_iptables_docker.sh
    fi
    log_echo "INFO" "pod_start_before End."
}

function set_histsize()
{
    local file_name="$1"
    if [ -f /etc/${file_name} ];then
        log_echo "INFO" "Begin  copy /etc/${file_name} to ${OPT_PATH}/dv_os_config."
        cp -rfp /etc/${file_name} ${OPT_PATH}/dv_os_config >> $LOG_FILE 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "cp -rfp /etc/${file_name} ${OPT_PATH}/dv_os_config failed."
            exit 1
        fi
        
        [ "${file_name}" == "bashrc" ] && echo "export TMOUT=900" >> ${OPT_PATH}/dv_os_config/${file_name}
        [ "${file_name}" == "profile" ] && sed -i "/^alias[[:blank:]]\+.*=.*/d" ${OPT_PATH}/dv_os_config/${file_name}
        sed -i "/export HISTSIZE=.*/d" ${OPT_PATH}/dv_os_config/${file_name}
        echo "" >> ${OPT_PATH}/dv_os_config/${file_name}
        echo "export HISTSIZE=1000" >> ${OPT_PATH}/dv_os_config/${file_name}
        echo "umask 0027" >> ${OPT_PATH}/dv_os_config/${file_name}

        if [ "${file_name}" == "profile" ]; then
            echo "alias ll='ls -l'" >> ${OPT_PATH}/dv_os_config/${file_name}
            echo "alias iptables='/usr/sbin/iptables -w'" >> ${OPT_PATH}/dv_os_config/${file_name}
            ## 注释/etc/profile 中的readonly HISTSIZE, 防止source /etc/profile 报错退出
            sed -i "s/^readonly[ ]*HISTSIZE/#readonly HISTSIZE/g" ${OPT_PATH}/dv_os_config/${file_name}
        fi
        log_echo "INFO" "modify ${OPT_PATH}/dv_os_config/${file_name} finished."
    else
        log_echo "INFO" "The /etc/${file_name} is not exist."
    fi
}

function copy_osfile()
{
    log_echo "INFO" "Begin to copy_osfile.nodetype=$1"
    local dv_os_config=${OPT_PATH}/dv_os_config
    mkdir -p ${dv_os_config}
    if [ -f /etc/ssh/sshd_config ];then
        log_echo "INFO" "Begin to copy sshd_config."
        cp -rfp /etc/ssh/sshd_config ${OPT_PATH}/dv_os_config >> $LOG_FILE 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "cp -rfp /etc/ssh/sshd_config ${OPT_PATH}/dv_os_config failed."
            exit 1
        fi
        
        if [ -f ${OPT_PATH}/dv_os_config/sshd_config ];then
            log_echo "INFO" "Begin to modify sshd_config."
            sed -i "/AllowGroups.*sshonly/d" ${OPT_PATH}/dv_os_config/sshd_config
            check_os_suse_arm
            if [ $? -eq 0 ]; then
                log_echo "INFO" "update dv_os_config/sshd_config to match ARM+SUSE."
                sed -i "s/.*\(KexDHMin.*\)/#\1/g" ${OPT_PATH}/dv_os_config/sshd_config
                sed -i "s/.*PermitRootLogin.*yes/PermitRootLogin yes/g" ${OPT_PATH}/dv_os_config/sshd_config
            fi
        fi
    fi
    
    if [ -f ${CURRENT_PATH}/tools/docker/start_dv_docker.sh ];then
        log_echo "INFO" "Begin to copy start_dv_docker.sh."
        cp -rfp ${CURRENT_PATH}/tools/docker/start_dv_docker.sh ${OPT_PATH}/dv_os_config/dv_docker >> $LOG_FILE 2>&1
    fi
    
    if [ -f /etc/ssh/sftpd_config ];then
        log_echo "INFO" "Begin to copy sftpd_config."
        cp -rfp /etc/ssh/sftpd_config ${OPT_PATH}/dv_os_config >> $LOG_FILE 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "cp -rfp /etc/ssh/sftpd_config ${OPT_PATH}/dv_os_config failed."
            exit 1
        fi
    else
        local dv_os_sftpd_config_file=${OPT_PATH}/dv_os_config/sftpd_config
        cp -rfp /etc/ssh/sshd_config ${dv_os_sftpd_config_file} >> $LOG_FILE 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "cp -rfp /etc/ssh/sshd_config ${OPT_PATH}/dv_os_config failed."
            exit 1
        fi
        sed -i "/AllowGroups/d" ${dv_os_sftpd_config_file}
        echo "AllowGroups  sftponly" >> ${dv_os_sftpd_config_file}
        sed -i "/Port/d" ${dv_os_sftpd_config_file}
        echo "Port  20022" >> ${dv_os_sftpd_config_file}
        sed -i "/Subsystem/d" ${dv_os_sftpd_config_file}
        echo "Subsystem   sftp   internal-sftp   -l    VERBOSE -f      AUTH    -u      077" >> ${dv_os_sftpd_config_file}

    fi

    if [ "X${IS_INSTALL_ICNFG}" == "XYes" ];then
        grep "Match User devdata" ${dv_os_sftpd_config_file} >/dev/null 2>&1
        if [ $? -ne 0 ];then
            echo "Match User devdata" >> ${dv_os_sftpd_config_file}
            echo "  ForceCommand internal-sftp -l VERBOSE -f AUTH" >> ${dv_os_sftpd_config_file}
            echo "  ChrootDirectory /home/<USER>" >> ${dv_os_sftpd_config_file}
            echo "  AllowTcpForwarding no" >> ${dv_os_sftpd_config_file}
            echo "  AllowAgentForwarding no" >> ${dv_os_sftpd_config_file}
            echo "  PasswordAuthentication no" >> ${dv_os_sftpd_config_file}
            echo "  X11Forwarding no" >> ${dv_os_sftpd_config_file}
        fi
    fi
  
    readlink /etc/localtime > ${OPT_PATH}/dv_os_config/time_link
    
    [ -f "${CURRENT_PATH}/root_shadow" ] && cp -rpf ${CURRENT_PATH}/root_shadow ${OPT_PATH}/dv_os_config/root_shadow
    
    config_ssh_sftp_for_pod "$1" "$2"
    
    adjust_parameter
    
    adjust_sysctl "net.ipv4.ip_forward"  "1"
    
    set_histsize "profile"
    
    set_histsize "bashrc"
    
    service docker restart 
    
    log_echo "INFO" "success copy_osfile.nodetype=$1."
    
    return 0
}
function chown_dbuser()
{
    log_echo "INFO" "Begin to excute chown dbuser:dbgroup -R /opt/zenith"
    ps -ef |grep dv_docker|grep -v grep > /dev/null
    if [ $? -eq 0  ];then
        pod_id=$(docker ps |grep "dv-container" |awk '{print $1}')
        docker exec -i ${pod_id} /bin/bash -c "chown dbuser:dbgroup -R /opt/zenith"
    else
        id dbuser >/dev/null 2>&1
        if [ $? -eq 0 ];then
            log_echo "INFO" "The dbuser already exited . no creat it ..."
        else
            log_echo "INFO" "Begin to creat dbuser"
            useradd -g 1999 -u 3002 -m dbuser -d /home/<USER>/bin/bash || die "Create user failed! Please check by command \"useradd -g 1999 -u 3002 -m dbuser -d /home/<USER>""
            chage -M 99999 dbuser
            chown  dbuser:dbgroup /home/<USER>
        fi
        chown dbuser:dbgroup -R /opt/zenith
        if [ $? -ne 0 ];then
           die "dbuser is exist, but excute chown dbuser:dbgroup -R /opt/zenith failed, please check"
        fi
        log_echo "INFO" "excute chown dbuser:dbgroup -R /opt/zenith success"
    fi
    
}

function mount_opt_passlib() {
    local mount_paaslib_tag=0
    check_empty_param_of_disk "PAAS_LIB_DISK" "${PAAS_LIB_DISK}"
    if [ $? -ne 0 ]; then
        ## os recover
        if [ "X${node_type}" == "Xos_recover_node" ]; then
            mount_paaslib_tag=1
        fi
        ## k8s is not install on the node
        which kubectl >/dev/null 2>&1
        if [ $? -ne 0 ]; then
            log_echo "INFO"  "dv is not shared with k8s, mount paaslib."
            mount_paaslib_tag=1
        else
            mount_paaslib_tag=0
        fi
    fi
    if [ $mount_paaslib_tag -eq 1 ]; then
        check_local_disk "/opt/paaslib" "${PAAS_LIB_DISK}" "${PAAS_LIB_DISK_least_size}" "vg_paaslib"
        if [ $? -ne 0 ]; then
            log_echo "ERROR" "Check /opt/paaslib of PAAS_LIB_DISK=${PAAS_LIB_DISK} failed, please check..."
            exit 1
        fi
        if [ "${AUTO_DISK_MOUNT}" == "TRUE" ]; then
            auto_mount_local "/opt/paaslib" "${PAAS_LIB_DISK}" "${PAAS_LIB_DISK_least_size}" "vg_paaslib" "lv_paaslib"
        fi
    fi
    log_echo "INFO" "Mounted paaslib on node ${node_type} finish."
}

function auto_mount_disk()
{
    log_echo "INFO" "Begin to prehandle." 
    
    node_type=$1
    scene_type=$2
    
    if [ "${scene_type}" == "extend" -a -f "${ATAE_extend_script}" ];then
        sed -i "s#EXTEND_98G_DISK=.*#EXTEND_98G_DISK=#g" ${UTILS_PATH}/DV_config.properties.tmp
    fi

    import_properties

    if [ "X${scene_type}" == "Xpaaslib" ]; then
        mount_opt_passlib
        return 0
    fi
    check_dir_size

    local_check_ret=0
    if [ "X${node_type}" == "XM:UNIEP:PUB" ];then
        check_local_disk "${OPT_PATH}/pub" "${UNIEP_PUB_DISK}" "${UNIEP_PUB_least_size}" "vg_uniep"
        [ $? -ne 0 ] && local_check_ret=1
        if [ ${local_check_ret} -ne 0 ];then
            log_echo "ERROR" "Check ${install_path}/pub failed"
            return 1
        fi
        systemctl daemon-reload
        if [ "${AUTO_DISK_MOUNT}" == "TRUE" ];then
            if [ "X${UNIEP_PUB_DISK}" != "X" -a "X${UNIEP_PUB_DISK}" != "X{{UNIEP_PUB_DISK}}" ];then
                auto_mount_local "${OPT_PATH}/pub" "${UNIEP_PUB_DISK}" "${UNIEP_PUB_least_size}" "vg_uniep" "lv_uniep"
            fi
        fi
        return 0
    fi
    log_echo "INFO" "Begin to check extend disk at DV node."
    ext_check_ret=0
    if [ "X${node_type}" != "Xall_in_one" ];then
        check_ext_disk
        ext_check_ret=$?
    fi

    if [ "X${i2k_is_dual}" != "XYes" -o "X${need_drbd}" == "XNO" ] && [ "X${node_type}" == "X" -o "X${node_type}" == "Xall_in_one" ];then
        if [ "X${i2k_is_dual}" == "XYes" ];then
            AUTO_DISK_MOUNT_old=${AUTO_DISK_MOUNT}
            AUTO_DISK_MOUNT="TRUE"
        fi

        if [ "${scene_type}" == "docker" ];then
            check_local_disk "${OPT_PATH}/share" "${APPNode_DISK}" "${APP_DISK_least_size}" "vg_dv"
        else
            check_local_disk "${i2k_install_path}" "${APPNode_DISK}" "${APP_DISK_least_size}" "vg_dv"
        fi
        [ $? -ne 0 ] && local_check_ret=1

        if [ "X${i2k_is_dual}" == "XYes" ];then
            AUTO_DISK_MOUNT=${AUTO_DISK_MOUNT_old}
        fi
    else
        local_check_ret=0
    fi
    
    if [ "X${node_type}" == "X" -o "X${node_type}" == "Xall_in_one" ];then
        check_empty_param_of_disk "TRACE_DISK" "${TRACE_DISK}"
        if [ $? -eq 0 ];then
           log_echo "INFO" "The TRACE_DISK is null.not need to check it."
        else
           if [ "${scene_type}" == "docker" ];then
               check_local_disk "${OPT_PATH}/share/trace" "${TRACE_DISK}" "${TRACE_DISK_least_size}" "vg_trace"
           else
               check_local_disk "${install_path}/share/SOP/DVEngineeringService/trace" "${TRACE_DISK}" "${TRACE_DISK_least_size}" "vg_trace"
           fi
           [ $? -ne 0 ] && local_check_ret=1
        fi
    fi
    
    if [ "X${node_type}" == "X" -o "X${node_type}" == "Xall_in_one" ];then
        if [ "X${i2k_is_dual}" == "XYes" ];then
            AUTO_DISK_MOUNT_old=${AUTO_DISK_MOUNT}
            AUTO_DISK_MOUNT="TRUE"
        fi
        check_local_disk "${OPT_PATH}/oss/hofs" "${HOFS_DISK}" "${HOFS_DISK_least_size}" "vg_hofs"
        [ $? -ne 0 ] && local_check_ret=1
        if [ "X${i2k_is_dual}" == "XYes" ];then
            AUTO_DISK_MOUNT=${AUTO_DISK_MOUNT_old}
        fi  
    fi
    
    if [ "X${node_type}" == "XSA" ];then
        check_empty_param_of_disk "VSINDEX_DISK" "${VSINDEX_DISK}"
        local tmp_check_ret_vs=$?
        if [ $tmp_check_ret_vs -eq 0 ] && [ -f ${CURRENT_PATH}/DV_Config_MergeCluster.config ];then
            mkdir -p ${HOME_PATH}/vsindex
            chmod 755 ${HOME_PATH}/vsindex
            log_echo "INFO" "The VSINDEX_DISK is null and network type is Merge Cluster.not need to check it."
        else
            check_local_disk "${HOME_PATH}/vsindex" "${VSINDEX_DISK}" "${VS_DISK_least_size}" "vg_vsindex"
            [ $? -ne 0 ] && local_check_ret=1
        fi
    fi
    
    if [ "X${node_type}" == "XOM" ];then
        check_empty_param_of_disk "SRVBigData_DISK" "${SRVBigData_DISK}"
        local tmp_check_ret_srv=$?
        if [ $tmp_check_ret_srv -eq 0 ] && [ -f ${CURRENT_PATH}/DV_Config_MergeCluster.config ];then
            mkdir -p /srv/BigData
            chmod 755 /srv/BigData
            log_echo "INFO" "The SRVBigData_DISK is null and network type is Merge Cluster.not need to check it."
        elif [ $tmp_check_ret_srv -eq 0 ] && [ "X${dv_deploy_scale_size}" == "Xsmall" ]; then
            log_echo "INFO" "This is dv_deploy_scale_size=${dv_deploy_scale_size} and SRVBigData_DISK is null, not need to check it."
        else
            check_local_disk "${KAFKA_PATH}" "${SRVBigData_DISK}" "${KAFKA_DISK_least_size}" "vg_kafka"
            [ $? -ne 0 ] && local_check_ret=1
        fi
    fi
    
    if [[ ${node_type} =~ ^FLUME.* ]];then
        check_local_disk "${KAFKA_PATH}" "${SRVBigData_DISK}" "${KAFKA_DISK_least_size}" "vg_kafka"
        [ $? -ne 0 ] && local_check_ret=1
    fi
    
    if [[ ${node_type} =~ ^VS.* ]];then
        check_local_disk "${VS_PATH}" "${VSINDEX_DISK}" "${VS_DISK_least_size}" "vg_vsindex"
        [ $? -ne 0 ] && local_check_ret=1
    fi
    
    if [ "X${node_type}" == "XSD" -o "X${node_type}" == "XSD1" -o "X${node_type}" == "XSD2" ];then
        check_local_disk "/opt/zenith" "${SDNode_DB_DISK}" "${DBNode_DISK_least_size}" "vg_sd_db"
        [ $? -ne 0 ] && local_check_ret=1
        check_local_disk "/opt/oss/backuptmp" "${SDNode_BACKUP_TMP_DISK}" "10" "vg_sd_dvbackuptmp"
        [ $? -ne 0 ] && local_check_ret=1
    fi
    
    if [ "X${SA_Cluster_Node_IPV4_List}" != "X" -a "X${SA_Cluster_Node_IPV4_List}" != "X{{SA_Cluster_Node_IPV4_List}}" ] && [[ ${node_type} =~ ^SA.* ]];then
        check_local_disk "${VS_PATH}" "${VSINDEX_DISK}" "${VS_DISK_least_size}" "vg_vsindex"
        [ $? -ne 0 ] && local_check_ret=1
    elif [ "X${SA_Cluster_Node_IPV6_List}" != "X" -a "X${SA_Cluster_Node_IPV6_List}" != "X{{SA_Cluster_Node_IPV6_List}}" ] && [[ ${node_type} =~ ^SA.* ]];then
        check_local_disk "${VS_PATH}" "${VSINDEX_DISK}" "${VS_DISK_least_size}" "vg_vsindex"
        [ $? -ne 0 ] && local_check_ret=1
    fi
    
    local_check_ret1=0
    if [ "X${OM_Cluster_Node_IPV4_List}" != "X" -a "X${OM_Cluster_Node_IPV4_List}" != "X{{OM_Cluster_Node_IPV4_List}}" ] && [[ ${node_type} =~ ^OM.* ]];then
        check_local_disk "${VS_PATH}" "${VSINDEX_DISK}" "${VS_DISK_least_size}" "vg_vsindex"
        [ $? -ne 0 ] && local_check_ret1=1

        check_local_disk "${KAFKA_PATH}" "${SRVBigData_DISK}" "${KAFKA_DISK_least_size}" "vg_kafka"
        [ $? -ne 0 ] && local_check_ret1=1

    elif [ "X${OM_Cluster_Node_IPV6_List}" != "X" -a "X${OM_Cluster_Node_IPV6_List}" != "X{{OM_Cluster_Node_IPV6_List}}" ] && [[ ${node_type} =~ ^OM.* ]];then
        check_local_disk "${VS_PATH}" "${VSINDEX_DISK}" "${VS_DISK_least_size}" "vg_vsindex"
        [ $? -ne 0 ] && local_check_ret1=1

        check_local_disk "${KAFKA_PATH}" "${SRVBigData_DISK}" "${KAFKA_DISK_least_size}" "vg_kafka"
        [ $? -ne 0 ] && local_check_ret1=1
    fi
    ## check PMnode dbdisk
    local local_check_node_disk_ret=0
    if [[ "${node_type}" =~ PM[0-9] ]] || [[ "${node_type}" =~ DVExtendCluster[0-9] && "${zenith_paramgroup_file}" != "largeCapacity" ]];then
        local node_number=$(echo ${node_type} |sed "s/^DVExtendCluster\(.*\)/\1/g")
        ###物理机场景需要跳过该判断       
        check_local_disk "${OPT_PATH}/zenith" "${PM_DB_DISK}" "${DBNode_DISK_least_size}" "vg_pm_db" "not_exit"
        if [ $? -ne 0 ]; then
           log_echo "WARN" "Can not find suitable disk for PM_DB_DISK"        
           local_check_node_disk_ret=1
           if [ ! -z ${Extend_Cluster_Node_exist_number} ] && [[ ${node_number} -le 12 ]]&& [[ $(( ${node_number} - ${Extend_Cluster_Node_exist_number} )) -le 2 ]];then
              log_echo "ERROR" "Can not find suitable disk for PM_DB_DISK on extend first two nodes"
              exit 1
           fi           
        fi  
        if [ $local_check_node_disk_ret -eq 0 ] ;then            
            check_local_disk "${OPT_PATH}/oss/backuptmp" "${PM_DB_BACKUP_TMP_DISK}" "10" "vg_pm_dvbackuptmp" "not_exit"
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Can not find suitable disk for PM_DB_BACKUP_TMP_DISK or PM_DB_BACKUP_TMP_DISK is null , please check "
                exit 1
            fi
        fi
        if [[ ${node_number} -le 4 && "${node_type}" =~ DVExtendCluster[0-9] ]] || [[ "${node_type}" =~ PM[0-9] ]];then
            local_check_ret=$local_check_node_disk_ret                      
        fi       
        if [[ ${node_number} -gt 4 && "${node_type}" =~ DVExtendCluster[0-9] ]] ;then 
            if [ "${AUTO_DISK_MOUNT}" != "TRUE" -a ! -f "/.dockerenv" ];then
                local_check_node_disk_ret=1
            fi
        fi
    fi
    
    if [[ "${node_type}" =~ SMExtendCluster[0-9] && "${zenith_paramgroup_file}" != "largeCapacity" ]];then
        local pm_db_disk_ret=0
        check_local_disk "${OPT_PATH}/zenith" "${PM_DB_DISK}" "${DBNode_DISK_least_size}" "vg_pm_db"
        [ $? -ne 0 ] && pm_db_disk_ret=1
        
        check_local_disk "${OPT_PATH}/oss/backuptmp" "${PM_DB_BACKUP_TMP_DISK}" "10" "vg_pm_dvbackuptmp"
        [ $? -ne 0 ] && pm_db_disk_ret=1
        echo "local_check_node_disk_ret=$pm_db_disk_ret" > ${CURRENT_PATH}/local_check_disk.txt
        [ ${pm_db_disk_ret} -ne 0 ] && local_check_ret=${pm_db_disk_ret}
    fi
    
    if [[ "${node_type}" =~ PM[0-9] ]] || [[ "${node_type}" =~ DVExtendCluster[0-9] ]];then
        echo "local_check_node_disk_ret=$local_check_node_disk_ret" > ${CURRENT_PATH}/local_check_disk.txt
    fi
    
    ## only check whether the disk of the current node exists.
    local pm_db_node_path_array=(${PHYSICAL_PM_DB_NODE_PATH_LIST//,/ }) 
    local path_list_number=${#pm_db_node_path_array[@]}
    local local_check_instance_disk_ret=0
    local path_index=0
    if [[ "${node_type}" =~ PMNODE[0-9] ]];then
        local node_number=$(echo ${node_type} |sed "s/^PMNODE\(.*\)DB.*/\1/g")
        path_index=$[node_number*2]
        if [[ "${node_type}" == *DB1 ]]; then
            if [ -z "${pm_db_node_path_array[$path_index]}" ];then
                log_echo "INFO" "The pm_db_node_path_array get $path_index is null."
                local_check_instance_disk_ret=1
            else
                log_echo "INFO" "execute cmd:[ check_local_disk \"${pm_db_node_path_array[$path_index]}\" \"${PHYSICAL_PM_DB_DISK1}\" \"${DBNode_DISK_least_size}\" \"vg_pm_db1\" \"not_exit\" ]."
                check_local_disk "${pm_db_node_path_array[$path_index]}" "${PHYSICAL_PM_DB_DISK1}" "${DBNode_DISK_least_size}" "vg_pm_db1" "not_exit"
                if [ $? -ne 0 ];then
                    log_echo "WARN" "Can not find suitable disk for PHYSICAL_PM_DB_DISK1"
                    local_check_instance_disk_ret=1
                    if [ "${is_old_extend_mode}" == "Yes" ] && [ ! -z ${Extend_Cluster_Node_exist_number} ] && [[ ${node_number} -le 5 ]] && [[ $(( ${node_number} - ${Extend_Cluster_Node_exist_number} - 1 )) -le 2 ]];then
                        log_echo "ERROR" "Can not find suitable disk for PHYSICAL_PM_DB_DISK1 on extend first two nodes"
                        exit 1
                    fi
                    
                    if [ "${is_old_extend_mode}" != "Yes" ];then
                        log_echo "ERROR" "Can not find suitable disk for PHYSICAL_PM_DB_DISK1 on ${node_type}."
                        exit 1
                    fi
                fi
                log_echo "INFO" "execute cmd:[ check_local_disk \"${pm_db_node_path_array[$path_index+1]}\" \"${PHYSICAL_PM_DB_DISK2}\" \"${DBNode_DISK_least_size}\" \"vg_pm_db2\" \"not_exit\" ]."
                check_local_disk "${pm_db_node_path_array[$path_index+1]}" "${PHYSICAL_PM_DB_DISK2}" "${DBNode_DISK_least_size}" "vg_pm_db2" "not_exit"
                if [ $? -ne 0 ];then
                    log_echo "WARN" "Can not find suitable disk for PHYSICAL_PM_DB_DISK2"
                    local_check_instance_disk_ret=1
                    if [ "${is_old_extend_mode}" == "Yes" ] && [ ! -z ${Extend_Cluster_Node_exist_number} ] && [[ ${node_number} -le 5 ]] && [[ $(( ${node_number} - ${Extend_Cluster_Node_exist_number} - 1 )) -le 2 ]];then
                        log_echo "ERROR" "Can not find suitable disk for PHYSICAL_PM_DB_DISK2 on extend first two nodes"
                        exit 1
                    fi
                    
                    if [ "${is_old_extend_mode}" != "Yes" ];then
                        log_echo "ERROR" "Can not find suitable disk for PHYSICAL_PM_DB_DISK2 on SM extend nodes"
                        exit 1
                    fi
                fi
                if [ ${node_number} -ge 2 -a $local_check_instance_disk_ret -eq 0 ] || [ "${is_old_extend_mode}" != "Yes" ];then
                    check_local_disk "${OPT_PATH}/oss/backuptmp" "${PM_DB_BACKUP_TMP_DISK}" "10" "vg_pm_dvbackuptmp" "not_exit"
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "Can not find ${PM_DB_BACKUP_TMP_DISK}G disk for PM_DB_BACKUP_TMP_DISK or PM_DB_BACKUP_TMP_DISK is null , please check "
                        exit 1
                    fi
                fi
            fi
        fi
        if [[ "${node_type}" == *DB2 ]]; then
            log_echo "INFO" "PHYSICAL_PM_DB_DISK2 is mounted, skip this action "
            return 0
        fi
        
        if [[ ${node_number} -lt 2 ]] || [[ "${scene_type}" =~ SMExtendCluster[0-9] ]];then
            local_check_ret=$local_check_instance_disk_ret
        fi
        
        if [ ${node_number} -ge 2 ] ;then
              if [ "${AUTO_DISK_MOUNT}" != "TRUE" -a ! -f "/.dockerenv" ] ;then
                    local_check_instance_disk_ret=1
              fi
              if [ "${AUTO_DISK_MOUNT}" != "TRUE" -a ! -f "/.dockerenv" ] ;then
                    local_check_instance_disk_ret=1
              fi
        fi

        log_echo "INFO" "The local_check_instance_disk_ret=$local_check_instance_disk_ret"
        echo "local_check_instance_disk_ret=$local_check_instance_disk_ret" > ${CURRENT_PATH}/local_check_disk.txt
    fi

    if [ ${ext_check_ret} -ne 0 -o ${local_check_ret} -ne 0 -o ${local_check_ret1} -ne 0 ];then
        log_echo "ERROR" "Check disk space failed at DV node, please check..."
        exit 1
    fi
        
    systemctl daemon-reload
    if [ "${AUTO_DISK_MOUNT}" == "TRUE" ];then
        if [ -f "${ATAE_extend_script}" -a "X${node_type}" != "Xall_in_one" ];then
            if [ "X${CUSTOM_PATH_SCENE}" == "XTRUE" ]; then
                log_echo "INFO" "The custom path scene do not to do ATAE Extend disk."
            else
                ATAE_Extend_Disk
                if [ "X${EXTEND_DISK}" != "X" -a "X${EXTEND_DISK}" != "X{{EXTEND_DISK}}" -a "X${is_use_varlog}" == "XYes" -a "${scene_type}" == "extend" ];then
                    rm -rf /var/log/oss
                    ln -sf /opt/oss/log /var/log/oss
                    if [ $? -ne 0 ];then
                        log_echo "ERROR" "Create soft link /opt/oss/log to /var/log/oss failed, please check..."
                        exit 1
                    fi
                fi
            fi
        fi

        if [ "X${EXTEND_98G_DISK}" != "X" -a "X${EXTEND_98G_DISK}" != "X{{EXTEND_98G_DISK}}" -a ! -f "${CURRENT_PATH}/mount_extdisk_finish" ];then
            auto_mount_ext
        fi

        check_log_disk_has_extend "${scene_type}"
        if [ $? -ne 0 ]; then
            if [ "X${pre_install_key}" != "X" -o "X${Digitalization_Disk_Param_Tag}" == "XYES" ];then
                preinstall_config_disk_symbol  "vg_log_extend" "${dv_cfg_file}.tmp"
            fi
            if [ "X${LOG_EXTEND_DISK}" != "X" -a "X${LOG_EXTEND_DISK}" != "X{{LOG_EXTEND_DISK}}" ] && [ -f "${ATAE_extend_script}" ]; then
                auto_extend_log_disk "${scene_type}"
            fi
        fi

        if [ "X${APPNode_DISK}" != "X" -a "X${APPNode_DISK}" != "X{{APPNode_DISK}}" ] && [ "X${i2k_is_dual}" != "XYes" -o "X${need_drbd}" == "XNO" ] && [ "X${node_type}" == "X" -o "X${node_type}" == "Xall_in_one" ];then
            if [ "${scene_type}" == "docker" ];then
                auto_mount_local "${OPT_PATH}/share" "${APPNode_DISK}" "${APP_DISK_least_size}" "vg_dv" "lv_dv"
            else
                auto_mount_local "${i2k_install_path}" "${APPNode_DISK}" "${APP_DISK_least_size}" "vg_dv" "lv_dv"
            fi
        fi
        
        if [ "X${HOFS_DISK}" != "X" -a "X${HOFS_DISK}" != "X{{HOFS_DISK}}" ] && [ "X${node_type}" == "X" -o "X${node_type}" == "Xall_in_one" ];then
            auto_mount_local "${OPT_PATH}/oss/hofs" "${HOFS_DISK}" "${HOFS_DISK_least_size}" "vg_hofs" "lv_hofs"
        fi
        
        if [ "X${TRACE_DISK}" != "X" -a "X${TRACE_DISK}" != "X{{TRACE_DISK}}" ] && [ "X${node_type}" == "X" -o "X${node_type}" == "Xall_in_one" ];then
            if [ "${scene_type}" == "docker" ];then
                auto_mount_local "${OPT_PATH}/share/trace" "${TRACE_DISK}" "${TRACE_DISK_least_size}" "vg_trace" "lv_trace"
            else
                auto_mount_local "${install_path}/share/SOP/DVEngineeringService/trace" "${TRACE_DISK}" "${TRACE_DISK_least_size}" "vg_trace" "lv_trace"
            fi
        fi
        if [ "X${VSINDEX_DISK}" != "X" -a "X${VSINDEX_DISK}" != "X{{VSINDEX_DISK}}" -a "X${node_type}" == "XSA" ];then
            auto_mount_local "${HOME_PATH}/vsindex" "${VSINDEX_DISK}" "${VS_DISK_least_size}" "vg_vsindex" "lv_vsindex"
        fi
        
        if [ "X${SRVBigData_DISK}" != "X" -a "X${SRVBigData_DISK}" != "X{{SRVBigData_DISK}}"  -a "X${node_type}" == "XOM" ];then
            auto_mount_local "${KAFKA_PATH}" "${SRVBigData_DISK}" "${KAFKA_DISK_least_size}" "vg_kafka" "lv_kafka"
        fi
        
        Flume_nodes_number=$(echo ${Flume_Cluster_Node_IPV4_List} | awk -F',' '{print NF}')
        VS_nodes_number=$(echo ${VS_Cluster_Node_IPV4_List} | awk -F',' '{print NF}')
        
        if [[ ${node_type} =~ ^FLUME.* ]];then
            for u in $(seq 1 $(echo "${Flume_Cluster_Node_IPV4_List}" | sed "s/,/\n/g" |wc -l) | xargs);
            do
                if [ "X${SRVBigData_DISK}" != "X{{SRVBigData_DISK}}" ];then
                    auto_mount_local "${KAFKA_PATH}" "${SRVBigData_DISK}" "${KAFKA_DISK_least_size}" "vg_kafka" "lv_kafka"
                fi
            done
        fi
    
        if [[ ${node_type} =~ ^VS.* ]];then
            for v in $(seq 1 $(echo "${VS_Cluster_Node_IPV4_List}" | sed "s/,/\n/g" |wc -l) | xargs);
            do
                if [ "X${VSINDEX_DISK}" != "X{{VSINDEX_DISK}}" ];then
                    auto_mount_local "${VS_PATH}" "${VSINDEX_DISK}" "${VS_DISK_least_size}" "vg_vsindex" "lv_vsindex"
                fi
            done
        fi
        
        if [ "X${SA_Cluster_Node_IPV4_List}" != "X" -a "X${SA_Cluster_Node_IPV4_List}" != "X{{SA_Cluster_Node_IPV4_List}}" ] || [ "X${SA_Cluster_Node_IPV6_List}" != "X" -a "X${SA_Cluster_Node_IPV6_List}" != "X{{SA_Cluster_Node_IPV6_List}}" ];then
            [ "X${SA_Cluster_Node_IPV4_List}" != "X{{SA_Cluster_Node_IPV4_List}}"  -a  "X${SA_Cluster_Node_IPV4_List}" != "X" ] && sa_ip_list="${SA_Cluster_Node_IPV4_List}"
            [ "X${SA_Cluster_Node_IPV6_List}" != "X{{SA_Cluster_Node_IPV6_List}}"  -a  "X${SA_Cluster_Node_IPV6_List}" != "X" ] && sa_ip_list="${SA_Cluster_Node_IPV6_List}"
            for g in $(seq 1 $(echo "${sa_ip_list}" | sed "s/,/\n/g" |wc -l) | xargs);
            do
                if [ "X${node_type}" == "XSA${g}" -a "X${VSINDEX_DISK}" != "X{{VSINDEX_DISK}}" ];then
                    auto_mount_local "${VS_PATH}" "${VSINDEX_DISK}" "${VS_DISK_least_size}" "vg_vsindex" "lv_vsindex"
                fi
            done
        fi
        
        if [ "X${OM_Cluster_Node_IPV4_List}" != "X" -a "X${OM_Cluster_Node_IPV4_List}" != "X{{OM_Cluster_Node_IPV4_List}}" ] || [ "X${OM_Cluster_Node_IPV6_List}" != "X" -a "X${OM_Cluster_Node_IPV6_List}" != "X{{OM_Cluster_Node_IPV6_List}}" ];then
            [ "X${OM_Cluster_Node_IPV4_List}" != "X{{OM_Cluster_Node_IPV4_List}}"  -a  "X${OM_Cluster_Node_IPV4_List}" != "X" ] && om_ip_list="${OM_Cluster_Node_IPV4_List}"
            [ "X${OM_Cluster_Node_IPV6_List}" != "X{{OM_Cluster_Node_IPV6_List}}"  -a  "X${OM_Cluster_Node_IPV6_List}" != "X" ] && om_ip_list="${OM_Cluster_Node_IPV6_List}"
            for h in $(seq 1 $(echo "${om_ip_list}" | sed "s/,/\n/g" |wc -l) | xargs);
            do
                if [ "X${node_type}" == "XOM${h}" -a "X${VSINDEX_DISK}" != "X{{VSINDEX_DISK}}" -a "X${SRVBigData_DISK}" != "X{{SRVBigData_DISK}}" ];then
                    auto_mount_local "${VS_PATH}" "${VSINDEX_DISK}" "${VS_DISK_least_size}" "vg_vsindex" "lv_vsindex"
                    auto_mount_local "${KAFKA_PATH}" "${SRVBigData_DISK}" "${KAFKA_DISK_least_size}" "vg_kafka" "lv_kafka"
                fi
            done
        fi
        
        if [ "X${node_type}" == "XSD" -o "X${node_type}" == "XSD1" -o "X${node_type}" == "XSD2" ];then
            auto_mount_local "${OPT_PATH}/zenith" "${SDNode_DB_DISK}" "${DBNode_DISK_least_size}" "vg_sd_db" "lv_sd_db"
            if [ "X${SDNode_BACKUP_TMP_DISK}" != "X" -a "X${SDNode_BACKUP_TMP_DISK}" != "X{{SDNode_BACKUP_TMP_DISK}}"  ];then
                lvdisplay /dev/vg_sd_dvbackuptmp/lv_sd_dvbackuptmp >/dev/null 2>&1
                if [ $? -ne 0 ];then
                    auto_mount_local "${OPT_PATH}/oss/backuptmp" "${SDNode_BACKUP_TMP_DISK}" "10" "vg_sd_dvbackuptmp" "lv_sd_dvbackuptmp"
                fi
            fi
        fi
        ## PM db_disk  correspond  "vg_pm_db" "lv_pm_db"  PM_DB_BACKUP_TMP_DISK  correspond "vg_pm_dvbackuptmp" "lv_pm_dvbackuptmp"
        if [ ${local_check_node_disk_ret} -eq 0 ];then
            local pm_db_auto_mount=0
            if [[ "${node_type}" =~ PM[0-9] ]];then
                pm_db_auto_mount=1
            elif [[ "${node_type}" =~ DVExtendCluster[0-9] && "${zenith_paramgroup_file}" != "largeCapacity" ]];then
                pm_db_auto_mount=1
            elif [[ "${node_type}" =~ SMExtendCluster[0-9] && "${zenith_paramgroup_file}" != "largeCapacity" ]];then
                pm_db_auto_mount=1
            else
                log_echo "INFO" "check pm_db_auto_mount is else."
            fi
            log_echo "INFO" "The pm_db_auto_mount=${pm_db_auto_mount} node_type=${node_type} zenith_paramgroup_file=${zenith_paramgroup_file}"
            if [ ${pm_db_auto_mount} -eq 1 ];then
                log_echo "INFO" "auto mount pm db."
                if [ "X${PM_DB_DISK}" != "X" -a "X${PM_DB_DISK}" != "X{{PM_DB_DISK}}" -a ${local_check_ret} -eq 0 ];then
                    auto_mount_local "${OPT_PATH}/zenith" "${PM_DB_DISK}" "${DBNode_DISK_least_size}" "vg_pm_db" "lv_pm_db"
                fi
                
                if [ "X${PM_DB_BACKUP_TMP_DISK}" != "X" -a "X${PM_DB_BACKUP_TMP_DISK}" != "X{{PM_DB_BACKUP_TMP_DISK}}"  ];then
                    lvdisplay /dev/vg_pm_dvbackuptmp/lv_pm_dvbackuptmp >/dev/null 2>&1
                    if [ $? -ne 0 ];then
                        auto_mount_local "${OPT_PATH}/oss/backuptmp" "${PM_DB_BACKUP_TMP_DISK}" "10" "vg_pm_dvbackuptmp" "lv_pm_dvbackuptmp"
                    fi
                fi
            fi
        fi
        ##If the disk exists, mount the disk. If the disk does not exist, do not mount the disk.
        if [ ${local_check_instance_disk_ret} -eq 0 ];then
            if [[ "${node_type}" == "PMNODE${node_number}DB1" ]];then                      
                if [ "X${PHYSICAL_PM_DB_DISK1}" != "X" -a "X${PHYSICAL_PM_DB_DISK1}" != "X{{PHYSICAL_PM_DB_DISK1}}"  ];then
                    auto_mount_local "${pm_db_node_path_array[$path_index]}" "${PHYSICAL_PM_DB_DISK1}" "${DBNode_DISK_least_size}" "vg_pm_db1" "lv_pm_db"
                    chown_dbuser                    
                fi
                if [ "X${PHYSICAL_PM_DB_DISK2}" != "X" -a "X${PHYSICAL_PM_DB_DISK2}" != "X{{PHYSICAL_PM_DB_DISK2}}"  ];then
                    auto_mount_local "${pm_db_node_path_array[$path_index+1]}" "${PHYSICAL_PM_DB_DISK2}" "${DBNode_DISK_least_size}" "vg_pm_db2" "lv_pm_db"
                    chown_dbuser
                fi
                if [  ${node_number} -ge 2 ];then
                    lvdisplay /dev/vg_pm_dvbackuptmp/lv_pm_dvbackuptmp >/dev/null 2>&1
                    if [ $? -ne 0 ];then
                        auto_mount_local "${OPT_PATH}/oss/backuptmp" "${PM_DB_BACKUP_TMP_DISK}" "10" "vg_pm_dvbackuptmp" "lv_pm_dvbackuptmp"
                    fi
                fi
            fi     
        fi
    fi
    
    ## add the PM node type

    if [ "X${node_type}" == "XSD" -o "X${node_type}" == "XSD1" -o "X${node_type}" == "XSD2" ] || [[ "${node_type}" =~ PM[0-9]  || "${node_type}" =~ DVExtendCluster[0-9] || "${node_type}" =~ SMExtendCluster[0-9] ]];then
        if [ -d "${OPT_PATH}/zenith" ];then
            grep "^dbgroup:" /etc/group >/dev/null 2>&1
            if [ $? -ne 0 ];then
                groupadd -g 1999 dbgroup
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Add group dbgroup failed, please check..."
                    exit 1
                fi
            fi
            
            rm -rf ${OPT_PATH}/zenith/*
            chmod 750 -R ${OPT_PATH}/zenith
            chown root:dbgroup -R ${OPT_PATH}/zenith
        fi
    fi

    if [ -d ${OPT_PATH}/zenith/data ];then
        log_echo "INFO" "use setfacl chmod data permissions"
        setfacl -m u:ossadm:rx ${OPT_PATH}/zenith/data
    else
        log_echo "INFO" "not has ${OPT_PATH}/zenith/data, need to setfacl"
    fi

    if [ "${scene_type}" == "docker" ];then
        mkdir -p ${OPT_PATH}/dv ${OPT_PATH}/oss/log ${HOME_PATH}/dv ${TMP_PATH}/dv ${OPT_PATH}/pub ${OPT_PATH}/share ${OPT_PATH}/oss/backuptmp ${OPT_PATH}/zenith ${HOME_PATH}/vsindex /srv/BigData ${OPT_PATH}/dv_sudoers.d ${OPT_PATH}/dv_osconfig ${OPT_PATH}/dv_uniepsudobin ${OPT_PATH}/dv_os_tool ${OPT_PATH}/dv_signtool ${OPT_PATH}/dv_os_config
        chmod 755 ${OPT_PATH}/dv
    fi
    return 0
}

function stop_nfs()
{
    service rpcbind.socket stop >/dev/null 2>&1
    service rpcbind.service stop >/dev/null 2>&1
    service nfs.service stop >/dev/null 2>&1
    
    which systemctl >/dev/null 2>&1
    if [ $? -eq 0 ];then
        systemctl disable rpcbind.socket >/dev/null 2>&1
        systemctl disable rpcbind.service >/dev/null 2>&1
        systemctl disable nfs.service >/dev/null 2>&1
    fi
    
    return 0
}

function preset_restore()
{
    log_echo "INFO" "preset_restore." 
    ## /opt/oss/backuptmp/DVEngineeringService/restore
    ## drwxr-x---  ossadm ossgroup  backuptmp  
    ## drwxr-x---  ossuser ossgroup DVEngineeringService
    ## drwxrwx---  ossuser ossgroup restore
    ## ${install_path} =/opt/oss 
    local dv_restore_dir=${install_path}/backuptmp/DVEngineeringService
    if [ ! -d ${dv_restore_dir} ];then
        if [ ! -f "/home/<USER>/SD_upgrade.tag" ];then
            log_echo "ERROR" "The dv_restore_dir=${dv_restore_dir} is not exist,need to backup the product first." 
            exit 1
        else
            if [ "X$(whoami)" == "Xroot" ];then
                mkdir -p ${dv_restore_dir}
                chown ossadm:ossgroup ${install_path}/backuptmp
                chown ${i2k_user_name}:${i2k_group_name} ${dv_restore_dir}
                mkdir -p ${install_path}/backuptmp/dynamic
                mkdir -p ${install_path}/backuptmp/node
                chmod 770 ${install_path}/backuptmp
                chmod 770 ${dv_restore_dir}
            else
                sudo -u ossuser mkdir -p ${dv_restore_dir}
                mkdir -p ${install_path}/backuptmp/dynamic
                mkdir -p ${install_path}/backuptmp/node
                chmod 770 ${install_path}/backuptmp
                sudo -u ossuser chmod 770 ${dv_restore_dir}
            fi
        fi
    fi
    if [ "X$(whoami)" == "Xroot" ];then
        mkdir -p ${dv_restore_dir}/restore
        chown -R ${i2k_user_name}:${i2k_group_name} ${dv_restore_dir}/restore
        chmod -R 770 ${dv_restore_dir}/restore

        ## drwxr-x---  ossadm  ossgroup   dynamic
        chown -R ossadm:${i2k_group_name} ${install_path}/backuptmp/dynamic
        chmod -R 750 ${install_path}/backuptmp/dynamic
        ## drwxr-x---  ossadm  ossgroup   node
        chown -R ossadm:${i2k_group_name} ${install_path}/backuptmp/node
        chmod -R 750 ${install_path}/backuptmp/node
    else
        sudo -u ossuser mkdir -p ${dv_restore_dir}/restore
        sudo -u ossuser chmod -R 770 ${dv_restore_dir}/restore
        chmod -R 750 ${install_path}/backuptmp/dynamic
        chmod -R 750 ${install_path}/backuptmp/node
    fi

    log_echo "INFO" "preset_restore finished." 
    return 0
}

function handle_icnfg_issue()
{
    I2000_baseversion=$(${command_prefix} cat ${root_path}/common/config/CommonInfo.ini | grep "FullVersion="|awk -F'=' '{print $2}')
    local I2000_backup_path="${i2k_install_path}/backup_for_upgrade/I2000_${I2000_baseversion}"
    if [ -z "`${command_prefix} ls ${I2000_backup_path}/I2000_iCnfg_*.tar.gz`" ];then
        log_echo "INFO" "It has installed icnfg, but we cannot find icnfg's backup in ${I2000_backup_path}, maybe base version is special version."
        return 0
    fi

    local icnfg_backup_file=$(${command_prefix} ls ${I2000_backup_path}/I2000_iCnfg_*.tar.gz)
    sudo -u ossuser rm -rf ${I2000_backup_path}/icnfg
    ${command_prefix} tar -zxf ${icnfg_backup_file} ${I2000_backup_path}/icnfg/webpages/var/data/
    sudo -u ossuser rm -rf ${I2000_backup_path}/icnfg
}

function backup_sudoerScripts()
{
    log_echo "INFO" "backup sudoScripts"
    mkdir -p /root/backup
    mkdir -p /root/restore
    chattr -i -R /root/backup
    chattr -i -R /root/restore
    ${i2k_user_home}/sudoScripts/backup_restore.sh ${i2k_user_home} /root backup >> "${LOG_FILE}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute [ ${i2k_user_home}/sudoScripts/backup_restore.sh ${i2k_user_home} /root backup ] failed, manual execute to see more detail"
        exit 1
    fi
    
    cp -rp /root/backup/* /root/restore/
    if [ -f ${i2k_user_home}/recover_i2k_privilege.sh ];then
        log_echo "INFO" "backup ${i2k_user_home}/recover_i2k_privilege.sh to /root/backup and /root/restore"
        cp -rpf ${i2k_user_home}/recover_i2k_privilege.sh /root/backup/
        cp -rpf ${i2k_user_home}/recover_i2k_privilege.sh /root/restore/
    fi
    
    chmod -R 700 /root/backup
    chattr -i -R /root/backup
    chmod -R 700 /root/restore
    chattr -i -R /root/restore
    
    if [ -d ${i2k_user_home}/backup ];then
        log_echo "INFO" "rm ${i2k_user_home}/backup dir"
        chattr -i -R ${i2k_user_home}/backup
        rm -rf ${i2k_user_home}/backup
    fi
    
    if [ -d ${i2k_user_home}/restore ];then
        log_echo "INFO" "rm ${i2k_user_home}/restore dir"
        chattr -i -R ${i2k_user_home}/restore
        rm -rf ${i2k_user_home}/restore
    fi
    
    if [ -f ${i2k_user_home}/recover_i2k_privilege.sh ];then
        log_echo "INFO" "rm ${i2k_user_home}/recover_i2k_privilege.sh file."
        chattr -i ${i2k_user_home}/recover_i2k_privilege.sh
        rm -rf ${i2k_user_home}/recover_i2k_privilege.sh
    fi
}

function modifyStartScript()
{
    log_echo "INFO" "start exc modifyStartScript"
    local start_script=${install_path}/SOP/apps/DVEngineeringService/bin/start.sh
    eval "${TEST_FILE} ${start_script}"
    if [ $? -eq 0 ];then
        local exist_flg=$(${command_prefix} cat ${start_script} |grep -w "I2000/not_need_start.tag")
        if [ ! -z "${exist_flg}" ];then
            log_echo "INFO" "exist_flg=${exist_flg} is not null. The startup script has been modified. Skip it!"
            return 0
        fi
        
        local key_row=$(${command_prefix} grep -nw "^[ ]*prehandleBeforeStart"  ${start_script} |awk -F':' '{print $1}')
        if [ -z "${key_row}" ];then
            log_echo "ERROR" "The key_row=${key_row} is null.The line number where the keyword is not found."
            return 1
        fi
        if [ "X$(whoami)" == "Xroot" ];then
            local insert_row=${key_row}
            sed -i "${insert_row} a if [ -f \$_APP_SHARE_DIR/I2000/not_need_start.tag ];then"  ${start_script}

            insert_row=$((${insert_row} + 1))
            sed -i "${insert_row} a log_echo \"INFO\" \"tag is exists, no need start.\""  ${start_script}

            insert_row=$((${insert_row} + 1))
            sed -i "${insert_row} a exit 0"  ${start_script}

            insert_row=$((${insert_row} + 1))
            sed -i "${insert_row} a fi"  ${start_script}
        else
            local insert_row=${key_row}
            sudo -u ossuser sed -i "${insert_row} a if [ -f \$_APP_SHARE_DIR/I2000/not_need_start.tag ];then"  ${start_script}

            insert_row=$((${insert_row} + 1))
            sudo -u ossuser sed -i "${insert_row} a log_echo \"INFO\" \"tag is exists, no need start.\""  ${start_script}

            insert_row=$((${insert_row} + 1))
            sudo -u ossuser sed -i "${insert_row} a exit 0"  ${start_script}

            insert_row=$((${insert_row} + 1))
            sudo -u ossuser sed -i "${insert_row} a fi"  ${start_script}
        fi

    fi
    
    local dv_common_script=${install_path}/SOP/apps/DVEngineeringService/bin/dv_common.sh
    eval "${TEST_FILE} ${dv_common_script}"
    if [ $? -eq 0 ];then
        exist_flg=$(${command_prefix} cat ${dv_common_script} |grep -w "I2000/not_need_start.tag")
        if [ ! -z "${exist_flg}" ];then
            log_echo "INFO" "exist_flg=${exist_flg} is not null. The startup script has been modified. Skip it!"
            return 0
        fi
        
        start_row=$(${command_prefix} grep -nw "^[ ]*function[ ]\+prehandleBeforeStart" ${dv_common_script} |awk -F':' '{print $1}')
        if [ -z "${start_row}" ];then
            log_echo "ERROR" "The start_row=${start_row} is null.The line number where the keyword(function prehandleBeforeStart) is not found."
            return 1
        fi
        end_row=$(${command_prefix} cat ${dv_common_script} |wc -l)
        
        key_row=0
        for line in $(seq ${start_row} ${end_row});do
            lineStr=$(${command_prefix} sed -n "${line},${line}p" ${dv_common_script} | grep -w "^[ ]*start_drbd" )
            if [ ! -z "${lineStr}" ];then
                key_row=${line};
                break
            fi
        done
        
        if [ "X${key_row}" == "X0" ];then
            log_echo "ERROR" "The key_row=${key_row} .The line number where the keyword(start_drbd) is not found."
            return 1
        fi

        if [ "X$(whoami)" == "Xroot" ];then
            insert_row=${key_row}
            sed -i "${insert_row} a if [ -f \$_APP_SHARE_DIR/I2000/not_need_start.tag ];then"  ${dv_common_script}

            insert_row=$((${insert_row} + 1))
            sed -i "${insert_row} a log_echo \"INFO\" \"tag is exists, no need start.\""  ${dv_common_script}

            insert_row=$((${insert_row} + 1))
            sed -i "${insert_row} a exit 0"  ${dv_common_script}

            insert_row=$((${insert_row} + 1))
            sed -i "${insert_row} a fi"  ${dv_common_script}
        else
            insert_row=${key_row}
            sudo -u ossuser sed -i "${insert_row} a if [ -f \$_APP_SHARE_DIR/I2000/not_need_start.tag ];then"  ${dv_common_script}

            insert_row=$((${insert_row} + 1))
            sudo -u ossuser sed -i "${insert_row} a log_echo \"INFO\" \"tag is exists, no need start.\""  ${dv_common_script}

            insert_row=$((${insert_row} + 1))
            sudo -u ossuser sed -i "${insert_row} a exit 0"  ${dv_common_script}

            insert_row=$((${insert_row} + 1))
            sudo -u ossuser sed -i "${insert_row} a fi"  ${dv_common_script}
        fi

    fi
    
    log_echo "INFO" "exc modifyStartScript finished."
    return 0
}

function modifyMonitorScript()
{
    log_echo "INFO" "start exc modifyMonitorScript."
    local monitor_script=${install_path}/SOP/apps/DVEngineeringService/bin/monitor.sh
    eval "${TEST_FILE} ${monitor_script}"
    if [ $? -eq 0 ];then
        local exist_flg=$(${command_prefix} cat ${monitor_script} |grep -w "I2000/not_need_start.tag")
        if [ ! -z "${exist_flg}" ];then
            log_echo "INFO" "exist_flg=${exist_flg} is not null. The monitor script has been modified. Skip it!"
            return 0
        fi
        
        local key_row=$(${command_prefix} grep -nw "^[ ]*.[ ]\+\${CURRENT_PATH}/dv_common.sh"  ${monitor_script} |awk -F':' '{print $1}')
        if [ -z "${key_row}" ];then
            log_echo "ERROR" "The key_row=${key_row} is null.The line number where the keyword is not found."
            return 1
        fi
        
        local insert_row=${key_row}
        if [ "X$(whoami)" == "Xroot" ];then
            sed -i "${insert_row} a if [ -f \$_APP_SHARE_DIR/I2000/not_need_start.tag ];then"  ${monitor_script}

            insert_row=$((${insert_row} + 1))
            sed -i "${insert_row} a exit 0"  ${monitor_script}

            insert_row=$((${insert_row} + 1))
            sed -i "${insert_row} a fi"  ${monitor_script}
        else
            sudo -u ossuser sed -i "${insert_row} a if [ -f \$_APP_SHARE_DIR/I2000/not_need_start.tag ];then"  ${monitor_script}

            insert_row=$((${insert_row} + 1))
            sudo -u ossuser sed -i "${insert_row} a exit 0"  ${monitor_script}

            insert_row=$((${insert_row} + 1))
            sudo -u ossuser sed -i "${insert_row} a fi"  ${monitor_script}
        fi

    fi
    log_echo "INFO" "exc modifyMonitorScript finished."
    return 0
}

function preBeforeUpgradePreSet()
{
    log_echo "INFO" "start exc preBeforeUpgradePreSet..."
    modifyStartScript || exit 1
    modifyMonitorScript || exit 1
    
    local tag_file=${install_path}/share/SOP/DVEngineeringService/I2000/not_need_start.tag
    local upgrade_tag_file=${install_path}/share/SOP/DVEngineeringService/I2000/dv_upgrading.tag

    eval "${TEST_FILE} ${tag_file}"
    local tag_file_exist=$?
    eval "${TEST_DIR} ${install_path}/share/SOP/DVEngineeringService/I2000"
    if [ $? -eq 0 -a ${tag_file_exist} -ne 0 ];then
        if [ "X$(whoami)" == "Xroot" ];then
            su - ${i2k_user_name} -c "touch ${tag_file}" 
        else
            sudo -u ossuser touch ${tag_file} 
        fi
        if [ $? -ne 0 ];then
            log_echo "ERROR" "touch ${tag_file} failed."
            exit 1
        fi
        log_echo "INFO" "touch ${tag_file} success."
        if [ "X$(whoami)" == "Xroot" ];then
              su - ${i2k_user_name} -c "touch ${upgrade_tag_file}" 
        else
              sudo -u ossuser touch ${upgrade_tag_file} 
        fi
        if [ $? -ne 0 ];then
            log_echo "ERROR" "touch ${upgrade_tag_file} failed."
            exit 1
        fi
        log_echo "INFO" "touch ${upgrade_tag_file} success."
    fi

    log_echo "INFO" "exc preBeforeUpgradePreSet finished."
}

function clean_tmp_backup()
{
    log_echo "INFO" "clean backup tmp files." 
    if [ "X$(whoami)" == "Xroot" ];then
        [ -d /opt/oss/backuptmp/DVEngineeringService/backup ] && rm -rf /opt/oss/backuptmp/DVEngineeringService/backup/*
        log_echo "INFO" "dir1=$(ls /opt/oss/backuptmp/DVEngineeringService/backup) dir2=$(ls /opt/oss/share/SOP/DVEngineeringService/backup_for_upgrade)." 
    else
        sudo -u ossuser test -d /opt/oss/backuptmp/DVEngineeringService/backup
        if [ $? -eq 0 ];then
            sudo -u ossuser chmod -R 770 /opt/oss/backuptmp/DVEngineeringService/backup/* >/dev/null 2>&1 
            sudo -u ossuser rm -rf /opt/oss/backuptmp/DVEngineeringService/backup/* >/dev/null 2>&1
            log_echo "INFO" "The /opt/oss/backuptmp/DVEngineeringService/backup is exists.cleanup it.ret=$?"
        fi
        
        log_echo "INFO" "sudo -u ossuser dir1=$(sudo -u ossuser ls /opt/oss/backuptmp/DVEngineeringService) dir2=$(sudo -u ossuser ls /opt/oss/share/SOP/DVEngineeringService/backup_for_upgrade)."
    fi
    log_echo "INFO" "clean backup tmp files End." 
}

function upgrade_check()
{
    log_echo "INFO" "Begin to upgrade_check." 
    
    check_hostname
    
    check_dir_size
    
    clean_tmp_backup

    TASK_TIME=$(basename $(realpath /opt/oss/SOP/apps/DVEngineeringService))
    I2K_UNZIP_PATH="/opt/oss/share/SOP/DVEngineeringService/backup/${TASK_TIME}"
    sudo -u ossuser test -d "$I2K_UNZIP_PATH"  && sudo -u ossuser rm -rf $I2K_UNZIP_PATH 
    
    if [ "X$(whoami)" == "Xroot" ];then
        su - ${i2k_user_name} -c "which i2kadm" > /dev/null 2>&1
    else
        sudo -u ossuser test -f /opt/oss/share/SOP/DVEngineeringService/I2000/bin/i2kadm > /dev/null 2>&1
    fi
    if [ $? -ne 0 ];then
        log_echo "INFO" "The command i2kadm was not found. This is the standby node.not need to check."
        return 0
    fi

    if sudo -u ossuser test -f "/opt/oss/share/SOP/DVEngineeringService/I2000/common/config/CommonInfo.ini";then
        if [ "${is_install_icnfg}" == "No" ];then
            sudo -u ossuser sed -i 's/,I2000_ICNFG_[^,]*//g' "/opt/oss/share/SOP/DVEngineeringService/I2000/common/config/CommonInfo.ini"
        fi
    fi
    
    log_echo "INFO" "Begin to check i2k status.pwd=$(pwd)"
    cd /home
    ## check i2k status
    if [ "X$(whoami)" == "Xroot" ];then
        su - ${i2k_user_name} -c "i2kadm status all" |grep -E "(.*)[.]{12}(.*)" |grep -v I2000APPAgent |  grep -v "RUNNING" >> ${LOG_FILE} 2>&1
    else
        sudo -u ossuser /opt/oss/share/SOP/DVEngineeringService/I2000/bin/i2kadm status all |grep -E "(.*)[.]{12}(.*)" |grep -v I2000APPAgent |grep -v "RUNNING" >> ${LOG_FILE} 2>&1
    fi
    if [ $? -eq 0 ];then
        log_echo "ERROR" "Check that there is a program that starts abnormally, please check:${LOG_FILE}."
        return 1
    fi
    cd -
    ## status success.
    log_echo "INFO" "Check i2k status is all running."

    upgrade_common_check
    
    log_echo "INFO" "upgrade_check finished."

    del_outclient_pfx_without_pwd
    return 0
}

function App_checkNetworkConfig()
{
    if [ "X${netWork_type}" == "XC" ];then
        source_file_config="DV_Config_Cluster.config"
    elif [ "X${netWork_type}" == "XL" ];then
        source_file_config="DV_Config_LargeCapacity.config"
    fi
    
    ## Single Primary Secondary
    if [ "X${app_node_role}" == "XSingle" ];then
        source_file_config="DV_Config_Test.config"
    fi
    
    if [ "X${netWork_type}" == "XM" ];then
        source_file_config="DV_Config_MergeCluster.config"
    fi
    
    . ${CURRENT_PATH}/${source_file_config}
    
    if [ "X${app_node_role}" == "XSingle" ];then
        ## ip=xxx,netmask=xx|xx.xx.xx.xx,nic=ethX
        checkNetworkConfig "ip=${APP_IPV4},netmask=${APP_IPV4_NETMASK},nic=${APP_IPV4_NIC}" || exit 1
        checkNetworkConfig "ip=${APP_IPV6},netmask=${APP_IPV6_NETMASK},nic=${APP_IPV6_NIC}" || exit 1
        checkNetworkConfig "ip=${APP_MGR_IPV6},netmask=,nic=" || exit 1
        
        if [ "X${netWork_type}" == "XT" -a "X${IPType}" == "X0" -a "X${isWebNetworkExtend}" == "XYes" ];then
            checkNetworkConfig "ip=${APP_WEB_EXTERNAL_IPV4},netmask=${APP_WEB_EXTERNAL_IPV4_NETMASK},nic=${APP_WEB_EXTERNAL_IPV4_NIC}" || exit 1
        fi
        
        noping_list=""
        null_list=""
        if [ "X${netWork_type}" == "XT" -a "X${IPType}" == "X0" -a "X${isAppNfvIpExtend}" == "XYes" ];then
            check_ip_ping "${APP_EXTERNAL_NFV_IPV4}" "APP_EXTERNAL_NFV_IPV4"
        elif [ "X${netWork_type}" == "XT" -a "X${IPType}" == "X1" -a "X${isAppNfvIpExtend}" == "XYes" ];then
            check_ip_ping "${APP_EXTERNAL_NFV_IPV6}" "APP_EXTERNAL_NFV_IPV6"
        elif [ "X${netWork_type}" == "XT" -a "X${IPType}" == "X2" -a "X${isAppNfvIpExtend}" == "XYes" ];then
            check_ip_ping "${APP_EXTERNAL_NFV_IPV4}" "APP_EXTERNAL_NFV_IPV4"
            check_ip_ping "${APP_EXTERNAL_NFV_IPV6}" "APP_EXTERNAL_NFV_IPV6"
        fi
        
        if [ ! -z "${null_list}" -o ! -z "${noping_list}" ];then
            [ ! -z "${null_list}" ] && log_echo "ERROR" "Following parameter is null ,please check : ${null_list}"
            [ ! -z "${noping_list}" ] && log_echo "ERROR" "Following IP can't be reached ,please check : ${noping_list}"
            exit 1
        fi
    elif [ "X${app_node_role}" == "XPrimary" ];then
        ## ip=xxx,netmask=xx|xx.xx.xx.xx,nic=ethX
        checkNetworkConfig "ip=${APP_Primary_IPV4},netmask=${APP_FloatIP_IPV4_NETMASK},nic=${APP_Primary_IPV4_NIC}" || exit 1
        checkNetworkConfig "ip=${APP_Primary_IPV6},netmask=${APP_FloatIP_IPV6_NETMASK},nic=${APP_Primary_IPV6_NIC}" || exit 1
        checkNetworkConfig "ip=${APP_Primary_MGR_IPV6},netmask=,nic=${APP_Primary_MGR_IPV6_NIC}" || exit 1
    elif [ "X${app_node_role}" == "XSecondary" ];then
        if [ "X${netWork_type}" == "XM" ];then
            ## ip=xxx,netmask=xx|xx.xx.xx.xx,nic=ethX
            checkNetworkConfig "ip=${DVSecondary_IPV4},netmask=,nic=${DVSecondary_IPV4_NIC}" || exit 1
            checkNetworkConfig "ip=${DVSecondary_IPV6},netmask=,nic=${DVSecondary_IPV6_NIC}" || exit 1
            checkNetworkConfig "ip=${DVSecondary_MGR_IPV6},netmask=,nic=${DVSecondary_MGR_IPV6_NIC}" || exit 1
        else
            ## ip=xxx,netmask=xx|xx.xx.xx.xx,nic=ethX
            checkNetworkConfig "ip=${APP_Secondary_IPV4},netmask=,nic=${APP_Secondary_IPV4_NIC}" || exit 1
            checkNetworkConfig "ip=${APP_Secondary_IPV6},netmask=,nic=${APP_Secondary_IPV6_NIC}" || exit 1
            checkNetworkConfig "ip=${APP_Secondary_MGR_IPV6},netmask=,nic=${APP_Secondary_MGR_IPV6_NIC}" || exit 1
        fi
    elif [ "X${app_node_role}" == "XOMSA" ];then
        if [ "X${netWork_type}" == "XM" ];then
            ## ip=xxx,netmask=xx|xx.xx.xx.xx,nic=ethX
            checkNetworkConfig "ip=${DVThird_IPV4},netmask=,nic=" || exit 1
            checkNetworkConfig "ip=${DVThird_IPV6},netmask=,nic=" || exit 1
            checkNetworkConfig "ip=${DVThird_MGR_IPV6},netmask=,nic=" || exit 1
        fi
    fi
}

function change_certificat_keylen_for_rollback()
{
    [ ! -d "${install_path}/share/SOP/DVEngineeringService/I2000" ] && return 0
    
    run_certconf_path="${install_path}/share/SOP/DVEngineeringService/I2000/run/etc/cert.conf/cert.policy"
    mttools_certconf_path="${install_path}/share/SOP/DVEngineeringService/I2000/mttools/etc/cert.conf/cert.policy"
    file_list="default-peer-cert-policy.properties default-local-cert-policy.properties default-trust-cert-policy.properties"
    
    for file in ${file_list}
    do
        eval "${TEST_FILE} ${run_certconf_path}/${file}"
        if [ $? -eq 0 ];then
            ${command_prefix} sed -i "s/^minEcPubKeyLen=.*/minEcPubKeyLen=256/g" ${run_certconf_path}/${file}
        fi
        eval "${TEST_FILE} ${mttools_certconf_path}/${file}"
        if [ $? -eq 0 ];then
            ${command_prefix} sed -i "s/^minEcPubKeyLen=.*/minEcPubKeyLen=256/g" ${mttools_certconf_path}/${file}
        fi
    done
}

function extend_node_preset()
{
    log_echo "INFO" "Begin to extend_node_preset."
    
    local obj_path="$1"
    local obj_name="$2"
    if [ -z "${obj_path}" -o -z "${obj_name}" ];then
        log_echo "ERROR" "The obj_path=${obj_path} or obj_name=${obj_name} has null.please chek."
        exit 1
    fi
    
    if [ -f ${CURRENT_PATH}/${obj_name}.tar.gz ];then
        log_echo "INFO" "The ${CURRENT_PATH}/${obj_name}.tar.gz is exist.do it."
        ${command_prefix} tar -zxvf ${CURRENT_PATH}/${obj_name}.tar.gz -C ${obj_path}
        if [ "X$(whoami)" == "Xroot" ];then
            chown ossuser:ossgroup ${obj_path}/${obj_name}
        fi
        ${command_prefix} bash -c "find ${obj_path}/${obj_name} -type f |xargs -i chmod 600 {}" > /dev/null 2>&1
        ${command_prefix} bash -c "find ${obj_path}/${obj_name} -type f |grep \".jar$\" |xargs -i chmod 400 {}"  > /dev/null 2>&1
        ${command_prefix} bash -c "find ${obj_path}/${obj_name} -type d |xargs -i chmod 700 {}" > /dev/null 2>&1
        ${command_prefix} chmod 700 ${obj_path}/${obj_name}
    fi
    
    log_echo "INFO" "The extend_node_preset end."
}

function add_dockuser_iptables_lvs()
{
    log_echo "INFO" "add_dockuser_iptables_lvs start..." 
    iptables -w -L FORWARD |grep "DOCKER\-USER" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "execute cmd:[ iptables -L FORWARD |grep \"DOCKER\-USER\" ] result is not equals 0"
        iptables -w -N DOCKER-USER
        iptables -w -D DOCKER-USER -j RETURN
        iptables -w -A DOCKER-USER -j RETURN
    fi
    iptables -w -D FORWARD -j DOCKER-USER
    iptables -w -I FORWARD 1 -j DOCKER-USER
    
    local rc_file=""
    if [ -f "/etc/rc.d/rc.local" ];then
        rc_file="/etc/rc.d/rc.local"
    fi
    if [ -f "/etc/rc.d/after.local" ];then
        rc_file="/etc/rc.d/after.local"
    fi
    if [ -f "/.dockerenv" -a -f "${CURRENT_PATH}/repair_iptables_lvs.sh" ];then
        cp -pf ${CURRENT_PATH}/repair_iptables.sh /os_config/repair_iptables.sh
        cp -pf ${CURRENT_PATH}/repair_iptables_lvs.sh /os_config/repair_iptables_lvs.sh
        chmod 500 /os_config/repair_iptables.sh
        chmod 500 /os_config/repair_iptables_lvs.sh
    elif [ ! -f "/.dockerenv" -a -f "${CURRENT_PATH}/repair_iptables_lvs.sh" ];then
        mkdir -p /root/repair_iptables
        cp -pf ${CURRENT_PATH}/repair_iptables_lvs.sh /root/repair_iptables
        cp -pf ${CURRENT_PATH}/repair_iptables.sh /root/repair_iptables
        cat ${rc_file}|grep "repair_iptables.sh"  > /dev/null 2>&1
        if [ $? -ne 0 ];then
            sed -i "\$a\sh /root/repair_iptables/repair_iptables.sh" ${rc_file} > /dev/null 2>&1
        fi
    fi

    log_echo "INFO" "Begin to add DOCKER-USER iptables for lvs"

    if [ "X${lvs_vip_ipv4}" != "X" -a "X${lvs_vip_ipv4}" != "X{{LVS_VIP_IPV4}}" ];then
        log_echo "INFO" "The lvs_vip_ipv4=${lvs_vip_ipv4}"
        if [ -f "/.dockerenv" ];then
            iptables -w -D FORWARD -j DOCKER-USER
            iptables -w -I FORWARD 1 -j DOCKER-USER
        fi
        
        iplist_add="${lvs_vip_ipv4}"
        
        if [ -f "${CURRENT_PATH}/DV_Config_LargeCapacity.config" ];then
            local PM_Node_IP_List=$(grep "^PM_Cluster_Node_IPV4_List=" ${CURRENT_PATH}/DV_Config_LargeCapacity.config | awk -F "=" '{print $2}' | sed 's#,# #g')
            iplist_add="${iplist_add} ${PM_Node_IP_List}"
        fi
        
        if [ -f "${CURRENT_PATH}/DV_Config_Cluster.config" ];then
            local PM_Node_IP_List=$(grep "^PM_Cluster_Node_IPV4_List=" ${CURRENT_PATH}/DV_Config_Cluster.config | awk -F "=" '{print $2}' | sed 's#,# #g')
            iplist_add="${iplist_add} ${PM_Node_IP_List}"
        fi
        
        if [ -f "${CURRENT_PATH}/DV_Config_MergeCluster.config" ];then
            Merge_NODE_IP1=$(grep "^DVPrimary_IPV4=" ${CURRENT_PATH}/DV_Config_MergeCluster.config|awk -F'=' '{print $2}')
            Merge_NODE_IP2=$(grep "^DVSecondary_IPV4=" ${CURRENT_PATH}/DV_Config_MergeCluster.config|awk -F'=' '{print $2}')
            
            Merge_Extend_List=$(grep "^SMExtend_Cluster_Node_IPV4_List=" ${CURRENT_PATH}/DV_Config_MergeCluster.config|awk -F'=' '{print $2}')
            if [ -z "${Merge_Extend_List}" ];then
                Merge_Extend_List=$(grep "^Extend_Cluster_Node_IPV4_List=" ${CURRENT_PATH}/DV_Config_MergeCluster.config|awk -F'=' '{print $2}')
            fi
            
            if [ ! -z "${Merge_Extend_List}" ];then
                Merge_NODE_IP1=$(echo "${Merge_Extend_List}"|awk -F',' '{print $1}')
                Merge_NODE_IP2=$(echo "${Merge_Extend_List}"|awk -F',' '{print $2}')
            fi
            
            iplist_add="${iplist_add} ${Merge_NODE_IP1} ${Merge_NODE_IP2}"
        fi
        
        if [ "X${netWorkType}" == "XM" ] && [ "X${SMPodExtend_Cluster_Node_IPV4_List}" != "X" -o "X${PodExtend_Cluster_Node_IPV4_List}" != "X" ];then
            local tmp_pod_ip_list=""
            [ "X${PodExtend_Cluster_Node_IPV4_List}" != "X" ] && tmp_pod_ip_list="${PodExtend_Cluster_Node_IPV4_List}"
            [ "X${SMPodExtend_Cluster_Node_IPV4_List}" != "X" ] && tmp_pod_ip_list="${SMPodExtend_Cluster_Node_IPV4_List}"
            
            local Merge_Node_List=$(echo "${tmp_pod_ip_list}" | sed 's/,/ /g')
            iplist_add="${iplist_add} ${Merge_Node_List}"
        fi
        
        if [ "X${netWorkType}" == "XM" -a "X${PodExtend_IPV4_List}" != "X" ];then
            local Merge_Node_List=$(echo "${PodExtend_IPV4_List}" | sed 's/,/ /g')
            iplist_add="${iplist_add} ${Merge_Node_List}"
        fi
        
        if [ "X${netWorkType}" == "XM" -a "X${merge_extend_ip_list}" != "X" ];then
            local Merge_Node_List=$(echo "${merge_extend_ip_list}" | sed 's/,/ /g')
            iplist_add="${iplist_add} ${Merge_Node_List}"
        fi
        
        for ip in ${iplist_add}
        do
            iptables -I DOCKER-USER -p all -s ${ip} -j ACCEPT
            iptables -I DOCKER-USER -p all -d ${ip} -j ACCEPT
        done
        
        
        if [ -f "/.dockerenv" -a -f "/os_config/repair_iptables_lvs.sh" ];then
            sed -i "s#{{docker_user_iplist}}#${iplist_add}#g" /os_config/repair_iptables_lvs.sh
            
        elif [ ! -f "/.dockerenv" -a -f "/root/repair_iptables/repair_iptables_lvs.sh" ];then
            sed -i "s#{{docker_user_iplist}}#${iplist_add}#g" /root/repair_iptables/repair_iptables_lvs.sh
        fi
    fi
}

function add_prerouting_iptables_lvs()
{
    if [ -f "/.dockerenv" ];then
        log_echo "INFO" "Containerized scene installation ,no need to config iptables."
        return 0
    fi

    local med_port="$1"
    local protocol_type="$2"
    
    if [ "X${lvs_vip_ipv4}" != "X" -a "X${lvs_vip_ipv4}" != "X{{LVS_VIP_IPV4}}" ] && [ "X${IPType}" != "X1" ];then
        if [ "X${APP_FloatIP_IPV4}" != "X" -a "X${APP_FloatIP_IPV4}" != "X{{APP_FloatIP_IPV4}}" ];then
            i2k_float_ip=${APP_FloatIP_IPV4}
        elif [ "X${DVFloatIP_IPV4}" != "X" -a "X${DVFloatIP_IPV4}" != "X{{DVFloatIP_IPV4}}" ];then
            i2k_float_ip=${DVFloatIP_IPV4}
        else
            log_echo "ERROR" "Get i2k float IP failed for add_prerouting_iptables_lvs"
            exit 1
        fi

        iptables -L PREROUTING -t nat | grep "${lvs_vip_ipv4}:${med_port}" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "INFO" "add rules for add_prerouting_iptables_lvs"
            iptables -w -t nat -A PREROUTING --dst ${i2k_float_ip} -p ${protocol_type} --dport ${med_port} -j DNAT --to-destination ${lvs_vip_ipv4}:${med_port}
        fi
        
        if [ -f "/etc/rc.d/rc.local" ];then
            grep "\-\-to\-destination ${lvs_vip_ipv4}:${med_port}" /etc/rc.d/rc.local > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i "\$a\iptables -t nat -A PREROUTING --dst ${i2k_float_ip} -p ${protocol_type} --dport ${med_port} -j DNAT --to-destination ${lvs_vip_ipv4}:${med_port}" /etc/rc.d/rc.local > /dev/null 2>&1
            fi
        fi
        if [ -f "/etc/rc.d/after.local" ];then
            grep "\-\-to\-destination ${lvs_vip_ipv4}:${med_port}" /etc/rc.d/after.local > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i "\$a\iptables -t nat -A PREROUTING --dst ${i2k_float_ip} -p ${protocol_type} --dport ${med_port} -j DNAT --to-destination ${lvs_vip_ipv4}:${med_port}" /etc/rc.d/after.local > /dev/null 2>&1
            fi
        fi
        adjust_sysctl "net.ipv4.ip_forward"  "1"
    fi

    if ( ! is_empty_param "${LVS_VIP_IPV6}" ) && [ "X${IPType}" != "X0" ];then
        if ( ! is_empty_param "${APP_FloatIP_IPV6}" );then
            i2k_float_ip=${APP_FloatIP_IPV6}
        elif ( ! is_empty_param "${DVFloatIP_IPV6}" );then
            i2k_float_ip=${DVFloatIP_IPV6}
        else
            log_echo "ERROR" "Get i2k float IP failed for add_prerouting_iptables_lvs"
            exit 1
        fi

        ip6tables -L PREROUTING -t nat |grep "\[${LVS_VIP_IPV6}\]:${med_port}" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "INFO" "add rules for add_prerouting_iptables_lvs"
            ip6tables -t nat -A PREROUTING --dst ${i2k_float_ip} -p ${protocol_type} --dport ${med_port} -j DNAT --to-destination [${LVS_VIP_IPV6}]:${med_port}
        fi

        if [ -f "/etc/rc.d/rc.local" ];then
            grep "\-\-to\-destination \[${LVS_VIP_IPV6}\]:${med_port}" /etc/rc.d/rc.local > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i "\$a\ip6tables -t nat -A PREROUTING --dst ${i2k_float_ip} -p ${protocol_type} --dport ${med_port} -j DNAT --to-destination [${LVS_VIP_IPV6}]:${med_port}" /etc/rc.d/rc.local > /dev/null 2>&1
            fi
        fi
        if [ -f "/etc/rc.d/after.local" ];then
            grep "\-\-to\-destination \[${LVS_VIP_IPV6}\]:${med_port}" /etc/rc.d/after.local > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i "\$a\ip6tables -t nat -A PREROUTING --dst ${i2k_float_ip} -p ${protocol_type} --dport ${med_port} -j DNAT --to-destination [${LVS_VIP_IPV6}]:${med_port}" /etc/rc.d/after.local > /dev/null 2>&1
            fi
        fi
        adjust_sysctl "net.ipv6.conf.all.forwarding"  "1"
    fi
    if [ -f /opt/oss/share/SOP/DVEngineeringService/I2000/run/etc/enableLegacyTLS.security ] && [ -f /opt/oss/SOP/apps/HOFSOsdFileAgent/bin/hfscmd ]; then
        su - ossuser -c "mkdir -p  /tmp/dvengineeringservicebucket/tls"
        su - ossuser -c "cp -p /opt/oss/share/SOP/DVEngineeringService/I2000/run/etc/enableLegacyTLS.security /tmp/dvengineeringservicebucket/tls"
        su - ossuser -c "/opt/oss/SOP/apps/HOFSOsdFileAgent/bin/hfscmd commitlocal dvengineeringservicebucket/tls/enableLegacyTLS.security  /tmp/"
        su - ossuser -c "rm -rf /tmp/dvengineeringservicebucket"
    fi

}

function add_postrouting_iptables_lvs()
{
    if [ -f "/.dockerenv" ];then
        log_echo "INFO" "Containerized scene installation ,no need to config iptables."
        return 0
    fi

    local med_port="$1"
    local protocol_type="$2"
    
    if [ "X${lvs_vip_ipv4}" != "X" -a "X${lvs_vip_ipv4}" != "X{{LVS_VIP_IPV4}}" ];then
        if [ "X${APP_FloatIP_IPV4}" != "X" -a "X${APP_FloatIP_IPV4}" != "X{{APP_FloatIP_IPV4}}" ];then
            i2k_float_ip=${APP_FloatIP_IPV4}
        elif [ "X${DVFloatIP_IPV4}" != "X" -a "X${DVFloatIP_IPV4}" != "X{{DVFloatIP_IPV4}}" ];then
            i2k_float_ip=${DVFloatIP_IPV4}
        else
            log_echo "ERROR" "Get i2k float IP failed for add_postrouting_iptables_lvs"
            exit 1
        fi

        iptables -L POSTROUTING -t nat | grep -w "${med_port}" > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "INFO" "add rules for add_postrouting_iptables_lvs"
            iptables -w -t nat -A POSTROUTING --dst ${lvs_vip_ipv4} -p ${protocol_type} --dport ${med_port} -j SNAT --to-source ${i2k_float_ip}
        fi
        
        if [ -f "/etc/rc.d/rc.local" ];then
            grep "\-\-dport ${med_port} \-j SNAT \-\-to\-source ${i2k_float_ip}" /etc/rc.d/rc.local > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i "\$a\iptables -t nat -A POSTROUTING --dst ${lvs_vip_ipv4} -p ${protocol_type} --dport ${med_port} -j SNAT --to-source ${i2k_float_ip}" /etc/rc.d/rc.local > /dev/null 2>&1
            fi
        fi
        if [ -f "/etc/rc.d/after.local" ];then
            grep "\-\-dport ${med_port} \-j SNAT \-\-to\-source ${i2k_float_ip}" /etc/rc.d/after.local > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i "\$a\iptables -t nat -A POSTROUTING --dst ${lvs_vip_ipv4} -p ${protocol_type} --dport ${med_port} -j SNAT --to-source ${i2k_float_ip}" /etc/rc.d/after.local > /dev/null 2>&1
            fi
        fi
    fi
}

function install_lvs()
{
    log_echo "INFO" "begin to install_lvs."
    rm -f /var/run/keepalived.pid
    keepalived -v > /dev/null 2>&1
    if [ $? -ne 0 ];then
        cd ${CURRENT_PATH}/LVS/keepalived
        unzip -o keepalived-*.jar -d keepalived

        cp -f keepalived/bin/keepalived_lvs /usr/sbin/keepalived
        [ $? -ne 0 ] && log_echo "ERROR" "cp keepalived to /usr/sbin failed" && exit 1
        cp -f keepalived/bin/genhash /usr/sbin/
        [ $? -ne 0 ] && log_echo "ERROR" "cp genhash to /usr/sbin failed" && exit 1
        
        mkdir -p /usr/lib64/dvlvslib
        cp -f keepalived/lib/* /usr/lib64/dvlvslib
        chmod 755 /usr/sbin/keepalived /usr/sbin/genhash
        chmod 755 -R /usr/lib64/dvlvslib
        sed -i "/#dv_lvs/d" /etc/ld.so.conf
        egrep  "^include\s+ld\.so\.conf\.d/\*\.conf$" /etc/ld.so.conf
        if [ $? -ne 0 ];then
            echo "include ld.so.conf.d/*.conf" >> /etc/ld.so.conf
        fi
        mkdir -p  /etc/ld.so.conf.d
        [ -f /etc/ld.so.conf.d/dv_lvs.conf ] && rm -f  /etc/ld.so.conf.d/dv_lvs.conf
        touch /etc/ld.so.conf.d/dv_lvs.conf
        echo "/usr/lib64" >> /etc/ld.so.conf.d/dv_lvs.conf
        echo "/usr/lib64/dvlvslib" >> /etc/ld.so.conf.d/dv_lvs.conf
        
        ldconfig > /dev/null 2>&1
        keepalived -v > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "keepalived does not work, please check"
            exit 1
        fi
        
        openssl version
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Failed to query the version because the openssl command is unavailable, please check /etc/ld.so.conf and check [ ldd $(which openssl) ]"
            exit 1
        fi
        
        rm -rf ${CURRENT_PATH}/LVS/keepalived/keepalived
    fi
    
    ipvsadm -l > /dev/null 2>&1
    if [ $? -ne 0 ];then
        cd ${CURRENT_PATH}/LVS/ipvsadm
        unzip -o ipvsadm-*.jar -d ipvsadm
        
        cp -f ipvsadm/bin/ipvsadm/ipvsadm /usr/sbin/
        [ $? -ne 0 ] && log_echo "ERROR" "cp ipvsadm to /usr/sbin failed" && exit 1
        chmod 755 /usr/sbin/ipvsadm
        
        ipvsadm -l > /dev/null 2>&1
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ipvsadm does not work, please check"
            exit 1
        fi
        rm -rf ${CURRENT_PATH}/LVS/ipvsadm/ipvsadm
    fi
    
    config_sudo "${i2k_user_name}" "${sudo_cmd_list_ossadm_lvs}" "ossadm"
    
    delete_iptables_lvs "${med_df_port}" "tcp"
    
    add_dockuser_iptables_lvs
    
    log_echo "INFO" "end to install_lvs."
}

function custom_for_C20SPC100B090()
{
    #C20SPC100B090消息跟踪cp了一个uniagent_client.jks到/opt/oss/SOP/etc/ssl/dv下，却没有添加信息到manifest，导致后续版本对证书做密码随机化时，获取不到uniagent_client.jks的旧密码，需要先删除
    eval "${TEST_DIR} ${install_path}/SOP/apps/DVEngineeringService"
    local ret=$?
    if [ "X${is_upgrade}" == "XYes" -a $ret -eq 0 ];then
        ${TEST_FILE} ${install_path}/SOP/etc/ssl/dv/uniagent_client.jks
        if [ $? -eq 0 ];then
            ${command_prefix} grep -w "uniagent_client.jks" ${install_path}/SOP/etc/ssl/dv/manifest.json > /dev/null 2>&1
            if [ $? -ne 0 ];then
                log_echo "INFO" "Find uniagent_client.jks but it's not in manifest.json under ${install_path}/SOP/etc/ssl/dv, delete it first."
                ${command_prefix} rm -rf ${install_path}/SOP/etc/ssl/dv/uniagent_client.jks
            fi
        fi
        ${TEST_FILE} ${i2k_user_home}/etc/ssl/dv/uniagent_client.jks
        if [ $? -eq 0 ];then
            ${command_prefix} grep -w "uniagent_client.jks" ${i2k_user_home}/etc/ssl/dv/manifest.json > /dev/null 2>&1
            if [ $? -ne 0 ];then
                log_echo "INFO" "Find uniagent_client.jks but it's not in manifest.json under ${i2k_user_home}/etc/ssl/dv, delete it first."
                ${command_prefix} rm -rf ${i2k_user_home}/etc/ssl/dv/uniagent_client.jks
            fi
        fi
    fi
}

function custom_i2kadm_for_icnfg()
{
    eval "${TEST_FILE} ${i2k_install_path}/I2000/bin/i2kadm"
    if [ $? -eq 0 ];then
        eval "${TEST_FILE} ${i2k_install_path}/icnfg/webpages/bin/kill_iCnfg.sh"
        if [ $? -ne 0 ];then
            ${command_prefix} sed -i "/exe_file .*kill_iCnfg.sh/d" ${i2k_install_path}/I2000/bin/i2kadm
        fi
    fi
}

function check_rpm_fuse()
{
    rpm -qa | grep "^fuse-[0-9]"
    if [ $? -ne 0 ];then
        log_echo "INFO" "Need fuse rpm for HOFS, begin to install..."
        cd ${CURRENT_PATH}/tools/rpm
        rpm -ivh fuse-*.rpm --nodeps --force
        if [ $? -ne 0 -o -z "`ls /usr/bin/fusermount`" ];then
            log_echo "ERROR" "Install fuse rpm failed, please check."
            exit 1
        fi
        chmod 755 /bin/fusermount
        chmod u+s /bin/fusermount
        chmod 755 /usr/bin/fusermount
        chmod u+s /usr/bin/fusermount
        cd - >/dev/null
    fi
}

function check_local_node()
{
    local ip_str="$1"
    check_ip "${ip_str}"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "The ip_str=${ip_str} , is error."
        exit 1
    fi
    
    tmp_ip=$(echo "${ip_str}"|sed "s/\./\\\./g")
    ip addr | grep -w "${tmp_ip}" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        node_role="$2"
        log_echo "INFO" "local node ip is ${ip_str}."
        return 0
    fi
    
    log_echo "INFO" "The ${ip_str} is not local ip." 
    return 1
}

function modify_ssh_port()
{
    local do_type="$1"
    log_echo "INFO" "modify_ssh_port do_type=${do_type} start"
    if [ "X${do_type}" == "Xmodify" ];then
        echo ""  >> /etc/ssh/sshd_config
        sed -i '/^Port /d' /etc/ssh/sshd_config
        echo "Port 55"  >> /etc/ssh/sshd_config

        echo ""  >> /etc/ssh/ssh_config
        sed -i '/^Port /d' /etc/ssh/ssh_config
        echo "Port 55"  >> /etc/ssh/ssh_config
    else
        sed -i '/^Port /d' /etc/ssh/sshd_config
        sed -i '/^Port /d' /etc/ssh/ssh_config
        sed -i '/^DenyUsers ossuser #DV_PreSet_Pod_DenyUsers/d' /etc/ssh/sshd_config
    fi
    is_modify_sshd=1
    is_modify_sftpd=0
    pod_restart_sshd_sftpd
    log_echo "INFO" "modify_ssh_port do_type=${do_type} finish."
}
 

function add_pod_iptables()
{
    log_echo "INFO" "add_pod_iptables start..."
    if [ -f /.dockerenv ];then
        if [ -f /os_config/DV_Config_MergeCluster_Docker.config ];then
            source /os_config/DV_Config_MergeCluster_Docker.config
        fi
        
        local tmp_ssh_port=22
        if [ "X${host_pre_install_key}" == "XHost_PreInstall" ]; then
            tmp_ssh_port=55
        fi
        
        log_echo "INFO" "Begin to add_pod_iptables." 
        check_ip "${HostPrimary_IPV4}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "The HostPrimary_IPV4=${HostPrimary_IPV4} , is error."
            exit 1
        fi
        
        check_ip "${HostSecondary_IPV4}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "The HostSecondary_IPV4=${HostSecondary_IPV4} , is error."
            exit 1
        fi
        
        node_role=""
        check_local_node "${DVPrimary_IPV4}" "Primary"
        check_local_node "${DVSecondary_IPV4}" "Secondary"
        iptables_add_ret=0
        if [ "X${node_role}" == "XPrimary" ];then
            tmp_peer_host_ip=$(echo "${HostSecondary_IPV4}"|sed "s/\./\\\./g")
            tmp_peer_pod_ip=$(echo "${DVSecondary_IPV4}"|sed "s/\./\\\./g")
            for i in $(seq 1 3);do
                check_ret=$(iptables -w -t nat -L -n |grep -w "${tmp_peer_host_ip}" |grep -w "${tmp_peer_pod_ip}")
                log_echo "INFO" "The node_role=${node_role} check_ret=${check_ret}.i=${i}"
                if [ -z "${check_ret}" ];then
                    log_echo "INFO" "execute cmd:[iptables -t nat -A OUTPUT -p tcp -d ${HostSecondary_IPV4} --dport ${tmp_ssh_port} -j DNAT --to-destination ${DVSecondary_IPV4}:${tmp_ssh_port}]."
                    iptables -w -t nat -A OUTPUT -p tcp -d ${HostSecondary_IPV4} --dport ${tmp_ssh_port} -j DNAT --to-destination ${DVSecondary_IPV4}:${tmp_ssh_port} >> $LOG_FILE 2>&1
                    iptables_add_ret=$?
                else
                    iptables_add_ret=0
                fi
                
                if [ ${iptables_add_ret} -eq 0 ];then
                    log_echo "INFO" "The iptables add success."
                    rm -f /home/<USER>/.ssh/known_hosts
                    break
                fi
                sleep 2
            done
        elif [ "X${node_role}" == "XSecondary" ];then
            tmp_peer_host_ip=$(echo "${HostPrimary_IPV4}"|sed "s/\./\\\./g")
            tmp_peer_pod_ip=$(echo "${DVPrimary_IPV4}"|sed "s/\./\\\./g")
            for i in $(seq 1 3);do
                check_ret=$(iptables -w -t nat -L -n |grep -w "${tmp_peer_host_ip}" |grep -w "${tmp_peer_pod_ip}")
                log_echo "INFO" "The node_role=${node_role} check_ret=${check_ret}.i=${i}"
                if [ -z "${check_ret}" ];then
                    log_echo "INFO" "execute cmd:[iptables -t nat -A OUTPUT -p tcp -d ${HostPrimary_IPV4} --dport ${tmp_ssh_port} -j DNAT --to-destination ${DVPrimary_IPV4}:${tmp_ssh_port}]."
                    iptables -w -t nat -A OUTPUT -p tcp -d ${HostPrimary_IPV4} --dport ${tmp_ssh_port} -j DNAT --to-destination ${DVPrimary_IPV4}:${tmp_ssh_port} >> $LOG_FILE 2>&1
                    iptables_add_ret=$?
                else
                    iptables_add_ret=0
                fi
                
                if [ ${iptables_add_ret} -eq 0 ];then
                    log_echo "INFO" "The iptables add success."
                    rm -f /home/<USER>/.ssh/known_hosts
                    break
                fi
                
                sleep 2
            done
        else
            log_echo "INFO" "The node_role=${node_role} not need to do it." 
        fi
        
        if [ ${iptables_add_ret} -ne 0 ];then
            log_echo "ERROR" "The iptables add failed. Please check whether iptables is occupied or whether the '/etc/resolv.conf' contains unreachable nameserver or IP addresses."
            exit 1
        fi

        if [ "X${host_pre_install_key}" == "XHost_PreInstall" ]; then
            modify_ssh_port "modify"
        fi
    fi
    
    log_echo "INFO" "add_pod_iptables finished." 
}

function del_pod_iptables()
{
    log_echo "INFO" "del_pod_iptables start..." 
    if [ -f /.dockerenv ];then
        if [ -f /os_config/DV_Config_MergeCluster_Docker.config ];then
            source /os_config/DV_Config_MergeCluster_Docker.config
        fi
        
        local tmp_ssh_port=22
        if [ "X${host_pre_install_key}" == "XHost_PreInstall" ]; then
            tmp_ssh_port=55
        fi
        
        log_echo "INFO" "Begin to del_pod_iptables." 
        check_ip "${HostPrimary_IPV4}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "The HostPrimary_IPV4=${HostPrimary_IPV4} , is error."
            exit 1
        fi
        
        check_ip "${HostSecondary_IPV4}"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "The HostSecondary_IPV4=${HostSecondary_IPV4} , is error."
            exit 1
        fi
        
        node_role=""
        check_local_node "${DVPrimary_IPV4}" "Primary"
        check_local_node "${DVSecondary_IPV4}" "Secondary"
        iptables_del_ret=0
        if [ "X${node_role}" == "XPrimary" ];then
            tmp_peer_host_ip=$(echo "${HostSecondary_IPV4}"|sed "s/\./\\\./g")
            tmp_peer_pod_ip=$(echo "${DVSecondary_IPV4}"|sed "s/\./\\\./g")
            check_ret=$(iptables -w -t nat -L -n |grep -w "${tmp_peer_host_ip}" |grep -w "${tmp_peer_pod_ip}")
            log_echo "INFO" "The node_role=${node_role} check_ret=${check_ret}."
            if [ ! -z "${check_ret}" ];then
                log_echo "INFO" "execute cmd:[iptables -t nat -D OUTPUT -p tcp -d ${HostSecondary_IPV4} --dport ${tmp_ssh_port} -j DNAT --to-destination ${DVSecondary_IPV4}:${tmp_ssh_port}]."
                iptables -w -t nat -D OUTPUT -p tcp -d ${HostSecondary_IPV4} --dport ${tmp_ssh_port} -j DNAT --to-destination ${DVSecondary_IPV4}:${tmp_ssh_port}
                iptables_del_ret=$?
                if [ ${iptables_del_ret} -ne 0 ];then
                    iptables --wait -t nat -D OUTPUT -p tcp -d ${HostSecondary_IPV4} --dport ${tmp_ssh_port} -j DNAT --to-destination ${DVSecondary_IPV4}:${tmp_ssh_port}
                    iptables_del_ret=$?
                fi
            fi
        elif [ "X${node_role}" == "XSecondary" ];then
            tmp_peer_host_ip=$(echo "${HostPrimary_IPV4}"|sed "s/\./\\\./g")
            tmp_peer_pod_ip=$(echo "${DVPrimary_IPV4}"|sed "s/\./\\\./g")
            check_ret=$(iptables -w -t nat -L -n |grep -w "${tmp_peer_host_ip}" |grep -w "${tmp_peer_pod_ip}")
            log_echo "INFO" "The node_role=${node_role} check_ret=${check_ret}."
            if [ ! -z "${check_ret}" ];then
                log_echo "INFO" "execute cmd:[iptables -t nat -D OUTPUT -p tcp -d ${HostPrimary_IPV4} --dport ${tmp_ssh_port} -j DNAT --to-destination ${DVPrimary_IPV4}:${tmp_ssh_port}]."
                iptables -w -t nat -D OUTPUT -p tcp -d ${HostPrimary_IPV4} --dport ${tmp_ssh_port} -j DNAT --to-destination ${DVPrimary_IPV4}:${tmp_ssh_port}
                iptables_del_ret=$?
                if [ ${iptables_del_ret} -ne 0 ];then
                    iptables --wait -t nat -D OUTPUT -p tcp -d ${HostPrimary_IPV4} --dport ${tmp_ssh_port} -j DNAT --to-destination ${DVPrimary_IPV4}:${tmp_ssh_port}
                    iptables_del_ret=$?
                fi
            fi
        else
            log_echo "INFO" "The node_role=${node_role} not need to do it." 
        fi
        
        if [ ${iptables_del_ret} -ne 0 ];then
            log_echo "ERROR" "The iptables del failed."
            exit 1
        fi
        rm -f /home/<USER>/.ssh/known_hosts

        if [ "X${host_pre_install_key}" == "XHost_PreInstall" ]; then
            modify_ssh_port "rollback"
        fi
    fi
    
    log_echo "INFO" "del_pod_iptables finished." 
}

function handle_hofs_dir()
{
    local hofs_dir="/opt/oss/hofs"
    
    if [ -L "${hofs_dir}" -o -n "`df -Ph | grep -w ${hofs_dir}`" -o -f "/.dockerenv" ];then
        log_echo "INFO" "No need to handle /opt/oss/hofs"
        if [ ! -d ${hofs_dir}/hofs_store ];then
            mkdir ${hofs_dir}/hofs_store
            chown ossuser:ossgroup ${hofs_dir}/hofs_store
            chmod 700 ${hofs_dir}/hofs_store
        fi
        return 0
    fi
    i2k_config_file=/opt/oss/share/SOP/DVEngineeringService/I2000/common/config/CommonInfo.ini
    eval "${TEST_FILE} ${i2k_config_file}"
    if [ $? -eq 0 ];then
        i2k_base_version=$(${command_prefix} cat ${i2k_config_file}|awk -F '=' '/version/ {print $2}')
        if [[ ${i2k_base_version} > V800R022 ]];then
            log_echo "INFO" "i2k_base_version is not less than V800R022, no need do handle /opt/oss/hofs"
            return 0
        fi
    fi
    chmod 770 /opt/oss
    ${command_prefix} bash -c "mkdir -p ${hofs_dir}/hofs_store"

    local hofs_available_size=$(df -PBG ${hofs_dir} |grep "/" |awk '{print $4}'|sed 's/G//g')
    
    if [ ${hofs_available_size} -lt ${HOFS_DISK_least_size} ] ; then
        i2k_path="/opt/oss/share/SOP/DVEngineeringService"
        need_size=$((HOFS_DISK_least_size+10))
        eval "${TEST_FILE} ${i2k_path}/I2000/bin/i2kadm"
        if [ $? -eq 0 ];then
            i2k_path_available_size=$(df -PBG ${i2k_path} |grep "/" |awk '{print $4}'|sed 's/G//g')
            if [ ${i2k_path_available_size} -lt ${need_size} ] ; then
                log_echo "ERROR" "The ${hofs_dir}'s available size is ${hofs_available_size}G, but we need ${HOFS_DISK_least_size}G at least, and ${i2k_path}'s available size is ${i2k_path_available_size}G, we can't creat soft link to it..."
                if [ -n "`ls /dev/drbd0 2>/dev/null`" ];then
                    log_echo "WARN" "It has drbd, you should expand the DRBD_disk both the primary and secondary node!"
                fi
                chmod 750 /opt/oss
                exit 1
            fi
            
            ${command_prefix} mkdir -p ${i2k_path}/hofs
            ${command_prefix} chown  ossuser: ${i2k_path}/hofs
            ${command_prefix} chown  ossuser: ${i2k_path}/hofs/hofs_store
            ${command_prefix} chmod  700 ${i2k_path}/hofs
            ${command_prefix} chmod  700 ${i2k_path}/hofs/hofs_store
            
            if [ -d "/opt/oss/SOP/apps/HOFSOsdFileAgent" ];then
                    source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopapp -app HOFSOsdFileAgent
            fi
            
            if [ -n "`${command_prefix} ls ${hofs_dir}/`" ];then
                sudo -u ossuser bash -c "cp -rp ${hofs_dir}/* ${i2k_path}/hofs"
                if [ $? -ne 0 ] ; then
                    log_echo "ERROR" "Copy files under ${hofs_dir} to ${i2k_path}/hofs failed..."
                    chmod 750 /opt/oss
                    exit 1
                fi
            else
                ls /dev/drbd0 > /dev/null 2>&1
                if [ $? -eq 0 ];then
                    if [ -d "/opt/oss/SOP/apps/HOFSOsdFileAgent" ];then
                        source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd stopapp -app HOFSOsdFileAgent
                    fi
                fi
            fi
        fi
        ${command_prefix} mkdir -p ${i2k_path}/hofs
        ${command_prefix} rm -rf ${hofs_dir:?}
        ${command_prefix} ln -sf ${i2k_path}/hofs ${hofs_dir}
        ${command_prefix} mkdir /opt/oss/hofs/hofs_store
        ${command_prefix} chmod  700 ${i2k_path}/hofs/hofs_store
        if [ "X$(whoami)" == "Xroot" ];then
            mkdir /opt/oss/hofs/hofs_store
            ${command_prefix} chown ossuser:ossgroup /opt/oss/hofs/hofs_store
            ${command_prefix} chmod  700 ${i2k_path}/hofs/hofs_store
        fi

        if [ $? -ne 0 ] ; then
            log_echo "ERROR" "create soft link ${i2k_path}/hofs to ${hofs_dir} failed..."
            chmod 750 /opt/oss
            exit 1
        fi
                
        if [ -d "/opt/oss/SOP/apps/HOFSOsdFileAgent" ];then
            source /opt/oss/manager/bin/engr_profile.sh;ipmc_adm -cmd startapp -app HOFSOsdFileAgent
        fi
    fi
    chmod 750 /opt/oss
}

function check_sshdconfig()
{
    sshdconfig_file="/etc/ssh/sshd_config"
    grep -w "^PubkeyAcceptedKeyTypes" ${sshdconfig_file} > /dev/null 2>&1
    if [ $? -eq 0 ];then
        if [ -f "/home/<USER>/.ssh/sshagent_privatekey" ];then
            grep -w "^PubkeyAcceptedKeyTypes" ${sshdconfig_file} |grep -w "rsa-sha2-256" > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i '/^PubkeyAcceptedKeyTypes/ s/$/,rsa-sha2-256/g' ${sshdconfig_file}
            fi
        else
            grep -w "^PubkeyAcceptedKeyTypes" ${sshdconfig_file} |grep -w "ssh-rsa" > /dev/null 2>&1
            if [ $? -ne 0 ];then
                sed -i '/^PubkeyAcceptedKeyTypes/ s/$/,ssh-rsa/g' ${sshdconfig_file}
            fi
        fi
        
        if [ -f /.dockerenv ];then
            restart_sshd
        else
            ## restart sshd 
            service sshd restart  >> $LOG_FILE
            if [ $? -ne 0 ];then
                restart_sshd_service
            fi
        fi
    fi
}

function upgrade_copy_jdbc()
{
    eval "${TEST_FILE} /home/<USER>/jdbc/copy_ojdbc8.tag"
    if [ $? -eq 0 ];then
        if [ "X$(whoami)" == "Xroot" ];then
            rm /home/<USER>/jdbc/copy_ojdbc8.tag
        else
            sudo -u ossuser rm /home/<USER>/jdbc/copy_ojdbc8.tag
        fi
    fi
    
    local lib_path="${install_path}/share/SOP/DVEngineeringService/I2000/run/3rdparty/lib"
    log_echo "INFO" "Execute upgrade_copy_jdbc start.lib_path=${lib_path}"
    eval "${TEST_DIR} ${lib_path}"
    if [ $? -ne 0 ];then
        log_echo "INFO" "The lib_path=${lib_path} is not exists.not need copy it."
        return 0
    fi
    
    local has_pkg=$(${command_prefix} bash -c "ls ${lib_path} |grep -w \"ojdbc8.*jar\"")
    if [ -z "${has_pkg}" ];then
        log_echo "INFO" "The lib_path:${lib_path} not found jar pkg ojdbc8.*jar"
        return 0
    fi
    
    mkdir_jdbc
    local jdbc_jar=$(${command_prefix} bash -c "ls ${lib_path} |grep -w \"ojdbc8.*jar\"" |tail -n 1)
    log_echo "INFO" "cp -rpf ${lib_path}/${jdbc_jar} /home/<USER>/jdbc"
    sudo -u ossuser cp -rpf ${lib_path}/${jdbc_jar} /home/<USER>/jdbc
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ cp -rpf ${lib_path}/${jdbc_jar} /home/<USER>/jdbc ] failed."
        exit 1
    fi
    eval "${TEST_FILE} /home/<USER>/jdbc/copy_ojdbc8.tag"
    if [ $? -ne 0 ];then
        if [ "X$(whoami)" == "Xroot" ];then
            touch /home/<USER>/jdbc/copy_ojdbc8.tag
        else
            sudo -u ossuser touch /home/<USER>/jdbc/copy_ojdbc8.tag
        fi
    fi
    if [ "X$(whoami)" == "Xroot" ];then
          chown ossuser:ossgroup /home/<USER>/jdbc/ojdbc8*jar
          chown ossuser:ossgroup  /home/<USER>/jdbc/copy_ojdbc8.tag
    fi

    ${command_prefix} chmod 400 /home/<USER>/jdbc/ojdbc8*jar
    ${command_prefix} chmod 600 /home/<USER>/jdbc/copy_ojdbc8.tag
    
    log_echo "INFO" "upgrade_copy_jdbc success."
}

function backup_ossuser_python()
{
    log_echo "INFO" "backup_ossuser_python start..."
    local filebackup_path=${CURRENT_PATH}/backup/${product_new_version}
    local dv_version_file="/opt/oss/share/SOP/DVEngineeringService/I2000/common/config/CommonInfo.ini"
    sudo -u ossuser bash -c "ls ${dv_version_file}" 2>/dev/null
    if [ $? -ne 0 ];then
        log_echo "INFO" "The dv_version_file=${dv_version_file} is not exist.not need do it."
        return 0
    fi
    
    local dv_version=$(sudo -u ossuser bash -c "cat ${dv_version_file}" |grep "FullVersion" |awk -F'=' '{print $2}'|sed "s/ //g")
    if [ -z "${dv_version}" ];then
        log_echo "INFO" "The dv_version=${dv_version} is null.not need do it."
        return 0
    fi
        
    log_echo "INFO" "The dv_version=${dv_version}"
    sudo -u ossuser bash -c "mkdir -p ${filebackup_path} && chmod 750 ${filebackup_path}"
    log_echo "INFO" "execute cmd:[ mkdir -p ${filebackup_path} && chmod 750 ${filebackup_path} ],ret=$?"
    sudo -u ossuser bash -c "mkdir -p ${filebackup_path}/ossuser_python_${dv_version} && chmod 750 ${filebackup_path}/ossuser_python_${dv_version}"
    log_echo "INFO" "execute cmd:[ mkdir -p ${filebackup_path}/ossuser_python_${dv_version} && chmod 750 ${filebackup_path}/ossuser_python_${dv_version} ],ret=$?"
    sudo -u ossuser bash -c "ls /home/<USER>/python/bin/python" 2>/dev/null
    if [ $? -eq 0 ];then
        sudo -u ossuser bash -c "cp -rpf /home/<USER>/python ${filebackup_path}/ossuser_python_${dv_version}"
        log_echo "INFO" "execute cmd:[ cp -rpf /home/<USER>/python ${filebackup_path}/ossuser_python_${dv_version} ],ret=$?"
    fi
    log_echo "INFO" "backup_ossuser_python finished."
}

function custom_icnfg_backup()
{
    # 针对R024C10、R024C20已发布的增量补丁，不涉及icnfg模块导致的升级C版本后再回退时恢复数据失败，在升级C版本时做特殊处理，备份icnfg
    sudo -u ossuser ls /opt/oss/SOP/apps/DVEngineeringService/I2000_*_ProductINSTALL_*.zip|grep -E  "V800R024C10CP1001|V800R024C10SPC101|V800R024C10SPC102|V800R024C20SPC001|V800R023C30CP0001|V800R024C00CP0021|V800R024C20SPC002|V800R024C00CP0001"
    if [ $? -ne 0 ];then
        return 0
    fi
    chmod 770 -R ${CURRENT_PATH}/custom_backup
    sudo -u ossuser cp -a /opt/oss/SOP/apps/DVEngineeringService/bin/internal/DVEngineeringService_backup.sh /opt/oss/SOP/apps/DVEngineeringService/bin/internal/DVEngineeringService_backup.sh_bak
    sudo -u ossuser cp -f ${CURRENT_PATH}/custom_backup/DVEngineeringService_backup.sh /opt/oss/SOP/apps/DVEngineeringService/bin/internal/DVEngineeringService_backup.sh
    if [ $? -ne 0 ];then
        log_echo "ERROR" "cp -f ${CURRENT_PATH}/custom_backup/DVEngineeringService_backup.sh /opt/oss/SOP/apps/DVEngineeringService/bin/internal/DVEngineeringService_backup.sh failed" 
        exit 1
    fi
    sudo -u ossuser chmod 500 /opt/oss/SOP/apps/DVEngineeringService/bin/internal/DVEngineeringService_backup.sh
    log_echo "INFO" "custom icnfg backup end" 

}

function is_contain_in_trust_cer()
{
    local tmp_ca_path="$1"
    local tmp_ca_content=$(cat ${tmp_ca_path} | grep -v "CERTIFICATE\-\-\-\-\-" | sed 's/^ *//;s/ *$//' | tr -d '\n')
    local trust_cer_path=${install_path}/SOP/etc/ssl/er_out/trust.cer
    OLD_IFS=$IFS
    IFS=$'\n'
    local start_lines=$(sed -n "/-----BEGIN CERTIFICATE-----/=" ${trust_cer_path})
    read -r -d '' -a start_line_array <<< "$start_lines"
    local end_lines=$(sed -n "/-----END CERTIFICATE-----/=" ${trust_cer_path})
    read -r -d '' -a end_line_array <<< "$end_lines"
    IFS=$OLD_IFS

    for i in "${!start_line_array[@]}"; do
        start_line=$((${start_line_array[$i]} + 1))
        end_line=$((${end_line_array[$i]} - 1))
        local content=$(sed -n "${start_line},${end_line}p" ${trust_cer_path} | sed 's/^ *//;s/ *$//' | tr -d '\n')
        if [ "X${content}" == "X${tmp_ca_content}" ]; then
            log_echo "INFO" "find trust.cer has the ca cert. return 0."
            return 0
        fi
    done
    log_echo "INFO" "cat not find trust.cer has the ca cert. return 1."
    return 1
}

function append_ca_to_erout()
{
    source ${install_path}/manager/agent/bin/engr_profile.sh
    export SSL_ROOT=${install_path}/manager/etc/ssl
    export CIPHER_ROOT=${install_path}/manager/etc/cipher

    log_echo "INFO" "start to append ca to ${install_path}/SOP/etc/ssl/er_out/trust.cer."

    local tmp_ca_cer=${CURRENT_PATH}/tmp_ca.cer
    rm -rf ${tmp_ca_cer:?}
    python ${CURRENT_PATH}/obtain_ca_by_rest.py "${tmp_ca_cer}"
    if [ -f "${tmp_ca_cer}" ]; then
        log_echo "INFO" "obtain ca successfully."
        dos2unix ${tmp_ca_cer}
        chmod 755 ${tmp_ca_cer}
        cat ${tmp_ca_cer} | grep "\-BEGIN CERTIFICATE" >/dev/null 2>&1
        if [ $? -eq 0 ]; then
            log_echo "INFO" "the ${tmp_ca_cer} has a cert content"
            is_contain_in_trust_cer "${tmp_ca_cer}"
            if [ $? -ne 0 ]; then
                log_echo "INFO" "dv ca cert is not contain in the trust.cer, need to append it"
                sudo -u ossuser bash -c "cp -f ${install_path}/SOP/etc/ssl/er_out/trust.cer ${install_path}/SOP/etc/ssl/er_out/trust.cer.bak"
                sudo -u ossuser bash -c "echo \" \" >> ${install_path}/SOP/etc/ssl/er_out/trust.cer"
                sudo -u ossuser bash -c "cat ${tmp_ca_cer} >> ${install_path}/SOP/etc/ssl/er_out/trust.cer"
                if [ $? -eq 0 ]; then
                    log_echo "INFO" "append ca to /opt/oss/SOP/etc/ssl/er_out/trust.cer successfully."
                else
                    log_echo "ERROR" "failed to append ca to /opt/oss/SOP/etc/ssl/er_out/trust.cer."
                    exit 1
                fi
            fi
        fi
   fi
   log_echo "INFO" "end to append ca to ${install_path}/SOP/etc/ssl/er_out/trust.cer."

}

function main()
{    
    log_echo "INFO" "Begin to prehandle." 
    
    init "$1"
    
    node_type=$2
    
    if [ "X${is_upgrade}" != "XYes" ];then
        
        check_dirs_io "${check_io_appnode}"
        
        check_hostname "${@: -1}"
        
        install_common_check

        init_user_passwd
        create_user_and_group
        
        set_system_operation_user
        
        create_dir
        
        change_permission
        
        adap_config_for_kylin
        
        stop_nfs
        if [ -d "${CURRENT_PATH}/LVS" ];then
            install_lvs
            kube_firewall_iptables "add"
        elif [[ "X${node_type}" =~ XPM[0-9] ]] || [[ "X${node_type}" =~ XDVExtendCluster[0-9] ]] || [[ "X${node_type}" =~ XSMExtendCluster[0-9] ]];then
            add_dockuser_iptables_lvs
            config_sudo "${i2k_user_name}" "${sudo_cmd_list_ossadm_lvs}" "ossadm"
        fi
        check_if_install_icnfg && devdata_set_permissions
        [ -d /opt/oss/log ] && chown ossadm:ossgroup /opt/oss/log
        mkdir_jdbc
        add_nodjs_path_for_tzdata
        set_loop_monitor ${CURRENT_PATH}/dv_loop_monitor.sh ${CURRENT_PATH}/dv_loop_monitor.service  ${CURRENT_PATH}/dv_loop_monitor.timer
        merge_cluster_network_adjust_sysctl
    else
        command_prefix=""
        if [ "X$(whoami)" == "Xroot" ];then
            command_prefix=""
        else
            command_prefix="sudo -u ossuser "
        fi
        TEST_FILE="${command_prefix} test -f "
        TEST_DIR="${command_prefix} test -d "

        chmod 770 ${CURRENT_PATH}
        ${command_prefix} mkdir -p ${CURRENT_PATH}/backup
        ${command_prefix} touch ${CURRENT_PATH}/add_cert.log
        ${command_prefix} chmod 660 ${CURRENT_PATH}/add_cert.log
        chmod 770 ${CURRENT_PATH}/kafka ${CURRENT_PATH}/certificate  ${CURRENT_PATH}/old_sign_dv ${CURRENT_PATH}/sign_dv
        
        check_modify_userpw_json
        
        backup_controller_server

        backup_dv_certificate
        
        backup_ossuser_python
        
        unset_chattr
        ## preset DVEngineeringService restore
        preset_restore
        ## preset modify dv start and monitor script 
        preBeforeUpgradePreSet
        
        if [ "X${i2k_is_dual}" == "XYes" ]; then
            forbidden_switch_hyperha
        fi
        
        if [ "X${i2k_dual_role}" != "XSecondary" -o "${need_drbd}" == "NO" ]; then
            check_if_install_icnfg && handle_icnfg_issue
        fi
        
        id ${system_sftp_user} >/dev/null 2>&1
        if [ $? -eq 0 -a -z "`cat /etc/passwd |grep \"^${system_sftp_user}:\" |grep \"/bin/false\"`" ];then
            usermod -s /bin/false ${system_sftp_user}
        fi
        
        change_certificat_keylen_for_rollback
        
        if [ "X$(whoami)" == "Xroot" ];then
              chown ossadm:ossgroup -R /usr/local/osconfig
              if [ -d /home/<USER>/.ssh ];then
                chmod  700 /home/<USER>/.ssh
                find  /home/<USER>/.ssh -type f |xargs -i chmod 600 {}
            fi
        fi

        custom_i2kadm_for_icnfg

        upgrade_copy_jdbc

        handle_ossuser_sshagent_privatekey

        append_ca_to_erout
    fi
    
    if [ "X${is_upgrade}" != "XYes" ];then
        set_env_params
        init_user_login
    fi
    
    if [ "X${node_type}" == "X" ];then
        if [ "X${is_upgrade}" != "XYes" ];then
            check_rpm_fuse

            rm -f /usr/bin/dv_python

            ln -s ${i2k_user_home}/python/bin/python /usr/bin/dv_python

            usermod_add_group sshonly dbuser

            copy_i2k_user_home_file

            replace_macro
        fi
        if [ "X${is_upgrade}" == "XYes" ];then
            eval "${TEST_DIR} ${i2k_user_home}/nbi_cert"
            local ret=$?
            if [ -d "${CURRENT_PATH}/nbi_cert" -a $ret -ne 0 ];then
                ${command_prefix} mkdir ${i2k_user_home}/nbi_cert
                chmod -R 775 ${CURRENT_PATH}/nbi_cert
                sudo -u ossuser bash -c "cp -rf ${CURRENT_PATH}/nbi_cert/nbi* ${i2k_user_home}/nbi_cert"
                chmod -R 755 ${CURRENT_PATH}/nbi_cert
                ${command_prefix} chmod 700 ${i2k_user_home}/nbi_cert
                sudo -u ossuser bash -c "find ${i2k_user_home}/nbi_cert -type f -exec chmod 600 {} +"  > /dev/null 2>&1
            fi
            
            cd /home
            for file_500 in $(${command_prefix} find ${i2k_user_home} -path ${i2k_user_home}/sudoScripts -prune -o -name "*" -type f | grep -E '*.sh$' ) ;do
                ${command_prefix} chmod 500 ${file_500} > /dev/null 2>&1
            done
            cd -
            sudo -u ossuser test -e /opt/oss/share/SOP/DVEngineeringService/I2000
            if [ $? -eq 0 ];then
                echo "product_new_version=${product_new_version}" > /home/<USER>/product_new_version.tag
            fi

            custom_icnfg_backup

        fi
        set_sudo "${@: -1}"
        if [ "X${i2k_is_dual}" == "XYes" ]; then
            if [ "X$(whoami)" == "Xroot" ];then
                check_sshdconfig
            fi

            if [ "X${i2k_dual_role}" == "XPrimary" -a "X${is_upgrade}" != "XYes" ]; then
                touch ${i2k_user_home}/primary.tag
                chown ${i2k_user_name}:${i2k_group_name} ${i2k_user_home}/primary.tag
            fi
            
            if [ "X${is_upgrade}" == "XYes" -a -d "${install_path}/SOP/apps/DVEngineeringService/envs" -a "X${is_express}" == "XNo" ];then
                if [ "`echo ${is_install_icnfg}|tr 'a-z' 'A-Z'`" == "YES" ];then
                    ${command_prefix} sed -i "s/IS_INSTALL_ICNFG=.*/IS_INSTALL_ICNFG=Yes/g" `${command_prefix} grep IS_INSTALL_ICNFG= -rl ${install_path}/SOP/apps/DVEngineeringService/envs`
                else
                    ${command_prefix} sed -i "s/IS_INSTALL_ICNFG=.*/IS_INSTALL_ICNFG=No/g" `${command_prefix} grep IS_INSTALL_ICNFG= -rl ${install_path}/SOP/apps/DVEngineeringService/envs`
                fi
            fi
        fi

        if [ "X${is_upgrade}" != "XYes" ];then
            delete_iptables_lvs "${med_df_port}" "tcp"

            add_prerouting_iptables_lvs "${med_snmp_port}" "udp"

            add_dockuser_iptables_lvs

            chmod 644 /etc/profile.d/i2k-env.csh /etc/profile.d/i2k-env.sh > /dev/null 2>&1
            chown root:root /etc/profile.d/i2k-env.csh /etc/profile.d/i2k-env.sh > /dev/null 2>&1
        fi
        if [ "X${is_upgrade}" == "XYes" -a -d "${install_path}/SOP/apps/DVEngineeringService" ];then
            handle_hofs_dir
        fi

        if [ "X${is_upgrade}" != "XYes" ];then
            mkdir -p /opt/oss/share/SOP/DVEngineeringService/I2000/run/var/iemp/data/ftp
            mount_loop_device "/opt/oss/share/SOP/DVEngineeringService/I2000/run/var/iemp/data/disk_ftp.img" "20480" "1024000" "/opt/oss/share/SOP/DVEngineeringService/I2000/run/var/iemp/data/ftp"
            [ -d /opt/oss/ ]  &&  chown  ossadm:ossgroup /opt/oss/
            [ -d /opt/oss/share/ ]  &&  chown  ossadm:ossgroup /opt/oss/share/
            [ -d /opt/oss/share/SOP/ ]  &&  chown  ossadm:ossgroup /opt/oss/share/SOP/
            [ -d /opt/oss/share/SOP/DVEngineeringService/ ]  &&  chown  ossuser:ossgroup /opt/oss/share/SOP/DVEngineeringService/
            [ -d /opt/oss/share/SOP/DVEngineeringService/I2000 ] && chown ossuser:ossgroup -R /opt/oss/share/SOP/DVEngineeringService/I2000
        fi
    else
        if [ "X$(whoami)" == "Xroot" ];then
            config_common_sudo
        fi
    fi
    if [ "X${is_upgrade}" != "XYes" ];then
        if [ "$(echo ${is_integrated_df_and_service}|tr 'a-z' 'A-Z')" == "YES" -a ! -f "/.dockerenv" ];then
            set_swap_off
            add_core_binding_timer
        fi
    fi

    if [ "X${is_upgrade}" != "XYes" ];then
        clean_env
        
        ## $1 = listenIpList
        config_ssh "$1"
        
        add_at_allow
        
        grep -E "^wheel:" /etc/group > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            usermod_add_group wheel ${i2k_user_name}
            usermod_add_group wheel ${system_operation_user}
            usermod_add_group wheel ossadm
        fi
        
        if [ "X${node_type}" == "X" ];then
            if [ "X${i2k_is_dual}" == "XYes" -a "${need_drbd}" == "YES" ]; then
                config_drbd
            fi
            config_profile
            
            grep -w "env_keep" /etc/sudoers |grep -w "USERNAME" > /dev/null 2>&1
            if [ $? -eq 0 ];then
                sed -i "/env_keep/ s/ USERNAME//g" /etc/sudoers
            fi
        fi
        
        create_backup_path
        
        if [ -d "${install_path}" -a "X${node_type}" == "X" ];then
            if [ ! -d ${dvshare_install_path} ];then
                mkdir -p ${dvshare_install_path}
            fi
            
            chown ossadm:ossgroup ${install_path} ${install_path}/share ${install_path}/share/SOP
            chmod 750 ${install_path} ${install_path} ${install_path}/share ${install_path}/share/SOP
            chmod 770 -R ${dvshare_install_path}
            chown  -R ${i2k_user_name}:${i2k_group_name} ${i2k_install_path}
            chmod 750 ${i2k_install_path}
        fi  

    fi
    
    custom_for_C20SPC100B090
    #please ensure the last parameter is node ip
    initial_certificate "${@: -1}"
    
    preset_sign
    
    preset_cipher_dv
    
    if [[ "X${node_type}" =~ X(SM[0-9]|SMExtendCluster[0-9]|DVExtendCluster[0-9]|AE[0-9]) ]];then
        local tmp_path=$(echo $CURRENT_PATH |awk -F '/' '{print $NF}')
        if [ "X${tmp_path}" == "XDVPreSet_extend" ];then
            log_echo "INFO" "no need run the preset_dvca command in capacity expansion scenarios."
		else
	        preset_dvca
        fi
    fi

    
    if [ "X${is_upgrade}" != "XYes" ];then
        adjust_parameter

        if [ -f "${CURRENT_PATH}/mount_extdisk_finish" ];then
            cp ${CURRENT_PATH}/mount_extdisk_finish ${system_login_userhome}
            chown ${system_login_user}:${i2k_group_name} ${system_login_userhome}/mount_extdisk_finish
            chmod 600 ${system_login_userhome}/mount_extdisk_finish
            if [ -d "${system_login_userhome}" -a -f "${CURRENT_PATH}/new_partition_finish" ];then
                cp ${CURRENT_PATH}/new_partition_finish ${system_login_userhome}/new_partition_finish
                chown ${system_login_user}:${i2k_group_name} ${system_login_userhome}/new_partition_finish
                chmod 600 ${system_login_userhome}/new_partition_finish
            fi
        fi

        [ -d "${VS_PATH}" ] && chown -R ${i2k_user_name}:${i2k_group_name} "${VS_PATH}"

        [ -d "${KAFKA_PATH}" ] && chown -R ${i2k_user_name}:${i2k_group_name} "${KAFKA_PATH}"
    fi

    if [ ! -d ${install_path}/SOP/etc ];then
        mkdir -p ${install_path}/SOP/etc
    fi

    if [ "X$(whoami)" == "Xroot" ];then
        chown ossadm:ossgroup ${install_path}/SOP
        chown ossadm:ossgroup ${install_path}/SOP/etc
    fi
    chmod 750 ${install_path}/SOP
    chmod 750 ${install_path}/SOP/etc

    eval "${TEST_DIR} ${install_path}/SOP/etc/ssl"
    if [ $? -ne 0 ];then
        ${command_prefix} mkdir -p ${install_path}/SOP/etc/ssl
        if [ "X$(whoami)" == "Xroot" ];then
            chown ossuser:ossgroup ${install_path}/SOP/etc/ssl
        fi
        ${command_prefix} chmod 750 ${install_path}/SOP/etc/ssl
    fi
    
    extend_node_preset "${install_path}/SOP/etc" "cipher_dv"
    extend_node_preset "${install_path}/SOP/etc" "sign_dv"
    extend_node_preset "${install_path}/SOP/etc/ssl" "dv"

    eval "${TEST_DIR} /home/<USER>/etc"
    if [ $? -ne 0 ];then
        ${command_prefix} mkdir -p /home/<USER>/etc
        if [ "X$(whoami)" == "Xroot" ];then
            chown ossuser:ossgroup /home/<USER>/etc
        fi
        ${command_prefix} chmod 700 /home/<USER>/etc
    fi

    eval "$TEST_DIR /home/<USER>/etc/ssl"
    if [ $? -ne 0 ];then
        ${command_prefix} mkdir -p /home/<USER>/etc/ssl
        if [ "X$(whoami)" == "Xroot" ];then
            chown ossuser:ossgroup /home/<USER>/etc/ssl
        fi
        ${command_prefix} chmod 700 /home/<USER>/etc/ssl
    fi
    
    extend_node_preset "/home/<USER>/etc" "cipher_dv"
    extend_node_preset "/home/<USER>/etc" "sign_dv"
    extend_node_preset "/home/<USER>" "jdbc"
    
    log_echo "INFO" "find chmod file.pwd=$(pwd)"
    cd ${CURRENT_PATH}
    [ -d ${CURRENT_PATH}/certificate/demoCA ] && chmod 700 ${CURRENT_PATH}/certificate/demoCA
    find ${CURRENT_PATH} -path ${CURRENT_PATH}/backup -prune -o -type d | xargs -i chmod 755 {} > /dev/null 2>&1
    find ${CURRENT_PATH} -path ${CURRENT_PATH}/backup -prune -o -name "*.log" -print0| xargs -0 --no-run-if-empty chmod 640 > /dev/null 2>&1
    find ${CURRENT_PATH} -path ${CURRENT_PATH}/backup -prune -o -type f -a \( -name "*.crt" -o -name "*.pem" -o -name "*.csr" \) -print0 | xargs -0 --no-run-if-empty chmod 600 > /dev/null 2>&1

    ${command_prefix} find ${CURRENT_PATH}/backup -type d | ${command_prefix} xargs -i chmod 755 {} > /dev/null 2>&1
    ${command_prefix} find ${CURRENT_PATH}/backup -name "*.log" -print0| xargs -0 --no-run-if-empty ${command_prefix} chmod 640 > /dev/null 2>&1
    ${command_prefix} find ${CURRENT_PATH}/backup -type f -a \( -name "*.crt" -o -name "*.pem" -o -name "*.csr" \) -print0 | xargs -0 --no-run-if-empty ${command_prefix} chmod 600 > /dev/null 2>&1
    cd -
    if [ "X${is_upgrade}" != "XYes" ];then
        if [ -d ${install_path}/uds_ossuser ];then
            chown -R ${i2k_user_name}:${i2k_group_name}  ${install_path}/uds_ossuser
        fi
        set_acl_permission
    fi

    create_hofs_dir "${node_type}"

    if [ "X${node_type}" == "X" ];then
        add_alias "appNode"
    else
        add_alias
    fi

    if [ "X${is_upgrade}" != "XYes" ];then
        backExtDiskInfo

        modify_tmpdir_authority

        modify_iptables "add"
    
        permission_handle
    fi

    if [ "X${is_upgrade}" != "XYes" ];then
        set_user_chage
        if [ "X${i2k_dual_role}" == "XSecondary" -a "${need_drbd}" == "YES" ]; then
            mkdir -p "${i2k_new_install_path}"
            chmod 755 "/opt/share"
            rm -rf "${i2k_install_path}"
            ln -sf "${i2k_new_install_path}" "${i2k_install_path}"
            chown -h ${i2k_user_name}:${i2k_group_name} "${i2k_install_path}"
            chown -R ${i2k_user_name}:${i2k_group_name} "${i2k_new_install_path}"
        fi
    fi

    if [ "X${is_upgrade}" != "XYes" ];then
        rm -rf /home/<USER>/sudoScripts/create_dvshare_path.sh
        rm -rf /home/<USER>/sudoScripts/i2kadm_logcollect.sh
        rm -rf /home/<USER>/sudoScripts/I2000_gatherinfo.sh
        rm -rf /home/<USER>/sudoScripts/I2000_gatherinfo_branch.sh
        persist_system_files_in_container
    fi
    
    remove_ca_privatekey

    if [ "X${is_upgrade}" != "XYes" ];then
        # delete cie crontab task
        crontab -l | grep -v DMU_HOME/config/log4j2.xml >/tmp/crontabCIE
        crontab /tmp/crontabCIE >/dev/null 2>&1
        rm -rf /tmp/crontabCIE
    fi
    
    add_dbuser_to_sshonly
    
    if [ -f /.dockerenv -a "X$(whoami)" == "Xroot" ];then
        change_enforce_for_root
    fi
    if [ "X${is_upgrade}" != "XYes" ];then
        config_firstlogin
    fi
    log_echo "INFO" "Prehandle finished." 
}

function check_tag_and_wait()
{   
    local tag_file="$1"
    
    chattr -i -R ${HOME_PATH}/dv > /dev/null 2>&1
    
    if [ -d ${OPT_PATH}/pub ];then
        chmod 750 ${OPT_PATH}/pub
        chown root:ossgroup ${OPT_PATH}/pub
    fi
    
    if [ -z "${tag_file}" ];then
        log_echo "check_tag_and_wait tag_file=$tag_file is null."
        return 0
    fi
    
    local wait_count=0
    local wait_max=30
    while [ ${wait_count} -lt ${wait_max} ]
    do
        if [ -f ${tag_file} ];then
            log_echo "The ${tag_file} is exists."
            return 0
        else
            log_echo "The ${tag_file} is not exists, check again after 2 seconds.wait_count=$wait_count"
            sleep 2
            let "wait_count++"
        fi
    done
    
    log_echo "INFO" "check_tag_and_wait The $tag_file is not exists."
    return 0
}

function add_group()
{
    local group_name="$1"
    local group_id="$2"
    if [ -z "${group_name}" -o -z "${group_id}" ];then
        log_echo "ERROR" "The group_name=${group_name} or group_id=${group_id} has null."
        exit 1
    fi
    
    cat /etc/group |grep "^${group_name}:" |grep ":${group_id}:" >> ${LOG_FILE} 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "The group name: ${group_name} and group id: ${group_id} is exists."
        return 0
    fi
    
    cat /etc/group |grep "^${group_name}:" >> ${LOG_FILE} 2>&1
    if [ $? -eq 0 ];then
        log_echo "ERROR" "The group name: ${group_name} is exists but group id is not ${group_id} "
        exit 1
    fi
    
    cat /etc/group |grep ":${group_id}:" >> ${LOG_FILE} 2>&1
    if [ $? -eq 0 ];then
        log_echo "ERROR" "The group id: ${group_id} is exists but group name is not ${group_name} "
        exit 1
    fi
    
    groupadd -g ${group_id} ${group_name} 
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[groupadd -g ${group_id} ${group_name} ] failed."
        exit 1
    fi
    
    log_echo "INFO" "add group ${group_name}:${group_id} success."
}

function config_timer()
{
    log_echo "INFO" "start config_crontab..."
    if [ ! -f ${OPT_PATH}/dv_os_config/DV_Config_MergeCluster_Docker.config ];then
        log_echo "INFO" "The ${OPT_PATH}/dv_os_config/DV_Config_MergeCluster_Docker.config is not exists."
        return 0
    fi
    
    local monitor_dir=$ROOT_CUSTOM_PATH/dv_pod_monitor
    local monitor_script=${monitor_dir}/dv_container_monitor.sh
    local tmp_dir_script=${CURRENT_PATH}/tools/docker/dv_container_monitor.sh
   
    if [ ! -f ${tmp_dir_script} ];then
        log_echo "INFO" "The tmp_dir_script=${tmp_dir_script} is not exists."
        return 0
    fi
    
    
    ## HostThird_IPV4
    local third_ip=$(cat ${OPT_PATH}/dv_os_config/DV_Config_MergeCluster_Docker.config | grep "^HostThird_IPV4=" |awk -F'=' '{print $2}')
    local tmp_ip=$(echo "${third_ip}"|sed "s/\./\\\./g")
    if [ -z "${tmp_ip}" ];then
        log_echo "INFO" "The third_ip=${third_ip} null."
        return 0
    fi
    
    ip addr|grep -w "${tmp_ip}" >> ${LOG_FILE} 2>&1 
    if [ $? -eq 0 ];then
        log_echo "INFO" "The third_ip=${third_ip} not need to add crontab."
        return 0
    fi
    
    mkdir -p ${monitor_dir}
    chmod 750 ${monitor_dir}
    cp -rf ${tmp_dir_script} ${monitor_script}
    chmod 750 ${monitor_script}
    log_echo "INFO" "cp -rf ${tmp_dir_script} ${monitor_script} success."

    config_sudo sysomc "${monitor_script}"

    
    log_echo "INFO" "Config timer to dv_pod_monitor."


    if [ -f ${CURRENT_PATH}/tools/docker/dv_container_monitor.service ];then
        cp -f  ${CURRENT_PATH}/tools/docker/dv_container_monitor.service  /etc/systemd/system/
        chmod 644 /etc/systemd/system/dv_container_monitor.service
    else    
        log_echo "ERROR" " ${CURRENT_PATH}/tools/docker/dv_container_monitor.service does not exists"
        exit 1
    fi

    if [ -f  ${CURRENT_PATH}/tools/docker/dv_container_monitor.timer ];then
        cp -f  ${CURRENT_PATH}/tools/docker/dv_container_monitor.timer  /etc/systemd/system/
        chmod 644 /etc/systemd/system/dv_container_monitor.timer
    else    
        log_echo "ERROR" " ${CURRENT_PATH}/tools/docker/dv_container_monitor.timer does not exists"
        exit 1
    fi

    systemctl daemon-reload
    systemctl start dv_container_monitor.service

    systemctl enable dv_container_monitor.service

    systemctl start dv_container_monitor.timer
    if [ $? -ne 0 ];then
        log_echo "ERROR" "core_binding.service start failed use 'systemctl start dv_container_monitor.timer'"
        exit 1
    fi
    systemctl enable dv_container_monitor.timer
  
    log_echo "INFO" "Config timer to dv_pod_monitor finished."
}

function config_host()
{
    if [ ! -f ${CURRENT_PATH}/root_shadow ];then
        local root_shadow=$(cat /etc/shadow | grep "^${oper_user}:" | sed "s/${oper_user}:/root:/1")
        echo "${root_shadow}" > ${CURRENT_PATH}/root_shadow
    fi
    
    local sshonly_group=$(cat /etc/group | grep -E "^sshonly:")
    local has_paas=$(cat /etc/group | grep -E "^sshonly:"|grep -w "paas")
    if [ ! -z "${sshonly_group}" -a -z "${has_paas}" ];then
        usermod -a -G sshonly paas
        log_echo "INFO" "Execute cmd[usermod -a -G sshonly paas],ret=$?"
    fi
    
    if [ -f /etc/rc.d/rc.local ];then
        chmod +x /etc/rc.d/rc.local
        log_echo "INFO" "execute cmd:[ chmod +x /etc/rc.d/rc.local ],ret=$?,check_ret=$(ls -l /etc/rc.d/rc.local)"
    fi
}

function extend_pod_preset()
{
    if [ -f "/.dockerenv" ];then
        log_echo "INFO" "execute extend_pod_preset start..."
        chown root:ossgroup /opt/pub
        chmod 750 /opt/pub
        chown ossadm:ossgroup /opt/oss/share
        chown ossadm:ossgroup /opt/oss/share/SOP
        chown ossuser:ossgroup /opt/oss/share/SOP/DVEngineeringService
        chown ossuser:ossgroup /opt/oss/share/SOP/DVEngineeringService/trace

        find /opt/oss -user root -name lost+found -exec rm -rf {} +
        local root_path=$(find /opt/oss -user root)
        if [ ! -z "${root_path}" ];then
            log_echo "ERROR" "Execute cmd:[ find /opt/oss -user root ],has root path or file: ${root_path}.please check."
            exit 1
        fi
        log_echo "INFO" "execute extend_pod_preset End."
    fi
}

function forbidden_switch_hyperha()
{
    if  [ -d /opt/oss/manager/apps/HyperHAAgent ];then
        if [ ! -f /opt/oss/manager/var/share/hyperha_standby_flag_file ];then
            if [ "X$(whoami)" == "Xroot" ];then
                  su - ossadm -c "/opt/oss/manager/apps/HyperHAAgent/bin/hactl freeze node -t=1440"
            else
                  /opt/oss/manager/apps/HyperHAAgent/bin/hactl freeze node -t=1440
            fi
            if [ $? -ne 0 ];then
                log_echo "ERROR" "forbidden switch hyperha failed"
                exit 1
            fi
        fi
    fi
}


function check_devdata_trust()
{
    local local_user=ossuser
    local ssh_user=devdata
    local local_ip=$1
    local remote_ip=$2
    local result=0

    su - ${local_user} -c "ssh -o NumberOfPasswordPrompts=0 -o StrictHostKeyChecking=no  ${ssh_user}@${local_ip} -p ${i2k_ssh_port} date " > /dev/null
    if [ $? -ne 0 ];then
        log_echo "ERROR" "The SSH trust relationship between the ${local_user}@localhost and the host whose IP address is  ${ssh_user}@${local_ip} is damaged."
        result=1
    fi

    su - ${local_user} -c "ssh -o NumberOfPasswordPrompts=0 -o StrictHostKeyChecking=no  ${ssh_user}@${remote_ip} -p ${i2k_ssh_port} date " > /dev/null
    if [ $? -ne 0 ];then
        log_echo "ERROR" "The SSH trust relationship between the ${local_user}@localhost and the host whose IP address is  ${ssh_user}@${remote_ip} is damaged."
        result=1
    fi
    if [ ${result} -ne 0 ];then
        exit 1
    fi
}

function pod_restore()
{
    log_echo "INFO" "pod_restore start..."
    pod_id=$(docker ps |grep "dv-container" |awk '{print $1}')
    if [ -z "${pod_id}" ];then
        log_echo "ERROR" "Execute cmd:[ docker ps |grep dv-container |awk '{print \$1}' ] get pod id failed, get pod id is null. please check..."
        exit 1
    fi
    
    docker exec -i ${pod_id} /bin/bash /os_config/pod_restore.sh
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ docker exec -i ${pod_id} /bin/bash /os_config/pod_restore.sh ] failed, please check..."
        exit 1
    fi
    log_echo "INFO" "pod_restore finished."
}

function chown_dir()
{
    log_echo "INFO" "chown_dir start..."
    local tmp_user="$1"
    local tmp_group="$2"
    local base_dir="$3"
    local chown_dir="$4"
    
    if [ ! -d "${base_dir}" ];then
        log_echo "INFO" "The base_dir=${base_dir} is not exists."
        return 0
    fi
    
    local next_dir="${base_dir}/${chown_dir}"
    while [ ${next_dir} != "${base_dir}" ];do
        if [ -d ${next_dir} ];then
            chown ${tmp_user}:${tmp_group} ${next_dir}
            log_echo "INFO" "chown ${tmp_user}:${tmp_group} ${next_dir} finish."
        fi
        next_dir=$(dirname ${next_dir})
    done
    log_echo "INFO" "chown_dir finish."
}

function restore_host()
{
    log_echo "INFO" "restore_host start..."
    if [ -f /opt/dv_os_config/DV_Config_MergeCluster_Docker.config ];then
        id  ${system_operation_user} > /dev/null 2>&1
        if [ $? -eq 0 ];then
            config_timer
        fi
    fi
    
    mkdir -p /home/<USER>/
    mount_loop_device "/home/<USER>/disk_sftpossuser.img" "1024" "102400" "/home/<USER>/sftpossuser" 
    [ -d /home/<USER>/home/<USER>
    log_echo "INFO" "img=$(ls -al /home/<USER>/disk_sftpossuser.img) sftpossuser=$(ls -al /home/<USER>"
    
    i2k_ftp=$(cat /etc/fstab |grep "/opt/share/I2000/run/var/iemp/data/ftp")
    log_echo "INFO" "i2k_ftp=${i2k_ftp}"
    if [ ! -z "${i2k_ftp}" ];then
        log_echo "INFO" "mkdir /opt/share/I2000/run/var/iemp/data/ftp"
        mkdir -p /opt/share/I2000/run/var/iemp/data/ftp
        chown_dir "3004" "ossgroup" "/opt/share" "I2000/run/var/iemp/data/ftp"
        mount_loop_device "/opt/share/I2000/run/var/iemp/data/disk_ftp.img" "20480" "1024000" "/opt/share/I2000/run/var/iemp/data/ftp" 
        chown 3004:ossgroup /opt/share/I2000/run/var/iemp/data/ftp
        log_echo "INFO" "img=$(ls -al /opt/share/I2000/run/var/iemp/data/disk_ftp.img) sftpossuser=$(ls -al /opt/share/I2000/run/var/iemp/data|grep -w ftp)"
    fi
    
    readlink /etc/localtime > /opt/dv_os_config/time_link
    log_echo "INFO" "readlink /etc/localtime to /opt/dv_os_config/time_link,ret=$?"
    log_echo "INFO" "restore_host finished."
}

function merge_cluster_network_adjust_sysctl()
{
    local check_ret=$(cat /etc/sysctl.conf |grep "net.ipv4.neigh.default.gc_thresh")
    log_echo "INFO" "merge_cluster_network_adjust_sysctl before.check_ret=${check_ret}"
    if [ -f "/.dockerenv" ];then
        log_echo "INFO" "This docker env. has /.dockerenv file.not need adjust sysctl."
        return 0
    fi
    
    if [ ! -f ${CURRENT_PATH}/netWorkTypeOfDvConfigParameters.config ];then
        log_echo "INFO" "The ${CURRENT_PATH}/netWorkTypeOfDvConfigParameters.config is not exist.not need adjust sysctl."
        return 0
    fi
    
    local pm_node_exist=$(cat ${CURRENT_PATH}/netWorkTypeOfDvConfigParameters.config |grep "^pm_node_exist=.*"|awk -F'=' '{print $2}')
    if [ "${pm_node_exist}" != "Yes" ];then
        log_echo "INFO" "The pm_node_exist=${pm_node_exist} is not Yes.not need adjust sysctl."
        return 0
    fi
    
    log_echo "INFO" "The pm_node_exist=${pm_node_exist}"
    adjust_sysctl "net.ipv4.neigh.default.gc_thresh1" "1024"
    adjust_sysctl "net.ipv4.neigh.default.gc_thresh2" "4096"
    adjust_sysctl "net.ipv4.neigh.default.gc_thresh3" "8192"
    check_ret=$(cat /etc/sysctl.conf |grep "net.ipv4.neigh.default.gc_thresh")
    log_echo "INFO" "merge_cluster_network_adjust_sysctl End.check_ret=${check_ret}"
}

function add_dv_er_path()
{
    log_echo "INFO" "add_dv_er_path start."
    local file_list="/etc/profile /etc/bashrc"
    for tmp_file in ${file_list};
    do
        if [ ! -f ${tmp_file} ];then
            log_echo "INFO" "The ${tmp_file} is not exists.not need add it."
            continue
        fi
        
        sed -i "/export DV_ER_PATH=.*/d" ${tmp_file}
        sed -i "\$a export DV_ER_PATH=${OPT_PATH}/dv/oss/SOP/etc/ssl/er" ${tmp_file}
        log_echo "INFO" "add_dv_er_path ret=$?,check_ret=$(cat ${tmp_file}|grep 'DV_ER_PATH=')"
    done
    log_echo "INFO" "add_dv_er_path end."
}

function create_register_config()
{
    log_echo "INFO" "begin to create register config info"
    if [ "X${is_upgrade}" != "XYes" ];then
        ## 安装
        if [ ! -d ${i2k_user_home}/etc/dvcfg/ ]; then
             log_echo "INFO" "create dir ${i2k_user_home}/etc/dvcfg/"
             mkdir -p ${i2k_user_home}/etc/dvcfg/
             chown ${i2k_user_name}: ${i2k_user_home}/etc/dvcfg/
             chmod 700 ${i2k_user_home}/etc/dvcfg/
        fi
        cp -f ${CURRENT_PATH}/registerConfigInfo.properties ${i2k_user_home}/etc/dvcfg/
        chown ${i2k_user_name}: ${i2k_user_home}/etc/dvcfg/registerConfigInfo.properties
        chmod 600 ${i2k_user_home}/etc/dvcfg/registerConfigInfo.properties
    else
        cmd_prefix="sudo -u ossuser "
        ${cmd_prefix} test -d ${i2k_user_home}/etc/dvcfg
        if [ $? -ne 0 ]; then
            log_echo "INFO" "create dir ${i2k_user_home}/etc/dvcfg/ by cmd prefix sudo -u ossuser"
            ${cmd_prefix} mkdir -p ${i2k_user_home}/etc/dvcfg/
            ${cmd_prefix} chmod 700 ${i2k_user_home}/etc/dvcfgg/
        fi

        ## 升级继承
        ${cmd_prefix} test -f ${install_path}/share/SOP/DVEngineeringService/etc/ssl/dv/registerConfigInfo.properties
        if [ $? -eq 0 ]; then
            if [ ! -s "${i2k_user_home}/etc/dvcfg/registerConfigInfo.properties" ];then
                log_echo "INFO" "cp registerConfigInfo file to ${i2k_user_home}/etc/dvcfg/"
                ${cmd_prefix} cp -f ${install_path}/share/SOP/DVEngineeringService/etc/ssl/dv/registerConfigInfo.properties ${i2k_user_home}/etc/dvcfg/
                if [ $? -ne 0 ]; then
                    log_echo "ERROR" "cp -f ${install_path}/share/SOP/DVEngineeringService/etc/ssl/dv/registerConfigInfo.properties to ${i2k_user_home}/etc/dvcfg/ failed, please check..."
                    exit 1
                fi
                ${cmd_prefix} chmod 600 ${i2k_user_home}/etc/dvcfg/registerConfigInfo.properties
            fi
        else
            ${cmd_prefix} test -f ${i2k_user_home}/etc/dvcfg/registerConfigInfo.properties
            if [ $? -ne 0 ]; then
                log_echo "INFO" "${i2k_user_home}/etc/dvcfg/registerConfigInfo.properties is not exists, touch it."
                ${cmd_prefix} touch ${i2k_user_home}/etc/dvcfg/registerConfigInfo.properties
                ${cmd_prefix} chmod 600 ${i2k_user_home}/etc/dvcfg/registerConfigInfo.properties
            fi
        fi
    fi
    return 0
}

if [ "X$1" == "Xmodify_hang_handler_conf" ];then
    modify_hang_handler_conf "$2"
    exit 0
fi

if [ "X$1" == "Xadd_paas_sshonly" ];then
    sshonly_group=$(cat /etc/group | grep -E "^sshonly:")
    if [ ! -z "${sshonly_group}" ];then
        usermod -a -G sshonly paas
        log_echo "INFO" "Execute cmd[usermod -a -G sshonly paas],ret=$?"
    fi
    exit 0
fi

if [ "X$1" == "Xupgrade_check" ];then
    upgrade_check || exit 1
    exit 0
elif [ "X$1" == "Xadd_prerouting_iptables_lvs" ];then
    if [ -f "/opt/oss/share/SOP/DVEngineeringService/I2000/run/etc/oms.xml" ];then
        if ( is_empty_param "${APP_FloatIP_IPV4}" );then
            APP_FloatIP_IPV4=$(grep -n '<param name="ip"' /opt/oss/share/SOP/DVEngineeringService/I2000/run/etc/oms.xml|tail -1|awk -F'>' '{print $2}'|awk -F'<' '{print $1}')
        fi
        if ( is_empty_param "${APP_FloatIP_IPV6}" );then
            APP_FloatIP_IPV6=$(grep -n '<param name="ipv6"' /opt/oss/share/SOP/DVEngineeringService/I2000/run/etc/oms.xml|tail -1|awk -F'>' '{print $2}'|awk -F'<' '{print $1}')
        fi
        add_prerouting_iptables_lvs "${med_snmp_port}" "udp"
    fi
    exit 0
elif [ "X$1" == "Xadd_host_iptables_lvs" ];then
    if [ "X$2" == "Xhost" ];then
        source ${CURRENT_PATH}/netWorkTypeOfDvConfigParameters.config
    fi
    if ( ! is_empty_param "${lvs_vip_ipv4}" );then
        add_prerouting_iptables_lvs "${med_snmp_port}" "udp"
    fi
    exit 0
elif [ "$1" == "add_dockuser_iptables_lvs" ];then
    add_dockuser_iptables_lvs
    exit 0
fi

if [ "X$1" == "Xunset_tmout" ];then
    modify_to_euleros11
    unset_tmout
    exit 0
elif [ "X$1" == "Xrollback_unset_tmout" ];then
    rollback_unset_tmout
    backExtDiskInfo
    exit 0
fi

if [ "X$1" == "XcheckNetworkConfig" ];then
    app_node_role="$2"
    netWork_type="$3"
    App_checkNetworkConfig
    [ "X${app_node_role}" == "XOMSA" -a "X${netWork_type}" == "XM" ] && config_sshd_and_sftpd
    exit 0
fi

if [ "X$1" == "Xchecktrust" ];then
    if [ "X$2" == "Xdevdata" ];then
        check_devdata_trust  $3 $4
    else
        if [ "X${is_upgrade}" == "XYes" -a "X${i2k_is_dual}" == "XYes" ];then
            check_ossuser_trust
        fi
    fi
elif [ "X$1" == "Xauto_mount" ];then
    auto_mount_disk "$2" "$3"
elif [ "X$1" == "Xconfig_host" ];then
    
    node_type=$2
    read -sp "input dv_user pwd:" pwd_list
    sysomc_pwd=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $1}')

    add_group "ossgroup" "2000"
    id  ${system_operation_user} > /dev/null 2>&1
    if [ $? -ne 0 ];then
        useradd -g ${i2k_group_id} -u ${system_operation_userid} -m ${system_operation_user} -d ${HOME_PATH}/${system_operation_user}
        if [ $? -ne 0 ];then
            log_echo "INFO" "create sysomc failed"
        fi
        set_user_passwd "sysomc" "${sysomc_pwd}"
        if [ $? -ne 0 ];then
            log_echo "INFO" "set password for sysomc failed"
        fi
        chage -M 99999 ${system_operation_user}
    fi
    config_sudo sysomc "$ROOT_CUSTOM_PATH/dv_loop_monitor/dv_loop_monitor.sh"
    systemctl status ita-ntpsmart | grep -wF "ITA NTPSmart time synchronization service" >> $LOG_FILE 2>&1
    if [ $? -eq 0 ];then
        if [ "X$2" == "XPrimaryNode" -o "X$2" == "XSecondaryNode" -o "X$2" == "XAllinONE" ];then
            config_ita_ntpsmart_service "${NTP_server_IP}"
        else
            config_ita_ntpsmart_service "${HostPrimary_IPV4},${HostSecondary_IPV4}"
        fi
    else
        config_ntp
    fi
    if [ "X$2" == "XSecondaryNode" ];then
        ntpdate -u ${HostPrimary_IPV4}
    fi
    
    systemctl start docker > /dev/null 2>&1
    systemctl enable docker > /dev/null 2>&1
    config_timer
    config_host
    # Set loop devices for host
    mkdir -p ${HOME_PATH}/dv/
    mount_loop_device "${HOME_PATH}/dv/disk_sftpossuser.img" "1024" "102400" "${HOME_PATH}/dv/sftpossuser"
    [ -d ${HOME_PATH}/dv ] && chmod 755 ${HOME_PATH}/dv
    if [ "X$2" == "XAllinONE" -o "X$2" == "XPrimaryNode" -o "X$2" == "XSecondaryNode" ];then
        mkdir -p ${OPT_PATH}/share/I2000/run/var/iemp/data/ftp
        mount_loop_device "${OPT_PATH}/share/I2000/run/var/iemp/data/disk_ftp.img" "20480" "1024000" "${OPT_PATH}/share/I2000/run/var/iemp/data/ftp" 
        if [ "X${is_install_icnfg}" = "XYes" ];then
            mount_loop_device "${HOME_PATH}/dv/disk_devdata.img" "1024" "1024000" "${HOME_PATH}/dv/devdata"
        fi
    fi 
    
    set_loop_monitor ${CURRENT_PATH}/dv_loop_monitor.sh ${CURRENT_PATH}/dv_loop_monitor.service  ${CURRENT_PATH}/dv_loop_monitor.timer

    if [ "X${node_type}" == "XPrimaryNode"  -o "X${node_type}" == "XSecondaryNode"  ] && [ "X$(echo ${is_integrated_df_and_service}|tr 'a-z' 'A-Z')" == "XNO" ];then
        custome_lvs_sysconf
    fi
    if [[ "${node_type}" =~ DVExtendCluster ]] || [[ "${node_type}" =~ SMExtendCluster ]] ;then
        custome_lvs_sysconf
    fi
    load_ip_vs
    merge_cluster_network_adjust_sysctl
    
    add_dv_er_path
    
    docker_host_add_users
    
    exit 0
    
elif [ "X$1" == "Xrestore_host" ];then
    restore_host
    exit 0
elif [ "X$1" == "Xpod_restore" ];then
    pod_restore
elif [ "X$1" == "Xconfig_ssh_sftp" ];then
    config_ssh_sftp "$2" "$3"
    [ "X$2" == "XThirdNode" ] && config_sshd_and_sftpd
    exit 0
elif [ "X$1" == "Xcopy_osfile" ];then
    copy_osfile "$2" "$3"
elif [ "X$1" == "Xpod_start_before" ];then
    pod_start_before
elif [ "X$1" == "Xadd_pod_iptables" ];then
    add_pod_iptables
elif [ "X$1" == "Xdel_pod_iptables" ];then
    del_pod_iptables
elif [ "X$1" == "Xkube_firewall_iptables" ];then
    kube_firewall_iptables "$2"
elif [ "X$1" == "Xcheck_tag_and_wait" ];then
    check_tag_and_wait "$2"
elif [ "X$1" == "Xextend_pod_preset" ];then
    extend_pod_preset
elif [ "X$1" == "Xset_first_login" ];then
    if [ ! -f "/.dockerenv" ] && [ "X${dv_force_change_os_pwd}" == "XYes" ];then
        log_echo "INFO" "Begin to initialize the passwd new passwd..."
        read -sp "input dv_user pwd:" pwd_list
        new_root_pwd=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $1}')
        new_sshusr_pwd=$(echo ${pwd_list}|awk -F'DVPreSet:Flag' '{print $2}')
    fi
    config_firstlogin
    exit 0
elif [ "X$1" == "Xinfocollect" ];then
    install_infocollect "${install_path}"
    install_health_check "${install_path}"
    collect_network_config "${source_file}" "Install"
elif [ "X$1" == "Xmodify_ssh_port" ];then
    modify_ssh_port "$2"
elif [ "X$1" == "Xcreate_register_config" ];then
    create_register_config
else
    main "$@"
fi
