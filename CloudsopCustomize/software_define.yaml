baseVersion: null
featureGroup:
    all:
        Zenithdb:
        -   deployOrder: 7
            deployTemplate: dv_cloudsop_a_zenithdb.json
            require: mandatory
            serviceName: FCSCustom
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_zenith.json
            deployOrder: 8
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_global.json
            deployOrder: 10
        -   serviceName: DVSM
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 11
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVEngineeringServicePackage
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVMiddleware
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVIpModify
        -   serviceName: DVSolution
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   serviceName: DVAlarm
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVEAM
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVNBI
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        base:
        -   deployOrder: 3
            deployTemplate: cloudsop_a_distribute_om.json
            require: mandatory
            serviceName: FCSCustom
        -   deployOrder: 6
            deployTemplate: cloudsop_amin_distribute_NoBER.json
            require: mandatory
            serviceName: HRS
        -   deployOrder: 9
            deployTemplate: cloudsop_a_alldistribute.json
            require: mandatory
            serviceName: Cron
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute.json
            require: mandatory
            serviceName: FEBS
        -   deployOrder: 10
            deployTemplate: cloudsop_multicluster_distribute.json
            require: mandatory
            serviceName: MQS
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global.json
            require: mandatory
            serviceName: Basic
        -   deployOrder: 30
            deployTemplate: ies_om_topocube_combine_all.json
            require: mandatory
            serviceName: TopoCube
        -   deployOrder: 30
            deployTemplate: ies_om_topology_combine_all.json
            require: mandatory
            serviceName: Topology
        -   deployOrder: 10
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: HiSecCAProxy
        -   deployOrder: 10
            deployTemplate: ies_om_global_securezone_common.json
            require: mandatory
            serviceName: HiSecLiteCA
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: SnbCertMgmt
        sysMonitor:
        -   deployOrder: 30
            deployTemplate: ies_om_global_eSight.json
            require: mandatory
            serviceName: SMLogLic
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVTopo
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: HomePageSettings
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: CAMP
        -   deployOrder: 30
            deployTemplate: ies_all_db_global_with_hiro.json
            require: mandatory
            serviceName: MinApiMgr
        -   deployOrder: 30
            deployTemplate: single_om_alldb_merge.json
            require: mandatory
            serviceName: DrvMgmt
        -   deployOrder: 30
            deployTemplate: single_om_merge.json
            require: mandatory
            serviceName: DrvFrm
        -   deployOrder: 30
            deployTemplate: cloudsop_om_all_combine.json
            require: mandatory
            serviceName: FM
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global_combine.json
            require: mandatory
            serviceName: ICS
        -   deployOrder: 30
            deployTemplate: UAinfo_UNaviWebsite.json
            require: mandatory
            serviceName: UAinfo
        -   serviceName: DTEBase
            require: optional
            deployTemplate: dte_om_global_dtebase.json
            deployOrder: 30
        -   serviceName: DTENEManager
            require: optional
            deployTemplate: dte_om_global_nemanager.json
            deployOrder: 30
        -   deployOrder: 30
            deployTemplate: ies_NBI_SingleEMS.json
            require: mandatory
            serviceName: NBIService
        -   serviceName: DVMenuUtm
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdFileAgent.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 9
            deployTemplate: HOFS_HOFSOsdService.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSTrackerService.json
            require: mandatory
            serviceName: HOFS
        DVPM:
        -   serviceName: DVPerformance
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        DVCertService:
        -   deployOrder: 10
            deployTemplate: dv_om_global_cert.json
            require: mandatory
            serviceName: DVCommon
        cluster:
        -   deployOrder: 3
            deployTemplate: ommha_om.json
            require: mandatory
            serviceName: OMMHA
        DVMaintenance:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVMaintenance
        DV4AService:
        -   deployOrder: 30
            deployTemplate: ies_om_4a.json
            require: optional
            serviceName: DVMaintenance
        OM_LogtraceService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogtrace
        OM_CallchainService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCallchain
        OM_CatService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCat
        OM_LogmatrixService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogmatrix
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVLogmatrixStreaming
        SA_WorkbenchService:
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVZookeeper
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNats
        -   deployOrder: 9
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVFoundation
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVElasticsearch
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVOMA
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVController
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbench
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbeans
        SA_IhealingService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVIhealing
        SA_InfocollectService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVInfocollect
        PMDashBoard:
        -   serviceName: DVDashBoard
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        DVSaaSOps:
        -   serviceName: DVSaaSOps
            require: optional
            deployTemplate: ies_om_global.json
            deployOrder: 30
        AnalysisEngine:
        -   serviceName: DVAnalysisEngine
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        SituationAwareness:
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_vertical.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_aiengine.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: EMSSecurityManager
        SOAR:
        -   deployOrder: 30    
            deployTemplate: ies_om_global_dv.json                                                                       
            require: optional
            serviceName: EMSSOARService    
        DVITManagement:
        -   deployOrder: 30
            deployTemplate: CloudSOP_Itm_Template.json
            require: mandatory
            serviceName: ITM
        -   serviceName: ItmCPPRuntimeRtsp
            require: mandatory
            deployTemplate: CloudSOP_Itm_Template_eSight.json
            deployOrder: 30
        BasicSecurityOM:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNESec
        -   deployOrder: 8
            deployTemplate: ssa_situation_4k_zenithdb.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 8
            deployTemplate: ssa_detect_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 8
            deployTemplate: ssa_management_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityManager
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: EMSSecurityAgent
        -   deployOrder: 30
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: ies_om_global.json
        -   deployOrder: 8
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: unified_asset_zenithdb.json
        -   deployOrder: 8
            serviceName: EMSConfigurationAssessment 
            require: mandatory
            deployTemplate: ssa_configurationAssessment_4k_zenithdb.json
        -   deployOrder: 8
            deployTemplate: soarservice_om_global_zenithdb_dv.json
            require: optional
            serviceName: EMSSOARService
        HostSecurityConfigurationCheck:
        -   deployOrder: 30
            serviceName: EMSConfigurationAssessment 
            require: mandatory
            deployTemplate: ies_om_global.json
        DVMediation:
        -   serviceName: DVMediation
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        DVPM1:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_1.json
            deployOrder: 8
        DVPM2:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_2.json
            deployOrder: 8
        DVPM3:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_3.json
            deployOrder: 8
        DVPM4:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_4.json
            deployOrder: 8
        DVPM5:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_5.json
            deployOrder: 8
        DVPM6:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_6.json
            deployOrder: 8
    single:
        Zenithdb:
        -   deployOrder: 7
            deployTemplate: dv_cloudsop_a_zenithdb.json
            require: mandatory
            serviceName: FCSCustom
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_zenith.json
            deployOrder: 8
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_global.json
            deployOrder: 10
        -   serviceName: DVSM
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 11
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVEngineeringServicePackage
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVMiddleware
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVIpModify
        -   serviceName: DVSolution
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   serviceName: DVAlarm
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVEAM
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVNBI
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        base:
        -   deployOrder: 3
            deployTemplate: cloudsop_a_distribute_om.json
            require: mandatory
            serviceName: FCSCustom
        -   deployOrder: 6
            deployTemplate: cloudsop_amin_distribute_NoBER.json
            require: mandatory
            serviceName: HRS
        -   deployOrder: 9
            deployTemplate: cloudsop_a_alldistribute.json
            require: mandatory
            serviceName: Cron
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute.json
            require: mandatory
            serviceName: FEBS
        -   deployOrder: 10
            deployTemplate: cloudsop_multicluster_distribute.json
            require: mandatory
            serviceName: MQS
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global.json
            require: mandatory
            serviceName: Basic
        -   deployOrder: 30
            deployTemplate: ies_om_topocube_combine_all.json
            require: mandatory
            serviceName: TopoCube
        -   deployOrder: 30
            deployTemplate: ies_om_topology_combine_all.json
            require: mandatory
            serviceName: Topology
        -   deployOrder: 10
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: HiSecCAProxy
        -   deployOrder: 10
            deployTemplate: ies_om_global_securezone_common.json
            require: mandatory
            serviceName: HiSecLiteCA
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: SnbCertMgmt
        sysMonitor:
        -   deployOrder: 30
            deployTemplate: ies_om_global_eSight.json
            require: mandatory
            serviceName: SMLogLic
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVTopo
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: HomePageSettings
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: CAMP
        -   deployOrder: 30
            deployTemplate: ies_all_db_global_with_hiro.json
            require: mandatory
            serviceName: MinApiMgr
        -   deployOrder: 30
            deployTemplate: single_om_alldb_merge.json
            require: mandatory
            serviceName: DrvMgmt
        -   deployOrder: 30
            deployTemplate: single_om_merge.json
            require: mandatory
            serviceName: DrvFrm
        -   deployOrder: 30
            deployTemplate: cloudsop_om_all_combine.json
            require: mandatory
            serviceName: FM
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global_combine.json
            require: mandatory
            serviceName: ICS
        -   deployOrder: 30
            deployTemplate: UAinfo_UNaviWebsite.json
            require: mandatory
            serviceName: UAinfo
        -   serviceName: DTEBase
            require: optional
            deployTemplate: dte_om_global_dtebase.json
            deployOrder: 30
        -   serviceName: DTENEManager
            require: optional
            deployTemplate: dte_om_global_nemanager.json
            deployOrder: 30
        -   deployOrder: 30
            deployTemplate: ies_NBI_SingleEMS.json
            require: mandatory
            serviceName: NBIService
        -   serviceName: DVMenuUtm
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdFileAgent.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 9
            deployTemplate: HOFS_HOFSOsdService.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSTrackerService.json
            require: mandatory
            serviceName: HOFS
        DVPM:
        -   serviceName: DVPerformance
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        DVCertService:
        -   deployOrder: 10
            deployTemplate: dv_om_global_cert.json
            require: mandatory
            serviceName: DVCommon
        DVITManagement:
        -   deployOrder: 30
            deployTemplate: CloudSOP_Itm_Template.json
            require: mandatory
            serviceName: ITM
        -   serviceName: ItmCPPRuntimeRtsp
            require: mandatory
            deployTemplate: CloudSOP_Itm_Template_eSight.json
            deployOrder: 30
        DVMaintenance:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVMaintenance
        DV4AService:
        -   deployOrder: 30
            deployTemplate: ies_om_4a.json
            require: optional
            serviceName: DVMaintenance
        OM_LogtraceService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogtrace
        OM_CallchainService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCallchain
        OM_CatService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCat
        OM_LogmatrixService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogmatrix
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVLogmatrixStreaming
        SA_WorkbenchService:
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVZookeeper
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNats
        -   deployOrder: 9
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVFoundation
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVElasticsearch
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVOMA
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVController
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbench
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbeans
        SA_IhealingService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVIhealing
        SA_InfocollectService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVInfocollect
        PMDashBoard:
        -   serviceName: DVDashBoard
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        DVSaaSOps:
        -   serviceName: DVSaaSOps
            require: optional
            deployTemplate: ies_om_global.json
            deployOrder: 30
        AnalysisEngine:       
        -   serviceName: DVAnalysisEngine
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        SituationAwareness:
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_vertical.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_aiengine.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: EMSSecurityManager
        SOAR:
        -   deployOrder: 30    
            deployTemplate: ies_om_global_dv.json                                                                       
            require: optional
            serviceName: EMSSOARService
        BasicSecurityOM:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNESec
        -   deployOrder: 8
            deployTemplate: ssa_situation_4k_zenithdb.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 8
            deployTemplate: ssa_detect_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 8
            deployTemplate: ssa_management_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityManager
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: EMSSecurityAgent
        -   deployOrder: 30
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: ies_om_global.json
        -   deployOrder: 8
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: unified_asset_zenithdb.json
        -   deployOrder: 8
            serviceName: EMSConfigurationAssessment 
            require: mandatory
            deployTemplate: ssa_configurationAssessment_4k_zenithdb.json
        -   deployOrder: 8
            deployTemplate: soarservice_om_global_zenithdb_dv.json
            require: optional
            serviceName: EMSSOARService
        HostSecurityConfigurationCheck:
        -   deployOrder: 30
            serviceName: EMSConfigurationAssessment 
            require: mandatory
            deployTemplate: ies_om_global.json
    merge:
        Zenithdb:
        -   deployOrder: 3
            deployTemplate: dv_cloudsop_a_zenithdb.json
            require: mandatory
            serviceName: FCSCustom
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_zenith.json
            deployOrder: 8
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_global.json
            deployOrder: 10
        -   serviceName: DVSM
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 11
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVEngineeringServicePackage
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVMiddleware
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVIpModify
        -   serviceName: DVSolution
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   serviceName: DVAlarm
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVEAM
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVNBI
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        base_merge:
        -   deployOrder: 2
            deployTemplate: dv_cloudsop_a_zk.json
            require: mandatory
            serviceName: FCSCustom
        -   deployOrder: 6
            deployTemplate: cloudsop_b_distribute_heshe_esight.json
            require: mandatory
            serviceName: HRS
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute_zenith.json
            require: mandatory
            serviceName: Cron
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute.json
            require: mandatory
            serviceName: FEBS
        -   deployOrder: 10
            deployTemplate: cloudsop_multicluster_distribute.json
            require: mandatory
            serviceName: MQS
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global.json
            require: mandatory
            serviceName: Basic
        -   deployOrder: 30
            deployTemplate: ies_om_topocube_combine_all.json
            require: mandatory
            serviceName: TopoCube
        -   deployOrder: 30
            deployTemplate: ies_om_topology_combine_all.json
            require: mandatory
            serviceName: Topology
        -   deployOrder: 10
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: HiSecCAProxy
        -   deployOrder: 10
            deployTemplate: ies_om_global_securezone_common.json
            require: mandatory
            serviceName: HiSecLiteCA
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: SnbCertMgmt
        sysMonitor:
        -   deployOrder: 30
            deployTemplate: ies_om_global_eSight.json
            require: mandatory
            serviceName: SMLogLic
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVTopo
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: HomePageSettings
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: CAMP
        -   deployOrder: 30
            deployTemplate: ies_all_db_global_with_hiro.json
            require: mandatory
            serviceName: MinApiMgr
        -   deployOrder: 30
            deployTemplate: single_om_alldb_merge.json
            require: mandatory
            serviceName: DrvMgmt
        -   deployOrder: 30
            deployTemplate: single_om_merge.json
            require: mandatory
            serviceName: DrvFrm
        -   deployOrder: 30
            deployTemplate: cloudsop_om_all_combine.json
            require: mandatory
            serviceName: FM
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global_combine.json
            require: mandatory
            serviceName: ICS
        -   deployOrder: 30
            deployTemplate: UAinfo_UNaviWebsite.json
            require: mandatory
            serviceName: UAinfo
        -   serviceName: DTEBase
            require: optional
            deployTemplate: dte_om_global_dtebase.json
            deployOrder: 30
        -   serviceName: DTENEManager
            require: optional
            deployTemplate: dte_om_global_nemanager.json
            deployOrder: 30
        -   deployOrder: 30
            deployTemplate: ies_NBI_SingleEMS.json
            require: mandatory
            serviceName: NBIService
        -   serviceName: DVMenuUtm
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdFileAgent.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdService.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSTrackerService.json
            require: mandatory
            serviceName: HOFS
        DVPM:
        -   serviceName: DVPerformance
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        DVCertService:
        -   deployOrder: 10
            deployTemplate: dv_om_global_cert.json
            require: mandatory
            serviceName: DVCommon
        DVITManagement:
        -   deployOrder: 30
            deployTemplate: CloudSOP_Itm_Template.json
            require: mandatory
            serviceName: ITM
        -   serviceName: ItmCPPRuntimeRtsp
            require: mandatory
            deployTemplate: CloudSOP_Itm_Template_eSight.json
            deployOrder: 30
        DVMaintenance:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVMaintenance
        DV4AService:
        -   deployOrder: 30
            deployTemplate: ies_om_4a.json
            require: optional
            serviceName: DVMaintenance
        OM_LogtraceService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogtrace
        OM_CallchainService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCallchain
        OM_CatService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCat
        OM_LogmatrixService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogmatrix
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVLogmatrixStreaming
        SA_WorkbenchService:
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVZookeeper
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNats
        -   deployOrder: 9
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVFoundation
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVElasticsearch
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVOMA
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVController
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbench
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbeans
        SA_IhealingService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVIhealing
        SA_InfocollectService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVInfocollect
        PMDashBoard:
        -   serviceName: DVDashBoard
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        DVSaaSOps:
        -   serviceName: DVSaaSOps
            require: optional
            deployTemplate: ies_om_global.json
            deployOrder: 30      
        AnalysisEngine:      
        -   serviceName: DVAnalysisEngine
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        SituationAwareness:
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_vertical.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_aiengine.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: EMSSecurityManager
        SOAR:
        -   deployOrder: 30    
            deployTemplate: ies_om_global_dv.json                                                                       
            require: optional
            serviceName: EMSSOARService
        BasicSecurityOM:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNESec
        -   deployOrder: 8
            deployTemplate: ssa_situation_4k_zenithdb.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 8
            deployTemplate: ssa_detect_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 8
            deployTemplate: ssa_management_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityManager
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: EMSSecurityAgent
        -   deployOrder: 30
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: ies_om_global.json
        -   deployOrder: 8
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: unified_asset_zenithdb.json
        -   deployOrder: 8
            serviceName: EMSConfigurationAssessment 
            require: mandatory
            deployTemplate: ssa_configurationAssessment_4k_zenithdb.json
        -   deployOrder: 8
            deployTemplate: soarservice_om_global_zenithdb_dv.json
            require: optional
            serviceName: EMSSOARService
        HostSecurityConfigurationCheck:
        -   deployOrder: 30
            serviceName: EMSConfigurationAssessment 
            require: mandatory
            deployTemplate: ies_om_global.json
    merge_lite:
        Zenithdb:
        -   deployOrder: 3
            deployTemplate: dv_cloudsop_a_zenithdb.json
            require: mandatory
            serviceName: FCSCustom
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_zenith.json
            deployOrder: 8
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_global.json
            deployOrder: 10
        -   serviceName: DVSM
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 11
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVEngineeringServicePackage
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVMiddleware
        -   serviceName: DVSolution
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   serviceName: DVAlarm
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVEAM
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        base_merge:
        -   deployOrder: 2
            deployTemplate: dv_cloudsop_a_zk.json
            require: mandatory
            serviceName: FCSCustom
        -   deployOrder: 6
            deployTemplate: cloudsop_b_distribute_heshe_esight.json
            require: mandatory
            serviceName: HRS
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute_zenith.json
            require: mandatory
            serviceName: Cron
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute.json
            require: mandatory
            serviceName: FEBS
        -   deployOrder: 10
            deployTemplate: cloudsop_multicluster_distribute.json
            require: mandatory
            serviceName: MQS
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global.json
            require: mandatory
            serviceName: Basic
        -   deployOrder: 30
            deployTemplate: ies_om_topocube_combine_all.json
            require: mandatory
            serviceName: TopoCube
        -   deployOrder: 10
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: HiSecCAProxy
        -   deployOrder: 10
            deployTemplate: ies_om_global_securezone_common.json
            require: mandatory
            serviceName: HiSecLiteCA
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: SnbCertMgmt
        sysMonitor:
        -   deployOrder: 30
            deployTemplate: ies_om_global_eSight.json
            require: mandatory
            serviceName: SMLogLic
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: HomePageSettings
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: CAMP
        -   deployOrder: 30
            deployTemplate: ies_all_db_global_with_hiro.json
            require: mandatory
            serviceName: MinApiMgr
        -   deployOrder: 30
            deployTemplate: single_om_alldb_merge.json
            require: mandatory
            serviceName: DrvMgmt
        -   deployOrder: 30
            deployTemplate: single_om_merge.json
            require: mandatory
            serviceName: DrvFrm
        -   deployOrder: 30
            deployTemplate: cloudsop_om_all_combine.json
            require: mandatory
            serviceName: FM
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global_combine.json
            require: mandatory
            serviceName: ICS
        -   deployOrder: 30
            deployTemplate: UAinfo_UNaviWebsite.json
            require: mandatory
            serviceName: UAinfo
        -   serviceName: DTEBase
            require: optional
            deployTemplate: dte_om_global_dtebase.json
            deployOrder: 30
        -   serviceName: DTENEManager
            require: optional
            deployTemplate: dte_om_global_nemanager.json
            deployOrder: 30
        -   serviceName: DVMenuUtm
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdFileAgent.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdService.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSTrackerService.json
            require: mandatory
            serviceName: HOFS
        DVCertService:
        -   deployOrder: 10
            deployTemplate: dv_om_global_cert.json
            require: mandatory
            serviceName: DVCommon
        NBIService:
        -   deployOrder: 30
            deployTemplate: ies_NBI_SingleEMS.json
            require: mandatory
            serviceName: NBIService
    merge_mini:
        Zenithdb:
        -   deployOrder: 3
            deployTemplate: dv_cloudsop_a_zenithdb.json
            require: mandatory
            serviceName: FCSCustom
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_zenith.json
            deployOrder: 8
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_global.json
            deployOrder: 10
        -   serviceName: DVSM
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 11
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVEngineeringServicePackage
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVIpModify
        -   serviceName: DVSolution
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   serviceName: DVAlarm
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVEAM
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        base_merge:
        -   deployOrder: 2
            deployTemplate: dv_cloudsop_a_zk.json
            require: mandatory
            serviceName: FCSCustom
        -   deployOrder: 6
            deployTemplate: cloudsop_b_distribute_heshe_esight.json
            require: mandatory
            serviceName: HRS
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute_zenith.json
            require: mandatory
            serviceName: Cron
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute.json
            require: mandatory
            serviceName: FEBS
        -   deployOrder: 10
            deployTemplate: cloudsop_multicluster_distribute.json
            require: mandatory
            serviceName: MQS
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global.json
            require: mandatory
            serviceName: Basic
        -   deployOrder: 30
            deployTemplate: ies_om_topocube_combine_all.json
            require: mandatory
            serviceName: TopoCube
        -   deployOrder: 10
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: HiSecCAProxy
        -   deployOrder: 10
            deployTemplate: ies_om_global_securezone_common.json
            require: mandatory
            serviceName: HiSecLiteCA
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: SnbCertMgmt
        sysMonitor:
        -   deployOrder: 30
            deployTemplate: ies_om_global_eSight.json
            require: mandatory
            serviceName: SMLogLic
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: HomePageSettings
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: CAMP
        -   deployOrder: 30
            deployTemplate: ies_all_db_global_with_hiro.json
            require: mandatory
            serviceName: MinApiMgr
        -   deployOrder: 30
            deployTemplate: single_om_alldb_merge.json
            require: mandatory
            serviceName: DrvMgmt
        -   deployOrder: 30
            deployTemplate: single_om_merge.json
            require: mandatory
            serviceName: DrvFrm
        -   deployOrder: 30
            deployTemplate: cloudsop_om_all_combine.json
            require: mandatory
            serviceName: FM
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global_combine.json
            require: mandatory
            serviceName: ICS
        -   deployOrder: 30
            deployTemplate: UAinfo_UNaviWebsite.json
            require: mandatory
            serviceName: UAinfo
        -   serviceName: DTEBase
            require: optional
            deployTemplate: dte_om_global_dtebase.json
            deployOrder: 30
        -   serviceName: DTENEManager
            require: optional
            deployTemplate: dte_om_global_nemanager.json
            deployOrder: 30
        -   deployOrder: 30
            deployTemplate: ies_NBI_SingleEMS.json
            require: mandatory
            serviceName: NBIService
        -   serviceName: DVMenuUtm
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdFileAgent.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdService.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSTrackerService.json
            require: mandatory
            serviceName: HOFS
        DVPM:
        -   serviceName: DVPerformance
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        DVCertService:
            - deployOrder: 30
              deployTemplate: dv_om_global_cert.json
              require: mandatory
              serviceName: DVCommon
        DVMiddleware:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVMiddleware
        PMDashBoard:
        -   serviceName: DVDashBoard
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        SA_WorkbenchService:
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVZookeeper
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNats
        -   deployOrder: 9
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVFoundation
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVElasticsearch
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVOMA
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVController
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbench
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbeans
        SA_IhealingService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVIhealing
    MiniCluster:
        Zenithdb:
        -   deployOrder: 3
            deployTemplate: dv_cloudsop_a_zenithdb.json
            require: mandatory
            serviceName: FCSCustom
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_zenith.json
            deployOrder: 8
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_global.json
            deployOrder: 10
        -   serviceName: DVSM
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 11
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVEngineeringServicePackage
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVMiddleware
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVIpModify
        -   serviceName: DVSolution
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   serviceName: DVAlarm
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVEAM
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVNBI
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        base_merge:
        -   deployOrder: 2
            deployTemplate: dv_cloudsop_a_zk.json
            require: mandatory
            serviceName: FCSCustom
        -   deployOrder: 6
            deployTemplate: cloudsop_b_distribute_heshe_esight.json
            require: mandatory
            serviceName: HRS
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute_zenith.json
            require: mandatory
            serviceName: Cron
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute.json
            require: mandatory
            serviceName: FEBS
        -   deployOrder: 10
            deployTemplate: cloudsop_multicluster_distribute.json
            require: mandatory
            serviceName: MQS
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global.json
            require: mandatory
            serviceName: Basic
        -   deployOrder: 30
            deployTemplate: ies_om_topocube_combine_all.json
            require: mandatory
            serviceName: TopoCube
        -   deployOrder: 30
            deployTemplate: ies_om_topology_combine_all.json
            require: mandatory
            serviceName: Topology
        -   deployOrder: 10
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: HiSecCAProxy
        -   deployOrder: 10
            deployTemplate: ies_om_global_securezone_common.json
            require: mandatory
            serviceName: HiSecLiteCA
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: SnbCertMgmt
        sysMonitor:
        -   deployOrder: 30
            deployTemplate: ies_om_global_eSight.json
            require: mandatory
            serviceName: SMLogLic
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVTopo
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: HomePageSettings
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: CAMP
        -   deployOrder: 30
            deployTemplate: ies_all_db_global_with_hiro.json
            require: mandatory
            serviceName: MinApiMgr
        -   deployOrder: 30
            deployTemplate: single_om_alldb_merge.json
            require: mandatory
            serviceName: DrvMgmt
        -   deployOrder: 30
            deployTemplate: single_om_merge.json
            require: mandatory
            serviceName: DrvFrm
        -   deployOrder: 30
            deployTemplate: cloudsop_om_all_combine.json
            require: mandatory
            serviceName: FM
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global_combine.json
            require: mandatory
            serviceName: ICS
        -   deployOrder: 30
            deployTemplate: UAinfo_UNaviWebsite.json
            require: mandatory
            serviceName: UAinfo
        -   serviceName: DTEBase
            require: optional
            deployTemplate: dte_om_global_dtebase.json
            deployOrder: 30
        -   serviceName: DTENEManager
            require: optional
            deployTemplate: dte_om_global_nemanager.json
            deployOrder: 30
        -   deployOrder: 30
            deployTemplate: ies_NBI_SingleEMS.json
            require: mandatory
            serviceName: NBIService
        -   serviceName: DVMenuUtm
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdFileAgent.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdService.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSTrackerService.json
            require: mandatory
            serviceName: HOFS
        DVPM:
        -   serviceName: DVPerformance
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        DVCertService:
        -   deployOrder: 10
            deployTemplate: dv_om_global_cert.json
            require: mandatory
            serviceName: DVCommon
        DVITManagement:
        -   deployOrder: 30
            deployTemplate: CloudSOP_Itm_Template.json
            require: mandatory
            serviceName: ITM
        -   serviceName: ItmCPPRuntimeRtsp
            require: mandatory
            deployTemplate: CloudSOP_Itm_Template_eSight.json
            deployOrder: 30
        DVMaintenance:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVMaintenance
        DV4AService:
        -   deployOrder: 30
            deployTemplate: ies_om_4a.json
            require: optional
            serviceName: DVMaintenance
        OM_LogtraceService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogtrace
        OM_CallchainService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCallchain
        OM_CatService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCat
        OM_LogmatrixService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogmatrix
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVLogmatrixStreaming
        SA_WorkbenchService:
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVZookeeper
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNats
        -   deployOrder: 9
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVFoundation
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVElasticsearch
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVOMA
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVController
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbench
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbeans
        SA_IhealingService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVIhealing
        SA_InfocollectService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVInfocollect
        PMDashBoard:
        -   serviceName: DVDashBoard
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        DVSaaSOps:
        -   serviceName: DVSaaSOps
            require: optional
            deployTemplate: ies_om_global.json
            deployOrder: 30
        AnalysisEngine:       
        -   serviceName: DVAnalysisEngine
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        DVMediation:
        -   serviceName: DVMediation
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        SituationAwareness:
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_vertical.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_aiengine.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: EMSSecurityManager
        SOAR:
        -   deployOrder: 30    
            deployTemplate: ies_om_global_dv.json                                                                      
            require: optional
            serviceName: EMSSOARService
        DVPM1:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_1.json
            deployOrder: 9
        DVPM2:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_2.json
            deployOrder: 10
        DVPM3:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_3.json
            deployOrder: 11
        DVPM4:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_4.json
            deployOrder: 12
        DVPM5:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_5.json
            deployOrder: 13
        DVPM6:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_6.json
            deployOrder: 14
        BasicSecurityOM:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNESec
        -   deployOrder: 15
            deployTemplate: ssa_situation_4k_zenithdb.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 15
            deployTemplate: ssa_detect_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 15
            deployTemplate: ssa_management_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityManager
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: EMSSecurityAgent
        -   deployOrder: 30
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: ies_om_global.json
        -   deployOrder: 15
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: unified_asset_zenithdb.json
        -   deployOrder: 15
            serviceName: EMSConfigurationAssessment 
            require: mandatory
            deployTemplate: ssa_configurationAssessment_4k_zenithdb.json
        -   deployOrder: 15
            deployTemplate: soarservice_om_global_zenithdb_dv.json
            require: optional
            serviceName: EMSSOARService
        HostSecurityConfigurationCheck:
        -   deployOrder: 30
            serviceName: EMSConfigurationAssessment 
            require: mandatory
            deployTemplate: ies_om_global.json
    MiniCluster_lite:
        Zenithdb:
        -   deployOrder: 3
            deployTemplate: dv_cloudsop_a_zenithdb.json
            require: mandatory
            serviceName: FCSCustom
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_zenith.json
            deployOrder: 8
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_global.json
            deployOrder: 10
        -   serviceName: DVSM
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 11
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVEngineeringServicePackage
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVIpModify
        -   serviceName: DVSolution
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   serviceName: DVAlarm
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVEAM
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        base_merge:
        -   deployOrder: 2
            deployTemplate: dv_cloudsop_a_zk.json
            require: mandatory
            serviceName: FCSCustom
        -   deployOrder: 6
            deployTemplate: cloudsop_b_distribute_heshe_esight.json
            require: mandatory
            serviceName: HRS
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute_zenith.json
            require: mandatory
            serviceName: Cron
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute.json
            require: mandatory
            serviceName: FEBS
        -   deployOrder: 10
            deployTemplate: cloudsop_multicluster_distribute.json
            require: mandatory
            serviceName: MQS
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global.json
            require: mandatory
            serviceName: Basic
        -   deployOrder: 30
            deployTemplate: ies_om_topocube_combine_all.json
            require: mandatory
            serviceName: TopoCube
        -   deployOrder: 10
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: HiSecCAProxy
        -   deployOrder: 10
            deployTemplate: ies_om_global_securezone_common.json
            require: mandatory
            serviceName: HiSecLiteCA
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: SnbCertMgmt
        sysMonitor:
        -   deployOrder: 30
            deployTemplate: ies_om_global_eSight.json
            require: mandatory
            serviceName: SMLogLic
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: HomePageSettings
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: CAMP
        -   deployOrder: 30
            deployTemplate: ies_all_db_global_with_hiro.json
            require: mandatory
            serviceName: MinApiMgr
        -   deployOrder: 30
            deployTemplate: single_om_alldb_merge.json
            require: mandatory
            serviceName: DrvMgmt
        -   deployOrder: 30
            deployTemplate: single_om_merge.json
            require: mandatory
            serviceName: DrvFrm
        -   deployOrder: 30
            deployTemplate: cloudsop_om_all_combine.json
            require: mandatory
            serviceName: FM
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global_combine.json
            require: mandatory
            serviceName: ICS
        -   deployOrder: 30
            deployTemplate: UAinfo_UNaviWebsite.json
            require: mandatory
            serviceName: UAinfo
        -   serviceName: DTEBase
            require: optional
            deployTemplate: dte_om_global_dtebase.json
            deployOrder: 30
        -   serviceName: DTENEManager
            require: optional
            deployTemplate: dte_om_global_nemanager.json
            deployOrder: 30
        -   deployOrder: 30
            deployTemplate: ies_NBI_SingleEMS.json
            require: mandatory
            serviceName: NBIService
        -   serviceName: DVMenuUtm
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdFileAgent.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdService.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSTrackerService.json
            require: mandatory
            serviceName: HOFS
        DVPM:
        -   serviceName: DVPerformance
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        DVCertService:
            - deployOrder: 30
              deployTemplate: dv_om_global_cert.json
              require: mandatory
              serviceName: DVCommon
        DVMiddleware:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVMiddleware
        PMDashBoard:
        -   serviceName: DVDashBoard
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        SA_WorkbenchService:
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVZookeeper
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNats
        -   deployOrder: 9
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVFoundation
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVElasticsearch
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVOMA
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVController
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbench
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbeans
        SA_IhealingService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVIhealing
    largeCapacity:
        Zenithdb:
        -   deployOrder: 7
            deployTemplate: dv_cloudsop_a_zenithdb.json
            require: mandatory
            serviceName: FCSCustom
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_zenith.json
            deployOrder: 8
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_global.json
            deployOrder: 10
        -   serviceName: DVSM
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 11
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVEngineeringServicePackage
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVMiddleware
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVIpModify
        -   serviceName: DVSolution
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   serviceName: DVAlarm
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVEAM
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVNBI
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        base:
        -   deployOrder: 3
            deployTemplate: cloudsop_a_distribute_om_largeCapacity.json
            require: mandatory
            serviceName: FCSCustom
        -   deployOrder: 6
            deployTemplate: cloudsop_amin_distribute_NoBER.json
            require: mandatory
            serviceName: HRS
        -   deployOrder: 9
            deployTemplate: cloudsop_a_alldistribute.json
            require: mandatory
            serviceName: Cron
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute.json
            require: mandatory
            serviceName: FEBS
        -   deployOrder: 10
            deployTemplate: cloudsop_multicluster_distribute.json
            require: mandatory
            serviceName: MQS
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global.json
            require: mandatory
            serviceName: Basic
        -   deployOrder: 30
            deployTemplate: ies_om_topocube_combine_all.json
            require: mandatory
            serviceName: TopoCube
        -   deployOrder: 30
            deployTemplate: ies_om_topology_combine_all.json
            require: mandatory
            serviceName: Topology
        -   deployOrder: 10
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: HiSecCAProxy
        -   deployOrder: 10
            deployTemplate: ies_om_global_securezone_common.json
            require: mandatory
            serviceName: HiSecLiteCA
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: SnbCertMgmt
        sysMonitor:
        -   deployOrder: 30
            deployTemplate: ies_om_global_eSight.json
            require: mandatory
            serviceName: SMLogLic
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVTopo
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: HomePageSettings
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: CAMP
        -   deployOrder: 30
            deployTemplate: ies_all_db_global_with_hiro.json
            require: mandatory
            serviceName: MinApiMgr
        -   deployOrder: 30
            deployTemplate: single_om_alldb_merge.json
            require: mandatory
            serviceName: DrvMgmt
        -   deployOrder: 30
            deployTemplate: single_om_merge.json
            require: mandatory
            serviceName: DrvFrm
        -   deployOrder: 30
            deployTemplate: cloudsop_om_all_combine.json
            require: mandatory
            serviceName: FM
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global_combine.json
            require: mandatory
            serviceName: ICS
        -   deployOrder: 30
            deployTemplate: UAinfo_UNaviWebsite.json
            require: mandatory
            serviceName: UAinfo
        -   serviceName: DTEBase
            require: optional
            deployTemplate: dte_om_global_dtebase.json
            deployOrder: 30
        -   serviceName: DTENEManager
            require: optional
            deployTemplate: dte_om_global_nemanager.json
            deployOrder: 30
        -   deployOrder: 30
            deployTemplate: ies_NBI_SingleEMS.json
            require: mandatory
            serviceName: NBIService
        -   serviceName: DVMenuUtm
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdFileAgent.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 9
            deployTemplate: HOFS_HOFSOsdService.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSTrackerService.json
            require: mandatory
            serviceName: HOFS
        DVPM:
        -   serviceName: DVPerformance
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        DVCertService:
        -   deployOrder: 10
            deployTemplate: dv_om_global_cert.json
            require: mandatory
            serviceName: DVCommon
        DVITManagement:
        -   deployOrder: 30
            deployTemplate: CloudSOP_Itm_Template.json
            require: mandatory
            serviceName: ITM
        -   serviceName: ItmCPPRuntimeRtsp
            require: mandatory
            deployTemplate: CloudSOP_Itm_Template_eSight.json
            deployOrder: 30
        cluster:
        -   deployOrder: 3
            deployTemplate: ommha_om.json
            require: mandatory
            serviceName: OMMHA
        DVMaintenance:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVMaintenance
        DV4AService:
        -   deployOrder: 30
            deployTemplate: ies_om_4a.json
            require: optional
            serviceName: DVMaintenance
        OM_LogtraceService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogtrace
        OM_CallchainService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCallchain
        OM_CatService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCat
        OM_LogmatrixService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogmatrix
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVLogmatrixStreaming
        SA_WorkbenchService:
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVZookeeper
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNats
        -   deployOrder: 9
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVFoundation
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVElasticsearch
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVOMA
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVController
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbench
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbeans
        SA_IhealingService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVIhealing
        SA_InfocollectService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVInfocollect
        PMDashBoard:
        -   serviceName: DVDashBoard
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        DVSaaSOps:
        -   serviceName: DVSaaSOps
            require: optional
            deployTemplate: ies_om_global.json
            deployOrder: 30
        AnalysisEngine:        
        -   serviceName: DVAnalysisEngine
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        DVMediation:
        -   serviceName: DVMediation
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        SituationAwareness:
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_vertical.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_aiengine.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: EMSSecurityManager
        SOAR:
        -   deployOrder: 30    
            deployTemplate: ies_om_global_dv.json                                                                       
            require: optional
            serviceName: EMSSOARService
        DVPM1:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_1.json
            deployOrder: 8
        DVPM2:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_2.json
            deployOrder: 8
        DVPM3:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_3.json
            deployOrder: 8
        DVPM4:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_4.json
            deployOrder: 8
        DVPM5:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_5.json
            deployOrder: 8
        DVPM6:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_6.json
            deployOrder: 8
        BasicSecurityOM:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNESec
        -   deployOrder: 8
            deployTemplate: ssa_situation_4k_zenithdb.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 8
            deployTemplate: ssa_detect_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 8
            deployTemplate: ssa_management_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityManager
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: EMSSecurityAgent
        -   deployOrder: 30
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: ies_om_global.json
        -   deployOrder: 8
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: unified_asset_zenithdb.json
        -   deployOrder: 8
            serviceName: EMSConfigurationAssessment 
            require: mandatory
            deployTemplate: ssa_configurationAssessment_4k_zenithdb.json
        -   deployOrder: 8
            deployTemplate: soarservice_om_global_zenithdb_dv.json
            require: optional
            serviceName: EMSSOARService
        HostSecurityConfigurationCheck:
        -   deployOrder: 30
            serviceName: EMSConfigurationAssessment 
            require: mandatory
            deployTemplate: ies_om_global.json
    single_tool:
        Zenithdb:
        -   deployOrder: 8
            deployTemplate: dv_cloudsop_a_zenithdb.json
            require: mandatory
            serviceName: FCSCustom
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_zenith.json
            deployOrder: 8
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_global_tool.json
            deployOrder: 10
        -   serviceName: DVSM
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 11
        -   deployOrder: 30
            deployTemplate: ies_om_global_tool.json
            require: mandatory
            serviceName: DVEngineeringServicePackage
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVMiddleware
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVIpModify
        -   serviceName: DVSolution
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   serviceName: DVEAM
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        base:
        -   deployOrder: 3
            deployTemplate: cloudsop_a_distribute_om.json
            require: mandatory
            serviceName: FCSCustom
        -   deployOrder: 6
            deployTemplate: cloudsop_amin_distribute_NoBER.json
            require: mandatory
            serviceName: HRS
        -   deployOrder: 9
            deployTemplate: cloudsop_a_alldistribute.json
            require: mandatory
            serviceName: Cron
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute.json
            require: mandatory
            serviceName: FEBS
        -   deployOrder: 10
            deployTemplate: cloudsop_multicluster_distribute.json
            require: mandatory
            serviceName: MQS
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global.json
            require: mandatory
            serviceName: Basic
        -   deployOrder: 30
            deployTemplate: ies_om_topocube_combine_all.json
            require: mandatory
            serviceName: TopoCube
        -   deployOrder: 30
            deployTemplate: ies_om_topology_combine_all.json
            require: mandatory
            serviceName: Topology
        -   deployOrder: 10
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: HiSecCAProxy
        -   deployOrder: 10
            deployTemplate: ies_om_global_securezone_common.json
            require: mandatory
            serviceName: HiSecLiteCA
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: SnbCertMgmt
        sysMonitor:
        -   deployOrder: 30
            deployTemplate: ies_om_global_eSight.json
            require: mandatory
            serviceName: SMLogLic
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVTopo
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: HomePageSettings
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: CAMP
        -   deployOrder: 30
            deployTemplate: ies_all_db_global_with_hiro.json
            require: mandatory
            serviceName: MinApiMgr
        -   deployOrder: 30
            deployTemplate: single_om_alldb_merge.json
            require: mandatory
            serviceName: DrvMgmt
        -   deployOrder: 30
            deployTemplate: single_om_merge.json
            require: mandatory
            serviceName: DrvFrm
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global_combine.json
            require: mandatory
            serviceName: ICS
        -   serviceName: DTEBase
            require: optional
            deployTemplate: dte_om_global_dtebase.json
            deployOrder: 30
        -   serviceName: DTENEManager
            require: optional
            deployTemplate: dte_om_global_nemanager.json
            deployOrder: 30
        -   serviceName: DVMenuUtm
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdFileAgent.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 9
            deployTemplate: HOFS_HOFSOsdService.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSTrackerService.json
            require: mandatory
            serviceName: HOFS
        DVCertService:
        -   deployOrder: 10
            deployTemplate: dv_om_global_cert.json
            require: mandatory
            serviceName: DVCommon
    merge_tool:
        Zenithdb:
        -   deployOrder: 3
            deployTemplate: dv_cloudsop_a_zenithdb.json
            require: mandatory
            serviceName: FCSCustom
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_zenith.json
            deployOrder: 8
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_global_tool.json
            deployOrder: 10
        -   serviceName: DVSM
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 11
        -   deployOrder: 30
            deployTemplate: ies_om_global_tool.json
            require: mandatory
            serviceName: DVEngineeringServicePackage
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVMiddleware
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVIpModify
        -   serviceName: DVSolution
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   serviceName: DVEAM
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        base_merge:
        -   deployOrder: 2
            deployTemplate: dv_cloudsop_a_zk.json
            require: mandatory
            serviceName: FCSCustom
        -   deployOrder: 6
            deployTemplate: cloudsop_b_distribute_heshe_esight.json
            require: mandatory
            serviceName: HRS
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute_zenith.json
            require: mandatory
            serviceName: Cron
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute.json
            require: mandatory
            serviceName: FEBS
        -   deployOrder: 10
            deployTemplate: cloudsop_multicluster_distribute.json
            require: mandatory
            serviceName: MQS
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global.json
            require: mandatory
            serviceName: Basic
        -   deployOrder: 30
            deployTemplate: ies_om_topocube_combine_all.json
            require: mandatory
            serviceName: TopoCube
        -   deployOrder: 30
            deployTemplate: ies_om_topology_combine_all.json
            require: mandatory
            serviceName: Topology
        -   deployOrder: 10
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: HiSecCAProxy
        -   deployOrder: 10
            deployTemplate: ies_om_global_securezone_common.json
            require: mandatory
            serviceName: HiSecLiteCA
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: SnbCertMgmt
        sysMonitor:
        -   deployOrder: 30
            deployTemplate: ies_om_global_eSight.json
            require: mandatory
            serviceName: SMLogLic
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVTopo
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: HomePageSettings
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: CAMP
        -   deployOrder: 30
            deployTemplate: ies_all_db_global_with_hiro.json
            require: mandatory
            serviceName: MinApiMgr
        -   deployOrder: 30
            deployTemplate: single_om_alldb_merge.json
            require: mandatory
            serviceName: DrvMgmt
        -   deployOrder: 30
            deployTemplate: single_om_merge.json
            require: mandatory
            serviceName: DrvFrm
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global_combine.json
            require: mandatory
            serviceName: ICS
        -   serviceName: DTEBase
            require: optional
            deployTemplate: dte_om_global_dtebase.json
            deployOrder: 30
        -   serviceName: DTENEManager
            require: optional
            deployTemplate: dte_om_global_nemanager.json
            deployOrder: 30
        -   serviceName: DVMenuUtm
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdFileAgent.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdService.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSTrackerService.json
            require: mandatory
            serviceName: HOFS
        DVCertService:
        -   deployOrder: 10
            deployTemplate: dv_om_global_cert.json
            require: mandatory
            serviceName: DVCommon
    merge_docker:
        Zenithdb:
        -   deployOrder: 3
            deployTemplate: dv_cloudsop_a_zenithdb.json
            require: mandatory
            serviceName: FCSCustom
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_zenith.json
            deployOrder: 8
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_global.json
            deployOrder: 10
        -   serviceName: DVSM
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 11
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVEngineeringServicePackage
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVMiddleware
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVIpModify
        -   serviceName: DVSolution
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   serviceName: DVAlarm
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVEAM
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVNBI
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        base_merge:
        -   deployOrder: 2
            deployTemplate: dv_cloudsop_a_zk.json
            require: mandatory
            serviceName: FCSCustom
        -   deployOrder: 6
            deployTemplate: cloudsop_b_distribute_heshe_esight.json
            require: mandatory
            serviceName: HRS
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute_zenith.json
            require: mandatory
            serviceName: Cron
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute.json
            require: mandatory
            serviceName: FEBS
        -   deployOrder: 10
            deployTemplate: cloudsop_multicluster_distribute.json
            require: mandatory
            serviceName: MQS
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global.json
            require: mandatory
            serviceName: Basic
        -   deployOrder: 30
            deployTemplate: ies_om_topocube_combine_all.json
            require: mandatory
            serviceName: TopoCube
        -   deployOrder: 30
            deployTemplate: ies_om_topology_combine_all.json
            require: mandatory
            serviceName: Topology
        -   deployOrder: 10
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: HiSecCAProxy
        -   deployOrder: 10
            deployTemplate: ies_om_global_securezone_common.json
            require: mandatory
            serviceName: HiSecLiteCA
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: SnbCertMgmt
        sysMonitor:
        -   deployOrder: 30
            deployTemplate: ies_om_global_eSight.json
            require: mandatory
            serviceName: SMLogLic
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVTopo
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: HomePageSettings
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: CAMP
        -   deployOrder: 30
            deployTemplate: ies_all_db_global_with_hiro.json
            require: mandatory
            serviceName: MinApiMgr
        -   deployOrder: 30
            deployTemplate: single_om_alldb_merge.json
            require: mandatory
            serviceName: DrvMgmt
        -   deployOrder: 30
            deployTemplate: single_om_merge.json
            require: mandatory
            serviceName: DrvFrm
        -   deployOrder: 30
            deployTemplate: cloudsop_om_all_combine.json
            require: mandatory
            serviceName: FM
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global_combine.json
            require: mandatory
            serviceName: ICS
        -   deployOrder: 30
            deployTemplate: UAinfo_UNaviWebsite.json
            require: mandatory
            serviceName: UAinfo
        -   serviceName: DTEBase
            require: optional
            deployTemplate: dte_om_global_dtebase.json
            deployOrder: 30
        -   serviceName: DTENEManager
            require: optional
            deployTemplate: dte_om_global_nemanager.json
            deployOrder: 30
        -   deployOrder: 30
            deployTemplate: ies_NBI_SingleEMS.json
            require: mandatory
            serviceName: NBIService
        -   serviceName: DVMenuUtm
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdFileAgent.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdService.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSTrackerService.json
            require: mandatory
            serviceName: HOFS
        DVCertService:
        -   deployOrder: 10
            deployTemplate: dv_om_global_cert.json
            require: mandatory
            serviceName: DVCommon
        DVPM:
        -   serviceName: DVPerformance
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        DVITManagement:
        -   deployOrder: 30
            deployTemplate: CloudSOP_Itm_Template.json
            require: mandatory
            serviceName: ITM
        -   serviceName: ItmCPPRuntimeRtsp
            require: mandatory
            deployTemplate: CloudSOP_Itm_Template_eSight.json
            deployOrder: 30
        DVMaintenance:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVMaintenance
        DV4AService:
        -   deployOrder: 30
            deployTemplate: ies_om_4a.json
            require: optional
            serviceName: DVMaintenance
        OM_LogtraceService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogtrace
        OM_CallchainService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCallchain
        OM_CatService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCat
        OM_LogmatrixService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogmatrix
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVLogmatrixStreaming
        SA_WorkbenchService:
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVZookeeper
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNats
        -   deployOrder: 9
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVFoundation
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVElasticsearch
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVOMA
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVController
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbench
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbeans
        SA_IhealingService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVIhealing
        SA_InfocollectService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVInfocollect
        PMDashBoard:
        -   serviceName: DVDashBoard
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        DVSaaSOps:
        -   serviceName: DVSaaSOps
            require: optional
            deployTemplate: ies_om_global.json
            deployOrder: 30      
        AnalysisEngine:      
        -   serviceName: DVAnalysisEngine
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        SituationAwareness:
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_vertical.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_aiengine.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: EMSSecurityManager
        SOAR:
        -   deployOrder: 30    
            deployTemplate: ies_om_global_dv.json                                                                       
            require: optional
            serviceName: EMSSOARService
        BasicSecurityOM:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNESec
        -   deployOrder: 8
            deployTemplate: ssa_situation_4k_zenithdb.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 8
            deployTemplate: ssa_detect_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 8
            deployTemplate: ssa_management_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityManager
        -   deployOrder: 30
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: ies_om_global.json
        -   deployOrder: 8
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: unified_asset_zenithdb.json
        -   deployOrder: 8
            serviceName: EMSConfigurationAssessment 
            require: mandatory
            deployTemplate: ssa_configurationAssessment_4k_zenithdb.json
        -   deployOrder: 8
            deployTemplate: soarservice_om_global_zenithdb_dv.json
            require: optional
            serviceName: EMSSOARService
        HostSecurityConfigurationCheck:
        -   deployOrder: 30
            serviceName: EMSConfigurationAssessment
            require: mandatory
            deployTemplate: ies_om_global.json
    MiniCluster_docker:
        Zenithdb:
        -   deployOrder: 3
            deployTemplate: dv_cloudsop_a_zenithdb.json
            require: mandatory
            serviceName: FCSCustom
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_zenith.json
            deployOrder: 8
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_om_global.json
            deployOrder: 10
        -   serviceName: DVSM
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 11
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVEngineeringServicePackage
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVMiddleware
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVIpModify
        -   serviceName: DVSolution
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   serviceName: DVAlarm
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVEAM
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        -   serviceName: DVNBI
            require: mandatory
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        base_merge:
        -   deployOrder: 2
            deployTemplate: dv_cloudsop_a_zk.json
            require: mandatory
            serviceName: FCSCustom
        -   deployOrder: 6
            deployTemplate: cloudsop_b_distribute_heshe_esight.json
            require: mandatory
            serviceName: HRS
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute_zenith.json
            require: mandatory
            serviceName: Cron
        -   deployOrder: 9
            deployTemplate: cloudsop_a_distribute.json
            require: mandatory
            serviceName: FEBS
        -   deployOrder: 10
            deployTemplate: cloudsop_multicluster_distribute.json
            require: mandatory
            serviceName: MQS
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global.json
            require: mandatory
            serviceName: Basic
        -   deployOrder: 30
            deployTemplate: ies_om_topocube_combine_all.json
            require: mandatory
            serviceName: TopoCube
        -   deployOrder: 30
            deployTemplate: ies_om_topology_combine_all.json
            require: mandatory
            serviceName: Topology
        -   deployOrder: 10
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: HiSecCAProxy
        -   deployOrder: 10
            deployTemplate: ies_om_global_securezone_common.json
            require: mandatory
            serviceName: HiSecLiteCA
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: SnbCertMgmt
        sysMonitor:
        -   deployOrder: 30
            deployTemplate: ies_om_global_eSight.json
            require: mandatory
            serviceName: SMLogLic
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: mandatory
            serviceName: DVTopo
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: HomePageSettings
        -   deployOrder: 30
            deployTemplate: ies_all_om_global.json
            require: mandatory
            serviceName: CAMP
        -   deployOrder: 30
            deployTemplate: ies_all_db_global_with_hiro.json
            require: mandatory
            serviceName: MinApiMgr
        -   deployOrder: 30
            deployTemplate: single_om_alldb_merge.json
            require: mandatory
            serviceName: DrvMgmt
        -   deployOrder: 30
            deployTemplate: single_om_merge.json
            require: mandatory
            serviceName: DrvFrm
        -   deployOrder: 30
            deployTemplate: cloudsop_om_all_combine.json
            require: mandatory
            serviceName: FM
        -   deployOrder: 30
            deployTemplate: cloudsop_all_global_combine.json
            require: mandatory
            serviceName: ICS
        -   deployOrder: 30
            deployTemplate: UAinfo_UNaviWebsite.json
            require: mandatory
            serviceName: UAinfo
        -   serviceName: DTEBase
            require: optional
            deployTemplate: dte_om_global_dtebase.json
            deployOrder: 30
        -   serviceName: DTENEManager
            require: optional
            deployTemplate: dte_om_global_nemanager.json
            deployOrder: 30
        -   deployOrder: 30
            deployTemplate: ies_NBI_SingleEMS.json
            require: mandatory
            serviceName: NBIService
        -   serviceName: DVMenuUtm
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdFileAgent.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSOsdService.json
            require: mandatory
            serviceName: HOFS
        -   deployOrder: 10
            deployTemplate: HOFS_HOFSTrackerService.json
            require: mandatory
            serviceName: HOFS
        DVPM:
        -   serviceName: DVPerformance
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        DVCertService:
        -   deployOrder: 10
            deployTemplate: dv_om_global_cert.json
            require: mandatory
            serviceName: DVCommon
        DVITManagement:
        -   deployOrder: 30
            deployTemplate: CloudSOP_Itm_Template.json
            require: mandatory
            serviceName: ITM
        -   serviceName: ItmCPPRuntimeRtsp
            require: mandatory
            deployTemplate: CloudSOP_Itm_Template_eSight.json
            deployOrder: 30
        DVMaintenance:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVMaintenance
        DV4AService:
        -   deployOrder: 30
            deployTemplate: ies_om_4a.json
            require: optional
            serviceName: DVMaintenance
        OM_LogtraceService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogtrace
        OM_CallchainService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCallchain
        OM_CatService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVCat
        OM_LogmatrixService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVLogmatrix
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVLogmatrixStreaming
        SA_WorkbenchService:
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVZookeeper
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNats
        -   deployOrder: 9
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVFoundation
        -   deployOrder: 11
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVElasticsearch
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVOMA
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVController
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbench
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVWorkbeans
        SA_IhealingService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVIhealing
        SA_InfocollectService:
        -   deployOrder: 30
            deployTemplate: ies_om_zenith.json
            require: optional
            serviceName: DVInfocollect
        PMDashBoard:
        -   serviceName: DVDashBoard
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        DVSaaSOps:
        -   serviceName: DVSaaSOps
            require: optional
            deployTemplate: ies_om_global.json
            deployOrder: 30
        AnalysisEngine:       
        -   serviceName: DVAnalysisEngine
            require: optional
            deployTemplate: ies_om_zenith.json
            deployOrder: 30
        DVMediation:
        -   serviceName: DVMediation
            require: mandatory
            deployTemplate: ies_om_global.json
            deployOrder: 30
        SituationAwareness:
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_vertical.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 30
            deployTemplate: ies_om_global_no_aiengine.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: EMSSecurityManager
        SOAR:
        -   deployOrder: 30    
            deployTemplate: ies_om_global_dv.json                                                                      
            require: optional
            serviceName: EMSSOARService
        DVPM1:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_1.json
            deployOrder: 9
        DVPM2:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_2.json
            deployOrder: 10
        DVPM3:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_3.json
            deployOrder: 11
        DVPM4:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_4.json
            deployOrder: 12
        DVPM5:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_5.json
            deployOrder: 13
        DVPM6:
        -   serviceName: DVCommon
            require: mandatory
            deployTemplate: dv_pm_zenith_6.json
            deployOrder: 14
        BasicSecurityOM:
        -   deployOrder: 30
            deployTemplate: ies_om_global.json
            require: optional
            serviceName: DVNESec
        -   deployOrder: 15
            deployTemplate: ssa_situation_4k_zenithdb.json
            require: optional
            serviceName: EMSSecuritySituationMonitor
        -   deployOrder: 15
            deployTemplate: ssa_detect_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityDetection
        -   deployOrder: 15
            deployTemplate: ssa_management_4k_zenithdb.json
            require: optional
            serviceName: EMSSecurityManager
        -   deployOrder: 30
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: ies_om_global.json
        -   deployOrder: 15
            serviceName: EMSUnificationService
            require: mandatory
            deployTemplate: unified_asset_zenithdb.json
        -   deployOrder: 15
            serviceName: EMSConfigurationAssessment 
            require: mandatory
            deployTemplate: ssa_configurationAssessment_4k_zenithdb.json
        -   deployOrder: 15
            deployTemplate: soarservice_om_global_zenithdb_dv.json
            require: optional
            serviceName: EMSSOARService
        HostSecurityConfigurationCheck:
        -   deployOrder: 30
            serviceName: EMSConfigurationAssessment
            require: mandatory
            deployTemplate: ies_om_global.json
optionalFeatureList:
    all:
        - DVMaintenance
        - PMDashBoard
        - OM_LogtraceService
        - OM_CallchainService
        - OM_CatService
        - OM_LogmatrixService
        - SA_WorkbenchService
        - SA_IhealingService
        - SA_InfocollectService
        - DV4AService
        - DVCertService
        - DVSaaSOps
        - AnalysisEngine
        - SituationAwareness
        - SOAR
        - BasicSecurityOM
        - HostSecurityConfigurationCheck
        - DVMediation
        - DVPM1
        - DVPM2
        - DVPM3
        - DVPM4
        - DVPM5
        - DVPM6
    single:
        - DVMaintenance
        - PMDashBoard
        - OM_LogtraceService
        - OM_CallchainService
        - OM_CatService
        - OM_LogmatrixService
        - SA_WorkbenchService
        - SA_IhealingService
        - SA_InfocollectService
        - DV4AService
        - DVCertService
        - DVSaaSOps
        - AnalysisEngine
        - SituationAwareness
        - SOAR
        - BasicSecurityOM
        - HostSecurityConfigurationCheck
    merge:
        - DVMaintenance
        - PMDashBoard
        - OM_LogtraceService
        - OM_CallchainService
        - OM_CatService
        - OM_LogmatrixService
        - SA_WorkbenchService
        - SA_IhealingService
        - SA_InfocollectService
        - DV4AService
        - DVCertService
        - DVSaaSOps
        - AnalysisEngine
        - SituationAwareness
        - SOAR
        - BasicSecurityOM
        - HostSecurityConfigurationCheck
    merge_lite:
        - DVPM
        - DVNBI
        - DVTopo
        - DVIpModify
    merge_mini:
        - DVMiddleware
        - PMDashBoard
        - SA_WorkbenchService
    MiniCluster:
        - DVMaintenance
        - PMDashBoard
        - OM_LogtraceService
        - OM_CallchainService
        - OM_CatService
        - OM_LogmatrixService
        - SA_WorkbenchService
        - SA_IhealingService
        - SA_InfocollectService
        - DV4AService
        - DVCertService
        - DVSaaSOps
        - AnalysisEngine
        - DVMediation
        - SituationAwareness
        - SOAR
        - DVPM1
        - DVPM2
        - DVPM3
        - DVPM4
        - DVPM5
        - DVPM6
        - BasicSecurityOM
        - HostSecurityConfigurationCheck
    MiniCluster_lite:
        - DVMiddleware
        - PMDashBoard
        - SA_WorkbenchService
        - SA_IhealingService
    largeCapacity:
        - DVMaintenance
        - PMDashBoard
        - OM_LogtraceService
        - OM_CallchainService
        - OM_CatService
        - OM_LogmatrixService
        - SA_WorkbenchService
        - SA_IhealingService
        - SA_InfocollectService
        - DV4AService
        - DVCertService
        - DVSaaSOps
        - AnalysisEngine
        - DVMediation
        - SituationAwareness
        - SOAR
        - DVPM1
        - DVPM2
        - DVPM3
        - DVPM4
        - DVPM5
        - DVPM6
        - BasicSecurityOM
        - HostSecurityConfigurationCheck
    single_tool:
        - DVCertService
    merge_tool:
        - DVCertService
    merge_docker:
        - DVMaintenance
        - PMDashBoard
        - OM_LogtraceService
        - OM_CallchainService
        - OM_CatService
        - OM_LogmatrixService
        - SA_WorkbenchService
        - SA_IhealingService
        - SA_InfocollectService
        - DV4AService
        - DVCertService
        - DVSaaSOps
        - AnalysisEngine
        - SituationAwareness
        - SOAR
        - BasicSecurityOM
        - HostSecurityConfigurationCheck
    MiniCluster_docker:
        - DVMaintenance
        - PMDashBoard
        - OM_LogtraceService
        - OM_CallchainService
        - OM_CatService
        - OM_LogmatrixService
        - SA_WorkbenchService
        - SA_IhealingService
        - SA_InfocollectService
        - DV4AService
        - DVCertService
        - DVSaaSOps
        - AnalysisEngine
        - DVMediation
        - SituationAwareness
        - SOAR
        - DVPM1
        - DVPM2
        - DVPM3
        - DVPM4
        - DVPM5
        - DVPM6
        - BasicSecurityOM
        - HostSecurityConfigurationCheck
fileVerison: 1
input:
    Zenithdb:
        enableNBIServiceMode:
            default: FALSE
            description: Whether to enable DVNBI Service
            invisible: true
            range: 'FALSE,TRUE'
            require: true
            type: enum
        dv_site_info_list:
            default: ''
            description: DV site information list
            require: false
            type: string
            invisible: false
            pattern: '[A-Za-z0-9.:\[\];|]*'
    base:
        APIGatewayRegisterMode:
            default: synchrono
            description: API gateway register mode
            invisible: false
            range: 'synchrono,asynchrono'
            require: true
            type: enum
        loadBalancingAndFaultTolerantMode:
            default: inTurn
            description: load balancing and fault tolerant mode
            invisible: false
            range: 'random,inTurn'
            require: true
            type: enum
        ServiceTracingMemory: 
            default: 300 
            description: service trace memory(unit M)
            invisible: false
            require: true
            type: int
        messageDrivingServieMaxMemory: 
            default: 100  
            description: message driver service max memory(unit M)
            invisible: false
            require: true 
            type: int
        SetingToolsMode: 
            default: synchrono 
            description: Setting tool mode 
            invisible: false 
            range: synchrono,asynchrono
            require: true
            type: enum
        FTP_PASSWD: 
            default: ''
            description: Password of ftpuser for OMC only
            invisible: false
            require: false
            type: encrypt
            pattern: '[\w~@#^*\-_+\[{}\]:,.\/?%=!]{0,256}'
        serviceMessageBusRegisterMode: 
            default: synchrono
            description: service and message bus register mode
            invisible: false
            range: synchrono,asynchrono
            require: true
            type: enum
        GLOBAL_UNIFIED_OSS_USER_PASSWORD:
            default: ''
            description: OS user password , include ossuser、sftpossuser、sshossuser、devdata、sysomc
            require: false
            type: encrypt
            invisible: true
        GLOBAL_UNIFIED_OSS_DB_USER_PASSWORD:
            default: ''
            description: DB user password , include OSSADMIN、OSSUSER、OMSSYS、OMSMODEL、IEMPEAM、OMSSM、OMSFM、OMSCMON、OMSPM、OMSPM_INDEX、OMSCM、OMUUSER、ICNFGAPPDB...
            require: false
            type: encrypt
            invisible: true
        web_admin_user_name:
            default: admin
            description: admin user name
            require: true
            type: string
            invisible: true
        web_admin_user_value:
            default: ''
            description: admin user password
            require: true
            type: encrypt
            invisible: false
            pattern: ^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!\"#$%&\'()*\+,\-./\:;\<=\>\?@\[\\\]^`{_|}~])[\da-zA-Z!\"#$%&\'()*+,-./:;<=>?@\[\\\]^`{_|}~]{8,32}$
        AUTH_ENABLE_TWOFACTOR:
            default: true
            description: Two-Factor Authentication
            require: true
            type: string
            invisible: true
        IS_ENABLE_SECONDARY_AUTHENTICATION:
            default: true
            description: Secondary Authentication
            range: true,false
            require: true
            type: enum
            invisible: false
        IS_ENABLE_REVIEW_ABILITY:
            default: true
            description: Open review ability or not
            range: true,false
            require: true
            type: enum
            invisible: false
        IS_ENABLE_CN_AND_EN_AUDITLOGS:
            default: true
            description: Record the security log in both Chinese and English
            range: true,false
            require: true
            type: enum
            invisible: true
        IS_SEND_MQ_AUDITLOG:
            default: true
            description: Report to kafka
            range: true,false
            require: true
            type: enum
            invisible: true
        wideCharVerified:
            default: true
            description: support chinese or not
            require: true
            type: string
            invisible: true
        IS_INCREMENTUPGRADE:
            default: NO
            description: is increment upgrade or not 
            require: true
            type: string
            invisible: true
        IS_INSTALL_ICNFG:
            default: YES
            description: is install icnfg or not  
            require: true
            type: string
            invisible: true
        is_support_express:
            default: false
            description: is support express or not  
            require: true
            type: string
            invisible: true
        enablePMServiceMode:
            default: TRUE
            description: is support DVPM or not  
            require: true
            type: string
            invisible: true
        ER_TOTAL_LIMIT_CONN:
            default: '1000'
            description: ER TOTAL LIMIT CONN
            require: true
            type: int
            invisible: false
            range: 2,30000
        LIMIT_CONN_PERIP:
            default: '300'
            description: LIMIT CONN PERIP
            require: true
            type: int
            invisible: false
            range: 2,30000
        KERNEL_PAGE_SIZE:
            default: '4K'
            description: PAGE TABLE SIZE
            require: true
            invisible: true
            type: enum
            range: 4K,64K
    base_merge:
        APIGatewayRegisterMode:
            default: synchrono
            description: API gateway register mode
            invisible: false
            range: 'synchrono,asynchrono'
            require: true
            type: enum
        loadBalancingAndFaultTolerantMode:
            default: inTurn
            description: load balancing and fault tolerant mode
            invisible: false
            range: 'random,inTurn'
            require: true
            type: enum
        ServiceTracingMemory: 
            default: 300 
            description: service trace memory(unit M)
            invisible: false
            require: true
            type: int
        messageDrivingServieMaxMemory: 
            default: 100  
            description: message driver service max memory(unit M)
            invisible: false
            require: true 
            type: int
        SetingToolsMode: 
            default: synchrono 
            description: Setting tool mode 
            invisible: false 
            range: synchrono,asynchrono
            require: true
            type: enum
        FTP_PASSWD: 
            default: ''
            description: Password of ftpuser for OMC only
            invisible: false
            require: false
            type: encrypt
            pattern: '[\w~@#^*\-_+\[{}\]:,.\/?%=!]{0,256}'
        serviceMessageBusRegisterMode: 
            default: synchrono
            description: service and message bus register mode
            invisible: false
            range: synchrono,asynchrono
            require: true
            type: enum
        GLOBAL_UNIFIED_OSS_USER_PASSWORD:
            default: ''
            description: OS user password , include ossuser、sftpossuser、sshossuser、devdata、sysomc
            require: false
            type: encrypt
            invisible: true
        GLOBAL_UNIFIED_OSS_DB_USER_PASSWORD:
            default: ''
            description: DB user password , include OSSADMIN、OSSUSER、OMSSYS、OMSMODEL、IEMPEAM、OMSSM、OMSFM、OMSCMON、OMSPM、OMSPM_INDEX、OMSCM、OMUUSER、ICNFGAPPDB...
            require: false
            type: encrypt
            invisible: true
        web_admin_user_name:
            default: admin
            description: admin user name
            require: true
            type: string
            invisible: true
        web_admin_user_value:
            default: ''
            description: admin user password
            require: true
            type: encrypt
            invisible: false
            pattern: ^(?=.*\d)(?=.*[A-Z])(?=.*[a-z])(?=.*[!\"#$%&\'()*\+,\-./\:;\<=\>\?@\[\\\]^`{_|}~])[\da-zA-Z!\"#$%&\'()*+,-./:;<=>?@\[\\\]^`{_|}~]{8,32}$
        AUTH_ENABLE_TWOFACTOR:
            default: true
            description: Two-Factor Authentication
            require: true
            type: string
            invisible: true
        IS_ENABLE_SECONDARY_AUTHENTICATION:
            default: true
            description: Secondary Authentication
            range: true,false
            require: true
            type: enum
            invisible: false
        IS_ENABLE_REVIEW_ABILITY:
            default: true
            description: Open review ability or not
            range: true,false
            require: true
            type: enum
            invisible: false
        IS_ENABLE_CN_AND_EN_AUDITLOGS:
            default: true
            description: Record the security log in both Chinese and English
            range: true,false
            require: true
            type: enum
            invisible: true        
        IS_SEND_MQ_AUDITLOG:
            default: true
            description: Report to kafka
            range: true,false
            require: true
            type: enum
            invisible: true
        wideCharVerified:
            default: true
            description: support chinese or not
            require: true
            type: string
            invisible: true
        IS_INCREMENTUPGRADE:
            default: NO
            description: is increment upgrade or not 
            require: true
            type: string
            invisible: true
        IS_INSTALL_ICNFG:
            default: YES
            description: is install icnfg or not  
            require: true
            type: string
            invisible: true
        is_support_express:
            default: false
            description: is support express or not  
            require: true
            type: string
            invisible: true
        is_integrated_df_and_service:
            default: No
            description: is integrated_df and service or not  
            require: true
            type: string
            invisible: true
        enablePMServiceMode:
            default: TRUE
            description: is support DVPM or not  
            require: true
            type: string
            invisible: true
        ER_TOTAL_LIMIT_CONN:
            default: '1000'
            description: 'ER TOTAL LIMIT CONN'
            require: true
            type: int
            invisible: false
            range: 2,30000
        LIMIT_CONN_PERIP:
            default: '300'
            description: 'LIMIT CONN PERIP'
            require: true
            type: int
            invisible: false
            range: 2,30000
        dv_lite_mode:
            default: 'FALSE'
            description: Lite Deploy support
            range: TRUE,FALSE
            require: true
            type: enum
            invisible: false
        KERNEL_PAGE_SIZE:
            default: '4K'
            description: PAGE TABLE SIZE
            require: true
            invisible: true
            type: enum
            range: 4K,64K
    sysMonitor:
        is_extend_internal_certificate_validity_period:
            default: 'False'
            description: is extend internal certificate validity period
            range: 'False,True'
            require: true
            type: enum
            invisible: true
        enableSwitchBack:
            default: 'Yes'
            description: enable switch back
            range: 'Yes,No'
            require: true
            type: enum
            invisible: false
        DV_SEC_RUNAS_USER:
            default: secuser
            description: Security run as user
            require: false
            type: string
            invisible: true
        Kafka_Retention_Time:
            default: Default
            description: Kafka Retention Time
            range: 'Default,Short'
            require: true
            type: enum
            invisible: false
        detailed_log_printing_mode:
            default: No
            description: detailed log printing mode
            range: 'No,Yes'
            require: true
            type: enum
            invisible: false
        custom_path_list:
            default: '/opt,/home,/tmp'
            description: custom path list
            require: false
            type: string
            invisible: true
        dv_deploy_scale_size:
            default: default
            description: Configure the running parameters. The default value is default. The options are default, small, venus.
            range: 'default,small,venus'
            require: true
            type: enum
        upgrade_pmdashboarddb_pwd:
            default: 
            description: upgrade pmdashboarddb password
            require: false
            type: string
            invisible: true
        tool_scp_pwd:
            default: 
            description: Certificate management password
            require: false
            type: string
            invisible: true
        dv_sso_whitelist:
            default: 
            description: SSO whitelist
            require: false
            type: string
            invisible: true
        isLiteMonitor:
            default: 'false'
            description: Liting Monitor or not.LiteMonitor is used for enhancing OM(B mode)
            require: true
            type: string
            pattern: '[a-zA-Z]{0,10}'
        i2k_interface_password_secure_mode:
            default: long_password
            description: The machine interface password mode
            require: true
            type: string
            pattern: '[a-zA-Z_]{0,64}'
        DATA_MGMT_SERVICE_IP:
            default: 127.0.0.1
            description: IP address of the management plane
            require: true
            type: string
            invisible: true
        MGR_ZK_HOST_IP:
            default: 127.0.0.1
            description: IP address and port number of manager
            require: true
            type: string
            invisible: true
        OMConsoleAccessAddress:
            default: 127.0.0.1:31943
            description: Floating IP address of the O&M plane
            require: true
            type: string
            invisible: true
        IS_USE_WEB_TOPO:
            default: 'true'
            description: is use web topo
            require: true
            type: string
            invisible: true
        UNISSO_SERVICE_TRUSTED_IP:
            default: 127.0.0.1
            description: IP address or domain name trusted by UniSSO
            require: true
            type: string
            invisible: true
        ZK_HOST_IP:
            default: 127.0.0.1
            description: IP address and port number of the node where ZooKeeper is
                deployed
            require: true
            type: string
            invisible: true
        ZK_LIST:
            default: 127.0.0.1
            description: List of IP addresses of the nodes where ZooKeeper is deployed
            require: true
            type: string
            invisible: true
        enableNetworkElementLargeCapacity:
            default: 'false'
            description: Whether to control network element LargeCapacity
            require: true
            type: string
            pattern: '[a-zA-Z]{0,10}'
        enableMappingHeaderDate:
            default: 'true'
            description: Whether MinAPIGatewayService maps the header date to X-IWS-Date
            require: true
            type: string
            invisible: true
        globalBackendLBFloatIPAddr:
            default: 127.0.0.1
            description: Floating IP address of BackEndER
            require: true
            type: string
            invisible: true
        regionAlias:
            default: SHA
            description: regionAlias name
            require: true
            type: string
            invisible: true
        zenith_paramgroup_file:
            default: large
            description: The zenith db install paramgroup_file parameter.
            require: true
            type: string
            invisible: true
        resourceScene:
            default: EMS
            description: Resource deployment mode
            require: true
            type: string
            invisible: true
        dteModeIMode:
            default: xml
            description: DTE model service running mode
            require: true
            type: string
            invisible: true
        secondaryAuthForceEnable:
            default: 'false'
            description: Whether to forcibly enable secondary authorization
            require: true
            type: string
            invisible: true
        secondaryAuthSupportRole:
            default: 'false'
            description: Whether secondary authorization can be configured for roles
            require: true
            type: string
            invisible: true
        tenantName:
            default: ies
            description: tenantName For DBM
            require: true
            type: string
            invisible: true
        ER_LISTEN_EXTERNAL_IP:
            description: 'Floating IP address that ERService listened'
            default: 'false'
            type: string
            require: true
            invisible: true
        CHECK_SESSION_IP:
            description: 'Verifying the Client IP Address'
            default: 'true'
            type: string
            require: true
            invisible: true
        BER_LISTEN_EXTERNAL_IP:
            description: 'Floating IP address that BackendERService listened'
            default: 'false'
            type: string
            require: true
            invisible: true
        ES_NODE_IP_LIST:
            description:  'IP list of elasticsearch sevice nodes.'
            default: '127.0.0.1'
            type: string
            require: true
            invisible: true
        ES_PORT:
            description:  'Port of elasticsearch sevice.'
            default: '9200'
            type: string
            require: true
            invisible: true
        INFOCENTER_MED_ROOT:
            description:  'Path of INFOCENTER_MED'
            default: '/opt/oss/share/SOP/DVCommonNotifyService'
            type: string
            require: true
            invisible: true
        DV_SFTP_USER:
            description:  'DV sftp user'
            default: 'admin'
            type: string
            require: true
            invisible: true
        DV_SFTP_PASSWORD:
            description:  'DV sftp user password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        paasinter_pwd:
            description:  'paasinter password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        I2kNfv_pwd:
            description:  'I2000 Nfv password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        cie_snmp_pwd:
            description:  'cie snmp password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        sftp_admin_pwd:
            description:  'sftp admin password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        sftp_nbiuser_pwd:
            description:  'sftp nbiuser password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        sftp_mmluser_pwd:
            description: 'sftp mmluser password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        sftp_assetuser_pwd:
            description:  'sftp assetuser password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        dms_db_pwd:
            description:  'dms db password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        easyms_db_pwd:
            description:  'easyms db password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        csweb_db_pwd:
            description:  'csweb db password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        pmdashboard_db_pwd:
            description:  'pmdashboard db password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        cs_service_pwd:
            description: 'cs service password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        DV_TOOLKIT_INSTALL:
            default: ''
            description: 'DV_TOOLKIT_INSTALL'
            require: true
            type: string
            invisible: true
        DV_CERT_DRIVER_pwd:
            description:  'DV cert driver password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        tool_mainast_pwd:
            description:  'tool mainast password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        tool_ideploy_pwd:
            description:  'tool ideploy password'
            default: ''
            type: encrypt
            require: true
            invisible: true
        tool_dms_pwd:
            description:  'tool dms password'
            default: ''
            type: encrypt
            require: false
            invisible: true
        tool_das_pwd:
            description:  'tool das password'
            default: ''
            type: encrypt
            require: false
            invisible: true
        tool_easyms_pwd:
            description:  'tool easyms password'
            default: ''
            type: encrypt
            require: false
            invisible: true
        dv_ca_p12_store_pwd:
            description:  'dv_ca.p12 store pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        dv_ca_p12_key_pwd:
            description:  'dv_ca.p12 key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        caTrustStore_jks_store_pwd:
            description:  'caTrustStore.jks store pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        caTrustStore_jks_key_pwd:
            description:  'caTrustStore.jks key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        servertrust_keystore_store_pwd:
            description:  'servertrust.keystore store pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        servertrust_keystore_key_pwd:
            description:  'servertrust.keystore key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        UNIAGENT_TRUSTSTORE_PWD:
            description:  'UNIAGENT_TRUSTSTORE PWD'
            default: ''
            type: encrypt
            require: false
            invisible: true
        uniagent_client_trust_jks_store_pwd:
            description:  'uniagent_client_trust.jks store pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        uniagent_client_trust_jks_key_pwd:
            description:  'uniagent_client_trust.jks key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        UNIAGENT_TRACE_TRUSTSTORE_PWD:
            description:  'UNIAGENT_TRACE_TRUSTSTORE PWD'
            default: ''
            type: encrypt
            require: false
            invisible: true
        uniagent_client_jks_store_pwd:
            description:  'uniagent_client.jks store pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        uniagent_client_jks_key_pwd:
            description:  'uniagent_client.jks key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        serverkey_keystore_store_pwd:
            description:  'serverkey.keystore store pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        serverkey_keystore_key_pwd:
            description:  'serverkey.keystore key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        UNIAGENT_KEYSTORE_PWD:
            description:  'UNIAGENT_KEYSTORE PWD'
            default: ''
            type: encrypt
            require: false
            invisible: true
        openapi_server_p12_store_pwd:
            description:  'openapi_server.p12 store pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        openapi_server_p12_key_pwd:
            description:  'openapi_server.p12 key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        outserver_p12_store_pwd:
            description:  'outserver.p12 store pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        outserver_p12_key_pwd:
            description:  'outserver.p12 key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        outclient_pfx_store_pwd:
            description:  'outclient.pfx store pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        outclient_pfx_key_pwd:
            description:  'outclient.pfx key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        nodeKeyStore_jks_store_pwd:
            description:  'nodeKeyStore.jks store pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        nodeKeyStore_jks_key_pwd:
            description:  'nodeKeyStore.jks key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        client_key_key_pwd:
            description:  'client.key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        controller_server_p12_store_pwd:
            description:  'controller_server.p12 store pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        controller_server_p12_key_pwd:
            description:  'controller_server.p12 key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        foreign_client_p12_store_pwd:
            description:  'foreign_client.p12 store pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        foreign_client_p12_key_pwd:
            description:  'foreign_client.p12 key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        trust_jks_store_pwd:
            description:  'trust.jks store pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        trust_jks_key_pwd:
            description:  'trust.jks key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        sftp_privateKey_key_pwd:
            description:  'sftp_privateKey key pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        uniagent_sign_pwd:
            description:  'uniagent sign pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        pkg_sign_pwd:
            description:  'pkg sign pwd'
            default: ''
            type: encrypt
            require: false
            invisible: true
        DV_SFTP_PORT:
            description:  'DV sftp port'
            default: '31922'
            type: string
            require: true
            invisible: true
        NFV_MODE:
            description:  'NFV mode'
            default: 'OFF'
            type: string
            require: true
            pattern: '[a-zA-Z]{0,10}'
        omcversion:
            description: 'OMC Version'
            default: 'V800R001C23'
            type: string
            require: true
            invisible: true
        IMAPBUS_SERVERNAME:
            description: 'BusService IP'
            default: 'true'
            type: string
            require: true
            invisible: true
        LIC_SnmpAgentService:
            description: 'LIC_SnmpAgentService'
            default: 'true'
            type: string
            require: true
            invisible: true
        CMP_ZK_NODE_LIST:
            default: 
            description: CMP ZK node IP address and port
            require: true
            type: string
            invisible: true
        FMDeployMode:
            default: Cluster
            description: FM Deploy Mode
            require: true
            type: string
            invisible: true
        NetworkType:
            default: Single
            description: Network Type
            require: true
            type: string
            invisible: true
        isAppNbiIpExtend:
            default: No
            description: Whether to configure app ip extension for north sftp interface
            require: true
            type: string
            invisible: true
        APP_EXTERNAL_NBI_IPV4:
            default: 127.0.0.1
            description: app ip extension for north sftp interface
            require: true
            type: string
            invisible: true
        isAppNfvIpExtend:
            default: No
            description: Whether to configure app ip extension for nfv interface
            require: true
            type: string
            invisible: true
        nfv_Extend_IP:
            default: 127.0.0.1
            description: app ip extension for nfv interface
            require: true
            type: string
            invisible: true
        CMP_ROOT_KEY_FACTORY_FIRST:
            default: 
            description: cmp root key factory first
            require: true
            type: string
            invisible: true
        CMP_ROOT_KEY_FACTORY_SECOND:
            default: 
            description: cmp root key factory second
            require: true
            type: string
            invisible: true
        CMP_WORK_KEY_FACTORY:
            default: 
            description: cmp work key factory
            require: true
            type: string
            invisible: true
        CMP_HMAC_SHARE_KEY:
            default: 
            description: cmp hmac share key
            require: true
            type: string
            invisible: true
        CMP_AUTH_SALT:
            default: 
            description: cmp auth salt
            require: true
            type: string
            invisible: true
        CMP_AUTH_PASS:
            default: 
            description: cmp auth pass
            require: true
            type: string
            invisible: true
        MED_ACCESS_KEY:
            default: 
            description: med access key
            require: true
            type: string
            invisible: true
        MED_SECRET_KEY:
            default: 
            description: med secret key
            require: true
            type: string
            invisible: true
        UTM_ACCESS_KEY:
            default: 
            description: utm access key
            require: true
            type: string
            invisible: true
        UTM_SECRET_KEY:
            default: 
            description: utm secret key
            require: true
            type: string
            invisible: true
        CurrentAlarmCapacity:
            default: 'Default'
            description: Current Alarm Capacity
            require: true
            type: string
            pattern: '^(50000|100000|200000|300000|500000|1000000|Default)$'
        enableLuceneQueryForHistoryAlarm:
            default: false
            description: Enable Lucene query for historical alarms
            range: true,false
            require: true
            type: enum
        enableMemSpeed:
            default: 'false'
            description: Current Alarm Capacity
            require: true
            type: string
            invisible: true
        StandbyIPAddress:
            default: 127.0.0.1:32041
            description: IP address of the standby node on the O&M plane
            require: true
            type: string
            invisible: true
        floatIPAddress:
            default: 127.0.0.1:32041
            description: Floating IP address of the O&M plane
            require: true
            type: string
            invisible: true
        floatIPMask:
            default: 127.0.0.1:32041
            description: Mask for the floating IP address of the O&M plane.
            require: true
            type: string
            invisible: true
        floatNICAlias:
            default: 127.0.0.1:32041
            description: NIC for the floating IP address of the O&M plane
            require: true
            type: string
            invisible: true
        Auth.UniSession.Auth.Mode:
            default: ''
            description: Authentication Mode
            range: 'thirdpart,'
            require: false
            type: enum
        AUTH:
            default: ''
            description: DigitalView SSO Customization
            require: false
            type: string
            pattern: '[0-9a-zA-Z@_:/.\[\]-]*'
        Auth.UniSession.Session.Key:
            default: ''
            description: Session cookie customization
            range: 'bspsession,'
            require: false
            type: enum
        ssoServerUrl:
            default: ''
            description: Third-party SSO server
            require: false
            type: string
            pattern: '[0-9a-zA-Z@_:/.\[\]-]*'
        ssoType:
            default: DEFAULT
            description: SSO type
            range: 'DEFAULT,VENUS,CBS_CAS,CBS_CAS_MULTIPLANE'
            require: false
            type: enum
        EMSMode:
            default: NA
            description: EMS Mode
            range: 'Central,Local,NA'
            require: false
            type: enum
        ssoBackupServerUrl:
            default: ''
            description: Third-party SSO backup server
            require: false
            type: string
            pattern: '[0-9a-zA-Z@_:/.\[\]-]*'
        approveMaxClientBodySize:
            default: true
            description: Whether to enable max clent body size
            range: true,false
            require: true
            type: enum
        enableSaasMode:
            default: false
            description: Whether to enable multi-tenancy
            range: true,false
            require: true
            type: enum
        enableAccountNonRootMode:
            default: false
            description: Specifies whether to enable account management in non-root mode,this mode requires the support of all NEs managed by the DigitalView.Otherwise, the password change function will be unavailable.
            range: true,false
            require: true
            type: enum
        Auth.DomainCustomeConfig:
            default: 'ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP'
            description: Domain-based Mode Customization
            require: true
            type: string
            pattern: '^[a-zA-Z0-9_\-.,]{1,256}$'
        smEnablePolicyAuth:
            default: false
            description: Policy-based Authentication Capability Switch
            range: true,false
            require: true
            type: enum
            invisible: false
        HOFS_STORE:
            default: '/opt/oss/hofs/hofs_store'
            description: hofs store dir
            require: true
            type: string
            invisible: true
        HOFS_ROOT_DIR:
            default: '/opt/oss/hofs/hofs_root'
            description: hofs root dir
            require: true
            type: string
            invisible: true
        HOFS_FUSE_HOME:
            default: '/opt/oss/hofs/hofs_fuse'
            description: hofs fuse home
            require: true
            type: string
            invisible: true
        need_drbd:
            default: 'YES'
            description: need drbd or not
            require: true
            type: string
            invisible: true
        integrated_mode:
            default: DigitalFoundry-DigitalView-service separate deployment
            description: The options are as follows【DigitalFoundry-service co-deployment,DigitalFoundry-DigitalView-service co-deployment and DigitalFoundry-DigitalView-service separate deployment】. By default, DigitalFoundry-DigitalView-service separate deployment， If the DigitalFoundry and services are co-deployed, select DigitalFoundry-service co-deployment. If the DigitalFoundry, DigitalView, and services are co-deployed, select DigitalFoundry-DigitalView-service co-deployment. Co-deployment indicates that the DigitalFoundry, DigitalView, services use the same operating system.
            invisible: true
            range: 'DigitalFoundry-service co-deployment,DigitalFoundry-DigitalView-service co-deployment,DigitalFoundry-DigitalView-service separate deployment'
            require: true
            type: enum
        enableUOAConnection:
            default: 'false'
            description: The options are as follows [false, true]. By default, false. Whether to enable the connection to the UOA management ne.
            invisible: false
            range: 'false,true'
            require: true
            type: enum
        enablePMServiceMode:
            default: TRUE
            description: is support DVPM or not  
            require: true
            type: string
            invisible: true
        EXTEND_TYPE:
            default: 'DEFAULT'
            description: DVPM EXTEND TYPE
            require: false
            type: string
            invisible: true
        zenith_pm_paramgroup_file:
            default: large
            description: The pm zenith db install paramgroup_file parameter.
            require: true
            type: string
            invisible: true
        PM_DATA_DB_SIZE:
            default: '30720'
            description: Tablespace size of the performance database
            require: false
            type: string
            invisible: true
        PM_DATA_DB_SIZE_1:
            default: '1048576'
            description: Tablespace size of the performance cluster 1 database
            require: false
            type: string
            invisible: true
        PM_DATA_DB_SIZE_2:
            default: '1048576'
            description: Tablespace size of the performance cluster 2 database
            require: false
            type: string
            invisible: true
        PM_DATA_DB_SIZE_3:
            default: '1048576'
            description: Tablespace size of the performance cluster 3 database
            require: false
            type: string
            invisible: true
        PM_DATA_DB_SIZE_4:
            default: '1048576'
            description: Tablespace size of the performance cluster 4 database
            require: false
            type: string
            invisible: true
        PM_DATA_DB_SIZE_5:
            default: '1048576'
            description: Tablespace size of the performance cluster 5 database
            require: false
            type: string
            invisible: true
        PM_DATA_DB_SIZE_6:
            default: '1048576'
            description: Tablespace size of the performance cluster 6 database
            require: false
            type: string
            invisible: true
        topology_scale:
            description:  'Service memory specification'
            default: 'systemScale'
            range: 'systemScale,medium,large'
            type: enum
            require: false
        odrs_scale:
            description:  'Service memory specification'
            default: 'systemScale'
            range: 'systemScale,eSight_5k,eSight_2w,nce_super_large,MAE_4k,MAE_1w,DTE_medium,DTE_large'
            type: enum
            require: false
            invisible: true
        use_forwarded_ip:
            default: false
            description: Whether to use the forwarding IP address
            range: 'false,true'
            type: enum
            invisible: false
        LVS_VIP_Addr:
            default: ''
            description: LVS Load Balancing Configuration
            require: false
            type: string
            invisible: false
            pattern: '[A-Za-z0-9.,:\[\];]*'
        LVS_VIP_Addr_Ipv6:
            default: ''
            description: LVS IPv6 Load Balancing Configuration
            require: false
            type: string
            invisible: false
            pattern: '[A-Za-z0-9.,:\[\];]*'
    cluster:
        StandbyIPAddress:
            default: 127.0.0.1:32041
            description: IP address of the standby node on the O&M plane
            require: true
            type: string
            invisible: true
        floatIPAddress:
            default: 127.0.0.1:32041
            description: Floating IP address of the O&M plane
            require: true
            type: string
            invisible: true
        floatIPMask:
            default: 127.0.0.1:32041
            description: Mask for the floating IP address of the O&M plane.
            require: true
            type: string
            invisible: true
        floatNICAlias:
            default: 127.0.0.1:32041
            description: NIC for the floating IP address of the O&M plane
            require: true
            type: string
            invisible: true
    SA_WorkbenchService:
        CMP_NATS_NODE_LIST:
            default: 
            description: CMP NATS node IP address and port
            require: true
            type: string
            invisible: true
        CMP_ES_NODE_IP_LIST:
            default: 
            description: CMP ES node IP address 
            require: true
            type: string
            invisible: true
        CMP_CONTROLLER_LOCAL_NODE_IP:
            default: 
            description: CMP CONTROLLER node IP address
            require: true
            type: string
            invisible: true
        I2K_NODE_FLOAT_IP:
            default: 
            description: I2K node float IP address
            require: true
            type: string
            invisible: true
        DV_SHARE_USER_NAME:
            default: 
            description: DV share user name
            require: true
            type: string
            invisible: true
        DV_SHARE_USER_PASSWORD:
            default: 
            description: DV share password
            require: true
            type: string
            invisible: true
        CBS_LOGTRACE_PWD:
            default: 
            description: CBS etrace openapi password
            require: false
            type: string
            invisible: true
        DV_SHARE_PATH:
            description:  DV share path
            default: '/opt/oss/share/SOP/DVEngineeringService/dvshare'
            require: true
            type: string
            invisible: true
        CMP_ES_DATA_DIR:
            description:  CMP ES DATA path
            default: '/home/<USER>'
            require: true
            type: string
            invisible: true
        CMP_LOCAL_IP_LIST:
            default: 'cmpnodeip'
            description: CMP local node IP address,not need to config manually
            require: false
            type: string
            invisible: true
    OM_LogmatrixService:
        CMP_KERBEROS_NODE_IP_LIST:
            default: 
            description: CMP KERBEROS node IP address and port
            require: true
            type: string
            invisible: true
        CMP_KERBEROS_HOSTNAMES:
            default: 
            description: CMP KERBEROS HOSTNAME List
            require: true
            type: string
            invisible: true
        CMP_KAFKA_ZK_NODE_LIST:
            default: 
            description: CMP KAFKAZK node IP address and port
            require: true
            type: string
            invisible: true
        CMP_KAFKA_NODE_LIST:
            default: 
            description: CMP KAFKA node IP address and port
            require: true
            type: string
            invisible: true
        CMP_KAFKA_CLUSTER_SIZE:
            default: defalut
            description: CMP KAFKA Cluster SIZE large or default
            require: true
            type: string
            invisible: true
        CMP_KAFKA_PARTITION_NUM:
            default: 4
            description: CMP KAFKA Partition Number
            require: true
            type: string
            invisible: true
        DV_SHARE_USER_NAME:
            default: 
            description: DV share user name
            require: true
            type: string
            invisible: true
        DV_SHARE_USER_PASSWORD:
            default: 
            description: DV share password
            require: true
            type: string
            invisible: true
        DV_SHARE_PATH:
            description:  DV share path
            default: '/opt/oss/share/SOP/DVEngineeringService/dvshare'
            require: true
            type: string
            invisible: true
        CMP_KAFKA_DATA_DIR:
            description:  CMP KAFKA DATA path
            default: '/srv/BigData'
            require: true
            type: string
            invisible: true
        DVKafkaNetCard:
            description:  kafka net card usage
            default: 'maintenance'
            require: true
            type: string
            invisible: true
        CMP_ES_DATA_DIR:
            description:  CMP ES DATA path
            default: '/home/<USER>'
            require: true
            type: string
            invisible: true
    PMDashBoard:
        DV_SHARE_USER_NAME:
            default: 
            description: DV share user name
            require: true
            type: string
            invisible: true
        DV_SHARE_USER_PASSWORD:
            default: 
            description: DV share password
            require: true
            type: string
            invisible: true
        DV_SHARE_PATH:
            description:  DV share path
            default: '/opt/oss/share/SOP/DVEngineeringService/dvshare'
            require: true
            type: string
            invisible: true
        I2K_NODE_FLOAT_IP:
            default: 
            description: I2K node float IP address
            require: true
            type: string
            invisible: true
        enablePMServiceMode:
            default: TRUE
            description: is support DVPM or not  
            require: true
            type: string
            invisible: true
    SA_IhealingService:
        open_body_size:
            default: 'true'
            description: Release the size limit of remote notification mail to 10M
            require: true
            type: string
            invisible: true
    AnalysisEngine:
        AIOpsMenuConfig:
            default: 'faultAnalysis,capacityEvaluation,indicatorPrediction'
            description: IM Menu Configuration
            require: true
            type: string
            invisible: false
            pattern: '[a-zA-Z0-9,]{0,256}'
    HostSecurityConfigurationCheck:
        neHost:
            default: 'true'
            description: init network element rules
            require: true
            type: string
            invisible: true
            readonly: true
        AssetSyncMode:
            default: 'service'
            description: asset sync mode
            require: true
            type: string
            invisible: true
        MqClusterMode:
            default: 'WNMBS'
            description: mq cluster mode
            require: true
            type: string
            invisible: true
        EnableLimitCpu:
            default: 'true'
            description: Control whether to enable CPU restriction
            require: true
            type: string
            invisible: true
    SituationAwareness:
        productTypeForSec:
            default: 'DV'
            description: product type for sec
            require: true
            type: string
            invisible: true
packageFormat: SolutionV3
packageType: Install
resources:
    feature:
        Zenithdb:
            en_US: Zenithdb Feature
            zh_CN: Zenith数据库特性
        base:
            en_US: Base Feature
            zh_CN: 基础特性
        base_merge:
            en_US: Base Feature
            zh_CN: 基础特性
        sysMonitor:
            en_US: SysMonitor Feature
            zh_CN: 系统监控
        cluster:
            en_US: Cluster Feature
            zh_CN: 运维面多实例特性
        DVMaintenance:
            en_US: MML Service
            zh_CN: MML 服务
        OM_LogtraceService:
            en_US: OM_LogtraceService Feature
            zh_CN: 运维分析-服务跟踪
        OM_CallchainService:
            en_US: OM_CallchainService Feature
            zh_CN: 运维分析-调用链
        OM_CatService:
            en_US: OM_CatService Feature
            zh_CN: 运维分析-业务拨测
        OM_LogmatrixService:
            en_US: OM_LogmatrixService Feature
            zh_CN: 运维分析-日志服务
        SA_WorkbenchService:
            en_US: SA_WorkBenchService Feature
            zh_CN: 业务保障-作业控制台
        SA_IhealingService:
            en_US: SA_FlowOrchestration Feature
            zh_CN: 业务保障-流程编排
        SA_InfocollectService:
            en_US: SA_InfocollectService Feature
            zh_CN: 业务保障-故障信息收集
        PMDashBoard:
            en_US: PMDashBoard Feature
            zh_CN: 监控大盘
        DV4AService:
            en_US: 4A Access Service
            zh_CN: 4A对接服务
        DVCertService:
            en_US: NE Cert Management
            zh_CN: 网元证书管理
        DVSaaSOps:
            en_US: Cloud Monitor
            zh_CN: 云原生监控
        AnalysisEngine:
            en_US: AIOps Service
            zh_CN: 智能运维
        DVMediation:
            en_US: DV Southbound Independent Service
            zh_CN: 南向独立服务
        SituationAwareness:
            en_US: Security Situational Awareness Service
            zh_CN: 安全态势感知
        SOAR:
            en_US: SOAR
            zh_CN: 响应闭环
        DVPM:
            en_US: Performance Monitor Service
            zh_CN: 性能服务
        DVPM1:
            en_US: Performance Monitor Cluster Service1
            zh_CN: 性能监控集群1
        DVPM2:
            en_US: Performance Monitor Cluster Service2
            zh_CN: 性能监控集群2
        DVPM3:
            en_US: Performance Monitor Cluster Service3
            zh_CN: 性能监控集群3
        DVPM4:
            en_US: Performance Monitor Cluster Service4
            zh_CN: 性能监控集群4
        DVPM5:
            en_US: Performance Monitor Cluster Service5
            zh_CN: 性能监控集群5
        DVPM6:
            en_US: Performance Monitor Cluster Service6
            zh_CN: 性能监控集群6
        DVITManagement:
            en_US: Integrated Task Management
            zh_CN: 集中任务管理
        BasicSecurityOM:
            en_US: Basic Security O&M
            zh_CN: 安全运维基础服务
        HostSecurityConfigurationCheck:
            en_US: Host Security Configuration Check
            zh_CN: 主机安全配置核查
        DVIpModify:
            en_US: IP Modify Service
            zh_CN: IP修改
        DVNBI:
            en_US: Performance Northbound Interface
            zh_CN: 北向性能
        DVTopo:
            en_US: Topo Service
            zh_CN: 拓扑服务
        NBIService:
            en_US: Northbound SNMP alarms
            zh_CN: 北向SNMP告警
        DVMiddleware:
            en_US: Middleware Monitor
            zh_CN: 中间件监控
    featureGroup:
        all:
            en_US: Cluster
            zh_CN: 集群
        single:
            en_US: Multi-Node Single-Server
            zh_CN: 多节点单机
        merge:
            en_US: All-In-One
            zh_CN: 一体机
        MiniCluster:
            en_US: Merge Cluster
            zh_CN: 合设集群
        MiniCluster_lite:
            en_US: Merge Cluster(Mini Deploy)
            zh_CN: 合设集群（小型化部署）
        largeCapacity:
            en_US: Large Capacity
            zh_CN: 大容量
        single_tool:
            en_US: Multi-Node Single-Server(Only ToolKit)
            zh_CN: 多节点单机（仅工具）
        merge_tool:
            en_US: All-In-One(Only ToolKit)
            zh_CN: 一体机（仅工具）
        merge_docker:
            en_US: All-In-One(Docker Deploy)
            zh_CN: 一体机（容器化部署）
        merge_lite:
            en_US: All-In-One(Lite Deploy)
            zh_CN: 一体机（轻量化部署）
        merge_mini:
            en_US: All-In-One(Mini Deploy)
            zh_CN: 一体机（小型化部署）
        MiniCluster_docker:
            en_US: Merge Cluster(Docker Deploy)
            zh_CN: 合设集群（容器化部署）
    input:
        DV_SEC_RUNAS_USER:
            en_US: Security run as user
            zh_CN: 安全面运行用户
            description:
                en_US: The default installation scenario is secuser. In upgrade scenarios, set this parameter to ossuser. In upgrade scenarios, set this parameter to ossuser. In upgrade scenarios, set this parameter to ossuser. In upgrade scenarios, set this parameter to ossuser. In upgrade scenarios, set this parameter to ossuser.
                zh_CN: 安装场景默认是secuser。升级场景从无到有参数就设置为ossuser，如果升级从有到有就是继承升级前的值。
        serviceMessageBusRegisterMode: 
            en_US: service and message bus register mode
            zh_CN: 服务和消息总线注册模式
            description:
                en_US: The value range is [synchrono,asynchrono]. The default value is synchrono. Service and message bus register mode, which is only for OMC scenarios.
                zh_CN: 取值范围【synchrono,asynchrono】，默认synchrono。服务和消息总线注册模式，仅用于OMC场景。
        loadBalancingAndFaultTolerantMode: 
            en_US: load balancing and fault tolerant mode
            zh_CN: 负载均衡和容错模式
            description:
                en_US: The value range is [random,inTurn]. The default value is inTurn. Load balancing and fault tolerant mode, which is only for OMC scenarios.
                zh_CN: 取值范围【random,inTurn】，默认inTurn。负载均衡和容错模式，仅用于OMC场景。
        APIGatewayRegisterMode: 
            en_US: API gateway register mode
            zh_CN: API网关注册模式
            description:
                en_US: The value range is [synchrono,asynchrono]. The default value is synchrono. API gateway register mode, which is only for OMC scenarios.
                zh_CN: 取值范围【synchrono,asynchrono】，默认synchrono。API网关注册模式，仅用于OMC场景。
        SetingToolsMode: 
            en_US: setting tools mode
            zh_CN: 配置工具模式
            description:
                en_US: The value range is [synchrono,asynchrono]. The default value is synchrono. Setting tools mode, which is only for OMC scenarios.
                zh_CN: 取值范围【synchrono,asynchrono】，默认synchrono。配置工具模式，仅用于OMC场景。
        messageDrivingServieMaxMemory: 
            en_US: message driver servcie max memory
            zh_CN: 消息驱动服务内存配置
            description:
                en_US: Message driver servcie max memory, which is only for OMC scenarios. The default value is 100, unit for M.
                zh_CN: 消息驱动服务内存配置。默认值100，单位M，仅用于OMC场景。
        ServiceTracingMemory: 
            en_US: service trace memory
            zh_CN: 服务跟踪内存配置
            description:
                en_US: Service trace memory, which is only for OMC scenarios. The default value is 100, unit for M.
                zh_CN: 服务跟踪内存配置。默认值300，单位M，仅用于OMC场景。
        FTP_PASSWD: 
            en_US: Password of ftpuser for OMC only
            zh_CN: OMC场景的ftpuser用户的密码
            description:
                en_US: Password of ftpuser, which is only for OMC scenarios.
                zh_CN: OMC场景的ftpuser用户的密码，仅用于OMC场景。
        isLiteMonitor:
            en_US: Liting Monitor or not.LiteMonitor is used for enhancing OM(B mode)
            zh_CN: 是否启用轻量化监控用于增强高级运维能力（用于B方案）
            description:
                en_US: The value range is [false, true]. The default value is false, indicating the full system monitoring feature. This value is applicable to the DigitalView installation and modernized NMS A solution. true indicates the lightweight system monitoring feature, which is applicable to the lightweight DigitalView and modernized NMS B solution.This parameter does not take effect only in tool scenarios.
                zh_CN: 取值范围【false，true】，默认false，代表全量系统监控特性，适用于纯DigitalView安装和现代化网管A方案。true代表轻量化系统监控特性，适用于轻量化DigitalView和现代化网管B方案。该参数在仅工具场景不生效。
        i2k_interface_password_secure_mode:
            en_US: The machine interface password mode
            zh_CN: 机机接口密码模式
            description:
                en_US: The value range is [long_password, compatible]. The default value is long_password, indicating that the password mode for the interconnection between the OSS and agent is long password security mode. This value applies to new installation scenarios. compatible indicates the compatible mode, which is applicable to the proxy of the old version. To check the compatible mode, log in to the proxy and check the interface_password_secure_mode configuration item in $BMU_HOME/uninstall/bmu.cfg. If the configuration item does not exist, select the compatible mode.This parameter does not need to be modified in tool scenarios.
                zh_CN: 取值范围【long_password，compatible】，默认long_password，代表网管和代理对接的密码模式为长密码安全模式，适用于新安装场景；compatible代表兼容模式，适用于对接老版本的代理，查看方式：登录代理查看$BMU_HOME/uninstall/bmu.cfg的interface_password_secure_mode配置项，和该配置项保持一致，如果没有该配置项，需要选择为兼容模式。该参数在仅工具场景无需修改。
        GLOBAL_UNIFIED_OSS_USER_PASSWORD:
            en_US: oss none service os password, include ossuser、sftpossuser、sshossuser、devdata、sysomc
            zh_CN: OSS非服务化用户密码配置.配置已经存在的ossuser、sftpossuser、sshossuser、devdata、sysomc的密码
        GLOBAL_UNIFIED_OSS_DB_USER_PASSWORD:
            en_US: oss none service db user password, include OSSADMIN、OSSUSER、OMSSYS、OMSMODEL、IEMPEAM、OMSSM、OMSFM、OMSCMON、OMSPM、OMSPM_INDEX、OMSCM、OMUUSER、ICNFGAPPDB...
            zh_CN: OSS非服务化数据库用户密码配置.配置已经存在的OSSADMIN、OSSUSER、OMSSYS、OMSMODEL、IEMPEAM、OMSSM、OMSFM、OMSCMON、OMSPM、OMSPM_INDEX、OMSCM、OMUUSER、ICNFGAPPDB等的密码
        web_admin_user_name:
            en_US: system administrator name
            zh_CN: 系统管理员名称
        web_admin_user_value:
            en_US: system administrator password
            zh_CN: 系统管理员密码
            description:
                en_US: 1. The password must contain 8 to 32 characters. 2. The password must contain at least one uppercase letter, at least one lowercase letter, at least one digit and at least one special character ~@#^*-_+[{}]:./? . 3. The password cannot contain more than 2 consecutive identical characters. 4. The number of occurrences of the same character cannot exceed 3. 5. The password cannot contain the user name or the reverse of the user name, and is case insensitive. 6. The positive and reverse characters of the password cannot be the same and are case insensitive. 7. Weak passwords cannot be set as weak passwords. Weak passwords include system default passwords, passwords that have been disclosed in the past, etc.
                zh_CN: 1.密码必须是8～32个字符。2.密码必须包含如下四种字符的组合：至少一个大写字母、至少一个小写字母、至少一个数字、至少一个特殊字符~@#^*-_+[{}]:./?。3.密码不能包含超过2个连续的相同字符。4.同一字符出现的次数不能超过3次。5.密码不能包含用户名或者用户名的倒写，并且不区分大小写。6.密码的正写与密码的倒写不能相同，且不区分大小写。7.不支持设置为弱密码，弱密码包括系统默认的密码、过去曾被泄露的密码等。
        IS_INCREMENTUPGRADE:
            en_US: is increment upgrade or not 
            zh_CN: 是否增量升级
        AUTH_ENABLE_TWOFACTOR:
            en_US: Open two-factor authentication or not 
            zh_CN: 是否开启双因素认证
        IS_ENABLE_SECONDARY_AUTHENTICATION:
            en_US: Open secondary authentication or not 
            zh_CN: 是否开启二次认证
            description:
                en_US: The value range is [true, false]. The default value is true, indicating that enble using secondary authentication. Select false when the NMS is the SSO client and has no SMS or email notification capabilities.
                zh_CN: 取值范围【true, false】，默认true，代表启用二次认证能力。当网管作为SSO客户端，且没有短信、邮件通知能力时请选false。
        IS_ENABLE_REVIEW_ABILITY:
            en_US: Open review ability or not 
            zh_CN: 是否开启审核能力
            description:
                en_US: The value range is [true, false]. The default value is true, indicating that enble using review ability. The range of controllable functions are alarm customization, custom indicator of performance, workbench and ihealing.
                zh_CN: 取值范围【true, false】，默认true，代表启用审核能力。可控制功能范围为【告警定制，性能自定义指标，作业控制台，流程编排】。
        wideCharVerified:
            en_US: Support chinese or not 
            zh_CN: 是否支持中文用户名
        IS_INSTALL_ICNFG:
            en_US: is install icnfg or not 
            zh_CN: 是否安装icnfg
        is_support_express:
            en_US: is support express or not 
            zh_CN: 是否支持极速版
        DATA_MGMT_SERVICE_IP:
            en_US: IP address of the management plane.
            zh_CN: 管理面所在IP地址
        MGR_ZK_HOST_IP:
            en_US: IP address and port number of manager
            zh_CN: 管理面的IP地址及端口号
        CMP_ZK_NODE_LIST:
            en_US: cmp zk node IP and port
            zh_CN: cmp zk的ip与port信息列表
        dv_lite_mode:
            en_US: is use lite mode to deploy
            zh_CN: 轻量化部署标志参数
            description:
                en_US: The value range is [TRUE, FALSE], The default value is FALSE. In non-light scenarios, set this parameter to FALSE.
                zh_CN: 取值范围【TRUE, FALSE】，默认FALSE。非轻量化场景需要保持FALSE
        OMConsoleAccessAddress:
            en_US: Floating IP address of the O&M plane
            zh_CN: 配置的运维面的浮动IP地址
        IS_USE_WEB_TOPO:
            en_US: is use web topo
            zh_CN: 部署拓扑参数
        StandbyIPAddress:
            en_US: IP address of the standby node on the O&M plane
            zh_CN: 运维面备节点所在IP地址
        UNISSO_SERVICE_TRUSTED_IP:
            en_US: IP address or domain name trusted by UniSSO
            zh_CN: UniSSO信任的IP或者域名
        ZK_HOST_IP:
            en_US: IP address and port number of the node where ZooKeeper is deployed
            zh_CN: Zookeeper服务的IP地址及端口号
        ZK_LIST:
            en_US: List of IP addresses of the nodes where ZooKeeper is deployed
            zh_CN: Zookeeper所部署节点的IP列表
        enableNetworkElementLargeCapacity:
            en_US: Whether to control network element LargeCapacity
            zh_CN: 网元大容量开关
            description:
                en_US: The value range is [false, true]. The default value is false.Set this parameter to true when the number of DigitalView management hosts (including physical hosts and VMs) exceeds 200.
                zh_CN: 取值范围【false，true】，默认为false，当DigitalView管理主机（含物理主机和虚拟机）超过200台的场景时需要设置为true。
        floatIPAddress:
            en_US: Floating IP address of the O&M plane
            zh_CN: 运维面所在的浮动IP地址
        floatIPMask:
            en_US: Mask for the floating IP address of the O&M plane.
            zh_CN: 运维面所在浮动IP地址的掩码
        floatNICAlias:
            en_US: NIC for the floating IP address of the O&M plane
            zh_CN: 运维面所在浮动IP地址的网口信息
        globalBackendLBFloatIPAddr:
            en_US: Floating IP address of BackEndER
            zh_CN: 配置BackEndER的浮动IP
        regionAlias:
            en_US: region Alias
            zh_CN: 区域别名
        zenith_paramgroup_file:
            en_US: zenith db install paramgroup_file parameter
            zh_CN: zenith 数据库安装配置参数模板文件参数
        zenith_pm_paramgroup_file:
            en_US: pm zenith db install paramgroup_file parameter
            zh_CN: 性能 zenith 数据库分库安装配置参数模板文件参数
        PM_DATA_DB_SIZE:
            en_US: Tablespace size of the performance database
            zh_CN: 性能数据库表空间大小
        PM_DATA_DB_SIZE_1:
            en_US: Tablespace size of the performance cluster 1 database
            zh_CN: 性能集群1数据库表空间大小
        PM_DATA_DB_SIZE_2:
            en_US: Tablespace size of the performance cluster 2 database
            zh_CN: 性能集群2数据库表空间大小
        PM_DATA_DB_SIZE_3:
            en_US: Tablespace size of the performance cluster 3 database
            zh_CN: 性能集群3数据库表空间大小
        PM_DATA_DB_SIZE_4:
            en_US: Tablespace size of the performance cluster 4 database
            zh_CN: 性能集群4数据库表空间大小
        PM_DATA_DB_SIZE_5:
            en_US: Tablespace size of the performance cluster 5 database
            zh_CN: 性能集群5数据库表空间大小
        PM_DATA_DB_SIZE_6:
            en_US: Tablespace size of the performance cluster 6 database
            zh_CN: 性能集群6数据库表空间大小
        resourceScene:
            en_US: Resource deployment mode
            zh_CN: 配置资源部署模式
        secondaryAuthForceEnable:
            en_US: Whether to forcibly enable secondary authorization
            zh_CN: 是否强制开启二次授权
        secondaryAuthSupportRole:
            en_US: Whether secondary authorization can be configured for roles
            zh_CN: 是否支持角色配置二次授权
        tenantName:
            en_US: tenantName For DBM
            zh_CN: DBM名称
        ER_LISTEN_EXTERNAL_IP:
            en_US: Floating IP address that ERService listened
            zh_CN: ERService浮动ip监听配置
        CHECK_SESSION_IP:
            en_US: Verifying the Client IP Address
            zh_CN: 校验客户端IP开关
        BER_LISTEN_EXTERNAL_IP:
            en_US: Floating IP address that BackendERService listened
            zh_CN: BackendERService浮动ip监听配置
        ES_NODE_IP_LIST:
            en_US: IP list of elasticsearch sevice nodes
            zh_CN: Elasticsearch 安装的节点，DV资源管理安装节点
        ES_PORT:
            en_US: Port of elasticsearch sevice 
            zh_CN: Elasticsearch 服务端口
        INFOCENTER_MED_ROOT:
            en_US: Path of INFOCENTER_MED
            zh_CN: 信息中心路径
        DV_SFTP_USER:
            en_US: DV sftp user
            zh_CN: DV sftp 用户名
        DV_SFTP_PASSWORD:
            en_US: DV sftp password
            zh_CN: DV sftp 密码
        paasinter_pwd:
            en_US: DF access DV password
            zh_CN: DF对接UTM的密码
        I2kNfv_pwd:
            en_US: NFV access I2000 password
            zh_CN: NFV对接I2000的密码
        cie_snmp_pwd:
            en_US: CIE SNMP password
            zh_CN: CIE SNMP密码
        sftp_admin_pwd:
            en_US: sftp admin password
            zh_CN: sftp admin密码
        sftp_nbiuser_pwd:
            en_US: sftp nbiuser password
            zh_CN: sftp nbiuser密码
        sftp_mmluser_pwd:
            en_US: sftp mmluser password
            zh_CN: sftp mmluser密码
        sftp_assetuser_pwd:
            en_US: sftp assetuser password
            zh_CN: sftp assetuser密码
        dms_db_pwd:
            en_US: dms db password
            zh_CN: dms db 密码
        easyms_db_pwd:
            en_US: easyms db password
            zh_CN: easyms db 密码
        csweb_db_pwd:
            en_US: csweb db password
            zh_CN: csweb db 密码
        pmdashboard_db_pwd:
            en_US: pmdashboard db password
            zh_CN: pmdashboard db 密码
        cs_service_pwd:
            en_US: cs service pwd
            zh_CN: cs service 密码
        DV_TOOLKIT_INSTALL:
            en_US: DV TOOLKIT INSTALL flag
            zh_CN: DV TOOLKIT INSTALL 标志
        DV_CERT_DRIVER_pwd:
            en_US: Uniagent access certificate-configuration service password
            zh_CN: 代理对接网管证书、配置服务密码
        tool_mainast_pwd:
            en_US: mainast access DV password
            zh_CN: 维护工具对接DV密码
        tool_ideploy_pwd:
            en_US: ideploy access DV password
            zh_CN: ideploy对接DV密码
        tool_dms_pwd:
            en_US: dms access DV password
            zh_CN: dms对接DV密码
        tool_das_pwd:
            en_US: das access DV password
            zh_CN: das对接DV密码
        tool_easyms_pwd:
            en_US: easyms access DV password
            zh_CN: easyms对接DV密码
        DV_SFTP_PORT:
            en_US: DV sftp port
            zh_CN: DV sftp 端口
        NFV_MODE:
            en_US: Is nfv mode or not
            zh_CN: 是否是NFV模式
            description:
                en_US: The value range is [OFF, ON]. The default value is OFF. Set this parameter to ON only in Carriers in China NFV scenarios.This parameter does not need to be modified in tool scenarios.
                zh_CN: 取值范围【OFF，ON】，默认为OFF，仅在中国运营商NFV场景需要设置为ON。该参数在仅工具场景无需修改。
        omcversion:
            en_US: Set OMC Version
            zh_CN: 配置OMC Version的值
            description:
                en_US: Configure the OMC version filed, The default value does not need to be changed.
                zh_CN: 配置OMC的版本字段，默认不需要修改。
        IMAPBUS_SERVERNAME:
            en_US: The ip of bus service
            zh_CN: BusService服务的IP地址
        LIC_SnmpAgentService:
            en_US: License control SnmpAgentService
            zh_CN: SnmpAgentService服务License控制项标识
        CMP_NATS_NODE_LIST:
            en_US: cmp NATS node IP list and port
            zh_CN: cmp NATS的ip与port信息列表
        CMP_ES_NODE_IP_LIST:
            en_US: cmp ES node IP list
            zh_CN: cmp ES的ip信息列表
        CMP_CONTROLLER_LOCAL_NODE_IP:
            en_US: cmp controller node IP
            zh_CN: cmp controller本地的ip信息
        I2K_NODE_FLOAT_IP:
            en_US: i2k float IP address
            zh_CN: i2k的浮动ip信息
        CMP_KAFKA_NODE_LIST:
            en_US: cmp kafka node IP list and port
            zh_CN: cmp kafka的ip与port信息列表
        CMP_KAFKA_ZK_NODE_LIST :
            en_US: cmp kafka-zk node IP list and port
            zh_CN: cmp kafka-zk的ip与port信息列表
        CMP_KERBEROS_NODE_IP_LIST:
            en_US: cmp kerberos node IP list
            zh_CN: cmp kerberos的ip信息列表
        CMP_KERBEROS_HOSTNAMES:
            en_US: cmp kerberos node hostname list
            zh_CN: cmp kerberos的主机名信息列表
        CMP_KAFKA_CLUSTER_SIZE:
            en_US: cmp kafka cluster size
            zh_CN: cmp kafka的规模
        CMP_KAFKA_PARTITION_NUM:
            en_US: cmp kafka partition number
            zh_CN: cmp kafka分区数
        DV_SHARE_USER_NAME:
            en_US: DV share user name
            zh_CN: DV 共享目录用户名
        DV_SHARE_USER_PASSWORD:
            en_US: DV share user password
            zh_CN: DV 共享目录密码
        CBS_LOGTRACE_PWD:
            en_US: CBS etrace openapi password
            zh_CN: CBS etrace 北向接口用户密码
        upgrade_pmdashboarddb_pwd:
            en_US: upgrade pmdashboarddb password
            zh_CN: 监控大盘密码
        tool_scp_pwd:
            en_US: Certificate management password
            zh_CN: 证书管理密码
        dv_sso_whitelist:
            en_US: SSO whitelist
            zh_CN: SSO白名单
        DV_SHARE_PATH:
            en_US: DV share path
            zh_CN: DV 共享目录路径
        FMDeployMode:
            en_US: FM Deploy Mode
            zh_CN: 告警部署模式
        NetworkType:
            en_US: DV Network Type
            zh_CN: DV 组网类型
        isAppNbiIpExtend:
            en_US: Whether extension ip for north interface
            zh_CN: 是否扩展北向接口IP
        APP_EXTERNAL_NBI_IPV4:
            en_US: north interface extension ip 
            zh_CN: 扩展北向接口IP
        isAppNfvIpExtend:
            en_US: Whether to configure app ip extension for nfv interface
            zh_CN: 是否扩展NFV接口IP
        nfv_Extend_IP:
            en_US: app ip extension for nfv interface
            zh_CN: 扩展NFV接口IP
        CMP_ROOT_KEY_FACTORY_FIRST:
            en_US: cmp root key factory first
            zh_CN: cmp根秘钥因子一
        CMP_ROOT_KEY_FACTORY_SECOND:
            en_US: cmp root key factory second
            zh_CN: cmp根秘钥因子二
        CMP_WORK_KEY_FACTORY:
            en_US: cmp work key factory
            zh_CN: cmp工作秘钥因子
        CMP_HMAC_SHARE_KEY:
            en_US: cmp hmac share key
            zh_CN: hmacsha256一致性校验算法秘钥
        CMP_AUTH_SALT:
            en_US: cmp auth salt
            zh_CN: pbkdf2算法盐值
        CMP_AUTH_PASS:
            en_US: cmp auth pass
            zh_CN: zk nats认证密码
        MED_ACCESS_KEY:
            en_US: med access key
            zh_CN: MED认证密钥对，用于高级运维和UniAgent向MED上报信息时的鉴权ACCESS KEY
        MED_SECRET_KEY:
            en_US: med secret key
            zh_CN: MED认证密钥对，用于高级运维和UniAgent向MED上报信息时的鉴权SECRET KEY
        UTM_ACCESS_KEY:
            en_US: utm access key
            zh_CN: UTM AKSK认证ACCESS KEY
        UTM_SECRET_KEY:
            en_US: utm secret key
            zh_CN: UTM AKSK认证SECRET KEY
        CurrentAlarmCapacity:
            en_US: Current Alarm Capacity
            zh_CN: 当前告警容量
            description:
                en_US: Value range is [Default,50000,100000,200000,300000,500000,1000000]. The default value is Default, indicating that the maximum number of current alarms is 20000. The value 1000000 indicates that the maximum number of current alarms is 1000000. The memory of the AppNode needs to be increased.This parameter does not take effect only in tool scenarios.
                zh_CN: 告警范围仅支持7个值[Default,50000,100000,200000,300000,500000,1000000]，默认Default，代表当前告警容量上限20000，如果取值为1000000则代表当前告警容量上限为最高的1000000，提高参数的同时需要定制化提升appnode节点内存。该参数在仅工具场景不生效。
        enableLuceneQueryForHistoryAlarm:
            en_US: Enable Lucene query for historical alarms
            zh_CN: 历史告警查询是否启动Lucene查询
            description:
                en_US: "Whether to enable Lucene query for historical alarms. Options: true: Enable. false: Disable."
                zh_CN: 历史告警查询是否启动Lucene查询。取值范围：true：开启。false：关闭。
        enableMemSpeed:
            en_US: enable Alarm Mem Speed
            zh_CN: 启用告警内存提速
        CMP_KAFKA_DATA_DIR:
            en_US: CMP KAFKA DATA DIR
            zh_CN: CMP kafka 数据存放路径
        DVKafkaNetCard:
            en_US: kafka net card usage
            zh_CN: kafka网口用途
        CMP_ES_DATA_DIR:
            en_US: CMP ES DATA DIR
            zh_CN: CMP ES 数据存放路径
        CMP_LOCAL_IP_LIST:
            en_US: CMP LOCAL IP List
            zh_CN: CMP 运维面IP
            description:
                en_US: IP List of CMP nodes.
                zh_CN: 高级运维节点IP列表。
        Auth.UniSession.Auth.Mode:
            en_US: Authentication Mode
            zh_CN: 认证模式
            description:
                en_US: Authentication Mode, the value range is [thirdpart, null]. The default value is null. If the authentication is third part, please choose thirdpart.
                zh_CN: 认证模式，取值范围【thirdpart，空】，默认为空。如果是第三方认证，请选择thirdpart。
        AUTH:
            en_US: DigitalView SSO Customization
            zh_CN: DigitalView SSO定制
            description:
                en_US: DigitalView SSO Customization. The default value is null.
                zh_CN: DigitalView SSO定制，默认为空。
        Auth.UniSession.Session.Key:
            en_US: Session cookie customization
            zh_CN: 定制会话Cookie
            description:
                en_US: Session cookie customization, the value range is [bspsession, null]. The default value is null. The key value of session cookie will be bspsession, when choose bspsession.
                zh_CN: 定制会话Cookie，取值范围【bspsession，空】，默认为空。如果选择bspsession，则会话cookie的key值为bspsession。
        ssoServerUrl:
            en_US: Third-party SSO server
            zh_CN: 第三方SSO服务器 URL
            description:
                en_US: Third-party SSO server. The default value is null. Non-third-party SSO server does not start.
                zh_CN: 第三方SSO服务器 URL，默认为空，非第三方SSO服务器不启用。
        ssoType:
            en_US: SSO type
            zh_CN: 单点登录类型
            description:
                en_US: The value range is [DEFAULT, VENUS,CBS_CAS] . The default value is DEFAULT.
                zh_CN: 取值范围【DEFAULT, VENUS, CBS_CAS】，默认DEFAULT。
        dv_deploy_scale_size:
            en_US: Network manager deploy scale size
            zh_CN: 网管部署规模
            description:
                en_US: The value range is [default, small, venus] . The default value is default. In mini mode and lite mode set this parameter to small.In Venus set this parameter to venus. In other scenarios, retain the default value.
                zh_CN: 取值范围【default, small, venus】，默认default。在小型化和轻量化场景下需要配置为small，在Venus场景下选venus，在其他场景中均保持default默认
        Kafka_Retention_Time:
            en_US: Kafka Retention Time
            zh_CN: kafka保留时间
            description:
                en_US: The value range is [Default, Short], Performance kafka retention time, default is Default. Manually modify this parameter. The modification takes effect only after the upgrade.
                zh_CN: 取值范围【Default, Short】, 性能kafka保留时间，默认是Default。手工修改此参数，需要升级才生效。
        detailed_log_printing_mode:
            en_US: Detailed Log Printing Mode
            zh_CN: 日志详细打印模式
            description:
                en_US: The value range is [No, Yes] . The default value is No. To enable the detailed log printing mode, select Yes.
                zh_CN: 取值范围【No, Yes】，默认No。如果要开启日志详细打印模式，需要选择Yes
        enableSwitchBack:
            en_US: Two-Node Cluster Switchover Secondary Development Script Execution Failure Whether to Perform Switchback
            zh_CN: 双机切换二次开发脚本执行失败是否回切
            description:
                en_US: The value range is [Yes, No] . The default value is Yes. If the secondary development script of the two-node cluster switchover fails to be executed, select Yes. If the switchback is not required, select No.
                zh_CN: 取值范围【Yes, No】，默认Yes。如果双机切换二次开发脚本执行失败需要回切，选择Yes，如果不需要回切，选择No
        is_extend_internal_certificate_validity_period:
            en_US: is extend internal certificate validity period
            zh_CN: 是否延长内部证书有效期
            description:
                en_US: The value range is [False, True] . The default value is False. If the internal certificate validity period is extended, select True, otherwise select False
                zh_CN: 取值范围【False,True】，默认False。如果延长内部证书有效期，选择True,否则选择False
        custom_path_list:
            en_US: Custom Path List
            zh_CN: 自定义安装路径信息
            description:
                en_US: Custom path list of /opt, /home, /tmp
                zh_CN: /opt, /home, /tmp的自定义安装路径信息
        EMSMode:
            en_US: EMS Mode
            zh_CN: 集中运维模式角色
            description:
                en_US: "The value range is [Central,Local,NA]. The default value is NA.Note: Central: central O&M system; Local: local O&M system; NA: default O&M mode. If this parameter is set to Central, the value of UUID generated by the DigitalView is fixed to 1." 
                zh_CN: 取值范围【Central, Local, NA】，默认NA。说明：Central:中心运维系统；Local:本地运维系统；NA:默认运维模式。设置为Central时，本DV生成的UUID值固定为1。
        ssoBackupServerUrl:
            en_US: Third-party BackUp SSO server
            zh_CN: 第三方SSO备份服务器 URL
            description:
                en_US: Third-party BackUp SSO server. The default value is null. Non-third-party SSO server does not start.
                zh_CN: 第三方SSO备份服务器 URL，默认为空，非第三方SSO服务器不启用。
        approveMaxClientBodySize:
            en_US: Whether to enable max clent body size
            zh_CN: 是否启用大请求体
            description:
                en_US: The value range is [false, true]. The default value is true. The max client body size is 2g, when choose true. It's 2m when choose false.
                zh_CN: 取值范围【false，true】，默认true。如果为true，最大请求体大小为2g；如果为false，则默认为2m。
        enableSaasMode:
            en_US: Whether to enable multi-tenancy
            zh_CN: 是否开启运维多租
            description:
                en_US: The value range is [false, true]. The default value is false. Indicates whether to enable multi-tenancy.
                zh_CN: 取值范围【false，true】，默认false，是否开启运维多租。
        enableAccountNonRootMode:
            en_US: Specifies whether to enable account management in non-root mode,this mode requires the support of all NEs managed by the DigitalView.Otherwise, the password change function will be unavailable.
            zh_CN: 是否启用帐号管理支持非root模式，该模式需要DigitalView管理的所有网元都支持，否则密码修改功能不可用。
            description:
                en_US: The value range is [false, true]. The default value is false. Indicates whether to enable multi-tenancy.
                zh_CN: 取值范围【false，true】，默认false，是否开启帐号管理支持非root模式。
        Auth.DomainCustomeConfig:
            en_US: Domain-based Mode Customization
            zh_CN: 分域模式定制
            description:
                en_US: The value range is ['ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP','DISABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP', 'ENABLE_ALL_RES,ENABLE_DEV_TYPE,ENABLE_SUBNET,ENABLE_DEV_SETS,ENABLE_DEV,DISABLE_RESGROUP,DISABLE_ALL_MNA_OBJ']. The default value is 'ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP'.
                zh_CN: 取值范围【'ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP', 'DISABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP','ENABLE_ALL_RES,ENABLE_DEV_TYPE,ENABLE_SUBNET,ENABLE_DEV_SETS,ENABLE_DEV,DISABLE_RESGROUP,DISABLE_ALL_MNA_OBJ'】，默认ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP。
        smEnablePolicyAuth:
            en_US: Policy-based Authentication Capability Switch
            zh_CN: 基于策略的鉴权能力开关
            description:
                en_US: The value range is [false, true]. The default value is false. If this function is enabled, ensure that Auth.DomainCustomeConfig is set to 'ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP'.
                zh_CN: 取值范围【false，true】，默认false，如果开启，需要保证Auth.DomainCustomeConfig的参数设置成'ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP'。
        open_body_size:
            en_US: Release the size limit of remote notification mail to 10M
            zh_CN: 放开远程通知邮件大小至10M
            description:
                en_US: Release the size limit of remote notification mail to 10M
                zh_CN: 放开远程通知邮件大小至10M
        AIOpsMenuConfig:
            en_US: IM Menu Configuration
            zh_CN: 智能运维菜单配置
            description:
                en_US: Value range is [faultAnalysis,capacityEvaluation,disasterRecovery,indicatorPrediction,tidalScheduling]. faultAnalysis indicates Fault Detection And Analysis, capacityEvaluation indicates Intelligent Capacity Evaluation, disasterRecovery indicates Intelligent Disaster Recovery, and indicatorPrediction indicates Indicator Trend Prediction, tidalScheduling indicates Tidal Scheduling. Intelligent Disaster Recovery depends on the Fault Detection And Analysis, After intelligent disaster recovery is configured, Fault Detection And Analysis is enabled by default.Tidal scheduling depends on the Indicator Trend Prediction, After tidal scheduling is configured, Indicator Trend Prediction is enabled by default.
                zh_CN: 取值范围【faultAnalysis,capacityEvaluation,disasterRecovery,indicatorPrediction,tidalScheduling】。说明：faultAnalysis表示故障检测分析，capacityEvaluation表示智能容量评估，disasterRecovery表示智能容灾，indicatorPrediction表示指标趋势预测，tidalScheduling表示潮汐调度。智能容灾依赖故障检测分析能力，配置智能容灾后会默认启用故障检测分析。潮汐调度依赖指标趋势预测能力，配置潮汐调度后会默认启用指标趋势预测。
        HOFS_STORE:
            en_US: hofs store dir
            zh_CN: hofs 存储路径
        HOFS_ROOT_DIR:
            en_US: hofs root dir
            zh_CN: HOFS本地文件目录
        HOFS_FUSE_HOME:
            en_US: hofs fuse dir
            zh_CN: HOFS FUSE根目录
        need_drbd:
            en_US: whether use drbd or not
            zh_CN: 是否使用drbd
        IS_ENABLE_CN_AND_EN_AUDITLOGS:
            en_US: Whether to record the security log in both Chinese and English
            zh_CN: 同时记录安全日志的中英文的开关
            description:
                en_US: The value range is [false, true]. The default value is true. Whether to record the security log in both Chinese and English. If no need this, please choose false.
                zh_CN: 取值范围【false，true】，默认true，是否同时记录安全日志的中英文。如果不需要，请选择false。
        IS_SEND_MQ_AUDITLOG:
            en_US: Whether to report to kafka
            zh_CN: 上报到kafka的开关
            description:
                en_US: The value range is [false, true]. The default value is true. Whether to report to kafka. If no need to report to kafka, please choose false.
                zh_CN: 取值范围【false，true】，默认true，是否上报到kafka开关。如果不希望上报到kafka，请选择false。
        integrated_mode:
            en_US: integrated mode of the DigitalFoundry, DigitalView, and service hosts
            zh_CN: DF、DV和业务主机合设模式
            description:
                en_US: The options are as follows【DigitalFoundry-service co-deployment,DigitalFoundry-DigitalView-service co-deployment and DigitalFoundry-DigitalView-service separate deployment】. By default, DigitalFoundry-DigitalView-service separate deployment， If the DigitalFoundry and services are co-deployed, select DigitalFoundry-service co-deployment. If the DigitalFoundry, DigitalView, and services are co-deployed, select DigitalFoundry-DigitalView-service co-deployment. Co-deployment indicates that the DigitalFoundry, DigitalView, services use the same operating system.
                zh_CN: 取值范围【DF和业务主机合设,DF、DV和业务主机合设,DF、DV、业务主机分设】，默认DF、DV、业务主机分设。当DF和业务存在主机合设的时候，请选择“DF和业务主机合设”，DF、DV和业务存在主机合设时，请选择“DF、DV和业务主机合设”，主机合设的含义是共用同一个操作系统
        enableUOAConnection:
            en_US: Whether to enable the connection to the UOA management ne
            zh_CN: 是否支持连接UOA管理网元
            description:
                en_US: The options are as follows [false, true]. By default, false. When need the connection to the UOA management ne, please choose true.
                zh_CN: 取值范围【false, true】，默认false。当需要支持连接UOA管理网元时，请选择true。
        use_forwarded_ip:
            en_US: Whether to use the forwarding IP address
            zh_CN: 是否使用转发IP为客户端IP
            description:
                en_US: Need to set it TRUE when accesses DigitalView through a proxy. Then X-Forwarded-For is used to obtain the client IP address.
                zh_CN: 前端通过代理访问DV时，需要设置为TRUE，开启后使用X-Forwarded-For获取客户端IP。
        LVS_VIP_Addr:
            en_US: Configuring LVS and VIP Forwarding
            zh_CN: 配置LVS和VIP转发
            description:
                en_US: Configuring IP Forwarding Parameters for LVS and VIP.
                zh_CN: 配置负载均衡lvs和vip的IP转发参数。
        LVS_VIP_Addr_Ipv6:
            en_US: Configuring IPv6 Network Forwarding for LVS and VIPs
            zh_CN: 配置LVS和VIP的IPV6网络转发
            description:
                en_US: Configuring IPv6 Forwarding Parameters for LVS and VIPs.
                zh_CN: 配置负载均衡lvs和vip的IPV6转发参数。
        enablePMServiceMode:
            en_US: Whether to enable Performance Monitor Cluster Service
            zh_CN: 是否开启性能监控集群服务能力
            description:
                en_US: The value range is [false, true]. The default value is false. Indicates whether to Performance Monitor Cluster Service.
                zh_CN: 取值范围【false，true】，默认false，是否开启性能监控集群服务能力。
        enableNBIServiceMode:
            en_US: Whether to enable DVNBI Service
            zh_CN: 是否开启北向服务化
            description:
                en_US: The value range is [FALSE, TRUE]. The default value is FALSE. Indicates whether to DVNBI Service.
                zh_CN: 取值范围【FALSE，TRUE】，默认FALSE，是否开启北向服务化。
        EXTEND_TYPE:
            en_US: DVPM EXTEND TYPE
            zh_CN: 性能服务扩容类型
        ER_TOTAL_LIMIT_CONN:
            en_US: Total number of concurrent ER connections
            zh_CN: 控制ER并发连接总数
            description:
                en_US: The value range is [2, 30000]. The default value is 1000. Total number of concurrent ER connections
                zh_CN: 取值范围【2, 30000】，默认值为1000，控制ER并发连接总数
        LIMIT_CONN_PERIP:
            en_US: Controls the number of concurrent connections to the same ER address
            zh_CN: 控制ER同一地址的并发连接数
            description:
                en_US: The value range is [2, 30000]. The default value is 300.Controls the number of concurrent connections to the same ER address.
                zh_CN: 取值范围【2, 30000】，默认值为300，控制ER同一地址的并发连接数
        topology_scale:
            en_US: Topology service memory specification
            zh_CN: 拓扑服务内存规格
            description:
                en_US: The value range is [systemScale，medium，large]. 
                zh_CN: 取值范围【systemScale，medium，large】.
        odrs_scale:
            en_US: DTENEManagerService Service memory specification
            zh_CN: 网元管理服务内存规格
            description:
                en_US: The value range is [systemScale,eSight_5k,eSight_2w,nce_super_large,MAE_4k,MAE_1w,DTE_medium,DTE_large]. 
                zh_CN: 取值范围【systemScale,eSight_5k,eSight_2w,nce_super_large,MAE_4k,MAE_1w,DTE_medium,DTE_large】.
        dv_site_info_list:
            en_US: DV site information list.
            zh_CN: DV站点信息列表
            description:
                en_US: Used to display DV information in the service topology. Configure information about all DigitalView production and DR sites, including the DigitalView floating IP address and the corresponding site ID. The site ID must be the same as the site ID allocated by the product deployment to the DigitalView. If the site ID is incorrectly configured, As a result, the DigitalView node information may not be displayed in the topology or the display may be abnormal. For details about the parameter example, see cbs:1:dv_floatIP1;2:dv_floatIP2. If it is a scenario where multiple solutions are jointly managed, use | to split, for example, cbs:1:dv_floatIP1;2:dv_floatIP2|bes:a:dv_floatIP1;b:dv_floatIP2. Site ID. To obtain the site ID, contact the product deployment personnel.
                zh_CN: 用于在业务拓扑上展示DV相关信息。配置DV容灾站点信息，包含DV所有生产和容灾站点信息，内容包含DV浮动IP和对应的站点ID，其中站点ID必须要与产品部署分配给DV的站点ID保持一致，如果该站点ID配置错误，则可能会导致在拓扑上无法看到DV节点信息，或者展示异常。参数样例参考:cbs:1:dv_floatIP1;2:dv_floatIP2. 如果是多个解决方案共管的场景，以|来分隔配置，样例参考:cbs:1:dv_floatIP1;2:dv_floatIP2|bes:a:dv_floatIP1;b:dv_floatIP2.站点ID获取方式：找产品部署人员提供。
    packageType:
        Install:
            en_US: Install Package
            zh_CN: 安装包
    softwareName:
        en_US: CloudOpera IES Orchestrator Solution
        zh_CN: CloudOpera IES Orchestrator解决方案
softwareName: DigitalView
softwareVersion: V800R022C30B010
mainVersion: V800R022C30B010
productType: DigitalView
releaseType: Full