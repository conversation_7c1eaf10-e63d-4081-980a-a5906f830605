package com.huawei.i2000.dvtopowebsite.client.service;

import java.util.*;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.huawei.i2000.dvtopowebsite.client.model.ResponseEntity;
import com.huawei.i2000.dvtopowebsite.client.model.DeleteLinkModel;
import com.huawei.i2000.dvtopowebsite.client.model.DeleteNodeModel;
import com.huawei.i2000.dvtopowebsite.client.model.LinkCondition;
import com.huawei.i2000.dvtopowebsite.client.model.NodeCondition;
import com.huawei.i2000.dvtopowebsite.client.model.UpdateMoModel;
import com.huawei.i2000.dvtopowebsite.util.RestClientUtil;
import com.huawei.bsp.remoteservice.exception.ExceptionArgs;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.clientsdk.HttpUtil;
import com.huawei.bsp.roa.util.clientsdk.JsonUtil;
import com.huawei.bsp.roa.util.restclient.Restful;
import com.huawei.bsp.roa.util.restclient.RestfulAsyncCallback;
import com.huawei.bsp.roa.util.restclient.RestfulFactory;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import java.util.Map;
import java.util.HashMap;

public class TopoWebsiteService {
    private RestfulOptions option = null;
    private Map<String, String> contextHeader = new HashMap<String, String>(1);
    private Restful restFull = null;
    
    public TopoWebsiteService() {
    
    }
    
        
    public TopoWebsiteService(Restful restFullout) {
        this.restFull = restFullout;
    }

  
    /**
     * 查询当前用户是否是admin
     * 查询当前用户是否是admin
     * @return ResponseEntity
     */
    public ResponseEntity isAdmin() throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/admin";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "isAdmin"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询当前用户是否是admin
     * 查询当前用户是否是admin
     * @param callback async callback object
     */
    public void asyncisAdmin(RestfulAsyncCallback callback) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/admin";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * 查询网元上下层级的链接
     * 查询网元上下层级的链接
     * @param solutionDn 网元的dn值
     * @param level 网元的dn值
     * @param open 是否展开
     * @param clickDn 网元的dn值
     * @param lastOpenDn 网元的dn值
     * @return ResponseEntity
     */
    public ResponseEntity queryLinks(String solutionDn, String level, Integer open, String clickDn, String lastOpenDn) throws ServiceException {
        
    
        // verify the required parameter 'solutionDn' is set
        if (solutionDn == null) {
            throw new ServiceException("Missing the required parameter 'solutionDn' when calling queryLinks", 400);
        }
    
        // verify the required parameter 'level' is set
        if (level == null) {
            throw new ServiceException("Missing the required parameter 'level' when calling queryLinks", 400);
        }
    
        // verify the required parameter 'open' is set
        if (open == null) {
            throw new ServiceException("Missing the required parameter 'open' when calling queryLinks", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/cross/links";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (solutionDn != null) {
            path = RestClientUtil.parameterToString(path, "", "solutionDn", solutionDn);
        }
        if (clickDn != null) {
            path = RestClientUtil.parameterToString(path, "", "clickDn", clickDn);
        }
        if (lastOpenDn != null) {
            path = RestClientUtil.parameterToString(path, "", "lastOpenDn", lastOpenDn);
        }
        if (level != null) {
            path = RestClientUtil.parameterToString(path, "", "level", level);
        }
        if (open != null) {
            path = RestClientUtil.parameterToString(path, "", "open", open);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "queryLinks"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询网元上下层级的链接
     * 查询网元上下层级的链接
     * @param solutionDn 网元的dn值
     * @param level 网元的dn值
     * @param open 是否展开
     * @param clickDn 网元的dn值
     * @param lastOpenDn 网元的dn值
     * @param callback async callback object
     */
    public void asyncqueryLinks(String solutionDn, String level, Integer open, String clickDn, String lastOpenDn, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // verify the required parameter 'solutionDn' is set
        if (solutionDn == null) {
            throw new ServiceException("Missing the required parameter 'solutionDn' when calling queryLinks", 400);
        }
    
        // verify the required parameter 'level' is set
        if (level == null) {
            throw new ServiceException("Missing the required parameter 'level' when calling queryLinks", 400);
        }
    
        // verify the required parameter 'open' is set
        if (open == null) {
            throw new ServiceException("Missing the required parameter 'open' when calling queryLinks", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/cross/links";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (solutionDn != null) {
            path = RestClientUtil.parameterToString(path, "", "solutionDn", solutionDn);
        }
        if (clickDn != null) {
            path = RestClientUtil.parameterToString(path, "", "clickDn", clickDn);
        }
        if (lastOpenDn != null) {
            path = RestClientUtil.parameterToString(path, "", "lastOpenDn", lastOpenDn);
        }
        if (level != null) {
            path = RestClientUtil.parameterToString(path, "", "level", level);
        }
        if (open != null) {
            path = RestClientUtil.parameterToString(path, "", "open", open);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * 是否能够自动保存网元位置
     * 是否能够自动保存网元位置
     * @param solutionDn 网元的dn值
     * @param level 网元的dn值
     * @param masterView 是否是主视图
     * @param clickDn 网元的dn值
     * @return ResponseEntity
     */
    public ResponseEntity queryCrossData(String solutionDn, String level, Integer masterView, String clickDn) throws ServiceException {
        
    
        // verify the required parameter 'solutionDn' is set
        if (solutionDn == null) {
            throw new ServiceException("Missing the required parameter 'solutionDn' when calling queryCrossData", 400);
        }
    
        // verify the required parameter 'level' is set
        if (level == null) {
            throw new ServiceException("Missing the required parameter 'level' when calling queryCrossData", 400);
        }
    
        // verify the required parameter 'masterView' is set
        if (masterView == null) {
            throw new ServiceException("Missing the required parameter 'masterView' when calling queryCrossData", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/cross/solution";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (solutionDn != null) {
            path = RestClientUtil.parameterToString(path, "", "solutionDn", solutionDn);
        }
        if (clickDn != null) {
            path = RestClientUtil.parameterToString(path, "", "clickDn", clickDn);
        }
        if (level != null) {
            path = RestClientUtil.parameterToString(path, "", "level", level);
        }
        if (masterView != null) {
            path = RestClientUtil.parameterToString(path, "", "masterView", masterView);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "queryCrossData"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 是否能够自动保存网元位置
     * 是否能够自动保存网元位置
     * @param solutionDn 网元的dn值
     * @param level 网元的dn值
     * @param masterView 是否是主视图
     * @param clickDn 网元的dn值
     * @param callback async callback object
     */
    public void asyncqueryCrossData(String solutionDn, String level, Integer masterView, String clickDn, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // verify the required parameter 'solutionDn' is set
        if (solutionDn == null) {
            throw new ServiceException("Missing the required parameter 'solutionDn' when calling queryCrossData", 400);
        }
    
        // verify the required parameter 'level' is set
        if (level == null) {
            throw new ServiceException("Missing the required parameter 'level' when calling queryCrossData", 400);
        }
    
        // verify the required parameter 'masterView' is set
        if (masterView == null) {
            throw new ServiceException("Missing the required parameter 'masterView' when calling queryCrossData", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/cross/solution";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (solutionDn != null) {
            path = RestClientUtil.parameterToString(path, "", "solutionDn", solutionDn);
        }
        if (clickDn != null) {
            path = RestClientUtil.parameterToString(path, "", "clickDn", clickDn);
        }
        if (level != null) {
            path = RestClientUtil.parameterToString(path, "", "level", level);
        }
        if (masterView != null) {
            path = RestClientUtil.parameterToString(path, "", "masterView", masterView);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * 是否能够自动保存网元位置
     * 是否能够自动保存网元位置
     * @return ResponseEntity
     */
    public ResponseEntity queryTree() throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/cross/tree";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "queryTree"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 是否能够自动保存网元位置
     * 是否能够自动保存网元位置
     * @param callback async callback object
     */
    public void asyncqueryTree(RestfulAsyncCallback callback) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/cross/tree";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * 删除连接
     * 删除连接
     * @param deleteLinkModel 连线信息
     * @return ResponseEntity
     */
    public ResponseEntity deletelink(DeleteLinkModel deleteLinkModel) throws ServiceException {
        Object postBody = deleteLinkModel;
    
        // verify the required parameter 'deleteLinkModel' is set
        if (deleteLinkModel == null) {
            throw new ServiceException("Missing the required parameter 'deleteLinkModel' when calling deletelink", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/deletelink";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "deletelink"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 删除连接
     * 删除连接
     * @param deleteLinkModel 连线信息
     * @param callback async callback object
     */
    public void asyncdeletelink(DeleteLinkModel deleteLinkModel, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = deleteLinkModel;
    
        // verify the required parameter 'deleteLinkModel' is set
        if (deleteLinkModel == null) {
            throw new ServiceException("Missing the required parameter 'deleteLinkModel' when calling deletelink", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/deletelink";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 删除虚拟网元
     * 删除虚拟网元
     * @param deleteLinkModel 连线信息
     * @return ResponseEntity
     */
    public ResponseEntity deleteVirtualNode(DeleteNodeModel deleteLinkModel) throws ServiceException {
        Object postBody = deleteLinkModel;
    
        // verify the required parameter 'deleteLinkModel' is set
        if (deleteLinkModel == null) {
            throw new ServiceException("Missing the required parameter 'deleteLinkModel' when calling deleteVirtualNode", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/deletevirtualnode";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "deleteVirtualNode"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 删除虚拟网元
     * 删除虚拟网元
     * @param deleteLinkModel 连线信息
     * @param callback async callback object
     */
    public void asyncdeleteVirtualNode(DeleteNodeModel deleteLinkModel, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = deleteLinkModel;
    
        // verify the required parameter 'deleteLinkModel' is set
        if (deleteLinkModel == null) {
            throw new ServiceException("Missing the required parameter 'deleteLinkModel' when calling deleteVirtualNode", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/deletevirtualnode";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 查询网元连接具体信息
     * 查询网元连接具体信息
     * @param resId 连线的resId
     * @return ResponseEntity
     */
    public ResponseEntity displayLinkTips(String resId) throws ServiceException {
        
    
        // verify the required parameter 'resId' is set
        if (resId == null) {
            throw new ServiceException("Missing the required parameter 'resId' when calling displayLinkTips", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/displaylinktips";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (resId != null) {
            path = RestClientUtil.parameterToString(path, "", "resId", resId);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "displayLinkTips"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询网元连接具体信息
     * 查询网元连接具体信息
     * @param resId 连线的resId
     * @param callback async callback object
     */
    public void asyncdisplayLinkTips(String resId, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // verify the required parameter 'resId' is set
        if (resId == null) {
            throw new ServiceException("Missing the required parameter 'resId' when calling displayLinkTips", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/displaylinktips";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (resId != null) {
            path = RestClientUtil.parameterToString(path, "", "resId", resId);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * 查询tips中展示的告警级别
     * 查询tips中展示的告警级别
     * @param dn 网元dn
     * @return ResponseEntity
     */
    public ResponseEntity displayMoTips(String dn) throws ServiceException {
        
    
        // verify the required parameter 'dn' is set
        if (dn == null) {
            throw new ServiceException("Missing the required parameter 'dn' when calling displayMoTips", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/displaymotips";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (dn != null) {
            path = RestClientUtil.parameterToString(path, "", "dn", dn);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "displayMoTips"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询tips中展示的告警级别
     * 查询tips中展示的告警级别
     * @param dn 网元dn
     * @param callback async callback object
     */
    public void asyncdisplayMoTips(String dn, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // verify the required parameter 'dn' is set
        if (dn == null) {
            throw new ServiceException("Missing the required parameter 'dn' when calling displayMoTips", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/displaymotips";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (dn != null) {
            path = RestClientUtil.parameterToString(path, "", "dn", dn);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * 是否能够自动保存网元位置
     * 是否能够自动保存网元位置
     * @param ids 角色id列表
     * @return ResponseEntity
     */
    public ResponseEntity hasRight(String ids) throws ServiceException {
        
    
        // verify the required parameter 'ids' is set
        if (ids == null) {
            throw new ServiceException("Missing the required parameter 'ids' when calling hasRight", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/hasRight";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (ids != null) {
            path = RestClientUtil.parameterToString(path, "", "ids", ids);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "hasRight"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 是否能够自动保存网元位置
     * 是否能够自动保存网元位置
     * @param ids 角色id列表
     * @param callback async callback object
     */
    public void asynchasRight(String ids, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // verify the required parameter 'ids' is set
        if (ids == null) {
            throw new ServiceException("Missing the required parameter 'ids' when calling hasRight", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/hasRight";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (ids != null) {
            path = RestClientUtil.parameterToString(path, "", "ids", ids);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * 创建连接
     * 创建连接
     * @param linkInfo 连线信息
     * @return ResponseEntity
     */
    public ResponseEntity createLink(LinkCondition linkInfo) throws ServiceException {
        Object postBody = linkInfo;
    
        // verify the required parameter 'linkInfo' is set
        if (linkInfo == null) {
            throw new ServiceException("Missing the required parameter 'linkInfo' when calling createLink", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/link";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "createLink"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 创建连接
     * 创建连接
     * @param linkInfo 连线信息
     * @param callback async callback object
     */
    public void asynccreateLink(LinkCondition linkInfo, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = linkInfo;
    
        // verify the required parameter 'linkInfo' is set
        if (linkInfo == null) {
            throw new ServiceException("Missing the required parameter 'linkInfo' when calling createLink", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/link";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 创建网元
     * 创建网元
     * @param nodeInfo 连线信息
     * @return ResponseEntity
     */
    public ResponseEntity createNode(NodeCondition nodeInfo) throws ServiceException {
        Object postBody = nodeInfo;
    
        // verify the required parameter 'nodeInfo' is set
        if (nodeInfo == null) {
            throw new ServiceException("Missing the required parameter 'nodeInfo' when calling createNode", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/node";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "createNode"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 创建网元
     * 创建网元
     * @param nodeInfo 连线信息
     * @param callback async callback object
     */
    public void asynccreateNode(NodeCondition nodeInfo, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = nodeInfo;
    
        // verify the required parameter 'nodeInfo' is set
        if (nodeInfo == null) {
            throw new ServiceException("Missing the required parameter 'nodeInfo' when calling createNode", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/node";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 查询是否开启业务拓扑菜单
     * 查询是否开启业务拓扑菜单
     * @return Boolean
     */
    public Boolean queryBusinessTopoMenu() throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/querybusinesstopomenu";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return (Boolean)org.apache.commons.beanutils.ConvertUtils.convert(response.getResponseContent(), Boolean.class);    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "queryBusinessTopoMenu"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询是否开启业务拓扑菜单
     * 查询是否开启业务拓扑菜单
     * @param callback async callback object
     */
    public void asyncqueryBusinessTopoMenu(RestfulAsyncCallback callback) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/querybusinesstopomenu";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * 查询右键菜单中屏蔽的单元
     * 查询右键菜单中屏蔽的单元
     * @return ResponseEntity
     */
    public ResponseEntity queryMaskedMenus() throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/querymaskedmenus";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "queryMaskedMenus"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询右键菜单中屏蔽的单元
     * 查询右键菜单中屏蔽的单元
     * @param callback async callback object
     */
    public void asyncqueryMaskedMenus(RestfulAsyncCallback callback) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/querymaskedmenus";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * 查询nfv开关是否打开
     * 查询nfv开关是否打开
     * @return ResponseEntity
     */
    public ResponseEntity queryNfvConfig() throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/querynfvconfig";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "queryNfvConfig"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询nfv开关是否打开
     * 查询nfv开关是否打开
     * @param callback async callback object
     */
    public void asyncqueryNfvConfig(RestfulAsyncCallback callback) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/querynfvconfig";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * 是否展示创建虚拟网元按钮
     * 是否展示创建虚拟网元按钮
     * @param dn 虚拟网元
     * @return ResponseEntity
     */
    public ResponseEntity showCreateMenu(String dn) throws ServiceException {
        
    
        // verify the required parameter 'dn' is set
        if (dn == null) {
            throw new ServiceException("Missing the required parameter 'dn' when calling showCreateMenu", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/showcreatemenu";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (dn != null) {
            path = RestClientUtil.parameterToString(path, "", "dn", dn);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "showCreateMenu"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 是否展示创建虚拟网元按钮
     * 是否展示创建虚拟网元按钮
     * @param dn 虚拟网元
     * @param callback async callback object
     */
    public void asyncshowCreateMenu(String dn, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // verify the required parameter 'dn' is set
        if (dn == null) {
            throw new ServiceException("Missing the required parameter 'dn' when calling showCreateMenu", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/showcreatemenu";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (dn != null) {
            path = RestClientUtil.parameterToString(path, "", "dn", dn);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * 更新网元的连接状态
     * 更新网元的连接状态
     * @param updateMoModel 网元的dn值
     * @return ResponseEntity
     */
    public ResponseEntity updateMoConnectStatusByDn(UpdateMoModel updateMoModel) throws ServiceException {
        Object postBody = updateMoModel;
    
        // verify the required parameter 'updateMoModel' is set
        if (updateMoModel == null) {
            throw new ServiceException("Missing the required parameter 'updateMoModel' when calling updateMoConnectStatusByDn", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/updatemoconnectstatus";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "updateMoConnectStatusByDn"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 更新网元的连接状态
     * 更新网元的连接状态
     * @param updateMoModel 网元的dn值
     * @param callback async callback object
     */
    public void asyncupdateMoConnectStatusByDn(UpdateMoModel updateMoModel, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = updateMoModel;
    
        // verify the required parameter 'updateMoModel' is set
        if (updateMoModel == null) {
            throw new ServiceException("Missing the required parameter 'updateMoModel' when calling updateMoConnectStatusByDn", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/updatemoconnectstatus";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 查询网元的部署主机
     * 提供查询网元的部署主机的功能
     * @param dn 网元的dn值
     * @return ResponseEntity
     */
    public ResponseEntity checkVirtualNodeByDn(String dn) throws ServiceException {
        
    
        // verify the required parameter 'dn' is set
        if (dn == null) {
            throw new ServiceException("Missing the required parameter 'dn' when calling checkVirtualNodeByDn", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/virtual";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (dn != null) {
            path = RestClientUtil.parameterToString(path, "", "dn", dn);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseEntity>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "checkVirtualNodeByDn"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询网元的部署主机
     * 提供查询网元的部署主机的功能
     * @param dn 网元的dn值
     * @param callback async callback object
     */
    public void asynccheckVirtualNodeByDn(String dn, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // verify the required parameter 'dn' is set
        if (dn == null) {
            throw new ServiceException("Missing the required parameter 'dn' when calling checkVirtualNodeByDn", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvtopowebsite/v1/topo/virtual";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (dn != null) {
            path = RestClientUtil.parameterToString(path, "", "dn", dn);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  
  
    public Map<String, String> getContextHeader() {
        return contextHeader;
    }

    public void setContextHeader(Map<String, String> contextHeader) {
        this.contextHeader = contextHeader;
    }
    
        public RestfulOptions getOption() {
        return option;
    }


    public void setOption(RestfulOptions option) {
        this.option = option;
    }
    
}
