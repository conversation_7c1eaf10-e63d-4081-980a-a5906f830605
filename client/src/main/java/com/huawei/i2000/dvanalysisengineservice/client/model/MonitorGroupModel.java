package com.huawei.i2000.dvanalysisengineservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import com.huawei.i2000.dvanalysisengineservice.client.model.GroupMember;
import java.util.*;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MonitorGroupModel     {
    
    
    @JsonProperty("groupId")
    private String groupId = null;
    @JsonProperty("groupName")
    private String groupName = null;
    @JsonProperty("taskId")
    private String taskId = null;
    @JsonProperty("groupKey")
    private String groupKey = null;
    @JsonProperty("switchType")
    private String switchType = "0";
    @JsonProperty("groupMembers")
    private List<GroupMember> groupMembers = new ArrayList<GroupMember>();
    @JsonProperty("isScript")
    private Boolean isScript = false;
    @JsonProperty("isMonitor")
    private Boolean isMonitor = false;

    
    /**
    **/
    public String getGroupId() {
        return groupId;
    }
    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
    
    /**
    **/
    public String getGroupName() {
        return groupName;
    }
    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }
    
    /**
    **/
    public String getTaskId() {
        return taskId;
    }
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    
    /**
    **/
    public String getGroupKey() {
        return groupKey;
    }
    public void setGroupKey(String groupKey) {
        this.groupKey = groupKey;
    }
    
    /**
    **/
    public String getSwitchType() {
        return switchType;
    }
    public void setSwitchType(String switchType) {
        this.switchType = switchType;
    }
    
    /**
    **/
    public List<GroupMember> getGroupMembers() {
        return groupMembers;
    }
    public void setGroupMembers(List<GroupMember> groupMembers) {
        this.groupMembers = groupMembers;
    }
    
    /**
    **/
    public Boolean getIsScript() {
        return isScript;
    }
    public void setIsScript(Boolean isScript) {
        this.isScript = isScript;
    }
    
    /**
    **/
    public Boolean getIsMonitor() {
        return isMonitor;
    }
    public void setIsMonitor(Boolean isMonitor) {
        this.isMonitor = isMonitor;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

