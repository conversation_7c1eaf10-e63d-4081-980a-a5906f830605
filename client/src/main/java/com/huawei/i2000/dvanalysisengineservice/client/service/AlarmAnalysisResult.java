package com.huawei.i2000.dvanalysisengineservice.client.service;

import java.util.*;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.huawei.i2000.dvanalysisengineservice.client.model.QueryAlarmDetailData;
import com.huawei.i2000.dvanalysisengineservice.client.model.AlarmDetailResult;
import com.huawei.i2000.dvanalysisengineservice.client.model.AlarmIdListResult;
import com.huawei.i2000.dvanalysisengineservice.client.model.QueryAlarmIdListData;
import com.huawei.i2000.dvanalysisengineservice.client.model.AlarmListResult;
import com.huawei.i2000.dvanalysisengineservice.client.model.QueryAlarmListData;
import com.huawei.i2000.dvanalysisengineservice.client.model.QueryAlarmMeasurementListData;
import com.huawei.i2000.dvanalysisengineservice.client.model.AlarmMeasurementListResult;
import com.huawei.i2000.dvanalysisengineservice.client.model.AlarmTopologyByEventIdResult;
import com.huawei.i2000.dvanalysisengineservice.client.model.QueryAlarmTopologyByEventIdData;
import com.huawei.i2000.dvanalysisengineservice.client.model.EventListResult;
import com.huawei.i2000.dvanalysisengineservice.client.model.QueryEventListData;
import com.huawei.i2000.dvanalysisengineservice.client.model.MarkingAlarmsData;
import com.huawei.i2000.dvanalysisengineservice.client.model.QueryQuantityTrendByAlarmIdData;
import com.huawei.i2000.dvanalysisengineservice.client.model.QuantityTrendByAlarmIdResult;
import com.huawei.i2000.dvanalysisengineservice.util.RestClientUtil;
import com.huawei.bsp.remoteservice.exception.ExceptionArgs;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.clientsdk.HttpUtil;
import com.huawei.bsp.roa.util.clientsdk.JsonUtil;
import com.huawei.bsp.roa.util.restclient.Restful;
import com.huawei.bsp.roa.util.restclient.RestfulAsyncCallback;
import com.huawei.bsp.roa.util.restclient.RestfulFactory;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import java.util.Map;
import java.util.HashMap;

public class AlarmAnalysisResult {
    private RestfulOptions option = null;
    private Map<String, String> contextHeader = new HashMap<String, String>(1);
    private Restful restFull = null;
    
    public AlarmAnalysisResult() {
    
    }
    
        
    public AlarmAnalysisResult(Restful restFullout) {
        this.restFull = restFullout;
    }

  
    /**
     * 查询告警详情
     * 查询告警详情
     * @param queryAlarmDetailData 查询告警详情入参
     * @return AlarmDetailResult
     */
    public AlarmDetailResult getAlarmDetail(QueryAlarmDetailData queryAlarmDetailData) throws ServiceException {
        Object postBody = queryAlarmDetailData;
    
        // verify the required parameter 'queryAlarmDetailData' is set
        if (queryAlarmDetailData == null) {
            throw new ServiceException("Missing the required parameter 'queryAlarmDetailData' when calling getAlarmDetail", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/alarmdetail";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<AlarmDetailResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getAlarmDetail"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询告警详情
     * 查询告警详情
     * @param queryAlarmDetailData 查询告警详情入参
     * @param callback async callback object
     */
    public void asyncgetAlarmDetail(QueryAlarmDetailData queryAlarmDetailData, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = queryAlarmDetailData;
    
        // verify the required parameter 'queryAlarmDetailData' is set
        if (queryAlarmDetailData == null) {
            throw new ServiceException("Missing the required parameter 'queryAlarmDetailData' when calling getAlarmDetail", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/alarmdetail";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 查询事件下的告警id集合
     * 查询事件下的告警id集合
     * @param queryAlarmIdListData 查询事件下的告警id集合入参
     * @return AlarmIdListResult
     */
    public AlarmIdListResult getAlarmIdList(QueryAlarmIdListData queryAlarmIdListData) throws ServiceException {
        Object postBody = queryAlarmIdListData;
    
        // verify the required parameter 'queryAlarmIdListData' is set
        if (queryAlarmIdListData == null) {
            throw new ServiceException("Missing the required parameter 'queryAlarmIdListData' when calling getAlarmIdList", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/alarmidlist";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<AlarmIdListResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getAlarmIdList"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询事件下的告警id集合
     * 查询事件下的告警id集合
     * @param queryAlarmIdListData 查询事件下的告警id集合入参
     * @param callback async callback object
     */
    public void asyncgetAlarmIdList(QueryAlarmIdListData queryAlarmIdListData, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = queryAlarmIdListData;
    
        // verify the required parameter 'queryAlarmIdListData' is set
        if (queryAlarmIdListData == null) {
            throw new ServiceException("Missing the required parameter 'queryAlarmIdListData' when calling getAlarmIdList", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/alarmidlist";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 查询告警列表
     * 查询告警列表
     * @param queryAlarmListData 查询告警列表入参
     * @return AlarmListResult
     */
    public AlarmListResult getAlarmList(QueryAlarmListData queryAlarmListData) throws ServiceException {
        Object postBody = queryAlarmListData;
    
        // verify the required parameter 'queryAlarmListData' is set
        if (queryAlarmListData == null) {
            throw new ServiceException("Missing the required parameter 'queryAlarmListData' when calling getAlarmList", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/alarmlist";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<AlarmListResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getAlarmList"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询告警列表
     * 查询告警列表
     * @param queryAlarmListData 查询告警列表入参
     * @param callback async callback object
     */
    public void asyncgetAlarmList(QueryAlarmListData queryAlarmListData, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = queryAlarmListData;
    
        // verify the required parameter 'queryAlarmListData' is set
        if (queryAlarmListData == null) {
            throw new ServiceException("Missing the required parameter 'queryAlarmListData' when calling getAlarmList", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/alarmlist";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 查询告警度量项列表
     * 查询告警度量项列表
     * @param queryAlarmMeasurementListData 查询告警度量项列表入参
     * @return AlarmMeasurementListResult
     */
    public AlarmMeasurementListResult getAlarmMeasurementList(QueryAlarmMeasurementListData queryAlarmMeasurementListData) throws ServiceException {
        Object postBody = queryAlarmMeasurementListData;
    
        // verify the required parameter 'queryAlarmMeasurementListData' is set
        if (queryAlarmMeasurementListData == null) {
            throw new ServiceException("Missing the required parameter 'queryAlarmMeasurementListData' when calling getAlarmMeasurementList", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/alarmmeasurementlist";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<AlarmMeasurementListResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getAlarmMeasurementList"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询告警度量项列表
     * 查询告警度量项列表
     * @param queryAlarmMeasurementListData 查询告警度量项列表入参
     * @param callback async callback object
     */
    public void asyncgetAlarmMeasurementList(QueryAlarmMeasurementListData queryAlarmMeasurementListData, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = queryAlarmMeasurementListData;
    
        // verify the required parameter 'queryAlarmMeasurementListData' is set
        if (queryAlarmMeasurementListData == null) {
            throw new ServiceException("Missing the required parameter 'queryAlarmMeasurementListData' when calling getAlarmMeasurementList", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/alarmmeasurementlist";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 根据事件id查询告警拓扑信息
     * 根据事件id查询告警拓扑信息
     * @param queryAlarmTopologyByEventIdData 根据事件id查询告警拓扑信息入参
     * @return AlarmTopologyByEventIdResult
     */
    public AlarmTopologyByEventIdResult getAlarmTopologyByEventId(QueryAlarmTopologyByEventIdData queryAlarmTopologyByEventIdData) throws ServiceException {
        Object postBody = queryAlarmTopologyByEventIdData;
    
        // verify the required parameter 'queryAlarmTopologyByEventIdData' is set
        if (queryAlarmTopologyByEventIdData == null) {
            throw new ServiceException("Missing the required parameter 'queryAlarmTopologyByEventIdData' when calling getAlarmTopologyByEventId", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/alarmtopologybyeventid";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<AlarmTopologyByEventIdResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getAlarmTopologyByEventId"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 根据事件id查询告警拓扑信息
     * 根据事件id查询告警拓扑信息
     * @param queryAlarmTopologyByEventIdData 根据事件id查询告警拓扑信息入参
     * @param callback async callback object
     */
    public void asyncgetAlarmTopologyByEventId(QueryAlarmTopologyByEventIdData queryAlarmTopologyByEventIdData, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = queryAlarmTopologyByEventIdData;
    
        // verify the required parameter 'queryAlarmTopologyByEventIdData' is set
        if (queryAlarmTopologyByEventIdData == null) {
            throw new ServiceException("Missing the required parameter 'queryAlarmTopologyByEventIdData' when calling getAlarmTopologyByEventId", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/alarmtopologybyeventid";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 定时清理告警通知表
     * 定时清理告警通知表
     * @return void
     */
    public void clearSendRecord() throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/clearsendrecord";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            return;
        }
        else {
            return ;
        }
    }
    
    /**
     * async method
     * 定时清理告警通知表
     * 定时清理告警通知表
     * @param callback async callback object
     */
    public void asyncclearSendRecord(RestfulAsyncCallback callback) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/clearsendrecord";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 查询事件列表
     * 查询事件列表
     * @param queryEventListData 查询事件列表入参
     * @return EventListResult
     */
    public EventListResult getEventList(QueryEventListData queryEventListData) throws ServiceException {
        Object postBody = queryEventListData;
    
        // verify the required parameter 'queryEventListData' is set
        if (queryEventListData == null) {
            throw new ServiceException("Missing the required parameter 'queryEventListData' when calling getEventList", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/eventlist";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<EventListResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getEventList"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询事件列表
     * 查询事件列表
     * @param queryEventListData 查询事件列表入参
     * @param callback async callback object
     */
    public void asyncgetEventList(QueryEventListData queryEventListData, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = queryEventListData;
    
        // verify the required parameter 'queryEventListData' is set
        if (queryEventListData == null) {
            throw new ServiceException("Missing the required parameter 'queryEventListData' when calling getEventList", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/eventlist";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 批量标记告警
     * 批量标记告警
     * @param markingAlarmsData 批量标记告警入参
     * @return void
     */
    public void markingAlarms(MarkingAlarmsData markingAlarmsData) throws ServiceException {
        Object postBody = markingAlarmsData;
    
        // verify the required parameter 'markingAlarmsData' is set
        if (markingAlarmsData == null) {
            throw new ServiceException("Missing the required parameter 'markingAlarmsData' when calling markingAlarms", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/markingalarms";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            return;
        }
        else {
            return ;
        }
    }
    
    /**
     * async method
     * 批量标记告警
     * 批量标记告警
     * @param markingAlarmsData 批量标记告警入参
     * @param callback async callback object
     */
    public void asyncmarkingAlarms(MarkingAlarmsData markingAlarmsData, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = markingAlarmsData;
    
        // verify the required parameter 'markingAlarmsData' is set
        if (markingAlarmsData == null) {
            throw new ServiceException("Missing the required parameter 'markingAlarmsData' when calling markingAlarms", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/markingalarms";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 查询告警下的告警数趋势图
     * 查询告警下的告警数趋势图
     * @param queryQuantityTrendByAlarmId 查询告警下的告警数趋势图入参
     * @return QuantityTrendByAlarmIdResult
     */
    public QuantityTrendByAlarmIdResult getQuantityTrendByAlarmId(QueryQuantityTrendByAlarmIdData queryQuantityTrendByAlarmId) throws ServiceException {
        Object postBody = queryQuantityTrendByAlarmId;
    
        // verify the required parameter 'queryQuantityTrendByAlarmId' is set
        if (queryQuantityTrendByAlarmId == null) {
            throw new ServiceException("Missing the required parameter 'queryQuantityTrendByAlarmId' when calling getQuantityTrendByAlarmId", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/quantitytrendbyalarmid";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<QuantityTrendByAlarmIdResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getQuantityTrendByAlarmId"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询告警下的告警数趋势图
     * 查询告警下的告警数趋势图
     * @param queryQuantityTrendByAlarmId 查询告警下的告警数趋势图入参
     * @param callback async callback object
     */
    public void asyncgetQuantityTrendByAlarmId(QueryQuantityTrendByAlarmIdData queryQuantityTrendByAlarmId, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = queryQuantityTrendByAlarmId;
    
        // verify the required parameter 'queryQuantityTrendByAlarmId' is set
        if (queryQuantityTrendByAlarmId == null) {
            throw new ServiceException("Missing the required parameter 'queryQuantityTrendByAlarmId' when calling getQuantityTrendByAlarmId", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/alarmanalysisresult/quantitytrendbyalarmid";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  
  
    public Map<String, String> getContextHeader() {
        return contextHeader;
    }

    public void setContextHeader(Map<String, String> contextHeader) {
        this.contextHeader = contextHeader;
    }
    
        public RestfulOptions getOption() {
        return option;
    }


    public void setOption(RestfulOptions option) {
        this.option = option;
    }
    
}
