package com.huawei.i2000.dvanalysisengineservice.client.service;

import java.util.*;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.huawei.i2000.dvanalysisengineservice.client.model.QueryOutlierIndicatorCardListData;
import com.huawei.i2000.dvanalysisengineservice.client.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineservice.client.model.AssociationResultQueryParam;
import com.huawei.i2000.dvanalysisengineservice.client.model.CorrelationLogTextQuery;
import com.huawei.i2000.dvanalysisengineservice.client.model.CorrelationReportNode;
import com.huawei.i2000.dvanalysisengineservice.client.model.AssocationAnalysisTaskQuery;
import com.huawei.i2000.dvanalysisengineservice.client.model.IndicatorPredictResultQueryParam;
import com.huawei.i2000.dvanalysisengineservice.client.model.QueryPerformanceData;
import com.huawei.i2000.dvanalysisengineservice.client.model.QueryIndicatorInfoListData;
import com.huawei.i2000.dvanalysisengineservice.client.model.RecommendAccessRecordQueryParam;
import com.huawei.i2000.dvanalysisengineservice.client.model.RecommendFeedbackQueryParam;
import com.huawei.i2000.dvanalysisengineservice.client.model.AnalysisTaskListQueryParam;
import com.huawei.i2000.dvanalysisengineservice.util.RestClientUtil;
import com.huawei.bsp.remoteservice.exception.ExceptionArgs;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.clientsdk.HttpUtil;
import com.huawei.bsp.roa.util.clientsdk.JsonUtil;
import com.huawei.bsp.roa.util.restclient.Restful;
import com.huawei.bsp.roa.util.restclient.RestfulAsyncCallback;
import com.huawei.bsp.roa.util.restclient.RestfulFactory;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import java.util.Map;
import java.util.HashMap;

public class AnalysisOpenapi {
    private RestfulOptions option = null;
    private Map<String, String> contextHeader = new HashMap<String, String>(1);
    private Restful restFull = null;
    
    public AnalysisOpenapi() {
    
    }
    
        
    public AnalysisOpenapi(Restful restFullout) {
        this.restFull = restFullout;
    }

  
    /**
     * 指标异常检测概览
     * 指标异常检测概览
     * @param param 
     * @return ResponseResult
     */
    public ResponseResult exceptionOverview(QueryOutlierIndicatorCardListData param) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling exceptionOverview", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/exceptionoverview";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "exceptionOverview"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 指标异常检测概览
     * 指标异常检测概览
     * @param param 
     * @param callback async callback object
     */
    public void asyncexceptionOverview(QueryOutlierIndicatorCardListData param, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling exceptionOverview", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/exceptionoverview";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 根据任务id获取关联分析结果列表
     * 根据任务id获取关联分析结果列表
     * @param param 
     * @return ResponseResult
     */
    public ResponseResult getAssociationResult(AssociationResultQueryParam param) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling getAssociationResult", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/getassociationresult";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getAssociationResult"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 根据任务id获取关联分析结果列表
     * 根据任务id获取关联分析结果列表
     * @param param 
     * @param callback async callback object
     */
    public void asyncgetAssociationResult(AssociationResultQueryParam param, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling getAssociationResult", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/getassociationresult";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 用于查询关联分析结果日志节点的钻取文本 - 导出报告使用
     * 用于查询关联分析结果日志节点的钻取文本 - 导出报告使用
     * @param logTextParam 日志文本查询入参
     * @return ResponseResult
     */
    public ResponseResult getCorrelationLogTextByTemplate(CorrelationLogTextQuery logTextParam) throws ServiceException {
        Object postBody = logTextParam;
    
        // verify the required parameter 'logTextParam' is set
        if (logTextParam == null) {
            throw new ServiceException("Missing the required parameter 'logTextParam' when calling getCorrelationLogTextByTemplate", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/getcorrelationlogtextbytemplate";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getCorrelationLogTextByTemplate"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 用于查询关联分析结果日志节点的钻取文本 - 导出报告使用
     * 用于查询关联分析结果日志节点的钻取文本 - 导出报告使用
     * @param logTextParam 日志文本查询入参
     * @param callback async callback object
     */
    public void asyncgetCorrelationLogTextByTemplate(CorrelationLogTextQuery logTextParam, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = logTextParam;
    
        // verify the required parameter 'logTextParam' is set
        if (logTextParam == null) {
            throw new ServiceException("Missing the required parameter 'logTextParam' when calling getCorrelationLogTextByTemplate", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/getcorrelationlogtextbytemplate";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 用于查询关联分析结果节点 - 导出报告使用
     * 用于查询关联分析结果节点 - 导出报告使用
     * @param nodeParam 节点查询入参
     * @return ResponseResult
     */
    public ResponseResult getCorrelationNodeInfo(CorrelationReportNode nodeParam) throws ServiceException {
        Object postBody = nodeParam;
    
        // verify the required parameter 'nodeParam' is set
        if (nodeParam == null) {
            throw new ServiceException("Missing the required parameter 'nodeParam' when calling getCorrelationNodeInfo", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/getcorrelationnodeinfo";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getCorrelationNodeInfo"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 用于查询关联分析结果节点 - 导出报告使用
     * 用于查询关联分析结果节点 - 导出报告使用
     * @param nodeParam 节点查询入参
     * @param callback async callback object
     */
    public void asyncgetCorrelationNodeInfo(CorrelationReportNode nodeParam, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = nodeParam;
    
        // verify the required parameter 'nodeParam' is set
        if (nodeParam == null) {
            throw new ServiceException("Missing the required parameter 'nodeParam' when calling getCorrelationNodeInfo", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/getcorrelationnodeinfo";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 用于查询关联分析结果模板 - 导出报告使用
     * 用于查询关联分析结果模板 - 导出报告使用
     * @param queryParam 关联分析结果模板关联分析结果入参
     * @return ResponseResult
     */
    public ResponseResult getCorrelationResult(AssocationAnalysisTaskQuery queryParam) throws ServiceException {
        Object postBody = queryParam;
    
        // verify the required parameter 'queryParam' is set
        if (queryParam == null) {
            throw new ServiceException("Missing the required parameter 'queryParam' when calling getCorrelationResult", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/getcorrelationresult";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getCorrelationResult"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 用于查询关联分析结果模板 - 导出报告使用
     * 用于查询关联分析结果模板 - 导出报告使用
     * @param queryParam 关联分析结果模板关联分析结果入参
     * @param callback async callback object
     */
    public void asyncgetCorrelationResult(AssocationAnalysisTaskQuery queryParam, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = queryParam;
    
        // verify the required parameter 'queryParam' is set
        if (queryParam == null) {
            throw new ServiceException("Missing the required parameter 'queryParam' when calling getCorrelationResult", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/getcorrelationresult";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 查询趋势预测分析结果
     * 查询趋势预测分析结果
     * @param param 
     * @return ResponseResult
     */
    public ResponseResult getIndicatorPredictResult(IndicatorPredictResultQueryParam param) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling getIndicatorPredictResult", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/getindicatorpredictresult";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getIndicatorPredictResult"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 查询趋势预测分析结果
     * 查询趋势预测分析结果
     * @param param 
     * @param callback async callback object
     */
    public void asyncgetIndicatorPredictResult(IndicatorPredictResultQueryParam param, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling getIndicatorPredictResult", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/getindicatorpredictresult";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 获取异常的指标列表
     * 获取异常的指标列表
     * @param param 查询指标基本信息入参
     * @return ResponseResult
     */
    public ResponseResult getIndicatorDataDetail(QueryPerformanceData param) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling getIndicatorDataDetail", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/indicatordatadetail";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getIndicatorDataDetail"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 获取异常的指标列表
     * 获取异常的指标列表
     * @param param 查询指标基本信息入参
     * @param callback async callback object
     */
    public void asyncgetIndicatorDataDetail(QueryPerformanceData param, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling getIndicatorDataDetail", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/indicatordatadetail";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 获取异常的指标列表
     * 获取异常的指标列表
     * @param param 查询指标基本信息入参
     * @return ResponseResult
     */
    public ResponseResult getIndicatorInfoList(QueryIndicatorInfoListData param) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling getIndicatorInfoList", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/indicatorinfolist";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getIndicatorInfoList"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 获取异常的指标列表
     * 获取异常的指标列表
     * @param param 查询指标基本信息入参
     * @param callback async callback object
     */
    public void asyncgetIndicatorInfoList(QueryIndicatorInfoListData param, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling getIndicatorInfoList", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/indicatorinfolist";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 推荐反馈
     * 查询推荐反馈
     * @param param 
     * @return ResponseResult
     */
    public ResponseResult queryRecommendAccessRecord(RecommendAccessRecordQueryParam param) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling queryRecommendAccessRecord", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/queryrecommendaccessrecord";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "queryRecommendAccessRecord"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 推荐反馈
     * 查询推荐反馈
     * @param param 
     * @param callback async callback object
     */
    public void asyncqueryRecommendAccessRecord(RecommendAccessRecordQueryParam param, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling queryRecommendAccessRecord", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/queryrecommendaccessrecord";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 推荐反馈
     * 查询推荐反馈
     * @param param 
     * @return ResponseResult
     */
    public ResponseResult queryRecommendFeedback(RecommendFeedbackQueryParam param) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling queryRecommendFeedback", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/queryrecommendfeedback";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "queryRecommendFeedback"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 推荐反馈
     * 查询推荐反馈
     * @param param 
     * @param callback async callback object
     */
    public void asyncqueryRecommendFeedback(RecommendFeedbackQueryParam param, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling queryRecommendFeedback", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/queryrecommendfeedback";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * 获取任务列表
     * 获取任务列表
     * @param param 
     * @return ResponseResult
     */
    public ResponseResult getTaskList(AnalysisTaskListQueryParam param) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling getTaskList", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/tasklist";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getTaskList"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * 获取任务列表
     * 获取任务列表
     * @param param 
     * @param callback async callback object
     */
    public void asyncgetTaskList(AnalysisTaskListQueryParam param, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = param;
    
        // verify the required parameter 'param' is set
        if (param == null) {
            throw new ServiceException("Missing the required parameter 'param' when calling getTaskList", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineservice/v1/analysisopenapi/tasklist";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  
  
    public Map<String, String> getContextHeader() {
        return contextHeader;
    }

    public void setContextHeader(Map<String, String> contextHeader) {
        this.contextHeader = contextHeader;
    }
    
        public RestfulOptions getOption() {
        return option;
    }


    public void setOption(RestfulOptions option) {
        this.option = option;
    }
    
}
