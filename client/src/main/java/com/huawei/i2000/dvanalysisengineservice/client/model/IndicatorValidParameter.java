package com.huawei.i2000.dvanalysisengineservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import com.huawei.i2000.dvanalysisengineservice.client.model.TriggerIndicator;
import java.util.*;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IndicatorValidParameter     {
    
    
    @JsonProperty("indicatorList")
    private List<TriggerIndicator> indicatorList = new ArrayList<TriggerIndicator>();
    @JsonProperty("dataSourceType")
    private String dataSourceType = null;

    
    /**
     * pql集合
    **/
    public List<TriggerIndicator> getIndicatorList() {
        return indicatorList;
    }
    public void setIndicatorList(List<TriggerIndicator> indicatorList) {
        this.indicatorList = indicatorList;
    }
    
    /**
    **/
    public String getDataSourceType() {
        return dataSourceType;
    }
    public void setDataSourceType(String dataSourceType) {
        this.dataSourceType = dataSourceType;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

