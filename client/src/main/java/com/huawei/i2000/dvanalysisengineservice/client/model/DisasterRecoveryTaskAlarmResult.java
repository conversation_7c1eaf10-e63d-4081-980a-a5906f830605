package com.huawei.i2000.dvanalysisengineservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
public class DisasterRecoveryTaskAlarmResult     {
    
    
    @JsonProperty("id")
    private String id = null;
    @JsonProperty("taskInstanceId")
    private String taskInstanceId = null;
    @JsonProperty("groupId")
    private String groupId = null;
    @JsonProperty("groupMemberId")
    private String groupMemberId = null;
    @JsonProperty("alarmId")
    private String alarmId = null;
    @JsonProperty("alarmDn")
    private String alarmDn = null;
    @JsonProperty("occurrenceTimes")
    private String occurrenceTimes = null;

    
    /**
    **/
    public String getId() {
        return id;
    }
    public void setId(String id) {
        this.id = id;
    }
    
    /**
    **/
    public String getTaskInstanceId() {
        return taskInstanceId;
    }
    public void setTaskInstanceId(String taskInstanceId) {
        this.taskInstanceId = taskInstanceId;
    }
    
    /**
    **/
    public String getGroupId() {
        return groupId;
    }
    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
    
    /**
    **/
    public String getGroupMemberId() {
        return groupMemberId;
    }
    public void setGroupMemberId(String groupMemberId) {
        this.groupMemberId = groupMemberId;
    }
    
    /**
    **/
    public String getAlarmId() {
        return alarmId;
    }
    public void setAlarmId(String alarmId) {
        this.alarmId = alarmId;
    }
    
    /**
    **/
    public String getAlarmDn() {
        return alarmDn;
    }
    public void setAlarmDn(String alarmDn) {
        this.alarmDn = alarmDn;
    }
    
    /**
    **/
    public String getOccurrenceTimes() {
        return occurrenceTimes;
    }
    public void setOccurrenceTimes(String occurrenceTimes) {
        this.occurrenceTimes = occurrenceTimes;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

