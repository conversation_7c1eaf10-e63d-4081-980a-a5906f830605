package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


/**
 * 页面信息
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class Paging     {
    
    
    @JsonProperty("pageSize")
    private Integer pageSize = 100;
    @JsonProperty("pageNumber")
    private Integer pageNumber = 1;
    @JsonProperty("sortField")
    private String sortField = null;
    @JsonProperty("sortType")
    private String sortType = null;

    
    /**
     * 每页数量
     * minimum: 1.0
     * maximum: 1000.0
    **/
    public Integer getPageSize() {
        return pageSize;
    }
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
    
    /**
     * 页数，默认1开始
     * minimum: 1.0
     * maximum: 10000.0
    **/
    public Integer getPageNumber() {
        return pageNumber;
    }
    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }
    
    /**
     * 分页字段
    **/
    public String getSortField() {
        return sortField;
    }
    public void setSortField(String sortField) {
        this.sortField = sortField;
    }
    
    /**
     * 分页类型（正序倒序）
    **/
    public String getSortType() {
        return sortType;
    }
    public void setSortType(String sortType) {
        this.sortType = sortType;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

