package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import java.math.BigDecimal;

@JsonIgnoreProperties(ignoreUnknown = true)
public class PmKpiQueryModel     {
    
    
    @JsonProperty("neName")
    private String neName = null;
    @JsonProperty("taskId")
    private String taskId = null;
    @JsonProperty("timestamp")
    private BigDecimal timestamp = null;

    
    /**
     * 网元名称
    **/
    public String getNeName() {
        return neName;
    }
    public void setNeName(String neName) {
        this.neName = neName;
    }
    
    /**
     * 国际化以后的任务名称
    **/
    public String getTaskId() {
        return taskId;
    }
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    
    /**
     * 需要导出数据的时间点
    **/
    public BigDecimal getTimestamp() {
        return timestamp;
    }
    public void setTimestamp(BigDecimal timestamp) {
        this.timestamp = timestamp;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

