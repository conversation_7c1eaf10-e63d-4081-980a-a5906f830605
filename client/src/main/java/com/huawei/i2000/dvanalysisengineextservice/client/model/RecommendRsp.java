package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import java.util.*;

/**
 * 智能提示返回体
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class RecommendRsp     {
    
    
    @JsonProperty("recommendQuestions")
    private List<String> recommendQuestions = new ArrayList<String>();

    
    /**
     * 答案
    **/
    public List<String> getRecommendQuestions() {
        return recommendQuestions;
    }
    public void setRecommendQuestions(List<String> recommendQuestions) {
        this.recommendQuestions = recommendQuestions;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

