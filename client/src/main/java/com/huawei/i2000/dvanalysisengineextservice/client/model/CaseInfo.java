package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
public class CaseInfo     {
    
    
    @JsonProperty("name")
    private String name = null;
    @JsonProperty("keyFault")
    private String keyFault = null;
    @JsonProperty("cause")
    private String cause = null;
    @JsonProperty("effect")
    private String effect = null;
    @JsonProperty("repair")
    private String repair = null;

    
    /**
    **/
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    
    /**
    **/
    public String getKeyFault() {
        return keyFault;
    }
    public void setKeyFault(String keyFault) {
        this.keyFault = keyFault;
    }
    
    /**
    **/
    public String getCause() {
        return cause;
    }
    public void setCause(String cause) {
        this.cause = cause;
    }
    
    /**
    **/
    public String getEffect() {
        return effect;
    }
    public void setEffect(String effect) {
        this.effect = effect;
    }
    
    /**
    **/
    public String getRepair() {
        return repair;
    }
    public void setRepair(String repair) {
        this.repair = repair;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

