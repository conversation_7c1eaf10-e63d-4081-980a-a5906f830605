package com.huawei.i2000.dvanalysisengineextservice.client.service;

import java.util.*;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.huawei.i2000.dvanalysisengineextservice.client.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineextservice.client.model.QueryAlarmInfoByIdParam;
import com.huawei.i2000.dvanalysisengineextservice.util.RestClientUtil;
import com.huawei.bsp.remoteservice.exception.ExceptionArgs;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.clientsdk.HttpUtil;
import com.huawei.bsp.roa.util.clientsdk.JsonUtil;
import com.huawei.bsp.roa.util.restclient.Restful;
import com.huawei.bsp.roa.util.restclient.RestfulAsyncCallback;
import com.huawei.bsp.roa.util.restclient.RestfulFactory;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import java.util.Map;
import java.util.HashMap;

public class DVAnalysisEngineExtServiceAlarms {
    private RestfulOptions option = null;
    private Map<String, String> contextHeader = new HashMap<String, String>(1);
    private Restful restFull = null;
    
    public DVAnalysisEngineExtServiceAlarms() {
    
    }
    
        
    public DVAnalysisEngineExtServiceAlarms(Restful restFullout) {
        this.restFull = restFullout;
    }

  
    /**
     * get_alarm_info
     * 通过告警流水号查询告警
     * @param csn 
     * @return ResponseResult
     */
    public ResponseResult queryAlarmInfoByAlarmCsn(String csn) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/alarms/alarminfo";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (csn != null) {
            path = RestClientUtil.parameterToString(path, "", "csn", csn);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "queryAlarmInfoByAlarmCsn"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * get_alarm_info
     * 通过告警流水号查询告警
     * @param csn 
     * @param callback async callback object
     */
    public void asyncqueryAlarmInfoByAlarmCsn(String csn, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/alarms/alarminfo";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (csn != null) {
            path = RestClientUtil.parameterToString(path, "", "csn", csn);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * get_alarm_list_by_ne_alarm
     * 通过id查询告警信息
     * @param queryAlarmInfoByIdParam 查询告警信息参数
     * @return ResponseResult
     */
    public ResponseResult queryAlarmInfoById(QueryAlarmInfoByIdParam queryAlarmInfoByIdParam) throws ServiceException {
        Object postBody = queryAlarmInfoByIdParam;
    
        // verify the required parameter 'queryAlarmInfoByIdParam' is set
        if (queryAlarmInfoByIdParam == null) {
            throw new ServiceException("Missing the required parameter 'queryAlarmInfoByIdParam' when calling queryAlarmInfoById", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/alarms/queryAlarmInfoById";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "queryAlarmInfoById"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * get_alarm_list_by_ne_alarm
     * 通过id查询告警信息
     * @param queryAlarmInfoByIdParam 查询告警信息参数
     * @param callback async callback object
     */
    public void asyncqueryAlarmInfoById(QueryAlarmInfoByIdParam queryAlarmInfoByIdParam, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = queryAlarmInfoByIdParam;
    
        // verify the required parameter 'queryAlarmInfoByIdParam' is set
        if (queryAlarmInfoByIdParam == null) {
            throw new ServiceException("Missing the required parameter 'queryAlarmInfoByIdParam' when calling queryAlarmInfoById", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/alarms/queryAlarmInfoById";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  /**
     * export_alarms_list
     * 告警查询总接口
     * @param neName 
     * @param severity 
     * @param clearanceStatus 
     * @param neType 
     * @param neIp 
     * @param timeRange 
     * @return ResponseResult
     */
    public ResponseResult queryGeneralAlarmsList(String neName, List<String> severity, List<String> clearanceStatus, String neType, String neIp, String timeRange) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/alarms/querygeneralalarmList";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (neName != null) {
            path = RestClientUtil.parameterToString(path, "", "neName", neName);
        }
        if (severity != null) {
            path = RestClientUtil.parameterToString(path, "csv", "severity", severity);
        }
        if (clearanceStatus != null) {
            path = RestClientUtil.parameterToString(path, "csv", "clearanceStatus", clearanceStatus);
        }
        if (neType != null) {
            path = RestClientUtil.parameterToString(path, "", "neType", neType);
        }
        if (neIp != null) {
            path = RestClientUtil.parameterToString(path, "", "neIp", neIp);
        }
        if (timeRange != null) {
            path = RestClientUtil.parameterToString(path, "", "timeRange", timeRange);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "queryGeneralAlarmsList"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * export_alarms_list
     * 告警查询总接口
     * @param neName 
     * @param severity 
     * @param clearanceStatus 
     * @param neType 
     * @param neIp 
     * @param timeRange 
     * @param callback async callback object
     */
    public void asyncqueryGeneralAlarmsList(String neName, List<String> severity, List<String> clearanceStatus, String neType, String neIp, String timeRange, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/alarms/querygeneralalarmList";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (neName != null) {
            path = RestClientUtil.parameterToString(path, "", "neName", neName);
        }
        if (severity != null) {
            path = RestClientUtil.parameterToString(path, "csv", "severity", severity);
        }
        if (clearanceStatus != null) {
            path = RestClientUtil.parameterToString(path, "csv", "clearanceStatus", clearanceStatus);
        }
        if (neType != null) {
            path = RestClientUtil.parameterToString(path, "", "neType", neType);
        }
        if (neIp != null) {
            path = RestClientUtil.parameterToString(path, "", "neIp", neIp);
        }
        if (timeRange != null) {
            path = RestClientUtil.parameterToString(path, "", "timeRange", timeRange);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  
  
    public Map<String, String> getContextHeader() {
        return contextHeader;
    }

    public void setContextHeader(Map<String, String> contextHeader) {
        this.contextHeader = contextHeader;
    }
    
        public RestfulOptions getOption() {
        return option;
    }


    public void setOption(RestfulOptions option) {
        this.option = option;
    }
    
}
