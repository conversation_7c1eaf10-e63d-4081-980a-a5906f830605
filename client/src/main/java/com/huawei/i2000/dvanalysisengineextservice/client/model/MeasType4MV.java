package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
public class MeasType4MV     {
    
    
    @JsonProperty("MEASTYPE_IMG")
    private String MEASTYPE_IMG = null;
    @JsonProperty("SPERATOR_FLAG")
    private String SPERATOR_FLAG = "~~";
    @JsonProperty("measUnitKey")
    private String measUnitKey = "";
    @JsonProperty("name")
    private String name = "";
    @JsonProperty("dataType")
    private String dataType = null;
    @JsonProperty("indexId")
    private String indexId = null;
    @JsonProperty("measTypeKey")
    private String measTypeKey = "";
    @JsonProperty("dn")
    private String dn = "";
    @JsonProperty("unit")
    private String unit = "";
    @JsonProperty("hasMeasObj")
    private Boolean hasMeasObj = null;
    @JsonProperty("icon")
    private String icon = null;
    @JsonProperty("chkDisabled")
    private Boolean chkDisabled = null;
    @JsonProperty("open")
    private Boolean open = null;
    @JsonProperty("resourceTypeKey")
    private String resourceTypeKey = null;
    @JsonProperty("indexGroupName")
    private String indexGroupName = null;

    
    /**
    **/
    public String getMEASTYPEIMG() {
        return MEASTYPE_IMG;
    }
    public void setMEASTYPEIMG(String MEASTYPE_IMG) {
        this.MEASTYPE_IMG = MEASTYPE_IMG;
    }
    
    /**
    **/
    public String getSPERATORFLAG() {
        return SPERATOR_FLAG;
    }
    public void setSPERATORFLAG(String SPERATOR_FLAG) {
        this.SPERATOR_FLAG = SPERATOR_FLAG;
    }
    
    /**
    **/
    public String getMeasUnitKey() {
        return measUnitKey;
    }
    public void setMeasUnitKey(String measUnitKey) {
        this.measUnitKey = measUnitKey;
    }
    
    /**
    **/
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    
    /**
    **/
    public String getDataType() {
        return dataType;
    }
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    
    /**
    **/
    public String getIndexId() {
        return indexId;
    }
    public void setIndexId(String indexId) {
        this.indexId = indexId;
    }
    
    /**
    **/
    public String getMeasTypeKey() {
        return measTypeKey;
    }
    public void setMeasTypeKey(String measTypeKey) {
        this.measTypeKey = measTypeKey;
    }
    
    /**
    **/
    public String getDn() {
        return dn;
    }
    public void setDn(String dn) {
        this.dn = dn;
    }
    
    /**
    **/
    public String getUnit() {
        return unit;
    }
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    /**
    **/
    public Boolean getHasMeasObj() {
        return hasMeasObj;
    }
    public void setHasMeasObj(Boolean hasMeasObj) {
        this.hasMeasObj = hasMeasObj;
    }
    
    /**
    **/
    public String getIcon() {
        return icon;
    }
    public void setIcon(String icon) {
        this.icon = icon;
    }
    
    /**
    **/
    public Boolean getChkDisabled() {
        return chkDisabled;
    }
    public void setChkDisabled(Boolean chkDisabled) {
        this.chkDisabled = chkDisabled;
    }
    
    /**
    **/
    public Boolean getOpen() {
        return open;
    }
    public void setOpen(Boolean open) {
        this.open = open;
    }
    
    /**
    **/
    public String getResourceTypeKey() {
        return resourceTypeKey;
    }
    public void setResourceTypeKey(String resourceTypeKey) {
        this.resourceTypeKey = resourceTypeKey;
    }
    
    /**
    **/
    public String getIndexGroupName() {
        return indexGroupName;
    }
    public void setIndexGroupName(String indexGroupName) {
        this.indexGroupName = indexGroupName;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

