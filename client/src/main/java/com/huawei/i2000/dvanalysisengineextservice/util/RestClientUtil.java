package com.huawei.i2000.dvanalysisengineextservice.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Collection;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.clientsdk.HttpUtil;
import com.huawei.bsp.roa.util.restclient.Restful;
import com.huawei.bsp.roa.util.restclient.RestfulAsyncCallback;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cloudsop.common.tokenhelper.util.rpc.RestAdapter;

public class RestClientUtil {
    public static RestfulResponse invokeMethod(String method, String path, RestfulParametes parameters, Restful restFull,
            RestfulOptions option) throws ServiceException {
        RestfulResponse response = null;
        RestAdapter restAdapter = new RestAdapter(restFull);
        if ("get".equalsIgnoreCase(method)) {
            response = restAdapter.get(path, parameters, option);
        }
        else if ("put".equalsIgnoreCase(method)) {
            response = restAdapter.put(path, parameters, option);
        }
        else if ("post".equalsIgnoreCase(method)) {
            response = restAdapter.post(path, parameters, option);
        }
        else if ("delete".equalsIgnoreCase(method)) {
            response = restAdapter.delete(path, parameters, option);
        }
        else if ("patch".equalsIgnoreCase(method)) {
            response = restAdapter.patch(path, parameters, option);
        }
        else {
            throw new ServiceException("NotSuppertMethod", 400);
        }
        return response;
    }

    public static void invokeAsyncMethod(String method, String path, RestfulParametes parameters, Restful restFull, RestfulOptions option,
            RestfulAsyncCallback callback) throws ServiceException {
        if ("get".equalsIgnoreCase(method)) {
            restFull.asyncGet(path, parameters, option, callback);
        }
        else if ("put".equalsIgnoreCase(method)) {
            restFull.asyncPut(path, parameters, option, callback);
        }
        else if ("post".equalsIgnoreCase(method)) {
            restFull.asyncPost(path, parameters, option, callback);
        }
        else if ("delete".equalsIgnoreCase(method)) {
            restFull.asyncDelete(path, parameters, option, callback);
        }
        else if ("patch".equalsIgnoreCase(method)) {
            restFull.asyncPatch(path, parameters, option, callback);
        }
        else {
            throw new ServiceException("NotSuppertMethod", 400);
        }
    }
    
    public static String parameterToString(String path, String collectionFormat, String name, Object value) throws ServiceException {
        Collection<?> valueCollection = null;
        if (value instanceof Collection<?>) {
            valueCollection = (Collection<?>) value;
        }
        else {
            return addToPath(path, name, HttpUtil.parameterToString(value));
        }

        if (valueCollection.isEmpty()) {
            return path;
        }

        collectionFormat = (collectionFormat == null || collectionFormat.isEmpty() ? "csv" : collectionFormat); // default: csv

        if (collectionFormat.equals("multi")) {
            for (Object item : valueCollection) {
                path = addToPath(path, name, HttpUtil.parameterToString(item));
            }

            return path;
        }

        String delimiter = ",";

        if (collectionFormat.equals("csv")) {
            delimiter = ",";
        }
        else if (collectionFormat.equals("ssv")) {
            delimiter = " ";
        }
        else if (collectionFormat.equals("tsv")) {
            delimiter = "\t";
        }
        else if (collectionFormat.equals("pipes")) {
            delimiter = "|";
        }

        StringBuilder sb = new StringBuilder();
        for (Object item : valueCollection) {
            sb.append(delimiter);
            sb.append(HttpUtil.parameterToString(item));
        }

        return addToPath(path, name, sb.substring(1));
    }
    
    private static String addToPath(String path, String name, String value) throws ServiceException {
        boolean bHaveQuery = false;

        if (path.contains("?")) {
            bHaveQuery = true;
        }

        StringBuilder builder = new StringBuilder();
        builder.append(path);
        if (value == null) {
            value = "";
        }
        String str = "";
        try {
            if (bHaveQuery) {
                str = String.format("&%s=%s", new Object[] { URLEncoder.encode(name, "UTF-8"), URLEncoder.encode(value, "UTF-8") });
            }
            else {
                str = String.format("?%s=%s", new Object[] { URLEncoder.encode(name, "UTF-8"), URLEncoder.encode(value, "UTF-8") });
            }
        }
        catch (UnsupportedEncodingException ex) {
            throw new ServiceException("Broken VM does not support UTF-8");
        }
        builder.append(str);

        return builder.toString();
    }
}

