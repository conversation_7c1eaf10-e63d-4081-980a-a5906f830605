package com.huawei.i2000.dvanalysisengineextservice.client.service;

import java.util.*;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.huawei.i2000.dvanalysisengineextservice.client.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineextservice.util.RestClientUtil;
import com.huawei.bsp.remoteservice.exception.ExceptionArgs;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.clientsdk.HttpUtil;
import com.huawei.bsp.roa.util.clientsdk.JsonUtil;
import com.huawei.bsp.roa.util.restclient.Restful;
import com.huawei.bsp.roa.util.restclient.RestfulAsyncCallback;
import com.huawei.bsp.roa.util.restclient.RestfulFactory;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import java.util.Map;
import java.util.HashMap;

public class ERDVAnalysisEngineExtServiceExport {
    private RestfulOptions option = null;
    private Map<String, String> contextHeader = new HashMap<String, String>(1);
    private Restful restFull = null;
    
    public ERDVAnalysisEngineExtServiceExport() {
    
    }
    
        
    public ERDVAnalysisEngineExtServiceExport(Restful restFullout) {
        this.restFull = restFullout;
    }

  
    /**
     * export_managedobject_list
     * 网元信息导出
     * @param connectStatus 
     * @return ResponseResult
     */
    public ResponseResult exportManagedObject(String connectStatus) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/website/export/managedobject/all";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (connectStatus != null) {
            path = RestClientUtil.parameterToString(path, "", "connectStatus", connectStatus);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "exportManagedObject"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * export_managedobject_list
     * 网元信息导出
     * @param connectStatus 
     * @param callback async callback object
     */
    public void asyncexportManagedObject(String connectStatus, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/website/export/managedobject/all";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (connectStatus != null) {
            path = RestClientUtil.parameterToString(path, "", "connectStatus", connectStatus);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * export_pm_kpi_list
     * KPI数据查询
     * @param neName 
     * @param taskName 
     * @param neType 
     * @param measTypeKey 
     * @param measObjects 
     * @param timeRange 
     * @return ResponseResult
     */
    public ResponseResult exportPmKpi(String neName, String taskName, String neType, String measTypeKey, String measObjects, String timeRange) throws ServiceException {
        
    
        // verify the required parameter 'neName' is set
        if (neName == null) {
            throw new ServiceException("Missing the required parameter 'neName' when calling exportPmKpi", 400);
        }
    
        // verify the required parameter 'taskName' is set
        if (taskName == null) {
            throw new ServiceException("Missing the required parameter 'taskName' when calling exportPmKpi", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/website/export/pm/kpi";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (neName != null) {
            path = RestClientUtil.parameterToString(path, "", "neName", neName);
        }
        if (taskName != null) {
            path = RestClientUtil.parameterToString(path, "", "taskName", taskName);
        }
        if (neType != null) {
            path = RestClientUtil.parameterToString(path, "", "neType", neType);
        }
        if (measTypeKey != null) {
            path = RestClientUtil.parameterToString(path, "", "measTypeKey", measTypeKey);
        }
        if (measObjects != null) {
            path = RestClientUtil.parameterToString(path, "", "measObjects", measObjects);
        }
        if (timeRange != null) {
            path = RestClientUtil.parameterToString(path, "", "timeRange", timeRange);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "exportPmKpi"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * export_pm_kpi_list
     * KPI数据查询
     * @param neName 
     * @param taskName 
     * @param neType 
     * @param measTypeKey 
     * @param measObjects 
     * @param timeRange 
     * @param callback async callback object
     */
    public void asyncexportPmKpi(String neName, String taskName, String neType, String measTypeKey, String measObjects, String timeRange, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // verify the required parameter 'neName' is set
        if (neName == null) {
            throw new ServiceException("Missing the required parameter 'neName' when calling exportPmKpi", 400);
        }
    
        // verify the required parameter 'taskName' is set
        if (taskName == null) {
            throw new ServiceException("Missing the required parameter 'taskName' when calling exportPmKpi", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/website/export/pm/kpi";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (neName != null) {
            path = RestClientUtil.parameterToString(path, "", "neName", neName);
        }
        if (taskName != null) {
            path = RestClientUtil.parameterToString(path, "", "taskName", taskName);
        }
        if (neType != null) {
            path = RestClientUtil.parameterToString(path, "", "neType", neType);
        }
        if (measTypeKey != null) {
            path = RestClientUtil.parameterToString(path, "", "measTypeKey", measTypeKey);
        }
        if (measObjects != null) {
            path = RestClientUtil.parameterToString(path, "", "measObjects", measObjects);
        }
        if (timeRange != null) {
            path = RestClientUtil.parameterToString(path, "", "timeRange", timeRange);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  
  
    public Map<String, String> getContextHeader() {
        return contextHeader;
    }

    public void setContextHeader(Map<String, String> contextHeader) {
        this.contextHeader = contextHeader;
    }
    
        public RestfulOptions getOption() {
        return option;
    }


    public void setOption(RestfulOptions option) {
        this.option = option;
    }
    
}
