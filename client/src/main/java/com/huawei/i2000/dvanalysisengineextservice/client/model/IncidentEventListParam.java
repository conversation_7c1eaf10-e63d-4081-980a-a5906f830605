package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import com.huawei.i2000.dvanalysisengineextservice.client.model.Paging;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IncidentEventListParam     {
    
    
    @JsonProperty("incidentId")
    private String incidentId = null;
    @JsonProperty("paging")
    private Paging paging = null;

    
    /**
     * 告警网元名称
    **/
    public String getIncidentId() {
        return incidentId;
    }
    public void setIncidentId(String incidentId) {
        this.incidentId = incidentId;
    }
    
    /**
     * 数据
    **/
    public Paging getPaging() {
        return paging;
    }
    public void setPaging(Paging paging) {
        this.paging = paging;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

