package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
public class ExecuteRequest     {
    
    
    @JsonProperty("ipList")
    private String ipList = null;
    @JsonProperty("username")
    private String username = "uniagent";
    @JsonProperty("executePath")
    private String executePath = null;
    @JsonProperty("scriptKey")
    private String scriptKey = null;
    @JsonProperty("extAttrs")
    private String extAttrs = null;

    
    /**
    **/
    public String getIpList() {
        return ipList;
    }
    public void setIpList(String ipList) {
        this.ipList = ipList;
    }
    
    /**
    **/
    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }
    
    /**
    **/
    public String getExecutePath() {
        return executePath;
    }
    public void setExecutePath(String executePath) {
        this.executePath = executePath;
    }
    
    /**
    **/
    public String getScriptKey() {
        return scriptKey;
    }
    public void setScriptKey(String scriptKey) {
        this.scriptKey = scriptKey;
    }
    
    /**
    **/
    public String getExtAttrs() {
        return extAttrs;
    }
    public void setExtAttrs(String extAttrs) {
        this.extAttrs = extAttrs;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

