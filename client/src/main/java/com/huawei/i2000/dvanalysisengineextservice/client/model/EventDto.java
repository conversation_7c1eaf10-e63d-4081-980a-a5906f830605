package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
public class EventDto     {
    
    
    @JsonProperty("eventId")
    private String eventId = null;
    @JsonProperty("csn")
    private Long csn = null;
    @JsonProperty("tenantId")
    private String tenantId = null;
    @JsonProperty("incidentId")
    private String incidentId = null;
    @JsonProperty("domain")
    private String domain = null;
    @JsonProperty("type")
    private String type = null;
    @JsonProperty("status")
    private String status = null;
    @JsonProperty("arriveTime")
    private Long arriveTime = null;
    @JsonProperty("occurTime")
    private Long occurTime = null;
    @JsonProperty("clearTime")
    private Long clearTime = null;
    @JsonProperty("severity")
    private String severity = null;
    @JsonProperty("name")
    private String name = null;
    @JsonProperty("sourceObjType")
    private String sourceObjType = null;
    @JsonProperty("sourceObjId")
    private String sourceObjId = null;
    @JsonProperty("sourceObjName")
    private String sourceObjName = null;
    @JsonProperty("description")
    private String description = null;
    @JsonProperty("sourceSystem")
    private String sourceSystem = null;
    @JsonProperty("sourceCsn")
    private Long sourceCsn = null;
    @JsonProperty("rootEventProbability")
    private Double rootEventProbability = null;
    @JsonProperty("meType")
    private String meType = null;
    @JsonProperty("moDn")
    private String moDn = null;
    @JsonProperty("nativeMoDn")
    private String nativeMoDn = null;
    @JsonProperty("moName")
    private String moName = null;
    @JsonProperty("moType")
    private String moType = null;
    @JsonProperty("rawData")
    private String rawData = null;

    
    /**
    **/
    public String getEventId() {
        return eventId;
    }
    public void setEventId(String eventId) {
        this.eventId = eventId;
    }
    
    /**
    **/
    public Long getCsn() {
        return csn;
    }
    public void setCsn(Long csn) {
        this.csn = csn;
    }
    
    /**
    **/
    public String getTenantId() {
        return tenantId;
    }
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    /**
    **/
    public String getIncidentId() {
        return incidentId;
    }
    public void setIncidentId(String incidentId) {
        this.incidentId = incidentId;
    }
    
    /**
    **/
    public String getDomain() {
        return domain;
    }
    public void setDomain(String domain) {
        this.domain = domain;
    }
    
    /**
    **/
    public String getType() {
        return type;
    }
    public void setType(String type) {
        this.type = type;
    }
    
    /**
    **/
    public String getStatus() {
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }
    
    /**
    **/
    public Long getArriveTime() {
        return arriveTime;
    }
    public void setArriveTime(Long arriveTime) {
        this.arriveTime = arriveTime;
    }
    
    /**
    **/
    public Long getOccurTime() {
        return occurTime;
    }
    public void setOccurTime(Long occurTime) {
        this.occurTime = occurTime;
    }
    
    /**
    **/
    public Long getClearTime() {
        return clearTime;
    }
    public void setClearTime(Long clearTime) {
        this.clearTime = clearTime;
    }
    
    /**
    **/
    public String getSeverity() {
        return severity;
    }
    public void setSeverity(String severity) {
        this.severity = severity;
    }
    
    /**
    **/
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    
    /**
    **/
    public String getSourceObjType() {
        return sourceObjType;
    }
    public void setSourceObjType(String sourceObjType) {
        this.sourceObjType = sourceObjType;
    }
    
    /**
    **/
    public String getSourceObjId() {
        return sourceObjId;
    }
    public void setSourceObjId(String sourceObjId) {
        this.sourceObjId = sourceObjId;
    }
    
    /**
    **/
    public String getSourceObjName() {
        return sourceObjName;
    }
    public void setSourceObjName(String sourceObjName) {
        this.sourceObjName = sourceObjName;
    }
    
    /**
    **/
    public String getDescription() {
        return description;
    }
    public void setDescription(String description) {
        this.description = description;
    }
    
    /**
    **/
    public String getSourceSystem() {
        return sourceSystem;
    }
    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }
    
    /**
    **/
    public Long getSourceCsn() {
        return sourceCsn;
    }
    public void setSourceCsn(Long sourceCsn) {
        this.sourceCsn = sourceCsn;
    }
    
    /**
    **/
    public Double getRootEventProbability() {
        return rootEventProbability;
    }
    public void setRootEventProbability(Double rootEventProbability) {
        this.rootEventProbability = rootEventProbability;
    }
    
    /**
    **/
    public String getMeType() {
        return meType;
    }
    public void setMeType(String meType) {
        this.meType = meType;
    }
    
    /**
    **/
    public String getMoDn() {
        return moDn;
    }
    public void setMoDn(String moDn) {
        this.moDn = moDn;
    }
    
    /**
    **/
    public String getNativeMoDn() {
        return nativeMoDn;
    }
    public void setNativeMoDn(String nativeMoDn) {
        this.nativeMoDn = nativeMoDn;
    }
    
    /**
    **/
    public String getMoName() {
        return moName;
    }
    public void setMoName(String moName) {
        this.moName = moName;
    }
    
    /**
    **/
    public String getMoType() {
        return moType;
    }
    public void setMoType(String moType) {
        this.moType = moType;
    }
    
    /**
    **/
    public String getRawData() {
        return rawData;
    }
    public void setRawData(String rawData) {
        this.rawData = rawData;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

