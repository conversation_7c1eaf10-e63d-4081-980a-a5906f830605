package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import java.util.*;

/**
 * 关键词项
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class KeywordItem     {
    
    
    @JsonProperty("name")
    private String name = null;
    public enum TypeEnum { 
    
    
         @JsonProperty("ALARM_ID") ALARM_ID,  @JsonProperty("ALARM_NAME") ALARM_NAME,  @JsonProperty("ERROR_CODE") ERROR_CODE,  @JsonProperty("MEAS_TYPE_KEY") MEAS_TYPE_KEY,  @JsonProperty("NE_TYPE") NE_TYPE,  @JsonProperty("TASK_NAME") TASK_NAME, ; 
        
    };
    @JsonProperty("type")
    private TypeEnum type = null;
    @JsonProperty("source")
    private String source = null;
    @JsonProperty("alias")
    private List<String> alias = new ArrayList<String>();
    public enum SolutionNameEnum { 
    
    
         @JsonProperty("CBS") CBS,  @JsonProperty("DV") DV, ; 
        
    };
    @JsonProperty("solutionName")
    private SolutionNameEnum solutionName = null;
    @JsonProperty("knowledgeIdList")
    private List<String> knowledgeIdList = new ArrayList<String>();

    
    /**
     * 关键词名称
    **/
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    
    /**
     * 关键词类型
    **/
    public TypeEnum getType() {
        return type;
    }
    public void setType(TypeEnum type) {
        this.type = type;
    }
    
    /**
     * 来源
    **/
    public String getSource() {
        return source;
    }
    public void setSource(String source) {
        this.source = source;
    }
    
    /**
     * 别名列表
    **/
    public List<String> getAlias() {
        return alias;
    }
    public void setAlias(List<String> alias) {
        this.alias = alias;
    }
    
    /**
     * 解决方案名称
    **/
    public SolutionNameEnum getSolutionName() {
        return solutionName;
    }
    public void setSolutionName(SolutionNameEnum solutionName) {
        this.solutionName = solutionName;
    }
    
    /**
     * 知识ID列表
    **/
    public List<String> getKnowledgeIdList() {
        return knowledgeIdList;
    }
    public void setKnowledgeIdList(List<String> knowledgeIdList) {
        this.knowledgeIdList = knowledgeIdList;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

