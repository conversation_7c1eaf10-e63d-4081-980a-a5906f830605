package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import java.util.*;

@JsonIgnoreProperties(ignoreUnknown = true)
public class LoginUserInfo     {
    
    
    @JsonProperty("resourceGroupIds")
    private List<String> resourceGroupIds = new ArrayList<String>();
    @JsonProperty("roleIdList")
    private List<Integer> roleIdList = new ArrayList<Integer>();
    @JsonProperty("userId")
    private String userId = null;
    @JsonProperty("userName")
    private String userName = null;
    @JsonProperty("admin")
    private Boolean admin = null;
    @JsonProperty("needDomain")
    private Boolean needDomain = null;

    
    /**
    **/
    public List<String> getResourceGroupIds() {
        return resourceGroupIds;
    }
    public void setResourceGroupIds(List<String> resourceGroupIds) {
        this.resourceGroupIds = resourceGroupIds;
    }
    
    /**
    **/
    public List<Integer> getRoleIdList() {
        return roleIdList;
    }
    public void setRoleIdList(List<Integer> roleIdList) {
        this.roleIdList = roleIdList;
    }
    
    /**
     * 用户id
    **/
    public String getUserId() {
        return userId;
    }
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    /**
     * 用户名称
    **/
    public String getUserName() {
        return userName;
    }
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    /**
    **/
    public Boolean getAdmin() {
        return admin;
    }
    public void setAdmin(Boolean admin) {
        this.admin = admin;
    }
    
    /**
    **/
    public Boolean getNeedDomain() {
        return needDomain;
    }
    public void setNeedDomain(Boolean needDomain) {
        this.needDomain = needDomain;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

