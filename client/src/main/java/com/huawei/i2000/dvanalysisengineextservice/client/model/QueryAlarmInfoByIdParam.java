package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
public class QueryAlarmInfoByIdParam     {
    
    
    @JsonProperty("alarmId")
    private String alarmId = null;
    @JsonProperty("neName")
    private String neName = null;

    
    /**
    **/
    public String getAlarmId() {
        return alarmId;
    }
    public void setAlarmId(String alarmId) {
        this.alarmId = alarmId;
    }
    
    /**
    **/
    public String getNeName() {
        return neName;
    }
    public void setNeName(String neName) {
        this.neName = neName;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

