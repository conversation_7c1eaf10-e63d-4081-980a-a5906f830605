package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkflowExecuteRequest     {
    
    
    @JsonProperty("scene")
    private String scene = null;
    @JsonProperty("faultObject")
    private String faultObject = null;
    @JsonProperty("occurUtc")
    private String occurUtc = null;
    @JsonProperty("csn")
    private String csn = null;
    @JsonProperty("address")
    private String address = null;

    
    /**
    **/
    public String getScene() {
        return scene;
    }
    public void setScene(String scene) {
        this.scene = scene;
    }
    
    /**
    **/
    public String getFaultObject() {
        return faultObject;
    }
    public void setFaultObject(String faultObject) {
        this.faultObject = faultObject;
    }
    
    /**
    **/
    public String getOccurUtc() {
        return occurUtc;
    }
    public void setOccurUtc(String occurUtc) {
        this.occurUtc = occurUtc;
    }
    
    /**
    **/
    public String getCsn() {
        return csn;
    }
    public void setCsn(String csn) {
        this.csn = csn;
    }
    
    /**
    **/
    public String getAddress() {
        return address;
    }
    public void setAddress(String address) {
        this.address = address;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

