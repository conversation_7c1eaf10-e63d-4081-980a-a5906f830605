package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import com.huawei.i2000.dvanalysisengineextservice.client.model.CommonParameter;
import com.huawei.i2000.dvanalysisengineextservice.client.model.RegexInfo;
import java.util.*;
import java.util.HashMap;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class ExecuteParameter extends CommonParameter    {
    
    
    @JsonProperty("scriptContent")
    private byte[] scriptContent = null;
    @JsonProperty("whoIsCallingController")
    private String whoIsCallingController = "unknown";
    @JsonProperty("resourceId")
    private String resourceId = null;
    @JsonProperty("useSecBox")
    private Boolean useSecBox = false;
    @JsonProperty("cfgFileId")
    private String cfgFileId = null;
    @JsonProperty("restartPlugin")
    private Boolean restartPlugin = false;
    @JsonProperty("ipParamMap")
    private Map<String, String> ipParamMap = new HashMap<String, String>();
    @JsonProperty("ips")
    private List<String> ips = new ArrayList<String>();
    @JsonProperty("ipEnvMap")
    private Map<String, Map<String, String>> ipEnvMap = new HashMap<String, Map<String, String>>();
    @JsonProperty("security")
    private Boolean security = false;
    @JsonProperty("successKeyword")
    private String successKeyword = null;
    @JsonProperty("ttlDay")
    private Integer ttlDay = 7;
    @JsonProperty("scriptMd5")
    private String scriptMd5 = null;
    @JsonProperty("timeoutSeconds")
    private Integer timeoutSeconds = 0;
    @JsonProperty("carePrecess")
    private Boolean carePrecess = false;
    @JsonProperty("zipTemplate")
    private Boolean zipTemplate = false;
    @JsonProperty("useInside")
    private Boolean useInside = true;
    @JsonProperty("scriptParams")
    private String scriptParams = null;
    @JsonProperty("regexInfos")
    private List<RegexInfo> regexInfos = new ArrayList<RegexInfo>();
    @JsonProperty("username")
    private String username = null;
    @JsonProperty("priorVM")
    private Boolean priorVM = true;

    
    /**
    **/
    public byte[] getScriptContent() {
        return scriptContent;
    }
    public void setScriptContent(byte[] scriptContent) {
        this.scriptContent = scriptContent;
    }
    
    /**
    **/
    public String getWhoIsCallingController() {
        return whoIsCallingController;
    }
    public void setWhoIsCallingController(String whoIsCallingController) {
        this.whoIsCallingController = whoIsCallingController;
    }
    
    /**
    **/
    public String getResourceId() {
        return resourceId;
    }
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }
    
    /**
    **/
    public Boolean getUseSecBox() {
        return useSecBox;
    }
    public void setUseSecBox(Boolean useSecBox) {
        this.useSecBox = useSecBox;
    }
    
    /**
    **/
    public String getCfgFileId() {
        return cfgFileId;
    }
    public void setCfgFileId(String cfgFileId) {
        this.cfgFileId = cfgFileId;
    }
    
    /**
    **/
    public Boolean getRestartPlugin() {
        return restartPlugin;
    }
    public void setRestartPlugin(Boolean restartPlugin) {
        this.restartPlugin = restartPlugin;
    }
    
    /**
    **/
    public Map<String, String> getIpParamMap() {
        return ipParamMap;
    }
    public void setIpParamMap(Map<String, String> ipParamMap) {
        this.ipParamMap = ipParamMap;
    }
    
    /**
    **/
    public List<String> getIps() {
        return ips;
    }
    public void setIps(List<String> ips) {
        this.ips = ips;
    }
    
    /**
    **/
    public Map<String, Map<String, String>> getIpEnvMap() {
        return ipEnvMap;
    }
    public void setIpEnvMap(Map<String, Map<String, String>> ipEnvMap) {
        this.ipEnvMap = ipEnvMap;
    }
    
    /**
    **/
    public Boolean getSecurity() {
        return security;
    }
    public void setSecurity(Boolean security) {
        this.security = security;
    }
    
    /**
    **/
    public String getSuccessKeyword() {
        return successKeyword;
    }
    public void setSuccessKeyword(String successKeyword) {
        this.successKeyword = successKeyword;
    }
    
    /**
    **/
    public Integer getTtlDay() {
        return ttlDay;
    }
    public void setTtlDay(Integer ttlDay) {
        this.ttlDay = ttlDay;
    }
    
    /**
    **/
    public String getScriptMd5() {
        return scriptMd5;
    }
    public void setScriptMd5(String scriptMd5) {
        this.scriptMd5 = scriptMd5;
    }
    
    /**
    **/
    public Integer getTimeoutSeconds() {
        return timeoutSeconds;
    }
    public void setTimeoutSeconds(Integer timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
    
    /**
    **/
    public Boolean getCarePrecess() {
        return carePrecess;
    }
    public void setCarePrecess(Boolean carePrecess) {
        this.carePrecess = carePrecess;
    }
    
    /**
    **/
    public Boolean getZipTemplate() {
        return zipTemplate;
    }
    public void setZipTemplate(Boolean zipTemplate) {
        this.zipTemplate = zipTemplate;
    }
    
    /**
    **/
    public Boolean getUseInside() {
        return useInside;
    }
    public void setUseInside(Boolean useInside) {
        this.useInside = useInside;
    }
    
    /**
    **/
    public String getScriptParams() {
        return scriptParams;
    }
    public void setScriptParams(String scriptParams) {
        this.scriptParams = scriptParams;
    }
    
    /**
    **/
    public List<RegexInfo> getRegexInfos() {
        return regexInfos;
    }
    public void setRegexInfos(List<RegexInfo> regexInfos) {
        this.regexInfos = regexInfos;
    }
    
    /**
    **/
    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }
    
    /**
    **/
    public Boolean getPriorVM() {
        return priorVM;
    }
    public void setPriorVM(Boolean priorVM) {
        this.priorVM = priorVM;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

