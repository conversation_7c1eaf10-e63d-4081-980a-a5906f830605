package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
public class CommonParameter     {
    
    
    @JsonProperty("username")
    private String username = null;
    @JsonProperty("timeoutSeconds")
    private Integer timeoutSeconds = 0;
    @JsonProperty("carePrecess")
    private Boolean carePrecess = false;
    @JsonProperty("ttlDay")
    private Integer ttlDay = 7;
    @JsonProperty("whoIsCallingController")
    private String whoIsCallingController = "unknown";
    @JsonProperty("cfgFileId")
    private String cfgFileId = null;
    @JsonProperty("restartPlugin")
    private Boolean restartPlugin = false;
    @JsonProperty("priorVM")
    private Boolean priorVM = true;

    
    /**
    **/
    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }
    
    /**
    **/
    public Integer getTimeoutSeconds() {
        return timeoutSeconds;
    }
    public void setTimeoutSeconds(Integer timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
    
    /**
    **/
    public Boolean getCarePrecess() {
        return carePrecess;
    }
    public void setCarePrecess(Boolean carePrecess) {
        this.carePrecess = carePrecess;
    }
    
    /**
    **/
    public Integer getTtlDay() {
        return ttlDay;
    }
    public void setTtlDay(Integer ttlDay) {
        this.ttlDay = ttlDay;
    }
    
    /**
    **/
    public String getWhoIsCallingController() {
        return whoIsCallingController;
    }
    public void setWhoIsCallingController(String whoIsCallingController) {
        this.whoIsCallingController = whoIsCallingController;
    }
    
    /**
    **/
    public String getCfgFileId() {
        return cfgFileId;
    }
    public void setCfgFileId(String cfgFileId) {
        this.cfgFileId = cfgFileId;
    }
    
    /**
    **/
    public Boolean getRestartPlugin() {
        return restartPlugin;
    }
    public void setRestartPlugin(Boolean restartPlugin) {
        this.restartPlugin = restartPlugin;
    }
    
    /**
    **/
    public Boolean getPriorVM() {
        return priorVM;
    }
    public void setPriorVM(Boolean priorVM) {
        this.priorVM = priorVM;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

