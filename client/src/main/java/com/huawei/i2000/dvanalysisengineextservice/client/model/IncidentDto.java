package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import com.huawei.i2000.dvanalysisengineextservice.client.model.CaseInfo;
import com.huawei.i2000.dvanalysisengineextservice.client.model.EventDto;
import java.util.*;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IncidentDto     {
    
    
    @JsonProperty("tenantId")
    private String tenantId = null;
    @JsonProperty("incidentId")
    private String incidentId = null;
    @JsonProperty("name")
    private String name = null;
    @JsonProperty("csn")
    private Long csn = null;
    @JsonProperty("category")
    private String category = null;
    @JsonProperty("subCategory")
    private String subCategory = null;
    @JsonProperty("incidentType")
    private Integer incidentType = null;
    @JsonProperty("status")
    private String status = null;
    @JsonProperty("ackStatus")
    private Integer ackStatus = 0;
    @JsonProperty("clearStatus")
    private Integer clearStatus = 0;
    @JsonProperty("createTime")
    private Long createTime = null;
    @JsonProperty("occurTime")
    private Long occurTime = null;
    @JsonProperty("clearTime")
    private Long clearTime = null;
    @JsonProperty("startTime")
    private Long startTime = null;
    @JsonProperty("endTime")
    private Long endTime = null;
    @JsonProperty("priority")
    private String priority = null;
    @JsonProperty("description")
    private String description = null;
    @JsonProperty("repairAdvice")
    private String repairAdvice = null;
    @JsonProperty("sourceObjType")
    private String sourceObjType = null;
    @JsonProperty("sourceObjId")
    private String sourceObjId = null;
    @JsonProperty("sourceObjName")
    private String sourceObjName = null;
    @JsonProperty("events")
    private List<EventDto> events = new ArrayList<EventDto>();
    @JsonProperty("cases")
    private List<CaseInfo> cases = new ArrayList<CaseInfo>();

    
    /**
    **/
    public String getTenantId() {
        return tenantId;
    }
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    /**
    **/
    public String getIncidentId() {
        return incidentId;
    }
    public void setIncidentId(String incidentId) {
        this.incidentId = incidentId;
    }
    
    /**
    **/
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    
    /**
    **/
    public Long getCsn() {
        return csn;
    }
    public void setCsn(Long csn) {
        this.csn = csn;
    }
    
    /**
    **/
    public String getCategory() {
        return category;
    }
    public void setCategory(String category) {
        this.category = category;
    }
    
    /**
    **/
    public String getSubCategory() {
        return subCategory;
    }
    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }
    
    /**
    **/
    public Integer getIncidentType() {
        return incidentType;
    }
    public void setIncidentType(Integer incidentType) {
        this.incidentType = incidentType;
    }
    
    /**
    **/
    public String getStatus() {
        return status;
    }
    public void setStatus(String status) {
        this.status = status;
    }
    
    /**
    **/
    public Integer getAckStatus() {
        return ackStatus;
    }
    public void setAckStatus(Integer ackStatus) {
        this.ackStatus = ackStatus;
    }
    
    /**
    **/
    public Integer getClearStatus() {
        return clearStatus;
    }
    public void setClearStatus(Integer clearStatus) {
        this.clearStatus = clearStatus;
    }
    
    /**
    **/
    public Long getCreateTime() {
        return createTime;
    }
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }
    
    /**
    **/
    public Long getOccurTime() {
        return occurTime;
    }
    public void setOccurTime(Long occurTime) {
        this.occurTime = occurTime;
    }
    
    /**
    **/
    public Long getClearTime() {
        return clearTime;
    }
    public void setClearTime(Long clearTime) {
        this.clearTime = clearTime;
    }
    
    /**
    **/
    public Long getStartTime() {
        return startTime;
    }
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }
    
    /**
    **/
    public Long getEndTime() {
        return endTime;
    }
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }
    
    /**
    **/
    public String getPriority() {
        return priority;
    }
    public void setPriority(String priority) {
        this.priority = priority;
    }
    
    /**
    **/
    public String getDescription() {
        return description;
    }
    public void setDescription(String description) {
        this.description = description;
    }
    
    /**
    **/
    public String getRepairAdvice() {
        return repairAdvice;
    }
    public void setRepairAdvice(String repairAdvice) {
        this.repairAdvice = repairAdvice;
    }
    
    /**
    **/
    public String getSourceObjType() {
        return sourceObjType;
    }
    public void setSourceObjType(String sourceObjType) {
        this.sourceObjType = sourceObjType;
    }
    
    /**
    **/
    public String getSourceObjId() {
        return sourceObjId;
    }
    public void setSourceObjId(String sourceObjId) {
        this.sourceObjId = sourceObjId;
    }
    
    /**
    **/
    public String getSourceObjName() {
        return sourceObjName;
    }
    public void setSourceObjName(String sourceObjName) {
        this.sourceObjName = sourceObjName;
    }
    
    /**
    **/
    public List<EventDto> getEvents() {
        return events;
    }
    public void setEvents(List<EventDto> events) {
        this.events = events;
    }
    
    /**
    **/
    public List<CaseInfo> getCases() {
        return cases;
    }
    public void setCases(List<CaseInfo> cases) {
        this.cases = cases;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

