package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


/**
 * 关键词查询请求参数
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class KeywordSearchRequest     {
    
    
    public enum KeywordTypeEnum { 
    
    
         @JsonProperty("ALARM_ID") ALARM_ID,  @JsonProperty("ALARM_NAME") ALARM_NAME,  @JsonProperty("ERROR_CODE") ERROR_CODE,  @JsonProperty("MEAS_TYPE_KEY") MEAS_TYPE_KEY,  @JsonProperty("NE_TYPE") NE_TYPE,  @JsonProperty("TASK_NAME") TASK_NAME, ; 
        
    };
    @JsonProperty("keywordType")
    private KeywordTypeEnum keywordType = null;
    @JsonProperty("input")
    private String input = null;
    @JsonProperty("maxSize")
    private Integer maxSize = 10;

    
    /**
     * 关键词类型
    **/
    public KeywordTypeEnum getKeywordType() {
        return keywordType;
    }
    public void setKeywordType(KeywordTypeEnum keywordType) {
        this.keywordType = keywordType;
    }
    
    /**
     * 用户输入的查询文本，用于模糊匹配
    **/
    public String getInput() {
        return input;
    }
    public void setInput(String input) {
        this.input = input;
    }
    
    /**
     * 返回结果的最大数量，默认10
     * minimum: 1.0
     * maximum: 100.0
    **/
    public Integer getMaxSize() {
        return maxSize;
    }
    public void setMaxSize(Integer maxSize) {
        this.maxSize = maxSize;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

