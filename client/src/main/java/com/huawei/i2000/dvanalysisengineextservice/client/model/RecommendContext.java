package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import java.util.*;

/**
 * 智能提示请求里面的Context,表示上轮对话
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class RecommendContext     {
    
    
    @JsonProperty("question")
    private String question = null;
    @JsonProperty("answer")
    private List<String> answer = new ArrayList<String>();

    
    /**
     * 问题
    **/
    public String getQuestion() {
        return question;
    }
    public void setQuestion(String question) {
        this.question = question;
    }
    
    /**
     * 答案
    **/
    public List<String> getAnswer() {
        return answer;
    }
    public void setAnswer(List<String> answer) {
        this.answer = answer;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

