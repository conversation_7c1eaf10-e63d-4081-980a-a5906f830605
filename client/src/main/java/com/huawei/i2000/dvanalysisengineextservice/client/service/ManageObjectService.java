package com.huawei.i2000.dvanalysisengineextservice.client.service;

import java.util.*;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.huawei.i2000.dvanalysisengineextservice.client.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineextservice.util.RestClientUtil;
import com.huawei.bsp.remoteservice.exception.ExceptionArgs;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.clientsdk.HttpUtil;
import com.huawei.bsp.roa.util.clientsdk.JsonUtil;
import com.huawei.bsp.roa.util.restclient.Restful;
import com.huawei.bsp.roa.util.restclient.RestfulAsyncCallback;
import com.huawei.bsp.roa.util.restclient.RestfulFactory;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import java.util.Map;
import java.util.HashMap;

public class ManageObjectService {
    private RestfulOptions option = null;
    private Map<String, String> contextHeader = new HashMap<String, String>(1);
    private Restful restFull = null;
    
    public ManageObjectService() {
    
    }
    
        
    public ManageObjectService(Restful restFullout) {
        this.restFull = restFullout;
    }

  
    /**
     * managedobject_connect_status
     * 大模型查询服务
     * @param neName 
     * @param neType 
     * @param ip 
     * @return ResponseResult
     */
    public ResponseResult connectStatus(String neName, String neType, String ip) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/managedobject/connectstatus";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (neName != null) {
            path = RestClientUtil.parameterToString(path, "", "neName", neName);
        }
        if (neType != null) {
            path = RestClientUtil.parameterToString(path, "", "neType", neType);
        }
        if (ip != null) {
            path = RestClientUtil.parameterToString(path, "", "ip", ip);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "connectStatus"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * managedobject_connect_status
     * 大模型查询服务
     * @param neName 
     * @param neType 
     * @param ip 
     * @param callback async callback object
     */
    public void asyncconnectStatus(String neName, String neType, String ip, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/managedobject/connectstatus";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (neName != null) {
            path = RestClientUtil.parameterToString(path, "", "neName", neName);
        }
        if (neType != null) {
            path = RestClientUtil.parameterToString(path, "", "neType", neType);
        }
        if (ip != null) {
            path = RestClientUtil.parameterToString(path, "", "ip", ip);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * managedobject_info
     * 大模型查询服务
     * @param dn 
     * @return ResponseResult
     */
    public ResponseResult getEventList(String dn) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/managedobject/eventlist";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (dn != null) {
            path = RestClientUtil.parameterToString(path, "", "dn", dn);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "getEventList"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * managedobject_info
     * 大模型查询服务
     * @param dn 
     * @param callback async callback object
     */
    public void asyncgetEventList(String dn, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/managedobject/eventlist";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (dn != null) {
            path = RestClientUtil.parameterToString(path, "", "dn", dn);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * managedobject_health
     * 大模型查询服务
     * @param neName 
     * @return ResponseResult
     */
    public ResponseResult healthStatus(String neName) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/managedobject/healthstatus";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (neName != null) {
            path = RestClientUtil.parameterToString(path, "", "neName", neName);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "healthStatus"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * managedobject_health
     * 大模型查询服务
     * @param neName 
     * @param callback async callback object
     */
    public void asynchealthStatus(String neName, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/managedobject/healthstatus";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (neName != null) {
            path = RestClientUtil.parameterToString(path, "", "neName", neName);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  /**
     * managedobject_info
     * 大模型查询服务
     * @param neName 
     * @return ResponseResult
     */
    public ResponseResult topoStatus(String neName) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/managedobject/topostatus";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (neName != null) {
            path = RestClientUtil.parameterToString(path, "", "neName", neName);
        }
        
        
        

        RestfulResponse response = RestClientUtil.invokeMethod("GET", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "topoStatus"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * managedobject_info
     * 大模型查询服务
     * @param neName 
     * @param callback async callback object
     */
    public void asynctopoStatus(String neName, RestfulAsyncCallback callback) throws ServiceException {
        
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/managedobject/topostatus";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        if (neName != null) {
            path = RestClientUtil.parameterToString(path, "", "neName", neName);
        }
        
        
        
        
        RestClientUtil.invokeAsyncMethod("GET", path, parameters, restFull, option, callback);
    }
  
  
    public Map<String, String> getContextHeader() {
        return contextHeader;
    }

    public void setContextHeader(Map<String, String> contextHeader) {
        this.contextHeader = contextHeader;
    }
    
        public RestfulOptions getOption() {
        return option;
    }


    public void setOption(RestfulOptions option) {
        this.option = option;
    }
    
}
