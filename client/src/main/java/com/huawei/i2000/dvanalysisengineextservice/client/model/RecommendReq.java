package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import com.huawei.i2000.dvanalysisengineextservice.client.model.RecommendContext;
import java.util.*;

/**
 * 智能提示请求
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class RecommendReq     {
    
    
    @JsonProperty("userQuestion")
    private String userQuestion = null;
    @JsonProperty("context")
    private List<RecommendContext> context = new ArrayList<RecommendContext>();

    
    /**
     * 用户问题
    **/
    public String getUserQuestion() {
        return userQuestion;
    }
    public void setUserQuestion(String userQuestion) {
        this.userQuestion = userQuestion;
    }
    
    /**
     * 上下文
    **/
    public List<RecommendContext> getContext() {
        return context;
    }
    public void setContext(List<RecommendContext> context) {
        this.context = context;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

