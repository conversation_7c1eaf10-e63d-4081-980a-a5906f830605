package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import com.huawei.i2000.dvanalysisengineextservice.client.model.IncidentDto;
import java.util.*;

@JsonIgnoreProperties(ignoreUnknown = true)
public class IncidentDtoList     {
    
    
    @JsonProperty("incidents")
    private List<IncidentDto> incidents = new ArrayList<IncidentDto>();

    
    /**
    **/
    public List<IncidentDto> getIncidents() {
        return incidents;
    }
    public void setIncidents(List<IncidentDto> incidents) {
        this.incidents = incidents;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

