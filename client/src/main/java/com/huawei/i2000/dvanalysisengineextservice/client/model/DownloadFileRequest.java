package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import com.huawei.i2000.dvanalysisengineextservice.client.model.DownloadParameter;
import java.util.*;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DownloadFileRequest     {
    
    
    @JsonProperty("filePath")
    private String filePath = null;
    @JsonProperty("downloadParameter")
    private DownloadParameter downloadParameter = null;
    @JsonProperty("ipList")
    private List<String> ipList = new ArrayList<String>();

    
    /**
    **/
    public String getFilePath() {
        return filePath;
    }
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    /**
    **/
    public DownloadParameter getDownloadParameter() {
        return downloadParameter;
    }
    public void setDownloadParameter(DownloadParameter downloadParameter) {
        this.downloadParameter = downloadParameter;
    }
    
    /**
    **/
    public List<String> getIpList() {
        return ipList;
    }
    public void setIpList(List<String> ipList) {
        this.ipList = ipList;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

