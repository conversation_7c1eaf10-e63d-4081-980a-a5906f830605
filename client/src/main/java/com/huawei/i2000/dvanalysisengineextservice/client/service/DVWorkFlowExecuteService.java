package com.huawei.i2000.dvanalysisengineextservice.client.service;

import java.util.*;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.huawei.i2000.dvanalysisengineextservice.client.model.WorkflowExecuteRequest;
import com.huawei.i2000.dvanalysisengineextservice.client.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineextservice.util.RestClientUtil;
import com.huawei.bsp.remoteservice.exception.ExceptionArgs;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.clientsdk.HttpUtil;
import com.huawei.bsp.roa.util.clientsdk.JsonUtil;
import com.huawei.bsp.roa.util.restclient.Restful;
import com.huawei.bsp.roa.util.restclient.RestfulAsyncCallback;
import com.huawei.bsp.roa.util.restclient.RestfulFactory;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import java.util.Map;
import java.util.HashMap;

public class DVWorkFlowExecuteService {
    private RestfulOptions option = null;
    private Map<String, String> contextHeader = new HashMap<String, String>(1);
    private Restful restFull = null;
    
    public DVWorkFlowExecuteService() {
    
    }
    
        
    public DVWorkFlowExecuteService(Restful restFullout) {
        this.restFull = restFullout;
    }

  
    /**
     * workbench_execute
     * 执行流程编排
     * @param executeWorkflow 执行流程编排参数
     * @return ResponseResult
     */
    public ResponseResult executeWorkflow(WorkflowExecuteRequest executeWorkflow) throws ServiceException {
        Object postBody = executeWorkflow;
    
        // verify the required parameter 'executeWorkflow' is set
        if (executeWorkflow == null) {
            throw new ServiceException("Missing the required parameter 'executeWorkflow' when calling executeWorkflow", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/workflow/execute";
        if(restFull == null){ 
            restFull = RestfulFactory.getRestInstance();
        }
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }

        RestfulResponse response = RestClientUtil.invokeMethod("POST", path, parameters, restFull, option);
        if(response != null) {
            if (response.getStatus() / 100 == 2) {
                try {
                    return JsonUtil.unMarshal(response.getResponseContent(), new TypeReference<ResponseResult>(){});    
                    
                }
                catch(Exception ex) {
                    throw new ServiceException("bad_param", 400);
                }
            }
            else {
                ExceptionArgs args = new ExceptionArgs();
                args.setDescArgs(new String[]{"Exception Module: " + "executeWorkflow"});
                args.setDetailArgs(new String[]{response.getResponseContent()});
                throw new ServiceException("bad status", response.getStatus(), args);
            }
        }
        else {
            return null;
        }
    }
    
    /**
     * async method
     * workbench_execute
     * 执行流程编排
     * @param executeWorkflow 执行流程编排参数
     * @param callback async callback object
     */
    public void asyncexecuteWorkflow(WorkflowExecuteRequest executeWorkflow, RestfulAsyncCallback callback) throws ServiceException {
        Object postBody = executeWorkflow;
    
        // verify the required parameter 'executeWorkflow' is set
        if (executeWorkflow == null) {
            throw new ServiceException("Missing the required parameter 'executeWorkflow' when calling executeWorkflow", 400);
        }
    
        // create path and map variables
        String path = "/rest/dvanalysisengineextservice/v1/workflow/execute";
        if(restFull == null) { 
            restFull = RestfulFactory.getRestInstance();
        }     
        RestfulParametes parameters = new RestfulParametes();
        if (!contextHeader.isEmpty()) {
            parameters.setHeaderMap(contextHeader);
        }
        
        
        if (postBody != null) {
            try {
                final String[] contentTypes = {
                        "application/json"
                };
                final String contentType = HttpUtil.selectHeaderContentType(contentTypes);
                parameters.putHttpContextHeader("Content-Type", contentType);
                parameters.setRawData(JsonUtil.marshal(postBody));
            }
            catch (IOException e) {
                throw new ServiceException("bad_param", 400);
            }
        }
        
        RestClientUtil.invokeAsyncMethod("POST", path, parameters, restFull, option, callback);
    }
  
  
    public Map<String, String> getContextHeader() {
        return contextHeader;
    }

    public void setContextHeader(Map<String, String> contextHeader) {
        this.contextHeader = contextHeader;
    }
    
        public RestfulOptions getOption() {
        return option;
    }


    public void setOption(RestfulOptions option) {
        this.option = option;
    }
    
}
