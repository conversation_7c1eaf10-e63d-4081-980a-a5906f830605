package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import java.math.BigDecimal;
import java.util.*;

@JsonIgnoreProperties(ignoreUnknown = true)
public class HistoryAggregateQueryModel     {
    
    
    @JsonProperty("pageIndex")
    private BigDecimal pageIndex = null;
    @JsonProperty("pageSize")
    private BigDecimal pageSize = null;
    @JsonProperty("beginTimestamp")
    private Long beginTimestamp = null;
    @JsonProperty("endTimestamp")
    private Long endTimestamp = null;
    @JsonProperty("moType")
    private String moType = null;
    @JsonProperty("measUnitKey")
    private String measUnitKey = null;
    @JsonProperty("measObjects")
    private List<String> measObjects = new ArrayList<String>();
    @JsonProperty("originalValues")
    private List<String> originalValues = new ArrayList<String>();
    @JsonProperty("dns")
    private List<String> dns = new ArrayList<String>();
    @JsonProperty("specifiedMeasTypes")
    private List<String> specifiedMeasTypes = new ArrayList<String>();
    @JsonProperty("autoLowerDimension")
    private Boolean autoLowerDimension = null;
    @JsonProperty("timeRange4LowerDimension")
    private List<Long> timeRange4LowerDimension = new ArrayList<Long>();

    
    /**
     * 查询页码
    **/
    public BigDecimal getPageIndex() {
        return pageIndex;
    }
    public void setPageIndex(BigDecimal pageIndex) {
        this.pageIndex = pageIndex;
    }
    
    /**
     * 每页查询的数据的大小
    **/
    public BigDecimal getPageSize() {
        return pageSize;
    }
    public void setPageSize(BigDecimal pageSize) {
        this.pageSize = pageSize;
    }
    
    /**
     * 数据查询的开始时间戳
    **/
    public Long getBeginTimestamp() {
        return beginTimestamp;
    }
    public void setBeginTimestamp(Long beginTimestamp) {
        this.beginTimestamp = beginTimestamp;
    }
    
    /**
     * 数据查询的结束时间戳
    **/
    public Long getEndTimestamp() {
        return endTimestamp;
    }
    public void setEndTimestamp(Long endTimestamp) {
        this.endTimestamp = endTimestamp;
    }
    
    /**
     * 网元类型
    **/
    public String getMoType() {
        return moType;
    }
    public void setMoType(String moType) {
        this.moType = moType;
    }
    
    /**
     * 测量单元key
    **/
    public String getMeasUnitKey() {
        return measUnitKey;
    }
    public void setMeasUnitKey(String measUnitKey) {
        this.measUnitKey = measUnitKey;
    }
    
    /**
     * 测量对象displayValue列表
    **/
    public List<String> getMeasObjects() {
        return measObjects;
    }
    public void setMeasObjects(List<String> measObjects) {
        this.measObjects = measObjects;
    }
    
    /**
     * 测量对象originalValue列表
    **/
    public List<String> getOriginalValues() {
        return originalValues;
    }
    public void setOriginalValues(List<String> originalValues) {
        this.originalValues = originalValues;
    }
    
    /**
     * DN列表
    **/
    public List<String> getDns() {
        return dns;
    }
    public void setDns(List<String> dns) {
        this.dns = dns;
    }
    
    /**
    **/
    public List<String> getSpecifiedMeasTypes() {
        return specifiedMeasTypes;
    }
    public void setSpecifiedMeasTypes(List<String> specifiedMeasTypes) {
        this.specifiedMeasTypes = specifiedMeasTypes;
    }
    
    /**
     * 是否开启自动降维
    **/
    public Boolean getAutoLowerDimension() {
        return autoLowerDimension;
    }
    public void setAutoLowerDimension(Boolean autoLowerDimension) {
        this.autoLowerDimension = autoLowerDimension;
    }
    
    /**
     * 计算降维的时间范围，格式为：[beginTimestamp, endTimeStamp]
    **/
    public List<Long> getTimeRange4LowerDimension() {
        return timeRange4LowerDimension;
    }
    public void setTimeRange4LowerDimension(List<Long> timeRange4LowerDimension) {
        this.timeRange4LowerDimension = timeRange4LowerDimension;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

