package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import com.huawei.i2000.dvanalysisengineextservice.client.model.KeywordItem;
import java.util.*;

/**
 * 关键词查询数据
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class KeywordSearchData     {
    
    
    @JsonProperty("keywords")
    private List<KeywordItem> keywords = new ArrayList<KeywordItem>();
    @JsonProperty("total")
    private Integer total = null;
    @JsonProperty("keywordType")
    private String keywordType = null;

    
    /**
     * 匹配的关键词列表
    **/
    public List<KeywordItem> getKeywords() {
        return keywords;
    }
    public void setKeywords(List<KeywordItem> keywords) {
        this.keywords = keywords;
    }
    
    /**
     * 总匹配数量
    **/
    public Integer getTotal() {
        return total;
    }
    public void setTotal(Integer total) {
        this.total = total;
    }
    
    /**
     * 查询的关键词类型
    **/
    public String getKeywordType() {
        return keywordType;
    }
    public void setKeywordType(String keywordType) {
        this.keywordType = keywordType;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

