package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import java.math.BigDecimal;

@JsonIgnoreProperties(ignoreUnknown = true)
public class HistoryDataQueryModel     {
    
    
    @JsonProperty("measUnitKey")
    private String measUnitKey = null;
    @JsonProperty("moType")
    private String moType = null;
    @JsonProperty("timeRangeMap")
    private Object timeRangeMap = null;
    @JsonProperty("pageIndex")
    private BigDecimal pageIndex = null;
    @JsonProperty("pageSize")
    private BigDecimal pageSize = null;
    @JsonProperty("tableType")
    private String tableType = "ORIGIN";
    @JsonProperty("dnOriginalValue2Index")
    private Object dnOriginalValue2Index = null;

    
    /**
     * 测量单元key值
    **/
    public String getMeasUnitKey() {
        return measUnitKey;
    }
    public void setMeasUnitKey(String measUnitKey) {
        this.measUnitKey = measUnitKey;
    }
    
    /**
     * 网元类型
    **/
    public String getMoType() {
        return moType;
    }
    public void setMoType(String moType) {
        this.moType = moType;
    }
    
    /**
     * Map<Long, Long>类型
    **/
    public Object getTimeRangeMap() {
        return timeRangeMap;
    }
    public void setTimeRangeMap(Object timeRangeMap) {
        this.timeRangeMap = timeRangeMap;
    }
    
    /**
     * 查询页码
    **/
    public BigDecimal getPageIndex() {
        return pageIndex;
    }
    public void setPageIndex(BigDecimal pageIndex) {
        this.pageIndex = pageIndex;
    }
    
    /**
     * 每页查询的数据的大小
    **/
    public BigDecimal getPageSize() {
        return pageSize;
    }
    public void setPageSize(BigDecimal pageSize) {
        this.pageSize = pageSize;
    }
    
    /**
     * 历史数据查询的表类型
    **/
    public String getTableType() {
        return tableType;
    }
    public void setTableType(String tableType) {
        this.tableType = tableType;
    }
    
    /**
     * Map<String, Map<String, Map<String, String>>>类型
    **/
    public Object getDnOriginalValue2Index() {
        return dnOriginalValue2Index;
    }
    public void setDnOriginalValue2Index(Object dnOriginalValue2Index) {
        this.dnOriginalValue2Index = dnOriginalValue2Index;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

