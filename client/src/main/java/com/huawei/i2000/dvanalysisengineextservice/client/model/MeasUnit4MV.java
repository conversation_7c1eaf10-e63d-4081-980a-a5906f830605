package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import com.huawei.i2000.dvanalysisengineextservice.client.model.MeasType4MV;
import java.util.*;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MeasUnit4MV     {
    
    
    @JsonProperty("icon")
    private String icon = null;
    @JsonProperty("name")
    private String name = null;
    @JsonProperty("measUnitKey")
    private String measUnitKey = null;
    @JsonProperty("dn")
    private String dn = null;
    @JsonProperty("hasMeasObj")
    private Boolean hasMeasObj = null;
    @JsonProperty("open")
    private Boolean open = null;
    @JsonProperty("chkDisabled")
    private Boolean chkDisabled = null;
    @JsonProperty("children")
    private List<MeasType4MV> children = new ArrayList<MeasType4MV>();

    
    /**
    **/
    public String getIcon() {
        return icon;
    }
    public void setIcon(String icon) {
        this.icon = icon;
    }
    
    /**
    **/
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    
    /**
    **/
    public String getMeasUnitKey() {
        return measUnitKey;
    }
    public void setMeasUnitKey(String measUnitKey) {
        this.measUnitKey = measUnitKey;
    }
    
    /**
    **/
    public String getDn() {
        return dn;
    }
    public void setDn(String dn) {
        this.dn = dn;
    }
    
    /**
    **/
    public Boolean getHasMeasObj() {
        return hasMeasObj;
    }
    public void setHasMeasObj(Boolean hasMeasObj) {
        this.hasMeasObj = hasMeasObj;
    }
    
    /**
    **/
    public Boolean getOpen() {
        return open;
    }
    public void setOpen(Boolean open) {
        this.open = open;
    }
    
    /**
    **/
    public Boolean getChkDisabled() {
        return chkDisabled;
    }
    public void setChkDisabled(Boolean chkDisabled) {
        this.chkDisabled = chkDisabled;
    }
    
    /**
    **/
    public List<MeasType4MV> getChildren() {
        return children;
    }
    public void setChildren(List<MeasType4MV> children) {
        this.children = children;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

