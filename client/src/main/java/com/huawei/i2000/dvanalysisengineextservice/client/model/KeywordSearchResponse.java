package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import com.huawei.i2000.dvanalysisengineextservice.client.model.KeywordSearchData;

/**
 * 关键词查询响应
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class KeywordSearchResponse     {
    
    
    @JsonProperty("resultCode")
    private Integer resultCode = null;
    @JsonProperty("resultMessage")
    private String resultMessage = null;
    @JsonProperty("data")
    private KeywordSearchData data = null;

    
    /**
     * 0:成功,-1失败
    **/
    public Integer getResultCode() {
        return resultCode;
    }
    public void setResultCode(Integer resultCode) {
        this.resultCode = resultCode;
    }
    
    /**
     * 在不正确的时候可以返回
    **/
    public String getResultMessage() {
        return resultMessage;
    }
    public void setResultMessage(String resultMessage) {
        this.resultMessage = resultMessage;
    }
    
    /**
     * 关键词列表数据
    **/
    public KeywordSearchData getData() {
        return data;
    }
    public void setData(KeywordSearchData data) {
        this.data = data;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

