package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
public class DownloadParameter     {
    
    
    @JsonProperty("username")
    private String username = null;
    @JsonProperty("invoker")
    private String invoker = null;

    
    /**
    **/
    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }
    
    /**
    **/
    public String getInvoker() {
        return invoker;
    }
    public void setInvoker(String invoker) {
        this.invoker = invoker;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

