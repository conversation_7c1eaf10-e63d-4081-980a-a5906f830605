package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
public class ResponseResult     {
    
    
    @JsonProperty("resultCode")
    private Integer resultCode = null;
    @JsonProperty("resultMessage")
    private String resultMessage = null;
    @JsonProperty("data")
    private Object data = null;

    
    /**
     * 0:成功,-1失败
    **/
    public Integer getResultCode() {
        return resultCode;
    }
    public void setResultCode(Integer resultCode) {
        this.resultCode = resultCode;
    }
    
    /**
     * 在不正确的时候可以返回
    **/
    public String getResultMessage() {
        return resultMessage;
    }
    public void setResultMessage(String resultMessage) {
        this.resultMessage = resultMessage;
    }
    
    /**
     * 数据
    **/
    public Object getData() {
        return data;
    }
    public void setData(Object data) {
        this.data = data;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

