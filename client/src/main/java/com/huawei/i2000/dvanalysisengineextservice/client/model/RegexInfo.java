package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
public class RegexInfo     {
    
    
    @JsonProperty("regex")
    private String regex = null;
    @JsonProperty("ttl")
    private Integer ttl = 0;

    
    /**
    **/
    public String getRegex() {
        return regex;
    }
    public void setRegex(String regex) {
        this.regex = regex;
    }
    
    /**
    **/
    public Integer getTtl() {
        return ttl;
    }
    public void setTtl(Integer ttl) {
        this.ttl = ttl;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

