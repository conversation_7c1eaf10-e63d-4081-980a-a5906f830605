package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import java.util.*;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MeasObjectVO     {
    
    
    @JsonProperty("i2kDisplayValue")
    private String i2kDisplayValue = null;
    @JsonProperty("originalValue")
    private String originalValue = null;
    @JsonProperty("indexValues")
    private List<String> indexValues = new ArrayList<String>();
    @JsonProperty("i2kValues")
    private List<String> i2kValues = new ArrayList<String>();
    @JsonProperty("indexes")
    private List<String> indexes = new ArrayList<String>();
    @JsonProperty("displayValue")
    private String displayValue = null;
    @JsonProperty("isAddedManlly")
    private Boolean isAddedManlly = null;
    @JsonProperty("keys")
    private List<String> keys = new ArrayList<String>();
    @JsonProperty("values")
    private List<String> values = new ArrayList<String>();

    
    /**
    **/
    public String getI2kDisplayValue() {
        return i2kDisplayValue;
    }
    public void setI2kDisplayValue(String i2kDisplayValue) {
        this.i2kDisplayValue = i2kDisplayValue;
    }
    
    /**
    **/
    public String getOriginalValue() {
        return originalValue;
    }
    public void setOriginalValue(String originalValue) {
        this.originalValue = originalValue;
    }
    
    /**
    **/
    public List<String> getIndexValues() {
        return indexValues;
    }
    public void setIndexValues(List<String> indexValues) {
        this.indexValues = indexValues;
    }
    
    /**
    **/
    public List<String> getI2kValues() {
        return i2kValues;
    }
    public void setI2kValues(List<String> i2kValues) {
        this.i2kValues = i2kValues;
    }
    
    /**
    **/
    public List<String> getIndexes() {
        return indexes;
    }
    public void setIndexes(List<String> indexes) {
        this.indexes = indexes;
    }
    
    /**
    **/
    public String getDisplayValue() {
        return displayValue;
    }
    public void setDisplayValue(String displayValue) {
        this.displayValue = displayValue;
    }
    
    /**
    **/
    public Boolean getIsAddedManlly() {
        return isAddedManlly;
    }
    public void setIsAddedManlly(Boolean isAddedManlly) {
        this.isAddedManlly = isAddedManlly;
    }
    
    /**
    **/
    public List<String> getKeys() {
        return keys;
    }
    public void setKeys(List<String> keys) {
        this.keys = keys;
    }
    
    /**
    **/
    public List<String> getValues() {
        return values;
    }
    public void setValues(List<String> values) {
        this.values = values;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

