package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import java.util.*;
import java.util.Map;

/**
 * 容灾切换流程参数--配置展示，不使用
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class AlarmParam     {
    
    
    @JsonProperty("alarms")
    private List<Map<String, String>> alarms = new ArrayList<Map<String, String>>();

    
    /**
     * List<Map<String, String>>类型
    **/
    public List<Map<String, String>> getAlarms() {
        return alarms;
    }
    public void setAlarms(List<Map<String, String>> alarms) {
        this.alarms = alarms;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

