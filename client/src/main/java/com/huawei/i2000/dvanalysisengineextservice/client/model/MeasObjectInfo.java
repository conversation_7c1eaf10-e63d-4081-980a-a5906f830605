package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import com.huawei.i2000.dvanalysisengineextservice.client.model.MeasObjectVO;
import java.util.*;

@JsonIgnoreProperties(ignoreUnknown = true)
public class MeasObjectInfo     {
    
    
    @JsonProperty("mos")
    private List<MeasObjectVO> mos = new ArrayList<MeasObjectVO>();
    @JsonProperty("indexes")
    private List<String> indexes = new ArrayList<String>();
    @JsonProperty("iempKeys")
    private List<String> iempKeys = new ArrayList<String>();

    
    /**
    **/
    public List<MeasObjectVO> getMos() {
        return mos;
    }
    public void setMos(List<MeasObjectVO> mos) {
        this.mos = mos;
    }
    
    /**
    **/
    public List<String> getIndexes() {
        return indexes;
    }
    public void setIndexes(List<String> indexes) {
        this.indexes = indexes;
    }
    
    /**
    **/
    public List<String> getIempKeys() {
        return iempKeys;
    }
    public void setIempKeys(List<String> iempKeys) {
        this.iempKeys = iempKeys;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

