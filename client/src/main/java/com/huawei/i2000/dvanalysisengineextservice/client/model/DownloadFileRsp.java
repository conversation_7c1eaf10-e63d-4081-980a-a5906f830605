package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import java.util.*;
import java.util.HashMap;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DownloadFileRsp     {
    
    
    @JsonProperty("filePath")
    private String filePath = null;
    @JsonProperty("flag")
    private Boolean flag = null;
    @JsonProperty("uri")
    private String uri = null;
    @JsonProperty("fileContent")
    private Map<String, List<String>> fileContent = new HashMap<String, List<String>>();

    
    /**
    **/
    public String getFilePath() {
        return filePath;
    }
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    /**
    **/
    public Boolean getFlag() {
        return flag;
    }
    public void setFlag(Boolean flag) {
        this.flag = flag;
    }
    
    /**
    **/
    public String getUri() {
        return uri;
    }
    public void setUri(String uri) {
        this.uri = uri;
    }
    
    /**
    **/
    public Map<String, List<String>> getFileContent() {
        return fileContent;
    }
    public void setFileContent(Map<String, List<String>> fileContent) {
        this.fileContent = fileContent;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

