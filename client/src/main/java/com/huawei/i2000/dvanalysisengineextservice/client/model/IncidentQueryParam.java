package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import com.huawei.i2000.dvanalysisengineextservice.client.model.Paging;

/**
 * 事件查询
 **/
@JsonIgnoreProperties(ignoreUnknown = true)
public class IncidentQueryParam     {
    
    
    @JsonProperty("meName")
    private String meName = null;
    @JsonProperty("paging")
    private Paging paging = null;

    
    /**
     * 告警网元名称
    **/
    public String getMeName() {
        return meName;
    }
    public void setMeName(String meName) {
        this.meName = meName;
    }
    
    /**
     * 数据
    **/
    public Paging getPaging() {
        return paging;
    }
    public void setPaging(Paging paging) {
        this.paging = paging;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

