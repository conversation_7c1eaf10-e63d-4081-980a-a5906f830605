package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;


@JsonIgnoreProperties(ignoreUnknown = true)
public class ServiceInfo     {
    
    
    @JsonProperty("serviceName")
    private String serviceName = null;
    @JsonProperty("serviceVersion")
    private String serviceVersion = null;
    @JsonProperty("serviceOwner")
    private String serviceOwner = null;

    
    /**
    **/
    public String getServiceName() {
        return serviceName;
    }
    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }
    
    /**
    **/
    public String getServiceVersion() {
        return serviceVersion;
    }
    public void setServiceVersion(String serviceVersion) {
        this.serviceVersion = serviceVersion;
    }
    
    /**
    **/
    public String getServiceOwner() {
        return serviceOwner;
    }
    public void setServiceOwner(String serviceOwner) {
        this.serviceOwner = serviceOwner;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

