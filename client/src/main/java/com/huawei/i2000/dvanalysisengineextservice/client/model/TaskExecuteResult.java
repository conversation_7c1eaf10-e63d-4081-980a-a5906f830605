package com.huawei.i2000.dvanalysisengineextservice.client.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;import java.math.BigDecimal;
import java.io.Serializable;

import java.util.HashMap;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskExecuteResult     {
    
    
    public enum StateEnum { 
    
    
         @JsonProperty("IN_PROGRESS") IN_PROGRESS,  @JsonProperty("SUCCESS") SUCCESS,  @JsonProperty("FAILED") FAILED,  @JsonProperty("TIMEOUT") TIMEOUT,  @JsonProperty("PART_SUCCESS") PART_SUCCESS, ; 
        
    };
    @JsonProperty("state")
    private StateEnum state = null;
    @JsonProperty("hostStateMap")
    private Map<String, String> hostStateMap = new HashMap<String, String>();
    @JsonProperty("startTime")
    private Long startTime = null;
    @JsonProperty("endTime")
    private Long endTime = null;

    
    /**
    **/
    public StateEnum getState() {
        return state;
    }
    public void setState(StateEnum state) {
        this.state = state;
    }
    
    /**
    **/
    public Map<String, String> getHostStateMap() {
        return hostStateMap;
    }
    public void setHostStateMap(Map<String, String> hostStateMap) {
        this.hostStateMap = hostStateMap;
    }
    
    /**
    **/
    public Long getStartTime() {
        return startTime;
    }
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }
    
    /**
    **/
    public Long getEndTime() {
        return endTime;
    }
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }
    
    
       
    
       
    
       
    
       
    
       
    
}

