<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.huawei.cmp</groupId>
        <artifactId>dvcontrollerservice-root</artifactId>
        <version>8.251.000592-SNAPSHOT</version>
    </parent>
    <artifactId>dvcontrollerservice-client</artifactId>
    <name>DVControllerServiceClient</name>
    <description>dvcontrollerserviceclient</description>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.huawei.bsp</groupId>
            <artifactId>com.huawei.bsp.commonlib.roa.restclient</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huawei.bsp</groupId>
            <artifactId>com.huawei.bsp.commonlib.roa.restserver</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huawei.cmp</groupId>
            <artifactId>dvcontrollerservice-api</artifactId>
            <version>${project.parent.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-simple</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.huawei.cloudsop</groupId>
            <artifactId>com.huawei.cloudsop.common.tokenhelper</artifactId>
        </dependency>
        <!--Hdt 使用离线模式依赖。-->
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.agent</artifactId>
            <version>0.8.8</version>
            <classifier>runtime</classifier>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
