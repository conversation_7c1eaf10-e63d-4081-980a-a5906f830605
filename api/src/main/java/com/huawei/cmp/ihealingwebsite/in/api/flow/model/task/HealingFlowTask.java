/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.cmp.ihealingwebsite.in.api.flow.model.task;

import com.huawei.cmp.utils.StringEscapeUtils;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 流程任务<br>
 * <p>
 * 功能详细描述<br>
 *
 * <AUTHOR>
 * @version DigitalView V600R001C00
 * @since 2016-09-20
 */
@NoArgsConstructor
public class HealingFlowTask {
    @Getter
    @Setter
    private String id;

    @Getter
    @Setter
    private String type;

    @Getter
    @Setter
    private String event;

    @Getter
    @Setter
    private String flowId;

    @Getter
    @Setter
    private String deployedId;

    @Getter
    @Setter
    private String flowName;

    @Getter
    @Setter
    private String flowDescription;

    @Getter
    @Setter
    private String orderBy;

    private Date startTime;

    private Date endTime;

    @Getter
    @Setter
    private String status;

    @Getter
    @Setter
    private String result;

    @Getter
    @Setter
    private String startTimeString;

    @Getter
    @Setter
    private String endTimeString;

    @Getter
    @Setter
    private String executor;

    @Getter
    @Setter
    private String tenantId;

    @Setter
    private Integer healSucc;

    @Setter
    private Integer diagSucc;

    @Setter
    private Integer hadDownload;

    @Setter
    private Integer hadView;

    @Getter
    @Setter
    private String content;

    @Getter
    @Setter
    private boolean allowedToDelete;

    @Getter
    @Setter
    private String version;

    @Getter
    @Setter
    private String reviewer;

    @Setter
    @Getter
    private String taskName;

    @Setter
    @Getter
    private String taskId;

    /**
     * 流程上限文变量，实时刷新上下文变量值
     */
    @Getter
    @Setter
    private String processVariables;

    public Integer getHadDownload() {
        if (hadDownload == null) {
            return 0;
        }
        return hadDownload;
    }

    public Integer getHadView() {
        if(null == hadView){
            return 0;
        }
        return hadView;
    }

    public Integer getHealSucc() {
        if (healSucc == null) {
            return 0;
        }
        return healSucc;
    }

    public Integer getDiagSucc() {
        if (diagSucc == null) {
            return 0;
        }
        return diagSucc;
    }

    public Date getStartTime() {
        return null == startTime ? null : (Date) startTime.clone();
    }

    public void setStartTime(Date startTime) {
        this.startTime = null == startTime ? null : (Date) startTime.clone();
    }

    public Date getEndTime() {
        return null == endTime ? null : (Date) endTime.clone();
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime == null ? null : (Date) endTime.clone();
    }

    @Getter
    @Setter
    private Map<String, Object> variables;

    @Override
    public String toString() {
        return StringEscapeUtils.escapeJava(ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE));
    }
}
