/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.cmp.ihealingwebsite.in.api.flow.model.view;

import com.huawei.cmp.foundation.validation.constraint.ByteLength;
import com.huawei.cmp.ihealingwebsite.in.api.pkg.anno.AsXmlAttribute;
import com.huawei.cmp.ihealingwebsite.in.api.pkg.anno.XmlAttributeAlias;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * <p>
 * 一句话功能简述<br>
 * <p>
 * 功能详细描述<br>
 *
 * <AUTHOR>
 * @version DigitalView V600R001C00
 * @since 2017-01-05
 */
@NoArgsConstructor
@XmlAttributeAlias(value = "variable")
public class FlowParameter {
    @Setter
    @Getter
    @AsXmlAttribute
    @XmlAttributeAlias(value = "globalParamId")
    @Pattern(regexp = "[^\"'><)(&]+")
    @ByteLength(max = 64)
    private String globalParamId;

    @Setter
    @Getter
    @XmlAttributeAlias(value = "valueIndex")
    @AsXmlAttribute
    private int valueIndex;

    @Setter
    @Getter
    @AsXmlAttribute
    @XmlAttributeAlias(value = "globalParamGroup")
    @Pattern(regexp = "[^\"'><)(&]+")
    @ByteLength(max = 64)
    private String globalParamGroup;

    @Setter
    @Getter
    @AsXmlAttribute
    @XmlAttributeAlias(value = "name")
    @NotBlank
    @Pattern(regexp = "[^\"'><)(&]+")
    @ByteLength(min = 1, max = 128)
    private String name;

    @Setter
    @Getter
    @AsXmlAttribute
    @XmlAttributeAlias(value = "value")
    @ByteLength(max = 409600)
    private String value;

    @Setter
    @Getter
    @XmlAttributeAlias(value = "valueLibrary")
    @AsXmlAttribute
    @ByteLength(max = 40960)
    private String valueLibrary;

    @Setter
    @Getter
    @AsXmlAttribute
    @XmlAttributeAlias(value = "selectValues")
    private String selectValues;

    @Setter
    @Getter
    @AsXmlAttribute
    @XmlAttributeAlias(value = "atomId")
    private String atomId;

    @Setter
    @Getter
    @AsXmlAttribute
    @XmlAttributeAlias(value = "atomName")
    private String atomName;

    @Setter
    @Getter
    @AsXmlAttribute
    @XmlAttributeAlias(value = "atomFile")
    private String atomFile;

    @Setter
    @Getter
    @AsXmlAttribute
    @XmlAttributeAlias(value = "description")
    @Pattern(regexp = "[^\"'><)(&]*")
    @ByteLength(max = 4096)
    private String description;

    // 自定义参数值类型
    @Setter
    @AsXmlAttribute
    @XmlAttributeAlias(value = "flowParamType")
    private FlowParameterType flowParamType;

    // 自定义参数值类型
    @Setter
    @AsXmlAttribute
    @XmlAttributeAlias(value = "flowParameterSource")
    private FlowParameterSource flowParameterSource;

    @Setter
    @Getter
    private String tenantId;

    @Setter
    @Getter
    @AsXmlAttribute
    @XmlAttributeAlias(value = "showStatus")
    private int showStatus;

    public FlowParameterType getFlowParamType() {
        if (flowParamType == null) {
            flowParamType = FlowParameterType.STRINGTYPE;
        }
        return flowParamType;
    }

    public FlowParameterSource getFlowParameterSource() {
        if (flowParameterSource == null) {
            return FlowParameterSource.DEFINITION;
        }
        return flowParameterSource;
    }

    /**
     * 自定义流程参数值类型
     */
    public enum FlowParameterType {
        // 字符串型
        STRINGTYPE,

        // 日期型
        DATETYPE,

        // 密码型
        PVALUETYPE,

        // 枚举型
        ENUMTYPE,

        // 脚本型
        SCRIPTTYPE,

        // key_value型
        ENUMRATE_KEY_VALUE,

        // IP列表型
        IP_TREE,

        // 联动型
        CASCADE,

        // 文件上传
        UPLOAD_FILE,

        // 用户通知用户ID
        REMOTE_NOTIFY_OBJ,

        // 网元类型
        DN_TYPE,

        // 数据库账号
        DB_ACCOUNT
    }

    /**
     * 流程参数值来源
     */
    public enum FlowParameterSource {
        // 自定义
        DEFINITION,

        // 全局参数引用
        GLOBAL,

        // 文件上传
        UPLOAD_FILE
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
