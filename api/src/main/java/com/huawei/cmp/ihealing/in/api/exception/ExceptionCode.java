/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2019-2019. All rights reserved.
 */

package com.huawei.cmp.ihealing.in.api.exception;

/**
 * 错误码 49301180000-49301189999 49301180000-49301180999 common错误码，如数据库操作异常 49301181000-49301181999 atom错误码，如数据库操作异常
 * 49301182000-49301182999 flow错误码，如数据库操作异常 49301183000-49301183999 adaptor错误码，如数据库操作异常 49301184000-49301184999
 * package错误码，如数据库操作异常 49301185000-49301185999 cfaapi错误码，如数据库操作异常
 * <p>
 * <功能详细描述>
 *
 * @since V100R001C20 -11-04
 * <AUTHOR>
 * @version DigitalView V600R001C00 , 2016/6/28
 * @see <相关类/方法>
 */
public interface ExceptionCode {
    /**
     * ERROR_CODE_BASE
     */
    String ERROR_CODE_BASE = "4930118";

    /**
     * COMMON_ERROR_CODE_BASE
     */
    String COMMON_ERROR_CODE_BASE = ERROR_CODE_BASE + "0";

    /**
     * ATOM_ERROR_CODE_BASE
     */
    String ATOM_ERROR_CODE_BASE = ERROR_CODE_BASE + "1";

    /**
     * FLOW_ERROR_CODE_BASE
     */
    String FLOW_ERROR_CODE_BASE = ERROR_CODE_BASE + "2";

    /**
     * ADAPTOR_ERROR_CODE_BASE
     */
    String ADAPTOR_ERROR_CODE_BASE = ERROR_CODE_BASE + "3";

    /**
     * API_ERROR_CODE_BASE
     */
    String API_ERROR_CODE_BASE = ERROR_CODE_BASE + "4";

    /**
     * PACKAGE_ERROR_CODE_BASE
     */
    String PACKAGE_ERROR_CODE_BASE = ERROR_CODE_BASE + "5";

    /**
     * GLOBAL_ERROR_CODE_BASE
     */
    String GLOBAL_ERROR_CODE_BASE = ERROR_CODE_BASE + "6";

    /**
     * DOWNLOAD_ERROR_CODE_BASE
     */
    String DOWNLOAD_ERROR_CODE_BASE = ERROR_CODE_BASE + "7";


    /**
     * EXPECT_COST
     */
    String EXPECT_COST = "500";

    /**
     * SUCCESS_CODE
     */
    int SUCCESS_CODE = 200;
}
