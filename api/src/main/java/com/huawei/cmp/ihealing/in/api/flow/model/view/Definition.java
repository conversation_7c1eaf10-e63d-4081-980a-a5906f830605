/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2019-2019. All rights reserved.
 */

package com.huawei.cmp.ihealing.in.api.flow.model.view;

import com.huawei.bsp.biz.util.JsonUtil;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cmp.foundation.validation.constraint.ListPattern;
import com.huawei.cmp.ihealing.in.api.pkg.anno.XmlAttributeAlias;
import com.huawei.cmp.ihealing.in.api.pkg.anno.XmlOmitField;
import com.huawei.cmp.ihealingwebsite.in.api.flow.model.view.JsonModel;
import com.huawei.cmp.utils.StringEscapeUtils;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

/**
 * <一句话功能简述> <功能详细描述>
 *
 * <AUTHOR>
 * @version DigitalView V600R001C00 , 2016/8/12
 * @see 相关类/方法
 * @since V600R001C00
 */
@NoArgsConstructor
@XmlAttributeAlias(value = "definition")
public class Definition {
    private static final OssLog LOGGER = OssLogFactory.getLogger(Definition.class);

    /**
     * Nodes
     */
    @Getter
    @Setter
    @XmlAttributeAlias(value = "nodes")
    @Valid
    private List<Node> nodes;

    /**
     * Edges
     */
    @Getter
    @Setter
    @XmlAttributeAlias(value = "edges")
    @Valid
    private List<Edge> edges;

    /**
     * Paras
     */
    @Getter
    @Setter
    @XmlAttributeAlias(value = "variables")
    @Valid
    private List<FlowParameter> paras = null;

    /**
     * Default paras
     */
    @Getter
    @Setter
    @Valid
    private List<FlowParameter> defaultParas;

    /**
     * Space ids
     */
    @Getter
    @Setter
    @XmlOmitField
    @ListPattern(regexp = "[^#%&'+<=>?\\\\/]*")
    private List<String> spaceIds;

    /**
     * Generate flowvariables map
     *
     * @return the map
     */
    public Map<String, String> generateFlowvariables() {
        Map<String, String> vars = new HashMap<>();

        if (CollectionUtils.isEmpty(paras)) {
            return vars;
        }

        for (FlowParameter flowParameter : paras) {
            if (FlowParameter.FlowParameterType.CASCADE.equals(flowParameter.getFlowParamType())) {
                JsonModel valueObj = null;
                try {
                    valueObj = JsonUtil.unMarshal(flowParameter.getValue(), JsonModel.class);
                } catch (IOException e) {
                    LOGGER.error("Failed to unMarshal value.", e);
                }
                vars.put(flowParameter.getName(), valueObj.getParamName());
                for (int i = 0; i < valueObj.getChildRenValue().size(); i++) {
                    String paraName = valueObj.getChildRenValue().get(i).get("paramName");
                    String paraValue = valueObj.getChildRenValue().get(i).get("paramValue");
                    vars.put(paraName, paraValue);
                }
            } else {
                vars.put(flowParameter.getName(), flowParameter.getValue());
            }
        }
        return vars;
    }

    @Override
    public String toString() {
        return StringEscapeUtils.escapeJava(ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE));
    }
}
