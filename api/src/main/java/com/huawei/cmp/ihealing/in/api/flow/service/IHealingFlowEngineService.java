/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2019-2019. All rights reserved.
 */

package com.huawei.cmp.ihealing.in.api.flow.service;

import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.cmp.foundation.service.exception.CMPException;
import com.huawei.cmp.ihealing.in.api.flow.model.task.BatchCreateTaskResponse;
import com.huawei.cmp.ihealing.in.api.flow.model.task.StartFlowRequest;
import com.huawei.cmp.ihealing.in.api.flow.model.view.FlowModel;
import com.huawei.cmp.ihealing.in.api.model.EventModel;

import java.util.Map;

/**
 * <p>
 * 故障诊断框架执行引擎接口
 * </p>
 *
 * @since 2016 -08-11
 * <AUTHOR>
 * @version DigitalView V600R001C00
 */
public interface IHealingFlowEngineService {
    /**
     * <p>
     * 部署一个流程
     * </p>
     *
     * @param process 流程实例
     * @return 返回部署的id string
     * @throws CMPException 拋出失败
     */
    String deployProcess(final FlowModel process) throws CMPException;

    /**
     * <p>
     * 启动一个流程
     * </p>
     *
     * @param deployedId 流程Id
     * @param event 事件
     * @param startVars 启动时候需要放置的参数
     * @return 返回实例ID string
     * @throws CMPException 拋出失败
     */
    String startProcess(final String deployedId, final EventModel event, final Map<String, Object> startVars)
        throws CMPException;

    /**
     * <p>
     * 卸载一个流程
     * </p>
     *
     * @param deployedId 部署ID
     * @throws CMPException 拋出失败
     */
    void uninstallProcess(final String deployedId) throws CMPException;

    /**
     * <p>
     * 流程是否运行
     * </p>
     *
     * @param deployedId 部署ID
     * @return 是否运行 boolean
     * @throws CMPException cmp exception
     */
    boolean isProcessRunning(final String deployedId) throws CMPException;

    /**
     * 通过流程参数和流程结构启动流程
     *
     * @param context 流程上下文
     * @param request 请求入参
     * @return 创建任务结果
     */
    BatchCreateTaskResponse startFlowByDefinition(HttpContext context, StartFlowRequest request);

}
