/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 */

package com.huawei.cmp.workbeans.model;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;

/**
 * Base atom
 *
 * <AUTHOR>
 * @since 2020 -07-22
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE,
    setterVisibility = Visibility.NONE)
public class BaseAtom {
    /**
     * Mo type
     */
    @Valid
    @JsonProperty("moType")
    private String moType = null;

    /**
     * Name
     */
    @Valid
    @JsonProperty("name")
    private String name = null;

    /**
     * Type
     */
    @Valid
    @JsonProperty("type")
    private String type = null;

    /**
     * Desc local
     */
    @Valid
    @JsonProperty("descLocal")
    private String descLocal = null;

    /**
     * Desc en
     */
    @Valid
    @JsonProperty("descEn")
    private String descEn = null;

    /**
     * Tag
     */
    @Valid
    @JsonProperty("tag")
    private String tag = null;

    /**
     * Source
     */
    @Valid
    @JsonProperty("source")
    private String source = null;

    /**
     * Create mode
     */
    @Valid
    @JsonProperty("createMode")
    private String createMode = null;

    /**
     * Create user
     */
    @Valid
    @JsonProperty("createUser")
    private String createUser = null;

    /**
     * Create time
     */
    @Valid
    @JsonProperty("createTime")
    private Long createTime = null;

    /**
     * Last modify user
     */
    @Valid
    @JsonProperty("lastModifyUser")
    private String lastModifyUser = null;

    /**
     * Last modify time
     */
    @Valid
    @JsonProperty("lastModifyTime")
    private Long lastModifyTime = null;

    /**
     * Common params
     */
    @Valid
    @JsonProperty("commonParams")
    private List<Parameter> commonParams = new ArrayList<Parameter>();

    /**
     * Original params
     */
    @Valid
    @JsonProperty("originalParams")
    private List<Parameter> originalParams = new ArrayList<Parameter>();

    /**
     * Request params
     */
    @Valid
    @JsonProperty("requestParams")
    private List<Parameter> requestParams = new ArrayList<Parameter>();

    /**
     * Response param
     */
    @Valid
    @JsonProperty("responseParam")
    private Parameter responseParam = null;

    /**
     * Gets mo type *
     *
     * @return the mo type
     */
    @JsonProperty("moType")
    public String getMoType() {
        return moType;
    }

    /**
     * Sets mo type *
     *
     * @param moType mo type
     */
    public void setMoType(String moType) {
        this.moType = moType;
    }

    /**
     * Gets name *
     *
     * @return the name
     */
    @JsonProperty("name")
    public String getName() {
        return name;
    }

    /**
     * Sets name *
     *
     * @param name name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Gets type *
     *
     * @return the type
     */
    @JsonProperty("type")
    public String getType() {
        return type;
    }

    /**
     * Sets type *
     *
     * @param type type
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * Gets desc local *
     *
     * @return the desc local
     */
    @JsonProperty("descLocal")
    public String getDescLocal() {
        return descLocal;
    }

    /**
     * Sets desc local *
     *
     * @param descLocal desc local
     */
    public void setDescLocal(String descLocal) {
        this.descLocal = descLocal;
    }

    /**
     * Gets desc en *
     *
     * @return the desc en
     */
    @JsonProperty("descEn")
    public String getDescEn() {
        return descEn;
    }

    /**
     * Sets desc en *
     *
     * @param descEn desc en
     */
    public void setDescEn(String descEn) {
        this.descEn = descEn;
    }

    /**
     * Gets tag *
     *
     * @return the tag
     */
    @JsonProperty("tag")
    public String getTag() {
        return tag;
    }

    /**
     * Sets tag *
     *
     * @param tag tag
     */
    public void setTag(String tag) {
        this.tag = tag;
    }

    /**
     * Gets create mode *
     *
     * @return the create mode
     */
    @JsonProperty("createMode")
    public String getCreateMode() {
        return createMode;
    }

    /**
     * Sets create mode *
     *
     * @param createMode create mode
     */
    public void setCreateMode(String createMode) {
        this.createMode = createMode;
    }

    /**
     * Gets create user *
     *
     * @return the create user
     */
    @JsonProperty("createUser")
    public String getCreateUser() {
        return createUser;
    }

    /**
     * Sets create user *
     *
     * @param createUser create user
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * Gets create time *
     *
     * @return the create time
     */
    @JsonProperty("createTime")
    public Long getCreateTime() {
        return createTime;
    }

    /**
     * Sets create time *
     *
     * @param createTime create time
     */
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    /**
     * Gets last modify user *
     *
     * @return the last modify user
     */
    @JsonProperty("lastModifyUser")
    public String getLastModifyUser() {
        return lastModifyUser;
    }

    /**
     * Sets last modify user *
     *
     * @param lastModifyUser last modify user
     */
    public void setLastModifyUser(String lastModifyUser) {
        this.lastModifyUser = lastModifyUser;
    }

    /**
     * Gets last modify time *
     *
     * @return the last modify time
     */
    @JsonProperty("lastModifyTime")
    public Long getLastModifyTime() {
        return lastModifyTime;
    }

    /**
     * Sets last modify time *
     *
     * @param lastModifyTime last modify time
     */
    public void setLastModifyTime(Long lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    /**
     * Gets common params *
     *
     * @return the common params
     */
    @JsonProperty("commonParams")
    public List<Parameter> getCommonParams() {
        return commonParams;
    }

    /**
     * Sets common params *
     *
     * @param commonParams common params
     */
    public void setCommonParams(List<Parameter> commonParams) {
        this.commonParams = commonParams;
    }

    /**
     * Gets original params *
     *
     * @return the original params
     */
    @JsonProperty("originalParams")
    public List<Parameter> getOriginalParams() {
        return originalParams;
    }

    /**
     * Sets original params *
     *
     * @param originalParams original params
     */
    public void setOriginalParams(List<Parameter> originalParams) {
        this.originalParams = originalParams;
    }

    /**
     * Gets request params *
     *
     * @return the request params
     */
    @JsonProperty("requestParams")
    public List<Parameter> getRequestParams() {
        return requestParams;
    }

    /**
     * Sets request params *
     *
     * @param requestParams request params
     */
    public void setRequestParams(List<Parameter> requestParams) {
        this.requestParams = requestParams;
    }

    /**
     * Gets response param *
     *
     * @return the response param
     */
    @JsonProperty("responseParam")
    public Parameter getResponseParam() {
        return responseParam;
    }

    /**
     * Sets response param *
     *
     * @param responseParam response param
     */
    public void setResponseParam(Parameter responseParam) {
        this.responseParam = responseParam;
    }

    /**
     * Gets source *
     *
     * @return the source
     */
    @JsonProperty("source")
    public String getSource() {
        return source;
    }

    /**
     * Sets source *
     *
     * @param source source
     */
    public void setSource(String source) {
        this.source = source;
    }
}