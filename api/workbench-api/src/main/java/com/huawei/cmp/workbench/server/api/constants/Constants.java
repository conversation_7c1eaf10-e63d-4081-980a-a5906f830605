/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2019-2020. All rights reserved.
 */

package com.huawei.cmp.workbench.server.api.constants;

import com.huawei.bsp.deploy.util.DefaultEnvUtil;

/**
 * Constants
 *
 * @since V800R001C02
 */
public interface Constants {

    /**
     * Command path
     */
    String COMMAND_PATH = "/Cmp/workbench/security";

    /**
     * Command
     */
    String COMMAND = "command";

    /**
     * Parameter
     */
    String PARAMETER = "params";

    /**
     * Download agents limit
     */
    String DOWNLOAD_AGENTS_LIMIT = "workbench.task.agent.limits";

    /**
     * Default agent limit
     */
    int DEFAULT_AGENT_LIMIT = 50;

    /**
     * Comma
     */
    String COMMA = ",";

    /**
     * Fileservice host config
     */
    String FILESERVICE_HOST_CONFIG = "";

    /**
     * Fileservice port config
     */
    String FILESERVICE_PORT_CONFIG = "";

    /**
     * Task id
     */
    String TASK_ID = "taskId";

    /**
     * Work bench
     */
    String WORK_BENCH = "workbench";

    /**
     * Who am i
     */
    String WHO_AM_I = "WorkbenchServer";

    /**
     * Cmp user config
     */
    String CMP_USER_CONFIG = "cmp_inter_user";

    /**
     * Cmp basswd config
     */
    String CMP_BASSWD_CONFIG = "cmp_inter_password";

    /**
     * Zk host config
     */
    String ZK_HOST_CONFIG = "zk_host";

    /**
     * Task info path
     */
    String TASK_INFO_PATH = "/cmp/workbench/task/";

    /**
     * Task trigger path
     */
    String TASK_TRIGGER_PATH = "/cmp/workbench/trigger/";

    /**
     * Task lock path
     */
    String TASK_LOCK_PATH = "/cmp/workbench/lock/";

    /**
     * Server path
     */
    String SERVER_PATH = "/cmp/workbench/server";

    /**
     * Download path
     */
    String DOWNLOAD_PATH = "/cmp/workbench/download";

    /**
     * File path
     */
    String FILE_PATH = "/data/workbench/";

    /**
     * Zk cfg path
     */
    String ZK_CFG_PATH = "/cmp/workbench/config/";

    /**
     * Depot host config
     */
    String DEPOT_HOST_CONFIG = "workbench_host_url";

    /**
     * Depot user
     */
    String DEPOT_USER = "/cmp/workbench/config/depot_host_usr";

    /**
     * Depot pwd
     */
    String DEPOT_PWD = "/cmp/workbench/config/depot_host_pwd";

    /**
     * Action result path
     */
    String ACTION_RESULT_PATH = "/cmp/workbench/result";

    /**
     * Agent path
     */
    String AGENT_PATH = "/cmp/workbench/agent";

    /**
     * Nats addr
     */
    String NATS_ADDR = "nats_addr";

    /**
     * Nats user
     */
    String NATS_USER = "cmp_inter_user";

    /**
     * Nats basswd
     */
    String NATS_BASSWD = "cmp_inter_password";

    /**
     * Nats trigger topic
     */
    String NATS_TRIGGER_TOPIC = "com.huawei.cmp.workbench.trigger";

    /**
     * Script delete topic
     */
    String SCRIPT_DELETE_TOPIC = "com.huawei.cmp.workbench.deleteScript";

    /**
     * Nats agent result topic
     */
    String NATS_AGENT_RESULT_TOPIC = "com.huawei.cmp.workbench.agent.result";

    /**
     * Share dir
     */
    String SHARE_DIR = "uee.upload.dir.tmp";

    /**
     * Workbench path
     */
    String WORKBENCH_PATH = DefaultEnvUtil.getAppName();

    /**
     * Download tmp
     */
    String DOWNLOAD_TMP = "downloadtmp";

    /**
     * Uee download path
     */
    String UEE_DOWNLOAD_PATH = "filePath";

    /**
     * Uee download name
     */
    String UEE_DOWNLOAD_NAME = "fileName";

    /**
     * Download action tmpdir
     */
    String DOWNLOAD_ACTION_TMPDIR = "result";

    /**
     * Filter hosts bykeyword
     */
    String FILTER_HOSTS_BYKEYWORD = "FILTERBYKEYWORD";

    /**
     * Control flow sliceid
     */
    int CONTROL_FLOW_SLICEID = -1;

    /**
     * ESCAPE_CHAR
     */
    String ESCAPE_CHAR = "#_";

    /**
     * SPECIAL_CHAR
     */
    String SPECIAL_CHAR = "_";

    /**
     * Download action type
     */
    int DOWNLOAD_ACTION_TYPE = 7;

    /**
     * Download file max size
     */
    String DOWNLOAD_FILE_MAX_SIZE = "workbench.download.max.file.size";

    /**
     * Zk op timeout
     */
    long ZK_OP_TIMEOUT = 60L;

    /**
     * Millsinsecond
     */
    long MILLSINSECOND = 1000L;

    /**
     * 默认分组名称
     */
    String DEFAULTGROUPNAME = "default";

    /**
     * Finish percent
     */
    int FINISH_PERCENT = 100;

    /**
     * 查询请求中最多允许携带100个有效tag
     */
    int MAX_SIZE_TAGS_QUERY = 100;

    /**
     * Expired data clean task period
     */
    // 过时数据清理任务运行周期配置项名称
    String EXPIRED_DATA_CLEAN_TASK_PERIOD = "workbench.expired.data.clean.task.period";

    /**
     * Expired data default period
     */
    // 过时数据清理任务运行周期默认值（分钟）
    int EXPIRED_DATA_DEFAULT_PERIOD = 10;
}
