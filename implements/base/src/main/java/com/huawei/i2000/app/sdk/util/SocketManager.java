/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

package com.huawei.i2000.app.sdk.util;

import static com.huawei.i2000.app.sdk.constant.SecurityConstants.SOCKET_CA_CHECK;
import static com.huawei.i2000.app.sdk.util.SSLScoketUtil.setSSLSocketFactory;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.security.AlgorithmConstraints;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.Security;
import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLParameters;
import javax.net.ssl.SSLSocket;

/**
 * SOCKET协议连接管理
 *
 * <AUTHOR>
 * @since I2000V500R003C60
 */
public class SocketManager{
    private static final OssLog LOG = OssLogFactory.getLogger(SocketManager.class);

    private static final String TLS_TYPE = "TLSv1.2";

    private static final String TLS_CIPHERSUITS = "TLSCipherSuits";

    private String host;

    private int port;

    /**
     * 用户是否登陆
     */
    private boolean logined = false;

    /**
     * The Output stream.
     */
    public OutputStream outputStream;

    /**
     * The Input stream.
     */
    public InputStream inputStream;

    private final int timeout = Integer.parseInt(PropertiesUtil.getProperties("socketConnectTimeOut")) * 1000;

    /**
     * The Ssl socket 定义一个Socket对象
     */
    public SSLSocket sslSocket;

    /**
     * Sets logined.
     *
     * @param logined the logined
     */
    public void setLogined(boolean logined) {
        this.logined = logined;
    }

    /**
     * Instantiates a new Mml client.
     *
     * @param host the host
     * @param port the port
     */
    public SocketManager(String host, int port) {
        // 需要服务器的IP地址和端口号，才能获得正确的Socket对象
        this.host = host;
        this.port = port;
    }

    /**
     * Connect.
     *
     * @throws IOException the io exception
     * @throws KeyManagementException the key management exception
     * @throws NoSuchAlgorithmException the no such algorithm exception
     */
    @SuppressWarnings({"unchecked", "rawtypes"})
    public void connect() throws IOException, KeyManagementException, NoSuchAlgorithmException {
        sslSocket = (SSLSocket) getSSLContext().getSocketFactory().createSocket(host, port);
        sslSocket.setSoTimeout(timeout);
        sslSocket.setEnabledProtocols(new String[]{TLS_TYPE});
        String cipherSuits = SSLScoketUtil.getStringPropertiesWhite(TLS_CIPHERSUITS);
        setEnabledCipherSuites(sslSocket, cipherSuits);
        SSLParameters sslParameter = sslSocket.getSSLParameters();
        AlgorithmConstraints disabledPssAlgorithmConstraints = null;
        String disabledAlgorithms = Security.getProperty("jdk.tls.disabledAlgorithms");
        LOG.warn("about to modify jvm security setting: jdk.tls.disabledAlgorithms.");
        try {
            Security.setProperty("jdk.tls.disabledAlgorithms", disabledAlgorithms);
            Class constraints = Class.forName("sun.security.util.DisabledAlgorithmConstraints");
            Constructor constructor = constraints.getDeclaredConstructor(String.class);
            Object constraint = constructor.newInstance("jdk.tls.disabledAlgorithms");
            if (constraint instanceof AlgorithmConstraints) {
                disabledPssAlgorithmConstraints = (AlgorithmConstraints) constructor.newInstance("jdk.tls.disabledAlgorithms");
            }
        } catch (ClassNotFoundException | NoSuchMethodException | InvocationTargetException | IllegalAccessException | InstantiationException e) {
            LOG.error("load SSLAlgorithmConstraints failed.", e);
        } finally {
            Security.setProperty("jdk.tls.disabledAlgorithms", disabledAlgorithms);
            LOG.warn("jvm security setting reset to last amend: jdk.tls.disabledAlgorithms.");
        }
        sslParameter.setAlgorithmConstraints(disabledPssAlgorithmConstraints);
        sslSocket.setSSLParameters(sslParameter);

        outputStream = sslSocket.getOutputStream();
        inputStream = sslSocket.getInputStream();
        sslSocket.startHandshake();
    }

    private SSLContext getSSLContext() throws NoSuchAlgorithmException{
        SSLContext sslContext = SSLContext.getInstance(TLS_TYPE);
        String checkCa = PropertiesUtil.getProperties(SOCKET_CA_CHECK);
        if (StringUtils.isEmpty(checkCa) || checkCa.toUpperCase(Locale.ROOT).equals("TRUE")) {
            setSSLSocketFactory(sslContext, true);
        } else {
            setSSLSocketFactory(sslContext, false);
        }
        return sslContext;
    }

    /**
     * 设置加密套
     *
     * @param sslSocket 连接
     * @param cipherSuits 加密套
     */
    private static void setEnabledCipherSuites(SSLSocket sslSocket, String cipherSuits) {
        if (!StringUtils.isEmpty(cipherSuits)) {
            String[] enabledCipherSuitesArr = sslSocket.getEnabledCipherSuites();
            List<String> enabledCipherSuites = Arrays.asList(enabledCipherSuitesArr);
            List<String> supportedCipherSuits = Arrays.asList(sslSocket.getSupportedCipherSuites());
            String[] cipherSuitsArr = Arrays.asList(cipherSuits.split(","))
                .stream()
                .filter((cipherSuit) -> supportedCipherSuits.contains(cipherSuit) && !enabledCipherSuites.contains(
                    cipherSuit))
                .collect(Collectors.toList())
                .toArray(new String[] {});
            // 补充配置文件中的加密套
            String[] enabledCipherSuitesAll = Arrays.copyOf(cipherSuitsArr,
                cipherSuitsArr.length + enabledCipherSuitesArr.length);
            System.arraycopy(enabledCipherSuitesArr, 0, enabledCipherSuitesAll, cipherSuitsArr.length,
                enabledCipherSuitesArr.length);
            sslSocket.setEnabledCipherSuites(enabledCipherSuitesAll);
        }
    }

    /**
     * Close connect.
     */
    public void closeConnect() {
        setLogined(false);
        LOG.info("Exit MMLClient");
        try {
            if (outputStream != null) {
                outputStream.close();
            }
        } catch (IOException e) {
            LOG.warn("Close MMLClient output stream failed !{}", e);
        }

        try {
            if (sslSocket != null) {
                sslSocket.close();
            }
        } catch (IOException e) {
            LOG.warn("Close MMLClient ssl socket failed !{}", e);

        }

        try {
            if (inputStream != null) {
                inputStream.close();
            }

        } catch (IOException e) {
            LOG.warn("Close MMLClient input stream failed !{}", e);
        }
    }
}
