const i18nResource = {
  'zh-cn': {
    'logmatrix.templateManage.title': '模板管理',
    'logmatrix.templateManage.filter.templateName': '模板名称:',
    'logmatrix.templateManage.filter.dataSource': '数据源:',
    'logmatrix.templateManage.filter.solutionType': '解决方案:',
    'logmatrix.templateManage.filter.indexRule': '索引规则:',
    'logmatrix.templateManage.filter.search': '搜索',
    'logmatrix.templateManage.filter.create': '新建模板',
    'logmatrix.templateManage.filter.validator': '输入不能超过64个字符，不能包含 # % & + = | > < \' ; ? " / \\ ( ) 特殊字符。',
    'logmatrix.templateManage.filter.name.validator': '名称不能为空，且不能超过64个字符，不能包含 # % & + = | > < \' ; ? " / \\ ( ) 特殊字符。',
    'logmatrix.templateManage.tableHead.time': '时间',
    'logmatrix.templateManage.tableHead.operation': '操作',
    'logmatrix.templateManage.tableCell.del': '删除',

    'logmatrix.templateManage.configuration.title': '配置',
    'logmatrix.templateManage.configuration.indexMode': '索引规则模式:',
    'logmatrix.templateManage.configuration.timeField': '日期和时间字段:',
    'logmatrix.templateManage.configuration.detail': '详情',
    'logmatrix.templateManage.configuration.create': '新建',
    'logmatrix.templateManage.configuration.tableHead.field': '字段',
    'logmatrix.templateManage.configuration.tableHead.fieldAlias': '字段别名',
    'logmatrix.templateManage.configuration.tableHead.fieldType': '字段类型',
    'logmatrix.templateManage.configuration.tableHead.operator': '操作符',
    'logmatrix.templateManage.configuration.tableHead.queried': '是否作为查询字段',
    'logmatrix.templateManage.configuration.tableHead.presetValue': '搜索预设值',
    'logmatrix.templateManage.configuration.tableHead.defaultValue': '默认值',
    'logmatrix.templateManage.configuration.tableHead.displayed': '搜索结果展示项',
    
    'logmatrix.templateManage.configuration.detailPopUp.title': '查看索引规则',
    'logmatrix.templateManage.configuration.createPopUp.title': '配置索引规则',

    'logmatrix.templateManage.configuration.button.refresh': '刷新',
    'logmatrix.templateManage.configuration.button.edit': '编辑',
    'logmatrix.templateManage.configuration.button.save': '保存',
    'logmatrix.templateManage.configuration.button.cancel': '取消',

    'logmatrix.templateManage.filter.validator.prev.strlength': '单个字段的预设值不能超过2048个字符。',
    'logmatrix.templateManage.filter.validator.prev.length': '单个字段的预设值不能超过100个。',
    'logmatrix.templateManage.filter.validator.prev.singleLength': '每个预设值不能超过256字符。',
    'logmatrix.templateManage.filter.validator.prev.duplicate': '单个字段的预设值不能重复。',
    'logmatrix.templateManage.filter.validator.deft.length': '单个字段的默认值不能超过10个。',
    'logmatrix.templateManage.filter.validator.deft.singleLength': '每个默认值不能超过256字符。',
    'logmatrix.templateManage.filter.validator.deft.duplicate': '单个字段的默认值不能重复。',
  },
  'en-us': {
    'logmatrix.templateManage.title': 'Template Manage',
    'logmatrix.templateManage.filter.templateName': 'Template Name:',
    'logmatrix.templateManage.filter.dataSource': 'Data Source:',
    'logmatrix.templateManage.filter.solutionType': 'SolutionType:',
    'logmatrix.templateManage.filter.indexRule': 'Index rule:',
    'logmatrix.templateManage.filter.search': 'Search',
    'logmatrix.templateManage.filter.create': 'Create Template',
    'logmatrix.templateManage.filter.validator': 'Input cannot exceed 64 characters, and the special characters # % & + = | > < \' ; ? " / \\ ( ) are not allowed.',
    'logmatrix.templateManage.filter.name.validator': 'Enter 1 to 64 characters for template name. The special characters # % & + = | > < \' ; ? " / \\ ( ) are not allowed.',
    'logmatrix.templateManage.tableHead.time': 'Time',
    'logmatrix.templateManage.tableHead.operation': 'Operation',
    'logmatrix.templateManage.tableCell.del': 'Delete',
    
    'logmatrix.templateManage.configuration.title': 'Template Settings',
    'logmatrix.templateManage.configuration.indexMode': 'Index rule pattern:',
    'logmatrix.templateManage.configuration.timeField': 'Date and time field:',
    'logmatrix.templateManage.configuration.detail': 'Detail',
    'logmatrix.templateManage.configuration.create': 'Create',
    'logmatrix.templateManage.configuration.tableHead.field': 'Field',
    'logmatrix.templateManage.configuration.tableHead.fieldAlias': 'Field Alias',
    'logmatrix.templateManage.configuration.tableHead.fieldType': 'Field Type',
    'logmatrix.templateManage.configuration.tableHead.operator': 'Operator',
    'logmatrix.templateManage.configuration.tableHead.queried': 'Used as Query Condition',
    'logmatrix.templateManage.configuration.tableHead.presetValue': 'Search Preset Value',
    'logmatrix.templateManage.configuration.tableHead.defaultValue': 'Default Value',
    'logmatrix.templateManage.configuration.tableHead.displayed': 'Displayed by Default',
    
    'logmatrix.templateManage.configuration.detailPopUp.title': 'View an index rule',
    'logmatrix.templateManage.configuration.createPopUp.title': 'Configure an index rule',

    'logmatrix.templateManage.configuration.button.refresh': 'Refresh',
    'logmatrix.templateManage.configuration.button.edit': 'Edit',
    'logmatrix.templateManage.configuration.button.save': 'Save',
    'logmatrix.templateManage.configuration.button.cancel': 'Cancel',
    
    'logmatrix.templateManage.filter.validator.prev.strlength': 'A single field cannot exceed 2048 characters.',
    'logmatrix.templateManage.filter.validator.prev.length': 'A single field cannot have more than 100 preset values.',
    'logmatrix.templateManage.filter.validator.prev.singleLength': 'Each preset value cannot exceed 256 characters.',
    'logmatrix.templateManage.filter.validator.prev.duplicate': 'Preset values for a single field must be unique.',
    'logmatrix.templateManage.filter.validator.deft.length': 'A single field cannot have more than 10 default values.',
    'logmatrix.templateManage.filter.validator.deft.singleLength': 'Each default value cannot exceed 256 characters.',
    'logmatrix.templateManage.filter.validator.deft.duplicate': 'Default values for a single field must be unique.',
  },
};

export default i18nResource;