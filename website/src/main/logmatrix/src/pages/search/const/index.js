import React from 'react';
import { $t, registerI18nResource } from '@util';
import i18nResource from '../locales';
import {tools} from '../../../commonUtil';
const {localToUTCTimestamp} = tools;

// 注册当前模块内的国际化资源（按需加载）
registerI18nResource(i18nResource, 'search');

export const NavBarContext = React.createContext();
export const FieldsAccordionContext = React.createContext();
export const TimeRangeContext = React.createContext();
export const ButtonsContext = React.createContext();
export const FiltersContext = React.createContext();
export const BarChartContext = React.createContext();
export const FieldsTableContext = React.createContext();
export const DrillContext = React.createContext();

export const TemplateType = {
  template: 0,
  indexRule: 1,
};

export const TimeMode = {
  lately: 0,
  time: 1,
};

export const TimeUnit = {
  MINITE: 60,
  HOUR: 3600,
  DAY: 86400,
  WEEK: 604800,
};

export const HIGHLIGHT = {
  prefix: 'cmphighlightbegin',
  suffix: 'cmphighlightend',
};

export const delHighLightStr = (str) => {
  const reg = new RegExp(`(${HIGHLIGHT.prefix}|${HIGHLIGHT.suffix})`, 'g');
  return str.toString().replace(reg, '');
};

export const needHighLight = (str) => {
  const reg = new RegExp(`(${HIGHLIGHT.prefix}|${HIGHLIGHT.suffix})`, 'g');
  return reg.test(str.toString());
};

// 左侧模板\索引规则 的类型下拉框
export const TYPE_OPTIONS = [
  {
    text: $t('logmatrix.search.tab.name.template'),
    value: TemplateType.template,
  },
  {
    text: $t('logmatrix.search.tab.name.index'),
    value: TemplateType.indexRule,
  },
];

// 默认无数据时的页签id(以及无页签时的空id)
export const DEFAULT_TABID = 'default_tabID';

/**
 * 通过传入的模板(索引)对象 以及对应的模板类型, 获取对应的页签名称
 */
export const getTabName = (obj, templateType) => {
  if (templateType === TemplateType.template) { // 模板
    return `${$t('logmatrix.search.tab.name.template')} ${obj.indexLabel}-${obj.indexName ?? obj.searchIndexPatternName}`;
  } else { // 模板
    return `${$t('logmatrix.search.tab.name.index')} ${obj.indexName}`;
  }
};

/**
 * 通过传入的全量过滤条件, 获取用于查询入参的过滤条件
 */
export const getFilterCondition = ({filters, clusterInfos, clusterTypes, solutionInfos, solutionTypes, groupNames, siteInfos}) => {
  let condition = {};
  for (let i = 0; i < filters.length; i++) {
    let searchField = filters[i];
    if (!searchField.value) {
      continue;
    }
    if (searchField.fieldName === 'ClusterId') {
      getDefaultSelectField(condition, clusterInfos, searchField.value, searchField.fieldName, searchField.fieldOperator);
    } else if (searchField.fieldName === 'ClusterType') {
      getDefaultSelectField(condition, clusterTypes, searchField.value, searchField.fieldName, searchField.fieldOperator);
    } else if (searchField.fieldName === 'SolutionId') {
      getDefaultSelectField(condition, solutionInfos, searchField.value, searchField.fieldName, searchField.fieldOperator);
    } else if (searchField.fieldName === 'SolutionType') {
      getDefaultSelectField(condition, solutionTypes, searchField.value, searchField.fieldName, searchField.fieldOperator);
    } else if (searchField.fieldName === 'GroupName') {
      getDefaultSelectField(condition, groupNames, searchField.value, searchField.fieldName, searchField.fieldOperator);
    } else if (searchField.fieldName === 'SiteName') {
      getDefaultSelectField(condition, siteInfos, searchField.value, searchField.fieldName, searchField.fieldOperator);
    } else {
      condition[searchField.fieldName] = searchField.value;
    }
  }
  return condition;
};

// 处理传入的查询条件入参
const getDefaultSelectField = function(condition, defaultObj, selectValue, selectFieldName, selectFieldOperator) {
  for (let key in defaultObj) {
    if (selectValue !== defaultObj[key]) {
      continue;
    }
    if (selectFieldOperator !== 0) {
      condition[selectFieldName] = key;
      return;
    }
    if (condition[selectFieldName] === undefined) {
      condition[selectFieldName] = `"${key}"`;
    } else {
      condition[selectFieldName] = `${condition[selectFieldName]} OR "${key}"`;
    }
  }
  if (condition[selectFieldName] === undefined) {
    condition[selectFieldName] = selectValue;
  }
};

/**
 * 通过传入的的全量过滤条件, 获取查询的操作符
 */
export const getFilterOperator = function(filters) {
  let condition = {};
  for (let i = 0; i < filters.length; i++) {
    let searchField = filters[i];
    if (!searchField.value) {
      continue;
    }

    condition[searchField.fieldName] = searchField.fieldOperator || 0;
  }
  return condition;
};

const cache = {};

/**
 * 所有页签的缓存, 用于切换页签时快速恢复页签状态
 */
export const pageCache = {
  setCache: cfg => {
    Object.assign(cache, cfg);
  },
  getCache: () => cache,
  removeCache: (prop) => {
    if (Object.prototype.hasOwnProperty.call(cache, prop)) {
      Reflect.deleteProperty(cache, prop);
    }
  },
};


// 字段类型
export const FIELD_TYPE = {
  keyword: 'keyword',
  text: 'text',
};

// 字段操作符
export const KEYWORD_OPERATOR = [
  { name: 'N/A', value: 0 },
  { name: 'is', value: 1 },
  { name: 'is not', value: 2 },
  { name: 'contain', value: 3 },
  { name: 'not contain', value: 6 },
  { name: 'is one of', value: 7 },
  { name: 'is not one of', value: 8 },
];

export const TEXT_OPERATOR = [
  { name: 'N/A', value: 0 },
  { name: 'analysis', value: 4 },
  { name: 'contain', value: 5 },
  { name: 'not contain', value: 9 },
  { name: 'is one of', value: 10 },
  { name: 'is not one of', value: 11 },
];

// 默认操作符
export const DEFAULT_OPERATOR = [
  { name: 'N/A', value: 0 },
];

// 允许多选的操作符
export const MULTI_VALUE_OPERATOR_SET = ['is one of', 'is not one of'];

// 允许反义的操作符
export const OPPOSITE_OPERATOR_MAP = [
  { name: 'contain', opposite: 'not contain', hasNot: false },
  { name: 'not contain', opposite: 'contain', hasNot: true },
  { name: 'is', opposite: 'is not', hasNot: false },
  { name: 'is not', opposite: 'is', hasNot: true },
  { name: 'is one of', opposite: 'is not one of', hasNot: false },
  { name: 'is not one of', opposite: 'is one of', hasNot: true },
];

// 根据过滤条件的类型，将操作符枚举值转换为文本
export const convertOperatorValueToName = (type, value) => {
  let filter;
  if (type === FIELD_TYPE.keyword) {
    filter = KEYWORD_OPERATOR.find(item => item.value === value);
  } else if (type === FIELD_TYPE.text) {
    filter = TEXT_OPERATOR.find(item => item.value === value);
  }
  if (filter && filter.name) {
    return filter.name;
  }
  return DEFAULT_OPERATOR[0].name;
};

// 根据过滤条件的类型，将操作符文本转换为枚举值
export const convertOperatorNameToValue = (type, name) => {
  let filter;
  if (type === FIELD_TYPE.keyword) {
    filter = KEYWORD_OPERATOR.find(item => item.name === name);
  } else if (type === FIELD_TYPE.text) {
    filter = TEXT_OPERATOR.find(item => item.name === name);
  }
  if (filter && filter.value) {
    return filter.value;
  }
  return DEFAULT_OPERATOR[0].value;
};

// 过滤条件中的值校验，默认值校验
export const filterValidator = (values) => {
  return commonValidator(values, CONDITION_MULTI_VALUE_SUM, CONDITION_VALUE_LENGTH, FILTER_FIELDS_SEPARATOR);
};

export const filterPresetValidator = (values) => {
  return commonValidator(values, 1000, CONDITION_VALUE_LENGTH, FILTER_FIELDS_SEPARATOR) &&
      values.join(',').length <= 2048;
};

const commonValidator = (values, sum, length, char) => {
  const regExp = new RegExp(char);
  let currentSum = 0;
  for (const value of values) {
    const trimValue = value.trim();
    if (!trimValue) {
      continue;
    }
    if (trimValue.length > length) {
      return false;
    }
    if (regExp.test(value)) {
      return false;
    }
    currentSum += 1;
    if (currentSum > sum) {
      return false;
    }
  }
  return true;
};

// 单个过滤条件可设置的值最大长度
export const CONDITION_VALUE_LENGTH = 256;

// 单个过滤条件最多可选择的值个数
export const CONDITION_MULTI_VALUE_SUM = 10;

// 最大长度
export const CONDITION_LABEL_LENGTH = 64;

// 下拉框可选项的最大个数
export const OPTIONS_MAX_COUNT = 1000;

// 编辑过滤条件对话框类型
export const FILTER_EDITOR_MODE = {
  NEW: 'new',
  MODIFY: 'modify',
};

// 与后台约定的多值字符串拼接分隔符
export const FILTER_FIELDS_SEPARATOR = ',';

/**
 * 获取查询可能需要的固定入参
 * */
export const getFixedCondition = ({validTimeRange, timeRange, id, indexName, solutionType, searchIndexPatternName, timeField}) => {
  if (!validTimeRange) {
    return { success: false };
  }
  const {start, end} = getUTCTimeRange(timeRange);
  const beginTimeStamp = Math.floor(start / 1000) * 1000;
  const endTimeStamp = Math.floor(end / 1000) * 1000 + 999;
  if (beginTimeStamp >= endTimeStamp) {
    return { success: false };
  }
  let pattern = {};
  // 普通模板（单数据源）
  if (id && !searchIndexPatternName) {
    pattern = {
      indexName: indexName, // 数据源
      solutionType: solutionType, // 解决方案
    };
  }
  // 索引模板（多数据源）
  if (id && searchIndexPatternName) {
    pattern = {
      searchIndexPatternName: searchIndexPatternName, // 索引名
    };
  }
  // 索引规则（无模板）
  if (!id && searchIndexPatternName) {
    pattern = {
      searchIndexPatternName: searchIndexPatternName,
    };
  }
  return {
    indexName: pattern.indexName,
    solutionType: pattern.solutionType,
    searchIndexPatternName: pattern.searchIndexPatternName,
    timeField: timeField,
    beginTimeStamp: beginTimeStamp,
    endTimeStamp: endTimeStamp,
    beginTimeUTCStr: `${tools.timeStampToUTC(beginTimeStamp)}.000`,
    endTimeUTCStr: `${tools.timeStampToUTC(endTimeStamp)}.999`,
    success: true,
  };
};

/**
 * 根据界面过滤条件区获取查询请求入参
 * */
export const getQueryFilterCondition = ({filters, siteInfos, groupNames, solutionTypes, solutionInfos, clusterTypes, clusterInfos}) => {
  const queryFields = {};
  const operatorFields = {};
  const queryFilters = filters.filter(item => item.queried && !item.banned && item.values);
  for (let filter of queryFilters) {
    const isMulti = MULTI_VALUE_OPERATOR_SET.includes(
      convertOperatorValueToName(filter.fieldType, filter.fieldOperator));
    let values;
    let oldValues = filter.values.map(item => item.trim()).filter(item => item);
    if (!isMulti) {
      oldValues = oldValues.length ? [oldValues[0]] : [];
    }
    if (!filterValidator(oldValues)) {
      return { success: false };
    }
    switch (filter.fieldName) {
      case 'SiteName':
        values = convertDisplayValues(oldValues, filter.fieldType, siteInfos);
        break;
      case 'GroupName':
        values = convertDisplayValues(oldValues, filter.fieldType, groupNames);
        break;
      case 'SolutionType':
        values = convertDisplayValues(oldValues, filter.fieldType, solutionTypes);
        break;
      case 'SolutionId':
        values = convertDisplayValues(oldValues, filter.fieldType, solutionInfos);
        break;
      case 'ClusterType':
        values = convertDisplayValues(oldValues, filter.fieldType, clusterTypes);
        break;
      case 'ClusterId':
        values = convertDisplayValues(oldValues, filter.fieldType, clusterInfos);
        break;
      default:
        values = oldValues;
    }
    if (!values || !values.length) {
      continue;
    }
    if (isMulti) {
      queryFields[filter.fieldName] = values.filter(item => item.trim()).join(FILTER_FIELDS_SEPARATOR);
    } else {
      queryFields[filter.fieldName] = values[0].trim();
    }
    operatorFields[filter.fieldName] = filter.fieldOperator;
  }
  return {
    queryFields: queryFields,
    operatorFields: operatorFields,
    success: true,
  };
};

// 网元关系别名转化为实际值
const convertDisplayValues = (values, type, infos) => {
  if (!infos || !values || !values.length) {
    return values;
  }
  let results = [];
  for (const value of values.map(item => item.trim()).filter(item => item)) {
    let result = null;
    for (const key in infos) {
      if (!Object.prototype.hasOwnProperty.call(infos, key)) {
        continue;
      }
      if (infos[key] !== value) {
        continue;
      }
      if (type !== DEFAULT_OPERATOR[0].value) {
        result = key;
        break;
      }
      // 一个别名可以被多个key关联，NA操作符支持or语法
      result = result ? `${result} OR "${key}"` : `"${key}"`;
    }
    if (!result) {
      result = value;
    }
    results = [...results, result];
  }
  return results;
};

export const BEST_BAR_INTERVAL = [
    { name: '1s', value: 1000 },
    { name: '5s', value: 5 * 1000 },
    { name: '10s', value: 10 * 1000 },
    { name: '30s', value: 30 * 1000 },
    { name: '1m', value: 60 * 1000 },
    { name: '5m', value: 5 * 60 * 1000 },
    { name: '10m', value: 10 * 60 * 1000 },
    { name: '30m', value: 30 * 60 * 1000 },
    { name: '1h', value: 60 * 60 * 1000 },
    { name: '2h', value: 2 * 60 * 60 * 1000 },
    { name: '3h', value: 3 * 60 * 60 * 1000 },
    { name: '6h', value: 6 * 60 * 60 * 1000 },
    { name: '12h', value: 12 * 60 * 60 * 1000 },
    { name: '1d', value: 24 * 60 * 60 * 1000 },
    { name: '7d', value: 7 * 24 * 60 * 60 * 1000 },
    { name: '30d', value: 30 * 24 * 60 * 60 * 1000 },
    { name: '365d', value: 365 * 24 * 60 * 60 * 1000 },
];

// 定义显示类型枚举
export const SHOW_TYPE = {
  TIME_RANGE: 0,
  QUICK_SELECT: 1,
};

// 定义选择模式枚举
export const SELECT_MODE = {
  RECENT: 0, // 最近**
  TIME_NOW: 1, // 确定时间点-now
  TIME_TIME: 2, // 确定时间点-确定时间点
  RESET: 3, // 重置时间
};
export const ONE_DAY = 86400;
export const ONE_HOUR = 3600;
export const ONE_MINUTE = 60;
export const MS_IN_ONE_SECOND = 1000;
export const DEFAULT_MS = 999;

export const getUTCTimeRange = (timeRange) => {
  saveTimeRange(timeRange);
  const {selectMode, spinNum, unit, startTime, endTime} = timeRange;
  let start;
  let end;
  if (selectMode === 1 || selectMode === 0) {
    end = Date.now();
    if (selectMode === 0) {
      start = end - spinNum * unit * MS_IN_ONE_SECOND;
    } else {
      start = localToUTCTimestamp(startTime);
    }
  } else {
    start = localToUTCTimestamp(startTime);
    end = localToUTCTimestamp(endTime) + DEFAULT_MS;
  }

  return {start: start, end: end};
};

export const saveTimeRange = (newRange) => {
  // 从 localStorage 中读取现有数据
  let recentTimeRangeFromStorage = JSON.parse(window.localStorage.getItem('recentTimeRange')) || [];

  // 检查并删除重复的旧条目
  recentTimeRangeFromStorage = recentTimeRangeFromStorage.filter(item => {
    // 如果所有属性都相同，则认为是重复条目
    return (
      item.showName !== newRange.showName
    );
  });

  // 将新条目推入数组
  recentTimeRangeFromStorage.push(newRange);

  // 限制数组总量为8个，删除最旧的条目
  if (recentTimeRangeFromStorage.length > 8) {
    recentTimeRangeFromStorage.shift(); // 删除第一个元素
  }

  // 将更新后的数组保存回 localStorage
  window.localStorage.setItem('recentTimeRange', JSON.stringify(recentTimeRangeFromStorage));
};

export const DRAWER_TYPE = {
  table: 0,
  line: 1,
  alarm: 2,
};

export const DRAWER_TITLE = {
  [DRAWER_TYPE.table]: $t('logmatrix.search.button.drawer.title.table'),
  [DRAWER_TYPE.line]: $t('logmatrix.search.button.drawer.title.line'),
  [DRAWER_TYPE.alarm]: $t('logmatrix.search.button.drawer.title.alarm'),
};

export const ALARM_EMPTY_PARAM = {
  /** 数据源 */
  dataSource: null,
  /** 解决方案类型 */
  solutionType: null,
  /** 索引名 */
  searchIndexPatternName: null,
  /** 模板名 */
  indexLabel: '',
  /** 模板中时间字段 */
  timeField: '',
  /** 过滤条件 */
  filterTerms: {},
  /** 过滤条件操作符 */
  operatorFields: {},
  /** 告警名称 */
  alertName: '',
  /** 级别 */
  level: '',
  /** 附加信息描述 */
  solution: '',
  /** 粒度 */
  mergeField: 'ClusterId',
  /** 自定义粒度的字段 */
  mergeFieldType: 'keyword',
  /** 触发时间 */
  timeRange: {},
  /** 触发条件-类型 */
  type: '',
  /** 触发条件-规则 */
  rule: '',
  /** 触发条件-规则-细节 */
  condition: {
    operator: 'LSS',
    value: '',
  },
  /** 触发条件-规则-细节-聚合字段 */
  aggregationField: '',
  /** 屏蔽规则 */
  timeShield: null,
};

export const ALARM_LEVEL_OPTION = [
  {
    text: $t('logmatrix.search.button.drawer.alarm.level.crirical'),
    value: 'CRITICAL',
  },
  {
    text: $t('logmatrix.search.button.drawer.alarm.level.major'),
    value: 'MAJOR',
  },
  {
    text: $t('logmatrix.search.button.drawer.alarm.level.minor'),
    value: 'MINOR',
  },
  {
    text: $t('logmatrix.search.button.drawer.alarm.level.warning'),
    value: 'WARNING',
  },
];

export const TIME_UNIT_SELECT_OPTION = [
  {
    text: 'minute',
    value: 'minute',
  },
  {
    text: 'hour',
    value: 'hour',
  },
];

export const ALARM_TYPE_SELECT_OPTION = [
  {
    text: $t('logmatrix.search.button.drawer.alarm.config.subTitle.eventAlarm'),
    value: 'EVENT_NUMBER',
  },
  {
    text: $t('logmatrix.search.button.drawer.alarm.config.subTitle.fieldAlarm'),
    value: 'FILED_STATISTICS',
  },
];

export const EVENT_RULE_SELECT_OPTION = [
  {
    text: $t('logmatrix.search.button.drawer.alarm.config.subTitle.count'),
    value: 'COUNT',
  },
];

export const FIELD_RULE_SELECT_OPTION = [
  {
    text: $t('logmatrix.search.button.drawer.alarm.config.subTitle.sum'),
    value: 'SUM',
  },
  {
    text: $t('logmatrix.search.button.drawer.alarm.config.subTitle.avg'),
    value: 'AVG',
  },
  {
    text: $t('logmatrix.search.button.drawer.alarm.config.subTitle.max'),
    value: 'MAX',
  },
  {
    text: $t('logmatrix.search.button.drawer.alarm.config.subTitle.min'),
    value: 'MIN',
  },
];

export const OPERATOR_SELECT_OPTION = [
  {
    text: $t('logmatrix.search.button.drawer.alarm.config.subTitle.less'),
    value: 'LSS',
  },
  {
    text: $t('logmatrix.search.button.drawer.alarm.config.subTitle.equal'),
    value: 'EQU',
  },
  {
    text: $t('logmatrix.search.button.drawer.alarm.config.subTitle.greater'),
    value: 'GTR',
  },
];

const NAME_REGEX = new RegExp('^[^\t#%&+=|><\';?"/\\\\\\(\\)]{1,64}$');

export const NAME_VALIDATOR = (value) => {
  if (NAME_REGEX.test(value)) {
    return {
      result: true,
    };
  } else {
    return {
      result: false,
      message: $t('logmatrix.search.button.drawer.alarm.validator.name'),
    };
  }
};

export const TIME_VALIDATOR = (isMinute) => (value) => {
  const n = parseInt(value);
  if (isMinute) { // 校验分钟, 范围1~59
    if (n <= 59 && n >= 1) {
      return { result: true };
    } else {
      return {
        result: false,
        message: $t('logmatrix.search.button.drawer.alarm.validator.minute'),
      };
    }
  } else { // 校验小时, 范围1~23
    if (n <= 23 && n >= 1) {
      return { result: true };
    } else {
      return {
        result: false,
        message: $t('logmatrix.search.button.drawer.alarm.validator.hour'),
      };
    }
  }
};


export const COUNT_VALIDATOR = (value) => {
  const n = parseInt(value);
  if (value.length <= 21 && n >= 1) {
    return { result: true };
  } else {
    return {
      result: false,
      message: $t('logmatrix.search.button.drawer.alarm.validator.count'),
    };
  }
};
