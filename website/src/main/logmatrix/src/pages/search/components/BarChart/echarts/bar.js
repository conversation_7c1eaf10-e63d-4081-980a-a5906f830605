import {tools} from '@util';

const barOption = {
  tooltip: '',
  grid: '',
  color: '',
  legend: '',
  dataZoom: '',
  xAxis: [{
    show: true,
    type: 'category',
    axisLine: { // 坐标轴线
      show: true,
      lineStyle: { color: '#e5e5e5', width: 1, type: 'solid' }, 
    },
    axisLabel: { show: true, textStyle: { color: '#666' } },
    axisTick: { show: true },
    splitLine: { show: false },
    boundaryGap: true,
    data: [],
  }],
  yAxis: [{
    type: 'value',
    axisLine: { show: false, lineStyle: { color: '#e5e5e5', width: 1, type: 'solid' } },
    axisTick: { show: false },
    splitLine: { show: true, lineStyle: { color: '#e5e5e5', width: 1, type: 'dotted' } },
  }],
  series: [],
};

export const getBarOption = (data) => {
  const option = tools.cloneObject(barOption);
  // 根据data计算seriesList
  const seriesList = validateSeriesData(data);
  // 计算x轴
  const maxMinList = getMaxMinList(seriesList.yDataList);
  const xSignal = formatXList(seriesList.xList);
  option.xAxis[0].data = xSignal.xList;
  // 计算grid
  option.grid = {
    borderColor: '#e5e5e5', borderWidth: 0,
    x: (maxMinList.len / 3 < 1) ? 35 : (maxMinList.len + 1 + maxMinList.len / 3) * 7 + 7, y: 0,
    x2: 40, y2: (xSignal.signal === true) ? 50 : 35,
  };

  // 组合所有options
  return assembleOptionData(option, seriesList);
};

const validateSeriesData = (seriesDataList) => {
  let seriesDataListVal = seriesDataList;
  if (seriesDataListVal.length === 0) {
    seriesDataListVal = [];
    return seriesDataListVal;
  }
  const obj = parseChartJson(seriesDataListVal);
  if (obj.yDataList.length % obj.legendNameList.length !== 0) {
    return [];
  }
  if (obj.legendNameList.length === 0) {
    return [];
  }
  if (obj.xDataList.length === 0 || obj.xList.length === 0) {
    return [];
  }
  return obj;
};

const parseChartJson = (seriesDataList) => {
  const xList = [];
  const yList = [];
  let obj = {
    xDataList: xList,
    yDataList: yList,
    legendNameList: [],
    wavePointList: { brust: [] },
    xList: [],
    markPointData: [],
  };
  let index = 0;
  for (let i = 0; i < seriesDataList.length; i++) {
    obj.legendNameList[i] = seriesDataList[i].name;
    let dataList = [];
    let valueList = [];
    for (let j = 0; j < seriesDataList[i].values.length; j++) {
      dataList[j] = seriesDataList[i].values[j].data;
      valueList[j] = seriesDataList[i].values[j].value;
      if (seriesDataList[i].values[j].isBrust === 1) {
        obj.wavePointList.brust[index++] = i;
        obj.wavePointList.brust[index++] = dataList[j];
        obj.wavePointList.brust[index++] = valueList[j];
      }
    }
    obj.xDataList[i] = dataList;
    obj.yDataList[i] = valueList;
  }

  const object = distinctArray(obj.xDataList);
  obj.xList = object.data;
  obj = reformatDataList(object, obj);
  return obj;
};

const distinctArray = (array) => {
  const obj = { data: [], indexs: [] };
  const temp = [];
  const flag = {};
  const indexs = [];
  for (let i = 0; i < array.length; i++) {
    for (let j = 0; j < array[i].length; j++) {
      if (!flag[array[i][j]] && array[i][j] !== '') {
        flag[array[i][j]] = true;
        temp.push(array[i][j]);
        indexs.push(j);
      }
    }
  }
  obj.data = temp;
  obj.indexs = indexs;
  return obj;
};

const reformatDataList = (object, obj) => {
  const newDataList = [];
  for (let i = 0; i < obj.yDataList.length; i++) {
    const dataList = [];
    for (let j = 0; j < object.indexs.length; j++) {
      dataList.push(obj.yDataList[i][object.indexs[j]]);
    }
    newDataList.push(dataList);
  }
  obj.yDataList = newDataList;
  return obj;
};

const getMaxMinList = (yDataList) => {
  const maxMinList = { max: [], min: [], len: 0 };
  for (let i = 0, li = yDataList.length; i < li; i++) {
    for (let j = 0; j < yDataList[i].length; j++) {
      if (yDataList[i][j] !== 'undefined' && yDataList[i][j] !== '-') {
        maxMinList.max[i] = Number(yDataList[i][j]);
        maxMinList.min[i] = Number(yDataList[i][j]);
        break;
      }
    }
    if (typeof (maxMinList.max[i]) === 'undefined' ||
      typeof (maxMinList.min[i]) === 'undefined') {
      maxMinList.max[i] = 0;
      maxMinList.min[i] = 0;
      continue;
    }
    for (let j = 0, lj = yDataList[i].length; j < lj; j++) {
      maxMinList.max[i] = (maxMinList.max[i] < Number(yDataList[i][j])) ? yDataList[i][j] : maxMinList.max[i];
      maxMinList.min[i] = (maxMinList.min[i] > Number(yDataList[i][j])) ? yDataList[i][j] : maxMinList.min[i];
    }

    const maxNum = parseInt(maxMinList.max[i]);
    const minNum = parseInt(maxMinList.min[i]);
    if (maxNum !== maxMinList.max[i] || minNum !== maxMinList.min[i]) {
      maxMinList.len = maxMinList.len < (maxNum.toString()).length ? (maxNum.toString()).length + 2 : maxMinList.len;
      maxMinList.len = maxMinList.len < (minNum.toString()).length ? (minNum.toString()).length + 2 : maxMinList.len;
    } else {
      maxMinList.len = maxMinList.len < (maxMinList.max[i].toString()).length ? (maxMinList.max[i].toString()).length : maxMinList.len;
      maxMinList.len = maxMinList.len < (maxMinList.min[i].toString()).length ? (maxMinList.min[i].toString()).length : maxMinList.len;
    }
  }
  return maxMinList;
};

const formatXList = (xList) => {
  const obj = {};
  for (let i = 0; i < xList.length; i++) {
    xList[i] = xList[i].toString();
    let length = xList[i].length;
    if (length > 10) {
      xList[i] = `${xList[i].substr(0, 10)}\n${xList[i].substr(10, length - 10)}`;
      obj.signal = true;
    }
  }
  obj.xList = xList;
  return obj;
};

const assembleOptionData = (optionData, seriesList) => {
  let optionDataVal = optionData;
  optionDataVal.tooltip = {
    formatter(params) {
      if (params.length === 0) {
        return `<div />`;
      }
      const param = params[0];
      return `
          <font style="font-family: helvetica;color: #666;font-size: 14px;">
            <b>${param.name}</b>
          </font>
          <br />
          <font style="font-family: arial;color:#42ccef;font-size: 14px;">
          <b>${param.value}:</b>
          </font> 
          <font style="font-family: microsoftyahei;color: #666;font-size: 14px;">
            ${param.seriesName}
          </font>
          <br />
        </>
      `;
    },
    show: true,
    trigger: 'axis',
  }; 
  optionDataVal.legend = {
    show: false,
    textStyle: { align: 'center' },
    orient: 'horizontal',
    itemGap: 10,
    x: 'right',
    y: 'top',
    padding: [0, 20],
    data: seriesList.legendNameList,
  };
  optionDataVal.dataZoom = {
    show: false,
    realtime: true,
    borderWidth: 1,
    height: 20,
    start: 0,
    end: 100,
    fillerColor: '#ffffff',
    backgroundColor: '#ebebeb',
  };
  optionDataVal = setBarSeriesDataFormat(optionDataVal, seriesList.yDataList, seriesList.legendNameList);
  return optionDataVal;
};

const setBarSeriesDataFormat = (optionData, yDataList, legendNameList) => {
  const optionDataVal = optionData;
  for (let i = 0; i < legendNameList.length; i++) {
    optionDataVal.series[i] = {
      name: legendNameList[i],
      type: 'bar',
      stack: '',
      smooth: true,
      symbol: 'none',
      showAllSymbol: true,
      symbolSize: 2,
      barMaxWidth: 14,
      data: yDataList[i],
      itemStyle: {
        normal: {
          barBorderRadius: [0, 0, 0, 0],
        },
        emphasis: {
          barBorderRadius: [0, 0, 0, 0],
        },
      },
    };
  }
  return optionDataVal;
};