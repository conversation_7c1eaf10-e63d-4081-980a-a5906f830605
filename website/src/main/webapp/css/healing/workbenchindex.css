/*dashboard*/
.top-banner {
	width: 100%;
	height: 240px;
	background: url(/dvihealingwebsite/images/healing/banner.png) center center no-repeat;
	margin: 20px 0px;
}
.top-banner > ul > li {
	float: left;
	width: 22.5%;
	height: 185px;
	margin: 30px 0px 0px 2%;
	border-radius: 6px;
	background: rgba(255,255,255,0.2);
	border: 1px solid rgba(255,255,255,0.7);
	box-sizing: border-box;
	box-shadow: 0px 3px 10px rgba(0,0,0,0.15);
	-webkit-transition: all .2s linear 0s;
    -o-transition: all .2s linear 0s;
    -moz-transition: all .2s linear 0s;
    -ms-transition: all .2s linear 0s;
    transition: all .2s linear 0s;
    cursor: pointer;
}
.top-banner > ul > li.temp-no-cursor{
	cursor: default;
}

.top-banner > ul > li.temp-no-cursor > .right-data li{
	cursor: pointer;
}

.top-banner > ul > li:hover {
	box-shadow: 0px 2px 10px rgba(255,255,255,0.7);
}
.top-banner > ul > li > .left-icon {
	float: left;
	width: 45%;
	padding-top: 32px;
    text-align: center;
}
.top-banner > ul > li > .right-data {
	float: left;
	width: 55%;
	height: 100%;
}
.top-banner > ul > li > .left-icon > img {

}
.top-banner > ul > li > .left-icon > p {
	color: #fff;
    font-size: 14px;
    padding-top: 10px;
}
.top-banner > ul > li > .right-data table {
	width: 100%;
	height: 100%;
}
.top-banner > ul > li > .right-data td {
	padding: 0px 16% 12px 0px;
}
.top-banner > ul > li > .right-data li {
	height: 40px;
	padding-top: 5px;
	border-bottom: 1px solid #acacac;
}
.top-banner > ul > li > .right-data li em {
	font-size: 36px;
	color: #fff;
}
.top-banner > ul > li > .right-data li span {
	float: right;
	color: #fff;
	margin-top: 19px;
}
.dashboard-task {
	height: 545px;
	position: relative;
}
.dashboard-left {
	position:absolute;
	width: 350px;
	height: 100%;
}
.dashboard-right {
	float: right;
	padding-left: 350px;
}
.dashboard-right > h3 {
	margin-top: 16px;
    font-size: 14px;
    padding-bottom: 10px;
}
.dashboard-right > h3 > span {
	font-size: 22px;
}
.df-input {
	box-sizing: content-box;
}
.df-select-box input {
	box-sizing: content-box;
}
#mainContainer {
	position: relative;
}
.main-title {
	position: relative;
	font-size: 18px;
	line-height: 18px;
	height: 18px;
	color: #666;
	padding-bottom: 18px;
	border-bottom: 1px solid #e5e5e5;
	box-shadow: 0px 1px 0px #fff;
}
.main-title.noborder {
	border: 0px;
	box-shadow: none;
}
.main-title > h3 {
	float: left;
	font-size: 18px;
	line-height: 18px;
	color: #666;
}
.main-title .back-track {
	float: left;
	position: relative;
	height: 28px;
	border: 1px solid #c3c3c3;
	line-height: 28px;
	padding: 0px 20px 0px 8px;
	margin: -7px 0px 0px 35px;
	cursor: pointer;
}
.main-title .back-track:before {
	position: absolute;
	content: '';
	width: 25px;
	height: 30px;
	top: -1px;
    left: -24px;
	background: url(/dvfoundationwebsite/images/back.png) 0px 0px no-repeat;
}
.main-title .main-track {
	position: absolute;
    right: 0px;
    top: 0px;
}
.main-title .main-track .status-swtich {
	margin: 0px;
}
.workbench-status > span {
	float: left;
	width: 12px;
	height: 12px;
	border-radius: 50%;
	margin: 2px 8px 0px 0px;
}
.workbench-status.success > span {
	background: #5ecc49;
}
.workbench-status.wait > span {
	background: #dadada;
}
.workbench-status.failed > span {
	background: #fc5043;
}
.workbench-status.warnning {
	line-height: 20px;
}
.workbench-status.warnning > span {
	width: 16px;
	height: 16px;
	background: url(/dvfoundationwebsite/images/state-alarm.png) 0px 0px no-repeat;
}
.workbench-status.running > span {
	width: 15px;
	height: 15px;
	background: url(/dvfoundationwebsite/images/in.gif) 0px 0px no-repeat;
	margin: 1px 7px 0px -2px;
}
.taskchart {
	height: 320px;
}
/*任务列表页面*/
.workbench-operate {
	padding: 20px 0px 10px;
}
.workbench-operate > p {
	float: left;
	line-height: 26px;
	color: #666;
	padding-right: 8px;
}
.workbench-operate .status-swtich {
	margin-right: 0px;
}
.workbench-operate > h3 {
	color: #666;
	font-size: 14px;
	line-height: 14px;
	padding-bottom: 20px;
}
.workbench-operate-search {
	padding-bottom: 10px;
}
.workbench-operate-search > ul > li {
	float: left;
	margin: 0px 40px 20px 0px;
}
.workbench-operate-search > ul > li > p {
	float: left;
	line-height: 26px;
	color: #666;
	padding-right: 5px;
}
.operate-tool li {
	float: left;
	width: 24px;
	height: 16px;
}

.icon-p-copy {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) 0px 0px;
	cursor: pointer;
}
.icon-p-copynew {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) 0px -16px;
	cursor: pointer;
}
.icon-p-copynew.disabled {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -64px -16px;
	cursor: pointer;
}
.icon-p-cron {
	width: 14px;
	height: 14px;
	background: url(/dvihealingwebsite/images/healing/cron-valid.png) 0px 0px no-repeat;
	margin-top: 2px;
	cursor: pointer;
}
.icon-p-cron.disabled {
	width: 14px;
	height: 14px;
	background: url(/dvihealingwebsite/images/healing/cron-invalid.png);
	cursor: default;
}
.icon-p-startup {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) 0px -32px;
	cursor: pointer;
}
.icon-p-startup.disabled {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) 0px -64px;
	cursor: default;
}
.icon-p-stop {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) 0px -48px;
	cursor: pointer;
}
.icon-p-stop.disabled {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -16px -80px;
	cursor: default;
}
.icon-p-execute {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -32px 0px;
	cursor: pointer;
}
.icon-p-execute.disabled {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) 0px -80px;
	cursor: default;
}
.icon-p-search {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -16px 0px;
	cursor: pointer;
}
.icon-p-lastsearch {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/lastest-exe-valid.png) ;
	cursor: pointer;
}
.icon-p-lastsearch.disabled {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/lastest-exe-invalid.png) ;
	cursor: default;
}
.icon-p-edit {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -16px -16px;
	cursor: pointer;
}
.icon-p-edit.disabled {
	width: 16px;
	height: 16px;
	background: url/dvihealingwebsite/images/healing/icon.png) -32px -16px;
	cursor: default;
}
.icon-p-del {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -16px -32px;
	cursor: pointer;
}
.icon-p-del.disabled {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -48px -80px;
	cursor: default;
}
.icon-p-viewlog {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -32px -80px;
	cursor: pointer;
}
.icon-p-download {
	width: 16px;
	height: 16px;
	background: url(/dvfoundationwebsite/images/download.png) 0px 0px no-repeat;
	cursor: pointer;
}

.downloadlink {
	    color: #00aaff;
}

.icon-p-redo {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -32px -32px;
	cursor: pointer;
}
.icon-p-clone {
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -48px -48px;
	cursor: pointer;
}
.tasklist-expanded {
	padding: 0px 0px 20px 14px;
}
.tasklist-expanded th {
	line-height: 40px;
	color: #333;
	padding: 0px 0px 0px 6px;
	border-top: 0px;
	padding-left: 5px !important;
}
.tasklist-expanded tr {
	background: none !important;
}
.tasklist-expanded table {
	table-layout: fixed;
}
/*快速脚本执行*/
.CodeMirror span {
	color: #fff;
}
.CodeMirror {
	height: 200px;
}
.CodeMirror-linenumber {
	padding: 0px;
	text-align: center;
}
.cm-s-rubyblue .CodeMirror-gutters {
    background: #434343;
    border-right: 1px solid #636363;
}
.cm-s-rubyblue.CodeMirror {
	background: #434343;
}
.CodeMirror-fullscreen {
  position: fixed;
  right: 0;
  bottom: 0;
  top: 0;
  left: 0;
  height: auto !important;
  z-index: 1001;
}
.jscode-textarea {
	width: 700px;
	border: 1px solid #d3d3d3;
	border-radius: 4px;
	overflow: hidden;
}
.jscode-textarea-top {
	height: 24px;
	background: #fff;
	padding:7px 10px 0px 18px;
	border-bottom: 1px solid #d3d3d3;
}
.jscode-textarea.popup{
	width: 450px;
}
/*脚本详情弹出框的codemirror，大一点*/
.large-codemirror .CodeMirror{
	height: 330px;
}
.large-codemirror .jscode-textarea.popup{
	width: 545px;
}
.fullscreen-bar {
	float: right;
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -16px -48px;
	cursor: pointer;
}
.esc-tips {
	position: fixed;
	display: none;
	padding: 10px;
	border: 1px solid #e5e5e5;
	background: #fff;
	top: 30px;
	z-index: 1001;
	left: 50%;
	-webkit-transition: top .3s ease-in 0s;
	-o-transition: top .3s ease-in 0s;
	-moz-transition: top .3s ease-in 0s;
	-ms-transition: top .3s ease-in 0s;
	transition: top .3s ease-in 0s;
}
.esc-tips.show {
	display: block;
	top: 0px;
}
.checkbox-column {
	margin-bottom: 20px;
}
.checkbox-column .checkbox {
	display: inline-block;
	vertical-align: middle;
	margin: -1px 10px 0px 0px;
}
.workbench-listcolumn {
	padding: 10px 0px;
}

.workbench-listcolumn.add-min-height {
	min-height : 550px;
}

.workbench-listcolumn.edit-min-height {
	min-height : 480px;
}

.workbench-listcolumn > table > tbody > tr > td {
	padding-bottom: 20px;
}
.workbench-listcolumn > table > tbody > tr > td > p {
	position: relative;
	padding: 0px 6px 0px 8px;
	line-height: 26px;
	color: #666;
}
.workbench-listcolumn > table > tbody > tr > td > p.path-exceed {
	width:1000px;
	white-space: normal;
	word-wrap: break-word;
}

.workbench-listcolumn > table > tbody > tr > td > p > span {
	position: absolute;
	color: #fc5043;
	left: 0px;
    top: 0px;
    font-size: 14px;
}
.workbench-listcolumn > table > tbody > tr > td:first-child {
	width: 98px;
}
.workbench-listcolumn > table > tbody > tr > td .df-input {
	box-sizing: content-box;
}
.workbench-listcolumn > table > tbody > tr > td .df-select-box input {
	box-sizing: content-box;
}
.workbench-listcolumn.fastexe {
	padding-bottom: 0px;
	margin-bottom: -20px;
}
.workbench-listcolumn > ul > li {
	float: left;
	padding-right: 40px;
	margin-bottom: 20px;
}
.workbench-listcolumn > ul > li > p {
	float: left;
	padding-right: 6px;
	color: #666;
	line-height: 26px;
}
.workbench-listcolumn ul>li>span::after{
	content : "";
	padding-left: 5px;
}
.workbench-listcolumn ul>li .df-date-line{
	display: inline;
}

.servicestatus {
	color: #5ecc49
}
.servicestatus.offline {
	color: #fc5043;
}
.cmp-popupwin-wrap  .df-input {
	box-sizing: content-box;
}
.cmp-popupwin-wrap .df-select-box input {
	box-sizing: content-box;
}

.workbench-label {
	line-height: 14px;
    display: inline-block;
    margin-left: 10px;
}
.workbench-label > .df-label-bar {
	float: left;
    margin-right: 5px;
}
.workbench-validate-status {
	display: inline-block;
	width: 16px;
	height: 16px;
	margin: -3px 5px 0px 5px;
    vertical-align: middle;
}
.workbench-validate-status.success {
	background: url(/dvihealingwebsite/images/healing/icon.png) -16px -64px;
}
.workbench-validate-status.fail {
	background: url(/dvihealingwebsite/images/healing/icon.png) -48px -64px;
}
.left-column {
	position: absolute;
	width: 250px;
	border: 1px solid #e5e5e5;
	background: #fff;
	margin-top: 20px;
}
.selectserver-column {
	float: right;
	padding-left: 272px;
	padding-bottom: 10px;
}
.selectserver-column > h3 {
	margin-top: 16px;
    font-size: 14px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e5e5e5;
}
.left-column-header {
	padding: 20px 0px 20px 15px;
    border-bottom: 1px solid #e5e5e5;
}
.left-column-header .df-icon-add {
	position: absolute;
    top: 22px;
    right: 15px;
}
.validate-user {
	text-align: right;
}
.validate-user .df-icon-add {
	display: inline-block;
    vertical-align: middle;
    margin: 0px 15px 0px 5px;
    float: none;
}
.validate-user .df-btn {
	float: none;
}

.no-page-control .ng-table-pagination .df-select-frame {
  display:none
}

/*执行详情*/
.wb-detail-basic {
	padding: 20px 0px;
}
.wb-detail-basic td {
	padding-bottom: 14px;
	max-width: 480px;
}
.wb-detail-basic td p {
	color: #666;
	padding-right: 6px;
}
.wb-detail-title {
	color: #333;
	font-size: 14px;
	padding-bottom: 20px;
}
.wb-step-panel {
	border: 1px solid #e5e5e5;
}
.wb-step-panel.open {
	border: 1px solid #00aaff;
}
.wb-step-panel-header {
	height: 40px;
	background: #f5f5f5;
	border-bottom: 1px solid #e5e5e5;
}
.wb-step-panel.open .wb-step-panel-header {
	border-bottom: 1px solid #00aaff;
}
.wb-step-status {
	float: left;
	position: relative;
	background: #42ccef;
	height: 40px;
	padding: 0px 30px 0px 20px;
	cursor: pointer;
}
.wb-step-status:after {
	position: absolute;
    content: '';
    border-right: 10px solid #f5f5f5;
    border-top: 20px solid transparent;
    border-left: 0px solid transparent;
    border-bottom: 20px solid transparent;
    right: 0px;
    top: 0px;

}
.wb-step-status > p {
	color: #fff;
	font-size: 14px;
	padding-left: 30px;
	line-height: 40px;
}
.wb-step-status.upload {
	background: #9592f0;
}
.wb-step-status.download {
	background: #ffa235;
}
.wb-step-panel-header > h3 {
	float: left;
	line-height: 40px;
	color: #666;
	padding: 0px 5px 0px 10px;
}
.wb-step-panel-header > h3 > i {
	color: #fc5043;
	font-style: normal;
	padding-right: 2px;
}
.wb-step-panelbar {
	float: left;
	width: 15px;
	height: 15px;
	margin-top: 12px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -32px -64px;
}
.wb-step-panel.open .wb-step-panelbar {
	background: url(/dvihealingwebsite/images/healing/icon.png) -32px -48px;
}
.wb-step-panel-content {
	padding: 20px;
	display: none;
}
.wb-step-left {
	float: left;
	width: 50%;
}
.wb-step-right {
	float: right;
	width: 49%;
}
.wb-step-right.combine {
	width: 100%;
}
.wb-step-topbar {
	min-height: 26px;
	padding: 20px 0px;
}
.wb-step-topbar.combine {
	padding: 10px 0px;
	margin-top: -10px;
}
.wb-step-topbar .status-swtich {
	float: right;
	margin: 0px;
}
.wb-step-viewlog {
	border: 1px solid #d5d5d5;
	padding: 5px 15px;
	height: 400px;
}
.wb-step-viewlog > pre {
	line-height: 24px;
	word-break: break-word;
}

/*新建任务*/
.percent-w {
	width: 50%;
}
@media screen and (min-width:1680px) {
   .percent-w {
		width: 45%;
    }
}
.wb-step-panel-operate {
	position: absolute;
    right: 20px;
    top: 13px;
}

.wb-step-panel-header .df-input-frame {
	top: 7px;
}
.df-icon-delete2 {
	float: left;
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -48px -16px no-repeat;
	cursor: pointer;
}
.df-icon-delete2.disabled{
	background: url(/dvfoundationwebsite/images/tool.png) -60px -31px no-repeat;
    height: 15px;
    width: 15px;
}
.addaction {
	display: inline-block;
	position: relative;
	top: 5px;
}
.addaction-menu {
	position: absolute;
	top: 26px;
	background: #fff;
	border:1px solid #dcdcdc;
	border-radius: 0px 0px 4px 4px;
	z-index: 100;
	box-shadow: 0px 2px 4px rgb(230, 226, 226);
}
.addaction-menu li{
	height: 16px;
	text-align: left;
  	padding: 7px 26px;
  	line-height: 16px;
  	color: #666;
  	cursor: pointer;
}
.addaction-menu.group-menu {
	top: 36px;
	left: 20px;
}
.addaction-menu.export-menu {
	top: 25px;
	left: -25px;
	right: 0px;
}
.addaction-menu li.disabled {
	color: #aaa;
	cursor: default;
}
.addaction li:hover {
	background: #00aaff;
  	color: #fff;
}


.retryaction {
	display: inline-block;
	position: relative;
	top: 5px;
}
.retryaction-menu li{
	height: 16px;
  	padding: 7px 16px;
  	line-height: 16px;
  	color: #666;
  	cursor: pointer;
  	text-align:left;
}

.retryaction-menu li.current {
    background: #00aaff;
    color: #fff;
}

.retryaction-menu li.disabled {
    color: #ccc;
}

.retryaction-menu {
	position: absolute;
	background: #fff;
	border:1px solid #dcdcdc;
	border-radius: 0px 0px 4px 4px;
	z-index: 100;
	box-shadow: 0px 2px 4px rgb(230, 226, 226);
}

.df-btn.df-btnicon.retry {
	width:75px;
}
.wb-step-left .df-btnicon .df-icon-download{
	float: none;
	margin: 0px 5px 0 0;
}
.retryaction-menu.group-menu {
	position: relative;
	left: -15px;
	width: 120px;
}

.radio.selectcycle {
	float: none;
	margin-top: 30px;
}
.radio.selectcycle > ul > li {
	float: none;
    padding: 0px 0px 20px;
    height: 16px;
}

.checkbox-frame.selectcycle > li {
	min-width: 40px;
    margin-right: 0px;
    padding-right: 16px;
}
.wb-uploadfile {
	height: 66px;
}
.wb-uploadfile-btn {
	float: left;
	position: relative;
	width: 98px;
	height: 32px;
	border-radius: 3px;
	background: #b7b7b7;
	line-height: 32px;
	text-align: center;
	overflow: hidden;
	cursor: pointer;
}
.wb-uploadfile-btn > span {
	color: #fff;
	position: relative;
	z-index: 10;
}
.wb-uploadfile-btn > div {
	position: absolute;
	width: 100%;
	height: 32px;
	top: 0px;
	background: #00aaff;
}
.wb-uploadfile > a {
	line-height: 32px;
    margin-left: 10px;
    color: #00aaff;
}
.wb-uploadfile-file > p {
	float: left;
	line-height: 14px;
	margin-right: 10px;
}
.wb-uploadfile-file {
	margin-top: 20px;
}
.wb-uploadfile-file .df-icon-delete {
	margin: 0px;
}

.typeahead.dropdown-menu {
	position: absolute;
	background: #fff;
    border: 1px solid #4ac9ff;
    border-radius: 0px 0px 4px 4px;
    border-top: 0px;
    z-index: 100;
    max-height: 210px;
    overflow: auto;
    width: 100%;
    box-sizing: border-box;
}
.typeahead.dropdown-menu > li {
	height: 16px;
    padding: 5px 8px;
    line-height: 16px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.typeahead.dropdown-menu > li > a {
	color: #000;
}
.typeahead.dropdown-menu li:hover {
	background: #f3f3f3;
  	color: #4ac8fe;
}

 .typeahead.dropdown-menu > li > a  strong {
	font-weight: bold;
}

.stress-bg {
    height: 24px;
    line-height: 24px;
    padding: 0px 10px;
    background: #00aaff;
    color: #fff;
    width: 60px;
    display: inline-block;
}
.stress-bg.no-color{
	background: none;
	color: #000;
}
.stress-bg.auto {
	width: auto;
}
.stress-bg.warnning {
	background: #fc5043;
}
.wb-log-textarea
{
    width:100%;
    height:100%;
    border-style:none;
}

/*黑名单*/
.blacklist-wrap {
	margin-top: 20px;
	height: 594px;
}
.blacklist-widget {
	width: 100%;
	float: right;
	height: 650px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 1px 0px 4px rgba(0,0,0,0.14);
}
.blacklist-widget:first-child {
	float: left;
}
.blacklist-tool {
	float: left;
	width: 10%;
	height: 100%;
}
.blacklist-widget .blacklist-title {
	padding-bottom: 20px;
    border-bottom: 1px solid #ddd;
    height: 20px;
    margin-bottom: 20px;
}
.blacklist-widget .blacklist-title > h3 {
	float: left;
	font-size: 14px;
	color: #666;
	line-height: 36px;
}
.blacklist-widget-inner {
	padding: 20px;
}
.blacklist-btn-left,
.blacklist-btn-right {
	position: relative;
	display: inline-block;
	height: 28px;
	border: 1px solid #c3c3c3;
	line-height: 28px;
	padding: 0px 12px 0px 8px;
	margin: 20px 0px 0px 12px;
	cursor: pointer;
}
.blacklist-btn-right {
	margin: 0px 5px 0px 0px;
	padding: 0px 8px 0px 12px;
}
.blacklist-btn-left:before {
	position: absolute;
	content: '';
	width: 12px;
	height: 30px;
	top: -1px;
	left: -12px;
	background: url(/dvfoundationwebsite/images/back.png) 0px 0px no-repeat;
}
.blacklist-tool td {
	text-align: center;
}
.workbench-tipslabel {
	line-height: 16px;
    display: inline-block;
    margin-left: 10px;
    margin-top: 5px;
}
.workbench-tipslabel > .df-label-bar {
	float: left;
    margin-right: 5px;
}

#editScriptJscode {
	width: 640px;
}
#editScriptJscode .CodeMirror {
	height: 345px;
}
#editScriptJscode .workbench-status.warnning {
    line-height: 20px;
    float: right;
    margin-right: 20px;
}
/*ng-table表头样式修改*/
.tableCommon-header {
	line-height: 30px;
    height: 30px;
    background: #7a89a2;
}
.ng-table th {
	padding: 0px;
}
.tableCommon-header > span {
    border-left: 0;
    color: #fff;
}
.tableCommon .tableCommon-header > span{
	padding-left: 5px;
	border-left: 1px solid #dddddd;
}
.ng-table th .checkbox {
	margin-top: 4px;
}


.progress {
  background: #ebebeb;
  border-radius: 10px;
  border-left: 1px solid transparent;
  border-right: 1px solid transparent;
  height: 20px;
}

.progress > span {
  margin: 0 -1px;
  min-width: 30px;
  position: relative;
  float: left;
  height: 18px;
  background: #cccccc;
  border: 1px solid;
  line-height: 16px;
  text-align: right;
  border-color: #bfbfbf #b3b3b3 #9e9e9e;
  border-radius: 10px;
  background-image: -o-linear-gradient(top, #f0f0f0 0%, #dbdbdb 70%, #cccccc 100%);
  background-image: linear-gradient(to bottom, #f0f0f0 0%, #dbdbdb 70%, #cccccc 100%);
  background-image: -webkit-linear-gradient(top, #f0f0f0 0%, #dbdbdb 70%, #cccccc 100%);
  background-image: -moz-linear-gradient(top, #f0f0f0 0%, #dbdbdb 70%, #cccccc 100%);
  -webkit-box-shadow: inset 0 1px rgba(255, 255, 255, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
  box-shadow: inset 0 1px rgba(255, 255, 255, 0.3), 0 1px 2px rgba(0, 0, 0, 0.2);
}

.progress > span > span {
  padding: 0 8px;
  color: #404040;
  color: rgba(0, 0, 0, 0.7);
  font-size: 11px;
  font-weight: bold;
  text-shadow: 0 1px rgba(255, 255, 255, 0.4);
}

.progress > span:before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  height: 18px;
  top: 0;
  bottom: 0;
  z-index: 1;
  background: url("/dvihealingwebsite/images/healing/progress.png") 0 0 repeat-x;
  border-radius: 10px;
}

.progress .green {
  background: #85c440;
  border-color: #78b337 #6ba031 #568128;
  background-image: -o-linear-gradient(top, #b7dc8e 0%, #99ce5f 70%, #85c440 100%);
  background-image: linear-gradient(to bottom, #b7dc8e 0%, #99ce5f 70%, #85c440 100%);
  background-image: -webkit-linear-gradient(top, #b7dc8e 0%, #99ce5f 70%, #85c440 100%);
  background-image: -moz-linear-gradient(top, #b7dc8e 0%, #99ce5f 70%, #85c440 100%);
}

.progress .red {
  background: #db3a27;
  border-color: #c73321 #b12d1e #8e2418;
  background-image: -o-linear-gradient(top, #ea8a7e 0%, #e15a4a 70%, #db3a27 100%);
  background-image: linear-gradient(to bottom, #ea8a7e 0%, #e15a4a 70%, #db3a27 100%);
  background-image: -webkit-linear-gradient(top, #ea8a7e 0%, #e15a4a 70%, #db3a27 100%);
  background-image: -moz-linear-gradient(top, #ea8a7e 0%, #e15a4a 70%, #db3a27 100%);
}

.downloadtip{
	margin-top: 10px;
	color:#F00;
	text-align: center;
}

.downloadtip > span{
	color:#F00;
}

.df-textarea.invalid {
	border: 1px solid #fc5043 !important;
}

#descriptionAreaRegion.focus {
	border: 1px solid #4ac9ff !important;
}
.df-input-frame.focus .df-input.command {
	border: 1px solid #434343 !important;
	box-shadow: none !important;
}
.df-input.command {
	background: #434343;
	border: 1px solid #434343;
	color: #fff;
}
.wb-step-viewlog.command {
	background: #434343;
	padding: 10px 15px;
}
.wb-step-viewlog > textarea {
	background: #434343;
	color: #fff;
}
.wb-step-viewlog > textarea {
	background: #434343;
	color: #fff;
}


table .df-label-tips {
    position: relative !important;
    top: 2px !important;
    left: 8px !important;
    display: inline-block !important;
}
.df-label-tips .left.df-tips::after{
	left:-15px
}
.df-tips > p.spcial-warp {
	width: 400px;
	white-space: normal;
}
.df-tips.up:after {
	position: absolute;
	content:'';
	left: 7px;
	top: -16px;
	border: 8px solid transparent;
	border-bottom: 8px solid #333;
}
.df-tips.up {
	left: -8px;
	top: 24px;
}
.df-tips.up.right:after {
	right: 16px;
	left: inherit;
}
.df-tips.up.right {
	right: -17px;
	left: inherit;
}
.table-tips-overflow.ng-table td {
	overflow: inherit;
}

.upload-many-input-group{
	float: left;
}
.upload-many-input-group .label-left{
	color: #666;
	padding-right: 5px;
	float:left;
	line-height: 26px;
}
.upload-many-input-group label > i{
	color: #fc5043;
	font-style: normal;
	padding-right: 2px;
}
.upload-many-input{
	float: left;
}
.upload-icon{
	margin-left: 10px;
	float: left;
	margin-top: 3px;
}
.upload-icon.add{
	background: url("/dvfoundationwebsite/images/tool.png") 0 0 repeat-x;
	width: 15px;
	height: 15px;
	cursor: pointer;
}
.upload-icon.add.disabled{
	background: url("/dvfoundationwebsite/images/tool.png") -60px -16px repeat-x;
	cursor: inherit;
}
.upload-icon.del{
	background: url("/dvfoundationwebsite/images/tool.png") 0 -60px repeat-x;
	width: 15px;
	height: 15px;
	cursor: pointer;
}
.upload-checkbox{
	float: left;
	margin-bottom: 0px;
	margin-top: 3px;
	margin-left: 15px;
}
.icon-p-help{
	background: url("/dvfoundationwebsite/images/tool.png") -45px -75px repeat-x;
	width: 15px;
	height: 15px;
	cursor: pointer;
}

.df-input-frame > span.review.disabled {
	background: url(/dvihealingwebsite/images/healing/icon.png) -64px -32px no-repeat;
}
.select-dropup .df-select-menu {
    border-bottom: 1px solid #4ac9ff;
    border-radius: 0px 0px 4px 4px;
    -webkit-transform: rotateX(180deg);
	transform:rotateX(180deg);
	-ms-transform:rotateX(180deg);
	-moz-transform:rotateX(180deg);
	-o-transform:rotateX(180deg);
	bottom: 26px;
}
.select-dropup .df-select-menu li {
	 -webkit-transform: rotateX(180deg);
	transform:rotateX(180deg);
	-ms-transform:rotateX(180deg);
	-moz-transform:rotateX(180deg);
	-o-transform:rotateX(180deg);
}

.select-dropup .df-select-box.select input {
    border-radius: 0px 0px 4px 4px;
}

.result-host{
	max-height: 530px;
    min-height: 525px;
	overflow: auto;
}

/*执行详情新样式*/
.workbench-detail-basic {
	padding: 20px 0px;
}
.workbench-detail-basic td {
	padding-bottom: 14px;
}
.workbench-detail-basic td p {
	color: #666;
	padding-right: 6px;
}
.workbench-detail-basic td > span {
	display: inline-block;
    width: 800px;
    word-spacing: initial;
    word-break: break-all;
}
.workbench-detail-title {
	color: #333;
	font-size: 16px;
	padding-bottom: 20px;
}
.chapter-wrap-place {
	top: -47px !important;
}
.workbench-step-status {
	position: absolute;
	background: #42ccef;
	height: 52px;
    width: 92px;
    text-align: center;
}
.workbench-step-status:after {
	position: absolute;
    content: '';
    border-left: 16px solid #42ccef;
    border-top: 26px solid transparent;
    border-right: 0px solid transparent;
    border-bottom: 26px solid transparent;
    right: -16px;
    top: 0px;
}
.workbench-step-status.status-1 {
	background: #ffa235;
}
.workbench-step-status.status-1:after {
    border-left: 16px solid #ffa235;
}
.workbench-step-status.status-2 {
	background: #5ecc49;
}
.workbench-step-status.status-2:after {
    border-left: 16px solid #5ecc49;
}
.workbench-step-status.status-3 {
	background: #42ccef;
}
.workbench-step-status.status-3:after {
    border-left: 16px solid #42ccef;
}
.workbench-step-status.status-4 {
	background: #9592f0;
}
.workbench-step-status.status-4:after {
    border-left: 16px solid #9592f0;
}
.workbench-step-status > p {
	color: #fff;
	font-size: 14px;
	line-height: 52px;
}
.workbench-step-info {
	padding-left: 92px;
	border: 1px solid #ddd;
	border-left: 0;
}
.workbench-step-info table {
	table-layout: fixed;
}
.workbench-step-info tr th,
.workbench-step-info tr td {
	height: 25px;

}
.workbench-step-info tr th:first-child,
.workbench-step-info tr td:first-child {
	padding-left: 40px;
}
.workbench-step-info tr th {
	background: #ebebeb;
	border-bottom: 1px solid #ddd;
}
.side-page-title > span {
	padding: 0px 3px;
}
.workbench-step-left {
	float: left;
	width: 50%;
}
.workbench-step-right {
	float: right;
	width: 49%;
}
.workbench-step-topbar {
	min-height: 26px;
	padding: 20px 0px;
}
.workbench-step-topbar .status-swtich {
	float: right;
	margin: 0px;
}
.workbench-step-viewlog {
	border: 1px solid #d5d5d5;
	padding: 5px 15px;
	height: 400px;
	background: #434343;
}
.workbench-step-viewlog > pre {
	line-height: 24px;
	word-break: break-word;
}
.workbench-log-textarea
{
    width:100%;
    height:100%;
    border-style:none;
}
.workbench-step-viewlog.command {
	background: #434343;
	padding: 10px 15px;
}
.workbench-step-viewlog > textarea {
	background: #434343;
	color: #fff;
}
.workbench-step-viewlog > textarea {
	background: #434343;
	color: #fff;
}
.workbench-step.selected{
	border: 1px solid #00aaff;
}
.workbench-detail-step{
	max-height: 300px;
    overflow-y: auto;
}

.chapter.special > span {
    color: #dadada;
}
.chapter.special.success > span {
    color: #5ecc49;
}
.chapter.special.failed > span {
    color: #fc5043;
}
.chapter.special.success .chapter-node > span {
    background: #5ecc49;
}
.chapter.special .chapter-node > span {
    background: #dadada;
}
.chapter.special .chapter-wrap {
    border-left: 1px solid #dadada;
}
.chapter.special.success .chapter-wrap {
    border-left: 1px solid #5ecc49;
}
.chapter.special .wb-step-panelbar {
	background: transparent;
}

.df-btn > span.icon-upload {
    background: url(/dvihealingwebsite/images/healing/upload.png) 0px 0px no-repeat;
    width: 16px;
    padding-right: 2px;
}

.df-btn > span.icon-download {
    background: url(/dvihealingwebsite/images/healing/download.png) 0px 0px no-repeat;
    width: 16px;
    padding-right: 2px;
}

.df-icon-break {
	float: left;
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -80px -64px no-repeat;
	cursor: pointer;
	margin: 2px 10px 0px 0px;
}
.df-icon-break.disabled{
	background: url(/dvihealingwebsite/images/healing/icon.png) -80px -80px no-repeat;
    height: 16px;
    width: 16px;
}

.df-icon-nextaction {
	float: left;
	width: 16px;
	height: 16px;
	background: url(/dvihealingwebsite/images/healing/icon.png) -80px -32px no-repeat;
	cursor: pointer;
	margin: 2px 10px 0px 0px;
}
.df-icon-nextaction.disabled{
	background: url(/dvihealingwebsite/images/healing/icon.png) -81px -48px no-repeat;
    height: 16px;
    width: 16px;
}
.df-dialog-wrap input::-ms-clear, .df-dialog-wrap input::-ms-reveal{
	display: none;
}

table.workbench-info td p{
	margin: 10px 0px 0px 0px;
}

table .df-label-bar{
	background: url(/dvfoundationwebsite/images/tool.png) -45px -75px no-repeat;
	cursor: help;
}

.workbench-detail-step table td{
	text-overflow: ellipsis;
	overflow: hidden;
}

.validate-user .df-btn{
	display: none;
}

.submit-message{
	display: none;
	float: left;
}
.submit-message>p{
	padding: 3px 0 0 10px;
}
.submit-message>p.success{
	color: #5ECC49;
}

.submit-message>p.failed{
	color: #FC5043;
}

/*select app*/
.workbench-app table tbody tr.selected {
	background: #d2f1fd;
}

.select-radio {
	float: left;
	width: 16px;
	height: 16px;
	background: url(/dvfoundationwebsite/images/radio.png) 0px 0px no-repeat;
	cursor: pointer;
}
.select-radio.selected {
	background: url(/dvfoundationwebsite/images/radio.png) 0px -16px no-repeat;
}

.scroll-table-height{
	max-height: 500px;
	min-height: 490px;
	overflow-y: scroll;
}

.df-tab-item .select-solution-top{
	float:left;
	margin-left:10px;
	margin-top:10px;
	width: 100%;
}

@media (max-width: 1500px) {
	/*快速及新建任务表头*/
	.table-tips-overflow.ng-table thead th:nth-of-type(2){
		width: 200px;
	}
	.table-tips-overflow.ng-table thead th:nth-of-type(3){
		width: 210px;
	}
	.table-tips-overflow.ng-table thead th:nth-of-type(4){
		width: 240px;
	}

	/*任务列表页面*/
	.task-list-table > thead > tr > th:nth-of-type(1){
		width: 37px
	}
	.task-list-table > thead > tr > th:nth-of-type(2){
		width: 80px
	}
	.task-list-table > thead > tr > th:nth-of-type(3){
		width: 71px
	}
	.task-list-table > thead > tr > th:nth-of-type(4){
		width: 80px
	}
	.task-list-table > thead > tr > th:nth-of-type(5){
		width: 109px
	}
	.task-list-table > thead > tr > th:nth-of-type(6){
		width: 78px
	}
	.task-list-table > thead > tr > th:nth-of-type(8){
		width: 148px
	}
	.task-list-table > thead > tr > th:nth-of-type(9){
		width: 65px
	}
	.task-list-table > thead > tr > th:nth-of-type(10){
		width: 198px
	}

	.task-list-table tbody tr td:nth-of-type(1){
		padding-left: 0px;
	}
	/*任务列表*/
	.history-list-table > thead > tr > th:nth-of-type(5){
		width: 142px;
	}
	.history-list-table > thead > tr > th:nth-of-type(6){
		width: 142px;
	}
	.history-list-table > thead > tr > th:nth-of-type(10){
		width: 70px;
	}
}

@media (max-width: 1700px) {
	.workbench-listcolumn > table > tbody > tr > td > p.path-exceed{
		width: 700px;
	}
		/*详情重试*/
	.wb-step-left .df-btn.df-btnicon {
		padding: 0px 6px;
	}
	.retryaction-menu.group-menu{
		left: -6px;
	}

	.wb-step-left .status-swtich li {
		padding: 0px 8px;
	}

	.wb-step-left .df-input {
		width: 160px !important;
	}

	/*上传下载的路径参数输入框*/
	.upload-many-input-group .label-left {
		width: 148px;
	}

	.workbench-listcolumn > table > tbody > tr > td .upload-many-input .df-input {
		width: 260px !important;
	}

	.workbench-listcolumn > table > tbody > tr > td .validate-user .df-input {
		width: 50px !important;
	}

	.workbench-listcolumn > table > tbody > tr > td .validate-user .df-input {
		width: 50px !important;
	}

	.task-list-table > thead > tr > th:nth-of-type(3){
		width: 100px
	}

	.task-list-table > thead > tr > th:nth-of-type(5){
		width: 115px
	}

	.task-list-table > thead > tr > th:nth-of-type(7){
		width: 135px
	}

}
@media (max-width: 1300px ){
	.task-list-table > thead > tr > th:nth-of-type(10){
		width: 170px
	}
	.task-list-table .operate-tool li{
		width: 20px
	}
}
@media (max-width: 1280px) {
	/*左侧菜单栏*/
	#fullNav > ul{
		height:100% !important
	}

}

.btn-span-text {
	top: 1px !important;
	display: initial !important;
	line-height: 15px !important;
	width: auto !important;
}

.btn-span-icon {
	top: 4px !important;
}


.ui-icon{
    background-image: url(/dvihealingwebsite/images/healing/ui-icons_444444_256x240.png) !important;
}
.ui-resizable-se{
    bottom: 30px !important;
    right: 10px !important;
}

.cron-title{
    margin-top:25px;
    width: 100%;
    height: 30px;
    text-align: center;
    color:#0af;
    font-size: 15px;
    font-weight: bold;
}

.cron-tips-broder{
    width:758px;
    height:390px;
    border:1px solid #f3f3f3;
    text-align: center;
    line-height: 390px;
}

.cron-fields{
    width: 90%;
    height: 30px;
    margin-top: 175px;
    margin-left: 37px;
    text-align: center;
}

.cron-fields-border{
    border:1px solid #bbb;
}

.cron-field-background{
    background: #ffffff;
}

.cron-field-width{
    width:90px;
    text-align:center;
    font-size:14;
}

.fields-info {
    margin-top: 15px;
    margin-left: 34px;
    position:relative;
    width:140px;
    height:150px;
    line-height:60px;
    border:1px solid #bbb;
    border-radius:4px;
    text-align:left;
    color:red;
    background: #fff;
}
.fields-nav {
    position:absolute;
    left:30px;
    overflow:hidden;
    width:0;
    height:0;
    border-width:10px;
    border-style:solid dashed dashed dashed;
}
.fields-nav-border {
    top:-20px;
    border-color:transparent transparent #bbb transparent;
}
.fields-nav-background {
    top:-19px;
    border-color:transparent transparent #ffffff transparent;
}

.fields-info-tips{
    margin-left: 5px;
    width:135px;
    height:150px;
}

.fields-tips-lineheight{
    line-height: 2;
    transform: translate(0, -50%);
    top: 50%;
    position: absolute;
    font-size:14;
    color:#333;
}

.df-tips.w200-workbench.new-bottom-workbench {
	left: -420px;
	width: 600px;
	bottom: 10px;
}
.df-tips.w200-workbench.new-bottom-workbench:after {
	left: 220px
}

.errorinfo.logs{
    width: 16px;
    height: 16px;
    background: url(/dvihealingwebsite/images/healing/icon.png) -48px -32px;
    cursor: pointer;
    margin-right: 10px;
}

.errorinfo.unlogs{
    width: 16px;
    height: 16px;
    background: url(/dvihealingwebsite/images/healing/icon.png) -64px -32px;
    margin-right: 10px;
}

.df-pagination.disabled{
    border:none
}
.df-pagination.disabled.hover{
    cursor:initial
}
.df-pagination.disabled>a{
    cursor:initial
}