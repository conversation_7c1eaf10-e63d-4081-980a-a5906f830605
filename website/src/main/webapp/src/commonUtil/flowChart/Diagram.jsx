/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
import React, { useEffect, useImperativeHandle, useRef } from 'react';
import { $t, pages } from '@util';
import { DIAGRAM_OPERATE_TYPES, TEMPLATE_NODE_PAGE_TYPE, TEMPLATE_TYPE } from '@const/common';
import './css/flowChart.css';
import './css/diagram.css';
import './flowDiagram';
import dialogUtils from './dialogUtils';
import { TASK_TYPE_MAP } from '@pages/taskManagement/const';

let diagramType;
let $UI = {};
const Diagram = (props) => {
  const { viewMode, cRef, popNewOrModifyNode, taskType, isRefresh, setTemplateData } = props;
  let rootId = 'flow-diagram';
  let nodeAttrs = null;
  let pathAttrs = null;
  let updateLocked;
  let m_elementNames = ['nodes', 'paths'];
  let m_nodeAttrNames = [
    ['key', 'id'], ['nodeface', 'title'], ['figures', 'figures'], ['jsonfigures', 'figures'],
    ['type', 'type'], ['logo', 'logo'], ['logobounds', 'logoBounds'], ['group', 'group'], ['parentkey', 'parent'],
    ['childrenkeys', 'children'], ['onpath', 'onpath'], ['activeMenuId', 'activeMenuId'], ['data', 'data'],
    ['jsonstrdata', 'jsonstrdata'], ['metatype', 'metatype'], ['anchornodes', 'anchornodes']];
  let m_pathAttrNames = [
    ['key', 'id'], ['sourcekey', 'source'], ['targetkey', 'target'], ['sourceanchor', 'sourceAnchor'],
    ['targetanchor', 'targetAnchor'], ['figures', 'figures'], ['jsonfigures', 'figures'], ['textareas', 'textareas'],
    ['sourcestyle', 'sourceStyle'], ['targetstyle', 'targetStyle'], ['routelist', 'routeList'],
    ['controls', 'controls'], ['dasharray', 'dashArray'], ['data', 'data'], ['jsonstrdata', 'jsonstrdata'],
    ['metatype', 'metatype']];
  let elementsModel;
  let nodesModel;
  let pathsModel;
  const bpmidediagram = useRef(props.bpmidediagram);
  $UI.diagrams = $UI.diagrams || {};
  $UI.diagram = function(id) {
    return $UI.diagrams[id];
  };

  for (let i in jBME.Diagram) {
    if (Object.prototype.hasOwnProperty.call(jBME.Diagram, i)) {
      $UI.diagram[i] = jBME.Diagram[i];
    }
  }

  // 删除按钮
  const remove = (jElement, jDiagram) => {
    if (jDiagram.viewmode !== DIAGRAM_OPERATE_TYPES.VIEW) {
      jDiagram.selections[jElement.id] = jElement;
      jDiagram.selectionSize = 1;
      jDiagram.removeSelections();
    }
  };

  const getTitle = (type, isModify) => {
    switch (type) {
      case TEMPLATE_TYPE.ALARM:
        return isModify ? $t('associated.alarm.modify.node') : $t('associated.alarm.view.node');
      case TEMPLATE_TYPE.LOG:
        return isModify ? $t('associated.log.modify.node') : $t('associated.log.view.node');
      case TEMPLATE_TYPE.START:
        return isModify ? $t('task.capacity.modify.main.kpi.title') : $t('task.capacity.view.main.kpi.title');
      case TEMPLATE_TYPE.TWO_START:
        return isModify ? $t('task.capacity.modify.two.main.kpi.title') : $t('task.capacity.view.two.main.kpi.title');
      case TEMPLATE_TYPE.CAPACITY:
        return isModify ? $t('task.capacity.modify.resource.tree.kpi.title') : $t('task.capacity.view.resource.tree.kpi.title');
      case TEMPLATE_TYPE.CAPACITY_TWO:
        return isModify ? $t('task.capacity.modify.two.common.kpi.title') : $t('task.capacity.view.two.common.kpi.title');
      case TEMPLATE_TYPE.PLAN:
        return isModify ? $t('associated.plan.modify.node') : $t('associated.plan.view.node');
      default:
        return isModify ? $t('task.associate.template.modify.kpi') : $t('task.associate.template.view.kpi');
    }
  };

  // 编辑按钮
  const editPop = (jElement, jDiagram) => {
    let title = getTitle(jElement.options.type, true);
    popNewOrModifyNode(jElement.options.type, TEMPLATE_NODE_PAGE_TYPE.MODIFY, title, jElement, jDiagram);
  };

  // 查看按钮
  const viewPop = (jElement, jDiagram) => {
    let title = getTitle(jElement.options.type, false);
    popNewOrModifyNode(jElement.options.type, TEMPLATE_NODE_PAGE_TYPE.VIEW, title, jElement, jDiagram);
  };

  useEffect(() => {
    diagramType = taskType;
  }, [taskType]);

  useEffect(() => {
    startDiagram();

    $(`#${rootId}`).on(jBME.Diagram.EVENT.notifychange, (event) => {
      if (!updateLocked) {
        let value;
        let actionArray = ['nodecreate', 'nodedelete', 'titlechange', 'pathcreate', 'pathdelete', 'pathchange', 'pathreconnect'];
        if (actionArray.includes(event.action)) {
          value = event.jElement.getJSONData();
        } else {
          value = {};
          value.nodes = {};
          value.paths = {};
          for (let id in event.jElement) {
            if (Object.prototype.hasOwnProperty.call(event.jElement, id)) {
              if (event.jElement[id].options.removable == false) {
                return;
              }
              if (event.jElement[id].isnode) {
                value.nodes[id] = event.jElement[id].getJSONData();
              } else if (event.jElement[id].ispath) {
                value.paths[id] = event.jElement[id].getJSONData();
              }
            }
          }
        }

        updateModel(event.action, value);
      }
    });

    return () => {
      destroy();
    };
  }, []);

  // 非首次进入，需要刷新流程图模型
  useEffect(() => {
    bpmidediagram.current = props.bpmidediagram;
    if (isRefresh) {
      removeOldElements();
      let WAIT_TIME = 50;
      setTimeout(() => {
        $UI.diagrams[rootId].update(bpmidediagram.current);
        setTemplateData({ isRefresh: false, isLoadingShow: false });
      }, WAIT_TIME);
    }
  }, [props.bpmidediagram]);

  function parseModelFromJson(m_AttrNames, elementAttrs, json) {
    if (!elementAttrs) {
      return json;
    }

    let elementModel = {};
    for (let j = 0; j < m_AttrNames.length; j++) {
      if (elementAttrs[m_AttrNames[j][0]]) {
        elementModel[elementAttrs[m_AttrNames[j][0]]] = (
          m_AttrNames[j][0] === 'figures' || m_AttrNames[j][0] === 'jsonstrdata' ||
          m_AttrNames[j][0] === 'logobounds' || m_AttrNames[j][0] === 'onpath'
        ) ? JSON.stringify(json[m_AttrNames[j][1]]) : json[m_AttrNames[j][1]];
      } else if (json[m_AttrNames[j][1]]) {
        elementModel[m_AttrNames[j][1]] = (
          (m_AttrNames[j][1] === 'figures' && m_AttrNames[j][0] !== 'jsonfigures') ||
          m_AttrNames[j][1] === 'jsonstrdata' || m_AttrNames[j][1] === 'logoBounds' || m_AttrNames[j][1] === 'onpath'
        ) ? JSON.stringify(json[m_AttrNames[j][1]]) : json[m_AttrNames[j][1]];
      }
    }

    return elementModel;
  }

  function removeOldElements() {
    updateLocked = true;
    let diagram = $UI.diagrams[rootId];
    if (diagram.nodes) {
      for (let i in diagram.nodes) {
        if (Object.prototype.hasOwnProperty.call(diagram.nodes, i)) {
          diagram.removeById(diagram.nodes[i].id);
        }
      }
    }
    if (diagram.paths) {
      for (let i in diagram.paths) {
        if (Object.prototype.hasOwnProperty.call(diagram.paths, i)) {
          diagram.removeById(diagram.paths[i].id);
        }
      }
    }
    updateLocked = false;
  }

  // 删除节点
  function deletePathOrNodeModel(id, elementsModel, m_attrNames) {
    for (let i = 0; i < elementsModel.length; i++) {
      if (id == elementsModel[i][m_attrNames[0][1]]) {
        elementsModel.splice(i, 1);
        break;
      }
    }
  }

  // 创建节点或路径
  function createNodeOrPath(index, m_AttrNames, attrs, json) {
    elementsModel = bpmidediagram.current[m_elementNames[index]];
    if (elementsModel) {
      let elementModel = parseModelFromJson(m_AttrNames, attrs, json);
      elementsModel.push(elementModel);
    }
  }

  // 删除节点或路径
  function deleteNodeOrPath(index, m_AttrNames, json) {
    elementsModel = bpmidediagram.current[m_elementNames[index]];
    if (elementsModel) {
      let id = json[m_AttrNames[0][1]];
      deletePathOrNodeModel(id, elementsModel, m_AttrNames);
    }
  }

  function allNodeOrPath(isNode, json, key, m_AttrNames, attrs) {
    let model = bpmidediagram.current[m_elementNames[isNode ? 0 : 1]];
    if (model) {
      model.splice(0, model.length);
      if (json[key]) {
        let modelRow;
        for (let i = 0; i < json[key].length; i++) {
          modelRow = parseModelFromJson(m_AttrNames, attrs, json.nodes[i]);
          model.push(modelRow);
        }
      }
    }

    if (isNode) {
      nodesModel = model;
    } else {
      pathsModel = model;
    }
  }

  // 复制节点或路径
  function pasteNodeOrPath(isNode, json, key, m_AttrNames, attrs) {
    let model = bpmidediagram.current[m_elementNames[isNode ? 0 : 1]];
    if (model && json[key]) {
      for (let id in json[key]) {
        if (Object.prototype.hasOwnProperty.call(json[key], id)) {
          let elementModel = parseModelFromJson(m_AttrNames, attrs, json[key][id]);
          model.push(elementModel);
        }
      }
    }
    if (isNode) {
      nodesModel = model;
    } else {
      pathsModel = model;
    }
  }

  // 标题变化
  function titleChange(json) {
    elementsModel = bpmidediagram.current[m_elementNames[0]];
    if (elementsModel) {
      let id = json[m_nodeAttrNames[0][1]];
      let title = json[m_nodeAttrNames[1][1]];
      let idKey = nodeAttrs ? nodeAttrs[m_nodeAttrNames[0][0]] : m_nodeAttrNames[0][1];
      let titleKey = nodeAttrs ? nodeAttrs[m_nodeAttrNames[1][0]] : m_nodeAttrNames[1][1];
      for (let i = 0; i < elementsModel.length; i++) {
        if (id === elementsModel[i][idKey]) {
          elementsModel[i][titleKey] = title;
          break;
        }
      }
    }
  }

  // path上其它条件变化
  function pathConditionChange(json) {
    elementsModel = bpmidediagram.current[m_elementNames[0]];
    if (elementsModel) {
      let id = json[m_nodeAttrNames[0][1]];
      let idKey = nodeAttrs ? nodeAttrs[m_nodeAttrNames[0][0]] : m_nodeAttrNames[0][1];

      for (let i = 0; i < elementsModel.length; i++) {
        if (id === elementsModel[i][idKey]) {
          elementsModel[i].logNodeDNFilter = json.logNodeDNFilter ?? { isShow: false, value: false };
          break;
        }
      }
    }
  }

  // 路径连接
  function pathConnect(json) {
    elementsModel = bpmidediagram.current[m_elementNames[1]];
    if (elementsModel) {
      let id = json[m_pathAttrNames[0][1]];
      let elementModel = parseModelFromJson(m_pathAttrNames, pathAttrs, json);
      let idKey = pathAttrs ? pathAttrs[m_pathAttrNames[0][0]] : m_pathAttrNames[0][1];
      for (let i = 0; i < elementsModel.length; i++) {
        if (id == elementsModel[i][idKey]) {
          elementsModel[i] = elementModel;
          break;
        }
      }
    }
  }

  // 节点变化
  function nodeChange(json) {
    nodesModel = bpmidediagram.current[m_elementNames[0]];
    if (nodesModel && json.nodes) {
      let idkey = nodeAttrs ? nodeAttrs[m_nodeAttrNames[0][0]] : m_nodeAttrNames[0][1];
      for (let id in json.nodes) {
        if (Object.prototype.hasOwnProperty.call(json.nodes, id)) {
          let elementModel = parseModelFromJson(m_nodeAttrNames, nodeAttrs, json.nodes[id]);
          for (let i = 0; i < nodesModel.length; i++) {
            if (id == nodesModel[i][idkey]) {
              nodesModel[i] = elementModel;
              break;
            }
          }
        }
      }
    }
  }

  function selectionsDelete(json) {
    nodesModel = bpmidediagram.current[m_elementNames[0]];
    if (nodesModel) {
      for (let id in json.nodes) {
        if (Object.prototype.hasOwnProperty.call(json.nodes, id)) {
          deletePathOrNodeModel(id, nodesModel, m_nodeAttrNames);
        }
      }
    }

    pathsModel = bpmidediagram.current[m_elementNames[1]];
    if (pathsModel) {
      for (let id in json.paths) {
        if (Object.prototype.hasOwnProperty.call(json.paths, id)) {
          deletePathOrNodeModel(id, pathsModel, m_pathAttrNames);
        }
      }
    }
  }

  // 更新流程图
  function updateModel(action, json) {
    switch (action) {
      case 'nodecreate':
        createNodeOrPath(0, m_nodeAttrNames, nodeAttrs, json);
        break;
      case 'pathcreate':
        createNodeOrPath(1, m_pathAttrNames, pathAttrs, json);
        break;
      case 'pathchange':
      case 'pathreconnect':
        pathConnect(json);
        break;
      case 'titlechange':
        titleChange(json);
        pathConditionChange(json);
        break;
      case 'nodeschange':
        nodeChange(json);
        break;
      case 'paste':
        pasteNodeOrPath(true, json, 'nodes', m_nodeAttrNames, nodeAttrs);
        pasteNodeOrPath(false, json, 'paths', m_pathAttrNames, pathAttrs);
        break;
      default:
        deleteModel(action, json);
        break;
    }
  }

  function deleteModel(action, json) {
    switch (action) {
      case 'nodedelete':
        deleteNodeOrPath(0, m_nodeAttrNames);
        break;
      case 'pathdelete':
        deleteNodeOrPath(1, m_pathAttrNames);
        break;
      case 'selectionsdelete':
        selectionsDelete(json);
        break;
      case 'all':
      default:
        allNodeOrPath(true, json, 'nodes', m_nodeAttrNames, nodeAttrs);
        allNodeOrPath(false, json, 'paths', m_pathAttrNames, pathAttrs);
        break;
    }
  }

  function startDiagram() {
    let options = {
      options: '',
      isCopy: diagramType === TASK_TYPE_MAP.CAPACITY_BOTTLENECK_ANALYSIS,
    };

    if (viewMode === DIAGRAM_OPERATE_TYPES.VIEW || viewMode === DIAGRAM_OPERATE_TYPES.EDIT) {
      options.viewmode = viewMode;
    }

    destroy();
    $UI.diagrams[rootId] = new jBME.Diagram(rootId, options);

    let editor = new jBME.Editor($UI.diagrams[rootId]);
    $UI.diagrams[rootId].editor = editor;
    editor.startup();
  }

  // 清理内存防止泄漏
  const destroy = () => {
    if ($UI.diagrams[rootId]) {
      $UI.diagrams[rootId].destroy();
      $UI.diagrams[rootId] = null;
    }
  };

  // 创建节点
  const createDiagramNode = (style) => {
    $UI.diagram(rootId).requestNode(null, style);
  };

  // 创建链路
  const createDiagramPath = (pathOption) => {
    $UI.diagram(rootId).requestPath(null, pathOption);
  };

  if (jBME.Node) {
    new jBME.Node.Type('call_activity',
      {
        figure(context, figure) {
          jBME.Node.Type.roundrect.figure.call(this, context, figure);
        },
        anchors: jBME.Node.Type.roundrect.anchors,
      });
  }

  // 注册下拉框选择插件
  jBME.Diagram.selectConditionPlugin = jBME.AbstractPlugin.extend({
    name: 'selectcondition',
    eventHandlers: [{ event: jBME.Diagram.EVENT.connectend, handler: 'select', targetselector: 'all' },
    ],
    select(e, jElement, jDiagram) {
      let path = e.jElement;
      let source = path.jSource;
      // 如果源是start类的节点
      if (source && (source.options.type === TEMPLATE_TYPE.START || source.options.type === TEMPLATE_TYPE.TWO_START)) {
        return;
      }
      this.showSelect(e, path, jDiagram);
    },
    showSelect(e, jPath, jDiagram) {
      let $box = jPath.$box;

      // 判断是否是关联分析任务管理页面
      let isAssociation = pages.getDefaultCompKey() === 'taskManage';
      if (!$('.conditionDialog').length && isAssociation) {
        const param = {
          title: jPath.options.title,
          logNodeDNFilter: jPath.options.logNodeDNFilter,
        };
        dialogUtils.conditionDialog($box, param, jPath, jDiagram, (content, logNodeDNFilter, jEvent) => {
          if (jDiagram.viewmode === DIAGRAM_OPERATE_TYPES.VIEW) {
            return;
          }
          // 双击链路：先删除线上的内容
          let textNodes = jPath.textnodes;
          if (!textNodes) {// 双击的文本图元
            jDiagram.resizeElement.toggleTextEdit(false, jDiagram, content, logNodeDNFilter);
            jDiagram.triggerElementOperation(jDiagram.resizeElement, 'titlechange');
          } else {
            for (let textId in textNodes) {
              if (Object.prototype.hasOwnProperty.call(textNodes, textId)) {
                jDiagram.removeById(textId);
              }
            }

            // 将选择的东东展示到连线上
            jDiagram.jHoverElement = jPath;
            jDiagram.defaultConnectionText = content;
            jDiagram.logNodeDNFilter = logNodeDNFilter;
            jDiagram.toggleTextEdit(undefined, jEvent, jPath);
          }
        });
      }
    },
    deleteSelect() {
      if (this.$selectionItem) {
        this.$selectionItem.remove();
        this.$selectionItem = undefined;
      }
    },
  });
  jBME.Editor.register(jBME.Diagram.selectConditionPlugin);

  // 暴露给父组件的方法
  useImperativeHandle(cRef, () => ({
    getDiagramData: () => {
      return bpmidediagram.current;
    },
    createNode: (kpiNode) => {
      return $UI.diagram(rootId).requestNode(null, kpiNode);
    },
    modifyNode: (nodeId, node, jElement, jDiagram) => {
      jDiagram.nodes[nodeId].options.nodeParam = node.nodeParam;
      jDiagram.nodes[nodeId].options.figures = node.figures;
      jDiagram.toggleTextEdit(true, null, jElement, node.nodeParam.nodeName);
      jElement.toggleHover(false, null, false);
    },
    remove,
    editPop,
    viewPop,
  }));

  return (
    <div className='flow-container'>
      <div className={viewMode === DIAGRAM_OPERATE_TYPES.EDIT ?
        'flow-item-container' : 'flow-item-container disabled'}
      >
        {taskType === TASK_TYPE_MAP.CAPACITY_BOTTLENECK_ANALYSIS ?
          <>
            <div className={`${document.cookie.indexOf('zh-cn') !== -1 ? 'flow-item' : 'flow-item flow-item-en'}`}
              onClick={() => popNewOrModifyNode(TEMPLATE_TYPE.START, TEMPLATE_NODE_PAGE_TYPE.NEW,
                $t('task.capacity.template.main.node.new'))}
            >
              <div style={{ paddingBottom: '2px' }}>
                <div className='start-node' />
              </div>
              <span>{$t('task.capacity.template.main.node')}</span>
            </div>
            <div className={`${document.cookie.indexOf('zh-cn') !== -1 ? 'flow-item' : 'flow-item flow-item-en'}`}
              onClick={() => popNewOrModifyNode(TEMPLATE_TYPE.TWO_START, TEMPLATE_NODE_PAGE_TYPE.NEW,
                $t('task.capacity.template.two.main.node.new'))}
            >
              <div style={{ paddingBottom: '2px' }}>
                <div className='twoStart-node' />
              </div>
              <span>{$t('task.capacity.template.two.main.node')}</span>
            </div>
            <div className={`${document.cookie.indexOf('zh-cn') !== -1 ? 'flow-item' : 'flow-item flow-item-en'}`}
              onClick={() => popNewOrModifyNode(TEMPLATE_TYPE.KPI, TEMPLATE_NODE_PAGE_TYPE.NEW,
                $t('task.capacity.common.node.new'))}
            >
              <div style={{ paddingBottom: '2px' }}>
                <div className='call' />
              </div>
              <span>{$t('task.capacity.common.node')}</span>
            </div>
            <div className={`${document.cookie.indexOf('zh-cn') !== -1 ? 'flow-item' : 'flow-item flow-item-en'}`}
              onClick={() => popNewOrModifyNode(TEMPLATE_TYPE.CAPACITY_TWO, TEMPLATE_NODE_PAGE_TYPE.NEW,
                $t('task.capacity.common.two.node.new'))}
            >
              <div style={{ paddingBottom: '2px' }}>
                <div className='purple-node' />
              </div>
              <span>{$t('task.capacity.common.two.node')}</span>
            </div>
            <div className={`${document.cookie.indexOf('zh-cn') !== -1 ? 'flow-item' : 'flow-item flow-item-en'}`}
              onClick={() => popNewOrModifyNode(TEMPLATE_TYPE.CAPACITY, TEMPLATE_NODE_PAGE_TYPE.NEW,
                $t('task.capacity.resource.tree.kpi.new'))}
            >
              <div style={{ paddingBottom: '2px' }}>
                <div className='capacity-node' />
              </div>
              <span>{$t('task.capacity.resource.tree.kpi')}</span>
            </div>
          </> :
          <>
            <div className='flow-item'
              onMouseDown={() => createDiagramNode('start')}
            >
              <div style={{ paddingBottom: '2px' }}>
                <div className='start-node' />
              </div>
              <span>{$t('task.associate.template.begin')}</span>
            </div>
            <div className='flow-item'
              onClick={() => popNewOrModifyNode(TEMPLATE_TYPE.KPI, TEMPLATE_NODE_PAGE_TYPE.NEW,
                $t('task.template.create.indicator.node'))}
            >
              <div style={{ paddingBottom: '2px' }}>
                <div className='call' />
              </div>
              <span>{$t('task.associate.template.kpi')}</span>
            </div>
            <div className='flow-item'
              onClick={() => popNewOrModifyNode(TEMPLATE_TYPE.ALARM, TEMPLATE_NODE_PAGE_TYPE.NEW,
                $t('associated.alarm.new.node'))}
            >
              <div style={{ paddingBottom: '2px' }}>
                <div className='capacity-node' />
              </div>
              <span>{$t('associated.alarm.icon')}</span>
            </div>
            <div className='flow-item'
              onClick={() => popNewOrModifyNode(TEMPLATE_TYPE.LOG, TEMPLATE_NODE_PAGE_TYPE.NEW,
                $t('associated.log.dialog.title'))}
            >
              <div style={{ paddingBottom: '2px' }}>
                <div className='purple-node' />
              </div>
              <span>{$t('associated.log')}</span>
            </div>
            <div className='flow-item'
              onClick={() => popNewOrModifyNode(TEMPLATE_TYPE.PLAN, TEMPLATE_NODE_PAGE_TYPE.NEW,
                $t('associated.plan.new.node'))}
            >
              <div style={{ paddingBottom: '2px' }}>
                <div className='plan-node' />
              </div>
              <span>{$t('associated.plan')}</span>
            </div>
          </>}
        <div className='flow-item' style={{ marginRight: '0' }}
          onMouseDown={() => createDiagramPath({
            figures: [{ type: 'line', context: { strokeStyle: '#00aaff' } }],
          })}
        >
          <div style={{ paddingBottom: '2px' }}>
            <div className='line' />
          </div>
          <span>{$t('task.associate.template.link')}</span>
        </div>
      </div>
      <div className='uee-diagram' tabIndex='0' id={rootId}>
        <div className='uee-diagram-client bf_cursor'>
          <div className='uee-overflow-indicator' />
        </div>
        <div id='_mask' className='uee-diagram-mask bf_cursor' />
        <div className='uee-diagram-marquee' />
      </div>
    </div>
  );
};

export default Diagram;
