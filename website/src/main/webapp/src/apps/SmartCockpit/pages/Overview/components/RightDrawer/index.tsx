import React, { useEffect, useState, useMemo, useRef } from 'react';
import { Drawer, Tab, TablePro, ConfigProvider, Button, MultipleSelect, TextField } from 'eview-ui';
import { formatDate2SysSetting } from '@digitalview/fe-utils';
import { $t } from '@util/intl';
import type { I_CountIncident } from '@apis/countIncident';
import { E_Priority, E_IncidentCategory, E_DiagnosticType, E_DiagnosticStatus, queryIncidentList } from '@apis/incident';
import { DEFAULT_PAGINATION, DIAGNOSTIC_STATUS_INTL, sortFieldMap, sortTypeMap } from '@src/apps/Incident/pages/IncidentListPage/consts';
import { getTableColumns } from '@src/apps/Incident/pages/IncidentListPage/components/TableColumns';
import { AUTO_DIAGNOSE_STATUS_MAP, MANUAL_DIAGNOSE_STATUS_MAP } from '@src/apps/SmartCockpit/consts';
import styles from '@src/apps/SmartCockpit/style.module.less';
import { filterConditionIcon } from '@assets/const';
import type { I_Incident } from '@apis/incident';
import { getGlobalState } from '@util/globalState';
import TableEmptyNode from './TableEmptyNode';

/**
 * 智能驾舱-首页-右侧事件列表
 */
interface I_RightDrawerProps {
  countIncident: I_CountIncident;
  selectedTabItem: number;
  selectedManualStatusIndex: number;
  selectedAutoStatusIndex: number;
  setSelectedTabItem: (number) => void;
  setSelectedManualStatusIndex: (number) => void;
  setSelectedAutoStatusIndex: (number) => void;
  showRightDrawer: boolean;
  setShowRightDrawer: (boolean) => void;
  queryTimeRange?: { startTime: number; endTime: number };
}

const RightDrawer: React.FC<I_RightDrawerProps> = props => {
  const {
    selectedTabItem, selectedManualStatusIndex, selectedAutoStatusIndex, setSelectedTabItem,
    setSelectedManualStatusIndex, setSelectedAutoStatusIndex, showRightDrawer, setShowRightDrawer,
    countIncident, queryTimeRange,
  } = props;

  const handleManualStatusClick = (index: number): any => {
    setSelectedManualStatusIndex(index);
  };
  const handleAutoStatusClick = (index: number): any => {
    setSelectedAutoStatusIndex(index);
  };

  const [manualFilterData, setManualFilterData] = useState({ // 用户选中的人工接管事件筛选排序分页数据 --用于控制接口查询入参
    diagnosticStatus: MANUAL_DIAGNOSE_STATUS_MAP[selectedManualStatusIndex] || null,
    name: null,
    categories: null,
    priorities: null,
    sourceObjName: null,
    sortField: null,
    sortType: null,
    pagination: DEFAULT_PAGINATION,
  });
  const [autoFilterData, setAutoFilterData] = useState({ // 用户选中的自动处置事件筛选排序分页数据 --用于控制接口查询入参
    diagnosticStatus: AUTO_DIAGNOSE_STATUS_MAP[selectedAutoStatusIndex] || null,
    name: null,
    categories: null,
    priorities: null,
    sourceObjName: null,
    sortField: null,
    sortType: null,
    pagination: DEFAULT_PAGINATION,
  });

  // 选中的人工状态索引改变时，改变人工Tab筛选数据
  useEffect(() => {
    const diagnosticStatus = MANUAL_DIAGNOSE_STATUS_MAP[selectedManualStatusIndex] || null;
    setManualFilterData(prev => {
      if (prev.diagnosticStatus === diagnosticStatus && prev.pagination.currentPage === 1) {
        // 避免多余的rerender
        return prev;
      }
      return {
        ...prev,
        diagnosticStatus,
        pagination: {
          ...prev.pagination,
          currentPage: 1,
        },
      };
    });
  }, [selectedManualStatusIndex]);

  // 选中的自动状态索引改变时，改变自动Tab筛选数据
  useEffect(() => {
    const diagnosticStatus = AUTO_DIAGNOSE_STATUS_MAP[selectedAutoStatusIndex] || null;
    setAutoFilterData(prev => {
      if (prev.diagnosticStatus === diagnosticStatus && prev.pagination.currentPage === 1) {
        // 避免多余的rerender
        return prev;
      }
      return {
        ...prev,
        diagnosticStatus,
        pagination: {
          ...prev.pagination,
          currentPage: 1,
        },
      };
    });
  }, [selectedAutoStatusIndex]);

  const [showFilterArea, setShowFilterArea] = useState(false); // 筛选区域是否显示

  const manualFilterValues = useRef<Record<string, any>>({}); // 临时存储筛选框中的值而不渲染
  const autoFilterValues = useRef<Record<string, any>>({});
  const [currentManualFilter, setCurrentManualFilter] = useState({ // 存储人工接管事件筛选框中的值 --用于控制筛选控件的值
    name: '',
    category: [],
    priority: [],
    sourceObjName: '',
  });
  const [currentAutoFilter, setCurrentAutoFilter] = useState({ // 存储自动处置事件筛选框中的值 --用于控制筛选控件的值
    name: '',
    category: [],
    priority: [],
    sourceObjName: '',
  });

  const [tableHeight, setTableHeight] = useState('calc(100vh - 25rem)'); // 展开过滤区域后，使表格高度减小
  const [incidentList, setIncidentList] = useState<{ isLoading: boolean; total: number; currentList: I_Incident[] }>({
    isLoading: true,
    total: 0,
    currentList: [],
  });

  const tableColumns = (useMemo(() => getTableColumns('RightDrawerTable'), [])).slice(1);

  const filteredColumns = useMemo(() => {
    // 显示在界面可以过滤的列
    const selectedKeys = ['name', 'sourceObjName', 'priority', 'category'];
    const columnMap = new Map(tableColumns.map(c => [c.key, c]));
    // 按照 selectedKeys 的顺序构建 filteredColumns
    return selectedKeys.map(key => columnMap.get(key));
  }, [tableColumns]);

  // 初始化、筛选、排序、切换页面时加载事件列表以及更新各状态事件数
  useEffect(() => {
    setIncidentList(prev => ({ ...prev, isLoading: true }));
    queryIncidentList({
      name: selectedTabItem === 0 ? manualFilterData.name : autoFilterData.name,
      categories: selectedTabItem === 0 ? manualFilterData.categories : autoFilterData.categories,
      priorities: selectedTabItem === 0 ? manualFilterData.priorities : autoFilterData.priorities,
      sourceObjName: selectedTabItem === 0 ? manualFilterData.sourceObjName : autoFilterData.sourceObjName,
      diagnosticType: selectedTabItem === 0 ? E_DiagnosticType.MANUAL : E_DiagnosticType.AUTO,
      diagnosticStatus: selectedTabItem === 0 ? manualFilterData.diagnosticStatus : autoFilterData.diagnosticStatus,
      paging: {
        pageSize: selectedTabItem === 0 ? manualFilterData.pagination.pageSize : autoFilterData.pagination.pageSize,
        pageNumber: selectedTabItem === 0 ? manualFilterData.pagination.currentPage : autoFilterData.pagination.currentPage,
        sortField: selectedTabItem === 0 ? manualFilterData.sortField : autoFilterData.sortField,
        sortType: selectedTabItem === 0 ? manualFilterData.sortType : autoFilterData.sortType,
      },
      startCreateTime: queryTimeRange ? queryTimeRange.startTime : undefined,
      endCreateTime: queryTimeRange ? queryTimeRange.endTime : undefined,
    })
      .then(data => {
        setIncidentList({ isLoading: false, total: data.total, currentList: data.objects });
      });
  }, [queryTimeRange, selectedTabItem, manualFilterData, autoFilterData]);

  const manualCircleColor = ['#F4840C', '#2070F3', '#36C18D', '#E7434A'];
  const manualStatus = [
    $t('dvAgent.smartCockpit.status.total'),
    DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.MANUAL_PENDING],
    DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.MANUAL_PROCESSING],
    DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.MANUAL_COMPLETED],
    DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.MANUAL_FAILED],
  ];
  const manualStatusCounts = [
    countIncident.manuallyProcessCnt,
    countIncident.pendingManualCnt,
    countIncident.manuallyProcessingCnt,
    countIncident.manualCompleted,
    countIncident.manualFailedCnt,
  ];

  const autoCircleColor = ['#2070F3', '#55CCD9', '#36C18D', '#E7434A'];
  const autoStatus = [
    $t('dvAgent.smartCockpit.status.total'),
    DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.AUTO_PENDING],
    DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.AUTO_PROCESSING],
    DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.AUTO_COMPLETED],
    DIAGNOSTIC_STATUS_INTL[E_DiagnosticStatus.AUTO_FAILED],
  ];
  const autoStatusCounts = [
    countIncident.autoProcessCnt,
    countIncident.pendingAutoCnt,
    countIncident.autoProcessingCnt,
    countIncident.autoCompletedCnt,
    countIncident.autoFailedCnt,
  ];

  const priorityFilterParam = {
    onChange: (value) => {
      if (selectedTabItem === 0) {
        manualFilterValues.current.priority = value;
      } else {
        autoFilterValues.current.priority = value;
      }
    },
    options: [
      { value: E_Priority.LOW, text: $t('dvAgent.incidentListPage.list.field.priority.low') },
      { value: E_Priority.MEDIUM, text: $t('dvAgent.incidentListPage.list.field.priority.medium') },
      { value: E_Priority.HIGH, text: $t('dvAgent.incidentListPage.list.field.priority.high') },
      { value: E_Priority.CRITICAL, text: $t('dvAgent.incidentListPage.list.field.priority.critical') },
    ],
    inputStyle: {
      width: '12rem',
      height: '2rem',
      maxWidth: 'unset',
    },
    value: selectedTabItem === 0 ? currentManualFilter.priority : currentAutoFilter.priority,
  };

  const categoryFilterParam = {
    onChange: (value) => {
      if (selectedTabItem === 0) {
        manualFilterValues.current.category = value;
      } else {
        autoFilterValues.current.category = value;
      }
    },
    options: [
      { value: E_IncidentCategory.ALARM, text: $t('dvAgent.incidentListPage.list.field.category.alarm') },
      { value: E_IncidentCategory.KPI, text: $t('dvAgent.incidentListPage.list.field.category.kpi') },
    ],
    inputStyle: {
      width: '12rem',
      height: '2rem',
      maxWidth: 'unset',
    },
    value: selectedTabItem === 0 ? currentManualFilter.category : currentAutoFilter.category,
  };

  let drawerTitle = $t('dvAgent.smartCockpit.title.panelTitle');
  let manualTabTitle = `${$t('dvAgent.smartCockpit.title.manualTakeoverEvents')} (${manualStatusCounts[0]})`;
  let autoTabTitle = `${$t('dvAgent.smartCockpit.title.autoDisposalEvents')} (${autoStatusCounts[0]})`;
  if (queryTimeRange) {
    drawerTitle = `${drawerTitle} (${formatDate2SysSetting(queryTimeRange.startTime)}~${formatDate2SysSetting(queryTimeRange.endTime)})`;
    manualTabTitle = $t('dvAgent.smartCockpit.title.manualTakeoverEvents');
    autoTabTitle = $t('dvAgent.smartCockpit.title.autoDisposalEvents');
  }

  return (
    // @ts-expect-error ConfigProvider组件接口声明问题，少了changeTheme，实际可用，这里指定changeTheme是为了避免影响body上的eview主题className
    <ConfigProvider version='aui3-1' theme='evening' changeTheme={false} locale={getGlobalState().language}>
      <Drawer
        visible={showRightDrawer}
        placement='right'
        onClose={() => setShowRightDrawer(false)}
        width='70%'
        height='100%'
        inContainer={true}
        footer={false}
        title={drawerTitle}
      >
        <Tab type='sub' draggable={false} selectedIndex={selectedTabItem} className={styles.tab}
          onClick={(index: number) => setSelectedTabItem(index)}
        >
          <Tab.TabItem title={manualTabTitle}>
            <div className={styles.stateCountsCellsContainer} style={{ display: queryTimeRange ? 'none' : 'flex' }}>
              <div className={styles.leftStateCountsCells}>
                <div className={styles.stateCountsCell}>
                  <span className={styles.stateText}>{manualStatus[0]}</span>
                  <span
                    onClick={() => handleManualStatusClick(0)}
                    className={selectedManualStatusIndex === 0 ? styles.selectedNumber : styles.underlinedNumber}
                  >{manualStatusCounts[0]}
                  </span>
                </div>
              </div>
              <div className={styles.rightStateCountsCells}>
                {manualStatusCounts.map((manualStatusCount, index) => {
                  if (index === 0) {
                    return null; // 当 index 为 0 时，直接返回 null，不渲染任何内容
                  }
                  return (
                    <div key={index} className={styles.stateCountsCell}>
                      <span className={styles.circle} style={{ backgroundColor: manualCircleColor[index - 1] }} />
                      <span className={styles.stateText}>{manualStatus[index]}</span>
                      <span
                        onClick={() => handleManualStatusClick(index)}
                        className={selectedManualStatusIndex === index ? styles.selectedNumber : styles.underlinedNumber}
                      >
                        {manualStatusCount}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
            <div className={styles.incidentListOperation}>
              <div className={styles.incidentListLeftFilter}>
                <Button
                  text={$t('dvAgent.table.filterCondition')}
                  status='default'
                  leftIcon={filterConditionIcon}
                  onClick={() => {
                    if (showFilterArea === true) {
                      setTableHeight('calc(100vh - 25rem)');
                    } else {
                      setTableHeight('calc(100vh - 35rem)');
                    }
                    setShowFilterArea(prev => !prev);
                  }}
                />
              </div>
              <div className={styles.incidentListRightBtns} style={{ display: showFilterArea ? 'flex' : 'none' }}>
                <Button
                  text={$t('dvAgent.table.reset')}
                  status='default'
                  onClick={() => {
                    manualFilterValues.current = {};
                    setCurrentManualFilter({
                      name: '',
                      category: [],
                      priority: [],
                      sourceObjName: '',
                    });
                  }}
                />
                <Button
                  text={$t('dvAgent.table.filter')}
                  status='primary'
                  onClick={() => {
                    setCurrentManualFilter({
                      name: manualFilterValues.current.name,
                      category: manualFilterValues.current.category,
                      priority: manualFilterValues.current.priority,
                      sourceObjName: manualFilterValues.current.sourceObjName,
                    });
                    setManualFilterData(prev => ({
                      ...prev,
                      name: manualFilterValues.current.name,
                      categories: manualFilterValues.current.category,
                      priorities: manualFilterValues.current.priority,
                      sourceObjName: manualFilterValues.current.sourceObjName,
                      pagination: {
                        ...prev.pagination,
                        currentPage: 1,
                      },
                    }));
                  }}
                />
              </div>
            </div>
            <div className={styles.incidentListFilterArea} style={{ display: showFilterArea ? 'flex' : 'none' }}>
              {
                filteredColumns.map((col, colIndex) => {
                  let component;
                  if (col.key === 'priority') {
                    component = (
                      <MultipleSelect {...priorityFilterParam} />
                    );
                  } else if (col.key === 'category') {
                    component = (
                      <MultipleSelect {...categoryFilterParam} />
                    );
                  } else {
                    component = (
                      <TextField
                        onChange={(value) => {
                          manualFilterValues.current[col.key] = value.trim();
                        }}
                        inputStyle={{
                          width: '12rem',
                          height: '2rem',
                          maxWidth: 'unset',
                        }}
                        value={currentManualFilter[col.key]}
                        useLatestValue={true}
                      />
                    );
                  }

                  return (
                    <div key={colIndex} className={styles.incidentListFilterItem}>
                      <span style={{ width: '4rem', textAlign: 'left' }}>{col.title}</span>
                      <span>{component}</span>
                    </div>
                  );
                })
              }
            </div>
            <div className={styles.rightDrawerTable}>
              <TablePro
                rowKey='csn'
                columns={tableColumns}
                dataset={incidentList.currentList}
                enableZebraCrossing={false}
                scroll={{ x: '100%', y: tableHeight }}
                emptyNode={<TableEmptyNode />}
                pagination={{
                  recordCount: incidentList.total,
                  pageSize: manualFilterData.pagination.pageSize,
                  currentPage: manualFilterData.pagination.currentPage,
                  onPageChange: (currentPage: number) => {
                    setManualFilterData(prev => ({
                      ...prev,
                      pagination: {
                        ...prev.pagination,
                        currentPage,
                      },
                    }));
                  },
                  onPageSizeChange: (pageSize: number) => {
                    setManualFilterData(prev => ({
                      ...prev,
                      pagination: {
                        pageSize,
                        currentPage: 1,
                      },
                    }));
                  },
                }}
                onChange={(value, event) => {
                  if (event.action === 'sort') {
                    const currentSortField = event.sort.field;
                    const currentSortOrder = event.sort.order;
                    // 获取映射后的排序字段
                    const mappedSortField = sortFieldMap[currentSortField];
                    // 获取映射后的排序类型
                    const mappedSortType = sortTypeMap[currentSortOrder];
                    setManualFilterData(prev => ({
                      ...prev,
                      sortField: mappedSortField,
                      sortType: mappedSortType,
                      pagination: {
                        ...prev.pagination,
                        currentPage: 1,
                      },
                    }));
                  }
                }}
              />
            </div>
          </Tab.TabItem>
          <Tab.TabItem title={autoTabTitle}>
            <div className={styles.stateCountsCellsContainer} style={{ display: queryTimeRange ? 'none' : 'flex' }}>
              <div className={styles.leftStateCountsCells}>
                <div className={styles.stateCountsCell}>
                  <span className={styles.stateText}>{autoStatus[0]}</span>
                  <span
                    onClick={() => handleAutoStatusClick(0)}
                    className={selectedAutoStatusIndex === 0 ? styles.selectedNumber : styles.underlinedNumber}
                  >{autoStatusCounts[0]}
                  </span>
                </div>
              </div>
              <div className={styles.rightStateCountsCells}>
                {autoStatusCounts.map((autoStatusCount, index) => {
                  if (index === 0) {
                    return null; // 当 index 为 0 时，直接返回 null，不渲染任何内容
                  }
                  return (
                    <div key={index} className={styles.stateCountsCell}>
                      <span className={styles.circle} style={{ backgroundColor: autoCircleColor[index - 1] }} />
                      <span className={styles.stateText}>{autoStatus[index]}</span>
                      <span
                        onClick={() => handleAutoStatusClick(index)}
                        className={selectedAutoStatusIndex === index ? styles.selectedNumber : styles.underlinedNumber}
                      >
                        {autoStatusCount}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
            <div className={styles.incidentListOperation}>
              <div className={styles.incidentListLeftFilter}>
                <Button
                  text={$t('dvAgent.table.filterCondition')}
                  status='default'
                  leftIcon={filterConditionIcon}
                  onClick={() => {
                    if (showFilterArea === true) {
                      setTableHeight('calc(100vh - 25rem)');
                    } else {
                      setTableHeight('calc(100vh - 35rem)');
                    }
                    setShowFilterArea(prev => !prev);
                  }}
                />
              </div>
              <div className={styles.incidentListRightBtns} style={{ display: showFilterArea ? 'flex' : 'none' }}>
                <Button
                  text={$t('dvAgent.table.reset')}
                  status='default'
                  onClick={() => {
                    autoFilterValues.current = {};
                    setCurrentAutoFilter({
                      name: '',
                      category: [],
                      priority: [],
                      sourceObjName: '',
                    });
                  }}
                />
                <Button
                  text={$t('dvAgent.table.filter')}
                  status='primary'
                  onClick={() => {
                    setCurrentAutoFilter({
                      name: autoFilterValues.current.name,
                      category: autoFilterValues.current.category,
                      priority: autoFilterValues.current.priority,
                      sourceObjName: autoFilterValues.current.sourceObjName,
                    });
                    setAutoFilterData(prev => ({
                      ...prev,
                      name: autoFilterValues.current.name,
                      categories: autoFilterValues.current.category,
                      priorities: autoFilterValues.current.priority,
                      sourceObjName: autoFilterValues.current.sourceObjName,
                      pagination: {
                        ...prev.pagination,
                        currentPage: 1,
                      },
                    }));
                  }}
                />
              </div>
            </div>
            <div className={styles.incidentListFilterArea} style={{ display: showFilterArea ? 'flex' : 'none' }}>
              {
                filteredColumns.map((col, colIndex) => {
                  let component;
                  if (col.key === 'priority') {
                    component = (
                      <MultipleSelect {...priorityFilterParam} />
                    );
                  } else if (col.key === 'category') {
                    component = (
                      <MultipleSelect {...categoryFilterParam} />
                    );
                  } else {
                    component = (
                      <TextField
                        onChange={(value) => {
                          autoFilterValues.current[col.key] = value.trim();
                        }}
                        inputStyle={{
                          width: '12rem',
                          height: '2rem',
                          maxWidth: 'unset',
                        }}
                        value={currentAutoFilter[col.key]}
                        useLatestValue={true}
                      />
                    );
                  }

                  return (
                    <div key={colIndex} className={styles.incidentListFilterItem}>
                      <span style={{ width: '4rem', textAlign: 'left' }}>{col.title}</span>
                      <span>{component}</span>
                    </div>
                  );
                })
              }
            </div>
            <div className='rightDrawerTableContainer'>
              <div className='rightDrawerTable'>
                <TablePro
                  rowKey='csn'
                  columns={tableColumns}
                  dataset={incidentList.currentList}
                  enableZebraCrossing={false}
                  scroll={{ x: '100%', y: tableHeight }}
                  emptyNode={<TableEmptyNode />}
                  pagination={{
                    recordCount: incidentList.total,
                    pageSize: autoFilterData.pagination.pageSize,
                    currentPage: autoFilterData.pagination.currentPage,
                    onPageChange: (currentPage: number) => {
                      setAutoFilterData(prev => ({
                        ...prev,
                        pagination: {
                          ...prev.pagination,
                          currentPage,
                        },
                      }));
                    },
                    onPageSizeChange: (pageSize: number) => {
                      setAutoFilterData(prev => ({
                        ...prev,
                        pagination: {
                          pageSize,
                          currentPage: 1,
                        },
                      }));
                    },
                  }}
                  onChange={(value, event) => {
                    if (event.action === 'sort') {
                      const currentSortField = event.sort.field;
                      const currentSortOrder = event.sort.order;
                      // 获取映射后的排序字段
                      const mappedSortField = sortFieldMap[currentSortField];
                      // 获取映射后的排序类型
                      const mappedSortType = sortTypeMap[currentSortOrder];
                      setAutoFilterData(prev => ({
                        ...prev,
                        sortField: mappedSortField,
                        sortType: mappedSortType,
                        pagination: {
                          ...prev.pagination,
                          currentPage: 1,
                        },
                      }));
                    }
                  }}
                />
              </div>
            </div>
          </Tab.TabItem>
        </Tab>
      </Drawer>
    </ConfigProvider>
  );
};

export default RightDrawer;
