import 'codemirror/lib/codemirror.css';

import AutoPlan<PERSON>pi from '@/Api/AutoPlanApi';
import DialogCustomHeader from '@/Components/DialogCustomHeader';
import emptyIcon from '@/NetGpt/Images/empty.svg';
import { StepContext } from '@/Pages/RecipeGenerate';
import { CheckResult, GenerateStepDo, StepType } from '@/Types/AutoPlanTypes';
import { Button, DialogPro, FormPro, TextField, TimeLine } from '@cloudsop/eview-ui';
import React, { useContext, useEffect, useState } from '@cloudsop/horizon';
import { useIntl } from '@cloudsop/horizon-intl';
import cloneDeep from 'lodash/cloneDeep';
import result from 'lodash/result';
import { Controlled as CodeMirror } from 'react-codemirror2';

import styles from './index.module.less';

const NodeGenerate = () => {
  const intl = useIntl();
  const { steps, setSteps } = useContext(StepContext);
  const form = FormPro.useForm();
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [nodeContent, setNodeContent] = useState<string>('');
  const [checkResult, setCheckResult] = useState<CheckResult>();

  const [passAllCheck, setPassAllCheck] = useState<boolean | undefined>();
  const [nodeCheckTimeLineParams, setNodeCheckTimeLineParams] = useState<any>();
  const [pathCheckTimeLineParams, setpathCheckTimeLineParams] = useState<any>();
  useEffect(() => {
    const nodes = steps.find(it => it.type === StepType.NODE_GENERATE)?.data;
    setNodeContent(JSON.stringify(nodes, null, 2));

    AutoPlanApi.checkNodeContent(JSON.stringify(nodes, null, 2)).then(res => {
      setCheckResult(result(res, 'data'));
    });
  }, []);

  useEffect(() => {
    if (checkResult) {
      setNodeCheckTimeLineParams({
        render: (obj: { isPass: boolean; title: string; text: string }) => {
          return (
            <div className={styles.checkResultItem}>
              <div className={styles.checkResultKey}>
                <div className={styles.checkName}>{obj.title}</div>
                <div className={obj.isPass ? styles.pass : styles.noPass}>
                  {intl.formatMessage(obj.isPass ? 'CHECK_PASS' : 'CHECK_FAIL')}
                </div>
              </div>
              <div className={styles.checkResultValue}>{obj.text}</div>
            </div>
          );
        },
        data: [
          {
            content: {
              isPass: true,
            },
          },
          {
            iconType: result(checkResult, 'pointCheckResult.hasEndNode') ? 'success' : 'error',
            content: {
              isPass: result(checkResult, 'pointCheckResult.hasEndNode'),
              title: intl.formatMessage('HVAE_END_NODE'),
              text: renderNodeCheckResult(result(checkResult, 'pointCheckResult.hasEndNode')),
            },
          },
          {
            iconType: result(checkResult, 'pointCheckResult.cycle') ? 'error' : 'success',
            content: {
              isPass: !result(checkResult, 'pointCheckResult.cycle'),
              title: intl.formatMessage('NODE_CHECK_RING'),
              text: renderNodeCheckResult(result(checkResult, 'pointCheckResult.cycle')),
            },
          },
          {
            iconType: result(checkResult, 'nodeCheckResult.unReferenceNodes', [])?.length > 0 ? 'error' : 'success',
            content: {
              isPass: result(checkResult, 'nodeCheckResult.unReferenceNodes', [])?.length > 0 ? false : true,
              title: intl.formatMessage('NODE_CHECK_ISOLATE_NODE'),
              text:
                result(checkResult, 'nodeCheckResult.unReferenceNodes', [])?.length > 0 ?
                  result(checkResult, 'nodeCheckResult.unReferenceNodes') :
                  intl.formatMessage('NONE'),
            },
          },
        ],
      });

      setpathCheckTimeLineParams({
        render: (obj: { isPass: boolean; title: string; text: string }) => {
          return (
            <div className={styles.checkResultItem}>
              <div className={styles.checkResultKey}>
                <div className={styles.checkName}>{obj.title}</div>
                <div className={obj.isPass ? styles.pass : styles.noPass}>
                  {intl.formatMessage(obj.isPass ? 'CHECK_PASS' : 'CHECK_FAIL')}
                </div>
              </div>
              <div className={styles.checkResultValue}>{obj.text}</div>
            </div>
          );
        },
        data: [
          {
            content: {
              isPass: true,
            },
          },
          {
            iconType: result(checkResult, 'pathCheckResult.withEndNodePaths', []).length > 0 ? 'error' : 'success',
            content: {
              isPass: !result(checkResult, 'pathCheckResult.withEndNodePaths', []).length,
              title: intl.formatMessage('PATH_CHECK_INCLUDE_END_NODE'),
              text: renderPaths(result(checkResult, 'pathCheckResult.withEndNodePaths', [])),
            },
          },
          {
            iconType:
              result(checkResult, 'pathCheckResult.withoutStartNodeErrorPaths', []).length > 0 ? 'error' : 'success',
            content: {
              isPass: !result(checkResult, 'pathCheckResult.withoutStartNodeErrorPaths', []).length,
              title: intl.formatMessage('PATH_CHECK_WITHOUT_START_NODE'),
              text: renderPaths(result(checkResult, 'pathCheckResult.withoutStartNodeErrorPaths', [])),
            },
          },
        ],
      });
    }
  }, [checkResult]);

  useEffect(() => {
    if (nodeCheckTimeLineParams && pathCheckTimeLineParams) {
      const nodeChecks = nodeCheckTimeLineParams.data.filter(
        (it: { content: { isPass: boolean } }) => !it.content.isPass,
      );
      const pathChecks = pathCheckTimeLineParams.data.filter(
        (it: { content: { isPass: boolean } }) => !it.content.isPass,
      );
      setPassAllCheck(!(nodeChecks.length > 0 || pathChecks.length > 0));
    } else {
      setPassAllCheck(false);
    }
  }, [nodeCheckTimeLineParams, pathCheckTimeLineParams]);

  const checkNodeContent = () => {
    AutoPlanApi.checkNodeContent(nodeContent).then(res => {
      setCheckResult(result(res, 'data'));
    });
  };

  const translateToRecipe = () => {
    if (nodeContent) {
      setDialogOpen(true);
    }
  };

  const renderPaths = (paths: string[][]) => {
    if (paths.length === 0) {
      return intl.formatMessage('NONE');
    }

    return (
      <>
        {paths.map((path, index) => {
          let pathStr = `${intl.formatMessage('PATH')}${index + 1}：`;
          for (let i = 0; i < path.length; i++) {
            pathStr += i === 0 ? path[i] : `-> ${path[i]}`;
          }
          return <div key={index}>{pathStr}</div>;
        })}
      </>
    );
  };

  const renderNodeCheckResult = (checkRes: boolean) => {
    return checkRes ? intl.formatMessage('HAVE') : intl.formatMessage('NONE');
  };

  const renderEmptyTip = () => {
    return (
      <div className={styles.emptyContainer}>
        <img src={emptyIcon} height='140' />
        <div className={styles.tip}>{intl.formatMessage('EMPTY_CHECK_TIP')}</div>
      </div>
    );
  };

  const renderEndResult = () => {
    if (passAllCheck) {
      return <div className={styles.checkPass}>{intl.formatMessage('CHECK_PASS')}</div>;
    } else if (passAllCheck === undefined) {
      return null;
    } else {
      return <div className={styles.checkFail}>{intl.formatMessage('CHECK_FAIL')}</div>;
    }
  };

  const onCreate = () => {
    form
      .validateFields()
      .then(async(values: any) => {
        // 生成yaml
        const yamlRes = await AutoPlanApi.translateToRecipe(nodeContent);
        const yamlStr = result(yamlRes, 'data', '');
        // 添加yaml头
        const header = addRecipeHeader(values);
        const params = {
          ...values,
          content: `${header}nodes:\n${addSpacesToStartOfEachLine(yamlStr)}`,
        };
        AutoPlanApi.createDraftRecipe(params)
          .then(res => {
            const draftRecipeId = result(res, 'data.draftRecipeId');
            const stepsCopy: GenerateStepDo[] = cloneDeep(steps);
            for (let step of stepsCopy) {
              if (step.type === StepType.NODE_GENERATE) {
                step.status = 'complete';
              } else if (step.type === StepType.RECIPE_GENERATE) {
                step.status = 'running';
                step.data = { ...params, id: draftRecipeId };
              }
            }
            setSteps(stepsCopy);
          })
          .finally(() => {
            setDialogOpen(false);
          });
      })
      .catch(error => {
        // no handling
      });
  };

  /**
   * 给字符串每行添加两个空格，包括第一行
   * @param str
   * @returns
   */
  const addSpacesToStartOfEachLine = (str: string) => {
    return str.replace(/^|\n/gm, '  $&');
  };

  const addRecipeHeader = (values: Record<string, string>) => {
    let str = '';
    const keys = Object.keys(values);
    for (let key of keys) {
      str += key === 'version' ? `${key}: ${values[key]}\n` : `${key}: "${values[key]}"\n`;
    }
    return str;
  };

  const custonRule = (value: string) =>
    value.trim().length === 0 ? Promise.reject(intl.formatMessage('CONTENT_EMPTY_HINT')) : Promise.resolve();

  return (
    <div className={styles.nodeGenerate}>
      <div className={styles.header}>
        <div className={styles.title}>{intl.formatMessage('NODE_GENERATE')}</div>
        <div className={styles.btnGroup}>
          <Button text={intl.formatMessage('CONTENT_CHECK')} onClick={checkNodeContent} disabled={!nodeContent} />
          <div className={`${styles.opeartorMain} ${passAllCheck ? '' : styles.disabled}`} onClick={translateToRecipe}>
            {intl.formatMessage({ id: 'YMAL_GENERATE' })}
          </div>
        </div>
      </div>

      <div className={styles.content}>
        <div className={styles.nodeContent}>
          <CodeMirror
            value={nodeContent}
            options={{
              mode: 'text/json',
              theme: 'monokai',
              lineNumbers: true,
              smartIndent: true,
            }}
            onBeforeChange={(editor, data, value) => {
              setNodeContent(value);
              setPassAllCheck(undefined);
              setCheckResult(undefined);
            }}
          />
        </div>
        <div className={styles.contentCheckResult}>
          <div className={styles.resultHeader}>
            <div className={styles.resultHeaderTitle}>{intl.formatMessage('CONTENT_CHECK_RESULT')}</div>
            {renderEndResult()}
          </div>
          <div className={styles.resultWrapper}>
            {checkResult ? (
              <div className={styles.resultContainer}>
                <div className={styles.level2Title}>{intl.formatMessage('NODE_CHECK')}</div>
                {nodeCheckTimeLineParams && <TimeLine {...nodeCheckTimeLineParams} className='nodeCheckTimeLine' />}
                <div className={styles.level2Title}>{intl.formatMessage('PATH_CHECK')}</div>
                {pathCheckTimeLineParams && <TimeLine {...pathCheckTimeLineParams} className='nodeCheckTimeLine' />}
              </div>
            ) : (
              renderEmptyTip()
            )}
          </div>
        </div>
      </div>

      {dialogOpen && (
        <DialogPro
          className={styles.dialog}
          isOpen={true}
          onClose={() => {
            setDialogOpen(false);
          }}
          focusOnClose={false}
          closable={false}
          minSize={[600, 400]}
          customHeader={
            <DialogCustomHeader
              title={intl.formatMessage('SAVE_RECIPE')}
              onClose={() => {
                setDialogOpen(false);
              }}
            />
          }
        >
          <div>
            <FormPro form={form} initialValues={{ description: '' }}>
              <FormPro.Item
                label={intl.formatMessage('NAME')}
                name='name'
                rules={[{ required: true }, { maxLength: 512 }, { custom: custonRule }]}
              >
                <TextField />
              </FormPro.Item>
              <FormPro.Item label={intl.formatMessage('DESCRIPTION')} name='description' rules={[{ maxLength: 1024 }]}>
                <TextField />
              </FormPro.Item>
              <FormPro.Item
                label='domain'
                name='domain'
                rules={[{ required: true }, { maxLength: 512 }, { custom: custonRule }]}
              >
                <TextField />
              </FormPro.Item>
              <FormPro.Item
                label={intl.formatMessage('SCENE')}
                name='scene'
                rules={[{ required: true }, { maxLength: 512 }, { custom: custonRule }]}
              >
                <TextField />
              </FormPro.Item>
              <FormPro.Item
                label={intl.formatMessage('VERSION')}
                name='version'
                rules={[{ required: true }, { maxLength: 32 }, { custom: custonRule }]}
              >
                <TextField />
              </FormPro.Item>
            </FormPro>
            <div className={styles.formBottom}>
              <Button text={intl.formatMessage('SAVE')} status='primary' onClick={onCreate} />
              <Button
                text={intl.formatMessage('CANCEL')}
                onClick={() => {
                  setDialogOpen(false);
                }}
              />
            </div>
          </div>
        </DialogPro>
      )}
    </div>
  );
};

export default NodeGenerate;
