import React from 'react';
import { useThemeStatus } from '@/Components/Hooks/UseThemeStatus';
import { useIntl } from '@cloudsop/horizon-intl';

import stylesDark from './dark.module.less';
import stylesLight from './light.module.less';

const RecommendQuestions: React.FC<{
 questions: string[];
}> = props => {
  const intl = useIntl();
  const { questions } = props;
  const styles = useThemeStatus() === 'light' ? stylesLight : stylesDark;

  const renderRecommendQuestions = () => {
    return questions.map((item) => {
      return (
        <div key={item} className={styles.questionItem} title={item}>
          {item}
        </div>
      );
    });
  };

  return (
    <div className={styles.recommendQuestionsWrapper}>
      <div className={styles.recommendQuestionsTitle}>{intl.formatMessage({ id: 'RECOMMEND_QUESTIONS' })}</div>
      <div className={styles.questionsContainer}>
        {renderRecommendQuestions()}
      </div>
    </div>
  );
};

export default RecommendQuestions;