/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

/**
 * 告警分析详情-事件表格行展开组件
 */

import React, { useEffect, useReducer, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Table, Tooltip, ButtonMenu, Row, Col } from 'eview-ui';
import * as echarts from 'echarts';
import { $t, registerI18nResource, modal, util, pages } from '@util';
import { loadTopo } from '@util/topo/TopoApp';
import criticalIcon from '@assets/topo/critical.png';
import majorIcon from '@assets/topo/major.png';
import minorIcon from '@assets/topo/minor.png';
import infoIcon from '@assets/topo/info.png';
import criticalSerialIcon from '@assets/topo/criticalAlarm.png';
import majorSerialIcon from '@assets/topo/majorAlarm.png';
import minorSerialIcon from '@assets/topo/minorAlarm.png';
import infoSerialIcon from '@assets/topo/infoAlarm.png';
import linkIcon from '@assets/topo/link.png';
import datepicker from '@assets/datepicker.png';
import restoreIcon from '@assets/restore.png';
import { RESULT_CODE } from '@const/common';
import AlarmTableRowExpand from './AlarmTableRowExpand';
import { AlarmColorBlock } from './CommonComponents';
import { alarmTableInitialState, alarmTableReducer } from '../reducer/alarmTable';
import { queryAlarmTable, markKeyAlarm, queryAlarmTopo, queryAlarmScore, queryAlarmCount } from '../api';
import detailsI18nResource from '../locales';
import { ALARM_WEBSITE_URL, ALARM_WEBSITE_URL_PARAMS, ALARM_TABLE_COLS, ALARM_TABLE_COMPRESS_COLS } from '../const';
import { KEY_ALARM_CONST, TOPO_INIT_PARAM, ALARM_TASK_TYPE } from '../const';
import pageCss from '../css/index.css';
import { handleTopoElements } from '../utils/TopoManagement';
import SelectTimes from './SelectTimes';

const EventTableRowExpand = props => {
  // 注册国际化资源
  registerI18nResource(detailsI18nResource, 'alarmI18n');

  const { eventId, eventTaskId, showType, showValue, rootAlarmId, timeRangeRadioId } = props;
  const { alarmType = ALARM_TASK_TYPE.analysis } = props;

  // ref定义
  const alarmTable = useRef(null);
  const savedData = useRef({});
  const savedCustomTime = useRef([]);
  const lineChartObj = useRef(null);
  const radarChartObj = useRef(null);

  // state定义
  const [checkedAlarmArr, setCheckedAlarmArr] = useState([]);
  const [expandedRows, setExpandedRows] = useState([]);

  // reducer定义
  const [state, dispatch] = useReducer(alarmTableReducer, alarmTableInitialState);
  const { alarmData, pageSize, currentPage, recordCount, showAlarmTopo, topoCanvasHeight, openDatePickerDialog } =
    state;

  // 批量标记菜单按钮初始项
  const buttonOptions = [
    { text: $t('alarm.details.buttonMenu.batchMarkAsKey'), value: KEY_ALARM_CONST.yes },
    { text: $t('alarm.details.buttonMenu.batchMarkAsNoKey'), value: KEY_ALARM_CONST.no },
  ];

  useEffect(() => {
    window.addEventListener('resize', resizeChart);

    return () => {
      window.removeEventListener('resize', resizeChart);
    };
  }, []);

  const resizeChart = () => {
    setTimeout(() => {
      lineChartObj.current.resize();
      radarChartObj.current.resize();
    }, 100);
  };

  // 设置拓扑视图显示/隐藏
  const setAlarmTopoShow = (flag, height) => {
    if (height) {
      dispatch({ type: 'changeShowTopo', showAlarmTopo: flag, topoCanvasHeight: height });
    } else {
      dispatch({ type: 'changeShowTopo', showAlarmTopo: flag, topoCanvasHeight });
    }
  };

  // 标记关键告警
  const markAlarm = (ids, key) => {
    const setting = {
      taskId: eventTaskId,
      ids,
      keyAlarm: key,
    };
    markKeyAlarm(setting, resp => {
      if (resp && resp.resultCode === RESULT_CODE.success) {
        updateAlarmTable();
        modal.success($t('alarm.details.dialog.title.success'), $t('alarm.details.batchMark.success'));
        setCheckedAlarmArr([]);
      } else {
        modal.error($t('alarm.details.dialog.title.error'), $t('alarm.details.batchMark.markError'));
      }
    }, error => {
      modal.error($t('alarm.details.dialog.title.error'), $t('alarm.details.batchMark.markError'));
    });
  };

  // 更新告警得分雷达图
  const updateAIScoreRadarChart = () => {
    const nameArr = [];
    const valueArr = [];
    const description = {};

    queryAlarmScore({ taskId: eventTaskId, id: eventId }, resp => {
      if (resp && resp.resultCode === RESULT_CODE.success && resp.data) {
        let respData = resp.data;
        if (respData.scores.length === 0) {
          return;
        }
        respData.scores.map(item => {
          nameArr.push(item.text);
          valueArr.push(item.value);
          description[item.text] = item.description;
        });
        const params = {
          scores: respData.scores,
          aiScore: respData.aiScore,
          nameArr,
          valueArr,
        };
        const options = getRadarChartOptions(params);
        let chartDom = document.getElementById(`${eventTaskId}_${eventId}_radarChart`);
        if (!chartDom) {
          return;
        }
        let chartObj = echarts.init(chartDom);
        chartObj.setOption(options);
        radarChartObj.current = chartObj;
      }
    });
  };

  // 获取雷达图初始化options
  const getRadarChartOptions = (params) => {
    const { aiScore, nameArr, valueArr, scores } = params;
    return {
      title: {
        text: `${$t('alarm.details.alarmScoreChart.aiScore')}: ${aiScore}`,
        top: '2%',
        left: 'center',
        subtextStyle: {
          color: 'black',
          fontSize: 16,
        },
      },
      tooltip: {
        formatter: (param) => {
          let result = `<span style='font-weight: bold;display: block;height: 2rem;'>${param.name}</span><table>`;
          if (param.dataIndex === 0) {
            for (let i = 0; i < param.value.length; i++) {
              result += `<tr>
                <td><div class='${pageCss.tipCircle}' style='background-color: ${param.color}'></div>${nameArr[i]}</td>
                <td style='padding-left: 15px'>
                  ${$t('alarm.details.radarTips.score')}<span style='font-weight: bold'>${param.value[i]}</span>
                </td></tr>`;
            }
            result += '</table>';
            return result;
          }
          return null;
        },
      },
      radar: {
        name: {
          show: true,
          color: 'black',
        },
        indicator: scores.map(item => {
          return { name: item.text, max: 100 };
        }),
        triggerEvent: true,
        center: ['50%', '58%'],
        radius: '60%',
      },
      series: [{
        name: '',
        type: 'radar',
        symbol: 'none',
        emphasis: {
          lineStyle: {
            width: 4,
          },
        },
        data: [
          {
            name: $t('alarm.details.alarmScoreChart.dimensionScore'),
            value: valueArr,
            itemStyle: {
              color: '#1c88e8',
            },
          },
        ],
      }],
    };
  };

  // 更新告警数量曲线图
  const updateAlarmCountChart = (customTimes) => {
    savedData.current = {};
    savedCustomTime.current = [];
    let chartDom = document.getElementById(`${eventTaskId}_${eventId}_alarmLineChart`);
    if (!chartDom) {
      return;
    }
    let chartObj = echarts.init(chartDom);
    lineChartObj.current = chartObj;
    pages.getEmptyChart(chartObj, '', $t('alarm.details.alarmCountChart.loading'));
    let setting = {
      id: rootAlarmId,
      taskId: eventTaskId,
      alarmTimes: customTimes ? customTimes : [],
    };
    queryAlarmCount(setting, resp => {
      if (resp && resp.resultCode === RESULT_CODE.success && resp.data && resp.data.alarmSummaryList.length > 0) {
        chartObj.clear();
        let optionsParam = getOptionsParam(`${resp.data.alarmName}(${rootAlarmId})`, resp.data);
        let options = getAlarmCountChartOptions(optionsParam);
        chartObj.setOption(options);
        savedData.current[rootAlarmId] = optionsParam.series[0];
        savedCustomTime.current = [optionsParam.startTime, optionsParam.endTime];
      } else {
        chartObj.clear();
      }
    }, error => {
      chartObj.clear();
    });
  };

  // 获取告警数量折线图options
  const getAlarmCountChartOptions = (optionsParam) => {
    return {
      title: {
        text: $t('alarm.details.alarmCountChart.title'),
        left: 'center',
        top: 8,
        align: 'right',
      },
      grid: {
        left: 16,
        right: 30,
        bottom: 44,
        containLabel: true,
      },
      toolbox: {
        top: 10,
        right: 30,
        itemSize: 16,
        itemGap: 20,
        showTitle: false,
        feature: {
          myRestore: {
            show: true,
            title: $t('alarm.details.alarmCountChart.customTime.restoreTip'),
            icon: `image://${restoreIcon}`,
            onclick: () => {
              updateAlarmCountChart();
            },
          },
          myOpenDataPicker: {
            show: true,
            title: $t('alarm.details.alarmCountChart.customTime.datepickerTitle'),
            icon: `image://${datepicker}`,
            onclick: () => {
              dispatch({ type: 'changeShowDatePickerDialog', openDatePickerDialog: true });
            },
          },
        },
        tooltip: {
          show: true,
          formatter(param) {
            return `<div style='color: #212121;margin-top: 3px'>${param.title}</div>`;
          },
          backgroundColor: '#fff',
          textStyle: {
            fontSize: 14,
          },
        },
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          animation: true,
          axis: 'x',
          label: {
            backgroundColor: '#505765',
            formatter: (params) => {
              if (params.axisDimension === 'x') {
                return util.dateTimeFormat(params.value).replace(' ', '\n');
              } else {
                return params.value;
              }
            },
          },
        },
      },
      dataZoom: [
        {
          show: true,
          realtime: true,
          start: 0,
          end: 100,
          height: 20,
          minValueSpan: 1000 * 60 * 10,
          showDetail: false,
          xAxisIndex: 0,
          labelFormatter: (value) => {
            return util.dateTimeFormat(value).replace(' ', '\n');
          },
        },
        {
          show: true,
          realtime: true,
          start: 0,
          end: 100,
          right: 0,
          yAxisIndex: 0,
          width: 25,
          showDetail: false,
        },
        {
          type: 'inside',
          realtime: true,
          start: 0,
          end: 100,
          zoomOnMouseWheel: 'ctrl',
        },
      ],
      xAxis: [
        {
          type: 'time',
          boundaryGap: false,
          min: optionsParam ? optionsParam.startTime : '',
          max: optionsParam ? optionsParam.endTime : '',
          axisLabel: {
            margin: 8,
            interval: 16,
            hideOverlap: true,
            formatter: (value) => {
              return util.dateTimeFormat(value).replace(' ', '\n');
            },
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: [
        {
          name: $t('alarm.details.alarmCountChart.unit'),
          type: 'value',
          axisLine: {
            show: true,
          },
          axisTick: {
            show: true,
          },
        },
      ],
      series: optionsParam.series,
    };
  };

  // 获取告警数量折线图options的参数
  const getOptionsParam = (name, respData) => {
    let data = respData.alarmSummaryList.map(item => {
      return { value: [parseInt(item.timestampStr), item.alarmNum] };
    });
    return {
      startTime: parseInt(respData.startTime),
      endTime: parseInt(respData.endTime),
      series: [{
        name,
        type: 'line',
        lineStyle: {
          width: 1.5,
        },
        emphasis: {
          focus: 'none',
          lineStyle: {
            width: 1.5,
          },
        },
        showSymbol: false,
        showSize: 2,
        data: data ? data : [],
      }],
    };
  };

  // 添加告警数量趋势线
  const addShowAlarmCountChart = (alarmId, alarmName) => {
    if (savedData.current[alarmId]) { // 该告警趋势线已存在
      return;
    }

    lineChartObj.current.showLoading('default', {
      text: $t('alarm.details.alarmCountChart.loading'),
      fontSize: 16,
      lineWidth: 3,
      textColor: '#000000',
      maskColor: 'rgba(201,196,196,0.4)',
    });
    let setting = {
      id: alarmId,
      taskId: eventTaskId,
      alarmTimes: savedCustomTime.current,
    };
    queryAlarmCount(setting, resp => {
      if (resp && resp.resultCode === RESULT_CODE.success && resp.data && resp.data.alarmSummaryList.length > 0) {
        let optionsParams = getOptionsParam(`${alarmName}(${alarmId})`, resp.data);
        savedData.current[alarmId] = optionsParams.series[0];
        let seriesArr = [];
        Object.keys(savedData.current).forEach(key => {
          seriesArr.push(savedData.current[key]);
        });
        optionsParams.series = seriesArr;
        let options = getAlarmCountChartOptions(optionsParams);
        lineChartObj.current.setOption(options);
      }
      lineChartObj.current.hideLoading();
    }, error => {
      lineChartObj.current.hideLoading();
    });
  };

  // 加载告警拓扑
  const loadAlarmTopo = () => {
    let setting = {
      taskId: eventTaskId,
      incidentId: eventId,
    };
    queryAlarmTopo(
      setting,
      async resp => {
        if (resp && resp.resultCode === RESULT_CODE.success && resp.data && resp.data.node.length > 1) {
          let newHeight = resizeCanvasHeight(resp.data.node);
          await setAlarmTopoShow(true, newHeight);
          initTopoApp(`${eventTaskId}_eventTopoDiv_${eventId}`, resp.data);
        } else {
          setAlarmTopoShow(false);
        }
      },
      () => {
        setAlarmTopoShow(false);
      },
    );
  };

  // 重新设置拓扑画布大小
  const resizeCanvasHeight = nodes => {
    let maxY = 0;
    let height = '';
    nodes.map(item => {
      if (item.location) {
        maxY = item.location.y > maxY ? item.location.y : maxY;
      }
    });
    if (maxY > 1) {
      height = `${TOPO_INIT_PARAM.defaultY + (maxY - 1) * TOPO_INIT_PARAM.multiples}rem`;
    }
    return height;
  };

  // 处理查询告警接口返回的数据
  const handleAlarmResp = respData => {
    const alarmList = respData.alarmList;
    return alarmList.map(item => {
      let data = {
        alarmNumber: <AlarmSerialNo keyAlarmFlag={item.keyAlarm} alarmSerialNo={item.id} />,
        alarmLevel: <AlarmColorBlock alarmKey={item.level} />,
        alarmId: item.alarmId,
        alarmName: item.alarmName,
        startTime: util.dateTimeFormat(item.startTime),
        endTime:
          item.endTime && item.endTime !== '0' ?
            util.dateTimeFormat(item.endTime) :
            $t('alarm.details.eventTable.happening'),
        operation: (
          <OperationCol
            keyAlarmFlag={item.keyAlarm}
            alarmSerialNo={item.id}
            markClick={markAlarm}
            alarmType={alarmType}
            startTime={item.startTime}
            endTime={item.endTime}
          />
        ),
        alarmSerialNumber: item.id,
      };
      if (alarmType === ALARM_TASK_TYPE.compress) {
        data.alarmSource = item.alarmSource;
      }
      return data;
    });
  };

  // 更新告警表格
  const updateAlarmTable = () => {
    const setting = {
      taskId: eventTaskId,
      incidentId: eventId,
      pageIndex: currentPage,
      pageSize,
      timeRangeRadioId,
      alarmType,
    };
    queryAlarmTable(setting, resp => {
      if (resp && resp.resultCode === RESULT_CODE.success && resp.data && resp.data.alarmList.length > 0) {
        const alarmTableArr = handleAlarmResp(resp.data);
        dispatch({
          type: 'updateTableData',
          recordCount: resp.data.totalCount,
          alarmData: alarmTableArr,
        });
      } else {
        dispatch({ type: 'updateTableData', recordCount: 0, alarmData: [] });
      }
    }, error => {
      dispatch({ type: 'updateTableData', recordCount: 0, alarmData: [] });
    });
  };

  // 事件表格行展开
  const handleRowExpand = (rowData) => {
    return <AlarmTableRowExpand alarmSerialNo={rowData.data[0].value} />;
  };

  // 事件表格分页
  const handlePaging = (pageIndex) => {
    dispatch({ type: 'updatePageIndexAndalarmSerialNo', currentPage: pageIndex });
  };

  // 事件表格切换页面大小
  const handlePageSize = (size) => {
    dispatch({ type: 'updatePageSize', pageSize: size });
  };

  // 批量标记按钮事件
  const batchMarkClick = (data) => {
    if (!checkedAlarmArr || checkedAlarmArr.length === 0) {
      modal.error($t('alarm.details.batchMark.noDataDialogTitle'), $t('alarm.details.batchMark.atLeastCheck'));
      return;
    }

    markAlarm(checkedAlarmArr, data.value);
  };

  // 初始化拓扑画布
  const initTopoApp = (topoDivId, respData) => {
    let topology = new eviewC.Topology(topoDivId);

    // 初始化topology
    const topoDivDoc = $(`#${topoDivId}`);
    topology.resizeView({
      x: 0,
      y: 0,
      width: topoDivDoc.width(),
      height: topoDivDoc.height(),
    });

    // 禁止鼠标滚轮放大/缩小画布
    topology.setDisableAllWheelZoom(true);

    // 启用tips提示
    topology.setEnableTooltip(true);
    topology.getTooltip = ele => {
      if (ele instanceof eviewC.Node && ele.getProperty('toolTipEle')) {
        return ele.getProperty('toolTipEle');
      }
      return null;
    };

    // 全局样式设置
    Util.setGlobalStyle('eViewC.group.fill.color', '#6d6a6a');
    Util.setGlobalStyle('eViewC.group.border.radius', 5);
    Util.setGlobalStyle('eViewC.node.borderStyle', 'circle');
    Util.setGlobalStyle('eViewC.node.borderWidth', 1);
    Util.setGlobalStyle('eViewC.node.borderColor', '#000000');
    Util.setGlobalStyle('eViewC.background.image', '');

    // 注册图片
    topology.registerImage(criticalIcon, 'criticalIcon');
    topology.registerImage(majorIcon, 'majorIcon');
    topology.registerImage(minorIcon, 'minorIcon');
    topology.registerImage(infoIcon, 'infoIcon');
    topology.registerImage(criticalSerialIcon, 'criticalSerialIcon');
    topology.registerImage(majorSerialIcon, 'majorSerialIcon');
    topology.registerImage(minorSerialIcon, 'minorSerialIcon');
    topology.registerImage(infoSerialIcon, 'infoSerialIcon');
    topology.registerImage(linkIcon, 'linkIcon');

    // 加载拓扑
    const topoData = handleTopoElements(respData, addShowAlarmCountChart);
    const params = {
      customTopology: topology,
      elements: topoData,
    };
    loadTopo(params);
  };

  // 异常表格全选按钮点击事件
  const onTableHeaderCheck = (checkedRows) => {
    let tempArr = checkedAlarmArr;
    if (checkedRows.length > 0) {
      // 全选
      checkedRows.map(item => {
        if (checkedAlarmArr.indexOf(item) === -1) {
          tempArr.push(item);
        }
      });
    } else {
      // 取消全选
      let currentPageIds = alarmData.map(item => {
        return item.alarmSerialNumber;
      });
      tempArr = checkedAlarmArr.filter(item => {
        return currentPageIds.indexOf(item) === -1;
      });
    }
    setCheckedAlarmArr(tempArr);
  };

  useEffect(() => {
    if (alarmType === ALARM_TASK_TYPE.analysis) {
      loadAlarmTopo();
      updateAIScoreRadarChart();
      updateAlarmCountChart();
    }

    return () => {
      savedData.current = {};
      savedCustomTime.current = [];
    };
  }, []);

  useEffect(() => {
    updateAlarmTable();
  }, [pageSize, currentPage, showType, showValue]);

  return (
    <div style={{ padding: '16px' }}>
      {alarmType === ALARM_TASK_TYPE.analysis && (
        <>
          {showAlarmTopo && (
            <div
              id={`${eventTaskId}_eventTopoDiv_${eventId}`}
              style={{ height: topoCanvasHeight, background: '#f5f5f5' }}
            />
          )}
          <div style={{ display: 'grid', gridTemplateColumns: 'auto 24rem', rowGap: '12px', height: '18rem' }}>
            <div id={`${eventTaskId}_${eventId}_alarmLineChart`} className={pageCss.alarmInfoRightChartCls} />
            <div id={`${eventTaskId}_${eventId}_radarChart`} className={pageCss.alarmInfoRightChartCls} />
          </div>
        </>
      )}

      <div id="alarmTableDiv" style={{ marginTop: '12px' }}>
        {
          alarmType === ALARM_TASK_TYPE.analysis && (
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <ButtonMenu
                text={$t('alarm.details.buttonMenu.name')}
                options={buttonOptions}
                onClick={batchMarkClick}
                className={pageCss.batchMarkAlarmButtonCls}
                optionStyle={{ width: '132px' }}
              />
            </div>
          )
        }
        <Table
          id="alarmTable"
          className={pageCss.alarm_analysis_alarmTableCls}
          columns={alarmType === ALARM_TASK_TYPE.compress ? ALARM_TABLE_COMPRESS_COLS : ALARM_TABLE_COLS}
          keyIndex={0}
          dataset={alarmData}
          pageSize={pageSize}
          currentPage={currentPage}
          recordCount={recordCount}
          onPageChange={handlePaging}
          onPageSizeChange={handlePageSize}
          onRowExpend={handleRowExpand}
          enablePagination
          enableRowExpand
          enableCheckBox={alarmType === ALARM_TASK_TYPE.analysis}
          expandedRow={expandedRows}
          enableMulitiExpand
          itemOrderChanger
          splitPagination={true}
          ref={alarmTable}
          checkedRows={checkedAlarmArr}
          onRowCheck={(row, checkedRows) => setCheckedAlarmArr(checkedRows)}
          onHeaderCheck={onTableHeaderCheck}
        />
      </div>
      <SelectTimes onClickConfirm={updateAlarmCountChart} showDialog={openDatePickerDialog} changeShowDialog={dispatch} />
    </div>
  );
};

EventTableRowExpand.propTypes = {
  eventId: PropTypes.string.isRequired,
  eventTaskId: PropTypes.string.isRequired,
  showType: PropTypes.number.isRequired,
  showValue: PropTypes.array.isRequired,
  rootAlarmId: PropTypes.string.isRequired,
  timeRangeRadioId: PropTypes.number.isRequired,
  alarmType: PropTypes.string,
};

// 告警流水号
const AlarmSerialNo = (props) => {
  return (
    <div>
      {
        props.keyAlarmFlag === KEY_ALARM_CONST.yes &&
        <Tooltip content={$t('alarm.details.alarmTableCol.keyAlarmTips')} trigger='hover' placement="bottom">
          <div className={pageCss.icon_div_keyAlarm} />
        </Tooltip>
      }
      <span className={pageCss.alarmSerialNoSpanCls}>{props.alarmSerialNo}</span>
    </div>
  );
};

// 操作列定义
const OperationCol = props => {
  const { alarmSerialNo, keyAlarmFlag, markClick, alarmType, startTime, endTime } = props;

  // 跳转到告警页面
  const jumpToAlarmPage = () => {
    util.jumpToAlarmLogPage([], [alarmSerialNo], startTime, endTime);
  };

  // 标记关键告警
  const mark = () => {
    markClick([alarmSerialNo], keyAlarmFlag === KEY_ALARM_CONST.yes ? KEY_ALARM_CONST.no : KEY_ALARM_CONST.yes);
  };

  return (
    <>
      {alarmType === ALARM_TASK_TYPE.analysis && (
        <Tooltip
          content={
            keyAlarmFlag === KEY_ALARM_CONST.yes ?
              $t('alarm.details.alarmTableCol.markAsNoKey') :
              $t('alarm.details.alarmTableCol.markAsKey')
          }
          trigger="hover"
          placement='bottom'
        >
          <div
            className={
              keyAlarmFlag === KEY_ALARM_CONST.yes ? pageCss.icon_div_markNoKeyAlarm : pageCss.icon_div_markKeyAlarm
            }
            onClick={() => mark()}
          />
        </Tooltip>
      )}
      <Tooltip content={$t('alarm.details.eventTableCol.showAlarm')} trigger="hover" placement='bottom'>
        <div className={pageCss.icon_div_jumpToAlarm} onClick={jumpToAlarmPage} />
      </Tooltip>
    </>
  );
};

OperationCol.propTypes = {
  alarmSerialNo: PropTypes.array.isRequired,
  keyAlarmFlag: PropTypes.string,
  markClick: PropTypes.func.isRequired,
  alarmType: PropTypes.string,
  startTime: PropTypes.string.isRequired,
  endTime: PropTypes.string.isRequired,
};

export default EventTableRowExpand;
