/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

export default {
  'zh-cn': {
    // 公共提示
    'task.common.tip': '提示',
    'task.common.risk.tip': '高危',
    'task.common.error.tip': '错误',
    'task.common.confirm.tip': '确认',
    'task.common.cancel': '取消',
    'task.common.back': '返回',
    'task.common.last.step': '上一步',
    'task.common.next.step': '下一步',
    'task.common.confirm': '确定',
    'task.common.submit': '完成',
    'task.common.modify': '修改',
    'task.common.please.enter': '请输入',
    'task.common.loading': '加载中...',
    'task.common.no.data': '暂无数据',
    'task.please.wait': '请等待...',
    'task.common.search.fail': '查询失败',
    'task.common.delete.failure': '删除失败',
    'task.create': '创建',
    'task.delete': '删除',
    'task.modify': '编辑',
    'task.view': '查看',
    'task.common.save.fail.tip': '保存信息失败',
    'task.common.table.operate': '操作',
    'task.common.total': '总计',
    'task.common.clear.all': '清除',
    'task.common.close': '关闭',
    'task.common.not.configured': '未配置',
    'task.common.configured': '已配置',
    'task.time.day': '天',
    'task.time.week': '周',
    'task.time.month': '月',
    'task.time.hour': '小时',
    // 任务列表
    'task.tab.title': '任务管理',
    'task.list': '任务列表',
    'task.please.input.name': '请输入任务名称',
    'task.auto.refresh': '自动刷新：',
    'task.list.name': '任务名称',
    'task.list.region': '容灾区域',
    'task.list.execute.cycle': '执行策略',
    'task.list.manager': '维护管理员',
    'task.list.status': '任务状态',
    'task.list.status.draft': '草稿',
    'task.list.no.started': '未启动',
    'task.list.activated': '已激活',
    'task.list.paused': '已暂停',
    'task.list.last.modify.time': '最近一次修改时间',
    'task.list.query.fail': '查询任务列表失败',
    'task.list.view.fail': '查看任务信息失败',
    'task.start': '启动',
    'task.stop': '暂停',
    'task.start.failure': '启动任务失败',
    'task.start.tip': '确定立即启动任务吗？',
    'task.stop.failure': '停止任务失败',
    'task.stop.tip': '任务正在运行中，是否立即停止?',
    'task.delete.tip': '删除任务将无法进行监控项健康检查和容灾切换，确定删除任务？',
    'new.task': '创建任务',
    // 配置基本信息
    'task.basic.info': '配置基本信息',
    'task.basic.info.name': '任务名称',
    'task.basic.info.desc': '任务描述',
    'task.name.placeholder': '请输入任务名称',
    'task.execute.cycle': '任务执行周期',
    'task.cron': 'CRON表达式',
    'task.cycle.one.minute': '1分钟',
    'task.cycle.five.minute': '5分钟',
    'task.cycle.ten.minute': '10分钟',
    'task.cycle.thirty.minute': '30分钟',
    'task.cycle.custom': '自定义',
    'task.valid.error': '校验接口异常',
    'task.name.repeat': '智能容灾任务名称重复',
    'task.basic.type': '任务类型',
    'task.type.disaster': '智能容灾',
    // 配置容灾区域
    'task.region': '配置容灾区域',
    'task.region.configured': '已配置',
    'task.region.max.tip': '单个任务最多创建5个容灾区域',
    'task.region.not.configured': '未配置',
    'task.region.modify': '修改容灾区域',
    'task.region.view': '查看容灾区域',
    'task.region.delete.tip': '确定删除这个容灾区域？',
    'task.region.no.data.tip': '请创建容灾区域',
    'task.region.process': '配置容灾流程',
    'task.region.monitor.info': '配置监控信息',
    'task.region.view.fail.tip': '查询区域信息失败',
    'task.region.list': '容灾区域',
    'task.region.list.member.num': '成员数',
    'task.region.list.monitor.info': '监控信息',
    'task.region.list.process': '容灾脚本',
    'task.region.member': '成员',
    'task.region.nex.step.tip': '请确保所有容灾区域的监控信息和容灾脚本已配置！',
    'task.region.success.tip': '创建容灾区域成功，是否配置监控信息？',
    'task.region.member.no.data.tip': '请先添加容灾区域成员',
    // 配置容灾区域信息及成员
    'task.region.select.member.tip': '请选择成员',
    'task.region.member.number.tip': '成员数量不得超过20个',
    'task.region.member.name': '名称',
    'task.region.member.select': '成员选择',
    'task.region.member.optional': '可选成员',
    'task.region.member.please.enter': '请输入成员名称',
    'task.region.member.selected': '已选成员',
    'task.region.member.home': '所属区域',
    'task.region.member.select.list': '选择成员',
    'task.region.member.max.tip': '成员数量不允许超过{0}',
    'task.region.member.delete.tip': '本操作属于高危操作，如果删除成员，会导致成员已配置的监控信息被清除，若关联监控项' +
      '或容灾切换项其中一项被清空，归属容灾区域的监控信息会变为“未配置”，需要重新进行监控信息配置。请确认是否继续执行该操作。',
    'task.region.member.delete.detail.tip': '我已明确该操作的风险。',
    // 配置监控信息
    'task.region.monitor.alarm': '告警',
    'task.region.monitor.process': '进程',
    'task.region.process.group.search.fail': '查询流程组失败',
    'task.region.member.list.search.fail': '查询成员列表失败',
    'task.region.process.fail': '查询流程失败',
    'task.region.process.detail.fail': '查询流程详情失败',
    'task.region.process.group': '流程组',
    'task.region.process.name': '流程',
    'task.region.process.detail': '流程详情',
    'task.region.process.nodata.tip': '请配置监控流程',
    'task.region.process.create.max.tip': '成员数量为{0}，最多允许创建{1}个进程',
    'task.region.reset.tab': '重置页签',
    'task.region.search.fail': '查询监控信息失败',
    // 配置监控项告警
    'task.region.monitor.alarm.name': '告警名称',
    'task.region.monitor.alarm.id': '告警ID',
    'task.region.monitor.alarm.group': '分组名称',
    'task.region.alarm.optional': '可选告警',
    'task.region.alarm.selected': '已选告警',
    'task.region.alarm.filter.keyword': '请输入关键字',
    'task.region.alarm.nodata.tip': '请配置告警',
    'task.region.selected.max.tip': '所选数量不允许超过{0}',
    // 执行主机
    'task.region.host.tip': '请选择成员！',
    'task.region.host.optional': '可选主机',
    'task.region.host.selected': '已选主机',
    'task.region.host.filter.keyword': '请输入IP或主机名',
    'task.region.host.select.title': '选择执行主机',
    'task.process.host.ip': 'IP地址',
    'task.process.host.name': '主机名',
    'task.process.host.status': '状态',
    'task.process.host.system': '操作系统',
    'task.process.host.type': '主机类型',
    'task.process.host.container': '容器',
    'task.process.host.vm': '虚拟机',
    'task.process.host.offline': '离线',
    'task.process.host.online': '在线',
    'task.process.host.no.data.tip': '请选择执行主机',
    // 配置容灾切换项
    'task.region.related.item': '关联监控项',
    'task.region.switch.new': '配置容灾切换项',
    'task.region.switch.modify': '修改容灾切换项',
    'task.region.switch.view': '查看容灾切换项',
    'task.region.switch.related.key': '名称key',
    'task.region.switch.show.zhName': '详情页展示中文名称',
    'task.region.switch.show.enName': '详情页展示英文名称',
    'task.region.switch.value.and.member': '取值+应用成员',
    'task.region.switch.value': '取值',
    'task.region.switch.member': '应用成员',
    'task.region.switch.key.repeat': '名称Key重复，请修改',
    'task.region.switch.delete.tip': '确定删除这个容灾切换项吗？',
    'task.region.create.tip': '仅支持配置20个容灾切换项',
    'task.region.switch.no.data.tip': '请配置容灾切换项',
    // 配置容灾脚本
    'task.region.script.search.fail': '查询容灾脚本信息失败',
    'task.region.dr.switch': '容灾切换',
    'task.region.dr.switchback': '容灾回切',
    'task.region.dr.switchCheck': '切换检查',
    'task.region.dr.switch.valid.atom.error': '{0}流程中不存在原子节点，请重新选择流程！',
    'task.region.process.valid.error': '监控流程{0}中不存在原子节点，请重新选择流程！',
    // 配置流程参数
    'task.process.param': '配置流程参数',
    'task.process.modify': '修改流程参数',
    'task.process.view': '查看流程参数',
    'task.process.delete.tip': '确定删除这个流程参数？',
    'task.process.no.data.tip': '请配置流程参数',
    'task.process.param.list': '流程参数列表',
    'task.process.please.enter': '请输入参数名称',
    'task.process.name': '名称',
    'task.process.key': '关联参数Key',
    'task.process.value': '取值',
    'task.process.param.max.tip': '单个任务最多创建30个流程参数',
    // 配置用户权限
    'task.user.permission': '配置用户权限',
    'task.user.name': '用户名',
    'task.user.full.name': '用户全名',
    'task.user.admin': '配置运维管理员',
    'task.user.permission.no.data.tip': '请配置维护管理员',
    'task.user.optional': '可选用户',
    'task.user.selected': '已选用户',
    'task.user.filter.keyword': '请输入用户名',
    'task.save.success.tip': '保存任务成功',
    // 任务详情
    'view.task.detail': '任务详情',
    'view.task.info': '基本信息',
    'view.task.region': '容灾区域',
    'view.task.region.list': '区域列表',
    'view.task.member.list': '成员列表',
    'view.task.process.param': '流程参数',
    'view.task.user.permission': '用户权限',
    // 性能指标—自定义展示列
    'kpi.custom.performance.kpi': '性能指标',
    'kpi.custom.column.max.tip': '性能指标展示列最多可添加20个',
    'kpi.custom.column.modify.tip': '删除或修改展示列、类型等信息，将相应删除该展示列关联的所有信息！',
    'kpi.custom.type.kpi': '指标',
    'kpi.custom.type.threshold.one': '主指标/衍生值阈值',
    'kpi.custom.type.threshold.two': '辅助指标阈值',
    'kpi.custom.type.auxiliary': '辅助项',
    'kpi.custom.name.repeat.tip': '展示列不能重复',
    'kpi.custom.table.name': '配置自定义展示列',
    'kpi.custom.show.column': '性能指标展示',
    'kpi.custom.set.show.column': '自定义展示列',
    'kpi.custom.no.data.tip': '请配置自定义展示列',
    'kpi.custom.show.name': '展示列',
    'kpi.custom.chinese.name': '中文名称',
    'kpi.custom.english.name': '英文名称',
    'kpi.custom.type': '类型',
    // 阈值规则
    'disasterRecovery.thresholdRule.page.title': '配置阈值规则',
    'disasterRecovery.thresholdRule.page.selectTemplate': '选择模板',
    'disasterRecovery.thresholdRule.page.selectTemplate.placeholder': '-请选择模板-',
    'disasterRecovery.thresholdRule.page.rule': '规则{0}',
    'disasterRecovery.thresholdRule.page.saveAsTemplate': '另存为模板',
    'disasterRecovery.thresholdRule.page.templateName': '模板名称',
    'disasterRecovery.thresholdRule.createRule.max.error': '最多允许创建{0}条规则',
    'disasterRecovery.thresholdRule.page.selectColumn': '选择展示列',
    'disasterRecovery.thresholdRule.page.thresholdType': '阈值类型',
    'disasterRecovery.thresholdRule.page.thresholdType.fixed': '固定阈值',
    'disasterRecovery.thresholdRule.page.thresholdType.period': '同环比阈值',
    'disasterRecovery.thresholdRule.page.period.collect': '采集周期',
    'disasterRecovery.thresholdRule.page.period.day': '天',
    'disasterRecovery.thresholdRule.page.time': '时间段',
    'disasterRecovery.thresholdRule.page.health': '健康',
    'disasterRecovery.thresholdRule.page.timeInterval': '时间间隔',
    'disasterRecovery.thresholdRule.page.samplePoint': '样本点个数',
    'disasterRecovery.thresholdRule.modal.error.title': '错误',
    'disasterRecovery.thresholdRule.modal.error.time': '时间段非法',
    // 辅助信息
    'disasterRecovery.auxiliaryInfo.page.title': '配置辅助信息',
    'disasterRecovery.auxiliaryInfo.create.max': '辅助项自定义展示列配置了{0}列，最多允许创建{1}条规则辅助信息',
    'disasterRecovery.auxiliaryInfo.create.invalid': '请配置辅助项自定义展示列',
    'disasterRecovery.auxiliaryInfo': '辅助信息',
    'disasterRecovery.auxiliaryInfo.type': '类型',
    'disasterRecovery.auxiliaryInfo.time.prefix': '最近',
    'disasterRecovery.auxiliaryInfo.time.hour': '小时',
    'disasterRecovery.auxiliaryInfo.type.text': '文本',
    'disasterRecovery.auxiliaryInfo.type.formula': '计算公式',
    // 衍生值
    'kpi.auxiliary.difference': '差值百分比',
    'kpi.auxiliary.statistic': '汇聚值',
    'kpi.percent.same': '同期对比',
    'kpi.percent.previous': '上期对比',
    'kpi.same.formula.first': '（本期数据/同期数据-1）*100%',
    'kpi.same.formula.second': '（1-本期数据/同期数据）*100%',
    'kpi.previous.formula.first': '（本期数据/上期数据-1）*100%',
    'kpi.previous.formula.second': '（1-本期数据/上期数据）*100%',
    'kpi.max.val': '最大值',
    'kpi.min.val': '最小值',
    'kpi.avg.val': '平均值',
    'kpi.auxiliary.threshold.tip': '请配置衍生值阈值',
    'kpi.auxiliary.configure.title': '配置衍生值',
    'kpi.auxiliary.template': '模板',
    'kpi.auxiliary.kpi.zh': '主指标中文名称',
    'kpi.auxiliary.kpi.en': '主指标英文名称',
    'kpi.auxiliary.zh': '衍生值中文名称',
    'kpi.auxiliary.en': '衍生值英文名称',
    'kpi.auxiliary.formula': '衍生值计算类型',
    'kpi.same.sample.num': '样本点个数',
    'kpi.auxiliary.time.period': '时间间隔',
    'kpi.auxiliary.static.formula': '计算公式',
    'kpi.auxiliary.static': '统计方式',
    'kpi.auxiliary.static.time': '统计时间段',
    'kpi.auxiliary.recently': '最近',
    'kpi.auxiliary.threshold': '衍生值阈值',
    // 模板
    'kpi.template.save.as': '另存为模板',
    'kpi.template.name': '模板名称',
    'kpi.template.save': '另存为',
    // 辅助指标
    'kpi.auxiliary.title': '配置辅助指标',
    'kpi.auxiliary.no.data.tip': '请配置辅助指标！',
    'kpi.auxiliary.threshold.no.data.tip': '请确保所有辅助指标阈值已配置！',
    'kpi.auxiliary.alias.tip': '请确保所有辅助指标的中英文别名已配置！',
    'kpi.auxiliary.show.col.tip': '请确保所有辅助指标的展示列已配置！',
    // 性能指标
    'kpi.no.data.tip': '请配置性能指标！',
    'kpi.threshold.tip': '请确保所有主指标阈值已配置！',
    'kpi.table.td': '指标列表：',
    // 关联指标异常检测
    'task.basic.info.auto.switch': '是否自动切换',
    'switch.task.yes': '是',
    'switch.task.no': '否',
    'task.basic.info.switch.condition': '切换条件',
    'task.basic.info.switch.flow': '核心进程',
    'task.basic.info.switch.alarm': '严重告警',
    'task.basic.info.switch.kpi': '关键KPI',
    'task.basic.info.switch.detection': '关联异常指标',
    'detection.kpi.no.data.tip': '请关联指标异常检测指标',
    'detection.kpi.member.no.select.tip': '请先选择成员',
    'detection.kpi.per.exist.tip': '指标已存在，所属任务：{0}',
    'detection.kpi.exceed.max.tip': '一个成员所选指标不允许超过{0}',
    'detection.kpi.all.exist.tip': '指标已存在， 测量指标：{0}，测量对象：{1}，所属任务：{2}',
    'detection.kpi.task.name': '任务名称',
    'detection.kpi.select': '选择指标',
    'detection.kpi.clear.all': '清除所有',
    'detection.kpi.delete.page': '删除当前页',
    'detection.kpi.associated.title': '关联指标异常检测',
    'region.switch.flow': '流程',
    'region.switch.mml': 'MML命令',
    'region.switch.type': '切换类型',
    'region.switch.command': '任务命令',
    'switch.search.config.fail': '查询自动切换配置接口异常',
  },
  'en-us': {
    // 公共提示
    'task.common.tip': 'Info',
    'task.common.risk.tip': 'High Risk',
    'task.common.error.tip': 'Error',
    'task.common.confirm.tip': 'Confirm',
    'task.common.cancel': 'Cancel',
    'task.common.back': 'Back',
    'task.common.last.step': 'Previous',
    'task.common.next.step': 'Next',
    'task.common.confirm': 'OK',
    'task.common.submit': 'Finish',
    'task.common.modify': 'Modify',
    'task.common.please.enter': '-Enter-',
    'task.common.loading': 'Loading...',
    'task.common.no.data': 'No records found.',
    'task.please.wait': 'Please wait...',
    'task.common.search.fail': 'Query failed.',
    'task.common.delete.failure': 'Deletion failed.',
    'task.create': 'Create',
    'task.delete': 'Delete',
    'task.modify': 'Edit',
    'task.view': 'View',
    'task.common.save.fail.tip': 'Saving failed.',
    'task.common.table.operate': 'Operation',
    'task.common.total': 'Total',
    'task.common.clear.all': 'Clear',
    'task.common.close': 'Close',
    'task.common.not.configured': 'No configured',
    'task.common.configured': 'Configured',
    'task.time.day': 'Day',
    'task.time.week': 'Week',
    'task.time.month': 'Month',
    'task.time.hour': 'Hour',
    // 任务列表
    'task.tab.title': 'Task Management',
    'task.list': 'Task List',
    'task.please.input.name': 'Enter a task name.',
    'task.auto.refresh': 'Auto Refresh:',
    'task.list.name': 'Task Name',
    'task.list.region': 'DR Region',
    'task.list.execute.cycle': 'Execution Policy',
    'task.list.manager': 'Maintenance Administrator',
    'task.list.status': 'Task Status',
    'task.list.status.draft': 'Draft',
    'task.list.no.started': 'Not started',
    'task.list.activated': 'Activated',
    'task.list.paused': 'Paused',
    'task.list.last.modify.time': 'Last Modified Time',
    'task.list.query.fail': 'Failed to query the task list.',
    'task.list.view.fail': 'Failed to view the task information.',
    'task.start': 'Start',
    'task.stop': 'Pause',
    'task.start.failure': 'Failed to start the task.',
    'task.start.tip': 'Are you sure you want to start the task immediately?',
    'task.stop.failure': 'Failed to stop the task.',
    'task.stop.tip': 'The task is running. Are you sure you want to stop it?',
    'task.delete.tip': 'After the task is deleted, a monitoring item health check and Disaster switchover cannot be ' +
      'performed. Are you sure you want to delete the task?',
    'new.task': 'Create Task',
    // 配置基本信息
    'task.basic.info': 'Configure Basic Information',
    'task.basic.info.name': 'Task Name',
    'task.basic.info.desc': 'Description',
    'task.name.placeholder': 'Enter a task name.',
    'task.execute.cycle': 'Task period',
    'task.cron': 'CRON Expression',
    'task.cycle.one.minute': '1 Min',
    'task.cycle.five.minute': '5 Mins',
    'task.cycle.ten.minute': '10 Mins',
    'task.cycle.thirty.minute': '30 Mins',
    'task.cycle.custom': 'Custom',
    'task.valid.error': 'The verification interface is abnormal.',
    'task.name.repeat': 'Duplicate disaster recovery task name.',
    'task.basic.type': 'Task Type',
    'task.type.disaster': 'Intelligent Disaster Recovery',
    // 配置容灾区域
    'task.region': 'Configure DR Regions',
    'task.region.configured': 'Configured',
    'task.region.max.tip': 'A maximum of 5 DR regions can be created for a single task',
    'task.region.not.configured': 'No configured',
    'task.region.modify': 'Modify DR Region',
    'task.region.view': 'View DR Region',
    'task.region.delete.tip': 'Are you sure you want to delete the DR region?',
    'task.region.no.data.tip': 'Create a DR region.',
    'task.region.process': 'Configure the DR flow.',
    'task.region.monitor.info': 'Configure monitoring information.',
    'task.region.view.fail.tip': 'Failed to query the region information.',
    'task.region.list': 'DR Region',
    'task.region.list.member.num': 'Members',
    'task.region.list.monitor.info': 'Monitoring info',
    'task.region.list.process': 'DR Script',
    'task.region.member': 'Member',
    'task.region.nex.step.tip': 'Ensure that the monitoring information and DR scripts are configured for all ' +
      'DR regions.',
    'task.region.success.tip': 'DR regions created successfully. Do you want to configure monitoring information?',
    'task.region.member.no.data.tip': 'Please add a member in the DR region first.',
    // 配置容灾区域信息及成员
    'task.region.select.member.tip': 'Select a member.',
    'task.region.member.number.tip': 'The number of members cannot exceed 20.',
    'task.region.member.name': 'Name',
    'task.region.member.select': 'Members',
    'task.region.member.optional': 'Available',
    'task.region.member.please.enter': 'Enter a member name.',
    'task.region.member.selected': 'Selected',
    'task.region.member.home': 'Region',
    'task.region.member.select.list': 'Members',
    'task.region.member.max.tip': 'The number of members cannot exceed {0}',
    'task.region.member.delete.tip': 'This is a high-risk operation. If you delete a member, the configured monitoring ' +
      'information of the member will be cleared. If either of the associated monitoring items or DR switchover items ' +
      'is cleared, the Monitoring info of the DR region will become No configured. In this case, you need to ' +
      'configure the monitoring information again. Are you sure you want to continue?',
    'task.region.member.delete.detail.tip': 'I understand the risks and want to continue.',
    // 配置监控信息
    'task.region.monitor.alarm': 'Alarms',
    'task.region.monitor.process': 'Processes',
    'task.region.process.group.search.fail': 'Failed to query the flow group.',
    'task.region.member.list.search.fail': 'Failed to query the member list.',
    'task.region.process.fail': 'Failed to query the flow.',
    'task.region.process.detail.fail': 'Failed to query the flow details.',
    'task.region.process.group': 'Flow Group',
    'task.region.process.name': 'Flow',
    'task.region.process.detail': 'Flow Details',
    'task.region.process.nodata.tip': 'Configure the monitoring flow.',
    'task.region.process.create.max.tip': 'The number of members is {0}, a maximum of {1} processes can be created.',
    'task.region.reset.tab': 'Reset tab page',
    'task.region.search.fail': 'Failed to query the monitoring information.',
    // 配置监控项告警
    'task.region.monitor.alarm.name': 'Alarm Name',
    'task.region.monitor.alarm.id': 'Alarm ID',
    'task.region.monitor.alarm.group': 'Group Name',
    'task.region.alarm.optional': 'Available',
    'task.region.alarm.selected': 'Selected',
    'task.region.alarm.filter.keyword': 'Enter a keyword.',
    'task.region.alarm.nodata.tip': 'Configure the alarm.',
    'task.region.selected.max.tip': 'A maximum of {0} items can be selected.',
    // 执行主机
    'task.region.host.tip': 'Select a member.',
    'task.region.host.optional': 'Available',
    'task.region.host.selected': 'Selected',
    'task.region.host.filter.keyword': 'Enter an IP address or host name.',
    'task.region.host.select.title': 'Select Host',
    'task.process.host.ip': 'IP Address',
    'task.process.host.name': 'Host Name',
    'task.process.host.status': 'Status',
    'task.process.host.system': 'OS',
    'task.process.host.type': 'Host Type',
    'task.process.host.container': 'Container',
    'task.process.host.vm': 'VM',
    'task.process.host.offline': 'Offline',
    'task.process.host.online': 'Online',
    'task.process.host.no.data.tip': 'Select a host.',
    // 配置容灾切换项
    'task.region.related.item': 'Associated Monitoring Item',
    'task.region.switch.new': 'Configure DR Switchover Item',
    'task.region.switch.modify': 'Modify DR Switchover Item',
    'task.region.switch.view': 'View DR Switchover Item',
    'task.region.switch.related.key': 'Name key',
    'task.region.switch.show.zhName': 'Chinese Name',
    'task.region.switch.show.enName': 'English Name',
    'task.region.switch.value.and.member': 'Value and Application Member',
    'task.region.switch.value': 'Value',
    'task.region.switch.member': 'Application member',
    'task.region.switch.key.repeat': 'Duplicate name key. Modify it.',
    'task.region.switch.delete.tip': 'Are you sure you want to delete the DR switchover item?',
    'task.region.create.tip': 'A maximum of 20 DR switchover items can be configured.',
    'task.region.switch.no.data.tip': 'Please configure a disaster switchover item',
    // 配置容灾脚本
    'task.region.script.search.fail': 'Failed to query DR script information.',
    'task.region.dr.switch': 'DR Switchover',
    'task.region.dr.switchback': 'DR Switchback',
    'task.region.dr.switchCheck': 'Checking the Switchover',
    'task.region.dr.switch.valid.atom.error': 'The atom node does not exist in the {0} flow. Please select' +
      ' another flow.',
    'task.region.process.valid.error': 'The atom node does not exist in the monitoring flow{0}. Please select' +
      ' another flow.',
    // 配置流程参数
    'task.process.param': 'Configure Flow Parameter',
    'task.process.modify': 'Modify Flow Parameter',
    'task.process.view': 'View Flow Parameter',
    'task.process.delete.tip': 'Are you sure you want to delete the flow parameter?',
    'task.process.no.data.tip': 'Configure flow parameters.',
    'task.process.param.list': 'Flow Parameter List',
    'task.process.please.enter': 'Enter a parameter name.',
    'task.process.name': 'Name',
    'task.process.key': 'Associated Parameter Key',
    'task.process.value': 'Value',
    'task.process.param.max.tip': 'A maximum of 30 flow parameters can be created for a single task',
    // 配置用户权限
    'task.user.permission': 'Configure User Permissions',
    'task.user.name': 'User Name',
    'task.user.full.name': 'Full User Name',
    'task.user.admin': 'Configure O&M Administrator',
    'task.user.permission.no.data.tip': 'Configure a maintenance administrator.',
    'task.user.optional': 'Available',
    'task.user.selected': 'Selected',
    'task.user.filter.keyword': 'Enter a user name.',
    'task.save.success.tip': 'Task saved successfully.',
    // 任务详情
    'view.task.detail': 'Task Details',
    'view.task.info': 'Basic Information',
    'view.task.region': 'DR Region',
    'view.task.region.list': 'Region List',
    'view.task.member.list': 'Member List',
    'view.task.process.param': 'Flow Parameters',
    'view.task.user.permission': 'User Permissions',
    // 性能指标—自定义展示列
    'kpi.custom.performance.kpi': 'Performance Indicators',
    'kpi.custom.column.max.tip': 'A maximum of 20 performance indicator columns can be added.',
    'kpi.custom.column.modify.tip': 'Delete or modify the information such as the display column and type, ' +
      'all the information associated with the display column will be deleted accordingly.',
    'kpi.custom.type.kpi': 'Indicator',
    'kpi.custom.type.threshold.one': 'Primary Indicator/Derived Value Threshold',
    'kpi.custom.type.threshold.two': 'Auxiliary Indicator Threshold',
    'kpi.custom.type.auxiliary': 'Auxiliary Item',
    'kpi.custom.name.repeat.tip': 'The display column must be unique.',
    'kpi.custom.table.name': 'Configure Customized Display Columns',
    'kpi.custom.show.column': 'Performance Indicator Display',
    'kpi.custom.set.show.column': 'Customize Display Columns',
    'kpi.custom.no.data.tip': 'Configure a customized display column.',
    'kpi.custom.show.name': 'Display Column',
    'kpi.custom.chinese.name': 'Chinese Name',
    'kpi.custom.english.name': 'English name',
    'kpi.custom.type': 'Type',
    // 阈值规则
    'disasterRecovery.thresholdRule.page.title': 'Configure Threshold Rule',
    'disasterRecovery.thresholdRule.page.selectTemplate': 'Select a Template.',
    'disasterRecovery.thresholdRule.page.selectTemplate.placeholder': '-Please Select Template-',
    'disasterRecovery.thresholdRule.page.rule': 'Rule {0}',
    'disasterRecovery.thresholdRule.page.saveAsTemplate': 'Save as Template',
    'disasterRecovery.thresholdRule.page.templateName': 'Template Name',
    'disasterRecovery.thresholdRule.createRule.max.error': 'A maximum of {0} rules can be created.',
    'disasterRecovery.thresholdRule.page.selectColumn': 'Select Display Column',
    'disasterRecovery.thresholdRule.page.thresholdType': 'Threshold Type',
    'disasterRecovery.thresholdRule.page.thresholdType.fixed': 'Fixed Threshold',
    'disasterRecovery.thresholdRule.page.thresholdType.period': 'Period-on-period Threshold',
    'disasterRecovery.thresholdRule.page.period.collect': 'Collection Period',
    'disasterRecovery.thresholdRule.page.period.day': 'Day',
    'disasterRecovery.thresholdRule.page.time': 'Time Range',
    'disasterRecovery.thresholdRule.page.health': 'Health',
    'disasterRecovery.thresholdRule.page.timeInterval': 'Interval',
    'disasterRecovery.thresholdRule.page.samplePoint': 'Sample Points',
    'disasterRecovery.thresholdRule.modal.error.title': 'Error',
    'disasterRecovery.thresholdRule.modal.error.time': 'Invalid time Range.',
    // 辅助信息
    'disasterRecovery.auxiliaryInfo.page.title': 'Configure Auxiliary Information',
    'disasterRecovery.auxiliaryInfo.create.max': 'Auxiliary item customized display column config {0} column, Information can be created for a maximum of {1} auxiliary rules.',
    'disasterRecovery.auxiliaryInfo.create.invalid': 'Please configure auxiliary items customized display columns',
    'disasterRecovery.auxiliaryInfo': 'Auxiliary Info',
    'disasterRecovery.auxiliaryInfo.type': 'Type',
    'disasterRecovery.auxiliaryInfo.time.prefix': 'Last',
    'disasterRecovery.auxiliaryInfo.time.hour': 'hours',
    'disasterRecovery.auxiliaryInfo.type.text': 'Text',
    'disasterRecovery.auxiliaryInfo.type.formula': 'Formula',
    // 衍生值
    'kpi.auxiliary.difference': 'Difference Percentage',
    'kpi.auxiliary.statistic': 'Aggregated Value',
    'kpi.percent.same': 'Same Period Comparison',
    'kpi.percent.previous': 'Last Period Comparison',
    'kpi.same.formula.first': '（Current Period Data/Contemporaneous data - 1）*100%',
    'kpi.same.formula.second': '（1 - Current Period Data/Contemporaneous data）*100%',
    'kpi.previous.formula.first': '（Current Period Data/Last Period Data - 1）*100%',
    'kpi.previous.formula.second': '（1 - Current Period Data/Last Period Data）*100%',
    'kpi.max.val': 'Maximum',
    'kpi.min.val': 'Minimum',
    'kpi.avg.val': 'Average',
    'kpi.auxiliary.threshold.tip': 'Configure the derivative value threshold.',
    'kpi.auxiliary.configure.title': 'Configure Derivative Value',
    'kpi.auxiliary.template': 'Template',
    'kpi.auxiliary.kpi.zh': 'Main indicator (Zh)',
    'kpi.auxiliary.kpi.en': 'Main indicator (En)',
    'kpi.auxiliary.zh': 'Derivative value (Zh)',
    'kpi.auxiliary.en': 'Derivative value (En)',
    'kpi.auxiliary.formula': 'Derivative value type',
    'kpi.same.sample.num': 'Sample points',
    'kpi.auxiliary.time.period': 'Interval',
    'kpi.auxiliary.static.formula': 'Formula',
    'kpi.auxiliary.static': 'Statistical Method',
    'kpi.auxiliary.static.time': 'Statistical Period',
    'kpi.auxiliary.recently': 'Last',
    'kpi.auxiliary.threshold': 'Derivative Value Threshold',
    // 模板
    'kpi.template.save.as': 'Save as Template',
    'kpi.template.name': 'Template Name',
    'kpi.template.save': 'Save As',
    // 辅助指标
    'kpi.auxiliary.title': 'Configure Auxiliary Indicator',
    'kpi.auxiliary.no.data.tip': 'Configure auxiliary indicators.',
    'kpi.auxiliary.threshold.no.data.tip': 'Ensure that all auxiliary indicator thresholds are configured.',
    'kpi.auxiliary.alias.tip': 'Ensure that aliases are configured for all auxiliary indicators.',
    'kpi.auxiliary.show.col.tip': 'Ensure that the display columns are configured for all auxiliary indicators.',
    // 性能指标
    'kpi.no.data.tip': 'Configure performance indicators.',
    'kpi.threshold.tip': 'Ensure that all main indicator thresholds are configured.',
    'kpi.table.td': 'Indicator List:',
    // 关联指标异常检测
    'task.basic.info.auto.switch': 'Automatic Switchover',
    'switch.task.yes': 'Yes',
    'switch.task.no': 'No',
    'task.basic.info.switch.condition': 'Switching Conditions',
    'task.basic.info.switch.flow': 'Core Processes',
    'task.basic.info.switch.alarm': 'Critical Alarm',
    'task.basic.info.switch.kpi': 'KPIs',
    'task.basic.info.switch.detection': 'Associated Abnormal KPIs',
    'detection.kpi.no.data.tip': 'Please associate an indicator exception detection indicator.',
    'detection.kpi.member.no.select.tip': 'Select a member first.',
    'detection.kpi.per.exist.tip': 'The indicator already exists. Task: {0}',
    'detection.kpi.exceed.max.tip': 'The number of indicators selected by a member cannot exceed {0}.',
    'detection.kpi.all.exist.tip': 'The indicator already exists. Measurement Indicator: {0}, Measurement object: {1}, Task: {2}',
    'detection.kpi.task.name': 'Task Name',
    'detection.kpi.select': 'Select Indicator',
    'detection.kpi.clear.all': 'Clear All',
    'detection.kpi.delete.page': 'Delete By Page',
    'detection.kpi.associated.title': 'Associated Indicator Abnormality Detection',
    'region.switch.flow': 'Flow',
    'region.switch.mml': 'MML Commands',
    'region.switch.type': 'Handover Type',
    'region.switch.command': 'Task Command',
    'switch.search.config.fail': 'An error occurred when querying the automatic switchover configuration interface.',
  },
};
