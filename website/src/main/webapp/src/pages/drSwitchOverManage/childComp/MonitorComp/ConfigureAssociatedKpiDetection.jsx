/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
import React, { useContext, useEffect, useRef, useState, useReducer, useImperativeHandle } from 'react';
import { Text<PERSON>utton, Toggle, Row, Col, SelectPro, TextField, Table, Select, Button, Loader } from 'eview-ui';
import LabelHint from '@comp/LabelHint';
import { $t, modal, validate, pages } from '@util';
import { NULL_DEFAULT } from '@const/common';
import { queryMember } from '../../utils/page';
import * as api from '../../api';
import {
  TASK_KPI_DETECTION_TABLE_COLUMNS, MEASURE_TYPE, MEASURE_TYPE_MAPS, KPI_TABLE_COLUMNS, OPERATE_SUCCESS_CODE,
  SELECTED_KPI_TABLE_COLUMNS, TASK_PAGE_TYPE, MAX_INDICATOR_SWITCH, SELECTED_KPI_TABLE_COLUMNS_VIEW,
} from '../../const';
import { monitorContext } from '../../reducer/monitor';

const ConfigureAssociatedKpiDetection = ({ groupId, taskPageType, isNew }) => {
  const [state, dispatch] = useContext(monitorContext);
  const { detectionTaskListPar, indicatorTablePar, isRefreshByKpi } = state;
  const [isLoadingShow, setIsLoadingShow] = useState(false);// 成员查询遮罩层
  const taskSearchRef = useRef();
  const eleSearchRef = useRef();
  const indicatorTableCache = useRef({});

  useEffect(() => {
    indicatorTableCache.current = { ...indicatorTablePar };
  }, [indicatorTablePar]);

  useEffect(() => {
    // 修改状态下，已选指标表格回显数据
    if (isRefreshByKpi && !isNew) {
      let rows = indicatorTablePar.selectedKpiTableAllData.map(item => ({
        ...item,
        operate: <TextButton text={$t('task.delete')} onClick={() => deleteSingleIndicator(item.keyId)} />,
      }));
      let data = {
        selectedKpiTableAllData: rows,
        selectedKpiTableList: rows.slice((indicatorTablePar.selectedKpiPageIndex - 1) * indicatorTablePar.selectedKpiPageSize,
          indicatorTablePar.selectedKpiPageIndex * indicatorTablePar.selectedKpiPageSize),
      };
      indicatorTableCache.current = { ...indicatorTableCache.current, ...data };
      setIndicatorTableParam(data);
    }

    if (isRefreshByKpi) {
      setIsLoadingShow(true);
      queryMember(groupId, (memberList) => {
        let _memberInfo = memberList.length ? memberList[0] : {};
        let data = { memberOptions: memberList, memberInfo: indicatorTablePar.memberInfo ?? _memberInfo };
        indicatorTableCache.current = { ...indicatorTableCache.current, ...data };
        setIndicatorTableParam(data);
      }, err => setIsLoadingShow(false));
    }
  }, []);

  useEffect(() => {
    if (isRefreshByKpi && verify(taskSearchRef.current)) {
      queryTaskList();
    }
  }, [detectionTaskListPar.autoRefreshTimeStamp, detectionTaskListPar.pageIndex]);

  useEffect(() => {
    if (isRefreshByKpi && detectionTaskListPar.checkedRows.length && verify(eleSearchRef.current)) {// 选中了任务才能触发查询
      getIndicatorList();
    }
  }, [
    detectionTaskListPar.checkedRows, indicatorTablePar.pageIndex, indicatorTablePar.memberInfo,
    indicatorTablePar.autoRefreshTimeStamp]);

  // 查询数据源类型为默认的指标异常检测任务
  const queryTaskList = () => {
    setDetectionTaskListParam({ isLoadingShow: true, emptyTableMsg: $t('task.common.loading') });
    api.queryDetectionTask({
      taskName: detectionTaskListPar.taskName,
      paging: {
        pageSize: detectionTaskListPar.pageSize, pageNumber: detectionTaskListPar.pageIndex, sortField: '', sortType: '',
      },
    }, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        setDetectionTaskListParam({ isLoadingShow: false, taskList: [], emptyTableMsg: $t('task.common.search.fail') });
        return;
      }

      let taskSelections = [];
      if (res.data?.rows.length) {
        taskSelections = res.data.rows.map(item => ({ taskName: item.taskName, taskId: item.taskId.toString() }));
      }

      setDetectionTaskListParam({
        taskList: taskSelections,
        recordCount: res.data?.total || 0,
        isLoadingShow: false,
        emptyTableMsg: $t('task.common.no.data'),
      });
    }, () => {
      setDetectionTaskListParam({ isLoadingShow: false, taskList: [], emptyTableMsg: $t('task.common.search.fail') });
    });
  };

  // 查询对应任务和成员下的指标列表
  const getIndicatorList = () => {
    setIndicatorTableParam({ isLoadingShow: true, emptyTableMsg: $t('task.common.loading') });
    api.queryIndicatorList({
      dn: indicatorTablePar.memberInfo?.dn,
      searchScope: indicatorTablePar.measureType,
      searchKey: indicatorTablePar.measureSearch,
      taskId: detectionTaskListPar.selectedTaskId,
      paging: {
        pageSize: indicatorTablePar.pageSize, pageNumber: indicatorTablePar.pageIndex, sortField: '', sortType: '',
      },
    }, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        setIndicatorTableParam({ isLoadingShow: false, tableList: [], emptyTableMsg: $t('task.common.search.fail') });
        return;
      }

      let rows = [];
      if (res.data?.rows) {
        rows = res.data.rows.map(item => ({
          keyId: `${item.taskId}(_)${item.indicatorId}`,
          indicatorId: item.indicatorId,
          dnName: item.dnName,
          indexName: item.indexName,
          measUnitName: item.measUnitName,
          displayValue: item.displayValue,
          otherInfo: {
            associationTaskId: item.taskId,
            measUnitKey: item.measUnitKey,
            measTypeKey: item.measTypeKey,
            originalValue: item.originalValue,
            dn: item.dn,
            hasMeasObj: item.hasMeasObj,
            indexId: item.indexId,
            groupId: indicatorTablePar.memberInfo?.groupId,
            groupMemberId: indicatorTablePar.memberInfo?.value,
          },
        }));
      }
      setIndicatorTableParam({
        tableList: rows,
        recordCount: res.data?.total || 0,
        checkedRows: getCheckedRows(indicatorTablePar.selectedKpiTableAllData),
        isLoadingShow: false,
        emptyTableMsg: $t('task.common.no.data'),
      });
    }, () => {
      setIndicatorTableParam({ isLoadingShow: false, tableList: [], emptyTableMsg: $t('task.common.search.fail') });
    });
  };

  // 单选任务过滤成员下的指标列表
  const handleCheck = (row, checkedRows) => {
    if (!indicatorTablePar.memberInfo?.dn) {
      setDetectionTaskListParam({ checkedRows: detectionTaskListPar.checkedRows });
      modal.info($t('task.common.tip'), $t('detection.kpi.member.no.select.tip'));
      return;
    }

    if (eleSearchRef.current && !eleSearchRef.current.validate()) {
      eleSearchRef.current.focus();
      setDetectionTaskListParam({ checkedRows: detectionTaskListPar.checkedRows });
      return;
    }

    setDetectionTaskListParam({ checkedRows, selectedTaskId: row.id, selectedTaskName: row._org?.taskName }, true);
    setIndicatorTableParam({ pageIndex: 1 }, true);
  };

  // 更新任务列表公共方法
  const setDetectionTaskListParam = (param, isRefreshByKpi = false) => {
    dispatch({
      type: 'setDetectionTaskListPar',
      detectionTaskListPar: { ...detectionTaskListPar, ...param },
      isRefreshByKpi,
    });
  };

  // 更新任务和成员过滤后的指标列表公共方法
  const setIndicatorTableParam = (param, isRefreshByKpi = false) => {
    dispatch({
      type: 'setIndicatorTablePar',
      indicatorTablePar: { ...indicatorTableCache.current, ...param },
      isRefreshByKpi,
    });
  };

  // 根据任务名称过滤
  const onFilterByTaskName = (event, current) => {
    // 13: 键盘enter键
    if ((event.keyCode === 13 || event.type === 'click') && verify(taskSearchRef.current)) {
      // 改变时间去驱使加载当前页码的任务列表
      setDetectionTaskListParam({ pageIndex: 1, autoRefreshTimeStamp: new Date().getTime() }, true);
    }
  };

  const onFilterByIndicatorInfo = (event) => {
    // 13: 键盘enter键
    if ((event.keyCode === 13 || event.type === 'click') && verify(eleSearchRef.current)) {
      // 改变时间去驱使加载当前页码的任务列表
      setIndicatorTableParam({ pageIndex: 1, autoRefreshTimeStamp: new Date().getTime() }, true);
    }
  };

  // 过滤框校验
  const verify = (currentObj) => {
    if (currentObj && !currentObj?.validate()) {
      currentObj.focus();
      return false;
    }
    return true;
  };

  const isLimitByMember = (selectedTableData) => {
    let filterLists = selectedTableData.filter(item => item.member === indicatorTablePar.memberInfo?.dn);
    return filterLists.length;
  };

  // 表格分页回调
  const onPageChange = (pageIndex, currentObj, setFn) => {
    setFn({ pageIndex }, true);
  };

  // 指标列表单选
  const handleIndicatorCheck = (row, checkedRows, event) => {
    if (row.checked && event.shiftKey) {// 屏蔽shift单击多选表格
      setIndicatorTableParam({ checkedRows: indicatorTablePar.checkedRows });
      return;
    }

    let selectedTableData = [...indicatorTablePar.selectedKpiTableAllData];
    if (row.checked) {// 选中
      let repeatIndex = selectedTableData.findIndex(item => item.indicatorId === row._org.indicatorId);
      if (repeatIndex !== -1) {// 指标重复
        let _row = selectedTableData[repeatIndex];
        modal.info($t('task.common.tip'), $t('detection.kpi.per.exist.tip', [_row.taskName]));
        setIndicatorTableParam({ checkedRows: indicatorTablePar.checkedRows });
        return;
      }

      if (isLimitByMember(indicatorTablePar.selectedKpiTableAllData) >= MAX_INDICATOR_SWITCH) {
        modal.info($t('task.common.tip'), $t('detection.kpi.exceed.max.tip', [MAX_INDICATOR_SWITCH]));
        setIndicatorTableParam({ checkedRows: indicatorTablePar.checkedRows });
        return;
      }

      selectedTableData.push({
        ...row._org,
        memberName: indicatorTablePar.memberInfo?.text,
        member: indicatorTablePar.memberInfo?.dn,
        taskName: detectionTaskListPar.selectedTaskName,
        operate: <TextButton text={$t('task.delete')} onClick={() => deleteSingleIndicator(row._org.keyId)} />,
      });
    } else {// 取消勾选
      let repeatIndex = selectedTableData.findIndex(item => item.keyId === row.id);
      selectedTableData.splice(repeatIndex, 1);
    }

    let currentPageIndex = row.checked ? indicatorTablePar.selectedKpiPageIndex : 1;
    updateIndicatorTableInfo(selectedTableData, currentPageIndex);
  };

  // 指标列表全选
  const handleAllCheck = (checkedRows) => {
    let selectedTableData = [...indicatorTablePar.selectedKpiTableAllData];
    if (checkedRows.length) {// 选中
      for (let i = 0; i < checkedRows.length; i++) {
        let findObj = selectedTableData.find(item => checkedRows[i].endsWith(`(_)${item.indicatorId}`));
        if (findObj && findObj.taskName !== detectionTaskListPar.selectedTaskName) {// 指标重复
          modal.info($t('task.common.tip'), $t('detection.kpi.all.exist.tip', [findObj.indexName, findObj.displayValue || NULL_DEFAULT, findObj.taskName]));
          setIndicatorTableParam({ checkedRows: indicatorTablePar.checkedRows });
          return;
        }

        if (isLimitByMember(selectedTableData) >= MAX_INDICATOR_SWITCH) {
          modal.info($t('task.common.tip'), $t('detection.kpi.exceed.max.tip', [MAX_INDICATOR_SWITCH]));
          setIndicatorTableParam({ checkedRows: indicatorTablePar.checkedRows });
          return;
        }

        if (!findObj) {// 不重复
          let rowObj = indicatorTablePar.tableList.find(item => item.keyId === checkedRows[i]);
          selectedTableData.push({
            ...rowObj,
            memberName: indicatorTablePar.memberInfo?.text,
            member: indicatorTablePar.memberInfo?.dn,
            taskName: detectionTaskListPar.selectedTaskName,
            operate: <TextButton text={$t('task.delete')} onClick={() => deleteSingleIndicator(rowObj.keyId)} />,
          });
        }
      }
    } else {// 取消勾选
      indicatorTablePar.tableList.forEach(_row => {
        let repeatIndex = selectedTableData.findIndex(item => item.keyId === _row.keyId);
        if (repeatIndex !== -1) {
          selectedTableData.splice(repeatIndex, 1);
        }
      });
    }

    let currentPageIndex = checkedRows.length ? indicatorTablePar.selectedKpiPageIndex : 1;
    updateIndicatorTableInfo(selectedTableData, currentPageIndex);
  };

  // 已选指标表格分页回调
  const handleSelectedKpiPageChange = ({ pageIndex, pageSize }) => {
    let rows = [...indicatorTablePar.selectedKpiTableAllData];
    setIndicatorTableParam({
      selectedKpiTableAllData: rows,
      selectedKpiTableList: rows.slice((pageIndex - 1) * pageSize, pageIndex * pageSize),
      selectedKpiPageSize: pageSize,
      selectedKpiPageIndex: pageIndex,
    });
  };

  // 已选指标删除
  const deleteSingleIndicator = (keyId) => {
    const { selectedKpiTableAllData, selectedKpiTableList, selectedKpiPageIndex } = indicatorTableCache.current;
    let selectedTableData = [...selectedKpiTableAllData];
    let repeatIndex = selectedTableData.findIndex(item => item.keyId === keyId);
    selectedTableData.splice(repeatIndex, 1);

    let currentPageIndex = (selectedKpiTableList.length === 1 && selectedKpiPageIndex !== 1) ?
      (selectedKpiPageIndex - 1) : selectedKpiPageIndex;
    updateIndicatorTableInfo(selectedTableData, currentPageIndex);
  };

  // 更新已选指标表格及可选指标勾选状态
  const updateIndicatorTableInfo = (selectedTableData, pageIndex) => {
    const { selectedKpiPageSize } = indicatorTableCache.current;
    setIndicatorTableParam({
      checkedRows: getCheckedRows(selectedTableData),
      selectedKpiTableAllData: selectedTableData,
      selectedKpiTableList: selectedTableData.slice((pageIndex - 1) * selectedKpiPageSize,
        pageIndex * selectedKpiPageSize),
      selectedKpiPageIndex: pageIndex,
    });
  };

  // 根据已选指标列表反推当前任务和成员下的指标表格勾选数组
  const getCheckedRows = (selectedTableData) => {
    const { memberInfo } = indicatorTableCache.current;
    let checkedRows = selectedTableData.filter(item => {
      return (item.member === memberInfo?.dn && item.taskName === detectionTaskListPar.selectedTaskName);
    }).map(item => item.keyId);
    return checkedRows;
  };

  // 删除当前页
  const clearCurrentPage = () => {
    const { selectedKpiTableAllData, selectedKpiTableList, selectedKpiPageIndex } = indicatorTableCache.current;
    if (!selectedKpiTableList.length) {
      return;
    }

    let selectedTableData = [...selectedKpiTableAllData];
    let currentPageRowKeys = selectedKpiTableList.map(item => item.keyId);
    let findIndex = selectedTableData.findIndex(item => currentPageRowKeys.includes(item.keyId));
    while (findIndex !== -1) {
      selectedTableData.splice(findIndex, 1);
      findIndex = selectedTableData.findIndex(item => currentPageRowKeys.includes(item.keyId));
    }
    let currentPageIndex = selectedKpiPageIndex === 1 ? 1 : selectedKpiPageIndex - 1;
    updateIndicatorTableInfo(selectedTableData, currentPageIndex);
  };

  // 清除所有
  const clearAll = () => {
    setIndicatorTableParam({
      checkedRows: [],
      selectedKpiTableList: [],
      selectedKpiPageSize: 50,
      selectedKpiPageIndex: 1,
      selectedKpiRecordCount: 0,
      selectedKpiTableAllData: [],
    });
  };

  return (
    <div style={{ marginTop: '24px', marginBottom: '24px' }}>
      {
        taskPageType !== TASK_PAGE_TYPE.VIEW &&
        <>
          <Row style={{ marginBottom: '24px' }}>
            <Col cols={24}>
              <ul className="ulCls">
                <li className="left-column">
                  <LabelHint label={$t('detection.kpi.select')} require={false} isHelp={false} />
                </li>
                <li className="middle-column">
                  <div style={{ marginBottom: '5px', width: '100%' }}>
                    <div className="df-input-frame" style={{ position: 'relative' }}>
                      <TextField
                        inputStyle={{ paddingRight: '28px', width: '100%' }} hintType='tip'
                        value={detectionTaskListPar.taskName}
                        placeholder={$t('detection.kpi.task.name')}
                        onChange={val => setDetectionTaskListParam({ taskName: val })}
                        tipDuration={6000}
                        autoComplete="off" style={{ width: '100%' }}
                        validator={(val, id) => validate(['cmpValidChar', 'checkLength'], val, id, null, 200)}
                        ref={(ele) => taskSearchRef.current = ele}
                        onKeyDown={onFilterByTaskName}
                      />
                      <div className="df-icon-search" onClick={event => onFilterByTaskName(event)} />
                    </div>
                  </div>
                  <div className='measure_border' style={{ overflow: 'hidden', borderTop: 'unset', height: '23.1rem' }}>
                    <Table
                      enablePagination
                      pageSize={detectionTaskListPar.pageSize}
                      currentPage={detectionTaskListPar.pageIndex}
                      recordCount={detectionTaskListPar.recordCount}
                      splitPagination={true}
                      columns={TASK_KPI_DETECTION_TABLE_COLUMNS}
                      showEmptyImage={false}
                      emptyTableMsg={detectionTaskListPar.emptyTableMsg}
                      keyIndex={0}
                      pagingType="select"
                      checkType="single"
                      enableCheckBox={true}
                      onRowCheck={handleCheck}
                      checkedRows={detectionTaskListPar.checkedRows}
                      onPageChange={pageIndex => onPageChange(pageIndex, taskSearchRef, setDetectionTaskListParam)}
                      dataset={detectionTaskListPar.taskList}
                    />
                  </div>
                </li>
                <li className="right-column">
                  <div style={{ overflow: 'hidden' }}>
                    <div style={{ float: 'left', marginRight: '10px' }}>
                      <SelectPro options={indicatorTablePar.memberOptions} style={{ width: '250px' }}
                        value={indicatorTablePar.memberInfo?.value}
                        hintType="tip" required={true}
                        onChange={(val, options) => setIndicatorTableParam({ memberInfo: options, pageIndex: 1 }, true)}
                      />
                    </div>
                    <Select options={MEASURE_TYPE} style={{ float: 'left', marginRight: '5px' }}
                      selectStyle={{ width: '200px' }}
                      value={indicatorTablePar.measureType}
                      onChange={val => setIndicatorTableParam({ measureType: val })}
                    />
                    <div style={{ float: 'left', width: 'calc(100% - 465px)' }}>
                      <div className="df-input-frame" style={{ position: 'relative' }}>
                        <TextField
                          inputStyle={{ paddingRight: '28px', width: '100%' }} hintType='tip'
                          value={indicatorTablePar.measureSearch} id="unitInput"
                          placeholder={MEASURE_TYPE_MAPS[indicatorTablePar.measureType]}
                          onChange={val => setIndicatorTableParam({ measureSearch: val })}
                          tipDuration={2000}
                          autoComplete="off" style={{ width: '100%' }}
                          validator={(val, id) => validate(['resourceValidChar', 'checkLength'], val, id, null, 600)}
                          ref={(ele) => eleSearchRef.current = ele}
                          onKeyDown={event => onFilterByIndicatorInfo(event)}
                        />
                        <div className="df-icon-search"
                          onClick={event => onFilterByIndicatorInfo(event)}
                        />
                      </div>
                    </div>
                  </div>
                  <div className='measure_border' style={{ overflow: 'hidden', borderTop: 'unset', height: '23.1rem' }}>
                    <Table
                      enablePagination
                      pageSize={indicatorTablePar.pageSize}
                      currentPage={indicatorTablePar.pageIndex}
                      recordCount={indicatorTablePar.recordCount}
                      splitPagination={true}
                      columns={KPI_TABLE_COLUMNS}
                      showEmptyImage={false}
                      keyIndex={0}
                      enableCheckBox={true}
                      pagingType="select"
                      onRowCheck={handleIndicatorCheck}
                      onHeaderCheck={handleAllCheck}
                      checkedRows={indicatorTablePar.checkedRows}
                      onPageChange={pageIndex => onPageChange(pageIndex, eleSearchRef, setIndicatorTableParam)}
                      dataset={indicatorTablePar.tableList}
                    />
                  </div>
                </li>
              </ul>
            </Col>
          </Row>
          <Row style={{ marginBottom: '24px' }}>
            <Col cols={24}>
              <Button text={$t('detection.kpi.clear.all')} style={{ float: 'right' }}
                onClick={clearAll}
              />
              <Button text={$t('detection.kpi.delete.page')} style={{ float: 'right', marginRight: '15px' }}
                onClick={clearCurrentPage}
              />
            </Col>
          </Row>
        </>
      }
      <Row>
        <Col cols={24}>
          <div style={{ marginLeft: `${taskPageType !== TASK_PAGE_TYPE.VIEW ? '160px' : '0'}` }}>
            <Table
              enablePagination
              pageSize={indicatorTablePar.selectedKpiPageSize}
              currentPage={indicatorTablePar.selectedKpiPageIndex}
              recordCount={indicatorTablePar.selectedKpiTableAllData.length}
              splitPagination={true}
              columns={taskPageType !== TASK_PAGE_TYPE.VIEW ? SELECTED_KPI_TABLE_COLUMNS : SELECTED_KPI_TABLE_COLUMNS_VIEW}
              showEmptyImage={false}
              maxHeight={taskPageType !== TASK_PAGE_TYPE.VIEW ? 400 : 520}
              keyIndex={0}
              pageSizeOptions={[50, 100, 200]}
              onPageChange={pageIndex => handleSelectedKpiPageChange({
                pageIndex, pageSize: indicatorTablePar.selectedKpiPageSize,
              })}
              onPageSizeChange={pageSize => handleSelectedKpiPageChange({ pageSize, pageIndex: 1 })}
              dataset={indicatorTablePar.selectedKpiTableList}
            />
          </div>
        </Col>
      </Row>
      <Loader type="global" isOpen={isLoadingShow || detectionTaskListPar.isLoadingShow || indicatorTablePar.isLoadingShow} desc={$t('task.please.wait')} />
    </div>
  );
};

export default ConfigureAssociatedKpiDetection;
