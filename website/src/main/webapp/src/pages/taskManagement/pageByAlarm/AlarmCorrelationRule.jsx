/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

import React, { useState, useEffect, useRef } from 'react';
import { Col, Table, Row, Tree, Dialog, Loader } from 'eview-ui';
import { $t, validate, modal } from '@util';
import * as api from '../api';
import SearchInput from '@comp/SearchInput';
import { ALARM_OPTIONAL_COLUMNS, ALARM_SELECTED_COLUMNS } from '../const/alarmCorrelationRule';
import { OPERATE_SUCCESS_CODE } from '../const';
import { ARRAY_INDEX_3, ARRAY_INDEX_4, ARRAY_LEN_FOUR, ARRAY_LEN_THREE } from '@const/common';

let alarmRuleTree;
let ruleNameSearch;
let selectedTable;
let radioList = {};
const AlarmCorrelationRule = ({ drawerTaskId, createRuleCallback, isAlarmRuleDialogOpen }) => {
  const [alarmRulePar, setAlarmRulePar] = useState({
    alarmRuleTreeData: [{
      id: '/',
      type: 'motypeGroup',
      children: [],
      text: $t('alarm.correlation.rule.by.ne'),
    }],
    ruleName: '',
    checkedKeys: [],
    expandedKeys: ['/'],
    optionalPageResults: [],
    moType: '',
    optionalRecordCount: 0,
    selectedRecordCount: 0,
    pageSize: 10,
    pageIndex: 1,
    pageResults: [],
    selectedPageResults: [],
    optionalCheckedRows: [],
    isOpen: false,
    radioParam: {},
    isLoadingShow: false,
    ruleTableKey: 0,
    isFilterDisplay: false,
  });
  const alarmRuleCurrent = useRef({});
  const checkedRootAlarm = useRef('');

  useEffect(() => {
    alarmRuleCurrent.current = { ...alarmRulePar };
  }, [alarmRulePar]);

  // 按网元类型分组查询树
  useEffect(() => {
    // 查询接口前启动正在加载提示
    setAlarmRulePar({ ...alarmRulePar, isLoadingShow: true });

    let treeData = [...alarmRulePar.alarmRuleTreeData];
    api.getAlarmTree({}, res => {
      // 操作失败
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        setAlarmRulePar({ ...alarmRulePar, isLoadingShow: false });
        modal.error($t('kpi.task.common.error.tip'), $t('alarm.correlation.rule.delete.fail'));
        return;
      }

      if (res.data) {
        treeData[0].children = res.data.map(item => ({
          ...item, children: [],
          text: item.displayType,
        }));
        setAlarmRulePar({ ...alarmRulePar, alarmRuleTreeData: treeData, isLoadingShow: false });
      }
    }, err => {
      setAlarmRulePar({ ...alarmRulePar, isLoadingShow: false });
    });
  }, []);

  // 点击左侧树查询相应告警信息
  const alarmOnSelect = (selectedKeys, node, event) => {
    // 查询接口前启动正在加载提示
    setAlarmRulePar({ ...alarmRulePar, isLoadingShow: true });
    api.getAlarmStaticInfo({
      moType: node.props.type,
    }, res => {
      // 操作失败
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        setAlarmRulePar({ ...alarmRulePar, isLoadingShow: false });
        modal.error($t('kpi.task.common.error.tip'), $t('alarm.correlation.rule.query.info.fail'));
        return;
      }

      setAlarmRulePar({
        ...alarmRulePar,
        optionalPageResults: res.data,
        moType: node.props.type,
        optionalRecordCount: res.data.length,
        pageIndex: 1,
        pageResults: res.data.slice(0, alarmRulePar.pageSize),
        isLoadingShow: false,
        isFilterDisplay: true,
        ruleTableKey: new Date().getTime(),
      });
    }, err => {
      setAlarmRulePar({ ...alarmRulePar, isLoadingShow: false });
    });
  };

  // 原生dom点击获取不到最新数据，需结合useRef
  const deleteTask = (alarmId) => {
    // 如果删除的是根因告警，则标志位恢复初始值
    if (alarmId === checkedRootAlarm.current) {
      checkedRootAlarm.current = '';
    }

    // 过滤出非删除表格行的其他数据
    let selectedTableData = selectedTable.getDataset();
    let restCheckedArr = selectedTableData.filter(item => item[0] !== alarmId);

    let rows = [];
    restCheckedArr.forEach((item, index) => {
      rows.push([
        item[0], item[1], item[2],
        item[ARRAY_INDEX_3], item[ARRAY_INDEX_4], getCustomRadio(item[0], index), getCustomOperation(item[0]),
      ]);
    });

    let alarmRuleData = {
      ...alarmRuleCurrent.current,
      selectedPageResults: restCheckedArr,
      optionalCheckedRows: getCheckedRows(restCheckedArr),
      selectedRecordCount: restCheckedArr.length,
    };
    setAlarmRulePar(alarmRuleData);
  };

  const handleCheck = (row, checkedRows) => {
    if (row.checked) { // 选中
      let selectedTableData = selectedTable.getDataset();

      // 至多只能选择两条数据
      if (selectedTableData.length > 1) {
        modal.info($t('kpi.task.common.tip'), $t('alarm.correlation.rule.max.two.alarms'));

        // 更新数据
        setAlarmRulePar({
          ...alarmRulePar,
          optionalCheckedRows: checkedRows.filter(item => item !== row.id),
        });
        return;
      }

      let res = alarmRulePar.pageResults[row.rowIndex];
      selectedTableData.push([
        res.alarmId, res.alarmGroupId, res.alarmName, res.alarmGroupName, res.eventType,
        getCustomRadio(res.alarmId, selectedTableData.length), getCustomOperation(res.alarmId),
      ]);

      // 更新数据
      setAlarmRulePar({
        ...alarmRulePar,
        selectedPageResults: selectedTableData,
        optionalCheckedRows: checkedRows,
        selectedRecordCount: selectedTableData.length,
      });
    } else {// 取消选中
      deleteTask(row.id);
    }
  };

  // 根据已选列表反推可选列表的选中数据
  const getCheckedRows = (restCheckedArr) => {
    return restCheckedArr.map(item => item[0]);
  };

  const getCustomOperation = (alarmId) => {
    return (
      <ul id='task_operate'>
        <li>
          <div title={$t('alarm.correlation.rule.delete')} style={{ color: 'rgb(24, 111, 194)' }}
            onClick={() => deleteTask(alarmId)}
          >{$t('alarm.correlation.rule.delete')}
          </div>
        </li>
      </ul>
    );
  };

  const searchAlarmList = (event) => {
    // 13: 键盘enter键
    if (event.keyCode === 13 || event.type === 'click') {
      if (ruleNameSearch && !ruleNameSearch?.validate()) {
        ruleNameSearch.focus();
        return;
      }

      setAlarmRulePar({ ...alarmRulePar, isLoadingShow: true });
      api.filterAlarmStaticInfo({
        moType: alarmRulePar.moType,
        searchKey: ruleNameSearch.getValue(),
      }, res => {
        // 操作失败
        if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
          setAlarmRulePar({ ...alarmRulePar, isLoadingShow: false });
          modal.error($t('kpi.task.common.error.tip'), $t('alarm.correlation.rule.filter.info.fail'));
          return;
        }

        setAlarmRulePar({
          ...alarmRulePar,
          optionalPageResults: res.data,
          pageResults: res.data.slice(0, alarmRulePar.pageSize),
          optionalRecordCount: res.data.length,
          isLoadingShow: false,
          pageIndex: 1,
        });
      }, err => {
        setAlarmRulePar({ ...alarmRulePar, isLoadingShow: false });
      });
    }
  };

  const getCustomRadio = (alarmId, index) => {
    return (
      <input type="radio" name='root' onChange={() => handleRadioCheck(alarmId, index)}
        ref={ele => radioList[`radio_${alarmId}`] = ele}
        checked={alarmId === checkedRootAlarm.current}
      />
    );
  };

  const handleRadioCheck = (alarmId, rootIndex) => {
    checkedRootAlarm.current = alarmId;

    let selectedTableData = selectedTable.getDataset();
    let rows = [];
    selectedTableData.forEach((item, index) => {
      rows.push([
        item[0], item[1], item[2],
        item[ARRAY_LEN_THREE], item[ARRAY_LEN_FOUR], getCustomRadio(item[0], index), getCustomOperation(item[0]),
      ]);
    });

    // 更新数据
    setAlarmRulePar({
      ...alarmRuleCurrent.current,
      selectedPageResults: rows,
    });
  };

  // 创建告警关联规则
  const createAlarmRule = () => {
    let selectedTableData = selectedTable.getDataset();
    if (selectedTableData.length < 2) {
      modal.info($t('kpi.task.common.tip'), $t('alarm.correlation.rule.at.least.two.alarms'));
      return;
    }
    if (!$('input:radio[name="root"]:checked').length) {
      modal.info($t('kpi.task.common.tip'), $t('alarm.correlation.rule.specify.a.root.alarm'));
      return;
    }

    // 将两条可选告警数据拼凑成一条数据作为接口入参
    let alarmCorrelationData = {};
    selectedTableData.forEach((item, index) => {
      if (item[0] === checkedRootAlarm.current) {
        alarmCorrelationData.rootAlarmId = item[0];
        alarmCorrelationData.rootAlarmName = item[2];
        alarmCorrelationData.rootAlarmGroupId = item[1];
      } else {
        alarmCorrelationData.childAlarmId = item[0];
        alarmCorrelationData.childAlarmName = item[2];
        alarmCorrelationData.childAlarmGroupId = item[1];
      }
    });
    alarmCorrelationData = { ...alarmCorrelationData, probability: 1.0, manualConfirm: 1 };

    // 插入一条关联关系,查询接口前开启请等待提示
    setAlarmRulePar({ ...alarmRulePar, isLoadingShow: true });
    api.insertAlarmAssociation({
      taskId: drawerTaskId,
      alarmAssociation: alarmCorrelationData,
    }, async res => {
      // 关闭请等待提示
      setAlarmRulePar({ ...alarmRulePar, isLoadingShow: false });

      // 操作失败
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        let msg = res.resultMessage || $t('alarm.correlation.rule.create.fail');
        modal.error($t('kpi.task.common.error.tip'), msg);
        return;
      }

      // 更新告警关联关系列表,关闭新建告警关联规则弹框
      createRuleCallback();
    }, () => {
      setAlarmRulePar({ ...alarmRulePar, isLoadingShow: false });
    });
  };

  // 可选告警表格页码pageIndex回调
  const handlePageIndexChange = (currentPage) => {
    const { optionalPageResults, pageSize } = alarmRulePar;
    let res = optionalPageResults.slice((currentPage - 1) * pageSize, currentPage * pageSize);
    setAlarmRulePar({ ...alarmRulePar, pageResults: res, pageIndex: currentPage });
  };

  // 可选告警表格数据量pageSize回调
  const handlePageSizeChange = (pageSize) => {
    // 更换分页数据量，页码需要置为1
    const { optionalPageResults } = alarmRulePar;
    let res = optionalPageResults.slice(0, pageSize);
    setAlarmRulePar({ ...alarmRulePar, pageResults: res, pageSize, pageIndex: 1 });
  };

  return (
    <Dialog
      title={$t('alarm.correlation.rule.new')}
      size={[700, 1100]}
      closeOnEscape={false}
      style={{ maxHeight: '700px', minHeight: '700px', maxWidth: '1100px', minWidth: '1100px' }}
      isOpen={true}
      onClose={() => isAlarmRuleDialogOpen(false)}
      resizable={false}
      buttons={[{
        text: $t('alarm.correlation.rule.confirm'),
        onClick: () => createAlarmRule(),
      }, {
        text: $t('alarm.correlation.rule.cancel'),
        onClick: () => isAlarmRuleDialogOpen(false),
      }]}
    >
      <Row>
        <Col cols={24} >
          <table style={{ tableLayout: 'fixed', width: '100%' }}>
            <tbody>
              <tr>
                <td style={{
                  width: '250px', height: '530px', display: 'inline-block',
                  overflow: 'auto', border: '1px solid #cccccc',
                }}
                >
                  <Tree data={alarmRulePar.alarmRuleTreeData} nodeKey="id"
                    onSelect={alarmOnSelect}
                    style={{ padding: '0px 0px 0px 5px' }}
                    enableScroll={true}
                    checkWhenSelect={false}
                    expandedKeys={alarmRulePar.expandedKeys}
                    ref={tree => { alarmRuleTree = tree; }}
                  />
                </td>
                <td id="alarmRule" style={{ width: '780px', verticalAlign: 'top', paddingLeft: '10px' }}>
                  <Row style={{ marginBottom: '10px' }}>
                    <Col cols={8} >
                      <span style={{ lineHeight: '2rem' }}>
                        {$t('alarm.selected.list')} {alarmRulePar.optionalRecordCount}
                      </span>
                    </Col>
                    <Col cols={16} >
                      {
                        alarmRulePar.isFilterDisplay &&
                        <SearchInput placeholder={$t('alarm.correlation.rule.enter.keyword')} id="searchTaskName"
                          inputStyle={{ width: '240px' }}
                          value={alarmRulePar.ruleName}
                          onChange={val => setAlarmRulePar({ ...alarmRulePar, ruleName: val })}
                          validator={(val, id) => validate(['cmpValidChar', 'checkLength'], val, id, null, 128)}
                          onSearch={searchAlarmList}
                          onRef={(ele) => ruleNameSearch = ele}
                        />
                      }
                    </Col>
                  </Row>
                  <Row style={{ width: '100%', height: '292px', overflow: 'hidden' }}>
                    <Col cols={24}>
                      <Table
                        key={alarmRulePar.ruleTableKey}
                        enablePagination
                        pageSize={alarmRulePar.pageSize}
                        currentPage={alarmRulePar.pageIndex}
                        recordCount={alarmRulePar.optionalRecordCount}
                        splitPagination={true}
                        columns={ALARM_OPTIONAL_COLUMNS}
                        showEmptyImage={false}
                        maxHeight={292}
                        keyIndex={0}
                        width="100%"
                        enableCheckBox={true}
                        onRowCheck={handleCheck}
                        checkedRows={alarmRulePar.optionalCheckedRows}
                        disableHeaderCheckbox={true}
                        onPageChange={handlePageIndexChange}
                        onPageSizeChange={handlePageSizeChange}
                        dataset={alarmRulePar.pageResults}
                      />
                    </Col>
                  </Row>
                  <Row style={{ marginBottom: '10px', marginTop: '10px' }}>
                    <Col cols={24} >
                      <span style={{ lineHeight: '2rem' }}>
                        {$t('alarm.optional.list')} {alarmRulePar.selectedRecordCount}
                      </span>
                    </Col>
                  </Row>
                  <Row style={{ width: '100%' }}>
                    <Col cols={24}>
                      <Table
                        columns={ALARM_SELECTED_COLUMNS}
                        showEmptyImage={false}
                        maxHeight={150}
                        width="100%"
                        dataset={alarmRulePar.selectedPageResults}
                        ref={table => { selectedTable = table; }}
                      />
                    </Col>
                  </Row>
                </td>
              </tr>
            </tbody>
          </table>
          <Loader type="global" isOpen={alarmRulePar.isLoadingShow} desc={$t('kpi.task.please.wait')} />
        </Col>
      </Row>
    </Dialog>
  );
};

export default AlarmCorrelationRule;
