/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

import React, { useEffect, useState, useRef } from 'react';
import { Table, Button, Dialog, TextField, ButtonGroup, TextButton, Loader } from 'eview-ui';
import { $t, validate, modal } from '@util';
import { RESULT_CODE } from '@const/common';
import SearchInput from '@comp/SearchInput';
import LabelHint from '@comp/LabelHint';
import useDebounce from '@util/useDebounce';
import { ALARM_NOTICE_CONFIG_COLUMN, TASK_PAGE_TYPE } from '../../const';
import pageCss from '../../css/index.css';
import { addAlarmNoticeRule, deleteAlarmNoticeRule, queryAlarmNoticeRule, updateAlarmNoticeRule } from '../../api';

const NoticeRule = ({ taskId }) => {
  const [isShowLoader, setIsShowLoader] = useState(false);
  const [filterParam, setFilterParam] = useState({ name: '', id: '' });
  const [pageData, setPageData] = useState({ pageSize: 10, currentPage: 1 });
  const [refreshFlag, setRefreshFlag] = useState(0);
  const [tableData, setTableData] = useState({
    recordCount: 0,
    rowsData: [],
    emptyTableMsg: $t('common.loading'),
  });
  const [showDialog, setShowDialog] = useState({ isShow: false, pageType: TASK_PAGE_TYPE.NEW, selectedId: null });

  const ruleNamSearchRef = useRef(null);
  const alarmIdSearchRef = useRef(null);
  const tableDataRef = useRef([]);

  useEffect(() => {
    initTableData();
  }, [pageData.currentPage, pageData.pageSize, refreshFlag]);

  useEffect(() => {
    tableDataRef.current = tableData.rowsData;
  }, [tableData]);

  const initTableData = isIgnoreFilter => {
    const param = {
      taskId,
      pageSize: pageData.pageSize,
      pageNumber: pageData.currentPage,
      alarmId: isIgnoreFilter ? '' : filterParam.id,
      ruleName: isIgnoreFilter ? '' : filterParam.name,
    };
    queryAlarmNoticeRule(
      param,
      resp => {
        if (!resp || resp.resultCode !== RESULT_CODE.success) {
          handleQueryError($t('common.search.fail'));
          return;
        }

        if (resp.data?.alarmCompressRuleList?.length > 0) {
          setTableData({
            recordCount: resp.data.total,
            rowsData: handleTableData(resp.data.alarmCompressRuleList),
            emptyTableMsg: $t('common.loading'),
          });
        } else {
          handleQueryError($t('common.nodata'));
        }
      },
      () => {
        handleQueryError($t('common.search.fail'));
      }
    );
  };

  const handleTableData = dataList => {
    return dataList.map(item => ({
      id: item.ruleId,
      alarmId: item.alarmId,
      alarmName: item.ruleName,
      location: item.locationInfoKey,
      information: item.additionInfoKey,
      operate: (
        <>
          <TextButton
            text={$t('common.edit')}
            onClick={() => setShowDialog({ isShow: true, selectedId: item.ruleId, pageType: TASK_PAGE_TYPE.MODIFY })}
          />
          <TextButton
            text={$t('common.delete')}
            style={{ marginLeft: '8px' }}
            onClick={() => deleteRule(item.ruleId)}
          />
        </>
      ),
    }));
  };

  const handleQueryError = errMsg => {
    setTableData({ recordCount: 0, rowsData: [], emptyTableMsg: errMsg });
  };

  // 切换页码
  const handlePaging = pageNum => {
    setPageData({ ...pageData, currentPage: pageNum });
  };

  // 分页大小点击回调
  const handlePageSize = size => {
    setPageData({ pageSize: size, currentPage: 1 });
  };

  const closeDialog = isRefresh => {
    setShowDialog({ isShow: false, selectedId: null, pageType: TASK_PAGE_TYPE.NEW });

    if (isRefresh) {
      setFilterParam({ name: '', id: '' });
      initTableData(true);
    }
  };

  // 删除通知规则
  const deleteRule = id => {
    const param = {
      ruleId: id,
      taskId: taskId,
    };

    modal.confirm($t('common.confirm'), $t('alarm.notice.delete.confirm'), () => {
      deleteAlarmNoticeRule(
        param,
        resp => {
          if (!resp || resp.resultCode !== RESULT_CODE.success) {
            modal.error($t('common.error'), resp?.resultMessage ?? $t('alarm.notice.delete.failed'));
            return;
          }

          modal.success($t('common.success'), $t('alarm.notice.delete.success'));

          if (tableDataRef.current.length === 1 && pageData.currentPage !== 1) {
            setPageData({ currentPage: pageData.currentPage - 1, pageSize: pageData.pageSize });
          } else {
            refreshTable();
          }
        },
        () => {
          modal.error($t('common.error'), $t('alarm.notice.delete.failed'));
        }
      );
    });
  };

  // 校验搜索输入框
  const validateInput = () => {
    if (alarmIdSearchRef.current && !alarmIdSearchRef.current.validate()) {
      alarmIdSearchRef.current.focus();
      return false;
    }

    if (ruleNamSearchRef.current && !ruleNamSearchRef.current.validate()) {
      ruleNamSearchRef.current.focus();
      return false;
    }

    return true;
  };

  const onChangeSearchValue = (key, value) => {
    setFilterParam({ ...filterParam, [key]: value });
  };

  const onSearch = event => {
    if (event.keyCode === 13 || event.type === 'click') {
      if (!validateInput()) {
        return;
      }

      if (pageData.currentPage === 1) {
        refreshTable();
      } else {
        setPageData({ currentPage: 1, pageSize: pageData.pageSize });
      }
    }
  };

  const refreshTable = () => {
    setRefreshFlag(Date.now());
  };

  return (
    <>
      <div style={{ marginTop: '16px' }}>
        <div className={pageCss.alarm_notice_head}>
          <SearchInput
            placeholder={$t('alarm.notice.config.column.alarmName')}
            inputStyle={{ width: '240px' }}
            value={filterParam.name}
            validator={(val, id) => validate(['cmpValidChar', 'checkLength'], val, id, null, 128)}
            onChange={val => onChangeSearchValue('name', val)}
            onSearch={onSearch}
            onRef={ele => (ruleNamSearchRef.current = ele)}
          />
          <SearchInput
            placeholder={$t('alarm.notice.config.column.alarmId')}
            inputStyle={{ width: '240px' }}
            value={filterParam.id}
            validator={(val, id) => validate(['cmpValidChar', 'checkLength'], val, id, null, 128)}
            onChange={val => onChangeSearchValue('id', val)}
            onSearch={onSearch}
            onRef={ele => (alarmIdSearchRef.current = ele)}
          />
          <div style={{ display: 'grid', justifyContent: 'end' }}>
            <Button
              status="primary"
              text={$t('common.create')}
              onClick={() => {
                setShowDialog({ isShow: true, selectedId: null, pageType: TASK_PAGE_TYPE.NEW });
              }}
            />
          </div>
        </div>

        <Table
          columns={ALARM_NOTICE_CONFIG_COLUMN}
          dataset={tableData.rowsData}
          recordCount={tableData.recordCount}
          emptyTableMsg={tableData.emptyTableMsg}
          pageSize={pageData.pageSize}
          currentPage={pageData.currentPage}
          onPageChange={handlePaging}
          onPageSizeChange={handlePageSize}
          splitPagination={true}
          enablePagination
        />
      </div>
      <Dialog
        title={showDialog.pageType === TASK_PAGE_TYPE.NEW ? $t('alarm.notice.create') : $t('alarm.notice.edit')}
        modal={true}
        closeOnEscape={false}
        style={{ width: '583px', height: '342px' }}
        isOpen={showDialog.isShow}
        onClose={() => closeDialog()}
      >
        <NewOrEditRule
          taskId={taskId}
          ruleId={showDialog.selectedId}
          pageType={showDialog.pageType}
          closeDialog={closeDialog}
        />
      </Dialog>
      <Loader type="global" isOpen={isShowLoader} desc={$t('common.please.wait')} />
    </>
  );
};

// 创建、编辑告警通知规则组件
const NewOrEditRule = ({ taskId, ruleId, pageType, closeDialog }) => {
  const [isShowLoader, setIsShowLoader] = useState(false);
  const [ruleInfo, setRuleInfo] = useState({ id: ruleId, alarmId: '', alarmName: '', location: '', information: '' });
  const alarmIdRef = useRef();
  const alarmNameRef = useRef();
  const locationRef = useRef();
  const informationRef = useRef();

  useEffect(() => {
    if (pageType === TASK_PAGE_TYPE.MODIFY) {
      queryAlarmNoticeRule(
        { taskId, ruleId, pageSize: 1, pageNumber: 1 },
        resp => {
          if (!resp || resp.resultCode !== RESULT_CODE.success) {
            modal.error($t('common.error'), resp?.resultMessage ?? $t('alarm.notice.query.failed'));
            return;
          }

          if (resp.data?.alarmCompressRuleList?.length > 0) {
            const item = resp.data.alarmCompressRuleList[0];
            setRuleInfo({
              id: item.ruleId,
              alarmId: item.alarmId,
              alarmName: item.ruleName,
              location: item.locationInfoKey,
              information: item.additionInfoKey,
            });
          } else {
            modal.error($t('common.error'), resp?.resultMessage ?? $t('alarm.notice.query.failed'));
          }
        },
        () => {
          modal.error($t('common.error'), $t('alarm.notice.query.failed'));
        }
      );
    }
  }, []);

  const buttons = [
    {
      text: $t('common.cancel'),
      onClick: () => closeDialog(),
    },
    {
      text: $t('common.save'),
      status: 'primary',
      onClick: () => {
        setIsShowLoader(true);
        saveDebounce();
      },
    },
  ];

  // 校验通知规则
  const validateRuleInfo = () => {
    const result = {
      flag: true,
      errMsg: '',
    };

    if (!ruleInfo.location && !ruleInfo.information) {
      result.flag = false;
      result.errMsg = $t('alarm.notice.validate.empty');
      return result;
    }

    return result;
  };

  // 保存防抖
  const saveDebounce = useDebounce(() => onSave(), 300);

  // 保存
  const onSave = () => {
    const arr = [alarmIdRef.current, alarmNameRef.current, locationRef.current, informationRef.current];
    for (let item of arr) {
      if (item && !item.validate()) {
        setIsShowLoader(false);
        item.focus();
        return;
      }
    }

    const validateResult = validateRuleInfo();
    if (!validateResult.flag) {
      setIsShowLoader(false);
      modal.error($t('common.error'), validateResult.errMsg);
      return;
    }

    const saveFunc = pageType === TASK_PAGE_TYPE.NEW ? addAlarmNoticeRule : updateAlarmNoticeRule;
    const param = {
      ruleId: ruleInfo.id ?? undefined,
      taskId: taskId,
      ruleName: ruleInfo.alarmName,
      alarmId: ruleInfo.alarmId,
      locationInfoKey: ruleInfo.location ?? '',
      additionInfoKey: ruleInfo.information ?? '',
    };
    saveFunc(
      param,
      resp => {
        setIsShowLoader(false);
        if (!resp || resp.resultCode !== RESULT_CODE.success) {
          let errorMsg = resp?.resultMessage;
          if (!errorMsg) {
            errorMsg = pageType === TASK_PAGE_TYPE.NEW ? $t('alarm.notice.create.failed') : $t('alarm.notice.update.failed');
          }

          modal.error($t('common.error'), errorMsg);
          return;
        }
        modal.success(
          $t('common.success'),
          pageType === TASK_PAGE_TYPE.NEW ? $t('alarm.notice.create.success') : $t('alarm.notice.update.success'),
          () => { closeDialog(true) }
        );
      },
      () => {
        setIsShowLoader(false);
        modal.error(
          $t('common.error'),
          pageType === TASK_PAGE_TYPE.NEW ? $t('alarm.notice.create.failed') : $t('alarm.notice.update.failed')
        );
      }
    );
  };

  const onChangeValue = (key, value) => {
    setRuleInfo({ ...ruleInfo, [key]: value });
  };

  return (
    <>
      <div className={pageCss.alarm_notice_container}>
        <LabelHint label={$t('alarm.notice.config.column.alarmName')} require={true} />
        <TextField
          inputStyle={{ width: '320px' }}
          hintType="tip"
          autoComplete="off"
          value={ruleInfo.alarmName}
          validator={(val, id) => validate(['required', 'checkLength', 'cmpValidChar'], val, id, null, 128)}
          onChange={val => onChangeValue('alarmName', val)}
          ref={ref => (alarmNameRef.current = ref)}
        />

        <LabelHint label={$t('alarm.notice.config.column.alarmId')} require={true} />
        <TextField
          inputStyle={{ width: '320px' }}
          hintType="tip"
          autoComplete="off"
          value={ruleInfo.alarmId}
          validator={(val, id) => validate(['required', 'checkLength', 'cmpValidChar'], val, id, null, 128)}
          onChange={val => onChangeValue('alarmId', val)}
          ref={ref => (alarmIdRef.current = ref)}
        />

        <div style={{ position: 'relative' }}>
          <span className="label-span">*</span>
          <span style={{ marginLeft: '8px' }}>{$t('alarm.notice.rule.content')}</span>
        </div>
        <div />

        <LabelHint
          require={false}
          isHelp={true}
          hint={$t('alarm.notice.config.column.location.tips')}
          label={$t('alarm.notice.config.column.location')}
        />
        <TextField
          inputStyle={{ width: '320px' }}
          hintType="tip"
          autoComplete="off"
          value={ruleInfo.location}
          validator={(val, id) => validate(['alarmNoticeRuleValidate', 'cmpValidChar'], val.trim(), id, null, 64)}
          onChange={val => onChangeValue('location', val.trim())}
          ref={ref => (locationRef.current = ref)}
        />

        <LabelHint
          require={false}
          isHelp={true}
          hint={$t('alarm.notice.config.column.information.tips')}
          label={$t('alarm.notice.config.column.information')}
        />
        <TextField
          inputStyle={{ width: '320px' }}
          hintType="tip"
          autoComplete="off"
          value={ruleInfo.information}
          validator={(val, id) => validate(['alarmNoticeRuleValidate', 'cmpValidChar'], val.trim(), id, null, 64)}
          onChange={val => onChangeValue('information', val.trim())}
          ref={ref => (informationRef.current = ref)}
        />
      </div>
      <ButtonGroup className={pageCss.alarm_notice_button_group} data={buttons} />
      <Loader type="global" isOpen={isShowLoader} desc={$t('common.please.wait')} />
    </>
  );
};

export default NoticeRule;
