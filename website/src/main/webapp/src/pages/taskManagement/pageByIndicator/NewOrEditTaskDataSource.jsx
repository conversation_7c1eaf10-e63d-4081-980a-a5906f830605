/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import React, { useContext, useEffect, useRef, useImperativeHandle } from 'react';
import PropTypes from 'prop-types';
import { Table, Select, RadioGroup, Button, TextField, Loader, TextButton } from 'eview-ui';
import LabelHint from '@comp/LabelHint';
import { util, modal, $t, validate, pages } from '@util';
import * as api from '../api';
import {
  NewTaskContext, DATA_RESOURCE_MAP, DV_PERFORMANCE_DATA_SOURCE, MEASURE_TABLE_COLUMNS, RESOURCE_TYPE,
  TASK_PAGE_TYPE, VIEW_MEASURE_TABLE_COLUMNS, OPERATE_SUCCESS_CODE, INDICATOR_TYPE_MAPS,
  CAPACITY_VIEW_MEASURE_TABLE_COLUMNS, CAPACITY_NO_TREE_DATA_SOURCE, CAPACITY_TREE_DATA_SOURCE,
  DR_SWITCH_MEASURE_TABLE_COLUMNS, MAX_INDICATOR_SWITCH, AUXILIARY_MEASURE_TABLE_COLUMNS,
  DR_SWITCH_MEASURE_TABLE_COLUMNS_VIEW, AUXILIARY_MEASURE_TABLE_COLUMNS_VIEW, CAPACITY_MEASURE_TABLE_COLUMNS,
  MAX_INDICATOR, CAPACITY_PER_NODE_LIMIT, TREND_MENU, INDEPENDENT_MEASURE_TABLE_COLUMNS, IS_AUTO_UPDATE_MO, IS_AUTO_KEY,
  IS_AUTO_KEY_TEXT, EDIT_MEASURE_MO_TYPE_COLUMNS, VIEW_MEASURE_MO_TYPE_COLUMNS, SWITCH_MANAGE_MENU, CAPACITY_MENU,
} from '../const';
import {
  DV_PERFORMANCE_TREND_TYPE, TREND_TABLE_COLUMNS, KPI_TREND_PREDICT_TYPE, TREND_TYPE_MAPS,
  CLUSTER_MEASURE_TABLE_COLUMNS_NEW, TREND_VIEW_TABLE_COLUMNS, DV_PERFORMANCE_TREND_TYPE_MS,
} from '../const/trend';
import { PROFILE_INDICATOR_TABLE_COLUMNS } from '../const/tidal';
import { deleteSpecifiedEleFromArr, isFaultAnalysis } from '../util';
import { ARRAY_INDEX_0, MAX_LENGTH_64, PAGE_SIZE_50, TEMPLATE_TYPE } from '@const/common';
import { queryMember } from '../../drSwitchOverManage/utils/page';
import DataCheck from '@util/utils/DataCheck';
import ConfigureTrendAuxiliaryIndicator from '../pageByTrend/ConfigureTrendAuxiliaryIndicator';
import DataSourceNETree from './DataSourceNETree';
import DataSourceUnitTree from './DataSourceUnitTree';
import DataSourceObjectTable from './DataSourceObjectTable';
import { getMaxKpiNum, validEmpty } from '@pages/taskManagement/util/pages';
import DataSourceClusterGroupTable from '../pageByTrend/DataSourceClusterGroupTable';
import ConfigureDataSetDialog from '@pages/taskManagement/pageByTrend/ConfigureDataSetDialog';

let measurePageTable;
const NewOrEditTaskDataSource = props => {
  const { taskList, newOrUpdateTask, setTaskList, cRef, setTaskPageType, taskPageType, submitTask, getCustomTableCol,
    maxIndicatorLength, isCapacity, nodeType, isDrSwitch, isAuxiliaryKpi, groupId, setPerformancePar, performancePar,
    getCustomToggle, columns, memberInfo, getCustomShowCol, isTreeRoot, rootMoTypeAndDn, hashName, isTrendAuxiliaryKpi,
    isProfileKpi, isCorrelationAnalysis, isKpiTaskManage,
  } = props;
  const [state, dispatch] = useContext(NewTaskContext);
  const { measureIndexList, netWorkTreeParam, unitTree, objectTable, checkedMeasureIndex, isRefreshByResource,
    resourceCache, isSolutionLoading, solutions, isSourceLoading, deleteMeasureDataParam, isUnitLoading, auxiliaryPar,
    isObjTableLoading, isDisplayLoading, clusterGroupParam, perClusterGroupKpiNum, associatedDataSetPar } = state;
  const { solutionName, solutionId, indicatorPredictScenario, isAutoUpdateMO } = measureIndexList;
  const measureData = useRef(resourceCache);
  // 用来存放点击修改按钮前的数据，点击取消后可恢复
  const medianResourceInfo = useRef({});
  const isSolutionChange = useRef(false); // 判断是否改变解决方案，触发网元树刷新
  const beforeSelect = useRef(measureIndexList.resourceRelatedMode);// 用来记录资源关联方式切换
  const beforePredictSelect = useRef(measureIndexList.indicatorPredictScenario);// 用来记录指标预测类型切换
  const createTaskAllInfo = useRef({});
  const checkedMeasureIndexTable = useRef(checkedMeasureIndex);
  const customIndicatorRef = useRef({
    upperThreshold: [],
    lowerThreshold: [],
    indicatorZhNames: [],
    indicatorEnNames: [],
  });
  const currentMember = useRef(netWorkTreeParam.memberId);
  const currentMemberName = useRef(netWorkTreeParam.memberName);
  const currentDn = useRef(netWorkTreeParam.dn);
  const currentTreeRootId = useRef('/');
  const NEChildRef = useRef(); // 网元树组件Ref
  const UnitTreeChildRef = useRef(); // 测量单元树组件Ref
  const objTableChildRef = useRef(); // 测量对象表格组件Ref
  const groupTableChildRef = useRef(); // 双机分组表格组件Ref
  const solutionIdRef = useRef({ id: '', name: '' });

  useEffect(() => {
    solutionIdRef.current = { id: solutionId, name: solutionName };
  }, [solutionId, solutionName]);

  useEffect(() => {
    createTaskAllInfo.current = { measureIndexList, netWorkTreeParam, unitTree, objectTable };
  }, [measureIndexList, netWorkTreeParam, unitTree, objectTable]);

  useEffect(() => {
    // 已选择指标情况下，指标表格的回显（智能容灾主指标、容量评估资源树节点）
    const isDisplayData = (isDrSwitch || isAuxiliaryKpi || isTrendAuxiliaryKpi || (nodeType === TEMPLATE_TYPE.CAPACITY && isCapacity)) &&
      JSON.stringify(performancePar) !== '{}';
    if (isDisplayData) {
      let { measureIndexList, netWorkTreeParam, unitTree, objectTable, checkedMeasureIndex, isRefreshByResource,
        resourceCache } = performancePar;
      let rows = [];
      checkedMeasureIndexTable.current = checkedMeasureIndex;
      measureData.current = resourceCache;
      if (isDrSwitch) {
        currentMember.current = netWorkTreeParam.memberId;
        currentDn.current = netWorkTreeParam.dn;
        currentMemberName.current = netWorkTreeParam.memberName;
        rows = getMainKpiTable(measureIndexList.measureTableList);
      } else if (isCapacity) {
        rows = getCapacityTable(measureIndexList);
      }

      dispatch({
        type: 'setSwitchKpi',
        measureIndexList: {
          ...measureIndexList,
          measurePageResults: rows.slice((measureIndexList.pageIndex - 1) * measureIndexList.pageSize,
            measureIndexList.pageIndex * measureIndexList.pageSize),
          measureTableList: rows,
        }, netWorkTreeParam, unitTree, objectTable, checkedMeasureIndex, isRefreshByResource, resourceCache,
      });
    }
  }, [performancePar]);

  useEffect(() => {
    // （智能容灾主指标）未选择指标情况下查询成员，新建时默认选择第一个成员关联的网元实例树
    const isQueryMember = taskPageType !== TASK_PAGE_TYPE.VIEW && taskPageType !== TASK_PAGE_TYPE.VIEW_OTHER && isDrSwitch &&
      ((JSON.stringify(performancePar) === '{}') || performancePar.isRefreshByResource);
    if (isQueryMember) {
      dispatch({ type: 'setIsSourceLoading', isSourceLoading: true });
      queryMember(groupId, (memberList) => {
        if (taskPageType === TASK_PAGE_TYPE.NEW) {
          currentMember.current = memberList[0].value;
          currentDn.current = memberList[0].dn;
          currentMemberName.current = memberList[0].text;
          setNetWorkTreeParam({
            memberId: currentMember.current,
            memberOptions: memberList,
            dn: currentDn.current,
            memberName: currentMemberName.current,
          });

          NEChildRef.current.queryNetWorkTree(measureIndexList.resourceRelatedMode, KPI_TREND_PREDICT_TYPE.INDEPENDENT);
        } else {
          setNetWorkTreeParam({ memberOptions: memberList });
        }
      }, err => {
        dispatch({ type: 'setIsSourceLoading', isSourceLoading: false });
      });
    }

    // 智能容灾辅助指标、指标趋势预测集群辅助指标回显网元树
    const isShowUnitTreeData = taskPageType !== TASK_PAGE_TYPE.VIEW && taskPageType !== TASK_PAGE_TYPE.VIEW_OTHER &&
      (isAuxiliaryKpi || isTrendAuxiliaryKpi) && ((JSON.stringify(performancePar) === '{}') || performancePar.isRefreshByResource);
    if (isShowUnitTreeData) {
      setNetWorkTreeParam({
        memberId: memberInfo.memberId,
        dn: memberInfo.dn,
        memberName: memberInfo.memberName,
      });
      currentMember.current = memberInfo.memberId;
      currentDn.current = memberInfo.dn;
      currentMemberName.current = memberInfo.memberName;
      if (taskPageType === TASK_PAGE_TYPE.NEW || taskPageType === TASK_PAGE_TYPE.COPY) {
        NEChildRef.current.queryNetWorkTree(measureIndexList.resourceRelatedMode);
      }
    }
  }, []);

  useEffect(() => {
    const isHandleNeTree = ((taskPageType !== TASK_PAGE_TYPE.MODIFY_OTHER && taskPageType !== TASK_PAGE_TYPE.VIEW_OTHER) &&
      ((taskPageType !== TASK_PAGE_TYPE.VIEW && isRefreshByResource) || measureIndexList.isFirstEntry) &&
      !isDrSwitch && !isAuxiliaryKpi && !isTrendAuxiliaryKpi &&
      (JSON.stringify(performancePar) === '{}' || nodeType !== TEMPLATE_TYPE.CAPACITY)) || isSolutionChange.current;
    if (isHandleNeTree) {
      // 复制和修改首次不调用，直接从查看信息接口获取数据
      const isQueryNeTree = !measureIndexList.isFirstEntry || (measureIndexList.isFirstEntry && taskPageType === TASK_PAGE_TYPE.NEW) ||
        isSolutionChange.current;
      if (isQueryNeTree) {
        NEChildRef.current.queryNetWorkTree(measureIndexList.resourceRelatedMode, measureIndexList.indicatorPredictScenario);
        if (isSolutionChange.current) {
          isSolutionChange.current = false;
        }
      } else {
        dispatch({ type: 'setMeasureIndexList', measureIndexList: { ...measureIndexList, isFirstEntry: false } });
      }
    }
  }, [measureIndexList.resourceRelatedMode, measureIndexList.solutionId, measureIndexList.indicatorPredictScenario]);

  useEffect(() => {
    const isShowIndicatorTableData = taskPageType !== TASK_PAGE_TYPE.MODIFY_OTHER && taskPageType !== TASK_PAGE_TYPE.VIEW_OTHER &&
      (taskPageType !== TASK_PAGE_TYPE.NEW && isRefreshByResource && !isSolutionChange.current) && !isDrSwitch &&
      !isAuxiliaryKpi && !isTrendAuxiliaryKpi;
    if (isShowIndicatorTableData) {
      // 获取指标列表最后一条数据,从中拼凑measureData
      let indicatorList = taskList.indicatorList;
      let lastMeasureData = indicatorList[indicatorList.length - 1];
      let _indicatorSelectType = getIndicatorSelectType();
      let isInstance = _indicatorSelectType === RESOURCE_TYPE.NETWORK_INSTANCE;
      measureData.current = getMeasureDataCurrent(indicatorList, isInstance, taskList.indicatorPredictScenario);
      beforeSelect.current = _indicatorSelectType;
      beforePredictSelect.current = taskList.indicatorPredictScenario;
      let rows = getMeasureTable(taskList.indicatorList, isInstance, taskList.indicatorPredictScenario);
      let noInstanceTableData = getPageResultsNoInstance(taskList.indicatorList);

      dispatch({
        type: 'setMeasureListByRefresh',
        isRefreshByResource: false,
        measureIndexList: {
          ...createTaskAllInfo.current.measureIndexList,
          solutionId: taskList.solutionId ? taskList.solutionId : '',
          solutionName: taskList.solutionName || $t('kpi.task.no.solution'),
          resourceRelatedMode: _indicatorSelectType,
          indicatorPredictScenario: taskList.indicatorPredictScenario,
          measurePageResults: rows.slice(0, 50),
          measureTableList: rows,
          pageIndex: 1,
          pageSize: 50,
          measureKey: new Date().getTime(),
          isFirstEntry: false,
          isAutoUpdateMO: taskList.updateIndicatorAuto ?? IS_AUTO_KEY.NO,
          measurePageResultsNoInstance: noInstanceTableData.slice(ARRAY_INDEX_0, PAGE_SIZE_50),
          measureTableListNoInstance: noInstanceTableData,
        },
      });

      if (taskPageType === TASK_PAGE_TYPE.COPY || taskPageType === TASK_PAGE_TYPE.MODIFY) {
        // 回显双机分组列表
        displayClusterGroupData(taskList);
        getSelectedData(isInstance, lastMeasureData, indicatorList, {
          solutionId: taskList.solutionId ? taskList.solutionId : '',
        }, taskList.indicatorPredictScenario);
      }
    }
  }, [taskPageType]);

  useEffect(() => {
    const isGetSolutions = taskPageType !== TASK_PAGE_TYPE.VIEW && taskPageType !== TASK_PAGE_TYPE.VIEW_OTHER && isRefreshByResource &&
      (isCorrelationAnalysis || isKpiTaskManage || (isCapacity && nodeType !== TEMPLATE_TYPE.CAPACITY) ||
        (isCapacity && nodeType === TEMPLATE_TYPE.CAPACITY && isTreeRoot && JSON.stringify(performancePar) === '{}')
      );
    if (isGetSolutions) {
      dispatch({ type: 'setIsSolutionLoading', isSolutionLoading: true });
      api.getSolutions({}, res => {
        dispatch({ type: 'setIsSolutionLoading', isSolutionLoading: false });

        // 查询解决方案列表失败
        if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
          modal.error($t('kpi.task.common.error.tip'), $t('kpi.task.query.solution.fail'));
          return;
        }

        dispatch({
          type: 'setSolutions',
          solutions: [{ text: $t('kpi.task.no.solution'), value: '' }, ...res.data],
        });
      }, err => {
        dispatch({ type: 'setIsSolutionLoading', isSolutionLoading: false });
      });
    }
  }, [taskPageType]);

  useEffect(() => {
    if (JSON.stringify(deleteMeasureDataParam) !== '{}') {
      if (isAutoUpdateMO) {
        deleteMeasureDataOfNoInstance(deleteMeasureDataParam);
      } else {
        deleteMeasureData(deleteMeasureDataParam);
      }
      dispatch({
        type: 'setDeleteMeasureDataParam',
        deleteMeasureDataParam: {},
      });
    }
  }, [deleteMeasureDataParam]);

  // 回显双机分组列表:复制时，groupTableChildRef.current初始获取不到问题处理
  const displayClusterGroupData = (taskList) => {
    if (taskList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE) {
      let groupTableRes = taskList.aggregateMachineGroupList ?
        JSON.parse(taskList.aggregateMachineGroupList).map(item => ({
          ...item,
          operation: groupTableChildRef.current ? groupTableChildRef.current.getCustomOperation(item) : '',
          solutionName: item.solutionName || '',
        })) : [];
      dispatch({
        type: 'setClusterGroupParam',
        clusterGroupParam: {
          tableResult: groupTableRes,
          checkedRows: [...new Set(taskList.indicatorList.map(item => item.checkedNetId))],
        },
      });
    }
  };

  // 兼容指标趋势预测老任务：C版本升级场景
  const getIndicatorSelectType = () => {
    if (hashName === TREND_MENU && taskList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.INDEPENDENT &&
      validEmpty(taskList.indicatorSelectType)) {
      return RESOURCE_TYPE.NETWORK_INSTANCE;
    }
    return taskList.indicatorSelectType;
  };

  // 获取不同的checkedNetName
  const getCheckedNetName = (lastMeasureData, predictScenario) => {
    if (hashName === TREND_MENU && predictScenario === KPI_TREND_PREDICT_TYPE.UNITE) {
      return lastMeasureData.clusterName;
    } else if (predictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE) {
      let dnNames = lastMeasureData.aggregateMos ? lastMeasureData.aggregateMos.map(item => item.dnName) : [];
      return dnNames;
    } else {
      return lastMeasureData.dnName;
    }
  };

  // 获取指标列表最后一条数据,从中拼凑measureData
  const getMeasureDataCurrent = (indicatorList, isInstance, predictScenario = KPI_TREND_PREDICT_TYPE.INDEPENDENT) => {
    if (!indicatorList.length) {
      return [];
    }

    let lastMeasureData = indicatorList[indicatorList.length - 1];
    const { dn, measUnitName, measUnitKey, measTypeKey, resourceTypeKey, moType } = lastMeasureData;
    let checkedDn = getClusterKey(isInstance, lastMeasureData, dn, predictScenario);
    let rowId = `${checkedDn}${resourceTypeKey}${moType}${measUnitName}${measUnitKey}${measTypeKey}`;
    let isUnitIndicator = hashName === TREND_MENU && predictScenario === KPI_TREND_PREDICT_TYPE.UNITE;
    let measureDataCurrent = {
      checkedNetName: getCheckedNetName(lastMeasureData, predictScenario),
      checkedNetType: lastMeasureData.moType,
      deploymentMoType: lastMeasureData.deploymentMoType ?? lastMeasureData.moType,
      checkedNetId: lastMeasureData.checkedNetId,
      indexGroupName: lastMeasureData.measUnitName,
      measureIndex: lastMeasureData.indexName,
      measUnitKey: lastMeasureData.measUnitKey,
      measTypeKey: lastMeasureData.measTypeKey,
      unit: lastMeasureData.unit,
      displayValue: lastMeasureData.hasMeasObj === 'true' || lastMeasureData.hasMeasObj === true ?
        lastMeasureData.displayValue : '',
      originalValue: lastMeasureData.hasMeasObj === 'true' || lastMeasureData.hasMeasObj === true ?
        lastMeasureData.originalValue : '',
      rowId,
      measureUnitId: rowId,
      id: lastMeasureData.hasMeasObj === 'true' || lastMeasureData.hasMeasObj === true ?
        `${rowId}_${lastMeasureData.indexId}_${lastMeasureData.displayValue}` : rowId,
      checkedDn: lastMeasureData.dn || '',
      hasMeasObj: lastMeasureData.hasMeasObj,
      indexId: lastMeasureData.indexId,
      resourceTypeKey: lastMeasureData.resourceTypeKey,
      eamNodeType: lastMeasureData.eamNodeType,
      unitRootType: lastMeasureData.deploymentMoType ? '' : 'default',
    };
    if (predictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE) {// 冷双机
      measureDataCurrent = { ...measureDataCurrent, clusterName: lastMeasureData.checkedNetId };
    }

    if (isUnitIndicator) {// 联合指标
      measureDataCurrent = {
        ...measureDataCurrent,
        clusterName: lastMeasureData.clusterName,
        siteId: lastMeasureData.siteId,
      };
    }
    return measureDataCurrent;
  };

  // 智能容灾，回显表格
  const getMainKpiTable = (res) => {
    res.forEach((item, index) => {
      const { isDisplay, auxiliaryKpiInfos, unit, hasMeasObj, derivedKpiInfo, thresholds,
        kpiAuxiliaryIndicators, groupMemberId, vnfDn, vnfDnName, indexId, displayValue } = item[1];
      item[7] = getCustomTableCol('derivedVal', derivedKpiInfo && Object.keys(derivedKpiInfo).length,
        { unit, rowId: item[0], derivedKpiInfo });
      item[8] = getCustomTableCol('mainThreshold', thresholds && thresholds.length,
        { unit, rowId: item[0], thresholds });
      item[9] = getCustomTableCol('auxiliaryIndicator', kpiAuxiliaryIndicators && kpiAuxiliaryIndicators.length, {
        unit, rowId: item[0], kpiAuxiliaryIndicators,
        memberInfo: {
          memberId: groupMemberId,
          dn: vnfDn,
          memberName: vnfDnName,
        },
      });
      item[10] = getCustomTableCol('auxiliaryInfo', auxiliaryKpiInfos && auxiliaryKpiInfos.length,
        { rowId: item[0], auxiliaryKpiInfos });
      item[11] = getCustomToggle(isDisplay, item[0]);
      item[12] = getCustomOperate(
        { measureId: item[0], measureUnitId: item[0], checkedRow: `${indexId}_${displayValue}` },
        `${(hasMeasObj === 'true' || hasMeasObj === true) ? 'table' : 'object'}`,
        'delete',
      );
    });
    return res;
  };

  // 容量评估，回显表格
  const getCapacityTable = (measureIndexList) => {
    let res = [...measureIndexList.measureTableList];
    res.forEach((item, index) => {
      item[8] = getCustomTextField(index, 'upperThreshold',
        measureIndexList.upperThreshold[index]);
      item[9] = getCustomTextField(index, 'lowerThreshold',
        measureIndexList.lowerThreshold[index]);
    });
    return res;
  };

  const getDrSwitchRow = (operateParam, commonPar, item, deleteType) => {
    let row = [
      operateParam.measureId,
      {
        ...commonPar,
        isDisplay: item.isDisplay,
        thresholds: item.thresholds,
        derivedKpiInfo: item.derivedKpiInfo,
        auxiliaryKpiInfos: item.auxiliaryKpiInfos,
        kpiAuxiliaryIndicators: item.kpiAuxiliaryIndicators,
        groupMemberId: item.groupMemberId,
        vnfDnName: item.vnfDnName,
        vnfDn: item.vnfDn,
      },
      (item.vnfDnName || currentMemberName.current), item.dnName, item.measUnitName, item.indexName, item.displayValue,
      getCustomTableCol('derivedVal', item.derivedKpiInfo && Object.keys(item.derivedKpiInfo).length, {
        unit: commonPar.unit, rowId: operateParam.measureId, derivedKpiInfo: item.derivedKpiInfo,
      }),
      getCustomTableCol('mainThreshold', item.thresholds && item.thresholds.length, {
        unit: commonPar.unit, rowId: operateParam.measureId, thresholds: item.thresholds,
      }),
      getCustomTableCol('auxiliaryIndicator', item.kpiAuxiliaryIndicators && item.kpiAuxiliaryIndicators.length, {
        unit: commonPar.unit, rowId: operateParam.measureId, kpiAuxiliaryIndicators: item.kpiAuxiliaryIndicators,
        memberInfo: {
          memberId: item.groupMemberId,
          dn: item.vnfDn,
          memberName: item.vnfDnName,
        },
      }),
      getCustomTableCol('auxiliaryInfo', item.auxiliaryKpiInfos && item.auxiliaryKpiInfos.length, {
        rowId: operateParam.measureId, auxiliaryKpiInfos: item.auxiliaryKpiInfos,
      }),
      getCustomToggle(item.isDisplay, operateParam.measureId),
      getCustomOperate(operateParam, deleteType, 'delete'),
    ];
    return row;
  };

  const getRow = (operateParam, commonPar, item, index, predictScenario, isGetPageResultsNoInstance) => {
    let row;
    let deleteType = (item.hasMeasObj === 'true' || item.hasMeasObj === true) ? 'table' : 'object';
    if (isDrSwitch) { // 智能容灾主指标
      row = getDrSwitchRow(operateParam, commonPar, item, deleteType);
    } else if (isAuxiliaryKpi) { // 智能容灾辅助指标
      row = [operateParam.measureId, {
        ...commonPar,
        auxiliaryThresholds: item.auxiliaryThresholds,
        columnName: item.columnName,
        groupMemberId: item.groupMemberId,
        vnfDnName: item.vnfDnName,
        vnfDn: item.vnfDn,
      },
      item.moType, item.dnName, item.measUnitName, item.indexName, item.displayValue,
      getCustomTableCol(item.auxiliaryThresholds && item.auxiliaryThresholds.length, {
        unit: commonPar.unit, rowId: operateParam.measureId, auxiliaryThresholds: item.auxiliaryThresholds,
      }),
      getAuxiliaryTextField(index, 'indicatorZhNames', item.auxiliaryKPIDisplayCN),
      getAuxiliaryTextField(index, 'indicatorEnNames', item.auxiliaryKPIDisplayEN),
      getCustomShowCol(item.columnName, operateParam.measureId),
      getCustomOperate(operateParam, deleteType, 'delete')];
    } else if (hashName === TREND_MENU && !isTrendAuxiliaryKpi) {
      row = getTrendRow(operateParam, commonPar, item, index, predictScenario, deleteType);
    } else {
      let param = nodeType === TEMPLATE_TYPE.CAPACITY ? {
        eamNodeType: item.eamNodeType || measureData.current.eamNodeType,
      } : {};
      row = [
        operateParam.measureId, { ...commonPar, ...param }, commonPar.deploymentMoType,
        item.moType, isGetPageResultsNoInstance ? commonPar.instanceNumber : item.dnName,
        item.measUnitName, item.indexName, item.displayValue,
        getCustomTextField(index, 'upperThreshold', item.upperThreshold),
        getCustomTextField(index, 'lowerThreshold', item.lowerThreshold),
        getCustomOperate(operateParam, deleteType, 'delete', commonPar.deploymentMoType),
      ];
    }
    return row;
  };

  // 指标趋势预测指标表格行
  const getTrendRow = (operateParam, commonPar, item, index, predictScenario, deleteType) => {
    let row;
    if (predictScenario === KPI_TREND_PREDICT_TYPE.UNITE) {
      row = [operateParam.measureId, {
        ...commonPar,
        auxiliaryIndicator: item.auxiliaryIndicator,
        clusterName: item.clusterName,
        siteId: item.siteId,
        measureIndex: item.indexName,
      },
      item.moType, item.clusterName, item.siteId, item.measUnitName, item.indexName, item.displayValue,
      getCustomAuxiliaryKpi(item.auxiliaryIndicator && item.auxiliaryIndicator.length, {
        rowId: operateParam.measureId, auxiliaryIndicator: item.auxiliaryIndicator,
      }), '', '', getCustomOperate(operateParam, deleteType, 'delete')];
    } else {
      let commonRow = [
        item.measUnitName, item.indexName, item.displayValue,
        getAssociatedDataSet(
          item.historyIndicatorId,
          {
            historyIndicatorId: item.historyIndicatorId,
            historyIndicatorName: item.historyIndicatorName,
            rowId: operateParam.measureId,
          },
        ), '', '', getCustomOperate(operateParam, deleteType, 'delete'),
      ];
      let commonParam = { ...commonPar, historyIndicatorId: item.historyIndicatorId, historyIndicatorName: item.historyIndicatorName };
      if (predictScenario === KPI_TREND_PREDICT_TYPE.INDEPENDENT) {// 独立指标
        row = [operateParam.measureId, commonParam, item.moType, item.dnName, ...commonRow];
      } else {// 双机分组指标
        let dnNames = item.aggregateMos ? item.aggregateMos.map(item => item.dnName) : [];
        row = [
          operateParam.measureId, { ...commonParam, clusterName: item.checkedNetId },
          item.checkedNetId, item.moType, dnNames, ...commonRow,
        ];
      }
    }
    return row;
  };

  const getClusterKey = (isInstance, { clusterName, siteId }, dn, predictScenario = KPI_TREND_PREDICT_TYPE.INDEPENDENT) => {
    let checkedDn;
    if (predictScenario === KPI_TREND_PREDICT_TYPE.UNITE && hashName === TREND_MENU) {
      checkedDn = siteId ? `${clusterName}${siteId}` : clusterName;
    } else if (predictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE) {// 冷双机
      checkedDn = clusterName;
    } else if (isInstance) {
      checkedDn = dn || '';
    } else {
      checkedDn = '';
    }
    return checkedDn;
  };

  // 不可编辑情况下，指标列表为用户有权限查看的列表，从indicatorList读取
  const getMeasureTable = (res, isInstance, predictScenario = KPI_TREND_PREDICT_TYPE.INDEPENDENT, len = 0) => {
    let rows = [];
    res.forEach((item, index) => {
      let checkedDn = getClusterKey(isInstance, item, item.dn, predictScenario);
      let rowId = `${checkedDn}${item.resourceTypeKey}${item.deploymentMoType ?? item.moType}${item.measUnitName}${item.measUnitKey}${item.measTypeKey}`;
      let operateParam;

      // 按网元类型下，实例化之后有dn，需要进行measureId和rowId的区分:  主机画像关联指标不实例化
      let dn = isInstance || (hashName === TREND_MENU && predictScenario === KPI_TREND_PREDICT_TYPE.UNITE) ||
        isProfileKpi || predictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE ? '' : item.dn;
      if (item.hasMeasObj === 'true' || item.hasMeasObj === true) { // 有测量对象数据
        operateParam = {
          measureId: `${dn}${rowId}_${item.indexId}_${item.displayValue}`,
          checkedRow: `${item.indexId}_${item.displayValue}`,
        };
      } else {
        operateParam = {
          measureId: `${dn}${rowId}`,
          measureUnitId: rowId,
          parentId: item.measUnitName,
        };
      }
      let commonPar = {
        checkedDn: item.dn || '',
        measUnitKey: item.measUnitKey,
        measTypeKey: item.measTypeKey,
        measureUnitId: rowId,
        unit: item.unit,
        originalValue: item.originalValue,
        displayValue: item.displayValue,
        checkedNetId: item.checkedNetId,
        hasMeasObj: item.hasMeasObj,
        indexId: item.indexId,
        resourceTypeKey: item.resourceTypeKey,
        upperThreshold: '', lowerThreshold: '',
        checkedNetType: item.moType,
        deploymentMoType: item.deploymentMoType,
        indexGroupName: item.measUnitName,
        measureIndex: item.indexName,
      };
      rows.push(getRow(operateParam, commonPar, item, len + index, predictScenario));
    });
    return rows;
  };

  const getPageResultsNoInstance = allSelectedInstance => {
    let rows = [];
    let uniqueTypeArr = classifyArrayByKeys(allSelectedInstance, ['moType', 'measTypeKey', 'originalValue']);
    Object.keys(uniqueTypeArr).map((key, index) => {
      let item = uniqueTypeArr[key][0];
      let operateParam;
      let uniqueTypeId = `${item.moType}_${item.measTypeKey}_${item.originalValue || ''}`;
      if (item.hasMeasObj === 'true' || item.hasMeasObj === true) { // 有测量对象数据
        operateParam = {
          measureId: uniqueTypeId,
          checkedRow: `${item.indexId}_${item.displayValue}`,
        };
      } else {
        operateParam = {
          measureId: uniqueTypeId,
          measureUnitId: uniqueTypeId,
          parentId: item.measUnitName,
        };
      }

      let commonPar = {
        checkedDn: item.dn || '',
        measUnitKey: item.measUnitKey,
        measTypeKey: item.measTypeKey,
        measureUnitId: uniqueTypeId,
        unit: item.unit,
        originalValue: item.originalValue,
        displayValue: item.displayValue,
        checkedNetId: item.checkedNetId,
        hasMeasObj: item.hasMeasObj,
        indexId: item.indexId,
        resourceTypeKey: item.resourceTypeKey,
        upperThreshold: '',
        lowerThreshold: '',
        checkedNetType: item.moType,
        deploymentMoType: item.deploymentMoType,
        indexGroupName: item.measUnitName,
        measureIndex: item.indexName,
        uniqueTypeId,
        instanceNumber: uniqueTypeArr[uniqueTypeId]?.length || 0,
      };

      rows.push(getRow(operateParam, commonPar, item, index, KPI_TREND_PREDICT_TYPE.INDEPENDENT, true));
    });

    return rows;
  };

  // 根据唯一key对返回的实例数组进行网元类型区分
  const classifyArrayByKeys = (arr, uniqueKeyArr) => {
    const classified = {};
    arr.forEach(item => {
      const uniqueKey = uniqueKeyArr.map(key => item[key]).join('_');
      if (!classified[uniqueKey]) {
        classified[uniqueKey] = [];
      }
      classified[uniqueKey].push(item);
    });

    return classified;
  };

  // 复制修改任务时回显指标树和表格数据
  const getSelectedData = (isInstance, indicatorSelectTree, indicatorList, { solutionId }, predictScenario = 0) => {
    let objApi;
    if (hashName === TREND_MENU && predictScenario === KPI_TREND_PREDICT_TYPE.UNITE) {// 查询集群树
      objApi = api.queryObjectTreeByType;
    } else if (isInstance) {
      objApi = api.getMeasureObjectInfo;
    } else {// 按网元类型查询网元树
      if (isCapacity && (nodeType === TEMPLATE_TYPE.CAPACITY) && !isTreeRoot) {
        objApi = api.queryCapacityObjectTreeByType;
      } else {
        objApi = api.queryObjectTreeByType;
      }
    }

    // 双机分组指标不用回显网元树
    if (predictScenario !== KPI_TREND_PREDICT_TYPE.TWO_NODE) {
      NEChildRef.current.displayNETree(isInstance, indicatorSelectTree, indicatorList, { solutionId }, predictScenario);
    }

    // 查询测量单元树
    UnitTreeChildRef.current.displayUnitTree(indicatorSelectTree, {
      type: indicatorSelectTree.deploymentMoType || indicatorSelectTree.moType,
      dn: isInstance ? indicatorSelectTree.dn : '',
      searchKey: '',
      searchScope: 'all',
      clusterName: indicatorSelectTree.clusterName,
      siteId: indicatorSelectTree.siteId,
      queryDeployment: isInstance ? false : isFaultAnalysis(),
    }, isInstance, indicatorList, predictScenario);

    // 查询测量对象表格
    objTableChildRef.current.displayTable(isInstance, indicatorSelectTree, indicatorList, objApi, predictScenario);
  };

  // 操作测量单元节点回调
  const addByUnitCallBack = (rows, selectedKeys, pageResultsNoInstance) => {
    dispatch({
      type: 'setResourceLists',
      isRefreshByResource: false,
      measureIndexList: {
        ...createTaskAllInfo.current.measureIndexList,
        measurePageResults: rows.slice((measureIndexList.pageIndex - 1) * measureIndexList.pageSize,
          measureIndexList.pageIndex * measureIndexList.pageSize),
        measureTableList: rows,
        pageIndex: measureIndexList.pageIndex,
        measurePageResultsNoInstance: pageResultsNoInstance?.slice((measureIndexList.pageIndex - 1) * measureIndexList.pageSize,
          measureIndexList.pageIndex * measureIndexList.pageSize) || [],
        measureTableListNoInstance: pageResultsNoInstance || [],
      },
      netWorkTreeParam: createTaskAllInfo.current.netWorkTreeParam,
      unitTree: { ...createTaskAllInfo.current.unitTree, checkedKeysByUnit: selectedKeys },
      objectTable: objTableChildRef.current.getClearObject(),
    });
  };

  // 操作测量对象表格回调
  const addByObjectCallBack = (measureTableData, measureObjectCheckedRow, pageResultsNoInstance) => {
    dispatch({
      type: 'setResourceLists',
      isRefreshByResource: false,
      measureIndexList: {
        ...createTaskAllInfo.current.measureIndexList,
        measurePageResults: measureTableData.slice((measureIndexList.pageIndex - 1) * measureIndexList.pageSize,
          measureIndexList.pageIndex * measureIndexList.pageSize),
        measureTableList: measureTableData,
        pageIndex: measureIndexList.pageIndex,
        measurePageResultsNoInstance: pageResultsNoInstance.slice((measureIndexList.pageIndex - 1) * measureIndexList.pageSize,
          measureIndexList.pageIndex * measureIndexList.pageSize),
        measureTableListNoInstance: pageResultsNoInstance,
      },
      netWorkTreeParam: createTaskAllInfo.current.netWorkTreeParam,
      unitTree: createTaskAllInfo.current.unitTree,
      objectTable: { ...createTaskAllInfo.current.objectTable, measureObjectCheckedRow },
    });
  };

  // 容量评估：dnName不使用真实dnName，需要自定义
  const getDnName = (type, checkedNetName) => {
    if (type === INDICATOR_TYPE_MAPS.HOST) {
      return 'host';
    } else if (type === INDICATOR_TYPE_MAPS.POD) {
      return 'pod';
    } else if (type === INDICATOR_TYPE_MAPS.DOCKER) {
      return 'docker';
    } else {
      return checkedNetName;
    }
  };

  // 获取单个指标信息
  const preIndicatorInfo = () => {
    const { checkedDn, measUnitKey, measTypeKey, checkedNetType, originalValue, displayValue, checkedNetName,
      indexGroupName, measureIndex, unit, checkedNetId, hasMeasObj, indexId, resourceTypeKey, eamNodeType,
    } = measureData.current;
    return {
      dn: checkedDn,
      moType: checkedNetType,
      measUnitKey,
      measUnitName: indexGroupName,
      measTypeKey,
      indexName: measureIndex,
      displayValue,
      originalValue,
      unit,
      dnName: getDnName(eamNodeType, checkedNetName),
      hasMeasObj,
      indexId,
      checkedNetId,
      resourceTypeKey,
    };
  };

  // 智能容灾：判断一个成员所选指标是否超过20
  const isLimitByMember = (measureTableData) => {
    let filterLists = measureTableData.filter(item => item[1].vnfDn === currentDn.current);
    return filterLists.length;
  };

  const handleGetIndicatorInstancesParams = indicatorArr => {
    if (measureData.current.resourceTypeKey === measureData.current.deploymentMoType ||
      measureData.current.resourceTypeKey.includes(measureData.current.deploymentMoType)) {
      return {
        solutionId: solutionIdRef.current.id,
        deploymentMoType: '',
        indicators: indicatorArr,
      };
    }

    const newIndicatorArr = indicatorArr.map(item => ({ ...item, moType: measureData.current.resourceTypeKey }));
    return {
      solutionId: solutionIdRef.current.id,
      deploymentMoType: measureData.current.deploymentMoType,
      indicators: newIndicatorArr,
    };
  };

  // 网元类型下，需要指标归一：指标异常检测
  const getIndicatorInstanceByUnitTree = (indicatorInfo, selectedKeys, isUnit, isAllCheck, oldMeasureData) => {
    dispatch({ type: 'setIsSourceLoading', isSourceLoading: true });
    let isCapacityChildTree = isCapacity && (nodeType === TEMPLATE_TYPE.CAPACITY) && !isTreeRoot;
    let apiFn = isCapacityChildTree ? api.getCapacityIndicatorInstances : api.getIndicatorInstances;
    let param = isCapacityChildTree ? {
      indicators: indicatorInfo,
      rootDn: rootMoTypeAndDn.dn,
      rootMoType: rootMoTypeAndDn.moType,
    } : handleGetIndicatorInstancesParams(indicatorInfo);
    apiFn(param, res => {
      dispatch({ type: 'setIsSourceLoading', isSourceLoading: false });
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        modal.error($t('kpi.task.common.error.tip'), res.resultMessage);
        return;
      }

      let data = res.data ? JSON.parse(res.data) : [];
      let maxLen = ((nodeType === TEMPLATE_TYPE.CAPACITY && !isTreeRoot) || nodeType === TEMPLATE_TYPE.KPI) ? maxIndicatorLength : MAX_INDICATOR;
      if (data.length > maxLen && isAllCheck) {// 全选 数量>1 截取前3501
        modal.info($t('kpi.task.common.tip'), $t('kpi.new.task.resource.measure.all.check.limit.num'));
        return;
      }
      if (data.length) {
        handleIndicatorInstanceByUnitTreeResp(data, isUnit, oldMeasureData, maxLen, selectedKeys);
      }
      if (isCapacityChildTree && data.length === 0) {
        modal.error($t('common.error'), $t('kpi.new.task.resource.measure.instance.noChild'));
      }
    }, err => {
      dispatch({ type: 'setIsSourceLoading', isSourceLoading: false });
    });
  };

  const handleIndicatorInstanceByUnitTreeResp = (respData, isUnit, oldMeasureData, maxLen, selectedKeys) => {
    let data = [...respData];
    let measureTableData = [...measureIndexList.measureTableList];
    let noInstanceTableData = [...(measureIndexList.measureTableListNoInstance ?? [])];
    data = data.filter(item => {
      let nodeId = `${item.dn}${item.resourceTypeKey}${item.deploymentMoType ?? item.moType}${item.measUnitName}${item.measUnitKey}${item.measTypeKey}`;
      nodeId = isUnit ? nodeId : `${nodeId}_${item.indexId}_${item.displayValue}`;
      let repeatIndex = measureTableData.findIndex(item => item[0] === nodeId);
      return repeatIndex === -1;
    });

    let pageRes = getMeasureTable(data, false, KPI_TREND_PREDICT_TYPE.INDEPENDENT, measureTableData.length);
    if (measureTableData.length + pageRes.length > maxLen) {
      setUnitTree({ checkedKeysByUnit: JSON.parse(JSON.stringify(unitTree.checkedKeysByUnit)) });
      let content = $t('kpi.new.task.resource.measure.table.limit.num');
      if (nodeType === TEMPLATE_TYPE.CAPACITY && !isTreeRoot) {
        content = $t('kpi.new.task.resource.measure.limit.num') + CAPACITY_PER_NODE_LIMIT;
      } else if (nodeType === TEMPLATE_TYPE.KPI) {
        content = $t('kpi.new.task.association.indicator.limit.num', [maxLen]);
      }

      modal.info($t('kpi.task.common.tip'), content);
      measureData.current = isUnit ? { ...oldMeasureData } : measureData.current;
      return;
    }

    let pageResultsNoInstance = [];
    if (isAutoUpdateMO) {
      pageResultsNoInstance = getPageResultsNoInstance(data);
    }
    if (maxIndicatorLength &&
      (measureTableData.length + pageRes.length > maxIndicatorLength)) {
      if (isAutoUpdateMO) {
        modal.info($t('common.tip'), $t('kpi.new.task.instance.limit.max', [maxIndicatorLength]));
        setUnitTree({ checkedKeysByUnit: JSON.parse(JSON.stringify(unitTree.checkedKeysByUnit)) });
        measureData.current = isUnit ? { ...oldMeasureData } : measureData.current;
        return;
      }
      modal.confirm($t('kpi.task.common.confirm.tip'), $t('kpi.new.task.instance.limit.num', [maxIndicatorLength]), () => {
        let operateFn = isUnit ? addByUnitCallBack : addByObjectCallBack;
        operateFn([...measureTableData, ...pageRes], selectedKeys, [...noInstanceTableData, ...pageResultsNoInstance]);
      }, () => {
        setUnitTree({ checkedKeysByUnit: JSON.parse(JSON.stringify(unitTree.checkedKeysByUnit)) });
        measureData.current = isUnit ? { ...oldMeasureData } : measureData.current;
      }, true);
    } else {
      let operateFn = isUnit ? addByUnitCallBack : addByObjectCallBack;
      operateFn([...measureTableData, ...pageRes], selectedKeys, [...noInstanceTableData, ...pageResultsNoInstance]);
    }
  };

  // 测量指标表格API
  const measureTableListeners = {
    getPreRow(rowId, len, operateParam, operateFromType) {
      let row;
      const { checkedDn, measUnitKey, measTypeKey, measureUnitId, checkedNetType, originalValue, displayValue,
        checkedNetName, indexGroupName, measureIndex, unit, checkedNetId, hasMeasObj,
        indexId, resourceTypeKey, eamNodeType, clusterName, siteId } = measureData.current;
      let commonPar = {
        checkedDn, measUnitKey, measTypeKey, measureUnitId, unit, originalValue, displayValue,
        checkedNetId, hasMeasObj, indexId, resourceTypeKey, upperThreshold: '', lowerThreshold: '',
        checkedNetType, indexGroupName, measureIndex,
      };

      if (isDrSwitch) { // 智能容灾主指标
        row = [
          rowId, {
            ...commonPar,
            isDisplay: true,
            groupMemberId: currentMember.current,
            vnfDnName: currentMemberName.current,
            vnfDn: currentDn.current,
          }, currentMemberName.current, checkedNetName, indexGroupName, measureIndex, displayValue,
          getCustomTableCol('derivedVal', false, { unit, rowId, derivedKpiInfo: {} }),
          getCustomTableCol('mainThreshold', false, { unit, rowId, thresholds: [] }),
          getCustomTableCol('auxiliaryIndicator', false, {
            unit, rowId,
            kpiAuxiliaryIndicators: [],
            memberInfo: { memberId: currentMember.current, dn: currentDn.current, memberName: currentMemberName.current },
          }),
          getCustomTableCol('auxiliaryInfo', false, { rowId, auxiliaryKpiInfos: [] }),
          getCustomToggle(true, rowId),
          getCustomOperate(operateParam, operateFromType),
        ];
      } else if (isAuxiliaryKpi) { // 智能容灾辅助指标
        row = [
          rowId, {
            ...commonPar,
            columnName: '',
            groupMemberId: currentMember.current,
            vnfDnName: currentMemberName.current,
            vnfDn: currentDn.current,
          }, checkedNetType, checkedNetName, indexGroupName, measureIndex, displayValue,
          getCustomTableCol(false, { unit, rowId, auxiliaryThresholds: [] }),
          getAuxiliaryTextField(len, 'indicatorZhNames', ''),
          getAuxiliaryTextField(len, 'indicatorEnNames', ''),
          getCustomShowCol(true, rowId),
          getCustomOperate(operateParam, operateFromType)];
      } else if (hashName === TREND_MENU && measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.UNITE) {
        row = [rowId, {
          ...commonPar,
          clusterName, siteId,
        }, checkedNetType, checkedNetName, siteId, indexGroupName, measureIndex,
        displayValue || '', getCustomAuxiliaryKpi(false, { auxiliaryIndicator: [], rowId }),
        getCustomTextField(len, 'upperThreshold', ''),
        getCustomTextField(len, 'lowerThreshold', ''),
        getCustomOperate(operateParam, operateFromType)];
      } else if (hashName === TREND_MENU && measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.INDEPENDENT && !isTrendAuxiliaryKpi) {
        row = [rowId, commonPar, checkedNetType, checkedNetName, indexGroupName, measureIndex, displayValue || '',
          getAssociatedDataSet(false, { historyIndicatorId: '', historyIndicatorName: '', rowId }),
          '', '', getCustomOperate(operateParam, operateFromType)];
      } else if (measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE) {// 双机指标
        row = [rowId, { ...commonPar, clusterName: checkedNetId }, checkedNetId, checkedNetType, checkedNetName,
          indexGroupName, measureIndex, displayValue || '',
          getAssociatedDataSet(false, { historyIndicatorId: '', historyIndicatorName: '', rowId }),
          '', '', getCustomOperate(operateParam, operateFromType)];
      } else {
        let param = nodeType === TEMPLATE_TYPE.CAPACITY ? { eamNodeType } : {};
        row = [rowId, { ...commonPar, ...param }, measureData.current.checkedNetType, checkedNetType, checkedNetName, indexGroupName, measureIndex,
          displayValue || '',
          getCustomTextField(len, 'upperThreshold', ''),
          getCustomTextField(len, 'lowerThreshold', ''),
          getCustomOperate(operateParam, operateFromType)];
      }

      return row;
    },
    returnModalTip(oldMeasureData, tip) {
      modal.info($t('kpi.task.common.tip'), tip);
      setUnitTree({ checkedKeysByUnit: UnitTreeChildRef.current.getCheckedKeysByUnit() });
      measureData.current = { ...oldMeasureData };
    },
    addByUnit(selectedKeys, oldMeasureData) {
      let rows = [...measureIndexList.measureTableList];
      const { id, measureUnitId, indexGroupName } = measureData.current;
      let isTrend = hashName === TREND_MENU && measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.UNITE;
      let maxLen = getMaxKpiNum(measureIndexList, hashName, perClusterGroupKpiNum, maxIndicatorLength);
      if (maxLen && rows.length >= maxLen) {
        let tip = isTrend ? $t('indicator.prediction.limit.num') : $t('kpi.new.task.resource.measure.limit.num');
        this.returnModalTip(oldMeasureData, tip + maxLen);
        return 'noVerify';
      }

      if (isDrSwitch && isLimitByMember(rows) >= MAX_INDICATOR_SWITCH) {
        this.returnModalTip(oldMeasureData, $t('kpi.new.task.switch.measure.limit.num'));
        return 'noVerify';
      }

      // 需要做去重处理
      let repeatIndex = rows.findIndex(item => item[0] === id);
      if (repeatIndex === -1) { // 指标列表中不存在此指标才插入列表中去
        rows.push(this.getPreRow(id, rows.length,
          { measureId: id, measureUnitId, parentId: indexGroupName },
          'object'));
        addByUnitCallBack(rows, selectedKeys);
      }
      return null;
    },
    addByObj(rowId, rowObjId) {
      let rows = [...measureIndexList.measureTableList];
      let measureTable = [...measureIndexList.measureTableList];
      let isTrend = hashName === TREND_MENU && measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.UNITE;
      let maxLen = getMaxKpiNum(measureIndexList, hashName, perClusterGroupKpiNum, maxIndicatorLength);
      if (maxLen && rows.length >= maxLen) {
        let tip = isTrend ? $t('indicator.prediction.limit.num') : $t('kpi.new.task.resource.measure.limit.num');
        modal.info($t('kpi.task.common.tip'), tip + maxLen);
        setObjectTable({ measureObjectCheckedRow: objTableChildRef.current.getObjectCheckedRow() });
        return 'noVerify';
      }

      if (isDrSwitch && rows.length && isLimitByMember(measureTable) >= MAX_INDICATOR_SWITCH) {
        modal.info($t('kpi.task.common.tip'), $t('kpi.new.task.switch.measure.limit.num'));
        setObjectTable({ measureObjectCheckedRow: objTableChildRef.current.getObjectCheckedRow() });
        return 'noVerify';
      }

      // 去重处理
      let repeatIndex = measureTable.findIndex(item => item[0] === rowId);
      if (repeatIndex === -1) {
        let lenNum = rows.length;
        rows.push(this.getPreRow(rowId, lenNum, { measureId: rowId, checkedRow: rowObjId }, 'table'));
      }
      return rows;
    },
  };

  const deleteMeasureDataCallBack = (deleteParams, deleteType, type, deploymentMoType) => {
    dispatch({
      type: 'setDeleteMeasureDataParam',
      deleteMeasureDataParam: { deleteParams, deleteType, type, deploymentMoType },
    });
  };

  const getCustomOperate = (deleteParams, deleteType, type, deploymentMoType) => {
    return (
      <TextButton text={$t('kpi.task.delete')} onClick={() => deleteMeasureDataCallBack(deleteParams, deleteType, type, deploymentMoType)} />
    );
  };

  // 指标别名，上下阈值、展示列输入框change回调
  const changeCallBack = (val, index, key) => {
    createTaskAllInfo.current.measureIndexList[key][index] = val;
    dispatch({
      type: 'setMeasureIndexList',
      measureIndexList: {
        ...createTaskAllInfo.current.measureIndexList,
        [key]: [...createTaskAllInfo.current.measureIndexList[key]],
      },
    });
  };

  // 指标趋势预测：辅助指标
  const openAuxiliaryDialog = (isConfigure, { auxiliaryIndicator, rowId }) => {
    dispatch({
      type: 'setAuxiliaryPar',
      auxiliaryPar: {
        ...auxiliaryPar,
        isAuxiliaryKpiOpen: true,
        auxiliaryDialogData: {
          auxiliaryIndicator: auxiliaryIndicator || [],
          isNew: !isConfigure,
          solutionDn: currentDn.current,
          mainIndicatorInfo: JSON.parse(JSON.stringify(createTaskAllInfo.current)),
          rowId,
        },
      },
    });
  };

  // 指标趋势预测：辅助指标
  const getCustomAuxiliaryKpi = (isConfigure, data) => {
    return (
      <TextButton text={isConfigure ? $t('task.common.configured') : $t('task.common.not.configured')}
        onClick={() => openAuxiliaryDialog(isConfigure, data)}
        disabled={taskPageType === TASK_PAGE_TYPE.VIEW && !isConfigure}
      />
    );
  };

  // 指标趋势预测：关联导入指标弹框
  const openAssociatedDataSetDialog = (isConfigure, { historyIndicatorId, historyIndicatorName, rowId }) => {
    if (taskPageType === TASK_PAGE_TYPE.VIEW && !isConfigure) {
      return;
    }

    dispatch({
      type: 'setAssociatedDataSetPar',
      associatedDataSetPar: {
        ...associatedDataSetPar,
        isAssociatedDataSetOpen: true,
        associatedDataSetDialogData: {
          historyIndicatorId: historyIndicatorId || '',
          historyIndicatorName: historyIndicatorName || '',
          isNew: !isConfigure,
          mainIndicatorInfo: JSON.parse(JSON.stringify(createTaskAllInfo.current)),
          rowId,
        },
      },
    });
  };

  // 指标趋势预测：关联导入指标
  const getAssociatedDataSet = (isConfigure, data) => {
    return (
      <TextButton text={isConfigure ? $t('task.common.configured') : $t('task.common.not.configured')}
        onClick={() => openAssociatedDataSetDialog(isConfigure, data)}
        disabled={taskPageType === TASK_PAGE_TYPE.VIEW && !isConfigure}
      />
    );
  };

  // 智能容量评估：上下阈值
  const getCustomTextField = (index, key, value) => {
    if (isCapacity || isProfileKpi) {
      createTaskAllInfo.current.measureIndexList[key][index] = value;
      return (
        (taskPageType !== TASK_PAGE_TYPE.VIEW_OTHER && taskPageType !== TASK_PAGE_TYPE.VIEW) ?
          <TextField placeholder={$t('kpi.task.common.please.enter')} id={`${key}_${index}`}
            inputStyle={{ width: '100%' }} hintType='tip' autoComplete="off"
            key={`${key}_${new Date().getTime()}`}
            value={value}
            disabled={(nodeType === TEMPLATE_TYPE.TWO_START || nodeType === TEMPLATE_TYPE.CAPACITY_TWO) &&
              index !== 0}
            validator={(val, id) => validate(['cmpValidChar', 'validThreshold'], val, id)}
            ref={textField => customIndicatorRef.current[key][index] = textField}
            onChange={(val) => changeCallBack(val, index, key)}
          /> :
          value
      );
    } else {
      return '';
    }
  };

  const getAuxiliaryTextField = (index, key, value) => {
    createTaskAllInfo.current.measureIndexList[key][index] = value;
    return (
      <TextField placeholder={$t('kpi.task.common.please.enter')} id={`${key}_${index}`}
        inputStyle={{ width: '100%' }} hintType='tip' autoComplete="off"
        value={value}
        validator={(val, id) => validate(['required', 'cmpValidChar', 'checkLength'], val, id, '', MAX_LENGTH_64)}
        ref={textField => customIndicatorRef.current[key][index] = textField}
        disabled={taskPageType === TASK_PAGE_TYPE.VIEW}
        onChange={(val) => changeCallBack(val, index, key)}
      />
    );
  };

  const deleteInputData = (findIndex) => {
    if (isCapacity) {
      createTaskAllInfo.current.measureIndexList.upperThreshold.splice(findIndex, 1);
      createTaskAllInfo.current.measureIndexList.lowerThreshold.splice(findIndex, 1);
    }

    if (isAuxiliaryKpi) {
      // 指标别名、展示列
      createTaskAllInfo.current.measureIndexList.indicatorZhNames.splice(findIndex, 1);
      createTaskAllInfo.current.measureIndexList.indicatorEnNames.splice(findIndex, 1);
    }
  };

  const updateMeasureTable = (measureTableData, findIndex) => {
    if (isCapacity) {
      createTaskAllInfo.current.measureIndexList.upperThreshold.splice(findIndex, 1);
      createTaskAllInfo.current.measureIndexList.lowerThreshold.splice(findIndex, 1);
      measureTableData.forEach((item, index) => {
        item[8] = getCustomTextField(index, 'upperThreshold',
          createTaskAllInfo.current.measureIndexList.upperThreshold[index]);
        item[9] = getCustomTextField(index, 'lowerThreshold',
          createTaskAllInfo.current.measureIndexList.lowerThreshold[index]);
      });
    }

    if (isAuxiliaryKpi) {
      // 指标别名、展示列
      createTaskAllInfo.current.measureIndexList.indicatorZhNames.splice(findIndex, 1);
      createTaskAllInfo.current.measureIndexList.indicatorEnNames.splice(findIndex, 1);
      measureTableData.forEach((item, index) => {
        item[8] = getAuxiliaryTextField(index, 'indicatorZhNames',
          createTaskAllInfo.current.measureIndexList.indicatorZhNames[index]);
        item[9] = getAuxiliaryTextField(index, 'indicatorEnNames',
          createTaskAllInfo.current.measureIndexList.indicatorEnNames[index]);
      });
    }

    return measureTableData;
  };

  const getTableColumns = () => {
    if (isCapacity && nodeType === TEMPLATE_TYPE.CAPACITY && !isTreeRoot) {
      return CAPACITY_VIEW_MEASURE_TABLE_COLUMNS;
    } else if (isCapacity) {
      return CAPACITY_MEASURE_TABLE_COLUMNS;
    } else if (isProfileKpi) {
      return PROFILE_INDICATOR_TABLE_COLUMNS;
    } else if (hashName === TREND_MENU && measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.UNITE) {
      return TREND_TABLE_COLUMNS;
    } else if (hashName === TREND_MENU && measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.INDEPENDENT) {// 独立指标
      return INDEPENDENT_MEASURE_TABLE_COLUMNS;
    } else if (measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE) {// 双机指标
      return CLUSTER_MEASURE_TABLE_COLUMNS_NEW;
    } else {
      return MEASURE_TABLE_COLUMNS;
    }
  };

  const deleteCommon = (measureId) => {
    let measureTableData = measureIndexList.measureTableList;
    let findIndex = measureTableData.findIndex(item => item[0] === measureId);
    if (findIndex !== -1) {
      measureTableData.splice(findIndex, 1);
      measureTableData = updateMeasureTable(measureTableData, findIndex);
    }

    return measureTableData;
  };

  const deleteMeasureData = ({ deleteParams, deleteType, deploymentMoType }) => {
    const isDeleteSpecifiedEle = measureIndexList.resourceRelatedMode === RESOURCE_TYPE.NETWORK_INSTANCE ||
      (hashName === TREND_MENU && measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.UNITE) ||
      (measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE);
    if (deleteType === 'object') {
      let { measureId, measureUnitId } = deleteParams;
      let restUnitData = [];
      if (UnitTreeChildRef.current && isDeleteSpecifiedEle) {
        restUnitData = deleteSpecifiedEleFromArr(measureUnitId, createTaskAllInfo.current.unitTree.checkedKeysByUnit);
      } else if (UnitTreeChildRef.current && measureIndexList.resourceRelatedMode === RESOURCE_TYPE.NETWORK_TYPE) {
        let unitCheckedLists = createTaskAllInfo.current.unitTree.checkedKeysByUnit;
        let measureTableData = [...measureIndexList.measureTableList];
        restUnitData = unitCheckedLists.filter(item => {
          let spliceIndex = measureTableData.findIndex(row => (row[0].indexOf(item) > -1) &&
            row[0] !== measureId);
          return spliceIndex !== -1;
        });
      } else { }

      let rows = deleteCommon(measureId);
      let currentPageIndex = (measureIndexList.measurePageResults.length === 1 && measureIndexList.pageIndex !== 1) ?
        (measureIndexList.pageIndex - 1) : measureIndexList.pageIndex;
      dispatch({
        type: 'setResourceLists',
        isRefreshByResource: false,
        measureIndexList: {
          ...createTaskAllInfo.current.measureIndexList,
          measurePageResults: rows.slice((currentPageIndex - 1) * measureIndexList.pageSize,
            currentPageIndex * measureIndexList.pageSize),
          measureTableList: rows,
          pageIndex: currentPageIndex,
        },
        netWorkTreeParam: createTaskAllInfo.current.netWorkTreeParam,
        unitTree: { ...createTaskAllInfo.current.unitTree, checkedKeysByUnit: [...restUnitData] },
        objectTable: createTaskAllInfo.current.objectTable,
      });
    } else {
      const { measureIndexList, netWorkTreeParam, unitTree, objectTable } = createTaskAllInfo.current;
      let { measureId, checkedRow } = deleteParams;
      let restObjectData = objTableChildRef.current.getObjectCheckedRow();
      if (measureId.includes(measureData.current.rowId)) {// 判断是否是同一指标的测量对象，非当前指标不需要更新测量对象表格勾选状态
        if (UnitTreeChildRef.current && isDeleteSpecifiedEle) {
          restObjectData = deleteSpecifiedEleFromArr(checkedRow, restObjectData);
        } else {
          let measureTableData = [...measureIndexList.measureTableList];
          restObjectData = restObjectData.filter(item => {
            let spliceIndex = measureTableData.findIndex(row => {
              return (row[0].indexOf(item) > -1) && row[0] !== measureId;
            });
            return spliceIndex !== -1;
          });
        }
      }
      let rows = deleteCommon(measureId);
      let currentPageIndex = (measureIndexList.measurePageResults.length === 1 && measureIndexList.pageIndex !== 1) ?
        (measureIndexList.pageIndex - 1) : measureIndexList.pageIndex;
      dispatch({
        type: 'setResourceLists',
        isRefreshByResource: false,
        measureIndexList: {
          ...createTaskAllInfo.current.measureIndexList,
          measurePageResults: rows.slice((currentPageIndex - 1) * measureIndexList.pageSize,
            currentPageIndex * measureIndexList.pageSize),
          measureTableList: rows,
          pageIndex: currentPageIndex,
        },
        netWorkTreeParam,
        unitTree,
        objectTable: { ...createTaskAllInfo.current.objectTable, measureObjectCheckedRow: restObjectData },
      });
    }
  };

  const deleteMeasureDataOfNoInstanceCommon = (isUnit, measureId, restUnitData, restObjectData) => {
    // 更新已选网元类型指标表
    let noInstanceTableData = [...measureIndexList.measureTableListNoInstance];
    let measureTableList = [...measureIndexList.measureTableList];
    // 查找需要删除的索引
    let findIndex = noInstanceTableData.findIndex(item => item[0] === measureId);
    if (findIndex !== -1) {
      let [deletedId] = noInstanceTableData.splice(findIndex, 1)[0];

      if (isUnit) {
        measureTableList = measureTableList.filter(item => {
          let id = `${item[1].resourceTypeKey}_${item[1].measTypeKey}_`;
          return id !== deletedId;
        });
      } else {
        measureTableList = measureTableList.filter(item => {
          let id = `${item[1].resourceTypeKey}_${item[1].measTypeKey}_${item[1].originalValue}`;
          return id !== deletedId;
        });
      }
    }

    let currentPageIndex = (measureIndexList.measurePageResultsNoInstance.length === 1 && measureIndexList.pageIndex !== 1) ?
      measureIndexList.pageIndex - 1 : measureIndexList.pageIndex;

    dispatch({
      type: 'setResourceLists',
      isRefreshByResource: false,
      measureIndexList: {
        ...createTaskAllInfo.current.measureIndexList,
        pageIndex: currentPageIndex,
        measurePageResultsNoInstance: noInstanceTableData.slice((currentPageIndex - 1) * measureIndexList.pageSize,
          currentPageIndex * measureIndexList.pageSize),
        measureTableListNoInstance: noInstanceTableData,
        measurePageResults: [],
        measureTableList,
      },
      netWorkTreeParam: createTaskAllInfo.current.netWorkTreeParam,
      unitTree: restUnitData,
      objectTable: restObjectData,
    });
  };

  // 网元类型-自动更新网元-删除已选指标
  const deleteMeasureDataOfNoInstance = ({ deleteParams, deleteType }) => {
    let { measureId } = deleteParams;
    let measureTableListNoInstance = [...measureIndexList.measureTableListNoInstance];
    // 删除的指标没有测量对象
    if (deleteType === 'object') {
      // 更新测量单元树的选择状态
      let unitCheckedLists = createTaskAllInfo.current.unitTree.checkedKeysByUnit;
      let restUnitData = unitCheckedLists.filter(item => {
        let spliceIndex = measureTableListNoInstance.findIndex(row => {
          let id = `${row[1].resourceTypeKey}${row[1].indexGroupName}${row[1].measUnitKey}${row[1].measTypeKey}`;
          return id === item && row[0] !== measureId;
        });
        return spliceIndex !== -1;
      });
      let unitData = { ...createTaskAllInfo.current.unitTree, checkedKeysByUnit: [...restUnitData] };
      deleteMeasureDataOfNoInstanceCommon(true, measureId, unitData, createTaskAllInfo.current.objectTable);
    } else {
      let restObjectData = objTableChildRef.current.getObjectCheckedRow();
      measureTableListNoInstance = [...createTaskAllInfo.current.measureIndexList.measureTableListNoInstance];
      // 判断是否是同一指标的测量对象，非当前指标不需要更新测量对象表格勾选状态
      let currentId = `${measureData.current.resourceTypeKey}_${measureData.current.measTypeKey}`;
      if (measureId.includes(currentId)) {
        restObjectData = restObjectData.filter(item => {
          let spliceIndex = measureTableListNoInstance.findIndex(row => {
            let id = `${row[1].indexId}_${row[1].displayValue}`;
            return id === item && row[0] !== measureId;
          });
          return spliceIndex !== -1;
        });
      }
      let objectData = { ...createTaskAllInfo.current.objectTable, measureObjectCheckedRow: restObjectData };
      deleteMeasureDataOfNoInstanceCommon(false, measureId, createTaskAllInfo.current.unitTree, objectData);
    }
  };

  // 切换指标预测类型
  const switchPredictScenario = (val) => {
    if (val === beforePredictSelect.current) {
      return;
    }
    beforePredictSelect.current = val;
    let resourceRelatedMode = val === KPI_TREND_PREDICT_TYPE.TWO_NODE ?
      RESOURCE_TYPE.NETWORK_TYPE : RESOURCE_TYPE.NETWORK_INSTANCE;
    switchResourceRelatedType(false, resourceRelatedMode, val);
  };

  // 切换是否自动更新网元
  const onChangeIsAutoUpdate = value => {
    clearAll(value);
  };

  // 切换资源关联方式
  const switchResourceRelatedType = (isSwitch, val, indicatorPredictScenario = 0) => {
    if (val === beforeSelect.current && isSwitch) {
      return;
    }
    beforeSelect.current = val;
    measureData.current = {};
    dispatch({
      type: 'setClusterResourceLists',
      isRefreshByResource: true,
      measureIndexList: {
        ...createTaskAllInfo.current.measureIndexList,
        resourceRelatedMode: val,
        measurePageResults: [],
        measureTableList: [],
        pageIndex: 1,
        pageSize: 50,
        measureKey: new Date().getTime(),
        solutionId: indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE ?
          '' : createTaskAllInfo.current.measureIndexList.solutionId,
        solutionName: indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE ?
          '' : createTaskAllInfo.current.measureIndexList.solutionName,
        indicatorPredictScenario,
        isAutoUpdateMO: IS_AUTO_KEY.NO,
        measurePageResultsNoInstance: [],
        measureTableListNoInstance: [],
      },
      clusterGroupParam: { tableResult: [], checkedRows: [] },
      netWorkTreeParam: {
        ...createTaskAllInfo.current.netWorkTreeParam,
        networkEle: '',
        isFilterSuccess: false,
        netWorkTreeData: [],
        checkedKeysByNetWork: [],
        expandedKeysByNetWork: (val === RESOURCE_TYPE.NETWORK_INSTANCE ||
          (hashName === TREND_MENU && indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.UNITE)) ?
          [`${solutionId ? solutionId : '/'}`] : '/',
      },
      unitTree: {
        ...createTaskAllInfo.current.unitTree,
        isUnitDisable: false,
        isSelectDisable: false,
        isTreeDisable: false,
        measureType: 'all',
        measureUnit: '',
        measureUnitTreeData: [{ text: 'All', id: '/', children: [], enableCheckbox: false, isLeaf: false }],
        expandedKeysByMeasureUnit: ['/'],
        checkedKeysByUnit: [],
      },
      objectTable: objTableChildRef.current.getClearObject(),
    });

    if (taskPageType === TASK_PAGE_TYPE.MODIFY_OTHER) {
      NEChildRef.current.queryNetWorkTree(val, indicatorPredictScenario);
    }
  };

  const clearAll = isAutoUpdate => {
    dispatch({
      type: 'setResourceLists',
      isRefreshByResource: false,
      measureIndexList: {
        ...createTaskAllInfo.current.measureIndexList,
        measurePageResults: [],
        measureTableList: [],
        upperThreshold: [],
        lowerThreshold: [],
        indicatorZhNames: [],
        indicatorEnNames: [],
        measurePageResultsNoInstance: [],
        measureTableListNoInstance: [],
        pageIndex: 1,
        pageSize: 50,
        isAutoUpdateMO: isAutoUpdate ?? measureIndexList.isAutoUpdateMO,
        solutionId: solutionIdRef.current.id,
        solutionName: solutionIdRef.current.name,
      },
      netWorkTreeParam: createTaskAllInfo.current.netWorkTreeParam,
      unitTree: { ...createTaskAllInfo.current.unitTree, checkedKeysByUnit: [] },
      objectTable: objTableChildRef.current.getClearObject(),
    });
    customIndicatorRef.current = {
      upperThreshold: [],
      lowerThreshold: [],
      indicatorZhNames: [],
      indicatorEnNames: [],
    };
  };

  // 删除当前页
  const clearCurrentPage = () => {
    const { measureIndexList } = createTaskAllInfo.current;
    if (!measureIndexList.measurePageResults.length) {
      return;
    }

    let measureTableData = [...measureIndexList.measureTableList];
    let measureNoInstanceTableData = [...measureIndexList.measureTableListNoInstance];
    let currentPageIndex = measureIndexList.pageIndex === 1 ? 1 : measureIndexList.pageIndex - 1;

    if (isAutoUpdateMO) {
      let currentTableKeys = measureIndexList.measurePageResultsNoInstance.map(item => item[0]);
      let findIndex = measureNoInstanceTableData.findIndex(item => currentTableKeys.includes(item[0]));
      while (findIndex !== -1) {
        measureNoInstanceTableData.splice(findIndex, 1);
        findIndex = measureNoInstanceTableData.findIndex(item => currentTableKeys.includes(item[0]));
      }

      // 根据删除的type，同步删除实例列表
      measureTableData = measureTableData.filter(item => {
        let uniqueTypeId = `${item[1].resourceTypeKey}_${item[1].measTypeKey}_${item[1].originalValue}`;
        return !currentTableKeys.includes(uniqueTypeId);
      });

      dispatch({
        type: 'setResourceLists',
        isRefreshByResource: false,
        measureIndexList: {
          ...createTaskAllInfo.current.measureIndexList,
          measurePageResults: [],
          measureTableList: measureTableData,
          pageIndex: currentPageIndex,
          measurePageResultsNoInstance: measureNoInstanceTableData.slice((currentPageIndex - 1) * measureIndexList.pageSize,
            currentPageIndex * measureIndexList.pageSize),
          measureTableListNoInstance: measureNoInstanceTableData,
        },
        netWorkTreeParam: createTaskAllInfo.current.netWorkTreeParam,
        unitTree: { ...createTaskAllInfo.current.unitTree, checkedKeysByUnit: getSelectedNodes(measureTableData) },
        objectTable: {
          ...createTaskAllInfo.current.objectTable,
          measureObjectCheckedRow: getCheckedObjRows(measureTableData),
        },
      });
    } else {
      let currentTableKeys = measureIndexList.measurePageResults.map(item => item[0]);
      let findIndex = measureTableData.findIndex(item => currentTableKeys.includes(item[0]));
      while (findIndex !== -1) {
        measureTableData.splice(findIndex, 1);
        deleteInputData(findIndex);
        findIndex = measureTableData.findIndex(item => currentTableKeys.includes(item[0]));
      }
      let rows = updateIndicatorTable(measureTableData);
      dispatch({
        type: 'setResourceLists',
        isRefreshByResource: false,
        measureIndexList: {
          ...createTaskAllInfo.current.measureIndexList,
          measurePageResults: rows.slice((currentPageIndex - 1) * measureIndexList.pageSize,
            currentPageIndex * measureIndexList.pageSize),
          measureTableList: rows,
          pageIndex: currentPageIndex,
        },
        netWorkTreeParam: createTaskAllInfo.current.netWorkTreeParam,
        unitTree: { ...createTaskAllInfo.current.unitTree, checkedKeysByUnit: getSelectedNodes(rows) },
        objectTable: {
          ...createTaskAllInfo.current.objectTable,
          measureObjectCheckedRow: getCheckedObjRows(rows),
        },
      });
    }
  };

  // 获取测量对象表格选中数组
  const getCheckedObjRows = (restObjectData) => {
    const { objectTable } = createTaskAllInfo.current;
    let checkedKpiRows = restObjectData.map(item => item[0]);
    let checkedObjRows = objectTable.measureObjectCheckedRow.filter(item => {
      let spliceIndex = checkedKpiRows.findIndex(row => (row.indexOf(item) > -1));
      return spliceIndex !== -1;
    });
    return checkedObjRows;
  };

  // 获取测量单元树选中节点
  const getSelectedNodes = (restObjectData) => {
    let selectedNodes = restObjectData.map(item => {
      let dn = getClusterKey(measureIndexList.resourceRelatedMode === RESOURCE_TYPE.NETWORK_INSTANCE, item[1],
        item[1].checkedDn, measureIndexList.indicatorPredictScenario);
      return `${dn}${item[1].resourceTypeKey}${item[4]}${item[1].measUnitKey}${item[1].measTypeKey}`;
    });

    // 测量对象表格有数据时，才需要保证测量单元当前节点要选中
    if (objectTable.objectTableList.length) {
      selectedNodes.push(measureData.current.rowId);
    }
    selectedNodes = [...new Set(selectedNodes)];
    return selectedNodes;
  };

  const queryMeasObjCount = callBack => {
    dispatch({ type: 'setIsLoadingShow', isLoadingShow: true });
    let { indicatorList } = getIndicatorParams();
    let arr = indicatorList.map(item => {
      return `${item.moType}~~${item.measUnitKey}`;
    });
    api.queryMeasObjectAmount(Array.from(new Set(arr)), resp => {
      dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
      if (resp && resp.resultCode === OPERATE_SUCCESS_CODE) {
        if (resp.data) {
          modal.confirm($t('kpi.task.common.confirm.tip'), $t('kpi.task.query.collect.indicators.max'), () => {
            callBack();
          });
        } else {
          callBack();
        }
      } else {
        callBack();
      }
    }, () => {
      dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
    });
  };

  // 上一步或下一步
  const goStep = ({ stepName, type }) => {
    let switchTabData = '';
    if (type !== 'next') {
      setResourceTab(stepName, switchTabData);
      return;
    }

    if (!verify()) {
      return;
    }

    let { param } = getIndicatorParams();
    if (hashName === TREND_MENU) {
      if (measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.UNITE) {
        setResourceTab(stepName, param);
      } else {
        validHistoryIndicator(stepName, param);
      }
    } else {
      queryMeasObjCount(() => { setResourceTab(stepName, param); });
    }
  };

  // 查看是否可以关联成功
  const validHistoryIndicator = (stepName, param) => {
    dispatch({ type: 'setIsLoadingShow', isLoadingShow: true });
    api.validAssociateHistoryIndicator({
      indicatorList: getIndicatorParams()?.indicatorList,
    }, res => {
      dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });

      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        let msg = res.resultMessage ? res.resultMessage : $t('task.valid.error');
        modal.error($t('kpi.task.common.error.tip'), msg);
        return;
      }

      if (res.data && !res.data.checkResult) {
        modal.error($t('kpi.task.common.error.tip'), res.data.describe);
        return;
      }

      setResourceTab(stepName, param);
    }, err => {
      dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
    });
  };

  const getTrendKey = ({ clusterName, siteId, dn }, predictScenario = KPI_TREND_PREDICT_TYPE.INDEPENDENT) => {
    let checkedDn;
    if (predictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE) {// 冷双机
      checkedDn = clusterName;
    } else {
      checkedDn = dn || '';
    }
    return checkedDn;
  };

  // 指标趋势预测：关联导入指标回调
  const configureDataSetCallBack = (associatedKpiInfo, rowId, mainIndicatorInfo) => {
    let indicatorList = getIndicatorParams(true).indicatorList;
    let findIndex = indicatorList.findIndex(item => {
      let checkedDn = getTrendKey(item, measureIndexList.indicatorPredictScenario);
      let id = `${checkedDn}${item.resourceTypeKey}${item.moType}${item.measUnitName}${item.measUnitKey}`;
      if (item.hasMeasObj === 'true' || item.hasMeasObj === true) {
        return rowId === `${id}${item.measTypeKey}_${item.indexId}_${item.displayValue}`;
      } else {
        return rowId === `${id}${item.measTypeKey}`;
      }
    });
    if (findIndex === -1) {
      return;
    }
    indicatorList[findIndex] = { ...indicatorList[findIndex], ...associatedKpiInfo };
    if (measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE) {// 双机
      indicatorList = indicatorList.map(item => ({ ...item, aggregateMos: getClusterGroupInfo(item.clusterName) }));
    }
    updateMeasureTableDataByInfo(indicatorList, false, mainIndicatorInfo, measureIndexList.indicatorPredictScenario);
  };

  const setResourceTab = (stepName, switchTabData) => {
    dispatch({
      type: 'setResourceTab',
      currentTab: stepName,
      isRefreshByResource: false,
      resourceCache: measureData.current,
      resourceData: switchTabData,
      checkedMeasureIndex: checkedMeasureIndexTable.current,
    });
  };

  const cancelModify = () => {
    setTaskPageType(TASK_PAGE_TYPE.VIEW);
    dispatch({
      type: 'setResourceLists',
      isRefreshByResource: false,
      measureIndexList: medianResourceInfo.current.measureIndexList,
      netWorkTreeParam: medianResourceInfo.current.netWorkTreeParam,
      unitTree: medianResourceInfo.current.unitTree,
      objectTable: medianResourceInfo.current.objectTable,
    });
  };

  const getResourceParam = (indicatorList) => ({
    updateIndicatorAuto: measureIndexList.isAutoUpdateMO,
    indicatorSelectType: hashName === TREND_MENU && measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.UNITE ?
      undefined : measureIndexList.resourceRelatedMode,
    indicatorPredictScenario: hashName === TREND_MENU ? measureIndexList.indicatorPredictScenario : undefined,
    indicatorList,
    solutionId: measureIndexList.solutionId,
    solutionName: measureIndexList.solutionName === $t('kpi.task.no.solution') ? '' : measureIndexList.solutionName,
    aggregateMachineGroupList: measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE ?
      JSON.stringify(clusterGroupParam.tableResult.map(item => ({
        clusterName: item.clusterName,
        moType: item.moType,
        solutionId: item.solutionId,
        solutionName: item.solutionName,
        aggregateMoList: item.aggregateMoList,
      }))) : undefined,
  });

  // 寻找指标所在的分组信息
  const getClusterGroupInfo = (clusterName) => {
    let currentGroupList = clusterGroupParam.tableResult.filter(group => group.clusterName === clusterName);
    if (currentGroupList.length) {
      return currentGroupList[0].aggregateMoList;
    }

    return [];
  };

  // 修改任务成功后需要刷新指标表格数据，数据里需要携带分组信息（修改-查看-修改）
  const getClusterGroupKpiParam = (indicatorList) => ({
    indicatorSelectType: measureIndexList.resourceRelatedMode,
    indicatorPredictScenario: measureIndexList.indicatorPredictScenario,
    indicatorList: indicatorList.map(item => ({ ...item, aggregateMos: getClusterGroupInfo(item.clusterName) })),
    solutionId: '',
    solutionName: '',
    aggregateMachineGroupList: JSON.stringify(clusterGroupParam.tableResult.map(item => ({
      clusterName: item.clusterName,
      moType: item.moType,
      solutionId: item.solutionId,
      solutionName: item.solutionName,
      aggregateMoList: item.aggregateMoList,
    }))),
  });

  // 获取指标表格相关信息，传给后台接口
  const getIndicatorListPar = (item, index, commonObj, isTrend, isTwoCluster) => {
    let isIndependent = hashName === TREND_MENU && measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.INDEPENDENT;
    let indicatorRelatedInfo = item[1];
    if (isCapacity || isProfileKpi) {// 容量评估和主机画像指标
      let otherParam = {};
      if (nodeType === TEMPLATE_TYPE.CAPACITY) {
        otherParam = { eamNodeType: indicatorRelatedInfo.eamNodeType };
      }
      return {
        ...commonObj,
        upperThreshold: createTaskAllInfo.current.measureIndexList.upperThreshold[index] || '',
        lowerThreshold: createTaskAllInfo.current.measureIndexList.lowerThreshold[index] || '',
        ...otherParam,
      };
    } else if (isDrSwitch) {// 智能容灾主指标
      return {
        ...commonObj,
        isDisplay: indicatorRelatedInfo.isDisplay,
        auxiliaryKpiInfos: indicatorRelatedInfo.auxiliaryKpiInfos,
        thresholds: indicatorRelatedInfo.thresholds,
        derivedKpiInfo: indicatorRelatedInfo.derivedKpiInfo,
        kpiAuxiliaryIndicators: indicatorRelatedInfo.kpiAuxiliaryIndicators,
        groupId,
        groupMemberId: indicatorRelatedInfo.groupMemberId,
        vnfDnName: indicatorRelatedInfo.vnfDnName,
        vnfDn: indicatorRelatedInfo.vnfDn,
      };
    } else if (isAuxiliaryKpi) {// 智能容灾辅助指标
      return {
        ...commonObj,
        groupMemberId: indicatorRelatedInfo.groupMemberId,
        vnfDnName: indicatorRelatedInfo.vnfDnName,
        vnfDn: indicatorRelatedInfo.vnfDn,
        columnName: indicatorRelatedInfo.columnName,
        auxiliaryThresholds: indicatorRelatedInfo.auxiliaryThresholds,
        auxiliaryKPIDisplayCN: createTaskAllInfo.current.measureIndexList.indicatorZhNames[index] || '',
        auxiliaryKPIDisplayEN: createTaskAllInfo.current.measureIndexList.indicatorEnNames[index] || '',
      };
    } else if (isTrend) {// 联合指标预测
      return {
        ...commonObj,
        auxiliaryIndicator: indicatorRelatedInfo.auxiliaryIndicator,
        clusterName: indicatorRelatedInfo.clusterName,
        siteId: indicatorRelatedInfo.siteId,
      };
    } else if (isIndependent) {// 独立指标预测
      return {
        ...commonObj,
        historyIndicatorId: indicatorRelatedInfo.historyIndicatorId,
        historyIndicatorName: indicatorRelatedInfo.historyIndicatorName,
      };
    } else if (isTwoCluster) {// 双机
      return {
        ...commonObj,
        clusterName: indicatorRelatedInfo.checkedNetId,
        historyIndicatorId: indicatorRelatedInfo.historyIndicatorId,
        historyIndicatorName: indicatorRelatedInfo.historyIndicatorName,
      };
    } else {
      return commonObj;
    }
  };

  const getIndicatorParams = (operateType, isCustom) => {
    let isTrend = hashName === TREND_MENU && measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.UNITE;
    let isTwoCluster = measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE;
    let indicatorList = createTaskAllInfo.current.measureIndexList.measureTableList.map((item, index) => {
      let indicatorRelatedInfo = item[1];

      // 处理dnName
      let dnName = item[4];
      if (hashName === TREND_MENU) {
        if (measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.INDEPENDENT) {
          dnName = item[3];
        } else {
          dnName = '';
        }
      }
      if (isTrendAuxiliaryKpi) {
        dnName = item[4];
      }
      if (util.getComponentKey() === SWITCH_MANAGE_MENU) {
        dnName = item[3];
      }

      let commonObj = {
        dn: indicatorRelatedInfo.checkedDn,
        moType: indicatorRelatedInfo.checkedNetType,
        deploymentMoType: indicatorRelatedInfo.deploymentMoType ?? undefined,
        measUnitKey: indicatorRelatedInfo.measUnitKey,
        measUnitName: indicatorRelatedInfo.indexGroupName,
        measTypeKey: indicatorRelatedInfo.measTypeKey,
        indexName: indicatorRelatedInfo.measureIndex,
        displayValue: indicatorRelatedInfo.displayValue,
        originalValue: indicatorRelatedInfo.originalValue,
        unit: indicatorRelatedInfo.unit,
        dnName: dnName,
        hasMeasObj: indicatorRelatedInfo.hasMeasObj,
        indexId: indicatorRelatedInfo.indexId,
        checkedNetId: indicatorRelatedInfo.checkedNetId,
        resourceTypeKey: indicatorRelatedInfo.resourceTypeKey,
      };
      return getIndicatorListPar(item, index, commonObj, isTrend, isTwoCluster);
    });

    let otherResourceParam = getResourceParam(indicatorList);

    let newSolutionName;
    if (isTwoCluster) {
      newSolutionName = undefined;
    } else {
      if (otherResourceParam.solutionId) {
        newSolutionName = otherResourceParam.solutionName;
      } else {
        newSolutionName = '';
      }
    }

    let resourceParam = {
      datasourceId: DATA_RESOURCE_MAP.KPI,
      ...otherResourceParam,
      solutionId: isTwoCluster ? undefined : otherResourceParam.solutionId ?? solutionIdRef.current.id,
      solutionName: newSolutionName,
    };
    let param = isCustom ? resourceParam : { ...taskList, ...resourceParam };
    return ({ param, indicatorList });
  };

  // 校验阈值 容量评估、服务画像
  const verifyThreshold = () => {
    let upperThresholdList = customIndicatorRef.current.upperThreshold;
    let lowerThresholdList = customIndicatorRef.current.lowerThreshold;
    for (let i = 0; i < measureIndexList.measureTableList.length; i++) {
      let upperThreshold = createTaskAllInfo.current.measureIndexList.upperThreshold[i];
      let lowerThreshold = createTaskAllInfo.current.measureIndexList.lowerThreshold[i];
      let pagePar = {
        pageIndex: Math.floor(i / measureIndexList.pageSize) + 1,
        pageSize: measureIndexList.pageSize,
      };
      const validRes = !DataCheck.cmpValidChar(upperThreshold) || !DataCheck.cmpValidChar(lowerThreshold) ||
        !DataCheck.validThreshold(upperThreshold) || !DataCheck.validThreshold(lowerThreshold);
      if (validRes) {
        handleResourcePageChange(pagePar);

        setTimeout(() => {
          let dataCheckArr = [upperThresholdList[i], lowerThresholdList[i]];
          pages.validateDataLists(dataCheckArr);
        }, 200);
        return false;
      }

      if (parseFloat(upperThreshold) < parseFloat(lowerThreshold)) {
        handleResourcePageChange(pagePar);
        let errorTip = isCapacity ? $t('task.capacity.threshold.tip') : $t('task.tidal.threshold.tip');
        modal.error($t('kpi.task.common.tip'), errorTip);
        return false;
      }
    }
    return true;
  };

  const verify = (isNoVerifyMax) => {
    if (!isAuxiliaryKpi && !isTrendAuxiliaryKpi && measureIndexList.measureTableList.length < 1) {
      modal.error($t('kpi.task.common.tip'), $t('kpi.task.please.select.index'));
      return false;
    }

    let isTrend = hashName === TREND_MENU && measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.UNITE;
    let maxLen = getMaxKpiNum(measureIndexList, hashName, perClusterGroupKpiNum, maxIndicatorLength);
    if (!isNoVerifyMax && maxLen && measureIndexList.measureTableList.length > maxLen) {
      let len = (nodeType === TEMPLATE_TYPE.CAPACITY && !isTreeRoot) ? CAPACITY_PER_NODE_LIMIT : maxLen;
      let tip = isTrend ? $t('indicator.prediction.limit.num') : $t('kpi.new.task.resource.measure.limit.num');
      modal.error($t('kpi.task.common.tip'), tip + len);
      return false;
    }

    if (isCapacity || isProfileKpi) {
      return verifyThreshold();
    }

    if (isAuxiliaryKpi && !verifyAuxiliaryIndicators()) {
      return false;
    }
    return true;
  };

  // 校验辅助指标名称
  const verifyAuxiliaryIndicators = () => {
    let indicatorZhLists = customIndicatorRef.current.indicatorZhNames;
    let indicatorEnLists = customIndicatorRef.current.indicatorEnNames;
    for (let i = 0; i < measureIndexList.measureTableList.length; i++) {
      let dataCheckArr = [indicatorZhLists[i], indicatorEnLists[i]];
      if (!pages.validateDataLists(dataCheckArr)) {
        return false;
      }
    }
    return true;
  };

  const submitCallBack = () => {
    let { param, indicatorList } = getIndicatorParams();
    dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
    let kpiResourceParam = measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE ?
      getClusterGroupKpiParam(indicatorList) : getResourceParam(indicatorList);
    dispatch({ type: 'setRefreshByResource', isRefreshByResource: true });
    setTaskList({ ...taskList, ...kpiResourceParam });
    setTaskPageType(TASK_PAGE_TYPE.VIEW);
  };

  const modifyTask = (operateType) => {
    let { param, indicatorList } = getIndicatorParams(operateType);
    submitTask(operateType, param, () => {
      submitCallBack();
    });
  };

  const modifyOrViewInfo = (indicatorList) => {
    // 获取指标列表最后一条数据,从中拼凑measureData
    let lastMeasureData = indicatorList[indicatorList.length - 1];
    currentMember.current = lastMeasureData.groupMemberId;
    currentDn.current = lastMeasureData.vnfDn;
    currentMemberName.current = lastMeasureData.vnfDnName;
    measureData.current = getMeasureDataCurrent(indicatorList, true);
    beforeSelect.current = RESOURCE_TYPE.NETWORK_INSTANCE;

    let rows = getMeasureTable(indicatorList, true);
    dispatch({
      type: 'setMeasureListByRefresh',
      isRefreshByResource: false,
      measureIndexList: {
        ...createTaskAllInfo.current.measureIndexList,
        resourceRelatedMode: RESOURCE_TYPE.NETWORK_INSTANCE,
        measurePageResults: rows.slice(0, 50),
        measureTableList: rows,
        pageIndex: 1,
        pageSize: 50,
        measureKey: new Date().getTime(),
        isFirstEntry: false,
      },
    });

    if (taskPageType !== TASK_PAGE_TYPE.VIEW && taskPageType !== TASK_PAGE_TYPE.NEW) {
      getSelectedData(true, lastMeasureData, indicatorList, {
        solutionId: lastMeasureData.vnfDn || '',
      });
    }
  };

  const switchModifyTask = () => {
    medianResourceInfo.current = createTaskAllInfo.current;
    setTaskPageType(TASK_PAGE_TYPE.MODIFY);
    dispatch({ type: 'setRefreshByResource', isRefreshByResource: true });
    setNetWorkTreeParam({ isFilterSuccess: false });
  };

  const changeSolution = (val, oldValue, text) => {
    if (val === oldValue) {
      return;
    }

    let isTrend = hashName === TREND_MENU && measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.UNITE;
    isSolutionChange.current = measureIndexList.resourceRelatedMode === RESOURCE_TYPE.NETWORK_INSTANCE || isTrend;
    dispatch({
      type: 'setMeasureIndexList',
      measureIndexList: {
        ...measureIndexList,
        solutionId: val,
        solutionName: text,
      },
    });
    solutionIdRef.current = { id: val, name: text };
    // 切换解决方案时清空已选指标表格
    clearAll();
  };

  // 该组件被嵌入其他组件中，状态为修改时
  const modifyDataSourceByOther = (nodeParam, isInstance) => {
    measureData.current = getMeasureDataCurrent(nodeParam.indicatorList, isInstance);
    beforeSelect.current = nodeParam.indicatorSelectType;
    let rows = getMeasureTable(nodeParam.indicatorList, isInstance);
    let noInstanceTableData = getPageResultsNoInstance(nodeParam.indicatorList);
    dispatch({
      type: 'setMeasureListByRefresh',
      isRefreshByResource: false,
      measureIndexList: {
        ...createTaskAllInfo.current.measureIndexList,
        solutionId: nodeParam.solutionId ? nodeParam.solutionId : '',
        solutionName: nodeParam.solutionName || $t('kpi.task.no.solution'),
        resourceRelatedMode: nodeParam.indicatorSelectType,
        measurePageResults: rows.slice(0, 50),
        measureTableList: rows,
        pageIndex: 1,
        pageSize: 50,
        measureKey: new Date().getTime(),
        isFirstEntry: false,
        upperThreshold: nodeParam.indicatorList.map(item => item.upperThreshold),
        lowerThreshold: nodeParam.indicatorList.map(item => item.lowerThreshold),
        isAutoUpdateMO: nodeParam.updateIndicatorAuto ?? IS_AUTO_KEY.NO,
        measurePageResultsNoInstance: noInstanceTableData.slice(ARRAY_INDEX_0, PAGE_SIZE_50),
        measureTableListNoInstance: noInstanceTableData,
      },
    });

    if (taskPageType !== TASK_PAGE_TYPE.VIEW_OTHER) {
      let lastMeasureData = nodeParam.indicatorList[nodeParam.indicatorList.length - 1];
      setTimeout(() => {
        getSelectedData(isInstance, lastMeasureData, nodeParam.indicatorList, {
          solutionId: nodeParam.solutionId ? nodeParam.solutionId : '',
        });
      }, 50);
    }
  };

  const updateMeasureTableData = (indicatorList, instance) => {
    let rows = getMeasureTable(indicatorList, instance);
    dispatch({
      type: 'setMeasureIndexList',
      measureIndexList: {
        ...measureIndexList,
        measurePageResults: rows.slice((measureIndexList.pageIndex - 1) * measureIndexList.pageSize,
          measureIndexList.pageIndex * measureIndexList.pageSize),
        measureTableList: rows,
      },
    });
  };

  const updateMeasureTableDataByInfo = (indicatorList, instance, mainIndicatorInfo, predictScenario = 0) => {
    let rows = getMeasureTable(indicatorList, instance, predictScenario);
    dispatch({
      type: 'setResourceLists',
      isRefreshByResource: false,
      measureIndexList: {
        ...mainIndicatorInfo.measureIndexList,
        measurePageResults: rows.slice((measureIndexList.pageIndex - 1) * measureIndexList.pageSize,
          measureIndexList.pageIndex * measureIndexList.pageSize),
        measureTableList: rows,
      },
      netWorkTreeParam: mainIndicatorInfo.netWorkTreeParam,
      unitTree: mainIndicatorInfo.unitTree,
      objectTable: mainIndicatorInfo.objectTable,
    });
  };

  useImperativeHandle(cRef, () => ({
    // 就是暴露给父组件的方法
    modifyTaskByKpi: (operateType) => {
      if (!verify()) {
        return;
      }
      if (hashName === TREND_MENU) {
        modifyTask(operateType);
      } else {
        queryMeasObjCount(() => { modifyTask(operateType); });
      }
    },
    switchModifyTaskByKpi: () => {
      switchModifyTask();
    },
    goStepByKpi: ({ stepName, type }) => {
      goStep({ stepName, type });
    },
    cancelModifyByKpi: () => {
      cancelModify();
    },
    getMeasureCache: () => {
      return measureData.current;
    },
    getIndicatorParam: (operateType, isCustom) => {
      return getIndicatorParams(operateType, isCustom);
    },
    verify: () => {
      return verify();
    },
    submit: () => {
      submitCallBack();
    },
    modifyDataSourceByOther: (nodeParam, isInstance) => {
      modifyDataSourceByOther(nodeParam, isInstance);
    },
    modifyOrViewInfo: (indicatorList) => {
      modifyOrViewInfo(indicatorList);
    },
    getCheckedNetType: () => {
      if (!measureIndexList.measureTableList.length) {
        return '';
      }

      let moType = measureIndexList.measureTableList[0][1].checkedNetType;
      let dn = measureIndexList.measureTableList[0][1].checkedDn;
      return { moType, dn };
    },
    saveMainKpiData: () => {
      const { param } = getIndicatorParams(true);
      let kpiParam = {
        measureIndexList, netWorkTreeParam, unitTree: JSON.parse(JSON.stringify(unitTree)),
        objectTable, checkedMeasureIndex, isRefreshByResource,
        resourceCache: measureData.current,
        indicatorParam: param,
      };
      setPerformancePar({ ...kpiParam });
    },
    getMainKpiInfo: () => {
      return JSON.parse(JSON.stringify(createTaskAllInfo.current));
    },
    clearDataSource: () => {
      dispatch({
        type: 'clearSourceData',
        isRefreshByResource: true,
        measureIndexList: {
          resourceRelatedMode: 0,
          indicatorPredictScenario: 0,
          measurePageResults: [],
          measureTableList: [],
          pageIndex: 1,
          pageSize: 50,
          measureKey: 0,
          isFirstEntry: true,
          isFirstEntryByCustom: true,
          dataSourceType: null,
          solutionId: '',
          solutionName: '',
          upperThreshold: [],
          lowerThreshold: [],
          indicatorZhNames: [],
          indicatorEnNames: [],
        },
      });
    },
    updateMeasureTableData: (indicatorList, instance, mainIndicatorInfo) => {
      if (mainIndicatorInfo) {
        updateMeasureTableDataByInfo(indicatorList, instance, mainIndicatorInfo);
      } else {
        updateMeasureTableData(indicatorList, instance);
      }
    },
  }));

  const getResourceType = () => {
    if (isCapacity) {
      return (nodeType === TEMPLATE_TYPE.START || nodeType === TEMPLATE_TYPE.KPI ||
        nodeType === TEMPLATE_TYPE.TWO_START || nodeType === TEMPLATE_TYPE.CAPACITY_TWO ||
        (nodeType === TEMPLATE_TYPE.CAPACITY && isTreeRoot)) ?
        CAPACITY_NO_TREE_DATA_SOURCE : CAPACITY_TREE_DATA_SOURCE;
    } else {
      return DV_PERFORMANCE_DATA_SOURCE;
    }
  };

  const updateIndicatorTable = (measureTableData) => {
    if (isCapacity) {
      measureTableData.forEach((item, index) => {
        item[8] = getCustomTextField(index, 'upperThreshold',
          createTaskAllInfo.current.measureIndexList.upperThreshold[index]);
        item[9] = getCustomTextField(index, 'lowerThreshold',
          createTaskAllInfo.current.measureIndexList.lowerThreshold[index]);
      });
    }

    if (isAuxiliaryKpi) {
      measureTableData.forEach((item, index) => {
        item[8] = getAuxiliaryTextField(index, 'indicatorZhNames',
          createTaskAllInfo.current.measureIndexList.indicatorZhNames[index]);
        item[9] = getAuxiliaryTextField(index, 'indicatorEnNames',
          createTaskAllInfo.current.measureIndexList.indicatorEnNames[index]);
      });
    }
    return measureTableData;
  };

  const handleResourcePageChange = ({ pageIndex, pageSize }) => {
    let rows = updateIndicatorTable(measureIndexList.measureTableList);
    let noInstanceRows = (measureIndexList.measureTableListNoInstance);
    dispatch({
      type: 'setMeasureIndexList',
      measureIndexList: {
        ...measureIndexList,
        measurePageResults: rows.slice((pageIndex - 1) * pageSize, pageIndex * pageSize),
        measureTableList: rows,
        measurePageResultsNoInstance: noInstanceRows.slice((pageIndex - 1) * pageSize, pageIndex * pageSize),
        measureTableListNoInstance: noInstanceRows,
        pageIndex,
        pageSize,
      },
    });
  };

  // 指标表格共用属性
  const getIndicatorTableProps = (id, isAuto = false) => ({
    width: '99.5%',
    id,
    key: measureIndexList.measureKey,
    showEmptyImage: false,
    maxHeight: 400,
    enablePagination: true,
    recordCount: isAuto ? measureIndexList.measureTableListNoInstance?.length : measureIndexList.measureTableList?.length,
    splitPagination: true,
    dataset: isAuto ? measureIndexList.measurePageResultsNoInstance : measureIndexList.measurePageResults,
    pageSize: measureIndexList.pageSize,
    currentPage: measureIndexList.pageIndex,
    pageSizeOptions: [50, 100, 200],
    onPageChange: (pageIndex) => handleResourcePageChange({ pageIndex, pageSize: measureIndexList.pageSize }),
    onPageSizeChange: (pageSize) => handleResourcePageChange({ pageSize, pageIndex: 1 }),
    ref: table => { measurePageTable = table; },
  });

  // 指标趋势预测：配置辅助指标回调
  const configureAuxiliaryIndicatorCallBack = (auxiliaryIndicator, rowId, mainIndicatorInfo) => {
    const indicatorList = getIndicatorParams(true).indicatorList;
    let findIndex = indicatorList.findIndex(item => {
      let keyId = item.siteId ? `${item.clusterName}${item.siteId}` : item.clusterName;
      let id = `${keyId}${item.resourceTypeKey}${item.moType}${item.measUnitName}${item.measUnitKey}`;
      if (item.hasMeasObj === 'true' || item.hasMeasObj === true) {
        return rowId === `${id}${item.measTypeKey}_${item.indexId}_${item.displayValue}`;
      } else {
        return rowId === `${id}${item.measTypeKey}`;
      }
    });
    indicatorList[findIndex] = { ...indicatorList[findIndex], auxiliaryIndicator };
    updateMeasureTableDataByInfo(indicatorList, false, mainIndicatorInfo, KPI_TREND_PREDICT_TYPE.UNITE);
  };

  const setUnitTree = (param) => {
    dispatch({ type: 'setUnitTree', unitTree: { ...createTaskAllInfo.current.unitTree, ...param } });
  };

  const setObjectTable = (param) => {
    dispatch({ type: 'setObjectTable', objectTable: { ...createTaskAllInfo.current.objectTable, ...param } });
  };

  const setNetWorkTreeParam = (param) => {
    dispatch({ type: 'setNetWorkTreeParam', netWorkTreeParam: { ...createTaskAllInfo.current.netWorkTreeParam, ...param } });
  };

  // 父组件相关数据的获取和更新
  const parentDataHandle = {
    setMeasureData: (param) => {
      measureData.current = {
        ...measureData.current,
        ...param,
      };
    },
    getMeasureData: () => measureData.current,
    getCurrentTreeRootId: () => currentTreeRootId.current,
    setCurrentTreeRootId: (ele) => currentTreeRootId.current = ele,
    getCurrentDn: () => currentDn.current,
    getCurrentMember: () => currentMember.current,
    getCurrentMemberName: () => currentMemberName.current,
    setMemberInfo: (member, memberName, dn) => {
      currentMember.current = member;
      currentMemberName.current = memberName;
      currentDn.current = dn;
    },
    getCheckedMeasureIndexTable: () => checkedMeasureIndexTable.current,
    setCheckedMeasureIndexTable: (item) => checkedMeasureIndexTable.current = item,
  };

  // 传给子组件公共数据
  const transferSubInfo = () => ({
    isDrSwitch,
    hashName,
    isCapacity,
    nodeType,
    isTreeRoot,
    isProfileKpi,
    isAuxiliaryKpi,
    isTrendAuxiliaryKpi,
    getCreateTaskAllInfo,
    parentDataHandle,
    rootMoTypeAndDn,
    setUnitTree,
    setObjectTable,
    setNetWorkTreeParam,
  });

  const getCreateTaskAllInfo = () => {
    return createTaskAllInfo.current;
  };

  const queryUnitTree = (apiFn, params, callback) => UnitTreeChildRef.current.query(apiFn, params, callback);
  const queryObjTable = (selectedKeys, queryFn, params, callback, isChecked, measureTableData, measureTableDataNoInstance) =>
    objTableChildRef.current.queryObjTable(selectedKeys, queryFn, params, callback, isChecked, measureTableData, measureTableDataNoInstance);

  return (
    <>
      {(taskPageType === TASK_PAGE_TYPE.VIEW || taskPageType === TASK_PAGE_TYPE.VIEW_OTHER) ?
        <table className='task-info' style={{ width: '100%' }}>
          <tbody>
            {(
              (isCapacity && (nodeType !== TEMPLATE_TYPE.CAPACITY || (nodeType === TEMPLATE_TYPE.CAPACITY && isTreeRoot))) ||
              isCorrelationAnalysis || isKpiTaskManage) &&
              indicatorPredictScenario !== KPI_TREND_PREDICT_TYPE.TWO_NODE &&
              <tr>
                <td className='table_label'>{$t('kpi.new.task.solution')}：</td>
                <td>{measureIndexList.solutionName}</td>
              </tr>}
            {!isDrSwitch && !isAuxiliaryKpi && hashName === TREND_MENU && !isTrendAuxiliaryKpi &&
              <tr>
                <td className='table_label'>{$t('indicator.prediction.type')}：</td>
                <td>
                  {TREND_TYPE_MAPS[measureIndexList.indicatorPredictScenario]}
                </td>
              </tr>}
            {(isProfileKpi || isCapacity || isCorrelationAnalysis || isKpiTaskManage) &&
              (measureIndexList.indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.INDEPENDENT) &&
              <tr>
                <td className='table_label'>{$t('kpi.new.task.resource.related.mode')}：</td>
                <td>
                  {util.detachAttributeValue(DV_PERFORMANCE_DATA_SOURCE, 'value', 'text',
                    measureIndexList.resourceRelatedMode)}
                </td>
              </tr>}
            {(!isDrSwitch && !isAuxiliaryKpi && !isTrendAuxiliaryKpi && !isProfileKpi && hashName !== TREND_MENU && nodeType !== TEMPLATE_TYPE.CAPACITY) &&
              measureIndexList.resourceRelatedMode === RESOURCE_TYPE.NETWORK_TYPE &&
              <tr>
                <td className='table_label'>{$t('kpi.new.task.resource.isAutoUpdateMo')}：</td>
                <td>
                  {IS_AUTO_KEY_TEXT[measureIndexList.isAutoUpdateMO]}
                </td>
              </tr>}
            {!isDrSwitch && !isAuxiliaryKpi && (hashName !== TREND_MENU || isTrendAuxiliaryKpi) && IS_AUTO_KEY.YES !== isAutoUpdateMO &&
              <tr>
                {!isTrendAuxiliaryKpi && <td />}
                <td>
                  <Table
                    columns={isCapacity ? CAPACITY_VIEW_MEASURE_TABLE_COLUMNS : VIEW_MEASURE_TABLE_COLUMNS}
                    {...getIndicatorTableProps('measurePageTable')}
                  />
                </td>
              </tr>}
            {!isDrSwitch && !isAuxiliaryKpi && (hashName !== TREND_MENU || isTrendAuxiliaryKpi) && IS_AUTO_KEY.YES === isAutoUpdateMO &&
              <tr>
                {!isTrendAuxiliaryKpi && <td />}
                <td>
                  <Table
                    columns={VIEW_MEASURE_MO_TYPE_COLUMNS}
                    {...getIndicatorTableProps('measurePageTable', IS_AUTO_KEY.YES)}
                  />
                </td>
              </tr>}
            {!isDrSwitch && !isAuxiliaryKpi && hashName === TREND_MENU && !isTrendAuxiliaryKpi &&
              <tr>
                <td />
                <td>
                  <Table
                    columns={TREND_VIEW_TABLE_COLUMNS[measureIndexList.indicatorPredictScenario]}
                    {...getIndicatorTableProps('measurePageTableView')}
                  />
                </td>
              </tr>}
            {
              (isAuxiliaryKpi || isDrSwitch) &&
              <tr>
                <td style={{ width: '160px' }}><span style={{ paddingLeft: '16px' }}>{$t('kpi.table.td')}</span></td>
                <td style={{ paddingBottom: 0 }}>
                  <Table
                    columns={isDrSwitch ? DR_SWITCH_MEASURE_TABLE_COLUMNS_VIEW : AUXILIARY_MEASURE_TABLE_COLUMNS_VIEW}
                    {...getIndicatorTableProps('measurePageTable')}
                  />
                </td>
              </tr>
            }
          </tbody>
        </table> :
        <table className='task-info' style={{ width: '100%', margin: '0px 10px 0 0' }}>
          <tbody>
            {(
              (isCapacity && (nodeType !== TEMPLATE_TYPE.CAPACITY || (nodeType === TEMPLATE_TYPE.CAPACITY && isTreeRoot))) ||
              isCorrelationAnalysis || isKpiTaskManage
            ) && indicatorPredictScenario !== KPI_TREND_PREDICT_TYPE.TWO_NODE &&
              <tr>
                <td><LabelHint label={$t('kpi.new.task.solution')} isHelp={false} /></td>
                <td>
                  <Select options={solutions} selectStyle={{ width: '800px' }} value={solutionId}
                    onChange={changeSolution}
                  />
                </td>
              </tr>}
            {hashName === TREND_MENU && !isTrendAuxiliaryKpi &&
              (taskPageType === TASK_PAGE_TYPE.NEW || taskPageType === TASK_PAGE_TYPE.COPY) &&
              <tr>
                <td style={{ lineHeight: '2rem' }}>
                  <LabelHint label={$t('indicator.prediction.type')} hint={$t('indicator.prediction.type.help')}
                    require={true} isHelp={true}
                  />
                </td>
                <td>
                  <RadioGroup rowSpacing="100px" data={Prel.isMs ? DV_PERFORMANCE_TREND_TYPE_MS : DV_PERFORMANCE_TREND_TYPE}
                    value={measureIndexList.indicatorPredictScenario}
                    onChange={val => switchPredictScenario(val)}
                  />
                </td>
              </tr>}
            {hashName === TREND_MENU && !isTrendAuxiliaryKpi && taskPageType === TASK_PAGE_TYPE.MODIFY &&
              <tr>
                <td style={{ lineHeight: '2rem' }}>
                  <LabelHint label={$t('indicator.prediction.type')} require={true} isHelp={false} />
                </td>
                <td style={{ lineHeight: '2rem' }}>
                  {TREND_TYPE_MAPS[measureIndexList.indicatorPredictScenario]}
                </td>
              </tr>}
            {((!isDrSwitch && !isAuxiliaryKpi && !isTrendAuxiliaryKpi && !isProfileKpi && hashName !== TREND_MENU) ||
              (hashName === TREND_MENU && indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.INDEPENDENT &&
                !isTrendAuxiliaryKpi)) &&
              <tr>
                <td style={{ lineHeight: '2rem' }}>
                  <LabelHint
                    label={$t('kpi.new.task.resource.related.mode')}
                    hint={$t('kpi.resource.select.tip')}
                    require={true} isHelp={hashName !== CAPACITY_MENU}
                  />
                </td>
                <td>
                  <RadioGroup rowSpacing="100px" data={getResourceType()}
                    value={measureIndexList.resourceRelatedMode}
                    onChange={val => switchResourceRelatedType(true, val)}
                  />
                </td>
              </tr>}
            {(!isDrSwitch && !isAuxiliaryKpi && !isTrendAuxiliaryKpi && !isProfileKpi &&
                hashName !== TREND_MENU && nodeType !== TEMPLATE_TYPE.CAPACITY) &&
              measureIndexList.resourceRelatedMode === RESOURCE_TYPE.NETWORK_TYPE &&
              <tr>
                <td style={{ lineHeight: '2rem' }}>
                  <LabelHint
                    label={$t('kpi.new.task.resource.isAutoUpdateMo')}
                    hint={$t('kpi.new.task.resource.isAutoUpdateMo.tips')}
                    require={true} isHelp={true}
                  />
                </td>
                <td>
                  <RadioGroup rowSpacing="100px" data={IS_AUTO_UPDATE_MO}
                    value={measureIndexList.isAutoUpdateMO}
                    onChange={val => onChangeIsAutoUpdate(val)}
                  />
                </td>
              </tr>}
            <tr>
              {isDrSwitch ?
                <td style={{ width: '160px', paddingBottom: '24px' }}>
                  <LabelHint label={$t('task.disaster.select.indicator')} require={false} isHelp={false} />
                </td> :
                <td style={{ width: '0px' }} />}
              <td>
                <ul id='source_measure'>
                  <li style={{ marginRight: '1%' }}>
                    {indicatorPredictScenario === KPI_TREND_PREDICT_TYPE.TWO_NODE ?
                      <DataSourceClusterGroupTable {...transferSubInfo()}
                        queryUnitTree={queryUnitTree}
                        cRef={groupTableChildRef}
                        selectedUnitNodes={(checkedNeNode, isExpand) => UnitTreeChildRef.current.selectedUnitNodes(checkedNeNode, isExpand)}
                        getClearObject={() => objTableChildRef.current.getClearObject()}
                        getClearUnitTreeInfo={() => UnitTreeChildRef.current.getClearUnitTreeInfo()}
                      /> :
                      <DataSourceNETree {...transferSubInfo()}
                        queryUnitTree={queryUnitTree}
                        getClearUnitTreeInfo={() => UnitTreeChildRef.current.getClearUnitTreeInfo()}
                        selectedUnitNodes={(checkedNeNode, isExpand) => UnitTreeChildRef.current.selectedUnitNodes(checkedNeNode, isExpand)}
                        getClearObject={() => objTableChildRef.current.getClearObject()}
                        cRef={NEChildRef}
                      />}
                  </li>
                  <li style={{ width: '34%', marginRight: '1%' }}>
                    <DataSourceUnitTree setUnitTree={setUnitTree}
                      {...transferSubInfo()}
                      getClusterKey={getClusterKey}
                      preIndicatorInfo={preIndicatorInfo}
                      deleteInputData={deleteInputData}
                      measureTableListeners={measureTableListeners}
                      updateIndicatorTable={updateIndicatorTable}
                      isAutoUpdateMO={measureIndexList.isAutoUpdateMO}
                      getIndicatorInstanceByUnitTree={getIndicatorInstanceByUnitTree}
                      queryObjTable={queryObjTable}
                      getClearObject={() => objTableChildRef.current.getClearObject()}
                      cRef={UnitTreeChildRef}
                    />
                  </li>
                  <li style={{ width: '34%' }}>
                    <DataSourceObjectTable {...transferSubInfo()}
                      preIndicatorInfo={preIndicatorInfo}
                      maxIndicatorLength={maxIndicatorLength}
                      deleteInputData={deleteInputData}
                      updateIndicatorTable={updateIndicatorTable}
                      getClusterKey={getClusterKey}
                      measureTableListeners={measureTableListeners}
                      isAutoUpdateMO={measureIndexList.isAutoUpdateMO}
                      getIndicatorInstanceByUnitTree={getIndicatorInstanceByUnitTree}
                      cRef={objTableChildRef}
                    />
                  </li>
                </ul>
              </td>
            </tr>
            <tr>
              <td />
              <td><Button text={$t('kpi.task.common.clear.all')} style={{ float: 'right' }} onClick={() => clearAll()} />
                {
                  !(nodeType === TEMPLATE_TYPE.CAPACITY && !isTreeRoot) &&
                  <Button text={$t('kpi.task.common.remove.page')} style={{ float: 'right', marginRight: '15px' }}
                    onClick={clearCurrentPage}
                  />
                }
              </td>
            </tr>
            {!isDrSwitch && !isAuxiliaryKpi && !isTrendAuxiliaryKpi && IS_AUTO_KEY.YES !== isAutoUpdateMO &&
              <tr>
                <td />
                <td>
                  <Table columns={getTableColumns()} {...getIndicatorTableProps('measurePageTable')} />
                </td>
              </tr>}
            {!isDrSwitch && !isAuxiliaryKpi && !isTrendAuxiliaryKpi && IS_AUTO_KEY.YES === isAutoUpdateMO &&
              <tr>
                <td />
                <td>
                  <Table
                    columns={EDIT_MEASURE_MO_TYPE_COLUMNS}
                    {...getIndicatorTableProps('measurePageTable', isAutoUpdateMO)}
                  />
                </td>
              </tr>}
            {
              (isAuxiliaryKpi || isDrSwitch) &&
              <tr>
                <td style={{ paddingBottom: 0 }} />
                <td style={{ paddingBottom: 0 }}>
                  <Table columns={isDrSwitch ? DR_SWITCH_MEASURE_TABLE_COLUMNS : AUXILIARY_MEASURE_TABLE_COLUMNS}
                    {...getIndicatorTableProps('measurePageTable')}
                  />
                </td>
              </tr>
            }
            {
              isTrendAuxiliaryKpi &&
              <tr>
                <td style={{ paddingBottom: 0 }} />
                <td style={{ paddingBottom: 0 }}>
                  <Table columns={MEASURE_TABLE_COLUMNS} {...getIndicatorTableProps('measurePageTable')} />
                </td>
              </tr>
            }
          </tbody>
        </table>}
      {auxiliaryPar.isAuxiliaryKpiOpen &&
        <ConfigureTrendAuxiliaryIndicator
          initData={auxiliaryPar.auxiliaryDialogData}
          taskPageType={taskPageType}
          submitCallBackFn={configureAuxiliaryIndicatorCallBack}
          setDialogOpen={() => dispatch({
            type: 'setAuxiliaryPar',
            auxiliaryPar: { ...auxiliaryPar, isAuxiliaryKpiOpen: false },
          })}
        />}
      {
        associatedDataSetPar.isAssociatedDataSetOpen &&
        <ConfigureDataSetDialog
          taskPageType={taskPageType}
          initData={associatedDataSetPar.associatedDataSetDialogData}
          submitCallBackFn={configureDataSetCallBack}
          setDialogOpen={(val) => dispatch({
            type: 'setAssociatedDataSetPar',
            associatedDataSetPar: { ...associatedDataSetPar, isAssociatedDataSetOpen: val },
          })}
        />
      }
      <Loader type="global"
        isOpen={isSourceLoading || isSolutionLoading || isUnitLoading || isObjTableLoading || isDisplayLoading}
        desc={$t('kpi.task.please.wait')} id="resourceLoader"
      />
    </>
  );
};

NewOrEditTaskDataSource.propTypes = {
  performancePar: PropTypes.object,
  taskList: PropTypes.object,
  rootMoTypeAndDn: PropTypes.object,
  memberInfo: PropTypes.object,
  isProfileKpi: PropTypes.bool,
  isCorrelationAnalysis: PropTypes.bool,
  isKpiTaskManage: PropTypes.bool,
};

export default NewOrEditTaskDataSource;
