import React, {useContext, useEffect, useImper<PERSON><PERSON><PERSON><PERSON>, useState} from 'react';
import {<PERSON><PERSON>, <PERSON>F<PERSON>, Loader, MultipleSelect, RadioGroup, Select, SelectPro, Spinner, TextArea} from 'eview-ui';
import {$t, modal, notification, pages, validate} from '@util';
import LabelHint from '@comp/LabelHint';
import {
  DATA_FORMAT,
  DEFAULT_INDICATOR_DISCARD_TIME,
  INDICATOR_TASK_TYPE,
  IS_AUTO_KEY,
  IS_AUTO_KEY_TEXT,
  IS_AUTO_UPDATE_MO,
  NewTaskContext,
  OPERATE_SUCCESS_CODE,
  TASK_PAGE_TYPE,
  TREND_MENU,
} from '@pages/taskManagement/const';
import FilterTree from '@pages/taskManagement/pageByIndicator/component/FilterTree';
import UnitTable from '@pages/taskManagement/pageByIndicator/component/UnitTable';
import IndicatorGroup from '@pages/taskManagement/pageByIndicator/component/IndicatorGroup';
import ViewIndicatorGroup from '@pages/taskManagement/pageByIndicator/component/ViewIndicatorGroup';
import {
  querymscustomsqltablecolumns,
  queryMsindicatorsbyindicatorgroup,
  queryMsmotree,
  queryMspmtablecolumns,
} from '../api';
import {deepClone} from '@digitalview/fe-utils';
import * as api from '@pages/taskManagement/api';

let indicatorKeySelect;
let sqlTextArea;
let timeKeySelect;
const StarrockDataSource = props => {
  const {
    taskList,
    setTaskList,
    cRef,
    setTaskPageType,
    taskPageType,
    ticket,
    dataSourceType,
    submitTask,
    hashName,
  } = props;
  const [state, dispatch] = useContext(NewTaskContext);

  const emptySelect = {
    indicatorList: {
      all: [],
      selected: [],
    },
    originalList: {
      all: [],
      selected: [],
    },
    time: {
      all: [],
      selected: '',
    },
    group: {
      all: [],
      selected: '',
    },
  };

  const [treeData, setTreeData] = new useState([]);
  const [currentTypeInfo, setCurrentTypeInfo] = useState({});
  const [measureUnit, setMeasureUnit] = useState([]);

  const [pageInfo, setPageInfo] = useState({
    pageSize: 20,
    currentPage: 1,
    recordCount: 0,
    pageResults: [],
    checkedRows: [],
  });

  const indicatorOnChange = value => {
    let indicatorListAll = starrockPar.indicatorList;
    indicatorListAll.selected = value;
    setStarrockPar({
      indicatorList: indicatorListAll,
    });
  };
  const {starrockPar, isRefreshByCustom, measureIndexList, authTicket, isSourceLoading} = state;

  const originalOnChange = value => {
    let originalAll = starrockPar.originalList;
    originalAll.selected = value;
    setStarrockPar({
      originalList: originalAll,
    });
  };
  useEffect(() => {
    queryMsmotree(res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        modal.error($t('kpi.task.common.error.tip'), res.resultMessage);
        return;
      }
      setTreeData(genDataForTree(res.data));
    });
    setStarrockPar(emptySelect);
  }, []);

  useEffect(() => {
    // 数据回显，数据需要设置到tab中
    if (taskPageType === TASK_PAGE_TYPE.MODIFY || taskPageType === TASK_PAGE_TYPE.COPY) {
      let newTaskTable = [];
      for (let i = 0; i < props.taskList.msIndicatorGroupList.length; i++) {
        let tmpInsData = {};
        let data = props.taskList.msIndicatorGroupList[i];
        let unitId = '';
        let starrockId = '';
        let indicatorString = data.measureIndicatorKeys.join(',');
        let originString = Object.keys(data.measureObjectKeys).join(',');
        if (!data.customSQL) {
          unitId = `${data.dn}${data.measUnit}`;
          starrockId = `${data.dn}${data.measUnit}`;
        } else {
          unitId = data.customSQL.trim();
        }
        let list = props.taskList.indicatorList.filter(item => item.msGroupId === data.msGroupId);
        if (list.length > 0) {
          let dataSet = [];
          let insColumns = ['indicator'];
          // 将字符串转换为对象，并处理没有值的列
          const fromEntries = Object.fromEntries(
            list[0].originalValue.split(',').map(item => {
              const [key, value] = item.split('=');
              return [key, value === undefined ? '' : value]; // 如果没有值，则设为空字符串
            })
          );
          Object.keys(fromEntries).forEach(key => {
            insColumns.push(key);
          });
          for (let j = 0; j < list.length; j++) {
            let tmpValue = [list[j].indexName];
            const fromEntries1 = Object.fromEntries(
              list[j].originalValue.split(',').map(item => {
                const key = item.split('=')[0]; // 获取第一个部分作为key
                const value = item.slice(item.indexOf('=') + 1);
                return [key, value === undefined ? '' : value]; // 如果没有值，则设为空字符串
              })
            );
            Object.keys(fromEntries1).forEach(key => {
              tmpValue.push(fromEntries1[key]);
            });
            dataSet.push(tmpValue);
          }
          tmpInsData = {
            columns: insColumns,
            dataSet,
            filterMapping: {},
          };
        }
        let tmpTableData = {
          unitId,
          dn: data.dn,
          moType: data.moType,
          measUnit: data.measUnit,
          collectTask: data.collectTask,
          customSQL: data.customSQL,
          starrockId,
          starrockName: data.collectTask || data.customSQL,
          neName: data.moName || 'N/A',
          insTable: tmpInsData,
          starrockTime: data.timestampKey,
          measureObject: originString,
          measurementIndex: indicatorString,
          indicatorList: {
            selected: data.measureIndicatorKeys || [],
          },
          originalList: {
            selected: Object.keys(data.measureObjectKeys) || [],
          },
          time: {
            selected: data.timestampKey || '',
          },
          group: {
            selected: data.indicatorGroupTag || [],
          },
        };
        newTaskTable.push(tmpTableData);
      }
      setTimeout(() => {
        setStarrockPar({
          spinner: props.taskList.indicatorDiscardTime,
          isAutoUpdateIndicator: props.taskList.updateIndicatorAuto,
          taskTable: newTaskTable,
        });
      }, 100);
    }
  }, [taskPageType]);
  const onSelectGroup = value => {
    let groupData = starrockPar.group;
    groupData.selected = value;
    setStarrockPar({
      group: groupData,
    });
  };

  const onSelectTime = value => {
    let timeData = starrockPar.time;
    timeData.selected = value;
    setStarrockPar({
      time: timeData,
    });
  };

  const genDataForTree = data => {
    return [
      {
        id: 'dv',
        text: 'DV',
        children: data.dv.map(item => {
          return {
            id: item.dn,
            text: item.moName,
            isLeaf: true,
            hideRootCheckbox: false,
            parentMoType: 'com.huawei.dvexttype.digitalviewv8',
            children: [],
          };
        }),
        isLeaf: false,
        hideRootCheckbox: false,
      },
      {
        id: 'prometheus',
        text: 'Prometheus',
        children: data.prometheus.map(item => {
          return {
            id: item.dn,
            text: item.moName,
            isLeaf: true,
            hideRootCheckbox: false,
            parentMoType: 'com.huawei.dvexttype.prometheus',
            children: [],
          };
        }),
        isLeaf: false,
        hideRootCheckbox: false,
      },
    ];
  };

  useEffect(() => {
    $('.singleInput .eui-select-input')?.attr('readonly', true);
    if (isRefreshByCustom) {
      // 查看、修改、复制从taskList读取数据
      if (taskPageType !== TASK_PAGE_TYPE.NEW) {
        setViewStarrockPar(taskList);
      }
      dispatch({type: 'setRefreshByCustom', isRefreshByCustom: false});
    }
  }, [taskPageType, dataSourceType, isRefreshByCustom]);

  // 更新第三方数据源state公共方法
  const setStarrockPar = param => {
    dispatch({
      type: 'setStarrockPar',
      starrockPar: {...starrockPar, ...param},
    });
  };

  useEffect(() => {
    if (!measureUnit || !measureUnit.data[1].value) {
      return;
    }
    dispatch({type: 'setIsSourceLoading', isSourceLoading: true});
    let param;
    if (starrockPar.dataFormatValue === 0) {
      param = {
        moType: currentTypeInfo.parentMoType,
        measUnitKey: measureUnit.data[1].value,
        isComparison: true,
      };
    } else {
      param = {
        customSql: starrockPar.sqlDesc,
      };
    }

    queryMspmtablecolumns(param, res => {
      dispatch({type: 'setIsSourceLoading', isSourceLoading: false});
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        modal.error($t('kpi.task.common.error.tip'), res.resultMessage);
        return;
      }
      let indicatorList = {
        all: res.data.measureIndicatorKeys.map(item => {
          return {
            value: item,
            text: item,
          };
        }),
        selected: [],
      };
      let originalList = {
        all: res.data.measureObjectKeys.map(item => {
          return {
            value: item,
            text: item,
          };
        }),
        selected: [],
      };
      let time = {
        all: res.data.timestampKeys.map(item => {
          return {
            value: item,
            text: item,
          };
        }),
        selected: '',
      };
      let group = {
        all: res.data.indicatorGroupTag.map(item => {
          return {
            value: item,
            text: item,
          };
        }),
        selected: '',
      };
      setStarrockPar({
        indicatorList,
        originalList,
        time,
        group,
      });
    });
  }, [measureUnit]);

  useEffect(() => {
    setStarrockPar({
      ...emptySelect,
    });
  }, [
    currentTypeInfo,
  ]);

  // 预执行，先提醒用户确保sql查询表数据无敏感数据，然后二次认证通过后方可执行
  const preExecution = () => {
    // 校验自定义数据源和sql是否已选
    const dataCheckArr = [sqlTextArea];
    if (!verifyData(dataCheckArr)) {
      return;
    }
    modal.warn($t('common.warning.tip'), $t('task.custom.sql.pre.execute.tip'), () => {
      pages.setSecondaryAuth(
        'analysis',
        'sqlExecute',
        tickets => {
          queryFields(
            {
              sql: starrockPar.sqlDesc,
            },
            false
          );
        },
        () => {
          dispatch({type: 'setIsSourceLoading', isSourceLoading: false});
        }
      );
    });
  };

  // 查询表字段，配置映射关系
  const queryFields = ({sql}) => {
    dispatch({type: 'setIsSourceLoading', isSourceLoading: true});
    querymscustomsqltablecolumns(
      {
        customSql: sql,
        isComparison: true,
      },
      res => {
        dispatch({type: 'setIsSourceLoading', isSourceLoading: false});
        if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
          let msg = res.resultMessage ? res.resultMessage : $t('task.custom.resource.pre.execute.fail');
          modal.error($t('kpi.task.common.error.tip'), msg);
          setStarrockPar({
            isClickPreButton: false,
            ...emptySelect,
          });
          return;
        }
        if (res.data) {
          let indicatorList = {
            all: res.data.measureIndicatorKeys.map(item => {
              return {
                value: item,
                text: item,
              };
            }),
            selected: [],
          };
          let originalList = {
            all: res.data.measureObjectKeys.map(item => {
              return {
                value: item,
                text: item,
              };
            }),
            selected: [],
          };
          let time = {
            all: res.data.timestampKeys.map(item => {
              return {
                value: item,
                text: item,
              };
            }),
            selected: '',
          };
          let group = {
            all: res.data.indicatorGroupTag.map(item => {
              return {
                value: item,
                text: item,
              };
            }),
            selected: '',
          };
          setStarrockPar({
            isClickPreButton: true,
            indicatorList,
            originalList,
            time,
            group,
          });
        }
        // // 单选搜索下拉框设置为只读只支持下拉
        $('.singleInput .eui-select-input').attr('readonly', true);
      },
      err => {
        dispatch({type: 'setIsSourceLoading', isSourceLoading: false});
        setStarrockPar({
          isClickPreButton: false,
          ...emptySelect,
        });
      }
    );
  };

  // sql输入框和自定义数据源下拉框回调,映射关系表格数据重置并隐藏
  const changeSqlTextAreaOrSelect = (val, key) => {
    setStarrockPar({
      isClickPreButton: false,
      [key]: val,
    });
  };

  const countRecord = input => {
    const {columns, dataSet, filterMapping} = input;
    // 遍历 filterMapping 对象
    const filteredRecords = dataSet.filter(record => {
      // 对 filterMapping 进行处理，获取列名和过滤值
      return Object.keys(filterMapping).every(columnName => {
        // 查找动态列名在 columns 中的索引
        const columnIndex = columns.indexOf(columnName);
        if (columnIndex === -1) {
          return false;
        } // 如果列名不存在，返回 false
        // 比较该列的值是否符合过滤条件
        let filterValue = filterMapping[columnName].split('&').filter(value => value !== '');
        if (filterValue.length === 0) {
          return true;
        }
        // 比较该列的值是否符合过滤条件
        return filterValue.includes(record[columnIndex]);
      });
    });
    // 输出过滤后的记录数
    return filteredRecords.length;
  };

  const processData = input => {
    const {columns, dataSet, filterMapping} = input;
    const result = {};
    // 遍历所有列
    columns.forEach((column, index) => {
      if (index === 0 || !column) {
        return;
      }
      const filterValue = filterMapping[column];

      if (filterValue) {
        // 如果 filterMapping 中有对应列且值不为空，按 '&' 分割
        result[column] = filterValue.split('&').map(value => value);
      } else {
        // 如果没有映射或值为空，获取去重后的当前列的值
        const uniqueValues = [...new Set(dataSet.map(row => row[index]))];
        result[column] = uniqueValues;
      }
    });
    return result;
  };

  const modifyTask = operateType => {
    let {msIndicatorGroupList, recordCount} = getMsIndicatorGroupList();
    // 判断是否预执行成功
    // 数据校验
    if (!submitVerify(recordCount)) {
      return;
    }
    const param = {
      ...taskList,
      msIndicatorGroupList,

      datasourceId: 8,
      indicatorSelectType: 1,
    };
    if (hashName !== TREND_MENU) {
      param.indicatorDiscardTime = starrockPar.spinner;
      param.updateIndicatorAuto = starrockPar.isAutoUpdateIndicator;
    } else {
      param.indicatorPredictScenario = 0;
    }
    submitTask(operateType, param, () => {
      submitCallBack();
    });
  };

  const submitVerify = recordCount => {
    if (recordCount === 0) {
      modal.error($t('kpi.task.common.tip'), $t('kpi.task.please.select.group'));
      return false;
    }
    if (measureIndexList.kpiTaskType === INDICATOR_TASK_TYPE.homogeneousComparison && recordCount > 200) {
      modal.error($t('kpi.task.common.tip'), $t('kpi.task.please.select.group.max.200'));
      return false;
    }
    if (measureIndexList.kpiTaskType !== INDICATOR_TASK_TYPE.homogeneousComparison && recordCount > 100) {
      modal.error($t('kpi.task.common.tip'), $t('kpi.task.please.select.group.max.100'));
      return false;
    }
    return true;
  };

  const getMsIndicatorGroupList = () => {
    let recordCount = 0;
    let msIndicatorGroupList = starrockPar.taskTable.map(item => {
      recordCount = recordCount + countRecord(item.insTable);
      let msIndicatorGroup = {
        timestampKey: item.time.selected,
        measureIndicatorKeys: item.indicatorList.selected,
        measureObjectKeys: processData(item.insTable),
      };
      if (item.group.selected) {
        msIndicatorGroup.indicatorGroupTag = item.group.selected;
      }

      if (item.customSQL) {
        msIndicatorGroup.customSQL = item.customSQL;
      } else {
        msIndicatorGroup.dn = item.dn;
        msIndicatorGroup.moName = item.neName;
        msIndicatorGroup.moType = item.moType;
        msIndicatorGroup.measUnit = item.measUnit;
        msIndicatorGroup.collectTask = item.collectTask;
      }
      return msIndicatorGroup;
    });
    return {
      msIndicatorGroupList,
      recordCount,
    };
  };

  // 上一步或下一步
  const goStep = ({stepName, type}) => {
    let switchTabData = '';
    // 获取提交参数
    if (type === 'next') {
      let {msIndicatorGroupList, recordCount} = getMsIndicatorGroupList();
      // 判断是否预执行成功
      // 数据校验
      if (!submitVerify(recordCount)) {
        return;
      }
      switchTabData = {
        msIndicatorGroupList,
        datasourceId: 8,
        indicatorSelectType: 1,
        indicatorPredictScenario: 0,
      };

      if (hashName !== TREND_MENU) {
        switchTabData.indicatorDiscardTime = starrockPar.spinner;
        switchTabData.updateIndicatorAuto = starrockPar.isAutoUpdateIndicator;
      }
    }

    dispatch({
      type: 'setCustomResourceTabNoCache',
      currentTab: stepName,
      isRefreshByCustom: false,
      resourceData: switchTabData,
    });
  };

  // 点击修改按钮
  const switchModifyTask = () => {
    setTaskPageType(TASK_PAGE_TYPE.MODIFY);
    dispatch({type: 'setRefreshByResource', isRefreshByResource: true});
  };

  // 取消修改
  const cancelModify = () => {
    setTaskPageType(TASK_PAGE_TYPE.VIEW);
  };

  // 输入校验
  const verifyData = dataCheckArr => {
    for (let i = 0; i < dataCheckArr.length; i++) {
      if (dataCheckArr[i] && !dataCheckArr[i].validate()) {
        dataCheckArr[i].focus();
        return false;
      }
    }
    return true;
  };

  const verify = () => {
    // 判断是否预执行成功
    if (!starrockPar.isClickPreButton) {
      modal.error($t('kpi.task.common.error.tip'), $t('task.custom.resource.tip'));
      return false;
    }

    // 数据校验
    const dataCheckArr = [dataSourceSelect, sqlTextArea, indicatorKeySelect, timeKeySelect];
    if (!verifyData(dataCheckArr)) {
      return false;
    }
    return true;
  };

  const getViewTaskInfo = () => {
    dispatch({type: 'setIsLoadingShow', isLoadingShow: true});
    api.viewTask(
      {
        taskId: taskList.taskId,
      },
      res => {
        dispatch({type: 'setIsLoadingShow', isLoadingShow: false});
        if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
          modal.error($t('kpi.task.common.error.tip'), $t('kpi.task.list.view.fail'));
          return;
        }
        setTaskList(res.data);
        setTaskPageType(TASK_PAGE_TYPE.VIEW);
        dispatch({type: 'isSourceLoading', isSqlLoading: false});
        dispatch({type: 'setIsLoadingShow', isLoadingShow: false});
        dispatch({type: 'setRefreshByCustom', isRefreshByCustom: true});
      },
      err => {
        dispatch({type: 'setIsLoadingShow', isLoadingShow: false});
      }
    );
  };
  const submitCallBack = () => {
    getViewTaskInfo();
  };

  // 修改任务
  const setViewStarrockPar = ({
    columnMapping,
    datasourceId,
    sourceName,
    updateIndicatorAuto,
    indicatorDiscardTime,
  }) => {
    setStarrockPar({
      sqlDesc: '',
      isClickPreButton: false,
      sourceName,
      isAutoUpdateIndicator: updateIndicatorAuto ?? IS_AUTO_KEY.NO,
      indicatorDiscardTime: indicatorDiscardTime || DEFAULT_INDICATOR_DISCARD_TIME,
    });
  };

  useImperativeHandle(cRef, () => ({
    // 就是暴露给父组件的方法
    modifyTaskByKpi: operateType => {
      modifyTask(operateType);
    },
    switchModifyTaskByKpi: () => {
      switchModifyTask();
    },
    goStepByKpi: stepName => {
      goStep(stepName);
    },
    cancelModifyByKpi: () => {
      cancelModify();
    },
    submit: () => {
      submitCallBack();
    },

    verify: () => {
      return verify();
    },
  }));

  // 提取 measureObjectKeys 的构建逻辑
  const buildMeasureObjectKeys = keys => {
    return keys.reduce((acc, item) => {
      acc[item] = [];
      return acc;
    }, {});
  };

  const addTaskHandler = () => {
    // 提取公共变量
    const {
      taskTable,
      time: {all: timeAll, selected: timeSelected},
      originalList: {all: originalAll, selected: originalSelected},
      indicatorList: {all: indicatorAll, selected: indicatorSelected},
      sqlDesc,
      dataFormatValue,
      group: {selected: groupSelected},
    } = starrockPar;

    const timetext = timeAll.find(item => item.value === timeSelected)?.text || '';
    const originText = originalAll.filter(item => originalSelected.includes(item.value));
    const originString = originText.map(item => item.text).join(',');

    const indicatorText = indicatorAll.filter(item => indicatorSelected.includes(item.value));
    const indicatorString = indicatorText.map(item => item.text).join(',');
    // 构建 param 对象
    let unitId; let starrockId; let dn; let moType; let measUnit; let collectTask; let customSQL; let neName = 'N/A'; let name;
    let param = {};
    if (dataFormatValue === 0) {
      const {id, parentMoType} = currentTypeInfo;
      const {data: measureUnitData} = measureUnit;

      unitId = `${id}${measureUnitData[1].value}`;
      starrockId = `${id}${measureUnitData[1].value}`;
      dn = id;
      moType = parentMoType;
      measUnit = measureUnitData[1].value;
      collectTask = measureUnitData[0].value;
      name = measureUnitData[0].value;
      neName = currentTypeInfo?.name;

      param = {
        dn: id,
        moType: parentMoType,
        measUnit: measureUnitData[1].value,
        timestampKey: timeSelected,
        measureIndicatorKeys: indicatorSelected,
        measureObjectKeys: buildMeasureObjectKeys(originalSelected),
      };
    } else {
      unitId = sqlDesc.trim();
      starrockId = sqlDesc;
      name = sqlDesc;
      customSQL = sqlDesc;

      param = {
        customSQL: sqlDesc,
        timestampKey: timeSelected,
        measureIndicatorKeys: indicatorSelected,
        measureObjectKeys: buildMeasureObjectKeys(originalSelected),
      };
    }

    // 检查是否已存在相同 unitId 的任务
    const exists = taskTable.some(item => item.unitId === unitId);
    if (exists) {
      notification.error($t('task.duplicate'));
      return;
    }

    // 如果是同类比较任务，添加 indicatorGroupTag
    if (measureIndexList.kpiTaskType === INDICATOR_TASK_TYPE.homogeneousComparison && groupSelected) {
      param.indicatorGroupTag = groupSelected;
    }

    // 开始加载
    dispatch({type: 'setIsSourceLoading', isSourceLoading: true});
    // 查询指标数据
    queryMsindicatorsbyindicatorgroup(param, res => {
      dispatch({type: 'setIsSourceLoading', isSourceLoading: false});
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        modal.error($t('kpi.task.common.error.tip'), res.resultMessage);
        return;
      }
      if (res.data.length === 0) {
        modal.error($t('kpi.task.common.tip'), $t('ms.task.common.search.empty'));
        return;
      }
      // 处理返回数据
      const dataSet = res.data.map(item => {
        const values = item.originalValue.split(',').map(entry => entry.slice(entry.indexOf('=') + 1));
        return [item.indexName, ...values];
      });

      const orignColumns = res.data[0].originalValue.split(',').map(item => item.split('=')[0]);
      // 构建新任务项
      const newTaskItem = {
        dn,
        moType,
        measUnit,
        collectTask,
        customSQL,
        unitId,
        starrockId,
        starrockName: name,
        neName,
        starrockTime: timetext,
        measureObject: originString,
        originalList: deepClone(starrockPar.originalList),
        indicatorList: deepClone(starrockPar.indicatorList),
        time: deepClone(starrockPar.time),
        group: deepClone(starrockPar.group),
        measurementIndex: indicatorString,
        insTable: {
          columns: ['indicator', ...orignColumns],
          dataSet,
          filterMapping: {},
        },
      };

      // 更新任务表
      const newTaskTable = [newTaskItem, ...taskTable];

      setStarrockPar({
        taskTable: newTaskTable,
      });
    });
  };

  return (
    <>
      {taskPageType === TASK_PAGE_TYPE.VIEW && (
        <>
          {hashName !== TREND_MENU && (
            <tr>
              <td className="table_label">{$t('task.auto.update.kpi')}</td>
              <td> {IS_AUTO_KEY_TEXT[measureIndexList.isAutoUpdateMO]}</td>
            </tr>
          )}
          <tr>
            <td />
            <td>
              <ViewIndicatorGroup {...props} />
            </td>
          </tr>
        </>
      )}
      {(taskPageType === TASK_PAGE_TYPE.NEW ||
        taskPageType === TASK_PAGE_TYPE.COPY ||
        taskPageType === TASK_PAGE_TYPE.MODIFY) && (
        <>
          <tr style={{marginTop: '24px'}}>
            <td style={{verticalAlign: 'top'}}>
              <LabelHint label={$t('task.template.datasource')} require={true} isHelp={false} />
            </td>
            <td style={{paddingBottom: '24px'}}>
              <Select
                options={[
                  {
                    value: 1,
                    text: 'StarRock',
                  },
                ]}
                selectStyle={{width: '800px'}}
                id="starrockDataSource"
                required
                value={['StarRock']}
                validator={(val, id) => validate(['required'], val, id)}
                hintType="tip"
                disabled={true}
              />
            </td>
          </tr>
          {hashName !== TREND_MENU && (
            <>
              <tr style={{marginTop: '24px'}}>
                <td style={{verticalAlign: 'top'}}>
                  <LabelHint
                    label={$t('starrock.auto.update.kpi')}
                    require={true}
                    isHelp={true}
                    hint={$t('task.auto.update.kpi.help')}
                  />
                </td>
                <td style={{paddingBottom: '24px'}}>
                  <RadioGroup
                    rowSpacing="100px"
                    data={IS_AUTO_UPDATE_MO}
                    value={starrockPar.isAutoUpdateIndicator}
                    onChange={val =>
                      setStarrockPar({
                        isAutoUpdateIndicator: val,
                      })}
                  />
                </td>
              </tr>
              {starrockPar.isAutoUpdateIndicator && (
                <tr style={{marginTop: '24px'}}>
                  <td style={{verticalAlign: 'top'}}>
                    <LabelHint
                      label={$t('starrock.discard.duration.kpi')}
                      require={true}
                      isHelp={true}
                      hint={$t('task.discard.evaluation.time.help')}
                    />
                  </td>
                  <td style={{paddingBottom: '24px'}}>
                    <Spinner
                      min={1}
                      max={7}
                      step={1}
                      onChange={val =>
                        setStarrockPar({
                          spinner: val,
                        })}
                      value={starrockPar.spinner}
                    />
                    <LabelField text={$t('alarm.radio.day')} style={{marginLeft: '1rem'}} />
                  </td>
                </tr>
              )}
            </>
          )}

          <tr>
            <td colSpan={2} className="kpi-dialog" style={{margin: '2rem'}}>
              <div className="eui_PanelItem_title">
                <span style={{marginLeft: '1rem', fontWeight: '700'}}>{$t('kpi.task.list.starrock.source')}</span>
              </div>
            </td>
          </tr>
          <tr style={{marginTop: '24px'}}>
            <td style={{verticalAlign: 'top'}}>
              <LabelHint
                label={$t('starrock.data format')}
                require={true}
                isHelp={true}
                hint={
                  <div>
                    {$t('starrock.task.custom.sql.requirements.first.Step')}
                    <br />
                    {$t('task.custom.sql.requirements.second.Step')}
                    <br />
                    {$t('starrock.task.custom.sql.requirements.third.step')}
                    <br />
                  </div>
                }
              />
            </td>
            <td style={{paddingBottom: '24px'}}>
              <RadioGroup
                rowSpacing="100px"
                data={DATA_FORMAT}
                value={starrockPar.dataFormatValue}
                onChange={val =>
                  setStarrockPar({
                    ...emptySelect,
                    dataFormatValue: val,
                  })}
              />
            </td>
          </tr>
          <tr>
            <td />
            <td>
              {starrockPar.dataFormatValue === 0 && (
                <ul id="source_measure">
                  <li style={{marginRight: '1%', width: '40%'}}>
                    <FilterTree
                      treeData={treeData}
                      setCurrentTypeInfo={setCurrentTypeInfo}
                      currentTypeInfo={currentTypeInfo}
                    />
                  </li>
                  <li style={{marginRight: '1%', width: '40%'}}>
                    <UnitTable
                      currentTypeInfo={currentTypeInfo}
                      setMeasureUnit={setMeasureUnit}
                      pageInfo={pageInfo}
                      setPageInfo={setPageInfo}
                    />
                  </li>
                </ul>
              )}
              {starrockPar.dataFormatValue === 1 && (
                <>
                  <div style={{float: 'left'}}>
                    <TextArea
                      rows={5}
                      cols={32}
                      inputStyle={{resize: 'both', width: '800px'}}
                      value={starrockPar.sqlDesc}
                      maxLength={8000}
                      validator={(val, id) => validate(['required', 'msSqlValid'], val, id)}
                      hintType="tip"
                      id="sqlDesc"
                      onChange={val => changeSqlTextAreaOrSelect(val, 'sqlDesc')}
                      ref={field => (sqlTextArea = field)}
                    />
                  </div>
                  <Button
                    text={$t('task.custom.pre.execution')}
                    status="primary"
                    disabled={starrockPar.isClickPreButton}
                    style={{float: 'left', marginLeft: '20px'}}
                    onClick={preExecution}
                  />
                </>
              )}
            </td>
          </tr>
          <tr>
            <td>
              <LabelHint label={$t('task.custom.mapping.relationship')} require={true} isHelp={false} />
            </td>
            <td>
              <table className="mappingTable" style={{width: '100%'}} id="starrock-mappingTable">
                <thead>
                  <tr style={{backgroundColor: '#1919190D'}}>
                    <th style={{width: '45%', textAlign: 'left'}}>{$t('task.custom.indicator.info')}</th>
                    <th style={{textAlign: 'left'}}>
                      {$t('task.custom.correspond.table.fields')}
                      <div className="eui_table_hed_separator" />
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr style={{backgroundColor: '#ffffff'}}>
                    <td>
                      <LabelField text={$t('task.custom.measurement.index')} required={true} />
                    </td>
                    <td>
                      <MultipleSelect
                        onChange={indicatorOnChange}
                        disabled={false}
                        useOption={true}
                        enableCloseIcon={true}
                        inputStyle={{width: '670px'}}
                        value={starrockPar.indicatorList.selected}
                        options={starrockPar.indicatorList.all}
                        labelStyle={{height: '1.875rem'}}
                        placeholder={$t('tidal.forecast.task.please.select')}
                      />
                    </td>
                  </tr>
                  <tr style={{backgroundColor: '#ffffff'}}>
                    <td>
                      <LabelField text={$t('task.custom.measure.object')} required={true} />
                    </td>
                    <td>
                      <MultipleSelect
                        onChange={originalOnChange}
                        disabled={false}
                        useOption={true}
                        enableCloseIcon={true}
                        inputStyle={{width: '670px'}}
                        value={starrockPar.originalList.selected}
                        options={starrockPar.originalList.all}
                        placeholder={$t('tidal.forecast.task.please.select')}
                      />
                    </td>
                  </tr>
                  <tr style={{backgroundColor: '#ffffff'}}>
                    <td>
                      <LabelField text={$t('task.custom.time')} required={true} />
                    </td>
                    <td>
                      <SelectPro
                        options={starrockPar.time.all}
                        value={starrockPar.time.selected}
                        style={{width: '670px'}}
                        onChange={onSelectTime}
                      />
                    </td>
                  </tr>
                  {measureIndexList.kpiTaskType === INDICATOR_TASK_TYPE.homogeneousComparison && (
                    <tr style={{backgroundColor: '#ffffff'}}>
                      <td>
                        <LabelField text={$t('task.custom.group')} />
                      </td>
                      <td>
                        <MultipleSelect
                          onChange={onSelectGroup}
                          disabled={false}
                          useOption={true}
                          enableCloseIcon={true}
                          inputStyle={{width: '670px'}}
                          value={starrockPar.group.selected}
                          options={starrockPar.group.all}
                          placeholder={$t('tidal.forecast.task.please.select')}
                        />
                      </td>
                    </tr>
                  )}
                  <tr style={{'border-bottom': '0px', backgroundColor: '#ffffff'}}>
                    <td />
                    <td>
                      <Button
                        text={$t('kpi.task.list.starrock.add')}
                        status="primary"
                        style={{marginTop: '1rem', marginBottom: '20px', float: 'right'}}
                        size="large"
                        disabled={
                          starrockPar.indicatorList.selected.length === 0 ||
                        starrockPar.originalList.selected.length === 0 ||
                        starrockPar.time.selected === ''
                        }
                        onClick={addTaskHandler}
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </td>
          </tr>
          <tr>
            <td colSpan={2} className="kpi-dialog" style={{margin: '2rem'}}>
              <div className="eui_PanelItem_title">
                <span style={{marginLeft: '1rem', fontWeight: '700'}}>{$t('kpi.task.list.indicator.group')}</span>
              </div>
            </td>
          </tr>
          <tr>
            <td colSpan="2">
              <IndicatorGroup
                taskTable={starrockPar.taskTable}
                setStarrockPar={setStarrockPar}
                starrockPar={starrockPar}
                measureIndexList={measureIndexList}
              />
            </td>
          </tr>
          <Loader type="global" isOpen={isSourceLoading} desc={$t('kpi.task.please.wait')} id="sqlResourceLoader" />
        </>
      )}
    </>
  );
};
export default StarrockDataSource;
