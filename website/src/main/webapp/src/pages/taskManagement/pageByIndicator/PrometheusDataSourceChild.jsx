/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

import React, { useEffect, useRef, useContext, useImperativeHandle } from 'react';
import { RadioGroup, Spinner, Icon, Button, Loader, Select, Table, TextArea } from 'eview-ui';
import { $t, validate, modal, pages } from '@util';
import LabelHint from '@comp/LabelHint';
import * as api from '../api';
import {
  NewTaskContext, TASK_PAGE_TYPE, PROMETHEUS_RADIO_MAP, OPERATE_SUCCESS_CODE, ALGORITHM_TIME_MAP,
  VALID_FAIL_CODES, EXECUTED_RESULT_CODES, EXECUTED_RESULT_TIP, MAX_PQL_LENGTH, PROMETHEUS_TABLE_COLUMNS,
  IS_AUTO_UPDATE_MO, IS_AUTO_KEY, INDICATOR_DISCARD_TIME, DEFAULT_INDICATOR_DISCARD_TIME,
} from '../const';
import addPng from '@assets/add.png';
import deletePqlPng from '@assets/deletePql.png';
import successPng from '@assets/success.png';
import failedPng from '@assets/failed.png';

const PrometheusDataSourceChild = (props) => {
  const { taskList, cRef, setTaskPageType, taskPageType, setTaskList,
    getResourceParam, queryThirdPartyDataSources, submitTask, kpiDialogData, dataSourceType } = props;
  const [state, dispatch] = useContext(NewTaskContext);
  const { prometheusPar, isRefreshByPrometheus, measureIndexList } = state;
  // 用来存放点击修改按钮前的数据，点击取消后可恢复
  const medianResourceInfo = useRef({});
  const pqlInput = useRef({}); // pql表达式输入框ref
  const dataSourceSelect = useRef('');
  const prometheusInfo = useRef({});

  useEffect(() => {
    prometheusInfo.current = prometheusPar;
  }, [prometheusPar]);

  useEffect(() => {
    // 新建时查询Prometheus数据源列表
    if (taskPageType !== TASK_PAGE_TYPE.VIEW &&
      taskPageType !== TASK_PAGE_TYPE.VIEW_OTHER) {
      queryPrometheusDataSources();
    }

    if (isRefreshByPrometheus) {
      dispatch({
        type: 'setMeasureIndexList',
        measureIndexList: { ...measureIndexList, dataSourceType: measureIndexList.dataSourceType || dataSourceType },
      });

      // 查看、修改、复制从taskList读取数据
      if (taskPageType === TASK_PAGE_TYPE.VIEW) {
        setPrometheusData(taskList);
      }

      // 修改和复制时需要预执行
      if (taskPageType === TASK_PAGE_TYPE.COPY || taskPageType === TASK_PAGE_TYPE.MODIFY) {
        let pqlList = JSON.parse(taskList.prometheusData.pqls);
        validatePql({
          pqls: pqlList.map(item => item.pql),
          step: taskList.prometheusData.step,
          dataSourceId: taskList.datasourceId.toString(),
        }, false, taskList);
      }

      dispatch({ type: 'setRefreshByPrometheus', isRefreshByPrometheus: false });
    }

    // 查看复制修改从nodeparam读取数据
    if (taskPageType === TASK_PAGE_TYPE.MODIFY_OTHER || taskPageType === TASK_PAGE_TYPE.VIEW_OTHER) {
      modifyDataSourceByOther(kpiDialogData);
    }
  }, [taskPageType]);

  const queryPrometheusDataSources = () => {
    queryThirdPartyDataSources({
      type: measureIndexList.dataSourceType || dataSourceType,
    }, (optionArr) => {
      setPrometheusPar({ customDataSourceOptions: optionArr });
    }, err => {
      setPrometheusPar({ customDataSourceOptions: [] });
    });
  };

  // 查看时更新页面数据
  const setPrometheusData = ({ prometheusData, sourceName, datasourceId, updateIndicatorAuto, indicatorDiscardTime }) => {
    let pqlList = JSON.parse(prometheusData.pqls);
    setPrometheusPar({
      customDataSource: parseInt(datasourceId),
      isClickPreButton: true,
      spinner: prometheusData.step ? prometheusData.step.split('|')[0] : 1,
      timeRadio: prometheusData.step ? prometheusData.step.split('|')[1] : 'MIN',
      pqlList,
      preCode: EXECUTED_RESULT_CODES.SUCCESS,
      sourceName,
      isAutoUpdateIndicator: updateIndicatorAuto ?? IS_AUTO_KEY.NO,
      indicatorDiscardTime: indicatorDiscardTime || DEFAULT_INDICATOR_DISCARD_TIME,
    });
  };

  const changePqlList = (index, val, key, id) => {
    let list = [...prometheusPar.pqlList];
    list[index][key] = val;
    setPrometheusPar({
      pqlList: list,
      isClickPreButton: key === 'alias' ? prometheusInfo.current.isClickPreButton : false,
      preCode: key === 'alias' ? prometheusInfo.current.preCode : EXECUTED_RESULT_CODES.START,
    });
    $(id).removeClass('input_error');
  };

  // +号添加pql输入框
  const addTextField = () => {
    if (prometheusPar.pqlList.length === MAX_PQL_LENGTH) {
      modal.error($t('kpi.task.common.error.tip'), $t('task.prometheus.number.limit'));
      return;
    }
    setPrometheusPar({
      pqlList: [...prometheusPar.pqlList, {
        pql: '',
        alias: '',
      }],
      isClickPreButton: false,
      preCode: EXECUTED_RESULT_CODES.START,
    });
  };

  // 删除单个PQL表达式
  const deleteTextField = (index) => {
    let list = [...prometheusPar.pqlList];
    list.splice(index, 1);
    setPrometheusPar({ pqlList: list, isClickPreButton: false, preCode: EXECUTED_RESULT_CODES.START });
    $(`#pql_${index}_area`).removeClass('input_error');
  };

  // 更新页面公共方法
  const setPrometheusPar = (param) => {
    dispatch({ type: 'setPrometheusPar', prometheusPar: { ...prometheusInfo.current, ...param } });
  };

  // eview-ui组件回调方法
  const changeCallBack = (val, key) => {
    setPrometheusPar({ [key]: val, isClickPreButton: false, preCode: EXECUTED_RESULT_CODES.START });
  };

  // prometheus数据源select选择回调
  const changeDataSourceSelect = (val, oldVal, text) => {
    setPrometheusPar({
      customDataSource: val,
      isClickPreButton: false,
      preCode: EXECUTED_RESULT_CODES.START,
      sourceName: text,
    });
  };

  const verify = () => {
    let inputList = Object.keys(pqlInput.current);
    for (let i = 0; i < inputList.length; i++) {
      const dataCheckArr = [pqlInput.current[`pql_${i}`], pqlInput.current[`alias_${i}`]];
      if (!pages.validateDataLists(dataCheckArr)) {
        return false;
      }
    }

    // 数据校验
    const dataCheckArr = [dataSourceSelect.current];
    if (!pages.validateDataLists(dataCheckArr)) {
      return false;
    }

    for (let i = 0; i < prometheusPar.pqlList.length; i++) {
      let validInput = pqlInput.current[`pql_${i}`];
      if (validInput && prometheusPar.pqlList.findIndex(item => item.pql === validInput.getValue()) !== i) {
        modal.error($t('kpi.task.common.error.tip'), $t('task.prometheus.cannot.repeat'));
        $(`#pql_${i}_area`).addClass('input_error');
        validInput.focus();
        return false;
      }
      $(`#pql_${i}_area`).removeClass('input_error');
    }

    return true;
  };

  // 预执行
  const preExecution = () => {
    if (!verify()) {
      return;
    }

    validatePql({
      pqls: prometheusPar.pqlList.map(item => item.pql),
      step: `${prometheusPar.spinner}|${prometheusPar.timeRadio}`,
      dataSourceId: prometheusPar.customDataSource.toString(),
    }, true, {});
  };

  const validatePql = (param, isNew, { prometheusData, sourceName, datasourceId }) => {
    // 新建时只需要转圈，修改和复制时需要更新相关任务数据
    if (isNew) {
      setPrometheusPar({ isPrometheusLoading: true });
    } else {
      let pqlList = JSON.parse(prometheusData.pqls);
      setPrometheusPar({
        isPrometheusLoading: true,
        customDataSource: parseInt(datasourceId),
        spinner: prometheusData.step ? prometheusData.step.split('|')[0] : 1,
        timeRadio: prometheusData.step ? prometheusData.step.split('|')[1] : 'MIN',
        pqlList,
        sourceName,
      });
    }

    api.validatePql(param, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        preFailed();
        return;
      }

      if (res.data) {
        setPrometheusPar({
          isClickPreButton: true,
          preCode: res.data.code,
          isPrometheusLoading: false,
          executionResults: res.data.code === EXECUTED_RESULT_CODES.VALID_FAIL ?
            handlePrometheusRes(res.data.resultMessage) : [],
        });

        if (res.data.code === EXECUTED_RESULT_CODES.VALID_FAIL) {
          modal.error($t('kpi.task.common.error.tip'), $t('task.prometheus.valid.fail.tip'));
        }
      } else {
        setPrometheusPar({ isPrometheusLoading: false, executionResults: [] });
      }
    }, err => {
      preFailed();
    });
  };

  const preFailed = () => {
    setPrometheusPar({
      isClickPreButton: false,
      preCode: EXECUTED_RESULT_CODES.FAIL,
      isPrometheusLoading: false,
      executionResults: [],
    });
  };

  // 处理预执行接口数据
  const handlePrometheusRes = (result) => {
    let executedResult = result.map(item => {
      if (item.code === VALID_FAIL_CODES.SUCCESS) {
        return '';
      } else if (item.code === VALID_FAIL_CODES.EXECUTE_FAILED) {
        return $t('kpi.task.execute.fail');
      } else {
        return item.data;
      }
    });
    return executedResult;
  };

  // 获取Prometheus数据源提交入参
  const getParam = (isCustom) => {
    let resourceParam = {
      datasourceId: prometheusPar.customDataSource.toString(),
      prometheusData: {
        pqls: JSON.stringify(prometheusPar.pqlList),
        step: `${prometheusPar.spinner}|${prometheusPar.timeRadio}`,
      },
      updateIndicatorAuto: prometheusPar.isAutoUpdateIndicator,
      indicatorDiscardTime: prometheusPar.isAutoUpdateIndicator === IS_AUTO_KEY.YES ? prometheusPar.indicatorDiscardTime : undefined,
    };

    let param = isCustom ? resourceParam : { ...taskList, ...resourceParam };
    return param;
  };

  const verifyPre = () => {
    // 判断是否预执行成功
    if (!prometheusPar.isClickPreButton || prometheusPar.preCode !== EXECUTED_RESULT_CODES.SUCCESS) {
      modal.error($t('kpi.task.common.error.tip'), $t('task.prometheus.resource.pre.tip'));
      return false;
    }

    if (!verify()) {
      return false;
    }

    return true;
  };

  const submitCallBack = () => {
    let param = getParam(false);
    dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
    setTaskList({ ...taskList, ...param });
    setTaskPageType(TASK_PAGE_TYPE.VIEW);
    dispatch({ type: 'setRefreshByPrometheus', isRefreshByPrometheus: true });
  };

  // 修改任务
  const saveTask = (operateType) => {
    if (!verifyPre()) {
      return;
    }

    let param = getParam(false);
    submitTask(operateType, param, () => {
      submitCallBack();
    });
  };

  // 上一步或下一步
  const goStep = ({ stepName, type }) => {
    let switchTabData = '';
    if (type === 'next') {
      if (!verifyPre()) {
        return;
      }

      switchTabData = getParam();
    }

    dispatch({
      type: 'setPrometheusResourceTab',
      currentTab: stepName,
      isRefreshByPrometheus: false,
      resourceData: switchTabData,
    });
  };

  // 点击修改按钮
  const switchModifyTask = () => {
    medianResourceInfo.current = { ...prometheusPar };
    setTaskPageType(TASK_PAGE_TYPE.MODIFY);
    dispatch({ type: 'setRefreshByPrometheus', isRefreshByPrometheus: true });
  };

  // 取消修改
  const cancelModify = () => {
    setTaskPageType(TASK_PAGE_TYPE.VIEW);
    // 取消修改，还原点击修改前的数据
    setPrometheusPar({ ...medianResourceInfo.current });
  };

  // 异常关联创建模板指标节点，修改时回调方法
  const modifyDataSourceByOther = (nodeParam) => {
    if (taskPageType === TASK_PAGE_TYPE.VIEW_OTHER) {
      setPrometheusData(nodeParam);
    }

    if (taskPageType === TASK_PAGE_TYPE.MODIFY_OTHER) {
      validatePql({
        pqls: JSON.parse(nodeParam.prometheusData.pqls).map(item => item.pql),
        step: nodeParam.prometheusData.step,
        dataSourceId: nodeParam.datasourceId,
      }, false, nodeParam);
    }
  };

  useImperativeHandle(cRef, () => ({
    // 就是暴露给父组件的方法
    modifyTaskByKpi: (operateType) => {
      saveTask(operateType);
    },
    switchModifyTaskByKpi: () => {
      switchModifyTask();
    },
    submit: () => {
      submitCallBack();
    },
    cancelModifyByKpi: () => {
      cancelModify();
    },
    getIndicatorParam: (operateType, isCustom) => {
      return {
        param: {
          ...getParam(isCustom),
          sourceName: prometheusPar.sourceName,
        },
      };
    },
    goStepByKpi: ({ stepName, type }) => {
      goStep({ stepName, type });
    },
    clearDataSource: () => {
      dispatch({
        type: 'clearPrometheusSourceData',
        isRefreshByPrometheus: true,
        measureIndexList: { ...measureIndexList, dataSourceType: null },
        prometheusPar: {
          pqlList: [{
            pql: '',
            alias: '',
          }],
          spinner: 1,
          timeRadio: 'MIN',
          isClickPreButton: false,
          customDataSource: '',
          customDataSourceOptions: [],
          preCode: 3,
          isAutoUpdateIndicator: false,
          indicatorDiscardTime: DEFAULT_INDICATOR_DISCARD_TIME,
        },
      });
    },
    modifyDataSourceByOther: (nodeParam) => {
      modifyDataSourceByOther(nodeParam);
    },
    verify: () => {
      return verifyPre();
    },
  }));

  return (
    <>
      {(taskPageType === TASK_PAGE_TYPE.VIEW || taskPageType === TASK_PAGE_TYPE.VIEW_OTHER) ?
        <>
          <tr>
            <td className='table_label'>{$t('task.template.datasource')}：</td>
            <td>
              {prometheusPar.sourceName}
            </td>
          </tr>
          <tr>
            <td className='table_label'>{$t('task.prometheus.pql.expression')}：</td>
            <td>
              <Table id="pqlTable"
                columns={PROMETHEUS_TABLE_COLUMNS} showEmptyImage={false}
                maxHeight={530}
                splitPagination={false} dataset={prometheusPar.pqlList}
              />
            </td>
          </tr>
          <tr>
            <td className='table_label'>{$t('task.prometheus.collection.period')}：</td>
            <td>
              {prometheusPar.spinner}{ALGORITHM_TIME_MAP[prometheusPar.timeRadio]}
            </td>
          </tr>
          <tr>
            <td className='table_label'>{$t('task.auto.update.kpi')}：</td>
            <td>
              {prometheusPar.isAutoUpdateIndicator ? $t('kpi.new.task.yes') : $t('kpi.new.task.no')}
            </td>
          </tr>
          {prometheusPar.isAutoUpdateIndicator === IS_AUTO_KEY.YES &&
            <tr>
              <td className='table_label'>{$t('task.discard.evaluation.time')}：</td>
              <td>
                {prometheusPar.indicatorDiscardTime === DEFAULT_INDICATOR_DISCARD_TIME ? $t('common.one.day') : $t('common.seven.day')}
              </td>
            </tr>}

        </> :
        <>
          <tr style={{ marginTop: '24px' }}>
            <td>
              <LabelHint label={$t('task.template.datasource')} require={true} isHelp={false} />
            </td>
            <td>
              <Select options={prometheusPar.customDataSourceOptions}
                selectStyle={{ width: '800px' }} id='prometheusSource' required
                value={prometheusPar.customDataSource}
                validator={(val, id) => validate(['required'], val, id)} hintType='tip'
                ref={field => dataSourceSelect.current = field}
                onChange={changeDataSourceSelect}
              />
            </td>
          </tr>
          <tr>
            <td>
              <LabelHint label={$t('task.prometheus.pql.expression')}
                require={true} isHelp={false}
              />
            </td>
            <td style={{ paddingBottom: '5px' }}>
              <TextArea inputStyle={{ width: '590px' }} hintType='tip'
                placeholder={$t('task.prometheus.pql.expression')}
                value={prometheusPar.pqlList[0].pql} id="pql_0" rows={2}
                style={{ float: 'left', width: '590px', marginRight: '10px' }}
                validator={(val, id) => validate(['required', 'validOnlyDigits'], val, id)} maxLength={8000}
                ref={textField => pqlInput.current.pql_0 = textField} autoComplete="off"
                onChange={(val) => changePqlList(0, val, 'pql', '#pql_0_area')}
              />
              <TextArea inputStyle={{ width: '200px', float: 'left' }} hintType='tip'
                placeholder={$t('task.prometheus.pql.alias')} style={{ float: 'left', width: '200px' }}
                value={prometheusPar.pqlList[0].alias} id="alias_0" rows={2} maxLength={64}
                validator={(val, id) => validate(['indicatorNameValidChar'], val, id)}
                ref={textField => pqlInput.current.alias_0 = textField} autoComplete="off"
                onChange={(val) => changePqlList(0, val, 'alias', '#alias_0_area')}
              />
              <Icon style={{ marginLeft: '8px', marginTop: '15px', cursor: 'pointer', float: 'left' }}
                iconUrl={addPng} size={['1.5rem', '1.5rem']}
                onClick={addTextField}
              />
              {(prometheusPar.preCode === EXECUTED_RESULT_CODES.VALID_FAIL && prometheusPar.executionResults[0]) &&
                <TextArea inputStyle={{ width: '240px', background: '#000', color: '#fff', fontSize: '12px' }}
                  value={`${$t('task.custom.pre.execution.result')}${prometheusPar.executionResults[0]}`}
                  rows={2} className="pql-result"
                />}
            </td>
          </tr>
          {prometheusPar.pqlList.map((item, index) => {
            if (index !== 0) {
              return (
                <tr key={`pql_${index}`}>
                  <td />
                  <td>
                    <TextArea inputStyle={{ width: '590px' }} hintType='tip'
                      value={prometheusPar.pqlList[index].pql} id={`pql_${index}`}
                      placeholder={$t('task.prometheus.pql.expression')} maxLength={8000}
                      validator={(val, id) => validate(['required', 'validOnlyDigits'], val, id)}
                      rows={2} style={{ float: 'left', width: '590px', marginRight: '10px' }}
                      ref={ele => pqlInput.current[`pql_${index}`] = ele} autoComplete="off"
                      onChange={(val) => changePqlList(index, val, 'pql', `#pql_${index}_area`)}
                    />
                    <TextArea inputStyle={{ width: '200px', float: 'left' }} hintType='tip'
                      placeholder={$t('task.prometheus.pql.alias')} style={{ width: '200px', float: 'left' }}
                      value={prometheusPar.pqlList[index].alias} rows={2}
                      id={`alias_${index}`} maxLength={64}
                      validator={(val, id) => validate(['indicatorNameValidChar'], val, id)}
                      ref={textField => pqlInput.current[`alias_${index}`] = textField} autoComplete="off"
                      onChange={(val) => changePqlList(index, val, 'alias', `#alias_${index}_area`)}
                    />
                    <Icon style={{ marginLeft: '8px', marginTop: '15px', cursor: 'pointer', float: 'left' }}
                      iconUrl={deletePqlPng}
                      size={['1.5rem', '1.5rem']}
                      onClick={() => deleteTextField(index)}
                    />
                    {(prometheusPar.preCode === EXECUTED_RESULT_CODES.VALID_FAIL &&
                        prometheusPar.executionResults[index]) &&
                        <TextArea inputStyle={{
                          width: '240px', background: '#000', color: '#fff', fontSize: '12px',
                        }}
                        value={`${$t('task.custom.pre.execution.result')}${prometheusPar.executionResults[index]}`}
                        rows={2} className="pql-result"
                        />}
                  </td>
                </tr>
              );
            }
            return null;
          })}
          <tr style={{ marginTop: '24px' }}>
            <td>
              <LabelHint label={$t('task.prometheus.collection.period')}
                require={true} isHelp={false}
              />
            </td>
            <td>
              <div style={{ float: 'left', width: '800px' }}>
                <Spinner value={prometheusPar.spinner} min={1}
                  step={1} max={500}
                  style={{ marginRight: '20px', width: '200px' }}
                  onChange={(val) => changeCallBack(val, 'spinner')}
                />
                <RadioGroup data={PROMETHEUS_RADIO_MAP} value={prometheusPar.timeRadio}
                  rowSpacing="20px"
                  onChange={(val) => changeCallBack(val, 'timeRadio')}
                />
              </div>
              <Button text={$t('task.custom.pre.execution')} status="primary"
                style={{ float: 'left' }}
                disabled={prometheusPar.isClickPreButton}
                onClick={preExecution}
              />
              {prometheusPar.preCode !== EXECUTED_RESULT_CODES.START &&
                <div style={{ float: 'left', paddingTop: '8px', paddingLeft: '10px' }}>
                  <div style={{ float: 'left' }}>
                    <Icon style={{ marginTop: '5px' }}
                      iconUrl={prometheusPar.preCode == EXECUTED_RESULT_CODES.SUCCESS ? successPng : failedPng}
                      size={['1rem', '1rem']}
                    />
                  </div>
                  <span style={{ fontSize: '14px' }}>
                    {EXECUTED_RESULT_TIP[prometheusPar.preCode]}
                  </span>
                </div>}
            </td>
          </tr>
          <tr>
            <td style={{ lineHeight: '2rem' }}>
              <LabelHint
                label={$t('task.auto.update.kpi')}
                hint={$t('task.auto.update.kpi.help')}
                require={true} isHelp={true}
              />
            </td>
            <td>
              <RadioGroup rowSpacing='100px' data={IS_AUTO_UPDATE_MO}
                value={prometheusPar.isAutoUpdateIndicator}
                onChange={val => setPrometheusPar({ isAutoUpdateIndicator: val, indicatorDiscardTime: DEFAULT_INDICATOR_DISCARD_TIME })}
              />
            </td>
          </tr>
          {prometheusPar.isAutoUpdateIndicator === IS_AUTO_KEY.YES &&
            <tr>
              <td style={{ lineHeight: '2rem' }}>
                <LabelHint
                  label={$t('task.discard.evaluation.time')}
                  hint={$t('task.discard.evaluation.time.help')}
                  require={true} isHelp={true}
                />
              </td>
              <td>
                <RadioGroup rowSpacing='100px' data={INDICATOR_DISCARD_TIME}
                  value={prometheusPar.indicatorDiscardTime}
                  onChange={val => setPrometheusPar({ indicatorDiscardTime: val })}
                />
              </td>
            </tr>}
        </>}
      <Loader type="global" isOpen={prometheusPar.isPrometheusLoading} desc={$t('kpi.task.please.wait')} id="prometheusLoader" />
    </>
  );
};

export default PrometheusDataSourceChild;
