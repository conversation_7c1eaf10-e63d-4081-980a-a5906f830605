import React, { useEffect, useRef, useState } from 'react';
import { $t, modal, validate } from '@util';
import * as api from '../api';
import { Col, Dialog, Loader, Row, Table } from 'eview-ui';
import { RESULT_CODE } from '@const/common';
import SearchInput from '@comp/SearchInput';
import { TASK_PAGE_TYPE } from '@pages/taskManagement/const';

const TABLE_SELECTED_COLUMNS = [
  {
    key: 'id',
    display: false,
  },
  {
    title: $t('associated.indicator.name'),
    key: 'indicatorName',
    width: '30%',
    allowSort: false,
  },
  {
    title: $t('associated.indicator.desc'),
    key: 'info',
    width: '55%',
    allowSort: false,
  },
  {
    title: $t('associated.indicator.user'),
    key: 'userName',
    width: '15%',
    allowSort: false,
  },
];

const ConfigureDataSetDialog = ({ setDialogOpen, taskPageType, initData, submitCallBackFn }) => {
  const [searchValue, setSearchValue] = useState('');
  const { isNew, rowId, historyIndicatorId, historyIndicatorName, mainIndicatorInfo } = initData;
  const [dataSetPar, setDataSetPar] = useState({
    isLoadingShow: false,
    checkedRows: [],
    pageSize: 8,
    pageIndex: 1,
    selectedDataSet: '',
    selectedId: '',
    recordCount: 0,
    rowsData: [],
    autoRefreshTimeStamp: 0,
    emptyTableMsg: $t('kpi.task.list.loading'),
  });
  const searchRef = useRef(''); // 指标名称过滤框Ref

  useEffect(() => {
    // 回显数据
    if (!isNew) {
      setDataSetParam({
        selectedDataSet: historyIndicatorName,
        selectedId: historyIndicatorId,
        checkedRows: [historyIndicatorId],
      });
    }
  }, []);

  useEffect(() => {
    if (taskPageType === TASK_PAGE_TYPE.VIEW) {
      getPreIndicatorInfo();
    } else {
      if (searchRef.current && !searchRef.current?.validate()) {
        searchRef.current.focus();
        return;
      }

      getTableData();
    }
  }, [dataSetPar.autoRefreshTimeStamp, dataSetPar.pageIndex, dataSetPar.pageSize]);

  // 查询关联导入列表
  const getTableData = () => {
    setDataSetParam({ isLoadingShow: true, emptyTableMsg: $t('kpi.task.list.loading') });
    api.getFileList({
      indicatorName: searchValue,
      paging: { pageSize: dataSetPar.pageSize, pageNumber: dataSetPar.pageIndex },
    }, res => {
      if (!res || res.resultCode !== RESULT_CODE.success) {
        setDataSetParam({ isLoadingShow: false, rowsData: [], emptyTableMsg: $t('task.common.search.fail') });
        return;
      }

      let rows = [];
      if (res.data?.rows && res.data.rows.length) {
        rows = res.data.rows;
      }
      setDataSetParam({
        recordCount: res.data?.total ?? 0,
        rowsData: rows,
        isLoadingShow: false,
        emptyTableMsg: $t('kpi.task.list.no.data'),
      });
    }, () => {
      setDataSetParam({ isLoadingShow: false, rowsData: [], emptyTableMsg: $t('task.common.search.fail') });
    });
  };

  // 查询已选中的单个关联导入指标信息
  const getPreIndicatorInfo = () => {
    setDataSetParam({ isLoadingShow: true, emptyTableMsg: $t('kpi.task.list.loading') });
    api.getHistoryIndicator(historyIndicatorId, res => {
      if (!res || res.resultCode !== RESULT_CODE.success) {
        setDataSetParam({ isLoadingShow: false, rowsData: [], emptyTableMsg: $t('task.common.search.fail') });
        return;
      }

      let rows = [];
      if (res.data) {
        rows = [{
          id: res.data.id,
          indicatorName: res.data.indicatorName,
          info: res.data.info,
          userName: res.data.userName,
        }];
      }
      setDataSetParam({
        recordCount: 1,
        rowsData: rows,
        isLoadingShow: false,
        emptyTableMsg: $t('kpi.task.list.no.data'),
      });
    }, () => {
      setDataSetParam({ isLoadingShow: false, rowsData: [], emptyTableMsg: $t('task.common.search.fail') });
    });
  };

  // 指标名称过滤搜索
  const handleSearch = (event) => {
    if (event.keyCode === 13 || event.type === 'click') {
      if (searchRef.current && !searchRef.current?.validate()) {
        searchRef.current.focus();
        return;
      }

      setDataSetParam({ pageIndex: 1, autoRefreshTimeStamp: new Date().getTime() });
    }
  };

  // 确定按钮回调
  const submit = () => {
    submitCallBackFn({
      historyIndicatorId: dataSetPar.selectedId,
      historyIndicatorName: dataSetPar.selectedDataSet,
    }, rowId, mainIndicatorInfo);
    setDialogOpen(false);
  };

  // 选中
  const onCheck = (row, checkedRows) => {
    setDataSetPar({
      ...dataSetPar,
      selectedDataSet: row._org?.indicatorName,
      selectedId: row._org?.id,
      checkedRows,
    });
  };

  // 清空
  const clearAll = () => {
    submitCallBackFn({
      historyIndicatorId: '',
      historyIndicatorName: '',
    }, rowId, mainIndicatorInfo);
    setDialogOpen(false);
  };

  const setDataSetParam = (param) => {
    setDataSetPar(_dataSetParam => ({ ..._dataSetParam, ...param }));
  };

  return (
    <Dialog
      title={$t('associated.indicator.dialog.title')}
      size={[720, 800]}
      closeOnEscape={false}
      style={{ maxHeight: '720px', minHeight: '720px', maxWidth: '800px', minWidth: '800px' }}
      isOpen={true}
      onClose={() => setDialogOpen(false)}
      resizable={false}
      buttons={taskPageType !== TASK_PAGE_TYPE.VIEW ? [{
        text: $t('task.common.cancel'),
        onClick: () => setDialogOpen(false),
      }, {
        text: $t('associated.indicator.clear'),
        status: 'primary',
        onClick: clearAll,
      }, {
        text: $t('task.common.confirm'),
        status: 'primary',
        onClick: () => submit(),
      }] : [{
        text: $t('task.common.close'),
        status: 'primary',
        onClick: () => setDialogOpen(false),
      }]}
    >
      {
        taskPageType !== TASK_PAGE_TYPE.VIEW &&
        <Row style={{ marginBottom: '20px' }}>
          <Col cols={24} >
            <div style={{ float: 'left' }}>
              <SearchInput
                id="kpiSearchInput"
                placeholder={$t('associated.indicator.name')}
                inputStyle={{ width: '240px' }}
                value={searchValue}
                onChange={val => setSearchValue(val)}
                onSearch={handleSearch}
                validator={(val, id) => validate(['indicatorNameValidChar', 'checkLength'], val, id, null, 600)}
                onRef={ref => (searchRef.current = ref)}
              />
            </div>
          </Col>
        </Row>
      }
      <Row>
        <Col cols={24}>
          <Table
            columns={TABLE_SELECTED_COLUMNS}
            showEmptyImage={false}
            enableCheckBox={taskPageType !== TASK_PAGE_TYPE.VIEW}
            maxHeight={565}
            keyIndex={0}
            checkType="single"
            width='100%'
            enablePagination={true} enableScrollPagination={true}
            pageSize={dataSetPar.pageSize}
            currentPage={dataSetPar.pageIndex}
            recordCount={dataSetPar.recordCount}
            pagingType="select"
            onRowCheck={onCheck}
            checkedRows={dataSetPar.checkedRows}
            onPageChange={pageIndex => setDataSetParam({ pageIndex })}
            onPageSizeChange={pageSize => setDataSetParam({ pageIndex: 1, pageSize })}
            pageSizeOptions={[8]}
            dataset={dataSetPar.rowsData}
            emptyTableMsg={dataSetPar.emptyTableMsg}
          />
        </Col>
      </Row>
      {
        dataSetPar.selectedDataSet && taskPageType !== TASK_PAGE_TYPE.VIEW &&
        <Row style={{ marginTop: '10px' }}>
          <Col cols={24}>
            <span>{$t('associated.indicator.selected')}</span>
            <span>{dataSetPar.selectedDataSet}</span>
          </Col>
        </Row>
      }
      <Loader type="global" isOpen={dataSetPar.isLoadingShow} desc={$t('kpi.task.please.wait')} />
    </Dialog>
  );
};

export default ConfigureDataSetDialog;


