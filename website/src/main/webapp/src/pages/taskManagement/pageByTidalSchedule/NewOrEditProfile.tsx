/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import React, { useContext, useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Table, Select, TextButton, Button, Loader, TextField, Row, Col, Tooltip } from 'eview-ui';
import {
  NewTaskContext, OPERATE_SUCCESS_CODE, TASK_PAGE_TYPE, TRAIN_STATUS_MAP, TASK_TYPE_MAP, TASK_STATUS,
  IS_CAN_MODIFY_TASK, LAST_EXECUTE_STATUS, TASK_TYPE_MODIFY_TIP,
} from '../const';
import LabelHint from '@comp/LabelHint';
import * as api from '../api';
import { $t, util, modal, validate, pages } from '@util';
import {
  RELATED_PREDICT_TABLE_COLUMNS, TIDAL_STEPS, MIN_RELATED_FORECAST_TASK, MAX_RELATED_FORECAST_TASK,
} from '../const/tidal';
import { modifyTaskCommon, validEmpty } from '../util/pages';

let customRelatedTaskRef = {
  upperThreshold: [],
  lowerThreshold: [],
};
const NewOrEditProfile = props => {
  const { taskList, newOrUpdateTask, setTaskList, isCanModify } = props;
  const [state, dispatch] = useContext(NewTaskContext);
  const [taskPageType, setTaskPageType] = useState(props.taskPageType);
  const { profileInfo } = state;
  const profileCache = useRef({});
  const profileSelect = useRef(null); // 服务画像
  const sharedIndicatorSelect = useRef(null);// 调度指标
  const relatedPredictTaskData = useRef(profileInfo.relatedTaskTableData);
  const medianProfileInfo = useRef({});

  useEffect(() => {
    profileCache.current = { ...profileInfo };
  }, [profileInfo]);

  useEffect(() => {
    if (profileInfo.isRefreshByProfile && taskPageType !== TASK_PAGE_TYPE.VIEW) {
      queryServiceProfileList();
      querySiteList();
    }

    const {
      associateProductPortraitId, associateProductPortraitName, scheduleDependentIndicator, siteId, scheduleConfig,
    } = taskList;
    let sharedIndicatorInfo = {
      displayValue: '',
      returnValue: '',
    };
    const { displayValue, returnValue } = typeof scheduleDependentIndicator === 'string' ?
      JSON.parse(scheduleDependentIndicator) : sharedIndicatorInfo;
    if (profileInfo.isRefreshByProfile && (taskPageType === TASK_PAGE_TYPE.MODIFY)) {
      querySharedIndicator(associateProductPortraitId, associateProductPortraitName, returnValue, displayValue, false);
      let site = validEmpty(siteId) ? '' : siteId;
      queryPredictTaskList(returnValue, displayValue, site, false, scheduleConfig, associateProductPortraitId);
    }

    if (profileInfo.isRefreshByProfile && taskPageType === TASK_PAGE_TYPE.VIEW) {
      setProfileInfo({
        profileId: associateProductPortraitId,
        profileName: associateProductPortraitName,
        sharedIndicator: returnValue,
        sharedIndicatorName: displayValue,
        site: siteId,
        predictTaskList: getTableRows(scheduleConfig, true),
      });
    }
  }, [taskPageType]);

  // 查询服务画像列表
  const queryServiceProfileList = () => {
    setProfileInfo({ isServiceProfileShow: true });
    api.queryServiceProfile({
      portraitType: [1], // 主机类型
      portraitName: '',
      paging: { pageSize: 20, pageNumber: 1, sortField: '', sortType: '' },
    }, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        modal.error($t('kpi.task.common.error.tip'), $t('tidal.search.service.profile.fail.tip'));
        setProfileInfo({ isServiceProfileShow: false, profileOptions: [] });
        return;
      }

      let profileList = [];
      if (res.data && res.data.rows && res.data.rows.length) {
        let rows = res.data.rows;
        for (let i = 0; i < rows.length; i++) {
          if (!rows[i].isAssociated ||
            (taskPageType === TASK_PAGE_TYPE.MODIFY && rows[i].id === taskList.associateProductPortraitId)) {
            profileList.push({ value: rows[i].id, text: rows[i].name, id: rows[i].id });
          }
        }
      }
      setProfileInfo({ isServiceProfileShow: false, profileOptions: profileList });
    }, err => {
      setProfileInfo({ isServiceProfileShow: false, profileOptions: [] });
    });
  };

  // 选择服务画像，查询画像调度指标
  const querySharedIndicator = (value, text, sharedIndicator = '', sharedIndicatorName = '', isOnChange = true) => {
    setProfileInfo({ isIndicatorShow: true });
    api.viewServiceProfile({
      productPortraitId: value,
    }, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        modal.error($t('kpi.task.common.error.tip'), $t('tidal.search.shared.indicator.fail.tip'));
        setProfileInfo(getSetProfileInfoParam([], value, text, sharedIndicator, sharedIndicatorName, isOnChange));
        return;
      }

      let sharedIndicatorOptions = [];
      if (res.data && res.data.sharedIndicators) {
        sharedIndicatorOptions = res.data.sharedIndicators.map((item) => ({
          value: item.returnValue, text: item.displayValue,
        }));
      }

      relatedPredictTaskData.current = [];
      setProfileInfo(getSetProfileInfoParam(sharedIndicatorOptions, value, text, sharedIndicator, sharedIndicatorName, isOnChange));
    }, err => {
      setProfileInfo(getSetProfileInfoParam([], value, text, sharedIndicator, sharedIndicatorName, isOnChange));
    });
  };

  const getSetProfileInfoParam = (sharedIndicatorOptions, profileId, profileName, sharedIndicator, sharedIndicatorName, isOnChange) => {
    let commonPar = {
      sharedIndicatorList: sharedIndicatorOptions,
      profileId,
      profileName,
      sharedIndicator,
      sharedIndicatorName,
      emptyTableMsg: $t('kpi.task.list.no.data'),
      isIndicatorShow: false,
    };
    if (isOnChange) {
      return { ...commonPar, relatedTaskTableData: [], predictTaskList: [] };
    } else {
      return commonPar;
    }
  };

  // 查询站点信息
  const querySiteList = () => {
    setProfileInfo({ isSiteShow: true });
    api.querySiteList({}, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        setProfileInfo({ isSiteShow: false, siteList: [] });
        modal.error($t('kpi.task.common.error.tip'), $t('tidal.search.site.fail.tip'));
        return;
      }

      let list = [];
      if (res.data) {
        res.data.forEach(item => {
          list = [...list, ...item.siteList];
        });
      }

      // 站点需要去重
      list = [...new Set(list)].map(site => ({ text: site, value: site }));
      setProfileInfo({ isSiteShow: false, siteList: list });
    }, err => {
      setProfileInfo({ isSiteShow: false, siteList: [] });
    });
  };

  // 调度指标change回调
  const onChangeSharedIndicator = (val, oldValue, text) => {
    queryPredictTaskList(val, text, profileInfo.site, true);
  };

  const handleFailure = (sharedIndicator, site, sharedIndicatorName) => {
    relatedPredictTaskData.current = [];
    setProfileInfo({
      emptyTableMsg: $t('task.common.search.fail'),
      predictTaskList: [],
      sharedIndicator,
      site,
      relatedTaskTableData: [],
      sharedIndicatorName,
      isPredictTaskShow: false,
    });
  };

  // 站点change回调
  const onChangeSite = (val) => {
    // 判断调度指标是否已选择，已选择的话则查询预测任务列表
    if (profileInfo.sharedIndicator) {
      queryPredictTaskList(profileInfo.sharedIndicator, profileInfo.sharedIndicatorName, val, true);
    } else {
      setProfileInfo({ site: val });
    }
  };

  // 查询关联预测任务列表信息
  const queryPredictTaskList = (sharedIndicator, sharedIndicatorName, site, isClick, scheduleConfig = [], profileId = '') => {
    setProfileInfo({ isPredictTaskShow: true });
    api.queryRelatedKpiList({
      portraitId: profileInfo.profileId || profileId,
      sharedIndicator,
      siteId: site,
    }, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        handleFailure(sharedIndicator, site, sharedIndicatorName);
        return;
      }

      let tableData = [];
      if (res.data) {
        relatedPredictTaskData.current = res.data.map(item => ({
          hostPortraitName: item.hostPortraitName,
          hostType: item.hostType,
          indicatorIdentity: item.indicatorIdentity,
          forecastTaskList: item.forecastTaskList,
          lowerThreshold: isClick ?
            '' : getScheduleConfigByKey(scheduleConfig, item.hostPortraitName, 'lowerThreshold'),
          upperThreshold: isClick ?
            '' : getScheduleConfigByKey(scheduleConfig, item.hostPortraitName, 'upperThreshold'),
          forecastTaskId: isClick ? '' :
            getForeCastTaskInfo(scheduleConfig, item.hostPortraitName, 'forecastTaskId', item.forecastTaskList),
          forecastTaskName: isClick ? '' :
            getForeCastTaskInfo(scheduleConfig, item.hostPortraitName, 'forecastTaskName', item.forecastTaskList),
        }));
        tableData = getTableRows(res.data);
      }
      setProfileInfo({
        emptyTableMsg: $t('kpi.task.list.no.data'),
        predictTaskList: tableData,
        sharedIndicator,
        sharedIndicatorName,
        site,
        relatedTaskTableData: relatedPredictTaskData.current,
        isPredictTaskShow: false,
      });
    }, err => {
      handleFailure(sharedIndicator, site, sharedIndicatorName);
    });
  };

  // 通过画像名称找到表格行指定信息
  const getScheduleConfigByKey = (data, name, key) => {
    let findItem = data.find(item => item.hostPortraitName === name);
    if (findItem) {
      return findItem[key];
    }
    return '';
  };

  // 回显时判断预测任务列表中是否有已存库id，没有置空
  const getForeCastTaskInfo = (data, name, key, forecastTaskList) => {
    let findItem = data.find(item => item.hostPortraitName === name);
    if (findItem && forecastTaskList.findIndex(ele => ele.taskId === findItem.forecastTaskId) !== -1) {
      return findItem[key];
    }
    return '';
  };

  // 获取关联预测任务下拉框列表信息
  const getRelatedTaskOptions = (data) => data.map(item => ({ text: item.taskName, value: item.taskId }));

  // 获取关联预测任务表格行
  const getTableRows = (data, isView = false) => {
    return data.map((item, index) => ({
      ...item,
      indicatorIdentity: item.indicatorIdentity,
      lowerThreshold: isView ? item.lowerThreshold : getThresholdTextField(index, 'lowerThreshold'),
      upperThreshold: isView ? item.upperThreshold : getThresholdTextField(index, 'upperThreshold'),
      task: isView ? item.forecastTaskName : getPredictTaskSelect(index, getRelatedTaskOptions(item.forecastTaskList)),
    }));
  };

  // 阈值上下限表格列
  const getThresholdTextField = (index, key) => {
    let checkParam = ['validThreshold'];
    if (relatedPredictTaskData.current[index].forecastTaskId) {
      checkParam = ['required', 'validThreshold'];
    }
    return (
      <TextField placeholder={$t('kpi.task.common.please.enter')} id={`${key}_${index}`}
        inputStyle={{ width: '100%' }} hintType='tip' autoComplete="off"
        value={relatedPredictTaskData.current[index][key]}
        validator={(val, id) => validate(checkParam, val, id)}
        ref={ele => customRelatedTaskRef[key][index] = ele}
        onChange={(val) => changeThreshold(val, index, key)}
      />
    );
  };

  // 阈值上下限输入框change回调
  const changeThreshold = (val, index, key) => {
    relatedPredictTaskData.current[index][key] = val;
    setProfileInfo({
      relatedTaskTableData: relatedPredictTaskData.current,
      predictTaskList: getTableRows(relatedPredictTaskData.current),
    });
  };

  // 单击创建预测任务
  const jumpToPredictTask = () => {
    let origin = window.location.origin;
    let pathname = window.location.pathname;
    let win = window.open(`${origin}${pathname}#path=/aiOpsService&subMenu=trendPredictionManage`);
    if (win) {
      win.opener = null;
    }
  };

  const refreshTable = () => {
    const dataCheckArr = [profileSelect.current, sharedIndicatorSelect.current];
    for (let i = 0; i < dataCheckArr.length; i++) {
      if (dataCheckArr[i] && !dataCheckArr[i].validate()) {
        dataCheckArr[i].focus();
        return;
      }
    }
    queryPredictTaskList(profileInfo.sharedIndicator, profileInfo.sharedIndicatorName, profileInfo.site,
      false, profileInfo.relatedTaskTableData);
  };

  // 趋势预测任务下拉框表格自定义
  const getPredictTaskSelect = (index, options) => {
    if (!options.length) {
      return (
        <>
          <span title={$t('tidal.forecast.task.click.tip')}>{$t('tidal.forecast.task.click')}</span>
          <TextButton
            style={{ verticalAlign: 'unset' }}
            text={$t('tidal.forecast.task.click.here')}
            onClick={jumpToPredictTask}
            title={$t('tidal.forecast.task.click.tip')}
          />
          <span title={$t('tidal.forecast.task.click.tip')}>{$t('tidal.forecast.task.click.create')}</span>
        </>
      );
    }

    return (
      <Select options={[{ text: $t('tidal.forecast.task.please.select'), value: '' }, ...options]}
        style={{ width: '100%' }}
        selectStyle={{ width: '100%', backgroundColor: '#FFF' }}
        className="predictTask"
        value={relatedPredictTaskData.current[index].forecastTaskId}
        hintType='tip'
        onChange={(value, oldValue, text) => changeForecastTask(value, text, index)}
      />
    );
  };

  // 关联预测任务下拉框回调
  const changeForecastTask = (val, text, index) => {
    relatedPredictTaskData.current[index].forecastTaskId = val;
    relatedPredictTaskData.current[index].forecastTaskName = text;
    setProfileInfo({
      relatedTaskTableData: relatedPredictTaskData.current,
      predictTaskList: getTableRows(relatedPredictTaskData.current),
    });
  };

  const setProfileInfo = (param, isRefresh = false) => {
    dispatch({
      type: 'setProfileInfo', profileInfo: { ...profileCache.current, ...param, isRefreshByProfile: isRefresh },
    });
    profileCache.current = { ...profileCache.current, ...param, isRefreshByProfile: isRefresh };
  };

  // 取消修改
  const cancelModify = () => {
    setTaskPageType(TASK_PAGE_TYPE.VIEW);
    setProfileInfo({ ...medianProfileInfo.current }, false);
  };

  // 修改按钮触发入口
  const switchModifyTask = () => {
    medianProfileInfo.current = { ...profileInfo };
    setTaskPageType(TASK_PAGE_TYPE.MODIFY);

    // 刷新页面
    setProfileInfo({}, true);
  };

  const verify = () => {
    const dataCheckArr = [profileSelect.current, sharedIndicatorSelect.current];
    for (let i = 0; i < dataCheckArr.length; i++) {
      if (dataCheckArr[i] && !dataCheckArr[i].validate()) {
        dataCheckArr[i].focus();
        return false;
      }
    }

    let lowThresholdLists = customRelatedTaskRef.lowerThreshold;
    let upperThresholdLists = customRelatedTaskRef.upperThreshold;
    let forecastTaskList = [];
    for (let i = 0; i < relatedPredictTaskData.current.length; i++) {
      if (relatedPredictTaskData.current[i].forecastTaskId) {
        let dataCheckArr = [upperThresholdLists[i], lowThresholdLists[i]];
        if (!pages.validateDataLists(dataCheckArr)) {
          return false;
        }
        forecastTaskList.push(relatedPredictTaskData.current[i].forecastTaskId);
      }

      if (parseFloat(relatedPredictTaskData.current[i].upperThreshold) <
        parseFloat(relatedPredictTaskData.current[i].lowerThreshold)) {
        lowThresholdLists[i].focus();
        modal.error($t('kpi.task.common.tip'), $t('task.tidal.threshold.tip'));
        return false;
      }
    }

    if (forecastTaskList.length < MIN_RELATED_FORECAST_TASK) {
      modal.error($t('kpi.task.common.tip'), $t('tidal.related.forecast.task.min.tip'));
      return false;
    }

    if (forecastTaskList.length > MAX_RELATED_FORECAST_TASK) {
      modal.error($t('kpi.task.common.tip'), $t('tidal.related.forecast.task.max.tip'));
      return false;
    }
    return true;
  };

  const validIsAssociated = (callBack) => {
    dispatch({ type: 'setIsLoadingShow', isLoadingShow: true });
    api.isPortraitAssociated({
      portraitId: profileInfo.profileId,
      portraitType: 'product',
    }, res => {
      dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        modal.error($t('kpi.task.common.error.tip'), $t('tidal.valid.error'));
        return;
      }

      if (!res.data.checkResult) {// checkResult false:重复 true:不重复
        modal.error($t('kpi.task.common.error.tip'), $t('tidal.profile.associated.tip'));
        return;
      }
      callBack();
    }, err => {
      dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
    });
  };

  // 下一步
  const goNextStep = (step) => {
    if (!verify()) {
      return;
    }

    validIsAssociated(() => {
      dispatch({ type: 'setCurrentTab', currentTab: step });
    });
  };

  const submitModify = () => {
    if (!verify()) {
      return;
    }

    // 未修改服务画像不需要调用校验接口判断是否重复
    if (profileInfo.profileId === taskList.associateProductPortraitId) {
      modifyProfile();
    } else {
      validIsAssociated(() => {
        modifyProfile();
      });
    }
  };

  // 提交修改
  const modifyProfile = () => {
    let param = {
      ...taskList,
      associateProductPortraitId: profileInfo.profileId,
      associateProductPortraitName: profileInfo.profileName,
      scheduleDependentIndicator: JSON.stringify({
        displayValue: profileInfo.sharedIndicatorName,
        returnValue: profileInfo.sharedIndicator,
      }),
      siteId: profileInfo.site,
      scheduleConfig: profileInfo.relatedTaskTableData.map(item => ({
        hostPortraitName: item.hostPortraitName,
        hostType: item.hostType,
        indicatorIdentity: item.indicatorIdentity,
        upperThreshold: validEmpty(item.upperThreshold) ? '' : parseFloat(item.upperThreshold),
        lowerThreshold: validEmpty(item.lowerThreshold) ? '' : parseFloat(item.lowerThreshold),
        forecastTaskId: item.forecastTaskId,
        forecastTaskName: item.forecastTaskName === $t('tidal.forecast.task.please.select') ? '' : item.forecastTaskName,
      })),
    };

    dispatch({ type: 'setIsLoadingShow', isLoadingShow: true });
    modifyTaskCommon(param, () => {
      dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
    }, res => {
      setProfileInfo({}, true);
      dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
      setTaskList(param);
      setTaskPageType(TASK_PAGE_TYPE.VIEW);
    });
  };

  return (
    <>
      {taskPageType === TASK_PAGE_TYPE.VIEW ?
        <Row style={{ marginTop: '30px' }}>
          <Col cols={20}>
            <table className='task-view'>
              <tbody>
                <tr>
                  <td className='table_label'>
                    {$t('tidal.service.profile')}：
                  </td>
                  <td>
                    {profileInfo.profileName}
                  </td>
                </tr>
                <tr>
                  <td className='table_label'>{$t('tidal.shared.indicator')}：</td>
                  <td>
                    {profileInfo.sharedIndicatorName}
                  </td>
                </tr>
                <tr>
                  <td className='table_label'>{$t('tidal.site')}：</td>
                  <td>
                    {validEmpty(profileInfo.site) ? $t('tidal.no.site') : profileInfo.site}
                  </td>
                </tr>
                <tr>
                  <td className='table_label'>{$t('tidal.related.predict.task')}：</td>
                  <td>
                    <Table
                      columns={RELATED_PREDICT_TABLE_COLUMNS}
                      width='100%'
                      dataset={profileInfo.predictTaskList}
                      showEmptyImage={false}
                      enableMulitiExpand
                      emptyTableMsg={profileInfo.emptyTableMsg}
                    />
                  </td>
                </tr>
              </tbody>
            </table>
          </Col>
          <Col cols={2}>
            {(taskList.trainStatus === TRAIN_STATUS_MAP.TRAINING || (taskList.startStatus === TASK_STATUS.RUNNING) ||
              taskList.startStatus === TASK_STATUS.VERIFYING ||
              isCanModify === IS_CAN_MODIFY_TASK.NO || taskList.status === LAST_EXECUTE_STATUS.RUNNING) ?
              <Tooltip content={TASK_TYPE_MODIFY_TIP[taskList.taskType]}>
                <Button text={$t('kpi.task.common.modify')} status="primary" disabled style={{ float: 'right' }} />
              </Tooltip> :
              <Button text={$t('kpi.task.common.modify')} status="primary" style={{ float: 'right' }}
                onClick={switchModifyTask}
              />}
          </Col>
        </Row> :
        <table className='task-info' style={{ width: '100%', marginTop: '30px' }}>
          <tbody>
            <tr>
              <td>
                <LabelHint label={$t('tidal.service.profile')} require={true} hint="" noColon={false}
                  isHelp={false}
                />
              </td>
              <td>
                <Select options={profileInfo.profileOptions}
                  selectStyle={{ width: '530px' }} id='profile' required
                  value={profileInfo.profileId}
                  validator={(val, id) => validate(['required'], val, id)}
                  hintType='tip'
                  ref={field => profileSelect.current = field}
                  onChange={(value, oldValue, text) => querySharedIndicator(value, text)}
                />
              </td>
            </tr>
            <tr>
              <td>
                <LabelHint label={$t('tidal.shared.indicator')} require={true} hint="" noColon={false}
                  isHelp={false}
                />
              </td>
              <td>
                <Select options={profileInfo.sharedIndicatorList}
                  selectStyle={{ width: '530px' }} id='sharedIndicator' required
                  value={profileInfo.sharedIndicator}
                  validator={(val, id) => validate(['required'], val, id)}
                  hintType='tip'
                  ref={field => sharedIndicatorSelect.current = field}
                  onChange={onChangeSharedIndicator}
                />
              </td>
            </tr>
            <tr>
              <td>
                <LabelHint label={$t('tidal.site')} require={false} hint="" noColon={false} isHelp={false} />
              </td>
              <td>
                <Select options={[{ text: $t('tidal.no.site'), value: '' }, ...profileInfo.siteList]}
                  selectStyle={{ width: '530px' }} id='site'
                  value={profileInfo.site}
                  hintType='tip'
                  onChange={onChangeSite}
                />
              </td>
            </tr>
            <tr>
              <td>
                <LabelHint label={$t('tidal.related.predict.task')} require={true} hint="" noColon={false}
                  isHelp={false}
                />
              </td>
              <td>
                <div style={{ float: 'left', width: '90%' }}>
                  <Table
                    columns={RELATED_PREDICT_TABLE_COLUMNS}
                    width='100%'
                    dataset={profileInfo.predictTaskList}
                    showEmptyImage={false}
                    enableMulitiExpand
                    emptyTableMsg={profileInfo.emptyTableMsg}
                  />
                </div>
                <Button text={$t('tidal.refresh')}
                  style={{ float: 'left', marginLeft: '15px' }}
                  onClick={refreshTable}
                />
              </td>
            </tr>
            {(taskPageType === TASK_PAGE_TYPE.NEW) ?
              <tr>
                <td colSpan={2} style={{ paddingTop: '30px' }}>
                  <Button text={$t('kpi.task.common.cancel')} style={{ float: 'left', marginRight: '20px' }}
                    onClick={() => newOrUpdateTask('taskList', true, {}, 1)}
                  />
                  <Button text={$t('kpi.task.common.next.step')} style={{ marginRight: '20px' }} status="primary"
                    onClick={() => goNextStep(TIDAL_STEPS.CONFIGURE_ALGORITHM)}
                  />
                </td>
              </tr> :
              <tr>
                <td colSpan={2} style={{ paddingTop: '30px' }}>
                  <Button text={$t('kpi.task.common.cancel')} style={{ float: 'left', marginRight: '20px' }}
                    onClick={cancelModify}
                  />
                  <Button text={$t('kpi.task.common.confirm')} status="primary"
                    onClick={submitModify}
                  />
                </td>
              </tr>}
          </tbody>
        </table>}
      <Loader type="global" isOpen={profileInfo.isServiceProfileShow || profileInfo.isSiteShow || profileInfo.isIndicatorShow ||
        profileInfo.isPredictTaskShow}
      desc={$t('kpi.task.please.wait')} id="profileLoading"
      />
    </>
  );
};

NewOrEditProfile.propTypes = {
  taskList: PropTypes.object.isRequired,
  newOrUpdateTask: PropTypes.func.isRequired,
  setTaskList: PropTypes.func.isRequired,
  isCanModify: PropTypes.bool.isRequired,
  taskPageType: PropTypes.string.isRequired,
};

export default NewOrEditProfile;
