/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

/**
 * 实体知识管理
 */

import React, { useEffect, useState, useRef } from 'react';
import { Loader, DivMessage, Button } from 'eview-ui';
import G6 from '@antv/g6';
import { $t, registerI18nResource, pages, modal } from '@util';
import { RESULT_CODE } from '@const/common';
import MenuTab from '@comp/MenuTab';
import CustomDrawer from '@comp/CustomDrawer';
import NoRecordHintsPage from '@comp/NoRecordHints';
import RightPanel from './components/RightPanel';
import Toolbar from './components/Toolbar';
import ImportNEKnowledge from './components/ImportNEKnowledge';
import i18nResource from './locale';
import pageCss from './css/index.less';
import { deleteEntity, exportEntityKnowledge, getSolutions, queryKnowledgeData } from './api';
import {
  ELEMENT_TYPE, PAGE_TYPE, G6_ELEMENT_TYPE, KnowledgeContext, EDGE_COLOR, NODE_TYPE, ARROW_SIZE,
  ARROW_DISTANCE, SOURCE_SCENE
} from './const';
import { createEventNode, createNENode, createEdge } from './util/util';

function NEKnowledgeManagement() {
  // 注册国际化资源
  registerI18nResource(i18nResource, 'neKnowledgeManagement');
  // 更新浏览器页签
  pages.setTabTitle($t('knowledge.management.entity'));
  // 设置帮助文档
  pages.setHelpHedex('im.EntityKnowledge');

  const divRef = useRef(null);
  const graphInstanceRef = useRef(null);
  const tempEdgeArr = useRef([]);
  const selectedMenuRef = useRef('');

  const [isShowNoData, setIsShowNoData] = useState(false);
  const [refreshTimestamp, setRefreshTimestamp] = useState(0);
  const [pageTips, setPageTips] = useState({
    isShowTips: false,
    content: '',
    menuKey: '',
  });
  const [graphInstance, setGraphInstance] = useState(null);
  const [rightPanelInfo, setRightPanelInfo] = useState({
    isShowPanel: false,
    pageType: PAGE_TYPE.VIEW,
    panelType: ELEMENT_TYPE.NE_TYPE_NODE,
    panelTitle: '',
    extendData: {},
  });
  const [isShowLoader, setIsShowLoader] = useState(false);
  const [importDrawerShow, setImportDrawerShow] = useState(false);
  const [selectedMenu, setSelectedMenu] = useState('');
  const [solutionMenus, setSolutionMenu] = useState([]);

  useEffect(() => {
    const bodyElement = document.body;
    bodyElement.classList.add('app_knowledge');

    initSolutionList();

    window.addEventListener('resize', onResizeGraph);

    return () => {
      bodyElement.classList.remove('app_knowledge');
      window.removeEventListener('resize', onResizeGraph);
      graphInstanceRef.current?.destroy();
    };
  }, [refreshTimestamp]);

  const onResizeGraph = () => {
    if (graphInstanceRef.current && divRef.current) {
      const width = divRef.current?.clientWidth;
      const height = divRef.current?.clientHeight;
      graphInstanceRef.current?.changeSize(width, height);
    }
  };

  // 查询解决方案列表
  const initSolutionList = () => {
    setIsShowLoader(true);
    getSolutions(
      1,
      resp => {
        if (!resp || resp.resultCode !== RESULT_CODE.success) {
          handleGetSolutionError();
          return;
        }

        if (resp.data?.length > 0) {
          setIsShowNoData(false);
          const selectOptionArr = resp.data.map(item => ({ text: item, value: item }));
          setSolutionMenu(selectOptionArr);
          setSelectedMenu(resp.data[0]);
          selectedMenuRef.current = resp.data[0];
          initKnowledgeData(resp.data[0]);
        } else {
          setIsShowNoData(true);
          handleQuerySolutionErrorCommon();
        }
      },
      () => {
        handleGetSolutionError();
      }
    );
  };

  const handleQuerySolutionErrorCommon = () => {
    setIsShowLoader(false);
    setSolutionMenu([]);
    setSelectedMenu('');
    selectedMenuRef.current = '';
    setGraphInstance(null);
  };

  // 查询解决方案失败处理
  const handleGetSolutionError = () => {
    handleQuerySolutionErrorCommon();
    modal.error($t('common.error'), $t('entity.knowledge.query.solution.failed'));
  };

  // 查询知识关系图数据
  const initKnowledgeData = solution => {
    setIsShowLoader(true);
    graphInstanceRef.current?.destroy();
    queryKnowledgeData(
      { solutionName: solution },
      resp => {
        if (!resp || resp.resultCode !== RESULT_CODE.success) {
          handleQueryDataFailed();
          return;
        }
        if (resp?.data?.eventResources?.length > 0 || resp?.data?.neResources?.length > 0) {
          initGraph(resp.data);
        } else {
          setIsShowLoader(false);
          initGraph(resp.data);
        }
      },
      () => {
        handleQueryDataFailed();
      }
    );
  };

  // 查询知识关系数据失败处理
  const handleQueryDataFailed = () => {
    setIsShowLoader(false);
    modal.error($t('common.error'), $t('entity.knowledge.query.entity.failed'));
  };

  const deleteElement = (param, element) => {
    deleteEntity(
      param,
      resp => {
        if (resp?.resultCode === RESULT_CODE.success) {
          setIsShowLoader(false);
          if (resp.data) {
            setRefreshTimestamp(Date.now());
          } else {
            modal.success($t('common.success'), $t('entity.knowledge.delete.success'));
            graphInstanceRef.current?.removeItem(element);
          }
          closeRightPanel();
        } else {
          modal.error($t('common.error'), $t('entity.knowledge.delete.failed'));
        }
      },
      () => {
        setIsShowLoader(false);
        modal.error($t('common.error'), $t('entity.knowledge.delete.failed'));
      }
    );
  };

  // 切换解决方案
  const switchSolution = taskId => {
    setPageTips({ isShowTips: false, content: '', menuKey: '' });
    setSelectedMenu(taskId);
    selectedMenuRef.current = taskId;

    // 关闭右侧面板
    closeRightPanel();

    initKnowledgeData(taskId);
  };

  // 关闭导入抽屉
  const closeImportDrawer = () => {
    setImportDrawerShow(false);
    setTimeout(() => setRefreshTimestamp(Date.now()), 300);
  };

  // 初始化G6画布
  const initGraph = respData => {
    if (!divRef.current) {
      return;
    }

    if (!respData) {
      return;
    }

    const width = divRef.current?.clientWidth;
    const height = divRef.current?.clientHeight;

    const data = handleDataToGraph(respData);

    G6.Util.processParallelEdges(data.edges, 20);

    const graph = new G6.Graph({
      container: divRef.current,
      width,
      height,
      linkCenter: true,
      plugins: [contextMenu, tooltip],
      layout: {
        type: 'gForce',
        preventOverlap: true,
        gpuEnabled: true,
        nodeStrength: 5000,
        nodeSpacing: 500,
        minMovement: 10,
        maxIteration: 2000,
        maxSpeed: 2000,
      },
      modes: {
        default: [
          'drag-node',
          {
            type: 'zoom-canvas',
            enableOptimize: false,
          },
          {
            type: 'drag-canvas',
            enableOptimize: false,
          },
          'activate-relations',
          {
            type: 'click-select',
            multiple: false,
            selectEdge: true,
          },
        ],
        editNode: [],
        editLink: ['create-edge'],
        editLinking: [],
      },
      defaultNode: {
        size: 30,
        style: {
          lineWidth: 2,
          fill: '#C6E5FF',
          stroke: '#5B8FF9',
        },
        labelCfg: {
          formatter: label => {
            const maxLength = 6; // 最大长度限制
            return label.length > maxLength ? `${label.slice(0, maxLength)}...` : label;
          },
        },
      },
      defaultEdge: {
        style: {
          stroke: EDGE_COLOR.default_color,
          lineWidth: 1,
          endArrow: {
            path: G6.Arrow.triangle(ARROW_SIZE, ARROW_SIZE, ARROW_DISTANCE),
            d: ARROW_DISTANCE,
            fill: EDGE_COLOR.default_color,
          },
        },
      },
    });

    graph.data(data);
    graph.render();

    registerListenerEvent(graph);

    setGraphInstance(graph);
    graphInstanceRef.current = graph;
  };

  // 处理数据
  const handleDataToGraph = respData => {
    const { eventResources, neResources, relationResources } = respData;
    const neNodeArr = neResources.map(item => createNENode(item));
    const eventNodeArr = eventResources.map(item => createEventNode(item));

    const edgeArr = [];
    for (let item of relationResources) {
      if (item.sourceType === NODE_TYPE.ne && item.targetType === NODE_TYPE.ne) {
        edgeArr.push(createEdge(item));
      }
    }

    return {
      nodes: [...neNodeArr, ...eventNodeArr],
      edges: edgeArr,
    };
  };

  // 选中node时，展示与其相关的edge
  const showEdge = edgeArr => {
    if (!edgeArr) {
      setIsShowLoader(false);
      return;
    }

    const arr = [];
    for (let item of edgeArr) {
      if (item.source.nodeType === NODE_TYPE.ne && item.target.nodeType === NODE_TYPE.ne) {
        continue;
      }

      const tempEdge = createEdge(item);
      arr.push(tempEdge);
    }
    G6.Util.processParallelEdges(arr, 20);
    tempEdgeArr.current = arr;

    const result = arr.map(item => ({ type: 'edge', model: item }));
    graphInstanceRef.current.addItems(result);

    setIsShowLoader(false);
  };

  // 取消选中node时，删除与其相关的edge
  const deleteTempEdge = selectedElement => {
    if (tempEdgeArr.current?.length === 0) {
      return;
    }

    // 如果选中的是临时展示的边，则不删除临时边
    for (let tempEdge of tempEdgeArr.current) {
      if (tempEdge.id === selectedElement?.getModel()?.id) {
        return;
      }
    }

    for (let tempEdge of tempEdgeArr.current) {
      graphInstanceRef.current.removeItem(tempEdge.id);
    }
    tempEdgeArr.current = [];
  };

  // 注册G6监听事件
  const registerListenerEvent = graph => {
    // 元素选中状态变更事件
    graph.on('nodeselectchange', event => {
      if (event.select) {
        const element = event.target;
        deleteTempEdge(element);
        if (element.getType() === 'node') {
          handleNodeClick(element);
        } else {
          handleEdgeClick(element);
        }
      } else {
        deleteTempEdge();
        closeRightPanel();
      }
    });

    // 监听事件来动态高亮edge
    graph.on('edge:mouseenter', evt => {
      const edge = evt.item; // 获取鼠标悬停的边
      graph.setItemState(edge, 'hover', true); // 设置 hover 状态
    });
    graph.on('edge:mouseleave', evt => {
      const edge = evt.item;
      graph.setItemState(edge, 'hover', false); // 取消 hover 状态
    });

    // 连线创建结束事件
    graph.on('aftercreateedge', param => {
      if (param.edge.destroyed) {
        return;
      }

      // 校验连线有效性
      const validateResult = validateEdge(param.edge);
      if (!validateResult.flag) {
        modal.error(
          $t('common.error'),
          validateResult.errMsg,
          () => {
            graphInstanceRef.current.removeItem(param.edge);
          },
          () => {
            graphInstanceRef.current.removeItem(param.edge);
          }
        );
        graphInstanceRef.current.setMode('default');
        setPageTips({ isShowTips: false, content: '', menuKey: '' });
        return;
      }

      rerenderEdgeAfterCreateEdge(param.edge);

      setRightPanelInfo({
        isShowPanel: true,
        pageType: PAGE_TYPE.CREATE,
        panelType: ELEMENT_TYPE.EDGE,
        panelTitle: $t('entity.knowledge.toolbar.create.edge'),
        extendData: param.edge,
      });

      // 切换模式，防止多次连线
      graphInstanceRef.current.setMode('editLinking');
    });

    // 布局结束事件
    graph.on('afterlayout', () => {
      setIsShowLoader(false);
      graph.fitCenter();
    });
  };

  // 校验创建连线有效性
  const validateEdge = edge => {
    const result = {
      flag: true,
      errMsg: '',
    };

    const sourceNode = edge.getSource();
    const targetNode = edge.getTarget();

    const sourceNodeType = sourceNode.getModel()?.eventType;
    const targetNodeType = targetNode.getModel()?.eventType;

    if (!(sourceNodeType && targetNodeType)) {
      result.flag = false;
      result.errMsg = $t('entity.knowledge.toolbar.create.edge.error');
      return result;
    }

    if (sourceNode.getModel().id === targetNode.getModel().id) {
      result.flag = false;
      result.errMsg = $t('entity.knowledge.toolbar.create.edge.same.node');
      return result;
    }

    // 错误码节点不允许连线
    if (sourceNodeType === NODE_TYPE.errorCode || targetNodeType === NODE_TYPE.errorCode) {
      result.flag = false;
      result.errMsg = $t('entity.knowledge.toolbar.create.edge.errorCode');
      return result;
    }

    // 告警与告警之间不允许连线
    if (sourceNodeType === NODE_TYPE.alarm && targetNodeType === NODE_TYPE.alarm) {
      result.flag = false;
      result.errMsg = $t('entity.knowledge.toolbar.create.edge.alarm');
      return result;
    }

    // 网元->告警不允许连线
    if (sourceNodeType === NODE_TYPE.ne && targetNodeType === NODE_TYPE.alarm) {
      result.flag = false;
      result.errMsg = $t('entity.knowledge.toolbar.create.edge.moTyeToAlarm');
      return result;
    }

    return result;
  };

  // 生成edge之后重新渲染edge保证弧度正常
  const rerenderEdgeAfterCreateEdge = createdEdge => {
    const sourceId = createdEdge.getModel().source;
    const targetId = createdEdge.getModel().target;

    const oldEdgeArr = graphInstanceRef.current.findAll('edge', edge => {
      return (
        ((edge.getModel().source === sourceId && edge.getModel().target === targetId) ||
          (edge.getModel().source === targetId && edge.getModel().target === sourceId)) &&
        edge.getModel().id !== createdEdge.getModel().id
      );
    });
    oldEdgeArr.push(createdEdge);

    const modelParam = [];
    for (let edge of oldEdgeArr) {
      modelParam.push({
        id: edge.getModel().id,
        sourceId: edge.getModel().source,
        targetId: edge.getModel().target,
      });
    }

    const edgeNewModel = [];
    for (let param of modelParam) {
      edgeNewModel.push(createEdge(param));
    }
    G6.Util.processParallelEdges(edgeNewModel, 20);

    for (let i = 0; i < oldEdgeArr.length; i++) {
      graphInstanceRef.current.updateItem(oldEdgeArr[i], edgeNewModel[i]);
    }
  };

  // 右键菜单--编辑
  const handleEdit = item => {
    const type = item.getType();
    if (type === G6_ELEMENT_TYPE.NODE) {
      graphInstanceRef.current.setMode('editNode');
      const nodeModel = item.getModel();
      setRightPanelInfo({
        isShowPanel: true,
        pageType: PAGE_TYPE.MODIFY,
        panelType: nodeModel.nodeType,
        panelTitle: `${$t('common.edit')}-${nodeModel.totalLabel}`,
        extendData: {
          ...nodeModel,
        },
      });
    } else {
      graphInstanceRef.current.setMode('editLinking');
      const edgeModel = item.getModel();
      setRightPanelInfo({
        isShowPanel: true,
        pageType: PAGE_TYPE.MODIFY,
        panelType: ELEMENT_TYPE.EDGE,
        panelTitle: $t('entity.knowledge.edit.edge'),
        extendData: {
          ...edgeModel,
        },
      });
    }
  };

  // 右键菜单--删除
  const handleDelete = item => {
    let confirmContent = `${$t('entity.knowledge.entity')}${item.getModel().totalLabel}`;
    const type = item.getType();
    if (type === G6_ELEMENT_TYPE.EDGE) {
      confirmContent = $t('entity.knowledge.edge');
    }
    modal.confirm($t('common.confirm'), `${$t('entity.knowledge.delete.confirm')}${confirmContent}`, () => {
      setIsShowLoader(true);
      const param = {
        type: '',
        id: [item.getModel().id],
        solutionName: selectedMenuRef.current,
      };
      if (type === G6_ELEMENT_TYPE.EDGE) {
        param.type = ELEMENT_TYPE.EDGE;
      } else {
        param.type = item.getModel().nodeType;
      }
      deleteElement(param, item);
    });
  };

  const contextMenu = new G6.Menu({
    className: 'contextMenuContainer',
    getContent(evt) {
      const source = evt.item?.getModel()?.sourceScene;
      return `
        <ul class="${source === SOURCE_SCENE.preset ? 'context_menu_disable' : 'context_menu_enable'}">
          <li id="edit">${$t('common.edit')}</li>
          <li id="delete">${$t('common.delete')}</li>
        </ul>`;
    },
    handleMenuClick: (target, item) => {
      let menuKey = target.getAttribute('id');
      if (menuKey === 'edit') {
        handleEdit(item);
      } else if (menuKey === 'delete') {
        handleDelete(item);
      }
    },
    shouldBegin() {
      return graphInstanceRef.current.getCurrentMode() === 'default';
    },
    offsetX: 20,
    offsetY: 20,
    itemTypes: ['node', 'edge'],
  });

  const tooltip = new G6.Tooltip({
    className: 'g6_tooltip_container',
    getContent(e) {
      const outDiv = document.createElement('div');
      let nameTitle = $t('entity.knowledge.moType.name');
      if (e.item.getModel().nodeType === ELEMENT_TYPE.EVENT_NODE) {
        nameTitle = $t('entity.knowledge.event.name');
      }
      outDiv.innerHTML = `
        <ul>
          <li>${nameTitle}${e.item.getModel().totalLabel}</li>
        </ul>`;
      return outDiv;
    },
    offsetX: 20,
    offsetY: 20,
    itemTypes: ['node'],
  });

  // node单击事件
  const handleNodeClick = item => {
    const nodeModel = item.getModel();
    setRightPanelInfo({
      isShowPanel: true,
      pageType: PAGE_TYPE.VIEW,
      panelType: nodeModel.nodeType,
      panelTitle: nodeModel.totalLabel,
      extendData: {
        ...nodeModel,
      },
    });
  };

  // edge单击事件
  const handleEdgeClick = item => {
    const edgeModel = item.getModel();
    setRightPanelInfo({
      isShowPanel: true,
      pageType: PAGE_TYPE.VIEW,
      panelType: ELEMENT_TYPE.EDGE,
      panelTitle: $t('entity.knowledge.view.edge'),
      extendData: {
        ...edgeModel,
      },
    });
  };

  // 关闭右侧面板并重置mode
  const closeRightPanelAndResetMode = () => {
    setRightPanelInfo({ ...rightPanelInfo, isShowPanel: false });
    graphInstanceRef.current.setMode('default');
  };

  // 关闭右侧面板
  const closeRightPanel = () => {
    setRightPanelInfo({ ...rightPanelInfo, isShowPanel: false });
  };

  const exportAllEntityKnowledge = () => {
    setIsShowLoader(true);
    exportEntityKnowledge(
      () => {
        setIsShowLoader(false);
      },
      () => {
        setIsShowLoader(false);
        modal.error($t('common.error'), $t('entity.knowledge.export.failed'));
      }
    );
  };

  const searchNode = (type, id) => {
    const node = graphInstanceRef.current.find('node', item => {
      return item.getModel().eventType === type && item.getModel().searchId === id;
    });
    if (!node) {
      modal.info($t('common.tip'), $t('entity.knowledge.toolbar.search.noData'));
      return;
    }

    // 聚焦node，选中node，打开详细信息面板
    graphInstanceRef.current.focusItem(node);
    graphInstanceRef.current.setItemState(node, 'selected', true);
    setRightPanelInfo({
      isShowPanel: true,
      pageType: PAGE_TYPE.VIEW,
      panelType: node.getModel().nodeType,
      panelTitle: node.getModel().totalLabel,
      extendData: {
        ...node.getModel(),
      },
    });
  };

  const setMenus = menuList => {
    setSolutionMenu(menuList);
  };

  return (
    <div style={{ position: 'relative' }}>
      <div className={pageCss.title_container}>
        <div style={{ height: '2rem', overflow: 'hidden' }}>
          <MenuTab
            menus={solutionMenus}
            taskId={selectedMenu}
            setMenus={setMenus}
            switchTaskCallBack={switchSolution}
            setDefaultDisplay={null}
          />
        </div>
        <div />
        <Button text={$t('common.import')} onClick={() => setImportDrawerShow(true)} status="primary" />
        <Button text={$t('common.export')} onClick={exportAllEntityKnowledge} status="primary" />
      </div>
      <div className="common-widget">
        <DivMessage
          type="default"
          className="pageTipsDiv"
          display={pageTips.isShowTips}
          text={pageTips.content}
          closable={false}
        />
        {graphInstance && (
          <Toolbar
            graphInstance={graphInstance}
            changePanelInfo={setRightPanelInfo}
            setPageTips={setPageTips}
            onSearchFnc={searchNode}
            closeRightPanel={closeRightPanel}
            selectedMenu={selectedMenu}
          />
        )}
        {isShowNoData && (
          <NoRecordHintsPage
            message={$t('entity.knowledge.noData')}
            isShowIcon={true}
            containerCls="pageNoDataHintCls"
            msgCls="pageNoDataHintFontCls"
          />
        )}
        {!isShowNoData && <div id="graphDiv" ref={divRef} style={{ width: '100%', height: 'calc(100vh - 15rem)' }} />}
        {rightPanelInfo.isShowPanel && (
          <KnowledgeContext.Provider value={{ solutionName: selectedMenu, setIsShowLoader }}>
            <RightPanel
              rightPanelInfo={rightPanelInfo}
              closePanel={closeRightPanelAndResetMode}
              graphInstance={graphInstance}
              setPageTips={setPageTips}
              showEdge={showEdge}
              tempEdgeArr={tempEdgeArr.current}
            />
          </KnowledgeContext.Provider>
        )}

        <CustomDrawer
          visible={importDrawerShow}
          zIndex={100000}
          placement="right"
          title={$t('entity.knowledge.import.title')}
          width="calc(90%)"
          customChildren={<ImportNEKnowledge />}
          bodyStyle={{ overflow: 'auto' }}
          headerStyle={{ fontSize: '20px' }}
          style={{ flexDirection: 'row-reverse', justifyContent: 'flex-start' }}
          drawerStyle={{ marginBottom: '5px', minHeight: 'calc(100vh - 76px)', minWidth: '50rem' }}
          onClose={closeImportDrawer}
          footer={false}
        />
        <Loader type="global" isOpen={isShowLoader} desc={$t('common.please.wait')} />
      </div>
    </div>
  );
}

export default NEKnowledgeManagement;
