/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

/**
 * 关联分析结果--告警信息卡片
 */

import React, { useEffect, useState } from 'react';
import { $t, registerI18nResource, pages, util } from '@util';
import { Table, Dialog, TextButton, PageMessage } from 'eview-ui';
import { RESULT_CODE, ALARM_KEY_COLOR, ALARM_KEY_TEXT } from '@const/common';
import closeIcon from '@assets/close.png';
import CommonCard from './CommonCard';
import constI18nResource from '../locales/associationAnalysis';
import * as echarts from 'echarts';
import { queryAlarmList, queryAlarmNumberChart } from '../api/associationAnalysis';
import { ALARM_TABLE_COL, ALARM_WEBSITE_URL, CSN_URL_PARAMS } from '../const/associationAnalysis';
import { getTheme } from '../utils/util';

const AlarmCard = props => {
  // 注册国际化资源
  registerI18nResource(constI18nResource, 'alarmCardI18n');

  const [isShowChart, setIsShowChart] = useState(false);
  const { isRootCard, changeRootCardExpand, logo, relevance, alarmId, alarmName, alarmSourceType, deviceTypeId,
    executionId, nodeId, beginTime, endTime, isShowMaxHints } = props;

  useEffect(() => {
    changeShowChart(false);
  }, [alarmId, relevance]);

  const changeShowChart = flag => {
    if (isRootCard) {
      changeRootCardExpand(flag);
    }
    setIsShowChart(flag);
  };

  // 点击告警名称，显示告警数量趋势图
  const onClickAlarmName = () => {
    changeShowChart(!isShowChart);
  };

  const getCommonCardProps = () => {
    return {
      logo,
      title: alarmName || 'NULL',
      relevance,
      onClickTitle: onClickAlarmName,
      itemArr: [
        {
          text: $t('alarm.associationAnalysis.card.content.alarmId'),
          value: alarmId,
        },
        {
          text: $t('alarm.associationAnalysis.card.content.alarmSourceType'),
          value: alarmSourceType,
        },
      ],
    };
  };

  return (
    <>
      <CommonCard {...getCommonCardProps()} />
      {isShowChart && (
        <AlarmChart
          alarmId={alarmId}
          executionId={executionId}
          nodeId={nodeId}
          deviceTypeId={deviceTypeId}
          closeChart={() => changeShowChart(false)}
          beginTime={beginTime}
          endTime={endTime}
          isShowMaxHints={isShowMaxHints}
        />
      )}
    </>
  );
};

const AlarmChart = props => {
  const { alarmId, executionId, nodeId, deviceTypeId, closeChart, beginTime, endTime, isShowMaxHints } = props;

  let chartDom;
  let chartObj;
  let elementId = `${alarmId}_${new Date().getTime()}`;
  const [alarmInfoDialog, setAlarmInfoDialog] = useState({ isShow: false, selectedTime: null });

  useEffect(() => {
    initAlarmChart();
  }, []);

  // 告警数量曲线点击事件
  const onClickAlarmChart = param => {
    if (param.componentType === 'series') {
      setAlarmInfoDialog({ isShow: true, selectedTime: param.value[0] });
    }
  };

  const initAlarmChart = () => {
    chartDom = document.getElementById(elementId);
    chartObj = echarts.init(chartDom);
    pages.getEmptyChart(chartObj, '', $t('indicator.detail.associationAnalysis.loading'));
    let param = {
      executionId,
      nodeId,
      alarmId,
      curveType: 'DV_ALARM',
      deviceTypeId,
    };
    queryAlarmNumberChart(
      param,
      resp => {
        chartObj.clear();
        if (resp && resp.data && resp.data.length > 0) {
          let options = getChartOption(resp.data);
          chartObj.setOption(options);
          chartObj.on('click', onClickAlarmChart);
        } else {
          pages.getEmptyChart(chartObj, '', $t('indicator.detail.associationAnalysis.noData'));
        }
      },
      () => {
        chartObj.clear();
        pages.getEmptyChart(chartObj, '', $t('indicator.detail.associationAnalysis.noData'));
      },
    );
  };

  const handleData = list => {
    return list.map(item => {
      return { value: [parseInt(item.collectTime), item.kpiValue] };
    });
  };

  const getChartOption = data => {
    const theme = getTheme();
    return {
      title: {
        text: $t('alarm.associationAnalysis.chart.title'),
        left: 'center',
        top: 8,
        align: 'right',
        textStyle: {
          color: theme.echart.titleColor,
        },
      },
      grid: {
        left: '2%',
        right: 25,
        bottom: 45,
        containLabel: true,
      },
      tooltip: {
        trigger: 'axis',
        textStyle: {
          color: theme.echart.tooltipColor,
        },
        borderColor: theme.echart.tooltipBorderColor,
        backgroundColor: theme.echart.tooltipBackgroundColor,
        axisPointer: {
          type: 'line',
          animation: true,
          axis: 'x',
          lineStyle: {
            color: theme.echart.tooltipLineColor,
          },
          label: {
            backgroundColor: theme.echart.tooltipLabelBackgroundColor,
            formatter: params => {
              if (params.axisDimension === 'x') {
                return util.dateTimeFormat(params.value).replace(' ', '\n');
              } else {
                return params.value;
              }
            },
          },
        },
      },
      toolbox: {
        top: 10,
        right: 18,
        itemGap: 20,
        showTitle: false,
        feature: {
          myToolCloseChart: {
            show: true,
            title: $t('indicator.detail.associationAnalysis.cancel'),
            icon: `image://${closeIcon}`,
            onclick: () => {
              closeChart();
            },
          },
        },
      },
      dataZoom: [
        {
          show: true,
          realtime: true,
          start: 0,
          end: 100,
          height: 20,
          showDetail: false,
          labelFormatter: value => {
            return util.dateTimeFormat(value).replace(' ', '\n');
          },
          minValueSpan: 1000 * 60 * 10,
        },
        {
          show: true,
          realtime: true,
          start: 0,
          end: 100,
          right: 0,
          yAxisIndex: 0,
          width: 20,
          showDetail: false,
        },
        {
          type: 'inside',
          realtime: true,
          start: 0,
          end: 100,
          zoomOnMouseWheel: 'ctrl',
        },
      ],
      xAxis: [
        {
          type: 'time',
          boundaryGap: false,
          min: parseInt(beginTime),
          max: parseInt(endTime),
          axisLabel: {
            margin: 8,
            hideOverlap: true,
            formatter: value => {
              return util.dateTimeFormat(value).replace(' ', '\n');
            },
            color: theme.echart.XLabelColor,
          },
          axisTick:{
            lineStyle: {
              color: theme.echart.XLineColor,
            },
          },
          axisLine:{
            lineStyle: {
              color: theme.echart.XLineColor,
            },
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: [
        {
          name: $t('alarm.associationAnalysis.chart.unit'),
          type: 'value',
          axisLine: {
            show: true,
          },
          axisTick: {
            show: true,
            lineStyle: {
              color: theme.echart.YTickColor,
            },
          },
          nameTextStyle:{
            color: theme.echart.YTextColor,
          },
          splitLine: {
            lineStyle: {
              color: theme.echart.YLineColor,
            },
          },
          axisLabel: {
            color: theme.echart.YLabelColor,
          },
        },
      ],
      series: [
        {
          name: $t('alarm.associationAnalysis.chart.seriesName'),
          type: 'line',
          lineStyle: {
            width: 1.5,
          },
          emphasis: {
            focus: 'none',
            lineStyle: {
              width: 1.5,
            },
          },
          showSymbol: false,
          showSize: 2,
          data: handleData(data),
        },
      ],
    };
  };

  return (
    <>
      <div style={{ paddingTop: '15px', background: getTheme().echart.backgroundColor }}>
        {isShowMaxHints && (
          <div style={{ padding: '0 8px' }}>
            <PageMessage text={$t('alarm.associationAnalysis.numberMax.info')} type="info" style={{ width: '100%' }} />
          </div>
        )}
        <div id={elementId} style={{ height: '20rem' }} />
      </div>
      {alarmInfoDialog.isShow && (
        <AlarmList
          closeDialog={() => {
            setAlarmInfoDialog({ ...alarmInfoDialog, isShow: false });
          }}
          selectedTime={alarmInfoDialog.selectedTime}
          alarmId={alarmId}
          executionId={executionId}
          nodeId={nodeId}
          deviceTypeId={deviceTypeId}
        />
      )}
    </>
  );
};

const AlarmList = props => {
  const { closeDialog, selectedTime, alarmId, executionId, nodeId, deviceTypeId } = props;
  const [allAlarmList, setAllAlarmList] = useState([]);
  const [tableData, setTableData] = useState({
    recordCount: 0,
    rowsData: [],
    emptyTableMsg: $t('alarm.associationAnalysis.csnTable.query.loading'),
  });
  const [pageData, setPageData] = useState({ pageSize: 10, currentPage: 1 });

  useEffect(() => {
    getAlarmList();
  }, []);

  useEffect(() => {
    handleTableDataPage(allAlarmList);
  }, [pageData.pageSize, pageData.currentPage]);

  // 分页点击回调
  const handlePaging = pageNum => {
    setPageData({ ...pageData, currentPage: pageNum });
  };

  // 分页大小点击回调
  const handlePageSize = size => {
    setPageData({ pageSize: size, currentPage: 1 });
  };

  const jumpAlarmLogPage = (csn, occurTime) => {
    let startTime = parseInt(occurTime) - 60 * 1000;
    let endTime = parseInt(occurTime) + 60 * 1000;
    let csnUrl = encodeURIComponent(CSN_URL_PARAMS).replace('replace', `%22${csn}%22`);
    let win = window.open(`${ALARM_WEBSITE_URL}${csnUrl}&latestEventTime=${startTime}~${endTime}`);
    if (win) {
      win.opener = null;
    }
  };

  const getAlarmList = () => {
    let param = {
      alarmId,
      deviceTypeId,
      executionId,
      nodeId,
      startTime: selectedTime,
      endTime: selectedTime + 60 * 1000,
    };
    queryAlarmList(
      param,
      resp => {
        if (resp && resp.resultCode === RESULT_CODE.success && resp.data && resp.data.length > 0) {
          let dataList = resp.data;
          let dataArr = dataList.map(item => {
            return {
              ...item,
              severity: <AlarmColorBlock level={item.severity} />,
              occurUtc: util.dateTimeFormat(item.occurUtc),
              clearUtc: item.clearUtc === '0' ? '' : util.dateTimeFormat(item.clearUtc),
              operation: (
                <TextButton
                  text={$t('alarm.associationAnalysis.csnTable.button.showAlarm')}
                  onClick={() => {
                    jumpAlarmLogPage(item.csn, item.occurUtc);
                  }}
                />
              ),
            };
          });
          setAllAlarmList(dataArr);
          handleTableDataPage(dataArr);
        } else {
          handleNoData($t('indicator.detail.associationAnalysis.noRecord'));
        }
      },
      () => {
        handleNoData($t('alarm.associationAnalysis.csnTable.query.failed'));
      },
    );
  };

  const handleNoData = errMsg => {
    setAllAlarmList([]);
    setTableData({ rowsData: [], recordCount: 0, emptyTableMsg: errMsg });
    setPageData({ pageSize: 10, currentPage: 1 });
  };

  // 前台处理分页
  const handleTableDataPage = allData => {
    if (allData && allData.length > 0) {
      let startIndex = pageData.pageSize * (pageData.currentPage - 1);
      let data = allData.slice(startIndex, startIndex + pageData.pageSize);
      setTableData({ ...tableData, rowsData: data, recordCount: allData.length });
    }
  };

  return (
    <Dialog
      title={$t('alarm.associationAnalysis.csnTable.title')}
      isOpen={true}
      onClose={closeDialog}
      closeOnEscape={false}
      modal={true}
      style={{
        maxHeight: '700px',
        minHeight: '670px',
        maxWidth: '1400px',
        minWidth: '1400px',
        left: '20%',
      }}
    >
      <Table
        id="alarmTable"
        columns={ALARM_TABLE_COL}
        dataset={tableData.rowsData}
        enablePagination
        keyIndex={0}
        headerCheckBoxSortAllow={false}
        pageSize={pageData.pageSize}
        currentPage={pageData.currentPage}
        recordCount={tableData.recordCount}
        onPageChange={handlePaging}
        onPageSizeChange={handlePageSize}
        emptyTableMsg={tableData.emptyTableMsg}
      />
    </Dialog>
  );
};

// 告警颜色块
export const AlarmColorBlock = props => {
  const { level } = props;
  // 告警颜色
  const color = ALARM_KEY_COLOR[level];

  return (
    <div>
      <span style={{ backgroundColor: color }} className="alarmLevelBlockColor">
        {ALARM_KEY_TEXT[level]}
      </span>
    </div>
  );
};

export default AlarmCard;
