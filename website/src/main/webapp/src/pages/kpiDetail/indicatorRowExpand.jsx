/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

import React, { useEffect, useMemo, useReducer, useRef, useState } from 'react';
import * as echarts from 'echarts';
import { Table, ButtonMenu, Dialog, Loader, Drawer, Tooltip, Icon } from 'eview-ui';
import { $t, registerI18nResource, util, modal, pages } from '@util';
import { changeCustomTimeStampToTime } from '@pages/kpiDetection/util/index';
import { RESULT_CODE, TASK_TYPE_MAP } from '@const/common';
import { KPI_COMMON_CONST } from '../kpiDetection/const/common';
import i18nResource from './locales';
import pageCss from './css/index.css';
import { queryExceptionsOfKPI, markResult, queryPerformanceData } from './api';
import { generateAnalysisReport, queryAssociationTask } from './api/associationAnalysis';
import CONSTANT from './const';
import { FLOW_ORCHESTRATION_URL } from './const/associationAnalysis';
import { KPIExceptionReducer, initialState } from './reducer/RowExpand';
import AssociationAnalysisResultPage from './components/AssociationAnalysisDrawer';
import AssociationAnalysisTask from './components/AssociationAnalysisTask';
import SelectIndex from './components/createAnalysisTask/SelectIndex';
import TransferLearningDialog from './components/TransferLearningDialog';
import { createExceptionVisualMap, AlarmLevelBlock, OperationCol } from './CommonComponents';
import EmailDetails from './components/EmailDetails';
import markRight from '@assets/markRight.svg';
import markError from '@assets/markError.svg';
import markUncertain from '@assets/markUncertain.svg';
import groupIndicator from '@assets/faultDetectionAnalysis/groupIndicator.png';
import refreshImg from '@assets/restore.png';
import analysisResult from '@assets/analysisResult.png';
import transferLearningIcon from '@assets/correlationAnalysis/transfer-learning.png';

const RowExpandPages = props => {
  // 注册国际化资源
  registerI18nResource(i18nResource, 'kpiDetail');

  const { id, name, timeRange, timePicker, scene, taskId, groupId, setGroupCompareViewParam, transferSwitch,
    changeAutoRefresh, updateRefreshTime } = props;
  // ref定义
  const table = useRef(null);
  const optionsExceptionData = useRef({});
  const associationAnalysisTask = useRef([]);

  // state定义
  const [checkedIdArr, setCheckedIdArr] = useState([]);
  const [performanceDataCache, setPerformanceDataCache] = useState([]);
  const [isShowExceptionTable, setIsShowExceptionTable] = useState(false);
  const [showSelectTaskTypeDialog, setShowSelectTaskTypeDialog] = useState(false);
  const [showAnalysisTaskDialog, setShowAnalysisTaskDialog] = useState(false);
  const [isShowDrawer, setIsShowDrawer] = useState(false);
  const [selectAnalysisTime, setSelectAnalysisTime] = useState('');
  const chartEleId = `chart_${id}_${taskId}`;
  const [isShowLoader, setIsShowLoader] = useState(false);
  const [kpiRefreshTime, setKpiRefreshTime] = useState(false);
  const [isShowEmailDialog, setIsShowEmailDialog] = useState(false);
  const [isShowEmailButton, setIsShowEmailButton] = useState(false);
  const [isShowTransferLearningDialog, setIsShowTransferLearningDialog] = useState(false);
  // reducer定义
  const [state, dispatch] = useReducer(KPIExceptionReducer, initialState);
  const {
    pageSize,
    currentPage,
    recordCount,
    exceptionsData,
    associationTaskTime,
    selectedAssociationTask,
    selectedAssociationTaskTime,
  } = state;

  // 性能图表对象定义
  let chartDom;
  let chartObj;

  // 批量标记菜单按钮初始项
  const buttonOptions = [
    { text: $t('markRight'), value: KPI_COMMON_CONST.markResult.correct, iconUrl: markRight },
    { text: $t('markError'), value: KPI_COMMON_CONST.markResult.incorrect, iconUrl: markError },
    { text: $t('markUncertain'), value: KPI_COMMON_CONST.markResult.doubted, iconUrl: markUncertain },
  ];

  useEffect(() => {
    // 主动销毁echarts，解决tooltip div残留问题
    return () => {
      if (chartObj) {
        chartObj.dispose();
      }
      window.removeEventListener('resize', resizeChart);
    };
  }, []);

  useEffect(() => {
    // 异常表格数据
    updateExceptionTable();
  }, [pageSize, currentPage]);

  useEffect(() => {
    queryExceptionData();
  }, [kpiRefreshTime]);

  // 批量标记点击事件
  const batchMarkClick = data => {
    if (checkedIdArr.length > 0) {
      markException(checkedIdArr, data.value);
      setCheckedIdArr([]);
    } else {
      modal.error($t('info'), $t('atLeastCheck'));
    }
  };

  // 获取查询异常表格的参数
  const getQueryExceptionParam = () => {
    return {
      id,
      situationIds: scene,
      timeRangeRadioId: timeRange,
      customTimes: changeCustomTimeStampToTime(timePicker),
      taskId,
      pageIndex: currentPage,
      pageSize,
    };
  };

  // 获取查询性能数据的参数
  const getQueryPerformanceDataParam = () => {
    return {
      id,
      taskId,
      timeRangeRadioId: timeRange,
      customTimes: changeCustomTimeStampToTime(timePicker),
    };
  };

  // 关闭标记结果弹窗回调
  const closeDialog = () => {
    dispatch({ type: 'hideMarkResultDialog' });

    // 标记完成后刷新异常表格
    updateExceptionTable();
  };

  // 标记异常检测结果
  const markException = (ids, marked) => {
    markResult({ ids, isCorrect: marked, indicatorId: id }, res => {
      if (res && res.resultCode === KPI_COMMON_CONST.responseCode.success) {
        if (res.data) {
          modal.success($t('success'), $t('markAndTrainSuccess'), closeDialog, closeDialog);
        } else {
          modal.success($t('success'), $t('markSuccess'), closeDialog, closeDialog);
        }
      } else {
        modal.error($t('error'), $t('markFailed'));
      }
    });
  };

  // 打开/关闭关联分析结果抽屉
  const switchAnalysisResultDrawer = flag => {
    if (flag) {
      $('body').addClass('hiddenScroll');
      // 关闭自动刷新
      changeAutoRefresh(false);
    } else {
      $('body').removeClass('hiddenScroll');
      // 打开自动刷新
      changeAutoRefresh(true);
      updateRefreshTime(Date.now());
    }
    setIsShowDrawer(flag);
    if (!flag) {
      refreshChartForAssociationTask();
      setIsShowEmailButton(false);
    }
  };

  const lineChartClick = param => {
    if (param.componentType === 'series') {
      setSelectAnalysisTime(param.value[0]);
      setShowSelectTaskTypeDialog(true);
    } else if (param.componentType === 'markPoint') {
      if (param.data.times > 1) {
        let times = param.data.executionTimes;
        let startTime = Math.min(...times);
        let endTime = Math.max(...times);
        dispatch({ type: 'setAssociationTaskTime', associationTaskTime: [startTime, endTime] });
        switchAnalysisTaskDialog(true);
      } else {
        let taskId = param.data.name;
        let executionTime = param.data.coord[0];
        jumpAnalysisResult(taskId, executionTime);
      }
    }
  };

  const jumpAnalysisResult = (executionId, executionTime) => {
    dispatch({
      type: 'setSelectedAssociationTask',
      selectedAssociationTask: executionId,
      selectedAssociationTaskTime: executionTime,
    });
    switchAnalysisResultDrawer(true);
  };

  // 查询关联分析任务列表
  const getAssociationTask = (performanceData, exceptionData, chartObj) => {
    let param = {
      taskId,
      indicatorId: id,
      timeRangeRadioId: timeRange,
      customTimes: changeCustomTimeStampToTime(timePicker),
    };
    queryAssociationTask(
      param,
      resp => {
        if (!resp || resp.resultCode !== RESULT_CODE.success) {
          associationAnalysisTask.current = [];
          return;
        }
        if (resp.data && resp.data.triggerExecutions && resp.data.triggerExecutions.length > 0) {
          associationAnalysisTask.current = resp.data.triggerExecutions;
        } else {
          associationAnalysisTask.current = [];
        }
        chartObj.setOption({ series: [{ id: 'observedValue', markPoint: getMarkPoints(performanceData) }] });
      },
      () => {
        associationAnalysisTask.current = [];
      },
    );
  };

  const resizeChart = () => {
    setTimeout(() => {
      chartObj.resize();
    }, 500);
  };

  // 加载性能数据图表
  const showPerformanceChart = exceptionData => {
    let chartSetting = getQueryPerformanceDataParam();
    pages.getEmptyChart(chartObj, '', $t('loading'));
    queryPerformanceData(
      chartSetting,
      resp => {
        chartObj.clear();
        if (
          resp &&
          resp.resultCode === KPI_COMMON_CONST.responseCode.success &&
          resp?.data?.performanceIndexValueList?.length > 0
        ) {
          let options = getOptions(resp.data, exceptionData);
          chartObj.setOption(options);
          if (!Prel.isMs) {
            chartObj.on('click', lineChartClick);
          }

          // 查询关联分析任务，异步绘制markPoint
          getAssociationTask(resp.data, exceptionData, chartObj);
        } else {
          pages.getEmptyChart(chartObj, '', $t('analysis.detail.log.common.noData'));
        }
        // 保存性能数据，图表联动刷新折线图时不需要再重新获取
        setPerformanceDataCache(resp.data);
      },
      error => {
        chartObj.clear();
        pages.getEmptyChart(chartObj, '', $t('analysis.detail.log.common.noData'));
      },
    );
    window.addEventListener('resize', resizeChart);
  };

  // 查询异常指标接口回调处理
  const getPMDataAndUpdateTable = respData => {
    if (respData && respData.indicatorOutlier.length > 0) {
      setIsShowExceptionTable(true);
      const tableData = respData.indicatorOutlier.map(data => {
        return [
          data.id,
          util.dateTimeFormat(data.startTime),
          util.dateTimeFormat(data.endTime),
          data.probableCause,
          <AlarmLevelBlock key={data.id} alarmLevel={data.alarmType} />,
          KPI_COMMON_CONST.markIdToDesc[data.correct],
          <OperationCol
            key={data.id}
            markHandler={markException}
            rowId={data.id}
            reportAlarmId={data.reportAlarmId}
            uuid={data.switchAlarmField}
            startTime={data.startTime}
            endTime={data.endTime}
            updateExceptionTable={updateExceptionTable}
            occurTime={data.startTime}
            isLog={false}
            analysisResultCallBack={showAnalysisResult}
          />,
        ];
      });
      dispatch({ type: 'updateTableData', exceptionsData: tableData, recordCount: respData.totalCount });
    } else {
      setIsShowExceptionTable(false);
      dispatch({ type: 'updateTableData', exceptionsData: [], recordCount: 0 });
    }
  };

  // 更新异常表格
  const updateExceptionTable = () => {
    let param = getQueryExceptionParam();
    // 查询指标的异常信息
    queryExceptionsOfKPI(
      param,
      res => {
        if (res && res.resultCode === KPI_COMMON_CONST.responseCode.success && res.data) {
          // 回调获取性能数据并更新表格
          getPMDataAndUpdateTable(res.data);
        } else {
          getPMDataAndUpdateTable();
        }
      },
      error => {
        getPMDataAndUpdateTable();
      },
    );
  };

  // 分页点击回调
  const handlePaging = pageNum => {
    dispatch({ type: 'updateCurrentPage', currentPage: pageNum });
  };

  // 分页大小点击回调
  const handlePageSize = size => {
    dispatch({ type: 'updatePageSize', pageSize: size });
  };

  // 查询所有异常并渲染图
  const queryExceptionData = () => {
    chartDom = document.getElementById(chartEleId);
    chartObj = echarts.init(chartDom);
    pages.getEmptyChart(chartObj, '', $t('loading'));
    let param = {
      id,
      situationIds: scene,
      timeRangeRadioId: timeRange,
      customTimes: changeCustomTimeStampToTime(timePicker),
      taskId,
    };
    queryExceptionsOfKPI(
      param,
      res => {
        if (res && res.resultCode === KPI_COMMON_CONST.responseCode.success && res.data) {
          optionsExceptionData.current = res.data.indicatorOutlier;
          showPerformanceChart(res.data.indicatorOutlier);
        } else {
          showPerformanceChart();
        }
      },
      () => {
        showPerformanceChart();
      },
    );
  };

  // 打开/关闭查看分析任务弹窗
  const switchAnalysisTaskDialog = flag => {
    setShowAnalysisTaskDialog(flag);
    if (!flag) {
      refreshChartForAssociationTask();
    }
  };

  // 刷新性能数据图中的关联分析记录点
  const refreshChartForAssociationTask = () => {
    chartDom = document.getElementById(chartEleId);
    chartObj = echarts.init(chartDom);
    getAssociationTask(performanceDataCache, optionsExceptionData.current, chartObj);
  };

  // 异常表格全选按钮点击事件
  const onTableHeaderCheck = checkedRows => {
    let tempArr = checkedIdArr;
    if (checkedRows.length > 0) {
      // 全选
      checkedRows.map(item => {
        if (checkedIdArr.indexOf(item) === -1) {
          tempArr.push(item);
        }
      });
    } else {
      // 取消全选
      let currentPageIds = exceptionsData.map(item => {
        return item[0];
      });
      tempArr = checkedIdArr.filter(item => {
        return currentPageIds.indexOf(item) === -1;
      });
    }
    setCheckedIdArr(tempArr);
  };

  // 获取options参数
  const getChartOptionsParam = (performanceRespData, exceptionData) => {
    const optionParam = {
      data: [],
      upper: [],
      lower: [],
      unit: '',
      startTime: '',
      endTime: '',
    };

    // 判空
    if (!performanceRespData) {
      return optionParam;
    }

    // 获取options参数
    optionParam.startTime = parseInt(performanceRespData.startTime);
    optionParam.endTime = parseInt(performanceRespData.endTime);
    optionParam.unit = performanceRespData.indexUnit;

    // 获取性能数据数组
    let performanceList = performanceRespData.performanceIndexValueList;
    if (performanceList) {
      if (exceptionData && exceptionData.length > 0) {
        for (let i = 0, len = performanceList.length; i < len; i++) {
          optionParam.data.push({
            value: [parseInt(performanceList[i].timestampStr), performanceList[i].indexValue],
          });
        }
      } else {
        for (let i = 0, len = performanceList.length; i < len; i++) {
          optionParam.data.push({
            value: [parseInt(performanceList[i].timestampStr), performanceList[i].indexValue],
            label: {},
            itemStyle: {
              color: CONSTANT.chartLineColor.normal,
            },
          });
        }
      }
    }

    // 获取阈值上限数据数组
    let upperThresholdDataList = performanceRespData.upperThresholdList;
    if (upperThresholdDataList) {
      for (let i = 0, len = upperThresholdDataList.length; i < len; i++) {
        optionParam.upper.push({
          value: [parseInt(upperThresholdDataList[i].timestampStr), upperThresholdDataList[i].upperThreshold],
          label: {},
          itemStyle: {
            color: CONSTANT.chartLineColor.upperLineColor,
          },
        });
      }
    }

    // 获取阈值下限数据数组
    let lowerThresholdDataList = performanceRespData.lowerThresholdList;
    if (lowerThresholdDataList) {
      for (let i = 0, len = lowerThresholdDataList.length; i < len; i++) {
        optionParam.lower.push({
          value: [parseInt(lowerThresholdDataList[i].timestampStr), lowerThresholdDataList[i].lowerThreshold],
          label: {},
          itemStyle: {
            color: CONSTANT.chartLineColor.lowerLineColor,
          },
        });
      }
    }

    return optionParam;
  };

  // 生成关联分析的记录点
  const getMarkPoints = performanceData => {
    if (!associationAnalysisTask.current.length || associationAnalysisTask.current.length === 0) {
      return {
        symbol: 'pin',
        symbolSize: 35,
        itemStyle: {
          color: '#ffa9a9',
        },
        data: [],
      };
    }

    // 计算冒泡坐标
    let allPoint = associationAnalysisTask.current.map(item => {
      let coordinate = binarySearchCoord(item.executionTime, performanceData.performanceIndexValueList);
      return {
        x: coordinate[0],
        y: coordinate[1],
        executionTime: item.executionTime,
        id: item.id,
      };
    });

    let coordObj = {};
    allPoint.map(item => {
      let value = coordObj[item.x];
      if (value) {
        value.times += 1;
        value.taskId.push(item.id);
        value.executionTime.push(item.executionTime);
      } else {
        coordObj[item.x] = { y: item.y, times: 1, id: item.id, taskId: [item.id], executionTime: [item.executionTime] };
      }
    });

    let dataArr = [];
    Object.keys(coordObj).forEach(key => {
      let value = coordObj[key];
      dataArr.push({
        name: value.id,
        taskIds: value.taskId,
        executionTimes: value.executionTime,
        value: '',
        times: value.times,
        coord: [parseInt(key), value.y],
      });
    });

    return {
      symbol: 'pin',
      symbolSize: 35,
      itemStyle: {
        color: '#ffa9a9',
      },
      data: dataArr,
    };
  };

  // 二分查找markPoint的Y坐标
  const binarySearchCoord = (xCoord, performanceArr) => {
    let length = performanceArr.length;
    let low = 0;
    let high = length - 1;
    let mid;
    let leftCoord;
    let leftValue;
    while (low <= high) {
      mid = Math.floor((low + high) / 2);
      let model = performanceArr[mid];
      if (model.timestampStr === xCoord) {
        return [xCoord, model.indexValue];
      } else if (model.timestampStr > xCoord) {
        high = mid - 1;
      } else {
        leftCoord = model.timestampStr;
        leftValue = model.indexValue;
        low = mid + 1;
      }
    }
    if (leftValue !== null) {
      return [leftCoord, leftValue];
    }
    return [xCoord, 0];
  };

  // 获取图表初始项
  const getOptions = (performanceData, exceptionData) => {
    const optionsParam = getChartOptionsParam(performanceData, exceptionData);
    const visualMap = createExceptionVisualMap(exceptionData, performanceData);
    const options = {
      grid: {
        left: '1%',
        right: 30,
        bottom: 50,
        containLabel: true,
      },
      toolbox: {
        top: 8,
        right: 20,
        itemGap: 20,
        showTitle: false,
        feature: {
          ...(Prel.isMs
            ? {}
            : {
              myToolShowAnalysisResult: {
                show: true,
                title: $t('indicator.detail.associationAnalysis.showAnalysisResult'),
                icon: `image://${analysisResult}`,
                onclick: () => {
                  dispatch({ type: 'setAssociationTaskTime', associationTaskTime: null });
                  switchAnalysisTaskDialog(true);
                },
              },
            }),
          myToolReset: {
            show: true,
            title: $t('restores'),
            icon: `image://${refreshImg}`,
            onclick: () => {
              chartObj.dispatchAction({ type: 'dataZoom', dataZoomIndex: [0, 1], start: 0, end: 100 });
            },
          },
        },
        tooltip: {
          show: true,
          formatter(param) {
            return `<div style='color: #212121;margin-top: 3px'>${param.title}</div>`;
          },
          backgroundColor: '#fff',
          textStyle: {
            fontSize: 12,
          },
        },
      },
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
        axisPointer: {
          type: 'line',
          animation: true,
          axis: 'x',
          label: {
            backgroundColor: '#505765',
            formatter: params => {
              if (params.axisDimension === 'x') {
                return util.dateTimeFormat(params.value).replace(' ', '\n');
              } else {
                return params.value;
              }
            },
          },
        },
      },
      dataZoom: [
        {
          show: true,
          realtime: true,
          start: 0,
          end: 100,
          labelFormatter: value => {
            if (value) {
              return util.dateTimeFormat(value).replace(' ', '\n');
            }
            return '';
          },
          minValueSpan: 1000 * 60 * 10,
          showDetail: false,
        },
        {
          show: true,
          realtime: true,
          start: 0,
          end: 100,
          right: 0,
          yAxisIndex: 0,
          width: 25,
          showDetail: false,
        },
        {
          type: 'inside',
          realtime: true,
          start: 0,
          end: 100,
          zoomOnMouseWheel: 'ctrl',
        },
      ],
      xAxis: [
        {
          type: 'time',
          boundaryGap: false,
          min: optionsParam.startTime,
          max: optionsParam.endTime,
          axisLabel: {
            margin: 8,
            interval: 16,
            hideOverlap: true,
            formatter: value => {
              return util.dateTimeFormat(value).replace(' ', '\n');
            },
          },
          splitLine: {
            show: false,
          },
        },
      ],
      yAxis: [
        {
          name: optionsParam.unit ? `${$t('unit')}: ${optionsParam.unit}` : '',
          type: 'value',
          axisLine: {
            show: true,
          },
          axisTick: {
            show: true,
          },
        },
      ],
      series: [
        {
          name: $t('upperThreshold'),
          type: 'line',
          lineStyle: {
            width: 1,
            type: 'dashed',
            dashOffset: 2,
            color: CONSTANT.chartLineColor.upperLineColor,
          },
          emphasis: {
            disabled: true,
          },
          showSymbol: false,
          showSize: 2,
          animation: false,
          yAxisIndex: 0,
          data: optionsParam.upper,
        },
        {
          id: 'observedValue',
          name: $t('indicator.details.chart.observedValue'),
          type: 'line',
          lineStyle: {
            width: 1.5,
          },
          emphasis: {
            focus: 'none',
            lineStyle: {
              width: 1.5,
            },
          },
          markPoint: [],
          showSymbol: false,
          showSize: 2,
          yAxisIndex: 0,
          data: optionsParam.data,
        },
        {
          name: $t('lowerThreshold'),
          type: 'line',
          lineStyle: {
            width: 1,
            type: 'dashed',
            color: CONSTANT.chartLineColor.lowerLineColor,
          },
          emphasis: {
            disabled: true,
          },
          showSymbol: false,
          showSize: 2,
          animation: false,
          yAxisIndex: 0,
          data: optionsParam.lower,
        },
      ],
    };

    if (groupId) {
      options.toolbox.feature.myToolGroupView = {
        show: true,
        title: $t('analysis.detail.compare.common.title'),
        icon: `image://${groupIndicator}`,
        onclick: () => {
          $('body').addClass('hiddenScroll');
          setGroupCompareViewParam({ showDrawer: true, groupId, taskId, indicatorId: id });
        },
      };
    }

    if (transferSwitch) {
      options.toolbox.feature.myToolTransferLearning = {
        show: true,
        title: $t('analysis.transferLearning.icon.title'),
        icon: `image://${transferLearningIcon}`,
        onclick: () => setIsShowTransferLearningDialog(true),
      };
    }

    if (visualMap) {
      options.visualMap = visualMap;
    } else {
      options.series[1].lineStyle.color = CONSTANT.chartLineColor.normal;
    }
    return options;
  };

  // 组装关联分析结果展示抽屉组件入参
  const getAssociationAnalysisParam = () => {
    return {
      indicatorId: id,
      selectedAssociationTask,
      executionTime: selectedAssociationTaskTime,
      setIsShowEmailButton,
    };
  };

  // 导出关联分析结果
  const exportAnalysisResult = executionId => {
    setIsShowLoader(true);
    pages.setSecondaryAuth(
      'analysis',
      'generateReport',
      ticket => {
        generateAnalysisReport(
          { id: executionId, ticket },
          resp => {
            setIsShowLoader(false);
            if (!resp || resp.resultCode !== RESULT_CODE.success) {
              modal.error($t('error'), resp.resultMessage || $t('associationAnalysis.export.failed'));
              return;
            }
            modal.success(
              $t('indicator.associationAnalysis.successTitle'),
              <div>
                {$t('associationAnalysis.export.success')}
                <span className="showReportTextButton" onClick={showAnalysisResultReport}>
                  {$t('associationAnalysis.export.buttonText.show')}
                </span>
              </div>
            );
          },
          () => {
            setIsShowLoader(false);
            modal.error($t('error'), $t('associationAnalysis.export.failed'));
          }
        );
      },
      () => {
        setIsShowLoader(false);
      }
    );
  };

  // 查看生成的关联分析报告--跳转流程编排
  const showAnalysisResultReport = () => {
    let origin = window.location.origin;
    window.open(`${origin}${FLOW_ORCHESTRATION_URL}`);
  };

  // 组装关联分析结果抽屉
  const returnCustomDrawer = useMemo(() => {
    return (
      <Drawer
        visible={isShowDrawer}
        placement="right"
        title={
          <div className="associationAnalysisDrawerTitle">
            <div style={{ paddingLeft: '16px' }}>{$t('indicator.detail.associationAnalysis.pageTitle')}</div>
            <div>
              {isShowEmailButton && (
                <Tooltip content={$t('analysis.detail.common.button.email')}>
                  <div className={pageCss.icon_div_email} onClick={() => setIsShowEmailDialog(true)} />
                </Tooltip>
              )}
            </div>
            <Tooltip content={$t('analysis.detail.common.button.export')}>
              <div
                className={pageCss.icon_div_export}
                onClick={() => exportAnalysisResult(selectedAssociationTask)}
              />
            </Tooltip>
            <Tooltip content={$t('analysis.detail.common.button.close')}>
              <div>
                <Icon
                  name="close"
                  size={['1rem', '1rem']}
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    switchAnalysisResultDrawer(false);
                  }}
                />
              </div>
            </Tooltip>
            <div />
          </div>
        }
        width="88%"
        closable={false}
        bodyStyle={{ overflow: 'auto', padding: '0' }}
        style={{ flexDirection: 'row-reverse', justifyContent: 'flex-start' }}
        drawerStyle={{ marginBottom: '5px', minHeight: 'calc(100vh - 4.6rem)', minWidth: '1000px' }}
        onClose={() => switchAnalysisResultDrawer(false)}
        footer={false}
      >
        <AssociationAnalysisResultPage {...getAssociationAnalysisParam()} />
      </Drawer>
    );
  }, [isShowDrawer, isShowEmailButton]);

  // 组装选择关联分析指标的组件入参
  const getSelectIndexParams = () => {
    return {
      parentIndicatorInfo: {
        indicatorTaskId: taskId,
        indicatorId: id,
      },
      selectedAnalysisTime: selectAnalysisTime,
      parentDispatch: dispatch,
      switchAnalysisResultDrawer,
      showSelectTaskTypeDialog,
      setShowSelectTaskTypeDialog,
    };
  };

  // 查看异常关联分析
  const showAnalysisResult = (startTime, endTime, exceptionId) => {
    let allAnalysisResult = [...associationAnalysisTask.current];
    let resultArr = allAnalysisResult.filter(item => {
      return item.outlierId === exceptionId;
    });
    if (resultArr.length === 0) {
      modal.error($t('error'), $t('analysis.detail.button.analysisResult.failed'));
      return;
    }
    jumpAnalysisResult(resultArr[0].id, resultArr[0].executionTime);
  };

  return (
    <div style={{ padding: '16px' }}>
      {isShowExceptionTable && (
        <>
          <div style={{ float: 'right', marginBottom: '12px' }}>
            <ButtonMenu
              text={$t('batchMark')}
              options={buttonOptions}
              value={1}
              onClick={batchMarkClick}
              className="markButtonMenuCls"
              optionStyle={{ width: '128px' }}
            />
          </div>
          <Table
            id="exceptionTable"
            columns={CONSTANT.exceptionTableCols}
            dataset={exceptionsData}
            enablePagination
            keyIndex={0}
            headerCheckBoxSortAllow={false}
            pageSize={pageSize}
            currentPage={currentPage}
            recordCount={recordCount}
            onPageChange={handlePaging}
            onPageSizeChange={handlePageSize}
            pageSizeOptions={[5, 10, 50, 100]}
            enableCheckBox
            ref={table}
            checkedRows={checkedIdArr}
            onRowCheck={(row, checkedRows) => setCheckedIdArr(checkedRows)}
            onHeaderCheck={onTableHeaderCheck}
          />
        </>
      )}
      <div id={chartEleId} className="chartDivCls" />
      {returnCustomDrawer}
      <SelectIndex {...getSelectIndexParams()} />
      <Dialog
        title={$t('indicator.detail.associationAnalysis.showAnalysisResult')}
        style={{
          maxHeight: '700px',
          minHeight: '670px',
          maxWidth: '1400px',
          minWidth: '1400px',
          left: '20%',
        }}
        closeOnEscape={false}
        isOpen={showAnalysisTaskDialog}
        onClose={() => switchAnalysisTaskDialog(false)}
        modal={true}
      >
        <AssociationAnalysisTask
          closeDialog={switchAnalysisTaskDialog}
          openDrawer={switchAnalysisResultDrawer}
          indicatorTaskId={taskId}
          indicatorId={id}
          indicatorName={name}
          timeRangeRadioId={timeRange}
          customTimes={changeCustomTimeStampToTime(timePicker)}
          executionTime={associationTaskTime}
          parentDispatch={dispatch}
          exportAnalysisResult={exportAnalysisResult}
          openEmailDialog={() => setIsShowEmailDialog(true)}
        />
      </Dialog>
      <TransferLearningDialog
        taskId={taskId}
        indicatorId={id}
        indicatorName={name}
        timeRangeRadioId={timeRange}
        customTimes={changeCustomTimeStampToTime(timePicker)}
        isShow={isShowTransferLearningDialog}
        closeTransferLearningListDialog={() => setIsShowTransferLearningDialog(false)}
      />
      <Loader id="exportLoader" type="global" isOpen={isShowLoader} desc={$t('analysis.detail.log.common.loading')} />
      {isShowEmailDialog && (
        <EmailDetails
          closeDialog={() => setIsShowEmailDialog(false)}
          setIsShowLoader={setIsShowLoader}
          analysisTaskId={selectedAssociationTask}
        />
      )}
    </div>
  );
};

export default RowExpandPages;
