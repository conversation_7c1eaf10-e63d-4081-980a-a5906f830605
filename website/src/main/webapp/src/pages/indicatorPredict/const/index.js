/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import React from 'react';
import { $t, registerI18nResource, util } from '@util';
import i18nResource from '../locale';

// 注册国际化资源
registerI18nResource(i18nResource, 'indicatorPredict');

// 汇聚规则
export const CONVERGENCE_MODE = {
  min: 1,
  max: 2,
  average: 4,
  sum: 8,
};

// 汇聚规则下拉框
export const CONVERGENCE_MODE_OPTIONS = [
  { text: $t('predict.common.convergence.min'), value: CONVERGENCE_MODE.min },
  { text: $t('predict.common.convergence.max'), value: CONVERGENCE_MODE.max },
  { text: $t('predict.common.convergence.avg'), value: CONVERGENCE_MODE.average },
  { text: $t('predict.common.convergence.sum'), value: CONVERGENCE_MODE.sum },
];

export const AUTO_REFRESH_TIME = 60 * 1000;

// 预测任务类型
export const TASK_TYPE = {
  single: 0,
  union: 1,
  group: 2,
};

export const TIME_TYPE = {
  around: 0,
  custom: 1,
  default: 2,
};

export const TIME_UNIT = {
  hour: 'HOUR',
  day: 'DAY',
};

export const TIME_TYPE_RADIO = [
  { value: TIME_TYPE.default, text: $t('predict.filter.time.default') },
  { value: TIME_TYPE.around, text: $t('predict.filter.time.around') },
  { value: TIME_TYPE.custom, text: $t('predict.filter.time.custom') },
];

export const TIME_UNIT_RADIO = [
  { value: TIME_UNIT.hour, text: $t('predict.filter.time.hour') },
  { value: TIME_UNIT.day, text: $t('predict.filter.time.day') },
];

export const CHART_LINE_COLOR = '#6D8FF0';
export const PREDICT_LINE_COLOR = '#00A874';
export const ERROR_LINE_COLOR = '#F46465';
export const DRIFT_COLOR = '#036bd2';

export const PREDICT_STATUS = {
  success: 0,
  failed: 1,
};

export const PREDICT_STATUS_COLOR = {
  [PREDICT_STATUS.success]: '#d9ead3',
  [PREDICT_STATUS.failed]: '#f4cccc',
};

export const PREDICT_STATUS_TEXT = {
  [PREDICT_STATUS.success]: $t('predict.status.success'),
  [PREDICT_STATUS.failed]: $t('predict.status.failed'),
};

export const BEFORE_SPINNER_MAX = {
  hour: 24,
  day: 30,
};
export const AFTER_SPINNER_MAX = {
  hour: 24,
  day: 30,
};

export const CHART_LINE_SERIES_ID = {
  history: 'historySeries',
  predict: 'predictSeries',
  errorRate: 'errorRateSeries',
};

export const PAGE_TYPE = {
  single: 'single',
  union: 'union',
};

export const GROUP_TABLE_COLUMN = [
  {
    key: 'indicatorId',
    display: false,
    isMovable: false,
    disableOrderChange: true,
  },
  {
    key: 'predictTime',
    isMovable: false,
    display: false,
    disableOrderChange: true,
  },
  {
    key: 'conceptDriftSwitch',
    isMovable: false,
    display: false,
    disableOrderChange: true,
  },
  {
    title: $t('predict.table.column.group.name'),
    allowSort: false,
    width: '13%',
    key: 'clusterName',
  },
  {
    title: $t('predict.table.column.indexName'),
    allowSort: false,
    width: '13%',
    key: 'indexName',
  },
  {
    title: $t('predict.table.column.measUnitName'),
    allowSort: false,
    width: '13%',
    key: 'measUnitName',
  },
  {
    title: $t('predict.table.column.displayValue'),
    allowSort: false,
    width: '13%',
    key: 'displayValue',
  },
  {
    title: $t('predict.table.column.moName'),
    allowSort: false,
    width: '13%',
    key: 'moName',
  },
  {
    title: $t('predict.table.column.lastPredictTime'),
    allowSort: false,
    width: '13%',
    key: 'lastPredictTime',
    render: value => {
      return util.dateTimeFormat(value);
    },
    tipFormatter: value => { return util.dateTimeFormat(value) || ''; },
  },
  {
    title: $t('predict.table.column.predictStatus'),
    allowSort: false,
    width: '14%',
    key: 'predictStatus',
  },
  {
    title: $t('common.operation'),
    allowSort: false,
    width: '8%',
    key: 'operation',
    tipFormatter: () => { return '' },
  },
];
export const SINGLE_TABLE_COLUMN = [
  {
    key: 'indicatorId',
    display: false,
    isMovable: false,
    disableOrderChange: true,
  },
  {
    key: 'predictTime',
    isMovable: false,
    display: false,
    disableOrderChange: true,
  },
  {
    key: 'conceptDriftSwitch',
    isMovable: false,
    display: false,
    disableOrderChange: true,
  },
  {
    title: $t('predict.table.column.indexName'),
    allowSort: false,
    width: '18%',
    key: 'indexName',
  },
  {
    title: $t('predict.table.column.measUnitName'),
    allowSort: false,
    width: '16%',
    key: 'measUnitName',
  },
  {
    title: $t('predict.table.column.displayValue'),
    allowSort: false,
    width: '16%',
    key: 'displayValue',
  },
  {
    title: $t('predict.table.column.moName'),
    allowSort: false,
    width: '14%',
    key: 'moName',
  },
  {
    title: $t('predict.table.column.lastPredictTime'),
    allowSort: false,
    width: '14%',
    key: 'lastPredictTime',
    render: value => {
      return util.dateTimeFormat(value);
    },
    tipFormatter: value => { return util.dateTimeFormat(value) || ''; },
  },
  {
    title: $t('predict.table.column.predictStatus'),
    allowSort: false,
    width: '14%',
    key: 'predictStatus',
  },
  {
    title: $t('common.operation'),
    allowSort: false,
    width: '8%',
    key: 'operation',
    tipFormatter: () => { return '' },
  },
];

export const CONCEPT_DRIFTING_STATUS = 0;
export const UNION_TABLE_COLUMN = [
  {
    key: 'unitedId',
    display: false,
    isMovable: false,
    disableOrderChange: true,
  },
  {
    key: 'conceptDriftSwitch',
    isMovable: false,
    display: false,
    disableOrderChange: true,
  },
  {
    title: $t('predict.table.column.clusterName'),
    allowSort: false,
    width: '14%',
    key: 'clusterName',
  },
  {
    title: $t('predict.table.column.site'),
    allowSort: false,
    width: '9%',
    key: 'siteId',
  },
  {
    title: $t('predict.table.column.indexName'),
    allowSort: false,
    width: '14%',
    key: 'indexName',
  },
  {
    title: $t('predict.table.column.measUnitName'),
    allowSort: false,
    width: '14%',
    key: 'measUnitName',
  },
  {
    title: $t('predict.table.column.displayValue'),
    allowSort: false,
    width: '14%',
    key: 'displayValue',
  },

  {
    title: $t('predict.table.column.lastPredictTime'),
    allowSort: false,
    width: '14%',
    key: 'lastPredictTime',
    render: value => {
      return util.dateTimeFormat(value);
    },
  },
  {
    title: $t('predict.table.column.predictStatus'),
    allowSort: false,
    width: '14%',
    key: 'predictStatus',
  },
  {
    title: $t('common.operation'),
    allowSort: false,
    width: '7%',
    key: 'operation',
    tipFormatter: () => { return '' },
  },
];

export const MARK_TABLE_COLUMN = [
  {
    key: 'id',
    display: false,
    isMovable: false,
    disableOrderChange: true,
  },
  {
    title: $t('predict.markInterval.table.startTime'),
    allowSort: false,
    width: '28%',
    key: 'startTime',
  },
  {
    title: $t('predict.markInterval.table.endTime'),
    allowSort: false,
    width: '28%',
    key: 'endTime',
  },
  {
    title: $t('predict.markInterval.table.createTime'),
    allowSort: false,
    width: '28%',
    key: 'createTime',
  },
  {
    title: $t('common.operation'),
    allowSort: false,
    width: '16%',
    key: 'operation',
  },
];
