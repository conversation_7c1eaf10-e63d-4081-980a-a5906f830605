/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Row, Col, Table, Button, Tooltip, PageMessage, Drawer, Icon, TextButton } from 'eview-ui';
import { $t, validate, modal } from '@util';
import SearchInput from '@comp/SearchInput';
import CustomDrawer from '@comp/CustomDrawer';
import { OPERATE_SUCCESS_CODE, TEMPLATE_NODE_PAGE_TYPE } from '@const/common';
import { ConfigureProfileProps, profileInfoState, HostProfileTableExpandRow, BusinessProfileTableInfo, } from '../interface';
import {
  HOST_PROFILE_TABLE_COLUMNS, TASK_PAGE_TYPE, NewProfileContext, WIZARDS_STEP_NAMES, HOST_PROFILE_TABLE_VIEW_COLUMNS,
  RELATED_INDICATOR_COLUMNS, BUSINESS_PROFILE_TABLE_COLUMNS, PROFILE_TYPE_MAPS, MAX_PER_SERVICE_PROFILE,
  BUSINESS_PROFILE_TABLE_VIEW_COLUMNS
} from '../const';
import LabelHint from '@comp/LabelHint';
import NewHostProfileDrawer from './NewHostProfileDrawer';
import NewBusinessProfileDrawer from './NewBusinessProfileDrawer';
import * as api from '../api';

const ConfigureProfile = (props: ConfigureProfileProps) => {
  const { setProfileInfo, returnToList, profileInfo, isAssociated } = props;
  // @ts-expect-error: NewProfileContext的值为数组
  const [state, dispatch] = useContext(NewProfileContext);
  const { profileData, basicInfo } = state;
  const [profilePageType, setProfilePageType] = useState(props.pageType);
  const nameSearch = useRef<any>(null); // 名称ref
  const profileCache = useRef<any>({});
  const medianProfileInfo = useRef({});

  useEffect(() => {
    profileCache.current = { ...profileData };
  }, [profileData]);

  useEffect(() => {
    // 初始进入页面，下拉表格展开状态重置
    setProfileState({ expandedRow: [] });

    return () => {
      $('body').removeClass('hiddenXYScroll');
    };
  }, []);

  useEffect(() => {
    if (profilePageType !== TASK_PAGE_TYPE.NEW) { //回显数据
      let isHost = profileInfo.type === PROFILE_TYPE_MAPS.HOST;
      let tableData = isHost ? profileInfo.hostPortraitList : profileInfo.businessPortraitList;
      tableData = tableData.map(item => ({ ...item, operation: getCustomOperation(item) }));
      setProfileState({
        cacheTableList: tableData,
        tableList: tableData,
        expandedRow: []
      });
    }
  }, [profilePageType]);

  const setProfileState = (param: profileInfoState) => {
    dispatch({ type: 'setProfileData', profileData: { ...profileCache.current, ...param } });
  };

  // 上一步
  const goPreStep = () => {
    dispatch({ type: 'setCurrentTab', currentTab: WIZARDS_STEP_NAMES.BASIC_INFO });
  };

  // 获取删除后的表格数据
  const deleteCommon = (TableList, dataInfo) => {
    let findIndex;
    if (basicInfo.profileType === PROFILE_TYPE_MAPS.HOST) {
      findIndex = TableList.findIndex(item => item.name === dataInfo.name);
    } else {
      findIndex = TableList.findIndex(item => item.businessName === dataInfo.businessName);
    }
    let tableData = [...TableList];
    if (findIndex !== -1) {
      tableData.splice(findIndex, 1);
    }
    return tableData;
  };

  // 删除画像
  const deleteProfile = (dataInfo) => {
    let cacheTableList = deleteCommon(profileCache.current.cacheTableList, dataInfo);
    let tableList = deleteCommon(profileCache.current.tableList, dataInfo);
    setProfileState({ cacheTableList, tableList, expandedRow: [] });
  };

  // 自定义操作列
  const getCustomOperation = (dataInfo) => {
    return (
      <>
        <TextButton
          text={$t('profile.modify')}
          style={{ marginRight: '20px' }}
          disabled={dataInfo.isAssociated}
          onClick={() => modify(dataInfo)}
        />
        <TextButton
          text={$t('profile.delete')}
          disabled={dataInfo.isAssociated}
          onClick={() => deleteProfile(dataInfo)}
        />
      </>
    );
  };

  // 编辑画像
  const modify = (drawerInfo) => {
    $('body').addClass('hiddenXYScroll');
    if (basicInfo.profileType === PROFILE_TYPE_MAPS.HOST) {
      setProfileState({ isHostDrawerOpen: true, drawerPageType: TEMPLATE_NODE_PAGE_TYPE.MODIFY, drawerInfo });
    } else {
      setProfileState({ isBusinessDrawerOpen: true, drawerPageType: TEMPLATE_NODE_PAGE_TYPE.MODIFY, drawerInfo });
    }
  };

  // 名称过滤搜索回调
  // @ts-expect-error: React是全局变量
  const searchHostProfileListByName = (event: React.KeyboardEvent) => {
    if (event.keyCode === 13 || event.type === 'click') {
      if (nameSearch.current && !nameSearch.current?.validate()) {
        nameSearch.current.focus();
        return;
      }
      setProfileState({ tableList: getFilterData(profileCache.current.cacheTableList), expandedRow: [] });
    }
  };

  // 获取过滤后列表数据
  const getFilterData = (data) => {
    let filterList = [];
    for (let i = 0; i < data.length; i++) {
      let filterKey = basicInfo.profileType === PROFILE_TYPE_MAPS.HOST ? 'name' : 'portraitName';
      if (data[i][filterKey].toLowerCase().indexOf(profileCache.current.searchName.toLowerCase()) > -1) {
        filterList.push({ ...data[i] });
      }
    }
    return filterList;
  };

  // 创建画像抽屉组件公共数据
  const profileDrawerProps = () => ({
    width: 'calc(98%)',
    bodyStyle: { overflow: 'auto' },
    style: { flexDirection: 'row-reverse', justifyContent: 'flex-start', overflow: 'auto' },
    drawerStyle: { marginBottom: '5px', minHeight: 'calc(100vh - 4.75rem)', minWidth: '1250px' },
    footer: false
  });

  // 创建画像回调
  const submitProfileCallBack = (param: BusinessProfileTableInfo, { drawerPageType, drawerShowKey, filterKey }:
    { drawerPageType: string; drawerShowKey: string; filterKey: string }, filterVal) => {
    let tableResult = [...profileCache.current.cacheTableList];
    if (drawerPageType === TEMPLATE_NODE_PAGE_TYPE.MODIFY) {
      let index = tableResult.findIndex(item => item[filterKey] === filterVal);
      tableResult[index] = { operation: getCustomOperation(param), ...param };
    } else {
      tableResult.unshift({ operation: getCustomOperation(param), ...param });
    }
    $('body').removeClass('hiddenXYScroll');
    setProfileState({
      tableList: getFilterData(tableResult), cacheTableList: tableResult, [drawerShowKey]: false, expandedRow: []
    });
  };

  // 根据指定key获取表格数据
  const getTableDataByKey = (key: string) => {
    return profileData.cacheTableList.map(item => item[key]);
  };

  // 关闭抽屉组件回调
  const closeDrawer = (drawerKey: string): void => {
    $('body').removeClass('hiddenXYScroll');
    setProfileState({ [drawerKey]: false });
  };

  // 主机画像
  const returnNewHostProfileDrawer = useMemo(() => {
    return (
      <CustomDrawer
        visible={profileData.isHostDrawerOpen}
        title={$t('profile.host.configure.title')}
        customChildren={
          <NewHostProfileDrawer
            drawerPageType={profileData.drawerPageType}
            submitCallBack={submitProfileCallBack}
            drawerInfo={profileData.drawerInfo}
            nameLists={getTableDataByKey('name')}
            hostTypeLists={getTableDataByKey('hostType')}
            onClose={() => closeDrawer('isHostDrawerOpen')}
          />
        }
        onClose={() => closeDrawer('isHostDrawerOpen')}
        {...profileDrawerProps()}
      />
    );
  }, [profileData.isHostDrawerOpen]);

  // 业务画像
  const returnNewBusinessProfileDrawer = useMemo(() => {
    return (
      <CustomDrawer
        visible={profileData.isBusinessDrawerOpen}
        title={$t('profile.business.configure.title')}
        customChildren={
          <NewBusinessProfileDrawer
            drawerPageType={profileData.drawerPageType}
            submitCallBack={submitProfileCallBack}
            nameLists={getTableDataByKey('portraitName')}
            serviceNameLists={getTableDataByKey('businessName')}
            onClose={() => closeDrawer('isBusinessDrawerOpen')}
            drawerInfo={profileData.drawerInfo}
          />
        }
        onClose={() => closeDrawer('isBusinessDrawerOpen')}
        {...profileDrawerProps()}
      />
    );
  }, [profileData.isBusinessDrawerOpen]);

  // 表格点击下拉展开回调
  const handleRowExpend = (row: HostProfileTableExpandRow) => {
    let indicatorLists = row._org.portraitIndicatorList;
    return (
      <div style={{ padding: '20px', background: '#efefef' }}>
        <Table
          columns={RELATED_INDICATOR_COLUMNS}
          maxHeight={430}
          width='100%'
          splitPagination={false}
          dataset={indicatorLists}
          showEmptyImage={false}
        />
      </div>
    );
  };

  // 创建画像
  const create = () => {
    $('body').addClass('hiddenXYScroll');
    if (basicInfo.profileType === PROFILE_TYPE_MAPS.HOST) {
      setProfileState({ isHostDrawerOpen: true, drawerPageType: TEMPLATE_NODE_PAGE_TYPE.NEW, drawerInfo: {} });
    } else {
      setProfileState({ isBusinessDrawerOpen: true, drawerPageType: TEMPLATE_NODE_PAGE_TYPE.NEW, drawerInfo: {} });
    }
  };

  // 获取提交参数
  const getSaveParam = (isModify) => {
    let param = isModify ? {
      id: profileInfo.id,
      name: profileInfo.name,
      type: profileInfo.type,
      cpuNum: profileInfo.cpuNum,
      ramNum: profileInfo.ramNum,
      description: profileInfo.description,
    } : {
      name: basicInfo.profileName,
      type: basicInfo.profileType,
      cpuNum: basicInfo.profileType === PROFILE_TYPE_MAPS.HOST ? parseInt(basicInfo.cpuNum) : undefined,
      ramNum: basicInfo.profileType === PROFILE_TYPE_MAPS.HOST ? parseInt(basicInfo.ramNum) : undefined,
      description: basicInfo.description,
    };

    let profileList;
    if (basicInfo.profileType === PROFILE_TYPE_MAPS.HOST) { // 主机
      profileList = profileData.cacheTableList.map(item => ({
        name: item.name,
        hostType: item.hostType,
        cpuNum: parseInt(item.cpuNum),
        ramSize: parseInt(item.ramSize),
        baseHostNum: item.baseHostNum ? parseInt(item.baseHostNum) : '',
        minHostNum: parseInt(item.minHostNum),
        maxHostNum: parseInt(item.maxHostNum),
        deployServiceList: item.deployServiceList,
        portraitIndicatorList: item.portraitIndicatorList,
      }));
    } else {
      profileList = profileData.cacheTableList.map(item => ({
        portraitName: item.portraitName,
        businessName: item.businessName,
        minCpuNum: parseInt(item.minCpuNum),
        maxCpuNum: parseInt(item.maxCpuNum),
        minRamNum: parseInt(item.minRamNum),
        maxRamNum: parseInt(item.maxRamNum),
        minReplicaNum: parseInt(item.minReplicaNum),
        maxReplicaNum: parseInt(item.maxReplicaNum),
        serviceDeployType: item.serviceDeployType,
      }));
    }

    let listKey = basicInfo.profileType === PROFILE_TYPE_MAPS.HOST ? 'hostPortraitList' : 'businessPortraitList';
    return { ...param, [listKey]: profileList };
  };

  // 切换到修改
  const switchModifyProfile = () => {
    medianProfileInfo.current = { ...profileData };
    setProfilePageType(TASK_PAGE_TYPE.MODIFY);
    setProfileState({ expandedRow: [], searchName: '' });
  };

  // 取消修改
  const cancelModify = () => {
    setProfilePageType(TASK_PAGE_TYPE.VIEW);
    setProfileState({ ...medianProfileInfo.current, expandedRow: [] });
  };

  // 保存服务画像
  const submitProfileInfo = (isModify) => {
    if (!profileData.cacheTableList.length) {
      modal.error($t('profile.common.error.tip'), $t('profile.no.data.tip'));
      return;
    }

    dispatch({ type: 'setIsLoadingShow', isLoadingShow: true });
    let submitParam = getSaveParam(isModify);
    api.saveProfileInfo(submitParam, res => {
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
        modal.error($t('kpi.task.common.error.tip'), res.resultMessage || $t('profile.common.save.fail.tip'));
        return;
      }

      if (isModify) {
        getViewProfileInfo();
      } else {
        dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
        modal.success($t('profile.common.tip'), $t('profile.common.save.success.tip'), () => {
          returnToList(1);
        }, () => {
          returnToList(1);
        });
      }
    }, () => {
      dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
    });
  };

  // 查看详情
  const getViewProfileInfo = () => {
    api.viewServiceProfile({ productPortraitId: profileInfo.id, }, res => {
      dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
      if (!res || res.resultCode !== OPERATE_SUCCESS_CODE) {
        modal.error($t('profile.common.error.tip'), $t('profile.common.view.fail.tip'));
        return;
      }

      setProfileInfo(res.data);
      setProfileState({ expandedRow: [] });
      setProfilePageType(TASK_PAGE_TYPE.VIEW);
    }, () => {
      dispatch({ type: 'setIsLoadingShow', isLoadingShow: false });
    });
  };

  return (
    profilePageType === TASK_PAGE_TYPE.VIEW ?
      <Row style={{ marginTop: '30px' }}>
        <Col cols={2} className='table_label'>
          <LabelHint label={$t('profile.list')} isHelp={false} require={false} noColon={false} />
        </Col>
        <Col cols={18}>
          {
            profileInfo.type === PROFILE_TYPE_MAPS.HOST ?
              <Table
                enableRowExpand // 启用展开
                onRowExpend={handleRowExpend} // 点击展开回调事件
                columns={HOST_PROFILE_TABLE_VIEW_COLUMNS}
                width='100%'
                dataset={profileData.tableList}
                showEmptyImage={false}
                enableMulitiExpand
                maxHeight={700}
                emptyTableMsg={profileData.emptyTableMsg}
                expandedRow={profileData.expandedRow}
              /> :
              <Table
                columns={BUSINESS_PROFILE_TABLE_VIEW_COLUMNS}
                width='100%'
                dataset={profileData.tableList}
                showEmptyImage={false}
                enableMulitiExpand
                maxHeight={700}
                emptyTableMsg={profileData.emptyTableMsg}
              />
          }
        </Col>
        <Col cols={2}>
          {
            isAssociated ?
              <Tooltip content={$t('profile.associated.tip')}>
                <Button
                  text={$t('kpi.task.common.modify')}
                  status="primary" disabled style={{ float: 'right' }}
                />
              </Tooltip> :
              <Button
                text={$t('profile.common.modify')}
                status="primary" style={{ float: 'right' }} onClick={switchModifyProfile}
              />
          }
        </Col>
      </Row> :
      <>
        <Row style={{ marginTop: '30px' }}>
          <Col cols={24}>
            <PageMessage
              text={basicInfo.profileType === PROFILE_TYPE_MAPS.HOST ? $t('profile.host.page.message')
                : $t('profile.business.page.message')}
              type="info"
              style={{ width: '100%', marginBottom: '15px' }}
            />
          </Col>
        </Row>
        <Row style={{ marginTop: '10px' }}>
          <Col cols={24}>
            <div style={{ float: 'left' }}>
              <SearchInput placeholder={$t('profile.search.name')} id='searchName'
                inputStyle={{ width: '240px' }}
                value={profileData.searchName}
                onChange={(val: string) => setProfileState({ searchName: val })}
                validator={(val: string, id: string) => validate(['cmpValidChar', 'checkLength'], val, id, null, 64)}
                onSearch={searchHostProfileListByName}
                onRef={(ele) => nameSearch.current = ele}
              />
            </div>
            <Button text={$t('profile.create')} status='primary' style={{ float: 'right' }}
              onClick={create} disabled={profileData.cacheTableList.length >= MAX_PER_SERVICE_PROFILE}
            />
          </Col>
        </Row>
        <Row style={{ marginTop: '30px' }}>
          <Col cols={24}>
            {
              basicInfo.profileType === PROFILE_TYPE_MAPS.HOST ?
                <Table
                  enableRowExpand // 启用展开
                  onRowExpend={handleRowExpend} // 点击展开回调事件
                  columns={HOST_PROFILE_TABLE_COLUMNS}
                  maxHeight={500}
                  width='100%'
                  dataset={profileData.tableList}
                  showEmptyImage={false}
                  enableMulitiExpand
                  emptyTableMsg={profileData.emptyTableMsg}
                  expandedRow={profileData.expandedRow}
                /> :
                <Table
                  columns={BUSINESS_PROFILE_TABLE_COLUMNS}
                  width='100%'
                  dataset={profileData.tableList}
                  showEmptyImage={false}
                  enableMulitiExpand
                  maxHeight={500}
                  emptyTableMsg={profileData.emptyTableMsg}
                />
            }
            <span style={{ marginTop: '10px', display: 'block' }}>
              {$t('profile.common.total')}{profileData.tableList.length}
            </span>
          </Col>
        </Row>
        <Row style={{ marginTop: '40px' }}>
          <Col cols={24}>
            {
              (profilePageType === TASK_PAGE_TYPE.NEW || profilePageType === TASK_PAGE_TYPE.COPY) ?
                <>
                  <Button text={$t('profile.common.cancel')} style={{ float: 'left', marginRight: '20px' }}
                    onClick={() => returnToList(1)}
                  />
                  <Button text={$t('profile.common.last.step')} style={{ marginRight: '20px' }}
                    onClick={goPreStep}
                  />
                  <Button text={$t('profile.common.submit')} style={{ marginRight: '20px' }}
                    status="primary"
                    onClick={() => submitProfileInfo(false)}
                  />
                </> :
                <>
                  <Button text={$t('profile.common.cancel')} style={{ float: 'left', marginRight: '20px' }}
                    onClick={cancelModify}
                  />
                  <Button text={$t('profile.common.confirm')} status="primary"
                    onClick={() => submitProfileInfo(true)}
                  />
                </>
            }
          </Col>
        </Row>
        {returnNewHostProfileDrawer}
        {returnNewBusinessProfileDrawer}
      </>
  );
};

export default ConfigureProfile;
