'use strict';

function SearchCtrl($scope, $rootScope, $sce) {
  let pageIndex = 1;
  let pageSize = 10;
  $scope.GLOBAL_LANG = I18n.locale;
  const SPECIAL_CONCATENATED_FIELDS = ['GroupName', 'SiteName', 'ClusterId', 'SolutionId', 'SolutionType'];
  const DEFAULT_OPERATOR_NA = 0;
  const EIGHT_HOURS = 8 * 60 * 60 * 1000;
  const BEST_AGG_MAP = {
    '1s': 1000,
    '5s': 5 * 1000,
    '10s': 10 * 1000,
    '30s': 30 * 1000,
    '1m': 60 * 1000,
    '5m': 5 * 60 * 1000,
    '10m': 10 * 60 * 1000,
    '30m': 30 * 60 * 1000,
    '1h': 60 * 60 * 1000,
    '2h': 2 * 60 * 60 * 1000,
    '3h': 3 * 60 * 60 * 1000,
    '6h': 6 * 60 * 60 * 1000,
    '12h': 12 * 60 * 60 * 1000,
    '1d': 24 * 60 * 60 * 1000,
    '7d': 7 * 24 * 60 * 60 * 1000,
    '30d': 30 * 24 * 60 * 60 * 1000,
    '365d': 365 * 24 * 60 * 60 * 1000,
  };
  const SELECTION_SIZE = 1000;

  $scope.init = function() {
    setHelpId('searchHelpLib_', 'cmp.logmatrix.templatesearch');
    $scope.$Model = {};
    $scope.$Model.indexPatternList = [];
    // 分组局址solutionID做为查询条件时存放的全局变量，用于change事件是否要进行的依据
    $scope.allSearchFields = {};
    // 从cookie中获取已保存的模板
    if (getCookie('patternCookie') !== undefined) {
      $scope.cookiePattern = JSON.parse(getCookie('patternCookie'));
      $scope.hasCookie = true;
    } else {
      $scope.hasCookie = false;
    }
    $scope.$Model.downloadCount = 5000;
    $scope.$Model.downloadSelectedFieldsList = [];
    $scope.$Model.drilldownloadCount = 200;

    let jumpUrl = window.location.href;
    if (jumpUrl.indexOf('AlarmId') !== -1) {
      // 从当前告警直接跳转到告警配置的界面参数获取
      $scope.alarmJumpingParams(jumpUrl);
    }

    // 解析URL中的参数
    $scope.parseUrlParams();

    $scope.$Model.beginTime = null;
    $scope.$Model.endTime = null;
    $scope.$Model.LayerSearch = true;
    $scope.isIndexRuleMode = false;
    $scope.hasSearch = false;
    $scope.noColShow = true;
    $scope.isShowTips = false;
    $scope.supportDrill = false;
    // 设置默认排序规则
    $scope.orderType = 'desc';
    // 设置默认日志钻取数
    $scope.drillingLog = 100;
    // 设置默认分层日志钻取数
    $scope.drillingLayerLog = 1000;
    // 设置默认分层日志下载数
    $scope.downloadLayerLog = 10000;
    // 默认分层日志并发下载个数
    $scope.downloadLayerLogMaxCount = 10;
    $scope.downloadLayerLogMinCount = 1;
    // 设置默认不包含时间查询条件
    $scope.hasTimeQuery = false;
    // 设置默认查询到的日志数
    $scope.count = 0;
    // 用于切换索引模板时初始化表格
    $scope.isReady = true;
    // 指示搜索页面初始化是否完成
    $scope.isInitOK = false;
    // 包含子scope时配置
    $scope.childController = {};
    // 默认下载日志条数
    $scope.downloadCount = 5000;
    $scope.drilldownloadCount = 200;
    // 自定义下载日志条数限制
    $scope.minDownloadCount = 1;
    $scope.maxDownloadCount = 100000;
    // 下载日志条数改变countPerSearch阈值
    $scope.downloadThreshold = 30000;

    // 搜索模板中选中的展示字段
    $scope.displayedFieldsList = [];

    // 日志检索表单数据
    $scope.$Model.logInfo = [];

    $scope.searchBeginTime = null;
    $scope.searchEndTime = null;

    $scope.searchBeginTimeNotUTC = null;
    $scope.searchEndTimeNotUTC = null;

    // 查询业务集群相关信息
    $scope.clusterInfos = {};
    $scope.solutionInfos = {};

    $scope.$Model.alertConfig = {};

    $scope.$Model.ChartDisplay = true;

    $scope.queryConfig();
    $scope.AGGREGATION_RULE = [
      {
        label: I18n.get('cmp.logmatrix.visualconfig.aggregation.auto'),
        value: 'auto',
      },
      {
        label: I18n.get('cmp.logmatrix.visualconfig.aggregation.1second'),
        value: '1s',
      },
      {
        label: I18n.get('cmp.logmatrix.visualconfig.aggregation.1min'),
        value: '1m',
      },
      {
        label: I18n.get('cmp.logmatrix.visualconfig.aggregation.1hour'),
        value: '1h',
      },
      {
        label: I18n.get('cmp.logmatrix.visualconfig.aggregation.1day'),
        value: '1d',
      },
      {
        label: I18n.get('cmp.logmatrix.visualconfig.aggregation.1week'),
        value: '7d',
      },
      {
        label: I18n.get('cmp.logmatrix.visualconfig.aggregation.1month'),
        value: '30d',
      },
      {
        label: I18n.get('cmp.logmatrix.visualconfig.aggregation.1year'),
        value: '365d',
      },
    ];
    $scope.clearChartData();

    // 初始查询所有模板数据
    $scope.queryIndexPattern();
    dataCheckFunc($scope);
  };

  // 告警跳转初始化页面方法
  $scope.alarmJumpingParams = function(jumpUrl) {
    // 跳转不根据cookie去跳转
    $scope.hasCookie = false;
    // 解析参数
    $scope.alarmId = decodeURIComponent(searchUrlParam('AlarmId'));
    $scope.indexLabel = decodeURIComponent(searchUrlParam('IndexLabel'));
    $scope.postTime = decodeURIComponent(searchUrlParam('PostTime'));
    $scope.timeRange = decodeURIComponent(searchUrlParam('TimeRange'));
  };

  $scope.customValidator = function() {
    let validationReturnMap = {};
    let timeScope = angular.element(document.getElementById('quickBeginTimeEview')).scope();
    let startDate = timestampToTime(timeScope.quickBeginTimeEview.getUTCDateTime());
    let endDate = timestampToTime(timeScope.quickEndTimeEview.getUTCDateTime());
    validationReturnMap.validationErrorMsg = I18n.get('cmp_basic_begintime_endtime');
    validationReturnMap.validationResult = true;
    if (startDate && endDate && getDateObject(startDate) >= getDateObject(endDate)) {
      $('#quickBeginTimeEview').addClass('eviewDateTime_inputInvalid');
      $('#quickEndTimeEview').addClass('eviewDateTime_inputInvalid');
      validationReturnMap.validationResult = false;
    } else {
      $('#quickBeginTimeEview').removeClass('input-border-red eviewDateTime_inputInvalid');
      $('#quickEndTimeEview').removeClass('input-border-red eviewDateTime_inputInvalid');
      validationReturnMap.validationResult = true;
    }
    return validationReturnMap;
  };

  let getDateObject = function(dateStr) {
    if (dateStr) {
      let dateStrArr = dateStr.split(' ');
      let datePart = dateStrArr[0].split('-');
      let timePart = dateStrArr[1].split(':');
      return new Date(datePart[0], datePart[1], datePart[2], timePart[0], timePart[1], timePart[2]);
    }
    return 0;
  };

  $scope.getparam = function(url) {
    let obj = {};
    let str = url.split('?').length > 1 && url.split('?')[1].split('&');
    if (str) {
      for (let i = 0; i < str.length; i++) {
        let a = str[i].split('=');
        obj[a[0]] = a[1];
      }
    }
    return obj;
  };
  $scope.parseUrlParams = function() {
    let oriUrlParams = $scope.getparam(window.location.href);
    if (!oriUrlParams || !oriUrlParams.datasource) {
      return;
    }

    if (oriUrlParams.begintime && oriUrlParams.endtime) {
      let t1 = parseTimeParam(oriUrlParams.begintime);
      let t2 = parseTimeParam(oriUrlParams.endtime);
      if (t1 && t2) {
        $scope.Model.beginTime = t1;
        $scope.Model.endTime = t2;
      }
    }

    let presertFields = {};
    if (oriUrlParams.message) {
      presertFields.Message = oriUrlParams.message;
    }

    if (oriUrlParams.moduleip) {
      presertFields.ModuleIp = oriUrlParams.moduleip;
    }

    $scope.Model.urlParams = {
      dataSource: oriUrlParams.datasource,
      oriParams: oriUrlParams,
      fields: presertFields,
    };
  };

  // 配置图标弹框
  $scope.visualchoose = function() {
    if ($scope.timeField === null || $scope.timeField === undefined) {
      return;
    }
    let win = eview.widgets.Window.get('exceptionDialog');
    win = createWindow('exceptionDialog', I18n.get('cmp_logmatrix_discover_chart_config'), 400, 270, 'complex', '/dvlogmatrixwebsite/app/logretrieval/visualchoose.html');
    win.show();
    // 给窗口绑定事件，事件名及事件处理函数可以自定义。窗口中可通过jQuery的trigger方法来触发此事件，在本页面（父页面）监听到事件发生时就会调用事件处理函数。
    win.on('passdataEvent', EventHandler);
  };

  // 定义事件处理函数,其中的参数data为窗口传递过来的。
  function EventHandler(data) {
    eview.widgets.Window.get('exceptionDialog').dispose();
    // 获取窗口中设置的值
    if (data.isNew) {
      $scope.isMasklayer = true;
      let visual = $scope.buildVisualFromSearch();
      visual.type = data.openType;
      if (visual.type === 'Table') {
        $scope.isTablePage = true;
        $('#tableConfigPage').animate({
          right: '0%',
        }, 400);
        $rootScope.$broadcast('tableDetailIsMasklayer', visual, true);
      } else {
        $scope.isLinePage = true;
        $('#lineConfigPage').animate({
          right: '0%',
        }, 400);
        $rootScope.$broadcast('lineDetailIsMasklayer', visual, true);
      }
    }
  }

  // 界面选择索引模板标签/索引规则列表
  $scope.setIndexName = function(pattern) {
    // 初始化页面表格数据
    $scope.isReady = false;
    $scope.isInitOK = false;
    $scope.hasSearch = false;
    $scope.$Model.total = 0;
    // 切换模板时，清空过滤条件的值
    if ($scope.alarmId && $scope.$Model.alertConfig) {
      $scope.$Model.alertConfig.filterTerms = {};
      $scope.$Model.alertConfig.operatorFields = {};
    }
    // 保存到缓存中
    $scope.indexPattern = pattern;
    if ($scope.isSearchByTemplate) {
      setCookies('patternCookie', JSON.stringify(pattern), 365);
    }
    $scope.isIndexRuleMode = isNotEmpty(pattern.searchIndexPatternName);
    load();
    $scope.queryCustomByIndexName({
      dataSourceName: $scope.isIndexRuleMode ? undefined : pattern.indexName,
      solutionType: $scope.isIndexRuleMode ? undefined : pattern.solutionType,
      id: $scope.isSearchByTemplate ? pattern.id : undefined,
      searchIndexPatternName: pattern.searchIndexPatternName,
    }, null, $scope.isIndexRuleMode);
  };

  // 查询customeFileds
  $scope.queryCustomByIndexName = function(perPatternInfo, queryOptions, isIndexRuleMode) {
    // 设置索引名称，初始化查询和展示条件
    $scope.initCustomFieldParam(perPatternInfo);
    ajax('post', 'json',
      JSON.stringify({
        indexName: perPatternInfo.dataSourceName,
        solutionType: perPatternInfo.solutionType,
        id: perPatternInfo.id,
        searchIndexPatternName: perPatternInfo.searchIndexPatternName,
      }),
      '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryCustomByIndexName',
      function(data) {
        // fieldsList 作为媒介，与其他对象关联，以适应界面修改搜索条件时多处动态变化
        $scope.$Model.fieldsList = data;
        // templateFields 始终与查询出来的模板配置保存一致
        $scope.$Model.templateFields = angular.copy(data);
        $scope.$apply();
        ajax(
          'post', 'json',
          JSON.stringify({
            solutionType: isIndexRuleMode ? undefined : $scope.indexPattern.solutionType,
            searchIndexPatternName: isIndexRuleMode ? $scope.indexPattern.searchIndexPatternName : undefined,
          }),
          '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryAllMoInfos',
          function(data) {
            closeLoad();
            $scope.$Model.clusterInfos = data.clusterInfos;
            $scope.$Model.solutionInfos = data.solutionInfos;
            $scope.$Model.groupNames = data.groupNames;
            $scope.$Model.siteInfos = data.siteInfos;
            $scope.$Model.solutionTypes = data.solutionTypes;
            $scope.$Model.clusterTypes = data.clusterTypes;
            let fieldValues = null;
            if (queryOptions && queryOptions.fieldValues) {
              fieldValues = queryOptions.fieldValues;
            }

            // 处理日志检索模板的查询字段
            $scope.handleQueryIndexPatternResult(fieldValues);
            if (queryOptions && queryOptions.autoQuery || $scope.alarmId) {
              $scope.discoverTableParams.attr('cur-page', 1);
            }
            // 选择模板后，重新生成展示框高度
            // 设置左侧字段选择列的高度
            let height = $(window).height() - $('.discover-right-container').offset().top;
            $('#fieldDisplaySetting').css('height', height - 30);
            $('#fieldDisplayDefault').css('height', height - 30);
            $('.discover-right-container').css('height', height - 18);
            $scope.$apply();

            $scope.isReady = true;
            $scope.isInitOK = true;
          }, function() {
            closeLoad();
          });
      }, function() {
        closeLoad();
      });
  };

  $scope.initCustomFieldParam = function(perPatternInfo) {
    $scope.indexName = perPatternInfo.dataSourceName;
    $scope.searchIndexPatternName = perPatternInfo.searchIndexPatternName;
    $scope.allSearchFields = {};
    $scope.searchCondition = [];
    $scope.searchConditionItems = {};
    $scope.selectFieldList = [];
    $scope.displayedFieldsList = [];
    $scope.availableFieldList = [];
    $scope.hasTimeQuery = false;
    $scope.fieldList = [];
    $scope.timeField = null;
    $scope.sortField = null;
    $scope.sortFieldType = null;
    $scope.searchDefaultValue = {};
  };

  $scope.buildVisualFromSearch = function() {
    let visual = {};
    visual.filters = $scope.getFilterCondition();
    visual.operatorFields = $scope.getFilterOperator();
    visual.timeConfig = {};
    visual.timeConfig.timeField = $scope.timeField;
    visual.timeConfig.timeType = $('#quickTimeType').val();
    visual.timeConfig.beginTime = $('#beginTimeQuick').val();
    visual.timeConfig.endTime = $('#endTimeQuick').val();
    visual.index = $scope.indexName;
    visual.solutionType = $scope.indexPattern.solutionType;
    visual.searchIndexPatternName = $scope.indexPattern.searchIndexPatternName;
    return visual;
  };


  $scope.manageTemplate = function() {
    // 跳转到管理页面
    if ($scope.alarmId) {
      setCookies('alarmId', null, { expires: 365 });
    }

    parent.window.location.href = '/dvlogmatrixwebsite/index.html#/templateManage';
    parent.document.getElementById('workspaceContainer').src = '/dvlogmatrixwebsite/app/logretrieval/templateManage.html';
    parent.window.logmatrixSignal.emit('routerChange', 'templateManage');
  };

  $scope.queryConfig = function () {
    let configKeyList = [
      'logQueryMaxBucket',
    ];
    ajax('post', 'json', JSON.stringify(configKeyList), '/rest/dvlogmatrixwebsite/v1/dvlogmatrixconfigservice/queryConfig', (data) => {
      if ( data.logQueryMaxBucket) {
        $scope.logQueryMaxBucket = parseInt(data.logQueryMaxBucket);
      } else {
        $scope.logQueryMaxBucket = 100;
      }
    });
  };

  // 初始查询所有模板数据
  $scope.queryIndexPattern = function(isFromSaveDialog) {
    ajax('post', 'json',
      JSON.stringify({
        queryFields: null,
        pageModel: null,

      }),
      '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryIndexPatterns',
      function(data) {
        $scope.$Model.indexPatternList = data.indexPatterns;
        $scope.hasNoTemplate = !data.indexPatterns || !data.indexPatterns.length;
        $scope.$apply();
        if (isFromSaveDialog) {// 另存为模板后刷新列表
          $scope.$apply();
          return;
        }
        load();

        let tgtIndexPattern;
        let queryOptions;
        if ($scope.alarmId) { // 跳转场景
          tgtIndexPattern = findIndexPatternByLabelName($scope.$Model.indexPatternList, $scope.indexLabel);
          // 根据跳转的告警id查询告警，处理其过滤条件
          $scope.queryAlertById($scope.alarmId);
        } else {
          if (!data.indexPatterns || !data.indexPatterns.length) {
            // index pattern list is invalid, or empty
            if ($scope.$Model.urlParams) {
              $scope.notifyRunLogTemplateNotFound();
            }
            closeLoad();
            return;
          }

          if ($scope.$Model.urlParams) {
            tgtIndexPattern = findIndexPattern(data.indexPatterns, $scope.$Model.urlParams.dataSource);
            if (!tgtIndexPattern) {
              $scope.notifyRunLogTemplateNotFound();
              closeLoad();
              return;
            }
            queryOptions = {
              fieldValues: $scope.$Model.urlParams.fields,
              autoQuery: true,
            };
          } else if ($scope.hasCookie) {
            tgtIndexPattern = findIndexPatternByLabelName(data.indexPatterns, $scope.cookiePattern.indexLabel);
          }

          if (!tgtIndexPattern) {
            // not found specified index pattern, choose the first one
            tgtIndexPattern = $scope.$Model.indexPatternList[0];
          }
        }

        let findInCookie = ($scope.hasCookie && (tgtIndexPattern.indexLabel === $scope.cookiePattern.indexLabel));

        if (tgtIndexPattern && !findInCookie) {
          setCookies('patternCookie', JSON.stringify(tgtIndexPattern), 365);
        }

        $scope.indexPattern = tgtIndexPattern;
        $scope.isIndexRuleMode = isNotEmpty($scope.indexPattern.searchIndexPatternName);
        $scope.queryCustomByIndexName({
          dataSourceName: tgtIndexPattern.indexName,
          solutionType: tgtIndexPattern.solutionType,
          id: tgtIndexPattern.id,
          searchIndexPatternName: tgtIndexPattern.searchIndexPatternName,
        }, queryOptions, $scope.isIndexRuleMode);
      }, function() {
        $scope.hasNoTemplate = true;
        closeLoad();
      });
  };

  $scope.queryAlertById = function(alarmId) {
    ajax('POST', 'json', alarmId,
      '/rest/dvlogmatrixwebsite/v1/dvalertconfigservice/queryAlertConfigById',
      function(data) {
        $scope.$Model.alertConfig = data;
        $scope.$Model.LayerSearch = false;
        $scope.$apply();
      });
  };

  $scope.notifyRunLogTemplateNotFound = function() {
    alertTips($scope.getI18n('cmp.logmatrix.indexpattern.lost', [$scope.Model.urlParams.dataSource]), 'error');
  };

  $scope.alarmAssignments = function($Model, fieldsList, fieldLength) {
    let notSerachCount = 0;
    let filterTerms = $scope.$Model.alertConfig.filterTerms || {};
    let operatorFields = $scope.$Model.alertConfig.operatorFields || {};
    for (let key in filterTerms) {
      if (!Object.prototype.hasOwnProperty.call(filterTerms, key)) {
        continue;
      }
      let isSerach = false;
      for (let i = 0; i < fieldLength; i++) {
        let field = fieldsList.customFields[i];
        if (key === field.fieldName && field.queried) {
          isSerach = true;
          $scope.assignments($Model, i, key, fieldsList, filterTerms, operatorFields);
        }
      }
      if (!isSerach) {
        notSerachCount++;
      }
    }
    if (notSerachCount !== 0) {
      // 模板发生变更，提示。
      alertTips($scope.getI18n('cmp.logmatrix.template.change.tips', [$scope.$Model.alertConfig.alertName]), 'confirm');
    }
  };

  // 设置时间
  $scope.quickChooseTimeSearch = function(index, isQuick) {
    if (window.location.href.indexOf('AlarmId') !== -1) {
      $scope.quickEndTime = getTimeFromTimestamp($scope.postTime);
      $scope.quickBeginTime = getTimeFromTimestamp($scope.postTime - $scope.timeRange);
      $scope.showTime = $scope.quickBeginTime + '—' + $scope.quickEndTime;
      $scope.beginTime = $scope.quickBeginTime;
      $scope.endTime = $scope.quickEndTime;
      $scope.quickTimeType = '';
      $scope.quickChoose = false;
      $scope.timePanel = false;
      $('#beginTimeQuick').val($scope.quickBeginTime);
      $('#endTimeQuick').val($scope.quickEndTime);
      $('#quickTimeType').val($scope.quickTimeType);
      return;
    }
    $('#quickBeginTimeEview').removeClass('input-border-red eviewDateTime_inputInvalid');
    $('#quickEndTimeEview').removeClass('input-border-red eviewDateTime_inputInvalid');
    let timeScope = angular.element(document.getElementById('quickTime')).scope();
    timeScope.quickChoose = true;
    timeScope.quickChooseIndex = index;
    timeScope.timePanel = false;
    $('#quickTimeType').val(timeScope.quickChooseIndex);
    $('#beginTimeQuick').val('');
    $('#endTimeQuick').val('');
  };

  $scope.assignments = function($Model, i, key, fieldsList, filterTerms, operatorFields) {
    if (key === 'ClusterId') {
      for (let index in $scope.$Model.clusterInfos) {
        if (filterTerms[key].includes(index)) {
          fieldsList.customFields[i].value = $scope.$Model.clusterInfos[index];
        }
      }
    } else if (key === 'SolutionId') {
      for (let index in $scope.$Model.solutionInfos) {
        if (filterTerms[key].includes(index)) {
          fieldsList.customFields[i].value = $scope.$Model.solutionInfos[index];
        }
      }
    } else {
      fieldsList.customFields[i].value = filterTerms[key];
    }
    fieldsList.customFields[i].fieldOperator = operatorFields[key] || DEFAULT_OPERATOR_NA;
  };

  $scope.handleQueryIndexPatternResult = function(presetFieldValues) {
    let fieldsList = $scope.$Model.fieldsList;

    let fieldLength = fieldsList.customFields.length;
    // 如果告警跳转，给搜索条件预设值
    if ($scope.alarmId) {
      // 告警跳转情况下，清除搜索模板的默认值
      for (let j = 0; j < 10; j++) {
        fieldsList.customFields[j].value = '';
      }
      $scope.alarmAssignments($scope.$Model, fieldsList, fieldLength);
    }

    if (fieldsList.timeField) {
      $scope.timeField = fieldsList.timeField;
      $scope.hasTimeQuery = true;
      // 将默认排序字段设置为时间字段
      $scope.sortField = fieldsList.timeField;
    }
    // 目前只有标准格式的运行日志支持钻取
    $scope.supportDrill = $scope
      .judgeIfSupportDrill(fieldsList.customFields);
    for (let i = 0; i < fieldLength; i++) {
      let field = fieldsList.customFields[i];
      let fieldPara = {};
      fieldPara.name = field.fieldName;
      fieldPara.label = field.fieldLabel;
      fieldPara.displayed = field.displayed;
      $scope.fieldList.push(fieldPara);
      if (field.fieldName === 'Time') {
        if (!$scope.sortField) {
          $scope.sortField = 'Time';
          $scope.orderType = 'desc';
        }
      }
      if (presetFieldValues && Object.prototype.hasOwnProperty.call(presetFieldValues, field.fieldName)) {
        field.value = presetFieldValues[field.fieldName];
        field.queried = true;
      }

      // 查询条件
      $scope.handleQueryFields(field);
      // 展示列
      if (field.displayed) {
        $scope.selectFieldList.push(field);
        $scope.displayedFieldsList.push(field.fieldName);
      } else {
        $scope.availableFieldList.push(field);
      }
    }

    // 字段排序
    $scope.orderSelectedOption();
    $scope.handleQueryResultHtml();
    $scope.handleQueryLayerResultHtml();
    // 关闭模板详情后清空了搜索结果，图表数据也一并清理
    $scope.clearChartData();
  };

  $scope.handleQueryLayerResultHtml = function() {
    if ($scope.discoverLayerTableParams) {
      $scope.discoverLayerTableParams.destroy();
      $('#discoverLayerTable').empty();
    }

    let html = '<eview:table id="discoverLayerTableParams" enable-pagination="false" enable-tooltip="true" cur-page="1"';
    html += ' scroll="false" width="100%" listeners="discoverLayerListeners">';
    html += '<eview:table-column id="expandLayerRowId" width="4%" column-type="expandrow" sortable="false"' +
      ' cell-alignment="center" fixed="true"></eview:table-column>';
    html += '<eview:table-column id="layerEventWithSolutionName" caption="' + I18n.get('cmp.logmatrix.search.event.module') +
      '" enable-tooltip="true" sortable="false" display="true"></eview:table-column>';
    html += '<eview:table-column id="layerEvent" caption="' + I18n.get('cmp.logmatrix.search.event.module') +
      '" enable-tooltip="true" sortable="false" display="false"></eview:table-column>';
    let $target = $('#discoverLayerTable');
    $target.append(html);
    eview.compileElement($target, $target.scope());
    $scope.discoverLayerTableParams.attr('dataset', []);
  };

  $scope.handleQueryResultHtml = function() {
    if ($scope.discoverTableParams) {
      $scope.discoverTableParams.destroy();
      $('#discoverTable').empty();
    }

    let html = '<eview:table id="discoverTableParams"  enable-pagination="true" enable-tooltip="true" cur-page="1"';
    html += ' display-length="10" pagination-handler="discoverPaginationHandler" display-length-options="[10,25, 50, 100]"';
    html += ' scroll="false" width="100%" listeners="discoverListeners" create-handler="discoverCreateHandler">';
    html += '<eview:table-column id="expandRowId" width="40px" column-type="expandrow" sortable="false"' +
      ' cell-alignment="center" fixed="true"></eview:table-column>';
    $.each($scope.$Model.fieldsList.customFields, function(index, item) {
      html += '<eview:table-column id="' + item.fieldName + '" caption="' + (item.fieldLabel || item.fieldName) +
        '" enable-tooltip="true" display="' + item.displayed + '"></eview:table-column>';
    });
    if ($scope.selectFieldList.length === 0) {
      html += '<eview:table-column id="event" caption="' + I18n.get('cmp.logmatrix.search.event') +
        '" enable-tooltip="true" sortable="false" display="true"></eview:table-column>';
    } else {
      html += '<eview:table-column id="event" caption="' + I18n.get('cmp.logmatrix.search.event') +
        '" enable-tooltip="true" sortable="false" display="false"></eview:table-column>';
    }
    html += '<eview:table-column id="operation"  width="60px" caption="' + I18n.get('cmp.logmatrix.visual.operation') +
      '" custom-editor="customOperationEditor" enable-tooltip="true" column-type="custom"' +
      ' sortable="false" ></eview:table-column></eview:table>';

    let $target = $('#discoverTable');
    $target.append(html);
    eview.compileElement($target, $target.scope());
    $scope.discoverTableParams.attr('dataset', []);
    $scope.discoverTableParams.attr('total-records', 0);
  };

  $scope.handleQueryFields = function(field) {
    field.afterPresetValue = angular.copy(field.presetValue);
    $scope.searchDefaultValue[field.fieldName] = field.value;
    $scope.initSearchConditionItem(field);
    if (field.queried) {
      if (field.fieldName === $scope.timeField) {

      } else if (field.fieldName === 'SiteName') {
        for (let key in $scope.$Model.siteInfos) {
          if (!Object.prototype.hasOwnProperty.call($scope.$Model.siteInfos, key)) {
            continue;
          }
          let siteName = $scope.$Model.siteInfos[key];
          field.afterPresetValue.push(siteName);
          if ($scope.searchConditionItems[field.fieldName].endIndex < SELECTION_SIZE) {
            $scope.searchConditionItems[field.fieldName].value.push(siteName);
            $scope.searchConditionItems[field.fieldName].endIndex++;
          }
        }
        $scope.searchCondition.push(field);
        $scope.allSearchFields[field.fieldName] = field.fieldName;
      } else if (field.fieldName === 'GroupName') {
        for (let key in $scope.$Model.groupNames) {
          if (!Object.prototype.hasOwnProperty.call($scope.$Model.groupNames, key)) {
            continue;
          }
          let groupName = $scope.$Model.groupNames[key];
          field.afterPresetValue.push(groupName);
          if ($scope.searchConditionItems[field.fieldName].endIndex < SELECTION_SIZE) {
            $scope.searchConditionItems[field.fieldName].value.push(groupName);
            $scope.searchConditionItems[field.fieldName].endIndex++;
          }
        }
        $scope.searchCondition.push(field);
        $scope.allSearchFields[field.fieldName] = field.fieldName;
      } else if (field.fieldName === 'ClusterId') {
        for (let key in $scope.$Model.clusterInfos) {
          if (!Object.prototype.hasOwnProperty.call($scope.$Model.clusterInfos, key)) {
            continue;
          }
          let clustName = $scope.$Model.clusterInfos[key];
          field.afterPresetValue.push(clustName);
          if ($scope.searchConditionItems[field.fieldName].endIndex < SELECTION_SIZE) {
            $scope.searchConditionItems[field.fieldName].value.push(clustName);
            $scope.searchConditionItems[field.fieldName].endIndex++;
          }
        }
        $scope.searchCondition.push(field);
      } else if (field.fieldName === 'SolutionId') {
        for (let key in $scope.$Model.solutionInfos) {
          if (!Object.prototype.hasOwnProperty.call($scope.$Model.solutionInfos, key)) {
            continue;
          }
          let solutionName = $scope.$Model.solutionInfos[key];
          field.afterPresetValue.push(solutionName);
          if ($scope.searchConditionItems[field.fieldName].endIndex < SELECTION_SIZE) {
            $scope.searchConditionItems[field.fieldName].value.push(solutionName);
            $scope.searchConditionItems[field.fieldName].endIndex++;
          }
        }
        $scope.searchCondition.push(field);
        $scope.allSearchFields[field.fieldName] = field.fieldName;
      } else if (field.fieldName === 'SolutionType') {
        for (let key in $scope.$Model.solutionTypes) {
          if (!Object.prototype.hasOwnProperty.call($scope.$Model.solutionTypes, key)) {
            continue;
          }
          let solutionType = $scope.$Model.solutionTypes[key];
          field.afterPresetValue.push(solutionType);
          if ($scope.searchConditionItems[field.fieldName].endIndex < SELECTION_SIZE) {
            $scope.searchConditionItems[field.fieldName].value.push(solutionType);
            $scope.searchConditionItems[field.fieldName].endIndex++;
          }
        }
        $scope.searchCondition.push(field);
      } else {
        $scope.searchCondition.push(field);
      }
    }
  };

  $scope.initSearchConditionItem = function (field) {
    if (field.presetValue.length <= SELECTION_SIZE) {
      $scope.searchConditionItems[field.fieldName] = {
        value: angular.copy(field.presetValue),
        startIndex: 0,
        endIndex: field.presetValue.length,
      }
    } else {
      $scope.searchConditionItems[field.fieldName] = {
        value: angular.copy(field.presetValue).slice(0, SELECTION_SIZE),
        startIndex: 0,
        endIndex: SELECTION_SIZE,
      }
    }
  };

  $scope.orderSelectedOption = function() {
    let object = [];
    let siteName = {};
    let groupName = {};
    let solutionId = {};
    let clusterId = {};
    for (let i = 0; i < $scope.searchCondition.length; i++) {
      if ($scope.searchCondition[i].fieldName === 'SiteName') {
        siteName = $scope.searchCondition[i];
        object.push(i);
      } else if ($scope.searchCondition[i].fieldName === 'GroupName') {
        groupName = $scope.searchCondition[i];
        object.push(i);
      } else if ($scope.searchCondition[i].fieldName === 'SolutionId') {
        solutionId = $scope.searchCondition[i];
        object.push(i);
      } else if ($scope.searchCondition[i].fieldName === 'ClusterId') {
        clusterId = $scope.searchCondition[i];
        object.push(i);
      }
    }
    for (let j = object.length - 1; j >= 0; j--) {
      $scope.searchCondition.splice(object[j], 1);
    }
    if (clusterId.fieldName === 'ClusterId') {
      $scope.searchCondition.unshift(clusterId);
    }
    if (solutionId.fieldName === 'SolutionId') {
      $scope.searchCondition.unshift(solutionId);
    }
    if (groupName.fieldName === 'GroupName') {
      $scope.searchCondition.unshift(groupName);
    }
    if (siteName.fieldName === 'SiteName') {
      $scope.searchCondition.unshift(siteName);
    }
  };

  $scope.closeShowTips = function() {
    setTimeout(function() {
      if ($scope.tip) {
        $scope.tip.hide();
      }
    }, 800);
  };

  $scope.judgeIfSupportDrill = function(fieldsList) {
    let tmpList = [];
    for (let k = 0; k < fieldsList.length; k++) {
      let field = fieldsList[k];
      if (field.analyzed && field.fieldName === 'SerialNo') {
        continue;
      }
      if (field.indexed || field.fieldName === 'Message') {
        tmpList.push(field.fieldName);
      }
    }
    // 如果以下字段不存在不支持日志钻取
    let isLogDrillField = $.inArray('Time', tmpList) === -1 ||
        $.inArray('ModuleIp', tmpList) === -1 ||
        $.inArray('FilePath', tmpList) === -1 ||
        $.inArray('SerialNo', tmpList) === -1 ||
        $.inArray('Message', tmpList) === -1 ||
        $.inArray('FileName', tmpList) === -1;
    return !isLogDrillField;
  };

  $scope.searchLog = function() {
    $scope.$Model.LayerSearch === false ? $scope.discoverTableParams.attr('cur-page', 1) : $scope.queryLayerLog();
  };

  // 查询分层日志
  $scope.queryLayerLog = function() {
    if (!$scope.beforeQueryLog()) {
      return;
    }
    $scope.chartData.true.displayAggRule = 'auto';
    $scope.generateBarChart();

    // 查询条件
    let condition = $scope.getFilterCondition();
    let fixedquerycondition = {
      beginTime: $scope.searchBeginTime,
      endTime: $scope.searchEndTime,
      indexName: $scope.indexName,
      timeField: $scope.timeField,
      solutionType: $scope.indexPattern.solutionType,
      searchIndexPatternName: $scope.indexPattern.searchIndexPatternName,
    };
    let exactSearch = $scope.$Model.ExactSearch;
    ajax('post', 'json',
      JSON.stringify({
        fixedCondition: fixedquerycondition,
        queryFields: condition,
        operatorFields: $scope.getFilterOperator(),
        pipeLine: 'layerByModule',
        pageModel: {
          pageIndex: 1,
          rowsCount: 10,
        },
        exactSearch,
      }),
      '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryLayerLog',
      function(data) {
        let str = JSON.stringify(data).replace(/&lt;/g, '<').replace(/&gt;/g, '>');
        let result = JSON.parse(str);
        let logs = [];
        for (let i = 0; i < result.length; i++) {
          let log = {};
          log.layerEvent = result[i].fieldValue.replaceAll('||', ' || ');
          let event = result[i].fieldValue.replaceAll('||', ' || ').split(' || ');
          event[2] = $scope.$Model.solutionInfos[event[2]] === undefined ? event[2] : $scope.$Model.solutionInfos[event[2]];
          log.layerEventWithSolutionName = event.join(' || ');
          log.expandLayerRowId = {};
          logs.push(log);
        }
        $scope.discoverLayerTableParams.attr('dataset', logs);
        $scope.$apply();
        closeLoad();
      });
  };

  $scope.beforeQueryLog = function() {
    // 界面尚未初始化成功，直接返回
    if (!$scope.isInitOK) {
      return false;
    }
    if (($scope.indexName === undefined && $scope.searchIndexPatternName === undefined) || $scope.searchCondition === undefined) {
      return false;
    }
    if ($scope.searchCondition) {
      for (let i = 0; i < $scope.searchCondition.length; i++) {
        if ($scope.dataTypeValidCheck(['checkLength'], 'searchMessage_' + i, $scope.searchCondition[i].value, null, 256)) {
          $scope.closeShowTips();
          return false;
        }
      }
    }
    if ($scope.hasTimeQuery) {
      // 时间补毫秒
      if (!$('#quickTimeType').val()) {
        $scope.searchBeginTime = getUTCTime($('#beginTimeQuick').val()) + '.000';
        $scope.searchEndTime = getUTCTime($('#endTimeQuick').val()) + '.999';
      } else {
        $scope.getTimeDuration($('#quickTimeType').val());
      }
    }
    // table
    // 保证每次查询获得的结果不包含历史记录，同时保证切换索引模板的时候表格数据清空
    $scope.$Model.logInfo = [];
    if (!$scope.isReady) {
      $scope.discoverTableParams.attr('dataset', []);
      $scope.discoverTableParams.attr('total-records', 0);
      return false;
    }

    if ($scope.searchBeginTime === null || $scope.searchEndTime === null) {
      return false;
    }

    // 加载等待
    load();
    return true;
  };

  // 查询日志
  $scope.queryLog = function(pageIndex, pageSize) {
    if (!$scope.beforeQueryLog()) {
      return;
    }
    $scope.chartData.false.displayAggRule = 'auto';
    $scope.generateBarChart();

    // 查询条件
    let sortField = $scope.sortField;
    let customFields = $scope.$Model.fieldsList.customFields;
    let row;
    for (let i = 0; i < customFields.length; i++) {
      row = customFields[i];
      if (row.fieldName === sortField) {
        $scope.sortFieldType = row.fieldType;
        break;
      }
    }
    let condition = $scope.getFilterCondition();
    let fixedquerycondition = {
      beginTime: $scope.searchBeginTime,
      endTime: $scope.searchEndTime,
      indexName: $scope.indexName,
      timeField: $scope.timeField,
      solutionType: $scope.indexPattern.solutionType,
      searchIndexPatternName: $scope.indexPattern.searchIndexPatternName,
    };
    let exactSearch = $scope.$Model.ExactSearch;
    ajax('post', 'json',
      JSON.stringify({
        fixedCondition: fixedquerycondition,
        queryFields: condition,
        operatorFields: $scope.getFilterOperator(),
        pageModel: {
          pageIndex: pageIndex,
          rowsCount: pageSize,
        },
        sortModel: {
          sortField: $scope.sortField,
          sortFieldType: $scope.sortFieldType,
          orderType: $scope.orderType,
        },
        exactSearch,
      }),
      '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryLog',
      function(data) {
        let str = JSON.stringify(data).replace(/&lt;/g, '<').replace(/&gt;/g, '>');
        $scope.$Model.searchResult = JSON.parse(str);
        $scope.$Model.count = $scope.$Model.searchResult.count;
        $scope.$Model.total = $scope.$Model.searchResult.total;
        for (let i = 0; i < $scope.$Model.searchResult.logs.length; i++) {
          let log = JSON.parse($scope.$Model.searchResult.logs[i]);
          log.Time = getLocalTime(log.Time);
          $scope.handleDisplayFields(log);
          log.event = trustHtml(log);
          log.expandRowId = {};
          for (let index in $scope.searchCondition) {
            if (!Object.prototype.hasOwnProperty.call($scope.searchCondition, index)) {
              continue;
            }
            let item = $scope.searchCondition[index];
            if (item.fieldName === $scope.timeField) {
              log[item.fieldName] = cmpLocalTime(log[item.fieldName]);
            } else if (item.fieldName === 'Message') {
              log[item.fieldName] = trustHtml(log[item.fieldName]);
            } else {
              log[item.fieldName] = trustHtml(log[item.fieldName]);
            }
          }
          $scope.$Model.logInfo.push(log);
        }
        $scope.discoverTableParams.attr('dataset', $scope.$Model.logInfo);
        $scope.discoverTableParams.attr('enable-tooltip', true);
        $scope.discoverTableParams.attr('total-records', $scope.$Model.count || 0);
        $scope.$apply();

        // 记录第一页第一条日志时间
        // 是否以时间字段排序
        if (pageIndex === 1 && $scope.sortField && $scope.timeField && $scope.sortField === $scope.timeField && $scope.$Model.logInfo.length > 0) {
          // 正序
          if ($scope.orderType === 'asc') {
            $scope.downloadBeginTime = getUTCTime($scope.$Model.logInfo[0][$scope.sortField]) + '.' + new Date($scope.$Model.logInfo[0][$scope.sortField]).getMilliseconds();
            $scope.downloadEndTime = $scope.searchEndTime;
          } else {
            $scope.downloadBeginTime = $scope.searchBeginTime;
            $scope.downloadEndTime = getUTCTime($scope.$Model.logInfo[0][$scope.sortField]) + '.' + new Date($scope.$Model.logInfo[0][$scope.sortField]).getMilliseconds();
          }
        } else {
          $scope.downloadBeginTime = $scope.searchBeginTime;
          $scope.downloadEndTime = $scope.searchEndTime;
        }
        $('.ng-table-pagination>li:eq(1)').hide();
        $scope.hasSearch = true;
        $scope.$apply();
        closeLoad();
      });
  };

  $scope.handleDisplayFields = function (log) {
    if (log.ClusterId) {
      // 替换高亮字段
      let clusterId = log.ClusterId.replace(/cmphighlightbegin/g, '').replace(/cmphighlightend/g, '');
      if ($scope.$Model.clusterInfos[clusterId]) {
        log.ClusterId = log.ClusterId.replace(clusterId, $scope.$Model.clusterInfos[clusterId]);
      }
    }
    if (log.ClusterType) {
      let clusterType = log.ClusterType.replace(/cmphighlightbegin/g, '').replace(/cmphighlightend/g, '');
      if ($scope.$Model.clusterTypes[clusterType]) {
        log.ClusterType = log.ClusterType.replace(clusterType, $scope.$Model.clusterTypes[clusterType]);
      }
    }
    if (log.SolutionId) {
      let solutionId = log.SolutionId.replace(/cmphighlightbegin/g, '').replace(/cmphighlightend/g, '');
      if ($scope.$Model.solutionInfos[solutionId]) {
        log.SolutionId = log.SolutionId.replace(solutionId, $scope.$Model.solutionInfos[solutionId]);
      }
    }
    if (log.SolutionType) {
      let solutionType = log.SolutionType.replace(/cmphighlightbegin/g, '').replace(/cmphighlightend/g, '');
      if ($scope.$Model.solutionTypes[solutionType]) {
        log.SolutionType = log.SolutionType.replace(solutionType, $scope.$Model.solutionTypes[solutionType]);
      }
    }
    if (log.Message) {
      // 处理高亮字段
      let position = log.Message.indexOf('cmphighlightbegin', 25);
      if (position !== -1) {
        log.aheadMessage = '...';
        log.behindMessage = log.Message.substr(position - 25);
      } else {
        log.aheadMessage = '';
        log.behindMessage = log.Message;
      }
    }
  };

  $scope.resetFilter = function () {
    for (let i = 0; i < $scope.searchCondition.length; i++) {
      let searchField = $scope.searchCondition[i];
      let fieldName = searchField.fieldName;
      if ($scope.searchDefaultValue[fieldName]) {
        searchField.value = $scope.searchDefaultValue[fieldName];
      } else {
        searchField.value = null;
      }
    }
    $scope.quickChooseTimeSearch(0, true);
  };

  $scope.handleBlankChange = function(fieldName, fieldValue) {
    // 条件非 SiteName 或GroupName 或SolutionId时不处理
    if (!$scope.allSearchFields[fieldName]) {
      return;
    }
    // 当条件为空时，显示全部
    if (fieldValue === '') {
      // 当清空SiteName时，清空 GroupName，SolutionId，ClusterId
      if (fieldName === 'SiteName' || fieldName === 'GroupName' || fieldName === 'SolutionId') {
        handleBlankValue(fieldName, '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryAllMoInfos');
      }
    }
  };
  let handleBlankValue = function(fieldName, url) {
    ajax(
      'post', 'json',
      JSON.stringify({
        solutionType: $scope.isIndexRuleMode ? undefined : $scope.indexPattern.solutionType,
        searchIndexPatternName: $scope.isIndexRuleMode ? $scope.indexPattern.searchIndexPatternName : undefined,
      }), url,
      function(data) {
        if (fieldName === 'SiteName') {
          $scope.handleQuerySelectOptions(data, fieldName);
          $scope.handleQuerySelectOptions(data, 'GroupName');
          $scope.handleQuerySelectOptions(data, 'SolutionId');
        } else if (fieldName === 'GroupName') {
          $scope.handleQuerySelectOptions(data, fieldName);
          $scope.handleQuerySelectOptions(data, 'SolutionId');
        } else if (fieldName === 'SolutionId') {
          $scope.handleQuerySelectOptions(data, fieldName);
        }
      });
  };

  $scope.setSearchConditionValue = function(fieldName, fieldValue) {
    for (let i = 0; i < $scope.searchCondition.length; i++) {
      if ($scope.searchCondition[i].fieldName === fieldName) {
        $scope.searchCondition[i].value = fieldValue;
        $scope.$apply();
        break;
      }
    }
  }

  // 初始化下拉框值
  $scope.queryAllSelectInfos = function(fieldName, fieldValue) {
    // 条件非 SiteName 或GroupName 或SolutionId时不处理
    if (!$scope.allSearchFields[fieldName]) {
      return;
    }

    if (fieldName === 'SiteName') {
      if (fieldValue === 'DefaultSite') {
        $scope.handleQuerySelectOptions({ groupNames: { DefaultGroup: 'DefaultGroup' } }, fieldName);
      } else {
        // 如搜索条件存在分组、solutionID、clusterId,则置空搜索条件值
        // 根据所选site查询所有分组
        ajax('post', 'json',
          JSON.stringify({
            solutionType: $scope.isIndexRuleMode ? undefined : $scope.indexPattern.solutionType,
            searchIndexPatternName: $scope.isIndexRuleMode ? $scope.indexPattern.searchIndexPatternName : undefined,
            siteName: fieldValue,
          }),
          '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryGroupNameBySiteName',
          function(data) {
            $scope.handleQuerySelectOptions(data, fieldName);
          });
      }
    } else if (fieldName === 'GroupName') {
      ajax('post', 'json',
        JSON.stringify({
          solutionType: $scope.isIndexRuleMode ? undefined : $scope.indexPattern.solutionType,
          searchIndexPatternName: $scope.isIndexRuleMode ? $scope.indexPattern.searchIndexPatternName : undefined,
          groupName: fieldValue,
        }),
        '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/querySolutionIdsByGroupName',
        function(data) {
          $scope.handleQuerySelectOptions(data, fieldName);
        });
    } else if (fieldName === 'SolutionId') {
      let solutionId = '';
      for (let key in $scope.$Model.solutionInfos) {
        if (fieldValue === $scope.$Model.solutionInfos[key]) {
          solutionId = key;
        }
      }
      ajax('post', 'json',
        JSON.stringify({
          solutionType: $scope.isIndexRuleMode ? undefined : $scope.indexPattern.solutionType,
          searchIndexPatternName: $scope.isIndexRuleMode ? $scope.indexPattern.searchIndexPatternName : undefined,
          solutionId,
        }),
        '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryClusterIdsBySolutionId',
        function(data) {
          $scope.handleQuerySelectOptions(data, fieldName);
        });
    }
  };

  $scope.handleQuerySelectOptions = function(data, fieldName) {
    if (fieldName === 'SiteName') {
      for (let i = 0; i < $scope.searchCondition.length; i++) {
        if ($scope.searchCondition[i].fieldName === 'GroupName') {
          $scope.commonHandleSelectOptions(i, data.groupNames);
        }
      }
    } else if (fieldName === 'GroupName') {
      for (let i = 0; i < $scope.searchCondition.length; i++) {
        if ($scope.searchCondition[i].fieldName === 'SolutionId') {
          $scope.commonHandleSelectOptions(i, data.solutionInfos);
        }
      }
    } else if (fieldName === 'SolutionId') {
      for (let i = 0; i < $scope.searchCondition.length; i++) {
        if ($scope.searchCondition[i].fieldName === 'ClusterId') {
          $scope.commonHandleSelectOptions(i, data.clusterInfos);
        }
      }
    }
    $scope.$apply();
  };

  // 级联查询
  $scope.commonHandleSelectOptions = function(index, data) {
    $scope.searchCondition[index].afterPresetValue = [];
    $scope.searchCondition[index].value = '';
    for (let key in data) {
      if (!Object.prototype.hasOwnProperty.call(data, key)) {
        continue;
      }
      $scope.searchCondition[index].afterPresetValue.push(data[key]);
      $scope.searchCondition[index].isCascadingQuery = true;
    }
  };

  $scope.discoverPaginationHandler = function(event) {
    pageSize = event.displayLength;
    pageIndex = event.currentPage;
    $scope.queryLog(pageIndex, pageSize);
  };

  $scope.discoverLayerListeners = {
    'expand-handler': function(event) {
      let $target = $('#' + event.expandDivId);
      if ($scope.discoverFileTableParams) {
        $scope.discoverFileTableParams.destroy();
        $target.empty();
      }

      // 在此处添加行展开的内容
      let html = '<eview:table id="discoverFileTableParams" style="padding: 1% 1.5%" enable-pagination="false" enable-tooltip="true"';
      html += ' scroll="false" width="97%" listeners="discoverFileListeners">';
      html += '<eview:table-column id="checkboxRowId" width="4%" column-type="checkbox" sortable="false"' +
        ' cell-alignment="center" fixed="true"></eview:table-column>';
      html += '<eview:table-column id="layerEvent" caption="' + I18n.get('cmp.logmatrix.search.event.file') +
        '" enable-tooltip="true" sortable="false" display="true"></eview:table-column>';
      html += '<eview:table-column id="operation" width="6%" caption="' + I18n.get('cmp.logmatrix.visual.operation') +
        '" custom-editor="customOperationEditor" enable-tooltip="true" column-type="custom"' +
        ' sortable="false" ></eview:table-column></eview:table>';
      $target.append(html);
      // 启动标签编译，wrap_content_chr为dom节点的id
      eview.compileElement($('#discoverLayerTable'), $('#discoverLayerTable').scope());

      let condition = $scope.getFilterCondition();
      condition.layerByModule = event.rowData.layerEvent.replaceAll(' || ', '||');

      if ($scope.dataTypeValidCheck(['checkLength'], 'visualTitle_Line', condition.layerByModule, null, 256)) {
        alertTips($scope.getI18n('cmp_logmatrix_search_fieldsizelimit', 'error'));
        return;
      }

      let fixedquerycondition = {
        beginTime: $scope.searchBeginTime,
        endTime: $scope.searchEndTime,
        indexName: $scope.indexName,
        timeField: $scope.timeField,
        solutionType: $scope.indexPattern.solutionType,
        searchIndexPatternName: $scope.indexPattern.searchIndexPatternName,
      };
      let exactSearch = $scope.$Model.ExactSearch;
      ajax('post', 'json',
        JSON.stringify({
          fixedCondition: fixedquerycondition,
          queryFields: condition,
          operatorFields: $scope.getFilterOperator(),
          pipeLine: 'layerByFile',
          pageModel: {
            pageIndex: 1,
            rowsCount: 10,
          },
          exactSearch,
        }),
        '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryLayerLog',
        function(data) {
          let str = JSON.stringify(data).replace(/&lt;/g, '<').replace(/&gt;/g, '>');
          let result = JSON.parse(str);
          let logs = [];
          for (let i = 0; i < result.length; i++) {
            let log = {};
            log.layerEvent = result[i].fieldValue.replaceAll('||', ' || ');
            logs.push(log);
          }
          $scope.discoverFileTableParams.attr('dataset', logs);
          $scope.$apply();
        });
    },
  };

  $scope.discoverListeners = {
    'expand-handler': function(event) {
      $scope.row = event.rowData;
      // 在此处添加行展开的内容
      let html = '<div id="expandrow_div"><ul class="ngtable-sub-list">';

      for (let key in $scope.row) {
        if (Object.prototype.hasOwnProperty.call($scope.row, key)) {
          for (let i = 0; i < $scope.fieldList.length; i++) {
            let fieldItem = $scope.fieldList[i];
            if (key === fieldItem.name) {// 日志字段列表和模板字段列表取交集展示
              let htmlText = $scope.row[fieldItem.name] || '';
              if (fieldItem.name !== $scope.timeField) {
                if (htmlText) {
                  if (typeof (htmlText) === 'object') {
                    htmlText = htmlText.$$unwrapTrustedValue();
                  }
                  htmlText = htmlText.substring(0, 50000);
                }
              }
              html += '<li>';
              html += '<span title="' + (fieldItem.label || fieldItem.name) + '" >' + (fieldItem.label || fieldItem.name) + ':</span>';
              // Message 字段内容较多时以滚动框的形式展示
              if (fieldItem.name === 'Message') {
                html += '<div><pre>' + htmlText + '</pre></div></li>';
              } else {
                html += '<pre>' + htmlText + '</pre></li>';
              }
              break;
            }
          }
        }
      }


      html += '</ul></div>';
      $('#' + event.expandDivId).append(html);

      // 启动标签编译，wrap_content_chr为dom节点的id

      let $target = $('#expandrow_div');
      eview.compileElement($target, $target.scope());
    },
  };

  function trustHtml(input) {
    let workParam = input;
    if (typeof workParam === 'object' || typeof workParam === 'number') {
      workParam = JSON.stringify(workParam);
    }
    let REGX_HTML_ENCODE = /"|&|'|<|>|[\x00-\x20]|[\x7F-\xFF]|[\u0100-\u2700]/g;
    workParam = (workParam !== undefined) ? workParam : '';
    workParam = workParam.replace(REGX_HTML_ENCODE,
      function($0) {
        let char = $0.charCodeAt(0);
        let arr = ['&#'];
        char = (char === 0x20) ? 0xA0 : char;
        arr.push(char);
        arr.push(';');
        return arr.join('');
      });
    return $sce.trustAsHtml(workParam.replace(/cmphighlightbegin/g, '<span class=\'highlight\'>').replace(/cmphighlightend/g, '</span>'));
  }

  // 折线图使用
  $scope.getTimeDurationNotUTC = function(quickChooseIndex) {
    $scope.searchEndTimeNotUTC = getDateTime(new Date().getTime()) + '.000';
    switch (quickChooseIndex) {
      case '0':
        $scope.searchBeginTimeNotUTC = getDateTime(new Date().getTime() - 30 * 60 * 1000);
        break;
      case '1':
        $scope.searchBeginTimeNotUTC = getDateTime(new Date().getTime() - 60 * 60 * 1000);
        break;
      case '2':
        $scope.searchBeginTimeNotUTC = getDateTime(new Date().getTime() - 6 * 60 * 60 * 1000);
        break;
      case '3':
        $scope.searchBeginTimeNotUTC = getDateTime(new Date().getTime() - 12 * 60 * 60 * 1000);
        break;
      case '4':
        $scope.searchBeginTimeNotUTC = getDateTime(new Date().getTime() - 24 * 60 * 60 * 1000);
        break;
      default:
        break;
    }
    $scope.searchBeginTimeNotUTC = $scope.searchBeginTimeNotUTC + '.999';
  };

  // 生成时间
  $scope.getTimeDuration = function(quickChooseIndex) {
    $scope.searchEndTime = getUTCTime(getDateTime(new Date().getTime())) + '.000';
    switch (quickChooseIndex) {
      case '0':
        $scope.searchBeginTime = getDateTime(new Date().getTime() - 30 * 60 * 1000);
        break;
      case '1':
        $scope.searchBeginTime = getDateTime(new Date().getTime() - 60 * 60 * 1000);
        break;
      case '2':
        $scope.searchBeginTime = getDateTime(new Date().getTime() - 6 * 60 * 60 * 1000);
        break;
      case '3':
        $scope.searchBeginTime = getDateTime(new Date().getTime() - 12 * 60 * 60 * 1000);
        break;
      case '4':
        $scope.searchBeginTime = getDateTime(new Date().getTime() - 24 * 60 * 60 * 1000);
        break;
      default:
        break;
    }
    $scope.searchBeginTime = getUTCTime($scope.searchBeginTime) + '.999';
  };

  $scope.customOperationEditor = function(event) {
    if ($scope.supportDrill) {
      let result = '<div class=\'df-trace-log\' style=\'display:block!important\' onclick=\'showDrillLog("' +
        event.rowData.rowid + '")\' title=\'' + I18n.get('cmp.logmatrix.search.drilllog') + '\'></div>';
      return result;
    } else {
      return null;
    }
  };

  // 展示配置页面
  $scope.showConfigPage = function() {
    $scope.isMasklayer = true;
    $scope.isConfigPage = true;
    $('#configPage').animate({
      right: '0%',
    }, 400);
    $rootScope.$broadcast('settingDetailIsMasklayer', $scope.indexPattern);
  };
  // 关闭配置页
  $scope.closeConfigPage = function() {
    $scope.isMasklayer = false;
    $scope.isConfigPage = false;
    $scope.hasSearch = false;
    $('#configPage').animate({
      right: '-100%',
    }, 400);
    $scope.closeShowTips();
    if ($scope.validInputArr) {
      for (let i = 0; i < $scope.validInputArr.length; i++) {
        $('#' + $scope.validInputArr[i].id).removeClass('input-border-red eviewDateTime_inputInvalid');
      }
    }
    // 刷新检索页面，保证不保留已被删除的信息
    $scope.isIndexRuleMode = isNotEmpty($scope.indexPattern.searchIndexPatternName);
    load();
    $scope.queryCustomByIndexName({
      dataSourceName: $scope.indexPattern.indexName,
      solutionType: $scope.indexPattern.solutionType,
      id: $scope.indexPattern.id,
      searchIndexPatternName: $scope.indexPattern.searchIndexPatternName,
    }, null, $scope.isIndexRuleMode);
  };

  // 切换按模板搜索还是索引规则搜索
  $scope.switchSearchMode = function(value) {
    $scope.isSearchByTemplate = value;

    // 清空数据
    $scope.hasNoTemplate = null;
    $scope.$Model.indexPatternList = [];
    $scope.indexPattern = [];
    $scope.$Model.fieldsList = [];
    $scope.$Model.beginTime = null;
    $scope.$Model.endTime = null;
    $scope.$Model.LayerSearch = true;// 分层搜索
    $scope.$Model.ExactSearch = false; // 精确搜索
    $scope.downloadCount = 5000; // 默认下载日志条数
    $scope.drilldownloadCount = 200;
    $scope.displayedFieldsList = [];// 搜索模板中选中的展示字段
    $scope.hasTimeQuery = false; // 设置默认不包含时间查询条件
    $scope.clearChartData(); // 清空柱状图数据，会隐藏柱状图
    $scope.quickChooseTimeSearch(0, true); // 默认30分钟
    if ($scope.discoverLayerTableParams) {
      $scope.discoverLayerTableParams.attr('dataset', []);
    }
    if ($scope.discoverTableParams) {
      $scope.discoverTableParams.attr('dataset', []);
    }
    $scope.$Model.fieldsList.customFields = [];
    $scope.searchCondition = [];
    $scope.searchConditionItems = {};

    load();
    if (value) {// 按模板搜索
      $scope.queryIndexPattern();
    } else {
      $scope.getIndexPatternList();
    }
  };

  // 查询索引规则列表
  $scope.getIndexPatternList = function() {
    ajax('post', 'json', JSON.stringify({ queryFields: null, pageModel: null }),
      '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/querySearchIndexPattern',
      function(data) {
        $scope.$Model.indexPatternList = data.indexPatterns;
        $scope.hasNoTemplate = !data.indexPatterns || !data.indexPatterns.length;
        if ($scope.$Model.indexPatternList.length !== 0) {
          $scope.indexPattern = $scope.$Model.indexPatternList[0];
          $scope.indexPattern.searchIndexPatternName = $scope.$Model.indexPatternList[0].indexName;
          $scope.queryCustomByIndexName({
            dataSourceName: null,
            solutionType: null,
            id: null,
            searchIndexPatternName: $scope.indexPattern.indexName,
          }, null, true);
        }
        closeLoad();
        $scope.$apply();
      }, function() {
        $scope.hasNoTemplate = true;
        closeLoad();
      });
  };

  // 保存模板
  $scope.saveTemplate = function() {
    let win = eview.widgets.Window.get('saveTemplateDialog');
    let title = I18n.get('cmp_logmatrix_save_template');
    win = createWindow('saveTemplateDialog', title, '460px', '200px', 'complex', '/dvlogmatrixwebsite/app/logretrieval/saveTemplate.html');
    win.setData('isSearchByTemplate', $scope.isSearchByTemplate);
    win.show();

    // 处理保存模板回调
    win.on('saveData', function(data) {
      let customPattern = angular.copy($scope.$Model.templateFields);
      let url = '/rest/dvlogmatrixwebsite/v1/dvlogretrievalconfigservice/updateCustomIndexPattern';
      if (data.isSaveNewTemplate) {
        customPattern.indexLabel = data.indexName;
        delete customPattern.id;
        delete customPattern.tenantId;
        url = '/rest/dvlogmatrixwebsite/v1/dvlogretrievalconfigservice/addCustomIndexPattern';
      }

      // 初始时将所有搜索条件设置为不展示
      for (let i = 0; i < customPattern.customFields.length; i++) {
        customPattern.customFields[i].queried = false;
      }
      // 处理保存模板参数（搜索条件及可作为搜索结果的字段信息）
      for (let j = $scope.searchCondition.length - 1; j >= 0; j--) {
        for (let i = 0; i < customPattern.customFields.length; i++) {
          if ($scope.searchCondition[j].fieldName === customPattern.customFields[i].fieldName) {
            customPattern.customFields.splice(i, 1);
            customPattern.customFields.unshift($scope.searchCondition[j]);
            if (!data.isSaveNewTemplate) {
              $scope.$Model.templateFields.customFields.splice(i, 1);
              $scope.$Model.templateFields.customFields.unshift(angular.copy($scope.searchCondition[j]));
            }
            break;
          }
        }
      }

      for (let i = 0; i < customPattern.customFields.length; i++) {
        let isExist = false;
        for (let x = 0; x < $scope.selectFieldList.length; x++) {
          if ($scope.selectFieldList[x].fieldName === customPattern.customFields[i].fieldName) {
            isExist = true;
            break;
          }
        }
        customPattern.customFields[i].displayed = isExist;

        delete customPattern.customFields[i].afterPresetValue;// 删除拼接数据
        delete customPattern.customFields[i].isCascadingQuery; // 是否关联查询
        const DEFAULT_OPERATOR_NA = 0;
        customPattern.customFields[i].fieldOperator = customPattern.customFields[i].fieldOperator || DEFAULT_OPERATOR_NA;
      }
      $scope.saveCustomIndexPattern(url, customPattern);
    });
  };

  // 保存模板回调
  $scope.saveCustomIndexPattern = function(url, customPattern) {
    $('#coverLayer').css('z-index', '19999999');
    $('#coverImg').css('z-index', '19100000');
    load();

    ajax('POST', 'text', angular.toJson(customPattern), url,
      function(data) {
        $scope.closePageLoad();
        eview.widgets.Window.get('saveTemplateDialog').dispose();
        alertTips(I18n.get('cmp_logmatrix_save_template_success'));
        // 刷新选择模板列表
        if ($scope.isSearchByTemplate) { // 按模板搜索
          $scope.queryIndexPattern(true);
        }
      },
      function() {
        $scope.closePageLoad();
      });
  };

  $scope.closePageLoad = function() {
    closeLoad();
    $('#coverLayer').css('z-index', '999999');
    $('#coverImg').css('z-index', '100000');
  };

  // 增加过滤条件
  $scope.addFilterCondition = function() {
    let win = eview.widgets.Window.get('addFilterConditionDialog');
    let title = I18n.get('cmp_logmatrix_configure_condition');
    win = createWindow('addFilterConditionDialog', title, '500px', '350px', 'complex', '/dvlogmatrixwebsite/app/logretrieval/configureFilterCondition.html');
    win.setData('filterInfo', {
      pageType: 'new',
      customFields: $scope.$Model.templateFields.customFields,
      searchCondition: $scope.searchCondition,
      timeField: $scope.$Model.fieldsList.timeField,
    });
    win.show();

    // 处理创建条件回调
    win.on('configureCondition', function(data) {
      $scope.handleQueryFields(data);
      $scope.updateAddCondition(data.fieldName);
      setTimeout(() => {
        let searchConditionsSelector = document.getElementById(`searchConditionsSelector_${data.fieldName}`);
        searchConditionsSelector.addEventListener('scroll', (event) => {
          let ele = event.target;
          let id = ele.getAttribute('id');
          let fieldName = id.split('_')[1];
          if (ele.scrollTop === 0) {
            $scope.getPreviousData(fieldName);
          } else if (ele.scrollTop + ele.clientHeight === ele.scrollHeight) {
            $scope.getFollowUpData(fieldName);
          }
        });
      }, 100);
      $scope.$apply();
      win.dispose();
    });
  };

  // 创建搜索条件时，通过引用关联其他对象
  $scope.updateAddCondition = function (fieldName) {
    for (let i = 0; i < $scope.searchCondition.length; i++) {
      if ($scope.searchCondition[i].fieldName !== fieldName) {
        continue;
      }
      for (let j = 0; j < $scope.$Model.fieldsList.customFields.length; j++) {
        if ($scope.$Model.fieldsList.customFields[j].fieldName !== fieldName) {
          continue;
        }
        Object.assign($scope.$Model.fieldsList.customFields[j], $scope.searchCondition[i]);
        $scope.searchCondition[i] = $scope.$Model.fieldsList.customFields[j];
        $scope.modifyColumnsCaption($scope.searchCondition[i]);
        return;
      }
    }
  };

  $scope.getPreviousData = function (fieldName) {
    if ($scope.searchConditionItems[fieldName].startIndex <= 0) {
      return;
    }
    let originFirstIndex = $scope.searchConditionItems[fieldName].startIndex;
    for (let i = 0; i < $scope.searchCondition.length; i++) {
      if ($scope.searchCondition[i].fieldName === fieldName) {
        if ($scope.searchConditionItems[fieldName].endIndex < $scope.searchCondition[i].afterPresetValue.length) {
          for (let j = 1; j <= SELECTION_SIZE / 2; j++) {
            if ($scope.searchConditionItems[fieldName].startIndex <= 0) {
              break;
            }
            $scope.searchConditionItems[fieldName].value.unshift($scope.searchCondition[i].afterPresetValue[$scope.searchConditionItems[fieldName].startIndex]);
            let len = $scope.searchConditionItems[fieldName].endIndex - $scope.searchConditionItems[fieldName].startIndex;
            $scope.searchConditionItems[fieldName].value = $scope.searchConditionItems[fieldName].value.slice(0, len);
            $scope.searchConditionItems[fieldName].startIndex--;
            $scope.searchConditionItems[fieldName].endIndex--;
          }
        }
      }
    }
    let firstRowId = originFirstIndex - $scope.searchConditionItems[fieldName].startIndex;
    let firstRow = document.getElementById(`searchConditionsItem_${fieldName}_${firstRowId}`);
    firstRow.scrollIntoView();
    $scope.$apply();
  };

  $scope.getFollowUpData = function (fieldName) {
    for (let i = 0; i < $scope.searchCondition.length; i++) {
      if ($scope.searchCondition[i].fieldName === fieldName) {
        if ($scope.searchConditionItems[fieldName].endIndex < $scope.searchCondition[i].afterPresetValue.length) {
          for (let j = 1; j <= SELECTION_SIZE / 2; j++) {
            if ($scope.searchConditionItems[fieldName].endIndex >= $scope.searchCondition[i].afterPresetValue.length) {
              break;
            }
            $scope.searchConditionItems[fieldName].value.push($scope.searchCondition[i].afterPresetValue[$scope.searchConditionItems[fieldName].endIndex]);
            $scope.searchConditionItems[fieldName].value = $scope.searchConditionItems[fieldName].value.slice(1);
            $scope.searchConditionItems[fieldName].startIndex++;
            $scope.searchConditionItems[fieldName].endIndex++;
          }
        }
      }
    }
    $scope.$apply();
  };

  // 编辑单个搜索条件
  $scope.editPerFilterCondition = function(field) {
    let win = eview.widgets.Window.get('addFilterConditionDialog');
    let title = I18n.get('cmp_logmatrix_configure_condition');
    win = createWindow('addFilterConditionDialog', title, '500px', '350px', 'complex', '/dvlogmatrixwebsite/app/logretrieval/configureFilterCondition.html');
    win.setData('filterInfo', {
      pageType: 'modify',
      fieldInfo: field,
    });
    win.show();

    // 处理创建条件回调
    win.on('configureCondition', function(data) {
      for (let i = 0; i < $scope.searchCondition.length; i++) {
        if (data.fieldName === $scope.searchCondition[i].fieldName) {
          const _beforePresetValue = $scope.searchCondition[i].afterPresetValue || [];
          $scope.searchCondition[i].fieldOperator = data.fieldOperator;
          $scope.searchCondition[i].value = data.value;
          $scope.searchCondition[i].fieldLabel = data.fieldLabel;
          $scope.searchCondition[i].presetValue = data.presetValue;
          $scope.searchCondition[i].afterPresetValue = angular.copy(data.presetValue);

          // 特殊级联字段处理
          if (SPECIAL_CONCATENATED_FIELDS.indexOf(data.fieldName) !== -1) {
            if (!$scope.searchCondition[i].isCascadingQuery) {
              let specialConcatenatedObj = {
                SiteName: $scope.$Model.siteInfos,
                GroupName: $scope.$Model.groupNames,
                ClusterId: $scope.$Model.clusterInfos,
                SolutionId: $scope.$Model.solutionInfos,
                SolutionType: $scope.$Model.solutionTypes,
              };
              let info = specialConcatenatedObj[data.fieldName];
              for (let key in info) {
                if (Object.prototype.hasOwnProperty.call(info, key)) {
                  $scope.searchCondition[i].afterPresetValue.push(info[key]);
                }
              }
            } else {
              $scope.searchCondition[i].afterPresetValue = _beforePresetValue;
            }
          }
          if ($scope.searchCondition[i].afterPresetValue.length <= SELECTION_SIZE) {
            $scope.searchConditionItems[data.fieldName].value = angular.copy($scope.searchCondition[i].afterPresetValue);
          } else {
            $scope.searchConditionItems[data.fieldName].value = angular.copy($scope.searchCondition[i].afterPresetValue).slice(0, SELECTION_SIZE);
          }
          $scope.searchConditionItems[data.fieldName].endIndex = $scope.searchConditionItems[data.fieldName].value.length;
          break;
        }
      }
      $scope.searchDefaultValue[data.fieldName] = data.value;
      $scope.modifyColumnsCaption(data);
      $scope.$apply();
      win.dispose();
    });
  };

  // 删除单个搜索条件的确认弹窗
  $scope.deletePerFilterConditionConfirm = function(field) {
    let options = {
      title: I18n.get('cmp.logmatrix.discover.delete.condition'),
      content: I18n.get('cmp.logmatrix.discover.delete.condition.tips'),
      buttons: [{
        label: commonI18n.get('cmp_basic_confirm'),
        default: false,
        handler: function(event) {
          $scope.deletePerFilterCondition(field);
        },
      }, {
        label: commonI18n.get('cmp_basic_cancel'),
        default: false,
        handler: function(event) {
          // do nothing
        },
      }],
    };
    new eview.widgets.MessageDialog.confirm(options);
  };

  // 删除单个搜索条件
  $scope.deletePerFilterCondition = function(field) {
    for (let i = 0; i < $scope.searchCondition.length; i++) {
      if (field.fieldName === $scope.searchCondition[i].fieldName) {
        $scope.searchConditionItems[field.fieldName] = {
          value: [],
          startIndex: 0,
          endIndex: 0,
        }
        $scope.searchCondition.splice(i, 1);
        break;
      }
    }

    // 删除条件后，搜索条件别名还原为模板初始值
    let fieldLabel;
    for (let j = 0; j < $scope.$Model.templateFields.customFields.length; j++) {
      if (field.fieldName === $scope.$Model.templateFields.customFields[j].fieldName) {
        fieldLabel = $scope.$Model.templateFields.customFields[j].fieldLabel;
        break;
      }
    }
    $.each($scope.$Model.fieldsList.customFields, (index, item) => {
      if (field.fieldName === item.fieldName) {
        $scope.$Model.fieldsList.customFields[index].fieldLabel = fieldLabel;
        $scope.modifyColumnsCaption($scope.$Model.fieldsList.customFields[index]);
        $scope.$Model.fieldsList.customFields[index].queried = false;
      }
    });
    $scope.$apply();
  };

  // 删除字段中的高亮标识
  $scope.deleteHighlightFlag = function(value) {
    return value.replace(/cmphighlightbegin/g, '').replace(/cmphighlightend/g, '');
  };
  // 查询钻取日志
  window.showDrillLog = function(index) {
    if ($scope.$Model.LayerSearch) {
      $scope.drillingLogByTime(index);
    } else {
      $scope.drillingLogBySerialNo(index);
    }
  };

  $scope.drillingLogByTime = function(index) {
    let dataset = $scope.discoverFileTableParams.dataset;
    let event = convertEventByFile(dataset[index].layerEvent);
    $scope.isMasklayer = true;
    $scope.isLogPage = true;

    // 加载等待
    load();
    if (!$scope.beforeQueryLog()) {
      return;
    }
    $scope.chartData.false.displayAggRule = 'auto';
    $scope.generateBarChart();

    // 查询条件
    let sortField = $scope.sortField;
    let customFields = $scope.$Model.fieldsList.customFields;
    let row;
    for (let i = 0; i < customFields.length; i++) {
      row = customFields[i];
      if (row.fieldName === sortField) {
        $scope.sortFieldType = row.fieldType;
        break;
      }
    }
    let condition = $scope.getFilterCondition();
    let fixedquerycondition = {
      beginTime: $scope.searchBeginTime,
      endTime: $scope.searchEndTime,
      indexName: $scope.indexName,
      timeField: $scope.timeField,
      solutionType: $scope.indexPattern.solutionType,
      searchIndexPatternName: $scope.indexPattern.searchIndexPatternName,
    };
    let exactSearch = $scope.$Model.ExactSearch;
    // 处理分层搜索得固定入参
    condition.ModuleIp = event.ModuleIp;
    condition.FilePath = event.FilePath;
    condition.FileName = event.FileName;
    condition.FileKey = event.FileKey;
    let filterOperator = $scope.getFilterOperator();
    filterOperator.ModuleIp = 5;
    filterOperator.FilePath = 5;
    filterOperator.FileName = 5;
    filterOperator.FileKey = 5;
    ajax('post', 'json',
      JSON.stringify({
        fixedCondition: fixedquerycondition,
        queryFields: condition,
        operatorFields: filterOperator,
        pageModel: {
          pageIndex: 1,
          rowsCount: 10,
        },
        sortModel: {
          sortField: 'Time',
          sortFieldType: 'date',
          orderType: 'desc',
        },
        exactSearch,
      }),
      '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryLog',
      function(data) {
        let str = JSON.stringify(data).replace(/&lt;/g, '<').replace(/&gt;/g, '>');
        $scope.$Model.searchResult = JSON.parse(str);
        if ($scope.$Model.searchResult.logs.length === 0) {
            closeLoad();
            return;
        }
        // 初始化分层下载时钻取字段值
        $scope.drillField = JSON.parse($scope.$Model.searchResult.logs[0]);

        let firstLog = JSON.parse($scope.$Model.searchResult.logs[0]);
        let serialNo = $scope.deleteHighlightFlag(firstLog.SerialNo);
        let time = $scope.deleteHighlightFlag(firstLog.Time);
        $scope.drillingLayerLogBySerialNo(serialNo, time, event);
      });
  };

  $scope.drillingLayerLogByFile = function(serialNo, event) {
    // 获取匹配上得第一条日志得serialNo,查询对应上下文
    ajax('post', 'json',
      JSON.stringify({
        indexName: $scope.indexName,
        time: $scope.searchEndTime,
        moduleIp: event.ModuleIp,
        logPath: event.FilePath,
        logName: event.FileName,
        fileKey: event.FileKey,
        serialNo: serialNo,
        drillCount: $scope.drillingLayerLog,
        solutionType: $scope.indexPattern.solutionType,
        searchIndexPatternName: $scope.indexPattern.searchIndexPatternName,
      }),
      '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryDrillingLogByTime',
      function(data) {
        let str = JSON.stringify(data).replace(/&lt;/g, '<').replace(/&gt;/g, '>');
        // 设置钻取源日志的位置
        $scope.$Model.drillLogInfo = JSON.parse(str);
        $scope.currentIndex = $scope.$Model.drillLogInfo.currentIndex;
        $scope.$apply();
        closeLoad();
      },
      function() {
        closeLoad();
      });
  }

  $scope.drillingLayerLogBySerialNo = function(serialNo, time, event) {
    ajax('post', 'json',
      JSON.stringify({
        indexName: $scope.indexName,
        time: time,
        moduleIp: event.ModuleIp,
        logPath: event.FilePath,
        logName: event.FileName,
        serialNo: serialNo,
        drillCount: $scope.drillingLayerLog,
        solutionType: $scope.indexPattern.solutionType,
        searchIndexPatternName: $scope.indexPattern.searchIndexPatternName,
      }),
      '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryDrillingLog',
      function(data) {
        let str = JSON.stringify(data).replace(/&lt;/g, '<').replace(/&gt;/g, '>');
        // 设置钻取源日志的位置
        $scope.$Model.drillLogInfo = JSON.parse(str);
        $scope.currentIndex = $scope.$Model.drillLogInfo.currentIndex;
        $scope.$apply();
        closeLoad();
      },
      function() {
        closeLoad();
      });
  }

  $scope.drillingLogBySerialNo = function(index) {
    $scope.drillField = JSON.parse($scope.$Model.searchResult.logs[index]);
    let field = $scope.drillField;
    $scope.isMasklayer = true;
    $scope.isLogPage = true;

    // 加载等待
    load();
    ajax('post', 'json',
      JSON.stringify({
        indexName: $scope.indexName,
        time: field.Time,
        moduleIp: $scope.deleteHighlightFlag(field.ModuleIp),
        logPath: $scope.deleteHighlightFlag(field.FilePath),
        logName: $scope.deleteHighlightFlag(field.FileName),
        serialNo: $scope.deleteHighlightFlag(field.SerialNo),
        drillCount: $scope.drillingLog,
        solutionType: $scope.indexPattern.solutionType,
        searchIndexPatternName: $scope.indexPattern.searchIndexPatternName,
      }),
      '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryDrillingLog',
      function(data) {
        let str = JSON.stringify(data).replace(/&lt;/g, '<').replace(/&gt;/g, '>');
        // 设置钻取源日志的位置
        $scope.$Model.drillLogInfo = JSON.parse(str);
        $scope.currentIndex = $scope.$Model.drillLogInfo.currentIndex;
        $scope.$apply();
        closeLoad();
      },
      function() {
        closeLoad();
      });
  };

  // 关闭钻取日志弹出页
  $scope.closeLogPage = function() {
    $scope.isMasklayer = false;
    $scope.isLogPage = false;
    $scope.$Model.drillLogInfo.drillingLogs = [];
  };

  $scope.confirmOptions = function(title, content, confirmParam) {
    let options = {
      title,
      content: content,
      buttons: [{
        label: commonI18n.get('cmp_basic_confirm'),
        default: false,
        handler: function(event) {
          if (confirmParam) {
            $scope.confirmOptionFun(confirmParam);
          } else {
            $scope.confirmOptionFun();
          }
        },
      }, {
        label: commonI18n.get('cmp_basic_cancel'),
        default: false,
        handler: function(event) {
          // do nothing
        },
      }],
    };
    new eview.widgets.MessageDialog.confirm(options);
  };

  // 校验下载日志数量
  $scope.downloadLog = function() {
    if ($scope.$Model.LayerSearch) {
      if ($scope.discoverFileTableParams === undefined ||
        $scope.discoverFileTableParams.checkedRows.checkboxRowId.arr.length < $scope.downloadLayerLogMinCount ||
        $scope.discoverFileTableParams.checkedRows.checkboxRowId.arr.length > $scope.downloadLayerLogMaxCount) {
        alertTips($scope.getI18n('cmp_logmatrix_downloadlayerlog_rule', [$scope.downloadLayerLogMinCount, $scope.downloadLayerLogMaxCount]), 'error');
        return;
      }
      $scope.downloadDrillingLogByTime();
    } else {
      $scope.openDownLoadWindow();
    }
  };

  $scope.downloadLogByLog = function (result) {
    $scope.$Model.downloadCount = result.downloadCount;
    $scope.$Model.downloadSelectedFieldsList = result.fieldList;
    if (!cmpPositiveInteger($scope.$Model.downloadCount)) {
      alertTips($scope.getI18n('cmp_logmatrix_download_rule', [$scope.minDownloadCount, $scope.maxDownloadCount]), 'error');
      return;
    }
    if ($scope.$Model.downloadCount < $scope.minDownloadCount || $scope.$Model.downloadCount > $scope.maxDownloadCount) {
      alertTips($scope.getI18n('cmp_logmatrix_download_rule', [$scope.minDownloadCount, $scope.maxDownloadCount]), 'error');
      return;
    }
    if ($scope.$Model.downloadCount > $scope.downloadThreshold) {
      $scope.confirmOptionFun = $scope.downloadLogBySelectedField;
      $scope.confirmOptions(I18n.get('cmp_logmatrix_visual_tips'), I18n.get('cmp_logmatrix_download_confirm'), null);
      return;
    }
    $scope.downloadLogBySelectedField();
  }

  $scope.openDownLoadWindow = function() {
    let win = createWindow('downLoadLogWindow', I18n.get('cmp_logretrival_download_tips'), 680, 160, 'complex', '/dvlogmatrixwebsite/app/logretrieval/downloadLog.html');
    win.show();
    win.on('downloadLogBySelectedField', $scope.downloadLogByLog);
  };

  $scope.downloadDrillingLogByTime = function() {
    let params = [];
    let checkedRows = $scope.discoverFileTableParams.checkedRows.checkboxRowId.arr;
    let dataSet = $scope.discoverFileTableParams.dataset;
    for (let i = 0; i < checkedRows.length; i++) {
      let event = convertEventByFile(dataSet[checkedRows[i]].layerEvent);
      let param = {
        indexName: $scope.indexName,
        time: $scope.searchEndTime,
        moduleIp: event.ModuleIp,
        logPath: event.FilePath,
        logName: event.FileName,
        fileKey: event.FileKey,
        drillCount: $scope.downloadLayerLog,
        solutionType: $scope.indexPattern.solutionType,
        searchIndexPatternName: $scope.indexPattern.searchIndexPatternName,
      };
      params.push(param);
    }
    load();
    fileDownloadPublic('post', '/rest/dvlogmatrixwebsite/v1/dvlogretrievaldownloadservice/downloadDrillingLogFileByTime', JSON.stringify(params), undefined, function() { closeLoad(); });
  };

  $scope.downloadLogBySelectedField = function() {
    if (($scope.indexName === undefined && $scope.searchIndexPatternName === undefined) || $scope.searchCondition === undefined) {
      return;
    }
    // 查询条件
    let condition = $scope.getFilterCondition();
    let beginTime = null;
    let endTime = null;

    if (!$('#quickTimeType').val()) {
      beginTime = getUTCTime($('#beginTimeQuick').val()) + '.000';
      endTime = getUTCTime($('#endTimeQuick').val()) + '.999';
    } else {
      $scope.getTimeDuration($('#quickTimeType').val());
      beginTime = $scope.searchBeginTime;
      endTime = $scope.searchEndTime;
    }

    let fixedquerycondition = {
      beginTime: beginTime,
      endTime: endTime,
      indexName: $scope.indexName,
      timeField: $scope.timeField,
      count: $scope.$Model.downloadCount,
      solutionType: $scope.indexPattern.solutionType,
      searchIndexPatternName: $scope.indexPattern.searchIndexPatternName,
    };
    $scope.$Model.exportData = {};

    let params = {
      fixedCondition: fixedquerycondition,
      displayedFields: $scope.$Model.downloadSelectedFieldsList,
      queryFields: condition,
      operatorFields: $scope.getFilterOperator(),
      sortModel: {
        sortField: $scope.sortField,
        orderType: $scope.orderType,
      },
    };
    load();
    fileDownloadPublic('post', '/rest/dvlogmatrixwebsite/v1/dvlogretrievaldownloadservice/downloadLogFile', JSON.stringify(params), undefined, function() { closeLoad(); });
  };

  // 校验下载钻取日志数量
  $scope.downloadDrillLogCheck = function() {
    // 当用户自定义下载数量为奇数，需要进行处理通过后台校验
    if ($scope.$Model.drilldownloadCount % 2 === 1) {
      $scope.$Model.drilldownloadCount++;
    }
    if (!cmpPositiveInteger($scope.$Model.drilldownloadCount)) {
      alertTips($scope.getI18n('cmp_logmatrix_download_rule', [$scope.minDownloadCount, $scope.maxDownloadCount]), 'error');
      return;
    }
    if ($scope.$Model.drilldownloadCount < $scope.minDownloadCount || $scope.$Model.drilldownloadCount > $scope.maxDownloadCount) {
      alertTips($scope.getI18n('cmp_logmatrix_download_rule', [$scope.minDownloadCount, $scope.maxDownloadCount]), 'error');
    } else if ($scope.$Model.drilldownloadCount > $scope.downloadThreshold) {
      $scope.confirmOptionFun = $scope.downloadDrillLog;
      $scope.confirmOptions(I18n.get('cmp_logmatrix_visual_tips'), I18n.get('cmp_logmatrix_download_confirm'), null);
    } else {
      $scope.downloadDrillLog();
    }
  };

  // 下载钻取日志
  $scope.downloadDrillLog = function() {
    let param = {
      indexName: $scope.indexName,
      time: $scope.drillField.Time,
      moduleIp: $scope.deleteHighlightFlag($scope.drillField.ModuleIp),
      logPath: $scope.deleteHighlightFlag($scope.drillField.FilePath),
      logName: $scope.deleteHighlightFlag($scope.drillField.FileName),
      serialNo: $scope.deleteHighlightFlag($scope.drillField.SerialNo),
      drillCount: $scope.$Model.drilldownloadCount / 2,
      solutionType: $scope.indexPattern.solutionType,
      searchIndexPatternName: $scope.indexPattern.searchIndexPatternName,
    };
    load();
    fileDownloadPublic('post', '/rest/dvlogmatrixwebsite/v1/dvlogretrievaldownloadservice/downloadDrillingLogFile', JSON.stringify(param), undefined, function() { closeLoad(); });
  };


  $scope.removeSelectedField = function(index, field) {
    $scope.discoverTableParams.setColumnDisplay(field.fieldName, false);
    $scope.selectFieldList.splice(index, 1);
    if ($scope.selectFieldList.length === 0) {
      $scope.discoverTableParams.setColumnDisplay('event', true);
    }
    $scope.availableFieldList.push(field);
    $scope.discoverTableParams.attr('dataset', $scope.$Model.logInfo);
    $scope.discoverTableParams.attr('enable-tooltip', true);
    $scope.discoverTableParams.attr('total-records', $scope.$Model.count);
  };

  $scope.addSelectedField = function(index, field) {
    $scope.discoverTableParams.setColumnDisplay('event', false);
    $scope.discoverTableParams.setColumnDisplay(field.fieldName, true);
    $scope.availableFieldList.splice(index, 1);
    $scope.selectFieldList.push(field);
    $scope.discoverTableParams.attr('dataset', $scope.$Model.logInfo);
    $scope.discoverTableParams.attr('enable-tooltip', true);
    $scope.discoverTableParams.attr('total-records', $scope.$Model.count);
  };

  $scope.modifyColumnsCaption = function (field) {
    let fieldLabel = field.fieldLabel ? field.fieldLabel : field.fieldName;
    $scope.discoverTableParams.getChildren(field.fieldName).attr('caption', fieldLabel);
  };

  $scope.discoverCreateHandler = function(event) {
    let tableObj = event.target;
    // 对列做自定义排序
    if (!$scope.$Model.fieldsList) {
      return;
    } else {
      $.each($scope.$Model.fieldsList.customFields, function(index, item) {
        tableObj.addSortHandler(item.fieldName, function(colId, curPage, displayLength, sortOrder) {
          $scope.sortField = colId;
          $scope.orderType = sortOrder;
          $scope.discoverTableParams.attr('cur-page', 1);
        });
      });
    }
  };
  // 从界面条件获取查询的过滤条件
  $scope.getFilterCondition = function() {
    let condition = {};
    for (let i = 0; i < $scope.searchCondition.length; i++) {
      let searchField = $scope.searchCondition[i];
      if (!searchField.value) {
        continue;
      }
      if (searchField.fieldName === 'ClusterId') {
        $scope.getDefaultSelectField(condition, $scope.$Model.clusterInfos, searchField.value, searchField.fieldName, searchField.fieldOperator);
      } else if (searchField.fieldName === 'ClusterType') {
        $scope.getDefaultSelectField(condition, $scope.$Model.clusterTypes, searchField.value, searchField.fieldName, searchField.fieldOperator);
      } else if (searchField.fieldName === 'SolutionId') {
        $scope.getDefaultSelectField(condition, $scope.$Model.solutionInfos, searchField.value, searchField.fieldName, searchField.fieldOperator);
      } else if (searchField.fieldName === 'SolutionType') {
        $scope.getDefaultSelectField(condition, $scope.$Model.solutionTypes, searchField.value, searchField.fieldName, searchField.fieldOperator);
      } else if (searchField.fieldName === 'GroupName') {
        $scope.getDefaultSelectField(condition, $scope.$Model.groupNames, searchField.value, searchField.fieldName, searchField.fieldOperator);
      } else if (searchField.fieldName === 'SiteName') {
        $scope.getDefaultSelectField(condition, $scope.$Model.siteInfos, searchField.value, searchField.fieldName, searchField.fieldOperator);
      } else {
        condition[searchField.fieldName] = searchField.value;
      }
    }
    return condition;
  };

  // 从界面条件获取查询的操作符
  $scope.getFilterOperator = function() {
    let condition = {};
    for (let i = 0; i < $scope.searchCondition.length; i++) {
      let searchField = $scope.searchCondition[i];
      if (!searchField.value) {
        continue;
      }

      condition[searchField.fieldName] = searchField.fieldOperator || DEFAULT_OPERATOR_NA;
    }
    return condition;
  };

  $scope.getDefaultSelectField = function(condition, defaultArray, selectValue, selectFieldName, selectFieldOperator) {
    for (let key in defaultArray) {
      if (selectValue !== defaultArray[key]) {
        continue;
      }
      if (selectFieldOperator !== 0) {
        condition[selectFieldName] = key;
        return;
      }
      if (condition[selectFieldName] === undefined) {
        condition[selectFieldName] = '"' + key + '"';
      } else {
        condition[selectFieldName] = condition[selectFieldName] + ' OR ' + '"' + key + '"';
      }
    }
    if (condition[selectFieldName] === undefined) {
      condition[selectFieldName] = selectValue;
    }
  };

  // 配置告警
  $scope.alertConfig = function() {
    if ($scope.timeField === null || $scope.timeField === undefined) {
      return;
    }
    $scope.isMasklayer = true;
    $scope.isAlertPage = true;
    $('#alertConfigPage').animate({
      right: '0%',
    }, 400);
    let visual = {};
    visual.filterTerms = $scope.getFilterCondition();
    visual.operatorFields = $scope.getFilterOperator();
    visual.timeField = $scope.timeField;
    visual.dataSource = $scope.indexName;
    visual.indexLabel = $scope.indexPattern.indexLabel;
    visual.solutionType = $scope.indexPattern.solutionType;
    visual.searchIndexPatternName = $scope.indexPattern.searchIndexPatternName;
    if ($scope.$Model.ExactSearch === true) {
      for (let key in visual.filterTerms) {
        if (!Object.prototype.hasOwnProperty.call(visual.filterTerms, key)) {
          continue;
        }
        let regex = /^"(.*)"$/;
        if (regex.test(visual.filterTerms[key])) {
          continue;
        }
        visual.filterTerms[key] = '"' + visual.filterTerms[key] + '"';
      }
    }
    $rootScope.$broadcast('alertDetailIsMasklayer', true, visual);
  };

  $scope.openFieldTopN = function(field) {
    let title = field.fieldLabel || field.fieldName;
    if (!field.indexed) {
      alertTips($scope.getI18n('cmp.logmatrix.discover.topn.noindex.tips', [title]), 'confirm');
      return;
    }
    if (field.fieldType === 'text' && field.analyzed) {
      let options = {
        title: commonI18n.get('cmp_basic_confirm'),
        content: $scope.getI18n('cmp.logmatrix.discover.topn.dividedstring.tips', [title]),
        buttons: [{
          label: commonI18n.get('cmp_basic_confirm'),
          default: false,
          handler: function(event) {
            $scope.openFieldTopNAction(field);
          },
        }],
      };
      new eview.widgets.MessageDialog.confirm(options);
      return;
    }
    $scope.openFieldTopNAction(field);
  };
  $scope.openFieldTopNAction = function(field) {
    $scope.topNOrderType = 'desc';
    $scope.topNFieldName = field.fieldName;
    let param = $scope.buildTopNSearchParam(field);
    load();
    ajax('POST', 'json', JSON.stringify(param),
      '/rest/dvlogmatrixwebsite/v1/dvlogretrievalqueryservice/queryFieldEvents',
      function(data) {
        closeLoad();
        $scope.$Model.topNResult = data;
        if (param.aggregationField === 'Time') {
          for (let i = 0; i < $scope.$Model.topNResult.length; i++) {
            $scope.$Model.topNResult[i].fieldValue = getLocalTime(reformatUTCTime($scope.$Model.topNResult[i].fieldValue));
          }
        }
        $scope.aggDocTotal = $scope.$Model.topNResult.length;
        let title = field.fieldLabel || field.fieldName;
        let win = eview.widgets.Window.get('fieldTopWindow');
        win = createWindow('fieldTopWindow', title, 480, 320, 'complex', '/dvlogmatrixwebsite/app/logretrieval/fieldTopN.html');
        let fieldTopData = {
          topNResult: $scope.$Model.topNResult,
          aggDocTotal: $scope.aggDocTotal,
          topNOrderType: $scope.topNOrderType,
          buildTopNSearchParam: $scope.buildTopNSearchParam(field),
          timeType: $('#quickTimeType').val(),
          clusterInfos: $scope.$Model.clusterInfos,
          solutionInfos: $scope.$Model.solutionInfos,
          solutionTypes: $scope.$Model.solutionTypes,
          clusterTypes: $scope.$Model.clusterTypes,
          fieldName: field.fieldName,
        };
        win.setData('fieldTopData', fieldTopData);
        win.show();
      },
      function() {
        closeLoad();
      });
  };

  $scope.buildTopNSearchParam = function(field) {
    let begin = '';
    let end = '';
    if ($scope.hasTimeQuery) {
      if (!$('#quickTimeType').val()) {
        begin = getUTCTime($('#beginTimeQuick').val()) + '.000';
        end = getUTCTime($('#endTimeQuick').val()) + '.999';
      } else {
        $scope.getTimeDuration($('#quickTimeType').val());
        begin = $scope.searchBeginTime;
        end = $scope.searchEndTime;
      }
    }
    let condition = $scope.getFilterCondition();
    let fixedquerycondition = {
      beginTime: begin,
      endTime: end,
      indexName: $scope.indexName,
      timeField: $scope.timeField,
      solutionType: $scope.indexPattern.solutionType,
      searchIndexPatternName: $scope.indexPattern.searchIndexPatternName,
    };
    let param = {
      fixedCondition: fixedquerycondition,
      filterFields: condition,
      operatorFields: $scope.getFilterOperator(),
      aggregationField: $scope.topNFieldName,
      aggregationFieldType: field.fieldType,
      order: $scope.topNOrderType,
    };
    return param;
  };

  $scope.clearChartData = function() {
    $scope.chartData = {
      true: {
        seriesDataList: undefined,
        displaySearchRange: undefined,
        displayAggRule: undefined,
      },
      false: {
        seriesDataList: undefined,
        displaySearchRange: undefined,
        displayAggRule: undefined,
      },
    };
  };

  $scope.ifDisplayChart = function() {
    return $scope.chartData[$scope.$Model.LayerSearch].seriesDataList;
  };

  $scope.setBarChartAgg = function(item) {
    let interval = item.value;
    if (interval === 'auto') {
      $scope.chartData[$scope.$Model.LayerSearch].displayAggRule = 'auto';
      $scope.generateBarChart();
      return;
    }
    let beginTime = new Date($scope.searchBeginTime).getTime() + EIGHT_HOURS;
    let endTime = new Date($scope.searchEndTime).getTime() + EIGHT_HOURS;
    if ((endTime - beginTime) / BEST_AGG_MAP[interval] <= $scope.logQueryMaxBucket) {
      $scope.chartData[$scope.$Model.LayerSearch].displayAggRule = interval;
      $scope.logQueryInterval = interval;
    } else {
      $scope.chartData[$scope.$Model.LayerSearch].displayAggRule = 'auto';
      alertTips(I18n.get('cmp.logmatrix.visualconfig.aggregation.set.auto', [$scope.logQueryMaxBucket]), 'confirm');
    }
    if ($scope.$Model.LayerSearch) {
      if ($scope.searchIntervalSelectorByLayer) {
        $scope.searchIntervalSelectorByLayer.setSelectItemsByValue($scope.chartData[$scope.$Model.LayerSearch].displayAggRule);
      }
    } else {
      if ($scope.searchIntervalSelectorNotByLayer) {
        $scope.searchIntervalSelectorNotByLayer.setSelectItemsByValue($scope.chartData[$scope.$Model.LayerSearch].displayAggRule);
      }
    }
    $scope.generateBarChart();
  };

  $scope.generateBarChart = function() {
    // 获取搜索时间，补毫秒
    if (!$('#quickTimeType').val()) {
      $scope.searchBeginTimeNotUTC = $('#beginTimeQuick').val() + '.000';
      $scope.searchEndTimeNotUTC = $('#endTimeQuick').val() + '.999';
    } else {
      $scope.getTimeDurationNotUTC($('#quickTimeType').val());
    }
    let beginTime = new Date($scope.searchBeginTimeNotUTC).getTime();
    let endTime = new Date($scope.searchEndTimeNotUTC).getTime();
    if (!$scope.chartData[$scope.$Model.LayerSearch].displayAggRule || $scope.chartData[$scope.$Model.LayerSearch].displayAggRule === 'auto') {
      $scope.calculateBestInterval(beginTime, endTime);
    }
    // 查询条件
    let req = {
      bucketAgg: {
        aggName: 'Time-per-' + $scope.logQueryInterval,
        aggParamMap: {
          field: 'Time',
          interval: $scope.logQueryInterval,
        },
        type: 'date_histogram',
      },
      exactSearch: $scope.$Model.ExactSearch,
      filters: $scope.getFilterCondition(),
      operatorFields: $scope.getFilterOperator(),
      index: $scope.indexName,
      solutionType: $scope.indexPattern.solutionType,
      searchIndexPatternName: $scope.indexPattern.searchIndexPatternName,
      metrics: [{
        aggName: 'COUNT',
        aggParamMap: {},
        close: false,
        type: 'COUNT',
      }],
      timeConfig: {
        beginTime: '',
        endTime: '',
        timeField: 'Time',
        timeType: '0',
      },
      timeRange: {
        beginTime: beginTime,
        endTime: endTime,
        preZone: '8:00',
        timeField: 'Time',
      },
      title: '',
      type: 'Line',
    };
    $scope.chartData[$scope.$Model.LayerSearch].seriesDataList = [];
    ajax('post', 'json', JSON.stringify(req), '/rest/dvlogmatrixwebsite/v1/dvlogretrievalvisualservice/queryLineVisualAggInfo', function(resp) {
      $scope.chartData[$scope.$Model.LayerSearch].seriesDataList = resp.lineModeList;
      if ($scope.chartData[$scope.$Model.LayerSearch].seriesDataList.length === 0) {
        $scope.chartData[$scope.$Model.LayerSearch].displaySearchRange = undefined;
        $scope.chartData[$scope.$Model.LayerSearch].displayAggRule = undefined;
      } else {
        $scope.chartData[$scope.$Model.LayerSearch].displaySearchRange = getLocalTime(beginTime) + ' ~ ' + getLocalTime(endTime);
        $scope.chartData[$scope.$Model.LayerSearch].displayAggRule = $scope.chartData[$scope.$Model.LayerSearch].displayAggRule ? $scope.chartData[$scope.$Model.LayerSearch].displayAggRule : 'auto';
        if ($scope.$Model.LayerSearch) {
          if ($scope.searchIntervalSelectorByLayer) {
            $scope.searchIntervalSelectorByLayer.setSelectItemsByValue($scope.chartData[$scope.$Model.LayerSearch].displayAggRule);
          }
        } else {
          if ($scope.searchIntervalSelectorNotByLayer) {
            $scope.searchIntervalSelectorNotByLayer.setSelectItemsByValue($scope.chartData[$scope.$Model.LayerSearch].displayAggRule);
          }
        }
        $scope.$apply();
      }
      $scope.drawChart();
    }, function() {
      $scope.clearChartData();
    });
  };

  $scope.drawChart = function() {
    if (!$scope.chartData[$scope.$Model.LayerSearch].seriesDataList || $scope.$Model.ChartDisplay === false) {
      return;
    }
    setTimeout(function() {
      let chartId = $scope.$Model.LayerSearch ? 'log-count-bar-layer' : 'log-count-bar-notLayer';
      getChart('bar', chartId, $scope.chartData[$scope.$Model.LayerSearch].seriesDataList, 1, {
        animation: true,
        legend: {
          show: false, // 只可能有一组数据，隐藏分组
        },
        barType: [0, 0, 0, 0], // 柱状图柱样式
        maxMinPoint: false,
      }, function(chart) {
        // do nothing
      });
    }, 100);
  };

  $scope.calculateBestInterval = function(beginTime, endTime) {
    let duration = endTime - beginTime;
    $scope.logQueryInterval = undefined;
    Object.keys(BEST_AGG_MAP).find(agg => {
      if (duration / BEST_AGG_MAP[agg] <= $scope.logQueryMaxBucket) {
        $scope.logQueryInterval = agg;
        return true;
      }
      return false;
    });
    $scope.logQueryInterval = $scope.logQueryInterval ? $scope.logQueryInterval : '365d';
  };

  $('.input-controller').click(function() {
    $('#fieldDisplaySetting').css('height', $(window).height() - $('.discover-right-container').offset().top - 30);
    $('#fieldDisplayDefault').css('height', $(window).height() - $('.discover-right-container').offset().top - 30);
    $('.discover-right-container').css('height', $(window).height() - $('.discover-right-container').offset().top - 18);
  });
  $('#fullFieldsNavBar').click(function() {
    $('.full-left-column').animate({
      width: '0px',
    }, 100);
    $('.full-left-column').hide();
    $('.tiny-left-column').show();
    $('.tiny-left-column').animate({
      width: '15px',
    }, 100);
    $('#discoverTable').animate({
      'padding-left': '25px',
    }, 100);
  });
  $('#tinyFieldsNavBar').click(function() {
    $('.tiny-left-column').animate({
      width: '0px',
    }, 100);
    $('.tiny-left-column').hide();
    $('.full-left-column').show();
    $('.full-left-column').animate({
      width: '200px',
    }, 100);
    $('#discoverTable').animate({
      'padding-left': '210px',
    }, 100);
  });
  $(document).click(function() {
    $('.quick-time-duration').hide();
  });
  $scope.openTimePanel = function() {
    $('.quick-time-duration').show();
  };
  $scope.getHelp = function(helpId) {
    $scope.GLOBAL_LANG = I18n.locale;
    let language = $scope.GLOBAL_LANG !== 'en-us' ? 'zh' : 'en';
    let libId = $.ajax({
      url: '/hedex/rest/infocenterwebsite/basehdxlibid?domain=default&language=' + language,
      async: false,
    });
    let hedexLib = libId.responseText;
    let url = '/hedex/hedex.do?lib=' + encodeURI(hedexLib) + '&id=' + encodeURI(helpId) + '&locale=' + $scope.GLOBAL_LANG;
    let win = parent.window.open(encodeURI(url), '_blank');
    win.opener = null;
    win.location = url;
  };
  // 绑定事件
}

(function() {
  'use strict';
  $CmpApp.directive('longCharSubstring', function() {
    return {
      restrict: 'A',
      scope: false,
      controller: function($scope, $element, $attrs, $compile) {
        let str = $attrs.longCharSubstring.split(',')[0];
        let long = parseInt($attrs.longCharSubstring.split(',')[1]);
        $scope.$watch(str, function(newV, oldV) {
          if (newV === undefined || newV === null) {
            return;
          }
          // 替换高亮字段
          let template = '';
          // 按10k长度进行截取
          let i = 0;
          for (let len = newV.length / long; i < len - 1; i++) {
            if (i === 0) {
              template += '<span >' + $scope.encode(newV.substr(i * long, long)) + '</span><span ng-init=\'showcontent=[true];showtips=[true]\' ng-show=\'showtips[' + i + ']\' ng-click=\'showcontent[' + (i + 1) + ']=true;showtips[' + (i + 1) + ']=true;showtips[' + i + ']=false\' class=\'long-char\'>    More ></span>';
            } else {
              template += '<span ng-show=\'showcontent[' + i + ']\'>' + $scope.encode(newV.substr(i * long, long)) + '</span><span ng-click=\'showcontent[' + (i + 1) + ']=true;showtips[' + (i + 1) + ']=true;showtips[' + i + ']=false\' ng-show=\'showtips[' + i + ']\' class=\'long-char\'>    More ></span>';
            }
          }
          if (i === 0) {
            template += '<span >' + $scope.encode(newV) + '</span>';
          } else {
            template += '<span ng-show=\'showcontent[' + i + ']\'>' + $scope.encode(newV.substr(i * long, long)) + '</span>';
          }
          // 还原高亮字段
          template = angular.element(template.replace(/cmphighlightbegin/g, '<span class=\'highlight\'>').replace(/cmphighlightend/g, '</span>'));
          $element.append(template);
          $compile(template)($scope);
        });
        $scope.encode = function(str) {
          let newStr;
          let REGX_HTML_ENCODE = /"|&|'|<|>|[\x00-\x20]|[\x7F-\xFF]|[\u0100-\u2700]/g;
          newStr = (str !== undefined) ? str : this.toString();
          return newStr.replace(REGX_HTML_ENCODE,
            function($0) {
              let c = $0.charCodeAt(0);
              let r = ['&#'];
              c = (c === 0x20) ? 0xA0 : c;
              r.push(c);
              r.push(';');
              return r.join('');
            });
        };
      },
      link: function(scope, tElement, tAttr, linker, compile) {
        // do nothing
      },
    };
  });
})();
