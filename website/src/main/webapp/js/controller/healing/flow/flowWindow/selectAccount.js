/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

function SelectAccountCtrl($scope, $q) {
  $scope.init = function() {
    parent.selectAccountScope = $scope;
    // 从win获取数据
    $scope.win = eview.widgets.Window.get('selectAccountWin');
    $scope.selectedMoType = $scope.win.getData('selectedMoType');
    $scope.selectedNeInstance = $scope.win.getData('selectedNeInstance');
    $scope.originSelectedNe = $scope.win.getData('selectedNeInstance');
    $scope.selectedAccountProtocol = $scope.win.getData('selectedAccountProtocol');
    $scope.selectedAccountName = $scope.win.getData('selectedAccountName');
    $scope.selectedAccount = $scope.win.getData('selectedAccount');
    $scope.originSelectedAccount = $scope.win.getData('selectedAccount');

    $scope.moTypeTreeData = {
      id: 'Root',
      label: 'Root',
      children: [],
    };
    $scope.allNeInstances = [];
    $scope.allAccounts = [];
    $scope.DnToAccountMap = {};

    $scope.completeMoTypeTree();
  };

  $scope.completeMoTypeTree = function() {
    ajax('POST', 'json', {}, '/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/getNeTypeTree', function(data) {
      if (data.children && data.children.length > 0) {
        $scope.moTypeTreeRootNode.addNodes('Root', data.children, true, true);
        $scope.moTypeTreeData = data;
      }
      $scope.setMoTypeSelected();
    });
  };

  $scope.neInstanceListeners = {
    change: function(obj) {
      $scope.acquireDnByMoType($scope.selectedMoType, obj.currentPage, obj.displayLength);
    },
  };

  $scope.acquireDnByMoType = function(moType, pageIndex, pageSize) {
    if (!moType || moType === '') {
      return;
    }
    $scope.selectedMoType = moType;

    let queryDnListByUserAndMoTypeReq = {
      limit: pageSize,
      offset: (pageIndex - 1) * pageSize,
      moType,
    };

    ajax('POST', 'json', JSON.stringify(queryDnListByUserAndMoTypeReq), '/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/queryDnListByUserAndMoType', function(data) {
      $scope.allNeInstances = data.healingDnInfos ? data.healingDnInfos : [];
      $scope.setDnSelected();
      $scope.neInstancePagination.attr('total-records', data.total);
      $scope.$apply();
    });
  };

  $scope.acquireAccountByDn = function(index) {
    // 如果之前保存过映射关系就不查询接口
    if ($scope.allNeInstances[index] && $scope.DnToAccountMap[$scope.selectedNeInstance]) {
      $scope.allAccounts = $scope.DnToAccountMap[$scope.selectedNeInstance];
      $scope.selectedAccountIndex = -1;
      $scope.setAccountSelected();
      $scope.$apply();
      return;
    }
    $scope.selectedNeInstance = $scope.allNeInstances[index].dn;

    ajax('POST', 'json', $scope.allNeInstances[index].dn, '/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/queryAccountByDn', function(data) {
      $scope.DnToAccountMap[$scope.allNeInstances[index].dn] = data ? data : [];
      $scope.allAccounts = $scope.DnToAccountMap[$scope.allNeInstances[index].dn];
      $scope.selectedAccountIndex = -1;
      $scope.setAccountSelected();
      $scope.$apply();
    });
  };

  $scope.setMoTypeSelected = function() {
    if (!$scope.selectedMoType) {
      return;
    }

    $scope.moTypeTreeRootNode.clickNode($scope.selectedMoType);
  };

  $scope.setDnSelected = function() {
    if ($scope.allNeInstances.length === 0) {
      $scope.allAccounts = [];
      return;
    }
    if (!$scope.originSelectedNe) {
      $scope.selectedDnIndex = 0;
      $scope.selectedNeInstance = $scope.allNeInstances[0].dn;
      $scope.acquireAccountByDn(0);
    } else {
      for (let i = 0; i < $scope.allNeInstances.length; i++) {
        if ($scope.allNeInstances[i].dn === $scope.originSelectedNe) {
          $scope.selectedDnIndex = i;
          $scope.selectedNeInstance = $scope.originSelectedNe;
          $scope.acquireAccountByDn(i);
          return;
        }
      }
      $scope.selectedDnIndex = 0;
      $scope.selectedNeInstance = $scope.allNeInstances[0].dn;
      $scope.acquireAccountByDn(0);
    }
  };

  $scope.setAccountSelected = function() {
    if ($scope.allAccounts.length === 0 || !$scope.originSelectedAccount) {
      return;
    }

    for (let i = 0; i < $scope.allAccounts.length; i++) {
      if ($scope.allAccounts[i].key === $scope.originSelectedAccount) {
        $scope.selectAccount(i);
        return;
      }
    }
  };

  $scope.hasNoNeInstances = function() {
    return !$scope.allNeInstances || $scope.allNeInstances.length === 0;
  };

  $scope.hasNoAccounts = function() {
    return !$scope.allAccounts || $scope.allAccounts.length === 0;
  };

  $scope.setDataToWin = function() {
    $scope.win.setData('selectedMoType', $scope.selectedMoType);
    $scope.win.setData('selectedNeInstance', $scope.selectedNeInstance);
    $scope.win.setData('selectedAccountProtocol', $scope.selectedAccountProtocol);
    $scope.win.setData('selectedAccountName', $scope.selectedAccountName);
    $scope.win.setData('selectedAccount', $scope.selectedAccount);
  };

  $scope.moTypeTreeListeners = {
    click: clickHandler,
  };

  function clickHandler(event) {
    $scope.selectedMoType = event.data.nodeId;
    $scope.selectedNeInstance = null;
    $scope.selectedAccountProtocol = null;
    $scope.selectedAccountName = null;
    $scope.selectedAccount = null;
    $scope.acquireDnByMoType(event.data.nodeId, 1, 10);
  }

  $scope.selectDn = function(neInstance, index) {
    $scope.selectedDnIndex = index;
    $scope.selectedNeInstance = neInstance.dn;
    $scope.selectedAccountProtocol = null;
    $scope.selectedAccountName = null;
    $scope.selectedAccount = null;
    $scope.acquireAccountByDn(index);
  };

  $scope.selectAccount = function(index) {
    $scope.selectedAccountIndex = index;
    $scope.selectedAccountProtocol = $scope.allAccounts[index].displayProtocol;
    $scope.selectedAccountName = $scope.allAccounts[index].accountName;
    $scope.selectedAccount = $scope.allAccounts[index].key;
  };

  $scope.showMessage = function(type, message) {
    showMessage($scope, type, message);
  };
}