'use strict';

function SelectParameterCtrl($scope) {
  _scope = $scope;
  $scope.init = function() {
    parent.parameterScope = $scope;
    var win = eview.widgets.Window.get('selectParameter');
    $scope.canCloseParamWin = true;
    $scope.timerTemplateList = [];
    $scope.uploadFilePaths = {};
    $scope.uploadFileNames = {};
    $scope.selectedParameters = win.getData('selectedParameter') || [];
    $scope.flowParameters = win.getData('flowParameters') || [];

    $scope.initParameterTable();
    $scope.queryFileConfig();
  };

  function setParams(i, paramType) {
    for (let j = 0; j < $scope.selectedParameters.length; j++) {
      if ($scope.flowParameters[i].name === $scope.selectedParameters[j].name) {
        if (paramType === 'PVALUETYPE') {
          $scope.flowParameters[i].value = null;
        } else if (paramType === 'STRINGTYPE' || paramType === 'DATETYPE' || paramType === 'PVALUETYPE' || paramType === 'REMOTE_NOTIFY_OBJ' || paramType === 'DN_TYPE' || paramType === 'IP_TREE' || paramType === 'UPLOAD_FILE') {
          $scope.flowParameters[i].value = $scope.selectedParameters[j].value;
        } else if ((paramType === 'ENUMTYPE' || paramType === 'SCRIPTTYPE')) {
          $scope.flowParameters[i].value = $scope.selectedParameters[j].value;
          $scope.flowParameters[i].label = $scope.selectedParameters[j].value;
        } else if (paramType === 'ENUMRATE_KEY_VALUE') {
          $scope.flowParameters[i].value = $scope.selectedParameters[j].value;
          $scope.flowParameters[i].label = $scope.selectedParameters[j].label;
        }
      }
    }
  }

  $scope.generateFlowParamsMap = function() {
    let paramType = '';
    for (let i = 0; i < $scope.flowParameters.length; i++) {
      paramType = $scope.flowParameters[i].flowParamType;
      setParams(i, paramType);
      if ($scope.flowParameters[i].flowParamType === 'UPLOAD_FILE' && $scope.flowParameters[i].value !== 'emptyPath') {
        $scope.uploadFileNames[$scope.flowParameters[i].name] = $scope.flowParameters[i].value.slice(36);
      }
      // 兼容V6流程包导入及数据割接
      if ($scope.flowParameters[i].flowParamType === 'ENUMTYPE' && !$scope.flowParameters[i].selectValues && $scope.flowParameters[i].valueLibrary) {
        $scope.flowParameters[i].selectValues = $scope.flowParameters[i].valueLibrary;
      }
      let item = $scope.flowParameters[i];
      if (item.flowParamType === 'ENUMTYPE') {
        if (item.valueIndex !== -1) {
          item.value = [];
          // 兼容v6流程包
          let valueLibrary = item.selectValues ? item.selectValues : item.valueLibrary;
          item.value.push(valueLibrary.split(/[,;|]/)[item.valueIndex]);
        }
      }
    }
  };

  $scope.initParameterTable = function() {
    if ($scope.flowParameters.length === 0) {
      return;
    }

    $scope.generateFlowParamsMap();

    setTimeout(function() {
      var tableScope = angular.element(document.getElementById('parameterTable')).scope();
      for (let i = 0; i < $scope.flowParameters.length; i++) {
        if ($scope.flowParameters[i].flowParamType === 'DATETYPE') {
          let timeScope = angular.element(document.getElementById('confirmFlow_' + i)).scope();
          if (timeScope['confirmTime_' + i]) {
            timeScope['confirmTime_' + i].setUTCDateTime($scope.convertTimeToTimestamp($scope.flowParameters[i].value));
          } else {
            $('#confirmTime_' + i + '_value').val($scope.flowParameters[i].value);
          }
        }
        if ($scope.flowParameters[i].flowParamType === 'REMOTE_NOTIFY_OBJ') {
          $scope.dealRemoteNotifyParam(i);
        }

        if ($scope.flowParameters[i].flowParamType === 'CASCADE') {
          let paramName = $scope.flowParameters[i].params[1].paramsName;
          for (let j = 0; j < $scope.selectedParameters.length; j++) {
            if ($scope.flowParameters[i].name === $scope.selectedParameters[j].name) {
              paramName = JSON.parse($scope.selectedParameters[j].value).paramName;
            }
          }
          tableScope['endValue_02_' + i].attr('values', $scope.flowParameters[i].params);
          tableScope['endValue_02_' + i].attr('select-value', paramName);
          tableScope['endValue_02_' + i].setSelectItemsByLabel(paramName);
          // 设置级联参数子节点值
          $scope.getChildData(paramName, $scope.flowParameters[i].name);
        }
        if ($scope.flowParameters[i].flowParamType === 'DN_TYPE') {
          $scope.transformDnParam(i);
        }
        if ($scope.flowParameters[i].flowParamType === 'ENUMTYPE') {
          if ($scope.flowParameters[i].value === 'false') {
            tableScope[`itemValue_17${i}`].setSelectItemsByValue('false');
          } else if ($scope.flowParameters[i].value === 'true') {
            tableScope[`itemValue_17${i}`].setSelectItemsByValue('true');
          }
        }
      }
    }, 0);
  };

  $scope.dealRemoteNotifyParam = function(index) {
    if (!$scope.flowParameters[index] || !$scope.flowParameters[index].value) {
      return;
    }

    var flowPramScope = $('#parameterTable').scope();
    if (typeof $scope.flowParameters[index].value === 'string') {
      var curValue = $scope.flowParameters[index].value.split(',');
      var idList = [];
      if (!curValue || !curValue.length) {
        flowPramScope['notifyListId_' + index].attr('value', '');
      }

      // 获取id列表
      curValue.forEach(function(id) {
        idList.push(id.split('_')[1]);
      });
      $scope.flowParameters[index].value = angular.copy(idList);
      // 判断是按用户还是用户组
      if (curValue[0].split('_')[0] === 'u') {
        $scope.dealUserList(index);
      } else {
        $scope.dealGroupList(index);
      }
    } else {
      var nameList = [];
      $scope.flowParameters[index].value.forEach(function(val) {
        var name = val.userName || val.name;
        nameList.push(name);
      });
      flowPramScope['notifyListId_' + index].attr('value', nameList.join());
    }
  };

  $scope.dealUserList = function(index) {
    var param = {
      offset: 0,
      limit: 100,
      userIds: $scope.flowParameters[index].value || [],
    };

    ajax('POST', 'json', JSON.stringify(param), '/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryEmail', function(data) {
      var flowPramScope = $('#parameterTable').scope();
      var nameList = [];
      // 当存在远程通知用户被清理时，提示重新选通知用户
      if (!data.total || data.total < $scope.flowParameters[index].value.length) {
        showMessage($scope, 'warning', I18n.get('cmp.healing.flow.para.remotenotify.user.update.tips') + $scope.flowParameters[index].name);
      }
      if (data && data.users) {
        data.users.forEach(function(user) {
          nameList.push(user.userName);
          user.userId = 'u_' + user.userId;
        });
        $scope.flowParameters[index].value = angular.copy(data.users);
      }
      flowPramScope['notifyListId_' + index].attr('value', nameList.join(','));
    });
  };

  $scope.dealGroupList = function(index) {
    var param = {
      offset: 0,
      limit: 100,
      groupIds: $scope.flowParameters[index].value || [],
    };

    ajax('POST', 'json', JSON.stringify(param), '/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryEmailGroups', function(data) {
      var flowPramScope = $('#parameterTable').scope();
      var groupList = [];
      // 当存在远程通知用户被清理时，提示重新选通知用户
      if (!data.totalSize || data.totalSize < $scope.flowParameters[index].value.length) {
        showMessage($scope, 'warning', I18n.get('cmp.healing.flow.para.remotenotify.user.update.tips') + $scope.flowParameters[index].name);
      }
      if (data && data.queryDate) {
        data.queryDate.forEach(function(group) {
          groupList.push(group.name);
          group.groupId = 'g_' + group.groupId;
        });
        $scope.flowParameters[index].value = angular.copy(data.queryDate);
      }
      flowPramScope['notifyListId_' + index].attr('value', groupList.join(','));
    });
  };

  $scope.transformDnParam = function(index) {
    if (!$scope.flowParameters[index].value) {
      $scope.flowParameters[index].value = JSON.stringify(new Map());
      return;
    }

    var nameList = [];
    var dnList = [];
    var paramMap = JSON.parse($scope.flowParameters[index].value);
    var paramScope = $('#parameterTable').scope();
    for (var dn in paramMap) {
      if (Object.prototype.hasOwnProperty.call(paramMap, dn)) {
        dnList.push(dn);
        nameList.push(paramMap[dn]);
      }
    }
    paramScope['neInstance_' + index].attr('value', nameList.join());
  };

  $scope.popSelectNeInstance = function(index) {
    // 获取已经被选中的网元实例
    $scope.selectedNodes = [];
    var selectedNeList = [];
    if ($scope.flowParameters[index].value) {
      var neObj = JSON.parse($scope.flowParameters[index].value);
      selectedNeList = angular.copy(Object.keys(neObj));
    }

    var options = {
      title: I18n.get('cmp.healing.flow.chose.ne.instance.title'),
      contentType: 'complex',
      content: '/dvihealingwebsite/app/healing/flow/selectNeInstance.html',
      modal: true,
      draggable: true,
      resizable: false,
      height: 450,
      width: 1024,
      frameBorder: 0,
      buttonRequired: true,
      listeners: {
        ok: function() {
          $scope.afterSelectNeInstance(index);
          eview.widgets.Window.get('selectNeInstance').dispose();
          $scope.canCloseParamWin = true;
        },
        cancel: function() {
          eview.widgets.Window.get('selectNeInstance').dispose();
          $scope.canCloseParamWin = true;
        },
        windowClose: function() {
          $scope.canCloseParamWin = true;
        },
      },
    };
    var win = eview.widgets.Window.create('selectNeInstance', options);
    win.setData('selectedNeList', selectedNeList);
    win.show();
    $scope.canCloseParamWin = false;
  };

  $scope.afterSelectNeInstance = function(index) {
    let tabelScope = $('#parameterTable').scope();
    // 处理被选中的dn，如果父节点被选中，则只计入父节点，子节点不计入选中，如果子节点只是部分选中，则直接记录子节点，父节点不计入
    $scope.filterSelectedNodes();
    $scope.selectedNeList = $scope.selectedNodes;
    // 将选中的dn写入网元实例流程参数
    // nameList用于前台展示
    let nameList = [];
    let valueMap = {};
    $scope.selectedNeList.forEach(function(neNode) {
      valueMap[neNode.id] = neNode.label;
      nameList.push(neNode.label);
    });
    $scope.flowParameters[index].value = JSON.stringify(valueMap);

    // 将选中的网元渲染至输入框
    tabelScope['neInstance_' + index].attr('value', nameList.join());
  };

  $scope.filterSelectedNodes = function() {
    if (neTreeScope.isByType) {
      $scope.selectedNodes = angular.copy(Object.values(neTreeScope.dnSelectTable));
    } else {
      $scope.selectedNodes = angular.copy(neTreeScope.neInstanceTreeRootNode.getCheckedNodes('fullyChecked'));
    }
    if (!$scope.selectedNodes || !$scope.selectedNodes.length) {
      return null;
    }
    $scope.selectedNodes.forEach(function(node) {
      $scope.deleteChildren(node);
    });

    return $scope.selectedNodes;
  };

  $scope.deleteChildren = function(node) {
    if (node.children && node.children.length) {
      node.children.forEach(function(child) {
        $scope.selectedNodes.forEach(function(item, index) {
          if (item.id === child.id) {
            $scope.selectedNodes.splice(index, 1);
          }
        });
        $scope.deleteChildren(child);
      });
    }
  };

  // 渲染子级取值
  $scope.setChildrenData = function(event, id, val) {
    var idList = id.split('_');
    var paramIndex = idList[idList.length - 1];
    var paramName = $scope.flowParameters[paramIndex].name;
    $scope.getChildData(val, paramName);
    $scope.setParamChange(paramIndex, paramName);
    $scope.$apply();
  };

  $scope.setParaChange = function() {
    $scope.isParaChange = true;
  };

  $scope.setParamChange = function(indexNum, paramName) {
    $scope.isParaChange = true;
    $scope.flowParameters.forEach(function(item) {
      if (item.name === paramName) {
        if (item.flowParamType !== 'CASCADE') {
          item.valueIndex = indexNum;
        } else {
        }
      }
    });
  };

  // paramsName选项名，name参数名
  $scope.getChildData = function(paramsName, name) {
    $scope.paramsName = paramsName;
    $scope.flowParameters.forEach(function(item) {
      var childrenList = [];
      var selectedChildren = null;
      var selectedChildList = [];
      var selectedParamValue = null;
      var isInitedFlag = false;
      if (item.name === name) {
        item.paramsObj = [];
        item.params.some(function(param) {
          if (param.paramsName === $scope.paramsName) {
            param.value.forEach(function(val) {
              item.paramsObj.push(val);
            });
            return true;
          }
          return false;
        });
        // 带初始值
        if ($scope.selectedParameters && $scope.selectedParameters.length > 0) {
          for (var j = 0; j < $scope.selectedParameters.length; j++) {
            if ($scope.selectedParameters[j].flowParamType === 'CASCADE') {
              selectedParamValue = JSON.parse($scope.selectedParameters[j].value);
              if ($scope.selectedParameters[j].name === name && selectedParamValue.paramName === $scope.paramsName) {
                isInitedFlag = true;
                selectedChildList = selectedParamValue.childRenValue;
                break;
              }
            }
          }
          for (var l = 0; l < selectedChildList.length; l++) {
            var childType = selectedChildList[l].type;
            var valueLibrary = JSON.parse(item.valueLibrary);
            if (childType === 'key_value') {
              var endValue = {};
              var selectedValues = selectedChildList[l].paramValue.split(',');
              var valueList = $scope.getValueList(valueLibrary, selectedChildList[l].paramName);

              var keyList = [];
              var valList = [];
              for (let index in selectedValues) {
                if (!Object.prototype.hasOwnProperty.call(selectedValues, index)) {
                  continue;
                }
                for (let k in valueList) {
                  if (!Object.prototype.hasOwnProperty.call(valueList, k)) {
                    continue;
                  }
                  if (selectedValues[index] === valueList[k].value) {
                    keyList.push(valueList[k].label);
                    valList.push(valueList[k].value);
                  }
                }
              }
              endValue.label = keyList.join();
              endValue.value = valList.join();
              selectedChildList[l].endValue = endValue;
              selectedChildList[l].value = valueList;
            } else if (childType === 'enumtype' || childType === 'scriptType') {
              selectedChildList[l].endValue = selectedChildList[l].paramValue;
              selectedChildList[l].value = $scope.getValueList(valueLibrary, selectedChildList[l].paramName);
              selectedChildList[l].value.forEach(function(val) {
                val.value = val.label;
              });
            } else {
              selectedChildList[l].endValue = selectedChildList[l].paramValue;
              selectedChildList[l].value = selectedChildList[l].endValue;
            }
          }
        }
        if (!isInitedFlag) {
          // 不带初始值
          childrenList = item.params;
          for (var i = 0; i < childrenList.length; i++) {
            if (childrenList[i].paramsName === $scope.paramsName || !$scope.paramsName) {
              selectedChildren = childrenList[i];
              $scope.paramsName = childrenList[i].paramsName;
              selectedChildList = selectedChildren.value;
              break;
            }
          }
          for (var k = 0; k < selectedChildList.length; k++) {
            if (selectedChildList[k].type === 'string' || selectedChildList[k].type === 'password' || selectedChildList[k].type === 'datetype' || selectedChildList[k].type === 'ip_tree') {
              selectedChildList[k].endValue = selectedChildList[k].value;
            } else if (selectedChildList[k].type === 'enumtype' || selectedChildList[k].type === 'scriptType') {
              selectedChildList[k].endValue = selectedChildList[k].value[0].value;
            } else if (selectedChildList[k].type === 'key_value') {
              var endValue = {};
              endValue.label = selectedChildList[k].value[0].label;
              endValue.value = selectedChildList[k].value[0].value;
              selectedChildList[k].endValue = endValue;
            }
          }
        }

        item.paramsObj.forEach(function(origin) {
          selectedChildList.forEach(function(val) {
            if (origin.paramName === val.paramName) {
              origin.value = val.value;
              origin.endValue = val.endValue;
              origin.selectMode = val.selectMode ? val.selectMode : 'multi';
            }
          });
        });
      }
    });
    setTimeout(function() {
      var tableScope = $('#parameterTable').scope();
      for (var i = 0; i < $scope.flowParameters.length; i++) {
        if ($scope.flowParameters[i].flowParamType === 'CASCADE') {
          for (var j = 0; j < $scope.flowParameters[i].paramsObj.length; j++) {
            if ($('#enumtype_' + i + '_' + j).length > 0) {
              tableScope['enumtype_' + i + '_' + j].attr('values', $scope.flowParameters[i].paramsObj[j].value);
              tableScope['enumtype_' + i + '_' + j].attr('select-value', $scope.flowParameters[i].paramsObj[j].endValue);
              tableScope['enumtype_' + i + '_' + j].setSelectItemsByLabel($scope.flowParameters[i].paramsObj[j].endValue.split(','));
            }
            if ($('#key_value_' + i + '_' + j).length > 0) {
              tableScope['key_value_' + i + '_' + j].attr('values', $scope.flowParameters[i].paramsObj[j].value);
              tableScope['key_value_' + i + '_' + j].attr('select-value', $scope.flowParameters[i].paramsObj[j].endValue.value);
              tableScope['key_value_' + i + '_' + j].setSelectItemsByLabel($scope.flowParameters[i].paramsObj[j].endValue.label.split(','));
            }
            if ($('#confirmCasTime_' + i + '_' + j).length > 0) {
              tableScope['confirmCasTime_' + i + '_' + j].setUTCDateTime($scope.convertTimeToTimestamp($scope.flowParameters[i].paramsObj[j].value));
            }
          }
        }
      }
    }, 100);
  };

  $scope.getValueList = function(valueLibrary, paramName) {
    var valueList = [];
    valueLibrary.forEach(function(val) {
      if (val.paramsName === $scope.paramsName) {
        val.paramList.forEach(function(param) {
          if (param.paramName === paramName) {
            let value = param.value;
            if (param.type === 'scriptType' && param.selectValues) {
              value = param.selectValues.split(';');
            }

            value.forEach(function(item) {
              var valueArr = item.split(':');
              valueList.push({
                label: valueArr[0],
                value: valueArr[1],
              });
            });
          }
        });
      }
    });

    return valueList;
  };

  // 脚本型参数页面的第一层数据发生变化，为firstValue\secondValueList更新数据
  $scope.setScriptCascadeData = function(event, id, val) {
    let idList = id.split('_');
    let paramIndex = idList[idList.length - 1];
    let item = $scope.flowParameters[paramIndex];

    if (event.checked) {
      // 勾选
      item.firstValue.push(item.firstValueList[event.index].paramId);
      $scope.appendSecondValueList(item, event.value);
    } else {
      // 去勾选
      item.firstValue.splice(item.firstValue.indexOf(event.value), 1);
      $scope.removeSecondValueList(item, event.value);
    }

    $scope.$apply();
    setTimeout(function() {
      let tableScope = $('#parameterTable').scope();
      for (let i = 0; i < item.secondValueList.length; i++) {
        if (!tableScope['script_second_value_' + paramIndex + '_' + i]) {
          continue;
        }

        // 给第二层的已勾选和下拉框列表的控件赋值
        tableScope['script_second_value_' + paramIndex + '_' + i].attr('values', item.secondValueList[i].children);
        tableScope['script_second_value_' + paramIndex + '_' + i].attr('select-value', item.secondValueList[i].value);
        tableScope['script_second_value_' + paramIndex + '_' + i].setSelectItemsByValue(item.secondValueList[i].value);

        $scope.setScriptSecondSelectAll(tableScope, paramIndex, i);
      }
    }, 100);
  };

  // 勾选/去勾选第二层的某个选项
  $scope.setScriptSecondCascadeData = function(event, id, val) {
    let idList = id.split('_');
    let paramIndex = idList[idList.length - 2];
    let item = $scope.flowParameters[paramIndex];
    let tableScope = $('#parameterTable').scope();
    setTimeout(function() {
      for (let i = 0; i < item.secondValueList.length; i++) {
        item.secondValueList[i].value = tableScope['script_second_value_' + paramIndex + '_' + i].getValue();
      }
    }, 100);
  };

  // 脚本型参数页面的第一层数据发生变化，为firstValue\secondValueList更新数据
  $scope.setScriptCascadeDataTimer = function(event, id, val) {
    $scope.setScriptCascadeData(event, id, val, $('#parameterTable').scope());
  };

  // 勾选/去勾选第二层的某个选项
  $scope.setScriptSecondCascadeDataTimer = function(event, id, val) {
    $scope.setScriptCascadeData(event, id, val, $('#parameterTable').scope());
  };

  // 是否为脚本型参数二层展示模式，二层展示模式时，selectValues为一个json数组，一层展示为普通的数组
  $scope.isScriptCascadeMode = function(selectValues) {
    return selectValues && selectValues.startsWith('[');
  };

  // // 是否展示脚本型参数的第二层
  $scope.showScriptCascadeRow = scriptParamHelper.showScriptCascadeRow($scope);

  $scope.popSelectIps = selectIhealingServerBase.popSelectIps($scope);

  $scope.beforeSelectFlowHostIp = selectIhealingServerBase.beforeSelectFlowHostIp($scope);

  $scope.afterSelectFlowHostIp = selectIhealingServerBase.afterSelectFlowHostIp($scope);

  $scope.queryFileConfig = function() {
    ajax('POST', 'text', JSON.stringify(),
      '/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryUploadFileConfig',
      function(data) {
        $scope.fileConfig = JSON.parse(data);
        var types = ['txt', 'properties', 'csv', 'log', 'zip'];
        $scope.uploadFileMaxSize = parseInt($scope.fileConfig.maxSizeStr);
        $scope.typeStr = $scope.fileConfig.typeStr;
        $scope.types = $scope.fileConfig.typeStr.split(',');
        $scope.legalFileTypes = $scope.types ? $scope.types : types;
        $scope.$apply();
      }
    );
  };

  $scope.clickUploadBtn = fileParam.clickUploadBtn($scope);
  $scope.uploadFile = fileParam.uploadFile($scope);

  // 弹出选择远程通知对象的界面
  $scope.beforeSelectRecipient = function(paramIndex) {
    var options = {
      title: I18n.get('cmp.healing.flow.choose.notify.object'),
      contentType: 'complex',
      content: '/dvihealingwebsite/app/healing/flow/selectRecipient.html',
      modal: true,
      draggable: true,
      resizable: false,
      height: 500,
      width: 1000,
      frameBorder: 0,
      buttonRequired: true,
      listeners: {
        ok: function() {
          $scope.afterSelectRecipient(paramIndex);
          $scope.canCloseParamWin = true;
        },
        cancel: function() {
          win.dispose();
          $scope.canCloseParamWin = true;
        },
        windowClose: function() {
          $scope.canCloseParamWin = true;
        },
      },
    };
    var win = eview.widgets.Window.create('selectedRecipient', options);
    // 将已选收件人传到弹窗页面
    $scope.selectedUserList = [];
    $scope.selectedGroupList = [];
    var valueList = $scope.flowParameters[paramIndex].value;
    if (valueList && valueList.length) {
      valueList.forEach(function(value) {
        if (value[0] === 'g') {
          $scope.selectedGroupList.push(value);
        }
        if (value[0] === 'u') {
          $scope.selectedUserList.push(value);
        }
      });
    }
    win.setData('selectedUserList', $scope.selectedUserList);
    win.setData('selectedGroupList', $scope.selectedGroupList);
    win.setData('selectType', 'user');
    win.show();
    $scope.canCloseParamWin = false;
  };

  $scope.afterSelectRecipient = function(paramIndex) {
    let selectedNameList = selectRecipientBase.getSelectNameList($scope, emailScope, paramIndex);

    // 渲染页面
    var flowTableScope = $('#parameterTable').scope();
    if (flowTableScope) {
      flowTableScope['notifyListId_' + paramIndex].attr('value', selectedNameList.join(','));
    }

    eview.widgets.Window.get('selectedRecipient').dispose();
    $scope.$apply();
  };

  $scope.queryFlowParamsConfig = function() {
    var request = { configKeyList: ['maxEmailCount'] };
    ajax('POST', 'json', JSON.stringify(request), '/rest/dvihealingwebsite/v1/dvflowviewservice/queryConfig', function(data) {
      if (data) {
        $scope.maxEmailCount = Number(data.maxEmailCount) || 200;
      }
    });
  };

  $scope.clearSelection = function(selectorId, index) {
    $scope.flowParameters[index].value = '';
    let tableScope = $('#parameterTable').scope();
    tableScope[selectorId].unSelectAll();
  };

  $scope.popSelectAccount = selectAccountBase.popSelectAccount($scope);

  $scope.selectPresentDateParam = function(i, j) {
    if (j) {
      $scope['confirmCasTime_' + i + '_' + j].setUTCDateTime(new Date());
    } else {
      $scope['confirmTime_' + i].setUTCDateTime(new Date());
    }
  };
}
