
window._scope = null;
(function() {
  'use strict';
  $CmpApp.directive('notifychangeListener', function() {
    return {
      restrict: 'ACE',
      scope: false,
      link: function(scope, elem, attrs) {
        elem.on('notifychange', function(event) {
          scope.$eval(attrs.notifychangeListener, {
            $event: event,
          });
        });
      },
    };
  });
})();

function CreateflowCtrl($scope, $timeout, $q, RefreshService, $rootScope, $UI) {
  dataCheckFunc($scope);
  _scope = $scope;
  // 内部对象，仅用于交互与显示效果
  $scope.murmur = {};
  $scope.englishList = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '#'];
  const dateParamToShowValueMap = {
    d: I18n.get('cmp_healing_flow_words_day'),
    h: I18n.get('cmp_healing_flow_words_hour'),
    m: I18n.get('cmp_healing_flow_words_minute'),
  };

  $scope.paramValueDivMap = {
    STRINGTYPE: stringParam.getStringDivFunc,
    DATETYPE: dateParamBase.getDateParamDivFunc,
    PVALUETYPE: stringParam.getPValueDivFunc,
    ENUMTYPE: selectionParam.getEnumDivFunc,
    SCRIPTTYPE: scriptParam.getScriptParamDivFunc,
    ENUMRATE_KEY_VALUE: selectionParam.getKeyValueDivFunc,
    IP_TREE: ipListParam.getIpListParamDivFunc,
    CASCADE: cascadeParam.getCascadeParamDivFunc,
    REMOTE_NOTIFY_OBJ: remoteNotifyBase.getRemoteNotifyParamDivFunc,
    DN_TYPE: neInstanceParam.getDnTypeParamDivFunc,
    DB_ACCOUNT: dbAccountParam.getDBAccountParamDivFunc,
    UPLOAD_FILE: flowParamTable.getUploadFIleDiv,
  };

  $scope.init = function(flowType) {
    $scope.clear();
    $scope.canConfig = {};
    $scope.initparamView(flowType);
    // 查询文件上传流程参数配置
    $scope.queryUploadFileConfig();

    if (flowType === 'createFlow') {
      $scope.$Model = {};
      // url中判断是否是复制模式
      $scope.$Model.isCopy = searchUrlParam('mode') === 'copy';
    } else {
      $scope.$Model.isCopy = $scope.$Model.flagIsCopy;
    }
    $scope.page = {};
    $scope.clickCounts = 0;
    $scope.alowCreate = true;
    $scope.canSelectHost = true;
    $scope.trDraggable = true;

    $scope.$Model.flagIsCopy = false;
    $scope.$Model.isSelectGlobal = false;

    let ipTest = {
      ipstr: '',
    };
    $scope.ipTest = ipTest;

    RefreshService.setAllFalse();

    $scope.isProcessEditorOpen = true;
    $scope.isEventEditoreOpen = true;
    $scope.isFlowParameterEditorOpen = true;

    // 远程通知参数
    $scope.selectedUserList = [];
    $scope.selectedGroupList = [];
    $scope.selectType = 'user';

    // 文件上传流程参数
    $scope.uploadFileParams = [];
    // 审核页面查看流程时传入的流程id
    $scope.curFlowId = null;
    $scope.queryFlowParamsConfig();

    $scope.source = '';

    // 如果是修改或者查看流程
    if ((searchUrlParam('id') || ($scope.$Model.lookFlowId)) && !$scope.$Model.isCopy) {
      $scope.isModify = true;
      let flowId = searchUrlParam('id') ? decodeURIComponent(searchUrlParam('id')) : undefined;
      if ($scope.$Model.lookFlowId) {
        flowId = $scope.$Model.lookFlowId;
        $scope.curFlowId = flowId;
        $scope.$Model.lookFlowId = null;
        $scope.isLook = true;
        $scope.mode = 'view';
      } else {
        $scope.mode = 'modify';
      }

      $scope.queryFlowDetail(flowId).then($scope.handleFlowWhenViewOrModify);
    }
    // 如果是复制
    else if (searchUrlParam('id') && $scope.$Model.isCopy) {
      $scope.mode = 'copy';
      let flowId = searchUrlParam('id') ? decodeURIComponent(searchUrlParam('id')) : undefined;
      $scope.queryFlowDetail(flowId).then($scope.handleFlowWhenCopy);
    } else if (flowType === 'start') {
      $scope.mode = 'execute';
      $scope.murmur.user = 'uniagent_custom';
      $scope.murmur.userType = 'DEFINITION';

      $scope.initEventConfig();
      $scope.loaded = true;
    } else {
      // 初始化流程基本信息的默认值
      $scope.mode = 'create';
      $scope.flowParameters = [];
      $scope.flow = {
        name: 'flow_' + getCurrentDateStr(),
        executeMode: 'SYNC',
        type: 'DIAGNOSE',
      };
      $scope.$Model.bpmidediagram = {
        nodes: [],
        paths: [],
      };
      $scope.murmur.user = 'uniagent_custom';
      $scope.murmur.userType = 'DEFINITION';

      $scope.initEventConfig();
      $scope.loaded = true;
    }

    $scope.querySolution();
    $scope.queryAtomSolution();
    $scope.queryCategories();
  };

  $scope.handleFlowWhenViewOrModify = function(data) {
    // 基本信息
    $scope.flow = data.basicInfo;
    $scope.flowCategory = $scope.changeCategory($scope.flow.category);

    if (!$scope.isLook) {
      if ($scope.$Model.flow) {
        $scope.flow.deployedId = $scope.$Model.flow.basicInfo.deployedId;
        $scope.$Model.flow = null;
      }
    }


    // 流程定义
    $scope.$Model.bpmidediagram = $scope.transformFlowDefinition(data.definition);
    // update原子中api的信息
    $scope.updateFlowModelApiInfo();

    $scope.getFlowParameters(data);

    // 事件
    let event = data.events;
    if (event) {
      event.forEach(function(item) {
        if (item.type === 'TIMER') {
          $scope.$Model.timerEvent = item;
        } else {
          $scope.$Model.alarmEvents = $scope.$Model.alarmEvents || [];
          $scope.$Model.alarmEvents.push(item);
        }
      });
    } else if (data.eventType) {
      if (data.eventType === 'TIMER') {
        $scope.$Model.timerEvent = { type: data.eventType };
        $scope.$Model.eventType = 'CRONTAB';
      } else {
        $scope.$Model.eventType = data.eventType;
      }
    }

    $scope.initEventConfig();
    // 加载完成
    $scope.loaded = true;
  };

  $scope.handleFlowWhenCopy = function(data) {
    // 基本信息
    $scope.flow = data.basicInfo;
    let flowName = $scope.flow.name;
    flowName = flowName + '_copy';
    $scope.flow.name = flowName;
    $scope.flow.deployedId = null;
    $scope.flowCategory = $scope.changeCategory($scope.flow.category);

    // 流程定义
    $scope.$Model.bpmidediagram = $scope.transformFlowDefinition(data.definition);

    // update原子中api的信息
    $scope.updateApiInfo();

    $scope.getFlowParameters(data);

    // 事件
    let event = data.events;
    if (event) {
      event.forEach(function(item) {
        if (item.type === 'TIMER') {
          $scope.$Model.timerEvent = item;
        } else {
          $scope.$Model.alarmEvents = [];
        }
      });
    } else if (data.eventType) {
      if (data.eventType === 'TIMER') {
        $scope.$Model.timerEvent = { type: data.eventType };
        $scope.$Model.eventType = 'CRONTAB';
      } else {
        $scope.$Model.eventType = data.eventType;
      }
    }

    $scope.initEventConfig();

    // 加载完成
    $scope.loaded = true;
  };

  $scope.dropPrefix = function(value) {
    remoteNotifyBase.dropPrefix($scope, value);
  };

  // 根据查询接口返回数据生成页面流程参数信息
  $scope.getFlowParameters = function(data) {
    flowParamTable.getFlowParameters($scope, $q, data);
  };

  $scope.initparamView = function(flowType) {
    // 流程图可编辑时加上删除按钮
    let deleteBtn = '';
    if (flowType === 'createFlow') {
      deleteBtn = ',3';
    }
    // 图元的定义
    jBME.Diagram.MetaData = {
      start: {
        name: 'Start',
        title: 'Start',
        editable: false,
        type: 'start',
        figures: [{
          type: 'round',
          style: 'width:50px;height:50px;',
          context: {
            strokeStyle: '#9fdfff',
            lineWidth: 2,
            fillStyle: '#9fdfff',
          },
        }],
        activeMenuId: String(deleteBtn),

        maxOccurrence: 1,
      },
      end: {
        name: 'End',
        title: 'End',
        editable: false,
        type: 'end',
        figures: [{
          type: 'round',
          style: 'width:50px;height:50px;',
          context: {
            strokeStyle: '#7cffd5',
            lineWidth: 2,
            fillStyle: '#7cffd5',
          },
        }],
        activeMenuId: String(deleteBtn),
        nextnodes: [],
        maxOccurrence: 1,
      },
      gate_way: {
        title: 'Exclusive',
        type: 'gate_way',
        editable: false,
        figures: [{
          type: 'diamond',
          style: 'width:55px;height:55px;',
          context: {
            strokeStyle: '#dfcbff',
            fillStyle: '#dfcbff',
          },
        }],
      },
      incl_gate_way: {
        title: 'Parallel',
        type: 'incl_gate_way',
        editable: false,
        figures: [{
          type: 'diamond',
          style: 'width:55px;height:55px;',
          context: {
            strokeStyle: '#C58489',
            fillStyle: '#C58489',
          },
        }],
      },
      user_task: {
        name: 'usertask',
        title: 'User Task',
        type: 'user_task',
        figures: [{
          type: 'diamond',
          style: 'width:70px;height:70px',
          options: {
            radius: 5,
          },
          context: {
            strokeStyle: '#ffe8c8',
            fillStyle: '#ffe8c8',
          },
        }],
        activeMenuId: String(deleteBtn),
      },
      send_mail: {
        name: 'sendmail',
        title: 'Send Mail',
        type: 'send_mail',
        figures: [{
          type: 'roundrect',
          style: 'width:70px;height:70px',
          options: {
            radius: 5,
          },
          context: {
            strokeStyle: '#00aaff',
            lineWidth: 1,
            fillStyle: '#e8f7ff',
          },
        }],
        activeMenuId: '2' + deleteBtn,
      },
      query_alarm: {
        name: 'queryAlarm',
        title: 'Alarm Query',
        type: 'query_alarm',
        figures: [{
          type: 'roundrect',
          style: 'width:140px;height:40px',
          options: {
            radius: 10,
          },
          context: {
            strokeStyle: '#00aaff',
            lineWidth: 1,
            fillStyle: '#e8f7ff',
          },
        }],
        activeMenuId: '2' + deleteBtn,
      },
      query_pm: {
        name: 'queryPm',
        title: 'Pm Query',
        type: 'query_pm',
        figures: [{
          type: 'roundrect',
          style: 'width:140px;height:40px',
          options: {
            radius: 5,
          },
          context: {
            strokeStyle: '#00aaff',
            lineWidth: 1,
            fillStyle: '#e8f7ff',
          },
        }],
        activeMenuId: '2' + deleteBtn,
      },
      service_task: {
        name: 'servicetask',
        title: 'Service Task',
        editable: false,
        type: 'service_task',
        figures: [{
          type: 'roundrect',
          style: 'width:120px;height:35px',
          options: {
            radius: 5,
          },
          context: {
            strokeStyle: '#00aaff',
            lineWidth: 1,
            fillStyle: '#e8f7ff',
          },
          fill: {
            start: {
              x: 0,
              y: 0,
            },
            end: {
              x: 0,
              y: 1,
            },
            colorStops: [0, '#e8f7ff', 0.5, '#e8f7ff', 1, '#e8f7ff'],
          },
        }],
        activeMenuId: '1,2' + deleteBtn,
      },
      suspend: {
        name: 'suspend',
        title: 'Suspend',
        editable: false,
        type: 'suspend',
        figures: [{
          type: 'diamond',
          style: 'width:70px;height:70px',
          options: {
            radius: 5,
          },
          context: {
            strokeStyle: '#9fdfff',
            fillStyle: '#9fdfff',
          },
        }],
        activeMenuId: '2' + deleteBtn,
      },
      user_input_task: {
        name: 'interact',
        title: 'Interact',
        type: 'user_input_task',
        editable: false,
        figures: [{
          type: 'hexagon',
          style: 'width:60px;height:60px;',
          context: {
            strokeStyle: '#85ccff',
            fillStyle: '#85ccff',
          },
        }],
        activeMenuId: '2' + deleteBtn,
      },
    };
    jBME.Diagram.MetaData.activeMenu = [{
      position: 'top',
      img: '/dvihealingwebsite/images/healing/edit-atom.png',
      handler: jBPMFunc.editAtom,
    },
    {
      position: 'top',
      img: '/dvihealingwebsite/images/healing/edit-icon.png',
      handler: jBPMFunc.editPop,
    },
    {
      position: 'top',
      img: '/dvihealingwebsite/images/healing/delete-icon.png',
      handler: jBPMFunc.remove,
    }];
  };

  $scope.clearFileValue = function() {
    $scope.alowCreate = true;
    $scope.isImgUploaded = false;
    $scope.uploadConfigFile = undefined;
    $scope.uploadLogText = '';
    if ($scope.tip) {
      $scope.tip.hide();
    }
    $('#uploadFile-file').val('');
    $('#uploadFile_border').removeClass('input-border-red ');
  };

  $scope.uploadConfigFiles = function(file) {
    $scope.alowCreate = true;
    $('#uploadFile_border').removeClass('input-border-red');
    if ($scope.tip) {
      $scope.tip.hide();
    }
    $scope.uploadConfigFile = file.files[0];
    $scope.uploadLogText = file.value;
    $scope.$apply();
    flowImg.imgFileSizeValid($scope, file);
    flowImg.imgTypeValid($scope, file);
    flowImg.imgFileNameValid($scope, file);
  };

  $scope.timeChange = function(event) {
    event.trigger('click');
  };

  $scope.convertTimeToTimestamp = function(time) {
    let timeStamp;
    if (typeof time === 'string' && (navigator.userAgent.indexOf('Firefox') > 0 || navigator.userAgent.indexOf('Trident') > 0)) {
      timeStamp = time.replace(' ', 'T');
    } else {
      timeStamp = time;
    }
    return new Date(timeStamp).getTime();
  };

  $scope.setRequestParamValue = function(requestParam, index) {
    let timeScope = angular.element(document.getElementById('dateTimePars')).scope();
    if (requestParam.required || requestParam.required === 'true' || requestParam.required === null) {
      requestParam.value = timestampToTime(timeScope['dateTimeParamId_' + index].getUTCDateTime());
    } else if (requestParam.required || requestParam.required === 'false') {
      requestParam.value = timestampToTime(timeScope['dateTimeParam_' + index].getUTCDateTime());
    } else {
    }
  };

  // 修改流程 复制流程
  $scope.operateFlowClick = function(flow, mode) {
    // 在修改流程之前进行一个判断（如果流程处于审核状态就不跳转）
    let status = flow.basicInfo.reviewStatus === 'pause' || flow.status === 'PAUSED' || flow.status === 'SUSPEND' || flow.status === 'EXECUTING';
    if (status && mode === 'modify') {
      alertTips(I18n.get('cmp.healing.flow.excuteStatus'));
    } else {
      // 跳转到创建流程界面
      let url = '/dvihealingwebsite/index.html#/createflow?id=' + encodeURIComponent(flow.basicInfo.flowId) + '&&mode=' + encodeURIComponent(mode);
      parent.window.ihealingEvent.emit('routerChange', encodeURI(url));
    }
  };

  // 新建流程
  $scope.submitFlow = createFlowBase.submitFlow($scope, $q);

  $scope.closeConditionPop = function() {
    $scope.clickCounts = $scope.clickCounts + 1;
    if ($scope.clickCounts > 1) {
      $('.condition-pop').remove();
    }
  };


  $scope.closeProcessEditor = function() {
    $scope.isProcessEditorOpen = false;
  };

  $scope.openProcessEditor = function() {
    $scope.isProcessEditorOpen = true;
  };

  $scope.closeEventEditor = function() {
    $scope.isEventEditoreOpen = false;
  };

  $scope.openEventEditor = function() {
    $scope.isEventEditoreOpen = true;
  };

  $scope.closeFlowParameterEditor = function() {
    $scope.isFlowParameterEditorOpen = false;
  };

  $scope.openFlowParameterEditor = function() {
    $scope.isFlowParameterEditorOpen = true;
  };

  $scope.switchStatus = function(index, item) {
    // 支持可配置的流程参数，必填时必须展示
    if ($scope.canConfig[item.flowParamType] && item.isRequired) {
      return;
    }
    // 状态取反
    $scope.flowParameters[index].isShow = !item.isShow;
    flowParamTable.refreshParamTable($scope);
  };

  $scope.switchRequired = function(index, item) {
    // 判断是否支持可配置
    if (item.flowParameterSource === 'GLOBAL' || !$scope.canConfig[item.flowParamType]) {
      return;
    }
    // 状态取反
    $scope.flowParameters[index].isRequired = !item.isRequired;
    // 支持可配置的流程参数，必填时必须展示
    if ($scope.flowParameters[index].isRequired) {
      $scope.flowParameters[index].isShow = true;
    }

    flowParamTable.refreshParamTable($scope);
  };

  $scope.addParamBySelf = flowParamTable.addParamBySelf($scope);

  $scope.addParamByUploadedFile = flowParamTable.addParamByUploadedFile($scope);

  // 选择全局参数按钮事件
  $scope.addParamByChooseGlobal = flowParamTable.addParamByChooseGlobal($scope);

  $scope.afterAddGlobalParamHandler = flowParamTable.afterAddGlobalParamHandler($scope);

  $scope.afterEditGlobalParamHandler = flowParamTable.afterEditGlobalParamHandler($scope);

  $scope.modifyParamByChooseGlobal = flowParamTable.modifyParamByChooseGlobal($scope);

  // 修改或复制流程时先取得原流程
  $scope.queryFlowDetail = getInfoHelper.queryFlowDetail($q);

  // 查询流程解决方案
  $scope.querySolution = getInfoHelper.querySolution($scope);

  // 查询原子解决方案
  $scope.queryAtomSolution = getInfoHelper.queryAtomSolution($scope);

  // 查询流程类型
  $scope.queryCategories = getInfoHelper.queryCategories($scope);


  // 删除自定义的图片
  $scope.deleteCustomFlowImg = flowImg.deleteCustomFlowImg($scope);

  $scope.initCodeMirror = scriptParam.initCodeMirror($scope);

  // 弹出编辑json的页面
  $scope.popEditJson = cascadeParam.popEditJson($scope);

  $scope.parsedJson = cascadeParam.parsedJson($scope);

  $scope.importConfig = cascadeParam.importConfig($scope);

  $scope.closeJsonWin = function() {
    $scope.closeDialog('popEditJson');
  };

  // 弹出选择IP的界面
  $scope.popSelectIps = ipListParam.popSelectIps($scope);

  // 弹出选择原子的界面
  $scope.popSelectAtom = atomNode.popSelectAtom($scope);

  $scope.popSelectCommonAtom = toolBoxNode.popSelectCommonAtom($scope);

  $scope.popMailTemplate = function(nodeId) {
    createNodeBaseObj.popMailTemplate($scope, $UI, nodeId);
  };
  $scope.popCommonQueryParamSetting = function(nodeId, queryType) {
    if (queryType === 'query_alarm') {
      createNodeBaseObj.popCommonQueryParamSetting($scope, $UI, nodeId, 'queryAlarmDataSetting', 'alarmQueryCondition', 'confirmAlarmQueryDialog');
    } else {
      createNodeBaseObj.popCommonQueryParamSetting($scope, $UI, nodeId, 'queryPmDataSetting', 'pmQueryCondition', 'confirmPmQueryDialog');
    }
  };
  $scope.popParamTemplate = atomNode.popParamTemplate($scope, $UI);

  // 弹出选择脚本的原子界面
  $scope.scriptSelectAtom = scriptParam.scriptSelectAtom($scope);

  $scope.getDataFromSelectServerPageHandler = selectHostHelper.getDataFromSelectServerPageHandler($scope);

  $scope.afterSelectAtom = function(selectedAtom) {
    $scope.atom = selectedAtom.atom;
    $scope.nodeId = selectedAtom.nodeId;
    $scope.murmur = selectedAtom.murmur;
    $scope.onConfirmAtom();
  };

  $scope.initAtomConfig = atomNodeParam.initAtomConfig($scope);

  // 新建原子时初始化原子配置参数
  $scope.initAtomConfigParams = atomNodeParam.initAtomConfigParams($scope);

  $scope.filterLetterArray = function(atoms, letter) {
    if (atoms) {
      return atoms.filter(function(atom) {
        if (atom) {
          if (letter !== '#') {
            return atom.name.charAt(0).toUpperCase() === letter.toUpperCase();
          }
          let firChar = atom.name.charAt(0).toLowerCase();
          return !(firChar >= 'a' && firChar <= 'z');
        }
        return false;
      });
    } else {
      return false;
    }
  };

  // 查原子列表
  $scope.queryAtomList = getInfoHelper.queryAtomList($scope, $q);

  $scope.goBelievable = function(type) {
    $scope.source = type;
    $scope.queryAtomList($scope.atomSolution);
  };

  $scope.onClickSearch = function(solution, searchAtomName) {
    $scope.queryAtomList(solution, searchAtomName);
  };

  $scope.onInputEnter = function(jEvent, solution, searchAtomName) {
    if (event.keyCode === '13') {
      $scope.onClickSearch(solution, searchAtomName);
    }
  };

  $scope.searchAtomByNameAndMoType = function(atomName, moType) {
    if (atomName && moType) {
      for (let i = 0, len = $scope.atoms.length; i < len; i++) {
        if ($scope.atoms[i].name === atomName && $scope.atoms[i].moType === moType) {
          return $scope.atoms[i];
        }
      }
    }
    return null;
  };

  // 选中原子后
  $scope.onSelectAtom = atomNodeParam.onSelectAtom($scope);

  // 弹出修改原子参数的界面
  $scope.popAtomParam = atomNodeParam.popAtomParam($scope);

  // 查询原子详情
  $scope.queryAtomDetail = getInfoHelper.queryAtomDetail($q);

  $scope.popProParam = pauseNode.popProParam($scope);

  $scope.initNodepro = pauseNode.initNodepro($scope);

  /** *
   * 从API库查询api的信息
   * @param apiId
   * @returns {*}
   */
  $scope.queryApiDetail = atomApiDetail.queryApiDetail($q);

  $scope.changeFilterOfflineHost = atomExecuteServer.changeFilterOfflineHost($scope);

  $scope.changeFilterOffAtomDetails = atomExecuteServer.changeFilterOffAtomDetails($scope);

  $scope.changeHostSelectionMode = function(hostSelectionMode) {
    $scope.hostSelectionMode = hostSelectionMode;
  };

  $scope.onConfirmAtomButton = atomNode.onConfirmAtomButton($scope);

  // 选中某流程类型
  $scope.onCategoryChanged = function(category) {
    if (category) {
      $scope.flow.category = category;
    } else {
      delete $scope.flow.category;
    }
    $scope.flowId = null;
  };

  // 选中原子确认后，所有原子数据设置完毕，确定
  $scope.onConfirmAtom = function() {
    if (!$scope.nodeId) {
      // 如果为新增
      atomNode.addNode($scope, $UI);
    } else {
      // 如果为修改原子
      atomNode.modifyNode($scope, $UI);
    }
    $scope.clearAtomConfig();
  };

  $scope.getNodeType = function(nodeId) {
    let nodes = $scope.$Model.bpmidediagram.nodes;
    let nodeType = '';
    nodes.some(function(node) {
      if (node.id === nodeId) {
        nodeType = node.type;
      }
    });
    return nodeType;
  };

  $scope.getPauseTimeTitle = pauseNode.getPauseTimeTitle($scope);

  // 选中原子确认后，所有原子数据设置完毕，确定
  $scope.onConfirmPro = pauseNode.onConfirmPro($scope);

  $scope.pageSelectApi = atomApiDetail.pageSelectApi($scope);

  $scope.onPrevious = atomNodeParam.onPrevious($scope);


  $scope.checkInputArr = paramValidateHelper.checkInputArr($scope);

  $scope.setUserInfo = atomNodeParam.setUserInfo($scope);

  // 确认原子的参数后,即将进入第三步
  $scope.onConfirmParam = atomNode.onConfirmParam($scope);

  // 查看原子信息时，从Atom的ips中得到ips的信息,设置$scope.murmur.ips的值
  $scope.setMurmurObj = atomExecuteServer.setMurmurObj($scope);

  // 设置选择的主机的table数据
  $scope.setServerTable = atomExecuteServer.setServerTable($scope);

  // 查询atom所选择主机的状态
  $scope.queryAtomIpInfos = getInfoHelper.queryAtomIpInfos($scope, $q);

  // 保存ips的信息，将murmur中的信息转为接口中atom所需要的格式
  $scope.setAtomIpsInfo = atomExecuteServer.setAtomIpsInfo($scope);

  $scope.onClickConfirmMotype = function($scope) {
    $scope.atom.moType = $scope.atomMoTypeInside;
  };

  // 从jBME的bpm模型转换为故障诊断的流程定义模型
  $scope.getFlowDefinition = getFlowModelHelper.getFlowDefinition($scope);

  // 根据界面模型设置传递给后台的ruleContent
  $scope.setRuleContent = atomExecuteServer.setRuleContent($scope);

  // 将ruleContent转换成界面模型
  $scope.transferRuleContent = atomExecuteServer.transferRuleContent($scope);

  // 将definition转换为jBME内的模型
  $scope.transformFlowDefinition = getJbmeModelHelper.transformFlowDefinition($scope, $UI);

  $scope.updateAtomStatus = atomNode.updateAtomStatus($scope, $UI);

  $scope.changeCategory = flowCategoryBase.changeCategory($scope);

  $scope.parseFlowCategory = flowCategoryBase.parseFlowCategory($scope);

  $scope.getFlowRequest = createFlowBase.getFlowRequest($scope);

  $scope.checkInputArrLength = paramValidateHelper.checkInputArrLength($scope);

  $scope.validatePageInfo = paramValidateHelper.validatePageInfo($scope);

  $scope.validateFlowNodes = paramValidateHelper.validateFlowNodes($scope);

  $scope.getStringLength = stringParam.getStringLength();

  $scope.validateFlowInfo = basicInfoValidator.validateFlowInfo($scope);


  // 流程参数校验
  $scope.checkFlowParameters = paramValidateHelper.checkFlowParameters($scope);

  $scope.checkParam = paramValidateHelper.checkParam($scope);

  // 校验联级型（CASCADE）流程参数中的paramList
  $scope.checkParamList = cascadeParam.checkParamList($scope);

  $scope.isDate = dateParamBase.isDate();

  $scope.isJSON = cascadeParam.isJSON();

  $scope.checkParamsName = paramValidateHelper.checkParamsName($scope);

  // 判断数组中是否有相同的元素
  $scope.isSameElement = paramValidateHelper.isSameElement();

  $scope.showSaveMsg = createFlowBase.showSaveMsg($scope);

  $scope.getAtomName = function(atomName) {
    if (atomName.length > 95) {
      return atomName.substring(0, 90) + '...';
    }
    return atomName;
  };

  $scope.getLetterId = function(letter) {
    if (letter === '#') {
      return 'specialId';
    }
    return letter;
  };

  $scope.moveScroll = function(letter) {
    let scroll_offset = $('#' + $scope.getLetterId(letter)).offset().top;
    let left_atom_offset = $('#left-atom-list-div').offset().top;
    let scroll_top = $('#left-atom-list-div').scrollTop();
    $('#left-atom-list-div').animate({
      scrollTop: scroll_top + (scroll_offset - left_atom_offset),
    }, 20);
  };

  // 修改流程
  $scope.modifyFlow = createFlowBase.modifyFlow($scope, $q);

  // 部署流程
  $scope.deploy = function(id) {
    let requestParam = {
      flowIds: [id],
    };
    let defer = $q.defer();
    ajax('POST', 'json', JSON.stringify(requestParam), '/rest/dvihealingwebsite/v1/dvihealingflowviewservice/deployFlow', function(data) {
      defer.resolve();
    });
    return defer.promise;
  };

  $scope.redirect = function(mode) {
    $scope.clear();
    let url = '/dvihealingwebsite/index.html#/flowlist';
    if (mode) {
      url = url + '?lastFlow=' + mode;
    }
    parent.window.ihealingEvent.emit('routerChange', encodeURI(url));
  };

  // 清理工作
  $scope.clear = function() {
    if ($scope.$Model && $scope.$Model.alarmEvents) {
      delete $scope.$Model.alarmEvents;
    }
    $scope.alarmRequire = false;
    $scope.selectedIps = [];
  };

  $scope.clearAtomConfig = atomNodeParam.clearAtomConfig($scope);

  $scope.clearProConfig = pauseNode.clearProConfig($scope);


  // 原子配置界面，添加主机或应用类型
  $scope.addHost = atomExecuteServer.addHost($scope);

  $scope.addMotype = atomExecuteServer.addMotype($scope);


  // ******************************建立ip树********************************************************************

  // 查询topMoType列表
  $scope.querySolutionTypes = getInfoHelper.querySolutionTypes($scope, $q);

  $scope.onMotypeTopMoTypeChange = moTypeTree.onMotypeTopMoTypeChange($scope);

  $scope.drawMotypeTree = moTypeTree.drawMotypeTree($scope);

  $scope.queryFirstTree = moTypeTree.queryFirstTree($scope, $q);

  $scope.queryTree = moTypeTree.queryTree($q);

  // 查资源列表
  $scope.queryResources = getInfoHelper.queryResources($q);

  $scope.drawTable = moTypeTree.drawTable($scope);

  // 事件配置相关js
  $scope.initEventConfig = eventConfigBase.initEventConfig($scope);

  $scope.checkFireTime = eventConfigBase.checkFireTime($scope);

  $scope.modifyAlarmInfo = configAlarmEvent.modifyAlarmInfo($scope);

  $scope.addAlarmEventHandler = configAlarmEvent.addAlarmEventHandler($scope);

  $scope.initAlarmConfig = function() {
    $scope.$Model = eview.widgets.Window.get('addAlarmEvent').getData('alarmData');
  };

  $scope.onClickYes = function() {
    $scope.onAddAndModify();
  };

  $scope.onAddAndModify = configAlarmEvent.onAddAndModify($scope);

  $scope.onModifyAlarmEvent = configAlarmEvent.onModifyAlarmEvent($scope);

  $scope.isExist = configAlarmEvent.isExist($scope);

  $scope.isUsed = configAlarmEvent.isUsed($scope);

  $scope.clearAlarmConfig = configAlarmEvent.clearAlarmConfig($scope);

  $scope.redirectToHelpPage = redirectHelper.redirectToHelpPage();

  $scope.onClickCancel = function() {
    $scope.clearAlarmConfig();
    eview.widgets.Window.get('addAlarmEvent').dispose();
  };

  $scope.continueCreatePath = jbmeModelBase.continueCreatePath($UI);

  $scope.setAlign = jbmeModelBase.setAlign($UI);

  // 关闭页面
  $scope.closeLookFlow = function() {
    $scope.$Model.isLookFlowMasklayerShow = false;
    $scope.isLook = false;
    $scope.$Model.bpmidediagram = null;
    $scope.$apply();

    $rootScope.$emit('isLookFlowMasklayerShowMasklayer', $scope.$Model.isLookFlowMasklayerShow, $scope.isLook);
  };

  /** *
   * 将从api库中获取到的api信息转成流程中存储api的结构
   * api在Flow中和api库中的数据结构稍有不同，
   * @param apiFromStore 从api库中获取的api信息
   * @returns {*|XML|XMLList}
   */
  $scope.apiFlowFrowApiStore = atomApiDetail.apiFlowFrowApiStore();


  /** *
   * 流程中api的请求参数和请求体及其source保持不变
   * @param apiSrc 流程中api信息
   * @param apiDst 从API库中获取的api信息
   * @returns {*}
   */
  $scope.remainApiParamsBodyInFlow = atomApiDetail.remainApiParamsBodyInFlow();


  $scope.updateFlowModelApiInfo = atomApiDetail.updateFlowModelApiInfo($scope);

  /**
   *  更新原子的API信息
   *
   *  查询完流程信息就对所有api的信息进行更新
   *  这样可以避免用户如果没有查看原子直接保存流程，
   *  也能将最新的API信息刷新到流程的数据中
   */
  $scope.updateApiInfo = atomApiDetail.updateApiInfo($scope);

  // 选择主机之前
  $scope.beforeSelectHost = ipListParam.beforeSelectHost($scope);

  $scope.afterSelectHostIp = ipListParam.afterSelectHostIp($scope);

  $scope.getIpString = ipListParam.getIpString($scope);

  $scope.cancleSelectServerIp = ipListParam.cancleSelectServerIp();

  // 选完主机之后
  $scope.afterSelectHost = atomExecuteServer.afterSelectHost($scope);

  $scope.cancleSelectServer = atomExecuteServer.cancleSelectServer();

  $scope.tableIpListeners = {
    change: selectHostWindow.ipListChangeHandler($scope),
  };

  $scope.solutionListeners = {
    change: selectHostWindow.solutionListChangeHandler($scope),
  };

  $scope.GroupListeners = {
    change: selectHostWindow.groupListChangeHandler($scope),
  };

  /**
   * [setSelectedServerTab 设置展示已选择的主机或分组的tab高亮]
   * @param {[type]} selectedServerList   [description]
   * @param {[type]} selectedSolutionList [description]
   * @param {[type]} selectedGroupList    [description]
   */
  $scope.setSelectedServerTab = selectHostHelper.setSelectedServerTab($scope);

  // 设置服务器列表table的值
  $scope.setServerTableData = selectHostHelper.setServerTableData($scope);

  /**
   * [设置所选择的Group或Solution的spaceId或groupStatus，目前不支持跨域选择主机或分组]
   * @param {[type]} data    [description]
   * @param {[type]} spaceId [description]
   */
  $scope.setGroupsSpaceIdAndStatus = selectHostHelper.setGroupsSpaceIdAndStatus();

  $scope.deleteSelectedServerList = selectHostHelper.deleteSelectedServerList($scope);

  $scope.deleteSelectedGroupList = selectHostHelper.deleteSelectedGroupList($scope);

  $scope.deleteSelectedSolutionList = selectHostHelper.deleteSelectedSolutionList($scope);

  // 删除选中的某个服务器、分组、文件
  $scope.deleteItem = function(param) {
    switch (param.type) {
      case 'ip':
        $scope.deleteSelectedServerList(param);
        break;
      case 'group': {
        $scope.deleteSelectedGroupList(param);
        break;
      }
      case 'solution': {
        $scope.deleteSelectedSolutionList(param);
        break;
      }
      default:
    }
    $scope.$apply();
  };

  /**
   * [deleteUnavailableHosts 删除离线或不受管控的主机]
   * @param  {[type]} deleteOfflineFlag [true是删除离线以及不受管控的主机，false为删除不受管控的主机]
   */
  $scope.deleteUnavailableHosts = function(deleteOfflineFlag) {
    if (!$scope.murmur.ips || !$scope.murmur.ips.ipInfos || !$scope.murmur.ips.ipInfos.ips || $scope.murmur.ips.ipInfos.ips.length < 1) {
      return;
    }

    let ipList = $scope.murmur.ips.ipInfos.ips;
    ipList = ipList.filter(function(ipItem) {
      // 根据deleteOfflineFlag判断是否删除离线的主机
      if (deleteOfflineFlag) {
        return !(ipItem.hostStatus === 3 || ipItem.hostStatus === 2);
      } else {
        return !(ipItem.hostStatus === 3);
      }
    });

    $scope.setServerTableData(ipList, 'ip', 1, 10);
  };

  /**
   * [deleteUnavailableGroups 删除不存在的分组]
   * @param  {[type]} type [manualGroups为自定义分组，solutionGroups为解决方案分组]
   */
  $scope.deleteUnavailableGroups = function(type) {
    if (!$scope.murmur.ips || !$scope.murmur.ips.ipInfos) {
      return;
    }

    let groupData = null;
    if (type === 'manualGroups') {
      groupData = $scope.murmur.ips.ipInfos.manualGroups;
    } else {
      groupData = $scope.murmur.ips.ipInfos.solutionGroups;
    }

    if (!groupData || groupData.length < 1) {
      return;
    }

    groupData = groupData.filter(function(groupItem) {
      return !(groupItem.groupStatus === 1);
    });

    if (type === 'manualGroups') {
      $scope.setServerTableData(groupData, 'group', 1, 10);
    } else {
      $scope.setServerTableData(groupData, 'solution', 1, 10);
    }
  };

  /**
   * [deleteUnavailableServer 删除不可执行的主机及分组]
   * @return {[type]} [description]
   */
  $scope.deleteUnavailableServer = function() {
    $scope.deleteUnavailableHosts(true);
    $scope.deleteUnavailableGroups('manualGroups');
    $scope.deleteUnavailableGroups('solutionGroups');
    $scope.$apply();
  };

  /**
   * [isContainUnavailableServer 判断是否含有不可执行主机、分组]
   * @return {Boolean} [description]
   */
  $scope.isContainUnavailableServer = function() {
    if (!$scope.murmur.ips || !$scope.murmur.ips.ipInfos) {
      return false;
    }

    if ($scope.murmur.ips.ipInfos.ips && $scope.murmur.ips.ipInfos.ips.length > 0) {
      let ipList = $scope.murmur.ips.ipInfos.ips;
      for (let i = 0; i < ipList.length; i++) {
        if (ipList[i].hostStatus === 2 || ipList[i].hostStatus === 3) {
          return true;
        }
      }
    }

    if ($scope.murmur.ips.ipInfos.manualGroups && $scope.murmur.ips.ipInfos.manualGroups.length > 0) {
      let manualGroups = $scope.murmur.ips.ipInfos.manualGroups;
      for (let i = 0; i < manualGroups.length; i++) {
        if (manualGroups[i].groupStatus === 1) {
          return true;
        }
      }
    }


    if ($scope.murmur.ips.ipInfos.solutionGroups && $scope.murmur.ips.ipInfos.solutionGroups.length > 0) {
      let solutionGroups = $scope.murmur.ips.ipInfos.solutionGroups;
      for (let i = 0; i < solutionGroups.length; i++) {
        if (solutionGroups[i].groupStatus === 1) {
          return true;
        }
      }
    }

    return false;
  };

  $scope.goStep = function(stepName) {
    let url = '/dvihealingwebsite/index.html#/' + encodeURIComponent(stepName);
    parent.window.ihealingEvent.emit('routerChange', encodeURI(url));
  };

  $scope.popConfirmUnavailableServer = function(deleteUnavailableServer) {
    if ($scope.isContainUnavailableServer()) {
      let opt = {};
      opt.title = I18n.get('cmp_healing_dashboard_tip');
      opt.confirmDescription = I18n.get('cmp.healing.select.server.delete.infos');
      opt.confirmFun = $scope.deleteUnavailableServer;
      opt.confirmFunPara = null;
      $scope.confirmDialog(opt);
    }
  };

  $scope.popConfirmDeleteServer = function(deleteItem) {
    let opt = {};
    opt.title = I18n.get('cmp_healing_dashboard_tip');
    opt.confirmDescription = I18n.get('cmp_healing_delete_host');
    opt.confirmFun = $scope.deleteItem;
    opt.confirmFunPara = {
      type: 'ip',
      data: deleteItem,
    };
    $scope.confirmDialog(opt);
  };

  $scope.popConfirmDeleteSolution = function(solution) {
    let opt = {};
    opt.title = I18n.get('cmp_healing_dashboard_tip');
    opt.confirmDescription = I18n.get('cmp_healing_delete_solution');
    opt.confirmFun = $scope.deleteItem;
    opt.confirmFunPara = {
      type: 'solution',
      data: solution,
    };
    $scope.confirmDialog(opt);
  };

  $scope.popConfirmDeleteGroup = function(group) {
    let opt = {};
    opt.title = I18n.get('cmp_healing_dashboard_tip');
    opt.confirmDescription = I18n.get('cmp_healing_delete_group');
    opt.confirmFun = $scope.deleteItem;
    opt.confirmFunPara = {
      type: 'group',
      data: group,
    };
    $scope.confirmDialog(opt);
  };

  $scope.createDiagramNode = function(style) {
    $UI.diagram('flow-diagram').requestNode(null, style);
  };

  $scope.createDiagramPath = function(pathOption) {
    $UI.diagram('flow-diagram').requestPath(null, pathOption);
  };


  $scope.popAddAlarmEvent = function() {
    let win = eview.widgets.Window.get('addAlarmEvent');
    win = createWindow('addAlarmEvent', I18n.get('cmp.healing.flow.add.alarm.event'),
      450, 320, 'complex', '/dvihealingwebsite/app/healing/flow/alarm.html');
    win.show();
    win.setData('alarmData', $scope.$Model);
    win.on('addAlarmEventData', $scope.addAlarmEventHandler);
  };

  $scope.closeDialog = windowHelper.closeDialog($scope);

  $scope.getI18n = function(i18nName, replaceParam) {
    if (!replaceParam) {
      return I18n.get(i18nName);
    }
    let I18nStr = I18n.get(i18nName);
    for (let i = 0; i < replaceParam.length; i++) {
      let oldStr = '{' + i + '}';
      I18nStr = I18nStr.replace(oldStr, replaceParam[i]);
    }
    return I18nStr;
  };

  $scope.setOptApi = function(viewApi) {
    $scope.optApi.showApi = false;
    $scope.optApi.requestParam.api = angular.copy(viewApi);
  };
  $scope.scriptOptApi = function(atomName, atomId) {
    let sendData = {
      atomName: atomName,
      atomId,
    };
    eview.widgets.Window.get('selectAtom').trigger('createFlowSendData', sendData);
    eview.widgets.Window.get('selectAtom').dispose();
  };

  $scope.getCurrentWindow = windowHelper.getCurrentWindow();

  // 时间格式化为日期字符串
  function getCurrentDateStr() {
    let d = new Date();
    let temp = [d.getFullYear(), d.getMonth() + 1, d.getDate(), d.getHours(), d.getMinutes(), d.getSeconds()];
    return temp.join('');
  }

  $scope.popAtomDetail = function(nodeId) {
    let nodes = $scope.$Model.bpmidediagram.nodes;
    nodes.some(function(node) {
      if (node.id === nodeId) {
        $scope.atom = $.extend(true, {}, node.data);
        return true;
      } else {
        return false;
      }
    });

    $scope.queryDetailsOfAtoms($scope.atom).then(function(atom) {
      $scope.atomDetailsData = {
        atom: atom,
        isLook: $scope.isLook,
      };
      let win = eview.widgets.Window.get('EditAtomWindow');
      win = createWindow('EditAtomWindow', I18n.get('cmp.healing.flow.chose.atom.edit'), 1000, 850, 'complex',
        '/dvihealingwebsite/app/healing/flow/edit_atom.html');
      win.setData('atomDetailsData', $scope.atomDetailsData);
      win.show();
    });
  };

  $scope.queryDetailsOfAtoms = function(atom) {
    let defer = $q.defer();
    ajax('post', 'json', JSON.stringify(atom.atomId), '/rest/dvworkbeanswebsite/v1/AtomQuery/queryById',
      function(result) {
        if (result === null) {
          alertTips(I18n.get('cmp.healing.atom.not.exist.tips'));
          return;
        }
        
        defer.resolve(result);
      });
    return defer.promise;
  };

  $scope.isValidArr = function(className) {
    let validArr = $('.' + className);
    for (let i = 0; i < validArr.length; i++) {
      if (validArr.eq(i).val() === '') {
        let id = validArr.eq(i).attr('id');
        let tipMessage = commonI18n.get('cmp_workbench_task_noomitted_tips');
        $scope.showTipMessage(id, tipMessage);
        return false;
      }
    }
    return true;
  };

  $scope.isOnlyWithSpace = function(str) {
    let newStr = str.trim();
    if (newStr.length === 0) {
      return true;
    }
    return false;
  };

  $scope.deleteFlowParameters = function(item, index) {
    // 对于文件上传流程参数，得删除已上传的文件
    if (item.value) {
      $scope.flowParameters.forEach(function(val) {
        if (val.flowParameterSource === 'UPLOAD_FILE' && item.value === val.value) {
          $scope.deleteFile(item);
        }
      });
    }

    $scope.flowParameters.splice(index, 1);
    flowParamTable.refreshParamTable($scope);
  };

  $scope.queryUploadFileConfig = function() {
    ajax('POST', 'text', JSON.stringify(), '/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryUploadFileConfig',
      function(data) {
        $scope.$Model.fileConfig = JSON.parse(data);
        let maxSizeStr = $scope.$Model.fileConfig.maxSizeStr;
        $scope.typeStr = $scope.$Model.fileConfig.typeStr;
        let types = ['txt', 'properties', 'csv', 'log', 'zip'];
        $scope.uploadFileMaxSize = parseInt(maxSizeStr);
        $scope.types = $scope.typeStr.split(',');
        $scope.legalFileTypes = $scope.types ? $scope.types : types;
        $scope.uploadTips = I18n.get('cmp.healing.flow.upload.file.tips', [$scope.typeStr, $scope.uploadFileMaxSize]);
        $scope.$apply();
      }
    );
  };

  $scope.queryAtomLimit = function() {
    ajax('POST');
  };

  $scope.clickUploadBtn = function(index) {
    let uploadInput = $('#uploadParam_' + index);
    uploadInput.val('');
    uploadInput.click();
  };

  $scope.uploadFile = function(event) {
    let uploadFile = event.files[0];
    let tmp = event.value.split('\\');
    let name = tmp[tmp.length - 1];
    let eventId = event.id;
    let curIndex = eventId.substring(eventId.lastIndexOf('_') + 1, eventId.length);

    if ($scope.checkUploadFile(uploadFile, name)) {
      $scope.uploadFileService(uploadFile, curIndex);
    }
  };

  $scope.checkUploadFile = function(uploadFile, name) {
    let fileType = name.substring(name.lastIndexOf('.') + 1, name.length);
    let flag = true;
    // 校验文件是否有合法
    for (let i = 0; i < $scope.legalFileTypes.length; i++) {
      if ($scope.legalFileTypes[i] === fileType) {
        flag = false;
        break;
      }
    }
    
    if (flag) {
      showMessage($scope, 'warning', I18n.get('cmp.healing.flow.upload.notice', [$scope.typeStr]));
      return false;
    }
    
    // 校验文件名称
    let fileName = name.substring(0, name.lastIndexOf('.'));
    let pattern = new RegExp('^[a-zA-Z0-9_=:.\\\\-]+$');
    if (!pattern.test(fileName)) {
      showMessage($scope, 'warning', I18n.get('cmp.healing.flow.upload.name.notice'));
      return false;
    }
    
    // 校验文件是否为空文件
    if (uploadFile.size === 0) {
      showMessage($scope, 'warning', I18n.get('cmp.healing.flow.upload.content.notice'));
      return false;
    }
    

    let fileSizeLimit = $scope.uploadFileMaxSize ? $scope.uploadFileMaxSize : 30;
    // 校验文件大小
    if (uploadFile.size > fileSizeLimit * 1024 * 1024) {
      fileSizeLimit = parseInt(fileSizeLimit);
      showMessage($scope, 'warning', I18n.get('cmp.healing.flow.upload.max', [fileSizeLimit]));
      return false;
    }
    

    return true;
  };

  $scope.uploadFileService = function(uploadFile, curIndex) {
    load();
    uploadFilePublic('POST', '/rest/dvihealingwebsite/v1/flowfile/upload', uploadFile, function(data) {
      // 删除之前已上传的文件
      let filePath = data.filePath;
      $scope.uploadFileParam = {};
      $scope.flowParameters.forEach(function(item, index) {
        if (item.flowParamType === 'UPLOAD_FILE' && index === parseInt(curIndex)) {
          // 更新上传成功的文件到页面
          $scope.uploadFileParam.filePath = filePath;
          $scope.uploadFileParam.fileName = filePath.split('$')[1];
          $scope.uploadFileParams.push($scope.uploadFileParam);
          item.value = filePath;
          item.fileName = filePath.slice(36);
          $scope.$apply();
          closeLoad();
        }
      });
      $scope.$apply();
      closeLoad();
    }, function() {
      $scope.$apply();
      closeLoad();
    });
  };

  $scope.sureDelete = function(item) {
    let win;
    const options = {
      title: I18n.get('cmp.healing.flow.flowParameter.operation.delete'),
      content: I18n.get('cmp.healing.flow.delete.file'),
      callback: function(event) {
        if ((event.type === 'button') && (event.id === 'yes')) {
          $scope.deleteFile(item);
        }
        
        // 响应结束之后注销dialog
        win.destroy();
      },
    };
    win = new eview.widgets.MessageDialog.confirm(options);
  };

  $scope.deleteFile = function(item) {
    if (!item.value) {
      return;
    }
    

    let filePaths = [];
    filePaths.push({ filePath: item.value });
    $scope.deleteFilePath = item.value;
    ajax('POST', 'text', JSON.stringify(filePaths), '/rest/dvihealingwebsite/v1/flowfile/delete', function(data) {
      if (data === 'failed_to_delete_file') {
        showMessage($scope, 'warning', I18n.get('cmp.healing.flow.delete.file.failed'));
        return;
      }
      $scope.flowParameters.forEach(function(param) {
        if (param.flowParamType === 'UPLOAD_FILE') {
          if (param.value === $scope.deleteFilePath) {
            param.value = '';
            param.fileName = '';
          }
        }
      });
      $scope.uploadFileParams.forEach(function(file, index) {
        if (file.filePath === $scope.deleteFilePath) {
          $scope.uploadFileParams.splice(index, 1);
        }
      });
      $scope.$apply();
    });
  };

  $scope.queryFlowParamsConfig = function() {
    let request = { configKeyList: ['requireParamType', 'maxEmailCount', 'maxQueryAtomCount'] };
    ajax('POST', 'json', JSON.stringify(request), '/rest/dvihealingwebsite/v1/dvflowviewservice/queryConfig', function(data) {
      if (data) {
        let requireParamTypes = data.requireParamType;
        requireParamTypes.forEach(function(type) {
          $scope.canConfig[type] = true;
        });
        $scope.maxEmailCount = Number(data.maxEmailCount) || 200;
        $scope.maxQueryAtomCount = Number(data.maxQueryAtomCount) || 50;
      }
    });
  };

  $scope.dealRequestParam = function(originRequest) {
    // 备份请求参数并对备份请求参数删除页面展示内容，防止页面因原请求参数属性被删出问题
    let backupRequest = angular.copy(originRequest);
    if (backupRequest.definition.paras && backupRequest.definition.paras.length > 0) {
      backupRequest.definition.paras.forEach(function(item) {
        deleteUnnecessaryParamProperties(item);
      });
    }
    if (backupRequest.definition.defaultParas && backupRequest.definition.defaultParas.length > 0) {
      backupRequest.definition.defaultParas.forEach(function(item) {
        deleteUnnecessaryParamProperties(item);
      });
    }
    if (backupRequest.definition.nodes && backupRequest.definition.nodes.length > 0) {
      backupRequest.definition.nodes.forEach(function(node) {
        if (node.type === 'SERVICE_TASK' && node.atom && node.atom.commonParams && node.atom.commonParams.length) {
          for (let i = 0; i < node.atom.commonParams.length; i++) {
            if (node.atom.commonParams[i].name === 'ips') {
              delete node.atom.commonParams[i].rule;
            }
          }
        }
      });
    }

    return backupRequest;
  };

  function deleteUnnecessaryParamProperties(item) {
    if (item.flowParamType === 'UPLOAD_FILE') {
      delete item.fileName;
    }
    if (item.tempParam) {
      delete item.tempParam;
    }
    if (item.defaultTempParam) {
      delete item.defaultTempParam;
    }
    if (item.description === '') {
      delete item.description;
    }
    // 删除用于页面展示使用的字段
    delete item.isRequired;
    delete item.defaultValue;
    delete item.isShow;
  }

  // 查询远程通知对象型参数值
  $scope.querySenderInfo = function() {
    let defer = $q.defer();
    let request = { limit: 100, offset: 0 };
    ajax('POST', 'json', JSON.stringify(request), '/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryEmail', function(data) {
      $scope.notifyList = data.users;
      $scope.$apply();
      defer.resolve(data);
    });
    return defer.promise;
  };

  $scope.showSolutionWin = function() {
    $scope.ips.isShowSolutionTree = true;
  };

  // 弹出选择远程通知对象的界面
  $scope.beforeSelectRecipient = function(paramIndex, isDefault) {
    let options = {
      title: I18n.get('cmp.healing.flow.choose.notify.object'),
      contentType: 'complex',
      content: '/dvihealingwebsite/app/healing/flow/selectRecipient.html',
      modal: true,
      draggable: true,
      resizable: false,
      height: 715,
      width: 1200,
      frameBorder: 0,
      buttonRequired: true,
      listeners: {
        ok: function() {
          $scope.afterSelectRecipient(paramIndex, isDefault);
        },
        cancel: function() {
          $scope.closeDialog('selectedRecipient');
        },
      },
    };
    let win = eview.widgets.Window.create('selectedRecipient', options);
    // 将已选收件人传到弹窗页面
    $scope.selectedUserList = [];
    $scope.selectedGroupList = [];
    if ($scope.flowParameters[paramIndex].value) {
      let valueList = $scope.flowParameters[paramIndex].value.split(',');
      if (valueList && valueList.length) {
        valueList.forEach(function(value) {
          if (value[0] === 'g') {
            $scope.selectedGroupList.push(value);
          }
          if (value[0] === 'u') {
            $scope.selectedUserList.push(value);
          }
        });
      }
    }
    win.setData('selectedUserList', $scope.selectedUserList);
    win.setData('selectedGroupList', $scope.selectedGroupList);
    win.setData('selectType', 'user');
    win.show();
  };

  $scope.afterSelectRecipient = function(paramIndex, isDefault) {
    let selectedNameList = selectRecipientBase.getSelectNameList($scope, emailScope, paramIndex, isDefault);

    // 渲染页面
    $scope.flowParameters[paramIndex].tempParam = {
      displayValue : selectedNameList.join(','),
    };
    flowParamTable.refreshParamTable($scope);

    $scope.$apply();
    $scope.closeDialog('selectedRecipient');
  };

  $scope.popSelectNeInstance = neInstanceParam.popSelectNeInstance($scope, 'create');

  $scope.popSelectAccount = selectAccountBase.popSelectAccount($scope);

  $scope.afterSelectNeInstance = neInstanceParam.afterSelectNeInstance($scope);

  $scope.filterSelectedNodes = neInstanceParam.filterSelectedNodes($scope);

  $scope.deleteChildren = neInstanceParam.deleteChildren($scope);

  // 配置日期型参数，目前仅用于配置日期型参数的默认值
  $scope.popConfigDate = function(index, isDefault) {
    let win = eview.widgets.Window.get('configDate');
    if (win) {
      return;
    }
    let dateParam;
    if (isDefault && isDefault === 'default') {
      dateParam = $scope.flowParameters[index].defaultValue;
    } else {
      dateParam = $scope.flowParameters[index].value;
    }

    let options = {
      title: I18n.get('cmp.healing.flow.config.date.title'),
      contentType: 'complex',
      content: '/dvihealingwebsite/app/healing/flow/configDate.html',
      modal: true,
      draggable: true,
      resizable: false,
      height: 350,
      width: 500,
      frameBorder: 0,
      buttonRequired: true,
      listeners: {
        ok: function() {
          $scope.afterConfigDate(index, isDefault);
          $scope.closeDialog('configDate');
        },
        cancel: function() {
          $scope.closeDialog('configDate');
        },
      },
    };
    win = eview.widgets.Window.create('configDate', options);
    win.setData('dateParam', dateParam);
    win.show();
  };

  $scope.afterConfigDate = function(index, isDefault) {
    configDateScope.getDateParam();
    let win = eview.widgets.Window.get('configDate');
    let dateParam = angular.copy(win.getData('dateParam')) ? angular.copy(win.getData('dateParam')) : '';
    if (isDefault && isDefault === 'default') {
      $scope.flowParameters[index].defaultValue = dateParam;
    } else {
      $scope.flowParameters[index].value = dateParam;
    }
    $scope.parseDateParam(dateParam, index, isDefault);
    flowParamTable.refreshParamTable($scope);
    $scope.closeDialog('configDate');
  };

  $scope.parseDateParam = function(dateParam, index, isDefault) {
    let showValue;
    if (dateParam === '' || dateParam.indexOf(' ') !== -1) {
      showValue = dateParam;
    } else {
      let unit = dateParam.slice(dateParam.length - 1, dateParam.length);
      if (dateParamToShowValueMap[unit] !== null) {
        showValue = I18n.get('cmp_healing_flow_words_before') + dateParam.split(/[dhm]/)[0] + dateParamToShowValueMap[unit];
      } else {
        showValue = '';
      }
    }
    if (isDefault && isDefault === 'default') {
      $scope.flowParameters[index].defaultTempParam = {
        displayValue: showValue,
      };
    } else {
      $scope.flowParameters[index].tempParam = {
        displayValue: showValue,
      };
    }
  };


  $scope.transformDnParam = neInstanceParam.transformDnParam($scope, 'create');

  $scope.getAtomIpValue = function() {
    if ($scope.murmur && $scope.murmur.ips && $scope.murmur.ips.source) {
      switch ($scope.murmur.ips.source) {
        case 'DEFAULT':
          $scope.murmur.ips.value = $scope.ips.defaultIpsValue;
          $scope.murmur.ips.startRule = null;
          $scope.murmur.ips.endRule = null;
          break;
        case 'ALARM':
          $scope.murmur.ips.value = $scope.ips.alarmIpsValue;
          break;
        case 'GLOBAL':
          $scope.murmur.ips.value = $scope.ips.globalIpsValue;
          $scope.murmur.ips.startRule = null;
          $scope.murmur.ips.endRule = null;
          break;
        case 'SELECT_PRIORITY_ONLINE_HOST':
          $scope.murmur.ips.value = $scope.ips.paramKey;
          $scope.murmur.ips.startRule = null;
          $scope.murmur.ips.endRule = null;
          break;
        default:
      }
    }

    if ($scope.atom && $scope.atom.commonParams && $scope.atom.commonParams.length > 0) {
      $scope.atom.commonParams.forEach(function(item) {
        if (item.name === 'ips' && item.source === 'DEFAULT') {
          item.value = $scope.murmur.ips.value;
          item.ruleContent = $scope.hostSelectionMode;
        }
      });
    }
  };

  $scope.setDialogData = function(win) {
    win.setData('selectedAtom', $scope.atom);
    win.setData('murmurData', $scope.murmur);
    win.setData('atomSolutions', $scope.atomSolutions);
    win.setData('flowSolution', $scope.flow.solution);
    win.setData('englishList', $scope.englishList);
    win.setData('atoms', $scope.atoms);
    win.setData('page', $scope.page);
    win.setData('solutionList', $scope.solutionList);
    win.setData('atom_script_top_solution_type', $scope.atom_script_top_solution_type);
    win.setData('selectIndex', $scope.page.selectIndex);
    win.setData('nodeId', $scope.nodeId);
    win.setData('Model', $scope.$Model);
    win.setData('isAtomHidden', $scope.isAtomHidden);
    win.setData('isFilterOfflineHost', $scope.isFilterOfflineHost);
    win.setData('hostSelectionMode', $scope.hostSelectionMode);
  };

  $scope.paramTypeSelection = [{
    label: I18n.get('cmp.healing.flow.para.string.type'),
    value: 'STRINGTYPE',
  }, {
    label: I18n.get('cmp.healing.flow.para.date.type'),
    value: 'DATETYPE',
  }, {
    label: I18n.get('cmp.healing.flow.para.password.type'),
    value: 'PVALUETYPE',
  }, {
    label: I18n.get('cmp.healing.flow.para.string.enum'),
    value: 'ENUMTYPE',
  }, {
    label: I18n.get('cmp.healing.flow.para.string.script'),
    value: 'SCRIPTTYPE',
  }, {
    label: I18n.get('cmp.healing.flow.para.string.map'),
    value: 'ENUMRATE_KEY_VALUE',
  }, {
    label: I18n.get('cmp.healing.flow.para.string.iptree'),
    value: 'IP_TREE',
  }, {
    label: I18n.get('cmp.healing.flow.para.string.json'),
    value: 'CASCADE',
  }, {
    label: I18n.get('cmp.healing.flow.para.string.remotenotify'),
    value: 'REMOTE_NOTIFY_OBJ',
  }, {
    label: I18n.get('cmp.healing.flow.para.string.neinstance'),
    value: 'DN_TYPE',
  }, {
    label: I18n.get('cmp.healing.flow.para.string.dbaccount'),
    value: 'DB_ACCOUNT',
  }];

  $scope.atomMoTypeTreeListeners = {
    click: atomMoTypeTreeClickHandler,
  };

  function atomMoTypeTreeClickHandler(event) {
    $scope.$apply(function() {
      $scope.atom.moType = event.data.additionalData;
    });
  }

  $scope.nameEditor = flowParamTable.nameEditor();
  $scope.descEditor = flowParamTable.descEditor();
  $scope.sourceEditor = flowParamTable.sourceEditor();
  $scope.typeEditor = flowParamTable.typeEditor();
  $scope.valueEditor = flowParamTable.valueEditor($scope);
  $scope.defaultValueEditor = flowParamTable.defaultValueEditor();
  $scope.requiredEditor = flowParamTable.requiredEditor($scope);
  $scope.visibleEditor = flowParamTable.visibleEditor($scope);
  $scope.operationEditor = flowParamTable.operationEditor();

  $scope.paramTypeChangeHandler = flowParamTable.paramTypeChangeHandler($scope);

  $scope.paramTableListeners = {
    dragStartHandler: flowParamTable.dragStartHandler($scope),
    dropHandler: flowParamTable.dropHandler($scope),
  };

  $scope.stopDrag = function() {
    $scope.trDraggable = false;
  };

  $scope.allowDrag = function() {
    $scope.trDraggable = true;
  };
}