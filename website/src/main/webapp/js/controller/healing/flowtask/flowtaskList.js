/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

'use strict';
window._scope = null;
function FlowTaskController($scope) {
  $scope.init = function() {
    $scope.searchedTaskName = null;
    $scope.searchedFlowGroup = null;
    $scope.searchedFlowName = null;
    $scope.searchedType = null;
    $scope.currentPageIndex = 1;
    $scope.currentPageLength = 10;
    $scope.$model = {};
    $scope.allTaskNames = [];
    $scope.allFlowNames = [];
    $scope.allFlowGroupNames = [];
    $scope.allTypes = ['TIMER', 'MANUAL'];
    $scope.flowTaskData = [];
    load();
    $scope.queryFlowTaskList();
    $scope.queryTaskListTimer = setInterval(function() {
      $scope.queryFlowTaskList();
    }, 15000);
    window.addEventListener('beforeunload', function(event) {
      clearInterval($scope.queryTaskListTimer);
    });
  };

  $scope.queryFlowTaskList = function() {
    let param = {
      limit: $scope.currentPageLength,
      offset: ($scope.currentPageIndex - 1) * $scope.currentPageLength,
      taskName: $scope.searchedTaskName === I18n.get('cmp.healing.flow.flowname.all') ? null : $scope.searchedTaskName,
      flowName: $scope.searchedFlowName === I18n.get('cmp.healing.flow.flowname.all') ? null : $scope.searchedFlowName,
      solution: $scope.searchedFlowGroup === I18n.get('cmp.healing.flow.flowname.all') ? null : $scope.searchedFlowGroup,
      type: $scope.searchedType === I18n.get('cmp.healing.flow.flowname.all') ? null : $scope.searchedType,
    };
    ajax('post', 'json', JSON.stringify(param), '/rest/dvihealingwebsite/v1/dvihealingtaskinfoservice/queryTaskInfoByPage', function(data) {
      $scope.flowTaskData = [];
      if (data !== null && data.length > 0) {
        let startIndex = ($scope.currentPageIndex - 1) * $scope.currentPageLength;
        let endIndex = $scope.currentPageIndex * $scope.currentPageLength < data.length ? $scope.currentPageIndex * $scope.currentPageLength : data.length;
        for (let i = startIndex; i < endIndex; i++) {
          let flowTask = {
            taskId: data[i].id,
            deployedId: data[i].deployedId,
            taskName: data[i].name,
            flowId: data[i].flowId,
            flowName: data[i].flowName,
            flowGroup: data[i].solution,
            type: data[i].type,
            cron: data[i].cron,
            reviewStatus: data[i].reviewStatus,
            param: getFlowParamDisplayValue(data[i].flowParameters),
            deployStatus: getDeployStatus(data[i].type, data[i].deployedId, data[i].reviewStatus),
            executeStatus: data[i].status,
            lastExecuteTime: displayServerTime(data[i].startTimeString),
          };
          $scope.flowTaskData.push(flowTask);
        }
        $scope.getAllTaskInfo(data);
      }
      $scope.flowTaskTableParams.attr('dataset', $scope.flowTaskData);
      $scope.flowTaskTableParams.attr('total-records', data.length);
      $scope.flowTaskTableParams.attr('cur-page', {
        pageIndex: $scope.currentPageIndex,
        isStopCallbackInvoke: true,
      });
      $scope.flowTaskTableParams.attr('display-length', $scope.currentPageLength);
      closeLoad();
    }, function() {
      closeLoad();
    }, (request) => {
      request.setRequestHeader('X-Non-Renewal-Session', 'true');
    });
  };

  $scope.getAllTaskInfo = function(data) {
    for (let i = 0; i < data.length; i++) {
      if ($scope.allTaskNames.indexOf(data[i].name) === -1) {
        $scope.allTaskNames.push(data[i].name);
      }
      if ($scope.allFlowNames.indexOf(data[i].flowName) === -1) {
        $scope.allFlowNames.push(data[i].flowName);
      }
      if ($scope.allFlowGroupNames.indexOf(data[i].solution) === -1) {
        $scope.allFlowGroupNames.push(data[i].solution);
      }
    }
    $scope.$apply();
  };

  $scope.operationEditor = function(obj) {
    let index = obj.rowData.rowid;
    let editIcon = '<a href=\'javascript:void(0)\' class=\'icon-edit\' ' +
      'onClick=\'editTask(' + index + ')\' ' +
      'title=\'' + I18n.get('cmp_healing_api_edit') + '\'></a>';
    let executeIcon = '';
    if (obj.rowData.reviewStatus === 'approve') {
      executeIcon = '<a href=\'javascript:void(0)\' class=\'flow-operation excute\' ' +
        'title=\'' + I18n.get('cmp_healing_flow_start') + '\' style=\'display: inline-block\' ' +
        'onClick=\'beforeStartTask(' + index + ')\'></a>';
    } else {
      executeIcon = '<a href=\'javascript:void(0)\' class=\'flow-operation unexcute\' ' +
        'title=\'' + I18n.get('cmp_healing_flow_start') + '\' style=\'display: inline-block\' ></a>';
    }
    let deployIcon = '';
    if (obj.rowData.type === 'TIMER') {
      if (!obj.rowData.deployedId) {
        if (obj.rowData.reviewStatus === 'approve') {
          deployIcon = '<a href=\'javascript:void(0)\' class=\'flow-operation deploy\' ' +
            'title=\'' + I18n.get('cmp_healing_flow_deploy') + '\' ' +
            'onClick=\'deployTask(' + index + ')\' style=\'display: inline-block\' ></a>';
        } else {
          deployIcon = '<a href=\'javascript:void(0)\' class=\'flow-operation deployGray\' ' +
            'title=\'' + I18n.get('cmp_healing_flow_deploy') + '\' style=\'display: inline-block\' ' +
            '\'></a>';
        }
      } else {
        deployIcon = '<a href=\'javascript:void(0)\' class=\'flow-operation undeploy\' ' +
          'title=\'' + I18n.get('cmp_healing_flow_undeploy') + '\' style=\'display: inline-block\'\' ' +
          'onClick=\'undeployTask(' + index + ')\'></a>';
      }
    }
    let copyIcon = '<a href=\'javascript:void(0)\' class=\'icon-copy\' ' +
      'onClick=\'copyTask(' + index + ')\' ' +
      'title=\'' + I18n.get('cmp_healing_task_copy') + '\'></a>';
    let jumpIcon = '<a href=\'javascript:void(0)\' class=\'icon-jump\' ' +
      'onClick=\'jumpToHis(' + index + ')\' ' +
      'title=\'' + I18n.get('cmp_healing_flow_history') + '\'></a>';
    let deleteIcon = '<a href=\'javascript:void(0)\' class=\'icon-btn-delete\' ' +
      'onClick=\'deleteTask(' + index + ')\' ' +
      'title=\'' + I18n.get('cmp.healing.flow.delete') + '\'></a>';
    return editIcon + executeIcon + deployIcon + copyIcon + jumpIcon + deleteIcon;
  };

  $scope.statusEditor = function(obj) {
    let executeStatus = obj.rowData.executeStatus;
    let label;
    switch (executeStatus) {
      case 'EXECUTING':
        label = '<span class="running-round"></span>' +
          '<span>' + I18n.get('cmp.healing.flow.executestatus.executing') + '</span>';
        break;
      case 'SUCCESS':
        label = '<span class="green-round"></span>' +
          '<span>' + I18n.get('cmp.healing.flow.executestatus.success') + '</span>';
        break;
      case 'FAILED':
        label = '<span class="red-round"></span>' +
        '<span>' + I18n.get('cmp.healing.flow.executestatus.failed') + '</span>';
        break;
      case 'PAUSED':
        label = '<span class="yellow-round"></span>' +
          '<span>' + I18n.get('cmp.healing.flow.executestatus.confirm') + '</span>';
        break;
      case 'SUSPEND':
        label = '<span class="yellow-round"></span>' +
          '<span>' + I18n.get('cmp.healing.flow.executestatus.suspend') + '</span>';
        break;
      case 'CANCELED':
        label = '<span class="red-round"></span>' +
          '<span>' + I18n.get('cmp.healing.flow.executestatus.canceled') + '</span>';
        break;
      default:
        label = '<span></span>';
    }

    return label;
  };

  $scope.paginationHandler = function(event) {
    $scope.currentPageIndex = event.currentPage;
    $scope.currentPageLength = event.displayLength;
    $scope.queryFlowTaskList();
  };

  $scope.canExecute = function(index) {
    if (!$scope.flowTaskData[index]) {
      return false;
    }
    return $scope.flowTaskData[index].reviewStatus === 'approve';
  };

  $scope.resetFilter = function() {
    $scope.searchedTaskName = null;
    $scope.searchedFlowGroup = null;
    $scope.searchedFlowName = null;
    $scope.searchedType = null;
    $scope.queryFlowTaskList();
  };

  $scope.onTaskNameChanged = function(taskName) {
    $scope.searchedTaskName = taskName ? taskName : null;
    $scope.queryFlowTaskList();
  };

  $scope.onGroupNameChanged = function(groupName) {
    $scope.searchedFlowGroup = groupName ? groupName : null;
    $scope.queryFlowTaskList();
  };

  $scope.onFlowNameChanged = function(flowName) {
    $scope.searchedFlowName = flowName ? flowName : null;
    $scope.queryFlowTaskList();
  };

  $scope.onSearchTypeChanged = function(type) {
    $scope.searchedType = type ? type : null;
    $scope.queryFlowTaskList();
  };

  $scope.addTask = function() {
    _scope.isCreate = true;
    _scope.isTaskInfoEditorOpen = true;
    _scope.isFlowInfoEditorOpen = true;
    _scope.currentTask = {
      taskId: null,
      taskName: '',
      flowName: '',
      flowGroup: '',
      flowId: '',
      type: 'MANUAL',
      flowTriggerType: '',
      cron: '',
      param: '',
      deployStatus: '',
      executeStatus: '',
      LastExecuteTime: '',
    };
    _scope.oldFlowParameters = [];
    _scope.flowParameters = [];
    getAllFlowInfo();
    _scope.getTaskParams();
    clearInterval($scope.queryTaskListTimer);
    $('#task-edit-mode-side-page')
      .animate({
        right: '0%',
      }, 400);
  };

  $scope.editTask = function(index) {
    _scope.isCreate = false;
    _scope.isTaskInfoEditorOpen = true;
    _scope.isFlowInfoEditorOpen = true;
    _scope.currentTask = angular.copy($scope.flowTaskData[index]);
    _scope.oldFlowParameters = parseParamString(_scope.currentTask.param);
    clearInterval($scope.queryTaskListTimer);
    _scope.queryFlowDetailAndRefresh();
    $scope.$apply();
    $('#task-edit-mode-side-page')
      .animate({
        right: '0%',
      }, 400);
  };
  window.editTask = $scope.editTask;

  $scope.copyTask = function(index) {
    _scope.isCreate = false;
    _scope.isTaskInfoEditorOpen = true;
    _scope.isFlowInfoEditorOpen = true;
    _scope.currentTask = angular.copy($scope.flowTaskData[index]);
    _scope.currentTask.taskId = null;
    _scope.currentTask.taskName = _scope.currentTask.taskName + '_copy';
    _scope.oldFlowParameters = parseParamString(_scope.currentTask.param);
    clearInterval($scope.queryTaskListTimer);
    _scope.queryFlowDetailAndRefresh();
    $scope.$apply();
    $('#task-edit-mode-side-page')
      .animate({
        right: '0%',
      }, 400);
  };
  window.copyTask = $scope.copyTask;

  $scope.saveTask = function() {
    $scope.setCascadeParam();
    let validArr = 'requiredValidInput';
    if (!$scope.isValidArr(validArr)) {
      return;
    }
    let taskDos = [];
    let taskDo = {
      id: _scope.currentTask.taskId,
      name: _scope.currentTask.taskName,
      type: _scope.currentTask.type,
      flowId: _scope.currentTask.flowId,
      cron: _scope.currentTask.cron,
      flowParameters: $scope.convertParameters(_scope.flowParameters),
    };
    load();
    taskDos.push(taskDo);
    ajax('POST', 'text', JSON.stringify(taskDos), '/rest/dvihealingwebsite/v1/dvihealingtaskinfoservice/saveOrUpdateTaskInfos', function(data) {
      $scope.closePage();
    });
  };

  $scope.isValidArr = function(className) {
    let validArr = $('.' + className);
    for (let i = 0; i < validArr.length; i++) {
      if (validArr.eq(i).val() === '') {
        let id = validArr.eq(i).attr('id');
        let tipMessage = commonI18n.get('cmp_workbench_task_noomitted_tips');
        $scope.showTipMessage(id, tipMessage);
        return false;
      }
    }
    return true;
  };

  $scope.beforeStartTask = function(index) {
    if (!$scope.canExecute(index)) {
      showMessage($scope, 'warn', I18n.get('cmp_healing_task_review_before_execute'));
      return;
    }
    $scope.authFilterAndStart($scope.startTask, index, null);
  };
  window.beforeStartTask = $scope.beforeStartTask;

  $scope.authFilterAndStart = function(submitFunc, submitFuncParam, cancelFunc) {
    $scope.opt = {
      startFunc: submitFunc,
      submitFuncParam: submitFuncParam,
      cancelFunc,
    };

    setSecondaryAuth('DVIhealingWebsite', 'startFlow', function(ticket) {
      $scope.checkPermissionParam = {
        authInfo: {
          operateId: 'startFlow',
          ticket,
        },
      };
      $scope.opt.startFunc($scope.opt.submitFuncParam, $scope.checkPermissionParam);
    });
  };

  $scope.startTask = function(index, checkPermission) {
    let request = {
      authInfo: checkPermission.authInfo,
      taskId: $scope.flowTaskData[index].taskId,
    };
    ajax('POST', 'text', JSON.stringify(request), '/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/deployAndExecuteTask', function() {
      showMessage($scope, 'success', I18n.get('cmp_healing_task_start_success'));
      $scope.queryFlowTaskList();
    });
  };

  $scope.deployTask = function(index) {
    if (!$scope.canExecute(index)) {
      return;
    }
    ajax('POST', 'text', JSON.stringify($scope.flowTaskData[index].taskId), '/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/deployTask', function(data) {
      showMessage($scope, 'success', I18n.get('cmp_healing_task_deploy_success'));
      $scope.queryFlowTaskList();
    }, function() {
      $scope.queryFlowTaskList();
    });
  };
  window.deployTask = $scope.deployTask;

  $scope.undeployTask = function(index) {
    ajax('POST', 'text', JSON.stringify($scope.flowTaskData[index].taskId), '/rest/dvihealingwebsite/v1/dvihealingflowtaskservice/undeployTask', function() {
      showMessage($scope, 'success', I18n.get('cmp_healing_task_undeploy_success'));
      $scope.queryFlowTaskList();
    }, function() {
      $scope.queryFlowTaskList();
    });
  };
  window.undeployTask = $scope.undeployTask;

  $scope.jumpToHis = function(index) {
    let url = `/dvihealingwebsite/index.html#/tasklist?taskname=${$scope.flowTaskData[index].taskName}`;
    parent.window.ihealingEvent.emit('routerChange', encodeURI(url));
  };
  window.jumpToHis = $scope.jumpToHis;

  $scope.deleteTask = function(index) {
    let taskIds = [];
    taskIds.push($scope.flowTaskData[index].taskId);
    ajax('POST', 'text', JSON.stringify(taskIds), '/rest/dvihealingwebsite/v1/dvihealingtaskinfoservice/deleteTaskInfoByTaskIds', function() {
      $scope.queryFlowTaskList();
    }, function() {
      $scope.queryFlowTaskList();
    });
  };
  window.deleteTask = $scope.deleteTask;

  $scope.closePage = function() {
    $('#task-edit-mode-side-page')
      .animate({
        right: '-100%',
      }, 400);
    $scope.queryFlowTaskList();
    $scope.queryTaskListTimer = setInterval(function() {
      $scope.queryFlowTaskList();
    }, 15000);
  };

  function getDeployStatus(type, deployedId, reviewStatus) {
    if (reviewStatus === 'draft') {
      return I18n.get('cmp_healing_check_flow_status_draft');
    }
    if (type === 'TIMER' && deployedId !== null) {
      return I18n.get('cmp_healing_task_status_activated');
    }
    return I18n.get('cmp_healing_task_status_ready');
  }

  $scope.convertParameters = timerTemplate.convertParameters($scope);

  $scope.setCascadeParam = function() {
    let tableScope = $('#parameterTable').scope();
    for (let i = 0; i < _scope.flowParameters.length; i++) {
      itemValueHandler(tableScope, i);

      cascadeHandler(tableScope, i);

      // 从页面的脚本级联型参数获取的值更新到selecValues中
      if (_scope.flowParameters[i].flowParamType === 'SCRIPTTYPE' && $scope.isScriptCascadeMode(_scope.flowParameters[i].selectValues)) {
        $scope.setScriptCascadeSelectValue(_scope.flowParameters[i], i, tableScope);
      }
    }

    _scope.flowParameters.forEach(function(item, index) {
      if (item.flowParamType === 'CASCADE') {
        let childIdList = [];
        let childValueList = [];
        item.paramsObj.forEach(function(child) {
          childIdList.push(child.paramName);
          let obj = {};
          obj.paramName = child.paramName;
          obj.description = child.description;
          obj.type = child.type;
          if (obj.type === 'enumtype' || obj.type === 'scriptType') {
            obj.paramValue = child.endValue;
            child.endValue = obj.paramValue;
          } else if (obj.type === 'key_value') {
            if (typeof (child.endValue) === 'object' &&
              Object.prototype.toString.call(child.endValue).toLowerCase() === '[object object]' &&
              !child.endValue.length) {
              obj.paramValue = child.endValue.value;
            } else {
              obj.paramValue = child.endValue;
            }
            child.endValue = obj.paramValue;
          } else if (obj.type === 'datetype' && tableScope['confirmCasTime_' + item.index + '_' + child.index]) {
            child.endValue = tableScope['confirmCasTime_' + item.index + '_' + child.index].getDateTime();
            obj.paramValue = child.endValue;
          } else {
            obj.paramValue = child.endValue;
            child.endValue = obj.paramValue;
          }
          childValueList.push(obj);
        });
        item.valueObject.childId = childIdList;
        item.valueObject.childRenValue = childValueList;
        item.value = JSON.stringify(item.valueObject);
      }
    });
  };

  function itemValueHandler(tableScope, i) {
    if ($('#itemValue_17' + i).length > 0) {
      let values = tableScope['itemValue_17' + i].getSelectedValues();
      _scope.flowParameters[i].value = values.join(',');
    }
    if ($('#itemValue_18' + i).length > 0) {
      let values = tableScope['itemValue_18' + i].getSelectedValues();
      _scope.flowParameters[i].value = values.join(',');
    }
  }

  function cascadeHandler(tableScope, i) {
    if (_scope.flowParameters[i].flowParamType === 'CASCADE') {
      for (let j = 0; j < _scope.flowParameters[i].paramsObj.length; j++) {
        if ($('#enumtype_' + i + '_' + j).length > 0) {
          let values = tableScope['enumtype_' + i + '_' + j].getSelectedValues();
          _scope.flowParameters[i].paramsObj[j].endValue = values.join(',');
        }
        if ($('#key_value_' + i + '_' + j).length > 0) {
          let values = tableScope['key_value_' + i + '_' + j].getSelectedValues();
          _scope.flowParameters[i].paramsObj[j].endValue = values.join(',');
        }
      }
    }
  }

  $scope.isScriptCascadeMode = function(selectValues) {
    return selectValues && selectValues.startsWith('[');
  };

  $scope.setScriptCascadeSelectValue = function(item, index, tableScope) {
    // 获取到第一层的值
    let firstValue = tableScope['scriptFirstValue_' + index].getSelectedValues();
    let value = [];
    for (let i = 0; i < firstValue.length; i++) {
      value.push({
        paramId: firstValue[i],
        children: $scope.getSpecifyParentSecondValue(item, index, firstValue[i], tableScope),
      });
    }

    item.value = JSON.stringify(value);
  };

  $scope.getSpecifyParentSecondValue = function(item, index, parentValue, tableScope) {
    let children = [];
    if (!item.secondValueList) {
      return children;
    }
    for (let j = 0; j < item.secondValueList.length; j++) {
      let tmpChild = {
        paramName: item.secondValueList[j].paramName,
        valueList: [],
      };
      if (tableScope['script_second_value_' + index + '_' + j]) {
        let secondValue = tableScope['script_second_value_' + index + '_' + j].getSelectedValues();
        for (let k = 0; k < secondValue.length; k++) {
          if (secondValue[k].startsWith(parentValue + '_')) {
            tmpChild.valueList.push({
              paramId: secondValue[k].slice((parentValue + '_').length),
            });
          }
        }
      }
      children.push(tmpChild);
    }

    return children;
  };

  function getFlowParamDisplayValue(flowParams) {
    if (!flowParams || flowParams.length === 0) {
      return '';
    }
    let displayValue = {};
    for (let i = 0; i < flowParams.length; i++) {
      displayValue[flowParams[i].name] = flowParams[i].value;
    }
    return JSON.stringify(displayValue);
  }

  function parseParamString(paramString) {
    if (!paramString || paramString === '') {
      return [];
    }
    return JSON.parse(paramString);
  }

  function getAllFlowInfo() {
    let req = {
      name: null,
      type: null,
      solution: null,
      triggerType: null,
      status: null,
      reviewStatus: null,
      flowId: null,
      sortMethod: 'desc',
      sortKey: 'submitDate',
      offset: 0,
      limit: 100,
    };
    ajax('post', 'json', JSON.stringify(req), '/rest/dvihealingwebsite/v1/dvihealingflowviewservice/queryFlowByPage', function(result) {
      _scope.allFlows = !result || !result.flows ? [] : result.flows.map(function(v) {
        return {
          name: v.basicInfo.name,
          solution: v.basicInfo.solution,
          id: v.basicInfo.flowId,
          type: v.triggerType,
        };
      });
      _scope.allOptionalSolutions = [];
      if (result && result.flows) {
        result.flows.forEach(flow => {
          let solution = flow.basicInfo.solution;
          if (_scope.allOptionalSolutions.indexOf(solution) === -1) {
            _scope.allOptionalSolutions.push(solution);
          }
        });
      }
      _scope.$apply();
    });
  }
}