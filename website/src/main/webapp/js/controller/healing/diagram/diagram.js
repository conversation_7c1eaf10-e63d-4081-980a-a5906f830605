

$CmpApp.directive('ueeDiagram', function($http, $templateCache, $UEE, $UI, $timeout) {
  // This is temp code. need to be move init diagram/header.uslx
  $templateCache.put('diagram.html', '<div class="uee-diagram" tabindex="0">  <div class="uee-diagram-client bf_cursor"><div class="uee-overflow-indicator"></div></div>  <!-- <input id="{{data_.id}}_data" type="hidden" dataname="bpm"/> -->  <div id="{{data_.id}}_mask" class="uee-diagram-mask bf_cursor"></div>  <div class="uee-diagram-marquee"></div> </div>');
  $templateCache.put('diagrammenu.html', '<div class="uee-diagram-menu uee-menu-container" style="display: none">  <div class="uee-menu-top">   <table width="100%" border="0" cellspacing="0" cellpadding="0">     <tr>      <td><div class="uee-menu-top-left"></div></td>      <td class="uee-menu-top-center"></td>      <td><div class="uee-menu-top-right"></div></td>     </tr>   </table>  </div>  <div class="uee-menu-center-left">    <div class="uee-menu-center-right">     <div class="uee-menu-center-center">       <ul class="uee-multimenu bc uee-diagram-multimenu" ng-transclude>       </ul>     </div>     <div class="uee-menu-nofloat"></div>    </div>  </div>  <div class="uee-menu-bottom">   <table width="100%" border="0" cellspacing="0" cellpadding="0">     <tr>      <td><div class="uee-menu-bottom-left"></div></td>      <td class="uee-menu-bottom-center"></td>      <td><div class="uee-menu-bottom-right"></div></td>     </tr>   </table>  </div></div>');
  $templateCache.put('diagrammenuitem.html', '<li style="position:relative;" class="a_link"> <a href="{{encodeURIComponen(data_.url)}}" class="bc uee-ui-ele uee-link" style="margin-left: 0px" disabler="{{data_.disabler}}" displayer="{{data_.display}}" onclick="return false;">  <img ng-src="{{data_.image}}"> {{data_.label}} </a>  <div ng-if="data_.hasseparatorafter" href="#none" class="uee-split"></div>  </li>');
  $templateCache.put('node.html', '<div class="uee-diagram-ele uee-node bf_cursor"> <canvas></canvas>  <div class="uee-node-adapter"></div> <pre class="uee-node-text"></pre></div>');
  $templateCache.put('path.html', '<div class="uee-diagram-ele uee-path">  <canvas></canvas></div>');

  return {
    restrict: 'E',
    replace: true,
    scope: true,
    transclude: true,
    templateUrl: 'diagram.html',
    controller: function($scope, $element, $attrs) {
      /**
       * provide a interface for uee:menu
       */
      this.registerMenu = function(menuId) {
        // create menu
        if (jBME.vars[menuId]) {
          jBME.vars[menuId].destroy();
          jBME.vars[menuId] = null;
        }
        var cfg = {
          id: menuId,
          bindid: $scope.id,
        };
        jBME.vars[menuId] = new jBME.diagrammultimenu(cfg);

        $UI.diagrams[$scope.id].menus = {
          id: menuId,
          contextmenu: jBME.vars[menuId],
        };
      };

      $scope.nodeAttrs = null;
      $scope.pathAttrs = null;
      this.addNodeAttrs = function(attrs) {
        $scope.nodeAttrs = $.extend({}, attrs);
      };
      this.addPathAttrs = function(attrs) {
        $scope.pathAttrs = $.extend({}, attrs);
      };

      $scope.jsonForDiagram = {};
      $scope.elementsExp = {
        nodes: null,
        paths: null,
      };

      var m_elementNames = ['nodes', 'paths'];
      var m_nodeAttrNames = [
        /* must*/
        ['key', 'id'],
        ['nodeface', 'title'],
        ['figures', 'figures'],
        ['jsonfigures', 'figures'],
        /* optional*/
        ['type', 'type'],
        ['logo', 'logo'],
        ['logobounds', 'logoBounds'],
        ['group', 'group'],
        ['parentkey', 'parent'],
        ['childrenkeys', 'children'],
        ['onpath', 'onpath'],
        ['activeMenuId', 'activeMenuId'],
        ['data', 'data'],
        ['jsonstrdata', 'jsonstrdata'],
        ['metatype', 'metatype'],
        ['anchornodes', 'anchornodes'],
      ];
      var m_pathAttrNames = [
        /* must*/
        ['key', 'id'],
        ['sourcekey', 'source'],
        ['targetkey', 'target'],
        ['sourceanchor', 'sourceAnchor'],
        ['targetanchor', 'targetAnchor'],
        ['figures', 'figures'],
        ['jsonfigures', 'figures'],
        /* optional*/
        ['textareas', 'textareas'],
        ['sourcestyle', 'sourceStyle'],
        ['targetstyle', 'targetStyle'],
        ['routelist', 'routeList'],
        ['controls', 'controls'],
        ['dasharray', 'dashArray'],
        ['data', 'data'],
        ['jsonstrdata', 'jsonstrdata'],
        ['metatype', 'metatype'],
      ];
      var m_logicalAttrs = [
        /* logical*/
        ['disable', 'disabled'],
        ['draggable', 'draggable'],
        ['resizable', 'resizable'],
        ['hoverable', 'hoverable'],
        ['editable', 'editable'],
        ['removable', 'removable'],
        ['visible', 'visible'],
      ];


      /**
       * parse model to json through diagram attrs
       * @returns the json for jBME.Diagram
       */
      this.getDiagramJson = function() {
        if ($attrs.property) {
          // can't use for(i in m_elementNames)
          for (var i = 0; i < m_elementNames.length; i++) {
            $scope.jsonForDiagram[m_elementNames[i]] = [];
            getElementJson(m_elementNames[i]);
          }
        }
      };

      function getElementJson(type) {
        if (!$scope.elementsExp[type]) {
          if ($attrs[type]) {
            $scope.elementsExp[type] = ($attrs.property + '.' + $attrs[type]);
          } else {
            $scope.elementsExp[type] = ($attrs.property + '.' + type);
          }
        }

        var elementModel = $UEE.propertyValue($scope, $scope.elementsExp[type]);
        if (elementModel) {
          // copy parent values, drop prefix
          var parentValues = {};
          var parentExps = $scope.elementsExp[type].split('.');
          for (var i = parentExps.length - 1; i >= 0; i--) {
            parentValues[parentExps[i]] = $UEE.propertyValue($scope, ((i == parentExps.length - 1) ?
              $scope.elementsExp[type] : $scope.elementsExp[type].substring(0, ($scope.elementsExp[type].indexOf(parentExps[i + 1]) - 1))));
          }

          var elementAttrs = null;
          if (type == m_elementNames[0]) {
            elementAttrs = $scope.nodeAttrs;
          } else if (type == m_elementNames[1]) {
            elementAttrs = $scope.pathAttrs;
          }

          if (elementAttrs) {
            for (var i = 0; i < elementModel.length; i++) {
              // create a new isolated scope to hold the row data, because the real exp is like model.nodes[i].title, but user will use title == 'Task1'
              var childScope = $scope.$new(true);
              childScope = $.extend(childScope, parentValues);

              var elementJson = parseJsonFromModel(type, elementModel[i], elementAttrs, childScope);
              $scope.jsonForDiagram[type].push(elementJson);
            }
          } else {
            $scope.jsonForDiagram[type] = elementModel;
          }
        }
      }

      function parseJsonFromModel(type, elementModel, elementAttrs, childScope) {
        var json = {};
        if (type == m_elementNames[0]) {
          for (i in m_nodeAttrNames) {
            if (elementAttrs[m_nodeAttrNames[i][0]]) {
              json[m_nodeAttrNames[i][1]] = (m_nodeAttrNames[i][0] == 'figures' || m_nodeAttrNames[i][0] == 'jsonstrdata' || m_nodeAttrNames[i][0] == 'logobounds' || m_nodeAttrNames[i][0] == 'onpath') ?
                angular.fromJson(elementModel[elementAttrs[m_nodeAttrNames[i][0]]]) :
                elementModel[elementAttrs[m_nodeAttrNames[i][0]]];
              childScope[elementAttrs[m_nodeAttrNames[i][0]]] = json[m_nodeAttrNames[i][1]];
            } else if (elementModel[m_nodeAttrNames[i][1]]) {
              json[m_nodeAttrNames[i][1]] = (m_nodeAttrNames[i][1] == 'figures' || m_nodeAttrNames[i][1] == 'jsonstrdata' || m_nodeAttrNames[i][1] == 'logoBounds' || m_nodeAttrNames[i][0] == 'onpath') ?
                angular.fromJson(elementModel[m_nodeAttrNames[i][1]]) :
                elementModel[m_nodeAttrNames[i][1]];
              childScope[m_nodeAttrNames[i][1]] = json[m_nodeAttrNames[i][1]];
            }
          }
        } else if (type == m_elementNames[1]) {
          for (i in m_pathAttrNames) {
            if (elementAttrs[m_pathAttrNames[i][0]]) {
              json[m_pathAttrNames[i][1]] = (m_pathAttrNames[i][0] == 'figures' || m_pathAttrNames[i][0] == 'jsonstrdata') ?
                angular.fromJson(elementModel[elementAttrs[m_pathAttrNames[i][0]]]) :
                elementModel[elementAttrs[m_pathAttrNames[i][0]]];
              childScope[elementAttrs[m_pathAttrNames[i][0]]] = json[m_pathAttrNames[i][1]];
            } else if (elementModel[m_pathAttrNames[i][1]]) {
              json[m_pathAttrNames[i][1]] = (m_pathAttrNames[i][1] == 'figures' || m_pathAttrNames[i][1] == 'jsonstrdata') ?
                angular.fromJson(elementModel[m_pathAttrNames[i][1]]) :
                elementModel[m_pathAttrNames[i][1]];
              childScope[m_pathAttrNames[i][1]] = json[m_pathAttrNames[i][1]];
            }
          }
        }

        // calculate the value in childScope
        if (elementAttrs['viewmode']) {
          var viewmodeval = elementAttrs['viewmode'];
          json['viewmode'] = (viewmodeval == 'view' || viewmodeval == 'edit') ? viewmodeval : (elementModel[viewmodeval] || childScope.$eval(viewmodeval));
        }
        for (i in m_logicalAttrs) {
          // store the temp variable
          var tempvar = elementAttrs[m_logicalAttrs[i][0]];
          if (tempvar) {
            if (tempvar == 'false' || tempvar == 'true') {
              json[m_logicalAttrs[i][1]] = (tempvar == 'true');
            } else if (typeof (elementModel[tempvar]) == 'boolean') {
              json[m_logicalAttrs[i][1]] = elementModel[tempvar];
            } else {
              json[m_logicalAttrs[i][1]] = childScope.$eval(tempvar);
            }
          }
        }
        return json;
      }

      $scope.modelcopy = null;
      /**
       * update model when diagram json changed
       * @param action the update action
       * @param json changed json
       * @returns
       */
      $scope.updateModel = function(action, json) {
        switch (action) {
          case 'nodecreate':
            var elementsModel = $UEE.propertyValue($scope, $scope.elementsExp[m_elementNames[0]]);
            if (elementsModel) {
              var elementModel = parseModelFromJson(m_nodeAttrNames, $scope.nodeAttrs, json);
              elementsModel.push(elementModel);
            }
            break;
          case 'nodedelete':
            var elementsModel = $UEE.propertyValue($scope, $scope.elementsExp[m_elementNames[0]]);
            if (elementsModel) {
              // delete by id
              var id = json[m_nodeAttrNames[0][1]];
              deleteNodeModel(id, elementsModel);
            }
            break;
          case 'pathcreate':
            var elementsModel = $UEE.propertyValue($scope, $scope.elementsExp[m_elementNames[1]]);
            if (elementsModel) {
              var elementModel = parseModelFromJson(m_pathAttrNames, $scope.pathAttrs, json);
              elementsModel.push(elementModel);
            }
            break;
          case 'pathdelete':
            var elementsModel = $UEE.propertyValue($scope, $scope.elementsExp[m_elementNames[1]]);
            if (elementsModel) {
              // delete by id
              var id = json[m_pathAttrNames[0][1]];
              deletePathModel(id, elementsModel);
            }
            break;
          case 'pathchange':
          case 'pathreconnect':
            var elementsModel = $UEE.propertyValue($scope, $scope.elementsExp[m_elementNames[1]]);
            if (elementsModel) {
              var id = json[m_pathAttrNames[0][1]];
              var elementModel = parseModelFromJson(m_pathAttrNames, $scope.pathAttrs, json);
              var idkey = $scope.pathAttrs ? $scope.pathAttrs[m_pathAttrNames[0][0]] : m_pathAttrNames[0][1];
              for (var i = 0; i < elementsModel.length; i++) {
                if (id == elementsModel[i][idkey]) {
                  elementsModel[i] = elementModel;
                  break;
                }
              }
            }
            break;
          case 'titlechange':
            var elementsModel = $UEE.propertyValue($scope, $scope.elementsExp[m_elementNames[0]]);
            if (elementsModel) {
              var id = json[m_nodeAttrNames[0][1]];
              var title = json[m_nodeAttrNames[1][1]];
              var idkey = $scope.nodeAttrs ? $scope.nodeAttrs[m_nodeAttrNames[0][0]] : m_nodeAttrNames[0][1];
              var titlekey = $scope.nodeAttrs ? $scope.nodeAttrs[m_nodeAttrNames[1][0]] : m_nodeAttrNames[1][1];
              for (var i = 0; i < elementsModel.length; i++) {
                if (id == elementsModel[i][idkey]) {
                  elementsModel[i][titlekey] = title;
                  break;
                }
              }
            }
            break;
          case 'nodeschange':
            var nodesModel = $UEE.propertyValue($scope, $scope.elementsExp[m_elementNames[0]]);
            if (nodesModel) {
              if (json.nodes) {
                var idkey = $scope.nodeAttrs ? $scope.nodeAttrs[m_nodeAttrNames[0][0]] : m_nodeAttrNames[0][1];
                for (id in json.nodes) {
                  var elementModel = parseModelFromJson(m_nodeAttrNames, $scope.nodeAttrs, json.nodes[id]);
                  for (var i = 0; i < nodesModel.length; i++) {
                    if (id == nodesModel[i][idkey]) {
                      nodesModel[i] = elementModel;
                      break;
                    }
                  }
                }
              }
            }
            break;
          case 'selectionsdelete':
            var nodesModel = $UEE.propertyValue($scope, $scope.elementsExp[m_elementNames[0]]);
            if (nodesModel) {
              for (id in json.nodes) {
                deleteNodeModel(id, nodesModel);
              }
            }

            var pathsModel = $UEE.propertyValue($scope, $scope.elementsExp[m_elementNames[1]]);
            if (pathsModel) {
              for (id in json.paths) {
                deletePathModel(id, pathsModel);
              }
            }
            break;
          case 'paste':
            var nodesModel = $UEE.propertyValue($scope, $scope.elementsExp[m_elementNames[0]]);
            if (nodesModel) {
              if (json.nodes) {
                for (id in json.nodes) {
                  var elementModel = parseModelFromJson(m_nodeAttrNames, $scope.nodeAttrs, json.nodes[id]);
                  nodesModel.push(elementModel);
                }
              }
            }

            var pathsModel = $UEE.propertyValue($scope, $scope.elementsExp[m_elementNames[1]]);
            if (pathsModel) {
              if (json.paths) {
                for (id in json.paths) {
                  var elementModel = parseModelFromJson(m_pathAttrNames, $scope.pathAttrs, json.paths[id]);
                  pathsModel.push(elementModel);
                }
              }
            }
            break;
          case 'all':
          default:
            var nodesModel = $UEE.propertyValue($scope, $scope.elementsExp[m_elementNames[0]]);
            if (nodesModel) {
              nodesModel.splice(0, nodesModel.length);
              if (json.nodes) {
                var modelrow;
                for (var i = 0; i < json.nodes.length; i++) {
                  modelrow = parseModelFromJson(m_nodeAttrNames, $scope.nodeAttrs, json.nodes[i]);
                  nodesModel.push(modelrow);
                }
              }
            }

            var pathsModel = $UEE.propertyValue($scope, $scope.elementsExp[m_elementNames[1]]);
            if (pathsModel) {
              pathsModel.splice(0, pathsModel.length);
              if (json.paths) {
                var modelrow;
                for (var i = 0; i < json.paths.length; i++) {
                  modelrow = parseModelFromJson(m_pathAttrNames, $scope.pathAttrs, json.paths[i]);
                  pathsModel.push(modelrow);
                }
              }
            }
            break;
        }

        $scope.modelcopy = $UEE.propertyValue($scope, $attrs.property);
      };

      function deleteNodeModel(id, elementsModel) {
        if ($scope.nodeAttrs) {
          for (var i = 0; i < elementsModel.length; i++) {
            if (id == elementsModel[i][$scope.nodeAttrs[m_nodeAttrNames[0][0]]]) {
              elementsModel.splice(i, 1);
              break;
            }
          }
        } else {
          for (var i = 0; i < elementsModel.length; i++) {
            if (id == elementsModel[i][m_nodeAttrNames[0][1]]) {
              elementsModel.splice(i, 1);
              break;
            }
          }
        }
      }

      function deletePathModel(id, elementsModel) {
        if ($scope.pathAttrs) {
          for (var i = 0; i < elementsModel.length; i++) {
            if (id == elementsModel[i][$scope.pathAttrs[m_pathAttrNames[0][0]]]) {
              elementsModel.splice(i, 1);
              break;
            }
          }
        } else {
          for (var i = 0; i < elementsModel.length; i++) {
            if (id == elementsModel[i][m_pathAttrNames[0][1]]) {
              elementsModel.splice(i, 1);
              break;
            }
          }
        }
      }

      function parseModelFromJson(m_AttrNames, elementAttrs, json) {
        if (!elementAttrs) {
          return json;
        }

        var elementModel = {};
        for (var j = 0; j < m_AttrNames.length; j++) {
          if (elementAttrs[m_AttrNames[j][0]]) {
            elementModel[elementAttrs[m_AttrNames[j][0]]] = (m_AttrNames[j][0] == 'figures' || m_AttrNames[j][0] == 'jsonstrdata' || m_AttrNames[j][0] == 'logobounds' || m_AttrNames[j][0] == 'onpath') ?
              angular.toJson(json[m_AttrNames[j][1]]) : json[m_AttrNames[j][1]];
          } else if (json[m_AttrNames[j][1]]) {
            elementModel[m_AttrNames[j][1]] = ((m_AttrNames[j][1] == 'figures' && m_AttrNames[j][0] != 'jsonfigures') || m_AttrNames[j][1] == 'jsonstrdata' || m_AttrNames[j][1] == 'logoBounds' || m_AttrNames[j][1] == 'onpath') ?
              angular.toJson(json[m_AttrNames[j][1]]) : json[m_AttrNames[j][1]];
          }
        }

        return elementModel;
      }

      /**
       * The update interface don't remove old element, we have to remove them.
       * Otherwise the element will be repeated.
       *
       * @returns
       */
      this.removeOldElements = function() {
        $scope.updateLocked = true;
        var diagram = $UI.diagrams[$scope.id];
        if (diagram.nodes) {
          for (var i in diagram.nodes) {
            diagram.removeById(diagram.nodes[i].id);
          }
        }
        if (diagram.paths) {
          for (var i in diagram.paths) {
            diagram.removeById(diagram.paths[i].id);
          }
        }
        $scope.updateLocked = false;
      };

      if (!$UI.diagrams) {
        $UI.diagrams = {};
      }

      $UI.diagram = function(id) {
        return $UI.diagrams[id];
      };

      for (var i in jBME.Diagram) {
        $UI.diagram[i] = jBME.Diagram[i];
      }
    },
    compile: function(tElement, tAttr, linker) {
      /**
       * startup JBME.Diagram
       */
      function startDiagram($element, $scope, $attr) {
        $element.attr('id', $scope.id);
        let nodeHtml = $templateCache.get('node.html');
        let pathHtml = $templateCache.get('path.html');

        // create diagram
        let options = {
          nodeHtml: nodeHtml,
          pathHtml: pathHtml,
          options: $attr.options || '',
        };

        if ($attr.viewmode == 'view' || $attr.viewmode == 'edit') {
          options.viewmode = $attr.viewmode;
        }

        if ($UI.diagrams[$scope.id]) {
          $UI.diagrams[$scope.id].destroy();
          $UI.diagrams[$scope.id] = null;
        }
        $UI.diagrams[$scope.id] = new jBME.Diagram($scope.id, options);

        let editor = new jBME.Editor($UI.diagrams[$scope.id]);
        $UI.diagrams[$scope.id].editor = editor;
        editor.startup();

        $timeout(function() {
          $element.trigger('diagramready');
        });
      }

      return function link($scope, $element, $attr, control) {
        // clone fire
        var childScope = $scope.$new(false);
        linker(childScope, function(clone) {
          $element.append(clone);
        });

        // destroy diagram
        $scope.$on('$destroy', function() {
          if ($UI.diagrams[$attr.id]) {
            $UI.diagrams[$attr.id].destroy();
            $UI.diagrams[$attr.id] = null;
          }
        });
        if (!$attr.id) {
          $scope.id = ('diagram_' + $.now());
        } else {
          $scope.id = $attr.id;
        }
        startDiagram($element, $scope, $attr);


        /**
         * When property value in model changed, update the diagram
         */
        $scope.$watch(function() {
          return $UEE.propertyValue($scope, $attr.property);
        }, function(newV, oldV) {
          var begin = $.now();
          // In my opinion, this callbck will excute instantly when the data in $Model changed
          // But in fact it's excution delayed and mixed with th notifychange event,
          // so use $scope.modelcopy to prevent.
          if (newV && (newV != $scope.modelcopy)) {
            if ((typeof newV) === 'string') {
              newV = angular.fromJson(newV);
              $UEE.propertyValue($scope, $attr.property, newV);
            } else {
              control.removeOldElements();
              control.getDiagramJson();
              $UI.diagrams[$scope.id].update($scope.jsonForDiagram);
            }
          }
        });

        /**
         * When the view operation triggered json change, update the model
         */
        $element.on(jBME.Diagram.EVENT.notifychange, function(event) {
          if (!$scope.updateLocked) {
            var value;
            if (event.action == 'nodecreate' || event.action == 'nodedelete' || event.action == 'titlechange' ||
              event.action == 'pathcreate' || event.action == 'pathdelete' || event.action == 'pathchange' || event.action == 'pathreconnect') {
              value = event.jElement.getJSONData();
            } else {
              value = {};
              value.nodes = {};
              value.paths = {};
              for (id in event.jElement) {
                if (event.jElement[id].options.removable == false) {
                  return;
                }
                if (event.jElement[id].isnode) {
                  value.nodes[id] = event.jElement[id].getJSONData();
                } else if (event.jElement[id].ispath) {
                  value.paths[id] = event.jElement[id].getJSONData();
                }
              }
            }

            $scope.updateModel(event.action, value);
          }
        });
      };
    },
  };
});


$CmpApp.directive('ueeNode', function($http, $templateCache) {
  return {
    require: ['?^ueeDiagram'],
    restrict: 'E',
    scope: true,
    controller: function control($scope, $element, $attrs) {
    },
    compile: function(tElement, tAttr, linker) {
      return function link(scope, iElement, iAttrs, control) {
        control[0].addNodeAttrs(iAttrs);
      };
    },
  };
});

$CmpApp.directive('ueePath', function($http, $templateCache) {
  return {
    require: ['?^ueeDiagram'],
    restrict: 'E',
    scope: true,
    controller: function control($scope, $element, $attrs) {
    },
    compile: function(tElement, tAttr, linker) {
      return function link(scope, iElement, iAttrs, control) {
        control[0].addPathAttrs(iAttrs);
      };
    },
  };
});

$CmpApp.directive('ueeDiagrammenu', function($http) {
  return {
    restrict: 'E',
    replace: true,
    scope: true,
    transclude: true,
    require: ['?^ueeDiagram'],
    templateUrl: 'diagrammenu.html',
    controller: function($scope, $element, $attrs) {
      $attrs.id = $attrs.id || ('menu_' + $.now());
      $scope.id = $attrs.id;
    },
    link: function($scope, $element, $attrs, $controls) {
      // register to parent control
      $scope.$watch('id', function(newV, oldV) {
        if (newV) {
          if (newV && $element.find('.uee-multimenu')) {
            $($element.find('.uee-multimenu')[0]).attr('id', newV);
            $controls[0] && $controls[0].registerMenu(newV);
          }
        }
      }, true);
    },
  };
});

$CmpApp.directive('ueeDiagrammenuitem', function($templateCache) {
  return {
    templateUrl: 'diagrammenuitem.html',
    require: ['?^ueeDiagrammenu'],
    restrict: 'E',
    scope: true,
    replace: true,
    transclude: true,
    controller: function($scope, $element, $attrs, $http, $interpolate) {
      var labelStr = $interpolate($attrs.label)($scope);
      $scope.data_ = {
        label: labelStr,
        url: $attrs.url || '#none',
        hasimage: $attrs.img && true,
        image: $attrs.img || '/dvihealingwebsite/images/healing/diagram/empty.gif',
        disabler: $attrs.disabled || false,
        display: $attrs.display || true,
        hasseparatorafter: $attrs.separatorafter && true,
      };
    },
    compile: function(tElement, tAttr, linker) {
      return function link(scope, iElement, iAttrs, parentCtrls) {
        // clone fire dom
        var childScope = scope.$new(false);
        linker(childScope, function(clone) {
          $(iElement).append(clone);
        });

        // generate the children menuitem dom element
        var html = $templateCache.get('diagrammenu.html');
        var jqElem = $(html);
        var jqUl = jqElem.find('ul');
        var haschild = false;
        iElement.children('li').each(function() {
          haschild = true;
          jqUl.append(this);
        });
        if (haschild) {
          // prevent id conflict
          jqUl.removeAttr('id');
          iElement.append(jqElem);
        }
      };
    },
  };
});


/**
 * Define jBME common variables
 */

(function() {
  // Define or get our global core object instance, which is quite a simple object used as singleton.
  var jBME = window.jBME = (window.jBME || {});

  /**
   * Use jBME.ready(callback) to add function.
   * Use jBME.ready() to run all
   * @param func
   * @return
   */
  jBME.readyFunc = [];
  jBME.ready = function(func) {
    // All scripts have no need to run again after shifting position in DOM-tree. TODO , to be refactor.
    if (jBME.lockReady) {
      return;
    }
    if (func == undefined) { // run
      while (jBME.readyFunc.length > 0) {
        var f = jBME.readyFunc.shift();
        f();
      }
    } else { // save function to run when ready.
      jBME.readyFunc.push(func);
    }
  };

  /**
   * Package: jBME.meta.
   */
  jBME.meta = {
    cbme_clear: 'cbme_clear',
    cbme_clear_item: 'cbme_clear_item',
    cbme_disabled: 'cbme_disabled',
    cbme_region_ignore: 'cbme_region_ignore',
    cbme_selector: 'cbme_selector',
  };

  /**
   * Define package: jBME.dom. Caching some special global DOM(wrapped in jQuery or not) elements for speed.
   */
  jBME.dom = {
    $view: $([]), // caching for jQuery(".bc_view"), <bme:view>. Initializated when page is loaded and ready.
  };

  // Define package: jBME.util .
  jBME.util = {
    description: 'Define common JavaScript functions in this package.',

    /**
     * Return JSON or other Javascript object length.
     */
    objectLength: function(obj) {
      var count = 0;
      for (var i in obj) {
        ++count;
      }
      return count;
    },
    /**
     * Just like jQuery.fn.hasClass(), but is static function.
     * For performance crucial scenes(eg: in loop), static function is faster then jQuery(element)(0.14ms);
     * @param dom, DOM element.
     */
    rclass: /[\n\t]/g,
    hasClass: function(dom, className) { // Codes below come form jQuery-1.4.4 jQuery.fn.hasClass().
      var className = ' ' + className + ' ';
      return dom.className && ((' ' + dom.className + ' ').replace(this.rclass, ' ').indexOf(className) > -1);
    },

    /**
     * Return a string describe the object attribute.
     */
    toString: function(object, name, includeFunc) {
      // default arguments.
      name = (name || object);
      includeFunc = (includeFunc || false);

      // ok, get string.
      var str = name + '{ ';
      for (var attr in object) {
        if (typeof object[attr] == 'function' && !includeFunc) {
          continue;
        }
        str += attr;
        str += ':';
        str += object[attr];
        str += ', ';
      }
      str += '}';
      return str;
    },

    /**
     * Get a valid id that can use as jQuery selector. Using \\ before some characters.
     */
    encodeId: function(id) {
      return id.replace('[', '\\[').replace(']', '\\]').replace('.', '\\.');
    },

    /**
     * Get valid jQuery selector from id(s) expression.
     * You can use the return string directly as jQuery selector to get object(s).
     * You will get an objects set union all selector results.
     *
     * @param idExp : "id1, id2, ,id3" or "id1" or "" or undefined.
     * @return jQuery selector: "#id1,#id2,noneTagSelector,#id3," or "#id1," or "noneTagSelector"
     */
    getIdSelector: function(idExp) {
      var selector = '';
      var ids = jQuery.trim(idExp).split(',');
      for (var i = 0; i < ids.length;) {
        var id = jQuery.trim(ids[i]);
        selector += (id ? '#' + id : 'noneTagSelector');
        if (++i < ids.length) {
          selector += ',';
        }
      }
      return selector;
    },

    /**
     * Get elements order by "selector1,selector2,selectorN"
     * Note: After jQuery 1.3.2 version, the elements returned by jQuery(expression, [context])
     * are order in DOM. So this function provides a way to get elements in order by selector.
     */
    getElementsOrderBySelector: function(selector, context) {
      var elements = [];
      var selectors = selector.split(',');
      for (var j = 0; j < selectors.length; ++j) {
        var selectorj = jQuery.trim(selectors[j]);
        if (selectorj != '') {
          jQuery(selectorj, context).each(function() {
            elements.push(this);
          });
        }
      }
      return jQuery(elements);
    },
    /**
     * Create a jQuery(div) wrapper from HTML.
     * Note: Only used in fire render mode as render resource which is speed crucial.
     *    For more generic scenes, please use jQuery.clean([html]) instead, which has more special tests.
     * ------------------------------------------------------------------------------------
     * @see jQuery-1.4.4.js/jQuery.clean()
     * @see jQuery-1.4.4.js/IE can't serialize <link> and <script> tags normally
     *   if ( !jQuery.support.htmlSerialize ) {
     *     wrapMap._default = [ 1, "div<div>", "</div>" ];
     *   }
     * @return jQuery(<div>html</div>)
     */
    createWrapDiv: function(html) {
      var fragment = document.createDocumentFragment();
      var div = document.createElement('div');
      fragment.appendChild(div);
      div.innerHTML = 'div<div>' + encodeURIComponent(html) + '</div>';

      // Find and replace the <script> elements created by innerHTML with created by document.createElement()
      // Cause: HTML5: "Note: script elements inserted using innerHTML do not execute when they are inserted."
      if (!jBME.support.evalScriptFromInnerHTML && jBME.support.evalScriptFromAPI) {
        var scripts = $.makeArray(div.getElementsByTagName('script'));
        for (var i = 0; i < scripts.length; ++i) {
          var s = scripts[i];
          var script = document.createElement('script');
          script.text = s.text;

          // Clone attributes
          for (var a = 0; a < s.attributes.length; ++a) {
            script.setAttribute(s.attributes[a].nodeName, s.attributes[a].nodeValue);
          }

          // Replace the <script> element created by innerHTML with created by document.createElement()
          s.parentNode.replaceChild(script, s);
          s = null, scripts[i] = null, script = null;
        }
      }
      return div.lastChild;
    },
    /**
     * Replace DOM element. >> !Higher Performance than $.fn.repalceWith()!
     * ------------------------------------------------------------------------------------
     * Note: jQuery.fn.replaceWidth() do three things,
     *    1.clean data. 2.remove old DOM 3.append new DOM(eval script within it).
     * However, 'remove' and 'append' will cause browser rendering twice.
     * Here, we use w3c Node.replaceChild(newNode, oldNode) directly to reduce rendering for speed.
     * Of course we will clean data before replacing just as jQuery does.
     */
    replaceElement: function(newElem, oldElem) {
      if (oldElem) {
        // Replace with empty.
        if (!newElem) {
          $(oldElem).remove();
          return oldElem;
        }

        // 1.Clean data of old element.(// 1: ELEMENT_NODE)
        if (oldElem.nodeType === 1) { // Codes from jquery-1.4.4/$.fn.remove()
          jQuery.cleanData(oldElem.getElementsByTagName('*'));
          jQuery.cleanData([oldElem]);
        }

        // 2.Replace DOM
        if (oldElem.parentNode) {
          oldElem.parentNode.replaceChild(newElem, oldElem);
        }

        // 3.evalScript Node(IE,Chrome won't eval <script> within newElem when appending to DOM tree)
        if (!jBME.support.evalScriptFromAPI && jQuery.contains(document, newElem)) {
          var scripts = ($.nodeName(newElem, 'SCRIPT') ? [newElem] : $.makeArray(newElem
            .getElementsByTagName('script')));
          for (var i = 0; i < scripts.length; ++i) { // Codes from jquery-1.4.4/function evalScript(). But ignore 'elem.parentNode.removeChild(elem)'.
            var elem = scripts[i];
            if (elem.src) {
              jQuery.ajax({
                url: elem.src,
                async: false,
                dataType: 'script',
              });
            } else {
              jQuery.globalEval(elem.text || elem.textContent || elem.innerHTML || '');
            }
          }
        }
      }
      return oldElem;
    },
    /**
     * Replace element(s) according to the given selector in a context
     *  with the element(s) in another context that have the same id.
     *
     * In other words, the function will replace ALL the element(s) in jqToContext selected by the selector
     *  by the element(s) with the same id in jqFromContext.
     *
     * Note: replacing order is according to elements order in selector(not in DOM)
     * @param selector  jQuery selector
     * @param jqFromContext Resources holder context,
     *     you may need to wrap your resources within an dummy element(eg:<div>),
     *     cause the jQuery.find() method will not return the element set itself like jQuery.filter() does.
     * @param jqToContext Default is current document.
     * @param callbackoneach
     */
    replaceElementsById: function(selector, jqFromContext, jqToContext, callbackOneach) {
      // Find element by id selector
      var $oldElements = this.getElementsOrderBySelector(selector, (jqToContext || document));
      if ($oldElements.length == 0) {
        return $oldElements;
      }

      // Replace elements by new ones with same id.
      var newElements = [];
      var $subcache = jqFromContext.children();
      $oldElements.each(function(i) {
        var oldElem = this;
        var newElem = $subcache.filter(function() {
          return oldElem.id == this.id;
        })[0] ||
          jqFromContext.find('#' + this.id)[0];
        if (oldElem != newElem && ((callbackOneach ? callbackOneach(oldElem) : true) !== false)) {
          jBME.dom.$view.trigger('beforerender', oldElem.id); // TODO

          jBME.util.replaceElement(newElem, oldElem);
          newElements.push(newElem);
        }
      });
      return $(newElements);
    },

    /**
     * Return function name from standard JS function call specification written in string.
     * @param functionExpresion (String)
     *    eg: "a.b.c.func( 123, 'str, 1234')"
     * @return String
     *    eg: a.b.c.func
     *
     */
    getNameByFunExp: function(functionExpresion) {
      functionExpresion = (functionExpresion || '');
      var iarg = functionExpresion.indexOf('(');
      if (iarg <= 0) {
        return jQuery.trim(functionExpresion);
      }
      return jQuery.trim(functionExpresion.substring(0, iarg));
    },

    /**
     * Return Arguments using standard JS function call specification written in string.
     * @param functionExpresion
     *    (String), eg: "a.b.c.func( 123, 'str, 1234')"
     * @return Array
     *    [123]
     *    ['str, 1234']
     */
    getArgsByFunExp: function(functionExpresion, jFire) {
      functionExpresion = (functionExpresion || '');

      // Replace the function name. eg: getArguments(123, 'str1, 234')
      var iarg = functionExpresion.indexOf('(');
      if (iarg < 0) {
        return [];
      }

      function getArguments() {
        return arguments;
      }
      var callExpression = 'getArguments' + functionExpresion.substring(iarg);

      // Get arguments array from JavaScript Compiler.
      var argumentArrayObject = [];
      try {
        argumentArrayObject = (eval(callExpression) || []);
      } catch (e) {
      }

      var argumentArray = new Array(argumentArrayObject.length);
      for (var i = 0; i < argumentArrayObject.length; ++i) {
        argumentArray[i] = argumentArrayObject[i];
      }
      return argumentArray;
    },

    /**
     * Check/set whether a DOM element is disabled within BME context.
     * (has attribute "disabled" or has css class "cbme_disabled".
     * @param element DOM element.
     */
    isDisabled: function(element) {
      var jq = jQuery(element);
      return element && (jq.hasClass(jBME.meta.cbme_disabled) || jq.is('[disabled]'));
    },
    setDisabled: function(element, disabled) {
      if (element) {
        jQuery(element).toggleClass(jBME.meta.cbme_disabled, disabled);
      }
    },
    encodeForEval: function(str) {
      return str.replace(/[\r]/g, '').replace(/[\n]/g, '');
    },
  }; // jBME.util package define.

  // Define package: jBME.var
  jBME.vars = {
    description: 'Save var in this package.',
  };
})();


(function($, jBME) {
  /**
   * Request Mechanism
   */
  jBME.Request = function(cmdThisRef) {
    this.queue = [];
    this.cmdThisRef = cmdThisRef;
  };
  jBME.Request.TYPES = {
    CREATE_NODE: 0x00000001,
    CREATE_PATH: 0x00000010,
  };
  jBME.Request.prototype = {
    add: function(type, execute, undo) { // Push the "Command{}" into queue.
      this.queue.push({
        type: type,
        execute: execute,
        undo: undo,
      });
    },
    remove: function() {
      this.queue = [];
    },
    perform: function(type, arg0, arg1 /* ...*/) {
      if (this.queue.length > 1) { // When click the node|path in panel n times, we just draw the last one.
        this.queue.splice(0, this.queue.length - 1);
      }
      var cmd = this.queue[0];
      if (cmd && (cmd.type & type)) {
        if (cmd.execute.apply(this.cmdThisRef, $.makeArray(arguments).slice(1)) !== false) { // If Command return false, it remains in the queue and will run again in future,
          // otherwise we remove the executed command from queue.
          this.queue.shift();
        }
      }
    },
    isEmpty: function() {
      return this.queue.length == 0;
    },
    has: function() {
      return this.queue.length > 0;
    },
    queue: null, /* [], init in construtor*/
  };

  jBME.Clazz = function() {
  };
  /**
   * Empty constructor.
   * @methodOf Clazz.prototype
   */
  jBME.Clazz.prototype = {
    construct: function() {
    },
  };
  jBME.Clazz.deepCopy = function(p, c) {
    var c = c || {};
    for (var i in p) {
      if (p[i] && (typeof p[i] === 'object')) {
        c[i] = (p[i].constructor === Array) ? [] : {};
        jBME.Clazz.deepCopy(p[i], c[i]);
      } else {
        c[i] = p[i];
      }
    }
    return c;
  };

  jBME.Clazz.extend = function(def) {
    var classDef = function() {
      if (arguments[0] !== jBME.Clazz) {
        this.construct.apply(this, arguments);
      }
    };

    var proto = jBME.Clazz.deepCopy(new this(jBME.Clazz), {});
    var superClass = this.prototype;

    for (var n in def) {
      var item = def[n];
      if (item instanceof Function) {item.$ = superClass;}
      proto[n] = item;
    }

    classDef.prototype = proto;

    // Give this new class the same static extend method
    classDef.extend = this.extend;
    return classDef;
  };
})(jQuery, window.jBME = (window.jBME || {}));

/* !
 * BME UI Component - Diagram Editor
 *
 * Dependency
 * ----------------------------------------------------------------------------
 * JS:
 * @requires jQuery v1.4.4 or later
 *
 * CSS:
 * bme.diagram.css
 *
 * HTML:
 * diagram.ftl
 * >Please keep fresh with the HTML!
 * ----------------------------------------------------------------------------
 * Copyright (c) Huawei Technologies Co., Ltd. 2010-2011. All rights reserved.
 *
 */

(function($, jBME) {
  /**
   * [jBME.Editor] Class and constructor
   * @param diagram: diagram instance
   */
  jBME.Editor = jBME.Clazz.extend({

    pluginInstances: [],

    construct: function(diagram) {
      this.diagram = diagram;
    },
    startup: function() {
      for (var i = 0; i < jBME.Editor.plugins.length; i++) {
        this.registerPlugin(jBME.Editor.plugins[i]);
      }
    },
    registerPlugin: function(pluginDefinition) {
      var plugin = new pluginDefinition(this);
      this.pluginInstances[this.pluginInstances.length] = plugin;
      plugin.init(this.diagram);

      var eventHandlers = plugin.eventHandlers;
      for (var j = 0; j < eventHandlers.length; j++) {
        var handler = plugin[eventHandlers[j].handler];
        this.registerEvent(eventHandlers[j].event, handler, eventHandlers[j].targetselector, plugin, eventHandlers[j].sourceselector);
      }
    },
    registerEvent: function(event, handler, targetselector, plugin, sourceselector) {
      var jDiagram = this.diagram;

      // Add plugin.name as event's namespace
      if (plugin && plugin.name) {
        event = [event, plugin.name].join('.');
      }

      // THe hander of this event
      var wrappedHandler = function(e) {
        if (jDiagram.jHoverElement && jDiagram.$activeMenu && jDiagram.isOnActiveMenu(e)) { // Doesn't response event when operate activemenu.
          return;
        }

        if (plugin) {
          plugin._refreshElement(this);
          handler.call(plugin || this, e, plugin.jElement, plugin.editor && plugin.editor.diagram);
          plugin.jElement = null;
        }
      };

      if (sourceselector) { // Event on specific sourceselector element( For events which can't bubble ).
        $(sourceselector).length > 0 && $(sourceselector).bind(event, wrappedHandler);
      } else if (targetselector == null) { // Event on all .uee-diagram-ele(nodes and paths).
        this.diagram.$box.delegate('div.uee-diagram-ele', event, wrappedHandler);
      } else if (targetselector == 'all') { // Event on diagram scope(nodes, paths and blank space).
        this.diagram.$box.bind(event, wrappedHandler);
      } else if (targetselector == 'document') { // Event on document.
        $(document).bind(event, wrappedHandler);
      } else { // Event on specific targetselector element( For events which can bubble ).
        this.diagram.$box.delegate(targetselector, event, wrappedHandler);
      }
    },
    /**
     * Remove plugins from diagram
     * @param plugins array
     *    The plugins in diagram. eg : [jBME.Diagram.nodedblclickPlugin]
     */
    unregisterPlugin: function(plugins) {
      for (var k = 0; k < plugins.length; k++) {
        this.unregister(plugins[k]);
      }
    },
    unregister: function(pluginDefinition) {
      for (var i = 0; i < this.pluginInstances.length; i++) {
        var plugInstance = this.pluginInstances[i];

        if (plugInstance instanceof pluginDefinition) {
          for (var j = 0; j < plugInstance.eventHandlers.length; j++) {
            var handler = plugInstance.eventHandlers[j];
            this.unregisterEvent(handler.event, plugInstance, handler.targetselector, handler.sourceselector);
          }
          this.pluginInstances.splice(i, 1);
          plugInstance = null;

          jBME.Editor.plugins.splice(i, 1);

          return;
        }
      }
    },
    unregisterEvent: function(event, plugin, targetselector, sourceselector) {
      if (plugin && plugin.name) {
        event = [event, plugin.name].join('.');
      }

      if (sourceselector) { // Event on specific sourceselector element( For events which can't bubble ).
        $(sourceselector).length > 0 && $(sourceselector).unbind(event);
      } else if (targetselector == null) { // Event on all .uee-diagram-ele(nodes and paths).
        this.diagram.$box.undelegate('div.uee-diagram-ele', event);
      } else if (targetselector == 'all') { // Event on diagram scope(nodes, paths and blank space).
        this.diagram.$box.unbind(event);
      } else if (targetselector == 'document') { // Event on document.
        $(document).unbind(event);
      } else { // Event on specific targetselector element( For events which can bubble ).
        this.diagram.$box.undelegate(targetselector, event);
      }
    },
    unregisterDelegateEvents: function() {
      var plugins = this.pluginInstances;

      for (var i = 0; i < plugins.length; i++) {
        var plugInstance = plugins[i];

        for (var j = 0; j < plugInstance.eventHandlers.length; j++) {
          var handler = plugInstance.eventHandlers[j];
          this.unregisterEvent(handler.event, plugInstance, handler.targetselector, handler.sourceselector);
        }
      }
    },
  });

  jBME.Editor.register = function(plugin) {
    jBME.Editor.plugins[jBME.Editor.plugins.length] = plugin;
  };

  jBME.Editor.plugins = [];

  jBME.AbstractPlugin = jBME.Clazz.extend({
    construct: function(editor) {
      this.editor = editor;
    },
    init: function() {
    },
    eventHandlers: [],
    name: '',
    editor: null,
    jDiagram: null,
    jElement: null,
    _refreshElement: function(e) {
      this.jElement = this.editor.diagram.nodes[e.id] || this.editor.diagram.paths[e.id];
      if (!this.jElement && e.className && e.className.indexOf('uee-node') > 0 && e.parentNode.className.indexOf('uee-node') > 0) {
        this.jElement = this.editor.diagram.nodes[e.parentNode.id];
      }
    },
  });
})(jQuery, window.jBME = (window.jBME || {}));

/* !
 * BME UI Component - Diagram
 * dependency
 * ----------------------------------------------------------------------------
 * JS : @requires jQuery v1.4.4 or later
 * CSS : bme.diagram.css
 * HTML: diagram.ftl, node.ftl, path.ftl
 * ----------------------------------------------------------------------------
 * @created by Derek 2011-12-12 Huawei Technologies Co., Ltd
 */

(function($, jBME) {
  // Global Tools
  var $doc = $(document);
  var sin45d = Math.sin(Math.PI / 4);
  var ZERO = 0.0001;

  $.browser = {};
  $.browser.mozilla = /firefox/.test(navigator.userAgent.toLowerCase());
  $.browser.webkit = /webkit/.test(navigator.userAgent.toLowerCase());
  $.browser.opera = /opera/.test(navigator.userAgent.toLowerCase());
  $.browser.msie = /msie/.test(navigator.userAgent.toLowerCase());
  var matched = uaMatch();
  $.browser.version = matched.version;
  /**
   * Diagram Namespace and Constructor.
   * Provide a platform holding nodes and paths, and ways to create
   */
  jBME.Diagram = function(id, options) {
    this.init(id, options);
  };

  // Static Constants: Public Event on Diagram.
  jBME.Diagram.EVENT = {
    /**
     * trigger when change the selected elements.
     * Event attribute data:
     * jEvent.firstSelection  -->first selected element
     * jEvent.selectionSize  -->the size of selected elements
     * jEvent.selections    -->all selected elements
     * jEvent.oldSelections  -->old elements before change
     */
    selectionchanged: 'selectionchanged', // trigger when change the selected elements.

    /**
     * trigger when create node or path,remove node or path and change title.
     * Event attribute data:
     * jEvent.jElement      -->changed element
     * jEvent.action       -->operation contains nodecreate, nodedelete, pathcreate, pathdelete, titlechange
     * jEvent.triggerSource    -->cause of this event contains outer(panel click, right click and others operation by api), pathdblclick, undoredo, paste, cut
     */
    notifychange: 'notifychange', // trigger when create node or path,remove node or path and change title.

    /**
     * trigger before create node or path.
     * Event attribute data:
     * jEvent.options       -->the options for creating node
     * jEvent.event        -->Cause to create nodes
     * jEvent.triggerSource    -->cause of this event contains outer(panel click, right click and others operation by api), pathdblclick, undoredo, paste, cut
     */
    beforecreatenode: 'beforecreatenode', // trigger before create node or path.

    /**
     * trigger when draw line' begin node.
     * Event attribute data:
     * jEvent.jElement  --> begin node
     */
    connectstart: 'connectstart', // trigger when draw line' begin node.

    /**
     * trigger when draw line' end node.
     * Event attribute data:
     * jEvent.jElement  --> path, jElement.jSource is source of this path, jElement.jTarget is target of this path
     */
    connectend: 'connectend', // trigger when draw line' end node.

    /**
     * trigger when the end of drag.
     * Event attribute data:
     * jEvent.jElement     --> dragging node
     * jEvent.dragPosition   --> drag start position
     * jEvent.dropPosition   --> drop position
     */
    ondragover: 'ondragover', // trigger when the end of drag.

    /**
     * trigger when double click node.
     * Event attribute data:
     * jEvent.jElement  --> clicked node
     */
    dblclick: 'dblclick', // trigger when double click node.

    /**
     * trigger when create a new bend.
     * Event attribute data:
     * jEvent.jElement  --> clicked path
     * jEvent.index    --> the index of elbowpoints
     * jEvent.elbowPoints --> the points of elbows
     */
    bendcreate: 'bendcreate', // trigger when create a new bend of path.

    /**
     * trigger when move a bend.
     * Event attribute data:
     * jEvent.jElement  --> clicked path
     * jEvent.index    --> the index of elbowpoints
     * jEvent.elbowPoints --> the points of elbows
     */
    bendmove: 'bendmove', // trigger when move bend of path.
  };

  // To store the metadata of diagram
  jBME.Diagram.MetaData = {};

  /**
   * Diagram Default options.
   * Also defines the data structure for nodes and paths.
   */
  jBME.Diagram.options = {
    node: {
      margin: 10,
      selected: {
        strokeStyle: '#333333',
        fillStyle: 'rgba(0,0,0,0)',
        lineWidth: 2,
      },
      disabledContext: {
        strokeStyle: '#333333',
        fillStyle: 'rgba(204, 204, 204, 0.4)',
        lineWidth: 1,
      },
      anchor: {
        normal: {
          strokeStyle: 'black',
          fillStyle: 'white',
          lineWidth: 1,
          radius: 2.8,
        },
        selected: {
          strokeStyle: 'black',
          fillStyle: '#C0C0C0',
          lineWidth: 1,
          radius: 2.8,
        },
        active: {
          strokeStyle: 'black',
          fillStyle: 'red',
          lineWidth: 1,
          radius: 2.8,
        },
      },
      logo: null,
      figures: [{
        type: '',
        style: null,
        options: {},
        attrs: {},
        context: {
          strokeStyle: '#333333',
          fillStyle: 'white',
          lineWidth: 1,
        },
      }],
      title: null,
      css: null,
      group: [],
      parent: '',
      father: '',
      children: [],
      onpath: {
        pathid: '', // the id of path
        scale: -1, // the scale on the path
      },
      zoomScale: 1,
      viewmode: null,
      draggable: null,
      resizable: null,
      hoverable: null,
      editable: null,
      removable: null,
      disabled: false,
      scroll: false,
      data: {},
      jsonstrdata: '',
      isanchornode: false,
      anchornodes: [],
    },
    path: {
      margin: 10,
      selected: {
        strokeStyle: 'black',
        lineWidth: 2.2,
      },
      disabledContext: {
        strokeStyle: 'rgba(204, 204, 204, 0.6)',
        lineWidth: 2,
      },
      arrow: {
        angle: 45, // degree
        length: 10, // px length for "---" of "--->"
        headlen: 10, // px length for "/"or"\" of arrow: "/\"
        open: true,
      },
      controls: [],
      controlStyle: {
        strokeStyle: 'black',
        fillStyle: 'yellow',
        lineWidth: 1,
        size: 8,
      },
      source: '',
      sourceAnchor: 'auto',
      sourceStyle: 'none', // arrow style : none, arrow, solidArrow, hollowArrow
      target: '',
      targetAnchor: 'auto',
      targetStyle: 'arrow',
      dashArray: [],
      figures: [{
        type: '',
        context: {
          strokeStyle: '#666666',
          lineWidth: 2,
        },
      }],
      zoomScale: 1,
      textareas: {},
      elbowPoints: [], // specify the elbow points by dragging
      routeList: [], // for orthogonalEdge
      xPlanes: [-1, -1], // record x axis dragend value of orthogonalEdge
      yPlanes: [-1, -1], // record y axis dragend value of orthogonalEdge
      viewmode: null,
      draggable: null,
      resizable: null,
      hoverable: null,
      editable: null,
      removable: null,
      disabled: false,
      data: {},
      jsonstrdata: '',
    },
    anchornode: {
      position: [-1, -1],
      proportionx: true,
      proportiony: true,
    },
  };

  /**
   * Tools Function.
   */
  var Utils = {
    /**
     * Resize Bounds Coefficient
     * [8 1 2]
     * [7 0 3] --> array contains bounding coefficient [c-left,c-top,c-width,c-height],
     * [6 5 4]   while coefficient's value in [0|1|-1].
     */
    RESIZE: {
      CURSORS: ['move', 'n-resize', 'ne-resize', 'e-resize', 'se-resize', 's-resize', 'sw-resize', 'w-resize', 'nw-resize', 'nw-resize'],
      DIRBOUNDS: [
        [1, 1, 0, 0],
        [0, 1, 0, -1],
        [0, 1, 1, -1],
        [0, 0, 1, 0],
        [0, 0, 1, 1],
        [0, 0, 0, 1],
        [1, 0, -1, 1],
        [1, 0, -1, 0],
        [1, 1, -1, -1],
      ],
      DIMENSION: ['left', 'top', 'width', 'height'],
    },
    /**
     * Used for jQuery.fn.degegate handler.
     * Fix jQuery Bug Ticket #9901, @see http:// bugs.jquery.com/ticket/9901
     */
    fixNamespace: function(e) {
      // Compatible with jquery 1.4.4 and jquery 1.8
      e.namespace = e.handleObj.origType.indexOf('.') != -1 ?
        e.handleObj.origType.substring(e.handleObj.origType.lastIndexOf('.') + 1) : e.handleObj.namespace;
      return e;
    },
    /**
     * Just Like jQuery constructor, plus accept id
     */
    $: function(object) {
      if (typeof object == 'string' && object[0] != '#') {
        return $(document.getElementById(object));
      } else {
        return $(object);
      }
    },
    /**
     * Returns true if the given test bound is contained within the boundaries of the owner bound.
     */
    containsBound: function(ownerBound, testBound) {
      return (ownerBound.left <= testBound.left &&
        testBound.left + testBound.width <= ownerBound.left + ownerBound.width &&
        ownerBound.top <= testBound.top &&
        testBound.top + testBound.height <= ownerBound.top + ownerBound.height);
    },
    /**
     * The Function is designed in form of C++ STL slice::slice.
     * Return a subset of an array that consists of a number of elements that are an equal distance apart
     *   and that start at a specified element.
     * eg: slice([0,1,2,3,4,5,6,7,8,9], 0, 4, 2) returns [0,2,4,6]
     * @param array
     * @param start, The array index of the first element in the subset.
     * @param length, The number of elements in the subset.
     * @param stride, The distance between elements in the subset.
     */
    slice: function(array, start, length, stride) {
      var result = [];
      for (var i = start, count = 0; i < array.length && count < length; i += stride, ++count) {
        result.push(array[i]);
      }
      return result;
    },
    distance: function(x1, y1, x2, y2) {
      return Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));
    },
    manhattanDistance: function(x1, y1, x2, y2) {
      return Math.abs(x1 - x2) + Math.abs(y1 - y2);
    },
    /**
     * To judge the browse whether to support canvas.
     */
    isSupportCanvas: function() {
      if (typeof this.supportCanvasResult == 'undefined') {
        this.supportCanvasResult = Boolean(document.createElement('canvas').getContext);
      }
      return this.supportCanvasResult;
    },
    /**
     * To judge the ie browse whether to use excanvas.
     * ie7,8 return true, else return false
     */
    isUseExcanvas: function() {
      return ($.browser.msie && !this.isSupportCanvas()) ? true : false;
    },

    /**
     * Return true if the point(x2,y2) in the straight line between (x0,y0) and (x1,y1).
     */
    isOnPath: function(x0, y0, x1, y1, x2, y2) {
      if (Math.abs(x0 - x1) < 1) {
        return this.distance(x2, y2, (x0 - x1) / (y0 - y1) * (y2 - y0) + x0, y2);
      } else {
        return this.distance(x2, y2, x2, (y0 - y1) / (x0 - x1) * (x2 - x0) + y0) < 1;
      }
    },

    // ----------------------------------------------------------------------------------------
    // [Bezier Utils]
    /**
     * Factorial. Return: n!
     */
    factorial: function(n) {
      return (n <= 1 ? 1 : n * Utils.factorial(n - 1));
    },
    /**
     * [Genearal Bézier curve]
     * @see http:// en.wikipedia.org/wiki/B%C3%A9zier_curve#Terminology
     * @param p, bezier control point [p0,p1,p2,...,pn]. n+1 points for n degree.
     * @param t, Coefficient in [0,1]
     * @return bezier value(x or y according to p's value type) on path in[0,1].
     */
    bezier: function(p, t) {
      var b = 0;
      var f = Utils.factorial;
      for (var i = 0, n = p.length - 1; i <= n; ++i) {
        b += p[i] * Math.pow(t, i) * Math.pow(1 - t, n - i) * f(n) / (f(i) * f(n - i));
      }
      return b;
    },
    /**
     * [Genearal Bézier curve - Return a control value by given a bezier value]
     * @param b, a bezier value on path in [0,1]
     * @param p, bezier control point [p0,p1,p2,...(no pj)...,pn]. n points for n degree excluded p[j]!!
     * @param t, Coefficient in [0,1]
     * @return control value(x or y according to p's value type) in[0,1].
     */
    arcbezier: function(b, p, t, j) {
      var n = p.length - 1;
      var f = Utils.factorial;
      var cj = Math.pow(t, j) * Math.pow(1 - t, n - j) * f(n) / (f(j) * f(n - j));
      return (b - this.bezier(p, t) + cj * p[j]) / (cj || ZERO);
    },
    /**
     * @points, [p0x,p0y, p1x,p1y, p2x,p2y, ..., px,py]
     *  eg: [p0x,p0y, p1x,p1y, p2x,p2y, ......] n =2, at least 2+1 points are needed for quadratic bezier.
     *  eg: [p0x,p0y, p1x,p1y, p2x,p2y, p3x,p3y, ......] n=3, at least 3+1 points are needed for cubic bezier.
     * Note: If given more than the minimum controls points, several bezier curves are patched together.
     * Take Cubic Bezier for Example:
     *   0,1, 2,3, 4,5, 6,7, 8,9, 10,11, 12,13
     *   P0 | P1 | P2 | P3 | P4 | P5  | P6
     *   |___bezier1____|_____bezier2____|
     * @param n, degree. eg: 2 for quadratic and 3 for cubic bezier.
     */
    bezierBends: function(points, n) {
      var bends = [];
      for (var i = 0; i + n * 2 + 1 < points.length; i += (n * 2)) {
        // We take the end or start point at which two curves meet as bends, if the controls are enough to draw many curves.
        i > 0 && bends.push(points[i], points[i + 1]);

        // PX/PY: [P0,P1,P2,Pn], 4(n+1) points's X or Y value array. Give (n-1) 't's, eg: 1/3, 2/3 when n = 3, on curve.
        var PX = Utils.slice(points, i, n + 1, 2);
        var PY = Utils.slice(points, i + 1, n + 1, 2);
        for (var bend = 1; bend < n; ++bend) {
          bends.push(Utils.bezier(PX, bend / n), Utils.bezier(PY, bend / n));
        }
      }
      return bends;
    },

    // ----------------------------------------------------------------------------------------
    // [Serialization Utils]
    capitalize: function(str) { // Return "Hello" for "hello".
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    /**
     * Object to JSON.
     * 1) toJSON(Array, object) return Array.
     * 2) toJSON(object) return JSON String.
     */
    toJSON: function(object) {
      // Function overload: Both 1 and 2 arguments can work.
      var sb = arguments[0];
      var value = arguments[1];
      if (arguments.length == 1) {
        return Utils.toJSON([], object).join('');
      }

      // Simple Type.
      switch (typeof value) {
        case 'string':
          if (sb.length >= 3 && sb[sb.length - 3] == 'style') { // To make sure all figures could contain '"'
            // e.g "{\"left\":0.05,\"top\":...h\":0.9,\"height\":0.9}"
            value = value.replace(/\"/g, '\\"');
          }

          sb.push('"', value, '"');
          return sb;
        case 'number':
        case 'boolean':
          sb.push(value);
          return sb;
      }

      // Null Value
      if (value == null) {
        sb.push(String(value));
        return sb;
      }

      // Complex Type.
      var length = 0;
      if ($.isPlainObject(value) && !$.isEmptyObject(value)) {
        length = sb.push('{');
        for (var key in value) {
          if (value[key] != null) {
            if ($.isPlainObject(value[key]) && $.isEmptyObject(value[key])) {
              continue;
            }

            sb.push('"', key, '"', ':');
            Utils.toJSON(sb, value[key]);
            sb.push(',');
          }
        }
        (sb.length > length) && sb.pop();
        sb.push('}');
      } else if ($.isArray(value)) {
        length = sb.push('[');
        for (var i = 0; i < value.length; ++i) {
          Utils.toJSON(sb, value[i]);
          sb.push(',');
        }
        (sb.length > length) && sb.pop();
        sb.push(']');
      }
      return sb;
    },
    equals: function(object1, object2) {
      if ($.isArray(object1)) { // Check Array.
        if (!$.isArray(object2) || object1.length != object2.length) { // Check array dimension.
          return false;
        } else { // Check array item.
          for (var i = 0; i < object1.length; ++i) {
            if (!Utils.equals(object1[i], object2[i])) {
              return false;
            }
          }
          return true;
        }
      } else if ($.isPlainObject(object1)) { // Check Plain Object
        var length1 = 0;
        var length2 = 0;
        for (var name in object1) { // Iterate object1 comparing to object2
          ++length1;
          if (!Utils.equals(object1[name], object2[name])) {
            return false;
          }
        }
        for (var name in object2) { // All items of object1 equals to object2, so iterate object2 to check more.
          if (++length2 > length1) {
            return false;
          }
        }
        return true;
      } else { // Check other types.
        return object1 === object2;
      }
    },
    /**
     * Return the changed parts of target JSON object compared to source JSON object.
     * eg: getChanged({a:1,b:2,c:4}}, {a:1,c:3}) returns {b:2,c:4}
     */
    getChanged: function(target, source) {
      var result = {};
      for (var name in target) {
        var newValue = target[name];
        var oldValue = source && source[name];
        if ($.isPlainObject(newValue)) { // Sub-object access
          var subResult = Utils.getChanged(newValue, oldValue);
          if (!$.isEmptyObject(subResult)) {
            result[name] = subResult;
          }
        } else if (!Utils.equals(newValue, oldValue)) { // Only copy changed values.
          result[name] = newValue;
        }
      }
      return result;
    },
    getColorChanged: function(oldColor, redDif, greenDif, blueDif) {
      var red = 0;
      var green = 0;
      var blue = 0;
      var alpha = 1;
      if (oldColor.indexOf('#') != -1) { // eg:#ffffff color
        oldColor = Utils.hex2rgb(oldColor);
      }
      if (oldColor.indexOf('rgba(') != -1) { // rgba color
        var color = oldColor.slice(5, oldColor.length - 1).split(',');
        red = parseInt(color[0], 10);
        green = parseInt(color[1], 10);
        blue = parseInt(color[2], 10);
        alpha = parseInt(color[3], 10);
      } else if (oldColor.indexOf('rgb(') != -1) { // rgb color
        var color = oldColor.slice(4, oldColor.length - 1).split(',');
        red = parseInt(color[0], 10);
        green = parseInt(color[1], 10);
        blue = parseInt(color[2], 10);
      }
      red = (red + redDif) > 255 ? 255 : red + redDif;
      green = (green + greenDif) > 255 ? 255 : green + greenDif;
      blue = (blue + blueDif) > 255 ? 255 : blue + blueDif;
      return 'rgba(' + red + ',' + green + ',' + blue + ',' + alpha + ')';
    },
    /** convert a hex color string to rgb string.
     * h - 3 or 6 character hex string, with or without leading #
     */
    hex2rgb: function(h) {
      h = h.replace('#', '');
      if (h.length == 3) {
        h = h.charAt(0) + h.charAt(0) + h.charAt(1) + h.charAt(1) + h.charAt(2) + h.charAt(2);
      }
      var rgb;
      rgb = 'rgb(' + parseInt(h.slice(0, 2), 16) + ', ' + parseInt(h.slice(2, 4), 16) + ', ' + parseInt(h.slice(4, 6), 16);
      rgb += ')';
      return rgb;
    },
    /**
     * When we dblclick path, we should generate textnode's options before create textnode.
     */
    generateTextOptions: function(pathId, closestPoint, title) {
      var options = {};
      var width = 40;
      var height = 30;
      var left = closestPoint[0] - width / 2;
      var top = closestPoint[1] - height / 2;
      options.figures = [];
      options.figures[0] = {
        style: ['left:', left, 'px;top:', top, 'px;width:' + width + 'px;height:' + height + 'px;'].join(''),
      };
      options.title = title;
      options.onpath = {
        pathid: pathId,
        scale: closestPoint[2],
      };
      return options;
    },
    /**
     * Search one element's position.
     */
    indexOf: function(array, value) {
      for (var i = 0; i < array.length; i++) {
        if (array[i] == value)
        {return i;}
      }
      return -1;
    },
    /**
     * Turn style string to style json.
     */
    getStyle: function(style) {
      if (!style || $.trim(style) == '') {
        return {};
      }

      var styleJson = {};
      var result = {};
      var styleArray = style.split(';');
      for (var i = 0; i < styleArray.length; i++) {
        var styleItem = styleArray[i].split(':');
        var key = $.trim(styleItem[0]).toLowerCase();
        var value = $.trim(styleItem[1]);

        value = parseInt(value.slice(0, value.length - 2), 10);
        isNaN(value) && (value = 0);
        styleJson[key] = value;
      }

      result['left'] = styleJson['left'];
      result['top'] = styleJson['top'];
      result['width'] = styleJson['width'];
      result['height'] = styleJson['height'];

      return result;
    },
    /**
     * Return all selected nodes' bounds by min left, min top, max right, max bottom
     * according to given selections.
     * @param selections : given nodes
     * @returns ensembleBound : whole bounds(left,top,right,bottom)
     */
    getEnsembleBounds: function(selections) {
      var ensembleBound = {}; // four bounds of together nodes
      for (var sid in selections) {
        var sb = selections[sid].getBounds();
        ensembleBound = {
          left: sb.left,
          top: sb.top,
          right: sb.left + sb.width,
          bottom: sb.top + sb.height,
        };
        break;
      }

      for (var nid in selections) {
        var nb = selections[nid].getBounds();
        ensembleBound.left = Math.min(nb.left, ensembleBound.left);
        ensembleBound.top = Math.min(nb.top, ensembleBound.top);
        ensembleBound.right = Math.max(nb.left + nb.width, ensembleBound.right);
        ensembleBound.bottom = Math.max(nb.top + nb.height, ensembleBound.bottom);
      }

      return ensembleBound;
    },
    getJsonFromString: function(str) {
      var json = {};
      var array = str.split(',');
      for (var i = 0; i < array.length; i++) {
        var item = array[i].split(':');
        json[$.trim(item[0])] = $.trim(item[1]);
      }
      return json;
    },
    parseDiagramOptions: function(options) {
      if (options) {
        if (options.indexOf('{') == 0) {
          options = eval('(' + options + ')');
        } else {
          options = eval('({' + options + '})');
        }
      }
      return options;
    },
    /**
     * Whether start <= value <= end.
     */
    belongTo: function(value, start, end) {
      if ((start <= value && value <= end) || (end <= value && value <= start)) {
        return true;
      } else {
        return false;
      }
    },
    /**
     * Keep n decimal places.
     */
    toFixed: function(value, digit) {
      var shiftNum = Math.pow(10, digit);
      return Math.round(value * shiftNum) / shiftNum;
    },
    /**
     * Get the width and height of text.
     * Create a hidden span to get width and height of text content.
     * @param content : text content
     * @returns {width, height}
     */
    getWidthHeight: function(content) {
      var span = document.createElement('span');
      var result = {};

      span.style.visibility = 'hidden';
      $(span).css('white-space', 'pre');
      document.body.appendChild(span);

      result.width = span.offsetWidth;
      result.height = span.offsetWidth;

      if (typeof span.textContent != 'undefined') {
        span.textContent = content;
      } else {
        span.innerText = content;
      }

      result.width = span.offsetWidth - result.width;
      result.height = span.offsetHeight - result.height;

      $(span).remove();

      return result;
    },
    /**
     * Get the intersection of two segements.
     * @param a : point
     * @param b : point
     * @param c : point
     * @param d : point
     * @returns intersection
     */
    getSegmentsIntersection: function(a, b, c, d) {
      // 1.Get the Solution of linear equations, and get the intersection point of line segments.
      // If the denominator is zero, parallel and collinear disjoint
      var denominator = (b.y - a.y) * (d.x - c.x) - (a.x - b.x) * (c.y - d.y);
      if (denominator == 0) {
        return false;
      }

      // The coordinate of intersection point on the srtaight line : (x , y).
      var x = ((b.x - a.x) * (d.x - c.x) * (c.y - a.y) +
        (b.y - a.y) * (d.x - c.x) * a.x -
        (d.y - c.y) * (b.x - a.x) * c.x) / denominator;
      var y = -((b.y - a.y) * (d.y - c.y) * (c.x - a.x) +
        (b.x - a.x) * (d.y - c.y) * a.y -
        (d.x - c.x) * (b.y - a.y) * c.y) / denominator;

      // 2 Determine whether the intersection point on the two line segments.
      if (
        // Focus on line 1
        (x - a.x) * (x - b.x) <= 0 && (y - a.y) * (y - b.y) <= 0
        // and focus on line 2
        &&
        (x - c.x) * (x - d.x) <= 0 && (y - c.y) * (y - d.y) <= 0
      ) {
        // return intersection
        return {
          x: x,
          y: y,
        };
      }

      // Otherwise the disjoint
      return false;
    },
    quickSort: function(arr, key) {
      if (arr.length <= 1) {
        return arr;
      }

      var pivotIndex = Math.floor(arr.length / 2);
      var pivot = arr.splice(pivotIndex, 1)[0];
      var left = [];
      var right = [];

      for (var i = 0; i < arr.length; i++) {
        if (arr[i][key] < pivot[key]) {
          left.push(arr[i]);
        } else {
          right.push(arr[i]);
        }
      }

      return Utils.quickSort(left, key).concat([pivot], Utils.quickSort(right, key));
    },
    /**
     * Data rounding
     * Something like Math.round, but add a step as base number.
     */
    round: function(value, step) {
      if ((Math.abs(step) - 0) < ZERO) {
        return value;
      }
      return Math.round(value / step) * step;
    },
    /**
     * Title filter for special character
     * The default filter is for DOM elements
     * eg: <script>, <div>, <pre> ...
     */
    specialListFilter: function(value) {
      var resultStart = value.replace(/<(\w+)>/g, function(match, originalText, pos) {
        return '&lt;' + originalText + '&gt;';
      });

      var result = resultStart.replace(/<\/(\w+)>/g, function(match, originalText, pos) {
        return '&lt;/' + originalText + '&gt;';
      });

      return result;
    },
    /**
     * For old version json compatibility
     * @param opts : json option
     */
    _getType: function(opts) {
      return opts.figures && opts.figures[0] && opts.figures[0].type || opts.type;
    },
    /**
     * For old version json compatibility
     * @param opts : json option
     */
    _getBounds: function(opts) {
      return opts.figures && opts.figures[0] && opts.figures[0].style || opts.bounds;
    },
    msie11: function() {
      return Boolean(navigator.userAgent.match(/Trident.*rv[ :]*11\./));
    },
  };
  window.Utils = Utils;

  /**
   * Diagram Class Define.
   */
  jBME.Diagram.prototype = {
    maxState: 10,

    init: function(id, options) {
      this.id = id;

      // DOM Cache
      this.$box = $(document.getElementById(this.id));
      scp = this.$box.scope();
      this.$client = this.$box.children('.uee-diagram-client');
      this.$marquee = this.$box.children('.uee-diagram-marquee');
      this.$data = this.$box.children('input');

      // Node and path html template
      this.nodeHtml = options.nodeHtml;
      this.pathHtml = options.pathHtml;

      // Global Tools
      this.$doc = $(document);

      // Zoom params, default zoom value is 1 and its index = 9
      this.zoomargs = 1;
      this.zoomIndex = 9;
      this.zoomList = [0.01, 0.02, 0.04, 0.08, 0.16, 0.25, 0.33, 0.5, 0.75, 1, 1.25, 1.5, 2, 3, 4];

      // Default value: width, height, fill, shadow
      this.defaultWidth = 40;
      this.defaultHeight = 40;
      this.pasteOffset = 30;
      this.defaultConnectionText = 'text';

      // Default scope of showing nodes
      this.showScopeX = 200;
      this.showScopeY = 200;
      this.clientWidth = 1200;
      this.clientHeight = 500;
      this.DOMCache = null;
      this.maxBounds = {};

      // Whether path can connect itself.
      this.selfConnection = false;

      // Default value: resize, draw line from anchor, reconnect scope.
      this.resizeScope = 4;
      this.drawLineScope = 8;
      this.reconnectScope = 16;

      this.spinStep = 1;
      this.createStep = 1;
      this.dragStep = 1;
      this.resizeStep = 1;

      // Object cache.
      this.nodes = {};
      this.paths = {};
      this.containers = {};

      this.pathsdelay = {};
      this.textnodesdelay = {};
      this.subnodesdelay = {};

      // Drag to create node
      this.registeredOptions = null;
      this.registeredNode = null;

      // The way of creating node, default is click to request node;
      // also can be "drag" and "all".
      this.createNodeMode = 'click';

      // The way of creating path, default is click and it can be drag
      this.createPathMode = 'click';

      // Make a difference between dragging create and click create
      this.draggingCreate = false;

      this.vcenters = {};
      this.hcenters = {};

      // Rightclick menu
      this.menus = options.menus;
      this.onrgtclkElement = {};

      this.selections = {};
      this.selectionSize = 0;
      this.jSelection = null;

      // Copy and paste operation
      this.clipboard = null;
      this.pasteTimes = 0;

      // Working Status.
      this.resizedir = 0;
      this.dragElement = false;
      this.jHoverElement = null;
      this.jReconnectPath = null;
      this.jReconnectEnd = null;
      this.createTarget = null;
      this.create = null;
      this.connecting = false;
      this.request = new jBME.Request(this);

      // default value : Event swich, default width,height, fill, shadow
      this.setDefaultValue(options);
      this.runtime = options.runtime;

      if ($.browser.msie && $.browser.version == 8) {
        this.$client.addClass('ie8');
      }

      this.custom();
    },
    /**
     * Attach an exist node to the diagram.
     */
    attachNode: function(id, options, $nodebox, source /* optional*/) {
      // Get the type of node, default value is "Default".
      var type = Utils.capitalize(Utils._getType(options) || 'Default');

      // Initialize the instance of node.
      this.nodes[id] = this.textnodesdelay[id] || ((jBME[type]) ?
        new jBME[type](id, options, $nodebox, this.zoomargs) :
        new jBME.Node(id, options, $nodebox, this.zoomargs));

      this.containerManager(id, options, source /* optional*/);

      if (options.onpath && options.onpath.pathid && options.onpath.scale >= 0 &&
        source != 'paste') { // Initialize textnode
        if (this.paths[options.onpath.pathid]) { // OK, the path is loaded, show textnodes now.
          this.paths[options.onpath.pathid].setTextOnPath(this.nodes[id]);
          this.nodes[id].$box.css('display', '');
          this.paths[options.onpath.pathid].updateTextNodeBounds();
          delete this.textnodesdelay[id];
        } else {
          this.textnodesdelay[id] = this.nodes[id];
          this.nodes[id].$box.css('display', 'none');
          return this.nodes[id];
        }
      }

      for (var pathid in this.pathsdelay) {
        this.attachPath(pathid, this.pathsdelay[pathid].options);
      }

      var nodeBounds = this.nodes[id].getBounds();
      this.addCenterPoint(this.hcenters, nodeBounds.left + nodeBounds.width / 2);
      this.addCenterPoint(this.vcenters, nodeBounds.top + nodeBounds.height / 2);

      return this.nodes[id];
    },
    /**
     * Attach an exist anchornode to the father.
     */
    attachAnchorNode: function(id, options, $nodebox, source /* optional*/) {
      // Get the type of node, default value is "Default".
      var type = Utils.capitalize(Utils._getType(options) || 'Default');

      // Initialize the instance of node.
      var anchornode = this.textnodesdelay[id] || ((jBME[type]) ?
        new jBME[type](id, options, $nodebox, this.zoomargs) :
        new jBME.Node(id, options, $nodebox, this.zoomargs));

      for (var pathid in this.pathsdelay) {
        this.attachPath(pathid, this.pathsdelay[pathid].options);
      }

      var nodeBounds = anchornode.getBounds();
      this.addCenterPoint(this.hcenters, nodeBounds.left + nodeBounds.width / 2);
      this.addCenterPoint(this.vcenters, nodeBounds.top + nodeBounds.height / 2);

      return anchornode;
    },
    /**
     * Attach an exist path to the diagram.
     */
    attachPath: function(id, options /* [optional only when delay attach]*/, $pathbox) {
      var type = Utils.capitalize(Utils._getType(options) || 'Line');

      var jPath = this.pathsdelay[id] || ((jBME[type]) ?
        new jBME[type](id, options, $pathbox, this.zoomargs) :
        new jBME.Path(id, options, $pathbox, this.zoomargs));

      var jSource = this.nodes[jPath.options.source] || jPath.jSource;
      jTarget = this.nodes[jPath.options.target] || jPath.jTarget;

      if (jSource && jTarget) { // OK, the nodes are loaded, attach the path now.
        jPath.setSource(jSource).setTarget(jTarget, null, options.routeList);

        this.paths[id] = jPath;

        delete this.pathsdelay[id];

        jPath.$box.css('display', '');

        // If this path have textnodes, create it.
        for (var textnodeid in this.textnodesdelay) {
          if (this.textnodesdelay[textnodeid].options.onpath.pathid == id) {
            this.attachNode(textnodeid, this.textnodesdelay[textnodeid].options);
          }
        }
      } else { // The nodes are not loaded yet, delay attaching.
        this.pathsdelay[id] = jPath;
        jPath.$box.css('display', 'none');
      }

      return jPath;
    },
    /**
     * Create Node
     * @param options : node's json
     * @param e    : create node by mouse down event for get node position
     * @param source : create node operation
     */
    createNode: function(options, e /* optional*/, source /* optional*/, clientBounds) {
      if (options.metatype && jBME.Diagram.MetaData[options.metatype]) {
        options = $.extend(true, {}, jBME.Diagram.MetaData[options.metatype], options);
      }
      var id = (options.id || this.generateId('N'));
      var $nodebox = null;
      var style = Utils._getBounds(options);
      var type = Utils._getType(options);
      var isShow = false;

      // If zoomargs exist, we use this value, else use options.zoomScale || 1.
      this.zoomargs = (this.zoomargs != 1 ? this.zoomargs : options.zoomScale) || 1;

      var styleObj = this.generateStyle(style, e, this.zoomargs);
      var DOMStyle = ['left:', styleObj.left, 'px;top:', styleObj.top, 'px;width:',
        styleObj.width, 'px;height:', styleObj.height, 'px;',
      ].join('');

      // Fill the shortage of properties.
      var newOpts = this.setNodeOptions(options, DOMStyle);
      var encodeTitle = $('<div></div>').text(newOpts.title || '').html();

      // start diff with BME3.0 , html is stored in templateCache
      $box = jQuery(this.nodeHtml);
      $box.attr({
        id: id,
        style: DOMStyle + (newOpts.css || ''),
      });
      $box.addClass(type || '');
      $box.find('.uee-node-text').html(encodeTitle || '');
      // end diff

      if (!clientBounds || (styleObj.left <= clientBounds.width + this.showScopeX &&
        styleObj.top <= clientBounds.height + this.showScopeY)) {
        if (this.ofragment) { // Create node from update method together.
          this.ofragment.appendChild($box[0]);
          $nodebox = $box;
        } else { // Just create one node.
          this.$client.append($box);
        }
        isShow = true;
      } else { // Init node which is not in showing scope.
        this.DOMCache.appendChild($box[0]);
        $nodebox = $box;
      }

      var jNode = this.attachNode(id, newOpts, $nodebox, source /* optional*/);
      jNode.isShow = isShow;

      if (e) {
        this.updateMaxBoundsByCreate(id, jNode.getBounds());
      }

      if (options.anchornodes) {
        for (var i = 0; i < options.anchornodes.length; i++) {
          this.createAnchorNode(options.anchornodes[i], null, null, clientBounds);
        }
      }

      return jNode;
    },
    /**
     * Create Node
     * @param options : node's json
     * @param source : create node operation
     */
    createAnchorNode: function(opts, e /* optional*/, source /* optional*/, clientBounds) {
      var fathernode = this.nodes[opts.father];
      if (opts.metatype && jBME.Diagram.MetaData[opts.metatype]) {
        options = $.extend(true, {}, jBME.Diagram.MetaData[opts.metatype]);
        options.father = opts.father;
      }
      var id = (opts.id || this.generateId('N'));
      opts.id = id;
      var $nodebox = null;
      var style = Utils._getBounds(options);
      var type = Utils._getType(options);
      var isShow = false;

      // If zoomargs exist, we use this value, else use options.zoomScale || 1.
      this.zoomargs = (this.zoomargs != 1 ? this.zoomargs : options.zoomScale) || 1;

      var fatherbounds = fathernode.getBounds();
      var pos = {
        left: opts.proportionx ? (fatherbounds.width * opts.position[0]) : opts.position[0],
        top: opts.proportiony ? (fatherbounds.height * opts.position[1]) : opts.position[1],
      };
      var styleObj = this.generateStyle(style, e, this.zoomargs);
      var DOMStyle = ['left:', pos.left, 'px;top:', pos.top, 'px;width:',
        styleObj.width, 'px;height:', styleObj.height, 'px;',
      ].join('');

      // Fill the shortage of properties.
      var newOpts = this.setNodeOptions(options, DOMStyle);
      var encodeTitle = $('<div></div>').text(newOpts.title || '').html();

      // start diff with BME3.0 , html is stored in templateCache
      $box = jQuery(this.nodeHtml);
      $box.attr({
        id: id,
        style: DOMStyle + (newOpts.css || ''),
      });
      $box.addClass(type || '');
      $box.find('.uee-node-text').html(encodeTitle || '');
      // end diff

      if (!clientBounds || (styleObj.left <= clientBounds.width + this.showScopeX &&
        styleObj.top <= clientBounds.height + this.showScopeY)) {
        if (fathernode) {
          fathernode.$box.append($box);
          $nodebox = $box;
        }
        isShow = true;
      } else { // Init node which is not in showing scope.
        this.DOMCache.appendChild($box[0]);
        $nodebox = $box;
      }

      var jNode = this.attachAnchorNode(id, newOpts, $nodebox, source /* optional*/);
      jNode.isShow = isShow;
      jNode.$box.addClass('uee-anchornode');

      if (fathernode) {
        for (var i = 0; i < fathernode.options.anchornodes.length; i++) {
          if (fathernode.options.anchornodes[i].id == opts.id) {
            fathernode.options.anchornodes[i] = opts;
            break;
          } else {
            var boundsnow = fathernode.anchornodes[fathernode.options.anchornodes[i].id].instance.bounds;
            if (boundsnow.top > jNode.bounds.top) {
              fathernode.options.anchornodes.splice(i, 0, opts);
              break;
            }
          }
        }
        if (i == fathernode.options.anchornodes.length) {
          fathernode.options.anchornodes.push(opts);
        }

        if (!fathernode.anchornodes) {
          fathernode.anchornodes = {};
        }
        fathernode.anchornodes[id] = {};
        fathernode.anchornodes[id].instance = jNode;
        fathernode.anchornodes[id].position = opts.position;
        fathernode.anchornodes[id].proportionx = opts.proportionx;
        fathernode.anchornodes[id].proportiony = opts.proportiony;
        fathernode.anchornodes[id].data = opts.data;
      }

      if (e) {
        this.updateMaxBoundsByCreate(id, jNode.getBounds());
      }

      return jNode;
    },
    /**
     * Create Path
     * @param options : path's json
     * @param e    : create path by mouse down event for get node position
     */
    createPath: function(options, e /* optional*/, clientBounds) {
      // this.triggerBeforeCreatePath(options, event, source)
      if (options.metatype && jBME.Diagram.MetaData[options.metatype]) {
        options = $.extend(true, {}, jBME.Diagram.MetaData[options.metatype], options);
      }
      var id = (options.id || this.generateId('P'));
      var $pathbox = null;
      var isShow = false;

      // start diff with BME3.0 , html is stored in templateCache
      var $box = jQuery(this.pathHtml);
      $box.attr('id', id);
      // end diff

      var sourcenode = this.nodes[options.source];
      var targetnode = this.nodes[options.target];

      var sourceBounds = sourcenode && sourcenode.getBounds();
      var targetBounds = targetnode && targetnode.getBounds();

      if (!clientBounds || !sourceBounds || !targetBounds ||
        // source node is in showing scope.
        (sourceBounds.left <= clientBounds.width + this.showScopeX &&
          sourceBounds.top <= clientBounds.height + this.showScopeY) ||
        // target node is in showing scope.
        (targetBounds.left <= clientBounds.width + this.showScopeX &&
          targetBounds.top <= clientBounds.height + this.showScopeY)) {
        if (this.ofragment) {
          this.ofragment.appendChild($box[0]);
          $pathbox = $box;
        } else {
          this.$client.append($box);
        }

        isShow = true;
      } else { // Init node which is not in showing scope.
        this.DOMCache.appendChild($box[0]);
        $pathbox = $box;
      }

      var jPath = this.attachPath(id, options, $pathbox);
      jPath.isShow = isShow;

      return jPath;
    },
    /**
     * provide a interface to create the node's subnode
     */
    createSubNode: function(fatherNodeId, subNodeOpts, groupid, e, pathoptions) {
      if (!this.nodes[fatherNodeId]) {
        return null;
      }
      var subNode = this.createNodeOuter(subNodeOpts);
      var fathernode = this.nodes[fatherNodeId];
      var fatherbounds = fathernode.getBounds();
      var sourceDom = e.$Target.closest('.uee-diagram-ele.uee-node');
      var datagrid = e.$Target.closest('div.uee-datagrid');
      var trDom = e.$Target.closest('tr');
      var anchornode = {
        position: [1, (datagrid.position().top + trDom.position().top)],
        proportionx: true,
        proportiony: false,
        father: fatherNodeId,
        data: {},
        id: groupid + '_anchornode',
        metatype: 'defaultexpand',
      };
      var nodetmp;

      if (!fathernode.anchornodes || !fathernode.anchornodes[groupid + '_anchornode']) {
        nodetmp = this.createAnchorNodeOuter(anchornode);
      } else if (fathernode.anchornodes && fathernode.anchornodes[groupid + '_anchornode']) {
        for (var i = 0; i < fathernode.options.anchornodes.length; i++) {
          if (fathernode.options.anchornodes[i].id == (groupid + '_anchornode')) {
            fathernode.options.anchornodes[i].position = anchornode.position;
            fathernode.options.anchornodes[i].proportionx = anchornode.proportionx;
            fathernode.options.anchornodes[i].proportiony = anchornode.proportiony;
            break;
          }
        }
        nodetmp = fathernode.anchornodes[anchornode.id].instance;
        nodetmp.updateBounds({
          left: (fatherbounds.width),
          top: (datagrid.position().top + trDom.position().top),
        });
      }

      if (pathoptions) {
        this.createPathOuter(pathoptions, null, e);
      } else {
        var tmpPathOpts = {
          source: fatherNodeId,
          sourceAnchor: groupid + '_anchornode',
          target: subNode.id,
          targetStyle: 'DataPane',
          targetAnchor: 7,
          draggable: false,
          hoverable: false,
          figures: [{
            type: 'elbow',
            context: {
              strokeStyle: '#60D91A',
              lineWidth: 1,
            },
          }],
          group: {
            id: groupid,
            source: fatherNodeId,
          },

        };
        this.createPathOuter(tmpPathOpts, null, e);
      }

      return subNode;
    },
    /**
     * Generate node DOM style by
     * ( figure style + default value || event position ) * zoom scale
     * @param style   : figure style
     * @param event   : mouse event which create node
     * @param zoomScale : zoom value
     * @return result : object {} DOM bounds.
     */
    generateStyle: function(style, event, zoomScale) {
      // Create a node when zoomargs != 1
      var bounds = Utils.getStyle(style);
      var ebounds = {
        left: 0,
        top: 0,
      };

      // Create from palette by user.
      if (event) {
        ebounds = this.getPositionWithScroll(event);
        ebounds.left = Utils.round(ebounds.left, this.createStep);
        ebounds.top = Utils.round(ebounds.top, this.createStep);
      }

      // No need to zoom left and top when create a new node by mouse position.
      var result = {
        left: (bounds.left || ebounds.left || ZERO) * (event ? 1 : zoomScale),
        top: (bounds.top || ebounds.top || ZERO) * (event ? 1 : zoomScale),
        width: (bounds.width || this.defaultWidth) * zoomScale,
        height: (bounds.height || this.defaultHeight) * zoomScale,
      };

      return result;
    },
    /**
     * Set new options if user options is lack of some attributes
     * attributes (left || top || width || height)
     */
    setNodeOptions: function(options, standardStyle) {
      var opts = $.extend(true, {}, options);

      if (opts && !opts.figures) {
        opts.figures = [];
      }

      if (!opts.figures[0]) {
        opts.figures[0] = {};
      }

      opts.figures[0].style = standardStyle;

      return opts;
    },
    /**
     * RequestNode,Undo rendo, paste, Right click container to create node or create textnode by double click path.
     * Except creating node from DO, we use this createNodeOuter method, because we can manager nodecreate event.
     */
    createNodeOuter: function(options, source, event) {
      if (source != 'undoredo' && source != 'inner' && this.triggerBeforeCreateNode(options, event, source)) {
        return;
      }

      var newNode = this.createNode(options, event, source);

      // Customize event trigger : after node is created
      source != 'inner' && source != 'paste' && this.triggerElementOperation(newNode, 'nodecreate', source, event);
      this.afterCreate();
      source != 'undoredo' && source != 'paste' && source != 'inner' && this.syncJson();

      // 原子添加认证图标
      addIcon(newNode);

      return newNode;
    },
    /**
     * RequestNode,Undo rendo, paste, Right click container to create node or create textnode by double click path.
     * Except creating node from DO, we use this createNodeOuter method, because we can manager nodecreate event.
     */
    createAnchorNodeOuter: function(options, source, event) {
      if (source != 'undoredo' && source != 'inner' && this.triggerBeforeCreateNode(options, event, source)) {
        return;
      }

      var newNode = this.createAnchorNode(options, event, source);

      // Customize event trigger : after node is created
      var parent = {};
      parent[newNode.options.father] = this.nodes[newNode.options.father];
      source != 'inner' && source != 'paste' && this.triggerElementOperation(parent, 'nodeschange', source, event);
      source != 'undoredo' && source != 'paste' && source != 'inner' && this.syncJson();

      return newNode;
    },
    /**
     * Undoredo to create node or create textnode by double click path.
     * Except creating node from DO, we use this createPathOuter method, because we can manager nodecreate event.
     */
    createPathOuter: function(options, source, event) {
      var newPath = this.createPath(options, event);

      if (newPath.jSource && newPath.jTarget) { // Customize event trigger : after path is created
        source != 'inner' && source != 'paste' && this.triggerElementOperation(newPath, 'pathcreate', source);
        (source != 'undoredo' && source != 'paste' && source != 'inner') && this.syncJson();
      }

      return newPath;
    },
    /**
     * Clean the js variable to prevent from memory leaking.
     */
    destroy: function() {
      this.editor.unregisterDelegateEvents();

      for (var pid in this.paths) {
        this.freeCanvas(this.paths[pid].canvas);
        delete this.paths[pid].jSource.paths[pid];
        delete this.paths[pid].jTarget.paths[pid];
        this.paths[pid].jSource = null;
        this.paths[pid].jTarget = null;
        delete this.paths[pid].jSource;
        delete this.paths[pid].jTarget;

        this.paths[pid] = null;
        delete this.paths[pid];
      }
      this.paths = [];

      for (var nid in this.nodes) {
        // destroy anchornodes
        for (var aid in this.nodes[nid].anchornodes) {
          var anchornode = this.nodes[nid].anchornodes[aid];
          destroyAnchorNode(anchornode.instance);
        }

        if (this.nodes[nid].options.figures[0].type == 'image') {
          // Image node cannot be released. Need unbind load event.
          var srcKey = this.nodes[nid].options.figures[0].options.src;
          $(this.nodes[nid][srcKey]).unbind('load');
          this.nodes[nid][srcKey] = null;
          delete this.nodes[nid][srcKey];
        }

        if (this.nodes[nid].logoImg) {
          $(this.nodes[nid].logoImg).unbind('load');
          this.nodes[nid].logoImg = null;
        }

        this.nodes[nid].options.figures[0].style = '';
        this.freeCanvas(this.nodes[nid].canvas);
        this.nodes[nid].logoImg = null;
        this.nodes[nid].paths = {};

        this.nodes[nid] = null;
        delete this.nodes[nid];
      }
      this.nodes = [];

      this.$box[0].innerHTML = '';

      this.editor.diagram = null;
      this.editor.pluginInstances = [];
      this.editor = null;

      jBME.Editor.prototype.pluginInstances = [];
      this.DOMCache = null;
    },
    /**
     * destroy anchornode
     */
    destroyAnchorNode: function(anchornode) {
      if (anchornode.options.figures[0].type == 'image') {
        // Image node cannot be released. Need unbind load event.
        var srcKey = anchornode.options.figures[0].options.src;
        $(anchornode[srcKey]).unbind('load');
        anchornode[srcKey] = null;
        delete anchornode[srcKey];
      }

      if (anchornode.logoImg) {
        $(anchornode.logoImg).unbind('load');
        anchornode.logoImg = null;
      }

      anchornode.options.figures[0].style = '';
      this.freeCanvas(anchornode.canvas);
      anchornode.logoImg = null;
      anchornode.paths = {};

      anchornode = null;
      delete anchornode;
    },
    freeCanvas: function(canvas) {
      canvas.getContext('2d').clearRect(0, 0, canvas.width, canvas.height);
      $(canvas).unbind().removeAttr('class').removeAttr('style');
      // Style attributes seemed to be still hanging around. wierd. Some ticks
      // Still retained a left: 0px attribute after reusing a canvas.
      $(canvas).css({
        left: '',
        top: '',
        position: '',
      });
      // Setting size to 0 may save memory of unused canvases?
      canvas.width = 0;
      canvas.height = 0;
      if (Utils.isUseExcanvas()) {
        // Excanvas can't be reused, but properly unset
        window.G_vmlCanvasManager.uninitElement(canvas);
      }
    },
    /**
     * Deal with the relationship of new node with created node.
     * When new node is in container,they are father and son.
     * If this is swimlane, do some special method.
     * @param id
     * @param options
     */
    containerManager: function(id, options, source /* optional*/) {
      this.nodes[id].iscontainer && (this.containers[id] = this.nodes[id]);

      if (this.nodes[id].isswimlane) { // Collect swimlanes and change its offset.
        this.nodes[id].addSwimLane(options, source);

        // Whether some nodes have been in the place of container.
        if (source != 'undoredo') {
          var subnodes = this.swallowBounds(this.nodes[id]);
          for (var sid in subnodes) {
            this.joinFamily(subnodes[sid], this.nodes[id]);
          }
        }

        // for not ignore usl create node first and usl is without parent attr
        for (var i = 0; i < this.nodes[id].options.children.length; i++) {
          if (!this.nodes[id].children[this.nodes[id].options.children[i]]) {
            this.joinFamily(this.nodes[this.nodes[id].options.children[i]], this.nodes[id]);
          }
        }
      }

      // If we put node in container,set its relationship.
      if (source == 'nodecreate' || source == 'paste') {
        var container = this.isContained(this.nodes[id]);
        this.joinFamily(this.nodes[id], container);
      }

      var parentid = this.nodes[id].options.parent;
      if (parentid && $.trim(parentid) != '' && source != 'nodecreate') { // This is subnode
        if (!this.nodes[parentid]) {
          !this.subnodesdelay[parentid] && (this.subnodesdelay[parentid] = []);
          this.subnodesdelay[parentid].push(this.nodes[id]);
        } else {
          this.joinFamily(this.nodes[id], this.nodes[parentid]);
        }
      }

      if (this.subnodesdelay[id]) { // This is container
        var subnodes = this.subnodesdelay[id];
        for (var s = 0; s < subnodes.length; s++) {
          subnodes[s].addParent(this.nodes[id]);
        }
        this.nodes[id].addChildren(subnodes);
        delete this.subnodesdelay[id];
      }
    },
    /**
     * Manage overall authority, container's authority and single node's authority.
     * @param jElement : this element
     * @param operateAttr : the property of this element, it can be draggable,hoverable,resizable,editable,removable,viewmode
     * @returns authority ：the result of this node's authority
     */
    authManager: function(jElement, operateAttr) {
      var authority = null;
      var jDiagram = this;

      getAuthority(jElement, operateAttr);
      if (authority === null) {
        // This element's attribute of authority, draggable || hoverable || resizable...
        var attrValueOverall = this[operateAttr];

        // Get authority from properties(draggable,hoverable...)
        // separately first, and then get authority from jElement's viewmode.
        if (attrValueOverall === true || attrValueOverall === false) {
          authority = attrValueOverall;
        } else if (this.viewmode == 'view') {
          authority = false;
        } else if (this.viewmode == 'edit') {
          authority = true;
        } else { // default value
          authority = true;
        }
      }

      function getAuthority(jElement, operateAttr) {
        // This element's attribute of authority, draggable || hoverable || resizable...
        var attrValue = jElement.options[operateAttr];

        // Get authority from properties(draggable,hoverable...)
        // separately first, and then get authority from jElement's viewmode.
        if (attrValue === true || attrValue === false) {
          authority = attrValue;
        } else if (jElement.options.viewmode == 'view') {
          authority = false;
        } else if (jElement.options.viewmode == 'edit') {
          authority = true;
        } else if (jElement.options.parent && $.trim(jElement.options.parent) != '' &&
          jDiagram.nodes[jElement.options.parent]) { // recursion : get authority from it's parent
          getAuthority(jDiagram.nodes[jElement.options.parent], operateAttr);
        }
      }

      return authority;
    },
    generateId: function(prefix) {
      var id;
      do {
        id = this.getId(prefix);
      } while (this.nodes[id] || this.paths[id] || this.pathsdelay[id]);
      return id;
    },
    getId: function(prefix) {
      if (prefix == 'N') { // If this is node,we use seqNode for ++
        this.seqNode = (this.seqNode || 0) + 1;
        return this.id + '_' + prefix + this.seqNode;
      } else { // If this is path,we use seqPath for ++
        this.seqPath = (this.seqPath || 0) + 1;
        return this.id + '_' + prefix + this.seqPath;
      }
    },
    setDefaultValue: function(options) {
      var op = Utils.parseDiagramOptions(options.options);

      // priority in options is higher than outside.
      var attrs = {
        viewmode: options.viewmode,
      };
      $.extend(true, attrs, op);

      for (var key in attrs) {
        this[key] = attrs[key];
      }
    },
    /**
     * Is mouse event on activemenu.
     * @param e: mosue event
     */
    isOnActiveMenu: function(e) {
      var jbd = this.jHoverElement.getBounds();
      var joffset = this.jHoverElement.$box.offset();
      var client = this.$client.offset();
      var rightMenu = $('.uee-activemenu-right').length > 0 ? Utils.getStyle($('.uee-activemenu-right').attr('style')) : undefined;
      var topMenu = $('.uee-activemenu-top').length > 0 ? Utils.getStyle($('.uee-activemenu-top').attr('style')) : undefined;
      var scrollBarX = this.$client.scrollLeft();

      // Mouse position relative the corner of right menu.
      var x1 = e.pageX - joffset.left - jbd.width;
      var y1 = e.pageY - joffset.top;

      // Mouse position relative the corner of top menu.
      var x2 = e.pageX - topMenu.left - client.left + scrollBarX;
      var y2 = joffset.top - e.pageY;

      if (rightMenu && (x1 >= -2 && x1 < rightMenu.width - 1 && y1 > 0 && y1 <= rightMenu.height)) {
        return true;
      } else if (topMenu && (x2 >= 0 && x2 < topMenu.width && y2 >= 0 && y2 < topMenu.height - 1)) {
        return true;
      } else {
        return false;
      }
    },
    /**
     * show active menu
     * @param : hover element
     */
    showActiveMenu: function(jHover) {
      if (!jBME.Diagram.MetaData.activeMenu || !jHover.options.activeMenuId) {
        return;
      }

      var jBound = jHover.getBounds();
      var activeMenu = jBME.Diagram.MetaData.activeMenu;
      var topNum = 0;
      var rightNum = 0;
      var jDiagram = this;

      // Count topnum and rightnum of activemenu for calculating size of DOM.
      for (var i = 0; i < activeMenu.length; i++) {
        var isShow = (jHover.options.activeMenuId.indexOf(i + 1) != -1 || jHover.options.activeMenuId == 'all');

        if (isShow) {
          activeMenu[i] = $.extend(true, {
            position: 'top',
            event: 'mousedown',
          }, activeMenu[i]);
          activeMenu[i].position == 'top' && topNum++;
          activeMenu[i].position == 'right' && rightNum++;
        }
      }

      // create DOM
      var menuStyle = ['left:', 0, 'px; top:', 0, 'px; height:1px; width:1px;'].join('');

      var rl = jBound.left + jBound.width + 1;
      var rt = jBound.top - 3;
      var rh = rightNum * 22 - 10;
      var rightMenuStyle = ['left:', rl, 'px; top:', rt, 'px; width:26px; height:', rh, 'px'].join('');
      var tl = jBound.left + jBound.width - topNum * 22 - 18;
      var tt = jBound.top - 29;
      var tw = jBound.left + jBound.width - 16 - tl;
      var topMenuStyle = ['left:', tl, 'px; top:', tt, 'px; width:', tw, 'px; height:26px'].join('');

      if (this.$activeMenu == undefined) {
        this.$activeMenu = jQuery(
          ['<div id="bc_activemenu" class="uee-activemenu-container" style="', menuStyle, '">', '</div>'].join(''));

        var $topActiveMenu = undefined;
        var $rightActiveMenu = undefined;

        for (var t = 0; t < activeMenu.length; t++) {
          var $menuitem = null;
          var isVisible = false;
          if (jHover.options.activeMenuId.indexOf(t + 1) != -1 || jHover.options.activeMenuId == 'all') {
            isVisible = true;
          }

          if (isVisible) {
            if (activeMenu[t].position == 'top') {
              if (!$topActiveMenu) {
                $topActiveMenu = jQuery(
                  ['<div class="uee-diagram-ele uee-activemenu-top" style="', topMenuStyle, '"></div>'].join(''));
                this.$activeMenu.append($topActiveMenu);
              }

              $menuitem = jQuery(
                ['<div class="uee-activemenuitem-top"><img id="activeMenu_', t, '"src="', activeMenu[t].img, '"/></div>'].join(''));
              this.$activeMenu.children('.uee-activemenu-top').append($menuitem);
            } else if (activeMenu[t].position == 'right') {
              if (!$rightActiveMenu) {
                $rightActiveMenu = jQuery(
                  ['<div class="uee-diagram-ele uee-activemenu-right" style="', rightMenuStyle, '"></div>'].join(''));
                this.$activeMenu.append($rightActiveMenu);
              }

              $menuitem = jQuery(
                ['<div class="uee-activemenuitem-right"><img id="activeMenu_', t, '"src="', activeMenu[t].img, '"/></div>'].join(''));
              this.$activeMenu.children('.uee-activemenu-right').append($menuitem);
            }

            $menuitem && $menuitem.children().bind(activeMenu[t].event, function(e) {
              var imgId = $(this).attr('id');
              var index = imgId.slice(11);
              activeMenu[index].handler && activeMenu[index].handler.call(null, jHover, jDiagram);
              jDiagram.removeActiveMenu();
            });
          }
        }
      }
      this.$client.append(this.$activeMenu);

      // hand pointer
      this.$activeMenu.bind('mouseenter', function(e) {
        jDiagram.$activeMenu.addClass('bf_activemenu_cursor');
      });

      // leave activemenu
      this.$activeMenu.bind('mouseleave', function(e) {
        jDiagram.$activeMenu.removeClass('bf_activemenu_cursor');
        jDiagram.$box.trigger('mouseleave.ishover');
      });
    },
    /**
     * remove active menu
     */
    removeActiveMenu: function() {
      this.$activeMenu && this.$activeMenu.remove();
      this.$activeMenu = undefined;
    },
    requestNode: function(creater, options) {
      if (this.connecting) {
        return;
      }

      if (this.createNodeMode == 'click' || this.createNodeMode == 'all') {
        this.clickCreateNode(creater, options);
      }

      if (this.createNodeMode == 'drag' || this.createNodeMode == 'all') {
        this.draggingCreateNode(options);
      }
    },
    requestPath: function(creater, options) {
      var jDiagram = this;
      if (jDiagram.connecting) {
        return;
      }

      jDiagram.request.add(jBME.Request.TYPES.CREATE_PATH, function(e) {
        if (jDiagram.jHoverElement == null || !jDiagram.jHoverElement.isnode ||
          jDiagram.triggerPathBegin(jDiagram.jHoverElement)) { // Customize event : beginpath,if user prevent connection,return
          jDiagram.$box.removeClass('creating');
          return;
        }

        if (typeof options == 'string') {
          options = jBME.Diagram.MetaData[options];
        }

        // Create path: start connecting
        var jPath = jDiagram.createPathOuter(options, null, e);
        var jMouseNode = new MouseNode();
        jMouseNode.updateBounds(jDiagram.getPositionWithScroll(e));
        jPath.setSource(jDiagram.jHoverElement, jDiagram.jHoverElement.hoverAnchor);
        jPath.setTarget(jMouseNode);
        jDiagram.attachPath(jPath.id, options, 'inner');
        jDiagram.connecting = true;
        // connecting...
        jDiagram.$box.bind('mousemove.connecting', function(emv) {
          if (jDiagram.jHoverElement && jDiagram.jHoverElement.hoverAnchor && jPath.jType.isOrthogonal) {
            var type = jDiagram.getPathType('target', jPath.sourceAnchorid, jDiagram.jHoverElement.hoverAnchor);
            jPath.jType = jBME.Path.Type[type] || jBME.Path.Type['default'];
            jPath.options.controls = $.extend([], jPath.jType.controls);
            jPath.options.figures[0].type = type;
            jPath.update();
          }
          jMouseNode.updateBounds(jDiagram.getPositionWithScroll(emv));
        });

        // stop connecting.
        var endPoint = 'target';
        var source = 'pathcreate';
        jDiagram.stopConnecting(this.request, jPath, e, endPoint, source, creater);
      });
      this.$box.addClass('creating');
      Utils.$(creater).addClass('active');
    },
    stopConnecting: function(request, jPath, e, endPoint, source, creater, oldNode, oldAnchor) {
      request.add(jBME.Request.TYPES.CREATE_PATH, function(eup) {
        if (this.jHoverElement && (
          (e.pageX == eup.pageX && e.pageY == eup.pageY)
          // self connect and prevent 'click' the same anchor.
          ||
          (this.selfConnection && this.jHoverElement.id == jPath.jSource.id &&
            this.jHoverElement.hoverAnchor == jPath.sourceAnchorid)
          // can't selft connect
          ||
          (!this.selfConnection && (this.jHoverElement.id == jPath.jSource.id ||
            this.jHoverElement.id == jPath.jTarget.id)))) {
          return false;
        }

        // DONE.
        if (this.jHoverElement && this.jHoverElement.isnode) {
          jPath.setEnd(endPoint, this.jHoverElement, this.jHoverElement.hoverAnchor);

          // this.triggerPathEnd : whether end node is prevented
          if (!this.triggerPathEnd(jPath)) {
            // Customize event trigger : after path is created
            this.triggerElementOperation(jPath, source);
            this.syncJson();
          } else {
            this.finishConnect(jPath, oldNode, oldAnchor, endPoint);
          }
        } else {
          if (source == 'pathreconnect' && this.$box.hasClass('reconnect')) { // Connect scenario: first click path and start reconnect.
            return false;
          }
          this.finishConnect(jPath, oldNode, oldAnchor, endPoint);
        }

        this.cleanupConnection(creater);
      });
    },
    finishConnect: function(jPath, oldNode, oldAnchor, endPoint) {
      if (oldNode && oldAnchor) {
        jPath.setEnd(endPoint, oldNode, oldAnchor);
      } else {
        this.remove(jPath, '', true);
      }
    },
    cleanupConnection: function(creater) {
      // Clean up
      this.connecting = false;
      this.$box.unbind('mousemove.connecting');
      this.$box.removeClass('creating');
      creater && Utils.$(creater).removeClass('active');
    },
    /**
     * Original method : click to create node.
     */
    clickCreateNode: function(creater, options) {
      this.request.add(jBME.Request.TYPES.CREATE_NODE, function(e) {
        if (typeof options == 'string') {
          options = jBME.Diagram.MetaData[options];
        }
        this.createNodeOuter(options, 'nodecreate', e);
        scp.$digest();
        this.$box.removeClass('creating');
        Utils.$(creater).removeClass('active');
      });

      this.unregisterNode();
      this.$box.addClass('creating');
      Utils.$(creater).addClass('active');
    },
    /**
     * Remember node's options for creating node when mouse enter diagram scope.
     * @param options
     */
    draggingCreateNode: function(options) {
      if (typeof options == 'string') {
        this.registeredOptions = jBME.Diagram.MetaData[options];
      } else {
        this.registeredOptions = options;
      }
    },
    /**
     * Clear attributes of node when drop the node.
     */
    unregisterNode: function() {
      this.registeredOptions = null;
      this.registeredNode = null;
      this.draggingCreate = false;
    },
    /**
     * Invoke draggable method of jquery ui with default arguments.
     * If you want to set arguments youself, set value into attrs.
     * @param dragSelector : the selector of dom you want to drag
     * @param attrs : customized attributes of jquery ui
     * @see http:// api.jqueryui.com/draggable/
     */
    setDraggingAttribute: function(dragSelector, attrs) {
      if (!$(dragSelector).draggable) {
        window.console && console.log('You need to import jquery ui');
        return;
      }

      var that = this;
      var defaultAttrs = {
        revert: true,
        revertDuration: 50,
        scroll: false,
        drag: function(e) {
          if (!that.draggingCreate) {
            that.request.remove();
            that.connecting = false;
            that.$box.removeClass('creating');
            that.draggingCreate = true;
          }
        },
        stop: function(e) {
          that.unregisterNode();
        },
      };
      var attributes = $.extend({}, defaultAttrs, attrs);

      $(dragSelector).draggable(attributes).bind('click', function() {
        $(this).focus();
      });
    },
    /**
     * When selectDrag point of path, we reconnect path again.
     * @param jPoint : "jSource" or "jTarget"
     * @param jPath : this path
     */
    reconnectPath: function(jPoint, jPath, e) {
      var jDiagram = this;
      var jMouseNode = new MouseNode();
      jDiagram.request.add(jBME.Request.TYPES.CREATE_PATH, function(e) {
        var jName = 'j' + Utils.capitalize(jPoint);
        var oldNode = jPath[jName]; // Node delay(jSource or jTarget) for update later.
        var oldAnchor = jPath[jPoint + 'Anchorid'];

        jMouseNode.updateBounds(this.getPositionWithScroll(e));
        jPath.setEnd(jPoint, jMouseNode);
        jDiagram.connecting = true;
        jDiagram.$box.addClass('creating');
        oldNode.update();

        var sourceAnchor;
        var targetAnchor;

        // connecting...
        jDiagram.$box.bind('mousemove.connecting', function(emv) {
          if (jDiagram.jHoverElement && jDiagram.jHoverElement.hoverAnchor && jPath.jType.isOrthogonal) {
            if (jPoint == 'source') {
              sourceAnchor = jDiagram.jHoverElement.hoverAnchor;
              targetAnchor = jPath.targetAnchorid;
            } else {
              sourceAnchor = jPath.sourceAnchorid;
              targetAnchor = jDiagram.jHoverElement.hoverAnchor;
            }
            var type = jDiagram.getPathType('target', sourceAnchor, targetAnchor);
            jPath.jType = jBME.Path.Type[type] || jBME.Path.Type['default'];
            jPath.options.controls = $.extend([], jPath.jType.controls);
            jPath.options.figures[0].type = type;
            jPath.update();
          }
          jMouseNode.updateBounds(jDiagram.getPositionWithScroll(emv));
        });

        // stop connecting.
        jDiagram.stopConnecting(this.request, jPath, e, jPoint, 'pathreconnect', null, oldNode, oldAnchor);
      });
    },
    /**
     * Draw path from given anchor.
     */
    drawingPathFromAnchor: function(jElement, anchor, e) {
      var jDiagram = this;
      if (jElement == null || !jElement.isnode || jDiagram.triggerPathBegin(jElement)) { // Customize event : beginpath,if user prevent connection,return
        this.$box.removeClass('creating');
        return;
      }
      this.request.add(jBME.Request.TYPES.CREATE_PATH);

      // Create path: start connecting
      var lineType = this.typeofPath;
      var options = {
        figures: [{
          type: lineType || 'line',
        }],
      };
      options.figures[0].type == 'elbow' && (options.figures[0].type = this.getPathType('source', anchor));
      var jPath = this.createPathOuter(options, null);
      var jMouseNode = new MouseNode();
      jMouseNode.updateBounds(this.getPositionWithScroll(e));
      jPath.setSource(jElement, anchor);
      jPath.setTarget(jMouseNode);
      this.attachPath(jPath.id, options, 'inner');
      jDiagram.connecting = true;

      // connecting...
      this.$box.bind('mousemove.connecting', function(emv) {
        if (jDiagram.jHoverElement && jDiagram.jHoverElement.hoverAnchor && lineType == 'elbow' &&
          jDiagram.jHoverElement != jElement) {
          options.figures[0].type = jDiagram.getPathType('target', anchor, jDiagram.jHoverElement.hoverAnchor);
          jPath.jType = jBME.Path.Type[options.figures[0].type] || jBME.Path.Type['default'];
          jPath.options.controls = $.extend([], jPath.jType.controls);
          jPath.options.figures[0].type = options.figures[0].type;
          jPath.update();
        }
        jMouseNode.updateBounds(jDiagram.getPositionWithScroll(emv));
      });

      // stop connecting.
      var endPoint = 'target';
      var source = 'pathcreate';
      jDiagram.stopConnecting(this.request, jPath, e, endPoint, source);

      this.$box.addClass('creating');
    },
    /**
     * Iterate over diagram's nodes and path, executing a function for each matched element.
     * We can break the loop at a particular iteration by making the callback function return false.
     */
    each: function(callback) { // Just like $.each() does, however when one loop breaks, the whole loop will stop.
      for (var id in this.nodes) {
        if (callback.call(this.nodes[id], id, this.nodes[id]) === false) {
          return this;
        }
      }
      
      for (var id in this.paths) {
        if (callback.call(this.paths[id], id, this.paths[id]) === false) {
          return this;
        }
      }
      
      return this;
    },
    getFirstSelection: function(jDiagram) {
      for (var id in this.selections) {
        return this.selections[id];
      }
      return null;
    },
    /**
     * TODO tempfunction
     * @param options
     * @return
     */
    updateSelection: function(options) {
      for (var id in this.selections) {
        var jElement = this.selections[id];
        $.extend(true, jElement.options, options);
        jElement.draw();
      }
    },
    /**
     * Remove the selected elements(node|path) one by one.
     */
    removeSelections: function(source) {
      var selections = this.getIndependentSelections();
      var deleteNodes = $.extend({}, selections);
      var deletePaths = {};
      var extendNodes = {};
      var deleteSelections = {};
      var onpathnodes = {};

      if ($.isEmptyObject(selections)) {
        return;
      }


      for (var did in deleteNodes) {
        var deleteNode = deleteNodes[did];
        if (deleteNode instanceof jBME.Node) {
          deletePaths = $.extend(true, deletePaths, deleteNodes[did].paths);
        } else if (jBME.Diagram.MetaData.removechild) {
          var tmp = {};
          tmp[deleteNode.jTarget.id] = deleteNode.jTarget;
          extendNodes = $.extend(true, extendNodes, tmp);
        } else {
          onpathnodes = $.extend(true, onpathnodes, deleteNodes[did].textnodes);
        }
      }
      for (var did in deletePaths) {
        onpathnodes = $.extend(true, onpathnodes, deletePaths[did].textnodes);
      }
      deleteSelections = $.extend(deleteNodes, deletePaths, onpathnodes);
      !source && this.triggerElementOperation(deleteSelections, 'selectionsdelete', source);

      for (var id in selections) {
        // Delete switch
        if ((!this.nodes[id] && !this.paths[id]) || !this.authManager(selections[id], 'removable')) {
          continue;
        }

        this.remove(selections[id], source);
      }

      for (var id in extendNodes) {
        var sourcextend = source || 'selectionsdelete';
        this.remove(extendNodes[id], sourcextend);
      }

      // TODO for performance
      this.updateMaxBounds();

      $.browser.msie && this.$box.focus();
      this.syncJson();

      this.request.remove();
      this.connecting = false;
      this.$box.removeClass('creating');
      this.$box.removeClass('hover');
    },
    /**
     * Edit node || pathtext.
     * @param edit, true/false
     */
    toggleTextEdit: function(edit /* optional*/, e, jElement) {
      if (jElement) {
        // TextEdit or dblclick to create text on path switch
        if (!this.authManager(jElement, 'editable')) {
          return;
        }

        if (jElement.isnode) {
          jElement.toggleTextEdit(edit, this);
        } else if (this.jHoverElement.ispath) {
          var eventBounds = this.getPositionWithScroll(e);
          var closestPosition = this.jHoverElement.getClosestPointFromPath(eventBounds.left, eventBounds.top);
          var options = Utils.generateTextOptions(jElement.id, closestPosition, this.defaultConnectionText);
          var textnode = this.createNodeOuter(options, 'pathdblclick');

          // Update the textnodes of this path.
          jElement.setTextOnPath(textnode);
        }
      }
    },
    /**
     * Customize event:create|delete a node|path.
     * @param elementInfo:Object the information of element(node object| path object) eg:Object { id="N4", $box=[1], more...}
     * @param operation:String (nodecreate|pathcreate|nodedelete|pathdelete|titlechange)
     */
    triggerElementOperation: function(jElement, action, source, event /* optional*/) {
      var jEvent = $.Event(jBME.Diagram.EVENT.notifychange);
      jEvent.jElement = jElement;
      jEvent.event = event;
      jEvent.action = action;
      jEvent.triggerSource = source || 'outer';
      this.$box.trigger(jEvent);
    },
    /**
     * Customize event:user decide that whether this node can be beginning.
     * @param linkSource: node object eg:Object { id="N4", $box=[1], more...}
     */
    triggerPathBegin: function(jElement) {
      var jEvent = $.Event(jBME.Diagram.EVENT.connectstart);
      jEvent.jElement = jElement;
      this.$box.trigger(jEvent);
      return jEvent.isDefaultPrevented();
    },
    /**
     * Customize event:user decide that whether this node can be the end.
     * @param linkPath:path object
     */
    triggerPathEnd: function(jElement) {
      var jEvent = $.Event(jBME.Diagram.EVENT.connectend);
      // the information of element(path object)
      jEvent.jElement = jElement;
      this.$box.trigger(jEvent);
      return jEvent.isDefaultPrevented();
    },
    triggerBeforeCreateNode: function(options, e, source) {
      var jEvent = $.Event(jBME.Diagram.EVENT.beforecreatenode);
      jEvent.options = options;
      jEvent.event = e;
      jEvent.triggerSource = source || 'outer';
      this.$box.trigger(jEvent);
      return jEvent.isDefaultPrevented();
    },
    triggerDragEnd: function(jElement, dragPosition, dropPosition) {
      var jEvent = $.Event(jBME.Diagram.EVENT.ondragover);
      jEvent.jElement = jElement;
      jEvent.dragPosition = dragPosition;
      jEvent.dropPosition = dropPosition;
      this.$box.trigger(jEvent);
    },
    triggerBendCreate: function(jElement, index, elbowPoints) {
      var jEvent = $.Event(jBME.Diagram.EVENT.bendcreate);
      jEvent.jElement = jElement;
      jEvent.index = index;
      jEvent.elbowPoints = elbowPoints;
      this.$box.trigger(jEvent);
    },
    triggerBendMove: function(jElement, index, elbowPoints, isDelete) {
      var jEvent = $.Event(jBME.Diagram.EVENT.bendmove);
      jEvent.jElement = jElement;
      jEvent.index = index;
      jEvent.elbowPoints = elbowPoints;
      jEvent.isDelete = isDelete;
      this.$box.trigger(jEvent);
    },
    /**
     * When diagram is changed, record a json for undo and redo to use.
     */
    syncJson: function() {
      if (this.json) {
        this.json.length = this.currentjsonflag;
        if (this.json.length < this.maxState) {
          this.json.push(this.toJSON());
        } else {
          this.json.shift();
          this.json.push(this.toJSON());
        }
      } else {
        this.json = [];
        this.json.push(this.toJSON());
        this.currentjsonflag = 1;
      }
      this.currentjsonflag = this.json.length;
    },
    drawing: function(drawHandler, e) {
      if (this.createPathMode == 'drag') {
        drawHandler.apply(this, arguments);
      } else {
        $doc.bind('mouseup.drawing', function(eup) {
          if (e.pageX == eup.pageX && e.pageY == eup.pageY) {
            drawHandler.apply(this, arguments);
          }
          $doc.unbind('mouseup.drawing');
        });
      }
    },
    /**
     * Auto align
     * For user to set element selected alignment by arguments
     * @param alignArgs : String left | center | right | top | middle | bottom
     * @param refNodeId : String reference node id
     */
    setAlign: function(alignArgs, refNodeId) {
      // When selections is {} and no reference node.
      if ($.isEmptyObject(this.selections) || !this.nodes[refNodeId]) {
        return;
      }

      // top and left of reference node.
      var alignLeft = this.nodes[refNodeId].getBounds().left;
      var alignTop = this.nodes[refNodeId].getBounds().top;

      // Change the top or left of selected node according to refNode.
      for (var nid in this.selections) {
        if (nid != refNodeId && this.selections[nid].isnode) {
          var newBound = {};
          switch (alignArgs) { // set offset of selected node
            case 'left':
              newBound = {
                left: alignLeft,
              };
              break;
            case 'center':
              newBound = {
                left: alignLeft + this.nodes[refNodeId].width / 2 - this.selections[nid].width / 2,
              };
              break;
            case 'right':
              newBound = {
                left: alignLeft + this.nodes[refNodeId].width - this.selections[nid].width,
              };
              break;
            case 'top':
              newBound = {
                top: alignTop,
              };
              break;
            case 'middle':
              newBound = {
                top: alignTop + this.nodes[refNodeId].height / 2 - this.selections[nid].height / 2,
              };
              break;
            case 'bottom':
              newBound = {
                top: alignTop + this.nodes[refNodeId].height - this.selections[nid].height,
              };
              break;
            default:
              return;
          }

          this.selections[nid].updateBounds(newBound);
        }
      }

      this.syncJson();

      // trigger model change
      this.triggerElementOperation(this.selections, 'nodeschange', 'drag');
    },
    /**
     * Set the same interval of selected nodes.
     * @param direction : vertical or horizontal
     */
    setSameInterval: function(direction) {
      if ($.isEmptyObject(this.selections)) {
        return;
      }

      var selections = this.selections;
      var selectedBounds = {};
      var selectedElements = [];

      for (var sid in selections) {
        if (selections[sid].isnode) {
          selectedBounds[sid] = selections[sid].getBounds();
          selectedBounds[sid]['id'] = sid;
          selectedElements.push(selectedBounds[sid]);
        }
      }

      if (selectedElements.length > 1) {
        if (direction == 'vertical') {
          this.setSameDirectionInterval(selectedBounds, selectedElements, 'top');
        } else {
          this.setSameDirectionInterval(selectedBounds, selectedElements, 'left');
        }
      }
    },
    setSameDirectionInterval: function(selectedBounds, selectedElements, direction) {
      var selectedElementsAsc = Utils.quickSort(selectedElements, direction);
      var fristElement = selectedElementsAsc[0];
      var lastElement = selectedElementsAsc[selectedElementsAsc.length - 1];
      var intervalKey = direction == 'left' ? 'width' : 'height';
      var totalValue = 0;

      for (var item = 0; item < selectedElementsAsc.length; item++) {
        totalValue += selectedElementsAsc[item][intervalKey];
      }

      var totalInterval = lastElement[direction] + lastElement[intervalKey] - fristElement[direction];
      var deviation = (totalInterval - totalValue) / (selectedElementsAsc.length - 1);
      var previoustValue = selectedElementsAsc[0][direction];

      if (deviation >= 0) { // Original interval is enough.
        for (var index = 1; index < selectedElementsAsc.length - 1; index++) {
          var node = this.nodes[selectedElementsAsc[index].id];

          if (direction == 'left') {
            node.updateBounds({
              left: previoustValue + selectedElementsAsc[index - 1][intervalKey] + deviation,
            });
          } else {
            node.updateBounds({
              top: previoustValue + selectedElementsAsc[index - 1][intervalKey] + deviation,
            });
          }

          previoustValue = previoustValue + selectedElementsAsc[index - 1][intervalKey] + deviation;
        }
      } else { // Original interval is not enough for these nodes.
        var newDeviation = (lastElement[direction] - fristElement[direction]) / (selectedElementsAsc.length - 1);
        for (var index = 1; index < selectedElementsAsc.length - 1; index++) {
          var node = this.nodes[selectedElementsAsc[index].id];

          if (direction == 'left') {
            node.updateBounds({
              left: previoustValue + newDeviation,
            });
          } else {
            node.updateBounds({
              top: previoustValue + newDeviation,
            });
          }

          previoustValue = previoustValue + newDeviation;
        }
      }
    },
    /**
     * Set uniform size
     * For user to set elements' size by arguments
     * @param refNodeId : String reference node id
     */
    setSize: function(refNodeId) {
      // When selections is {} and no reference node.
      if ($.isEmptyObject(this.selections) || !this.nodes[refNodeId]) {
        return;
      }

      // Change the width and height of selected node according to refNode.
      for (var nid in this.selections) {
        if (nid != refNodeId && this.selections[nid].isnode) {
          var newBounds = {
            width: this.nodes[refNodeId].width,
            height: this.nodes[refNodeId].height,
          };
          this.selections[nid].updateBounds(newBounds);
        }
      }

      this.syncJson();
    },
    /**
     * For outer to set type of path drawed from anchor.
     */
    setPathType: function(type) {
      this.typeofPath = type;
    },
    /**
     * Get type of path according to anchor.
     * @param jPath
     * @param whichPoint
     * @param targetAnchor
     * @returns {String}
     */
    getPathType: function(whichPoint, sourceAnchor, targetAnchor /* optional, hover target anchor*/) {
      var lineType = 'elbowDoub';
      if (whichPoint == 'source') {
        if (sourceAnchor % 4 == 1) {
          lineType = 'elbow';
        } else if (sourceAnchor % 4 == 3) {
          lineType = 'elbowOpp';
        }
      } else {
        if (sourceAnchor % 4 == 1 && targetAnchor % 4 == 1) {
          lineType = 'elbowDoubOpp';
        } else if (sourceAnchor % 4 == 1 && targetAnchor % 4 == 3) {
          lineType = 'elbow';
        } else if (sourceAnchor % 4 == 3 && targetAnchor % 4 == 1) {
          lineType = 'elbowOpp';
        } else if (sourceAnchor % 4 == 3 && targetAnchor % 4 == 3) {
          lineType = 'elbowDoub';
        }
      }
      return lineType;
    },
    /**
     * Set children's size of container by every node's type.
     * For user to set container's children's size
     * Loop container's children, and find first type as refNode, then set size of others whose type are same by refNode .
     * @param refContainerId : String reference container id
     */
    unifyChildrenSize: function(refContainerId) {
      var container = this.nodes[refContainerId];
      var refNodes = {}; // reference nodes

      for (var i = 0; i < container.options.children.length; i++) {
        var nodeType = this.nodes[container.options.children[i]].jType.name;
        if (!refNodes[nodeType]) { // Every type cache one node as refNode.
          refNodes[nodeType] = this.nodes[container.options.children[i]];
        } else { // Unify size according to refNode
          var newBound = {
            width: refNodes[nodeType].width,
            height: refNodes[nodeType].height,
          };
          this.nodes[container.options.children[i]].updateBounds(newBound);
        }
      }
    },
    /**
     * Get a subset
     * Reduce the set of selected elements to those that match the function's test.
     */
    getSelections: function(filter) {
      var subset = {};
      for (var id in this.selections) {
        if (filter && !filter.call(this.selections[id], id)) {
          continue;
        }
        subset[id] = this.selections[id];
      }
      return subset;
    },
    /**
     * Return a sub-set of current selections.
     * Each selected element can be moved/removed independent.
     * In another word, if an element and its ancestor are both selected, we only take the top selected ancestor as selection.
     * This is useful when remove/move a group of selected elements.
     */
    getIndependentSelections: function(filter) {
      var jDiagram = this;
      return jDiagram.getSelections(function(id) {
        if (filter && !filter.call(this, id)) {
          return false;
        }
        var ancestors = jDiagram.getElementAncestors(id);
        for (var i = 0; i < ancestors.length; ++i) {
          if (jDiagram.selections[ancestors[i]]) {
            return false;
          }
        }
        return true;
      });
    },
    /**
     * Return set of a group which contain all nodes in this group.
     * @param groupId:String
     */
    getGroupMembers: function(nodeid) {
      var members = {};
      var groupId = '';
      if (!nodeid || nodeid == '' || this.nodes[nodeid].options.group.length == 0) {
        return members;
      }
      groupId = this.nodes[nodeid].options.group[this.nodes[nodeid].options.group.length - 1];

      for (var nid in this.nodes) {
        if (this.nodes[nid].options.group.length > 0 && (this.nodes[nid].options.group[this.nodes[nid].options.group.length - 1] == groupId)) {
          members[nid] = this.nodes[nid];
        }
      }
      return members;
    },
    /**
     * Remove the given element(node|path)
     * @param jElement : delete this element
     * @param innerArgs : request path need to use
     */
    remove: function(jElement, source, innerArgs) {
      if (!jElement || (!this.nodes[jElement.id] && !this.paths[jElement.id])) {
        return;
      }

      if (jElement.isnode) { // Remove node, meanwhile remove paths connected to this node.
        if (jElement.iscontainer) {
          if (jElement.options.children.length > 0) { // Remove container's children.
            for (var nid in jElement.children) {
              this.remove(jElement.children[nid], source);
            }
          }
          // Remove swimlane to update other swimlanes
          if (jElement.isswimlane) {
            jElement.removeSwimLane();
          }
        }
        // When remove textnode, clear the information of its path.
        if (jElement.jType.name == 'default' && jElement.options.onpath &&
          jElement.options.onpath.pathid && jElement.options.onpath.scale >= 0) {
          this.paths[jElement.options.onpath.pathid] && this.paths[jElement.options.onpath.pathid].removeTextOnPath(jElement.id);
        }

        var bounds = jElement.getBounds();
        this.removeCenterPoint(this.hcenters, bounds.left + bounds.width / 2);
        this.removeCenterPoint(this.vcenters, bounds.top + bounds.height / 2);

        // remove all child nodes
        if (jBME.Diagram.MetaData.removechild) {
          var deleteNodes = this.nodes[jElement.id].getNextNode();
          var sourcechild = source || 'removechild';
          for (var id in deleteNodes) {
            this.remove(deleteNodes[id], sourcechild);
          }
        }

        this.removeFamily(jElement);
        this.nodes[jElement.id].iscontainer && delete this.containers[jElement.id];
        delete this.nodes[jElement.id];

        if (jElement.isswimlane) {
          jBME.Diagram.DOMCache.prototype.updateWindow(null, null, this);
        }

        // Customize event : nodedelete
        source && this.triggerElementOperation(jElement, 'nodedelete', source);
        for (var id in jElement.paths) {
          this.remove(jElement.paths[id], source);
        }
      } else { // Remove path, de-select it first to redraw the connected nodes.
        this.removeWhenNotNode(jElement, innerArgs, source);
      }
      delete this.selections[jElement.id];
      jElement.$box.remove();
      this.removeActiveMenu();
    },
    removeWhenNotNode: function(jElement, innerArgs, source) {
      if (this.paths[jElement.id]) { // Prevent duplicate remove.
        delete this.paths[jElement.id];

        if (!innerArgs) { // If innerArgs undefined, trigger customize event -- pathdelete
          source && this.triggerElementOperation(jElement, 'pathdelete', source);
        }
        jElement.toggleSelect(false);
        jElement.setSource(null).setTarget(null);
        for (var tid in jElement.textnodes) {
          this.remove(jElement.textnodes[tid], source);
        }
      }
    },
    removeById: function(id) {
      var jElement = this.nodes[id] || this.paths[id];
      jElement && this.remove(jElement);
    },
    removeAnchorNode: function(fatherid, anchorid) {
      var fathernode = this.nodes[fatherid];
      var nextNodes = $.extend(true, [], fathernode.getNextNodeByAnchor(anchorid));
      for (var i = 0; i < nextNodes.length; i++) {
        this.remove(nextNodes[i], 'removeanchornode');
      }

      for (var i = 0; i < fathernode.options.anchornodes.length; i++) {
        if (fathernode.options.anchornodes[i].id == anchorid) {
          fathernode.options.anchornodes.splice(i, 1);
        }
      }

      if (fathernode.anchornodes && fathernode.anchornodes[anchorid]) {
        fathernode.anchornodes[anchorid].instance.$box.remove();
        delete fathernode.anchornodes[anchorid].instance;
        delete fathernode.anchornodes[anchorid];
      }
    },
    /**
     * Return the position relative to the node's offset container(diagram or parent node),
     *   by the give offset relative to the document(page)
     */
    getPositionFromOffset: function(pageEvent) {
      var offset = this.$box.offset();
      return {
        left: pageEvent.pageX - offset.left,
        top: pageEvent.pageY - offset.top,
      };
    },
    /**
     * Return the position relative to the node's offset container(diagram or parent node),
     *   by the give offset + scrollLeft|scrollTop relative to the document(page)
     */
    getPositionWithScroll: function(pageEvent) {
      var offset = this.$box.offset();
      return {
        left: pageEvent.pageX + this.$client.scrollLeft() - offset.left,
        top: pageEvent.pageY + this.$client.scrollTop() - offset.top,
      };
    },
    /**
     * Return the given element(node|path)'s all ancestor nodes id (Array, [parentid, grandparentid, ...]).
     * If the element is not with another node(that means element is directly within diagram), return empty Array [].
     */
    getElementAncestors: function(id) {
      var jElement = this.nodes[id] || this.paths[id];
      if (!jElement || jElement.$box.parent()[0] === this.$client[0]) {
        return [];
      } else {
        return jElement.$box.parentsUntil('#' + this.id, 'div.uee-node').map(function() {
          return this.id;
        });
      }
    },
    /**
     * nodes:[{},{},{},{},...],
     * paths:[{},{},{},{},...],
     */
    toJSON: function() {
      // nodes:
      var sb = ['{"nodes":['];
      var length = sb.length;

      function jElementToJSON(id, jElement) {
        sb.push(jElement.toJSON(), ',');
      }

      $.each(this.nodes, jElementToJSON);
      (sb.length > length) && sb.pop();

      // paths:
      sb.push('],"paths":[');
      length = sb.length;
      $.each(this.paths, jElementToJSON);
      (sb.length > length) && sb.pop();
      sb.push(']}');
      return sb.join('');
    },
    getJSONData: function() {
      var nodesnow = [];
      var pathsnow = [];

      function jElementNodesToJSON(id, jElement) {
        nodesnow.push(jElement.getJSONData());
      }

      function jElementPathsToJSON(id, jElement) {
        pathsnow.push(jElement.getJSONData());
      }

      // nodes:
      $.each(this.nodes, jElementNodesToJSON);

      // paths:
      $.each(this.paths, jElementPathsToJSON);

      var result = {
        nodes: nodesnow,
        paths: pathsnow,
      };
      return result;
    },
    /**
     * Put all nodes in a horizontal line
     * @param json
     * @return
     */
    horizontalLayout: function(json) {
      var sequence = [];
      var node = this.findStartNode(json);
      sequence.push(node.id);
      this.nodes[node.id].updateBounds(this.createBounds(sequence.length - 1, this.nodes[node.id].title()));

      while (this.nodes[node.id].getNextNode().length != 0) {
        if (this.nodes[node.id].getNextNode().length == 1 && this.nodes[node.id].getNextNode()[0].id != node.id) {
          sequence.push(this.nodes[node.id].getNextNode()[0].id);
          node = this.nodes[node.id].getNextNode()[0];
        } else {
          for (var i = 0; i < this.nodes[node.id].getNextNode().length; ++i) {
            var nextNode = this.nodes[node.id].getNextNode()[i];

            if (Utils.indexOf(sequence, nextNode.id) == -1) {
              sequence.push(nextNode.id);
              node = nextNode;
              break;
            }
          }
        }
        this.nodes[node.id].updateBounds(this.createBounds(sequence.length - 1, this.nodes[node.id].title()));
      }
      this.pathLayout(sequence);
    },
    /**
     * To update the options of the path.
     * @param sequence
     * @return
     */
    pathLayout: function(sequence) {
      var jPathType = false;
      for (var pid in this.paths) {
        var layoutPath = this.paths[pid];
        var jSourceIndex = Utils.indexOf(sequence, layoutPath.jSource.id);
        var jTargetIndex = Utils.indexOf(sequence, layoutPath.jTarget.id);

        // To update the options of the path ,which is
        if (jSourceIndex > -1 && jTargetIndex > -1 && jSourceIndex > jTargetIndex) {
          $.extend(true, this.paths[pid].options, this.ceatePathType(jPathType));
          this.paths[pid].update();
          jPathType = !jPathType;
        }
      }
    },
    /**
     * To find the first node of a flow.
     * @param json
     * @return
     *    the first node of a flow.
     */
    findStartNode: function(json) {
      var start = json.nodes[0];
      for (var i = 0; i < json.nodes.length; ++i) {
        start = json.nodes[i];
        for (var j = 0; j < json.paths.length; ++j) { // The first node of a flow should not be target of a path
          if (json.nodes[i].id && json.paths[j].target && json.nodes[i].id == json.paths[j].target) {
            start = null;
            break;
          }
        }

        if (start) {
          break;
        }
      }
      return start;
    },

    /**
     * trigger angular to update model
     */
    updateModel: function() {
      var $scope = angular.element('#' + this.id).scope();
      $scope.updateModel('all', JSON.parse(this.toJSON()));
    },

    hideWithChlidrenAndPaths: function(node) {
      $('#' + node.id).css('display', 'none');
      var paths = node.paths;
      for (var i in paths) {
        $('#' + i).css('display', 'none');
      }

      var children = node.options.children;
      var len = children.length;
      if (len > 0) {
        for (var i = 0; i < len; i++) {
          this.hideWithChlidrenAndPaths(this.nodes[i]);
        }
      }
    },

    showWithChlidrenAndPaths: function(node) {
      $('#' + node.id).css('display', 'block');
      var paths = node.paths;
      for (var i in paths) {
        $('#' + i).css('display', 'block');
      }

      var children = node.options.children;
      var len = children.length;
      if (len > 0) {
        for (var i = 0; i < len; i++) {
          this.showithChlidrenAndPaths(this.nodes[i]);
        }
      }
    },

    /**
     * TODO to be refactor for better update ways.
     * @param json
     * @return
     */
    update: function(json) {
      if (!json) {
        return;
      }

      // Need to clean lanes when save diagram.
      jBME.Swimlane.prototype.lanes = [];
      jBME.Horizontalswimlane.prototype.lanes = [];

      // Hidden client div calculating width and height is not correct.
      // So we use default value.
      var isClientHidden = this.$client.is(':hidden');
      var clientBounds = {
        width: isClientHidden ? this.clientWidth : this.$client.width(),
        height: isClientHidden ? this.clientHeight : this.$client.height(),
      };

      this.maxBounds = {
        xId: '',
        xValue: 0,
        yId: '',
        yValue: 0,
      };

      this.DOMCache = document.createDocumentFragment();

      // [Node]
      if (json.nodes) {
        this.updateNodes(json.nodes, clientBounds);
      }

      // [Path]
      if (json.paths) {
        this.updatePaths(json.paths, clientBounds);
      }

      // Update indicator position.
      this.$client.children('.uee-overflow-indicator').css({
        left: this.maxBounds.xValue,
        top: this.maxBounds.yValue,
      });

      // To support horizontal layout.
      if (json.layout) {
        this.horizontalLayout(json);
      }

      // hide invisible nodes
      for (var nid in this.nodes) {
        if (this.nodes[nid].options.visible == false) {
          this.hideWithChlidrenAndPaths(this.nodes[nid]);
        }
      }

      // hide invisible paths
      for (var pid in this.paths) {
        if (this.paths[pid].options.visible == false) {
          $('#' + pid).css('display', 'none');
        }
      }


      // For performance, first value of json array is object,
      // then all values is String.
      this.json = [];
      this.json.push(json);
      this.currentjsonflag = 1;

      this.afterCreate();
    },
    /**
     * Init or update nodes.
     */
    updateNodes: function(nodes, clientBounds) {
      // DocumentFragment for collecting nodes
      this.ofragment = document.createDocumentFragment();

      for (var i = 0; i < nodes.length; ++i) {
        var options = nodes[i];
        // diff start from BME3.0
        // reason : the datasource from java parsed may cotain null value
        for (var key in options) {
          if (options[key] == null) {
            delete options[key];
          }
        }
        // diff end
        var node = this.nodes[options.id];
        if (node) {
          // For undo redo initialize zoom scale
          this.zoomargs = options.zoomScale = options.zoomScale || 1;
          node.update(options);
        } else {
          node = this.createNode(options, null, null, clientBounds);
          options.id = node.id;
        }

        this.setMaxBounds(options.id, node.getBounds());
      }

      // Just append DOM one time to reduce dom operation.
      this.$client[0].appendChild(this.ofragment);

      // Not drawing node when create node, so this need to draw it.
      for (var nid in this.nodes) {
        if (this.nodes[nid].isShow) {
          this.nodes[nid].draw(this.nodes[nid].context, this.nodes[nid].options);
          // 原子添加认证图标
          addIcon(this.nodes[nid]);
        }
      }

      this.ofragment = null;
    },
    /**
     * Init or update paths.
     */
    updatePaths: function(paths, clientBounds) {
      // DocumentFragment for collecting paths
      this.ofragment = document.createDocumentFragment();

      for (var i = 0; i < paths.length; ++i) {
        var options = paths[i];
        // diff start from BME3.0
        // reason : the datasource from java parsed may cotain null value
        for (var key in options) {
          if (options[key] == null) {
            delete options[key];
          }
        }
        // diff end
        var path = this.paths[options.id];
        if (path) {
          // For undo redo initialize zoom scale
          this.zoomargs = options.zoomScale = options.zoomScale || 1;
          this.paths[options.id].update(options);
        } else {
          path = this.createPath(options, null, clientBounds);
          options.id = path.id;
        }

        this.setMaxBounds(options.id, path.getBounds());
      }

      this.$client[0].appendChild(this.ofragment);

      for (var pid in this.paths) {
        this.paths[pid].draw(this.paths[pid].context, this.paths[pid].options);
      }

      this.ofragment = null;
    },
    /**
     * Update page_overflow_indicator position for fixed scrollbar.
     * TODO refactor for performance(reduce calculation and css operation).
     */
    updateMaxBounds: function() {
      this.maxBounds = {
        xId: '',
        xValue: 0,
        yId: '',
        yValue: 0,
      };

      // Get the max value of right and bottom.
      for (var nid in this.nodes) {
        var nbounds = this.nodes[nid].getBounds();
        this.setMaxBounds(nid, nbounds);
      }

      for (var pid in this.paths) {
        var pbounds = this.paths[pid].getBounds();
        this.setMaxBounds(pid, pbounds);
      }

      this.$client.children('.uee-overflow-indicator').css({
        left: this.maxBounds.xValue,
        top: this.maxBounds.yValue,
      });
    },
    /**
     * Update maxBounds when creating node.
     */
    updateMaxBoundsByCreate: function(id, nodeBounds) {
      var isChanged = this.setMaxBounds(id, nodeBounds);
      isChanged && this.$client.children('.uee-overflow-indicator').css({
        left: this.maxBounds.xValue,
        top: this.maxBounds.yValue,
      });
    },
    /**
     * Update maxBounds value.
     */
    setMaxBounds: function(id, bounds) {
      if (!bounds) { // User invoke update of diagram and path is lack of source or target.
        // Then path.getBounds() will be underfined.
        return false;
      }

      var x = bounds.left + bounds.width;
      var y = bounds.top + bounds.height;
      var isChanged = false;

      if (x > this.maxBounds.xValue) {
        this.maxBounds.xId = id;
        this.maxBounds.xValue = x;
        isChanged = true;
      }

      if (y > this.maxBounds.yValue) {
        this.maxBounds.yId = id;
        this.maxBounds.yValue = y;
        isChanged = true;
      }

      return isChanged;
    },
    /**
     * To create the bounds by the length of title and the order in the flow.
     * @param row
     * @param title
     * @return
     */
    createBounds: function(row, title) {
      var bounds = {};
      bounds.width = Math.ceil(title.length / 6) * 40;
      var jDiagramWidth = Math.floor(this.$box.width() / 9);
      bounds.left = Math.floor(jDiagramWidth * row + 0.5 * jDiagramWidth) - Math.max(20, bounds.width / 2);
      bounds.top = Math.floor(0.5 * this.$box.height());
      return bounds;
    },
    /**
     * To create the jType, anchor, controls of a path
     * @param json
     * @return
     */
    ceatePathType: function(option) {
      var pathOption;

      if (option) {
        pathOption = {
          type: 'elbowDoub',
          sourceAnchor: 1,
          targetAnchor: 5,
          controls: [0.0017, 1.9, 0.9986, 1.9],
        };
      } else {
        pathOption = {
          type: 'elbowDoubOpp',
          sourceAnchor: 5,
          targetAnchor: 1,
          controls: [0.0017, 1.9, 0.9986, 1.9],
        };
      }
      return pathOption;
    },
    /**
     * for undo redo
     * @param json
     */
    updateOuter: function(json) {
      var jDiagram = this;
      this.ofragment = document.createDocumentFragment();
      for (var i = 0; i < json.nodes.length; ++i) {
        var options = json.nodes[i];
        if (jDiagram.nodes[options.id]) {
          jDiagram.nodes[options.id].options.group = [];
          // For undo redo initialize zoom scale
          this.zoomargs = options.zoomScale = options.zoomScale || 1;
          jDiagram.nodes[options.id].update(options, null, false);
        } else {
          jDiagram.createNodeOuter(options, 'undoredo');
        }
      }
      this.$client[0].appendChild(this.ofragment);
      for (var nid in this.nodes) {
        this.nodes[nid].draw(this.nodes[nid].context, this.nodes[nid].options);
      }
      this.ofragment = null;

      this.ofragment = document.createDocumentFragment();
      for (var i = 0; i < json.paths.length; ++i) {
        var options = json.paths[i];
        if (jDiagram.paths[options.id]) {
          // For undo redo initialize zoom scale
          this.zoomargs = options.zoomScale = options.zoomScale || 1;
          jDiagram.paths[options.id].update(options);
        } else {
          jDiagram.createPathOuter(options, 'undoredo');
        }
      }
      this.$client[0].appendChild(this.ofragment);
      for (var pid in this.paths) {
        this.paths[pid].draw(this.paths[pid].context, this.paths[pid].options);
      }
      this.ofragment = null;
    },
    /**
     * for undo redo
     */
    restate: function(json) {
      // for restore nodes state,get node for delete
      var oriNodeLength = 0;
      for (var orinode in this.nodes) {
        oriNodeLength = oriNodeLength + 1;
      }

      if (oriNodeLength > json.nodes.length) {
        var deletenodes = [];
        for (var orinode in this.nodes) {
          for (var i = 0; i <= json.nodes.length; i++) {
            if (json.nodes[i] && orinode == json.nodes[i].id) {
              break;
            }
            if (json.nodes.length == 0 || i == json.nodes.length - 1) {
              deletenodes.push(orinode);
            }
          }
        }

        // delete nodes
        for (var i = 0; i < deletenodes.length; i++) {
          this.remove(this.nodes[deletenodes[i]], 'undoredo');
        }
      }

      // for restore paths state,get path for delete
      var oriPathLength = 0;
      for (oripath in this.paths) {
        oriPathLength = oriPathLength + 1;
      }

      if (oriPathLength > json.paths.length) {
        var deletepaths = [];
        for (var oripath in this.paths) {
          for (var i = 0; i <= json.paths.length; i++) {
            if (json.paths[i] && oripath == json.paths[i].id) {
              break;
            }
            if (json.paths.length == 0 || i == json.paths.length - 1) {
              deletepaths.push(oripath);
            }
          }
        }

        // delete paths
        for (var i = 0; i < deletepaths.length; i++) {
          this.remove(this.paths[deletepaths[i]], 'undoredo');
        }
      }
      this.updateOuter(json);
    },
    /**
     * When we create node,whether this node is in container.
     * If this node is in many container, we get the last one.
     * Priority level of swimlane is the lowest that if swimlane is the last container, we get previous one.
     * Return : this node's container.
     */
    isContained: function(jElement) {
      var container = [];
      if (jElement && jElement.isnode) {
        for (var cid in this.nodes) {
          if (cid != jElement.id && this.nodes[cid].iscontainer) { // This node in container.
            var contains = Utils.containsBound(this.nodes[cid].getBounds(), jElement.getBounds());
            contains && container.push(this.nodes[cid]);
          }
        }
      }

      if (container.length == 0) { // This node is not in container
        return null;
      } else if (container.length == 1) { // This node is only in one container
        return container[0];
      } else { // This node is in many containers.
        if (container[container.length - 1].jType.name == 'swimlane') { // The priority of swimlane is lowest.
          return container[container.length - 2];
        }
        return container[container.length - 1];
      }
    },
    /**
     * Use to drag some nodes or paths out of container.
     * When they are out of container, their relationship with parent is break.
     */
    leaveContainer: function() {
      for (var sid in this.selections) {
        this.removeFamily(this.selections[sid]);
      }
    },
    /**
     * When create container,we will hold the nodes whose place is in this container.
     * @param : new container
     * @return : the nodes are covered
     */
    swallowBounds: function(jContainer) {
      var subnodes = {};
      if (jContainer && jContainer.index() != -1) { // This is container
        var containerBounds = jContainer.getBounds();
        var nodeBounds = {};
        for (var nid in this.nodes) {
          if (jContainer.id != nid && !this.nodes[nid].options.parent) {
            nodeBounds = this.nodes[nid].getBounds();
            if (Utils.containsBound(containerBounds, nodeBounds)) {
              subnodes[nid] = this.nodes[nid];
            }
          }
        }
      }

      return subnodes;
    },
    /**
     * All selections join in one group.
     */
    checkGroup: function() {
      // Remove all lines.
      var gnodes = {};
      for (var id in this.getIndependentSelections(function() {
        return this.isnode;
      })) {
        gnodes[id] = this.selections[id];
      }

      // Whether all group name is the same.
      var isSame = true;
      var isMulti = 0;
      var refGroup = '';
      for (var rid in gnodes) {
        if (gnodes[rid].options.group.length > 0) { // Set a reference group name
          refGroup = gnodes[rid].options.group[gnodes[rid].options.group.length - 1];
        }
        isMulti++;
      }
      if (isMulti < 2) {
        return false;
      }
      if (refGroup != '') {
        for (var said in gnodes) {
          if (gnodes[said].options.group.length == 0 || (gnodes[said].options.group.length > 0 &&
            (gnodes[said].options.group[gnodes[said].options.group.length - 1] != refGroup))) { // There are difference
            isSame = false;
            break;
          }
        }
        if (isSame) {
          return false;
        }
      }
      return gnodes;
    },
    /**
     * All selections join in one group.
     */
    joinGroup: function() {
      if ($.isEmptyObject(this.selections)) {
        return;
      }

      var gnodes = this.checkGroup();
      if (!gnodes) {
        return;
      }

      // Set group name
      var groupName = '';
      for (var sid in gnodes) {
        groupName = groupName.concat('G', sid);
        break;
      }

      // Push group name
      for (var nid in gnodes) {
        gnodes[nid].options.group.push(groupName);
      }

      // Save a json for undo and redo.
      this.syncJson();
    },
    /**
     * Remove group according to selections.
     */
    removeGroup: function() {
      if ($.isEmptyObject(this.selections)) {
        return;
      }

      // Remove all lines.
      var gnodes = {};
      for (var id in this.getIndependentSelections(function() {
        return this.isnode;
      })) {
        gnodes[id] = this.selections[id];
      }

      // Pop groupname from group array.
      for (var gid in gnodes) {
        if (gnodes[gid].options.group.length > 0) {
          gnodes[gid].options.group.pop();
        }
      }

      // Save a json for undo and redo.
      this.syncJson();
    },
    /**
     * Set family relationship of container and subnode.
     */
    joinFamily: function(jElement, jContainer) {
      if (jElement && jContainer && jContainer.canContain(jElement.options.name)) {
        // Set jElement's parent and jContainer's children.
        jElement.addParent(jContainer);
        jContainer.addChild(jElement);
      }
    },
    /**
     * Remove family relationship of container and subnode.
     */
    removeFamily: function(jElement) {
      if (jElement && jElement.isnode && jElement.options.parent && $.trim(jElement.options.parent) != '') { // Delete parent's children first
        var parent = this.nodes[jElement.options.parent];
        parent.removeChild(jElement);
        jElement.removeParent();
      }
    },
    undo: function() {
      if (this.currentjsonflag && this.currentjsonflag - 1 > 0) {
        var currentJson = this.json[this.currentjsonflag - 2];
        this.currentjsonflag = this.currentjsonflag - 1;
        if (typeof (currentJson) == 'object') {
          // For zoom to last step.
          var nodes = currentJson.nodes;
          for (var index = 0; index < nodes.length; index++) {
            var figureStyle = nodes[index].figures && nodes[index].figures[0] && nodes[index].figures[0].style;
            if (figureStyle && nodes[index].zoomScale && nodes[index].zoomScale != 1) {
              var bounds = Utils.getStyle(figureStyle);
              var style = '';
              for (var key in bounds) {
                style += key + ':' + bounds[key] / nodes[index].zoomScale + 'px;';
              }
              nodes[index].figures[0].style = style;
            }
          }
          this.restate(currentJson);
        } else {
          this.restate(jQuery.parseJSON(currentJson));
        }
      }
    },
    redo: function() {
      if (this.currentjsonflag && this.currentjsonflag < this.json.length) {
        var currentJson = this.json[this.currentjsonflag];
        this.currentjsonflag = this.currentjsonflag + 1;
        this.restate(jQuery.parseJSON(currentJson));
      }
    },
    /**
     * add vertical || horizontal center point
     * @param collection : object {}
     * @param key : number of vertical or horizontal center point
     * collection[key] = undefined, 1, 2,.... n;
     */
    addCenterPoint: function(collection, key) {
      if (!collection[key]) {
        collection[key] = 1;
      } else {
        collection[key]++;
      }
    },
    /**
     * remove vertical || horizontal center point
     * @param collection : object {}
     * @param key : number of vertical or horizontal center point
     */
    removeCenterPoint: function(collection, key) {
      if (collection[key] == 1) {
        delete collection[key];
      } else {
        collection[key]--;
      }
    },
    /**
     * Whether show vertical line or horizontal line
     * @param bound : bound of dragging node
     */
    snapLineManager: function(bound) {
      var centerX = bound.left + bound.width / 2;
      var centerY = bound.top + bound.height / 2;
      if (this.hcenters[centerX]) {
        this.showVerticalLine(centerX);
      } else {
        this.removeVerticalLine();
      }
      if (this.vcenters[centerY]) {
        this.showHorizontalLine(centerY);
      } else {
        this.removeHorizontalLine();
      }
    },
    /**
     * show vertical line for aligning
     * @param centerX : number
     */
    showVerticalLine: function(centerX) {
      if (this.$verticalLine == undefined) {
        this.$verticalLine = jQuery(
          ['<div style="position:absolute;left:', centerX - this.$client.scrollLeft(), 'px;top:0px;width:1px;height:auto;bottom:0px;background:none repeat scroll 0% 0% rgb(0, 153, 255);z-index:10;">', '</div>'].join(''));
        this.$box.append(this.$verticalLine);
      }
    },
    /**
     * remove vertical line
     */
    removeVerticalLine: function() {
      this.$verticalLine && this.$verticalLine.remove();
      this.$verticalLine = undefined;
    },
    /**
     * show horizontal line for aligning
     * @param centerY : number
     */
    showHorizontalLine: function(centerY) {
      if (this.$horizontalLine == undefined) {
        this.$horizontalLine = jQuery(
          ['<div style="position:absolute;left:0px;top:', centerY - this.$client.scrollTop(), 'px;width:auto;height:1px;right:0px;background:none repeat scroll 0% 0% rgb(0, 153, 255);z-index:10;">', '</div>'].join(''));
        this.$box.append(this.$horizontalLine);
      }
    },
    /**
     * remove horizontal line
     */
    removeHorizontalLine: function() {
      this.$horizontalLine && this.$horizontalLine.remove();
      this.$horizontalLine = undefined;
    },
    /**
     * zoom all nodes and paths
     * @param zoomin : zoom = zoomList + 1
     *    zoomout : zoom = zoomList - 1
     *    4    : zoom = zoomList's value = 4
     */
    zoom: function(zoomargs) {
      if (zoomargs == 'zoomin' && this.zoomIndex + 1 < this.zoomList.length) {
        var newIndex = $.inArray(this.zoomargs, this.zoomList);
        this.zoomIndex = newIndex == -1 ? ++this.zoomIndex : ++newIndex;
        this.zoomargs = this.zoomList[this.zoomIndex];
      } else if (zoomargs == 'zoomout' && this.zoomIndex - 1 >= 0) {
        var newIndex = $.inArray(this.zoomargs, this.zoomList);
        this.zoomIndex = newIndex == -1 ? --this.zoomIndex : --newIndex;
        this.zoomargs = this.zoomList[this.zoomIndex];
      } else if ((typeof zoomargs == 'number' && zoomargs >= 0) ||
        !isNaN(parseFloat(zoomargs, 10))) {
        this.zoomargs = parseFloat(zoomargs, 10);
        for (var i = 0; i < this.zoomList.length; i++) { // set zoomIndex
          if (this.zoomList[i] > zoomargs) {
            this.zoomIndex = i;
            break;
          }
        }
      } else {
        return 'Parameters do not match';
      }

      // Zoom nodes
      for (var nid in this.nodes) {
        this.nodes[nid].zoom(this.zoomargs);
      }

      // Zoom paths
      for (var pid in this.paths) {
        this.paths[pid].zoom(this.zoomargs);
      }

      this.updateMaxBounds();

      jBME.Diagram.DOMCache.prototype.updateWindow(null, null, this);

      this.syncJson();

      // User may click button to zoom, so need to focus for undo and redo.
      this.$box.focus();
    },
    /**
     * Sets or gets the fullscreen state.
     *
     * @param {boolean=} state
     *  True to enable fullscreen mode, false to disable it. If not
     *  specified then the current fullscreen state is returned.
     * @return {boolean|null}
     *  True is returned when browser is currently in full screen mode.
     *  False is returned if browser is not in full screen mode.
     *  Null is returned if browser doesn't support fullscreen mode at all.
     */
    fullScreen: function(state) {
      var ele = this.$box[0];
      var dgm; var fsFunc;
      var frame = window.frameElement;

      // Fix the problem with iframe
      if (frame) {
        if (!frame.hasAttribute('webkitAllowFullScreen')) {
          $(frame).attr('webkitAllowFullScreen', true);
        }
        if (!frame.hasAttribute('mozAllowFullScreen')) {
          $(frame).attr('mozAllowFullScreen', true);
        }
        if (!frame.hasAttribute('allowFullScreen')) {
          $(frame).attr('allowFullScreen', true);
        }
      }

      // Find the real element and the document (Depends on whether the
      // document itself or a HTML element was selected)
      if (ele.ownerDocument) {
        dgm = ele.ownerDocument;
      } else {
        dgm = ele;
        ele = dgm.documentElement;
      }

      // When no state was specified then return the current state.
      if (state == null) {
        // do not supported then return null
        if (!(dgm['cancelFullScreen'] || dgm['webkitCancelFullScreen'] || dgm['mozCancelFullScreen'])) {
          return null;
        }

        // return fullscreen state : true or false
        state = Boolean(dgm['fullScreen']) || Boolean(dgm['webkitIsFullScreen']) || Boolean(dgm['mozFullScreen']);
        return state;
      }

      // When state was specified then enter or exit fullscreen mode.
      if (state) { // Enter fullscreen
        fsFunc = ele['requestFullScreen'] || ele['webkitRequestFullScreen'] || ele['mozRequestFullScreen'];
        if (fsFunc) {fsFunc.call(ele, Element['ALLOW_KEYBOARD_INPUT']);}
      } else { // Exit fullscreen
        fsFunc = dgm['cancelFullScreen'] || dgm['webkitCancelFullScreen'] || dgm['mozCancelFullScreen'];
        if (fsFunc) {fsFunc.call(dgm);}
      }
    },
    /**
     * Toggles the fullscreen mode.
     */
    toggleFullScreen: function() {
      return this.fullScreen(!this.fullScreen());
    },
    /**
     * customize method , support this interface for user
     */
    afterCreate: function() {
    },
    // User implements.
    custom: $.noop,
  };

  // 原子添加认证图标
  function addIcon(node) {
    if (node) {
      if (node.options && node.options.data && (node.options.data.source === 'hwWithSign')) {
        setTimeout(function() {
          var elements = document.getElementById(node.id);
          // 判断元素中是否已包含认证图标，增加后childElementCount为4，防止重复添加图标
          if (elements.classList.contains('roundrect') && elements.childElementCount < 4) {
            var newLi = document.createElement('div');
            newLi.innerHTML = '<div style=\'position: absolute;top: -12px;left:-16px\'><img src=\'/dvihealingwebsite/images/healing/trustworthy.png\' /></div>';
            elements.append(newLi);
          }
        }, 100);
      }
    }
  }

  /**
   * Node.
   */
  jBME.Node = jBME.Clazz.extend({
    construct: function(id, options /* [optinal]*/, $nodebox, zoomargs) {
      // Cache.
      this.id = id;
      this.$box = $nodebox || $(document.getElementById(this.id));

      // To support IE 7,8,because the element of canvas created by jQuery is not known in IE 7,8,
      // so we create the element of canvas by the method of HTML.
      if (!Utils.isSupportCanvas()) {
        var canvas = document.createElement('canvas');
        this.$box[0] && this.$box[0].replaceChild(canvas, this.$box.children('canvas')[0]);
      }

      this.$canvas = this.$box.children('canvas');
      this.$adapter = this.$box.children('.uee-node-adapter');
      this.canvas = this.$canvas[0];

      this.paths = {};
      this.bounds = {};
      this.isDOMFigure = [];
      this.parent = null;

      // Pasted node's options is with default options.
      this.options = (options && options.isPasted) ?
        options : $.extend(true, {}, jBME.Diagram.options.node, options);

      options && (this.options = this.mergeOptions(options, this.options));
      this.disabled = this.options.disabled === true;
      this.options.zoomScale = zoomargs || 1;

      var type = Utils._getType(this.options);
      this.jType = jBME.Node.Type[type] || jBME.Node.Type['default'];

      if (this.disabled) {
        this.options.viewmode = 'view';
        this.$box.addClass('disabled');
      }

      if (this.canvas) {
        this.setBounds(Utils.getStyle(this.options.figures[0].style));
        this.updateBounds(null, $nodebox);
      }
    },
    isnode: true,
    /**
     * Min value of width
     */
    minWidth: 10,
    /**
     * Min value of height
     */
    minHeight: 10,
    /**
     * @param options
     */
    update: function(options, isNotDraw, isUpdatePath) {
      if (options) {
        this.options = this.mergeOptions(options, this.options);
        this.options = $.extend(true, this.options, options);
        this.options = this.mergeOptions(options, this.options);
      }

      var type = Utils._getType(this.options);
      this.jType = jBME.Node.Type[type] || jBME.Node.Type['default'];

      var figureStyle = options && (Utils._getBounds(options));
      var bounds = Utils.getStyle(figureStyle);

      if (options) {
        // update title pre
        if (options.title) {
          this.title(options.title, null);
        }

        // update disabled
        if (typeof options.disabled == 'boolean') {
          this.toggleDisabled(options.disabled);
        }

        // update DOM figure
        if (options.figures && options.figures.length > 1) {
          this.updateDOMFigure(options.figures);
        }

        // update zoom scale
        if (options.zoomScale) {
          for (var key in bounds) {
            bounds[key] = bounds[key] * options.zoomScale;
          }
        }
      }

      this.canvas && this.updateBounds(bounds, isNotDraw, isUpdatePath);
    },
    /**
     * @param options
     */
    updateAnchorNode: function(opts) {
      for (var i = 0; i < this.options.anchornodes.length; i++) {
        if (this.options.anchornodes[i].id == opts.id) {
          this.options.anchornodes[i] = opts;
          break;
        }
      }

      var anchornodeopts = this.anchornodes[opts.id];
      if (anchornodeopts) {
        opts.position && (anchornodeopts.position = opts.position);
        opts.proportionx && (anchornodeopts.proportionx = opts.proportionx);
        opts.proportiony && (anchornodeopts.proportiony = opts.proportiony);
        opts.options && anchornodeopts.instance.update(opts.options);
      }

      this.updateBounds();
    },
    /**
     * Update DOM figure
     * Support the add and remove.
     * @param : all figures
     */
    updateDOMFigure: function(figures) {
      var deletedNum = 0;

      for (var i = 1, len = figures.length; i <= len; i++) {
        if (figures[i] && this.isDOMFigure[i - deletedNum] == true) {
          // DOM object's index is always 0;
          $(this.$figure.children()[0]).remove();

          if ($.isEmptyObject(figures[i])) { // {} means delete this figure.
            this.options.figures.splice(i - deletedNum, 1);
            this.isDOMFigure.splice(i - deletedNum, 1);
            deletedNum++;
          } else { // Current dom has been drawed, delete it first and it will be drawed again.
            this.isDOMFigure[i - deletedNum] = false;
          }
        }
      }
    },
    /**
     * Update the bounds {|left,|top,|width,|height}, Redraw is needed if [width|height] changed.
     */
    updateBounds: function(bounds /* [optional]*/, isNotDraw, isUpdatePath, isZoom) {
      bounds && this.setBounds(bounds, isZoom);
      this.optimizeBounds();

      if (!bounds || $.isEmptyObject(bounds) || (bounds && (
        (typeof bounds.width == 'number' && bounds.width >= 0) ||
        (typeof bounds.height == 'number' && bounds.height >= 0)))) {
        // Considering the zoom
        var outsideBounds = this.getBounds();
        var margin = this.options.margin * this.options.zoomScale;
        this.canvas.width = outsideBounds.width + 2 * margin;
        this.canvas.height = outsideBounds.height + 2 * margin;
        this.$canvas.css('margin', -margin);

        this.canvasManager(this.canvas);

        // We don't need to draw again when DOMFragment initialize
        // and drag node without changing width and height.
        !isNotDraw && this.draw();
      }

      // Update paths bounds connected to this node.
      var updatePath = isUpdatePath != undefined ? isUpdatePath : true;
      if (updatePath) {
        for (var id in this.paths) {
          this.paths[id].updateBounds();
        }
      }

      // Update anchornodes bounds connected to this node.
      for (var id in this.anchornodes) {
        var anchornode = this.anchornodes[id].instance;
        var pos = this.anchornodes[id].position;
        var newposition = {
          left: this.anchornodes[id].proportionx ? this.bounds.width * pos[0] : pos[0],
          top: this.anchornodes[id].proportiony ? this.bounds.height * pos[1] : pos[1],
        };
        anchornode.updateBounds(newposition);
      }

      return this;
    },
    /**
     * Set node's cache of left, top, width, height.
     * @param bounds : {left:xxx, top:xxx, width:xxx, height:xxx}
     */
    setBounds: function(bounds, isZoom) {
      if (bounds) {
        // Update DOM style.
        this.$box.css(bounds);

        // Update bounds cache of node.
        if (typeof bounds.left == 'number' && !isZoom) {
          this.bounds.left = Math.max(ZERO, bounds.left / this.options.zoomScale);
        }

        if (typeof bounds.top == 'number' && !isZoom) {
          this.bounds.top = Math.max(ZERO, bounds.top / this.options.zoomScale);
        }

        if (typeof bounds.width == 'number') {
          if (!isZoom) {
            this.bounds.width = Math.max(ZERO, bounds.width / this.options.zoomScale);
          }
          this.width = Math.max(ZERO, bounds.width);
        }

        if (typeof bounds.height == 'number') {
          if (!isZoom) {
            this.bounds.height = Math.max(ZERO, bounds.height / this.options.zoomScale);
          }
          this.height = Math.max(ZERO, bounds.height);
        }
      }
    },
    /**
     * Make sure width and height is not undefined.
     */
    optimizeBounds: function() {
      if (!this.width) {
        this.width = Math.max(ZERO, this.$box.width());
      }

      if (!this.height) {
        this.height = Math.max(ZERO, this.$box.height());
      }

      if (!this.bounds.width) {
        this.bounds.width = Math.max(ZERO, this.$box.width() / this.options.zoomScale);
      }

      if (!this.bounds.height) {
        this.bounds.height = Math.max(ZERO, this.$box.height() / this.options.zoomScale);
      }
    },
    canvasManager: function(canvas) {
      // To support IE 7,8,because the context of the canvas does not initialize the element by dynamic creating,so we need to initialize.
      if (canvas && !canvas.getContext) {
        window.G_vmlCanvasManager.initElement(canvas);
      }
      if (canvas && canvas.getContext && !this.context) {
        this.context = canvas.getContext('2d');
      }
      if (Utils.isUseExcanvas()) {
        canvas.firstChild.style.width = canvas.width;
        canvas.firstChild.style.height = canvas.height;
      }
      return canvas;
    },
    updateTextNodeOptions: function(scale) {
      this.options.onpath.scale = scale;
    },
    /**
     * Return the bounds[left,top,width,height] relative to the given container or the offsetParent by default.
     */
    getBounds: function($relativeto /* [optional]*/) {
      if ($relativeto) { // For marquee : the node relative diagram's offset.
        var c = $relativeto.offset();
        var b = this.$box.offset();
        return {
          left: b.left - c.left,
          top: b.top - c.top,
          width: this.width,
          height: this.height,
        };
      } else {
        var zoomScale = this.options.zoomScale;
        return {
          left: this.bounds.left * zoomScale,
          top: this.bounds.top * zoomScale,
          width: this.bounds.width * zoomScale,
          height: this.bounds.height * zoomScale,
        };
      }
    },
    /**
     * add parent to this node
     * @param jElement : this node's parent container
     */
    addParent: function(jElement) {
      this.options.parent = jElement.id;
      this.parent = jElement;
    },
    removeParent: function() {
      this.options.parent = '';
      this.parent = null;
    },
    /**
     * Return the closest node anchor from the given position relative to diagram.
     */
    getClosestAnchorFromPosition: function(position) {
      var pos = this.getAnchorPosition(0);
      var x = position.left - pos.left;
      var y = position.top - pos.top;
      var relativeHeight = Math.abs(this.getAnchorPosition(5).top - this.getAnchorPosition(1).top);
      var relativeWidth = Math.abs(this.getAnchorPosition(3).left - this.getAnchorPosition(7).left);

      var relativex = x / relativeWidth + this.jType.anchors[0][0];
      var relativey = y / relativeHeight + this.jType.anchors[0][1];

      var base = (Math.round(4 * (Math.atan2(-x * relativeHeight / relativeWidth, y) / Math.PI + 1)) % 8 + 1);
      var baseAnchor = this.jType.anchors[base];

      var closetid = base;
      var closetdis = Math.pow((baseAnchor[0] - relativex), 2) + Math.pow((baseAnchor[1] - relativey), 2);

      for (var id in this.anchornodes) {
        var anchornode = this.anchornodes[id];
        var posx = anchornode.proportionx ? anchornode.position[0] : (anchornode.position[0] / relativeWidth);
        var posy = anchornode.proportiony ? anchornode.position[1] : (anchornode.position[1] / relativeHeight);
        var nowdis = Math.pow((posx - relativex), 2) + Math.pow((posy - relativey), 2);
        if (nowdis < closetdis) {
          closetid = id;
          closetdis = nowdis;
        }
      }

      return closetid;
    },
    /**
     * Return the given node anchor(number)'s position relative to the node container(diagram or another node).
     * TODO problem: node in node?
     */
    getAnchorPosition: function(anchorid, position /* [optional]When anchorid is 'auto'*/) {
      if (anchorid == 'auto') {
        anchorid = this.getClosestAnchorFromPosition(position);
      }
      var anchornodepos;
      this.anchornodes && this.anchornodes[anchorid] && (anchornodepos = this.anchornodes[anchorid].position);
      var anchorXY = this.jType.anchors[anchorid] || anchornodepos || this.jType.anchors[0];
      var jNodeBound = this.getBounds();
      var pos = {
        left: jNodeBound.left,
        top: jNodeBound.top,
      };
      if (typeof (this.jType.customAnchor) == 'function') { // If special nodetype has custom anchor
        pos = this.jType.customAnchor.apply(this, arguments);
      } else if (this.anchornodes && this.anchornodes[anchorid]) {
        var anchornodeopts = this.anchornodes[anchorid];
        var relativex = anchornodeopts.proportionx ? Math.round(this.width * anchorXY[0]) : Math.round(anchorXY[0]);
        var relativey = anchornodeopts.proportiony ? Math.round(this.height * anchorXY[1]) : Math.round(anchorXY[1]);
        pos.left += relativex;
        pos.top += relativey;
        pos.anchorid = anchorid;
        var anchornodebounds = anchornodeopts.instance.getBounds();

        if (relativey >= this.height || (relativey >= (this.height - anchornodebounds.height) && relativex > 0 && relativex < (this.width - anchornodebounds.width))) {
          // close to bottom
          pos.left += Math.round(anchornodebounds.width / 2);
          pos.top += Math.round(anchornodebounds.height);
        } else if (relativey <= -anchornodebounds.height || (relativey <= 0 && relativex > 0 && relativex < (this.width - anchornodebounds.width))) {
          // close to top
          pos.left += Math.round(anchornodebounds.width / 2);
        } else if (relativex > anchornodebounds.width / 2) {
          // close to right
          pos.left += Math.round(anchornodebounds.width);
          pos.top += Math.round(anchornodebounds.height / 2);
        } else {
          // close to left
          pos.top += Math.round(anchornodebounds.height / 2);
        }
      } else {
        pos.anchorid = anchorid;
        pos.left += Math.round(this.width * anchorXY[0]);
        pos.top += Math.round(this.height * anchorXY[1]);
      }
      return pos;
    },
    /**
     * Return the given node anchor(number)'s position with scrollLeft and scrollTop.
     * @param anchorid
     * @param position
     * @returns posWithScroll
     */
    getAnchorPositionScroll: function(anchorid, position /* [optional]When anchorid is 'auto'*/) {
      var pos = this.getAnchorPosition(anchorid, position);
      var container = this.$box.parent();
      var posWithScroll = {
        left: pos.left + container.scrollLeft(),
        top: pos.top + container.scrollTop(),
      };
      return posWithScroll;
    },
    /**
     * Active Anchors: for paths connected anchor or
     */
    getActiveAnchors: function() {
      this.activeAnchors = {};
      for (var id in this.paths) {
        var jPath = this.paths[id];
        if (jPath.isselected) {
          var anchorId = (jPath.jSource == this ? jPath.sourceAnchorid : jPath.targetAnchorid);
          this.activeAnchors[anchorId] = true;
        }
      }
      if (this.hoverAnchor != null) {
        this.activeAnchors[this.hoverAnchor] = true;
      }
      return this.activeAnchors;
    },
    getCenterPosition: function() {
      return this.getAnchorPosition(0);
    },
    /**
     * Return the position relative to the node's offset container(diagram or parent node),
     *   by the give offset relative to the document(page)
     */
    getPositionFromOffset: function(pageEvent) {
      var offset = this.$box.parent().offset();
      var offLeft = offset ? offset.left : 0;
      var offTop = offset ? offset.top : 0;
      return {
        left: pageEvent.pageX - offLeft,
        top: pageEvent.pageY - offTop,
      };
    },
    /**
     * Return the position relative to the node's offset container(diagram or parent node),
     *   by the give offset + scrollLeft|scrollTop relative to the document(page)
     */
    getPositionWithScroll: function(pageEvent) {
      var boxParent = this.$box.parent();
      var offset = boxParent.offset();
      var offLeft = offset ? offset.left : 0;
      var offTop = offset ? offset.top : 0;
      return {
        left: pageEvent.pageX + boxParent.scrollLeft() - offLeft,
        top: pageEvent.pageY + boxParent.scrollTop() - offTop,
      };
    },
    /**
     * return : the array of next node
     * eg: A->B A.getNextNode = B
     */
    getNextNode: function() {
      var nextNode = [];
      if (!this.anchornodes) {
        for (var pid in this.paths) {
          if (this.id == this.paths[pid].jSource.id) {
            nextNode.push(this.paths[pid].jTarget);
          }
        }
      } else {
        for (var i = 0; i < this.options.anchornodes.length; i++) {
          var children = this.getNextNodeByAnchor(this.options.anchornodes[i].id);
          nextNode = nextNode.concat(children);
        }
      }
      return nextNode;
    },
    /**
     * return : the array of next node
     * eg: A->B A.getNextNode = B
     */
    getNextNodeByAnchor: function(anchorid) {
      var nextNode = [];
      for (var pid in this.paths) {
        if (this.id == this.paths[pid].jSource.id && anchorid == this.paths[pid].sourceAnchorid) {
          nextNode.push(this.paths[pid].jTarget);
        }
      }
      return nextNode;
    },
    /**
     * return : the array of previous node
     * eg: A->B B.getPrevNode = A
     */
    getPrevNode: function() {
      var prevNode = [];
      for (var pid in this.paths) {
        if (this.id == this.paths[pid].jTarget.id) {
          prevNode.push(this.paths[pid].jSource);
        }
      }
      return prevNode;
    },
    /**
     * To use old options and new options together.
     * Modified at 2013/2/28
     */
    mergeOptions: function(orgnlOpts, mergeOpts) {
      // merget context
      if (orgnlOpts.normal) {
        if (!(orgnlOpts.figures && orgnlOpts.figures[0] && orgnlOpts.figures[0].context)) {
          $.extend(mergeOpts.figures[0].context, orgnlOpts.normal);
        }
      }

      // merge type
      if (orgnlOpts.type) {
        if (!(orgnlOpts.figures && orgnlOpts.figures[0] && orgnlOpts.figures[0].type)) {
          mergeOpts.figures[0].type = orgnlOpts.type;
        }
      }

      // merge bounds
      if (orgnlOpts.bounds) {
        if (!(orgnlOpts.figures && orgnlOpts.figures[0] && orgnlOpts.figures[0].style)) {
          mergeOpts.figures[0].style = orgnlOpts.bounds;
        }
      }

      // merge image src
      if (orgnlOpts.imageSrc) {
        if (!(orgnlOpts.figures && orgnlOpts.figures[0] && orgnlOpts.figures[0].options && orgnlOpts.figures[0].options.src)) {
          mergeOpts.figures[0].options.src = orgnlOpts.imageSrc;
        }
      }

      return mergeOpts;
    },
    /**
     * Drag textnode on its path.
     * @param jPath : textnode's path
     * @param event : mousemove event
     * @returns newBound : the bound after drag
     */
    moveTextNode: function(jPath, event) {
      var eventBounds = this.getPositionWithScroll(event);
      var closestPoint = jPath.getClosestPointFromPath(eventBounds.left, eventBounds.top);
      this.updateTextNodeOptions(closestPoint[2]);
      var newBound = {
        left: (closestPoint[0] - this.width / 2),
        top: (closestPoint[1] - this.height / 2),
      };
      return newBound;
    },
    /**
     * Resize textnode on its path.
     * @param oldBound
     * @param newBound
     * @returns newBound : the bound after resize
     */
    resizeTextNode: function(oldBound, newBound) {
      var newLeft = (newBound.left || oldBound.left) - ((newBound.left || oldBound.left) + (newBound.width || oldBound.width) / 2 - oldBound.left - oldBound.width / 2);
      var newTop = (newBound.top || oldBound.top) - ((newBound.top || oldBound.top) + (newBound.height || oldBound.height) / 2 - oldBound.top - oldBound.height / 2);
      newBound = {
        left: newLeft,
        top: newTop,
        width: newBound.width,
        height: newBound.height,
      };
      return newBound;
    },
    /**
     * Paste node
     * @param offsetDif : the distance of horizontal excursion
     * @param pasteTimes : the times of node paste
     * @returns newOpts : the new options of node to paste
     */
    onPaste: function(offsetDif, pasteTimes, groupTime, pasteOffset) {
      // Clear information of copy selections
      var newOpts = $.extend(true, {}, this.options);
      newOpts.id = null; // TODO children parent and group
      newOpts.children = [];
      newOpts.parent = '';
      newOpts.isPasted = true;

      for (var i = 0; i < newOpts.group.length; i++) {
        newOpts.group[i] += ('P' + groupTime);
      }

      // Update node's bounds.
      if (!this.isswimlane || (this.lanes && this.lanes.length == 0)) {
        var newLeftNum = this.bounds.left + offsetDif + pasteTimes * pasteOffset;
        var newTopNum = this.bounds.top + pasteTimes * pasteOffset;
        newOpts.figures[0].style = 'left:' + newLeftNum + 'px; top:' + newTopNum + 'px; width:' + this.bounds.width + 'px; height:' + this.bounds.height + 'px;';
      } else {
        newOpts.data && delete newOpts.data.position;
        newOpts.figures[0].style = 'width:' + this.bounds.width + 'px; height:' + this.bounds.height + 'px;';
      }

      return newOpts;
    },
    toggleHover: function(hover, anchor) {
      this.ishover = (hover == undefined ? !this.ishover : hover);
      this.$box.toggleClass('hover', this.ishover);
      this.hoverAnchor = anchor;
      this.draw();
      return this;
    },
    toggleSelect: function(select /* [optional]*/) {
      // isdraw for second select not to draw again.
      var isdraw = (select ^ this.isselected || select == undefined);
      var isIE7 = $.browser.msie && $.browser.version == 7;
      this.isselected = (select == undefined ? !this.isselected : select);
      if (isdraw) {
        this.$box.toggleClass('selected', this.isselected);
        if (isIE7 && select) {
          // this will break dblclick.
          var that = this;
          window.setTimeout(function() {
            that.draw();
          }, 100);
        } else {
          this.draw();
        }
      }

      return this;
    },
    toggleDisabled: function(disabled) {
      this.disabled = (disabled === undefined ? !this.disabled : disabled);
      this.options.disabled = this.disabled === true;
      this.draw();

      if (this.disabled) {
        this.options.viewmode = 'view';
        !this.$box.hasClass('disabled') && this.$box.addClass('disabled');
      } else {
        this.options.viewmode = 'edit';
        this.$box.hasClass('disabled') && this.$box.removeClass('disabled');
      }
      return this;
    },
    toggleTextEdit: function(edit, jDiagram) {
      var newEdit = (edit === undefined ? !this.textedit : edit);
      if (!(newEdit ^ this.textedit)) { // No need to toggle if new status is the same to old status.
        return false;
      }

      var title = this.title();
      this.$box.toggleClass('textedit', newEdit);
      this.$adapter.children('.uee-node-textedit').remove();
      if (newEdit) {
        this.$adapter.append(['<div class="uee-node-textedit"><textarea>', title, '</textarea></div>'].join(''));

        // Set focus at the last position of title.
        var This = this;
        setTimeout(function() {
          var $textarea = This.$adapter.find('textarea');
          if ($textarea[0]) {
            var len = $textarea[0].value.length;
            $textarea.focus();

            if (document.selection) { // To support ie
              var textRange = $textarea[0].createTextRange();
              textRange.moveStart('character', len);
              textRange.collapse();
              textRange.select();
            } else if (typeof $textarea[0].selectionStart == 'number' &&
              typeof $textarea[0].selectionEnd == 'number') { // firefox and chrome
              $textarea[0].selectionStart = $textarea[0].selectionEnd = len;
            }
          }
        }, 1);
      } else {
        this.title(title, jDiagram);
      }
      this.textedit = newEdit;
      return true;
    },
    title: function(title /* optional*/, jDiagram) {
      if (title === undefined) {
        return this.textedit ? this.$adapter.find('.uee-node-textedit').children('textarea').val() : this.$box.children('pre').text();
      } else {
        // To support IE 7,8.To convey the '\n'.
        if ($.browser.msie && parseInt($.browser.version) == 8) {
          title = title.replace(/(\n)/gm, '\n');
        } else if ($.browser.msie && parseInt($.browser.version) == 7) {
          title = title.replace(/(\n)/gm, '\r');
        }

        this.$box.children('pre').text(title);
        this.options.title = title;

        jDiagram && jDiagram.syncJson();
        return this;
      }
    },
    /**
     * The scope of resizing
     */
    resizeScope: function(resizedir, difX, difY, bounds) {
      if (resizedir >= 6) { // anchorid == 6,7,8
        // (move left : node's left) <= difX <= (move right : node's width)
        // Math.min : move left && Math.max : move right
        difX = Math.min(Math.max(-bounds.left, difX), bounds.width - this.minWidth);
      } else if (resizedir >= 2 || resizedir <= 4) { // anchorid == 2,3,4,restrict its difX and 1,5 without difX
        difX = Math.max(-bounds.width + this.minWidth, difX);
      }

      if (resizedir == 1 || resizedir == 2 || resizedir == 8) { // anchorid == 1,2,8
        difY = Math.min(Math.max(-bounds.top, difY), bounds.height - this.minHeight);
      } else if (resizedir >= 4 || resizedir <= 6) { // anchorid == 4,5,6,restrict its difY and 3,7 without difY
        difY = Math.max(-bounds.height + this.minHeight, difY);
      }

      return {
        difX: difX,
        difY: difY,
      };
    },
    canDrag: function(e) {
      if ($.contains(this.$adapter[0], e.target)) { // prevent dragging things except node or path inside node adapter
        if (Utils.indexOf($(':input'), e.target) != -1) { // input element
          return false;
        }

        var offset = $(e.target).offset() || {
          left: 0,
          top: 0,
        };
        if (e.pageX - offset.left > (e.target.clientWidth || e.target.offsetWidth) ||
          e.pageY - offset.top > (e.target.clientHeight || e.target.offsetHeight)) { // mouse on scroll
          return false;
        }
      }
      return true;
    },
    canContain: function(name) {
      var containments = this.options.containments;

      // return true if did not define containments
      if (containments == undefined) {
        return true;
      }

      // compare with target's name by lowercase
      // 'none' means this source could not contain
      for (var i = 0; i < containments.length; i++) {
        if (containments[i] == 'none') {
          return false;
        }
        if (containments[i].toLocaleLowerCase() == name.toLocaleLowerCase()) {
          return true;
        }
      }

      return false;
    },
    /**
     * The node zoomin and zoomout by this zoom scale.
     * @param zoomScale : zoom scale
     */
    zoom: function(zoomScale) {
      this.options.zoomScale = zoomScale;
      this.updateBounds(this.getBounds(), false, false, true);
    },
    isOnShape: function(pageEvent) {
      var tgid = pageEvent.currentTarget.id;
      if (this.anchornodes && this.anchornodes[tgid]) {
        return true;
      }
      var offset = this.$box.offset();
      var x = pageEvent.pageX - offset.left;
      var y = pageEvent.pageY - offset.top;
      if ($.browser.msie) { // for unifying active menu scope of ie and ff/chrome
        // event.pageY of ie = 1 + e.pageY of ff/chrome
        x--;
        y++;
      }

      if ($.browser.mozilla && $.browser.version.indexOf('19') != -1) {
        x = x + this.options.margin;
        y = y + this.options.margin;
      }

      if (x < 0 || x >= this.width || y < 0 || y >= this.height) {
        return false;
      }

      if (this.jType.figure == jBME.Node.Type.prototype.figure) {
        return !this.iscontainer &&
          (pageEvent.type != 'mouseleave' || $('#' + this.$box.parent().parent()[0].id + '_mask'));
      } else if (this.context) {
        // To support IE 7,8,because the method of isPointInPath()is not supported by using excanvas.
        if (!this.context.isPointInPath) {
          // TODO pageEvent.target.className == "uee-node-adapter" ---> sometime hover on uee-node-adapter with ie7/8
          return $.nodeName(pageEvent.target, 'shape') || $.nodeName(pageEvent.target, 'pre') || $.nodeName(pageEvent.target, 'textarea');
        } else {
          return (this.context.isPointInPath(x + this.options.margin, y + this.options.margin));
        }
      } else {
        return true;
      }
    },
    toJSON: function() { // builds JSON string.
      var data = this.getJSONData();
      return JSON.stringify(data);
    },
    getJSONData: function() { // builds JSON string.
      var data = Utils.getChanged(this.options, jBME.Diagram.options.node);
      data.id = this.id;

      // Generate style of first figure.
      !data.figures && (data.figures = []);
      !data.figures[0] && (data.figures[0] = {});
      data.figures[0].style = ['left:', this.bounds.left, 'px;top:', this.bounds.top, 'px;width:', this.bounds.width, 'px;height:', this.bounds.height, 'px;'].join('');

      if (data.title) {
        data.title = data.title.replace(/(\r\n|\n|\r)/gm, '\\n').replace(/\'/gm, '\\\'');
      }
      return data;
    },
    /**
     * Draw shape, logo, anchors and so on.
     * Standardized canvas API :
     * @param refrence http:// www.w3.org/html/wg/drafts/2dcontext/html5_canvas/
     *   context.beginPath();
     *   context.moveTo(x, y);
     *   context.lineTo(x, y);
     *   ....
     *
     *   context.save(); // save state
     *   context.fillStyle = "#C0C0C0";
     *   context.strokeStyle = "#C0C0C0";
     *   context.lineWidth = 2;
     *   context.fill();
     *   context.stroke();
     *   context.restore(); // recover state
     * @param context: provides objects, methods, and properties to draw and manipulate graphics on a canvas drawing surface.
     * @param options: node's json
     * @param zoom scale
     */
    draw: function(context /* [optional]*/, options /* [optional]*/) {
      context = context || this.context;
      options = options || this.options;
      if (context == null) {
        return;
      }

      // prepare
      var margin = this.options.margin * this.options.zoomScale;
      context.save();
      context.clearRect(0, 0, this.canvas.width, this.canvas.height);
      context.translate(margin, margin);

      // Shape
      this.drawShape(context, options);

      // Logo
      this.drawLogo(context, options);

      // Anchors.
      this.drawAnchor(context, options);

      // Hit region
      this.drawHit(context, options);

      // DONE.
      context.restore();
    },
    drawShape: function(context, options) {
      // draw figures
      var defResult = {
        fill: true,
        stroke: true,
        dom: false,
      };
      if (!this.$figure && options.figures.length > 1) {
        this.$figure = jQuery('<div class="bc_dgm_figures"></div>');
        this.$adapter.append(this.$figure);
      }

      for (var i = 0; i < options.figures.length; i++) {
        var figure = options.figures[i];

        if (!figure) {
          continue;
        }

        figure.context = $.extend(true, {}, options.figures[0].context, figure.context);
        figure.options = $.extend({}, figure.options);

        context.beginPath();
        context.save();
        if (!this.isDOMFigure[i]) {
          var result = jBME.Node.Type[figure.type || 'default'].figure.call(this, context, figure);
          var re = $.extend({}, defResult, result);

          this.isDOMFigure[i] = re.dom;

          if (this.disabled) {
            re.fill && this.fillShape(context, figure, false);
            re.stroke && this.stroke(context, options.disabledContext.strokeStyle, options.disabledContext.lineWidth);
          } else {
            re.fill && this.fillShape(context, figure, false);
            re.stroke && this.stroke(context, figure.context.strokeStyle, figure.context.lineWidth, figure.context.lineDash);
          }
        }

        context.restore();
      }

      // selected
      if (!this.disabled && this.isselected) {
        context.beginPath();
        context.save();
        if (!this.isDOMFigure[0]) {
          var selectResult = this.jType.selectFigure.call(this, context, options.figures[0]);
          var sr = $.extend({}, defResult, selectResult);

          sr.fill && this.fill(context, options.selected.fillStyle);
          sr.stroke && this.stroke(context, options.selected.strokeStyle, options.selected.lineWidth);
        }

        context.restore();
      }
    },
    drawHit: function(context, options) {
      // draw hitFigure
      var defResult = {
        fill: true,
        stroke: true,
        dom: false,
      };

      context.beginPath();
      context.save();
      var reHit = this.jType.hitFigure.call(this, context, options.figures[0]);
      var rh = $.extend({}, defResult, reHit);

      rh.fill && this.fill(context, 'rgba(0,0,0,0)');
      rh.stroke && this.stroke(context, 'rgba(0,0,0,0)', this.isselected ? options.selected.lineWidth : options.figures[0].context.lineWidth);

      context.restore();
    },
    drawAnchor: function(context, options) {
      var actives = this.getActiveAnchors();
      var outsideBounds = this.getBounds();
      if (this.anchornodes) {
        for (var aid in this.anchornodes) {
          var anchornode = this.anchornodes[aid] ? this.anchornodes[aid].instance : null;
          if (anchornode && anchornode.isShow) {
            anchornode.draw(anchornode.context, anchornode.options);
          }
        }
      }
      if (!this.ishover && !this.isselected && $.isEmptyObject(actives) && !this.bigStatus) {
        return;
      }
      for (var i = 1; i < this.jType.anchors.length; ++i) {
        var anchor = this.jType.anchors[i];
        var style = null;
        if (actives[i]) {
          style = options.anchor.active;
        } else if (i == this.bigAnchor && this.bigStatus) {
          style = {
            fillStyle: '#F17D00',
            lineWidth: 1,
            radius: 2.5,
            strokeStyle: '#F17D00',
          };

          var newstyle = {
            lineWidth: 2,
            radius: 7.5,
            strokeStyle: '#fc7d4b',
          };

          context.beginPath();
          context.save();
          context.arc(outsideBounds.width * anchor[0], outsideBounds.height * anchor[1], newstyle.radius * this.options.zoomScale, 0, 2 * Math.PI, false);
          this.stroke(context, newstyle.strokeStyle, newstyle.lineWidth);
          context.restore();
        } else if (this.isselected) {
          style = options.anchor.selected;
        } else if (this.ishover) {
          style = options.anchor.normal;
        } else {
          continue;
        }

        context.beginPath();
        context.save();
        context.arc(outsideBounds.width * anchor[0], outsideBounds.height * anchor[1], style.radius * this.options.zoomScale, 0, 2 * Math.PI, false);
        this.fill(context, style.fillStyle);
        this.stroke(context, style.strokeStyle, style.lineWidth);
        context.restore();
      }
    },
    drawLogo: function(context, options) {
      if (options.logo && $.trim(this.options.logo) != '') {
        var logoBounds = this.getLogoBounds(options.logoBounds);
        if (!this.logoImg) { // When we're loading image first time,we should invoke onload function to make sure image is load completely.
          // Create a new image
          this.logoImg = new Image();
          this.logoImg.src = options.logo + '?' + $.now();
          var margin = options.margin * options.zoomScale;

          $(this.logoImg).load(function() {
            try {
              context.drawImage(this, logoBounds.x + margin, logoBounds.y + margin, logoBounds.w, logoBounds.h);
            } catch (err) {
            }
          });
        } else { // Use the image in the cookies
          try {
            context.drawImage(this.logoImg, logoBounds.x, logoBounds.y, logoBounds.w, logoBounds.h);
          } catch (err) {
          }
        }
      }
    },
    getLogoBounds: function(logoBounds) {
      var result = {};
      var bounds = $.extend({
        x: 8,
        y: 4,
        w: 16,
        h: 16,
      }, logoBounds);
      // The outside bounds -> first figure bounds.
      var oursideBounds = this.getBounds();

      for (var key in bounds) {
        var value = bounds[key];
        var reference = key == 'x' || key == 'r' || key == 'w' ? oursideBounds.width : oursideBounds.height;

        if (value >= -1 && value <= 1) {
          result[key] = value * reference;
        } else {
          result[key] = value * this.options.zoomScale;
        }
      }

      // Adjust right and bottom value
      if (!result.x && result.r) {
        result.x = oursideBounds.width - result.r - result.w;
        delete result.r;
      }
      if (!result.y && result.b) {
        result.y = oursideBounds.height - result.b - result.h;
        delete result.b;
      }

      return result;
    },
    getFillType: function(figure) {
      var fill = figure.fill;

      if (!fill) {
        return 'COLOR';
      } else if (fill.gradientStyle == 'linear') {
        return 'DEFAULTLINEAR';
      } else if (fill.gradientStyle == 'radial') {
        return 'DEFAULTRADIAL';
      } else if (fill.start && fill.end && !fill.start.radius && !fill.end.radius) {
        return 'LINEAR_GRADIENT';
      } else if (fill.start && fill.end && typeof fill.start.radius == 'number' && typeof fill.end.radius == 'number') {
        return 'RADIAL_GRADIENT';
      } else {
        return 'UNKNOWN';
      }
    },
    fillShape: function(context, figure, skipShadow) {
      var fillType = this.getFillType(figure);
      var fbounds = this.getFigureBounds(figure);

      if (!skipShadow && figure.shadow) {
        this.applyShadow(context, figure);
      }

      if (!this.disabled) {
        switch (fillType) {
          case 'COLOR':
            this.fill(context, figure.context.fillStyle);
            break;
          case 'DEFAULTLINEAR':
            var c = Utils.getColorChanged(figure.context.fillStyle, 70, 70, 70);
            var f = {
              start: {
                x: 0,
                y: 0,
              },
              end: {
                x: 1,
                y: 1,
              },
              colorStops: [0, c, 1, figure.context.fillStyle],
            };
            this.fillLinearGradient(context, f, fbounds.width, fbounds.height);
            break;
          case 'DEFAULTRADIAL':
            var c = Utils.getColorChanged(figure.context.fillStyle, 70, 70, 70);
            var f = {
              start: {
                x: 0.5,
                y: 0.5,
                radius: 0,
              },
              end: {
                x: 0.5,
                y: 0.5,
                radius: 1,
              },
              colorStops: [0, c, 1, figure.context.fillStyle],
            };
            this.fillRadialGradient(context, f, fbounds.width, fbounds.height);
            break;
          case 'LINEAR_GRADIENT':
            this.fillLinearGradient(context, figure.fill, fbounds.width, fbounds.height);
            break;
          case 'RADIAL_GRADIENT':
            this.fillRadialGradient(context, figure.fill, fbounds.width, fbounds.height);
            break;
          default:
            this.fill(context, 'black');
            break;
        }
      } else { // When disabled state, no gradient.
        this.fill(context, this.options.disabledContext.fillStyle);
      }

      if (!skipShadow && figure.shadow) {
        this.fillShape(context, figure, true);
      }
    },
    /**
     * fill shape with fillStyle's color
     * @param fillStyle : color
     */
    fill: function(context, fillStyle) {
      context.save();
      context.fillStyle = fillStyle;
      context.fill();
      context.restore();
    },
    /**
     * fill shape with Linear Gradient
     * @param context
     * @param fill : Linear Gradient fill
     */
    fillLinearGradient: function(context, fill, w, h) {
      var s = fill.start;
      var e = fill.end;
      var colorStops = fill.colorStops;

      context.save();

      // build color stops
      var grd = context.createLinearGradient(s.x * w, s.y * h, e.x * w, e.y * h);
      for (var n = 0; n < colorStops.length; n += 2) {
        grd.addColorStop(colorStops[n], colorStops[n + 1]);
      }
      context.fillStyle = grd;
      context.fill();

      context.restore();
    },
    /**
     * fill shape with Radial Gradient
     * @param context
     * @param fill : Radial Gradient fill
     */
    fillRadialGradient: function(context, fill, w, h) {
      var s = fill.start;
      var e = fill.end;
      var colorStops = fill.colorStops;

      context.save();

      // build color stops
      var grd = context.createRadialGradient(s.x * w, s.y * h, s.radius * w, e.x * w, e.y * h, e.radius * w);
      for (var n = 0; n < colorStops.length; n += 2) {
        grd.addColorStop(colorStops[n], colorStops[n + 1]);
      }
      context.fillStyle = grd;
      context.fill();

      context.restore();
    },
    /**
     * stroke shape with strokeStyle's color
     * @param strokeStyle : color
     */
    stroke: function(context, strokeStyle, lineWidth, /* optional*/ lineDash) {
      context.save();
      context.strokeStyle = strokeStyle;
      context.lineWidth = lineWidth * this.options.zoomScale;
      if (lineDash && context.setLineDash) {
        context.setLineDash(lineDash);
      }
      context.stroke();
      context.restore();
    },
    applyShadow: function(context, figure) {
      var shadow = figure.shadow;
      if (shadow) {
        // defaults
        var color = shadow.color || 'black';
        var blur = (shadow.blur || 5) * this.options.zoomScale;

        context.shadowColor = color;
        context.shadowBlur = blur;
        context.shadowOffsetX = (shadow.offsetX || 2) * this.options.zoomScale;
        context.shadowOffsetY = (shadow.offsetY || 2) * this.options.zoomScale;
      }
    },
    /**
     * Get node bounds from figure and calculate bottom and right.
     * @param figure
     * @returns figureBounds
     */
    getFigureBounds: function(figure) {
      var figureBounds = this.getBoundsFromFigure(figure);

      // Adjust right and bottom value
      if (!figureBounds.left && figureBounds.right) {
        figureBounds.left = this.width - figureBounds.right - figureBounds.width;
        delete figureBounds.right;
      }
      if (!figureBounds.top && figureBounds.bottom) {
        figureBounds.top = this.height - figureBounds.bottom - figureBounds.height;
        delete figureBounds.bottom;
      }

      return figureBounds;
    },
    /**
     * Get node bounds from figure.
     * @param figure : node figure data
     * @returns bounds : node bounds
     */
    getBoundsFromFigure: function(figure) {
      var bounds = {};
      var style;
      // Outside node bounds -> the first figure
      var outsideBounds = this.getBounds();
      if (figure.style && (
        // Object type
        typeof figure.style == 'object' ||
        // String type eg. "{\"left\": 0,\"top\": 0...dth\": 1,\"height\": 1}"
        (figure.style.indexOf('{') != -1 && figure.style.indexOf('}') != -1))) { // Second ...N figure need to parese.
        if (typeof figure.style != 'object') { // new string type of figures which need to parse
          // e.g '{"left":10,"top":0.1}'
          try {
            style = $.parseJSON(figure.style);
          } catch (exception) {
            style = figure.style;
          }
        } else { // Old object type which don't need to parse
          style = figure.style;
        }

        for (var key in style) {
          var value = style[key];
          var reference = key == 'left' || key == 'right' || key == 'width' ?
            outsideBounds.width : outsideBounds.height;

          if (value >= -1 && value <= 1) { // the referece has already zoom, so this place should not multiply by zoomargs
            bounds[key] = value * reference;
          } else {
            bounds[key] = value * this.options.zoomScale;
          }
        }
      } else { // First figure don't need to parse.
        bounds = {
          left: 0,
          top: 0,
          width: outsideBounds.width,
          height: outsideBounds.height,
        };
      }

      return bounds;
    },
  });

  /**
   * A special Node inherit from [jBME.Node]
   * The [MouseNode] DONOT display and has no DOM, but indicates the position of Mouse.
   * And performs like a Node(has all Node's API).
   * Used for connecting and reconnecting the path, as a end of the path.
   */
  var MouseNode = function() {
    return $.extend(new jBME.Node(['__MouseNode', $.now(), '__'].join('')), this);
  };
  MouseNode.prototype = {
    updateBounds: function(bounds) {
      this.bounds = bounds;
      // Update paths bounds connected to this node.
      for (var id in this.paths) {
        this.paths[id].updateBounds();
      }
    },
    getBounds: function() {
      return this.bounds;
    },
    getCenterPosition: function() {
      return this.bounds;
    },
    getAnchorPosition: function(anchorid, position) {
      return this.bounds;
    },
    draw: function() {
      /* nothing.*/
    },
  };

  /**
   * Node Type Define.
   * @param name,
   * The node type name
   * @param options contains figure, hitFigure and anchors,
   * @param figure,
   * The function actually draw the Node shape.
   * In the function, 'this' point to Node instance(NOT the Node Type Class defined here)
   * @param hitFigure,
   * The function actually draw the hit region to make the path.
   * @param anchors,
   * A Matrix(2D-Array, [[x,y],...]), defines #Anchor Number and Position Coefficient([x,y], x,y,0~1)
   * ------------------------------------
   * (0,0)NW N NE(1,0)   #8 #1 #2
   *    W  C E    ==> #7 #0 #3
   * (0,1)SW S SE(1,1)   #6 #5 #4
   */
  jBME.Node.Type = function(name, options) {
    jBME.Node.Type[name] = this;
    this.name = name;
    $.extend(this, options);
  };
  jBME.Node.Type.prototype = {
    name: '',

    anchors: [
      [0.5, 0.5],
      [0.5, 0],
      [1, 0],
      [1, 0.5],
      [1, 1],
      [0.5, 1],
      [0, 1],
      [0, 0.5],
      [0, 0],
    ],

    /**
     * draw shape with canvas API
     * 'this' point to the Node instance(Not the Node Type prototype defined here)
     * @param context: provides objects, methods, and properties to draw and manipulate graphics on a canvas drawing surface.
     * @param figure: node's json
     */
    figure: function(context, figure) {
    },
    /**
     * draw hit region with canvas API
     * 'this' point to the Node instance(Not the Node Type prototype defined here)
     * @param context: provides objects, methods, and properties to draw and manipulate graphics on a canvas drawing surface.
     * @param figure: node's json
     */
    hitFigure: function(context, figure) {
      // Use figure as default
      return this.jType.figure.call(this, context, figure);
    },
    /**
     * draw selected style with canvas API
     * 'this' point to the Node instance(Not the Node Type prototype defined here)
     * @param context: provides objects, methods, and properties to draw and manipulate graphics on a canvas drawing surface.
     * @param figure: node's json
     */
    selectFigure: function(context, figure) {
      // Use figure as default
      return this.jType.figure.call(this, context, figure);
    },
  };

  /**
   * OK, we defines several node types here. Of course you can defines it anywhere.
   */
  new jBME.Node.Type('default', {
    selectFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
  });
  new jBME.Node.Type('rect', {
    figure: function(context, figure) {
      var bounds = this.getFigureBounds(figure);
      context.translate(bounds.left, bounds.top);
      context.rect(0, 0, bounds.width, bounds.height);
    },
  });
  new jBME.Node.Type('round', {
    figure: function(context, figure) {
      var bounds = this.getFigureBounds(figure);
      var diameter = Math.max(ZERO, Math.min(bounds.width, bounds.height));
      var radius = diameter / 2;

      // Scale the circle to support both oval and circle.
      context.save();
      context.translate(bounds.left + bounds.width / 2, bounds.top + bounds.height / 2);
      context.scale(bounds.width / diameter, bounds.height / diameter);
      context.arc(0, 0, radius, 0, 2 * Math.PI, false);

      // To support IE 7,8,because the function of context.restore() clear the scale in the context,so we draw the oval failing.
      if (Utils.isUseExcanvas()) {
        var ctxStyle = this.isselected ? this.options.selected : figure.context;
        this.fillShape(context, figure, false);
        this.stroke(context, ctxStyle.strokeStyle, ctxStyle.lineWidth);
      }
      context.restore();

      if (Utils.isUseExcanvas()) {
        return {
          fill: false,
          stroke: false,
        };
      }
    },
    hitFigure: function(context, figure) {
      var bounds = this.getFigureBounds(figure);
      var diameter = Math.max(ZERO, Math.min(bounds.width, bounds.height));
      var radius = diameter / 2;

      // Scale the circle to support both oval and circle.
      context.save();
      context.translate(bounds.width / 2, bounds.height / 2);
      context.scale(bounds.width / diameter, bounds.height / diameter);
      context.arc(0, 0, radius, 0, 2 * Math.PI, false);

      // To support IE 7,8,because the function of context.restore() clear the scale in the context,so we draw the oval failing.
      if (Utils.isUseExcanvas()) {
        this.fill(context, 'rgba(0,0,0,0)');
        this.stroke(context, 'rgba(0,0,0,0)', this.isselected ? this.options.selected.lineWidth : figure.context.lineWidth);
      }

      context.restore();

      if (Utils.isUseExcanvas()) {
        return {
          fill: false,
          stroke: false,
        };
      }
    },
    anchors: (function(p, m) {
      return [
        [0.5, 0.5],
        [0.5, 0],
        [p, m],
        [1, 0.5],
        [p, p],
        [0.5, 1],
        [m, p],
        [0, 0.5],
        [m, m],
      ];
    })((1 + sin45d) / 2, (1 - sin45d) / 2),
  });
  new jBME.Node.Type('triangle', {
    figure: function(context, figure) {
      var bounds = this.getFigureBounds(figure);
      context.translate(bounds.left, bounds.top);
      context.moveTo(0, bounds.height);
      context.lineTo(bounds.width / 2, 0);
      context.lineTo(bounds.width, bounds.height);
      context.lineTo(0, bounds.height);
    },
    anchors: [
      [0.5, 0.5],
      [0.5, 0],
      [0.75, 0.5],
      [0.75, 0.5],
      [1, 1],
      [0.5, 1],
      [0, 1],
      [0.25, 0.5],
      [0.25, 0.5],
    ],
  });
  new jBME.Node.Type('diamond', {
    figure: function(context, figure) {
      var bounds = this.getFigureBounds(figure);
      context.translate(bounds.left, bounds.top);
      context.moveTo(0, bounds.height / 2);
      context.lineTo(bounds.width / 2, 0);
      context.lineTo(bounds.width, bounds.height / 2);
      context.lineTo(bounds.width / 2, bounds.height);
      context.lineTo(0, bounds.height / 2);
    },
    anchors: [
      [0.5, 0.5],
      [0.5, 0],
      [0.75, 0.25],
      [1, 0.5],
      [0.75, 0.75],
      [0.5, 1],
      [0.25, 0.75],
      [0, 0.5],
      [0.25, 0.25],
    ],
  });
  new jBME.Node.Type('hexagon', {
    figure: function(context, figure) {
      var bounds = this.getFigureBounds(figure);
      context.translate(bounds.left, bounds.top);
      context.moveTo(0, bounds.height / 2);
      context.lineTo(bounds.width / 4, 0);
      context.lineTo(bounds.width * 3 / 4, 0);
      context.lineTo(bounds.width, bounds.height / 2);
      context.lineTo(bounds.width * 3 / 4, bounds.height);
      context.lineTo(bounds.width / 4, bounds.height);
      context.lineTo(0, bounds.height / 2);
    },
    anchors: [
      [0.5, 0.5],
      [0.5, 0],
      [0.75, 0],
      [1, 0.5],
      [0.75, 1],
      [0.5, 1],
      [0.25, 1],
      [0, 0.5],
      [0.25, 0],
    ],
  });
  new jBME.Node.Type('trapezoid', {
    figure: function(context, figure) {
      var bounds = this.getFigureBounds(figure);
      context.translate(bounds.left, bounds.top);
      context.moveTo(0, 0);
      context.lineTo(bounds.width, 0);
      context.lineTo(bounds.width * 3 / 4, bounds.height);
      context.lineTo(bounds.width / 4, bounds.height);
      context.lineTo(0, 0);
    },
    anchors: [
      [0.5, 0.5],
      [0.5, 0],
      [1, 0],
      [0.875, 0.5],
      [0.75, 1],
      [0.5, 1],
      [0.25, 1],
      [0.125, 0.5],
      [0, 0],
    ],
  });
  new jBME.Node.Type('roundrect', {
    figure: function(context, figure) {
      var bounds = this.getFigureBounds(figure);
      var radius = figure.options.radius || 5;
      var minRadius = Math.max(ZERO, Math.min(bounds.width, bounds.height)) / 2;
      radius = radius > minRadius ? minRadius / 4 : radius;

      context.translate(bounds.left, bounds.top);
      context.moveTo(0, radius);
      context.arc(radius, radius, radius, Math.PI, Math.PI * 3 / 2, false);
      context.lineTo(bounds.width - radius, 0);
      context.arc(bounds.width - radius, radius, radius, Math.PI * 3 / 2, Math.PI * 2, false);
      context.lineTo(bounds.width, bounds.height - radius);
      context.arc(bounds.width - radius, bounds.height - radius, radius, 0, Math.PI * 1 / 2, false);
      context.lineTo(radius, bounds.height);
      context.arc(radius, bounds.height - radius, radius, Math.PI * 1 / 2, Math.PI, false);
      context.lineTo(0, radius);
    },
  });
  new jBME.Node.Type('image', {
    figure: function(context, figure) {
      var bounds = this.getFigureBounds(figure);
      if (figure.options.src && $.trim(figure.options.src) != '') {
        if (!this[figure.options.src]) { // When we're loading image first time,we should invoke onload function to make sure image is load completely.
          // Create a new image
          this[figure.options.src] = new Image();
          this[figure.options.src].src = figure.options.src + '?' + $.now();
          var margin = this.options.margin * this.options.zoomScale;
          var that = this;
          $(this[figure.options.src]).load(function(e) {
            context.drawImage(this, margin + bounds.left, margin + bounds.top, bounds.width, bounds.height);
            Utils.isUseExcanvas() && that.drawHit(context, that.options);
          });
        } else { // Use the image in the cookies
          try {
            context.drawImage(this[figure.options.src], bounds.left, bounds.top, bounds.width, bounds.height);
          } catch (err) {
          }
        }
      }
      return {
        fill: false,
        stroke: false,
      };
    },
    hitFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
    selectFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
  });
  new jBME.Node.Type('swimlane', {
    figure: function(context, figure) {
      var titleHeight = figure.options.titleHeight || this.titleHeight;
      context.rect(0, 0, this.width, this.height);
      this.stroke(context, figure.context.strokeStyle, figure.context.lineWidth);

      context.beginPath();
      context.rect(0, 0, this.width, titleHeight);
    },
    hitFigure: function(context, figure) {
      var titleHeight = figure.options.titleHeight || this.titleHeight;
      context.rect(0, 0, this.width, titleHeight);
    },
    selectFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
  });
  new jBME.Node.Type('horizontalswimlane', {
    figure: function(context, figure) {
      var titleHeight = figure.options.titleHeight || this.titleHeight;
      context.rect(0, 0, this.width, this.height);
      this.stroke(context, figure.context.strokeStyle, figure.context.lineWidth);

      context.beginPath();
      context.rect(0, 0, titleHeight, this.height);
    },
    hitFigure: function(context, figure) {
      var titleHeight = figure.options.titleHeight || this.titleHeight;
      context.rect(0, 0, titleHeight, this.height);
    },
    selectFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
  });
  new jBME.Node.Type('rectcontainer', {
    figure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
    hitFigure: function(context, figure) {
      var titleHeight = 30;
      context.rect(0, 0, this.width, titleHeight);
    },
  });
  new jBME.Node.Type('DOM', {
    figure: function(context, figure) {
      var that = this;
      $(function() {
        var $box = jQuery(figure.attrs.selector).clone().removeAttr('id');
        $box.attr('style', figure.style);
        for (var key in figure.attrs) {
          key != 'selector' && $box.attr(key, figure.attrs[key]);
        }
        that.$figure.append($box);
      });

      return {
        dom: true,
      };
    },
    hitFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
  });
  new jBME.Node.Type('div', {
    figure: function(context, figure) {
      var $box = jQuery(['<div style="', figure.style, '"></div>'].join(''));
      for (var key in figure.attrs) {
        key != 'selector' && $box.attr(key, figure.attrs[key]);
      }
      this.$figure.append($box);

      return {
        dom: true,
      };
    },
    hitFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
  });
  new jBME.Node.Type('img', {
    figure: function(context, figure) {
      var $box = jQuery(['<img style="', figure.style, '"></img>'].join(''));
      for (var key in figure.attrs) {
        key != 'selector' && $box.attr(key, figure.attrs[key]);
      }
      this.$figure.append($box);

      return {
        dom: true,
      };
    },
    hitFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
  });
  jBME.Node.Type['roundrect'].customAnchor = function(anchorid) {
    var anchornodepos;
    this.anchornodes && this.anchornodes[anchorid] && (anchornodepos = this.anchornodes[anchorid].position);

    var anchorXY = this.jType.anchors[anchorid] || anchornodepos || this.jType.anchors[0];
    var jNodeBound = this.getBounds();
    var pos = {
      left: jNodeBound.left,
      top: jNodeBound.top,
    };
    var radius = this.options.figures[0].options.radius || 5;
    var minRadius = Math.max(ZERO, Math.min(this.width, this.height)) / 2;

    radius = radius > minRadius ? minRadius / 4 : radius;

    // the offsetX and offsetY from corner in roundrect to corn in rect
    var anchorOffset = (2 - Math.sqrt(2)) * radius / 2 - 0.6;

    if (anchorid % 2 == 0) { // When anchorid==2,- +; anchorid==4,- -; anchorid==6,+ -; anchorid==8,+ +;
      pos.left += Math.round(this.width * anchorXY[0] + Math.cos(Math.PI / 3 + (anchorid / 4) * Math.PI) * 2 * anchorOffset);
      pos.top += Math.round(this.height * anchorXY[1] + Math.cos(Math.PI / 3 + ((anchorid - 2) / 4) * Math.PI) * 2 * anchorOffset);
    } else if (this.anchornodes && this.anchornodes[anchorid]) {
      var anchornodeopts = this.anchornodes[anchorid];
      var relativex = anchornodeopts.proportionx ? Math.round(this.width * anchorXY[0]) : Math.round(anchorXY[0]);
      var relativey = anchornodeopts.proportiony ? Math.round(this.height * anchorXY[1]) : Math.round(anchorXY[1]);
      pos.left += relativex;
      pos.top += relativey;
      var anchornodebounds = anchornodeopts.instance.getBounds();

      if (relativey >= this.height || (relativey >= (this.height - anchornodebounds.height) && relativex > 0 && relativex < (this.width - anchornodebounds.width))) {
        // close to bottom
        pos.left += Math.round(anchornodebounds.width / 2);
        pos.top += Math.round(anchornodebounds.height);
      } else if (relativey <= -anchornodebounds.height || (relativey <= 0 && relativex > 0 && relativex < (this.width - anchornodebounds.width))) {
        // close to top
        pos.left += Math.round(anchornodebounds.width / 2);
      } else if (relativex > anchornodebounds.width / 2) {
        // close to right
        pos.left += Math.round(anchornodebounds.width);
        pos.top += Math.round(anchornodebounds.height / 2);
      } else {
        // close to left
        pos.top += Math.round(anchornodebounds.height / 2);
      }
    } else {
      pos.left += Math.round(this.width * anchorXY[0]);
      pos.top += Math.round(this.height * anchorXY[1]);
    }
    pos.anchorid = anchorid;
    return pos;
  };
  /**
   * Container
   */
  jBME.Container = jBME.Node.extend({
    construct: function(id, options, $nodebox, zoomargs) {
      this.construct.$.construct.call(this, id, options, $nodebox, zoomargs);

      /**
       * The subnodes of this container
       */
      this.children = {};
    },
    /**
     * Min value of width
     */
    minWidth: 10,
    /**
     * Min value of height
     */
    minHeight: 45,
    /**
     * The different between node and container
     */
    iscontainer: true,
    /**
     * Add subnode to container
     * @param subnode
     */
    addChild: function(subnode) {
      if (Utils.indexOf(this.options.children, subnode.id) == -1) {
        this.options.children.push(subnode.id);
      }
      this.children[subnode.id] = subnode;
    },
    /**
     * Add subnodes to container
     * @param subnodes[] the array of this children
     */
    addChildren: function(subnodes) {
      for (var i = 0; i < subnodes.length; i++) {
        this.addChild(subnodes[i]);
      }
    },
    /**
     * Remove subnode from container
     * @param subnode
     */
    removeChild: function(subnode) {
      var children = this.options.children;
      children.splice(Utils.indexOf(children, subnode.id), 1);
      delete this.children[subnode.id];
    },
    /**
     * Remove subnodes from container
     * @param subnodes the array of this children
     */
    removeChildren: function(subnodes) {
      for (var i = 0; i < subnodes.length; i++) {
        this.removeChild(subnodes[i]);
      }
    },
    /**
     * Get all descendant of this node
     * descent: children and children'children.
     * @return descendant : the object, all descendant of this container
     */
    getDescendant: function() {
      var descendant = {};
      getChildren(this);

      // Recursion method to look for children.
      function getChildren(jElement) {
        if (jElement.iscontainer) {
          for (var id in jElement.children) {
            descendant[id] = jElement.children[id];
            if (jElement.children[id].iscontainer) { // recursion
              getChildren(jElement.children[id]);
            }
          }
        }
      }

      return descendant;
    },
    /**
     * Get this node's parent's parent, until return it's root element.
     * @returns rootElement
     */
    getRootElement: function() {
      var rootElement = null;
      getRoot(this);

      // Recursion method to look for root.
      function getRoot(jElement) {
        if (jElement.parent) {
          rootElement = jElement.parent;
          getRoot(jElement.parent);
        }
      }

      return rootElement;
    },
    /**
     * The scope of resizing
     */
    resizeScope: function(resizedir, difX, difY, bounds) {
      // Resizing and dragging scope of container.
      var children = this.getDescendant();
      for (var cid in children) {
        children[cid].$box.css('display') == 'none' && delete children[cid];
      }

      // children's whole bounds
      var ensembleBound = Utils.getEnsembleBounds(children);
      var isEmpty = $.isEmptyObject(ensembleBound);

      if (resizedir >= 6) { // anchorid == 6,7,8
        // (move left : node's left) <= difX <= (move right : min child's left - node's left)
        // Math.min : move left && Math.max : move right
        var maxValue = isEmpty ? bounds.width - this.minWidth : ensembleBound.left - bounds.left - 1;
        difX = Math.min(Math.max(-bounds.left, difX), maxValue);
      } else if (resizedir >= 2 || resizedir <= 4) { // anchorid == 2,3,4,restrict its difX and 1,5 without difX
        // Child's right - node's right <= difX < endless
        var minValue = isEmpty ? -bounds.width + this.minWidth : ensembleBound.right - (bounds.left + bounds.width) + 1;
        difX = Math.max(minValue, difX);
      }

      if (resizedir == 1 || resizedir == 2 || resizedir == 8) { // anchorid == 1,2,8
        // - node's top <= difY <= child's top - node's top
        var maxValue = isEmpty ? bounds.height - this.minHeight : ensembleBound.top - bounds.top - 1;
        difY = Math.min(Math.max(-bounds.top, difY), maxValue);
      } else if (resizedir >= 4 || resizedir <= 6) { // anchorid == 4,5,6,restrict its difY and 3,7 without difY
        // Child's bottom - node's bottom <= difY < endless
        var minValue = isEmpty ? -bounds.height + this.minHeight : ensembleBound.bottom - (bounds.top + bounds.height) + 1;
        difY = Math.max(minValue, difY);
      }

      return {
        difX: difX,
        difY: difY,
      };
    },
  });
  /**
   * Rectcontainer
   */
  jBME.Rectcontainer = jBME.Container.extend({});
  /**
   * The interface of Swimlane
   */
  jBME.AbstructSwimlane = jBME.Container.extend({
    /**
     * Specified swimlane
     */
    isswimlane: true,
    /**
     * Add a swimlane
     */
    addSwimLane: function() {
    },
    /**
     * Remove a swimlane
     */
    removeSwimLane: function() {
    },
    /**
     * Resize swimlane
     */
    resizeSwimLane: function() {
    },
    /**
     * Update the bounds of swimlane and its subnodes
     */
    updateSwimLane: function() {
    },
  });
  /**
   * Vertical Swimlane extend Siwnlane
   */
  jBME.Swimlane = jBME.AbstructSwimlane.extend({
    /**
     * Swimlane type
     */
    verticalSwimlane: true,
    /**
     * All vertical swimlanes
     */
    lanes: [],
    /**
     * Min value of width
     */
    minWidth: 15,
    /**
     * Min value of height
     */
    minHeight: 45,
    /**
     * The height of title scope
     */
    titleHeight: 40,
    /**
     * Add a vertical swimlane
     */
    addSwimLane: function(options, source) {
      var position = 0;
      var currentBound = this.getBounds();

      if (options.data && (options.data.position >= 0 || options.data.position <= this.lanes.length)) { // Insert swimlane to specified position
        position = options.data.position;
        delete options.data.position;
      } else { // Insert to the last position
        position = this.lanes.length;
        if (source != 'paste' && source != 'nodecreate') { // Looking for original place when save || undo redo to create swimlane.
          for (var i = 0; i < this.lanes.length; i++) {
            var bound = this.lanes[i].getBounds();
            if (currentBound.left <= bound.left) {
              position = i;
              break;
            }
          }
        }
      }

      if (this.lanes.length == 0) { // Create the first lane.
        this.index() == -1 && this.lanes.push(this);
      } else {
        if (this.lanes.length > 0) {
          if (position != this.lanes.length) { // Insert into the middle of lanes
            // Insert this lane into position
            var insertBound = this.lanes[position].getBounds();
            this.updateBounds({
              left: insertBound.left,
              top: insertBound.top,
            });

            // Change the offset of lanes after this position
            for (var i = position; i < this.lanes.length; i++) {
              var changeBounds = {
                changeLeft: this.width,
              };
              this.lanes[i].updateSwimLane(changeBounds);
            }
          } else { // Insert into the last place
            var lastBound = this.lanes[this.lanes.length - 1].getBounds();
            this.updateBounds({
              left: lastBound.left + lastBound.width,
              top: lastBound.top,
            });
          }
        }

        // Keep the same height
        var laneHeight = this.lanes[0].getBounds().height;
        currentBound.height != laneHeight && this.updateBounds({
          height: laneHeight,
        });

        // Insert the id of this lane into lanes array
        this.index() == -1 && this.lanes.splice(position, 0, this);
      }
    },
    removeSwimLane: function() {
      var index = this.index();

      if (index != -1) {
        this.lanes.splice(index, 1);

        // Move the nodes after this node forward
        for (var i = index; i < this.lanes.length; i++) {
          var changeBounds = {
            changeLeft: -this.width,
          };
          this.lanes[i].updateSwimLane(changeBounds);
        }
      }
    },
    resizeSwimLane: function(difX, difY, oldBound, newBound, resizedir) {
      if (difY != this.oldDifY) { // Vertical resizing, vertical lanes just need to update top and height.
        for (var dyi = 0; dyi < this.lanes.length; dyi++) { // difY resize
          if (this.lanes[dyi].id != this.id) {
            this.lanes[dyi].updateBounds({
              top: this.getBounds().top,
              height: newBound.height || oldBound.height,
            });
          }
        }
        this.oldDifY = difY;
      }

      if (difX != this.oldDifX) { // Horizontal resizing, vertical lanes need to update self and modify others.
        var index = this.index();
        if (newBound.left) { // Resize forward
          for (var dxi = 0; dxi < index; dxi++) { // Change the nodes' left before this one.
            var disOfLanes = 0;
            for (var il = dxi; il < index; il++) {
              disOfLanes += this.lanes[il].width;
            }
            var changeBounds = {
              changeLeft: newBound.left - disOfLanes - this.lanes[dxi].getBounds().left,
            };
            this.lanes[dxi].updateSwimLane(changeBounds);
          }
        } else if (resizedir >= 2 && resizedir <= 4) { // Resize back
          for (var dxb = index + 1; dxb < this.lanes.length; dxb++) { // Change the nodes' left after this one.
            var disOfLanes = 0;
            for (var ib = index + 1; ib < dxb; ib++) {
              disOfLanes += this.lanes[ib].width;
            }
            var changeBounds = {
              changeLeft: oldBound.left + newBound.width + disOfLanes - this.lanes[dxb].getBounds().left,
            };
            this.lanes[dxb].updateSwimLane(changeBounds);
          }
        }
        this.oldDifX = difX;
      }
    },
    /**
     * Update the bounds of swimlane and its subnodes
     * @param changeBounds : {changeLeft:xx, changeTop:xx}
     */
    updateSwimLane: function(changeBounds) {
      // Merge oldBounds and changeBounds
      var containerBounds = this.getBounds();
      changeBounds.changeLeft && (containerBounds.left += changeBounds.changeLeft);
      changeBounds.changeTop && (containerBounds.top += changeBounds.changeTop);
      this.updateBounds(containerBounds);

      // Move its children
      var laneChildren = this.getDescendant();
      for (var lid in laneChildren) {
        // Merge oldBounds and changeBounds
        var childBounds = laneChildren[lid].getBounds();
        changeBounds.changeLeft && (childBounds.left += changeBounds.changeLeft);
        changeBounds.changeTop && (childBounds.top += changeBounds.changeTop);
        laneChildren[lid].updateBounds(childBounds);
      }
    },
    /**
     * The scope of resizing
     */
    resizeScope: function(resizedir, difX, difY, bounds) {
      var totalLanesWidth = 0;
      // Sum of lanes' with before this swimlane, for limiting its resizing left value.
      for (var i = 0; i < this.index(); i++) {
        totalLanesWidth += this.lanes[i].getBounds().width;
      }

      // Resizing left and right
      var children = this.getDescendant();
      for (var cid in children) {
        children[cid].$box.css('display') == 'none' && delete children[cid];
      }
      // current siwmlane's children
      var ensembleBound = Utils.getEnsembleBounds(children);
      var isEmpty = $.isEmptyObject(ensembleBound);

      // Resizing top and bottom
      for (var i = 0; i < this.lanes.length; i++) {
        children = $.extend(children, this.lanes[i].getDescendant());
      }
      // all swimlane's children
      var swimlaneBounds = Utils.getEnsembleBounds(children);
      var isEmptySwimlane = $.isEmptyObject(swimlaneBounds);

      if (resizedir >= 6) { // anchorid == 6,7,8
        // (move left : node's left) <= difX <= (move right : min child's left - node's left)
        // Math.min : move left && Math.max : move right
        var maxValue = isEmpty ? bounds.width - this.minWidth :
          ensembleBound.left - bounds.left - 1;
        difX = Math.min(Math.max(-bounds.left + totalLanesWidth, difX), maxValue);
      } else if (resizedir >= 2 || resizedir <= 4) { // anchorid == 2,3,4,restrict its difX and 1,5 without difX
        // Child's right - node's right <= difX < endless
        var minValue = isEmpty ? -bounds.width + this.minWidth :
          ensembleBound.right - (bounds.left + bounds.width) + 1;
        difX = Math.max(minValue, difX);
      }

      if (resizedir == 1 || resizedir == 2 || resizedir == 8) { // anchorid == 1,2,8
        // - node's top <= difY <= child's top - node's top
        var maxValue = isEmptySwimlane ? bounds.height - this.minHeight :
          Math.min(swimlaneBounds.top - bounds.top - 1, bounds.height - this.minHeight);
        difY = Math.min(Math.max(-bounds.top, difY), maxValue);
      } else if (resizedir >= 4 || resizedir <= 6) { // anchorid == 4,5,6,restrict its difY and 3,7 without difY
        // Child's bottom - node's bottom <= difY < endless
        var minValue = isEmptySwimlane ? -bounds.height + this.minHeight :
          Math.max(swimlaneBounds.bottom - bounds.top - bounds.height + 1, -bounds.height + this.minHeight);
        difY = Math.max(minValue, difY);
      }

      return {
        difX: difX,
        difY: difY,
      };
    },
    /**
     * Get the index in the lanes
     * @returns {Number} order || -1
     */
    index: function() {
      for (var i = 0; i < this.lanes.length; i++) {
        if (this.lanes[i].id == this.id) {
          return i;
        }
      }
      return -1;
    },
  });
  jBME.Horizontalswimlane = jBME.AbstructSwimlane.extend({
    /**
     * Swimlane type
     */
    horizontalSwimlane: true,
    /**
     * All horizontal swimlanes
     */
    lanes: [],
    /**
     * Min value of width
     */
    minWidth: 45,
    /**
     * Min value of height
     */
    minHeight: 15,
    /**
     * The width of title scope
     */
    titleHeight: 40,
    /**
     * Add a horizontal swimlane
     */
    addSwimLane: function(options, source) {
      var position = 0;
      var currentBound = this.getBounds();

      if (options.data && (options.data.position >= 0 || options.data.position <= this.lanes.length)) { // Insert swimlane to specified position
        position = options.data.position;
        delete options.data.position;
      } else { // Insert to the last position
        position = this.lanes.length;
        if (source != 'paste' && source != 'nodecreate') {
          for (var i = 0; i < this.lanes.length; i++) {
            var bound = this.lanes[i].getBounds();
            if (currentBound.top <= bound.top) {
              position = i;
              break;
            }
          }
        }
      }

      if (this.lanes.length == 0) { // Create the first lane.
        this.index() == -1 && this.lanes.push(this);
      } else {
        if (this.lanes.length > 0) {
          if (position != this.lanes.length) { // Insert into the middle of lanes
            // Insert this lane into position
            var insertBound = this.lanes[position].getBounds();
            this.updateBounds({
              left: insertBound.left,
              top: insertBound.top,
            });

            // Change the offset of lanes after this position
            for (var i = position; i < this.lanes.length; i++) {
              var changeBounds = {
                changeTop: this.height,
              };
              this.lanes[i].updateSwimLane(changeBounds);
            }
          } else { // Insert into the last place
            var lastBound = this.lanes[this.lanes.length - 1].getBounds();
            this.updateBounds({
              left: lastBound.left,
              top: lastBound.top + lastBound.height,
            });
          }
        }

        // Keep the same width
        var laneWidth = this.lanes[0].getBounds().width;
        currentBound.width != laneWidth && this.updateBounds({
          width: laneWidth,
        });

        // Insert the id of this lane into lanes array
        this.index() == -1 && this.lanes.splice(position, 0, this);
      }
    },
    /**
     * Delete swimlane and update others.
     */
    removeSwimLane: function() {
      var index = this.index();

      if (index != -1) {
        this.lanes.splice(index, 1);

        // Move the nodes after this node forward
        for (var i = index; i < this.lanes.length; i++) {
          var changeBounds = {
            changeTop: -this.height,
          };
          this.lanes[i].updateSwimLane(changeBounds);
        }
      }
    },
    /**
     * Resize one swimlane and others resize together.
     * @param difX : the distance of horizontally moving
     * @param difY : the distance of vertically moving
     * @param oldBound : the bounds before moving
     * @param newBound : the bounds after moving
     * @param resizedir : the direction of resizing
     */
    resizeSwimLane: function(difX, difY, oldBound, newBound, resizedir) {
      if (difX != this.olddifX) { // Horizontal resizing, horizontal lanes just need to update top and height.
        for (var dxi = 0; dxi < this.lanes.length; dxi++) { // difX resize
          if (this.lanes[dxi].id != this.id) {
            this.lanes[dxi].updateBounds({
              left: this.getBounds().left,
              width: newBound.width || oldBound.width,
            });
          }
        }
        this.oldDifX = difX;
      }

      if (difY != this.oldDifY) { // Vertical resizing, horizontal lanes need to update self and modify others.
        var index = this.index();
        if (newBound.top) { // Resize up
          for (var dyi = 0; dyi < index; dyi++) { // Change the nodes' top before this one.
            var disOfLanes = 0;
            for (var il = dyi; il < index; il++) {
              disOfLanes += this.lanes[il].height;
            }
            var changeBounds = {
              changeTop: newBound.top - disOfLanes - this.lanes[dyi].getBounds().top,
            };
            this.lanes[dyi].updateSwimLane(changeBounds);
          }
        } else if (resizedir >= 4 && resizedir <= 6) { // Resize down
          for (var dyb = index + 1; dyb < this.lanes.length; dyb++) { // Change the nodes' left after this one.
            var disOfLanes = 0;
            for (var ib = index + 1; ib < dyb; ib++) {
              disOfLanes += this.lanes[ib].height;
            }
            var changeBounds = {
              changeTop: oldBound.top + newBound.height + disOfLanes - this.lanes[dyb].getBounds().top,
            };
            this.lanes[dyb].updateSwimLane(changeBounds);
          }
        }
        this.oldDifY = difY;
      }
    },
    /**
     * Update the bounds of swimlane and its subnodes
     * @param changeBounds : {changeLeft:xx, changeTop:xx}
     */
    updateSwimLane: function(changeBounds) {
      // Merge oldBounds and changeBounds
      var containerBounds = this.getBounds();
      changeBounds.changeLeft && (containerBounds.left += changeBounds.changeLeft);
      changeBounds.changeTop && (containerBounds.top += changeBounds.changeTop);
      this.updateBounds(containerBounds);

      if (!Utils.isSupportCanvas()) {
        this.$box.find('.bc_node_text').width(containerBounds.height);
      }

      // Move its children
      var laneChildren = this.getDescendant();
      for (var lid in laneChildren) {
        // Merge oldBounds and changeBounds
        var childBounds = laneChildren[lid].getBounds();
        changeBounds.changeLeft && (childBounds.left += changeBounds.changeLeft);
        changeBounds.changeTop && (childBounds.top += changeBounds.changeTop);
        laneChildren[lid].updateBounds(childBounds);
      }
    },
    /**
     * The scope of resizing
     */
    resizeScope: function(resizedir, difX, difY, bounds) {
      var totalLanesHeight = 0;
      // Sum of lanes' with before this swimlane, for limiting its resizing left value.
      for (var i = 0; i < this.index(); i++) {
        totalLanesHeight += this.lanes[i].getBounds().height;
      }

      // Resizing left and right
      var children = this.getDescendant();
      for (var cid in children) {
        children[cid].$box.css('display') == 'none' && delete children[cid];
      }
      // current siwmlane's children
      var ensembleBound = Utils.getEnsembleBounds(children);
      var isEmpty = $.isEmptyObject(ensembleBound);

      // Resizing top and bottom
      for (var i = 0; i < this.lanes.length; i++) {
        children = $.extend(children, this.lanes[i].getDescendant());
      }
      // all swimlane's children
      var swimlaneBounds = Utils.getEnsembleBounds(children);
      var isEmptySwimlane = $.isEmptyObject(swimlaneBounds);

      if (resizedir >= 6) { // anchorid == 6,7,8
        // (move left : node's left) <= difX <= (move right : min child's left - node's left)
        // Math.min : move left && Math.max : move right
        var maxValue = isEmptySwimlane ? bounds.width - this.minWidth :
          Math.min(swimlaneBounds.left - bounds.left - 1, bounds.width - this.minWidth);
        difX = Math.min(Math.max(-bounds.left, difX), maxValue);
      } else if (resizedir >= 2 || resizedir <= 4) { // anchorid == 2,3,4,restrict its difX and 1,5 without difX
        // Child's right - node's right <= difX < endless
        var minValue = isEmptySwimlane ? -bounds.width + this.minWidth :
          Math.max(swimlaneBounds.right - (bounds.left + bounds.width) + 1, -bounds.width + this.minWidth);
        difX = Math.max(minValue, difX);
      }

      if (resizedir == 1 || resizedir == 2 || resizedir == 8) { // anchorid == 1,2,8
        // - node's top <= difY <= child's top - node's top
        var maxValue = isEmpty ? bounds.height - this.minHeight :
          Math.min(ensembleBound.top - bounds.top - 1, bounds.height - this.minHeight);
        difY = Math.min(Math.max(-bounds.top + totalLanesHeight, difY), maxValue);
      } else if (resizedir >= 4 || resizedir <= 6) { // anchorid == 4,5,6,restrict its difY and 3,7 without difY
        // Child's bottom - node's bottom <= difY < endless
        var minValue = isEmpty ? -bounds.height + this.minHeight :
          Math.max(ensembleBound.bottom - bounds.top - bounds.height + 1, -bounds.height + this.minHeight);
        difY = Math.max(minValue, difY);
      }

      return {
        difX: difX,
        difY: difY,
      };
    },
    /**
     * Get the index in the lanes
     * @returns {Number} order || -1
     */
    index: function() {
      for (var i = 0; i < this.lanes.length; i++) {
        if (this.lanes[i].id == this.id) {
          return i;
        }
      }
      return -1;
    },
  });
  /**
   * Path
   */
  jBME.Path = jBME.Clazz.extend({
    construct: function(id, options, $pathbox, zoomargs) {
      // Cache
      this.id = id;
      this.$box = $pathbox || $(document.getElementById(this.id));
      $pathbox && (this.isDraw = true);

      // To support IE 7,8,because the element of canvas created by jQuery is not known in IE 7,8,
      // so we create the element of canvas by the method of HTML.
      if (!Utils.isSupportCanvas()) {
        var canvas = document.createElement('canvas');
        this.$box[0].replaceChild(canvas, this.$box[0].firstChild);
      }

      this.$canvas = this.$box.children('canvas');
      this.canvas = this.$canvas[0];

      // 'data' holds parts of options, stores only 'my' options that are different with the default ones.
      this.options = $.extend(true, {}, jBME.Diagram.options.path, options);
      this.options = this.mergeOptions(options, this.options);

      this.disabled = this.options.disabled === true;
      if (this.disabled) {
        this.options.viewmode = 'view';
        this.$box.addClass('disabled');
      }

      this.jType = jBME.Path.Type[this.options.figures[0].type] || jBME.Path.Type['default'];
      this.jSource = null;
      this.jTarget = null;
      this.hoverBend = null;
      this.textnodes = {};
      this.textBoxes = {};

      // support for "source:sourceAnchor"
      if (!options.sourceAnchor && this.options.source.split(':')[1]) {
        this.options.sourceAnchor = this.options.source.split(':')[1];
      }
      this.options.source = this.options.source.split(':')[0];

      if (!options.targetAnchor && this.options.target.split(':')[1]) {
        this.options.targetAnchor = this.options.target.split(':')[1] || this.options.targetAnchor;
      }
      this.options.target = this.options.target.split(':')[0];

      // The actually drawing controls contains the last point([1,1])
      this.options.controls.length == 0 && (this.options.controls = $.extend([], this.jType.controls));

      this.options.zoomScale = zoomargs || 1;
    },
    ispath: true,
    update: function(options) {
      // update disabled
      if (options) {
        if (typeof options.disabled == 'boolean') {
          this.toggleDisabled(options.disabled);
        }

        if (!$.isEmptyObject(options.textareas)) {
          this.options.textareas = $.extend(true, {}, options.textareas);
        } else {
          this.options.textareas = {};
        }

        if (options.zoomScale) {
          this.options.zoomScale = options.zoomScale;
        }
      }

      this.updateBounds();
    },
    /**
     * Update the bounds {|left,|top,|width,|height}, and then redraw.
     */
    updateBounds: function(routeList) {
      this.sourceAnchor = this.jSource.getAnchorPosition(this.options.sourceAnchor, this.jTarget.getCenterPosition());
      this.targetAnchor = this.jTarget.getAnchorPosition(this.options.targetAnchor, this.jSource.getCenterPosition());
      this.mirrorx = (this.sourceAnchor.left < this.targetAnchor.left ? 1.0 : -1.0);
      this.mirrory = (this.sourceAnchor.top < this.targetAnchor.top ? 1.0 : -1.0);

      // bounds, canvas bounds, canvas bounds
      this.bounds = {
        left: Math.min(this.sourceAnchor.left, this.targetAnchor.left),
        top: Math.min(this.sourceAnchor.top, this.targetAnchor.top),
        width: Math.abs(this.sourceAnchor.left - this.targetAnchor.left) || ZERO,
        height: Math.abs(this.sourceAnchor.top - this.targetAnchor.top) || ZERO,
      };
      this.$box.css(this.bounds);

      this.sourceX = this.sourceAnchor.left - this.bounds.left;
      this.sourceY = this.sourceAnchor.top - this.bounds.top;
      this.targetX = this.targetAnchor.left - this.bounds.left;
      this.targetY = this.targetAnchor.top - this.bounds.top;

      if (this.isOrthogonalEdge) {
        var nodeMargin = (this.options.nodeMargin || 0.5) * 2;
        var nodePadding = this.options.nodePadding || 14;

        // Auto route
        if (routeList) {
          this.options.routeList = routeList;
        }

        // Generate controls for drawing arrow
        this.options.controls = this.getControls(this.options.routeList);

        // Update canvas bounds
        var elbowBounds = this.getElbowBounds(this.options.routeList);
        this.updateCanvas(elbowBounds);
      } else {
        this.updateCanvasBounds();
      }

      // Update the position of textnodes on this path
      this.updateTextNodeBounds();

      // draw.
      !this.isDraw && this.draw();
      this.isDraw = false;

      var redrawSource = this.isselected && (this.sourceAnchorid != this.sourceAnchor.anchorid);
      var redrawTarget = this.isselected && (this.targetAnchorid != this.targetAnchor.anchorid);
      this.sourceAnchorid = this.sourceAnchor.anchorid;
      this.targetAnchorid = this.targetAnchor.anchorid;
      redrawSource && this.jSource.draw();
      redrawTarget && this.jTarget.draw();

      this.updateConnectionText();

      return this;
    },
    updateCanvasBounds: function() {
      var outer = this.getVisibleBound();
      var margin = this.options.margin * this.options.zoomScale;
      this.canvasMargin = {
        'margin-left': outer[0] * this.bounds.width - margin,
        'margin-top': outer[1] * this.bounds.height - margin,
        'margin-right': -(outer[2] * this.bounds.width + margin),
        'margin-bottom': -(outer[3] * this.bounds.height + margin),
      };

      this.$canvas.css(this.canvasMargin);

      // Changing width and height of canvas will affect drawing perfermance.
      // But IE7/8 must update canvas width and height, or drawing nothing.
      if (!Utils.isSupportCanvas() ||
        Math.abs((this.bounds.width - this.canvasMargin['margin-left'] - this.canvasMargin['margin-right']) - this.canvas.width) > 0.01) {
        this.canvas.width = this.bounds.width - this.canvasMargin['margin-left'] - this.canvasMargin['margin-right'];
      }
      if (!Utils.isSupportCanvas() ||
        Math.abs((this.bounds.height - this.canvasMargin['margin-top'] - this.canvasMargin['margin-bottom']) - this.canvas.height) > 0.01) {
        this.canvas.height = this.bounds.height - this.canvasMargin['margin-top'] - this.canvasMargin['margin-bottom'];
      }

      this.canvasManager(this.canvas);

      return this;
    },
    canvasManager: function(canvas) {
      // Because the context of the canvas does not initialize the element by dynamic creating,so we need to initialize.
      if (canvas && canvas && !canvas.getContext) {
        window.G_vmlCanvasManager.initElement(canvas);
      }
      if (canvas && canvas.getContext && !this.context) {
        this.context = canvas.getContext('2d');
      }
      if (Utils.isUseExcanvas()) {
        canvas.firstChild.style.width = canvas.width;
        canvas.firstChild.style.height = canvas.height;
      }
      return canvas;
    },
    /**
     * Update the textndoes' position of this path when we drag the points of this path(jSource, jTarget, bends).
     */
    updateTextNodeBounds: function() {
      if (!$.isEmptyObject(this.textnodes)) {
        var pointsOfPath = this.getPointsOfPath();
        for (var tid in this.textnodes) {
          var indexOfPoints = Math.floor(this.textnodes[tid].options.onpath.scale * pointsOfPath.length);
          indexOfPoints += (indexOfPoints % 2);
          var newPointX = pointsOfPath[indexOfPoints];
          var newPointY = pointsOfPath[indexOfPoints + 1];
          var newTextBounds = {
            left: newPointX - this.textnodes[tid].width / 2,
            top: newPointY - this.textnodes[tid].height / 2,
          };
          this.textnodes[tid].updateBounds(newTextBounds);
        }
      }
    },
    /**
     * Update connection text position when dragging node.
     */
    updateConnectionText: function() {
      var textareas = this.options.textareas;
      var textBoxes = this.textBoxes;

      if (!$.isEmptyObject(textareas)) {
        var points = this.getPointsOfPath();
        var pathBounds = this.bounds;

        for (var name in textareas) {
          if (textareas[name] == null) { // The deleted textarea will be null.
            continue;
          }

          var index = Math.floor(textareas[name].location * points.length);

          index += (index % 2);

          var positionX = points[index];
          var positionY = points[index + 1];
          $textBox = this.textBoxes[name],
          content = textareas[name].value;

          var textBounds = Utils.getWidthHeight(content);
          var width = textBounds.width;
          var height = textBounds.height;

          // To support IE 7,8.To convey the '\n'.
          if ($.browser.msie && parseInt($.browser.version) == 8) {
            content = content.replace(/(\n)/gm, '\n');
          } else if ($.browser.msie && parseInt($.browser.version) == 7) {
            content = content.replace(/(\n)/gm, '\r');
          }

          var leftValue = Utils.toFixed((positionX - width / 2 - pathBounds.left), 2) + 'px';
          var topValue = Utils.toFixed((positionY - height / 2 - pathBounds.top), 2) + 'px';
          var bounds = {
            left: leftValue,
            top: topValue,
          };

          if (!$textBox && content == '') {
            continue;
          } else if (!$textBox && content != '') {
            style = ['left:', leftValue, ';top:', topValue, ';'].join(''),
            this.textBoxes[name] = jBME.Diagram.connectionTextPlugin.prototype.createDOM(this, style, content, name);
          } else if (content == '') {
            this.options.textareas[name] = null;
            delete this.textBoxes[name];
            $textBox.remove();
          } else {
            var $text = $textBox.children('pre');
            $text.text(content);
            $textBox.css(bounds);
          }
        }
      }

      for (var item in textBoxes) {
        if (!textareas[item]) {
          textBoxes[item].remove();
          delete this.textBoxes[item];
        }
      }
    },
    /**
     * Return the bounds[left,top,width,height] relative to the given container or the offsetParent by default.
     */
    getBounds: function($relativeto /* [optional]*/) {
      if ($relativeto) {
        var c = $relativeto.offset();
        var b = this.$box.offset();
        return {
          left: b.left - c.left,
          top: b.top - c.top,
          width: this.bounds.width,
          height: this.bounds.height,
        };
      } else {
        return this.bounds;
      }
    },
    /**
     * Return the inner position relative to the path, by the give offset relative to the document(page)
     */
    getOffsetToElement: function(pageEvent) {
      var offset = this.$box.offset();
      return {
        left: pageEvent.pageX - offset.left,
        top: pageEvent.pageY - offset.top,
      };
    },
    /**
     * Return the closest point and its scale(index/length) in path array.
     * scale for dragging path to change textnode's position.
     */
    getClosestPointFromPath: function(pointx, pointy) {
      var allPoints = this.getPointsOfPath();
      var closestPoint = [];
      var closestNum = Utils.manhattanDistance(pointx, pointy, allPoints[0], allPoints[1]);

      // Get closest point from event's position
      for (var i = 0; i < allPoints.length; i += 2) {
        var distance = Utils.manhattanDistance(pointx, pointy, allPoints[i], allPoints[i + 1]);
        if (distance <= closestNum) {
          closestPoint.splice(0, 2);
          closestPoint.push(allPoints[i], allPoints[i + 1]);
          closestNum = distance;
        }
      }

      // Save scale
      var pointPosition = Math.max(Utils.indexOf(allPoints, closestPoint[0]), Utils.indexOf(allPoints, closestPoint[1]));
      closestPoint.push(pointPosition / allPoints.length);

      var points = this.getAbsoluteRouteList();
      var fontControl = 0;
      for (var j = 2; j < points.length; j += 2) {
        fontControl = Math.max(Utils.indexOf(allPoints, points[j]), Utils.indexOf(allPoints, points[j + 1])) / allPoints.length;
        if (fontControl > closestPoint[2]) {
          closestPoint[3] = j / 2 - 1;
          break;
        }
      }

      closestPoint[0] = Utils.toFixed(closestPoint[0], 2);
      closestPoint[1] = Utils.toFixed(closestPoint[1], 2);

      return closestPoint;
    },
    /**
     * To use old options and new options together.
     * Modified at 2013/2/28
     */
    mergeOptions: function(orgnlOpts, mergeOpts) {
      // merget context
      if (orgnlOpts.normal) {
        if (!(orgnlOpts.figures && orgnlOpts.figures[0] && orgnlOpts.figures[0].context)) {
          $.extend(mergeOpts.figures[0].context, orgnlOpts.normal);
        }
      }

      // merge type
      if (orgnlOpts.type) {
        if (!(orgnlOpts.figures && orgnlOpts.figures[0] && orgnlOpts.figures[0].type)) {
          mergeOpts.figures[0].type = orgnlOpts.type;
        }
      }

      return mergeOpts;
    },
    /**
     * When create textnode on path, we should add textnodes on this path.
     */
    setTextOnPath: function(jNode) {
      this.textnodes[jNode.id] = jNode;
    },
    /**
     * When remove textnode on path, we should remove textnode from this path.
     */
    removeTextOnPath: function(jNodeId) {
      delete this.textnodes[jNodeId];
    },
    /**
     * Set End-Node of path
     * @param which, end name: source|target
     * @param jElement, jSource or jTarget, can be null to clear.
     * @param anchor, end anchor.
     */
    setEnd: function(which /* source|target*/, jElement, anchor /* [optional]*/, routeList) {
      // Update options
      this.options[which] = (jElement || 0).id;
      if (anchor) {
        this.options[which + 'Anchor'] = anchor;
      }

      // Update Object
      var jName = 'j' + Utils.capitalize(which);
      if (this[jName] && (!this.jSource || !this.jTarget || this.jSource.id != this.jTarget.id)) {
        delete this[jName].paths[this.id];
      }

      this[jName] = jElement;
      this[jName] && (jElement.paths[this.id] = this);

      // Update "view"
      this.jSource && this.jTarget && this.updateBounds(null, routeList);
      return this;
    },
    setSource: function(jElement, anchor /* [optional]*/) {
      return this.setEnd('source', jElement, anchor);
    },
    setTarget: function(jElement, anchor /* [optional]*/, routeList) {
      return this.setEnd('target', jElement, anchor, routeList);
    },
    toggleHover: function(hover) {
      this.ishover = (hover == undefined ? !this.ishover : hover);
      this.$box.toggleClass('hover', this.ishover);
      this.draw();
      return this;
    },
    toggleReconnect: function(reconnect) {
      this.$box.toggleClass('reconnect', reconnect);
    },
    toggleSelect: function(select /* [optional]*/) {
      this.isselected = (select == undefined ? !this.isselected : select);
      this.$box.toggleClass('selected', this.isselected);

      // draw ends.
      this.draw();
      this.jSource.draw();
      this.jTarget.draw();
      return this;
    },
    toggleDisabled: function(disabled /* [optional]*/) {
      this.disabled = (disabled == undefined ? !this.disabled : disabled);
      this.options.disabled = this.disabled === true;
      this.draw();

      if (this.disabled) {
        this.options.viewmode = 'view';
        !this.$box.hasClass('disabled') && this.$box.addClass('disabled');
      } else {
        this.options.viewmode = 'edit';
        this.$box.hasClass('disabled') && this.$box.removeClass('disabled');
      }
      return this;
    },
    isOnShape: function(pageEvent) {
      var offset = this.$canvas.offset();
      var x = pageEvent.pageX - offset.left;
      var y = pageEvent.pageY - offset.top;

      // Mouse position is in canvas scope.
      if (this.context &&
        x >= 0 && x < this.canvas.width &&
        y >= 0 && y < this.canvas.height) {
        if (this.context.getImageData) { // Canvas API: getImageData to see whether this position is with some color.
          var isOpaque = false;
          try {
            // 3, 3 is width and height of scope to get color.
            var data = this.context.getImageData(x, y, 3, 3).data;
            for (var i = 0; i < data.length; i += 4) {
              if (data[i + 3] > 0) {
                isOpaque = true;
                break;
              }
            }
          } catch (e) {
            isOpaque = false;
          }

          return isOpaque;
        } else { // To support IE 7,8,because the method of getImageData() is not supported by using excanvas.
          return ($.nodeName(pageEvent.target, 'shape') || this.isOnPath(pageEvent));
        }

        return false;
      }
    },
    /**
     * Zoomin or zoomout of path.
     * @param zoomScale : the scale of zoom vale.
     */
    zoom: function(zoomScale) {
      this.options.zoomScale = zoomScale;
      this.updateBounds();
    },
    /**
     * To judge the position of the mouse in the diagram ,whether it is on the path,
     *
     */
    isOnPath: function(pageEvent) {
      var controlsPoint = [];
      if (this.jType.name == 'quadratic' || this.jType.name == 'bezier') {
        controlsPoint = this.getPointsOfPath();
      } else {
        controlsPoint = this.getAbsoluteRouteList();
      }

      var offset = this.$box.parent().parent().offset();
      var mousePositionLeft = pageEvent.pageX - offset.left;
      var mousePositionTop = pageEvent.pageY - offset.top;
      for (var i = 0; i < controlsPoint.length - 2; i += 2) {
        if ((Math.max(ZERO, Math.min(controlsPoint[i] - 1, controlsPoint[i + 2] - 1)) <= mousePositionLeft &&
            mousePositionLeft <= (Math.max(controlsPoint[i] + 1, controlsPoint[i + 2] + 1))) &&
          (Math.max(ZERO, Math.min(controlsPoint[i + 1] - 1, controlsPoint[i + 3] - 1)) < mousePositionTop &&
            mousePositionTop < (Math.max(controlsPoint[i + 1] + 1, controlsPoint[i + 3] + 1)))) {
          return Utils.isOnPath(controlsPoint[i], controlsPoint[i + 1], controlsPoint[i + 2],
            controlsPoint[i + 3], mousePositionLeft, mousePositionTop);
        }
      }
      return false;
    },
    /**
     * Return all the control points for path, CONTAINing start and end points, units in [0,1]
     * Note: for curves, control points may not lie on the path(curve).
     * eg:[0,0, cp1x,cp1y, cp2x,cp2y, 1,1]
     */
    getPoints: function() {
      return [0, 0].concat(this.options.controls, [1, 1]);
    },
    /**
     * Get the route list according to diagram $box.
     */
    getAbsoluteRouteList: function() {
      var elbowPoints = this.options.elbowPoints;
      var bounds = this.getBounds();

      if (this.isOrthogonalEdge) {
        var routeList = this.options.routeList;
        var result = [];
        for (var n = 0; n < routeList.length; n += 2) {
          result.push(routeList[n]);
          result.push(routeList[n + 1]);
        }
        return result;
      } else if (elbowPoints.length != 0) {
        var result = [this.sourceX + bounds.left, this.sourceY + bounds.top];
        for (var j = 0; j < elbowPoints.length; j++) {
          result.push(elbowPoints[j].x);
          result.push(elbowPoints[j].y);
        }
        return result.concat([this.targetX + bounds.left, this.targetY + bounds.top]);
      } else {
        var controls = this.options.controls;
        var result = [this.sourceX + bounds.left, this.sourceY + bounds.top];
        for (var i = 0; i < controls.length; i += 2) {
          result.push(Utils.toFixed(this.sourceX + controls[i] * bounds.width * this.mirrorx + bounds.left, 2));
          result.push(Utils.toFixed(this.sourceY + controls[i + 1] * bounds.height * this.mirrory + bounds.top, 2));
        }
        return result.concat([this.targetX + bounds.left, this.targetY + bounds.top]);
      }
    },
    /**
     * Get the route list according to path's canvas.
     */
    getRelativeRouteList: function() {
      var elbowPoints = this.options.elbowPoints;
      var bounds = this.getBounds();

      if (this.isOrthogonalEdge) {
        var routeList = this.options.routeList;
        var result = [];
        for (var n = 0; n < routeList.length; n += 2) {
          result.push(routeList[0] - bounds.left);
          result.push(routeList[1] - bounds.top);
        }
        return result;
      } else if (elbowPoints.length != 0) {
        var result = [this.sourceX, this.sourceY];
        for (var j = 0; j < elbowPoints.length; j++) {
          result.push(elbowPoints[j].x - bounds.left);
          result.push(elbowPoints[j].y - bounds.top);
        }
        return result.concat([this.targetX, this.targetY]);
      } else {
        var controls = this.options.controls;
        var result = [this.sourceX, this.sourceY];
        for (var i = 0; i < controls.length; i += 2) {
          result.push(Utils.toFixed(this.sourceX + controls[i] * bounds.width * this.mirrorx, 2));
          result.push(Utils.toFixed(this.sourceY + controls[i + 1] * bounds.height * this.mirrory, 2));
        }
        return result.concat([this.targetX, this.targetY]);
      }
    },
    /**
     * Return all the control points for path, CONTAINing start and end points, according to offset of points.
     * Note: for curves, control points may not lie on the path(curve).
     * eg:[100,100, cp1x,cp1y, cp2x,cp2y, 800,800]
     */
    getPointsFromOffset: function() {
      var elbowPoints = this.options.elbowPoints;
      if (elbowPoints.length == 0) {
        var points = this.getPoints();
        for (var i = 0; i < points.length; i += 2) {
          // When mirrorx or mirrory = 1 ,Math.cos((1 + this.mirrorx) * Math.PI/4) = 0;mirrorx or mirrory = -1,Math.cos((1 + this.mirrorx) * Math.PI/4) = 1
          points[i] = this.bounds.left + (Math.max(ZERO, Math.cos((1 + this.mirrorx) * Math.PI / 4)) + points[i] * this.mirrorx) * this.bounds.width;
          points[i + 1] = this.bounds.top + (Math.max(ZERO, Math.cos((1 + this.mirrory) * Math.PI / 4)) + points[i + 1] * this.mirrory) * this.bounds.height;
        }
        return points;
      } else {
        var bounds = this.getBounds();
        var startPoint = [bounds.left + Math.max(-this.mirrorx, 0) * bounds.width, bounds.top + Math.max(-this.mirrory, 0) * bounds.height];
        var endPoint = [bounds.left + Math.max(this.mirrorx, 0) * bounds.width, bounds.top + Math.max(this.mirrory, 0) * bounds.height];
        return startPoint.concat(elbowPoints, endPoint);
      }
    },
    /**
     * @param distance : the distance of two points in line
     * @param points : origin point,control points,terminal point
     * return : the array of all points of this path which the distance of two points is param.
     */
    getPointsOfPath: function() {
      var points = this.getAbsoluteRouteList();
      var pointsOfPath = new Array();
      var distance = 5; // THe number 5 is the distance of two points on path.

      if (this.jType.name == 'quadratic' || this.jType.name == 'bezier') { // curve
        // Prepare values of x and y respectively.
        var pointx = new Array();
        var pointy = new Array();
        var refence = new Array();
        for (var j = 0; j < points.length; j += 2) {
          pointx.push(points[j]);
          pointy.push(points[j + 1]);
        }

        // Use values of x and y, then repeat t with 0.001 less enough.
        // When the distance of two points on curve less than given distance, we remember this point.
        pointsOfPath.push(points[0], points[1]); // start point
        refence.push(points[0], points[1]);
        for (var t = 0.001; t < 1; t += 0.03) {
          var nextPointx = Utils.bezier(pointx, t);
          var nextPointy = Utils.bezier(pointy, t);
          var apart = Utils.manhattanDistance(refence[0], refence[1], nextPointx, nextPointy); // The real distance of two points
          if (apart > distance) {
            pointsOfPath.push(nextPointx, nextPointy);
            refence[0] = nextPointx;
            refence[1] = nextPointy;
          }
        }
        pointsOfPath.push(points[points.length - 2], points[points.length - 1]); // end point
      } else { // When this path is elbow, we need cut it into several lines.
        for (var i = 0; i < points.length; i += 2) {
          // line
          pointsOfPath.push(points[i], points[i + 1]); // start point
          var totleLength = Utils.manhattanDistance(points[i], points[i + 1], points[i + 2], points[i + 3]); // The length of one line
          while (totleLength > distance) { // If the distance of two points is less than given distance, get one point from origin point which distance is given distance.
            points[i] += (points[i + 2] - points[i]) * distance / totleLength;
            points[i + 1] += (points[i + 3] - points[i + 1]) * distance / totleLength;
            pointsOfPath.push(points[i], points[i + 1]);
            totleLength = Utils.manhattanDistance(points[i], points[i + 1], points[i + 2], points[i + 3]);
          }
        }
        pointsOfPath.push(points[points.length - 2], points[points.length - 1]); // end point
      }
      return pointsOfPath;
    },
    /**
     * Return the bend points for path according to the given control points, units in [0,1]
     * NOT CONTAINing start or end points.
     * Note: bend points should always lie on the path(include curves).
     * eg:[bp1x,bp1y, bp2x,bp2y]
     */
    getBends: function() {
      return this.jType.getBends(this);
    },
    /**
     * Hit test the hover bend point to drag and change the trend of path.
     * We mark a bend point as hover when we move the mouse near a bend point,
     *   and when the distance between the two is within a certain range(eg:10 pixels).
     * @return structured information of a hoverBend; null if no bend point is hover.
     */
    hitTestHoverBend: function(pageEvent) {
      // Reset the hover bend when given pageEvent is null.
      if (pageEvent == null) {
        this.hoverBend = null;
        return false;
      }

      var pts = this.getRelativeRouteList();
      var offset = this.getOffsetToElement(pageEvent);

      if (this.jType.name != 'bezier' && this.jType.name != 'quadratic') {
        for (var i = 2; i < pts.length - 2; i += 2) { // Which bend is cloest to dragging postion
          if (Utils.distance(pts[i], pts[i + 1], offset.left, offset.top) < 10) {
            this.hoverBend = {
              index: i / 2 - 1,
              bends: this.getBends(),
              p: this.getPoints(),
            };
            return true;
          }
        }
      } else { // Curve's controls are different with bends.
        var bounds = this.getBounds();
        for (var j = 0, bends = this.getBends(); j < bends.length; j += 2) {
          var p1 = this.sourceX + bends[j] * bounds.width * this.mirrorx;
          var p2 = this.sourceY + bends[j + 1] * bounds.height * this.mirrory;
          if (Utils.distance(p1, p2, offset.left, offset.top) < 10) {
            this.hoverBend = {
              index: j / 2,
              bends: this.getBends(),
              p: this.getPoints(),
            };
            return true;
          }
        }
      }

      this.hoverBend = null;
      return false;
    },
    /**
     * Whether drag source or target
     * @param pageEvent : selectDrag event
     * @returns source || target || null
     */
    hitPathPoint: function(pageEvent, responseDistance) {
      // Event position
      var pos = this.jSource.getPositionWithScroll(pageEvent);

      var anchorPosSource = this.jSource.getAnchorPosition(this.sourceAnchorid);
      var distanceSource = Utils.distance(pos.left, pos.top, anchorPosSource.left, anchorPosSource.top);
      var anchorPosTarget = this.jTarget.getAnchorPosition(this.targetAnchorid);
      var distanceTarget = Utils.distance(pos.left, pos.top, anchorPosTarget.left, anchorPosTarget.top);

      var jPoint = null;
      if (distanceSource < distanceTarget && distanceSource < responseDistance) {
        jPoint = 'source';
      } else if (distanceSource >= distanceTarget && distanceTarget < responseDistance) {
        jPoint = 'target';
      }
      return jPoint;
    },
    moveHoverBend: function(difX, difY, position) {
      // prepare a short name and check.
      if (this.jType.name != 'bezier' && this.jType.name != 'quadratic') { // Line, elbow line...
        if (this.options.elbowPoints.length > 0) {
          this.options.elbowPoints[position[3]] = {
            x: position[0] + difX,
            y: position[1] + difY,
          };
        }
      } else { // Curve
        var hover = this.hoverBend;
        if (hover == null) {
          return;
        }

        // Scale the bend changes of pixel into coefficient in [0,1].
        // New control point (x,y) in [0,1] for the given 't' coefficient
        var newBend = [hover.bends[hover.index * 2] + difX * (this.mirrorx / this.bounds.width),
          hover.bends[hover.index * 2 + 1] + difY * (this.mirrory / this.bounds.height),
        ];
        var newControl = this.jType.getControlByBend(newBend, hover.p, hover.index + 1);
        this.options.controls[hover.index * 2] = newControl[0];
        this.options.controls[hover.index * 2 + 1] = newControl[1];
      }

      this.updateCanvasBounds();
      this.draw();
    },
    getVisibleBound: function() {
      // margin: left,top,right,bottom
      var outer = [0, 0, 0, 0];
      var elbows = this.options.elbowPoints;

      if (elbows.length == 0) {
        var transformX = Math.abs(Math.min(this.mirrorx, 0));
        var transformY = Math.abs(Math.min(this.mirrory, 0));
        var points = this.getPoints();
        var controls = points.slice(2, points.length - 2);

        for (var i = 0; i < controls.length; ++i) {
          var controlsValue = controls[i];
          if (i % 2 == 0) {
            controlsValue = (controlsValue * this.mirrorx + transformX);
          } else {
            controlsValue = (controlsValue * this.mirrory + transformY);
          }

          if (controlsValue > 1) {
            outer[2 + i % 2] = Math.max(outer[2 + i % 2], controlsValue - 1);
          } else if (controlsValue < 0) {
            outer[i % 2] = Math.min(outer[i % 2], controlsValue);
          }
        }
      } else { // For absolute bends
        var bounds = this.getBounds();
        for (var j = 0; j < elbows.length; j++) {
          if (elbows[j].x < bounds.left) { // marin-left
            outer[0] = Math.min(outer[0], (elbows[j].x - bounds.left) / bounds.width);
          } else if (elbows[j].x > (bounds.left + bounds.width)) { // marin-right
            outer[2] = Math.max(outer[2], (elbows[j].x - bounds.left - bounds.width) / bounds.width);
          }

          if (elbows[j].y < bounds.top) { // marin-left
            outer[1] = Math.min(outer[1], (elbows[j].y - bounds.top) / bounds.height);
          } else if (elbows[j].y > (bounds.top + bounds.height)) { // marin-right
            outer[3] = Math.max(outer[3], (elbows[j].y - bounds.top - bounds.height) / bounds.height);
          }
        }
      }

      return outer;
    },
    /**
     * Clear info of path which is need to copy.
     */
    prepareCopy: function() {
      this.options.pasteSource = null;
      this.options.pasteTarget = null;
      this.options.newSource = null;
      this.options.newTarget = null;
      this.options.textnodesArray = [];
    },
    /**
     * Remember source and target of path to prevent it to be removed when we use cut method.
     */
    onCopy: function() {
      this.options.pasteSource = this.options.source;
      this.options.pasteTarget = this.options.target;
      this.options.textnodesArray = [];
    },
    /**
     * Construct new options to paste this path.
     */
    onPaste: function() {
      var newOpts = $.extend(true, {}, this.options);
      newOpts.id = null;
      newOpts.source = newOpts.newSource;
      newOpts.target = newOpts.newTarget;
      newOpts.pasteSource = newOpts.pasteTarget = newOpts.newSource = newOpts.newTarget = null;
      return newOpts;
    },
    toJSON: function() {
      var data = this.getJSONData();
      return JSON.stringify(data);
    },
    getJSONData: function() {
      var data = Utils.getChanged(this.options, jBME.Diagram.options.path);
      data.id = this.id;
      data.sourceAnchor = this.sourceAnchorid;
      data.targetAnchor = this.targetAnchorid;
      if (Utils.equals(data.controls, this.jType.controls)) { // Remove redundant information which can recover by default.
        delete data.controls;
      }

      for (var name in data.textareas) {
        if (data.textareas[name] == null) {
          continue;
        }
        data.textareas[name].value = data.textareas[name].value.replace(/(\r\n|\n|\r)/gm, '\\n').replace(/\'/gm, '\\\'');
      }
      return data;
    },
    /**
     * The main draw function.
     */
    draw: function(context /* [optional]*/, options /* [optional]*/) {
      context = context || this.context;
      options = options || this.options;

      if (context == null) {
        return;
      }

      // prepare
      context.save();
      context.clearRect(0, 0, this.canvas.width, this.canvas.height);
      context.translate(-this.canvasMargin['margin-left'], -this.canvasMargin['margin-top']);

      // draw path
      context.beginPath();
      context.save();

      if (this.disabled) {
        $.extend(context, options.disabledContext);
      } else {
        $.extend(context, this.isselected ? options.selected : options.figures[0].context);
      }

      this.jType.figure.call(this, context);
      context.lineWidth = (this.isselected ? options.selected.lineWidth : options.figures[0].context.lineWidth) * this.options.zoomScale;
      context.stroke();
      context.restore();

      // draw arrow
      this.drawArrow(context, options);

      // draw bend points on curve.
      if (this.isselected) {
        this.drawBends(context, options);
      }

      // DONE.
      context.restore();
    },
    drawArrow: function(context, options) {
      var sourceFromX = this.targetX;
      var sourceFromY = this.targetY;
      var targetFromX = this.sourceX;
      var targetFromY = this.sourceY;
      var bounds = this.getBounds();
      var elbowPoints = this.options.elbowPoints;

      if (this.isOrthogonalEdge) { // Orthogonal edge can get reference node by routeList.
        var routeList = this.options.routeList;
        var routeLen = routeList.length;
        sourceFromX = routeList[2] - bounds.left;
        sourceFromY = routeList[3] - bounds.top;
        targetFromX = routeList[routeLen - 4] - bounds.left;
        targetFromY = routeList[routeLen - 3] - bounds.top;
      } else if (elbowPoints.length != 0) { // ElbowPoints
        var elbowLen = elbowPoints.length;
        sourceFromX = elbowPoints[0].x - bounds.left;
        sourceFromY = elbowPoints[0].y - bounds.top;
        targetFromX = elbowPoints[elbowLen - 1].x - bounds.left;
        targetFromY = elbowPoints[elbowLen - 1].y - bounds.top;
      } else { // Other Node can get from controls.
        var controls = this.options.controls;
        var len = controls.length;
        if (len > 1) {
          sourceFromX = this.sourceX + controls[0] * bounds.width * this.mirrorx;
          sourceFromY = this.sourceY + controls[1] * bounds.height * this.mirrory;
          targetFromX = this.sourceX + controls[len - 2] * bounds.width * this.mirrorx;
          targetFromY = this.sourceY + controls[len - 1] * bounds.height * this.mirrory;
        }
      }

      if (options.sourceStyle !== 'none') {
        this.drawArrowImpl(context, sourceFromX, sourceFromY, this.sourceX, this.sourceY, options.sourceStyle, options);
      }
      if (options.targetStyle !== 'none') {
        this.drawArrowImpl(context, targetFromX, targetFromY, this.targetX, this.targetY, options.targetStyle, options);
      }
    },
    /**
     * [Draw arrow on canvas tag]
     *  angle \  ______\
     * (degree)/ ,    / , (fromex,fromy)---->(tox,toy)
     */
    drawArrowImpl: function(context, fromx, fromy, tox, toy, style, options) {
      var angle = Math.atan2(toy - fromy, tox - fromx);
      var headlen = options.arrow.headlen * this.options.zoomScale;
      var arrow = jBME.Path.Arrow[style] || jBME.Path.Arrow['arrow'];

      context.beginPath();
      context.save();

      arrow.drawArrow.call(this, context, tox, toy, angle, headlen, options);

      if (this.disabled) {
        $.extend(context, options.disabledContext);
      } else {
        $.extend(context, this.isselected ? options.selected : options.figures[0].context);
      }
      context.lineWidth = (this.isselected ? options.selected.lineWidth : options.figures[0].context.lineWidth) * this.options.zoomScale;
      context.stroke();
      context.restore();
    },
    /**
     * Draw all bends of edge.
     */
    drawBends: function(context, options) {
      var bounds = this.getBounds();
      var elbowPoints = this.options.elbowPoints;

      context.save();
      $.extend(context, options.controlStyle);

      if (this.isOrthogonalEdge) { // Orthogonal Edge
        var routeList = this.options.routeList;
        for (var i = 2; i < routeList.length - 2; i += 2) {
          var x = routeList[i] - bounds.left;
          var y = routeList[i + 1] - bounds.top;
          this.drawBendImpl(context, x, y, options);
        }
      } else if (elbowPoints.length != 0) { // Width elbowPoints.
        for (var i = 0; i < elbowPoints.length; i++) {
          var x = elbowPoints[i].x - bounds.left;
          var y = elbowPoints[i].y - bounds.top;
          this.drawBendImpl(context, x, y, options);
        }
      } else { // Controls
        for (var i = 0, cps = this.getBends(); i + 1 < cps.length; i += 2) {
          var x = this.sourceX + cps[i] * bounds.width * this.mirrorx;
          var y = this.sourceY + cps[i + 1] * bounds.height * this.mirrory;
          this.drawBendImpl(context, x, y, options);
        }
      }

      context.restore();
    },
    /**
     * x, y : position of bend.
     */
    drawBendImpl: function(context, x, y, options) {
      var w = h = options.controlStyle.size * this.options.zoomScale;
      context.save();
      context.translate(x - w / 2, y - h / 2);
      context.beginPath();
      context.moveTo(0, h / 2);
      context.lineTo(w / 2, 0);
      context.lineTo(w, h / 2);
      context.lineTo(w / 2, h);
      context.lineTo(0, h / 2);
      context.lineTo(w / 2, 0);
      context.lineWidth = options.controlStyle.lineWidth * this.options.zoomScale;
      context.fill();
      context.stroke();
      context.restore();
    },
    drawReferBend: function(context, pos) {
      var bounds = this.getBounds();
      var r = 3;
      var px = pos[0] - bounds.left;
      var py = pos[1] - bounds.top;

      context.beginPath();
      context.save();
      context.translate(-this.canvasMargin['margin-left'], -this.canvasMargin['margin-top']);
      context.arc(px, py, r, 0, 2 * Math.PI, false);
      context.fillStyle = '#FA0000';
      context.strokeStyle = '#000000';
      context.fill();
      context.stroke();
      context.restore();
    },
  });

  /**
   * Path Type Define.
   */
  jBME.Path.Type = function(name, options) {
    jBME.Path.Type[name] = this;
    this.name = name;
    $.extend(this, options);
  };
  jBME.Path.Type.prototype = {
    name: '',
    /**
     * The path draw function.
     */
    figure: function(context) {
      /**/
    },
    controls: [],
    /**
     * Return the bend points on path.
     * When the path is linear, the bend points are the same as control points(controls);
     * When the path is curve(quadratic/cubic or high-order bezier),
     *  the bends points can be intermediate points on path according to the controls.
     * For example, for Bezier Curve with 2 control points, we can return 2 bend points(t=1/3, 2/3).
     * @return [x1,y1,x2,y2,...]
     */
    getBends: function(jPath) {
      return jPath.options.controls.concat( /* just a copy.*/);
    },
    /**
     * Return the new control point by the given bend and the according information.
     * @param bend, [x,y] bend point.
     * @param points, t, @see Utils.bezier()
     */
    getControlByBend: function(bend, points, t) {
      return bend;
    },
    /**
     * Draw dashed line.
     */
    drawDashedLine: function(context, x, y, x2, y2, dashArray) {
      var dashCount = dashArray.length;
      var dx = (x2 - x);
      var dy = (y2 - y);
      var xSlope = dx > dy;
      var slope = (xSlope) ? dy / dx : dx / dy;

      if (slope > 9999) {
        slope = 9999;
      } else if (slope < -9999) {
        slope = -9999;
      }

      var distRemaining = Math.sqrt(dx * dx + dy * dy);
      var dashIndex = 0;
      var draw = true;
      while (distRemaining >= 0.1 && dashIndex < 10000) {
        var drawType = draw ? 'lineTo' : 'moveTo';
        var dashLength = dashArray[dashIndex++ % dashCount];
        dashLength = dashLength === 0 ? 0.001 : dashLength;
        dashLength = dashLength > distRemaining ? distRemaining : dashLength;
        var step = Math.sqrt(dashLength * dashLength / (1 + slope * slope));
        var xIncrement = dx < 0 && dy < 0 ? step * -1 : step;
        var yIncrement = dx < 0 && dy < 0 ? slope * step * -1 : slope * step;

        if (xSlope) {
          x += xIncrement;
          y += yIncrement;
        } else {
          x += yIncrement;
          y += xIncrement;
        }
        context[drawType](x, y);
        distRemaining = distRemaining - dashLength;
        draw = !draw;
      }

      context.moveTo(x2, y2);
    },
  };

  /**
   * [Cubic Bézier Curve]
   * @see http:// en.wikipedia.org/wiki/B%C3%A9zier_curve
   */
  new jBME.Path.Type('bezier', {
    figure: function(context) { // @see Utils.bezierBends()
      for (var n = 3, i = 0, pts = this.getRelativeRouteList(); i + n * 2 + 1 < pts.length; i += (n * 2)) {
        i == 0 && context.moveTo(pts[i], pts[i + 1]);
        context.bezierCurveTo.apply(context, pts.slice(i + 2, i + 2 + n * 2));
      }
    },
    getBends: function(jPath) {
      return Utils.bezierBends(jPath.getPoints(), 3);
    },
    getControlByBend: function(bend, points, t) { // for 't' > n(several curves patched together, we don't support return control by bend right now.
      var n = 3;
      return [Utils.arcbezier(bend[0], Utils.slice(points, 0, n + 1, 2), t / n, t),
        Utils.arcbezier(bend[1], Utils.slice(points, 1, n + 1, 2), t / n, t),
      ];
    },
    controls: [1 / 3, 1 / 12, 2 / 3, 11 / 12],

    // extend
    getPoint: function(jPath, t) {
      Utils.bezier(jPath.getPoints(), t);
    },
  });

  /**
   * [Quadratic Bézier Curve]
   */
  new jBME.Path.Type('quadratic', {
    figure: function(context) { // @see Utils.bezierBends()
      for (var n = 2, i = 0, pts = this.getRelativeRouteList(); i + n * 2 + 1 < pts.length; i += (n * 2)) {
        i == 0 && context.moveTo(pts[i], pts[i + 1]);
        context.quadraticCurveTo.apply(context, pts.slice(i + 2, i + 2 + n * 2));
      }
    },
    getBends: function(jPath) {
      return Utils.bezierBends(jPath.getPoints(), 2);
    },
    getControlByBend: function(bend, points, t) {
      var n = 2;
      return [Utils.arcbezier(bend[0], Utils.slice(points, 0, n + 1, 2), t / n, t),
        Utils.arcbezier(bend[1], Utils.slice(points, 1, n + 1, 2), t / n, t),
      ];
    },
    controls: [0.25, 0.75],
  });

  /**
   * The predefined path types.
   */
  jBME.Path.Type['default'] = new jBME.Path.Type('line', {
    figure: function(context) {
      var pts = this.getRelativeRouteList();

      context.moveTo(pts[0], pts[1]);
      for (var n = 2; n < pts.length; n += 2) {
        var x = pts[n];
        var y = pts[n + 1];
        var dash = this.options.dashArray;

        if (dash && dash.length > 0) { // draw dashed line
          var lastX = pts[n - 2];
          var lastY = pts[n - 1];
          this.jType.drawDashedLine(context, lastX, lastY, x, y, dash);
        } else { // draw normal line
          context.lineTo(x, y);
        }
      }
    },
  });

  /**
   * [Elbow contector line] : |_
   */
  new jBME.Path.Type('elbow', {
    figure: jBME.Path.Type['line'].figure,
    controls: [0, 1],
    isOrthogonal: true,
  });

  /**
   * [Elbow contector line] :   _
   *                |
   */
  new jBME.Path.Type('elbowOpp', {
    figure: jBME.Path.Type['line'].figure,
    controls: [1, 0],
    isOrthogonal: true,
  });

  /**
   * [quadraticElbow contector line] :  _
   *                    |_
   */
  new jBME.Path.Type('elbowDoub', {
    figure: jBME.Path.Type['line'].figure,
    controls: [0.5, 0, 0.5, 1],
    isOrthogonal: true,
  });

  /**
   * [quadraticElbow contector line] : |_
   *                   |
   */
  new jBME.Path.Type('elbowDoubOpp', {
    figure: jBME.Path.Type['line'].figure,
    controls: [0, 0.5, 1, 0.5],
    isOrthogonal: true,
  });

  /**
   * [cubicElbow contector line] :
   *
   */
  new jBME.Path.Type('orthogonalEdge', {
    figure: function(context) {
      var pts = this.options.routeList.concat();
      var len = pts.length;
      for (var i = 0; i < len; i += 2) { // Tansform to relative position.
        pts[i] -= this.bounds.left;
        pts[i + 1] -= this.bounds.top;
      }

      context.moveTo(pts[0], pts[1]);

      for (var n = 2; n < len; n += 2) {
        var x = pts[n];
        var y = pts[n + 1];
        var dash = this.options.dashArray;

        if (dash && dash.length > 0) {
          // draw dashed line
          var lastX = pts[n - 2];
          var lastY = pts[n - 1];
          this.jType.drawDashedLine(context, lastX, lastY, x, y, dash);
        } else {
          // draw normal line
          context.lineTo(x, y);
        }
      }
    },
  });

  jBME.OrthogonalEdge = jBME.Path.extend({
    construct: function(id, options, $pathbox, zoomargs) {
      this.construct.$.construct.call(this, id, options, $pathbox, zoomargs);

      this.isOrthogonalEdge = true;
      /**
       * Whether this path can avoid all nodes(block).
       */
      this.avoidNodes = false;
    },
    /**
     * Auto Routing method for initializing route list.
     * @param margin : min number between node and path.
     * @param nodePadding : min number between nodes.
     * @param sourceAnchor : the position of source anchor
     * @param targetAnchor : the position of target anchor
     * @returns routeList : the points in the route(start end and controls)
     */
    autoRouteManager: function(margin, nodePadding, sourceAnchor, targetAnchor) {
      var sourceNode = this.jSource;
      var targetNode = this.jTarget;

      var sourceBounds = $.extend({
        width: 1,
        height: 1,
      }, sourceNode.getBounds());
      var targetBounds = $.extend({
        width: 1,
        height: 1,
      }, targetNode.getBounds());

      // The original axis x and y of source node and target node.
      var axisOld = this.getAxis(sourceBounds, targetBounds, margin);

      // Enlarge the scope of source node and target node so that edge will be not close to node.
      sourceBounds = this.enlarge(sourceBounds, nodePadding);
      targetBounds = this.enlarge(targetBounds, nodePadding);

      // The new asix.
      var axis = this.getAxis(sourceBounds, targetBounds, margin);

      // The whole graph is consist of grid 5 * 5 by source node and target node.
      var graph = this.setGraph(axis);

      // Set the source node and target node's grid with block.
      this.setBlock(graph, axis, sourceBounds);
      this.setBlock(graph, axis, targetBounds);

      var start = this.getEndPointGrid(sourceAnchor, sourceBounds, axis, axisOld, graph);
      var end = this.getEndPointGrid(targetAnchor, targetBounds, axis, axisOld, graph);
      var startNode = graph[start.x][start.y];
      var endNode = graph[end.x][end.y];

      // The whole route of source anchor to target anchor.
      var route = jBME.Path.Algorithm.astar.search(graph, startNode, endNode, start.direction, false);

      // Just keep the bend points.
      route = this.clearUselessRoute(route, end);

      // Keep the length of route array for type changing then cleaning xPlanes and yPlanes.
      var oldLength = this.options.routeList ? this.options.routeList.length : 0;

      var routeList = this.getRouteList(route, sourceAnchor, targetAnchor, end);

      if (oldLength != routeList.length) {
        this.options.xPlanes = [-1, -1];
        this.options.yPlanes = [-1, -1];
      }

      // If user has modified xPlanes or yPlanes, use these values.
      routeList = this.updatePlanes(routeList);

      return routeList;
    },
    /**
     * Enlarge node's bounds with enlarge value for routing.
     * So the min value between node and path is the enlarge value.
     * @param bounds
     * @param enlargeValue
     * @returns
     */
    enlarge: function(bounds, enlargeValue) {
      bounds.left = bounds.left - enlargeValue;
      bounds.top = bounds.top - enlargeValue;
      bounds.width = bounds.width + 2 * enlargeValue;
      bounds.height = bounds.height + 2 * enlargeValue;
      return bounds;
    },
    /**
     * Genetate X and Y axis
     * @param sourceBounds
     * @param targetBounds
     * @param margin
     * @returns {axisX : x axis values, axisY : y axis values}
     */
    getAxis: function(sourceBounds, targetBounds, margin) {
      var axisX = [sourceBounds.left - 0.1, sourceBounds.left + sourceBounds.width + 0.1,
        targetBounds.left - 0.1, targetBounds.left + targetBounds.width + 0.1,
      ].sort(function(x, y) {
        return (x - y);
      });
      var axisY = [sourceBounds.top - 0.1, sourceBounds.top + sourceBounds.height + 0.1,
        targetBounds.top - 0.1, targetBounds.top + targetBounds.height + 0.1,
      ].sort(function(x, y) {
        return (x - y);
      });

      axisX.unshift(axisX[0] - margin >= 0 ? axisX[0] - margin : 0);
      axisX.push(axisX[axisX.length - 1] + margin);
      axisY.unshift(axisY[0] - margin >= 0 ? axisY[0] - margin : 0);
      axisY.push(axisY[axisY.length - 1] + margin);

      return {
        axisX: axisX,
        axisY: axisY,
      };
    },
    /**
     * Creates a Graph class used in the astar search algorithm.
     * @param grid two-dimensional array
     */
    setGraph: function(axis) {
      var axisX = axis.axisX;
      var axisY = axis.axisY;
      var nodes = [];

      for (var i = 0; i < axisY.length - 1; i++) {
        nodes[i] = [];
        for (var j = 0; j < axisX.length - 1; j++) {
          // default type = 1, bounds = {left, top, width, height} of rectangle
          var bounds = {
            left: axisX[j],
            top: axisY[i],
            width: axisX[j + 1] - axisX[j],
            height: axisY[i + 1] - axisY[i],
          };
          nodes[i][j] = this.setGraphNode(i, j, 1, bounds);
        }
      }

      return nodes;
    },
    setGraphNode: function(x, y, type, bounds) {
      var node = {};
      node.data = {};
      node.x = x;
      node.y = y;
      node.pos = {
        x: x,
        y: y,
      };
      node.type = type;
      node.bounds = bounds;

      return node;
    },
    /**
     * If grid is in source node or target node, this grid is a block.
     * @param nodes : all grid
     * @param axis : x axis and y axis
     * @param bounds : the bounds of node
     * @returns
     */
    setBlock: function(nodes, axis, bounds) {
      var graph = nodes;
      var axisX = axis.axisX;
      var axisY = axis.axisY;

      var startIndexY = $.inArray(bounds.top - 0.1, axisY);
      var endIndexY = $.inArray(bounds.top + bounds.height + 0.1, axisY);
      for (var i = startIndexY; i < endIndexY; i++) {
        var startIndexX = $.inArray(bounds.left - 0.1, axisX);
        var endIndexX = $.inArray(bounds.left + bounds.width + 0.1, axisX);
        for (var j = startIndexX; j < endIndexX; j++) {
          graph[i][j].type = 0;
        }
      }
      return graph;
    },
    /**
     * Get the end point by looking for closest node bounds.
     * eg: anchor position {left : 100, top : 50}
     *   node bounds {left :0, top : 0, width : 100, height : 100}
     * result : direction is west
     *     gridx is calculated by axis y numbers
     *     gridy is calculated by axis x numbers
     * @param endPoint : anchor position
     * @param nodePadding : min number of node and edge
     * @param bounds : anchor position
     * @param axis : x axis and y axis
     * @param graph : all grids
     * @returns
     */
    getEndPointGrid: function(endPoint, bounds, axis, axisOld, graph) {
      var direction = '';
      var gridx = 0;
      var gridy = 0;
      var paddingLeft = endPoint.left - bounds.left;
      var paddingRight = bounds.left + bounds.width - endPoint.left;
      var paddingTop = endPoint.top - bounds.top;
      var paddingBottom = bounds.top + bounds.height - endPoint.top;

      var padding = [paddingLeft, paddingRight, paddingTop, paddingBottom].sort(function(x, y) {
        return (x - y);
      });
      if (padding[0] == paddingLeft) {
        direction = 'West';
        gridy = $.inArray(bounds.left - 0.1, axis.axisX) - 1;
        gridx = this.getGridIndex(endPoint.top, axisOld.axisY);
      } else if (padding[0] == paddingRight) {
        direction = 'East';
        gridy = $.inArray(bounds.left + bounds.width + 0.1, axis.axisX);
        gridx = this.getGridIndex(endPoint.top, axisOld.axisY);
      } else if (padding[0] == paddingTop) {
        direction = 'North';
        gridx = $.inArray(bounds.top - 0.1, axis.axisY) - 1;
        gridy = this.getGridIndex(endPoint.left, axisOld.axisX);
      } else {
        direction = 'South';
        gridx = $.inArray(bounds.top + bounds.height + 0.1, axis.axisY);
        gridy = this.getGridIndex(endPoint.left, axisOld.axisX);
      }

      var endPoint = {
        direction: direction,
        x: gridx,
        y: gridy,
      };

      var point = this.reviseEndPoint(endPoint, graph);

      return point;
    },
    /**
     * Revise the position of end point by graph.
     * If end point is in block(type = 0), move it by direction.
     * @param endPoint
     * @param graph : grid of graph
     */
    reviseEndPoint: function(endPoint, graph) {
      while (graph[endPoint.x][endPoint.y].type == 0) {
        switch (endPoint.direction) { // x means row and y means column
          case 'North':
            endPoint.x -= 1;
            break;
          case 'South':
            endPoint.x += 1;
            break;
          case 'East':
            endPoint.y += 1;
            break;
          case 'West':
            endPoint.y -= 1;
            break;
        }
      }
      return endPoint;
    },
    /**
     * Get the x and y direction index of grid when its value greater than axis value firstly.
     * @param endPointValue
     * @param axisArray
     * @returns {Number}
     */
    getGridIndex: function(endPointValue, axisArray) {
      for (var index = 0; index < axisArray.length; index++) {
        if (axisArray[index] > endPointValue) {
          return index - 1;
        }
      }
    },
    /**
     * Clear route point which is not bend point.
     * @param route : the whole grid
     * @param end : the grid of end point
     * @returns Array : bends
     */
    clearUselessRoute: function(route, end) {
      if (route.length <= 1) { // When route.length == 1, don't clear.
        return route;
      }

      var routePoints = [];

      for (var step = 0; step < route.length - 1; step++) { // Get the bend via direction is not the same width next point.
        if (route[step].direction != route[step + 1].direction) {
          routePoints.push(route[step]);
        }
      }

      if (route[route.length - 1].direction != this.getReverseDirection(end.direction)) { // The last one in route which is not the end point.
        routePoints.push(route[route.length - 1]);
      }

      return routePoints;
    },
    /**
     * Adjust route by start point and end point.
     * Because the route is gird instead of point.
     * @param route : the route without start point and end point.
     * @param sourcePoint : the source anchor position
     * @param targetPoint : the target anchor position
     * @param end : the grid of end point
     * @returns {Array}
     */
    getRouteList: function(route, sourcePoint, targetPoint, end) {
      var routeList = [sourcePoint.left, sourcePoint.top];
      var index = 0;

      for (var i = 0; i < route.length; i++) {
        if (route[i].direction == 'North' || route[i].direction == 'South') {
          routeList.push(routeList[index]);
          routeList.push(route[i].bounds.top + route[i].bounds.height / 2);
        } else {
          routeList.push(route[i].bounds.left + route[i].bounds.width / 2);
          routeList.push(routeList[index + 1]);
        }
        index = index + 2;
      }

      // Correct deviation by end point
      var lastDirection = this.getReverseDirection(end.direction);

      if ((end.direction == 'North' || end.direction == 'South') &&
        routeList[index] != targetPoint.left) {
        this.getNorthSouthRouteList(routeList, index, route, lastDirection);
      } else if ((end.direction == 'East' || end.direction == 'West') &&
        routeList[index + 1] != targetPoint.top) {
        this.getWestEastRouteList(routeList, index, targetPoint, route,);
      }

      routeList.push(targetPoint.left);
      routeList.push(targetPoint.top);

      return routeList;
    },
    getNorthSouthRouteList: function(routeList, index, targetPoint, route, lastDirection) {
      let newIndex = index;
      if (route.length == 1 &&
        (route[0].direction == end.direction || route[0].direction == lastDirection)) { // route.direction == lastDirection that is two nodes in a horizontal line
        routeList[newIndex] = routeList[0];
        routeList.push(targetPoint.left);
        routeList.push(routeList[newIndex + 1]);
      } else {
        routeList[newIndex] = targetPoint.left;
        newIndex = newIndex - 2;

        for (let j = route.length - 1; j >= 0; j--) {
          if (route[j].direction == lastDirection) {
            routeList[newIndex] = targetPoint.left;
            newIndex = newIndex - 2;
          } else {
            break;
          }
        }
      }
    },
    getWestEastRouteList: function(routeList, index, targetPoint, route, lastDirection) {
      let newIndex = index;
      if (route.length == 1 &&
        (route[0].direction == end.direction || route[0].direction == lastDirection)) { // route.direction == lastDirection that is two nodes in a horizontal line
        routeList[newIndex + 1] = routeList[1];
        routeList.push(routeList[newIndex]);
        routeList.push(targetPoint.top);
      } else {
        routeList[newIndex + 1] = targetPoint.top;
        newIndex = newIndex - 2;

        for (let j = route.length - 1; j >= 0; j--) {
          if (route[j].direction == lastDirection) {
            routeList[newIndex + 1] = targetPoint.top;
            newIndex = newIndex - 2;
          } else {
            break;
          }
        }
      }
    }
    ,
    /**
     * Get the reverse direction.
     * N -> S, S -> N, W -> E, E -> W
     */
    getReverseDirection: function(direction) {
      switch (direction) {
        case 'North':
          return 'South';
        case 'South':
          return 'North';
        case 'East':
          return 'West';
        case 'West':
          return 'East';
      }
    },
    /**
     * Get the bounds of orthogonal edge by calculating
     * min number and max number of bend points.
     * @param routeList
     * @returns elbowBounds
     */
    getElbowBounds: function(routeList) {
      var minx = routeList[0];
      var maxx = routeList[0];
      var miny = routeList[1];
      var maxy = routeList[1];
      for (var index = 2; index < routeList.length; index += 2) {
        if (routeList[index] < minx) {
          minx = routeList[index];
        } else if (routeList[index] > maxx) {
          maxx = routeList[index];
        }

        if (routeList[index + 1] < miny) {
          miny = routeList[index + 1];
        } else if (routeList[index + 1] > maxy) {
          maxy = routeList[index + 1];
        }
      }
      var elbowBounds = {
        left: minx,
        top: miny,
        width: maxx - minx,
        height: maxy - miny,
      };

      return elbowBounds;
    },
    /**
     * Modify routeList according to xPlanes and yPlanes.
     */
    updatePlanes: function(routeList) {
      if (this.options.xPlanes[0] == -1 && this.options.xPlanes[1] == -1 &&
        this.options.yPlanes[0] == -1 && this.options.yPlanes[1] == -1) {
        return routeList;
      }

      // Don't take start point and end point into consideration.
      for (var i = 2, len = routeList.length; i < len - 3; i += 2) {
        // The index of xPlanes or yPlanes.
        var index = Math.floor((i - 2) / 4);

        if (Math.abs(routeList[i] - routeList[i - 2]) < 1) { // Compare with previous node whether previous line is vertical
          // Then this line is horizontal, so update yPlanes.
          var yplane = this.options.yPlanes[index];
          if (yplane != -1) {
            routeList[i + 1] = routeList[i + 3] = yplane;
          }
        } else {
          var xplane = this.options.xPlanes[index];
          if (xplane != -1) {
            routeList[i] = routeList[i + 2] = xplane;
          }
        }
      }

      return routeList;
    },
    /**
     * Update canvas margin for orthogonal edge out of side sometimes.
     * @param elbowBounds : edge bounds
     */
    updateCanvas: function(elbowBounds) {
      var margin = this.options.margin * this.options.zoomScale;

      this.canvasMargin = {
        'margin-left': -(this.bounds.left - elbowBounds.left + margin),
        'margin-top': -(this.bounds.top - elbowBounds.top + margin),
        'margin-right': -(elbowBounds.left + elbowBounds.width + margin - this.bounds.left - this.bounds.width),
        'margin-bottom': -(elbowBounds.top + elbowBounds.height + margin - this.bounds.top - this.bounds.height),
      };
      this.$canvas.css(this.canvasMargin);

      this.canvas.width = elbowBounds.width - this.canvasMargin['margin-left'] - this.canvasMargin['margin-right'];
      this.canvas.height = elbowBounds.height - this.canvasMargin['margin-top'] - this.canvasMargin['margin-bottom'];
      this.canvasManager(this.canvas);
    },
    /**
     * Generate controls of orthogonalEdge by routeList
     * @param routeList
     */
    getControls: function(routeList) {
      var controls = [];
      for (var index = 2; index < routeList.length - 3; index += 2) {
        var controlX = this.mirrorx * (routeList[index] - routeList[0]) / this.bounds.width;
        var controlY = this.mirrory * (routeList[index + 1] - routeList[1]) / this.bounds.height;
        controls.push(controlX);
        controls.push(controlY);
      }
      return controls;
    },
    /**
     * Whether current mouse position can drag orthogonal edge horizontal or vertical.
     * @param event : mouse event
     * @param diagramOffset : the offset of diagram
     * @returns {Boolean}
     */
    hitMovingEdge: function(positionx, positiony) {
      var routeList = this.options.routeList;
      var distance = 6;

      if (routeList.length <= 6) { // Can't be moved
        return false;
      }

      for (var index = 2, len = routeList.length; index < len - 4; index += 2) { // Elimination of the starting point and end point
        var startX = routeList[index];
        var startY = routeList[index + 1];
        var endX = routeList[index + 2];
        var endY = routeList[index + 3];

        if (Math.abs(startX - endX) < 1 &&
          Math.abs(startX - positionx) < distance &&
          Utils.belongTo(positiony, startY, endY)) { // The cursor position is on the vertical route and horizontal dragging.
          this.draggingDir = 'horizontal';
          this.draggingIndex = index;
          return true;
        }

        if (Math.abs(startY - endY) < 1 &&
          Math.abs(startY - positiony) < distance &&
          Utils.belongTo(positionx, startX, endX)) { // The cursor position is on the vertical route and horizontal dragging.
          this.draggingDir = 'vertical';
          this.draggingIndex = index;
          return true;
        }
      }

      return false;
    },
    /**
     * Move route edge by mouse position.
     * @param offsetX : mouse position of x axis relative diagram offset
     * @param offsetY : mouse position of y axis relative diagram offset
     */
    moveHoverEdge: function(offsetX, offsetY) {
      var routeList = this.options.routeList;
      var index = this.draggingIndex;
      var planeIndex = Math.floor((index - 2) / 4);

      if (this.draggingDir == 'horizontal') {
        routeList[index] = offsetX;
        routeList[index + 2] = offsetX;
        this.options.xPlanes[planeIndex] = offsetX;
      } else if (this.draggingDir == 'vertical') {
        routeList[index + 1] = offsetY;
        routeList[index + 3] = offsetY;
        this.options.yPlanes[planeIndex] = offsetY;
      }

      this.updateBounds(routeList);
    },
  });

  /**
   * Arrow type define
   * @returns {jBME.Path.Arrow}
   */
  jBME.Path.Arrow = function(name, drawArrow) {
    jBME.Path.Arrow[name] = this;
    this.name = name;
    this.drawArrow = drawArrow;
  };
  jBME.Path.Arrow.prototype = {
    name: '',
    drawArrow: function(context, tox, toy, angle, headlen, options) {
    },
  };
  new jBME.Path.Arrow('arrow', function(context, tox, toy, angle, headlen, options) {
    var arc = (options.arrow.angle * Math.PI / 180) / 2;
    context.moveTo(tox - headlen * Math.cos(angle - arc), toy - headlen * Math.sin(angle - arc));
    context.lineTo(tox, toy);
    context.lineTo(tox - headlen * Math.cos(angle + arc), toy - headlen * Math.sin(angle + arc));
  });
  new jBME.Path.Arrow('hollowArrow', function(context, tox, toy, angle, headlen, options) {
    var arc = (options.arrow.angle * Math.PI / 180) / 2;
    context.moveTo(tox - headlen * Math.cos(angle - arc), toy - headlen * Math.sin(angle - arc));
    context.lineTo(tox, toy);
    context.lineTo(tox - headlen * Math.cos(angle + arc), toy - headlen * Math.sin(angle + arc));
    context.closePath();
    context.fillStyle = '#FFFFFF';
    context.fill();
  });
  new jBME.Path.Arrow('solidArrow', function(context, tox, toy, angle, headlen, options) {
    var arc = (options.arrow.angle * Math.PI / 180) / 2;
    context.moveTo(tox - headlen * Math.cos(angle - arc), toy - headlen * Math.sin(angle - arc));
    context.lineTo(tox, toy);
    context.lineTo(tox - headlen * Math.cos(angle + arc), toy - headlen * Math.sin(angle + arc));
    context.closePath();
    context.fillStyle = this.isselected ? options.selected.strokeStyle : options.figures[0].context.strokeStyle;
    context.fill();
  });
  new jBME.Path.Arrow('aggregation', function(context, tox, toy, angle, headlen, options) {
    var arc = (options.arrow.angle * Math.PI / 180) / 2;
    context.moveTo(tox - headlen * Math.cos(angle - arc), toy - headlen * Math.sin(angle - arc));
    context.lineTo(tox, toy);
    context.lineTo(tox - headlen * Math.cos(angle + arc), toy - headlen * Math.sin(angle + arc));
    context.lineTo(tox - headlen * Math.cos(arc) * Math.cos(angle) * 2,
      toy - headlen * Math.cos(arc) * Math.sin(angle) * 2);
    context.closePath();
    context.fillStyle = '#FFFFFF';
    context.fill();
  });
  new jBME.Path.Arrow('composition', function(context, tox, toy, angle, headlen, options) {
    var arc = (options.arrow.angle * Math.PI / 180) / 2;
    context.moveTo(tox - headlen * Math.cos(angle - arc), toy - headlen * Math.sin(angle - arc));
    context.lineTo(tox, toy);
    context.lineTo(tox - headlen * Math.cos(angle + arc), toy - headlen * Math.sin(angle + arc));
    context.lineTo(tox - headlen * Math.cos(arc) * Math.cos(angle) * 2,
      toy - headlen * Math.cos(arc) * Math.sin(angle) * 2);
    context.closePath();
    context.fillStyle = this.isselected ? options.selected.strokeStyle : options.figures[0].context.strokeStyle;
    context.fill();
  });
  new jBME.Path.Arrow('circleOpen', function(context, tox, toy, angle, headlen, options) {
    var arc = (options.arrow.angle * Math.PI / 180) / 2;
    var radius = headlen / 2;
    context.arc(tox - radius * Math.cos(angle), toy - radius * Math.sin(angle), radius, 0, 2 * Math.PI, true);
    context.fillStyle = '#FFFFFF';
    context.fill();
  });
  new jBME.Path.Arrow('circleClosed', function(context, tox, toy, angle, headlen, options) {
    var arc = (options.arrow.angle * Math.PI / 180) / 2;
    var radius = headlen / 2;
    context.arc(tox - radius * Math.cos(angle), toy - radius * Math.sin(angle), radius, 0, 2 * Math.PI, true);
    context.fillStyle = this.isselected ? options.selected.strokeStyle : options.figures[0].context.strokeStyle;
    context.fill();
  });
  new jBME.Path.Arrow('BPMNDefault', function(context, tox, toy, angle, headlen, options) {
    var angular = (options.arrow.angle * Math.PI / 180);
    var crossx = tox - headlen * Math.cos(angle);
    var crossy = toy - headlen * Math.sin(angle);
    context.moveTo(crossx - headlen * Math.cos(angle - angular), crossy - headlen * Math.sin(angle - angular));
    context.lineTo(crossx + headlen * Math.cos(angle - angular), crossy + headlen * Math.sin(angle - angular));
  });
  new jBME.Path.Arrow('BPMNCondition', function(context, tox, toy, angle, headlen, options) {
    var angular = (options.arrow.angle * Math.PI / 180);
    var sidelen = headlen * 4 / 5;

    context.moveTo(tox - sidelen * Math.cos(angle - angular), toy - sidelen * Math.sin(angle - angular));
    context.lineTo(tox, toy);
    context.lineTo(tox - sidelen * Math.cos(angle + angular), toy - sidelen * Math.sin(angle + angular));
    context.lineTo(tox - sidelen * Math.cos(angular) * Math.cos(angle) * 2,
      toy - sidelen * Math.cos(angular) * Math.sin(angle) * 2);
    context.closePath();
    context.fillStyle = '#FFFFFF';
    context.fill();
  });
})(jQuery, window.jBME = (window.jBME || {}));

/* !
 * BME UI Component - Diagram Editor
 *
 * Dependency
 * ----------------------------------------------------------------------------
 * JS:
 * @requires jQuery v1.4.4 or later
 *
 * CSS:
 * bme.diagram.css
 *
 * HTML:
 * diagram.ftl
 * >Please keep fresh with the HTML!
 * ----------------------------------------------------------------------------
 * Copyright (c) Huawei Technologies Co., Ltd. 2010-2011. All rights reserved.
 *
 */

(function($, jBME) {
  jBME.Diagram.locatedOnBoundsPlugin = jBME.AbstractPlugin.extend({
    name: 'locatedOnBoundsPlugin',
    eventHandlers: [{
      event: jBME.Diagram.EVENT.notifychange,
      handler: 'afterCreatingNode',
      targetselector: 'all',
    }],
    afterCreatingNode: function(e, jElement, jDiagram) {
      if (e.action != 'nodecreate') {
        return;
      }

      jElement = e.jElement;
      if (!jElement) {
        return;
      }
      var locatedOnBounds = jElement.options.locatedOnBounds;
      if (!locatedOnBounds) {
        return;
      }

      if (!jElement.updateBounds(jElement.getBounds())) {
        jDiagram.remove(jElement);
      }
    },
  });

  jBME.Editor.register(jBME.Diagram.locatedOnBoundsPlugin);

  jBME.Diagram.locatedInPlugin = jBME.AbstractPlugin.extend({
    name: 'locatedInPlugin',
    eventHandlers: [{
      event: jBME.Diagram.EVENT.beforecreatenode,
      handler: 'beforeCreating',
      targetselector: 'all',
    }],
    beforeCreating: function(e, jElement, jDiagram) {
      var locatedIn = e.options.locatedInNodes;
      if (!locatedIn) {
        e.stopPropagation();
        return;
      }

      var containers = [];
      var bounds = jDiagram.getPositionWithScroll(e.event);
      bounds.width = 0;
      bounds.height = 0;

      for (var cid in jDiagram.nodes) {
        var contains = Utils.containsBound(jDiagram.nodes[cid].getBounds(), bounds);
        contains && containers.push(jDiagram.nodes[cid]);
      }

      for (var i = 0; i < containers.length; i++) {
        var name = containers[i].options.name;
        if (!name) {
          continue;
        }
        for (var j = 0; j < locatedIn.length; j++) {
          if (name == locatedIn[j]) {
            e.stopPropagation();
            return;
          }
        }
      }

      e.stopPropagation();
      e.preventDefault();
      return;
    },
  });

  jBME.Editor.register(jBME.Diagram.locatedInPlugin);

  jBME.Diagram.nodeOccurrencePlugin = jBME.AbstractPlugin.extend({
    name: 'nodeOccurrencePlugin',
    eventHandlers: [{
      event: jBME.Diagram.EVENT.beforecreatenode,
      handler: 'beforeCreating',
      targetselector: 'all',
    }],
    beforeCreating: function(e, jElement, jDiagram) {
      var maxOccurrence = e.options.maxOccurrence;
      var name = e.options.name;
      var nodes = jDiagram.nodes;
      var occurrence = e.event && e.event.type == 'mouseup' ? 0 : 1;

      if (!maxOccurrence) {
        e.stopPropagation();
        return;
      }

      for (var i in nodes) {
        var node = nodes[i];
        if (name == node.options.name) {
          occurrence++;
          if (occurrence > maxOccurrence) {
            e.stopPropagation();
            e.preventDefault();
            return;
          }
        }
      }
      e.stopPropagation();
    },
  });
  jBME.Editor.register(jBME.Diagram.nodeOccurrencePlugin);

  jBME.Diagram.titleHoverPlugin = jBME.AbstractPlugin.extend({
    name: 'titleHoverPlugin',

    eventHandlers: [{
      event: 'hover',
      handler: 'onHover',
      targetselector: '.uee-node-text',
    },
    {
      event: 'mousemove',
      handler: 'moveTitle',
      targetselector: '.uee-node-text',
    },
    {
      event: 'notifychange',
      handler: 'removeNodeAndTitle',
      targetselector: 'all',
    },
    ],
    /**
     * Hover event to manager popup title
     * @param e : hover event
     * @param jElement : hover element
     * @param jDiagram : object of diagram
     */
    onHover: function(e, jElement, jDiagram) {
      e.stopPropagation();

      if (e.type == 'mouseenter') {
        this.deleteFullTitle();
        this.showFullTitle(e, jElement, jDiagram);
      } else if (e.type == 'mouseleave') {
        this.deleteFullTitle();
      }
    },
    /**
     * Show title
     * @param e : event
     * @param text : title content
     * @param jDiagram : object of diagram
     */
    showFullTitle: function(e, jElement, jDiagram) {
      var eventPos = jDiagram.getPositionWithScroll(e);
      var x = eventPos.left - 2;
      var y = eventPos.top + 20;

      this.$titleSection = jQuery(
        ['<div class="uee-popuptip" style="position:absolute; top:', y, 'px;left:', x, 'px;"><span class="uee-popuptip-arrow uee-popuptip-arrow-top"><em>◆</em><i>◆</i></span><pre>', jElement.innerHTML, , '</pre></div>'].join(''));
      jDiagram.$client.append(this.$titleSection);

      this.titleAdapter(jDiagram, x, y);

      jElement && (this.hoverNodeId = jElement.parentNode.id);
    },
    /**
     * Calculate the exact coordinates.
     * @param pop_x: default x postion
     * @param pop_y: default y postion
     */
    titleAdapter: function(jDiagram, pop_x, pop_y) {
      var scrollLeft = jDiagram.$client.scrollLeft();
      var scrollTop = jDiagram.$client.scrollTop();
      var offsetWidth = jDiagram.$box[0].offsetWidth;
      var offsetHeight = jDiagram.$box[0].offsetHeight;
      var titleWidth = this.$titleSection.width();
      var titleHeight = this.$titleSection.height();
      var scrollLength = 16;

      if ((pop_x + titleWidth + scrollLength) > (scrollLeft + offsetWidth)) { // title.right > diagram.rigth
        var title_spn = this.$titleSection.children('span');
        title_spn.removeClass('uee-popuptip-arrow-top');
        title_spn.addClass('uee-popuptip-arrow-right');

        this.$titleSection.css({
          left: pop_x - titleWidth - 50,
          top: pop_y - 30,
        });
      } else if ((pop_y + titleHeight + scrollLength) > (scrollTop + offsetHeight)) { // title.bottom > diagram.bottom
        var title_spn = this.$titleSection.children('span');
        title_spn.removeClass('uee-popuptip-arrow-top');
        title_spn.addClass('uee-popuptip-arrow-bottom');

        this.$titleSection.css({
          left: pop_x,
          top: pop_y - titleHeight - 50,
        });
      }
    },
    /**
     * Delete this popup title.
     */
    deleteFullTitle: function() {
      if (this.$titleSection) {
        this.$titleSection.remove();
        this.$titleSection = undefined;
        this.hoverNodeId = undefined;
      }
    },
    _refreshElement: function(e) {
      this.jElement = e;
    },
    /**
     * Move title position according to mouse position.
     */
    moveTitle: function(e, jElement, jDiagram) {
      if (this.$titleSection) {
        var eventPos = jDiagram.getPositionWithScroll(e);
        var x = eventPos.left - 2;
        var y = eventPos.top + 20;

        this.$titleSection.css({
          left: x,
          top: y,
        });

        this.titleAdapter(jDiagram, x, y);
      }
    },
    /**
     * Delete title after node is deleted.
     */
    removeNodeAndTitle: function(e, jElement, jDiagram) {
      if (e.action == 'selectionsdelete') {
        for (var id in e.jElement) {
          if (id == this.hoverNodeId) {
            this.deleteFullTitle();
          }
        }
      }
    },
  });
  jBME.Editor.register(jBME.Diagram.titleHoverPlugin);

  jBME.Diagram.hoverPlugin = jBME.AbstractPlugin.extend({
    name: 'hoverPlugin',
    eventHandlers: [{
      event: 'hover',
      handler: 'onHover',
    },
    {
      event: 'mousemove',
      handler: 'onHover',
    },
    ],
    /**
     * Hover Element in diagram.
     * The pseudo-event-name "hover" is a shorthand for "mouseenter mouseleave"
     */
    onHover: function(e, jElement, jDiagram) {
      // jRePath : Restore reconnect path, jReEnd : Restore reconnect end point
      var jHover = null;
      var jRePath = null;
      var jReEnd = null;

      if (jDiagram.$box.hasClass('resize') || jDiagram.$box.hasClass('dragging')) {
        return;
      }

      if (Utils.isUseExcanvas) {
        if (jDiagram.hoverPageX == e.pageX && jDiagram.hoverPageY == e.pageY) {
          return;
        } else {
          jDiagram.hoverPageX = e.pageX;
          jDiagram.hoverPageY = e.pageY;
        }
      }

      if (jElement) {
        if (jElement.ispath && !jDiagram.request.has()) {
          jReEnd = jElement.hitPathPoint(e, jDiagram.reconnectScope);
        }

        if (jReEnd) { // Reconnect path
          jRePath = jElement;
        } else if (jDiagram.connecting) {
          for (var nid in jDiagram.nodes) {
            if (jDiagram.nodes[nid].isOnShape(e)) {
              jHover = jDiagram.nodes[nid];
            }
          }
        } else if (jElement.isOnShape(e)) { // The event current target is on shape, hover it!
          // When jElement's type == default and mask is exist, return true for preventing from dblclick on textnode in IE9 bug.
          jHover = jElement;
        } else { // The event current target is not on shape, we need to check each elements(nodes|paths):(, while SVG/VML not need this check.
          jDiagram.each(function(id) {
            if (jElement.id != id) {
              if (this.ispath && !jDiagram.request.has()) {
                jReEnd = this.hitPathPoint(e, jDiagram.reconnectScope);
              }

              if (jReEnd) {
                jRePath = this;
                return false;
              } else if (this.isOnShape(e)) {
                jHover = this;
              }
            }
          });
        }
      }

      // Only accept one hover element(path and node) at the same time.
      if (jHover !== jDiagram.jHoverElement) {
        if (jDiagram.jHoverElement) {
          jDiagram.jHoverElement.toggleHover(false, null);
          jDiagram.jHoverElement.isnode && jDiagram.removeActiveMenu();
        }
        jDiagram.jHoverElement = jHover;

        // Hover switch
        if (jHover && !jDiagram.authManager(jHover, 'hoverable')) {
          return;
        }

        jHover && !jDiagram.request.has() && jHover.toggleHover(true);

        if (jHover && jHover.ispath) {
          var eventPosition = jDiagram.getPositionWithScroll(e);
          if (jHover.isOrthogonalEdge && jHover.hitMovingEdge(eventPosition.left, eventPosition.top)) {
            var cursor = '';
            if (jHover.draggingDir == 'horizontal') {
              cursor = 'e-resize';
            } else if (jHover.draggingDir == 'vertical') {
              cursor = 'n-resize';
            }
            jHover.$box.css('cursor', cursor);
          } else {
            !jDiagram.request.has() && jDiagram.$box.toggleClass('hover', Boolean(jHover));
          }
          jDiagram.lastHover = jHover;
        } else {
          jDiagram.lastHover && jDiagram.lastHover.$box.css('cursor', '');
          !jDiagram.request.has() && jDiagram.$box.toggleClass('hover', Boolean(jHover));
        }

        if (jHover && jHover.isnode) {
          if (jHover) {
            jDiagram.showActiveMenu(jHover);
          } else {
            jDiagram.removeActiveMenu();
          }
        }
      } else {
        if (jHover && jHover.isnode && !jDiagram.$activeMenu) {
          jDiagram.showActiveMenu(jHover);
        }
      }

      // Change reconnect style.
      if (jRePath !== jDiagram.jReconnectPath && jDiagram.viewmode != 'view') {
        jDiagram.jReconnectPath && jDiagram.jReconnectPath.toggleReconnect(false);
        jDiagram.jReconnectPath = jRePath;
        jDiagram.jReconnectEnd = jReEnd;

        jRePath && jRePath.toggleReconnect(true);
        jDiagram.$box.toggleClass('reconnect', Boolean(jRePath));
      }

      // Hover anchor on node when creating path. TODO
      if (jDiagram.request.has() && jDiagram.jHoverElement && jDiagram.jHoverElement.isnode) {
        var pos = jDiagram.getPositionWithScroll(e);
        var anchor = jDiagram.jHoverElement.getClosestAnchorFromPosition(pos);
        jDiagram.jHoverElement.toggleHover(true, anchor);
      }
    },
  });
  jBME.Editor.register(jBME.Diagram.hoverPlugin);

  jBME.Diagram.selectdragPlugin = jBME.AbstractPlugin.extend({
    name: 'selectdragPlugin',
    eventHandlers: [{
      event: 'mousedown',
      handler: 'onSelectdrag',
    }],
    onSelectdrag: function(e, jElement, jDiagram) {
      var jHover = this.selectHover(jDiagram, jElement, e);
      // prevent default image dragging in node.
      if (jElement && jElement.isnode && $.nodeName(e.target, 'IMG')) {
        e.preventDefault();
      }

      // Only accept left mouse button; Prevent dragging when create node/path.
      if (!jElement || e.which != 1 || (jElement.isnode && !jElement.canDrag(e)) || jDiagram.request.has() ||
        (!e.selections && jDiagram.resizedir == 0 && !jDiagram.jReconnectPath)) {
        return;
      }

      // [drag]
      if (jElement.isnode) {
        this.nodeDrag(jDiagram, jHover, e);
      } else if (jElement.ispath) {
        this.pathDrag(jDiagram, jHover, e);
      } // end drag
    },
    nodeDrag: function(jDiagram, jHover, e) {
      if (jDiagram.jReconnectPath && jDiagram.jReconnectEnd && jDiagram.viewmode != 'view' &&
        !jDiagram.$box.hasClass('connectstart') && !jDiagram.$box.hasClass('resize')) { // Redraw path
        jDiagram.reconnectPath(jDiagram.jReconnectEnd, jDiagram.jReconnectPath, e);
      } else { // Drag node
        let dragElement = null;

        if (jHover) {
          dragElement = jHover;
        } else if (jDiagram.resizedir > 0 && jDiagram.resizeElement) {
          dragElement = jDiagram.resizeElement;
        }
        dragElement && dragElement.isnode && this.dragSelections(jDiagram, dragElement, e);
      }
    },
    pathDrag: function(jDiagram, jHover, e) {
      // For drag one point of path to redraw path.
      if (jDiagram.jReconnectPath && jDiagram.jReconnectEnd && jDiagram.viewmode != 'view' &&
        !jDiagram.$box.hasClass('connectstart') && !jDiagram.$box.hasClass('resize')) { // Redraw path
        jDiagram.reconnectPath(jDiagram.jReconnectEnd, jDiagram.jReconnectPath, e);
        return;
      }

      if (jHover && jHover.ispath) { // Drag path.
        this.dragPath(jDiagram, jHover, e);
      } else if (!jDiagram.jReconnectPath) { // Drag container.
        let dragElement = null;

        // To solve container's resize problem when it is covered by path.
        // We should use jHover and selected container.
        if (jHover) {
          dragElement = jHover;
        } else if (jDiagram.resizedir > 0 && jDiagram.resizeElement) {
          dragElement = jDiagram.resizeElement;
        }

        dragElement && dragElement.isnode && this.dragSelections(jDiagram, dragElement, e);
      }
    },

    dragSelections: function(jDiagram, dragElement, e) {
      // Draggable switch for user to control container.
      if (dragElement && jDiagram.resizedir == 0 && !jDiagram.authManager(dragElement, 'draggable')) {
        return;
      }

      var draggingBounds = this.getDraggingBounds(jDiagram, dragElement);
      this.draggingNode(jDiagram, function(emv) {
        jBME.Diagram.selectdragPlugin.updateDraggingBounds(jDiagram, draggingBounds, emv.pageX - e.pageX, emv.pageY - e.pageY, emv);

        // When mousedown in one iframe and move outside and mouseup, ie can't reponse this "mouseup" event.
        if ($.browser.msie && jDiagram.$box[0].setCapture) {
          jDiagram.$box[0].setCapture();
        }
      }, draggingBounds, e);
    },
    getDraggingBounds: function(jDiagram, jTargetElement) {
      var draggingBounds = {};
      if (jDiagram.resizedir) { // We only accept resize one element which is the event target.
        draggingBounds[jTargetElement.id] = jTargetElement.getBounds();
      } else { // We support moving multiple nodes.
        for (var id in jDiagram.getIndependentSelections(function() {
          return this.isnode;
        })) {
          if (jDiagram.nodes[id]) {
            draggingBounds[id] = jDiagram.nodes[id].getBounds();

            // Looking for the group of this node.
            var jGroup = jDiagram.getGroupMembers(id);
            for (var gid in jGroup) {
              draggingBounds[gid] = jDiagram.nodes[gid].getBounds();
            }

            // If this is container,drag together
            if (jDiagram.nodes[id].iscontainer) {
              var children = {};
              if (jDiagram.nodes[id].isswimlane) { // swimlane
                var lanes = jDiagram.nodes[id].lanes;
                for (var i = 0; i < jDiagram.nodes[id].lanes.length; i++) { // This swimlane has children,drag together.
                  lanes[i].id != id && (draggingBounds[lanes[i].id] = lanes[i].getBounds());
                  children = $.extend(children, lanes[i].getDescendant());
                }
              } else { // normal container
                children = $.extend(children, jDiagram.nodes[id].getDescendant());
              }
              for (var cid in children) {
                draggingBounds[cid] = jDiagram.nodes[cid].getBounds();
              }
            }
          }
        }
      }
      return draggingBounds;
    },
    dragPath: function(jDiagram, jPath, e) {
      // Draggable switch for user to control path.
      if (jPath && jDiagram.resizedir == 0 && !jDiagram.authManager(jPath, 'draggable')) {
        return;
      }

      var eventPosition = jDiagram.getPositionWithScroll(e);

      if (!jPath.isOrthogonalEdge) {
        if (jPath.hitTestHoverBend(e)) { // Drag bend
          var position = [eventPosition.left, eventPosition.top, 0, jPath.hoverBend.index];
          this.draggingPath(jDiagram, e, position);
        }
      } else if (jPath.hitMovingEdge(eventPosition.left, eventPosition.top)) {
        this.draggingOrthogonalEdge(jDiagram, function(emv) {
          var emvPosition = jDiagram.getPositionWithScroll(emv);
          jPath.moveHoverEdge(emvPosition.left, emvPosition.top);
        });
      }
    },
    /**
     * Install Dragging Handler for nodes.
     * @param draggingHandler
     * @return
     */
    draggingNode: function(jDiagram, draggingHandler, draggingBounds, e) {
      // [Dragging]
      var dragging = 'mousemove.drag' + jDiagram.id;
      var dragend = 'mouseup.drag' + jDiagram.id;
      var startPosition = jDiagram.getPositionWithScroll(e);
      var dragPosition = {
        x: startPosition.left,
        y: startPosition.top,
      };
      var that = this;

      jDiagram.$box.css('cursor', jDiagram.resizedir ? Utils.RESIZE.CURSORS[jDiagram.resizedir] : '');

      // Fix IE 8 and IE9 scroll bug.
      that.scroll = {};
      if ($.browser.msie && $.browser.version < 10) {
        that.scroll.top = jDiagram.$client.scrollTop();
        that.scroll.left = jDiagram.$client.scrollLeft();
      }

      // Clean the style of crosshair by draw line from anchor.
      jDiagram.isDragStart = true;

      if (jDiagram.$box.hasClass('connectstart')) {
        jDiagram.$box.removeClass('connectstart');
      }

      for (var rid in draggingBounds) {
        if (jDiagram.resizedir > 0 && !jDiagram.jHoverElement) {
          jDiagram.nodes[rid].toggleSelect(true);
        }

        var bounds = draggingBounds[rid];
        jDiagram.removeCenterPoint(jDiagram.hcenters, bounds.left + bounds.width / 2);
        jDiagram.removeCenterPoint(jDiagram.vcenters, bounds.top + bounds.height / 2);
      }
      $(document).bind('selectstart.drag', function() {
        return false;
      }).bind(dragging, function(emv) {
        if (!jDiagram.dragElement &&
          (e.pageX != emv.pageX || e.pageY != emv.pageY) &&
          !jDiagram.$box.hasClass('dragging')) {
          jDiagram.resizedir == 0 && jDiagram.$box.addClass('dragging');
        }

        jDiagram.removeActiveMenu();
        jDiagram.dragElement = true;
        draggingHandler.apply(jDiagram, arguments);

        if (jDiagram.resizedir == 0) {
          for (var sid in draggingBounds) {
            jDiagram.snapLineManager(jDiagram.nodes[sid].getBounds());
          }
        }
      }).bind(dragend, function(eup) { // [Drag end]
        // Whether these nodes are in containers.
        that.isInContainers(jDiagram, draggingBounds);

        jDiagram.$doc.unbind(dragend).unbind(dragging).unbind('selectstart.drag');
        jDiagram.$box.removeClass('dragging');
        jDiagram.$box.css('cursor', '');

        if (jDiagram.registeredOptions) { // Drag to create node.
          jBME.Diagram.dragToCreateNode.isCreate(jDiagram, e, eup);
        } else { // Drag node to move it.
          var endPosition = jDiagram.getPositionWithScroll(eup);
          var dropPosition = {
            x: endPosition.left,
            y: endPosition.top,
          };

          that.draggingNodeEnd(jDiagram, dragPosition, dropPosition);
        }

        // Update max bounds to change page_overflow_indicator position.
        jDiagram.updateMaxBounds();

        that.browserHandler(that, jDiagram);

        jDiagram.dragElement = false;
        jDiagram.isDragStart = false;
      });
    },
    browserHandler: function(that, jDiagram) {
      if ($.browser.msie && $.browser.version < 10) { // If the max top or max left are changed, scrollTop and scrollLeft will change together.
        // But IE8 and IE9 can't trigger scroll event.
        let scrollTop = jDiagram.$client.scrollTop();
        let scrollLeft = jDiagram.$client.scrollLeft();
        if (that.scroll.top != scrollTop || that.scroll.left != scrollLeft) {
          jDiagram.$client.trigger('scroll');
        }
      }

      // When mousedown in one iframe and move outside and mouseup, ie can't reponse this "mouseup" event.
      if ($.browser.msie && jDiagram.$box[0].releaseCapture) {
        jDiagram.$box[0].releaseCapture();
      }
    },
    /**
     * When drag end, we need to deal with the node whether it is in container.
     * @param jDiagram : instance of diagram
     * @param draggingBounds : all the bounds of dragging nodes
     */
    isInContainers: function(jDiagram, draggingBounds) {
      for (var did in draggingBounds) {
        if (!jDiagram.nodes[did]) {
          continue;
        }

        var container = {};
        if (!draggingBounds[jDiagram.nodes[did].options.parent] &&
          jDiagram.nodes[did].$box.css('display') != 'none') {
          container = jDiagram.isContained(jDiagram.nodes[did]);
        }

        if (!$.isEmptyObject(container)) {
          if ($.trim(jDiagram.nodes[did].options.parent) != '') { // This node is already in container.
            jDiagram.removeFamily(jDiagram.nodes[did]);
          }
          jDiagram.joinFamily(jDiagram.nodes[did], container);
        }

        if (jDiagram.resizedir > 0 && !jDiagram.jHoverElement) {
          jDiagram.nodes[did].toggleSelect(false);
        }

        var jBound = jDiagram.nodes[did].getBounds();
        if (Utils.isUseExcanvas()) {
          if (jBound.top == draggingBounds[did].top && jBound.left == draggingBounds[did].left &&
            jBound.width == draggingBounds[did].width && jBound.height == draggingBounds[did].height) {
            jDiagram.dragElement = false;
          }
        }

        jDiagram.removeVerticalLine();
        jDiagram.removeHorizontalLine();
        jDiagram.addCenterPoint(jDiagram.hcenters, jBound.left + jBound.width / 2);
        jDiagram.addCenterPoint(jDiagram.vcenters, jBound.top + jBound.height / 2);
      }
    }
    ,
    /**
     * At the end of the drag and drop nodes, we should change its states and trigger ondragover event.
     * @param jDiagram
     * @param dragPosition : the mouse position at dragging start
     * @param dropPosition : the mouse position at dragging end
     */
    draggingNodeEnd: function(jDiagram, dragPosition, dropPosition) {
      if (jDiagram.dragElement &&
        (dragPosition.x != dropPosition.x ||
          dragPosition.y != dropPosition.y)) {
        jDiagram.syncJson();
        jDiagram.resizedir == 0 && jDiagram.triggerDragEnd(jDiagram.selections, dragPosition, dropPosition);
        // This is for model sync.
        jDiagram.triggerElementOperation(jDiagram.selections, 'nodeschange', 'drag');
      }
    }
    ,
    /**
     * Install Dragging Handler for paths.
     * @param draggingHandler
     * @return
     */
    draggingPath: function(jDiagram, e, position) {
      // [Dragging]
      var dragging = 'mousemove.dragbend' + jDiagram.id;
      var dragend = 'mouseup.dragbend' + jDiagram.id;
      var jPath = jDiagram.jHoverElement;
      var hoverBend = jPath.hoverBend;
      var index = hoverBend.index;
      var pts = jPath.getRelativeRouteList();
      var elbowPoints = jPath.options.elbowPoints;
      var bounds = jPath.getBounds();
      var bgPath = null;
      var drawRef = true;
      var that = this;

      $(document).bind('selectstart.drag', function() {
        return false;
      }).bind(dragging, function(emv) { // [Drag move]
        if (drawRef && (e.pageX != emv.pageX || e.pageY != emv.pageY) &&
          jPath.jType.name != 'bezier' && jPath.jType.name != 'quadratic') {
          jDiagram.$box.addClass('dragging');

          if (jPath.options.controls.length > 0 && elbowPoints.length == 0) { // elbow lines to transform elbowPoints.
            for (var i = 2; i < pts.length - 2; i += 2) {
              elbowPoints.push({
                x: pts[i] + bounds.left,
                y: pts[i + 1] + bounds.top,
              });
            }
            jPath.options.controls = [];
          }

          bgPath = that.createReferEdge(jPath, jDiagram, index);

          drawRef = false;
        }
        jPath.moveHoverBend(emv.pageX - e.pageX, emv.pageY - e.pageY, position);
      }).bind(dragend, function(eup) { // [Drag end]
        var isDelete = false;
        if (jPath.jType.name != 'bezier' && jPath.jType.name != 'quadratic') {
          var x1 = pts[index * 2];
          var y1 = pts[index * 2 + 1];
          var x2 = pts[index * 2 + 4];
          var y2 = pts[index * 2 + 5];
          var offset = jPath.$box.offset();
          var x = eup.pageX - offset.left;
          var y = eup.pageY - offset.top;

          // triangle area to calculate h
          // S = 1/2 * h * L((x1, y1), (x2, y2))
          //  = 1/2 * L((x1, y1), (x, y)) * L((x2, y2), (x, y))
          //  (x1, y1)  (x, y)
          //   ______
          //   \   |
          //   \  |
          //    \  |
          //    \ |
          //     \ |
          //     \| (x2, y2)
          var S = Math.abs((1 / 2) * (x1 * y2 + x2 * y + x * y1 - x1 * y - x2 * y1 - x * y2));
          var h = 2 * S / Math.sqrt((x1 - x2) * (x1 - x2) + (y1 - y2) * (y1 - y2));

          // if the distance from bend to line less than 6px, delete bend
          if (h < 8) {
            if (elbowPoints.length != 0) {
              elbowPoints.splice(index, 1);
              isDelete = true;
            }
          }
        }
        jPath.update();

        if (!drawRef) {
          jDiagram.triggerBendMove(jPath, index, elbowPoints, isDelete);
        }

        bgPath && jDiagram.remove(bgPath, '', true);

        jDiagram.$doc.unbind(dragend).unbind(dragging).unbind('selectstart.drag');
        jDiagram.$box.removeClass('dragging');
      });
    }
    ,
    /**
     * Create a edge for reference.
     */
    createReferEdge: function(jPath, jDiagram, index) {
      var options = $.extend(true, {}, jPath.options);
      options.id = jDiagram.id + '_refpath';
      options.controls.splice(index * 2, 2);
      if (options.elbowPoints.length != 0) {
        options.elbowPoints.splice(index, 1);
        if (options.elbowPoints.length == 0) { // Elbow line drag bend and refer edge should reduce controls.
          options.controls.splice(index * 2, 2);
        }
      }
      options.figures[0].context.strokeStyle = 'rgba(0,0,0,0.4)';
      delete options.textareas;

      var bgPath = jDiagram.createPathOuter(options, 'inner');

      if (options.controls.length == 0) {
        bgPath.options.controls = [];
        bgPath.update();
      }

      return bgPath;
    }
    ,
    draggingOrthogonalEdge: function(jDiagram, draggingHandler) {
      // [Dragging]
      var dragging = 'mousemove.dragedge' + jDiagram.id;
      var dragend = 'mouseup.dragedge' + jDiagram.id;
      var jPath = jDiagram.jHoverElement;

      if (jPath) {
        if (jPath.draggingDir == 'horizontal') {
          jDiagram.$box.addClass('draggingedge_horizontal');
        } else if (jPath.draggingDir == 'vertical') {
          jDiagram.$box.addClass('draggingedge_vertical');
        }
      }

      $(document).bind('selectstart.drag', function() {
        return false;
      }).bind(dragging, function(emv) { // [Drag move]
        draggingHandler.apply(jDiagram, arguments);
      }).bind(dragend, function(eup) { // [Drag end]
        jDiagram.$doc.unbind(dragend).unbind(dragging).unbind('selectstart.drag');
        jDiagram.$box.removeClass('draggingedge_horizontal');
        jDiagram.$box.removeClass('draggingedge_vertical');
      });
    },
    /**
     * Select hover element
     * @param jTargetElement, node or path which received event.
     * @param e, jQuery.Event.
     * @return diagram's jHoverElement for convenience.
     */
    selectHover: function(jDiagram, jTargetElement, e) {
      // Select hover element
      var jHover = jDiagram.jHoverElement;
      jBME.Diagram.selectPlugin.select(jDiagram, jHover, e);

      return jHover;
    },
  });
  /**
   * Update the dragging bounds[|left,|top,|width,|height]
   *  new bonds = old bounds + (Coefficient * Difference);
   */
  jBME.Diagram.selectdragPlugin.updateDraggingBounds = function(jDiagram, draggingBounds, difX, difY, event) {
    var dif = jBME.Diagram.selectdragPlugin.restrictDraggingBounds(jDiagram, draggingBounds, difX, difY);
    var diffX = dif.difX;
    var diffY = dif.difY;
    var cbound = Utils.RESIZE.DIRBOUNDS[jDiagram.resizedir];

    for (var id in draggingBounds) {
      var oldBound = draggingBounds[id];
      var newBound = {};
      for (var i = 0; i < cbound.length; ++i) { // Only update the dimension that changed(which means coefficient is not zero).
        if (cbound[i]) { // dimension: "left"|"top"|"width"|"height"
          var dimension = Utils.RESIZE.DIMENSION[i];
          var node = jDiagram.nodes[id];
          var op = node.options;
          var parent = op.parent;
          if (!parent || parent == '' || node.$box.css('display') == 'none') { // It's not in container
            newBound[dimension] = Math.max(0, oldBound[dimension] + cbound[i] * (i % 2 == 0 ? diffX : diffY));
          } else { // Its bounds can't out of its parent's bounds.
            var parentBounds = jDiagram.nodes[parent].getBounds();
            var parentLineWidth = jDiagram.nodes[parent].options.figures[0].context.lineWidth;
            if (i < 2) { // 0 and 1 : the min value of left and top when this node is in container.
              newBound[dimension] = Math.min(parentBounds[dimension] + parentBounds[Utils.RESIZE.DIMENSION[i + 2]] - parentLineWidth - oldBound[Utils.RESIZE.DIMENSION[i + 2]],
                Math.max(parentBounds[dimension] + parentLineWidth, oldBound[dimension] + cbound[i] * (i % 2 == 0 ? diffX : difY)));
            } else { // 2 and 3 : the min value of width and height when this node is in container.
              newBound[dimension] = Math.min(parentBounds[Utils.RESIZE.DIMENSION[i - 2]] + parentBounds[dimension] - parentLineWidth - (newBound[Utils.RESIZE.DIMENSION[i - 2]] || oldBound[Utils.RESIZE.DIMENSION[i - 2]]),
                oldBound[dimension] + cbound[i] * (i % 2 == 0 ? diffX : diffY));
            }
          }
        }
      }

      // Move and resize textnode
      var onpath = jDiagram.nodes[id].options.onpath;
      if (onpath && onpath.pathid && onpath.scale >= 0) {
        if (jDiagram.resizedir == 0) { // move textnode
          newBound = jDiagram.nodes[id].moveTextNode(jDiagram.paths[jDiagram.nodes[id].options.onpath.pathid], event);
        } else { // resize textnode
          newBound = jDiagram.nodes[id].resizeTextNode(oldBound, newBound);
        }
      }

      if (!jDiagram.nodes[id].isswimlane) { // SwimLane is special container, we need to in consideration of lane terrace.
        if (jDiagram.resizedir == 0) {
          newBound.left && (newBound.left = Utils.round(newBound.left, jDiagram.dragStep));
          newBound.top && (newBound.top = Utils.round(newBound.top, jDiagram.dragStep));
        } else {
          newBound.left && (newBound.left = Utils.round(newBound.left, jDiagram.resizeStep));
          newBound.top && (newBound.top = Utils.round(newBound.top, jDiagram.resizeStep));

          // 当resize的偏移量到步长的一半的时候，保证width和height的四舍五入效果和left和top一直。
          newBound.width && (newBound.width = Math.max(jDiagram.nodes[id].minWidth, Utils.round(newBound.width - 0.00001, jDiagram.resizeStep)));
          newBound.height && (newBound.height = Math.max(jDiagram.nodes[id].minHeight, Utils.round(newBound.height - 0.00001, jDiagram.resizeStep)));
        }
      }

      jDiagram.nodes[id].updateBounds(newBound, null, false);

      // Swimlane resize
      if (jDiagram.nodes[id].isswimlane && jDiagram.nodes[id].lanes.length > 1 && jDiagram.resizedir > 0) {
        jDiagram.nodes[id].resizeSwimLane(diffX, diffY, oldBound, newBound, jDiagram.resizedir);
      }
    }

    for (var pid in jDiagram.paths) {
      var path = jDiagram.paths[pid];
      var sourceid = path.jSource.id;
      var targetid = path.jTarget.id;

      if (draggingBounds[sourceid] || draggingBounds[targetid]) {
        path.updateBounds();
      }
    }
  };
  /**
   * Whether selections can drag
   * If some nodes reach to the side, stop.
   * @param draggingBounds
   * @param difX : horizontal offset
   * @param difY : vertical offset
   * @returns newOffset : keep dragging and resize node will be not out of the bound of diagram.
   */
  jBME.Diagram.selectdragPlugin.restrictDraggingBounds = function(jDiagram, draggingBounds, difX, difY) {
    var scope = jBME.Diagram.selectdragPlugin.getScope(jDiagram, draggingBounds);

    if (jDiagram.resizedir == 0) { // Dragging
      difX = scope.right >= 0 ? Math.min(Math.max(-scope.left, difX), scope.right) : Math.max(-scope.left, difX);
      difY = scope.bottom >= 0 ? Math.min(Math.max(-scope.top, difY), scope.bottom) : Math.max(-scope.top, difY);
    } else { // Resize
      for (var rid in draggingBounds) {
        var distance = jDiagram.nodes[rid].resizeScope(jDiagram.resizedir, difX, difY, draggingBounds[rid]);
        difX = distance.difX;
        difY = distance.difY;
      }
    }

    return {
      difX: difX,
      difY: difY,
    };
  };
  jBME.Diagram.selectdragPlugin.getScope = function(jDiagram, draggingBounds) {
    var topNum = 0;
    var leftNum = 0;
    var rightNum = -1;
    var bottomNum = -1;

    for (var fid in draggingBounds) { // First left and top of draggingBounds as refrence.
      topNum = draggingBounds[fid].top;
      leftNum = draggingBounds[fid].left;
      if (jDiagram.nodes[fid].options && $.trim(jDiagram.nodes[fid].options.parent) != '' &&
        !draggingBounds[jDiagram.nodes[fid].options.parent] && jDiagram.resizedir == 0) {
        var parentBound = jDiagram.nodes[jDiagram.nodes[fid].options.parent].getBounds();
        var lineWidth = jDiagram.nodes[fid].options.figures[0].context.lineWidth;
        rightNum = parentBound.left + parentBound.width - draggingBounds[fid].left - draggingBounds[fid].width - lineWidth;
        bottomNum = parentBound.top + parentBound.height - draggingBounds[fid].top - draggingBounds[fid].height - lineWidth;
        break;
      }
    }

    for (var did in draggingBounds) { // Get min left and top of draggingBounds
      topNum = Math.min(draggingBounds[did].top, topNum);
      leftNum = Math.min(draggingBounds[did].left, leftNum);
      if (jDiagram.nodes[fid].options && $.trim(jDiagram.nodes[did].options.parent) != '' &&
        !draggingBounds[jDiagram.nodes[did].options.parent] && jDiagram.resizedir == 0) {
        var pBound = jDiagram.nodes[jDiagram.nodes[did].options.parent].getBounds();
        var lineWidth = jDiagram.nodes[jDiagram.nodes[did].options.parent].options.figures[0].context.lineWidth;
        topNum = Math.min(draggingBounds[did].top - pBound.top - lineWidth, topNum);
        leftNum = Math.min(draggingBounds[did].left - pBound.left - lineWidth, leftNum);
        rightNum = Math.min(pBound.left + pBound.width - draggingBounds[did].left - draggingBounds[did].width - lineWidth, rightNum);
        bottomNum = Math.min(pBound.top + pBound.height - draggingBounds[did].top - draggingBounds[did].height - lineWidth, bottomNum);
      }
    }

    return {
      left: leftNum,
      top: topNum,
      right: rightNum,
      bottom: bottomNum,
    };
  };
  jBME.Editor.register(jBME.Diagram.selectdragPlugin);

  jBME.Diagram.dblclickPlugin = function(e, jElement, jDiagram) {
    if ((!Utils.isUseExcanvas() && e.which != 1) || (Utils.isUseExcanvas() && e.which != 0)) {
      return;
    }

    jDiagram.toggleTextEdit(undefined, e, jDiagram.jHoverElement);
  };

  jBME.Diagram.nodedblclickPlugin = jBME.AbstractPlugin.extend({
    name: 'nodedblclickPlugin',
    eventHandlers: [{
      event: 'dblclick',
      handler: 'nodedblclickPlugin',
      targetselector: '.uee-node',
    }],
    nodedblclickPlugin: function(e, jElement, jDiagram) {
      if (jDiagram.jHoverElement && jDiagram.jHoverElement.isnode) {
        jBME.Diagram.dblclickPlugin.call(this, e, jElement, jDiagram);
      }
    },
  });
  jBME.Editor.register(jBME.Diagram.nodedblclickPlugin);

  jBME.Diagram.pathdblclickPlugin = jBME.AbstractPlugin.extend({
    name: 'pathdblclickPlugin',
    eventHandlers: [{
      event: 'dblclick',
      handler: 'pathdblclickPlugin',
      targetselector: '.uee-path',
    }],
    pathdblclickPlugin: function(e, jElement, jDiagram) {
      if (jDiagram.jHoverElement && jDiagram.jHoverElement.ispath) {
        jBME.Diagram.dblclickPlugin.call(this, e, jElement, jDiagram);
      }
    },
  });
  jBME.Editor.register(jBME.Diagram.pathdblclickPlugin);

  jBME.Diagram.connectionTextPlugin = jBME.AbstractPlugin.extend({
    name: 'connectionTextPlugin',
    eventHandlers: [{
      event: 'dblclick',
      handler: 'textManager',
      targetselector: '.uee-path',
    },
    {
      event: 'mousedown',
      handler: 'textManager',
      targetselector: '.uee-path',
    },
    ],
    textManager: function(e, jElement, jDiagram) {
      // Click on the other path's text underlying.
      var jPathId; var name;
      if ($.nodeName(e.target, 'pre')) {
        jPathId = $(e.target).closest('.uee-path').attr('id');
        name = $(e.target).closest('.uee-path-text').attr('name');

        if (e.type == 'dblclick') {
          this.editText(e, jDiagram, jPathId, name);
        } else if (e.type == 'mousedown') {
          this.dragText(e, jDiagram, jPathId, name);
        }

        return;
      }

      // Click on the other places.
      var eventPosition = jDiagram.getPositionWithScroll(e);
      var paths = jDiagram.paths;
      var coveredoptions = this.isOnConnectionText(eventPosition, paths);

      if (coveredoptions) { // Click on the other path's text.
        jPathId = coveredoptions.pathId;
        name = coveredoptions.textName;

        if (e.type == 'dblclick') {
          this.editText(e, jDiagram, jPathId, name);
        } else if (e.type == 'mousedown') {
          this.dragText(e, jDiagram, jPathId, name);
        }
      } else if (e.type == 'dblclick') {
        var jPath = jDiagram.jHoverElement;
        if (jPath && jPath.ispath) {
          var textName = this.createText(e, jDiagram, jPath);
          this.editText(e, jDiagram, jPath.id, textName);
        }
      }
    },
    /**
     * Create connection text on path.
     * @param e : dblclick event
     * @param jDiagram
     * @param jPath : this path
     */
    createText: function(e, jDiagram, jPath) {
      // dblclick to edit text on path switch
      if (!jDiagram.authManager(jPath, 'editable') || $.nodeName(e.target, 'textarea')) {
        return;
      }

      var mousePos = jDiagram.getPositionWithScroll(e);
      var closestPosition = jPath.getClosestPointFromPath(mousePos.left, mousePos.top);
      var position = {
        x: closestPosition[0],
        y: closestPosition[1],
      };

      var content = jDiagram.defaultConnectionText;
      var textBounds = this.getInitTextBounds(position, content);
      var style = this.getStyle(textBounds, jPath.bounds);
      var textName = this.getTextName(jPath);

      var $textBox = this.createDOM(jPath, style, content, textName);

      // Store textareas options
      jPath.options.textareas[textName] = {
        location: closestPosition[2],
        side: 0,
        value: content,
      };

      // Store textares DOM
      !jPath.textBoxes && (jPath.textBoxes = {});
      jPath.textBoxes[textName] = $textBox;

      return textName;
    },
    /**
     * Create DOM of connection text.
     * @param jPath : path
     * @param style : left, top, width, height of text
     * @param content : text content
     */
    createDOM: function(jPath, style, content, textName) {
      var name = textName;
      var $textBox = jQuery(
        ['<div name = "', name, '" class="uee-path-text" style="', style, '">', '<pre>', content, '</pre>', '</div>'].join(''));

      jPath.$box.append($textBox);

      return $textBox;
    },
    /**
     * Get the bounds of text for initialization.
     * @param position : the point position on path
     * @param content : text content
     * @returns {left, top, width, height} of text content
     */
    getInitTextBounds: function(position, content) {
      var bounds = {
        left: position.x,
        top: position.y,
      };
      var width_height = Utils.getWidthHeight(content);

      bounds.width = width_height.width;
      bounds.height = width_height.height;

      return bounds;
    },
    /**
     * Get the position of connection text
     * @param closestPoint : the closest x and y
     * @returns style : left, top, width, height of text
     */
    getStyle: function(textBounds, pathBounds) {
      var width = textBounds.width;
      var height = textBounds.height;
      var left = Utils.toFixed(textBounds.left - width / 2 - pathBounds.left, 2);
      var top = Utils.toFixed(textBounds.top - height / 2 - pathBounds.top, 2);
      var style = ['left:', left, 'px;top:', top, 'px;'].join('');
      return style;
    },
    /**
     * Get the name or key of text.
     * eg: {text0, text1, text2} then return text3.
     * @param jPath : the instance of path
     * @returns text name : text n
     */
    getTextName: function(jPath) {
      var index = 0;
      var textareas = jPath.options.textareas;

      if (!$.isEmptyObject(textareas)) {
        while (textareas['text' + index]) {
          index++;
        }
      }

      return 'text' + index;
    },
    /**
     * Edit text when dblclick text.
     * @param e
     * @param jDiagram
     * @param jPathId : id of path
     * @param name : text name
     */
    editText: function(e, jDiagram, jPathId, name) {
      var $pathTextBox = $(e.target).closest('.uee-path-textedit');

      if ($pathTextBox.length > 0) { // Textarea has been created, select content of textarea.
        $pathTextBox.children('textarea').select();
        return;
      }

      var jPath = jDiagram.paths[jPathId];

      // dblclick to edit text on path switch
      if (!jDiagram.authManager(jPath, 'editable') || $.nodeName(e.target, 'textarea')) {
        return;
      }

      var textarea = jPath.options.textareas[name];
      var $textBox = jPath.textBoxes[name];
      var content = textarea.value;

      // For setting the z-index of textarea next step.
      jPath.$box.css('z-index', 'auto');

      // Add textarea to edit.
      var $textareaBox = this.createTextArea($textBox, content);

      // Textarea self adaption
      this.textareaAdaption($textareaBox);

      // Remove textarea after finished.
      this.editTextArea(jDiagram, jPath, name, $textareaBox);
    },
    /**
     * Calculate event position whether it is on the other path's text.
     * @param position : event position
     * @param paths : all paths in diagram
     * @returns {path, textName}
     */
    isOnConnectionText: function(position, paths) {
      var coveredoptions = {};
      for (var id in paths) {
        var textBoxes = paths[id].textBoxes;
        var pathBounds = paths[id].bounds;

        for (var name in textBoxes) {
          var $textBox = textBoxes[name];
          var eventBounds = {
            left: position.left,
            top: position.top,
            width: 0,
            height: 0,
          };
          var DOMBounds = this.getBoundsFromDOM($textBox);
          var boxBounds = {
            left: DOMBounds.left + pathBounds.left,
            top: DOMBounds.top + pathBounds.top,
            width: DOMBounds.width,
            height: DOMBounds.height,
          };

          if (Utils.containsBound(boxBounds, eventBounds)) {
            coveredoptions.pathId = id;
            coveredoptions.textName = name;
          }
        }

        if (!$.isEmptyObject(coveredoptions)) {
          return coveredoptions;
        }
      }

      return null;
    },
    /**
     * Add textarea to edit.
     * @param $text
     * @param content
     * @returns
     */
    createTextArea: function($textBox, content) {
      var $textareaBox = jQuery(
        ['<div class="uee-path-textedit"><textarea>', content, '</textarea></div>'].join(''));

      $textBox.append($textareaBox);

      // Expand textarea to edit.
      var width_height = Utils.getWidthHeight(content);
      if ($.browser.msie) {
        width_height.width += 18;
      }
      var bounds = {
        width: width_height.width + 20 + 'px',
        height: width_height.height + 20 + 'px',
      };
      $textareaBox.css(bounds);

      var textarea = $textareaBox.children('textarea')[0];

      // Set focus at the last position of title.
      setTimeout(function() {
        if (textarea) {
          var len = textarea.value.length;
          $(textarea).focus();

          if (document.selection) { // To support IE
            var textRange = textarea.createTextRange();
            textRange.moveStart('character', len);
            textRange.collapse();
            textRange.select();
          } else if (typeof textarea.selectionStart == 'number' && typeof textarea.selectionEnd == 'number') { // firefox and chrome
            textarea.selectionStart = textarea.selectionEnd = len;
          }
        }
      }, 1);

      return $textareaBox;
    },
    /**
     * Remove textarea after finished.
     * @param jDiagram
     * @param jPath
     * @param name : the key of connection text
     * @param $textBox : The temporary building textarea
     */
    editTextArea: function(jDiagram, jPath, name, $textareaBox) {
      var that = this;
      var $textBox = jPath.textBoxes[name];
      var textarea = jPath.options.textareas[name];

      $textBox.toggleClass('textedit');

      jDiagram.$box.unbind('mousedown.removetextarea').bind('mousedown.removetextarea',
        function(event) {
          if ($(event.target).closest('.uee-path-textedit').length == 0) {
            var $textarea = $textareaBox.children('textarea');
            var content = $textarea.val();

            if (content == '') {
              jPath.options.textareas[name] = null;
              delete jPath.textBoxes[name];
              $textBox.remove();
            } else {
              // To support IE 7,8.To convey the '\n'.
              if ($.browser.msie && parseInt($.browser.version) == 8) {
                content = content.replace(/(\n)/gm, '\n');
              } else if ($.browser.msie && parseInt($.browser.version) == 7) {
                content = content.replace(/(\n)/gm, '\r');
              }

              textarea.value = content;

              var oldBounds = that.getBoundsFromDOM($textBox);
              var oldWidthandHeight = Utils.getWidthHeight($textBox.children('pre').text());

              oldBounds.width = oldWidthandHeight.width;
              oldBounds.height = oldWidthandHeight.height;
              $textBox.children('pre').text(content);

              var newBounds = Utils.getWidthHeight(content);
              that.updateTextBounds($textBox, oldBounds, newBounds);
            }

            $textarea.unbind('keyup.connectionText');
            $textareaBox.remove();
            jPath.$box.css('z-index', '1');
            $textBox.toggleClass('textedit');
            jDiagram.$box.unbind('mousedown.removetextarea');
            jDiagram.syncJson();
          }
        }
      );
    },
    /**
     * Get bounds of DOM.
     * @param $box : DOM
     * @returns bounds : {left, top, width, height}
     */
    getBoundsFromDOM: function($box) {
      var position = Utils.getStyle($box.attr('style'));
      var bounds = {
        left: position.left,
        top: position.top,
        width: $box.width(),
        height: $box.height(),
      };
      return bounds;
    },
    /**
     * Update left and top of text's DOM.
     * @param $text : text DOM
     * @param oldBounds : bounds before edit
     * @param newBounds : bounds after edit
     */
    updateTextBounds: function($textBox, oldBounds, newBounds) {
      var position = {
        left: oldBounds.left + (oldBounds.width - newBounds.width) / 2,
        top: oldBounds.top + (oldBounds.height - newBounds.height) / 2,
      };

      $textBox.css(position);
    },
    /**
     * The adaption of width and height for editing textarea.
     * @param $textBox : The temporary building textarea
     */
    textareaAdaption: function($textareaBox) {
      var $textarea = $textareaBox.children('textarea');

      $textarea.bind('keyup.connectionText', function(event) {
        var content = $textarea.val();
        var width_height = Utils.getWidthHeight(content);
        if ($.browser.msie) {
          width_height.width += 18;
        }
        var bounds = {
          width: width_height.width + 20 + 'px',
          height: width_height.height + 20 + 'px',
        };

        $textareaBox.css(bounds);
      });
    },
    /**
     * Drag text along path.
     * @param e
     * @param jDiagram
     * @param jPathId : id of this path
     * @param name : text name
     */
    dragText: function(e, jDiagram, jPathId, name) {
      var jPath = jDiagram.paths[jPathId];
      var width_height = Utils.getWidthHeight(jPath.options.textareas[name].value);
      var that = this;

      // dblclick to edit text on path switch
      if (!jDiagram.authManager(jPath, 'editable') || $.nodeName(e.target, 'textarea')) {
        return;
      }

      this.dragging(jDiagram, e, function(emv) {
        var mousePos = jDiagram.getPositionWithScroll(emv);
        var closestPosition = jPath.getClosestPointFromPath(mousePos.left, mousePos.top);
        var position = {
          x: closestPosition[0],
          y: closestPosition[1],
        };
        var location = encodeURIComponent(closestPosition[2]);

        that.moveText(jPath, name, position, location, width_height);
      });
    },
    /**
     * Dragging event and callback operation.
     * @param jDiagram
     * @param draggingHandler
     */
    dragging: function(jDiagram, e, draggingHandler) {
      var dragging = 'mousemove.dragtext';
      var dragend = 'mouseup.dragtext';

      jDiagram.$box.addClass('draggingtext');
      $(document).bind('selectstart.dragtext', function() {
        return false;
      }).bind(dragging, function(emv) {
        draggingHandler.apply(jDiagram, arguments);
      }).bind(dragend, function(eup) {
        $(document).unbind(dragend).unbind(dragging).unbind('selectstart.dragtext');
        jDiagram.$box.removeClass('draggingtext');

        if (e.pageX != eup.pageX || e.pageY != eup.pageY) {
          jDiagram.syncJson();
        }
      });
    },
    /**
     * Move text according to closest position from event position to path.
     * @param jPath : the instance of path
     * @param name : text key
     * @param position : the closest position on path
     * @param location : new location of text
     * @param width_height : width and height of text
     */
    moveText: function(jPath, name, position, location, width_height) {
      var textarea = jPath.options.textareas[name];
      var $textBox = jPath.textBoxes[name];
      pathBounds = jPath.bounds,
      width = width_height.width,
      height = width_height.height,

      leftValue = Utils.toFixed((position.x - width / 2 - pathBounds.left), 2) + 'px',
      topValue = Utils.toFixed((position.y - height / 2 - pathBounds.top), 2) + 'px',
      bounds = {
        left: leftValue,
        top: topValue,
      };

      $textBox.css(bounds);
      textarea.location = location;
    },
  });

  jBME.Diagram.drawlinePlugin = jBME.AbstractPlugin.extend({
    name: 'drawlinePlugin',
    eventHandlers: [{
      event: 'hover',
      handler: 'isConnect',
      targetselector: '.uee-node',
    },
    {
      event: 'mousemove',
      handler: 'isConnect',
      targetselector: '.uee-node',
    },
    {
      event: 'mousedown',
      handler: 'connect',
      targetselector: '.uee-node',
    },
    ],
    isConnect: function(e, jElement, jDiagram) {
      if (jDiagram.isDragStart || !jElement || jElement.ispath || jDiagram.request.has() ||
        jDiagram.$box.hasClass('dragging') || jDiagram.viewmode == 'view') {
        if (jDiagram.$box.hasClass('connectstart')) {
          jDiagram.$box.removeClass('connectstart');
        }
        return;
      }

      var pos = jElement.getPositionWithScroll(e);
      var anchor = jElement.getClosestAnchorFromPosition(pos);
      var anchorPos = jElement.getAnchorPosition(anchor);

      if (Utils.distance(pos.left, pos.top, anchorPos.left, anchorPos.top) <= jDiagram.drawLineScope) {
        jDiagram.$box.addClass('connectstart');
      } else if (jDiagram.$box.hasClass('connectstart')) {
        jDiagram.$box.removeClass('connectstart');
      }
    },
    connect: function(e, jElement, jDiagram) {
      if (!jElement || jElement.ispath || jDiagram.request.has() ||
        jDiagram.viewmode == 'view' || jDiagram.$box.hasClass('resize') || e.which != 1) {
        return;
      }

      var pos = jElement.getPositionWithScroll(e);
      var anchor = jElement.getClosestAnchorFromPosition(pos);
      var anchorPos = jElement.getAnchorPosition(anchor);

      if (Utils.distance(pos.left, pos.top, anchorPos.left, anchorPos.top) <= 8) {
        jDiagram.drawing(function() {
          jDiagram.drawingPathFromAnchor(jElement, anchor, e);
        }, e);
      }
    },
  });
  jBME.Editor.register(jBME.Diagram.drawlinePlugin);

  jBME.Diagram.constraintPlugin = jBME.AbstractPlugin.extend({
    name: 'constraintPlugin',
    eventHandlers: [{
      event: jBME.Diagram.EVENT.connectstart,
      handler: 'onConnect',
      targetselector: 'all',
    },
    {
      event: jBME.Diagram.EVENT.connectend,
      handler: 'onConnect',
      targetselector: 'all',
    },
    ],
    onConnect: function(e, jElement, jDiagram) {
      if (jElement && ((jElement.ispath && !this.canConnect(jElement)) || !this.canPathBegin(jElement))) {
        e.preventDefault();
        e.stopPropagation();
      }
    },
    canConnect: function(jPath) {
      var source = jPath.jSource;
      var target = jPath.jTarget;
      var maxOutGoing = source.options.maxOutGoing;
      if (maxOutGoing) {
        var cardinality = 0;
        for (var i in source.paths) {
          cardinality = cardinality + 1;
        }

        if (cardinality > maxOutGoing) {
          return false;
        }
      }

      var nodes = source.options.nextnodes;

      // return true if did not define nextNodes
      if (nodes == undefined) {
        return true;
      }

      if (nodes.length == 0) {
        return false;
      }

      // compare with target's name by lowercase
      // 'all' means this source could connect any node
      // 'none' means this source could not connect
      for (var i = 0; i < nodes.length; i++) {
        if (nodes[i] == 'all' ||
          nodes[i].toLocaleLowerCase() == target.options.name.toLocaleLowerCase()) {
          return true;
        }
      }

      return false;
    },
    canPathBegin: function(jElement) {
      var nodes = jElement.options.nextnodes;

      // return true if did not define nextNodes
      if (nodes == undefined) {
        return true;
      }

      if (nodes.length == 0) {
        return false;
      }
      return true;
    },
  });
  jBME.Editor.register(jBME.Diagram.constraintPlugin);

  jBME.Diagram.resizeNodePlugin = jBME.AbstractPlugin.extend({
    name: 'resizeNodePlugin',

    eventHandlers: [{
      event: 'mousemove',
      handler: 'onResizeNode',
    }],
    onResizeNode: function(e, jElement, jDiagram) {
      // [resize]
      var oldElement = jElement;

      // To solve container's resize problem when it is covered by path.
      // We should use jHover and selected container.
      if (jElement) {
        if (!jDiagram.jHoverElement && jDiagram.getFirstSelection()) {
          jElement = jDiagram.getFirstSelection();
        } else if (jDiagram.jHoverElement && jDiagram.jHoverElement.isnode) {
          jElement = jDiagram.jHoverElement;
        }
      }

      if (!jElement || jDiagram.isDragStart || jDiagram.$box.hasClass('dragging')) {
        return;
      }
      // Resize switch
      if (!jDiagram.authManager(jElement, 'resizable')) {
        return;
      }

      // The event should bubble, but parent element should not handler it.Cause we can only resize one element.
      if (!jElement.isnode || e.stopElementPropagation || jDiagram.dragElement) {
        !jElement.ispath && jElement.$box.css('cursor') && jElement.$box.css('cursor', '');
        return;
      }
      e.stopElementPropagation = true;

      // Update 'resizingElement' and the resize-cursor.
      var pos = jElement.getPositionWithScroll(e);
      var anchor = jElement.getClosestAnchorFromPosition(pos);
      var anchorPos = jElement.getAnchorPosition(anchor);
      jDiagram.resizedir = (jDiagram.request.isEmpty() &&
      Utils.distance(pos.left, pos.top, anchorPos.left, anchorPos.top) <= jDiagram.resizeScope ? anchor : 0);
      jElement.isnode && jElement.$box.css('cursor', jDiagram.resizedir ? window.Utils.RESIZE.CURSORS[anchor] : '');
      oldElement.isenode && oldElement.$box.css('cursor', jDiagram.resizedir ? window.Utils.RESIZE.CURSORS[anchor] : '');
      jDiagram.resizeElement = jElement;

      if (jDiagram.resizedir > 0) {
        jDiagram.$box.removeClass('hover');
        jDiagram.$box.addClass('resize');
      } else {
        jDiagram.$box.removeClass('resize');
      }
    },
  });
  jBME.Editor.register(jBME.Diagram.resizeNodePlugin);

  jBME.Diagram.selectPlugin = jBME.AbstractPlugin.extend({
    name: 'selectPlugin',

    eventHandlers: [{
      event: 'mousedown',
      handler: 'onMouseDownMarquee',
      targetselector: 'all',
    }],

    onMouseDownMarquee: function(e, jElement, jDiagram) {
      // Should not marquee when has selected element or is resizing element.
      if (!$.isEmptyObject(e.selections) || jDiagram.resizedir > 0 ||
        jDiagram.jReconnectPath || $.nodeName(e.target, 'textarea') || jDiagram.$box.hasClass('draggingtext')) {
        return;
      }

      // Should not marquee when we are clicking scrollbar.
      var eventPosition = jDiagram.getPositionFromOffset(e);
      var clientDOM = jDiagram.$client[0];
      if (e.type == 'mousedown' && (eventPosition.left > clientDOM.clientWidth ||
        eventPosition.top > clientDOM.clientHeight)) {
        return;
      }

      // When the event has selected nothing descendants, we should call select null to update selection.
      // If nothing selected yet, there is no need to select null again.
      var pos = jDiagram.getPositionFromOffset(e);
      var bounds = $.extend({
        width: 0,
        height: 0,
      }, pos);
      jDiagram.selectionSize > 0 && jBME.Diagram.selectPlugin.select(jDiagram, null, e);

      jDiagram.$marquee.css(bounds);

      // temp solve method， wait BME to compelete
      var activemenu = $(e.target).closest('.uee-activemenu-container');
      if (activemenu && activemenu.length > 0) {
        return;
      }

      $(document).bind('selectstart.drag', function() {
        return false;
      }).bind('mousemove.marquee', function(emv) {
        if ((e.pageX != emv.pageX || e.pageY != emv.pageY) && !jDiagram.$box.hasClass('marquee')) {
          jDiagram.$box.addClass('marquee');
        }
        var curpos = jDiagram.getPositionFromOffset(emv);
        bounds.left = Math.min(pos.left, curpos.left);
        bounds.top = Math.min(pos.top, curpos.top);
        bounds.width = Math.abs(pos.left - curpos.left);
        bounds.height = Math.abs(pos.top - curpos.top);
        jDiagram.$marquee.css(bounds);
      }).bind('mouseup.marquee', function(eup) {
        jDiagram.$box.removeClass('marquee');
        $(document).unbind('mouseup.marquee').unbind('mousemove.marquee').unbind('selectstart.drag');
        if (bounds.width > 0 && bounds.height > 0) {
          jBME.Diagram.selectPlugin.selectAll(jDiagram, bounds);
        }
      });
    },
  });
  /**
   * Select Element in diagram.
   * @param jElement, select target, can be node or path. null to clear selections.
   * @param e, jQuery.Event, [optional], if null, just add element to selections.
   */
  jBME.Diagram.selectPlugin.select = function(jDiagram, jElement /* can be null*/, e /* [optional]*/) {
    // Handler current elements selection change.
    var oldSelections = jDiagram.selections;
    var oldSize = jDiagram.selectionSize;
    if (jElement == null || (e && !e.ctrlKey && !jElement.isselected)) {
      for (var id in jDiagram.selections) {
        jDiagram.selections[id].toggleSelect(false);
      }
      jDiagram.selections = {}, jDiagram.selectionSize = 0;
    }

    // Select current element, may be nothing. If ctrlKey is pressed, toggle select status.
    // if not ctrl+A, must be mouse left click.
    if (jElement && (!e || (e && e.which === 1))) {
      if (jElement.toggleSelect(e && e.ctrlKey ? undefined : true).isselected) {
        jDiagram.selections[jElement.id] == null && ++jDiagram.selectionSize;
        jDiagram.selections[jElement.id] = jElement;

        // Deal with group which contains this element.
        if (jElement.isnode && jElement.options.group.length > 0) {
          for (var jid in jDiagram.nodes) {
            if (jDiagram.nodes[jid].options.group.length > 0 &&
              (jDiagram.nodes[jid].options.group[jDiagram.nodes[jid].options.group.length - 1] ===
                jElement.options.group[jElement.options.group.length - 1])) {
              jDiagram.selections[jid] == null && ++jDiagram.selectionSize;
              jDiagram.selections[jid] = jDiagram.nodes[jid];
              jDiagram.selections[jid].toggleSelect(true);
            }
          }
        }
      } else if (jDiagram.selections[jElement.id]) {
        delete jDiagram.selections[jElement.id];
        --jDiagram.selectionSize;
      }
    }

    // Mark the event, so when it bubbles up, the diagram event handler can know how to deal with.
    e && (e.selections = jDiagram.selections);

    // Selection Changed by User-Direct-Operation(has event)
    if (e && (oldSelections !== jDiagram.selections || oldSize !== jDiagram.selectionSize)) {
      this.triggerSelectionChanged(jDiagram, oldSelections);
    }
  };
  /**
   * Select All elements.
   * If given bounds, only select all elements within it.(marquee)
   */
  jBME.Diagram.selectPlugin.selectAll = function(jDiagram, bounds /* optional*/) {
    var oldSelections = jDiagram.selections;
    var oldSize = jDiagram.selectionSize;
    jDiagram.each(function(id, jElement) {
      if (bounds == null || Utils.containsBound(bounds, jElement.getBounds(jDiagram.$box))) {
        jBME.Diagram.selectPlugin.select(jDiagram, jElement);
      }
    });

    // Selection Changed
    if ((oldSelections !== jDiagram.selections || oldSize !== jDiagram.selectionSize)) {
      this.triggerSelectionChanged(jDiagram, oldSelections);
    }
  };
  jBME.Diagram.selectPlugin.triggerSelectionChanged = function(jDiagram, oldSelections) {
    var jEvent = $.Event(jBME.Diagram.EVENT.selectionchanged);
    jEvent.firstSelection = jDiagram.getFirstSelection();
    jEvent.selectionSize = jDiagram.selectionSize;
    jEvent.selections = jDiagram.selections;
    jEvent.oldSelections = oldSelections;
    jBME.Diagram.selectPlugin.onSelectionChanged(jDiagram, jEvent);
    jDiagram.$box.trigger(jEvent);
  };
  jBME.Diagram.selectPlugin.onSelectionChanged = function(jDiagram, jEvent) {
    for (var id in jEvent.oldSelections) {
      var jElement = jEvent.oldSelections[id];
      if (jElement.isnode) {
        if (jElement.toggleTextEdit(false, jDiagram)) {
          // Customize event : title is changed
          jDiagram.triggerElementOperation(jElement, 'titlechange');
        }
      }
    }
  };
  jBME.Editor.register(jBME.Diagram.selectPlugin);

  jBME.Diagram.keydownPlugin = jBME.AbstractPlugin.extend({
    name: 'keydownPlugin',
    eventHandlers: [{
      event: 'keydown',
      handler: 'onKeydown',
      targetselector: 'all',
    }],
    onKeydown: function(e, jElement, jDiagram) {
      if (jDiagram.$box.hasClass('dragging')) {
        return;
      }

      // To support IE 7,8,9. Ignore keydown on DOM element inside diagram.(Normal, on document or body)
      try {
        if (($.browser.msie || Utils.msie11()) && $(document.activeElement).parents('.uee-node-adapter')[0]) {
          return;
        }
      } catch (e) {
      }

      // To suport Firefox and Chrome,ignore keydown on DOM element inside diagram.(Normal, on document or body)
      if (!($.browser.msie || Utils.msie11()) && $.contains(jDiagram.$box[0], e.target)) {
        return;
      }

      // shortcuts
      if (e.ctrlKey && e.which === 65 /* CTRL+A*/) { // select all.
        e.preventDefault();
        jBME.Diagram.selectPlugin.selectAll(jDiagram);
      } else if (e.which === 46 /* DELETE*/) { // remove selections.
        if (!jDiagram.$box.hasClass('creating') || !jDiagram.dragElement) {
          jDiagram.removeSelections();
        }
      }
    },
  });
  jBME.Editor.register(jBME.Diagram.keydownPlugin);

  jBME.Diagram.copyPlugin = jBME.AbstractPlugin.extend({
    name: 'copyPlugin',
    eventHandlers: [{
      event: 'keydown',
      handler: 'copyPlugin',
      targetselector: 'all',
    }],
    copyPlugin: function(e, jElement, jDiagram) {
      if (jDiagram.$box.hasClass('dragging') || jDiagram.viewmode === 'view') {
        return;
      }

      // To support IE 7,8,9. Ignore keydown on DOM element inside diagram.(Normal, on document or body)
      try {
        if (($.browser.msie || Utils.msie11()) &&
          $(document.activeElement).parents('.uee-node-adapter')[0]) {
          return;
        }
      } catch (e) {
      }

      // To suport Firefox and Chrome,ignore keydown on DOM element inside diagram.(Normal, on document or body)
      if (!($.browser.msie || Utils.msie11()) && $.contains(jDiagram.$box[0], e.target)) {
        return;
      }

      if (e.ctrlKey && e.which === 67 /* CTRL+C*/) { // copy selections
        this.copy(jDiagram);
      } else if (e.ctrlKey && e.which === 86 /* CTRL+V*/) { // paste selections
        this.paste(jDiagram);
      } else if (e.ctrlKey && e.which === 88 /* CTRL+X*/) { // cut selections
        this.cut(jDiagram);
      }
    },
    /**
     * Ctrl + C and right click to copy selections.
     */
    copy: function(jDiagram) {
      // Clear the original info of clipboard before copy operation.
      for (var cbid in jDiagram.clipboard) {
        if (jDiagram.clipboard[cbid].ispath) {
          jDiagram.clipboard[cbid].prepareCopy();
        }
      }
      jDiagram.clipboard = {};

      // Copy info from original elements
      for (var cid in jDiagram.selections) {
        jDiagram.clipboard[cid] = jDiagram.selections[cid];
        if (jDiagram.selections[cid].ispath) {
          jDiagram.clipboard[cid].onCopy();
        }
      }
      jDiagram.pasteTimes = 0;
    },
    /**
     * Ctrl + X and right click to cut selections to clipboard.
     */
    cut: function(jDiagram) {
      this.copy(jDiagram);
      jDiagram.removeSelections('cut');
    },
    /**
     * Ctrl + V and right click to paste elements which are copied and cut.
     */
    paste: function(jDiagram) {
      if ($.isEmptyObject(jDiagram.clipboard)) {
        return;
      }

      // All paste nodes and paths.
      var pasteElements = jDiagram.clipboard;

      // Calculate the shifting according to the most left element.
      var highestElement = this.getReferNode(pasteElements);

      if (!highestElement) {
        return;
      }

      // Paste position.
      var offsetDifX = jDiagram.$box.innerWidth() / 2 - highestElement.getBounds().left;

      // Considering gourp id is unique.
      jDiagram.groupTime = (jDiagram.groupTime || 0) + 1;

      // Create new elements by clipboard.
      var createdElements = this.createElementsFromOld(jDiagram, pasteElements, offsetDifX);

      jDiagram.pasteTimes++;

      // Trigger paste event.
      jDiagram.triggerElementOperation(createdElements, 'paste', 'paste');

      // SyncJson for undo and redo.
      jDiagram.syncJson();
    },
    /**
     * Get the element which left value is min.
     * @param pasteElements
     * @returns highestElement
     */
    getReferNode: function(pasteElements) {
      var highestElement = null;
      var refLeft = 9999;

      for (var id in pasteElements) {
        if (pasteElements[id].isnode && pasteElements[id].getBounds().left < refLeft) {
          highestElement = pasteElements[id];
        }
      }

      return highestElement;
    },
    /**
     * Create elements by old options.
     * @param jDiagram
     * @param pasteElements
     * @param offsetDifX
     * @returns createdElements : all create element object.
     */
    createElementsFromOld: function(jDiagram, pasteElements, offsetDifX) {
      // For notifychange event, jElement could have all the elements at a time.
      var createdElements = {};
      var pastedNodes = {};
      var pastedPaths = {};

      // Nodes
      // DocumentFragment for collecting nodes
      jDiagram.ofragment = document.createDocumentFragment();
      for (var nid in pasteElements) { // Create all nodes firstly.
        if (pasteElements[nid].isnode) {
          var pasteNode = this.createNodeFromOld(jDiagram, nid, pasteElements, offsetDifX);
          createdElements[pasteNode.id] = pasteNode;
          pastedNodes[pasteNode.id] = pasteNode;
        }
      }

      // Append docummentFragment to actual DOM.
      jDiagram.$client[0].appendChild(jDiagram.ofragment);
      for (var nid in pastedNodes) {
        pastedNodes[nid].draw(pastedNodes[nid].context, pastedNodes[nid].options);
      }
      // Clear param
      jDiagram.ofragment = null;

      // Paths
      jDiagram.ofragment = document.createDocumentFragment();
      for (var pid in pasteElements) { // Create paths.
        if (pasteElements[pid].ispath) {
          var pastePath = this.createPathFromOld(jDiagram, pid, pasteElements);
          if (pastePath) {
            createdElements[pastePath.id] = pastePath;
            pastedPaths[pastePath.id] = pastePath;
          }
        }
      }

      jDiagram.$client[0].appendChild(jDiagram.ofragment);
      for (var pid in pastedPaths) {
        pastedPaths[pid].draw(pastedPaths[pid].context, pastedPaths[pid].options);
      }
      jDiagram.ofragment = null;

      return createdElements;
    },
    /**
     * Create node by old options.
     * @param jDiagram
     * @param pid : old node id.
     * @param pasteElements
     * @param offsetDifX
     * @returns pasteNode : new node created by old options.
     */
    createNodeFromOld: function(jDiagram, nid, pasteElements, offsetDifX) {
      // Clear information of copy selections
      var oldId = pasteElements[nid].id;
      var cloneOptions = pasteElements[nid].onPaste(offsetDifX, jDiagram.pasteTimes, jDiagram.groupTime, jDiagram.pasteOffset);
      var pasteNode = jDiagram.createNodeOuter(cloneOptions, 'paste');

      pasteNode.originalId = oldId;

      // Remember textnode on path which will be pasted future.
      if (cloneOptions.onpath && cloneOptions.onpath.pathid && cloneOptions.onpath.scale >= 0) {
        if (pasteElements[cloneOptions.onpath.pathid]) {
          pasteElements[cloneOptions.onpath.pathid].options.textnodesArray.push(pasteNode.id);
        } else {
          pasteNode.options.onpath = {
            pathid: '',
            scale: -1,
          };
        }
      }

      // Update the source and target of lines in clipboard.
      for (var lid in pasteElements) {
        if (pasteElements[lid].ispath && pasteElements[lid].options.pasteSource && pasteElements[lid].options.pasteTarget) {
          pasteElements[lid].options.pasteSource == oldId && (pasteElements[lid].options.newSource = pasteNode.id);
          pasteElements[lid].options.pasteTarget == oldId && (pasteElements[lid].options.newTarget = pasteNode.id);
        }
      }

      delete pasteNode.options.isPasted;

      return pasteNode;
    },
    /**
     * Create new paths by old options.
     * @param jDiagram
     * @param pid
     * @param pasteElements
     */
    createPathFromOld: function(jDiagram, pid, pasteElements) {
      if (!pasteElements[pasteElements[pid].options.pasteSource] || !pasteElements[pasteElements[pid].options.pasteTarget]) { // When the source or the target of line isn't in this clipboard, we don't paste this line.
        return;
      }

      var cloneOptions = pasteElements[pid].onPaste();
      var pastePath = jDiagram.createPathOuter(cloneOptions, 'paste');

      pastePath.originalId = pid;

      // Recover this new path's textnodes from textnodeArray.
      for (var i = 0; i < cloneOptions.textnodesArray.length; i++) {
        jDiagram.nodes[cloneOptions.textnodesArray[i]].options.onpath.pathid = pastePath.id;
        pastePath.textnodes[cloneOptions.textnodesArray[i]] = jDiagram.nodes[cloneOptions.textnodesArray[i]];
      }

      return pastePath;
    },
  });
  jBME.Editor.register(jBME.Diagram.copyPlugin);

  jBME.Diagram.undoPlugin = jBME.AbstractPlugin.extend({
    name: 'undoPlugin',
    eventHandlers: [{
      event: 'keydown',
      handler: 'undoPlugin',
      targetselector: 'all',
    }],
    undoPlugin: function(e, jElement, jDiagram) {
      if (jDiagram.$box.hasClass('dragging')) {
        return;
      }

      // To support IE 7,8,9. Ignore keydown on DOM element inside diagram.(Normal, on document or body)
      try {
        if (($.browser.msie || Utils.msie11()) && $(document.activeElement).parents('.uee-node-adapter')[0]) {
          return;
        }
      } catch (e) {
      }

      // To suport Firefox and Chrome,ignore keydown on DOM element inside diagram.(Normal, on document or body)
      if (!($.browser.msie || Utils.msie11()) && $.contains(jDiagram.$box[0], e.target)) {
        return;
      }

      if (e.ctrlKey && e.which == 90) { // undo
        jDiagram.undo();
      } else if (e.ctrlKey && e.which == 89) { // redo
        jDiagram.redo();
      }
    },
  });
  jBME.Editor.register(jBME.Diagram.undoPlugin);

  jBME.Diagram.contextmenuPlugin = jBME.AbstractPlugin.extend({
    name: 'contextmenu',
    eventHandlers: [{
      event: 'contextmenu',
      handler: 'onContextmenu',
      targetselector: 'all',
    }],
    onContextmenu: function(e, jElement, jDiagram) {
      // [contextmenu select]
      if (!jDiagram.menus) {
        return;
      }
      var optsMenu = jDiagram.menus.contextmenu;

      optsMenu.poper.offsetInfo.left = -2;
      optsMenu.poper.offsetInfo.right = 6;
      optsMenu.poper.offsetInfo.top = -3;
      optsMenu.poper.offsetInfo.bottom = 8;

      bindBeforeShow();
      bindBeforeHide();
      onrgtclk();

      function bindBeforeShow() {
        // Before popup menu, we deel with visiable and enable attribute of all items.
        optsMenu.jqPopMenu.unbind('beforeShow').bind('beforeShow', function(e) {
          var jqItems = optsMenu.getItems();
          var jqNode = optsMenu.jqPopFrom;
          var flag = false;
          var $scope = angular.element('#' + jDiagram.id).scope();
          jqItems.each(function() {
            var jqItem = $(this).children('a');
            var itemData = getMenuItemData(jqItem);
            handleMenuItemDisabled(jqNode, jqItem, itemData, $scope);
            flag = handleMenuItemDisplay(jqNode, jqItem, itemData, $scope) || flag;
          });
          if (!flag) {
            e.preventDefault();
          }
        });
      }

      function bindBeforeHide() {
        // Clear the style for contextmenu before we hide menu: anchor and selected
        optsMenu.jqPopMenu.unbind('beforeHide.menu').bind('beforeHide.menu', function() {
          if (jDiagram.onrgtclkElement) {
            for (var selectId in jDiagram.selections) {
              jDiagram.selections[selectId].toggleHover(false, null);
              jDiagram.selections[selectId].toggleSelect(false);
            }
            jDiagram.selections = {};
            jDiagram.selectionSize = 0;
            jDiagram.$client.find('.bc_poplocation').remove();
          }
        });
      }

      function getMenuItemData(jqItem) {
        var itemData = jqItem.data('itemData');
        if (!itemData) {
          var disabled = jqItem.attr('disabler');
          var display = jqItem.attr('displayer');
          try {
            if (undefined == disabled) {
              disabled = false;
            } else {
              jqItem.removeAttr('diabler');
            }
          } catch (e) {
          }
          
          try {
            if (undefined == display) {
              display = true;
            }
          } catch (e) {
          }
          
          itemData = {
            disabled: disabled,
            display: display,
          };
          jqItem.data('itemData', itemData);
        }
        return itemData;
      }

      function handleMenuItemDisabled(jqNode, jqItem, itemData, $scope) {
        // true/false/func
        var type = typeof itemData.disabled;
        var isDisabled = false;
        if (type == 'boolean') {
          isDisabled = itemData.disabled;
        } else {
          isDisabled = $scope.$eval(itemData.disabled);
        }

        if (isDisabled) {
          jqItem.attr('disabled', 'disabled');
          jqItem.parent().addClass('cbme_disabled');
          jqItem.children('img').addClass('cbme_disabled');
        } else {
          jqItem.removeAttr('disabled');
          jqItem.parent().removeClass('cbme_disabled');
          jqItem.children('img').removeClass('cbme_disabled');
        }
      }

      function handleMenuItemDisplay(jqNode, jqItem, itemData, $scope) {
        // true/false/func/other, other means that a,b,c without quotation marks
        // a,b,c is the type of node,means that the type of node is a or b or c can show in the menu.
        type = typeof itemData.display;
        var isDisplay = true;
        if (type == 'boolean') {
          isDisplay = itemData.display;
        } else {
          isDisplay = $scope.$eval(itemData.display);
        }

        if (isDisplay) {
          jqItem.parent().show();
          return true;
        } else {
          jqItem.parent().hide();
          return false;
        }
      }

      function onrgtclk() {
        // Right click repeatedly,menu will pop again before hidden,so that state machine will be muddledness.Therefore, we hide menu one time.
        // Can not complete it in component,because the data of popFrom is not accordance there.
        optsMenu.hideMenu();
        e.preventDefault();

        // Get rightclick element : jHover
        var jEventElement = null;
        var jHover = null;
        var eventParent = $(e.target).parents('.uee-diagram-ele')[0];

        eventParent && (jEventElement = jDiagram.nodes[eventParent.id] || jDiagram.paths[eventParent.id]);

        // Can right click blank space on diagram
        if (!jEventElement) { // User can get right click element from onrgtclkElement
          jDiagram.onrgtclkElement = jDiagram;
        } else if (jEventElement.isOnShape(e)) {
          jHover = jEventElement;
        } else { // When jEventElement is path, we should make sure whether this path is covered.
          jDiagram.each(function(id) {
            if (jEventElement.id != id && this.isOnShape(e)) {
              jHover = this;
            }
          });
        }

        if (!jHover) {
          jDiagram.onrgtclkElement = jDiagram;
        } else { // Draw anchor as style
          jHover.toggleSelect(true);
          jDiagram.selections[jHover.id] = jHover;
          jDiagram.selectionSize = jDiagram.selectionSize == 0 ? 1 : jDiagram.selectionSize;
          jDiagram.onrgtclkElement = jHover;
        }

        // set the right click element to angular diagram scope
        var $scope = angular.element('#' + jDiagram.id).scope();
        $scope.$Element = jDiagram.onrgtclkElement;

        // Show menu
        var $box = jQuery('<div class="bc_poplocation" style="height: 0px; width: 0px;"></div>');
        jDiagram.$client.append($box);
        $box.offset({
          left: e.pageX,
          top: e.pageY,
        });
        optsMenu.jqPopFrom = $box;
        optsMenu.showMenu();
      }
    },
    init: function(jDiagram) {
      this.editor.unregisterEvent('contextmenu', this);
    },
  });
  jBME.Editor.register(jBME.Diagram.contextmenuPlugin);

  jBME.Diagram.createNodePlugin = jBME.AbstractPlugin.extend({
    name: 'createNodePlugin',
    eventHandlers: [{
      event: 'mousedown',
      handler: 'createElement',
      targetselector: 'all',
    },
    {
      event: 'mouseup',
      handler: 'createElement',
      targetselector: 'all',
    },
    ],
    createElement: function(e, jElement, jDiagram) {
      if (e.which == 1 && jDiagram.request.queue[0] && jDiagram.request.queue[0].type == '1') {
        jDiagram.request.perform(jBME.Request.TYPES.CREATE_NODE, e);
      }
    },
  });
  jBME.Editor.register(jBME.Diagram.createNodePlugin);

  jBME.Diagram.createPathPlugin = jBME.AbstractPlugin.extend({
    name: 'createPathPlugin',
    eventHandlers: [{
      event: 'mousedown',
      handler: 'createElement',
      targetselector: 'all',
    },
    {
      event: 'mouseup',
      handler: 'createElement',
      targetselector: 'all',
    },
    ],
    createElement: function(e, jElement, jDiagram) {
      if (e.which == 1 && jDiagram.request.queue[0] && jDiagram.request.queue[0].type == '16') {
        jDiagram.request.perform(jBME.Request.TYPES.CREATE_PATH, e);
      }
    },
  });
  jBME.Editor.register(jBME.Diagram.createPathPlugin);

  jBME.Diagram.createPathByDraggingPlugin = jBME.AbstractPlugin.extend({
    name: 'createPathByDraggingPlugin',
    eventHandlers: [{
      event: 'mousedown',
      handler: 'createElement',
      targetselector: 'all',
    },
    {
      event: 'mouseup',
      handler: 'finishConnect',
      targetselector: 'all',
    },
    ],
    createElement: function(e, jElement, jDiagram) {
      if (e.which == 1 && jDiagram.request.queue[0] && jDiagram.request.queue[0].type == '16') {
        jDiagram.request.perform(jBME.Request.TYPES.CREATE_PATH, e);
      }
    },
    finishConnect: function(e, jElement, jDiagram) {
      if (e.which == 1 && jDiagram.request.queue[0] && jDiagram.request.queue[0].type == '16') {
        jDiagram.request.perform(jBME.Request.TYPES.CREATE_PATH, e);
      }
    },
  });

  jBME.Diagram.submitPlugin = jBME.AbstractPlugin.extend({
    name: 'submitPlugin',
    init: function(jDiagram) {
      jDiagram.$data.bind('beforesubmit', function(e) {
        (e.senddata || {})[this.getAttribute('dataname')] = jDiagram.toJSON();
      });
    },
  });
  jBME.Editor.register(jBME.Diagram.submitPlugin);

  jBME.Diagram.activeBendPlugin = jBME.AbstractPlugin.extend({
    name: 'activeBendPlugin',
    eventHandlers: [{
      event: 'mousedown',
      handler: 'createControlPoint',
      targetselector: '.uee-path',
    }],
    createControlPoint: function(e, jElement, jDiagram) {
      var jPath = jDiagram.jHoverElement;
      if (!jPath || !jPath.ispath || jPath.jType.name == 'bezier' || jPath.jType.name == 'quadratic' ||
        jPath.isOrthogonalEdge || jDiagram.request.has() || !jDiagram.authManager(jPath, 'draggable') ||
        $.nodeName(e.target, 'pre') || $.nodeName(e.target, 'textarea')) {
        return;
      }

      if (!jPath.hitTestHoverBend(e)) {
        this.createDraggingBend(jDiagram, e);
      }
    },
    /**
     * Dragging out a new bend.
     */
    createDraggingBend: function(jDiagram, e) {
      var dragging = 'mousemove.createbend';
      var dragend = 'mouseup.crateend';
      var jPath = jDiagram.jHoverElement;
      var index = -1;
      var created = false;

      var eventPosition = jDiagram.getPositionWithScroll(e);
      var pos = jPath.getClosestPointFromPath(eventPosition.left, eventPosition.top);
      var referPath = null;
      var elbowPoints = jPath.options.elbowPoints;
      var that = this;
      var drawBGBends = true;

      jDiagram.$box.bind(dragging, function(emv) {
        if (jPath && jPath.ispath && !created &&
          (e.pageX != emv.pageX || e.pageY != emv.pageY)) { // Mouse down and mouse move to create a new bend.
          index = pos[3];
          created = true;

          if (index != -1) {
            if (!jDiagram.$box.hasClass('dragging')) {
              jDiagram.$box.addClass('dragging');
            }

            if (elbowPoints.length == 0) {
              if (jPath.options.controls.length == 0) { // Line
                elbowPoints.push({
                  x: pos[0],
                  y: pos[1],
                });
              } else { // Elbow line..
                var pts = jPath.getAbsoluteRouteList();
                for (var i = 2; i < pts.length - 2; i += 2) {
                  elbowPoints.push({
                    x: pts[i],
                    y: pts[i + 1],
                  });
                }
                elbowPoints.splice(index, 0, {
                  x: pos[0],
                  y: pos[1],
                });

                jPath.options.controls = [];
              }
            } else {
              elbowPoints.splice(index, 0, {
                x: pos[0],
                y: pos[1],
              });
            }

            // Draw a backgourd edge for reference
            referPath = that.createReferenceEdge(jPath, jDiagram);

            // Draw a red circle bend for reference
            if (drawBGBends && referPath) {
              referPath.drawReferBend(referPath.context, pos);
              drawBGBends = false;
            }
          }
        }
        if (elbowPoints.length > 0) {
          elbowPoints[index] = {
            x: pos[0] + emv.pageX - e.pageX,
            y: pos[1] + emv.pageY - e.pageY,
          };
        }

        jPath.updateCanvasBounds();
        jPath.draw();
      }).bind(dragend, function(eup) {
        // If dragging end position is cloest to original dragging position,
        // we will delete this bend.
        var dfx = eup.pageX - e.pageX;
        var dfy = eup.pageY - e.pageY;
        var df = Math.sqrt(dfx * dfx + dfy * dfy);

        if (index != -1 && df < 6) {
          elbowPoints.splice(index, 1);
          created = false;
        } else {
          jDiagram.triggerBendCreate(jPath, index, elbowPoints);
        }

        referPath && jDiagram.remove(referPath, '', true);

        jDiagram.$box.unbind(dragend).unbind(dragging);
        jDiagram.$box.removeClass('dragging');

        jDiagram.triggerElementOperation(jPath, 'pathchange', 'drag');
      });
    },
    /**
     * Create a background line for reference.
     */
    createReferenceEdge: function(jPath, jDiagram) {
      var options = $.extend(true, {}, jPath.options);
      options.id = 'pathBG';
      options.figures[0].context.strokeStyle = 'rgba(0,0,0,0.4)';
      delete options.textareas;
      return jDiagram.createPathOuter(options, 'inner');
    },
  });
  jBME.Editor.register(jBME.Diagram.activeBendPlugin);

  jBME.Diagram.dragToCreateNode = jBME.AbstractPlugin.extend({
    name: 'dragToCreateNode',
    eventHandlers: [{
      event: 'mouseenter',
      handler: 'createNode',
      targetselector: 'all',
    },
    {
      event: 'mousedown',
      handler: 'fixedCreateNode',
      targetselector: 'document',
    },
    {
      event: 'mouseup',
      handler: 'unregister',
      targetselector: 'document',
    },
    ],
    createNode: function(e, jElement, jDiagram) {
      if (jDiagram.registeredOptions && !jDiagram.registeredNode && jDiagram.draggingCreate) {
        jDiagram.registeredNode = jDiagram.createNodeOuter(jDiagram.registeredOptions, 'inner', e);
        jBME.Diagram.selectPlugin.select(jDiagram, null);
        jBME.Diagram.selectPlugin.select(jDiagram, jDiagram.registeredNode);
        jDiagram.resizedir = 0;

        if (!jDiagram.registeredNode.isswimlane ||
          (jDiagram.registeredNode.isswimlane && jDiagram.registeredNode.lanes.length == 1)) {
          jBME.Diagram.selectdragPlugin.prototype.dragSelections(jDiagram, jDiagram.registeredNode, e);
        }
      }
    },
    /**
     * This method is for IE 7/8 bug : Browser don't response mouseenter event when dragging DOM.
     * @param e
     * @param jElement
     * @param jDiagram
     */
    fixedCreateNode: function(e, jElement, jDiagram) {
      if ($.browser.msie && $.browser.version <= 8) {
        var diagramOffest = jDiagram.$box.offset();
        var diagramBounds = {
          left: diagramOffest.left,
          top: diagramOffest.top,
          width: jDiagram.$box.width(),
          height: jDiagram.$box.height(),
        };
        var draggingCreate = 'mousemove.dragToCreateNode';
        var draggingCreateEnd = 'mouseup.dragToCreateNode';
        var that = this;

        $(document).bind(draggingCreate, function(emv) {
          if (emv.pageX > diagramBounds.left && emv.pageX < diagramBounds.left + diagramBounds.width &&
            emv.pageY > diagramBounds.top && emv.pageY < diagramBounds.top + diagramBounds.height) {
            that.createNode(emv, jElement, jDiagram);
          }
        }).bind(draggingCreateEnd, function(eup) {
          $(document).unbind(draggingCreateEnd).unbind(draggingCreate);
        });
      }
    },
    /**
     * Delete options when click outside of diagram.
     */
    unregister: function(e, jElement, jDiagram) {
      if ($(e.target).closest('.uee-diagram').length == 0) {
        jDiagram.unregisterNode();
      }
    },
  });
  /**
   * When drag end, whether this node can be created.
   * @param jDiagram : the instance of diagram
   * @param eup : the mouse up event
   */
  jBME.Diagram.dragToCreateNode.isCreate = function(jDiagram, e, eup) {
    var offset = jDiagram.$box.offset();
    var boxWidth = jDiagram.$box.width();
    var boxHeight = jDiagram.$box.height();
    var pageX = eup.pageX;
    var pageY = eup.pageY;

    if ((pageX < offset.left || pageX > offset.left + boxWidth ||
        pageY < offset.top || pageY > offset.top + boxHeight) ||
      jDiagram.triggerBeforeCreateNode(jDiagram.registeredOptions, eup, 'outer')) {
      jDiagram.remove(jDiagram.registeredNode);
    } else if (e && (e.pageX != pageX || e.pageY != pageY)) {
      jDiagram.triggerElementOperation(jDiagram.registeredNode, 'nodecreate', null, eup);
      jDiagram.syncJson();
    }
    jDiagram.unregisterNode();
  };
  jBME.Editor.register(jBME.Diagram.dragToCreateNode);

  jBME.Diagram.spinnerControl = jBME.AbstractPlugin.extend({
    name: 'spinnerControl',
    eventHandlers: [{
      event: 'keydown',
      handler: 'spinnerControl',
      targetselector: 'all',
    }],
    /**
     * Spinner control plugin.
     * @param e
     * @param jElement
     * @param jDiagram
     */
    spinnerControl: function(e, jElement, jDiagram) {
      if ($.nodeName(e.target, 'textarea')) { // Can't move when inputing.
        return;
      }

      var direction = this.getDirection(e.which, jDiagram.spinStep);
      if (direction) {
        var selections = jDiagram.selections;

        // Validate selected nodes.
        var result = this.validate(selections, direction, jDiagram);

        // Update all bounds.
        if (result) {
          this.updateSelections(selections, direction);

          jDiagram.syncJson();
        }
      }
    },
    /**
     * Update the bounds of selected nodes by key top, bottom, left, right.
     * @param selections : the selected elements
     * @param direction : spin direction
     */
    updateSelections: function(selections, direction) {
      for (var nid in selections) {
        if (selections[nid].isnode) {
          for (var key in direction) {
            var bounds = selections[nid].getBounds();
            var value = bounds[key] + direction[key];

            key == 'left' ? selections[nid].updateBounds({
              left: value,
            }) :
              selections[nid].updateBounds({
                top: value,
              });

            if (selections[nid].children && !selections[selections[nid].children.id]) {
              this.updateChildrenBounds(selections[nid].children, key, direction[key], selections);
            }
          }
        }
      }
    },
    /**
     * Update children bounds together
     * @param children : children of current node
     * @param key : direction left/top
     * @param step : spinner step
     * @param selections : all selected nodes
     */
    updateChildrenBounds: function(children, key, step, selections) {
      for (var cid in children) {
        if (!selections[cid]) {
          var childBounds = children[cid].getBounds();
          var value = childBounds[key] + step;

          key == 'left' ? children[cid].updateBounds({
            left: value,
          }) :
            children[cid].updateBounds({
              top: value,
            });
        }
      }
    },
    /**
     * Get the direction and changed step of spinner
     * @param eventKeyCode : keydown event code
     *            37 left 38 up 39 right 40 down
     * @param spinStep : spinner step
     * @returns direction : record the value to update bounds of nodes.
     */
    getDirection: function(eventKeyCode, spinStep) {
      var direction = null;

      switch (eventKeyCode) {
        case 37:
          direction = {
            left: -spinStep,
          };
          break;
        case 38:
          direction = {
            top: -spinStep,
          };
          break;
        case 39:
          direction = {
            left: spinStep,
          };
          break;
        case 40:
          direction = {
            top: spinStep,
          };
          break;
      }

      return direction;
    },
    /**
     * Validate whether selecited nodes go beyond its parent container or containers puts away its children.
     * @param selections : all selected nodes
     * @param direction : spinner direction
     * @param jDiagram : the instance of diagram
     * @returns {Boolean} result : whether can be updated.
     */
    validate: function(selections, direction, jDiagram) {
      var result = true;

      for (var nid in selections) {
        for (var key in direction) {
          var bounds = selections[nid].getBounds();
          var value = bounds[key] + direction[key];

          result = this.validateNode(selections[nid], key, value, jDiagram);
        }
      }

      return result;
    },
    /**
     * Validate evey nodes
     * @param node : current node
     * @param key : left/top
     * @param value : new value of left/top
     * @param jDiagram : the instance of diagram
     * @returns {Boolean} result : whether can be updated.
     */
    validateNode: function(node, key, value, jDiagram) {
      var intervalKey = key == 'left' ? 'width' : 'height';
      var bounds = node.getBounds();

      // Draggable
      if (node && !jDiagram.authManager(node, 'draggable')) {
        return false;
      }

      // Minmum of node left or top is 0.
      if (value < 0) {
        return false;
      }

      // Whether this node Beyond the scope of the parent node.
      if (node.parent) {
        var parentBounds = node.parent.getBounds();

        if (value <= parentBounds[key] ||
          (value + bounds[intervalKey] >= parentBounds[key] + parentBounds[intervalKey])) {
          return false;
        }
      }

      return true;
    },
  });
  jBME.Editor.register(jBME.Diagram.spinnerControl);

  jBME.Diagram.DOMCache = jBME.AbstractPlugin.extend({
    name: 'DOMCache',
    eventHandlers: [{
      event: 'scroll',
      handler: 'updateDOM',
      sourceselector: '.uee-diagram-client',
    },
    {
      event: 'resize',
      handler: 'updateWindow',
      sourceselector: window,
    },
    {
      event: 'click',
      handler: 'updateWindow',
      sourceselector: '.tabpanel-items-name-li',
    }, // to fix the problem under uee:panel
    ],
    lastScrollTop: 0,

    lasttScrollLeft: 0,
    /**
     * Show some DOM and hidden some by scroll position.
     * @param e
     * @param jElement
     * @param jDiagram
     */
    updateDOM: function(e, jElement, jDiagram) {
      var scrollTop = jDiagram.$client.scrollTop();
      var scrollLeft = jDiagram.$client.scrollLeft();
      var stepX = jDiagram.showScopeX / 2;
      var stepY = jDiagram.showScopeY / 2;

      this.initScroll(scrollTop, scrollLeft);

      // Response scope is showScope / 2
      if (Math.abs(scrollTop - this.lastScrollTop) > stepY ||
        Math.abs(scrollLeft - this.lastScrollLeft) > stepX) {
        var clientWidth = jDiagram.$client.width();
        var clientHeight = jDiagram.$client.height();

        this.updateShowElement(jDiagram, jDiagram.nodes, scrollTop, scrollLeft, clientWidth, clientHeight);

        this.updateShowElement(jDiagram, jDiagram.paths, scrollTop, scrollLeft, clientWidth, clientHeight);

        // Keep the last nearest scroll position.
        this.lastScrollTop = Math.round(scrollTop / stepY) * stepY;
        this.lastScrollLeft = Math.round(scrollLeft / stepX) * stepX;
      }
    },
    /**
     * Init scroll position when mousedown to drag scroll first time.
     */
    initScroll: function(scrollTop, scrollLeft) {
      if (!this.lastScrollTop) {
        this.lastScrollTop = scrollTop;
      }

      if (!this.lastScrollLeft) {
        this.lastScrollLeft = scrollLeft;
      }
    },
    /**
     * Calculation for nodes and paths whether they should be showed.
     */
    updateShowElement: function(jDiagram, elements, scrollTop, scrollLeft, clientWidth, clientHeight) {
      // Show node's dom which are in showing scope and hidden the others.
      for (var eid in elements) {
        var element = elements[eid];
        var bounds = element.getBounds();
        var left = bounds.left;
        var top = bounds.top;
        var right = bounds.left + bounds.width;
        var bottom = bounds.top + bounds.height;

        // Showing scope.
        if (!((top > scrollTop + clientHeight + jDiagram.showScopeY) ||
          (bottom < scrollTop - jDiagram.showScopeY) ||
          (left > scrollLeft + clientWidth + jDiagram.showScopeX) ||
          (right < scrollLeft - jDiagram.showScopeX))) {
          // In showing scope but this element was not showed, show it.
          if (!element.isShow) {
            jDiagram.$client.append(element.$box);
            element.draw();
            element.isShow = true;
            // 新展示的元素如果是是可信原子节点，则添加可信标志
            if (element && element.options && element.options.data && (element.options.data.source === 'hwWithSign')) {
              let dom = document.getElementById(element.id);
              // 判断元素中是否已包含认证图标，增加后childElementCount为4，防止重复添加图标
              if (dom.classList.contains('roundrect') && dom.childElementCount < 4) {
                let newLi = document.createElement('div');
                newLi.innerHTML = '<div style=\'position: absolute;top: -12px;left:-16px\'><img src=\'/dvihealingwebsite/images/healing/trustworthy.png\' /></div>';
                dom.append(newLi);
              }
            }

            // Remove this DOM in the cache.
            $(jDiagram.DOMCache).children('#' + eid).remove();
          }
        } else {
          // Not in showing scope but this element was showed, hidden it.
          if (element.isShow) {
            // Store this DOM in the cache.
            jDiagram.DOMCache.appendChild(element.$box[0]);

            jDiagram.$client.children('#' + eid).remove();
            element.isShow = false;
          }
        }
      }
    },
    updateWindow: function(e, jElement, jDiagram) {
      var scrollTop = jDiagram.$client.scrollTop();
      var scrollLeft = jDiagram.$client.scrollLeft();
      var clientWidth = jDiagram.$client.width();
      var clientHeight = jDiagram.$client.height();

      this.updateShowElement(jDiagram, jDiagram.nodes, scrollTop, scrollLeft, clientWidth, clientHeight);

      this.updateShowElement(jDiagram, jDiagram.paths, scrollTop, scrollLeft, clientWidth, clientHeight);
    },
  });
  jBME.Editor.register(jBME.Diagram.DOMCache);
})
(jQuery, window.jBME = (window.jBME || {}));

/**
 * Define Class jBME.DiagramMenuPoper
 */
(function() {
  jBME.DiagramMenuPoper = function(jqPopFrom, jqPop) {
    this.jqPopFrom = jqPopFrom;
    this.jqPop = jqPop;
    this.leftFirst = null;
    this.jqMask = null;
    // 这里最终体现的是实际弹出方向
    this.resultBottom = true;
    this.resultRight = true;

    // jqPop的弹出偏移信息
    this.offsetInfo = {
      left: 0,
      top: 0,
      right: 0,
      bottom: 0,
    },

    this.menuContainer = this.jqPop.closest('.uee-menu-container');
    this.children = this.menuContainer.find('div.uee-menu-container'); // 存取子菜单
    this.jqPopParent = this.menuContainer.parent();

    this.initMenu();
    return this;
  };

  jBME.DiagramMenuPoper.prototype = {
    initMenu: function() {
      // 为了兼容原来的menuitem上的属性separatorafter，这里需要将这段提取出来
      this.jqPop.find('li> .uee-split').each(function() {
        var hrObj = $(this);
        var hrParent = hrObj.parent();
        if (hrObj.siblings('.uee-link').length != 0) {
          var hrContainer = $('<li style=\'overflow:hidden;position:relative;\' class=\'split\'></li>');
          hrObj.appendTo(hrContainer);
          hrContainer.insertAfter(hrParent);
        } else {
          hrParent.addClass('split');
        }
      });

      this.jqPop.find('li> .uee-link').each(function() {
        var jq = $(this);
        var jqParent = jq.parent();
        jqParent.addClass('a_link');
      });

      // 为每个有子菜单的菜单项添加图标
      for (var i = 0; i < this.children.length; i++) {
        var children = $(this.children[i]);
        var li = children.parent('li');
        li.addClass('has_sub');
      }

      // 解决ie下点击后hover的状态还存在（原来是通过样式hover来实现的）
      this.menuContainer.find('li.a_link').each(function() {
        $(this).unbind('mouseenter').bind('mouseenter', function() {
          $(this).addClass('hover');
        }).unbind('mouseleave')
          .bind('mouseleave', function() {
            $(this).removeClass('hover');
          });
      });
    },
    initMenuSize: function() {
      // 需要先清一下对应的宽度，使这时的宽度根据内容或者用户自定义的宽度进行适应，如果不清，就会与第一次出现的宽度一致
      this.jqPop.css('width', '');
      this.menuContainer.css('width', '');

      var containerWidth = this.jqPop.width();
      // bc_center_center元素设置了min-width，在IE7下，如果menu组件本身没设置宽度,this.jqPop取出的宽度不正确，比最小宽度小
      if ($.browser.msie && $.browser.version == '7.0') {
        var jqparentWidth = this.jqPop.parent().width();
        if (containerWidth < jqparentWidth) {
          containerWidth = jqparentWidth;
        }
      }

      this.menuContainer.hide();
      this.menuContainer.width(containerWidth + 11);

      // ie7下面，li的宽度跟兄弟姐妹没有保持一致,需要主动对ul元素设置一下宽度
      this.jqPop.css('width', containerWidth);
    },
    showMenu: function(jqPopFrom) {
      // 将menu放到body下面
      // We use W3C API instead of jQuery.fn.appendTo to prevent <script>
      // re-eval and remove.
      document.body.appendChild(this.menuContainer[0]);

      // 确定第一级菜单的宽度,menu处于不可见状态，在show的时候重新计算menu的宽度
      this.menuContainer.show();
      this.initMenuSize();

      if (undefined != jqPopFrom) {
        this.jqPopFrom = jqPopFrom;
      }

      // 添加mask
      if (this.jqMask == undefined) {
        this.jqMask = $('<div class="bf_mask" style="position:fixed;z-index:199; width:100%;height:100%;left:0px;top:0px;"></div>');
      }
      this.jqMask.appendTo(document.body);
      this.jqMask.show();

      var winInfo = this.getWinInfo();

      this.initMenuPosition(winInfo);

      this.initSubMenuPosition(winInfo);

      this.showSubMenu();
    },

    hideMenu: function() {
      // 将hover样式去掉
      this.menuContainer.find('li.hover').each(function() {
        $(this).removeClass('hover');
      });
      this.menuContainer.hide();
      this.jqPopParent[0].appendChild(this.menuContainer[0]);

      this.jqMask.remove();
      this.jqMask = undefined;
    },

    initMenuPosition: function(winInfo) {
      this.jqPop.css({
        position: 'static',
        display: 'block',
        overflow: 'visible',
      });

      var popFromInfo = this.getPopFromInfo(this.jqPopFrom);
      var poperInfo = this.getPoperInfo(this.menuContainer);

      // 确定垂直方向的弹出
      var modifedTop = 0;
      // 向下弹出
      this.resultBottom = true;
      poperInfo.top = popFromInfo.top + popFromInfo.height + this.offsetInfo.top - modifedTop;
      if (poperInfo.top - winInfo.scrollTop + modifedTop + poperInfo.height - this.offsetInfo.bottom > winInfo.offsetHeight) {
        // 下方空间不足，向上弹出
        this.resultBottom = false;
        poperInfo.top = popFromInfo.top - poperInfo.height + this.offsetInfo.bottom - modifedTop;
      }
      // 垂直方向仍然无法显示，则将top置为当前窗口滚动top位置
      if ((poperInfo.top - winInfo.scrollTop + modifedTop - this.offsetInfo.top < 0) ||
        (poperInfo.top - winInfo.scrollTop + modifedTop + poperInfo.height -
          this.offsetInfo.bottom > winInfo.offsetHeight)) {
        poperInfo.top = winInfo.scrollTop - modifedTop;
      }

      // 确定水平方向的弹出
      var modifedLeft = 0;
      // 向右弹出
      this.resultRight = true;
      poperInfo.left = popFromInfo.left + this.offsetInfo.left - modifedLeft;
      if ((this.leftFirst == false) ||
        (poperInfo.left - winInfo.scrollLeft - modifedLeft + poperInfo.width -
          this.offsetInfo.right > winInfo.offsetWidth)) {
        // 空间不足，则向左弹出
        this.resultRight = false;
        poperInfo.left = popFromInfo.left + popFromInfo.width - poperInfo.width + this.offsetInfo.right -
          modifedLeft;
      }
      // 水平方向仍然无法显示，则将left置为当前窗口滚动left位置
      if (poperInfo.left + modifedLeft < winInfo.scrollLeft + this.offsetInfo.left) {
        poperInfo.left = winInfo.scrollLeft - modifedLeft;
      }

      // 重新确定弹出位置
      this.menuContainer.css({
        left: (poperInfo.left + 'px'),
        top: (poperInfo.top + 'px'),
        display: 'block',
      });
    },

    initSubMenuPosition: function(winInfo) {
      var children = this.children; // 存取子菜单
      var childNum = children.size();

      for (var i = 0; i < childNum; i++) {
        var menuObj = $(children[i]);
        menuObj.show();
        var menuPoperFrom = menuObj.parent().closest('.uee-menu-container');
        var popFromInfo = this.getPopFromInfo(menuPoperFrom);
        var menuContentWidth = null;

        // 解决ie7下面宽度无法根据内容自适应，原因是受上层父元素宽度的影响
        if ($.browser.msie && $.browser.version == '7.0') {
          var menuParentElem = menuObj.parent()[0];
          document.body.appendChild(menuObj[0]);

          var menuContainer = $(menuObj.find('.uee-menu-center-center')[0]);
          menuContentWidth = menuContainer.outerWidth();

          // ie7下面，li的宽度跟兄弟姐妹没有保持一致,需要主动对ul元素设置一下宽度
          menuContainer.css('width', menuContentWidth);
          menuParentElem.appendChild(menuObj[0]);
        } else {
          menuContentWidth = $(menuObj.find('.uee-menu-center-center')[0]).outerWidth();
        }

        var poperInfo = {
          left: 0,
          top: 0,
          width: menuContentWidth + 11,
          height: 0,
        };
        menuObj.width(poperInfo.width);

        // 确定水平方向
        var modifedLeft = 12;
        poperInfo.left = popFromInfo.width - modifedLeft;
        if ((this.leftFirst == false) ||
          (poperInfo.left + popFromInfo.left - winInfo.scrollLeft + poperInfo.width > winInfo.offsetWidth)) {
          // 空间不足，则向左弹出
          poperInfo.left = -poperInfo.width + 4;
        }
        // 水平方向仍然无法显示，则将left置为当前窗口滚动left位置
        if (poperInfo.left + popFromInfo.left < winInfo.scrollLeft) {
          poperInfo.left = winInfo.scrollLeft;
        }

        // 重新确定弹出位置
        menuObj.css('left', poperInfo.left + 'px');
        menuObj.css('top', '-4px');
      }
      // 需要将显示的元素给hidden掉
      for (var i = (childNum - 1); i >= 0; i--) {
        $(this.children[i]).hide();
      }
    },

    showSubMenu: function() {
      var t = 15;
      var parentList = []; // 存取有子菜单的项目
      var children = this.children; // 存取子菜单
      // for chrome if you use "li[0].style.overflow = "hidden" to hide a
      // element,it will make same problem,so we use jquery method
      var ischrome = $.browser.webkit;

      init();

      function init() {
        for (var i = 0; i < children.size(); i++) {
          var li = $(children[i]).parent('li');
          parentList[i] = li;
          $(parentList[i]).data('subNum', i);

          // if disabled,not show
          if (!$(parentList[i]).hasClass('cbme_disabled')) {
            parentList[i].mouseenter(function(e) {
              children[$(this).data('subNum')].style.display = 'block';
            }).mouseleave(function(e) {
              children[$(this).data('subNum')].style.display = 'none';
            });
          }
        }
      }
    },

    // 得到popFrom的位置信息
    getPopFromInfo: function(popFrom) {
      var popFromInfo = {
        left: 0,
        top: 0,
        width: 0,
        height: 0,
      };
      var needHidden = false;
      if (popFrom.is(':hidden')) {
        needHidden = true;
        popFrom.show();
      }
      popFromInfo.left = popFrom.offset().left;
      popFromInfo.top = popFrom.offset().top;
      popFromInfo.width = popFrom.outerWidth();
      popFromInfo.height = popFrom.outerHeight();
      if (needHidden) {
        popFrom.hide();
      }
      return popFromInfo;
    },

    // 计算弹出窗口的大小
    getPoperInfo: function(menuContainer) {
      var poperInfo = {
        left: 0,
        top: 0,
        width: 0,
        height: 0,
      };
      poperInfo.width = menuContainer.outerWidth();
      poperInfo.height = menuContainer.outerHeight();
      return poperInfo;
    },

    // 计算当前窗口的大小
    getWinInfo: function() {
      var ele = document.documentElement;
      var body = document.body;

      // chrome识别documentElement的scrollTop/scrollLeft有问题，使用body则没有问题
      // 这两个值浏览器总是只返回一个有效的，所以直接相加，避免判断浏览器
      var winInfo = {
        scrollTop: ele.scrollTop + body.scrollTop,
        scrollHeight: ele.scrollHeight,
        scrollLeft: ele.scrollLeft + body.scrollLeft,
        scrollWidth: ele.scrollWidth,
        offsetHeight: ele.offsetHeight,
        offsetWidth: ele.offsetWidth,
      };

      return winInfo;
    },

  };
})();

/**
 * Define Class jBME.menu
 */
(function() {
  /**
   * Define Class and its constructor
   *
   * @param cfg:
   *      cfg.id cfg.bindid cfg.event 当jqBind上触发此事件时，弹出菜单
   */
  jBME.diagrammultimenu = function(cfg) {
    this.attach(cfg);
    return this;
  };

  /**
   * Define Class members
   */
  jBME.diagrammultimenu.prototype = {
    /**
     * DOM element for menu.
     */
    // menu对象
    jqPopMenu: undefined,
    // menu监听哪个对象的事件
    jqBind: undefined,
    // menu在哪个对象旁边弹出，一般来说等于jqbind，在树上使用时，会是各个节点
    jqPopFrom: undefined,
    // 鼠标位置，当tree的右键菜单从鼠标位置弹出时使用，为了保持兼容不修改jqPopFrom才增加此属性
    jqPopPosition: undefined,

    cfg: undefined,

    poper: undefined,

    /**
     * @param bindid/id
     */
    attach: function(cfg) {
      var options = {
        css: {
          'margin-left': '8px',
          '*margin-left': '11px',
        },
      };
      this.cfg = cfg;
      this.jqPopMenu = $(document.getElementById(cfg.id));
      this.jqBind = $(document.getElementById(cfg.bindid));
      this.jqPopFrom = this.jqBind;
      // toolbar需要根据dom找到menu对象
      this.jqPopMenu.data('menu', this);
      this.initImg();

      this.poper = new jBME.DiagramMenuPoper(this.jqPopFrom, this.jqPopMenu);

      this.bindEvent(options);
    },
    initImg: function() {
      // 没有menu的节点，创建出空的img来使得文字可以对齐
      this.jqPopMenu.find('li> .uee-link').each(
        function() {
          var jqA = $(this);
          var jqChildren = jqA.children();
          if ($.isEmptyObject(jqChildren) || !($(jqChildren[0]).is('img'))) {
            jqA.prepend('<img align=\'absmiddle\' src=\'/dvihealingwebsite/images/healing/diagram/empty.gif\'/>');
          }
        });
    },
    // 获取所有的tems，不包括menuItem、link以外的东东
    getItems: function() {
      return this.jqPopMenu.find('li>.uee-link').parent();
    },
    bindEvent: function(options) {
      var menuObj = this;
      if (this.cfg.event != '') {
        this.jqBind.bind(this.cfg.event, function() {
          menuObj.showMenu(options);

          $(window).bind('resize.menuitem', function() {
            menuObj.hideMenu();
          });
        });
      }
      this.getItems().each(function() {
        var jqItem = $(this);

        jqItem.unbind('click.menuitem').bind('click.menuitem', function(event) {
          event.stopPropagation();
          var item = jqItem.children()[0];
          if (jBME.util.isDisabled(item)) {
            event.preventDefault();
            return;
          }
          var jqEvent = new jQuery.Event('beforeHide');
          jqEvent.menuObj = menuObj;
          menuObj.jqPopMenu.trigger(jqEvent);
          menuObj.poper.hideMenu();

          var jqEvent = new jQuery.Event('diagramMenuItemClick');
          jqEvent.jqItem = $(item);
          jqEvent.menuObj = menuObj;
          menuObj.jqPopMenu.trigger(jqEvent);
          $(document).unbind('mousedown.' + menuObj.cfg.id).unbind('click.' + menuObj.cfg.id);
          $(window).unbind('resize.menuitem');
        });
      });
    },
    showMenuFrom: function(jqPopFrom) {
      this.jqPopFrom = jqPopFrom;
      this.showMenu();
    },
    showMenu: function(options) {
      this.hideMenu();
      var menuObj = this;
      if (jBME.util.isDisabled(menuObj.jqBind)) {
        return;
      }

      function trigger(eventName) {
        var jqEvent = new jQuery.Event(eventName);
        jqEvent.menuObj = menuObj;
        // diff start from BME3.0
        // reason : the following sentence will cause the plugin can't recieve the event
        // diff end
        menuObj.jqPopMenu.trigger(jqEvent);
        if (jqEvent.isDefaultPrevented())
        {return true;}
        return false;
      }

      // 触发一个beforeshow事件，让外界有机会对菜单进行调整
      if (trigger('beforeShow')) {
        return;
      }

      this.poper.showMenu(this.jqPopPosition || this.jqPopFrom);

      // 触发一个aftershow事件，让外界有机会做菜单外观外挂
      trigger('afterShow');

      var hideEvent = 'click.' + this.cfg.id;
      var isIE = ('ActiveXObject' in window);
      if ((!$.browser.mozilla) || isIE) {
        hideEvent = hideEvent + ' mousedown.' + this.cfg.id;
      }

      $(document).unbind(hideEvent).bind(hideEvent, function(event) {
        var jqTarget = $(event.target);
        if ( // !Click element is the child of menu;
          !(menuObj.cfg.id && jqTarget.closest('#' + menuObj.cfg.id).length > 0) &&
          !(menuObj.cfg.bindid && jqTarget.closest('#' + menuObj.cfg.bindid).length > 0)
          // !Click element is right click element;
          &&
          !(event.target == menuObj.jqPopFrom.get(0))
          // !Right click element contains click element.
          &&
          !($.contains(menuObj.jqPopFrom.get(0), event.target))) {
          menuObj.hideMenu();
        }
      });
    },
    hideMenu: function() {
      if (this.jqPopMenu.is(':hidden')) {
        return;
      }
      // 触发一个beforehide事件
      var jqEvent = new jQuery.Event('beforeHide');
      jqEvent.menuObj = this;
      this.jqPopMenu.trigger(jqEvent);

      this.poper.hideMenu();

      $(document).unbind('mousedown.' + this.cfg.id).unbind('click.' + this.cfg.id);
      $(window).unbind('resize.menuitem');
    },
  }; // end prototype.
})();

/* !
 * BME UI Component - Diagram customized Node.
 * ----------------------------------------------------------------------------
 * @created by Adrian 2012-09-12 Huawei Technologies Co., Ltd
 */
if (jBME.Node) {
  new jBME.Node.Type('tasknode', {
    figure: function(context, figure) {
      // main node width,height and margin
      var margin = parseInt(figure.context.lineWidth);
      var mnNodeWidth = this.width;
      var mnNodeHeight = this.height * 0.65;

      // state image width,height,left,top,margin
      var imgMargin = Math.min(mnNodeWidth, mnNodeHeight) * 1 / 4;
      var imgWidth = Math.min(mnNodeWidth, mnNodeHeight) * 0.5;
      var imgHeight = Math.min(mnNodeWidth, mnNodeHeight) * 0.5;
      var imgLeft = this.options.data.imageLocation == 'right' ? this.width - imgWidth - 2 * margin : 2 * margin;
      var imgTop = imgMargin;

      // description text width and height
      var dspWidth = this.width;
      var dspHeight = this.height - mnNodeHeight;
      var dspTop = mnNodeHeight;
      var description = this.options.data.description;
      var processWidth = this.options.data.process * mnNodeWidth;
      var src = figure.options.src;

      // draw main node
      if (this.options.data.process > 0 && this.options.data.process < 1) {
        context.rect(processWidth + margin / 2, 0, mnNodeWidth - processWidth - margin / 2, mnNodeHeight);
        this.fill(context, 'rgb(242, 250, 0)');
        this.stroke(context, figure.context.strokeStyle, figure.context.lineWidth);

        context.beginPath();
        context.rect(0, 0, processWidth, mnNodeHeight);
        this.fill(context, figure.context.fillStyle);
        this.stroke(context, figure.context.strokeStyle, figure.context.lineWidth);
      } else {
        context.rect(0, 0, mnNodeWidth, mnNodeHeight);
        this.fill(context, figure.context.fillStyle);
        this.stroke(context, figure.context.strokeStyle, figure.context.lineWidth);
      }

      // draw image
      if (src && $.trim(src) != '') {
        if (!this.img) { // when we're loading image first time,we should invoke onload function to make sure image is load completely.
          // create a new image
          this.img = new Image();
          this.img.src = src + '?' + $.now();
          var nodeMargin = this.options.margin;
          $(this.img).load(function() {
            context.drawImage(this, nodeMargin + imgLeft, nodeMargin + imgTop, imgWidth, imgHeight);
          });
        } else { // use the image in the cookies
          try {
            context.drawImage(this.img, imgLeft, imgTop, imgWidth, imgHeight);
          } catch (err) {
          }
        }
      }

      // draw description with canvas text.
      context.beginPath();
      context.rect(0, dspTop, dspWidth, dspHeight);
      this.stroke(context, figure.context.strokeStyle, figure.context.lineWidth);

      if (description && $.trim(description) != '') {
        context.beginPath();
        context.save();
        context.fillStyle = 'rgb(0,0,0)';
        context.font = 'normal 400 13px Unknown Font, sans-serif';
        var description_width = context.measureText(description).width;
        if (description_width > dspWidth) {
          description_width = dspWidth;
        }
        context.fillText(description, (dspWidth - description_width) / 2, dspTop + 14, dspWidth);
        context.restore();
      }

      return {
        fill: false,
        stroke: false,
      };
    },
    hitFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
    selectFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
  });
  new jBME.Node.Type('conditionnode', {
    figure: function(context, figure) {
      // the width,height and margin of condition part
      var margin = parseInt(figure.context.lineWidth);
      var conditWidth = this.width;
      var conditHeight = this.height * 0.5;

      // main node width,height,left and top
      var mnNodeWidth = this.width;
      var mnNodeHeight = this.height * 0.35;
      var mnLeft = 0;
      var mnTop = conditHeight;

      // description text width and height
      var dspWidth = this.width;
      var dspHeight = this.height - mnTop - mnNodeHeight;
      var dspLeft = 0;
      var dspTop = mnTop + mnNodeHeight;
      var description = this.options.data.description;

      // state image width and height
      var imgMargin = Math.min(mnNodeWidth, mnNodeHeight) * 1 / 4;
      var imgWidth = Math.min(mnNodeWidth, mnNodeHeight) * 0.5;
      var imgHeight = Math.min(mnNodeWidth, mnNodeHeight) * 0.5;
      var imgLeft = this.options.data.imageLocation == 'right' ? this.width - imgWidth - margin : margin;
      var imgTop = conditHeight + imgMargin;

      var processWidth = this.options.data.process * mnNodeWidth;
      var src = figure.options.src;

      // draw main node
      if (this.options.data.process > 0 && this.options.data.process < 1) { // to show process, we must draw background.
        context.rect(processWidth + margin / 2, mnTop, mnNodeWidth - processWidth - margin / 2, mnNodeHeight);
        this.fill(context, 'rgb(242, 250, 0)');
        this.stroke(context, figure.context.strokeStyle, figure.context.lineWidth);

        context.beginPath();
        context.rect(mnLeft, mnTop, processWidth, mnNodeHeight);
        this.fill(context, figure.context.fillStyle);
        this.stroke(context, figure.context.strokeStyle, figure.context.lineWidth);
      } else {
        context.rect(mnLeft, mnTop, mnNodeWidth, mnNodeHeight);
        this.fill(context, figure.context.fillStyle);
        this.stroke(context, figure.context.strokeStyle, figure.context.lineWidth);
      }

      // draw image
      if (src && $.trim(src) != '') {
        if (!this.img) { // when we're loading image first time,we should invoke onload function to make sure image is load completely.
          // create a new image
          this.img = new Image();
          this.img.src = src + '?' + $.now();
          var nodeMargin = this.options.margin;
          $(this.img).load(function() {
            context.drawImage(this, nodeMargin + imgLeft, nodeMargin + imgTop, imgWidth, imgHeight);
          });
        } else { // use the image in the cookies
          try {
            context.drawImage(this.img, imgLeft, imgTop, imgWidth, imgHeight);
          } catch (err) {
          }
        }
      }

      // draw diamond
      context.beginPath();
      context.moveTo(0, conditHeight / 2);
      context.lineTo(conditWidth / 2, 0);
      context.lineTo(conditWidth, conditHeight / 2);
      context.lineTo(conditWidth / 2, conditHeight);
      context.lineTo(0, conditHeight / 2);
      context.lineTo(conditWidth / 2, 0);
      this.stroke(context, figure.context.strokeStyle, figure.context.lineWidth);

      // draw description with canvas text.
      context.beginPath();
      context.rect(dspLeft, dspTop, dspWidth, dspHeight);
      this.stroke(context, figure.context.strokeStyle, figure.context.lineWidth);

      if (description && $.trim(description) != '') {
        context.beginPath();
        context.save();
        context.fillStyle = 'rgb(0,0,0)';
        context.font = 'normal 400 13px Unknown Font, sans-serif';

        var description_width = context.measureText(description).width;
        if (description_width > dspWidth) {
          description_width = dspWidth;
        }
        context.fillText(description, (dspWidth - description_width) / 2, dspTop + 14, dspWidth);
        context.restore();
      }
    },
    hitFigure: function(context, figure) {
      var margin = parseInt(figure.context.lineWidth);
      var conditWidth = this.width;
      var conditHeight = this.height * 0.5 - margin;
      var nextWidth = this.width;
      var nextHeight = this.height - conditHeight;

      context.moveTo(0, conditHeight / 2);
      context.lineTo(conditWidth / 2, 0);
      context.lineTo(conditWidth, conditHeight / 2);
      context.lineTo(conditWidth / 2, conditHeight);
      context.lineTo(0, conditHeight / 2);
      context.rect(0, conditHeight, nextWidth, nextHeight);
    },
    anchors: [
      [0.5, 0.5],
      [0.5, 0],
      [1, 0.24],
      [1, 0.5],
      [1, 1],
      [0.5, 1],
      [0, 1],
      [0, 0.5],
      [0, 0.24],
    ],
  });
  new jBME.Node.Type('ETLImage', {
    figure: function(context, figure) {
      context.strokeStyle = 'rgba(0,0,0,0)';
      var margin = this.options.margin;
      var mainImgMargin = figure.context.lineWidth;

      var imgWidth = this.width;
      var imgHeight = this.height;
      var stateImgSrc = this.options.data.stateImgSrc;
      src = figure.options.src;

      if (src && $.trim(src) != '') {
        context.shadowColor = 'rgba(0,0,0,0)';
        if (!this.img) {
          this.img = new Image();
          this.img.src = src;
          $(this.img).load(function() {
            try {
              context.drawImage(this, mainImgMargin + margin, mainImgMargin + margin, imgWidth, imgHeight);
              stateImg && context.drawImage(stateImg, 20, 20, 20, 20);
            } catch (err) {
            }
          });
        } else {
          try {
            context.drawImage(this.img, mainImgMargin, mainImgMargin, imgWidth, imgHeight);
          } catch (err) {
          }
        }
      }

      if (stateImgSrc && $.trim(stateImgSrc) != '') {
        // set shadow transparent if not it will cover the image.
        context.shadowColor = 'rgba(0,0,0,0)';
        if (!this.stateImg || this.stateImg.src.indexOf(stateImgSrc) < 0) { // when we're loading image first time,we should invoke onload function to make sure image is load completely.
          // create a new image
          var stateImg = new Image();
          stateImg.src = stateImgSrc;
          $(stateImg).load(function() {
            context.drawImage(this, margin + 20, margin + 20, 20, 20);
          });
          this.stateImg = stateImg;
        } else { // use the image in the cookies
          try {
            context.drawImage(this.stateImg, 20, 20, 20, 20);
          } catch (err) {
          }
        }
      }
    },
    hitFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
    selectFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
    anchors: [
      [0.5, 0.5],
      [0.5, 0],
      [1, 0],
      [1, 0.5],
      [1, 1],
      [0.5, 1],
      [0, 1],
      [0, 0.5],
      [0, 0],
    ],
  });
  new jBME.Node.Type('jNode', {
    figure: function(context, figure) {
      var radius = 8;
      context.lineWidth = 0.1;
      context.moveTo(0, radius);
      context.arc(radius, radius, radius, Math.PI, Math.PI * 3 / 2, false);
      context.lineTo(this.width - radius, 0);
      context.arc(this.width - radius, radius, radius, Math.PI * 3 / 2, Math.PI * 2, false);
      context.lineTo(this.width, this.height - radius);
      context.arc(this.width - radius, this.height - radius, radius, 0, Math.PI * 1 / 2, false);
      context.lineTo(radius, this.height);
      context.arc(radius, this.height - radius, radius, Math.PI * 1 / 2, Math.PI, false);
      context.lineTo(0, radius);
      this.fill(context, figure.context.fillStyle);
      this.stroke(context, figure.context.strokeStyle, figure.context.lineWidth);

      if (!this.img) {
        var img = new Image();
        img.src = figure.options.src;
        $(img).load(function() {
          context.drawImage(img, 3, 0);
        });
        this.img = img;
      } else { // Use the image in the cookies
        try {
          var img = new Image();
          img.src = figure.options.src;
          context.drawImage(this.img, -22, -25);
        } catch (err) {
        }
      }
    },
    hitFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
    anchors: [
      [0.5, 0.5],
      [0.5, 0],
      [1, 0],
      [1, 0.5],
      [1, 1],
      [0.5, 1],
      [0, 1],
      [0, 0.5],
      [0, 0],
    ],
  });
  new jBME.Node.Type('infobeannode', {
    figure: function(context, figure) {
      // main node width,height and margin
      var mainImgMargin = figure.context.lineWidth;
      var mainImgWidth = this.width - 2 * mainImgMargin;
      var mainImgHeight = this.height - 2 * mainImgMargin;
      var mainImgLeft = mainImgMargin;
      var mainImgTop = mainImgMargin;

      // state image width,height,left,top,margin
      var imgMargin = Math.min(mainImgWidth, mainImgHeight) * 1 / 10;
      var imgWidth = Math.min(mainImgWidth, mainImgHeight) / 4;
      var imgHeight = Math.min(mainImgWidth, mainImgHeight) / 4;
      var imgLeft = mainImgWidth - imgWidth - imgMargin;
      var imgTop = mainImgHeight - imgHeight - imgMargin;

      var cornerMargin = Math.min(mainImgWidth, mainImgHeight) * 1 / 10;
      var cornerWidth = Math.min(mainImgWidth, mainImgHeight) / 4;
      var cornerHeight = Math.min(mainImgWidth, mainImgHeight) / 4;
      var cornerLeft = mainImgWidth - cornerWidth - cornerMargin;
      var cornerTop = cornerMargin;

      var mainImgSrc = figure.options.src;
      var stateSrc = this.options.data.stateImgSrc;
      var cornerSrc = this.options.data.cornerImgSrc;
      var nodeMargin = this.options.margin;

      // draw main image
      if (mainImgSrc && $.trim(mainImgSrc) != '') {
        // set shadow transparent if not it will cover the image.
        context.shadowColor = 'rgba(0,0,0,0)';
        if (!this.mainImg) { // when we're loading image first time,we should invoke onload function to make sure image is load completely.
          // create a new image
          this.mainImg = new Image();
          this.mainImg.src = mainImgSrc + '?' + $.now();
          $(this.mainImg).load(function() {
            try {
              context.drawImage(this, mainImgLeft + nodeMargin, mainImgTop + nodeMargin, mainImgWidth, mainImgHeight);
              stateImg && context.drawImage(stateImg, imgLeft + nodeMargin, imgTop + nodeMargin, imgWidth, imgHeight);
              cornerImg && context.drawImage(cornerImg, cornerLeft + nodeMargin, cornerTop + nodeMargin, cornerWidth, cornerHeight);
            } catch (err) {
            }
          });
        } else { // use the image in the cookies
          try {
            context.drawImage(this.mainImg, mainImgLeft, mainImgTop, mainImgWidth, mainImgHeight);
          } catch (err) {
          }
        }
      }

      // draw state image
      if (stateSrc && $.trim(stateSrc) != '') {
        // set shadow transparent if not it will cover the image.
        context.shadowColor = 'rgba(0,0,0,0)';
        if (!this.stateImg) { // when we're loading image first time,we should invoke onload function to make sure image is load completely.
          // create a new image
          var stateImg = new Image();
          stateImg.src = stateSrc + '?' + $.now();
          $(stateImg).load(function() {
            context.drawImage(this, imgLeft + nodeMargin, imgTop + nodeMargin, imgWidth, imgHeight);
          });
          this.stateImg = stateImg;
        } else { // use the image in the cookies
          try {
            context.drawImage(this.stateImg, imgLeft, imgTop, imgWidth, imgHeight);
          } catch (err) {
          }
        }
      }

      // draw corner image
      if (cornerSrc && $.trim(cornerSrc) != '') {
        // set shadow transparent if not it will cover the image.
        context.shadowColor = 'rgba(0,0,0,0)';
        if (!this.cornerImg) { // when we're loading image first time,we should invoke onload function to make sure image is load completely.
          // create a new image
          var cornerImg = new Image();
          cornerImg.src = cornerSrc + '?' + $.now();
          $(cornerImg).load(function() {
            context.drawImage(this, cornerLeft + nodeMargin, cornerTop + nodeMargin, cornerWidth, cornerHeight);
          });
          this.cornerImg = cornerImg;
        } else { // use the image in the cookies
          try {
            context.drawImage(this.cornerImg, cornerLeft, cornerTop, cornerWidth, cornerHeight);
          } catch (err) {
          }
        }
      }

      // draw transparent node for hover and drag
      context.beginPath();
      context.fillStyle = 'rgba(0,0,0,0)';
      context.strokeStyle = 'rgba(0,0,0,0)';
      context.rect(0, 0, this.width, this.height);
    },
    anchors: jBME.Node.Type['rect'].anchor,
  });
  new jBME.Node.Type('processbar', {
    figure: function(context, figure) {
      var bounds = this.getFigureBounds(figure);
      context.translate(bounds.left, bounds.top);

      var process = this.options.data.process * bounds.width;
      if (process > bounds.width) {
        process = bounds.width;
      }
      var radius = Math.min(bounds.width, bounds.height) / 2;

      context.shadowOffsetX = 1;
      context.shadowOffsetY = 1;
      context.shadowBlur = 3;
      context.shadowColor = '#666';

      context.lineWidth = 1;
      context.fillStyle = 'rgba(189,189,189,1)';
      context.moveTo(radius, 0);
      context.lineTo(bounds.width - radius, 0);
      context.arc(bounds.width - radius, radius, radius, -Math.PI / 2, Math.PI / 2, false);
      context.lineTo(radius, bounds.height);
      context.arc(radius, radius, radius, Math.PI / 2, 3 * Math.PI / 2, false);
      context.closePath();
      context.fill();
      var lingrad = context.createLinearGradient(0, bounds.height, 0, 0);
      lingrad.addColorStop(0, 'rgba(255,255,255, 0.1)');
      lingrad.addColorStop(0.4, 'rgba(255,255,255, 0.7)');
      lingrad.addColorStop(1, 'rgba(255,255,255,0.4)');
      context.fillStyle = lingrad;
      context.fill();

      context.moveTo(radius, 0);
      context.lineTo(bounds.width - radius, 0);
      context.arc(bounds.width - radius, radius, radius, -Math.PI / 2, Math.PI / 2, false);
      context.lineTo(radius, bounds.height);
      context.arc(radius, radius, radius, Math.PI / 2, 3 * Math.PI / 2, false);
      context.closePath();
      context.beginPath();

      // Blue gradient for progress bar
      var progress_lingrad = context.createLinearGradient(0, bounds.height, 0, 0);
      progress_lingrad.addColorStop(0, '#4DA4F3');
      progress_lingrad.addColorStop(0.4, '#ADD9FF');
      progress_lingrad.addColorStop(1, '#9ED1FF');
      context.fillStyle = progress_lingrad;

      if (process < radius) {
        offset = radius - Math.sqrt(Math.pow(radius, 2) - Math.pow((radius - process), 2));
        context.moveTo(process, offset);
        context.lineTo(process, bounds.height - offset);
        context.arc(radius, radius, radius, Math.PI - Math.acos((radius - process) / radius), Math.PI + Math.acos((radius - process) / radius), false);
      } else if (process + radius > bounds.width) {
        offset = radius - Math.sqrt(Math.pow(radius, 2) - Math.pow((radius - (bounds.width - process)), 2));
        context.moveTo(radius, 0);
        context.lineTo(bounds.width - radius, 0);
        context.arc(bounds.width - radius, radius, radius, -Math.PI / 2, -Math.acos((radius - (bounds.width - process)) / radius), false);
        context.lineTo(process, bounds.height - offset);
        context.arc(bounds.width - radius, radius, radius, Math.acos((radius - (bounds.width - process)) / radius), Math.PI / 2, false);
        context.lineTo(radius, bounds.height);
        context.arc(radius, radius, radius, Math.PI / 2, 3 * Math.PI / 2, false);
      } else {
        context.moveTo(radius, 0);
        context.lineTo(process, 0);
        context.lineTo(process, bounds.height);
        context.lineTo(radius, bounds.height);
        context.arc(radius, radius, radius, Math.PI / 2, 3 * Math.PI / 2, false);
      }
      context.closePath();
      context.fill();

      context.beginPath();

      // set font
      context.font = 'normal 400 12px/2 Unknown Font, sans-serif';
      this.context.fillStyle = 'white';
      var text = Math.floor(this.options.data.process * 100) + '%';
      var text_width = this.context.measureText(text).width;
      var text_x = process - text_width - radius / 2;
      if (process <= radius + text_width) {
        text_x = radius / 2;
      }
      context.fillText(text, text_x, bounds.height - 1);

      // draw transparent node for hover and drag
      context.beginPath();
      context.fillStyle = 'rgba(0,0,0,0)';
      context.strokeStyle = 'rgba(0,0,0,0)';
      context.moveTo(radius, 0);
      context.lineTo(bounds.width - radius, 0);
      context.arc(bounds.width - radius, radius, radius, -Math.PI / 2, Math.PI / 2, false);
      context.lineTo(radius, bounds.height);
      context.arc(radius, radius, radius, Math.PI / 2, 3 * Math.PI / 2, false);
      context.closePath();
    },
    anchors: jBME.Node.Type['rect'].anchor,
  });
  jBME.Boundaryerror = jBME.Node.extend({

    getClosestAttached: function(bounds) {
      var currentBounds = this.getBounds();
      $.extend(currentBounds, bounds);

      var center = {
        left: bounds.left,
        top: bounds.top,
        width: 0,
        height: 0,
      };
      var containers = [];
      var distance = 1000000;
      var postion; var result = {};

      for (var cid in this.diagram.nodes) {
        if (cid == this.id) {
          continue;
        }

        var container = this.diagram.nodes[cid];
        var name = container.options.name;
        if (!name) {
          continue;
        }

        for (var j = 0; j < this.options.locatedOnBounds.length; j++) {
          if (name == this.options.locatedOnBounds[j]) {
            var contains = Utils.containsBound(container.getBounds(), center);
            if (!contains) {
              continue;
            }

            var tempPostion = container.getAnchorPosition('auto', center);
            var dis = (bounds.left - tempPostion.left) * (bounds.left - tempPostion.left) + (bounds.top - tempPostion.top) * (bounds.top - tempPostion.top);
            if (distance > dis) {
              distance = dis;
              result.attachedNode = container;
              result.position = tempPostion;
            }
          }
        }
      }

      return result;
    },
    updateBounds: function(bounds /* [optional]*/, isDraw, zoomargs) {
      zoomargs && (this.zoomargs = zoomargs);
      if (this.diagram.registeredOptions) {
        bounds && this.$box.css(bounds);
      }

      var style = Utils.getStyle(this.options.figures[0].style);
      var width = style.width;
      var height = style.height;

      if (!bounds) {
        var creatingBounds = this.getBounds();

        this.result = this.getClosestAttached(creatingBounds);
      } else {
        var currentBounds = this.getBounds();
        $.extend(currentBounds, bounds);

        if ($.isEmptyObject(this.result)) {
          this.result = this.getClosestAttached(bounds);
        } else {
          this.result.position = this.result.attachedNode.getAnchorPosition('auto', bounds);
        }
      }

      var postion = this.result.position;

      if (!$.isEmptyObject(this.result)) {
        var newBounds = {};
        newBounds.left = postion.left - width / 2;
        newBounds.top = postion.top - height / 2;

        this.$box.css(newBounds);
      }

      if (!bounds || (bounds && (bounds.width || bounds.height))) {
        this.width = Math.max(0, this.$box.width());
        this.height = Math.max(0, this.$box.height());
        this.canvas.width = this.width + 2 * this.options.margin;
        this.canvas.height = this.height + 2 * this.options.margin;
        this.$canvas.css('margin', -this.options.margin);

        this.canvasManager(this.canvas);
        !isDraw && this.draw();
      }

      // Update paths bounds connected to this node.
      for (var id in this.paths) {
        this.paths[id].updateBounds(this.zoomargs);
      }
      return this;
    },
  });

  new jBME.Node.Type('taskcontainer', {
    figure: function(context, figure) {
      // draw rect
      context.rect(0, 0, this.width, this.height);

      // draw state image
      var stateSrc = this.options.data.stateSrc;
      if (stateSrc && $.trim(stateSrc) != '') {
        var imgWidth = Math.min(16, Math.min(this.width, this.height) / 2);
        var imgHeight = Math.min(16, Math.min(this.width, this.height) / 2);
        var imgLeft = imgWidth / 2;
        var imgTop = imgHeight / 2;
        var nodeMargin = this.options.margin;

        if (!this.stateImg) { // when we're loading image first time,we should invoke onload function to make sure image is load completely.
          // create a new image
          var stateImg = new Image();
          stateImg.src = stateSrc + '?' + $.now();
          $(stateImg).load(function() {
            context.drawImage(this, imgLeft + nodeMargin, imgTop + nodeMargin, imgWidth, imgHeight);
          });
          this.stateImg = stateImg;
        } else { // use the image in the cookies
          try {
            context.drawImage(this.stateImg, imgLeft, imgTop, imgWidth, imgHeight);
          } catch (err) {
          }
        }
      }
    },
  });
  jBME.Taskcontainer = jBME.Container.extend({

    fold: function(nodes, newWidth, newHeight) {
      if (this.foldState) {
        return;
      }

      this.foldState = true; /* prevent fold many times */
      this.options.originalWidth = this.width;
      this.options.originalHeight = this.height;

      // Fold this container.
      var newBounds = {
        width: newWidth || this.width,
        height: newHeight || this.height,
      };
      this.options.data.stateSrc = this.options.data.stateImg;
      this.updateBounds(newBounds);

      // Hide its children and change some paths' source or target
      var children = this.getDescendant(nodes);
      for (var cid in children) {
        children[cid].$box.css('display', 'none');

        for (var pid in children[cid].paths) {
          if (children[children[cid].paths[pid].jSource.id] && children[children[cid].paths[pid].jTarget.id]) {
            children[cid].paths[pid].$box.css('display', 'none');
          } else if (!children[children[cid].paths[pid].jSource.id] && children[children[cid].paths[pid].jTarget.id]) {
            !children[cid].paths[pid].options.orignalTarget && (children[cid].paths[pid].options.orignalTarget = []);
            children[cid].paths[pid].options.orignalTarget.push(children[cid].paths[pid].jTarget);
            children[cid].paths[pid].setTarget(this);
          } else if (children[children[cid].paths[pid].jSource.id] && !children[children[cid].paths[pid].jTarget.id]) {
            !children[cid].paths[pid].options.orignalSource && (children[cid].paths[pid].options.orignalSource = []);
            children[cid].paths[pid].options.orignalSource.push(children[cid].paths[pid].jSource);
            children[cid].paths[pid].setSource(this);
          }
        }
      }

      var difWidth = (this.options.originalWidth || this.width) - this.width;
      var difHeight = (this.options.originalHeight || this.height) - this.height;
      this.pullOn(nodes, difWidth, difHeight);
    },
    unfold: function(nodes) {
      if (!this.foldState) {
        return;
      }

      this.foldState = false; /* prevent unfold many times */

      // Unfold this container
      var newBounds = {
        width: this.options.originalWidth || this.width,
        height: this.options.originalHeight || this.height,
      };
      this.options.data.stateSrc = '';

      var changeBound = {};
      if ($.trim(this.options.parent) != '') {
        var thisBound = this.getBounds();
        var parentBound = nodes[this.options.parent].getBounds();

        if (thisBound.top + newBounds.height > parentBound.top + parentBound.height) {
          newBounds.top = parentBound.top + parentBound.height - newBounds.height - this.options.figure.context.lineWidth;
          changeBound.top = newBounds.top - thisBound.top;
        }
        if (thisBound.left + newBounds.width > parentBound.left + parentBound.width) {
          newBounds.left = parentBound.left + parentBound.width - newBounds.width - this.options.figure.context.lineWidth;
          changeBound.left = newBounds.left - thisBound.left;
        }
      }
      this.updateBounds(newBounds);

      this.forceOut(nodes);

      // Show its children
      var children = this.getDescendant(nodes);
      for (var cid in children) {
        if (!nodes[children[cid].options.parent].foldState) {
          children[cid].$box.css('display', '');
          if (!$.isEmptyObject(changeBound)) {
            var cBound = children[cid].getBounds();
            children[cid].updateBounds({
              left: cBound.left + (changeBound.left || 0),
              top: cBound.top + (changeBound.top || 0),
            });
          }
          for (var pid in children[cid].paths) {
            children[cid].paths[pid].$box.css('display', '');
            children[cid].paths[pid].update();
          }
        }
      }

      // Recover paths
      for (var pcid in this.paths) {
        var newPath = {};
        if (this.paths[pcid].options.orignalSource && this.paths[pcid].jSource.id == this.id) {
          newPath = this.paths[pcid].setSource(this.paths[pcid].options.orignalSource[this.paths[pcid].options.orignalSource.length - 1]);
          newPath.options.orignalSource.pop();
        } else if (this.paths[pcid].options.orignalTarget && this.paths[pcid].jTarget.id == this.id) {
          newPath = this.paths[pcid].setTarget(this.paths[pcid].options.orignalTarget[this.paths[pcid].options.orignalTarget.length - 1]);
          newPath.options.orignalTarget.pop();
        }
      }

      this.options.originalWidth = 0;
      this.options.originalHeight = 0;
    },
    /**
     * When unfold taskcontainer, it will force out some nodes in its new scope.
     * @param nodes : all nodes in diagram
     */
    forceOut: function(nodes) {
      var moveNodes = {}; // Restore the nodes to move down
      var topMargin = 20; // The min margin between two nodes
      var jElementBound = this.getBounds();

      for (var nid in nodes) {
        if (this.id == nid || this.options.parent == nid ||
          $.trim(nodes[nid].options.parent) != '' || this.getDescendant(nodes)[nid]) { // 1.Whether these nodes are its descendant or its parent or other container's descendant.
          // If true, don't care and if false, force them out.
          continue;
        }

        var thisBound = nodes[nid].getBounds();

        if (jElementBound.top < thisBound.top && jElementBound.left - thisBound.width <= thisBound.left && jElementBound.left + jElementBound.width >= thisBound.left) { // 2.This node is in influence scope
          moveNodes[nid] = nodes[nid];
        }
      }

      if ($.isEmptyObject(moveNodes)) {
        return;
      }

      var topNum = 0;
      for (var tid in moveNodes) { // get first top num
        topNum = moveNodes[tid].getBounds().top;
        break;
      }

      for (var bid in moveNodes) { // get min top num
        topNum = Math.min(moveNodes[bid].getBounds().top, topNum);
      }
      var changeBound = jElementBound.top + jElementBound.height + topMargin - topNum;

      for (var cvid in moveNodes) { // pull this node
        var cvBound = moveNodes[cvid].getBounds();
        var mdyChangeBound = 0;
        for (var jid in nodes) { // If this node is pull on, whether cover the other nodes.
          var coverBound = nodes[jid].getBounds();
          if (!moveNodes[jid] && nodes[jid].$box.css('display') != 'none' && $.trim(nodes[jid].options.parent) == '' &&
            coverBound.top >= cvBound.top + changeBound - coverBound.height && coverBound.top <= cvBound.top + coverBound.height &&
            coverBound.left - cvBound.width <= cvBound.left && coverBound.left + coverBound.width >= cvBound.left) {
            mdyChangeBound = coverBound.top + coverBound.height + topMargin - cvBound.top;
          }
        }

        // 3.Move down influenced nodes
        var difBound = {
          top: cvBound.top + (mdyChangeBound || changeBound),
        };

        nodes[cvid].updateBounds(difBound);

        // 4.If this node is container, move its descendant.
        if (nodes[cvid].iscontainer) {
          var children = nodes[cvid].getDescendant(nodes);
          for (var cid in children) {
            children[cid].updateBounds({
              top: children[cid].getBounds().top + (mdyChangeBound || changeBound),
            });
          }
        }
      }
    },
    unfoldContainer: function() {
      for (var nid in this.nodes) {
        if (this.nodes[nid].foldState) {
          this.nodes[nid].unfold(this.nodes);
        }
      }
    },
    pullOn: function(nodes, difWidth, difHeight) {
      var moveNodes = {}; // Restore the nodes to pull on
      var topMargin = 20; // The min margin between two nodes
      var jElementBound = this.getBounds();

      for (var pid in nodes) {
        if (this.id != pid && $.trim(nodes[pid].options.parent) == '') {
          var thisBound = nodes[pid].getBounds();
          if (thisBound.top > jElementBound.top + jElementBound.height && jElementBound.left - thisBound.width < thisBound.left &&
            jElementBound.left + jElementBound.width > thisBound.left) {
            moveNodes[pid] = nodes[pid];
          }
        }
      }
      if ($.isEmptyObject(moveNodes)) {
        return;
      }

      var topNum = 0;
      for (var tid in moveNodes) { // get first top num
        topNum = moveNodes[tid].getBounds().top;
        break;
      }

      for (var bid in moveNodes) { // get min top num
        topNum = Math.min(moveNodes[bid].getBounds().top, topNum);
      }
      var changeBound = jElementBound.top + jElementBound.height + topMargin - topNum;

      for (var cvid in moveNodes) { // pull this node
        var cvBound = moveNodes[cvid].getBounds();
        var mdyChangeBound = 0;
        for (var jid in nodes) { // If this node is pull on, whether cover the other nodes.
          var coverBound = nodes[jid].getBounds();
          if (!moveNodes[jid] && nodes[jid].$box.css('display') != 'none' && $.trim(nodes[jid].options.parent) == '' &&
            coverBound.top >= cvBound.top + changeBound - coverBound.height && coverBound.top <= cvBound.top + coverBound.height &&
            coverBound.left - cvBound.width <= cvBound.left && coverBound.left + coverBound.width >= cvBound.left) {
            mdyChangeBound = coverBound.top + coverBound.height + topMargin - cvBound.top;
          }
        }

        // Pull on
        moveNodes[cvid].updateBounds({
          top: cvBound.top + (mdyChangeBound || changeBound),
        });

        if (moveNodes[cvid].iscontainer) { // pull its children together.
          var children = moveNodes[cvid].getDescendant(nodes);
          for (var cid in children) {
            children[cid].updateBounds({
              top: children[cid].getBounds().top + (mdyChangeBound || changeBound),
            });
          }
        }
      }
    },
  });

  new jBME.Node.Type('eventsub', {
    _drawDashedLine: function(context, x, y, x2, y2) {
      var dashArray = [3, 2];
      var dashCount = dashArray.length;
      var dx = (x2 - x);
      var dy = (y2 - y);
      var xSlope = dx > dy;
      var slope = (xSlope) ? dy / dx : dx / dy;

      /*
       * gaurd against slopes of infinity
       */
      if (slope > 9999) {
        slope = 9999;
      } else if (slope < -9999) {
        slope = -9999;
      }

      var distRemaining = Math.sqrt(dx * dx + dy * dy);
      var dashIndex = 0;
      var draw = true;
      while (distRemaining >= 0.1 && dashIndex < 10000) {
        var drawType = draw ? 'lineTo' : 'moveTo';
        var dashLength = dashArray[dashIndex++ % dashCount];
        dashLength = dashLength === 0 ? 0.001 : dashLength;
        dashLength = dashLength > distRemaining ? distRemaining : dashLength;
        var step = Math.sqrt(dashLength * dashLength / (1 + slope * slope));
        var xIncrement = dx < 0 && dy < 0 ? step * -1 : step;
        var yIncrement = dx < 0 && dy < 0 ? slope * step * -1 : slope * step;

        if (xSlope) {
          x += xIncrement;
          y += yIncrement;
        } else {
          x += yIncrement;
          y += xIncrement;
        }
        context[drawType](x, y);
        distRemaining = distRemaining - dashLength;
        draw = !draw;
      }

      context.moveTo(x2, y2);
    },
    figure: function(context, figure) {
      this.jType._drawDashedLine(context, -3, 0, this.width, 0);
      this.jType._drawDashedLine(context, this.width, 0, this.width, this.height);
      this.jType._drawDashedLine(context, this.width, this.height, 0, this.height);
      this.jType._drawDashedLine(context, 0, this.height, 0, 0);
    },
    hitFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
  });
  jBME.Eventsub = jBME.Taskcontainer.extend({
    unfold: function(nodes) {
      if (!this.foldState) {
        return;
      }

      this.foldState = false; /* prevent unfold many times */
      this.img = null;
      this.options.figures[1].options.src = 'theme/default/images/demo/diagram/bpmide/Collapse.gif';
      this.options.figures[1].style.top = 0.8;

      // Unfold this container
      var newBounds = {
        width: this.options.originalWidth || this.width,
        height: this.options.originalHeight || this.height,
      };
      this.options.data.stateSrc = '';

      var changeBound = {};
      if ($.trim(this.options.parent) != '') {
        var thisBound = this.getBounds();
        var parentBound = nodes[this.options.parent].getBounds();

        if (thisBound.top + newBounds.height > parentBound.top + parentBound.height) {
          newBounds.top = parentBound.top + parentBound.height - newBounds.height - this.options.figure.context.lineWidth;
          changeBound.top = newBounds.top - thisBound.top;
        }
        if (thisBound.left + newBounds.width > parentBound.left + parentBound.width) {
          newBounds.left = parentBound.left + parentBound.width - newBounds.width - this.options.figure.context.lineWidth;
          changeBound.left = newBounds.left - thisBound.left;
        }
      }
      this.updateBounds(newBounds);

      // Show its children
      var children = this.getDescendant(nodes);
      for (var cid in children) {
        if (!nodes[children[cid].options.parent].foldState) {
          children[cid].$box.css('display', '');
          if (!$.isEmptyObject(changeBound)) {
            var cBound = children[cid].getBounds();
            children[cid].updateBounds({
              left: cBound.left + (changeBound.left || 0),
              top: cBound.top + (changeBound.top || 0),
            });
          }
          for (var pid in children[cid].paths) {
            children[cid].paths[pid].$box.css('display', '');
            children[cid].paths[pid].update();
          }
        }
      }

      // Recover paths
      for (var pcid in this.paths) {
        var newPath = {};
        if (this.paths[pcid].options.orignalSource && this.paths[pcid].jSource.id == this.id) {
          newPath = this.paths[pcid].setSource(this.paths[pcid].options.orignalSource[this.paths[pcid].options.orignalSource.length - 1]);
          newPath.options.orignalSource.pop();
        } else if (this.paths[pcid].options.orignalTarget && this.paths[pcid].jTarget.id == this.id) {
          newPath = this.paths[pcid].setTarget(this.paths[pcid].options.orignalTarget[this.paths[pcid].options.orignalTarget.length - 1]);
          newPath.options.orignalTarget.pop();
        }
      }

      this.options.originalWidth = 0;
      this.options.originalHeight = 0;
    },
    fold: function(nodes, newWidth, newHeight) {
      if (this.foldState) {
        return;
      }

      this.img = null;
      this.options.figures[1].options.src = 'theme/default/images/demo/diagram/bpmide/puck.png';
      this.options.figures[1].style.top = 0.65;
      this.foldState = true; /* prevent fold many times */
      this.options.originalWidth = this.width;
      this.options.originalHeight = this.height;

      // Fold this container.
      var newBounds = {
        width: newWidth || this.width,
        height: newHeight || this.height,
      };
      this.options.data.stateSrc = this.options.data.stateImg;
      this.updateBounds(newBounds);

      // Hide its children and change some paths' source or target
      var children = this.getDescendant(nodes);
      for (var cid in children) {
        children[cid].$box.css('display', 'none');

        for (var pid in children[cid].paths) {
          if (children[children[cid].paths[pid].jSource.id] && children[children[cid].paths[pid].jTarget.id]) {
            children[cid].paths[pid].$box.css('display', 'none');
          } else if (!children[children[cid].paths[pid].jSource.id] && children[children[cid].paths[pid].jTarget.id]) {
            !children[cid].paths[pid].options.orignalTarget && (children[cid].paths[pid].options.orignalTarget = []);
            children[cid].paths[pid].options.orignalTarget.push(children[cid].paths[pid].jTarget);
            children[cid].paths[pid].setTarget(this);
          } else if (children[children[cid].paths[pid].jSource.id] && !children[children[cid].paths[pid].jTarget.id]) {
            !children[cid].paths[pid].options.orignalSource && (children[cid].paths[pid].options.orignalSource = []);
            children[cid].paths[pid].options.orignalSource.push(children[cid].paths[pid].jSource);
            children[cid].paths[pid].setSource(this);
          }
        }
      }
    },
  });
  new jBME.Node.Type('subprocess', {
    figure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
  });
  jBME.Subprocess = jBME.Eventsub.extend({});
  new jBME.Node.Type('UEEInclude', {
    figure: function(context, figure) {
      var that = this;
      $(function() {
        var $box = jQuery(['<uee:include src="$Webapp+\'', figure.attrs.include, '\'"></uee:include>'].join(''));
        $box.attr('style', figure.style);
        var adapterScp = that.$adapter.scope();
        if (adapterScp) {
          var childScope = adapterScp.$new(false);
          if (that.options.jsonstrdata) {
            that.options.jsonstrdata = angular.fromJson(that.options.jsonstrdata);
            childScope.$Itemdata = that.options.jsonstrdata;
          }

          // this is for size!
          $(that.$figure).unbind('resize.figure').bind('resize.figure', function(e) {
            var heightnow = $(this).outerHeight() + 7;
            if (heightnow > 47 && heightnow != that.bounds.height) {
              that.updateBounds({
                height: heightnow,
              });
            }
          });

          safeApply(childScope, function() {
            var compiled = childScope.$Get('$compile')($box);
            that.$figure.append($box);
            compiled(childScope);
          });
        } else {
          return {
            dom: false,
          };
        }

        function safeApply(scope, fn) {
          (scope.$$phase || scope.$root.$$phase) ? fn() : scope.$apply(fn);
        }
      });

      return {
        dom: true,
      };
    },
    hitFigure: function(context, figure) {
      context.rect(0, 0, this.width, this.height);
    },
  });
}


(function($, window, undefined) {
  '$:nomunge'; // Used by YUI compressor.

  // A jQuery object containing all non-window elements to which the resize
  // event is bound.
  var elems = $([]);

  // Extend $.resize if it already exists, otherwise create it.
  var jq_resize = $.resize = $.extend($.resize, {});

  var timeout_id;

  // Reused strings.
  var str_setTimeout = 'setTimeout';
  var str_resize = 'resize';
  var str_data = str_resize + '-special-event';
  var str_delay = 'delay';
  var str_throttle = 'throttleWindow';

  jq_resize[str_delay] = 250;

  jq_resize[str_throttle] = true;

  $.event.special[str_resize] = {

    // Called only when the first 'resize' event callback is bound per element.
    setup: function() {
      // Since window has its own native 'resize' event, return false so that
      // jQuery will bind the event using DOM methods. Since only 'window'
      // objects have a .setTimeout method, this should be a sufficient test.
      // Unless, of course, we're throttling the 'resize' event for window.
      if (!jq_resize[str_throttle] && this[str_setTimeout]) {
        return false;
      }

      var elem = $(this);

      // Add this element to the list of internal elements to monitor.
      elems = elems.add(elem);

      // Initialize data store on the element.
      $.data(this, str_data, {
        w: elem.width(),
        h: elem.height(),
      });

      // If this is the first element added, start the polling loop.
      if (elems.length === 1) {
        loopy();
      }
    },

    // Called only when the last 'resize' event callback is unbound per element.
    teardown: function() {
      // Since window has its own native 'resize' event, return false so that
      // jQuery will unbind the event using DOM methods. Since only 'window'
      // objects have a .setTimeout method, this should be a sufficient test.
      // Unless, of course, we're throttling the 'resize' event for window.
      if (!jq_resize[str_throttle] && this[str_setTimeout]) {
        return false;
      }

      var elem = $(this);

      // Remove this element from the list of internal elements to monitor.
      elems = elems.not(elem);

      // Remove any data stored on the element.
      elem.removeData(str_data);

      // If this is the last element removed, stop the polling loop.
      if (!elems.length) {
        clearTimeout(timeout_id);
      }
    },

    // Called every time a 'resize' event callback is bound per element (new in
    // jQuery 1.4).
    add: function(handleObj) {
      // Since window has its own native 'resize' event, return false so that
      // jQuery doesn't modify the event object. Unless, of course, we're
      // throttling the 'resize' event for window.
      if (!jq_resize[str_throttle] && this[str_setTimeout]) {
        return false;
      }

      var old_handler;

      // The new_handler function is executed every time the event is triggered.
      // This is used to update the internal element data store with the width
      // and height when the event is triggered manually, to avoid double-firing
      // of the event callback. See the "Double firing issue in jQuery 1.3.2"
      // comments above for more information.

      function new_handler(e, w, h) {
        var elem = $(this);
        var data = $.data(this, str_data);

        // If called from the polling loop, w and h will be passed in as
        // arguments. If called manually, via .trigger( 'resize' ) or .resize(),
        // those values will need to be computed.
        data.w = w !== undefined ? w : elem.width();
        data.h = h !== undefined ? h : elem.height();

        old_handler.apply(this, arguments);
      }

      // This may seem a little complicated, but it normalizes the special event
      // .add method between jQuery 1.4/1.4.1 and 1.4.2+
      if ($.isFunction(handleObj)) {
        // 1.4, 1.4.1
        old_handler = handleObj;
        return new_handler;
      } else {
        // 1.4.2+
        old_handler = handleObj.handler;
        handleObj.handler = new_handler;
      }
    },

  };

  function loopy() {
    // Start the polling loop, asynchronously.
    timeout_id = window[str_setTimeout](function() {
      // Iterate over all elements to which the 'resize' event is bound.
      elems.each(function() {
        var elem = $(this);
        var width = elem.width();
        var height = elem.height();
        var data = $.data(this, str_data);

        // If element size has changed since the last time, update the element
        // data store and trigger the 'resize' event.
        if (width !== data.w || height !== data.h) {
          elem.trigger(str_resize, [data.w = width, data.h = height]);
        }
      });

      // Loop.
      loopy();
    }, jq_resize[str_delay]);
  }
})(jQuery, this);
