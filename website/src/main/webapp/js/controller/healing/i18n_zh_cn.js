/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

var I18n_Exisit = I18n || {};
var I18n = {
  'zh-cn': {
    cmp_select_ne_by_dn: '按网元实例',
    cmp_select_ne_by_type: '按网元类型',
    cmp_healing_dnType_search: '输入过滤网元类型',
    cmp_healing_dn_name_search: '输入过滤网元名称',
    cmp_pm_query_table_orig: '查询原始表',
    cmp_pm_query_table_hour: '查询小单位时表',
    cmp_pm_query_table_day: '查询天单位表',
    cmp_pm_dn_select: '选中',
    cmp_pm_dn_name: '网元名称',
    cmp_pm_dn_type: '网元类型',
    cmp_pm_measKeyType: '测量指标',
    cmp_pm_algorithm: '指标算法',
    cmp_pm_measKeyType_limit: '选择的测量指标已达到上限10个',
    cmp_pm_dn_select_limit: '选择的网元实例已达到上限10个',
    cmp_pm_query_param_empty: '查询参数列表为空',
    cmp_alarm_filters_limit: '告警查询过滤条件限制30',
    cmp_pm_query_time: '数据查询时间区间',
    cmp_pm_query_time_start_empty: '初始时间不能为空',
    cmp_pm_query_time_end_empty: '结束时间不能为空',
    cmp_alarm_filter_relation_and: '过滤条件关系为且',
    cmp_alarm_filter_relation_or: '过滤条件关系为或',
    cmp_alarm_param_introduce: '告警参数设置，过滤条件可为空。查询关系为所有过滤条件取and 或者 or。查询结果最大支持2000条。结果文件下发地址参数对应流程参数中的参数名称。结果文件标识为结果文件路径标识，供后续原子脚本分析。',
    cmp_pm_param_introduce: '性能参数设置，最多选择10个测量指标，10个网元实例进行查询。注意，不同测量单元，网元实例存在重复情况。选择不同的网元类型会得到不同测量单元，不同测量单元，下测量指标可能出现相同的情况。切换测量单元，网元实例列表也会切换，需要通过点击测量单元名称，或者展开测量单元名称树。',
    cmp_pm_query_time_zone: '查询时间：',
    cmp_alarm_filter_name_duplicated: '自定义名称不可重复',
    cmp_healing_flow_unit: '参数单位为秒/分钟，默认单位为秒。',
    cmp_healing_flow_check: '检查当前输入的值：',
    cmp_healing_flow_empty: '必填项为空!',
    cmp_healing_flow_noParameter: '该流程无流程参数。',
    cmp_healing_flow_execute: '执行流程',
    cmp_healing_task_outputting: '输出',
    cmp_healing_noUrl: '该次执行无可跳转地址。',
    cmp_healing_executes: '请先执行该流程！',
    cmp_healing_flow_excute_tips_modify_name: '此修改只对本次流程执行生效',
    cmp_healing_refresh: '刷新',
    cmp_healing_flow_search_reviewtatus: '审核状态：',
    cmp_healing_flow_flowParameter_tip: '设置方法：',
    cmp_healing_flow_flowParameter_tip_1: '1）该参数为空时，执行主机的IP默认取前一原子中appendIps设置的目标执行主机列表。',
    cmp_healing_flow_flowParameter_tip_2: '2）该参数不为空时，执行主机的IP取参数中配置的主机列表。该参数最多配置一个，与“创建流程”的“流程参数”的配置的参数名称或原子的“appendparameter”中的key一致。',
    cmp_healing_flow_flowParameter_tip_first: '1）该参数为空时，执行主机IP默认取所有解决方案主机列表中的全部IP。',
    cmp_healing_flow_flowParameter_tip_second: '2）该参数不为空时，执行主机IP取所选解决方案主机列表中的全部IP。该参数最多配置一个，并且与“创建流程”中“流程参数”中的参数名称一致。',

    cmp_healing_flow_flowParameter_one_tip_first: '1）该参数为空时，执行主机IP默认取所有解决方案主机列表中的全部IP中随机选择一个。',
    cmp_healing_flow_flowParameter_one_tip_second: '2）该参数不为空时，执行主机IP取所选解决方案主机列表中的全部IP中随机选择一个。该参数最多配置一个，并且与“创建流程”中“流程参数”中的参数名称一致。',
    checkInput: '最大长度为128，不能包含的特殊字符为#%&$+=|><\';`?\\()',
    cmp_healing_flow_upload_file_tips: ' 1. 支持{0}格式的文件，大小{1}M以内。2. 文件名必须以数字或字母开头，且不能包含特殊字符/\:"?*><|。3. 文件名长度不能超过128个字符。',
    cmp_healing_flow_flowParameter_validation_params_upload_file_tip: '参数来源为文件上传时，此处输入参数名即可，文件在流程执行时上传。',
    cmp_healing_flow_flowParameter_validation_params_upload_file_require_tip: '文件上传类型流程参数不能为空',
    cmp_healing_flow_flowParameter_validation_params_upload_file: '在有密码型参数时，不允许有文件上传参数',
    cmp_healing_flow_flowParameter_validation_params_json_scriptType_hasParam: '级联型参数中的脚本型参数，必须包含键值对‘hasParam’，其值为布尔型（true/false），参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_scriptType_length: '级联型参数中的脚本型，其value值的长度不能超过256，参数名：',
    cmp_workbeans_script_search: '查找：',
    cmp_healing_atom_title: '原子库',
    cmp_healing_atom_solution_motype: '解决方案网元类型',
    cmp_healing_atom_solution_motype_all: '全部',
    cmp_healing_filesize_tips: '上传文件大小不超过50MB限制！',
    cmp_healing_file_maximum_tips: '最多上传两个文件！',
    cmp_healing_files_duplication_tips: '上传文件不能重复！',
    cmp_healing_atom_not_exist_tips: '当前原子不存在,请检查原子库中原子是否被删除！',
    cmp_healing_atom_list: '原子列表',
    cmp_healing_atom_name: '原子名称',
    cmp_healing_atom_atomName: '原子名称',
    cmp_healing_atom_type_all: '全部',
    cmp_healing_atom_type_shell: 'shell',
    cmp_healing_atom_type_python: 'python',
    cmp_healing_atom_type_dsf: 'dsf',
    cmp_healing_atom_type_rest: 'rest',
    cmp_healing_atom_func: '功能',
    cmp_healing_atom_func_all: '全部',
    cmp_healing_atom_func_diag: '诊断',
    cmp_healing_atom_func_heal: '自愈',
    cmp_healing_atom_func_other: '其他',
    cmp_healing_atom_motype: '网元类型',
    cmp_healing_atom_desc: '描述',
    cmp_healing_atom_operation: '操作',
    cmp_healing_atom_operation_delete: '删除',
    cmp_healing_atom_operation_view: '查看',
    cmp_healing_atom_script_edit: '编辑原子脚本',
    cmp_healing_atom_script_name: '名称',
    cmp_healing_atom_script_desc: '描述',
    cmp_healing_atom_script_func: '功能',
    cmp_healing_atom_script_func_diag: '诊断',
    cmp_healing_atom_script_func_heal: '自愈',
    cmp_healing_atom_script_func_other: '其他',
    cmp_healing_atom_script_type: '类型',
    cmp_healing_atom_script_type_shell: 'shell',
    cmp_healing_atom_script_type_python: 'python',
    cmp_healing_atom_script_params: '参数',
    cmp_healing_atom_script_params_config: '参数配置',
    cmp_healing_atom_script_params_name: '参数名称',
    cmp_healing_atom_script_params_desc: '参数描述',
    cmp_healing_atom_script_params_confirm: '确认',
    cmp_healing_atom_script_params_cancel: '取消',
    cmp_healing_atom_script_params_operation: '操作',
    cmp_healing_atom_script_params_operation_add: '增加',
    cmp_healing_atom_script_params_operation_delete: '删除',
    cmp_healing_atom_script_content: '内容',
    cmp_healing_atom_script_solution_motype: '解决方案网元类型',
    cmp_healing_atom_script_create: '保存',
    cmp_healing_atom_script_update: '更新',
    cmp_healing_atom_script_list: '原子脚本列表',
    cmp_healing_atom_script_upload: '上传',
    cmp_healing_atom_script_upload_select: '请选择文件：',
    cmp_healing_atom_script_source: '来源',
    cmp_healing_atom_script_source_manual: '手工录入',
    cmp_healing_atom_script_source_local: '本地上传',
    cmp_healing_atom_script_type_all: '全部',
    cmp_healing_atom_script_type_diagnose: '诊断',
    cmp_healing_atom_script_type_healing: '自愈',
    cmp_healing_atom_script_status: '状态',
    cmp_healing_atom_script_status_all: '全部',
    cmp_healing_atom_script_status_released: '已发布',
    cmp_healing_atom_script_status_unreleased: '未发布',
    cmp_healing_atom_script_timerange: '时间范围',
    cmp_healing_atom_script_description: '描述',
    cmp_healing_atom_script_creator: '创建人',
    cmp_healing_atom_script_createtime: '创建时间',
    cmp_healing_atom_script_modifier: '修改人',
    cmp_healing_atom_script_modifytime: '修改时间',
    cmp_healing_atom_script_operation: '操作',
    cmp_healing_atom_script_operation_release: '发布',
    cmp_healing_atom_script_operation_view: '查看',
    cmp_healing_atom_script_editor_shell_header_info1: '原子脚本文件头',
    cmp_healing_atom_script_editor_shell_method_info1: '原子API函数头',
    cmp_healing_atom_script_api_name: '原子名称',
    cmp_healing_atom_script_api_description: '原子描述',
    cmp_healing_atom_script_api_parameterNames: '原子参数',
    cmp_healing_atom_script_api_returnName: '原子返回',
    cmp_healing_atom_edit_script_tips: '流程编排仅提供执行通道，内容安全由用户保证。',
    cmp_healing_atom_status: '执行状态',
    cmp_healing_atom_status_executing: '执行中',
    cmp_healing_atom_status_success: '成功',
    cmp_healing_atom_status_failed: '失败',
    cmp_healing_atom_status_timeout: '超时',
    cmp_healing_atom_status_discover: '发现故障',
    cmp_healing_atom_status_undiscover: '未发现故障',
    cmp_healing_atom_status_healingsuccess: '自愈成功',
    cmp_healing_atom_status_healingfailed: '自愈失败',
    cmp_healing_atom_duration: '耗时（秒）',
    cmp_healing_atom_result: '执行结果',
    cmp_healing_atom_result_ok: '执行无异常',
    cmp_healing_atom_script_exception: '脚本异常',
    cmp_healing_atom_response: '返回值',
    cmp_healing_atom_type: '原子类型',
    cmp_healing_atom_starttime: '开始时间',
    cmp_healing_atom_endtime: '结束时间',
    cmp_healing_atom_hostip: '主机IP',
    cmp_healing_atom_runuser: '执行用户',
    cmp_healing_atom_detail: '详情',
    cmp_healing_atom_host_detail: '详情',
    cmp_healing_atom_host_result: '执行结果',
    cmp_healing_atom_host_result_ok: '执行无异常',
    cmp_healing_atom_details_close: '关闭',
    cmp_healing_atom_alias_name: '原子别名',

    // healing_api_zh_CN
    cmp_healing_api_colon: ':',
    cmp_healing_api_name_table_title: 'API名称',
    cmp_healing_api_name: 'API名称',
    cmp_healing_api_uri: 'URI',
    cmp_healing_api_source: '请求源',
    cmp_healing_api_method: '请求方法',
    cmp_healing_api_head: '请求头',
    cmp_healing_api_body: '请求体',
    cmp_healing_api_params: '请求参数',
    cmp_healing_api_description: '描述',
    cmp_healing_api_info: 'API信息',
    cmp_healing_api_new: '新建API',
    cmp_healing_api_search: '输入API名称、描述',
    cmp_healing_api_delete_info: '确定是否删除名称为{0}的API？',
    cmp_healing_flow_select_from_flow_api: 'API库引用',
    cmp_healing_flow_select_api: '选取API',
    cmp_healing_api_input_param_name: '请填写流程参数名称',
    cmp_healing_api_select: '选择',
    cmp_healing_api_select_info: '当前选择的API是：',
    cmp_healing_api_edit: '编辑',
    cmp_healing_api_update: '更新',
    cmp_healing_api_refer_null: '参数取值类型为API库引用时，不能为空，请为{0}选取API.',
    cmp_healing_api_headers_repeat: 'Headers输入重复',
    cmp_healing_api_uri_tips: '在URL中不使用IP，使用motype方式：将motype用[]标志，例如 https:,且URL中不能包含dv自身ip,CMP请求源只允许填写白名单内的URL',
    cmp_healing_api_url_tips: 'URL中不能包含@符号',
    cmp_healing_api_insecure_protocol_tips: '注意http://是不安全协议算法',

    // healing_dashboard_zh_CN
    cmp_healing_dashboard_line_title: '流程执行统计',
    cmp_healing_dashboard_todo_title: '待审核流程',
    cmp_healing_dashboard_todo_confirm: '审核',
    cmp_healing_dashboard_confirm_approve: '同意',
    cmp_healing_dashboard_confirm_refuse: '全部不同意',
    cmp_healing_dashboard_confirm_cancle: '取消',
    cmp_healing_dashboard_select_tip: '请至少选择一个待确认主机',
    cmp_healing_dashboard_select_user_task: '请至少选择一个待审核节点',
    cmp_healing_dashboard_confirm: '确认',
    cmp_healing_dashboard_tip: '提示',
    cmp_healing_dashboard_execute_count: '-执行次数',
    cmp_healing_dashboard_success_count: '-成功次数',
    cmp_healing_dashboard_confirm_list: '待审核节点列表',
    cmp_healing_dashboard_flow_detail: '流程执行详情',
    cmp_healing_dashboard_last_atom: '审核节点前，流程执行的最后一个原子',
    cmp_healing_dashboard_banner1_title: '新手看我',
    cmp_healing_dashboard_banner2_title: '功能介绍',
    cmp_healing_dashboard_banner3_title: '典型操作场景',
    cmp_healing_dashboard_banner4_title: '典型应用场景',
    cmp_healing_dashboard_banner_learnmore: '了解更多',
    cmp_healing_dashboard_banner1_info1: '辅助用户总结和固化故障诊断经验，提供诊断流程的灵活编排和触发能力，能够对已知故障即时响应，并实现分钟级自动诊断和修复。',
    cmp_healing_dashboard_banner1_info2: '运维人员+研发人员',
    cmp_healing_dashboard_banner1_info3: '运维人员',
    cmp_healing_dashboard_banner1_info4: '运维人员+审批人员',
    cmp_healing_dashboard_banner1_info5: '梳理故障场景，开发故障诊断流程',
    cmp_healing_dashboard_banner1_info6: '将原子与流程打包导出，导入运营商环境',
    cmp_healing_dashboard_banner1_info7: '按需求或自动化执行诊断流程',
    cmp_healing_dashboard_banner1_info8: '发现并消除故障',
    cmp_healing_dashboard_banner2_info1: '自定义故障诊断脚本',
    cmp_healing_dashboard_banner2_info2: '在线配置Restful API',
    cmp_healing_dashboard_banner2_info3: '灵活编排流程',
    cmp_healing_dashboard_banner2_info4: '快速查看诊断结果',
    cmp_healing_dashboard_banner2_info5: '审批高危操作流程',
    cmp_healing_dashboard_banner3_info1: '开发故障诊断流程',
    cmp_healing_dashboard_banner3_info2: '使用故障诊断流程',
    cmp_healing_dashboard_banner4_info1: 'JVM内存泄漏故障诊断',
    cmp_healing_dashboard_banner4_info2: '业务拨测系统定时拨测各个业务节点的健康检查页面，并保存拨测结果',
    cmp_healing_dashboard_banner4_info3: '故障诊断流程定时查询拨测任务结果，并判断哪些业务节点可能发生内存泄露',
    cmp_healing_dashboard_banner4_info4: '根据内存泄露的节点列表，执行故障隔离操作', // healing_zh_CN
    cmp_healing_location: '您的位置',
    cmp_healing_title: '流程管理',
    cmp_healing_dashboard_title: '首页',
    cmp_healing_flow_title: '流程管理',
    cmp_healing_atom_title: '原子库',
    cmp_healing_flow_list_title: '流程库',
    cmp_healing_flow_create_title: '创建流程',
    cmp_healing_flow_create_title_diagram: '图形化创建流程',
    cmp_healing_flow_history_title: '执行历史',
    cmp_healing_query: '查询',
    cmp_healing_reset: '重置',
    cmp_healing_package_title: '流程包',
    cmp_healing_export_package_title: '导出流程包',
    cmp_healing_import_package_title: '导入流程包',
    cmp_healing_import_package_overwrite: '全量覆盖',
    cmp_healing_import_package_tips: '分组已经存在，导入可能存在覆盖，请选择导入方式。',
    cmp_healing_import_package_min_flow_tips: '请至少选择一个流程',
    cmp_healing_all: '全部',
    cmp_healing_null: '无',
    cmp_healing_checklist: '审核列表',
    cmp_healing_checklist_flow: '流程审核列表',
    cmp_healing_checklist_atom: '用户审核列表',
    cmp_healing_check_flow_approve: '通过',
    cmp_healing_check_flow_refuse: '拒绝',
    cmp_healing_check_flow_status_draft: '草稿',
    cmp_healing_check_flow_status_pause: '待审核',
    cmp_healing_check_flow_status_approve: '审核通过',
    cmp_healing_check_flow_status_disapprove: '审核拒绝',
    cmp_healing_check_flow_review_status: '审核状态',
    cmp_healing_check_flow_reviewer: '审核人',
    cmp_healing_checklist_review_failed_reason: '失败原因',
    cmp_healing_checklist_review_result_title: '流程审核结果',
    cmp_healing_checklist_review_result: '审核结果',
    cmp_healing_checklist_review_success: '成功',
    cmp_healing_checklist_review_failed: '失败',
    cmp_healing_check_flow_batch_approve: '批量通过',
    cmp_healing_check_flow_batch_refuse: '批量拒绝',
    cmp_healing_check_flow_batch_review_tip: '请至少选择一条流程进行审核',
    cmp_healing_check_flow_submit_date: '提交时间',
    cmp_healing_management: '系統管理',
    cmp_healing_api_library: 'API库',
    cmp_healing_global_library: '全局参数库',
    IHEALING_VALIDATOR_SPECIAL_7CHAR: '输入不能包含特殊字符！其中，特殊字符为<>()\'"&',
    IHEALING_VALIDATOR_ONE_QUESTION: '输入中最多只能出现一个?',
    IHEALING_VALIDATOR_SPECIAL_6CHAR: '输入不能包含特殊字符！其中，特殊字符为<>()\'"',
    IHEALING_VALIDATOR_URL_PARAM: '输入不能包含特殊字符！其中，特殊字符为<>()"?',
    IHEALING_VALIDATOR_DEFAULT_INFO: '{0}',
    cmp_healing_flow_flowParameter_solution_tip: '根据解决方案选应用类型',

    // healing_flow_zh_CN
    cmp_healing_flow_excuteStatus: '流程正在审核或暂停或执行中，不能被编辑！',
    cmp_healing_flow_search_name: '流程名称：',
    cmp_healing_flow_search_name_pure: '流程名称',
    cmp_healing_flow_search_solution: '解决方案：',
    cmp_healing_flow_search_type: '流程类型：',
    cmp_healing_flow_search_executestatus: '最近一次执行状态：',
    cmp_healing_flow_search_eventtype: '触发事件：',
    cmp_healing_flow_search_lastexecutetime: '最近一次执行时间：',
    cmp_healing_flow_type: '流程类型',
    cmp_healing_flow_type_all: '全部',
    cmp_healing_flow_type_heal: '自愈',
    cmp_healing_flow_type_diag: '诊断',
    cmp_healing_flow_executemode: '执行模式：',
    cmp_healing_flow_executemode_sync: '同步',
    cmp_healing_flow_executemode_async: '异步',
    cmp_healing_flow_executemode_tips: '当执行模式为异步时，并行网关各分支原子支持并行执行，此属性支持修改。',
    cmp_healing_flow_solution: '解决方案',
    cmp_healing_flow_solution_all: '全部',
    cmp_healing_flow_flowname_all: '全部',
    cmp_healing_flow_executestatus: '最近一次执行状态',
    cmp_healing_flow_executestatus_all: '全部',
    cmp_healing_flow_executestatus_notstart: '未执行',
    cmp_healing_flow_executestatus_executing: '正在执行',
    cmp_healing_flow_executestatus_failed: '执行失败',
    cmp_healing_flow_executestatus_success: '执行成功',
    cmp_healing_flow_executestatus_confirm: '待审核',
    cmp_healing_flow_executestatus_canceled: '审核不通过',
    cmp_healing_flow_executestatus_suspended: '执行终止',
    cmp_healing_flow_yesyes: '已签名',
    cmp_healing_flow_nono: '未签名',
    cmp_healing_flow_is_believable: '是否签名',
    cmp_healing_flow_name: '流程名称',
    cmp_healing_flow_description_info: '流程描述',
    cmp_healing_flow_start: '执行',
    cmp_healing_flow_deploy: '启用',
    cmp_healing_flow_submit: '提交',
    cmp_healing_flow_config_template: '配置模板',
    cmp_healing_flow_undeploy: '取消启用',
    cmp_healing_flow_batch_deploy: '批量启用',
    cmp_healing_flow_delete: '删除',
    cmp_healing_flow_modify: '修改',
    cmp_healing_flow_history_trace: '执行历史',
    cmp_healing_flow_view: '查看',
    cmp_healing_flow_deploystatus: '启用状态',
    cmp_healing_flow_deploystatus_deployed: '已启用',
    cmp_healing_flow_deploystatus_undeploy: '未启用',
    cmp_healing_flow_status_lastexecutetime: '最近一次执行时间',
    cmp_healing_flow_operation: '操作',
    cmp_healing_flow_modify_title: '修改流程',
    cmp_healing_flow_copy_title: '复制流程',
    cmp_healing_flow_look_title: '查看流程',
    cmp_healing_flow_save: '保存',
    cmp_healing_flow_release: '保存并启用',
    cmp_healing_flow_history: '查看执行历史',
    cmp_healing_flow_event_type: '事件类型',
    cmp_healing_flow_event_timer: '定时启动',
    cmp_healing_flow_event_alarm: '告警触发',
    cmp_healing_flow_event_alarm_id: '告警ID',
    cmp_healing_flow_event_alarm_motype: '网元类型',
    cmp_healing_flow_event_alarm_measureUnit: '测量单元',
    cmp_healing_flow_event_alarm_metricName: '指标名称',
    cmp_healing_flow_event_alarm_threshold: '阈值',
    cmp_healing_flow_event_alarm_operation: '操作',
    cmp_healing_flow_delete_info: '该流程删除后不可恢复。请确定是否删除流程',
    cmp_healing_flow_delete_confirm_info: '当前流程正在执行，删除将影响其正常执行。',
    cmp_healing_flow_start_info: '请确定是否执行流程？',
    cmp_healing_flow_deploy_info: '若流程绑定了定时或告警事件，流程会被自动执行，请确定是否启用',
    cmp_healing_flow_undeploy_info: '请确定是否取消启用',
    cmp_healing_flow_modify_info: '当前流程正在执行，修改将影响其执行结果，请确认是否修改',
    cmp_healing_flow_event_normal_alarm: '普通告警',
    cmp_healing_flow_event_threshold_alarm: '阈值告警',
    cmp_healing_flow_add: '增加',
    cmp_healing_flow_add_alarm_event: '新增告警事件',
    cmp_healing_flow_cron_expression: 'CRON表达式',
    cmp_healing_flow_event_timer_name: '任务名',
    cmp_healing_flow_select_flow_parameter: '选择流程参数',
    cmp_healing_flow_event_timer_parameter: '执行参数',
    cmp_healing_flow_init_execute_time: '预期执行时间',
    cmp_healing_flow_scheduled_configure_limit: '最多可以配置',
    cmp_healing_flow_scheduled_tasks_number: '个任务。',
    cmp_healing_flow_confirm: '确认',
    cmp_healing_flow_cancel: '取消',
    cmp_healing_flow_tianjia: '添加',
    cmp_healing_flow_auto_refresh: '自动刷新',
    cmp_healing_flow_chose_atom: '1.选择原子',
    cmp_healing_flow_chose_atom_pure: '选择原子',
    cmp_healing_flow_parameter: '流程参数',
    cmp_healing_flow_parameter_number: '流程参数不能超过40个',
    cmp_healing_flow_upload_parameter_number: '文件上传流程参数不能超过10个',
    cmp_healing_interact_template_parameter_number: '交互模板不能超过20个参数',
    cmp_healing_flow_config_template_minimum: '请最少配置一条定时任务模板',
    cmp_healing_flow_config_in_edit: '存在定时任务模板未保存',
    cmp_flow_para_change: '修改参数',
    cmp_healing_flow_parameter_change_complete: '修改流程参数成功。',
    cmp_healing_flow_set_param: '2.参数设置',
    cmp_healing_flow_set_param_pure: '参数设置',
    cmp_healing_flow_chose_host: '3.选择主机',
    cmp_healing_flow_atom_solution: '通过组名查找原子',
    cmp_healing_flow_find_atom: '通过名称查找原子',
    cmp_healing_flow_find_common_atom: '通过名称查找预置工具',
    cmp_healing_flow_find_common_atom_invalid_name: '预置工具的名称只能包含数字、字母、下划线（只能以字母、下划线开头）。并且长度不能超过128。',
    cmp_healing_flow_find_flow: '通过名称查找流程',
    cmp_healing_flow_atom_name: '原子名称：',
    cmp_healing_flow_atom_description: '原子简介：',
    cmp_healing_flow_self_define: '自定义',
    cmp_healing_flow_alarm_information: '告警信息引用',
    cmp_healing_flow_select_from_flow_params: '流程参数引用',
    cmp_healing_flow_location_information: '定位信息',
    cmp_healing_flow_extra_information: '附加信息',
    cmp_healing_flow_alarm_source: '告警源',
    cmp_healing_flow_alarm_start_time: '告警产生时间',
    cmp_healing_flow_match: '首尾匹配',
    cmp_healing_flow_regular_expression: '正则表达式',
    cmp_healing_flow_first_key: '首关键字',
    cmp_healing_flow_last_key: '尾关键字',
    cmp_healing_flow_rule_type: '参数值获取正则表达式',
    cmp_healing_flow_from_atom: '上一原子结果',
    cmp_healing_flow_start_end_key: '首尾关键字：',
    cmp_healing_flow_regular_rule: '正则规则：',
    cmp_healing_flow_host: '执行主机：',
    cmp_healing_flow_host_default: '按应用类型执行',
    cmp_healing_flow_from_alarm: '从告警信息中解析',
    cmp_healing_flow_from_flowParameter: '从流程参数中获取',
    cmp_healing_flow_priority_online: '系统自动选择网元主机',
    cmp_healing_flow_execute_user: '执行用户：',
    cmp_healing_flow_host_chose: '选择主机',
    cmp_healing_flow_get_word: '获取字段：',
    cmp_healing_flow_get_method: '获取方式：',
    cmp_healing_flow_last_step: '上一步',
    cmp_healing_flow_next_step: '下一步',
    cmp_healing_flow_sure: '确定',
    cmp_healing_flow_from_application: '按应用选取',
    cmp_healing_flow_add_hand: '手动添加',
    cmp_healing_flow_host_list: '主机列表',
    cmp_healing_flow_resource: '资源ID',
    cmp_healing_flow_ip: 'IP',
    cmp_healing_flow_manual_ip: '请输入IP，以空格或者回车分隔',
    cmp_healing_flow_solution_colon: '解决方案：',
    cmp_healing_flow_groupname_colon: '组名：',
    cmp_healing_flow_groupname: '组名',
    cmp_healing_flow_diag: '诊断',
    cmp_healing_flow_heal: '自愈',
    cmp_healing_flow_graph_flow: '流程编排',
    cmp_healing_flow_edit: '编辑流程',
    cmp_healing_flow_copy: '复制流程',
    cmp_healing_flow_begin: '开始',
    cmp_healing_flow_end: '结束',
    cmp_healing_flow_condition: '条件',
    cmp_healing_flow_user_confirm: '用户审核',
    cmp_healing_flow_atom: '原子',
    cmp_healing_flow_toolbox: '工具箱',
    cmp_healing_flow_send_mail: '发送邮件',
    cmp_healing_flow_alarm_query: '告警查询',
    cmp_healing_flow_alarm_query_param_setting: '告警查询参数设置:',
    cmp_healing_flow_alarm_query_param_name: '查询条件自定义名称',
    cmp_healing_flow_alarm_query_current_alarm: '当前告警查询',
    cmp_healing_flow_alarm_query_history_alarm: '历史告警查询',
    cmp_healing_flow_alarm_query_param_filed: '查询条件对应字段',
    cmp_healing_flow_alarm_query_param_opt: '操作符',
    cmp_healing_flow_alarm_query_param_value: '值',
    cmp_healing_flow_alarm_query_param_sort: '排序',

    cmp_healing_flow_alarm_query_param_relation_and: '查询条件关系为且',
    cmp_healing_flow_alarm_query_param_relation_or: '查询条件关系为或',
    cmp_healing_flow_alarm_query_param_not_volatile: '过滤条件不可变',
    cmp_healing_flow_alarm_query_param_except_ret_field: '查询结果期待返回字段',
    cmp_healing_flow_alarm_query_param_except_ret_count: '返回结果集默认大小:',
    cmp_healing_flow_alarm_query_param_file_target_address_param: '结果文件下发机器地址参数：',
    cmp_healing_flow_alarm_query_param_file_target_file_flag: '结果文件标识：',
    cmp_healing_flow_pm_query_param_setting: '性能查询参数设置',
    cmp_healing_flow_mail_recipient: '收件人：',
    cmp_healing_flow_mail_carbon_copy: '抄送：',
    cmp_healing_flow_mail_title: '标题：',
    cmp_healing_flow_mail_patch: '附件：',
    cmp_healing_flow_mail_context: '正文：',
    cmp_healing_flow_mail_title_length_validate: '标题不能超过1000个字符',
    cmp_healing_flow_mail_context_length_validate: '正文不能超过8000个字符',
    cmp_healing_flow_mail_patch_upload: '请通过流程参数上传附件，此处填写参数名称',
    cmp_healing_flow_file_path: '文件路径：',
    cmp_healing_flow_file_path_param: '文件路径参数：',
    cmp_healing_flow_host_ip: '主机地址：',
    cmp_healing_flow_host_ip_param: '主机地址参数：',
    cmp_healing_flow_upload_manually: '手动上传',
    cmp_healing_flow_connect: '连接',
    cmp_healing_flow_interact: '交互',
    cmp_healing_flow_interact_template_config: '交互模板配置',
    cmp_healing_flow_interact_add_parameter: '增加参数',
    cmp_healing_flow_validate_parameter_minimum: '请至少添加一个参数。',
    cmp_healing_flow_description: '描述信息',
    cmp_healing_flow_event_config: '事件配置',
    cmp_healing_flow_null: '无',
    CMP_HEALING_VALIDATOR_CRONEXPRESSION: 'cron表达式不正确，请参考格式 */10 * * * * ?',
    cmp_healing_flow_set_alarm_event: '请配置告警事件',
    cmp_healing_flow_set_solution: '请填写解决方案',
    cmp_healing_flow_alarm_msg: '如果原子参数设置为“告警信息引用”或执行主机设置为“从告警信息中解析”，则事件类型必须为“告警触发”。',
    cmp_healing_flow_modify_alarm_event: '修改告警事件',
    cmp_healing_flow_exist_repeat_alarm: '存在重复告警',
    cmp_healing_flow_bind_flow: '该告警已绑定流程：',
    cmp_healing_flow_cannot_bind: '。不能重复绑定',
    cmp_healing_flow_edit_alias_name: '请完成流程中对原子别名的编辑',
    cmp_healing_flow_config_date_title: '配置日期型参数',
    cmp_healing_flow_create_tips_type: '诊断流程只能选择诊断原子，自愈流程可以选择诊断+自愈原子',
    cmp_healing_flow_create_tips_atom_param: 'timeoutSecond参数为默认参数，表示执行原子的超时时间（秒）。取值范围：1~7200。其余参数均为创建原子时指定的参数。',
    cmp_healing_flow_create_tips_atom_param_account: 'account参数为默认参数，表示执行原子的执行用户。填写流程参数名称',
    cmp_healing_flow_create_tips_atom_param_resultIp: 'resultIp参数为默认参数，表示结果文件下发机器地址。填写流程参数名称',
    cmp_healing_flow_create_tips_atom_param_resultFile: 'resultFile参数为默认参数，表示执行原子的结果文件路径变量。',
    cmp_healing_flow_create_tips_sql_atom_param: '流程参数名称',
    cmp_healing_flow_create_tips_sql_atom_param_resultFile_desc: '结果文件标识',
    cmp_healing_flow_create_tips_atom_param_source: '参数取值类型包括四类:1)自定义:表示自定义参数取值。' + '2)告警信息引用：从告警的定位信息或附加信息中匹配得到实际值。' + '3)流程参数引用：根据参数名称从流程参数中匹配到实际值，流程参数包括页面设置的固定参数与原子执行时调用appendParameter方法设置的参数。' + '4)API库引用：将API执行的结果传作为原子参数。',
    cmp_healing_flow_create_tips_atom_self_param_type: '自定义参数值包括两类:1)字符串型:供用户手动输入。2)日期型:在时间控件中选择相应的日期。3)IP列表型:在弹窗中选择相应的IP。',
    cmp_healing_flow_create_tips_host: '执行主机包括四种方式：',
    cmp_healing_flow_create_tips_host_1: '1) 按应用类型执行，是按照原子操作归属的网元类型，运行时自动从管理资源中匹配对应的主机；',
    cmp_healing_flow_create_tips_host_2: '2) 从告警字段中解析，表示从告警信息中的字段解析出产生故障的IP地址；',
    cmp_healing_flow_create_tips_host_3: '3) 从流程参数中获取，表示从流程的参数中进行取值，使用前面原子使用appendIps设置的目标执行主机列表；',
    cmp_healing_flow_create_tips_host_4: '4) 从默认分组中获取，按在线且已接入、在线但未接入为优先级，选取IP字典序排序后第一个在线主机执行；',
    cmp_healing_flow_create_tips_host_5: '5) 自定义，是由用户根据IP，解决方案分组和自定义分组这3个维度来选择目标执行主机。',
    cmp_healing_flow_create_tips_ruletype: '获取方式取值类型包括两类：',
    cmp_healing_flow_create_tips_ruletype_1: '1)正则表达式：配置获取的正则表达式，根据正则匹配获取ip地址',
    cmp_healing_flow_create_tips_ruletype_2: '2)首尾匹配：配置ip的首关键字和尾关键字，系统根据首尾关键字截取ip地址',
    cmp_healing_flow_create_tips_event: '配置启动流程的方式：由告警事件触发、由定时任务触发，或者手动启动流程。',
    cmp_healing_flow_create_tips_event_alarm: '告警事件类型分为：',
    cmp_healing_flow_create_tips_event_alarm_id: '普通告警：已经有明确告警ID的告警',
    cmp_healing_flow_create_tips_event_threshold: '阈值告警：随机生成告警ID',
    cmp_healing_flow_create_tips_event_alarm_2: '一个流程支持配置多个告警',
    cmp_healing_flow_create_tips_event_alarmid: '请填写产品规划的故障告警ID。输入长度小于等于32的数值字符串',
    cmp_healing_flow_create_tips_event_alarm_measunit: '请填写阈值告警对应的测量单元。输入长度小于等于128的字符串',
    cmp_healing_flow_create_tips_event_alarm_measunit2: '样例：CPU性能数据',
    cmp_healing_flow_create_tips_event_alarm_metricname: '请填写阈值告警对应的指标名称。输入长度小于等于128的字符串',
    cmp_healing_flow_create_tips_event_alarm_metricname2: '样例：CPU占用率',
    cmp_healing_flow_create_tips_event_cron_1: 'CRON表达式格式：0 */10 * * * ?',
    cmp_healing_flow_create_tips_event_cron_2: '由以空格符为分隔符的6个字段组成，按照从左至右的顺序分别表示：秒、分、时、日、月、周几。',
    cmp_healing_flow_create_tips_event_cron_3: '其中，“*”  表示任意合法值；“/”表示起始时间开始触发，每隔固定时间触发一次；“?”  表示一个不确定的合法值。',
    cmp_healing_flow_create_tips_event_cron_4: '样例：0 */10 * * * ?，表示每10分钟触发一次，建议时间间隔大于5分钟。',
    cmp_healing_flow_create_tips_user: '执行用户类型包括两种方式：1) 自定义，自定义用户名称且自定义用户的属组需要添加到onip组。2) 流程参数引用，从流程参数中匹配名称为user的流程参数；',
    cmp_healing_flow_create_tips_flow_param: '配置初始流程参数，参数个数最多为30个。例如：创建原子时配置了A参数，如果选择从流程中获取或引用参数，则需要在“流程参数”中增加A参数及对应取值，或在前面原子执行时调用appendParameter方法设置A参数及对应取值。',
    cmp_healing_flow_create_tips_tasklist_batchdelete: '只允许删除{0}天前的执行历史',
    cmp_healing_flow_write_chose: '请填写或者选择解决方案',
    cmp_healing_flow_words_before: '最近',
    cmp_healing_flow_words_day: '天',
    cmp_healing_flow_words_hour: '小时',
    cmp_healing_flow_words_minute: '分钟',
    cmp_healing_flow_create_tips: '点击打开创建流程样例',
    cmp_healing_flow_atom_host_message: '没有可选择的主机IP',
    cmp_healing_flow_deploy_info_noflow: '没有选中的流程',
    cmp_healing_flow_info: '提示',
    cmp_healing_flow_eventtype: '触发事件',
    cmp_healing_flow_event_all: '全部',
    cmp_healing_flow_event_manual: '手动执行',
    cmp_healing_flow_event_internal: '内部执行',
    cmp_healing_flow_invalid_ip: '存在非法的IP地址',
    cmp_healing_flow_failed: '表示流程包含了废弃原子，请及时修改流程',
    cmp_healing_flow_success: '表示流程正常可用',
    cmp_healing_flow_save_msg: '流程保存成功',
    cmp_healing_flow_save_msg_scheduled_tip: '流程保存成功，请为流程创建定时配置或流程任务',
    cmp_healing_flow_save_msg_scheduled_update_tip: '流程保存成功，请更新定时配置以及关联的任务',
    cmp_healing_flow_delete_file_failed: '删除文件失败',
    cmp_healing_flow_align_vertically: '垂直居中对齐',
    cmp_healing_flow_align_horizontally: '水平居中对齐',
    cmp_healing_flow_align_left: '垂直靠左对齐',
    cmp_healing_flow_align_right: '垂直靠右对齐',
    cmp_healing_flow_align_top: '水平靠上对齐',
    cmp_healing_flow_align_bottom: '水平靠下对齐',
    cmp_healing_validator_containspace: '自定义参数值不能包含空格。',
    cmp_healing_flow_motype_chose: '选择应用类型',
    cmp_healing_flow_host_selection_mode: '主机选择模式：',
    cmp_healing_flow_select_all_hosts: '所有主机',
    cmp_healing_flow_select_only_one_host: '一台主机',
    cmp_healing_flow_select_atom_motype: '应用类型：',
    cmp_healing_flow_flowParameter: '流程参数',
    cmp_healing_flow_flowParameter_name: '参数名称',
    cmp_healing_flow_flowParameter_type: '参数类型',
    cmp_healing_flow_flowParameter_description: '参数描述',
    cmp_healing_flow_flowParameter_description_empty: '无描述信息',
    cmp_healing_flow_flowParameter_change_name: '流程参数名称',
    com_healing_flow_whether_can_parameter_modify: '所有流程参数的总长度不能超过1500.',
    cmp_healing_flow_flowParameter_value: '参数值',
    cmp_healing_flow_default_parameter_value: '默认值',
    cmp_healing_flow_flowParameter_user_name: '用户名',
    cmp_healing_flow_flowParameter_group_name: '用户组名',
    cmp_healing_flow_flowParameter_operation: '操作',
    cmp_healing_flow_flowParameter_operation_add_self: '自定义增加',
    cmp_healing_flow_flowParameter_operation_add_global: '选择全局参数',
    cmp_healing_flow_flowParameter_operation_delete: '删除',
    cmp_healing_flow_flowParameter_validation_params_name_duplicate: '流程参数的名称重复，参数名：',
    cmp_healing_flow_flowParameter_validation_params_password_prefix: '密码型参数名没有以IHEALING_FLOW_PASSWD_开头，参数名：',
    cmp_healing_flow_flowParameter_validation_params_password_prefix_tip: '密码型参数名必须以IHEALING_FLOW_PASSWD_开头。',
    cmp_healing_flow_flowParameter_validation_params_not_password_prefix: '非密码型参数不得以IHEALING_FLOW_PASSWD_开头，参数名：',
    cmp_healing_flow_flowParameter_validation_params_name_buildin: '不能输入与内置参数相同的参数，请更正！',
    cmp_healing_flow_flowParameter_db_account_motype_not_exist: '选择的网元类型不存在',
    cmp_healing_flow_flowParameter_db_account_ne_instance: '网元实例',
    cmp_healing_flow_flowParameter_db_account_ne_instance_name: '网元名称',
    cmp_healing_flow_flowParameter_db_account_ne_instance_ip: '网元IP地址',
    cmp_healing_flow_flowParameter_db_account_ne_instance_not_exist: '选择的网元实例不存在',
    cmp_healing_flow_flowParameter_db_account_account: '账号',
    cmp_healing_flow_flowParameter_db_account_account_name: '账号名称',
    cmp_healing_flow_flowParameter_db_account_account_type: '账号类型',
    cmp_healing_flow_flowParameter_db_account_account_protocol: '协议类型',
    cmp_healing_flow_flowParameter_db_account_account_management_address: '账号管理IP',
    cmp_healing_flow_flowParameter_null: '< 空 >',
    cmp_healing_flow_no_record: '没有记录',
    cmp_healing_flow_not_exist: '流程不存在。',
    cmp_healing_flow_sure_name: '确定执行流程：{0}',
    cmp_healing_flow_para_string_type: '字符串型',
    cmp_healing_flow_para_date_type: '日期型',
    cmp_healing_flow_para_password_type: '密码型',
    cmp_healing_flow_pause_time: '暂停时间',
    cmp_healing_flow_para_date_now: '此刻',
    cmp_healing_flow_sort_faults: '只显示发现故障的流程',
    cmp_healing_select_host_status_unavailable: '不受管控',
    cmp_healing_select_group_status: '分组状态',
    cmp_healing_select_group_status_exist: '存在',
    cmp_healing_select_group_status_noexist: '不存在',
    cmp_healing_select_server_delete: '删除不可执行的主机及分组',
    cmp_healing_select_server_delete_infos: '不可执行的主机及分组是指离线、不受管控的主机、不存在的分组，确定删除吗？',
    com_healing_flow_non_execute_info: '该流程包含离线、不受管控的主机、不存在的分组，流程执行时，将不会执行这些主机及分组。',
    com_healing_flow_select_atom_info: '请选择一个原子',
    cmp_healing_flow_flowParameter_source: '参数来源',
    cmp_healing_flow_global_refer: '全局参数引用',
    cmp_healing_flow_create_tips_flow_param_add: '系统支持自定义增加和引用全局参数这两种流程参数增加方式。对于流程参数的更改操作，自定义参数可直接在编辑框中更改，引用全局参数的流程参数更改需要点击编辑按钮进行重新选择。如果流程参数的参数名称和原子参数的参数名称相同，原子参数的值会被流程参数的值覆盖。',
    cmp_healing_global_flow_parameter_update_no_flows: '没有引用该全局参数的流程。',
    cmp_healing_ip_offline_filter: '是否过滤掉离线主机：',
    cmp_healing_atom_details_filter: '是否允许在执行详情页面不展示：',
    cmp_healing_flow_pass_flow_tips: '编辑、复制或者再次保存流程时，对于密码型的参数需要重新输入。', // delete host
    cmp_healing_delete_host: '确定要删除该目标机器吗？',
    cmp_healing_delete_group: '确定要删除该分组吗？',
    cmp_healing_delete_solution: '确定要删除该解决方案吗？', // healing_global_zh_CN
    cmp_healing_global_new: '新建全局参数',
    cmp_healing_global_search: '输入全局参数名称、描述',
    cmp_healing_global_name_table_title: '参数名称',
    cmp_healing_global_description: '描述',
    cmp_healing_global_type: '类型',
    cmp_healing_global_value: '参数值',
    cmp_healing_global_type_string: '字符串型',
    cmp_healing_global_type_date: '日期型',
    cmp_healing_flow_para_string_neinstance: '网元实例型',
    cmp_healing_global_view: '查看',
    cmp_healing_global_delete_info: '确定是否删除名称为{0}的全局参数？',
    cmp_healing_global_flow_check: '查看引用流程',
    cmp_healing_global_info: '全局参数信息',
    cmp_healing_global_name: '参数名称',
    cmp_healing_global_colon: '：',
    cmp_healing_global_edit: '编辑',
    cmp_healing_global_update: '更新',
    cmp_healing_global_select: '选择',
    cmp_healing_flow_global_parameter_search_flows: '流程信息',
    cmp_healing_global_param_name: '引用{0}全局参数的流程列表',
    cmp_ihealing_global_flow_update: '更新流程参数',
    cmp_healing_global_flow_parameter_update: '更新全局参数后，需要去查看引用流程页面执行更新流程参数操作才能使修改后的全局参数值生效。',
    cmp_healing_flow_flowParameter_validation_params_remote_notify_tip: '使用远程通知功能时，请只选择有必要的用户，避免扩散到无关人员',
    cmp_healing_flow_para_string_remotenotify: '远程通知对象型',
    cmp_healing_flow_para_remotenotify_user_update_tips: '有些远程通知用户或用户组不存在，请更新远程通知对象型流程参数：',
    cmp_healing_flow_para_remotenotify_user_max_tips: '选择用户或用户组的最大数量：',
    cmp_healing_flow_global_para_update_tips: '当前有流程正在执行，修改参数会影响这些流程的正常执行，请确定是否继续更新流程参数。',
    cmp_healing_global_flow_parameter_update_success_tips: '更新所有引用该全局变量的流程参数成功。',
    cmp_healing_flow_delete_file: '确定要删除该文件吗？',
    cmp_healing_flow_upload_notice: '请上传{0}文件！',
    cmp_healing_flow_upload_name_notice: '文件名只能包含数字、字母或以下特殊字符_=:.\-',
    cmp_healing_flow_upload_content_notice: '文件内容不能为空！',
    cmp_healing_flow_upload_max: '文件大小不能超过{0}MB！',
    cmp_healing_flow_file_upload: '文件上传',
    cmp_healing_flow_flowParameter_operation_add_file: '文件上传',
    cmp_workbench_fast_script_file_select: '请选择文件',

    // healing_package_zh_CN
    cmp_healing_package_import: '导入',
    cmp_healing_package_export: '导出',
    cmp_healing_package_only_export_atom: '只导出原子',
    cmp_healing_package_obfuscate: '混淆导出',
    cmp_healing_package_only_import_flow_atom: '只导入流程和原子',
    CMP_HEALING_ATOM_TITLE: '原子',
    CMP_HEALING_PACKAGE_ATOM_TYPE: '类型:',
    CMP_HEALING_PACKAGE_ATOM_FILENAME: '脚本名称\:',
    CMP_HEALING_PACKAGE_FLOW_FILENAME: '文件名称:',
    CMP_HEALING_PACKAGE_VERSION: '自愈包版本号',
    CMP_HEALING_PACKAGE_SOLUTION_NAME: '组名',
    CMP_HEALING_PACKAGE_EXPORT: '导出',
    cmp_healing_package_name: '包名',
    cmp_healing_package_import_time: '导入时间',
    cmp_healing_package_create_time: '创建时间',
    cmp_healing_package_flow_number: '流程数量',
    cmp_healing_package_operation: '操作',
    cmp_healing_package_solution_exist: '系统中已存在同名解决方案的流程、原子、API或全局参数，继续导入将覆盖同名内容',
    cmp_healing_package_import_confirm: '继续导入',
    cmp_healing_package_import_cancle: '放弃导入',
    cmp_healing_package_importexport: '诊断自愈包管理',
    cmp_healing_package_list: '包列表',
    cmp_healing_package_atom: '原子列表',
    cmp_healing_package_flow: '流程列表',
    cmp_healing_package_success_count: '成功个数',
    cmp_healing_package_fail_count: '失败个数',
    cmp_healing_package_import_count: '导入总数',
    cmp_healing_package_export_count: '导出总数',
    cmp_healing_package_atom_name: '原子名称',
    cmp_healing_package_flow_name: '流程名称',
    cmp_healing_package_api_name: 'API名称',
    cmp_healing_package_global_name: '全局参数名称',
    cmp_healing_package_result: '结果',
    cmp_healing_package_result_success: '成功',
    cmp_healing_package_result_fail: '失败',
    cmp_healing_package_result_success_massage: '导入成功',
    cmp_healing_package_import_atom_name: '原子',
    cmp_healing_package_import_flow_name: '流程',
    cmp_healing_package_import_api_name: 'API',
    cmp_healing_package_import_global_name: '全局参数',
    cmp_healing_package_problem: '错误详情',
    cmp_healing_package_cancel: '取消',
    cmp_healing_package_chose_solution: '选择组名',
    cmp_healing_package_all: '全部',
    cmp_healing_package_tip_import: '请导入诊断自愈包',
    cmp_healing_package_suffix_tip: '请导入zip格式流程包及p7s格式签名文件',
    cmp_healing_package_sign_and_package: '请导入流程包。',
    cmp_healing_package_tip_zip: '诊断包名只能包含数字、字母或者以下特殊字符_=:.\-',
    cmp_healing_package_import_tips: '只导入流程和原子时，二次导入同名流程包不更新原有原子相关配置、API和流程参数，只更新脚本内容和流程图。',
    cmp_healing_package_title_repeat: '重复',
    cmp_healing_package_title_import: '导入信息',
    cmp_healing_package_title_export: '导出信息',
    cmp_healing_package_size_message: '请保证导入的故障流程包大小小于10MB。',
    CMP_HEALING_PACKAGE_EXPORT_NOTE: '废弃原子和包含废弃原子的流程不会被导出',
    cmp_healing_flow_import_tip: '导入流程包会覆盖相同解决方案的原子、流程、配置API和全局参数。',
    cmp_healing_flow_create_tips_host_number: '执行主机数量限制：150台',
    cmp_healing_flow_import_package_tips: '选择流程包或流程包及其签名文件',

    // healing_task_zh_CN
    cmp_healing_task_flowname: '流程名称',
    cmp_healing_task_name: '任务名称',
    cmp_healing_task_copy: '复制',
    cmp_healing_task_flowDescription: '流程描述',
    cmp_healing_task_deploy_status: '任务状态',
    cmp_healing_task_type: '任务类型',
    cmp_healing_task_event: '触发事件',
    cmp_healing_task_alarmInfomation: '告警信息',
    cmp_healing_task_event_all: '全部',
    cmp_healing_task_event_manual: '手动执行',
    cmp_healing_task_event_timer: '定时任务',
    cmp_healing_task_event_alarm: '告警触发',
    cmp_healing_task_alarm_name: '告警名称',
    cmp_healing_task_alarm_id: '告警ID',
    cmp_healing_task_alarm_motype: '网元类型',
    cmp_healing_task_alarm_object_instance: '定位信息',
    cmp_healing_task_confirm: '确定',
    cmp_healing_task_noInfo: '无告警',
    cmp_healing_task_alarm_info: '点击查看告警信息',
    cmp_healing_task_alarminfo_tips: '告警信息详情',
    cmp_healing_task_alarm_metrics: '指标',
    cmp_healing_task_alarm_content: '内容',
    cmp_healing_task_timeout: '超时时间',
    cmp_healing_task_start_success: '任务启动成功，请跳转执行历史查看结果',
    cmp_healing_task_deploy_success: '部署任务成功',
    cmp_healing_task_undeploy_success: '去部署任务成功',
    cmp_healing_task_starttime: '开始执行时间',
    cmp_healing_task_endtime: '结束执行时间',
    cmp_healing_task_status: '执行状态',
    cmp_healing_task_save_all: '保存全部',
    cmp_healing_task_status_all: '全部',
    cmp_healing_task_status_unexecute: '未执行',
    cmp_healing_task_status_executing: '执行中',
    cmp_healing_task_status_paused: '待审核',
    cmp_healing_task_status_success: '执行成功',
    cmp_healing_task_status_failed: '执行失败',
    cmp_healing_task_status_canceled: '审核不通过',
    cmp_healing_task_result: '执行结果',
    cmp_healing_task_result_diagSuccess: '发现故障：',
    cmp_healing_task_result_healSuccess: '自愈故障：',
    cmp_healing_task_result_diagSuccess_nocolon: '发现故障',
    cmp_healing_task_result_healSuccess_nocolon: '自愈故障',
    cmp_healing_task_result_normal: '执行成功',
    cmp_healing_task_review_before_execute: '流程必须审核通过才能通过任务执行',
    cmp_healing_task_operation: '操作',
    cmp_healing_task_history: '执行详情',
    cmp_healing_task_history_atom_steps: '执行步骤',
    cmp_healing_task_step: '执行步骤',
    cmp_healing_task_result_toconfirm: '待审核',
    cmp_healing_task_atom: '原子维度',
    cmp_healing_task_host: '主机维度',
    cmp_healing_task_chart_tip: '数据不合法',
    cmp_healing_task_flow_trace: '流程轨迹',
    cmp_healing_task_no_result: '无结果',
    cmp_healing_task_log_step: '步骤名称',
    cmp_healing_task_log_date: '执行时间',
    cmp_healing_task_log_details: '错误详情',
    cmp_healing_task_log_stack: '错误详情',
    cmp_healing_task_flowname_colon: '流程名称：',
    cmp_healing_task_flowname_pure: '流程名称',
    cmp_healing_task_event_colon: '触发事件：',
    cmp_healing_task_starttime_colon: '开始执行时间：',
    cmp_healing_task_status_colon: '执行状态：',
    cmp_healing_task_status_ready: '就绪',
    cmp_healing_task_status_activated: '已激活',
    cmp_healing_task_flow_step_log: '流程执行步骤',
    cmp_healing_task_exception: '发生异常',
    cmp_healing_task_error: '错误详情',
    cmp_healing_task_timeout_help: '超时帮助',
    cmp_healing_task_now_less_than: '开始时间不能大于结束时间',
    cmp_healing_task_executor: '启动人',
    cmp_healing_task_create: '新建任务',
    cmp_ihealing_advice_timeout_head: '主机执行超时排查指导：',
    cmp_ihealing_advice_timeout_content1: '1. 检查主机是否离线，通过作业控制台“主机和分组”界面查看。',
    cmp_ihealing_advice_timeout_content2: '修复建议：重启插件进程。',
    cmp_ihealing_advice_timeout_content3: '2. 检查服务端是否存在时间跳变导致nats服务冻结客户端主机。',
    cmp_ihealing_advice_timeout_content4: '修复建议：重启nats。',
    cmp_ihealing_advice_timeout_content5: '3. 检查客户端主机和nats服务端时间是否同步。',
    cmp_ihealing_advice_timeout_content6: '修复建议：系统配置ntp时钟同步。',
    cmp_ihealing_advice_timeout_content7: '4. 检查客户端主机作业控制台插件进程是否正常运行。',
    cmp_ihealing_advice_timeout_content8: '修复建议：查看debug日志是否有异常，重启插件进程。',
    cmp_ihealing_advice_timeout_content9: '5. 检查插件的debug日志，是否收到nats消息，查找日志“Received topic ”。',
    cmp_ihealing_advice_timeout_content10: '修复建议：重启插件进程。',
    cmp_ihealing_advice_timeout_content11: '6. 检查插件的debug日志，是否存在“process execute timeout”关键字。',
    cmp_ihealing_advice_timeout_content12: '修复建议：排查执行脚本是否存在阻塞操作。',
    cmp_ihealing_advice_timeout_content13: '7. 检查cmp服务端管控平台depot_run.log日志中是否存在错误信息。',
    cmp_ihealing_advice_timeout_content14: '8. 客户端插件日志存放位置${UNIAGENT_HOME}/agent_plugins/workbench/log/debug。',
    cmp_ihealing_advice_timeout_content15: '9. cmp服务端管控平台日志存放位置${APP_ROOT_LOG}/dvcontrollerservice-*。',
    cmp_healing_task_search: '执行结果：',
    cmp_healing_task_search_type_all: '全部',
    cmp_healing_task_search_type_fault: '发现故障',
    cmp_healing_task_execute_host_list: '{0}的执行主机列表',
    cmp_healing_task_execute_atom_list: '在{0}执行的原子列表',
    cmp_healing_task_display_diag_host: '只显示发现故障原子的主机：',
    cmp_healing_task_display_diag_atom: '只显示发现故障的原子：',
    cmp_healing_task_display_atom_details: '只显示过滤后的原子详情信息：',
    cmp_healing_task_host_list: '主机列表',
    cmp_healing_task_atom_detail: '原子详情',
    cmp_healing_task_result_detail: '详情',
    cmp_healing_task_result_chart: '图表',
    cmp_healing_task_result_nochart: '未检测到合法的画图数据',
    cmp_healing_task_download: '下载',
    cmp_healing_task_download_begin: '开始下载',
    cmp_healing_task_download_console: '下载执行详情',
    cmp_healing_task_download_file: '下载文件',
    cmp_healing_task_download_process: '下载进度',
    cmp_healing_task_download_timeout: '已超时，是否继续下载？',
    cmp_healing_task_download_flow: '正在下载流程{0}的文件',
    cmp_healing_task_download_atom: '正在下载原子{0}的文件',
    cmp_healing_task_download_ip: '正在下载主机{0}的文件',
    cmp_healing_task_download_total: '下载总进度：',
    cmp_healing_task_download_type: '选择下载类型',
    cmp_healing_task_download_info1: '下载类型有两种：',
    cmp_healing_task_download_ignore: '下载过程中以下原子或主机下载失败，如果忽略则只会下载成功的原子或主机，不忽略则停止下载',
    cmp_healing_task_download_packaging: '打包中',
    cmp_healing_task_download_ignore_yes: '忽略',
    cmp_healing_task_download_ignore_no: '不忽略',
    cmp_healing_delete_flow_task_info: '该流程历史删除后不可恢复。请确定是否删除流程?',
    cmp_healing_task_batch_delete: '批量删除',
    cmp_healing_flow_deploy_info_notask: '没有选中的历史',
    HEALING_CODE_1: '下载失败',
    HEALING_CODE_2: '下载超时',
    HEALING_CODE_201: '读取文件失败',
    HEALING_CODE_202: '脚本执行失败',
    HEALING_CODE_203: '管控平台异常',
    HEALING_CODE_204: '管控平台异常',
    HEALING_CODE_205: '管控平台异常',
    HEALING_CODE_207: '文件不存在',
    HEALING_CODE_208: '文件无读取权限',
    HEALING_CODE_209: '文件异常，请确认文件存在且不是文件夹',
    HEALING_CODE_210: '文件大小超出上限',
    HEALING_CODE_211: '文件为空文件',
    HEALING_CODE_212: '管控平台异常，复制文件失败，1、共享磁盘或者业务磁盘满。2、没权限。',
    HEALING_CODE_213: '管控平台异常，更改文件权限失败',
    HEALING_CODE_214: '管控平台异常，文件传输失败',
    HEALING_CODE_215: '管控平台参数异常',
    HEALING_CODE_216: '管控平台异常，执行命令失败',
    HEALING_CODE_217: '管控平台异常，连接zookeeper失败',
    HEALING_CODE_218: '管控平台异常，下载脚本超时',
    HEALING_CODE_10010021: '文件大小超出上限',
    HEALING_CODE_10010022: '文件大小超出上限',
    HEALING_CODE_10010023: '管控平台异常，文件读写异常。请检查权限。',
    HEALING_CODE_10010006: '文件共享盘读写异常。',
    HEALING_CODE_10010005: 'Vsearch异常。',
    HEALING_CODE_10010004: 'Vsearch异常。',
    HEALING_CODE_10010003: 'Vsearch异常。',
    HEALING_CODE_10010002: '管控平台异常，接口参数校验失败。',
    HEALING_CODE_10010001: '数据库数据不完整，被损坏。',
    HEALING_CODE_10010007: '管控平台异常，MD5文件校验异常。',
    HEALING_CODE_10010008: '管控平台异常，Zookeeper分布式锁异常。',
    HEALING_CODE_10010009: '管控平台异常，Netty通道异常。',
    HEALING_CODE_10010020: '管控平台异常，下载失败。',
    HEALING_CODE_10010030: '管控平台异常。',
    HEALING_CODE_10010031: '管控平台异常。',
    HEALING_CODE_10010040: '管控平台异常。',
    HEALING_CODE_10010050: '管控平台异常。',
    HEALING_CODE_10010060: '管控平台异常。',
    HEALING_CODE_10010070: '管控平台异常。',
    HEALING_CODE_10010080: '管控平台异常。',
    HEALING_CODE_10010090: '管控平台异常。',
    cmp_basic_confirm: '确定',
    cmp_basic_cancel: '取消',

    cmp_workbench_select_host_ip: '按IP选择',
    cmp_workbench_select_host_solution: '按解决方案分组选择',
    cmp_workbench_select_host_solutions: '按解决方案分组选择',
    cmp_workbench_select_host_group: '按自定义分组选择',
    cmp_workbench_select_host_groupss: '按自定义分组选择',
    cmp_workbench_select_host_status: '主机状态：',
    cmp_workbench_select_host_filter: '通过IP、主机名进行过滤',
    cmp_workbench_select_host_status_all: '全部',
    cmp_workbench_select_host_status_online: '在线',
    cmp_workbench_select_host_status_offline: '离线',
    cmp_workbench_select_host_type: '主机类型',
    cmp_workbench_select_host_access: '是否接入拓扑',
    cmp_workbench_select_host_type_vm: '虚拟机',
    cmp_workbench_select_host_type_docker: '容器',
    cmp_workbench_select_host_access_yes: '已接入',
    cmp_workbench_select_host_access_no: '未接入',
    cmp_workbench_select_host_name: '主机名',
    cmp_workbench_select_host_status1: '状态',
    cmp_workbench_select_host_list: '{0}主机列表',
    cmp_workbench_select_host_select_ip: '选择主机',
    cmp_workbench_select_host_select_group: '选择分组',
    cmp_workbench_select_host_add: '添加',
    cmp_workbench_select_host_cancle: '取消',
    cmp_workbench_select_host_selectall: '全选',
    cmp_workbench_select_host_clean: '清空',
    cmp_workbench_select_host_selectall_tips: '选择当前分组所有主机',
    cmp_workbench_select_host_clean_tips: '清空当前分组所有主机',

    cmp_workbench_fast_script_ip_list: 'IP列表',
    cmp_workbench_fast_script_group_list: '自定义分组列表',
    cmp_workbench_fast_script_solution_list: '解决方案分组列表',
    cmp_workbench_fast_script_host_name: '主机名',
    cmp_workbench_fast_script_host_namethead: '主机名',
    cmp_workbench_fast_script_host_status: '状态',
    cmp_workbench_fast_script_host_operate: '操作',
    cmp_workbench_fast_script_host_delete: '删除',
    cmp_workbench_fast_script_group_name: '分组名称',
    cmp_workbench_fast_script_solution_name: '解决方案名称',
    cmp_workbench_fast_script_group_path: '路径',
    cmp_workbench_fast_script_host_name_ip: 'IP地址',
    cmp_workbench_fast_download_file_failed: '下载文件失败！',
    cmp_workbench_fast_script_input_passwd: '请输入登录用户密码',
    cmp_healing_flow_para_string_script: '脚本型',
    cmp_healing_flow_para_enumrated_tips: '枚举型参数必须以分号或逗号进行分隔。',
    cmp_healing_flow_para_select_initparam_tips: '请选择调用初始化参数的脚本。',
    cmp_workbench_systemconfiguration: '系统管理',
    cmp_healing_flow_para_string_enum: '枚举型',
    cmp_healing_flow_suspend: '暂停',
    cmp_healing_param_select_user_filter: '通过用户名进行过滤',
    cmp_healing_param_select_group_filter: '通过用户组名进行过滤',
    cmp_healing_param_user_type: '用户类型',
    cmp_healing_param_notify_user: '通知用户',
    cmp_healing_param_operation_user: '运维用户',
    cmp_healing_flow_email_addr: '邮箱地址',
    cmp_healing_flow_resume_info: '请确定是否继续执行流程?',
    cmp_healing_flow_resume: '恢复',
    cmp_healing_flow_input: '输入',
    cmp_healing_flow_executestatus_suspend: '暂停中',
    cmp_healing_flow_executestatus_input: '待输入',
    cmp_healing_flow_node_properties: '节点参数',
    cmp_healing_flow_node_name: '节点名称',
    cmp_healing_flow_pauseNodeUnit_second: '秒',
    cmp_healing_flow_pauseNodeUnit_minute: '分',
    cmp_healing_flow_search_category: '流程类型：',
    cmp_healing_flow_category: '流程类型',
    cmp_healing_flow_version: '版本号',
    cmp_healing_solution_version: '版本号：',
    cmp_healing_flow_category_colon: '流程类型：',
    cmp_healing_flow_list: '列表展示',
    cmp_healing_flow_card: '卡片展示',
    cmp_healing_flow_category_all: '全部',
    cmp_healing_flow_category_other: '其他',
    cmp_healing_flow_category_healthinspection: '健康巡检',
    cmp_healing_flow_category_collectinfo: '信息采集',
    cmp_healing_flow_category_specificationcheck: '规范检查',
    cmp_healing_flow_category_changeexecution: '变更执行',
    cmp_healing_flow_category_faultrecovery: '故障恢复',
    cmp_healing_flow_category_faultdiag: '故障诊断',
    cmp_healing_flow_category_backup: '备份',
    cmp_healing_flow_set_category: '请填写流程类型',
    cmp_healing_flow_write_chose_category: '请填写或者选择流程类型',
    cmp_healing_task_view_no: '打开文件的格式不匹配，仅支持打开HTML文件',
    cmp_healing_task_view: '查看文件',
    cmp_healing_task_flow_step: ' 2_流程步骤',
    cmp_healing_task_flow_para: ' 1_流程参数',
    cmp_healing_task_flow_para_name: ' 流程参数名称',
    cmp_healing_task_flow_para_value: ' 流程参数值',
    cmp_healing_task_download_again: '重新下载',
    cmp_healing_task_download_failed_again: '下载失败，重新下载:{0}?',
    cmp_healing_task_downloading: ' 下载中',
    cmp_healing_task_download_file_quick: ' 下载文件（快速下载）',
    cmp_healing_task_download_file_newly: ' 下载文件（全新下载）',
    cmp_healing_task_download_info2: '1）下载执行详情：执行详情指原子脚本在控制台的输出，2）下载文件：文件指原子脚本中指定的文件',
    cmp_healing_task_download_error: '下载过程中以下原子或主机下载失败',
    cmp_healing_task_download_opening: '打开中',
    cmp_healing_task_view_no: '文件的格式不正确，仅支持打开HTML，JS，PDF，TXT，LOG等格式的文件',
    cmp_healing_task_view: '查看文件',
    cmp_healing_task_query: '根据ID查询任务发生错误',
    cmp_healing_task_view_failed: '下载失败',
    cmp_healing_task_view_loading: '下载中',
    cmp_healing_task_view_none: '无可查看文件',
    cmp_healing_task_view_status: '更新查看状态出错',
    cmp_healing_task_view_status_zip: '查询打包状态出错',
    cmp_healing_task_view_status_download: '查询下载状态出错',
    cmp_healing_task_view_download_failed: '下载失败，失败原因如下：1)确认文件是否存在 2)确认打开的文件不是文件夹 3)确认uniagent用户是否有读取权限',
    cmp_healing_task_view_zip: '文件打包发生错误',
    HEALING_CODE_219: '解压文件失败',
    HEALING_CODE_220: ' 查找文件失败',
    cmp_healing_package_ip: ' IP',
    cmp_healing_api_refer_maxnumber: '参数类型为IP列表时，ip数量不能超过1000，参数名：{0}',
    cmp_healing_flow_list_title: '流程库',
    IHEALING_VALIDATOR_PARA_VALUE: ' 输入不能包含特殊字符以及相对路径（../），其中特殊字符为#%&+><)(',
    IHEALING_VALIDATOR_ENUMPARA_VALUE: ' 输入不能包含特殊字符以及相对路径（../），不能以",;"结尾，其中特殊字符为#%&+=>?<()',
    IHEALING_VALIDATOR_LINAGE_VALUE: ' 输入不能包含特殊字符以及相对路径（../），其中特殊字符为#%&+=|>;<?()',
    cmp_healing_flow_search_category: ' 流程类型：',
    cmp_healing_flow_category: ' 流程类型',
    cmp_healing_flow_category_colon: ' 流程类型：',
    cmp_healing_flow_list: ' 列表展示',
    cmp_healing_flow_card: ' 卡片展示',
    cmp_healing_flow_category_all: ' 全部',
    cmp_healing_flow_category_other: ' 其他',
    cmp_healing_flow_category_healthinspection: ' 健康巡检',
    cmp_healing_flow_category_collectinfo: ' 信息采集',
    cmp_healing_flow_category_specificationcheck: ' 规范检查',
    cmp_healing_flow_category_changeexecution: ' 变更执行',
    cmp_healing_flow_category_faultrecovery: ' 故障恢复',
    cmp_healing_flow_category_faultdiag: ' 故障诊断',
    cmp_healing_flow_category_backup: ' 备份',
    cmp_healing_flow_executestatus_downloadfailed: '下载失败',
    cmp_healing_flow_executestatus_suspend: ' 暂停中',
    cmp_healing_flow_node_properties: ' 节点参数',
    cmp_healing_flow_node_name: ' 节点名称',
    cmp_healing_flow_pauseNodeUnit_second: ' 秒',
    cmp_healing_flow_pauseNodeUnit_minute: ' 分',
    cmp_healing_flow_resume: ' 恢复',
    cmp_healing_flow_import: ' 导入',
    cmp_healing_flow_event_alarm_id: ' 告警ID',
    cmp_healing_flow_event_alarm_motype: ' 网元类型',
    cmp_healing_flow_event_alarm_measureUnit: ' 测量单元',
    cmp_healing_flow_event_alarm_metricName: ' 指标名称',
    cmp_healing_flow_event_alarm_threshold: ' 阈值',
    cmp_healing_flow_event_alarm_operation: ' 操作',
    cmp_healing_flow_task_list: '流程任务',
    cmp_healing_flow_task_create: '创建任务',
    cmp_healing_flow_task_choose_flow: '选择流程',
    cmp_healing_flow_task_choose_flow_invalid: '创建定时任务需要选择触发方式为定时执行的流程',
    cmp_healing_flow_task_replace_flow: '编辑已有任务时禁止更换流程，如希望对其他流程创建任务请新建',
    cmp_healing_flow_task_info: '任务信息',
    cmp_healing_flow_resume_info: ' 请确定是否继续执行流程?',
    cmp_healing_flow_format: ' 检查',
    cmp_healing_flow_chose_atom_edit: '查看原子',
    cmp_healing_flow_last_atom_result: ' 从上一个原子的执行结果中获取',
    cmp_healing_flow_select_from_flow_sql: ' sql结果引用',
    cmp_healing_flow_excuted: ' 已执行',
    cmp_healing_flow_times: ' 次',
    cmp_healing_flow_solution_colon: ' 解决方案：',
    cmp_healing_flow_imgPath_colon: ' 流程背景图片：',
    cmp_healing_flow_groupname_colon: ' 组名：',
    cmp_healing_flow_groupname: ' 组名',
    cmp_healing_flow_condition: ' 排他条件',
    cmp_healing_flow_parallel: ' 并行条件',
    cmp_healing_flow_inclusive: ' 包容条件',
    cmp_healing_flow_suspend: ' 暂停',
    cmp_healing_flow_terminate: '终止',
    cmp_healing_flow_terminate_confirm: '确认是否终止流程？',
    cmp_healing_flow_stop: '确认',
    cmp_healing_flow_stop_confirm: '流程中存在正在执行的原子，系统会等待当前原子执行完成后再停止流程。流程停止后，建议等待当前原子执行完成后再触发执行流程。确认强制停止流程？',
    cmp_healing_flow_set_category: ' 请填写流程类型',
    cmp_healing_flow_set_pathErr: ' 有连接线连接错误',
    cmp_healing_flow_write_chose_category: ' 请填写或者选择流程类型',
    cmp_healing_flow_config_timer: '配置定时任务模板',
    cmp_healing_flow_import_img: '上传',
    cmp_healing_flow_import_img_success: '上传成功',
    cmp_healing_flow_import_img_tips: '1_ 支持jpg、png格式的图片文件，大小2M以内。2_ 尺寸比例建议2:1，建议尺寸不小于300px * 150px,不超过4000px * 4000px。3_ 文件名只能由字母，数字，下划线 "_" 和横杠 "-" 组成。4_ 文件名长度不能超过60个字符。',
    cmp_healing_flow_import_img_isDelete: '已有自定义图片，是否删除？',
    cmp_healing_flow_import_img_type_error: '文件扩展名不正确，允许的扩展名为JPG、PNG',
    cmp_healing_flow_import_img_size_error: '尺寸不合适，建议比例为2:1',
    cmp_healing_flow_import_imgfile_size_error: '文件大小超过2M',
    cmp_healing_flow_import_imgfile_name_error: '文件名只能由字母，数字，下划线 "_" 和横杠 "-" 组成。文件名长度不能超过60个字符。',
    cmp_healing_flow_flowParameter_change_child_name: ' 子参数名称',
    cmp_healing_flow_flowParameter_required: ' 是否必填',
    cmp_healing_flow_flowParameter_visible: ' 是否展示',
    cmp_healing_flow_sure_name: ' 确定执行流程： {0} ?',
    cmp_healing_flow_sure_import: ' 导入包的组名已存在，可能会覆盖同名称的流程，确定继续导入?',
    cmp_healing_flow_description_colon: ' 流程描述：',
    cmp_healing_flow_flowParameter_validation_params_enum_maxlength: ' 枚举型参数的内嵌参数数量超出限制(100)，参数名：',
    cmp_healing_flow_flowParameter_validation_params_enum_value: ' 枚举型流程参数中含有非法字符串参数名：',
    cmp_healing_flow_flowParameter_validation_params_enum_onlySpace: '枚举型流程参数不能包含空枚举值，参数名：',
    cmp_healing_flow_flowParameter_validation_params_enum_valueLength: ' 枚举型流程参数的内嵌参数长度超过限制（108），参数名：',
    cmp_healing_flow_flowParameter_validation_params_enum_sameValue: ' 枚举型流程参数中不能含有重复参数，参数名：',
    cmp_healing_flow_flowParameter_validation_params_map_maxlength: ' key-value型流程参数的内嵌参数数量超出限制，参数名：',
    cmp_healing_flow_flowParameter_validation_params_map_value: ' key-value型流程参数的内嵌参数必须遵守key:value的格式，参数名：',
    cmp_healing_flow_flowParameter_validation_params_map_sameValue: ' key-value型流程参数的内嵌参数不能含有重复的key值，参数名：',
    cmp_healing_flow_flowParameter_validation_params_map_valueLength: ' key-value型流程参数的内嵌参数长度超过限制（108），参数名：',
    cmp_healing_flow_flowParameter_validation_params_patch_maxNum: '附件数量超出限制（5）',
    cmp_healing_flow_flowParameter_validation_params_iptree_maxlength: ' IP列表型参数ip数量超出限制(20)，参数名：',
    cmp_healing_flow_flowParameter_validation_params_iptree_minlength: ' IP列表型参数至少选择一个IP，参数名：',
    cmp_healing_flow_flowParameter_validation_params_enum_prefix_tip: '枚举型参数必须以","、";"或"|"进行分隔，其中","或";"分隔的参数支持多选，"|"分隔的参数只支持单选。',
    cmp_healing_flow_flowParameter_validation_params_enum_prefix_default_tip: '填写枚举型参数的默认值，多个值以逗号（\,）或分号（\;）隔开',
    cmp_healing_flow_flowParameter_validation_params_map_prefix_tip: ' key-value型流程参数的内嵌参数必须遵守key:value的格式，多个参数以","、";"或"|"进行分隔，其中","或";"分隔的参数支持多选，"|"分隔的参数只支持单选。',
    cmp_healing_flow_flowParameter_validation_params_map_prefix_default_tip: '填写key-value型参数的默认的key，多个值以逗号（\,）或分号（\;）隔开',
    cmp_healing_flow_flowParameter_validation_params_scripttype_prefix_tip: ' 请选择包含初始化函数的原子',
    cmp_healing_flow_flowParameter_validation_params_json_prefix_tip: ' 级联型参数必须输入指定格式的json字符串。第二层级联的枚举型和key-value型参数可以通过配置selectMode属性控制单选（single）或多选（multi）。',
    cmp_healing_flow_flowParameter_validation_params_json_isjson: ' 请输入json格式的字符串，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_format: ' 格式不正确，请仔细检查json字符串的格式',
    cmp_healing_flow_flowParameter_validation_params_json_isFirstArray: ' json格式最外层请输入json对象数组，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_paramsName: ' json字符串中请输入键值对‘paramsName’，其值不能为空，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_paramList: ' json字符串中请输入键值对‘paramList’，其值为json对象数组，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_child: ' 第一级json数组内请输入json对象，参数名：',
    cmp_healing_flow_flowParameter_validation_params_ne_maxlength: '所有网元实例id总长度不能超过2048，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_paramName: ' paramList的数组内每个值内请输入键值对"paramName"，其值不能为空，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_paramName_length: ' "paramName"的值长度不能超过128，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_description: ' paramList的数组内每个值内请输入键值对"description"，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_type: ' paramList的数组内每个值内请输入键值对"type"，其值不能为空，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_datetype: ' 请输入格式正确的日期型参数,日期格式：yyyy-MM-dd HH:mm:ss  如：2020-01-01 00:00:00 参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_iptree: ' 级联型参数包含ip.tree类型的参数时，ip.tree类型的参数值必须为空，参数名：',
    cmp_healing_flow_flowParameter_validation_params_paramList_value: ' paramList的数组内每个值内请输入键值对"value"，参数名：',
    cmp_healing_flow_flowParameter_validation_params_paramList_enumValue: ' paramList的数组内每个值内请输入键值对"value"，enumrate型参数的值为string数组，key.value型参数的值为json数组，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_istype: ' paramList的数组内每个值内的键值对"type"，其值只能为"string"，"datetype"，"ENUMTYPE"，"key.value"，"ip.tree"其中之一，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_paramList_sameName: ' paramList的数组内的paramName的值不能重复，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_value_chinese: ' paramList的数组内每个值内的键值对"value"，其值不能包含中文，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_paramName_format: ' 存在非法字符，"paramsName"、"paramName"的值只能由字母、数字或下划线组成(首字符只能用字母和下划线)，参数名：',
    cmp_healing_flow_flowParameter_validation_params_json_value_enum: ' paramList的数组内每个值中的键值对"value"，若是enumrate类型，则数组内的值不能重复，若是key.value类型，则数组内的值的键不能重复，参数名：',
    cmp_healing_flow_flowParameter_validation_params_value_none: ' 参数值不能为空，请输入，参数名：',
    cmp_healing_flow_flowParameter_validation_params_value: ' 输入不能包含中文、特殊字符以及相对路径（../），其中特殊字符为#%&+=><?\"()，参数名：',
    cmp_healing_flow_flowParameter_validation_params_value_maxlength: ' 参数的字符串长度超出取值范围，长度必须小于等于8000，参数名：',
    cmp_healing_flow_flowParameter_validation_length: ' 流程参数的数量不能超过30个，其中一个级联型参数应记作二级参数数量最多的参数数量',
    cmp_healing_flow_flow_query_node_validation: ' 性能查询工具、告警查询工具、SQL原子的数量合计不能超过',
    cmp_healing_flow_para_string_enum: ' 枚举型',
    cmp_healing_flow_para_string_map: ' Key-Value型',
    cmp_healing_flow_para_string_iptree: ' IP列表型',
    cmp_healing_flow_para_string_dbaccount: ' 数据库账号类型',
    cmp_healing_flow_para_string_scripttype: ' 脚本型',
    cmp_healing_flow_chose_ip_pure: ' 选择IP',
    cmp_healing_flow_chose_ne_instance_title: '选择网元实例',
    cmp_healing_flow_choose_db_account: '选择数据库账号',
    cmp_healing_flow_choose_notify_object: '选择通知对象',
    cmp_healing_flow_choose_notify_object_by_user: '按用户选择',
    cmp_healing_flow_choose_notify_object_by_group: '按用户组选择',
    cmp_healing_flow_edit_json: ' 编辑json',
    cmp_healing_flow_para_string_json: ' 级联型',
    cmp_healing_flow_deploy_tip: '流程执行前需要先启用，未启用的定时任务或告警触发流程即使满足触发条件也不会自动执行。请检查是否有需要执行的流程尚未启用。',
    cmp_healing_session_backhome: '确定',
    cmp_healing_notification: '注意事项',
    cmp_healing_info_title: '流程编排的定位是在云场景下，针对处理流程相对固化的故障或运维场景，将故障的告警和处理流程关联，告警发生时按照提前预置好的故障处理流程自动执行，实现分钟级故障诊断、自愈和运维场景。其约束如下：',
    cmp_healing_info_content1: '1. 流程编排仅提供下发命令或脚本的能力，业务需要对命令或脚本的内容正确性、是否可重复执行、业务运行环境、执行结果和执行脚本间传递用户填写的密码明文场景的安全性负责，脚本中不能包含敏感信息。除密码型参数外，其他功能场景禁止输入密码明文等敏感信息。',
    cmp_healing_info_content2: '2. 命令或脚本执行失败时，流程编排不提供自动重新执行的能力，只提供手工触发重新执行的能力。',
    cmp_healing_info_content3: '3. 用户对流程编排Portal的操作权限，由DigitalView系统管理员用户来分配；脚本在业务运行环境上的运行权限，由业务来分配。',
    cmp_healing_info_content4: '4. 业务的启停、升级、安装场景，建议使用DigitalFoundry的应用生命周期管理功能来完成，不建议使用流程编排。',
    cmp_healing_info_content5: '5. 对于某些危险命令（如删除用户、删除目录），建议进行高危命令管理；针对高危命令，请谨慎授权。',
  },
};
if ($) {
  I18n = $.extend(true, I18n_Exisit, I18n);
}