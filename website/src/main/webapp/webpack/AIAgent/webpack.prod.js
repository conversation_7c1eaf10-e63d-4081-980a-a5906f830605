const path = require('path');
const _ROOTPATH = process.cwd();
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const { basicRules, resolve, externals, ReplaceCssPlugin } = require('../webpack.basic');
const packageInfo = require('../../package.json');
const appName = path.basename(__dirname);
const rootPath = `/${packageInfo.name.split('/')[1]}/build/${appName}`;
const buildOutPath = path.resolve(_ROOTPATH, `./build/${appName}`);

const appConfig = {
  mode: 'production', // "production" | "development" | "none"
  entry: {
    app_main: [path.resolve(_ROOTPATH, `./src/apps/${appName}/entry`)],
  },
  output: {
    path: buildOutPath,
    publicPath: `${rootPath}/`, // 输出解析文件的目录，url 相对于 HTML 页面
    chunkFilename: '[name].bundle.js',
    library: `${packageInfo.name}_${appName}`,
    libraryTarget: 'umd', // 通用模块定义
  },
  optimization: {
    splitChunks: {
      chunks: 'all',
      minChunks: 2,
    },
  },
  module: {
    rules: basicRules, // 关于模块配置
  },
  resolve,
  context: _ROOTPATH,
  target: 'web', // bundle 应该运行的环境
  node: {
    fs: 'empty',
    module: 'empty',
  },
  plugins: [
    new MiniCssExtractPlugin({
      chunkFilename: '[name].[id].css',
      filename: '[name].css',
    }),
    new ReplaceCssPlugin(['app_main']),
    new webpack.ProvidePlugin({
      Axios: 'axios',
    }),
  ],
  externals,
};

const piu4AIAgentConfig = {
  mode: 'production', // "production" | "development" | "none"
  entry: {
    piu: [path.resolve(_ROOTPATH, './src/apps/piu4AIAgent/entry')],
  },
  output: {
    path: buildOutPath,
    publicPath: `${rootPath}/`, // 输出解析文件的目录，url 相对于 HTML 页面
    filename: '[name].js',
    chunkFilename: '[name].[contenthash:8].js',
    libraryTarget: 'umd', // 通用模块定义
  },
  optimization: {
    splitChunks: {
      chunks: 'all',
      minChunks: 2,
    },
  },
  module: {
    rules: basicRules, // 关于模块配置
  },
  resolve,
  context: _ROOTPATH,
  target: 'web', // bundle 应该运行的环境
  node: {
    fs: 'empty',
    module: 'empty',
  },
  plugins: [
    new MiniCssExtractPlugin({
      chunkFilename: '[name].[contenthash:8].css',
      filename: '[name].css',
    }),
  ],
  externals,
};

module.exports = [appConfig, piu4AIAgentConfig];
