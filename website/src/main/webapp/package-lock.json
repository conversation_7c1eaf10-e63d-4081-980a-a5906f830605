{"name": "@hw-dv/dvsolutionwebsite", "version": "1.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "@hw-dv/dvsolutionwebsite", "version": "1.0.0", "license": "ISC", "dependencies": {"@baize/weselector": "0.1.6", "@digitalview/hw-dom": "1.0.14"}, "devDependencies": {"@babel/core": "7.9.0", "@babel/plugin-proposal-class-properties": "7.4.4", "@babel/plugin-syntax-dynamic-import": "7.2.0", "@babel/plugin-transform-react-jsx": "7.18.6", "@babel/plugin-transform-runtime": "7.4.4", "@babel/preset-env": "7.4.5", "@babel/preset-react": "7.9.4", "@cloudsop/babel-plugin-eviewui-import": "0.0.3", "@cloudsop/horizon": "0.0.63", "babel-loader": "8.1.0", "css-loader": "4.2.1", "html-webpack-plugin": "4.4.1", "style-loader": "1.2.1", "url-loader": "4.1.0", "webpack": "4.44.1", "webpack-cli": "3.3.12", "webpack-dev-server": "3.11.0"}}, "node_modules/@babel/code-frame": {"version": "7.22.13", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/code-frame/-/code-frame-7.22.13.tgz", "integrity": "sha512-XktuhWlJ5g+3TJXc5upd9Ks1HutSArik6jf2eAjYFyIOf4ej3RN+184cZbzDvbPnuTJIUhPKKJE3cIsYTiAT3w==", "dev": true, "license": "MIT", "dependencies": {"@babel/highlight": "^7.22.13", "chalk": "^2.4.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.22.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/compat-data/-/compat-data-7.22.9.tgz", "integrity": "sha1-cc2wChzjoynOTL7DpE+f7zVmlzA=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/core/-/core-7.9.0.tgz", "integrity": "sha1-rJd7U4t34TL/cG87ik260JwDxW4=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.8.3", "@babel/generator": "^7.9.0", "@babel/helper-module-transforms": "^7.9.0", "@babel/helpers": "^7.9.0", "@babel/parser": "^7.9.0", "@babel/template": "^7.8.6", "@babel/traverse": "^7.9.0", "@babel/types": "^7.9.0", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.1", "json5": "^2.1.2", "lodash": "^4.17.13", "resolve": "^1.3.2", "semver": "^5.4.1", "source-map": "^0.5.0"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/generator": {"version": "7.22.10", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/generator/-/generator-7.22.10.tgz", "integrity": "sha1-ySJUNh85jhYGRaxYgxBpcHOCtyI=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.22.10", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17", "jsesc": "^2.5.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-annotate-as-pure": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.22.5.tgz", "integrity": "sha1-5/BnN7GX1YCgHt912X4si+mdOII=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-builder-binary-assignment-operator-visitor": {"version": "7.22.10", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.22.10.tgz", "integrity": "sha1-Vz5zWTfpnqdeoweItX61L6t0aMk=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.22.10"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.22.10", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-compilation-targets/-/helper-compilation-targets-7.22.10.tgz", "integrity": "sha1-AdZIu8Jd2I9RPYYu4N8nt9TmcCQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.22.9", "@babel/helper-validator-option": "^7.22.5", "browserslist": "^4.21.9", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-create-class-features-plugin": {"version": "7.22.11", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.22.11.tgz", "integrity": "sha512-y1grdYL4WzmUDBRGK0pDbIoFd7UZKoDurDzWEoNMYoj1EL+foGRQNyPWDcC+YyegN5y1DUsFFmzjGijB3nSVAQ==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-member-expression-to-functions": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-replace-supers": "^7.22.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-class-features-plugin/node_modules/semver": {"version": "6.3.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.22.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.22.9.tgz", "integrity": "sha1-nY5hqNk2b+ZhmPV8QFZWY94IJfY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "regexpu-core": "^5.3.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-regexp-features-plugin/node_modules/semver": {"version": "6.3.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-environment-visitor": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.5.tgz", "integrity": "sha1-8G3UG3wfROH42mxAVbQas6Cafpg=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-function-name": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-function-name/-/helper-function-name-7.22.5.tgz", "integrity": "sha1-7eMAgokFuxXlgsA3Fi+Z1Rg68b4=", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.22.5", "@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-hoist-variables": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz", "integrity": "sha1-wBoAfawFwIWRTo+2UrM521DYI7s=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-member-expression-to-functions": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.22.5.tgz", "integrity": "sha1-CnxWEXytM3L7+NL7S/j41koedrI=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.22.15", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-module-imports/-/helper-module-imports-7.22.15.tgz", "integrity": "sha512-0pYVBnDKZO2fnSPCrgM/6WMc7eS20Fbok+0r88fp+YtWVLZrp4CkafFGIp+W0VKw4a22sgebPT99y+FDNMdP4w==", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.22.15"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.22.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-module-transforms/-/helper-module-transforms-7.22.9.tgz", "integrity": "sha1-kt/LH7uyvGJSkCT3LZQqjJcUISk=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-module-imports": "^7.22.5", "@babel/helper-simple-access": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/helper-validator-identifier": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-optimise-call-expression": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.22.5.tgz", "integrity": "sha1-8hUxqcy/9kT90Va0B3wW/ww/YJ4=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-plugin-utils/-/helper-plugin-utils-7.22.5.tgz", "integrity": "sha1-3X7jc16KMTufewWnc9iS6I5tcpU=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-remap-async-to-generator": {"version": "7.22.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.22.9.tgz", "integrity": "sha1-U6JbdITnItfvucNQx1wDLUYo3oI=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-wrap-function": "^7.22.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-replace-supers": {"version": "7.22.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-replace-supers/-/helper-replace-supers-7.22.9.tgz", "integrity": "sha1-y9wn1tjRjNIsga5Ck3ZaXZr9B3k=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-member-expression-to-functions": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-simple-access": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-simple-access/-/helper-simple-access-7.22.5.tgz", "integrity": "sha1-STg1fcfXgrgO1tuwOg+6PSKx1d4=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.22.5.tgz", "integrity": "sha1-AH8VJAtXUcU3xA53q7TonuqqiEc=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-split-export-declaration": {"version": "7.22.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.6.tgz", "integrity": "sha1-MixhtzEMCZf+TDI5VWZ/GPzvuRw=", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-string-parser/-/helper-string-parser-7.22.5.tgz", "integrity": "sha1-Uz82RXolgUzx32SIUjrVR9eEqZ8=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.22.20", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.20.tgz", "integrity": "sha512-Y4O<PERSON>+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-validator-option/-/helper-validator-option-7.22.5.tgz", "integrity": "sha1-3lIAChWhd0E8gjT6Oor07oEC0Kw=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-wrap-function": {"version": "7.22.10", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helper-wrap-function/-/helper-wrap-function-7.22.10.tgz", "integrity": "sha1-2EXgQ4gO0LjBi9GUoSAFyxbS9hQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-function-name": "^7.22.5", "@babel/template": "^7.22.5", "@babel/types": "^7.22.10"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.22.11", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/helpers/-/helpers-7.22.11.tgz", "integrity": "sha512-vyOXC8PBWaGc5h7GMsNx68OH33cypkEDJCHvYVVgVbbxJDROYVtexSk0gK5iCF1xNjRIN2s8ai7hwkWDq5szWg==", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.22.5", "@babel/traverse": "^7.22.11", "@babel/types": "^7.22.11"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/highlight": {"version": "7.22.13", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/highlight/-/highlight-7.22.13.tgz", "integrity": "sha512-C/BaXcnnvBCmHTpz/VGZ8jgtE2aYlW4hxDhseJAWZb7gqGM/qtCK6iZUb0TyKFf7BOUsBH7Q7fkRsDRhg1XklQ==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.22.5", "chalk": "^2.4.2", "js-tokens": "^4.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.22.14", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/parser/-/parser-7.22.14.tgz", "integrity": "sha512-1KucTHgOvaw/LzCVrEOAyXkr9rQlp0A1HiHRYnSUE9dmb8PvPW7o5sscg+5169r54n3vGlbx6GevTE/Iw/P3AQ==", "dev": true, "license": "MIT", "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-proposal-async-generator-functions": {"version": "7.7.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-proposal-async-generator-functions/-/plugin-proposal-async-generator-functions-7.7.0.tgz", "integrity": "sha1-g+8tYERJa0wV2LSQTiIZ5tzMaXE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.7.0", "@babel/plugin-syntax-async-generators": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-class-properties": {"version": "7.4.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.4.4.tgz", "integrity": "sha1-k6ZIbu2G1TRSq5urNeNo6UYRmM4=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.4.4", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-json-strings": {"version": "7.18.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-proposal-json-strings/-/plugin-proposal-json-strings-7.18.6.tgz", "integrity": "sha1-foeIwYEcOTr/digX59vx69DAXws=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-json-strings": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-object-rest-spread": {"version": "7.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.9.0.tgz", "integrity": "sha1-oomTaZ/BPfFlmVNiaTliumsGHW8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-syntax-object-rest-spread": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-optional-catch-binding": {"version": "7.18.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-proposal-optional-catch-binding/-/plugin-proposal-optional-catch-binding-7.18.6.tgz", "integrity": "sha1-+UANDmo+qTup73CwnnLdbaY4oss=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-unicode-property-regex": {"version": "7.7.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-proposal-unicode-property-regex/-/plugin-proposal-unicode-property-regex-7.7.0.tgz", "integrity": "sha1-VJ/hcXob0KKn5jFjhByzfngXnV0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.7.0", "@babel/helper-plugin-utils": "^7.0.0"}, "engines": {"node": ">=4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-async-generators": {"version": "7.8.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "integrity": "sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-dynamic-import": {"version": "7.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.2.0.tgz", "integrity": "sha1-acFZ/69JmBIhYa2OvF5tH1XfhhI=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-json-strings": {"version": "7.8.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "integrity": "sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.22.5.tgz", "integrity": "sha1-praOhPt251n8O5PpAYdv+rvh2Rg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "integrity": "sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "integrity": "sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-arrow-functions": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.22.5.tgz", "integrity": "sha1-5bpWbQxYpbK6Kot5VFBkGVC3GVg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-to-generator": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.22.5.tgz", "integrity": "sha1-x6hfRORviVL20n/lfC7TzAhMN3U=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-remap-async-to-generator": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoped-functions": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.22.5.tgz", "integrity": "sha1-J5eAdb+uufpYbTy2Oj0wwd5YACQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoping": {"version": "7.22.10", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.22.10.tgz", "integrity": "sha1-iKHczDODiZ615mBTSnaiLs7mT6o=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-classes": {"version": "7.22.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-classes/-/plugin-transform-classes-7.22.6.tgz", "integrity": "sha1-4E19gE7VuFATESk9Gg5tQ+lMM2M=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-compilation-targets": "^7.22.6", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-optimise-call-expression": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-computed-properties": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.22.5.tgz", "integrity": "sha1-zR6ZS/nzFr0cLa/NAgY+wmG7OGk=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/template": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-destructuring": {"version": "7.22.10", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.22.10.tgz", "integrity": "sha1-OOInOBSljIELbDTqKTvklzxOteI=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-dotall-regex": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.22.5.tgz", "integrity": "sha1-27Tw5Fdm61ROGT+wDmWh3TsqQWU=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-duplicate-keys": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.22.5.tgz", "integrity": "sha1-tuZCjZQW9fC7oZxw0ebn4LiKsoU=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-exponentiation-operator": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.22.5.tgz", "integrity": "sha1-QCQyrVRKH5pIDahl/aJr5lPkj2o=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-for-of": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.22.5.tgz", "integrity": "sha1-qxuKIAqPmQE3r/mghPjeQJmrFz8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-function-name": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.22.5.tgz", "integrity": "sha1-k1GJr2iwGJjg1tmWWNtrFkIFwUM=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-literals": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-literals/-/plugin-transform-literals-7.22.5.tgz", "integrity": "sha1-6TQfS1oWeVJXbiPbjUNYSbHdeSA=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-member-expression-literals": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.22.5.tgz", "integrity": "sha1-T8yQUO3tmBpGg0fdN0U57T4Fje8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-amd": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.22.5.tgz", "integrity": "sha1-TgRfVdz5iv0A+FaRpo/AeAcE9SY=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-commonjs": {"version": "7.22.11", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.22.11.tgz", "integrity": "sha512-o2+bg7GDS60cJMgz9jWqRUsWkMzLCxp+jFDeDUT5sjRlAxcJWZ2ylNdI7QQ2+CH5hWu7OnN+Cv3htt7AkSf96g==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.22.9", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-simple-access": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-systemjs": {"version": "7.22.11", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.22.11.tgz", "integrity": "sha512-rIqHmHoMEOhI3VkVf5jQ15l539KrwhzqcBO6wdCNWPWc/JWt9ILNYNUssbRpeq0qWns8svuw8LnMNCvWBIJ8wA==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-module-transforms": "^7.22.9", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-umd": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.22.5.tgz", "integrity": "sha1-RpSuQKh7F0Xjd1tqf+lkADFdT5g=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.22.5.tgz", "integrity": "sha1-Z/4Y7ozgLVfIVRheJ+PclZsumR8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-new-target": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.22.5.tgz", "integrity": "sha1-GySKzqVM5E6gbf03JHugifz5dY0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-super": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.22.5.tgz", "integrity": "sha1-eUqNL8tdCDWvciFzwanXBPROIYw=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-replace-supers": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-parameters": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.22.5.tgz", "integrity": "sha1-w1Qt08ObQsgGmTbkhxeo0XnWOhg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-property-literals": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.22.5.tgz", "integrity": "sha1-td2r1zpPfybNDiD120gpC4hzJ2Y=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-display-name": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.22.5.tgz", "integrity": "sha1-PEMm+fzjHHlo1sud68rzLZ4nmis=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx": {"version": "7.18.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.18.6.tgz", "integrity": "sha1-JyHpbTHfluO3rUj/RGmV0mvAKP8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-module-imports": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/types": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-development": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.22.5.tgz", "integrity": "sha1-5xa27b75cqkhZc1p2S8SVffnPoc=", "dev": true, "license": "MIT", "dependencies": {"@babel/plugin-transform-react-jsx": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-development/node_modules/@babel/plugin-transform-react-jsx": {"version": "7.22.15", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.22.15.tgz", "integrity": "sha512-oKckg2eZFa8771O/5vi7XeTvmM6+O9cxZu+kanTU7tD4sin5nO/G8jGJhq8Hvt2Z0kUoEDRayuZLaUlYl8QuGA==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.22.5", "@babel/helper-module-imports": "^7.22.15", "@babel/helper-plugin-utils": "^7.22.5", "@babel/plugin-syntax-jsx": "^7.22.5", "@babel/types": "^7.22.15"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-self": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.22.5.tgz", "integrity": "sha1-yi/cEbwg1NRt4BE3MYsT0E5IHY4=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-react-jsx-source": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.22.5.tgz", "integrity": "sha1-Sa8WFb/fbtnT6eQ+Ql4LK2XRW2w=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-regenerator": {"version": "7.22.10", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.22.10.tgz", "integrity": "sha1-jO7zvXN1xNt2UoeLAkGyvl0MPMo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "regenerator-transform": "^0.15.2"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-reserved-words": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.22.5.tgz", "integrity": "sha1-gyzTW4HCh8S80JzgPiIZlkH5ZPs=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-runtime": {"version": "7.4.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.4.4.tgz", "integrity": "sha1-pQ9dFunDpKwYoan5gDwQfDgLzgg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "resolve": "^1.8.1", "semver": "^5.5.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-shorthand-properties": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.22.5.tgz", "integrity": "sha1-bid2VL6CtVWfxLn1gIhQfCTwxiQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-spread": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-spread/-/plugin-transform-spread-7.22.5.tgz", "integrity": "sha1-ZIf9KfIpyV4oS6bJjWXq+4k/6ms=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-skip-transparent-expression-wrappers": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-sticky-regex": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.22.5.tgz", "integrity": "sha1-KVq6FZW/yBl6vQLq5fwojA3rJqo=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-template-literals": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.22.5.tgz", "integrity": "sha1-jzjPKR5feo5g6fczGT8LzBCQm/8=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typeof-symbol": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.22.5.tgz", "integrity": "sha1-XiukeNpLYDr4Zz/3xU91qXtxazQ=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-regex": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.22.5.tgz", "integrity": "sha1-zn57s+8gjE/2fgKiKBZlYlbXoYM=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-env": {"version": "7.4.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/preset-env/-/preset-env-7.4.5.tgz", "integrity": "sha1-L61/Ypg9WvVjtfMTkkJ1WISZilg=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-proposal-async-generator-functions": "^7.2.0", "@babel/plugin-proposal-json-strings": "^7.2.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.4", "@babel/plugin-proposal-optional-catch-binding": "^7.2.0", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/plugin-syntax-async-generators": "^7.2.0", "@babel/plugin-syntax-json-strings": "^7.2.0", "@babel/plugin-syntax-object-rest-spread": "^7.2.0", "@babel/plugin-syntax-optional-catch-binding": "^7.2.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-async-to-generator": "^7.4.4", "@babel/plugin-transform-block-scoped-functions": "^7.2.0", "@babel/plugin-transform-block-scoping": "^7.4.4", "@babel/plugin-transform-classes": "^7.4.4", "@babel/plugin-transform-computed-properties": "^7.2.0", "@babel/plugin-transform-destructuring": "^7.4.4", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/plugin-transform-duplicate-keys": "^7.2.0", "@babel/plugin-transform-exponentiation-operator": "^7.2.0", "@babel/plugin-transform-for-of": "^7.4.4", "@babel/plugin-transform-function-name": "^7.4.4", "@babel/plugin-transform-literals": "^7.2.0", "@babel/plugin-transform-member-expression-literals": "^7.2.0", "@babel/plugin-transform-modules-amd": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.4.4", "@babel/plugin-transform-modules-systemjs": "^7.4.4", "@babel/plugin-transform-modules-umd": "^7.2.0", "@babel/plugin-transform-named-capturing-groups-regex": "^7.4.5", "@babel/plugin-transform-new-target": "^7.4.4", "@babel/plugin-transform-object-super": "^7.2.0", "@babel/plugin-transform-parameters": "^7.4.4", "@babel/plugin-transform-property-literals": "^7.2.0", "@babel/plugin-transform-regenerator": "^7.4.5", "@babel/plugin-transform-reserved-words": "^7.2.0", "@babel/plugin-transform-shorthand-properties": "^7.2.0", "@babel/plugin-transform-spread": "^7.2.0", "@babel/plugin-transform-sticky-regex": "^7.2.0", "@babel/plugin-transform-template-literals": "^7.4.4", "@babel/plugin-transform-typeof-symbol": "^7.2.0", "@babel/plugin-transform-unicode-regex": "^7.4.4", "@babel/types": "^7.4.4", "browserslist": "^4.6.0", "core-js-compat": "^3.1.1", "invariant": "^2.2.2", "js-levenshtein": "^1.1.3", "semver": "^5.5.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-react": {"version": "7.9.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/preset-react/-/preset-react-7.9.4.tgz", "integrity": "sha1-xsl2k6xltrnAtPJblIqPZlRjAU0=", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3", "@babel/plugin-transform-react-display-name": "^7.8.3", "@babel/plugin-transform-react-jsx": "^7.9.4", "@babel/plugin-transform-react-jsx-development": "^7.9.0", "@babel/plugin-transform-react-jsx-self": "^7.9.0", "@babel/plugin-transform-react-jsx-source": "^7.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/regjsgen": {"version": "0.8.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/regjsgen/-/regjsgen-0.8.0.tgz", "integrity": "sha1-8LppsHXh8F+yglt/rZkeetuxgxA=", "dev": true, "license": "MIT"}, "node_modules/@babel/runtime": {"version": "7.22.11", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/runtime/-/runtime-7.22.11.tgz", "integrity": "sha1-epuju+QGrW+ejdTaLs5FPrI6d6Q=", "dev": true, "license": "MIT", "dependencies": {"regenerator-runtime": "^0.14.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.22.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/template/-/template-7.22.5.tgz", "integrity": "sha1-DIxNlEUJh1hJvQNE/wBQdW7vxuw=", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.22.5", "@babel/parser": "^7.22.5", "@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.22.11", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/traverse/-/traverse-7.22.11.tgz", "integrity": "sha512-mzAenteTfomcB7mfPtyi+4oe5BZ6MXxWcn4CX+h4IRJ+OOGXBrWU6jDQavkQI9Vuc5P+donFabBfFCcmWka9lQ==", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.22.10", "@babel/generator": "^7.22.10", "@babel/helper-environment-visitor": "^7.22.5", "@babel/helper-function-name": "^7.22.5", "@babel/helper-hoist-variables": "^7.22.5", "@babel/helper-split-export-declaration": "^7.22.6", "@babel/parser": "^7.22.11", "@babel/types": "^7.22.11", "debug": "^4.1.0", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.23.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@babel/types/-/types-7.23.0.tgz", "integrity": "sha512-0oIyUfKoI3mSqMvsxBdclDwxXKXAUA8v/apZbc+iSyARYou1o8ZGDxbUYyLFoW2arqS2jDGqJuZvv1d/io1axg==", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.22.5", "@babel/helper-validator-identifier": "^7.22.20", "to-fast-properties": "^2.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@baize/wdk": {"version": "0.7.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/product_npm/@baize/wdk/-/@baize/wdk-0.7.2.tgz", "integrity": "sha512-UHdCwfJkJLyTE3SmVXuMNlBbjuXUs24mWgqTEZzaISr2S92ilTyuycU9bJAeYMXl9WPM5OtR6zsJ6mEULeTzLw==", "license": "MIT", "engines": {"node": ">= 10", "npm": ">= 6.x"}}, "node_modules/@baize/weselector": {"version": "0.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/product_npm/@baize/weselector/-/@baize/weselector-0.1.6.tgz", "integrity": "sha512-Dso4UyXTBJurpI9AlNbEc+lFhRtnsXWgCMfvPFzwZLyoaBRE+Yq9serKJKVOp7iHS1fJMGsdJwkkGRlnmQW06g==", "dependencies": {"@baize/wdk": "^0.7.2"}}, "node_modules/@cloudsop/babel-plugin-eviewui-import": {"version": "0.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/product_npm/@cloudsop/babel-plugin-eviewui-import/-/@cloudsop/babel-plugin-eviewui-import-0.0.3.tgz", "integrity": "sha512-PTitqf0ui+dkabIRfBquaxauRpjtzy3sz5DV5wNFcEsOLEVBstemvfOynG+g6fKbwF90ikh0+jsbtavnUsLuig==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/@cloudsop/horizon": {"version": "0.0.63", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/product_npm/@cloudsop/horizon/-/@cloudsop/horizon-0.0.63.tgz", "integrity": "sha512-RPDzOO7bYNmsj2pFhFFZj+xuGzVbvzwI1FjybODcFizeSWlTIRWgghW+/EZMzlrQ8y7B5ToJUMG/MLbkFq6Rnw==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/@digitalview/hw-dom": {"version": "1.0.14", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/product_npm/@digitalview/hw-dom/-/@digitalview/hw-dom-1.0.14.tgz", "integrity": "sha512-2wgNGOtF9fUzYMoFA5HuJb9eCHaa4i5aofUmLECblh0uzaxqw9Zy5uYqYbdkhG8z9J6HFNG2qUXq09ry9fdwmQ==", "license": "ISC", "peerDependencies": {"@baize/wdk": "0.7.2"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@jridgewell/gen-mapping/-/gen-mapping-0.3.3.tgz", "integrity": "sha1-fgLm6135AartsIUUIDsJZhQCQJg=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.0.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@jridgewell/resolve-uri/-/resolve-uri-3.1.1.tgz", "integrity": "sha1-wIZ5Bj8nlhWjMmWDujqQ0dgsxyE=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@jridgewell/set-array/-/set-array-1.1.2.tgz", "integrity": "sha1-fGz5mNbSC5FMClWpGuko/yWWXnI=", "dev": true, "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.4.15", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz", "integrity": "sha1-18bmdVx4VnqVHgSrUu8P0m3lnzI=", "dev": true, "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.19", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@jridgewell/trace-mapping/-/trace-mapping-0.3.19.tgz", "integrity": "sha1-+KMkmGL5G+SNMSfDz+mS95tLiBE=", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@types/glob": {"version": "7.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@types/glob/-/glob-7.2.0.tgz", "integrity": "sha1-vBtb86qS8lvV3TnzXFc2G9zlsus=", "dev": true, "license": "MIT", "dependencies": {"@types/minimatch": "*", "@types/node": "*"}}, "node_modules/@types/html-minifier-terser": {"version": "5.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@types/html-minifier-terser/-/html-minifier-terser-5.1.2.tgz", "integrity": "sha1-aTsxatMj6pfu1rOO0aPMArFnK1c=", "dev": true, "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.12", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@types/json-schema/-/json-schema-7.0.12.tgz", "integrity": "sha1-1w+rpwOdX8pUyDx9urQQUdK29ss=", "dev": true, "license": "MIT"}, "node_modules/@types/minimatch": {"version": "3.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@types/minimatch/-/minimatch-3.0.1.tgz", "integrity": "sha1-toPrYL41gwTvFG9XddtMDjaWpVA=", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "16.18.43", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@types/node/-/node-16.18.43.tgz", "integrity": "sha512-YFpgPKPRcwYbeNOimfu70B+TVJe6tr88WiW/TzEldkwGxQXrmabpU+lDjrFlNqdqIi3ON0o69EQBW62VH4MIxw==", "dev": true, "license": "MIT"}, "node_modules/@types/source-list-map": {"version": "0.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@types/source-list-map/-/source-list-map-0.1.2.tgz", "integrity": "sha1-AHiDYGP/rxdBI0m7o2QIfgrALsk=", "dev": true, "license": "MIT"}, "node_modules/@types/tapable": {"version": "1.0.8", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@types/tapable/-/tapable-1.0.8.tgz", "integrity": "sha1-uUpDkchWZse3Mpn9OtedT6pDUxA=", "dev": true, "license": "MIT"}, "node_modules/@types/uglify-js": {"version": "3.17.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@types/uglify-js/-/uglify-js-3.17.2.tgz", "integrity": "sha512-9SjrHO54LINgC/6Ehr81NjAxAYvwEZqjUHLjJYvC4Nmr9jbLQCIZbWSvl4vXQkkmR1UAuaKDycau3O1kWGFyXQ==", "dev": true, "license": "MIT", "dependencies": {"source-map": "^0.6.1"}}, "node_modules/@types/uglify-js/node_modules/source-map": {"version": "0.6.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-map/0.6.1/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/@types/webpack": {"version": "4.41.33", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@types/webpack/-/webpack-4.41.33.tgz", "integrity": "sha1-FhZIRaW+ajBry+VUqOZ/nKwhX/w=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/tapable": "^1", "@types/uglify-js": "*", "@types/webpack-sources": "*", "anymatch": "^3.0.0", "source-map": "^0.6.0"}}, "node_modules/@types/webpack-sources": {"version": "0.1.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@types/webpack-sources/-/webpack-sources-0.1.5.tgz", "integrity": "sha1-vkfBD3g9PW7+FHH/fwQmEb1GSpI=", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/source-list-map": "*", "source-map": "^0.6.1"}}, "node_modules/@types/webpack-sources/node_modules/source-map": {"version": "0.6.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-map/0.6.1/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/@types/webpack/node_modules/source-map": {"version": "0.6.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-map/0.6.1/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/@webassemblyjs/ast": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/ast/-/ast-1.9.0.tgz", "integrity": "sha1-vYUGBLQEJFmlpBzX0zjL7Wle2WQ=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/helper-module-context": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0"}}, "node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.9.0.tgz", "integrity": "sha1-PD07Jxvd/ITesA9xNEQ4MR1S/7Q=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-api-error": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/helper-api-error/-/helper-api-error-1.9.0.tgz", "integrity": "sha1-ID9nbjM7lsnaLuqzzO8zxFkotqI=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-buffer": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/helper-buffer/-/helper-buffer-1.9.0.tgz", "integrity": "sha1-oUQtJpxf6yP8vJ73WdrDVH8p3gA=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-code-frame": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/helper-code-frame/-/helper-code-frame-1.9.0.tgz", "integrity": "sha1-ZH+Iks0gQ6gqwMjF51w28dkVnyc=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/wast-printer": "1.9.0"}}, "node_modules/@webassemblyjs/helper-fsm": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/helper-fsm/-/helper-fsm-1.9.0.tgz", "integrity": "sha1-wFJWtxJEIUZx9LCOwQitY7cO3bg=", "dev": true, "license": "ISC"}, "node_modules/@webassemblyjs/helper-module-context": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/helper-module-context/-/helper-module-context-1.9.0.tgz", "integrity": "sha1-JdiIS3aDmHGgimxvgGw5ee9xLwc=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0"}}, "node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.9.0.tgz", "integrity": "sha1-T+2L6sm4wU+MWLcNEk1UndH+V5A=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.9.0.tgz", "integrity": "sha1-WkE41aYpK6GLBMWuSXF+QWeWU0Y=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0"}}, "node_modules/@webassemblyjs/ieee754": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/ieee754/-/ieee754-1.9.0.tgz", "integrity": "sha1-Fceg+6roP7JhQ7us9tbfFwKtOeQ=", "dev": true, "license": "MIT", "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "node_modules/@webassemblyjs/leb128": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/leb128/-/leb128-1.9.0.tgz", "integrity": "sha1-8Zygt2ptxVYjoJz/p2noOPoeHJU=", "dev": true, "license": "MIT", "dependencies": {"@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/utf8": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/utf8/-/utf8-1.9.0.tgz", "integrity": "sha1-BNM7Y2945qaBMifoJAL3Y3tiKas=", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/wasm-edit": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/wasm-edit/-/wasm-edit-1.9.0.tgz", "integrity": "sha1-P+bXnT8PkiGDqoYALELdJWz+6c8=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/helper-wasm-section": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wasm-opt": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "@webassemblyjs/wast-printer": "1.9.0"}}, "node_modules/@webassemblyjs/wasm-gen": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/wasm-gen/-/wasm-gen-1.9.0.tgz", "integrity": "sha1-ULxw7Gje2OJ2OwGhQYv0NJGnpJw=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/ieee754": "1.9.0", "@webassemblyjs/leb128": "1.9.0", "@webassemblyjs/utf8": "1.9.0"}}, "node_modules/@webassemblyjs/wasm-opt": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/wasm-opt/-/wasm-opt-1.9.0.tgz", "integrity": "sha1-IhEYHlsxMmRDzIES658LkChyGmE=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-buffer": "1.9.0", "@webassemblyjs/wasm-gen": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0"}}, "node_modules/@webassemblyjs/wasm-parser": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/wasm-parser/-/wasm-parser-1.9.0.tgz", "integrity": "sha1-nUjkSCbfSmWYKUqmyHRp1kL/9l4=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-api-error": "1.9.0", "@webassemblyjs/helper-wasm-bytecode": "1.9.0", "@webassemblyjs/ieee754": "1.9.0", "@webassemblyjs/leb128": "1.9.0", "@webassemblyjs/utf8": "1.9.0"}}, "node_modules/@webassemblyjs/wast-parser": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/wast-parser/-/wast-parser-1.9.0.tgz", "integrity": "sha1-MDERXXmsW9JhVWzsw/qQo+9FGRQ=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/floating-point-hex-parser": "1.9.0", "@webassemblyjs/helper-api-error": "1.9.0", "@webassemblyjs/helper-code-frame": "1.9.0", "@webassemblyjs/helper-fsm": "1.9.0", "@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/wast-printer": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@webassemblyjs/wast-printer/-/wast-printer-1.9.0.tgz", "integrity": "sha1-STXVTIX+9jewDOn1I3dFHQDUeJk=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/wast-parser": "1.9.0", "@xtuc/long": "4.2.2"}}, "node_modules/@xtuc/ieee754": {"version": "1.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@xtuc/ieee754/-/ieee754-1.2.0.tgz", "integrity": "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@xtuc/long": {"version": "4.2.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/@xtuc/long/-/long-4.2.2.tgz", "integrity": "sha1-0pHGpOl5ibXGHZrPOWrk/hM6cY0=", "dev": true, "license": "Apache-2.0"}, "node_modules/accepts": {"version": "1.3.8", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/accepts/-/accepts-1.3.8.tgz", "integrity": "sha1-C/C+EltnAUrcsLCSHmLbe//hay4=", "dev": true, "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "6.4.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/acorn/6.4.2/acorn-6.4.2.tgz", "integrity": "sha1-NYZv1xBSjpLeEM8GAWSY5H454eY=", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ajv": {"version": "6.12.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ajv/6.12.6/ajv-6.12.6.tgz", "integrity": "sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-errors": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ajv-errors/-/ajv-errors-1.0.0.tgz", "integrity": "sha1-7PAh+hCP0X37Xms4Py3SM+Mf/Fk=", "dev": true, "license": "MIT", "peerDependencies": {"ajv": ">=5.0.0"}}, "node_modules/ajv-keywords": {"version": "3.5.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ajv-keywords/3.5.2/ajv-keywords-3.5.2.tgz", "integrity": "sha1-MfKdpatuANHC0yms97WSlhTVAU0=", "dev": true, "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/ansi-colors": {"version": "3.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ansi-colors/-/ansi-colors-3.2.1.tgz", "integrity": "sha1-ljgEfkIT80KKEZRKfUsxy6Cj/5U=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ansi-html": {"version": "0.0.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ansi-html/0.0.7/ansi-html-0.0.7.tgz", "integrity": "sha1-gTWEAhliqenm/QOflA0S9WynhZ4=", "dev": true, "engines": ["node >= 0.8.0"], "license": "Apache-2.0", "bin": {"ansi-html": "bin/ansi-html"}}, "node_modules/ansi-regex": {"version": "2.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ansi-regex/2.1.1/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/ansi-styles": {"version": "3.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ansi-styles/3.2.1/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "dev": true, "license": "MIT", "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/anymatch": {"version": "3.1.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=", "dev": true, "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/aproba": {"version": "1.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/aproba/-/aproba-1.1.2.tgz", "integrity": "sha1-RcZikJTeTpb2k+9+q3SuB5wkD8E=", "dev": true, "license": "ISC"}, "node_modules/arr-diff": {"version": "4.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/arr-diff/4.0.0/arr-diff-4.0.0.tgz", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/arr-flatten": {"version": "1.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/arr-flatten/1.1.0/arr-flatten-1.1.0.tgz", "integrity": "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/arr-union": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/arr-union/3.1.0/arr-union-3.1.0.tgz", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-buffer-byte-length": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/array-buffer-byte-length/-/array-buffer-byte-length-1.0.0.tgz", "integrity": "sha1-+r6LwZP+qGXzF/54Bwhe4N7lrq0=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "is-array-buffer": "^3.0.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/array-flatten": {"version": "2.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/array-flatten/2.1.2/array-flatten-2.1.2.tgz", "integrity": "sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=", "dev": true, "license": "MIT"}, "node_modules/array-union": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/array-union/1.0.2/array-union-1.0.2.tgz", "integrity": "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=", "dev": true, "license": "MIT", "dependencies": {"array-uniq": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/array-uniq": {"version": "1.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/array-uniq/1.0.3/array-uniq-1.0.3.tgz", "integrity": "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array-unique": {"version": "0.3.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/array-unique/0.3.2/array-unique-0.3.2.tgz", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/array.prototype.reduce": {"version": "1.0.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/array.prototype.reduce/-/array.prototype.reduce-1.0.6.tgz", "integrity": "sha512-UW+Mz8LG/sPSU8jRDCjVr6J/ZKAGpHfwrZ6kWTG5qCxIEiXdVshqGnu5vEZA8S1y6X4aCSbQZ0/EEsfvEvBiSg==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "es-array-method-boxes-properly": "^1.0.0", "is-string": "^1.0.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/arraybuffer.prototype.slice": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.1.tgz", "integrity": "sha1-m16jhopu68MCc9pXfriIOBwARLs=", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.0", "call-bind": "^1.0.2", "define-properties": "^1.2.0", "get-intrinsic": "^1.2.1", "is-array-buffer": "^3.0.2", "is-shared-array-buffer": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/asn1.js": {"version": "5.4.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/asn1.js/5.4.1/asn1.js-5.4.1.tgz", "integrity": "sha1-EamAuE67kXgc41sP3C7ilON4Pwc=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "safer-buffer": "^2.1.0"}}, "node_modules/asn1.js/node_modules/bn.js": {"version": "4.11.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/bn.js/-/bn.js-4.11.6.tgz", "integrity": "sha1-UzRK2xRhehP26N0s4okF0cC6MhU=", "dev": true, "license": "MIT"}, "node_modules/assert": {"version": "1.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/assert/-/assert-1.1.2.tgz", "integrity": "sha1-raoExGu1jG3R8pTaPrJuYijrbkQ=", "dev": true, "license": "MIT", "dependencies": {"util": "0.10.3"}}, "node_modules/assert/node_modules/inherits": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/inherits/2.0.1/inherits-2.0.1.tgz", "integrity": "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=", "dev": true, "license": "ISC"}, "node_modules/assert/node_modules/util": {"version": "0.10.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/util/0.10.3/util-0.10.3.tgz", "integrity": "sha1-evsa/lCAUkZInj23/g7TeTNqwPk=", "dev": true, "license": "MIT", "dependencies": {"inherits": "2.0.1"}}, "node_modules/assign-symbols": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/assign-symbols/1.0.0/assign-symbols-1.0.0.tgz", "integrity": "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/async": {"version": "2.6.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/async/-/async-2.6.4.tgz", "integrity": "sha1-cGt/9ghGZM1+rnE/b5ZUM7VQQiE=", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/async-each": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/async-each/-/async-each-1.0.2.tgz", "integrity": "sha1-i4p8oqZY+Sfp8wfW0aQvQZnw9zU=", "dev": true, "license": "MIT"}, "node_modules/async-limiter": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/async-limiter/1.0.1/async-limiter-1.0.1.tgz", "integrity": "sha1-3TeelPDbgxCwgpH51kwyCXZmF/0=", "dev": true, "license": "MIT"}, "node_modules/atob": {"version": "2.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/atob/2.1.2/atob-2.1.2.tgz", "integrity": "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=", "dev": true, "license": "(MIT OR Apache-2.0)", "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/available-typed-arrays": {"version": "1.0.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/available-typed-arrays/-/available-typed-arrays-1.0.5.tgz", "integrity": "sha1-kvlWFlAQadB9EO2y/DfT4cZRI7c=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/babel-loader": {"version": "8.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/babel-loader/8.1.0/babel-loader-8.1.0.tgz", "integrity": "sha1-xhHVESvVIJq+i5+oTD5NolJ18cM=", "dev": true, "license": "MIT", "dependencies": {"find-cache-dir": "^2.1.0", "loader-utils": "^1.4.0", "mkdirp": "^0.5.3", "pify": "^4.0.1", "schema-utils": "^2.6.5"}, "engines": {"node": ">= 6.9"}, "peerDependencies": {"@babel/core": "^7.0.0", "webpack": ">=2"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=", "dev": true, "license": "MIT"}, "node_modules/base": {"version": "0.11.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/base/0.11.2/base-0.11.2.tgz", "integrity": "sha1-e95c7RRbbVUakNuH+DxVi060io8=", "dev": true, "license": "MIT", "dependencies": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/define-property": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/define-property/1.0.0/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base64-js": {"version": "1.5.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/base64-js/-/base64-js-1.5.1.tgz", "integrity": "sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/batch": {"version": "0.6.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/batch/0.6.1/batch-0.6.1.tgz", "integrity": "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=", "dev": true, "license": "MIT"}, "node_modules/big.js": {"version": "5.2.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/big.js/5.2.2/big.js-5.2.2.tgz", "integrity": "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/binary-extensions": {"version": "2.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/binary-extensions/2.2.0/binary-extensions-2.2.0.tgz", "integrity": "sha1-dfUC7q+f/eQvyYgpZFvk6na9ni0=", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=8"}}, "node_modules/bindings": {"version": "1.5.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/bindings/1.5.0/bindings-1.5.0.tgz", "integrity": "sha1-EDU8npRTNLwFEabZCzj7x8nFBN8=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"file-uri-to-path": "1.0.0"}}, "node_modules/bluebird": {"version": "3.7.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/bluebird/3.7.2/bluebird-3.7.2.tgz", "integrity": "sha1-nyKcFb4nJFT/qXOs4NvueaGww28=", "dev": true, "license": "MIT"}, "node_modules/bn.js": {"version": "5.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/bn.js/-/bn.js-5.2.1.tgz", "integrity": "sha1-C8UnpqDRjQqo1bBTjOSnfcz6e3A=", "dev": true, "license": "MIT"}, "node_modules/body-parser": {"version": "1.20.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/body-parser/-/body-parser-1.20.1.tgz", "integrity": "sha1-sYEqiRLBlc03Gj7l5m+qIzilxmg=", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.11.0", "raw-body": "2.5.1", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/body-parser/node_modules/bytes": {"version": "3.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/bytes/-/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/debug/2.6.9/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ms/2.0.0/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/body-parser/node_modules/qs": {"version": "6.11.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/qs/-/qs-6.11.0.tgz", "integrity": "sha1-/Q2WNEb3pl4TZ+AavYVClFPww3o=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.4"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/bonjour": {"version": "3.5.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/bonjour/3.5.0/bonjour-3.5.0.tgz", "integrity": "sha1-jokKGD2O6aI5OzhExpGkK897yfU=", "dev": true, "license": "MIT", "dependencies": {"array-flatten": "^2.1.0", "deep-equal": "^1.0.1", "dns-equal": "^1.0.0", "dns-txt": "^2.0.2", "multicast-dns": "^6.0.1", "multicast-dns-service-types": "^1.1.0"}}, "node_modules/boolbase": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/boolbase/1.0.0/boolbase-1.0.0.tgz", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24=", "dev": true, "license": "ISC"}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/brace-expansion/1.1.11/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "2.3.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/braces/2.3.2/braces-2.3.2.tgz", "integrity": "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=", "dev": true, "license": "MIT", "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/braces/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/extend-shallow/2.0.1/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/braces/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-extendable/0.1.1/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/brorand": {"version": "1.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/brorand/1.1.0/brorand-1.1.0.tgz", "integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=", "dev": true, "license": "MIT"}, "node_modules/browserify-aes": {"version": "1.0.8", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/browserify-aes/-/browserify-aes-1.0.8.tgz", "integrity": "sha1-yPo7G3WFu3unfFVgtgmW3extUwk=", "dev": true, "license": "MIT", "dependencies": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "node_modules/browserify-cipher": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/browserify-cipher/1.0.1/browserify-cipher-1.0.1.tgz", "integrity": "sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=", "dev": true, "license": "MIT", "dependencies": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}}, "node_modules/browserify-des": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/browserify-des/1.0.2/browserify-des-1.0.2.tgz", "integrity": "sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=", "dev": true, "license": "MIT", "dependencies": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/browserify-rsa": {"version": "4.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/browserify-rsa/4.1.0/browserify-rsa-4.1.0.tgz", "integrity": "sha1-sv0Gtbda4pf3zi3GUfkY9b4VjI0=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^5.0.0", "randombytes": "^2.0.1"}}, "node_modules/browserify-sign": {"version": "4.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/browserify-sign/4.2.1/browserify-sign-4.2.1.tgz", "integrity": "sha1-6vSt1G3VS+O7OzbAzxWrvrp5VsM=", "dev": true, "license": "ISC", "dependencies": {"bn.js": "^5.1.1", "browserify-rsa": "^4.0.1", "create-hash": "^1.2.0", "create-hmac": "^1.1.7", "elliptic": "^6.5.3", "inherits": "^2.0.4", "parse-asn1": "^5.1.5", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}}, "node_modules/browserify-sign/node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/browserify-zlib": {"version": "0.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/browserify-zlib/0.2.0/browserify-zlib-0.2.0.tgz", "integrity": "sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=", "dev": true, "license": "MIT", "dependencies": {"pako": "~1.0.5"}}, "node_modules/browserslist": {"version": "4.21.10", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/browserslist/-/browserslist-4.21.10.tgz", "integrity": "sha1-27rFdmKME9OyIxMyyy7FpG4BW7A=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001517", "electron-to-chromium": "^1.4.477", "node-releases": "^2.0.13", "update-browserslist-db": "^1.0.11"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer": {"version": "4.9.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/buffer/4.9.2/buffer-4.9.2.tgz", "integrity": "sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=", "dev": true, "license": "MIT", "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "node_modules/buffer-from": {"version": "1.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/buffer-from/1.1.1/buffer-from-1.1.1.tgz", "integrity": "sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=", "dev": true, "license": "MIT"}, "node_modules/buffer-indexof": {"version": "1.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/buffer-indexof/1.1.1/buffer-indexof-1.1.1.tgz", "integrity": "sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=", "dev": true, "license": "MIT"}, "node_modules/buffer-xor": {"version": "1.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/buffer-xor/1.0.3/buffer-xor-1.0.3.tgz", "integrity": "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=", "dev": true, "license": "MIT"}, "node_modules/buffer/node_modules/isarray": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/isarray/1.0.0/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true, "license": "MIT"}, "node_modules/builtin-status-codes": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/builtin-status-codes/3.0.0/builtin-status-codes-3.0.0.tgz", "integrity": "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=", "dev": true, "license": "MIT"}, "node_modules/bytes": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/bytes/3.0.0/bytes-3.0.0.tgz", "integrity": "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cacache": {"version": "12.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/cacache/12.0.4/cacache-12.0.4.tgz", "integrity": "sha1-ZovL0QWutfHZL+JVcOyVJcj6pAw=", "dev": true, "license": "ISC", "dependencies": {"bluebird": "^3.5.5", "chownr": "^1.1.1", "figgy-pudding": "^3.5.1", "glob": "^7.1.4", "graceful-fs": "^4.1.15", "infer-owner": "^1.0.3", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.3", "ssri": "^6.0.1", "unique-filename": "^1.1.1", "y18n": "^4.0.0"}}, "node_modules/cache-base": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/cache-base/1.0.1/cache-base-1.0.1.tgz", "integrity": "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=", "dev": true, "license": "MIT", "dependencies": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/call-bind": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/call-bind/1.0.2/call-bind-1.0.2.tgz", "integrity": "sha1-sdTonmiBGcPJqQOtMKuy9qkZvjw=", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "get-intrinsic": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/camel-case": {"version": "4.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/camel-case/4.1.2/camel-case-4.1.2.tgz", "integrity": "sha1-lygHKpVPgFIoIlpt7qazhGHhvVo=", "dev": true, "license": "MIT", "dependencies": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "node_modules/camelcase": {"version": "6.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/camelcase/-/camelcase-6.3.0.tgz", "integrity": "sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/caniuse-lite": {"version": "1.0.30001525", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/caniuse-lite/-/caniuse-lite-1.0.30001525.tgz", "integrity": "sha512-/3z+wB4icFt3r0USMwxujAqRvaD/B7rvGTsKhbhSQErVrJvkZCLhgNLJxU8MevahQVH6hCU9FsHdNUFbiwmE7Q==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chalk": {"version": "2.4.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/chalk/2.4.2/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/chokidar": {"version": "3.5.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/chokidar/-/chokidar-3.5.3.tgz", "integrity": "sha1-HPN8hwe5Mr0a8a4iwEMuKs0ZA70=", "dev": true, "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT", "optional": true, "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/braces": {"version": "3.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/braces/-/braces-3.0.2.tgz", "integrity": "sha1-NFThpGLujVmeI23zNs2epPiv4Qc=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"fill-range": "^7.0.1"}, "engines": {"node": ">=8"}}, "node_modules/chokidar/node_modules/fill-range": {"version": "7.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/fill-range/-/fill-range-7.0.1.tgz", "integrity": "sha1-GRmmp8df44ssfHflGYU12prN2kA=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/chokidar/node_modules/is-number": {"version": "7.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-number/-/is-number-7.0.0.tgz", "integrity": "sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.12.0"}}, "node_modules/chokidar/node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/chownr": {"version": "1.1.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/chownr/1.1.4/chownr-1.1.4.tgz", "integrity": "sha1-b8nXtC0ypYNZYzdmbn0ICE2izGs=", "dev": true, "license": "ISC"}, "node_modules/chrome-trace-event": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/chrome-trace-event/1.0.2/chrome-trace-event-1.0.2.tgz", "integrity": "sha1-I0CQ7pfH1K0aLEvq4nUF3v/GCKQ=", "dev": true, "license": "MIT", "dependencies": {"tslib": "^1.9.0"}, "engines": {"node": ">=6.0"}}, "node_modules/chrome-trace-event/node_modules/tslib": {"version": "1.14.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/tslib/-/tslib-1.14.1.tgz", "integrity": "sha1-zy04vcNKE0vK8QkcQfZhni9nLQA=", "dev": true, "license": "0BSD"}, "node_modules/cipher-base": {"version": "1.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/cipher-base/-/cipher-base-1.0.3.tgz", "integrity": "sha1-7qvxlEGc6QDaMBjCB9IS8qbfCgc=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1"}}, "node_modules/class-utils": {"version": "0.3.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/class-utils/0.3.6/class-utils-0.3.6.tgz", "integrity": "sha1-+TNprouafOAv1B+q0MqDAzGQxGM=", "dev": true, "license": "MIT", "dependencies": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/define-property": {"version": "0.2.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/define-property/0.2.5/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/is-accessor-descriptor": {"version": "0.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-accessor-descriptor/0.1.6/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/is-accessor-descriptor/node_modules/kind-of": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/-/kind-of-3.1.0.tgz", "integrity": "sha1-R11pil5J/15T0U4+cyQp3Iv0z0c=", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/is-data-descriptor": {"version": "0.1.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-data-descriptor/0.1.4/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/is-data-descriptor/node_modules/kind-of": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/-/kind-of-3.1.0.tgz", "integrity": "sha1-R11pil5J/15T0U4+cyQp3Iv0z0c=", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/is-descriptor": {"version": "0.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-descriptor/0.1.6/is-descriptor-0.1.6.tgz", "integrity": "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=", "dev": true, "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/kind-of": {"version": "5.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/5.1.0/kind-of-5.1.0.tgz", "integrity": "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/clean-css": {"version": "4.2.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/clean-css/-/clean-css-4.2.4.tgz", "integrity": "sha1-czv0brpOYHxokepXwkqYk1aDEXg=", "dev": true, "license": "MIT", "dependencies": {"source-map": "~0.6.0"}, "engines": {"node": ">= 4.0"}}, "node_modules/clean-css/node_modules/source-map": {"version": "0.6.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-map/0.6.1/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/cliui": {"version": "5.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/cliui/5.0.0/cliui-5.0.0.tgz", "integrity": "sha1-3u/P2y6AB4SqNPRvoI4GhRx7u8U=", "dev": true, "license": "ISC", "dependencies": {"string-width": "^3.1.0", "strip-ansi": "^5.2.0", "wrap-ansi": "^5.1.0"}}, "node_modules/cliui/node_modules/ansi-regex": {"version": "4.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ansi-regex/-/ansi-regex-4.1.1.tgz", "integrity": "sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/cliui/node_modules/strip-ansi": {"version": "5.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/strip-ansi/5.2.0/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/collection-visit": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/collection-visit/1.0.0/collection-visit-1.0.0.tgz", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "dev": true, "license": "MIT", "dependencies": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/color-convert": {"version": "1.9.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/color-convert/-/color-convert-1.9.2.tgz", "integrity": "sha1-SYgbj7pn3xKpa98/VsCqueeRMUc=", "dev": true, "license": "MIT", "dependencies": {"color-name": "1.1.1"}}, "node_modules/color-name": {"version": "1.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/color-name/-/color-name-1.1.1.tgz", "integrity": "sha1-SxQVMEz1ACjqgWQ2Q72C6gWANok=", "dev": true, "license": "MIT"}, "node_modules/commander": {"version": "4.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/commander/4.1.1/commander-4.1.1.tgz", "integrity": "sha1-n9YCvZNilOnp70aj9NaWQESxgGg=", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/commondir": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/commondir/1.0.1/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=", "dev": true, "license": "MIT"}, "node_modules/component-emitter": {"version": "1.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/component-emitter/1.3.0/component-emitter-1.3.0.tgz", "integrity": "sha1-FuQHD7qK4ptnnyIVhT7hgasuq8A=", "dev": true, "license": "MIT"}, "node_modules/compressible": {"version": "2.0.16", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/compressible/-/compressible-2.0.16.tgz", "integrity": "sha1-pJv5hY84IbZM4b4Clq/HOARmp38=", "dev": true, "license": "MIT", "dependencies": {"mime-db": ">= 1.38.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/compression": {"version": "1.7.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/compression/1.7.4/compression-1.7.4.tgz", "integrity": "sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.5", "bytes": "3.0.0", "compressible": "~2.0.16", "debug": "2.6.9", "on-headers": "~1.0.2", "safe-buffer": "5.1.2", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/compression/node_modules/debug": {"version": "2.6.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/debug/2.6.9/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/compression/node_modules/ms": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ms/2.0.0/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/compression/node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/safe-buffer/5.1.2/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "dev": true, "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/concat-map/0.0.1/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true, "license": "MIT"}, "node_modules/concat-stream": {"version": "1.6.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/concat-stream/1.6.2/concat-stream-1.6.2.tgz", "integrity": "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=", "dev": true, "engines": ["node >= 0.8"], "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/connect-history-api-fallback": {"version": "1.6.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/connect-history-api-fallback/1.6.0/connect-history-api-fallback-1.6.0.tgz", "integrity": "sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/console-browserify": {"version": "1.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/console-browserify/1.2.0/console-browserify-1.2.0.tgz", "integrity": "sha1-ZwY871fOts9Jk6KrOlWECujEkzY=", "dev": true}, "node_modules/constants-browserify": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/constants-browserify/1.0.0/constants-browserify-1.0.0.tgz", "integrity": "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=", "dev": true, "license": "MIT"}, "node_modules/content-disposition": {"version": "0.5.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/content-disposition/-/content-disposition-0.5.4.tgz", "integrity": "sha1-i4K076yCUSoCuwsdzsnSxejrW/4=", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/content-type/1.0.4/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "1.9.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/convert-source-map/-/convert-source-map-1.9.0.tgz", "integrity": "sha1-f6rmI1P7QhM2bQypg1jSLoNosF8=", "dev": true, "license": "MIT"}, "node_modules/cookie": {"version": "0.5.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/cookie/-/cookie-0.5.0.tgz", "integrity": "sha1-0fXXGt7GVYxY84mYfDZqpH6ZT4s=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/cookie-signature/1.0.6/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "dev": true, "license": "MIT"}, "node_modules/copy-concurrently": {"version": "1.0.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/copy-concurrently/1.0.5/copy-concurrently-1.0.5.tgz", "integrity": "sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=", "dev": true, "license": "ISC", "dependencies": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}}, "node_modules/copy-descriptor": {"version": "0.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/copy-descriptor/0.1.1/copy-descriptor-0.1.1.tgz", "integrity": "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/core-js-compat": {"version": "3.32.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/core-js-compat/-/core-js-compat-3.32.1.tgz", "integrity": "sha1-Vfmn0pfAdhqOsdMbWT4PW2/66WQ=", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.21.10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-util-is": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/core-util-is/1.0.2/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "dev": true, "license": "MIT"}, "node_modules/create-ecdh": {"version": "4.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/create-ecdh/4.0.4/create-ecdh-4.0.4.tgz", "integrity": "sha1-1uf0v/pmc2CFoHYv06YyaE2rzE4=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^4.1.0", "elliptic": "^6.5.3"}}, "node_modules/create-ecdh/node_modules/bn.js": {"version": "4.11.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/bn.js/-/bn.js-4.11.6.tgz", "integrity": "sha1-UzRK2xRhehP26N0s4okF0cC6MhU=", "dev": true, "license": "MIT"}, "node_modules/create-hash": {"version": "1.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/create-hash/1.2.0/create-hash-1.2.0.tgz", "integrity": "sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=", "dev": true, "license": "MIT", "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "node_modules/create-hmac": {"version": "1.1.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/create-hmac/1.1.7/create-hmac-1.1.7.tgz", "integrity": "sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=", "dev": true, "license": "MIT", "dependencies": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "node_modules/cross-spawn": {"version": "6.0.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/cross-spawn/6.0.5/cross-spawn-6.0.5.tgz", "integrity": "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=", "dev": true, "license": "MIT", "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "engines": {"node": ">=4.8"}}, "node_modules/crypto-browserify": {"version": "3.12.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/crypto-browserify/3.12.0/crypto-browserify-3.12.0.tgz", "integrity": "sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=", "dev": true, "license": "MIT", "dependencies": {"browserify-cipher": "^1.0.0", "browserify-sign": "^4.0.0", "create-ecdh": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.0", "diffie-hellman": "^5.0.0", "inherits": "^2.0.1", "pbkdf2": "^3.0.3", "public-encrypt": "^4.0.0", "randombytes": "^2.0.0", "randomfill": "^1.0.3"}, "engines": {"node": "*"}}, "node_modules/css-loader": {"version": "4.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/css-loader/-/css-loader-4.2.1.tgz", "integrity": "sha1-n0j9fq4SGdYpo/CFupqRAsoRQac=", "dev": true, "license": "MIT", "dependencies": {"camelcase": "^6.0.0", "cssesc": "^3.0.0", "icss-utils": "^4.1.1", "loader-utils": "^2.0.0", "normalize-path": "^3.0.0", "postcss": "^7.0.32", "postcss-modules-extract-imports": "^2.0.0", "postcss-modules-local-by-default": "^3.0.3", "postcss-modules-scope": "^2.2.0", "postcss-modules-values": "^3.0.0", "postcss-value-parser": "^4.1.0", "schema-utils": "^2.7.0", "semver": "^7.3.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.27.0 || ^5.0.0"}}, "node_modules/css-loader/node_modules/loader-utils": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/loader-utils/2.0.0/loader-utils-2.0.0.tgz", "integrity": "sha1-5MrOW4FtQloWa18JfhDNErNgZLA=", "dev": true, "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/css-loader/node_modules/lru-cache": {"version": "6.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/lru-cache/6.0.0/lru-cache-6.0.0.tgz", "integrity": "sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/css-loader/node_modules/semver": {"version": "7.5.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/semver/-/semver-7.5.4.tgz", "integrity": "sha1-SDmG7E7TjhxsSMNIlKkYLb/2im4=", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/css-loader/node_modules/yallist": {"version": "4.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/yallist/4.0.0/yallist-4.0.0.tgz", "integrity": "sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=", "dev": true, "license": "ISC"}, "node_modules/css-select": {"version": "4.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/css-select/-/css-select-4.3.0.tgz", "integrity": "sha1-23EpsoRmYv2GKM/ElquytZ5BUps=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.0.1", "domhandler": "^4.3.1", "domutils": "^2.8.0", "nth-check": "^2.0.1"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/css-what": {"version": "6.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/css-what/-/css-what-6.1.0.tgz", "integrity": "sha1-+17/z3bx3eosgb36pN5E55uscPQ=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/cssesc": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/cssesc/3.0.0/cssesc-3.0.0.tgz", "integrity": "sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=", "dev": true, "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/cyclist": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/cyclist/-/cyclist-1.0.2.tgz", "integrity": "sha1-ZztfIzvzTY5gK5SUKfgXHZEhvqM=", "dev": true, "license": "MIT"}, "node_modules/debug": {"version": "4.3.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/debug/-/debug-4.3.4.tgz", "integrity": "sha1-Exn2V5NX8jONMzfSzdSRS7XcyGU=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decamelize": {"version": "1.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/decamelize/1.2.0/decamelize-1.2.0.tgz", "integrity": "sha1-9lNNFRSCabIDUue+4m9QH5oZEpA=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/decode-uri-component": {"version": "0.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/decode-uri-component/-/decode-uri-component-0.2.1.tgz", "integrity": "sha1-6dev1xb8Gn7GrnzAqj5UCh6sLp0=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/deep-equal": {"version": "1.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/deep-equal/1.1.1/deep-equal-1.1.1.tgz", "integrity": "sha1-tcmMlCzv+vfLBR4k4UNKJaLmB2o=", "dev": true, "license": "MIT", "dependencies": {"is-arguments": "^1.0.4", "is-date-object": "^1.0.1", "is-regex": "^1.0.4", "object-is": "^1.0.1", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.2.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/default-gateway": {"version": "4.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/default-gateway/4.2.0/default-gateway-4.2.0.tgz", "integrity": "sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"execa": "^1.0.0", "ip-regex": "^2.1.0"}, "engines": {"node": ">=6"}}, "node_modules/define-properties": {"version": "1.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/define-properties/-/define-properties-1.2.0.tgz", "integrity": "sha1-UpiFcGcMnqzt2AZPSpkPJAWEm9U=", "dev": true, "license": "MIT", "dependencies": {"has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-property": {"version": "2.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/define-property/2.0.2/define-property-2.0.2.tgz", "integrity": "sha1-1Flono1lS6d+AqgX+HENcCyxbp0=", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/del": {"version": "4.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/del/4.1.1/del-4.1.1.tgz", "integrity": "sha1-no8RciLqRKMf86FWwEm5kFKp8LQ=", "dev": true, "license": "MIT", "dependencies": {"@types/glob": "^7.1.1", "globby": "^6.1.0", "is-path-cwd": "^2.0.0", "is-path-in-cwd": "^2.0.0", "p-map": "^2.0.0", "pify": "^4.0.1", "rimraf": "^2.6.3"}, "engines": {"node": ">=6"}}, "node_modules/depd": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/depd/2.0.0/depd-2.0.0.tgz", "integrity": "sha1-tpYWPMdXVg0JzyLMj60Vcbeedt8=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/des.js": {"version": "1.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/des.js/-/des.js-1.1.0.tgz", "integrity": "sha1-HTf1dm87v/Tuljjocah2jBc7gdo=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "node_modules/destroy": {"version": "1.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/destroy/-/destroy-1.2.0.tgz", "integrity": "sha1-SANzVQmti+VSk0xn32FPlOZvoBU=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-file": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/detect-file/1.0.0/detect-file-1.0.0.tgz", "integrity": "sha1-8NZtA2cqglyxtzvbP+YjEMjlUrc=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/detect-node": {"version": "2.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/detect-node/2.0.4/detect-node-2.0.4.tgz", "integrity": "sha1-AU7o+PZpxcWAI9pkuBecCDooxGw=", "dev": true, "license": "ISC"}, "node_modules/diffie-hellman": {"version": "5.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/diffie-hellman/5.0.3/diffie-hellman-5.0.3.tgz", "integrity": "sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}}, "node_modules/diffie-hellman/node_modules/bn.js": {"version": "4.11.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/bn.js/-/bn.js-4.11.6.tgz", "integrity": "sha1-UzRK2xRhehP26N0s4okF0cC6MhU=", "dev": true, "license": "MIT"}, "node_modules/dns-equal": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/dns-equal/1.0.0/dns-equal-1.0.0.tgz", "integrity": "sha1-s55/HabrCnW6nBcySzR1PEfgZU0=", "dev": true, "license": "MIT"}, "node_modules/dns-packet": {"version": "1.3.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/dns-packet/-/dns-packet-1.3.4.tgz", "integrity": "sha1-40VQZYJKJQe6iGxVqJljuxB97G8=", "dev": true, "license": "MIT", "dependencies": {"ip": "^1.1.0", "safe-buffer": "^5.0.1"}}, "node_modules/dns-txt": {"version": "2.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/dns-txt/2.0.2/dns-txt-2.0.2.tgz", "integrity": "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=", "dev": true, "license": "MIT", "dependencies": {"buffer-indexof": "^1.0.0"}}, "node_modules/dom-converter": {"version": "0.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/dom-converter/0.2.0/dom-converter-0.2.0.tgz", "integrity": "sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=", "dev": true, "license": "MIT", "dependencies": {"utila": "~0.4"}}, "node_modules/dom-serializer": {"version": "1.4.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/dom-serializer/-/dom-serializer-1.4.1.tgz", "integrity": "sha1-3l1Bsa6ikCFdxFptrorc8dMuLTA=", "dev": true, "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/domain-browser": {"version": "1.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/domain-browser/1.2.0/domain-browser-1.2.0.tgz", "integrity": "sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=", "dev": true, "license": "MIT", "engines": {"node": ">=0.4", "npm": ">=1.2"}}, "node_modules/domelementtype": {"version": "2.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/domelementtype/-/domelementtype-2.3.0.tgz", "integrity": "sha1-XEXo6GmVJiYzHXqrMm0B2vZdWJ0=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/domhandler": {"version": "4.3.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/domhandler/-/domhandler-4.3.1.tgz", "integrity": "sha1-jXkgM0FvWdaLwDpap7AYwcqJJ5w=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.2.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/domutils": {"version": "2.8.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/domutils/-/domutils-2.8.0.tgz", "integrity": "sha1-RDfe9dtuLR9dbuhZvZXKfQIEgTU=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/dot-case": {"version": "3.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/dot-case/3.0.4/dot-case-3.0.4.tgz", "integrity": "sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=", "dev": true, "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/duplexify": {"version": "3.7.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/duplexify/3.7.1/duplexify-3.7.1.tgz", "integrity": "sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=", "dev": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ee-first/1.1.1/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "dev": true, "license": "MIT"}, "node_modules/electron-to-chromium": {"version": "1.4.505", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/electron-to-chromium/-/electron-to-chromium-1.4.505.tgz", "integrity": "sha512-0A50eL5BCCKdxig2SsCXhpuztnB9PfUgRMojj5tMvt8O54lbwz3t6wNgnpiTRosw5QjlJB7ixhVyeg8daLQwSQ==", "dev": true, "license": "ISC"}, "node_modules/elliptic": {"version": "6.5.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/elliptic/6.5.4/elliptic-6.5.4.tgz", "integrity": "sha1-2jfOvTHnmhNn6UG1ku0fvr1Yq7s=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^4.11.9", "brorand": "^1.1.0", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.1", "inherits": "^2.0.4", "minimalistic-assert": "^1.0.1", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/elliptic/node_modules/bn.js": {"version": "4.12.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/bn.js/-/bn.js-4.12.0.tgz", "integrity": "sha1-d1s/J477uXGO7HNh9IP7Nvu/6og=", "dev": true, "license": "MIT"}, "node_modules/emoji-regex": {"version": "7.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/emoji-regex/7.0.3/emoji-regex-7.0.3.tgz", "integrity": "sha1-kzoEBShgyF6DwSJHnEdIqOTHIVY=", "dev": true, "license": "MIT"}, "node_modules/emojis-list": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/emojis-list/3.0.0/emojis-list-3.0.0.tgz", "integrity": "sha1-VXBmIEatKeLpFucariYKvf9Pang=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/encodeurl": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/encodeurl/1.0.2/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/end-of-stream/-/end-of-stream-1.4.0.tgz", "integrity": "sha1-epDYM+/abPpurA9JSduw+tOmMgY=", "dev": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/enhanced-resolve": {"version": "4.5.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/enhanced-resolve/4.5.0/enhanced-resolve-4.5.0.tgz", "integrity": "sha1-Lzz9hNvjtIfxjy2y7x4GSlccpew=", "dev": true, "dependencies": {"graceful-fs": "^4.1.2", "memory-fs": "^0.5.0", "tapable": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/enhanced-resolve/node_modules/memory-fs": {"version": "0.5.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/memory-fs/0.5.0/memory-fs-0.5.0.tgz", "integrity": "sha1-MkwBKIuIZSlm0WHbd4OHIIRajjw=", "dev": true, "license": "MIT", "dependencies": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "node_modules/entities": {"version": "2.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/entities/2.2.0/entities-2.2.0.tgz", "integrity": "sha1-CY3JDruD2N/6CJ1VJWs1HTTE2lU=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/errno": {"version": "0.1.8", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/errno/0.1.8/errno-0.1.8.tgz", "integrity": "sha1-i7Ppx9Rjvkl2/4iPdrSAnrwugR8=", "dev": true, "license": "MIT", "dependencies": {"prr": "~1.0.1"}, "bin": {"errno": "cli.js"}}, "node_modules/es-abstract": {"version": "1.22.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/es-abstract/-/es-abstract-1.22.1.tgz", "integrity": "sha1-i05fxc79fxZg8PjhpSkA37ydnMw=", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.0", "arraybuffer.prototype.slice": "^1.0.1", "available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "es-set-tostringtag": "^2.0.1", "es-to-primitive": "^1.2.1", "function.prototype.name": "^1.1.5", "get-intrinsic": "^1.2.1", "get-symbol-description": "^1.0.0", "globalthis": "^1.0.3", "gopd": "^1.0.1", "has": "^1.0.3", "has-property-descriptors": "^1.0.0", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "internal-slot": "^1.0.5", "is-array-buffer": "^3.0.2", "is-callable": "^1.2.7", "is-negative-zero": "^2.0.2", "is-regex": "^1.1.4", "is-shared-array-buffer": "^1.0.2", "is-string": "^1.0.7", "is-typed-array": "^1.1.10", "is-weakref": "^1.0.2", "object-inspect": "^1.12.3", "object-keys": "^1.1.1", "object.assign": "^4.1.4", "regexp.prototype.flags": "^1.5.0", "safe-array-concat": "^1.0.0", "safe-regex-test": "^1.0.0", "string.prototype.trim": "^1.2.7", "string.prototype.trimend": "^1.0.6", "string.prototype.trimstart": "^1.0.6", "typed-array-buffer": "^1.0.0", "typed-array-byte-length": "^1.0.0", "typed-array-byte-offset": "^1.0.0", "typed-array-length": "^1.0.4", "unbox-primitive": "^1.0.2", "which-typed-array": "^1.1.10"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/es-array-method-boxes-properly": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/es-array-method-boxes-properly/-/es-array-method-boxes-properly-1.0.0.tgz", "integrity": "sha1-hz8+hEGN5O4Zxb51KZCy5EcY0J4=", "dev": true, "license": "MIT"}, "node_modules/es-set-tostringtag": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/es-set-tostringtag/-/es-set-tostringtag-2.0.1.tgz", "integrity": "sha1-M41QL29nQwHXELgMhZLeihXwnNg=", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.3", "has": "^1.0.3", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-to-primitive": {"version": "1.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/es-to-primitive/1.2.1/es-to-primitive-1.2.1.tgz", "integrity": "sha1-5VzUyc3BiLzvsDs2bHNjI/xciYo=", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/escalade": {"version": "3.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/escalade/3.1.1/escalade-3.1.1.tgz", "integrity": "sha1-2M/ccACWXFoBdLSoLqpcBVJ0LkA=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/escape-html/1.0.3/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "dev": true, "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/escape-string-regexp/1.0.5/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/eslint-scope": {"version": "4.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/eslint-scope/4.0.3/eslint-scope-4.0.3.tgz", "integrity": "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=4.0.0"}}, "node_modules/esrecurse": {"version": "4.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/esrecurse/-/esrecurse-4.1.0.tgz", "integrity": "sha1-RxO2U2rffyrE8yfVWed1a/9kgiA=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "~4.1.0", "object-assign": "^4.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/esrecurse/node_modules/estraverse": {"version": "4.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/estraverse/-/estraverse-4.1.1.tgz", "integrity": "sha1-9srKcokzqFDvkGYdDheYK6RxEaI=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/estraverse": {"version": "4.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/estraverse/4.3.0/estraverse-4.3.0.tgz", "integrity": "sha1-OYrT88WiSUi+dyXoPRGn3ijNvR0=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/etag/1.8.1/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/eventemitter3": {"version": "4.0.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/eventemitter3/4.0.7/eventemitter3-4.0.7.tgz", "integrity": "sha1-Lem2j2Uo1WRO9cWVJqG0oHMGFp8=", "dev": true, "license": "MIT"}, "node_modules/events": {"version": "3.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/events/-/events-3.3.0.tgz", "integrity": "sha1-Mala0Kkk4tLEGagTrrLE6HjqdAA=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/eventsource": {"version": "1.0.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/eventsource/1.0.7/eventsource-1.0.7.tgz", "integrity": "sha1-j7xyyT/NNAiAkLwKTmT0tc7m2NA=", "dev": true, "license": "MIT", "dependencies": {"original": "^1.0.0"}, "engines": {"node": ">=0.12.0"}}, "node_modules/evp_bytestokey": {"version": "1.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/evp_bytestokey/1.0.3/evp_bytestokey-1.0.3.tgz", "integrity": "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=", "dev": true, "license": "MIT", "dependencies": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "node_modules/execa": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/execa/1.0.0/execa-1.0.0.tgz", "integrity": "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/expand-brackets": {"version": "2.1.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/expand-brackets/2.1.4/expand-brackets-2.1.4.tgz", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "dev": true, "license": "MIT", "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/debug": {"version": "2.6.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/debug/2.6.9/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/expand-brackets/node_modules/define-property": {"version": "0.2.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/define-property/0.2.5/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/extend-shallow/2.0.1/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-accessor-descriptor": {"version": "0.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-accessor-descriptor/0.1.6/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-accessor-descriptor/node_modules/kind-of": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/-/kind-of-3.1.0.tgz", "integrity": "sha1-R11pil5J/15T0U4+cyQp3Iv0z0c=", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-data-descriptor": {"version": "0.1.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-data-descriptor/0.1.4/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-data-descriptor/node_modules/kind-of": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/-/kind-of-3.1.0.tgz", "integrity": "sha1-R11pil5J/15T0U4+cyQp3Iv0z0c=", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-descriptor": {"version": "0.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-descriptor/0.1.6/is-descriptor-0.1.6.tgz", "integrity": "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=", "dev": true, "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-extendable/0.1.1/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/kind-of": {"version": "5.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/5.1.0/kind-of-5.1.0.tgz", "integrity": "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/ms": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ms/2.0.0/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/expand-tilde": {"version": "2.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/expand-tilde/2.0.2/expand-tilde-2.0.2.tgz", "integrity": "sha1-l+gBqgUt8CRU3kawK/YhZCzchQI=", "dev": true, "license": "MIT", "dependencies": {"homedir-polyfill": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/express": {"version": "4.18.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/express/-/express-4.18.2.tgz", "integrity": "sha1-P6vggpbpMMeWwZ48UWl5OGup/Vk=", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.1", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.5.0", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.2.0", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.7", "qs": "6.11.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.18.0", "serve-static": "1.15.0", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/express/node_modules/array-flatten": {"version": "1.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/array-flatten/1.1.1/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=", "dev": true, "license": "MIT"}, "node_modules/express/node_modules/debug": {"version": "2.6.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/debug/2.6.9/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/express/node_modules/ms": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ms/2.0.0/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/express/node_modules/qs": {"version": "6.11.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/qs/-/qs-6.11.0.tgz", "integrity": "sha1-/Q2WNEb3pl4TZ+AavYVClFPww3o=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.4"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/extend-shallow": {"version": "3.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/extend-shallow/3.0.2/extend-shallow-3.0.2.tgz", "integrity": "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=", "dev": true, "license": "MIT", "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob": {"version": "2.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/extglob/2.0.4/extglob-2.0.4.tgz", "integrity": "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=", "dev": true, "license": "MIT", "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/define-property": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/define-property/1.0.0/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/extend-shallow/2.0.1/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-extendable/0.1.1/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/fast-deep-equal/3.1.3/fast-deep-equal-3.1.3.tgz", "integrity": "sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=", "dev": true, "license": "MIT"}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/fast-json-stable-stringify/2.1.0/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=", "dev": true, "license": "MIT"}, "node_modules/faye-websocket": {"version": "0.10.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/faye-websocket/0.10.0/faye-websocket-0.10.0.tgz", "integrity": "sha1-TkkvjQTftviQA1B/btvy1QHnxvQ=", "dev": true, "license": "MIT", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.4.0"}}, "node_modules/figgy-pudding": {"version": "3.5.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/figgy-pudding/3.5.2/figgy-pudding-3.5.2.tgz", "integrity": "sha1-tO7oFIq7Adzx0aw0Nn1Z4S+mHW4=", "dev": true, "license": "ISC"}, "node_modules/file-uri-to-path": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/file-uri-to-path/1.0.0/file-uri-to-path-1.0.0.tgz", "integrity": "sha1-VTp7hEb/b2hDWcRF8eN6BdrMM90=", "dev": true, "license": "MIT", "optional": true}, "node_modules/fill-range": {"version": "4.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/fill-range/4.0.0/fill-range-4.0.0.tgz", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "dev": true, "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fill-range/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/extend-shallow/2.0.1/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fill-range/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-extendable/0.1.1/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/finalhandler": {"version": "1.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/finalhandler/-/finalhandler-1.2.0.tgz", "integrity": "sha1-fSP+VzGyB7RkDk/NAK7B+SB6ezI=", "dev": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/debug/2.6.9/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ms/2.0.0/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/find-cache-dir": {"version": "2.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/find-cache-dir/2.1.0/find-cache-dir-2.1.0.tgz", "integrity": "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=", "dev": true, "license": "MIT", "dependencies": {"commondir": "^1.0.1", "make-dir": "^2.0.0", "pkg-dir": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/find-up": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/find-up/3.0.0/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/findup-sync": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/findup-sync/3.0.0/findup-sync-3.0.0.tgz", "integrity": "sha1-F7EI+e5RLft6XH88iyfqnhqcCNE=", "dev": true, "license": "MIT", "dependencies": {"detect-file": "^1.0.0", "is-glob": "^4.0.0", "micromatch": "^3.0.4", "resolve-dir": "^1.0.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/flush-write-stream": {"version": "1.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/flush-write-stream/1.1.1/flush-write-stream-1.1.1.tgz", "integrity": "sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "readable-stream": "^2.3.6"}}, "node_modules/follow-redirects": {"version": "1.15.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/follow-redirects/-/follow-redirects-1.15.6.tgz", "integrity": "sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/for-each": {"version": "0.3.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/for-each/0.3.3/for-each-0.3.3.tgz", "integrity": "sha1-abRH6IoKXTLD5whPPxcQA0shN24=", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.1.3"}}, "node_modules/for-in": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/for-in/1.0.2/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/forwarded": {"version": "0.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/forwarded/-/forwarded-0.2.0.tgz", "integrity": "sha1-ImmTZCiq1MFcfr6XeahL8LKoGBE=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fragment-cache": {"version": "0.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/fragment-cache/0.2.1/fragment-cache-0.2.1.tgz", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "dev": true, "license": "MIT", "dependencies": {"map-cache": "^0.2.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/fresh/0.5.2/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/from2": {"version": "2.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/from2/2.3.0/from2-2.3.0.tgz", "integrity": "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "node_modules/fs-write-stream-atomic": {"version": "1.0.10", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/fs-write-stream-atomic/1.0.10/fs-write-stream-atomic-1.0.10.tgz", "integrity": "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=", "dev": true, "license": "ISC", "dependencies": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/fs.realpath/1.0.0/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true, "license": "ISC"}, "node_modules/fsevents": {"version": "2.3.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/fsevents/-/fsevents-2.3.3.tgz", "integrity": "sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=", "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/function-bind/1.1.1/function-bind-1.1.1.tgz", "integrity": "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=", "dev": true, "license": "MIT"}, "node_modules/function.prototype.name": {"version": "1.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/function.prototype.name/-/function.prototype.name-1.1.6.tgz", "integrity": "sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.22.1", "functions-have-names": "^1.2.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/functions-have-names/-/functions-have-names-1.2.3.tgz", "integrity": "sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/gensync/1.0.0-beta.2/gensync-1.0.0-beta.2.tgz", "integrity": "sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/get-caller-file/2.0.5/get-caller-file-2.0.5.tgz", "integrity": "sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=", "dev": true, "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/get-intrinsic/-/get-intrinsic-1.2.1.tgz", "integrity": "sha1-0pVkT+1FBfyc3pUsN+4StHeoPYI=", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1", "has": "^1.0.3", "has-proto": "^1.0.1", "has-symbols": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-stream": {"version": "4.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/get-stream/4.1.0/get-stream-4.1.0.tgz", "integrity": "sha1-wbJVV189wh1Zv8ec09K0axw6VLU=", "dev": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/get-symbol-description": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/get-symbol-description/-/get-symbol-description-1.0.0.tgz", "integrity": "sha1-f9uByQAQH71WTdXxowr1qtweWNY=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-value": {"version": "2.0.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/get-value/2.0.6/get-value-2.0.6.tgz", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/glob/-/glob-7.2.3.tgz", "integrity": "sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/glob-parent/-/glob-parent-5.1.2.tgz", "integrity": "sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=", "dev": true, "license": "ISC", "optional": true, "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/global-modules": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/global-modules/2.0.0/global-modules-2.0.0.tgz", "integrity": "sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=", "dev": true, "license": "MIT", "dependencies": {"global-prefix": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/global-prefix": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/global-prefix/3.0.0/global-prefix-3.0.0.tgz", "integrity": "sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=", "dev": true, "license": "MIT", "dependencies": {"ini": "^1.3.5", "kind-of": "^6.0.2", "which": "^1.3.1"}, "engines": {"node": ">=6"}}, "node_modules/globals": {"version": "11.12.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/globals/11.12.0/globals-11.12.0.tgz", "integrity": "sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/globalthis": {"version": "1.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/globalthis/-/globalthis-1.0.3.tgz", "integrity": "sha1-WFKIKlK4DcMBsGYCc+HtCC8LbM8=", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.1.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/globby": {"version": "6.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/globby/6.1.0/globby-6.1.0.tgz", "integrity": "sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=", "dev": true, "license": "MIT", "dependencies": {"array-union": "^1.0.1", "glob": "^7.0.3", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/globby/node_modules/pify": {"version": "2.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/pify/2.3.0/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/gopd": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/gopd/-/gopd-1.0.1.tgz", "integrity": "sha1-Kf923mnax0ibfAkYpXiOVkd8Myw=", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=", "dev": true, "license": "ISC"}, "node_modules/handle-thing": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/handle-thing/2.0.1/handle-thing-2.0.1.tgz", "integrity": "sha1-hX95zjWVgMNA1DCBzGSJcNC7I04=", "dev": true, "license": "MIT"}, "node_modules/has": {"version": "1.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/has/1.0.3/has-1.0.3.tgz", "integrity": "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-bigints": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/has-bigints/-/has-bigints-1.0.2.tgz", "integrity": "sha1-CHG9Pj1RYm9soJZmaLo11WAtbqo=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-flag": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/has-flag/3.0.0/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/has-property-descriptors": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/has-property-descriptors/-/has-property-descriptors-1.0.0.tgz", "integrity": "sha1-YQcIYAYG02lh7QTBlhk7amB/qGE=", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.1"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/has-proto/-/has-proto-1.0.1.tgz", "integrity": "sha1-GIXBMFU4lYr/Rp/vN5N8InlUCOA=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/has-symbols/-/has-symbols-1.0.3.tgz", "integrity": "sha1-u3ssQ0klHc6HsSX3vfh0qnyLOfg=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/has-tostringtag/-/has-tostringtag-1.0.0.tgz", "integrity": "sha1-fhM4GKfTlHNPlB5zw9P5KR5liyU=", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-value": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/has-value/1.0.0/has-value-1.0.0.tgz", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "dev": true, "license": "MIT", "dependencies": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/has-values/1.0.0/has-values-1.0.0.tgz", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "dev": true, "license": "MIT", "dependencies": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values/node_modules/kind-of": {"version": "4.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/4.0.0/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/hash-base": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/hash-base/3.1.0/hash-base-3.1.0.tgz", "integrity": "sha1-VcOB2eBuHSmXqIO0o/3f5/DTrzM=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.4", "readable-stream": "^3.6.0", "safe-buffer": "^5.2.0"}, "engines": {"node": ">=4"}}, "node_modules/hash-base/node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/hash.js": {"version": "1.1.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/hash.js/-/hash.js-1.1.5.tgz", "integrity": "sha1-44q0uF37HgxA/pJlwOm1SFTCOBI=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/he": {"version": "1.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/he/-/he-1.2.0.tgz", "integrity": "sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==", "dev": true, "license": "MIT", "bin": {"he": "bin/he"}}, "node_modules/hmac-drbg": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/hmac-drbg/1.0.1/hmac-drbg-1.0.1.tgz", "integrity": "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=", "dev": true, "license": "MIT", "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/homedir-polyfill": {"version": "1.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/homedir-polyfill/1.0.3/homedir-polyfill-1.0.3.tgz", "integrity": "sha1-dDKYzvTlrz4ZQWH7rcwhUdOgWOg=", "dev": true, "license": "MIT", "dependencies": {"parse-passwd": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/hpack.js": {"version": "2.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/hpack.js/2.1.6/hpack.js-2.1.6.tgz", "integrity": "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}}, "node_modules/html-entities": {"version": "1.4.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/html-entities/1.4.0/html-entities-1.4.0.tgz", "integrity": "sha1-z70bAdKvr5rcobEK59/6uYxx0tw=", "dev": true, "license": "MIT"}, "node_modules/html-minifier-terser": {"version": "5.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/html-minifier-terser/5.1.1/html-minifier-terser-5.1.1.tgz", "integrity": "sha1-ki6W8fO7YIMsJjS3mIQJY4mx8FQ=", "dev": true, "license": "MIT", "dependencies": {"camel-case": "^4.1.1", "clean-css": "^4.2.3", "commander": "^4.1.1", "he": "^1.2.0", "param-case": "^3.0.3", "relateurl": "^0.2.7", "terser": "^4.6.3"}, "bin": {"html-minifier-terser": "cli.js"}, "engines": {"node": ">=6"}}, "node_modules/html-webpack-plugin": {"version": "4.4.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/html-webpack-plugin/-/html-webpack-plugin-4.4.1.tgz", "integrity": "sha1-YauFqhqEuhgUQzReuurVGru4QUk=", "dev": true, "license": "MIT", "dependencies": {"@types/html-minifier-terser": "^5.0.0", "@types/tapable": "^1.0.5", "@types/webpack": "^4.41.8", "html-minifier-terser": "^5.0.1", "loader-utils": "^1.2.3", "lodash": "^4.17.15", "pretty-error": "^2.1.1", "tapable": "^1.1.3", "util.promisify": "1.0.0"}, "engines": {"node": ">=6.9"}, "peerDependencies": {"webpack": ">=4.0.0 < 6.0.0"}}, "node_modules/htmlparser2": {"version": "6.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/htmlparser2/-/htmlparser2-6.1.0.tgz", "integrity": "sha1-xNditsM3GgXb5l6UrkOp+EX7j7c=", "dev": true, "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "domutils": "^2.5.2", "entities": "^2.0.0"}}, "node_modules/http-deceiver": {"version": "1.2.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/http-deceiver/1.2.7/http-deceiver-1.2.7.tgz", "integrity": "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=", "dev": true, "license": "MIT"}, "node_modules/http-errors": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/http-errors/-/http-errors-2.0.0.tgz", "integrity": "sha1-t3dKFIbvc892Z6ya4IWMASxXudM=", "dev": true, "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-proxy": {"version": "1.18.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/http-proxy/1.18.1/http-proxy-1.18.1.tgz", "integrity": "sha1-QBVB8FNIhLv5UmAzTnL4juOXZUk=", "dev": true, "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-proxy-middleware": {"version": "0.19.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/http-proxy-middleware/0.19.1/http-proxy-middleware-0.19.1.tgz", "integrity": "sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=", "dev": true, "license": "MIT", "dependencies": {"http-proxy": "^1.17.0", "is-glob": "^4.0.0", "lodash": "^4.17.11", "micromatch": "^3.1.10"}, "engines": {"node": ">=4.0.0"}}, "node_modules/https-browserify": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/https-browserify/1.0.0/https-browserify-1.0.0.tgz", "integrity": "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=", "dev": true, "license": "MIT"}, "node_modules/iconv-lite": {"version": "0.4.24", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/iconv-lite/0.4.24/iconv-lite-0.4.24.tgz", "integrity": "sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/icss-utils": {"version": "4.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/icss-utils/4.1.1/icss-utils-4.1.1.tgz", "integrity": "sha1-IRcLU3ie4nRHwvR91oMIFAP5pGc=", "dev": true, "license": "ISC", "dependencies": {"postcss": "^7.0.14"}, "engines": {"node": ">= 6"}}, "node_modules/ieee754": {"version": "1.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ieee754/1.2.1/ieee754-1.2.1.tgz", "integrity": "sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/iferr": {"version": "0.1.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/iferr/0.1.5/iferr-0.1.5.tgz", "integrity": "sha1-xg7taebY/bazEEofy8ocGS3FtQE=", "dev": true, "license": "MIT"}, "node_modules/import-local": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/import-local/2.0.0/import-local-2.0.0.tgz", "integrity": "sha1-VQcL44pZk88Y72236WH1vuXFoJ0=", "dev": true, "license": "MIT", "dependencies": {"pkg-dir": "^3.0.0", "resolve-cwd": "^2.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/imurmurhash/0.1.4/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/infer-owner": {"version": "1.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/infer-owner/1.0.4/infer-owner-1.0.4.tgz", "integrity": "sha1-xM78qo5RBRwqQLos6KPScpWvlGc=", "dev": true, "license": "ISC"}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/inflight/1.0.6/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/inherits/2.0.4/inherits-2.0.4.tgz", "integrity": "sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=", "dev": true, "license": "ISC"}, "node_modules/ini": {"version": "1.3.8", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ini/1.3.8/ini-1.3.8.tgz", "integrity": "sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=", "dev": true, "license": "ISC"}, "node_modules/internal-ip": {"version": "4.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/internal-ip/4.3.0/internal-ip-4.3.0.tgz", "integrity": "sha1-hFRSuq2dLKO2nGNaE3rLmg2tCQc=", "dev": true, "license": "MIT", "dependencies": {"default-gateway": "^4.2.0", "ipaddr.js": "^1.9.0"}, "engines": {"node": ">=6"}}, "node_modules/internal-slot": {"version": "1.0.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/internal-slot/-/internal-slot-1.0.5.tgz", "integrity": "sha1-8qLuIfZo+GJ6RmfzCdwPT7ZnSYY=", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.0", "has": "^1.0.3", "side-channel": "^1.0.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/interpret": {"version": "1.4.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/interpret/1.4.0/interpret-1.4.0.tgz", "integrity": "sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/invariant": {"version": "2.2.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/invariant/-/invariant-2.2.4.tgz", "integrity": "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=", "dev": true, "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/ip": {"version": "1.1.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ip/1.1.5/ip-1.1.5.tgz", "integrity": "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=", "dev": true, "license": "MIT"}, "node_modules/ip-regex": {"version": "2.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ip-regex/2.1.0/ip-regex-2.1.0.tgz", "integrity": "sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/ipaddr.js": {"version": "1.9.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ipaddr.js/-/ipaddr.js-1.9.1.tgz", "integrity": "sha1-v/OFQ+64mEglB5/zoqjmy9RngbM=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/is-absolute-url": {"version": "3.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-absolute-url/3.0.3/is-absolute-url-3.0.3.tgz", "integrity": "sha1-lsaiK2ojkpsR6gr7GDbDatSl1pg=", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-accessor-descriptor": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-accessor-descriptor/1.0.0/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-arguments": {"version": "1.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-arguments/1.1.0/is-arguments-1.1.0.tgz", "integrity": "sha1-YjUwMd++4HzrNGVqa95Z7+yujdk=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-array-buffer": {"version": "3.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-array-buffer/-/is-array-buffer-3.0.2.tgz", "integrity": "sha1-8mU87YQSCBY47LDrvQxBxuCuy74=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.0", "is-typed-array": "^1.1.10"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-bigint": {"version": "1.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-bigint/-/is-bigint-1.0.3.tgz", "integrity": "sha1-/J2eNkIQSAZ1ZT3a6gUYUo1JpYE=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-binary-path": {"version": "2.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-binary-path/2.1.0/is-binary-path-2.1.0.tgz", "integrity": "sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-boolean-object": {"version": "1.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-boolean-object/-/is-boolean-object-1.1.2.tgz", "integrity": "sha1-XG3CACRt2TIa5LiFoRS7H3X2Nxk=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-buffer": {"version": "1.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-buffer/1.1.6/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4=", "dev": true, "license": "MIT"}, "node_modules/is-callable": {"version": "1.2.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-callable/-/is-callable-1.2.7.tgz", "integrity": "sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-core-module": {"version": "2.13.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-core-module/-/is-core-module-2.13.0.tgz", "integrity": "sha1-u1Kqbiy9SaMMK6aMQr80Nbpgcts=", "dev": true, "license": "MIT", "dependencies": {"has": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-data-descriptor": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-data-descriptor/1.0.0/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-date-object": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-date-object/1.0.2/is-date-object-1.0.2.tgz", "integrity": "sha1-vac28s2P0G0yhE53Q7+nSUw7/X4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-descriptor": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-descriptor/1.0.2/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "license": "MIT", "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-extendable": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-extendable/1.0.1/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "dev": true, "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-extglob/2.1.1/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-fullwidth-code-point/2.0.0/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/is-glob": {"version": "4.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-glob/4.0.1/is-glob-4.0.1.tgz", "integrity": "sha1-dWfb6fL14kZ7x3q4PEopSCQHpdw=", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-negative-zero": {"version": "2.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-negative-zero/-/is-negative-zero-2.0.2.tgz", "integrity": "sha1-e/bwOigAO4s5Zd46wm9mTXZfMVA=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-number/3.0.0/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number-object": {"version": "1.0.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-number-object/-/is-number-object-1.0.7.tgz", "integrity": "sha1-WdUK2kxFJReE6ZBPUkbHQvB6Qvw=", "dev": true, "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-number/node_modules/kind-of": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/-/kind-of-3.1.0.tgz", "integrity": "sha1-R11pil5J/15T0U4+cyQp3Iv0z0c=", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-odd": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-odd/-/is-odd-2.0.0.tgz", "integrity": "sha1-dkZiRnH9fqVYzNmieVGC8pWPGyQ=", "dev": true, "license": "MIT", "dependencies": {"is-number": "^4.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-odd/node_modules/is-number": {"version": "4.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-number/4.0.0/is-number-4.0.0.tgz", "integrity": "sha1-ACbjf1RU1z41bf5lZGmYZ8an8P8=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-path-cwd": {"version": "2.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-path-cwd/2.2.0/is-path-cwd-2.2.0.tgz", "integrity": "sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/is-path-in-cwd": {"version": "2.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-path-in-cwd/2.1.0/is-path-in-cwd-2.1.0.tgz", "integrity": "sha1-v+Lcomxp85cmWkAJljYCk1oFOss=", "dev": true, "license": "MIT", "dependencies": {"is-path-inside": "^2.1.0"}, "engines": {"node": ">=6"}}, "node_modules/is-path-inside": {"version": "2.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-path-inside/2.1.0/is-path-inside-2.1.0.tgz", "integrity": "sha1-fJgQWH1lmkDSe8201WFuqwWUlLI=", "dev": true, "license": "MIT", "dependencies": {"path-is-inside": "^1.0.2"}, "engines": {"node": ">=6"}}, "node_modules/is-plain-object": {"version": "2.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-plain-object/2.0.4/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "dev": true, "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-regex": {"version": "1.1.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-regex/-/is-regex-1.1.4.tgz", "integrity": "sha1-7vVmPNWfpMCuM5UFMj32hUuxWVg=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-shared-array-buffer": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-shared-array-buffer/-/is-shared-array-buffer-1.0.2.tgz", "integrity": "sha1-jyWcVztgtqMtQFihoHQwwKc0THk=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-stream": {"version": "1.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-stream/1.1.0/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-string": {"version": "1.0.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-string/-/is-string-1.0.7.tgz", "integrity": "sha1-DdEr8gBvJVu1j2lREO/3SR7rwP0=", "dev": true, "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-symbol": {"version": "1.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-symbol/1.0.3/is-symbol-1.0.3.tgz", "integrity": "sha1-OOEBS55jKb4N6dJKQU/XRB7GGTc=", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-typed-array": {"version": "1.1.12", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-typed-array/-/is-typed-array-1.1.12.tgz", "integrity": "sha1-0Lq1aG70p296cwl7lUcKsZnFfUo=", "dev": true, "license": "MIT", "dependencies": {"which-typed-array": "^1.1.11"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-weakref": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-weakref/-/is-weakref-1.0.2.tgz", "integrity": "sha1-lSnzg6kzggXol2XgOS78LxAPBvI=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-windows": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-windows/1.0.2/is-windows-1.0.2.tgz", "integrity": "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-wsl": {"version": "1.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-wsl/1.1.0/is-wsl-1.1.0.tgz", "integrity": "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/isarray": {"version": "2.0.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/isarray/2.0.5/isarray-2.0.5.tgz", "integrity": "sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=", "dev": true, "license": "MIT"}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/isexe/2.0.0/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true, "license": "ISC"}, "node_modules/isobject": {"version": "3.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/isobject/3.0.1/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/js-levenshtein": {"version": "1.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/js-levenshtein/1.1.6/js-levenshtein-1.1.6.tgz", "integrity": "sha1-xs7ljrNVA3LfjeuF+tXOZs4B1Z0=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/js-tokens/4.0.0/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true, "license": "MIT"}, "node_modules/jsesc": {"version": "2.5.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/jsesc/2.5.2/jsesc-2.5.2.tgz", "integrity": "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=4"}}, "node_modules/json-parse-better-errors": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/json-parse-better-errors/1.0.2/json-parse-better-errors-1.0.2.tgz", "integrity": "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/json-schema-traverse/0.4.1/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true, "license": "MIT"}, "node_modules/json3": {"version": "3.3.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/json3/3.3.3/json3-3.3.3.tgz", "integrity": "sha1-f8EON1/FrkLEcFpcwKpvYr4wW4E=", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/json5/-/json5-2.2.3.tgz", "integrity": "sha1-eM1vGhm9wStz21rQxh79ZsHikoM=", "dev": true, "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/killable": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/killable/1.0.1/killable-1.0.1.tgz", "integrity": "sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=", "dev": true, "license": "ISC"}, "node_modules/kind-of": {"version": "6.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/6.0.3/kind-of-6.0.3.tgz", "integrity": "sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/loader-runner": {"version": "2.4.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/loader-runner/2.4.0/loader-runner-2.4.0.tgz", "integrity": "sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=", "dev": true, "license": "MIT", "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "node_modules/loader-utils": {"version": "1.4.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/loader-utils/-/loader-utils-1.4.2.tgz", "integrity": "sha1-KalX86Y5c4g+toTxD/09FR/sAaM=", "dev": true, "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^1.0.1"}, "engines": {"node": ">=4.0.0"}}, "node_modules/loader-utils/node_modules/json5": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/json5/-/json5-1.0.2.tgz", "integrity": "sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/locate-path": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/locate-path/3.0.0/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/lodash/4.17.21/lodash-4.17.21.tgz", "integrity": "sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=", "dev": true, "license": "MIT"}, "node_modules/loglevel": {"version": "1.8.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/loglevel/-/loglevel-1.8.1.tgz", "integrity": "sha1-XGIfg9W0jFSuk7YVY1P1VZYzd7Q=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6.0"}, "funding": {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/loglevel"}}, "node_modules/loose-envify": {"version": "1.4.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/loose-envify/-/loose-envify-1.4.0.tgz", "integrity": "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=", "dev": true, "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lower-case": {"version": "2.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/lower-case/2.0.2/lower-case-2.0.2.tgz", "integrity": "sha1-b6I3xj29xKgsoP2ILkci3F5jTig=", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.3"}}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/lru-cache/5.1.1/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/make-dir": {"version": "2.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/make-dir/2.1.0/make-dir-2.1.0.tgz", "integrity": "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=", "dev": true, "license": "MIT", "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "engines": {"node": ">=6"}}, "node_modules/map-cache": {"version": "0.2.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/map-cache/0.2.2/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/map-visit": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/map-visit/1.0.0/map-visit-1.0.0.tgz", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "dev": true, "license": "MIT", "dependencies": {"object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/md5.js": {"version": "1.3.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/md5.js/1.3.5/md5.js-1.3.5.tgz", "integrity": "sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=", "dev": true, "license": "MIT", "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/media-typer/0.3.0/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/memory-fs": {"version": "0.4.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/memory-fs/0.4.1/memory-fs-0.4.1.tgz", "integrity": "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=", "dev": true, "license": "MIT", "dependencies": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}}, "node_modules/merge-descriptors": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/merge-descriptors/1.0.1/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=", "dev": true, "license": "MIT"}, "node_modules/methods": {"version": "1.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/methods/1.1.2/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "3.1.10", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/micromatch/3.1.10/micromatch-3.1.10.tgz", "integrity": "sha1-cIWbyVyYQJUvNZoGij/En57PrCM=", "dev": true, "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/miller-rabin": {"version": "4.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/miller-rabin/-/miller-rabin-4.0.0.tgz", "integrity": "sha1-SmL7HUKTPAVYOYL0xxb2+55sbT0=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}, "bin": {"miller-rabin": "bin/miller-rabin"}}, "node_modules/miller-rabin/node_modules/bn.js": {"version": "4.11.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/bn.js/-/bn.js-4.11.6.tgz", "integrity": "sha1-UzRK2xRhehP26N0s4okF0cC6MhU=", "dev": true, "license": "MIT"}, "node_modules/mime": {"version": "1.6.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/mime/1.6.0/mime-1.6.0.tgz", "integrity": "sha1-Ms2eXGRVO9WNGaVor0Uqz/BJgbE=", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/mime-db": {"version": "1.52.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/mime-db/-/mime-db-1.52.0.tgz", "integrity": "sha1-u6vNwChZ9JhzAchW4zh85exDv3A=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/mime-types/-/mime-types-2.1.35.tgz", "integrity": "sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=", "dev": true, "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/minimalistic-assert/1.0.1/minimalistic-assert-1.0.1.tgz", "integrity": "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=", "dev": true, "license": "ISC"}, "node_modules/minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/minimalistic-crypto-utils/1.0.1/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=", "dev": true, "license": "MIT"}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/minimist/-/minimist-1.2.8.tgz", "integrity": "sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/mississippi": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/mississippi/3.0.0/mississippi-3.0.0.tgz", "integrity": "sha1-6goykfl+C16HdrNj1fChLZTGcCI=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^3.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/mixin-deep": {"version": "1.3.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/mixin-deep/1.3.2/mixin-deep-1.3.2.tgz", "integrity": "sha1-ESC0PcNZp4Xc5ltVuC4lfM9HlWY=", "dev": true, "license": "MIT", "dependencies": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mkdirp": {"version": "0.5.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/mkdirp/-/mkdirp-0.5.6.tgz", "integrity": "sha1-fe8D0kMtyuS6HWEURcSDlgYiVfY=", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/move-concurrently": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/move-concurrently/1.0.1/move-concurrently-1.0.1.tgz", "integrity": "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=", "dev": true, "license": "ISC", "dependencies": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}}, "node_modules/ms": {"version": "2.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ms/2.1.2/ms-2.1.2.tgz", "integrity": "sha1-0J0fNXtEP0kzgqjrPM0YOHKuYAk=", "dev": true, "license": "MIT"}, "node_modules/multicast-dns": {"version": "6.2.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/multicast-dns/6.2.3/multicast-dns-6.2.3.tgz", "integrity": "sha1-oOx72QVcQoL3kMPIL04o2zsxsik=", "dev": true, "license": "MIT", "dependencies": {"dns-packet": "^1.3.1", "thunky": "^1.0.2"}, "bin": {"multicast-dns": "cli.js"}}, "node_modules/multicast-dns-service-types": {"version": "1.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/multicast-dns-service-types/1.1.0/multicast-dns-service-types-1.1.0.tgz", "integrity": "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=", "dev": true, "license": "MIT"}, "node_modules/nan": {"version": "2.17.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/nan/-/nan-2.17.0.tgz", "integrity": "sha1-wBUKI2ihgvAz6apRlex26kGhmcs=", "dev": true, "license": "MIT", "optional": true}, "node_modules/nanomatch": {"version": "1.2.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/nanomatch/-/nanomatch-1.2.9.tgz", "integrity": "sha1-h59xUMstq3pHElkGbBBO7m4Pp8I=", "dev": true, "license": "MIT", "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-odd": "^2.0.0", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/negotiator": {"version": "0.6.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha1-WOMjpy/twNb5zU0x/kn1FHlZDM0=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/neo-async": {"version": "2.6.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/neo-async/2.6.2/neo-async-2.6.2.tgz", "integrity": "sha1-tKr7k+OustgXTKU88WOrfXMIMF8=", "dev": true, "license": "MIT"}, "node_modules/nice-try": {"version": "1.0.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/nice-try/1.0.5/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=", "dev": true, "license": "MIT"}, "node_modules/no-case": {"version": "3.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/no-case/3.0.4/no-case-3.0.4.tgz", "integrity": "sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=", "dev": true, "license": "MIT", "dependencies": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "node_modules/node-forge": {"version": "0.10.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/node-forge/0.10.0/node-forge-0.10.0.tgz", "integrity": "sha1-Mt6ir7Ppkm8C7lzoeUkCaRpna/M=", "dev": true, "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "engines": {"node": ">= 6.0.0"}}, "node_modules/node-libs-browser": {"version": "2.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/node-libs-browser/2.2.1/node-libs-browser-2.2.1.tgz", "integrity": "sha1-tk9RPRgzhiX5A0bSew0jXmMfZCU=", "dev": true, "license": "MIT", "dependencies": {"assert": "^1.1.1", "browserify-zlib": "^0.2.0", "buffer": "^4.3.0", "console-browserify": "^1.1.0", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.11.0", "domain-browser": "^1.1.1", "events": "^3.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "0.0.1", "process": "^0.11.10", "punycode": "^1.2.4", "querystring-es3": "^0.2.0", "readable-stream": "^2.3.3", "stream-browserify": "^2.0.1", "stream-http": "^2.7.2", "string_decoder": "^1.0.0", "timers-browserify": "^2.0.4", "tty-browserify": "0.0.0", "url": "^0.11.0", "util": "^0.11.0", "vm-browserify": "^1.0.1"}}, "node_modules/node-libs-browser/node_modules/punycode": {"version": "1.2.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/punycode/-/punycode-1.2.4.tgz", "integrity": "sha1-VACKyXKux0F13vnLpt9/qdORh0A=", "dev": true, "engines": ["node", "rhino"]}, "node_modules/node-releases": {"version": "2.0.13", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/node-releases/-/node-releases-2.0.13.tgz", "integrity": "sha1-1e0WJ8I+NGHoGbAuV7deSJmxyB0=", "dev": true, "license": "MIT"}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/normalize-path/3.0.0/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm-run-path": {"version": "2.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/npm-run-path/2.0.2/npm-run-path-2.0.2.tgz", "integrity": "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=", "dev": true, "license": "MIT", "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/nth-check": {"version": "2.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/nth-check/-/nth-check-2.1.1.tgz", "integrity": "sha1-yeq0KO/842zWuSySS9sADvHx7R0=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/object-assign/-/object-assign-4.1.1.tgz", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy": {"version": "0.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/object-copy/0.1.0/object-copy-0.1.0.tgz", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "dev": true, "license": "MIT", "dependencies": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/define-property": {"version": "0.2.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/define-property/0.2.5/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/is-accessor-descriptor": {"version": "0.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-accessor-descriptor/0.1.6/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/is-data-descriptor": {"version": "0.1.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-data-descriptor/0.1.4/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/is-descriptor": {"version": "0.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-descriptor/0.1.6/is-descriptor-0.1.6.tgz", "integrity": "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=", "dev": true, "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/is-descriptor/node_modules/kind-of": {"version": "5.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/5.1.0/kind-of-5.1.0.tgz", "integrity": "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/kind-of": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/-/kind-of-3.1.0.tgz", "integrity": "sha1-R11pil5J/15T0U4+cyQp3Iv0z0c=", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.12.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/object-inspect/-/object-inspect-1.12.3.tgz", "integrity": "sha1-umLf/WfuJWyMCG365p4BbNHxmLk=", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-is": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/object-is/-/object-is-1.0.2.tgz", "integrity": "sha1-a4DrhP5FFJj2UAeYLwNaW0Re3sQ=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/object-keys/1.1.1/object-keys-1.1.1.tgz", "integrity": "sha1-HEfyct8nfzsdrwYWd9nILiMixg4=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/object-visit": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/object-visit/1.0.1/object-visit-1.0.1.tgz", "integrity": "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=", "dev": true, "license": "MIT", "dependencies": {"isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.assign": {"version": "4.1.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/object.assign/-/object.assign-4.1.4.tgz", "integrity": "sha1-lnPHx8NRq4xNC1FvQ0Pr9N+3eZ8=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "has-symbols": "^1.0.3", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.getownpropertydescriptors": {"version": "2.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/object.getownpropertydescriptors/-/object.getownpropertydescriptors-2.1.6.tgz", "integrity": "sha1-Xlw4TdIJ+k7//q0546BRJ3DMwxI=", "dev": true, "license": "MIT", "dependencies": {"array.prototype.reduce": "^1.0.5", "call-bind": "^1.0.2", "define-properties": "^1.2.0", "es-abstract": "^1.21.2", "safe-array-concat": "^1.0.0"}, "engines": {"node": ">= 0.8"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object.pick": {"version": "1.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/object.pick/1.3.0/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "dev": true, "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/obuf": {"version": "1.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/obuf/1.1.2/obuf-1.1.2.tgz", "integrity": "sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=", "dev": true, "license": "MIT"}, "node_modules/on-finished": {"version": "2.4.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/on-finished/-/on-finished-2.4.1.tgz", "integrity": "sha1-WMjEQRblSEWtV/FKsQsDUzGErD8=", "dev": true, "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/on-headers/1.0.2/on-headers-1.0.2.tgz", "integrity": "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/once/1.4.0/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/opn": {"version": "5.5.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/opn/5.5.0/opn-5.5.0.tgz", "integrity": "sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=", "dev": true, "license": "MIT", "dependencies": {"is-wsl": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/original": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/original/1.0.2/original-1.0.2.tgz", "integrity": "sha1-5EKmHP/hxf0gpl8yYcJmY7MD8l8=", "dev": true, "license": "MIT", "dependencies": {"url-parse": "^1.4.3"}}, "node_modules/os-browserify": {"version": "0.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/os-browserify/0.3.0/os-browserify-0.3.0.tgz", "integrity": "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=", "dev": true, "license": "MIT"}, "node_modules/p-finally": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/p-finally/1.0.0/p-finally-1.0.0.tgz", "integrity": "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/p-limit": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/p-limit/-/p-limit-2.0.0.tgz", "integrity": "sha1-5iTtVO6MRgp3izyfNnBJb/ileuw=", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/p-locate": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/p-locate/3.0.0/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/p-map": {"version": "2.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/p-map/2.1.0/p-map-2.1.0.tgz", "integrity": "sha1-MQko/u+cnsxltosXaTAYpmXOoXU=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/p-retry": {"version": "3.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/p-retry/3.0.1/p-retry-3.0.1.tgz", "integrity": "sha1-MWtMiJPiyNwc+okfQGxLQivr8yg=", "dev": true, "license": "MIT", "dependencies": {"retry": "^0.12.0"}, "engines": {"node": ">=6"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/p-try/2.2.0/p-try-2.2.0.tgz", "integrity": "sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pako": {"version": "1.0.11", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/pako/1.0.11/pako-1.0.11.tgz", "integrity": "sha1-bJWZ00DVTf05RjgCUqNXBaa5kr8=", "dev": true, "license": "(MIT AND Zlib)"}, "node_modules/parallel-transform": {"version": "1.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/parallel-transform/1.2.0/parallel-transform-1.2.0.tgz", "integrity": "sha1-kEnKN9bLIYLDsdLHIL6U0UpYFPw=", "dev": true, "license": "MIT", "dependencies": {"cyclist": "^1.0.1", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}}, "node_modules/param-case": {"version": "3.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/param-case/3.0.4/param-case-3.0.4.tgz", "integrity": "sha1-fRf+SqEr3jTUp32RrPtiGcqtAcU=", "dev": true, "license": "MIT", "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/parse-asn1": {"version": "5.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/parse-asn1/5.1.6/parse-asn1-5.1.6.tgz", "integrity": "sha1-OFCAo+wTy2KmLTlAnLPoiETNrtQ=", "dev": true, "license": "ISC", "dependencies": {"asn1.js": "^5.2.0", "browserify-aes": "^1.0.0", "evp_bytestokey": "^1.0.0", "pbkdf2": "^3.0.3", "safe-buffer": "^5.1.1"}}, "node_modules/parse-passwd": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/parse-passwd/1.0.0/parse-passwd-1.0.0.tgz", "integrity": "sha1-bVuTSkVpk7I9N/QKOC1vFmao5cY=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/parseurl": {"version": "1.3.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/parseurl/1.3.3/parseurl-1.3.3.tgz", "integrity": "sha1-naGee+6NEt/wUT7Vt2lXeTvC6NQ=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/pascal-case": {"version": "3.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/pascal-case/3.1.2/pascal-case-3.1.2.tgz", "integrity": "sha1-tI4O8rmOIF58Ha50fQsVCCN2YOs=", "dev": true, "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "node_modules/pascalcase": {"version": "0.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/pascalcase/0.1.1/pascalcase-0.1.1.tgz", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-browserify": {"version": "0.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/path-browserify/0.0.1/path-browserify-0.0.1.tgz", "integrity": "sha1-5sTd1+06onxoogzE5Q4aTug7vEo=", "dev": true, "license": "MIT"}, "node_modules/path-dirname": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/path-dirname/1.0.2/path-dirname-1.0.2.tgz", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=", "dev": true, "license": "MIT"}, "node_modules/path-exists": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/path-exists/3.0.0/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/path-is-absolute/1.0.1/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-is-inside": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/path-is-inside/1.0.2/path-is-inside-1.0.2.tgz", "integrity": "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=", "dev": true, "license": "(WTFPL OR MIT)"}, "node_modules/path-key": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/path-key/2.0.1/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=", "dev": true, "license": "MIT"}, "node_modules/path-to-regexp": {"version": "0.1.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/path-to-regexp/0.1.7/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=", "dev": true, "license": "MIT"}, "node_modules/pbkdf2": {"version": "3.0.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/pbkdf2/-/pbkdf2-3.0.9.tgz", "integrity": "sha1-8sSyWmAAWLPDdzwIbDfbvuH/5pM=", "dev": true, "license": "MIT", "dependencies": {"create-hmac": "^1.1.2"}, "engines": {"node": ">=0.12"}}, "node_modules/picocolors": {"version": "0.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/picocolors/-/picocolors-0.2.1.tgz", "integrity": "sha1-VwZw95NkaFHRuhNZlpYqutWHhZ8=", "dev": true, "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=", "dev": true, "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pify": {"version": "4.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/pify/4.0.1/pify-4.0.1.tgz", "integrity": "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pinkie": {"version": "2.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/pinkie/2.0.4/pinkie-2.0.4.tgz", "integrity": "sha1-clVrgM+g1IqXToDnckjoDtT3+HA=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie-promise": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/pinkie-promise/2.0.1/pinkie-promise-2.0.1.tgz", "integrity": "sha1-ITXW36ejWMBprJsXh3YogihFD/o=", "dev": true, "license": "MIT", "dependencies": {"pinkie": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/pkg-dir": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/pkg-dir/3.0.0/pkg-dir-3.0.0.tgz", "integrity": "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=", "dev": true, "license": "MIT", "dependencies": {"find-up": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/portfinder": {"version": "1.0.32", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/portfinder/-/portfinder-1.0.32.tgz", "integrity": "sha1-L+G55YOJcSQp3CvqW+shRhRsf4E=", "dev": true, "license": "MIT", "dependencies": {"async": "^2.6.4", "debug": "^3.2.7", "mkdirp": "^0.5.6"}, "engines": {"node": ">= 0.12.0"}}, "node_modules/portfinder/node_modules/debug": {"version": "3.2.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/debug/3.2.7/debug-3.2.7.tgz", "integrity": "sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/posix-character-classes": {"version": "0.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/posix-character-classes/0.1.1/posix-character-classes-0.1.1.tgz", "integrity": "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/postcss": {"version": "7.0.39", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/postcss/-/postcss-7.0.39.tgz", "integrity": "sha1-liQ3XZZWMOLh8sAqk1yCpZy0gwk=", "dev": true, "license": "MIT", "dependencies": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}, "engines": {"node": ">=6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}}, "node_modules/postcss-modules-extract-imports": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/postcss-modules-extract-imports/2.0.0/postcss-modules-extract-imports-2.0.0.tgz", "integrity": "sha1-gYcZoa4doyX5gyRGsBE27rSTzX4=", "dev": true, "license": "ISC", "dependencies": {"postcss": "^7.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/postcss-modules-local-by-default": {"version": "3.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/postcss-modules-local-by-default/3.0.3/postcss-modules-local-by-default-3.0.3.tgz", "integrity": "sha1-uxTgzHgnnVBNvcv9fgyiiZP/u7A=", "dev": true, "license": "MIT", "dependencies": {"icss-utils": "^4.1.1", "postcss": "^7.0.32", "postcss-selector-parser": "^6.0.2", "postcss-value-parser": "^4.1.0"}, "engines": {"node": ">= 6"}}, "node_modules/postcss-modules-scope": {"version": "2.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/postcss-modules-scope/2.2.0/postcss-modules-scope-2.2.0.tgz", "integrity": "sha1-OFyuATzHdD9afXYC0Qc6iequYu4=", "dev": true, "license": "ISC", "dependencies": {"postcss": "^7.0.6", "postcss-selector-parser": "^6.0.0"}, "engines": {"node": ">= 6"}}, "node_modules/postcss-modules-values": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/postcss-modules-values/3.0.0/postcss-modules-values-3.0.0.tgz", "integrity": "sha1-W1AA1uuuKbQlUwG0o6VFdEI+fxA=", "dev": true, "license": "ISC", "dependencies": {"icss-utils": "^4.0.0", "postcss": "^7.0.6"}}, "node_modules/postcss-selector-parser": {"version": "6.0.13", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/postcss-selector-parser/-/postcss-selector-parser-6.0.13.tgz", "integrity": "sha1-0F2NdrHo4XMlfvnWC3Bqjl6Zvxs=", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-value-parser": {"version": "4.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/postcss-value-parser/4.1.0/postcss-value-parser-4.1.0.tgz", "integrity": "sha1-RD9qIM7WSBor2k+oUypuVdeJoss=", "dev": true, "license": "MIT"}, "node_modules/postcss/node_modules/source-map": {"version": "0.6.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-map/0.6.1/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/pretty-error": {"version": "2.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/pretty-error/2.1.2/pretty-error-2.1.2.tgz", "integrity": "sha1-von4LYGxyG7I/fvDhQRYgnJ/k7Y=", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.17.20", "renderkid": "^2.0.4"}}, "node_modules/process": {"version": "0.11.10", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/process/0.11.10/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/process-nextick-args/2.0.1/process-nextick-args-2.0.1.tgz", "integrity": "sha1-eCDZsWEgzFXKmud5JoCufbptf+I=", "dev": true, "license": "MIT"}, "node_modules/promise-inflight": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/promise-inflight/1.0.1/promise-inflight-1.0.1.tgz", "integrity": "sha1-mEcocL8igTL8vdhoEputEsPAKeM=", "dev": true, "license": "ISC"}, "node_modules/proxy-addr": {"version": "2.0.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/proxy-addr/-/proxy-addr-2.0.7.tgz", "integrity": "sha1-8Z/mnOqzEe65S0LnDowgcPm6ECU=", "dev": true, "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/prr": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/prr/1.0.1/prr-1.0.1.tgz", "integrity": "sha1-0/wRS6BplaRexok/SEzrHXj19HY=", "dev": true, "license": "MIT"}, "node_modules/public-encrypt": {"version": "4.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/public-encrypt/4.0.3/public-encrypt-4.0.3.tgz", "integrity": "sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=", "dev": true, "license": "MIT", "dependencies": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/public-encrypt/node_modules/bn.js": {"version": "4.11.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/bn.js/-/bn.js-4.11.6.tgz", "integrity": "sha1-UzRK2xRhehP26N0s4okF0cC6MhU=", "dev": true, "license": "MIT"}, "node_modules/pump": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/pump/3.0.0/pump-3.0.0.tgz", "integrity": "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=", "dev": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/pumpify": {"version": "1.5.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/pumpify/1.5.1/pumpify-1.5.1.tgz", "integrity": "sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=", "dev": true, "license": "MIT", "dependencies": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}}, "node_modules/pumpify/node_modules/pump": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/pump/2.0.1/pump-2.0.1.tgz", "integrity": "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=", "dev": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/punycode/-/punycode-2.3.0.tgz", "integrity": "sha1-9n+mfJTaj00M//mBruQRgGQZm48=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/qs": {"version": "6.11.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/qs/-/qs-6.11.2.tgz", "integrity": "sha1-ZL6lHxLB9dobwBSW9I/8/3xp19k=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.4"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/querystring-es3": {"version": "0.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/querystring-es3/-/querystring-es3-0.2.0.tgz", "integrity": "sha1-w2WgimnEQ6zP6zqd6rNePwq6pHY=", "dev": true, "engines": {"node": ">=0.4.x"}}, "node_modules/querystringify": {"version": "2.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/querystringify/2.2.0/querystringify-2.2.0.tgz", "integrity": "sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=", "dev": true, "license": "MIT"}, "node_modules/randombytes": {"version": "2.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/randombytes/2.1.0/randombytes-2.1.0.tgz", "integrity": "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/randomfill": {"version": "1.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/randomfill/1.0.4/randomfill-1.0.4.tgz", "integrity": "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=", "dev": true, "license": "MIT", "dependencies": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}}, "node_modules/range-parser": {"version": "1.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/range-parser/1.2.1/range-parser-1.2.1.tgz", "integrity": "sha1-PPNwI9GZ4cJNGlW4SADC8+ZGgDE=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.5.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/raw-body/-/raw-body-2.5.1.tgz", "integrity": "sha1-/hsWKLGBtwAhXl/UI4n5i3E5KFc=", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/raw-body/node_modules/bytes": {"version": "3.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/bytes/-/bytes-3.1.2.tgz", "integrity": "sha1-iwvuuYYFrfGxKPpDhkA8AJ4CIaU=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/readable-stream": {"version": "2.3.8", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/readable-stream/-/readable-stream-2.3.8.tgz", "integrity": "sha1-kRJegEK7obmIf0k0X2J3Anzovps=", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/readable-stream/node_modules/isarray": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/isarray/1.0.0/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true, "license": "MIT"}, "node_modules/readable-stream/node_modules/safe-buffer": {"version": "5.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/safe-buffer/5.1.2/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "dev": true, "license": "MIT"}, "node_modules/readable-stream/node_modules/string_decoder": {"version": "1.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/string_decoder/1.1.1/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/readdirp": {"version": "3.6.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/readdirp/-/readdirp-3.6.0.tgz", "integrity": "sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/regenerate": {"version": "1.4.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/regenerate/1.4.2/regenerate-1.4.2.tgz", "integrity": "sha1-uTRtiCfo9aMve6KWN9OYtpAUhIo=", "dev": true, "license": "MIT"}, "node_modules/regenerate-unicode-properties": {"version": "10.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.0.tgz", "integrity": "sha1-fDGSyrbdJOIctEYeXd190k+oN0w=", "dev": true, "license": "MIT", "dependencies": {"regenerate": "^1.4.2"}, "engines": {"node": ">=4"}}, "node_modules/regenerator-runtime": {"version": "0.14.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/regenerator-runtime/-/regenerator-runtime-0.14.0.tgz", "integrity": "sha1-XhnWjrEtSG95fhWjxqkY987F60U=", "dev": true, "license": "MIT"}, "node_modules/regenerator-transform": {"version": "0.15.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/regenerator-transform/-/regenerator-transform-0.15.2.tgz", "integrity": "sha1-W7rli1IgmOvfCbyi+Dg4kpABx6Q=", "dev": true, "license": "MIT", "dependencies": {"@babel/runtime": "^7.8.4"}}, "node_modules/regex-not": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/regex-not/1.0.2/regex-not-1.0.2.tgz", "integrity": "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=", "dev": true, "license": "MIT", "dependencies": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/regexp.prototype.flags": {"version": "1.5.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/regexp.prototype.flags/-/regexp.prototype.flags-1.5.0.tgz", "integrity": "sha1-/nziXn5Myo2ze2Y0yKLHAJGZucs=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.2.0", "functions-have-names": "^1.2.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/regexpu-core": {"version": "5.3.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/regexpu-core/-/regexpu-core-5.3.2.tgz", "integrity": "sha1-EaKwaITzUnrsPpPbv0o7lYqVVGs=", "dev": true, "license": "MIT", "dependencies": {"@babel/regjsgen": "^0.8.0", "regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.1.0", "regjsparser": "^0.9.1", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/regjsparser": {"version": "0.9.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/regjsparser/-/regjsparser-0.9.1.tgz", "integrity": "sha1-Jy0FqhDHwfZwlbH/Ct2uhEL8Vwk=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~0.5.0"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/regjsparser/node_modules/jsesc": {"version": "0.5.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/jsesc/0.5.0/jsesc-0.5.0.tgz", "integrity": "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=", "dev": true, "bin": {"jsesc": "bin/jsesc"}}, "node_modules/relateurl": {"version": "0.2.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/relateurl/0.2.7/relateurl-0.2.7.tgz", "integrity": "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/remove-trailing-separator": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/remove-trailing-separator/-/remove-trailing-separator-1.0.1.tgz", "integrity": "sha1-YV67lq9VlVLUv0BXyENtSGq2PMQ=", "dev": true, "license": "ISC"}, "node_modules/renderkid": {"version": "2.0.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/renderkid/-/renderkid-2.0.7.tgz", "integrity": "sha1-Rk8namvc7mBvShWZP5sp/HTKhgk=", "dev": true, "license": "MIT", "dependencies": {"css-select": "^4.1.3", "dom-converter": "^0.2.0", "htmlparser2": "^6.1.0", "lodash": "^4.17.21", "strip-ansi": "^3.0.1"}}, "node_modules/repeat-element": {"version": "1.1.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/repeat-element/1.1.3/repeat-element-1.1.3.tgz", "integrity": "sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/repeat-string": {"version": "1.6.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/repeat-string/1.6.1/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/require-directory/2.1.1/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-main-filename": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/require-main-filename/2.0.0/require-main-filename-2.0.0.tgz", "integrity": "sha1-0LMp7MfMD2Fkn2IhW+aa9UqomJs=", "dev": true, "license": "ISC"}, "node_modules/requires-port": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/requires-port/1.0.0/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=", "dev": true, "license": "MIT"}, "node_modules/resolve": {"version": "1.22.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/resolve/-/resolve-1.22.4.tgz", "integrity": "sha1-HcQN9GVUza+JSKSGoQ9roeICbDQ=", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-cwd": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/resolve-cwd/2.0.0/resolve-cwd-2.0.0.tgz", "integrity": "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=", "dev": true, "license": "MIT", "dependencies": {"resolve-from": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/resolve-dir": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/resolve-dir/1.0.1/resolve-dir-1.0.1.tgz", "integrity": "sha1-eaQGRMNivoLybv/nOcm7U4IEb0M=", "dev": true, "license": "MIT", "dependencies": {"expand-tilde": "^2.0.0", "global-modules": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/resolve-dir/node_modules/global-modules": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/global-modules/1.0.0/global-modules-1.0.0.tgz", "integrity": "sha1-bXcPDrUjrHgWTXK15xqIdyZcw+o=", "dev": true, "license": "MIT", "dependencies": {"global-prefix": "^1.0.1", "is-windows": "^1.0.1", "resolve-dir": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/resolve-dir/node_modules/global-prefix": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/global-prefix/1.0.2/global-prefix-1.0.2.tgz", "integrity": "sha1-2/dDxsFJklk8ZVVoy2btMsASLr4=", "dev": true, "license": "MIT", "dependencies": {"expand-tilde": "^2.0.2", "homedir-polyfill": "^1.0.1", "ini": "^1.3.4", "is-windows": "^1.0.1", "which": "^1.2.14"}, "engines": {"node": ">=0.10.0"}}, "node_modules/resolve-from": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/resolve-from/3.0.0/resolve-from-3.0.0.tgz", "integrity": "sha1-six699nWiBvItuZTM17rywoYh0g=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/resolve-url": {"version": "0.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/resolve-url/0.2.1/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=", "dev": true, "license": "MIT"}, "node_modules/ret": {"version": "0.1.15", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ret/0.1.15/ret-0.1.15.tgz", "integrity": "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=", "dev": true, "license": "MIT", "engines": {"node": ">=0.12"}}, "node_modules/retry": {"version": "0.12.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/retry/0.12.0/retry-0.12.0.tgz", "integrity": "sha1-G0KmJmoh8HQh0bC1S33BZ7AcATs=", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/rimraf": {"version": "2.7.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/rimraf/2.7.1/rimraf-2.7.1.tgz", "integrity": "sha1-NXl/E6f9rcVmFCwp1PB8ytSD4+w=", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/ripemd160": {"version": "2.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ripemd160/2.0.2/ripemd160-2.0.2.tgz", "integrity": "sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=", "dev": true, "license": "MIT", "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "node_modules/run-queue": {"version": "1.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/run-queue/1.0.3/run-queue-1.0.3.tgz", "integrity": "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=", "dev": true, "license": "ISC", "dependencies": {"aproba": "^1.1.1"}}, "node_modules/safe-array-concat": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/safe-array-concat/-/safe-array-concat-1.0.0.tgz", "integrity": "sha1-IGQiPLo8CNLuBRSO7bxWPNbYQGA=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.0", "has-symbols": "^1.0.3", "isarray": "^2.0.5"}, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safe-buffer": {"version": "5.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/safe-buffer/5.2.1/safe-buffer-5.2.1.tgz", "integrity": "sha1-Hq+fqb2x/dTsdfWPnNtOa3gn7sY=", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safe-regex": {"version": "1.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/safe-regex/1.1.0/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "dev": true, "license": "MIT", "dependencies": {"ret": "~0.1.10"}}, "node_modules/safe-regex-test": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/safe-regex-test/-/safe-regex-test-1.0.0.tgz", "integrity": "sha1-eTuHTVJOs2QNGHOq0DWW2y1PIpU=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.1.3", "is-regex": "^1.1.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/safer-buffer/2.1.2/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "dev": true, "license": "MIT"}, "node_modules/schema-utils": {"version": "2.7.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/schema-utils/2.7.1/schema-utils-2.7.1.tgz", "integrity": "sha1-HKTzLRskxZDCA7jnpQvw6kzTlNc=", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.5", "ajv": "^6.12.4", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/select-hose": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/select-hose/2.0.0/select-hose-2.0.0.tgz", "integrity": "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=", "dev": true, "license": "MIT"}, "node_modules/selfsigned": {"version": "1.10.14", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/selfsigned/-/selfsigned-1.10.14.tgz", "integrity": "sha1-7lHYTZ3OzGHgfkq6NPIpq1JcFXQ=", "dev": true, "license": "MIT", "dependencies": {"node-forge": "^0.10.0"}}, "node_modules/semver": {"version": "5.7.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/semver/-/semver-5.7.2.tgz", "integrity": "sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "node_modules/send": {"version": "0.18.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/send/-/send-0.18.0.tgz", "integrity": "sha1-ZwFnzGVLBfWqSnZ/kRO7NxvHBr4=", "dev": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/debug/2.6.9/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ms/2.0.0/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/send/node_modules/ms": {"version": "2.1.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ms/2.1.3/ms-2.1.3.tgz", "integrity": "sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=", "dev": true, "license": "MIT"}, "node_modules/serialize-javascript": {"version": "4.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/serialize-javascript/4.0.0/serialize-javascript-4.0.0.tgz", "integrity": "sha1-tSXhI4SJpez8Qq+sw/6Z5mb0sao=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/serve-index": {"version": "1.9.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/serve-index/1.9.1/serve-index-1.9.1.tgz", "integrity": "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-index/node_modules/debug": {"version": "2.6.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/debug/2.6.9/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/serve-index/node_modules/depd": {"version": "1.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/depd/1.1.2/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/serve-index/node_modules/http-errors": {"version": "1.6.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/http-errors/1.6.3/http-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "dev": true, "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/serve-index/node_modules/inherits": {"version": "2.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/inherits/2.0.3/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true, "license": "ISC"}, "node_modules/serve-index/node_modules/ms": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ms/2.0.0/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/serve-index/node_modules/setprototypeof": {"version": "1.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/setprototypeof/1.1.0/setprototypeof-1.1.0.tgz", "integrity": "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=", "dev": true, "license": "ISC"}, "node_modules/serve-index/node_modules/statuses": {"version": "1.5.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/statuses/1.5.0/statuses-1.5.0.tgz", "integrity": "sha1-Fhx9rBd2Wf2YEfQ3cfqZOBR4Yow=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/serve-static": {"version": "1.15.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/serve-static/-/serve-static-1.15.0.tgz", "integrity": "sha1-+q7wjP/goaYvYMrQxOUTz/CslUA=", "dev": true, "license": "MIT", "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.18.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/set-blocking": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/set-blocking/2.0.0/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc=", "dev": true, "license": "ISC"}, "node_modules/set-value": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/set-value/2.0.1/set-value-2.0.1.tgz", "integrity": "sha1-oY1AUw5vB95CKMfe/kInr4ytAFs=", "dev": true, "license": "MIT", "dependencies": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/set-value/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/extend-shallow/2.0.1/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/set-value/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-extendable/0.1.1/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/setimmediate": {"version": "1.0.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/setimmediate/1.0.5/setimmediate-1.0.5.tgz", "integrity": "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=", "dev": true, "license": "MIT"}, "node_modules/setprototypeof": {"version": "1.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/setprototypeof/1.2.0/setprototypeof-1.2.0.tgz", "integrity": "sha1-ZsmiSnP5/CjL5msJ/tPTPcrxtCQ=", "dev": true, "license": "ISC"}, "node_modules/sha.js": {"version": "2.4.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/sha.js/-/sha.js-2.4.9.tgz", "integrity": "sha1-mPZIgEdLdPSji42p08Dy0QRjPn0=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "bin": {"sha.js": "bin.js"}}, "node_modules/shebang-command": {"version": "1.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/shebang-command/1.2.0/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/shebang-regex": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/shebang-regex/1.0.0/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/side-channel": {"version": "1.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/side-channel/1.0.4/side-channel-1.0.4.tgz", "integrity": "sha1-785cj9wQTudRslxY1CkAEfpeos8=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.0", "get-intrinsic": "^1.0.2", "object-inspect": "^1.9.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/signal-exit/3.0.7/signal-exit-3.0.7.tgz", "integrity": "sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=", "dev": true, "license": "ISC"}, "node_modules/snapdragon": {"version": "0.8.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/snapdragon/0.8.2/snapdragon-0.8.2.tgz", "integrity": "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=", "dev": true, "license": "MIT", "dependencies": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node": {"version": "2.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/snapdragon-node/2.1.1/snapdragon-node-2.1.1.tgz", "integrity": "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=", "dev": true, "license": "MIT", "dependencies": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/define-property": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/define-property/1.0.0/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-util": {"version": "3.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/snapdragon-util/3.0.1/snapdragon-util-3.0.1.tgz", "integrity": "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-util/node_modules/kind-of": {"version": "3.2.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/3.2.2/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/debug": {"version": "2.6.9", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/debug/2.6.9/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/snapdragon/node_modules/define-property": {"version": "0.2.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/define-property/0.2.5/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/extend-shallow/2.0.1/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "license": "MIT", "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-accessor-descriptor": {"version": "0.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-accessor-descriptor/0.1.6/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-accessor-descriptor/node_modules/kind-of": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/-/kind-of-3.1.0.tgz", "integrity": "sha1-R11pil5J/15T0U4+cyQp3Iv0z0c=", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-data-descriptor": {"version": "0.1.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-data-descriptor/0.1.4/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-data-descriptor/node_modules/kind-of": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/-/kind-of-3.1.0.tgz", "integrity": "sha1-R11pil5J/15T0U4+cyQp3Iv0z0c=", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-descriptor": {"version": "0.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-descriptor/0.1.6/is-descriptor-0.1.6.tgz", "integrity": "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=", "dev": true, "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-extendable/0.1.1/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/kind-of": {"version": "5.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/5.1.0/kind-of-5.1.0.tgz", "integrity": "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/ms": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ms/2.0.0/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true, "license": "MIT"}, "node_modules/sockjs": {"version": "0.3.20", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/sockjs/0.3.20/sockjs-0.3.20.tgz", "integrity": "sha1-smooPsVi74smh7RAM6Tuzqx12FU=", "dev": true, "license": "MIT", "dependencies": {"faye-websocket": "^0.10.0", "uuid": "^3.4.0", "websocket-driver": "0.6.5"}}, "node_modules/sockjs-client": {"version": "1.4.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/sockjs-client/1.4.0/sockjs-client-1.4.0.tgz", "integrity": "sha1-yfJWjhnI/YFztJl+o0IOC7MGx9U=", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.5", "eventsource": "^1.0.7", "faye-websocket": "~0.11.1", "inherits": "^2.0.3", "json3": "^3.3.2", "url-parse": "^1.4.3"}}, "node_modules/sockjs-client/node_modules/debug": {"version": "3.2.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/debug/-/debug-3.2.5.tgz", "integrity": "sha1-wkGPv9ein01PcP9M6mBNS2TEZAc=", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/sockjs-client/node_modules/faye-websocket": {"version": "0.11.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/faye-websocket/0.11.3/faye-websocket-0.11.3.tgz", "integrity": "sha1-XA6aiWjokSwoZjn96XeosgnyUI4=", "dev": true, "license": "Apache-2.0", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/source-list-map": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-list-map/2.0.1/source-list-map-2.0.1.tgz", "integrity": "sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=", "dev": true, "license": "MIT"}, "node_modules/source-map": {"version": "0.5.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-map/0.5.7/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-resolve": {"version": "0.5.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-map-resolve/0.5.3/source-map-resolve-0.5.3.tgz", "integrity": "sha1-GQhmvs51U+H48mei7oLGBrVQmho=", "dev": true, "license": "MIT", "dependencies": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-map-support/-/source-map-support-0.5.21.tgz", "integrity": "sha1-BP58f54e0tZiIzwoyys1ufY/bk8=", "dev": true, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/source-map-support/node_modules/source-map": {"version": "0.6.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-map/0.6.1/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-url": {"version": "0.4.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-map-url/0.4.1/source-map-url-0.4.1.tgz", "integrity": "sha1-CvZmBadFpaL5HPG7+KevvCg97FY=", "dev": true, "license": "MIT"}, "node_modules/spdy": {"version": "4.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/spdy/4.0.2/spdy-4.0.2.tgz", "integrity": "sha1-t09GYgOj7aRSwCSSuR+56EonZ3s=", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/spdy-transport": {"version": "3.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/spdy-transport/3.0.0/spdy-transport-3.0.0.tgz", "integrity": "sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.0", "detect-node": "^2.0.4", "hpack.js": "^2.1.6", "obuf": "^1.1.2", "readable-stream": "^3.0.6", "wbuf": "^1.7.3"}}, "node_modules/spdy-transport/node_modules/readable-stream": {"version": "3.6.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/readable-stream/-/readable-stream-3.6.2.tgz", "integrity": "sha1-VqmzbqllwAxak+8x6xEaDxEFaWc=", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/split-string": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/split-string/3.1.0/split-string-3.1.0.tgz", "integrity": "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=", "dev": true, "license": "MIT", "dependencies": {"extend-shallow": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ssri": {"version": "6.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ssri/-/ssri-6.0.2.tgz", "integrity": "sha1-FXk5E08gRk5zAd26PpD/qPdyisU=", "dev": true, "license": "ISC", "dependencies": {"figgy-pudding": "^3.5.1"}}, "node_modules/static-extend": {"version": "0.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/static-extend/0.1.2/static-extend-0.1.2.tgz", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "dev": true, "license": "MIT", "dependencies": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/define-property": {"version": "0.2.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/define-property/0.2.5/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "license": "MIT", "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/is-accessor-descriptor": {"version": "0.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-accessor-descriptor/0.1.6/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/is-accessor-descriptor/node_modules/kind-of": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/-/kind-of-3.1.0.tgz", "integrity": "sha1-R11pil5J/15T0U4+cyQp3Iv0z0c=", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/is-data-descriptor": {"version": "0.1.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-data-descriptor/0.1.4/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/is-data-descriptor/node_modules/kind-of": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/-/kind-of-3.1.0.tgz", "integrity": "sha1-R11pil5J/15T0U4+cyQp3Iv0z0c=", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/is-descriptor": {"version": "0.1.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-descriptor/0.1.6/is-descriptor-0.1.6.tgz", "integrity": "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=", "dev": true, "license": "MIT", "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/kind-of": {"version": "5.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/5.1.0/kind-of-5.1.0.tgz", "integrity": "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/statuses": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/statuses/2.0.1/statuses-2.0.1.tgz", "integrity": "sha1-VcsADM8dSHKL0jxoWgY5mM8aG2M=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/stream-browserify": {"version": "2.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/stream-browserify/2.0.2/stream-browserify-2.0.2.tgz", "integrity": "sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=", "dev": true, "license": "MIT", "dependencies": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}}, "node_modules/stream-each": {"version": "1.2.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/stream-each/1.2.3/stream-each-1.2.3.tgz", "integrity": "sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=", "dev": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}}, "node_modules/stream-http": {"version": "2.8.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/stream-http/2.8.3/stream-http-2.8.3.tgz", "integrity": "sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=", "dev": true, "license": "MIT", "dependencies": {"builtin-status-codes": "^3.0.0", "inherits": "^2.0.1", "readable-stream": "^2.3.6", "to-arraybuffer": "^1.0.0", "xtend": "^4.0.0"}}, "node_modules/stream-shift": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/stream-shift/1.0.1/stream-shift-1.0.1.tgz", "integrity": "sha1-1wiCgVWasneEJCebCHfaPDktWj0=", "dev": true, "license": "MIT"}, "node_modules/string_decoder": {"version": "1.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/string_decoder/1.3.0/string_decoder-1.3.0.tgz", "integrity": "sha1-QvEUWUpGzxqOMLCoT1bHjD7awh4=", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-width": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/string-width/3.1.0/string-width-3.1.0.tgz", "integrity": "sha1-InZ74htirxCBV0MG9prFG2IgOWE=", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^7.0.1", "is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^5.1.0"}, "engines": {"node": ">=6"}}, "node_modules/string-width/node_modules/ansi-regex": {"version": "4.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ansi-regex/-/ansi-regex-4.1.1.tgz", "integrity": "sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/string-width/node_modules/strip-ansi": {"version": "5.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/strip-ansi/5.2.0/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/string.prototype.trim": {"version": "1.2.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/string.prototype.trim/-/string.prototype.trim-1.2.7.tgz", "integrity": "sha1-poNSdAhZ9ok/FM4+8bswN/epBTM=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimend": {"version": "1.0.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/string.prototype.trimend/-/string.prototype.trimend-1.0.6.tgz", "integrity": "sha1-xKJ/oCbZedecBPFzl/JQpGKURTM=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/string.prototype.trimstart": {"version": "1.0.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/string.prototype.trimstart/-/string.prototype.trimstart-1.0.6.tgz", "integrity": "sha1-6Qq2aqjkAH2S71kbvzzUIsVr3PQ=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "define-properties": "^1.1.4", "es-abstract": "^1.20.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/strip-ansi": {"version": "3.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/strip-ansi/3.0.1/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-eof": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/strip-eof/1.0.0/strip-eof-1.0.0.tgz", "integrity": "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/style-loader": {"version": "1.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/style-loader/-/style-loader-1.2.1.tgz", "integrity": "sha1-xcu/vxFw0HbP3YbgEJxbuhFLqho=", "dev": true, "license": "MIT", "dependencies": {"loader-utils": "^2.0.0", "schema-utils": "^2.6.6"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}}, "node_modules/style-loader/node_modules/loader-utils": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/loader-utils/2.0.0/loader-utils-2.0.0.tgz", "integrity": "sha1-5MrOW4FtQloWa18JfhDNErNgZLA=", "dev": true, "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/supports-color": {"version": "5.5.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/supports-color/5.5.0/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha1-btpL00SjyUrqN21MwxvHcxEDngk=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/tapable": {"version": "1.1.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/tapable/1.1.3/tapable-1.1.3.tgz", "integrity": "sha1-ofzMBrWNth/XpF2i2kT186Pme6I=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/terser": {"version": "4.8.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/terser/-/terser-4.8.1.tgz", "integrity": "sha1-oA5WNFYt4iOf1ATGSQUb9vwhFE8=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"commander": "^2.20.0", "source-map": "~0.6.1", "source-map-support": "~0.5.12"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=6.0.0"}}, "node_modules/terser-webpack-plugin": {"version": "1.4.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/terser-webpack-plugin/1.4.5/terser-webpack-plugin-1.4.5.tgz", "integrity": "sha1-oheu+uozDnNP+sthIOwfoxLWBAs=", "dev": true, "license": "MIT", "dependencies": {"cacache": "^12.0.2", "find-cache-dir": "^2.1.0", "is-wsl": "^1.1.0", "schema-utils": "^1.0.0", "serialize-javascript": "^4.0.0", "source-map": "^0.6.1", "terser": "^4.1.2", "webpack-sources": "^1.4.0", "worker-farm": "^1.7.0"}, "engines": {"node": ">= 6.9.0"}, "peerDependencies": {"webpack": "^4.0.0"}}, "node_modules/terser-webpack-plugin/node_modules/schema-utils": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/schema-utils/1.0.0/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "engines": {"node": ">= 4"}}, "node_modules/terser-webpack-plugin/node_modules/source-map": {"version": "0.6.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-map/0.6.1/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/terser/node_modules/commander": {"version": "2.20.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/commander/2.20.3/commander-2.20.3.tgz", "integrity": "sha1-/UhehMA+tIgcIHIrpIA16FMa6zM=", "dev": true, "license": "MIT"}, "node_modules/terser/node_modules/source-map": {"version": "0.6.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-map/0.6.1/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/through2": {"version": "2.0.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/through2/2.0.5/through2-2.0.5.tgz", "integrity": "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=", "dev": true, "license": "MIT", "dependencies": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}}, "node_modules/thunky": {"version": "1.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/thunky/1.1.0/thunky-1.1.0.tgz", "integrity": "sha1-Wrr3FKlAXbBQRzK7zNLO3Z75U30=", "dev": true, "license": "MIT"}, "node_modules/timers-browserify": {"version": "2.0.12", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/timers-browserify/2.0.12/timers-browserify-2.0.12.tgz", "integrity": "sha1-RKRcEfv0B/NPl7zNFXfGUjYbAO4=", "dev": true, "license": "MIT", "dependencies": {"setimmediate": "^1.0.4"}, "engines": {"node": ">=0.6.0"}}, "node_modules/to-arraybuffer": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/to-arraybuffer/1.0.1/to-arraybuffer-1.0.1.tgz", "integrity": "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=", "dev": true, "license": "MIT"}, "node_modules/to-fast-properties": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/to-fast-properties/2.0.0/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/to-object-path": {"version": "0.3.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/to-object-path/0.3.0/to-object-path-0.3.0.tgz", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-object-path/node_modules/kind-of": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/kind-of/-/kind-of-3.1.0.tgz", "integrity": "sha1-R11pil5J/15T0U4+cyQp3Iv0z0c=", "dev": true, "license": "MIT", "dependencies": {"is-buffer": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex": {"version": "3.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/to-regex/3.0.2/to-regex-3.0.2.tgz", "integrity": "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=", "dev": true, "license": "MIT", "dependencies": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex-range": {"version": "2.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/to-regex-range/2.1.1/to-regex-range-2.1.1.tgz", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "dev": true, "license": "MIT", "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/toidentifier/-/toidentifier-1.0.1.tgz", "integrity": "sha1-O+NDIaiKgg7RvYDfqjPkefu43TU=", "dev": true, "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/tslib": {"version": "2.6.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/tslib/-/tslib-2.6.2.tgz", "integrity": "sha1-cDrClCXns3zW/UVukkBNRtHz5K4=", "dev": true, "license": "0BSD"}, "node_modules/tty-browserify": {"version": "0.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/tty-browserify/0.0.0/tty-browserify-0.0.0.tgz", "integrity": "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=", "dev": true, "license": "MIT"}, "node_modules/type-is": {"version": "1.6.18", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/type-is/1.6.18/type-is-1.6.18.tgz", "integrity": "sha1-TlUs0F3wlGfcvE73Od6J8s83wTE=", "dev": true, "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typed-array-buffer": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/typed-array-buffer/-/typed-array-buffer-1.0.0.tgz", "integrity": "sha1-GN4+fteXSwpynT/uy5QzjRRyzWA=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "get-intrinsic": "^1.2.1", "is-typed-array": "^1.1.10"}, "engines": {"node": ">= 0.4"}}, "node_modules/typed-array-byte-length": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/typed-array-byte-length/-/typed-array-byte-length-1.0.0.tgz", "integrity": "sha1-14eiSplXEWEfsrh6QFJ5lReyMNA=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "for-each": "^0.3.3", "has-proto": "^1.0.1", "is-typed-array": "^1.1.10"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-byte-offset": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/typed-array-byte-offset/-/typed-array-byte-offset-1.0.0.tgz", "integrity": "sha1-y76JtR/e+c1qrwetRwc0CrvE6gs=", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "has-proto": "^1.0.1", "is-typed-array": "^1.1.10"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typed-array-length": {"version": "1.0.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/typed-array-length/-/typed-array-length-1.0.4.tgz", "integrity": "sha1-idg3heXECYvscuCLMZZR8OrJwbs=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "for-each": "^0.3.3", "is-typed-array": "^1.1.9"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/typedarray": {"version": "0.0.6", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/typedarray/0.0.6/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "dev": true, "license": "MIT"}, "node_modules/unbox-primitive": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/unbox-primitive/-/unbox-primitive-1.0.2.tgz", "integrity": "sha1-KQMgIQV9Xmzb0IxRKcIm3/jtb54=", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-bigints": "^1.0.2", "has-symbols": "^1.0.3", "which-boxed-primitive": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/unicode-canonical-property-names-ecmascript": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz", "integrity": "sha1-MBrNxSVjFnDTn2FG4Od/9rvevdw=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-ecmascript": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz", "integrity": "sha1-VP0W4OyxZ88Ezx91a9zJLrp5dsM=", "dev": true, "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-value-ecmascript": {"version": "2.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz", "integrity": "sha1-y1//3NFqBRJPWksL98N3Agisu+A=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-property-aliases-ecmascript": {"version": "2.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz", "integrity": "sha1-Q9QeO+aYvUk++REHfJsTH4J+jM0=", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/union-value": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/union-value/1.0.1/union-value-1.0.1.tgz", "integrity": "sha1-C2/nuDWuzaYcbqTU8CwUIh4QmEc=", "dev": true, "license": "MIT", "dependencies": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^2.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/union-value/node_modules/is-extendable": {"version": "0.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-extendable/0.1.1/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/unique-filename": {"version": "1.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/unique-filename/1.1.1/unique-filename-1.1.1.tgz", "integrity": "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=", "dev": true, "license": "ISC", "dependencies": {"unique-slug": "^2.0.0"}}, "node_modules/unique-slug": {"version": "2.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/unique-slug/2.0.2/unique-slug-2.0.2.tgz", "integrity": "sha1-uqvOkQg/xk6UWw861hPiZPfNTmw=", "dev": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/unpipe/1.0.0/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/unset-value": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/unset-value/1.0.0/unset-value-1.0.0.tgz", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "dev": true, "license": "MIT", "dependencies": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value": {"version": "0.3.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/has-value/0.3.1/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "dev": true, "license": "MIT", "dependencies": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value/node_modules/isobject": {"version": "2.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/isobject/2.1.0/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "dev": true, "license": "MIT", "dependencies": {"isarray": "1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-values": {"version": "0.1.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/has-values/0.1.4/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/isarray": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/isarray/1.0.0/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true, "license": "MIT"}, "node_modules/upath": {"version": "1.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/upath/1.2.0/upath-1.2.0.tgz", "integrity": "sha1-j2bbzVWog6za5ECK+LA1pQRMGJQ=", "dev": true, "license": "MIT", "engines": {"node": ">=4", "yarn": "*"}}, "node_modules/update-browserslist-db": {"version": "1.0.11", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/update-browserslist-db/-/update-browserslist-db-1.0.11.tgz", "integrity": "sha1-mipkGtKQeuezYWUG9Ll3hR21uUA=", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.1.1", "picocolors": "^1.0.0"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/update-browserslist-db/node_modules/picocolors": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/picocolors/-/picocolors-1.0.0.tgz", "integrity": "sha1-y1vcdP8/UYkiNur3nWi8RFZKuBw=", "dev": true, "license": "ISC"}, "node_modules/uri-js": {"version": "4.4.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/uri-js/4.4.1/uri-js-4.4.1.tgz", "integrity": "sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/urix": {"version": "0.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/urix/0.1.0/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=", "dev": true, "license": "MIT"}, "node_modules/url": {"version": "0.11.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/url/-/url-0.11.1.tgz", "integrity": "sha1-JvkPYVQn7KG59NaigojBR+IwKjI=", "dev": true, "license": "MIT", "dependencies": {"punycode": "^1.4.1", "qs": "^6.11.0"}}, "node_modules/url-loader": {"version": "4.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/url-loader/-/url-loader-4.1.0.tgz", "integrity": "sha1-x9aw1rD8zVGrP/xYp40yuNiae+I=", "dev": true, "license": "MIT", "dependencies": {"loader-utils": "^2.0.0", "mime-types": "^2.1.26", "schema-utils": "^2.6.5"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"file-loader": "*", "webpack": "^4.0.0 || ^5.0.0"}, "peerDependenciesMeta": {"file-loader": {"optional": true}}}, "node_modules/url-loader/node_modules/loader-utils": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/loader-utils/2.0.0/loader-utils-2.0.0.tgz", "integrity": "sha1-5MrOW4FtQloWa18JfhDNErNgZLA=", "dev": true, "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/url-parse": {"version": "1.5.7", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/url-parse/-/url-parse-1.5.7.tgz", "integrity": "sha1-AHgPYNva6QGB9R7YX7JBCUIskyo=", "dev": true, "license": "MIT", "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "node_modules/url/node_modules/punycode": {"version": "1.4.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/punycode/1.4.1/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "dev": true, "license": "MIT"}, "node_modules/use": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/use/-/use-3.1.0.tgz", "integrity": "sha1-FHFr8D/f79AwQK71jYtLhfOnxUQ=", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/util": {"version": "0.11.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/util/0.11.1/util-0.11.1.tgz", "integrity": "sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=", "dev": true, "license": "MIT", "dependencies": {"inherits": "2.0.3"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/util-deprecate/1.0.2/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true, "license": "MIT"}, "node_modules/util.promisify": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/util.promisify/1.0.0/util.promisify-1.0.0.tgz", "integrity": "sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.1.2", "object.getownpropertydescriptors": "^2.0.3"}}, "node_modules/util/node_modules/inherits": {"version": "2.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/inherits/2.0.3/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true, "license": "ISC"}, "node_modules/utila": {"version": "0.4.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/utila/0.4.0/utila-0.4.0.tgz", "integrity": "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=", "dev": true, "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/utils-merge/1.0.1/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "3.4.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/uuid/3.4.0/uuid-3.4.0.tgz", "integrity": "sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=", "dev": true, "license": "MIT", "bin": {"uuid": "bin/uuid"}}, "node_modules/v8-compile-cache": {"version": "2.4.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/v8-compile-cache/-/v8-compile-cache-2.4.0.tgz", "integrity": "sha1-za2ovsYeFYZfBdCXxfT9MOlNwSg=", "dev": true, "license": "MIT"}, "node_modules/vary": {"version": "1.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/vary/1.1.2/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/vm-browserify": {"version": "1.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/vm-browserify/1.1.2/vm-browserify-1.1.2.tgz", "integrity": "sha1-eGQcSIuObKkadfUR56OzKobl3aA=", "dev": true, "license": "MIT"}, "node_modules/watchpack": {"version": "1.7.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/watchpack/1.7.5/watchpack-1.7.5.tgz", "integrity": "sha1-EmfmxV4Lm1vkTCAjrtVDeiwmxFM=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.1.2", "neo-async": "^2.5.0"}, "optionalDependencies": {"chokidar": "^3.4.1", "watchpack-chokidar2": "^2.0.1"}}, "node_modules/watchpack-chokidar2": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/watchpack-chokidar2/2.0.1/watchpack-chokidar2-2.0.1.tgz", "integrity": "sha1-OFAAcu5uzmbzdpk2lQ6hdxvhyVc=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"chokidar": "^2.1.8"}}, "node_modules/watchpack-chokidar2/node_modules/anymatch": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/anymatch/2.0.0/anymatch-2.0.0.tgz", "integrity": "sha1-vLJLTzeTTZqnrBe0ra+J58du8us=", "dev": true, "license": "ISC", "optional": true, "dependencies": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}}, "node_modules/watchpack-chokidar2/node_modules/anymatch/node_modules/normalize-path": {"version": "2.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/normalize-path/2.1.1/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"remove-trailing-separator": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/watchpack-chokidar2/node_modules/binary-extensions": {"version": "1.13.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/binary-extensions/-/binary-extensions-1.13.0.tgz", "integrity": "sha1-lSPgATBqMkRLkHQj8d4hZCIvarE=", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/watchpack-chokidar2/node_modules/chokidar": {"version": "2.1.8", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/chokidar/2.1.8/chokidar-2.1.8.tgz", "integrity": "sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}, "optionalDependencies": {"fsevents": "^1.2.7"}}, "node_modules/watchpack-chokidar2/node_modules/fsevents": {"version": "1.2.13", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/fsevents/1.2.13/fsevents-1.2.13.tgz", "integrity": "sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1"}, "engines": {"node": ">= 4.0"}}, "node_modules/watchpack-chokidar2/node_modules/glob-parent": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/glob-parent/3.1.0/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "dev": true, "license": "ISC", "optional": true, "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}}, "node_modules/watchpack-chokidar2/node_modules/glob-parent/node_modules/is-glob": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-glob/3.1.0/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"is-extglob": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/watchpack-chokidar2/node_modules/is-binary-path": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-binary-path/1.0.1/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"binary-extensions": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/watchpack-chokidar2/node_modules/readdirp": {"version": "2.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/readdirp/2.2.1/readdirp-2.2.1.tgz", "integrity": "sha1-DodiKjMlqjPokihcr4tOhGUppSU=", "dev": true, "license": "MIT", "optional": true, "dependencies": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}, "engines": {"node": ">=0.10"}}, "node_modules/wbuf": {"version": "1.7.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/wbuf/1.7.3/wbuf-1.7.3.tgz", "integrity": "sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=", "dev": true, "license": "MIT", "dependencies": {"minimalistic-assert": "^1.0.0"}}, "node_modules/webpack": {"version": "4.44.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/webpack/-/webpack-4.44.1.tgz", "integrity": "sha1-F+af/58yG48RfR/acU7fwLk5zCE=", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.9.0", "@webassemblyjs/helper-module-context": "1.9.0", "@webassemblyjs/wasm-edit": "1.9.0", "@webassemblyjs/wasm-parser": "1.9.0", "acorn": "^6.4.1", "ajv": "^6.10.2", "ajv-keywords": "^3.4.1", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^4.3.0", "eslint-scope": "^4.0.3", "json-parse-better-errors": "^1.0.2", "loader-runner": "^2.4.0", "loader-utils": "^1.2.3", "memory-fs": "^0.4.1", "micromatch": "^3.1.10", "mkdirp": "^0.5.3", "neo-async": "^2.6.1", "node-libs-browser": "^2.2.1", "schema-utils": "^1.0.0", "tapable": "^1.1.3", "terser-webpack-plugin": "^1.4.3", "watchpack": "^1.7.4", "webpack-sources": "^1.4.1"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=6.11.5"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}, "webpack-command": {"optional": true}}}, "node_modules/webpack-cli": {"version": "3.3.12", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/webpack-cli/-/webpack-cli-3.3.12.tgz", "integrity": "sha1-lOmtoIFFPNCqYJyZ5QABL9OtLUo=", "dev": true, "license": "MIT", "dependencies": {"chalk": "^2.4.2", "cross-spawn": "^6.0.5", "enhanced-resolve": "^4.1.1", "findup-sync": "^3.0.0", "global-modules": "^2.0.0", "import-local": "^2.0.0", "interpret": "^1.4.0", "loader-utils": "^1.4.0", "supports-color": "^6.1.0", "v8-compile-cache": "^2.1.1", "yargs": "^13.3.2"}, "bin": {"webpack-cli": "bin/cli.js"}, "engines": {"node": ">=6.11.5"}, "peerDependencies": {"webpack": "4.x.x"}}, "node_modules/webpack-cli/node_modules/supports-color": {"version": "6.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/supports-color/6.1.0/supports-color-6.1.0.tgz", "integrity": "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/webpack-dev-middleware": {"version": "3.7.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/webpack-dev-middleware/3.7.3/webpack-dev-middleware-3.7.3.tgz", "integrity": "sha1-Bjk3KxQyYuK4SrldO5GnWXBhwsU=", "dev": true, "license": "MIT", "dependencies": {"memory-fs": "^0.4.1", "mime": "^2.4.4", "mkdirp": "^0.5.1", "range-parser": "^1.2.1", "webpack-log": "^2.0.0"}, "engines": {"node": ">= 6"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}}, "node_modules/webpack-dev-middleware/node_modules/mime": {"version": "2.6.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/mime/-/mime-2.6.0.tgz", "integrity": "sha1-oqaCqVzU0MsdYlfij4PafjWAA2c=", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/webpack-dev-server": {"version": "3.11.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/webpack-dev-server/3.11.0/webpack-dev-server-3.11.0.tgz", "integrity": "sha1-jxVKO84bz9HMYY705wMniFXn/4w=", "dev": true, "license": "MIT", "dependencies": {"ansi-html": "0.0.7", "bonjour": "^3.5.0", "chokidar": "^2.1.8", "compression": "^1.7.4", "connect-history-api-fallback": "^1.6.0", "debug": "^4.1.1", "del": "^4.1.1", "express": "^4.17.1", "html-entities": "^1.3.1", "http-proxy-middleware": "0.19.1", "import-local": "^2.0.0", "internal-ip": "^4.3.0", "ip": "^1.1.5", "is-absolute-url": "^3.0.3", "killable": "^1.0.1", "loglevel": "^1.6.8", "opn": "^5.5.0", "p-retry": "^3.0.1", "portfinder": "^1.0.26", "schema-utils": "^1.0.0", "selfsigned": "^1.10.7", "semver": "^6.3.0", "serve-index": "^1.9.1", "sockjs": "0.3.20", "sockjs-client": "1.4.0", "spdy": "^4.0.2", "strip-ansi": "^3.0.1", "supports-color": "^6.1.0", "url": "^0.11.0", "webpack-dev-middleware": "^3.7.2", "webpack-log": "^2.0.0", "ws": "^6.2.1", "yargs": "^13.3.2"}, "bin": {"webpack-dev-server": "bin/webpack-dev-server.js"}, "engines": {"node": ">= 6.11.5"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/webpack-dev-server/node_modules/anymatch": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/anymatch/2.0.0/anymatch-2.0.0.tgz", "integrity": "sha1-vLJLTzeTTZqnrBe0ra+J58du8us=", "dev": true, "license": "ISC", "dependencies": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}}, "node_modules/webpack-dev-server/node_modules/anymatch/node_modules/normalize-path": {"version": "2.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/normalize-path/2.1.1/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "license": "MIT", "dependencies": {"remove-trailing-separator": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/webpack-dev-server/node_modules/binary-extensions": {"version": "1.13.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/binary-extensions/-/binary-extensions-1.13.0.tgz", "integrity": "sha1-lSPgATBqMkRLkHQj8d4hZCIvarE=", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/webpack-dev-server/node_modules/chokidar": {"version": "2.1.8", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/chokidar/2.1.8/chokidar-2.1.8.tgz", "integrity": "sha1-gEs6e2qZNYw8XGHnHYco8EHP+Rc=", "dev": true, "license": "MIT", "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}, "optionalDependencies": {"fsevents": "^1.2.7"}}, "node_modules/webpack-dev-server/node_modules/fsevents": {"version": "1.2.13", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/fsevents/1.2.13/fsevents-1.2.13.tgz", "integrity": "sha1-8yXLBFVZJCi88Rs4M3DvcOO/zDg=", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["darwin"], "dependencies": {"bindings": "^1.5.0", "nan": "^2.12.1"}, "engines": {"node": ">= 4.0"}}, "node_modules/webpack-dev-server/node_modules/glob-parent": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/glob-parent/3.1.0/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}}, "node_modules/webpack-dev-server/node_modules/glob-parent/node_modules/is-glob": {"version": "3.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-glob/3.1.0/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/webpack-dev-server/node_modules/is-binary-path": {"version": "1.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/is-binary-path/1.0.1/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "dev": true, "license": "MIT", "dependencies": {"binary-extensions": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/webpack-dev-server/node_modules/readdirp": {"version": "2.2.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/readdirp/2.2.1/readdirp-2.2.1.tgz", "integrity": "sha1-DodiKjMlqjPokihcr4tOhGUppSU=", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}, "engines": {"node": ">=0.10"}}, "node_modules/webpack-dev-server/node_modules/schema-utils": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/schema-utils/1.0.0/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "engines": {"node": ">= 4"}}, "node_modules/webpack-dev-server/node_modules/semver": {"version": "6.3.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/semver/-/semver-6.3.1.tgz", "integrity": "sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/webpack-dev-server/node_modules/supports-color": {"version": "6.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/supports-color/6.1.0/supports-color-6.1.0.tgz", "integrity": "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/webpack-log": {"version": "2.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/webpack-log/2.0.0/webpack-log-2.0.0.tgz", "integrity": "sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8=", "dev": true, "license": "MIT", "dependencies": {"ansi-colors": "^3.0.0", "uuid": "^3.3.2"}, "engines": {"node": ">= 6"}}, "node_modules/webpack-sources": {"version": "1.4.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/webpack-sources/1.4.3/webpack-sources-1.4.3.tgz", "integrity": "sha1-7t2OwLko+/HL/plOItLYkPMwqTM=", "dev": true, "license": "MIT", "dependencies": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}}, "node_modules/webpack-sources/node_modules/source-map": {"version": "0.6.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/source-map/0.6.1/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/webpack/node_modules/schema-utils": {"version": "1.0.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/schema-utils/1.0.0/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "engines": {"node": ">= 4"}}, "node_modules/websocket-driver": {"version": "0.6.5", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/websocket-driver/0.6.5/websocket-driver-0.6.5.tgz", "integrity": "sha1-XLJVbOuF9Dc8bYI4qmkchFThOjY=", "dev": true, "license": "MIT", "dependencies": {"websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.6.0"}}, "node_modules/websocket-extensions": {"version": "0.1.4", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/websocket-extensions/0.1.4/websocket-extensions-0.1.4.tgz", "integrity": "sha1-f4RzvIOd/YdgituV1+sHUhFXikI=", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}}, "node_modules/which": {"version": "1.3.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/which/1.3.1/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/which-boxed-primitive": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/which-boxed-primitive/-/which-boxed-primitive-1.0.2.tgz", "integrity": "sha1-E3V7yJsgmwSf5dhkMOIc9AqJqOY=", "dev": true, "license": "MIT", "dependencies": {"is-bigint": "^1.0.1", "is-boolean-object": "^1.1.0", "is-number-object": "^1.0.4", "is-string": "^1.0.5", "is-symbol": "^1.0.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/which-module": {"version": "2.0.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/which-module/-/which-module-2.0.1.tgz", "integrity": "sha1-d2sf412Qrr6Z6KwV6yQJM4mkpAk=", "dev": true, "license": "ISC"}, "node_modules/which-typed-array": {"version": "1.1.11", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/which-typed-array/-/which-typed-array-1.1.11.tgz", "integrity": "sha1-mdaR8jxyqrZ2hoCAWicbaXYe1ho=", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.5", "call-bind": "^1.0.2", "for-each": "^0.3.3", "gopd": "^1.0.1", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/worker-farm": {"version": "1.7.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/worker-farm/1.7.0/worker-farm-1.7.0.tgz", "integrity": "sha1-JqlMU5G7ypJhUgAvabhKS/dy5ag=", "dev": true, "license": "MIT", "dependencies": {"errno": "~0.1.7"}}, "node_modules/wrap-ansi": {"version": "5.1.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/wrap-ansi/5.1.0/wrap-ansi-5.1.0.tgz", "integrity": "sha1-H9H2cjXVttD+54EFYAG/tpTAOwk=", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^3.2.0", "string-width": "^3.0.0", "strip-ansi": "^5.0.0"}, "engines": {"node": ">=6"}}, "node_modules/wrap-ansi/node_modules/ansi-regex": {"version": "4.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ansi-regex/-/ansi-regex-4.1.1.tgz", "integrity": "sha1-Fk2qyHqy1vbbOimHXi0XZlgtq+0=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/wrap-ansi/node_modules/strip-ansi": {"version": "5.2.0", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/strip-ansi/5.2.0/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/wrappy/1.0.2/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true, "license": "ISC"}, "node_modules/ws": {"version": "6.2.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/ws/-/ws-6.2.2.tgz", "integrity": "sha1-3Vzb1XqZeZFgl2UtePHMX66gwy4=", "dev": true, "license": "MIT", "dependencies": {"async-limiter": "~1.0.0"}}, "node_modules/xtend": {"version": "4.0.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/xtend/4.0.2/xtend-4.0.2.tgz", "integrity": "sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=", "dev": true, "license": "MIT", "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "4.0.3", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/y18n/-/y18n-4.0.3.tgz", "integrity": "sha1-tfJZyCzW4zaSHv17/Yv1YN6e7t8=", "dev": true, "license": "ISC"}, "node_modules/yallist": {"version": "3.1.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/yallist/3.1.1/yallist-3.1.1.tgz", "integrity": "sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=", "dev": true, "license": "ISC"}, "node_modules/yargs": {"version": "13.3.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/yargs/13.3.2/yargs-13.3.2.tgz", "integrity": "sha1-rX/+/sGqWVZayRX4Lcyzipwxot0=", "dev": true, "license": "MIT", "dependencies": {"cliui": "^5.0.0", "find-up": "^3.0.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^3.0.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^13.1.2"}}, "node_modules/yargs-parser": {"version": "13.1.2", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/yargs-parser/13.1.2/yargs-parser-13.1.2.tgz", "integrity": "sha1-Ew8JcC667vJlDVTObj5XBvek+zg=", "dev": true, "license": "ISC", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}}, "node_modules/yargs-parser/node_modules/camelcase": {"version": "5.3.1", "resolved": "https://cmc.centralrepo.rnd.huawei.com/artifactory/api/npm/npm-central-repo/camelcase/5.3.1/camelcase-5.3.1.tgz", "integrity": "sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}}}