<!DOCTYPE html>
<html xmlns:eview="ignored">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-Control" content="no-cache">
    <link href="/dvlogmatrixwebsite/css/metaconfig/index.css" type="text/css" rel="stylesheet"/>
    <link href="/dvfoundationwebsite/css/tableCommon.css" type="text/css" rel="stylesheet" />
	<link href="/dvfoundationwebsite/css/reset.css" type="text/css" rel="stylesheet"/>
    <link href="/dvfoundationwebsite/css/common.css" type="text/css" rel="stylesheet" />
	<link href="/dvfoundationwebsite/css/loading.css" type="text/css" rel="stylesheet"  />
	<style>
		body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, textarea, p, blockquote, th, td, span, a {
			font-size:12px;
		}
        .right-container{
            padding-left: 0px;
            padding-top: 20px;
        }
	</style>

    <script src="/dvlogmatrixwebsite/js/auth.js" id="licenseScript" name="DVLogmatrixService.Basic"></script>
    <script src="/febs/v1/assets/prelude-loader"></script>
    <script src="/dvfoundationwebsite/js/common.js"></script>
    
    <script>
        Prel.ready(function () {
            Prel.autoLoad({
                'eView3rdLibs': '*',
                'eViewBasic': '*',
            }).then(function() {
                window.$CmpApp = angular.module('cmp', ['eviewWidgets']);
                setHelpId("statusHelpLib_", "cmp.logmatrix.config");
                loadJs('/dvfoundationwebsite/js/cmp.js');
                loadJs('/dvlogmatrixwebsite/js/controller/i18n.js');
                loadJs('/dvfoundationwebsite/js/i18n.js');
                loadJs('/dvfoundationwebsite/js/dataCheck.js');
                loadJs('/dvfoundationwebsite/js/dataCheckFunc.js');
                loadJs("/dvlogmatrixwebsite/js/controller/common.js");
                loadJs("/dvlogmatrixwebsite/js/controller/metaconfig/status.js");
                loadJs("/dvlogmatrixwebsite/js/controller/metaconfig/collect.js");
                loadJs("/dvlogmatrixwebsite/js/controller/metaconfig/analysis.js",function(){
                    $CmpApp.controller('StatusController', ['$scope',  StatusController]);
                    $CmpApp.controller("CollectController", ["$scope",  CollectController]);
                    $CmpApp.controller('AnalyisController', ['$scope',  AnalyisController]);
                    angular.bootstrap(document, ['cmp']);
                });
            });
        })
        window.eviewWidgetConfig = window.eviewWidgetConfig || {};
        window.eviewWidgetConfig.theme = 'default';
    </script>
</head>
<body>
    <div class="right-container">
        <div class="main-frame" style="padding-left: 0px" ng-cloak ng-controller="StatusController" ng-init="init()" cmp-help-id="42190008">
            <div class="df-tab" style="margin-bottom:10px;padding-top: 0px">
                <div class="df-tab-header" ng-init="$Model.currentTab=0">
                    <ul>
                        <li ng-class="{'current':$Model.currentTab==0}" ng-click="$Model.currentTab=0">
                            <span class="status-tab-text" title="{{'cmp.metaconfig.collect.title'|i18n}}">{{'cmp.metaconfig.collect.title'|i18n}}</span>
                        </li>
                        <li ng-show="analysisShow" ng-class="{'current':$Model.currentTab==1}" ng-click="$Model.currentTab=1">
                            <span class="status-tab-text" title="{{'cmp.metaconfig.analysis.title'|i18n}}">{{'cmp.metaconfig.analysis.title'|i18n}}</span>
                        </li>
                    </ul>
                    <div class="column-tip" ng-click="backToConfig()">
                        <div class="left-icon"></div>
                        <p style="font-size:20px;margin-left:20px;">
                            <span ng-bind="'cmp.metaconfig.status.returnIndex'|i18n"></span></p>
                    </div>
                </div>
            </div>
            <div>
                <div ng-if="$Model.currentTab==0" ng-controller="CollectController" ng-cloak ng-style="{'overflow':overflow}" ng-init="init()">
                    <div class="main-frame" style="padding-left: 0">
                        <div class="main-container" style="padding-top: 0px">
                            <div class="right-column">
                                <div id="collectParam" style="width: 100%;float: left;margin-bottom:40px;">
                                    <div style="float:left;margin-right:20px;">
                                        <p class="paramtext">{{'cmp.matrixmonit.detail.solution.instancesid'|i18n}}:</p>
                                        <div class="df-select-frame" style="margin-left:20px;">
                                            <div class="df-select-box" cmp-selector>
                                                <input type="text" ng-model="$Model.searchParams.solutionId"
                                                       property="$Model.searchParams.solutionId" readonly="true"
                                                       value="{{'cmp.metaconfig.status.all'|i18n}}"
                                                       title="{{$Model.searchParams.solutionId}}"
                                                       style="width: 96px"/>
                                                <div class="df-select-bar"></div>
                                            </div>
                                            <ul class="df-select-menu">
                                                <li title="{{'cmp.metaconfig.status.all'|i18n}}" ng-click="selectChoose('','solutioninstance')">
                                                    {{'cmp.metaconfig.status.all'|i18n}}
                                                </li>
                                                <li ng-repeat="(key,value) in $Model.solutionInstanceIds"
                                                    ng-click="$Model.configingExist = false;$Model.paramsChanged=true; selectChoose(key,'solutioninstance')"
                                                    title="{{value}}">{{value}}
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div style="float:left;margin-right:20px;">
                                        <p class="paramtext">{{'cmp.matrixmonit.detail.clustertype'|i18n}}:</p>
                                        <div class="df-select-frame" style="margin-left:20px;">
                                            <div class="df-select-box" cmp-selector>
                                                <input type="text" ng-model="$Model.searchParams.clusterType"
                                                       property="$Model.searchParams.clusterType" readonly="true"
                                                       value="{{'cmp.metaconfig.status.all'|i18n}}"
                                                       title="{{$Model.searchParams.clusterType}}"
                                                       style="width: 96px"/>
                                                <div class="df-select-bar"></div>
                                            </div>
                                            <ul class="df-select-menu">
                                                <li title="{{'cmp.metaconfig.status.all'|i18n}}" ng-click="selectChoose('','clustertype')">
                                                    {{'cmp.metaconfig.status.all'|i18n}}
                                                </li>
                                                <li ng-repeat="(key,value) in $Model.clusterType"
                                                    ng-click="$Model.configingExist = false;$Model.paramsChanged=true; selectChoose(key,'clustertype')"
                                                    title="{{value}}">{{value}}
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div style="float:left;margin-right:20px;">
                                        <div class="paramtext">{{'cmp.metaconfig.collect.ip'|i18n}}:</div>
                                        <div class="ds-input-frame" style="float:left;margin-left:20px">
                                            <input class="df-input" ng-model="$Model.searchParams.ip" id="statusSearchParamsIp" type="text" title="{{$Model.searchParams.ip}}"
                                                   ng-change="dataTypeValidCheck(['cmpValidChar','checkLength'],'statusSearchParamsIp',$Model.searchParams.ip, null, 128);paramsChange()"
                                                   ng-click="dataTypeValidCheck(['cmpValidChar','checkLength'],'statusSearchParamsIp',$Model.searchParams.ip, null, 128)"
                                                   ng-blur="closeShowTip()" u-validator="ip"/>
                                        </div>
                                    </div>
                                    <div style="float:left;margin-right:20px;">
                                        <p class="paramtext">{{'cmp.metaconfig.collect.logtype'|i18n}}:</p>
                                        <div class="df-select-frame" style="margin-left:20px;">
                                            <div class="df-select-box" cmp-selector>
                                                <input type="text" ng-model="$Model.searchParams.logType"
                                                       property="$Model.searchParams.logType" readonly="true"
                                                       title="{{$Model.searchParams.logType}}"
                                                       style="width: 96px"/>
                                                <div class="df-select-bar"></div>
                                            </div>
                                            <ul class="df-select-menu">
                                                <li ng-repeat="item in $Model.logTypes"
                                                    ng-click="$Model.configingExist = false;$Model.paramsChanged=true;"
                                                    title="{{item}}">{{item}}
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div style="float:left;margin-right:20px;">
                                        <p class="paramtext">{{'cmp.metaconfig.status'|i18n}}:</p>
                                        <div class="df-select-frame" style="margin-left:5px;">
                                        <div class="df-select-box" cmp-selector>
                                            <input type="text" readonly="true" value="{{'cmp.metaconfig.status.all'|i18n}}"
                                                   ng-model="$Model.selectedCollectStatus" title="{{$Model.selectedCollectStatus}}"
                                                   style="width: 96px; color:#000"/>
                                            <div class="df-select-bar"></div>
                                        </div>
                                        <ul class="df-select-menu">
                                            <li ng-class="{'current':$Model.searchParams.status==0}"
                                                ng-click="changeStatus(0);"
                                                title="{{'cmp.metaconfig.status.all'|i18n}}">
                                                {{'cmp.metaconfig.status.all'|i18n}}
                                            </li>
                                            <li ng-class="{'current':$Model.searchParams.status==4}"
                                                ng-click="changeStatus(4);"
                                                title="{{'cmp.metaconfig.status.configing'|i18n}}">
                                                {{'cmp.metaconfig.status.configing'|i18n}}
                                            </li>
                                            <li ng-class="{'current':$Model.searchParams.status==6}"
                                                ng-click="changeStatus(6);"
                                                title="{{'cmp.metaconfig.status.collecting'|i18n}}">
                                                {{'cmp.metaconfig.status.collecting'|i18n}}
                                            </li>
                                            <li ng-class="{'current':$Model.searchParams.status==1 || $Model.searchParams.status==3}"
                                                ng-click="changeStatus(1);"
                                                title="{{'cmp.metaconfig.status.configfailed'|i18n}}">
                                                {{'cmp.metaconfig.status.configfailed'|i18n}}
                                            </li>
                                            <li ng-class="{'current':$Model.searchParams.status==5}"
                                                ng-click="changeStatus(5);"
                                                title="{{'cmp.metaconfig.status.collect.stoping'|i18n}}">
                                                {{'cmp.metaconfig.status.collect.stoping'|i18n}}
                                            </li>
                                            <li ng-class="{'current':$Model.searchParams.status==7}"
                                                ng-click="changeStatus(7);"
                                                title="{{'cmp.metaconfig.status.stoped'|i18n}}">
                                                {{'cmp.metaconfig.status.stoped'|i18n}}
                                            </li>
                                            <li ng-class="{'current':$Model.searchParams.status==2}"
                                                ng-click="changeStatus(2);"
                                                title="{{'cmp.metaconfig.status.stopfailed'|i18n}}">
                                                {{'cmp.metaconfig.status.stopfailed'|i18n}}
                                            </li>
                                        </ul>
                                        </div>
                                    </div>
                                    <div>
                                        <div class="df-btn df-btnicon" ng-click="search()">
                                            <span class="icon-search"></span>
                                            {{'cmp.metaconfig.search'|i18n}}
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="mc-detail-icon-load" ng-show="!isReady"></div>
                                </div>
                                <div>
                                    <div style="float:left;margin-right:20px;">
                                        <div ng-if="!$Model.paramsChanged && !$Model.configingExist && $Model.ableStartCollect"
                                             class="df-btn df-btnicon" ng-click="startCollect()">
                                            {{'cmp.metaconfig.startcollect'|i18n}}
                                        </div>
                                        <div ng-if="$Model.paramsChanged||$Model.configingExist||!$Model.ableStartCollect"
                                             class="df-btn df-btnicon disabled">
                                            {{'cmp.metaconfig.startcollect'|i18n}}
                                        </div>
                                    </div>
                                    <div>
                                        <div ng-if="!$Model.paramsChanged && !$Model.configingExist && $Model.ableStopCollect"
                                             class="df-btn df-btnicon" ng-click="stopCollect()">
                                            {{'cmp.metaconfig.stopcollect'|i18n}}
                                        </div>
                                        <div ng-if="$Model.paramsChanged||$Model.configingExist||!$Model.ableStopCollect"
                                             class="df-btn df-btnicon disabled">
                                            {{'cmp.metaconfig.stopcollect'|i18n}}
                                        </div>
                                    </div>
                                    <div ng-show="$Model.paramsChanged" style="float: left;margin-top: 5px;margin-left: 10px;">
                                        <div class="df-metaconfig-help" title="{{'cmp.metaconfig.collect.paramschanged'|i18n}}"></div>
                                    </div>
                                    <div ng-show="$Model.configingExist" style="float: left;margin-top: 5px;margin-left: 10px;">
                                        <div class="df-metaconfig-help" title="{{'cmp.metaconfig.collect.configingexist'|i18n}}"></div>
                                    </div>
                                </div>
                                <div style="float:left;margin-top:15px;">
                                    <table id="collectStatus" class="tableCommon">
                                        <thead>
                                        <tr>
                                            <th width="20px">
                                                <div class="tableCommon-header"><span></span></div>
                                            </th>
                                            <th style="width:101px;">
                                                <div class="tableCommon-header">
                                                    <span>{{'cmp.metaconfig.collect.ip' | i18n}}</span>
                                                </div>
                                            </th>
                                            <th style="width:150px;">
                                                <div class="tableCommon-header">
                                                    <span>{{'cmp.metaconfig.clustermotype' | i18n}}</span>
                                                </div>
                                            </th>
                                            <th style="width:115px;">
                                                <div class="tableCommon-header">
                                                    <span>{{'cmp.metaconfig.updatetime' | i18n}}</span>
                                                </div>
                                            </th>
                                            <th style="width:75px;">
                                                <div class="tableCommon-header">
                                                    <span>{{'cmp.metaconfig.status' | i18n}}</span>
                                                </div>
                                            </th>
                                            <th style="width:17%;">
                                                <div class="tableCommon-header">
                                                    <span>{{'cmp.metaconfig.collectlogtype' | i18n}}</span>
                                                </div>
                                            </th>
                                        </tr>
                                        </thead>
                                        <tr ng-repeat="row in $Model.collectStatusRes.nodeDispatchStatusList">
                                            <td></td>
                                            <td><span title="{{row.ip}}">{{row.ip}}</span></td>
                                            <td><span title="{{row.clusterMoTypeName}}">{{row.clusterMoTypeName}}</span></td>
                                            <td><span title="{{row.updateTime | transTimeStamp}}">{{row.updateTime |
                                                transTimeStamp}}</span>
                                            </td>
                                            <td>
                                    <span class="metaconfig-status failed" ng-show='row.status==1 || row.status==3' title="{{'cmp.metaconfig.status.configfailed' | i18n}}">
                                        <span></span>
                                            <a class="faildesc-shower" href="javascript:void(0)" style="text-decoration: underline;" ng-click="faildescPage(row.failDescription)">
                                                {{'cmp.metaconfig.status.configfailed' | i18n}}
                                            </a>
                                    </span>
                                                <span class="metaconfig-status failed" ng-show='row.status==2' title="{{'cmp.metaconfig.status.stopfailed' | i18n}}">
                                        <span></span>
                                            <a class="faildesc-shower" style="float: left;text-decoration: underline;" href="javascript:void(0)"ng-click="faildescPage(row.failDescription)">
                                                {{'cmp.metaconfig.status.stopfailed' | i18n}}
                                            </a>
                                    </span>
                                                <span class="metaconfig-status processing" ng-show='row.status==4' title="{{'cmp.metaconfig.status.configing' | i18n}}">
                                        <span class="detail-icon-load"></span>{{'cmp.metaconfig.status.configing' | i18n}}
                                    </span>
                                                <span class="metaconfig-status processing" ng-show='row.status==5' title="{{'cmp.metaconfig.status.collect.stoping' | i18n}}">
                                        <span  class="detail-icon-load"></span>{{'cmp.metaconfig.status.collect.stoping' | i18n}}
                                    </span>
                                                <span class="metaconfig-status succeed" ng-show='row.status==6' title="{{'cmp.metaconfig.status.collecting' | i18n}}">
                                        <span></span>{{'cmp.metaconfig.status.collecting' | i18n}}
                                    </span>
                                                <span class="metaconfig-status stoped" ng-show='row.status==7' title="{{'cmp.metaconfig.status.stoped' | i18n}}">
                                        <span></span>{{'cmp.metaconfig.status.stoped' | i18n}}
                                    </span>
                                            </td>
                                            <td><span title="{{row.logType}}">{{row.logType}}</span></td>
                                        </tr>
                                    </table>
                                </div>
                                <div>
                                    <eview:pagination id="fullPagination" display="true" total-records="$Model.collectStatusRes.totalStatus"
                                        listeners="pageListeners" length-options="[10,25,50,100]" cur-page="1" type="full_numbers"></eview:pagination>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div ng-if="$Model.currentTab==1" ng-controller="AnalyisController" ng-cloak ng-style="{'overflow':overflow}" ng-init="init()">
                    <div class="main-frame" style="padding-left: 0">
                        <div class="main-container analysisColumn">
                            <div id="analysisParam" style="width: 100%;float: left;margin-bottom:40px;">
                                <div style="float:left;margin-right:100px;">
                                    <div class="paramtext">{{'cmp.metaconfig.name'|i18n}}:</div>
                                    <div class="ds-input-frame" style="margin-left: 20px;float:left;">
                                        <input class="df-input" ng-model="$Model.analysisSearchParams.name" type="text"  id="analysisName"
                                            ng-click="dataTypeValidCheck(['cmpValidChar','checkLength'],'analysisName',$Model.analysisSearchParams.name, null, 128)"
                                            ng-change="dataTypeValidCheck(['cmpValidChar','checkLength'],'analysisName',$Model.analysisSearchParams.name, null, 128);analysisParamsChange()"
                                            ng-blur="closeShowTip()"/>
                                    </div>
                                </div>
                                <div>
                                    <p class="paramtext">{{'cmp.metaconfig.collect.logtype'|i18n}}:</p>
                                    <div class="df-select-frame" style="margin-left:20px;">
                                        <div class="df-select-box" cmp-selector>
                                            <input type="text" ng-model="$Model.analysisSearchParams.logType"
                                                   property="$Model.analysisSearchParams.logType" readonly="true"
                                                   style="width: 96px"/>
                                            <div class="df-select-bar"></div>
                                        </div>
                                        <ul class="df-select-menu">
                                            <li ng-repeat="item in $Model.analysisLogTypes"
                                                ng-click="$Model.startingExisted = false;$Model.analysisParamsChanged=true;"
                                                title="{{item}}">{{item}}
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div style="margin:15px 0px 20px 0px;">
                                    <p class="paramtext">{{'cmp.metaconfig.status'|i18n}}:</p>
                                    <div>
                                        <div class="statusItem" ng-class="{'current':$Model.analysisSearchParams.status==0}"
                                             ng-click="changeStatus(0);">{{'cmp.metaconfig.status.all'|i18n}}
                                        </div>
                                        <div class="statusItem" ng-class="{'current':$Model.analysisSearchParams.status==4}"
                                             ng-click="changeStatus(4);">
                                            {{'cmp.metaconfig.status.analysis.starting'|i18n}}
                                        </div>
                                        <div class="statusItem" ng-class="{'current':$Model.analysisSearchParams.status==6}"
                                             ng-click="changeStatus(6);">
                                            {{'cmp.metaconfig.status.analysis.analysising'|i18n}}
                                        </div>
                                        <div class="statusItem" ng-class="{'current':$Model.analysisSearchParams.status==1}"
                                             ng-click="changeStatus(1);">
                                            {{'cmp.metaconfig.status.analysis.startFailed'|i18n}}
                                        </div>
                                    </div>
                                    <div style="margin-left: 95px;">
                                        <div class="statusItem" ng-class="{'current':$Model.analysisSearchParams.status==5}"
                                             ng-click="changeStatus(5);">
                                            {{'cmp.metaconfig.status.analysis.stoping'|i18n}}
                                        </div>
                                        <div class="statusItem" ng-class="{'current':$Model.analysisSearchParams.status==7}"
                                             ng-click="changeStatus(7);">
                                            {{'cmp.metaconfig.status.analysis.stopped'|i18n}}
                                        </div>
                                        <div class="statusItem" ng-class="{'current':$Model.analysisSearchParams.status==2}"
                                             ng-click="changeStatus(2);">
                                            {{'cmp.metaconfig.status.analysis.stopFailed'|i18n}}
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <div class="df-btn df-btnicon" ng-click="search()">
                                        <span class="icon-search"></span>
                                        {{'cmp.metaconfig.search'|i18n}}
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div class="mc-detail-icon-load" ng-show="!isReady"></div>
                            </div>
                            <div>
                                <div style="float:left;margin-right:20px;">
                                    <div ng-if="$Model.analysisParamsChanged||$Model.startingExisted||!$Model.ableStartAnalysising"
                                        class="df-btn df-btnicon disabled">
                                        {{'cmp.metaconfig.startanalysis'|i18n}}
                                    </div>
                                    <div ng-if="!($Model.analysisParamsChanged||$Model.startingExisted||!$Model.ableStartAnalysising)"
                                        class="df-btn df-btnicon"  ng-click="startAnalysis()">
                                        {{'cmp.metaconfig.startanalysis'|i18n}}
                                    </div>
                                </div>
                                <div>
                                    <div ng-if="$Model.analysisParamsChanged||$Model.startingExisted||!$Model.ableStopAnalysising"
                                        class="df-btn df-btnicon disabled">
                                        {{'cmp.metaconfig.stopanalysis'|i18n}}
                                    </div>
                                    <div ng-if="!($Model.analysisParamsChanged||$Model.startingExisted||!$Model.ableStopAnalysising)"
                                        class="df-btn df-btnicon" ng-click="stopAnalysis()">
                                        {{'cmp.metaconfig.stopanalysis'|i18n}}
                                    </div>
                                </div>
                                <div ng-show="$Model.analysisParamsChanged" style="float: left;margin-top: 5px;margin-left: 10px;">
                                    <div class="df-metaconfig-help" title="{{'cmp.metaconfig.analysis.paramschanged'|i18n}}"></div>
                                </div>
                                <div ng-show="$Model.startingExisted" style="float: left;margin-top: 5px;margin-left: 10px;">
                                    <div class="df-metaconfig-help" title="{{'cmp.metaconfig.analysis.startingexist'|i18n}}"></div>
                                </div>
                            </div>
                            <div style="float:left;margin-top:15px;">
                                <table id="analysisStatus" class="tableCommon">
                                    <thead>
                                    <tr>
                                        <th width="20px">
                                            <div class="tableCommon-header"><span></span></div>
                                        </th>
                                        <th style="width:20%;">
                                            <div class="tableCommon-header">
                                                <span>{{'cmp.metaconfig.name' | i18n}}</span>
                                            </div>
                                        </th>
                                        <th style="width:15%;">
                                            <div class="tableCommon-header">
                                                <span>{{'cmp.metaconfig.analysis.solution' | i18n}}</span>
                                            </div>
                                        </th>
                                        <th style="width:15%;">
                                            <div class="tableCommon-header">
                                                <span>{{'cmp.metaconfig.updatetime' | i18n}}</span>
                                            </div>
                                        </th>
                                        <th style="width:15%;">
                                            <div class="tableCommon-header">
                                                <span>{{'cmp.metaconfig.status' | i18n}}</span>
                                            </div>
                                        </th>
                                        <th style="width:20%;">
                                            <div class="tableCommon-header">
                                                <span>{{'cmp.metaconfig.collect.logtype' | i18n}}</span>
                                            </div>
                                        </th>
                                    </tr>
                                    </thead>
                                    <tr ng-repeat="row in $Model.analysisstatusRes.nodeDispatchStatusList track by row.ip">
                                        <td></td>
                                        <td><span title="{{row.ip}}">{{row.ip}}</span></td>
                                        <td><span  title="{{row.solutionMoType}}">{{row.solutionMoType}}</span></td>
                                        <td><span title="{{row.updateTime | transTimeStamp}}">{{row.updateTime | transTimeStamp}}</span>
                                        </td>
                                        <td>
                            <span class="metaconfig-status failed" ng-show='row.status==1 || row.status==3' title="{{'cmp.metaconfig.status.analysis.startFailed' | i18n}}">
                                <span></span>
                                    <a class="faildesc-shower" ng-click="faildescPage(row.failDescription);" href="javascript:void(0)">
                                        {{'cmp.metaconfig.status.analysis.startFailed' | i18n}}</a>
                            </span>
                                            <span class="metaconfig-status failed" ng-show='row.status==2' title="{{'cmp.metaconfig.status.analysis.stopFailed' | i18n}}">
                                            <span></span>
                                    <a class="faildesc-shower" ng-click="faildescPage(row.failDescription);" href="javascript:void(0)">
                                        {{'cmp.metaconfig.status.analysis.stopFailed' | i18n}}</a>
                            </span>
                                            <span class="metaconfig-status processing" ng-show='row.status==4' title="{{'cmp.metaconfig.status.analysis.starting' | i18n}}">
                                <span class="detail-icon-load"></span>{{'cmp.metaconfig.status.analysis.starting' | i18n}}
                            </span>
                                            <span class="metaconfig-status processing" ng-show='row.status==5' title="{{'cmp.metaconfig.status.analysis.stoping' | i18n}}">
                                <span class="detail-icon-load"></span>{{'cmp.metaconfig.status.analysis.stoping' | i18n}}
                            </span>
                                            <span class="metaconfig-status succeed" ng-show='row.status==6' title="{{'cmp.metaconfig.status.analysis.analysising' | i18n}}">
                                <span></span>{{'cmp.metaconfig.status.analysis.analysising' | i18n}}
                            </span>
                                            <span class="metaconfig-status stoped" ng-show='row.status==7' title="{{'cmp.metaconfig.status.analysis.stopped' | i18n}}">
                                <span></span>{{'cmp.metaconfig.status.analysis.stopped' | i18n}}
                            </span>
                                        </td>
                                        <td><span title="{{row.logType}}">{{row.logType}}</span></td>
                                    </tr>
                                </table>
                            </div>
                            <div>
                                <eview:pagination id="analysisPagination" display="true" total-records="$Model.analysisstatusRes.totalStatus"
                                    listeners="analysisListeners" length-options="[10,25,50,100]" cur-page="1" type="full_numbers"></eview:pagination>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tip-message" style="display:none">
                    <div class="cusotm-tip">
                        <div class="input-tip"></div><span id="tipContent" style="color:#e05c5c"></span>
                    </div>
                </div>
            </div>
            <div id="loadingPage" ng-include="'/dvfoundationwebsite/app/loading.html'" style="display:none"></div>
        </div>
    </div>
</body>
