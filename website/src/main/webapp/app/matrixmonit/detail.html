<!DOCTYPE html>
<html xmlns:eview="ignored">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Cache-Control" content="no-cache">
    <link href="/dvfoundationwebsite/css/tableCommon.css" type="text/css" rel="stylesheet"/>
    <link href="/dvfoundationwebsite/css/reset.css" type="text/css" rel="stylesheet"/>
    <link href="/dvfoundationwebsite/css/common.css" type="text/css" rel="stylesheet"/>
    <link href="/dvlogmatrixwebsite/js/controller/plugin/style.min.css" type="text/css" rel="stylesheet"/>
    <link href="/dvfoundationwebsite/css/loading.css" type="text/css" rel="stylesheet"/>
    <link href="/dvfoundationwebsite/css/custom.css" type="text/css" rel="stylesheet"/>

    <link href="/dvlogmatrixwebsite/css/matrixmonit/detail.css" type="text/css" rel="stylesheet"/>
    <style>
        body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, textarea, p, blockquote, th, td, span, a {
            font-size: 12px;
        }

        table.dataTable tbody > tr {
            height: 43px !important;
        }

        table.dataTable thead th {
            padding: 0.0625rem .625rem 0rem .4125rem !important;
        }

    </style>

    <script src="/dvlogmatrixwebsite/js/auth.js" id="licenseScript" name="DVLogmatrixService.Basic"></script>
    <script src="/febs/v1/assets/prelude-loader"></script>
    <script src="/dvfoundationwebsite/js/common.js"></script>

    <script>
      Prel.ready(function () {
        Prel.autoLoad({
          'eView3rdLibs': '*',
          'eViewBasic': '*',
        }).then(function () {
          window.$CmpApp = angular.module('cmp', ['eviewWidgets']);
          setHelpId("mdetailMonitHelpLib_", "cmp.logmatrix.monitor");
          loadJs('/dvfoundationwebsite/js/cmp.js');
          loadJs('/dvlogmatrixwebsite/js/controller/i18n.js');
          loadJs('/dvfoundationwebsite/js/i18n.js');
          loadJs('/dvfoundationwebsite/js/dataCheck.js');
          loadJs('/dvfoundationwebsite/js/dataCheckFunc.js');
          loadJs("/dvlogmatrixwebsite/js/controller/plugin/echarts.js");
          loadJs("/dvfoundationwebsite/js/echart/agileUITheme.js");
          loadJs("/dvfoundationwebsite/js/echart/dataview/dataViewBase.js");
          loadJs("/dvfoundationwebsite/js/echart/dataview/dataViewRectangular.js");
          loadJs("/dvfoundationwebsite/js/echart/dataview/dataViewLine.js");
          loadJs("/dvlogmatrixwebsite/js/controller/matrixmonit/detail.js");
        });
      })
      window.eviewWidgetConfig = window.eviewWidgetConfig || {};
      window.eviewWidgetConfig.theme = 'default';
    </script>
</head>
<body>
<div class="main-frame" ng-cloak ng-controller="detailController" cmp-help-id="42190007" ng-init="init(false)">
    <div class="main-title" style="margin-bottom:10px;margin-top:18px">
        <h3 style="margin-right: 30px;">{{'cmp.matrixmonit.detail.title' | i18n}}</h3>
        <div class="unInitialCount_value">
            <span style="cursor:pointer;" ng-click="backToProfile()">
                < {{'cmp.matrixmonit.detail.return' | i18n}}
            </span>
        </div>
    </div>
    <div class="main-container">
        <div class="flumemonitor-widget">
            <div style="float:left;margin-right:20px;">
                <p class="paramtext">{{'cmp.logmatrix_visual_solutiontype' | i18n}}:</p>
                <div class="df-select-frame" style="margin-left:20px;">
                    <div class="df-select-box" cmp-selector>
                        <input type="text" ng-model="$scope.currentSolutionName" readonly="true"
                               title="{{currentSolutionName}}" style="width: 96px;"/>
                        <div class="df-select-bar"></div>
                    </div>
                    <ul class="df-select-menu">
                        <li ng-repeat="solutionInfo in $Model.solutionInfoList"
                            title="{{solutionInfo.solutionName}}"
                            ng-click="chooseSolutionHandle(solutionInfo.solutionName)">
                            {{solutionInfo.solutionName}}
                        </li>
                    </ul>
                </div>
            </div>
            <div style="float:left;margin-right:20px;">
                <p class="paramtext">{{'cmp.matrixmonit.detail.solution.instancesid' | i18n}}:</p>
                <div class="df-select-frame" style="margin-left:20px;">
                    <div class="df-select-box" cmp-selector>
                        <input type="text" ng-model="selectedSolutionInstances" readonly="true"
                               value="{{'cmp.matrixmonit.detail.all'|i18n}}"
                               title="{{selectedSolutionInstances}}" style="width: 96px;"/>
                        <div class="df-select-bar"></div>
                    </div>
                    <ul class="df-select-menu">
                        <li title="{{'cmp.matrixmonit.detail.all'|i18n}}"
                            ng-click="selectChoose('','solutioninstance')">
                            {{'cmp.matrixmonit.detail.all'|i18n}}
                        </li>
                        <li ng-repeat="(key,value) in $Model.solutionInstanceIds" title="{{value}}"
                            ng-click="selectChoose(key,'solutioninstance')">{{value}}
                        </li>
                    </ul>
                </div>
            </div>
            <div style="float:left;margin-right:20px;">
                <p class="paramtext">{{'cmp.matrixmonit.detail.clustertype' | i18n}}:</p>
                <div class="df-select-frame" style="margin-left:20px;">
                    <div class="df-select-box" cmp-selector>
                        <input type="text" ng-model="selectedClusterType" readonly="true"
                               value="{{'cmp.matrixmonit.detail.all'|i18n}}"
                               title="{{selectedClusterType}}" style="width: 96px;"/>
                        <div class="df-select-bar"></div>
                    </div>
                    <ul class="df-select-menu">
                        <li title="{{'cmp.matrixmonit.detail.all'|i18n}}" ng-click="selectChoose('','clustertype')">
                            {{'cmp.matrixmonit.detail.all'|i18n}}
                        </li>
                        <li ng-repeat="(key,value) in $Model.clusterType" title="{{value}}"
                            ng-click="selectChoose(key,'clustertype')">{{value}}
                        </li>
                    </ul>
                </div>
            </div>
            <div style="float:left;margin-right:20px;">
                <p class="paramtext">{{'cmp.metaconfig.collect.ip'|i18n}}:</p>
                <div id="ip" class="ds-input-frame" style="float:left;margin-left:20px">
                    <input class="df-input" ng-model="$Model.searchParams.ip" id="inputSearchParamsIp" type="text"
                           title="{{$Model.searchParams.ip}}"
                           u-validator="ip"
                           ng-click="dataTypeValidCheck(['nameLengthValidator','cmpValidChar'], 'inputSearchParamsIp', $Model.searchParams.ip,null,128)"
                           ng-change="dataTypeValidCheck(['nameLengthValidator','cmpValidChar'], 'inputSearchParamsIp', $Model.searchParams.ip,null,128)"
                           ng-blur="closeShowTip()"/>
                </div>
            </div>
            <div>
                <p class="paramtext">{{'cmp.metaconfig.collect.logtype'|i18n}}:</p>
                <div class="df-select-frame" style="margin-left:20px;">
                    <div class="df-select-box" cmp-selector>
                        <input type="text" ng-model="selectedLogType" readonly="true"
                               value="{{'cmp.matrixmonit.detail.all'|i18n}}"
                               title="{{selectedLogType}}" style="width: 96px"/>
                        <div class="df-select-bar"></div>
                    </div>
                    <ul class="df-select-menu">
                        <li title="{{'cmp.matrixmonit.detail.all'|i18n}}" ng-click="selectChoose('','logtype')">
                            {{'cmp.matrixmonit.detail.all'|i18n}}
                        </li>
                        <li ng-repeat="item in $Model.logTypes" ng-click="selectChoose(item,'logtype')"
                            title="{{item}}">
                            {{item}}
                        </li>
                    </ul>
                </div>
            </div>
            <div style="margin:15px 0px 20px 0px;">
                <p class="paramtext">{{'cmp.matrixmonit.detail.status'|i18n}}:</p>
                <div>
                    <div class="statusItem"
                         ng-class="{'current':currentStatus== '0'}"
                         ng-click="chooseStatus( '0');">{{'cmp.matrixmonit.good' | i18n}}
                    </div>
                    <div class="statusItem"
                         ng-class="{'current':currentStatus== '1'}"
                         ng-click="chooseStatus( '1');">{{'cmp.matrixmonit.warn' | i18n}}
                    </div>
                    <div class="statusItem"
                         ng-class="{'current':currentStatus== '2'}"
                         ng-click="chooseStatus( '2');">{{'cmp.matrixmonit.error' | i18n}}
                    </div>
                </div>
                <div style="height:32px">
                    <div id="searchbtn" class="df-btn df-btnicon" style="margin-top:15px;margin-bottom: 10px;"
                         ng-click="searchMonitInfoCount()">
                        <span class="icon-search"></span>{{'cmp.logmatrix.submenu.search' | i18n}}

                    </div>
                </div>

                <ul class="distribute-label overview-label clearfix">
                    <div>
                        <p class="refresh-label" style="float: left;margin: 5px 7px 15px 0px;">
                            {{'cmp.matrixmonit.detail.refresh.automatically' | i18n}}：</p>
                        <div class="df-switch" ng-class="{'on':switchIcon}" ng-click="switchStatus($Model)">
                            <p ng-show="switchIcon">YES</p>
                            <p ng-show="!switchIcon">NO</p>
                        </div>
                        <div style="float:left;margin-left:40px;">
                            <p class="paramtext">
                                {{'cmp.matrixmonit.detail.sumtotalrate'|i18n}}{{$Model.sumTotalRate}}</p>
                        </div>
                        <div class="clearfix"></div>
                    </div>
                    <eview:table scroll="false" id="tableParams" enable-pagination="true" cur-page="1" allow-parsing="false"
                                 enable-tooltip="true" display-length="10" display-length-options="[10,25, 50, 100]"
                                 dataset="$Model.monitInfos" pagination-handler="paginationHandler" create-handler="createHandler">
                        <eview:table-column id="id" width="20px" sortable="false"></eview:table-column>
                        <eview:table-column id="ip" width="120px" caption="{{'cmp.metaconfig.collect.ip' | i18n}}"
                                            sortable="false" enable-tooltip="true">
                        </eview:table-column>
                        <eview:table-column id="status" caption="{{'cmp.matrixmonit.detail.status' | i18n}}"
                                            sortable="false"
                                            column-type='custom' enable-tooltip="true"
                                            custom-editor='customStatusEditor'>
                        </eview:table-column>
                        <eview:table-column id="clusterTypeName"
                                            caption="{{'cmp.matrixmonit.detail.clustertype' | i18n}}" sortable="false"
                                            enable-tooltip="true">
                        </eview:table-column>
                        <eview:table-column id="logType" caption="{{'cmp.metaconfig.collect.logtype' | i18n}}"
                                            sortable="false" enable-tooltip="true"></eview:table-column>
                        <eview:table-column id="totalRate"
                                            caption="{{'cmp.matrixmonit.detail.eventdrainsuccessrate' | i18n}}"
                                            sortable="true" enable-tooltip="true"></eview:table-column>
                        <eview:table-column id="metricProblemsText"
                                            caption="{{'cmp.matrixmonit.detail.describe' | i18n}}" column-type='custom'
                                            sortable="false"
                                            enable-tooltip="false" custom-editor='customDescEditor'>
                        </eview:table-column>
                    </eview:table>
                </ul>
            </div>
        </div>
    </div>
    <div class="tip-message" style="display:none">
        <div class="cusotm-tip">
            <div class="input-tip"></div>
            <span id="tipContent" style="color:#e05c5c"></span>
        </div>
    </div>

    <div id="loadingPage" ng-include="'/dvfoundationwebsite/app/loading.html'" style="display:none"></div>
</div>
</body>
</html>
