<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <link href="/dvfoundationwebsite/css/reset.css" type="text/css" rel="stylesheet"/>
    <link href="/dvfoundationwebsite/css/common.css" type="text/css" rel="stylesheet"/>
    <link href="/dvihealingwebsite/css/healing/workbenchindex.css" type="text/css" rel="stylesheet">
    <title>Title</title>
    <link href="/dvfoundationwebsite/css/tableCommon.css" type="text/css" rel="stylesheet"/>
    <link href="/dvfoundationwebsite/css/tipmessage.css" type="text/css" rel="stylesheet">

    <link href="/dvihealingwebsite/css/healing/flow.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/healing.css" type="text/css" rel="stylesheet">

    <link href="/dvihealingwebsite/css/healing/cmp-ihealing-event.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/api.css" type="text/css" rel="stylesheet">

    <link href="/dvihealingwebsite/css/healing/index.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/healing.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/flow.css" type="text/css" rel="stylesheet">

    <style>
        body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, textarea, p, blockquote, th, td, span, a {
            font-size: 12px;
        }

        .current span {
            color: #666;
        }

        .servicestatus {
            color: #5ecc49 !important;
        }

        .servicestatus.offline {
            color: #fc5043 !important;
        }

        .servicestatus.unavailable {
            color: #ffa235 !important;
        }

        table .checkbox {
            display: block;
        }

        #flowParameterTips p {
            width: 350px;
        }

        .ztree li span.button.ico_open, .ztree li span.button.ico_close, .ztree li span.button.ico_docu
        {
            width: 0px;
        }
    </style>
    <script src="/dvihealingwebsite/js/auth.js" id="licenseScript" name="DVIhealingWebsite.BasicAccess"></script>
    <script src="/febs/v1/assets/prelude-loader"></script>
    <script src="/dvfoundationwebsite/js/common.js"></script>

    <script>
      Prel.ready(function() {
        Prel.autoLoad({
          eView3rdLibs: '*',
          eViewBasic: '*',
        }).then(function() {
          window.$CmpApp = angular.module('cmp', ['eviewWidgets']);
          loadJs('/dvfoundationwebsite/js/cmp.js');
          loadJs('/dvihealingwebsite/js/controller/healing/i18n.js');
          loadJs('/dvihealingwebsite/js/controller/healing/i18n_en_us.js');
          loadJs('/dvihealingwebsite/js/controller/healing/i18n_zh_cn.js');
          loadJs('/dvfoundationwebsite/js/i18n.js');
          loadJs('/dvworkbenchwebsite/js/controller/workbench/i18n.js');
          loadJs('/dvfoundationwebsite/js/dataCheck.js');
          loadJs('/dvfoundationwebsite/js/dataCheckFunc.js');
          loadJs('/dvfoundationwebsite/js/tipmessage.js');
          loadJs('/dvihealingwebsite/js/filter/healing/filter.js');
          loadJs('/dvihealingwebsite/js/controller/healing/refreshfourpage.js');
          loadJs('/dvworkbenchwebsite/js/controller/workbenchBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/healing.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/createflow.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/createflow/createFlowBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/selectionParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/createflow/configAlarmEvent.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/flowParamTable.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/dbAccountParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/flowImg.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/remoteNotifyBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/stringParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/scriptParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/cascadeParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/ipListParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/paramValidateHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/dateParamBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/neInstanceParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowWindow/selectAccountBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/getInfoHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/getFlowModelHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/getJbmeModelHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/extendNode.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/flowCategoryBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/basicInfoValidator.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/eventConfigBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/jbmeModelBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/atomNode.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/atomNodeParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/toolBox.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/pauseNode.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/atomApiDetail.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/atomExecuteServer.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/moTypeTree.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowWindow/selectHostHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowWindow/selectHostWindow.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowWindow/redirectHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowWindow/windowHelper.js');
          loadJs('/dvworkbenchwebsite/js/controller/workbench/taskManagement/workbench.js');
          loadJs('/dvworkbenchwebsite/js/controller/workbench/taskManagement/selectServer.js');
          loadJs('/dvworkbenchwebsite/js/controller/workbench/taskManagement/selectServerBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/api/newApi.js');
          loadJs('/dvihealingwebsite/js/controller/healing/api/apiLibrary.js', function() {
            $CmpApp.controller('HealingCtrl',
              ['$scope', '$filter', '$rootScope', '$sce', '$q', '$compile', '$timeout', HealingCtrl]);
            $CmpApp.controller('CreateflowCtrl',
              ['$scope', '$timeout', '$q', 'RefreshService', '$rootScope', '$UI', CreateflowCtrl]);
            $CmpApp.controller('SelectServerCtrl', ['$scope', '$rootScope', SelectServerCtrl]);
            $CmpApp.controller('WorkbenchCtrl',
              ['$scope', '$rootScope', '$timeout', '$filter', WorkbenchCtrl]);
            $CmpApp.controller('NewApiCtrl', ['$scope', '$q', '$filter', NewApiCtrl]);
            $CmpApp.controller('ApiStoreCtrl', ['$scope', '$q', '$filter', ApiStoreCtrl]);
            angular.bootstrap(document, ['cmp']);
          });
        });
      });
      window.eviewWidgetConfig = window.eviewWidgetConfig || {};
      window.eviewWidgetConfig.theme = 'default';
    </script>
</head>

<body ng-controller="HealingCtrl">
<div ng-controller="CreateflowCtrl" ng-cloak ng-init="initAtomConfig()">
    <div class="tip-message" style="display:none">
        <div class="cusotm-tip">
            <div class="input-tip"></div>
            <span id="tipContent" style="color:#e05c5c"></span>
        </div>
    </div>
    <div class="atom-pop-header" style="padding-top: 20px;">
        <ul ng-if="atom.type === 'sql'">
            <li>
                <div class="header-title current">
                    {{'cmp.healing.flow.chose.atom' | i18n}}
                </div>
            </li>
            <li>
                <div class="header-title" ng-class="{'current':page.selectIndex>=1}">
                    {{'cmp.healing.flow.set.param' | i18n}}
                </div>
            </li>
        </ul>
        <ul ng-if="atom.type !== 'sql'">
            <li>
                <div class="header-title current">
                    {{'cmp.healing.flow.chose.atom' | i18n}}
                </div>
            </li>
            <li>
                <div class="header-title" ng-class="{'current':page.selectIndex>=1}">
                    {{'cmp.healing.flow.set.param' | i18n}}
                </div>
            </li>
            <li>
                <div class="header-title" ng-class="{'current':page.selectIndex==2}">
                    {{'cmp.healing.flow.chose.host' | i18n}}
                </div>
            </li>
        </ul>
        <div class="bar-span">
            <div ng-if="atom.type === 'sql'" class="current-bar"
                 ng-style="{'width':page.selectIndex==0?'50%':'100%'}"></div>
            <div ng-if="atom.type !== 'sql'" class="current-bar"
                 ng-style="{'width':page.selectIndex==0?'33%':(page.selectIndex==1?'66%':'100%')}"></div>
        </div>
    </div>
    <!--步骤一：选择原子-->
    <div class="atom-pop-content" ng-show="page.selectIndex==0" style="width:1210px;overflow: hidden">
        <table>
            <tr>
                <td>
                    <p style="color: #666;width: 120px;">
                        <span>{{'cmp.healing.flow.atom.solution' | i18n}}</span>
                    </p>
                </td>
                <td style="padding-right:50px">
                    <div class="df-select-frame" style="margin-left:10px;">
                        <div class="df-select-box" cmp-selector>
                            <input readonly="readonly" style="width: 160px;" ng-model="atomSolution"/>
                            <div class="df-select-bar"></div>
                        </div>
                        <ul class="df-select-menu">
                            <li ng-repeat="solution in atomSolutions" title="{{solution}}"
                                ng-click="queryAtomList(solution)">{{solution}}
                            </li>
                        </ul>
                    </div>
                </td>
                <td>
                    <span>{{'cmp.healing.flow.is.believable' | i18n}}</span>
                </td>
                <td>
                    <div class="df-select-frame" style="margin-left:10px;">
                        <div class="df-select-box" cmp-selector>
                            <input type="text" value="{{'cmp_healing_atom_func_all' | i18n}}" disabled/>
                            <div class="df-select-bar"></div>
                        </div>
                        <ul class="df-select-menu">
                            <li ng-click="goBelievable()">{{'cmp_healing_atom_func_all' | i18n}}</li>
                            <li ng-click="goBelievable('hwWithSign')">{{'cmp.healing.flow.yesyes' | i18n}}</li>
                            <li ng-click="goBelievable('noSign')">{{'cmp.healing.flow.nono' | i18n}}</li>
                        </ul>
                    </div>
                </td>
                <td style="width: 45%">
                    <div class="df-input-frame" cmp-input-focus style="float:right;" id="search_atom_frame">
                        <input class="df-input" placeholder="{{'cmp.healing.flow.find.atom' | i18n}}"
                               id="searchAtomName" ng-model="searchAtomName"
                               title="{{searchAtomName || 'cmp.healing.flow.find.atom' | i18n}}"
                               ng-change="dataTypeValidCheck(['alpha','nameLengthValidator'],'search_atom_frame',searchAtomName,null,128)"
                               ng-click="dataTypeValidCheck(['alpha','nameLengthValidator'],'search_atom_frame',searchAtomName,null,128)"
                               ng-blur="closeShowTip()" ng-keyup="onInputEnter($event,atomSolution,searchAtomName)"/>
                        <div class="df-icon-search" ng-click="onClickSearch(atomSolution,searchAtomName)">
                        </div>
                    </div>
                </td>
            </tr>
        </table>

        <div class="clearfix"></div>
        <div class="atom-list">
            <div class="atom-pick-frame" style="overflow: auto">
                <div class="left-atom-list" id="left-atom-list-div">
                    <div class="letter-atom" ng-repeat="letter in englishList" id="letter-atom-div">
                        <div id="{{getLetterId(letter)}}" ng-if="filterLetterArray(atoms,letter).length>0"
                             class="left-letter-div">
                            <ul id="atom-letter-title">
                                <div class="letter-title">
                                    {{letter}}
                                </div>
                            </ul>
                            <ul ng-repeat="atom in filterLetterArray(atoms,letter)" title="{{atom.name}}"
                                class="atom-pick"
                                ng-class="{'current':page.atomName===atom.name && page.moType === atom.moType}"
                                ng-click="page.atomName=atom.name;page.moType=atom.moType">
                                <div class="flow-icon"
                                     ng-class="{'flow-icon-green':atom.func=='DIAG','flow-icon-orange':atom.func=='HEAL'}">
                                </div>
                                {{getAtomName(atom.name)}}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="right-atom-bar" ng-if="true">
                    <div ng-repeat="letter in englishList" ng-if="filterLetterArray(atoms,letter).length>0"
                         class="right-letter">
                        <a ng-click="moveScroll(letter)">{{letter}}</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--步骤二：配置原子参数-->
    <div ng-show="page.selectIndex==1" ng-init="optApi = {};optApi.showApi = false">
        <!--原子参数配置-->
        <div class="atom-pop-content" ng-show="!optApi.showApi" style="width:1100px">
            <div class="main-title-atom">
                <ul>
                    <li>
                        <div class="atom-content-title">
                            {{'cmp.healing.flow.atom.name' | i18n}}
                        </div>
                        <div class="atom-content-title">
                            {{atom.name}}
                        </div>
                    </li>
                    <li>
                        <div class="atom-content-title">
                            {{'cmp.healing.flow.solution.colon' | i18n}}
                        </div>
                        <div class="atom-content-title">
                            {{atom.topMoType}}
                        </div>
                    </li>
                    <li>
                        <div class="atom-content-title">
                            {{'cmp.healing.flow.atom.description' | i18n}}
                        </div>
                        <div class="atom-content-title" style="word-break: break-all;display: inline;"
                             ng-if="(atom.type === 'dsf' || atom.type === 'ebus') && ($Global.$Lang() === 'en_US')">
                            {{atom.descEn}}
                        </div>
                        <div class="atom-content-title" style="word-break: break-all;display: inline;"
                             ng-if="(atom.type === 'dsf' || atom.type === 'ebus') && ($Global.$Lang() !== 'en_US')">
                            {{atom.descLocal}}
                        </div>
                        <div class="atom-content-title" style="word-break: break-all;display: inline;"
                             ng-if="!(atom.type === 'dsf' || atom.type === 'ebus')">
                            {{atom.descLocal}}
                        </div>
                    </li>
                </ul>
            </div>
            <div id="atom_param_frame"
                 style="border-top: 1px solid #e5e5e5;box-shadow: 0px 1px 0px #fff;padding-top: 10px;margin-top: 10px;">
                <div ng-if="atom.type !== 'sql'" class="atom-content-title" style="line-height:200%">
                    {{'cmp.healing.flow.create.tips.atom.param.source' | i18n}}
                </div>
                <div ng-if="atom.type !== 'sql'" class="atom-content-title" style="line-height:200%;margin-top: -15px">
                    {{'cmp.healing.flow.create.tips.atom.self.param.type' | i18n}}
                </div>
                <table class="atom-param-table">
                    <tr ng-repeat="commonParam in atom.commonParams track by $index"
                        ng-if="commonParam.name!='ips' && commonParam.name!='user'">
                        <td style="width: 120px">
                            <div style="overflow: hidden; white-space: nowrap; text-overflow:ellipsis;color:#666;width:120px;">
                                <span style="color:#fc5043;">*</span>
                                <span title="{{commonParam.name}}:">{{commonParam.name}}:</span>
                            </div>
                        </td>
                        <td ng-if="commonParam.name === 'timeoutSeconds'">
                            <div class="df-label-tips" style="margin-top:3px;" ng-init="isShowAtomParamTips = false"
                                 ng-mouseover="isShowAtomParamTips = true" ng-mouseout="isShowAtomParamTips = false">
                                <div class="df-label-bar"></div>
                                <div ng-cloak class="df-tips left" ng-show="isShowAtomParamTips">
                                    <p class="spcial-warp"
                                       ng-bind="'cmp.healing.flow.create.tips.atom.param' | i18n"></p>
                                    <div class="df-tips-arrow"></div>
                                </div>
                            </div>
                        </td>
                        <td ng-if="commonParam.name === 'account'">
                            <div class="df-label-tips" style="margin-top:3px;" ng-init="isShowAtomParamTips = false"
                                 ng-mouseover="isShowAtomParamTips = true" ng-mouseout="isShowAtomParamTips = false">
                                <div class="df-label-bar"></div>
                                <div ng-cloak class="df-tips left" ng-show="isShowAtomParamTips">
                                    <p class="spcial-warp"
                                       ng-bind="'cmp.healing.flow.create.tips.atom.param.account' | i18n"></p>
                                    <div class="df-tips-arrow"></div>
                                </div>
                            </div>
                        </td>
                        <td ng-if="commonParam.name === 'resultIp'">
                            <div class="df-label-tips" style="margin-top:3px;" ng-init="isShowAtomParamTips = false"
                                 ng-mouseover="isShowAtomParamTips = true" ng-mouseout="isShowAtomParamTips = false">
                                <div class="df-label-bar"></div>
                                <div ng-cloak class="df-tips left" ng-show="isShowAtomParamTips">
                                    <p class="spcial-warp"
                                       ng-bind="'cmp.healing.flow.create.tips.atom.param.resultIp' | i18n"></p>
                                    <div class="df-tips-arrow"></div>
                                </div>
                            </div>
                        </td>
                        <td ng-if="commonParam.name === 'resultFile'">
                            <div class="df-label-tips" style="margin-top:3px;" ng-init="isShowAtomParamTips = false"
                                 ng-mouseover="isShowAtomParamTips = true" ng-mouseout="isShowAtomParamTips = false">
                                <div class="df-label-bar"></div>
                                <div ng-cloak class="df-tips left" ng-show="isShowAtomParamTips">
                                    <p class="spcial-warp"
                                       ng-bind="'cmp.healing.flow.create.tips.atom.param.resultFile' | i18n"></p>
                                    <div class="df-tips-arrow"></div>
                                </div>
                            </div>
                        </td>
                        <td ng-if="commonParam.name != 'timeoutSeconds' && commonParam.name != 'account' && commonParam.name != 'resultIp' && commonParam.name != 'resultFile'">
                            <div class="df-label-tips" style="margin-top:3px;"
                                 ng-if="commonParam.descEn && commonParam.descEn.length>0 || commonParam.descLocal && commonParam.descLocal.length>0"
                                 ng-init="isShowCommonParamTips = false" ng-mouseover="isShowCommonParamTips = true"
                                 ng-mouseout="isShowCommonParamTips = false">
                                <div class="df-label-bar"></div>
                                <div ng-cloak class="df-tips left" ng-show="isShowCommonParamTips"
                                     ng-if="commonParam.descEn && (atom.type === 'dsf' || atom.type === 'ebus') && ($Global.$Lang() === 'en_US')">
                                    <p class="spcial-warp"> {{commonParam.descEn}}</p>
                                    <div class="df-tips-arrow"></div>
                                </div>
                                <div ng-cloak class="df-tips left" ng-show="isShowCommonParamTips"
                                     ng-if="commonParam.descLocal && (atom.type === 'dsf' || atom.type === 'ebus') && ($Global.$Lang() !== 'en_US')">
                                    <p class="spcial-warp"> {{commonParam.descLocal}}</p>
                                    <div class="df-tips-arrow"></div>
                                </div>
                                <div ng-cloak class="df-tips left" ng-show="isShowCommonParamTips"
                                     ng-if="commonParam.descLocal && !(atom.type === 'dsf' || atom.type === 'ebus')">
                                    <p class="spcial-warp"> {{commonParam.descLocal}}</p>
                                    <div class="df-tips-arrow"></div>
                                </div>
                            </div>
                        </td>

                        <td class="fixed-width">
                            <div class="df-input-frame" ng-if="commonParam.name === 'account' || commonParam.name === 'resultIp'">
                                <input class="df-input requestParamsValid" title="{{commonParam.value}}" ng-model="commonParam.value" style="width: 130px;" value="{{commonParam.value}}" id="commonParamValue_{{commonParam.name}}"
                                       ng-change="dataTypeValidCheck(['required','alpha','nameLengthValidator'],'commonParamValue_{{commonParam.name}}',commonParam.value,null,128)"
                                       ng-click="dataTypeValidCheck(['required','alpha','nameLengthValidator'],'commonParamValue_{{commonParam.name}}',commonParam.value,null,128)"
                                       ng-blur="closeShowTip()"
                                       placeholder="{{'cmp.healing.flow.create.tips.sql.atom.param' | i18n}}" />
                            </div>
                            <div class="df-input-frame" ng-if="commonParam.name === 'resultFile'">
                                <input class="df-input requestParamsValid" title="{{commonParam.value}}" ng-model="commonParam.value" style="width: 130px;" value="{{commonParam.value}}" id="commonParamValue_resultFile"
                                       ng-change="dataTypeValidCheck(['required','alpha','nameLengthValidator'],'commonParamValue_{{commonParam.name}}',commonParam.value,null,128)"
                                       ng-click="dataTypeValidCheck(['required','alpha','nameLengthValidator'],'commonParamValue_resultFile',commonParam.value,null,128)"
                                       ng-blur="closeShowTip()"
                                       placeholder="{{'cmp.healing.flow.create.tips.sql.atom.param.resultFile.desc' | i18n}}" />
                            </div>
                            <div ng-if="atom.createMode === 'BUILD_IN'|| commonParam.name === 'timeoutSeconds'|| commonParam.name === 'dbType_preset'|| commonParam.name === 'dbIpOrDn_preset'
							|| commonParam.name === 'dbPort_preset'|| commonParam.name === 'dbName_preset'|| commonParam.name === 'dbUsername_preset' && commonParam.name != 'account' && commonParam.name != 'resultIp' && commonParam.name != 'resultFile'"
                                 class="df-select-frame">
                                <div class="df-select-box" style="color: #ececec" cmp-selector>
                                    <input readonly="readonly" style="width: 120px;color: #999999"
                                           value="{{'cmp.healing.flow.self.define' | i18n}}"
                                           ng-if="commonParam.source === 'DEFINITION'"/>
                                    <div class="df-select-bar" style="color: #999999"></div>
                                </div>
                            </div>

                            <div ng-if="atom.createMode !== 'BUILD_IN' && commonParam.name != 'timeoutSeconds' && commonParam.name != 'dbType_preset'&& commonParam.name != 'dbIpOrDn_preset'
							&& commonParam.name != 'dbPort_preset'&& commonParam.name != 'dbName_preset'&& commonParam.name != 'dbUsername_preset' && commonParam.name != 'account' && commonParam.name != 'resultIp' && commonParam.name != 'resultFile'"
                                 class="df-select-frame">
                                <div class="df-select-box" cmp-selector>
                                    <input readonly="readonly" style="width: 120px;"
                                           value="{{'cmp.healing.flow.self.define' | i18n}}"
                                           ng-if="commonParam.source === 'DEFINITION'"/>
                                    <input readonly="readonly" style="width: 120px;"
                                           value="{{'cmp.healing.flow.alarm.informationn' | i18n}}"
                                           ng-if="commonParam.source === 'ALARM'"/>
                                    <input readonly="readonly" style="width: 120px;"
                                           value="{{'cmp.healing.flow.select.from.flow.params' | i18n}}"
                                           ng-if="commonParam.source === 'GLOBAL'"/>
                                    <div class="df-select-bar"></div>
                                </div>
                                <ul class="df-select-menu">
                                    <li
                                            ng-click="commonParam.source='DEFINITION';commonParam.value=commonParam.defaultValue">
                                        {{'cmp.healing.flow.self.define' | i18n}}
                                    </li>
                                    <li ng-click="commonParam.source='ALARM';commonParam.value='objectInstance'">
                                        {{'cmp.healing.flow.alarm.information' | i18n}}
                                    </li>
                                    <li ng-click="commonParam.source='GLOBAL';commonParam.value=null">
                                        {{'cmp.healing.flow.select.from.flow.params' | i18n}}
                                    </li>
                                </ul>
                            </div>

                        </td>
                        <td class="fixed-width" ng-if="commonParam.source == 'DEFINITION'">
                            <div ng-init="commonParam.paramType='STRINGTYPE'">
                                <div ng-if="atom.createMode === 'BUILD_IN'|| commonParam.name === 'timeoutSeconds'|| commonParam.name === 'dbType_preset'|| commonParam.name === 'dbIpOrDn_preset'
							|| commonParam.name === 'dbPort_preset'|| commonParam.name === 'dbName_preset'|| commonParam.name === 'dbUsername_preset'"
                                     class="df-select-frame">
                                    <div class="df-select-box" style="color: #ececec" cmp-selector>
                                        <input readonly="readonly" style="width: 215px;color: #999999"
                                               value="{{'cmp.healing.flow.para.string.type' | i18n}}"
                                               ng-if="commonParam.source === 'DEFINITION'"/>
                                        <div class="df-select-bar" style="color: #999999"></div>
                                    </div>
                                </div>
                            </div>
                            <div ng-if="atom.createMode !== 'BUILD_IN' && commonParam.name != 'timeoutSeconds' && commonParam.name != 'dbType_preset'&& commonParam.name != 'dbIpOrDn_preset'
							&& commonParam.name != 'dbPort_preset'&& commonParam.name != 'dbName_preset'&& commonParam.name != 'dbUsername_preset' && commonParam.name != 'account' && commonParam.name != 'resultIp' && commonParam.name != 'resultFile'"
                                 class="df-select-frame" ng-init="commonParam.paramType='STRINGTYPE'">
                                <div class="df-select-box" cmp-selector>
                                    <input readonly="readonly" style="width: 215px;"
                                           value="{{'cmp.healing.flow.para.string.type' | i18n}}"
                                           ng-if="commonParam.paramType === 'STRINGTYPE'"/>
                                    <input readonly="readonly" style="width: 215px;"
                                           value="{{'cmp.healing.flow.para.date.type' | i18n}}"
                                           ng-if="commonParam.paramType === 'DATETYPE'"/>
                                    <div class="df-select-bar"></div>
                                </div>
                                <ul class="df-select-menu">
                                    <li ng-click="commonParam.paramType='STRINGTYPE';">
                                        {{'cmp.healing.flow.para.string.type' | i18n}}
                                    </li>
                                    <li ng-click="commonParam.paramType='DATETYPE';">
                                        {{'cmp.healing.flow.para.date.type' | i18n}}
                                    </li>
                                </ul>
                            </div>
                        </td>
                        <td style="width:600px">
                            <div class="df-input-frame" ng-if="commonParam.source == 'DEFINITION'">
                                <input ng-if="commonParam.name === 'dbUsername_preset' && commonParam.type !== 'java.lang.Integer' && atom.createMode !== 'BUILD_IN'"
                                       class="df-input" title="{{commonParam.value}}" ng-model="commonParam.value" style="width: 225px;"
                                       value="{{commonParam.value}}" id="commonParamValue_09"
                                       ng-change="dataTypeValidCheck(['nameLengthValidator'],'commonParamValue_09',commonParam.value,null,128)"
                                       ng-click="dataTypeValidCheck(['nameLengthValidator'],'commonParamValue_09',commonParam.value,null,128)"
                                       ng-blur="closeShowTip()"/>
                                <input
                                        ng-if="commonParam.name === 'dbIpOrDn_preset' && commonParam.type !== 'java.lang.Integer' && atom.createMode !== 'BUILD_IN'"
                                        id="commonParamValue" class="df-input" title="{{commonParam.value}}" ng-model="commonParam.value"
                                        style="width: 225px;" value="{{commonParam.value}}"
                                        ng-change="dataTypeValidCheck(['required','validIpOrDn','nameLengthValidator'],'commonParamValue',commonParam.value,null,128)"
                                        ng-click="dataTypeValidCheck(['required','validIpOrDn','nameLengthValidator'],'commonParamValue',commonParam.value,null,128)"
                                        ng-blur="closeShowTip()"/>
                                <input readonly="readonly"
                                       ng-if="commonParam.type !== 'java.lang.Integer' && atom.createMode === 'BUILD_IN'"
                                       class="df-input" title="{{commonParam.value}}" ng-model="commonParam.value" style="width: 225px;"
                                       value="{{commonParam.value}}" id="commonParamValue_02"
                                       ng-change="dataTypeValidCheck(['nameLengthValidator'],'commonParamValue_02',commonParam.value,null,128)"
                                       ng-click="dataTypeValidCheck(['nameLengthValidator'],'commonParamValue_02',commonParam.value,null,128)"
                                       ng-blur="closeShowTip()"/>

                                <input
                                        ng-if="commonParam.type === 'java.lang.Integer' && atom.createMode !== 'BUILD_IN' && commonParam.name !== 'timeoutSeconds' && commonParam.name !== 'dbPort_preset'"
                                        class="df-input" title="{{commonParam.value}}" ng-model="commonParam.value" style="width: 225px;"
                                        value="{{commonParam.value}}" id="commonParamValue_03"
                                        ng-change="dataTypeValidCheck(['range'],'commonParamValue_03',commonParam.value,-2147483648,2147483647)"
                                        ng-click="dataTypeValidCheck(['range'],'commonParamValue_03',commonParam.value,-2147483648,2147483647)"
                                        ng-blur="closeShowTip()"/>
                                <input readonly="readonly"
                                       ng-if="commonParam.type === 'java.lang.Integer' && atom.createMode === 'BUILD_IN' && commonParam.name !== 'timeoutSeconds'"
                                       class="df-input" id="commonParamValue_04" title="{{commonParam.value}}" ng-model="commonParam.value"
                                       style="width: 225px;" value="{{commonParam.value}}"
                                       ng-change="dataTypeValidCheck(['range'],'commonParamValue_04',commonParam.value,-2147483648,2147483647)"
                                       ng-click="dataTypeValidCheck(['range'],'commonParamValue_04',commonParam.value,-2147483648,2147483647)"
                                       ng-blur="closeShowTip()"/>
                                <input ng-if="commonParam.name === 'timeoutSeconds'" class="df-input"
                                       id="commonParamValue_05" title="{{commonParam.value}}" ng-model="commonParam.value" style="width: 224px;"
                                       value="{{commonParam.value}}"
                                       class="requestParamsValid"
                                       ng-change="dataTypeValidCheck(['required','validFlowPara','range'],'commonParamValue_05',commonParam.value,1,7200)"
                                       ng-click="dataTypeValidCheck(['required','validFlowPara','range'],'commonParamValue_05',commonParam.value,1,7200)"
                                       ng-blur="closeShowTip()"/>
                            </div>
                            <div class="df-select-frame" ng-if="commonParam.source == 'ALARM'"
                                 style="margin-right: 10px;">
                                <div class="df-select-box" cmp-selector>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.extra.information' | i18n}}"
                                           ng-if="commonParam.value === 'additionalInformation'"/>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.location.information' | i18n}}"
                                           ng-if="commonParam.value === 'objectInstance'"/>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.location.information' | i18n}}"
                                           ng-if="commonParam.value !== 'additionalInformation' && commonParam.value !== 'objectInstance'"/>
                                    <div class="df-select-bar"></div>
                                </div>
                                <ul class="df-select-menu">
                                    <li ng-click="commonParam.value='objectInstance'">
                                        {{'cmp.healing.flow.location.information' | i18n}}
                                    </li>
                                    <li ng-click="commonParam.value='additionalInformation'">
                                        {{'cmp.healing.flow.extra.information' | i18n}}
                                    </li>
                                </ul>

                            </div>
                            <div class="df-select-frame" ng-if="commonParam.source == 'ALARM'">
                                <div class="df-select-box" cmp-selector>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.regular.expression' | i18n}}"
                                           ng-if="commonParam.ruleType === 'REGEXP'"/>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.match' | i18n}}"
                                           ng-if="commonParam.ruleType === 'STARTENDKEY'"/>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.match' | i18n}}"
                                           ng-if="commonParam.ruleType !== 'REGEXP' && commonParam.ruleType !== 'STARTENDKEY'"/>
                                    <div class="df-select-bar"></div>
                                </div>
                                <ul class="df-select-menu">
                                    <li ng-click="commonParam.ruleType='STARTENDKEY'">
                                        {{'cmp.healing.flow.match' | i18n}}
                                    </li>
                                    <li ng-click="commonParam.ruleType='REGEXP'">
                                        {{'cmp.healing.flow.regular.expression' | i18n}}
                                    </li>
                                </ul>

                            </div>
                        </td>

                        <td ng-if="commonParam.ruleType == 'STARTENDKEY' && commonParam.source == 'ALARM'">
                            <div class="df-input-frame">
                                <input class="df-input" ng-model="commonParam.startRule" style="width: 225px;"
                                       id="startRule" value="{{commonParam.startRule}}"
                                       ng-change="dataTypeValidCheck(['nameLengthValidator'],'startRule',commonParam.startRule,null,128)"
                                       ng-click="dataTypeValidCheck(['nameLengthValidator'],'startRule',commonParam.startRule,null,128)"
                                       ng-blur="closeShowTip()"
                                       placeholder="{{'cmp.healing.flow.first.key' | i18n}}"/>
                            </div>
                            <div class="df-input-frame">
                                <input class="df-input" ng-model="commonParam.endRule" style="width: 225px;"
                                       value="{{commonParam.endRule}}" id="commonParamEndRule"
                                       ng-change="dataTypeValidCheck(['nameLengthValidator'],'commonParamEndRule',commonParam.endRule,null,128)"
                                       ng-click="dataTypeValidCheck(['nameLengthValidator'],'commonParamEndRule',commonParam.endRule,null,128)"
                                       ng-blur="closeShowTip()" placeholder="{{'cmp.healing.flow.last.key' | i18n}}"/>
                            </div>
                        </td>

                        <td ng-if="commonParam.ruleType == 'REGEXP' && commonParam.source == 'ALARM'">
                            <div class="df-input-frame">
                                <input class="df-input" ng-model="commonParam.rule" style="width: 225px;"
                                       value="{{commonParam.rule}}" id="commonParamRule"
                                       ng-change="dataTypeValidCheck(['required','nameLengthValidator'],commonParamRule,commonParam.rule,null,128)"
                                       ng-click="dataTypeValidCheck(['required','nameLengthValidator'],commonParamRule,commonParam.rule,null,128)"
                                       ng-blur="closeShowTip()"
                                       placeholder="{{'cmp.healing.flow.rule.type' | i18n}}"/>
                            </div>
                        </td>
                        <td>

                        </td>
                    </tr>
                </table>
                <table class="atom-param-table">
                    <tr ng-repeat="requestParam in atom.requestParams"
                        ng-if="requestParam.name!='ips' && requestParam.name!='user'">
                        <td style="width: 120px">
                            <div
                                    style="overflow: hidden; white-space: nowrap; text-overflow:ellipsis;color:#666;width:91px;">
                                <div ng-if="requestParam.required == true || requestParam.required == 'true' || requestParam.required == null"
                                     style="overflow: hidden;text-overflow:ellipsis;">
                                    <span style="color:#fc5043;">*</span>
                                    <span title="{{requestParam.name}}:">{{requestParam.name}}:</span>
                                </div>
                                <div ng-if="requestParam.required == false || requestParam.required == 'false'"
                                     style="overflow: hidden;text-overflow:ellipsis;">
                                    <span title="{{requestParam.name}}:">{{requestParam.name}}:</span>
                                </div>
                            </div>

                        </td>
                        <td style="width: 15px">
                            <div class="df-label-tips" style="margin-top:3px;"
                                 ng-if="requestParam.descEn && requestParam.descEn.length>0 || requestParam.descLocal && requestParam.descLocal.length>0"
                                 ng-init="isShowRequestParamTips = false"
                                 ng-mouseover="isShowRequestParamTips = true"
                                 ng-mouseout="isShowRequestParamTips = false">
                                <div class="df-label-bar">
                                </div>
                                <div ng-cloak class="df-tips left" ng-show="isShowRequestParamTips"
                                     ng-if="requestParam.descEn && (atom.type === 'dsf' || atom.type === 'ebus') && ($Global.$Lang() === 'en_US')">
                                    <p class="spcial-warp" style="white-space: pre-line;"> {{requestParam.descEn}}
                                    </p>
                                    <div class="df-tips-arrow"></div>
                                </div>
                                <div ng-cloak class="df-tips left" ng-show="isShowRequestParamTips"
                                     ng-if="requestParam.descLocal && (atom.type === 'dsf' || atom.type === 'ebus') && ($Global.$Lang() !== 'en_US')">
                                    <p class="spcial-warp" style="white-space: pre-line;">
                                        {{requestParam.descLocal}}</p>
                                    <div class="df-tips-arrow"></div>
                                </div>
                                <div ng-cloak class="df-tips left" ng-show="isShowRequestParamTips"
                                     ng-if="requestParam.descLocal && !(atom.type === 'dsf' || atom.type === 'ebus')">
                                    <p class="spcial-warp"> {{requestParam.descLocal}}</p>
                                    <div class="df-tips-arrow"></div>
                                </div>
                            </div>
                        </td>
                        <td class="fixed-width">
                            <div class="df-select-frame">
                                <div class="df-select-box" cmp-selector>
                                    <input readonly="readonly" style="width: 120px;"
                                           value="{{'cmp.healing.flow.self.define' | i18n}}"
                                           ng-if="requestParam.source === 'DEFINITION'"/>
                                    <input readonly="readonly" style="width: 120px;"
                                           value="{{'cmp.healing.flow.alarm.information' | i18n}}"
                                           ng-if="requestParam.source === 'ALARM'"/>
                                    <input readonly="readonly" style="width: 120px;"
                                           value="{{'cmp.healing.flow.select.from.flow.params' | i18n}}"
                                           ng-if="requestParam.source === 'GLOBAL'"/>
                                    <input readonly="readonly" style="width: 120px;"
                                           value="{{'cmp.healing.flow.select.from.flow.api' | i18n}}"
                                           ng-if="requestParam.source === 'API'"/>
                                    <div class="df-select-bar"></div>
                                </div>
                                <ul class="df-select-menu">
                                    <li
                                            ng-click="requestParam.source='DEFINITION';requestParam.value=requestParam.defaultValue;requestParam.paramType='STRINGTYPE'">
                                        {{'cmp.healing.flow.self.define' | i18n}}
                                    </li>
                                    <li ng-click="requestParam.source='ALARM';requestParam.value='objectInstance';requestParam.paramType = null">
                                        {{'cmp.healing.flow.alarm.information' | i18n}}
                                    </li>
                                    <li ng-click="requestParam.source='GLOBAL';requestParam.value=null;requestParam.paramType = null">
                                        {{'cmp.healing.flow.select.from.flow.params' | i18n}}
                                    </li>
                                    <li ng-click="requestParam.source='API';requestParam.value=null;requestParam.paramType = null">
                                        {{'cmp.healing.flow.select.from.flow.api' | i18n}}
                                    </li>
                                </ul>
                            </div>
                        </td>
                        <td class="fixed-width" ng-if="requestParam.source == 'DEFINITION'">
                            <div class="df-select-frame" ng-if="requestParam.paramType=='STRINGTYPE'">
                                <div class="df-select-box" cmp-selector>
                                    <input readonly="readonly" style="width: 215px;"
                                           value="{{'cmp.healing.flow.para.string.type' | i18n}}"
                                           ng-if="requestParam.paramType === 'STRINGTYPE'"/>
                                    <input readonly="readonly" style="width: 215px;"
                                           value="{{'cmp.healing.flow.para.date.type' | i18n}}"
                                           ng-if="requestParam.paramType === 'DATETYPE'"/>
                                    <div class="df-select-bar"></div>
                                </div>
                                <ul class="df-select-menu">
                                    <li ng-click="requestParam.paramType='STRINGTYPE';">
                                        {{'cmp.healing.flow.para.string.type' | i18n}}
                                    </li>
                                    <li ng-click="requestParam.paramType='DATETYPE';requestParam.value = null;">
                                        {{'cmp.healing.flow.para.date.type' | i18n}}
                                    </li>
                                    <li ng-click="requestParam.paramType='IP_TREE';requestParam.value = null;">
                                        {{'cmp.healing.flow.para.string.iptree' | i18n}}
                                    </li>

                                </ul>
                            </div>

                            <div class="df-select-frame" ng-if="requestParam.paramType=='DATETYPE'">
                                <div class="df-select-box" cmp-selector>
                                    <input readonly="readonly" style="width: 215px;"
                                           value="{{'cmp.healing.flow.para.string.type' | i18n}}"
                                           ng-if="requestParam.paramType === 'STRINGTYPE'"/>
                                    <input readonly="readonly" style="width: 215px;"
                                           value="{{'cmp.healing.flow.para.date.type' | i18n}}"
                                           ng-if="requestParam.paramType === 'DATETYPE'"/>
                                    <input readonly="readonly" style="width: 215px;"
                                           value="{{'cmp.healing.flow.para.string.iptree' | i18n}}"
                                           ng-if="requestParam.paramType === 'IP_TREE'"/>
                                    <div class="df-select-bar"></div>
                                </div>
                                <ul class="df-select-menu">
                                    <li ng-click="requestParam.paramType='STRINGTYPE';requestParam.value = null;">
                                        {{'cmp.healing.flow.para.string.type' | i18n}}
                                    </li>
                                    <li ng-click="requestParam.paramType='DATETYPE';">
                                        {{'cmp.healing.flow.para.date.type' | i18n}}
                                    </li>
                                    <li ng-click="requestParam.paramType='IP_TREE';requestParam.value = null;">
                                        {{'cmp.healing.flow.para.string.iptree' | i18n}}
                                    </li>
                                </ul>
                            </div>

                            <div class="df-select-frame" ng-if="requestParam.paramType=='IP_TREE'">
                                <div class="df-select-box" cmp-selector>
                                    <input readonly="readonly" style="width: 215px;"
                                           value="{{'cmp.healing.flow.para.string.type' | i18n}}"
                                           ng-if="requestParam.paramType === 'STRINGTYPE'"/>
                                    <input readonly="readonly" style="width: 215px;"
                                           value="{{'cmp.healing.flow.para.date.type' | i18n}}"
                                           ng-if="requestParam.paramType === 'DATETYPE'"/>
                                    <input readonly="readonly" style="width: 215px;"
                                           value="{{'cmp.healing.flow.para.string.iptree' | i18n}}"
                                           ng-if="requestParam.paramType === 'IP_TREE'"/>
                                    <div class="df-select-bar"></div>
                                </div>
                                <ul class="df-select-menu">
                                    <li ng-click="requestParam.paramType='STRINGTYPE';requestParam.value = null;">
                                        {{'cmp.healing.flow.para.string.type' | i18n}}
                                    </li>
                                    <li ng-click="requestParam.paramType='DATETYPE';">
                                        {{'cmp.healing.flow.para.date.type' | i18n}}
                                    </li>
                                    <li ng-click="requestParam.paramType='IP_TREE';requestParam.value = null;">
                                        {{'cmp.healing.flow.para.string.iptree' | i18n}}
                                    </li>
                                </ul>
                            </div>

                        </td>
                        <td style="width:255px"
                            ng-if="requestParam.source === 'DEFINITION' || requestParam.source === 'ALARM'">
                            <div class="df-input-frame"
                                 ng-if="requestParam.source == 'DEFINITION' && requestParam.paramType == 'STRINGTYPE'">
                                <input
                                        ng-if="requestParam.type !== 'java.lang.Integer' && atom.createMode === 'BUILD_IN' && (requestParam.required === true || requestParam.required === 'true' || requestParam.required === null)"
                                        class="df-input requestParamsValid" ng-model="requestParam.value"
                                        style="width: 225px;"
                                        id="requestParamValue_0{{$index}}"
                                        ng-change="dataTypeValidCheck(['required','nameLengthValidator'],'requestParamValue_0'+$index,requestParam.value,null,2048)"
                                        ng-click="dataTypeValidCheck(['required','nameLengthValidator'],'requestParamValue_0'+$index,requestParam.value,null,2048)"
                                        ng-blur="closeShowTip()"/>
                                <input
                                        ng-if="requestParam.type !== 'java.lang.Integer' && atom.createMode !== 'BUILD_IN' && (requestParam.required === true || requestParam.required === 'true' || requestParam.required === null)"
                                        class="df-input requestParamsValid" ng-model="requestParam.value"
                                        style="width: 225px;"
                                        id="requestParamValue_1{{$index}}"
                                        ng-change="dataTypeValidCheck(['required','validFlowPara','nameLengthValidator'],'requestParamValue_1'+$index,requestParam.value,null,2048)"
                                        ng-click="dataTypeValidCheck(['required','validFlowPara','nameLengthValidator'],'requestParamValue_1'+$index,requestParam.value,null,2048)"
                                        ng-blur="closeShowTip()"/>
                                <input
                                        ng-if="requestParam.type !== 'java.lang.Integer' && atom.createMode === 'BUILD_IN' && (requestParam.required === false || requestParam.required === 'false')"
                                        class="df-input requestParamsValid" ng-model="requestParam.value"
                                        style="width: 225px;"
                                        id="requestParamValue_2{{$index}}"
                                        ng-change="dataTypeValidCheck(['nameLengthValidator'],'requestParamValue_2'+$index,requestParam.value,null,2048)"
                                        ng-click="dataTypeValidCheck(['nameLengthValidator'],'requestParamValue_2'+$index,requestParam.value,null,2048)"
                                        ng-blur="closeShowTip()"/>
                                <input
                                        ng-if="requestParam.type !== 'java.lang.Integer' && atom.createMode !== 'BUILD_IN' && (requestParam.required === false || requestParam.required === 'false')"
                                        class="df-input requestParamsValid" ng-model="requestParam.value"
                                        style="width: 225px;"
                                        id="requestParamValue_3{{$index}}"
                                        ng-change="dataTypeValidCheck(['nameLengthValidator'],'requestParamValue_3'+$index,requestParam.value,null,2048)"
                                        ng-click="dataTypeValidCheck(['nameLengthValidator'],'requestParamValue_3'+$index,requestParam.value,null,2048)"
                                        ng-blur="closeShowTip()"/>
                                <input
                                        ng-if="requestParam.type === 'java.lang.Integer' && atom.createMode === 'BUILD_IN' && (requestParam.required === true || requestParam.required === 'true' || requestParam.required === null)"
                                        class="df-input requestParamsValid" ng-model="requestParam.value"
                                        style="width: 225px;"
                                        id="requestParamValue_4{{$index}}"
                                        ng-change="dataTypeValidCheck(['required','range'],'requestParamValue_4'+$index,requestParam.value,-2147483648,2147483647)"
                                        ng-click="dataTypeValidCheck(['required','range'],'requestParamValue_4'+$index,requestParam.value,-2147483648,2147483647)"
                                        ng-blur="closeShowTip()"/>
                                <input
                                        ng-if="requestParam.type === 'java.lang.Integer' && (requestParam.required === false || requestParam.required === 'false')"
                                        class="df-input requestParamsValid" ng-model="requestParam.value"
                                        style="width: 225px;"
                                        id="requestParamValue_5{{$index}}"
                                        ng-change="dataTypeValidCheck(['range'],'requestParamValue_5'+$index,requestParam.value,-2147483648,2147483647)"
                                        ng-click="dataTypeValidCheck(['range'],'requestParamValue_5'+$index,requestParam.value,-2147483648,2147483647)"
                                        ng-blur="closeShowTip()"/>
                            </div>
                            <div class="df-input-frame"
                                 ng-if="requestParam.source == 'DEFINITION' && requestParam.paramType == 'IP_TREE'">
                                <input class="df-input requestParamsValid" readonly ng-model="requestParam.value"
                                       style="width: 225px;"
                                       id="requestParamValue_6{{$index}}"
                                       ng-change="dataTypeValidCheck(['nameLengthValidator'],'requestParamValue_6'+$index,requestParam.value,null,2048)"
                                       ng-click="popSelectIps($index,'atom');dataTypeValidCheck(['nameLengthValidator'],'requestParamValue_6'+$index,requestParam.value,null,2048)"
                                       ng-blur="closeShowTip()">
                                </input>
                            </div>
                            <div ng-if="requestParam.source == 'DEFINITION' && requestParam.paramType == 'DATETYPE'"
                                 id="dateTimePars">
                                <eview:date-time-picker id="dateTimeParamId_{{$index}}" class="xdatetime"
                                                        type="datetime"
                                                        ng-if="requestParam.required === true || requestParam.required === 'true' || requestParam.required === null"
                                                        validator="customValidator" readonly='true'
                                                        ng-click="setRequestParamValue(requestParam,$index)"
                                                        change="timeChange">
                                </eview:date-time-picker>
                                <eview:date-time-picker id="dateTimeParam_{{$index}}" class="xdatetime"
                                                        ng-if="requestParam.required === false || requestParam.required === 'false'"
                                                        ng-click="setRequestParamValue(requestParam,$index)"
                                                        readonly='true'
                                                        type="datetime" validator="customValidator" change="timeChange">
                                </eview:date-time-picker>
                            </div>
                            <div class="df-select-frame" ng-if="requestParam.source == 'ALARM'"
                                 style="margin-right: 10px;">
                                <div class="df-select-box" cmp-selector>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.location.information' | i18n}}"
                                           ng-if="requestParam.value === 'objectInstance'"/>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.extra.information' | i18n}}"
                                           ng-if="requestParam.value === 'additionalInformation'"/>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.alarm.source' | i18n}}"
                                           ng-if="requestParam.value === 'moDN'"/>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.alarm.start.time' | i18n}}"
                                           ng-if="requestParam.value === 'eventTime'"/>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.location.information' | i18n}}"
                                           ng-if="requestParam.value !== 'objectInstance' && requestParam.value !== 'additionalInformation' && requestParam.value !== 'moDN' && requestParam.value !== 'eventTime'"/>
                                    <div class="df-select-bar"></div>
                                </div>
                                <ul class="df-select-menu">
                                    <li ng-click="requestParam.value='objectInstance'">
                                        {{'cmp.healing.flow.location.information' | i18n}}
                                    </li>
                                    <li ng-click="requestParam.value='additionalInformation'">
                                        {{'cmp.healing.flow.extra.information' | i18n}}
                                    </li>
                                    <li ng-click="requestParam.value='moDN'">
                                        {{'cmp.healing.flow.alarm.source' | i18n}}
                                    </li>
                                    <li ng-click="requestParam.value='eventTime'">
                                        {{'cmp.healing.flow.alarm.start.time' | i18n}}
                                    </li>
                                </ul>
                            </div>
                            <div class="df-select-frame"
                                 ng-if="requestParam.source == 'ALARM' && (requestParam.value == 'additionalInformation' || requestParam.value == 'objectInstance')">
                                <div class="df-select-box" cmp-selector>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.regular.expression' | i18n}}"
                                           ng-if="requestParam.ruleType === 'REGEXP'"/>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.match' | i18n}}"
                                           ng-if="requestParam.ruleType === 'STARTENDKEY'"/>
                                    <input readonly="readonly" style="width: 80px;"
                                           value="{{'cmp.healing.flow.match' | i18n}}"
                                           ng-if="requestParam.ruleType !== 'REGEXP' && requestParam.ruleType !== 'STARTENDKEY'"/>
                                    <div class="df-select-bar"></div>
                                </div>
                                <ul class="df-select-menu">
                                    <li ng-click="requestParam.ruleType='STARTENDKEY'">
                                        {{'cmp.healing.flow.match' | i18n}}
                                    </li>
                                    <li ng-click="requestParam.ruleType='REGEXP'">
                                        {{'cmp.healing.flow.regular.expression' | i18n}}
                                    </li>
                                </ul>

                            </div>

                        </td>

                        <td ng-if="requestParam.source === 'API'" colspan="2">
                            <div class="df-btn" ng-click="page.selectApi=angular.copy(requestParam.api);
                                    optApi.showApi = true;optApi.requestParam = requestParam; pageSelectApi(requestParam)">
                                {{'cmp.healing.flow.select.api' | i18n}}
                            </div>
                            <div ng-if="requestParam.api" class="view-input"
                                 style="float: left;line-height: 26px;margin-left: 20px"
                                 ng-click="page.selectApi=angular.copy(requestParam.api);optApi.showApi =true;
                                    optApi.requestParam = requestParam; pageSelectApi(requestParam)">
                                {{'cmp.healing.api.select.info' | i18n}}
                                <a style="cursor: pointer;" ng-bind="requestParam.api.name">
                                </a>
                            </div>
                        </td>

                        <td
                                ng-if="requestParam.ruleType == 'STARTENDKEY' && requestParam.source == 'ALARM'
                                && (requestParam.value == 'additionalInformation' || requestParam.value == 'objectInstance')">
                            <div class="df-input-frame">
                                <input class="df-input" ng-model="requestParam.startRule" style="width: 225px;"
                                       id="requestParamStartRule" value="{{requestParam.startRule}}"
                                       ng-change="dataTypeValidCheck(['nameLengthValidator'],'requestParamStartRule',requestParam.startRule,null,128)"
                                       ng-click="dataTypeValidCheck(['nameLengthValidator'],'requestParamStartRule',requestParam.startRule,null,128)"
                                       ng-blur="closeShowTip()"
                                       placeholder="{{'cmp.healing.flow.first.key' | i18n}}"/>
                            </div>
                            <div class="df-input-frame">
                                <input class="df-input" ng-model="requestParam.endRule" style="width: 225px;"
                                       id="requestParamEndRule" value="{{requestParam.endRule}}"
                                       ng-change="dataTypeValidCheck(['nameLengthValidator'],'requestParamEndRule',requestParam.endRule,null,128)"
                                       ng-click="dataTypeValidCheck(['nameLengthValidator'],'requestParamEndRule',requestParam.endRule,null,128)"
                                       ng-blur="closeShowTip()" placeholder="{{'cmp.healing.flow.last.key' | i18n}}"/>
                            </div>
                        </td>

                        <td
                                ng-if="requestParam.ruleType == 'REGEXP' && requestParam.source == 'ALARM'
                                && (requestParam.value == 'additionalInformation' || requestParam.value == 'objectInstance')">
                            <div class="df-input-frame">
                                <input class="df-input" ng-model="requestParam.rule" style="width: 225px;"
                                       id="requestParamRule" value="{{requestParam.rule}}"
                                       ng-change="dataTypeValidCheck(['required','nameLengthValidator'],'requestParamRule',requestParam.rule,null,128)"
                                       ng-click="dataTypeValidCheck(['required','nameLengthValidator'],'requestParamRule',requestParam.rule,null,128)"
                                       ng-blur="closeShowTip()"
                                       placeholder="{{'cmp.healing.flow.rule.type' | i18n}}"/>
                            </div>
                        </td>
                        <td>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <!--配置API信息页面-->
        <div class="atom-pop-content" ng-if="optApi.showApi" style="padding-right: 0px;width: 1230px;">
            <div style="float: left;width: 56%;" ng-include="'/dvihealingwebsite/app/healing/api/apiList.html'">
            </div>
            <div style="float: left;width: 42%;margin: 36px 0px 0px 5px;"
                 ng-include="'/dvihealingwebsite/app/healing/api/apiRequest.html'"></div>
        </div>
    </div>
    <!--步骤三：选择主机-->
    <div ng-if="atom.type !== 'sql'" class="atom-pop-content" ng-show="page.selectIndex==2 && !murmur.showIp" id="atom_ip_frame"
         style="width:1100px">
        <div style="padding-left: 9px">
            <table class="atom-param-table">
                <tr>
                    <td style="width:90px">
                        <div style="float:left;padding-right:10px">
                            <span style="color: #fc5043">*</span>
                            {{'cmp.healing.flow.execute.user' | i18n}}
                        </div>
                        <div style="float:left" ng-init="isShowUserTips = false" ng-mouseover="isShowUserTips = true"
                             ng-mouseout="isShowUserTips = false">
                            <div class="df-label-bar">
                            </div>
                            <div ng-cloak class="df-tips" style="position:absolute;left:100px;top:10px;z-index:100;"
                                 ng-show="isShowUserTips" clearfix>
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.user' | i18n"></p>
                                <div class="df-tips-arrow"></div>
                            </div>
                        </div>

                    </td>
                    <td style="width:90px">
                        <div class="df-select-frame">
                            <div class="df-select-box" cmp-selector>
                                <input readonly="readonly" style="width: 120px;"
                                       value="{{'cmp.healing.flow.self.define' | i18n}}"
                                       ng-if="murmur.userType === 'DEFINITION'"/>
                                <input readonly="readonly" style="width: 120px;"
                                       value="{{'cmp.healing.flow.select.from.flow.params' | i18n}}"
                                       ng-if="murmur.userType === 'GLOBAL'"/>
                                <div class="df-select-bar"></div>
                            </div>
                            <ul class="df-select-menu">
                                <li ng-click="murmur.userType='DEFINITION'">{{'cmp.healing.flow.self.define' | i18n}}
                                </li>
                                <li ng-click="murmur.userType='GLOBAL'">
                                    {{'cmp.healing.flow.select.from.flow.params' | i18n}}
                                </li>
                            </ul>
                        </div>
                    </td>
                    <td>
                        <div class="df-input-frame" ng-if="murmur.userType == 'DEFINITION'">
                            <input class="df-input" ng-model="murmur.user" id="murmurUser" autocomplete="off"
                                   ng-if="atom.source !== 'hwWithSign'"
                                   ng-change="dataTypeValidCheck(['required','cmpValidChar','nameLengthValidator','formatBlockList'],'murmurUser',murmur.user,null,16)"
                                   ng-click="dataTypeValidCheck(['required','cmpValidChar','nameLengthValidator','formatBlockList'],'murmurUser',murmur.user,null,16)"
                                   ng-blur="closeShowTip()"/>
                            <input class="df-input" ng-model="murmur.user" id="murmurUser" autocomplete="off"
                                   ng-if="atom.source === 'hwWithSign'"
                                   ng-change="dataTypeValidCheck(['required','cmpValidChar','nameLengthValidator','barredRoot'],'murmurUser',murmur.user,null,16)"
                                   ng-click="dataTypeValidCheck(['required','cmpValidChar','nameLengthValidator','barredRoot'],'murmurUser',murmur.user,null,16)"
                                   ng-blur="closeShowTip()"/>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <div class="main-title-atom" style="width:100%;height:40px;">
            <div class="radio" style="float:left; padding-top:16px; padding-right:20px;padding-left:9px">
                <h3>{{'cmp.healing.flow.host' | i18n}}</h3>
                <ul>
                    <li>
                        <div ng-init="isShowHostTips = false" ng-mouseover="isShowHostTips = true"
                             ng-mouseout="isShowHostTips = false">
                            <div class="df-label-bar"></div>
                            <div ng-cloak class="df-tips"
                                 style="position:absolute;left:100px;top:44px;z-index:100;width:516px"
                                 ng-show="isShowHostTips">
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.host.number' | i18n"></p>
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.host' | i18n"></p>
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.host.1' | i18n"></p>
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.host.2' | i18n"></p>
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.host.3' | i18n"></p>
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.host.4' | i18n"></p>
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.host.5' | i18n"></p>
                                <div class="df-tips-arrow"></div>
                            </div>
                        </div>
                    </li>
                    <li ng-class="{'current':murmur.ips.source=='DEFAULT'}">
                        <div class="radio-bar"
                             ng-click="murmur.ips.source='DEFAULT';murmur.ips.value=null;addMotype();ips.isShowSolutionTree=false;">
                        </div>
                        <p>{{'cmp.healing.flow.host.default' | i18n}}</p>
                    </li>
                    <li ng-class="{'current':murmur.ips.source=='ALARM'}">
                        <div class="radio-bar"
                             ng-click="murmur.ips.source='ALARM';murmur.ips.value='objectInstance'"></div>
                        <p>{{'cmp.healing.flow.from.alarm' | i18n}}</p>
                    </li>
                    <li ng-class="{'current':murmur.ips.source=='GLOBAL'}">
                        <div class="radio-bar" ng-click="murmur.ips.source='GLOBAL';murmur.ips.value=null"></div>
                        <p>{{'cmp.healing.flow.from.flowParameter' | i18n}}</p>
                    </li>
                    <li ng-class="{'current':murmur.ips.source=='SELECT_PRIORITY_ONLINE_HOST'}">
                        <div class="radio-bar" ng-click="murmur.ips.source='SELECT_PRIORITY_ONLINE_HOST';"></div>
                        <p>{{'cmp.healing.flow.priority.online' | i18n}}</p>
                    </li>
                    <li ng-class="{'current':murmur.ips.source=='NEWDEFINITION'}">
                        <div class="radio-bar" ng-click="murmur.ips.source='NEWDEFINITION';"></div>
                        <p>{{'cmp.healing.flow.self.define' | i18n}}</p>
                    </li>
                </ul>
            </div>
        </div>

        <div>
            <div class="ihealing-operate clearfix"
                 style="padding-top: 0px; float: left; padding-left: 8px; padding-top: 5px;width:100%">
                <p class="refresh-label" ng-bind="'cmp.healing.ip.offline.filter' | i18n"></p>
                <div class="df-switch" ng-class="{'on':isFilterOfflineHost == 'Yes'}"
                     ng-click="changeFilterOfflineHost()">
                    <p ng-show="isFilterOfflineHost == 'Yes'">YES</p>
                    <p ng-show="isFilterOfflineHost != 'Yes'">NO</p>
                </div>
            </div>
        </div>

        <div>
            <div class="ihealing-operate clearfix"
                 style="padding-top: 0px; float: left; padding-left: 8px; padding-top: 5px;width:100%">
                <p class="refresh-label" ng-bind="'cmp.healing.atom.details.filter' | i18n"></p>
                <div class="df-switch" ng-class="{'on':isAtomHidden == 'Yes'}"
                     ng-click="changeFilterOffAtomDetails()">
                    <p ng-show="isAtomHidden == 'Yes'">YES</p>
                    <p ng-show="isAtomHidden != 'Yes'">NO</p>
                </div>
            </div>
        </div>

        <div class="clearfix"></div>
        <!--应用类型-->
        <div ng-if="murmur.ips.source=='DEFAULT'">

            <div class="radio" style="float:left; padding-top:16px; padding-right:20px;padding-left:9px">
                <h3>{{'cmp.healing.flow.host.selection.mode' | i18n}}</h3>
                <ul>
                    <li ng-class="{'current':hostSelectionMode!='OnlyOneHost'}">
                        <div class="radio-bar"
                             ng-click="hostSelectionMode='AllHosts';changeHostSelectionMode('AllHosts')">
                        </div>
                        <p>{{'cmp.healing.flow.select.all.hosts' | i18n}}</p>
                    </li>
                    <li ng-class="{'current':hostSelectionMode=='OnlyOneHost'}">
                        <div class="radio-bar"
                             ng-click="hostSelectionMode='OnlyOneHost';changeHostSelectionMode('OnlyOneHost')">
                        </div>
                        <p>{{'cmp.healing.flow.select.only.one.host' | i18n}}</p>
                    </li>

                </ul>
            </div>

            <table class="atom-param-table">
                <tr>
                    <td style="width:90px;padding-left: 10px">
                        <div style="float:left">{{'cmp.healing.flow.select.atom.motype' | i18n}}</div>
                        <div class="df-label-tips title-tips" ng-init="isShowSolutionTips = false"
                             ng-mouseover="isShowSolutionTips = true" ng-mouseout="isShowSolutionTips = false"
                             style="float:left;margin-right:13px;">
                            <div class="df-label-bar"></div>
                            <div ng-cloak class="df-tips left" ng-show="isShowSolutionTips">
                                <p class="spcial-warp"
                                   ng-bind="'cmp.healing.flow.flowParameter.solution.tip' | i18n"></p>
                                <div class="df-tips-arrow"></div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="df-input-frame">
                            <input class="df-input" ng-model="atom.moType" style="width: 225px;"
                                   value="{{atom.moType}}" id="atommoType"
                                   ng-change="dataTypeValidCheck(['cmpValidChar','nameLengthValidator','validContainsChinese'],'atommoType',atom.moType,null,255)"
                                   ng-click="dataTypeValidCheck(['cmpValidChar','nameLengthValidator','validContainsChinese'],'atommoType',atom.moType,null,255)"
                                   ng-blur="closeShowTip()"
                                   placeholder="{{'cmp.healing.flow.select.atom.motype' | i18n}}"/>
                            <div class="df-icon-search" ng-click="showSolutionWin()"></div>
                        </div>
                    </td>
                    <td style="width:120px;line-height:16px;">
                        <div style="float:left"> {{'cmp_healing_flow_flowParameter' | i18n}} :</div>
                        <div id="flowParameterTips" class="df-label-tips title-tips" ng-init="isKeyValueTips = false"
                             ng-mouseover="isKeyValueTips = true"
                             ng-mouseout="isKeyValueTips = false" style="float:left;margin-right:13px;">
                            <div class="df-label-bar"></div>
                            <div ng-cloak class="df-tips left" ng-show="isKeyValueTips">
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.flowParameter.tip' | i18n"></p>
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.flowParameter.tip.first' | i18n"></p>
                                <p class="spcial-warp" ng-bind="'cmp_healing.flow.flowParameter.tip.second' | i18n"></p>
                                <div class="df-tips-arrow"></div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="df-input-frame">
                            <input class="df-input" ng-model="ips.defaultIpsValue" style="width: 225px;"
                                   value="{{ips.defaultIpsValue}}" id="atomFlow"
                                   ng-change="dataTypeValidCheck(['alpha','nameLengthValidator'],'atomFlow',ips.defaultIpsValue,null,128)"
                                   ng-click="dataTypeValidCheck(['alpha','nameLengthValidator'],'atomFlow',ips.defaultIpsValue,null,128)"
                                   ng-blur="closeShowTip()"/>
                        </div>
                    </td>
                </tr>
            </table>
            <div class="left-column" style="height: 370px;overflow-y: scroll;width: 1210px !important;"
                 ng-show="ips.isShowSolutionTree">
                <table class="atom-param-table">
                    <tr>
                        <td style="width:90px;padding-left:10px">{{'cmp.healing.flow.search.solution' | i18n}}</td>
                        <td>
                            <div class="df-select-frame">
                                <div class="df-select-box" cmp-selector>
                                    <input type="text" readonly="readonly" ng-model="atom_script_top_solution_type"
                                           title="{{atom_script_top_solution_type}}"/>
                                    <div class="df-select-bar"></div>
                                </div>
                                <ul class="df-select-menu" style="display: none;">
                                    <li ng-repeat="solution in solutionList" title="{{solution.name}}"
                                        ng-click="onMotypeTopMoTypeChange(solution)">{{solution.name}}
                                    </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                </table>
                <eview:tree id="moTypeTree" selection-type="single" hideroot="true" listeners='atomMoTypeTreeListeners'>
                    <eview:tree-node id="moTypeTreeNode" data="moTypeTreeNodeData"></eview:tree-node>
                </eview:tree>
            </div>
        </div>
        <!--从告警信息中解析-->
        <div ng-if="murmur.ips.source=='ALARM'" style="padding-left:9px">
            <table class="atom-param-table">
                <tr>
                    <td style="width:90px;">{{'cmp.healing.flow.get.word' | i18n}}</td>
                    <td>
                        <div class="df-select-frame">
                            <div class="df-select-box" cmp-selector>
                                <input readonly="readonly" style="width: 160px;"
                                       value="{{'cmp.healing.flow.extra.information' | i18n}}"
                                       ng-if="ips.alarmIpsValue === 'additionalInformation'"/>
                                <input readonly="readonly" style="width: 160px;"
                                       value="{{'cmp.healing.flow.location.information' | i18n}}"
                                       ng-if="ips.alarmIpsValue === 'objectInstance'"/>
                                <input readonly="readonly" style="width: 160px;"
                                       value="{{'cmp.healing.flow.location.information' | i18n}}"
                                       ng-if="ips.alarmIpsValue !== 'additionalInformation' && ips.alarmIpsValue !== 'objectInstance'"/>
                                <div class="df-select-bar"></div>
                            </div>
                            <ul class="df-select-menu">
                                <li ng-click="ips.alarmIpsValue='additionalInformation'">
                                    {{'cmp.healing.flow.extra.information' | i18n}}
                                </li>
                                <li ng-click="ips.alarmIpsValue='objectInstance'">
                                    {{'cmp.healing.flow.location.information' | i18n}}
                                </li>
                            </ul>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td style="width:90px">
                        <div style="float:left">{{'cmp.healing.flow.get.method' | i18n}}</div>
                        <div class="df-label-tips" style="float:left"
                             ng-init="isShowRuleTips = false" ng-mouseover="isShowRuleTips = true"
                             ng-mouseout="isShowRuleTips = false">
                            <div class="df-label-bar"></div>
                            <div ng-cloak class="df-tips left" ng-show="isShowRuleTips">
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.ruletype' | i18n"></p>
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.ruletype.1' | i18n">
                                </p>
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.ruletype.2' | i18n">
                                </p>
                                <div class="df-tips-arrow"></div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="df-select-frame">
                            <div class="df-select-box" cmp-selector>
                                <input readonly="readonly" style="width: 160px;"
                                       value="{{'cmp.healing.flow.match' | i18n}}"
                                       ng-if="murmur.ips.ruleType === 'STARTENDKEY'"/>
                                <input readonly="readonly" style="width: 160px;"
                                       value="{{'cmp.healing.flow.regular.expression' | i18n}}"
                                       ng-if="murmur.ips.ruleType === 'REGEXP'"/>
                                <input readonly="readonly" style="width: 160px;"
                                       value="{{'cmp.healing.flow.match' | i18n}}"
                                       ng-if="murmur.ips.ruleType !== 'STARTENDKEY' && murmur.ips.ruleType !== 'REGEXP'"/>
                                <div class="df-select-bar"></div>
                            </div>
                            <ul class="df-select-menu">
                                <li ng-click="murmur.ips.ruleType='STARTENDKEY'">{{'cmp.healing.flow.match' | i18n}}
                                </li>
                                <li ng-click="murmur.ips.ruleType='REGEXP'">
                                    {{'cmp.healing.flow.regular.expression' | i18n}}
                                </li>

                            </ul>
                        </div>
                    </td>

                </tr>
                <tr>
                    <td ng-if="murmur.ips.ruleType == 'STARTENDKEY'" style="width:90px;">
                        {{'cmp.healing.flow.start.end.key' | i18n}}
                    </td>
                    <td ng-if="murmur.ips.ruleType == 'REGEXP'" style="width:90px;"><span
                            style="color:#fc5043;">*</span>{{'cmp.healing.flow.regular.rule' | i18n}}
                    </td>
                    <td ng-if="murmur.ips.ruleType == 'STARTENDKEY'">
                        <div class="df-input-frame">
                            <input class="df-input" ng-model="murmur.ips.startRule" style="width: 225px;"
                                   value="{{murmur.ips.startRule}}" id="ipsstartRule"
                                   ng-change="dataTypeValidCheck(['nameLengthValidator'],'ipsstartRule',murmur.ips.startRule,null,128)"
                                   ng-click="dataTypeValidCheck(['nameLengthValidator'],'ipsstartRule',murmur.ips.startRule,null,128)"
                                   ng-blur="closeShowTip()" placeholder="{{'cmp.healing.flow.first.key' | i18n}}"/>
                        </div>
                        <div class="df-input-frame">
                            <input class="df-input" ng-model="murmur.ips.endRule" style="width: 225px;"
                                   value="{{murmur.ips.endRule}}" id="ipsendRule"
                                   ng-change="dataTypeValidCheck(['nameLengthValidator'],'ipsendRule',murmur.ips.endRule,null,128)"
                                   ng-click="dataTypeValidCheck(['nameLengthValidator'],'ipsendRule',murmur.ips.endRule,null,128)"
                                   ng-blur="closeShowTip()" placeholder="{{'cmp.healing.flow.last.key' | i18n}}"/>
                        </div>
                    </td>

                    <td ng-if="murmur.ips.ruleType == 'REGEXP'">
                        <div class="df-input-frame">
                            <input class="df-input" ng-model="murmur.ips.rule" style="width: 225px;"
                                   value="{{murmur.ips.rule}}" id="ipsrule"
                                   ng-change="dataTypeValidCheck(['required','nameLengthValidator'],'ipsrule',murmur.ips.rule,null,128)"
                                   ng-click="dataTypeValidCheck(['required','nameLengthValidator'],'ipsrule',murmur.ips.rule,null,128)"
                                   ng-blur="closeShowTip()" placeholder="{{'cmp.healing.flow.rule.type' | i18n}}"/>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <!--从流程参数获取-->
        <div ng-if="murmur.ips.source=='GLOBAL'" style="padding-left:9px">
            <table class="atom-param-table">
                <tr>
                    <td style="width:120px;line-height: 16px;">
                        <div style="float:left"> {{'cmp_healing_flow_flowParameter' | i18n}} :</div>
                        <div class="df-label-tips title-tips" ng-init="isKeyValueTips = false"
                             ng-mouseover="isKeyValueTips = true"
                             ng-mouseout="isKeyValueTips = false" style="float:left;margin-right:13px;">
                            <div class="df-label-bar"></div>
                            <div ng-cloak class="df-tips left" ng-show="isKeyValueTips">
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.flowParameter.tip' | i18n"></p>
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.flowParameter.tip.1' | i18n"></p>
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.flowParameter.tip.2' | i18n"></p>
                                <div class="df-tips-arrow"></div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="df-input-frame">
                            <input class="df-input" ng-model="ips.globalIpsValue" style="width: 225px;"
                                   value="{{ips.globalIpsValue}}" id="atomFlow"
                                   ng-change="dataTypeValidCheck(['alpha','nameLengthValidator'],'atomFlow',ips.globalIpsValue,null,128)"
                                   ng-click="dataTypeValidCheck(['alpha','nameLengthValidator'],'atomFlow',ips.globalIpsValue,null,128)"
                                   ng-blur="closeShowTip()"/>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <!--系统自动选择网元主机-->
        <div ng-if="murmur.ips.source=='SELECT_PRIORITY_ONLINE_HOST'" style="padding-left:9px">
            <table class="atom-param-table">
                <tr>
                    <td style="width:120px;line-height: 16px;">
                        <div style="float:left"> {{'cmp_healing_flow_flowParameter' | i18n}} :</div>
                        <div class="df-label-tips title-tips" ng-init="isKeyValueTips = false"
                             ng-mouseover="isKeyValueTips = true"
                             ng-mouseout="isKeyValueTips = false" style="float:left;margin-right:13px;">
                            <div class="df-label-bar"></div>
                            <div ng-cloak class="df-tips left" ng-show="isKeyValueTips">
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.flowParameter.tip' | i18n"></p>
                                <p class="spcial-warp"
                                   ng-bind="'cmp.healing.flow.flowParameter.one.tip.first' | i18n"></p>
                                <p class="spcial-warp"
                                   ng-bind="'cmp.healing.flow.flowParameter.one.tip.second' | i18n"></p>
                                <div class="df-tips-arrow"></div>
                            </div>
                        </div>
                    </td>
                    <td>
                        <div class="df-input-frame">
                            <input class="df-input" ng-model="ips.paramKey" style="width: 225px;"
                                   value="{{ips.paramKey}}" id="flowParamKey"
                                   ng-change="dataTypeValidCheck(['alpha','nameLengthValidator'],'flowParamKey',ips.paramKey,null,128)"
                                   ng-click="dataTypeValidCheck(['alpha','nameLengthValidator'],'flowParamKey',ips.paramKey,null,128)"
                                   ng-blur="closeShowTip()"/>
                        </div>
                    </td>
                </tr>
            </table>
        </div>
        <!--自定义-->
        <div ng-if="murmur.ips.source=='NEWDEFINITION'">
            <div class="df-btn-group-left" style="margin-top:10px;">
                <div class="df-btn" ng-click="addHost();beforeSelectHost($Model)"
                     ng-class="{disabled : !canSelectHost}">
                    <span class="icon-add"></span>{{'cmp.healing.flow.host.chose' | i18n}}
                </div>
            </div>
            <div class="clearfix"></div>
            <!--选择的主机信息-->
            <div class="df-tab" style="padding-top: 10px;">
                <div class="df-tab-header" style="margin-bottom: 20px;">
                    <ul ng-init="murmur.ips.serverListTabStatus == null">
                        <li ng-class="{'current':murmur.ips.serverListTabStatus == null}"
                            ng-click="murmur.ips.serverListTabStatus = null;"
                            ng-bind="'cmp.workbench.fast.script.ip.list'| i18n"></li>
                        <li ng-class="{'current':murmur.ips.serverListTabStatus == 1}"
                            ng-click="murmur.ips.serverListTabStatus = 1;"
                            ng-bind="'cmp.workbench.fast.script.solution.list'| i18n"></li>
                        <li ng-class="{'current':murmur.ips.serverListTabStatus == 2}"
                            ng-click="murmur.ips.serverListTabStatus = 2;"
                            ng-bind="'cmp.workbench.fast.script.group.list'| i18n"></li>
                    </ul>

                    <div style="float: right">
                        <div class="df-btn df-btnicon" ng-class="{disabled : !isContainUnavailableServer()}"
                             ng-click="popConfirmUnavailableServer()">
                            <span class="icon-clear-all btn-span-icon"></span>
                            {{'cmp.healing.select.server.delete' | i18n}}
                        </div>
                    </div>
                </div>
                <div class="df-tab-item" ng-class="{'current':murmur.ips.serverListTabStatus == null}">

                    <table class="tableCommon">
                        <thead>
                        <tr>
                            <th width="20px">
                                <div class="tableCommon-header">
                                    <span></span>
                                </div>
                            </th>
                            <th>
                                <div class="tableCommon-header">
                                    <span ng-bind="'cmp.workbench.fast.script.host.name.ip' | i18n"></span>
                                </div>
                            </th>
                            <th>
                                <div class="tableCommon-header">
                                    <span ng-bind="'cmp.workbench.fast.script.host.namethead' | i18n"></span>
                                </div>
                            </th>
                            <th>
                                <div class="tableCommon-header">
                                    <span ng-bind="'cmp.workbench.fast.script.host.status' | i18n"></span>
                                </div>
                            </th>
                            <th>
                                <div class="tableCommon-header">
                                    <span ng-bind="'cmp.workbench.select.host.type' | i18n"></span>
                                </div>
                            </th>

                            <th width="250px">
                                <div class="tableCommon-header">
                                    <span ng-bind="'cmp.workbench.fast.script.host.operate' | i18n"></span>
                                </div>
                            </th>
                        </tr>
                        </thead>
                        <tr ng-repeat="server in murmur.ips.tableParams">
                            <td></td>
                            <td>
                                <span ng-controller="WorkbenchCtrl" title="{{customUniagentIp(server)}}">{{customUniagentIp(server)}}</span>
                            </td>
                            <td><span title="{{server.hostname}}">{{server.hostname}}</span></td>
                            <td>
                                    <span class="servicestatus"
                                          ng-class="{'offline' : server.hostStatus === 2,'unavailable' : server.hostStatus === 3}"
                                          title="{{statusMap[server.hostStatus]}}">{{statusMap[server.hostStatus]}}
                                    </span>
                            </td>
                            <td>
                                <div ng-if="server.hostStatus !== 3">
                                    <div ng-if="server.dockerId"
                                         ng-bind="'cmp.workbench.select.host.type.docker' | i18n"></div>
                                    <div ng-if="!server.dockerId"
                                         ng-bind="'cmp.workbench.select.host.type.vm' | i18n"></div>
                                </div>
                            </td>
                            <td>
                                <div class="icon-p-del" title="{{'cmp.workbench.fast.script.host.delete' | i18n}}"
                                     ng-click="popConfirmDeleteServer(server)">
                                </div>
                            </td>
                        </tr>
                    </table>
                    <div style="overflow: hidden;">
                        <eview:pagination id="tableParamsIp" display="true" display-length="10"
                                          listeners="tableIpListeners" length-options="[10,25,50,100]"
                                          total-records="ipTotal"
                                          cur-page="1" type="full_numbers"></eview:pagination>
                    </div>
                </div>
                <div class="df-tab-item" ng-class="{'current':murmur.ips.serverListTabStatus == 1}">
                    <table class="tableCommon">
                        <thead>
                        <tr>
                            <th width="20px">
                                <div class="tableCommon-header">
                                    <span></span>
                                </div>
                            </th>
                            <th>
                                <div class="tableCommon-header">
                                    <span ng-bind="'cmp.workbench.fast.script.solution.name' | i18n"></span>
                                </div>
                            </th>
                            <th>
                                <div class="tableCommon-header">
                                    <span ng-bind="'cmp.workbench.fast.script.group.path' | i18n"></span>
                                </div>
                            </th>
                            <th>
                                <div class="tableCommon-header">
                                    <span ng-bind="'cmp.healing.select.group.status' | i18n"></span>
                                </div>
                            </th>
                            <th width="250px">
                                <div class="tableCommon-header">
                                    <span ng-bind="'cmp.workbench.fast.script.host.operate' | i18n"></span>
                                </div>
                            </th>
                        </tr>
                        </thead>
                        <tr ng-repeat="group in murmur.ips.tableParamsSolution">
                            <td></td>
                            <td><span title="{{group.groupName}}">{{group.groupName}}</span></td>
                            <td><span title="{{group.groupPath}}">{{group.groupPath}}</span></td>
                            <td>
                                    <span class="servicestatus online" ng-if="group.groupStatus === 0"
                                          title="{{'cmp.healing.select.group.status.exist' | i18n}}"
                                          ng-bind="'cmp.healing.select.group.status.exist' | i18n">
                                    </span>
                                <span class="servicestatus unavailable" ng-if="group.groupStatus === 1"
                                      title="{{'cmp.healing.select.group.status.noexist' | i18n}}"
                                      ng-bind="'cmp.healing.select.group.status.noexist' | i18n">
                                    </span>
                            </td>
                            <td>
                                <div class="icon-p-del" title="{{'cmp.workbench.fast.script.host.delete' | i18n}}"
                                     ng-click="popConfirmDeleteSolution(group)">
                                </div>
                            </td>
                        </tr>
                    </table>
                    <div style="overflow: hidden;">
                        <eview:pagination id="tableParamsSolution" display="true" display-length="10"
                                          listeners="solutionListeners" length-options="[10,25,50,100]"
                                          total-records="solutionTotal" cur-page="1"
                                          type="full_numbers"></eview:pagination>
                    </div>
                </div>
                <div class="df-tab-item" ng-class="{'current':murmur.ips.serverListTabStatus == 2}">
                    <table class="tableCommon">
                        <thead>
                        <tr>
                            <th width="20px">
                                <div class="tableCommon-header">
                                    <span></span>
                                </div>
                            </th>
                            <th>
                                <div class="tableCommon-header">
                                    <span ng-bind="'cmp.workbench.fast.script.group.name' | i18n"></span>
                                </div>
                            </th>
                            <th>
                                <div class="tableCommon-header">
                                    <span ng-bind="'cmp.workbench.fast.script.group.path' | i18n"></span>
                                </div>
                            </th>
                            <th>
                                <div class="tableCommon-header">
                                    <span ng-bind="'cmp.healing.select.group.status' | i18n"></span>
                                </div>
                            </th>
                            <th width="250px">
                                <div class="tableCommon-header">
                                    <span ng-bind="'cmp.workbench.fast.script.host.operate' | i18n"></span>
                                </div>
                            </th>
                        </tr>
                        </thead>
                        <tr ng-repeat="group in murmur.ips.tableParamsGroup">
                            <td></td>
                            <td><span title="{{group.groupName}}">{{group.groupName}}</span></td>
                            <td><span title="{{group.groupPath}}">{{group.groupPath}}</span></td>
                            <td>
                                    <span class="servicestatus online" ng-if="group.groupStatus === 0"
                                          title="{{'cmp.healing.select.group.status.exist' | i18n}}"
                                          ng-bind="'cmp.healing.select.group.status.exist' | i18n">
                                    </span>
                                <span class="servicestatus unavailable" ng-if="group.groupStatus === 1"
                                      title="{{'cmp.healing.select.group.status.noexist' | i18n}}"
                                      ng-bind="'cmp.healing.select.group.status.noexist' | i18n">
                                    </span>
                            </td>
                            <td>
                                <div class="icon-p-del" title="{{'cmp.workbench.fast.script.host.delete' | i18n}}"
                                     ng-click="popConfirmDeleteGroup(group)">
                                </div>
                            </td>
                        </tr>
                    </table>
                    <div style="overflow: hidden;">
                        <eview:pagination id="tableParamsgroup" display="true" display-length="10"
                                          listeners="GroupListeners" length-options="[10,25,50,100]"
                                          total-records="groupTotal"
                                          cur-page="1" type="full_numbers"></eview:pagination>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="clearfix"></div>
    <div class="df-btn-group-center bottom" style="margin-top: 30px;" ng-show="!murmur.showIp && !optApi.showApi">
        <div class="df-btn" ng-show="(page.selectIndex==1 && !nodeId) || page.selectIndex==2"
             ng-click="onPrevious()">
            {{'cmp.healing.flow.last.step' | i18n}}
        </div>
        <div class="df-btn" ng-show="page.selectIndex==0"
             ng-class="{'disabled': !page.atomName && page.atomName!==''}" ng-click="onSelectAtom()">
            {{'cmp.healing.flow.next.step' | i18n}}
        </div>
        <div class="df-btn" ng-show="page.selectIndex==1 && (atom.type == 'shell' || atom.type== 'python' )"
             ng-click="onConfirmParam()">
            {{'cmp.healing.flow.next.step' | i18n}}
        </div>
        <div class="df-btn" ng-show="page.selectIndex==2" ng-click="onConfirmAtomButton()">
            {{'cmp.healing.flow.sure' | i18n}}
        </div>
        <div class="df-btn" ng-show="page.selectIndex==1 && atom.type != 'shell' && atom.type != 'python' "
             ng-click="onConfirmAtomButton()">
            {{'cmp.healing.flow.sure' | i18n}}
        </div>
        <div class="df-btn" ng-click="clearAtomConfig();closeDialog('edit')">
            {{'cmp.healing.flow.cancel' | i18n}}
        </div>
    </div>

    <!--选择主机：自定义页面-->
    <div ng-if="page.selectIndex==2 && murmur.showIp">
        <div ng-controller="WorkbenchCtrl" class="atom-pop-content" id="select-server-region"
             ng-include="'/dvworkbenchwebsite/app/workbench/taskManagement/selectServer.html'" style="width:1200px">
        </div>

        <div class="clearfix"></div>
        <div class="df-btn-group-center bottom" style="margin-top: 30px;"
             ng-show="page.selectIndex==2 && murmur.showIp">
            <div class="df-btn" ng-click="afterSelectHost();murmur.showIp=false">
                {{'cmp.healing.flow.tianjia' | i18n}}
            </div>
            <div class="df-btn" ng-click="cancleSelectServer();murmur.showIp=false">
                {{'cmp.healing.flow.cancel' | i18n}}
            </div>
        </div>
    </div>

    <div class="clearfix"></div>
    <div class="df-btn-group-center bottom" style="margin-top: 30px;"
         ng-show="page.selectIndex==1 && optApi.showApi">
        <div class="df-btn"
             ng-click="optApi.showApi=false;optApi.requestParam.api = angular.copy(page.selectViewApi);setOptApi(page.selectViewApi)">
            {{'cmp.healing.flow.sure' | i18n}}
        </div>
        <div class="df-btn" ng-click="optApi.showApi=false">
            {{'cmp.healing.flow.cancel' | i18n}}
        </div>
    </div>

    <uee:fire bind="#popwin_close" script="clearAtomConfig()"></uee:fire>
</div>
</body>

</html>