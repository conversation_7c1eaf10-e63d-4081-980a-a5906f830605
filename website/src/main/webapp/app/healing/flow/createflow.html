<!DOCTYPE html>
<html xmlns:eview="ignored">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <link href="/dvihealingwebsite/css/healing/dashboard.css" type="text/css" rel="stylesheet">
    <link href="/dvfoundationwebsite/css/tipmessage.css" type="text/css" rel="stylesheet">
    <link href="/dvfoundationwebsite/css/tableCommon.css" type="text/css" rel="stylesheet"/>
    <link href="/dvihealingwebsite/js/controller/healing/plugin/codemirror.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/js/controller/healing/plugin/rubyblue.css" type="text/css" rel="stylesheet">
    <link href="/dvfoundationwebsite/css/reset.css" type="text/css" rel="stylesheet"/>
    <link href="/dvfoundationwebsite/css/common.css" type="text/css" rel="stylesheet"/>
    <link href="/dvfoundationwebsite/css/loading.css" type="text/css" rel="stylesheet"/>
    <link href="/dvihealingwebsite/css/healing/flow.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/healing.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/task.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/atom.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/package.css" type="text/css" rel="stylesheet">

    <link href="/dvihealingwebsite/css/healing/cmp-ihealing-event.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/api.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/workbenchindex.css" type="text/css" rel="stylesheet">

    <link href="/dvihealingwebsite/css/healing/diagram.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/index.css" type="text/css" rel="stylesheet"/>

    <style>
        body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, textarea, p, blockquote, th, td, span, a {
            font-size: 12px;
        }

        .hwt-container {
            height: 100%;
        }

        .eview-input-default .eviewInput_suggestionList {
            top: 23px !important;
        }

        .eview-table-default table.dataTable tr td {
            overflow: visible;
        }

        #flowParamTable_tableContent {
            overflow: visible;
        }

        #flowParamTable_tbody>tr {
            height: 2.5rem;
        }

        .uploadfile-btn {
            background-color: #00aaff;
        }

        .noClick {
            pointer-events: none;
            background-color: #dcdcdc !important;
            color: #999 !important;
        }
    </style>
    <script src="/dvihealingwebsite/js/auth.js" id="licenseScript" name="DVIhealingWebsite.BasicAccess"></script>
    <script src="/febs/v1/assets/prelude-loader"></script>
    <script src="/dvfoundationwebsite/js/common.js"></script>

    <script>
      Prel.ready(function() {
        Prel.autoLoad({
          eView3rdLibs: '*',
          eViewBasic: '*',
        }).then(function() {
          window.$CmpApp = angular.module('cmp', ['eviewWidgets']);
          setHelpId('createflowHelpLib_', 'cmp.ihealing.faultdiagnosis.configflow');
          loadJs('/dvfoundationwebsite/js/cmp.js');
          loadJs('/dvihealingwebsite/js/controller/healing/i18n.js');
          loadJs('/dvihealingwebsite/js/controller/healing/i18n_en_us.js');
          loadJs('/dvihealingwebsite/js/controller/healing/i18n_zh_cn.js');
          loadJs('/dvworkbeanswebsite/js/controller/workbeans/i18n.js');
          loadJs('/dvfoundationwebsite/js/i18n.js');
          loadJs('/dvworkbenchwebsite/js/controller/workbench/i18n.js');
          loadJs('/dvfoundationwebsite/js/dataCheck.js');
          loadJs('/dvfoundationwebsite/js/dataCheckFunc.js');
          loadJs('/dvfoundationwebsite/js/tipmessage.js');
          loadJs('/dvihealingwebsite/js/controller/healing/diagram/diagram.js');
          loadJs('/dvihealingwebsite/js/filter/healing/filter.js');
          loadJs('/dvihealingwebsite/js/controller/healing/plugin/codemirror.js');
          loadJs('/dvihealingwebsite/js/controller/healing/refreshfourpage.js');
          loadJs('/dvihealingwebsite/js/controller/healing/healing.js');
          loadJs('/dvihealingwebsite/js/controller/healing/diagram/JBME.js');
          loadJs('/dvihealingwebsite/js/controller/healing/diagram/bpm.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowlist.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/selectIhealingServerBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowLibrary/flowListBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowLibrary/deployFlowHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowLibrary/deleteFlowHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowLibrary/searchFlowHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowExecutePage/executeFlowBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowExecutePage/paramTransformHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowExecutePage/timerTemplate.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowExecutePage/scriptParamHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/selectRecipientBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/createNodeBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/createflow/createFlowBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/selectionParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/createflow/configAlarmEvent.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/flowParamTable.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/dbAccountParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/flowImg.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/remoteNotifyBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/stringParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/scriptParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/cascadeParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/ipListParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/paramValidateHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/dateParamBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowWindow/selectAccountBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowParam/neInstanceParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/getInfoHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/getFlowModelHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/getJbmeModelHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/extendNode.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/flowCategoryBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/basicInfoValidator.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/eventConfigBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowModel/jbmeModelBase.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/atomNode.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/atomNodeParam.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/toolBox.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/pauseNode.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/atomApiDetail.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/atomExecuteServer.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowNode/moTypeTree.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowWindow/selectHostHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowWindow/selectHostWindow.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowWindow/redirectHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/flowWindow/windowHelper.js');
          loadJs('/dvihealingwebsite/js/controller/healing/flow/createflow.js', function() {
            $CmpApp.controller('HealingCtrl',
              ['$scope', '$filter', '$rootScope', '$sce', '$q', '$compile', '$timeout', HealingCtrl]);
            $CmpApp.controller('CreateflowCtrl',
              ['$scope', '$timeout', '$q', 'RefreshService', '$rootScope', '$UI', CreateflowCtrl]);
            angular.bootstrap(document, ['cmp']);
          });
        });
      });
      window.eviewWidgetConfig = window.eviewWidgetConfig || {};
      window.eviewWidgetConfig.theme = 'default';
    </script>
</head>

<body ng-controller="HealingCtrl" ng-cloak style="min-width:1440px">
<div ng-controller="CreateflowCtrl" cmp-help-id="cmp.ihealing.faultdiagnosis.configflow" ng-init="init('createFlow')">
    <div id="loadingPage" ng-include="'/dvfoundationwebsite/app/loading.html'" style="display:none"></div>
    <div class="tip-message" style="display:none">
        <div class="cusotm-tip">
            <div class="input-tip"></div>
            <span id="tipContent" style="color:#e05c5c"></span>
        </div>
    </div>
    <div class="flow-page-wrap">
        <div class="main-title" ng-click="closeConditionPop()" style="margin-top: 18px;">
            <h3 ng-if="!flow.flowId" ng-bind="'cmp.healing.flow.create.title' | i18n"></h3>
            <h3 ng-if="flow.flowId && !$Model.isCopy" ng-bind="'cmp.healing.flow.modify.title' | i18n"></h3>
            <h3 ng-if="flow.flowId && $Model.isCopy" ng-bind="'cmp.healing.flow.copy.title' | i18n"></h3>
            <div class="df-label-tips title-tips" ng-init="isFlowTips = false" ng-mouseover="isFlowTips = true"
                 ng-mouseout="isFlowTips = false">
                <div class="df-label-bar"
                     style="background: url(/dvfoundationwebsite/images/tool.png) -45px -75px no-repeat;cursor: pointer;"
                     ng-click="redirectToHelpPage()">
                </div>
                <div ng-cloak class="df-tips left" ng-show="isFlowTips" style="width:300px">
                    <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips' | i18n"></p>
                    <div class="df-tips-arrow"></div>
                </div>
            </div>
            <div class="track-back" ng-click="goStep('flowlist')" ng-bind="'cmp.workbeans.common.return' | i18n">
            </div>
        </div>

        <div class="clearfix"></div>
        <div style="position:relative;">
            <div class="healing-flow-createflow">
                <table>
                    <tr>
                        <td>
                            <p>
                                <span>*</span>{{'cmp.healing.flow.name'| i18n}}：
                            </p>
                        </td>
                        <td style="padding-right:50px">
                            <div class="df-input-frame">
                                <input id="flowName" class="df-input requiredValidInput" ng-model="flow.name"
                                       ng-change="dataTypeValidCheck(['required','cmpValidChar','nameLengthValidator'],'flowName',flow.name,null,128)"
                                       ng-click="dataTypeValidCheck(['required','cmpValidChar','nameLengthValidator'],'flowName',flow.name,null,128)"
                                       ng-blur="closeShowTip()"/>
                            </div>
                        </td>
                        <td>
                            <p>
                                <span>*</span>{{'cmp.healing.flow.groupname.colon'| i18n}}
                            </p>
                        </td>
                        <td style="padding-right:40px">
                            <div class="df-select-frame">
                                <div class="df-select-box" cmp-selector>
                                    <input style="width: 170px;" title="{{flow.solution}}" ng-model="flow.solution" id="flowSolution"
                                           ng-change="dataTypeValidCheck(['required','cmpValidChar','nameLengthValidator','spaceCheck','validContainsChinese'],'flowSolution',flow.solution,null,128)"
                                           ng-click="dataTypeValidCheck(['required','cmpValidChar','nameLengthValidator','spaceCheck','validContainsChinese'],'flowSolution',flow.solution,null,128)"
                                           ng-blur="closeShowTip()" autocomplete="off" class="requiredValidInput"
                                           placeholder="{{'cmp.healing.flow.write.chose' | i18n}}"/>
                                    <div class="df-select-bar"></div>
                                </div>
                                <ul class="df-select-menu">
                                    <li ng-repeat="solution in solutions" title="{{solution}}"
                                        ng-click="flow.solution = solution;dataTypeValidCheck(['cmpValidChar','nameLengthValidator','validContainsChinese'],'flowSolution',flow.solution,null,128)">
                                        {{solution}}
                                    </li>
                                </ul>
                            </div>
                        </td>
                        <td>
                            <p>
                                <span>*</span>{{'cmp.healing.flow.category.colon'| i18n}}
                            </p>
                        </td>
                        <td style="padding-right:40px">
                            <div class="df-select-frame">
                                <div class="df-select-box" cmp-selector>
                                    <input style="width: 170px;" title="{{flowCategory}}" ng-model="flowCategory" id="flowCategory"
                                           ng-change="dataTypeValidCheck(['required','cmpValidChar','nameLengthValidator'],'flowCategory',flowCategory,null,128)"
                                           ng-click="dataTypeValidCheck(['required','cmpValidChar','nameLengthValidator'],'flowCategory',flowCategory,null,128)"
                                           ng-blur="closeShowTip()" autocomplete="off" class="requiredValidInput"
                                           placeholder="{{'cmp.healing.flow.write.chose' | i18n}}"/>
                                    <div class="df-select-bar"></div>
                                </div>
                                <ul class="df-select-menu">
                                    <li ng-repeat="category in categoriesValues" title="{{category}}"
                                        ng-click="flow.category = category">{{category}}
                                    </li>
                                </ul>
                            </div>
                        </td>
                        <td>
                            <p>
                                <span>*</span>{{'cmp.healing.flow.executemode'| i18n}}
                            </p>
                        </td>
                        <td style="padding-right:40px">
                            <div class="radio" style="float: left; margin-top: 5px">
                                <ul>
                                    <li ng-class="{'current':flow.executeMode=='SYNC'}">
                                        <div class="radio-bar" ng-click="flow.executeMode='SYNC'">
                                        </div>
                                        <p>{{'cmp.healing.flow.executemode.sync'| i18n}}</p>
                                    </li>
                                    <li ng-class="{'current':flow.executeMode=='ASYNC'}">
                                        <div class="radio-bar" ng-click="flow.executeMode='ASYNC'">
                                        </div>
                                        <p>{{'cmp.healing.flow.executemode.async'| i18n}}</p>
                                    </li>
                                </ul>
                            </div>
                            <div class="df-label-tips title-tips" style="margin-top: 5px"
                                 ng-init="isShowExecuteModeTips = false" ng-mouseover="isShowExecuteModeTips = true"
                                 ng-mouseout="isShowExecuteModeTips = false">
                                <div class="df-label-bar"
                                     style="background: url(/dvfoundationwebsite/images/tool.png) -45px -75px no-repeat;cursor: help;">
                                </div>
                                <div ng-cloak class="df-tips left" style="cursor: auto;" ng-show="isShowExecuteModeTips">
                                    <p class="spcial-warp ng-binding"
                                       ng-bind="'cmp.healing.flow.executemode.tips' | i18n">
                                    </p>
                                    <div class="df-tips-arrow"></div>
                                </div>
                            </div>
                        </td>

                        <td ng-show="false">
                            <p>
                                {{'cmp.healing.flow.type'|i18n}}{{'：' | i18n}}
                            </p>
                        </td>
                        <td ng-show="false">
                            <div class="card-pick-frame">
                                <div class="card-pick" ng-class="{'current':flow.type=='DIAGNOSE'}"
                                     ng-click="flow.type='DIAGNOSE'">
                                    <div class="flow-icon flow-icon-green"></div>
                                    <p>{{'cmp.healing.flow.diag'| i18n}}</p>
                                </div>
                                <div class="card-pick" ng-class="{'current':flow.type=='HEALING'}"
                                     ng-click="flow.type='HEALING'">
                                    <div class="flow-icon flow-icon-orange"></div>
                                    <p>{{'cmp.healing.flow.heal'| i18n}}</p>
                                </div>
                            </div>
                            <div class="df-label-tips" style="margin-top:3px;" ng-init="isShowTypeTips = false"
                                 ng-mouseover="isShowTypeTips = true" ng-mouseout="isShowTypeTips = false">
                                <uee:fire script="closeConditionPop()"></uee:fire>
                                <div class="df-label-bar"
                                     style="background: url(/dvfoundationwebsite/images/tool.png) -45px -75px no-repeat;">
                                </div>
                                <div ng-cloak class="df-tips w300 new-bottom" style="width:300"
                                     ng-show="isShowTypeTips">
                                    <p class="spcial-warp" style="width:280"
                                       ng-bind="'cmp.healing.flow.create.tips.type' | i18n"></p>
                                    <div class="df-tips-arrow"></div>
                                </div>
                            </div>
                        </td>
                    </tr>

                </table>
                <table>
                    <tr>
                        <td>
                            <div class="import-img-title">{{'cmp.healing.flow.imgPath.colon'| i18n}}</div>
                        </td>
                        <td>
                            <div class="import-main-title">
                                <div style="float:left">
                                    <div class="uee-field uee ng-scope" id="mc-import-config" labeldisplay="false"
                                         required="required" style="width:500px;">
                                        <div class="uee-field-req" ng-show="labeldisplay">
                                            <span ng-show="required" class="">*</span>
                                        </div>
                                        <div class="uee-field-label" ng-show="labeldisplay">
                                            <label ng-bind="fieldHead()" class="ng-binding"></label>
                                        </div>
                                        <div class="uee-field-body">
                                            <div class="file ng-scope" id="uploadFile" property="$Model.uploadFile">
                                                <div class="uee-file uee ng-scope" style="height: auto">
                                                    <div class="file-clear uee-toggleable"
                                                         ng-click="clearFileValue('log')"
                                                         title="{{'cmp_basic_clear' | commonI18n}}"
                                                         ng-disabled="disable"></div>
                                                    <div class="uee-border uee-file-border" id="uploadFile_border">
                                                        <form action="" id="uploadLogForm">
                                                            <div class="uee-file-pad">
                                                                <input id="uploadFile-file-value" type="text"
                                                                       readonly="readonly"
                                                                       class="uee-file-text ng-pristine ng-valid"
                                                                       title="{{uploadLogText}}"
                                                                       ng-model="uploadLogText">
                                                            </div>
                                                        </form>
                                                        <div class="file-browser uee-toggleable">
                                                            <div class="ng-scope">
                                                                <input id="uploadFile-file" class="fileImpl"
                                                                       ng-disabled="disable" type="file"
                                                                       onchange="angular.element(this).scope().uploadConfigFiles(this)">
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="df-label-tips" ng-mouseover="isShowConfigTips = true"
                                     ng-mouseout="isShowConfigTips = false" style="margin-top: 2px;">
                                    <div class="df-label-bar"></div>
                                    <div class="df-tips left" ng-show="isShowConfigTips">
                                        <p class="spcial-warp ng-binding"
                                           ng-bind="'cmp.healing.flow.import.img.tips' | i18n">
                                        </p>
                                        <div class="df-tips-arrow"></div>
                                    </div>
                                </div>
                            </div>

                        </td>
                    </tr>
                </table>
                <div style="padding: 0px 10px" class="import-img-title"
                     ng-if="(isModify || $Model.isCopy) && flow.imgPath">
                    <p style="float: left">{{'cmp.healing.flow.import.img.isDelete' | i18n}}</p>
                    <div style="display: inline-block;" class="flow-operation deleteImg"
                         ng-click="deleteCustomFlowImg(flow)"
                         title="{{'cmp.healing.flow.delete' | i18n}}">
                    </div>
                </div>

                <div class="process-element">
                    <div class="process-title">
                        <uee:fire script="closeConditionPop()"></uee:fire>
                        <div class="healing-step-status"
                             ng-click="isProcessEditorOpen ? closeProcessEditor() : openProcessEditor()">
                            <div ng-show="isProcessEditorOpen" class="wb-step-panelbar-open">
                            </div>
                            <div ng-show="!isProcessEditorOpen" class="wb-step-panelbar-close">
                            </div>
                            <p style="width:135px !important;">{{'cmp.healing.flow.graph.flow'| i18n}}</p>
                        </div>
                    </div>
                    <div ng-show="isProcessEditorOpen" class="flow-container">
                        <div class="flow-item-container" ng-click="closeConditionPop()">
                            <div class="flow-item" ng-mousedown="createDiagramNode('start')">
                                <div class="start-node"></div>
                                <span>{{'cmp.healing.flow.begin'| i18n}}</span>
                            </div>
                            <div class="flow-item" ng-mousedown="createDiagramNode('end')">
                                <div class="terminate-node"></div>
                                <span>{{'cmp.healing.flow.end'| i18n}}</span>
                            </div>
                            <div class="flow-item" ng-mousedown="createDiagramNode('gate_way')">
                                <div class="exclusiveGateway"></div>
                                <span>{{'cmp.healing.flow.condition'| i18n}}</span>
                            </div>
                            <div class="flow-item" ng-mousedown="createDiagramNode('incl_gate_way')">
                                <div class="parallelGateway"></div>
                                <span>{{'cmp.healing.flow.parallel'| i18n}}</span>
                            </div>
                            <div class="flow-item" ng-mousedown="createDiagramNode('user_task')">
                                <div class="user-task"></div>
                                <span>{{'cmp.healing.flow.user.confirm'| i18n}}</span>
                            </div>
                            <div class="flow-item" ng-mousedown="createDiagramNode('suspend')">
                                <div class="suspend"></div>
                                <span>{{'cmp.healing.flow.suspend'| i18n}}</span>
                            </div>
                            <div class="flow-item" ng-click="popSelectCommonAtom()">
                                <div class="toolbox"></div>
                                <span>{{'cmp.healing.flow.toolbox'| i18n}}</span>
                            </div>
                            <div class="flow-item" ng-click="popSelectAtom()">
                                <div class="call"></div>
                                <span>{{'cmp.healing.flow.atom'| i18n}}</span>
                            </div>
                            <div class="flow-item" ng-click="popParamTemplate()">
                                <div class="user-input-task"></div>
                                <span>{{'cmp.healing.flow.interact'| i18n}}</span>
                            </div>
                            <div class="flow-item"
                                 ng-mousedown="createDiagramPath({figures:[{type:'line', context:{strokeStyle:'#00aaff'}}]})">
                                <div class="connection-line"></div>
                                <span>{{'cmp.healing.flow.connect'| i18n}}</span>
                            </div>
                        </div>

                        <div>
                            <uee:diagram id="flow-diagram" property="$Model.bpmidediagram" style="height:100%"
                                         notifychange-listener="continueCreatePath($event)">
                                <uee:diagrammenu>
                                    <uee:diagrammenuitem label="{{'cmp.healing.flow.align.vertically'| i18n}}"
                                                         ng-click="setAlign($UI.diagram('flow-diagram').jHoverElement.id,'center')">
                                    </uee:diagrammenuitem>
                                    <uee:diagrammenuitem label="{{'cmp.healing.flow.align.left'| i18n}}"
                                                         ng-click="setAlign($UI.diagram('flow-diagram').jHoverElement.id,'left')">
                                    </uee:diagrammenuitem>
                                    <uee:diagrammenuitem label="{{'cmp.healing.flow.align.right'| i18n}}"
                                                         ng-click="setAlign($UI.diagram('flow-diagram').jHoverElement.id,'right')">
                                    </uee:diagrammenuitem>
                                    <uee:diagrammenuitem label="{{'cmp.healing.flow.align.horizontally'| i18n}}"
                                                         ng-click="setAlign($UI.diagram('flow-diagram').jHoverElement.id,'middle')">
                                    </uee:diagrammenuitem>
                                    <uee:diagrammenuitem label="{{'cmp.healing.flow.align.top'| i18n}}"
                                                         ng-click="setAlign($UI.diagram('flow-diagram').jHoverElement.id,'top')">
                                    </uee:diagrammenuitem>
                                    <uee:diagrammenuitem label="{{'cmp.healing.flow.align.bottom'| i18n}}"
                                                         ng-click="setAlign($UI.diagram('flow-diagram').jHoverElement.id,'bottom')">
                                    </uee:diagrammenuitem>
                                </uee:diagrammenu>
                            </uee:diagram>
                        </div>

                    </div>
                    <div ng-show="isProcessEditorOpen" style="height: 180px; width:100%;background:#fff;">
                        <div class="flow-area">
                                <textarea class="df-textarea" style="height: 150px !important"
                                          placeholder="{{'cmp.healing.flow.description'| i18n}}"
                                          ng-class="{'focus':focusFlag}" title="{{flow.description}}" ng-model="flow.description"
                                          id="flowdescription"
                                          ng-change="dataTypeValidCheck(['nameLengthValidator'],'flowdescription',flow.description,null,1000)"
                                          ng-click="dataTypeValidCheck(['nameLengthValidator'],'flowdescription',flow.description,null,1000)"
                                          ng-blur="closeShowTip()" ng-focus="focusFlag = true"
                                          ng-blur="focusFlag = false">
                                </textarea>
                        </div>
                    </div>
                </div>
            </div>


            <div class="process-element">
                <uee:fire script="closeConditionPop()"></uee:fire>
                <div class="process-title">
                    <div class="healing-step-status"
                         ng-click="isFlowParameterEditorOpen ? closeFlowParameterEditor() : openFlowParameterEditor()">
                        <div ng-show="isFlowParameterEditorOpen" class="wb-step-panelbar-open">
                        </div>
                        <div ng-show="!isFlowParameterEditorOpen" class="wb-step-panelbar-close">
                        </div>
                        <p style="width:135px; !important">{{'cmp.healing.flow.flowParameter'| i18n}}</p>
                        <div class="clear df-label-tips" style="margin-top: 6px;margin-left: 225px"
                             ng-init="isShowFlowParamTips = false" ng-mouseover="isShowFlowParamTips = true"
                             ng-mouseout="isShowFlowParamTips = false">
                            <div class="df-label-bar"
                                 style="background: url(/dvfoundationwebsite/images/tool.png) -45px -75px no-repeat;cursor: help;">
                            </div>
                            <div ng-cloak class="df-tips left" style="cursor: auto;" ng-show="isShowFlowParamTips">
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.flow.param' | i18n">
                                </p>
                                <div class="df-tips-arrow"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div ng-show="isFlowParameterEditorOpen" style="padding:24px 24px 24px 24px;">
                    <div class="df-btn-group-left" style="padding-bottom: 10px;">
                        <div class="df-btn" ng-click="addParamBySelf()">
                            <span class="icon-add"></span>
                            {{'cmp.healing.flow.flowParameter.operation.add.self' | i18n}}
                        </div>
                    </div>
                    <div class="df-btn-group-left" style="padding-bottom: 10px;">
                        <div class="df-btn" ng-click="addParamByChooseGlobal()">
                            <span class="icon-add"></span>
                            {{'cmp.healing.flow.flowParameter.operation.add.global' | i18n}}
                        </div>
                    </div>
                    <div class="df-btn-group-left" style="padding-bottom: 10px;">
                        <div class="df-btn" ng-click="addParamByUploadedFile()">
                            <span class="icon-add"></span>
                            {{'cmp.healing.flow.flowParameter.operation.add.file' | i18n}}
                        </div>
                    </div>
                    <div class="df-label-tips title-tips" ng-init="isFlowTips = false"
                         ng-mouseover="isFlowTips = true" ng-mouseout="isFlowTips = false"
                         style="margin-top: 3px;z-index:10">
                        <div class="df-label-bar"
                             style="background: url(/dvfoundationwebsite/images/tool.png) -45px -75px no-repeat;cursor: help;">
                        </div>
                        <div ng-cloak class="df-tips left" ng-show="isFlowTips">
                            <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.flow.param.add' | i18n">
                            </p>
                            <div class="df-tips-arrow"></div>
                        </div>
                    </div>

                    <eview:table id="flowParamTable" width="100%" dataset="tableData" listeners="paramTableListeners" drag-level='row' drop-level='row' enable-pagination="false" enable-tooltip="false" allow-parsing="false">
                        <eview:table-column id="name" width="14%" column-type='custom' custom-editor="nameEditor" caption="getI18n('cmp.healing.flow.flowParameter.name')" sortable='false' enable-tooltip="false"></eview:table-column>
                        <eview:table-column id="description" width="14%" column-type='custom' custom-editor="descEditor" caption="getI18n('cmp.healing.flow.flowParameter.description')" sortable='false' enable-tooltip="false"></eview:table-column>
                        <eview:table-column id="flowParameterSource" width="14%" column-type='custom' custom-editor="sourceEditor" caption="getI18n('cmp.healing.flow.flowParameter.source')" sortable='false' enable-tooltip="false"></eview:table-column>
                        <eview:table-column id="type" width="14%" column-type='custom' custom-editor="typeEditor" caption="getI18n('cmp.healing.flow.flowParameter.type')" sortable='false' enable-tooltip="false"></eview:table-column>
                        <eview:table-column id="value" width="15%" column-type='custom' custom-editor="valueEditor" caption="getI18n('cmp.healing.flow.flowParameter.value')" sortable='false' enable-tooltip="false"></eview:table-column>
                        <eview:table-column id="defaultValue" width="15%" column-type='custom' custom-editor="defaultValueEditor" caption="getI18n('cmp.healing.flow.default.parameter.value')" sortable='false' enable-tooltip="false"></eview:table-column>
                        <eview:table-column id="required" width="5%" column-type='custom' custom-editor="requiredEditor" caption="getI18n('cmp.healing.flow.flowParameter.required')" sortable='false' enable-tooltip="false"></eview:table-column>
                        <eview:table-column id="visible" width="5%" column-type='custom' custom-editor="visibleEditor" caption="getI18n('cmp.healing.flow.flowParameter.visible')" sortable='false' enable-tooltip="false"></eview:table-column>
                        <eview:table-column id="operation" width="4%" column-type='custom' custom-editor="operationEditor" caption="getI18n('cmp.healing.flow.flowParameter.operation')" sortable='false' enable-tooltip="false"></eview:table-column>
                    </eview:table>
                </div>
            </div>

            <div class="process-element">
                <uee:fire script="closeConditionPop()"></uee:fire>
                <div class="process-title">
                    <div class="healing-step-status"
                         ng-click="isEventEditoreOpen ? closeEventEditor() : openEventEditor()">
                        <div ng-if="isEventEditoreOpen" class="wb-step-panelbar-open">
                        </div>
                        <div ng-if="!isEventEditoreOpen" class="wb-step-panelbar-close">
                        </div>
                        <p style="width:135px; !important">{{'cmp.healing.flow.event.config'| i18n}}</p>
                        <div class="clear df-label-tips" style="margin-top: 6px;margin-left: 225px"
                             ng-init="isShowEventTips = false" ng-mouseover="isShowEventTips = true"
                             ng-mouseout="isShowEventTips = false">
                            <div class="df-label-bar"
                                 style="background: url(/dvfoundationwebsite/images/tool.png) -45px -75px no-repeat;cursor: help;">
                            </div>
                            <div ng-cloak class="df-tips left" style="cursor: auto;" ng-show="isShowEventTips">
                                <p class="spcial-warp" ng-bind="'cmp.healing.flow.create.tips.event' | i18n"></p>
                                <div class="df-tips-arrow"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div ng-if="isEventEditoreOpen" style="padding-left:24px">

                    <!--事件配置-->
                    <div class="radio" style="margin-top:20px;margin-bottom:20px;">
                        <h3>{{'cmp.healing.flow.event.type' | i18n}}</h3>
                        <ul>
                            <li ng-class="{'current':$Model.eventType=='NULL'}">
                                <div class="radio-bar" ng-click="$Model.eventType='NULL'">
                                </div>
                                <p>{{'cmp.healing.flow.null' | i18n}}</p>
                            </li>
                            <li ng-class="{'current':$Model.eventType=='ALARM'}">
                                <div class="radio-bar" ng-click="$Model.eventType='ALARM'">
                                </div>
                                <p>{{'cmp.healing.flow.event.alarm' | i18n}}</p>
                            </li>
                            <li ng-class="{'current':$Model.eventType=='CRONTAB'}">
                                <div class="radio-bar" ng-click="$Model.eventType='CRONTAB';$scope.checkFireTime()">
                                </div>
                                <p>{{'cmp.healing.flow.event.timer' | i18n}}</p>
                            </li>
                        </ul>
                        <div class="error-tips" style="margin-top:20px" ng-show="alarmMsg"><span
                                class="error-icon"></span>{{alarmMsg}}
                        </div>
                    </div>
                    <div class="clearfix"></div>
                    <table class="event-table" ng-if="$Model.eventType=='ALARM'">
                        <tr>
                            <td>
                                <div>
                                    <div class="df-btn-group-left" style="padding-bottom: 10px;">
                                        <div class="df-btn" ng-click="popAddAlarmEvent()"><span
                                                class="icon-add"></span>
                                            {{'cmp.healing.flow.add' | i18n}}
                                        </div>
                                    </div>
                                    <div class="df-label-tips alarm-trigger-tips" style="margin-top:3px;"
                                         ng-init="isShowEventAlarmTips = false"
                                         ng-mouseover="isShowEventAlarmTips = true"
                                         ng-mouseout="isShowEventAlarmTips = false">
                                        <div class="df-label-bar"
                                             style="background: url(/dvfoundationwebsite/images/tool.png) -45px -75px no-repeat;">
                                        </div>
                                        <div ng-cloak class="df-tips left" ng-show="isShowEventAlarmTips">
                                            <p class="spcial-warp"
                                               ng-bind="'cmp.healing.flow.create.tips.event.alarm' | i18n"></p>
                                            <p class="spcial-warp"
                                               ng-bind="'cmp.healing.flow.create.tips.event.alarm.id' | i18n"></p>
                                            <p class="spcial-warp"
                                               ng-bind="'cmp.healing.flow.create.tips.event.threshold' | i18n"></p>
                                            <p class="spcial-warp"
                                               ng-bind="'cmp.healing.flow.create.tips.event.alarm.2' | i18n"></p>
                                            <div class="df-tips-arrow"></div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <table style="width:100%;margin-bottom:20px;" ng-table template-pagination="none"
                                       class="tableCommon clearfix">
                                    <thead>
                                    <tr>
                                        <th style="width:20px;">
                                            <div class="healing-table-header">
                                            </div>
                                        </th>
                                        <th style="width:25%;" class="healing-table-header">
                                            <span ng-bind="'cmp.healing.flow.event.alarm.id' | i18n"></span>
                                        </th>

                                        <th style="width:25%;" class="healing-table-header">
                                                    <span
                                                            ng-bind="'cmp.healing.flow.event.alarm.measureUnit' | i18n"></span>
                                        </th>
                                        <th style="width:25%;" class="healing-table-header">
                                                    <span
                                                            ng-bind="'cmp.healing.flow.event.alarm.metricName' | i18n"></span>
                                        </th>
                                        <th style="width:25%;" class="healing-table-header">
                                                    <span
                                                            ng-bind="'cmp.healing.flow.event.alarm.operation' | i18n"></span>
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr ng-repeat="alarmEvent in $Model.alarmEvents">
                                        <td>
                                            <div></div>
                                        </td>
                                        <td title="{{alarmEvent.parameter.alarmId}}">
                                            {{alarmEvent.parameter.alarmId}}
                                        </td>
                                        <td title="{{alarmEvent.parameter.measureUnit}}">
                                            {{alarmEvent.parameter.measureUnit}}
                                        </td>
                                        <td title="{{alarmEvent.parameter.metricName}}">
                                            {{alarmEvent.parameter.metricName}}
                                        </td>
                                        <td>
                                            <a class="healing-alarm-detail"
                                               title="{{'cmp.healing.flow.modify' | i18n}}"
                                               ng-click="modifyAlarmInfo(alarmEvent)">
                                            </a>
                                            <a class="icon-btn-delete"
                                               title="{{'cmp.healing.flow.delete' | i18n}}"
                                               ng-click="$Model.alarmEvents.splice($index,1)">
                                            </a>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="df-btn-group-left" style="margin: 30px 0 20px 0;">
        <div ng-if="!isModify && !$Model.isCopy" class="df-btn" ng-click="submitFlow('draft')">
            {{'cmp.healing.flow.save'|i18n}}
        </div>
        <div ng-if="isModify && !$Model.isCopy" class="df-btn" ng-click="modifyFlow('draft')">
            {{'cmp.healing.flow.save'|i18n}}
        </div>
        <div ng-if="isModify && !$Model.isCopy" class="df-btn" ng-click="modifyFlow('pause');">
            {{'cmp.healing.flow.submit'|i18n}}
        </div>
        <div ng-if="!isModify && !$Model.isCopy" class="df-btn" ng-click="submitFlow('pause')">
            {{'cmp.healing.flow.submit'|i18n}}
        </div>
        <div ng-if="!isModify && $Model.isCopy" class="df-btn" ng-click="submitFlow('draft')">
            {{'cmp.healing.flow.save'|i18n}}
        </div>
        <div ng-if="!isModify && $Model.isCopy" class="df-btn" ng-click="submitFlow('pause')">
            {{'cmp.healing.flow.submit'|i18n}}
        </div>
        <div style="margin-top:20px;color:#666;display: inline;" ng-show="saveMsg"><span></span>{{saveMsg}}
        </div>
        <div class="df-btn" ng-click="goStep('flowlist')">
            {{'cmp.basic.cancel'|i18n}}
        </div>
    </div>

</div>
</body>

</html>
