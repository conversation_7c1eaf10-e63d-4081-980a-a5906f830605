<!DOCTYPE html>
<html xmlns:eview="ignored">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <title></title>

    <link href="/dvihealingwebsite/css/healing/dashboard.css" type="text/css" rel="stylesheet">
    <link href="/dvfoundationwebsite/css/tableCommon.css" type="text/css" rel="stylesheet" />
    <link href="/dvfoundationwebsite/css/reset.css" type="text/css" rel="stylesheet" />
    <link href="/dvfoundationwebsite/css/common.css" type="text/css" rel="stylesheet" />
    <link href="/dvfoundationwebsite/css/loading.css" type="text/css" rel="stylesheet" />
    <link href="/dvihealingwebsite/css/healing/flow.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/healing.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/cmp-ihealing-event.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/workbenchindex.css" type="text/css" rel="stylesheet">
    <link href="/dvihealingwebsite/css/healing/index.css" type="text/css" rel="stylesheet" />

    <style>
        body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, textarea, p, blockquote, th, td, span, a {
            font-size: 12px;
        }

        table tbody tr {
            height: 50px !important;
        }

        table.tableCommon tbody tr {
            height: 43px !important;
        }

        table tbody td.property_name {
            width: 5%;
        }

        table tbody td.property_content {
            width: 95%;
        }

        span {
            color: #666;
        }

        html {
            font-size: 16px !important;
        }
        .paramName {
            max-width: 250px;
            display: inline-block;
            overflow: hidden;
        }
        .require{
            color: #fc5043;
            width: 6px;
            display: inline-block;
        }
        .wide-input {
            width: 500px !important;
        }
    </style>

    <script src="/dvihealingwebsite/js/auth.js" id="licenseScript" name="DVIhealingWebsite.BasicAccess"></script>
    <script src="/febs/v1/assets/prelude-loader"></script>
    <script src="/dvfoundationwebsite/js/common.js"></script>
</head>

<body ng-controller="HealingCtrl">
<div class="side-page-wrap" ng-controller="FlowTaskController">
    <div id="loadingPage" ng-include="'/dvfoundationwebsite/app/loading.html'" style="display:none"></div>
    <div ng-controller="TaskEditModeCtrl" cmp-help-id="cmp.ihealing.faultdiagnosis.flowtask" ng-init="taskEditModeInit()"
         ng-cloak>
        <div class="tip-message" style="display:none">
            <div class="cusotm-tip">
                <div class="input-tip"></div>
                <span id="tipContent" style="color:#e05c5c"></span>
            </div>
        </div>

        <div class="side-page-close" ng-click="closePage()"></div>
        <div class="main-title" style="margin-top: 20px;">
            <h3>{{'cmp_healing_flow_task_create' | i18n}}</h3>
        </div>
        <div class="process-title" style="margin-top:10px;">
            <div class="healing-step-status"
                 ng-click="switchTaskInfoEditor()">
                <div ng-show="isTaskInfoEditorOpen" class="wb-step-panelbar-open">
                </div>
                <div ng-show="!isTaskInfoEditorOpen" class="wb-step-panelbar-close">
                </div>
                <p style="width:135px !important;">{{'cmp.healing.flow.task.info'| i18n}}</p>
            </div>
        </div>
        <table ng-show=isTaskInfoEditorOpen id="taskInfoTable" style="width: 100%">
            <tbody>
            <tr>
                <td class="property_name">
                    <span>{{'cmp_healing_task_name' | i18n}}</span>
                </td>
                <td class="property_content">
                    <input id="currentTaskName" class="df-input requiredValidInput" style="width: 290px" ng-model="currentTask.taskName"
                           ng-change="dataTypeValidCheck(['cmpValidChar','required','nameLengthValidator'],'currentTaskName',currentTask.taskName,null,128)"
                           ng-click="dataTypeValidCheck(['cmpValidChar','required','nameLengthValidator'],'currentTaskName',currentTask.taskName,null,128)"
                           ng-blur="closeShowTip()"/>
                </td>
            </tr>
            <tr>
                <td class="property_name">
                    <span>{{'cmp_healing_task_type' | i18n}}</span>
                </td>
                <td class="property_content">
                    <div style="float: left;height: 30px;margin-bottom: 0px;">
                        <div id="manualDiv" class="card-pick" ng-class="{'current' : currentTask.type==='MANUAL'}"
                             ng-click="focusTagField('MANUAL')" style="width:110px">{{'cmp_healing_task_event_manual' | i18n}}
                        </div>
                        <div id="scheduleDiv" class="card-pick" ng-class="{'current' : currentTask.type==='TIMER'}"
                             ng-click="focusTagField('TIMER')" style="width:110px">{{'cmp_healing_task_event_timer' | i18n}}
                        </div>
                    </div>
                </td>
            </tr>
            <tr ng-if="currentTask.type==='TIMER'">
                <td class="property_name">
                    <span>{{'cmp.healing.flow.cron.expression' | i18n}}</span>
                </td>
                <td class="property_content">
                    <input id="cronExpression" class="df-input requiredValidInput" style="width: 210px" ng-model="currentTask.cron"
                           ng-init="checkCronExpression()"
                           ng-change="checkCronExpression()"
                           ng-click="checkCronExpression()"
                           ng-blur="closeShowTip()"/>
                    <div class="df-label-tips" ng-init="isShowCronTips = false"
                         ng-mouseover="isShowCronTips = true" ng-mouseout="isShowCronTips = false">
                        <div class="df-label-bar"
                             style="background: url(/dvfoundationwebsite/images/tool.png) -45px -75px no-repeat;">
                        </div>
                        <div ng-cloak class="df-tips left" ng-show="isShowCronTips">
                            <p class="spcial-warp"
                               ng-bind="'cmp.healing.flow.create.tips.event.cron.1' | i18n">
                            </p>
                            <p class="spcial-warp"
                               ng-bind="'cmp.healing.flow.create.tips.event.cron.2' | i18n">
                            </p>
                            <p class="spcial-warp"
                               ng-bind="'cmp.healing.flow.create.tips.event.cron.3' | i18n">
                            </p>
                            <p
                                    ng-bind="'cmp.healing.flow.create.tips.event.cron.4' | i18n">
                            </p>
                            <p ng-if="cronNextFireTime">
                                {{'cmp.healing.flow.init.execute.time' | i18n}} :
                            </p>
                            <div ng-repeat="fireTime in cronNextFireTime">
                                <span style="color: white;padding-left: 16px">{{fireTime}}</span>
                            </div>
                            <div class="df-tips-arrow"></div>
                        </div>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
        <div class="process-title" style="margin-top:10px;">
            <div class="healing-step-status"
                 ng-click="switchFlowInfoEditor()">
                <div ng-show="isFlowInfoEditorOpen" class="wb-step-panelbar-open">
                </div>
                <div ng-show="!isFlowInfoEditorOpen" class="wb-step-panelbar-close">
                </div>
                <p style="width:135px !important;">{{'cmp.healing.flow.global.parameter.search.flows'| i18n}}</p>
            </div>
        </div>
        <table ng-show=isFlowInfoEditorOpen id="flowInfoTable">
            <tbody>
            <tr>
                <td class="property_name">
                    <span>{{'cmp_healing_flow_task_choose_flow' | i18n}}</span>
                </td>
                <td class="property_content">
                    <ul class="clearfix">
                        <li>
                            <p ng-bind="'cmp_healing_flow_groupname' | i18n" class="ng-binding" style="line-height: 26px;padding-right: 6px;float: left"></p>
                            <div class="df-select-frame" style="padding-right: 30px;float: left">
                                <div class="df-input-frame" cmp-input-focus="">
                                    <div class="df-select-box" cmp-selector="">
                                        <input id="groupSelector" ng-if="isCreate"
                                               class="df-input requiredValidInput"
                                               autocomplete="off"
                                               ng-model="currentTask.flowGroup"
                                               placeholder="{{'cmp_healing_flow_groupname' | i18n}}"
                                               ng-change="dataTypeValidCheck(['cmpValidChar','nameLengthValidator'],'groupSelector',searchedFlowGroup,null,128)"
                                               ng-click="dataTypeValidCheck(['cmpValidChar','nameLengthValidator'],'groupSelector',searchedFlowGroup,null,128)"
                                               ng-blur="closeShowTip()"/>
                                        <input ng-model="currentTask.flowGroup" ng-if="!isCreate"
                                               ng-click="cannotReplaceFlow()"
                                               style="width: 170px;color:#c3c3c3" readonly />
                                        <div class="df-select-bar"></div>
                                    </div>
                                    <ul class="df-select-menu" ng-if="isCreate">
                                        <li ng-repeat="groupName in allOptionalSolutions track by $index" title="{{groupName}}"
                                            ng-click="queryFlowBySolution($index)">
                                            {{groupName}}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                        <li ng-if="currentTask.flowGroup !== ''">
                            <p ng-bind="'cmp_healing_flow_name' | i18n" class="ng-binding" style="line-height: 26px;padding-right: 6px;float: left"></p>
                            <div class="df-select-frame" style="float: left">
                                <div class="df-input-frame" cmp-input-focus="">
                                    <div class="df-select-box" cmp-selector="">
                                        <input id="flowSelector" ng-if="isCreate"
                                               class="df-input requiredValidInput"
                                               autocomplete="off"
                                               ng-model="currentTask.flowName"
                                               placeholder="{{'cmp_healing_flow_name' | i18n}}"
                                               ng-change="dataTypeValidCheck(['cmpValidChar','nameLengthValidator'],'flowSelector',searchedFlowName,null,128)"
                                               ng-click="dataTypeValidCheck(['cmpValidChar','nameLengthValidator'],'flowSelector',searchedFlowName,null,128)"
                                               ng-blur="closeShowTip()"/>
                                        <input ng-if="!isCreate" ng-model="currentTask.flowName"
                                               ng-click="cannotReplaceFlow()"
                                               style="width: 170px;color:#c3c3c3" readonly />
                                        <div class="df-select-bar"></div>
                                    </div>
                                    <ul class="df-select-menu" ng-if="isCreate">
                                        <li ng-repeat="flowName in allOptionalFlows track by $index" title="{{flowName}}"
                                            ng-click="afterSelectFlow($index)">
                                            {{flowName}}
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </li>
                    </ul>
                </td>
            </tr>
                <td class="property_name">
                    <span>{{'cmp_healing_flow_parameter' | i18n}}</span>
                </td>
                <td class="property_content">
                    <table id="parameterTable" class="tableCommon clearfix ng-scope" template-pagination="none" style="margin-top: 10px">
                        <thead>
                        <tr>
                            <th style="width: 25%">
                                <div class="healing-table-header"
                                     style="text-align: left;padding-left: 12px">
                                        <span
                                                style="border-left:0px">{{'cmp.healing.flow.flowParameter.change.name' | i18n}}</span>
                                </div>
                            </th>
                            <th style="width: 40%">
                                <div class="healing-table-header"
                                     style="text-align: left;">
                                        <span
                                                style="border-left:0px">{{'cmp.healing.flow.flowParameter.value' | i18n}}</span>
                                </div>
                            </th>
                            <th style="width: 35%">
                                <div class="healing-table-header" style="text-align: left">
                                        <span
                                                style="border-left:0px">{{'cmp.healing.flow.flowParameter.description' | i18n}}</span>
                                </div>
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr ng-init="item.index = $index" ng-repeat="item in flowParameters track by $index" ng-show="item.showStatus === 1 || item.showStatus === 3">
                            <td colspan="3" style="padding: 0;border: none;overflow: visible">
                                <table style="width: 100%;padding: 0px;">
                                    <tr>
                                        <td ng-if="item.flowParamType === 'CASCADE'" style="width: 25%">
                                            <div style="margin-top:1px;display: inline-block" class="df-sub-arrow" ng-click="item.show = !item.show" ng-class="{'df-arrow-close': !item.show}"></div>
                                            <div style="display: inline-block">
                                                <span class="require" ng-show="item.showStatus == 1 || item.showStatus == 2">*</span>
                                                <span class="optional" ng-show="item.showStatus == 3 || item.showStatus == 4"></span>
                                                <span id="paraName_{{$index}}" class="paramName" ng-bind="item.name" title="{{item.name}}"></span>
                                            </div>
                                        </td>
                                        <td ng-if="item.flowParamType !== 'CASCADE'" style="width: 25%">
                                            <div style="padding-left: 10px;display: inherit">
                                                <span class="require" ng-show="item.showStatus == 1 || item.showStatus == 2">*</span>
                                                <span class="optional" ng-show="item.showStatus == 3 || item.showStatus == 4"></span>
                                                <span id="paraName_{{$index}}" class="paramName" ng-bind="item.name" title="{{item.name}}"></span>
                                            </div>
                                        </td>
                                        <td style="overflow: visible;width: 50%;">
                                            <div ng-if="(item.flowParameterSource == 'DEFINITION' || item.flowParameterSource == undefined || item.flowParameterSource == null)" class="df-input-frame" cmp-input-focus>
                                                <div ng-if="(item.flowParamType == 'STRINGTYPE' || item.flowParamType == undefined || item.flowParamType == null)  && item.showStatus == 1">
                                                    <input class="df-input wide-input" ng-class="{'requiredValidInput': item.showStatus === 1}" ng-model="item.value" id="itemValue_08_{{$index}}" autocomplete="off"
                                                           ng-change="setParaChange();dataTypeValidCheck(['required','nameLengthValidator','validFlowPara'],'itemValue_08_'+$index,item.value,null,8000)"
                                                           ng-click="dataTypeValidCheck(['required','nameLengthValidator','validFlowPara'],'itemValue_08_'+$index,item.value,null,8000)"
                                                           ng-blur="closeShowTip()">
                                                    </input>
                                                </div>
                                                <div ng-if="(item.flowParamType == 'STRINGTYPE' || item.flowParamType == undefined || item.flowParamType == null) && item.showStatus == 3">
                                                    <input class="df-input wide-input" ng-class="{'requiredValidInput': item.showStatus === 1}" ng-model="item.value" id="itemValue_08_{{$index}}" autocomplete="off"
                                                           ng-change="setParaChange();dataTypeValidCheck(['nameLengthValidator','validFlowPara'],'itemValue_08_'+$index,item.value,null,8000)"
                                                           ng-click="dataTypeValidCheck(['nameLengthValidator','validFlowPara'],'itemValue_08_'+$index,item.value,null,8000)"
                                                           ng-blur="closeShowTip()">
                                                    </input>
                                                </div>
                                                <div ng-if="item.flowParamType == 'DATETYPE'" id="confirmFlow_{{$index}}">
                                                    <div style="display: inline-block;float: left">
                                                        <eview:date-time-picker id="confirmTime_{{$index}}" type='datetime' width='530px' required='item.showStatus == 1'>
                                                        </eview:date-time-picker>
                                                    </div>
                                                    <div style="margin-left:20px" class="df-btn" ng-click="selectPresentDateParam($index)">
                                                        {{'cmp.healing.flow.para.date.now'|i18n}}
                                                    </div>
                                                </div>
                                                <div ng-if="item.flowParamType == 'PVALUETYPE'">
                                                    <input id="itemValue_pValue_{{$index}}" class="df-input wide-input" ng-class="{'requiredValidInput': item.showStatus === 1}" type="password" ng-model="item.value" autocomplete="off" style="float: left">
                                                    <div class="df-label-tips title-tips" ng-init="pvalue_{{$index}} = false"
                                                         ng-mouseover="pvalue_{{$index}} = true" ng-mouseout="pvalue_{{$index}} = false"
                                                         style="float:left;margin-top:5px;margin-left:5px;">
                                                        <div class="df-label-bar" style="display: inline-block;background: url(/dvfoundationwebsite/images/tool.png) -45px -75px no-repeat;"></div>
                                                        <div ng-cloak class="df-tips left" ng-show="pvalue_{{$index}}" style="width:205px">
                                                            <p class="spcial-warp" style="width:200px" ng-bind="'cmp.healing.flow.pass.flow.tips' | i18n"></p>
                                                            <div class="df-tips-arrow"></div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div ng-if="item.flowParamType === 'ENUMTYPE' && item.isSupportMulti == true">
                                                    <div style="display: inline-block;float: left">
                                                        <eview:editable-select id="itemValue_17{{$index}}" width='530px' values="item.valueList" required='item.showStatus == 1' change="setParaChange" select-value='item.value'
                                                                               default-label="" value-field="value" label-field="label" mode="multi" select-all="true" case-insensitive-filter='true'>
                                                        </eview:editable-select>
                                                    </div>
                                                </div>
                                                <div ng-if="item.flowParamType === 'ENUMTYPE' && item.isSupportMulti == false">
                                                    <div style="display: inline-block;float: left">
                                                        <eview:editable-select id="itemValue_17{{$index}}" width='530px' values="item.valueList" required='item.showStatus == 1' change="setParaChange"
                                                                               select-value='item.value' default-label="" value-field="value" label-field="label" case-insensitive-filter='true'>
                                                        </eview:editable-select>
                                                    </div>
                                                </div>
                                                <div ng-if="item.flowParamType === 'SCRIPTTYPE'">
                                                    <!-- 脚本型参数支持一层下拉展示 -->
                                                    <div ng-if="!isScriptCascadeMode(item.selectValues)" style="display: inline-block;float: left">
                                                        <eview:editable-select id="itemValue_17{{$index}}" width='530px' values="item.valueList" required='item.showStatus == 1' change="setParaChange"
                                                                               select-value='item.value' default-label="" value-field="value" label-field="label" mode="multi" select-all="true" case-insensitive-filter='true'>
                                                        </eview:editable-select>
                                                    </div>
                                                    <!-- 脚本型参数支持级联，两层下来展示，第一层数据 -->
                                                    <div ng-if="isScriptCascadeMode(item.selectValues)" style="display: inline-block;float: left" ng-init="item.show = true">
                                                        <eview:editable-select id="scriptFirstValue_{{$index}}" width='530px' values="item.firstValueList" required='item.showStatus == 1' change="setScriptCascadeData"
                                                                      select-value='item.firstValue' default-label="" value-field="paramId" label-field="paramName" mode="multi" select-all="true" case-insensitive-filter='true'>
                                                        </eview:editable-select>
                                                    </div>
                                                </div>
                                                <div ng-if="item.flowParamType === 'DB_ACCOUNT' && item.showStatus == 1" ng-click="popSelectAccount($index)">
                                                    <input class="df-input wide-input" ng-class="{'requiredValidInput': item.showStatus === 1}" ng-model="item.tempParam.displayValue" autocomplete="off"
                                                           id="itemValue_06_{{$index}}" readonly
                                                           ng-change="setParaChange();dataTypeValidCheck(['required'],'itemValue_06_'+$index,item.value,null,8000)"
                                                           ng-click="dataTypeValidCheck(['required'],'itemValue_06_'+$index,item.value,null,8000)"
                                                           ng-blur="closeShowTip()">
                                                </div>
                                                <div ng-if="item.flowParamType === 'DB_ACCOUNT' && item.showStatus == 3" ng-click="popSelectAccount($index)">
                                                    <input class="df-input wide-input" ng-class="{'requiredValidInput': item.showStatus === 1}" ng-model="item.tempParam.displayValue" autocomplete="off"
                                                           id="itemValue_06_{{$index}}" readonly
                                                           ng-blur="closeShowTip()">
                                                </div>
                                                <div ng-if="item.flowParamType === 'ENUMRATE_KEY_VALUE'">
                                                    <div style="display: inline-block;float: left">
                                                        <eview:editable-select id="itemValue_18{{$index}}" width='530px' values="item.valueList" required='item.showStatus == 1' change="setParaChange"
                                                                               select-value='item.value' default-label="" value-field="value" label-field="label" mode="item.isSupportMulti ? 'multi' : 'single'" select-all="true" case-insensitive-filter='true'>
                                                        </eview:editable-select>
                                                    </div>
                                                </div>
                                                <div ng-if="item.flowParamType === 'REMOTE_NOTIFY_OBJ'" ng-click="beforeSelectRecipient($index)">
                                                    <eview:input id="notifyListId_{{$index}}" name="inputText" value='' required='item.showStatus == 1' readonly='true' width="530px" type="text" place-holder=" ">
                                                    </eview:input>
                                                </div>
                                                <div ng-if="item.flowParamType === 'DN_TYPE'" ng-click="popSelectNeInstance($index)">
                                                    <eview:input id="neInstance_{{$index}}" name="inputText" value='' required='item.showStatus == 1' readonly='true' width="530px" type="text" place-holder=" ">
                                                    </eview:input>
                                                </div>
                                                <div ng-if="item.flowParamType === 'IP_TREE'" ng-click="popSelectIps($index, 'father', $index)">
                                                    <input class="df-input wide-input" ng-class="{'requiredValidInput': item.showStatus === 1}" readonly value="{{item.value}}" ng-model="item.value" autocomplete="off" id="itemValue_09_{{$index}}"></input>
                                                </div>
                                                <div ng-if="item.flowParamType === 'CASCADE'">
                                                    <div style="display: inline-block;float: left">
                                                        <eview:editable-select id="endValue_02_{{$index}}" width='530px' values="item.params" required='item.showStatus == 1' change="setChildrenData"
                                                                      select-value='item.valueObject.paramName' default-label="" value-field="paramsName" label-field="paramsLabel">
                                                        </eview:editable-select>
                                                    </div>
                                                </div>
                                            </div>
                                            <div ng-if="item.flowParameterSource == 'GLOBAL'"
                                                 class="df-input-frame" cmp-input-focus>
                                                <input readonly="readonly" ng-model="item.value"
                                                       class="wide-input" ng-class="{'requiredValidInput': item.showStatus === 1}"
                                                       style="text-overflow: ellipsis" id="itemValue_10_{{$index}}" title="{{item.value}}"
                                                       ng-change="setParaChange();dataTypeValidCheck(['required','nameLengthValidator','validFlowPara'],'itemValue_10_'+$index,item.value,null,2048)"
                                                       ng-click="dataTypeValidCheck(['required','nameLengthValidator','validFlowPara'],'itemValue_10_'+$index,item.value,null,2048)"
                                                       ng-blur="closeShowTip()">
                                                </input>
                                            </div>
                                            <div ng-if="item.flowParameterSource === 'UPLOAD_FILE'" class="df-input-frame" cmp-input-focus>
                                                <div style="display: inline-block;" class="uploadfile-btn" ng-click="clickUploadBtn(item.name)">
                                                    <span ng-bind="'cmp.workbench.fast.script.file.select' | i18n"></span>
                                                    <div ng-style="{'width': uploadBars[item.name]}"></div>
                                                </div>
                                                <div class="df-label-tips title-tips"
                                                     style="float:right;margin-top:5px;margin-left:5px"
                                                     ng-mouseover="isFlowTips = true"
                                                     ng-mouseout="isFlowTips = false"
                                                     ng-hide="uploadFileNames[item.name]"
                                                     ng-init="isFlowTips = false">
                                                    <div class="df-label-bar"
                                                         style="background: url(/dvfoundationwebsite/images/tool.png) -45px -75px no-repeat;">
                                                    </div>
                                                    <div ng-cloak class="df-tips left"
                                                         ng-show="isFlowTips" style="width:405px">
                                                        <p class="spcial-warp" style="width:400px">
                                                            {{I18n.get('cmp.healing.flow.upload.file.tips', [typeStr, uploadFileMaxSize])}}
                                                        </p>
                                                        <div class="df-tips-arrow"></div>
                                                    </div>
                                                </div>
                                                <div style="display: inline-block;" class="uploadfile-file">
                                                    <p ng-bind="uploadFileNames[item.name]" title="{{uploadFileNames[item.name]}}" style="white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:400px"></p>
                                                    <input type="file" id="{{item.name}}"
                                                           ng-show="false"
                                                           onchange="angular.element(this).scope().uploadFile(this)" />
                                                </div>
                                            </div>
                                        </td>
                                        <td style="width: 35%;">
                                            <span title="{{item.description}}" ng-bind="item.description" style="display: inline-block; max-width:300px;text-overflow: ellipsis;"></span>
                                        </td>
                                    </tr>
                                    <tr style="overflow:visible" ng-init="item.index = $index" ng-if="item.show && (item.showStatus === 1 || item.showStatus === 3) && item.flowParamType === 'CASCADE'">
                                        <td style="overflow:visible" colspan="3">
                                            <table style="width:95%;float:right;" classify="children"
                                                   class="ng-table">
                                                <thead>
                                                <tr>
                                                    <th style="width:23%">
                                                        <div class="healing-table-header"
                                                             style="text-align: left;padding-left: 12px">
                                                                    <span
                                                                            style="border-left:0px">{{'cmp.healing.flow.flowParameter.change.child.name' | i18n}}</span>
                                                        </div>
                                                    </th>
                                                    <th>
                                                        <div class="healing-table-header"
                                                             style="text-align: left">
                                                                    <span style="border-left:0px">
                                                                        {{'cmp.healing.flow.flowParameter.value' | i18n}}
                                                                    </span>
                                                        </div>
                                                    </th>
                                                    <th style="width:30%">
                                                        <div class="healing-table-header"
                                                             style="text-align: left;padding-left: 12px">
                                                            <span style="border-left:0px">{{'cmp.healing.flow.flowParameter.description' | i18n}}</span>
                                                        </div>
                                                    </th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr
                                                        ng-repeat="param in item.paramsObj track by $index">
                                                    <td style="text-align: left;width: 200px;padding-left: 10px;">
                                                        <div class="df-input-frame" cmp-input-focus
                                                             style="padding-left: 10px;display: inherit">
                                                            <input readonly
                                                                   ng-model="param.paramName"
                                                                   id="endValue_04_{{$index}}"
                                                                   ng-change="dataTypeValidCheck(['required','alpha','nameLengthValidator'],'endValue_04_'+$index,param.paramName,null,128)"
                                                                   ng-click="dataTypeValidCheck(['required','alpha','nameLengthValidator'],'endValue_04_'+$index,param.paramName,null,128)"
                                                                   ng-blur="closeShowTip()"
                                                                   u-validator="required;alpha;maxLength(128);">
                                                            </input>
                                                        </div>
                                                    </td>
                                                    <td style="overflow: visible;">
                                                        <div class="df-input-frame" cmp-input-focus>
                                                            <div class="df-input-frame"
                                                                 cmp-input-focus>
                                                                <div
                                                                        ng-if="(param.type == 'string' || param.type == undefined || param.type == null) && item.showStatus == 1">
                                                                    <input class="df-input wide-input" autocomplete="off"
                                                                           ng-model="param.endValue"
                                                                           id="itemValue_14_{{$index}}"
                                                                           ng-change="setParaChange();dataTypeValidCheck(['required','nameLengthValidator','validFlowPara'],'itemValue_14_'+$index,param.endValue,null,8000)"
                                                                           ng-click="dataTypeValidCheck(['required','nameLengthValidator','validFlowPara'],'itemValue_14_'+$index,param.endValue,null,8000)"
                                                                           ng-blur="closeShowTip()">
                                                                    </input>
                                                                </div>
                                                                <div
                                                                        ng-if="(param.type == 'string' || param.type == undefined || param.type == null) && item.showStatus == 3">
                                                                    <input class="df-input wide-input" autocomplete="off"
                                                                           ng-model="param.endValue"
                                                                           id="itemValue_14_{{$index}}"
                                                                           ng-change="setParaChange();dataTypeValidCheck(['nameLengthValidator','validFlowPara'],'itemValue_14_'+$index,param.endValue,null,8000)"
                                                                           ng-click="dataTypeValidCheck(['nameLengthValidator','validFlowPara'],'itemValue_14_'+$index,param.endValue,null,8000)"
                                                                           ng-blur="closeShowTip()">
                                                                    </input>
                                                                </div>
                                                                <div ng-if="param.type == 'datetype'"
                                                                     id="confirmFlow_{{item.index}}_{{param.index}}">
                                                                    <div style="display: inline-block;float: left">
                                                                        <eview:date-time-picker
                                                                                id="confirmCasTime_{{item.index}}_{{param.index}}"
                                                                                type='datetime'
                                                                                width='530px'
                                                                                required='item.showStatus == 1'>
                                                                        </eview:date-time-picker>
                                                                    </div>
                                                                    <div style="margin-left:20px" class="df-btn" ng-click="selectPresentDateParam({{item.index}}, {{param.index}})">
                                                                        {{'cmp.healing.flow.para.date.now'|i18n}}
                                                                    </div>
                                                                </div>
                                                                <div
                                                                        ng-if="param.type === 'enumtype' || param.type === 'scriptType'">
                                                                    <eview:editable-select
                                                                            id="enumtype_{{item.index}}_{{param.index}}"
                                                                            width='530px'
                                                                            values="param.value"
                                                                            required='item.showStatus == 1'
                                                                            change="setParaChange"
                                                                            select-value='param.endValue'
                                                                            default-label=""
                                                                            value-field="value"
                                                                            label-field="label"
                                                                            mode="param.selectMode"
                                                                            select-all="true"
                                                                            case-insensitive-filter='true'>
                                                                    </eview:editable-select>
                                                                </div>
                                                                <div
                                                                        ng-if="param.type === 'key_value'">
                                                                    <eview:editable-select
                                                                            id="key_value_{{item.index}}_{{param.index}}"
                                                                            width='530px'
                                                                            values="param.value"
                                                                            required='item.showStatus == 1'
                                                                            change="setParaChange"
                                                                            select-value='param.endValue.value'
                                                                            default-label=""
                                                                            value-field="value"
                                                                            label-field="label"
                                                                            mode="param.selectMode"
                                                                            select-all="true"
                                                                            case-insensitive-filter='true'>
                                                                    </eview:editable-select>
                                                                </div>
                                                                <div ng-if="param.type === 'ip_tree'">
                                                                    <input class="df-input wide-input" readonly autocomplete="off"
                                                                           value="{{param.endValue}}"
                                                                           ng-model="param.endValue"
                                                                           id="itemValue_15"
                                                                           ng-change="setParaChange();"
                                                                           ng-click="popSelectIps($index, 'child', item.index);"
                                                                           ng-blur="closeShowTip()">
                                                                    </input>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td style="text-align: left;width: 300px;">
                                                        <div class="df-input-frame" cmp-input-focus
                                                             style="padding-left: 10px;display: inherit;">
                                                            <input readonly="readonly" style="width: 300px;text-overflow: ellipsis;"
                                                                   ng-model="param.description"
                                                                   title="{{param.description}}"
                                                                   id="pvalueTypeId_03_{{$index}}">
                                                            </input>
                                                        </div>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>

                                    <!-- 脚本型参数支持级联，第二层数据 -->
                                    <tr style="overflow:visible" ng-init="item.index = $index" ng-if="item.show && (item.showStatus === 1 || item.showStatus === 3) && item.flowParamType === 'SCRIPTTYPE' && showScriptCascadeRow(item)">
                                        <td style="overflow:visible" colspan="3">
                                            <table style="width:95%;float:right;" classify="children"
                                                   class="ng-table">
                                                <thead>
                                                <tr>
                                                    <th style="width:30%">
                                                        <div class="healing-table-header"
                                                             style="text-align: left;padding-left: 12px">
                                                                                                <span
                                                                                                        style="border-left:0px">{{'cmp.healing.flow.flowParameter.change.child.name' | i18n}}</span>
                                                        </div>
                                                    </th>
                                                    <th>
                                                        <div class="healing-table-header"
                                                             style="text-align: left">
                                                                                                <span style="border-left:0px">
                                                                                                    {{'cmp.healing.flow.flowParameter.value' | i18n}}
                                                                                                </span>
                                                        </div>
                                                    </th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr ng-repeat="param in item.secondValueList track by $index">
                                                    <td style="text-align: left;width: 200px;padding-left: 10px;">
                                                        <div class="df-input-frame" cmp-input-focus style="padding-left: 10px;display: inherit">
                                                            <input readonly
                                                                   ng-model="param.paramName"
                                                                   id="script_second_key_{{$index}}"
                                                                   ng-change="dataTypeValidCheck(['required','alpha','nameLengthValidator'],'script_endValue_04_'+$index,param.paramName,null,128)"
                                                                   ng-click="dataTypeValidCheck(['required','alpha','nameLengthValidator'],'script_endValue_04_'+$index,param.paramName,null,128)"
                                                                   ng-blur="closeShowTip()"
                                                                   u-validator="required;alpha;maxLength(128);"/>
                                                        </div>
                                                    </td>
                                                    <td style="overflow: visible;">
                                                        <div class="df-input-frame" cmp-input-focus>
                                                            <div class="df-input-frame" cmp-input-focus>
                                                                <div>
                                                                    <!--required='true'-->
                                                                    <eview:editable-select
                                                                            id="script_second_value_{{item.index}}_{{$index}}"
                                                                            width='530px'
                                                                            values="param.children"
                                                                            change="setScriptSecondCascadeData"
                                                                            select-value='param.value'
                                                                            default-label=""
                                                                            value-field="paramId"
                                                                            label-field="paramName"
                                                                            mode="multi"
                                                                            select-all="true"
                                                                            case-insensitive-filter='true'>
                                                                    </eview:editable-select>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>


                                </table>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
            </tbody>
        </table>
        <table id="operationTable">
            <tbody>
            <tr>
                <td class="property_name">
                </td>
                <td class="property_content">
                    <div class="df-btn-group-left" style="margin-top: 20px;">
                        <div class="df-btn stress" ng-show="editAtomScriptPageStatus != 'EDIT'" ng-click="saveTask()">
                            {{'cmp_healing_atom_script_create' | i18n}}
                        </div>
                        <div class="df-btn" ng-click="closePage()">
                            {{'cmp_healing_dashboard_confirm_cancle' | i18n}}
                        </div>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>

    </div>
</div>
</body>

</html>
