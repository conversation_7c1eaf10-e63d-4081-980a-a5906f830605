<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.huawei.i2000</groupId>
        <artifactId>dvengineeringservice-root</artifactId>
        <version>8.251.000626-SNAPSHOT</version>
    </parent>

    <artifactId>dvengineeringservice-deployment</artifactId>
    <name>DVEngineeringServiceDeployment</name>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.huawei.bsp</groupId>
            <artifactId>com.huawei.bsp.commonlib.cbb</artifactId>
        </dependency>
        <dependency>
            <groupId>com.huawei.i2000</groupId>
            <artifactId>com.huawei.i2000.kernel.updatekey</artifactId>
            <version>${dvkernel.version}</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.i2000</groupId>
            <artifactId>com.huawei.digitalview.dvkernel.commons.utils</artifactId>
            <version>${dvkernel.version}</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.i2000</groupId>
            <artifactId>dvengineeringservice-tool</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.huawei.bsp.biz</groupId>
            <artifactId>com.huawei.bsp.biz.inittool</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.huawei.bsp</groupId>
                    <artifactId>com.huawei.bsp.cloudsop-commonlib-service</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.huawei.bsp</groupId>
                    <artifactId>com.huawei.bsp.commonlib.roa.restserver</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>com.huawei.bepcloud</groupId>
                <artifactId>rebuild-maven-plugin</artifactId>
                <version>2.1.T1</version>
                <executions>
                    <execution>
                        <id>rebuild_1</id>
                        <goals>
                            <goal>rebuild</goal>
                        </goals>
                        <configuration>
                            <overwrite>true</overwrite>
                            <rules>
                                <rule>(pom.properties,#)-&gt;del</rule>
                                <rule>(MANIFEST.MF,Import-Service)-&gt;sort</rule>
                            </rules>
                        </configuration>
                    </execution>
                    <execution>
                      <id>rebuild_2</id>
                      <goals>
                        <goal>fixFileTimeAttr</goal>
                      </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
