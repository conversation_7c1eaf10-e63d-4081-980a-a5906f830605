{"fileVersion": "1", "name": "DVRubikCoordinatorService", "description": "DVRubikCoordinatorService", "requireDiskSize": 50, "rtspSoftLink": true, "upgrade": {"use": {"jre": "~1.3", "mqclient": "~1.0", "dvkernelcbb": "~1.0", "dv3rdlib": "~1.0", "dvplatformcbb": "~1.0"}, "postInstall": {"command": ["${APP_ROOT}/init/post_install.sh"], "timeout": 300}, "uninstall": {"command": ["${APP_ROOT}/init/uninstall.sh"], "timeout": 3000}, "check": {"command": ["${APP_ROOT}/upgrade/check.sh"], "timeout": 30}, "upgrade": {"command": ["${APP_ROOT}/upgrade/init.sh"], "timeout": 300}}, "processes": {"dvrubikcoordinatorservice": {"use": {"jre": "~1.3", "tomcat": "~9.0", "python": "~1.3", "cxx3rdlib": "~8.0", "mqclient": "~1.0", "dvkernelcbb": "~1.0", "dv3rdlib": "~1.0", "dvplatformcbb": "~1.0"}, "subscribes": [{"name": "DVControllerService", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "DVOmaService", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "DVEamCoreService", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "DVEamAmService", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "digitalviewappbase", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "FMAccessService", "roles": ["op_regular", "op_driver"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "SecurityMgmtWebsite", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "UserService", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "AuditService", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "LicenseService", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "InvGrpService", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "DVCertMgmtService", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "MQProxy", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "DVEamAmService", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}, {"name": "NetWorkPrivilegeService", "roles": ["op_regular"], "srv_ctx": {}, "ins_ctx": {}}], "mode": "cluster", "cpu": 0.5, "memory": 1024, "memoryLimit": 4096, "patterns": ["[roles=op_regular]/rest/dvrubikcoordinatorservice"], "healthChk": {"proto": "https", "url": "/rest/dvrubikcoordinatorservice/v1/healthcheck"}, "useAdditionalEnv": ["_APP_LOG_DIR", "_APP_TMP_DIR", "_APP_SHARE_DIR"], "protocols": {"IR": "http"}, "commands": {"start": ["${APP_ROOT}/bin/start.sh"], "stop": ["${APP_ROOT}/bin/stop.sh"], "cipherChanged": ["$APP_ROOT/bin/updateKey.sh", "$CHANGED_KEY_ALIAS"], "monitorTimeout": "60"}, "events": {"preIPChange": {"command": ["${APP_ROOT}/bin/pre_modify_app_ip.sh"], "timeout": 300}, "opconfChanged": {"command": ["${APP_ROOT}/bin/opconfchanged.sh"], "timeout": 300}}}}, "databases": {"dvrubikcoordinatorservicedb": {"type": ["mysql", "oracle", "zenith"], "serviceName": "dvrubikcoordinatorservicedb", "dataSize": 256, "logSize": 128, "sharding": false, "defaultShardingNumber": 1}}, "resources": [{"provider": "mq", "subresources": [{"name": "dv_cert_change", "options": {"topicname": "dv_cert_change", "consume": {"consumer.groupname": "i2000.kafka.group"}}}]}, {"provider": "hofs", "sources": [{"name": "dvrubikcoordinatorservicebucket", "options": {"size": 10737418240, "singleSize": 10737418240, "replication": 2, "rotate": [{"monitor_paths": "dvrubikcoordinatorservicebucket", "wrap_type": 2, "max_direction_capacity": 9782, "timer_type": 2, "timer_time": "04:00"}], "backup": {"policies": ["/config/", "/etc/", "/sftpConfig/"]}}}]}]}