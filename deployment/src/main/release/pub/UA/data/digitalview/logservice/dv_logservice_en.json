{"Import_1_title": "Configure Log Collection", "Import_1_content": "<div>Before using the log service function, you need to deliver the log collection configuration package and set the log name, storage path, and log format so that logs can be collected to the DigitalView.</div><div>If the configuration package has been imported, skip this step.</div>", "Import_1_btn1": "<PERSON><PERSON>", "Import_1_btn2": "Next", "Import_2_title": "Click <span style='font-weight: bold;'>Configuration</span>", "Import_2_content": "The page for importing a configuration package is displayed.", "Import_3_title": "Select Local Configuration Package", "Import_3_content": "Before configuring the log service, you need to obtain the log service configuration package released by the service side.", "Import_4_title": "Click <span style='font-weight: bold;'>Upload</span>", "Import_4_content": "Upload the configuration package.", "Import_5_title": "Verify Upload Status", "Import_5_content": "Verify", "Import_6_title": "Click <span style='font-weight: bold;'>OK</span>", "Import_6_content": "Failed to upload. Rectify the fault based on the exception information.", "Import_7_title": "Click <span style='font-weight: bold;'>Save and Deliver</span>", "Import_7_content": "Deliver the log collection information to the service NE and wait for the delivery result.", "Import_8_title": "Enter Password", "Import_8_content": "Enter the login password.", "Import_8_error": "Incorrect password. Please try again.", "Import_8_error2": "The password is empty.", "Import_9_title": "Click <span style='font-weight: bold;'>Confirm</span>", "Import_9_content": "Verify the login user password.", "Import_10_title": "Verify Authentication Result", "Import_10_content": "Verify", "Import_11_title": "Wait For Configuration Delivery", "Import_11_content": "Wait until the configuration delivery is complete and click Next.", "Import_12_title": "Check Collection Status", "Import_12_content": "If the status is <span style='font-weight: bold;'>Delivered</span>, the log collection information has been delivered to the service side.", "Import_13_title": "Click <span style='font-weight: bold;'>Status Management</span>", "Import_13_content": "Check the collection status of different collection log types of each collection NE.", "Import_14_title": "Check Collection Status", "Import_14_content": "If the collection status is abnormal, check the status of the abnormal NE node and deliver the configuration again.", "Import_15_title": "Complete to Import Package", "Import_15_content": "You have successfully imported the configuration package. You can continue to configure the search template.", "Import_16_title": "Select Configuration Signature File", "Import_16_content": "Select the signature file corresponding to the configuration package.", "Template_1_title": "Configure Search Template", "Template_1_content": "<div>To improve the search efficiency, search keywords and time segments can be configured in a template for the log search.</div>", "Template_1_tip": "Before configuring a template, ensure that the configuration package has been imported. Otherwise, the template cannot be created.", "Template_1_btn": "Import Configuration Package", "Template_2_title": "Click <span style='font-weight: bold;'>Template Management</span>", "Template_2_content": "The page for log search is displayed.", "Template_3_title": "Wait For Page to Load", "Template_4_title": "Click <span style='font-weight: bold;'>OK</span>", "Template_4_content": "Failed to search. Rectify the fault based on the exception information.", "Template_5_title": "Back to Template Management", "Template_5_content": "The template configuration page is displayed.", "Template_6_title": "Click <span style='font-weight: bold;'>Create Template</span>", "Template_6_content": "The page for creating a template is displayed.", "Template_7_title": "Configure Template Name", "Template_7_content": "For Example: Template_Log", "Template_7_error1": "The name contains special characters, please fill it in again.", "Template_8_title": "Configure <span style='font-weight: bold;'>SolutionType</span>", "Template_8_content": "Select the solution corresponding to the search template.", "Template_8_error": "Please select a solution.", "Template_9_title": "Select Solution", "Template_9_content": "Select the solution name preset in the imported configuration package.", "Template_10_title": "Configure <span style='font-weight: bold;'>Data source</span>", "Template_10_content": "Select the log type corresponding to the search template.", "Template_10_error": "Please select a data source.", "Template_11_title": "Select Log Type", "Template_11_content": "Select the data source type preset in the imported configuration package.", "Template_12_title": "Configure <span style='font-weight: bold;'>Date and time field</span>", "Template_12_content": "Select the time field type corresponding to the search template.", "Template_12_error": "Please select a time field.", "Template_13_title": "Select Time Type", "Template_13_content": "For example: Time indicates that logs generated within a specified period can be queried.", "Template_14_title": "Configure Search Field", "Template_14_content": "Set the Field Alias and whether to use the field as the query condition. ", "Template_15_title": "Click <span style='font-weight: bold;'>Save</span>", "Template_15_content": "Save the template.", "Template_16_title": "Verify Saving Status", "Template_17_title": "Click <span style='font-weight: bold;'>OK</span>", "Template_17_content": "Failed to save. Rectify the fault based on the exception information.", "Template_18_title": "No Configuration Package", "Template_18_tip": "Before configuring a template, ensure that the configuration package has been imported. Otherwise, the template cannot be created.", "Template_18_btn": "Import Configuration Package", "Template_19_title": "Complete to Create Template", "Template_19_content": "You have successfully created the log search template. You can continue to search for logs.", "Search_1_title": "Search Log", "Search_1_content": "Search for collection logs based on the fields configured in the template. The search results are displayed in a table. You can view log details, and configure charts and alarms based on the search results. ", "Search_1_btn": "Configure Search Template", "Search_2_title": "Click <span style='font-weight: bold;'>Template Management</span>", "Search_2_content": "The page for log search is displayed.", "Search_3_title": "Wait For Page to Load", "Search_4_title": "No Template", "Search_4_content": "Before searching for logs, ensure that the configuration package has been imported and the search template has been configured.", "Search_4_btn": "Create Template", "Search_5_title": "Click <span style='font-weight: bold;'>OK</span>", "Search_5_content": "Failed to search. Rectify the fault based on the exception information.", "Search_6_title": "Back to Template Management", "Search_6_content": "The template configuration page is displayed.", "Search_7_title": "wait", "Search_7_content": "wait", "Search_8_title": "Click <span style='font-weight: bold;'>Search by Template</span>", "Search_8_content": "Select the row where the template is located and search for logs using the template.", "Search_9_title": "Select Time Range", "Search_9_content": "Query logs within a specified time range.", "Search_10_title": "Select <span style='font-weight: bold;'>24hours</span>", "Search_10_content": "Set a long time range to ensure that logs can be found.", "Search_11_title": "Click <span style='font-weight: bold;'>Search</span>", "Search_11_content": "Search for logs.", "Search_12_title": "Verify Test Status", "Search_12_content": "Verify", "Search_13_title": "Click <span style='font-weight: bold;'>OK</span>", "Search_13_content": "Failed to search. Rectify the fault based on the exception information.", "Search_14_title": "Expand Optional Fields ", "Search_14_content": "Expand all fields configured in the log template.", "Search_14_tip": "If no log is found, no preview data may be displayed during chart and alarm creation. This exception can be ignored only for operation experience. ", "Search_15_title": "Select Log Display Fields ", "Search_15_content": "When you point to a field, the plus sign (+) or hyphen (-) is displayed on the right of the field, indicating that the field can be displayed or not displayed.", "Search_16_title": "Optional field dialog box verification", "Search_17_title": "Close Window", "Search_17_content": "Confirm the optional fields to be added.", "Search_18_title": "Close Window", "Search_18_content": "Confirm the optional fields to be added.", "Search_19_title": "Click Expand Button", "Search_19_content": "The log details are displayed.", "Search_20_title": "View Log Details", "Search_20_content": "View the details of the log fields.", "Search_21_title": "Complete to Search Log", "Search_21_content": "You have successfully completed the log search. You can continue to configure the chart.", "Chart_1_title": "Configure Chart", "Chart_1_content": "After log search is complete, if the log data volume is large, you can configure charts to display search results in charts, facilitating further statistics and analysis. ", "Chart_1_tip": "Before configuring charts, ensure that the configuration package has been imported and the search template has been configured.", "Chart_1_btn": "Configure Search Template", "Chart_2_title": "Click <span style='font-weight: bold;'>Template Management</span>", "Chart_2_content": "The page for log search is displayed.", "Chart_3_title": "Wait For Page to Load", "Chart_4_title": "No Template", "Chart_4_content": "Before searching for logs, ensure that the configuration package has been imported and the search template has been configured.", "Chart_4_btn": "Create Template", "Chart_5_title": "Click <span style='font-weight: bold;'>OK</span>", "Chart_5_content": "Failed to search. Rectify the fault based on the exception information.", "Chart_6_title": "Back to Template Management", "Chart_6_content": "The template configuration page is displayed.", "Chart_7_title": "wait", "Chart_7_content": "wait", "Chart_8_title": "Click <span style='font-weight: bold;'>Search by Template</span>", "Chart_8_content": "Select the row where the template is located and search for logs using the template.", "Chart_9_title": "Select Time Range", "Chart_9_content": "Query logs within a specified time range.", "Chart_10_title": "Select <span style='font-weight: bold;'>24hours</span>", "Chart_10_content": "Set a long time range to ensure that logs can be found.", "Chart_11_title": "Click <span style='font-weight: bold;'>Search</span>", "Chart_11_content": "Search for logs.", "Chart_12_title": "Verify Test Status", "Chart_12_content": "Verify", "Chart_13_title": "Click <span style='font-weight: bold;'>OK</span>", "Chart_13_content": "Failed to search. Rectify the fault based on the exception information.", "Chart_14_title": "Click <span style='font-weight: bold;'>Configure Chart</span>", "Chart_14_content": "<div>You can view log statistics in a table or line chart.</div>", "Chart_14_tip": "If no log is found, no preview data may be displayed during chart creation. This exception can be ignored only for operation experience.", "Chart_15_title": "Select Chart Type", "Chart_15_content": "Select <span style='font-weight: bold;'>Table</span> or <span style='font-weight: bold;'>Line Chart</span>.", "Chart_15_btn1": "Create Table", "Chart_15_btn2": "Create Line Chart", "Chart_16_title": "Configure Table Name", "Chart_16_content": "Enter a table name, for example, Table_01.", "Chart_16_error1": "The name is empty, please fill it in again.", "Chart_16_error2": "The name contains special characters, please fill it in again.", "Chart_17_title": "Set Table Parameters ", "Chart_17_content": "<div>The following describes how to configure the top 5 IP addresses with the largest number of statistical logs.</div><ul><li>Set <span style='font-weight: bold;'>Indicator Calculation Type</span> to <span style='font-weight: bold;'>COUNT</span>, indicates the total number of logs in a data classification. </li><li>Set <span style='font-weight: bold;'>Data Classification Type</span> to <span style='font-weight: bold;'>By top N</span>.</li><li>Set <span style='font-weight: bold;'>Field</span> to <span style='font-weight: bold;'>ModuleIp</span>.</li><li>Set <span style='font-weight: bold;'>Sorting rule</span> to <span style='font-weight: bold;'>Descending</span>.</li><li>Set <span style='font-weight: bold;'>Number of obtained results</span> to <span style='font-weight: bold;'>5</span>.</li><li>Set <span style='font-weight: bold;'>Sorting reference</span> to <span style='font-weight: bold;'>COUNT</span>.</li></ul>", "Chart_17_error1": "Incorrect parameter settings, please fill it in again.", "Chart_17_error2": "Please set <span style='font-weight: bold;'>Data Classification Type</span> to <span style='font-weight: bold;'>By top N</span>.", "Chart_17_error3": "The Field cannot be empty, please fill it in again.", "Chart_17_error4": "The Sorting rule cannot be empty, please fill it in again.", "Chart_17_error5": "The Number of obtained results cannot be empty, please fill it in again.", "Chart_17_error6": "The Number of obtained results is not a positive integer, please fill it in again.", "Chart_17_error7": "The Sorting reference cannot be empty, please fill it in again.", "Chart_17_condition1": "Select", "Chart_17_condition2": "By top N", "Chart_18_title": "Click <span style='font-weight: bold;'>Preview</span>", "Chart_18_content": "Preview the table.", "Chart_19_title": "View Preview Result", "Chart_19_content": "The table shows the top 5 IP addresses with the largest total number of logs.", "Chart_20_title": "Click <span style='font-weight: bold;'>Save</span>", "Chart_20_content": "The chart configuration is complete.", "Chart_21_title": "Verify Saving Status", "Chart_22_title": "Configure Chart Name", "Chart_22_content": "Enter a line chart name, for example, Line_01.", "Chart_22_error1": "The name is empty, please fill it in again.", "Chart_22_error2": "The name contains special characters, please fill it in again.", "Chart_23_title": "Set Chart Parameters ", "Chart_23_content": "<div>The following describes how to collect statistics on the total number of logs every 10 minutes.</div><ul><li>Set <span style='font-weight: bold;'>Indicator Calculation Type</span> to <span style='font-weight: bold;'>COUNT</span>, indicates the total number of logs in a data classification. </li><li>Set <span style='font-weight: bold;'>Data Classification Type</span> to <span style='font-weight: bold;'>By time</span>.</li><li>Set <span style='font-weight: bold;'>Field</span> to <span style='font-weight: bold;'>Time</span>.</li><li>Set <span style='font-weight: bold;'>Interval</span> to <span style='font-weight: bold;'>10</span>.</li><li>Set <span style='font-weight: bold;'>Interval Unit</span> to <span style='font-weight: bold;'>Minute</span>.</li></ul>", "Chart_23_error1": "Incorrect parameter settings, please fill it in again.", "Chart_23_error2": "The Field cannot be empty, please fill it in again.", "Chart_23_error3": "The Interval cannot be empty, please fill it in again.", "Chart_23_error4": "The Interval is not a positive integer, please fill it in again.", "Chart_23_error5": "The Interval Unit cannot be empty, please fill it in again.", "Chart_24_title": "Click <span style='font-weight: bold;'>Preview</span>", "Chart_24_content": "Preview the line chart.", "Chart_25_title": "View Preview Result", "Chart_25_content": "The line chart of the total number of logs generated every 10 minutes is displayed.", "Chart_26_title": "Click <span style='font-weight: bold;'>Save</span>", "Chart_26_content": "The chart configuration is complete.", "Chart_27_title": "Verify Saving Status", "Chart_28_title": "Click <span style='font-weight: bold;'>OK</span>", "Chart_28_content": "Failed to save. Rectify the fault based on the exception information.", "Chart_29_title": "Complete to Configure Chart", "Chart_29_content": "You have successfully configured the chart. You can continue to configure the dashboard.", "Dashboard_1_title": "Configure Dashboard", "Dashboard_1_content": "A dashboard displays the charts configured in the system. ", "Dashboard_1_tip": "Before configuring the dashboard, ensure that the charts have been configured.", "Dashboard_1_btn": "Configure Chart", "Dashboard_2_title": "Click <span style='font-weight: bold;'>Dashboard</span>", "Dashboard_2_content": "The page for dashboard Configuration is displayed.", "Dashboard_3_title": "Verify Test Status", "Dashboard_3_content": "Verify", "Dashboard_4_title": "Click <span style='font-weight: bold;'>OK</span>", "Dashboard_4_content": "System error. Rectify the fault based on the exception information.", "Dashboard_5_title": "Click <span style='font-weight: bold;'>Create Dashboard</span>", "Dashboard_5_content": "Create a dashboard and select a configured chart.", "Dashboard_6_title": "Configure Dashboard", "Dashboard_6_content": "Set the panel name and select the created chart. Example: Dashboard_001.", "Dashboard_6_error1": "The name is empty, please fill it in again.", "Dashboard_6_error2": "The name contains special characters. Please enter another one.", "Dashboard_7_title": "Click <span style='font-weight: bold;'>Save</span>", "Dashboard_7_content": "Save the dashboard.", "Dashboard_8_title": "Verify Test Status", "Dashboard_8_content": "Verify", "Dashboard_9_title": "Click <span style='font-weight: bold;'>OK</span>", "Dashboard_9_content": "Failed to save. Rectify the fault based on the exception information.", "Dashboard_10_title": "Click <span style='font-weight: bold;'>OK</span>", "Dashboard_10_content": "Failed to save. Rectify the fault based on the exception information.", "Dashboard_11_title": "View Dashboard", "Dashboard_11_content": "View the dashboard display result.", "Dashboard_12_title": "Complete to Create Dashboard", "Dashboard_12_content": "You have successfully configured the dashboard. You can continue to configure alarms.", "Alarm_1_title": "Configure Alarm", "Alarm_1_content": "Alarm triggering mode and condition can be configured for logs with the specified data source. When the alarm triggering mode and condition are met, an alarm can be reported to the <span style='font-weight: bold;'>Current Alarms</span> page of the system monitor.", "Alarm_1_tip": "Before configuring alarms, ensure that the configuration package has been imported and the search template has been configured.", "Alarm_1_btn": "Configure Search Template", "Alarm_2_title": "Click <span style='font-weight: bold;'>Template Management</span>", "Alarm_2_content": "The page for log search is displayed.", "Alarm_3_title": "Wait For Page to Load", "Alarm_4_title": "No Template", "Alarm_4_content": "Before searching for logs, ensure that the configuration package has been imported and the search template has been configured.", "Alarm_4_btn": "Create Template", "Alarm_5_title": "Click <span style='font-weight: bold;'>OK</span>", "Alarm_5_content": "Failed to search. Rectify the fault based on the exception information.", "Alarm_6_title": "Back to Template Management", "Alarm_6_content": "The template configuration page is displayed.", "Alarm_7_title": "wait", "Alarm_7_content": "wait", "Alarm_8_title": "Click <span style='font-weight: bold;'>Search by Template</span>", "Alarm_8_content": "Select the row where the template is located and search for logs using the template.", "Alarm_9_title": "Select Time Range", "Alarm_9_content": "Query logs within a specified time range.", "Alarm_10_title": "Select <span style='font-weight: bold;'>24hours</span>", "Alarm_10_content": "Set a long time range to ensure that logs can be found.", "Alarm_11_title": "Click <span style='font-weight: bold;'>Search</span>", "Alarm_11_content": "Search for logs.", "Alarm_12_title": "Verify Test Status", "Alarm_12_content": "Verify", "Alarm_13_title": "Click <span style='font-weight: bold;'>OK</span>", "Alarm_13_content": "Failed to search. Rectify the fault based on the exception information.", "Alarm_14_title": "Click <span style='font-weight: bold;'>Configure Alarm</span>", "Alarm_14_content": "The page for alarm Configuration is displayed.", "Alarm_14_tip": "If no log is found, no preview data may be displayed during alarm creation. This exception can be ignored only for operation experience.", "Alarm_15_title": "Configure Alarm Information", "Alarm_15_content": "<div>For example, if the total number of events in a cluster exceeds 10,000, the system reports a warning alarm named Too Many Logs.</div><ul><li>Set the <span style='font-weight: bold;'>Alarm Name</span> to <span style='font-weight: bold;'>Too Many Logs</span>.</li><li>Set the <span style='font-weight: bold;'>Alarm Level</span> to <span style='font-weight: bold;'>Warning</span>.</li><li>Set the <span style='font-weight: bold;'>Additional info</span> to alarm handling procedures to locate and handle alarms.</li><li>Set the <span style='font-weight: bold;'>Monitoring granularity</span> to <span style='font-weight: bold;'>Cluster</span>.</li><li>Set the <span style='font-weight: bold;'>Trigger mode interval</span> to <span style='font-weight: bold;'>10 minutes</span>. </li><li>Set the <span style='font-weight: bold;'>Trigger type</span> to <span style='font-weight: bold;'>Event count-based alarm</span>.</li><li>Set the <span style='font-weight: bold;'>Trigger rule</span> to <span style='font-weight: bold;'>COUNT&gt;10000</span>.</li></ul>", "Alarm_15_error1": "The name cannot be empty, please fill it in again.", "Alarm_15_error2": "The name contains special characters, please fill it in again.", "Alarm_15_error3": "The alarm level cannot be empty.", "Alarm_15_error4": "The triggering mode cannot be empty.", "Alarm_15_error5": "The value of Interval cannot exceed 2 characters, please fill it in again.", "Alarm_15_error6": "The Interval is not a positive integer, please fill it in again.", "Alarm_15_error7": "The interval cannot exceed 24 hours, please fill it in again.", "Alarm_15_error8": "The interval cannot exceed 60 minutes, please fill it in again.", "Alarm_15_error9": "The triggering condition type cannot be empty.", "Alarm_15_error10": "The triggering rule cannot be empty.", "Alarm_15_error11": "The value of triggering rule is not 0 or a positive integer, please fill it in again.", "Alarm_15_condition1": "Hour", "Alarm_15_condition2": "Minute", "Alarm_16_title": "Click <span style='font-weight: bold;'>Preview</span>", "Alarm_16_content": "Preview the calculation result of the trigger condition.", "Alarm_17_title": "Preview Calculation Result ", "Alarm_17_content": "Check whether the alarm triggering conditions are proper based on the calculation result. If not, return to to modify the conditions. ", "Alarm_18_title": "Click <span style='font-weight: bold;'>Save</span>", "Alarm_18_content": "Save the alarm settings.", "Alarm_19_title": "Verify Saving Status", "Alarm_20_title": "Click <span style='font-weight: bold;'>OK</span>", "Alarm_20_content": "Failed to save. Rectify the fault based on the exception information.", "Alarm_21_title": "Click <span style='font-weight: bold;'>Alarm</span>", "Alarm_21_content": "The page for viewing alarms is displayed.", "Alarm_22_title": "View Alarm Information", "Alarm_22_content": "View the alarm setting result and check whether the alarm status is <span style='font-weight: bold;'>Running</span>. ", "Alarm_23_title": "Complete to Configure Alarm", "Alarm_23_content": "You have successfully configured configuring alarms. You can continue to monitor the collection status.", "Monitor_1_title": "Monitor Collection Status", "Monitor_1_content": "When the data collection configuration is successfully delivered, you can view the collection rate in real time.", "Monitor_2_title": "Click <span style='font-weight: bold;'>Monitor</span>", "Monitor_2_content": "The page for monitoring is displayed.", "Monitor_3_title": "Select Solution", "Monitor_3_content": "View the collection status of each solution.", "Monitor_4_title": "Select Status Type", "Monitor_4_content": "View the plug-in status details. ", "Monitor_5_title": "Select NE type", "Monitor_5_content": "Select an NE type in the navigation tree. ", "Monitor_6_title": "Click <span style='font-weight: bold;'>Search</span>", "Monitor_6_content": "Query the node information in the selected status.", "Monitor_7_title": "View Node List", "Monitor_7_content": "If the node status is <span style='font-weight: bold;'>Warn</span> or <span style='font-weight: bold;'>Error</span>, the possible causes are listed in the <span style='font-weight: bold;'>Description</span> column.", "Monitor_8_title": "You Have Finished the Experience", "Monitor_8_content": "You have successfully configured the log service. You can try other experience tasks.", "Monitor_9_title": "Failed to query the monitoring status", "Monitor_9_tip": "Monitoring exception. Check whether the collection status is normal.", "wizard_config_1_wizard_name": "Log Service", "wizard_config_1_0_name": "1.Import Configuration Package", "wizard_config_1_1_name": "2.Configure Search Template", "wizard_config_1_2_name": "3.<PERSON><PERSON>", "wizard_config_1_3_name": "4.Configure Chart", "wizard_config_1_4_name": "5.Configure Dashboard", "wizard_config_1_5_name": "6.<PERSON>figu<PERSON>", "wizard_config_1_6_name": "7.<PERSON> Plug-in Status", "end": "Finish"}