#!/bin/bash
#
# Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
#

# log archive
function scroll_log()
{
    local LOG_FILE="$1"
    [ ! -f ${LOG_FILE} ] && return 0
    log_size=$(du -b ${LOG_FILE} | awk '{print $1}')
    LOG_DIR=$(dirname ${LOG_FILE})
    LOG_NAME=$(basename ${LOG_FILE})
    logname_prefix=$(basename -s .log ${LOG_NAME})
    if [ ${log_size} -gt 10240000 ]; then
        cd ${LOG_DIR}

        log_zip_num=$(ls -v |grep "${logname_prefix}"|grep ".zip"|wc -l)
        if [ ${log_zip_num} -ge 5 ];then
            log_zip_del=$(ls -vrt |grep "${logname_prefix}"|grep ".zip"|head -1)
            rm -f ${log_zip_del}
        fi

        zip -q ${logname_prefix}_$(date +'%Y%m%d%H%M%S').zip ${LOG_NAME}
        chmod 400 ${logname_prefix}*.zip
        echo "" > ${LOG_NAME}
        cd -
    fi
}

function dv_log()
{
    local log_type="$1"
    local log_msg="$2"
    local log_time=$(date '+%Y-%m-%d %H:%M:%S')
    local LOG_FILE="$3"
    if [ -n "$3" ];then
        if [ ! -f ${LOG_FILE} ];then
            touch ${LOG_FILE}
            chmod 640 ${LOG_FILE}
        fi
    echo "${log_time} [${log_type}] ${log_msg}" >> ${LOG_FILE}
    fi
}