#!/bin/bash

source ${APP_ROOT}/bin/internal/function.sh

#check user
CUR_PATH=$(cd `dirname $0`;pwd)
SCRIPT_PATH=$0
IPMC_USER="`stat -c '%U' ${SCRIPT_PATH}`"
export IPMC_USER
CURRENT_USER="`/usr/bin/id -u -n`"
if [ "${IPMC_USER}" != "${CURRENT_USER}" ]
then
    warn "only ${IPMC_USER} can execute this script."
    exit 1
fi

source ${APP_ROOT}/bin/internal/utils.sh
STOP_LOG_FILE="${_APP_LOG_DIR}/${APP_NAME}_Stop.log"

scroll_log "${STOP_LOG_FILE}"

complete_process_name=${PROCESS_NAME}-${NODE_ID}-${PROCESS_SLOT}
dv_log "INFO" "Begin to stop ${complete_process_name}, pid:$PID" "${STOP_LOG_FILE}"

kill -9 $PID

sleep 1
for i in $(seq 1 3);do
    isExist=$(ps -ef | grep -vw grep | grep ${complete_process_name} |grep -w $PID)
    if [[ -n "${isExist}" ]];then
        dv_log "WARN" "${complete_process_name} is still exist, try to kill again ${i}." "${STOP_LOG_FILE}"
        kill -9 $PID
        sleep 3
    fi
    break
done
dv_log "INFO" "End to stop ${complete_process_name}." "${STOP_LOG_FILE}"

result=0;$CUR_PATH/../../../../manager/agent/tools/shscript/syslogutils.sh "$(basename $0)" "$result" "Execute($#):$CUR_PATH/$0 $@";exit $result
