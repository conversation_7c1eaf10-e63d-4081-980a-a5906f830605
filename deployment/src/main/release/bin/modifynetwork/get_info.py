# -*- coding:utf-8 -*-
import os
import os.path
import sys
import time
import json
from json import JSONDecodeError

SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
LOG_FILE = os.fdopen(os.open(os.path.join(SCRIPT_DIR, 'modifyip.log'), os.O_RDWR | os.O_CREAT, 0o755), 'w+')
JSON_FILE_PATH = sys.argv[1]
INFO_TO_GET = sys.argv[2]

def verify_input(input_string):
    if input_string.startswith('='):
        return '\t' + input_string
    return input_string


def log_echo(log_type="INFO", log_str=""):
    time_str = str(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()))
    try:
        if LOG_FILE:
            LOG_FILE.write(verify_input("["+log_type+"]["+time_str+"]:"+log_str+"\n"))
    except(FileNotFoundError, IOError) as exp:
        log_echo("ERROR", "write " + str(os.path.join(SCRIPT_DIR, 'modifyip.log')) + " failed: %s" % str(exp))
        LOG_FILE.close()
        print("ERROR")
        sys.exit(-1)


def read_json(file_name):
    try:
        file_name = os.path.realpath(file_name)
        with open(file_name) as file:
            json_obj = json.load(file)
    except(FileNotFoundError, IOError, JSONDecodeError) as error:
        log_echo("ERROR", "Read " + file_name + " failed: %s" % str(error))
        print("ERROR")
        sys.exit(-1)
    return json_obj


def map_has_key(json_map, json_key):
    if json_key in json_map.keys():
        return True

    return False


def get_new_info():
    old_ip = sys.argv[3]
    json_load = read_json(JSON_FILE_PATH)
    new_info = ""

    if map_has_key(json_load, "datalist"):
        for node_info in json_load["datalist"]:
            for net_info in node_info["networkinfo"]:
                if net_info["oldip"] == old_ip:
                    new_info = net_info[INFO_TO_GET]
                    break

    if new_info:
        print(new_info)
    else:
        log_echo("ERROR", "new_info: " + INFO_TO_GET + " is null for " + old_ip)
        print("ERROR")


if __name__ == '__main__':
    try:
        if INFO_TO_GET == "newip":
            get_new_info()
    finally:
        LOG_FILE.close()
