#!/bin/bash
set +x
source /home/<USER>/.bashrc
ip_cmd=$(which ip)
if [ "X${ip_cmd}" == "X" ];then
    ip_cmd="/usr/sbin/ip"
fi

function scroll_log()
{
    log_size=$(du -b ${INFO_LOG_FILE} | awk '{print $1}')
    LOG_DIR=$(dirname ${INFO_LOG_FILE})
    LOG_NAME=$(basename ${INFO_LOG_FILE})
    logname_prefix=$(basename -s .log ${LOG_NAME})
    if [ ${log_size} -gt 20480000 ]; then
        cd ${LOG_DIR}
        
        log_zip_num=$(ls -v |grep "${logname_prefix}"|grep ".zip"|wc -l)
        if [ ${log_zip_num} -ge 5 ];then
            log_zip_del=$(ls -vrt |grep "${logname_prefix}"|grep ".zip"|head -1)
            rm -f ${log_zip_del}
        fi

        zip -q ${logname_prefix}_$(date +'%Y%m%d%H%M%S').zip ${LOG_NAME}
        chmod 400 ${logname_prefix}*.zip
        echo "" > ${LOG_NAME}
        cd -
    fi
}

function log_echo()
{
    log_level=INFO
    if [ $# -ge 2 ]; then
        log_level=$1
        shift
    fi
    log_level=$(echo ${log_level} | tr 'a-z' 'A-Z')
    echo "$(date +'%Y-%m-%d %H:%M:%S') [${log_level}] $*" >> $INFO_LOG_FILE
    if [ "X${log_level}" == "XINFO" ]; then
        echo -e "\033[49;32m[INFO]\033[0m $*" 
    elif [ "X${log_level}" == "XWARN" ]; then
        echo -e "\033[49;33m[WARN]\033[0m $*" 
    elif [ "X${log_level}" == "XERROR" ]; then
        echo -e "\033[49;31m[ERROR]\033[0m $*" 
    else
        echo -e "\033[49;31m${log_level}\033[0m $*"
    fi
}

function die()
{
    local t=$(date '+%Y-%m-%d %H:%M:%S') 
    echo "$t [ERROR] $*"
    echo "$t [ERROR] $*" >>$INFO_LOG_FILE
    exit 1
}

function check_exist_core_binding()
{
    grep -w "^is_integrated_df_and_service" ${APP_ROOT}/envs/env.properties | grep "Yes" >/dev/null 2>&1
    if [ $? -ne 0 ];then
        return 1
    fi

    return 0
}

function core_binding_for_server_list()
{
    local cpu_core="$1"
    
    local server_list=$(cat ${APP_ROOT}/etc/core_bind_service_list.cfg)
    
    for server in ${server_list}
    do
        local server_pid=$(ps -ef | grep ${server} | grep java | grep -v grep | awk -F ' ' '{print $2}')
        if [ -n "${server_pid}" ];then
            log_echo "INFO" "bind ${server} pid ${server_pid} to cpu core $cpu_core"
            taskset -cpa $cpu_core ${server_pid} >/dev/null 2>&1
        fi
    done

}



function core_binding()
{
    local bind_command="$1"

    check_exist_core_binding
    if [ $? -ne 0 ];then
        if [ "X${bind_command}" == "Xbind" ];then
            core_binding_for_server_list "0"
        else
            processor_num=`expr $(cat /proc/cpuinfo | grep processor | wc -l) - 1`
            core_binding_for_server_list "0-$processor_num"
        fi
    fi
}

function killProcessUsingPath()
{
    sudo ${HOME}/sudoScripts/kill_installpath.sh
}

function getLocalAccessIP()
{
    ip_type=$1
    APP_JSON_FILE="${APP_ROOT}/etc/sysconf/nodelist.json"
    accessIP=$(${APP_ROOT}/rtsp/python/bin/python ${APP_ROOT}/bin/tools/getInfo.py "${APP_JSON_FILE}" "accessIP" "${ip_type}")
    echo ${accessIP}
}

function getLocalAccessAndAccess2IP()
{
    ip_type=$1
    APP_JSON_FILE="${APP_ROOT}/etc/sysconf/nodelist.json"
    access_access2=$(${APP_ROOT}/rtsp/python/bin/python ${APP_ROOT}/bin/tools/getInfo.py "${APP_JSON_FILE}" "access_access2" "${ip_type}")
    echo ${access_access2}
}

function getAccessExternalIP()
{
    ip_type=$1
    APP_JSON_FILE="${APP_ROOT}/etc/sysconf/nodelist.json"
    local floating_base_ip=$(${APP_ROOT}/rtsp/python/bin/python ${APP_ROOT}/bin/tools/getInfo.py "${APP_JSON_FILE}" "access-external" "${ip_type}")
    echo ${floating_base_ip}
}

function change_I2000_IP()
{
    log_echo "INFO" "Start to modify IP in i2000 config."
    
    oldip=$1
    newip=$2
    is_disater=$3
    i2k_plat_install_path=$_APP_SHARE_DIR
    
    if [ -f ${i2k_plat_install_path}/I2000/run/tools/changeip/I2000_IPModify.sh ]; then
        grep "is_disaster" ${i2k_plat_install_path}/I2000/run/tools/changeip/script/I2000_changeip.sh > /dev/null 2>&1
        if [ $? -eq 0 ];then
            ${i2k_plat_install_path}/I2000/run/tools/changeip/I2000_IPModify.sh $oldip $newip $is_disater >> ${INFO_LOG_FILE} 2>&1
        else
            ${i2k_plat_install_path}/I2000/run/tools/changeip/I2000_IPModify.sh $oldip $newip >> ${INFO_LOG_FILE} 2>&1
        fi
        if [ $? -ne 0 ];then
            die "modify i2000 ip failed!"
        fi
    fi
    
    echo ${oldip} |grep ":" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        tmp_oldip=$(echo ${oldip} | sed 's/:/\\\\\\:/g')
        tmp_newip=$(echo ${newip} | sed 's/:/\\\\\\:/g')
        sed -i "s/\<$tmp_oldip\>/$tmp_newip/g" ${i2k_plat_install_path}/I2000/run/uninstall/etc/config/DefaultValues.properties > /dev/null 2>&1
        sed -i "s/\<$tmp_oldip\>/$tmp_newip/g" ${i2k_plat_install_path}/I2000/run/uninstall/data/InstallStorage.properties > /dev/null 2>&1
    fi

    log_echo "INFO" "Modify i2000 config successfully."
}


function change_cie_ip()
{
    log_echo "INFO" "Start to modify IP in cie config."
    
    oldip=$1
    newip=$2
    i2k_plat_install_path=$_APP_SHARE_DIR
    oldip=$(echo ${oldip}|sed "s/\./\\\./g")
    
    sed -i "s/$oldip/$newip/g" ${i2k_plat_install_path}/cie/dmu/config/dmu.properties  >> $INFO_LOG_FILE 2>&1
    sed -i "s/$oldip/$newip/g" ${i2k_plat_install_path}/cie/mq/conf/mq.cfg  >> $INFO_LOG_FILE 2>&1

    log_echo "INFO" "Modify cie config successfully."
}

function modifyipv4_for_dualstack()
{
    APP_JSON_FILE="${APP_ROOT}/etc/sysconf/nodelist.json"
    grep -w "access2" ${APP_JSON_FILE} > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "It's dual stack, need to modify ipv4 ip."
        local_accessIP_ipv4=$(getLocalAccessIP "access_ipv4")
        if [ "${local_accessIP_ipv4}" == "ERROR" ];then
            log_echo "ERROR" "Get local_accessIP_ipv4 ERROR."
            exit 1
        fi
        peer_accessIPipv4=$(grep 'param name="ip"' ${_APP_SHARE_DIR}/I2000/run/etc/oms.xml | sed "s#<param name=\"ip\">\(.*\)</param>#\1#g"|sed 's/ //g'|sort|uniq)
        
        change_I2000_IP ${peer_accessIPipv4} ${local_accessIP_ipv4} >>$INFO_LOG_FILE 2>&1
        change_cie_ip ${peer_accessIPipv4} ${local_accessIP_ipv4} >>$INFO_LOG_FILE 2>&1
    fi
}

function check_float_ip()
{
    local is_exist=0
    APP_FLOAT_IP=$(getLocalAccessIP)
    for app_ip in ${APP_FLOAT_IP}
    do
        app_ip_tmp=$(echo "${app_ip}"|sed "s/\./\\\./g")
        ${ip_cmd} addr | grep -w "${app_ip_tmp}" > /dev/null 2>&1
        if [ $? -ne 0 ]; then
            is_exist=1
            break
        fi
    done
    return  ${is_exist}
}

function get_peer_ip()
{   
    peer_ip=""
    local peer_ip_tmp=""
    maintenance_list=$(${APP_ROOT}/rtsp/python/bin/python ${APP_ROOT}/bin/tools/getInfo.py "${APP_ROOT}/etc/sysconf/nodelist.json" "maintenance_list" "")
    for tmp_ip in ${maintenance_list};do
        peer_ip_tmp=$(echo "${tmp_ip}"|sed "s/\./\\\./g")
        ${ip_cmd} addr | grep -w "${peer_ip_tmp}" > /dev/null 2>&1
        if [ $? -ne 0 ]; then
            peer_ip=${tmp_ip}
            break
        fi
    done
}

function check_ip()
{
    if [ ! -f "/opt/oss/manager/apps/HyperHAAgent/bin/hactl" ];then
        log_echo "INFO" "not dual mode , not need to check ip."
        return 0
    fi

    log_echo "INFO" "Check APP float IP exits or not."
   
    check_float_ip
    if [ $? -ne 0 ];then
        die "Float IP of APP ${app_ip} is not local"
    fi
    
    log_echo "INFO" "Check database IP reachable."
    APP_JSON_FILE="${APP_ROOT}/etc/sysconf/${APP_NAME}*.json"
    DB_SERVICE_IP=`cat ${APP_JSON_FILE} | grep accessHost | awk -F'[""]' '{print $4}' | sed 's/ //g' |head -1`
    ping_result="No"
    source_ip=${APP_FLOAT_IP}
    if [ -z "`echo ${DB_SERVICE_IP}|grep :`" ];then
        ping_cmd="ping"
    else
        ping_cmd="ping6"
        for app_ip in ${APP_FLOAT_IP}
        do
            if [ -n "`echo ${app_ip}|grep :`" ];then
                source_ip=${app_ip}
                break
            fi
        done
    fi

    i=0
    fail_time=0
    while [ $i -lt 5 ]
    do
        if [ "X${DB_SERVICE_IP}" == "X127.0.0.1" -o "X${DB_SERVICE_IP}" == "X::1" ];then
            log_echo "INFO" "not need to check 127.0.0.1 or ::1 ."
            return 0
        else
            source_ip=$(${APP_ROOT}/rtsp/python/bin/python ${APP_ROOT}/bin/tools/getInfo.py "${APP_ROOT}/etc/sysconf/nodelist.json" "maintenance_list" "")
            ${ping_cmd} -I ${source_ip} -c 4 ${DB_SERVICE_IP} > /dev/null 2>&1
        fi
        if [ $? -eq 0 ];then
            ping_result="Yes"
            break
        else
            ping_result="No"
            ((fail_time=fail_time+1))
        fi
        ((i=i+1))
        sleep 3
    done
    
    if [ "X${ping_result}" == "XNo" -a ${fail_time} -eq 5 ];then
        die "Can't ping to ${DB_SERVICE_IP}, please check"
    else
        log_echo "INFO" "Database IP is reachable."
    fi
}

function test_ssh()
{
    expect -c "set timeout -1;
                spawn ssh -o ServerAliveInterval=60 -o StrictHostKeyChecking=no $2 ;
                expect {
                    *assword:* {send -- $1\r;
                                 expect {
                                    *denied* {exit 2;}
                                    *word:* {exit 3;}
                                    eof {catch wait result;exit [lindex \${result} 3]}
                                 }
                    }
                    eof {exit 0;}
                }
                "
    return $?
}


function simplify_ipv6_ip()
{
    local ipv6_ip=$1
    
    echo ${ipv6_ip} | grep ":0" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        tmp_ip=$(echo ${ipv6_ip}|sed "s/:0/:/g")
        while true
        do
            tmp_ip=$(echo ${tmp_ip}|sed "s/:0/:/g")
            echo ${tmp_ip} | grep ":::" > /dev/null 2>&1
            if [ $? -eq 0 ];then
                tmp_ip=$(echo ${tmp_ip}|sed "s/:::/::/g")
            fi
            
            echo ${tmp_ip} | grep -E ":0|:::" > /dev/null 2>&1
            if [ $? -ne 0 ];then
                break
            fi
        done
        echo ${tmp_ip}
    else
        echo ${ipv6_ip}
    fi
}

function ssh_local_exc_cmd()
{
    local cmd="$1"
    if [ -z "${cmd}" ];then
        log_echo "ERROR" "exc ssh_local_exc_cmd cmd=${cmd} is null."  | tee -a $INFO_LOG_FILE
        exit 1
    fi
    
    local primary_app_ip=""
    local secondary_app_ip=""
    local i2k_service_ip=$(cat ${APP_ROOT}/envs/upgrade.properties |grep -w "^HOSTING_SERVER_IP" |awk -F'=' '{print $2}')
    
    if [ -n "`ls /dev/drbd0`" ];then
        primary_app_ip=$(cat ${APP_ROOT}/envs/upgrade.properties |grep -w "^HOSTING_SERVER_IP" |awk -F'=' '{print $2}')
        secondary_app_ip=$(cat ${APP_ROOT}/envs/upgrade.properties |grep -w "^StandbyIPAddress" |awk -F'=' '{print $2}')
    fi
    
    local localhost=$(hostname)
    ## single
    if [ -z "${primary_app_ip}" -a -z "${secondary_app_ip}" ];then
        localhost="${i2k_service_ip}"
    else
        ## dual 
        local ipList="${primary_app_ip} ${secondary_app_ip}"
        local isIPV6=""
        local simplify_ipv6=""
        
        for tmpIp in ${ipList};do
            isIPV6=$(echo "${tmpIp}" |grep ":")
            if [ "X${isIPV6}" != "X" ];then
                tmpIp=$(echo "${tmpIp}" | tr '[A-Z]' '[a-z]')
                ifconfig |grep -w "${tmpIp}" >/dev/null 2>&1
                if [ $? -ne 0 ];then
                    simplify_ipv6=$(simplify_ipv6_ip "${tmpIp}")
                    ## Uppercase to lowercase
                    tmpIp=$(echo "${simplify_ipv6}" | tr '[A-Z]' '[a-z]')
                fi
            fi
            
            tmpIp_tmp=$(echo "${tmpIp}"|sed "s/\./\\\./g")
            ifconfig |grep -w "${tmpIp_tmp}" >/dev/null 2>&1
            if [ $? -eq 0 ];then
                localhost="${tmpIp}"
                break;
            fi
        done
    fi
    
    devdata_user=$(grep "^SFTP_SERVICE_USER=.*" ${_APP_SHARE_DIR}/icnfg/webpages/WEB-INF/config/config.properties |awk -F'=' '{print $2}')
    if [ -z "${devdata_user}" ];then
        devdata_user="devdata"
    fi
    
    ssh -o ServerAliveInterval=60  -o StrictHostKeyChecking=no ${devdata_user}@${localhost} "$cmd" >> $INFO_LOG_FILE 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "exc cmd[ssh -o ServerAliveInterval=60  -o StrictHostKeyChecking=no ${devdata_user}@${localhost} \"$cmd\"] failed.ipList=${ipList}"  | tee -a $INFO_LOG_FILE
        exit 1
    fi
}

function copyCerTodevdata()
{
    product_path=$(echo $_APP_SHARE_DIR  | sed "s#share/##g" | sed "s#/DVEngineeringService##g")
    log_echo "INFO" "Start to copy server.cer to devdata ...."
    if [ "X${product_path}" != "X" ];then
        log_echo "INFO" "The product_path is not null."
        v8CloudsopI2kCer=${product_path}/etc/ssl/internal/server.cer
        devdataTargetCer=${_APP_SHARE_DIR}/icnfg/webpages/var/data/necert/i2000.cer
        
        if [ -f ${v8CloudsopI2kCer} ];then
            log_echo "INFO" "The v8CloudsopI2kCer=${v8CloudsopI2kCer} is exist."
            
            chmod 750 ${product_path}/etc/ssl/internal
            chmod_dir=$?
            
            chmod 640 ${v8CloudsopI2kCer}
            chmod_cer=$?
            
            devdata_user=$(grep "^SFTP_SERVICE_USER=.*" ${_APP_SHARE_DIR}/icnfg/webpages/WEB-INF/config/config.properties |awk -F'=' '{print $2}')
            cp -rf ${v8CloudsopI2kCer} ${devdataTargetCer}
            cp_cer=$?
            
            if [ ${chmod_dir} -ne 0 -o ${chmod_cer} -ne 0 -o ${cp_cer} -ne 0 ];then
                log_echo "ERROR" "The rule_exec_user=$(whoami) cmd [ chmod 750 ${product_path}/etc/ssl/internal;chmod 640 ${v8CloudsopI2kCer};cp -rf ${v8CloudsopI2kCer} ${devdataTargetCer} ] execute failed.chmod_dir=${chmod_dir};chmod_cer=${chmod_cer};cp_cer=${cp_cer};"
            fi
            
            chmod 700 ${product_path}/etc/ssl/internal
            chmod_dir=$?
            
            chmod 600 ${v8CloudsopI2kCer}
            chmod_cer=$?
            
            if [ ${chmod_dir} -ne 0 -o ${chmod_cer} -ne 0 ];then
                log_echo "ERROR" "The rule_exec_user=$(whoami) cmd [ chmod 700 ${product_path}/etc/ssl/internal;chmod 600 ${v8CloudsopI2kCer};] execute failed.chmod_dir=${chmod_dir};chmod_cer=${chmod_cer};"
            fi
            
            log_echo "INFO" "The copy file ${v8CloudsopI2kCer} to ${devdataTargetCer} successfully!" 
        else
            log_echo "ERROR" "The v8CloudsopI2kCer=${v8CloudsopI2kCer} is not exist!" 
        fi
    else
        log_echo "ERROR" "The product_path=${product_path} is null!"
    fi
}

function start_process()
{
    log_echo "INFO" "Begin to start app process."
    export COMPLETE_PROCESS_NAME=$PROCESS_NAME-$NODE_ID-$PROCESS_SLOT

    if [ -z "`ps -ef |grep "DNFW=$PROCESS_NAME"|grep -v grep`" ];then
        sh $APP_ROOT/bin/process_holder.sh -DNFW=${COMPLETE_PROCESS_NAME} &
    else
        log_echo "INFO" "App process has been started"
    fi
    core_binding "unbind"  
}

function restart_sshd_sftp(){
    ls /opt/oss/*/apps/HyperHAAgent/ >> /dev/null
    if [ $? -eq 0  ];then
        ## restart sshd
        if [ -f "/.dockerenv" ];then
            sudo /home/<USER>/sudoScripts/restart_sshd_in_docker.sh
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Execute the cmd [sudo /home/<USER>/sudoScripts/restart_sshd_in_docker.sh] failed."
            fi
        else
            log_echo "INFO" "Begin to restart sshd"
            sudo /sbin/service sshd restart
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Execute the cmd [sudo /sbin/service sshd restart] failed."
            fi
            log_echo "INFO" "Begin to restart sftp"
            if [ -f /etc/ssh/sftpd_config ];then
                sudo /sbin/service sftpd restart
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "Execute the cmd [sudo /sbin/service sftpd restart] failed."
                fi
            fi
        fi    
    fi
}
function prehandleBeforeStart()
{
    echo ${I2000_USER_HOME}|grep "ossuser" > /dev/null 2>&1
    if [ $? -ne 0 ];then
        I2000_USER_HOME=/home/<USER>
    fi
    product_path=$(echo $_APP_SHARE_DIR  | sed "s#share/##g" | sed "s#/DVEngineeringService##g")
    if [ -f "$_APP_SHARE_DIR/I2000/run/etc/primary_dual_key" ];then
        dv_engineering_path=${product_path}/apps/${APP_NAME}

        rm -f ${CURRENT_PATH}/primary_stop_tag
        if [ -f ${I2000_USER_HOME}/primary.tag ];then
            rest_ip=$(cat $_APP_SHARE_DIR/I2000/run/etc/primary_dual_key | grep rest_mgr_ip |  awk -F '=' '{print $2}') 
        else
            rest_ip=$(cat $_APP_SHARE_DIR/I2000/run/etc/secondary_dual_key | grep rest_mgr_ip |  awk -F '=' '{print $2}') 
        fi

        sed -i "s|\"host\": \".*\"|\"host\": \"${rest_ip}\"|g" ${APP_ROOT}/etc/conf/restclient.json

        [ -z "`grep "dv_profile.sh" ${_APP_SHARE_DIR}/I2000/run/lbin/start.sh`" ] && sed -i "/export HEDEX_LANGUAGE/a\source $dv_engineering_path/bin/dv_profile.sh" ${_APP_SHARE_DIR}/I2000/run/lbin/start.sh
        [ -z "`grep "RUNTIME_CENTER_PATH=" ${_APP_SHARE_DIR}/I2000/run/lbin/start.sh`" ] && sed -i "/dv_profile.sh/a\export RUNTIME_CENTER_PATH=${dv_engineering_path}" ${_APP_SHARE_DIR}/I2000/run/lbin/start.sh

        [ -z "`grep "dv_profile.sh" ${_APP_SHARE_DIR}/I2000/mttools/bin/start-sysmon.sh`" ] && sed -i "/export HEDEX_LANGUAGE/a\[ -f $dv_engineering_path/bin/dv_profile.sh ] && source $dv_engineering_path/bin/dv_profile.sh " ${_APP_SHARE_DIR}/I2000/mttools/bin/start-sysmon.sh
        [ -z "`grep "RUNTIME_CENTER_PATH" ${_APP_SHARE_DIR}/I2000/mttools/bin/start-sysmon.sh`" ] && sed -i "/dv_profile.sh/a\export RUNTIME_CENTER_PATH=${dv_engineering_path}" ${_APP_SHARE_DIR}/I2000/mttools/bin/start-sysmon.sh
        
        secondary_ret=1
        secondary_ip=$(cat ${APP_ROOT}/envs/upgrade.properties |grep -w "^StandbyIPAddress" |awk -F'=' '{print $2}')
        if [ "X${secondary_ip}" == "X" ];then
            log_echo "ERROR" "The secondary_ip=${secondary_ip} is null."
        else
            secondary_ip_tmp=$(echo "${secondary_ip}"|sed "s/\./\\\./g")
            ${ip_cmd} addr | grep -w "${secondary_ip_tmp}" > /dev/null 2>&1
            secondary_ret=$?
        fi
        
        if [ ${secondary_ret} -eq 0 ]; then
            log_echo "INFO" "this secondary node.to do build trust relationship of ossuser."
            need_to_build_trust=0
            oss_user=$(getfacl ${_APP_SHARE_DIR}/I2000 2>/dev/null|awk '{if($0~"owner:[ ]+"){a=$NF}else if($0~"group:[ ]+"){b=$NF}}END{print a}')
            oss_user_home=""
            if [ "X${oss_user}" == "X" ];then
                log_echo "ERROR" "The oss_user=${oss_user} is null."
            else
                id ${oss_user}
                if [ $? -eq 0 ];then
                    log_echo "INFO" "The oss_user=${oss_user} is exist.get user home."
                    oss_user_home=$(cat /etc/passwd|grep -w ${oss_user} |awk -F':' '{print $6}')
                else
                    log_echo "ERROR" "The oss_user=${oss_user} is not exist."
                fi
            fi
            
            if [ -f ${oss_user_home}/.ssh/authorized_keys ];then
                log_echo "INFO" "The ${oss_user_home}/.ssh/authorized_keys is exist."
                test_ssh "${oss_user}" "${oss_user}@${secondary_ip} echo ssh_ok"
                if [ $? -eq 0 ];then
                    log_echo "INFO" "${oss_user}@${secondary_ip} already has build trust relationship."
                    need_to_build_trust=1
                fi
            fi
            
            if [ ${need_to_build_trust} -eq 0 ];then
                log_echo "INFO" "The need_to_build_trust=${need_to_build_trust} to do build trust relationship of ossuser."
                cat ${oss_user_home}/.ssh/id_rsa.pub >> ${oss_user_home}/.ssh/authorized_keys
                if [ $? -ne 0 ];then
                    log_echo "ERROR" "The user=$(whoami) execute cmd [cp -f ${oss_user_home}/.ssh/id_rsa.pub ${oss_user_home}/.ssh/authorized_keys] failed."
                fi
                
                chmod 600 ${oss_user_home}/.ssh/authorized_keys
                
                test_ssh "${oss_user}" "${oss_user}@${secondary_ip} echo ssh_ok"
                test_ssh_ret=$?
                if [ ${test_ssh_ret} -ne 0 ];then
                    log_echo "ERROR" "secondary node need to do build trust relationship of (ossuser) users failed.test_ssh_ret=${test_ssh_ret}"
                else
                    ssh -o ServerAliveInterval=60 -o StrictHostKeyChecking=no ${oss_user}@${secondary_ip} "echo test"
                    log_echo "INFO" "build trust relationship of ossuser successfully."
                fi
                
            else
                log_echo "INFO" "The need_to_build_trust=${need_to_build_trust} not need to build trust relationship of ossuser."
            fi
        else
            log_echo "INFO" "this not secondary node.not need to build trust relationship of ossuser."
        fi
            
        ## Refresh iCnfig config          
        icnfg_config_path=${_APP_SHARE_DIR}/icnfg/webpages/WEB-INF/config/config.properties
        roa_inst_xml=${_APP_SHARE_DIR}/I2000/run/etc/iemp.framework/roa.inst.xml
        if [ -f ${icnfg_config_path} -a -f ${roa_inst_xml} ];then
            log_echo "INFO" "Refresh iCnfig config..."
            roa_inst_ip_tmp=$(cat ${roa_inst_xml} |grep "name=\"ip\""|head -1|awk -F'value=' '{print $2}' |awk -F' ' '{print $1}' |awk -F'[""]' '{print $2}')
            roa_inst_port=$(cat ${roa_inst_xml} |grep "name=\"port\""|head -1|awk -F'value=' '{print $2}' |awk -F' ' '{print $1}' |awk -F'[""]' '{print $2}')
            
            roa_inst_ip=${roa_inst_ip_tmp}
            isIPV6=$(echo "${roa_inst_ip_tmp}" |grep ":")
            if [ "X${isIPV6}" != "X" ];then
                roa_inst_ip="[${roa_inst_ip_tmp}]"
            fi
            
            ## EAM_Service_URL = https://{ip}:{port}/eamadapter/services/NEM/ 
            sed -i "s|^EAM_Service_URL[ ]*=.*|EAM_Service_URL =  https://${roa_inst_ip}:${roa_inst_port}/eamadapter/services/NEM/|g" ${icnfg_config_path}
            
            ## UM_Service_URL = https://{ip}:{port}/smadapter/services/SMInterfaceExp
            sed -i "s|^UM_Service_URL[ ]*=.*|UM_Service_URL =  https://${roa_inst_ip}:${roa_inst_port}/smadapter/services/SMInterfaceExp|g" ${icnfg_config_path}
            
            log_echo "INFO" "Refresh iCnfig config finished."
        fi
        
        if [ -d ${_APP_SHARE_DIR}/icnfg/webpages ];then
            copyCerTodevdata
            
            #build trust relationship of (ossuser,devdata) users
            log_echo "INFO" "Begin to execute build_trust_relation" 
            log_echo "INFO" "The rule_exec_user=$(whoami)."
            ## primary node is install time to do build trust relationship of (ossuser,devdata) users .
            ## secondary node need to do build trust relationship of (ossuser,devdata) users . 
            secondary_ret=1
            if [ "X${secondary_ip}" == "X" ];then
                log_echo "ERROR" "The secondary_ip=${secondary_ip} is null."
            else
                secondary_ip_tmp=$(echo "${secondary_ip}"|sed "s/\./\\\./g")
                ${ip_cmd} addr | grep -w "${secondary_ip_tmp}" > /dev/null 2>&1
                secondary_ret=$?
            fi
        
            if [ ${secondary_ret} -eq 0 ]; then
                log_echo "INFO" "this is secondary node."
                oss_user=$(getfacl ${_APP_SHARE_DIR}/I2000 2>/dev/null|awk '{if($0~"owner:[ ]+"){a=$NF}else if($0~"group:[ ]+"){b=$NF}}END{print a}')
                oss_user_home=$(cat /etc/passwd|grep -w ${oss_user} |awk -F':' '{print $6}')
                devdata_home=$(cat /etc/passwd|grep -w ${devdata_user} |awk -F':' '{print $6}')
                need_to_build_trust=0
                
                chmod 750 ${oss_user_home}/.ssh
                chmod 640 ${oss_user_home}/.ssh/id_rsa.pub
                
                ssh_local_exc_cmd "chmod 750 ${devdata_home}/.ssh"
                ssh_local_exc_cmd "chmod 640 ${devdata_home}/.ssh/authorized_keys"
                ## chek authorized_keys is exist
                if [ -f ${devdata_home}/.ssh/authorized_keys ];then
                    log_echo "INFO" "The ${devdata_home}/.ssh/authorized_keys is exist.not need to build trust relationship."
                    test_ssh "${devdata_user}" "${devdata_user}@${secondary_ip} echo ssh_ok"
                    if [ $? -eq 0 ];then
                        log_echo "INFO" "${devdata_user}@${secondary_ip} already has build trust relationship."
                        need_to_build_trust=1
                    fi
                fi
                
                if [ ${need_to_build_trust} -eq 0 ];then
                    log_echo "INFO" "start build trust relationship of (ossuser,devdata) users[${devdata_user}@${secondary_ip}]."
                    ssh_local_exc_cmd "mkdir -p ${devdata_home}/.ssh"
                    if [ -f ${devdata_home}/.ssh/authorized_keys ];then
                        ssh_local_exc_cmd "chmod 660 ${devdata_home}/.ssh/authorized_keys"
                        cat ${oss_user_home}/.ssh/id_rsa.pub  >> ${devdata_home}/.ssh/authorized_keys
                    else
                        ssh_local_exc_cmd "cp -f ${oss_user_home}/.ssh/id_rsa.pub  ${devdata_home}/.ssh/authorized_keys"
                    fi
                    
                    test_ssh "${devdata_user}" "${devdata_user}@${secondary_ip} echo ssh_ok"
                    test_ssh_ret=$?
                    if [ ${test_ssh_ret} -ne 0 ];then
                        log_echo "ERROR" "secondary node need to do build trust relationship of (ossuser,devdata) users failed.test_ssh_ret=${test_ssh_ret}"
                    else
                        ssh -o ServerAliveInterval=60 -o StrictHostKeyChecking=no ${devdata_user}@${secondary_ip} "echo test"
                        log_echo "INFO" "secondary node build trust relationship of (ossuser,devdata) users successfully."
                    fi
                fi
                
                ssh_local_exc_cmd "chmod 700 ${devdata_home}/.ssh"
                ssh_local_exc_cmd "chmod 600 ${devdata_home}/.ssh/authorized_keys"
                chmod 700 ${oss_user_home}/.ssh
                chmod 600 ${oss_user_home}/.ssh/id_rsa.pub
            else
                log_echo "INFO" "this not secondary node.not need to build trust relationship of (ossuser,devdata) users"
            fi
        fi
        
        
    fi
    
    dv_register_path="${product_path}/apps/DVRegisterService"
        
    common_agent_https_port=$(grep -A 20 "digitalviewappbase-" $dv_register_path/etc/sysconf/DVRegisterService-*.json | grep -A 3 "local" | grep port |awk -F':' '{print $2}' | sed 's|^[\t| ]*||g' |  sed "s|,$||g")
    
    [ ! -z $common_agent_https_port ] && sed -i "s|<property name=\"port\" value=\".*\" />|<property name=\"port\" value=\"${common_agent_https_port}\" />|g" "${_APP_SHARE_DIR}/I2000/run/etc/iemp.framework/roa.inst.xml"
    
    app_agent_https_port=$(grep -A 20 "digitalviewi2000appagent-" $dv_register_path/etc/sysconf/DVRegisterService-*.json | grep -A 3 "local" | grep port |awk -F':' '{print $2}' | sed 's|^[\t| ]*||g' |  sed "s|,$||g")

    [ ! -z $app_agent_https_port ] && sed -i "s|<property name=\"port\" value=\".*\" />|<property name=\"port\" value=\"${app_agent_https_port}\" />|g" "${_APP_SHARE_DIR}/I2000/run/etc/iemp.framework/roa.inst.app.xml"
    
    nodejs_httpsclient_port=$(grep -A 20 "digitalviewnodejs-" $dv_register_path/etc/sysconf/DVRegisterService-*.json | grep -A 3 "local" | grep port |awk -F':' '{print $2}' | sed 's|^[\t| ]*||g' |  sed "s|,$||g")
    
    [ ! -z $nodejs_httpsclient_port ] && sed -i "s|nodejs.httpsClient=.*|nodejs.httpsClient=$nodejs_httpsclient_port|g" "${_APP_SHARE_DIR}/I2000/run/etc/iemp.framework/roa.properties"

    sysmonitor_https_port=$(grep -A 20 "sysmonitor-" $dv_register_path/etc/sysconf/DVRegisterService-*.json | grep -A 3 "local" | grep port |awk -F':' '{print $2}' | sed 's|^[\t| ]*||g' |  sed "s|,$||g")
    [ ! -z $sysmonitor_https_port ] && sed -i "s|<property name=\"port\" value=\".*\" */>|<property name=\"port\" value=\"${sysmonitor_https_port}\" />|g" ${_APP_SHARE_DIR}/I2000/mttools/etc/iemp.framework/roa.inst.xml

    restart_sshd_sftp
}

function modify_db_port()
{
    current_db_port=$(cat ${_APP_SHARE_DIR}/I2000/run/etc/conf/sysconfigure.xml|grep serverPort|awk -F'>' '{print $2}'|awk -F'<' '{print $1}'|sed 's/ //g')
    
    real_db_port=$(cat ${APP_ROOT}/etc/sysconf/DVEngineeringService*.json|grep accessPort|uniq|awk -F':' '{print $2}'|sed 's/[ ,]//g')
    if [ "${current_db_port}" == "${real_db_port}" ];then
        log_echo "INFO" "No need to modify_db_port."
        return 0
    fi
    
    cd ${_APP_SHARE_DIR}
    find I2000/run/etc I2000/run/hotdeploy I2000/run/tools I2000/run/uninstall I2000/mttools/etc -type f \( -name '*.xml' -o -name '*.properties' -o -name '*.cfg' -o -name '*.conf' \) | xargs sed -i 's/\<'$current_db_port'\>/'$real_db_port'/g' 2>/dev/null
    sed -i 's/\<'$current_db_port'\>/'$real_db_port'/g' icnfg/webpages/WEB-INF/config/db/jdbc.properties
    sed -i 's/\<'$current_db_port'\>/'$real_db_port'/g' cie/dmu/config/jdbc.properties
    cd -
}

function modify_db_IP()
{
    current_db_ip=$(cat ${_APP_SHARE_DIR}/cie/dmu/config/jdbc.properties|awk -F '@' '/jdbc.url/{print $2}'|sed -r 's#(.*):.*#\1#g')
    
    real_db_ip=$(cat ${APP_ROOT}/etc/sysconf/DVEngineeringService*.json|grep accessHost|uniq|awk -F'"' '{print $(NF-1)}'|sed 's/ //g')
    
    log_echo "INFO" "start modify_db_IP.current_db_ip=${current_db_ip} real_db_ip=${real_db_ip}"
    
    if [ "${current_db_ip}" == "${real_db_ip}" ];then
        log_echo "INFO" "No need to modify_db_IP."
        return 0
    fi

    cd ${_APP_SHARE_DIR}
    find I2000/run/etc I2000/run/hotdeploy I2000/run/tools I2000/run/uninstall I2000/mttools/etc -type f \( -name '*.xml' -o -name '*.properties' -o -name '*.cfg' -o -name '*.conf' \) | xargs sed -i 's/\<'$current_db_ip'\>/'$real_db_ip'/g' 2>/dev/null
    sed -i 's/\<'$current_db_ip'\>/'$real_db_ip'/g' icnfg/webpages/WEB-INF/config/db/jdbc.properties
    sed -i 's/\<'$current_db_ip'\>/'$real_db_ip'/g' cie/dmu/config/jdbc.properties
    
    echo "${current_db_ip}"|grep ":" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        current_db_ip_tmp=$(cat ${_APP_SHARE_DIR}/I2000/run/etc/oms.core/jdbc.properties |grep "^bme.url="|head -1|awk -F'=' '{print $2}'|awk -F'[][]' '{print $2}'|sed 's/ //g')
        if [ -z "${current_db_ip_tmp}" ];then
            cd -
            log_echo "INFO" "The current_db_ip_tmp=${current_db_ip_tmp} is null."
            return 0
        fi
        
        if [ "${current_db_ip_tmp}" == "${real_db_ip}" ];then
            cd -
            log_echo "INFO" "current_db_ip_tmp=${current_db_ip_tmp} and real_db_ip=${real_db_ip} is same. No need to modify_db_IP."
            return 0
        fi
        
        log_echo "INFO" "modify db ip.current_db_ip_tmp=${current_db_ip_tmp} real_db_ip=${real_db_ip}"
        find I2000/run/etc I2000/run/hotdeploy I2000/run/tools I2000/run/uninstall I2000/mttools/etc -type f \( -name '*.xml' -o -name '*.properties' -o -name '*.cfg' -o -name '*.conf' \) | xargs sed -i 's/\<'$current_db_ip_tmp'\>/'$real_db_ip'/g' 2>/dev/null
        sed -i 's/\<'$current_db_ip_tmp'\>/'$real_db_ip'/g' icnfg/webpages/WEB-INF/config/db/jdbc.properties
        sed -i 's/\<'$current_db_ip_tmp'\>/'$real_db_ip'/g' cie/dmu/config/jdbc.properties
    fi
    
    cd -
}

function modify_ip()
{
    local peer_accessIP=$(echo "$1"|sed "s/ //g" |tr 'A-Z' 'a-z')
    local local_accessIP=$(echo "$2"|sed "s/ //g" |tr 'A-Z' 'a-z')
    
    log_echo "INFO" "modify_ip start..."
    log_echo "INFO" "peer_accessIP=${peer_accessIP} local_accessIP=${local_accessIP}"
    if [ "X${peer_accessIP}" == "X${local_accessIP}"  ];then
        log_echo "INFO" "The value of peer_accessIP is the same as that of local_accessIP. The IP address has been changed, skip this step."
        return 0
    fi
    
    if [ "X${peer_accessIP}" != "X" -a "X${local_accessIP}" != "X" ];then
        log_echo "INFO" "change_I2000_IP ${peer_accessIP} ${local_accessIP} YES"
        if [ -f ${_APP_SHARE_DIR}/I2000/run/etc/oms.core/jdbc.properties ];then
            tmp_old_iplist=$(grep ".url=" ${_APP_SHARE_DIR}/I2000/run/etc/oms.core/jdbc.properties |grep -v "^[[:blank:]]*#"|sed 's#.*\[\(.*\)\].*#\1#g' |uniq |grep ":")
        fi
        
        check_old_ip_list="${peer_accessIP}"
        ##execute cmd chek ipv6.${HOME}/python/bin/python ${CUR_PATH}/check_ipv6_addresses.py  "ip1" "ip2" ${APP_ROOT}/bin/tools/getInfo.py
        if [ ! -z "${tmp_old_iplist}" ];then
            log_echo "INFO" "The tmp_old_iplist=${tmp_old_iplist}"
            for jdbc_old_ip in ${tmp_old_iplist};
            do
                ## ret is YES = is same.
                check_ip_ret=$(${APP_ROOT}/rtsp/python/bin/python ${APP_ROOT}/bin/check_ipv6_addresses.py "${jdbc_old_ip}" "${peer_accessIP}")
                ## is null = is not exist.
                check_exist=$(echo "${check_old_ip_list}" |grep -wF "${jdbc_old_ip}" )
                if [ "${check_ip_ret}" == "YES" -a -z "${check_exist}" ];then
                    log_echo "INFO" "add jdbc_old_ip=${jdbc_old_ip}"
                    check_old_ip_list="${check_old_ip_list} ${jdbc_old_ip}"
                fi
            done
        fi
        
        log_echo "INFO" "The check_old_ip_list=${check_old_ip_list}"
        for i2k_old_ip in ${check_old_ip_list};
        do
            log_echo "INFO" "change_I2000_IP ${i2k_old_ip} ${local_accessIP} YES"
            change_I2000_IP "${i2k_old_ip}" "${local_accessIP}" "YES" >>$INFO_LOG_FILE 2>&1
            if [ $? -ne 0 ];then
                log_echo "ERROR" "Find standby_site, change IP failed."
                exit 1
            fi
        done
        
        change_cie_ip ${peer_accessIP} ${local_accessIP} >>$INFO_LOG_FILE 2>&1
    fi
    log_echo "INFO" "modify_ip End"
}

function modify_oms_multiplane_nbi_iplist() {
    log_echo "INFO" "start modify_oms_multiplane_nbi_iplist..."
    local floating_base_ip=$(getAccessExternalIP)
    if [ -n "${floating_base_ip}"  ]; then
        log_echo "INFO" "modify oms.multiplane.nbi.iplist="${floating_base_ip}" to ${_APP_SHARE_DIR}/I2000/run/etc/oms.core/multiplane.properties."
        float_ipv4=$(echo ${floating_base_ip[@]} | sed "s/\[//g; s/\]//g; s/'//g; s/ //g")
        grep "oms.multiplane.nbi.iplist=" ${_APP_SHARE_DIR}/I2000/run/etc/oms.core/multiplane.properties
        if [ $? -ne 0 ];then
            echo "oms.multiplane.nbi.iplist=${float_ipv4}" >> ${_APP_SHARE_DIR}/I2000/run/etc/oms.core/multiplane.properties
        else
            sed -i "s#oms.multiplane.nbi.iplist=.*#oms.multiplane.nbi.iplist=${float_ipv4}#g" ${_APP_SHARE_DIR}/I2000/run/etc/oms.core/multiplane.properties
        fi
    else
        log_echo "INFO" "getAccessExternalIP in null, no need to modify ${_APP_SHARE_DIR}/I2000/run/etc/oms.core/multiplane.properties."
    fi


    log_echo "INFO" "end to modify_oms_multiplane_nbi_iplist."
}

function modify_ip_port()
{
    log_echo "INFO" "start modify_ip_port..."
    if [ ! -f "${_APP_SHARE_DIR}/I2000/run/tools/changeip/I2000_IPModify.sh" ];then
        log_echo "ERROR" "Find standby_site, but can't find ${_APP_SHARE_DIR}/I2000/run/tools/changeip/I2000_IPModify.sh."
        exit 1
    fi
    local med_svc_xml="${_APP_SHARE_DIR}/I2000/run/sysagent/etc/sysconf/svcbase/med_node_99_svc.xml"
    local oms_xml="${_APP_SHARE_DIR}/I2000/run/etc/oms.xml"
    
    grep "IPMode" ${oms_xml}|grep "ipall" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        peer_accessIP_med=$(grep 'param name="MedUniqueIPV6"' ${med_svc_xml} | sed "s#<param name=\"MedUniqueIPV6\">\(.*\)</param>#\1#g"|sed 's/ //g'|sort|uniq)
        peer_accessIP_med_ipv4=$(grep 'param name="MedUniqueIP"' ${med_svc_xml} | sed "s#<param name=\"MedUniqueIP\">\(.*\)</param>#\1#g"|sed 's/ //g'|sort|uniq)
        
        peer_accessIP_oms=$(grep 'param name="ipv6"' ${oms_xml} | sed "s#<param name=\"ipv6\">\(.*\)</param>#\1#g"|sed 's/ //g'|sort|uniq)
        peer_accessIP_oms_ipv4=$(grep 'param name="ip"' ${oms_xml} | sed "s#<param name=\"ip\">\(.*\)</param>#\1#g"|sed 's/ //g'|sort|uniq)
        
    else
        peer_accessIP_med=$(grep 'param name="MedUniqueIP"' ${med_svc_xml} | sed "s#<param name=\"MedUniqueIP\">\(.*\)</param>#\1#g"|sed 's/ //g'|sort|uniq)
        ###针对ipall切换至ipv6，再回切场景，MedUniqueIP被切换至ipv6时置空，导致回切时获取的peer_accessIP_med为空，需要获取对应IPV6的IP
        if  [ "X${peer_accessIP_med}" == "X" ];then
            peer_accessIP_med=$(grep 'param name="MedUniqueIPV6"' ${med_svc_xml} | sed "s#<param name=\"MedUniqueIPV6\">\(.*\)</param>#\1#g"|sed 's/ //g'|sort|uniq)
        fi
        peer_accessIP_med_ipv4=""
        
        peer_accessIP_oms=$(grep 'param name="ip"' ${oms_xml} | sed "s#<param name=\"ip\">\(.*\)</param>#\1#g"|sed 's/ //g'|sort|uniq)
        ###针对ipall切换至ipv6，再回切场景，ip在切换至ipv6时置空，导致回切时通过ip获取peer_accessIP_med为空，需要获取对应IPV6的IP
        if  [ "X${peer_accessIP_med}" == "X" ];then
            peer_accessIP_oms=$(grep 'param name="ipv6"' ${oms_xml} | sed "s#<param name=\"ipv6\">\(.*\)</param>#\1#g"|sed 's/ //g'|sort|uniq)
        fi
        peer_accessIP_oms_ipv4=""
    fi
    
    if [ "X${peer_accessIP_med_ipv4}" != "X" -o "X${peer_accessIP_oms_ipv4}" != "X" ];then
        if [ "X$ipType" == "X2" ]; then
            log_echo "INFO" "It's dual stack, need to modify ipv4 ip."
            local_accessIP_ipv4=$(getLocalAccessIP "access_ipv4")
            if [ "${local_accessIP_ipv4}" == "ERROR" ];then
                log_echo "ERROR" "Get local_accessIP_ipv4 ERROR for start.sh."
                exit 1
            fi
        elif [ "X$ipType" == "X1" ]; then
            log_echo "INFO" "It's ipv6 single stack, need to change to ipv6 ip"
            touch ${_APP_SHARE_DIR}/I2000/type2Totype1.tag
            sed -i 's/ipall</ipv6</g' ${_APP_SHARE_DIR}/I2000/mttools/etc/oms.xml
            sed -i 's/ipall</ipv6</g' ${_APP_SHARE_DIR}/I2000/run/etc/oms.xml
            local_accessIP_ipv4="${local_accessIP}"
        else
            log_echo "ERROR" "It's ipv4 single stack, switch is not supported"
            exit 1
        fi
    fi
    
    cd ${CURRENT_PATH}
    
    modify_ip "${peer_accessIP_med}" "${local_accessIP}"
    modify_ip "${peer_accessIP_med_ipv4}" "${local_accessIP_ipv4}"
    if [ ${peer_accessIP_med} != ${peer_accessIP_oms} ];then
        modify_ip "${peer_accessIP_oms}" "${local_accessIP}"
    fi
    modify_ip "${peer_accessIP_oms_ipv4}" "${local_accessIP_ipv4}"

    modify_db_IP
    modify_db_port

    modify_oms_multiplane_nbi_iplist

    log_echo "INFO" "End modify_ip_port."
}

function check_whether_dual_or_disaser_switch()
{
    log_echo "INFO" "Begin to check whether dual or disaser switch."
    
    check_ret=$(/opt/oss/SOP/apps/HOFSOsdFileAgent/bin/hfscmd ls -l dvengineeringservicebucket/current_ip)
    echo ${check_ret} | grep "No such" >> $INFO_LOG_FILE 2>&1
    if [ $? -ne 0 ];then
        echo ${check_ret} | grep -w "invalid" >> $INFO_LOG_FILE 2>&1
        if [ $? -ne 0 ];then
            log_echo "INFO" "The current_ip=$(cat ${APP_ROOT}/bin/current_ip)."
            rm -f ${APP_ROOT}/bin/current_ip
            execute_hofs_jdk "download" "current_ip" "${APP_ROOT}/bin/current_ip"
            if [ $? -ne 0 ];then
                ## Re-Confirm the file damage or the service is error
                check_file_damage_ret=$(/opt/oss/SOP/apps/HOFSOsdFileAgent/bin/hfscmd update dvengineeringservicebucket/current_ip)
                echo "${check_file_damage_ret}" | grep -w "error" >> $INFO_LOG_FILE 2>&1
                if [ $? -eq 0 ];then
                    log_echo "INFO" "Finding current_ip file occurred damage, upload file again"
                    echo ${HOSTING_SERVER_IP} > ${APP_ROOT}/bin/current_ip
                    execute_hofs_jdk "upload" "current_ip" "${APP_ROOT}/bin/current_ip"
                    if [ $? -eq 0 ];then
                        log_echo "INFO" "File damage scenario, will reload file from hofs."
                        touch ${APP_ROOT}/bin/switch
                        execute_hofs_jdk "upload" "switch" "${APP_ROOT}/bin/switch"
                        return 0
                    else
                        die "Download and retransmit current_ip in hofs failed, HOFS Service occurred fault, please check"
                    fi
                fi
            fi
        
            old_ip=$(cat ${APP_ROOT}/bin/current_ip)
            current_ip=${HOSTING_SERVER_IP}
            if [ "X${old_ip}" == "X" ];then
                die "Get old_ip is null from current_ip, please check"
            fi
            if [ -f "${APP_ROOT}/bin/NEW_HOSTING_SERVER_IP" ];then
                cat ${APP_ROOT}/bin/NEW_HOSTING_SERVER_IP > ${APP_ROOT}/bin/current_ip
                rm ${APP_ROOT}/bin/NEW_HOSTING_SERVER_IP
                old_ip=$(cat ${APP_ROOT}/bin/current_ip)	    
            fi
            if [ "${old_ip}" != "${current_ip}" ];then
                log_echo "INFO" "dual or disaser switch."
                touch ${APP_ROOT}/bin/switch
                execute_hofs_jdk "upload" "switch" "${APP_ROOT}/bin/switch"
            else
                log_echo "INFO" "dual or disaser not switch."
                rm ${APP_ROOT}/bin/switch
                execute_hofs_jdk "remove" "switch" "true"
            fi
        else
            die "Can't find current_ip in hofs, please check"
        fi
    else
        log_echo "INFO" "start first time, create current_ip file."
        echo ${HOSTING_SERVER_IP} > ${APP_ROOT}/bin/current_ip
        execute_hofs_jdk "upload" "current_ip" "${APP_ROOT}/bin/current_ip"
        if [ $? -ne 0 ];then
            die "Upload current_ip to hofs failed first time, please check"
        fi
    fi
}

function check_hofs_fuse()
{
    log_echo "INFO" "Begin to check hofs_fuse"

    touch ${CURRENT_PATH}/check_tag
    i=0
    while [ $i -lt 300 ]
    do
        execute_hofs_jdk "upload" "check_tag" "${CURRENT_PATH}/check_tag"
        if [ $? -eq 0 ];then
            execute_hofs_jdk "remove" "check_tag" "true" &
            rm -rf ${CURRENT_PATH}/check_tag
            log_echo "INFO" "check hofs_fuse successfully"
            return 0
        fi
        if [ $i == 60 ];then
            start_hofs
        fi
        sleep 5
        ((i=i+5))
    done

    return 1
}

function check_whether_disaster_backuprestore()
{
    if [ -f ${APP_ROOT}/bin/internal/disaster_backuprestore.tag ];then
      log_echo "INFO" "This is disaster_backuprestore scene."
      delete_hofs_route
      log_echo "INFO" "Remove disaster_backuprestore.tag"
      rm -f ${APP_ROOT}/bin/internal/disaster_backuprestore.tag
      log_echo "INFO" "wait for hofs recover"
      check_hofs_fuse
    fi
}

function delete_hofs_route()
{
    log_echo "delete_hofs_route start"
    hofs_db_file=$(find "/opt/oss/SOP/apps/HOFSOsdService/etc/sysconf/" -name  "HOFSOsdService-*.json" | head -1)

    ## 导入python的zenith模块，导入SOP秘钥
    export PYTHONPATH=$APP_ROOT/dbdriver_zenith/pyscript/zenith/:$PYTHONPATH
    export LD_LIBRARY_PATH=$APP_ROOT/dbdriver_zenith/pyscript/zenith/:$LD_LIBRARY_PATH
    export _APP_LOG_DIR=/home/<USER>
    cp -a /opt/oss/SOP/etc/cipher /home/<USER>/chekc_cipher_tmp
    [ -f /home/<USER>/chekc_cipher_tmp/common_shared.ksf ] && cp -a /home/<USER>/chekc_cipher_tmp/common_shared.ksf /home/<USER>/chekc_cipher_tmp/common_shared.key
    [ -f /home/<USER>/chekc_cipher_tmp/base.ksf ] &&  cp -a  /home/<USER>/chekc_cipher_tmp/base.ksf  /home/<USER>/chekc_cipher_tmp/root.key
    export CIPHER_ROOT=/home/<USER>/chekc_cipher_tmp

    ## 删除数据库中hofs路由
    local ret=$(${APP_ROOT}/rtsp/python/bin/python ${APP_ROOT}/bin/tools/delete_hofs_routes.py ${hofs_db_file})
    if [ "${ret}" == "" ];then
        log_echo "WARN" "Found hofs route in db is empty, skip"
    else
        echo ${ret} >> $INFO_LOG_FILE
    fi

    ## 清理本机
    sudo -l |grep handle_hofs.sh
    if [ $? -eq 0 ];then
        sudo /home/<USER>/sudoScripts/handle_hofs.sh "delete_hofs_route" >> $INFO_LOG_FILE 2>&1
    fi

    ## 清理远端
    get_peer_ip
    if [ "X${peer_ip}" != "X" ];then
        timeout -s KILL 300 ssh -o ServerAliveInterval=60 -o StrictHostKeyChecking=no ossuser@${peer_ip} "sudo /home/<USER>/sudoScripts/handle_hofs.sh delete_hofs_route"
        if [ $? -eq 0 ];then
            log_echo "INFO" "Connect to secondary node to delete hofs node info success."
        else
            log_echo "WARN" "Execute [ssh -o ServerAliveInterval=60 -o StrictHostKeyChecking=no ossuser@${peer_ip} sudo /home/<USER>/sudoScripts/handle_hofs.sh delete_hofs_route] failed, continue"
        fi
    else
        log_echo "INFO" "The peer_ip=${peer_ip} is null, this is single net_type"
    fi

    rm -rf /home/<USER>/chekc_cipher_tmp
    rm -f /home/<USER>/oss.script.trace
    log_echo "delete_hofs_route end"
}

function execute_hofs_jdk()
{
    export APP_NAME=$(grep "^APP_NAME=" ${APP_ROOT}/envs/DVEngineeringService.properties |awk -F'APP_NAME=' '{print $2}')
    export UDS_ROOT=$(grep "^UDS_ROOT=" ${APP_ROOT}/envs/DVEngineeringService.properties |awk -F'UDS_ROOT=' '{print $2}')
    export UDS_ROOT_OSSUSER=$(grep "^UDS_ROOT_OSSUSER=" ${APP_ROOT}/envs/DVEngineeringService.properties |awk -F'UDS_ROOT_OSSUSER=' '{print $2}')
    
    sed -i "s#{{_APP_LOG_DIR}}#${_APP_LOG_DIR}#" ${APP_ROOT}/bin/sync2hofs/log4j2.xml
    
    local operation="$1"
    local param1="$2"
    local param2="$3"
    
    ${JAVA_HOME}/bin/java -cp "${_APP_SHARE_DIR}/I2000/run/3rdparty/lib/*" -Dio.netty.native.workdir=${APP_ROOT} -Dlog4j.configurationFile=${APP_ROOT}/bin/sync2hofs/log4j2.xml com.huawei.digitalview.commons.utils.hofs.HofsCmd "${operation}" "/dvengineeringservicebucket/${param1}" "${param2}" 2>/dev/null
}

function kill_process()
{
    echo "${I2000_process}" | grep -E "Dprocname=I2000 |Dprocname=sysmonitor|Dprocname=I2000_app_agent" | grep -v grep > /dev/null 2>&1
    if [ $? -eq 0 ];then
        echo y | $_APP_SHARE_DIR/I2000/mttools/lbin/kill.sh >> ${_APP_LOG_DIR}/DVEngineeringService_Stop.log 2>&1
    fi
    
    echo "${I2000_process}" | grep "Dprocname=I2000_CIE_MQ" | grep -v grep > /dev/null 2>&1
    if [ $? -eq 0 ];then
        $_APP_SHARE_DIR/cie/mq/bin/MQ stop >> ${_APP_LOG_DIR}/DVEngineeringService_Stop.log 2>&1
    fi
    
    echo "${I2000_process}" | grep "Dprocname=I2000_CIE_DMU" | grep -v grep > /dev/null 2>&1
    if [ $? -eq 0 ];then
        $_APP_SHARE_DIR/cie/dmu/bin/wrapper.sh stop >> ${_APP_LOG_DIR}/DVEngineeringService_Stop.log 2>&1
    fi
    
    echo "${I2000_process}" |grep "Dprocname=iCnfg" | grep -v grep > /dev/null 2>&1
    if [ $? -eq 0 ];then
        cd $_APP_SHARE_DIR/icnfg/webpages/bin
        $_APP_SHARE_DIR/icnfg/webpages/bin/kill_iCnfg.sh >> ${_APP_LOG_DIR}/DVEngineeringService_Stop.log 2>&1
        cd -
    fi
}

function check_start_hofs()
{
    local current_path=$1
    log_echo "INFO" "Begin to check hofs"
    
    ps -ef |grep -w "DNFW=hofsosdfileagent"|grep -v grep >>$INFO_LOG_FILE 2>&1
    ret1=$?
    ps -ef |grep -w "DNFW=hofsosdservice"|grep -v grep >>$INFO_LOG_FILE 2>&1
    ret2=$?
    if [ $ret1 -ne 0 -o $ret2 -ne 0 ];then
        log_echo "INFO" "try to start hofs"
        flock -xn /home/<USER>/hofs.lock -c "sudo /home/<USER>/sudoScripts/handle_hofs.sh startall" >>$INFO_LOG_FILE 2>&1
    fi
    
    touch ${current_path}/check_hofs_tag
    i=0
    while [ $i -lt 300 ]
    do
        execute_hofs_jdk "upload" "check_hofs_tag" "${current_path}/check_hofs_tag"
        if [ $? -eq 0 ];then
            execute_hofs_jdk "remove" "check_hofs_tag" "true"
            rm -rf ${current_path}/check_hofs_tag
            log_echo "INFO" "check hofs successfully"
            return 0
        fi
        if [ $i == 60 ];then
            log_echo "INFO" "try to start hofs again"
            flock -xn /home/<USER>/hofs.lock -c "sudo /home/<USER>/sudoScripts/handle_hofs.sh startall" >>$INFO_LOG_FILE 2>&1
        fi
        sleep 5
        ((i=i+5))
    done
    
    log_echo "ERROR" "check hofs timeout"
    return 1
}

function check_hofs_status()
{
    local current_path=$1
    log_echo "INFO" "Begin to check hofs status..."
    
    ps -ef |grep -w "DNFW=hofsosdfileagent"|grep -v grep >>$INFO_LOG_FILE 2>&1
    ret1=$?
    ps -ef |grep -w "DNFW=hofsosdservice"|grep -v grep >>$INFO_LOG_FILE 2>&1
    ret2=$?
    if [ $ret1 -ne 0 -o $ret2 -ne 0 ];then
        log_echo "ERROR" "The hofs not found hofsosdfileagent ret1=${ret1} or hofsosdservice ret2=${ret2} process."
        return 1
    fi
    
    touch ${current_path}/check_hofs_status
    local i=0
    while [ $i -lt 300 ]
    do
        execute_hofs_jdk "upload" "check_hofs_status" "${current_path}/check_hofs_status"
        if [ $? -eq 0 ];then
            execute_hofs_jdk "remove" "check_hofs_status" "true"
            rm -rf ${current_path}/check_hofs_status
            log_echo "INFO" "check hofs status ok"
            return 0
        fi
        sleep 5
        ((i=i+5))
        log_echo "INFO" "check hofs status try again.times=$i"
    done
    
    log_echo "ERROR" "check hofs status timeout"
    return 1
}

function dual_switch_custom()
{
    local operation_type="$1"
    local custom_time_out="$2"
    log_echo "INFO" "dual_switch_custom start... operation_type=${operation_type} custom_time_out=${custom_time_out}"
    ## 检查是否双机，标准集群 NetworkType=Cluster ,大容量 LargeCapacity  合设集群 MiniCluster ，不是双机组网，不需要操作。
    local network_type=$(cat ${APP_ROOT}/envs/env.properties | grep "^NetworkType=" |awk -F'=' '{print $2}')
    echo "${network_type}" | grep -wiE "Cluster|MiniCluster|largeCapacity" >>$INFO_LOG_FILE  2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "The network_type=${network_type} is not equal to [ Cluster|MiniCluster|largeCapacity ].not need do it."
        return 0
    fi
    
    ## 检查定制脚本是否存在，不存在不需要处理。
    local custom_script="/home/<USER>/custom_script/dual_switch_custom.sh"
    ## 设置调用命令执行超时时间5秒。超过这个时间，则超时退出。
    local time_out=5
    timeout -s KILL ${time_out} sudo -u sysomc /usr/bin/test -f ${custom_script}
    if [ $? -ne 0 ];then
        log_echo "INFO" "The ${custom_script} is not exists.not need do it."
        return 0
    fi
    
    ## 检查是否存在ipv6的浮动IP，不存在着不需要处理，跳过。存在则继续往下执行。
    local float_ip=$(cat ${APP_ROOT}/envs/env.properties | grep "^floatIPAddress=" |awk -F'=' '{print $2}')
    echo "${float_ip}" |grep ":" >>$INFO_LOG_FILE  2>&1
    if [ $? -ne 0 ];then
        log_echo "INFO" "The float_ip=${float_ip} is not has ipv6.not need do it."
        return 0
    fi
    
    ## 获取是否回切的开关。值为Yes表示回切，否则不回切。
    local enable_switch_back=$(cat ${APP_ROOT}/envs/env.properties | grep "^enableSwitchBack=" |awk -F'=' '{print $2}')
    log_echo "INFO" "The enable_switch_back=${enable_switch_back}"
    local enable_switch_back_log="INFO"
    [ "${enable_switch_back}" == "Yes" ] && enable_switch_back_log="ERROR"
    
    ## 浮动IP有多个时，获取IPV6的浮动IP。
    local ipv6_float_ip=""
    for tmp_ip_str in $(echo "${float_ip}" |sed "s/,/ /g");do
        echo "${tmp_ip_str}" |grep ":" >>$INFO_LOG_FILE  2>&1
        if [ $? -eq 0 ];then
            ipv6_float_ip="${tmp_ip_str}"
            break
        fi
    done
    
    log_echo "INFO" "The ipv6_float_ip=${ipv6_float_ip}"
    if [ -z "${ipv6_float_ip}" ];then
        log_echo "${enable_switch_back_log}" "The ipv6_float_ip is null."
        [ "${enable_switch_back}" == "Yes" ] && exit 1 || return 0
    fi
    
    ## 获取 IPV6 浮动ip所对应的当前节点网卡基础IP
    local tmp_cfg_file="$_APP_SHARE_DIR/I2000/run/etc/secondary_dual_key"
    if [ -f /home/<USER>/primary.tag ];then
        tmp_cfg_file="$_APP_SHARE_DIR/I2000/run/etc/primary_dual_key"
    fi
    local eni_ip=$(cat ${tmp_cfg_file}| grep "^rest_ip=" |awk -F'=' '{print $2}')
    if [ -z "${eni_ip}" ];then
        log_echo "${enable_switch_back_log}" "The eni_ip is null."
        [ "${enable_switch_back}" == "Yes" ] && exit 1 || return 0
    fi
    
    ## 设置调用定制脚本执行超时时间120秒。超过这个时间，则超时退出。 
    log_echo "INFO" "execute custom script cmd:[ timeout -s KILL ${custom_time_out} sudo -u sysomc ${custom_script} ${operation_type} /home/<USER>/log ${ipv6_float_ip} ${eni_ip} ]."
    timeout -s KILL ${custom_time_out} sudo -u sysomc ${custom_script} "${operation_type}" "/home/<USER>/log" "${ipv6_float_ip}" "${eni_ip}"
    local execute_ret=$?
    ## 执行超时的返回码为137 
    local timeout_code=137
    if [ ${execute_ret} -eq ${timeout_code} ];then
        if [ "${operation_type}" == "ShowPort" ];then
            log_echo "WARNING" "execute custom script cmd:[ timeout -s KILL ${custom_time_out} sudo -u sysomc ${custom_script} ${operation_type} /home/<USER>/log ${ipv6_float_ip} ${eni_ip} ] time out ( ${custom_time_out} s ).Please check."
            log_echo "INFO" "The operation_type=${operation_type} execute custom script time out (${custom_time_out} s)."
            return 0
        fi
        
        log_echo "${enable_switch_back_log}" "execute custom script cmd:[ timeout -s KILL ${custom_time_out} sudo -u sysomc ${custom_script} ${operation_type} /home/<USER>/log ${ipv6_float_ip} ${eni_ip} ] time out ( ${custom_time_out} s ).Please check."
        [ "${enable_switch_back}" == "Yes" ] && exit 1 || return 0
    fi
    
    if [ ${execute_ret} -ne 0 ];then
        log_echo "${enable_switch_back_log}" "execute custom script cmd:[ timeout -s KILL ${custom_time_out} sudo -u sysomc ${custom_script} ${operation_type} /home/<USER>/log ${ipv6_float_ip} ${eni_ip} ] Failed.Please check."
        [ "${enable_switch_back}" == "Yes" ] && exit 1 || return 0
    fi
    log_echo "INFO" "dual_switch_custom End."
}
