#!/bin/bash
current_path=$(cd "$(dirname "$0")"; pwd)

function log_echo()
{
    log_level=INFO
    if [ $# -ge 2 ]; then
        log_level=$1
        shift
    fi
    log_level=$(echo ${log_level} | tr 'a-z' 'A-Z')

    [ ! -d "$LOG_PATH" ] && mkdir -p "$LOG_PATH"

    echo "[${log_level}] $*"
    if [ "X${log_level}" == "XINFO" ]; then
        echo "$(date +'%Y-%m-%d %H:%M:%S') [${log_level}] $*" >> $INFO_LOG_FILE
    elif [ "X${log_level}" == "XERROR" ]; then
        echo "$(date +'%Y-%m-%d %H:%M:%S') [${log_level}] $*" >> $ERROR_LOG_FILE
    fi
}

function scroll_log()
{
    LOG_FILE=$1
    LOG_DIR=$(dirname ${LOG_FILE})
    LOG_NAME=$(basename ${LOG_FILE})
    logname_prefix=$(basename -s .log ${LOG_NAME})
    log_size=$(du -b ${LOG_FILE} | awk '{print $1}')
    if [ ${log_size} -gt 20480000 ]; then
        cd ${LOG_DIR}
        log_zip_num=$(ls -v |grep "${logname_prefix}"|grep ".zip"|wc -l)
        if [ ${log_zip_num} -ge 3 ];then
            log_zip_del=$(ls -vrt |grep "${logname_prefix}"|grep ".zip"|head -1)
            rm -f ${log_zip_del}
        fi
        zip -q ${logname_prefix}_$(date +'%Y%m%d%H%M%S').zip ${LOG_NAME}
        chmod 400 ${logname_prefix}*.zip
        echo "" > ${LOG_NAME}
        cd -
    fi
}

function init_env()
{    
    APP_ROOT=$(grep "^APP_ROOT=" ${current_path}/../../envs/DVEngineeringService.properties |awk -F'APP_ROOT=' '{print $2}')
    _APP_LOG_DIR=$(grep "^_APP_LOG_DIR=" ${APP_ROOT}/envs/env.properties | awk -F'=' '{print $2}')
    export _APP_SHARE_DIR=$(grep "^_APP_SHARE_DIR=" ${APP_ROOT}/envs/env.properties | awk -F'=' '{print $2}')
    
    LOG_PATH="$_APP_LOG_DIR/dvDrlog"
    INFO_LOG_FILE="${LOG_PATH}/drRsyncInfo.log"
    ERROR_LOG_FILE="${LOG_PATH}/drRsyncError.log"

    sync_time=$(grep "sync_time=" "${current_path}/dr.properties" | awk -F '=' '{print $2}')
    [ -z "${sync_time}" ] && sync_time=120
    ((timeout_time=$sync_time/2))
    
    peer_float_ip=$(grep "peer_float_ip=" "${current_path}/dr.properties" | awk -F '=' '{print $2}')
    echo ${peer_float_ip} | grep ":" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        ping_cmd="ping6"
    else
        ping_cmd="ping"
    fi
    
    ${ping_cmd} -c 4 ${peer_float_ip} > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Ping peer_float_ip failed, please check. peer_float_ip can't be null in dr.properties"
        exit 1
    fi
    
    rsync_dest_ip=${peer_float_ip}
    echo ${peer_float_ip}|grep ":" >/dev/null 2>&1
    [ $? -eq 0 ] && rsync_dest_ip="[${peer_float_ip}]"
    
    DRFILECHANGERECORD_PATH="${current_path}/sync2disaster.ini"
    
    [ -f "$INFO_LOG_FILE" ] && scroll_log "$INFO_LOG_FILE"
    [ -f "$ERROR_LOG_FILE" ] && scroll_log "$ERROR_LOG_FILE"
}

function change_dr_file_status_to_updating()
{
   log_echo "INFO" "Begin to change dr file status to updating."
   cat $DRFILECHANGERECORD_PATH | grep -E "drStatus_updated|drStatus_failed|drStatus_init"  >/dev/null 2>&1
   if [ $? -eq 0 ];then
       sed -i "s#drStatus_updated#drStatus_updating#g" $DRFILECHANGERECORD_PATH
       sed -i "s#drStatus_failed#drStatus_updating#g" $DRFILECHANGERECORD_PATH 
       sed -i "s#drStatus_init#drStatus_updating#g" $DRFILECHANGERECORD_PATH
    fi
    log_echo "INFO" "Success to change drRecordFiles's status to updating."
}

function check_env()
{
    dvdrexcute_ps_count=$(ps -ef | grep "${current_path}/sync2disaster.sh" | grep -v grep |grep -v "$$"|wc -l)
    if [ ${dvdrexcute_ps_count} -ge 1 ];then
        log_echo "INFO" "There are ${dvdrexcute_ps_count} processes running.Skip dr execute.[ps -ef | grep $PWD_DIR/dvDRExcute.sh | grep -v grep |grep -v $$ |wc -l]"
        exit 0
    fi

    #4.check recordFileTime has been modified in recent time[${sync_time}s] or not.
    local recordFileTime=`stat -c %Y $DRFILECHANGERECORD_PATH`
    local currentTime=`date +%s`
    cat $DRFILECHANGERECORD_PATH | grep -E "drStatus_updated|drStatus_failed|drStatus_updating"  >/dev/null 2>&1
    if [ $? -ne 0 -o $(($currentTime - $recordFileTime)) -gt ${sync_time} -o $(($currentTime - $recordFileTime)) -lt 0 ];then
       log_echo "INFO" "dr Record file is init status OR has not been modified in recent time[${sync_time}s] OR the currente time is before.Continue dr excute."
    else
       log_echo "INFO" "dr Record file has been modified in recent time[${sync_time}s].Before next execution has $[ ${sync_time} - $currentTime + $recordFileTime ] seconds left. Skip dr excute."
       exit 0
    fi
    
    sh /opt/oss/manager/tools/drmgr/getsitestatus.sh -pn SOP |grep "\"local\":[[:blank:]]*\"active\""  >> $INFO_LOG_FILE 2>&1
    if [ $? -eq 0 ];then
        ## this disaster. need to rsync.
        log_echo "INFO" "this disaster. need to rsync."
    else
        log_echo "INFO" "this not disaster.not need to rsync."
        exit 0
    fi
}

function execute_rsync()
{
    log_echo "INFO" "Begin to rsync dr file record."
    for path_config in `cat $DRFILECHANGERECORD_PATH | grep  'drStatus_updating'`
    do
        path=`echo ${path_config} | awk -F',' '{print $1}'`
        exclude_filename=`echo ${path_config} | awk -F',' '{print $3}'`
        if [ "X${exclude_filename}" != "X" ];then
            DR_EXCLUDELIST_FILE="$(dirname $DRFILECHANGERECORD_PATH)/${exclude_filename}"
        fi
        
        local source_path=`eval echo "${path}"`
        local des_path="$source_path"
        if [ -d $des_path ];then
            des_path=$(dirname $des_path)
        elif [ ! -f $des_path ];then
            sed -i "s#${path},drStatus_updating#${path},drStatus_init#g" $DRFILECHANGERECORD_PATH
            log_echo "INFO" "$source_path is not exist."
            continue
        fi

        sh $current_path/dvDRRsync.sh "${rsync_dest_ip}" "${source_path}" "${timeout_time}" "${DRFILECHANGERECORD_PATH}" "${path_config}" "${LOG_PATH}" "${des_path}" & >> $INFO_LOG_FILE 2>&1
   done
   
   wait
}

function main()
{
    who_am_i=$(whoami)
    if [ "${who_am_i}" != "ossuser" ];then
        echo "$(date +'%Y-%m-%d %H:%M:%S') [ERROR] Only ossuser can execute this."
        exit 1
    fi
    
    NFV_MODE=$(grep "^NFV_MODE=" ${current_path}/../../envs/DVEngineeringService.properties |awk -F'NFV_MODE=' '{print $2}')
    if [ "${NFV_MODE}" != "ON" ];then
        echo "$(date +'%Y-%m-%d %H:%M:%S') [INFO] NFV_MODE is not ON, no need to execute this."
        exit 0
    fi
    
    init_env
    
    check_env
    
    change_dr_file_status_to_updating
    
    execute_rsync
}

main