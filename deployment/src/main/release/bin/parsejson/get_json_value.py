#  Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.

import sys
import json


def get_json(jsondata, key):
    try:
        key_list = key.split("#")
        for jp in range(len(key_list)):
            jsondata = jsondata.get(key_list[jp])
        return jsondata
    except Exception as e:
        print("error")
        return ""


if __name__ == '__main__':
    json_path = sys.argv[1]
    with open(json_path, 'r') as jsonfile:
        obj_json = json.load(jsonfile)
    key_path = sys.argv[2]
    print(get_json(obj_json, key_path))