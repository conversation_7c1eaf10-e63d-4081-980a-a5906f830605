#
# Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
#

#!/bin/bash

CURRENT_PATH=$(
  cd $(dirname $0)
  pwd
)
source $CURRENT_PATH/../../envs/*.properties

log_file=${_APP_LOG_DIR}/${APP_NAME}_DR.log
# 添加排他锁，只有第一个拿到锁的服务能启动hofs
flock -xn /home/<USER>/hofs.lock -c "sudo /home/<USER>/sudoScripts/handle_hofs.sh start"
ret=$?
if [ $ret -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') [INFO] Start hofsosdfileagent success." >> $log_file
else
    echo "$(date '+%Y-%m-%d %H:%M:%S') [WARN] Start hofsosdfileagent failed, maybe another process has start it first." >> $log_file
fi

sh ${APP_ROOT}/bin/sync_files.sh "download" "allDrFile"

exit 0
