CREATE TABLE IF NOT EXISTS dv_interconnection_status (
    id varchar(50) NOT NULL,
    applied bool,
    PRIMARY KEY (id)
)
/

CREATE TABLE IF NOT EXISTS register_task_progress (
    id varchar(50) NOT NULL,
    progress int,
    status int,
    response_status int,
    err_msg varchar(500),
    PRIMARY KEY (id)
    )
    /

CREATE TABLE IF NOT EXISTS atlas_local_config (
                                           id varchar(50) NOT NULL,
                                           sitename varchar(500),
                                           businessnodetype varchar(100),
                                           businessconfigporttype varchar(100),
                                           businessip varchar(100),
                                           businessipv6 varchar(100),
                                           businessnatenable bool,
                                           businessnatip varchar(100),
                                           businessnatipv6 varchar(100),
                                           businessberport int,
                                           businesssiteidentity varchar(100),
                                           adminnodetype varchar(100),
                                           adminconfigporttype varchar(100),
                                           adminip varchar(100),
                                           adminipv6 varchar(100),
                                           adminnatenable bool,
                                           adminnatip varchar(100),
                                           adminnatipv6 varchar(100),
                                           adminberport int,
                                           adminconnport int,
                                           adminsiteidentity varchar(100),
                                           businessapplybyipv4 bool,
                                           adminapplybyipv4 bool,
                                           businessnatapplybyipv4 bool,
                                           adminnatapplybyipv4 bool,
                                           PRIMARY KEY (id)
)
/

CREATE TABLE IF NOT EXISTS atlas_peer_config (
                                          id varchar(100) NOT NULL,
                                          sitename varchar(500),
                                          businessip varchar(100),
                                          businessberport int,
                                          businesssiteidentity varchar(100),
                                          adminip varchar(100),
                                          adminberport int,
                                          adminconnport int,
                                          adminsiteidentity varchar(100),
                                          enable bool,
                                          hasconnected bool,
                                          peer_role varchar(100),
                                          peer_physical_role varchar(100),
                                          peernatenable bool,
                                          conn_id varchar(100) NOT NULL,
                                          PRIMARY KEY (id)
)
/

CREATE TABLE IF NOT EXISTS atlas_local_conn (
                                         id varchar(100) NOT NULL,
                                         localid varchar(100) NOT NULL,
                                         status  varchar(100) NOT NULL,
                                         last_fail_status varchar(100),
                                         op_type varchar(100) NOT NULL,
                                         msg text,
                                         PRIMARY KEY (id)
)

/
COMMIT
/

DROP PROCEDURE IF EXISTS P_ADD_DVOMPEXT_COLUMN
/
CREATE OR REPLACE PROCEDURE P_ADD_DVOMPEXT_COLUMN(tableName IN VARCHAR, testColumnName IN VARCHAR, testColumnDetail IN VARCHAR) AS
num int:=0;
tabNum int:=0;
sqlStr VARCHAR(512):='';
BEGIN
  SELECT count(*) into tabNum FROM MY_TABLES WHERE TABLE_NAME = UPPER(tableName);
  IF tabNum > 0 THEN
    SELECT count(*) INTO num  FROM MY_TAB_COLUMNS WHERE TABLE_NAME = UPPER(tableName) AND COLUMN_NAME = UPPER(testColumnName);
    IF num = 0 THEN
      sqlStr:=CONCAT('ALTER TABLE ', tableName, ' ADD ', testColumnName,' ',testColumnDetail);
      EXECUTE IMMEDIATE sqlStr;
      COMMIT;
    END IF;
  END IF;
END
/
CALL P_ADD_DVOMPEXT_COLUMN('atlas_peer_config', 'peernatenable', 'BOOLEAN')
/
CALL P_ADD_DVOMPEXT_COLUMN('atlas_local_config', 'businessapplybyipv4', 'BOOLEAN')
/
CALL P_ADD_DVOMPEXT_COLUMN('atlas_local_config', 'adminapplybyipv4', 'BOOLEAN')
/
CALL P_ADD_DVOMPEXT_COLUMN('atlas_local_config', 'businessnatapplybyipv4', 'BOOLEAN')
/
CALL P_ADD_DVOMPEXT_COLUMN('atlas_local_config', 'adminnatapplybyipv4', 'BOOLEAN')
/
