/*==============================================================*/
/*               包管理                                           */
/*==============================================================*/
DECLARE V_FLAG NUMBER;
BEGIN
SELECT COUNT(*) INTO V_FLAG FROM MY_TABLES WHERE TABLE_NAME = UPPER('CHANGEINFORMATION');
IF V_FLAG = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE CHANGEINFORMATION
	(
		ID  NUMBER(10) NOT NULL,
		CHANGEINFO VARCHAR(1024)  NOT NULL,
		CREATETIME NUMBER(20)  NOT NULL,
		PRIMARY KEY(ID)
	)
';
END IF;
END;
/

DECLARE V_FLAG NUMBER;
BEGIN
SELECT COUNT(*) INTO V_FLAG FROM MY_TABLES WHERE TABLE_NAME = UPPER('MEDNODEINFO');
IF V_FLAG = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE MEDNODEINFO
	(
		ID VARCHAR2(32) NOT NULL,
		IPADDRESS VARCHAR2(51)  NOT NULL,
		NODEID VARCHAR2(127)  NOT NULL,
		DES VARCHAR2(255),
		CREATETIME NUMBER(19)  NOT NULL,
		MODIFYTIME NUMBER(19),
		PRIMARY KEY(ID)
	)
';
END IF;
END;
/

DECLARE V_FLAG NUMBER;
BEGIN
SELECT COUNT(*) INTO V_FLAG FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_MOPKG_CHANGEINFO');
IF V_FLAG = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_MOPKG_CHANGEINFO
(
    CHANGEID INT NOT NULL AUTO_INCREMENT,
    CHANGETYPE VARCHAR(128) NOT NULL,
    CHANGECONTENT VARCHAR(1024) NOT NULL,
    CHANGEDATE DATETIME,
    CHANGEDATEUTC DECIMAL(13),
    PRIMARY KEY (CHANGEID)
)
';
END IF;
END;
/

DECLARE V_FLAG NUMBER;
BEGIN
SELECT COUNT(*) INTO V_FLAG FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_MOPKG_CHANGEPROCRESULT');
IF V_FLAG = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_MOPKG_CHANGEPROCRESULT
(
    CHANGEID INT NOT NULL,
    COMPID VARCHAR(128) NOT NULL,
    PROCRESULT INT,
    PRIMARY KEY ( CHANGEID, COMPID )
)
';
END IF;
END;
/

DECLARE V_FLAG NUMBER;
BEGIN
SELECT COUNT(*) INTO V_FLAG FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_MOPKG_COMP');
IF V_FLAG = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_MOPKG_COMP
(
    COMPID VARCHAR(128) NOT NULL,
    PRIMARY KEY ( COMPID )
)
';
END IF;
END;
/

/*==============================================================*/
/*               AK/SK                                           */
/*==============================================================*/
DECLARE V_FLAG NUMBER;
BEGIN
SELECT COUNT(*) INTO V_FLAG FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ACCESS_KEYS');
IF V_FLAG = 0 THEN
EXECUTE IMMEDIATE
'create table TBL_ACCESS_KEYS
	(
    		ID VARCHAR(32)  not null,
    		CREATEDTIME NUMBER(19) not null,
    		SECRETKEY        VARCHAR(512) not null,
    		CREATOR          VARCHAR(32)  not null,
    		PERIODOFVALIDITY NUMBER(10),
    		STATUS           NUMBER(4),
    		LABEL            VARCHAR(32),
    		CHECKED INT,
		    PRIMARY KEY(ID),
            UNIQUE(CREATEDTIME)
	)
';
END IF;
END;
/

/*==============================================================*/
/*               账号管理                                        */
/*==============================================================*/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_TASKBASICATTR');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_TASKBASICATTR
(
    TASK_ID VARCHAR(128) NOT NULL,
	TASK_NAME VARCHAR(128) NOT NULL,
	CREATE_TIME NUMBER(20,0) NOT NULL,
	EXECUTE_TIME NUMBER(20,0),
	CREATOR VARCHAR(64) NOT NULL,
	STATE VARCHAR(32) NOT NULL,
    CUR_STEP VARCHAR(32) NOT NULL,
	DESCRIPTION VARCHAR(512),
	TENANTID VARCHAR(128),
    PRIMARY KEY ("TASK_ID")
)
';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_TASKEXTENDATTR');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_TASKEXTENDATTR
(
    TASK_ID VARCHAR(128) NOT NULL,
	FIELDNAME VARCHAR(128),
	FIELDVALUE CLOB,
    FOREIGN KEY(TASK_ID) REFERENCES TBL_TASKBASICATTR(TASK_ID) ON DELETE CASCADE,
    PRIMARY KEY ("TASK_ID","FIELDNAME")
)
';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_CHANGEPWD_ITEMRECORD');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_CHANGEPWD_ITEMRECORD
(
    TASK_ID VARCHAR(128) NOT NULL,
	ACCOUNTID VARCHAR(255) NOT NULL,
	NEID VARCHAR(255) NOT NULL,
	STEP VARCHAR(32) NOT NULL,
	RESULT VARCHAR(255),
	DESCRIPTION VARCHAR(512),
	OMCIP VARCHAR(255),
    FOREIGN KEY(TASK_ID) REFERENCES TBL_TASKBASICATTR(TASK_ID) ON DELETE CASCADE
)
';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_REGACCOUNT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_REGACCOUNT
(
    ID NUMBER(10,0) NOT NULL,
    NAME VARCHAR(128) NOT NULL,
    TYPE VARCHAR(128) NOT NULL,
    NE_IP VARCHAR(255) NOT NULL,
	IP VARCHAR(128),
	PORT NUMBER(8,0) NOT NULL,
	PROTOCOL_TYPE VARCHAR(128) NOT NULL,
	CREATE_TIME VARCHAR(128) NOT NULL,
    PRIMARY KEY ("ID")
)
';
END IF;
IF V_CNT > 0 THEN
EXECUTE IMMEDIATE
'ALTER TABLE TBL_REGACCOUNT MODIFY NE_IP VARCHAR(255)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_REGACCOUNT_EXTEND');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_REGACCOUNT_EXTEND
(
    ID NUMBER(10,0) NOT NULL,
    FIELDNAME VARCHAR(64) NOT NULL,
    FIELDVALUE VARCHAR(1024),
    FOREIGN KEY(ID) REFERENCES TBL_REGACCOUNT(ID) ON DELETE CASCADE,
    PRIMARY KEY ("ID","FIELDNAME")
)
';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ACCOUNT_ROLE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ACCOUNT_ROLE
(
    ROLEID VARCHAR(32) NOT NULL,
	ACCOUNTKEY VARCHAR(32) NOT NULL,
	PRIMARY KEY ("ROLEID","ACCOUNTKEY")
)
';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ACCOUNT_RELATION');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ACCOUNT_RELATION
(
    SRCNODE VARCHAR(255) NOT NULL,
	DESTNODE VARCHAR(255) NOT NULL,
	TYPE VARCHAR(128) NOT NULL,
	DESCRIPTION VARCHAR(512),
    PRIMARY KEY ("SRCNODE","DESTNODE","TYPE"),
    UNIQUE(SRCNODE, DESTNODE)
)
';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ACCOUNTPROTOCOL');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ACCOUNTPROTOCOL
(
    MOTYPE VARCHAR(128) NOT NULL,
    PROTOCOLTYPE VARCHAR(128) NOT NULL,
    INSERTTIME NUMBER(20,0),
    EXTENDS VARCHAR(255),
    PRIMARY KEY ("MOTYPE","PROTOCOLTYPE")
)
';
END IF;
END;
/

/*==============================================================*/
/*                   资产管理                                     */
/*==============================================================*/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_BASE');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_BASE
(
    ASSETID            NUMBER(38) NOT NULL,
    TYPEGROUP          VARCHAR(32) NOT NULL,
    TYPEID             VARCHAR(64) NOT NULL,
    NAME               VARCHAR(128),
    VENDOR             VARCHAR(64),
    IP                 VARCHAR(48),
    ASSETVERSION       VARCHAR(128),
    STATUS             VARCHAR(16),
    ADMINISTRATOR      VARCHAR(256),
    LOCATION           VARCHAR(256),
    LASTMODIFIEDTIME   BIGINT,
    LASTMODIFIEDPERSON VARCHAR(32) NOT NULL,
    DESCRIPTION        VARCHAR(512),
    PRIMARY KEY ("ASSETID")
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_ACC_I2000DEVICE');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_ACC_I2000DEVICE
(
    ASSETID       NUMBER(38) NOT NULL,
    PROPSUNIT     VARCHAR(64) NOT NULL,
    PROPERTYROWID TINYINT NOT NULL,
    FIELDNAME     VARCHAR(64) NOT NULL,
    FIELDVALUE    VARCHAR(2000),
    ISENCODE      VARCHAR(64),
    PRIMARY KEY (ASSETID, FIELDNAME, PROPERTYROWID, PROPSUNIT),
    FOREIGN KEY (ASSETID) REFERENCES TBL_ASSET_BASE (ASSETID) ON DELETE CASCADE,
    UNIQUE (ASSETID, PROPSUNIT, PROPERTYROWID, FIELDNAME)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_ACC_I2000HOST');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_ACC_I2000HOST
(
    ASSETID       NUMBER(38) NOT NULL,
    PROPSUNIT     VARCHAR(64) NOT NULL,
    PROPERTYROWID TINYINT NOT NULL,
    FIELDNAME     VARCHAR(64) NOT NULL,
    FIELDVALUE    VARCHAR(2000),
    ISENCODE      VARCHAR(64),
    PRIMARY KEY (ASSETID, FIELDNAME, PROPERTYROWID, PROPSUNIT),
    FOREIGN KEY (ASSETID) REFERENCES TBL_ASSET_BASE (ASSETID) ON DELETE CASCADE,
    UNIQUE (ASSETID, PROPSUNIT, PROPERTYROWID, FIELDNAME)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_ACC_I2000MIDDLEWARE');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_ACC_I2000MIDDLEWARE
(
    ASSETID       NUMBER(38) NOT NULL,
    PROPSUNIT     VARCHAR(64) NOT NULL,
    PROPERTYROWID TINYINT NOT NULL,
    FIELDNAME     VARCHAR(64) NOT NULL,
    FIELDVALUE    VARCHAR(2000),
    ISENCODE      VARCHAR(64),
    PRIMARY KEY (ASSETID, FIELDNAME, PROPERTYROWID, PROPSUNIT),
    FOREIGN KEY (ASSETID) REFERENCES TBL_ASSET_BASE (ASSETID) ON DELETE CASCADE,
    UNIQUE (ASSETID, PROPSUNIT, PROPERTYROWID, FIELDNAME)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_ACC_I2000SUBNET');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_ACC_I2000SUBNET
(
    ASSETID       NUMBER(38) NOT NULL,
    PROPSUNIT     VARCHAR(64) NOT NULL,
    PROPERTYROWID TINYINT NOT NULL,
    FIELDNAME     VARCHAR(64) NOT NULL,
    FIELDVALUE    VARCHAR(2000),
    ISENCODE      VARCHAR(64),
    PRIMARY KEY (ASSETID, FIELDNAME, PROPERTYROWID, PROPSUNIT),
    FOREIGN KEY (ASSETID) REFERENCES TBL_ASSET_BASE (ASSETID) ON DELETE CASCADE,
    UNIQUE (ASSETID, PROPSUNIT, PROPERTYROWID, FIELDNAME)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_EXT_I2000DEVICE');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_EXT_I2000DEVICE
(
    ASSETID       NUMBER(38) NOT NULL,
    PROPSUNIT     VARCHAR(64) NOT NULL,
    PROPERTYROWID TINYINT NOT NULL,
    FIELDNAME     VARCHAR(64) NOT NULL,
    FIELDVALUE    VARCHAR(2000),
    ISENCODE      VARCHAR(64),
    PRIMARY KEY (ASSETID, FIELDNAME, PROPERTYROWID, PROPSUNIT),
    FOREIGN KEY (ASSETID) REFERENCES TBL_ASSET_BASE (ASSETID) ON DELETE CASCADE,
    UNIQUE (ASSETID, PROPSUNIT, PROPERTYROWID, FIELDNAME)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_EXT_I2000HOST');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_EXT_I2000HOST
(
    ASSETID       NUMBER(38) NOT NULL,
    PROPSUNIT     VARCHAR(64) NOT NULL,
    PROPERTYROWID TINYINT NOT NULL,
    FIELDNAME     VARCHAR(64) NOT NULL,
    FIELDVALUE    VARCHAR(2000),
    ISENCODE      VARCHAR(64),
    PRIMARY KEY (ASSETID, FIELDNAME, PROPERTYROWID, PROPSUNIT),
    FOREIGN KEY (ASSETID) REFERENCES TBL_ASSET_BASE (ASSETID) ON DELETE CASCADE,
    UNIQUE (ASSETID, PROPSUNIT, PROPERTYROWID, FIELDNAME)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_EXT_I2000MIDDLEWARE');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_EXT_I2000MIDDLEWARE
(
    ASSETID       NUMBER(38) NOT NULL,
    PROPSUNIT     VARCHAR(64) NOT NULL,
    PROPERTYROWID TINYINT NOT NULL,
    FIELDNAME     VARCHAR(64) NOT NULL,
    FIELDVALUE    VARCHAR(2000),
    ISENCODE      VARCHAR(64),
    PRIMARY KEY (ASSETID, FIELDNAME, PROPERTYROWID, PROPSUNIT),
    FOREIGN KEY (ASSETID) REFERENCES TBL_ASSET_BASE (ASSETID) ON DELETE CASCADE,
    UNIQUE (ASSETID, PROPSUNIT, PROPERTYROWID, FIELDNAME)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_EXT_I2000SUBNET');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_EXT_I2000SUBNET
(
    ASSETID       NUMBER(38) NOT NULL,
    PROPSUNIT     VARCHAR(64) NOT NULL,
    PROPERTYROWID TINYINT NOT NULL,
    FIELDNAME     VARCHAR(64) NOT NULL,
    FIELDVALUE    VARCHAR(2000),
    ISENCODE      VARCHAR(64),
    PRIMARY KEY (ASSETID, FIELDNAME, PROPERTYROWID, PROPSUNIT),
    FOREIGN KEY (ASSETID) REFERENCES TBL_ASSET_BASE (ASSETID) ON DELETE CASCADE,
    UNIQUE (ASSETID, PROPSUNIT, PROPERTYROWID, FIELDNAME)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_IMPORTTASK');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_IMPORTTASK
(
    TASKID       NUMBER(20) NOT NULL,
    TASKNAME     VARCHAR(128) NOT NULL,
    TASKTYPE     NUMBER(2) NOT NULL,
    TEMPLATENAME VARCHAR(128) NOT NULL,
    CREATETIME   NUMBER(20) NOT NULL,
    FINISHTIME   NUMBER(20),
    STATUS       NUMBER(1) NOT NULL,
    OPERATOR     VARCHAR(64)  NOT NULL,
    ALLCOUNT     NUMBER(20) NOT NULL,
    SUCCESSCOUNT NUMBER(20) NOT NULL,
    PROGRESS     NUMBER(3) NOT NULL,
    DESCRIPTION  VARCHAR(512),
    PRIMARY KEY (TASKID)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_IMPORTNODE');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_IMPORTNODE
(
    TASKID         NUMBER(20) NOT NULL,
    DATAID         NUMBER(20) NOT NULL,
    ASSETTYPEGROUP VARCHAR(32),
    ASSETTYPE      VARCHAR(64),
    ASSETNAME      VARCHAR(128),
    ASSETIP        VARCHAR(48),
    ASSETVERSION   VARCHAR(128),
    DESCRIPTION    VARCHAR(512),
    ASSETADMIN     VARCHAR(256),
    ASSETLOCATION  VARCHAR(256),
    ASSETVENDOR    VARCHAR(64),
    STATUS         NUMBER(1) NOT NULL,
    ERRORDETAIL    VARCHAR(1024),
    FOREIGN KEY (TASKID) REFERENCES TBL_ASSET_IMPORTTASK (TASKID)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_INTALLEDASSET');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_INTALLEDASSET
(
    ASSETID            INT AUTO_INCREMENT,
    NAME               VARCHAR(512) NOT NULL,
    CATEGORYGROUP      VARCHAR(32)  NOT NULL,
    CATEGORY           VARCHAR(32)  NOT NULL,
    TYPE               VARCHAR(128) NOT NULL,
    VERSION            VARCHAR(256) NOT NULL,
    VERSIONCHECKSTATUS TINYINT      NOT NULL,
    PATCH              VARCHAR(4000),
    NEDN               VARCHAR(255),
    SN                 VARCHAR(128),
    MOTYPEID           VARCHAR(128),
    SOLUTIONID         TINYINT,
    SOLUTIONTYPE       VARCHAR(64),
    SOLUTIONNAME       VARCHAR(512),
    SOLUTIONSTATUS     TINYINT,
    AGENTIP            VARCHAR(128),
    INCLUDEPLATFORM    VARCHAR(512),
    CREATETIME         NUMBER(24) NOT NULL,
    LASTMODIFIEDTIME   NUMBER(24),
    PRIMARY KEY (ASSETID)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_INTALLEDASSET_EXT');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_INTALLEDASSET_EXT
(
    ASSETID    INT NOT NULL,
    FIELDNAME  VARCHAR(128) NOT NULL,
    FIELDVALUE VARCHAR(512),
    PRIMARY KEY (ASSETID, FIELDNAME),
    FOREIGN KEY (ASSETID) REFERENCES TBL_ASSET_INTALLEDASSET (ASSETID)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_LIFESPAN');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_LIFESPAN
(
    ID                NUMBER(32) NOT NULL,
    DN                VARCHAR(64),
    NAME              VARCHAR(100),
    CATEGROY          VARCHAR(16),
    TYPE              VARCHAR(100),
    TYPENAME          VARCHAR(100),
    VERSION           VARCHAR(100),
    STATUS            VARCHAR(16),
    SERIALNUMBER      VARCHAR(100),
    MANUFACTUREDATE   VARCHAR(16),
    STARTINGPOINTDATE VARCHAR(16),
    SERVICEDURATION   VARCHAR(8),
    GADATE            VARCHAR(20),
    EOMDATE           VARCHAR(20),
    EOFSDATE          VARCHAR(20),
    EOSDATE           VARCHAR(20),
    EXINFODATA        VARCHAR(200),
    TENANTID          VARCHAR(100),
    PRIMARY KEY (ID)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_REFER');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_REFER
(
    ASSETID    NUMBER(38) NOT NULL,
    PROPERTYID VARCHAR(32) NOT NULL,
    FIELDNAME  VARCHAR(32) NOT NULL,
    REFASSETID NUMBER(38) NOT NULL,
    FOREIGN KEY (ASSETID) REFERENCES TBL_ASSET_BASE (ASSETID)
)
';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_ASSET_RELATION');
IF V_CNT < 1 THEN
EXECUTE IMMEDIATE
'
CREATE TABLE TBL_ASSET_RELATION
(
    SOURCEASSETID NUMBER(38) NOT NULL,
    RELATIONTYPE  VARCHAR(32) NOT NULL,
    TARGETASSETID NUMBER(38) NOT NULL,
    PRIMARY KEY (SOURCEASSETID, TARGETASSETID),
    FOREIGN KEY (SOURCEASSETID) REFERENCES TBL_ASSET_BASE (ASSETID),
    FOREIGN KEY (TARGETASSETID) REFERENCES TBL_ASSET_BASE (ASSETID)
)
';
END IF;
END;
/

/*==============================================================*/
/*                自定义告警                                          */
/*==============================================================*/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_DEPLOYTASK');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_DEPLOYTASK (
	ID VARCHAR(512) NOT NULL,
	NAME VARCHAR(512) NOT NULL,
	CREATE_TIME TIMESTAMP NOT NULL,
	MO_TYPE VARCHAR(128) NOT NULL,
	DEPLOY_MODE NUMBER(1) NOT NULL,
	TYPE NUMBER(1) NOT NULL,
	DESCRIPTION VARCHAR(1024),
	ERROR_INFO VARCHAR(1024),
	EXTEND_PARAM VARCHAR(4096),
	PRIMARY KEY ("ID")
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_DEPLOYTASK_GROUP');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_DEPLOYTASK_GROUP (
	DEPLOY_TASK_ID VARCHAR(512) NOT NULL,
	GROUP_DN VARCHAR(255) NOT NULL
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_DEPLOYTASK_MO');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_DEPLOYTASK_MO (
	DEPLOY_TASK_ID VARCHAR(512) NOT NULL,
	DN VARCHAR(255) NOT NULL,
	AGENT_DN VARCHAR(255),
	TIMESTAMP TIMESTAMP NOT NULL,
	ACCOUNT_DN VARCHAR(255),
	USER_NAME VARCHAR(255),
	USER_INFO VARCHAR(255),
	CONNECTION_INFO VARCHAR(512),
	DEPLOY_STATUS NUMBER(1) NOT NULL,
	RESULT VARCHAR(1024)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_DEPLOYTASK_SCRIPT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_DEPLOYTASK_SCRIPT (
	DEPLOY_TASK_ID VARCHAR(512) NOT NULL,
	TYPE NUMBER(3) NOT NULL,
	NAME VARCHAR(1024),
	CONTENT CLOB NOT NULL,
	SCRIPTPARAM VARCHAR(1024)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_ALARMTASK_CONFIG');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_ALARMTASK_CONFIG (
	ALARM_ID NUMBER(20) NOT NULL,
	DN VARCHAR(255) NOT NULL,
	DEPLOY_TASK_ID VARCHAR(512) NOT NULL,
	STATUS NUMBER(1) NOT NULL,
	CURRENT_ALARM_LEVEL NUMBER(1) NOT NULL,
	EVENT_TIME NUMBER(20),
	ALARM_SN NUMBER(11),
	PERIOD BINARY_INTEGER NOT NULL,
	FIRST_START_TIME VARCHAR(5),
	ALARM_CONDITION_TYPE NUMBER(1) NOT NULL,
	ALARM_CONDITION_JSON VARCHAR(2048) NOT NULL,
	EXTEND_PARAM VARCHAR(4096)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_ALARMTASK_STATICINFO');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_ALARMTASK_STATICINFO (
	ALARM_ID NUMBER(20) NOT NULL,
	ALARM_GROUP_ID NUMBER(20) NOT NULL,
	ALARM_NAME VARCHAR(256) NOT NULL,
	EVENT_TYPE NUMBER(5),
	CLEAR_TYPE NUMBER(5),
	SEVERITY NUMBER(5),
	PROBALE_CAUSE_KEY NUMBER(20),
	PROBALE_CAUSE VARCHAR(2048),
	PROPOSED_REPAIR VARCHAR(2048),
	TYPE NUMBER(5),
	PRIMARY KEY ("ALARM_GROUP_ID","ALARM_ID")
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_ALARM_TEMPLET');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_ALARM_TEMPLET (
	ALARM_ID NUMBER(20) NOT NULL,
	ALARM_NAME VARCHAR(256) NOT NULL,
	PERIOD NUMBER(10) NOT NULL,
	EVENT_TYPE NUMBER(2) NOT NULL,
	CLEARED_TYPE NUMBER(2) NOT NULL,
	PERCEIVEDSEVERITY NUMBER(2) NOT NULL,
	PROABLECASE VARCHAR(128),
	REPAIR_URL VARCHAR(500),
	DESCRIBE VARCHAR(256),
	ALARM_CONDITION NUMBER(2) NOT NULL,
	NAME VARCHAR(256) NOT NULL,
	REPEATTIMES NUMBER(20),
	CONDITION VARCHAR(2048) NOT NULL,
	TIMEOUT NUMBER(20),
	SCRIPTTYPE NUMBER(3) NOT NULL,
	SCRIPTNAME VARCHAR(1024) NOT NULL,
	SCRIPTCONTEXT VARCHAR(4000),
	RESCRIPTNAME VARCHAR(1024),
	RESCRIPTCONTEXT CLOB,
	FIRST_OCCUR_TIME VARCHAR(20),
	EXTEND_PARAM VARCHAR(1024),
	TENANTID VARCHAR(128),
	SCRIPTPARAM VARCHAR(1024),
	SCRIPT_CONTENT CLOB,
	ADD_SCRIPT_CONTENT CLOB,
	ISSINGNED VARCHAR(32) NOT NULL,
	PRIMARY KEY ("NAME")
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO  V_CNT FROM  MY_TAB_COLUMNS WHERE TABLE_NAME='TBL_ALARM_TEMPLET' AND COLUMN_NAME = 'PRE_CHECK_SCRIPT_CONTENT';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'ALTER TABLE TBL_ALARM_TEMPLET ADD (PRE_CHECK_SCRIPT_CONTENT CLOB)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_TASK_AUDIT');
IF V_CNT > 0 THEN
EXECUTE IMMEDIATE
'DROP TABLE IF EXISTS TBL_TASK_AUDIT';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO  V_CNT FROM  MY_TAB_COLUMNS WHERE TABLE_NAME='TBL_ALARMTASK_CONFIG' AND COLUMN_NAME = 'ALARM_TIME_UNIT';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'ALTER TABLE TBL_ALARMTASK_CONFIG ADD (ALARM_TIME_UNIT VARCHAR(20))';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO  V_CNT FROM  MY_TAB_COLUMNS WHERE TABLE_NAME='TBL_ALARM_TEMPLET' AND COLUMN_NAME = 'ALARM_TIME_UNIT';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'ALTER TABLE TBL_ALARM_TEMPLET ADD (ALARM_TIME_UNIT VARCHAR(20))';
END IF;
END;
/


/*==============================================================*/
/*               配置管理                                        */
/*==============================================================*/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_CUSTOM_CONFIG');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_CUSTOM_CONFIG (
	CONFIG_NAME VARCHAR(256) NOT NULL,
	CONFIG_VALUE VARCHAR(256) NOT NULL
)';
END IF;
END;
/

/*==============================================================*/
/*               代理管理                                        */
/*==============================================================*/

declare
v_count  number:=0;
begin
SELECT COUNT(*) INTO v_count FROM MY_TABLES WHERE TABLE_NAME=UPPER('TASKINFO');
if v_count = 0 then
        execute immediate '
		   CREATE TABLE TASKINFO
			(
				"ID" NUMBER(20, 0) NOT NULL,
				"TASKCLASS" VARCHAR2(256) NOT NULL,
				"EXECUTER" VARCHAR2(256) NOT NULL,
				"RISKLEVEL" VARCHAR2(256) NOT NULL,
				"TASKTYPE" NUMBER(12, 0) NOT NULL,
				"NAME" VARCHAR2(256) NOT NULL,
				"DESCRIPTION" VARCHAR2(512),
				"STATE" NUMBER(12, 0) NOT NULL,
				"RATE" NUMBER(12, 0) NOT NULL,
				"RESULT" NUMBER(12, 0) NOT NULL,
				"ALLSELECTED" NUMBER(12, 0) NOT NULL,
				"CREATE_TIME" NUMBER(20, 0) NOT NULL,
				"STARTTIME" NUMBER(20, 0) NOT NULL,
				"CONSUMETIME" NUMBER(20, 0) NOT NULL,
				"USERID" VARCHAR2(128) NOT NULL,
				"USERNAME" VARCHAR2(128) NOT NULL,
				"EXTENDATTRI1" VARCHAR2(2048),
				"EXTENDATTRI2" VARCHAR2(2048),
				"EXTENDATTRI3" VARCHAR2(2048),
				PRIMARY KEY("ID")
			)
		';
end if;

SELECT COUNT(*) INTO v_count FROM MY_TABLES WHERE TABLE_NAME=UPPER('TASKHOSTINFO');
if v_count = 0 then
        execute immediate '
		   CREATE TABLE TASKHOSTINFO
			(
				"TASKID" NUMBER(20, 0) NOT NULL,
				"HOSTDN" VARCHAR2(512) NOT NULL,
				"STATUS" NUMBER(12, 0) NOT NULL,
				"RESULT" NUMBER(12, 0) NOT NULL,
				"HOSTINFO_COPY" VARCHAR2(640),
				PRIMARY KEY("TASKID", "HOSTDN")
			)
		';
end if;

SELECT COUNT(*) INTO v_count FROM MY_TABLES WHERE TABLE_NAME=UPPER('TASKLOG');
if v_count = 0 then
        execute immediate '
		   CREATE TABLE TASKLOG
			(
				"TASKID" NUMBER(20, 0) NOT NULL,
				"HOSTDN" VARCHAR2(512) NOT NULL,
				"OPREATETYPE" VARCHAR2(64) NOT NULL,
				"LOGTIME" NUMBER(20, 0) NOT NULL,
				"LOGSTRING" VARCHAR2(2048) NOT NULL
			)
		';
end if;

select COUNT(*) INTO  v_count from MY_TAB_COLUMNS  where UPPER(COLUMN_NAME)='LOGORDER' AND TABLE_NAME = UPPER('TASKLOG');
if v_count = 0 then
        execute immediate '
		    ALTER TABLE TASKLOG add (LOGORDER NUMBER(20, 0))
		';
end if;


SELECT COUNT(*) INTO v_count FROM MY_TABLES WHERE TABLE_NAME=UPPER('HOSTGROUP');
if v_count = 0 then
        execute immediate '
		  CREATE TABLE HOSTGROUP
			(
				"ID" NUMBER(20, 0) NOT NULL,
				"GROUPNAME" VARCHAR2(256) NOT NULL,
				"GROUPTYPE" NUMBER(12, 0) NOT NULL,
				"DN" VARCHAR2(512) NOT NULL,
				PRIMARY KEY("ID")
			)
		';
end if;

SELECT COUNT(*) INTO v_count FROM MY_TABLES WHERE TABLE_NAME=UPPER('HOSTINFO');
if v_count = 0 then
        execute immediate '
		  CREATE TABLE HOSTINFO
			(
				"DN" VARCHAR2(512) NOT NULL,
				"GROUPID" NUMBER(20, 0) NOT NULL,
				"IP" VARCHAR2(128) NOT NULL,
				"HOSTNAME" VARCHAR2(512) NOT NULL,
				"CHECK_TASK_STATUS" NUMBER(12, 0) NOT NULL,
				"CHECK_RESULT" NUMBER(12, 0) NOT NULL,
				"CHECK_ITEMRESULT" CLOB,
				"CHECK_USERID" VARCHAR2(512),
				"CHECK_USERNAME" VARCHAR2(512),
				"CHECK_STARTTIME" NUMBER(20, 0),
				"CHECK_ENDTIME" NUMBER(20, 0),
				"SS_RESULT" NUMBER(12, 0) NOT NULL,
				"AGENT_STATUS" NUMBER(12, 0) NOT NULL,
				"RPOCESS_STATUS" VARCHAR2(512),
				"SS_USERID" VARCHAR2(512),
				"SS_USERNAME" VARCHAR2(512),
				"SS_STARTTIME" NUMBER(20, 0),
				"SS_ENDTIME" NUMBER(20, 0),
				"SYNC_STARTTIME" NUMBER(20, 0),
				"SYNC_ENDTIME" NUMBER(20, 0),
				"EXTENDATTRI1" VARCHAR2(2048),
				"EXTENDATTRI2" VARCHAR2(2048),
				"EXTENDATTRI3" VARCHAR2(2048),
				"AGENT_VERSION" VARCHAR2(128),
				PRIMARY KEY("DN")
			)
		';
end if;

SELECT COUNT(*) INTO v_count FROM MY_TABLES WHERE TABLE_NAME=UPPER('HOST_SANDBOX');
if v_count = 0 then
        execute immediate '
          CREATE TABLE HOST_SANDBOX
            (
                "DN" VARCHAR2(512) NOT NULL,
	            "IP" VARCHAR2(128) NOT NULL,
	            "PKG_NAME_SHORT" VARCHAR2(256),
	            "PKG_NAME" VARCHAR2(256),
	            "PKG_CONTENT" CLOB,
	            "USER_MAPPING" VARCHAR2(1024),
	            "USER_MAPPING_DISPATCH" NUMBER(12, 0),
	            "STATUS" NUMBER(12, 0),
	            "DETAIL" VARCHAR2(1024),
	            "START_TIME" NUMBER(20, 0),
	            "END_TIME" NUMBER(20, 0),
                PRIMARY KEY("DN","PKG_NAME_SHORT")
            )
        ';
end if;
end;
/


DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_TPVIEW';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_TPVIEW
(
    id VARCHAR(128) not null,
    name VARCHAR(128),
    type VARCHAR(128),
    owner VARCHAR(128),
    description VARCHAR(256),
    pageUrl VARCHAR(1024),
    pageUrlPRI int,
    supportQueryByPermit int,
    PRIMARY KEY ( id )
)';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_TPBASICATTR';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_TPBASICATTR
(
    seqNO VARCHAR(128) not null,
    id VARCHAR(128) not null,
    dn VARCHAR(128),
    name VARCHAR(128),
    toClass VARCHAR(128),
    parentId VARCHAR(128),
    viewId VARCHAR(128),
    icon VARCHAR(512),
    image VARCHAR(512),
    PRIMARY KEY ( seqNO )
)';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_TPEXTENDATTR';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_TPEXTENDATTR
(
    seqNO VARCHAR(128) not null,
    fieldName VARCHAR(128) not null,
    dataType VARCHAR(128) not null,
    fieldValue VARCHAR(768)
)';
END IF;
END;
/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_TPSOLUTIONVIEW';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_TPSOLUTIONVIEW
(
	id VARCHAR(128) not null,
	name VARCHAR(128) not null,
	image VARCHAR(256) not null,
	url VARCHAR(512) not null,
	position int,
	filter VARCHAR(128),
	source VARCHAR(128),
        openType int,
	PRIMARY KEY ( id )
)';
END IF;
END;
/


/*==============================================================*/
/*               配置管理                                        */
/*==============================================================*/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SCH_INDEX';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE SCH_INDEX
(
    "dn"           VARCHAR(255) not null,
    "owner"        VARCHAR(64)  not null,
    "configItem"   VARCHAR(255) not null,
    "lastModified" NUMBER(20)   not null,
       primary key ("dn", "owner", "configItem")
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_ITEMINFO_MANAGER';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_ITEMINFO_MANAGER
(
    "dn"           VARCHAR(255)  not null,
    "contextDn"    VARCHAR(255)  not null,
    "owner"        VARCHAR(64)   not null,
    "configItem"   VARCHAR(255)  not null,
    "lastModified" NUMBER(20) not null,
    "referUrl"     VARCHAR(4000) not null,
    primary key ("dn", "contextDn", "owner", "configItem")
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_TEMPLATE_INFO';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_TEMPLATE_INFO
(
    NAME       VARCHAR(255) not null
        primary key,
    OWNER      VARCHAR(64)  not null,
    REMARKS    VARCHAR(1024),
    CREATETIME NUMBER(20,0) not null
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_SCHEDULE_BACKUP';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_SCHEDULE_BACKUP
(
    BACKUPFILENAME VARCHAR(255) not null
        primary key,
    CREATETIME     NUMBER(20,0) not null
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_SCHEDULE_VERSIONS';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_SCHEDULE_VERSIONS
(
    VERSIONID       NUMBER(20,0) not null
        primary key,
    DN              VARCHAR(255) not null,
    VERSIONNAME     VARCHAR(128),
    OPERATOR        VARCHAR(100),
    DESCRIPTION     VARCHAR(512),
    VERSIONTYPE     VARCHAR(128),
    CREATETIME      NUMBER(20,0),
    MODIFYTIME      NUMBER(20,0),
    MODIFYOPERATOR  VARCHAR(50),
    DATALOCATION    VARCHAR(512),
    VERSIONLEVEL    VARCHAR(24),
    BUSINESSVERSION VARCHAR(255)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_VERSION_STRUCTURE';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_VERSION_STRUCTURE
(
    ID        NUMBER(20,0) not null
        primary key,
    VERSIONID NUMBER(20,0),
    NAME      VARCHAR(255),
    ATTRTYPE  VARCHAR(255),
    MODIFIER  VARCHAR(255),
    TYPE      VARCHAR(64),
    DN        VARCHAR(255),
    VERSION   VARCHAR(128),
    VIRTUALDN VARCHAR(255)
)';
END IF;
END;
/

/*==============================================================*/
/*               iConfig                                        */
/*==============================================================*/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_CONFIG_FIELDS';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_CONFIG_FIELDS
(
    "doid"           VARCHAR(255)  not null,
    "user"         VARCHAR(255)  not null,
    "displayColumns" VARCHAR(1024) not null,
    primary key ("doid", "user")
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_ICNFGDNTODEVID';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_ICNFGDNTODEVID
(
	DN VARCHAR(255) NOT NULL,
	DEVID NUMERIC(7,0) NOT NULL,
	PRIMARY KEY (DEVID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_ICNFGMODULETYPE';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_ICNFGMODULETYPE
(
	DN VARCHAR(255) NOT NULL,
	NAME VARCHAR(128) NOT NULL,
	MODULETYPEID VARCHAR(10) NOT NULL,
	MODULETYPENAME VARCHAR(128) NOT NULL,
	NEDN VARCHAR(255) NOT NULL,
	NEID VARCHAR(10) NOT NULL,
	NETYPE VARCHAR(10) NOT NULL,
	VERSION VARCHAR(64) NOT NULL,
	PRIMARY KEY (DN)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_SOAPAPPINFO';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_SOAPAPPINFO
(
	APPID VARCHAR(128) NOT NULL,
	APPTYPE VARCHAR(32),
	APPADDRESS VARCHAR(64),
	REGDATETIME NUMERIC(19),
	NOTIFYURL VARCHAR(1024),
	SUPERTOKEN VARCHAR(255),
	PRIMARY KEY (APPID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'DEVICEINFO';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE DEVICEINFO
(
	DEVID NUMBER(10,10) NOT NULL,
	DEVNAME VARCHAR(383),
	SUBNETID NUMBER(10,10),
	IPADDR VARCHAR(50),
	DEVTYPE VARCHAR(255),
	VERSION VARCHAR(383),
	NETYPE VARCHAR(32),
	NEID NUMBER(10,10),
	NENAME VARCHAR(255),
	MODULETYPE VARCHAR(255),
	MODULECODE VARCHAR(255),
	MODULENAME VARCHAR(255),
	TOPONODETYPE NUMBER(10,10),
	INSTANCEID VARCHAR(255),
	PROTOCOL NUMBER(10,10),
	PROTOCOLVERSION VARCHAR(64),
	BELONGSYSTEMID NUMBER(10,10),
	PRODUCTID VARCHAR(255),
	PRODUCTVERSION VARCHAR(255),
	STATUS NUMBER(10,10),
	PRIMARY KEY (DEVID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'DEVICEPARAM';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE DEVICEPARAM
(
	DEVID NUMBER(10,10) NOT NULL,
	PROTOCOL NUMBER(10,10) NOT NULL,
	VERSION VARCHAR(64) NOT NULL,
	PARAMKEY VARCHAR(64) NOT NULL,
	PARAMVALUE VARCHAR(255),
	STATUS NUMBER(10,10),
	PRIMARY KEY (DEVID,PARAMKEY,PROTOCOL,VERSION)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SUBNET';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE SUBNET
(
	SUBNETID NUMBER(10,10) NOT NULL,
	SUBNETNAME VARCHAR(383),
	PARENTID NUMBER(10,10),
	STATUS NUMBER(10,10),
	REMARK VARCHAR(383),
	PRIMARY KEY (SUBNETID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'CIHISTORY';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE CIHISTORY
(
	USERNAME VARCHAR(32) NOT NULL,
	FUNID VARCHAR(255) NOT NULL,
	VIEWID VARCHAR(32) NOT NULL,
	PRODUCTID VARCHAR(32) NOT NULL,
	PRODUCTVERSION VARCHAR(64) NOT NULL,
	MODULEID VARCHAR(32) NOT NULL,
	MODULEVERSION VARCHAR(32),
	NEDEVID VARCHAR(16) NOT NULL,
	LASTTIME NUMBER(14,19),
	PRIMARY KEY (FUNID,MODULEID,NEDEVID,PRODUCTID,PRODUCTVERSION,USERNAME,VIEWID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'UOATOPOMT';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE UOATOPOMT
(
	UOAID NUMBER(10,10) NOT NULL,
	NEID NUMBER(10,10) NOT NULL,
	MODULETYPE VARCHAR(255) NOT NULL,
	MODULENAME VARCHAR(64),
	MODULEVERSION VARCHAR(255) NOT NULL,
	CONTEXTNAME VARCHAR(255),
	CONTEXTENGINEID VARCHAR(255),
	STATUS NUMBER(10,10),
	I2KDEVID NUMBER(10,10),
	I2KTYPE NUMBER(10,10),
	PRIMARY KEY (MODULETYPE,MODULEVERSION,NEID,UOAID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'UOATOPONE';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE UOATOPONE
(
	UOAID NUMBER(10,10) NOT NULL,
	NEID NUMBER(10,10) NOT NULL,
	NETYPE NUMBER(10,10),
	NENAME VARCHAR(255),
	NEVERSION VARCHAR(255),
	NEWORKMODE NUMBER(10,10),
	NEWORKSTATUS NUMBER(10,10),
	IPADDR VARCHAR(50),
	HOSTNAME VARCHAR(255),
	CONTEXTNAME VARCHAR(255),
	CONTEXTENGINEID VARCHAR(255),
	SYSID NUMBER(10,10),
	SYSNAME VARCHAR(255),
	STATUS NUMBER(10,10),
	I2KDEVID NUMBER(10,10),
	I2KTYPE NUMBER(10,10),
	PRIMARY KEY (NEID,UOAID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'USERLOG';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE USERLOG
(
    ID           NUMBER(14,19) not null
        primary key,
    ACCURATETIME NUMBER(14,19)
        constraint IDX_FLAG
        unique,
    RECORDID     VARCHAR(254),
    USERNAME     VARCHAR(300),
    TERMINAL     VARCHAR(60),
    BEGINDATE    NUMBER(10,10)
        constraint IDX_BEGINDATE
        unique,
    CATEGORY     VARCHAR(60),
    OPERATION    VARCHAR(180),
    TARGETOBJ    VARCHAR(300),
    RESULT       VARCHAR(60),
    ADDINFO      VARCHAR(4000),
    ENDDATE      NUMBER(10,10)
        constraint IDX_ENDDATE
        unique
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SYSLOG';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE SYSLOG
(
    ID        NUMBER(14,19) not null
        primary key,
    RECORDID  VARCHAR(254),
    LEVAL     VARCHAR(90),
    "MODULE"  VARCHAR(90),
    TARGETOBJ VARCHAR(300)
        constraint IDX_TARGETOBJ
            unique,
    "TIME"    NUMBER(10,10)
        constraint IDX_TIME
        unique,
    CONTENT   VARCHAR(383),
    BASEINFO  VARCHAR(383),
    BAK1      VARCHAR(383),
    BAK2      VARCHAR(383),
    BAK3      NUMBER(10,10),
    BAK4      NUMBER(10,10),
    RESULT    VARCHAR(383),
    constraint IDX_MODULE
        unique ("MODULE", LEVAL)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'PAGETBL';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE PAGETBL
(
    ID       NUMBER(14,19) not null
        primary key,
    USERNAME VARCHAR(32)
        constraint IDX_PAGETBL
            unique,
    PAGESIZE NUMBER(10,10)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'MYOFTENMENU';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE MYOFTENMENU
(
	USERNAME VARCHAR(32) NOT NULL,
	VALUE VARCHAR(512),
    PRIMARY KEY (USERNAME)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'MYCONFIGMGRMENU';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE MYCONFIGMGRMENU
(
	USERNAME VARCHAR(32) NOT NULL,
	PRODUCTID VARCHAR(32) NOT NULL,
	PRODUCTVERSION VARCHAR(64) NOT NULL,
	VALUE VARCHAR(512),
	DEFAULTVALUE VARCHAR(64),
	PRIMARY KEY (PRODUCTID,PRODUCTVERSION,USERNAME)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'CONFIGED_ITEM';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE CONFIGED_ITEM
(
    USERNAME       VARCHAR(32)  not null,
    FUNID          VARCHAR(255) not null,
    DEVID          NUMBER(10,10) not null,
    MODULENEID     NUMBER(10,10),
    NEID           NUMBER(10,10),
    PRODUCTID      VARCHAR(64),
    PRODUCTVERSION VARCHAR(64),
    MODULEID       VARCHAR(64),
    MODULEVERSION  VARCHAR(64),
    "COUNT"        NUMBER(10,10),
        primary key (USERNAME, FUNID, DEVID),
    constraint IDX_CONFIGED_ITEM
        unique (USERNAME, PRODUCTID, PRODUCTVERSION),
    constraint IDX_MODULENEID_CONFIGED_ITEM
        unique (USERNAME, MODULENEID),
    constraint IDX_NEID_CONFIGED_ITEM
        unique (USERNAME, NEID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'CFGSTRUCTURE4VER';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE CFGSTRUCTURE4VER
(
	ID NUMBER(10,10) NOT NULL,
	VERSIONID NUMBER(10,10),
	NAME VARCHAR(255),
	ATTRTYPE VARCHAR(255),
	MODIFIER VARCHAR(32),
	PARENTID NUMBER(14,19),
	TYPE VARCHAR(64),
	DEVID NUMBER(10,10),
	VERSION VARCHAR(128),
	VIRTUALDEVID NUMBER(10,10),
	PRIMARY KEY (ID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'CFGVERSIONS';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE CFGVERSIONS
(
	VERSIONID NUMBER(10,10) NOT NULL,
	DEVID NUMBER(10,10),
	LOCKOPERATOR VARCHAR(64),
	LOCKTIME NUMBER(14,19),
	VERSIONNAME VARCHAR(128),
	DESCRIPTION VARCHAR(512),
	VERSIONTYPE VARCHAR(128),
	FILEFORMATVERSION VARCHAR(255),
	SENDERNAME VARCHAR(512),
	VENDORNAME VARCHAR(64),
	FOOTERDATETIME VARCHAR(64),
	XMLNAMESPACE VARCHAR(255),
	CREATETIME NUMBER(14,19),
	MODIFYTIME NUMBER(14,19),
	MODIFYOPERATOR VARCHAR(255),
	HASGENXML VARCHAR(24),
	GENXMLTIME NUMBER(14,19),
	XMLFILENAME VARCHAR(64),
	VERSIONLEVEL VARCHAR(24),
	PRIMARY KEY (VERSIONID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TABLESEQUENCE';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TABLESEQUENCE
(
	TABLENAME VARCHAR(255) NOT NULL,
	LONG NUMBER(14,19),
	NEXTID NUMBER(10,10),
	PRIMARY KEY (TABLENAME)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'VERSTATEINFO';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE VERSTATEINFO
(
	ID NUMBER(14,19) NOT NULL,
	VERSIONID VARCHAR(64),
	STATE NUMBER(10,10),
	REFID VARCHAR(64),
	USERNAME VARCHAR(64),
	USEFULLIFE NUMBER(14,19),
	PRIMARY KEY (ID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'LOCKINFO';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE LOCKINFO
(
	ID NUMBER(14,19) NOT NULL,
	DEVID VARCHAR(64),
	USERNAME VARCHAR(64),
	LOCKTIME NUMBER(10,10),
	PRIMARY KEY (ID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SMUSERS';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE SMUSERS
(
	USERID VARCHAR(50) NOT NULL,
	PASSWORD VARCHAR(254),
	DESCRIPTION VARCHAR(383),
	UESRTYPE NUMBER(10,10),
	AVAILABILITY NUMBER(10,10),
	EXPIRYDATE VARCHAR(255),
	MUSTCHANGEPWD NUMBER(10,10),
	ISFIRSTLOGIN NUMBER(10,10),
	BINDIPS VARCHAR(380),
	ISDEFAULTUSER NUMBER(10,10),
	ISLOCKED VARCHAR(30),
	LASTLOGINTIME VARCHAR(255),
	FAILEDLOGINCOUNT NUMBER(10,10),
	ISQUESTION NUMBER(10,10),
	QUESTION VARCHAR(50),
	ANSWER VARCHAR(50),
	PRIMARY KEY (USERID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SMUSERGROUPS';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE SMUSERGROUPS
(
	GROUPID NUMBER(10,10) NOT NULL,
	GROUPNAME VARCHAR(50),
	DESCRIPTION VARCHAR(255),
	ISDEFAULTGROUP NUMBER(10,10),
	PRIMARY KEY (GROUPID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SMUSERGROUPRELATIONS';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE SMUSERGROUPRELATIONS
(
	USERID VARCHAR(50) NOT NULL,
	GROUPID NUMBER(10,10) NOT NULL,
	PRIMARY KEY (GROUPID,USERID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SMRIGHTRELATIONS';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE SMRIGHTRELATIONS
(
    SRCRIGHTID    VARCHAR(255) not null,
    TARGETRIGHTID VARCHAR(255) not null
        constraint IDX_SMRIGHTRELATIONS_TARID
            unique,
    RELATIONTYPE  NUMBER(10,10),
        primary key (SRCRIGHTID, TARGETRIGHTID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SMRIGHTSINFO';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE SMRIGHTSINFO
(
	RIGHTID VARCHAR(255) NOT NULL,
	SOCLASSNAME VARCHAR(50),
	RIGHTTYPE NUMBER(10,10),
	PRIMARY KEY (RIGHTID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SMUSERGROUPRIGHTS';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE SMUSERGROUPRIGHTS
(
	GROUPID NUMBER(10,10) NOT NULL,
	SOINSTANCE VARCHAR(50) NOT NULL,
	RIGHTID VARCHAR(255) NOT NULL,
	PRIMARY KEY (GROUPID,RIGHTID,SOINSTANCE)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SYSBULLETIN';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE SYSBULLETIN
(
	ID NUMBER(10,10) NOT NULL,
	TITLE VARCHAR(50),
	CONTENT VARCHAR(500),
	PUBLISHER VARCHAR(50),
	PUBLISHTIME VARCHAR(50),
	PRIMARY KEY (ID)
)';
END IF;
END;
/

/*==============================================================*/
/*                    批量导入                                   */
/*==============================================================*/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_IMPORTTASK';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_IMPORTTASK
(
	TASKID NUMBER(20, 0) NOT NULL,
	TASKTYPE NUMBER(1, 0) NOT NULL,
	TASKNAME VARCHAR2(128) NOT NULL,
	TASKSTATUS NUMBER(2, 0) NOT NULL,
	CREATETIME NUMBER(20, 0),
	FINISHEDTIME NUMBER(20, 0),
	ALLCOUNT NUMBER(20, 0) NOT NULL,
	SUCCESSCOUNT NUMBER(20, 0) NOT NULL,
	OPERATOR VARCHAR2(64) NOT NULL,
	RESULT NUMBER(1, 0) NOT NULL,
	PROGRESS NUMBER(8, 0),
	DESCRIPTION VARCHAR2(256),
	USERCANCANCEL NUMBER(1, 0),
	FAILONERROR NUMBER(1, 0),
	PRIMARY KEY (TASKID)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_IMPORTINGNODE';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_IMPORTINGNODE
(
	TASKID NUMBER(20, 0) NOT NULL,
	ELEMENTID NUMBER(20, 0) NOT NULL,
	NAME VARCHAR2(128),
	MOTYPE VARCHAR2(128),
	NECATEGORY NUMBER(5, 0),
	DN VARCHAR2(255),
	PARENTDN VARCHAR2(128),
	MAINTAINADDRESS VARCHAR2(32),
	ACCESSGATEWAY VARCHAR2(64),
	STATUS NUMBER(5, 0) NOT NULL,
	ERRORDETAIL VARCHAR2(2000),
	DESCRIPTION VARCHAR2(256),
	PARENTNAME VARCHAR2(256),
	RESULT VARCHAR2(32),
	TIMEZONE VARCHAR2(32),
	CLIENTPROPERTIES CLOB,
    TENANTID VARCHAR2(32),
	PRIMARY KEY (ELEMENTID)
)';
END IF;
IF V_CNT > 0 THEN
EXECUTE IMMEDIATE
'ALTER TABLE TBL_IMPORTINGNODE MODIFY DN VARCHAR2(255)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_AGENTINFO';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AGENTINFO
(
	TASKID NUMBER(20, 0) NOT NULL,
	ELEMENTID NUMBER(20, 0) NOT NULL,
	AGENTCONTENT CLOB,
	URL VARCHAR2(128)
)';
END IF;
END;
/

/*==============================================================*/
/*                    告警相关性规则                              */
/*==============================================================*/
DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_ALARMRELATION';
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_ALARMRELATION
(
	SOURCE_MODN VARCHAR(255) NOT NULL,
	SOURCE_ALARMSN INTEGER NOT NULL,
	SOURCE_ALARMID INTEGER NOT NULL,
	SOURCE_ALARMDEVSN INTEGER NOT NULL DEFAULT -1,
	RELATION_TYPE INTEGER NOT NULL,
	TARGET_MODN VARCHAR(255) NOT NULL,
	TARGET_ALARMSN INTEGER NOT NULL,
	TARGET_ALARMDEVSN INTEGER NOT NULL DEFAULT -1,
	TARGET_ALARMID INTEGER NOT NULL,
        PRIMARY KEY (SOURCE_MODN,  SOURCE_ALARMSN, TARGET_MODN, TARGET_ALARMSN, RELATION_TYPE)
)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_FM_ALARMCOLUMN_I2000');
IF V_CNT > 0 THEN
EXECUTE IMMEDIATE
'DROP TABLE IF EXISTS TBL_FM_ALARMCOLUMN_I2000';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_LATESTALARMS');
IF V_CNT > 0 THEN
EXECUTE IMMEDIATE
'DROP TABLE IF EXISTS TBL_LATESTALARMS';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('QUERY_CONDITION_I2000');
IF V_CNT > 0 THEN
EXECUTE IMMEDIATE
'DROP TABLE IF EXISTS QUERY_CONDITION_I2000';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('QUERY_CONDITIONNEALARM_I2000');
IF V_CNT > 0 THEN
EXECUTE IMMEDIATE
'DROP TABLE IF EXISTS QUERY_CONDITIONNEALARM_I2000';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'ALARM_SEVERITY_RULE';
IF V_CNT > 0 THEN
EXECUTE IMMEDIATE
'UPDATE ALARM_SEVERITY_RULE T1 SET T1.RULENAME = (SELECT T.ALARMNAME FROM ALARM_STATIC_INFO T WHERE T1.ALARMID=T.ALARMID AND T.GROUPID IN (SELECT T2.GROUPID FROM TBL_STATICINFO_MAPPING T2 WHERE T2.MOTYPE = T1.MOTYPE)) WHERE T1.RULENAME IS NULL';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'T_HOMEPAGE_CUSTOM';
IF V_CNT > 0 THEN
EXECUTE IMMEDIATE
'UPDATE T_HOMEPAGE_CUSTOM SET CONFIG = replace(CONFIG,''i2000.fm.extension.portlet_statAlarm'',''oms.fm.extension.portlet_statAlarm''),EXTEND=''portlet_oms.fm.extension.portlet_statAlarm'' where CONFIG LIKE ''%\"i2000.fm.extension.portlet_statAlarm\"%''';
EXECUTE IMMEDIATE
'UPDATE T_HOMEPAGE_CUSTOM SET CONFIG = replace(CONFIG,''i2000.fm.extension.portlet'',''oms.fm.extension.portlet!1420041600000''),EXTEND=''portlet_oms.fm.extension.portlet!1420041600000'' where CONFIG LIKE ''%\"i2000.fm.extension.portlet\"%''';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'ALARM_STATIC_INFO';
IF V_CNT > 0 THEN
EXECUTE IMMEDIATE
'DELETE FROM ALARM_STATIC_INFO WHERE GROUPID = ''100000'' AND ALARMNAME LIKE ''%Veritas%''';
END IF;
END;
/

DECLARE 
V_CNT NUMBER;
V_NUM NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SHIELDRULE_ALARM';
IF V_CNT > 0 THEN
    SELECT COUNT(*) INTO V_NUM FROM MY_TABLES WHERE TABLE_NAME = 'SHIELDRULE_MAIN';
    IF V_NUM > 0 THEN
   EXECUTE IMMEDIATE
    'DELETE FROM SHIELDRULE_ALARM WHERE SHIELDRULE_ID NOT IN (SELECT SHIELDRULE_ID FROM SHIELDRULE_MAIN)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''SRCA'',60150015 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''SRCA'',60150026 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''SRCA'',60210011 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''SRCA'',60210036 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''SRCA'',60210044 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''SRCA'',490081022 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''inf.host.blade.atae'',60150015 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''inf.host.blade.atae'',60150026 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''inf.host.blade.atae'',60210011 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''inf.host.blade.atae'',60210036 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''inf.host.blade.atae'',60210044 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''inf.host.blade.atae'',490081022 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''inf.virtual.vm.bmu.suse'',60210036 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_ALARM(SHIELDRULE_ID, MOTYPE, ALARM_ID) SELECT 99999,''inf.virtual.vm.bmu.suse'',60150026 FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    END IF;
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SHIELDRULE_EXTENDS';
IF V_CNT > 0 THEN
    DECLARE V_NUM NUMBER;
BEGIN
SELECT COUNT(*) INTO V_NUM FROM MY_TABLES WHERE TABLE_NAME = 'SHIELDRULE_MAIN';
IF V_NUM > 0 THEN
    EXECUTE IMMEDIATE
    'DELETE FROM SHIELDRULE_EXTENDS WHERE SHIELDRULE_ID NOT IN (SELECT SHIELDRULE_ID FROM SHIELDRULE_MAIN)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_EXTENDS(SHIELDRULE_ID, SHIELD_TYPE, SHIELD_VALUE) SELECT 99999, ''OBJECTINSTANCE'', ''''FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_EXTENDS(SHIELDRULE_ID, SHIELD_TYPE, SHIELD_VALUE) SELECT 99999, ''OBJECTINSTANCE_OPT'', ''0''FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_EXTENDS(SHIELDRULE_ID, SHIELD_TYPE, SHIELD_VALUE) SELECT 99999, ''ALARMNAME'', ''''FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_EXTENDS(SHIELDRULE_ID, SHIELD_TYPE, SHIELD_VALUE) SELECT 99999, ''ALARMNAME_OPT'', ''0''FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_EXTENDS(SHIELDRULE_ID, SHIELD_TYPE, SHIELD_VALUE) SELECT 99999, ''ADDITIONALINFO'', ''''FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_EXTENDS(SHIELDRULE_ID, SHIELD_TYPE, SHIELD_VALUE) SELECT 99999, ''ADDITIONALINFO_OPT'', ''0''FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_EXTENDS(SHIELDRULE_ID, SHIELD_TYPE, SHIELD_VALUE) SELECT 99999, ''DESCRIPTION'', ''''FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_EXTENDS(SHIELDRULE_ID, SHIELD_TYPE, SHIELD_VALUE) SELECT 99999, ''DURATION_OPT'', ''8''FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_EXTENDS(SHIELDRULE_ID, SHIELD_TYPE, SHIELD_VALUE) SELECT 99999, ''DURATION'', ''300''FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_EXTENDS(SHIELDRULE_ID, SHIELD_TYPE, SHIELD_VALUE) SELECT 99999, ''LASTMODIFITIME'',(SELECT (SYSDATE - 8 / 24 - TO_DATE(''1970-01-01'', ''yyyy-mm-dd'')) * 86400000 CURRENT_MILLI FROM SYS_DUMMY) FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID = 99999)';
END IF;
END;
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SHIELDRULE_NE';
IF V_CNT > 0 THEN
    DECLARE V_NUM NUMBER;
BEGIN
SELECT COUNT(*) INTO V_NUM FROM MY_TABLES WHERE TABLE_NAME = 'SHIELDRULE_MAIN';
IF V_NUM > 0 THEN
    EXECUTE IMMEDIATE
    'DELETE FROM SHIELDRULE_NE WHERE SHIELDRULE_ID NOT IN (SELECT SHIELDRULE_ID FROM SHIELDRULE_MAIN)';
    EXECUTE IMMEDIATE
    'INSERT INTO SHIELDRULE_NE(SHIELDRULE_ID, DN, MOTYPE) SELECT 99999, ''/'', ''/''FROM SYS_DUMMY WHERE NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID=99999)';
END IF;
END;
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'SHIELDRULE_MAIN';
IF V_CNT > 0 THEN
EXECUTE IMMEDIATE
'INSERT INTO SHIELDRULE_MAIN(SHIELDRULE_ID, SHIELDRULE_NAME, ENABLE, START_DATE, END_DATE, RULE_TYPE) SELECT 99999, ''I2000MainRule'', 1, '''', '''', 0 FROM SYS_DUMMY WHERE  NOT EXISTS (SELECT * FROM SHIELDRULE_MAIN WHERE SHIELDRULE_ID=99999)';
END IF;
END;
/

DECLARE V_CNT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = 'TBL_STATICINFO_MAPPING';
IF V_CNT > 0 THEN
EXECUTE IMMEDIATE
'DELETE FROM TBL_STATICINFO_MAPPING  T1 WHERE (T1.MOTYPE,T1.GROUPID) IN (SELECT MOTYPE,GROUPID FROM TBL_STATICINFO_MAPPING GROUP BY MOTYPE,GROUPID HAVING COUNT(*)>1) AND ROWID NOT IN (SELECT MIN(ROWID) FROM TBL_STATICINFO_MAPPING GROUP BY MOTYPE,GROUPID HAVING COUNT(*)>1)';
END IF;
END;
/
