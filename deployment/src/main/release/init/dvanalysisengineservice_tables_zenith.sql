DECLARE V_CNT NUMBER;
BEGIN

/*==============================================================*/
/* Table: "TBL_AIOPS_TASK_EXECUTION_RESULT"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TASK_EXECUTION_RESULT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TASK_EXECUTION_RESULT (
  TASK_ID INTEGER NOT NULL ,
  START_TIME BIGINT NOT NULL ,
  END_TIME BIGINT,
  STATUS VARCHAR(32) NOT NULL ,
  TASK_PERIOD_TYPE VARCHAR(32) NOT NULL,
  PARTIAL_RECORDS CLOB,
  FAILURE_CAUSE CLOB,
  CONSTRAINT TBL_AIOPS_TASK_EXECUTION_RESULT PRIMARY KEY (TASK_ID,START_TIME,TASK_PERIOD_TYPE)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_INDICATOR_OUTLIER"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_INDICATOR_OUTLIER');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_INDICATOR_OUTLIER (
  ID VARCHAR(255) NOT NULL ,
  TASK_ID INTEGER NOT NULL ,
  INDICATOR_ID VARCHAR(1024) NOT NULL ,
  START_TIME BIGINT NOT NULL ,
  END_TIME BIGINT,
  DURATION BIGINT,
  CORRECT NUMBER(1) NOT NULL ,
  PROBABLE_CAUSE VARCHAR(1024) ,
  SWITCH_ALARM_FIELD VARCHAR(256) ,
  ALARM_SHIELD BOOLEAN ,
  CONSTRAINT TBL_AIOPS_INDICATOR_OUTLIER PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_INDICATOR_MISS_DATA_POINT_OUTLIER"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_INDICATOR_MISS_DATA_POINT_OUTLIER');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_INDICATOR_MISS_DATA_POINT_OUTLIER (
  ID VARCHAR(255) NOT NULL ,
  TASK_ID INTEGER NOT NULL ,
  INDICATOR_ID VARCHAR(1024) NOT NULL ,
  START_TIME BIGINT NOT NULL ,
  END_TIME BIGINT,
  PROBABLE_CAUSE VARCHAR(1024) ,
  SWITCH_ALARM_FIELD VARCHAR(256) ,
  ALARM_SHIELD BOOLEAN ,
  ALARM_RECORD CLOB,
  CONSTRAINT TBL_AIOPS_INDICATOR_MISS_DATA_POINT_OUTLIER PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_ANALYSIS_TASK"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_ANALYSIS_TASK (
  TASK_ID INTEGER AUTO_INCREMENT NOT NULL,
  TASK_DESC VARCHAR(2048),
  TASK_NAME VARCHAR(256) NOT NULL,
  SITUATION_ID BINARY_BIGINT,
  TASK_TYPE BINARY_INTEGER NOT NULL,
  PERIODIC_TYPE BINARY_INTEGER,
  PREDICT_CRON VARCHAR(256),
  TRAIN_CRON VARCHAR(256),
  SOLUTION_ID VARCHAR(256),
  SOLUTION_NAME VARCHAR(256),
  TRAIN_STATUS BINARY_INTEGER,
  START_STATUS BINARY_INTEGER,
  REPORT_ALARM BINARY_INTEGER,
  ALARM_TYPE BINARY_INTEGER,
  BEGIN_TIME BINARY_BIGINT,
  END_TIME BINARY_BIGINT,
  INDICATOR_SELECT_TYPE BINARY_INTEGER,
  UPDATE_TIME BINARY_BIGINT NOT NULL,
  ALGORITHM_MODEL_ID VARCHAR(256),
  ALGORITHM_MODEL_NAME VARCHAR(256),
  ALGORITHM_PARAM CLOB,
  DATASOURCE_ID VARCHAR(256),
  PATH VARCHAR(256),
  TASK_DETAIL VARCHAR(1024),
  ADDITION VARCHAR(256),
  USER_ID VARCHAR(256) NOT NULL,
  LAST_MODIFY_USER_ID VARCHAR(256),
  LAST_EXECUTOR_USER_ID VARCHAR(256),
  ALARM_TYPE_LIST CLOB,
  ALARM_SELECT_TYPE BINARY_INTEGER,
  ALARM_SELECT_LIST CLOB,
  ALARM_SELECT_GROUP_LIST CLOB,
  INDICATOR_PREDICT_NUM BINARY_BIGINT,
  INDICATOR_TRAIN_NUM BINARY_BIGINT,
  ALARM_PREDICT_TIME VARCHAR(50),
  ALARM_TRAIN_TIME VARCHAR(50),
  LOG_PREDICT_TIME VARCHAR(50),
  LOG_TRAIN_TIME VARCHAR(50),
  REPORT_ALARM_NAME VARCHAR(256),
  REPORT_ALARM_ID VARCHAR(256),
  IMPORT_TASK_STATUS BOOLEAN,
  GROUP_LEVEL INTEGER,
  TRAIN_ADDRESS VARCHAR(255),
  PREDICT_ADDRESS VARCHAR(255),
  INDICATOR_PREDICT_SCENARIO INTEGER,
  SUB_SITE VARCHAR(255),
  AGGREGATE_MACHINE_GROUP_LIST CLOB,
  SEND_EVENT BOOLEAN,
  SEND_EVENT_LEVEL INTEGER,
  SEND_EVENT_CONTENT VARCHAR(2048),
  SEND_EVENT_ALARM_CONTENT VARCHAR(2048),
  ALARM_TASK_TYPE VARCHAR(128),
  UPDATE_INDICATOR_AUTO BOOLEAN,
  INDICATOR_DISCARD_TIME INTEGER,
  ALARM_NUMBER_THRESHOLD INTEGER,
  EVENT_DURATION_THRESHOLD INTEGER,
  USE_ORIGINAL_ALARM_ID BOOLEAN,
  INDICATOR_TASK_TYPE INTEGER,
  TEMPLATE_TASK_ID VARCHAR(255),
  CONSTRAINT TBL_AIOPS_ANALYSIS_TASK PRIMARY KEY (TASK_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_SITUATION"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_SITUATION');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_SITUATION (
  ID  INTEGER AUTO_INCREMENT NOT NULL ,
  SITUATION_NAME VARCHAR(256) NOT NULL ,
  CONSTRAINT TBL_AIOPS_SITUATION PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_SITUATION"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_PROMETHEUS');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_PROMETHEUS (
      TASK_ID INTEGER NOT NULL,
      PQLS VARCHAR(2048),
      STEP VARCHAR(256) NOT NULL,
      DATASOURCE_ID INTEGER,
      CONSTRAINT TBL_AIOPS_PROMETHEUS PRIMARY KEY (TASK_ID)
)';
END IF;

/*==============================================================*/

/*==============================================================*/
/* Table: "TBL_AIOPS_ALARMCOMPRESS_RULES"                       */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_ALARMCOMPRESS_RULES');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_ALARMCOMPRESS_RULES (
    RULE_ID INTEGER AUTO_INCREMENT NOT NULL ,
    ALARM_ID VARCHAR(255) NOT NULL,
    TASK_ID INTEGER NOT NULL,
	RULE_NAME VARCHAR(255) NOT NULL,
    LOCATIONINFOKEY VARCHAR(512),
    ADDITIONINFOKEY VARCHAR(512),
    MODIFY_TIME BIGINT default 0,
    CONSTRAINT PK_TBL_AIOPS_ALARMCOMPRESS_RULES PRIMARY KEY (RULE_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_COMPRESS_GROUP_RULE"                       */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_COMPRESS_GROUP_RULE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_COMPRESS_GROUP_RULE (
    ID INTEGER AUTO_INCREMENT NOT NULL,
    TASK_ID INTEGER,
    RULE_NAME VARCHAR(512),
	GROUP_DETAIL CLOB,
    TEMP_GROUP_DETAIL CLOB,
    MOUNT_POINT VARCHAR(256),
    MOUNT_POINT_NAME VARCHAR(256),
    GROUP_SATUS VARCHAR(256),
    GROUP_SATUS_DESC VARCHAR(512),
    DESCRIPTION VARCHAR(1024),
    UPDATE_TIME BINARY_BIGINT default 0,
    RULE_TYPE VARCHAR(256),
    SOLUTION_TYPE VARCHAR(512),
    SOLUTION_TYPE_NAME VARCHAR(512),
    CONSTRAINT TBL_AIOPS_COMPRESS_GROUP_RULE PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/

/* Table: "TBL_AIOPS_DATASOURCE"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_DATASOURCE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_DATASOURCE (
ID INTEGER AUTO_INCREMENT NOT NULL,
TYPE VARCHAR(256) NOT NULL,
NAME VARCHAR(256) NOT NULL,
DESCRIPTION VARCHAR(256),
RECENTLY_TIME BIGINT NOT NULL,
IP VARCHAR(700),
PORT VARCHAR(256),
USER_NAME VARCHAR(256),
PASSWD VARCHAR(256),
SERVICE_ID VARCHAR(256),
SID VARCHAR(256),
STATUS BOOLEAN,
CREATE_USER VARCHAR(256),
TENANT_ID VARCHAR(256),
DATASOURCE_TYPE INTEGER,
CONSTRAINT TBL_AIOPS_DATASOURCE PRIMARY KEY (ID)
 )';
END IF;
/*==============================================================*/
/* Table: "TBL_AIOPS_CUSTOM_INDICATOR_MAPPING"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_CUSTOM_INDICATOR_MAPPING');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_CUSTOM_INDICATOR_MAPPING (
  DATA_SOURCE_ID INTEGER NOT NULL,
  TASK_ID INTEGER NOT NULL,
  SQL VARCHAR(8000) NOT NULL,
  INDICATOR_KEY VARCHAR(1024) NOT NULL,
  ORIGINAL_VALUE_KEY VARCHAR(1024),
  MEAS_UNIT_KEY VARCHAR(1024),
  TIME_KEY VARCHAR(100) NOT NULL,
  IP_KEY VARCHAR(50),
  DN_KEY VARCHAR(50),
  CONSTRAINT TBL_AIOPS_CUSTOM_INDICATOR_MAPPING PRIMARY KEY (DATA_SOURCE_ID, TASK_ID)
)';
END IF;
/*==============================================================*/
/* Table: "TBL_AIOPS_TASK_INDICATOR"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TASK_INDICATOR (
  INDICATOR_ID VARCHAR(1024) NOT NULL,
  TASK_ID INTEGER NOT NULL,
  ABNORMAL NUMBER(1),
  MEAS_UNIT_KEY VARCHAR(256),
  MEAS_UNIT_NAME VARCHAR(600),
  MEAS_TYPE_KEY VARCHAR(256),
  DN VARCHAR(256),
  INDEX_NAME VARCHAR(600),
  MO_TYPE VARCHAR(256),
  DISPLAY_VALUE VARCHAR(8000),
  ORIGINAL_VALUE VARCHAR(600),
  UNIT VARCHAR(256),
  DN_NAME VARCHAR(256),
  CHECKED_NET_ID VARCHAR(2048),
  HAS_MEAS_OBJ VARCHAR(5),
  INDEX_ID VARCHAR(256),
  RESOURCE_TYPE_KEY VARCHAR(256),
  PQL VARCHAR(8000),
  SOLUTION_ID VARCHAR(256),
  GROUP_ID VARCHAR(256),
  RESOURCE_GROUP VARCHAR(256),
  HISTORY_INDICATOR_ID INTEGER,
  HISTORY_INDICATOR_NAME VARCHAR(1800),
  SOFT_DELETE BOOLEAN,
  SOFT_DELETE_TIMESTAMP BIGINT,
  DEPLOYMENT_MO_TYPE VARCHAR(256),
  INDICATOR_TASK_TYPE INTEGER,
  CONSTRAINT TBL_AIOPS_TASK_INDICATOR PRIMARY KEY (INDICATOR_ID, TASK_ID)
)';
END IF;
/*==============================================================*/
/* Table: "TBL_AIOPS_TASK_INDICATOR_UPDATE"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR_UPDATE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TASK_INDICATOR_UPDATE (
  INDICATOR_ID VARCHAR(1024) NOT NULL,
  TASK_ID INTEGER NOT NULL,
  MEAS_UNIT_KEY VARCHAR(256),
  MEAS_UNIT_NAME VARCHAR(600),
  MEAS_TYPE_KEY VARCHAR(256),
  DN VARCHAR(256),
  INDEX_NAME VARCHAR(600),
  MO_TYPE VARCHAR(256),
  DISPLAY_VALUE VARCHAR(8000),
  ORIGINAL_VALUE VARCHAR(600),
  UNIT VARCHAR(256),
  DN_NAME VARCHAR(256),
  CHECKED_NET_ID VARCHAR(2048),
  HAS_MEAS_OBJ VARCHAR(5),
  INDEX_ID VARCHAR(256),
  RESOURCE_TYPE_KEY VARCHAR(256),
  DEPLOYMENT_MO_TYPE VARCHAR(256),
  CONSTRAINT TBL_AIOPS_TASK_INDICATOR_UPDATE PRIMARY KEY (INDICATOR_ID, TASK_ID)
)';
END IF;
/*==============================================================*/
/* Table: "TBL_AIOPS_ALGORITHM"                                 */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_ALGORITHM');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'create table TBL_AIOPS_ALGORITHM(
    ID  INTEGER  AUTO_INCREMENT  not null,
    ALGORITHM_NAME VARCHAR(256)  not null,
    ALGORITHM_TYPE INTEGER       not null,
    UPDATE_TIME    BIGINT        not null,
    VERSION        VARCHAR(256)  not null,
    STATUS         INTEGER,
    CALL_NUMBER    BIGINT,
    FAILED_NUMBER  BIGINT,
    USERID         VARCHAR(256),
    TENANT         VARCHAR(256),
    FILE_BYTES      CLOB         not null,
    USE_STATUS INTEGER DEFAULT 1,
    constraint TBL_AIOPS_ALGORITHM primary key (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL (
  ID INTEGER AUTO_INCREMENT NOT NULL,
  MODEL_ID INTEGER NOT NULL,
  MODEL_NAME VARCHAR(256) NOT NULL,
  FEATURE_TYPE VARCHAR(256),
  FEATURE_TYPE_NAME VARCHAR(1024),
  ALGORITHM_NAME VARCHAR(256) NOT NULL,
  ALGORITHM_PARAM CLOB NOT NULL,
  USER_ID VARCHAR(256) NOT NULL,
  CONSTRAINT TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_CORRELATION_TEMPLATE"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_CORRELATION_TEMPLATE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_CORRELATION_TEMPLATE (
  TASK_ID INTEGER NOT NULL,
  TRIGGER_TYPE INTEGER NOT NULL,
  RELATION_TREE CLOB,
  USER_ID VARCHAR(256) NOT NULL,
  DELAY VARCHAR(100),
  USE_TEMPLATE INTEGER NOT NULL,
  INDICATOR_IDS VARCHAR(8000),
  SEND_EMAIL BOOLEAN,
  USER_GROUP_INFO VARCHAR(1024),
  MANUAL_TRIGGER_EMAIL BOOLEAN,
  CORRELATE_TYPE INTEGER default 1,
  TEMPLATE_NODE_COUNT VARCHAR(1024),
  DISPLAY_FILTER BOOLEAN DEFAULT FALSE,
  FILTER_RELEVANCE VARCHAR(100),
  CONSTRAINT TBL_AIOPS_CORRELATION_TEMPLATE PRIMARY KEY (TASK_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_TEMPLATE_TRIGGER_INDICATOR"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TEMPLATE_TRIGGER_INDICATOR');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TEMPLATE_TRIGGER_INDICATOR (
  TEMPLATE_ID INTEGER NOT NULL,
  TASK_ID INTEGER NOT NULL,
  INDICATOR_ID VARCHAR(1024) NOT NULL
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_TRIGGER_EXECUTION_RESULT"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_RESULT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT (
  ID VARCHAR(255) NOT NULL,
  START_TIME BINARY_BIGINT NOT NULL,
  TEMPLATE_ID INTEGER,
  TREE_RESULT CLOB,
  VIEW_RESULT CLOB,
  INDICATOR_ID VARCHAR(1024),
  TASK_ID INTEGER NOT NULL,
  UPDATE_TIME BINARY_BIGINT,
  STATUS VARCHAR(256),
  FAILURE_CAUSE VARCHAR(512),
  CREATE_USER VARCHAR(256),
  EXECUTION_TIME VARCHAR(256),
  EXECUTION_ADDRESS VARCHAR(256),
  OUTLIER_ID VARCHAR(256),
  CONTINUE_TEMPLATE_ID INTEGER,
  ANALYSIS_TIME_CONSUMPTION VARCHAR(256),
  CONTINUE_ANALYSIS_TIME_CONSUMPTION VARCHAR(256),
  MERGED BOOLEAN,
  MERGE_ID VARCHAR(256),
  MERGE_INDICATOR_ID VARCHAR(1024),
  CONSTRAINT TBL_AIOPS_TRIGGER_EXECUTION_RESULT PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_TRIGGER_EXECUTION_NODE"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_NODE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE (
  NODE_ID VARCHAR(255) NOT NULL,
  EXECUTION_ID VARCHAR(255) NOT NULL,
  NODE_NAME VARCHAR(1024) NOT NULL,
  MO_NAME VARCHAR(256),
  INDICATOR_DATA_TYPE VARCHAR(256),
  MAIN_INDICATOR_DATA_TYPE VARCHAR(256),
  CORRELATION_DEGREE FLOAT(5,2),
  ALGORITHM_TEMPLATE_ID INTEGER,
  ALGORITHM_PARAM CLOB,
  DATASOURCE_ID VARCHAR(255),
  MAIN_EXTEND_JSON VARCHAR(8000),
  EXTEND_JSON VARCHAR(8000),
  MAIN_INDICATOR VARCHAR(8000),
  BEGIN_TIME BINARY_BIGINT,
  END_TIME BINARY_BIGINT,
  EXECUTION_STATUS VARCHAR(256) NOT NULL,
  MORE_THAN_ALARM_MAX BOOLEAN,
  CONSTRAINT TBL_AIOPS_TRIGGER_EXECUTION_NODE PRIMARY KEY (NODE_ID, EXECUTION_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_TRIGGER_EXECUTION_FLOW"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_FLOW');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TRIGGER_EXECUTION_FLOW (
  NODE_ID VARCHAR(255) NOT NULL,
  EXECUTION_ID VARCHAR(255) NOT NULL,
  NODE_NAME VARCHAR(1024) NOT NULL,
  FLOW_TASK_ID VARCHAR2(256) NOT NULL,
  FLOW_ID VARCHAR(256),
  FLOW_GROUP VARCHAR(255),
  FLOW_NAME VARCHAR(255),
  FLOW_EXE_STATUS VARCHAR(256),
  FLOW_EXE_PARAM VARCHAR(8000),
  FLOW_EXE_HOSTS VARCHAR(8000),
  UPDATE_TIME BINARY_BIGINT,
  CONSTRAINT TBL_AIOPS_TRIGGER_EXECUTION_FLOW PRIMARY KEY (NODE_ID, EXECUTION_ID, FLOW_TASK_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_TRIGGER_INDICATOR"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_INDICATOR');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TRIGGER_INDICATOR (
  EXECUTION_ID VARCHAR(255) NOT NULL,
  NODE_ID VARCHAR(255) NOT NULL,
  CORRELATION_DEGREE FLOAT(5,2),
  INDICATOR_ID VARCHAR(1024) NOT NULL,
  DATASOURCE_ID INTEGER,
  TASK_ID INTEGER,
  ABNORMAL NUMBER(1),
  MEAS_UNIT_KEY VARCHAR(256),
  MEAS_UNIT_NAME VARCHAR(600),
  MEAS_TYPE_KEY VARCHAR(256),
  DN VARCHAR(256),
  INDEX_NAME VARCHAR(600),
  MO_TYPE VARCHAR(256),
  DISPLAY_VALUE VARCHAR(8000),
  ORIGINAL_VALUE VARCHAR(600),
  UNIT VARCHAR(256),
  DN_NAME VARCHAR(256),
  CHECKED_NET_ID VARCHAR(2048),
  HAS_MEAS_OBJ VARCHAR(5),
  INDEX_ID VARCHAR(256),
  RESOURCE_TYPE_KEY VARCHAR(256),
  PQL VARCHAR(8000),
  SOLUTION_ID VARCHAR(256),
  SOFT_DELETE BOOLEAN,
  SOFT_DELETE_TIMESTAMP BIGINT,
  SCORE_SOURCES VARCHAR(256),
  CONSTRAINT TBL_AIOPS_TRIGGER_INDICATOR PRIMARY KEY (EXECUTION_ID, NODE_ID, INDICATOR_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_TRIGGER_ALARM_TABLE"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_ALARM_TABLE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TRIGGER_ALARM_TABLE (
  EXECUTION_ID VARCHAR(255) NOT NULL,
  NODE_ID VARCHAR(255) NOT NULL,
  CORRELATION_DEGREE FLOAT(5,2),
  ALARM_ID VARCHAR(1024) NOT NULL,
  DEVICE_TYPE_ID VARCHAR(1024) NOT NULL,
  ALARM_NAME VARCHAR(1024),
  PRODUCT_NAME VARCHAR(1024),
  ALARM_CURVE CLOB,
  CONSTRAINT TBL_AIOPS_TRIGGER_ALARM_TABLE PRIMARY KEY (EXECUTION_ID, NODE_ID, ALARM_ID, DEVICE_TYPE_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_TRIGGER_LOG_TABLE"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_LOG_TABLE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TRIGGER_LOG_TABLE (
  EXECUTION_ID VARCHAR(255) NOT NULL,
  NODE_ID VARCHAR(255) NOT NULL,
  CORRELATION_DEGREE FLOAT(5,2),
  LOG_CURVE CLOB,
  CONSTRAINT TBL_AIOPS_TRIGGER_LOG_TABLE PRIMARY KEY (EXECUTION_ID, NODE_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_TRIGGER_LOG_TEMPLATE"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_LOG_TEMPLATE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TRIGGER_LOG_TEMPLATE (
  EXECUTION_ID VARCHAR(255) NOT NULL,
  NODE_ID VARCHAR(255) NOT NULL,
  TEMPLATE_ID VARCHAR(255) NOT NULL,
  TEMPLATE_VALUE CLOB,
  CONSTRAINT TBL_AIOPS_TRIGGER_LOG_TEMPLATE PRIMARY KEY (EXECUTION_ID, NODE_ID, TEMPLATE_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_CAPACITY_CORRELATION"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_CAPACITY_CORRELATION (
  TASK_ID INTEGER NOT NULL,
  TRAIN_STATUS INTEGER,
  PREDICT_STATUS INTEGER,
  MULTIPLE_TASK_TYPE INTEGER NOT NULL,
  IF_RESERVE INTEGER,
  IF_REPREDICT INTEGER,
  SORT_UPDATE_TIME BIGINT,
  USER_ID VARCHAR(256) NOT NULL,
  RELATION_TREE CLOB,
  EAM_TREE_ADD_RELATION_TREE CLOB,
  RESULT_RELATION_TREE CLOB,
  ASSOCIATED_PREDICT_TASK_ID INTEGER,
  TWO_MACHINE_GROUP_NAME VARCHAR(256),
  ALARM_NORMAL_LEVEL INTEGER,
  ALARM_SEVERITY_LEVEL INTEGER,
  ALARM_REPORT_TIME_RANGE VARCHAR(256),
  HAS_TEMPLATE BOOLEAN,
  CONSTRAINT TBL_AIOPS_CAPACITY_CORRELATION PRIMARY KEY (TASK_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MULTIPLE_EAM_ROOT_NODE"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_EAM_ROOT_NODE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MULTIPLE_EAM_ROOT_NODE (
  TASK_ID INTEGER NOT NULL,
  NODE_ID VARCHAR(255) NOT NULL,
  EAM_EXECUTION_ID VARCHAR(255) NOT NULL,
  EAM_ROOT_NODE_ID VARCHAR(255),
  DN VARCHAR(255),
  MO VARCHAR(255),
  MO_TYPE VARCHAR(255),
  CHILDREN_NUM INTEGER,
  PARENT_NODE_ID VARCHAR(255),
  IF_IGNORE BOOLEAN,
  IS_ROOT BOOLEAN,
  UPDATE_TIME BINARY_BIGINT,
  CONSTRAINT TBL_AIOPS_MULTIPLE_EAM_ROOT_NODE PRIMARY KEY (TASK_ID, EAM_EXECUTION_ID, NODE_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MULTIPLE_EXECUTION_RESULT"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_EXECUTION_RESULT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MULTIPLE_EXECUTION_RESULT (
  NODE_ID VARCHAR(255),
  NODE_NAME VARCHAR(255),
  EXECUTION_ID VARCHAR(255) NOT NULL,
  START_TIME BINARY_BIGINT,
  TASK_ID INTEGER NOT NULL,
  UPDATE_TIME BINARY_BIGINT,
  NODE_TYPE BINARY_INTEGER,
  EAM_EXECUTION_NODE_ID VARCHAR(255),
  STATUS VARCHAR(256),
  CREATE_USER VARCHAR(256),
  INDICATOR_SELECT_TYPE BINARY_INTEGER,
  SORT_NUM INTEGER NOT NULL,
  IS_ROOT BOOLEAN,
  TRAIN_STATUS INTEGER,
  PREDICT_STATUS INTEGER,
  CAN_RETRAIN BOOLEAN,
  IF_NET_CHANGE BOOLEAN,
  HAS_MODEL BOOLEAN,
  ERROR_INDICATOR_NUM INTEGER,
  WARNING_INDICATOR_NUM INTEGER,
  NORMAL_INDICATOR_NUM INTEGER,
  INDICATOR_NUM INTEGER,
  FIGURE CLOB,
  CONSTRAINT TBL_AIOPS_MULTIPLE_EXECUTION_RESULT PRIMARY KEY (TASK_ID, EXECUTION_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR (
  TARGET_EXECUTION_UID VARCHAR(255),
  TARGET_NODE_ID VARCHAR(255),
  NODE_ID VARCHAR(255),
  NODE_NAME VARCHAR(256),
  EXECUTION_UID VARCHAR(255),
  INDICATOR_ID VARCHAR(1024) NOT NULL,
  TASK_ID INTEGER NOT NULL,
  ABNORMAL NUMBER(1),
  DN VARCHAR(256),
  MO_TYPE VARCHAR(256),
  MEAS_UNIT_KEY VARCHAR(256),
  MEAS_UNIT_NAME VARCHAR(600),
  MEAS_TYPE_KEY VARCHAR(256),
  INDEX_NAME VARCHAR(600),
  DISPLAY_VALUE VARCHAR(8000),
  ORIGINAL_VALUE VARCHAR(600),
  UNIT VARCHAR(256),
  DN_NAME VARCHAR(256),
  CHECKED_NET_ID VARCHAR(2048),
  HAS_MEAS_OBJ VARCHAR(5),
  INDEX_ID VARCHAR(256),
  RESOURCE_TYPE_KEY VARCHAR(256),
  KPI_VALUE VARCHAR(256),
  PREDICTION_UPPER_THRESHOLD VARCHAR(256),
  PREDICTION_LOWER_THRESHOLD VARCHAR(256),
  PREDICTION_KPI_VALUE VARCHAR(256),
  UPPER_THRESHOLD VARCHAR(256),
  LOWER_THRESHOLD VARCHAR(256),
  RESULT_TYPE INTEGER,
  TRAIN_RESULT INTEGER,
  IS_ROOT BOOLEAN,
  POD_NAME VARCHAR(256),
  CONSTRAINT TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR PRIMARY KEY (TASK_ID, INDICATOR_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MULTIPLE_DEPENDENCE_INDICATOR_MAP"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_DEPENDENCE_INDICATOR_MAP');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MULTIPLE_DEPENDENCE_INDICATOR_MAP (
  NODE_ID VARCHAR(255),
  EXECUTION_ID VARCHAR(255) NOT NULL,
  TASK_ID INTEGER NOT NULL,
  DEPENDENCE_INDICATOR_ID VARCHAR(1024) NOT NULL,
  CONSTRAINT TBL_AIOPS_MULTIPLE_DEPENDENCE_INDICATOR_MAP PRIMARY KEY (TASK_ID, EXECUTION_ID, DEPENDENCE_INDICATOR_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_CAPACITY_HISTORY_RESULT"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_HISTORY_RESULT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_CAPACITY_HISTORY_RESULT (
  ID VARCHAR(255) NOT NULL,
  TASK_ID INTEGER NOT NULL,
  PREDICT_TYPE VARCHAR(256),
  UPDATE_TIME BINARY_BIGINT NOT NULL,
  RELATION_TREE CLOB,
  MAIN_KPI_VALUE VARCHAR(256),
  MAIN_TIME_RANGE VARCHAR(256),
  CAPACITY_OCCUR_TIME BIGINT,
  CAPACITY_PREDICT_STATUS VARCHAR(256),
  CONSTRAINT TBL_AIOPS_CAPACITY_HISTORY_RESULT PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_TRIGGER_INDICATOR"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_DATASOURCE_CERT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_DATASOURCE_CERT (
  DATASOURCE_ID INTEGER NOT NULL,
  CERT_ID VARCHAR(255) NOT NULL,
  CERT_STATUS INTEGER,
  CONSTRAINT TBL_AIOPS_DATASOURCE_CERT PRIMARY KEY (DATASOURCE_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MONITOR_GROUP"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_GROUP');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MONITOR_GROUP(
    GROUP_ID VARCHAR(256) NOT NULL,
    GROUP_NAME VARCHAR(256) NOT NULL,
    TASK_ID VARCHAR(256) NOT NULL,
    GROUP_KEY VARCHAR(256) NOT NULL,
    SWITCH_TYPE VARCHAR(256) DEFAULT 0,
    CONSTRAINT TBL_AIOPS_MONITOR_GROUP PRIMARY KEY (GROUP_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MONITOR_GROUP_MEMBER"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_GROUP_MEMBER');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MONITOR_GROUP_MEMBER(
    GROUP_MEMBER_ID VARCHAR(256) NOT NULL,
    GROUP_ID VARCHAR(256) NOT NULL,
    DN VARCHAR(256),
    DN_NAME VARCHAR(256),
    STATUS VARCHAR(256),
    KPI_STATUS VARCHAR(256),
    ALARM_STATUS VARCHAR(256),
    FLOW_STATUS VARCHAR(256),
    ASSOCIATION_KPI_STATUS VARCHAR(256),
    TASK_INSTANCE_ID  VARCHAR(256),
    UNIQUE (DN),
    CONSTRAINT TBL_AIOPS_MONITOR_GROUP_MEMBER PRIMARY KEY (GROUP_MEMBER_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_DISASTER_RECOVERY_TASK"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK (
    TASK_ID   VARCHAR(256) NOT NULL,
    TASK_DESC  CLOB,
    TASK_NAME VARCHAR(256),
    PERIOD_TYPE   VARCHAR(256),
    TASK_TYPE_VALUE   VARCHAR(256),
    TASK_STATUS VARCHAR(256),
    UPDATE_TIME   BINARY_BIGINT,
    LAST_EXECUTOR_ID  VARCHAR(256),
    ASSIGN_USERS VARCHAR(2048),
    IS_OPEN_AUTO_SWITCH  BOOLEAN DEFAULT FALSE,
    SWITCH_CONDITIONS VARCHAR(256),
    USER_ID VARCHAR(256) DEFAULT 1,
    CONSTRAINT TBL_AIOPS_DISASTER_RECOVERY_TASK PRIMARY KEY (TASK_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_DISASTER_RECOVERY_FLOW"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_FLOW');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_DISASTER_RECOVERY_FLOW(
    FLOW_ID VARCHAR(256) NOT NULL,
    FLOW_GROUP VARCHAR(255) NOT NULL,
    FLOW_NAME VARCHAR(255) NOT NULL,
    GROUP_ID VARCHAR(256) NOT NULL,
    NODE_ID VARCHAR(256) NOT NULL,
    ATOM_ID VARCHAR(256) NOT NULL,
    HOST_CONTENT CLOB,
    TYPE VARCHAR(256) NOT NULL,
    CONSTRAINT TBL_AIOPS_DISASTER_RECOVERY_FLOW PRIMARY KEY (FLOW_ID,GROUP_ID,NODE_ID,ATOM_ID,TYPE)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_DISASTER_RECOVERY_MML"  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_MML');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_DISASTER_RECOVERY_MML (
    ID  VARCHAR(256) NOT NULL,
    GROUP_ID  VARCHAR(256),
    SWITCH_COMMAND   VARCHAR(8000),
    SWITCH_BACK_COMMAND VARCHAR(8000),
    FLOW_ID VARCHAR(256),
    FLOW_NAME VARCHAR(256),
    FLOW_GROUP VARCHAR(256),
    CONSTRAINT TBL_AIOPS_DISASTER_RECOVERY_MML PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MONITOR_ALARM"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_ALARM');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MONITOR_ALARM(
    ALARM_ID VARCHAR(256) NOT NULL,
    GROUP_ID VARCHAR(256) NOT NULL,
    GROUP_MEMBER_ID VARCHAR(256) DEFAULT NULL,
    ALARM_GROUP_ID VARCHAR(256) NOT NULL,
    ALARM_NAME VARCHAR(256) NOT NULL,
    ALARM_GROUP_NAME VARCHAR(256),
    CONSTRAINT TBL_AIOPS_MONITOR_ALARM PRIMARY KEY (ALARM_ID,GROUP_ID,ALARM_GROUP_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MONITOR_FLOW"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_FLOW');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MONITOR_FLOW(
    FLOW_ID VARCHAR(256) NOT NULL,
    GROUP_ID VARCHAR(256) NOT NULL,
    GROUP_MEMBER_ID VARCHAR(256) NOT NULL,
    NODE_ID VARCHAR(256) NOT NULL,
    ATOM_ID VARCHAR(256) NOT NULL,
    FLOW_NAME VARCHAR(256) NOT NULL,
    FLOW_GROUP VARCHAR(256) NOT NULL,
    HOST_CONTENT CLOB,
    CONSTRAINT TBL_AIOPS_MONITOR_FLOW PRIMARY KEY (FLOW_ID,GROUP_ID,GROUP_MEMBER_ID,NODE_ID,ATOM_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MONITOR_ASSOCIATION_KPI"   */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_ASSOCIATION_KPI');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MONITOR_ASSOCIATION_KPI(
    INDICATOR_ID VARCHAR(1024) NOT NULL,
    ASSOCIATION_TASK_ID INTEGER NOT NULL,
    ASSOCIATION_TASK_NAME VARCHAR(256) NOT NULL,
    GROUP_ID VARCHAR(256),
    GROUP_MEMBER_ID VARCHAR(256) NOT NULL,
    MEAS_UNIT_KEY  VARCHAR(256),
    MEAS_UNIT_NAME VARCHAR(600),
    MEAS_TYPE_KEY VARCHAR(256),
    DN VARCHAR(256),
    DN_NAME VARCHAR(256),
    INDEX_ID VARCHAR(256),
    INDEX_NAME VARCHAR(600),
    HAS_MEAS_OBJ VARCHAR(5),
    DISPLAY_VALUE VARCHAR(8000),
    ORIGINAL_VALUE VARCHAR(600),
    CONSTRAINT TBL_AIOPS_MONITOR_ASSOCIATION_KPI PRIMARY KEY (INDICATOR_ID, GROUP_MEMBER_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MONITOR_AUXILIARY_ITEM"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_AUXILIARY_ITEM');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MONITOR_AUXILIARY_ITEM(
    GROUP_ID VARCHAR(256) NOT NULL,
    GROUP_MEMBER_ID VARCHAR(256) NOT NULL,
    KEY VARCHAR(256) NOT NULL,
    DISPLAY_NAME_ZH VARCHAR(256) NOT NULL,
    DISPLAY_NAME_EN VARCHAR(256) NOT NULL,
    VALUE VARCHAR(256) NOT NULL,
    CONSTRAINT TBL_AIOPS_MONITOR_AUXILIARY_ITEM PRIMARY KEY (GROUP_MEMBER_ID,KEY)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_DISASTER_RECOVERY_FLOW_PARAMETER"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_FLOW_PARAMETER');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_DISASTER_RECOVERY_FLOW_PARAMETER(
    KEY VARCHAR(256) NOT NULL,
    KEY_NAME VARCHAR(256) NOT NULL,
    TASK_ID VARCHAR(256) NOT NULL,
    VALUE VARCHAR(256) NOT NULL,
    UPDATE_TIME BINARY_BIGINT NOT NULL,
    UNIQUE (KEY_NAME, TASK_ID),
    CONSTRAINT TBL_AIOPS_DISASTER_RECOVERY_FLOW_PARAMETER PRIMARY KEY (KEY,TASK_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MONITOR_KPI_COLUMN_TITLE"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_KPI_COLUMN_TITLE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MONITOR_KPI_COLUMN_TITLE(
    GROUP_ID          VARCHAR(256) NOT NULL,
    COLUMN_KEY        VARCHAR(256) NOT NULL,
    COLUMN_NAME       VARCHAR(256) NOT NULL,
    COLUMN_DISPLAY_CN VARCHAR(256) NOT NULL,
    COLUMN_DISPLAY_EN VARCHAR(256) NOT NULL,
    COLUMN_TYPE       VARCHAR(256) NOT NULL,
    CONSTRAINT TBL_AIOPS_MONITOR_KPI_COLUMN_TITLE PRIMARY KEY (GROUP_ID,COLUMN_NAME)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MONITOR_KPI"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_KPI');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MONITOR_KPI(
    INDICATOR_ID VARCHAR(1024) NOT NULL,
    GROUP_ID VARCHAR(256),
    GROUP_MEMBER_ID VARCHAR(256) NOT NULL,
    MEAS_UNIT_KEY  VARCHAR(256),
    MEAS_UNIT_NAME VARCHAR(600),
    MEAS_TYPE_KEY VARCHAR(256),
    DN VARCHAR(256),
    INDEX_NAME VARCHAR(600),
    MO_TYPE VARCHAR(256),
    DISPLAY_VALUE VARCHAR(8000),
    ORIGINAL_VALUE VARCHAR(600),
    UNIT VARCHAR(256),
    DN_NAME VARCHAR(256),
    CHECKED_NET_ID VARCHAR(2048),
  	HAS_MEAS_OBJ VARCHAR(5),
  	INDEX_ID VARCHAR(256),
  	AUXILIARY_KPI_DISPLAY_CN VARCHAR(256),
  	AUXILIARY_KPI_DISPLAY_EN VARCHAR(256),
  	RESOURCE_TYPE_KEY VARCHAR(256),
  	BELONG_INDICATOR_ID VARCHAR(1024),
  	COLUMN_NAME VARCHAR(256),
  	COLUMN_KEY VARCHAR(256),
  	IS_DISPLAY BOOLEAN,
    CONSTRAINT TBL_AIOPS_MONITOR_KPI PRIMARY KEY (INDICATOR_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MONITOR_KPI_THRESHOLD"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_KPI_THRESHOLD');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MONITOR_KPI_THRESHOLD(
    INDICATOR_ID  VARCHAR(1024) NOT NULL,
    SRC_TYPE             VARCHAR(256) NOT NULL,
    COLUMN_NAME   VARCHAR(256) NOT NULL,
    COLUMN_KEY   VARCHAR(256),
    THRESHOLD_TYPE   VARCHAR(256)NOT NULL,
    EFFECTIVETIME    VARCHAR(32),
    HEALTH_CONDITION       VARCHAR(256),
    PERIOD_TYPE          VARCHAR(256),
    SAMPLE_COUNT         NUMBER(3),
    CONSTRAINT TBL_AIOPS_MONITOR_KPI_THRESHOLD PRIMARY KEY (INDICATOR_ID,SRC_TYPE,COLUMN_NAME)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MONITOR_KPI_AUXILIARY_INFO"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_KPI_AUXILIARY_INFO');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MONITOR_KPI_AUXILIARY_INFO(
    INDICATOR_ID VARCHAR(1024) NOT NULL,
    COLUMN_NAME  VARCHAR(256) NOT NULL,
    COLUMN_KEY   VARCHAR(256),
    INFO_TYPE           VARCHAR(256) NOT NULL,
    TEXT_VALUE          VARCHAR(8000),
    FORMULA_VALUE        VARCHAR(32),
    CONSTRAINT TBL_AIOPS_MONITOR_KPI_AUXILIARY_INFO PRIMARY KEY (INDICATOR_ID,COLUMN_NAME)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_MONITOR_KPI_DERIVED"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_KPI_DERIVED');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_MONITOR_KPI_DERIVED(
    INDICATOR_ID    VARCHAR(1024) NOT NULL,
    DERIVED_DISPLAY_CN    VARCHAR(256) NOT NULL,
    DERIVED_DISPLAY_EN                   VARCHAR(256) NOT NULL,
    KPI_DISPLAY_CN                   VARCHAR(256) NOT NULL,
    KPI_DISPLAY_EN                   VARCHAR(256) NOT NULL,
    TYPE        VARCHAR(256) NOT NULL,
    DIFFERENCE_TYPE        VARCHAR(256),
    DIFFERENCE_SAME_PERIOD VARCHAR(256),
    DIFFERENCE_SAME_SAMPLE NUMBER(3),
    CALCULATION_FORMULA NUMBER(1),
    STATISTIC_TYPE         VARCHAR(256),
    STATISTIC_PERIOD       VARCHAR(256),
    CONSTRAINT TBL_AIOPS_MONITOR_KPI_DERIVED PRIMARY KEY (INDICATOR_ID)
)';
END IF;


/*==============================================================*/
/* Table: "TBL_AIOPS_DISASTER_RECOVERY_TASK_RESULT"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK_RESULT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK_RESULT (
  TASK_INSTANCE_ID  VARCHAR(256) NOT NULL,
  TASK_ID           VARCHAR(256) NOT NULL,
  GROUP_ID          VARCHAR(256) NOT NULL,
  GROUP_MEMBER_ID   VARCHAR(256) NOT NULL,
  START_TIME     BIGINT NOT NULL,
  END_TIME       BIGINT,
  EXECUTE_USER   VARCHAR(128),
  STATUS         VARCHAR(256),
  KPI_STATUS     VARCHAR(256),
  ALARM_STATUS   VARCHAR(256),
  FLOW_STATUS    VARCHAR(256),
  ASSOCIATION_KPI_STATUS VARCHAR(256),
  CONSTRAINT TBL_AIOPS_DISASTER_RECOVERY_TASK_RESULT PRIMARY KEY (TASK_INSTANCE_ID, GROUP_MEMBER_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT (
  ID                VARCHAR(256) NOT NULL,
  TASK_INSTANCE_ID  VARCHAR(256) NOT NULL,
  GROUP_ID          VARCHAR(256),
  GROUP_MEMBER_ID   VARCHAR(256) NOT NULL,
  ALARM_ID          VARCHAR(8000) NOT NULL,
  ALARM_DN          VARCHAR(256) NOT NULL,
  OCCURRENCE_TIMES  VARCHAR(8000) NOT NULL,
  CONSTRAINT TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_DISASTER_RECOVERY_TASK_FLOW_RESULT"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK_FLOW_RESULT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK_FLOW_RESULT (
  ID                VARCHAR(256) NOT NULL,
  TASK_INSTANCE_ID  VARCHAR(256) NOT NULL,
  GROUP_MEMBER_ID  VARCHAR(256) NOT NULL,
  STATUS            VARCHAR(256) NOT NULL,
  FLOW_EXE_STATUS   VARCHAR(256) NOT NULL,
  DIAG_SUCC   		VARCHAR(256),
  HEAL_SUCC         VARCHAR2(256),
  FLOW_TASK_ID   VARCHAR2(256),
  MONITOR_FLOW_RESULT     CLOB,
  CONSTRAINT TBL_AIOPS_DISASTER_RECOVERY_TASK_FLOW_RESULT PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_DISASTER_RECOVERY_TASK_ASSOCIATION_KPI_RESULT"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK_ASSOCIATION_KPI_RESULT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK_ASSOCIATION_KPI_RESULT (
  ID                VARCHAR(256) NOT NULL,
  TASK_INSTANCE_ID  VARCHAR(256) NOT NULL,
  GROUP_MEMBER_ID   VARCHAR(256) NOT NULL,
  ABNORMAL_KPI_INFO   VARCHAR(8000) NOT NULL,
  ABNORMAL_KPI_COUNT  VARCHAR(256) NOT NULL,
  STATUS  VARCHAR(256),
  CONSTRAINT TBL_AIOPS_DISASTER_RECOVERY_TASK_ASSOCIATION_KPI_RESULT PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_DISASTER_RECOVERY_ACTION_RESULT"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_ACTION_RESULT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_DISASTER_RECOVERY_ACTION_RESULT(
    TASK_INSTANCE_ID VARCHAR(256) NOT NULL,
    TASK_ID VARCHAR(256) NOT NULL,
    GROUP_ID VARCHAR(256) NOT NULL,
    START_TIME BIGINT,
    END_TIME BIGINT,
    EXECUTOR VARCHAR(256),
    FLOW_PARAMETER CLOB,
    FLOW_EXE_STATUS VARCHAR(256) NOT NULL,
    DIAG_SUCC VARCHAR(256),
    HEAL_SUCC VARCHAR(256),
    FLOW_TASK_ID VARCHAR(256),
    FLOW_RESULT CLOB,
    CHECK_TASK_ID VARCHAR(256),
    DISASTER_TASK_ID VARCHAR(256),
    DISASTER_DETAIL CLOB,
    CONSTRAINT TBL_AIOPS_DISASTER_RECOVERY_ACTION_RESULT PRIMARY KEY (TASK_INSTANCE_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_ALARM_TREND_TABLE"                                  */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALARM_TREND_TABLE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_ALARM_TREND_TABLE(
    TASK_ID INTEGER NOT NULL,
    ALARM_ID VARCHAR(256) NOT NULL,
    ALARM_NAME VARCHAR(1024) NOT NULL,
    COLLECT_TIME BINARY_BIGINT NOT NULL,
    QUANTITY INTEGER NOT NULL,
    EXT_FIELD VARCHAR(256),
    CONSTRAINT TBL_AIOPS_ALARM_TREND_TABLE PRIMARY KEY (TASK_ID,ALARM_ID,COLLECT_TIME)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_LOG_DETECT_TASK" */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_LOG_DETECT_TASK');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_LOG_DETECT_TASK(
    TASK_ID INTEGER NOT NULL,
    BEGIN_TIME BIGINT,
    END_TIME BIGINT,
    INDEX_NAME VARCHAR(600) NOT NULL,
    COUNT VARCHAR(255),
    TIME_FIELD VARCHAR(255),
    PRE_ZONE VARCHAR(255),
    SOLUTION_TYPE VARCHAR(255) NOT NULL,
    SOLUTION_NAME VARCHAR(255) NOT NULL,
    ANALYSIS_FIELD VARCHAR(255),
    FILTER_COLUMNS CLOB,
    FILE_NAME VARCHAR(255),
    MODULE_IP VARCHAR(255),
    SOLUTION_ID VARCHAR(255),
    HOST_NAME VARCHAR(255),
    FILE_PATH VARCHAR(255),
    ABNORMAL INTEGER,
    CONSTRAINT TBL_AIOPS_LOG_DETECT_TASK PRIMARY KEY (TASK_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_LOG_DETECT_ABNORMAL_RESULT" */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_LOG_DETECT_ABNORMAL_RESULT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_LOG_DETECT_ABNORMAL_RESULT(
    ID VARCHAR(256) NOT NULL,
    TASK_ID INTEGER NOT NULL,
    START_TIME BIGINT NOT NULL,
    END_TIME BIGINT NOT NULL,
    DURATION BIGINT,
    ABNORMALITY_CAUSE VARCHAR(256),
    CORRECT INTEGER NOT NULL,
    SWITCH_ALARM_FIELD VARCHAR(256),
    CONSTRAINT TBL_AIOPS_LOG_DETECT_ABNORMAL_RESULT PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_TASK_UNITED_INDICATOR" */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_UNITED_INDICATOR');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TASK_UNITED_INDICATOR(
    UNITED_ID VARCHAR(256) NOT NULL,
    TASK_ID BINARY_INTEGER NOT NULL,
    CLUSTER_NAME VARCHAR(256),
    SITE_ID VARCHAR(256),
    MO_TYPE VARCHAR(256),
    MEAS_UNIT_KEY VARCHAR(256),
    MEAS_TYPE_KEY VARCHAR(256),
    MEAS_UNIT_NAME VARCHAR(600),
    INDEX_ID VARCHAR(256),
    INDEX_NAME VARCHAR(600),
    DISPLAY_VALUE VARCHAR(600),
    ORIGINAL_VALUE VARCHAR(600),
    UNIT VARCHAR(256),
    HAS_MEAS_OBJ VARCHAR(256),
    CHECKED_NET_ID VARCHAR(2048),
    PRIMARY KEY (UNITED_ID,TASK_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO" */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO(
    UNITED_ID VARCHAR(256) NOT NULL,
    TASK_ID BINARY_INTEGER NOT NULL,
    INDICATOR_ID VARCHAR(1024) NOT NULL,
    DN VARCHAR(256),
    DN_NAME VARCHAR(256),
    IF_IGNORE BOOLEAN,
    CLUSTER_NAME VARCHAR(256),
    SITE_ID VARCHAR(256),
    MO_TYPE VARCHAR(256),
    MEAS_UNIT_KEY VARCHAR(256),
    MEAS_TYPE_KEY VARCHAR(256),
    MEAS_UNIT_NAME VARCHAR(600),
    INDEX_ID VARCHAR(256),
    INDEX_NAME VARCHAR(600),
    DISPLAY_VALUE VARCHAR(600),
    ORIGINAL_VALUE VARCHAR(600),
    UNIT VARCHAR(256),
    HAS_MEAS_OBJ VARCHAR(256),
    CHECKED_NET_ID VARCHAR(2048),
    AGGREGATION_STATUS INTEGER,
    PRIMARY KEY (UNITED_ID,TASK_ID, INDICATOR_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR" */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR(
    INDICATOR_ID VARCHAR(1024) NOT NULL,
    BELONG_INDICATOR_ID VARCHAR(1024) NOT NULL,
    MEAS_UNIT_KEY VARCHAR(256),
    MEAS_UNIT_NAME VARCHAR(600),
    MEAS_TYPE_KEY VARCHAR(256),
    DN VARCHAR(256),
    DN_NAME VARCHAR(256),
    MO_TYPE VARCHAR(256),
    INDEX_ID VARCHAR(256),
    INDEX_NAME VARCHAR(600),
    DISPLAY_VALUE VARCHAR(600),
    ORIGINAL_VALUE VARCHAR(600),
    UNIT VARCHAR(256),
    HAS_MEAS_OBJ VARCHAR(256),
    CHECKED_NET_ID VARCHAR(2048),
    PRIMARY KEY (INDICATOR_ID,BELONG_INDICATOR_ID)
)';
END IF;

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_BUSINESS_PORTRAIT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_BUSINESS_PORTRAIT (
    PORTRAIT_ID         INT               NOT NULL AUTO_INCREMENT,
    PORTRAIT_NAME       VARCHAR(256 CHAR) NOT NULL,
    BUSINESS_NAME       VARCHAR(256 CHAR) NOT NULL,
    BUSINESS_DESC       VARCHAR(256 CHAR),
    PRODUCT_PORTRAIT_ID INT               NOT NULL,
    MIN_CPU_NUM         INT               NOT NULL,
    MAX_CPU_NUM         INT               NOT NULL,
    MIN_RAM_SIZE        INT               NOT NULL,
    MAX_RAM_SIZE        INT               NOT NULL,
    MIN_REPLICA_NUM     INT,
    MAX_REPLICA_NUM     INT,
    SERVICE_DEPLOY_TYPE INT,
    CONSTRAINT TBL_AIOPS_BUSINESS_PORTRAIT PRIMARY KEY (PORTRAIT_ID)
    )';
END IF;

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_HOST_PORTRAIT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_HOST_PORTRAIT (
    PORTRAIT_ID         INT                 NOT NULL AUTO_INCREMENT,
    HOST_PORTRAIT_NAME  VARCHAR(256 CHAR)   NOT NULL,
    HOST_TYPE           VARCHAR(256 CHAR)   NOT NULL,
    PRODUCT_PORTRAIT_ID INT                 NOT NULL,
    HOST_PORTRAIT_DESC  VARCHAR(1024 CHAR),
    CPU_NUM             INT                 NOT NULL,
    RAM_SIZE            INT                 NOT NULL,
    DEPLOY_SERVICES     VARCHAR(2560 CHAR)  NOT NULL,
    HOST_NUM_BASELINE   INT,
    MIN_HOST_NUM        INT,
    MAX_HOST_NUM        INT,
    CONSTRAINT TBL_AIOPS_HOST_PORTRAIT PRIMARY KEY (PORTRAIT_ID)
    )';
END IF;

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_PORTRAIT_INDICATOR');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_PORTRAIT_INDICATOR (
    MEAS_UNIT_KEY           VARCHAR(256 CHAR)  NOT NULL,
    INDICATOR_ID            INT                NOT NULL AUTO_INCREMENT,
    BELONG_PORTRAIT_ID      INT                NOT NULL,
    MEAS_UNIT_NAME          VARCHAR(600 CHAR)  NOT NULL,
    MEAS_TYPE_KEY           VARCHAR(256 CHAR)  NOT NULL,
    MEAS_OBJ_DISPLAY_VALUE  VARCHAR(256 CHAR),
    MEAS_OBJ_ORIGINAL_VALUE VARCHAR(256 CHAR),
    MO_TYPE                 VARCHAR(256 CHAR)  NOT NULL,
    UPPER_THRESHOLD         BINARY_DOUBLE,
    LOWER_THRESHOLD         BINARY_DOUBLE,
    INDEX_NAME              VARCHAR(600),
    UNIT                    VARCHAR(256),
    CHECKED_NET_ID          VARCHAR(2048),
    HAS_MEAS_OBJ            VARCHAR(5),
    INDEX_ID                VARCHAR(256),
    RESOURCE_TYPE_KEY       VARCHAR(256),
    CONSTRAINT TBL_AIOPS_PORTRAIT_INDICATOR PRIMARY KEY (INDICATOR_ID)
    )';
END IF;

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_PRODUCT_PORTRAIT');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_PRODUCT_PORTRAIT (
    PORTRAIT_ID                       INT               NOT NULL AUTO_INCREMENT,
    PORTRAIT_NAME                     VARCHAR(256 CHAR) NOT NULL,
    PORTRAIT_TYPE                     INT               NOT NULL,
    PORTRAIT_LAST_MODIFY_DATE         BIGINT            NOT NULL,
    PORTRAIT_DESC                     VARCHAR(1024 CHAR),
    PRODUCT_PORTRAIT_CPU_NUM_BASELINE INT,
    PRODUCT_PORTRAIT_RAM_NUM_BASELINE INT,
    SHEARED_INDICATORS                VARCHAR(1280 CHAR),
    USER_ID                           VARCHAR(256) NOT NULL,
    CONSTRAINT TBL_AIOPS_PRODUCT_PORTRAIT PRIMARY KEY (PORTRAIT_ID)
    )';
END IF;

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_RESOURCE_SCHEDULE_TASK');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_RESOURCE_SCHEDULE_TASK (
    TASK_ID                          INT                NOT NULL,
    IS_AUTO_SCHEDULE                 BOOLEAN,
    SCHEDULE_STATUS_AND_DETAIL       VARCHAR(8000 CHAR),
    EXECUTE_TYPE                     INT                NOT NULL,
    ASSOCIATE_PRODUCT_PORTRAIT_ID    INT                NOT NULL,
    ASSOCIATE_PRODUCT_PORTRAIT_NAME  VARCHAR(256 CHAR)  NOT NULL,
    SCHEDULE_DEPENDENCY_INDICATE     VARCHAR(256 CHAR),
    EXPAND_DELIVERY_TIME             VARCHAR(256 CHAR),
    SCALE_IN_DELIVERY_TIME           VARCHAR(256 CHAR),
    SCHEDULE_TIMEOUT                 VARCHAR(256 CHAR),
    SCHEDULE_PERIOD                  VARCHAR(256 CHAR),
    SCHEDULE_SCALE_IN_WAIT_TIME      VARCHAR(256 CHAR),
    STRATEGY_IDS                     VARCHAR(8000 CHAR),
    DETECT_OUTPUT_COUNT              INT,
    SITE_ID                          VARCHAR(256 CHAR),
    CONSTRAINT TBL_AIOPS_RESOURCE_SCHEDULE_TASK PRIMARY KEY (TASK_ID),
    CONSTRAINT UNIQUE_ASSOCIATE_PORTRAIT_ID UNIQUE (ASSOCIATE_PRODUCT_PORTRAIT_ID)
    )';
END IF;

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_SCHEDULE_EXCUTE_DETAIL');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_SCHEDULE_EXCUTE_DETAIL (
    ID                   INT                NOT NULL AUTO_INCREMENT,
    SCHEDULE_TASK_ID     INT                NOT NULL,
    INDICATOR_GROUP_ID   VARCHAR(256 CHAR)  NOT NULL,
    EXCUTE_START_TIME    BIGINT,
    EXCUTE_END_TIME      BIGINT,
    EXCUTE_OBJECT        VARCHAR(256 CHAR),
    STATUS               VARCHAR(256 CHAR)  NOT NULL,
    EXCUTE_PARAMS        VARCHAR(8000 CHAR),
    EXCUTE_RESULT_DETAIL VARCHAR(8000 CHAR) NOT NULL,
    CONSTRAINT TBL_AIOPS_SCHEDULE_EXCUTE_DETAIL PRIMARY KEY (ID)
    )';
END IF;

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_SCHEDULE_CONFIG');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_SCHEDULE_CONFIG (
    CONFIG_ID                       INT                NOT NULL AUTO_INCREMENT,
    SCHEDULE_TASK_ID                INT                NOT NULL,
    SUB_PORTRAIT_NAME               VARCHAR(256 CHAR)  NOT NULL,
    INDICATOR_PREDICTION_TASK_ID    INT,
    INDICATOR_PREDICTION_TASK_NAME  VARCHAR(256 CHAR),
    INDICATOR_UPPER                 BINARY_DOUBLE,
    INDICATOR_LOWER                 BINARY_DOUBLE,
    INDICATOR_DISPLAY               VARCHAR(1024 CHAR) NOT NULL,
    INDICATOR_RETURN                VARCHAR(1024 CHAR) NOT NULL,
    UNITED_ID                       VARCHAR(1024 CHAR),
    SITE_ID                         VARCHAR(256 CHAR),
    HOST_TYPE                       VARCHAR(256 CHAR),
    CONSTRAINT TBL_AIOPS_SCHEDULE_CONFIG PRIMARY KEY (CONFIG_ID)
    )';
END IF;

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_RESOURCE_SCHEDULE_STRATEGY');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_RESOURCE_SCHEDULE_STRATEGY (
    STRATEGY_ID       INT               NOT NULL AUTO_INCREMENT,
    STRATEGY_NAME     VARCHAR(256 CHAR) NOT NULL,
    EXECUTE_PERIOD     VARCHAR(256 CHAR) NOT NULL,
    EXECUTE_DURATION   VARCHAR(256 CHAR) NOT NULL,
    STRATEGY_PRIORITY INT               NOT NULL,
    STRATEGY_TYPE     INT,
    USER_ID           VARCHAR(256) NOT NULL,
    CONSTRAINT TBL_AIOPS_RESOURCE_SCHEDULE_STRATEGY PRIMARY KEY (STRATEGY_ID)
    )';
END IF;

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_SCHEDULE_STRATEGY_EXCUTE_CONFIG');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_SCHEDULE_STRATEGY_EXCUTE_CONFIG (
    CONFIG_ID       INT               NOT NULL AUTO_INCREMENT,
    STRATEGY_ID     INT               NOT NULL,
    BUSINESS_NAME   VARCHAR(256 CHAR) NOT NULL,
    SCHEDULE_CONFIG INT               NOT NULL,
    CONSTRAINT TBL_AIOPS_SCHEDULE_STRATEGY_EXCUTE_CONFIG PRIMARY KEY (CONFIG_ID)
    )';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_CONFIG_DATA"                     */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_CONFIG_DATA');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_CONFIG_DATA (
  CONFIG_ID INTEGER AUTO_INCREMENT NOT NULL,
  CONFIG_NAME VARCHAR(256) NOT NULL,
  DATA_TYPE VARCHAR(256) NOT NULL,
  DEFAULT_VALUE VARCHAR(256) NOT NULL,
  REAL_VALUE VARCHAR(256),
  MAX_VALUE VARCHAR(256),
  MIN_VALUE VARCHAR(256),
  UNIQUE (CONFIG_NAME),
  CONSTRAINT TBL_AIOPS_CONFIG_DATA PRIMARY KEY (CONFIG_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_SEND_ALARM_RECORD"                     */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_SEND_ALARM_RECORD');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_SEND_ALARM_RECORD (
  ID INTEGER NOT NULL AUTO_INCREMENT,
  INCIDENT_ID VARCHAR(256) NOT NULL,
  TASK_ID INTEGER  NOT NULL,
  ALARM_LEVEL INTEGER,
  STATUS VARCHAR(64),
  CATEGORY INTEGER,
  ALARM_RECORD CLOB,
  UPDATE_TIME BIGINT,
  SEND_ALARM_STATUS VARCHAR(64),
  CONSTRAINT TBL_AIOPS_SEND_ALARM_RECORD PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_IMPORT_HISTORY_INDICATOR"                     */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_IMPORT_HISTORY_INDICATOR');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_IMPORT_HISTORY_INDICATOR (
  ID INTEGER AUTO_INCREMENT NOT NULL,
  USER_NAME VARCHAR(256) NOT NULL,
  FILE_NAME VARCHAR(256) NOT NULL,
  INDICATOR_NAME VARCHAR(1800) NOT NULL,
  INFO VARCHAR(512),
  START_TIME BIGINT NOT NULL ,
  END_TIME BIGINT NOT NULL ,
  DATA_NUM INTEGER NOT NULL ,
  DATA_INTERVAL BIGINT NOT NULL ,
  MAX_VALUE BINARY_DOUBLE,
  MIN_VALUE BINARY_DOUBLE,
  CREATE_TIME BIGINT NOT NULL ,
  CONSTRAINT TBL_AIOPS_IMPORT_HISTORY_INDICATOR PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_CONCEPT_DRIFT_RECORD"                     */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_CONCEPT_DRIFT_RECORD');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_CONCEPT_DRIFT_RECORD (
  ID INTEGER AUTO_INCREMENT NOT NULL,
  TASK_ID INTEGER NOT NULL,
  INDICATOR_ID VARCHAR(1024) NOT NULL,
  START_TIME BIGINT NOT NULL ,
  END_TIME BIGINT,
  CORRECT INTEGER NOT NULL,
  RETRAIN INTEGER NOT NULL,
  CONSTRAINT TBL_AIOPS_CONCEPT_DRIFT_RECORD PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_CAPACITY_CORRELATION_ALARM"                     */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION_ALARM');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_CAPACITY_CORRELATION_ALARM (
  TASK_ID INTEGER NOT NULL,
  INDICATOR_ID VARCHAR(256) NOT NULL,
  PUSH_ALARM_JSON CLOB,
  CONSTRAINT TBL_AIOPS_CAPACITY_CORRELATION_ALARM PRIMARY KEY (TASK_ID,INDICATOR_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_BUSINESS_STRATEGY"                     */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_BUSINESS_STRATEGY');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_BUSINESS_STRATEGY (
  ID INTEGER AUTO_INCREMENT NOT NULL,
  STRATEGY_NAME VARCHAR(512) NOT NULL,
  DESCRIPTION VARCHAR(2048),
  START_TIME BIGINT NOT NULL ,
  END_TIME BIGINT,
  PRIORITY INTEGER NOT NULL,
  STATUS BOOLEAN DEFAULT FALSE,
  TERMINATE_TASK_STATUS INTEGER DEFAULT 0,
  CONSTRAINT TBL_AIOPS_BUSINESS_STRATEGY PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_BUSINESS_STRATEGY_ACTION"                     */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_BUSINESS_STRATEGY_ACTION');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_BUSINESS_STRATEGY_ACTION (
  STRATEGY_ID INTEGER NOT NULL,
  ACTION_TYPE INTEGER NOT NULL,
  SELECT_TYPE INTEGER NOT NULL,
  TASK_TYPE INTEGER DEFAULT 0,
  TASK_ID INTEGER DEFAULT 0,
  TASK_NAME VARCHAR(256),
  CONSTRAINT TBL_AIOPS_BUSINESS_STRATEGY_ACTION PRIMARY KEY (STRATEGY_ID, ACTION_TYPE, SELECT_TYPE, TASK_TYPE, TASK_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL"                     */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL (
  ID INTEGER AUTO_INCREMENT NOT NULL,
  TASK_ID INTEGER NOT NULL,
  INDICATOR_ID VARCHAR(1024) NOT NULL,
  START_TIME BIGINT NOT NULL,
  END_TIME BIGINT NOT NULL,
  CREATE_TIME BIGINT NOT NULL,
  CONSTRAINT TBL_AIOPS_INDICATOR_PREDICT_EXCEPTION_INTERVAL PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_SEND_EMAIL_RECORD"                     */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_SEND_EMAIL_RECORD');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_SEND_EMAIL_RECORD (
  ID VARCHAR(255),
  TITLE VARCHAR(255),
  SEND_USER VARCHAR(255),
  STATUS VARCHAR(64),
  UPDATE_TIME BIGINT,
  SEND_CONTENT CLOB,
  CONSTRAINT TBL_AIOPS_SEND_EMAIL_RECORD PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_INDICATOR_TRANSFER"                     */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_INDICATOR_TRANSFER');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_INDICATOR_TRANSFER (
  ID INTEGER AUTO_INCREMENT NOT NULL,
  TASK_ID INTEGER NOT NULL,
  INDICATOR_ID VARCHAR(1024) NOT NULL,
  OCCUR_TIME BIGINT NOT NULL ,
  CORRECT BOOLEAN NOT NULL,
  CONSTRAINT TBL_AIOPS_INDICATOR_TRANSFER PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE"                     */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE (
    ID VARCHAR(128) NOT NULL,
    NAME VARCHAR(1000) NOT NULL,
    TYPE VARCHAR(128),
    EVENT_TYPE_ID VARCHAR(256),
    ASSOCIATE_REPOSITORY_ID VARCHAR(128),
    DESCRIPTION VARCHAR(2000),
    EVENT_INFO VARCHAR(7000),
    RECOVERY_SUGGESTION CLOB,
    CAUSE VARCHAR(7000),
    SOURCE_SCENE INTEGER,
    CONSTRAINT TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_KNOWLEDGE_NE_RESOURCE"                     */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_NE_RESOURCE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_KNOWLEDGE_NE_RESOURCE (
    ID VARCHAR(128) NOT NULL,
    NAME VARCHAR(256) NOT NULL,
    MO_TYPE_ID VARCHAR(256) NOT NULL,
    ASSOCIATE_REPOSITORY_ID VARCHAR(128) NOT NULL,
    DESCRIPTION VARCHAR(256),
    SOURCE_SCENE INTEGER,
    CONSTRAINT TBL_AIOPS_KNOWLEDGE_NE_RESOURCE PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE"               */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE (
    ID VARCHAR(128) NOT NULL,
    SOURCE VARCHAR(256) NOT NULL,
    TARGET VARCHAR(256) NOT NULL,
    INVOKE_RELATIONSHIP VARCHAR(128) NOT NULL,
    ASSOCIATE_REPOSITORY_ID VARCHAR(128) NOT NULL,
    DESCRIPTION VARCHAR(256),
    SOURCE_SCENE INTEGER,
    CONSTRAINT TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_KNOWLEDGE_REPOSITORY_INFO"                     */
/*==============================================================*/
SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_REPOSITORY_INFO');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_KNOWLEDGE_REPOSITORY_INFO (
    ID VARCHAR(128) NOT NULL,
    NAME VARCHAR(265),
    SOLUTION VARCHAR(256),
    CREATE_TIME VARCHAR(128),
    UPDATE_TIME VARCHAR(128),
    CONSTRAINT TBL_AIOPS_KNOWLEDGE_REPOSITORY_INFO PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE"                     */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE (
    ID VARCHAR(128) NOT NULL,
    TITLE VARCHAR(2000)  NOT NULL,
    CAUSE VARCHAR(7000),
    KEYWORD VARCHAR(2000),
    REPAIR_METHOD CLOB,
    ASSOCIATE_REPOSITORY_ID VARCHAR(100) NOT NULL,
    SOLUTION VARCHAR(256) NOT NULL,
    EFFECT VARCHAR(7000),
    SOURCE_SCENE INTEGER,
    KG_ID BIGINT,
    CASE_VERSION VARCHAR(200),
    CONSTRAINT TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_INTELLIGENTR_RECOMMENDATION"                     */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_INTELLIGENTR_RECOMMENDATION');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION (
    EXECUTION_ID VARCHAR(128) NOT NULL,
    NODE_ID VARCHAR(128)  NOT NULL,
    NODE_NAME VARCHAR(128) NOT NULL,
    NODE_TYPE VARCHAR(128) NOT NULL,
    GENERALERAL_RECOMMENDATION VARCHAR(6000),
    CASE_RECOMMENDATION CLOB,
    CONSTRAINT TBL_AIOPS_INTELLIGENTR_RECOMMENDATION PRIMARY KEY (EXECUTION_ID, NODE_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_FEEDBACK"                     */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_FEEDBACK');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_FEEDBACK (
    ID VARCHAR(128) NOT NULL,
    EXECUTION_ID VARCHAR(128) NOT NULL,
    NODE_ID VARCHAR(128),
    USER_ID VARCHAR(128)  NOT NULL,
    CASE_ID VARCHAR(128) NOT NULL,
    ADOPTION_STATUS INTEGER NOT NULL,
    RECOMMEND_TYPE INTEGER NOT NULL,
    FEEDBACK_INFO VARCHAR(6000),
    UPDATE_TIME VARCHAR(128) NOT NULL,
    CONSTRAINT TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_FEEDBACK PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_RECORD"                     */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_RECORD');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_RECORD (
    ID VARCHAR(128) NOT NULL,
    USER_NAME VARCHAR(128)  NOT NULL,
    RECOMMEND_TYPE VARCHAR(100) NOT NULL,
    UPDATE_TIME VARCHAR(128) NOT NULL,
    EXECUTION_ID VARCHAR(128) NOT NULL,
    CONSTRAINT TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_RECORD PRIMARY KEY (ID)
)';
END IF;

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_FILTER_MO');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TRIGGER_EXECUTION_FILTER_MO (
    EXECUTION_ID VARCHAR(128) NOT NULL,
    NODE_ID VARCHAR(128) NOT NULL,
    DN VARCHAR(256) NOT NULL,
    TYPE VARCHAR(16) NOT NULL,
    MO_TYPE VARCHAR(128) NOT NULL,
    ALARM_ID VARCHAR(64),
    SCORE FLOAT(5,2) NOT NULL,
    CONSTRAINT TBL_AIOPS_TRIGGER_EXECUTION_FILTER_MO PRIMARY KEY (EXECUTION_ID, NODE_ID, DN)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_NETWORK_ELEMENT_INFO"                     */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_NETWORK_ELEMENT_INFO');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_NETWORK_ELEMENT_INFO (
    DN VARCHAR(256) NOT NULL,
    MO_TYPE VARCHAR(256),
    MO_NAME VARCHAR(256),
    PARENT_DN VARCHAR(128),
    SOLUTION_ID VARCHAR(256),
    SOLUTION_TYPE VARCHAR(256),
    CONSTRAINT TBL_AIOPS_NETWORK_ELEMENT_INFO PRIMARY KEY (DN)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_NETWORK_ELEMENT_TYPE"                      */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_NETWORK_ELEMENT_TYPE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_NETWORK_ELEMENT_TYPE (
    MO_TYPE VARCHAR(256) NOT NULL,
    DISPLAY_TYPE VARCHAR(256),
    PARENT_TYPE VARCHAR(256),
    LAYER INTEGER,
    CONSTRAINT TBL_AIOPS_NETWORK_ELEMENT_TYPE PRIMARY KEY (MO_TYPE)
)';
END IF;
/*==============================================================*/
/* Table: "TBL_AIOPS_HWSKIND_INTERNATIONALIZATION"                     */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_HWSKIND_INTERNATIONALIZATION');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_HWSKIND_INTERNATIONALIZATION (
    ID INTEGER AUTO_INCREMENT NOT NULL,
    HWSKIND_KEY VARCHAR(512),
    VALUE_ZH VARCHAR(512),
    VALUE_EN VARCHAR(512),
    CONSTRAINT TBL_AIOPS_HWSKIND_INTERNATIONALIZATION PRIMARY KEY (ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_TASK_INDICATOR_MS_GROUP"                     */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR_MS_GROUP');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TASK_INDICATOR_MS_GROUP (
    TASK_ID INTEGER NOT NULL,
    MS_GROUP_ID VARCHAR(1024) NOT NULL,
    DN VARCHAR(256),
    MO_NAME VARCHAR(256),
    MO_TYPE VARCHAR(256),
    MEAS_UNIT VARCHAR(256),
    COLLECT_TASK VARCHAR(256),
    TABLE_NAME VARCHAR(256),
    CUSTOM_SQL VARCHAR(1024),
    TIME_STAMP_KEY VARCHAR(256),
    MEASURE_INDICATOR_KEYS_JSON VARCHAR(2048),
    MEASURE_OBJECT_KEYS_JSON VARCHAR(2048),
    INDICATOR_GROUP_TAG_JSON VARCHAR(2048),
    CONSTRAINT TBL_AIOPS_TASK_INDICATOR_MS_GROUP PRIMARY KEY (TASK_ID, MS_GROUP_ID)
)';
END IF;

/*==============================================================*/
/* Table: "TBL_AIOPS_INCIDENT_TASK" */
/*==============================================================*/

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE TABLE_NAME = UPPER('TBL_AIOPS_INCIDENT_TASK');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_INCIDENT_TASK(
    TASK_ID INTEGER NOT NULL,
    AGGREGATE_MAX_EVENT_COUNT INTEGER,
    EXECUTE_INTERVAL INTEGER,
    SOFT_DELETED INTEGER,
    CONSTRAINT TBL_AIOPS_INCIDENT_TASK PRIMARY KEY (TASK_ID)
)';
END IF;

SELECT COUNT(*) INTO V_CNT FROM MY_TABLES WHERE  TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_ANALYSIS_TEMPLATE');
IF V_CNT = 0 THEN
EXECUTE IMMEDIATE
'CREATE TABLE TBL_AIOPS_TRIGGER_ANALYSIS_TEMPLATE (
    TASK_ID INTEGER AUTO_INCREMENT NOT NULL,
    TASK_DESC VARCHAR(2048),
    TASK_NAME VARCHAR(1024) NOT NULL,
    TASK_TYPE BINARY_INTEGER NOT NULL,
    UPDATE_TIME BINARY_BIGINT NOT NULL,
    USER_ID VARCHAR(256) NOT NULL,
    IMPORT_TASK_STATUS BOOLEAN,
    SUB_SITE VARCHAR(255),
    INTELLIGENT_RECOMMENDATION VARCHAR(32),
    SOLUTION_TYPE_FOR_RECOMMENDATION VARCHAR(128),
    TRIGGER_TYPE INTEGER NOT NULL,
    RELATION_TREE CLOB,
    DELAY VARCHAR(100),
    USE_TEMPLATE INTEGER NOT NULL,
    INDICATOR_IDS VARCHAR(8000),
    SEND_EMAIL BOOLEAN,
    USER_GROUP_INFO VARCHAR(1024),
    MANUAL_TRIGGER_EMAIL BOOLEAN,
    CORRELATE_TYPE INTEGER default 1,
    TEMPLATE_NODE_COUNT VARCHAR(1024),
    TRIGGER_INDICATOR_LIST CLOB,
    STATUS VARCHAR(100),
    DISPLAY_FILTER BOOLEAN,
    FILTER_RELEVANCE VARCHAR(100),
    VALID_DESC VARCHAR(1024),
    CONSTRAINT TBL_AIOPS_TRIGGER_ANALYSIS_TEMPLATE PRIMARY KEY (TASK_ID)
)';
END IF;

END;
/

DECLARE V_CMT NUMBER;
BEGIN
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CONFIG_DATA') AND COLUMN_NAME = UPPER('FEATURE_TYPE');
IF V_CMT = 0 THEN
     EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CONFIG_DATA ADD FEATURE_TYPE INTEGER';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CONFIG_DATA') AND COLUMN_NAME = UPPER('CONFIG_ITEM_MEANING');
IF V_CMT = 0 THEN
     EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CONFIG_DATA ADD CONFIG_ITEM_MEANING VARCHAR(256)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CONFIG_DATA') AND COLUMN_NAME = UPPER('USED_CAPACITY');
IF V_CMT = 0 THEN
     EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CONFIG_DATA ADD USED_CAPACITY VARCHAR(256)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CONFIG_DATA') AND COLUMN_NAME = UPPER('TARGET_VALUE');
IF V_CMT = 0 THEN
     EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CONFIG_DATA ADD TARGET_VALUE VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME =UPPER('TBL_AIOPS_INDICATOR_OUTLIER') AND COLUMN_NAME = UPPER('ALARM_SHIELD');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_INDICATOR_OUTLIER ADD ALARM_SHIELD BOOLEAN DEFAULT FALSE';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_INDEXES WHERE TABLE_NAME = UPPER('TBL_AIOPS_INDICATOR_OUTLIER') AND INDEX_NAME = UPPER('TASK_INDICATOR_INDEX');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'CREATE INDEX TASK_INDICATOR_INDEX ON TBL_AIOPS_INDICATOR_OUTLIER("TASK_ID", "INDICATOR_ID")';
 END IF;
END;
/

DECLARE V_CMT NUMBER;
BEGIN
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('INDICATOR_ALL_TASK','Integer','2000','2000','25000','2000');
    UPDATE TBL_AIOPS_CONFIG_DATA SET FEATURE_TYPE = 0, CONFIG_ITEM_MEANING = '执行中最大指标数量' WHERE CONFIG_NAME = 'INDICATOR_ALL_TASK';
    UPDATE TBL_AIOPS_CONFIG_DATA SET TARGET_VALUE = REAL_VALUE WHERE CONFIG_NAME = 'INDICATOR_ALL_TASK' and TARGET_VALUE is null;
    UPDATE TBL_AIOPS_CONFIG_DATA SET MAX_VALUE = MAX_VALUE - 500, REAL_VALUE = GREATEST(REAL_VALUE - 500,  COALESCE(USED_CAPACITY, 0), 2000), DEFAULT_VALUE = 2000 WHERE CONFIG_NAME = 'INDICATOR_ALL_TASK' AND DEFAULT_VALUE = 2500;
    UPDATE TBL_AIOPS_CONFIG_DATA SET MIN_VALUE = 2000 WHERE CONFIG_NAME = 'INDICATOR_ALL_TASK';
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('CUSTOM_DATA_INDICATOR_ALL_TASK','Integer','1500','1500','25000','1500');


    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('INDICATOR_ONE_TASK', 'Integer','100','100','1000','100');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('CAPACITY_INDICATOR_ALL_TASK','Integer','2500','2500','2500','2500', 2, '执行中最大指标数量', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('CAPACITY_MAX_INDICATOR_COUNT','Integer','2500','2500','2500','2500', 2, '总指标数量', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('INDICATOR_TASK_COUNT','Integer','30','60','300','30');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('INDICATOR_PREDICT_AGGREGATE_TASK_NUMBER','Integer','10','10',null,null);
    UPDATE TBL_AIOPS_CONFIG_DATA SET MAX_VALUE = 100, MIN_VALUE = 10 WHERE CONFIG_NAME = 'INDICATOR_PREDICT_AGGREGATE_TASK_NUMBER';
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('INDICATOR_PREDICT_AGGREGATE_GROUP_INDICATOR_NUMBER','Integer','10','10',null,null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('KPI_AGGREGATE_GROUP_NE_NUM','Integer','10','10',null,null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('KPI_AGGREGATE_GROUP_NUM','Integer','10','10',null,null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('AGGREGATE_GROUP_INDICATOR_TOTAL_NUMBER','Integer','100','100',null,null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('MAX_INDICATOR_PREDICT_TASK_NUMBER','Integer','200','200',null,null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('MAX_QUERY_NUM','Integer','5','5',null,null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('MAX_LOG_QUERY_AMOUNT','Integer','100000','100000',null,null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('ES_BUFF_MAX_SIZE','Integer','10','10','100','1');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('ES_IDLE_MAX_TIME','Integer','5','5','1440','1');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('MAX_LOG_COUNT_ASSOCIATION','Integer','100000','100000',null,null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('MAX_LOG_COUNT_TRAIN','Integer','1000000','1000000',null,null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('MAX_LOG_COUNT_PREDICT','Integer','1000','1000',null,null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('ALL_USER_INDICATOR_TASK_NUM','Integer','100','100','1000','100');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('PREDICT_CURRENT_MAX_INSERT_DATA', 'Long', '2016000', '2016000', '40320000', '60');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('MAX_INDICATOR_COUNT','Integer','4000','4000','4000','4000');
    UPDATE TBL_AIOPS_CONFIG_DATA SET MIN_VALUE=4000, FEATURE_TYPE = 0, CONFIG_ITEM_MEANING = '总指标数量' WHERE CONFIG_NAME = 'MAX_INDICATOR_COUNT';
    UPDATE TBL_AIOPS_CONFIG_DATA SET TARGET_VALUE = REAL_VALUE WHERE CONFIG_NAME = 'MAX_INDICATOR_COUNT' AND TARGET_VALUE is null;
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('MAXIMUM_DATA_NUMBER','Integer','4032000','4032000','40320000','1');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('IM_NODE_NUM','Integer','1','1','50','1');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('DRIFT_MAX_TIME','Integer','300000','300000','3600000','60000');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('KILL_ALGORITHM_PROCESS','Integer','0','0','1','0');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('CPU_USAGE_SAMPLE_PERIOD','Integer','12','12','600','1');
    UPDATE TBL_AIOPS_CONFIG_DATA SET MAX_VALUE = 50 WHERE CONFIG_NAME = 'IM_NODE_NUM';
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('alarm_compress_clean_count', 'Integer','500000','500000','1000000','10');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('ASSOCIATION_ANALYSIS_ALARM_ID_LIMIT', 'Integer','1000','1000','2000','1');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('alarm_query_shield', 'Integer','0','0','1','0');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('alarm_data_shield_executor_size', 'Integer','1','1','50','1');
-- 容量配置未开启时的配置项
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES ('INDICATOR_ALL_TASK_CONFIG','Integer','2500',(SELECT REAL_VALUE FROM TBL_AIOPS_CONFIG_DATA WHERE CONFIG_NAME = 'INDICATOR_ALL_TASK'),'25000','2500');
    UPDATE TBL_AIOPS_CONFIG_DATA SET DEFAULT_VALUE = 1500, TARGET_VALUE = 1500, MIN_VALUE = 1500 WHERE CONFIG_NAME = 'INDICATOR_ALL_TASK_CONFIG';
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES ('MAX_INDICATOR_COUNT_CONFIG','Integer','10000',(SELECT REAL_VALUE FROM TBL_AIOPS_CONFIG_DATA WHERE CONFIG_NAME = 'MAX_INDICATOR_COUNT'),'20000','0');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('PREDICT_MAX_ALARM_COUNT','Integer','2500','3000','5000','500');
    UPDATE TBL_AIOPS_CONFIG_DATA SET MIN_VALUE = 500 WHERE CONFIG_NAME = 'PREDICT_MAX_ALARM_COUNT' AND MIN_VALUE = 0;
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('RESULT_HANDLE_NOT_END_ALARM_COUNT','Integer','3000','3000','10000','1000');
    UPDATE TBL_AIOPS_CONFIG_DATA SET DEFAULT_VALUE = 3000, REAL_VALUE = 3000, MAX_VALUE = 10000, MIN_VALUE = 1000 WHERE CONFIG_NAME = 'RESULT_HANDLE_NOT_END_ALARM_COUNT' AND REAL_VALUE < 3000;
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('WAIT_PERFORMANCE_PERIOD','Integer','3','3','120','1');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('ASSOCIATION_NODE_EXECUTOR_SIZE','Integer','2','2','20','1');
    UPDATE TBL_AIOPS_CONFIG_DATA SET MAX_VALUE = 20, MIN_VALUE = 1 WHERE CONFIG_NAME = 'ASSOCIATION_NODE_EXECUTOR_SIZE';
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('training_task_executor_core_pool_size','Integer','1','1','2','1');

    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('SINGLE_INDICATOR_PREDICT_MAX_INDICATOR_COUNT','Integer','500','500','10000','500', 1, '独立指标数量', 500);
    UPDATE TBL_AIOPS_CONFIG_DATA SET MAX_VALUE = MAX_VALUE - 500, REAL_VALUE = GREATEST(REAL_VALUE - 500, COALESCE(USED_CAPACITY, 0), 500), DEFAULT_VALUE = 500 WHERE CONFIG_NAME ='SINGLE_INDICATOR_PREDICT_MAX_INDICATOR_COUNT' AND DEFAULT_VALUE = 1000;
    UPDATE TBL_AIOPS_CONFIG_DATA SET MIN_VALUE = 500 WHERE CONFIG_NAME ='SINGLE_INDICATOR_PREDICT_MAX_INDICATOR_COUNT';
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('UNITED_INDICATOR_PREDICT_MAX_INDICATOR_COUNT','Integer','100','100','200','100', 1, '联合指标数量', 100);
    UPDATE TBL_AIOPS_CONFIG_DATA SET MAX_VALUE = 100, MIN_VALUE = 100 WHERE CONFIG_NAME = 'UNITED_INDICATOR_PREDICT_MAX_INDICATOR_COUNT';
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('AGGREGATE_INDICATOR_PREDICT_MAX_INDICATOR_COUNT','Integer','100','100','200','100', 1, '冷双机总指标数量', 100);
    UPDATE TBL_AIOPS_CONFIG_DATA SET MAX_VALUE = 100, MIN_VALUE = 100 WHERE CONFIG_NAME = 'AGGREGATE_INDICATOR_PREDICT_MAX_INDICATOR_COUNT';
    UPDATE TBL_AIOPS_CONFIG_DATA SET REAL_VALUE = 60 WHERE CONFIG_NAME = 'INDICATOR_TASK_COUNT' AND REAL_VALUE = 30;
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ASSOCIATION_ANALYSIS_INDICATOR_MAX','Integer','800000','800000','5000000','200000', null, null, null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ASSOCIATION_TASK_SAME_TIME_INDICATOR_MAX','Integer','10000','10000','100000','2500', null, null, null);
    UPDATE TBL_AIOPS_CONFIG_DATA SET MIN_VALUE = 1 WHERE CONFIG_NAME = 'ASSOCIATION_TEMPLATE_EXECUTOR_SIZE' AND MIN_VALUE is null;
    UPDATE TBL_AIOPS_CONFIG_DATA SET MAX_VALUE = 20 WHERE CONFIG_NAME = 'ASSOCIATION_TEMPLATE_EXECUTOR_SIZE' AND MAX_VALUE is null;
    UPDATE TBL_AIOPS_CONFIG_DATA SET DEFAULT_VALUE = 4, REAL_VALUE = 4 WHERE CONFIG_NAME = 'ASSOCIATION_NODE_EXECUTOR_SIZE' AND REAL_VALUE = 2;
    UPDATE TBL_AIOPS_CONFIG_DATA SET REAL_VALUE = 10000 WHERE CONFIG_NAME = 'ASSOCIATION_TASK_SAME_TIME_INDICATOR_MAX' AND REAL_VALUE < 10000;
    UPDATE TBL_AIOPS_CONFIG_DATA SET REAL_VALUE = 800000 WHERE CONFIG_NAME = 'ASSOCIATION_ANALYSIS_INDICATOR_MAX' AND REAL_VALUE < 800000;
    UPDATE TBL_AIOPS_CONFIG_DATA SET MIN_VALUE = 2500, MAX_VALUE = 100000, DEFAULT_VALUE = 10000 WHERE CONFIG_NAME = 'ASSOCIATION_TASK_SAME_TIME_INDICATOR_MAX';
    UPDATE TBL_AIOPS_CONFIG_DATA SET MIN_VALUE = 200000, DEFAULT_VALUE = 800000, MAX_VALUE = 5000000 WHERE CONFIG_NAME = 'ASSOCIATION_ANALYSIS_INDICATOR_MAX';
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('ASSOCIATION_TEMPLATE_EXECUTE_MAX_SIZE','Integer','100','100','1000','1');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('ASSOCIATION_NODE_EXECUTE_MAX_SIZE','Integer','1000','1000','10000','1');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('ASSOCIATION_TEMPLATE_EXECUTE_TIME_OUT','Integer','60','60','600','1');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('ASSOCIATION_NODE_EXECUTE_TIME_OUT','Integer','5','5','50','1');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('ASSOCIATION_MERGED_TIME_RANGE','Integer','5','5','50','1');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('ASSOCIATION_MERGED_RELEVANCY','Float','0.9','0.9','1.0','0');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('ASSOCIATION_MERGED_TIME_OUT','Integer','30','30','300','1');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE) VALUES('ASSOCIATION_MERGED_INDICATOR_NUM','Integer','200','200','2000','1');

-- 配置管理页面配置项
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ONE_MINUTE','Long','60000','60000','3600000','60000', null, 'CPU计算时长，默认1分钟，最大1小时', '60000');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('TASK_TYPE_INDICATOR_EXCEPTION_PREDICT_CPU','Integer','110','110','10000','110', null, '指标异常检测单指标推理执行时长，单位毫秒', '110');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('TASK_TYPE_INDICATOR_PREDICT_CPU','Integer','200','200','10000','200', null, '指标趋势预测单指标推理执行时长，单位毫秒', '200');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('TASK_TYPE_UNITED_INDICATOR_PREDICT_CPU','Integer','2000','2000','10000','200', null, '联合指标趋势预测单指标推理执行时长，单位毫秒', '200');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('TASK_TYPE_AGGREGATE_INDICATOR_PREDICT_CPU','Integer','2000','2000','10000','200', null, '冷双机指标趋势预测单指标推理执行时长，单位毫秒', '200');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('CPU_OVERSTEP','Long','580000','580000','10000000','580000', null, '超出cpu', '580000');
    UPDATE TBL_AIOPS_CONFIG_DATA SET DATA_TYPE = 'Integer', DEFAULT_VALUE = 580000, REAL_VALUE = 580000, MIN_VALUE = 1 WHERE CONFIG_NAME = 'CPU_OVERSTEP';
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('BASIC_CPU_CONSUMPTION','Integer','15','15','80','15', null, '内存基础消耗', '15');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('OTHER_CPU_CONSUMPTION','Integer','120000','120000','12000000','120000', null, 'CPU其他消耗', '120000');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ONE_INDICATOR_TRAIN_MEMORY_USAGE','Integer','40320','40320','403200','40320', null, '单指标训练内存占用，单位kb', '40320');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ONE_INDICATOR_PREDICT_MEMORY_USAGE','Integer','40320','40320','403200','40320', null, '单指标推理内存占用，单位kb', '40320');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('BASIC_MEMORY_CONSUMPTION','Integer','7','7','80','7', null, '内存基础消耗，单位kb', '7');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('OTHER_MEMORY_CONSUMPTION','Long','2097152','2097152','31457280','2097152', null, '其他内存消耗[2-30Gb]，单位kb', '2097152');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('INDICATOR_EXCEPTION_MAX_PREDICT_NUM','Integer','40320','40320','403200','40320', null, '指标异常检测推理点数最大值，单位kb', '40320');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('INDICATOR_PREDICT_MAX_PREDICT_NUM','Integer','40320','40320','403200','40320', null, '指标趋势预测推理点数最大值，单位kb', '40320');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('INDICATOR_EXCEPTION_DB','Integer','10','10','100000','10', null, '指标异常检测非自定义数据源任务单指标点数据库占用，单位b,1kb = 1000b', '10');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('INDICATOR_PREDICT_DB','Integer','10','10','100000','10', null, '指标趋势预测单指标点占用数据库大小，单位b,1kb = 1000b', '10');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('UNITED_INDICATOR_PREDICT_DB','Integer','100','100','100000','10', null, '指标趋势预测联合单指标点占用数据库大小，单位b,1kb = 1000b', '10');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('AGGREGATE_INDICATOR_PREDICT_DB','Integer','100','100','100000','10', null, '指标趋势预测冷双机单指标点占用数据库大小，单位b,1kb = 1000b', '10');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('BASIC_DB_CONSUMPTION','Integer','400000','400000','40000000','400000', null, '基础数据库消耗，单位kb', '400000');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('OTHER_DB_CONSUMPTION','Long','2097152','2097152','31457280','2097152', null, '其他数据库占用[2-30GB]，单位kb', '2097152');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('CPU_MAXIMUM','Integer','80','80','100','80', null, 'CPU最大阈值', '80');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('MEMORY_MAXIMUM','Integer','80','80','100','80', null, '内存最大阈值', '80');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('DATABASE_MAXIMUM','Integer','80','80','100','80', null, '数据库最大阈值', '80');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('CUSTOM_DATA_PREDICT_MINUTE_LIMIT','Integer','1440','1440','40320','30', null, '指标异常检测第三方数据源推理查询开始时间（分钟）', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('INTERVAL_QUERY_DELETED_PM_DATA','Long','7200000','7200000','86400000','1800', null, '查询消亡网元性能数据时的分隔时间', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('PROMETHEUS_REST_MAX_BODY_SIZE','Integer','31457280','31457280','157286400','5242880', null, '查询普罗米修斯数据源时，限制响应体大小（byte）', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('CONCEPT_DRIFT_EXECUTE_PERIOD','Integer','10','10','120','1', null, '概念漂移任务执行周期（分钟）', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('CONCEPT_DRIFT_SCORE_INTERVAL','Integer','5','5','60','1', null, '概念漂移分数时间间隔（分钟）', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('MAX_IM_NUM','Integer','5','5','50','1', null, 'im节点数量', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('CORRESPONDENCE_BETWEEN_MAIN_AND_AUXILIARY_INDICATOR','Integer','5','5','100','1', null, '联合指标主指标辅助指标对应关系', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('TLS_VERSION','Integer','3','3','0','3', null, 'TLS版本', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ASSOCIATION_ANALYSIS_NODE_COUNT_MAX','Integer','500','500','2000','100', null, '关联分析节点总数上限', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ASSOCIATION_ANALYSIS_LOG_NODE_MAX','Integer','250','250','2000','10', null, '关联分析日志节点数量上限', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ASSOCIATION_ANALYSIS_ALARM_NODE_MAX','Integer','500','500','2000','10', null, '关联分析告警节点数量上限', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('MAX_SQL_RESULT_LEN','Integer','200','200','1024','1', null, '数据库结果集最大长度', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ASSOCIATION_ANALYSIS_CUSTOM_TASK_EXECUTE','Integer','0','0','1','0', null, '关联分析自定义任务执行', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('LOG_MINDSPORE_CONFIG','Integer','0','0','1','0', null, '日志mindspore配置项', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ALARM_COMPRESS_MAX_BATCH_SIZE','Integer','3000','3000','100000','10', null, '告警压缩每次最多压缩的告警数', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ALARM_COMPRESS_MAX_QUEUE_SIZE','Integer','12000','12000','1000000','1000', null, '告警压缩队列的最大长度', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ASSOCIATION_LARGE_CAPACITY_CONFIG','Integer','0','0','1','0', null, '关联分析大容量配置', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('IM_CPU_RESOURCE_POOL_SIZE','Integer','16','16','160','1', null, '智能运维CPU资源池', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('INDICATOR_ONE_TASK_HOMOGENEOUS','Integer','200','200','5000','1', null, '同质化比较单任务最大指标数', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('HOMOGENEOUS_INDICATOR_NUM','Integer','2000','2000','100000','2000', 0, '同质化比较最大启动指标数', '2000');
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ASSOCIATION_GROUP_SIZE','Integer','100','100','100000','0', null, '关联分析分组计算阈值', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ASSOCIATION_LOG_FILTER_MO_TOP_N','Integer','3','3','100','0', null, '关联分析日志节点过滤网元topN', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('INDICATOR_THRESHOLD_SHOW_ACCURACY','Integer','3','3','20','1', null, '指标异常检测阈值小数点精度', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('MANUAL_SINGLE_INDICATOR_PREDICT_MAX_INDICATOR_COUNT','Integer','0','0','1000000','0', null, '单指标趋势预测手动上限', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('MANUAL_UNITED_INDICATOR_PREDICT_MAX_INDICATOR_COUNT','Integer','0','0','1000000','0', null, '联合指标趋势预测手动上限', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('MANUAL_AGGREGATE_INDICATOR_PREDICT_MAX_INDICATOR_COUNT','Integer','0','0','1000000','0', null, '双机指标趋势预测手动上限', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('MANUAL_MAX_INDICATOR_COUNT','Integer','0','0','1000000','0', null, '指标异常检测指标数手动上限', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('MANUAL_HOMOGENEOUS_INDICATOR_NUM','Integer','0','0','1000000','0', null, '同质比较指标数手动上限', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('MANUAL_INDICATOR_ALL_TASK','Integer','0','0','1000000','0', null, '总指标数手动上限', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('CORRELATE_TASK_MAX_SIZE','Integer','20','20','1000','1', null, '关联分析模板关联的最大指标异常检测任务数', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('ADD_SOLUTION_DN_LIST','String','default','default','default','default', null, '手动补充解决方案列表的dn', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('TOPO_INCIDENT_AGGREGATE_MAX_EVENT_COUNT','Integer','2500','2500','100000','10', null, 'Incident任务最大聚合告警数', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('TOPO_INCIDENT_EXECUTE_INTERVAL','Integer','20','20','3600','1', null, 'Incident任务执行时间间隔', null);
    INSERT IGNORE INTO TBL_AIOPS_CONFIG_DATA(CONFIG_NAME, DATA_TYPE, DEFAULT_VALUE, REAL_VALUE, MAX_VALUE, MIN_VALUE, FEATURE_TYPE, CONFIG_ITEM_MEANING, TARGET_VALUE) VALUES ('CAPACITY_ONE_TASK_MAX_INDICATOR_NUM','Integer','1000','1000','10000','1', null, '容量瓶颈分析单任务指标数上限', null);

    UPDATE TBL_AIOPS_CONFIG_DATA SET DEFAULT_VALUE = 10485760, REAL_VALUE = 10485760 WHERE CONFIG_NAME = 'PROMETHEUS_REST_MAX_BODY_SIZE' AND REAL_VALUE = 31457280;
    UPDATE TBL_AIOPS_CONFIG_DATA SET MAX_VALUE = 2000, MIN_VALUE = 10 WHERE CONFIG_NAME = 'ASSOCIATION_ANALYSIS_LOG_NODE_MAX';
    UPDATE TBL_AIOPS_CONFIG_DATA SET DEFAULT_VALUE = 500, REAL_VALUE = 500, MAX_VALUE = 2000, MIN_VALUE = 10 WHERE CONFIG_NAME = 'ASSOCIATION_ANALYSIS_ALARM_NODE_MAX';
    UPDATE TBL_AIOPS_CONFIG_DATA SET DEFAULT_VALUE = 500, REAL_VALUE = 500, MAX_VALUE = 2000, MIN_VALUE = 10 WHERE CONFIG_NAME = 'ASSOCIATION_ANALYSIS_NODE_COUNT_MAX';
    UPDATE TBL_AIOPS_CONFIG_DATA SET DEFAULT_VALUE = 2016000, REAL_VALUE = 2016000 WHERE CONFIG_NAME = 'PREDICT_CURRENT_MAX_INSERT_DATA';
    UPDATE TBL_AIOPS_CONFIG_DATA SET MAX_VALUE = 160 WHERE CONFIG_NAME = 'IM_CPU_RESOURCE_POOL_SIZE';

    INSERT IGNORE INTO TBL_AIOPS_HWSKIND_INTERNATIONALIZATION VALUES(1,'Solution','解决方案层','Solution layer');
    INSERT IGNORE INTO TBL_AIOPS_HWSKIND_INTERNATIONALIZATION VALUES(2,'Pod','容器组层','Pod layer');
    INSERT IGNORE INTO TBL_AIOPS_HWSKIND_INTERNATIONALIZATION VALUES(3,'PhysicPod','资源pod层','PhysicPod layer');
    INSERT IGNORE INTO TBL_AIOPS_HWSKIND_INTERNATIONALIZATION VALUES(4,'AppUnit','应用单元层','AppUnit layer');
    INSERT IGNORE INTO TBL_AIOPS_HWSKIND_INTERNATIONALIZATION VALUES(5,'Application','应用层','Application layer');
    INSERT IGNORE INTO TBL_AIOPS_HWSKIND_INTERNATIONALIZATION VALUES(6,'Container','容器层','Container layer');
    INSERT IGNORE INTO TBL_AIOPS_HWSKIND_INTERNATIONALIZATION VALUES(7,'Node','主机层','Node layer');
    INSERT IGNORE INTO TBL_AIOPS_HWSKIND_INTERNATIONALIZATION VALUES(8,'NodeGroup','主机组层','NodeGroup layer');
    INSERT IGNORE INTO TBL_AIOPS_DATASOURCE VALUES(1,'default','Performance','Performance Source',1,' ',' ',' ',' ',' ',' ',true,'default','',0);
    INSERT IGNORE INTO TBL_AIOPS_DATASOURCE VALUES(2,'default','Monitoring Dashboard','Monitoring Dashboard Source',2,' ',' ',' ',' ',' ',' ',true,'default','',0);
    INSERT IGNORE INTO TBL_AIOPS_DATASOURCE VALUES(3,'default','Alarm','Alarm Source',3,' ',' ',' ',' ',' ',' ',true,'default','',0);
    INSERT IGNORE INTO TBL_AIOPS_DATASOURCE VALUES(4,'default','Log','Log Source',4,' ',' ',' ',' ',' ',' ',true,'default','',0);
-- 预置容量瓶颈分析数据源
    INSERT IGNORE INTO TBL_AIOPS_DATASOURCE VALUES(5,'default','Capacity','Capacity Prediction',6,' ',' ',' ',' ',' ',' ',true,'default','',0);
    INSERT IGNORE INTO TBL_AIOPS_DATASOURCE VALUES(99,'default','Custom','Custom Source Start',99,' ',' ',' ',' ',' ',' ',true,'default','',0);
    INSERT IGNORE INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES(1,'DVAnomalyDetection',1,0,'default',null,null,null,null,null,' ');
    INSERT IGNORE INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES(2,'DVAlarmAnalysis',2,0,'default',null,null,null,null,null,' ');
-- 更新MAX_INDICATOR_COUNT
    UPDATE TBL_AIOPS_CONFIG_DATA
    SET DEFAULT_VALUE       = 4000,
        MAX_VALUE           = GREATEST((SELECT COALESCE(MAX_VALUE, 0) FROM TBL_AIOPS_CONFIG_DATA WHERE CONFIG_NAME = 'INDICATOR_ALL_TASK') +
                                       (SELECT COALESCE(MAX_VALUE, 0) FROM TBL_AIOPS_CONFIG_DATA WHERE CONFIG_NAME = 'HOMOGENEOUS_INDICATOR_NUM'),
                                       COALESCE(USED_CAPACITY, 0), 4000),
        FEATURE_TYPE        = 0,
        REAL_VALUE          = GREATEST((SELECT COALESCE(REAL_VALUE, 0) FROM TBL_AIOPS_CONFIG_DATA WHERE CONFIG_NAME = 'INDICATOR_ALL_TASK') +
                                       (SELECT COALESCE(REAL_VALUE, 0) FROM TBL_AIOPS_CONFIG_DATA WHERE CONFIG_NAME = 'HOMOGENEOUS_INDICATOR_NUM'),
                                       COALESCE(USED_CAPACITY, 0), 4000),
        MIN_VALUE           = 4000,
        CONFIG_ITEM_MEANING = '总指标数量'
    WHERE CONFIG_NAME = 'MAX_INDICATOR_COUNT'
      AND DEFAULT_VALUE = 5000;
-- 预置容量瓶颈分析算法包
    SELECT COUNT(*) INTO V_CMT FROM TBL_AIOPS_ALGORITHM WHERE ALGORITHM_NAME = 'DVCapacityPrediction' AND VERSION = 'default';
    IF V_CMT = 0 THEN
    EXECUTE IMMEDIATE 'INSERT IGNORE INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES(null,:1,5,0,:2,null,null,null,null,null,:3)'
    Using 'DVCapacityPrediction','default',' ';
    END IF;
    SELECT COUNT(*) INTO V_CMT FROM TBL_AIOPS_ALGORITHM WHERE ALGORITHM_NAME = 'DVCorrelationAnalysis' AND VERSION = 'default';
    IF V_CMT = 0 THEN
    EXECUTE IMMEDIATE 'INSERT IGNORE INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES(null,:1,4,0,:2,null,null,null,null,null,:3)'
    Using 'DVCorrelationAnalysis','default',' ';
    END IF;
    SELECT COUNT(*) INTO V_CMT FROM TBL_AIOPS_ALGORITHM WHERE ALGORITHM_NAME = 'DVIndicatorAlarmAnalysis' AND VERSION = 'default';
    IF V_CMT = 0 THEN
    EXECUTE IMMEDIATE 'INSERT IGNORE INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES(null,:1,4,0,:2,null,null,null,null,null,:3)'
    Using 'DVIndicatorAlarmAnalysis','default',' ';
    END IF;
    SELECT COUNT(*) INTO V_CMT FROM TBL_AIOPS_ALGORITHM WHERE ALGORITHM_NAME = 'DVIndicatorLogAnalysis' AND VERSION = 'default';
    IF V_CMT = 0 THEN
    EXECUTE IMMEDIATE 'INSERT IGNORE INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES(null,:1,4,0,:2,null,null,null,null,null,:3)'
    Using 'DVIndicatorLogAnalysis','default',' ';
    END IF;
    SELECT COUNT(*) INTO V_CMT FROM TBL_AIOPS_ALGORITHM WHERE ALGORITHM_NAME = 'DVHomogeneousComparison' AND VERSION = 'default';
    IF V_CMT = 0 THEN
    EXECUTE IMMEDIATE 'INSERT IGNORE INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES(null,:1,1,0,:2,null,null,null,null,null,:3)'
    Using 'DVHomogeneousComparison','default',' ';
    END IF;
    SELECT COUNT(*) INTO V_CMT FROM TBL_AIOPS_ALGORITHM WHERE ALGORITHM_NAME = 'DVLogAnomalyDetection' AND VERSION = 'default';
    IF V_CMT = 0 THEN
    EXECUTE IMMEDIATE 'INSERT IGNORE INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES(null,:1,3,0,:2,null,null,null,null,null,:3)'
    Using 'DVLogAnomalyDetection','default',' ';
    END IF;
    SELECT COUNT(*) INTO V_CMT FROM TBL_AIOPS_ALGORITHM WHERE ALGORITHM_NAME = 'DVIndicatorForecast' AND VERSION = 'default';
    IF V_CMT = 0 THEN
    EXECUTE IMMEDIATE 'INSERT IGNORE INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES(null,:1,6,0,:2,null,null,null,null,null,:3)'
    Using 'DVIndicatorForecast','default',' ';
    END IF;
    SELECT COUNT(*) INTO V_CMT FROM TBL_AIOPS_ALGORITHM WHERE ALGORITHM_NAME = 'DVResourceSchedule' AND VERSION = 'default';
    IF V_CMT = 0 THEN
    EXECUTE IMMEDIATE 'INSERT IGNORE INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES(null,:1,7,0,:2,null,null,null,null,null,:3)'
    Using 'DVResourceSchedule','default',' ';
    END IF;
    SELECT COUNT(*) INTO V_CMT FROM TBL_AIOPS_ALGORITHM WHERE ALGORITHM_NAME = 'DVAlarmIncident' AND VERSION = 'default';
    IF V_CMT = 0 THEN
    EXECUTE IMMEDIATE 'INSERT IGNORE INTO TBL_AIOPS_ALGORITHM(ID, ALGORITHM_NAME, ALGORITHM_TYPE, UPDATE_TIME, VERSION, STATUS, CALL_NUMBER, FAILED_NUMBER, USERID, TENANT, FILE_BYTES) VALUES(null,:1,10,0,:2,null,null,null,null,null,:3)'
    Using 'DVAlarmIncident','default',' ';
    END IF;
    update TBL_AIOPS_ANALYSIS_TASK SET ALGORITHM_MODEL_ID = 1, ALGORITHM_MODEL_NAME = 'DVAnomalyDetection_default' WHERE ALGORITHM_MODEL_ID = 'DVAnomalyDetectionV100';
    update TBL_AIOPS_ANALYSIS_TASK SET ALGORITHM_MODEL_ID = 2, ALGORITHM_MODEL_NAME = 'DVAlarmAnalysis_default' WHERE ALGORITHM_MODEL_ID = 'DVAlarmAnalysisV100';
END;
/

DECLARE V_CMT NUMBER;
BEGIN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR modify DISPLAY_VALUE VARCHAR(8000)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_INDICATOR modify DISPLAY_VALUE VARCHAR(8000)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CUSTOM_INDICATOR_MAPPING MODIFY ORIGINAL_VALUE_KEY null';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CUSTOM_INDICATOR_MAPPING MODIFY MEAS_UNIT_KEY null';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CUSTOM_INDICATOR_MAPPING modify SQL VARCHAR(8000)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK MODIFY PERIODIC_TYPE null';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK MODIFY START_STATUS null';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK MODIFY ALGORITHM_MODEL_ID null';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK MODIFY ALGORITHM_MODEL_NAME null';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK MODIFY DATASOURCE_ID null';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR MODIFY INDICATOR_ID VARCHAR(1024)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TEMPLATE_TRIGGER_INDICATOR MODIFY INDICATOR_ID VARCHAR(1024) null';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT MODIFY INDICATOR_ID VARCHAR(1024)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_INDICATOR MODIFY INDICATOR_ID VARCHAR(1024)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR MODIFY INDICATOR_ID VARCHAR(1024) NOT NULL';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_DEPENDENCE_INDICATOR_MAP MODIFY DEPENDENCE_INDICATOR_ID VARCHAR(1024) NOT NULL';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR MODIFY CHECKED_NET_ID VARCHAR(2048)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR MODIFY CHECKED_NET_ID VARCHAR(2048)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_INDICATOR MODIFY CHECKED_NET_ID VARCHAR(2048)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MONITOR_KPI MODIFY CHECKED_NET_ID VARCHAR(2048)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_UNITED_INDICATOR MODIFY CHECKED_NET_ID VARCHAR(2048)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO MODIFY CHECKED_NET_ID VARCHAR(2048)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR MODIFY CHECKED_NET_ID VARCHAR(2048)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_PORTRAIT_INDICATOR MODIFY CHECKED_NET_ID VARCHAR(2048)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DATASOURCE MODIFY IP VARCHAR(700)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARM_TREND_TABLE MODIFY ALARM_NAME VARCHAR(1024)';

SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('MEAS_UNIT_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR modify MEAS_UNIT_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_INDICATOR') AND COLUMN_NAME = UPPER('MEAS_UNIT_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_INDICATOR modify MEAS_UNIT_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR') AND COLUMN_NAME = UPPER('MEAS_UNIT_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR modify MEAS_UNIT_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_KPI') AND COLUMN_NAME = UPPER('MEAS_UNIT_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MONITOR_KPI modify MEAS_UNIT_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_UNITED_INDICATOR') AND COLUMN_NAME = UPPER('MEAS_UNIT_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_UNITED_INDICATOR modify MEAS_UNIT_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO') AND COLUMN_NAME = UPPER('MEAS_UNIT_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO modify MEAS_UNIT_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR') AND COLUMN_NAME = UPPER('MEAS_UNIT_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR modify MEAS_UNIT_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_PORTRAIT_INDICATOR') AND COLUMN_NAME = UPPER('MEAS_UNIT_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_PORTRAIT_INDICATOR modify MEAS_UNIT_NAME VARCHAR(600)';
END IF;

SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('INDEX_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR modify INDEX_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_INDICATOR') AND COLUMN_NAME = UPPER('INDEX_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_INDICATOR modify INDEX_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR') AND COLUMN_NAME = UPPER('INDEX_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR modify INDEX_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_KPI') AND COLUMN_NAME = UPPER('INDEX_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MONITOR_KPI modify INDEX_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_LOG_DETECT_TASK') AND COLUMN_NAME = UPPER('INDEX_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_LOG_DETECT_TASK modify INDEX_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_UNITED_INDICATOR') AND COLUMN_NAME = UPPER('INDEX_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_UNITED_INDICATOR modify INDEX_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO') AND COLUMN_NAME = UPPER('INDEX_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO modify INDEX_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR') AND COLUMN_NAME = UPPER('INDEX_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR modify INDEX_NAME VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_PORTRAIT_INDICATOR') AND COLUMN_NAME = UPPER('INDEX_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_PORTRAIT_INDICATOR modify INDEX_NAME VARCHAR(600)';
END IF;

SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_UNITED_INDICATOR') AND COLUMN_NAME = UPPER('DISPLAY_VALUE');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_UNITED_INDICATOR modify DISPLAY_VALUE VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO') AND COLUMN_NAME = UPPER('DISPLAY_VALUE');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO modify DISPLAY_VALUE VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR') AND COLUMN_NAME = UPPER('DISPLAY_VALUE');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR modify DISPLAY_VALUE VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_INDEXES WHERE TABLE_NAME = UPPER('TBL_AIOPS_RESOURCE_SCHEDULE_TASK');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_RESOURCE_SCHEDULE_TASK ADD CONSTRAINT UNIQUE_ASSOCIATE_PORTRAIT_ID UNIQUE (ASSOCIATE_PRODUCT_PORTRAIT_ID)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('ORIGINAL_VALUE');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR modify ORIGINAL_VALUE VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_INDICATOR') AND COLUMN_NAME = UPPER('ORIGINAL_VALUE');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_INDICATOR modify ORIGINAL_VALUE VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR') AND COLUMN_NAME = UPPER('ORIGINAL_VALUE');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR modify ORIGINAL_VALUE VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_KPI') AND COLUMN_NAME = UPPER('ORIGINAL_VALUE');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MONITOR_KPI modify ORIGINAL_VALUE VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_UNITED_INDICATOR') AND COLUMN_NAME = UPPER('ORIGINAL_VALUE');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_UNITED_INDICATOR modify ORIGINAL_VALUE VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO') AND COLUMN_NAME = UPPER('ORIGINAL_VALUE');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO modify ORIGINAL_VALUE VARCHAR(600)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR') AND COLUMN_NAME = UPPER('ORIGINAL_VALUE');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_PREDICT_AUXILIARY_INDICATOR modify ORIGINAL_VALUE VARCHAR(600)';
END IF;

SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('SEND_EVENT');
IF V_CMT = 0 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD SEND_EVENT BOOLEAN';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('SEND_EVENT_LEVEL');
IF V_CMT = 0 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD SEND_EVENT_LEVEL INTEGER';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('SEND_EVENT_CONTENT');
IF V_CMT = 0 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD SEND_EVENT_CONTENT VARCHAR(2048)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('SEND_EVENT_ALARM_CONTENT');
IF V_CMT = 0 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD SEND_EVENT_ALARM_CONTENT VARCHAR(2048)';
END IF;

SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('SEND_EVENT_CONTENT');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK MODIFY SEND_EVENT_CONTENT VARCHAR(2048)';
END IF;

SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('SEND_EVENT_ALARM_CONTENT');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK MODIFY SEND_EVENT_ALARM_CONTENT VARCHAR(2048)';
END IF;

SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('TASK_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK MODIFY TASK_NAME VARCHAR(1024)';
END IF;

SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('REPORT_ALARM_NAME');
IF V_CMT = 1 THEN
EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK MODIFY REPORT_ALARM_NAME VARCHAR(1024)';
END IF;

 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('TRAIN_ADDRESS');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD TRAIN_ADDRESS VARCHAR(255)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('PREDICT_ADDRESS');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD PREDICT_ADDRESS VARCHAR(255)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('SUB_SITE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD SUB_SITE VARCHAR(255)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('INDICATOR_PREDICT_SCENARIO');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD INDICATOR_PREDICT_SCENARIO INTEGER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('AGGREGATE_MACHINE_GROUP_LIST');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD AGGREGATE_MACHINE_GROUP_LIST CLOB';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('PQL');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR ADD PQL VARCHAR(8000)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('GROUP_ID');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR ADD GROUP_ID VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('RESOURCE_GROUP');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR ADD RESOURCE_GROUP VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_RESOURCE_SCHEDULE_TASK') AND COLUMN_NAME = UPPER('SCHEDULE_SCALE_IN_WAIT_TIME');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_RESOURCE_SCHEDULE_TASK ADD SCHEDULE_SCALE_IN_WAIT_TIME VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_INDICATOR') AND COLUMN_NAME = UPPER('PQL');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_INDICATOR ADD PQL VARCHAR(8000)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('SOLUTION_ID');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR ADD SOLUTION_ID VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_INDICATOR') AND COLUMN_NAME = UPPER('SOLUTION_ID');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_INDICATOR ADD SOLUTION_ID VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CUSTOM_INDICATOR_MAPPING') AND COLUMN_NAME = UPPER('AUXILIARY_KEY');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CUSTOM_INDICATOR_MAPPING ADD AUXILIARY_KEY VARCHAR(8000)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_NODE') AND COLUMN_NAME = UPPER('MAIN_INDICATOR');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE modify MAIN_INDICATOR VARCHAR(8000)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_NODE') AND COLUMN_NAME = UPPER('NODE_TYPE');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE RENAME COLUMN NODE_TYPE TO INDICATOR_DATA_TYPE';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_NODE') AND COLUMN_NAME = UPPER('MAIN_INDICATOR_DATA_TYPE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE ADD MAIN_INDICATOR_DATA_TYPE VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_NODE') AND COLUMN_NAME = UPPER('MAIN_COLUMN_MAPPING');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE RENAME COLUMN MAIN_COLUMN_MAPPING TO MAIN_EXTEND_JSON';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_NODE') AND COLUMN_NAME = UPPER('COLUMN_MAPPING');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE RENAME COLUMN COLUMN_MAPPING TO EXTEND_JSON';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_NODE') AND COLUMN_NAME = UPPER('MORE_THAN_ALARM_MAX');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE ADD MORE_THAN_ALARM_MAX BOOLEAN';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_NODE') AND COLUMN_NAME = UPPER('NODE_NAME');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE MODIFY NODE_NAME VARCHAR(1024) NOT NULL';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('CREATE_USER');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT ADD CREATE_USER VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('FAILURE_CAUSE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT ADD FAILURE_CAUSE VARCHAR(512)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('EXECUTION_TIME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT ADD EXECUTION_TIME VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('EXECUTION_ADDRESS');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT ADD EXECUTION_ADDRESS VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('OUTLIER_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT ADD OUTLIER_ID VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('CONTINUE_TEMPLATE_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT ADD CONTINUE_TEMPLATE_ID INTEGER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('ANALYSIS_TIME_CONSUMPTION');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT ADD ANALYSIS_TIME_CONSUMPTION VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('CONTINUE_ANALYSIS_TIME_CONSUMPTION');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT ADD CONTINUE_ANALYSIS_TIME_CONSUMPTION VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('MERGED');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT ADD MERGED BOOLEAN';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('MERGE_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT ADD MERGE_ID VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('MERGE_INDICATOR_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_RESULT ADD MERGE_INDICATOR_ID VARCHAR(1024)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DATASOURCE_CERT') AND COLUMN_NAME = UPPER('CERT_OWNER');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DATASOURCE_CERT DROP CERT_OWNER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_ACTION_RESULT') AND COLUMN_NAME = UPPER('CHECK_TASK_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_ACTION_RESULT ADD CHECK_TASK_ID VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_ACTION_RESULT') AND COLUMN_NAME = UPPER('DISASTER_TASK_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_ACTION_RESULT ADD DISASTER_TASK_ID VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_ACTION_RESULT') AND COLUMN_NAME = UPPER('DISASTER_DETAIL');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_ACTION_RESULT ADD DISASTER_DETAIL CLOB';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('REPORT_ALARM_NAME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD REPORT_ALARM_NAME VARCHAR(256) DEFAULT ''default_name''';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('REPORT_ALARM_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD REPORT_ALARM_ID VARCHAR(256) DEFAULT 505410000';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT') AND COLUMN_NAME = UPPER('GROUP_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT ADD GROUP_ID VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_GROUP_MEMBER') AND COLUMN_NAME = UPPER('TASK_INSTANCE_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MONITOR_GROUP_MEMBER ADD TASK_INSTANCE_ID VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_INDEXES WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT') AND INDEX_NAME = UPPER('ALARM_RESULT_INDEX');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'CREATE INDEX ALARM_RESULT_INDEX ON TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT("TASK_INSTANCE_ID", "GROUP_ID", "GROUP_MEMBER_ID")';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT') AND COLUMN_NAME = UPPER('ALARM_ID');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT MODIFY ALARM_ID VARCHAR(8000) NOT NULL';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT') AND COLUMN_NAME = UPPER('ALARM_ADDITIONAL_INFO');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT RENAME COLUMN ALARM_ADDITIONAL_INFO TO OCCURRENCE_TIMES';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT') AND COLUMN_NAME = UPPER('OCCURRENCE_TIMES');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK_ALARM_RESULT MODIFY OCCURRENCE_TIMES VARCHAR(8000) NOT NULL';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR') AND COLUMN_NAME = UPPER('NODE_NAME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR ADD NODE_NAME VARCHAR(255)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR') AND COLUMN_NAME = UPPER('POD_NAME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR ADD POD_NAME VARCHAR(255)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_NODE') AND COLUMN_NAME = UPPER('COMPLEMENTARY_VALUE');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE ADD COMPLEMENTARY_VALUE VARCHAR(255)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_NODE') AND COLUMN_NAME = UPPER('DEMARCATION_LOCATING');
 IF V_CMT = 0 THEN
   EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE ADD DEMARCATION_LOCATING VARCHAR(255)';
 END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_NODE') AND COLUMN_NAME = UPPER('MO_NAME');
IF V_CMT = 0 THEN
   EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE ADD MO_NAME VARCHAR(256)';
END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('NODE_NAME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_RESULT ADD NODE_NAME VARCHAR(255)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('INDICATOR_NUM');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_RESULT ADD ERROR_INDICATOR_NUM INTEGER';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_RESULT ADD WARNING_INDICATOR_NUM INTEGER';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_RESULT ADD NORMAL_INDICATOR_NUM INTEGER';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_RESULT ADD INDICATOR_NUM INTEGER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR') AND COLUMN_NAME = UPPER('TRAIN_RESULT');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR ADD TRAIN_RESULT INTEGER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR') AND COLUMN_NAME = UPPER('PREDICTION_TIME_RANGE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EXECUTION_INDICATOR ADD PREDICTION_TIME_RANGE VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('IMPORT_TASK_STATUS');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD IMPORT_TASK_STATUS BOOLEAN DEFAULT FALSE';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION') AND COLUMN_NAME = UPPER('EAM_TREE_ADD_RELATION_TREE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_CORRELATION ADD EAM_TREE_ADD_RELATION_TREE CLOB';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION') AND COLUMN_NAME = UPPER('RESULT_RELATION_TREE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_CORRELATION ADD RESULT_RELATION_TREE CLOB';
 END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION') AND COLUMN_NAME = UPPER('IF_RESERVE');
IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_CORRELATION ADD IF_RESERVE INTEGER';
END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION') AND COLUMN_NAME = UPPER('ASSOCIATED_PREDICT_TASK_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_CORRELATION ADD ASSOCIATED_PREDICT_TASK_ID INTEGER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION') AND COLUMN_NAME = UPPER('TWO_MACHINE_GROUP_NAME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_CORRELATION ADD TWO_MACHINE_GROUP_NAME VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION') AND COLUMN_NAME = UPPER('IF_REPREDICT');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_CORRELATION ADD IF_REPREDICT INTEGER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION') AND COLUMN_NAME = UPPER('ALARM_NORMAL_LEVEL');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_CORRELATION ADD ALARM_NORMAL_LEVEL INTEGER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION') AND COLUMN_NAME = UPPER('ALARM_SEVERITY_LEVEL');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_CORRELATION ADD ALARM_SEVERITY_LEVEL INTEGER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION') AND COLUMN_NAME = UPPER('ALARM_REPORT_TIME_RANGE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_CORRELATION ADD ALARM_REPORT_TIME_RANGE VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION') AND COLUMN_NAME = UPPER('HAS_TEMPLATE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_CORRELATION ADD HAS_TEMPLATE BOOLEAN';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION') AND COLUMN_NAME = UPPER('SORT_UPDATE_TIME');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_CORRELATION MODIFY SORT_UPDATE_TIME NULL';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION') AND COLUMN_NAME = UPPER('TRAIN_ADDRESS');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_CORRELATION DROP TRAIN_ADDRESS';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_CORRELATION') AND COLUMN_NAME = UPPER('PREDICT_ADDRESS');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_CORRELATION DROP PREDICT_ADDRESS';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('GROUP_LEVEL');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD GROUP_LEVEL INTEGER';
 END IF;

SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALARMCOMPRESS_RULES') AND COLUMN_NAME = UPPER('MODIFY_TIME');
IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALARMCOMPRESS_RULES ADD MODIFY_TIME BIGINT DEFAULT 0';
END IF;
 
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MULTIPLE_EAM_ROOT_NODE') AND COLUMN_NAME = UPPER('EAM_ROOT_NODE_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MULTIPLE_EAM_ROOT_NODE ADD EAM_ROOT_NODE_ID VARCHAR(255)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('ALARM_SELECT_GROUP_LIST');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD ALARM_SELECT_GROUP_LIST CLOB';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('PARTIAL_RECORDS');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_EXECUTION_RESULT ADD PARTIAL_RECORDS CLOB';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_EXECUTION_RESULT') AND COLUMN_NAME = UPPER('FAILURE_CAUSE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_EXECUTION_RESULT ADD FAILURE_CAUSE CLOB';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO') AND COLUMN_NAME = UPPER('AGGREGATION_STATUS');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_AGGREGATE_INDICATOR_INFO ADD AGGREGATION_STATUS INTEGER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_HISTORY_RESULT') AND COLUMN_NAME = UPPER('MAIN_KPI_VALUE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_HISTORY_RESULT ADD MAIN_KPI_VALUE VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_HISTORY_RESULT') AND COLUMN_NAME = UPPER('MAIN_TIME_RANGE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_HISTORY_RESULT ADD MAIN_TIME_RANGE VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_HISTORY_RESULT') AND COLUMN_NAME = UPPER('CAPACITY_PREDICT_STATUS');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_HISTORY_RESULT ADD CAPACITY_PREDICT_STATUS VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CAPACITY_HISTORY_RESULT') AND COLUMN_NAME = UPPER('CAPACITY_OCCUR_TIME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CAPACITY_HISTORY_RESULT ADD CAPACITY_OCCUR_TIME BIGINT';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL') AND COLUMN_NAME = UPPER('FEATURE_TYPE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL ADD FEATURE_TYPE VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL') AND COLUMN_NAME = UPPER('FEATURE_TYPE_NAME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALGORITHM_TEMPLATE_MODEL ADD FEATURE_TYPE_NAME VARCHAR(1024)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_EXECUTION_FLOW') AND COLUMN_NAME = UPPER('UPDATE_TIME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_FLOW ADD UPDATE_TIME BINARY_BIGINT';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('HISTORY_INDICATOR_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR ADD HISTORY_INDICATOR_ID INTEGER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('HISTORY_INDICATOR_NAME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR ADD HISTORY_INDICATOR_NAME VARCHAR(1800)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK_RESULT') AND COLUMN_NAME = UPPER('ASSOCIATION_KPI_STATUS');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK_RESULT ADD ASSOCIATION_KPI_STATUS VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_GROUP_MEMBER') AND COLUMN_NAME = UPPER('ASSOCIATION_KPI_STATUS');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MONITOR_GROUP_MEMBER ADD ASSOCIATION_KPI_STATUS VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK') AND COLUMN_NAME = UPPER('IS_OPEN_AUTO_SWITCH');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK ADD IS_OPEN_AUTO_SWITCH BOOLEAN DEFAULT FALSE';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK') AND COLUMN_NAME = UPPER('SWITCH_CONDITIONS');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK ADD SWITCH_CONDITIONS VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK') AND COLUMN_NAME = UPPER('USER_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK ADD USER_ID VARCHAR(256) DEFAULT 1';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_GROUP') AND COLUMN_NAME = UPPER('SWITCH_TYPE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MONITOR_GROUP ADD SWITCH_TYPE VARCHAR(256) DEFAULT 0';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_TASK_ASSOCIATION_KPI_RESULT') AND COLUMN_NAME = UPPER('STATUS');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_TASK_ASSOCIATION_KPI_RESULT ADD STATUS VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_MONITOR_KPI_DERIVED') AND COLUMN_NAME = UPPER('INDICATOR_ID');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_MONITOR_KPI_DERIVED MODIFY INDICATOR_ID VARCHAR(1024) NOT NULL';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('CONCEPT_DRIFT_SWITCH');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD CONCEPT_DRIFT_SWITCH BOOLEAN DEFAULT FALSE';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('CONCEPT_DRIFT_RETRAIN');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD CONCEPT_DRIFT_RETRAIN BOOLEAN DEFAULT FALSE';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CORRELATION_TEMPLATE') AND COLUMN_NAME = UPPER('SEND_EMAIL');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CORRELATION_TEMPLATE ADD SEND_EMAIL BOOLEAN';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CORRELATION_TEMPLATE') AND COLUMN_NAME = UPPER('USER_GROUP_INFO');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CORRELATION_TEMPLATE ADD USER_GROUP_INFO VARCHAR(1024)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CORRELATION_TEMPLATE') AND COLUMN_NAME = UPPER('MANUAL_TRIGGER_EMAIL');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CORRELATION_TEMPLATE ADD MANUAL_TRIGGER_EMAIL BOOLEAN';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_MML') AND COLUMN_NAME = UPPER('FLOW_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_MML ADD FLOW_ID VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_MML') AND COLUMN_NAME = UPPER('FLOW_NAME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_MML ADD FLOW_NAME VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_DISASTER_RECOVERY_MML') AND COLUMN_NAME = UPPER('FLOW_GROUP');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_DISASTER_RECOVERY_MML ADD FLOW_GROUP VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_BUSINESS_STRATEGY') AND COLUMN_NAME = UPPER('UPDATE_TIME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_BUSINESS_STRATEGY ADD UPDATE_TIME BINARY_BIGINT DEFAULT 0';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('ALARM_TASK_TYPE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD ALARM_TASK_TYPE VARCHAR(128)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('UPDATE_INDICATOR_AUTO');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD UPDATE_INDICATOR_AUTO BOOLEAN';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('INDICATOR_DISCARD_TIME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD INDICATOR_DISCARD_TIME INTEGER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_LOG_DETECT_TASK') AND COLUMN_NAME = UPPER('INDEX_TEMPLATE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_LOG_DETECT_TASK ADD INDEX_TEMPLATE VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_LOG_DETECT_TASK') AND COLUMN_NAME = UPPER('SERIAL_NO_FIELD');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_LOG_DETECT_TASK ADD SERIAL_NO_FIELD VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_LOG_DETECT_TASK') AND COLUMN_NAME = UPPER('FILE_FIELDS');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_LOG_DETECT_TASK ADD FILE_FIELDS VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_SEND_EMAIL_RECORD') AND COLUMN_NAME = UPPER('SEND_CONTENT') AND DATA_TYPE != UPPER('CLOB');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_SEND_EMAIL_RECORD RENAME COLUMN SEND_CONTENT TO SEND_CONTENT_OLD';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_SEND_EMAIL_RECORD ADD COLUMN SEND_CONTENT CLOB';
 EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_SEND_EMAIL_RECORD SET SEND_CONTENT = SEND_CONTENT_OLD';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_SEND_EMAIL_RECORD DROP COLUMN SEND_CONTENT_OLD';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('INTELLIGENT_RECOMMENDATION');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD INTELLIGENT_RECOMMENDATION VARCHAR(32)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('SOLUTION_TYPE_FOR_RECOMMENDATION');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD SOLUTION_TYPE_FOR_RECOMMENDATION VARCHAR(128)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('ALARM_NUMBER_THRESHOLD');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD ALARM_NUMBER_THRESHOLD INTEGER DEFAULT 0';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('EVENT_DURATION_THRESHOLD');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD EVENT_DURATION_THRESHOLD INTEGER DEFAULT 0';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('USE_ORIGINAL_ALARM_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD USE_ORIGINAL_ALARM_ID BOOLEAN';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('INDICATOR_TASK_TYPE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD INDICATOR_TASK_TYPE INTEGER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ANALYSIS_TASK') AND COLUMN_NAME = UPPER('TEMPLATE_TASK_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ANALYSIS_TASK ADD TEMPLATE_TASK_ID VARCHAR(255)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_SEND_ALARM_RECORD') AND COLUMN_NAME = UPPER('ALARM_TYPE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_SEND_ALARM_RECORD ADD ALARM_TYPE VARCHAR(128)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_NE_RESOURCE') AND COLUMN_NAME = UPPER('NODE_NAME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_NE_RESOURCE ADD NODE_NAME VARCHAR(128)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE') AND COLUMN_NAME = UPPER('NODE_NAME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE ADD NODE_NAME VARCHAR(128)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE') AND COLUMN_NAME = UPPER('RECOVERY_SUGGESTION');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE ADD RECOVERY_SUGGESTION VARCHAR(6000)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE') AND COLUMN_NAME = UPPER('EVENT_INFO');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE ADD EVENT_INFO VARCHAR(512)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE') AND COLUMN_NAME = UPPER('CAUSE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE ADD CAUSE VARCHAR(1600)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE') AND COLUMN_NAME = UPPER('EVENT_TYPE');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE RENAME COLUMN EVENT_TYPE TO TYPE';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_NE_RESOURCE') AND COLUMN_NAME = UPPER('NE_ID');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_NE_RESOURCE RENAME COLUMN NE_ID TO MO_TYPE_ID';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CORRELATION_TEMPLATE') AND COLUMN_NAME = UPPER('CORRELATE_TYPE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CORRELATION_TEMPLATE ADD CORRELATE_TYPE INTEGER default 1';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CORRELATION_TEMPLATE') AND COLUMN_NAME = UPPER('TEMPLATE_NODE_COUNT');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CORRELATION_TEMPLATE ADD TEMPLATE_NODE_COUNT VARCHAR(1024)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CORRELATION_TEMPLATE') AND COLUMN_NAME = UPPER('DISPLAY_FILTER');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CORRELATION_TEMPLATE ADD DISPLAY_FILTER BOOLEAN DEFAULT FALSE';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_CORRELATION_TEMPLATE') AND COLUMN_NAME = UPPER('FILTER_RELEVANCE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_CORRELATION_TEMPLATE ADD FILTER_RELEVANCE VARCHAR(100)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_INTELLIGENTR_RECOMMENDATION') AND COLUMN_NAME = UPPER('QUERY_INFO');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION ADD QUERY_INFO VARCHAR(8000)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_INTELLIGENTR_RECOMMENDATION') AND COLUMN_NAME = UPPER('CASE_RECOMMENDATION') AND DATA_TYPE != UPPER('CLOB');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION RENAME COLUMN CASE_RECOMMENDATION TO CASE_RECOMMENDATION_OLD';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION ADD COLUMN CASE_RECOMMENDATION CLOB';
 EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION SET CASE_RECOMMENDATION = CASE_RECOMMENDATION_OLD';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION DROP COLUMN CASE_RECOMMENDATION_OLD';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_INTELLIGENTR_RECOMMENDATION') AND COLUMN_NAME = UPPER('GENERALERAL_RECOMMENDATION') AND DATA_TYPE != UPPER('CLOB');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION RENAME COLUMN GENERALERAL_RECOMMENDATION TO GENERALERAL_RECOMMENDATION_OLD';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION ADD COLUMN GENERALERAL_RECOMMENDATION CLOB';
 EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION SET GENERALERAL_RECOMMENDATION = GENERALERAL_RECOMMENDATION_OLD';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION DROP COLUMN GENERALERAL_RECOMMENDATION_OLD';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_INTELLIGENTR_RECOMMENDATION') AND COLUMN_NAME = UPPER('CORRELATION_NODE_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION ADD CORRELATION_NODE_ID VARCHAR(128)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_NE_RESOURCE') AND COLUMN_NAME = UPPER('NODE_NAME');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE t1 JOIN TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE t2 ON t1.ID = t2.ID SET t1.SOURCE = t2.TARGET, t1.TARGET = t2.SOURCE, t1.INVOKE_RELATIONSHIP = ''subClassOf'' WHERE t1.INVOKE_RELATIONSHIP = ''subClass''';
 EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE t1 JOIN TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE t2 ON t1.ID = t2.ID SET t1.SOURCE = t2.TARGET, t1.TARGET = t2.SOURCE, t1.INVOKE_RELATIONSHIP = ''call'' WHERE t1.INVOKE_RELATIONSHIP = ''callBy''';
 EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE t1 JOIN TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE t2 ON t1.ID = t2.ID SET t1.SOURCE = t2.TARGET, t1.TARGET = t2.SOURCE, t1.INVOKE_RELATIONSHIP = ''happenOn'' WHERE t1.INVOKE_RELATIONSHIP = ''hasAlarm''';
 EXECUTE IMMEDIATE 'DELETE FROM TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE WHERE ID IN (SELECT ID FROM (SELECT ID, ROW_NUMBER() OVER (PARTITION BY SOURCE, TARGET, INVOKE_RELATIONSHIP ORDER BY ID) AS rn FROM TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE) AS RankedRows WHERE rn > 1)';
 EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE a, TBL_AIOPS_KNOWLEDGE_NE_RESOURCE b SET a.SOURCE = b.ID WHERE a.SOURCE = b.NODE_NAME';
 EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE a, TBL_AIOPS_KNOWLEDGE_NE_RESOURCE b SET a.TARGET = b.ID WHERE a.TARGET = b.NODE_NAME';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_NE_RESOURCE DROP NODE_NAME';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE') AND COLUMN_NAME = UPPER('NODE_NAME');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE a, TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE b SET a.SOURCE = b.ID WHERE a.SOURCE = b.NODE_NAME';
 EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE a, TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE b SET a.TARGET = b.ID WHERE a.TARGET = b.NODE_NAME';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE DROP NODE_NAME';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE') AND COLUMN_NAME = UPPER('RECOVERY_SUGGESTION') AND DATA_TYPE != UPPER('CLOB');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE MODIFY RECOVERY_SUGGESTION VARCHAR(7000)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE') AND COLUMN_NAME = UPPER('NAME');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE MODIFY EVENT_TYPE_ID VARCHAR(256)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE MODIFY EVENT_INFO VARCHAR(7000)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_NE_RESOURCE') AND COLUMN_NAME = UPPER('NAME');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_NE_RESOURCE MODIFY NAME VARCHAR(256)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_NE_RESOURCE MODIFY MO_TYPE_ID VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE') AND COLUMN_NAME = UPPER('SOURCE_SCENE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE ADD SOURCE_SCENE INTEGER default 1';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_NE_RESOURCE') AND COLUMN_NAME = UPPER('SOURCE_SCENE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_NE_RESOURCE ADD SOURCE_SCENE INTEGER default 1';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE') AND COLUMN_NAME = UPPER('SOURCE_SCENE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_RELATION_RESOURCE ADD SOURCE_SCENE INTEGER default 1';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE') AND COLUMN_NAME = UPPER('SOURCE_SCENE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE ADD SOURCE_SCENE INTEGER default 1';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_ALGORITHM') AND COLUMN_NAME = UPPER('USE_STATUS');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_ALGORITHM ADD USE_STATUS INTEGER DEFAULT 1';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_REPOSITORY_INFO') AND COLUMN_NAME = UPPER('NAME');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_REPOSITORY_INFO MODIFY NAME VARCHAR(265)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_REPOSITORY_INFO MODIFY SOLUTION VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE') AND COLUMN_NAME = UPPER('SOLUTION');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE MODIFY SOLUTION VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_FEEDBACK') AND COLUMN_NAME = UPPER('NODE_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION_FEEDBACK ADD NODE_ID VARCHAR(128)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE') AND COLUMN_NAME = UPPER('KEYWORD');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE MODIFY KEYWORD VARCHAR(2000)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE MODIFY TITLE VARCHAR(2000) NOT NULL';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE MODIFY CAUSE VARCHAR(7000)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE MODIFY EFFECT VARCHAR(7000)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE MODIFY NAME VARCHAR(1000) NOT NULL';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE MODIFY DESCRIPTION VARCHAR(2000)';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE MODIFY CAUSE VARCHAR(7000)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE') AND COLUMN_NAME = UPPER('RECOVERY_SUGGESTION') AND DATA_TYPE != UPPER('CLOB');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE RENAME COLUMN RECOVERY_SUGGESTION TO RECOVERY_SUGGESTION_OLD';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE ADD COLUMN RECOVERY_SUGGESTION CLOB';
 EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE SET RECOVERY_SUGGESTION = RECOVERY_SUGGESTION_OLD';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_EVENT_RESOURCE DROP COLUMN RECOVERY_SUGGESTION_OLD';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE') AND COLUMN_NAME = UPPER('REPAIR_METHOD') AND DATA_TYPE != UPPER('CLOB');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE RENAME COLUMN REPAIR_METHOD TO REPAIR_METHOD_OLD';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE ADD COLUMN REPAIR_METHOD CLOB';
 EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE SET REPAIR_METHOD = REPAIR_METHOD_OLD';
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE DROP COLUMN REPAIR_METHOD_OLD';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('SOFT_DELETE');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR ADD SOFT_DELETE BOOLEAN';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('SOFT_DELETE_TIMESTAMP');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR ADD SOFT_DELETE_TIMESTAMP BIGINT';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_INDICATOR') AND COLUMN_NAME = UPPER('SOFT_DELETE');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_INDICATOR ADD SOFT_DELETE BOOLEAN';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_INDICATOR') AND COLUMN_NAME = UPPER('SOFT_DELETE_TIMESTAMP');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_INDICATOR ADD SOFT_DELETE_TIMESTAMP BIGINT';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TRIGGER_INDICATOR') AND COLUMN_NAME = UPPER('SCORE_SOURCES');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_INDICATOR ADD SCORE_SOURCES VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE') AND COLUMN_NAME = UPPER('KG_ID');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE ADD KG_ID BIGINT';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE') AND COLUMN_NAME = UPPER('CASE_VERSION');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_KNOWLEDGE_CASE_RESOURCE ADD CASE_VERSION VARCHAR(200)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('DEPLOYMENT_MO_TYPE');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR ADD DEPLOYMENT_MO_TYPE VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR_UPDATE') AND COLUMN_NAME = UPPER('DEPLOYMENT_MO_TYPE');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR_UPDATE ADD DEPLOYMENT_MO_TYPE VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('INDICATOR_TASK_TYPE');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR ADD INDICATOR_TASK_TYPE INTEGER';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_INTELLIGENTR_RECOMMENDATION') AND COLUMN_NAME = UPPER('NODE_NAME');
 IF V_CMT = 1 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_INTELLIGENTR_RECOMMENDATION MODIFY NODE_NAME VARCHAR(2048)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_COMPRESS_GROUP_RULE') AND COLUMN_NAME = UPPER('UPDATE_TIME');
 IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_COMPRESS_GROUP_RULE ADD UPDATE_TIME BINARY_BIGINT default 0';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_COMPRESS_GROUP_RULE') AND COLUMN_NAME = UPPER('RULE_NAME');
 IF V_CMT = 1 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_COMPRESS_GROUP_RULE MODIFY RULE_NAME VARCHAR(512)';
 END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_COMPRESS_GROUP_RULE') AND COLUMN_NAME = UPPER('RULE_TYPE');
IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_COMPRESS_GROUP_RULE ADD RULE_TYPE VARCHAR(256)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_COMPRESS_GROUP_RULE') AND COLUMN_NAME = UPPER('SOLUTION_TYPE');
IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_COMPRESS_GROUP_RULE ADD SOLUTION_TYPE VARCHAR(512)';
END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_COMPRESS_GROUP_RULE') AND COLUMN_NAME = UPPER('SOLUTION_TYPE_NAME');
IF V_CMT = 0 THEN
 EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_COMPRESS_GROUP_RULE ADD SOLUTION_TYPE_NAME VARCHAR(512)';
END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_NETWORK_ELEMENT_INFO') AND COLUMN_NAME = UPPER('SOLUTION_ID');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_NETWORK_ELEMENT_INFO ADD SOLUTION_ID VARCHAR(256)';
 END IF;
 SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_NETWORK_ELEMENT_INFO') AND COLUMN_NAME = UPPER('SOLUTION_TYPE');
 IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_NETWORK_ELEMENT_INFO ADD SOLUTION_TYPE VARCHAR(256)';
 END IF;
SELECT COUNT(*) INTO V_CMT FROM MY_TAB_COLS WHERE TABLE_NAME = UPPER('TBL_AIOPS_TASK_INDICATOR') AND COLUMN_NAME = UPPER('MS_GROUP_ID');
IF V_CMT = 0 THEN
  EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TASK_INDICATOR ADD MS_GROUP_ID VARCHAR(1024)';
END IF;
END;
/

BEGIN
FOR c IN ( SELECT table_name FROM MY_TABLES WHERE table_name LIKE 'TBL_AIOPS_DISASTER_RECOVERY_TASK_KPI_RESULT%' )
  LOOP
    EXECUTE IMMEDIATE 'ALTER TABLE ' || c.table_name || ' MODIFY INDICATOR_ID VARCHAR(1024)';
    EXECUTE IMMEDIATE 'ALTER TABLE ' || c.table_name || ' MODIFY DERIVED_KPI_TIME BIGINT';
END LOOP;
END;
/

DECLARE V_CWT VARCHAR(64);
BEGIN
    SELECT DATA_TYPE INTO V_CWT FROM MY_TAB_COLS WHERE TABLE_NAME='TBL_AIOPS_TRIGGER_EXECUTION_NODE' AND COLUMN_NAME='EXTEND_JSON';
    IF V_CWT = 'VARCHAR' THEN
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE ADD EXTEND_JSON_BAK CLOB';
        EXECUTE IMMEDIATE 'UPDATE TBL_AIOPS_TRIGGER_EXECUTION_NODE SET EXTEND_JSON_BAK = EXTEND_JSON';
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE DROP COLUMN EXTEND_JSON';
        EXECUTE IMMEDIATE 'ALTER TABLE TBL_AIOPS_TRIGGER_EXECUTION_NODE RENAME COLUMN EXTEND_JSON_BAK TO EXTEND_JSON';
    END IF;
END;
/

DECLARE A_NUM NUMBER;
BEGIN
FOR c IN ( SELECT table_name FROM MY_TABLES WHERE table_name LIKE 'TBL_AIOPS_ALARMANALYSIS_RESULT_%' )
  LOOP
    select COUNT(*) INTO A_NUM FROM MY_INDEXES t WHERE TABLE_NAME=c.table_name and COLUMNS='ALARM_ID';
    IF A_NUM = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX alarm_id_' || c.table_name || ' ON ' || c.table_name || '(ALARM_ID) ONLINE';
    END IF;
END LOOP;
END;
/

DECLARE A_NUM NUMBER;
BEGIN
FOR c IN ( SELECT table_name FROM MY_TABLES WHERE table_name LIKE 'TBL_AIOPS_INDICATOR_THRESHOLD_%' )
  LOOP
    select COUNT(*) INTO A_NUM FROM MY_INDEXES t WHERE TABLE_NAME=c.table_name and COLUMNS='COLLECT_TIME';
    IF A_NUM = 0 THEN
        EXECUTE IMMEDIATE 'CREATE INDEX time_' || c.table_name || ' ON ' || c.table_name || '(COLLECT_TIME) ONLINE';
    END IF;
END LOOP;
END;
/

BEGIN
FOR c IN ( SELECT table_name FROM MY_TABLES WHERE table_name LIKE 'TBL_AIOPS_ALARMANALYSIS_ASSOCIATION_%' )
  LOOP
    EXECUTE IMMEDIATE 'ALTER TABLE ' || c.table_name || ' MODIFY ROOT_ALARM_NAME VARCHAR(1024)';
    EXECUTE IMMEDIATE 'ALTER TABLE ' || c.table_name || ' MODIFY CHILD_ALARM_NAME VARCHAR(1024)';
END LOOP;
END;
/

BEGIN
FOR c IN ( SELECT table_name FROM MY_TABLES WHERE table_name LIKE 'TBL_AIOPS_ALARMANALYSIS_RESULT_%' )
  LOOP
    EXECUTE IMMEDIATE 'ALTER TABLE ' || c.table_name || ' MODIFY ALARM_NAME VARCHAR(1024)';
END LOOP;
END;
/

BEGIN
FOR c IN ( SELECT table_name FROM MY_TABLES WHERE table_name LIKE 'TBL_AIOPS_ALARMANALYSIS_INCIDENT_%' )
  LOOP
    EXECUTE IMMEDIATE 'ALTER TABLE ' || c.table_name || ' MODIFY DESCRIPTION VARCHAR(1024)';
END LOOP;
END;
/

DECLARE A_NUM NUMBER;
BEGIN
FOR c IN ( SELECT table_name FROM MY_TABLES WHERE table_name LIKE 'TBL_AIOPS_LOG_DETECT_CURVATURE_RESULT_%' )
    LOOP
        SELECT COUNT(*) INTO A_NUM FROM MY_TAB_COLS WHERE TABLE_NAME = c.table_name AND COLUMN_NAME = UPPER('PERIOD');
        IF A_NUM = 0 THEN
             EXECUTE IMMEDIATE 'ALTER TABLE ' || c.table_name || ' ADD PERIOD BIGINT';
        END IF;
    END LOOP;
END;
/