<?xml version='1.0' encoding='utf-8'?>
<Server port="-1" shutdown="xde##JEK183%">
    <Listener className="org.apache.catalina.startup.VersionLoggerListener"/>
    <Listener className="org.apache.catalina.core.AprLifecycleListener" SSLEngine="on"/>
    <Listener className="org.apache.catalina.core.JreMemoryLeakPreventionListener"/>
    <Listener className="org.apache.catalina.mbeans.GlobalResourcesLifecycleListener"/>
    <Listener className="org.apache.catalina.core.ThreadLocalLeakPreventionListener"/>
    <Listener className="org.apache.catalina.security.SecurityListener" checkedOsUsers="root" minimumUmask="0007" />

    <Service name="Catalina">
        <Connector port="0"
                   protocol="com.huawei.cloudsop.tomcat.api.ssl.Http11NioProtocolExt"
                   scheme="https" sslImplementationName="com.huawei.cloudsop.tomcat.api.ssl.JSSEImplementationExt"
                   SSLEnabled="true" secure="true" keepAliveTimeout="65000" connectionTimeout="65000" maxPostSize="2097152"
                   maxHttpHeaderSize="8192" minSpareThreads="2" maxThreads="100" maxConnections="1024" allowTrace="false"
                   URIEncoding="utf-8"/>
        <Engine name="Catalina" defaultHost="localhost">

            <Host name="localhost" appBase="webapps"
                  unpackWARs="true" autoDeploy="false" deployOnStartup="false" errorReportValveClass="com.huawei.cloudsop.tomcat.api.errorpage.ErrorReportValveExt">
                <Context path="/" reloadable="false" docBase="ROOT" workDir="${TOMCAT_WORK_DIR}">
                    <Manager className="com.huawei.cloudsop.tomcat.api.session.StandardManagerExt"/>
                </Context>
                <Valve className="com.huawei.cloudsop.tomcat.api.log.AccessLogValveExt"
                       pattern="%h %l %u %t &quot;%r&quot; %s %b %{x-request-id}i"/>

            </Host>
        </Engine>
    </Service>
</Server>
