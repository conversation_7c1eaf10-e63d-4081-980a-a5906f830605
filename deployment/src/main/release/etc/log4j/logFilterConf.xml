<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
  -->

<setting>
    <config>
        <!-- 是否在日志中自动替换,默认1自动替换.如果为0则不会生效,需要单点引用 -->
        <autoFilterLog>1</autoFilterLog>
        <!-- 替换敏感信息的字符串 -->
        <replaceStr>*xxxx*</replaceStr>
    </config>
    <!--敏感信息的内容，不区分大小写 配置一律为小写 -->
    <keys>
        <!-- formatRef代表敏感信息出现的格式，如果没有该数据则表示任何格式都有可能出现 -->
        <key>password</key>
        <key>passwd</key>
        <key>pwd</key>
        <key>Password</key>
        <key>rootPwd</key>
        <key>sudoToken</key>
        <key>AuthPassword</key>
        <key>openasjmx_password</key>
        <key>passwordencrypt</key>
        <key>secretkey</key>
        <key>token</key>
        <key>sudoToken</key>
        <key>userToken</key>
        <key>progressToken</key>
        <key>session</key>
        <key>sessionId</key>
        <key>bspSession</key>
        <key formatRef="sftp">ftpuser</key>
    </keys>
    <!--敏感信息的数据结构,可根据实际情况添加配置 . -->
    <!--例如：password="**",<password>**</password>,password:"**","password":"**",password=[] -->
    <formats>
        <!-- id需要唯一，用于key的引用;value没有结束符的话会对过滤结果有影响 -->
        <!-- json example: "key":"value",key:"value" -->
        <format id="json">"key"\s*:\s*"value"</format>
        <format id="json1">\\"key\\"\s*:\s*\\"value\\"</format>
        <!--        <format id="json">\"\s*key\"\s*:\s*\"value\"</format>-->
        <!-- xml example: <key>value</key> -->
        <format id="xml"><![CDATA[<key>value</key>]]></format>
        <!-- common example: key=value  endwith 默认以空格,@做结束符 必须是正则-->
        <format id="kv" endwith="\s,;&amp;\]">key\s*=\s*value</format>
        <!-- sftp://ftpuser:password@ip:port/path/ -->
        <format id="sftp" endwith="@,">key\s*:\s*value</format>
        <!-- 支持不带key结构的则作为字符串单独替换,例如邮箱\密文等信息 -->
        <!-- 例如:密文32位以上的数字字母_ -->
        <!-- <format>\b\w{32,}\b</format> -->
        <format>AAAAHgAA[A-Za-z0-9/+=]{32,}|@010200000\w{32,}\b|00000001000000\w{32,}\b|AAAA\w{32,}\b</format>
    </formats>
    <sensitiveExceptions>
        <exception>java.io.FileNotFoundException</exception>
        <exception>java.util.jar.JarException</exception>
        <exception>java.util.MissingResourceException</exception>
        <exception>java.security.acl.NotOwnerException</exception>
        <exception>java.util.ConcurrentModificationException</exception>
        <exception>javax.naming.InsufficientResourcesException</exception>
        <exception>java.net.BindException</exception>
        <exception>java.lang.OutOfMemoryError</exception>
        <exception>java.lang.StackOverflowError</exception>
        <exception>java.sql.SQLException</exception>
        <exception>com.fasterxml.jackson.core.JsonProcessingException</exception>
        <exception>com.fasterxml.jackson.databind.JsonMappingException</exception>
        <exception>com.fasterxml.jackson.core.JsonParseException</exception>
        <exception>com.huawei.security.validator.util.jackson.JSONException</exception>
    </sensitiveExceptions>

</setting>