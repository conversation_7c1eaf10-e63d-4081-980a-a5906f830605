#########SSL options
ssl.protocols=TLSv1.3
ssl.ciphers=TLS_AES_256_GCM_SHA384,TLS_AES_128_GCM_SHA256
ssl.authPeer=true
ssl.checkCN.host=false
ssl.checkCN.white=false
ssl.checkCN.white.file=white.list
ssl.allowRenegociate=false

#########certificates config
ssl.storePath=etc/ssl/dv
ssl.trustStore=dv_trust.jks
ssl.trustStoreType=JKS
ssl.trustStoreValue=trustStoreValue
ssl.keyStore=dv_client.p12
ssl.keyStoreType=PKCS12
ssl.keyStoreValue=keyStoreValue
ssl.crl=client.crl