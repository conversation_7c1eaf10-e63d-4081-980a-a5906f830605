#上传的模板中不能有注释内容，上传前需删除
#每次只能导入一种类型的任务，不要同时使用2种样例
#关联分析模板样例
#目前只支持sql类型的普通节点导入，性能指标只支持motype类型节点导入

# 如果需要替换公共参数，则需要另外导入properties配置文件(下载模板中不包含公共参数配置文件)，该文件用来记录公共参数的名称和值
# Properties文件中格式要求：key=value
# 同一properties文件中如果出现重复的key值，key对应的value值会被后出现的替换
# 不同properties文件中如果出现重复的key值，则导入失败
# 可以同时导入最多10个文件，包含至少1个JSON文件
# 公共参数替换方式：
#  a) Json模板中增加占位符“#”，如：json模板中配置“#xx_key#”,会被自动按照properties全局配置文件中的xx_key对应的值替换
#  b) 公共变量只支持字符串替换
#  c) 公共变量不支持变量的嵌套替换
#  d) 不支持模糊匹配替换
# 替换示例：
#  单值替换：对"taskName"值进行替换: json文件中修改为"taskName": "#taskName#"，properties文件中增加 taskName=test0，替换后"taskName": "test0"
#  列表替换："xxx_list": ["value1","value2"]，则需修改为："xxx_list": [#xxx_list#]，properties文件中增加 xxx_list="value1","value2

# 空的properties文件会导致导入失败
# 格式错误，缺少key或者value会导致导入失败
# properties文件导入失败会导致JSON文件导入失败，任务导入失败

{
	"taskName": "test导入2", #任务名称
	"taskType": "CorrelationAnalysis", #导入任务类型，关联分析任务
	"nodeFilter": "bySolutionId", #可选配置，用于进行多站点过滤，如需使用请和指标异常检测任务一起配置
	"useTemplate": "0", #非必填，模板自动触发，0：否，1：是
	"delay": "1|MIN", #非必填，配置自动触发延时
	"correlateTaskName": "指标异常检测任务1,指标异常检测2", #非必填，配置模板自动触发时需要配置该项，配置需要自动触发的任务名，关联该任务下所有指标，任务名称不可重复，最多配置20个任务
	"intelligentRecommendation": "false", #非必填，是否开启智能推荐，默认为false
	"solutionTypeForRecommendation": "", #默认为空，如果开启智能推荐则不能为空，关闭智能推荐必须为空或跳过改字段配置
	"nodes": [{         #节点列表，根据nodeType区分节点类型：main：主节点，节点列表中必须要有一个主节点；moType：网元类型节点，需要配置具体的网元类型，系统会自动查询网元类型下的所有实例并生成节点；normal：普通类型节点，支持根据数据源选择指标。
			"nodeType": "main", #主节点，必须要有
			"main_task": "This is AD task"
		},
		{
			"nodeName": "节点1",
			"nodeType": "moType", #网元类型节点样例
			"moType":"OMS",       #网元类型 (DV性能的指标执行模板时会选择该网元类型下所有实例的指标)
			"moName":"@regex@(?i)^abc.*123$",#根据网元名称过滤指标，支持正则匹配：以@regex@开头后面为正则，禁止使用()，如果要不区分大小写在开头加上(?i)，示例表示匹配以abc开头、123结尾，中间可以是任意字符，且大小写不敏感的字符串
			"solutionName":"@regex@(?i)^abc.*123$",#根据解决方案名称过滤指标，支持正则匹配：以@regex@开头后面为正则，禁止使用()，如果要不区分大小写在开头加上(?i)，示例表示匹配以abc开头、123结尾，中间可以是任意字符，且大小写不敏感的字符串
			"deployMoType": "",#根据填写的网元类型查询网元，并按照网元部署关系找到对应的宿网元，进而过滤对应实例的资源指标
			"updateIndicatorAuto": true, #如果启用自动更新网元，则会自动选择所有该网元类型下面的网元实例的指标，同时会自动更新该网元类型下面的实例和指标数量，如果指标总数超过规格限制，则只保留最大规格数量指标，不配置默认是true
			"indicatorList": [{      #指标需要填写以下属性，系统会根据网元查出具体的指标
				"measUnitKey": "I2K_NBI",       
				"measTypeKey":"receiveCnt",
				"originalValue":"@regex@(?i)^abc.*123$" #根据测量对象过滤指标，支持正则匹配：以@regex@开头后面为正则，禁止使用()，如果要不区分大小写在开头加上(?i)，示例表示匹配以abc开头、123结尾，中间可以是任意字符，且大小写不敏感的字符串
			},{
				"measUnitKey": "I2K_OS",       
				"measTypeKey":"IOWait",       
				"originalValue":"ip=x.x.x.x"
			}],
			"algorithmTemplateName": "关联分析使用的算法名称", #可选，可以配置要使用的算法名称，如果已存在则使用已有的
			"algorithmModelName": "DVCorrelationAnalysis",  #算法名称，默认为DVCorrelationAnalysis算法参数为默认值
			"algorithmFeatureType": "homogeneous_comparison_matching", #可选，特征匹配类型，不配置的使用默认的关联分析
			"algorithmParam":[{#可选，需要修改的算法的参数，注意算法是新建的，前面配置的算法名称不要重复否则会使用已有算法
				"parameterName":"eps", #算法参数key值
				"parameterDefaultValue":"0.5" #算法参数默认值
				},{
				"parameterName":"manual_uppervalue",
				"parameterDefaultValue":"100"
			}],
			"kpiTimeRange": "1|HOUR", #时间拉取的范围，格式为 数值|单位（MIN或HOUR）
			"kpiTimeType": "recently" #数据拉取的时间类型，recently表示最近，beforeAfter表示前后
		},
		{
		"nodeName": "节点3",
		"nodeType": "normal", #表示是普通类型节点
		"deployMoType": "",#根据填写的网元类型查询网元，并按照网元部署关系找到对应的宿网元，进而过滤对应实例的资源指标
		"indicatorList": [{      #指标需要填写以下属性，系统会根据网元查出具体的指标
			"moType":"OMS",       #网元类型 (DV性能的指标执行模板时会选择该网元类型下所有实例的指标)
			"measUnitKey": "I2K_NBI",
			"measTypeKey":"receiveCnt"
		},{
		"moType":"OMS",       #网元类型 (DV性能的指标执行模板时会选择该网元类型下所有实例的指标)
		"measUnitKey": "I2K_OS",
		"measTypeKey":"IOWait",
		"originalValue":"ip=x.x.x.x"
		}],
		"algorithmModelName": "DVCorrelationAnalysis",
		"kpiTimeRange": "1|HOUR", #时间拉取的范围，格式为 数值|单位（MIN或HOUR）
		"kpiTimeType": "recently" #数据拉取的时间类型，recently表示最近，beforeAfter表示前后
		},{
			"nodeName": "节点4",
			"nodeType": "alarm",  #表示告警类型节点
			"moName": "@regex@(?i)^abc.*123$", #根据网元名称过滤告警，支持正则匹配：以@regex@开头后面为正则，禁止使用()，如果要不区分大小写在开头加上(?i)，示例表示匹配以abc开头、123结尾，中间可以是任意字符，且大小写不敏感的字符串
			"extendJson":[ #填写告警过滤条件 in代表匹配具体值，like代表包含填写的值，不需要过滤的字段不用填写
			{"field":"alarmId","operator":"in","values":["999999999"]}, #告警id，只支持in操作
			{"field":"alarmName","operator":"like","values":["网管服务器与网元通讯异常"]}, #告警名称，支持in/like
			{"field":"severity","operator":"in","values":[]}, #告警级别，只支持in操作，固定的4种可在告警页面查看取值1-4
			{"field":"eventType","operator":"in","values":[]}, #告警类型，只支持in操作，固定的21种可在告警页面查看取值1-21
			{"field":"deviceTypeId","operator":"in","values":[]}, #告警源类型，支持in/like，类似网元类型，填写实际的moType，如：cbs.billing.cbs
			{"field":"nativeMoDn","operator":"like","values":[]}, #告警源，只支持like，类似网元dn
			{"field":"moi","operator":"in","values":[]} #定位信息，支持in/like
			],
			"algorithmModelName": "DVIndicatorAlarmAnalysis", #告警的算法名称
			"kpiTimeRange": "1|HOUR",
			"kpiTimeType": "recently",
			"solutionName": ""		# 解决方案名称，如果配置了解决方案，则告警过滤条件必须配置告警源类型并且operator只能为in，且不再使用告警源这个告警过滤条件
		},{
			"nodeName": "节点5",
			"nodeType": "log",  #表示日志类型节点
			"demarcationLocating": "Locating", #表示该节点为小节点环绕，只适配CBS业务场景使用，其他产品不建议使用，小节点只能作为叶子节点配置，不能再连接其他节点，并且只能有一个父节点，预案节点不能配置为小节点类型
			"extendJson":{
				"queryFields":{ #日志过滤条件，可填写以下条件，不需要则不填，规则请参考日志服务
					"Message":"agentIp",
					"FileName":"workbench_debug.log",
					"SolutionType":"mpaas.momgr.MPAAS",
					"FilePath":"xxx",
					"ModuleIp":"xxx",
					"ClusterType":"xxx",
					"SolutionId":"xxx",
					"NodeId":"xxx",
					"HostName":"xxx"
				},
				"solutionType":"mpaas.momgr.MPAAS", #解决方案类型，请参考日志服务
				"logType":"dsflogs2", #日志类型，请参考日志服务
				"analysisField":"Message" #需要分析的字段，为日志里面的某个字段
			},
			"algorithmModelName": "DVIndicatorLogAnalysis", #日志的算法名称
			"kpiTimeRange": "1|HOUR",
			"kpiTimeType": "recently"
		},{
			"nodeName": "节点6",
			"nodeType": "execute",  #表示预案类型节点
			"indicatorDataType": "DV_FLOW/DV_SHOW", #DV_FLOW流程执行节点，DV_SHOW人工排查节点
			"extendJson":{ #流程中原子关联的执行主机和实例化的环境有关系，所以导入后请去页面上填写每个原子的执行主机
			"flowActions": [ #DV_FLOW流程执行节点必填
			{
			"flowGroup":"", #流程组名称
			"flowName":"" #流程名称
			}
			],
			"causeInfo":"xxx", #故障原因，必填，不能包含特殊字符#%&+|'?"\，长度不能超过1024
			"helpInfo":"xxx", #故障建议，必填，不能包含特殊字符#%&+|'?"\，长度不能超过1024
			}
		}
	],
	"paths": [{ #各个节点的指向关系
			"source": "main",
			"target": "节点1"#填写目标节点的nodeName
		},{
			"source": "节点1",#填写源节点的nodeName
			"target": "节点3",#填写目标节点的nodeName
			"condition": ">=90.0"#除了source为main的节点，路径都需要填写关联度关系，必须在0到100之间，且只能为小数
		}, {
			"source": "节点3", #填写源节点的nodeName
			"target": "节点4", #填写目标节点的nodeName
			"condition": ">=90.0"#除了source为main的节点，路径都需要填写关联度关系，必须在0到100之间，且只能为小数
		}, {
			"source": "节点3", #填写源节点的nodeName
			"target": "节点5", #填写目标节点的nodeName
			"condition": ">=90.0", #除了source为main的节点，路径都需要填写关联度关系，必须在0到100之间，且只能为小数
			"filterMo": { # 是否通过前置节点网元过滤日志，仅当前置节点为指标或告警，且目标节点为日志时，该字段生效
				"value": true
			}
		}, {
			"source": "节点3", #填写源节点的nodeName
			"target": "节点6", #填写目标节点的nodeName
			"condition": ">=90.0"#除了source为main的节点，路径都需要填写关联度关系，必须在0到100之间，且只能为小数
		}
	]
}

#指标异常检测模板样例
{
	"situationName": "lalala", #场景名称
	"taskType": "AnomalyDetection", #导入任务类型，指标异常检测任务
	"taskSplit": "bySolutionId", #可选配置，用于进行多站点过滤，如需使用请和关联分析任务一起配置
	"taskList": [
		{
			"taskName": "性能指标任务", #任务名称
			"moName":"@regex@(?i)^abc.*123$",#根据网元名称过滤指标，支持正则匹配：以@regex@开头后面为正则，禁止使用()，如果要不区分大小写在开头加上(?i)，示例表示匹配以abc开头、123结尾，中间可以是任意字符，且大小写不敏感的字符串
			"solutionName":"@regex@(?i)^abc.*123$",#根据解决方案名称过滤指标，支持正则匹配：以@regex@开头后面为正则，禁止使用()，如果要不区分大小写在开头加上(?i)，示例表示匹配以abc开头、123结尾，中间可以是任意字符，且大小写不敏感的字符串
			"deployMoType": "",#根据填写的网元类型查询网元，并按照网元部署关系找到对应的宿网元，进而过滤对应实例的资源指标
			"updateIndicatorAuto": true, #如果启用自动更新网元，则会自动选择所有该网元类型下面的网元实例的指标，同时会自动更新该网元类型下面的实例和指标数量，如果指标总数超过规格限制，则只保留最大规格数量指标，不配置默认是true
			"indicatorList": [{  #DV性能的指标信息
				"moType":"OMS",
				"measUnitKey": "I2K_FMMGR",
				"measTypeKey":"FMDataUnhandled"
			},{
				"moType":"OMS",
				"measUnitKey": "I2K_FMMGR",
				"measTypeKey":"FMAlarmDelay"
			}],
			"taskDetail": "用户输入定位信息", #上报告警时用户输入的定位信息
			"alarmType": 2, #上报告警级别
			"predictCron": "5|MIN", #推理周期，必填项
			"reportAlarm":true, #是否上报告警
			"reportAlarmName":"上报告警名称", #上报告警名称
			"taskDesc":"描述", #任务描述
			"indicatorPredictNum":30, #推理数据点数
			"indicatorTrainNum":10080, #训练数据点数
			"conceptDriftSwitch": true, #概念漂移开关
			"conceptDriftRetrain": true, #概念漂移是否重训练
			"taskImportConfig":{ #可选配置，确定重复任务时的更新逻辑
				"indicatorUpdateMode":"2", #可选配置，重复任务导入时指标是否更新，1：不更新，2：用文件里的替换原有任务（默认值）
				"configUpdateMode":"1" #可选配置，重复任务导入时任务配置和算法参数是否更新，1：不更新（默认值），2：用文件里的替换原有任务
			}
			},{
			"taskName": "任务2",
			"indicatorList": [{
				"moType":"OMS",
				"measUnitKey": "I2K_JVM",
				"measTypeKey":"edenUsage",
				"originalValue":"@regex@(?i)^abc.*123$" #根据测量对象过滤指标，支持正则匹配：以@regex@开头后面为正则，禁止使用()，如果要不区分大小写在开头加上(?i)，示例表示匹配以abc开头、123结尾，中间可以是任意字符，且大小写不敏感的字符串
			},{
				"moType":"OMS",
				"measUnitKey": "I2K_OS",
				"measTypeKey":"IOWait",
				"originalValue":"ip=x.x.x.x"
			}],
			"predictCron": "5|MIN",
			"algorithmModel":{ #可配置修改算法默认参数，非必须
				"modelName":"DVAnomalyDetection", #算法名称
				"version":"default", #算法版本
				"parameter":[{
					"parameterName":"manual_switch", #算法参数key值
					"parameterDefaultValue":"yes" #算法参数默认值
				},{
					"parameterName":"manual_uppervalue",
					"parameterDefaultValue":"100"
				},{
				"parameterName": "drift_detect_window_points", #概念漂移检测窗口点数
				"parameterDefaultValue": 5
				},{
				"parameterName": "concept_drift_detect_num", #漂移检测判别最小点数
				"parameterDefaultValue": 3
				},{
				"parameterName": "concept_drift_threshold", #漂移得分阈值
				"parameterDefaultValue": 0.5
				}]
			}
		}
	]
}