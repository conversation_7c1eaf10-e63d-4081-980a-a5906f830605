/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.dvanalysisenginewebsite.business.disasterrecovery.taskmanage;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulOptions;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvanalysisenginewebsite.business.common.library.LibraryClient;
import com.huawei.i2000.dvanalysisenginewebsite.business.taskmanage.TaskValidChecker;
import com.huawei.i2000.dvanalysisenginewebsite.model.PerformanceDataResult;
import com.huawei.i2000.dvanalysisenginewebsite.model.QueryPerformanceDataParam;
import com.huawei.i2000.dvanalysisenginewebsite.model.StartDisasterRecoveryTaskParam;
import com.huawei.i2000.dvanalysisenginewebsite.model.StopDisasterRecoveryTaskParam;
import com.huawei.i2000.dvanalysisenginewebsite.util.RestConstant;
import com.huawei.i2000.dvanalysisenginewebsite.util.RestUtil;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2021/3/9
 */
@Service
public class DisasterRecoveryTaskManagerClient {

    private static final OssLog LOGGER = OssLogFactory.getLogger(DisasterRecoveryTaskManagerClient.class);

    @Autowired
    LibraryClient libraryClient;

    @Autowired
    TaskValidChecker taskValidChecker;

    private static final int MAX_BODY_SIZE = 200 * 1024 * 1024;

    private static final String TASK_MANAGE_IR_URL_PREFIX = "/rest/dvanalysisengineservice/v1/disasterrecovery";

    private static final String START_IR_URL = "/startdisasterrecoverytask";

    private static final String STOP_IR_URL = "/stopdisasterrecoverytask";

    private static final String PERFORMANCE_DATA_URL = "/performancedatadetail";

    /**
     * 开始任务
     *
     * @param startDisasterRecoveryTaskParam startDisasterRecoveryTaskParam
     * @throws ServiceException ServiceException
     */
    public void startDisasterRecoveryTaskById(StartDisasterRecoveryTaskParam startDisasterRecoveryTaskParam) throws ServiceException {
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(startDisasterRecoveryTaskParam));
        RestfulParametes parametes = new RestfulParametes();
        parametes.setRawData(jsonObject.toJSONString());
        try {
            RestfulResponse restfulResponse = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
                TASK_MANAGE_IR_URL_PREFIX + START_IR_URL, parametes, null);
            RestUtil.checkIRCode(restfulResponse);
        } catch (ServiceException e) {
            LOGGER.error("startDisasterRecoveryTaskById : {}", e);
            throw e;
        }
    }

    /**
     * 停止任务，只针对周期性任务
     *
     * @param stopDisasterRecoveryTaskParam stopDisasterRecoveryTaskParam
     * @throws ServiceException ServiceException
     */
    public void stopDisasterRecoveryTaskById(StopDisasterRecoveryTaskParam stopDisasterRecoveryTaskParam) throws ServiceException {
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(stopDisasterRecoveryTaskParam));
        RestfulParametes parametes = new RestfulParametes();
        parametes.setRawData(jsonObject.toJSONString());
        try {
            RestfulResponse restfulResponse = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
                TASK_MANAGE_IR_URL_PREFIX + STOP_IR_URL, parametes, null);
            RestUtil.checkIRCode(restfulResponse);
        } catch (ServiceException e) {
            LOGGER.error("stopDisasterRecoveryTaskById : {}", e);
            throw e;
        }
    }

    /**
     * 查询性能数据
     *
     * @param queryPerformanceDataParam queryPerformanceDataParam
     * @return PerformanceDataResult performanceDataResult
     * @throws ServiceException ServiceException
     */
    public PerformanceDataResult getPerformanceData(QueryPerformanceDataParam queryPerformanceDataParam) throws ServiceException {
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(queryPerformanceDataParam));
        RestfulParametes parametes = new RestfulParametes();
        parametes.setRawData(jsonObject.toJSONString());
        RestfulOptions options = new RestfulOptions();
        options.setRestTimeout(RestfulOptions.REST_OPTIONS_TIMEOUT_MAXTIMEOUT);
        options.setOption("maxBodySize", MAX_BODY_SIZE);
        try {
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
                TASK_MANAGE_IR_URL_PREFIX + PERFORMANCE_DATA_URL, parametes, options);
            RestUtil.checkIRCode(response);
            return  JSON.parseObject(response.getResponseContent(), PerformanceDataResult.class);
        } catch (ServiceException e) {
            LOGGER.error("getPerformanceData failed, e = {}", e.getMessage());
            throw e;
        }
    }
}
