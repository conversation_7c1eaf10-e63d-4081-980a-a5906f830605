/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.i2000.dvanalysisenginewebsite.business.taskmanage;

import static com.huawei.i2000.dvanalysisenginewebsite.impl.PortraitManageServiceDelegateImpl.ADMIN_ID;
import static com.huawei.i2000.dvanalysisenginewebsite.impl.PortraitManageServiceDelegateImpl.BUSINESS_PORTRAIT_TYPE;

import com.huawei.bsp.biz.util.HttpUtil;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cloudsop.common.tokenhelper.token.OMToken;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.cbb.eam.cloudsop.ResourceGroupUtil;
import com.huawei.i2000.cbb.security.context.WebContext;
import com.huawei.i2000.cbb.sm.OperationUtil;
import com.huawei.i2000.cbb.sm.UserRoleUtil;
import com.huawei.i2000.cbb.sm.model.CloudSOPTicketCredit;
import com.huawei.i2000.cbb.sm.model.TicketInfo;
import com.huawei.i2000.dvanalysisenginewebsite.business.capacity.MultipleTreeModel;
import com.huawei.i2000.dvanalysisenginewebsite.business.capacity.MultipleTreeNode;
import com.huawei.i2000.dvanalysisenginewebsite.business.common.LogmatrixAdaptor;
import com.huawei.i2000.dvanalysisenginewebsite.business.common.TaskTypeEnum;
import com.huawei.i2000.dvanalysisenginewebsite.business.common.enums.CapacityAnalysisEnum;
import com.huawei.i2000.dvanalysisenginewebsite.business.common.library.LibraryClient;
import com.huawei.i2000.dvanalysisenginewebsite.business.common.parametercheck.BooleanCheckResult;
import com.huawei.i2000.dvanalysisenginewebsite.business.common.parametercheck.ParameterCheckClient;
import com.huawei.i2000.dvanalysisenginewebsite.business.datasourcemanage.DataSourceManager;
import com.huawei.i2000.dvanalysisenginewebsite.business.datasourcemanage.alarmtask.AlarmSelectService;
import com.huawei.i2000.dvanalysisenginewebsite.business.datasourcemanage.alarmtask.model.AlarmSourceType;
import com.huawei.i2000.dvanalysisenginewebsite.business.datasourcemanage.model.ManageObject;
import com.huawei.i2000.dvanalysisenginewebsite.business.datasourcemanage.tasktree.MeasTypeTreeBuilder;
import com.huawei.i2000.dvanalysisenginewebsite.business.datasourcemanage.tasktree.MoInstanceTreeBuilder;
import com.huawei.i2000.dvanalysisenginewebsite.business.datasourcemanage.tasktree.MoTypeTreeBuilder;
import com.huawei.i2000.dvanalysisenginewebsite.business.datasourcemanage.tasktree.model.MoInfoAlarm;
import com.huawei.i2000.dvanalysisenginewebsite.business.datasourcemanage.util.EamUtil;
import com.huawei.i2000.dvanalysisenginewebsite.business.datasourcemanage.util.MoConstants;
import com.huawei.i2000.dvanalysisenginewebsite.business.logdetectmanage.FieldResult;
import com.huawei.i2000.dvanalysisenginewebsite.business.logdetectmanage.FilterField;
import com.huawei.i2000.dvanalysisenginewebsite.business.logdetectmanage.LogDetectManage;
import com.huawei.i2000.dvanalysisenginewebsite.business.portraitmanage.PortraitManager;
import com.huawei.i2000.dvanalysisenginewebsite.business.portraitmanage.dto.DeployService;
import com.huawei.i2000.dvanalysisenginewebsite.business.taskmanage.dto.Graph;
import com.huawei.i2000.dvanalysisenginewebsite.business.taskmanage.dto.LogFilterData;
import com.huawei.i2000.dvanalysisenginewebsite.business.taskmanage.dto.PqlsEntity;
import com.huawei.i2000.dvanalysisenginewebsite.business.taskmanage.dto.PreExecutionNode;
import com.huawei.i2000.dvanalysisenginewebsite.business.taskmanage.dto.QFilterElement;
import com.huawei.i2000.dvanalysisenginewebsite.business.taskmanage.dto.trigger.TemplateTreeModel;
import com.huawei.i2000.dvanalysisenginewebsite.business.taskmanage.dto.trigger.TemplateTreeNode;
import com.huawei.i2000.dvanalysisenginewebsite.business.taskmanage.dto.trigger.TemplateTreePath;
import com.huawei.i2000.dvanalysisenginewebsite.business.taskmanage.validate.AlarmSelectList;
import com.huawei.i2000.dvanalysisenginewebsite.business.taskmanage.validate.AlarmTypeSelectItem;
import com.huawei.i2000.dvanalysisenginewebsite.business.triggermanage.TriggerManagerClient;
import com.huawei.i2000.dvanalysisenginewebsite.delegate.ConfigDataServiceDelegate;
import com.huawei.i2000.dvanalysisenginewebsite.delegate.TaskAnalysisResultDelegate;
import com.huawei.i2000.dvanalysisenginewebsite.impl.util.ConfigurationUtil;
import com.huawei.i2000.dvanalysisenginewebsite.model.AggregateMachineGroup;
import com.huawei.i2000.dvanalysisenginewebsite.model.AggregateMo;
import com.huawei.i2000.dvanalysisenginewebsite.model.AlgorithmModel;
import com.huawei.i2000.dvanalysisenginewebsite.model.AnalysisTask;
import com.huawei.i2000.dvanalysisenginewebsite.model.AnalysisTaskList;
import com.huawei.i2000.dvanalysisenginewebsite.model.AnalysisTaskListQueryParam;
import com.huawei.i2000.dvanalysisenginewebsite.model.BusinessPortrait;
import com.huawei.i2000.dvanalysisenginewebsite.model.ColumnMapping;
import com.huawei.i2000.dvanalysisenginewebsite.model.ConfigData;
import com.huawei.i2000.dvanalysisenginewebsite.model.CustomColumnsParam;
import com.huawei.i2000.dvanalysisenginewebsite.model.DataSourceQueryParam;
import com.huawei.i2000.dvanalysisenginewebsite.model.DataSourceQueryResult;
import com.huawei.i2000.dvanalysisenginewebsite.model.FieldMapping;
import com.huawei.i2000.dvanalysisenginewebsite.model.FlowAction;
import com.huawei.i2000.dvanalysisenginewebsite.model.HostPortrait;
import com.huawei.i2000.dvanalysisenginewebsite.model.IndicatorInfo;
import com.huawei.i2000.dvanalysisenginewebsite.model.IndicatorInfoResult;
import com.huawei.i2000.dvanalysisenginewebsite.model.IndicatorValidParameter;
import com.huawei.i2000.dvanalysisenginewebsite.model.IpsDetail;
import com.huawei.i2000.dvanalysisenginewebsite.model.LibraryCommonResponse;
import com.huawei.i2000.dvanalysisenginewebsite.model.LogFilterRule;
import com.huawei.i2000.dvanalysisenginewebsite.model.LogmatrixSolution;
import com.huawei.i2000.dvanalysisenginewebsite.model.MeasObjectInfo;
import com.huawei.i2000.dvanalysisenginewebsite.model.MeasObjectVO;
import com.huawei.i2000.dvanalysisenginewebsite.model.MeasType4MV;
import com.huawei.i2000.dvanalysisenginewebsite.model.MeasUnit4MV;
import com.huawei.i2000.dvanalysisenginewebsite.model.ModelParameter;
import com.huawei.i2000.dvanalysisenginewebsite.model.MultipleExecutionNode;
import com.huawei.i2000.dvanalysisenginewebsite.model.MultipleRelationIndicator;
import com.huawei.i2000.dvanalysisenginewebsite.model.Paging;
import com.huawei.i2000.dvanalysisenginewebsite.model.PqlQueryParam;
import com.huawei.i2000.dvanalysisenginewebsite.model.PqlValidateResponse;
import com.huawei.i2000.dvanalysisenginewebsite.model.ProductPortrait;
import com.huawei.i2000.dvanalysisenginewebsite.model.PrometheusData;
import com.huawei.i2000.dvanalysisenginewebsite.model.QueryIndicatorInfoListById;
import com.huawei.i2000.dvanalysisenginewebsite.model.ResponseEntity;
import com.huawei.i2000.dvanalysisenginewebsite.model.TaskIndicator;
import com.huawei.i2000.dvanalysisenginewebsite.model.TriggerAlgorithmModel;
import com.huawei.i2000.dvanalysisenginewebsite.model.TriggerExecutionNode;
import com.huawei.i2000.dvanalysisenginewebsite.model.TriggerIndicator;
import com.huawei.i2000.dvanalysisenginewebsite.model.TriggerTaskMessage;
import com.huawei.i2000.dvanalysisenginewebsite.model.TriggerTemplateIndicator;
import com.huawei.i2000.dvanalysisenginewebsite.model.UITreeNode;
import com.huawei.i2000.dvanalysisenginewebsite.util.ContextUtils;
import com.huawei.i2000.dvanalysisenginewebsite.util.ExceptionConstant;
import com.huawei.i2000.dvanalysisenginewebsite.util.ParamCheckConstant;
import com.huawei.i2000.dvanalysisenginewebsite.util.PermissionsDomainUtils;
import com.huawei.i2000.dvanalysisenginewebsite.util.RestConstant;
import com.huawei.i2000.dvanalysisenginewebsite.util.RestUtil;
import com.huawei.i2000.dvanalysisenginewebsite.util.operationlog.LogConstant;
import com.huawei.i2000.dvanalysisenginewebsite.util.operationlog.OperationLogUtil;
import com.huawei.i2000.util.runtime.SystemUtils;
import com.huawei.oms.eam.mo.ManagedObject;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.TypeReference;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.scheduling.support.CronExpression;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Queue;
import java.util.Set;
import java.util.Stack;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

/**
 * TaskValidChecker
 *
 * <AUTHOR>
 * @since ?
 */
@Component
public class TaskValidChecker {

    private static final OssLog LOGGER = OssLogFactory.getLogger(TaskValidChecker.class);

    private static final String CHECK_INDICATOR_URL = "/rest/dvanalysisengineservice/v1/taskmanage/checkindicator";

    private static final String QUERY_ROLEIDS_BY_USERID = "/rest/plat/uam/v1/_users/";

    private static final Pattern FLOAT_FORMAT_PATTERN = Pattern.compile("^(0|[1-9]\\d*)(\\.\\d{1})+$");

    private static final Pattern FLOW_DIAGRAM_PATTERN = Pattern.compile("flow-diagram_N[0-9]+");

    private static final String ROOT_DN = "/";

    private static final String LOG_COLOR_GRAY = "#c5c5c5";

    private static final String LOG_COLOR_PURPLE = "#f0e7ff";

    public static final String CONCEPT_DRIFT_FEATURE = "conceptDrift";

    private static final int AGGREGATE_TASK = 2;

    private static final int BY_MO = 0;

    private static final String WINDOW_SIZE = "window_size";

    private static final String DETECT_NUM = "detect_num";

    private static final String TRAIN_POINT_NEED = "train_point_need";

    private static final String DETECT_POINT_NEED = "detect_point_need";

    private static final String DETECT_OUTPUT_POINT = "detect_output_point";

    private static final String DRIFT_SCORE_DETECT_WINDOW = "drift_detect_window_points";

    private static final String DRIFT_JUDGE_POINT = "concept_drift_detect_num";

    private static final String TOGGLING_ALARM_TRUSTLIST = "toggling_alarm_trust_list";
    private static final String UPPER_LIMIT_FLAG = "upperlimit_flag";
    private static final String LOWER_LIMIT_FLAG = "lowerlimit_flag";
    private static final String THRESHOLD_NOT_DISPLAY = "no";

    private static final int YES = 1;

    private static final int LEVEL_SEVERITY = 1;

    private static final int LEVEL_INFO = 4;

    private static final int NO = 0;

    private static final int ES_FILE_FIELD_COUNT = 3;

    private static final int ES_FILTER_CONTENT_MAX_LENGTH = 256;

    private static final int ALARM_FILTER_MAX_LEN = 2048;

    private static final int ALARM_DEVICE_TYPE_FILTER_MAX_LEN = 30000;

    private static final int MAIN_NODE = 0;

    private static final int SUB_NODE = 1;

    private static HashSet<String> predictTimeSet = new HashSet<>();

    private static HashSet<String> trainTimeSet = new HashSet<>();

    private static List<String> alarmTypeValue = new ArrayList<>();

    private static HashSet<String> timeUnit = new HashSet<>();

    private static final HashSet<String> capacityAlarmTimeRangeSet = new HashSet<>();

    private static final Pattern NODE_NAME_BLACK_CHARACTER = Pattern.compile("[#%&+|><';?\"()]");

    private static final Pattern CLUSTER_NAME_BLACK_CHARACTER = Pattern.compile("[#%&+=|><';?\"\\/()]");

    private static final List<String> SQL_BLACK_LIST = new ArrayList<>();

    private static final List<String> ALARM_JSON_FIELDS = Collections.unmodifiableList(
        Arrays.asList("alarmId", "alarmName", "eventType", "deviceTypeId", "nativeMoDn", "moi", "severity"));

    private static final List<String> ALARM_JSON_OPERATOR = Collections.unmodifiableList(
        Arrays.asList("in", "like", "startwith", "endwith"));

    private static final List<String> ALARM_EVENT_TYPE = Collections.unmodifiableList(
        Arrays.asList("1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18",
            "19", "20", "21"));
    private static final List<String> ES_FILTER_TYPE = Collections.unmodifiableList(Arrays.asList("matchQuery", "termQuery", "wildcardQuery"));

    private static final List<Integer> CONCEPT_DRIFT_TASK_TYPE = Collections.unmodifiableList(
        Arrays.asList(TaskTypeEnum.INDICATORTASK.enumToInt(), TaskTypeEnum.INDICATORPREDICTTASK.enumToInt()));

    private static final List<String> ALARM_SEVERITY = Collections.unmodifiableList(Arrays.asList("1", "2", "3", "4"));

    private static final HashMap<String,String> ALGORITHM_MAP = new HashMap<>();

    private static final String INDICATOR_PREDICT_AGGREGATE_TASK_NUMBER = "INDICATOR_PREDICT_AGGREGATE_TASK_NUMBER";

    private static final String INDICATOR_PREDICT_AGGREGATE_GROUP_INDICATOR_NUMBER = "INDICATOR_PREDICT_AGGREGATE_GROUP_INDICATOR_NUMBER";

    private static final String KPI_AGGREGATE_GROUP_NUM = "KPI_AGGREGATE_GROUP_NUM";

    private static final String PREDICT_CURRENT_MAX_INSERT_DATA = "PREDICT_CURRENT_MAX_INSERT_DATA";

    private static final TypeReference<List<Map<String, String>>> TEMPLATE_FIELD = new TypeReference<List<Map<String, String>>>() { };

    private static final Pattern ALARM_CONTENT_PATTERN = Pattern.compile("\\{\\$(.*?)\\}");

    private static final Pattern EXECUTE_BLACK_CHARACTER = Pattern.compile("[#%&+|'?\"\\\\]");

    private static final Pattern ALARM_ID_REGEX = Pattern.compile("^[0-9]+(,[0-9]+)*$");

    private static final Pattern FLOAT_VALUE_PATTERN = Pattern.compile("^(0|[1-9]\\d*)\\.\\d{1,3}$");

    private static final Pattern INT_VALUE_PATTERN = Pattern.compile("^(0|[1-9]\\d*)$");

    private static final String NODE_TYPE_LOG = "Associated_LOG";

    private static final String INTELLIGENT_RECOMMENDATION = "true";

    /**
     * 转换用时间戳单位
     */
    private static final long MIN = 60 * 1000;

    private static final long HOUR = 60 * MIN;

    private static final long DAY = 24 * HOUR;

    private static final long WEEK = 7 * DAY;

    private static final Boolean CONTAINER = Boolean.parseBoolean(System.getenv("CONTAINER"));

    private final ThreadPoolExecutor validQueryManageExecutor = new ThreadPoolExecutor(5, 50, 5, TimeUnit.MINUTES,
        new LinkedBlockingQueue<>(100), new CustomizableThreadFactory("ValidManage-Thread-"));

    @Autowired
    ConfigurationUtil configurationUtil;

    @Autowired
    MeasTypeTreeBuilder measTypeTreeBuilder;

    @Autowired
    MoTypeTreeBuilder moTypeTreeBuilder;

    @Autowired
    ParameterCheckClient parameterCheckClient;

    @Autowired
    LibraryClient libraryClient;

    @Autowired
    TaskManagerClient taskManagerClient;

    @Autowired
    DataSourceManager dataSourceManager;

    @Autowired
    TriggerManagerClient triggerManagerClient;

    @Autowired
    TaskAnalysisResultDelegate taskAnalysisResultDelegate;

    @Autowired
    LogmatrixAdaptor logmatrixAdaptor;

    @Autowired
    PortraitManager portraitManager;

    @Autowired
    MoInstanceTreeBuilder moInstanceTreeBuilder;

    @Autowired
    LogDetectManage logDetectManage;

    @Autowired
    ConfigDataServiceDelegate configDataServiceDelegate;

    static {
        timeUnit.add("MIN");
        timeUnit.add("HOUR");
        timeUnit.add("DAY");
        timeUnit.add("WEEK");
        predictTimeSet.add("1|MIN");
        predictTimeSet.add("5|MIN");
        predictTimeSet.add("10|MIN");
        predictTimeSet.add("30|MIN");
        trainTimeSet.add("1|DAY");
        trainTimeSet.add("3|DAY");
        trainTimeSet.add("1|WEEK");
        trainTimeSet.add("2|WEEK");
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_COMMUNICATIONALARM));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_EQUIPMENTALARM));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_PROCESSINGERRORALARM));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_QUALITYOFSERVICEALARM));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_ENVIRONMENTALALARM));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_INTEGRITYALARM));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_OPERATIONALARM));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_PHYSICALRESOURCEALARM));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_SECURITYALARM));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_TIMEDOMAINALARM));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_PROPERTYCHANGE));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_OBJECTCREATION));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_OBJECTDELETE));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_RELATIONSHIPCHANGE));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_STATECHANGE));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_OUTECHANGE));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_PROTECTIONSWITCHING));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_OVERLIMIT));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_FILETRANSFERSTATUS));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_BACKUPSTATUS));
        alarmTypeValue.add(ResourceUtil.getMessage(ParamCheckConstant.ALARM_TYPE_HEARTBEAT));
        SQL_BLACK_LIST.add("ALTER");
        SQL_BLACK_LIST.add("DELETE");
        SQL_BLACK_LIST.add("UPDATE");
        SQL_BLACK_LIST.add("INSERT");
        SQL_BLACK_LIST.add("DROP");
        SQL_BLACK_LIST.add("GRANT");
        SQL_BLACK_LIST.add("REVOKE");
        SQL_BLACK_LIST.add("EXECUTE");
        SQL_BLACK_LIST.add("EXEC");
        SQL_BLACK_LIST.add("DECLARE");
        SQL_BLACK_LIST.add("CALL");
        SQL_BLACK_LIST.add("PROCEDURE");
        SQL_BLACK_LIST.add("TRUNCATE");
        ALGORITHM_MAP.put("Associated_KPI", "CorrelationAnalysis");
        ALGORITHM_MAP.put("Associated_ALARM", "IndicatorAlarmAnalysis");
        ALGORITHM_MAP.put("Associated_LOG", "IndicatorLogAnalysis");
        capacityAlarmTimeRangeSet.add("unLimit");
        capacityAlarmTimeRangeSet.add("1|DAY");
        capacityAlarmTimeRangeSet.add("1|WEEK");
        capacityAlarmTimeRangeSet.add("1|MONTH");
        capacityAlarmTimeRangeSet.add("6|MONTH");
        capacityAlarmTimeRangeSet.add("1|YEAR");
    }

    /**
     * 校验任务合法性
     *
     * @param task 任务
     * @param httpServletRequest 请求
     * @param moListByField dn
     * @param isTicketValid isTicketValid
     * @return 是否合法
     * @throws ServiceException excepton
     */
    public boolean isTaskValidate(AnalysisTask task, HttpServletRequest httpServletRequest, Set<String> moListByField,
        Boolean isTicketValid) throws ServiceException {
        if (!commonCheck(task, moListByField)) {
            return false;
        }
        return task.getTaskType() != TaskTypeEnum.CORRELATIONTASK.enumToInt() ? cycleTaskCheck(task, moListByField,
            httpServletRequest, isTicketValid) : checkCorrelationTask(task, isTicketValid, moListByField);
    }

    /**
     * 公用校验
     *
     * @param task 任务
     * @param moListByField 有权限的DN
     * @return 是否校验成功
     */
    private boolean commonCheck(AnalysisTask task, Set<String> moListByField) {
        if (TaskTypeEnum.INDICATORPREDICTTASK.enumToInt() == task.getTaskType()
            && Objects.equals(TaskConstant.INDICATOR_PREDICT_SCENARIO_UNITED, task.getIndicatorPredictScenario())) {
            return predictHostAndSiteCheck(task, moListByField);
        }
        if (task.getTaskType() == TaskTypeEnum.LOGTASK.enumToInt()) {
            return logMatrixParamValidate(task) && logDetectTaskSpecialCheck(task);
        }
        if (task.getTaskType() == TaskTypeEnum.RESOURCESCHEDULETASK.enumToInt()) {
            return resourceScheduleTaskSpecialCheck(task);
        }
        return true;
    }

    private boolean predictHostAndSiteCheck(AnalysisTask task, Set<String> moListByField) {
        List<String> dns = null;
        if (moListByField != null) {
            dns = new ArrayList<>(moListByField);
        }
        List<UITreeNode> uiTreeNodes = moInstanceTreeBuilder.queryOneLayerWithCluster(ROOT_DN, dns);
        List<TaskIndicator> indicators = task.getIndicatorList();
        List<String> uiTreeNodesPairs = new ArrayList<>();
        uiTreeNodes.forEach(uiTreeNode -> {
            if (StringUtils.isNotEmpty(uiTreeNode.getClusterName())) {
                uiTreeNodesPairs.add(uiTreeNode.getClusterName() + "@" + uiTreeNode.getSiteId());
            }
        });
        boolean checkClusterAndSiteId = true;
        for (TaskIndicator indicator : indicators) {
            if (!uiTreeNodesPairs.contains(indicator.getClusterName() + "@" + indicator.getSiteId())) {
                LOGGER.error(
                    "[predictHostAndSiteCheck] check clusterName or siteId error, please check, the clusterName is {}, the siteId is {}",
                    indicator.getClusterName(), indicator.getSiteId());
                checkClusterAndSiteId = false;
                break;
            }
        }
        return checkClusterAndSiteId;
    }

    private boolean resourceScheduleTaskSpecialCheck(AnalysisTask task) {
        // 校验任务执行周期--不支持单次执行
        if (task.getPeriodicType() != TaskConstant.TASK_PERIOD_TYPE_CYCLE || StringUtils.isEmpty(
            task.getPredictCron()) || StringUtils.isEmpty(task.getTrainCron())) {
            LOGGER.error("Resource schedule task only support period type.");
            return false;
        }
        return true;
    }

    private boolean logMatrixParamValidate(AnalysisTask task) {
        if (!"4".equals(task.getDatasourceId())) {
            return true;
        }
        try {
            // 判断日志解决方案是否存在
            List<LogmatrixSolution> logMatrixSolutions = logmatrixAdaptor.getLogmatrixSolutionList();
            List<String> solutionNames = logMatrixSolutions.stream().map(LogmatrixSolution::getSolutionName).collect(
                Collectors.toList());
            if (!solutionNames.contains(task.getLogSolutionName())) {
                LOGGER.error("Log analysis solution is not exist, log solutionName is {}", task.getLogSolutionName());
                return false;
            }
            // 判断日志数据源是否存在
            List<String> logIndexNames = logmatrixAdaptor.getLogTypeList(task.getLogSolutionType());
            if (!logIndexNames.contains(task.getLogIndexName())) {
                LOGGER.error("Log datasource is not exist, log datasource is {}.", task.getLogIndexName());
                return false;
            }
            // 判断日志过滤内容是否存在
            List<LogFilterRule> filterColumns = logmatrixAdaptor.getLogFilterRuleList(task.getLogSolutionType(),
                task.getLogIndexName());
            List<String> logFilterNames = filterColumns.stream().map(LogFilterRule::getFilterName).collect(
                Collectors.toList());
            String taskFilterColumns = task.getLogFilterColumns();
            JSONObject logFilterRules = JSONObject.parseObject(taskFilterColumns);
            for (Map.Entry<String, Object> entry : logFilterRules.entrySet()) {
                if (!logFilterNames.contains(entry.getKey())) {
                    LOGGER.error("Log filter column is not exist. filter key is {}", entry.getKey());
                    return false;
                }
                if (entry.getValue().toString().length() > 256) {
                    LOGGER.error("Log filter column is over 256");
                    return false;
                }
            }
            // 分析字段只能为message
            if (!"Message".equals(task.getLogAnalysisField().trim())) {
                LOGGER.error("Log analysis is not 'Message'.");
                return false;
            }
        } catch (ServiceException e) {
            LOGGER.error("something wrong with calling log service api.", e);
            return false;
        }
        return true;
    }

    private boolean logDetectTaskSpecialCheck(AnalysisTask task) {
        // 日志分析校验参数 -- 因为日志分析算法参数支持用户随意输入正则，算法公共校验无法支持
        List<ModelParameter> modelParameters = JSONArray.parseArray(task.getAlgorithmParam(), ModelParameter.class);
        if (!TaskValidCheckerUtils.checkLogAlgorithmParameter(modelParameters)) {
            LOGGER.error("log task algorithm parameter error.");
            return false;
        }

        // 校验任务执行周期--不支持单次执行
        if (task.getPeriodicType() != TaskConstant.TASK_PERIOD_TYPE_CYCLE || StringUtils.isEmpty(
            task.getLogPredictTime()) || StringUtils.isEmpty(task.getLogTrainTime())) {
            LOGGER.error("Log detect task only support period type.");
            return false;
        } else {
            if (task.getPredictCron().contains("|")) {
                if(!logPredictCheck(task.getPredictCron())){
                    return false;
                }
            }

            if (task.getLogPredictTime().contains("|")) {
                if(!logPredictCheck(task.getLogPredictTime())){
                    return false;
                }
            }

            if (task.getLogTrainTime().contains("|")) {
                if(!logTrainCheck(task.getLogTrainTime())){
                    return false;
                }
            }
        }

        return true;
    }

    private boolean logPredictCheck(String logPredictTime) {
        String[] logPredictTimes = logPredictTime.split("\\|");
        if ("MIN".equals(logPredictTimes[1])) {
            if (Integer.parseInt(logPredictTimes[0]) > 1440 || Integer.parseInt(logPredictTimes[0]) < 1) {
                return false;
            }
        } else if ("HOUR".equals(logPredictTimes[1])) {
            if (Integer.parseInt(logPredictTimes[0]) > 24 || Integer.parseInt(logPredictTimes[0]) < 1) {
                return false;
            }
        } else {
            return false;
        }
        return true;
    }

    private boolean logTrainCheck(String logTrainTime) {
        String[] logTrainTimes = logTrainTime.split("\\|");
        if ("DAY".equals(logTrainTimes[1])) {
            if (Integer.parseInt(logTrainTimes[0]) > 30 || Integer.parseInt(logTrainTimes[0]) < 1) {
                return false;
            }
        } else if ("WEEK".equals(logTrainTimes[1])) {
            if (Integer.parseInt(logTrainTimes[0]) > 4 || Integer.parseInt(logTrainTimes[0]) < 1) {
                return false;
            }
        } else {
            return false;
        }
        return true;
    }

    /**
     * 任务数据源校验
     *
     * @param task 任务
     * @param moListByField moListByField
     * @param httpServletRequest request
     * @param isTicketChecked isTicketChecked
     * @return 是否校验成功
     * @throws ServiceException 异常
     */
    public boolean cycleTaskCheck(AnalysisTask task, Set<String> moListByField, HttpServletRequest httpServletRequest,
        Boolean isTicketChecked) throws ServiceException {
        if (!canModifyCheck(task) || !cronCheck(task)) {
            return false;
        }
        if (!modelCheck(task)) {
            return false;
        }
        if (task.getTaskType().equals(TaskTypeEnum.CAPACITYTASK.enumToInt())) {
            return checkCapacityTask(task, moListByField);
        }
        if (task.getTaskType().equals(TaskTypeEnum.INDICATORTASK.enumToInt()) && !task.getDatasourceId().equals("1") && !task.getDatasourceId().equals("8") && Integer.parseInt(task.getDatasourceId()) <= 99) {
            return false;
        }
        if (task.getDatasourceId().equals("1") && !isPerformanceTaskValid(task, moListByField)) {
            throw new ServiceException(ResourceUtil.getMessage(LogConstant.EXCEPTION_INDICATOR_ERROR, ContextUtils.getContext().getLocale()));
        }
        if (task.getTaskType().equals(TaskTypeEnum.ALARMTASK.enumToInt()) && !task.getDatasourceId().equals("3")) {
            return false;
        }
        if (task.getTaskType().equals(TaskTypeEnum.ALARMTASK.enumToInt()) && !isAlarmTaskValid(task)) {
            return false;
        }
        if (task.getTaskType().equals(TaskTypeEnum.LOGTASK.enumToInt()) && !validLogParam(task)) {
            return false;
        }
        return (Integer.parseInt(task.getDatasourceId()) <= 99 || task.getTaskType().equals(TaskTypeEnum.LOGTASK.enumToInt()))
            || isCustomTaskValid(task, httpServletRequest, isTicketChecked);
    }

    private boolean validLogParam(AnalysisTask task) throws ServiceException {
        task.getLogFieldMapping().setAnalysisField(task.getLogAnalysisField());
        return validLogParam(task.getDatasourceId(), task.getLogIndexName(), task.getLogIndexTemplate(),
            task.getLogFieldMapping(), task.getLogFilterColumns());
    }

    private boolean validLogParam(String dataSourceIdStr, String indexName, String indexTemplate,
        FieldMapping fieldMapping, String filterColumns) throws ServiceException {
        if ("4".equals(dataSourceIdStr)) {
            return true;
        }
        Integer datasourceId = Integer.valueOf(dataSourceIdStr);
        if (!isDatasourceValid(datasourceId)) {
            return false;
        }
        // 校验索引模板
        List<String> indices;
        try {
            indices = logDetectManage.getEsIndices(datasourceId, indexName);
        } catch (ServiceException e) {
            LOGGER.error("valid log param getEsIndices error.", e);
            throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.ES_DATASOURCE_VALID_ERROR,ContextUtils.getContext().getLocale()));
        }
        if (!indices.contains(indexTemplate)) {
            return false;
        }

        FieldResult fields;
        try {
            fields = logDetectManage.getEsFields(datasourceId, indexTemplate);
        } catch (ServiceException e) {
            LOGGER.error("valid log param getEsFields error.", e);
            throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.ES_DATASOURCE_VALID_ERROR,ContextUtils.getContext().getLocale()));
        }
        if (!fields.getLogAnalysisFields().contains(fieldMapping.getAnalysisField())) {
            return false;
        }
        if (!fields.getLogTimeFields().contains(fieldMapping.getTimeField())) {
            return false;
        }
        if (StringUtils.isNotEmpty(fieldMapping.getSerialNoField())
            && !fields.getLogSerialNoFields().contains(fieldMapping.getSerialNoField())) {
            return false;
        }
        if (StringUtils.isNotEmpty(fieldMapping.getFileFields())) {
            List<String> fileFields = JSON.parseArray(fieldMapping.getFileFields(), String.class);
            if (fileFields.size() > ES_FILE_FIELD_COUNT
                || fileFields.stream().anyMatch(s -> !fields.getLogFileFields().contains(s))) {
                return false;
            }
        }
        if (StringUtils.isEmpty(filterColumns)) {
            return false;
        }
        List<FilterField> filterFields = JSON.parseArray(filterColumns, FilterField.class);
        return filterFields.stream().allMatch(filterField -> fields.getLogFilterFields().contains(filterField.getField())
            && ES_FILTER_TYPE.contains(filterField.getType())
            && StringUtils.isNotEmpty(filterField.getValue())
            && filterField.getValue().length() <= ES_FILTER_CONTENT_MAX_LENGTH);
    }

    private boolean checkCapacityTask(AnalysisTask task, Set<String> moListByField) throws ServiceException {
        MultipleTreeModel multipleTreeModel = JSONObject.parseObject(task.getMultipleTaskMessage().getRelationTree(), MultipleTreeModel.class);
        List<MultipleTreeNode> nodes = multipleTreeModel.getNodes();
        for (MultipleTreeNode multipleTreeNode : nodes) {
            List<TaskIndicator> taskIndicators = new ArrayList<>();
            for (MultipleRelationIndicator multipleRelationIndicator :multipleTreeNode.getNodeParam().getIndicatorList()) {
                // 校验指标是否有权限
                if (moListByField != null && !moListByField.contains(multipleRelationIndicator.getDn())) {
                    LOGGER.error("indicator authentication failed, dn = {}", multipleRelationIndicator.getDn());
                    OperationLogUtil.sendAuthFailedSecurityLog(LogConstant.MO_AUTHENTICATION_FAILED,
                        new Object[]{multipleRelationIndicator.getDn()});
                    return false;
                }
                taskIndicators.add(JSONObject.parseObject(JSON.toJSONString(multipleRelationIndicator), TaskIndicator.class));
            }
            // 校验双机指标网元类型、测量单元、测量对象是否相同
            if (checkIndicatorAndSolutionNotValid(moListByField, multipleTreeNode, taskIndicators)) {
                return false;
            }
            // 校验资源树节点的指标数量（按网元类型）是不是正确的
            checkEamTreeIndicators(multipleTreeNode);
        }
        // 校验是否重训练、告警相关配置是否合法
        if (task.getCapacityIfRePredict() != null && task.getCapacityIfRePredict() != YES && task.getCapacityIfRePredict() != NO) {
            LOGGER.error("task capacity if re predict is not null or 1");
            return false;
        }
        if (task.getReportAlarm() != null && task.getReportAlarm() != NO) {
            if (task.getAlarmNormalLevel() != null && (task.getAlarmNormalLevel() < LEVEL_SEVERITY || task.getAlarmNormalLevel() > LEVEL_INFO)) {
                LOGGER.error("capacity alarm normal level error");
                return false;
            }
            if (task.getAlarmSeverityLevel() == null || task.getAlarmSeverityLevel() < LEVEL_SEVERITY || task.getAlarmSeverityLevel() > LEVEL_INFO) {
                LOGGER.error("capacity alarm severity level error");
                return false;
            }
            if (!validAlarmTimeRange(task.getAlarmReportTimeRange())) {
                LOGGER.error("capacity alarm time range error");
                return false;
            }
        }
        return true;
    }

    private void checkEamTreeIndicators(MultipleTreeNode multipleTreeNode) throws ServiceException {
        if (!CapacityAnalysisEnum.TEMPLATE_EAM_TREE_TYPE.equals(multipleTreeNode.getType())) {
            return;
        }
        List<MultipleRelationIndicator> allIndicatorList = multipleTreeNode.getNodeParam().getIndicatorList();
        MultipleRelationIndicator mainIndicator = getMainMultipleRelationIndicator(allIndicatorList);
        if (mainIndicator == null) {
            LOGGER.error("getMainMultipleRelationIndicator is null, nodeName = {}", multipleTreeNode.getTitle());
            throw new ServiceException(ResourceUtil.getMessage(LogConstant.EXCEPTION_INDICATOR_ERROR,
                ContextUtils.getContext().getLocale()) + ResourceUtil.getMessage(LogConstant.NODE_NAME,
                ContextUtils.getContext().getLocale()) + multipleTreeNode.getTitle());
        }
        List<MultipleRelationIndicator> subIndicatorList = allIndicatorList.stream()
            .filter(indicator -> indicator.getEamNodeType() != null && indicator.getEamNodeType() == SUB_NODE)
            .collect(Collectors.toList());

        // 指标分类进行检查
        Map<String, List<MultipleRelationIndicator>> groupedMap = subIndicatorList.stream()
            .collect(Collectors.groupingBy(indicator ->
                indicator.getMeasUnitKey() +
                    indicator.getMeasTypeKey() +
                    indicator.getOriginalValue()
            ));

        for (Map.Entry<String, List<MultipleRelationIndicator>> entry : groupedMap.entrySet()) {
            TaskIndicator paramIndicator = JSON.parseObject(JSON.toJSONString(entry.getValue().get(0)), TaskIndicator.class);
            List<TaskIndicator> taskIndicators = moTypeTreeBuilder.getIndicatorSubInstances(mainIndicator.getDn(),
                mainIndicator.getMoType(), Collections.singletonList(paramIndicator), false);
            if (entry.getValue().size() != taskIndicators.size()) {
                LOGGER.error("sub indicator number is wrong, nodeName = {}", multipleTreeNode.getTitle());
                throw new ServiceException(ResourceUtil.getMessage(LogConstant.EXCEPTION_INDICATOR_ERROR,
                    ContextUtils.getContext().getLocale()) + ResourceUtil.getMessage(LogConstant.NODE_NAME,
                    ContextUtils.getContext().getLocale()) + multipleTreeNode.getTitle());
            }
        }
    }

    private MultipleRelationIndicator getMainMultipleRelationIndicator(List<MultipleRelationIndicator> indicatorList) {
        for (MultipleRelationIndicator indicator : indicatorList) {
            if (indicator.getEamNodeType() != null && indicator.getEamNodeType() == MAIN_NODE) {
                return indicator;
            }
        }
        return null;
    }

    private boolean checkIndicatorAndSolutionNotValid(Set<String> moListByField, MultipleTreeNode multipleTreeNode,
        List<TaskIndicator> taskIndicators) throws ServiceException {
        if (CapacityAnalysisEnum.TEMPLATE_HIGH_AVAILABLE_MAIN_TYPE.equals(multipleTreeNode.getType()) ||
            CapacityAnalysisEnum.TEMPLATE_HIGH_AVAILABLE_COMM_TYPE.equals(multipleTreeNode.getType())) {
            TaskIndicator baseIndicator = taskIndicators.get(0);
            for (TaskIndicator taskIndicator : taskIndicators) {
                if (!taskIndicator.getMoType().equals(baseIndicator.getMoType())
                    || !taskIndicator.getMeasUnitKey().equals(baseIndicator.getMeasUnitKey())
                    || !taskIndicator.getMeasTypeKey().equals(baseIndicator.getMeasTypeKey())) {
                    return true;
                }
            }
        }
        MultipleExecutionNode node = multipleTreeNode.getNodeParam();
        if (!isSolutionIdValid(node.getSolutionId(), node.getSolutionName(), moListByField)
            || !isSolutionValid(node.getSolutionId(), node.getSolutionName(), moListByField)
            || !checkIndicatorList(taskIndicators, 0)) {
            throw new ServiceException(ResourceUtil.getMessage(LogConstant.EXCEPTION_INDICATOR_ERROR,
                ContextUtils.getContext().getLocale()) + ResourceUtil.getMessage(LogConstant.NODE_NAME,
                ContextUtils.getContext().getLocale()) + multipleTreeNode.getTitle());
        }
        return false;
    }

    /**
     * ticket是否合法
     *
     * @param request 请求
     * @param ticket ticket
     * @param operation 操作名
     * @return 是否合法
     */
    public boolean isTicketValid(HttpServletRequest request, String ticket, String operation) {
        boolean checkResult = false;
        try {
            TicketInfo ticketInfo = new TicketInfo();
            ticketInfo.setSessionId(HttpUtil.getSessionId(request));
            ticketInfo.setService(TaskConstant.SERVICE_NAME);
            ticketInfo.setOperation(operation);
            CloudSOPTicketCredit cloudSOPTicketCredit = new CloudSOPTicketCredit();
            cloudSOPTicketCredit.setTicketInfo(ticketInfo);
            cloudSOPTicketCredit.setTicketValue(ticket);
            checkResult = OperationUtil.ticketCheck(cloudSOPTicketCredit);
            return checkResult;
        } finally {
            String terminal = request.getHeader(HttpUtil.CLIENT_ADDR_HEADER);
            OperationLogUtil.sendSecAuthSecurityLog(terminal, checkResult);
        }

    }

    /**
     * 不可修改项校验
     *
     * @param task task
     * @return boolean
     * @throws ServiceException ServiceException
     */
    private boolean canModifyCheck(AnalysisTask task) throws ServiceException {
        if (task.getTaskId() != null) {
            AnalysisTask analysisTaskById = taskManagerClient.getAnalysisTaskById(task.getTaskId());
            if (analysisTaskById == null || analysisTaskById.getCanModify() == TaskConstant.CANNOT_MODIFY) {
                return false;
            }
            boolean isFoo = !analysisTaskById.getStartStatus().equals(TaskConstant.TASK_START_STATUS_STOP)
                && !analysisTaskById.getStartStatus().equals(TaskConstant.TASK_START_STATUS_INITIAL);
            boolean isEqualsTaskStatusRunning = analysisTaskById.getStatus() != null && analysisTaskById.getStatus()
                .equals(TaskConstant.TASK_STATUS_RUNNING);
            boolean isEqualstaskStartStatusTrain = analysisTaskById.getTrainStatus() != null
                && analysisTaskById.getTrainStatus().equals(TaskConstant.TASK_START_STATUS_TRAIN);
            if (isFoo || isEqualsTaskStatusRunning || isEqualstaskStartStatusTrain) {
                return false;
            }
            if (!analysisTaskById.getTaskName().equals(task.getTaskName()) || !analysisTaskById.getTaskType()
                .equals(task.getTaskType())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 校验cron表达式
     *
     * @param task task
     * @return boolean
     */
    public boolean cronCheck(AnalysisTask task) {
        String[] predictUnits = {"MIN"};
        String[] trainUnits = {"DAY", "WEEK"};
        if (task.getTaskType().equals(TaskTypeEnum.CAPACITYTASK.enumToInt()) || task.getTaskType().equals(TaskTypeEnum.LOGTASK.enumToInt())) {
            return periodValid(task.getTrainCron(), trainUnits, trainTimeSet) || cron(task.getTrainCron(), "train");
        }
        if (task.getTaskType().equals(TaskTypeEnum.ALARMTASK.enumToInt()) && TaskConstant.ALARM_TYPE_COMPRESS.equals(task.getAlarmTaskType())) {
            return true;
        }
        if (task.getPeriodicType().equals(TaskConstant.TASK_PERIOD_TYPE_CYCLE)) {
            if (task.getTaskType().equals(TaskTypeEnum.INDICATORPREDICTTASK.enumToInt())) {
                // 校验指标趋势预测的执行周期
                return (periodValid(task.getPredictCron(), predictUnits, new HashSet<>(Arrays.asList("10|MIN", "30|MIN")))
                    || cron(task.getPredictCron(), null, task.getTaskType()))
                    && (periodValid(task.getTrainCron(), trainUnits, trainTimeSet) || cron(task.getTrainCron(), "train"));
            }

            return (periodValid(task.getPredictCron(), predictUnits, predictTimeSet) || cron(task.getPredictCron(),
                null)) && (periodValid(task.getTrainCron(), trainUnits, trainTimeSet) || cron(task.getTrainCron(),
                "train"));
        } else {
            return periodValid(task.getTrainCron(), trainUnits, trainTimeSet) || cron(task.getTrainCron(), "train");
        }
    }

    private boolean periodValid(String period, String[] units, HashSet<String> set) {
        String[] times = period.split("\\|");
        if (NumberUtils.isParsable(times[0]) && Arrays.stream(units).anyMatch(unit -> unit.equals(times[1]))) {
            return set.contains(period);
        } else {
            return false;
        }
    }

    private boolean cron(String cron, String type) {
        return cron(cron, type, 0);
    }

    private boolean cron(String cron, String type, Integer taskType) {
        boolean isCron = CronExpression.isValidExpression(cron);
        if (isCron) {
            if (parameterCheckClient.checkCronInterval(cron, type, taskType)) {
                return false;
            }
        }
        return isCron;
    }

    /**
     * 校验算法
     *
     * @param task task
     * @return boolean
     * @throws ServiceException ServiceException
     */
    private boolean modelCheck(AnalysisTask task) throws ServiceException {
        // 首先判断是否有这个算法
        List<AlgorithmModel> models = libraryClient.getModels(task.getTaskType(), null);
        Optional<AlgorithmModel> model = models.stream()
            .filter(naieModel ->
                task.getAlgorithmModelName().equals(naieModel.getAlgorithmName() + "_" + naieModel.getVersion())
                    && naieModel.getId().equals(Integer.valueOf(task.getAlgorithmModelId())))
            .findAny();
        if (!model.isPresent()) {
            LOGGER.error("No algorithm model found.");
            return false;
        }
        alarmCompressionVerificationVersion(model.get(), task);
        // 还要判断算法参数
        return checkModelParameter(model.get(), task);
    }

    private void alarmCompressionVerificationVersion(AlgorithmModel selectModel, AnalysisTask task)
        throws ServiceException {
        if (TaskConstant.ALARM_TYPE_COMPRESS.equals(task.getAlarmTaskType())) {
            // 如果是告警压缩算法 那么只支持R024C10以后版本的算法包
            if (!PermissionsDomainUtils.compareVersions("V800R024C10", selectModel.getVersion())) {
                throw new ServiceException(
                    ResourceUtil.getMessage(ExceptionConstant.ALARM_COMPRESS_ALGORITHM_VERSION_ERROR, ContextUtils.getContext().getLocale()));
            }
        }
    }

    /**
     * 检查算法参数
     *
     * @param selectModel 模型包
     * @param task 任务
     * @return 检查结果
     * @throws ServiceException exception
     */
    private boolean checkModelParameter(AlgorithmModel selectModel, AnalysisTask task) throws ServiceException {
        // 判断算法参数
        if (StringUtils.isEmpty(task.getAlgorithmParam())) {
            return false;
        }
        boolean unValidModelParameter = isUnValidModelParameter(task);

        List<ModelParameter> modelParameters;
        try {
            modelParameters = JSONArray.parseArray(task.getAlgorithmParam(), ModelParameter.class);
        } catch (JSONException e) {
            LOGGER.error("parse algorithm param error, algorithmParam = {}", task.getAlgorithmParam());
            return false;
        }
        // 在判断是否在参数规定的范围内
        boolean isConceptDriftOn = CONCEPT_DRIFT_TASK_TYPE.contains(task.getTaskType()) && task.getConceptDriftSwitch();
        String feature = isConceptDriftOn ? CONCEPT_DRIFT_FEATURE : "";
        feature = TaskConstant.ALARM_TYPE_COMPRESS.equals(task.getAlarmTaskType()) ? task.getAlarmTaskType() : feature;
        List<ModelParameter> modelDefaultParameters = libraryClient.getModelDeaultParameters(
            selectModel.getAlgorithmName(), selectModel.getVersion(), feature);
        Map<String, ModelParameter> modelDefaultParameterMap = new HashMap<>();
        modelDefaultParameters.forEach(
            modelParameter -> modelDefaultParameterMap.put(modelParameter.getParameterName(), modelParameter));
        if (validateAlgorithmParam(unValidModelParameter, modelParameters, modelDefaultParameters, modelDefaultParameterMap)) {
            return false;
        }

        if (!validateParamCustom(modelParameters, task, unValidModelParameter) || (isConceptDriftOn && !validateConceptDriftParam(modelParameters))) {
            return false;
        }
        task.setAlgorithmParam(JSONObject.toJSONString(modelParameters));
        return true;
    }

    private boolean validateAlgorithmParam(boolean unValidModelParameter, List<ModelParameter> modelParameters,
        List<ModelParameter> modelDefaultParameters, Map<String, ModelParameter> modelDefaultParameterMap) {
        Double manualUpperValue = null;
        Double manualLowerValue = null;
        Double suppressLower = null;
        Double suppressUpper = null;
        String manualSwitch = null;
        if (!isMsMod() && modelParameters.size() != modelDefaultParameters.size()) {
            LOGGER.error("algorithm model parameter count error");
            return true;
        }
        for (ModelParameter modelParameter : modelParameters) {
            ModelParameter modelDefaltParameter = modelDefaultParameterMap.get(modelParameter.getParameterName());
            if (modelDefaltParameter == null || !(modelDefaltParameter.getValueRange() instanceof JSONObject)) {
                return true;
            }
            if (!validParameterType(modelParameter, modelDefaltParameter, unValidModelParameter)) {
                LOGGER.error("algorithm model parameter type valid error");
                return true;
            }
            if (modelParameter.getParameterName().equals("manual_switch")) {
                manualSwitch = modelParameter.getParameterDefaultValue().toString();
            }

            if (modelParameter.getParameterName().equals("manual_uppervalue")) {
                manualUpperValue = Double.parseDouble(modelParameter.getParameterDefaultValue().toString());
            }
            if (modelParameter.getParameterName().equals("manual_lowervalue")) {
                manualLowerValue = Double.parseDouble(modelParameter.getParameterDefaultValue().toString());
            }
            if (modelParameter.getParameterName().equals("suppress_lower") && StringUtils.isNotEmpty(
                modelParameter.getParameterDefaultValue().toString())) {
                suppressLower = Double.parseDouble(modelParameter.getParameterDefaultValue().toString());
            }
            if (modelParameter.getParameterName().equals("suppress_upper") && StringUtils.isNotEmpty(
                modelParameter.getParameterDefaultValue().toString())) {
                suppressUpper = Double.parseDouble(modelParameter.getParameterDefaultValue().toString());
            }
        }

        return validateThreshold(manualUpperValue, manualLowerValue, suppressLower, suppressUpper, manualSwitch);
    }

    public static boolean isMsMod() {
        return Boolean.TRUE.equals(CONTAINER) && Boolean.TRUE.equals(SystemUtils.isMsMod());
    }

    private static boolean validateThreshold(Double manualUpperValue, Double manualLowerValue, Double suppressLower,
        Double suppressUpper, String manualSwitch) {
        if (manualUpperValue != null && manualLowerValue != null && manualLowerValue > manualUpperValue) {
            return true;
        }

        if (Objects.nonNull(suppressLower) && Objects.nonNull(suppressUpper) && suppressLower > suppressUpper) {
            return true;
        }

        if (Objects.equals(manualSwitch, "upper") && Objects.nonNull(manualUpperValue)) {
            return (Objects.nonNull(suppressUpper) && (suppressUpper > manualUpperValue)) || (
                Objects.nonNull(suppressLower) && suppressLower > manualUpperValue);
        } else if (Objects.equals(manualSwitch, "lower") && Objects.nonNull(manualLowerValue)) {
            return (Objects.nonNull(suppressLower) && (manualLowerValue > suppressLower)) || (
                Objects.nonNull(suppressUpper) && manualLowerValue > suppressUpper);
        } else if (Objects.equals(manualSwitch, "yes") && Objects.nonNull(manualLowerValue) && Objects.nonNull(
            manualUpperValue)) {
            if (Objects.nonNull(suppressUpper) && (suppressUpper > manualUpperValue || suppressUpper < manualLowerValue)) {
                return true;
            }
            return Objects.nonNull(suppressLower) && (suppressLower > manualUpperValue
                || suppressLower < manualLowerValue);
        }
        return false;
    }

    private boolean isUnValidModelParameter(AnalysisTask task) throws ServiceException {
        // 获取原有任务的算法参数，判断原有任务的算法参数与本次算法参数是否相等，相等则代表算法参数未变动，可跳过算法参数校验，新增任务无taskID,可跳过校验
        boolean unValidModelParameter = false;
        if (!Objects.isNull(task.getTaskId())) {
            unValidModelParameter = task.getAlgorithmParam().equals(taskManagerClient.getAnalysisTaskById(task.getTaskId()).getAlgorithmParam());
        }
        return unValidModelParameter;
    }

    /**
     * 校验概念漂移参数
     *
     * @param modelParameters 算法参数
     * @return 校验结果
     */
    private boolean validateConceptDriftParam(List<ModelParameter> modelParameters) {
        int detectWindow = 0;
        int judgePoint = 0;

        for (ModelParameter modelParameter : modelParameters) {
            if (DRIFT_SCORE_DETECT_WINDOW.equals(modelParameter.getParameterName())) {
                detectWindow = Integer.parseInt(String.valueOf(modelParameter.getParameterDefaultValue()));
                continue;
            }
            if (DRIFT_JUDGE_POINT.equals(modelParameter.getParameterName())) {
                judgePoint = Integer.parseInt(String.valueOf(modelParameter.getParameterDefaultValue()));
            }
        }

        if (detectWindow <= 0 || judgePoint <= 0) {
            LOGGER.error("judgePoint or detectWindow is empty, judgePoint = {}, detectWindow = {}", judgePoint, detectWindow);
            return false;
        }

        if (detectWindow < judgePoint) {
            // 如果漂移检测窗口点数小于漂移判别最小点数，则校验失败
            LOGGER.error("judge point larger than detect window, judgePoint = {}, detectWindow = {}", judgePoint, detectWindow);
            return false;
        }
        return true;
    }

    private boolean validParameterType(ModelParameter modelParameter, ModelParameter modelDefaltParameter, Boolean unValidModelParameter) {
        setModelParameterValue(modelParameter, modelDefaltParameter);
        JSONObject valueRange = (JSONObject) modelParameter.getValueRange();
        String type = valueRange.getString("type");
        if (!Boolean.parseBoolean(valueRange.getString("required")) && !"string".equals(modelParameter.getType())
            && StringUtils.isEmpty(modelParameter.getParameterDefaultValue().toString())) {
            return true;
        }

        if (type.equals("array")) {
            JSONArray value = valueRange.getJSONArray("value");
            // 目前只有string类型
            Set<String> valueSet = new HashSet<>(value.toJavaList(String.class));
            // 为空不判断，日志异常检测添加
            if (valueSet.isEmpty()) {
                return true;
            }
            if (!valueSet.contains(modelParameter.getParameterDefaultValue().toString())) {
                return false;
            }
        } else if (type.equals("range")) {
            if (!unValidModelParameter && !validateRange(modelParameter)) {
                return false;
            }
        }
        return true;
    }

    private void setModelParameterValue(ModelParameter modelParameter, ModelParameter modelDefaltParameter) {
        modelParameter.setDescription(modelDefaltParameter.getDescription());
        modelParameter.setValueRange(modelDefaltParameter.getValueRange());
        modelParameter.setType(modelDefaltParameter.getType());
        modelParameter.setDisplayName(modelDefaltParameter.getDisplayName());
    }

    private boolean validateParamCustom(List<ModelParameter> modelParameters, AnalysisTask task, Boolean unValidModelParameter)
        throws ServiceException {
        // 测试提的单，极其定制
        Integer windowSize = null;
        Integer detectNum = null;
        String upperLimitFlag = null;
        String lowerLimitFlag = null;
        for (ModelParameter modelParameter : modelParameters) {
            String paramName = modelParameter.getParameterName();
            if (paramName.equals(WINDOW_SIZE)) {
                windowSize = Integer.parseInt(modelParameter.getParameterDefaultValue().toString());
            }
            if (paramName.equals(DETECT_NUM)) {
                detectNum = Integer.parseInt(modelParameter.getParameterDefaultValue().toString());
            }
            if (validSpecialParameter(task, unValidModelParameter, modelParameter, paramName)) {
                return false;
            }
            if (paramName.equals(TOGGLING_ALARM_TRUSTLIST)) {
                if (!validateAlarmTrustList((String) modelParameter.getParameterDefaultValue())) {
                    return false;
                }
            }
            if (paramName.equals(UPPER_LIMIT_FLAG)) {
                upperLimitFlag = modelParameter.getParameterDefaultValue().toString();
            }
            if (paramName.equals(LOWER_LIMIT_FLAG)) {
                lowerLimitFlag = modelParameter.getParameterDefaultValue().toString();
            }
        }
        if (detectNum != null && windowSize != null && detectNum.compareTo(windowSize) > 0) {
            return false;
        }
        if (Objects.equals(upperLimitFlag, THRESHOLD_NOT_DISPLAY) && Objects.equals(lowerLimitFlag, THRESHOLD_NOT_DISPLAY)) {
            LOGGER.error("[TaskValidChecker] upperLimitFlag and lowerLimitFlag cannot be set no at the same time.");
            return false;
        }
        Long indicatorPredictNum = task.getIndicatorPredictNum();
        return indicatorPredictNum == null || windowSize == null || windowSize <= indicatorPredictNum.intValue();
    }

    private boolean validSpecialParameter(AnalysisTask task, Boolean unValidModelParameter, ModelParameter modelParameter,
        String paramName) {
        if (paramName.equals(TRAIN_POINT_NEED)) {
            ModelParameter copyParameter = JSONObject.parseObject(JSONObject.toJSONString(modelParameter), ModelParameter.class);
            copyParameter.setParameterDefaultValue(task.getIndicatorTrainNum().toString());
            if (!unValidModelParameter && !validateRange(copyParameter)) {
                LOGGER.error("[TaskValidChecker] algorithm model param error, param = {}", JSON.toJSONString(copyParameter));
                return true;
            }
        }
        if (paramName.equals(DETECT_POINT_NEED)) {
            ModelParameter copyParameter = JSONObject.parseObject(JSONObject.toJSONString(modelParameter), ModelParameter.class);
            copyParameter.setParameterDefaultValue(task.getIndicatorPredictNum().toString());
            if (!unValidModelParameter && !validateRange(copyParameter)) {
                LOGGER.error("[TaskValidChecker] algorithm model param error, param = {}", JSON.toJSONString(copyParameter));
                return true;
            }
        }
        if (paramName.equals(DETECT_OUTPUT_POINT)) {
            ModelParameter copyParameter = JSONObject.parseObject(JSONObject.toJSONString(modelParameter), ModelParameter.class);
            copyParameter.setParameterDefaultValue(task.getDetectOutNum().toString());
            if (!unValidModelParameter && !validateRange(copyParameter)) {
                LOGGER.error("[TaskValidChecker] algorithm model param error, param = {}", JSON.toJSONString(copyParameter));
                return true;
            }
        }
        return false;
    }

    private boolean validateAlarmTrustList(String values) throws ServiceException {
        if (StringUtils.isNotEmpty(values)) {
            String[] list = values.split(",");
            if (list.length > 100 || values.length() > 1024) {
                return false;
            }
            if (!ALARM_ID_REGEX.matcher(values).matches()) {
                LOGGER.error("validateAlarmTrustList regex failed");
                throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.ALARM_COMPRESS_TRUST_ALARM_ID_ERROR, ContextUtils.getContext().getLocale()));
            }
        }
        return true;
    }

    private boolean validateRange(ModelParameter modelParameter) {
        JSONObject valueRange = (JSONObject) modelParameter.getValueRange();
        JSONObject value = valueRange.getJSONObject("value");
        if (!(modelParameter.getParameterDefaultValue() instanceof String)) {
            return false;
        }
        try {
            if (modelParameter.getType().equals("int")) {
                Integer max = value.getInteger("max");
                Integer min = value.getInteger("min");
                String stringValue = modelParameter.getParameterDefaultValue().toString();
                int intValue = Integer.parseInt(modelParameter.getParameterDefaultValue().toString());
                return INT_VALUE_PATTERN.matcher(stringValue).matches() && intValue <= max && intValue >= min;
            }
            if (modelParameter.getType().equals("float")) {
                double max = value.getDouble("max");
                double min = value.getDouble("min");
                String stringValue = modelParameter.getParameterDefaultValue().toString();
                double doubleValue = Double.parseDouble(modelParameter.getParameterDefaultValue().toString());
                return FLOAT_VALUE_PATTERN.matcher(stringValue).matches() && doubleValue <= max && doubleValue >= min;
            }
        } catch (NumberFormatException e) {
            // 直接通过工具调用接口时， 算法参数设置的值可能不符合要求，如int类型参数填入浮点数，转数字时会出现异常
            LOGGER.error("number format error, parameterName = {}, defaultValue = {}.", modelParameter.getParameterName(), modelParameter.getParameterDefaultValue());
            return false;
        }
        return false;
    }

    private boolean isPerformanceTaskValid(AnalysisTask task, Set<String> moListByField) throws ServiceException {
        return isSolutionValid(task.getSolutionId(), task.getSolutionName(), moListByField) && checkIndicatorList(
            task.getIndicatorList(), task.getIndicatorSelectType(), task.getIndicatorPredictScenario(), task.getUpdateIndicatorAuto());
    }

    private boolean isSolutionIdValid(String solutionId, String solutionName, Set<String> moListByField)
        throws ServiceException {
        if (StringUtils.isNotEmpty(solutionId) && StringUtils.isEmpty(solutionName)) {
            List<LibraryCommonResponse> solutions = libraryClient.getSolutions(moListByField);
            List<Object> values = solutions.stream().map(LibraryCommonResponse::getValue).collect(Collectors.toList());
            return values.contains(solutionId);
        }
        return true;
    }

    private boolean isSolutionValid(String solutionId, String solutionName, Set<String> moListByField)
        throws ServiceException {
        // 校验解决方案
        if (StringUtils.isNotEmpty(solutionId) || StringUtils.isNotEmpty(solutionName)) {
            List<LibraryCommonResponse> solutions = libraryClient.getSolutions(moListByField);
            if (solutions.stream()
                .noneMatch(libraryCommonResponse ->
                    (StringUtils.isNotEmpty(solutionName) && libraryCommonResponse.getText().equals(solutionName)) && (
                        StringUtils.isNotEmpty(solutionId) && libraryCommonResponse.getValue().equals(solutionId)))) {
                LOGGER.error("[isSolutionValid] valid solution failed, solutionId={}, solutionName={}", solutionId,
                    solutionName);
                return false;
            }
        }
        return true;
    }

    public boolean checkIndicatorList(List<TaskIndicator> indicatorList, Integer indicatorSelectType) {
        return checkIndicatorList(indicatorList, indicatorSelectType, 0, null);
    }

    public boolean checkIndicatorList(List<TaskIndicator> indicatorList, Integer indicatorSelectType, Integer indicatorPredictScenario, Boolean updateIndicatorAuto) {
        long startTime = System.currentTimeMillis();
        if (CollectionUtils.isEmpty(indicatorList)) {
            return false;
        }
        // 联合指标不允许选择非主机类型的指标，且必须给出集群名称
        if (indicatorPredictScenario != null && indicatorPredictScenario == 1 && indicatorList.stream()
            .anyMatch(indicate -> !MoConstants.HOST_MO_TYPE.contains(indicate.getMoType()) || StringUtils.isEmpty(indicate.getClusterName()))) {
            return false;
        }
        Map<String, List<TaskIndicator>> moTypeMap = new HashMap<>();
        indicatorList.forEach(indicate -> moTypeMap.computeIfAbsent(indicate.getMoType() + "@" + indicate.getMeasUnitKey(), key -> new ArrayList<>()).add(indicate));
        Map<String, Map<String, ManageObject>> moTypeMoMap = new HashMap<>();
        for (Map.Entry<String, List<TaskIndicator>> entry : moTypeMap.entrySet()) {
            String[] key = entry.getKey().split("@");
            if (!moTypeMoMap.containsKey(key[0])) {
                List<ManageObject> moList = EamUtil.getMoByTypeSoftDeleted(key[0]);
                if (CollectionUtils.isNotEmpty(moList)) {
                    Map<String, ManageObject> moMap = moList.stream()
                        .collect(Collectors.toMap(ManageObject::getDn, Function.identity()));
                    moTypeMoMap.put(key[0], moMap);
                }
            }
            List<MeasUnit4MV> measUnits = measTypeTreeBuilder.getMeasUnitTree(key[0], "*", "", "", "all");
            if (CollectionUtils.isEmpty(measUnits)) {
                // moType有问题查不出来
                LOGGER.error("[checkIndicatorList] moType error, motype={}", key[0]);
                return false;
            }
            MeasUnit4MV matchedMeasUnit = measUnits.stream().filter(measUnit -> measUnit.getMeasUnitKey().equals(key[1])).findFirst().orElse(new MeasUnit4MV());
            if (CollectionUtils.isEmpty(matchedMeasUnit.getChildren())) {
                // measUnitKey有问题找不到
                LOGGER.error("[checkIndicatorList] measUnitKey error, measUnitKey={}", key[1]);
                return false;
            }

            MeasObjectInfo objectInfo = moTypeTreeBuilder.getObjectInfoByMoType(key[0], key[1], "", "", null, null);
            List<MeasObjectVO> measObjects = objectInfo.getMos();
            List<TaskIndicator> values = entry.getValue();
            if (!checkIndicators(values, matchedMeasUnit.getName(), indicatorSelectType, matchedMeasUnit.getChildren(),
                measObjects, indicatorPredictScenario, updateIndicatorAuto, moTypeMoMap.get(key[0]))) {
                return false;
            }
        }
        LOGGER.info("[checkIndicatorList] costTime={}", System.currentTimeMillis() - startTime);
        return true;
    }

    private boolean checkIndicators(List<TaskIndicator> values, String measUnitName, Integer indicatorSelectType,
        List<MeasType4MV> measTypes, List<MeasObjectVO> measObjects, Integer scenario, Boolean updateIndicatorAuto, Map<String, ManageObject> moMap) {
        for (TaskIndicator indicator : values) {
            // 不校验软删除的指标
            if (moMap != null && moMap.get(indicator.getDn()) != null) {
                if (moMap.get(indicator.getDn()).isDeleted()) {
                    LOGGER.info("[checkIndicatorList] soft delete indicator skip, indicatorId={}", indicator.getIndicatorId());
                    continue;
                }
            }
            if (!measUnitName.equals(indicator.getMeasUnitName())) {
                // measUnitName 错误
                LOGGER.error("[checkIndicatorList] measUnitName error, measUnitName={}", measUnitName);
                return false;
            }

            if (StringUtils.isNotEmpty(indicator.getCheckedNetId()) && indicatorSelectType != null && (scenario == null
                || scenario != AGGREGATE_TASK)) {
                String[] checkIds = indicator.getCheckedNetId().split("\\(_\\)");
                if (indicatorSelectType == 0) {
                    // 按网元实例
                    if (moInstanceValid(indicator, checkIds)) {
                        return false;
                    }
                } else {
                    if (updateIndicatorAuto != null && !updateIndicatorAuto) {
                        ManagedObject object = EamUtil.getMoByDn(indicator.getDn());
                        if (Objects.isNull(object)) {
                            // 校验dn和dnName
                            LOGGER.error("[checkIndicatorList motype] dn error, dn={},dnName={}", indicator.getDn(), indicator.getDnName());
                            return false;
                        }
                        indicator.setDnName(object.getName());
                    }
                    if (validCheckIds(indicator, checkIds)) {
                        return false;
                    }
                }
            }

            if (!checkMeasTypes(measTypes, indicator)) {
                return false;
            }

            if (!checkMeasObjects(measObjects, indicator)) {
                return false;
            }
        }
        return true;
    }

    private boolean validCheckIds(TaskIndicator indicator, String[] checkIds) {
        if (StringUtils.isNotEmpty(indicator.getDeploymentMoType())) {
            if (!indicator.getDeploymentMoType().equals(checkIds[checkIds.length - 1])) {
                // 校验CheckedNetId
                LOGGER.error("[checkIndicatorList] CheckedNetId error, checkedNetId={}", indicator.getCheckedNetId());
                return true;
            }
        } else {
            if (!indicator.getMoType().equals(checkIds[checkIds.length - 1])) {
                // 校验CheckedNetId
                LOGGER.error("[checkIndicatorList] CheckedNetId error, checkedNetId={}", indicator.getCheckedNetId());
                return true;
            }
        }
        return false;
    }

    private boolean moInstanceValid(TaskIndicator indicator, String[] checkIds) {
        ManagedObject object = EamUtil.getMoByDn(indicator.getDn());
        if (Objects.isNull(object)) {
            // 校验dn和dnName
            LOGGER.error("[checkIndicatorList] dn dnName error, dn={},dnName={}", indicator.getDn(), indicator.getDnName());
            return true;
        }
        indicator.setDnName(object.getName());

        if (!indicator.getDn().equals(checkIds[checkIds.length - 1]) && !indicator.getMoType().equals(
            checkIds[checkIds.length - 1])) {
            // 校验CheckedNetId
            LOGGER.error("[checkIndicatorList] CheckedNetId error, checkedNetId={}",
                indicator.getCheckedNetId());
            return true;
        }
        return false;
    }

    private boolean checkMeasTypes(List<MeasType4MV> measTypes, TaskIndicator indicator) {
        boolean flag = false;
        for (MeasType4MV measType4MV : measTypes) {
            if (measType4MV.getIndexId().equals(indicator.getIndexId())) {
                flag = true;
                if (!measType4MV.getMeasTypeKey().equals(indicator.getMeasTypeKey())) {
                    LOGGER.error("[checkIndicatorList] measTypeKey error, measTypeKey={}", indicator.getMeasTypeKey());
                    return false;
                }
                if (!measType4MV.getName().equals(indicator.getIndexName())) {
                    LOGGER.error("[checkIndicatorList] IndexName error, IndexName={}", indicator.getIndexName());
                    return false;
                }
                if (!String.valueOf(measType4MV.getHasMeasObj()).equals(indicator.getHasMeasObj())) {
                    LOGGER.error("[checkIndicatorList] HasMeasObj error, HasMeasObj={}", indicator.getHasMeasObj());
                    return false;
                }
                if (!measType4MV.getResourceTypeKey().equals(indicator.getResourceTypeKey())) {
                    LOGGER.error("[checkIndicatorList] ResourceTypeKey error, ResourceTypeKey={}",
                        indicator.getResourceTypeKey());
                    return false;
                }
                if (StringUtils.isNotEmpty(measType4MV.getUnit()) && !measType4MV.getUnit()
                    .equals(indicator.getUnit())) {
                    LOGGER.error("[checkIndicatorList] Unit error, Unit={}", indicator.getUnit());
                    return false;
                }
                break;
            }
        }
        if (!flag) {
            LOGGER.error("[checkIndicatorList] measType4MV not fount error");
            return false;
        }
        return true;
    }

    private boolean checkMeasObjects(List<MeasObjectVO> measObjects, TaskIndicator indicator) {
        boolean flag = false;
        boolean measObjectsFlag = true;
        boolean originalValueFlag = true;
        if (StringUtils.isNotEmpty(indicator.getOriginalValue()) && (indicator.getOriginalValue().contains("@wildcard@") || indicator.getOriginalValue().contains("@regex@")
            || indicator.getOriginalValue().equals(indicator.getDisplayValue()))) {
            // 允许cbs导入系统上不存在的指标，去掉校验
            return true;
        }
        if (CollectionUtils.isEmpty(measObjects) || (measObjects.size() == 1 && StringUtils.isEmpty(
            measObjects.get(0).getOriginalValue()))) {
            measObjectsFlag = false;
        }
        if (StringUtils.isEmpty(indicator.getOriginalValue())) {
            originalValueFlag = false;
        }

        if (originalValueFlag && measObjectsFlag) {
            for (MeasObjectVO measObject : measObjects) {
                if (!measObject.getOriginalValue().equals(indicator.getOriginalValue())) {
                    continue;
                }
                if (!measObject.getDisplayValue().equals(indicator.getDisplayValue())) {
                    LOGGER.error("[checkIndicatorList] DisplayValue error, DisplayValue={}, measObject DisplayValue={}",
                        indicator.getDisplayValue(), measObject.getDisplayValue());
                    continue;
                }
                flag = true;
                break;
            }
        } else if (!originalValueFlag && !measObjectsFlag) {
            return true;
        } else {
            return false;
        }
        if (!flag) {
            LOGGER.error("[checkIndicatorList] MeasObjectVO not fount error, indicator={}",
                JSONObject.toJSONString(indicator));
            return false;
        }
        return true;
    }

    private boolean isCustomTaskValid(AnalysisTask task, HttpServletRequest httpServletRequest, Boolean isTicketChecked)
        throws ServiceException {
        if (task.getPrometheusData() != null && task.getPrometheusData().getStep() != null) {
            task.getPrometheusData().setDataSourceId(Integer.valueOf(task.getDatasourceId()));
            if (!checkPrometheus(task.getPrometheusData())) {
                throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.PQL_NOT_VALID, ContextUtils.getContext().getLocale()));
            }
            return true;
        } else {
            return (isTicketChecked || isTicketValid(httpServletRequest, task.getTicket(),
                TaskConstant.OPERATION_MODIFY_CUSTOM_TASK)) && checkDataBase(Integer.parseInt(task.getDatasourceId()),
                task.getColumnMapping());
        }
    }

    private boolean checkPrometheus(PrometheusData prometheusData) throws ServiceException {
        List<PqlsEntity> pqlsEntities = JSON.parseArray(prometheusData.getPqls(), PqlsEntity.class);
        for (PqlsEntity pqlsEntity : pqlsEntities) {
            if (!checkPql(pqlsEntity.getPql())) {
                return false;
            }
        }
        PqlQueryParam param = new PqlQueryParam();
        param.setDataSourceId(String.valueOf(prometheusData.getDataSourceId()));
        param.setStep(prometheusData.getStep());
        param.setPqls(pqlsEntities.stream().map(PqlsEntity::getPql).collect(Collectors.toList()));
        PqlValidateResponse pqlValidateResponse = taskManagerClient.validatePql(param);
        return pqlValidateResponse.getCode().equals(TaskConstant.PROMETHUS_CHECK_CODE_SUCCESS);
    }

    public static boolean checkPql(String pql) throws ServiceException {
        return !pql.matches("[0-9]+");
    }

    private boolean checkDataBase(int dataSourceId, ColumnMapping columnMapping) throws ServiceException {
        if (!isSqlValid(columnMapping.getSql())) {
            return false;
        }

        if (!isDatasourceValid(dataSourceId)) {
            return false;
        }

        CustomColumnsParam customColumnsParam = new CustomColumnsParam();
        customColumnsParam.setSql(columnMapping.getSql());
        customColumnsParam.setDataSourceId(dataSourceId);
        Set<String> columns = new HashSet<>(taskManagerClient.customColumns(customColumnsParam));
        List<String> checkColumns = new ArrayList<>();
        if (StringUtils.isNotEmpty(columnMapping.getMeasUnitKey())) {
            checkColumns.addAll(Arrays.asList(columnMapping.getMeasUnitKey().split(",")));
        }
        if (StringUtils.isNotEmpty(columnMapping.getOriginalValueKey())) {
            checkColumns.addAll(Arrays.asList(columnMapping.getOriginalValueKey().split(",")));
        }
        if (StringUtils.isNotEmpty(columnMapping.getDnKey())) {
            checkColumns.add(columnMapping.getDnKey());
        }
        if (StringUtils.isNotEmpty(columnMapping.getIpKey())) {
            checkColumns.add(columnMapping.getIpKey());
        }
        if (StringUtils.isNotEmpty(columnMapping.getAuxiliaryKey())) {
            checkColumns.add(columnMapping.getAuxiliaryKey());
        }
        checkColumns.addAll(Arrays.asList(columnMapping.getIndicatorKey().split(",")));
        checkColumns.add(columnMapping.getTimeKey());
        for (String checkColumn : checkColumns) {
            if (StringUtils.isNotEmpty(checkColumn) && !columns.contains(checkColumn)) {
                return false;
            }
            columns.remove(checkColumn);
        }
        return true;
    }

    public boolean isSqlValid(String sql) {
        for (String black : SQL_BLACK_LIST) {
            if (sql.toUpperCase(Locale.ROOT).contains(black)) {
                return false;
            }
        }
        return (sql.toUpperCase(Locale.ROOT).trim().startsWith("SELECT ") || sql.toUpperCase(Locale.ROOT)
            .trim()
            .startsWith("WITH ")) && !sql.contains(";");
    }

    private boolean isAlarmTaskValid(AnalysisTask analysisTask) throws ServiceException {
        if (analysisTask.getAlarmSelectType() != 1 && analysisTask.getAlarmSelectType() != 2 && analysisTask.getAlarmSelectType() != 3) {
            return false;
        }
        if (validAlarmTaskSelect(analysisTask)) {
            return false;
        }
        if (StringUtils.isNotEmpty(analysisTask.getAlarmTaskType()) && !TaskConstant.ALARM_TYPE_COMPRESS.equals(
            analysisTask.getAlarmTaskType()) && !"Analysis".equals(analysisTask.getAlarmTaskType())) {
            return false;
        }
        if (!TaskConstant.ALARM_TYPE_COMPRESS.equals(analysisTask.getAlarmTaskType())) {
            String[] alarmPredictTimes = analysisTask.getAlarmPredictTime().split("\\|");
            String[] alarmTrainTimes = analysisTask.getAlarmTrainTime().split("\\|");
            if (Integer.parseInt(alarmPredictTimes[0]) > 500 || Integer.parseInt(alarmPredictTimes[0]) < 1) {
                return false;
            }
            if (!timeUnit.contains(alarmPredictTimes[1])) {
                return false;
            }
            if (Integer.parseInt(alarmTrainTimes[0]) > 500 || Integer.parseInt(alarmTrainTimes[0]) < 1) {
                return false;
            }
            if (!timeUnit.contains(alarmTrainTimes[1])) {
                return false;
            }
        }
        if (analysisTask.getSendEvent()) {
            return validAlarmSendEvent(analysisTask);
        }
        return true;
    }

    private boolean validAlarmTaskSelect(AnalysisTask analysisTask) throws ServiceException {
        AlarmSelectList alarmSelectList = JSONObject.parseObject(analysisTask.getAlarmSelectList(),
            AlarmSelectList.class);
        List<AlarmTypeSelectItem> alarmRightSelectItems = alarmSelectList.getAlarmRightSelectItems();
        if (analysisTask.getAlarmSelectType() != 3 && CollectionUtils.isEmpty(alarmRightSelectItems)) {
            return true;
        }
        if (analysisTask.getAlarmSelectType() == 1 && !isAlarmSourceTypeValid(alarmSelectList)) {
            return true;
        }
        if (analysisTask.getAlarmSelectType() == 2 && !isAlarmTypeValid(alarmSelectList)) {
            return true;
        }
        if (analysisTask.getAlarmSelectType() == 3 && !isAlarmIdValid(analysisTask.getAlarmTypeList())) {
            return true;
        }
        return false;
    }

    private boolean validAlarmSendEvent(AnalysisTask analysisTask) {
        try {
            String template = taskManagerClient.getAlarmTemplate(analysisTask.getAlarmTaskType());
            String eventFields = JSONObject.parseObject(template).getJSONObject("eventTemplate").getString("fields");
            List<Map<String, String>> eventTemplateFields = JSONObject.parseObject(eventFields, TEMPLATE_FIELD);
            if (!validAlarmEvent(analysisTask.getSendEventContent(), eventTemplateFields)) {
                LOGGER.error("eventTemplate valid fail");
                return false;
            }
            if (!validAlarmEventLength(analysisTask.getSendEventContent(), eventTemplateFields)) {
                return false;
            }
            String alarmFields = JSONObject.parseObject(template)
                .getJSONObject("alarmInfoTemplate")
                .getString("fields");
            List<Map<String, String>> alarmInfoTemplateFields = JSONObject.parseObject(alarmFields, TEMPLATE_FIELD);
            if (!validAlarmEvent(analysisTask.getSendEventAlarmContent(), alarmInfoTemplateFields)) {
                LOGGER.error("alarmInfoTemplate valid fail");
                return false;
            }
            if (!validAlarmEventLength(analysisTask.getSendEventAlarmContent(), alarmInfoTemplateFields)) {
                return false;
            }
        } catch (ServiceException e) {
            LOGGER.error("getAlarmTemplate error,e=", e);
            return false;
        }
        return true;
    }

    private boolean validAlarmEventLength(String content, List<Map<String, String>> fields) {
        String str = content;
        for (Map<String, String> map : fields) {
            String key = "{$" + map.get("key") + "}";
            String value = "{" + map.get("name") + "}";
            str = str.replace(key, value);
        }
        if (str.length() > 512) {
            LOGGER.error("alarmEventLength valid fail,{}", str);
            return false;
        }
        return true;
    }

    private boolean validAlarmEvent(String content, List<Map<String, String>> fields) throws ServiceException {
        List<String> words = new ArrayList<>();
        for (Map<String, String> map : fields) {
            words.add(map.get("key"));
        }
        // 判断{是不是成对出现,和空的{}
        Stack<Character> stack = new Stack<>();
        boolean lastWasOpenBracket = false;
        for (char ch : content.toCharArray()) {
            if (ch == '{') {
                stack.push(ch);
                lastWasOpenBracket = true;
            } else if (ch == '}') {
                if (stack.isEmpty() || lastWasOpenBracket) {
                    return false;
                }
                stack.pop();
            } else {
                lastWasOpenBracket = false;
            }
        }
        if (!stack.isEmpty()) {
            return false;
        }
        Matcher matcher = ALARM_CONTENT_PATTERN.matcher(content);
        Set<String> foundWords = new HashSet<>();
        while (matcher.find()) {
            String word = matcher.group(1);
            if (!words.contains(word) || foundWords.contains(word)) {
                return false;
            }
            foundWords.add(word);
        }
        return !foundWords.isEmpty();
    }

    private boolean isAlarmIdValid(String alarmTypeList) throws ServiceException {
        if (StringUtils.isEmpty(alarmTypeList)) {
            throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.ALARM_COMPRESS_ALARM_ID_ERROR, ContextUtils.getContext().getLocale()));
        }
        if (alarmTypeList.length() > 1024) {
            LOGGER.error("isAlarmIdValid false length > 1024");
            throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.ALARM_COMPRESS_ALARM_ID_ERROR, ContextUtils.getContext().getLocale()));
        }
        List<String> alarmList = JSONArray.parseArray(alarmTypeList, String.class);
        if (alarmList.size() > 100) {
            LOGGER.error("isAlarmIdValid false size > 100");
            throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.ALARM_COMPRESS_ALARM_ID_ERROR, ContextUtils.getContext().getLocale()));
        }
        if (CollectionUtils.isEmpty(alarmList)) {
            throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.ALARM_COMPRESS_ALARM_ID_ERROR, ContextUtils.getContext().getLocale()));
        }
        if (!ALARM_ID_REGEX.matcher(alarmTypeList.substring(1, alarmTypeList.length() - 1)).matches()) {
            throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.ALARM_COMPRESS_ALARM_ID_ERROR, ContextUtils.getContext().getLocale()));
        }
        return true;
    }

    private boolean isAlarmTypeValid(AlarmSelectList alarmSelectList) {
        Set<String> usedValue = new HashSet<>();
        for (AlarmTypeSelectItem alarmRightSelectItem : alarmSelectList.getAlarmRightSelectItems()) {
            if (!alarmTypeValue.get(Integer.parseInt(alarmRightSelectItem.getValue()) - 1)
                .equals(alarmRightSelectItem.getText())) {
                return false;
            }
            if (usedValue.contains(alarmRightSelectItem.getValue())) {
                return false;
            } else {
                usedValue.add(alarmRightSelectItem.getValue());
            }
        }
        for (AlarmTypeSelectItem alarmTypeSelectItem : alarmSelectList.getAlarmTypeSelectItems()) {
            if (!alarmTypeValue.get(Integer.parseInt(alarmTypeSelectItem.getValue()) - 1)
                .equals(alarmTypeSelectItem.getText())) {
                return false;
            }
            if (usedValue.contains(alarmTypeSelectItem.getValue())) {
                return false;
            } else {
                usedValue.add(alarmTypeSelectItem.getValue());
            }
        }
        return true;
    }

    private boolean isAlarmSourceTypeValid(AlarmSelectList alarmSelectList) {
        Set<String> usedValue = new HashSet<>();
        List<AlarmSourceType> typeList = new ArrayList<>();
        String language = ContextUtils.getContext().getLocale().toString();
        if (StringUtils.isEmpty(language)) {
            language = AlarmSelectService.LOCALE_EN;
        }
        EamUtil.getMoTypeByPage(typeList, AlarmSelectService.PAGESIZE, AlarmSelectService.CURRPAGE, language);
        // 该告警源类型查不到
        AlarmSourceType oss = new AlarmSourceType();
        oss.setType(AlarmSelectService.OSS_CLOUDSOP_TYPE);
        oss.setDisplayType("OSS");
        typeList.add(oss);
        HashMap<String, String> alarmSourceTypeMap = new HashMap<>();
        typeList.forEach(alarmSourceType -> {
            alarmSourceTypeMap.put(alarmSourceType.getType(), alarmSourceType.getDisplayType());
        });
        for (AlarmTypeSelectItem alarmRightSelectItem : alarmSelectList.getAlarmRightSelectItems()) {
            if (!alarmRightSelectItem.getType().equals(alarmRightSelectItem.getValue())) {
                return false;
            }
            if (usedValue.contains(alarmRightSelectItem.getValue())) {
                return false;
            } else {
                usedValue.add(alarmRightSelectItem.getValue());
            }
        }
        for (AlarmTypeSelectItem alarmTypeSelectItem : alarmSelectList.getAlarmTypeSelectItems()) {
            if (!alarmTypeSelectItem.getType().equals(alarmTypeSelectItem.getValue())) {
                return false;
            }
            if (usedValue.contains(alarmTypeSelectItem.getValue())) {
                return false;
            } else {
                usedValue.add(alarmTypeSelectItem.getValue());
            }
        }
        return true;
    }

    /**
     * 校验datasource是否可用
     *
     * @param datasourceId id
     * @return 是否可用
     * @throws ServiceException exception
     */
    public boolean isDatasourceValid(int datasourceId) throws ServiceException {
        DataSourceQueryParam queryParam = new DataSourceQueryParam();
        queryParam.setId(datasourceId);
        queryParam.setCreateUser(dataSourceManager.getUser());
        RestfulParametes restfulParametes = new RestfulParametes();
        restfulParametes.setRawData(JSON.toJSONString(queryParam));
        RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
            DataSourceManager.DATA_SOURCE_GET, restfulParametes, null);

        if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
            LOGGER.error("[DataSourceManager] get error, response is error");
            return false;
        }

        DataSourceQueryResult dataSourceQueryResult = JSONObject.parseObject(response.getResponseContent(),
            DataSourceQueryResult.class);

        return dataSourceQueryResult.getTotal() == 1;
    }

    public boolean checkCorrelationTask(AnalysisTask analysisTask, boolean isTicketChecked, Set<String> moListByField)
        throws ServiceException {
        TriggerTaskMessage triggerTaskMessage = analysisTask.getTriggerTaskMessage();
        // triggerType为一标名是指标关联分析，以后可能还有告警关联分析之类的，预留字段
        if (StringUtils.isEmpty(triggerTaskMessage.getRelationTree())) {
            return false;
        }
        TemplateTreeModel templateTreeModel = JSONObject.parseObject(triggerTaskMessage.getRelationTree(),
            TemplateTreeModel.class);
        List<TemplateTreeNode> nodes = templateTreeModel.getNodes();
        if (CollectionUtils.isEmpty(nodes) || nodes.size() < 2) {
            return false;
        }
        Set<String> ids = new HashSet<>();
        if (!checkNodeData(nodes, ids, isTicketChecked, moListByField, getAnalysisTaskUserId(analysisTask.getTaskId()))) {
            return false;
        }
        String mainId = "";
        Map<String, TemplateTreeNode> nodeMap = new HashMap<>();
        Set<String> nodeName = new HashSet<>();
        for (TemplateTreeNode node : nodes) {
            nodeMap.put(node.getId(), node);
            if (node.getType().equals("start")) {
                mainId = node.getId();
            }
            if (!nodeName.add(node.getTitle())) {
                return false;
            }
        }
        List<TemplateTreePath> paths = templateTreeModel.getPaths();
        if (!checkTree(ids, paths, mainId, nodeMap)) {
            return false;
        }
        if (!checkUserTemplate(analysisTask.getTriggerTaskMessage())) {
            return false;
        }
        if (!checkExecuteNodeStatus(templateTreeModel.getNodes(), templateTreeModel.getPaths())) {
            return false;
        }
        checkCycle(templateTreeModel.getPaths());
        return true;
    }

    public void checkCycle(List<TemplateTreePath> paths) throws ServiceException {
        Graph graph = convert(paths);
        if (hasCycle(graph)) {
            throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.CORRELATION_TASK_VALID_ERROR1, ContextUtils.getContext().getLocale()));
        }
    }

    public static Graph convert(List<TemplateTreePath> paths) {
        Graph graph = new Graph();
        for (TemplateTreePath path : paths) {
            graph.addEdge(path.getSource(), path.getTarget());
        }
        return graph;
    }

    public static boolean hasCycle(Graph graph) {
        // 统计每个节点的入度。
        // 将所有入度为 0 的节点加入队列。
        // 依次移除入度为 0 的节点，并减少其邻居的入度。
        // 若最终处理的节点数小于总节点数，则存在环
        Map<String, Integer> inDegree = new HashMap<>();
        Map<String, List<String>> adjList = graph.getAdjList();

        // 初始化入度
        for (String node : adjList.keySet()) {
            inDegree.put(node, 0);
        }

        // 填充入度
        for (Map.Entry<String, List<String>> entry : adjList.entrySet()) {
            List<String> targets = entry.getValue();
            for (String target : targets) {
                inDegree.put(target, inDegree.get(target) + 1);
            }
        }

        Queue<String> queue = new LinkedList<>();
        for (Map.Entry<String, Integer> entry : inDegree.entrySet()) {
            if (entry.getValue() == 0) {
                queue.add(entry.getKey());
            }
        }

        int processedCount = 0;

        while (!queue.isEmpty()) {
            String node = queue.poll();
            processedCount++;

            for (String neighbor : adjList.get(node)) {
                inDegree.put(neighbor, inDegree.get(neighbor) - 1);
                if (inDegree.get(neighbor) == 0) {
                    queue.add(neighbor);
                }
            }
        }

        return processedCount != adjList.size();
    }

    private boolean validUserGroup(TriggerTaskMessage triggerTaskMessage) throws ServiceException {
        String userGroupInfo = triggerTaskMessage.getUserGroupInfo();
        if (StringUtils.isEmpty(userGroupInfo)) {
            return true;
        }
        if (StringUtils.isNotEmpty(userGroupInfo) && JSONArray.parseArray(userGroupInfo).size() > 10) {
            LOGGER.error("userGroupInfo size error");
            throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.ALARM_USER_GROUP_ERROR, ContextUtils.getContext().getLocale()));
        }
        try {
            String users = triggerManagerClient.getUserGroups("500","1");
            JSONObject result = JSONObject.parseObject(users);
            Integer retCode = result.getInteger("retCode");
            if (retCode == 0) {
                JSONArray queryData = result.getJSONObject("data").getJSONArray("queryDate");
                List<JSONObject> list = queryData.stream().map(JSONObject.class::cast).collect(Collectors.toList());
                Map<Integer, JSONObject> map = list.stream()
                    .collect(Collectors.toMap(jsonObject -> jsonObject.getInteger("groupId"), Function.identity()));
                JSONArray userArray = JSONArray.parseArray(userGroupInfo);
                for (Object userObj : userArray) {
                    Integer groupId = ((JSONObject) userObj).getInteger("groupId");
                    if (!map.containsKey(groupId)) {
                        LOGGER.error("[validUserGroup] groupId error={}", groupId);
                        throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.ALARM_USER_GROUP_ERROR, ContextUtils.getContext().getLocale()));
                    } else {
                        JSONObject user = map.get(groupId);
                        ((JSONObject) userObj).put("name", user.getString("name"));
                        ((JSONObject) userObj).put("description", user.getString("description"));
                    }
                }
                triggerTaskMessage.setUserGroupInfo(userArray.toJSONString());
            } else {
                LOGGER.error("[validUserGroup] getUserGroups retCode={}", retCode);
                throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.ALARM_USER_GROUP_ERROR, ContextUtils.getContext().getLocale()));
            }
        } catch (ServiceException e) {
            LOGGER.error("[validUserGroup] getUserGroups error=", e);
            throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.ALARM_USER_GROUP_ERROR, ContextUtils.getContext().getLocale()));
        }
        return true;
    }

    private boolean checkExecuteNodeStatus(List<TemplateTreeNode> nodes, List<TemplateTreePath> paths) {
        // 判断预案节点要是叶子节点且不能连主节点
        List<String> executeNodeName = new ArrayList<>();
        String mainNodeId = "";
        for (TemplateTreeNode node : nodes) {
            if ("Contingency_PLAN".equals(node.getType())) {
                executeNodeName.add(node.getId());
            }
            if ("start".equals(node.getType())) {
                mainNodeId = node.getId();
            }
        }
        for (TemplateTreePath path : paths) {
            if (executeNodeName.contains(path.getSource())) {
                LOGGER.error("[checkExecuteNodeStatus] plan node must be a leaf node. nodeName:{}", path.getSource());
                return false;

            }
            if (executeNodeName.contains(path.getTarget()) && executeNodeName.contains(path.getSource())) {
                LOGGER.error("[checkExecuteNodeStatus] plan node must be a leaf node. nodeName:{}", path.getSource());
                return false;
            }
            if (mainNodeId.equals(path.getSource()) && executeNodeName.contains(path.getTarget())) {
                LOGGER.error("[checkExecuteNodeStatus] plan node cannot connect to the main node. nodeName:{}", path.getTarget());
                return false;
            }
        }
        return true;
    }

    private String getAnalysisTaskUserId(Integer taskId) throws ServiceException {
        if (taskId == null) {
            return null;
        } else {
            AnalysisTask analysisTaskById = taskManagerClient.getAnalysisTaskById(taskId);
            return analysisTaskById.getUserId();
        }
    }

    public boolean checkCorrelationTaskSync(AnalysisTask analysisTask, HttpServletRequest request) throws ServiceException {
        // ticket和sql同步校验
        if (StringUtils.isEmpty(analysisTask.getTriggerTaskMessage().getRelationTree())) {
            return false;
        }
        TemplateTreeModel templateTreeModel = JSONObject.parseObject(
            analysisTask.getTriggerTaskMessage().getRelationTree(), TemplateTreeModel.class);
        List<TemplateTreeNode> nodes = templateTreeModel.getNodes();
        boolean isTicketChecked = false;
        for (TemplateTreeNode node : nodes) {
            TriggerExecutionNode nodeParam = node.getNodeParam();
            if (nodeParam != null && nodeParam.getColumnMapping() != null
                && nodeParam.getColumnMapping().getSql() != null) {
                isTicketChecked = true;
                if (!isSqlValid(nodeParam.getColumnMapping().getSql())) {
                    return false;
                }
            }
            if (validExecuteTicket(nodeParam)) {
                isTicketChecked = true;
            }
            if (!validNodeFiguresStyle(node.getFigures())) {
                LOGGER.error("valid node figures style fail");
                throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.TRIGGER_NODE_FIGURES_STYLE_INVALID,
                    ContextUtils.getContext().getLocale()) + node.getTitle());
            }
            if (node.getType().equals("Associated_LOG") && !logNodeValid(node, analysisTask.getImportTaskStatus())) {
                LOGGER.error("valid log node fail");
                return false;
            }
        }

        // 更新模板
        analysisTask.getTriggerTaskMessage().setRelationTree(JSON.toJSONString(templateTreeModel, JSONWriter.Feature.LargeObject));

        if (CollectionUtils.isNotEmpty(templateTreeModel.getPaths())) {
            for (TemplateTreePath path : templateTreeModel.getPaths()) {
                if (!validPathFiguresStyle(path.getFigures())) {
                    LOGGER.error("valid path figures style fail");
                    return false;
                }
            }
        }
        if (!validUserGroup(analysisTask.getTriggerTaskMessage())) {
            return false;
        }
        if ((INTELLIGENT_RECOMMENDATION.equals(analysisTask.getIntelligentRecommendation())) == StringUtils.isEmpty(analysisTask.getSolutionTypeForRecommendation())) {
            return false;
        }
        if (isTicketChecked) {
            return isTicketValid(request, analysisTask.getTicket(), TaskConstant.OPERATION_MODIFY_CUSTOM_TASK);
        }
        return true;
    }

    private boolean validExecuteTicket(TriggerExecutionNode nodeParam) {
        // 预案节点要校验ticket
        if (nodeParam != null && "DV_FLOW".equals(nodeParam.getIndicatorDataType())) {
            PreExecutionNode preExecutionNode = JSONObject.parseObject(nodeParam.getExtendJson(),
                PreExecutionNode.class);
            return preExecutionNode.getIfAutoExecute();
        }
        return false;
    }

    private boolean validPathFiguresStyle(String figures) {
        JSONObject jsonObject = JSONObject.parseObject(figures);
        JSONArray array = jsonObject.getJSONArray("edge");
        for (int i = 0; i < array.size(); i++) {
            JSONObject object = array.getJSONObject(i);
            Integer lineWidth = object.getJSONObject("context").getInteger("lineWidth");
            if (lineWidth != 2) {
                return false;
            }
        }
        JSONArray conditionArray = jsonObject.getJSONArray("condition");
        if (conditionArray != null) {
            for (int j = 0; j < conditionArray.size(); j++) {
                JSONObject conditionObject = conditionArray.getJSONObject(j);
                Integer lineWidth = conditionObject.getJSONObject("context").getInteger("lineWidth");
                if (lineWidth != null && lineWidth != 1) {
                    return false;
                }
                if (!styleCheck(conditionObject.getString("style"))) {
                    return false;
                }
            }
        }
        return true;
    }

    private boolean validNodeFiguresStyle(String figures) {
        JSONArray array = JSON.parseArray(figures);
        for (int i = 0; i < array.size(); i++) {
            JSONObject object = array.getJSONObject(i);
            Integer lineWidth = object.getJSONObject("context").getInteger("lineWidth");

            if ("round".equals(object.getString("type")) && lineWidth != 2) {
                return false;
            }
            if ("roundrect".equals(object.getString("type")) && lineWidth != 1) {
                return false;
            }
            if (!styleCheck(object.getString("style"))) {
                return false;
            }
        }
        return true;
    }

    private boolean styleCheck(String style) {
        String px = "px";
        String left = "left";
        String top = "top";
        String width = "width";
        String height = "height";
        String marks = ":";
        int location = 100000;
        int size = 200;
        String[] cssStyle = style.split(";");
        for (String css : cssStyle) {
            if (css.contains(left)) {
                String value = css.substring(css.indexOf(left + marks) + 5, css.indexOf(px));
                if (Float.parseFloat(value) > location) {
                    return false;
                }
                continue;
            }
            if (css.contains(top)) {
                String value = css.substring(css.indexOf(top + marks) + 4, css.indexOf(px));
                if (Float.parseFloat(value) > location) {
                    return false;
                }
                continue;
            }
            if (css.contains(width)) {
                String value = css.substring(css.indexOf(width + marks) + 6, css.indexOf(px));
                if (Float.parseFloat(value) > size) {
                    return false;
                }
                continue;
            }
            if (css.contains(height)) {
                String value = css.substring(css.indexOf(height + marks) + 7, css.indexOf(px));
                if (Float.parseFloat(value) > size) {
                    return false;
                }
            }
        }
        return true;
    }

    public boolean indicatorTaskTicketValid(AnalysisTask task, HttpServletRequest request) {
        if (Integer.parseInt(task.getDatasourceId()) > 99
            && !Objects.equals(task.getTaskType(), TaskTypeEnum.LOGTASK.enumToInt())
            && task.getPrometheusData() == null) {
            return isTicketValid(request, task.getTicket(), TaskConstant.OPERATION_MODIFY_CUSTOM_TASK);
        }
        return true;
    }

    /**
     * 校验任务类型和名称
     *
     * @param task task
     * @return 是否正确
     * @throws ServiceException exception
     */
    public boolean validTaskNameAndType(AnalysisTask task) throws ServiceException {
        if (task.getTaskId() != null) {
            AnalysisTask analysisTaskById = taskManagerClient.getAnalysisTaskById(task.getTaskId());
            return analysisTaskById.getTaskName().equals(task.getTaskName()) && analysisTaskById.getTaskType()
                .equals(task.getTaskType());
        } else {
            return createTaskNameCheck(task.getTaskName());
        }
    }

    public boolean createTaskNameCheck(String taskName) throws ServiceException {
        BooleanCheckResult repeat = null;
        try {
            repeat = parameterCheckClient.repeat("analysistask", "taskName", taskName);
        } catch (ServiceException e) {
            LOGGER.error("[createTaskNameCheck] createTask error", e);
            return false;
        }
        if (!repeat.getCheckResult()) {
            AnalysisTaskListQueryParam param = new AnalysisTaskListQueryParam();
            param.setTaskName(taskName);
            AnalysisTaskList analysisTaskList = taskManagerClient.getAnalysisTaskList(param);
            if (analysisTaskList.getRows()
                .stream()
                .anyMatch(task -> !task.getTaskType().equals(TaskTypeEnum.INCIDENTTASK.enumToInt()))) {
                LOGGER.error("[createTaskNameCheck] taskname is repeat");
                throw new ServiceException(ResourceUtil.getMessage(LogConstant.EXCEPTION_TASKNAME_REPEAT, ContextUtils.getContext().getLocale()));
            }
        }
        return true;
    }

    private boolean checkTree(Set<String> ids, List<TemplateTreePath> paths, String mainId, Map<String, TemplateTreeNode> nodeMap) {
        if (CollectionUtils.isEmpty(paths)) {
            return false;
        }
        for (TemplateTreePath path : paths) {
            if (!ids.contains(path.getSource()) || !ids.contains(path.getTarget()) || path.getSource()
                .equals(path.getTarget())) {
                return false;
            }
            if (!path.getId().matches("flow-diagram_P[0-9]+")) {
                return false;
            }

            if (path.getSource().equals(mainId) && nodeMap.containsKey(path.getTarget()) &&
                String.valueOf(TaskTypeEnum.PREEXECUTETASK.enumToInt()).equals(nodeMap.get(path.getTarget()).getNodeParam().getDatasourceId())) {
                return false;
            }

            if (!path.getSource().equals(mainId)) {
                String symbol = path.getCondition().substring(0, 2);
                String numberString = path.getCondition().substring(2);
                if (!FLOAT_FORMAT_PATTERN.matcher(numberString).matches()) {
                    LOGGER.error("[checkTree] path is error, numberString={}", numberString);
                    return false;
                }
                float number = Float.parseFloat(numberString);
                if (!numberString.contains(".") || number < 0 || number > 100) {
                    return false;
                }
                switch (symbol) {
                    case "==":
                        break;
                    case ">=":
                        break;
                    case "<=":
                        break;
                    case "!=":
                        break;
                    default:
                        return false;
                }
            }
        }
        return true;
    }

    private boolean checkUserTemplate(TriggerTaskMessage triggerTaskMessage) throws ServiceException {
        if (triggerTaskMessage.getUseTemplate() == 0) {
            return !CollectionUtils.isNotEmpty(triggerTaskMessage.getIndicatorList()) && !StringUtils.isNotEmpty(
                triggerTaskMessage.getIndicatorIds()) && !StringUtils.isNotEmpty(triggerTaskMessage.getDelay());
        }
        if (StringUtils.isNotEmpty(triggerTaskMessage.getDelay())) {
            String[] delay = triggerTaskMessage.getDelay().split("\\|");
            if (delay.length != 2 || Integer.parseInt(delay[0]) < 1 || Integer.parseInt(delay[0]) > 500) {
                return false;
            }
            if (!delay[1].equals("MIN") && !delay[1].equals("HOUR")) {
                return false;
            }
        }
        if (Objects.equals(1, triggerTaskMessage.getCorrelateType())) {
            // 模板通过指标关联
            if (CollectionUtils.isEmpty(triggerTaskMessage.getIndicatorList())
                || triggerTaskMessage.getIndicatorList().size() > 2500) {
                throw new ServiceException(ResourceUtil.getMessage(LogConstant.ASSOCIATED_INDICATOR_COUNT_ERROR, ContextUtils.getContext().getLocale()));
            }
            List<String> taskIds = triggerTaskMessage.getIndicatorList().stream().map(indicator -> String.valueOf(indicator.getTaskId())).collect(Collectors.toList());
            QueryIndicatorInfoListById queryIndicatorInfoListById = new QueryIndicatorInfoListById();
            queryIndicatorInfoListById.setTaskId(taskIds);
            queryIndicatorInfoListById.setIndicatorName("");
            Paging paging = new Paging();
            paging.setPageSize(2500);
            paging.setPageNumber(1);
            queryIndicatorInfoListById.setPaging(paging);
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(JSONObject.toJSONString(queryIndicatorInfoListById));
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
                "/rest/dvanalysisengineservice/synanalysisresult/v1/indicatorinfolistbyid", restfulParametes, null);
            IndicatorInfoResult indicatorInfoResult = JSON.parseObject(response.getResponseContent(),
                IndicatorInfoResult.class);
            Map<String, IndicatorInfo> map = new HashMap<>();
            for (IndicatorInfo indicatorInfo : indicatorInfoResult.getIndicatorInfoList()) {
                map.put(indicatorInfo.getTaskId() + indicatorInfo.getIndicatorId(), indicatorInfo);
            }
            for (TriggerTemplateIndicator triggerTemplateIndicator : triggerTaskMessage.getIndicatorList()) {
                IndicatorInfo indicatorInfo = map.get(
                    triggerTemplateIndicator.getTaskId() + triggerTemplateIndicator.getIndicatorId());
                if (indicatorInfo == null || !checkIndicator(triggerTemplateIndicator, indicatorInfo)) {
                    throw new ServiceException(ResourceUtil.getMessage(LogConstant.ASSOCIATED_INDICATOR_CHANGED, ContextUtils.getContext().getLocale()));
                }
            }
        }
        return true;
    }

    private boolean checkIndicator(TriggerTemplateIndicator triggerTemplateIndicator, IndicatorInfo indicatorInfo) {
        if (StringUtils.isNotEmpty(triggerTemplateIndicator.getDisplayValue()) && !StringUtils.equals(
            triggerTemplateIndicator.getDisplayValue(), indicatorInfo.getDisplayValue())) {
            return false;
        }
        if (!StringUtils.equals(triggerTemplateIndicator.getIndexName(), indicatorInfo.getIndexName())) {
            return false;
        }
        if (!StringUtils.equals(triggerTemplateIndicator.getMeasUnitName(), indicatorInfo.getMeasUnitName())) {
            return false;
        }
        if (!StringUtils.equals(triggerTemplateIndicator.getPql(), indicatorInfo.getPql())) {
            return false;
        }
        if (!StringUtils.equals(triggerTemplateIndicator.getTaskName(), indicatorInfo.getTaskName())) {
            return false;
        }
        return true;
    }

    private boolean checkNodeData(List<TemplateTreeNode> nodes, Set<String> ids, boolean isTicketChecked,
        Set<String> moListByField, String taskUserId) throws ServiceException {
        boolean hasMain = false;
        for (TemplateTreeNode node : nodes) {
            if (!FLOW_DIAGRAM_PATTERN.matcher(node.getId()).matches() || !ids.add(node.getId())) {
                return false;
            }
            TriggerExecutionNode nodeParam = node.getNodeParam();
            if (node.getType().equals("start")) {
                if (!node.getTitle().equals("Start") || hasMain || nodeParam != null) {
                    return false;
                }
                hasMain = true;
                continue;
            }
            if (!checkNormalNode(node, isTicketChecked, moListByField, taskUserId)) {
                return false;
            }
        }
        return hasMain;
    }

    private static boolean hasIllegalCharacter(String string, Pattern pattern) {
        return pattern.matcher(string).replaceAll("").trim().length() != string.trim().length();
    }

    private boolean checkNormalNode(TemplateTreeNode node, boolean isTicketChecked, Set<String> moListByField, String taskUserId)
        throws ServiceException {
        TriggerExecutionNode nodeParam = node.getNodeParam();
        if (!node.getType().equals("Associated_KPI") && !node.getType().equals("Associated_ALARM") && !node.getType()
            .equals("Associated_LOG") && !node.getType().equals("Contingency_PLAN")) {
            return false;
        }
        if (nodeParam == null || nodeParam.getNodeName() == null || !nodeNormalCheck(node, taskUserId)) {
            return false;
        }
        return validIndicatorDataType(nodeParam, isTicketChecked, moListByField);
    }

    private boolean validIndicatorDataType(TriggerExecutionNode nodeParam, boolean isTicketChecked, Set<String> moListByField) throws ServiceException {
        switch (nodeParam.getIndicatorDataType()) {
            case "DV_PM": {
                if (!dvPmValid(nodeParam, moListByField)) {
                    return false;
                }
                break;
            }
            case "PROMETHEUS_PM": {
                if (!checkPrometheusNode(nodeParam)) {
                    return false;
                }
                break;
            }
            case "SQL_PM": {
                if (!isTicketChecked || !checkDataBase(Integer.parseInt(nodeParam.getDatasourceId()),
                    nodeParam.getColumnMapping())) {
                    return false;
                }
                break;
            }
            case "CUSTOM_PM": {
                checkIndicators(nodeParam.getIndicatorList(), nodeParam.getDataSourceType());
                break;
            }
            case "DV_ALARM": {
                if (!alarmNodeValid(nodeParam, moListByField)) {
                    return false;
                }
                break;
            }
            case "DV_SHOW":
            case "DV_FLOW": {
                if (!executeNodeValid(nodeParam)) {
                    return false;
                }
                break;
            }
            case "DV_LOG": {
                // 已经同步校验过日志节点了，无需再校验
                break;
            }
            default:
                return false;
        }
        return true;
    }

    private boolean executeNodeValid(TriggerExecutionNode nodeParam) {
        if (!nodeParam.getDatasourceId().equals("8") || !nodeParam.getDataSourceType().equals("execute")) {
            return false;
        }
        String extendJson = nodeParam.getExtendJson();
        if (StringUtils.isEmpty(extendJson)) {
            return false;
        }
        PreExecutionNode executeFlowAction = JSONObject.parseObject(extendJson, PreExecutionNode.class);
        if ("DV_FLOW".equals(nodeParam.getIndicatorDataType())) {
            if (CollectionUtils.isEmpty(executeFlowAction.getFlowActions())) {
                LOGGER.error("[TaskValidChecker] flowActions is empty");
                return false;
            }
            List<String> flowGroups;
            try {
                flowGroups = DomainUtil.getFlowGroupWithPerm(ContextUtils.getContext().getHeaderMap());
            } catch (ServiceException e) {
                LOGGER.error("[TaskValidChecker] getFlowGroupWithPerm error, e=", e);
                return false;
            }
            FlowAction action = executeFlowAction.getFlowActions().get(0);
            if (!flowGroupCheck(flowGroups, action)) {
                return false;
            }
            List<String> flowIds;
            try {
                flowIds = DomainUtil.getFlowWithPermBySolutionAndFlowName(ContextUtils.getContext().getHeaderMap(), action.getFlowGroup(), action.getFlowName());
            } catch (ServiceException e) {
                LOGGER.error("[TaskValidChecker] getFlowWithPermBySolutionAndFlowName error, e=", e);
                return false;
            }
            if (CollectionUtils.isEmpty(flowIds) || flowIds.size() != 1) {
                LOGGER.error("[TaskValidChecker] get flowId is error, flowIds={}", flowIds);
                return false;
            }
            if (!ipDetailsCheck(action)) {
                return false;
            }
        }
        if (validExecuteInfo(executeFlowAction.getHelpInfo())) {
            LOGGER.error("[TaskValidChecker] helpInfo is illegal");
            return false;
        }
        if (validExecuteInfo(executeFlowAction.getCauseInfo())) {
            LOGGER.error("[TaskValidChecker] causeInfo is illegal");
            return false;
        }
        return true;
    }

    private boolean ipDetailsCheck(FlowAction action) {
        if (CollectionUtils.isEmpty(action.getIpsDetails())) {
            return true;
        }
        List<IpsDetail> ipsDetails;
        try {
            ipsDetails = DomainUtil.queryFlowDetails(ContextUtils.getContext().getHeaderMap(), action.getFlowId());
        } catch (Exception e) {
            LOGGER.error("[TaskValidChecker] queryFlowDetails error, e=", e);
            return false;
        }
        if (CollectionUtils.isEmpty(ipsDetails)) {
            LOGGER.error("[TaskValidChecker] queryFlowDetails empty");
            return false;
        }
        Map<String, String> ipMap = ipsDetails.stream()
            .collect(Collectors.toMap(IpsDetail::getNodeId, IpsDetail::getAtomId));
        for (IpsDetail detail : action.getIpsDetails()) {
            if (!ipMap.containsKey(detail.getNodeId()) || !detail.getAtomId().equals(ipMap.get(detail.getNodeId()))) {
                LOGGER.error("[TaskValidChecker] valid node and atomid error,NodeId={},AtomId={}", detail.getNodeId(),
                    detail.getAtomId());
                return false;
            }
            if (StringUtils.isNotEmpty(detail.getIps())) {
                LOGGER.error("[TaskValidChecker] valid ips is not empty");
                return false;
            }
        }
        return true;
    }

    private boolean flowGroupCheck(List<String> flowGroups, FlowAction action) {
        if (!flowGroups.contains(action.getFlowGroup())) {
            LOGGER.error("[flowGroupCheck] executeNodeHandler flowGroups is error, flowGroup={}", action.getFlowGroup());
            return false;
        }
        // 查询流程组下所有流程信息，找到填写的流程，填充flowId
        if (StringUtils.isEmpty(action.getFlowName())) {
            LOGGER.error("[flowGroupCheck] executeNodeHandler flowName is error, flowName={}", action.getFlowName());
            return false;
        }
        return true;
    }

    private boolean validExecuteInfo(String string) {
        if (StringUtils.isEmpty(string)) {
            return true;
        }
        if (string.length() > 1024) {
            LOGGER.error("[validExecuteInfo] string is over size,{}", string);
            return true;
        }
        return EXECUTE_BLACK_CHARACTER.matcher(string).replaceAll("").trim().length() != string.trim().length();
    }

    private boolean dvPmValid(TriggerExecutionNode nodeParam, Set<String> moListByField) throws ServiceException {
        if (!nodeParam.getDatasourceId().equals("1") || !nodeParam.getDataSourceType().equals("performance")
            || CollectionUtils.isEmpty(nodeParam.getIndicatorList()) || !isSolutionValid(nodeParam.getSolutionId(),
            nodeParam.getSolutionName(), moListByField)) {
            return false;
        }
        if (nodeParam.getIndicatorSelectType() != 0 && nodeParam.getIndicatorSelectType() != 1) {
            LOGGER.error("[dvPmValid] indicatorSelectType error,getIndicatorSelectType={}", nodeParam.getIndicatorSelectType());
            return false;
        }
        if (!checkIndicatorList(
            JSON.parseArray(JSONObject.toJSONString(nodeParam.getIndicatorList()), TaskIndicator.class),
            nodeParam.getIndicatorSelectType())) {
            throw new ServiceException(ResourceUtil.getMessage(LogConstant.EXCEPTION_INDICATOR_ERROR, ContextUtils.getContext().getLocale()) + ResourceUtil.getMessage(LogConstant.NODE_NAME, ContextUtils.getContext().getLocale()) + nodeParam.getNodeName());
        }
        return true;
    }

    private boolean alarmNodeValid(TriggerExecutionNode nodeParam, Set<String> moListByField) throws ServiceException {
        if (!nodeParam.getDatasourceId().equals("3") || !nodeParam.getDataSourceType().equals("alarm")) {
            return false;
        }
        if (!alarmNodeValid(nodeParam.getSolutionId(), nodeParam.getSolutionName(), nodeParam.getExtendJson(), moListByField)) {
            return false;
        }
        return true;
    }

    private boolean logNodeValid(TemplateTreeNode node, Boolean isImportTask) throws ServiceException {
        if (!node.getNodeParam().getDataSourceType().equals("log")) {
            return false;
        }

        if (!"4".equals(node.getNodeParam().getDatasourceId())) {
            // 校验es数据源日志节点并设置样式
            String extendJson = node.getNodeParam().getExtendJson();
            if (StringUtils.isEmpty(extendJson)) {
                return false;
            }
            LogFilterData logFilterData = JSONObject.parseObject(extendJson, LogFilterData.class);
            logFilterData.getLogFieldMapping().setAnalysisField(logFilterData.getAnalysisField());
            handleNodeFigure(node, true);
            // 获取日志es数据源节点的校验结果
            return validLogParam(node.getNodeParam().getDatasourceId(), logFilterData.getLogIndexName(),
                logFilterData.getLogIndexTemplate(), logFilterData.getLogFieldMapping(),
                JSON.toJSONString(logFilterData.getFilterFields()));
        }

        if (isImportTask == null || !isImportTask) {
            return logNodeExtendJsonValid(node.getNodeParam().getExtendJson(), node.getNodeParam().getDatasourceId(), node.getNodeParam().getNodeName());
        }

        // 校验默认数据源日志节点
        return logNodeImportExtendJsonValid(node);
    }

    private boolean nodeNormalCheck(TemplateTreeNode node, String taskUserId) throws ServiceException {
        TriggerExecutionNode nodeParam = node.getNodeParam();
        if (hasIllegalCharacter(nodeParam.getNodeName(), NODE_NAME_BLACK_CHARACTER) || hasIllegalCharacter(
            node.getTitle(), NODE_NAME_BLACK_CHARACTER)) {
            return false;
        }
        if (nodeParam.getNodeName().length() > 320 || node.getTitle().length() > 320) {
            return false;
        }
        if (String.valueOf(TaskTypeEnum.PREEXECUTETASK.enumToInt()).equals(nodeParam.getDatasourceId())) {
            return true;
        }

        if (!validTimeRange(nodeParam)) {
            return false;
        }
        return algorithmCheck(nodeParam.getAlgorithmTemplateId(), nodeParam.getAlgorithmTemplateName(), node.getType(),
            taskUserId);
    }

    private boolean validTimeRange(TriggerExecutionNode nodeParam) {
        String timeRange = nodeParam.getKpiTimeRange();
        String timeType = nodeParam.getKpiTimeType();
        if (StringUtils.isEmpty(timeRange) || StringUtils.isEmpty(timeType)) {
            return false;
        }

        String[] split = timeRange.split("\\|");
        if (split.length != 2) {
            return false;
        }
        int number = Integer.parseInt(split[0]);
        boolean timeResultMinute = split[1].equals("MIN") && number >= 10 && number <= 10080;
        boolean timeResultHour = split[1].equals("HOUR") && number >= 1 && number <= 168;
        boolean timeResultDay = split[1].equals("DAY") && number >= 1 && number <= 7;
        if (!timeResultMinute && !timeResultHour && !timeResultDay) {
            return false;
        }
        return TaskConstant.TRIGGER_TASK_NODE_TIME_TYPE_RECENTLY.equals(timeType)
            || TaskConstant.TRIGGER_TASK_NODE_TIME_TYPE_BEFOREAFTER.equals(timeType);
    }

    private boolean algorithmCheck(Integer id, String name, String type, String taskUserId) throws ServiceException {
        ResponseEntity result = triggerManagerClient.algorithmTemplateGet(id);
        if (result.getData() == null) {
            LOGGER.error("[TaskValidChecker] algorithmCheck query empty,id={}",id);
            return false;
        }
        TriggerAlgorithmModel queryResult = JSONObject.parseObject(JSONObject.toJSONString(result.getData()),
            TriggerAlgorithmModel.class);
        String newName = queryResult.getAlgorithmName() + "(" + queryResult.getUserName() + ")";
        if (StringUtils.isEmpty(taskUserId) || (ContextUtils.getContext().getAdmin() && isAdminByUserId(taskUserId))) {
            if ((queryResult.getAlgorithmName().equals(name) || newName.equals(name)) && queryResult.getModelName()
                .contains(ALGORITHM_MAP.get(type))) {
                return true;
            }
        } else {
            if (queryResult.getUserId().equals(taskUserId) && ((queryResult.getAlgorithmName().equals(name) || newName.equals(name)))
                && queryResult.getModelName().contains(ALGORITHM_MAP.get(type))) {
                return true;
            }
        }
        LOGGER.error("[TaskValidChecker] algorithmCheck valid error,name={}", name);
        return false;
    }

    private boolean isAdminByUserId(String userId) {
        // 判断该userId是否是管理员
        String url = QUERY_ROLEIDS_BY_USERID.concat(userId);
        RestfulResponse response;
        try {
            response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_GET, url, null, null);
            if (response == null || response.getStatus() != RestConstant.STATUS_OK) {
                LOGGER.error("[isAdminByUserId] response is null or response status is not ok");
                throw new ServiceException("response error");
            }
            JSONObject object = JSONObject.parseObject(response.getResponseContent());
            JSONArray roles = object.getJSONArray("roles");
            for (int i = 0; i < roles.size(); i++) {
                JSONObject jsonObject = roles.getJSONObject(i);
                if ("Administrators".equals(jsonObject.getString("name"))) {
                    return true;
                }
            }
            return false;
        } catch (ServiceException e) {
            LOGGER.error("[isAdminByUserId] getUserNameByUserId has error :{}", e.getMessage());
            return false;
        }
    }

    private boolean checkPrometheusNode(TriggerExecutionNode nodeParam) throws ServiceException {
        PrometheusData prometheusData = nodeParam.getPrometheusData();
        if (!nodeParam.getDataSourceType().equals("prometheus") || prometheusData == null) {
            return false;
        }
        prometheusData.setDataSourceId(Integer.valueOf(nodeParam.getDatasourceId()));
        return checkPrometheus(prometheusData);
    }

    private void checkIndicators(List<TriggerIndicator> indicatorList, String dataSourceType) throws ServiceException {
        IndicatorValidParameter indicatorValidParameter = new IndicatorValidParameter();
        indicatorValidParameter.setIndicatorList(indicatorList);
        indicatorValidParameter.setDataSourceType(dataSourceType);
        RestfulParametes restfulParametes = new RestfulParametes();
        restfulParametes.setRawData(JSONObject.toJSONString(indicatorValidParameter));
        RestfulResponse restfulResponse = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
            CHECK_INDICATOR_URL, restfulParametes, null);
        RestUtil.checkIRCode(restfulResponse);
    }

    public boolean alarmNodeValid(String solutionId, String solutionName, String extendJson, Set<String> moListByField)
        throws ServiceException {
        List<QFilterElement> filters = StringUtils.isNotEmpty(extendJson)
            ? JSONArray.parseArray(extendJson, QFilterElement.class) : Collections.emptyList();
        if (StringUtils.isEmpty(solutionName) && StringUtils.isEmpty(solutionId) && CollectionUtils.isEmpty(filters)) {
            return true;
        }
        if (isAlarmSolutionIllegal(solutionId, solutionName, moListByField, filters)) {
            return false;
        }
        for (QFilterElement filter : filters) {
            if (!isValidFilterField(filter) || !isValidFilterOperator(filter) || !isValidNativeMoDn(filter)) {
                return false;
            }
            if (CollectionUtils.isEmpty(filter.getValues())) {
                continue;
            }
            if (checkEventTypeAndSeverity(filter)) {
                return false;
            }
            if ("alarmId".equals(filter.getField())) {
                if (alarmIdLimitValid(filter)) {
                    continue;
                } else {
                    return false;
                }
            }
            int charLenLimit = "deviceTypeId".equals(filter.getField()) ? ALARM_DEVICE_TYPE_FILTER_MAX_LEN : ALARM_FILTER_MAX_LEN;
            // 加上前台的换行符，长度就能和前台保持一致
            if (filter.getValues().size() > 1000 || filter.getValues().stream().mapToInt(String::length).sum() + (filter.getValues().size() - 1) > charLenLimit) {
                LOGGER.error("[TaskValidChecker] alarmNode value size or total char over max size");
                return false;
            }
        }
        return true;
    }

    private boolean isAlarmSolutionIllegal(String solutionId, String solutionName, Set<String> moListByField,
        List<QFilterElement> filters) throws ServiceException {
        if (StringUtils.isNotEmpty(solutionId) || StringUtils.isNotEmpty(solutionName)) {
            if (StringUtils.isEmpty(solutionId) || StringUtils.isEmpty(solutionName)) {
                return true;
            }
            if (filters.stream().noneMatch(filter -> "deviceTypeId".equals(filter.getField())
                && "in".equals(filter.getOperator()) && CollectionUtils.isNotEmpty(filter.getValues()))) {
                return true;
            }
            return !isSolutionValid(solutionId, solutionName, moListByField);
        }
        return false;
    }

    private static boolean checkEventTypeAndSeverity(QFilterElement filter) {
        if ("eventType".equals(filter.getField())) {
            if (filter.getValues().stream().anyMatch(value -> !ALARM_EVENT_TYPE.contains(value)) || filter.getValues().stream().distinct().count() != filter.getValues().size()) {
                LOGGER.error("[TaskValidChecker] alarmNode validEventType error");
                return true;
            }
        }
        if ("severity".equals(filter.getField())) {
            if (filter.getValues().stream().anyMatch(value -> !ALARM_SEVERITY.contains(value)) || filter.getValues().stream().distinct().count() != filter.getValues().size()) {
                LOGGER.error("[TaskValidChecker] alarmNode valid severity error");
                return true;
            }
        }
        return false;
    }

    private boolean isValidFilterField(QFilterElement filter) {
        if (!ALARM_JSON_FIELDS.contains(filter.getField())) {
            LOGGER.error("[TaskValidChecker] alarmNode field illegal, field={}", filter.getField());
            return false;
        }
        return true;
    }

    private boolean isValidFilterOperator(QFilterElement filter) {
        if (!ALARM_JSON_OPERATOR.contains(filter.getOperator())) {
            LOGGER.error("[TaskValidChecker] alarmNode operator illegal, operator={}", filter.getOperator());
            return false;
        }
        return true;
    }

    private boolean isValidNativeMoDn(QFilterElement filter) {
        if ("nativeMoDn".equals(filter.getField()) && CollectionUtils.isNotEmpty(filter.getSelectedIds()) &&
            (filter.getSelectedIds().size() > 1000 || filter.getSelectedIds().stream().mapToInt(String::length).sum() > 2048)) {
            LOGGER.error("[TaskValidChecker] alarmNode selectedIds size or total char over max size");
            return false;
        }
        return true;
    }

    private boolean alarmIdLimitValid(QFilterElement filter) {
        try {
            ResponseEntity response = configDataServiceDelegate.queryConfigItem(null,
                "ASSOCIATION_ANALYSIS_ALARM_ID_LIMIT");
            if (response.getResultCode() != 0) {
                LOGGER.error("[TaskValidChecker] alarmNode valid queryConfigItem error,e=",
                    response.getResultMessage());
                return false;
            }
            List<ConfigData> configDataList = JSON.parseArray(response.getData().toString(), ConfigData.class);
            int limitCount = Integer.parseInt(configDataList.get(0).getValue());
            if (filter.getValues().size() > limitCount
                || filter.getValues().stream().mapToInt(String::length).sum() + (filter.getValues().size() - 1)
                > limitCount * 10) {
                LOGGER.error("[ImportTaskValidCheker] alarmNode alarmId value size or total char over max size");
                return false;
            } else {
                return true;
            }
        } catch (ServiceException e) {
            LOGGER.error("[TaskValidChecker] alarmNode valid queryConfigItem error,e=", e);
            return false;
        }
    }

    public boolean logNodeExtendJsonValid(String extendJson, String datasourceId, String nodeName) throws ServiceException {
        if (StringUtils.isEmpty(extendJson)) {
            return true;
        }
        LogFilterData logFilterData = JSONObject.parseObject(extendJson, LogFilterData.class);
        if (!"4".equals(datasourceId)) {
            logFilterData.getLogFieldMapping().setAnalysisField(logFilterData.getAnalysisField());
            return validLogParam(datasourceId, logFilterData.getLogIndexName(), logFilterData.getLogIndexTemplate(),
                logFilterData.getLogFieldMapping(), JSON.toJSONString(logFilterData.getFilterFields()));
        }
        List<LogmatrixSolution> solutionTypes = logmatrixAdaptor.getLogmatrixSolutionList();
        LogmatrixSolution logmatrixSolution = solutionTypes.stream()
            .filter(type -> type.getMotype().equals(logFilterData.getSolutionType()))
            .findAny()
            .orElse(null);
        if (logmatrixSolution == null) {
            LOGGER.error("[TaskValidChecker] logNodeValid solutionType is illegal, solutionType={}",
                logFilterData.getSolutionType());
            throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.LOG_DATASOURCE_VALID_ERROR, ContextUtils.getContext().getLocale()) + nodeName);
        }

        List<String> logTypes = logmatrixAdaptor.getLogTypeList(logFilterData.getSolutionType());
        if (!logTypes.contains(logFilterData.getLogType())) {
            LOGGER.error("[TaskValidChecker] logNodeValid logType is illegal, logType={}", logFilterData.getLogType());
            throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.LOG_DATASOURCE_VALID_ERROR, ContextUtils.getContext().getLocale()) + nodeName);
        }

        List<LogFilterRule> filters = logmatrixAdaptor.getLogFilterRuleList(logFilterData.getSolutionType(),
            logFilterData.getLogType());
        if (CollectionUtils.isEmpty(filters)) {
            LOGGER.error("[TaskValidChecker] logNodeValid filters is empty");
            throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.LOG_DATASOURCE_VALID_ERROR, ContextUtils.getContext().getLocale()) + nodeName);
        }
        List<String> filterName = filters.stream().map(LogFilterRule::getFilterName).collect(Collectors.toList());
        for (String key : logFilterData.getQueryFields().keySet()) {
            if (!filterName.contains(key)) {
                LOGGER.error("[TaskValidChecker] logNodeValid filters is illegal, key={}", key);
                throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.LOG_DATASOURCE_VALID_ERROR, ContextUtils.getContext().getLocale()) + nodeName);
            }
            String value = logFilterData.getQueryFields().get(key);
            if (StringUtils.isNotEmpty(value) && value.length() > 256) {
                LOGGER.error("[TaskValidChecker] logNodeValid filters is illegal, value size over 256={}", value);
                throw new ServiceException(ResourceUtil.getMessage(ExceptionConstant.LOG_DATASOURCE_VALID_ERROR, ContextUtils.getContext().getLocale()) + nodeName);
            }
        }
        return true;
    }

    private boolean logNodeImportExtendJsonValid(TemplateTreeNode node) {
        // 是否通过了所有校验
        boolean passAllValid = true;

        String extendJson = node.getNodeParam().getExtendJson();
        if (StringUtils.isEmpty(extendJson)) {
            return true;
        }
        try {
            LogFilterData logFilterData = JSONObject.parseObject(extendJson, LogFilterData.class);
            List<LogmatrixSolution> solutionTypes = logmatrixAdaptor.getLogmatrixSolutionList();
            LogmatrixSolution logmatrixSolution = solutionTypes.stream()
                .filter(type -> type.getMotype().equals(logFilterData.getSolutionType()))
                .findAny()
                .orElse(null);
            if (logmatrixSolution == null) {
                passAllValid = false;
                logFilterData.setLogSolutionName(" ");
            } else {
                logFilterData.setLogSolutionName(solutionTypes.get(0).getSolutionName());
            }

            List<String> logTypes = logmatrixAdaptor.getLogTypeList(logFilterData.getSolutionType());
            if (!logTypes.contains(logFilterData.getLogType())) {
                passAllValid = false;
            }

            List<LogFilterRule> filters = logmatrixAdaptor.getLogFilterRuleList(logFilterData.getSolutionType(),
                logFilterData.getLogType());
            if (CollectionUtils.isEmpty(filters)) {
                passAllValid = false;
            }
            List<String> filterName = filters.stream().map(LogFilterRule::getFilterName).collect(Collectors.toList());
            for (String key : logFilterData.getQueryFields().keySet()) {
                if (!filterName.contains(key)) {
                    passAllValid = false;
                }
                String value = logFilterData.getQueryFields().get(key);
                if (StringUtils.isNotEmpty(value) && value.length() > 256) {
                    LOGGER.error("[TaskValidChecker] logNodeValid filters is illegal, value size over 256={}", value);
                    return false;
                }
            }
            TriggerExecutionNode nodeParam = node.getNodeParam();
            nodeParam.setExtendJson(JSON.toJSONString(logFilterData));
            node.setNodeParam(nodeParam);
        } catch (ServiceException e) {
            LOGGER.error("[TaskValidChecker] logNodeValid error, e = {}", e);
            passAllValid = false;
        }
        handleNodeFigure(node, passAllValid);
        return true;
    }

    private void handleNodeFigure(TemplateTreeNode node, boolean passAllValid) {
        // 如果通过了所有校验，需要把置灰的日志节点恢复成正常节点
        if (passAllValid) {
            node.setFigures(node.getFigures().replace(LOG_COLOR_GRAY, LOG_COLOR_PURPLE));
            node.setGray(false);
            node.getNodeParam().setIsGray(false);
            return;
        }
        node.setFigures(node.getFigures().replace(LOG_COLOR_PURPLE, LOG_COLOR_GRAY));
        node.setGray(true);
        node.getNodeParam().setIsGray(true);
    }

    /**
     * 校验告警分析任务的checkedNetId
     *
     * @param selectGroupList 用户配置的分组
     * @return 是否正确
     * @throws ServiceException Service Exception
     */
    public boolean validAlarmTaskSelectedGroup(List<MoInfoAlarm> selectGroupList) throws ServiceException {
        // 如果selectGroupList为空，说明用户未配置分组，校验通过
        if (selectGroupList == null) {
            return true;
        }
        // 获取有权限的dn列表
        OMToken token = WebContext.getCurrentToken();
        List<String> authDns;
        if (UserRoleUtil.isAdministrator(token.getUserName(), WebContext.getOrgId())) {
            // 管理员用户dnList为null
            authDns = null;
        } else {
            if (SystemUtils.isEnableResGroup()) {
                authDns = PermissionsDomainUtils.getMoListByGroup(token.getResGroupsByCategory(RestConstant.RESOURCE_CATEGORY_ALL));
            } else {
                authDns = ResourceGroupUtil.queryDnListByGroupIds(token.getResGroupsByCategory(RestConstant.RESOURCE_CATEGORY_ALL));
            }
        }
        // 校验dn和checkedNetId
        if (!validCheckedAllNetId(selectGroupList, authDns)) {
            LOGGER.error("[validAlarmTaskSelectedGroup] validCheckedAllNetId error.");
            return false;
        }

        // 校验分组，若网元有重复，或网元之间有父子关系则校验不通过
        HashSet<String> moDns = new HashSet<>();
        for (MoInfoAlarm moInfoAlarm : selectGroupList) {
            moDns.add(moInfoAlarm.getDn());
        }
        if (moDns.size() != selectGroupList.size()) {
            return false;
        }
        if (!validCheckedAllPaternity(moDns)) {
            LOGGER.error("[validAlarmTaskSelectedGroup] validCheckedAllPaternity error.");
            return false;
        }
        return true;
    }

    private boolean validCheckedAllNetId(List<MoInfoAlarm> selectGroupList, List<String> authDns) {
        long startTime = System.currentTimeMillis();
        CountDownLatch countDownLatchNetId = new CountDownLatch(selectGroupList.size());
        AtomicBoolean checkNetIdResult = new AtomicBoolean(true);
        for (MoInfoAlarm moInfoAlarm : selectGroupList) {
            validQueryManageExecutor.execute(() -> {
                try {
                    try {
                        if (!validCheckedNetId(moInfoAlarm, authDns)) {
                            LOGGER.error("[validCheckedAllNetId] error. The MoInfoAlarm is {}", moInfoAlarm.getName());
                            checkNetIdResult.set(false);
                        }
                    } catch (Throwable e) {
                        LOGGER.error("[validCheckedAllNetId] query validCheckedNetId thread error", e);
                        checkNetIdResult.set(false);
                    } finally {
                        countDownLatchNetId.countDown();
                    }
                } catch (Throwable e) {
                    LOGGER.error("validCheckedNetId thread error, e:", e);
                }
            });
        }
        try {
            if (!countDownLatchNetId.await(30, TimeUnit.SECONDS)) {
                // 超过30s没有查询完毕，设定为校验失败
                LOGGER.error("[validCheckedAllPaternity] Time expire.");
                return false;
            }
            if (!checkNetIdResult.get()) {
                LOGGER.error("[validCheckedAllNetId] validCheckedNetId error.");
                return false;
            }
        } catch (InterruptedException e) {
            LOGGER.error("[validCheckedAllNetId] Exception: await interrupted exception", e);
            return false;
        } finally {
            LOGGER.debug("[validCheckedAllNetId] countDownLatchNetId is {}", countDownLatchNetId);
        }
        long endTime = System.currentTimeMillis();
        LOGGER.info("[validCheckedAllNetId] The validCheckedNetId cost is {}", endTime - startTime);
        return true;
    }

    private boolean validCheckedAllPaternity(HashSet<String> moDns) {
        long startTime = System.currentTimeMillis();
        CountDownLatch countDownLatchPaternity = new CountDownLatch(moDns.size());
        AtomicBoolean checkPaternityResult = new AtomicBoolean(true);
        for (String dn : moDns) {
            validQueryManageExecutor.execute(() -> {
                try {
                    try {
                        if (!validPaternity(dn, moDns)) {
                            LOGGER.error("[validCheckedAllPaternity] error. The dn is {}", dn);
                            checkPaternityResult.set(false);
                        }
                    } catch (Throwable e) {
                        LOGGER.error("[validCheckedAllPaternity] query validCheckedNetId thread error", e);
                        checkPaternityResult.set(false);
                    } finally {
                        countDownLatchPaternity.countDown();
                    }
                } catch (Throwable throwable) {
                    LOGGER.error("validCheckedAllPaternity thread error, e:", throwable);
                }
            });
        }
        try {
            if (!countDownLatchPaternity.await(30, TimeUnit.SECONDS)) {
                // 超过30s没有查询完毕，设定为校验失败
                LOGGER.error("[validCheckedAllPaternity] Time expire.");
                return false;
            }
            if (!checkPaternityResult.get()) {
                LOGGER.error("[validCheckedAllPaternity] checkPaternityResult error.");
                return false;
            }
        } catch (InterruptedException e) {
            LOGGER.error("[validCheckedAllPaternity] Exception: await interrupted exception", e);
            return false;
        } finally {
            LOGGER.debug("[validCheckedAllPaternity] countDownLatchPaternity is {}", countDownLatchPaternity);
        }
        long endTime = System.currentTimeMillis();
        LOGGER.info("[validCheckedAllPaternity] The validCheckedAllPaternity cost is {}", endTime - startTime);
        return true;
    }

    private boolean validCheckedNetId(MoInfoAlarm moInfoAlarm, List<String> authDns) {
        if (authDns != null && !authDns.contains(moInfoAlarm.getDn())) {
            LOGGER.error("[validAlarmTaskSelectedGroup] dn authority error, dn={}", moInfoAlarm.getDn());
            return false;
        }
        ManagedObject object = EamUtil.getMoByDn(moInfoAlarm.getDn());

        // 校验dn和dnName的一致性
        if (object == null || !object.getName().equals(moInfoAlarm.getName())) {
            LOGGER.error("[validAlarmTaskSelectedGroup] dn & dnName error, dn={},dnName={}",
                moInfoAlarm.getDn(), moInfoAlarm.getName());
            return false;
        }

        // 校验checkedNetId是否满足父子关系
        if (moInfoAlarm.getCheckedNetId() == null) {
            LOGGER.error("[validAlarmTaskSelectedGroup] have not checkedNetId, dn={}", moInfoAlarm.getDn());
            return false;
        }
        String[] netIds = moInfoAlarm.getCheckedNetId().split("\\(_\\)");
        if (!moInfoAlarm.getDn().equals(netIds[netIds.length - 1])) {
            LOGGER.error("[validAlarmTaskSelectedGroup] checkedNetId error, dn={}", moInfoAlarm.getDn());
            return false;
        }
        if (!netIds[0].equals(ROOT_DN)) {
            LOGGER.error("[validAlarmTaskSelectedGroup] checkedNetId error, dn={}", moInfoAlarm.getDn());
            return false;
        }
        for (int i = netIds.length - 1; i > 0; i--) {
            ManagedObject checkMo = EamUtil.getMoByDn(netIds[i]);
            if (checkMo == null || !checkMo.getParent().getValue().equals(netIds[i - 1])) {
                LOGGER.error("[validAlarmTaskSelectedGroup] checked parent error, dn={}", moInfoAlarm.getDn());
                return false;
            }
        }
        return true;
    }

    private boolean validPaternity(String dn ,HashSet<String> moDns) {
        // 向上查询网元树路径，依次校验是否存在于父集合
        String currentDn = dn;
        ManagedObject mo;
        do {
            mo = EamUtil.getMoByDn(currentDn);
            if (mo == null || mo.getParent().getValue() == null) {
                LOGGER.error("[validAlarmTaskSelectedGroup] mo not exist error, dn={}", dn);
                return false;
            }
            if (moDns.contains(mo.getParent().getValue())) {
                LOGGER.error("[validAlarmTaskSelectedGroup] select parent error, dn={}", dn);
                return false;
            }
            currentDn = mo.getParent().getValue();
        } while (!currentDn.equals(ROOT_DN));

        return true;
    }

    public boolean checkBusinessDeployType(List<BusinessPortrait> businessPortraits) {
        boolean wrongFormat = businessPortraits
            .stream()
            .map(BusinessPortrait::getServiceDeployType)
            .anyMatch(Objects::isNull);
        if (wrongFormat) {
            LOGGER.error("[checkBusinessDeployType] the deploy type is wrong format.");
            return false;
        }
        return true;
    }

    /**
     * 校验主机画像关联的服务画像
     *
     * @param hostPortraits hostPortraits
     * @return 是否正确
     */
    public boolean checkBusinessPortraitList(List<HostPortrait> hostPortraits) {
        List<BusinessPortrait> totalList = new ArrayList<>();
        try {
            if (ADMIN_ID.equals(ContextUtils.getContext().getUserId())) {
                totalList = portraitManager.getBusinessPortraitList();
            } else  {
                List<ProductPortrait> totalPortraitList = portraitManager.getProductPortraitList()
                    .stream()
                    .filter(productPortrait -> productPortrait.getUserId().equals(ContextUtils.getContext().getUserId())
                        && BUSINESS_PORTRAIT_TYPE == productPortrait.getType())
                    .collect(Collectors.toList());
                for (ProductPortrait portrait : totalPortraitList) {
                    ProductPortrait detailPortrait = portraitManager.getProductPortraitDetail(portrait.getId());
                    totalList.addAll(detailPortrait.getBusinessPortraitList());
                }
            }
        } catch (ServiceException e) {
            LOGGER.error("[checkBusinessPortraitList] valid business portrait error");
            return false;
        }
        Set<String> businessNames = totalList.stream().map(BusinessPortrait::getBusinessName)
            .collect(Collectors.toSet());
        for (HostPortrait hostPortrait : hostPortraits) {
            List<String> businessList = hostPortrait.getDeployServiceList();
            List<DeployService> deployServiceList = new ArrayList<>();
            for (String business : businessList) {
                DeployService deployService = JSONObject.parseObject(business, DeployService.class);
                deployServiceList.add(deployService);
            }
            if (CollectionUtils.isEmpty(deployServiceList)) {
                LOGGER.error("[checkBusinessPortraitList] deploy service list is empty.");
                return false;
            }
            Set<DeployService> hostBusiness = new HashSet<>(deployServiceList);
            if (hostBusiness.size() != businessList.size()) {
                return false;
            }
            for (DeployService business : hostBusiness) {
                if (!businessNames.contains(business.getServiceName())) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 校验主机画像的部署服务
     *
     * @param hostPortraits hostPortraits
     * @return 是否正确
     */
    public boolean checkDeployServiceList(List<HostPortrait> hostPortraits) {
        try {
            for (HostPortrait hostPortrait : hostPortraits) {
                List<String> deployServices = hostPortrait.getDeployServiceList();
                for (String deployService : deployServices) {
                    DeployService deployItem = JSONObject.parseObject(deployService, DeployService.class);
                    if (deployItem.getDeployNumber() == null) {
                        LOGGER.error("[checkDeployServiceList] deployNumber null.");
                        return false;
                    }
                    if (deployItem.getDeployNumber() > 10000 || deployItem.getDeployNumber() < 1) {
                        LOGGER.error("[checkDeployServiceList] deployNumber error.");
                        return false;
                    }
                    if (StringUtils.isEmpty(deployItem.getServiceName()) || deployItem.getServiceName().length() > 100) {
                        LOGGER.error("[checkDeployServiceList] service name error.");
                        return false;
                    }
                }
            }
        } catch (JSONException e) {
            LOGGER.error("[checkDeployServiceList] something wrong with parse json.", e);
            return false;
        }
        return true;
    }

    /**
     * 校验主机画像是否重复
     *
     * @param hostPortraitList hostPortraitList
     * @return 是否正确
     */
    public boolean checkHostPortraitList(List<HostPortrait> hostPortraitList) {
        Set<String> hostName = hostPortraitList.stream().map(HostPortrait::getName).collect(Collectors.toSet());
        Set<String> hostType = hostPortraitList.stream().map(HostPortrait::getHostType).collect(Collectors.toSet());
        return hostName.size() == hostPortraitList.size() && hostType.size() == hostPortraitList.size();
    }

    /**
     * 校验冷双机指标预测分组
     *
     * @param task task
     * @return 是否正确
     * @throws ServiceException 服务异常
     */
    public boolean aggregateIndicatorPredictGroupValid(AnalysisTask task) throws ServiceException {
        // 包含冷双机指标数量不能超过配置的值
        if (task.getIndicatorList().size() > Integer.parseInt(configurationUtil.getConfigItemValue(INDICATOR_PREDICT_AGGREGATE_GROUP_INDICATOR_NUMBER))) {
            LOGGER.error("[aggregateIndicatorPredictTaskValid] too many aggregate indicators in one task.");
            return false;
        }

        // 双机指标是否合法
        if (!checkIndicatorList(task.getIndicatorList(), BY_MO, AGGREGATE_TASK, null)) {
            LOGGER.error("[aggregateIndicatorPredictTaskValid] indicator list is invalid.");
            return false;
        }

        List<AggregateMachineGroup> aggregateGroups;
        try {
            aggregateGroups = JSON.parseArray(task.getAggregateMachineGroupList(), AggregateMachineGroup.class);
        } catch (JSONException e) {
            LOGGER.error("[aggregateIndicatorPredictTaskValid] parse AggregateMachineGroup json error.", e);
            return false;
        }

        // 双机分组数量不能超过配置的值
        if (aggregateGroups.size() > Integer.parseInt(configurationUtil.getConfigItemValue(KPI_AGGREGATE_GROUP_NUM))) {
            LOGGER.error("[aggregateIndicatorPredictTaskValid] too many aggregate groups.");
            return false;
        }

        // 双机指标的分组名是否重复，是否包含非法字符，是否已在双机分组列表中定义
        Set<String> groupNames = aggregateGroups.stream().map(AggregateMachineGroup::getClusterName)
            .collect(Collectors.toSet());
        if (groupNames.size() != aggregateGroups.size()) {
            LOGGER.error("[aggregateIndicatorPredictTaskValid] aggregate group name repeat.");
            return false;
        }
        for (String name : groupNames) {
            if (hasIllegalCharacter(name, CLUSTER_NAME_BLACK_CHARACTER)) {
                LOGGER.error("[aggregateIndicatorPredictTaskValid] illegal aggregate group name.");
                return false;
            }
        }
        for (TaskIndicator taskIndicator : task.getIndicatorList()) {
            if (!groupNames.contains(taskIndicator.getClusterName()) || !groupNames.contains(taskIndicator.getCheckedNetId())) {
                LOGGER.error("[aggregateIndicatorPredictTaskValid] aggregate groups don't have indicator group.");
                return false;
            }
        }

        return checkDnAndDnName(task, aggregateGroups);
    }

    private static boolean checkDnAndDnName(AnalysisTask task, List<AggregateMachineGroup> aggregateGroups) {
        // 双机分组中的网元不可重复
        int totalMoSize = 0;
        Set<String> totalMoDns = new HashSet<>();
        for (AggregateMachineGroup machineGroup : aggregateGroups) {
            totalMoSize += machineGroup.getAggregateMoList().size();
            totalMoDns.addAll(machineGroup.getAggregateMoList().stream().map(AggregateMo::getDn).collect(Collectors.toSet()));
        }
        if (totalMoDns.size() != totalMoSize) {
            LOGGER.error("[aggregateIndicatorPredictTaskValid] aggregate mo dns repeat.");
            return false;
        }

        for (AggregateMachineGroup aggregateGroup : aggregateGroups) {
            for (AggregateMo aggregateMo : aggregateGroup.getAggregateMoList()) {
                ManagedObject object = EamUtil.getMoByDn(aggregateMo.getDn());
                if (Objects.isNull(object)) {
                    // 校验dn
                    LOGGER.error("dn dnName error, dn={},dnName={}", aggregateMo.getDn(), aggregateMo.getDnName());
                    return false;
                }
                aggregateMo.setDnName(object.getName());
            }
        }
        task.setAggregateMachineGroupList(JSON.toJSONString(aggregateGroups));

        return true;
    }

    /**
     * 校验冷双机指标预测任务数量
     *
     * @param taskId 任务Id
     * @return 是否正确
     * @throws ServiceException 服务异常
     */
    public boolean aggregateIndicatorPredictTaskValid(Integer taskId) throws ServiceException {
        AnalysisTaskListQueryParam param = new AnalysisTaskListQueryParam();
        List<Integer> taskTypes = new ArrayList<>();
        taskTypes.add(TaskTypeEnum.INDICATORPREDICTTASK.enumToInt());
        param.setTaskType(taskTypes);
        AnalysisTaskList analysisTaskList;
        try {
            analysisTaskList = taskManagerClient.getAnalysisTaskList(param);
        } catch (ServiceException e) {
            LOGGER.error("[aggregateIndicatorPredictTaskValid] get aggregate task list error.", e);
            throw new ServiceException("get aggregate task list error.");
        }
        List<AnalysisTask> analysisTasks = analysisTaskList.getRows().stream()
            .filter(analysisTask -> analysisTask.getIndicatorPredictScenario() != null
                && analysisTask.getIndicatorPredictScenario() == 2).collect(
                Collectors.toList());

        // 新建任务时，冷双机指标预测任务数量不能超过配置的值
        if (taskId == null && analysisTasks.size() >= getAggregateTaskMaxCount()) {
            LOGGER.error("[aggregateIndicatorPredictTaskValid] too many aggregate tasks.");
            return false;
        }
        return true;
    }

    public int getAggregateTaskMaxCount() throws ServiceException {
        return Integer.parseInt(configurationUtil.getConfigItemValue(INDICATOR_PREDICT_AGGREGATE_TASK_NUMBER));
    }

    /**
     * 当前指标区域预测任务是否支持运行
     *
     * @param toStartTask 与启动任务
     * @return 结果
     * @throws ServiceException ex
     */
    public boolean isPredictTaskSupportRunning(AnalysisTask toStartTask) throws ServiceException {
        AnalysisTaskListQueryParam param = new AnalysisTaskListQueryParam();
        List<Integer> taskTypes = new ArrayList<>();
        taskTypes.add(TaskTypeEnum.INDICATORPREDICTTASK.enumToInt());
        param.setTaskType(taskTypes);
        AnalysisTaskList analysisTaskList = taskManagerClient.getAnalysisTaskList(param);
        // 查询当前的已激活的任务以及正在预测的任务
        List<AnalysisTask> analysisTasks = new ArrayList<>();
        for (AnalysisTask task : analysisTaskList.getRows()) {
            if (TaskConstant.TASK_START_STATUS_BEGIN == task.getStartStatus() || (task.getStatus() != null
                && TaskConstant.TASK_STATUS_RUNNING == task.getStatus())) {
                analysisTasks.add(task);
            }
        }
        LOGGER.info("[isPredictTaskSupportRunning] The current running task num is {}", analysisTasks.size());
        // 添加本身任务
        if (analysisTasks.stream().noneMatch(task -> Objects.equals(task.getTaskId(), toStartTask.getTaskId()))) {
            analysisTasks.add(toStartTask);
        }
        // 指标预测任务列表
        List<AnalysisTask> analysisTasksList = taskManagerClient.getAnalysisTaskList(TaskTypeEnum.INDICATORPREDICTTASK.enumToInt());
        long maxInsertData = Long.parseLong(configurationUtil.getConfigItemValue(PREDICT_CURRENT_MAX_INSERT_DATA));
        long minuteCountDataOfAllTask = 0L;
        for (AnalysisTask analysisTask: analysisTasks) {
            AnalysisTask runningTask = analysisTasksList.stream()
                .filter(task -> analysisTask.getTaskId().equals(task.getTaskId()))
                .collect(Collectors.toList())
                .get(0);
            List<ModelParameter> modelParameters = JSONArray.parseArray(runningTask.getAlgorithmParam(), ModelParameter.class);
            long predictLength = 60L;
            for (ModelParameter modelParameter : modelParameters) {
                // 预测点数
                if (modelParameter.getParameterName().equals("predict_length")) {
                    predictLength = Integer.parseInt(modelParameter.getParameterDefaultValue().toString());
                    break;
                }
            }
            // 每分钟数据量
            minuteCountDataOfAllTask += runningTask.getIndicatorList().size() * predictLength;
            if (minuteCountDataOfAllTask > maxInsertData) {
                LOGGER.info("[isPredictTaskSupportRunning] The current running task data is {}", minuteCountDataOfAllTask);
                return true;
            }
        }
        return false;
    }

    /**
     * 计算cron表达式两次之间的差值
     *
     * @param cron cron表达式
     * @return 差值
     */
    public static long calculateCronTimeInterval(String cron) {
        CronExpression cronExpression = CronExpression.parse(cron);
        LocalDateTime nextDate = cronExpression.next(LocalDateTime.now());
        if (nextDate != null) {
            LocalDateTime secondDate = cronExpression.next(nextDate);
            if (secondDate != null) {
                return secondDate.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()
                    - nextDate.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            } else {
                return 10 * MIN;
            }
        } else {
            return 10 * MIN;
        }
    }

    /**
     * 把带单位的时间转换为时间戳,时间格式为 数字+|+单位
     *
     * @param time 时间戳字符
     * @return 时间戳
     */
    public long transformTime(String time) {
        String[] times = time.split("\\|");
        long timeInterVal;
        switch (times[1]) {
            case "MIN":
                timeInterVal = MIN;
                break;
            case "HOUR":
                timeInterVal = HOUR;
                break;
            case "DAY":
                timeInterVal = DAY;
                break;
            case "WEEK":
                timeInterVal = WEEK;
                break;
            default:
                throw new IllegalArgumentException("can't transform time because not know timeInterval!");
        }
        return Integer.parseInt(times[0]) * timeInterVal;
    }

    private boolean validAlarmTimeRange(String alarmTimeRange) {
        if (StringUtils.isEmpty(alarmTimeRange)) {
            return false;
        }
        if (!capacityAlarmTimeRangeSet.contains(alarmTimeRange)) {
            LOGGER.error("time range set not contains this time range");
            return false;
        }
        return true;
    }

    /**
     * 校验同质比较算法参数
     *
     * @param task 任务
     * @return boolean校验是否通过
     */
    public boolean homogeneousComparisonAlgorithmValid(AnalysisTask task) {
        List<ModelParameter> modelParameters = JSONArray.parseArray(task.getAlgorithmParam(), ModelParameter.class);
        int radiusType = 0;
        float eps = 0.0f;
        for (ModelParameter modelParameter : modelParameters) {
            if (modelParameter.getParameterName().equals("radius_type")) {
                radiusType = Integer.parseInt(modelParameter.getParameterDefaultValue().toString());
            } else if (modelParameter.getParameterName().equals("eps")) {
                eps = Float.parseFloat(modelParameter.getParameterDefaultValue().toString());
            }
        }
        return radiusType != 0 || (!(eps > 1.0));
    }
}
