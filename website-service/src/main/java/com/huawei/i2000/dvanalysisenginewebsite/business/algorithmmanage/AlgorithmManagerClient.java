/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisenginewebsite.business.algorithmmanage;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.digitalview.commons.cms.CMSVerifyException;
import com.huawei.digitalview.commons.cms.ReleasePkgCheck;
import com.huawei.i2000.cbb.security.file.FileUtils;
import com.huawei.i2000.dvanalysisenginewebsite.business.common.TaskTypeEnum;
import com.huawei.i2000.dvanalysisenginewebsite.model.AlgorithmListQueryParam;
import com.huawei.i2000.dvanalysisenginewebsite.model.AlgorithmQueryParam;
import com.huawei.i2000.dvanalysisenginewebsite.model.AlgorithmResult;
import com.huawei.i2000.dvanalysisenginewebsite.util.ContextUtils;
import com.huawei.i2000.dvanalysisenginewebsite.util.RestConstant;
import com.huawei.i2000.dvanalysisenginewebsite.util.RestUtil;
import com.huawei.i2000.dvanalysisenginewebsite.util.operationlog.LogConstant;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * AlgorithmManagerClient
 *
 * <AUTHOR>
 * @since ?
 */
@Service
public class AlgorithmManagerClient {

    private static final String TEMP_PATH = System.getProperty("java.io.tmpdir") + File.separator;

    private static final OssLog LOGGER = OssLogFactory.getLogger(AlgorithmManagerClient.class);

    private static final String URL_ALGORITHM_UPLOAD = "/rest/dvanalysisengineservice/v1/algorithm/upload";

    private static final String ALGORITHM_GET = "/rest/dvanalysisengineservice/v1/algorithm/get";

    private static final String ALGORITHM_BY_TYPE = "/rest/dvanalysisengineservice/v1/algorithm/algorithmlistbytype";

    private static final String ALGORITHM_DELETE = "/rest/dvanalysisengineservice/v1/algorithm/delete";

    private static final String ALGORITHM_IS_USERD = "/rest/dvanalysisengineservice/v1/algorithm/isalgorithmused";

    private static HashMap<String, Integer> algorithmTypeMap = new HashMap<>();

    private static final Integer ALGORITHM_NAME_INDEX = 2;

    private static final Integer VERSION_INDEX = 1;

    private static final String FILE_NAME_REGEX = "[a-zA-Z0-9\\-]*_[a-zA-Z0-9]*_[a-zA-Z0-9]*_[a-zA-Z0-9]*";

    static {
        algorithmTypeMap.put("DVAnomalyDetection", TaskTypeEnum.INDICATORTASK.enumToInt());
        algorithmTypeMap.put("DVAlarmAnalysis", TaskTypeEnum.ALARMTASK.enumToInt());
        algorithmTypeMap.put("DVLogAnomalyDetection", TaskTypeEnum.LOGTASK.enumToInt());
        algorithmTypeMap.put("DVCorrelationAnalysis", TaskTypeEnum.CORRELATIONTASK.enumToInt());
        algorithmTypeMap.put("DVIndicatorAlarmAnalysis", TaskTypeEnum.CORRELATIONTASK.enumToInt());
        algorithmTypeMap.put("DVIndicatorLogAnalysis", TaskTypeEnum.CORRELATIONTASK.enumToInt());
        algorithmTypeMap.put("DVAnomalyDetectionV1", TaskTypeEnum.INDICATORTASK.enumToInt());
        algorithmTypeMap.put("DVCapacityPrediction", TaskTypeEnum.CAPACITYTASK.enumToInt());
        algorithmTypeMap.put("DVIndicatorLogAnalysisPyTorch", TaskTypeEnum.CORRELATIONTASK.enumToInt());
        algorithmTypeMap.put("DVLogAnomalyDetectionPyTorch", TaskTypeEnum.LOGTASK.enumToInt());
        algorithmTypeMap.put("DVAnomalyDetectionV2", TaskTypeEnum.INDICATORTASK.enumToInt());
        algorithmTypeMap.put("DVIndicatorForecast", TaskTypeEnum.INDICATORPREDICTTASK.enumToInt());
        algorithmTypeMap.put("DVResourceSchedule", TaskTypeEnum.RESOURCESCHEDULETASK.enumToInt());
    }

    public synchronized void uploadAlgorithmModelPackage(Map<String, byte[]> fileMap, String types)
        throws ServiceException {
        Map.Entry<String, byte[]> algorithmEntry = null;
        Map.Entry<String, byte[]> signEntry = null;
        for (Map.Entry<String, byte[]> fileEntry : fileMap.entrySet()) {
            if (fileEntry.getKey().endsWith(".zip")) {
                algorithmEntry = fileEntry;
            } else if (fileEntry.getKey().endsWith(".p7s")) {
                signEntry = fileEntry;
            }
        }
        if (algorithmEntry == null || signEntry == null) {
            throw new ServiceException(ResourceUtil.getMessage(LogConstant.ALGORITHM_NAME_ERROR, ContextUtils.getContext().getLocale()));
        }
        checkAlgorithmName(algorithmEntry.getKey());
        checkSignName(signEntry.getKey());
        checkSign(algorithmEntry, signEntry);
        String[] fileNames = algorithmEntry.getKey().split("_");
        String fileName = fileNames[ALGORITHM_NAME_INDEX] + "_" + fileNames[VERSION_INDEX] + ".zip";
        RestfulParametes restfulParametes = new RestfulParametes();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("algorithmTypes", types);
        jsonObject.put("fileName", fileName);
        jsonObject.put("fileByte", Base64.getEncoder().encodeToString(algorithmEntry.getValue()));
        restfulParametes.setRawData(jsonObject.toJSONString());
        RestfulResponse restfulResponse = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
            URL_ALGORITHM_UPLOAD, restfulParametes, null);
        RestUtil.checkIRCode(restfulResponse);
    }

    private void checkAlgorithmName(String fileName) throws ServiceException {
        if (!Pattern.matches(FILE_NAME_REGEX, fileName.substring(0, fileName.indexOf(".zip")))) {
            throw new ServiceException(ResourceUtil.getMessage(LogConstant.ALGORITHM_NAME_ERROR, ContextUtils.getContext().getLocale()));
        }
    }

    private void checkSignName(String fileName) throws ServiceException {
        if (!Pattern.matches(FILE_NAME_REGEX, fileName.substring(0, fileName.indexOf(".zip.p7s")))) {
            throw new ServiceException(ResourceUtil.getMessage(LogConstant.ALGORITHM_NAME_ERROR, ContextUtils.getContext().getLocale()));
        }
    }

    /**
     * 校验签名
     *
     * @param algorithmEntry 算法包文件
     * @param signEntry 签名文件
     * @throws ServiceException 异常
     */
    private void checkSign(Map.Entry<String, byte[]> algorithmEntry, Map.Entry<String, byte[]> signEntry)
        throws ServiceException {
        if (algorithmEntry == null || signEntry == null) {
            throw new ServiceException(ResourceUtil.getMessage(LogConstant.SIGN_ERROR, ContextUtils.getContext().getLocale()));
        }
        String tempPath = TEMP_PATH + UUID.randomUUID();
        String zipTmpPath = tempPath + File.separator + algorithmEntry.getKey();
        String p7sTmpPath = tempPath + File.separator + signEntry.getKey();
        try {
            Files.createDirectories(Paths.get(tempPath));
        } catch (IOException e) {
            LOGGER.error("[checkSign] create dir error", e);
            throw new ServiceException(ResourceUtil.getMessage(LogConstant.SIGN_ERROR, ContextUtils.getContext().getLocale()));
        }
        try {
            writeFile(zipTmpPath, algorithmEntry.getValue());
            writeFile(p7sTmpPath, signEntry.getValue());
            ReleasePkgCheck pkgCheck = new ReleasePkgCheck(p7sTmpPath, zipTmpPath);
            pkgCheck.verify();
        } catch (CMSVerifyException exception) {
            LOGGER.error("verify P7S file error", exception);
            throw new ServiceException(ResourceUtil.getMessage(LogConstant.SIGN_ERROR, ContextUtils.getContext().getLocale()));
        } finally {
            FileUtils.getInst().deleteDir(new File(tempPath));
        }
    }

    /**
     * 将文件写入本地目录
     *
     * @param filePath 文件路径
     * @param mFile 文件内容
     * @throws ServiceException 异常信息
     */
    private void writeFile(String filePath, byte[] mFile) throws ServiceException {
        try (FileOutputStream fileOutputStream = new FileOutputStream(filePath)) {
            fileOutputStream.write(mFile);
            fileOutputStream.flush();
        } catch (IOException e) {
            LOGGER.error("write file to local path failed", e);
            throw new ServiceException(ResourceUtil.getMessage(LogConstant.INNER_ERROR, ContextUtils.getContext().getLocale()));
        }
    }

    public AlgorithmResult get(AlgorithmQueryParam queryParam) throws ServiceException {
        LOGGER.info("[AlgorithmManagerClient] entry get()");
        try {
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(JSON.toJSONString(queryParam));
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, ALGORITHM_GET,
                restfulParametes, null);
            RestUtil.checkIRCode(response);
            return JSONObject.parseObject(response.getResponseContent(), AlgorithmResult.class);
        } catch (ServiceException exception) {
            LOGGER.error("[AlgorithmManagerClient] get() failed, error: {}", exception);
            throw exception;
        }
    }

    /**
     * 按算法包类型查询算法包列表
     *
     * @param queryParam 入参
     * @return 算法包列表
     * @throws ServiceException 异常
     */
    public AlgorithmResult getAlgorithmListByType(AlgorithmListQueryParam queryParam) throws ServiceException {
        LOGGER.info("[AlgorithmManagerClient] entry getAlgorithmListByType()");
        try {
            RestfulParametes restfulParametes = new RestfulParametes();
            restfulParametes.setRawData(JSON.toJSONString(queryParam));
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, ALGORITHM_BY_TYPE,
                restfulParametes, null);
            RestUtil.checkIRCode(response);
            return JSONObject.parseObject(response.getResponseContent(), AlgorithmResult.class);
        } catch (ServiceException exception) {
            LOGGER.error("[AlgorithmManagerClient] getAlgorithmListByType() failed, error: {}", exception);
            throw exception;
        }
    }

    public void delete(String id) throws ServiceException {
        LOGGER.info("[AlgorithmManagerClient] entry delete()");
        try {
            AlgorithmQueryParam queryParam = new AlgorithmQueryParam();
            queryParam.setId(Integer.parseInt(id));
            AlgorithmResult result = get(queryParam);
            if (result.getTotal() == 1 && algorithmTypeMap.containsKey(
                result.getAlgorithmList().get(0).getAlgorithmName()) && "default".equals(
                result.getAlgorithmList().get(0).getVersion())) {
                LOGGER.error("[AlgorithmManagerClient] default algorithm not allowed delete, id: {}", id);
                throw new ServiceException("delete not allowed");
            }
            String url = RestUtil.addUrlParam(ALGORITHM_DELETE, "id", id);
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST, url, null, null);
            RestUtil.checkIRCode(response);
        } catch (ServiceException exception) {
            LOGGER.error("[AlgorithmManagerClient] delete() failed, error: {}", exception);
            throw exception;
        }
    }

    public JSONObject isAlgorithmUsed(String id) throws ServiceException {
        try {
            String url = RestUtil.addUrlParam(ALGORITHM_IS_USERD, "id", id);
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_GET, url, null, null);
            RestUtil.checkIRCode(response);
            return JSONObject.parseObject(response.getResponseContent());
        } catch (ServiceException exception) {
            LOGGER.error("[AlgorithmManagerClient] isAlgorithmUsed() failed, error: {}", exception);
            throw exception;
        }
    }
}
