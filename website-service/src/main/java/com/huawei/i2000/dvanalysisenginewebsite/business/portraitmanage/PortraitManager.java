/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 */

package com.huawei.i2000.dvanalysisenginewebsite.business.portraitmanage;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.dvanalysisenginewebsite.business.common.parametercheck.BooleanCheckResult;
import com.huawei.i2000.dvanalysisenginewebsite.business.portraitmanage.dto.DeployService;
import com.huawei.i2000.dvanalysisenginewebsite.model.BusinessPortrait;
import com.huawei.i2000.dvanalysisenginewebsite.model.HostPortrait;
import com.huawei.i2000.dvanalysisenginewebsite.model.PortraitIndicator;
import com.huawei.i2000.dvanalysisenginewebsite.model.PortraitListValidateParam;
import com.huawei.i2000.dvanalysisenginewebsite.model.ProductPortrait;
import com.huawei.i2000.dvanalysisenginewebsite.model.ProductPortraitSharedIndicators;
import com.huawei.i2000.dvanalysisenginewebsite.util.ContextUtils;
import com.huawei.i2000.dvanalysisenginewebsite.util.RestConstant;
import com.huawei.i2000.dvanalysisenginewebsite.util.RestUtil;
import com.huawei.i2000.dvanalysisenginewebsite.util.operationlog.LogConstant;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONException;
import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 画像管理器
 *
 * <AUTHOR>
 * @since 2023/4/26
 */
@Component
public class PortraitManager {
    OssLog LOGGER = OssLogFactory.getLogger(PortraitManager.class);

    public static final String INDICATOR_IDENTITY_NAME_SEP = "(_)";

    private static final String PORTRAIT_MANAGE_IR_URL_PREFIX = "/rest/dvanalysisengineservice/v1/portraitmanage";

    private static final String MODIFY_IR_URL = "/productportrait";

    private static final String REMOVE_IR_URL = "/removeproductportrait";

    private static final String PRODUCT_LIST_IR_URL = "/getproductportraitlist";

    private static final String DETAIL_IR_URL = "/getproductportraitdetail";

    private static final String BUSINESS_LIST_IR_URL = "/getbusinessportraitlist";

    private static final String ASSOCIATION_IR_URL = "/isportraitassociated";

    private static final Integer MAX_INTEGER_DIGITS = 12;

    private static final Integer MAX_DECIMAL_DIGITS = 2;

    public static final String ADMIN_ID = "1";

    public static final int HOST_PORTRAIT_TYPE = 1;

    public static final int BUSINESS_PORTRAIT_TYPE = 2;

    /**
     * 新增或者修改产品画像
     *
     * @param productPortrait productPortrait
     * @throws ServiceException ServiceException
     */
    public void modifyProductPortrait(ProductPortrait productPortrait) throws ServiceException {
        if (productPortrait.getType().equals(1)) {
            setupPortraitSharedIndicator(productPortrait);
        }
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(productPortrait));
        RestfulParametes parametes = new RestfulParametes();
        parametes.setRawData(jsonObject.toJSONString());
        try {
            RestfulResponse restfulResponse = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
                PORTRAIT_MANAGE_IR_URL_PREFIX + MODIFY_IR_URL, parametes, null);
            RestUtil.checkIRCode(restfulResponse);
        } catch (ServiceException e) {
            LOGGER.error("modifyProductPortrait error : {}", e);
            throw e;
        }
    }

    private void setupPortraitSharedIndicator(ProductPortrait productPortrait) throws ServiceException {
        Map<String, Integer> indicatorCount = new HashMap<>();
        Map<String, String> displayTokenRef = new HashMap<>();
        productPortrait.getHostPortraitList()
            .forEach(hostPortrait -> hostPortrait.getPortraitIndicatorList().forEach(portraitIndicator -> {
                String indicatorIdentifyKey = getIndicatorIdentifyKey(portraitIndicator);
                String indicatorDisplayToken = getIndicatorDisplayToken(portraitIndicator);
                indicatorCount.put(indicatorIdentifyKey, indicatorCount.getOrDefault(indicatorIdentifyKey, 0) + 1);
                displayTokenRef.computeIfAbsent(indicatorIdentifyKey, key -> indicatorDisplayToken);
            }));

        int size = productPortrait.getHostPortraitList().size();
        List<ProductPortraitSharedIndicators> sharedIndicators = indicatorCount.entrySet()
            .stream()
            .filter(entry -> entry.getValue().equals(size))
            .collect(Collectors.toList())
            .stream()
            .map(entry -> {
                ProductPortraitSharedIndicators current = new ProductPortraitSharedIndicators();
                current.setDisplayValue(displayTokenRef.get(entry.getKey()));
                current.setReturnValue(entry.getKey());
                return current;
            })
            .collect(Collectors.toList());

        if (sharedIndicators.size() > 0) {
            productPortrait.getSharedIndicators().addAll(sharedIndicators);
        } else {
            throw new ServiceException(ResourceUtil.getMessage(LogConstant.NO_SHARED_INDICATOR, ContextUtils.getContext().getLocale()));
        }
    }

    private String getIndicatorIdentifyKey(PortraitIndicator portraitIndicator) {
        String[] components = StringUtils.isEmpty(portraitIndicator.getDisplayValue()) ? new String[] {
            portraitIndicator.getMeasUnitKey(), portraitIndicator.getMeasTypeKey()
        } : new String[] {
            portraitIndicator.getMeasUnitKey(), portraitIndicator.getMeasTypeKey(), portraitIndicator.getDisplayValue()
        };
        return String.join(INDICATOR_IDENTITY_NAME_SEP, components);
    }

    private String getIndicatorDisplayToken(PortraitIndicator portraitIndicator) {
        String[] components = StringUtils.isEmpty(portraitIndicator.getDisplayValue()) ? new String[] {
            portraitIndicator.getMoType(), portraitIndicator.getMeasUnitName(), portraitIndicator.getIndexName()
        } : new String[] {
            portraitIndicator.getMoType(), portraitIndicator.getMeasUnitName(), portraitIndicator.getIndexName(),
            portraitIndicator.getDisplayValue()
        };
        return String.join("~", components);
    }

    /**
     * 删除产品画像
     *
     * @param productPortraitId 画像ID
     * @throws ServiceException ServiceException
     */
    public void removeProductPortrait(Integer productPortraitId) throws ServiceException {
        RestfulParametes restfulParametes = new RestfulParametes();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("productPortraitId", productPortraitId);
        restfulParametes.setRawData(jsonObject.toJSONString());
        try {
            RestfulResponse restfulResponse = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
                PORTRAIT_MANAGE_IR_URL_PREFIX + REMOVE_IR_URL, restfulParametes, null);
            RestUtil.checkIRCode(restfulResponse);
        } catch (ServiceException e) {
            LOGGER.error("removeProductPortrait error : {}", e);
            throw e;
        }
    }

    /**
     * 校验是否可以修改或删除
     *
     * @param productPortraitId productPortraitId
     * @param productPortrait productPortrait
     * @throws ServiceException ServiceException
     */
    public void validateModify(Integer productPortraitId, ProductPortrait productPortrait) throws ServiceException {
        ProductPortrait localProductPortrait = getProductPortraitDetail(productPortraitId);
        if (!ADMIN_ID.equals(ContextUtils.getContext().getUserId())) {
            if (!localProductPortrait.getUserId().equals(ContextUtils.getContext().getUserId())) {
                throw new ServiceException(
                    ResourceUtil.getMessage(LogConstant.INNER_ERROR, ContextUtils.getContext().getLocale()));
            }
        }
        if (isPortraitCompleted(localProductPortrait) && localProductPortrait.getIsAssociated() != null && localProductPortrait.getIsAssociated()) {
            throw new ServiceException(ResourceUtil.getMessage(LogConstant.MODIFY_INVALID_ASSOCIATION, ContextUtils.getContext().getLocale()));
        }
        if (productPortrait.getName() != null && (!productPortrait.getName().equals(localProductPortrait.getName()) ||
            !productPortrait.getType().equals(localProductPortrait.getType()))) {
            throw new ServiceException("can not change portrait name or type");
        }
        if (localProductPortrait.getType().equals(2)) {
            AtomicBoolean invalid = new AtomicBoolean(false);
            localProductPortrait.getBusinessPortraitList().forEach(businessPortrait -> {
                if (businessPortrait.getIsAssociated() != null && businessPortrait.getIsAssociated()
                    && !(productPortrait.getBusinessPortraitList()
                    .stream()
                    .map(BusinessPortrait::getBusinessName)
                    .collect(Collectors.toSet())
                    .contains(businessPortrait.getBusinessName()) && businessPortraitEquals(businessPortrait,
                    productPortrait.getBusinessPortraitList()
                        .stream()
                        .filter(businessPortrait1 -> businessPortrait1.getBusinessName()
                            .equals(businessPortrait.getBusinessName()))
                        .findFirst()
                        .orElse(new BusinessPortrait())))) {
                    invalid.set(true);
                }
            });
            if (invalid.get()) {
                throw new ServiceException(ResourceUtil.getMessage(LogConstant.MODIFY_INVALID_ASSOCIATION, ContextUtils.getContext().getLocale()));
            }
        }
    }

    private boolean businessPortraitEquals(BusinessPortrait bp1, BusinessPortrait bp2) {
        return Comparator.comparing(BusinessPortrait::getPortraitName)
            .thenComparing(BusinessPortrait::getMaxCpuNum, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BusinessPortrait::getMinCpuNum, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BusinessPortrait::getMaxRamNum, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BusinessPortrait::getMinRamNum, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BusinessPortrait::getMaxReplicaNum, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BusinessPortrait::getMinReplicaNum, Comparator.nullsLast(Comparator.naturalOrder()))
            .thenComparing(BusinessPortrait::getServiceDeployType, Comparator.nullsLast(Comparator.naturalOrder()))
            .compare(bp1, bp2) == 0;
    }

    /**
     * 获取产品画像列表
     *
     * @return 产品画像列表
     * @throws ServiceException ServiceException
     */
    public List<ProductPortrait> getProductPortraitList() throws ServiceException {
        RestfulParametes restfulParametes = new RestfulParametes();
        try {
            RestfulResponse restfulResponse = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
                PORTRAIT_MANAGE_IR_URL_PREFIX + PRODUCT_LIST_IR_URL, restfulParametes, null);
            RestUtil.checkIRCode(restfulResponse);
            return JSON.parseArray(restfulResponse.getResponseContent(), ProductPortrait.class);
        } catch (ServiceException e) {
            LOGGER.error("getProductPortraitList error : {}", e);
            throw e;
        }
    }

    /**
     * 获取产品画像列表
     *
     * @param userId 用户id
     * @return 产品画像列表
     * @throws ServiceException ex
     */
    public List<ProductPortrait> getProductPortraitListByUser(String userId) throws ServiceException {
        List<ProductPortrait> totalList = new ArrayList<>();
        if (ADMIN_ID.equals(userId)) {
            totalList = getProductPortraitList();
        } else {
            totalList = getProductPortraitList().stream()
                .filter(productPortrait -> productPortrait.getUserId().equals(userId))
                .collect(Collectors.toList());
        }
        return totalList;
    }

    /**
     * 获取产品画像详情
     *
     * @param productPortraitId productPortraitId
     * @return 产品画像详情
     * @throws ServiceException ServiceException
     */
    public ProductPortrait getProductPortraitDetail(Integer productPortraitId) throws ServiceException {
        RestfulParametes restfulParametes = new RestfulParametes();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("productPortraitId", productPortraitId);
        restfulParametes.setRawData(jsonObject.toJSONString());
        try {
            RestfulResponse restfulResponse = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
                PORTRAIT_MANAGE_IR_URL_PREFIX + DETAIL_IR_URL, restfulParametes, null);
            RestUtil.checkIRCode(restfulResponse);
            ProductPortrait productPortrait = JSONObject.parseObject(restfulResponse.getResponseContent(), ProductPortrait.class);
            // 处理兼容
            compatibleOldDeployService(productPortrait);
            return productPortrait;
        } catch (ServiceException e) {
            LOGGER.error("getProductPortraitDetail error : {}", e);
            throw e;
        }
    }

    /**
     * 兼容老版本的部署服务格式 ['serviceA','ServiceB','ServiceC']
     *
     * @param productPortrait 产品画像
     */
    private void compatibleOldDeployService(ProductPortrait productPortrait) {
        List<HostPortrait> hostPortraits = productPortrait.getHostPortraitList();
        for (HostPortrait hostPortrait : hostPortraits) {
            List<String> deployServices = hostPortrait.getDeployServiceList();
            try {
                for (String deployService : deployServices) {
                    JSONObject.parseObject(deployService, DeployService.class);
                }
            } catch (JSONException e) {
                // 历史版本格式，保留兼容
                List<String> defaultSetList = new ArrayList<>();
                for (String deploy : deployServices) {
                    DeployService business = new DeployService();
                    business.setServiceName(deploy);
                    business.setDeployNumber(1);
                    defaultSetList.add(JSONObject.toJSONString(business));
                }
                hostPortrait.setDeployServiceList(defaultSetList);
            }
        }
    }

    /**
     * 获取业务画像列表
     *
     * @return 业务画像列表
     * @throws ServiceException ServiceException
     */
    public List<BusinessPortrait> getBusinessPortraitList() throws ServiceException {
        RestfulParametes restfulParametes = new RestfulParametes();
        try {
            RestfulResponse restfulResponse = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
                PORTRAIT_MANAGE_IR_URL_PREFIX + BUSINESS_LIST_IR_URL, restfulParametes, null);
            RestUtil.checkIRCode(restfulResponse);
            return JSON.parseArray(restfulResponse.getResponseContent(), BusinessPortrait.class);
        } catch (ServiceException e) {
            LOGGER.error("getBusinessPortraitList error : {}", e);
            throw e;
        }
    }

    /**
     * 获取业务画像列表
     *
     * @param userId 用户Id
     * @return 业务画像列表
     * @throws ServiceException ex
     */
    public List<BusinessPortrait> getBusinessPortraitListByUser(String userId) throws ServiceException {
        List<BusinessPortrait> totalList = new ArrayList<>();
        if (ADMIN_ID.equals(userId)) {
            totalList = getBusinessPortraitList();
        } else {
            List<ProductPortrait> totalPortraitList = getProductPortraitList().stream()
                .filter(productPortrait -> productPortrait.getUserId().equals(userId)
                    && BUSINESS_PORTRAIT_TYPE == productPortrait.getType())
                .collect(Collectors.toList());
            for (ProductPortrait portrait : totalPortraitList) {
                ProductPortrait detailPortrait = getProductPortraitDetail(portrait.getId());
                totalList.addAll(detailPortrait.getBusinessPortraitList());
            }
        }
        return totalList;
    }

    /**
     * 画像是否被关联
     *
     * @param param param
     * @return BooleanCheckResult
     * @throws ServiceException Service Exception
     */
    public BooleanCheckResult isPortraitAssociated(PortraitListValidateParam param) throws ServiceException {
        RestfulParametes restfulParametes = new RestfulParametes();
        restfulParametes.setRawData(JSONObject.toJSONString(param));
        try {
            RestfulResponse restfulResponse = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_POST,
                PORTRAIT_MANAGE_IR_URL_PREFIX + ASSOCIATION_IR_URL, restfulParametes, null);
            RestUtil.checkIRCode(restfulResponse);
            return JSONObject.parseObject(restfulResponse.getResponseContent(), BooleanCheckResult.class);
        } catch (ServiceException e) {
            LOGGER.error("isPortraitAssociated error : {}", e);
            throw e;
        }
    }

    /**
     * 上下阈值是否合法
     *
     * @param productPortrait productPortrait
     * @throws ServiceException Service Exception
     */
    public void checkUpperLowerThresholds(ProductPortrait productPortrait) throws ServiceException {
        // 校验主机画像上下阈值
        if (productPortrait.getType() == 1) {
            List<HostPortrait> hostPortraits = productPortrait.getHostPortraitList();
            for (HostPortrait hostPortrait : hostPortraits) {
                if (hostPortrait.getMaxHostNum() != null && hostPortrait.getMaxHostNum() < hostPortrait.getMinHostNum()) {
                    throw new ServiceException(ResourceUtil.getMessage(LogConstant.HOST_PORTRAIT_HOST_NUMBER, ContextUtils.getContext().getLocale()));
                }
                List<PortraitIndicator> portraitIndicators = hostPortrait.getPortraitIndicatorList();
                for (PortraitIndicator indicator : portraitIndicators) {
                    if (thresholdsOutOfRange(indicator.getUpperThreshold()) || thresholdsOutOfRange(indicator.getLowerThreshold())) {
                        throw new ServiceException("Indicator thresholds illegal");
                    }
                    if (indicator.getLowerThreshold() != null && indicator.getUpperThreshold() != null
                        && indicator.getUpperThreshold() < indicator.getLowerThreshold()) {
                        throw new ServiceException(ResourceUtil.getMessage(LogConstant.HOST_PORTRAIT_INDICATOR_THRESHOLD, ContextUtils.getContext().getLocale()));
                    }
                }
            }
        }
        // 校验业务画像上下阈值
        List<BusinessPortrait> businessPortraits = productPortrait.getBusinessPortraitList();
        for (BusinessPortrait businessPortrait : businessPortraits) {
            if (businessPortrait.getMaxCpuNum() < businessPortrait.getMinCpuNum()) {
                throw new ServiceException(ResourceUtil.getMessage(LogConstant.BUSINESS_PORTRAIT_CPU_NUMBER, ContextUtils.getContext().getLocale()));
            }
            if (businessPortrait.getMaxRamNum() < businessPortrait.getMinRamNum()) {
                throw new ServiceException(ResourceUtil.getMessage(LogConstant.BUSINESS_PORTRAIT_RAM_NUMBER, ContextUtils.getContext().getLocale()));
            }
            if (businessPortrait.getMaxReplicaNum() != null && businessPortrait.getMaxReplicaNum() < businessPortrait.getMinReplicaNum()) {
                throw new ServiceException(ResourceUtil.getMessage(LogConstant.BUSINESS_PORTRAIT_REPLICAS_NUMBER, ContextUtils.getContext().getLocale()));
            }
        }
    }

    private boolean thresholdsOutOfRange(Double thresholds) {
        if (thresholds != null) {
            DecimalFormat format = new DecimalFormat();
            format.setRoundingMode(RoundingMode.DOWN);
            format.setGroupingUsed(false);
            String thresholdsStr = format.format(thresholds);
            if (thresholdsStr.contains(".")) {
                List<String> splits = Arrays.asList(thresholdsStr.split("\\."));
                return splits.get(0).length() > MAX_INTEGER_DIGITS || splits.get(1).length() > MAX_DECIMAL_DIGITS;
            } else {
                return thresholdsStr.length() > MAX_INTEGER_DIGITS;
            }
        }
        return false;
    }

    private boolean isPortraitCompleted(ProductPortrait localProductPortrait) {
        // CPU总数基线和RAM总数基线是否完成
        if (localProductPortrait.getCpuNum() == null || localProductPortrait.getRamNum() == null) {
            return false;
        }
        // 主机画像的最小主机数是否完成
        if (!CollectionUtils.isEmpty(localProductPortrait.getHostPortraitList())) {
            List<HostPortrait> hostPortraits = localProductPortrait.getHostPortraitList();
            for (HostPortrait hostPortrait : hostPortraits) {
                if (hostPortrait.getMinHostNum() == null) {
                    return false;
                }
            }
        }
        // 业务画像的最小主机数是否完成
        if (!CollectionUtils.isEmpty(localProductPortrait.getBusinessPortraitList())) {
            List<BusinessPortrait> businessPortraits = localProductPortrait.getBusinessPortraitList();
            for (BusinessPortrait businessPortrait : businessPortraits) {
                if (businessPortrait.getMinReplicaNum() == null) {
                    return false;
                }
            }
        }
        return true;
    }
}
