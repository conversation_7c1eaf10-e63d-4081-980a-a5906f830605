/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisenginewebsite.impl;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.common.HttpContext;
import com.huawei.cloudsop.sys.util.i18n.ResourceUtil;
import com.huawei.i2000.cbb.sm.model.LogResult;
import com.huawei.i2000.cbb.sm.model.LogSeverity;
import com.huawei.i2000.dvanalysisenginewebsite.business.disasterrecovery.monitorgroup.GroupManagerClient;
import com.huawei.i2000.dvanalysisenginewebsite.delegate.MonitorGroupServiceDelegate;
import com.huawei.i2000.dvanalysisenginewebsite.impl.util.ResponseEntityHelper;
import com.huawei.i2000.dvanalysisenginewebsite.model.AddMonitorGroupResp;
import com.huawei.i2000.dvanalysisenginewebsite.model.DisasterRecoveryTaskInfo;
import com.huawei.i2000.dvanalysisenginewebsite.model.FlowActionList;
import com.huawei.i2000.dvanalysisenginewebsite.model.GroupIdParam;
import com.huawei.i2000.dvanalysisenginewebsite.model.GroupMember;
import com.huawei.i2000.dvanalysisenginewebsite.model.MemberIdParam;
import com.huawei.i2000.dvanalysisenginewebsite.model.MonitorGroupModel;
import com.huawei.i2000.dvanalysisenginewebsite.model.MonitorItem;
import com.huawei.i2000.dvanalysisenginewebsite.model.ResponseEntity;
import com.huawei.i2000.dvanalysisenginewebsite.util.ContextUtils;
import com.huawei.i2000.dvanalysisenginewebsite.util.operationlog.LogConstant;
import com.huawei.i2000.dvanalysisenginewebsite.util.operationlog.LogOperationModule;
import com.huawei.i2000.dvanalysisenginewebsite.util.operationlog.OperationLogUtil;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 容灾区域管理
 *
 * <AUTHOR>
 * @since 2022/08/18
 */
@Component
public class MonitorGroupServiceDelegateImpl implements MonitorGroupServiceDelegate {
    private static final OssLog LOGGER = OssLogFactory.getLogger(MonitorGroupServiceDelegateImpl.class);

    @Autowired
    GroupManagerClient groupManagerClient;

    @Override
    public ResponseEntity addFlowAction(HttpContext context, FlowActionList flowActions) throws ServiceException {
        LogOperationModule logOperationModule = OperationLogUtil.getLogOperationModule(
            ResourceUtil.getMessage(LogConstant.OPERATION_DR_AREA_ID, ContextUtils.getContext().getLocale())
                + ResourceUtil.getMessage(LogConstant.COLON, ContextUtils.getContext().getLocale())
                + flowActions.getGroupId(),
            ResourceUtil.getMessage(LogConstant.OPERATION_ADD, ContextUtils.getContext().getLocale()),
            ResourceUtil.getMessage(LogConstant.OPERATION_ADD_FLOW_ACTION_DETAIL,
                ContextUtils.getContext().getLocale()), LogResult.SUCCESSFUL, LogSeverity.MINOR);
        try {
            groupManagerClient.addFlowAction(flowActions);
            return ResponseEntityHelper.correctResponseEntity(null);
        } catch (ServiceException e) {
            logOperationModule.setResult(LogResult.FAILURE);
            return ResponseEntityHelper.failResponseEntity(e);
        } finally {
            OperationLogUtil.sendAuditLog(context.getHttpServletRequest(), logOperationModule);
        }
    }

    @Override
    public ResponseEntity add(HttpContext context, DisasterRecoveryTaskInfo disasterRecoveryTaskInfo)
        throws ServiceException {
        LogOperationModule logOperationModule = OperationLogUtil.getLogOperationModule(
            ResourceUtil.getMessage(LogConstant.OPERATION_DR_AREA_NAME, ContextUtils.getContext().getLocale())
                + ResourceUtil.getMessage(LogConstant.COLON, ContextUtils.getContext().getLocale())
                + disasterRecoveryTaskInfo.getMonitorGroup().getGroupName(),
            ResourceUtil.getMessage(LogConstant.OPERATION_ADD, ContextUtils.getContext().getLocale()),
            getAddGroupOperationDetail(disasterRecoveryTaskInfo), LogResult.SUCCESSFUL, LogSeverity.MINOR);
        try {
            AddMonitorGroupResp addMonitorGroupResp = groupManagerClient.addMonitorGroup(disasterRecoveryTaskInfo);
            return ResponseEntityHelper.correctResponseEntity(addMonitorGroupResp);
        } catch (ServiceException e) {
            logOperationModule.setResult(LogResult.FAILURE);
            return ResponseEntityHelper.failResponseEntity(e);
        } finally {
            OperationLogUtil.sendAuditLog(context.getHttpServletRequest(), logOperationModule);
        }
    }

    @Override
    public ResponseEntity addMonitorItem(HttpContext context, MonitorItem monitorItem) throws ServiceException {
        LogOperationModule logOperationModule = OperationLogUtil.getLogOperationModule(
            ResourceUtil.getMessage(LogConstant.OPERATION_DR_AREA_ID, ContextUtils.getContext().getLocale())
                + ResourceUtil.getMessage(LogConstant.COLON, ContextUtils.getContext().getLocale())
                + monitorItem.getGroupId(),
            ResourceUtil.getMessage(LogConstant.OPERATION_ADD, ContextUtils.getContext().getLocale()),
            ResourceUtil.getMessage(LogConstant.OPERATION_ADD_MONITOR_ITEM_DETAIL,
                ContextUtils.getContext().getLocale()), LogResult.SUCCESSFUL, LogSeverity.MINOR);
        try {
            groupManagerClient.addMonitorItem(monitorItem);
            return ResponseEntityHelper.correctResponseEntity(null);
        } catch (ServiceException e) {
            logOperationModule.setResult(LogResult.FAILURE);
            return ResponseEntityHelper.failResponseEntity(e);
        } finally {
            OperationLogUtil.sendAuditLog(context.getHttpServletRequest(), logOperationModule);
        }
    }

    private String getAddGroupOperationDetail(DisasterRecoveryTaskInfo disasterRecoveryTaskInfo) {
        String detailTemplate = ResourceUtil.getMessage(LogConstant.OPERATION_ADD_DETAIL,
            ContextUtils.getContext().getLocale());
        String groupName = disasterRecoveryTaskInfo.getMonitorGroup().getGroupName();
        List<GroupMember> groupMemberList = disasterRecoveryTaskInfo.getMonitorGroup().getGroupMembers();
        String dnDetail = "";

        if (CollectionUtils.isNotEmpty(groupMemberList)) {
            if (groupMemberList.size() <= 3) {
                dnDetail = String.join(",",
                    groupMemberList.stream().map(GroupMember::getDn).collect(Collectors.toList()));
            } else {
                dnDetail = groupMemberList.get(0).getDn() + "," + groupMemberList.get(1).getDn() + ","
                    + groupMemberList.get(2).getDn() + "...";
            }
        }
        return String.format(detailTemplate, groupName, dnDetail);
    }

    @Override
    public ResponseEntity deleteGroup(HttpContext context, GroupIdParam groupIdParam) throws ServiceException {
        LogOperationModule logOperationModule = OperationLogUtil.getLogOperationModule(
            ResourceUtil.getMessage(LogConstant.OPERATION_DR_AREA_ID, ContextUtils.getContext().getLocale())
                + ResourceUtil.getMessage(LogConstant.COLON, ContextUtils.getContext().getLocale())
                + groupIdParam.getGroupId(),
            ResourceUtil.getMessage(LogConstant.OPERATION_DEL, ContextUtils.getContext().getLocale()),
            ResourceUtil.getMessage(LogConstant.OPERATION_DEL_DETAIL, ContextUtils.getContext().getLocale()),
            LogResult.SUCCESSFUL, LogSeverity.RISK);
        try {
            groupManagerClient.deleteGroupById(groupIdParam.getGroupId());
            return ResponseEntityHelper.correctResponseEntity(null);
        } catch (ServiceException e) {
            logOperationModule.setResult(LogResult.FAILURE);
            return ResponseEntityHelper.failResponseEntity(e);
        } finally {
            OperationLogUtil.sendAuditLog(context.getHttpServletRequest(), logOperationModule);
        }
    }

    @Override
    public ResponseEntity deletMember(HttpContext context, MemberIdParam memberIdParam) throws ServiceException {
        LogOperationModule logOperationModule = OperationLogUtil.getLogOperationModule(
            ResourceUtil.getMessage(LogConstant.OPERATION_DR_MEMBER_ID, ContextUtils.getContext().getLocale())
                + ResourceUtil.getMessage(LogConstant.COLON, ContextUtils.getContext().getLocale())
                + memberIdParam.getMemberId(),
            ResourceUtil.getMessage(LogConstant.OPERATION_DEL, ContextUtils.getContext().getLocale()),
            ResourceUtil.getMessage(LogConstant.OPERATION_DEL_DETAIL, ContextUtils.getContext().getLocale()),
            LogResult.SUCCESSFUL, LogSeverity.RISK);
        try {
            groupManagerClient.deleteMemberById(memberIdParam.getMemberId());
            return ResponseEntityHelper.correctResponseEntity(null);
        } catch (ServiceException e) {
            logOperationModule.setResult(LogResult.FAILURE);
            return ResponseEntityHelper.failResponseEntity(e);
        } finally {
            OperationLogUtil.sendAuditLog(context.getHttpServletRequest(), logOperationModule);
        }
    }

    @Override
    public ResponseEntity queryAllGroup(HttpContext context, String taskId) throws ServiceException {
        try {
            return ResponseEntityHelper.correctResponseEntity(groupManagerClient.getGroupList(taskId));
        } catch (ServiceException e) {
            return ResponseEntityHelper.failResponseEntity(e);
        }
    }

    @Override
    public ResponseEntity queryFlowAction(HttpContext context, String groupId) throws ServiceException {
        try {
            return ResponseEntityHelper.correctResponseEntity(groupManagerClient.getFlowAction(groupId));
        } catch (ServiceException e) {
            return ResponseEntityHelper.failResponseEntity(e);
        }
    }

    @Override
    public ResponseEntity queryGroup(HttpContext context, String groupId) throws ServiceException {
        try {
            MonitorGroupModel group = groupManagerClient.getGroup(groupId);
            return ResponseEntityHelper.correctResponseEntity(group);
        } catch (ServiceException e) {
            return ResponseEntityHelper.failResponseEntity(e);
        }
    }

    @Override
    public ResponseEntity queryMonitoritem(HttpContext context, String groupId) throws ServiceException {
        try {
            MonitorItem monitorItem = groupManagerClient.getMonitoritem(groupId);
            return ResponseEntityHelper.correctResponseEntity(monitorItem);
        } catch (ServiceException e) {
            return ResponseEntityHelper.failResponseEntity(e);
        }
    }

}

