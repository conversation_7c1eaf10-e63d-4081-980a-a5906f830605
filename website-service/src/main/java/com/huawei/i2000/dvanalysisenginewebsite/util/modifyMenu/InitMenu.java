/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.i2000.dvanalysisenginewebsite.util.modifyMenu;

import com.huawei.bsp.deploy.util.DefaultEnvUtil;
import com.huawei.bsp.deploy.util.SystemUtil;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.util.restclient.RestfulParametes;
import com.huawei.bsp.roa.util.restclient.RestfulResponse;
import com.huawei.i2000.dvanalysisenginewebsite.business.taskmanage.TaskValidChecker;
import com.huawei.i2000.dvanalysisenginewebsite.util.MenuConfigHelp;
import com.huawei.i2000.dvanalysisenginewebsite.util.RestConstant;
import com.huawei.i2000.dvanalysisenginewebsite.util.RestUtil;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;
import org.springframework.stereotype.Component;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Properties;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 单套件升级时菜单初始化类
 *
 * <AUTHOR>
 * @since 2022/09/02
 */
@Component
public class InitMenu implements InitializingBean {
    private static final OssLog LOGGER = OssLogFactory.getLog(InitMenu.class);

    private static final String VERSION_KEY = "APP_NEW_VERSION";

    private static final String DV_COMMON_ENV_PATH = "/opt/oss/SOP/apps/DVCommonConfigService/envs/env.properties";

    private static final int DVR022C20_VERSION = 8222; // 该版本及以后的版本菜单不需要处理

    private static final String FEBS_MENU_URL = "/i/febs/apps";

    private static final String DELETE_MENU_URL = "/i/febs/apps/";

    private static final String FEBS_JSON_FILE_NAME = "febs.json";

    private static final String ALL_MENUS_FILE_NAME = "allMenus.json";

    private static final String TEMP_PATH = "/webapps/ROOT/WEB-INF/classes/menus/";

    private static final String[] DELETED_MENU_ID = new String[] {
        "faultAnalysis", "capacityEvaluation", "disasterRecovery", "indicatorPrediction"
    };

    private static final String[] ALL_MENU_ID = new String[] {
        "faultAnalysis_cf", "capacityEvaluation_cf", "disasterRecovery_cf", "indicatorPrediction_cf",
        "tidalScheduling_cf", "capacityConfig_cf"
    };

    private static final String MENU_KEY_SUFFIX = "_cf";

    private final MenuConfigHelp menuConfigHelp = new MenuConfigHelp();

    private static final String GET_MENU_URL = "/febs/pub/info?locale=en-us";

    private final ThreadPoolExecutor refreshMenuExecutor = new ThreadPoolExecutor(1, 1, 5, TimeUnit.MINUTES,
        new LinkedBlockingQueue<>(100), new CustomizableThreadFactory("Refresh-menu-Thread-"));

    /**
     * spring启动后处理
     */
    @Override
    public void afterPropertiesSet() {
        // 起一个线程，先确保febs服务正常之后，再更新智能运维菜单
        refreshMenuExecutor.execute(() -> {
            try {
                waitFebsAndRefreshMenus();
            } catch (InterruptedException t) {
                LOGGER.error("refresh menu thread interrupt!", t);
            } catch (Throwable throwable) {
                LOGGER.error("waitFebsAndRefreshMenus error, e: ", throwable);
            }
        });
    }

    private static boolean isNeedRegisterMenu() {
        String websiteVersion = SystemUtil.getenv(VERSION_KEY);
        if (StringUtils.isEmpty(websiteVersion)) {
            LOGGER.error("Get DVAnalysisWebsite APP_NEW_VERSION failed!");
            return false;
        }
        LOGGER.info("DVAnalysisWebsite APP_NEW_VERSION is {}", websiteVersion);

        Properties dvCommonProperties = new Properties();
        File envFile = new File(DV_COMMON_ENV_PATH);
        try (InputStream in = new BufferedInputStream(new FileInputStream(envFile));
            InputStreamReader propertiesReader = new InputStreamReader(in, StandardCharsets.UTF_8)) {
            dvCommonProperties.load(propertiesReader);
        } catch (FileNotFoundException e) {
            LOGGER.error("[InitMenu] DVCommonConfigService/envs/env.properties is not found!");
            return false;
        } catch (IOException e) {
            LOGGER.error("[InitMenu] read DVCommonConfigService/envs/env.properties failed!");
            return false;
        }
        String dvCommonVersion = dvCommonProperties.getProperty(VERSION_KEY);
        if (StringUtils.isEmpty(dvCommonVersion)) {
            LOGGER.error("Get DVCommonConfigService APP_NEW_VERSION failed!");
            return false;
        }
        LOGGER.info("DVCommonConfigService APP_NEW_VERSION is {}", dvCommonVersion);

        int websiteVersionNum = getVersionNumber(websiteVersion);
        if (websiteVersionNum == 0) {
            return false;
        }
        int commonVersionNum = getVersionNumber(dvCommonVersion);
        if (commonVersionNum == 0) {
            return false;
        }
        LOGGER.info("Get version Finished! websiteVersionNum is {}", websiteVersionNum);
        LOGGER.info("Get version Finished! commonVersionNum is {}", commonVersionNum);
        if (commonVersionNum >= DVR022C20_VERSION) { // R022C20及以后的版本不需要考虑单套件升级的菜单问题
            LOGGER.info("DV Version is {}, No menu needs to be added.", commonVersionNum);
            return false;
        }
        // DVCommon和website的版本号不一致，表示进行了单套件升级
        return commonVersionNum != websiteVersionNum;
    }

    private static int getVersionNumber(String fullVersion) {
        int result = 0;
        String[] versionArr = fullVersion.split("\\.");
        if (versionArr.length < 2) {
            LOGGER.error("Handle fullVersion failed! fullVersion is {}", fullVersion);
            return result;
        }
        String versionNumber = versionArr[0] + versionArr[1];
        try {
            result = Integer.parseInt(versionNumber);
        } catch (NumberFormatException numberFormatException) {
            LOGGER.error("The version number contains invalid characters. fullVersion is {}", fullVersion);
        }
        return result;
    }

    /**
     * 调用febs接口，添加菜单
     *
     * @param menuJsonString 菜单配置项
     */
    private static void addFebsMenu(String menuJsonString) {
        LOGGER.info("Start to patch add menu!");
        LOGGER.debug("menuJsonString is [{}]", menuJsonString);
        RestfulParametes parameters = new RestfulParametes();
        parameters.setRawData(menuJsonString);
        try {
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_PATCH, FEBS_MENU_URL,
                parameters, null);
            // 如果febs服务返回异常，重试60次，每次等待10秒，共计10分钟
            int count = 0;
            while ((response == null || response.getStatus() != RestConstant.STATUS_OK) && count < 60) {
                response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_PATCH, FEBS_MENU_URL,
                    parameters, null);

                count++;
                Thread.sleep(10000);
            }
            if(count == 60) {
                LOGGER.error("Patch Add Febs Menu Failed! response is {}", JSON.toJSONString(response));
            }else {
                LOGGER.info("Patch Add Febs Menu Success!");
            }
        } catch (ServiceException | InterruptedException e) {
            LOGGER.error("Add menu failed.", e);
        }
        LOGGER.info("End to patch add menu!");
    }

    private static String readFebsJsonFile(String fileName) {
        LOGGER.info("Start to read file!");
        String rootPath = DefaultEnvUtil.getAppRoot();
        File jsonFile = new File(rootPath + TEMP_PATH + fileName);
        try {
            return FileUtils.readFileToString(jsonFile, "UTF-8");
        } catch (IOException e) {
            LOGGER.error("Read file failed! e is {}", e.getMessage());
            return null;
        }
    }

    /**
     * 删除菜单
     *
     * @param appId 应用ID
     */
    private static void deleteMenu(String appId) {
        LOGGER.info("Start to delete menu, appId is {}", appId);
        RestfulParametes parameters = new RestfulParametes();
        try {
            RestfulResponse response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_DELETE,
                DELETE_MENU_URL + appId, parameters, null);
            // 如果febs服务返回异常，重试60次，每次等待10秒，共计10分钟
            int count = 0;
            while ((response == null || response.getStatus() != RestConstant.STATUS_OK) && count < 60) {
                response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_DELETE,
                    DELETE_MENU_URL + appId, parameters, null);

                count++;
                Thread.sleep(10000);
            }
            if(count == 60){
                LOGGER.error("Delete Menu failed! response is {}", JSON.toJSONString(response));
            }else {
                LOGGER.debug("Delete Menu Success!");
            }
        } catch (ServiceException | InterruptedException e) {
            LOGGER.error("Delete Menu failed.", e);
        }
        LOGGER.debug("End to delete menu!");
    }

    /**
     * 根据动态配置注册菜单
     */
    private void registerMenuByConfig() {
        LOGGER.info("start to register menu by config.");
        // 删除R023C00B088前注册的菜单，B版本兼容
        for (String appId : DELETED_MENU_ID) {
            deleteMenu(appId);
        }
        // 删除所有已注册的菜单
        for (String appId : ALL_MENU_ID) {
            deleteMenu(appId);
        }

        String configStr = menuConfigHelp.getMenuConfig();
        if (StringUtils.isEmpty(configStr)) {
            LOGGER.error("Menu config is null");
            return;
        }
        LOGGER.info("Menu config is [{}]", configStr);
        String[] menuConfigArr = configStr.split(",");

        // 根据配置注册菜单
        String allMenuStr = readFebsJsonFile(ALL_MENUS_FILE_NAME);
        JSONObject allMenu = JSON.parseObject(allMenuStr);
        JSONObject allApps = (JSONObject) allMenu.get("apps");
        msMenuShield(allMenu);
        JSONObject finalApps = new JSONObject();
        for (String configKey : menuConfigArr) {
            String menuKey = configKey + MENU_KEY_SUFFIX;
            allApps.keySet().forEach(key -> {
                if (menuKey.equalsIgnoreCase(key) && finalApps.get(key) == null) { // 模糊匹配到且不是重复的
                    finalApps.put(key, allApps.get(key));
                }
            });
        }
        if (finalApps.isEmpty()) {
            LOGGER.error("No valid menu item is found.");
            return;
        }
        addFebsMenu(finalApps.toJSONString());
        LOGGER.info("end to register menu by config");
    }

    private void msMenuShield(JSONObject menu) {
        if (TaskValidChecker.isMsMod()) {
            if (Objects.nonNull(menu)) {
                menu = menu.getJSONObject("apps");
            }

            if (Objects.nonNull(menu)) {
                menu = menu.getJSONObject("capacityConfig_cf");
            }

            if (Objects.nonNull(menu)) {
                menu = menu.getJSONObject("actions");
            }
            if (Objects.nonNull(menu)) {
                menu.remove("knowledgeManagement");
            }
        }
    }

    private void waitFebsAndRefreshMenus() throws InterruptedException {
        // 如果febs服务异常，轮询90次，每次间隔20秒，共计半小时
        int waitCount = 0;
        boolean febsOk = false;
        RestfulParametes parameters = new RestfulParametes();
        RestfulResponse response = new RestfulResponse();
        while (waitCount < 120) {
            try {
                response = RestUtil.sendRestRequest(RestConstant.RESTFUL_METHOD_GET, GET_MENU_URL, parameters, null);
            } catch (ServiceException e) {
                LOGGER.error("[InitMenu] get Menu failed.", e);
            }
            if (response != null && response.getStatus() == RestConstant.STATUS_OK) {
                febsOk = true;
                break;
            }
            waitCount++;
            Thread.sleep(60000);
        }
        if(waitCount == 120){
            LOGGER.error("[InitMenu] febs service is abnormal, wait count = {}.", waitCount);
        }
        if (!febsOk) {
            LOGGER.error("[InitMenu] febs service is abnormal for 120 min, get menu failed.");
            return;
        }
        LOGGER.info("[InitMenu] febs service is ok, get Menu start.");

        // 确认febs服务正常，开始处理菜单
        if (isNeedRegisterMenu()) {
            LOGGER.info("[InitMenu] start to add menu!");
            String paramJsonStr = readFebsJsonFile(FEBS_JSON_FILE_NAME);
            if (StringUtils.isNotEmpty(paramJsonStr)) {
                addFebsMenu(paramJsonStr);
            } else {
                LOGGER.error("[InitMenu] febs.json content is empty!");
            }
        }
        registerMenuByConfig();
    }
}
