<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
  -->

<param_validators>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/multipleanalysis/getcapacitypredictiontask"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="mainKpiValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="mainTimeRange" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="capacityOccurTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="922337203685475807"/>
        </parameter>
        <parameter name="minutesRemainingStatus" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="currentTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="922337203685475807"/>
        </parameter>
        <parameter name="queryType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="relationTree" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="progressing" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="queryHistoryId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="predictReverse.id" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="predictReverse.timeStamp" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="922337203685475807"/>
        </parameter>
        <parameter name="predictReverse.value" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="predictReverse.capacityOccurTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="922337203685475807"/>
        </parameter>
        <parameter name="timeStampList.id" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="timeStampList.timeStamp" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="922337203685475807"/>
        </parameter>
        <parameter name="timeStampList.predictValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="timeStampList.capacityOccurTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="922337203685475807"/>
        </parameter>
        <parameter name="timeStampList.mainKpiValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="timeStampList.mainTimeRange" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="timeStampList.predictStatus" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/multipleanalysis/querycapacitynodeindicatorinfo"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="id" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="nodeId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="queryType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="REGEXP_VALIDATOR" rule="0|1"/>
        </parameter>
        <parameter name="paging.pageSize" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="paging.pageNumber" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="paging.sortField" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128"/>
        </parameter>
        <parameter name="paging.sortType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128"/>
        </parameter>
        <parameter name="paging.offset" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/multipleanalysis/querycapacitytasklist"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="get" dataPattern="form" allParameterCheck="true">
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/multipleanalysis/setcapacitydefaulttask"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false" />
            <validator name="DIGITS_VALIDATOR" />
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/multipleanalysis/checkcapacitytaskmodel"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="relationTree" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="3000000"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/multipleanalysis/capacitytaskretrain"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false" />
            <validator name="DIGITS_VALIDATOR" />
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="nodeId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/multipleanalysis/querycanassociatepredicttask"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="indicatorType" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
        <parameter name="indicatorList" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="indicatorList.targetExecutionUId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.targetNodeId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.nodeId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.nodeUid" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.indicatorId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.taskId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="DIGITS_VALIDATOR" />
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorList.abnormal" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="DIGITS_VALIDATOR" />
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorList.dn" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.moType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.measUnitKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.measUnitName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.measTypeKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.indexName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.displayValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.originalValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.unit" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.dnName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.checkedNetId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.hasMeasObj" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.indexId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.resourceTypeKey" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.upperThreshold" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.lowerThreshold" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.kpiValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.predictionUpperThreshold" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.predictionLowerThreshold" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.predictionKpiValue" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.predictionTimeRange" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.resultType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="DIGITS_VALIDATOR" />
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="indicatorList.isRoot" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="indicatorList.podName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true" />
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="255"/>
        </parameter>
        <parameter name="paging" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="paging.pageSize" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="paging.pageNumber" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
        </parameter>
        <parameter name="paging.sortField" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="20"/>
            <validator name="REGEXP_VALIDATOR" rule="^[A-Za-z0-9_]*$" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="paging.sortType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="4"/>
            <validator name="REGEXP_VALIDATOR" rule="asc|desc" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/multipleanalysis/querycapacitypredictresultlist"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false" />
            <validator name="DIGITS_VALIDATOR" />
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="nodeId" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
        </parameter>
    </param_validator>
</param_validators>