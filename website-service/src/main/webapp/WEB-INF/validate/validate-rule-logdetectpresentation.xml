<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
  -->
<param_validators>
    <param_validator url="/rest/dvanalysisenginewebsite/v1/logdetectanalysisresult/getlogdetectinfolist"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="situationIds" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="logStatusList" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="1"/>
        </parameter>
        <parameter name="dataSourceType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64"/>
        </parameter>
        <parameter name="fileName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
        </parameter>
        <parameter name="filePath" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64"/>
        </parameter>
        <parameter name="indexName" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="600"/>
        </parameter>
        <parameter name="timeRangeRadioId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="4"/>
        </parameter>
        <parameter name="customTimes" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="922337203685475807" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageIndex" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="pageSize" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/logdetectanalysisresult/getlogdetectabnormallist"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="dataSourceType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64"/>
        </parameter>
        <parameter name="situationIds" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="timeRangeRadioId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="4"/>
        </parameter>
        <parameter name="customTimes" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="922337203685475807" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
        <parameter name="pageIndex" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="pageSize" required="false">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/logdetectanalysisresult/getlogdetectpolyline"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="dataSourceType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64"/>
        </parameter>
        <parameter name="situationIds" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="timeRangeRadioId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="4"/>
        </parameter>
        <parameter name="customTimes" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR" errormessage="WEB.VALIDATOR.PARAM"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="922337203685475807" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/logdetectanalysisresult/getlogdetecttemplates"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="dataSourceType" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="64"/>
        </parameter>
        <parameter name="collectTime" required="false">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="922337203685475807"/>
        </parameter>
        <parameter name="pageIndex" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="pageSize" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/logdetectanalysisresult/marklogdetectabnormal"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="json" allParameterCheck="true">
        <parameter name="taskId" required="true">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="2147483647"/>
        </parameter>
        <parameter name="isCorrect" required="true">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="DIGITS_VALIDATOR"/>
            <validator name="RANGE_VALIDATOR" minValue="0" maxValue="4"/>
        </parameter>
        <parameter name="ids" required="true">
            <validator name="EMPTY_VALIDATOR" empty="true"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="128"/>
        </parameter>
    </param_validator>

    <param_validator url="/rest/dvanalysisenginewebsite/v1/logdetectanalysisresult/reportabnormalalarm"
                     errorHandler="com.huawei.i2000.dvanalysisenginewebsite.validate.handler.ParameterValidateErrorHandler"
                     method="post" dataPattern="form" allParameterCheck="true">
        <parameter name="id" required="true">
            <validator name="EMPTY_VALIDATOR" empty="false"/>
            <validator name="MAX_LENGTH_VALIDATOR" maxLength="256"/>
            <validator name="REGEXP_VALIDATOR" rule="[a-zA-Z0-9-]*" caseSensitive="true" errormessage="WEB.VALIDATOR.PARAM"/>
        </parameter>
    </param_validator>

</param_validators>