/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

package com.huawei.cmp.callchain.flume.server.om.plugin;

import com.huawei.bsp.commonlib.hofs.HofsException;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.cmp.callchain.flume.server.om.performance.MetricsConstant;
import com.huawei.cmp.foundation.configuration.util.SystemConfigUtil;
import com.huawei.cmp.foundation.hofs.DVHofsUtil;
import com.huawei.cmp.foundation.log.channel.monitor.util.JMXUtil;
import com.huawei.i2000.cbb.common.ContainerUtil;

import com.alibaba.fastjson2.JSONObject;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.File;
import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 流式处理性能上报实现类
 *
 * <AUTHOR>
 * @since 2021-09-01
 */
public class PerformenceReport implements Runnable {
    private static final OssLog LOGGER = OssLogFactory.getLogger(PerformenceReport.class);

    private static final String STREAM_CHANNEL_PROPERTIES = "/conf/streamchannel.properties";

    private static final String LOCAL_IP = Boolean.parseBoolean(System.getenv("CONTAINER"))
        ? ContainerUtil.getHostName()
        : SystemConfigUtil.getIPAddr();

    private static final String REMOTE_STREAM_CHANNEL_PATH = "/channnel/logmatrixStreaming_" + LOCAL_IP + ".properties";

    private static final String TOTAL_RATE_PROPERTIES = "/conf/totalRate.properties";

    private static final String REMOTE_TOTAL_RATE_PATH = "/totalRate/logmatrixStreaming_" + LOCAL_IP + ".properties";

    private static final String NODE_IP = "logmatrixStreaming-" + LOCAL_IP;

    private static final String TIME = "time";

    private static final String EVENTPUTSUCCESSPERCENTAGE = "EventPutSuccessPercentage";

    private static final String CHANNELCAPACITY = "ChannelCapacity";

    private static final String CHANNELFILLPERCENTAGE = "ChannelFillPercentage";

    private static final String CHANNELSIZE = "ChannelSize";

    private static final String EVENTTAKESUCCESSCOUNT = "EventTakeSuccessCount";

    private static final String EVENTTAKEATTEMPTCOUNT = "EventTakeAttemptCount";

    private static final String EVENTPUTATTEMPTCOUNT = "EventPutAttemptCount";

    private static final String EVENTPUTSUCCESCOUNT = "EventPutSuccessCount";

    private static final String MEASTYPE_KEY_EVENTPUTSUCCESSPERCENTAGE = "hwEventPutSuccessPercentage";

    private static final String MEASTYPE_KEY_CHANNELCAPACITY = "hwChannelCapacity";

    private static final String MEASTYPE_KEY_CHANNELFILLPERCENTAGE = "hwChannelFillPercentage";

    private static final String MEASTYPE_KEY_CHANNELSIZE = "hwChannelSize";

    private static final String MEASTYPE_KEY_EVENTTAKESUCCESSCOUNT = "hwEventTakeSuccessCount";

    private static final String MEASTYPE_KEY_EVENTTAKEATTEMPTCOUNT = "hwEventTakeAttemptCount";

    private static final String MEASTYPE_KEY_EVENTPUTATTEMPTCOUNT = "hwEventPutAttemptCount";

    private static final String MEASTYPE_KEY_EVENTPUTSUCCESCOUNT = "hwEventPutSuccessCount";

    private static final String ES_SUFFIX = "-es";

    private static final String FILE_SUFFIX = "-file";

    private static final String RATE_ES = "rate-es";

    private static final String RATE_FILE = "rate-file";

    private static final String CHARSET_NAME = "UTF-8";

    private static final String TEMP_DIR_KEY = "uee.upload.dir.tmp";

    private static final String BUCKET_NAME_KEY = "SERVICE_BUCKET_NAME";

    private static final String EVENT_SUCCESS_COUNT_KEY = "EventDrainSuccessCount";

    private static final String SAMPLING_AGENT_COST_KEY = "SamplingAgentCost";

    private static final String SAMPLING_STREAMING_COST_KEY = "SamplingStreamingCost";

    private static final String MEAS_TYPE_KEY_DATA_FLOW_RATE = "dataFlowRate";

    private static final String MEAS_TYPE_KEY_AGENT_COST = "agentCost";

    private static final String MEAS_TYPE_KEY_STREAMING_COST = "streamingCost";

    private static Map<String, Long> lateRateMap = new HashMap<>();

    /**
     * key: 数据源的channel/sink名称
     * value: 日志采集时延, 流式处理时延, EventDrainSuccessCount
     */
    private static Map<String, List<Long>> logLatencyMap = new HashMap<>();

    private static final int MILLION_TO_SECOND = 1000;

    private long lastTimeStamp;

    @Override
    public void run() {
        LOGGER.info("Start to import properties to remote");
        getTotalRate();
        getStreamChannelInfo();
        LOGGER.info("End to import properties to remote");
    }

    /**
     * 获取流式处理Channel统计，输出到远端共享目录文件下
     */
    public void getStreamChannelInfo() {
        try {
            Map<String, Map<String, String>> metricMap = JMXUtil.getAllMBeans();
            Map<String, Object> map = new HashMap<>();
            if (metricMap.isEmpty()) {
                LOGGER.warn("metricMap is empty");
            }
            Iterator<Map.Entry<String, Map<String, String>>> it = metricMap.entrySet().iterator();
            while (it.hasNext()) {
                Map.Entry<String, Map<String, String>> entry = it.next();
                Map<String, Object> measType = new HashMap<>();
                String[] type = StringUtils.split(entry.getKey(), MetricsConstant.StringOperation.DOT_SPLIT);
                String name = NODE_IP + "-" + type[1];
                if (type.length == 0) {
                    continue;
                }
                if (!(MetricsConstant.Key.CHANNEL_BIG.equals(type[MetricsConstant.Metrics.ID.getIndex()]))) {
                    it.remove();
                } else {
                    long eventPutSuccessCount = Long.parseLong(
                        entry.getValue().get(MetricsConstant.Key.EVENTPUTSUCCESCOUNT));
                    long eventPutAttemptCount = Long.parseLong(
                        entry.getValue().get(MetricsConstant.Key.EVENTPUTATTEMPTCOUNT));
                    float eventPutSuccessPercentage;
                    if (eventPutAttemptCount == 0 || eventPutSuccessCount == 0) {
                        eventPutSuccessPercentage = 0.0f;
                    } else {
                        eventPutSuccessPercentage = (eventPutSuccessCount * 100) / eventPutAttemptCount;
                    }
                    entry.getValue().put(EVENTPUTSUCCESSPERCENTAGE, String.valueOf(eventPutSuccessPercentage));
                    getMeasTypeInfo(measType, entry, type[MetricsConstant.Metrics.NAME.getIndex()]);
                    map.put(name, new JSONObject(measType));
                }
            }
            map.put(TIME, String.valueOf(System.currentTimeMillis()));
            String localPath = SystemConfigUtil.getProperty(TEMP_DIR_KEY) + STREAM_CHANNEL_PROPERTIES;
            String remotePath = SystemConfigUtil.getProperty(BUCKET_NAME_KEY) + REMOTE_STREAM_CHANNEL_PATH;
            File localFile = new File(localPath);
            FileUtils.write(localFile, new JSONObject(map).toJSONString(), CHARSET_NAME);
            DVHofsUtil.pushFile(localPath, remotePath);
        } catch (Exception e) {
            LOGGER.error("push file to remote failed. {}", e.getMessage());
        }
    }

    /**
     * 获取总速率
     */
    public void getTotalRate() {
        try {
            Map<String, Map<String, String>> metricMap = JMXUtil.getAllMBeans();

            if (metricMap.isEmpty()) {
                LOGGER.warn("metricMap is empty");
            }
            Iterator<Map.Entry<String, Map<String, String>>> it = metricMap.entrySet().iterator();
            long curEventDrainSuccessCountEs = 0L;
            long curEventDrainSuccessCountFile = 0L;

            // 日志采集时延受采集包时延开关影响，可能没有值；es流式处理时延一定有值
            List<List<Long>> agentAndStreamingCostEsList = new ArrayList<>();
            List<Long> streamingCostEsList = new ArrayList<>();

            while (it.hasNext()) {
                Map.Entry<String, Map<String, String>> entry = it.next();
                String[] type = StringUtils.split(entry.getKey(), MetricsConstant.StringOperation.DOT_SPLIT);
                if (!(MetricsConstant.Key.SINK_BIG.equals(type[MetricsConstant.Metrics.ID.getIndex()]))) {
                    continue;
                }
                if (StringUtils.endsWith(type[MetricsConstant.Metrics.NAME.getIndex()], FILE_SUFFIX)) {
                    curEventDrainSuccessCountFile += Long.parseLong(entry.getValue().get(EVENT_SUCCESS_COUNT_KEY));
                } else {
                    long sinkEventDrainSuccessCountEs = Long.parseLong(entry.getValue().get(EVENT_SUCCESS_COUNT_KEY));
                    curEventDrainSuccessCountEs += sinkEventDrainSuccessCountEs;

                    long sinkAgentCostEs = Long.parseLong(entry.getValue().get(SAMPLING_AGENT_COST_KEY));
                    long sinkStreamingCostEs = Long.parseLong(entry.getValue().get(SAMPLING_STREAMING_COST_KEY));
                    String channelName = updateLogLatencyCost(type[MetricsConstant.Metrics.NAME.getIndex()],
                        sinkAgentCostEs, sinkStreamingCostEs, sinkEventDrainSuccessCountEs);
                    if (StringUtils.isEmpty(channelName)
                        || logLatencyMap.get(channelName).get(0) < 0 && logLatencyMap.get(channelName).get(1) < 0) {
                        continue;
                    }
                    if (sinkAgentCostEs > 0 && sinkStreamingCostEs > 0) {
                        agentAndStreamingCostEsList.add(Arrays.asList(sinkAgentCostEs, sinkStreamingCostEs));
                    } else if (sinkStreamingCostEs > 0) {
                        streamingCostEsList.add(sinkStreamingCostEs);
                    }
                }
            }
            calculateRate(curEventDrainSuccessCountEs, curEventDrainSuccessCountFile, agentAndStreamingCostEsList,
                streamingCostEsList);
        } catch (Exception e) {
            LOGGER.error("push file to remote failed. {}", e.getMessage());
        }
    }

    private String updateLogLatencyCost(String name, long sinkAgentCostEs, long sinkStreamingCostEs, long sinkEventDrainSuccessCountEs) {
        // 正在采集的日志streaming时延总是有值的
        if (sinkStreamingCostEs <= 0) {
            return null;
        }
        String channelName;
        int lastIndex = name.lastIndexOf("sink");
        if (lastIndex != -1) {
            channelName = name.substring(0, lastIndex) + "channel" + name.substring(lastIndex + "sink".length());
        } else {
            return null;
        }
        if (!logLatencyMap.containsKey(channelName)) {
            logLatencyMap.put(channelName, new ArrayList<>(Arrays.asList(sinkAgentCostEs, sinkStreamingCostEs, sinkEventDrainSuccessCountEs)));
            return channelName;
        }
        // 检查时延值是否是旧数据
        if (sinkEventDrainSuccessCountEs == logLatencyMap.get(channelName).get(2)) {
            logLatencyMap.put(channelName, new ArrayList<>(Arrays.asList(-1L, -1L, sinkEventDrainSuccessCountEs)));
        } else {
            logLatencyMap.put(channelName, new ArrayList<>(Arrays.asList(sinkAgentCostEs, sinkStreamingCostEs, sinkEventDrainSuccessCountEs)));
        }
        return channelName;
    }

    private void calculateRate(long curEventDrainSuccessCountEs, long curEventDrainSuccessCountFile,
        List<List<Long>> agentAndStreamingCostEsList, List<Long> streamingCostEsList)
        throws HofsException, IOException, NoSuchAlgorithmException {
        long totalRateEs = 0L;
        long totalRateFile = 0L;
        if (lateRateMap.isEmpty()) {
            lateRateMap.put(RATE_ES, curEventDrainSuccessCountEs);
            lateRateMap.put(RATE_FILE, curEventDrainSuccessCountFile);
            lastTimeStamp = System.currentTimeMillis();
        } else {
            long period = (System.currentTimeMillis() - lastTimeStamp) / MILLION_TO_SECOND;
            lastTimeStamp = System.currentTimeMillis();
            // 总速率取前后两次sinCounter成功发送的事件累加值（EventDrainSuccessCount），相减后，除以两次时间差，获取到每秒成功采集总数
            totalRateEs = (curEventDrainSuccessCountEs - lateRateMap.get(RATE_ES)) / period;
            totalRateFile = (curEventDrainSuccessCountFile - lateRateMap.get(RATE_FILE)) / period;
            // totalRate无故小于0的情况下，置零
            if (totalRateEs < 0) {
                totalRateEs = 0;
            }
            if (totalRateFile < 0) {
                totalRateFile = 0;
            }
            lateRateMap.put(RATE_ES, curEventDrainSuccessCountEs);
            lateRateMap.put(RATE_FILE, curEventDrainSuccessCountFile);
        }

        long samplingAgentCostEs = -1L;
        long samplingStreamingCostEs = -1L;
        if (CollectionUtils.isNotEmpty(agentAndStreamingCostEsList)) {
            int index = SecureRandom.getInstanceStrong().nextInt(agentAndStreamingCostEsList.size());
            samplingAgentCostEs = agentAndStreamingCostEsList.get(index).get(0);
            samplingStreamingCostEs = agentAndStreamingCostEsList.get(index).get(1);
        } else if (CollectionUtils.isNotEmpty(streamingCostEsList)) {
            int index = SecureRandom.getInstanceStrong().nextInt(streamingCostEsList.size());
            samplingStreamingCostEs = streamingCostEsList.get(index);
        }

        writeTotalRateFile(totalRateEs, totalRateFile, samplingAgentCostEs, samplingStreamingCostEs);
    }

    private void writeTotalRateFile(long totalRateEs, long totalRateFile, long agentCostEs, long streamingCostEs)
        throws HofsException, IOException {
        Map<String, Object> measTypeEs = new HashMap<>();
        measTypeEs.put(MEAS_TYPE_KEY_DATA_FLOW_RATE, totalRateEs);
        if (agentCostEs >= 0) {
            measTypeEs.put(MEAS_TYPE_KEY_AGENT_COST, agentCostEs);
        }
        if (streamingCostEs >= 0) {
            measTypeEs.put(MEAS_TYPE_KEY_STREAMING_COST, streamingCostEs);
        }

        Map<String, Object> measTypeFile = new HashMap<>();
        measTypeFile.put(MEAS_TYPE_KEY_DATA_FLOW_RATE, totalRateFile);

        Map<String, Object> result = new HashMap<>();
        result.put(NODE_IP + ES_SUFFIX, new JSONObject(measTypeEs));
        result.put(NODE_IP + FILE_SUFFIX, new JSONObject(measTypeFile));
        result.put(TIME, String.valueOf(System.currentTimeMillis()));

        String localPath = SystemConfigUtil.getProperty(TEMP_DIR_KEY) + TOTAL_RATE_PROPERTIES;
        String remotePath = SystemConfigUtil.getProperty(BUCKET_NAME_KEY) + REMOTE_TOTAL_RATE_PATH;
        File localFile = new File(localPath);
        FileUtils.write(localFile, JSONObject.toJSONString(result), CHARSET_NAME);
        DVHofsUtil.pushFile(localPath, remotePath);
    }

    private void getMeasTypeInfo(Map<String, Object> measType, Map.Entry<String, Map<String, String>> entry, String channelName) {
        measType.put(MEASTYPE_KEY_CHANNELCAPACITY, entry.getValue().get(CHANNELCAPACITY));
        measType.put(MEASTYPE_KEY_CHANNELFILLPERCENTAGE, entry.getValue().get(CHANNELFILLPERCENTAGE));
        measType.put(MEASTYPE_KEY_CHANNELSIZE, entry.getValue().get(CHANNELSIZE));
        measType.put(MEASTYPE_KEY_EVENTTAKESUCCESSCOUNT, entry.getValue().get(EVENTTAKESUCCESSCOUNT));
        measType.put(MEASTYPE_KEY_EVENTTAKEATTEMPTCOUNT, entry.getValue().get(EVENTTAKEATTEMPTCOUNT));
        measType.put(MEASTYPE_KEY_EVENTPUTATTEMPTCOUNT, entry.getValue().get(EVENTPUTATTEMPTCOUNT));
        measType.put(MEASTYPE_KEY_EVENTPUTSUCCESCOUNT, entry.getValue().get(EVENTPUTSUCCESCOUNT));
        measType.put(MEASTYPE_KEY_EVENTPUTSUCCESSPERCENTAGE, entry.getValue().get(EVENTPUTSUCCESSPERCENTAGE));

        if (logLatencyMap.containsKey(channelName)) {
            if (logLatencyMap.get(channelName).get(0) > 0) {
                measType.put(SAMPLING_AGENT_COST_KEY, String.valueOf(logLatencyMap.get(channelName).get(0)));
            }
            if (logLatencyMap.get(channelName).get(1) > 0) {
                measType.put(SAMPLING_STREAMING_COST_KEY, String.valueOf(logLatencyMap.get(channelName).get(1)));
            }
        }
    }
}
