/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 */

package com.huawei.cmp.callchain.flume.server.om.cert.task;

import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.cmp.callchain.flume.server.om.cert.model.CertResult;
import com.huawei.cmp.callchain.flume.server.om.cert.model.CertUpdateResult;
import com.huawei.cmp.callchain.flume.server.om.cert.model.ServiceCertUpdateResult;
import com.huawei.cmp.callchain.flume.server.om.cert.model.ZkClient;
import com.huawei.cmp.callchain.flume.server.om.certcheck.RestUtils;
import com.huawei.cmp.callchain.flume.server.om.plugin.Plugin;
import com.huawei.cmp.utils.CollectionUtils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.TypeReference;

import org.apache.commons.lang3.StringUtils;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.utils.ZKPaths;
import org.apache.zookeeper.data.Stat;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 证书管理类
 *
 * <AUTHOR>
 * @description 证书管理类
 * @since 2021-08-28
 */
public class CertManager implements Plugin {
    private final static OssLog LOGGER = OssLogFactory.getLogger(CertManager.class);

    private static final String LOCAL_IP = CertConstant.LOCAL_IP;

    private static final String UPDATE_URL_RESULT = "/rest/dvcertmgmtservice/v1/cert/report-update-cert-result";

    private static final String TASK_STATUS = "taskStatus";

    private static final String TASK_ID = "taskId";

    private static final String SERVICE_ID_LIST = "serviceIds";

    private ExecutorService executor;

    @Override
    public void start() throws ServiceException {
        LOGGER.info("start to check cert manager");
        // 开启证书管理任务前，需要获取zk中的任务状态的值，上报给证书管理服务
        reportCertTaskStatus();

        executor = Executors.newSingleThreadExecutor();
        try {
            executor.submit(new CertRegisterTask());
        } catch (Exception e) {
            LOGGER.error("executor.submit failed");
        }
        executor.shutdown();
        LOGGER.info("end to check cert manager");
    }

    private static void reportCertTaskStatus() {
        LOGGER.info("start to reportCertTaskStatus");
        CuratorFramework zkClient = ZkClient.getInstance();
        try {
            Stat stat = zkClient.checkExists().forPath(CertConstant.CERT_TASK_PATH);
            if (stat == null) {
                return;
            }
            List<String> nodes = zkClient.getChildren().forPath(CertConstant.CERT_TASK_PATH);
            for (String node : nodes) {
                if (LOCAL_IP.equals(node)) {
                    byte[] bytes = zkClient.getData().forPath(ZKPaths.makePath(CertConstant.CERT_TASK_PATH, node));
                    Map<String, String> map = JSON.parseObject(new String(bytes, StandardCharsets.UTF_8),
                        new TypeReference<Map<String, String>>() { });
                    String status = map.get(TASK_STATUS);
                    String taskId = map.get(TASK_ID);
                    String serviceIdStr = map.get(SERVICE_ID_LIST);
                    List<String> serviceIds = new ArrayList<>();
                    if (StringUtils.isNotEmpty(serviceIdStr)) {
                        JSONArray jsonArray = JSONArray.parseArray(serviceIdStr);
                        serviceIds = jsonArray.toList(String.class);
                    }
                    if (CollectionUtils.isNotEmpty(serviceIds)) {
                        reportUpdateResult(status, taskId, serviceIds);
                        LOGGER.info("End to reportCertTaskStatus");
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("Report Update Cert Result failed, {}", e);
        } finally {
            try {
                String nodePath = ZKPaths.makePath(CertConstant.CERT_TASK_PATH, LOCAL_IP);
                Stat stat = zkClient.checkExists().forPath(nodePath);
                // 删除zk中的状态信息
                if (stat != null) {
                    zkClient.delete().deletingChildrenIfNeeded().forPath(nodePath);
                }
            } catch (Exception e) {
                LOGGER.error("Delete task status in zk failed, {}", e);
            }
        }
    }

    private static void reportUpdateResult(String status, String taskId, List<String> serviceIds) {
        LOGGER.info("start to reportUpdateResult");
        CertUpdateResult updateResult = new CertUpdateResult();
        updateResult.setNodeIP(LOCAL_IP);
        updateResult.setTaskId(taskId);
        CertResult result = new CertResult();
        result.setReturnCode(Boolean.valueOf(status) ? 0 : 1);
        List<ServiceCertUpdateResult> serviceCertUpdateResultList = new ArrayList<>();
        for (String serviceId : serviceIds) {
            ServiceCertUpdateResult serviceUpdateResult = new ServiceCertUpdateResult();
            serviceUpdateResult.setServiceId(serviceId);
            serviceUpdateResult.setResult(result);
            serviceCertUpdateResultList.add(serviceUpdateResult);
        }

        updateResult.setServiceCertUpdateResultList(serviceCertUpdateResultList);
        try {
            RestUtils.sentRestRequest(CertConstant.POST, UPDATE_URL_RESULT,
                JSON.toJSONString(updateResult));
            LOGGER.info("End to reportUpdateResult");
        } catch (ServiceException e) {
            LOGGER.error("Report cert update result fail, {}", e);
        }
    }
}
