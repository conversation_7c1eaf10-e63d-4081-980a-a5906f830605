 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class ServiceInfo     {
    
    
    @Valid
    
    @JsonProperty("serviceName")
    private String serviceName = null;
    
    @Valid
    
    @JsonProperty("serviceVersion")
    private String serviceVersion = null;
    
    @Valid
    
    @JsonProperty("serviceOwner")
    private String serviceOwner = null;
    
     
    /**
    **/
    @JsonProperty("serviceName")
    public String getServiceName() {
        return serviceName;
    }
    
    
    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }
    
    
    /**
    **/
    @JsonProperty("serviceVersion")
    public String getServiceVersion() {
        return serviceVersion;
    }
    
    
    public void setServiceVersion(String serviceVersion) {
        this.serviceVersion = serviceVersion;
    }
    
    
    /**
    **/
    @JsonProperty("serviceOwner")
    public String getServiceOwner() {
        return serviceOwner;
    }
    
    
    public void setServiceOwner(String serviceOwner) {
        this.serviceOwner = serviceOwner;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class ServiceInfo {\n");
        
        sb.append("  serviceName: ").append(serviceName).append("\n");
        sb.append("  serviceVersion: ").append(serviceVersion).append("\n");
        sb.append("  serviceOwner: ").append(serviceOwner).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

