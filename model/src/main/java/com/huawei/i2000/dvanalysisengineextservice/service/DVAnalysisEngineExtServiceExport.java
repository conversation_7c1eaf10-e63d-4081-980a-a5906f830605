package com.huawei.i2000.dvanalysisengineextservice.service;

import com.huawei.i2000.dvanalysisengineextservice.delegate.DVAnalysisEngineExtServiceExportDelegate;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;


import java.util.*;

import javax.validation.constraints.*;
import javax.validation.Valid;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.annotation.*;



import com.huawei.bsp.roa.common.HttpContext;



@Service("com.huawei.i2000.dvanalysisengineextservice.service.DVAnalysisEngineExtServiceExport")
@Path("/dvanalysisengineextservice/v1/export/managedobject/all")
@Target("DVAnalysisEngineExtServiceExport")


public class DVAnalysisEngineExtServiceExport {

    @Autowired
    private DVAnalysisEngineExtServiceExportDelegate delegate;


    
    @GET
    @Consumes({ "application/json" })
    public ResponseResult exportManagedObject(HttpContext context,   @QueryParam("connectStatus")String connectStatus)
        throws ServiceException {
        return    delegate.exportManagedObject(context, connectStatus);
    }


}


