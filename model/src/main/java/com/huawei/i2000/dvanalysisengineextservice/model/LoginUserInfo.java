 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import java.util.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class LoginUserInfo     {
    
    
    @Valid
    
    @JsonProperty("resourceGroupIds")
    private List<String> resourceGroupIds = new ArrayList<String>();
    
    @Valid
    
    @JsonProperty("roleIdList")
    private List<Integer> roleIdList = new ArrayList<Integer>();
    
    @Valid
    
    @JsonProperty("userId")
    private String userId = null;
    
    @Valid
    
    @JsonProperty("userName")
    private String userName = null;
    
    @Valid
    
    @JsonProperty("admin")
    private Boolean admin = null;
    
    @Valid
    
    @JsonProperty("needDomain")
    private Boolean needDomain = null;
    
     
    /**
    **/
    @JsonProperty("resourceGroupIds")
    public List<String> getResourceGroupIds() {
        return resourceGroupIds;
    }
    
    
    public void setResourceGroupIds(List<String> resourceGroupIds) {
        this.resourceGroupIds = resourceGroupIds;
    }
    
    
    /**
    **/
    @JsonProperty("roleIdList")
    public List<Integer> getRoleIdList() {
        return roleIdList;
    }
    
    
    public void setRoleIdList(List<Integer> roleIdList) {
        this.roleIdList = roleIdList;
    }
    
    
    /**
     * 用户id
    **/
    @JsonProperty("userId")
    public String getUserId() {
        return userId;
    }
    
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    
    /**
     * 用户名称
    **/
    @JsonProperty("userName")
    public String getUserName() {
        return userName;
    }
    
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
    
    
    /**
    **/
    @JsonProperty("admin")
    public Boolean getAdmin() {
        return admin;
    }
    
    
    public void setAdmin(Boolean admin) {
        this.admin = admin;
    }
    
    
    /**
    **/
    @JsonProperty("needDomain")
    public Boolean getNeedDomain() {
        return needDomain;
    }
    
    
    public void setNeedDomain(Boolean needDomain) {
        this.needDomain = needDomain;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class LoginUserInfo {\n");
        
        sb.append("  resourceGroupIds: ").append(resourceGroupIds).append("\n");
        sb.append("  roleIdList: ").append(roleIdList).append("\n");
        sb.append("  userId: ").append(userId).append("\n");
        sb.append("  userName: ").append(userName).append("\n");
        sb.append("  admin: ").append(admin).append("\n");
        sb.append("  needDomain: ").append(needDomain).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

