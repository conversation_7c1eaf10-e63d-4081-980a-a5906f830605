package com.huawei.i2000.dvanalysisengineextservice.service;

import com.huawei.i2000.dvanalysisengineextservice.delegate.KeywordAssociationDelegate;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import com.huawei.i2000.dvanalysisengineextservice.model.RecommendRsp;
import com.huawei.i2000.dvanalysisengineextservice.model.RecommendReq;


import java.util.*;

import javax.validation.constraints.*;
import javax.validation.Valid;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.annotation.*;



import com.huawei.bsp.roa.common.HttpContext;



@Service("com.huawei.i2000.dvanalysisengineextservice.service.KeywordAssociation")
@Path("/dvanalysisengineextservice/v1/customization/get/recommend")
@Target("KeywordAssociation")


public class KeywordAssociation {

    @Autowired
    private KeywordAssociationDelegate delegate;


    
    @POST
    @Consumes({ "application/json" })
    @Produces({ "application/json" })
    public RecommendRsp recommend(HttpContext context, @Valid   @NotNull() RecommendReq req)
        throws ServiceException {
        return    delegate.recommend(context, req);
    }


}


