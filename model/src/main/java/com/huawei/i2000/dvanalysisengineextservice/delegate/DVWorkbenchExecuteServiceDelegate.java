package com.huawei.i2000.dvanalysisengineextservice.delegate;

import com.huawei.i2000.dvanalysisengineextservice.service.*;

import com.huawei.i2000.dvanalysisengineextservice.model.ExecuteRequest;
import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;


import java.util.*;

import com.huawei.bsp.remoteservice.exception.ServiceException;

import com.huawei.bsp.roa.common.HttpContext;



public interface DVWorkbenchExecuteServiceDelegate {
  
    public abstract ResponseResult executeScript(HttpContext context, ExecuteRequest executeParam)
      throws ServiceException;
  
}

