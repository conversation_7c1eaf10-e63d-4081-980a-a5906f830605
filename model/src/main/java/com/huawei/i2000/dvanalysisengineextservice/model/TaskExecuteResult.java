 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import java.util.HashMap;
import java.util.Map;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class TaskExecuteResult     {
    
    
    public enum StateEnum { 
    
    
         @JsonProperty("IN_PROGRESS") IN_PROGRESS,  @JsonProperty("SUCCESS") SUCCESS,  @JsonProperty("FAILED") FAILED,  @JsonProperty("TIMEOUT") TIMEOUT,  @JsonProperty("PART_SUCCESS") PART_SUCCESS, ; 
        
        
    };
    
    
    @JsonProperty("state")
    private StateEnum state = null;
    
    @Valid
    
    @JsonProperty("hostStateMap")
    private Map<String, String> hostStateMap = new HashMap<String, String>();
    
    @Valid
    
    @JsonProperty("startTime")
    private Long startTime = null;
    
    @Valid
    
    @JsonProperty("endTime")
    private Long endTime = null;
    
     
    /**
    **/
    @JsonProperty("state")
    public StateEnum getState() {
        return state;
    }
    
    
    public void setState(StateEnum state) {
        this.state = state;
    }
    
    
    /**
    **/
    @JsonProperty("hostStateMap")
    public Map<String, String> getHostStateMap() {
        return hostStateMap;
    }
    
    
    public void setHostStateMap(Map<String, String> hostStateMap) {
        this.hostStateMap = hostStateMap;
    }
    
    
    /**
    **/
    @JsonProperty("startTime")
    public Long getStartTime() {
        return startTime;
    }
    
    
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }
    
    
    /**
    **/
    @JsonProperty("endTime")
    public Long getEndTime() {
        return endTime;
    }
    
    
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class TaskExecuteResult {\n");
        
        sb.append("  state: ").append(state).append("\n");
        sb.append("  hostStateMap: ").append(hostStateMap).append("\n");
        sb.append("  startTime: ").append(startTime).append("\n");
        sb.append("  endTime: ").append(endTime).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

