 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class WorkflowExecuteRequest     {
    
    
    @Size(max = 1024, min = 0)
    @Valid
    
    @JsonProperty("scene")
    private String scene = null;
    
    @Size(max = 1024, min = 0)
    @Valid
    
    @JsonProperty("faultObject")
    private String faultObject = null;
    
    @Size(max = 1024, min = 0)
    @Valid
    
    @JsonProperty("occurUtc")
    private String occurUtc = null;
    
    @Size(max = 1024, min = 0)
    @Valid
    
    @JsonProperty("csn")
    private String csn = null;
    
    @Size(max = 1024, min = 0)
    @Valid
    
    @JsonProperty("address")
    private String address = null;
    
     
    /**
     * minLength: 0
     * maxLength: 1024
    **/
    @JsonProperty("scene")
    public String getScene() {
        return scene;
    }
    
    
    public void setScene(String scene) {
        this.scene = scene;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 1024
    **/
    @JsonProperty("faultObject")
    public String getFaultObject() {
        return faultObject;
    }
    
    
    public void setFaultObject(String faultObject) {
        this.faultObject = faultObject;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 1024
    **/
    @JsonProperty("occurUtc")
    public String getOccurUtc() {
        return occurUtc;
    }
    
    
    public void setOccurUtc(String occurUtc) {
        this.occurUtc = occurUtc;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 1024
    **/
    @JsonProperty("csn")
    public String getCsn() {
        return csn;
    }
    
    
    public void setCsn(String csn) {
        this.csn = csn;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 1024
    **/
    @JsonProperty("address")
    public String getAddress() {
        return address;
    }
    
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class WorkflowExecuteRequest {\n");
        
        sb.append("  scene: ").append(scene).append("\n");
        sb.append("  faultObject: ").append(faultObject).append("\n");
        sb.append("  occurUtc: ").append(occurUtc).append("\n");
        sb.append("  csn: ").append(csn).append("\n");
        sb.append("  address: ").append(address).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

