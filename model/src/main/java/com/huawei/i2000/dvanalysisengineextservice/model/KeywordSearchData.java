 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import com.huawei.i2000.dvanalysisengineextservice.model.KeywordItem;
import java.util.*;

/**
 * 关键词查询数据
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class KeywordSearchData     {
    
    
    @Valid
    
    @JsonProperty("keywords")
    private List<KeywordItem> keywords = new ArrayList<KeywordItem>();
    
    @Valid
    
    @JsonProperty("total")
    private Integer total = null;
    
    @Valid
    
    @JsonProperty("keywordType")
    private String keywordType = null;
    
     
    /**
     * 匹配的关键词列表
    **/
    @JsonProperty("keywords")
    public List<KeywordItem> getKeywords() {
        return keywords;
    }
    
    
    public void setKeywords(List<KeywordItem> keywords) {
        this.keywords = keywords;
    }
    
    
    /**
     * 总匹配数量
    **/
    @JsonProperty("total")
    public Integer getTotal() {
        return total;
    }
    
    
    public void setTotal(Integer total) {
        this.total = total;
    }
    
    
    /**
     * 查询的关键词类型
    **/
    @JsonProperty("keywordType")
    public String getKeywordType() {
        return keywordType;
    }
    
    
    public void setKeywordType(String keywordType) {
        this.keywordType = keywordType;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class KeywordSearchData {\n");
        
        sb.append("  keywords: ").append(keywords).append("\n");
        sb.append("  total: ").append(total).append("\n");
        sb.append("  keywordType: ").append(keywordType).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

