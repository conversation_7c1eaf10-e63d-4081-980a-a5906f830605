package com.huawei.i2000.dvanalysisengineextservice.service;

import com.huawei.i2000.dvanalysisengineextservice.delegate.DVSftpServiceDelegate;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineextservice.model.SftpUploadParam;


import java.util.*;

import javax.validation.constraints.*;
import javax.validation.Valid;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.annotation.*;



import com.huawei.bsp.roa.common.HttpContext;



@Service("com.huawei.i2000.dvanalysisengineextservice.service.DVSftpService")
@Path("/dvanalysisengineextservice/v1/sftp/upload")
@Target("DVSftpService")


public class DVSftpService {

    @Autowired
    private DVSftpServiceDelegate delegate;


    
    @POST
    @Consumes({ "application/json" })
    public ResponseResult sftpUpload(HttpContext context, @Valid   @NotNull() SftpUploadParam sftpUploadParam)
        throws ServiceException {
        return    delegate.sftpUpload(context, sftpUploadParam);
    }


}


