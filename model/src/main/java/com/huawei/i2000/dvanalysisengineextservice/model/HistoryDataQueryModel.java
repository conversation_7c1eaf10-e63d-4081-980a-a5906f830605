 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import java.math.BigDecimal;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class HistoryDataQueryModel     {
    
    
    @Valid
    
    @JsonProperty("measUnitKey")
    private String measUnitKey = null;
    
    @Valid
    
    @JsonProperty("moType")
    private String moType = null;
    
    @Valid
    
    @JsonProperty("timeRangeMap")
    private Object timeRangeMap = null;
    
    @Valid
    
    @JsonProperty("pageIndex")
    private BigDecimal pageIndex = null;
    
    @Valid
    
    @JsonProperty("pageSize")
    private BigDecimal pageSize = null;
    
    @Valid
    
    @JsonProperty("tableType")
    private String tableType = "ORIGIN";
    
    @Valid
    
    @JsonProperty("dnOriginalValue2Index")
    private Object dnOriginalValue2Index = null;
    
     
    /**
     * 测量单元key值
    **/
    @JsonProperty("measUnitKey")
    public String getMeasUnitKey() {
        return measUnitKey;
    }
    
    
    public void setMeasUnitKey(String measUnitKey) {
        this.measUnitKey = measUnitKey;
    }
    
    
    /**
     * 网元类型
    **/
    @JsonProperty("moType")
    public String getMoType() {
        return moType;
    }
    
    
    public void setMoType(String moType) {
        this.moType = moType;
    }
    
    
    /**
     * Map<Long, Long>类型
    **/
    @JsonProperty("timeRangeMap")
    public Object getTimeRangeMap() {
        return timeRangeMap;
    }
    
    
    public void setTimeRangeMap(Object timeRangeMap) {
        this.timeRangeMap = timeRangeMap;
    }
    
    
    /**
     * 查询页码
    **/
    @JsonProperty("pageIndex")
    public BigDecimal getPageIndex() {
        return pageIndex;
    }
    
    
    public void setPageIndex(BigDecimal pageIndex) {
        this.pageIndex = pageIndex;
    }
    
    
    /**
     * 每页查询的数据的大小
    **/
    @JsonProperty("pageSize")
    public BigDecimal getPageSize() {
        return pageSize;
    }
    
    
    public void setPageSize(BigDecimal pageSize) {
        this.pageSize = pageSize;
    }
    
    
    /**
     * 历史数据查询的表类型
    **/
    @JsonProperty("tableType")
    public String getTableType() {
        return tableType;
    }
    
    
    public void setTableType(String tableType) {
        this.tableType = tableType;
    }
    
    
    /**
     * Map<String, Map<String, Map<String, String>>>类型
    **/
    @JsonProperty("dnOriginalValue2Index")
    public Object getDnOriginalValue2Index() {
        return dnOriginalValue2Index;
    }
    
    
    public void setDnOriginalValue2Index(Object dnOriginalValue2Index) {
        this.dnOriginalValue2Index = dnOriginalValue2Index;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class HistoryDataQueryModel {\n");
        
        sb.append("  measUnitKey: ").append(measUnitKey).append("\n");
        sb.append("  moType: ").append(moType).append("\n");
        sb.append("  timeRangeMap: ").append(timeRangeMap).append("\n");
        sb.append("  pageIndex: ").append(pageIndex).append("\n");
        sb.append("  pageSize: ").append(pageSize).append("\n");
        sb.append("  tableType: ").append(tableType).append("\n");
        sb.append("  dnOriginalValue2Index: ").append(dnOriginalValue2Index).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

