 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;

/**
 * 页面信息
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class Paging     {
    
    
    @Min(value=1L)
    @Max(value=1000L) 
    @Valid
    
    @JsonProperty("pageSize")
    private Integer pageSize = 100;
    
    @Min(value=1L)
    @Max(value=10000L) 
    @Valid
    
    @JsonProperty("pageNumber")
    private Integer pageNumber = 1;
    
    @Size(max = 1024, min = 0)
    @Valid
    
    @JsonProperty("sortField")
    private String sortField = null;
    
    @Size(max = 1024, min = 0)
    @Valid
    
    @JsonProperty("sortType")
    private String sortType = null;
    
     
    /**
     * 每页数量
     * minimum: 1.0
     * maximum: 1000.0
    **/
    @JsonProperty("pageSize")
    public Integer getPageSize() {
        return pageSize;
    }
    
    
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
    
    
    /**
     * 页数，默认1开始
     * minimum: 1.0
     * maximum: 10000.0
    **/
    @JsonProperty("pageNumber")
    public Integer getPageNumber() {
        return pageNumber;
    }
    
    
    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }
    
    
    /**
     * 分页字段
     * minLength: 0
     * maxLength: 1024
    **/
    @JsonProperty("sortField")
    public String getSortField() {
        return sortField;
    }
    
    
    public void setSortField(String sortField) {
        this.sortField = sortField;
    }
    
    
    /**
     * 分页类型（正序倒序）
     * minLength: 0
     * maxLength: 1024
    **/
    @JsonProperty("sortType")
    public String getSortType() {
        return sortType;
    }
    
    
    public void setSortType(String sortType) {
        this.sortType = sortType;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class Paging {\n");
        
        sb.append("  pageSize: ").append(pageSize).append("\n");
        sb.append("  pageNumber: ").append(pageNumber).append("\n");
        sb.append("  sortField: ").append(sortField).append("\n");
        sb.append("  sortType: ").append(sortType).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

