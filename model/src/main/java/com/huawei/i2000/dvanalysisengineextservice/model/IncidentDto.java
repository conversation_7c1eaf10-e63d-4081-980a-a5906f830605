 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import com.huawei.i2000.dvanalysisengineextservice.model.CaseInfo;
import com.huawei.i2000.dvanalysisengineextservice.model.EventDto;
import java.util.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class IncidentDto     {
    
    
    @Valid
    
    @JsonProperty("tenantId")
    private String tenantId = null;
    
    @Valid
    
    @JsonProperty("incidentId")
    private String incidentId = null;
    
    @Valid
    
    @JsonProperty("name")
    private String name = null;
    
    @Valid
    
    @JsonProperty("csn")
    private Long csn = null;
    
    @Valid
    
    @JsonProperty("category")
    private String category = null;
    
    @Valid
    
    @JsonProperty("subCategory")
    private String subCategory = null;
    
    @Valid
    
    @JsonProperty("incidentType")
    private Integer incidentType = null;
    
    @Valid
    
    @JsonProperty("status")
    private String status = null;
    
    @Valid
    
    @JsonProperty("ackStatus")
    private Integer ackStatus = 0;
    
    @Valid
    
    @JsonProperty("clearStatus")
    private Integer clearStatus = 0;
    
    @Valid
    
    @JsonProperty("createTime")
    private Long createTime = null;
    
    @Valid
    
    @JsonProperty("occurTime")
    private Long occurTime = null;
    
    @Valid
    
    @JsonProperty("clearTime")
    private Long clearTime = null;
    
    @Valid
    
    @JsonProperty("startTime")
    private Long startTime = null;
    
    @Valid
    
    @JsonProperty("endTime")
    private Long endTime = null;
    
    @Valid
    
    @JsonProperty("priority")
    private String priority = null;
    
    @Valid
    
    @JsonProperty("description")
    private String description = null;
    
    @Valid
    
    @JsonProperty("repairAdvice")
    private String repairAdvice = null;
    
    @Valid
    
    @JsonProperty("sourceObjType")
    private String sourceObjType = null;
    
    @Valid
    
    @JsonProperty("sourceObjId")
    private String sourceObjId = null;
    
    @Valid
    
    @JsonProperty("sourceObjName")
    private String sourceObjName = null;
    
    @Size(max = 10000, min = 0)
    @Valid
    
    @JsonProperty("events")
    private List<EventDto> events = new ArrayList<EventDto>();
    
    @Size(max = 10000, min = 0)
    @Valid
    
    @JsonProperty("cases")
    private List<CaseInfo> cases = new ArrayList<CaseInfo>();
    
     
    /**
    **/
    @JsonProperty("tenantId")
    public String getTenantId() {
        return tenantId;
    }
    
    
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    
    /**
    **/
    @JsonProperty("incidentId")
    public String getIncidentId() {
        return incidentId;
    }
    
    
    public void setIncidentId(String incidentId) {
        this.incidentId = incidentId;
    }
    
    
    /**
    **/
    @JsonProperty("name")
    public String getName() {
        return name;
    }
    
    
    public void setName(String name) {
        this.name = name;
    }
    
    
    /**
    **/
    @JsonProperty("csn")
    public Long getCsn() {
        return csn;
    }
    
    
    public void setCsn(Long csn) {
        this.csn = csn;
    }
    
    
    /**
    **/
    @JsonProperty("category")
    public String getCategory() {
        return category;
    }
    
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    
    /**
    **/
    @JsonProperty("subCategory")
    public String getSubCategory() {
        return subCategory;
    }
    
    
    public void setSubCategory(String subCategory) {
        this.subCategory = subCategory;
    }
    
    
    /**
    **/
    @JsonProperty("incidentType")
    public Integer getIncidentType() {
        return incidentType;
    }
    
    
    public void setIncidentType(Integer incidentType) {
        this.incidentType = incidentType;
    }
    
    
    /**
    **/
    @JsonProperty("status")
    public String getStatus() {
        return status;
    }
    
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    
    /**
    **/
    @JsonProperty("ackStatus")
    public Integer getAckStatus() {
        return ackStatus;
    }
    
    
    public void setAckStatus(Integer ackStatus) {
        this.ackStatus = ackStatus;
    }
    
    
    /**
    **/
    @JsonProperty("clearStatus")
    public Integer getClearStatus() {
        return clearStatus;
    }
    
    
    public void setClearStatus(Integer clearStatus) {
        this.clearStatus = clearStatus;
    }
    
    
    /**
    **/
    @JsonProperty("createTime")
    public Long getCreateTime() {
        return createTime;
    }
    
    
    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }
    
    
    /**
    **/
    @JsonProperty("occurTime")
    public Long getOccurTime() {
        return occurTime;
    }
    
    
    public void setOccurTime(Long occurTime) {
        this.occurTime = occurTime;
    }
    
    
    /**
    **/
    @JsonProperty("clearTime")
    public Long getClearTime() {
        return clearTime;
    }
    
    
    public void setClearTime(Long clearTime) {
        this.clearTime = clearTime;
    }
    
    
    /**
    **/
    @JsonProperty("startTime")
    public Long getStartTime() {
        return startTime;
    }
    
    
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }
    
    
    /**
    **/
    @JsonProperty("endTime")
    public Long getEndTime() {
        return endTime;
    }
    
    
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }
    
    
    /**
    **/
    @JsonProperty("priority")
    public String getPriority() {
        return priority;
    }
    
    
    public void setPriority(String priority) {
        this.priority = priority;
    }
    
    
    /**
    **/
    @JsonProperty("description")
    public String getDescription() {
        return description;
    }
    
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    
    /**
    **/
    @JsonProperty("repairAdvice")
    public String getRepairAdvice() {
        return repairAdvice;
    }
    
    
    public void setRepairAdvice(String repairAdvice) {
        this.repairAdvice = repairAdvice;
    }
    
    
    /**
    **/
    @JsonProperty("sourceObjType")
    public String getSourceObjType() {
        return sourceObjType;
    }
    
    
    public void setSourceObjType(String sourceObjType) {
        this.sourceObjType = sourceObjType;
    }
    
    
    /**
    **/
    @JsonProperty("sourceObjId")
    public String getSourceObjId() {
        return sourceObjId;
    }
    
    
    public void setSourceObjId(String sourceObjId) {
        this.sourceObjId = sourceObjId;
    }
    
    
    /**
    **/
    @JsonProperty("sourceObjName")
    public String getSourceObjName() {
        return sourceObjName;
    }
    
    
    public void setSourceObjName(String sourceObjName) {
        this.sourceObjName = sourceObjName;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 10000
    **/
    @JsonProperty("events")
    public List<EventDto> getEvents() {
        return events;
    }
    
    
    public void setEvents(List<EventDto> events) {
        this.events = events;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 10000
    **/
    @JsonProperty("cases")
    public List<CaseInfo> getCases() {
        return cases;
    }
    
    
    public void setCases(List<CaseInfo> cases) {
        this.cases = cases;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class IncidentDto {\n");
        
        sb.append("  tenantId: ").append(tenantId).append("\n");
        sb.append("  incidentId: ").append(incidentId).append("\n");
        sb.append("  name: ").append(name).append("\n");
        sb.append("  csn: ").append(csn).append("\n");
        sb.append("  category: ").append(category).append("\n");
        sb.append("  subCategory: ").append(subCategory).append("\n");
        sb.append("  incidentType: ").append(incidentType).append("\n");
        sb.append("  status: ").append(status).append("\n");
        sb.append("  ackStatus: ").append(ackStatus).append("\n");
        sb.append("  clearStatus: ").append(clearStatus).append("\n");
        sb.append("  createTime: ").append(createTime).append("\n");
        sb.append("  occurTime: ").append(occurTime).append("\n");
        sb.append("  clearTime: ").append(clearTime).append("\n");
        sb.append("  startTime: ").append(startTime).append("\n");
        sb.append("  endTime: ").append(endTime).append("\n");
        sb.append("  priority: ").append(priority).append("\n");
        sb.append("  description: ").append(description).append("\n");
        sb.append("  repairAdvice: ").append(repairAdvice).append("\n");
        sb.append("  sourceObjType: ").append(sourceObjType).append("\n");
        sb.append("  sourceObjId: ").append(sourceObjId).append("\n");
        sb.append("  sourceObjName: ").append(sourceObjName).append("\n");
        sb.append("  events: ").append(events).append("\n");
        sb.append("  cases: ").append(cases).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

