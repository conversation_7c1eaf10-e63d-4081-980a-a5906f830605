 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import com.huawei.i2000.dvanalysisengineextservice.model.MeasType4MV;
import java.util.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class MeasUnit4MV     {
    
    
    @Valid
    
    @JsonProperty("icon")
    private String icon = null;
    
    @Valid
    
    @JsonProperty("name")
    private String name = null;
    
    @Valid
    
    @JsonProperty("measUnitKey")
    private String measUnitKey = null;
    
    @Valid
    
    @JsonProperty("dn")
    private String dn = null;
    
    @Valid
    
    @JsonProperty("hasMeasObj")
    private Boolean hasMeasObj = null;
    
    @Valid
    
    @JsonProperty("open")
    private Boolean open = null;
    
    @Valid
    
    @JsonProperty("chkDisabled")
    private Boolean chkDisabled = null;
    
    @Valid
    
    @JsonProperty("children")
    private List<MeasType4MV> children = new ArrayList<MeasType4MV>();
    
     
    /**
    **/
    @JsonProperty("icon")
    public String getIcon() {
        return icon;
    }
    
    
    public void setIcon(String icon) {
        this.icon = icon;
    }
    
    
    /**
    **/
    @JsonProperty("name")
    public String getName() {
        return name;
    }
    
    
    public void setName(String name) {
        this.name = name;
    }
    
    
    /**
    **/
    @JsonProperty("measUnitKey")
    public String getMeasUnitKey() {
        return measUnitKey;
    }
    
    
    public void setMeasUnitKey(String measUnitKey) {
        this.measUnitKey = measUnitKey;
    }
    
    
    /**
    **/
    @JsonProperty("dn")
    public String getDn() {
        return dn;
    }
    
    
    public void setDn(String dn) {
        this.dn = dn;
    }
    
    
    /**
    **/
    @JsonProperty("hasMeasObj")
    public Boolean getHasMeasObj() {
        return hasMeasObj;
    }
    
    
    public void setHasMeasObj(Boolean hasMeasObj) {
        this.hasMeasObj = hasMeasObj;
    }
    
    
    /**
    **/
    @JsonProperty("open")
    public Boolean getOpen() {
        return open;
    }
    
    
    public void setOpen(Boolean open) {
        this.open = open;
    }
    
    
    /**
    **/
    @JsonProperty("chkDisabled")
    public Boolean getChkDisabled() {
        return chkDisabled;
    }
    
    
    public void setChkDisabled(Boolean chkDisabled) {
        this.chkDisabled = chkDisabled;
    }
    
    
    /**
    **/
    @JsonProperty("children")
    public List<MeasType4MV> getChildren() {
        return children;
    }
    
    
    public void setChildren(List<MeasType4MV> children) {
        this.children = children;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class MeasUnit4MV {\n");
        
        sb.append("  icon: ").append(icon).append("\n");
        sb.append("  name: ").append(name).append("\n");
        sb.append("  measUnitKey: ").append(measUnitKey).append("\n");
        sb.append("  dn: ").append(dn).append("\n");
        sb.append("  hasMeasObj: ").append(hasMeasObj).append("\n");
        sb.append("  open: ").append(open).append("\n");
        sb.append("  chkDisabled: ").append(chkDisabled).append("\n");
        sb.append("  children: ").append(children).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

