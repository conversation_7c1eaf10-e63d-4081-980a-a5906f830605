 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import java.util.*;

/**
 * 关键词项
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class KeywordItem     {
    
    
    @Valid
    
    @JsonProperty("name")
    private String name = null;
    
    public enum TypeEnum { 
    
    
         @JsonProperty("ALARM_ID") ALARM_ID,  @JsonProperty("ALARM_NAME") ALARM_NAME,  @JsonProperty("ERROR_CODE") ERROR_CODE,  @JsonProperty("MEAS_TYPE_KEY") MEAS_TYPE_KEY,  @JsonProperty("NE_TYPE") NE_TYPE,  @JsonProperty("TASK_NAME") TASK_NAME, ; 
        
        
    };
    
    
    @JsonProperty("type")
    private TypeEnum type = null;
    
    @Valid
    
    @JsonProperty("source")
    private String source = null;
    
    @Valid
    
    @JsonProperty("alias")
    private List<String> alias = new ArrayList<String>();
    
    public enum SolutionNameEnum { 
    
    
         @JsonProperty("CBS") CBS,  @JsonProperty("DV") DV, ; 
        
        
    };
    
    
    @JsonProperty("solutionName")
    private SolutionNameEnum solutionName = null;
    
    @Valid
    
    @JsonProperty("knowledgeIdList")
    private List<String> knowledgeIdList = new ArrayList<String>();
    
     
    /**
     * 关键词名称
    **/
    @JsonProperty("name")
    public String getName() {
        return name;
    }
    
    
    public void setName(String name) {
        this.name = name;
    }
    
    
    /**
     * 关键词类型
    **/
    @JsonProperty("type")
    public TypeEnum getType() {
        return type;
    }
    
    
    public void setType(TypeEnum type) {
        this.type = type;
    }
    
    
    /**
     * 来源
    **/
    @JsonProperty("source")
    public String getSource() {
        return source;
    }
    
    
    public void setSource(String source) {
        this.source = source;
    }
    
    
    /**
     * 别名列表
    **/
    @JsonProperty("alias")
    public List<String> getAlias() {
        return alias;
    }
    
    
    public void setAlias(List<String> alias) {
        this.alias = alias;
    }
    
    
    /**
     * 解决方案名称
    **/
    @JsonProperty("solutionName")
    public SolutionNameEnum getSolutionName() {
        return solutionName;
    }
    
    
    public void setSolutionName(SolutionNameEnum solutionName) {
        this.solutionName = solutionName;
    }
    
    
    /**
     * 知识ID列表
    **/
    @JsonProperty("knowledgeIdList")
    public List<String> getKnowledgeIdList() {
        return knowledgeIdList;
    }
    
    
    public void setKnowledgeIdList(List<String> knowledgeIdList) {
        this.knowledgeIdList = knowledgeIdList;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class KeywordItem {\n");
        
        sb.append("  name: ").append(name).append("\n");
        sb.append("  type: ").append(type).append("\n");
        sb.append("  source: ").append(source).append("\n");
        sb.append("  alias: ").append(alias).append("\n");
        sb.append("  solutionName: ").append(solutionName).append("\n");
        sb.append("  knowledgeIdList: ").append(knowledgeIdList).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

