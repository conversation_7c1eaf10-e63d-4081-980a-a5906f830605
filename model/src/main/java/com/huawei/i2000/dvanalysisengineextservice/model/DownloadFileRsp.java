 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import java.util.*;
import java.util.HashMap;
import java.util.Map;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class DownloadFileRsp     {
    
    
    @Valid
    
    @JsonProperty("filePath")
    private String filePath = null;
    
    @Valid
    
    @JsonProperty("flag")
    private Boolean flag = null;
    
    @Valid
    
    @JsonProperty("uri")
    private String uri = null;
    
    @Valid
    
    @JsonProperty("fileContent")
    private Map<String, List<String>> fileContent = new HashMap<String, List<String>>();
    
     
    /**
    **/
    @JsonProperty("filePath")
    public String getFilePath() {
        return filePath;
    }
    
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    
    /**
    **/
    @JsonProperty("flag")
    public Boolean getFlag() {
        return flag;
    }
    
    
    public void setFlag(Boolean flag) {
        this.flag = flag;
    }
    
    
    /**
    **/
    @JsonProperty("uri")
    public String getUri() {
        return uri;
    }
    
    
    public void setUri(String uri) {
        this.uri = uri;
    }
    
    
    /**
    **/
    @JsonProperty("fileContent")
    public Map<String, List<String>> getFileContent() {
        return fileContent;
    }
    
    
    public void setFileContent(Map<String, List<String>> fileContent) {
        this.fileContent = fileContent;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class DownloadFileRsp {\n");
        
        sb.append("  filePath: ").append(filePath).append("\n");
        sb.append("  flag: ").append(flag).append("\n");
        sb.append("  uri: ").append(uri).append("\n");
        sb.append("  fileContent: ").append(fileContent).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

