 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class CaseInfo     {
    
    
    @Valid
    
    @JsonProperty("name")
    private String name = null;
    
    @Valid
    
    @JsonProperty("keyFault")
    private String keyFault = null;
    
    @Valid
    
    @JsonProperty("cause")
    private String cause = null;
    
    @Valid
    
    @JsonProperty("effect")
    private String effect = null;
    
    @Valid
    
    @JsonProperty("repair")
    private String repair = null;
    
     
    /**
    **/
    @JsonProperty("name")
    public String getName() {
        return name;
    }
    
    
    public void setName(String name) {
        this.name = name;
    }
    
    
    /**
    **/
    @JsonProperty("keyFault")
    public String getKeyFault() {
        return keyFault;
    }
    
    
    public void setKeyFault(String keyFault) {
        this.keyFault = keyFault;
    }
    
    
    /**
    **/
    @JsonProperty("cause")
    public String getCause() {
        return cause;
    }
    
    
    public void setCause(String cause) {
        this.cause = cause;
    }
    
    
    /**
    **/
    @JsonProperty("effect")
    public String getEffect() {
        return effect;
    }
    
    
    public void setEffect(String effect) {
        this.effect = effect;
    }
    
    
    /**
    **/
    @JsonProperty("repair")
    public String getRepair() {
        return repair;
    }
    
    
    public void setRepair(String repair) {
        this.repair = repair;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class CaseInfo {\n");
        
        sb.append("  name: ").append(name).append("\n");
        sb.append("  keyFault: ").append(keyFault).append("\n");
        sb.append("  cause: ").append(cause).append("\n");
        sb.append("  effect: ").append(effect).append("\n");
        sb.append("  repair: ").append(repair).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

