package com.huawei.i2000.dvanalysisengineextservice.delegate;

import com.huawei.i2000.dvanalysisengineextservice.service.*;

import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineextservice.model.SftpUploadParam;


import java.util.*;

import com.huawei.bsp.remoteservice.exception.ServiceException;

import com.huawei.bsp.roa.common.HttpContext;



public interface DVSftpServiceDelegate {
  
    public abstract ResponseResult sftpUpload(HttpContext context, SftpUploadParam sftpUploadParam)
      throws ServiceException;
  
}

