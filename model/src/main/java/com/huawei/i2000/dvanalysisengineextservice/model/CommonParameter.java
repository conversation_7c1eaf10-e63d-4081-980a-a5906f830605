 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class CommonParameter     {
    
    
    @Valid
    
    @JsonProperty("username")
    private String username = null;
    
    @Valid
    
    @JsonProperty("timeoutSeconds")
    private Integer timeoutSeconds = 0;
    
    @Valid
    
    @JsonProperty("carePrecess")
    private Boolean carePrecess = false;
    
    @Valid
    
    @JsonProperty("ttlDay")
    private Integer ttlDay = 7;
    
    @Valid
    
    @JsonProperty("whoIsCallingController")
    private String whoIsCallingController = "unknown";
    
    @Valid
    
    @JsonProperty("cfgFileId")
    private String cfgFileId = null;
    
    @Valid
    
    @JsonProperty("restartPlugin")
    private Boolean restartPlugin = false;
    
    @Valid
    
    @JsonProperty("priorVM")
    private Boolean priorVM = true;
    
     
    /**
    **/
    @JsonProperty("username")
    public String getUsername() {
        return username;
    }
    
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    
    /**
    **/
    @JsonProperty("timeoutSeconds")
    public Integer getTimeoutSeconds() {
        return timeoutSeconds;
    }
    
    
    public void setTimeoutSeconds(Integer timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
    
    
    /**
    **/
    @JsonProperty("carePrecess")
    public Boolean getCarePrecess() {
        return carePrecess;
    }
    
    
    public void setCarePrecess(Boolean carePrecess) {
        this.carePrecess = carePrecess;
    }
    
    
    /**
    **/
    @JsonProperty("ttlDay")
    public Integer getTtlDay() {
        return ttlDay;
    }
    
    
    public void setTtlDay(Integer ttlDay) {
        this.ttlDay = ttlDay;
    }
    
    
    /**
    **/
    @JsonProperty("whoIsCallingController")
    public String getWhoIsCallingController() {
        return whoIsCallingController;
    }
    
    
    public void setWhoIsCallingController(String whoIsCallingController) {
        this.whoIsCallingController = whoIsCallingController;
    }
    
    
    /**
    **/
    @JsonProperty("cfgFileId")
    public String getCfgFileId() {
        return cfgFileId;
    }
    
    
    public void setCfgFileId(String cfgFileId) {
        this.cfgFileId = cfgFileId;
    }
    
    
    /**
    **/
    @JsonProperty("restartPlugin")
    public Boolean getRestartPlugin() {
        return restartPlugin;
    }
    
    
    public void setRestartPlugin(Boolean restartPlugin) {
        this.restartPlugin = restartPlugin;
    }
    
    
    /**
    **/
    @JsonProperty("priorVM")
    public Boolean getPriorVM() {
        return priorVM;
    }
    
    
    public void setPriorVM(Boolean priorVM) {
        this.priorVM = priorVM;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class CommonParameter {\n");
        
        sb.append("  username: ").append(username).append("\n");
        sb.append("  timeoutSeconds: ").append(timeoutSeconds).append("\n");
        sb.append("  carePrecess: ").append(carePrecess).append("\n");
        sb.append("  ttlDay: ").append(ttlDay).append("\n");
        sb.append("  whoIsCallingController: ").append(whoIsCallingController).append("\n");
        sb.append("  cfgFileId: ").append(cfgFileId).append("\n");
        sb.append("  restartPlugin: ").append(restartPlugin).append("\n");
        sb.append("  priorVM: ").append(priorVM).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

