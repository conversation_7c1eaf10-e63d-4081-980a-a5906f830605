package com.huawei.i2000.dvanalysisengineextservice.service;

import com.huawei.i2000.dvanalysisengineextservice.delegate.DVAnalysisEngineExtServiceDelegate;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;



import java.util.*;

import javax.validation.constraints.*;
import javax.validation.Valid;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.annotation.*;



import com.huawei.bsp.roa.common.HttpContext;



@Service("com.huawei.i2000.dvanalysisengineextservice.service.DVAnalysisEngineExtService")
@Path("/dvanalysisengineextservice/v1")
@Target("DVAnalysisEngineExtService")


public class DVAnalysisEngineExtService {

    @Autowired
    private DVAnalysisEngineExtServiceDelegate delegate;


    
    @GET
    @Produces({ "application/json" })
    public String getServiceInfo(HttpContext context)
        throws ServiceException {
        return    delegate.getServiceInfo(context);
    }


}


