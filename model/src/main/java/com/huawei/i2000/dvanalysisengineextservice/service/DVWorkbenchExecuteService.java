package com.huawei.i2000.dvanalysisengineextservice.service;

import com.huawei.i2000.dvanalysisengineextservice.delegate.DVWorkbenchExecuteServiceDelegate;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import com.huawei.i2000.dvanalysisengineextservice.model.ExecuteRequest;
import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;


import java.util.*;

import javax.validation.constraints.*;
import javax.validation.Valid;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.annotation.*;



import com.huawei.bsp.roa.common.HttpContext;



@Service("com.huawei.i2000.dvanalysisengineextservice.service.DVWorkbenchExecuteService")
@Path("/dvanalysisengineextservice/v1/workbench/execute")
@Target("DVWorkbenchExecuteService")


public class DVWorkbenchExecuteService {

    @Autowired
    private DVWorkbenchExecuteServiceDelegate delegate;


    
    @POST
    @Consumes({ "application/json" })
    public ResponseResult executeScript(HttpContext context, @Valid   @NotNull() ExecuteRequest executeParam)
        throws ServiceException {
        return    delegate.executeScript(context, executeParam);
    }


}


