 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import com.huawei.i2000.dvanalysisengineextservice.model.RecommendContext;
import java.util.*;

/**
 * 智能提示请求
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class RecommendReq     {
    
    
    @Size(max = 1024)
    @Valid
    
    @JsonProperty("userQuestion")
    private String userQuestion = null;
    
    @Size(max = 5)
    @Valid
    
    @JsonProperty("context")
    private List<RecommendContext> context = new ArrayList<RecommendContext>();
    
     
    /**
     * 用户问题
     * maxLength: 1024
    **/
    @JsonProperty("userQuestion")
    public String getUserQuestion() {
        return userQuestion;
    }
    
    
    public void setUserQuestion(String userQuestion) {
        this.userQuestion = userQuestion;
    }
    
    
    /**
     * 上下文
     * maxLength: 5
    **/
    @JsonProperty("context")
    public List<RecommendContext> getContext() {
        return context;
    }
    
    
    public void setContext(List<RecommendContext> context) {
        this.context = context;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class RecommendReq {\n");
        
        sb.append("  userQuestion: ").append(userQuestion).append("\n");
        sb.append("  context: ").append(context).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

