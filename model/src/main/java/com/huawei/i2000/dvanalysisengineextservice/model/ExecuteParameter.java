 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import com.huawei.i2000.dvanalysisengineextservice.model.CommonParameter;
import com.huawei.i2000.dvanalysisengineextservice.model.RegexInfo;
import java.util.*;
import java.util.HashMap;
import java.util.Map;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class ExecuteParameter extends CommonParameter    {
    
    
    @Valid
    
    @JsonProperty("scriptContent")
    private byte[] scriptContent = null;
    
    @Valid
    
    @JsonProperty("whoIsCallingController")
    private String whoIsCallingController = "unknown";
    
    @Valid
    
    @JsonProperty("resourceId")
    private String resourceId = null;
    
    @Valid
    
    @JsonProperty("useSecBox")
    private Boolean useSecBox = false;
    
    @Valid
    
    @JsonProperty("cfgFileId")
    private String cfgFileId = null;
    
    @Valid
    
    @JsonProperty("restartPlugin")
    private Boolean restartPlugin = false;
    
    @Valid
    
    @JsonProperty("ipParamMap")
    private Map<String, String> ipParamMap = new HashMap<String, String>();
    
    @Valid
    
    @JsonProperty("ips")
    private List<String> ips = new ArrayList<String>();
    
    @Valid
    
    @JsonProperty("ipEnvMap")
    private Map<String, Map<String, String>> ipEnvMap = new HashMap<String, Map<String, String>>();
    
    @Valid
    
    @JsonProperty("security")
    private Boolean security = false;
    
    @Valid
    
    @JsonProperty("successKeyword")
    private String successKeyword = null;
    
    @Valid
    
    @JsonProperty("ttlDay")
    private Integer ttlDay = 7;
    
    @Valid
    
    @JsonProperty("scriptMd5")
    private String scriptMd5 = null;
    
    @Valid
    
    @JsonProperty("timeoutSeconds")
    private Integer timeoutSeconds = 0;
    
    @Valid
    
    @JsonProperty("carePrecess")
    private Boolean carePrecess = false;
    
    @Valid
    
    @JsonProperty("zipTemplate")
    private Boolean zipTemplate = false;
    
    @Valid
    
    @JsonProperty("useInside")
    private Boolean useInside = true;
    
    @Valid
    
    @JsonProperty("scriptParams")
    private String scriptParams = null;
    
    @Valid
    
    @JsonProperty("regexInfos")
    private List<RegexInfo> regexInfos = new ArrayList<RegexInfo>();
    
    @Valid
    
    @JsonProperty("username")
    private String username = null;
    
    @Valid
    
    @JsonProperty("priorVM")
    private Boolean priorVM = true;
    
     
    /**
    **/
    @JsonProperty("scriptContent")
    public byte[] getScriptContent() {
        return scriptContent;
    }
    
    
    public void setScriptContent(byte[] scriptContent) {
        this.scriptContent = scriptContent;
    }
    
    
    /**
    **/
    @JsonProperty("whoIsCallingController")
    public String getWhoIsCallingController() {
        return whoIsCallingController;
    }
    
    
    public void setWhoIsCallingController(String whoIsCallingController) {
        this.whoIsCallingController = whoIsCallingController;
    }
    
    
    /**
    **/
    @JsonProperty("resourceId")
    public String getResourceId() {
        return resourceId;
    }
    
    
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }
    
    
    /**
    **/
    @JsonProperty("useSecBox")
    public Boolean getUseSecBox() {
        return useSecBox;
    }
    
    
    public void setUseSecBox(Boolean useSecBox) {
        this.useSecBox = useSecBox;
    }
    
    
    /**
    **/
    @JsonProperty("cfgFileId")
    public String getCfgFileId() {
        return cfgFileId;
    }
    
    
    public void setCfgFileId(String cfgFileId) {
        this.cfgFileId = cfgFileId;
    }
    
    
    /**
    **/
    @JsonProperty("restartPlugin")
    public Boolean getRestartPlugin() {
        return restartPlugin;
    }
    
    
    public void setRestartPlugin(Boolean restartPlugin) {
        this.restartPlugin = restartPlugin;
    }
    
    
    /**
    **/
    @JsonProperty("ipParamMap")
    public Map<String, String> getIpParamMap() {
        return ipParamMap;
    }
    
    
    public void setIpParamMap(Map<String, String> ipParamMap) {
        this.ipParamMap = ipParamMap;
    }
    
    
    /**
    **/
    @JsonProperty("ips")
    public List<String> getIps() {
        return ips;
    }
    
    
    public void setIps(List<String> ips) {
        this.ips = ips;
    }
    
    
    /**
    **/
    @JsonProperty("ipEnvMap")
    public Map<String, Map<String, String>> getIpEnvMap() {
        return ipEnvMap;
    }
    
    
    public void setIpEnvMap(Map<String, Map<String, String>> ipEnvMap) {
        this.ipEnvMap = ipEnvMap;
    }
    
    
    /**
    **/
    @JsonProperty("security")
    public Boolean getSecurity() {
        return security;
    }
    
    
    public void setSecurity(Boolean security) {
        this.security = security;
    }
    
    
    /**
    **/
    @JsonProperty("successKeyword")
    public String getSuccessKeyword() {
        return successKeyword;
    }
    
    
    public void setSuccessKeyword(String successKeyword) {
        this.successKeyword = successKeyword;
    }
    
    
    /**
    **/
    @JsonProperty("ttlDay")
    public Integer getTtlDay() {
        return ttlDay;
    }
    
    
    public void setTtlDay(Integer ttlDay) {
        this.ttlDay = ttlDay;
    }
    
    
    /**
    **/
    @JsonProperty("scriptMd5")
    public String getScriptMd5() {
        return scriptMd5;
    }
    
    
    public void setScriptMd5(String scriptMd5) {
        this.scriptMd5 = scriptMd5;
    }
    
    
    /**
    **/
    @JsonProperty("timeoutSeconds")
    public Integer getTimeoutSeconds() {
        return timeoutSeconds;
    }
    
    
    public void setTimeoutSeconds(Integer timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
    
    
    /**
    **/
    @JsonProperty("carePrecess")
    public Boolean getCarePrecess() {
        return carePrecess;
    }
    
    
    public void setCarePrecess(Boolean carePrecess) {
        this.carePrecess = carePrecess;
    }
    
    
    /**
    **/
    @JsonProperty("zipTemplate")
    public Boolean getZipTemplate() {
        return zipTemplate;
    }
    
    
    public void setZipTemplate(Boolean zipTemplate) {
        this.zipTemplate = zipTemplate;
    }
    
    
    /**
    **/
    @JsonProperty("useInside")
    public Boolean getUseInside() {
        return useInside;
    }
    
    
    public void setUseInside(Boolean useInside) {
        this.useInside = useInside;
    }
    
    
    /**
    **/
    @JsonProperty("scriptParams")
    public String getScriptParams() {
        return scriptParams;
    }
    
    
    public void setScriptParams(String scriptParams) {
        this.scriptParams = scriptParams;
    }
    
    
    /**
    **/
    @JsonProperty("regexInfos")
    public List<RegexInfo> getRegexInfos() {
        return regexInfos;
    }
    
    
    public void setRegexInfos(List<RegexInfo> regexInfos) {
        this.regexInfos = regexInfos;
    }
    
    
    /**
    **/
    @JsonProperty("username")
    public String getUsername() {
        return username;
    }
    
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    
    /**
    **/
    @JsonProperty("priorVM")
    public Boolean getPriorVM() {
        return priorVM;
    }
    
    
    public void setPriorVM(Boolean priorVM) {
        this.priorVM = priorVM;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class ExecuteParameter {\n");
        sb.append("  " + super.toString()).append("\n");
        sb.append("  scriptContent: ").append(scriptContent).append("\n");
        sb.append("  whoIsCallingController: ").append(whoIsCallingController).append("\n");
        sb.append("  resourceId: ").append(resourceId).append("\n");
        sb.append("  useSecBox: ").append(useSecBox).append("\n");
        sb.append("  cfgFileId: ").append(cfgFileId).append("\n");
        sb.append("  restartPlugin: ").append(restartPlugin).append("\n");
        sb.append("  ipParamMap: ").append(ipParamMap).append("\n");
        sb.append("  ips: ").append(ips).append("\n");
        sb.append("  ipEnvMap: ").append(ipEnvMap).append("\n");
        sb.append("  security: ").append(security).append("\n");
        sb.append("  successKeyword: ").append(successKeyword).append("\n");
        sb.append("  ttlDay: ").append(ttlDay).append("\n");
        sb.append("  scriptMd5: ").append(scriptMd5).append("\n");
        sb.append("  timeoutSeconds: ").append(timeoutSeconds).append("\n");
        sb.append("  carePrecess: ").append(carePrecess).append("\n");
        sb.append("  zipTemplate: ").append(zipTemplate).append("\n");
        sb.append("  useInside: ").append(useInside).append("\n");
        sb.append("  scriptParams: ").append(scriptParams).append("\n");
        sb.append("  regexInfos: ").append(regexInfos).append("\n");
        sb.append("  username: ").append(username).append("\n");
        sb.append("  priorVM: ").append(priorVM).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

