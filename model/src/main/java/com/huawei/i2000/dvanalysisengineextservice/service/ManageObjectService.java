package com.huawei.i2000.dvanalysisengineextservice.service;

import com.huawei.i2000.dvanalysisengineextservice.delegate.ManageObjectServiceDelegate;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;


import java.util.*;

import javax.validation.constraints.*;
import javax.validation.Valid;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.annotation.*;



import com.huawei.bsp.roa.common.HttpContext;



@Service("com.huawei.i2000.dvanalysisengineextservice.service.ManageObjectService")
@Path("/dvanalysisengineextservice/v1/managedobject")
@Target("ManageObjectService")


public class ManageObjectService {

    @Autowired
    private ManageObjectServiceDelegate delegate;


    
    @GET
    @Path("/connectstatus")
    @Consumes({ "application/json" })
    @Produces({ "application/json" })
    public ResponseResult connectStatus(HttpContext context,   @QueryParam("neName")String neName,   @QueryParam("neType")String neType,   @QueryParam("ip")String ip)
        throws ServiceException {
        return    delegate.connectStatus(context, neName,neType,ip);
    }


    
    @GET
    @Path("/eventlist")
    @Consumes({ "application/json" })
    @Produces({ "application/json" })
    public ResponseResult getEventList(HttpContext context,   @QueryParam("dn")String dn)
        throws ServiceException {
        return    delegate.getEventList(context, dn);
    }


    
    @GET
    @Path("/healthstatus")
    @Consumes({ "application/json" })
    @Produces({ "application/json" })
    public ResponseResult healthStatus(HttpContext context,   @QueryParam("neName")String neName)
        throws ServiceException {
        return    delegate.healthStatus(context, neName);
    }


    
    @GET
    @Path("/topostatus")
    @Consumes({ "application/json" })
    @Produces({ "application/json" })
    public ResponseResult topoStatus(HttpContext context,   @QueryParam("neName")String neName)
        throws ServiceException {
        return    delegate.topoStatus(context, neName);
    }


}


