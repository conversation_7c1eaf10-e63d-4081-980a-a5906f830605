 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import java.util.*;

/**
 * 智能提示返回体
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class RecommendRsp     {
    
    
    @Size(max = 20)
    @Valid
    
    @JsonProperty("recommendQuestions")
    private List<String> recommendQuestions = new ArrayList<String>();
    
     
    /**
     * 答案
     * maxLength: 20
    **/
    @JsonProperty("recommendQuestions")
    public List<String> getRecommendQuestions() {
        return recommendQuestions;
    }
    
    
    public void setRecommendQuestions(List<String> recommendQuestions) {
        this.recommendQuestions = recommendQuestions;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class RecommendRsp {\n");
        
        sb.append("  recommendQuestions: ").append(recommendQuestions).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

