 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import com.huawei.i2000.dvanalysisengineextservice.model.MeasObjectVO;
import java.util.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class MeasObjectInfo     {
    
    
    @Valid
    
    @JsonProperty("mos")
    private List<MeasObjectVO> mos = new ArrayList<MeasObjectVO>();
    
    @Valid
    
    @JsonProperty("indexes")
    private List<String> indexes = new ArrayList<String>();
    
    @Valid
    
    @JsonProperty("iempKeys")
    private List<String> iempKeys = new ArrayList<String>();
    
     
    /**
    **/
    @JsonProperty("mos")
    public List<MeasObjectVO> getMos() {
        return mos;
    }
    
    
    public void setMos(List<MeasObjectVO> mos) {
        this.mos = mos;
    }
    
    
    /**
    **/
    @JsonProperty("indexes")
    public List<String> getIndexes() {
        return indexes;
    }
    
    
    public void setIndexes(List<String> indexes) {
        this.indexes = indexes;
    }
    
    
    /**
    **/
    @JsonProperty("iempKeys")
    public List<String> getIempKeys() {
        return iempKeys;
    }
    
    
    public void setIempKeys(List<String> iempKeys) {
        this.iempKeys = iempKeys;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class MeasObjectInfo {\n");
        
        sb.append("  mos: ").append(mos).append("\n");
        sb.append("  indexes: ").append(indexes).append("\n");
        sb.append("  iempKeys: ").append(iempKeys).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

