 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import java.util.*;
import java.util.Map;

/**
 * 容灾切换流程参数--配置展示，不使用
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class AlarmParam     {
    
    
    @Valid
    
    @JsonProperty("alarms")
    private List<Map<String, String>> alarms = new ArrayList<Map<String, String>>();
    
     
    /**
     * List<Map<String, String>>类型
    **/
    @JsonProperty("alarms")
    public List<Map<String, String>> getAlarms() {
        return alarms;
    }
    
    
    public void setAlarms(List<Map<String, String>> alarms) {
        this.alarms = alarms;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class AlarmParam {\n");
        
        sb.append("  alarms: ").append(alarms).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

