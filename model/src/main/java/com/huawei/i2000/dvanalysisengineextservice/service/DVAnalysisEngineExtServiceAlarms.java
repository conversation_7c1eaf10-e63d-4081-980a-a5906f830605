package com.huawei.i2000.dvanalysisengineextservice.service;

import com.huawei.i2000.dvanalysisengineextservice.delegate.DVAnalysisEngineExtServiceAlarmsDelegate;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineextservice.model.QueryAlarmInfoByIdParam;


import java.util.*;

import javax.validation.constraints.*;
import javax.validation.Valid;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.annotation.*;



import com.huawei.bsp.roa.common.HttpContext;



@Service("com.huawei.i2000.dvanalysisengineextservice.service.DVAnalysisEngineExtServiceAlarms")
@Path("/dvanalysisengineextservice/v1/alarms")
@Target("DVAnalysisEngineExtServiceAlarms")


public class DVAnalysisEngineExtServiceAlarms {

    @Autowired
    private DVAnalysisEngineExtServiceAlarmsDelegate delegate;


    
    @GET
    @Path("/alarminfo")
    @Consumes({ "application/json" })
    public ResponseResult queryAlarmInfoByAlarmCsn(HttpContext context,   @QueryParam("csn")String csn)
        throws ServiceException {
        return    delegate.queryAlarmInfoByAlarmCsn(context, csn);
    }


    
    @POST
    @Path("/queryAlarmInfoById")
    @Consumes({ "application/json" })
    @Produces({ "application/json" })
    public ResponseResult queryAlarmInfoById(HttpContext context, @Valid   @NotNull() QueryAlarmInfoByIdParam queryAlarmInfoByIdParam)
        throws ServiceException {
        return    delegate.queryAlarmInfoById(context, queryAlarmInfoByIdParam);
    }


    
    @GET
    @Path("/querygeneralalarmList")
    @Consumes({ "application/json" })
    public ResponseResult queryGeneralAlarmsList(HttpContext context,   @QueryParam("neName")String neName,   @QueryParam("severity")String [] severity,   @QueryParam("clearanceStatus")String [] clearanceStatus,   @QueryParam("neType")String neType,   @QueryParam("neIp")String neIp,   @QueryParam("timeRange")String timeRange)
        throws ServiceException {
        return    delegate.queryGeneralAlarmsList(context, neName,severity,clearanceStatus,neType,neIp,timeRange);
    }


}


