package com.huawei.i2000.dvanalysisengineextservice.delegate;

import com.huawei.i2000.dvanalysisengineextservice.service.*;

import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;


import java.util.*;

import com.huawei.bsp.remoteservice.exception.ServiceException;

import com.huawei.bsp.roa.common.HttpContext;



public interface ManageObjectServiceDelegate {
  
    public abstract ResponseResult connectStatus(HttpContext context, String neName,String neType,String ip)
      throws ServiceException;
  
    public abstract ResponseResult getEventList(HttpContext context, String dn)
      throws ServiceException;
  
    public abstract ResponseResult healthStatus(HttpContext context, String neName)
      throws ServiceException;
  
    public abstract ResponseResult topoStatus(HttpContext context, String neName)
      throws ServiceException;
  
}

