package com.huawei.i2000.dvanalysisengineextservice.service;

import com.huawei.i2000.dvanalysisengineextservice.delegate.DVAnalysisEngineExtServiceIncidentDelegate;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;


import java.util.*;

import javax.validation.constraints.*;
import javax.validation.Valid;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.annotation.*;



import com.huawei.bsp.roa.common.HttpContext;



@Service("com.huawei.i2000.dvanalysisengineextservice.service.DVAnalysisEngineExtServiceIncident")
@Path("/dvanalysisengineextservice/v1/incident/aggregatealarms")
@Target("DVAnalysisEngineExtServiceIncident")


public class DVAnalysisEngineExtServiceIncident {

    @Autowired
    private DVAnalysisEngineExtServiceIncidentDelegate delegate;


    
    @POST
    @Consumes({ "application/json" })
    public ResponseResult aggregateAlarms(HttpContext context)
        throws ServiceException {
        return    delegate.aggregateAlarms(context);
    }


}


