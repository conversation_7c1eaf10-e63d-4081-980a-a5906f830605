package com.huawei.i2000.dvanalysisengineextservice.service;

import com.huawei.i2000.dvanalysisengineextservice.delegate.DVAnalysisEngineExtServiceQueryDelegate;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;


import java.util.*;

import javax.validation.constraints.*;
import javax.validation.Valid;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.annotation.*;



import com.huawei.bsp.roa.common.HttpContext;



@Service("com.huawei.i2000.dvanalysisengineextservice.service.DVAnalysisEngineExtServiceQuery")
@Path("/dvanalysisengineextservice/v1/query/pm/kpi")
@Target("DVAnalysisEngineExtServiceQuery")


public class DVAnalysisEngineExtServiceQuery {

    @Autowired
    private DVAnalysisEngineExtServiceQueryDelegate delegate;


    
    @GET
    @Consumes({ "application/json" })
    @Produces({ "application/json" })
    public ResponseResult queryPmKpi(HttpContext context,   @NotNull() @QueryParam("analysisType")String analysisType,   @QueryParam("neName")String neName,   @QueryParam("taskName")String taskName,   @QueryParam("neType")String neType,   @QueryParam("measTypeKey")String measTypeKey,   @QueryParam("measObjects")String measObjects,   @QueryParam("timeRange")String timeRange)
        throws ServiceException {
        return    delegate.queryPmKpi(context, analysisType,neName,taskName,neType,measTypeKey,measObjects,timeRange);
    }


}


