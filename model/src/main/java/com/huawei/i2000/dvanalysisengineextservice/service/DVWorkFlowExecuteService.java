package com.huawei.i2000.dvanalysisengineextservice.service;

import com.huawei.i2000.dvanalysisengineextservice.delegate.DVWorkFlowExecuteServiceDelegate;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import com.huawei.i2000.dvanalysisengineextservice.model.WorkflowExecuteRequest;
import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;


import java.util.*;

import javax.validation.constraints.*;
import javax.validation.Valid;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.annotation.*;



import com.huawei.bsp.roa.common.HttpContext;



@Service("com.huawei.i2000.dvanalysisengineextservice.service.DVWorkFlowExecuteService")
@Path("/dvanalysisengineextservice/v1/workflow/execute")
@Target("DVWorkFlowExecuteService")


public class DVWorkFlowExecuteService {

    @Autowired
    private DVWorkFlowExecuteServiceDelegate delegate;


    
    @POST
    @Consumes({ "application/json" })
    public ResponseResult executeWorkflow(HttpContext context, @Valid   @NotNull() WorkflowExecuteRequest executeWorkflow)
        throws ServiceException {
        return    delegate.executeWorkflow(context, executeWorkflow);
    }


}


