 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class RegexInfo     {
    
    
    @Valid
    
    @JsonProperty("regex")
    private String regex = null;
    
    @Valid
    
    @JsonProperty("ttl")
    private Integer ttl = 0;
    
     
    /**
    **/
    @JsonProperty("regex")
    public String getRegex() {
        return regex;
    }
    
    
    public void setRegex(String regex) {
        this.regex = regex;
    }
    
    
    /**
    **/
    @JsonProperty("ttl")
    public Integer getTtl() {
        return ttl;
    }
    
    
    public void setTtl(Integer ttl) {
        this.ttl = ttl;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class RegexInfo {\n");
        
        sb.append("  regex: ").append(regex).append("\n");
        sb.append("  ttl: ").append(ttl).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

