package com.huawei.i2000.dvanalysisengineextservice.service;

import com.huawei.i2000.dvanalysisengineextservice.delegate.ERDVAnalysisEngineExtServiceExportDelegate;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;

import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;


import java.util.*;

import javax.validation.constraints.*;
import javax.validation.Valid;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.annotation.*;



import com.huawei.bsp.roa.common.HttpContext;



@Service("com.huawei.i2000.dvanalysisengineextservice.service.ERDVAnalysisEngineExtServiceExport")
@Path("/dvanalysisengineextservice/v1/website/export")
@Target("ERDVAnalysisEngineExtServiceExport")


public class ERDVAnalysisEngineExtServiceExport {

    @Autowired
    private ERDVAnalysisEngineExtServiceExportDelegate delegate;


    
    @GET
    @Path("/managedobject/all")
    @Consumes({ "application/json" })
    public ResponseResult exportManagedObject(HttpContext context,  @Size(max = 256) @QueryParam("connectStatus")String connectStatus)
        throws ServiceException {
        return    delegate.exportManagedObject(context, connectStatus);
    }


    
    @GET
    @Path("/pm/kpi")
    @Consumes({ "application/json" })
    @Produces({ "application/json" })
    public ResponseResult exportPmKpi(HttpContext context,  @Size(max = 256) @NotNull() @QueryParam("neName")String neName,  @Size(max = 1024) @NotNull() @QueryParam("taskName")String taskName,  @Size(max = 256) @QueryParam("neType")String neType,  @Size(max = 1024) @QueryParam("measTypeKey")String measTypeKey,  @Size(max = 1024) @QueryParam("measObjects")String measObjects,  @Size(max = 256) @QueryParam("timeRange")String timeRange)
        throws ServiceException {
        return    delegate.exportPmKpi(context, neName,taskName,neType,measTypeKey,measObjects,timeRange);
    }


}


