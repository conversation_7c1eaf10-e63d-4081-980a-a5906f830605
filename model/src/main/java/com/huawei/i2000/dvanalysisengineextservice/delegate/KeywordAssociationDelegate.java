package com.huawei.i2000.dvanalysisengineextservice.delegate;

import com.huawei.i2000.dvanalysisengineextservice.service.*;

import com.huawei.i2000.dvanalysisengineextservice.model.RecommendRsp;
import com.huawei.i2000.dvanalysisengineextservice.model.RecommendReq;


import java.util.*;

import com.huawei.bsp.remoteservice.exception.ServiceException;

import com.huawei.bsp.roa.common.HttpContext;



public interface KeywordAssociationDelegate {
  
    public abstract RecommendRsp recommend(HttpContext context, RecommendReq req)
      throws ServiceException;
  
}

