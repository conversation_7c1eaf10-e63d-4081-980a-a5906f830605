 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import java.math.BigDecimal;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class PmKpiQueryModel     {
    
    
    @Valid
    
    @JsonProperty("neName")
    private String neName = null;
    
    @Valid
    
    @JsonProperty("taskId")
    private String taskId = null;
    
    @Valid
    
    @JsonProperty("timestamp")
    private BigDecimal timestamp = null;
    
     
    /**
     * 网元名称
    **/
    @JsonProperty("neName")
    public String getNeName() {
        return neName;
    }
    
    
    public void setNeName(String neName) {
        this.neName = neName;
    }
    
    
    /**
     * 国际化以后的任务名称
    **/
    @JsonProperty("taskId")
    public String getTaskId() {
        return taskId;
    }
    
    
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    
    
    /**
     * 需要导出数据的时间点
    **/
    @JsonProperty("timestamp")
    public BigDecimal getTimestamp() {
        return timestamp;
    }
    
    
    public void setTimestamp(BigDecimal timestamp) {
        this.timestamp = timestamp;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class PmKpiQueryModel {\n");
        
        sb.append("  neName: ").append(neName).append("\n");
        sb.append("  taskId: ").append(taskId).append("\n");
        sb.append("  timestamp: ").append(timestamp).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

