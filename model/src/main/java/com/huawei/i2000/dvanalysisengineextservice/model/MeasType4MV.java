 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class MeasType4MV     {
    
    
    @Valid
    
    @JsonProperty("MEASTYPE_IMG")
    private String MEASTYPE_IMG = null;
    
    @Valid
    
    @JsonProperty("SPERATOR_FLAG")
    private String SPERATOR_FLAG = "~~";
    
    @Valid
    
    @JsonProperty("measUnitKey")
    private String measUnitKey = "";
    
    @Valid
    
    @JsonProperty("name")
    private String name = "";
    
    @Valid
    
    @JsonProperty("dataType")
    private String dataType = null;
    
    @Valid
    
    @JsonProperty("indexId")
    private String indexId = null;
    
    @Valid
    
    @JsonProperty("measTypeKey")
    private String measTypeKey = "";
    
    @Valid
    
    @JsonProperty("dn")
    private String dn = "";
    
    @Valid
    
    @JsonProperty("unit")
    private String unit = "";
    
    @Valid
    
    @JsonProperty("hasMeasObj")
    private Boolean hasMeasObj = null;
    
    @Valid
    
    @JsonProperty("icon")
    private String icon = null;
    
    @Valid
    
    @JsonProperty("chkDisabled")
    private Boolean chkDisabled = null;
    
    @Valid
    
    @JsonProperty("open")
    private Boolean open = null;
    
    @Valid
    
    @JsonProperty("resourceTypeKey")
    private String resourceTypeKey = null;
    
    @Valid
    
    @JsonProperty("indexGroupName")
    private String indexGroupName = null;
    
     
    /**
    **/
    @JsonProperty("MEASTYPE_IMG")
    public String getMEASTYPEIMG() {
        return MEASTYPE_IMG;
    }
    
    
    public void setMEASTYPEIMG(String MEASTYPE_IMG) {
        this.MEASTYPE_IMG = MEASTYPE_IMG;
    }
    
    
    /**
    **/
    @JsonProperty("SPERATOR_FLAG")
    public String getSPERATORFLAG() {
        return SPERATOR_FLAG;
    }
    
    
    public void setSPERATORFLAG(String SPERATOR_FLAG) {
        this.SPERATOR_FLAG = SPERATOR_FLAG;
    }
    
    
    /**
    **/
    @JsonProperty("measUnitKey")
    public String getMeasUnitKey() {
        return measUnitKey;
    }
    
    
    public void setMeasUnitKey(String measUnitKey) {
        this.measUnitKey = measUnitKey;
    }
    
    
    /**
    **/
    @JsonProperty("name")
    public String getName() {
        return name;
    }
    
    
    public void setName(String name) {
        this.name = name;
    }
    
    
    /**
    **/
    @JsonProperty("dataType")
    public String getDataType() {
        return dataType;
    }
    
    
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    
    
    /**
    **/
    @JsonProperty("indexId")
    public String getIndexId() {
        return indexId;
    }
    
    
    public void setIndexId(String indexId) {
        this.indexId = indexId;
    }
    
    
    /**
    **/
    @JsonProperty("measTypeKey")
    public String getMeasTypeKey() {
        return measTypeKey;
    }
    
    
    public void setMeasTypeKey(String measTypeKey) {
        this.measTypeKey = measTypeKey;
    }
    
    
    /**
    **/
    @JsonProperty("dn")
    public String getDn() {
        return dn;
    }
    
    
    public void setDn(String dn) {
        this.dn = dn;
    }
    
    
    /**
    **/
    @JsonProperty("unit")
    public String getUnit() {
        return unit;
    }
    
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    
    /**
    **/
    @JsonProperty("hasMeasObj")
    public Boolean getHasMeasObj() {
        return hasMeasObj;
    }
    
    
    public void setHasMeasObj(Boolean hasMeasObj) {
        this.hasMeasObj = hasMeasObj;
    }
    
    
    /**
    **/
    @JsonProperty("icon")
    public String getIcon() {
        return icon;
    }
    
    
    public void setIcon(String icon) {
        this.icon = icon;
    }
    
    
    /**
    **/
    @JsonProperty("chkDisabled")
    public Boolean getChkDisabled() {
        return chkDisabled;
    }
    
    
    public void setChkDisabled(Boolean chkDisabled) {
        this.chkDisabled = chkDisabled;
    }
    
    
    /**
    **/
    @JsonProperty("open")
    public Boolean getOpen() {
        return open;
    }
    
    
    public void setOpen(Boolean open) {
        this.open = open;
    }
    
    
    /**
    **/
    @JsonProperty("resourceTypeKey")
    public String getResourceTypeKey() {
        return resourceTypeKey;
    }
    
    
    public void setResourceTypeKey(String resourceTypeKey) {
        this.resourceTypeKey = resourceTypeKey;
    }
    
    
    /**
    **/
    @JsonProperty("indexGroupName")
    public String getIndexGroupName() {
        return indexGroupName;
    }
    
    
    public void setIndexGroupName(String indexGroupName) {
        this.indexGroupName = indexGroupName;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class MeasType4MV {\n");
        
        sb.append("  MEASTYPE_IMG: ").append(MEASTYPE_IMG).append("\n");
        sb.append("  SPERATOR_FLAG: ").append(SPERATOR_FLAG).append("\n");
        sb.append("  measUnitKey: ").append(measUnitKey).append("\n");
        sb.append("  name: ").append(name).append("\n");
        sb.append("  dataType: ").append(dataType).append("\n");
        sb.append("  indexId: ").append(indexId).append("\n");
        sb.append("  measTypeKey: ").append(measTypeKey).append("\n");
        sb.append("  dn: ").append(dn).append("\n");
        sb.append("  unit: ").append(unit).append("\n");
        sb.append("  hasMeasObj: ").append(hasMeasObj).append("\n");
        sb.append("  icon: ").append(icon).append("\n");
        sb.append("  chkDisabled: ").append(chkDisabled).append("\n");
        sb.append("  open: ").append(open).append("\n");
        sb.append("  resourceTypeKey: ").append(resourceTypeKey).append("\n");
        sb.append("  indexGroupName: ").append(indexGroupName).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

