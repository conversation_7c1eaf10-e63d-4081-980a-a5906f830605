 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import java.util.*;

/**
 * 智能提示请求里面的Context,表示上轮对话
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class RecommendContext     {
    
    
    @Size(max = 1024)
    @Valid
    
    @JsonProperty("question")
    private String question = null;
    
    @Size(max = 5)
    @Valid
    
    @JsonProperty("answer")
    private List<String> answer = new ArrayList<String>();
    
     
    /**
     * 问题
     * maxLength: 1024
    **/
    @JsonProperty("question")
    public String getQuestion() {
        return question;
    }
    
    
    public void setQuestion(String question) {
        this.question = question;
    }
    
    
    /**
     * 答案
     * maxLength: 5
    **/
    @JsonProperty("answer")
    public List<String> getAnswer() {
        return answer;
    }
    
    
    public void setAnswer(List<String> answer) {
        this.answer = answer;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class RecommendContext {\n");
        
        sb.append("  question: ").append(question).append("\n");
        sb.append("  answer: ").append(answer).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

