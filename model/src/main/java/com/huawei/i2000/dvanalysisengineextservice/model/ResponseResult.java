 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class ResponseResult     {
    
    
    @Valid
    
    @JsonProperty("resultCode")
    private Integer resultCode = null;
    
    @Valid
    
    @JsonProperty("resultMessage")
    private String resultMessage = null;
    
    @Valid
    
    @JsonProperty("data")
    private Object data = null;
    
     
    /**
     * 0:成功,-1失败
    **/
    @JsonProperty("resultCode")
    public Integer getResultCode() {
        return resultCode;
    }
    
    
    public void setResultCode(Integer resultCode) {
        this.resultCode = resultCode;
    }
    
    
    /**
     * 在不正确的时候可以返回
    **/
    @JsonProperty("resultMessage")
    public String getResultMessage() {
        return resultMessage;
    }
    
    
    public void setResultMessage(String resultMessage) {
        this.resultMessage = resultMessage;
    }
    
    
    /**
     * 数据
    **/
    @JsonProperty("data")
    public Object getData() {
        return data;
    }
    
    
    public void setData(Object data) {
        this.data = data;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class ResponseResult {\n");
        
        sb.append("  resultCode: ").append(resultCode).append("\n");
        sb.append("  resultMessage: ").append(resultMessage).append("\n");
        sb.append("  data: ").append(data).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

