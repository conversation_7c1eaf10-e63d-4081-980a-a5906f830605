package com.huawei.i2000.dvanalysisengineextservice.delegate;

import com.huawei.i2000.dvanalysisengineextservice.service.*;

import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;


import java.util.*;

import com.huawei.bsp.remoteservice.exception.ServiceException;

import com.huawei.bsp.roa.common.HttpContext;



public interface ERDVAnalysisEngineExtServiceExportDelegate {
  
    public abstract ResponseResult exportManagedObject(HttpContext context, String connectStatus)
      throws ServiceException;
  
    public abstract ResponseResult exportPmKpi(HttpContext context, String neName,String taskName,String neType,String measTypeKey,String measObjects,String timeRange)
      throws ServiceException;
  
}

