package com.huawei.i2000.dvanalysisengineextservice.delegate;

import com.huawei.i2000.dvanalysisengineextservice.service.*;

import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;


import java.util.*;

import com.huawei.bsp.remoteservice.exception.ServiceException;

import com.huawei.bsp.roa.common.HttpContext;



public interface DVAnalysisEngineExtServiceQueryDelegate {
  
    public abstract ResponseResult queryPmKpi(HttpContext context, String analysisType,String neName,String taskName,String neType,String measTypeKey,String measObjects,String timeRange)
      throws ServiceException;
  
}

