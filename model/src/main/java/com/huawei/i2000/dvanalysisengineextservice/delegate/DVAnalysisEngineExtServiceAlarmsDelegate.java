package com.huawei.i2000.dvanalysisengineextservice.delegate;

import com.huawei.i2000.dvanalysisengineextservice.service.*;

import com.huawei.i2000.dvanalysisengineextservice.model.ResponseResult;
import com.huawei.i2000.dvanalysisengineextservice.model.QueryAlarmInfoByIdParam;


import java.util.*;

import com.huawei.bsp.remoteservice.exception.ServiceException;

import com.huawei.bsp.roa.common.HttpContext;



public interface DVAnalysisEngineExtServiceAlarmsDelegate {
  
    public abstract ResponseResult queryAlarmInfoByAlarmCsn(HttpContext context, String csn)
      throws ServiceException;
  
    public abstract ResponseResult queryAlarmInfoById(HttpContext context, QueryAlarmInfoByIdParam queryAlarmInfoByIdParam)
      throws ServiceException;
  
    public abstract ResponseResult queryGeneralAlarmsList(HttpContext context, String neName,String [] severity,String [] clearanceStatus,String neType,String neIp,String timeRange)
      throws ServiceException;
  
}

