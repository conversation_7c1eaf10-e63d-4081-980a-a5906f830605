 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import com.huawei.i2000.dvanalysisengineextservice.model.DownloadParameter;
import java.util.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class DownloadFileRequest     {
    
    
    @Valid
    
    @JsonProperty("filePath")
    private String filePath = null;
    
    @Valid
    
    @JsonProperty("downloadParameter")
    private DownloadParameter downloadParameter = null;
    
    @Valid
    
    @JsonProperty("ipList")
    private List<String> ipList = new ArrayList<String>();
    
     
    /**
    **/
    @JsonProperty("filePath")
    public String getFilePath() {
        return filePath;
    }
    
    
    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }
    
    
    /**
    **/
    @JsonProperty("downloadParameter")
    public DownloadParameter getDownloadParameter() {
        return downloadParameter;
    }
    
    
    public void setDownloadParameter(DownloadParameter downloadParameter) {
        this.downloadParameter = downloadParameter;
    }
    
    
    /**
    **/
    @JsonProperty("ipList")
    public List<String> getIpList() {
        return ipList;
    }
    
    
    public void setIpList(List<String> ipList) {
        this.ipList = ipList;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class DownloadFileRequest {\n");
        
        sb.append("  filePath: ").append(filePath).append("\n");
        sb.append("  downloadParameter: ").append(downloadParameter).append("\n");
        sb.append("  ipList: ").append(ipList).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

