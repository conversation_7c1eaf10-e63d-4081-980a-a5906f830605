 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import com.huawei.i2000.dvanalysisengineextservice.model.Paging;

/**
 * 事件查询
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class IncidentQueryParam     {
    
    
    @Valid
    
    @JsonProperty("meName")
    private String meName = null;
    
    @Valid
    
    @JsonProperty("paging")
    private Paging paging = null;
    
     
    /**
     * 告警网元名称
    **/
    @JsonProperty("meName")
    public String getMeName() {
        return meName;
    }
    
    
    public void setMeName(String meName) {
        this.meName = meName;
    }
    
    
    /**
     * 数据
    **/
    @JsonProperty("paging")
    public Paging getPaging() {
        return paging;
    }
    
    
    public void setPaging(Paging paging) {
        this.paging = paging;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class IncidentQueryParam {\n");
        
        sb.append("  meName: ").append(meName).append("\n");
        sb.append("  paging: ").append(paging).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

