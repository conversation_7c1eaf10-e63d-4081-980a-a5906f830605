 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;

/**
 * 关键词查询请求参数
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class KeywordSearchRequest     {
    
    
    public enum KeywordTypeEnum { 
    
    
         @JsonProperty("ALARM_ID") ALARM_ID,  @JsonProperty("ALARM_NAME") ALARM_NAME,  @JsonProperty("ERROR_CODE") ERROR_CODE,  @JsonProperty("MEAS_TYPE_KEY") MEAS_TYPE_KEY,  @JsonProperty("NE_TYPE") NE_TYPE,  @JsonProperty("TASK_NAME") TASK_NAME, ; 
        
        
    };
    
    @NotNull()
    
    @JsonProperty("keywordType")
    private KeywordTypeEnum keywordType = null;
    
    @Size(max = 256)
    @Valid
    
    @JsonProperty("input")
    private String input = null;
    
    @Min(value=1L)
    @Max(value=100L) 
    @Valid
    
    @JsonProperty("maxSize")
    private Integer maxSize = 10;
    
     
    /**
     * 关键词类型
    **/
    @JsonProperty("keywordType")
    public KeywordTypeEnum getKeywordType() {
        return keywordType;
    }
    
    
    public void setKeywordType(KeywordTypeEnum keywordType) {
        this.keywordType = keywordType;
    }
    
    
    /**
     * 用户输入的查询文本，用于模糊匹配
     * maxLength: 256
    **/
    @JsonProperty("input")
    public String getInput() {
        return input;
    }
    
    
    public void setInput(String input) {
        this.input = input;
    }
    
    
    /**
     * 返回结果的最大数量，默认10
     * minimum: 1.0
     * maximum: 100.0
    **/
    @JsonProperty("maxSize")
    public Integer getMaxSize() {
        return maxSize;
    }
    
    
    public void setMaxSize(Integer maxSize) {
        this.maxSize = maxSize;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class KeywordSearchRequest {\n");
        
        sb.append("  keywordType: ").append(keywordType).append("\n");
        sb.append("  input: ").append(input).append("\n");
        sb.append("  maxSize: ").append(maxSize).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

