 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class SftpUploadParam     {
    
    
    @Size(max = 512, min = 0)
    @Valid
    
    @JsonProperty("fileName")
    private String fileName = null;
    
    @Size(max = 2048, min = 0)
    @Valid
    
    @JsonProperty("fileContent")
    private String fileContent = null;
    
     
    /**
     * minLength: 0
     * maxLength: 512
    **/
    @JsonProperty("fileName")
    public String getFileName() {
        return fileName;
    }
    
    
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 2048
    **/
    @JsonProperty("fileContent")
    public String getFileContent() {
        return fileContent;
    }
    
    
    public void setFileContent(String fileContent) {
        this.fileContent = fileContent;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class SftpUploadParam {\n");
        
        sb.append("  fileName: ").append(fileName).append("\n");
        sb.append("  fileContent: ").append(fileContent).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

