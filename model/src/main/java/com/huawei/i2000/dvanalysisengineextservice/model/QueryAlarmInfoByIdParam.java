 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class QueryAlarmInfoByIdParam     {
    
    
    @Size(max = 200)
    @Valid
    
    @JsonProperty("alarmId")
    private String alarmId = null;
    
    @Size(max = 200)
    @Valid
    
    @JsonProperty("neName")
    private String neName = null;
    
     
    /**
     * maxLength: 200
    **/
    @JsonProperty("alarmId")
    public String getAlarmId() {
        return alarmId;
    }
    
    
    public void setAlarmId(String alarmId) {
        this.alarmId = alarmId;
    }
    
    
    /**
     * maxLength: 200
    **/
    @JsonProperty("neName")
    public String getNeName() {
        return neName;
    }
    
    
    public void setNeName(String neName) {
        this.neName = neName;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class QueryAlarmInfoByIdParam {\n");
        
        sb.append("  alarmId: ").append(alarmId).append("\n");
        sb.append("  neName: ").append(neName).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

