 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import java.math.BigDecimal;
import java.util.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class HistoryAggregateQueryModel     {
    
    
    @Valid
    
    @JsonProperty("pageIndex")
    private BigDecimal pageIndex = null;
    
    @Valid
    
    @JsonProperty("pageSize")
    private BigDecimal pageSize = null;
    
    @Valid
    
    @JsonProperty("beginTimestamp")
    private Long beginTimestamp = null;
    
    @Valid
    
    @JsonProperty("endTimestamp")
    private Long endTimestamp = null;
    
    @Valid
    
    @JsonProperty("moType")
    private String moType = null;
    
    @Valid
    
    @JsonProperty("measUnitKey")
    private String measUnitKey = null;
    
    @Valid
    
    @JsonProperty("measObjects")
    private List<String> measObjects = new ArrayList<String>();
    
    @Valid
    
    @JsonProperty("originalValues")
    private List<String> originalValues = new ArrayList<String>();
    
    @Valid
    
    @JsonProperty("dns")
    private List<String> dns = new ArrayList<String>();
    
    @Valid
    
    @JsonProperty("specifiedMeasTypes")
    private List<String> specifiedMeasTypes = new ArrayList<String>();
    
    @Valid
    
    @JsonProperty("autoLowerDimension")
    private Boolean autoLowerDimension = null;
    
    @Valid
    
    @JsonProperty("timeRange4LowerDimension")
    private List<Long> timeRange4LowerDimension = new ArrayList<Long>();
    
     
    /**
     * 查询页码
    **/
    @JsonProperty("pageIndex")
    public BigDecimal getPageIndex() {
        return pageIndex;
    }
    
    
    public void setPageIndex(BigDecimal pageIndex) {
        this.pageIndex = pageIndex;
    }
    
    
    /**
     * 每页查询的数据的大小
    **/
    @JsonProperty("pageSize")
    public BigDecimal getPageSize() {
        return pageSize;
    }
    
    
    public void setPageSize(BigDecimal pageSize) {
        this.pageSize = pageSize;
    }
    
    
    /**
     * 数据查询的开始时间戳
    **/
    @JsonProperty("beginTimestamp")
    public Long getBeginTimestamp() {
        return beginTimestamp;
    }
    
    
    public void setBeginTimestamp(Long beginTimestamp) {
        this.beginTimestamp = beginTimestamp;
    }
    
    
    /**
     * 数据查询的结束时间戳
    **/
    @JsonProperty("endTimestamp")
    public Long getEndTimestamp() {
        return endTimestamp;
    }
    
    
    public void setEndTimestamp(Long endTimestamp) {
        this.endTimestamp = endTimestamp;
    }
    
    
    /**
     * 网元类型
    **/
    @JsonProperty("moType")
    public String getMoType() {
        return moType;
    }
    
    
    public void setMoType(String moType) {
        this.moType = moType;
    }
    
    
    /**
     * 测量单元key
    **/
    @JsonProperty("measUnitKey")
    public String getMeasUnitKey() {
        return measUnitKey;
    }
    
    
    public void setMeasUnitKey(String measUnitKey) {
        this.measUnitKey = measUnitKey;
    }
    
    
    /**
     * 测量对象displayValue列表
    **/
    @JsonProperty("measObjects")
    public List<String> getMeasObjects() {
        return measObjects;
    }
    
    
    public void setMeasObjects(List<String> measObjects) {
        this.measObjects = measObjects;
    }
    
    
    /**
     * 测量对象originalValue列表
    **/
    @JsonProperty("originalValues")
    public List<String> getOriginalValues() {
        return originalValues;
    }
    
    
    public void setOriginalValues(List<String> originalValues) {
        this.originalValues = originalValues;
    }
    
    
    /**
     * DN列表
    **/
    @JsonProperty("dns")
    public List<String> getDns() {
        return dns;
    }
    
    
    public void setDns(List<String> dns) {
        this.dns = dns;
    }
    
    
    /**
    **/
    @JsonProperty("specifiedMeasTypes")
    public List<String> getSpecifiedMeasTypes() {
        return specifiedMeasTypes;
    }
    
    
    public void setSpecifiedMeasTypes(List<String> specifiedMeasTypes) {
        this.specifiedMeasTypes = specifiedMeasTypes;
    }
    
    
    /**
     * 是否开启自动降维
    **/
    @JsonProperty("autoLowerDimension")
    public Boolean getAutoLowerDimension() {
        return autoLowerDimension;
    }
    
    
    public void setAutoLowerDimension(Boolean autoLowerDimension) {
        this.autoLowerDimension = autoLowerDimension;
    }
    
    
    /**
     * 计算降维的时间范围，格式为：[beginTimestamp, endTimeStamp]
    **/
    @JsonProperty("timeRange4LowerDimension")
    public List<Long> getTimeRange4LowerDimension() {
        return timeRange4LowerDimension;
    }
    
    
    public void setTimeRange4LowerDimension(List<Long> timeRange4LowerDimension) {
        this.timeRange4LowerDimension = timeRange4LowerDimension;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class HistoryAggregateQueryModel {\n");
        
        sb.append("  pageIndex: ").append(pageIndex).append("\n");
        sb.append("  pageSize: ").append(pageSize).append("\n");
        sb.append("  beginTimestamp: ").append(beginTimestamp).append("\n");
        sb.append("  endTimestamp: ").append(endTimestamp).append("\n");
        sb.append("  moType: ").append(moType).append("\n");
        sb.append("  measUnitKey: ").append(measUnitKey).append("\n");
        sb.append("  measObjects: ").append(measObjects).append("\n");
        sb.append("  originalValues: ").append(originalValues).append("\n");
        sb.append("  dns: ").append(dns).append("\n");
        sb.append("  specifiedMeasTypes: ").append(specifiedMeasTypes).append("\n");
        sb.append("  autoLowerDimension: ").append(autoLowerDimension).append("\n");
        sb.append("  timeRange4LowerDimension: ").append(timeRange4LowerDimension).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

