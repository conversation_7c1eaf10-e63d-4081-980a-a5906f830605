 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import java.util.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class MeasObjectVO     {
    
    
    @Valid
    
    @JsonProperty("i2kDisplayValue")
    private String i2kDisplayValue = null;
    
    @Valid
    
    @JsonProperty("originalValue")
    private String originalValue = null;
    
    @Valid
    
    @JsonProperty("indexValues")
    private List<String> indexValues = new ArrayList<String>();
    
    @Valid
    
    @JsonProperty("i2kValues")
    private List<String> i2kValues = new ArrayList<String>();
    
    @Valid
    
    @JsonProperty("indexes")
    private List<String> indexes = new ArrayList<String>();
    
    @Valid
    
    @JsonProperty("displayValue")
    private String displayValue = null;
    
    @Valid
    
    @JsonProperty("isAddedManlly")
    private Boolean isAddedManlly = null;
    
    @Valid
    
    @JsonProperty("keys")
    private List<String> keys = new ArrayList<String>();
    
    @Valid
    
    @JsonProperty("values")
    private List<String> values = new ArrayList<String>();
    
     
    /**
    **/
    @JsonProperty("i2kDisplayValue")
    public String getI2kDisplayValue() {
        return i2kDisplayValue;
    }
    
    
    public void setI2kDisplayValue(String i2kDisplayValue) {
        this.i2kDisplayValue = i2kDisplayValue;
    }
    
    
    /**
    **/
    @JsonProperty("originalValue")
    public String getOriginalValue() {
        return originalValue;
    }
    
    
    public void setOriginalValue(String originalValue) {
        this.originalValue = originalValue;
    }
    
    
    /**
    **/
    @JsonProperty("indexValues")
    public List<String> getIndexValues() {
        return indexValues;
    }
    
    
    public void setIndexValues(List<String> indexValues) {
        this.indexValues = indexValues;
    }
    
    
    /**
    **/
    @JsonProperty("i2kValues")
    public List<String> getI2kValues() {
        return i2kValues;
    }
    
    
    public void setI2kValues(List<String> i2kValues) {
        this.i2kValues = i2kValues;
    }
    
    
    /**
    **/
    @JsonProperty("indexes")
    public List<String> getIndexes() {
        return indexes;
    }
    
    
    public void setIndexes(List<String> indexes) {
        this.indexes = indexes;
    }
    
    
    /**
    **/
    @JsonProperty("displayValue")
    public String getDisplayValue() {
        return displayValue;
    }
    
    
    public void setDisplayValue(String displayValue) {
        this.displayValue = displayValue;
    }
    
    
    /**
    **/
    @JsonProperty("isAddedManlly")
    public Boolean getIsAddedManlly() {
        return isAddedManlly;
    }
    
    
    public void setIsAddedManlly(Boolean isAddedManlly) {
        this.isAddedManlly = isAddedManlly;
    }
    
    
    /**
    **/
    @JsonProperty("keys")
    public List<String> getKeys() {
        return keys;
    }
    
    
    public void setKeys(List<String> keys) {
        this.keys = keys;
    }
    
    
    /**
    **/
    @JsonProperty("values")
    public List<String> getValues() {
        return values;
    }
    
    
    public void setValues(List<String> values) {
        this.values = values;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class MeasObjectVO {\n");
        
        sb.append("  i2kDisplayValue: ").append(i2kDisplayValue).append("\n");
        sb.append("  originalValue: ").append(originalValue).append("\n");
        sb.append("  indexValues: ").append(indexValues).append("\n");
        sb.append("  i2kValues: ").append(i2kValues).append("\n");
        sb.append("  indexes: ").append(indexes).append("\n");
        sb.append("  displayValue: ").append(displayValue).append("\n");
        sb.append("  isAddedManlly: ").append(isAddedManlly).append("\n");
        sb.append("  keys: ").append(keys).append("\n");
        sb.append("  values: ").append(values).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

