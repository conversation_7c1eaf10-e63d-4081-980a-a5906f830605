 package com.huawei.i2000.dvanalysisengineextservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class ExecuteRequest     {
    
    
    @Size(max = 1024, min = 0)
    @Valid
    
    @JsonProperty("ipList")
    private String ipList = null;
    
    @Size(max = 1024, min = 0)
    @Valid
    
    @JsonProperty("username")
    private String username = "uniagent";
    
    @Size(max = 1024, min = 0)
    @Valid
    
    @JsonProperty("executePath")
    private String executePath = null;
    
    @Size(max = 1024, min = 0)
    @Valid
    
    @JsonProperty("scriptKey")
    private String scriptKey = null;
    
    @Size(max = 1000000, min = 0)
    @Valid
    
    @JsonProperty("extAttrs")
    private String extAttrs = null;
    
     
    /**
     * minLength: 0
     * maxLength: 1024
    **/
    @JsonProperty("ipList")
    public String getIpList() {
        return ipList;
    }
    
    
    public void setIpList(String ipList) {
        this.ipList = ipList;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 1024
    **/
    @JsonProperty("username")
    public String getUsername() {
        return username;
    }
    
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 1024
    **/
    @JsonProperty("executePath")
    public String getExecutePath() {
        return executePath;
    }
    
    
    public void setExecutePath(String executePath) {
        this.executePath = executePath;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 1024
    **/
    @JsonProperty("scriptKey")
    public String getScriptKey() {
        return scriptKey;
    }
    
    
    public void setScriptKey(String scriptKey) {
        this.scriptKey = scriptKey;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 1000000
    **/
    @JsonProperty("extAttrs")
    public String getExtAttrs() {
        return extAttrs;
    }
    
    
    public void setExtAttrs(String extAttrs) {
        this.extAttrs = extAttrs;
    }
    
    

       
    
       
    
       
    
       

      
    @Override
    public String toString()  {
        StringBuilder sb = new StringBuilder();
        sb.append("class ExecuteRequest {\n");
        
        sb.append("  ipList: ").append(ipList).append("\n");
        sb.append("  username: ").append(username).append("\n");
        sb.append("  executePath: ").append(executePath).append("\n");
        sb.append("  scriptKey: ").append(scriptKey).append("\n");
        sb.append("  extAttrs: ").append(extAttrs).append("\n");
        sb.append("}\n");
        return sb.toString();
      }
}

