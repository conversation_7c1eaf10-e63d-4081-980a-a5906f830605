package com.huawei.i2000.dvanalysisengineextservice.service;

import com.huawei.i2000.dvanalysisengineextservice.delegate.DVAnalysisEngineExtServiceHealthDelegate;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;



import java.util.*;

import javax.validation.constraints.*;
import javax.validation.Valid;

import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.bsp.roa.annotation.*;



import com.huawei.bsp.roa.common.HttpContext;



@Service("com.huawei.i2000.dvanalysisengineextservice.service.DVAnalysisEngineExtServiceHealth")
@Path("/dvanalysisengineextservice/v1/healthcheck")
@Target("DVAnalysisEngineExtServiceHealth")


public class DVAnalysisEngineExtServiceHealth {

    @Autowired
    private DVAnalysisEngineExtServiceHealthDelegate delegate;


    
    @GET
    public void healthCheck(HttpContext context)
        throws ServiceException {
        delegate.healthCheck(context);
    }


}


