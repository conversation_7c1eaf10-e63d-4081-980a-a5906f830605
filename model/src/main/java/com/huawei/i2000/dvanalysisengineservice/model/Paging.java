 package com.huawei.i2000.dvanalysisengineservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;

/**
 * 页面信息
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class Paging     {
    
    
    @Min(value=0L)
    @Max(value=2147483647L) 
    @Valid
    
    @JsonProperty("total")
    private Integer total = null;
    
    @Min(value=0L)
    @Max(value=10000L) 
    @Valid
    
    @JsonProperty("pageSize")
    private Integer pageSize = null;
    
    @Min(value=0L)
    @Max(value=10000L) 
    @Valid
    
    @JsonProperty("pageNumber")
    private Integer pageNumber = null;
    
    @Size(max = 1024, min = 0)
    @Valid
    
    @JsonProperty("sortField")
    private String sortField = null;
    
    @Size(max = 1024, min = 0)
    @Valid
    
    @JsonProperty("sortType")
    private String sortType = null;
    
    @Min(value=0L)
    @Max(value=2147483647L) 
    @Valid
    
    @JsonProperty("offset")
    private Integer offset = null;
    
     
    /**
     * 总数
     * minimum: 0.0
     * maximum: 2.147483647E9
    **/
    @JsonProperty("total")
    public Integer getTotal() {
        return total;
    }
    
    
    public void setTotal(Integer total) {
        this.total = total;
    }
    
    
    /**
     * 每页数量
     * minimum: 0.0
     * maximum: 10000.0
    **/
    @JsonProperty("pageSize")
    public Integer getPageSize() {
        return pageSize;
    }
    
    
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }
    
    
    /**
     * 页数，默认1开始
     * minimum: 0.0
     * maximum: 10000.0
    **/
    @JsonProperty("pageNumber")
    public Integer getPageNumber() {
        return pageNumber;
    }
    
    
    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }
    
    
    /**
     * 分页字段
     * minLength: 0
     * maxLength: 1024
    **/
    @JsonProperty("sortField")
    public String getSortField() {
        return sortField;
    }
    
    
    public void setSortField(String sortField) {
        this.sortField = sortField;
    }
    
    
    /**
     * 分页类型（正序倒序,1,正序，2倒序）
     * minLength: 0
     * maxLength: 1024
    **/
    @JsonProperty("sortType")
    public String getSortType() {
        return sortType;
    }
    
    
    public void setSortType(String sortType) {
        this.sortType = sortType;
    }
    
    
    /**
     * 偏移量
     * minimum: 0.0
     * maximum: 2.147483647E9
    **/
    @JsonProperty("offset")
    public Integer getOffset() {
        return offset;
    }
    
    
    public void setOffset(Integer offset) {
        this.offset = offset;
    }
    
    

       
    
       
    
       
    
       

      
}

