 package com.huawei.i2000.dvanalysisengineservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import com.huawei.i2000.dvanalysisengineservice.model.HistoryIndicatorUploadEntity;
import java.util.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class HistoryIndicatorUploadParam     {
    
    
    @Valid
    
    @JsonProperty("fileEntityList")
    private List<HistoryIndicatorUploadEntity> fileEntityList = new ArrayList<HistoryIndicatorUploadEntity>();
    
     
    /**
     * pql集合
    **/
    @JsonProperty("fileEntityList")
    public List<HistoryIndicatorUploadEntity> getFileEntityList() {
        return fileEntityList;
    }
    
    
    public void setFileEntityList(List<HistoryIndicatorUploadEntity> fileEntityList) {
        this.fileEntityList = fileEntityList;
    }
    
    

       
    
       
    
       
    
       

      
}

