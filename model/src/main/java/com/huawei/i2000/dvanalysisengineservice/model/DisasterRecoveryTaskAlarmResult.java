 package com.huawei.i2000.dvanalysisengineservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class DisasterRecoveryTaskAlarmResult     {
    
    
    @Valid
    
    @JsonProperty("id")
    private String id = null;
    
    @Valid
    
    @JsonProperty("taskInstanceId")
    private String taskInstanceId = null;
    
    @Valid
    
    @JsonProperty("groupId")
    private String groupId = null;
    
    @Valid
    
    @JsonProperty("groupMemberId")
    private String groupMemberId = null;
    
    @Valid
    
    @JsonProperty("alarmId")
    private String alarmId = null;
    
    @Valid
    
    @JsonProperty("alarmDn")
    private String alarmDn = null;
    
    @Valid
    
    @JsonProperty("occurrenceTimes")
    private String occurrenceTimes = null;
    
     
    /**
    **/
    @JsonProperty("id")
    public String getId() {
        return id;
    }
    
    
    public void setId(String id) {
        this.id = id;
    }
    
    
    /**
    **/
    @JsonProperty("taskInstanceId")
    public String getTaskInstanceId() {
        return taskInstanceId;
    }
    
    
    public void setTaskInstanceId(String taskInstanceId) {
        this.taskInstanceId = taskInstanceId;
    }
    
    
    /**
    **/
    @JsonProperty("groupId")
    public String getGroupId() {
        return groupId;
    }
    
    
    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
    
    
    /**
    **/
    @JsonProperty("groupMemberId")
    public String getGroupMemberId() {
        return groupMemberId;
    }
    
    
    public void setGroupMemberId(String groupMemberId) {
        this.groupMemberId = groupMemberId;
    }
    
    
    /**
    **/
    @JsonProperty("alarmId")
    public String getAlarmId() {
        return alarmId;
    }
    
    
    public void setAlarmId(String alarmId) {
        this.alarmId = alarmId;
    }
    
    
    /**
    **/
    @JsonProperty("alarmDn")
    public String getAlarmDn() {
        return alarmDn;
    }
    
    
    public void setAlarmDn(String alarmDn) {
        this.alarmDn = alarmDn;
    }
    
    
    /**
    **/
    @JsonProperty("occurrenceTimes")
    public String getOccurrenceTimes() {
        return occurrenceTimes;
    }
    
    
    public void setOccurrenceTimes(String occurrenceTimes) {
        this.occurrenceTimes = occurrenceTimes;
    }
    
    

       
    
       
    
       
    
       

      
}

