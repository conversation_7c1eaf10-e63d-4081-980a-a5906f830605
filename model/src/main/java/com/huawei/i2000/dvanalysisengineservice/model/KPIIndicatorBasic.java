 package com.huawei.i2000.dvanalysisengineservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class KPIIndicatorBasic     {
    
    
    @Valid
    
    @JsonProperty("indicatorId")
    private String indicatorId = null;
    
    @Valid
    
    @JsonProperty("groupId")
    private String groupId = null;
    
    @Valid
    
    @JsonProperty("groupMemberId")
    private String groupMemberId = null;
    
    @Valid
    
    @JsonProperty("dn")
    private String dn = null;
    
    @Valid
    
    @JsonProperty("dnName")
    private String dnName = null;
    
    @Valid
    
    @JsonProperty("moType")
    private String moType = null;
    
    @Valid
    
    @JsonProperty("measUnitKey")
    private String measUnitKey = null;
    
    @Valid
    
    @JsonProperty("measUnitName")
    private String measUnitName = null;
    
    @Valid
    
    @JsonProperty("measTypeKey")
    private String measTypeKey = null;
    
    @Valid
    
    @JsonProperty("indexName")
    private String indexName = null;
    
    @Valid
    
    @JsonProperty("displayValue")
    private String displayValue = null;
    
    @Valid
    
    @JsonProperty("originalValue")
    private String originalValue = null;
    
    @Valid
    
    @JsonProperty("unit")
    private String unit = null;
    
    @Valid
    
    @JsonProperty("checkedNetId")
    private String checkedNetId = null;
    
    @Valid
    
    @JsonProperty("hasMeasObj")
    private String hasMeasObj = null;
    
    @Valid
    
    @JsonProperty("indexId")
    private String indexId = null;
    
    @Valid
    
    @JsonProperty("resourceTypeKey")
    private String resourceTypeKey = null;
    
    @Valid
    
    @JsonProperty("auxiliaryKPIDisplayCN")
    private String auxiliaryKPIDisplayCN = null;
    
    @Valid
    
    @JsonProperty("auxiliaryKPIDisplayEN")
    private String auxiliaryKPIDisplayEN = null;
    
    @Valid
    
    @JsonProperty("columnKey")
    private String columnKey = null;
    
    @Valid
    
    @JsonProperty("columnName")
    private String columnName = null;
    
    @Valid
    
    @JsonProperty("vnfDn")
    private String vnfDn = null;
    
    @Valid
    
    @JsonProperty("vnfDnName")
    private String vnfDnName = null;
    
    @Valid
    
    @JsonProperty("belongIndicatorId")
    private String belongIndicatorId = null;
    
    @Valid
    
    @JsonProperty("isDisplay")
    private Boolean isDisplay = true;
    
     
    /**
    **/
    @JsonProperty("indicatorId")
    public String getIndicatorId() {
        return indicatorId;
    }
    
    
    public void setIndicatorId(String indicatorId) {
        this.indicatorId = indicatorId;
    }
    
    
    /**
    **/
    @JsonProperty("groupId")
    public String getGroupId() {
        return groupId;
    }
    
    
    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }
    
    
    /**
    **/
    @JsonProperty("groupMemberId")
    public String getGroupMemberId() {
        return groupMemberId;
    }
    
    
    public void setGroupMemberId(String groupMemberId) {
        this.groupMemberId = groupMemberId;
    }
    
    
    /**
    **/
    @JsonProperty("dn")
    public String getDn() {
        return dn;
    }
    
    
    public void setDn(String dn) {
        this.dn = dn;
    }
    
    
    /**
    **/
    @JsonProperty("dnName")
    public String getDnName() {
        return dnName;
    }
    
    
    public void setDnName(String dnName) {
        this.dnName = dnName;
    }
    
    
    /**
    **/
    @JsonProperty("moType")
    public String getMoType() {
        return moType;
    }
    
    
    public void setMoType(String moType) {
        this.moType = moType;
    }
    
    
    /**
    **/
    @JsonProperty("measUnitKey")
    public String getMeasUnitKey() {
        return measUnitKey;
    }
    
    
    public void setMeasUnitKey(String measUnitKey) {
        this.measUnitKey = measUnitKey;
    }
    
    
    /**
    **/
    @JsonProperty("measUnitName")
    public String getMeasUnitName() {
        return measUnitName;
    }
    
    
    public void setMeasUnitName(String measUnitName) {
        this.measUnitName = measUnitName;
    }
    
    
    /**
    **/
    @JsonProperty("measTypeKey")
    public String getMeasTypeKey() {
        return measTypeKey;
    }
    
    
    public void setMeasTypeKey(String measTypeKey) {
        this.measTypeKey = measTypeKey;
    }
    
    
    /**
    **/
    @JsonProperty("indexName")
    public String getIndexName() {
        return indexName;
    }
    
    
    public void setIndexName(String indexName) {
        this.indexName = indexName;
    }
    
    
    /**
    **/
    @JsonProperty("displayValue")
    public String getDisplayValue() {
        return displayValue;
    }
    
    
    public void setDisplayValue(String displayValue) {
        this.displayValue = displayValue;
    }
    
    
    /**
    **/
    @JsonProperty("originalValue")
    public String getOriginalValue() {
        return originalValue;
    }
    
    
    public void setOriginalValue(String originalValue) {
        this.originalValue = originalValue;
    }
    
    
    /**
    **/
    @JsonProperty("unit")
    public String getUnit() {
        return unit;
    }
    
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    
    /**
    **/
    @JsonProperty("checkedNetId")
    public String getCheckedNetId() {
        return checkedNetId;
    }
    
    
    public void setCheckedNetId(String checkedNetId) {
        this.checkedNetId = checkedNetId;
    }
    
    
    /**
    **/
    @JsonProperty("hasMeasObj")
    public String getHasMeasObj() {
        return hasMeasObj;
    }
    
    
    public void setHasMeasObj(String hasMeasObj) {
        this.hasMeasObj = hasMeasObj;
    }
    
    
    /**
    **/
    @JsonProperty("indexId")
    public String getIndexId() {
        return indexId;
    }
    
    
    public void setIndexId(String indexId) {
        this.indexId = indexId;
    }
    
    
    /**
    **/
    @JsonProperty("resourceTypeKey")
    public String getResourceTypeKey() {
        return resourceTypeKey;
    }
    
    
    public void setResourceTypeKey(String resourceTypeKey) {
        this.resourceTypeKey = resourceTypeKey;
    }
    
    
    /**
    **/
    @JsonProperty("auxiliaryKPIDisplayCN")
    public String getAuxiliaryKPIDisplayCN() {
        return auxiliaryKPIDisplayCN;
    }
    
    
    public void setAuxiliaryKPIDisplayCN(String auxiliaryKPIDisplayCN) {
        this.auxiliaryKPIDisplayCN = auxiliaryKPIDisplayCN;
    }
    
    
    /**
    **/
    @JsonProperty("auxiliaryKPIDisplayEN")
    public String getAuxiliaryKPIDisplayEN() {
        return auxiliaryKPIDisplayEN;
    }
    
    
    public void setAuxiliaryKPIDisplayEN(String auxiliaryKPIDisplayEN) {
        this.auxiliaryKPIDisplayEN = auxiliaryKPIDisplayEN;
    }
    
    
    /**
    **/
    @JsonProperty("columnKey")
    public String getColumnKey() {
        return columnKey;
    }
    
    
    public void setColumnKey(String columnKey) {
        this.columnKey = columnKey;
    }
    
    
    /**
    **/
    @JsonProperty("columnName")
    public String getColumnName() {
        return columnName;
    }
    
    
    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }
    
    
    /**
    **/
    @JsonProperty("vnfDn")
    public String getVnfDn() {
        return vnfDn;
    }
    
    
    public void setVnfDn(String vnfDn) {
        this.vnfDn = vnfDn;
    }
    
    
    /**
    **/
    @JsonProperty("vnfDnName")
    public String getVnfDnName() {
        return vnfDnName;
    }
    
    
    public void setVnfDnName(String vnfDnName) {
        this.vnfDnName = vnfDnName;
    }
    
    
    /**
    **/
    @JsonProperty("belongIndicatorId")
    public String getBelongIndicatorId() {
        return belongIndicatorId;
    }
    
    
    public void setBelongIndicatorId(String belongIndicatorId) {
        this.belongIndicatorId = belongIndicatorId;
    }
    
    
    /**
    **/
    @JsonProperty("isDisplay")
    public Boolean getIsDisplay() {
        return isDisplay;
    }
    
    
    public void setIsDisplay(Boolean isDisplay) {
        this.isDisplay = isDisplay;
    }
    
    

       
    
       
    
       
    
       

      
}

