 package com.huawei.i2000.dvanalysisengineservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import java.util.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class QueryPerformanceDataParam     {
    
    
    @Size(max = 256, min = 0)
    @Valid
    
    @JsonProperty("id")
    private String id = null;
    
    @Size(max = 256, min = 0)
    @Valid
    
    @JsonProperty("taskId")
    private String taskId = null;
    
    @Size(max = 1, min = 0)
    @Valid
    
    @JsonProperty("timeRangeRadioId")
    private String timeRangeRadioId = null;
    
    @Size(max = 2, min = 0)
    @Valid
    
    @JsonProperty("customTimes")
    private List<Long> customTimes = new ArrayList<Long>();
    
     
    /**
     * minLength: 0
     * maxLength: 256
    **/
    @JsonProperty("id")
    public String getId() {
        return id;
    }
    
    
    public void setId(String id) {
        this.id = id;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 256
    **/
    @JsonProperty("taskId")
    public String getTaskId() {
        return taskId;
    }
    
    
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 1
    **/
    @JsonProperty("timeRangeRadioId")
    public String getTimeRangeRadioId() {
        return timeRangeRadioId;
    }
    
    
    public void setTimeRangeRadioId(String timeRangeRadioId) {
        this.timeRangeRadioId = timeRangeRadioId;
    }
    
    
    /**
     * minLength: 0
     * maxLength: 2
    **/
    @JsonProperty("customTimes")
    public List<Long> getCustomTimes() {
        return customTimes;
    }
    
    
    public void setCustomTimes(List<Long> customTimes) {
        this.customTimes = customTimes;
    }
    
    

       
    
       
    
       
    
       

      
}

