 package com.huawei.i2000.dvanalysisengineservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;

/**
 * 概念漂移信息
 **/

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class ConceptDrift     {
    
    
    @Valid
    
    @JsonProperty("id")
    private Integer id = null;
    
    @Valid
    
    @JsonProperty("taskId")
    private Integer taskId = null;
    
    @Valid
    
    @JsonProperty("indicatorId")
    private String indicatorId = null;
    
    @Valid
    
    @JsonProperty("startTime")
    private Long startTime = null;
    
    @Valid
    
    @JsonProperty("endTime")
    private Long endTime = null;
    
    @Valid
    
    @JsonProperty("correct")
    private Integer correct = 0;
    
    @Valid
    
    @JsonProperty("retrain")
    private Integer retrain = 0;
    
    @Valid
    
    @JsonProperty("isFirst")
    private Boolean isFirst = null;
    
     
    /**
    **/
    @JsonProperty("id")
    public Integer getId() {
        return id;
    }
    
    
    public void setId(Integer id) {
        this.id = id;
    }
    
    
    /**
    **/
    @JsonProperty("taskId")
    public Integer getTaskId() {
        return taskId;
    }
    
    
    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }
    
    
    /**
    **/
    @JsonProperty("indicatorId")
    public String getIndicatorId() {
        return indicatorId;
    }
    
    
    public void setIndicatorId(String indicatorId) {
        this.indicatorId = indicatorId;
    }
    
    
    /**
    **/
    @JsonProperty("startTime")
    public Long getStartTime() {
        return startTime;
    }
    
    
    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }
    
    
    /**
    **/
    @JsonProperty("endTime")
    public Long getEndTime() {
        return endTime;
    }
    
    
    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }
    
    
    /**
    **/
    @JsonProperty("correct")
    public Integer getCorrect() {
        return correct;
    }
    
    
    public void setCorrect(Integer correct) {
        this.correct = correct;
    }
    
    
    /**
    **/
    @JsonProperty("retrain")
    public Integer getRetrain() {
        return retrain;
    }
    
    
    public void setRetrain(Integer retrain) {
        this.retrain = retrain;
    }
    
    
    /**
    **/
    @JsonProperty("isFirst")
    public Boolean getIsFirst() {
        return isFirst;
    }
    
    
    public void setIsFirst(Boolean isFirst) {
        this.isFirst = isFirst;
    }
    
    

       
    
       
    
       
    
       

      
}

