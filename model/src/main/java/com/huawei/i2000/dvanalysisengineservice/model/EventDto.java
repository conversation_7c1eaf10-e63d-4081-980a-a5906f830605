 package com.huawei.i2000.dvanalysisengineservice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class EventDto     {
    
    
    @Valid
    
    @JsonProperty("eventId")
    private String eventId = null;
    
    @Valid
    
    @JsonProperty("csn")
    private Long csn = null;
    
    @Valid
    
    @JsonProperty("tenantId")
    private String tenantId = null;
    
    @Valid
    
    @JsonProperty("incidentId")
    private String incidentId = null;
    
    @Valid
    
    @JsonProperty("domain")
    private String domain = null;
    
    @Valid
    
    @JsonProperty("type")
    private String type = null;
    
    @Valid
    
    @JsonProperty("status")
    private String status = null;
    
    @Valid
    
    @JsonProperty("arriveTime")
    private Long arriveTime = null;
    
    @Valid
    
    @JsonProperty("occurTime")
    private Long occurTime = null;
    
    @Valid
    
    @JsonProperty("clearTime")
    private Long clearTime = null;
    
    @Valid
    
    @JsonProperty("severity")
    private String severity = null;
    
    @Valid
    
    @JsonProperty("name")
    private String name = null;
    
    @Valid
    
    @JsonProperty("sourceObjType")
    private String sourceObjType = null;
    
    @Valid
    
    @JsonProperty("sourceObjId")
    private String sourceObjId = null;
    
    @Valid
    
    @JsonProperty("sourceObjName")
    private String sourceObjName = null;
    
    @Valid
    
    @JsonProperty("description")
    private String description = null;
    
    @Valid
    
    @JsonProperty("sourceSystem")
    private String sourceSystem = null;
    
    @Valid
    
    @JsonProperty("sourceCsn")
    private Long sourceCsn = null;
    
    @Valid
    
    @JsonProperty("rootEventProbability")
    private Double rootEventProbability = null;
    
    @Valid
    
    @JsonProperty("meType")
    private String meType = null;
    
    @Valid
    
    @JsonProperty("moDn")
    private String moDn = null;
    
    @Valid
    
    @JsonProperty("nativeMoDn")
    private String nativeMoDn = null;
    
    @Valid
    
    @JsonProperty("moName")
    private String moName = null;
    
    @Valid
    
    @JsonProperty("moType")
    private String moType = null;
    
    @Valid
    
    @JsonProperty("rawData")
    private String rawData = null;
    
    @Valid
    
    @JsonProperty("nodeType")
    private Integer nodeType = null;
    
     
    /**
    **/
    @JsonProperty("eventId")
    public String getEventId() {
        return eventId;
    }
    
    
    public void setEventId(String eventId) {
        this.eventId = eventId;
    }
    
    
    /**
    **/
    @JsonProperty("csn")
    public Long getCsn() {
        return csn;
    }
    
    
    public void setCsn(Long csn) {
        this.csn = csn;
    }
    
    
    /**
    **/
    @JsonProperty("tenantId")
    public String getTenantId() {
        return tenantId;
    }
    
    
    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    
    
    /**
    **/
    @JsonProperty("incidentId")
    public String getIncidentId() {
        return incidentId;
    }
    
    
    public void setIncidentId(String incidentId) {
        this.incidentId = incidentId;
    }
    
    
    /**
    **/
    @JsonProperty("domain")
    public String getDomain() {
        return domain;
    }
    
    
    public void setDomain(String domain) {
        this.domain = domain;
    }
    
    
    /**
    **/
    @JsonProperty("type")
    public String getType() {
        return type;
    }
    
    
    public void setType(String type) {
        this.type = type;
    }
    
    
    /**
    **/
    @JsonProperty("status")
    public String getStatus() {
        return status;
    }
    
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    
    /**
    **/
    @JsonProperty("arriveTime")
    public Long getArriveTime() {
        return arriveTime;
    }
    
    
    public void setArriveTime(Long arriveTime) {
        this.arriveTime = arriveTime;
    }
    
    
    /**
    **/
    @JsonProperty("occurTime")
    public Long getOccurTime() {
        return occurTime;
    }
    
    
    public void setOccurTime(Long occurTime) {
        this.occurTime = occurTime;
    }
    
    
    /**
    **/
    @JsonProperty("clearTime")
    public Long getClearTime() {
        return clearTime;
    }
    
    
    public void setClearTime(Long clearTime) {
        this.clearTime = clearTime;
    }
    
    
    /**
    **/
    @JsonProperty("severity")
    public String getSeverity() {
        return severity;
    }
    
    
    public void setSeverity(String severity) {
        this.severity = severity;
    }
    
    
    /**
    **/
    @JsonProperty("name")
    public String getName() {
        return name;
    }
    
    
    public void setName(String name) {
        this.name = name;
    }
    
    
    /**
    **/
    @JsonProperty("sourceObjType")
    public String getSourceObjType() {
        return sourceObjType;
    }
    
    
    public void setSourceObjType(String sourceObjType) {
        this.sourceObjType = sourceObjType;
    }
    
    
    /**
    **/
    @JsonProperty("sourceObjId")
    public String getSourceObjId() {
        return sourceObjId;
    }
    
    
    public void setSourceObjId(String sourceObjId) {
        this.sourceObjId = sourceObjId;
    }
    
    
    /**
    **/
    @JsonProperty("sourceObjName")
    public String getSourceObjName() {
        return sourceObjName;
    }
    
    
    public void setSourceObjName(String sourceObjName) {
        this.sourceObjName = sourceObjName;
    }
    
    
    /**
    **/
    @JsonProperty("description")
    public String getDescription() {
        return description;
    }
    
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    
    /**
    **/
    @JsonProperty("sourceSystem")
    public String getSourceSystem() {
        return sourceSystem;
    }
    
    
    public void setSourceSystem(String sourceSystem) {
        this.sourceSystem = sourceSystem;
    }
    
    
    /**
    **/
    @JsonProperty("sourceCsn")
    public Long getSourceCsn() {
        return sourceCsn;
    }
    
    
    public void setSourceCsn(Long sourceCsn) {
        this.sourceCsn = sourceCsn;
    }
    
    
    /**
    **/
    @JsonProperty("rootEventProbability")
    public Double getRootEventProbability() {
        return rootEventProbability;
    }
    
    
    public void setRootEventProbability(Double rootEventProbability) {
        this.rootEventProbability = rootEventProbability;
    }
    
    
    /**
    **/
    @JsonProperty("meType")
    public String getMeType() {
        return meType;
    }
    
    
    public void setMeType(String meType) {
        this.meType = meType;
    }
    
    
    /**
    **/
    @JsonProperty("moDn")
    public String getMoDn() {
        return moDn;
    }
    
    
    public void setMoDn(String moDn) {
        this.moDn = moDn;
    }
    
    
    /**
    **/
    @JsonProperty("nativeMoDn")
    public String getNativeMoDn() {
        return nativeMoDn;
    }
    
    
    public void setNativeMoDn(String nativeMoDn) {
        this.nativeMoDn = nativeMoDn;
    }
    
    
    /**
    **/
    @JsonProperty("moName")
    public String getMoName() {
        return moName;
    }
    
    
    public void setMoName(String moName) {
        this.moName = moName;
    }
    
    
    /**
    **/
    @JsonProperty("moType")
    public String getMoType() {
        return moType;
    }
    
    
    public void setMoType(String moType) {
        this.moType = moType;
    }
    
    
    /**
    **/
    @JsonProperty("rawData")
    public String getRawData() {
        return rawData;
    }
    
    
    public void setRawData(String rawData) {
        this.rawData = rawData;
    }
    
    
    /**
    **/
    @JsonProperty("nodeType")
    public Integer getNodeType() {
        return nodeType;
    }
    
    
    public void setNodeType(Integer nodeType) {
        this.nodeType = nodeType;
    }
    
    

       
    
       
    
       
    
       

      
}

