 package com.huawei.i2000.dvtoposervice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import com.huawei.i2000.dvtoposervice.model.BusinessIndicator;
import com.huawei.i2000.dvtoposervice.model.StripGroup;
import java.util.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class StripeTeam     {
    
    
    @Valid
    
    @JsonProperty("stripeTeamId")
    private Integer stripeTeamId = null;
    
    @Valid
    
    @JsonProperty("stripeTeamName")
    private String stripeTeamName = null;
    
    @Valid
    
    @JsonProperty("solutionId")
    private Integer solutionId = null;
    
    @Valid
    
    @JsonProperty("isManage")
    private Boolean isManage = null;
    
    @Valid
    
    @JsonProperty("groupList")
    private List<StripGroup> groupList = new ArrayList<StripGroup>();
    
    @Valid
    
    @JsonProperty("indicatorList")
    private List<BusinessIndicator> indicatorList = new ArrayList<BusinessIndicator>();
    
     
    /**
    **/
    @JsonProperty("stripeTeamId")
    public Integer getStripeTeamId() {
        return stripeTeamId;
    }
    
    
    public void setStripeTeamId(Integer stripeTeamId) {
        this.stripeTeamId = stripeTeamId;
    }
    
    
    /**
    **/
    @JsonProperty("stripeTeamName")
    public String getStripeTeamName() {
        return stripeTeamName;
    }
    
    
    public void setStripeTeamName(String stripeTeamName) {
        this.stripeTeamName = stripeTeamName;
    }
    
    
    /**
    **/
    @JsonProperty("solutionId")
    public Integer getSolutionId() {
        return solutionId;
    }
    
    
    public void setSolutionId(Integer solutionId) {
        this.solutionId = solutionId;
    }
    
    
    /**
    **/
    @JsonProperty("isManage")
    public Boolean getIsManage() {
        return isManage;
    }
    
    
    public void setIsManage(Boolean isManage) {
        this.isManage = isManage;
    }
    
    
    /**
    **/
    @JsonProperty("groupList")
    public List<StripGroup> getGroupList() {
        return groupList;
    }
    
    
    public void setGroupList(List<StripGroup> groupList) {
        this.groupList = groupList;
    }
    
    
    /**
    **/
    @JsonProperty("indicatorList")
    public List<BusinessIndicator> getIndicatorList() {
        return indicatorList;
    }
    
    
    public void setIndicatorList(List<BusinessIndicator> indicatorList) {
        this.indicatorList = indicatorList;
    }
    
    

       
    
       
    
       
    
       

      
}

