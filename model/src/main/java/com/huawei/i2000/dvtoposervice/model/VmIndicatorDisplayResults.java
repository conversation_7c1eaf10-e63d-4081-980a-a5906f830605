 package com.huawei.i2000.dvtoposervice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import com.huawei.i2000.dvtoposervice.model.IndicatorIdName;
import com.huawei.i2000.dvtoposervice.model.PodViewHistoryResults;
import java.util.*;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class VmIndicatorDisplayResults     {
    
    
    @Valid
    
    @JsonProperty("cpuNum")
    private Integer cpuNum = null;
    
    @Valid
    
    @JsonProperty("vmIp")
    private String vmIp = null;
    
    @Valid
    
    @JsonProperty("availableStatus")
    private String availableStatus = null;
    
    @Valid
    
    @JsonProperty("csnState")
    private Integer csnState = null;
    
    @Valid
    
    @JsonProperty("vmName")
    private String vmName = null;
    
    @Valid
    
    @JsonProperty("memoryTotal")
    private String memoryTotal = null;
    
    @Valid
    
    @JsonProperty("memoryIndex")
    private String memoryIndex = null;
    
    @Valid
    
    @JsonProperty("memoryUsageRate")
    private String memoryUsageRate = null;
    
    @Valid
    
    @JsonProperty("memoryUsageRateIndex")
    private String memoryUsageRateIndex = null;
    
    @Valid
    
    @JsonProperty("vmIndicatorList")
    private List<PodViewHistoryResults> vmIndicatorList = new ArrayList<PodViewHistoryResults>();
    
    @Valid
    
    @JsonProperty("indicatorIdList")
    private List<IndicatorIdName> indicatorIdList = new ArrayList<IndicatorIdName>();
    
     
    /**
    **/
    @JsonProperty("cpuNum")
    public Integer getCpuNum() {
        return cpuNum;
    }
    
    
    public void setCpuNum(Integer cpuNum) {
        this.cpuNum = cpuNum;
    }
    
    
    /**
    **/
    @JsonProperty("vmIp")
    public String getVmIp() {
        return vmIp;
    }
    
    
    public void setVmIp(String vmIp) {
        this.vmIp = vmIp;
    }
    
    
    /**
    **/
    @JsonProperty("availableStatus")
    public String getAvailableStatus() {
        return availableStatus;
    }
    
    
    public void setAvailableStatus(String availableStatus) {
        this.availableStatus = availableStatus;
    }
    
    
    /**
    **/
    @JsonProperty("csnState")
    public Integer getCsnState() {
        return csnState;
    }
    
    
    public void setCsnState(Integer csnState) {
        this.csnState = csnState;
    }
    
    
    /**
    **/
    @JsonProperty("vmName")
    public String getVmName() {
        return vmName;
    }
    
    
    public void setVmName(String vmName) {
        this.vmName = vmName;
    }
    
    
    /**
    **/
    @JsonProperty("memoryTotal")
    public String getMemoryTotal() {
        return memoryTotal;
    }
    
    
    public void setMemoryTotal(String memoryTotal) {
        this.memoryTotal = memoryTotal;
    }
    
    
    /**
    **/
    @JsonProperty("memoryIndex")
    public String getMemoryIndex() {
        return memoryIndex;
    }
    
    
    public void setMemoryIndex(String memoryIndex) {
        this.memoryIndex = memoryIndex;
    }
    
    
    /**
    **/
    @JsonProperty("memoryUsageRate")
    public String getMemoryUsageRate() {
        return memoryUsageRate;
    }
    
    
    public void setMemoryUsageRate(String memoryUsageRate) {
        this.memoryUsageRate = memoryUsageRate;
    }
    
    
    /**
    **/
    @JsonProperty("memoryUsageRateIndex")
    public String getMemoryUsageRateIndex() {
        return memoryUsageRateIndex;
    }
    
    
    public void setMemoryUsageRateIndex(String memoryUsageRateIndex) {
        this.memoryUsageRateIndex = memoryUsageRateIndex;
    }
    
    
    /**
    **/
    @JsonProperty("vmIndicatorList")
    public List<PodViewHistoryResults> getVmIndicatorList() {
        return vmIndicatorList;
    }
    
    
    public void setVmIndicatorList(List<PodViewHistoryResults> vmIndicatorList) {
        this.vmIndicatorList = vmIndicatorList;
    }
    
    
    /**
    **/
    @JsonProperty("indicatorIdList")
    public List<IndicatorIdName> getIndicatorIdList() {
        return indicatorIdList;
    }
    
    
    public void setIndicatorIdList(List<IndicatorIdName> indicatorIdList) {
        this.indicatorIdList = indicatorIdList;
    }
    
    

       
    
       
    
       
    
       

      
}

