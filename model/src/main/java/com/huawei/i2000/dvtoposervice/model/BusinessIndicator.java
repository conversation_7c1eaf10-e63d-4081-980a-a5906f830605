 package com.huawei.i2000.dvtoposervice.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.annotation.JsonIgnore;import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlTransient;
import java.math.BigDecimal;
import java.io.Serializable;

import javax.validation.Valid;
import javax.validation.constraints.*;
import javax.validation.groups.*;
import com.huawei.i2000.dvtoposervice.model.Indicator;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonAutoDetect(fieldVisibility = Visibility.ANY, getterVisibility = Visibility.NONE, setterVisibility = Visibility.NONE)
public class BusinessIndicator extends Indicator    {
    
    
    @Valid
    
    @JsonProperty("measUnitKey")
    private String measUnitKey = null;
    
    @Valid
    
    @JsonProperty("hasMeasObj")
    private String hasMeasObj = null;
    
    @Valid
    
    @JsonProperty("indicatorDisplayType")
    private Integer indicatorDisplayType = null;
    
    @Valid
    
    @JsonProperty("indicatorSortNumber")
    private Integer indicatorSortNumber = null;
    
    @Valid
    
    @JsonProperty("businessId")
    private Integer businessId = null;
    
    @Valid
    
    @JsonProperty("siteName")
    private String siteName = null;
    
    @Valid
    
    @JsonProperty("threshold")
    private Double threshold = null;
    
    @Valid
    
    @JsonProperty("dn")
    private String dn = null;
    
    @Valid
    
    @JsonProperty("measUnitName")
    private String measUnitName = null;
    
    @Valid
    
    @JsonProperty("moName")
    private String moName = null;
    
    @Valid
    
    @JsonProperty("aggrType")
    private Integer aggrType = null;
    
    @Valid
    
    @JsonProperty("indicatorId")
    private String indicatorId = null;
    
    @Valid
    
    @JsonProperty("instanceId")
    private Integer instanceId = null;
    
    @Valid
    
    @JsonProperty("measTypeKey")
    private String measTypeKey = null;
    
    @Valid
    
    @JsonProperty("indicatorStatus")
    private Boolean indicatorStatus = null;
    
    @Valid
    
    @JsonProperty("indexKey")
    private String indexKey = null;
    
    @Valid
    
    @JsonProperty("indexId")
    private String indexId = null;
    
    @Valid
    
    @JsonProperty("isAggregateByAllSolution")
    private Boolean isAggregateByAllSolution = null;
    
    @Valid
    
    @JsonProperty("needAggregation")
    private Integer needAggregation = null;
    
    @Valid
    
    @JsonProperty("dnName")
    private String dnName = null;
    
    @Valid
    
    @JsonProperty("indexName")
    private String indexName = null;
    
    @Valid
    
    @JsonProperty("IP")
    private String IP = null;
    
    @Valid
    
    @JsonProperty("originalValue")
    private String originalValue = null;
    
    @Valid
    
    @JsonProperty("indicatorCsnList")
    private String indicatorCsnList = null;
    
    @Valid
    
    @JsonProperty("displayValue")
    private String displayValue = null;
    
    @Valid
    
    @JsonProperty("unit")
    private String unit = null;
    
    @Valid
    
    @JsonProperty("moType")
    private String moType = null;
    
    @Valid
    
    @JsonProperty("siteId")
    private String siteId = null;
    
    @Valid
    
    @JsonProperty("solutionId")
    private Integer solutionId = null;
    
     
    /**
    **/
    @JsonProperty("measUnitKey")
    public String getMeasUnitKey() {
        return measUnitKey;
    }
    
    
    public void setMeasUnitKey(String measUnitKey) {
        this.measUnitKey = measUnitKey;
    }
    
    
    /**
    **/
    @JsonProperty("hasMeasObj")
    public String getHasMeasObj() {
        return hasMeasObj;
    }
    
    
    public void setHasMeasObj(String hasMeasObj) {
        this.hasMeasObj = hasMeasObj;
    }
    
    
    /**
    **/
    @JsonProperty("indicatorDisplayType")
    public Integer getIndicatorDisplayType() {
        return indicatorDisplayType;
    }
    
    
    public void setIndicatorDisplayType(Integer indicatorDisplayType) {
        this.indicatorDisplayType = indicatorDisplayType;
    }
    
    
    /**
    **/
    @JsonProperty("indicatorSortNumber")
    public Integer getIndicatorSortNumber() {
        return indicatorSortNumber;
    }
    
    
    public void setIndicatorSortNumber(Integer indicatorSortNumber) {
        this.indicatorSortNumber = indicatorSortNumber;
    }
    
    
    /**
    **/
    @JsonProperty("businessId")
    public Integer getBusinessId() {
        return businessId;
    }
    
    
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }
    
    
    /**
    **/
    @JsonProperty("siteName")
    public String getSiteName() {
        return siteName;
    }
    
    
    public void setSiteName(String siteName) {
        this.siteName = siteName;
    }
    
    
    /**
    **/
    @JsonProperty("threshold")
    public Double getThreshold() {
        return threshold;
    }
    
    
    public void setThreshold(Double threshold) {
        this.threshold = threshold;
    }
    
    
    /**
    **/
    @JsonProperty("dn")
    public String getDn() {
        return dn;
    }
    
    
    public void setDn(String dn) {
        this.dn = dn;
    }
    
    
    /**
    **/
    @JsonProperty("measUnitName")
    public String getMeasUnitName() {
        return measUnitName;
    }
    
    
    public void setMeasUnitName(String measUnitName) {
        this.measUnitName = measUnitName;
    }
    
    
    /**
    **/
    @JsonProperty("moName")
    public String getMoName() {
        return moName;
    }
    
    
    public void setMoName(String moName) {
        this.moName = moName;
    }
    
    
    /**
    **/
    @JsonProperty("aggrType")
    public Integer getAggrType() {
        return aggrType;
    }
    
    
    public void setAggrType(Integer aggrType) {
        this.aggrType = aggrType;
    }
    
    
    /**
    **/
    @JsonProperty("indicatorId")
    public String getIndicatorId() {
        return indicatorId;
    }
    
    
    public void setIndicatorId(String indicatorId) {
        this.indicatorId = indicatorId;
    }
    
    
    /**
    **/
    @JsonProperty("instanceId")
    public Integer getInstanceId() {
        return instanceId;
    }
    
    
    public void setInstanceId(Integer instanceId) {
        this.instanceId = instanceId;
    }
    
    
    /**
    **/
    @JsonProperty("measTypeKey")
    public String getMeasTypeKey() {
        return measTypeKey;
    }
    
    
    public void setMeasTypeKey(String measTypeKey) {
        this.measTypeKey = measTypeKey;
    }
    
    
    /**
    **/
    @JsonProperty("indicatorStatus")
    public Boolean getIndicatorStatus() {
        return indicatorStatus;
    }
    
    
    public void setIndicatorStatus(Boolean indicatorStatus) {
        this.indicatorStatus = indicatorStatus;
    }
    
    
    /**
    **/
    @JsonProperty("indexKey")
    public String getIndexKey() {
        return indexKey;
    }
    
    
    public void setIndexKey(String indexKey) {
        this.indexKey = indexKey;
    }
    
    
    /**
    **/
    @JsonProperty("indexId")
    public String getIndexId() {
        return indexId;
    }
    
    
    public void setIndexId(String indexId) {
        this.indexId = indexId;
    }
    
    
    /**
    **/
    @JsonProperty("isAggregateByAllSolution")
    public Boolean getIsAggregateByAllSolution() {
        return isAggregateByAllSolution;
    }
    
    
    public void setIsAggregateByAllSolution(Boolean isAggregateByAllSolution) {
        this.isAggregateByAllSolution = isAggregateByAllSolution;
    }
    
    
    /**
     * Whether aggregation is required
    **/
    @JsonProperty("needAggregation")
    public Integer getNeedAggregation() {
        return needAggregation;
    }
    
    
    public void setNeedAggregation(Integer needAggregation) {
        this.needAggregation = needAggregation;
    }
    
    
    /**
    **/
    @JsonProperty("dnName")
    public String getDnName() {
        return dnName;
    }
    
    
    public void setDnName(String dnName) {
        this.dnName = dnName;
    }
    
    
    /**
    **/
    @JsonProperty("indexName")
    public String getIndexName() {
        return indexName;
    }
    
    
    public void setIndexName(String indexName) {
        this.indexName = indexName;
    }
    
    
    /**
    **/
    @JsonProperty("IP")
    public String getIP() {
        return IP;
    }
    
    
    public void setIP(String IP) {
        this.IP = IP;
    }
    
    
    /**
    **/
    @JsonProperty("originalValue")
    public String getOriginalValue() {
        return originalValue;
    }
    
    
    public void setOriginalValue(String originalValue) {
        this.originalValue = originalValue;
    }
    
    
    /**
    **/
    @JsonProperty("indicatorCsnList")
    public String getIndicatorCsnList() {
        return indicatorCsnList;
    }
    
    
    public void setIndicatorCsnList(String indicatorCsnList) {
        this.indicatorCsnList = indicatorCsnList;
    }
    
    
    /**
    **/
    @JsonProperty("displayValue")
    public String getDisplayValue() {
        return displayValue;
    }
    
    
    public void setDisplayValue(String displayValue) {
        this.displayValue = displayValue;
    }
    
    
    /**
    **/
    @JsonProperty("unit")
    public String getUnit() {
        return unit;
    }
    
    
    public void setUnit(String unit) {
        this.unit = unit;
    }
    
    
    /**
    **/
    @JsonProperty("moType")
    public String getMoType() {
        return moType;
    }
    
    
    public void setMoType(String moType) {
        this.moType = moType;
    }
    
    
    /**
    **/
    @JsonProperty("siteId")
    public String getSiteId() {
        return siteId;
    }
    
    
    public void setSiteId(String siteId) {
        this.siteId = siteId;
    }
    
    
    /**
    **/
    @JsonProperty("solutionId")
    public Integer getSolutionId() {
        return solutionId;
    }
    
    
    public void setSolutionId(Integer solutionId) {
        this.solutionId = solutionId;
    }
    
    

       
    
       
    
       
    
       

      
}

