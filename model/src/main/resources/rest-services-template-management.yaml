swagger: '2.0'
#   服务基本信息定义,包括服务名称，版本，服务描述（包括SLA），服务Owner                         #
info:
  #服务接口版本
  version: v1
  # 定义服务名称
  title: DVAnalysisEngineService
  description: |
    提供任务管理功能

    ** 服务SLA：**

    |SLA项|定义|
    |-----|-----|
    |请求成功率| >=99.9%|
    |可用性|Tair1|
    |数据一致性|最终一致，不一致时长<1分钟|
    |吞吐量|4000tps|
    |TP50请求时延|2ms|
    |TP99.9请求时延|5ms|

# 服务支持的访问协议http(s), 当前CloudSOP都是https的
schemes:
  - https
# Base PATH, 完整的访问路径为 basePath + path
basePath: /rest

#                            资源及资源上绑定的标准方法定义                                     #
paths:
  # 资源URL定义, 不带PATH参数
  /dvanalysisengineservice/v1/templatemanagement/create:
    # POST方法定义
    post:
      summary: 任务信息
      description: 新增模板
      tags:
        - TemplateManagementService    # 用于代码生成，声明方法所在类名称
      operationId: createTemplate  # 用于代码生成，声明方法名称
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      produces:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      parameters:
        - name: template # 参数名称
          in: body      # 参数类型，此外还支持header,cookie,path,query等类型
          description: 任务信息
          required: true   # 参数是否必须,必须参数要显示声明为true, 默认false
          schema:
            $ref: '#/definitions/CorrelationAnalysisTemplate'
      responses:
        '200':
          description: 是否成功

  /dvanalysisengineservice/v1/templatemanagement/modify:
    post:
      summary: 任务信息
      description: 修改模板
      tags:
        - TemplateManagementService    # 用于代码生成，声明方法所在类名称
      operationId: modifyTemplate  # 用于代码生成，声明方法名称
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      produces:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      parameters:
        - name: template # 参数名称
          in: body      # 参数类型，此外还支持header,cookie,path,query等类型
          description: 任务信息
          required: true   # 参数是否必须,必须参数要显示声明为true, 默认false
          schema:
            $ref: '#/definitions/CorrelationAnalysisTemplate'
      responses:
        '200':
          description: 返回任务信息列表

  /dvanalysisengineservice/v1/templatemanagement/delete:
    post:
      summary: 任务信息
      description: 删除模板
      tags:
        - TemplateManagementService    # 用于代码生成，声明方法所在类名称
      operationId: deleteTemplate  # 用于代码生成，声明方法名称
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      produces:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      parameters:
        - name: taskId
          description: taskId
          in: query
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: 是否成功
  /dvanalysisengineservice/v1/templatemanagement/query:
    # POST方法定义
    post:
      summary: 任务信息
      description: 查询模板
      tags:
        - TemplateManagementService    # 用于代码生成，声明方法所在类名称
      operationId: queryTemplate  # 用于代码生成，声明方法名称
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      produces:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      parameters:
        - name: param
          in: body  # 查询参数采用'query'描述
          description: |
          required: true  # 描述参数是否必须
          schema:
            $ref: '#/definitions/TemplateQueryParam'
      responses:
        '200':
          description: 返回任务信息列表
          schema:
            $ref: '#/definitions/TemplateList'

  /dvanalysisengineservice/v1/templatemanagement/savetemplateresult:
    post:
      summary: 更新保存模板结果
      description: 更新保存模板结果
      tags:
        - TemplateManagementService    # 用于代码生成，声明方法所在类名称
      operationId: saveTemplateResult  # 用于代码生成，声明方法名称
      consumes:
        - application/json
      produces:
        - application/json
      parameters:
        - name: param # 参数名称
          in: body      # 参数类型，此外还支持header,cookie,path,query等类型
          description: 返回值
          required: true   # 参数是否必须,必须参数要显示声明为true, 默认false
          schema:
            $ref: 'rest-services-task-manage.yaml#/definitions/TriggerTaskCheckResult'
      responses:
        '200':
          description: 查询成功

  /dvanalysisengineservice/v1/templatemanagement/instanceindicatorinfo:
    # POST方法定义
    post:
      summary: 任务信息
      description: 实例化指标信息
      tags:
        - TemplateManagementService    # 用于代码生成，声明方法所在类名称
      operationId: instanceIndicatorInfo  # 用于代码生成，声明方法名称
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      produces:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      parameters:
        - name: param
          in: body  # 查询参数采用'query'描述
          description: |
          required: true  # 描述参数是否必须
          schema:
            $ref: '#/definitions/InstancesParam'
      responses:
        '200':
          description: 返回任务信息列表
          schema:
            type: array
            items:
              $ref: '#/definitions/InstancesResult'

  /dvanalysisengineservice/v1/templatemanagement/querytaskassociatedtemplate:
    get:
      summary: 任务信息
      description: 查询这个模板关联的任务
      tags:
        - TemplateManagementService    # 用于代码生成，声明方法所在类名称
      operationId: queryTaskAssociatedTemplate  # 用于代码生成，声明方法名称
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      produces:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      parameters:
        - name: templateId
          in: query
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: 返回任务信息列表
          schema:
            type: array
            items:
              $ref: '#/definitions/AssociationAnalysisTask'

  /dvanalysisengineservice/v1/templatemanagement/queryexisttemplate:
    get:
      summary: 任务信息
      description: 查询所有模板和节点名称
      tags:
        - TemplateManagementService    # 用于代码生成，声明方法所在类名称
      operationId: queryAllTemplateName  # 用于代码生成，声明方法名称
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      produces:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      responses:
        '200':
          description: 返回任务信息列表
          schema:
            type: array
            items:
              $ref: '#/definitions/TemplateTree'

  /dvanalysisengineservice/v1/templatemanagement/querynodeInfo:
    get:
      summary: 任务信息
      description: 查询某个模板的节点信息
      tags:
        - TemplateManagementService    # 用于代码生成，声明方法所在类名称
      operationId: queryNodeDetail  # 用于代码生成，声明方法名称
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      produces:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      parameters:
        - name: templateId
          in: query
          required: true
          type: integer
          format: int32
        - name: nodeId
          in: query
          required: true
          type: string
      responses:
        '200':
          description: 返回任务信息列表
          schema:
            type: string

  /dvanalysisengineservice/v1/templatemanagement/exporttemplate:
    get:
      summary: 任务信息
      description: 查询某个模板的节点信息
      tags:
        - TemplateManagementService    # 用于代码生成，声明方法所在类名称
      operationId: exportTemplate  # 用于代码生成，声明方法名称
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      produces:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      parameters:
        - name: templateId
          in: query
          required: true
          type: integer
          format: int32
      responses:
        '200':
          description: 返回任务信息列表
          schema:
            type: string

  /dvanalysisengineservice/v1/templatemanagement/instantiatetask:
    post:
      summary: 任务信息
      description: 模板实例化为任务
      tags:
        - TemplateManagementService    # 用于代码生成，声明方法所在类名称
      operationId: instantiateTask  # 用于代码生成，声明方法名称
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      produces:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      parameters:
        - name: param
          in: body  # 查询参数采用'query'描述
          description: |
          required: true  # 描述参数是否必须
          schema:
            $ref: '#/definitions/InstantiateTaskParam'
      responses:
        '200':
          description: 返回任务信息列表

  /dvanalysisengineservice/v1/templatemanagement/querynameassociations:
    post:
      summary: 任务信息
      description: 查询模板名称是否有实例化任务
      tags:
        - TemplateManagementService    # 用于代码生成，声明方法所在类名称
      operationId: queryNameAssociations  # 用于代码生成，声明方法名称
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      produces:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      parameters:
        - name: taskNames # 参数名称
          in: body      # 参数类型，此外还支持header,cookie,path,query等类型
          description: 任务名称
          required: true   # 参数是否必须,必须参数要显示声明为true, 默认false
          schema:
            type: array
            items:
              type: string
      responses:
        '200':
          description: 返回任务信息列表
          schema:
            $ref: '#/definitions/AssociationResult'

definitions:
  CorrelationAnalysisTemplate:
    properties:
      taskId:
        type: integer
        format: int32
      taskDesc:
        type: string
      taskName:
        type: string
      taskType:
        type: integer
        format: int32
      updateTime:
        type: integer
        format: int64
      userId:
        type: string
      importTaskStatus:
        type: boolean
      subSite:
        type: string
      intelligentRecommendation:
        type: string
      solutionTypeForRecommendation:
        type: string
      triggerType:
        type: integer
        format: int32
      relationTree:
        type: string
      delay:
        type: string
      useTemplate:
        type: integer
        format: int32
      indicatorIds:
        description: 关联指标id
        type: string
      sendEmail:
        type: boolean
        default: false
      userGroupInfo:
        type: string
      manualTriggerEmail:
        type: boolean
        default: false
      correlateType:
        type: integer
        format: int32
        default: 1
      templateNodeCount:
        type: string
      status:
        type: string
      triggerIndicatorList:
        type: string
      displayFilter:
        type: boolean
      filterRelevance:
        type: string
      validDesc:
        type: string
      validTemplate:
        type: boolean

  TemplateQueryParam:
    properties:
      taskId:
        type: integer
        format: int32
      taskName:
        type: string
      status:
        type: array
        items:
          type: string
      pageSize:
        type: integer
        format: int32
      pageNumber:
        type: integer
        format: int32
      returnTreeResult:
        type: boolean
        default: false

  TemplateList:
    description: 模板列表
    properties:
      templates:
        description: 数据
        type: array
        items:
          $ref: '#/definitions/CorrelationAnalysisTemplate'
      total:
        description: 总数
        type: integer
        format: int32

  InstancesParam:
    properties:
      solutionName:
        type: string
      moName:
        type: string
      dn:
        type: string
      indicatorSelectType:
        type: integer
        format: int32
      moType:
        type: string
      measUnitKey:
        type: string
      deploymentMoType:
        type: string
      originalValue:
        type: string
      hasMeasObj:
        type: string

  InstancesResult:
    properties:
      solutionName:
        type: string
      moName:
        type: string
      dn:
        type: string
      measObject:
        type: object

  AssociationAnalysisTask:
    properties:
      taskId:
        type: integer
        format: int32
      taskName:
        type: string

  TemplateTree:
    properties:
      templateId:
        type: integer
        format: int32
      templateName:
        type: string
      templateNode:
        type: array
        items:
          $ref: '#/definitions/TemplateNode'

  TemplateNode:
    properties:
      nodeId:
        type: string
      nodeName:
        type: string
      type:
        type: string

  InstantiateTaskParam:
    properties:
      templateId:
        type: integer
        format: int32
      overwrite:
        type: boolean
      taskName:
        type: string

  AssociationResult:
    properties:
      instanceList:
        type: array
        items:
          type: string
      noInstanceList:
        type: array
        items:
          type: string

  IndicatorGroup:
    properties:
      taskId:
        type: integer
        format: int32
      indicatorGroupId:
        type: string
      dn:
        type: string
      dnName:
        type: string
      moName:
        type: string
      moType:
        type: string
      measUnitKey:
        type: string
      measUnitName:
        type: string
      measTypeKey:
        type: string
      indexName:
        type: string
      originalValue:
        type: string
      displayValue:
        type: string
      hasMeasObj:
        type: string
      deployMoType:
        type: string
      unit:
        type: string
      indicatorId:
        type: string
      dataSourceId:
        type: integer
        format: int32
      pql:
        type: string
      dataSourceName:
        type: string
      taskName:
        type: string