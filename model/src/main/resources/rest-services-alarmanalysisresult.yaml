# 告警分析详情页接口定义文件
---
swagger: '2.0'
# 服务基本信息定义,包括服务名称，版本，服务描述（包括SLA），服务Owner
info:
  # 服务接口版本
  version: v1
  # 定义服务名称
  title: DVAnalysisEngineWebsite
  # 描述服务提供的功能、限制、注意事项、服务的SLA等
  description: |
    告警分析结果查询接口定义，用于查询告警分析结果中的事件信息、告警信息、分数信息等

    ** 服务SLA：**

    |SLA项|定义|
    |-----|-----|
    |请求成功率| >=99.9%|
    |可用性|Tair1|
    |数据一致性|最终一致，不一致时长<1分钟|
    |吞吐量|4000tps|
    |TP50请求时延|2ms|
    |TP99.9请求时延|5ms|
  # 声明服务维护人员信息
  contact:
    name: username
    email: <EMAIL>
# 服务支持的访问协议http(s), 当前CloudSOP都是https的
schemes:
  - https
# Base PATH, 完整的访问路径为 basePath + path
basePath: /rest
paths:
  # 资源URL定义，用于查询事件列表
  /dvanalysisenginewebsite/v1/alarmanalysisresult/eventlist:
    # POST方法定义
    post:
      summary: '查询事件列表'
      description: '查询事件列表'
      tags:
        - AlarmAnalysisResult  # 用于代码生成，声明方法所在类名称
      operationId: getEventList  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: queryEventListData
          description: 查询事件列表入参
          in: body
          required: true
          schema:
            $ref: '#/definitions/QueryEventListData'
      responses:
        200:
          description: 事件列表返回值
          schema: # 声明返回值Schema
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  # 资源URL定义，用于查询告警列表
  /dvanalysisenginewebsite/v1/alarmanalysisresult/alarmlist:
    # POST方法定义
    post:
      summary: '查询告警列表'
      description: '查询告警列表'
      tags:
        - AlarmAnalysisResult  # 用于代码生成，声明方法所在类名称
      operationId: getAlarmList  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: queryAlarmListData
          description: 查询告警列表入参
          in: body
          required: true
          schema:
            $ref: '#/definitions/QueryAlarmListData'
      responses:
        200:
          description: 告警列表返回值
          schema: # 声明返回值Schema
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  # 资源URL定义，用于查询告警详情
  /dvanalysisenginewebsite/v1/alarmanalysisresult/alarmdetail:
    # POST方法定义
    post:
      summary: '查询告警详情'
      description: '查询告警详情'
      tags:
        - AlarmAnalysisResult  # 用于代码生成，声明方法所在类名称
      operationId: getAlarmDetail  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: queryAlarmDetailData
          description: 查询告警详情入参
          in: body
          required: true
          schema:
            $ref: '#/definitions/QueryAlarmDetailData'
      responses:
        200:
          description: 告警详情返回值
          schema: # 声明返回值Schema
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  # 资源URL定义，用于查询告警度量项列表
  /dvanalysisenginewebsite/v1/alarmanalysisresult/alarmmeasurementlist:
    # POST方法定义
    post:
      summary: '查询告警度量项列表'
      description: '查询告警度量项列表'
      tags:
        - AlarmAnalysisResult  # 用于代码生成，声明方法所在类名称
      operationId: getAlarmMeasurementList  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: queryAlarmMeasurementListData
          description: 查询告警度量项列表入参
          in: body
          required: true
          schema:
            $ref: '#/definitions/QueryAlarmMeasurementListData'
      responses:
        200:
          description: 告警度量项列表返回值
          schema: # 声明返回值Schema
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  # 资源URL定义，用于查询告警下的告警数趋势图
  /dvanalysisenginewebsite/v1/alarmanalysisresult/quantitytrendbyalarmid:
    # POST方法定义
    post:
      summary: '查询告警下的告警数趋势图'
      description: '查询告警下的告警数趋势图'
      tags:
        - AlarmAnalysisResult  # 用于代码生成，声明方法所在类名称
      operationId: getQuantityTrendByAlarmId  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: queryQuantityTrendByAlarmId
          description: 查询告警下的告警数趋势图入参
          in: body
          required: true
          schema:
            $ref: '#/definitions/QueryQuantityTrendByAlarmIdData'
      responses:
        200:
          description: 告警下的告警数趋势图返回值
          schema: # 声明返回值Schema
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  # 资源URL定义，用于查询事件下的告警id集合
  /dvanalysisenginewebsite/v1/alarmanalysisresult/alarmidlist:
    # POST方法定义
    post:
      summary: '查询事件下的告警id集合'
      description: '查询事件下的告警id集合'
      tags:
        - AlarmAnalysisResult  # 用于代码生成，声明方法所在类名称
      operationId: getAlarmIdList  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: queryAlarmIdListData
          description: 查询事件下的告警id集合入参
          in: body
          required: true
          schema:
            $ref: '#/definitions/QueryAlarmIdListData'
      responses:
        200:
          description: 事件下的告警id集合返回值
          schema: # 声明返回值Schema
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  # 资源URL定义，用于批量标记告警
  /dvanalysisenginewebsite/v1/alarmanalysisresult/markingalarms:
    # POST方法定义
    post:
      summary: '批量标记告警'
      description: '批量标记告警'
      tags:
        - AlarmAnalysisResult  # 用于代码生成，声明方法所在类名称
      operationId: markingAlarms  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: markingAlarmsData
          description: 批量标记告警入参
          in: body
          required: true
          schema:
            $ref: '#/definitions/MarkingAlarmsData'
      responses:
        200:
          description: 批量标记告警返回值
          schema: # 声明返回值Schema
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  # 资源URL定义，根据事件id查询告警拓扑信息
  /dvanalysisenginewebsite/v1/alarmanalysisresult/alarmtopologybyeventid:
    # POST方法定义
    post:
      summary: '根据事件id查询告警拓扑信息'
      description: '根据事件id查询告警拓扑信息'
      tags:
        - AlarmAnalysisResult  # 用于代码生成，声明方法所在类名称
      operationId: getAlarmTopologyByEventId  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: queryAlarmTopologyByEventIdData
          description: 根据事件id查询告警拓扑信息入参
          in: body
          required: true
          schema:
            $ref: '#/definitions/QueryAlarmTopologyByEventIdData'
      responses:
        200:
          description: 根据事件id查询告警拓扑信息返回值
          schema: # 声明返回值Schema
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

# 自定义的复杂数据结构,可选
definitions:
  QueryAlarmListPageIndexData:
    type: object
    properties:
      incidentId:
        type: string
      taskId:
        type: string
      alarmSerialNo:
        type: string
      pageSize:
        type: integer
        format: int32
        minimum: 0
        maximum: 100
  QueryEventListData:
    type: object
    properties:
      taskIds:
        type: array
        minItems: 0
        maxItems: 100
        items:
          type: string
          minLength: 0
          maxLength: 100000
      incidentId:
        type: string
        maxLength: 64
      alarmSerialNo:
        type: string
        maxLength: 1024
      alarmTypes:
        type: array
        minItems: 0
        maxItems: 30
        items:
          type: string
          minLength: 0
          maxLength: 10
      alarmSourceTypes:
        type: array
        minItems: 0
        maxItems: 10000
        items:
          type: string
          minLength: 0
          maxLength: 255
      timeRangeRadioId:
        type: string
        maxLength: 1
      customTimes:
        type: array
        minItems: 0
        maxItems: 2
        items:
          type: integer
          format: int64
          minimum: 0
          maximum: 922337203685475807
      sortType:
        type: string
        minLength: 0
        maxLength: 1024
      sortField:
        type: string
        minLength: 0
        maxLength: 1024
      pageIndex:
        type: integer
        format: int32
        minimum: 0
        maximum: 10000
      pageSize:
        type: integer
        format: int32
        minimum: 0
        maximum: 100
      alarmType:
        type: string
        minLength: 0
        maxLength: 20
      levels:
        type: array
        minItems: 0
        maxItems: 4
        items:
          type: integer
          format: int32
          minimum: 1
          maximum: 4
  EventListResult:
    type: object
    properties:
      totalCount:
        type: integer
        format: int32
      eventList:
        type: array
        items:
          $ref: '#/definitions/EventInfo'
  EventInfo:
    properties:
      id:
        type: string
      taskId:
        type: string
      taskName:
        type: string
      level:
        type: integer
      alarmNum:
        type: integer
      description:
        type: string
      startTime:
        type: string
      endTime:
        type: string
      aiScore:
        type: string
      rootId:
        type: string
  QueryAlarmListData:
    type: object
    properties:
      incidentId:
        type: string
        minLength: 0
        maxLength: 10
      taskId:
        type: string
        minLength: 0
        maxLength: 10
      pageIndex:
        type: integer
        format: int32
        minimum: 0
        maximum: 10000
      pageSize:
        type: integer
        format: int32
        minimum: 0
        maximum: 1000
      timeRangeRadioId:
        type: string
        maxLength: 1
      alarmType:
        type: string
        minLength: 0
        maxLength: 20
  AlarmListResult:
    type: object
    properties:
      totalCount:
        type: integer
        format: int32
      alarmList:
        type: array
        items:
          $ref: '#/definitions/AlarmInfo'
  AlarmInfo:
    properties:
      id:
        type: string
      alarmId:
        type: string
      level:
        type: integer
      alarmName:
        type: string
      startTime:
        type: string
      endTime:
        type: string
      keyAlarm:
        type: string
      alarmSource:
        type: string
  QueryAlarmDetailData:
    type: object
    properties:
      id:
        type: string
        minLength: 0
        maxLength: 10
  AlarmDetailResult:
    type: object
    properties:
      resPoolId:
        type: string
      resPoolName:
        type: string
      alarmName:
        type: string
      region:
        type: string
      ackUser:
        type: string
      alarmId:
        type: string
      virtualNe:
        type: string
      originSystem:
        type: string
      logicalRegionName:
        type: string
      probableCause:
        type: string
      moi:
        type: string
  QueryAlarmMeasurementListData:
    type: object
    properties:
      id:
        type: string
        minLength: 0
        maxLength: 10
      taskId:
        type: string
        minLength: 0
        maxLength: 10
  AlarmMeasurementListResult:
    type: object
    properties:
      aiScore:
        type: number
        format: float
      scores:
        type: array
        items:
          $ref: '#/definitions/Scores'
  Scores:
    type: object
    properties:
      text:
        type: string
      value:
        type: number
        format: float
      description:
        type: string
  QueryQuantityTrendByAlarmIdData:
    type: object
    properties:
      taskId:
        type: integer
        format: int32
        minimum: 0
        maximum: 2147483647
      id:
        type: string
        maxLength: 10
      alarmTimes:
        type: array
        minItems: 0
        maxItems: 10
        items:
          type: integer
          format: int64
          minimum: 0
          maximum: 922337203685475807
  QuantityTrendByAlarmIdResult:
    type: object
    properties:
      startTime:
        type: string
      endTime:
        type: string
      alarmName:
        type: string
      alarmSummaryList:
        type: array
        items:
          $ref: '#/definitions/QuantityTrendByAlarmId'
  QuantityTrendByAlarmId:
    type: object
    properties:
      timestampStr:
        type: string
      alarmNum:
        type: integer
  QueryAlarmIdListData:
    type: object
    properties:
      id:
        type: string
        minLength: 0
        maxLength: 10
      taskId:
        type: string
        minLength: 0
        maxLength: 10
  AlarmIdListResult:
    type: object
    properties:
      totalCount:
        type: integer
        format: int32
      alarmIds:
        type: array
        items:
          type: string
  MarkingAlarmsData:
    type: object
    properties:
      ids:
        type: array
        minItems: 0
        maxItems: 10000
        items:
          type: string
          minLength: 0
          maxLength: 10
      taskId:
        type: string
        minLength: 0
        maxLength: 10
      keyAlarm:
        type: integer
        minimum: 0
        maximum: 1
  QueryAlarmTopologyByEventIdData:
    type: object
    properties:
      incidentId:
        type: string
        minLength: 0
        maxLength: 10
      taskId:
        type: string
        minLength: 0
        maxLength: 10
  AlarmTopologyByEventIdResult:
    type: object
    properties:
      node:
        type: array
        items:
          $ref: '#/definitions/Node'
      link:
        type: array
        items:
          $ref: '#/definitions/LinkInfo'

  Node:
    type: object
    properties:
      id:
        type: string
      name:
        type: string
      alarmSerialCount:
        type: integer
      level:
        type: integer
      location:
        $ref: '#/definitions/Location'
  Location:
    type: object
    properties:
      x:
        type: integer
      y:
        type: integer

  LinkInfo:
    type: object
    properties:
      name:
        type: string
      fromId:
        type: string
      toId:
        type: string