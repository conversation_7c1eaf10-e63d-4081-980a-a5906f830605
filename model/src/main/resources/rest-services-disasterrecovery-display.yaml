# 服务生成默认接口定义文件
---
swagger: '2.0'
# 服务基本信息定义,包括服务名称，版本，服务描述（包括SLA），服务Owner
info:
  # 服务接口版本
  version: v1
  # 定义服务名称
  title: DVAnalysisEngineWebsite
  # 描述服务提供的功能、限制、注意事项、服务的SLA等
  description: |
    默认生成yaml文件提供了默认接口，用于检查应用部署是否成功

    ** 服务SLA：**

    |SLA项|定义|
    |-----|-----|
    |请求成功率| >=99.9%|
    |可用性|Tair1|
    |数据一致性|最终一致，不一致时长<1分钟|
    |吞吐量|4000tps|
    |TP50请求时延|2ms|
    |TP99.9请求时延|5ms|
  # 声明服务维护人员信息
  contact:
    name: username
    email: <EMAIL>
# 服务支持的访问协议http(s), 当前CloudSOP都是https的
schemes:
  - https
# Base PATH, 完整的访问路径为 basePath + path
basePath: /rest
paths:
  /dvanalysisenginewebsite/v1/disasterrecovery/taskdisplay/gettaskboardinfo:
    get:
      summary: '获取页面所有容灾任务'
      description: '获取页面所有容灾任务'
      tags:
        - DVAnalysisEngineWebsiteDisRecoveryTaskShow  # 用于代码生成，声明方法所在类名称
      operationId: getTaskBoardInfo  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: limit
          description: limit
          in: query
          required: false
          type: string
          maxLength: 255
      responses:
        200:
          description: Get TaskTags
          schema:
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  /dvanalysisenginewebsite/v1/disasterrecovery/taskdisplay/getgroupboardinfo:
    get:
      summary: '获取页面所有容灾任务'
      description: '获取页面所有容灾任务'
      tags:
        - DVAnalysisEngineWebsiteDisRecoveryTaskShow  # 用于代码生成，声明方法所在类名称
      operationId: getGroupBoardInfo  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      parameters:
        - name: taskID
          description: taskID
          in: query
          required: true
          type: string
          maxLength: 255
      responses:
        200:
          description: 'Get GroupBoard'
          schema:
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  /dvanalysisenginewebsite/v1/disasterrecovery/taskdisplay/groupcolumns:
    get:
      summary: '获取容灾区域的列名'
      description: '获取容灾区域的列名'
      tags:
        - DVAnalysisEngineWebsiteDisRecoveryTaskShow  # 用于代码生成，声明方法所在类名称
      operationId: getGroupColumns
      produces:
        - application/json  # 声明返回Json格式数据
      parameters:
        - name: groupID
          description: groupID
          in: query
          required: true
          type: string
          maxLength: 255
      responses:
        200:
          description: 'Get Columns'
          schema:
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  /dvanalysisenginewebsite/v1/disasterrecovery/taskdisplay/querymemberdata:
    post:
      summary: '获取容灾区域成员信息'
      description: '获取容灾区域成员信息'
      tags:
        - DVAnalysisEngineWebsiteDisRecoveryTaskShow
      operationId: queryMemberData
      produces:
        - application/json
      consumes:
        - application/json
      parameters:
        - name: memberDetailParam
          description: '查询容灾成员信息参数'
          in: body
          required: true
          schema:
            $ref: '#/definitions/MemberDetailParam'
      responses:
        200:
          description: 'Post Query data'
          schema:
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  /dvanalysisenginewebsite/v1/disasterrecovery/taskdisplay/modifyauxiliaryitems:
    post:
      summary: '修改成员辅助项值'
      description: '修改成员辅助项值'
      tags:
        - DVAnalysisEngineWebsiteDisRecoveryTaskShow
      operationId: modifyAuxiliaryItems
      produces:
        - application/json
      consumes:
        - application/json
      parameters:
        - name: auxiliaryItemsParam
          description: '查询容灾成员信息参数'
          in: body
          required: true
          schema:
            $ref: '#/definitions/AuxiliaryItemsParam'
      responses:
        200:
          description: 'Post Modify Data'
          schema:
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  /dvanalysisenginewebsite/v1/disasterrecovery/taskdisplay/queryauxiliaryitems:
    post:
      summary: '修改成员辅助项值'
      description: '修改成员辅助项值'
      tags:
        - DVAnalysisEngineWebsiteDisRecoveryTaskShow # 用于代码生成，声明方法所在类名称
      operationId: queryAuxiliaryItems  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: queryAuxiliaryItemsParam
          description: queryAuxiliaryItemsParam
          in: body
          required: true
          schema:
            $ref: '#/definitions/AuxiliaryQueryItemsParam'
      responses:
        200:
          description: path
          schema:
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  /dvanalysisenginewebsite/v1/disasterrecovery/taskdisplay/queryalarmdata:
    post:
      summary: '获取成员告警信息'
      description: '获取成员告警信息'
      tags:
        - DVAnalysisEngineWebsiteDisRecoveryTaskShow  # 用于代码生成，声明方法所在类名称
      operationId: queryAlarmData
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: alarmQueryParam
          description: 'alarmQueryParam'
          in: body
          required: true
          schema:
            $ref: '#/definitions/AlarmQueryParam'
      responses:
        200:
          description: 'Query Alarm Data'
          schema:
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  /dvanalysisenginewebsite/v1/disasterrecovery/taskdisplay/querymemberflowdata:
    post:
      summary: '获取成员告警信息'
      description: '获取成员告警信息'
      tags:
        - DVAnalysisEngineWebsiteDisRecoveryTaskShow  # 用于代码生成，声明方法所在类名称
      operationId: queryMemberFlowData
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: flowQueryParam
          description: flowQueryParam
          in: body
          required: true
          schema:
            $ref: '#/definitions/FlowQueryParam'
      responses:
        200:
          description: 'Query Monitor Data'
          schema:
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  /dvanalysisenginewebsite/v1/disasterrecovery/taskdisplay/querymemberkpidata:
    post:
      summary: '获取成员告警信息'
      description: '获取成员告警信息'
      tags:
        - DVAnalysisEngineWebsiteDisRecoveryTaskShow  # 用于代码生成，声明方法所在类名称
      operationId: queryMemberKPIData
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: kpiQueryParam
          description: kpiQueryParam
          in: body
          required: true
          schema:
            $ref: '#/definitions/KPIQueryParam'
      responses:
        200:
          description: 'Query Monitor Data'
          schema:
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

  /dvanalysisenginewebsite/v1/disasterrecovery/taskdisplay/querymemberassociationkpidata:
    post:
      summary: '获取成员关联KPI信息'
      description: '获取成员关联KPI信息'
      tags:
        - DVAnalysisEngineWebsiteDisRecoveryTaskShow  # 用于代码生成，声明方法所在类名称
      operationId: queryMemberAssociationKPIData
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      parameters:
        - name: associationKpiQueryParam
          description: associationKpiQueryParam
          in: body
          required: true
          schema:
            $ref: '#/definitions/AssociationKpiQueryParam'
      responses:
        200:
          description: 'Query Monitor Data'
          schema:
            $ref: 'rest-services.yaml#/definitions/ResponseEntity'

# 自定义的复杂数据结构,可选
definitions:
  DisRecoveryTaskInfoParam:
    type: object
    properties:
      createUser:
        type: string
  # 请求体
  MemberDetailParam:
    type: object
    properties:
      paging:
        $ref: 'rest-services-task-manage.yaml#/definitions/Paging'
      taskID:
        type: string
        minLength: 0
        maxLength: 1000
      groupID:
        type: string
        minLength: 0
        maxLength: 1000

  AuxiliaryItemsParam:
    type: object
    properties:
      groupMemberID:
        type: string
        description: '容灾成员ID'
        minLength: 0
        maxLength: 64
      auxiliaryItems:
        type: array
        minItems: 0
        maxItems: 20
        items:
          $ref: '#/definitions/AuxiliaryItem'

  AuxiliaryQueryItemsParam:
    type: object
    properties:
      taskID:
        type: string
        minLength: 0
        maxLength: 64
      groupID:
        type: string
        minLength: 0
        maxLength: 64
      groupMemberID:
        type: string
        minLength: 0
        maxLength: 64
      userName:
        type: string
        minLength: 0
        maxLength: 64

  AlarmQueryParam:
    type: object
    description: '告警查询参数'
    properties:
      taskID:
        type: string
        minLength: 0
        maxLength: 1000
      groupID:
        type: string
        minLength: 0
        maxLength: 1000
      groupMemberID:
        type: string
        minLength: 0
        maxLength: 1000
      groupMemberName:
        type: string
        minLength: 0
        maxLength: 1000
      userName:
        type: string
        minLength: 0
        maxLength: 1000
      paging:
        $ref: 'rest-services-task-manage.yaml#/definitions/Paging'

  FlowQueryParam:
    type: object
    description: '流程查询参数'
    properties:
      taskID:
        type: string
        minLength: 0
        maxLength: 1000
      groupID:
        type: string
        minLength: 0
        maxLength: 1000
      groupMemberID:
        type: string
        minLength: 0
        maxLength: 1000
      groupMemberName:
        type: string
        minLength: 0
        maxLength: 1000
      userName:
        type: string
        minLength: 0
        maxLength: 1000
      paging:
        $ref: 'rest-services-task-manage.yaml#/definitions/Paging'

  KPIQueryParam:
    type: object
    description: 'kpi指标查询参数'
    properties:
      taskID:
        type: string
        minLength: 0
        maxLength: 1000
      groupID:
        type: string
        minLength: 0
        maxLength: 1000
      groupMemberID:
        type: string
        minLength: 0
        maxLength: 1000
      groupMemberName:
        type: string
        minLength: 0
        maxLength: 1000
      userName:
        type: string
        minLength: 0
        maxLength: 1000
      paging:
        $ref: 'rest-services-task-manage.yaml#/definitions/Paging'

  AssociationKpiQueryParam:
    type: object
    description: '关联指标查询参数'
    properties:
      timeRangeRadioId:
        type: string
        minLength: 0
        maxLength: 2
      customTimes:
        type: array
        minItems: 0
        maxItems: 2
        items:
          type: integer
          format: int64
          minimum: 0
          maximum: 922337203685475807
      taskID:
        type: string
        minLength: 0
        maxLength: 64
      groupID:
        type: string
        minLength: 0
        maxLength: 64
      groupMemberID:
        type: string
        minLength: 0
        maxLength: 64
      groupMemberName:
        type: string
        minLength: 0
        maxLength: 600
      userName:
        type: string
        minLength: 0
        maxLength: 1000
      paging:
        $ref: 'rest-services-task-manage.yaml#/definitions/Paging'

  # 结构体
  TaskTagsDataList:
    type: object
    properties:
      taskTagsDataList:
        type: array
        items:
          $ref: '#/definitions/TaskTagsData'

  TaskTagsData:
    type: object
    properties:
      taskID:
        type: string
        description: '容灾任务ID'
      taskName:
        type: string
        description: '容灾任务名称'
      isOpenAutoSwitch:
        type: boolean
        description: '是否自动切换任务'

  GroupTagsData:
    type: object
    properties:
      groupID:
        type: string
        description: '容灾区域ID'
      groupName:
        type: string
        description: '容灾区域名称'

  GroupTotalDataList:
    type: object
    properties:
      groupStatusCount:
        type: array
        items:
          $ref: '#/definitions/GroupStatusCount'
      isLastSwitchResultOver:
        type: boolean
      switchTaskID:
        type: string

  AlarmDetailList:
    type: object
    description: '告警查询返回结构体'
    properties:
      alarmIdList:
        type: object
      dnList:
        type: object

  GroupStatusCount:
    type: object
    description: '容灾区域各成员状态统计'
    properties:
      groupID:
        type: string
        description: '容灾区域ID'
      groupName:
        type: string
        description: '容灾区域名称'
      healthCount:
        type: integer
        format: int32
      unhealthyCount:
        type: integer
        format: int32
      unknownCount:
        type: integer
        format: int32
      checkingCount:
        type: integer
        format: int32
      switchedCount:
        type: integer
        format: int32
      switchFailedCount:
        type: integer
        format: int32
      totalMember:
        type: integer
        format: int32
      switchType:
        type: string
        description: '容灾区域切换类型'

  GroupShowColumns:
    type: object
    description: '容灾区域用于展示的列名'
    properties:
      KPIStatusShow:
        type: boolean
        description: 'KPI是否展示'
      alarmStatusShow:
        type: boolean
        description: '告警是否展示'
      flowStatusShow:
        type: boolean
        description: '流程监控是否展示'
      associationKpiStatusShow:
        type: boolean
        description: '关联指标是否展示'
      auxiliaryColumns:
        type: object
        description: '容灾辅助监控项'

  MemberStatusData:
    type: object
    properties:
      memberStatus:
        type: string
        description: '容灾区域成员状态'
      memberStatusCount:
        type: integer
        format: int32
        description: '容灾区域成员状态统计'

  MemberDetails:
    type: object
    properties:
      groupID:
        type: string
        description: '容灾区域ID'
      taskID:
        type: string
        description: '所属容灾任务ID'
      memberDetailsData:
        type: object
        description: '成员详细信息'
      totalMember:
        type: integer
        format: int32
        description: '容灾区域总成员数'
      isMemberSwitching:
        type: boolean
        description: '该区域是否有容灾成员在切换/回切'
      
  MemberDetailsData:
    type: object
    properties:
      groupMemberID:
        type: string
        description: '容灾区域成员ID'
      DNNumber:
        type: string
        description: '网元DN'
      DNName:
        type: string
        description: '网元DN名称'
      status:
        type: string
        description: '容灾区域成员汇总状态'
      KPIStatus:
        type: string
        description: '容灾区域成员状态'
      alarmStatus:
        type: string
        description: '严重告警个数'
      flowStatus:
        type: string
        description: '监控流程状态'
      associationKpiStatus:
        type: string
        description: '异常关联指标个数'
      auxiliaryItem:
        type: object
        description: '辅助项值'

  AuxiliaryItem:
    type: object
    description: '辅助监控项'
    properties:
      auxiliaryKey:
        type: string
        description: '成员key,页面填写,作为标示'
        minLength: 0
        maxLength: 64
      displayNameZH:
        type: string
        description: '辅助项中文名'
        minLength: 0
        maxLength: 64
      displayNameEN:
        type: string
        description: '辅助项英文名'
        minLength: 0
        maxLength: 64
      auxiliaryValue:
        type: string
        description: '辅助项值'
        minLength: 0
        maxLength: 64

  KPIDetailsData:
    type: object
    properties:
      groupMemberID:
        type: string
        description: '容灾区域成员ID'
      DNNumber:
        type: string
        description: '网元DN'
      DNName:
        type: string
        description: '网元DN名称'
      status:
        type: string
        description: '成员指定指标的状态'
      KPITime:
        type: string
        description: 'KPI上报时间'
      KPIName:
        type: string
        description: '主指标名称'
      lastPeriodKPIValue:
        type: string
        description: '最近一个周期主指标的KPI值'
      lastPeriodDerivedKPIValue:
        type: string
        description: '最近一个周期主指标的衍生值'
      derivedKPITime:
        type: string
        description: '衍生值类型汇聚'
      KPIThresholdValue:
        type: string
        description: 'KPI校验阈值'
      dynamicColumns:
        type: array
        items:
          $ref: '#/definitions/DynamicColumns'
        description: '创建动态列的值'

  DynamicColumns:
    type: object
    properties:
      columnName:
        type: string
        description: '列名'
      columnDisplayName:
        type: string
        description: '页面展示名'
      value:
        type: string
        description: '对应列名的值'

  AlarmDetailsData:
    type: object
    properties:
      groupMemberID:
        type: string
        description: '容灾区域成员ID'
      groupMemberName:
        type: string
        description: '成员名称'
      alarmID:
        type: string
        description: '告警ID'
      alarmDN:
        type: string
        description: '发生告警的网元DN，对应告警的资源标识字段'
      alarmAdditionalInfo:
        type: string
        description: '告警定位信息'
      alarmName:
        type: string
        description: '告警名称'

  FlowDetailsData:
    type: object
    properties:
      groupMemberID:
        type: string
        description: '容灾区域成员ID'
      monitorFlowResult:
        type: string
        description: '页面详情展示'
