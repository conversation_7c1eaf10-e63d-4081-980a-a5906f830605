swagger: '2.0'
#   服务基本信息定义,包括服务名称，版本，服务描述（包括SLA），服务Owner                         #
info:
  #服务接口版本
  version: v1
  # 定义服务名称
  title: DVAnalysisEngineService
  description: |
    提供任务管理功能

    ** 服务SLA：**

    |SLA项|定义|
    |-----|-----|
    |请求成功率| >=99.9%|
    |可用性|Tair1|
    |数据一致性|最终一致，不一致时长<1分钟|
    |吞吐量|4000tps|
    |TP50请求时延|2ms|
    |TP99.9请求时延|5ms|

# 服务支持的访问协议http(s), 当前CloudSOP都是https的
schemes:
  - https
# Base PATH, 完整的访问路径为 basePath + path
basePath: /rest

#  资源及资源上绑定的标准方法定义  #
paths:
  # 资源URL定义, 不带PATH参数
  /dvanalysisengineservice/v1/msecology/querymsmotree:
    # POST方法定义
    get:
      summary: 查询startRock实例树
      description: 查询startRock实例树
      tags:
        - MsEcologyService    # 用于代码生成，声明方法所在类名称
      operationId: queryMsMoTree  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json
      responses:
        '200':
          description: 是否成功
          schema: # 声明返回值Schema
            $ref: '#/definitions/MsMoTree'

  /dvanalysisengineservice/v1/msecology/querymspmtablecolumns:
    # POST方法定义
    post:
      summary: 查询starRock数据表的列名
      description: 查询starRock数据表的列名
      tags:
        - MsEcologyService    # 用于代码生成，声明方法所在类名称
      operationId: queryMsPmTableColumns  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      parameters:
        - name: queryParam
          description: 查询入参
          in: body
          required: true
          schema:
            $ref: '#/definitions/QueryMsTableColumnsParam'
      responses:
        200:
          description: path
          schema:
            $ref: 'rest-services-datasourcemanager.yaml#/definitions/ResponseResult'

  /dvanalysisengineservice/v1/msecology/querymsindicatorsbyindicatorgroup:
    # POST方法定义
    post:
      summary: 根据指标组查询指标列表
      description: 根据指标组查询指标列表
      tags:
        - MsEcologyService    # 用于代码生成，声明方法所在类名称
      operationId: queryMsIndicatorsByIndicatorGroup  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      parameters:
        - name: queryParam
          description: 查询入参
          in: body
          required: true
          schema:
            $ref: 'rest-services-task-manage.yaml#/definitions/MsIndicatorGroup'
      responses:
        '200':
          description: 指标列表
          schema:
            $ref: 'rest-services-datasourcemanager.yaml#/definitions/ResponseResult'

  /dvanalysisengineservice/v1/msecology/querymscustomsqltablecolumns:
    # POST方法定义
    post:
      summary: 查询自定义sql数据表的列名
      description: 查询自定义sql数据表的列名
      tags:
        - MsEcologyService    # 用于代码生成，声明方法所在类名称
      operationId: queryMsCustomSqlTableColumns  # 用于代码生成，声明方法名称
      produces:
        - application/json  # 声明返回Json格式数据
      consumes:
        - application/json  # 定义输入的body参数格式，还支持application/xml,text/plat等
      parameters:
        - name: queryParam
          description: 查询入参
          in: body
          required: true
          schema:
            $ref: '#/definitions/QueryMsTableColumnsParam'
      responses:
        200:
          description: path
          schema:
            $ref: 'rest-services-datasourcemanager.yaml#/definitions/ResponseResult'

definitions:
  MsMoTree:
    properties:
      dv:
        type: array
        items:
          $ref: '#/definitions/MsMo'
      prometheus:
        type: array
        items:
          $ref: '#/definitions/MsMo'

  MsMo:
    properties:
      moName:
        type: string
      dn:
        type: string

  QueryMsTableColumnsParam:
    properties:
      moType:
        type: string
      measUnitKey:
        type: string
      customSql:
        type: string
      isComparison:
        type: boolean
