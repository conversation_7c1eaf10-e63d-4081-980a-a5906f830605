# 服务生成默认接口定义文件
---
swagger: '2.0'
# 服务基本信息定义,包括服务名称，版本，服务描述（包括SLA），服务Owner
info:
  # 服务接口版本
  version: v1
  # 定义服务名称
  title: DVCallchainWebsite
  # 描述服务提供的功能、限制、注意事项、服务的SLA等
  description: |
    默认生成yaml文件提供了默认接口，用于检查应用部署是否成功
    
    ** 服务SLA：**
    
    |SLA项|定义|
    |-----|-----|
    |请求成功率| >=99.9%|
    |可用性|Tair1|
    |数据一致性|最终一致，不一致时长<1分钟|
    |吞吐量|4000tps|
    |TP50请求时延|2ms|
    |TP99.9请求时延|5ms|
  # 声明服务维护人员信息  
  contact:
    name: xxxx
    email: <EMAIL>
# 服务支持的访问协议http(s), 当前CloudSOP都是https的
schemes:
  - https
# Base PATH, 完整的访问路径为 basePath + path
basePath: /rest
paths:
  /dvcallchainwebsite/v1/callchainqueryservice/queryCallchainDataViewByFilter:
    post:
      summary: '根据解决方案查询调用链总览视图'
      description: '根据解决方案查询调用链总览视图，各解决方案下调用链数以及成功率'
      tags:
        - CallChainQueryWebsite
      operationId: queryCallchainDataViewByFilter
      produces:
        - application/json
      consumes:
        - application/json
      x-imports: "com.huawei.cmp.dvcallchainservice.model.callchainquery.CallchainDataModel;com.huawei.cmp.dvcallchainservice.model.CallchainDataViewVO"
      parameters:
        - name: callchainDataModel
          in: body
          required: true
          schema:
            $ref: "#/definitions/CallchainDataModel"
      responses:
        200:
          description: '接口正常'
          schema:
            type: array
            items:
              $ref: "#/definitions/CallchainDataViewVO"
        400:
          description: '参数错误'
        403:
          description: 'URL鉴权'
        404:
          description: 'rest接口没找到'
        406:
          description: '流控限流'
        500:
          description: '默认ServiceException异常码，应用内部错误'
        502:
          description: '网关错误'
        503:
          description: '服务不可用'
        504:
          description: '表示时间超时，总线超时时间300s'
        888:
          description: '服务摘机，不提供服务'
  /dvcallchainwebsite/v1/callchainqueryservice/queryOverviewTraceCountByFilter:
    post:
      summary: '根据过滤条件查询概述记录数量'
      description: '根据过滤条件查询概述记录数量'
      tags:
        - CallChainQueryWebsite
      operationId: queryOverviewTraceCountByFilter
      produces:
        - application/json
      consumes:
        - application/json
      x-imports: "com.huawei.cmp.dvcallchainservice.model.ChainQueryParamModel"
      parameters:
        - name: queryParamVO
          in: body
          required: true
          schema:
            $ref: "#/definitions/ChainQueryParamModel"
      responses:
        200:
          description: '接口正常'
          schema:
            type: integer
            format: int64
        400:
          description: '参数错误'
        403:
          description: 'URL鉴权'
        404:
          description: 'rest接口没找到'
        406:
          description: '流控限流'
        500:
          description: '默认ServiceException异常码，应用内部错误'
        502:
          description: '网关错误'
        503:
          description: '服务不可用'
        504:
          description: '表示时间超时，总线超时时间300s'
        888:
          description: '服务摘机，不提供服务'
  /dvcallchainwebsite/v1/callchainqueryservice/queryOverviewTraceByFilter:
    post:
      summary: '根据过滤条件查询概述记录'
      description: '根据过滤条件查询概述记录'
      tags:
        - CallChainQueryWebsite
      operationId: queryOverviewTraceByFilter
      produces:
        - application/json
      consumes:
        - application/json
      x-imports: "com.huawei.cmp.dvcallchainservice.model.ChainQueryParamModel;com.huawei.cmp.dvcallchainservice.model.CallChainLogVO"
      parameters:
        - name: queryParamVO
          in: body
          required: true
          schema:
            $ref: "#/definitions/ChainQueryParamModel"
      responses:
        200:
          description: '接口正常'
          schema:
            type: array
            items:
              $ref: "#/definitions/CallChainLogVO"
        400:
          description: '参数错误'
        403:
          description: 'URL鉴权'
        404:
          description: 'rest接口没找到'
        406:
          description: '流控限流'
        500:
          description: '默认ServiceException异常码，应用内部错误'
        502:
          description: '网关错误'
        503:
          description: '服务不可用'
        504:
          description: '表示时间超时，总线超时时间300s'
        888:
          description: '服务摘机，不提供服务'
  /dvcallchainwebsite/v1/callchainqueryservice/querySingleChainByTraceId:
    post:
      summary: '获取某traceid的调用链信息'
      description: '获取某traceid的调用链信息'
      tags:
        - CallChainQueryWebsite
      operationId: querySingleChainByTraceId
      produces:
        - application/json
      consumes:
        - application/json
      x-imports: "com.huawei.cmp.dvcallchainservice.model.callchainquery.SingleChainByTraceIdQueryDTO;com.huawei.cmp.dvcallchainservice.model.topo.CallChainDetailTopoVO"
      parameters:
        - name: singleChainByTraceIdQueryDTO
          in: body
          required: true
          schema:
            $ref: "#/definitions/SingleChainByTraceIdQueryDTO"
      responses:
        200:
          description: '接口正常'
          schema:
            $ref: "#/definitions/CallChainDetailTopoVO"
        400:
          description: '参数错误'
        403:
          description: 'URL鉴权'
        404:
          description: 'rest接口没找到'
        406:
          description: '流控限流'
        500:
          description: '默认ServiceException异常码，应用内部错误'
        502:
          description: '网关错误'
        503:
          description: '服务不可用'
        504:
          description: '表示时间超时，总线超时时间300s'
        888:
          description: '服务摘机，不提供服务'
  /dvcallchainwebsite/v1/callchainqueryservice/querySingleChainAssociatedDrillingInfoByTraceId:
    post:
      summary: '获取某traceid的关联日志钻取信息'
      description: '获取某traceid的关联日志钻取信息'
      tags:
        - CallChainQueryWebsite
      operationId: querySingleChainAssociatedDrillingInfoByTraceId
      produces:
        - application/json
      consumes:
        - application/json
      x-imports: "com.huawei.cmp.dvcallchainservice.model.callchainquery.SingleChainByTraceIdSeqNoQueryDTO;com.huawei.cmp.dvcallchainservice.model.ChainAssociatedDrillingVO"
      parameters:
        - name: singleChainByTraceIdSeqNoQueryDTO
          in: body
          required: true
          schema:
            $ref: "#/definitions/SingleChainByTraceIdSeqNoQueryDTO"
      responses:
        200:
          description: '接口正常'
          schema:
             $ref: "#/definitions/ChainAssociatedDrillingVO"
        400:
          description: '参数错误'
        403:
          description: 'URL鉴权'
        404:
          description: 'rest接口没找到'
        406:
          description: '流控限流'
        500:
          description: '默认ServiceException异常码，应用内部错误'
        502:
          description: '网关错误'
        503:
          description: '服务不可用'
        504:
          description: '表示时间超时，总线超时时间300s'
        888:
          description: '服务摘机，不提供服务'
  /dvcallchainwebsite/v1/callchainqueryservice/queryChainLogInfo:
    post:
      summary: '显示埋点日志详情'
      description: '显示埋点日志详情'
      tags:
        - CallChainQueryWebsite
      operationId: queryChainLogInfo
      produces:
        - application/json
      consumes:
        - application/json
      x-imports: "com.huawei.cmp.dvcallchainservice.model.callchainquery.ChainLogInfoQueryDTO;com.huawei.cmp.dvcallchainservice.model.CallChainLogVO"
      parameters:
        - name: chainLogInfoQueryDTO
          in: body
          required: true
          schema:
            $ref: "#/definitions/ChainLogInfoQueryDTO"
      responses:
        200:
          description: '接口正常'
          schema:
            type: array
            items:
              $ref: "#/definitions/CallChainLogVO"
        400:
          description: '参数错误'
        403:
          description: 'URL鉴权'
        404:
          description: 'rest接口没找到'
        406:
          description: '流控限流'
        500:
          description: '默认ServiceException异常码，应用内部错误'
        502:
          description: '网关错误'
        503:
          description: '服务不可用'
        504:
          description: '表示时间超时，总线超时时间300s'
        888:
          description: '服务摘机，不提供服务'
  /dvcallchainwebsite/v1/callchainqueryservice/queryTraceByFilter:
    post:
      summary: '根据过滤条件查询记录'
      description: '根据过滤条件查询记录'
      tags:
        - CallChainQueryWebsite
      operationId: queryTraceByFilter
      produces:
        - application/json
      consumes:
        - application/json
      x-imports: "com.huawei.cmp.dvcallchainservice.model.callchainquery.TraceByFilterQueryDTO;com.huawei.cmp.dvcallchainservice.model.CallChainLogVO"
      parameters:
        - name: traceByFilterQueryDTO
          in: body
          required: true
          schema:
            $ref: "#/definitions/TraceByFilterQueryDTO"
      responses:
        200:
          description: '接口正常'
          schema:
            type: array
            items:
              $ref: "#/definitions/CallChainLogVO"
        400:
          description: '参数错误'
        403:
          description: 'URL鉴权'
        404:
          description: 'rest接口没找到'
        406:
          description: '流控限流'
        500:
          description: '默认ServiceException异常码，应用内部错误'
        502:
          description: '网关错误'
        503:
          description: '服务不可用'
        504:
          description: '表示时间超时，总线超时时间300s'
        888:
          description: '服务摘机，不提供服务'
  /dvcallchainwebsite/v1/callchainqueryservice/queryNeedQueryBussinessTraceLog:
    post:
      summary: '获取是否打开查询业务调用链日志开关'
      description: '获取是否打开查询业务调用链日志开关'
      tags:
        - CallChainQueryWebsite
      operationId: queryNeedQueryBussinessTraceLog
      produces:
        - application/json
      consumes:
        - application/json
      responses:
        200:
          description: '接口正常'
          schema:
            type: boolean
        400:
          description: '参数错误'
        403:
          description: 'URL鉴权'
        404:
          description: 'rest接口没找到'
        406:
          description: '流控限流'
        500:
          description: '默认ServiceException异常码，应用内部错误'
        502:
          description: '网关错误'
        503:
          description: '服务不可用'
        504:
          description: '表示时间超时，总线超时时间300s'
        888:
          description: '服务摘机，不提供服务'
  /dvcallchainwebsite/v1/callchainqueryservice/queryReentryTraceByTraceId:
    post:
      summary: '根据traceID查询重入的调用链'
      description: '根据traceID查询重入的调用链'
      tags:
        - CallChainQueryWebsite
      operationId: queryReentryTraceByTraceId
      produces:
        - application/json
      consumes:
        - application/json
      x-imports: "com.huawei.cmp.dvcallchainservice.model.callchainquery.ReentryTraceByTraceIdDTO"
      parameters:
        - name: reentryTraceByTraceIdDTO
          in: body
          required: true
          schema:
            $ref: "#/definitions/ReentryTraceByTraceIdDTO"
      responses:
        200:
          description: '接口正常'
          schema:
            type: string
        400:
          description: '参数错误'
        403:
          description: 'URL鉴权'
        404:
          description: 'rest接口没找到'
        406:
          description: '流控限流'
        500:
          description: '默认ServiceException异常码，应用内部错误'
        502:
          description: '网关错误'
        503:
          description: '服务不可用'
        504:
          description: '表示时间超时，总线超时时间300s'
        888:
          description: '服务摘机，不提供服务'
  /dvcallchainwebsite/v1/callchainqueryservice/queryLongTimeRst:
    get:
      summary: '查询异步长任务数据'
      description: '查询异步长任务数据'
      tags:
        - CallChainQueryWebsite
      operationId: queryLongTimeRst
      produces:
        - application/json
      consumes:
        - application/json
      x-imports: "com.huawei.cmp.dvcallchainservice.model.LongTimeApiRsp"
      parameters:
        - name: taskId
          description: 'taskId'
          in: query
          required: true
          type: string
          maxLength: 64
      responses:
        200:
          description: '接口正常'
          schema:
            $ref: "#/definitions/LongTimeApiRsp"
        400:
          description: '参数错误'
        403:
          description: 'URL鉴权'
        404:
          description: 'rest接口没找到'
        406:
          description: '流控限流'
        500:
          description: '默认ServiceException异常码，应用内部错误'
        502:
          description: '网关错误'
        503:
          description: '服务不可用'
        504:
          description: '表示时间超时，总线超时时间300s'
        888:
          description: '服务摘机，不提供服务'