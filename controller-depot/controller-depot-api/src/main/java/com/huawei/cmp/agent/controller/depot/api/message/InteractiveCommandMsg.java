/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.cmp.agent.controller.depot.api.message;

import com.google.protobuf.Descriptors;

/**
 * Protobuf type {@code InteractiveCommandMsg}
 *
 * <AUTHOR>
 * @version V800R001C02
 * @since 2020 -03-29
 */
public final class InteractiveCommandMsg extends com.google.protobuf.GeneratedMessage
    implements
    // @@protoc_insertion_point(message_implements:InteractiveCommandMsg)
    InteractiveCommandMsgOrBuilder {
    // Use InteractiveCommandMsg.newRequestFileMsgBuilder() to construct.
    private InteractiveCommandMsg(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
        super(builder);
    }

    private InteractiveCommandMsg() {
        messageType_ = 0;
        command_ = "";
    }

    @Override
    public final com.google.protobuf.UnknownFieldSet getUnknownFields() {
        return com.google.protobuf.UnknownFieldSet.getDefaultInstance();
    }

    private InteractiveCommandMsg(
        com.google.protobuf.CodedInputStream input, com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
        this();
        int mutable_bitField0_ = 0;
        try {
            boolean interactivedone = false;
            while (!interactivedone) {
                int tag = input.readTag();
                switch (tag) {
                    case 0:
                        interactivedone = true;
                        break;
                    default: {
                        if (!input.skipField(tag)) {
                            interactivedone = true;
                        }
                        break;
                    }
                    case 8: {
                        messageType_ = input.readInt32();
                        break;
                    }
                    case 18: {
                        String s = input.readStringRequireUtf8();

                        command_ = s;
                        break;
                    }
                }
            }
        } catch (com.google.protobuf.InvalidProtocolBufferException commandMsgEx) {
            throw commandMsgEx.setUnfinishedMessage(this);
        } catch (java.io.IOException commandMsgEx) {
            throw new com.google.protobuf.InvalidProtocolBufferException(commandMsgEx).setUnfinishedMessage(this);
        } finally {
            makeExtensionsImmutable();
        }
    }

    /**
     * Gets descriptor *
     *
     * @return the descriptor
     */
    public static final Descriptors.Descriptor getDescriptor() {
        return Message.internal_static_InteractiveCommandMsg_descriptor;
    }

    /**
     * 获取内部附加信息表字段信息
     *
     * @return FieldAccessorTable
     * @since V100R001C20
     */
    protected FieldAccessorTable internalGetFieldAccessorTable() {
        return Message.internal_static_InteractiveCommandMsg_fieldAccessorTable.ensureFieldAccessorsInitialized(
            InteractiveCommandMsg.class, Builder.class);
    }

    /**
     * 消息类型字段数
     */
    public static final int MESSAGETYPE_FIELD_NUMBER = 1;

    private int messageType_;

    /**
     * <code>optional int32 messageType = 1;</code>
     */
    public int getMessageType() {
        return messageType_;
    }

    /**
     * 命令字段数
     */
    public static final int COMMAND_FIELD_NUMBER = 2;

    private volatile Object command_;

    /**
     * <code>optional string command = 2;</code>
     */
    public String getCommand() {
        Object ref = command_;
        if (ref instanceof String) {
            return (String) ref;
        } else {
            com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
            String s = bs.toStringUtf8();
            command_ = s;
            return s;
        }
    }

    /**
     * <code>optional string command = 2;</code>
     */
    public com.google.protobuf.ByteString getCommandBytes() {
        Object command = command_;
        if (command instanceof String) {
            com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((String) command);
            command_ = b;
            return b;
        } else {
            return (com.google.protobuf.ByteString) command;
        }
    }

    private byte commandMemoizedIsInitialized = -1;

    /**
     * 是否为初始化状态
     *
     * @return boolean
     * @since V100R001C20
     */
    public final boolean isInitialized() {
        byte isInitialized = commandMemoizedIsInitialized;
        if (isInitialized == 1) return true;
        if (isInitialized == 0) return false;

        commandMemoizedIsInitialized = 1;
        return true;
    }

    /**
     * 写操作
     *
     * @param output 输出流对象
     * @throws java.io.IOException io异常
     * @since V100R001C20
     */
    public void writeTo(com.google.protobuf.CodedOutputStream output) throws java.io.IOException {
        if (messageType_ != 0) {
            output.writeInt32(1, messageType_);
        }
        if (!getCommandBytes().isEmpty()) {
            com.google.protobuf.GeneratedMessage.writeString(output, 2, command_);
        }
    }

    /**
     * 获取序列化大小
     *
     * @return int
     * @since V100R001C20
     */
    public int getSerializedSize() {
        int interactiveCommandSize = memoizedSize;
        if (interactiveCommandSize != -1) return interactiveCommandSize;

        interactiveCommandSize = 0;
        if (messageType_ != 0) {
            interactiveCommandSize += com.google.protobuf.CodedOutputStream.computeInt32Size(1, messageType_);
        }
        if (!getCommandBytes().isEmpty()) {
            interactiveCommandSize += com.google.protobuf.GeneratedMessage.computeStringSize(2, command_);
        }
        memoizedSize = interactiveCommandSize;
        return interactiveCommandSize;
    }

    private static final long serialVersionUID = 0L;

    /**
     * Parse from interactive command msg
     *
     * @param data data
     * @return the interactive command msg
     * @throws InvalidProtocolBufferException invalid protocol buffer exception
     */
    public static InteractiveCommandMsg parseFrom(com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
    }

    /**
     * Parse from interactive command msg
     *
     * @param data data
     * @param extensionRegistry extension registry
     * @return the interactive command msg
     * @throws InvalidProtocolBufferException invalid protocol buffer exception
     */
    public static InteractiveCommandMsg parseFrom(
        com.google.protobuf.ByteString data, com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
    }

    /**
     * Parse from interactive command msg
     *
     * @param data data
     * @return the interactive command msg
     * @throws InvalidProtocolBufferException invalid protocol buffer exception
     */
    public static InteractiveCommandMsg parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data);
    }

    /**
     * Parse from interactive command msg
     *
     * @param data data
     * @param extensionRegistry extension registry
     * @return the interactive command msg
     * @throws InvalidProtocolBufferException invalid protocol buffer exception
     */
    public static InteractiveCommandMsg parseFrom(
        byte[] data, com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
        return PARSER.parseFrom(data, extensionRegistry);
    }

    /**
     * Parse from interactive command msg
     *
     * @param input input
     * @return the interactive command msg
     * @throws IOException io exception
     */
    public static InteractiveCommandMsg parseFrom(java.io.InputStream input) throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage.parseWithIOException(PARSER, input);
    }

    /**
     * Parse from interactive command msg
     *
     * @param input input
     * @param extensionRegistry extension registry
     * @return the interactive command msg
     * @throws IOException io exception
     */
    public static InteractiveCommandMsg parseFrom(
        java.io.InputStream input, com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage.parseWithIOException(PARSER, input, extensionRegistry);
    }

    /**
     * Parse delimited from interactive command msg
     *
     * @param input input
     * @return the interactive command msg
     * @throws IOException io exception
     */
    public static InteractiveCommandMsg parseDelimitedFrom(java.io.InputStream input) throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage.parseDelimitedWithIOException(PARSER, input);
    }

    /**
     * Parse delimited from interactive command msg
     *
     * @param input input
     * @param extensionRegistry extension registry
     * @return the interactive command msg
     * @throws IOException io exception
     */
    public static InteractiveCommandMsg parseDelimitedFrom(
        java.io.InputStream input, com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage.parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }

    /**
     * Parse from interactive command msg
     *
     * @param input input
     * @return the interactive command msg
     * @throws IOException io exception
     */
    public static InteractiveCommandMsg parseFrom(com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage.parseWithIOException(PARSER, input);
    }

    /**
     * Parse from interactive command msg
     *
     * @param input input
     * @param extensionRegistry extension registry
     * @return the interactive command msg
     * @throws IOException io exception
     */
    public static InteractiveCommandMsg parseFrom(
        com.google.protobuf.CodedInputStream input, com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
        return com.google.protobuf.GeneratedMessage.parseWithIOException(PARSER, input, extensionRegistry);
    }

    /**
     * 新建类型构建器
     *
     * @return Builder
     * @since V100R001C20
     */
    public Builder newBuilderForType() {
        return newBuilder();
    }

    /**
     * New builder builder
     *
     * @return the builder
     */
    public static Builder newBuilder() {
        return DEFAULT_INSTANCE.toBuilder();
    }

    /**
     * New builder builder
     *
     * @param prototype prototype
     * @return the builder
     */
    public static Builder newBuilder(InteractiveCommandMsg prototype) {
        return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }

    /**
     * to builder
     *
     * @return Builder
     * @since V100R001C20
     */
    public Builder toBuilder() {
        return this == DEFAULT_INSTANCE ? new Builder() : new Builder().mergeFrom(this);
    }

    @Override
    protected Builder newBuilderForType(BuilderParent parent) {
        Builder commandBuilder = new Builder(parent);
        return commandBuilder;
    }

    /**
     * Protobuf type {@code InteractiveCommandMsg}
     *
     * @since V800R001C02
     */
    public static final class Builder extends com.google.protobuf.GeneratedMessage.Builder<Builder>
        implements
        // @@protoc_insertion_point(builder_implements:InteractiveCommandMsg)
        InteractiveCommandMsgOrBuilder {
        /**
         * Gets descriptor *
         *
         * @return the descriptor
         */
        public static final com.google.protobuf.Descriptors.Descriptor getDescriptor() {
            return Message.internal_static_InteractiveCommandMsg_descriptor;
        }

        /**
         * 获取内部附加信息表字段信息
         *
         * @return FieldAccessorTable
         * @since V100R001C20
         */
        protected FieldAccessorTable internalGetFieldAccessorTable() {
            return Message.internal_static_InteractiveCommandMsg_fieldAccessorTable.ensureFieldAccessorsInitialized(
                InteractiveCommandMsg.class, Builder.class);
        }

        // Construct using InteractiveCommandMsg.newRequestFileMsgBuilder()
        private Builder() {
            commandInitialization();
        }

        private Builder(BuilderParent parent) {
            super(parent);
            commandInitialization();
        }

        private void commandInitialization() {}

        /**
         * 资源释放
         *
         * @return Builder
         * @since V100R001C20
         */
        public Builder clear() {
            super.clear();
            messageType_ = 0;

            command_ = "";

            return this;
        }

        /**
         * 获取消息类型详细信息
         *
         * @return Descriptor
         * @since V100R001C20
         */
        public com.google.protobuf.Descriptors.Descriptor getDescriptorForType() {
            return Message.internal_static_InteractiveCommandMsg_descriptor;
        }

        public InteractiveCommandMsg getDefaultInstanceForType() {
            return InteractiveCommandMsg.getDefaultInstance();
        }

        /**
         * 构建交互命令消息
         *
         * @return InteractiveCommandMsg
         * @since V100R001C20
         */
        public InteractiveCommandMsg build() {
            InteractiveCommandMsg result = buildPartial();
            if (!result.isInitialized()) {
                throw newUninitializedMessageException(result);
            }
            return result;
        }

        /**
         * 构建部分交互命令消息
         *
         * @return InteractiveCommandMsg
         * @since V100R001C20
         */
        public InteractiveCommandMsg buildPartial() {
            InteractiveCommandMsg result = new InteractiveCommandMsg(this);
            result.messageType_ = messageType_;
            result.command_ = command_;
            onBuilt();
            return result;
        }

        /**
         * 合并其它交互命令消息
         *
         * @param other 其它交互命令消息
         * @return Builder
         * @since V100R001C20
         */
        public Builder mergeFrom(com.google.protobuf.Message other) {
            if (other instanceof InteractiveCommandMsg) {
                return mergeFrom((InteractiveCommandMsg) other);
            } else {
                super.mergeFrom(other);
                return this;
            }
        }

        /**
         * Merge from builder
         *
         * @param interactiveCommandMsg interactive command msg
         * @return the builder
         */
        public Builder mergeFrom(InteractiveCommandMsg interactiveCommandMsg) {
            if (interactiveCommandMsg == InteractiveCommandMsg.getDefaultInstance()) return this;
            if (interactiveCommandMsg.getMessageType() != 0) {
                setMessageType(interactiveCommandMsg.getMessageType());
            }
            if (!interactiveCommandMsg.getCommand().isEmpty()) {
                command_ = interactiveCommandMsg.command_;
                onChanged();
            }
            onChanged();
            return this;
        }

        /**
         * 是否为初始化状态
         *
         * @return boolean
         * @since V100R001C20
         */
        public final boolean isInitialized() {
            return true;
        }

        /**
         * 合并交互命令消息
         *
         * @param stream 流式对象
         * @param lite 扩展注册
         * @return Builder
         * @throws java.io.IOException 异常
         * @since V100R001C20
         */
        public Builder mergeFrom(
            com.google.protobuf.CodedInputStream stream, com.google.protobuf.ExtensionRegistryLite lite)
            throws java.io.IOException {
            InteractiveCommandMsg msg = null;
            try {
                msg = PARSER.parsePartialFrom(stream, lite);
            } catch (com.google.protobuf.InvalidProtocolBufferException commandEx) {
                msg = (InteractiveCommandMsg) commandEx.getUnfinishedMessage();
                throw commandEx.unwrapIOException();
            } finally {
                if (msg != null) {
                    mergeFrom(msg);
                }
            }
            return this;
        }

        private int messageType_;

        public int getMessageType() {
            return messageType_;
        }

        /**
         * Sets message type *
         *
         * @param i the
         * @return the message type
         */
        public Builder setMessageType(int i) {
            messageType_ = i;
            onChanged();
            return this;
        }

        /**
         * <code>optional int32 messageType = 1;</code>
         *
         * @return the builder
         */
        public Builder clearMessageType() {
            messageType_ = 0;
            onChanged();
            return this;
        }

        private Object command_ = "";

        /**
         * <code>optional string command = 2;</code>
         */
        public String getCommand() {
            Object ref = command_;
            if (!(ref instanceof String)) {
                com.google.protobuf.ByteString bs = (com.google.protobuf.ByteString) ref;
                String s = bs.toStringUtf8();
                command_ = s;
                return s;
            } else {
                return (String) ref;
            }
        }

        /**
         * <code>optional string command = 2;</code>
         */
        public com.google.protobuf.ByteString getCommandBytes() {
            Object ref = command_;
            if (ref instanceof String) {
                com.google.protobuf.ByteString b = com.google.protobuf.ByteString.copyFromUtf8((String) ref);
                command_ = b;
                return b;
            } else {
                return (com.google.protobuf.ByteString) ref;
            }
        }

        /**
         * Sets command *
         *
         * @param command command
         * @return the command
         */
        public Builder setCommand(String command) {
            if (command == null) {
                throw new NullPointerException();
            }

            command_ = command;
            onChanged();
            return this;
        }

        /**
         * <code>optional string command = 2;</code>
         *
         * @return the builder
         */
        public Builder clearCommand() {
            command_ = getDefaultInstance().getCommand();
            onChanged();
            return this;
        }

        /**
         * <code>optional string command = 2;</code>
         *
         * @param commandBytes command bytes
         * @return the command bytes
         */
        public Builder setCommandBytes(com.google.protobuf.ByteString commandBytes) {
            if (commandBytes == null) {
                throw new NullPointerException();
            }
            checkByteStringIsUtf8(commandBytes);

            command_ = commandBytes;
            onChanged();
            return this;
        }

        /**
         * 设置未知字段
         *
         * @param fieldSet 未知字段列表
         * @return Builder
         * @since V100R001C20
         */
        public final Builder setUnknownFields(final com.google.protobuf.UnknownFieldSet fieldSet) {
            return this;
        }

        /**
         * 合并未知字段
         *
         * @param unknownFields 未知字段
         * @return Builder
         * @since V100R001C20
         */
        public final Builder mergeUnknownFields(final com.google.protobuf.UnknownFieldSet unknownFields) {
            return this;
        }

        // @@protoc_insertion_point(builder_scope:InteractiveCommandMsg)
    }

    // @@protoc_insertion_point(class_scope:InteractiveCommandMsg)
    private static final InteractiveCommandMsg DEFAULT_INSTANCE;

    static {
        DEFAULT_INSTANCE = new InteractiveCommandMsg();
    }

    /**
     * Gets default instance *
     *
     * @return the default instance
     */
    public static InteractiveCommandMsg getDefaultInstance() {
        return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<InteractiveCommandMsg> PARSER =
        new com.google.protobuf.AbstractParser<InteractiveCommandMsg>() {
            public InteractiveCommandMsg parsePartialFrom(
                com.google.protobuf.CodedInputStream input,
                com.google.protobuf.ExtensionRegistryLite extensionRegistry)
                throws com.google.protobuf.InvalidProtocolBufferException {
                return new InteractiveCommandMsg(input, extensionRegistry);
            }
        };

    /**
     * Parser com . google . protobuf . parser
     *
     * @return the com . google . protobuf . parser
     */
    public static com.google.protobuf.Parser<InteractiveCommandMsg> parser() {
        return PARSER;
    }

    @Override
    public com.google.protobuf.Parser<InteractiveCommandMsg> getParserForType() {
        return PARSER;
    }

    /**
     * 获取默认实例类型
     *
     * @return InteractiveCommandMsg
     * @since V100R001C20
     */
    public InteractiveCommandMsg getDefaultInstanceForType() {
        return DEFAULT_INSTANCE;
    }
}
