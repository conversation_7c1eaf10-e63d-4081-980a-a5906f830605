/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2012-2020. All rights reserved.
 */

package com.huawei.cmp.foundation.task.service;

import com.huawei.baize.avauger.base.Preconditions;
import com.huawei.bsp.log.OssLog;
import com.huawei.bsp.log.OssLogFactory;
import com.huawei.bsp.remoteservice.exception.ServiceException;
import com.huawei.cmp.foundation.task.IExecutor;
import com.huawei.cmp.foundation.task.ITaskPersistence;
import com.huawei.cmp.foundation.task.Task;
import com.huawei.cmp.foundation.task.TaskException;
import com.huawei.cmp.foundation.task.cell.TaskConfigValidator;
import com.huawei.cmp.foundation.task.constant.Constants;
import com.huawei.cmp.foundation.task.constant.TaskConcurrentLevel;
import com.huawei.cmp.foundation.task.constant.TaskStatus;
import com.huawei.cmp.foundation.task.constant.TaskType;
import com.huawei.cmp.foundation.task.exception.TaskRunTimeException;
import com.huawei.cmp.foundation.task.executor.ExecutorFactory;
import com.huawei.cmp.foundation.task.persistence.TaskListenerBeanPostProcessor;
import com.huawei.cmp.foundation.task.service.cluster.ClusterTask;

import com.alibaba.fastjson2.JSON;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * Distributed task
 *
 * @since V800R001C02
 */
public class DistributedTask implements DistributedTaskMBean {
    private static OssLog LOGGER = OssLogFactory.getLogger(DistributedTask.class);

    @Autowired
    @Qualifier("zkTaskPersistence")
    private ITaskPersistence taskPersistence;

    @Autowired
    @Qualifier("taskListenProcessor")
    private TaskListenerBeanPostProcessor taskPostProcessor;

    @Autowired
    @Qualifier("taskFactory")
    private ExecutorFactory taskFactory;

    @Override
    public void registerClusterTask(String name, String group, String expression, String targetBean, TaskType type,
        long timeout, TaskConcurrentLevel level, TaskStatus status, Map<String, String> configMap) {
        LOGGER.info("Start to register task to cluster. [name={},group={}]", name, group);
        LOGGER.info("Start to register task to cluster. [{},{},{},{},{},{},{},{},{}]", name, group, expression,
            targetBean, type, timeout, level, status, configMap);
        Preconditions.checkArgument(
            StringUtils.isNotBlank(name) && StringUtils.isNotBlank(group) && StringUtils.isNotBlank(expression));

        TaskType typeValid = TaskConfigValidator.checkTaskType(type);
        String expressionValid = TaskConfigValidator.checkTaskExpression(type, expression);
        Long timeoutValid = TaskConfigValidator.checkTaskTimeout(timeout);
        TaskConcurrentLevel levelValid = TaskConfigValidator.checkTaskLevel(level);
        TaskStatus statusValid = TaskConfigValidator.checkTaskStatus(status);

        try {
            // 将元数据写入/cmp/task，该动作只在一个节点做，集群其他节点监听cluster_task变化，初始化quartz任务
            writeTaskMetadataToCMPTask(name, group, expressionValid, targetBean, typeValid, timeoutValid, levelValid,
                statusValid, configMap);
        } catch (Exception e) {
            LOGGER.error("Fail to register cluster task.[name={},group={}]", name, group);
            LOGGER.error("Fail to register cluster task.[name={},group={}]", name, group, e);
            throw new TaskRunTimeException("Fail to register cluster task.", e);
        }
        LOGGER.info("Success to register task to cluster. [name={},group={}]", name, group);
        LOGGER.info("Success to register task to cluster. [name={},group={}]", name, group);
    }

    private void writeTaskMetadataToCMPTask(String name, String group, String expression, String targetBean,
        TaskType type, long timeout, TaskConcurrentLevel level, TaskStatus status, Map<String, String> configMap)
        throws Exception {
        if (!isExist(name, group)) {
            Task task = createTaskInConfig(name, group, expression, targetBean, type, timeout, level, status,
                configMap);
            taskPersistence.createTaskConfig(task);
            LOGGER.info("writeTaskMetadataToCMPTask createTaskInConfig.[{}]", task);
            LOGGER.info("write task metadata to /cmp/task. [name={},group={}]", name, group);

            // 将元数据写入cluster_task节点，用于集群监听
            ClusterTask clusterTask = new ClusterTask();
            clusterTask.setGroup(group).setName(name);
            byte[] value = JSON.toJSONString(clusterTask).getBytes(Constants.ENCODING_UTF8);
            taskPersistence.createClusterTaskConfig(name, group, value);
            LOGGER.info("write task metadata to cluster. [name={},group={}]", name, group);
        } else {
            Task task = updateTaskInConfig(name, group, expression, targetBean, type, timeout, level, status,
                configMap);
            LOGGER.info("writeTaskMetadataToCMPTask updateTaskInConfig.[{}]", task);
            taskPersistence.updateTaskConfig(task);
        }
    }

    @Override
    public void unregisterClusterTask(String name, String group) {
        LOGGER.info("Start to unregister cluster task. [name={},group={}]", name, group);
        LOGGER.info("Start to unregister cluster task. [name={},group={}]", name, group);

        try {
            taskPersistence.deleteClusterTaskConfig(name, group);
        } catch (Exception e) {
            LOGGER.error("Fail to unregister cluster task. [name={},group={}]", name, group, e);
            throw new TaskRunTimeException("Fail to unregister cluster task.", e);
        }

        LOGGER.info("Success to unregister cluster task. [name={},group={}]", name, group);
        LOGGER.info("Success to unregister cluster task. [name={},group={}]", name, group);
    }

    @Override
    public void updateClusterTask(String name, String group, String expression, TaskType type, long timeOut,
        TaskConcurrentLevel level, Map<String, String> configMap) {
        LOGGER.info("Start to update cluster task  [name={},group={}]", name, group);
        LOGGER.info("Start to update cluster task [name={},group={}]", name, group);
        Preconditions.checkState(isExist(name, group));
        try {
            Task taskInZk = taskPersistence.getTask(name, group);
            if (null == taskInZk) {
                LOGGER.warn("task is not existed [name={},group={}]", name, group);
                return;
            }
            // 只需要更新zk节点值，内部会监听值的变化重新调度任务
            this.writeTaskMetadataToCMPTask(name, group, expression, taskInZk.getTargetBean(), type, timeOut, level,
                taskInZk.getStatus(), configMap);
        } catch (Exception e) {
            LOGGER.error("Fail to update cluster task. [name={},group={}]", name, group, e);
            throw new TaskRunTimeException("Fail to update cluster task.", e);
        }

        LOGGER.info("Success to update cluster task [name={},group={}]", name, group);
        LOGGER.info("Success to update cluster task [name={},group={}]", name, group);
    }

    @Override
    public void start(String name, String group) {
        LOGGER.info("invoke start to run task. [name={},group={}]", name, group);
        changeTaskStatus(name, group, TaskStatus.RUNNING);
    }

    @Override
    public void stop(String name, String group) {
        LOGGER.info("invoke stop to pause task. [name={},group={}]", name, group);
        changeTaskStatus(name, group, TaskStatus.STOPPED);
    }

    private void changeTaskStatus(String name, String group, TaskStatus status) {
        if (taskPersistence.isExists(name, group)) {
            try {
                taskPersistence.configTaskItem(name, group, Constants.ZK_TASK_STATUS,
                    status.name().getBytes(Constants.ENCODING_UTF8));
            } catch (Exception e) {
                String errMsg = "Fail to config the task name: " + name + " in group: " + group
                    + " for unsupported encoding.";
                LOGGER.error(errMsg);
                throw new TaskRunTimeException(errMsg, e);
            }
        } else {
            throw new TaskRunTimeException("task is not exist.");
        }
    }

    // need to lock
    @Override
    public void unregister(String name, String group) {
        LOGGER.info("invoke unregister to remove task. [name={},group={}]", name, group);
        try {
            if (taskPersistence.isExists(name, group)) {
                stop(name, group);
                Task task = taskPersistence.getTask(name, group);
                if (null != task) {
                    // clean up action before remove
                    taskPostProcessor.postProcessBeforeRemove(name, group);
                    taskPersistence.delete(name, group);
                }
            }
        } catch (Exception e) {
            LOGGER.error("Fail to unregister task. [name={},group={}]", name, group, e);
            throw new TaskRunTimeException(e.getMessage());
        }
    }

    @Override
    public void register(String name, String group, String expression, Object targetBean, TaskType type, long timeOut,
        TaskConcurrentLevel level, TaskStatus status, Map<String, String> configMap) {
        LOGGER.info("Start to register task. [name={},group={}]", name, group);
        LOGGER.info("Start to register task. [name={},group={}]", name, group);

        try {
            Task task;

            if (!isExist(name, group)) {
                task = createTaskInConfig(name, group, expression, targetBean, type, timeOut, level, status, configMap);
                taskPersistence.createTaskConfig(task);
            } else {
                task = updateTaskInConfig(name, group, expression, targetBean, type, timeOut, level, status, configMap);
                taskPersistence.updateTaskConfig(task);
            }

            initTaskInScheduler(task, targetBean);

            LOGGER.info("End register to add task. [name={},group={}]", name, group);
            LOGGER.info("End register to add task. [name={},group={}]", name, group);
        } catch (Exception e) {
            LOGGER.error("Failed to register task. [name={},group={}]", name, group, e);
            throw new TaskRunTimeException(e.getMessage());
        }
    }

    @Override
    public void register(String name, String group, String expression, Object targetBean, long timeOut) {
        this.register(name, group, expression, targetBean, null, timeOut, null, null, null);
    }

    @Override
    public void register(String name, String group, String expression, Object targetBean) {
        this.register(name, group, expression, targetBean, null, 0L, null, null, null);
    }

    @Override
    public void update(String name, String group, String expression, TaskType type, long timeOut,
        TaskConcurrentLevel level, Map<String, String> configMap) {
        try {
            Preconditions.checkState(isExist(name, group));
            Task taskInZk = taskPersistence.getTask(name, group);

            if (null == taskInZk) {
                LOGGER.warn("{} in group {} is not existed.", name, group);
                return;
            }

            Map<String, String> configInZk = taskInZk.toProperties();

            type = TaskConfigValidator.checkTaskType(type, taskInZk.getType());

            configInZk.put(Constants.ZK_TASK_TYPE, type.name());

            expression = TaskConfigValidator.checkTaskExpression(type, expression, taskInZk.getExpression());

            configInZk.put(Constants.ZK_TASK_EXPRESSION, expression);

            timeOut = TaskConfigValidator.checkTaskTimeout(timeOut);

            configInZk.put(Constants.ZK_TASK_TIMEOUT, String.valueOf(timeOut));

            level = TaskConfigValidator.checkTaskLevel(level, taskInZk.getLevel());

            configInZk.put(Constants.ZK_TASK_LEVEL, level.name());

            if (null != configMap) {
                for (Entry<String, String> entry : configMap.entrySet()) {
                    configInZk.put(entry.getKey(), entry.getValue());
                }
            }

            Task newTask = Task.createTask(name, group, configInZk);

            IExecutor targetBean = taskFactory.fetchTask(newTask.getTargetBean());

            // 清理原有的task
            unregister(name, group);

            // 重新注册task
            taskPersistence.createTaskConfig(newTask);

            initTaskInScheduler(newTask, targetBean);
        } catch (Exception e) {
            LOGGER.error("Fail to update task. [name={},group={}]", name, group, e);
            throw new TaskRunTimeException(e.getMessage());
        }
    }

    @Override
    public boolean isExist(String name, String group) {
        Preconditions.checkArgument(StringUtils.isNotEmpty(name) && StringUtils.isNotEmpty(group));
        return taskPersistence.isExists(name, group);
    }

    @Override
    public Map<String, Map<String, TaskStatus>> queryAllTaskName() {
        Map<String, Map<String, TaskStatus>> allNameMap = new HashMap<String, Map<String, TaskStatus>>();

        try {
            List<Task> allTasks = taskPersistence.getAllTasks();

            for (Task task : allTasks) {
                String groupName = task.getGroup();

                Map<String, TaskStatus> groupTaskMap = allNameMap.get(groupName);

                if (null == groupTaskMap) {
                    groupTaskMap = new HashMap<String, TaskStatus>();

                    allNameMap.put(groupName, groupTaskMap);
                }

                groupTaskMap.put(task.getName(), task.getStatus());
            }

        } catch (Exception e) {
            // handle exception
            throw new TaskRunTimeException(e.getMessage());
        }

        return allNameMap;
    }

    @Override
    public Map<String, TaskStatus> queryGroupTaskName(String group) {
        Preconditions.checkArgument(StringUtils.isNotBlank(group));

        Map<String, TaskStatus> allNameMap = new HashMap<String, TaskStatus>();

        try {
            List<Task> allTasks = taskPersistence.getAllTasks();

            for (Task task : allTasks) {
                if (StringUtils.equals(group, task.getGroup())) {
                    allNameMap.put(task.getName(), task.getStatus());
                }
            }
        } catch (Exception e) {
            // handle exception
            throw new TaskRunTimeException(e.getMessage());
        }

        return allNameMap;
    }

    private Task createTaskInConfig(String name, String group, String expression, Object targetBean, TaskType type,
        long timeOut, TaskConcurrentLevel level, TaskStatus status, Map<String, String> configMap)
        throws TaskException {
        LOGGER.info("create task in config. [{},{},{},{},{},{},{},{},{}]", name, group, expression, targetBean, type,
            timeOut, level, status, configMap);
        Preconditions.checkArgument(
            StringUtils.isNotBlank(name) && StringUtils.isNotBlank(group) && StringUtils.isNotBlank(expression));

        Map<String, String> args = new HashMap<>();

        args.put(Constants.ZK_TASK_TARGET_BEAN, TaskConfigValidator.checkTaskTargetBean(name, group, targetBean));
        TaskType taskType = TaskConfigValidator.checkTaskType(type);
        args.put(Constants.ZK_TASK_TYPE, taskType.name());
        args.put(Constants.ZK_TASK_EXPRESSION, TaskConfigValidator.checkTaskExpression(taskType, expression));
        args.put(Constants.ZK_TASK_TIMEOUT, String.valueOf(TaskConfigValidator.checkTaskTimeout(timeOut)));
        args.put(Constants.ZK_TASK_LEVEL, TaskConfigValidator.checkTaskLevel(level).name());
        args.put(Constants.ZK_TASK_STATUS, TaskConfigValidator.checkTaskStatus(status).name());

        if (null != configMap) {
            args.putAll(configMap);
        }

        return Task.createTask(name, group, args);
    }

    private Task updateTaskInConfig(String name, String group, String expression, Object targetBean, TaskType type,
        long timeOut, TaskConcurrentLevel level, TaskStatus status, Map<String, String> configMap)
        throws TaskException {
        LOGGER.info("update task in config. [{},{},{},{},{},{},{},{},{}]", name, group, expression, targetBean, type,
            timeOut, level, status, configMap);
        Task task = createTaskInConfig(name, group, expression, targetBean, type, timeOut, level, status, configMap);

        Task taskInZk = taskPersistence.getTask(name, group);

        if (null == taskInZk) {
            LOGGER.warn("{} in group {} is not existed.", name, group);
            return null;
        }
        // 同步zk中任务状态信息
        task.setSequence(taskInZk.getSequence());
        task.setNextTimer(taskInZk.getNextTimer());
        task.setConfigMap(taskInZk.getConfigMap());

        return task;
    }

    /**
     * If task config is exist in zk,just initialize task in quartz scheduler...<一句话功能简述>
     *
     * @param task task
     * @param targetBean target bean
     * @throws ServiceException ServiceException
     */
    public void initTaskInScheduler(Task task, Object targetBean) throws ServiceException {
        if (targetBean == null) {
            throw new ServiceException("target bean is null.");
        }
        taskPostProcessor.postProcessAfterRegistery(targetBean, task);
    }

    @Override
    public void updateTaskItem(String name, String group, String item, String value) {
        Preconditions.checkArgument(
            StringUtils.isNotBlank(name) && StringUtils.isNotBlank(group) && StringUtils.isNotBlank(item) && StringUtils
                .isNotBlank(value));

        LOGGER.info("Update task name: {} group: {} item: {} with value: {}", name, group, item, value);
        try {
            try {
                taskPersistence.configTaskItem(name, group, item, value.getBytes(Constants.ENCODING_UTF8));
            } catch (UnsupportedEncodingException e) {
                String errMsg = "Fail to update the task item: " + item + " in name: " + name + ",group: " + group
                    + " for unsupported encoding.";

                LOGGER.error(errMsg);

                throw new TaskException(errMsg, e);
            }
        } catch (RuntimeException e) {
            throw new TaskRunTimeException(e.getMessage());
        } catch (Exception e) {
            // handle exception
            throw new TaskRunTimeException(e.getMessage());
        }
    }

    @Override
    public String fetchTaskItem(String name, String group, String item) {
        Preconditions.checkArgument(
            StringUtils.isNotBlank(name) && StringUtils.isNotBlank(group) && StringUtils.isNotBlank(item));

        LOGGER.info("Fetch task name:{} group:{} item:{}", name, group, item);

        String tmpValue = null;

        try {
            byte[] value = taskPersistence.queryTaskItem(name, group, item);

            if (null != value) {
                try {
                    tmpValue = new String(value, Constants.ENCODING_UTF8);
                } catch (UnsupportedEncodingException e) {
                    String errMsg = "Fail to fetch the task item: " + item + " in name: " + name + ",group: " + group
                        + " for unsupported encoding.";

                    LOGGER.error(errMsg);

                    throw new TaskException(errMsg, e);
                }
            } else {
                LOGGER.error("task name: " + name + " group: " + group + " item: " + item + " is not existed... ");
            }
        } catch (RuntimeException e) {
            throw new TaskRunTimeException(e.getMessage());
        } catch (Exception e) {
            throw new TaskRunTimeException(e.getMessage());
        }

        return tmpValue;
    }
}
