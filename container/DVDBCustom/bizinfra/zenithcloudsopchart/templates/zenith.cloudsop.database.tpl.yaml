{{$root := .Values}}
{{$equivalentNE := default 15000 ((default dict .Values.global.scale).equivalentNE) | int -}}
{{$instanceRoot := $root.cloudsop.default.zenith.instance}}
{{- if eq "std" (.Values.build.app.producttype | toString) -}}
  {{- if hasPrefix "mae" (lower (.Values.global.scene | toString)) -}}
    {{$instanceRoot = (index $root.cloudsop "mae").zenith.instance}}
  {{- else if hasPrefix "qiankunop" (lower (.Values.global.scene | toString)) -}}
    {{$instanceRoot = (index $root.cloudsop "qiankunop").zenith.instance}}
  {{- else if hasPrefix "ccae" (lower (.Values.global.scene | toString)) -}}
    {{$instanceRoot = (index $root.cloudsop "ccae").zenith.instance}}
  {{- end -}}
  {{- else if eq "lite" (.Values.build.app.producttype | toString) -}}
    {{$instanceRoot = $root.cloudsoplite.default.zenith.instance}}
  {{$equivalentNE = 3000}}
{{- end -}}
{{$totalStorage := dict "default" 0 }}
{{range $key, $value := $instanceRoot }}
  {{- if and (ne $key "default") (ne $key "customer") }}
    {{- if (index $instanceRoot $key "feaSpec")}}
        {{- $already := false -}}
              {{$instanceName := $key}}
              {{- if $instanceRoot -}}
                {{- if $instanceName -}}
                  {{- if (index $instanceRoot $instanceName) -}}
                    {{- if (index $instanceRoot $instanceName "feaSpec") -}}
                      {{- range (index $instanceRoot $instanceName "feaSpec") -}}
                        {{- if not $already -}}
                            {{- $already = true -}}
                            {{- range $storage := .storages}}
                              {{- $totalStorage := set $totalStorage $key .size -}}
                            {{- end -}}
                        {{- end -}}
                      {{- end -}}
                    {{- end -}}
                  {{- end -}}
                {{- end -}}
              {{- end -}}
    {{- else if and (gt $equivalentNE ($value.minScale | int) ) (le $equivalentNE ($value.maxScale| int)) }}
      {{- $already := false -}}
      {{- $equivalentNE := default 15000 ((default dict $root.global.scale).equivalentNE) -}}
      {{- if eq "lite" ($root.build.app.producttype | toString) -}}
        {{$equivalentNE = 3000}}
      {{- end -}}
      {{$instanceName := $key}}
      {{- if $instanceRoot -}}
        {{- if $instanceName -}}
          {{- if (index $instanceRoot $instanceName) -}}
            {{- if (index $instanceRoot $instanceName "spec") -}}
              {{- range (index $instanceRoot $instanceName "spec") -}}
                {{- if not $already -}}
                  {{- if le ($equivalentNE| int) (.scale| int) -}}
                    {{- $already = true -}}
                    {{- range $storage := .storages}}
                      {{- $totalStorage := set $totalStorage $key .size -}}
                    {{- end -}}
                  {{- end -}}
                {{- end -}}
              {{- end -}}
            {{- end -}}
          {{- end -}}
        {{- end -}}
      {{- end -}}
    {{- end -}}
  {{- end -}}
{{- end -}}

{{$databaseRoot := $root.cloudsop.default.zenith.database}}
{{- if eq "std" (.Values.build.app.producttype | toString) -}}
  {{- if hasPrefix "mae" (lower (.Values.global.scene | toString)) -}}
    {{$databaseRoot = (index $root.cloudsop "mae").zenith.database}}
  {{- else if hasPrefix "qiankunop" (lower (.Values.global.scene | toString)) -}}
    {{$databaseRoot = (index $root.cloudsop "qiankunop").zenith.database}}
  {{- else if hasPrefix "ccae" (lower (.Values.global.scene | toString)) -}}
    {{$databaseRoot = (index $root.cloudsop "ccae").zenith.database}}
  {{- end -}}
{{- else if eq "lite" (.Values.build.app.producttype | toString) -}}
  {{$databaseRoot = $root.cloudsoplite.default.zenith.database}}
{{- end -}}
{{range $key, $value := $databaseRoot }}
{{- if ne $key "default" }}
{{ if eq $key "omcdb" -}}
  {{- $already := false -}}
  {{- if $root.global.cloudsop -}}
    {{- if $root.global.cloudsop.mae -}}
      {{- if $root.global.cloudsop.mae.zenith -}}
        {{- if $root.global.cloudsop.mae.zenith.database -}}
          {{- if $root.global.cloudsop.mae.zenith.database.omcdb -}}
            {{- if $root.global.cloudsop.mae.zenith.database.omcdb.maxStorage -}}
              {{- $already = true -}}
{{- $maxStorage := $root.global.cloudsop.mae.zenith.database.omcdb.maxStorage}}
{{ include "cloudsop.zenith.database.spec.maxStorage" (dict "context" $ "databaseRoot" $databaseRoot "databaseName" $key "maxStorage" (sub $maxStorage 12288)) }}
{{- $instanceName := (include "cloudsop.database.instanceName" (dict "context" $ "databaseRoot" $databaseRoot "databaseName" $key)) -}}
{{- $currentStorage := get $totalStorage $instanceName -}}
{{- if not (hasKey $totalStorage $instanceName) -}}
{{- $totalStorage := set $totalStorage $instanceName $maxStorage -}}
{{- else -}}
{{- $sum := (add ($currentStorage | int) ($maxStorage |int) ("1" |int)) }}
{{- $totalStorage := set $totalStorage $instanceName $sum }}
{{- end }}
---
            {{- end -}}
          {{- end -}}
        {{- end -}}
      {{- end -}}
    {{- end -}}
  {{- end -}}
  {{- if (not $already) -}}
{{ include "cloudsop.zenith.database.spec" (dict "context" $ "databaseRoot" $databaseRoot "databaseName" $key) }}
{{- $instanceName := (include "cloudsop.database.instanceName" (dict "context" $ "databaseRoot" $databaseRoot "databaseName" $key)) -}}
{{- $currentStorage := get $totalStorage $instanceName -}}
{{- $maxStorage := (include "cloudsop.database.maxStorage" (dict "context" $ "databaseRoot" $databaseRoot "databaseName" $key))}}
{{- if not (hasKey $totalStorage $instanceName) -}}
{{- $totalStorage := set $totalStorage $instanceName $maxStorage -}}
{{- else -}}
{{- $sum := (add ($currentStorage | int) ($maxStorage |int) ("1" |int))}}
{{- $totalStorage := set $totalStorage $instanceName $sum }}
{{- end }}
---
  {{- end -}}
{{- else -}}
  {{$already := false}}
  {{- if (index $databaseRoot $key "feaSpec")}}
    {{$feaSpec := (index $databaseRoot $key "feaSpec")}}
      {{- range $i,$file := $feaSpec -}}
        {{- if eq (include "cloudsop.database.feaSpec.feature" (dict "Values" $root "featureList" (index $file "feature"))) "true" }}
        {{$already = true}}
{{- $namespace := $root.global.namespace -}}
apiVersion: resource.sop.huawei.com/v1
kind: ZenithDatabase
metadata:
  name: zenith.{{ $key }}.database
  namespace: {{ $namespace }}
spec:
  name: {{ $key }}
  instanceMetaName: {{ include "zenith.workload.instance" (dict "global" $root.global "instanceName" .instanceName "databaseName" $key) }}
  type: {{default "Bitmap" .type}}
  nologging: {{default "\"Off\"" (quote .nologging)}}
  nobackup: {{default "\"Off\"" (quote .nobackup)}}
  dataFiles:
  {{- range $datafile := .dataFiles}}
    - type: {{ .type }}
      maxStorage: {{ .maxStorage }}
      autoExtend: {{ default "\"On\"" .autoExtend}}
      minStorage: {{ default 8 .minStorage}}
      extStorage: {{ default 128 .extStorage}}
  {{- end}}
{{- $instanceName := (include "cloudsop.database.instanceName.feaSpec" (dict "context" $ "databaseRoot" $databaseRoot "databaseName" $key)) -}}
{{- $currentStorage := get $totalStorage $instanceName -}}
{{- $maxStorage := (include "cloudsop.database.maxStorage.feaSpec" (dict "context" $ "databaseRoot" $databaseRoot "databaseName" $key))}}
{{- if not (hasKey $totalStorage $instanceName) -}}
{{- $totalStorage := set $totalStorage $instanceName $maxStorage -}}
{{- else -}}
{{- $sum := (add ($currentStorage | int) ($maxStorage |int) ("1" |int))}}
{{- $totalStorage := set $totalStorage $instanceName $sum }}
{{- end }}
---
        {{- end}}
      {{- end}}
{{- end -}}
{{- if eq $already false}}
  {{- if (index $databaseRoot $key "feature") -}}
    {{ if eq (include "cloudsop.database.features" (dict "Values" $root "featureList" (index $databaseRoot $key "feature"))) "true" }}
{{ include "cloudsop.zenith.database.spec" (dict "context" $ "databaseRoot" $databaseRoot "databaseName" $key) }}
{{- $instanceName := (include "cloudsop.database.instanceName" (dict "context" $ "databaseRoot" $databaseRoot "databaseName" $key)) -}}
{{- $currentStorage := get $totalStorage $instanceName -}}
{{- $maxStorage := (include "cloudsop.database.maxStorage" (dict "context" $ "databaseRoot" $databaseRoot "databaseName" $key))}}
{{- if not (hasKey $totalStorage $instanceName) -}}
{{- $totalStorage := set $totalStorage $instanceName $maxStorage -}}
{{- else -}}
{{- $sum := (add ($currentStorage | int) ($maxStorage |int) ("1" |int))}}
{{- $totalStorage := set $totalStorage $instanceName $sum }}
{{- end }}
---
    {{- end -}}
  {{- else -}}
{{ include "cloudsop.zenith.database.spec" (dict "context" $ "databaseRoot" $databaseRoot "databaseName" $key) }}
{{- $instanceName := (include "cloudsop.database.instanceName" (dict "context" $ "databaseRoot" $databaseRoot "databaseName" $key)) -}}
{{- $currentStorage := get $totalStorage $instanceName -}}
{{- $maxStorage := (include "cloudsop.database.maxStorage" (dict "context" $ "databaseRoot" $databaseRoot "databaseName" $key))}}
{{- if not (hasKey $totalStorage $instanceName) -}}
{{- $totalStorage := set $totalStorage $instanceName $maxStorage -}}
{{- else -}}
{{- $sum := (add ($currentStorage | int) (atoi $maxStorage))}}
{{- $totalStorage := set $totalStorage $instanceName $sum }}
{{- end }}
---
  {{ end }}
  {{- end -}}
{{- end -}}
{{- end}}
{{- end -}}


{{$root := .Values}}
{{$equivalentNE := default 15000 ((default dict .Values.global.scale).equivalentNE) | int -}}
{{$instanceRoot := $root.cloudsop.default.zenith.instance}}
{{- if eq "std" (.Values.build.app.producttype | toString) -}}
  {{- if hasPrefix "mae" (lower (.Values.global.scene | toString)) -}}
        {{$instanceRoot = (index $root.cloudsop "mae").zenith.instance}}
  {{- else if hasPrefix "qiankunop" (lower (.Values.global.scene | toString)) -}}
        {{$instanceRoot = (index $root.cloudsop "qiankunop").zenith.instance}}
  {{- else if hasPrefix "ccae" (lower (.Values.global.scene | toString)) -}}
    {{$instanceRoot = (index $root.cloudsop "ccae").zenith.instance}}
  {{- end -}}
  {{- else if eq "lite" (.Values.build.app.producttype | toString) -}}
        {{$instanceRoot = $root.cloudsoplite.default.zenith.instance}}
        {{$equivalentNE = 3000}}
{{- end -}}
{{range $key, $value := $instanceRoot }}
  {{- if and (ne $key "default") (ne $key "customer") }}
  {{- if (index $instanceRoot $key "feaSpec")}}
        {{$feaSpec := (index $instanceRoot $key "feaSpec")}}
        {{- range $i, $file := $feaSpec -}}
          {{- if eq (include "cloudsop.database.feaSpec.feature" (dict "Values" $root "featureList" (index $file "feature"))) "true" }}
apiVersion: resource.sop.huawei.com/v1
kind: ZenithInstance
metadata:
  name: zenith.{{ $key }}.instance
  namespace: {{ $root.global.namespace }}
  annotations:
    sop.deploy/install-check: "ready"
  labels:
    backup.sop.huawei.com/resource.group.db: cloudsop
spec:
  name: {{ $key }}
  {{- include "default.zenith.nodeTemplate" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key) | indent 2 }}
  replicas: {{- include "cloudsop.zenith.instance.feaSpec.replicas" (dict "Values" $root "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
  logicZone: {{ $root.global.customLogicZoneSet.Svc_D_Zone }}
  engineVersion: {{- include "cloudsop.zenith.instance.engineVersion" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
  maxConnections: {{- include "cloudsop.zenith.instance.feaSpec.maxConnections" (dict "Values" $root "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
  replicaLocalMode: {{- include "cloudsop.zenith.instance.replicaLocalMode" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
  replicaGuardMode: {{- include "cloudsop.zenith.instance.replicaGuardMode" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
  paramTemplates:
    - name: zenith.cloudsop.default.conf
    - name: {{- include "cloudsop.zenith.instance.feaSpec.paramTemplate" (dict "Values" $root "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
  sqlTemplates:
    - name: zenith.cloudsop.sqlmap.conf
  systemTableSpaces:
    {{- include "cloudsop.zenith.instance.feaSpec.systemTableSpaces" (dict "Values" $root "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
{{ include "cloudsop.zenith.instance.feaSpec.maxStorage" (dict "Values" $root "context" $ "instanceRoot" $instanceRoot "instanceName" $key "maxStorage" (get $totalStorage $key)) | indent 2 }}
          {{- end}}
        {{- end}}
  {{- else if and (gt $equivalentNE ($value.minScale | int) ) (le $equivalentNE ($value.maxScale| int))}}
apiVersion: resource.sop.huawei.com/v1
kind: ZenithInstance
metadata:
  name: zenith.{{ $key }}.instance
  namespace: {{ $root.global.namespace }}
  annotations:
    sop.deploy/install-check: "ready"
  labels:
    backup.sop.huawei.com/resource.group.db: cloudsop
spec:
  name: {{ $key }}
  {{- include "default.zenith.nodeTemplate" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key) | indent 2 }}
  replicas: {{- include "cloudsop.zenith.instance.replicas" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
  logicZone: {{ $root.global.customLogicZoneSet.Svc_D_Zone }}
  engineVersion: {{- include "cloudsop.zenith.instance.engineVersion" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
  maxConnections: {{- include "cloudsop.zenith.instance.maxConnections" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
  replicaLocalMode: {{- include "cloudsop.zenith.instance.replicaLocalMode" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
  replicaGuardMode: {{- include "cloudsop.zenith.instance.replicaGuardMode" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
  paramTemplates:
    - name: zenith.cloudsop.default.conf
    - name: {{- include "cloudsop.zenith.instance.paramTemplate" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
  sqlTemplates:
    - name: zenith.cloudsop.sqlmap.conf
  systemTableSpaces:
    {{- include "cloudsop.zenith.instance.systemTableSpaces" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key) }}
{{ if eq $key "cloudsopdbsvr" -}}
  {{- $already := false -}}
  {{- if $root.global.cloudsop -}}
    {{- if $root.global.cloudsop.mae -}}
      {{- if $root.global.cloudsop.mae.zenith -}}
        {{- if $root.global.cloudsop.mae.zenith.database -}}
          {{- if $root.global.cloudsop.mae.zenith.database.omcdb -}}
            {{- if $root.global.cloudsop.mae.zenith.database.omcdb.maxStorage -}}
              {{- $already = true -}}
{{ include "cloudsop.zenith.instance.spec.maxStorage" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key "maxStorage" (get $totalStorage $key)) | indent 2 }}
            {{- end -}}
        {{- end -}}
      {{- end -}}
      {{- end -}}
    {{- end -}}
  {{- end -}}
  {{- if (not $already) -}}
{{ include "cloudsop.zenith.instance.spec.maxStorage" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key "maxStorage" (get $totalStorage $key)) | indent 2 }}
  {{- end -}}
{{- else -}}
{{ include "cloudsop.zenith.instance.spec.maxStorage" (dict "context" $ "instanceRoot" $instanceRoot "instanceName" $key "maxStorage" (get $totalStorage $key)) | indent 2 }}
{{- end -}}
{{- end}}
{{- end}}
---
{{- end -}}