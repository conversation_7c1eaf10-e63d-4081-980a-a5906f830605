build:
  app:
    producttype: std
cloudsop:
  default:
    zenith:
      database:
        adminhomedb:
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        aifmdb:
          feature:
          - global.features.cloudsop.aifm.aifmservice
          spec:
          - dataFiles:
            - maxStorage: 30720
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 30720
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 30720
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 30720
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 30720
              type: Normal
            instanceName: cloudsopfmdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 30720
              type: Normal
            instanceName: cloudsopfmdbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 30720
              type: Normal
            instanceName: cloudsopfmdbsvr
            scale: 300000
        aifmriskdb:
          feature:
          - global.features.cloudsop.aifm.aifmrisk
          spec:
          - dataFiles:
            - maxStorage: 2048
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 2048
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 9216
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 22528
              type: Normal
            instanceName: cloudsopfmdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 30720
              type: Normal
            instanceName: cloudsopfmdbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 30720
              type: Normal
            instanceName: cloudsopfmdbsvr
            scale: 300000
        aijobservicedb:
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 300000
        apicatalogmgrservicedb:
          feature:
          - global.features.iservice.snbi.apicatalog
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        apigatewayamdb:
          feature:
          - global.features.cloudsop.snbi.apigw
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 300000
        appenginedb:
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 300000
        appmgmtdb:
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 300000
        appmgmtnobackupdb:
          spec:
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            nobackup: 'On'
            scale: 3000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            nobackup: 'On'
            scale: 6000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            nobackup: 'On'
            scale: 15000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            nobackup: 'On'
            scale: 30000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            nobackup: 'On'
            scale: 80000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            nobackup: 'On'
            scale: 150000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            nobackup: 'On'
            scale: 300000
        apppublishservicedb:
          feature:
          - global.features.cloudsop.app.apppublish
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
        appsecuritydb:
          feature:
          - global.features.cloudsop.sm.appsecurity
          spec:
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 300000
        auditlogdb:
          feature:
          - global.features.cloudsop.sm.sm
          spec:
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 20480
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 230400
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 230400
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 300000
        autodiscoverydb:
          feature:
          - global.features.cloudsop.autodis
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 2048
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 10240
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 27306
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 51200
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 102400
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        cmccloudservicedb:
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        cmdbcorehistorydb:
          spec:
          - dataFiles:
            - maxStorage: 15360
              type: Normal
            instanceName: cloudsopdbsvr
            nobackup: 'On'
            scale: 3000
          - dataFiles:
            - maxStorage: 15360
              type: Normal
            instanceName: cloudsopdbsvr
            nobackup: 'On'
            scale: 6000
          - dataFiles:
            - maxStorage: 15360
              type: Normal
            instanceName: cloudsopdbsvr
            nobackup: 'On'
            scale: 15000
          - dataFiles:
            - maxStorage: 30720
              type: Normal
            instanceName: cloudsopdbsvr
            nobackup: 'On'
            scale: 30000
          - dataFiles:
            - maxStorage: 30720
              type: Normal
            instanceName: cloudsopodrsdbsvr
            nobackup: 'On'
            scale: 80000
          - dataFiles:
            - maxStorage: 30720
              type: Normal
            instanceName: cloudsopodrsdatastore
            nobackup: 'On'
            scale: 150000
          - dataFiles:
            - maxStorage: 360000
              type: Normal
            instanceName: cloudsopodrsdatastore
            scale: 300000
        cmdbcoreproxydb:
          spec:
          - dataFiles:
            - maxStorage: 4590
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 9180
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 22950
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 45900
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 122400
              type: Normal
            instanceName: cloudsopodrsdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 229500
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 150000
          - dataFiles:
            - maxStorage: 573440
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 300000
        cmdbcoresvrdb:
          spec:
          - dataFiles:
            - maxStorage: 28000
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 56000
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 140000
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 280000
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 746666
              type: Normal
            instanceName: cloudsopodrsdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 856000
              type: Normal
            instanceName: cloudsopodrsdatastore
            scale: 150000
          - dataFiles:
            - maxStorage: '1228000'
              type: Normal
            instanceName: cloudsopodrsdatastore
            scale: 300000
        cmdbcoresvrdb0:
          feaSpec:
          - dataFiles:
            - maxStorage: 10240
              type: Normal
            feature: global.features.cloudsop.odae.subdb
            instanceName: cloudsopodrsgraphdbsvr
          - dataFiles:
            - maxStorage: 102400
              type: Normal
            feature: global.features.cloudsop.odae.alonesubdb
            instanceName: cloudsopodrsdbsvr0
          feature:
          - global.features.cloudsop.odae.subdb
          - global.features.cloudsop.odae.alonesubdb
        datareplicationservicedb:
          feature:
          - global.features.cloudsop.odae.datareplication
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        dispatcherdb:
          spec:
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 300000
        dteeventcomputeservicedb:
          feature:
          - global.features.cloudsop.odae.eventcompute
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        eamdb:
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 2048
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 10240
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 27306
              type: Normal
            instanceName: cloudsopodrsdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 51200
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 150000
          - dataFiles:
            - maxStorage: 102400
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 300000
        evaluationdb:
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 300000
        febsdb:
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        fmdb:
          feature:
          - global.features.cloudsop.fmpub
          - global.features.cloudsop.fm
          spec:
          - dataFiles:
            - maxStorage: 13824
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 13824
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 31232
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 45568
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256512
              type: Normal
            instanceName: cloudsopfmdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256512
              type: Normal
            instanceName: cloudsopfmdbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 512000
              type: Normal
            instanceName: cloudsopfmdbsvr
            scale: 300000
        globalsearchservicedb:
          feature:
          - global.features.cloudsop.osearch.globalsearchwebsite
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        idgendb:
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 2048
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 10240
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 27306
              type: Normal
            instanceName: cloudsopodrsdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 51200
              type: Normal
            instanceName: cloudsopodrsdatastore
            scale: 150000
          - dataFiles:
            - maxStorage: 102400
              type: Normal
            instanceName: cloudsopodrsdatastore
            scale: 300000
        invhistorydb:
          feaSpec:
          - dataFiles:
            - maxStorage: 25600
              type: Normal
            feature: global.features.cloudsop.odae.subdb
            instanceName: cloudsopodrsgraphdbsvr
          - dataFiles:
            - maxStorage: 409600
              type: Normal
            feature: global.features.cloudsop.odae.alonesubdb
            instanceName: cloudsopodrsgraphdbsvr
          feature:
          - global.features.cloudsop.odae.subdb
          - global.features.cloudsop.odae.alonesubdb
          - global.features.cloudsop.odrs.history
          spec:
          - dataFiles:
            - maxStorage: 10240
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 10240
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 25600
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 51200
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 102400
              type: Normal
            instanceName: cloudsopodrsdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256000
              type: Normal
            instanceName: cloudsopodrsdatastore
            scale: 150000
        invmetadatadb:
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodrsdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 300000
        invmirrordb:
          feature:
          - global.features.cloudsop.odrs.mirror
          spec:
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopodrsdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopodrsdatastore
            scale: 150000
        iopstsadb:
          feature:
          - global.features.cloudsop.iops.iopstsa
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        licensedb:
          feature:
          - global.features.cloudsop.sm.sm
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 300000
        lifecycledb:
          feature:
          - global.features.cloudsop.snbi.driver
          - global.features.iservice.snbi.drivermgmt
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 300000
        litecasignservicedb:
          spec:
          - dataFiles:
            - maxStorage: 20480
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 20480
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 20480
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 20480
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 20480
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 20480
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 20480
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        logmgrservicedb:
          feature:
          - global.features.cloudsop.odae.log
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        meresgrpdb:
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 2730
              type: Normal
            instanceName: cloudsopodrsdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 2730
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 150000
          - dataFiles:
            - maxStorage: 10240
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 300000
        mouiservicedb:
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        msagentdb:
          spec:
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 300000
        nbicommondb:
          feature:
          - global.features.netgraph.snbi.legacynbi.proxy
          - global.features.cloudsop.snbi.legacynbi
          - global.features.iservice.snbi.snmp
          - global.features.iservice.snbi.tmf615
          spec:
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 300000
        nbidataservicedb:
          feature:
          - global.features.netgraph.snbi.data
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 300000
        nbifrmconfigservicedb:
          feature:
          - global.features.cloudsop.snbi.nbifrm
          - global.features.iservice.snbi.nbifrmnotify
          spec:
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 300000
        nbifrmftpdb:
          feature:
          - global.features.netgraph.snbi.nbifrm.ftp
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 300000
        networkprivdb:
          feature:
          - global.features.cloudsop.sm.privilege
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 300000
        nscfrtcatalogservicedb:
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodrsdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 150000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 300000
        objectgraphdb:
          feature:
          - global.features.cloudsop.odrs.objectGraph
          spec:
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopodrsdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopodrsdatastore
            scale: 150000
        odaeaccesspointservicedb:
          feature:
          - global.features.cloudsop.odae.core
          - global.features.cloudsop.odae.ap
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        odaeconfigmgrdb:
          feature:
          - global.features.cloudsop.odae.core
          - global.features.cloudsop.odae.conf
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        odaedatacatalogdb:
          spec:
          - dataFiles:
            - maxStorage: 1280
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 1280
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 1280
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 1280
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 1280
              type: Normal
            instanceName: cloudsopodrsdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 1280
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 150000
          - dataFiles:
            - maxStorage: 1280
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 300000
        odaedataexploreservicedb:
          feature:
          - global.features.cloudsop.odae.monitor
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        odaedataqualityservicedb:
          feature:
          - global.features.cloudsop.odae.dataquality
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        odaedumpservicedb:
          feature:
          - global.features.cloudsop.odae.datalifecycle
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        odaeolapagentservicedb:
          feature:
          - global.features.cloudsop.odae.driver
          - global.features.cloudsop.odae.olap
          spec:
          - dataFiles:
            - maxStorage: 2048
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 2048
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 2048
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 2048
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 4096
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 4096
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 4096
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        odaeolaploaderservicedb:
          feature:
          - global.features.cloudsop.odae.driver
          - global.features.cloudsop.odae.olap
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        odaepipelinemgrservicedb:
          feature:
          - global.features.cloudsop.odae.schedule
          spec:
          - dataFiles:
            - maxStorage: 2560
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 2560
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 2560
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 2560
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 2560
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 2560
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 2560
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        odaequeryenginedb:
          feature:
          - global.features.cloudsop.odae.core
          - global.features.cloudsop.odae.qe
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        odaesparkdispservicedb:
          feature:
          - global.features.cloudsop.odae.driver
          - global.features.cloudsop.odae.spark
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopodaedbsvr
            scale: 300000
        omcdb:
          feature:
          - global.features.cloudsop.imap.systemservice
          - global.features.cloudsop.imap.itmservice
          - global.features.cloudsop.imap.nhcservice
          spec:
          - dataFiles:
            - maxStorage: 200
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 200
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 200
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 200
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 200
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 200
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 200
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        privilegedb:
          feature:
          - global.features.cloudsop.sm.auth
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 300000
        rcaccessconfigdb:
          feature:
          - global.features.cloudsop.snbi.driver
          - global.features.iservice.snbi.drivermgmt
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopapidbsvr
            scale: 300000
        repomgrservicedb:
          feature:
          - global.features.cloudsop.repomgr
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        retrainmgmtdb:
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: softcomaidbsvr
            scale: 300000
        retrainservicedb:
          spec:
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: softcomaidbsvr
            scale: 300000
        rmcoordinatedb:
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 2048
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 10240
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 27306
              type: Normal
            instanceName: cloudsopodrsdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 51200
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 150000
          - dataFiles:
            - maxStorage: 201400
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 300000
        rmtaskmgmtdb:
          spec:
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodrsdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopodrsproxy
            scale: 300000
        rndb:
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        sampledb:
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: softcomaidbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: softcomaidbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: softcomaidbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: softcomaidbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: softcomaidbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: softcomaidbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: softcomaidbsvr
            scale: 300000
        secondaryauthdb:
          feature:
          - global.features.cloudsop.sm.auth
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 300000
        securityconfigdb:
          feature:
          - global.features.cloudsop.sm.sm
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 300000
        smartotextanalysisdb:
          feature:
          - global.features.cloudsop.iops.smartotextanalysis
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        snbcertmgmtservicedb:
          feature:
          - global.features.cloudsop.snbi.cert
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        sysfensdb:
          spec:
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        syslogdb:
          feature:
          - global.features.cloudsop.sm.sm
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 300000
        syspreferencesdb:
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        topodb:
          feature:
          - global.features.cloudsop.imap.systemservice
          - global.features.cloudsop.topo
          - global.features.cloudsop.imap.topoadpservice
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 2048
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 10240
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 27306
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 51200
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 102400
              type: Normal
            instanceName: cloudsopcommondbsvr
            scale: 300000
        userdb:
          feature:
          - global.features.cloudsop.sm.auth
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 6000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 15000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 30000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 80000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 150000
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopsmdbsvr
            scale: 300000
      instance:
        cloudsopdbsvr:
          maxScale: 60000
          minScale: 0
          spec:
          - paramTemplate: zenith.cloudsop.small.conf
            resource:
              limits:
                cpu: 3000m
                memory: 20480Mi
              requests:
                cpu: 200m
                memory: 2000Mi
            scale: 7000
            storages:
            - size: 96257
              type: Normal
            systemTableSpaces:
            - extStorage: 128
              maxStorage: 2048
              minStorage: 32
              name: default
            - extStorage: 128
              maxStorage: 2048
              minStorage: 128
              name: system
            - extStorage: 128
              maxStorage: 20480
              minStorage: 128
              name: sysaux
            - extStorage: 128
              maxStorage: 10240
              minStorage: 128
              name: temp
            - extStorage: 128
              maxStorage: 10240
              minStorage: 128
              name: undo
            - maxStorage: 10240
              minStorage: 512
              name: redolog
            - maxStorage: 30720
              name: archive
            - extStorage: 1
              maxStorage: 1
              minStorage: 1
              name: temp2
            - extStorage: 128
              maxStorage: 10240
              minStorage: 32
              name: temp2_undo
          - paramTemplate: zenith.cloudsop.large.conf
            resource:
              limits:
                cpu: 6000m
                memory: 9216Mi
              requests:
                cpu: 4000m
                memory: 9216Mi
            scale: 60000
            storages:
            - size: 96257
              type: Normal
            systemTableSpaces:
            - extStorage: 128
              maxStorage: 2048
              minStorage: 32
              name: default
            - extStorage: 128
              maxStorage: 2048
              minStorage: 128
              name: system
            - extStorage: 128
              maxStorage: 20480
              minStorage: 128
              name: sysaux
            - extStorage: 128
              maxStorage: 10240
              minStorage: 128
              name: temp
            - extStorage: 128
              maxStorage: 10240
              minStorage: 128
              name: undo
            - maxStorage: 10240
              minStorage: 512
              name: redolog
            - maxStorage: 30720
              name: archive
            - extStorage: 1
              maxStorage: 1
              minStorage: 1
              name: temp2
            - extStorage: 128
              maxStorage: 10240
              minStorage: 32
              name: temp2_undo
        cloudsopsmdbsvr:
          maxScale: 60000
          minScale: 0
          spec:
          - paramTemplate: zenith.cloudsop.sm.tiny2.conf
            resource:
              limits:
                cpu: 1500m
                memory: 3072Mi
              requests:
                cpu: 1000m
                memory: 2048Mi
            scale: 3000
            storages:
            - size: 32769
              type: Normal
            systemTableSpaces:
            - extStorage: 128
              maxStorage: 2048
              minStorage: 32
              name: default
            - extStorage: 128
              maxStorage: 2048
              minStorage: 128
              name: system
            - extStorage: 128
              maxStorage: 10240
              minStorage: 128
              name: sysaux
            - extStorage: 128
              maxStorage: 2048
              minStorage: 128
              name: temp
            - extStorage: 128
              maxStorage: 2048
              minStorage: 128
              name: undo
            - maxStorage: 2048
              minStorage: 512
              name: redolog
            - maxStorage: 10240
              name: archive
            - extStorage: 1
              maxStorage: 1
              minStorage: 1
              name: temp2
            - extStorage: 128
              maxStorage: 2048
              minStorage: 32
              name: temp2_undo
          - paramTemplate: zenith.cloudsop.sm.medium.conf
            resource:
              limits:
                cpu: 1500m
                memory: 6144Mi
              requests:
                cpu: 1000m
                memory: 6144Mi
            scale: 7000
            storages:
            - size: 32769
              type: Normal
            systemTableSpaces:
            - extStorage: 128
              maxStorage: 2048
              minStorage: 32
              name: default
            - extStorage: 128
              maxStorage: 2048
              minStorage: 128
              name: system
            - extStorage: 128
              maxStorage: 10240
              minStorage: 128
              name: sysaux
            - extStorage: 128
              maxStorage: 2048
              minStorage: 128
              name: temp
            - extStorage: 128
              maxStorage: 2048
              minStorage: 128
              name: undo
            - maxStorage: 2048
              minStorage: 512
              name: redolog
            - maxStorage: 10240
              name: archive
            - extStorage: 1
              maxStorage: 1
              minStorage: 1
              name: temp2
            - extStorage: 128
              maxStorage: 2048
              minStorage: 32
              name: temp2_undo
          - paramTemplate: zenith.cloudsop.sm.medium.conf
            resource:
              limits:
                cpu: 3000m
                memory: 6144Mi
              requests:
                cpu: 2000m
                memory: 6144Mi
            scale: 60000
            storages:
            - size: 32769
              type: Normal
            systemTableSpaces:
            - extStorage: 128
              maxStorage: 2048
              minStorage: 32
              name: default
            - extStorage: 128
              maxStorage: 2048
              minStorage: 128
              name: system
            - extStorage: 128
              maxStorage: 10240
              minStorage: 128
              name: sysaux
            - extStorage: 128
              maxStorage: 2048
              minStorage: 128
              name: temp
            - extStorage: 128
              maxStorage: 2048
              minStorage: 128
              name: undo
            - maxStorage: 2048
              minStorage: 512
              name: redolog
            - maxStorage: 10240
              name: archive
            - extStorage: 1
              maxStorage: 1
              minStorage: 1
              name: temp2
            - extStorage: 128
              maxStorage: 2048
              minStorage: 32
              name: temp2_undo
        default:
          engineVersion: {{zenith_version}}
          nodeSelector: node.adn.huawei.com/db
          replicaGuardMode: MaximizeAvailability
          replicaLocalMode: MaximizeAvailability
          spec:
          - maxConnections: 900
            replicas: 2
            scale: 7000
          - maxConnections: 900
            replicas: 2
            scale: 60000
cloudsoplite:
  default:
    zenith:
      database:
        adminhomedb:
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        aifmdb:
          feature:
          - global.features.cloudsop.aifm.aifmservice
          spec:
          - dataFiles:
            - maxStorage: 30720
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        aifmriskdb:
          feature:
          - global.features.cloudsop.aifm.aifmrisk
          spec:
          - dataFiles:
            - maxStorage: 2048
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        apicatalogmgrservicedb:
          feature:
          - global.features.iservice.snbi.apicatalog
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        apigatewayamdb:
          feature:
          - global.features.cloudsop.snbi.apigw
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        apppublishservicedb:
          feature:
          - global.features.cloudsop.app.apppublish
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        auditlogdb:
          feature:
          - global.features.cloudsop.sm.minsm
          spec:
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        autodiscoverydb:
          feature:
          - global.features.cloudsop.autodis
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        cmdbcorehistorydb:
          spec:
          - dataFiles:
            - maxStorage: 15360
              type: Normal
            instanceName: cloudsopdbsvr
            nobackup: 'On'
            scale: 3000
        cmdbcoreproxydb:
          spec:
          - dataFiles:
            - maxStorage: 4590
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        cmdbcoresvrdb:
          spec:
          - dataFiles:
            - maxStorage: 28000
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        datareplicationservicedb:
          feature:
          - global.features.cloudsop.odae.datareplication
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        dteeventcomputeservicedb:
          feature:
          - global.features.cloudsop.odae.eventcompute
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        eamdb:
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        febsdb:
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        fmdb:
          feature:
          - global.features.cloudsop.fmpub
          - global.features.cloudsop.fm
          spec:
          - dataFiles:
            - maxStorage: 13824
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        globalsearchservicedb:
          feature:
          - global.features.cloudsop.osearch.globalsearchwebsite
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        idgendb:
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        invhistorydb:
          feature:
          - global.features.cloudsop.odrs.history
          spec:
          - dataFiles:
            - maxStorage: 10240
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        invmetadatadb:
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        invmirrordb:
          feature:
          - global.features.cloudsop.odrs.mirror
          spec:
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        iopstsadb:
          feature:
          - global.features.cloudsop.iops.iopstsa
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        licensedb:
          feature:
          - global.features.cloudsop.sm.minsm
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        lifecycledb:
          feature:
          - global.features.cloudsop.snbi.driver
          - global.features.iservice.snbi.drivermgmt
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        logmgrservicedb:
          feature:
          - global.features.cloudsop.odae.log
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        meresgrpdb:
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        mouiservicedb:
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        nbicommondb:
          feature:
          - global.features.netgraph.snbi.legacynbi.proxy
          - global.features.cloudsop.snbi.legacynbi
          - global.features.iservice.snbi.snmp
          - global.features.iservice.snbi.tmf615
          spec:
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        nbidataservicedb:
          feature:
          - global.features.netgraph.snbi.data
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        nbifrmconfigservicedb:
          feature:
          - global.features.cloudsop.snbi.nbifrm
          - global.features.iservice.snbi.nbifrmnotify
          spec:
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        nbifrmftpdb:
          feature:
          - global.features.netgraph.snbi.nbifrm.ftp
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        networkprivdb:
          feature:
          - global.features.cloudsop.sm.minsm
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        nscfrtcatalogservicedb:
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        objectgraphdb:
          feature:
          - global.features.cloudsop.odrs.objectGraph
          spec:
          - dataFiles:
            - maxStorage: 5120
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        odaeconfigmgrdb:
          feature:
          - global.features.cloudsop.odae.core
          - global.features.cloudsop.odae.conf
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        odaedatacatalogdb:
          spec:
          - dataFiles:
            - maxStorage: 1280
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        odaedataexploreservicedb:
          feature:
          - global.features.cloudsop.odae.monitor
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        odaedumpservicedb:
          feature:
          - global.features.cloudsop.odae.datalifecycle
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        odaepipelinemgrservicedb:
          feature:
          - global.features.cloudsop.odae.schedule
          spec:
          - dataFiles:
            - maxStorage: 2560
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        odaequeryenginedb:
          feature:
          - global.features.cloudsop.odae.core
          - global.features.cloudsop.odae.qe
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        odaerdbplugindb:
          spec:
          - dataFiles:
            - maxStorage: 20480
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        odaesparkdispservicedb:
          feature:
          - global.features.cloudsop.odae.driver
          - global.features.cloudsop.odae.spark
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        omcdb:
          feature:
          - global.features.cloudsop.imap.systemservice
          - global.features.cloudsop.imap.itmservice
          - global.features.cloudsop.imap.nhcservice
          spec:
          - dataFiles:
            - maxStorage: 200
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        privilegedb:
          feature:
          - global.features.cloudsop.sm.minsm
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        rcaccessconfigdb:
          feature:
          - global.features.cloudsop.snbi.driver
          - global.features.iservice.snbi.drivermgmt
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
        repomgrservicedb:
          feature:
          - global.features.cloudsop.repomgr
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        rmcoordinatedb:
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        rmtaskmgmtdb:
          spec:
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        rndb:
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        secondaryauthdb:
          feature:
          - global.features.cloudsop.sm.minsm
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        securityconfigdb:
          feature:
          - global.features.cloudsop.sm.minsm
          spec:
          - dataFiles:
            - maxStorage: 512
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        smartotextanalysisdb:
          feature:
          - global.features.cloudsop.iops.smartotextanalysis
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        snbcertmgmtservicedb:
          feature:
          - global.features.cloudsop.snbi.cert
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        sysfensdb:
          spec:
          - dataFiles:
            - maxStorage: 128
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        syslogdb:
          feature:
          - global.features.cloudsop.sm.minsm
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        syspreferencesdb:
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        topodb:
          feature:
          - global.features.cloudsop.imap.systemservice
          - global.features.cloudsop.topo
          - global.features.cloudsop.imap.topoadpservice
          spec:
          - dataFiles:
            - maxStorage: 1024
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
        userdb:
          feature:
          - global.features.cloudsop.sm.minsm
          spec:
          - dataFiles:
            - maxStorage: 256
              type: Normal
            instanceName: cloudsopdbsvr
            scale: 3000
      instance:
        cloudsopdbsvr:
          maxScale: 3000
          minScale: 0
          spec:
          - paramTemplate: zenith.cloudsop.small.conf
            resource:
              limits:
                cpu: 6000m
                memory: 6144Mi
              requests:
                cpu: 3000m
                memory: 3072Mi
            scale: 3000
            storages:
            - size: 61441
              type: Normal
            systemTableSpaces:
            - extStorage: 128
              maxStorage: 2048
              minStorage: 32
              name: default
            - extStorage: 128
              maxStorage: 2048
              minStorage: 128
              name: system
            - extStorage: 128
              maxStorage: 10240
              minStorage: 128
              name: sysaux
            - extStorage: 128
              maxStorage: 10240
              minStorage: 128
              name: temp
            - extStorage: 128
              maxStorage: 10240
              minStorage: 128
              name: undo
            - maxStorage: 6144
              minStorage: 512
              name: redolog
            - maxStorage: 10240
              name: archive
            - extStorage: 1
              maxStorage: 1
              minStorage: 1
              name: temp2
            - extStorage: 128
              maxStorage: 10240
              minStorage: 32
              name: temp2_undo
        default:
          engineVersion: {{zenith_version}}
          nodeSelector: node.adn.huawei.com/db
          replicaGuardMode: MaximizeAvailability
          replicaLocalMode: MaximizeAvailability
          spec:
          - maxConnections: 900
            replicas: 1
            scale: 3000
global:
  cloudsopdbsvr:
    storages:
      size: 350Gi
    paramTemplate: zenith.dv.cloudsop.large.conf
    archive: 51200
  customLogicZoneSet:
    Svc_D_Zone: Svc_D_Zone
  scale:
    equivalentNE: 6000
  namespace: sop
  nodePool: sop
  storageClass:
    default: any