#!/bin/bash

BASE_DIR=$(cd $(dirname $0); pwd)
IS_DEPENDENCY_SYS="False"

function main()
{
    # 服务包如果区分系统架构，则构建出的镜像包需要带上系统架构类型，反之则统一使用noarch
    # 通过 IS_DEPENDENCY_SYS 变量来区分，默认为 false，表示不依赖系统架构，取值范围 [Yes, False]
    if [ "x${IS_DEPENDENCY_SYS}" == "xYes" ];then
        if [ "x${os_tag}" == "x_aarch64" ];then
            sys_tag="aarch64"
        else
            sys_tag="x86_64"
        fi
    else
        sys_tag="noarch"
    fi

    # 打包pub目录
    cd ${BASE_DIR}/../../../deployment/src/main/release
    tar --format=gnu -cvf package.tar pub manifest.txt
    chmod 644 package.tar
    cp -rpf package.tar ${BASE_DIR}

    # 获取服务的版本
    service_version=$(cat ${BASE_DIR}/../../../target/checkout/deployment/src/main/release/pub/release_version.properties |grep "release_version" |awk -F "=" '{print $2}')

    # 构建新镜像
    local image_repository_name=$(echo "${SERVICE_NAME}" |awk '{print tolower($0)}')
    cd ${BASE_DIR}
    docker build -t ${image_repository_name}_pub-${sys_tag}:${service_version} .

    # 保存镜像
	docker save ${image_repository_name}_pub-${sys_tag}:${service_version} > ${BASE_DIR}/${image_repository_name}_pub-${service_version}-${sys_tag}.tar.gz

    # 拷贝包至共享目录
    [ ! -d "${WORKSPACE}/output/image-pkgs/${SERVICE_NAME}" ] && mkdir -p ${WORKSPACE}/output/image-pkgs/${SERVICE_NAME}
    cp -rf ${BASE_DIR}/${image_repository_name}_pub-${service_version}-${sys_tag}.tar.gz ${WORKSPACE}/output/image-pkgs/${SERVICE_NAME}
}

main $@
exit $?