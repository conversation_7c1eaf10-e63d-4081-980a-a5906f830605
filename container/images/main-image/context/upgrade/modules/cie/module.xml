<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<module installSubDir="cie" ne="singlehost,primary,extendhost,extendprimary">
    <preClean>
        <file path="cie/dmu/dhcp/lib/"/>
        <file path="cie/dmu/lib/"/>
        <file path="cie/dmu/tools/dtools/enc_tool/"/>
        <file path="cie/dmu/rpackage/"/>
        <file path="cie/dmu/repository/dmu/"/>
        <file path="cie/dmu/share/linux-addon/enc_mgr/enc_tool/*.jar"/>
        <file path="cie/dmu/bin/deal_range.sh"/>
        <file path="cie/dmu/dhcp/bin/dhcpd.sh"/>
        <file path="cie/dmu/dhcp/bin/setupClasspath.sh"/>
        <file path="cie/dmu/dhcp/bin/start.sh"/>
        <file path="cie/dmu/modules/deploy/sudo_integrity_check.sh"/>
        <file path="cie/dmu/sudoScripts/solution/pkg/make_software.sh"/>
        <file path="cie/dmu/sudoScripts/solution/pkg/ospatchinstall/update_patch.sh"/>
        <file path="cie/dmu/share/collect/CIE/getomulog.sh"/>
        <file path="cie/dmu/share/collect/collect.sh"/>
        <file path="cie/dmu/repository/c3/com.huawei.c3.naming.sdk-3.2.2-SNAPSHOT.jar"/>
        <file path="cie/dmu/log/debug/dNaming.log"/>
        <file path="cie/dmu/repository/ext/org.eclipse.virgo.jetty.web-3.0.3.RELEASE.plan"/>
        <file path="cie/dmu/repository/dmu/com.huawei.cde.dmu.commons.plan"/>
        <file path="cie/dmu/repository/thirdparty/com.springsource.org.objectweb.asm-2.2.3.jar"/>
        <file path="cie/dmu/repository/thirdparty/commons-codec-1.3.jar"/>
        <file path="cie/dmu/lib/kernel/commons-codec-1.3.jar"/>
        <file path="cie/dmu/repository/ext/commons-codec-1.3.jar"/>
        <file path="cie/dmu/repository/thirdparty/com.springsource.org.xmlpull-1.1.4.c.jar"/>
        <file path="cie/dmu/repository/thirdparty/commons-jxpath-1.3.jar"/>
        <file path="cie/dmu/repository/thirdparty/commons-io-2.4.jar"/>
        <file path="cie/dmu/dhcp/lib/commons-io-2.4.jar"/>
        <file path="cie/dmu/repository/ext/commons-io-2.4.jar"/>
        <file path="cie/dmu/repository/thirdparty/netty-buffer-4.0.27.Final.jar"/>
        <file path="cie/dmu/repository/thirdparty/netty-common-4.0.27.Final.jar"/>
        <file path="cie/dmu/repository/thirdparty/netty-transport-4.0.27.Final.jar"/>
        <file path="cie/dmu/dhcp/lib/hawtbuf-1.9.jar"/>
        <file path="cie/dmu/dhcp/lib/activemq-client-5.9.0.jar"/>
        <file path="cie/dmu/dhcp/lib/activemq-client-5.15.6.jar"/>
        <file path="cie/dmu/dhcp/lib/commons-logging-1.2.jar"/>
        <file path="cie/dmu/repository/thirdparty/commons-logging-1.2.jar"/>
        <file path="cie/dmu/dhcp/lib/commons-lang3-3.7.jar"/>
        <file path="cie/dmu/dhcp/lib/jackson-*-2.9.10.jar"/>
        <file path="cie/dmu/share/linux-addon/enc_mgr/enc_tool/log4j-*-2.11.2.jar"/>
        <file path="cie/dmu/tools/dtools/enc_tool/log4j-*-2.11.2.jar"/>
        <file path="cie/dmu/share/linux-addon/enc_mgr/enc_tool/commons-collections4-4.2.jar"/>
        <file path="cie/dmu/tools/dtools/enc_tool/commons-collections4-4.2.jar"/>
        <file path="cie/dmu/repository/ext/commons-codec-1.11.jar"/>
        <file path="cie/dmu/repository/ext/javax.annotation-api-1.3.jar"/>
        <file path="cie/dmu/tools/commons/lib/quartz-2.3.0.jar"/>
        <file path="cie/dmu/tools/dtools/enc_tool/log4j-1.2.17.jar"/>
        <file path="cie/dmu/share/linux-addon/enc_mgr/enc_tool/log4j-1.2.17.jar"/>
        <file path="cie/dmu/config/org.eclipse.virgo.*.properties"/>
        <file path="cie/dmu/config/org.eclipse.virgo.kernel.authentication.config"/>
        <file path="cie/dmu/config/osgi.console.*.properties"/>
        <file path="cie/dmu/config/serviceability.xml"/>
        <file path="cie/dmu/dhcp/lib/org.springframework.*-3.0.5.RELEASE.jar"/>
        <file path="cie/dmu/lib/kernel"/>
        <file path="cie/dmu/lib/com.springsource.javax.transaction-1.1.0.jar"/>
        <file path="cie/dmu/lib/com.springsource.org.apache.mina.core-2.0.2.jar"/>
        <file path="cie/dmu/lib/com.springsource.org.apache.sshd.core-0.5.0.jar"/>
        <file path="cie/dmu/lib/com.springsource.slf4j.api-1.6.1.jar"/>
        <file path="cie/dmu/lib/java6-server.profile"/>
        <file path="cie/dmu/lib/org.apache.felix.gogo.runtime-0.8.0.v201105062003.jar"/>
        <file path="cie/dmu/lib/org.eclipse.equinox.cm-1.0.300.v20101204.jar"/>
        <file path="cie/dmu/lib/org.eclipse.equinox.console.supportability-1.0.0.201108021516.jar"/>
        <file path="cie/dmu/lib/org.eclipse.osgi.services-3.3.0.v20110110.jar"/>
        <file path="cie/dmu/lib/org.eclipse.osgi-3.7.0.v20110613.jar"/>
        <file path="cie/dmu/lib/org.eclipse.virgo.kernel.authentication-3.0.3.RELEASE.jar"/>
        <file path="cie/dmu/lib/org.eclipse.virgo.kernel.launch.properties"/>
        <file path="cie/dmu/lib/org.eclipse.virgo.kernel.shutdown-3.0.3.RELEASE.jar"/>
        <file path="cie/dmu/lib/org.eclipse.virgo.osgi.console-3.0.3.RELEASE.jar"/>
        <file path="cie/dmu/lib/org.eclipse.virgo.osgi.extensions.equinox-3.0.3.RELEASE.jar"/>
        <file path="cie/dmu/lib/org.eclipse.virgo.osgi.launcher-3.0.3.RELEASE.jar"/>
        <file path="cie/dmu/repository/ext/org.springframework.*-3.0.5.RELEASE.jar"/>
        <file path="cie/dmu/repository/ext/org.eclipse.virgo.*-3.0.3.RELEASE.jar"/>
        <file path="cie/dmu/repository/ext/osgi.console.*.properties"/>
        <file path="cie/dmu/repository/ext/org.eclipse.virgo.kernel.userregion.springdm.plan"/>
        <file path="cie/dmu/repository/ext/com.springsource.slf4j.*-1.6.1.jar"/>
        <file path="cie/dmu/repository/ext/org.springframework.osgi.*-1.2.1.jar"/>
        <file path="cie/dmu/repository/ext/com.springsource.javax.ejb-3.0.0.jar"/>
        <file path="cie/dmu/repository/ext/com.springsource.javax.servlet-2.5.0.jar"/>
        <file path="cie/dmu/repository/ext/com.springsource.javax.xml.rpc-1.1.0.v20110517.jar"/>
        <file path="cie/dmu/repository/ext/com.springsource.org.aopalliance-1.0.0.jar"/>
        <file path="cie/dmu/repository/ext/com.sun.el-1.0.0.v201105211818.jar"/>
        <file path="cie/dmu/repository/ext/javax.el-2.1.0.v201105211819.jar"/>
        <file path="cie/dmu/repository/ext/org.apache.jasper.glassfish-2.1.0.v201110031002.jar"/>
        <file path="cie/dmu/repository/ext/org.eclipse.equinox.ds-1.3.0.v20110124-0830.jar"/>
        <file path="cie/dmu/repository/ext/org.eclipse.equinox.event-1.2.100.v20110110.jar"/>
        <file path="cie/dmu/repository/ext/org.eclipse.equinox.util-1.0.200.v20100503.jar"/>
        <file path="cie/dmu/repository/ext/org.eclipse.osgi.services-3.3.0.v20110110.jar"/>
        <file path="cie/dmu/p2"/>
        <file path="cie/dmu/pickup"/>
        <file path="cie/dmu/plugins"/>
        <file path="cie/dmu/bin/bundle.sh"/>
        <file path="cie/dmu/bin/checkJava.sh"/>
        <file path="cie/dmu/bin/dmk.sh"/>
        <file path="cie/dmu/bin/jconsole.sh"/>
        <file path="cie/dmu/bin/plan.sh"/>
        <file path="cie/dmu/bin/setupClasspath.sh"/>
        <file path="cie/dmu/bin/shutdown.sh"/>
        <file path="cie/dmu/config/dmu"/>
        <file path="cie/dmu/config/cie-logback.xml"/>
        <file path="cie/dmu/configuration"/>
        <file path="cie/dmu/jetty"/>
        <file path="cie/dmu/lib"/>
        <file path="cie/dmu/repository/dopra"/>
        <file path="cie/dmu/repository/ext"/>
        <file path="cie/dmu/repository/oms"/>
        <file path="cie/dmu/repository/usr"/>
        <file path="cie/dmu/repository/dmu/com.huawei.cie.bundle.controller-8.102.000000-SNAPSHOT.jar"/>
        <file path="cie/dmu/repository/dmu/com.huawei.cie.dmu.virgo.server-8.102.000000-SNAPSHOT.jar"/>
        <file path="cie/dmu/repository/dmu/com.huawei.cie.dbcp.proton.fragment-8.102.000000-SNAPSHOT.jar"/>
        <file path="cie/dmu/repository/dmu/com.huawei.cie.log.config-8.102.000000-SNAPSHOT.jar"/>
        <file path="cie/dmu/repository/dmu/snmp4j-2.6.2.fixed.jar"/>
        <file path="cie/dmu/repository/thirdparty/antlr-2.7.7.fixed.jar"/>
        <file path="cie/dmu/repository/thirdparty/dom4j-2.1.1.fixed.jar"/>
        <file path="cie/dmu/repository/thirdparty/ojdbc8-********.fixed.jar"/>
        <file path="cie/dmu/repository/thirdparty/quartz-2.3.2.fixed.jar"/>
        <file path="cie/dmu/repository/thirdparty/sblim-cim-client2-2.1.1.fixed.jar"/>
        <file path="cie/dmu/repository/thirdparty/spring-orm-5.1.13.RELEASE.fixed.jar"/>
        <file path="cie/dmu/repository/thirdparty/netty-*-4.1.47.Final.jar"/>
        <file path="cie/modules/scripts/check_ssh_connect_v1.sh"/>
        <file path="cie/dmu/share/linux-addon/enc_mgr/enc_tool/log4j-*-2.14.1.jar"/>
        <file path="cie/dmu/repository/thirdparty/log4j-*-2.14.1.jar"/>
        <file path="cie/dmu/tools/dtools/enc_tool/log4j-*-2.14.1.jar"/>

        <file path="cie/mq/bin/"/>
        <file path="cie/mq/jre/"/>
        <file path="cie/mq/lib/"/>

    </preClean>

    <config>
		<file path="cie/dmu/repository/thirdparty/iControl-*.jar" operationType="intact"/>
		<file path="cie/dmu/repository/thirdparty/axis-*.jar" operationType="intact"/>
		<file path="cie/dmu/repository/thirdparty/commons-discovery-*.jar" operationType="intact"/>
		<file path="cie/dmu/repository/thirdparty/jaxrpc-api-*.jar" operationType="intact"/>
		<file path="cie/dmu/repository/thirdparty/wsdl4j-*.jar" operationType="intact"/>
		<file path="cie/dmu/repository/thirdparty/hessian-*.jar" operationType="intact"/>
        <file path="cie/dmu/config/jdbc.properties"  type="properties">
            <items>
                <item>jdbc.url</item>
                <item>jdbc.password</item>
            </items>
        </file>
        <file path="cie/dmu/config/snmp.properties" type="properties">
            <items>
                <item>snmp.support.versions</item>
                <item>snmp.request.source.ip.limit</item>
                <item>snmp.user2</item>
            </items>
        </file>
        <file path="cie/dmu/config/dmu.properties" operationType="intact"/>

        <file path="cie/mq/conf/mq.cfg" operationType="intact"/>
        <file path="cie/mq/conf/huaweiServer.ks" operationType="intact"/>
        <file path="cie/mq/conf/trust.store" operationType="intact"/>
        <file path="cie/mq/conf/credentials.properties" operationType="intact"/>
        <file path="cie/mq/conf/activemq.xml" operationType="intact"/>


    </config>
</module>
