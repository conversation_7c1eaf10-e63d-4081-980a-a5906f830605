{"$sopChartSchemaVersion": 2, "type": "object", "required": ["global"], "properties": {"global": {"$ref": "#/definitions/global"}}, "definitions": {"global": {"type": "object", "required": ["APIGatewayRegisterMode", "EMSMode", "ER_TOTAL_LIMIT_CONN", "IS_ENABLE_REVIEW_ABILITY", "Kafka_Retention_Time", "LIMIT_CONN_PERIP", "NFV_MODE", "SetingToolsMode", "dv_deploy_scale_size", "enableAccountNonRootMode", "enableNetworkElementLargeCapacity", "enableUOAConnection", "i2k_interface_password_secure_mode", "isLiteMonitor", "loadBalancingAndFaultTolerantMode", "serviceMessageBusRegisterMode", "ssoType", "use_forwarded_ip", "sm", "fm", "all", "topology_scale", "detailed_log_printing_mode", "approveMaxClientBodySize", "dv_lite_mode", "sandbox_escape", "assurance_escape", "pkgmgr_escape"], "properties": {"sm": {"$ref": "#/definitions/sm"}, "fm": {"$ref": "#/definitions/fm"}, "caas": {"type": "string", "visible": false, "readOnly": true}, "hofs": {"$ref": "#/definitions/hofs"}, "hofsClusterName": {"type": "string", "visible": false, "readOnly": true}, "zenith": {"$ref": "#/definitions/zenith"}, "redis": {"$ref": "#/definitions/redis"}, "cloudsop": {"$ref": "#/definitions/cloudsop"}, "repo": {"$ref": "#/definitions/repo"}, "deployMode": {"type": "string", "visible": false, "readOnly": true}, "namespace": {"type": "string", "visible": false, "readOnly": true}, "managerNamespace": {"type": "string", "visible": false, "readOnly": true}, "I2K_NODE_FLOAT_IP": {"type": "string", "visible": false, "readOnly": true}, "nodePool": {"type": "string", "visible": false, "readOnly": true}, "arch": {"type": "string", "visible": false, "readOnly": true}, "lang": {"type": "string", "visible": false, "readOnly": true}, "scale": {"$ref": "#/definitions/scale"}, "storageClass": {"$ref": "#/definitions/storageClass"}, "features": {"$ref": "#/definitions/features"}, "scene": {"type": "string", "visible": false, "readOnly": true}, "productName": {"type": "string", "visible": false, "readOnly": true}, "DV_IPS": {"type": "string", "visible": false, "readOnly": false}, "AIOpsMenuConfig": {"type": "string", "localeTitle": {"zh-CN": "AIOPS菜单配置", "en-US": "AIOPS Menu Configuration"}, "visible": true, "readOnly": false, "pattern": "^[a-zA-Z,]*$", "maxLength": 100, "localeDescription": {"zh-CN": "取值范围【faultAnalysis,capacityEvaluation,disasterRecovery,indicatorPrediction,tidalScheduling】。说明：faultAnalysis表示故障检测分析，capacityEvaluation表示智能容量评估，disasterRecovery表示智能容灾，indicatorPrediction表示指标趋势预测，tidalScheduling表示潮汐调度。智能容灾依赖故障检测分析能力，配置智能容灾后会默认启用故障检测分析。潮汐调度依赖指标趋势预测能力，配置潮汐调度后会默认启用指标趋势预测。", "en-US": "Value range is [faultAnalysis,capacityEvaluation,disasterRecovery,indicatorPrediction,tidalScheduling]. faultAnalysis indicates Fault Detection And Analysis, capacityEvaluation indicates Intelligent Capacity Evaluation, disasterRecovery indicates Intelligent Disaster Recovery, and indicatorPrediction indicates Indicator Trend Prediction, tidalScheduling indicates Tidal Scheduling. Intelligent Disaster Recovery depends on the Fault Detection And Analysis, After intelligent disaster recovery is configured, Fault Detection And Analysis is enabled by default.Tidal scheduling depends on the Indicator Trend Prediction, After tidal scheduling is configured, Indicator Trend Prediction is enabled by default."}}, "APIGatewayRegisterMode": {"type": "string", "localeTitle": {"zh-CN": "API网关注册模式", "en-US": "API gateway register mode"}, "localeDescription": {"zh-CN": "取值范围【synchrono,asynchrono】，默认synchrono。API网关注册模式，仅用于OMC场景。", "en-US": "The value range is [synchrono,asynchrono]. The default value is synchrono. API gateway register mode, which is only for OMC scenarios."}, "enum": ["synchrono", "asynchrono"], "visible": true, "readOnly": false}, "loadBalancingAndFaultTolerantMode": {"type": "string", "localeTitle": {"zh-CN": "负载均衡和容错模式", "en-US": "load balancing and fault tolerant mode"}, "localeDescription": {"zh-CN": "取值范围【random,inTurn】，默认inTurn。负载均衡和容错模式，仅用于OMC场景", "en-US": "The value range is [random,inTurn]. The default value is inTurn. Load balancing and fault tolerant mode, which is only for OMC scenarios."}, "enum": ["random", "inTurn"], "visible": true, "readOnly": false}, "ServiceTracingMemory": {"type": "integer", "localeTitle": {"zh-CN": "服务跟踪内存配置", "en-US": "service trace memory"}, "localeDescription": {"zh-CN": "服务跟踪内存配置。默认值300，单位M，仅用于OMC场景。", "en-US": "Service trace memory, which is only for OMC scenarios. The default value is 100, unit for M."}, "maximum": 2000, "minimum": 0, "visible": true, "readOnly": false}, "detailed_log_printing_mode": {"type": "string", "localeTitle": {"zh-CN": "日志详细打印模式", "en-US": "Detailed Log Printing Mode"}, "localeDescription": {"zh-CN": "取值范围【No, Yes】，默认No。如果要开启日志详细打印模式，需要选择Yes", "en-US": "The value range is [No, Yes] . The default value is No. To enable the detailed log printing mode, select Yes."}, "enum": ["No", "Yes"], "visible": true, "readOnly": false}, "dv_lite_mode": {"type": "string", "localeTitle": {"zh-CN": "轻量化部署标志参数", "en-US": "is use lite mode to deploy"}, "localeDescription": {"zh-CN": "取值范围【TRUE, FALSE】，默认FALSE。非轻量化场景需要保持FALSE", "en-US": "The value range is [TRUE, FALSE], The default value is FALSE. In non-light scenarios, set this parameter to FALSE."}, "enum": ["TRUE", "FALSE"], "visible": true, "readOnly": false}, "SetingToolsMode": {"type": "string", "localeTitle": {"zh-CN": "配置工具模式", "en-US": "setting tools mode"}, "localeDescription": {"zh-CN": "取值范围【synchrono,asynchrono】，默认synchrono。配置工具模式，仅用于OMC场景。", "en-US": "The value range is [synchrono,asynchrono]. The default value is synchrono. Setting tools mode, which is only for OMC scenarios."}, "enum": ["synchrono", "asynchrono"], "visible": true, "readOnly": false}, "messageDrivingServieMaxMemory": {"type": "integer", "localeTitle": {"zh-CN": "消息驱动服务内存配置", "en-US": "message driver servcie max memory"}, "localeDescription": {"zh-CN": "消息驱动服务内存配置。默认值100，单位M，仅用于OMC场景。", "en-US": "Message driver servcie max memory, which is only for OMC scenarios. The default value is 100, unit for M."}, "maximum": 2000, "minimum": 0, "visible": true, "readOnly": false}, "serviceMessageBusRegisterMode": {"type": "string", "localeTitle": {"zh-CN": "服务和消息总线注册模式", "en-US": "service and message bus register mode"}, "localeDescription": {"zh-CN": "取值范围【synchrono,asynchrono】，默认synchrono。服务和消息总线注册模式，仅用于OMC场景。", "en-US": "The value range is [synchrono,asynchrono]. The default value is synchrono. Service and message bus register mode, which is only for OMC scenarios."}, "enum": ["synchrono", "asynchrono"], "visible": true, "readOnly": false}, "IS_ENABLE_REVIEW_ABILITY": {"type": "boolean", "localeTitle": {"zh-CN": "是否开启审核能力", "en-US": "Open review ability or not"}, "localeDescription": {"zh-CN": "取值范围【true, false】，默认true，代表启用审核能力。可控制功能范围为【告警定制，性能自定义指标，作业控制台，流程编排】。", "en-US": "The value range is [true, false]. The default value is true, indicating that enble using review ability. The range of controllable functions are alarm customization, custom indicator of performance, workbench and ihealing."}, "visible": true, "readOnly": false}, "ER_TOTAL_LIMIT_CONN": {"type": "integer", "localeTitle": {"zh-CN": "控制ER并发连接总数", "en-US": "Total number of concurrent ER connections"}, "localeDescription": {"zh-CN": "取值范围【2, 30000】，默认值为1000，控制ER并发连接总数", "en-US": "The value range is [2, 30000]. The default value is 1000. Total number of concurrent ER connections"}, "maximum": 30000, "minimum": 2, "visible": true, "readOnly": false}, "LIMIT_CONN_PERIP": {"type": "integer", "localeTitle": {"zh-CN": "控制ER同一地址的并发连接数", "en-US": "Controls the number of concurrent connections to the same ER address"}, "localeDescription": {"zh-CN": "取值范围【2, 30000】，默认值为300，控制ER同一地址的并发连接数", "en-US": "The value range is [2, 30000]. The default value is 300.Controls the number of concurrent connections to the same ER address."}, "maximum": 30000, "minimum": 2, "visible": true, "readOnly": false}, "Kafka_Retention_Time": {"type": "string", "localeTitle": {"zh-CN": "kafka保留时间", "en-US": "Kafka Retention Time"}, "localeDescription": {"zh-CN": "取值范围【Default, Short】, 性能kafka保留时间，默认是Default。手工修改此参数，需要升级才生效。", "en-US": "The value range is [Default, Short], Performance kafka retention time, default is Default. Manually modify this parameter. The modification takes effect only after the upgrade."}, "enum": ["<PERSON><PERSON><PERSON>", "Short"], "visible": true, "readOnly": false}, "dv_deploy_scale_size": {"type": "string", "localeTitle": {"zh-CN": "网管部署规模", "en-US": "Network manager deploy scale size"}, "localeDescription": {"zh-CN": "取值范围【default, small, venus】，默认default。在小型化和轻量化场景下需要配置为small，在Venus场景下选venus，在其他场景中均保持default默认", "en-US": "The value range is [default, small, venus] . The default value is default. In mini mode and lite mode set this parameter to small.In Venus set this parameter to venus. In other scenarios, retain the default value."}, "enum": ["default", "small", "venus"], "visible": true, "readOnly": false}, "isLiteMonitor": {"type": "boolean", "localeTitle": {"zh-CN": "是否启用轻量化监控用于增强高级运维能力（用于B方案）", "en-US": "Liting Monitor or not.LiteMonitor is used for enhancing OM(B mode)"}, "localeDescription": {"zh-CN": "取值范围【false，true】，默认false，代表全量系统监控特性，适用于纯DigitalView安装和现代化网管A方案。true代表轻量化系统监控特性，适用于轻量化DigitalView和现代化网管B方案。该参数在仅工具场景不生效。", "en-US": "The value range is [false, true]. The default value is false, indicating the full system monitoring feature. This value is applicable to the DigitalView installation and modernized NMS A solution. true indicates the lightweight system monitoring feature, which is applicable to the lightweight DigitalView and modernized NMS B solution.This parameter does not take effect only in tool scenarios."}, "visible": true, "readOnly": false}, "i2k_interface_password_secure_mode": {"type": "string", "localeTitle": {"zh-CN": "机机接口密码模式", "en-US": "The machine interface password mode"}, "localeDescription": {"zh-CN": "取值范围【long_password，compatible】，默认long_password，代表网管和代理对接的密码模式为长密码安全模式，适用于新安装场景；compatible代表兼容模式，适用于对接老版本的代理，查看方式：登录代理查看$BMU_HOME/uninstall/bmu.cfg的interface_password_secure_mode配置项，和该配置项保持一致，如果没有该配置项，需要选择为兼容模式。该参数在仅工具场景无需修改。", "en-US": "The value range is [long_password, compatible]. The default value is long_password, indicating that the password mode for the interconnection between the OSS and agent is long password security mode. This value applies to new installation scenarios. compatible indicates the compatible mode, which is applicable to the proxy of the old version. To check the compatible mode, log in to the proxy and check the interface_password_secure_mode configuration item in $BMU_HOME/uninstall/bmu.cfg. If the configuration item does not exist, select the compatible mode.This parameter does not need to be modified in tool scenarios."}, "enum": ["long_password", "compatible"], "visible": true, "readOnly": false}, "enableNetworkElementLargeCapacity": {"type": "boolean", "localeTitle": {"zh-CN": "网元大容量开关", "en-US": "Whether to control network element LargeCapacity"}, "localeDescription": {"zh-CN": "取值范围【false，true】，默认为false，当DigitalView管理主机（含物理主机和虚拟机）超过200台的场景时需要设置为true。", "en-US": "The value range is [false, true]. The default value is false.Set this parameter to true when the number of DigitalView management hosts (including physical hosts and VMs) exceeds 200."}, "visible": true, "readOnly": false}, "NFV_MODE": {"type": "string", "localeTitle": {"zh-CN": "是否是NFV模式", "en-US": "Is nfv mode or not"}, "localeDescription": {"zh-CN": "取值范围【OFF，ON】，默认为OFF，仅在中国运营商NFV场景需要设置为ON。该参数在仅工具场景无需修改。", "en-US": "The value range is [OFF, ON]. The default value is OFF. Set this parameter to ON only in Carriers in China NFV scenarios.This parameter does not need to be modified in tool scenarios."}, "visible": true, "readOnly": false, "enum": ["ON", "OFF"]}, "AUTH": {"type": "string", "localeTitle": {"zh-CN": "DigitalView SSO定制", "en-US": "DigitalView SSO Customization"}, "localeDescription": {"zh-CN": "DigitalView SSO定制，默认为空。", "en-US": "DigitalView SSO Customization. The default value is null."}, "visible": true, "readOnly": false, "maxLength": 200, "pattern": "^[0-9a-zA-Z@_:/.\\[\\]-]*$"}, "ssoServerUrl": {"type": "string", "localeTitle": {"zh-CN": "第三方SSO服务器 URL", "en-US": "Third-party SSO server"}, "localeDescription": {"zh-CN": "第三方SSO服务器 URL，默认为空，非第三方SSO服务器不启用。", "en-US": "Third-party SSO server. The default value is null. Non-third-party SSO server does not start."}, "visible": true, "readOnly": false, "maxLength": 200, "pattern": "^[0-9a-zA-Z:.\\[\\];]*$"}, "ssoType": {"type": "string", "localeTitle": {"zh-CN": "单点登录类型", "en-US": "SSO type"}, "localeDescription": {"zh-CN": "取值范围【DEFAULT, VENUS, CBS_CAS】，默认DEFAULT", "en-US": "The value range is [DEFAULT, VENUS,CBS_CAS] . The default value is DEFAULT."}, "visible": true, "readOnly": false, "enum": ["DEFAULT", "VENUS", "CBS_CAS"]}, "EMSMode": {"type": "string", "localeTitle": {"zh-CN": "集中运维模式角色", "en-US": "EMS Mode"}, "localeDescription": {"zh-CN": "取值范围【Central, Local, NA】，默认NA。说明：Central:中心运维系统；Local:本地运维系统；NA:默认运维模式。设置为Central时，本DV生成的UUID值固定为1。", "en-US": "The value range is [Central,Local,NA]. The default value is NA.Note: Central: central O&M system; Local: local O&M system; NA: default O&M mode. If this parameter is set to Central, the value of UUID generated by the DigitalView is fixed to 1."}, "visible": true, "readOnly": false, "enum": ["Central", "Local", "NA"]}, "ssoBackupServerUrl": {"type": "string", "localeTitle": {"zh-CN": "第三方SSO备份服务器 URL", "en-US": "Third-party BackUp SSO server"}, "localeDescription": {"zh-CN": "第三方SSO备份服务器 URL，默认为空，非第三方SSO服务器不启用。", "en-US": "Third-party BackUp SSO server. The default value is null. Non-third-party SSO server does not start."}, "visible": true, "readOnly": false, "maxLength": 200, "pattern": "^[0-9a-zA-Z@_:/.\\[\\]-]*$"}, "approveMaxClientBodySize": {"type": "boolean", "localeTitle": {"zh-CN": "是否启用大请求体", "en-US": "Whether to enable max clent body size"}, "localeDescription": {"zh-CN": "取值范围【false，true】，默认true。如果为true，最大请求体大小为2g；如果为false，则默认为2m。", "en-US": "The value range is [false, true]. The default value is true. The max client body size is 2g, when choose true. It's 2m when choose false."}, "visible": true, "readOnly": false}, "enableAccountNonRootMode": {"type": "boolean", "localeTitle": {"zh-CN": "是否启用帐号管理支持非root模式，该模式需要DigitalView管理的所有网元都支持，否则密码修改功能不可用。", "en-US": "Specifies whether to enable account management in non-root mode,this mode requires the support of all NEs managed by the DigitalView.Otherwise, the password change function will be unavailable."}, "localeDescription": {"zh-CN": "取值范围【false，true】，默认false，是否开启帐号管理支持非root模式。", "en-US": "The value range is [false, true]. The default value is false. Indicates whether to enable multi-tenancy"}, "visible": true, "readOnly": false}, "enableUOAConnection": {"type": "boolean", "localeTitle": {"zh-CN": "是否支持连接UOA管理网元", "en-US": "Whether to enable the connection to the UOA management ne"}, "localeDescription": {"zh-CN": "取值范围【false, true】，默认false。当需要支持连接UOA管理网元时，请选择true。", "en-US": "The options are as follows [false, true]. By default, false. When need the connection to the UOA management ne, please choose true."}, "visible": true, "readOnly": false}, "topology_scale": {"type": "string", "localeTitle": {"zh-CN": "拓扑服务内存规格", "en-US": "Topology service memory specification"}, "localeDescription": {"zh-CN": "取值范围【systemScale，medium，large】.", "en-US": "The value range is [systemScale，medium，large]."}, "visible": true, "readOnly": false, "enum": ["systemScale", "medium", "large"]}, "use_forwarded_ip": {"type": "boolean", "localeTitle": {"zh-CN": "是否使用转发IP为客户端IP", "en-US": "Whether to use the forwarding IP address"}, "localeDescription": {"zh-CN": "前端通过代理访问DV时，需要设置为true，开启后使用X-Forwarded-For获取客户端IP。", "en-US": "Need to set it true when accesses DigitalView through a proxy. Then X-Forwarded-For is used to obtain the client IP address."}, "visible": true, "readOnly": false}, "dv_site_info_list": {"type": "string", "localeTitle": {"zh-CN": "DV站点信息列表", "en-US": "DV site information list"}, "localeDescription": {"zh-CN": "用于在业务拓扑上展示DV相关信息。配置DV容灾站点信息，包含DV所有生产和容灾站点信息，内容包含DV浮动IP和对应的站点ID，其中站点ID必须要与产品部署分配给DV的站点ID保持一致，如果该站点ID配置错误，则可能会导致在拓扑上无法看到DV节点信息，或者展示异常。参数样例参考:cbs:1:dv_floatIP1;2:dv_floatIP2. 如果是多个解决方案共管的场景，以|来分隔配置，样例参考:cbs:1:dv_floatIP1;2:dv_floatIP2|bes:a:dv_floatIP1;b:dv_floatIP2.站点ID获取方式：找产品部署人员提供。", "en-US": "Used to display DV information in the service topology. Configure information about all DigitalView production and DR sites, including the DigitalView floating IP address and the corresponding site ID. The site ID must be the same as the site ID allocated by the product deployment to the DigitalView. If the site ID is incorrectly configured, As a result, the DigitalView node information may not be displayed in the topology or the display may be abnormal. For details about the parameter example, see cbs:1:dv_floatIP1;2:dv_floatIP2. If it is a scenario where multiple solutions are jointly managed, use | to split, for example, cbs:1:dv_floatIP1;2:dv_floatIP2|bes:a:dv_floatIP1;b:dv_floatIP2. Site ID. To obtain the site ID, contact the product deployment personnel."}, "visible": true, "readOnly": false, "maxLength": 200, "pattern": "^[0-9a-zA-Z:.\\[\\];|]*$"}, "sandbox_escape": {"type": "string", "localeTitle": {"zh-CN": "沙箱逃生配置", "en-US": "sandbox_escape"}, "localeDescription": {"zh-CN": "取值范围【true/false】，默认值false，当取值为true时，自定义编程场景（告警定制，自定义性能，作业控制台，流程编排，故障信息收集）自定义任务可以不通过沙箱执行。", "en-US": "The value range is [true/false]. The default value is false. When the value is true, the sandbox is not used to execute user-defined programming scenarios (alarm customization, user-defined performance, job console, process orchestration, and fault information collection)."}, "enum": ["true", "false"], "visible": true, "readOnly": false}, "assurance_escape": {"type": "string", "localeTitle": {"zh-CN": "业务保障逃生配置", "en-US": "assurance_escape"}, "localeDescription": {"zh-CN": "取值范围【true/false】，默认值false，主要满足中国区诉求，当参数为true时，流程编排、作业控制台和故障信息收集各特性前台页面可支持配置root用户。", "en-US": "The options are true and false. The default value is false, which meets requirements in China. When this parameter is set to true, the root user can be configured on the foreground pages of the process orchestration, workbench, and fault information collection features."}, "enum": ["true", "false"], "visible": true, "readOnly": false}, "pkgmgr_escape": {"type": "string", "localeTitle": {"zh-CN": "包管理逃生配置", "en-US": "pkgmgr_escape"}, "localeDescription": {"zh-CN": "取值范围【true/false】，默认值false，当参数为true时，包管理可以上传无签名文件的网元包以及DV各个特性的无签名文件包，用于兼容存量业务场景.", "en-US": "The value can be true or false. The default value is false. If this parameter is set to true, NE packages without signature files and DigitalView feature packages without signature files can be uploaded for compatibility with inventory service scenarios."}, "enum": ["true", "false"], "visible": true, "readOnly": false}, "all": {"$ref": "#/definitions/all"}}}, "sm": {"type": "object", "required": ["configAuthDomainCustome", "enableSecondaryAuthentication", "smEnablePolicyAuth"], "properties": {"trustedIps": {"type": "string", "localeTitle": {"zh-CN": "SSO白名单", "en-US": "SSO whitelist"}, "localeDescription": {"zh-CN": "对接SSO的服务器的IP地址列表或域名，用于白名单校验。取值范围：可以配置为IP地址列表或域名，使用逗号分隔，例如：127.0.0.1。", "en-US": "List of IP addresses or domain names of interconnected SSO servers, which is used for whitelist verification. Enter IP addresses and domain names separated by commas (,), for example, 127.0.0.1."}, "maximum": 580, "minimum": 8, "visible": false, "readOnly": false}, "enableAuthTwoFactor": {"type": "boolean", "visible": false, "localeTitle": {"zh-CN": "双因素认证", "en-US": "Two-factor authentication"}, "localeDescription": {"zh-CN": "本地用户是否支持双因素认证。true：本地用户支持双因素认证。false：本地用户不支持双因素认证。", "en-US": "Whether to support two-factor authentication for local user. true: Two-factor authentication is supported for local users. false: Two-factor authentication is not supported for local users."}}, "smEnablePolicyAuth": {"type": "boolean", "visible": true, "readOnly": false, "localeTitle": {"zh-CN": "基于策略的鉴权能力开关", "en-US": "Policy-based Authentication Capability Switch"}, "localeDescription": {"zh-CN": "取值范围【false，true】，默认false，如果开启，需要保证configAuthDomainCustome的参数设置成'ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP'。", "en-US": "The value range is [false, true]. The default value is false. If this function is enabled, ensure that configAuthDomainCustome is set to 'ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP'."}}, "enableSecondaryAuthentication": {"type": "boolean", "localeTitle": {"zh-CN": "是否开启二次认证", "en-US": "Open secondary authentication or not"}, "localeDescription": {"zh-CN": "取值范围【true, false】，默认true，代表启用二次认证能力。当网管作为SSO客户端，且没有短信、邮件通知能力时请选false。", "en-US": "The value range is [true, false]. The default value is true, indicating that enble using secondary authentication. Select false when the NMS is the SSO client and has no SMS or email notification capabilities."}, "visible": true, "readOnly": false}, "configAuthDomainCustome": {"type": "string", "localeTitle": {"zh-CN": "分域模式定制", "en-US": "Domain-based Mode Customization"}, "localeDescription": {"zh-CN": "取值范围【'ENABL<PERSON>_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP', 'DISABLE_ALL_RES,DISABLE_SUBNET,D<PERSON><PERSON>LE_DEV,ENABLE_RESGROUP','ENABLE_ALL_RES,ENABLE_DEV_TYPE,ENABLE_SUBNET,ENABLE_DEV_SETS,ENABLE_DEV,DISABLE_RESGROUP,DISABLE_ALL_MNA_OBJ'】，默认ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP", "en-US": "The value range is ['<PERSON>NABL<PERSON>_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP','DISABLE_ALL_RES,DISABLE_SUBNET,<PERSON><PERSON><PERSON><PERSON>_DEV,ENABLE_RESGROUP', 'ENABLE_ALL_RES,ENABLE_DEV_TYPE,ENABLE_SUBNET,ENABLE_DEV_SETS,ENABLE_DEV,DISABLE_RESGROUP,DISABLE_ALL_MNA_OBJ']. The default value is 'ENABLE_ALL_RES,DISABLE_SUBNET,D<PERSON><PERSON><PERSON>_DEV,ENABLE_RESGROUP'."}, "enum": ["ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP", "DISABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP", "ENABLE_ALL_RES,ENABLE_DEV_TYPE,ENABLE_SUBNET,ENABLE_DEV_SETS,ENABLE_DEV,DISABLE_RESGROUP,DISABLE_ALL_MNA_OBJ"], "visible": true, "readOnly": false}, "configUnisessionSessionKey": {"type": "string", "localeTitle": {"zh-CN": "定制会话<PERSON>ie", "en-US": "Session cookie customization"}, "localeDescription": {"zh-CN": "定制会话Cookie，取值范围【bspsession，空】，默认为空。如果选择bspsession，则会话cookie的key值为bspsession。", "en-US": "Session cookie customization, the value range is [bspsession, null]. The default value is null. The key value of session cookie will be bspsession, when choose bspsession."}, "pattern": "^[0-9a-zA-Z]*$", "maxLength": 100, "visible": true, "readOnly": false}, "configUnisessionAuthMode": {"type": "string", "localeTitle": {"zh-CN": "认证模式", "en-US": "Authentication Mode"}, "localeDescription": {"zh-CN": "认证模式，取值范围【thirdpart，空】，默认为空。如果是第三方认证，请选择thirdpart", "en-US": "Authentication Mode, the value range is [thirdpart, null]. The default value is null. If the authentication is third part, please choose thirdpart."}, "pattern": "^[0-9a-zA-Z]*$", "maxLength": 100, "visible": true, "readOnly": false}}}, "fm": {"type": "object", "required": ["scale", "lucene"], "properties": {"scale": {"type": "object", "required": ["currentAlarm"], "properties": {"rwo": {"type": "object", "properties": {"pv": {"type": "object", "properties": {"service": {"type": "object", "properties": {"storageClassName": {"type": "string", "visible": false, "readOnly": true}}}}}}}, "currentAlarm": {"type": "integer", "localeTitle": {"zh-CN": "当前告警容量", "en-US": "Current Alarm Capacity"}, "localeDescription": {"zh-CN": "告警范围仅支持7个值[20000,50000,100000,200000,300000,500000,1000000]，默认20000，代表当前告警容量上限20000，如果取值为1000000则代表当前告警容量上限为最高的1000000，提高参数的同时需要定制化提升appnode节点内存。该参数在仅工具场景不生效。", "en-US": "Value range is [20000,50000,100000,200000,300000,500000,1000000]. The default value is 20000, indicating that the maximum number of current alarms is 20000. The value 1000000 indicates that the maximum number of current alarms is 1000000. The memory of the AppNode needs to be increased.This parameter does not take effect only in tool scenarios."}, "enum": [20000, 50000, 100000, 200000, 300000, 500000, 1000000], "visible": true, "readOnly": false}}}, "lucene": {"type": "object", "required": ["enabled"], "properties": {"enabled": {"type": "boolean", "localeTitle": {"zh-CN": "历史告警查询是否启动Lucene查询", "en-US": "Enable <PERSON><PERSON> query for historical alarms"}, "localeDescription": {"zh-CN": "历史告警查询是否启动Lucene查询。取值范围：true：开启。false：关闭。", "en-US": "Whether to enable <PERSON><PERSON> query for historical alarms. Options: true: Enable. false: Disable."}, "visible": true, "readOnly": false}}}}}, "hofs": {"type": "object", "properties": {"cluster": {"type": "string", "visible": false, "readOnly": true}}}, "zenith": {"type": "object", "properties": {"replicas": {"type": "integer", "visible": false, "readOnly": true}}}, "redis": {"type": "object", "properties": {"replicas": {"type": "integer", "visible": false, "readOnly": true}}}, "cloudsop": {"type": "object", "properties": {"zenith": {"type": "object", "properties": {"instance": {"properties": {"default": {"type": "object", "properties": {"replicas": {"type": "integer", "visible": false, "readOnly": true}}}}}}}, "redis": {"type": "object", "properties": {"instance": {"type": "object", "properties": {"default": {"type": "object", "properties": {"replicas": {"type": "integer", "visible": false, "readOnly": true}}}}}}}}}, "repo": {"type": "object", "properties": {"address": {"type": "string", "visible": false, "readOnly": true}}}, "scale": {"type": "object", "properties": {"equivalentNE": {"type": "integer", "visible": true, "readOnly": true}}}, "storageClass": {"type": "object", "properties": {"default": {"type": "string", "visible": false, "readOnly": true}}}, "features": {"type": "object", "properties": {"cloudsop": {"$ref": "#/definitions/cloudsop-features"}, "digitalview": {"$ref": "#/definitions/digitalview-features"}}}, "digitalview-features": {"type": "object", "properties": {"sysMonitor": {"type": "object", "properties": {"dvsm": {"type": "boolean", "visible": false, "readOnly": true}, "dvengineeringservicepackage": {"type": "boolean", "visible": false, "readOnly": true}, "dvmiddleware": {"type": "boolean", "visible": false, "readOnly": true}, "dvsolution": {"type": "boolean", "visible": false, "readOnly": true}, "dvalarm": {"type": "boolean", "visible": false, "readOnly": true}, "dveam": {"type": "boolean", "visible": false, "readOnly": true}, "dvnbi": {"type": "boolean", "visible": false, "readOnly": true}, "dvtopo": {"type": "boolean", "visible": false, "readOnly": true}, "dvipmodify": {"type": "boolean", "visible": false, "readOnly": true}, "dvmenuutm": {"type": "boolean", "visible": false, "readOnly": true}}}, "dvcommon": {"type": "object", "properties": {"dvcertmgmtservice": {"type": "boolean", "visible": false, "readOnly": true}, "dvregisterservice": {"type": "boolean", "visible": false, "readOnly": true}, "dvcommonnotifyservice": {"type": "boolean", "visible": false, "readOnly": true}, "dvcommonwebsite": {"type": "boolean", "visible": false, "readOnly": true}}}, "dvperformance": {"type": "boolean", "visible": false, "readOnly": true}, "dvmaintenance": {"type": "object", "properties": {"mmlmaintenanceservice": {"type": "boolean", "localeTitle": {"zh-CN": "MML 服务", "en-US": "MML Service"}, "localeDescription": {"zh-CN": "仅适用于中国运营商NFV局点使用。请根据局点规划确定是否勾选。", "en-US": "This feature applies only to NFV sites of carriers in China. Select this feature based on the site requirements."}, "visible": true, "readOnly": false}, "dvmaintenancewebsite": {"type": "boolean", "localeTitle": {"zh-CN": "MML 服务", "en-US": "MML Service"}, "localeDescription": {"zh-CN": "仅适用于中国运营商NFV局点使用。请根据局点规划确定是否勾选。", "en-US": "This feature applies only to NFV sites of carriers in China. Select this feature based on the site requirements."}, "visible": true, "readOnly": false}, "dv4aaccessservice": {"type": "boolean", "localeTitle": {"zh-CN": "4A对接服务", "en-US": "4A Access Service"}, "localeDescription": {"zh-CN": "仅适用于中国运营商NFV局点使用。请根据局点规划确定是否勾选。4A对接服务为POC特性，仅支持调测，不做商用。", "en-US": "This feature applies only to NFV sites of carriers in China. Select this feature based on the site requirements.The 4A interconnection service is a POC feature that supports only commissioning and is not put into commercial use."}, "visible": true, "readOnly": false}}}, "dvlogtrace": {"type": "boolean", "localeTitle": {"zh-CN": "运维分析-服务跟踪", "en-US": "OM_LogtraceService Feature"}, "localeDescription": {"zh-CN": "若安装“运维分析-服务跟踪”，必须勾选“运维分析-日志服务”。用户使用跟踪条件对业务内部操作、外部对业务系统的请求消息进行匹配，匹配成功的消息处理流程记录在跟踪日志中，以便使用跟踪日志定位业务故障。", "en-US": "To install OM_LogtraceService Feature, OM_LogmatrixService Feature must be selected.You can configure tracing conditions to match an internal service operation based on a request from an external service system. The process of handling a request for which an internal service operation is matched is recorded in tracing logs. Tracing logs can be used to locate service faults."}, "visible": true, "readOnly": false}, "dvcallchain": {"type": "boolean", "localeTitle": {"zh-CN": "运维分析-调用链", "en-US": "OM_CallchainService Feature"}, "localeDescription": {"zh-CN": "若安装“运维分析-调用链”，必须勾选“运维分析-日志服务”。运维系统收集服务调用日志，并通过分析日志定界故障。运维人员可使用此功能来定界故障和分析性能瓶颈。", "en-US": "To install OM_CallchainService Feature, OM_LogmatrixService must be selected.The O&M system collects service invoking logs and demarcates faults by analyzing the logs.O&M personnel can use this function to demarcate faults and analyze performance bottlenecks."}, "visible": true, "readOnly": false}, "dvcat": {"type": "boolean", "localeTitle": {"zh-CN": "运维分析-业务拨测", "en-US": "OM_CatService Feature"}, "localeDescription": {"zh-CN": "若安装“运维分析-业务拨测”，必须勾选“业务保障-作业控制台”。业务拨测即主动检查业务系统的状态，监控被拨测系统的服务质量，图形化呈现业务的可用性信息，并可触发即时告警和进行智能分析。", "en-US": "To install OM_CatService Feature, SA_WorkBenchService Feature must be selected.Proactively checks the status of the service system, monitors the service quality, graphically displays the service availability information, triggers real-time alarms, and performs intelligent analysis."}, "visible": false, "readOnly": true}, "dvlogmatrix": {"type": "object", "properties": {"dvlogmatrix": {"type": "boolean", "localeTitle": {"zh-CN": "运维分析-日志服务", "en-US": "OM_LogmatrixService Feature"}, "localeDescription": {"zh-CN": "若安装“运维分析-日志服务”，必须勾选“业务保障-作业控制台”。日志服务不仅为服务调用链和服务跟踪提供日志，还具备端到端基于日志的故障定位能力。", "en-US": "To install OM_LogmatrixService Feature, SA_WorkBenchService Feature must be selected.Provides logs for service call chains and service tracing, and locates end-to-end faults."}, "visible": true, "readOnly": false}, "dvlogmatrixstreaming": {"type": "boolean", "localeTitle": {"zh-CN": "运维分析-日志服务", "en-US": "OM_LogmatrixService Feature"}, "localeDescription": {"zh-CN": "若安装“运维分析-日志服务”，必须勾选“业务保障-作业控制台”。日志服务不仅为服务调用链和服务跟踪提供日志，还具备端到端基于日志的故障定位能力。", "en-US": "To install OM_LogmatrixService Feature, SA_WorkBenchService Feature must be selected.Provides logs for service call chains and service tracing, and locates end-to-end faults."}, "visible": false, "readOnly": true}}}, "dvworkbench": {"type": "object", "properties": {"dvzookeeper": {"type": "boolean", "localeTitle": {"zh-CN": "业务保障-作业控制台", "en-US": "SA_WorkBenchService Feature"}, "localeDescription": {"zh-CN": "作业控制台提供一个可视化、一键式、自动化的作业平台，运维人员可通过它批量执行命令和脚本，远程完成复杂的维护任务。", "en-US": "The workbench is a visualized, one-click, and automatic operation platform for O&M personnel to run commands and scripts in batches and remotely execute complex maintenance tasks."}, "visible": true, "readOnly": false}, "dvnats": {"type": "boolean", "localeTitle": {"zh-CN": "业务保障-作业控制台", "en-US": "SA_WorkBenchService Feature"}, "localeDescription": {"zh-CN": "作业控制台提供一个可视化、一键式、自动化的作业平台，运维人员可通过它批量执行命令和脚本，远程完成复杂的维护任务。", "en-US": "The workbench is a visualized, one-click, and automatic operation platform for O&M personnel to run commands and scripts in batches and remotely execute complex maintenance tasks."}, "visible": true, "readOnly": false}, "dvfoundation": {"type": "boolean", "localeTitle": {"zh-CN": "业务保障-作业控制台", "en-US": "SA_WorkBenchService Feature"}, "localeDescription": {"zh-CN": "作业控制台提供一个可视化、一键式、自动化的作业平台，运维人员可通过它批量执行命令和脚本，远程完成复杂的维护任务。", "en-US": "The workbench is a visualized, one-click, and automatic operation platform for O&M personnel to run commands and scripts in batches and remotely execute complex maintenance tasks."}, "visible": true, "readOnly": false}, "dvelasticsearch": {"type": "boolean", "localeTitle": {"zh-CN": "业务保障-作业控制台", "en-US": "SA_WorkBenchService Feature"}, "localeDescription": {"zh-CN": "作业控制台提供一个可视化、一键式、自动化的作业平台，运维人员可通过它批量执行命令和脚本，远程完成复杂的维护任务。", "en-US": "The workbench is a visualized, one-click, and automatic operation platform for O&M personnel to run commands and scripts in batches and remotely execute complex maintenance tasks."}, "visible": true, "readOnly": false}, "dvoma": {"type": "boolean", "localeTitle": {"zh-CN": "业务保障-作业控制台", "en-US": "SA_WorkBenchService Feature"}, "localeDescription": {"zh-CN": "作业控制台提供一个可视化、一键式、自动化的作业平台，运维人员可通过它批量执行命令和脚本，远程完成复杂的维护任务。", "en-US": "The workbench is a visualized, one-click, and automatic operation platform for O&M personnel to run commands and scripts in batches and remotely execute complex maintenance tasks."}, "visible": true, "readOnly": false}, "dvworkbench": {"type": "boolean", "localeTitle": {"zh-CN": "业务保障-作业控制台", "en-US": "SA_WorkBenchService Feature"}, "localeDescription": {"zh-CN": "作业控制台提供一个可视化、一键式、自动化的作业平台，运维人员可通过它批量执行命令和脚本，远程完成复杂的维护任务。", "en-US": "The workbench is a visualized, one-click, and automatic operation platform for O&M personnel to run commands and scripts in batches and remotely execute complex maintenance tasks."}, "visible": true, "readOnly": false}, "dvworkbeans": {"type": "boolean", "localeTitle": {"zh-CN": "业务保障-作业控制台", "en-US": "SA_WorkBenchService Feature"}, "localeDescription": {"zh-CN": "作业控制台提供一个可视化、一键式、自动化的作业平台，运维人员可通过它批量执行命令和脚本，远程完成复杂的维护任务。", "en-US": "The workbench is a visualized, one-click, and automatic operation platform for O&M personnel to run commands and scripts in batches and remotely execute complex maintenance tasks."}, "visible": true, "readOnly": false}, "dvcontroller": {"type": "boolean", "localeTitle": {"zh-CN": "业务保障-作业控制台", "en-US": "SA_WorkBenchService Feature"}, "localeDescription": {"zh-CN": "作业控制台提供一个可视化、一键式、自动化的作业平台，运维人员可通过它批量执行命令和脚本，远程完成复杂的维护任务。", "en-US": "The workbench is a visualized, one-click, and automatic operation platform for O&M personnel to run commands and scripts in batches and remotely execute complex maintenance tasks."}, "visible": true, "readOnly": false}}}, "dvihealing": {"type": "boolean", "localeTitle": {"zh-CN": "业务保障-流程编排", "en-US": "SA_FlowOrchestration Feature"}, "localeDescription": {"zh-CN": "若安装“业务保障-故障信息收集”，必须勾选“业务保障-作业控制台”。针对处理流程相对固化的故障，将故障的告警和处理流程关联，告警发生时按照提前预置好的故障处理流程自动执行，实现分钟级故障诊断和自愈。", "en-US": "To install SA_FlowOrchestration Feature, SA_WorkBenchService Feature must be selected.For faults with fixed handling processes, fault alarms are associated with these handling processes. When an alarm is generated, the preset troubleshooting process is automatically executed, implementing minute-level fault diagnosis and self-healing."}, "visible": true, "readOnly": false}, "dvinfocollect": {"type": "boolean", "localeTitle": {"zh-CN": "业务保障-故障信息收集", "en-US": "SA_InfoCollectService Feature"}, "localeDescription": {"zh-CN": "若安装“业务保障-故障信息收集”，必须勾选“业务保障-作业控制台”。业务系统发生故障后，运维人员可以使用故障信息收集工具一键收集日志、配置等信息用于故障定位，避免手工收集导致的信息收集不全以及效率低下。", "en-US": "To install SA_FaultInformationCollection Feature, SA_WorkBenchService Feature must be selected.When a fault occurs in the service system, O&M personnel can use the fault information collection tool to collect logs and configurations in one-click mode for fault locating, preventing incomplete information collection and low efficiency caused by manual collection."}, "visible": true, "readOnly": false}, "dvdashboard": {"type": "boolean", "localeTitle": {"zh-CN": "监控大盘", "en-US": "PMDashBoard Feature"}, "localeDescription": {"zh-CN": "提供大盘性能指标监控能力。", "en-US": "Monitors dashboard performance indicators."}, "visible": true, "readOnly": false}, "dvsaasops": {"type": "boolean", "localeTitle": {"zh-CN": "云原生监控", "en-US": "Cloud Monitor"}, "localeDescription": {"zh-CN": "用于集成CloudMonitor服务。请根据实际规划选择。", "en-US": "You can select this feature only in the all-in-one networking scenario. Select this feature based on the site requirements."}, "visible": true, "readOnly": false}, "dvanalysisengine": {"type": "boolean", "localeTitle": {"zh-CN": "智能运维", "en-US": "AIOps Service"}, "localeDescription": {"zh-CN": "基于智能技术实现无人化看守，快速自主检测异常，提升告警定位和处理效率，智能化评估容量变化，预测容量瓶颈，帮助运营商提升运维效率，降低故障闭环时间，保障软件服务质量。", "en-US": "Implements unattended monitoring based on intelligent technologies, quickly and automatically detect exceptions, improves alarm locating and handling efficiency, intelligently evaluates capacity changes, and predicts capacity bottlenecks. This feature helps carriers improve O&M efficiency, reduce fault rectification time, and ensure service quality. "}, "visible": true, "readOnly": false}, "dvmediation": {"type": "boolean", "visible": false, "readOnly": true}}}, "cloudsop-features": {"type": "object", "properties": {"aifm": {"type": "object", "properties": {"aifmservice": {"type": "boolean", "visible": false, "readOnly": true}, "aifmrisk": {"type": "boolean", "visible": false, "readOnly": true}, "aifmwebsite": {"type": "boolean", "visible": false, "readOnly": true}}}, "naie": {"type": "object", "properties": {"infers": {"type": "object", "properties": {"aijobs": {"type": "boolean", "visible": false, "readOnly": true}}}}}, "odrs": {"type": "object", "properties": {"history": {"type": "boolean", "visible": false, "readOnly": true}, "mirror": {"type": "boolean", "visible": false, "readOnly": true}, "objectGraph": {"type": "boolean", "visible": false, "readOnly": true}, "resWebsite": {"type": "boolean", "visible": false, "readOnly": true}, "dynamicMap": {"type": "boolean", "visible": false, "readOnly": true}}}, "odae": {"type": "object", "properties": {"core": {"type": "boolean", "visible": false, "readOnly": true}, "driver": {"type": "boolean", "visible": false, "readOnly": true}, "datareplication": {"type": "boolean", "visible": false, "readOnly": true}, "datalifecycle": {"type": "boolean", "visible": false, "readOnly": true}, "schedule": {"type": "boolean", "visible": false, "readOnly": true}, "monitor": {"type": "boolean", "visible": false, "readOnly": true}, "log": {"type": "boolean", "visible": false, "readOnly": true}, "dteeventcomput": {"type": "boolean", "visible": false, "readOnly": true}, "dteesservice": {"type": "boolean", "visible": false, "readOnly": true}}}, "infrastructure": {"type": "object", "properties": {"mqscluster": {"type": "boolean", "visible": false, "readOnly": true}, "business": {"type": "boolean", "visible": false, "readOnly": true}, "hyperha": {"type": "boolean", "visible": false, "readOnly": true}}}, "topo": {"type": "boolean", "visible": false, "readOnly": true}, "autodis": {"type": "boolean", "visible": false, "readOnly": true}, "lighttopowebsite": {"type": "boolean", "visible": false, "readOnly": true}, "snbi": {"type": "object", "properties": {"apigw": {"type": "boolean", "visible": false, "readOnly": true}, "cert": {"type": "boolean", "visible": false, "readOnly": true}, "driverwebsite": {"type": "boolean", "visible": false, "readOnly": true}, "nbifrm": {"type": "boolean", "visible": false, "readOnly": true}, "driver": {"type": "boolean", "visible": false, "readOnly": true}, "legacynbi": {"type": "boolean", "visible": false, "readOnly": true}}}, "uainfo": {"type": "object", "properties": {"unaviwebsite": {"type": "boolean", "visible": false, "readOnly": true}}}, "ics": {"type": "object", "properties": {"icsservice": {"type": "boolean", "visible": false, "readOnly": true}}}, "hiseccaproxy": {"type": "object", "properties": {"deploymentPlane": {"type": "object", "properties": {"service": {"type": "boolean", "visible": false, "readOnly": true}, "management": {"type": "boolean", "visible": false, "readOnly": true}}}}}, "imap": {"type": "object", "properties": {"commonlicservice": {"type": "boolean", "visible": false, "readOnly": true}, "namingservice": {"type": "boolean", "visible": false, "readOnly": true}, "scriptmoduleservice": {"type": "boolean", "visible": false, "readOnly": true}, "scriptproxyservice": {"type": "boolean", "visible": false, "readOnly": true}, "scriptrunnerservice": {"type": "boolean", "visible": false, "readOnly": true}, "nhcservice": {"type": "boolean", "visible": false, "readOnly": true}, "trapdispatcherservice": {"type": "boolean", "visible": false, "readOnly": true}}}}}, "all": {"type": "object", "required": ["enableSaasMode"], "properties": {"enableSaasMode": {"type": "string", "enum": ["true", "false"], "localeTitle": {"zh-CN": "是否开启运维多租", "en-US": "Whether to enable multi-tenancy"}, "localeDescription": {"zh-CN": "取值范围【false，true】，默认false，是否开启运维多租。", "en-US": "The value range is [false, true]. The default value is false. Indicates whether to enable multi-tenancy."}, "visible": true, "readOnly": false}}}}}