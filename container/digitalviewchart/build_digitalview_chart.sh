#!/bin/bash
BASE_DIR=$(cd $(dirname $0); pwd)
SERVICE_NAME="digitalview"

source ${WORKSPACE}/DV/build/config.properties

function main()
{
    # 判断操作系统架构
    if [ "x${os_tag}" == "x_aarch64" ];then
        sys_tag="aarch64"
        output_dir="${WORKSPACE}/output/image-pkgs/product/ARM/temp/helm"
    else
        sys_tag="x86_64"
        output_dir="${WORKSPACE}/output/image-pkgs/product/X86_64/temp/helm"
    fi

    echo "---------- start build dv chart pkg ----------"
    # 获取CI代码仓中非服务化版本号
    pkg_verison=$(echo ${I2000_VERSION} | awk -F "-SNAPSHOT" '{print $1}')

    # 目录存在先清理目录
    [ -d "${BASE_DIR}/charts" ] && rm -rf ${BASE_DIR}/charts
    mkdir -p ${BASE_DIR}/charts
    # 拷贝DV及平台服务chart包
    src_dir="${WORKSPACE}/output/image-pkgs"
    local cloud_base_dir="${WORKSPACE}/output/platform/CloudBase/Software/Software/${sys_tag}/CloudBase"
    local iservice_dir="${WORKSPACE}/output/platform/iService/Software/helm"
    local dte_dir="${WORKSPACE}/output/platform/DTE/Software/helm"
    local dest_dir="${BASE_DIR}/charts"

    # 平台chart包白名单管控
    local iservice_trust_service="baseservicebusiness bizinfra fm hiseccaproxy hisecliteca ics sm snbi topo uainfo imapadapter"
    local dte_trust_service="dte"
    local cloudbase_trust_service="baseservice infra"
    for iservice_service in ${iservice_trust_service[@]}
    do
        find ${iservice_dir} -type f |grep -w "${iservice_service}" |egrep -e ".tgz$" |xargs -i cp -rf {} ${dest_dir}
    done

    for dte_service in ${dte_trust_service[@]}
    do
        find ${dte_dir} -type f |grep -w "${dte_service}" |egrep -e ".tgz$" |xargs -i cp -rf {} ${dest_dir}
    done

    for cloudbase_service in ${cloudbase_trust_service[@]}
    do
        if [ "x${cloudbase_service}" == "xbaseservice" ];then
            # 业务面的为 baseservicebusiness，此处不需要拷贝管理面的baseservice，防止部署失败
            continue
        fi
        find ${cloud_base_dir} -type f |grep -w "${cloudbase_service}" |egrep -e ".tgz$" |xargs -i cp -rf {} ${dest_dir}
    done

    # 拷贝DV自身服务的chart包
    find ${src_dir} -name "*.tgz" | egrep -v "product" | xargs -i cp -rpf {} ${dest_dir}

    # 平台bizinfra包定制
    modify_bizinfra

    # 修改参数
    local redis_image_pkg=$(find ${cloud_base_dir} -type f |grep -w "redis" |egrep -e ".tar.gz$")
    local redis_image_version=$(echo ${redis_image_pkg} |awk -F "/" '{print $NF}' |awk -F "-" '{print $2}')


    find ${BASE_DIR}/.. -name "*.yaml" | xargs sed -i "s/{{dv_chart_version}}/${pkg_verison}/g"
    find ${BASE_DIR} -name "*.yaml" | xargs sed -i "s/{{redis_image_version}}/${redis_image_version}/g"
    find ${BASE_DIR} -name "*.yaml" | xargs sed -i "s/{{dv_version}}/${DV_PKG_VERSION}/g"


    local hofsosdservice_image_pkg=$(find ${cloud_base_dir} -type f |grep -w "hofsosdservice" |egrep -e ".tar.gz$")
    local hofsosdservice_image_version=$(echo ${hofsosdservice_image_pkg} |awk -F "/" '{print $NF}' |awk -F "-" '{print $2}')
    find ${BASE_DIR} -name "hofscluter.yaml" | xargs sed -i "s/{{osd_version}}/${hofsosdservice_image_version}/g"

    # 文件如果存在，构建前清理
    [ -f "${BASE_DIR}/package.tar" ] && rm -rf ${BASE_DIR}/package.tar
    # 构建package.tar包
    cd ${BASE_DIR}
    tar --format=gnu -cvf package.tar --exclude="Dockerfile" --exclude="build_digitalview_chart.sh" *
    chmod 644 package.tar

    # 构建产品chart镜像包
    docker build -t ${SERVICE_NAME}-noarch:${pkg_verison} .

    # 保存镜像
    [ -d "${output_dir}" ] && rm -rf ${output_dir}
    mkdir -p ${output_dir}
    docker save ${SERVICE_NAME}-noarch:${pkg_verison} | pigz -n -6 -p 16 > ${output_dir}/${SERVICE_NAME}-${pkg_verison}-noarch.tar.gz
    echo "---------- build dv chart pkg success ----------"
}

function modify_bizinfra()
{
    echo ">>> start modify bizinfra package..."
    [ -d "${dest_dir}/bizinfra_temp" ] && rm -rf ${dest_dir}/bizinfra_temp
    mkdir -p ${dest_dir}/bizinfra_temp
    cd ${dest_dir}
    bizinfra_name=$(ls bizinfra-*.tgz)
    tar -zxvf ${bizinfra_name} -C bizinfra_temp
    cp -rf ${BASE_DIR}/../DVDBCustom/bizinfra/zenithcloudsopchart bizinfra_temp/bizinfra/charts
    zenith_version=$(find ${src_dir} -name "zenith-*.tar.gz" | grep "${sys_tag}" | awk -F "/" '{print $NF}' | awk -F "-" '{print $2}')
    sed -i "s#{{zenith_version}}#${zenith_version}#g" ${dest_dir}/bizinfra_temp/bizinfra/charts/zenithcloudsopchart/values.yaml
    cd ${dest_dir}/bizinfra_temp
    tar -czvf ${dest_dir}/${bizinfra_name} *
    echo ">>> end modify bizinfra package."
    rm -rf ${dest_dir}/bizinfra_temp
}

main $@
exit $?