type: object
field:
  editMasterButton:
    type: boolean
    display: false
    value: false
  lld:
    type: object
    widget: CustomPanel
    title:
      uis_zh: 参数配置
      uis_en: Parameter Configuration
    field:
      lldConfigList:
        type: array
        widget: Tab
        items:
          - type: object
            title:
              uis_zh: 网络规划
              uis_en: Network Planning
            field:
              basicInfo:
                type: object
                widget: RightAlignLayout
                field:
                  statInfo:
                    widget: CustomParamInfo
                    enable: false
                    props:
                      paramInfo:
                        type: formDetails
                        func: uis_getFormDetails
                        params:
                          path: '{{modelPath(#.#)}}'
                      uis_interacts:
                        - type: onRequiredClick
                          func: uis_onFocusForm
                          params:
                            type: required
                            path: '$.lld.lldConfigList.0.details'
                        - type: onToBeFilledClick
                          func: uis_onFocusForm
                          params:
                            type: toBeFilled
                            path: '$.lld.lldConfigList.0.details'
                        - type: onErrorClick
                          func: uis_onFocusForm
                          params:
                            type: error
                            path: '$.lld.lldConfigList.0.details'
                  editable:
                    type: boolean
                    widget: EditableButton
                    staticValue: false
                    id: network_planning
                    props:
                      uis_interacts:
                        - type: onClick
                          func: onClickEditableButton
                          params:
                            value: '{{@}}'
                          target:
                            value: '{{path(@)}}'
                      disabled: '{{$.editMasterButton}}'
              details:
                type: object
                field:
                  node_admission_information:
                    type: object
                    title:
                      uis_zh: 节点准入信息
                      uis_en: Node Admission Information
                    field:
                      configDetail:
                        type: array
                        widget: Table
                        header:
                          hostname:
                            title:
                              uis_zh: 主机名
                              uis_en: Host Name
                          interface:
                            title:
                              uis_zh: 网口
                              uis_en: Network Interface
                          ip_version:
                            title:
                              uis_zh: IP版本
                              uis_en: IP Version
                          network_plan:
                            title:
                              uis_zh: 网络平面
                              uis_en: Network Plane
                          ipaddress:
                            title:
                              uis_zh: IP地址
                              uis_en: IP Address
                          netmask:
                            title:
                              uis_zh: 掩码
                              uis_en: Mask
                          des_network:
                            title:
                              uis_zh: 目的网段
                              uis_en: Destination network segment
                          des_mask:
                            title:
                              uis_zh: 目的掩码
                              uis_en: Genmask
                          gateway:
                            title:
                              uis_zh: 网关
                              uis_en: Gateway
                          is_external:
                            title:
                              uis_zh: 对外网口
                              uis_en: External Network Port
                            display: false
                        ext_format: one_to_many
                        id: node_info
                        items:
                          - id: node_master1
                            type: object
                            row: 1
                            field:
                              hostname:
                                type: string
                                widget: LabelField
                                value: master1
                              interface:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomTextField
                                    value: eth0
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                                      uis_interacts:
                                        - type: onBlur
                                          func: onBatchModify
                                          target:
                                            path0: '{{path($.lld.lldConfigList.0.details.node_admission_information.configDetail.0.interface.0)}}'
                                            path1: '{{path($.lld.lldConfigList.0.details.node_admission_information.configDetail.1.interface.0)}}'
                                            path2: '{{path($.lld.lldConfigList.0.details.node_admission_information.configDetail.2.interface.0)}}'
                                            path3: '{{path($.lld.lldConfigList.0.details.app_network_information.configDetail.0.interface)}}'
                              ip_version:
                                type: array
                                items:
                                  - type: string
                                    widget: LabelField
                                    value: IPV4
                              network_plan:
                                type: array
                                items:
                                  - type: string
                                    widget: LabelField
                                    value: External Plane
                                    props:
                                      helpTips:
                                        tipContent:
                                          uis_zh: CaaS中转机运维平面
                                          uis_en: O&M plane of the CaaS transition host
                              ipaddress:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomIPInput
                                    required: true
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              netmask:
                                type: array
                                items:
                                  - type: string
                                    widget: SubnetMask
                                    required: true
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              des_network:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomIPInput
                                    value: 0.0.0.0
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              des_mask:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomIPInput
                                    value: 0.0.0.0
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              gateway:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomIPInput
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              is_external:
                                type: array
                                items:
                                  - type: string
                                    widget: LabelField
                                    value: true
                          - id: node_master2
                            type: object
                            row: 1
                            field:
                              hostname:
                                type: string
                                widget: LabelField
                                value: master2
                              interface:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomTextField
                                    value: eth0
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                                      uis_interacts:
                                        - type: onBlur
                                          func: onBatchModify
                                          target:
                                            path0: '{{path($.lld.lldConfigList.0.details.node_admission_information.configDetail.1.interface.0)}}'
                                            path1: '{{path($.lld.lldConfigList.0.details.node_admission_information.configDetail.0.interface.0)}}'
                                            path2: '{{path($.lld.lldConfigList.0.details.node_admission_information.configDetail.2.interface.0)}}'
                                            path3: '{{path($.lld.lldConfigList.0.details.app_network_information.configDetail.0.interface)}}'
                              ip_version:
                                type: array
                                items:
                                  - type: string
                                    widget: LabelField
                                    value: IPV4
                              network_plan:
                                type: array
                                items:
                                  - type: string
                                    widget: LabelField
                                    value: External Plane
                                    props:
                                      helpTips:
                                        tipContent:
                                          uis_zh: CaaS中转机运维平面
                                          uis_en: O&M plane of the CaaS transition host
                              ipaddress:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomIPInput
                                    required: true
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              netmask:
                                type: array
                                items:
                                  - type: string
                                    widget: SubnetMask
                                    required: true
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              des_network:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomIPInput
                                    value: 0.0.0.0
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              des_mask:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomIPInput
                                    value: 0.0.0.0
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              gateway:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomIPInput
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              is_external:
                                type: array
                                items:
                                  - type: string
                                    widget: LabelField
                                    value: true
                          - id: node_master3
                            type: object
                            row: 1
                            field:
                              hostname:
                                type: string
                                widget: LabelField
                                value: master3
                              interface:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomTextField
                                    value: eth0
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                                      uis_interacts:
                                        - type: onBlur
                                          func: onBatchModify
                                          target:
                                            path0: '{{path($.lld.lldConfigList.0.details.node_admission_information.configDetail.2.interface.0)}}'
                                            path1: '{{path($.lld.lldConfigList.0.details.node_admission_information.configDetail.0.interface.0)}}'
                                            path2: '{{path($.lld.lldConfigList.0.details.node_admission_information.configDetail.1.interface.0)}}'
                                            path3: '{{path($.lld.lldConfigList.0.details.app_network_information.configDetail.0.interface)}}'
                              ip_version:
                                type: array
                                items:
                                  - type: string
                                    widget: LabelField
                                    value: IPV4
                              network_plan:
                                type: array
                                items:
                                  - type: string
                                    widget: LabelField
                                    value: External Plane
                                    props:
                                      helpTips:
                                        tipContent:
                                          uis_zh: CaaS中转机运维平面
                                          uis_en: O&M plane of the CaaS transition host
                              ipaddress:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomIPInput
                                    required: true
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              netmask:
                                type: array
                                items:
                                  - type: string
                                    widget: SubnetMask
                                    required: true
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              des_network:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomIPInput
                                    value: 0.0.0.0
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              des_mask:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomIPInput
                                    value: 0.0.0.0
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              gateway:
                                type: array
                                items:
                                  - type: string
                                    widget: CustomIPInput
                                    props:
                                      editable: '{{dslId(network_planning)}}'
                              is_external:
                                type: array
                                items:
                                  - type: string
                                    widget: LabelField
                                    value: true
                  app_network_information:
                    type: object
                    title:
                      uis_zh: 业务容器网络配置
                      uis_en: Service Container Network Configuration
                    field:
                      configDetail:
                        type: array
                        widget: Table
                        header:
                          interface:
                            title:
                              uis_zh: 网口
                              uis_en: Network Interface
                          network_usage:
                            title:
                              uis_zh: 网络用途
                              uis_en: Network Usage
                          ip_version:
                            title:
                              uis_zh: IP版本
                              uis_en: IP Version
                          ipaddress:
                            title:
                              uis_zh: IP地址
                              uis_en: IP address
                          netmask:
                            title:
                              uis_zh: 掩码
                              uis_en: Mask
                          des_network:
                            title:
                              uis_zh: 目的网段
                              uis_en: Destination network segment
                          des_mask:
                            title:
                              uis_zh: 目的掩码
                              uis_en: Genmask
                          gateway:
                            title:
                              uis_zh: 网关
                              uis_en: Gateway
                        ext_format: one_to_one
                        id: app_network_info
                        items:
                          - id: nce_north_ip
                            type: object
                            field:
                              interface:
                                type: string
                                widget: CustomTextField
                                value: eth0
                                props:
                                  editable: '{{dslId(network_planning)}}'
                                  uis_interacts:
                                    - type: onBlur
                                      func: onBatchModify
                                      target:
                                        path0: '{{path($.lld.lldConfigList.0.details.app_network_information.configDetail.1.interface)}}'
                                        path1: '{{path($.lld.lldConfigList.0.details.node_admission_information.configDetail.0.interface.1)}}'
                                        path2: '{{path($.lld.lldConfigList.0.details.node_admission_information.configDetail.1.interface.1)}}'
                                        path3: '{{path($.lld.lldConfigList.0.details.node_admission_information.configDetail.2.interface.1)}}'
                              network_usage:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: NorthBound
                                  uis_en: NorthBound
                                props:
                                  helpTips:
                                    tipContent:
                                      uis_zh: 请填写External Plane未被使用的IP地址
                                      uis_en: Enter the IP address of the external plane that is not in use
                              ip_version:
                                type: string
                                widget: LabelField
                                value: IPV4
                              ipaddress:
                                type: string
                                widget: CustomIPInput
                                required: true
                                props:
                                  editable: '{{dslId(network_planning)}}'
                                  uis_interacts:
                                    - type: onBlur
                                      func: onBatchModify
                                      target:
                                        path0: '{{path($.lld.lldConfigList.0.details.app_network_information.configDetail.0.ipaddress)}}'
                                        path1: '{{path($.lld.lldConfigList.0.details.app_network_information.uniep_login_ip)}}'
                                        path2: '{{path($.lld.lldConfigList.0.details.app_network_information.dv_login_ip)}}'
                              netmask:
                                  type: string
                                  widget: SubnetMask
                                  required: true
                                  props:
                                    editable: '{{dslId(network_planning)}}'
                              des_network:
                                  type: string
                                  widget: CustomIPInput
                                  value: 0.0.0.0
                                  props:
                                    editable: '{{dslId(network_planning)}}'
                                    disabled: true
                              des_mask:
                                  type: string
                                  widget: CustomIPInput
                                  value: 0.0.0.0
                                  props:
                                    editable: '{{dslId(network_planning)}}'
                                    disabled: true
                              gateway:
                                  type: string
                                  widget: CustomIPInput
                                  required: true
                                  props:
                                    editable: '{{dslId(network_planning)}}'
                      uniep_login_ip:
                        id: uniep_login_ip
                        type: string
                        display: false
                        props:
                          uis_interacts:
                            - type: onBlur
                              func: onBatchModify
                              target:
                                path0: '{{path($.lld.lldConfigList.0.details.app_network_information.configDetail.0.ipaddress)}}'
                                path1: '{{path($.lld.lldConfigList.0.details.app_network_information.uniep_login_ip)}}'
                                path2: '{{path($.lld.lldConfigList.0.details.app_network_information.dv_login_ip)}}'
                      dv_login_ip:
                        id: dv_login_ip
                        type: string
                        display: false
                        props:
                          uis_interacts:
                            - type: onBlur
                              func: onBatchModify
                              target:
                                path0: '{{path($.lld.lldConfigList.0.details.app_network_information.configDetail.0.ipaddress)}}'
                                path1: '{{path($.lld.lldConfigList.0.details.app_network_information.uniep_login_ip)}}'
                                path2: '{{path($.lld.lldConfigList.0.details.app_network_information.dv_login_ip)}}'


          - type: object
            title:
              uis_zh: 系统配置
              uis_en: System Configuration
            field:
              basicInfo:
                type: object
                widget: RightAlignLayout
                field:
                  statInfo:
                    widget: CustomParamInfo
                    enable: false
                    props:
                      paramInfo:
                        type: formDetails
                        func: uis_getFormDetails
                        params:
                          path: '{{modelPath(#.#)}}'
                      uis_interacts:
                        - type: onRequiredClick
                          func: uis_onFocusForm
                          params:
                            type: required
                            path: '{{path(#.#.details)}}'
                        - type: onToBeFilledClick
                          func: uis_onFocusForm
                          params:
                            type: toBeFilled
                            path: '{{path(#.#.details)}}'
                        - type: onErrorClick
                          func: uis_onFocusForm
                          params:
                            type: error
                            path: '{{path(#.#.details)}}'
                  editable:
                    type: boolean
                    widget: EditableButton
                    staticValue: false
                    id: edit_product
                    props:
                      uis_interacts:
                        - type: onClick
                          func: onClickEditableButton
                          params:
                            value: '{{@}}'
                          target:
                            value: '{{path(@)}}'
                      disabled: '{{$.editMasterButton}}'
              details:
                type: object
                field:
                  system_config:
                    type: object
                    title:
                      uis_zh: 系统配置
                      uis_en: System Configuration
                    field:
                      configDetail:
                        type: array
                        widget: Table
                        header:
                          configurationItem:
                            title:
                              uis_zh: 配置项
                              uis_en: Configuration Item
                          planValue:
                            title:
                              uis_zh: 规划值
                              uis_en: Planned Value
                        items:
                          - id: system
                            type: object
                            field:
                              configurationItem:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 语言
                                    uis_en: Language
                              planValue:
                                id: language
                                type: string
                                widget: CustomSelect
                                required: true
                                value: zh
                                options:
                                  - text:
                                      uis_zh: 中文
                                      uis_en: zh
                                    value: zh
                                  - text:
                                      uis_zh: 英文
                                      uis_en: en
                                    value: en
                                props:
                                  editable: '{{dslId(edit_product)}}'
                          - id: NetworkType
                            type: object
                            field:
                              configurationItem:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 组网
                                    uis_en: NetworkType
                              planValue:
                                id: NetworkType
                                type: string
                                widget: CustomTextField
                                required: true
                                value: 'M'
                                props:
                                  editable: '{{dslId(edit_product)}}'
                          - type: object
                            field:
                              configurationItem:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 是否安装iCnfg特性。
                                    uis_en: IS_INSTALL_ICNFG
                              help_info:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: 是否安装iCnfg特性
                                  uis_en: Whether to install the iCnfg feature.
                              planValue:
                                id: IS_INSTALL_ICNFG
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "Yes"
                                options:
                                  - value: "No"
                                    text:
                                      uis_zh: 否
                                      uis_en: "No"
                                  - value: "Yes"
                                    text:
                                      uis_zh: 是
                                      uis_en: "Yes" 
                          - type: object
                            field:
                              configurationItem:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 统一密码设置
                                    uis_en: is_unified_pwd
                              help_info:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: 是否使用统一密码
                                  uis_en: Whether to use the unified password
                              planValue:
                                id: is_unified_pwd
                                type: string
                                widget: CustomSelect
                                required: true
                                value: 2
                                options:
                                  - value: 1
                                    text:
                                      uis_zh: 否
                                      uis_en: "No"
                                  - value: 2
                                    text:
                                      uis_zh: 是
                                      uis_en: "Yes" 
                          - type: object
                            field:
                              configurationItem:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 是否与DF和服务合设
                                    uis_en: is_integrated_df_and_service
                              help_info:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: is_integrated_df_and_service
                                  uis_en: is_integrated_df_and_service
                              planValue:
                                id: is_integrated_df_and_service
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "No"
                                options:
                                  - value: "No"
                                    text:
                                      uis_zh: 否
                                      uis_en: "No"
                                  - value: "Yes"
                                    text:
                                      uis_zh: 是
                                      uis_en: "Yes" 
                          - type: object
                            field:
                              configurationItem:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: K8S IP
                                    uis_en: K8S IP Address
                              help_info:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: K8S IP
                                  uis_en: K8S IP Address
                              planValue:
                                id: k8s_floating_address
                                type: string
                                widget: CustomIPInput
                                required: true
                  disk_config:
                    type: object
                    title:
                      uis_zh: 磁盘
                      uis_en: Disk Configuration
                    field:
                      configDetail:
                        type: array
                        widget: Table
                        header:
                          configurationItem:
                            title:
                              uis_zh: 磁盘名称
                              uis_en: disk name
                          planValue:
                            title:
                              uis_zh: 分区名
                              uis_en: part name
                        items:
                          - id: extenddisk
                            type: object
                            field:
                              configurationItem:
                                id: diskname
                                type: string
                                widget: LabelField
                                value: extenddisk
                              planValue:
                                id: partname
                                type: string
                                widget: CustomTextField
                                required: true
                                value:
                                props:
                                  editable: '{{dslId(edit_product)}}'
                          - id: localpvdisk
                            type: object
                            field:
                              configurationItem:
                                id: diskname
                                type: string
                                widget: LabelField
                                value: localpvdisk
                              planValue:
                                id: partname
                                type: string
                                widget: CustomTextField
                                required: true
                                value:
                                props:
                                  editable: '{{dslId(edit_product)}}'
                          - id: thinpooldisk
                            type: object
                            display: "{{dslId(is_integrated_df_and_service) == 'No'}}"
                            field:
                              configurationItem:
                                id: diskname
                                type: string
                                widget: LabelField
                                value: image_disk
                              planValue:
                                id: partname
                                type: string
                                widget: CustomTextField
                                required: true
                                value:
                                props:
                                  editable: '{{dslId(edit_product)}}'
                  pv_config:
                    type: object
                    title:
                      uis_zh: 持久卷规划
                      uis_en: Persistent Volume Configuration
                    field:
                      configDetail:
                        type: array
                        widget: Table
                        header:
                          configurationItem:
                            title:
                              uis_zh: 持久卷名称
                              uis_en: disk name
                          planValue:
                            title:
                              uis_zh: 规划大小（默认单位：G）
                              uis_en: part name(default unit:G)
                        items:
                          - type: object
                            field:
                              configurationItem:
                                type: string
                                widget: LabelField
                                value: zenith-cloudsopdbsvr
                              planValue:
                                id: cloudsopdbsvr
                                type: number
                                greaterThan: 50
                                required: true
                                value: 350
                                props:
                                  editable: '{{dslId(edit_product)}}'
                          - type: object
                            field:
                              configurationItem:
                                type: string
                                widget: LabelField
                                value: es-cmp-storage
                              planValue:
                                id: esstorage
                                type: number
                                greaterThan: 50
                                required: true
                                value: 200
                                props:
                                  editable: '{{dslId(edit_product)}}'
                          - type: object
                            field:
                              configurationItem:
                                type: string
                                widget: LabelField
                                value: data-dvkafkaservice
                              planValue:
                                id: dvkafkaservice
                                type: number
                                greaterThan: 50
                                required: true
                                value: 200
                                props:
                                  editable: '{{dslId(edit_product)}}'
                          - type: object
                            field:
                              configurationItem:
                                type: string
                                widget: LabelField
                                value: data-digitalview-hofs
                              planValue:
                                id: hofs_size
                                type: number
                                greaterThan: 50
                                required: true
                                value: 150
                                props:
                                  editable: '{{dslId(edit_product)}}'
                          - type: object
                            field:
                              configurationItem:
                                type: string
                                widget: LabelField
                                value: messageBrokerStorageSize
                              planValue:
                                id: messageBrokerStorageSize
                                type: number
                                greaterThan: 50
                                required: true
                                value: 150
                                props:
                                  editable: '{{dslId(edit_product)}}'

                  password_config:
                    type: object
                    title:
                      uis_zh: 密码规划
                      uis_en: Configuration for product installation
                    field:
                      configDetail:
                        type: array
                        widget: Table
                        header:
                          user_category:
                            title:
                              uis_zh: 用户分类
                              uis_en: User Category
                          user_name:
                            title:
                              uis_zh: 密码参数名称
                              uis_en: Password Parameter Name
                          password:
                            title:
                              uis_zh: 密码
                              uis_en: Password
                          describe:
                            title:
                              uis_zh: 说明
                              uis_en: Declaration
                        items:
                          - id: password
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 操作系统用户
                                    uis_en: Operating system user
                              user_name:
                                id: caas_root_user
                                type: string
                                widget: LabelField
                                value: root
                              password:
                                id: caas_root_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: DigitalView节点root用户密码。
                                    uis_en: Password of the root user of DigitalView node.
                          - type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 操作系统用户
                                    uis_en: Operating system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: k8s_root
                              password:
                                id: k8s_root
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: k8s浮动IP节点root密码
                                    uis_en: Password of user root for logging in to the Kubernetes floating IP node
                          - type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 操作系统用户
                                    uis_en: Operating system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: k8s_paas
                              password:
                                id: paas_password
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: k8s用户paas的密码
                                    uis_en: Password of the k8s user paas
                          - type: object
                            display: "{{dslId(is_unified_pwd) == 2}}"
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 统一密码
                                    uis_en: Unified password
                              user_name:
                                type: string
                                widget: LabelField
                                value: DV_UNIFIED_PWD
                              password:
                                id: DV_UNIFIED_PWD
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 统一密码。
                                    uis_en: Unified password
                          - type: object
                            display: "{{dslId(is_unified_pwd) == 1}}"
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                id: ossadm
                                type: string
                                widget: LabelField
                                value: ossadm
                              password:
                                id: ossadm_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: dvengineeringservice容器内部ossadm、devdata用户密码。
                                    uis_en: Passwords of the ossadm and devdata users in the dvengineeringservice container.
                          - type: object
                            display: "{{dslId(is_unified_pwd) == 1}}"
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                id: DV_CERT_DRIVER_user
                                type: string
                                widget: LabelField
                                value: DV_CERT_DRIVER
                              password:
                                id: DV_CERT_DRIVER_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 系统监控和UniAgent间证书管理接口的鉴权用户。
                                    uis_en: Authentication user for certificate management APIs between system monitoring and the UniAgent.
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: paasinter
                              password:
                                id: paasinter_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: UTM后台Basic鉴权用户。
                                    uis_en: Basic authentication user on the UTM backend.
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: I2kNfv
                              password:
                                id: I2kNfv_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 用于VNFM对接系统监控。安装VNFM适配包后存在该用户。
                                    uis_en: User for the VNFM to interconnect with system monitoring. This user exists after the VNFM adaptation package is installed.
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: cie_snmp
                              password:
                                id: cie_snmp_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 解析物理设备上报的snmp v3 trap告警时使用的默认鉴权用户trapuser。
                                    uis_en: Default authentication user trapuser, who parses SNMPv3 trap alarms reported by physical devices.
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: sftp_admin
                              password:
                                id: sftp_admin_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 南向FTPS/SFTP用户admin，用于DigitalView和网元之间传输文件。
                                    uis_en: Southbound FTPS/SFTP user admin, who transfers files between the DigitalView and NEs.
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户 
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: sftp_nbiuser
                              password:
                                id: sftp_nbiuser_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 北向FTPS/SFTP用户nbiuser，上级网管通过该用户连接DigitalView，获取北向文件。
                                    uis_en: Northbound FTPS/SFTP user nbiuser for the upper-layer NMS to connect to the DigitalView to obtain northbound files.
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: sftp_mmluser
                              password:
                                id: sftp_mmluser_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: MML的ftps/sftp用户。维护助手通过该用户连接DigitalView MML，上传restful巡检命令结果。
                                    uis_en: FTPS/SFTP user of the MML. The MainAst uses this user to connect to the DigitalView MML and upload RESTful inspection command results.
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: sftp_assetuser
                              password:
                                id: sftp_assetuser_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 北向FTPS/SFTP用户assetuser，上级网管通过该用户连接DigitalView，获取北向文件。
                                    uis_en: Northbound FTPS/SFTP user assetuser for the upper-layer NMS to connect to the DigitalView to obtain northbound files.
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user DigitalView
                              user_name:
                                type: string
                                widget: LabelField
                                value: cs_service
                              password:
                                id: cs_service_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: CS Service与DigitalView通信的机机用户。
                                    uis_en: Machine-machine user for the CS Service to communicate with the DigitalView.
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: tool_mainast
                              password:
                                id: tool_mainast_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 机机账号，用于DigitalView和iCheck工具通信。
                                    uis_en: Machine-machine account for communication between the DigitalView and iCheck.
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: tool_dms
                              password:
                                id: tool_dms_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 机机账号，分别用于DigitalView和DataManagementService通信，DigitalView和DataAccessService通信以及用于DigitalView和EasyMS工具通信。
                                    uis_en: Machine-machine accounts for communication between the DigitalView and DataManagementService, between the DigitalView and DataAccessService, and between the DigitalView and EasyMS, respectively.
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: tool_das
                              password:
                                id: tool_das_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 机机账号，分别用于DigitalView和DataManagementService通信，DigitalView和DataAccessService通信以及用于DigitalView和EasyMS工具通信。
                                    uis_en: Machine-machine accounts for communication between the DigitalView and DataManagementService, between the DigitalView and DataAccessService, and between the DigitalView and EasyMS, respectively.
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: tool_ideploy
                              password:
                                id: tool_ideploy_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 机机账号，用于DigitalView和iDeploy工具通信。
                                    uis_en: Machine-machine account for communication between the DigitalView and iDeploy.    
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: tool_easyms
                              password:
                                id: tool_easyms_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 机机账号，分别用于DigitalView和DataManagementService通信，DigitalView和DataAccessService通信以及用于DigitalView和EasyMS工具通信。
                                    uis_en: Machine-machine accounts for communication between the DigitalView and DataManagementService, between the DigitalView and DataAccessService, and between the DigitalView and EasyMS, respectively.      
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: dms_db
                              password:
                                id: dms_db_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: DataManagementService和DigitalView高斯数据库通信用户dmsdb的密码。
                                    uis_en: Password of the dmsdb user for communication between DataManagementService and the DigitalView GaussDB.  
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: easyms_db
                              password:
                                id: easyms_db_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: EasyMS和DigitalView高斯数据库通信用户easymsdb的密码。
                                    uis_en: Password of the easymsdb user for communication between EasyMS and the DigitalView GaussDB.  
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户
                                    uis_en: Application system user 
                              user_name:
                                type: string
                                widget: LabelField
                                value: csweb_db
                              password:
                                id: csweb_db_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: CS Service的数据库用户cswebdb。
                                    uis_en: CS Service database user cswebdb.  
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户 
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: pmdashboard_db
                              password:
                                id: pmdashboard_db_pwd
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 监控大盘连接DigitalView数据库通信用户pmdashboarddb。
                                    uis_en: pmdashboarddb user for the Monitoring Dashboard to connect to the DigitalView database. 
                          - display: "{{dslId(is_unified_pwd) == 1}}"
                            type: object
                            field:
                              user_category:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: 应用系统用户 
                                    uis_en: Application system user
                              user_name:
                                type: string
                                widget: LabelField
                                value: CM_BASIC_AUTH_PWD
                              password:
                                id: CM_BASIC_AUTH_PWD
                                type: string
                                widget: CustomPassword
                                required: true
                                props:
                                  canPasswordPaste: true
                                  editable: '{{dslId(edit_product)}}'
                              describe:
                                type: string
                                widget: LabelField
                                props:
                                  label:
                                    uis_zh: CloudMonitor认证密码。
                                    uis_en: CloudMonitor authentication password. 
          - type: object
            title:
              uis_zh: 部署参数
              uis_en: Deployment Parameters
            field:
              basicInfo:
                type: object
                widget: RightAlignLayout
                field:
                  statInfo:
                    widget: CustomParamInfo
                    enable: false
                    props:
                      paramInfo:
                        type: formDetails
                        func: uis_getFormDetails
                        params:
                          path: '{{modelPath(#.#)}}'
                      uis_interacts:
                        - type: onRequiredClick
                          func: uis_onFocusForm
                          params:
                            type: required
                            path: '$.lld.lldConfigList.0.details'
                        - type: onToBeFilledClick
                          func: uis_onFocusForm
                          params:
                            type: toBeFilled
                            path: '$.lld.lldConfigList.0.details'
                        - type: onErrorClick
                          func: uis_onFocusForm
                          params:
                            type: error
                            path: '$.lld.lldConfigList.0.details'
                  editable:
                    type: boolean
                    widget: EditableButton
                    staticValue: false
                    id: deployment_parameters
                    props:
                      uis_interacts:
                        - type: onClick
                          func: onClickEditableButton
                          params:
                            value: '{{@}}'
                          target:
                            value: '{{path(@)}}'
                      disabled: '{{$.editMasterButton}}'
              details:
                type: object
                field:
                  features_information:
                    type: object
                    title:
                      uis_zh: 特性
                      uis_en: Node Admission Information
                    field:
                      features:
                        type: string
                        widget: CustomMultiSelect
                        id: features
                        required: true
                        title:
                          uis_zh: 特性列表
                          uis_en: Features list
                        value: [ 'sysMonitor', 'DVPM', 'DVCertService', 'DVITManagement' ]
                        data:
                          - value: sysMonitor
                            text:
                              uis_zh: '系统监控'
                              uis_en: 'sysMonitor'
                            tipText:
                              uis_zh: '系统监控'
                              uis_en: 'SysMonitor Feature'
                          - value: DVPM
                            text:
                              uis_zh: '性能服务'
                              uis_en: 'DVPM'
                            tipText:
                              uis_zh: '性能服务'
                              uis_en: 'Performance Monitor Service'
                          - value: DVCertService
                            text:
                              uis_zh: '网元证书管理'
                              uis_en: 'DVCertService'
                            tipText:
                              uis_zh: '网元证书管理'
                              uis_en: 'NE Cert Management'
                          - value: DVITManagement
                            text:
                              uis_zh: '集中任务管理'
                              uis_en: 'DVITManagement'
                            tipText:
                              uis_zh: '集中任务管理'
                              uis_en: 'Integrated Task Management'
                          - value: DVMaintenance
                            text:
                              uis_zh: 'MML 服务'
                              uis_en: 'DVMaintenance'
                            tipText:
                              uis_zh: 'MML 服务'
                              uis_en: 'MML Service'
                          - value: DV4AService
                            text:
                              uis_zh: '4A对接服务'
                              uis_en: 'DV4AService'
                            tipText:
                              uis_zh: '4A对接服务'
                              uis_en: '4A Access Service'
                          - value: OM_LogtraceService
                            text:
                              uis_zh: '运维分析-服务跟踪'
                              uis_en: 'OM_LogtraceService'
                            tipText:
                              uis_zh: '运维分析-服务跟踪'
                              uis_en: 'OM_LogtraceService Feature'
                          - value: OM_CallchainService
                            text:
                              uis_zh: '运维分析-调用链'
                              uis_en: 'OM_CallchainService Feature'
                            tipText:
                              uis_zh: '运维分析-调用链'
                              uis_en: 'OM_CallchainService'
                          - value: OM_CatService
                            text:
                              uis_zh: '运维分析-业务拨测'
                              uis_en: 'OM_CatService'
                            tipText:
                              uis_zh: '运维分析-业务拨测'
                              uis_en: 'OM_CatService Feature'
                          - value: OM_LogmatrixService
                            text:
                              uis_zh: '运维分析-日志服务'
                              uis_en: 'OM_LogmatrixService'
                            tipText:
                              uis_zh: '运维分析-日志服务'
                              uis_en: 'OM_LogmatrixService Feature'
                          - value: SA_WorkbenchService
                            text:
                              uis_zh: '业务保障-作业控制台'
                              uis_en: 'SA_WorkBenchService'
                            tipText:
                              uis_zh: '业务保障-作业控制台'
                              uis_en: 'SA_WorkBenchService Feature'
                          - value: SA_IhealingService
                            text:
                              uis_zh: '业务保障-流程编排'
                              uis_en: 'SA_FlowOrchestration'
                            tipText:
                              uis_zh: '业务保障-流程编排'
                              uis_en: 'SA_FlowOrchestration Feature'
                          - value: SA_InfocollectService
                            text:
                              uis_zh: '业务保障-故障信息收集'
                              uis_en: 'SA_InfocollectService'
                            tipText:
                              uis_zh: '业务保障-故障信息收集'
                              uis_en: 'SA_InfocollectService Feature'
                          - value: PMDashBoard
                            text:
                              uis_zh: '监控大盘'
                              uis_en: 'PMDashBoard'
                            tipText:
                              uis_zh: '监控大盘'
                              uis_en: 'PMDashBoard Feature'
                          - value: DVSaaSOps
                            text:
                              uis_zh: '云原生监控'
                              uis_en: 'Cloud Monitor'
                            tipText:
                              uis_zh: '云原生监控'
                              uis_en: 'Cloud Monitor'
                          - value: AnalysisEngine
                            text:
                              uis_zh: '智能运维'
                              uis_en: 'AIOps Service'
                            tipText:
                              uis_zh: '智能运维'
                              uis_en: 'AIOps Service'
                      feature_list:
                        type: string
                        widget: LabelField
                        id: feature_list
                        display: false
                        value: "sysMonitor,DVPM,DVCertService,DVITManagement,DVMaintenance,DV4AService,OM_LogtraceService,OM_CallchainService,OM_CatService,OM_LogmatrixService,SA_WorkbenchService,SA_IhealingService,SA_InfocollectService,PMDashBoard,DVSaaSOps,AnalysisEngine,DVMediation" 
                  configurationItemList:
                    type: object
                    title:
                      uis_zh: 配置项列表
                      uis_en: Configuration Item List
                    field:
                      not_required_list_items:
                        type: string
                        widget: LabelField
                        id: not_required_list_items
                        display: false
                        value: "AUTH,ssoServerUrl,ssoBackupServerUrl,configUnisessionSessionKey" 
                      configDetail:
                        type: array
                        widget: Table
                        header:
                          parameter_item:
                            title:
                              uis_zh: 配置项
                              uis_en: Configuration Item
                            width: 20%
                          plan_value:
                            title:
                              uis_zh: 规划值
                              uis_en: Planned Value
                            width: 20%
                          description:
                            title:
                              uis_zh: 配置项说明
                              uis_en: Configuration Item Description
                        items:
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: enableSecondaryAuthentication
                                  uis_en: enableSecondaryAuthentication
                              plan_value:
                                id: enableSecondaryAuthentication
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "true"
                                options:
                                  - value: "true"
                                  - value: "false"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【true, false】，默认true，代表启用二次认证能力。当网管作为SSO客户端，且没有短信、邮件通知能力时请选false。"
                                  uis_en: "The value range is [true, false]. The default value is true, indicating that enble using secondary authentication. Select false when the NMS is the SSO client and has no SMS or email notification capabilities."
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: IS_ENABLE_REVIEW_ABILITY
                                  uis_en: IS_ENABLE_REVIEW_ABILITY
                              plan_value:
                                id: IS_ENABLE_REVIEW_ABILITY
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "true"
                                options:
                                  - value: "true"
                                  - value: "false"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【true, false】，默认true，代表启用审核能力。可控制功能范围为【告警定制，性能自定义指标，作业控制台，流程编排】。"
                                  uis_en: "The value range is [true, false]. The default value is true, indicating that enble using review ability. The range of controllable functions are alarm customization, custom indicator of performance, workbench and ihealing."
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: enableNetworkElementLargeCapacity
                                  uis_en: enableNetworkElementLargeCapacity
                              plan_value:
                                id: enableNetworkElementLargeCapacity
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "false"
                                options:
                                  - value: "false"
                                  - value: "true"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: |
                                    网元大容量开关：
                                    •false，默认不开启。
                                    •true，当DigitalView管理主机（含物理主机和虚拟机）超过200台时需要设置为“true”。
                                  uis_en: |
                                    Large-capacity NE switch:
                                    •false: disabled by default.
                                    •true: Set this parameter to true when the number of DigitalView management hosts (including physical hosts and VMs) exceeds 200.
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: NFV_MODE
                                  uis_en: NFV_MODE
                              plan_value:
                                id: NFV_MODE
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "OFF"
                                options:
                                  - value: "ON"
                                  - value: "OFF"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【OFF，ON】，默认为OFF，仅在中国运营商NFV场景需要设置为ON。该参数在仅工具场景无需修改"
                                  uis_en: "The value range is [OFF, ON]. The default value is OFF. Set this parameter to ON only in Carriers in China NFV scenarios.This parameter does not need to be modified in tool scenarios."
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: detailed_log_printing_mode
                                  uis_en: detailed_log_printing_mode
                              plan_value:
                                id: detailed_log_printing_mode
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "No"
                                options:
                                  - value: "No"
                                  - value: "Yes"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【No, Yes】，默认No。如果要开启日志详细打印模式，需要选择Yes"
                                  uis_en: "The value range is [No, Yes] . The default value is No. To enable the detailed log printing mode, select Yes."
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: dv_deploy_scale_size
                                  uis_en: dv_deploy_scale_size
                              plan_value:
                                id: dv_deploy_scale_size
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "default"
                                options:
                                  - value: "default"
                                  - value: "small"
                                  - value: "venus"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【default, small, venus】，默认default。在小型化和轻量化场景下需要配置为small，在Venus场景下选venus，在其他场景中均保持default默认"
                                  uis_en: "The value range is [default, small, venus] . The default value is default. In mini mode and lite mode set this parameter to small.In Venus set this parameter to venus. In other scenarios, retain the default value."
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: isLiteMonitor
                                  uis_en: isLiteMonitor
                              plan_value:
                                id: isLiteMonitor
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "false"
                                options:
                                  - value: "true"
                                  - value: "false"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【false，true】，默认false，代表全量系统监控特性，适用于纯DigitalView安装和现代化网管A方案。true代表轻量化系统监控特性，适用于轻量化DigitalView和现代化网管B方案。该参数在仅工具场景不生效。"
                                  uis_en: "The value range is [false, true]. The default value is false, indicating the full system monitoring feature. This value is applicable to the DigitalView installation and modernized NMS A solution. true indicates the lightweight system monitoring feature, which is applicable to the lightweight DigitalView and modernized NMS B solution.This parameter does not take effect only in tool scenarios."
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: enableAccountNonRootMode
                                  uis_en: enableAccountNonRootMode
                              plan_value:
                                id: enableAccountNonRootMode
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "false"
                                options:
                                  - value: "false"
                                  - value: "true"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【false，true】，默认false，是否开启帐号管理支持非root模式。"
                                  uis_en: "The value range is [false, true]. The default value is false. Indicates whether to enable account management in non-root mode."
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: i2k_interface_password_secure_mode
                                  uis_en: i2k_interface_password_secure_mode
                              plan_value:
                                id: i2k_interface_password_secure_mode
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "long_password"
                                options:
                                  - value: "long_password"
                                  - value: "compatible"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【long_password，compatible】，默认long_password，代表网管和代理对接的密码模式为长密码安全模式，适用于新安装场景；compatible代表兼容模式，适用于对接老版本的代理，查看方式：登录代理查看$BMU_HOME/uninstall/bmu.cfg的interface_password_secure_mode配置项，和该配置项保持一致，如果没有该配置项，需要选择为兼容模式。该参数在仅工具场景无需修改。"
                                  uis_en: "The value range is [long_password, compatible]. The default value is long_password, indicating that the password mode for the interconnection between the OSS and agent is long password security mode. This value applies to new installation scenarios. compatible indicates the compatible mode, which is applicable to the proxy of the old version. To check the compatible mode, log in to the proxy and check the interface_password_secure_mode configuration item in $BMU_HOME/uninstall/bmu.cfg. If the configuration item does not exist, select the compatible mode.This parameter does not need to be modified in tool scenarios."
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: topology_scale
                                  uis_en: topology_scale
                              plan_value:
                                id: topology_scale
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "systemScale"
                                options:
                                  - value: "systemScale"
                                  - value: "medium"
                                  - value: "large"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "拓扑服务内存规格，取值范围【systemScale，medium，large】."
                                  uis_en: "Topology service memory specification，The value range is [systemScale，medium，large]."
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: use_forwarded_ip
                                  uis_en: use_forwarded_ip
                              plan_value:
                                id: use_forwarded_ip
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "false"
                                options:
                                  - value: "false"
                                  - value: "true"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "前端通过代理访问DV时，需要设置为TRUE，开启后使用X-Forwarded-For获取客户端IP。"
                                  uis_en: "Need to set it TRUE when accesses DigitalView through a proxy. Then X-Forwarded-For is used to obtain the client IP address."
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: currentAlarm
                                  uis_en: currentAlarm
                              plan_value:
                                id: currentAlarm
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "20000"
                                options:
                                  - value: "20000"
                                  - value: "50000"
                                  - value: "100000"
                                  - value: "200000"
                                  - value: "300000"
                                  - value: "500000"
                                  - value: "1000000"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "告警范围仅支持7个值[20000,50000,100000,200000,300000,500000,1000000]，默认20000，代表当前告警容量上限20000，如果取值为1000000则代表当前告警容量上限为最高的1000000，提高参数的同时需要定制化提升appnode节点内存。该参数在仅工具场景不生效。"
                                  uis_en: "Value range is [20000,50000,100000,200000,300000,500000,1000000]. The default value is 20000, indicating that the maximum number of current alarms is 20000. The value 1000000 indicates that the maximum number of current alarms is 1000000. The memory of the AppNode needs to be increased.This parameter does not take effect only in tool scenarios."
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: lucene
                                  uis_en: lucene
                              plan_value:
                                id: lucene
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "true"
                                options:
                                  - value: "true"
                                  - value: "false"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "历史告警查询是否启动Lucene查询"
                                  uis_en: "Whether to enable Lucene query for historical alarms. Options: true: Enable. false: Disable."
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: enableSaasMode
                                  uis_en: enableSaasMode
                              plan_value:
                                id: enableSaasMode
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "false"
                                options:
                                  - value: "true"
                                  - value: "false"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【false，true】，默认false，是否开启运维多租。"
                                  uis_en: "The value range is [false, true]. The default value is false. Indicates whether to enable multi-tenancy." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: APIGatewayRegisterMode
                                  uis_en: APIGatewayRegisterMode
                              plan_value:
                                id: APIGatewayRegisterMode
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "synchrono"
                                options:
                                  - value: "synchrono"
                                  - value: "asynchrono"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【synchrono,asynchrono】，默认synchrono。API网关注册模式，仅用于OMC场景。"
                                  uis_en: "The value range is [synchrono,asynchrono]. The default value is synchrono. API gateway register mode, which is only for OMC scenarios." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: loadBalancingAndFaultTolerantMode
                                  uis_en: loadBalancingAndFaultTolerantMode
                              plan_value:
                                id: loadBalancingAndFaultTolerantMode
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "inTurn"
                                options:
                                  - value: "inTurn"
                                  - value: "random"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【random,inTurn】，默认inTurn。负载均衡和容错模式，仅用于OMC场景。"
                                  uis_en: "The value range is [random,inTurn]. The default value is inTurn. Load balancing and fault tolerant mode, which is only for OMC scenarios." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: ServiceTracingMemory
                                  uis_en: ServiceTracingMemory
                              plan_value:
                                id: ServiceTracingMemory
                                type: number
                                required: true
                                value: 300
                                constraints:
                                  greaterThan:
                                    rule: 0
                                    message: 
                                      uis_zh: "输入值需大于0"
                                      uis_en: "The input value need greater then 0"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "服务跟踪内存配置。默认值300，单位M，仅用于OMC场景。"
                                  uis_en: "Service trace memory, which is only for OMC scenarios. The default value is 300, unit for M." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: messageDrivingServieMaxMemory
                                  uis_en: messageDrivingServieMaxMemory
                              plan_value:
                                id: messageDrivingServieMaxMemory
                                type: number
                                required: true
                                value: 100
                                constraints:
                                  greaterThan:
                                    rule: 0
                                    message: 
                                      uis_zh: "输入值需大于0"
                                      uis_en: "The input value need greater then 0"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "消息驱动服务内存配置。默认值100，单位M，仅用于OMC场景。"
                                  uis_en: "Message driver servcie max memory, which is only for OMC scenarios. The default value is 100, unit for M." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: SetingToolsMode
                                  uis_en: SetingToolsMode
                              plan_value:
                                id: SetingToolsMode
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "synchrono"
                                options:
                                  - value: "synchrono"
                                  - value: "asynchrono"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【synchrono,asynchrono】，默认synchrono。配置工具模式，仅用于OMC场景。"
                                  uis_en: "The value range is [synchrono,asynchrono]. The default value is synchrono. Setting tools mode, which is only for OMC scenarios." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: serviceMessageBusRegisterMode
                                  uis_en: serviceMessageBusRegisterMode
                              plan_value:
                                id: serviceMessageBusRegisterMode
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "synchrono"
                                options:
                                  - value: "synchrono"
                                  - value: "asynchrono"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【synchrono,asynchrono】，默认synchrono。服务和消息总线注册模式，仅用于OMC场景。"
                                  uis_en: "The value range is [synchrono,asynchrono]. The default value is synchrono. Service and message bus register mode, which is only for OMC scenarios." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: dv_lite_mode
                                  uis_en: dv_lite_mode
                              plan_value:
                                id: dv_lite_mode
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "FALSE"
                                options:
                                  - value: "TRUE"
                                  - value: "FALSE"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【TRUE, FALSE】，默认FALSE。非轻量化场景需要保持FALSE"
                                  uis_en: "The value range is [TRUE, FALSE], The default value is FALSE. In non-light scenarios, set this parameter to FALSE." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: isAppNbiIpExtend
                                  uis_en: isAppNbiIpExtend
                              plan_value:
                                id: isAppNbiIpExtend
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "false"
                                options:
                                  - value: "true"
                                  - value: "false"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "是否扩展北向接口IP"
                                  uis_en: "Whether extension ip for north interface" 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: isAppNfvIpExtend
                                  uis_en: isAppNfvIpExtend
                              plan_value:
                                id: isAppNfvIpExtend
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "false"
                                options:
                                  - value: "true"
                                  - value: "false"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "是否扩展NFV接口IP"
                                  uis_en: "Whether to configure app ip extension for nfv interface" 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: configUnisessionAuthMode
                                  uis_en: configUnisessionAuthMode
                              plan_value:
                                id: configUnisessionAuthMode
                                type: string
                                widget: CustomSelect
                                required: false
                                value: ""
                                options:
                                  - value: ""
                                  - value: "thirdpart"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "认证模式，取值范围【thirdpart，空】，默认为空。如果是第三方认证，请选择thirdpart。"
                                  uis_en: "Authentication Mode, the value range is [thirdpart, null]. The default value is null. If the authentication is third part, please choose thirdpart." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: AUTH
                                  uis_en: AUTH
                              plan_value:
                                id: AUTH
                                type: string
                                required: false
                                value: ""
                                constraints:
                                  pattern: 
                                    rule: '^[0-9a-zA-Z@_:/.\[\]-]*$'
                                    message: 
                                      uis_zh: "请输入合法的参数"
                                      uis_en: "Please enter valid parameter value"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "DigitalView SSO定制，默认为空。"
                                  uis_en: "DigitalView SSO Customization. The default value is null." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: configUnisessionSessionKey
                                  uis_en: configUnisessionSessionKey
                              plan_value:
                                id: configUnisessionSessionKey
                                type: string
                                widget: CustomSelect
                                required: false
                                value: ""
                                options:
                                  - value: ""
                                  - value: "bspsession"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "定制会话Cookie，取值范围【bspsession，空】，默认为空。如果选择bspsession，则会话cookie的key值为bspsession。"
                                  uis_en: "Session cookie customization, the value range is [bspsession, null]. The default value is null. The key value of session cookie will be bspsession, when choose bspsession." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: ssoServerUrl
                                  uis_en: ssoServerUrl
                              plan_value:
                                id: ssoServerUrl
                                type: string
                                required: false
                                value: ""
                                constraints:
                                  pattern: 
                                    rule: '^[0-9a-zA-Z@_:/.\[\]-]*$'
                                    message: 
                                      uis_zh: "请输入合法的参数"
                                      uis_en: "Please enter valid parameter value"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "第三方SSO服务器 URL，默认为空，非第三方SSO服务器不启用。"
                                  uis_en: "Third-party SSO server. The default value is null. Non-third-party SSO server does not start." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: ssoType
                                  uis_en: ssoType
                              plan_value:
                                id: ssoType
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "DEFAULT"
                                options:
                                  - value: "DEFAULT"
                                  - value: "VENUS"
                                  - value: "CBS_CAS"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【DEFAULT, VENUS, CBS_CAS】，默认DEFAULT。"
                                  uis_en: "The value range is [DEFAULT, VENUS,CBS_CAS] . The default value is DEFAULT." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: EMSMode
                                  uis_en: EMSMode
                              plan_value:
                                id: EMSMode
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "NA"
                                options:
                                  - value: "Central"
                                  - value: "Local"
                                  - value: "NA"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【Central, Local, NA】，默认NA。说明：Central:中心运维系统；Local:本地运维系统；NA:默认运维模式。设置为Central时，本DV生成的UUID值固定为1"
                                  uis_en: "The value range is [Central,Local,NA]. The default value is NA.Note: Central: central O&M system; Local: local O&M system; NA: default O&M mode. If this parameter is set to Central, the value of UUID generated by the DigitalView is fixed to 1." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: ssoBackupServerUrl
                                  uis_en: ssoBackupServerUrl
                              plan_value:
                                id: ssoBackupServerUrl
                                type: string
                                required: false
                                value: ""
                                constraints:
                                  pattern: 
                                    rule: '^[0-9a-zA-Z@_:/.\[\]-]*$'
                                    message: 
                                      uis_zh: "请输入合法的参数"
                                      uis_en: "Please enter valid parameter value"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "第三方SSO备份服务器 URL，默认为空，非第三方SSO服务器不启用。"
                                  uis_en: "Third-party BackUp SSO server. The default value is null. Non-third-party SSO server does not start." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: approveMaxClientBodySize
                                  uis_en: approveMaxClientBodySize
                              plan_value:
                                id: approveMaxClientBodySize
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "true"
                                options:
                                  - value: "true"
                                  - value: "false"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【false，true】，默认true。如果为true，最大请求体大小为2g；如果为false，则默认为2m。"
                                  uis_en: "The value range is [false, true]. The default value is true. The max client body size is 2g, when choose true. It's 2m when choose false." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: integrated_mode
                                  uis_en: integrated_mode
                              plan_value:
                                id: integrated_mode
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "DigitalFoundry-DigitalView-service separate deployment"
                                options:
                                  - value: "DigitalFoundry-service co-deployment"
                                  - value: "DigitalFoundry-DigitalView-service co-deployment"
                                  - value: "DigitalFoundry-DigitalView-service separate deployment"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【DF和业务主机合设,DF、DV和业务主机合设,DF、DV、业务主机分设】，默认DF、DV、业务主机分设。当DF和业务存在主机合设的时候，请选择“DF和业务主机合设”，DF、DV和业务存在主机合设时，请选择“DF、DV和业务主机合设”，主机合设的含义是共用同一个操作系统"
                                  uis_en: "The options are as follows【DigitalFoundry-service co-deployment,DigitalFoundry-DigitalView-service co-deployment and DigitalFoundry-DigitalView-service separate deployment】. By default, DigitalFoundry-DigitalView-service separate deployment， If the DigitalFoundry and services are co-deployed, select DigitalFoundry-service co-deployment. If the DigitalFoundry, DigitalView, and services are co-deployed, select DigitalFoundry-DigitalView-service co-deployment. Co-deployment indicates that the DigitalFoundry, DigitalView, services use the same operating system." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: Kafka_Retention_Time
                                  uis_en: Kafka_Retention_Time
                              plan_value:
                                id: Kafka_Retention_Time
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "Default"
                                options:
                                  - value: "Default"
                                  - value: "Short"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【Default, Short】, 性能kafka保留时间，默认是Default。手工修改此参数，需要升级才生效"
                                  uis_en: "The value range is [Default, Short], Performance kafka retention time, default is Default. Manually modify this parameter. The modification takes effect only after the upgrade." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: ER_TOTAL_LIMIT_CONN
                                  uis_en: ER_TOTAL_LIMIT_CONN
                              plan_value:
                                id: ER_TOTAL_LIMIT_CONN
                                type: number
                                required: true
                                value: 1000
                                constraints:
                                  greaterOrEqual:
                                    rule: 2
                                    message: 
                                      uis_zh: "取值范围【2, 30000】"
                                      uis_en: "The value range is [2, 30000]"
                                  lessOrEqual:
                                    rule: 30000
                                    message: 
                                      uis_zh: "取值范围【2, 30000】"
                                      uis_en: "The value range is [2, 30000]"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【2, 30000】，默认值为1000，控制ER并发连接总数"
                                  uis_en: "The value range is [2, 30000]. The default value is 1000. Total number of concurrent ER connections" 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: LIMIT_CONN_PERIP
                                  uis_en: LIMIT_CONN_PERIP
                              plan_value:
                                id: LIMIT_CONN_PERIP
                                type: number
                                required: true
                                value: 300
                                constraints:
                                  greaterOrEqual:
                                    rule: 2
                                    message: 
                                      uis_zh: "取值范围【2, 30000】"
                                      uis_en: "The value range is [2, 30000]"
                                  lessOrEqual:
                                    rule: 30000
                                    message: 
                                      uis_zh: "取值范围【2, 30000】"
                                      uis_en: "The value range is [2, 30000]"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【2, 30000】，默认值为300，控制ER同一地址的并发连接数"
                                  uis_en: "The value range is [2, 30000]. The default value is 300.Controls the number of concurrent connections to the same ER address." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: enableUOAConnection
                                  uis_en: enableUOAConnection
                              plan_value:
                                id: enableUOAConnection
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "false"
                                options:
                                  - value: "true"
                                  - value: "false"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【false, true】，默认false。当需要支持连接UOA管理网元时，请选择true。"
                                  uis_en: "The options are as follows [false, true]. By default, false. When need the connection to the UOA management ne, please choose true." 
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: smEnablePolicyAuth
                                  uis_en: smEnablePolicyAuth
                              plan_value:
                                id: smEnablePolicyAuth
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "false"
                                options:
                                  - value: "true"
                                  - value: "false"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "取值范围【false，true】，默认false，如果开启，需要保证configAuthDomainCustome的参数设置成'ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP'"
                                  uis_en: "The value range is [false, true]. The default value is false. If this function is enabled, ensure that configAuthDomainCustome is set to 'ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP'"
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: configAuthDomainCustome
                                  uis_en: configAuthDomainCustome
                              plan_value:
                                id: configAuthDomainCustome
                                type: string
                                widget: CustomSelect
                                required: true
                                value: "ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP"
                                options:
                                  - value: "ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP"
                                  - value: "DISABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP"
                                  - value: "ENABLE_ALL_RES,ENABLE_DEV_TYPE,ENABLE_SUBNET,ENABLE_DEV_SETS,ENABLE_DEV,DISABLE_RESGROUP,DISABLE_ALL_MNA_OBJ"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: |
                                    分组鉴权模式参数，若为分组鉴权模式，可开启界面的“资源 > 资源分组”菜单。
                                    •若“enableSaasMode”配置为开启，则此参数的配置如下所示，表示在多租场景下，创建角色时的管理对象仅支持资源分组。
                                    DISABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP
                                    •若“enableSaasMode”配置为关闭，则此参数的配置如下所示：
                                      ◾默认值表示为分组鉴权模式，如下所示：
                                      ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP
                                      ◾若需修改为非分组鉴权模式，则配置为：
                                      ENABLE_ALL_RES,ENABLE_DEV_TYPE,ENABLE_SUBNET,ENABLE_DEV_SETS,ENABLE_DEV,DISABLE_RESGROUP,DISABLE_ALL_MNA_OBJ

                                  uis_en: | 
                                    Group authentication mode parameter. If the group authentication mode is used, choose Resource > Resource Groups on the GUI.
                                    •If enableSaasMode is set to true, set this parameter as follows, indicates that only resource groups can be managed by a role in a multi-tenant scenario.
                                      DISABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP

                                    •If enableSaasMode is set to false, set this parameter as follows:
                                      ◾The default value indicates the group authentication mode. The default values are as follows:
                                      ENABLE_ALL_RES,DISABLE_SUBNET,DISABLE_DEV,ENABLE_RESGROUP

                                      ◾To change the authentication mode to non-group authentication, set the parameters as follows:
                                      ENABLE_ALL_RES,ENABLE_DEV_TYPE,ENABLE_SUBNET,ENABLE_DEV_SETS,ENABLE_DEV,DISABLE_RESGROUP,DISABLE_ALL_MNA_OBJ     
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: sandbox_escape
                                  uis_en: sandbox_escape
                              plan_value:
                                id: sandbox_escape
                                type: string
                                required: true
                                value: "false"
                                options:
                                  - value: "true"
                                  - value: "false"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: |
                                    取值范围【true/false】，默认值false，当取值为true时，自定义编程场景（告警定制，自定义性能，作业控制台，流程编排，故障信息收集）自定义任务不通过沙箱执行。
                                  uis_en: |
                                    The value range is [true/false]. The default value is false. When the value is true, the sandbox is not used to execute user-defined programming scenarios (alarm customization, user-defined performance, job console, process orchestration, and fault information collection).
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "assurance_escape"
                                  uis_en: assurance_escape
                              plan_value:
                                id: assurance_escape
                                type: string
                                required: true
                                value: "false"
                                options:
                                  - value: "true"
                                  - value: "false"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: |
                                    取值范围【true/false】，默认值false，主要满足中国区诉求，当参数为true时，流程编排、作业控制台和故障信息收集各特性前台页面可支持配置root用户。
                                  uis_en: |
                                    The options are true and false. The default value is false, which meets requirements in China. When this parameter is set to true, the root user can be configured on the foreground pages of the process orchestration, workbench, and fault information collection features.
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: "pkgmgr_escape"
                                  uis_en: pkgmgr_escape
                              plan_value:
                                id: pkgmgr_escape
                                type: string
                                required: true
                                value: "false"
                                options:
                                  - value: "true"
                                  - value: "false"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: |
                                    取值范围【true/false】，默认值false，当参数为true时，包管理可以上传无签名文件的网元包以及DV各个特性的无签名文件包，用于兼容存量业务场景.
                                  uis_en: |
                                    The value can be true or false. The default value is false. If this parameter is set to true, NE packages without signature files and DigitalView feature packages without signature files can be uploaded for compatibility with inventory service scenarios.
                          - type: object
                            field:
                              parameter_item:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: AIOpsMenuConfig
                                  uis_en: AIOpsMenuConfig
                              plan_value:
                                id: AIOpsMenuConfig
                                type: string
                                required: true
                                value: "faultAnalysis,capacityEvaluation,indicatorPrediction"
                                constraints:
                                  pattern: 
                                    rule: '^[a-zA-Z0-9,]{0,256}$'
                                    message: 
                                      uis_zh: "请输入合法的参数"
                                      uis_en: "Please enter valid parameter value"
                              description:
                                type: string
                                widget: LabelField
                                title:
                                  uis_zh: |
                                    智能运维菜单配置。
                                    通过配置此参数值，确定是否开启智能运维的各功能菜单。默认值为“faultAnalysis,capacityEvaluation,indicatorPrediction”。请根据局点实际规划进行动态配置智能运维的各功能菜单，参数间以英文逗号隔开。详细字段含义如下所示：•faultAnalysis：表示开启故障检测分析功能菜单，此功能适用于CBS、MM和NGBSS解决方案。
                                    •capacityEvaluation：表示开启智能容量评估功能菜单，此功能适用于MM、Commerce和NGBSS解决方案。
                                    •disasterRecovery：表示开启智能容灾功能菜单，此功能适用于彩铃解决方案。配置智能容灾后会默认启用故障检测分析功能。
                                    •indicatorPrediction：表示开启指标趋势预测功能菜单，此功能适用于MM解决方案。
                                    •tidalScheduling：表示开启潮汐调度功能菜单，潮汐调度依赖指标趋势预测能力，配置潮汐调度后会默认启用指标趋势预测，此功能适用于CBS解决方案。此特性为POC特性，仅支持调测，不做商用。

                                  uis_en: |
                                    Configure the AIOps Service menu.
                                    You can set this parameter to determine whether to enable AIOps Service function menus. The default value is faultAnalysis,capacityEvaluation,indicatorPrediction. Set this parameter based on the site plan. Use commas (,) to separate the parameters. The detailed fields are described as follows:
                                    •faultAnalysis: Indicates that the Fault Detection and Analysis function is enabled. This function is applicable to the CBS, MM and NGBSS solutions.
                                    •capacityEvaluation: Indicates that the Intelligent Capacity Evaluation function is enabled. This function is applicable to the MM, Commerce and NGBSS solutions.
                                    •disasterRecovery: Indicates that the Intelligent Disaster Recovery function is enabled. This function is applicable to the RBT solution. After Intelligent Disaster Recovery is configured, the Fault Detection and Analysis function is enabled by default.
                                    •indicatorPrediction: Indicates that the Indicator Trend Prediction function is enabled. This function is applicable to the MM solutions.
                                    •tidalScheduling: Indicates that the Tidal Scheduling function is enabled. Tidal scheduling depends on the KQI/KPI trend prediction capability. After tidal scheduling is configured, Indicator Trend Prediction is enabled by default. This function is applicable to the CBS solutions. This feature is a POC feature and can be commissioned but cannot be put into commercial use.
      paramInfo:  
        type: formDetails
        func: uis_getFormDetails
