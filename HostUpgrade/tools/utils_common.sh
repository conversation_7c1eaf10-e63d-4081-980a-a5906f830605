#!/bin/bash
helm_command="/var/paas/kubernetes/helm/helm"

TOOL_PATH=${CURRENT_PATH}
echo "${TOOL_PATH}" | grep "/tools"
if [ $? -ne 0 ]; then
    TOOL_PATH=${CURRENT_PATH}/tools
fi

SCRIPT_PATH="$1"
if [ -z "${SCRIPT_PATH}" ]; then
    SCRIPT_PATH=${TOOL_PATH}
fi

cat /etc/ssh/sshd_config 2> /dev/null| grep -w 'Port' | grep -v '#' > /dev/null 2>&1
if [ $? -ne 0 ];then
    ssh_port=22
else
    ssh_port=$(cat /etc/ssh/sshd_config | grep -w "Port" | grep -v "#" |awk -F 'Port' '{print $2}' | sed s/[[:space:]]//g)
fi

common_config_file=${TOOL_PATH}/common_config.properties
. ${common_config_file}

function log_echo()
{
    log_level=INFO
    if [ $# -ge 2 ]; then
        log_level=$1
        shift
    fi
    log_level=$(echo ${log_level} | tr 'a-z' 'A-Z')
    log_time=$(date +'%Y-%m-%d %H:%M:%S')
    echo "${log_time} [${log_level}] $*" >> $LOG_FILE
    echo "${log_time} [${log_level}] $*" 
}

function update_config()
{
    local config_file="$1"
    local key="$2"
    local value="$3"

    if [ -z "${config_file}" -o -z "${key}" ];then
        log_echo "ERROR" "The config_file=${config_file} or key=${key} has null."
        return 1
    fi

    if [ ! -f "${config_file}" ];then
        log_echo "ERROR" "The config_file=${config_file} is not exist."
        return 1
    fi

    local isExist=$(cat ${config_file} |grep -v "^[ ]*#"|grep -w "${key}")
    if [ -z "${isExist}" ];then
        echo "${key}=${value}" >> ${config_file}
    else
        sed -i "s#^${key}=.*#${key}=${value}#g" ${config_file}
    fi
    local config_ret=$?
    if [ ${config_ret} -ne 0 ];then
        log_echo "ERROR" "update_config key=${key}  and value to ${config_file} failed."
        return 1
    fi
    log_echo "INFO" "update_config key=${key} and value successfully."
    return 0
}

function getchar()
{
    stty cbreak -echo
    dd if=/dev/tty bs=1 count=1 2>/dev/null
    stty -cbreak echo
}

function echo_pwd()
{
    PwdReturn=""
    printf "$1:"
    while : ;do
        ret=$(getchar)
        #for escape character
        #exit for space character
        if [[ "X${ret}" == "X" ]];then
            echo
            break
        fi
        PwdReturn="$PwdReturn$ret"
        printf "*"
    done
}

function input_pwd()
{
    local node_ip=$1
    local pwd_user="root"
    [ "X$2" != "X" ] && pwd_user="$2"
    [ "X$3" != "X" ] && standby_tag="$3"
    pwdReturn=""
    
    sed -i "/^${node_ip} /d" /root/.ssh/known_hosts > /dev/null 2>&1

    local input_times=0
    while [ ${input_times} -lt 3 ]
    do
        if [ "X${standby_tag}" == "Xstandby_tag" ]; then
            echo_pwd "Please input the k8s standby node ${pwd_user}\'s password of ${node_ip} "
        else
            echo_pwd "Please input the k8s node ${pwd_user}\'s password of ${node_ip} "
        fi
        pwdReturn="$PwdReturn"

        auto_smart_ssh ${pwdReturn} "${pwd_user}@${node_ip} echo ssh_ok"
        if [ $? -eq 0 ];then
            break
        elif [ ${input_times} -eq 2 ];then
            log_echo "ERROR" "ssh to ${node_ip} execute command failed, please check."
            exit 1
        else
            log_echo "INFO" "Please try again to ssh to ${node_ip}."
        fi
        input_times=$((${input_times}+1))
    done
}

function cleanup_iptables_primarynode()
{
    local dv_config=${OPT_PATH}/dv_os_config/DV_Config_MergeCluster_Docker.config
    if [ ! -f "${dv_config}" ];then
        return 0
    fi

    local HostSecondary_IPV4=$(grep "^HostSecondary_IPV4=" "${dv_config}" |awk -F'HostSecondary_IPV4=' '{print $2}')
    local PodSecondary_IPV4=$(grep "^PodSecondary_IPV4=" "${dv_config}" |awk -F'PodSecondary_IPV4=' '{print $2}')

    local tmp_peer_host_ip=$(echo "${HostSecondary_IPV4}"|sed "s/\./\\\./g")
    local tmp_peer_pod_ip=$(echo "${PodSecondary_IPV4}"|sed "s/\./\\\./g")
    check_ret=$(iptables -w -t nat -L -n |grep -w "${tmp_peer_host_ip}" |grep -w "${tmp_peer_pod_ip}")
    log_echo "INFO" "The node_role=PrimaryHost check_iptables_ret=${check_ret}."
    if [ ! -z "${check_ret}" ];then
        log_echo "INFO" "execute cmd:[iptables -t nat -D OUTPUT -p tcp -d ${HostSecondary_IPV4} --dport 22 -j DNAT --to-destination ${PodSecondary_IPV4}:22]."
        iptables -t nat -D OUTPUT -p tcp -d ${HostSecondary_IPV4} --dport 22 -j DNAT --to-destination ${PodSecondary_IPV4}:22
        local iptables_del_ret=$?
        if [ ${iptables_del_ret} -ne 0 ];then
            iptables --wait -t nat -D OUTPUT -p tcp -d ${HostSecondary_IPV4} --dport 22 -j DNAT --to-destination ${PodSecondary_IPV4}:22
            iptables_del_ret=$?
        fi

        if [ ${iptables_del_ret} -ne 0 ];then
           log_echo "ERROR" "The iptables on PrimaryNode local del failed."
           exit 1
        fi
    fi
}

function Check_execute()
{
    local check_key=$1
    cat ${action_tag}  2>/dev/null| grep -w "${check_key} : success" > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "${check_key} has been executed"
        return 0
    else
        cat ${action_tag} 2>/dev/null | grep -w "${check_key} : failed" > /dev/null 2>&1
        if [ $? -eq 0 ];then
            log_echo "INFO" "Execute ${check_key} failed, need cleanup first."
            return 2  
        else
            return 1
        fi
    fi
}

function ssh_scp()
{
    option=$1
    passwd=$2
    comand1=$3
    comand2=$4
    
    local last_arg="${@: -1}"
    echo ${last_arg} | grep "dv_user_" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        comand2=""
        last_arg=$(echo ${last_arg}|awk -F'dv_user_' '{print $2}')
    fi


    echo "${passwd} host:passwd ${last_arg} dvuser:passwd\n" | ${CURRENT_PATH}/tools/ssh_scp.exp "${option}" "${ssh_port}" "${comand1}" "${comand2}"
}

function su_root_ssh_scp()
{
    local passwd="$1"

    local username_and_ip="$2"
    ## sudo到root场景，不需要加-t参数创建一个伪终端会话
    username_and_ip=$(echo "${username_and_ip}" | sed "s/^-t[ ]*//")

    local exec_cmd="$3"
    local args="$4"
    local last_arg="${@: -1}"
    echo ${last_arg} | grep "dv_user_" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        args=""
        dv_user_pwd=$(echo ${last_arg}|awk -F'dv_user_' '{print $2}')
    else
        dv_user_pwd=""
    fi
    echo "${passwd} host:passwd ${dv_user_pwd} dvuser:passwd\n" | ${CURRENT_PATH}/tools/su_root_ssh_scp.exp "${ssh_port}" "${username_and_ip}" "${exec_cmd}" "${args}"
}

function check_remote_user_pwd()
{
    local remote_user_pwd="$1"
    if [ -z "${remote_user_pwd}" ];then
        log_echo "ERROR" "The remote_user_pwd is null, please check."
        return 1
    fi

    local invalid_characters="\x1b|\x7f"
    echo "${remote_user_pwd}" |grep -iE "${invalid_characters}" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "ERROR" "The remote_user_pwd string contains invalid characters, please check."
        return 1
    fi

    return 0
}

function auto_smart_ssh () {
    if [ "X${SUDO_USER}" != "X" ];then
        ## 去除开头的空格
        local ssh_command=$(echo "$2" | sed "s#^[ ]\+##")
        ## sudo到root场景，不需要加-t参数创建一个伪终端会话
        ssh_command=$(echo "${ssh_command}" | sed "s/^-t[ ]*//")
        ## command1 获取第一个空格之前的内容
        comand1="${ssh_command%% *}"
        ## command1 获取第一个空格之后的内容
        comand2="${ssh_command#* }"
        ## 容器化场景，需要先sudo su - 切换为root用户，再执行命令
        if [ "X${SUDO_USER_COMMAND_IS_SU}" == "XYES" ]; then
            ## 统一去掉command2开头结尾的引号
            echo "${comand2}" | grep "^\"" >/dev/null 2>&1
            if [ $? -eq 0 ]; then
                comand2=$(echo "${comand2}" | sed 's#^"##1' | sed 's#"$##1')
            fi
            su_root_ssh_scp "$1" "${comand1}" "${comand2}" "$3"
            return $?
        fi
    fi
    ssh_scp "ssh -q -p" "$1" "$2" "$3"
    return $?
}

function auto_scp () {
    ssh_scp "scp -P" "$1" "$2" "$3"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "scp $2 $3 failed , please check."
        exit 1
    fi
}

function obtain_k8s_information()
{
    log_echo "INFO" "begin to obtain k8s info..."
    ## 获取k8s版本号
    auto_smart_ssh "${k8s_root_pwd}" "${k8s_ssh_user}@${k8s_ip} su - paas -c '/var/paas/bootstrap/bin/version.sh --all'" > ${CURRENT_PATH}/k8s.version

    local k8s_version=$(cat ${CURRENT_PATH}/k8s.version | grep "^Current version: DigitalFoundry" | awk -F "DigitalFoundry " '{print $2}' | sed 's/\r//g')
    if [ -z "${k8s_version}" ]; then
        log_echo "ERROR" "get k8s version failed."
        exit 1
    fi
    update_config ${config_file} "k8s_version" "${k8s_version}"
    log_echo "INFO" "obtain k8s_version=${k8s_version}."
}

function get_parameter()
{
    ## CD流水线场景已知密*,通过参数传入
    k8s_root_pwd="$1"

    if [ -f "${CURRENT_PATH}/get_parameter_ok" ];then
        . ${config_file}
        log_echo "INFO" "get_parameter has executed. The network_type is ${network_type}."
        return 0
    fi

    sed -i "/network_type=/d" ${config_file}
    echo "" >> ${config_file}
    grep "ALL_IN_ONE" ${nodelists_file} > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "This is ALL_IN_ONE network."
        network_type="O"
        echo "network_type=O" >> ${config_file}
    fi
    
    grep "DVThirdNode" ${nodelists_file} > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "INFO" "This is MergeCluster network."
        network_type="M"
        echo "network_type=M" >> ${config_file}
    fi

    float_ip=$(docker ps | grep dv-container |awk -F' ' '{print $NF}'|awk -F'-' '{print $3}')
    ping -c 3 ${float_ip} > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Can't ping DV float IP: ${float_ip}, please check"
        exit 1
    fi
    sed -i "s/DVFloatIP_IPV4=.*/DVFloatIP_IPV4=${float_ip}/" ${config_file}
    
    k8s_ip=$(docker images | grep "/root/pause"|awk -F' ' '{print $1}'|awk -F':' '{print $1}')
    ping -c 3 ${k8s_ip} > /dev/null 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Can't ping K8S IP: ${k8s_ip}, please check"
        exit 1
    fi
    sed -i "s/K8S_IPV4=.*/K8S_IPV4=${k8s_ip}/" ${config_file}

    if [ -z "${k8s_root_pwd}" ]; then
        input_pwd "${k8s_ip}" "${k8s_ssh_user}"
        k8s_root_pwd="${pwdReturn}"
    fi
    local check_times=1
    while [ $check_times -le 5 ]; do
        auto_smart_ssh "${k8s_root_pwd}" "${k8s_ssh_user}@${k8s_ip} \"source /etc/profile;kubectl get node --show-labels | grep  --color=none -wF ${float_ip}\"" > ${CURRENT_PATH}/kubectl_get_node.txt
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ssh to k8s: ${k8s_ip} to execute command 'kubectl get node --show-labels' failed."
            exit 1
        fi
        cat ${CURRENT_PATH}/kubectl_get_node.txt | grep -v "kubectl get node --show-labels" | grep -wF "${float_ip}" >/dev/null 2>&1
        if [ $? -eq 0 ];then
            break
        fi
        ((check_times++))
    done

    if [ $check_times -gt 5 ]; then
        log_echo "ERROR" "Can't find dv's floatip from the result with 'kubectl get node --show-labels', please check ${PreSet_PATH}/kubectl_get_node.txt"
        exit 1
    fi

    if [ "${network_type}" == "M" ];then
        ip_list=$(grep -wF "${float_ip}" ${CURRENT_PATH}/kubectl_get_node.txt|grep -v "kubectl get node --show-labels"|awk -F' ' '{print $1}'|sed 's/dv-//g'|sed 's/paas-//g'|sed 's/-/./g')
        host_ip_list=$(echo "${ip_list}"|sed "s/$/,/g"|tr -d '\n'|sed "s/ //g"|sed "s/,$//g")
        sed -i "s/HostNode_List_IPV4=.*/HostNode_List_IPV4=${host_ip_list}/" ${config_file}
    else
        node_number=$(grep -wF "${float_ip}" ${CURRENT_PATH}/kubectl_get_node.txt|grep -v "kubectl get node --show-labels"|wc -l)
        if [ "${node_number}" -ne 1 ];then
            log_echo "ERROR" "This is ALL_IN_ONE, but can't find 1 node with 'kubectl get node --show-labels', please check ${CURRENT_PATH}/kubectl_get_node.txt"
            exit 1
        fi
        node_ip=$(grep -wF "${float_ip}" ${CURRENT_PATH}/kubectl_get_node.txt|grep -v "kubectl get node --show-labels"|awk -F' ' '{print $1}'|sed 's/dv-//g'|sed 's/paas-//g'|sed 's/-/./g')
        sed -i "s/Host_IPV4=.*/Host_IPV4=$(echo ${node_ip}|awk '{print $1}')/" ${config_file}
    fi
    
    touch ${CURRENT_PATH}/get_parameter_ok

    obtain_k8s_information

    . ${config_file}
}

function check_helm()
{
    if [ -z "${k8s_root_pwd}" ];then
        log_echo "INFO" "We need the ${k8s_ssh_user} permission to ssh to k8s node."
        input_pwd "${K8S_IPV4}" ${k8s_ssh_user}
        k8s_root_pwd="${pwdReturn}"
    fi

    contain_k8s_static_ip

    auto_smart_ssh "${k8s_root_pwd}" "${k8s_ssh_user}@${K8S_IPV4} ls -l ${helm_command}" > ${CURRENT_PATH}/ls_helm.txt 2>&1
    if [ $? -ne 0 ];then
        log_echo "ERROR" "ssh to k8s node:${K8S_IPV4} to execute command 'ls -l ${helm_command}' failed, please check"
        exit 1
    fi
    
    grep -i "No such file" ${CURRENT_PATH}/ls_helm.txt > /dev/null 2>&1
    if [ $? -eq 0 ];then
        log_echo "ERROR" "Can't find helm command with 'ls -l ${helm_command}' at k8s node:${K8S_IPV4}, please check"
        log_echo "ERROR" "Maybe the k8s has switchovered to other node which is not the node with helm, please contact huawei engineer to modify the correct IP of 'K8S_IPV4' in ${CURRENT_PATH}/DV_HostUpgrade.properties"
        exit 1
    fi
}

function wait_uniep_start()
{
    local i=0
    local time_out=1200
    while [ $i -lt ${time_out} ]
    do
        pod_id=$(docker ps |grep "dv-container" |awk '{print $1}')
        if [ -z "${pod_id}" ];then
            sleep 10
            ((i=i+10))
            log_echo "INFO" "Wait for container start up"
            continue
        fi
        docker exec -i ${pod_id} ps -ef |grep nodeagent
        if [ $? -eq 0 ];then
            log_echo "INFO" "Find nodeagent process, you can login the cloudsop website to wait for all the services running."
            return 0
        fi
        log_echo "INFO" "Wait for cloudsop's nodeagent process..."
        sleep 10
        ((i=i+10))
    done

    log_echo "ERROR" "wait for uniep start timed out, please check."
    exit 1
}

function cleanup_knownhosts()
{
    log_echo "INFO" "Begin to cleanup known_hosts ..."
    if [ "${network_type}" == "M" ];then
        local ssh_first_ip=""
        local index=0
        local execute_type=""
        local host_pwd=""
        for host_ip in $(echo "${HostNode_List_IPV4}" |sed "s/,/ /g");
        do
            execute_type="${execute_type_list[${index}]}"
            host_pwd="${host_pwd_list[${index}]}"
            host_ssh_pwd="${host_ssh_pwd_list[${index}]}"
            if [ "${execute_type}" == "ssh" -a -z "${host_pwd}" ];then
                if [ -z "${ssh_first_ip}" -a "X${is_use_root}" != "Xyes" -a -z "${node_ssh_pwd}" ];then
                    ssh_first_ip="${host_ip}"
                    input_node_ssh_pwd "${upgrade_ssh_user}" "${host_ip}"
                    node_ssh_pwd="${nodepwdReturn}"
                fi
                log_echo "INFO" "Need root passwd to cleanup known_hosts at ${host_ip}"
                input_pwd_upgrade "${host_ip}"
                host_ssh_pwd="${sshpwdReturn}"
                host_pwd="${rootpwdReturn}"
            fi
            node_cleanup_knownhosts "${execute_type}" "${host_ip}" "${host_pwd}" "${host_ssh_pwd}"
            ((index++))
        done
    elif [ "${network_type}" == "O" ];then
        node_cleanup_knownhosts "local" "${Host_IPV4}" ""
    fi
}

function node_cleanup_knownhosts()
{
    local execute_type="$1"
    local app_ssh_ip="$2"
    local app_scp_ip="$2"
    local app_node_pwd="$3"
    local remote_ssh_passwd="$4"
    
    if [ -z "${app_ssh_ip}" ];then
        log_echo "ERROR" "The app_ssh_ip=${app_ssh_ip} is null, please check"
        exit 1
    fi

    log_echo "INFO" "Begin to cleanup known_hosts at ${app_ssh_ip}"

    if [ "X${execute_type}" == "Xlocal" ];then
        sh ${CURRENT_PATH}/tools/upgrade_config_host.sh "cleanup_known_hosts"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Execute cmd:[ sh ${CURRENT_PATH}/tools/upgrade_config_host.sh cleanup_known_hosts ] at node ${app_ssh_ip} failed, please check..."
            exit 1
        fi
    else
        if [ -z "${app_node_pwd}" ];then
            log_echo "ERROR" "The app_node_pwd of ${app_ssh_ip} is null, please check"
            exit 1
        fi
        
        tmp_dir=${TMP_PATH}/DVHostUpgrade
        ssh_execute "${upgrade_ssh_user}" "${remote_ssh_passwd}" "${app_node_pwd}" "${app_ssh_ip}" "sh ${tmp_dir}/tools/upgrade_config_host.sh cleanup_known_hosts"
        if [ $? -ne 0 ];then
            log_echo "ERROR" "Execute cmd:[ sh ${tmp_dir}/tools/upgrade_config_host.sh cleanup_known_hosts ] at node ${app_ssh_ip} failed, please check..."
            exit 1
        fi        
    fi
}

function input_user_pwd_before()
{
    if [ "X${upgrade_ssh_user}" == "Xroot" ]; then
        read -p "Can we ssh to all the nodes with root?  please choose is_use_root : yes/no  "  is_use_root
        if [ -z "${is_use_root}" ];then
            log_echo "ERROR" "The input is_use_root is null"
            exit 1
        elif [ "${is_use_root}" != "yes" -a "${is_use_root}" != "no" ];then
            log_echo "ERROR" "The input is_use_root is incorrect"
            exit 1
        fi
    else
        ## sudo场景默认为yes
        is_use_root="yes"
    fi
    
    if [ "X${is_use_root}" == "Xyes" ];then
        upgrade_ssh_user="${upgrade_ssh_user}"
    else
        upgrade_ssh_user="sshusr"
    fi
    
    sed -i "s/^upgrade_ssh_user=.*/upgrade_ssh_user=${upgrade_ssh_user}/g" ${config_file}
    sed -i "s/^is_use_root=.*/is_use_root=${is_use_root}/g" ${config_file}
    log_echo "INFO" "is_use_root is ${is_use_root}..."
}

function clean_known_hosts()
{
    known_ip=$1
    if [ -f /root/.ssh/known_hosts ];then
        sed -i -e "/.*${known_ip}.*/d" /root/.ssh/known_hosts
    fi

    if [ -f /home/<USER>/.ssh/known_hosts ];then
        sed -i -e "/.*${known_ip}.*/d" /home/<USER>/.ssh/known_hosts
    fi
}

function input_node_ssh_pwd()
{
    local ssh_user="$1"
    local node_ip="$2"
    nodepwdReturn=""
    log_echo "INFO" "We need ${ssh_user}'s password of all nodes are the same !!!"
    sed -i -e  "/.*${node_ip}.*/d" /root/.ssh/known_hosts
    local input_times=0
    while [ ${input_times} -lt 3 ]
    do
        echo_pwd "Please input ${ssh_user}\'s password of ${node_ip} "
        nodepwdReturn="$PwdReturn"

        auto_smart_ssh ${nodepwdReturn} "${ssh_user}@${node_ip} echo ssh_ok"
        if [ $? -eq 0 ];then
            break
        elif [ ${input_times} -eq 2 ];then
            log_echo "ERROR" "ssh to ${node_ip} execute command failed, please check."
            exit 1
        else
            log_echo "INFO" "Please try again to ssh to ${node_ip}."
        fi
        input_times=$((${input_times}+1))
    done
}

function su_root_ssh()
{
    local sshuser=$1
    local sshpwd=$2
    local rootpwd=$3
    local sship=$4
    local sshcmd=$5
    
    local last_arg="${@: -1}"
    echo ${last_arg} | grep "dv_user_" >/dev/null 2>&1
    if [ $? -eq 0 ];then
        last_arg=$(echo ${last_arg}|awk -F'dv_user_' '{print $2}')
    else
        last_arg=""
    fi
    
    echo "${sshpwd} host:passwd ${rootpwd} work:passwd ${last_arg} dvuser:passwd\n" |  ${CURRENT_PATH}/tools/su_root_ssh.exp ${sship} ${sshuser} root ${ssh_port} "${sshcmd}"
    if [ $? -ne 0 ];then
        log_echo "INFO" "su root to execute command ${sshcmd} with return 1 at ${sship}, please check."
        return 1
    fi
}

function input_pwd_upgrade()
{
    local node_ip=$1
    local ssh_user="${upgrade_ssh_user}"
    if [ "X${is_use_root}" == "Xyes" ];then
        sshpwdReturn=""
        local input_times=0
        while [ ${input_times} -lt 3 ]
        do
            echo_pwd "Please input ${ssh_user}\'s password of ${node_ip} "
            sshpwdReturn="$PwdReturn"

            clean_known_hosts "${node_ip}"
            auto_smart_ssh ${sshpwdReturn} "${ssh_user}@${node_ip} echo ssh_ok"
            if [ $? -eq 0 ];then
                break
            elif [ ${input_times} -eq 2 ];then
                log_echo "ERROR" "ssh to ${node_ip} execute command failed, please check."
                exit 1
            else
                log_echo "INFO" "Please try again to ssh to ${node_ip}."
            fi
            input_times=$((${input_times}+1))
        done
        rootpwdReturn="${sshpwdReturn}"
    else
        sshpwdReturn="${node_ssh_pwd}"
        input_times=0
        while [ ${input_times} -lt 3 ]
        do
            echo_pwd "Please input root\'s password of ${node_ip} "
            rootpwdReturn="$PwdReturn"

            clean_known_hosts "${node_ip}"
            su_root_ssh "${ssh_user}" "${sshpwdReturn}" "${rootpwdReturn}" "${node_ip}" "echo ssh_ok"
            if [ $? -eq 0 ];then
                break
            elif [ ${input_times} -eq 2 ];then
                log_echo "ERROR" "ssh to ${node_ip} su root to execute command failed, please check."
                exit 1
            else
                log_echo "INFO" "Please try again ."
            fi
            input_times=$((${input_times}+1))
        done
    fi
}

function ssh_execute()
{
    local last_arg="${@: -1}"
    local username="$1"
    if [ "X${is_use_root}" == "Xyes" ];then
        local root_pwd="$2"
        local root_pwd=${root_pwd// /}
        local remote_ip="$4"
        local ssh_cmd="$5"
        echo ${last_arg} | grep "dv_user_" >/dev/null 2>&1
        if [ $? -ne 0 ];then
            last_arg=""
        fi
        if [ "X${SUDO_USER}" != "X" ];then
            auto_smart_ssh "${root_pwd}" "${username}@${remote_ip} ${ssh_cmd}" "${last_arg}"
        else
            auto_smart_ssh "${root_pwd}" "-t root@${remote_ip} ${ssh_cmd}" "${last_arg}"
        fi
    else
        su_root_ssh "$1" "$2" "$3" "$4" "$5" "${last_arg}"
    fi
}
function config_sudo_env_reset()
{
    sed -i "s#^Defaults .*env_reset#Defaults env_reset#g" /etc/sudoers
    sed -i "s#^Defaults .*\!visiblepw#Defaults visiblepw#g" /etc/sudoers
    sed -i "s#^Defaults .*requiretty#\#Defaults requiretty#g" /etc/sudoers
}

function config_sudo()
{
    user_name=$1
    sudo_cmd_list=$(echo "$2" | sed 's/\\\:/:/g')
    sudo_user=$3
    if [ "X$sudo_user" == "X" ]; then
        sudo_user="root"
    fi
    cur_sudo=$(cat /etc/sudoers | grep "^${user_name} ALL=(${sudo_user})"|grep -v uniepsudobin |tail -1 | sed 's/\\\:/:/g')
    if [ "X$cur_sudo" == "X" ]; then
        fina_sudo="$user_name ALL=(${sudo_user}) NOPASSWD: $sudo_cmd_list"
        echo "${fina_sudo}" | sed 's/:/\\\:/g' | sed 's/NOPASSWD\\\:/NOPASSWD:/g' >> /etc/sudoers
    else
        fina_sudo=$(echo "$sudo_cmd_list" | awk -vcur_sudo="$cur_sudo" -F',' '{
            for (i = 1; i <= NF; i++)
            {
                if ( index(cur_sudo",", $i",") == 0 )
                {
                    cur_sudo=cur_sudo","$i
                }
            }
            print cur_sudo
        }' 2>/dev/null)
        
        local line_num=$(grep -n "^${user_name} ALL=(${sudo_user})" /etc/sudoers |grep -v uniepsudobin |tail -1|awk -F':' '{print $1}')
        if [ -z "${line_num}" ];then
            echo "${fina_sudo}" | sed 's/:/\\\:/g' | sed 's/NOPASSWD\\\:/NOPASSWD:/g' >> /etc/sudoers
        else
            local fina_sudo_new=$(echo "${fina_sudo}" | sed 's/:/\\\:/g' | sed 's/NOPASSWD\\\:/NOPASSWD:/g')
            sed -i "${line_num} a ${fina_sudo_new}" /etc/sudoers
            sed -i "${line_num},${line_num}d" /etc/sudoers
        fi
    fi
    
    config_sudo_env_reset
    
}

function delete_old_sudo()
{
    user_name=$1
    need_delete_cmd_list=$(echo "$2" | sed 's/\\\:/:/g')
    sudo_user=$3
    if [ "X$sudo_user" == "X" ]; then
        sudo_user="root"
    fi
    cur_sudo=$(cat /etc/sudoers | grep "^${user_name} ALL=(${sudo_user})"|grep -v uniepsudobin |tail -1 | sed 's/\\\:/:/g'|awk -F'NOPASSWD:' '{print $2}'|sed 's/^ *//')
    if [ "X$cur_sudo" == "X" ]; then
        return 0
    else
        fina_sudo=$(echo "$cur_sudo" | awk -vnouse_sudo="$need_delete_cmd_list" -vtmp_sudo="" -F',' '{
            for (i = 1; i <= NF; i++)
            {
                if ( index(nouse_sudo",", $i",") == 0 )
                {
                    tmp_sudo=tmp_sudo","$i
                }
            }
            print tmp_sudo
        }' 2>/dev/null)
    fi
    
    tmp_finalsudo=$(echo "${fina_sudo}"|sed 's/ //g')
    [ "${tmp_finalsudo}" == "" ] && return 0
    
    fina_sudo=$(echo "${fina_sudo}" | sed 's/^,//g')
    fina_sudo="${user_name} ALL=(${sudo_user}) NOPASSWD:  ${fina_sudo}"
    local fina_sudo_new=$(echo "${fina_sudo}" | sed 's/:/\\\:/g' | sed 's/NOPASSWD\\\:/NOPASSWD:/g')
    
    local line_num=$(grep -n "^${user_name} ALL=(${sudo_user})" /etc/sudoers |grep -v uniepsudobin |tail -1|awk -F':' '{print $1}')
    if [ -z "${line_num}" ];then
        echo "${fina_sudo_new}" >> /etc/sudoers
    else
        sed -i "${line_num} a ${fina_sudo_new}" /etc/sudoers
        sed -i "${line_num},${line_num}d" /etc/sudoers
    fi
    return 0
}
function cleanup_known_hosts()
{
    known_hosts_files=$(find ${HOME_PATH}/dv -name "known_hosts" | grep ".ssh")
    
    for file in ${known_hosts_files}
    do
        echo > ${file}
    done
}

function set_loop_monitor()
{
    if [ -f "/.dockerenv"  ];then
        return 0
    fi
    script_path=$1
    service_path=$2
    timer_path=$3

    mkdir -p ${root_custom_path}/dv_loop_monitor
    cp -f ${script_path} ${root_custom_path}/dv_loop_monitor/
    local cfg_path=$(dirname ${script_path})
    cp -f ${cfg_path}/dv_loop_monitor.cfg ${root_custom_path}/dv_loop_monitor/
    
    if [ "X${service_path}" != "X" ] &&  [ -f ${service_path} ];then
            cp  -f ${service_path}  /etc/systemd/system/
            chmod 644 /etc/systemd/system/dv_loop_monitor.service
    else
        log_echo "ERROR" "${UTILS_PATH}/dv_loop_monitor.service does not exists"
        exit 1
    fi

    if [ "X${timer_path}" != "X" ] &&  [ -f ${timer_path} ];then
        cp  -f  ${timer_path}  /etc/systemd/system/
        chmod 644 /etc/systemd/system/dv_loop_monitor.timer
    else
        log_echo "ERROR" "${UTILS_PATH}/dv_loop_monitor.timer does not exists"
        exit 1
    fi
    chmod 500  ${root_custom_path}/dv_loop_monitor/dv_loop_monitor.sh
    chmod 600  ${root_custom_path}/dv_loop_monitor/dv_loop_monitor.cfg
    chmod 700  ${root_custom_path}/dv_loop_monitor
    chown root:root  ${root_custom_path}/dv_loop_monitor/dv_loop_monitor.sh
    chown root:root  ${root_custom_path}/dv_loop_monitor/dv_loop_monitor.cfg
    chown root:root  /etc/systemd/system/dv_loop_monitor.service
    chown root:root  /etc/systemd/system/dv_loop_monitor.timer
    systemctl daemon-reload
    systemctl enable dv_loop_monitor.service
    systemctl start dv_loop_monitor.timer
    if [ $? -ne 0 ];then
        log_echo "ERROR" "dv_loop_monitor.timer start failed use 'systemctl start dv_loop_monitor.timer'"
        exit 1
    fi
    systemctl enable dv_loop_monitor.timer
    
    ret=$(ps -ef | grep dv_loop_monitor.sh | grep -v grep)
    if [ "X$ret" == "X" ];then
        if [ -L ${OPT_PATH}/dv/oss/SOP/apps/DVMediationService ];then
            if [ -f /etc/cni/net.d/bunker.conf ];then
                mv /etc/cni/net.d/bunker.conf /etc/cni/net.d/bunker.conf.bak
            fi
            systemctl start dv_loop_monitor.service
            if [ -f /etc/cni/net.d/bunker.conf.bak ];then
                mv /etc/cni/net.d/bunker.conf.bak /etc/cni/net.d/bunker.conf
            fi
            
        else
            systemctl start dv_loop_monitor.service
        fi
    fi
    

}

function delete_loop_monitor()
{
    if [ -f "/.dockerenv"  ];then
        return 0
    fi
    rm -rf ${root_custom_path}/dv_loop_monitor
    if [ -f "/etc/rc.d/after.local" ];then
        sed -i "/dv_loop_monitor/d" /etc/rc.d/after.local >/dev/null
        
    fi
    if [ -f "/etc/rc.d/rc.local" ];then
        sed -i "/dv_loop_monitor/d" /etc/rc.d/rc.local >/dev/null
    fi
    systemctl disable dv_loop_monitor.timer >/dev/null 2>&1
    systemctl disable dv_loop_monitor.service >/dev/null 2>&1
    rm -f  /etc/systemd/system/dv_loop_monitor.service >/dev/null 2>&1
    rm -f  /etc/systemd/system/dv_loop_monitor.timer >/dev/null 2>&1
    systemctl daemon-reload
} 

function set_loop_monitor_for_upgrade()
{
    if [ -f "/.dockerenv"  ];then
        return 0
    fi
    
    if [ ! -f ${root_custom_path}/dv_loop_monitor/dv_loop_monitor.sh ];then
        mkdir -p ${root_custom_path}/dv_loop_monitor/
        touch ${root_custom_path}/dv_loop_monitor/upgrade_${ITA_image_tag}.tag
    fi
    set_loop_monitor $@ 
}

function delete_loop_monitor_for_upgrade()
{
    if [ -f ${root_custom_path}/dv_loop_monitor/dv_loop_monitor.sh ] && [ -f ${root_custom_path}/dv_loop_monitor/upgrade_${ITA_image_tag}.tag ];then
        delete_loop_monitor 
    fi
}

function set_user_passwd()
{

    user_name=$1
    user_pwd=$2
    log_echo "INFO" " ... begin to set users [${user_name}] passwd ... "
    
    if [ -f "/etc/security/opasswd" ];then
        sed -i "/^${user_name}:/d" /etc/security/opasswd   
    fi
    
    suse_version=0
    if [  -f /etc/SuSE-release ];then
        suse_version=$(cat /etc/SuSE-release | grep VERSION | awk -F'=' '{print $2}' | sed -r 's/ //g')
    fi

    log_echo "INFO"  "Set password for user ${user_name}"
    passwd -d ${user_name}
    local ret="0"
    if [ ${suse_version} -eq 12 ]; then
        echo "${user_name}":"${user_pwd}" | chpasswd >>$LOG_FILE 2>&1 || ret="1"
    else
        echo "${user_pwd}" | passwd ${user_name} --stdin >>$LOG_FILE 2>&1
        if [ $? -ne 0 ];then
            echo "${user_name}":"${user_pwd}" | chpasswd >>$LOG_FILE 2>&1 || ret="1"
        fi
    fi
    chage -M 99999 ${user_name}
    if [ "X$ret" == "X1" ];then
        log_echo "ERROR" "Set password of ${user_name} failed, please check!"
        return 1
    fi
}

function set_limit_memory()
{
    local limit_cpu_line=$1
    local deployment_yaml=$2
    local is_integrated_df_and_service=$3
    
    log_echo "INFO" "set_limit_memory start..."
    log_echo "INFO" "The limit_cpu_line=${limit_cpu_line} deployment_yaml=${deployment_yaml}"
    local limit_memory_line=$((${limit_cpu_line}+1))
    local limit_memory_str=$(sed -n "${limit_memory_line},${limit_memory_line}p" ${deployment_yaml} |grep -w "memory:")
    log_echo "INFO" "The limit_memory_line=${limit_memory_line} limit_memory_str=${limit_memory_str} is_integrated_df_and_service=${is_integrated_df_and_service}"
    ## In the co-deployment scenario, set 96G = 98304Mi
    if [ "X${is_integrated_df_and_service}" == "XYes" ];then
        ## memory: 98304Mi
        local limit_memory=98304
        local memory_total=$(free -m |grep Mem |awk '{print $2}')
        if [ ${memory_total} -lt ${limit_memory} ];then
            limit_memory=${memory_total}
        fi
        
        if [ -z "${limit_memory_str}" ];then
            ## new add to it.
            sed -i "${limit_cpu_line} a {{limit_memory}}" ${deployment_yaml}
            log_echo "INFO" "add {{limit_memory}},ret=$?"
            local limit_memory_new=$(sed -n "${limit_cpu_line},${limit_cpu_line}p" ${deployment_yaml}|sed "s#cpu: \"[0-9]\+\"#memory: ${limit_memory}Mi#")
            sed -i "s#{{limit_memory}}#${limit_memory_new}#g" ${deployment_yaml}
            log_echo "INFO" "modify {{limit_memory}},ret=$?"
        else
            ## modify it.
            sed -i "${limit_memory_line}s#memory: [0-9]\+Mi#memory: ${limit_memory}Mi#" ${deployment_yaml}
            log_echo "INFO" "modify limit memory,ret=$?"
        fi
        
        log_echo "INFO" "The limit_memory_str=$(sed -n "${limit_memory_line},${limit_memory_line}p" ${deployment_yaml})"
        if [ -z "$(grep -w 'memory:' ${deployment_yaml})" ];then
            log_echo "ERROR" "modify_label in helm configure imit memory failed, please check"
            exit 1
        fi
    else
        if [ ! -z "${limit_memory_str}" ];then
            sed -i "${limit_memory_line},${limit_memory_line}d" ${deployment_yaml}
            log_echo "INFO" "del limit_memory_str=${limit_memory_str} in line ${limit_memory_line},ret=$?"
        fi
    fi
    
    log_echo "INFO" "set_limit_memory End"
}

function set_requests_memory()
{
    local requests_cpu_line=$1
    local deployment_yaml=$2
    local is_integrated_df_and_service=$3
    log_echo "INFO" "set_requests_memory start..."
    log_echo "INFO" "The requests_cpu_line=${requests_cpu_line} deployment_yaml=${deployment_yaml}"
    local requests_memory_line=$((${requests_cpu_line}+1))
    local requests_memory_str=$(sed -n "${requests_memory_line},${requests_memory_line}p" ${deployment_yaml} |grep -w "memory:")
    log_echo "INFO" "The requests_memory_line=${requests_memory_line} requests_memory_str=${requests_memory_str} is_integrated_df_and_service=${is_integrated_df_and_service}"
    ## In the co-deployment scenario, set 40G = 40960Mi
    if [ "X${is_integrated_df_and_service}" == "XYes" ];then
        ## memory: 40960Mi
        local requests_memory=40960
        if [ -z "${requests_memory_str}" ];then
            ## new add to it.
            sed -i "${requests_cpu_line} a {{requests_memory}}" ${deployment_yaml}
            log_echo "INFO" "add {{requests_memory}},ret=$?"
            local requests_memory_new=$(sed -n "${requests_cpu_line},${requests_cpu_line}p" ${deployment_yaml}|sed "s#cpu: \"[0-9]\+\"#memory: ${requests_memory}Mi#")
            sed -i "s#{{requests_memory}}#${requests_memory_new}#g" ${deployment_yaml}
            log_echo "INFO" "modify {{requests_memory}},ret=$?"
        else
            ## modify it.
            sed -i "${requests_memory_line}s#memory: [0-9]\+Mi#memory: ${requests_memory}Mi#" ${deployment_yaml}
            log_echo "INFO" "modify requests memory,ret=$?"
        fi
        
        log_echo "INFO" "The requests_memory_str=$(sed -n "${requests_memory_line},${requests_memory_line}p" ${deployment_yaml})"
        if [ -z "$(grep -w 'memory:' ${deployment_yaml})" ];then
            log_echo "ERROR" "modify_label in helm configure requests memory failed, please check"
            exit 1
        fi
    else
        if [ ! -z "${requests_memory_str}" ];then
            sed -i "${requests_memory_line},${requests_memory_line}d" ${deployment_yaml}
            log_echo "INFO" "del requests_memory_str=${requests_memory_str} in line ${requests_memory_line},ret=$?"
        fi
    fi
    
    log_echo "INFO" "set_requests_memory End"
}


function update_cpu_check()
{
    local old_cpu="$1"
    local new_cpu="$2"
    local old_memory="$3"
    local new_memory="$4"
    local tag_key="$5"
    
    ## new_memory is null,is Exclusive Scenario need to set old. 
    if [ -z "${new_memory}" ];then
        log_echo "INFO" "The new_memory=${new_memory} is null,need to update ${tag_key} cpu of old.This Exclusive Scenario"
        return 1
    fi
    
    ##Non-exclusive scenario
    if [ ! -z "${old_memory}" ];then
        ## new to new
        log_echo "INFO" "The new_memory=${new_memory} and old_memory=${old_memory} is not null,need to update ${tag_key} cpu of old.This Non-exclusive scenario new to new."
        return 1
    fi
    
    local tmp_old=$(echo "${old_cpu}" |awk -F'"' '{print $2}')
    local tmp_new=$(echo "${new_cpu}" |awk -F'"' '{print $2}')
    if [ "${tmp_old}" == "${tmp_new}" ];then
        log_echo "INFO" "The tmp_old=${tmp_old} and tmp_new=${tmp_new} is same.not need update it.of ${tag_key}."
        return 0
    fi
    
    ## is requests and old and new is not same. 
    if [ "${tag_key}" == "requests" ];then
        log_echo "INFO" "The tag_key=${tag_key} and tmp_old=${tmp_old} and tmp_new=${tmp_new} is not same.need to update ${tag_key} cpu of old."
        return 1
    fi
    
    ## tag_key is limit, and old , new is not same
    processor=$(cat /proc/cpuinfo | grep processor | wc -l)
    if [ "${processor}" != "${tmp_old}" ];then
        log_echo "INFO" "The tmp_old=${tmp_old} and processor=${processor} is not same.not need update it.of ${tag_key}."
        return 1
    fi
    
    log_echo "INFO" "The tmp_old=${tmp_old} and processor=${processor} is same.not need update it.of ${tag_key}."
    return 0
}

function update_cpu_memory()
{
    local tag_key="$1"
    local deployment_yaml="$2"
    local old_deployment_yaml="$3"
    
    if [ -z "${tag_key}" -o -z "${deployment_yaml}" -o -z "${old_deployment_yaml}" ];then
        log_echo "ERROR" "The tag_key=${tag_key} or deployment_yaml=${deployment_yaml} or old_deployment_yaml=${old_deployment_yaml} has null."
        exit 1
    fi
    
    if [ ! -f ${deployment_yaml} ];then
        log_echo "ERROR" "The deployment_yaml=${deployment_yaml} is not exits."
        exit 1
    fi
    
    if [ ! -f ${old_deployment_yaml} ];then
        log_echo "ERROR" "The old_deployment_yaml=${old_deployment_yaml} is not exits."
        exit 1
    fi
    
    log_echo "INFO" "The tag_key=${tag_key} deployment_yaml=${deployment_yaml} old_deployment_yaml=${old_deployment_yaml}"
    ## get old_deployment_yaml
    local old_tag_line=$(grep -n  "${tag_key}" ${old_deployment_yaml}  |cut -d ":" -f 1)
    local old_cpu_line=$((${old_tag_line} + 1))
    local old_memory_line=$((${old_tag_line} + 2))
    
    local old_cpu=$(sed -n "${old_cpu_line},${old_cpu_line}p" ${old_deployment_yaml} |grep -w "cpu:")
    local old_memory=$(sed -n "${old_memory_line},${old_memory_line}p" ${old_deployment_yaml} |grep -w "memory:")
    log_echo "INFO" "The old_tag_line=${old_tag_line} old_cpu_line=${old_cpu_line} old_memory_line=${old_memory_line} old_cpu=${old_cpu} old_memory=${old_memory}"
    
    ## get deployment_yaml
    local new_tag_line=$(grep -n  "${tag_key}" ${deployment_yaml}  |cut -d ":" -f 1)
    local new_cpu_line=$((${new_tag_line} + 1))
    local new_memory_line=$((${new_tag_line} + 2))
    
    local new_cpu=$(sed -n "${new_cpu_line},${new_cpu_line}p" ${deployment_yaml} |grep -w "cpu:")
    local new_memory=$(sed -n "${new_memory_line},${new_memory_line}p" ${deployment_yaml} |grep -w "memory:")
    log_echo "INFO" "The new_tag_line=${new_tag_line} new_cpu_line=${new_cpu_line} new_memory_line=${new_memory_line} new_cpu=${new_cpu} new_memory=${new_memory}"
    
    log_echo "INFO" "update_cpu_memory tag_key=${tag_key} start..."
    update_cpu_check "${old_cpu}" "${new_cpu}" "${old_memory}" "${new_memory}" "${tag_key}"
    if [ $? -eq 1 ];then
        sed -i "${new_cpu_line}s#.*cpu: \"[0-9]\+\"#${old_cpu}#" ${deployment_yaml}
        log_echo "INFO" "update ${tag_key} cpu End.ret=$?"
    fi
    
    if [ -z "${old_memory}" ];then
        log_echo "INFO" "The ${tag_key} of old_memory=${old_memory} is null.not need to update it."
    else
        log_echo "INFO" "update ${tag_key} memory start..."
        local memory_str=$(sed -n "${new_memory_line},${new_memory_line}p" ${deployment_yaml} |grep -w "memory:")
        if [ -z "${memory_str}" ];then
            ## new add to it.
            sed -i "${new_cpu_line} a {{${tag_key}_memory}}" ${deployment_yaml}
            log_echo "INFO" "add {{${tag_key}_memory}},ret=$?"
            sed -i "s#{{${tag_key}_memory}}#${old_memory}#g" ${deployment_yaml}
            log_echo "INFO" "modify {{${tag_key}_memory}},ret=$?"
        else
            ## modify it.
            sed -i "${new_memory_line}s#.*memory: [0-9]\+Mi#${old_memory}#" ${deployment_yaml}
            log_echo "INFO" "modify ${tag_key} memory,ret=$?"
        fi
        log_echo "INFO" "update ${tag_key} memory End."
    fi
    log_echo "INFO" "update_cpu_memory tag_key=${tag_key} End."
}

function to_pod_get_parameters()
{
    Check_execute "to_pod_get_parameters"
    if [ $? -eq 0 ];then
        return 0
    fi

    log_echo "INFO" "to_pod_get_parameters start..."
    local pod_id=$(docker ps |grep "dv-container" |awk '{print $1}')
    if [ -z "${pod_id}" ];then
        log_echo "ERROR" "Execute cmd:[ docker ps |grep dv-container |awk '{print \$1}' ] get pod id failed, get pod id is null. please check..."
        exit 1
    fi
    cp -rpf ${CURRENT_PATH}/tools/to_pod_get_parameters.sh ${OPT_PATH}/dv_os_config/
    docker exec -i ${pod_id} /bin/bash /os_config/to_pod_get_parameters.sh
    if [ $? -ne 0 ];then
        log_echo "ERROR" "Execute cmd:[ docker exec -i ${pod_id} /bin/bash /os_config/to_pod_get_parameters.sh ] failed, please check..."
        exit 1
    fi

    local product_json=${OPT_PATH}/dv_os_config/get_product_info/product_SOP.json
    if [ ! -f ${product_json} ];then
        log_echo "ERROR" "The ${product_json} is not exits."
        exit 1
    fi
    dv_deploy_scale_size=$(cat ${product_json} |sed "s/,/\n/g"|grep -w "dv_deploy_scale_size" |sed "s/ //g" |sed 's#"dv_deploy_scale_size":"\(.*\)"#\1#g')
    is_integrated_df_and_service=$(cat ${product_json} |sed "s/,/\n/g"|grep -w "is_integrated_df_and_service" |sed "s/ //g" |sed 's#"is_integrated_df_and_service":"\(.*\)"#\1#g')
    log_echo "INFO" "to_pod_get_parameters finished.dv_deploy_scale_size=${dv_deploy_scale_size} is_integrated_df_and_service=${is_integrated_df_and_service}"

    update_config ${common_config_file} "dv_deploy_scale_size" "${dv_deploy_scale_size}"
    update_config ${common_config_file} "is_integrated_df_and_service" "${is_integrated_df_and_service}"

    redefine_custom_path "$product_json"
    echo "to_pod_get_parameters : success" >> ${action_tag}
}

function init_custom_path()
{
    ## 拼接自定义路径匹配层级, 层级数与预置包里的DV_config.properties的custom_path_depth配置一致
    custom_path_depth=1
    local path_match_depth=""
    for i in $(seq 1 $custom_path_depth) ; do
        path_match_depth="${path_match_depth}/*"
    done

    local custom_opt_path=$(ls ${path_match_depth}/dv/oss/infocollect/infocollect.sh | sed "s#/dv/oss/infocollect/infocollect.sh##g" | head -1)
    if [ -n "${custom_opt_path}" ]; then
        OPT_PATH="${custom_opt_path}"
    fi

    to_pod_get_parameters

    . ${common_config_file}

    update_config ${TOOL_PATH}/../k8sAgentUpgrade/dv_custom_config.properties "CUSTOM_OPT" "${OPT_PATH}"
}

function redefine_custom_path()
{
     local product_json="$1"
     ## 获取自定义路径
     local custom_path_list=$(cat "${product_json}" | grep -ow "\"custom_path_list\":\"[/{}A-Za-z0-9,_]\+\"" | sed "s#\"custom_path_list\":\"\(.*\)\"#\1#g")
     if [ -z "$custom_path_list" ]; then
         log_echo "INFO" "Can not get the custom_path_list from product json file=${product_json}."
         return  0
     fi

     local opt_custom_path=$(echo "${custom_path_list}" | awk -F "," '{print $1}')
     if [ "X${opt_custom_path}" != "X" -a "X${opt_custom_path}" != "X{{OPT_CUSTOM_PATH}}" -a "X${opt_custom_path}" != "X/opt" ]; then
         OPT_PATH="${opt_custom_path}"
         update_config ${common_config_file} "OPT_PATH" "${OPT_PATH}"
     fi

     local var_log_path="/var/log/dv_docker"
     local home_custom_path=$(echo "${custom_path_list}" | awk -F "," '{print $2}')
     if [ "X${home_custom_path}" != "X" -a "X${home_custom_path}" != "X{{HOME_CUSTOM_PATH}}" -a "X${home_custom_path}" != "X/home" ]; then
         HOME_PATH="${home_custom_path}"
         update_config ${common_config_file} "HOME_PATH" "${HOME_PATH}"
         update_config ${common_config_file} "root_custom_path" "${HOME_PATH}/root"
         update_config ${common_config_file} "dev_custom_path" "${HOME_PATH}/dev"
         var_log_path="${HOME_PATH}/var/log/dv_docker"
     fi

     sed -i "s#^log_path=.*#log_path=${var_log_path}#g" ${TOOL_PATH}/helm_install_at_k8s.sh
     sed -i "s#^log_path=.*#log_path=${var_log_path}#g" ${TOOL_PATH}/load_docker_image.sh

     local tmp_custom_path=$(echo "${custom_path_list}" | awk -F "," '{print $3}')
     if [ "X${tmp_custom_path}" != "X" -a "X${tmp_custom_path}" != "X{{TMP_CUSTOM_PATH}}" -a "X${tmp_custom_path}" != "X/tmp" ]; then
         TMP_PATH="${tmp_custom_path}"
         update_config ${common_config_file} "TMP_PATH" "${TMP_PATH}"
     fi
     . ${common_config_file}
}

function link_dev_log()
{
    dev_log=$(ls -al /dev/log |grep "/run/systemd/journal/dev-log")
    journal_dev_log=$(ls /run/systemd/journal/dev-log 2>/dev/null)
    if [ -z "${dev_log}" -a ! -z "${journal_dev_log}" ];then
        rm -f /dev/log
        ln -s /run/systemd/journal/dev-log /dev/log
        log_echo "INFO" "execute cmd:[ ln -s /run/systemd/journal/dev-log /dev/log ].ret=$?"
    fi
    log_echo "INFO" "check_ret=$(ls -al /dev/log)"
}

function check_exist()
{
    local check_val="$1"
    ## F -> 文件, V -> 变量值
    local check_type="$2"

    if [ "X${check_type}" == "XF" ]; then
        if [ ! -f "${check_val}" ]; then
            log_echo "ERROR" "The file ${check_val} is not exists, please check..."
            exit 1
        else
            log_echo "INFO" "The file ${check_val} is exists."
            return 0
        fi
    elif [ "X${check_type}" == "XV" ]; then
        if [ -z "${check_val}" ]; then
            log_echo "ERROR" "The variable is null, please check..."
            exit 1
        else
            log_echo "INFO" "The variable is exists."
            return 0
        fi
    fi
    return 0
}

function is_local_ip()
{
    local ip="$1"
    if [ -z "${ip}" ];then
        log_echo "ERROR" "The ip is null.of is_local_ip"
        exit 1
    fi

    ip addr |grep -wF "${ip}"
    if [ $? -eq 0 ];then
        log_echo "INFO" "The ${ip} is local ip."
        return 0
    fi
    log_echo "INFO" "The ${ip} is not local ip."
    return 1
}

function add_zero()
{
    local num="$1"
    local max_count=10000
    RETURN_VAL=$(expr $max_count + $num)
    RETURN_VAL=$(echo "$RETURN_VAL" | sed 's/1//')
}

function check_upgrade_timeout()
{
    rm -f "${TOOL_PATH:?}/kubeagent_upgrade_concurrent.timeout"
    local pid="$1"
    ## 超时时间10min
    sleep 600
    kill -TERM $pid
    touch ${TOOL_PATH}/kubeagent_upgrade_concurrent.timeout
}

function get_hostnode_three_node_index()
{
    ## global variable
    extend_node_k8s_version=""

    ## 合设集群宿主机所有ip
    local all_ip_list="${HostNode_List_IPV4}"
    local i_ip=""
    local index=0
    for i_ip in $(echo "${all_ip_list}" | sed "s/,/ /g") ; do
        ## 获取远程节点的root密码和sshusr密码
        local app_node_pwd="${host_pwd_list[${index}]}"
        local remote_ssh_passwd="${host_ssh_pwd_list[${index}]}"
        is_local_ip "${i_ip}"
        if [ $? -eq 0 ]; then
            HostNode_Three_NODE_INDEX="${HostNode_Three_NODE_INDEX},${index}"
        else
            auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${i_ip} which kubectl"
            if [ $? -eq 0 ]; then
                HostNode_Three_NODE_INDEX="${HostNode_Three_NODE_INDEX},${index}"
            else
                if [ -z "${extend_node_k8s_version}" ]; then
                    ## 扩展节点，获取一下版本号
                    auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${i_ip} su - paas -c '/var/paas/metadata/bin/version.sh --all'" > ${CURRENT_PATH}/extend_node_k8s.version
                    if [ $? -ne 0 ]; then
                        log_echo "INFO" "ssh ${upgrade_ssh_user}@${i_ip} su - paas -c '/var/paas/metadata/bin/version.sh --all' failed."
                        exit 1
                    fi
                    extend_node_k8s_version=$(cat ${CURRENT_PATH}/extend_node_k8s.version | grep "^Current version: DigitalFoundry" | awk -F "DigitalFoundry " '{print $2}' | sed 's/\r//g')
                fi
            fi
        fi
        ((index++))
    done
    update_config ${CURRENT_PATH}/DV_HostUpgrade.properties "HostNode_Three_NODE_INDEX" "${HostNode_Three_NODE_INDEX},"
    update_config ${CURRENT_PATH}/DV_HostUpgrade.properties "extend_node_k8s_version" "${extend_node_k8s_version}"

    source ${CURRENT_PATH}/DV_HostUpgrade.properties
    log_echo "INFO" "get host node three node index=${HostNode_Three_NODE_INDEX}."
}

function get_success_kubeagent_upgrade_index()
{
    local exec_failed_ip="$1"

    local exec_success_ip_index_list=""
    local host_ip_i=""
    local index=0
    for host_ip_i in $(echo "${HostNode_List_IPV4},${Host_IPV4}" | sed "s#,# #g");
    do
        if [ -z "${host_ip_i}" ]; then
            continue
        fi
        echo "${exec_failed_ip}" | grep "${host_ip_i},"
        if [ $? -ne 0 ]; then
            exec_success_ip_index_list="${exec_success_ip_index_list},${index}"
        fi
        ((index++))
    done
    update_config ${CURRENT_PATH}/DV_HostUpgrade.properties "KUBEAGENT_UPGRADE_SUCCESS_NODE_INDEX" "${exec_success_ip_index_list},"

    source ${CURRENT_PATH}/DV_HostUpgrade.properties
    log_echo "INFO" "get host node three node index=${KUBEAGENT_UPGRADE_SUCCESS_NODE_INDEX}."
}

function need_upgrade_kubeagent_ip_list()
{
    RETURN_IP_LIST=""

    local exec_failed_ip=$(ls -l ${TOOL_PATH}/execute_kube_agent_upgrade_*_.failed | grep -Eo '([0-9]{1,3}\.){3}[0-9]{1,3}' | tr '\n' ',')
    if [ -n "${exec_failed_ip}" ]; then
        get_success_kubeagent_upgrade_index "${exec_failed_ip}"
        log_echo "INFO" "The kubeagent upgrade failed node=${exec_failed_ip}, need to retry upgrade."
        RETURN_IP_LIST="${HostNode_List_IPV4},${Host_IPV4}"
        return 0
    else
        update_config ${CURRENT_PATH}/DV_HostUpgrade.properties "KUBEAGENT_UPGRADE_SUCCESS_NODE_INDEX" " "
        source ${CURRENT_PATH}/DV_HostUpgrade.properties
    fi

    local local_kubeagent_version=$(su - paas -c '/var/paas/metadata/bin/version.sh --all' | grep "^Current version: DigitalFoundry" | awk -F "DigitalFoundry " '{print $2}')
    if [ -z "${local_kubeagent_version}" ];then
        local_kubeagent_version=$(su - paas -c '/var/paas/bootstrap/bin/version.sh --all' | grep "^Current version: DigitalFoundry" | awk -F "DigitalFoundry " '{print $2}')
        if [ -z "${local_kubeagent_version}" ];then
            log_echo "ERROR" "The local_kubeagent_version=${local_kubeagent_version} is null."
            exit 1
        fi
    fi
    ## 一体机合设或合设集群前三节点合设
    which kubectl >/dev/null 2>&1
    if [ $? -eq 0 ]; then
        log_echo "INFO" "dv is shared with k8s."
        ## 处理和k8s合设的合设集群扩展节点
        local host_count=$(echo "${HostNode_List_IPV4}" | sed "s#,#\n#g" | grep "[0-9]" | wc -l)
        if [ $host_count -gt 3 ]; then
            log_echo "INFO" "begin to handle extend node..."
            ## 获取到合设集群前三节点的宿主机ip在HostNode_List_IPV4中的位置下标存储到HostNode_Three_NODE_INDEX
            if [ -z "${HostNode_Three_NODE_INDEX}" ]; then
                get_hostnode_three_node_index
            fi

            ## 扩展节点与前三节点版本相同
            if [ "X${extend_node_k8s_version}" == "X${local_kubeagent_version}" ]; then
                log_echo "INFO" "The extend node kubeagent version=${extend_node_k8s_version} is same with local kubeagent version=${local_kubeagent_version}."
                if [ "X${kubagent_upgrade_type}" == "Xupgrade" ]; then
                    log_echo "INFO" "The kubagent version is same, not need to upgrade"
                    return 0
                else
                    ## 如果是回退且upgrade_host.tag中有标记才回退
                    cat "${CURRENT_PATH}/upgrade_host.tag" | grep "kubeagent_upgrade_all_dv_node"
                    if [ $? -ne 0 ]; then
                        log_echo "INFO" "The kubagent_upgrade_type=${kubagent_upgrade_type}, but there is no kubeagent_upgrade_all_dv_node in ${CURRENT_PATH}/upgrade_host.tag."
                        return 0
                    fi
                fi
            fi
            log_echo "INFO" "The extend node kubeagent version=${extend_node_k8s_version}, local kubeagent version=${local_kubeagent_version}."
            RETURN_IP_LIST="${HostNode_List_IPV4}"
        fi
        return 0
    fi

    ## 非合设，判断版本是否相同
    if [ "X${local_kubeagent_version}" == "X${k8s_version}" ]; then
        log_echo "INFO" "The local node kubeagent version=${local_kubeagent_version} is same with k8s version=${k8s_version}."
        if [ "X${kubagent_upgrade_type}" == "Xupgrade" ]; then
            log_echo "INFO" "The k8s version is same with local kubeagent version. not need to upgrade."
            return 0
        else
            cat "${CURRENT_PATH}/upgrade_host.tag" | grep "kubeagent_upgrade_all_dv_node"
            if [ $? -ne 0 ]; then
                log_echo "INFO" "The kubagent_upgrade_type=${kubagent_upgrade_type}, but there is no kubeagent_upgrade_all_dv_node in ${CURRENT_PATH}/upgrade_host.tag."
                return 0
            fi
        fi
    fi
    log_echo "INFO" "The local node kubeagent version=${local_kubeagent_version}, k8s version=${k8s_version}."
    RETURN_IP_LIST="${HostNode_List_IPV4},${Host_IPV4}"
}

function contain_k8s_static_ip()
{
    source ${CURRENT_PATH}/DV_HostUpgrade.properties
    ## k8s 节点
    if [ -z "${first_node_ip}" -a -z "${single_node_ip}" ]; then
        local pod_name="kube-controller-manage"
        auto_smart_ssh "${k8s_root_pwd}" "${k8s_ssh_user}@${K8S_IPV4} \"source /etc/profile; kubectl get pods -nmanage -owide | grep --color=none ${pod_name}\"" > ${TOOL_PATH}/get_k8s_node_ip.txt
        if [ $? -ne 0 ];then
            log_echo "ERROR" "ssh to k8s: ${K8S_IPV4} to execute command 'kubectl get pods -nmanage -owide' failed."
            exit 1
        fi

        ## 过滤出k8s管理容器在的所有ipv4, 3节点k8s有两个,单机只有一个
        ## 集群k8s第一个管理节点ip
        local first_node_ip=$(cat ${TOOL_PATH}/get_k8s_node_ip.txt | grep -v grep | grep ${pod_name} | grep -Eo '([0-9]{1,3}\.){3}[0-9]{1,3}' | head -1)
        if [ -z "${first_node_ip}" ]; then
            log_echo "ERROR" "cat not parse k8s node ip from ${TOOL_PATH}/get_k8s_node_ip.txt, please check."
            exit 1
        fi

        ## 集群k8s第二个管理节点ip
        local second_node_ip=$(cat ${TOOL_PATH}/get_k8s_node_ip.txt | grep -v grep | grep ${pod_name} | grep -Eo '([0-9]{1,3}\.){3}[0-9]{1,3}' | tail -1)
        if [ "X${first_node_ip}" == "X${second_node_ip}" ]; then
            ## 判断为单机k8s
            single_node_ip="${K8S_IPV4}"
            ## 置空
            second_node_ip=""
        else
            ## 登录k8s浮动ip所在节点，判断first_node_ip是不是主机
            auto_smart_ssh "${k8s_root_pwd}" "${k8s_ssh_user}@${K8S_IPV4} \"ifconfig | grep -wF ${first_node_ip}\""
            if [ $? -ne 0 ]; then
                ## first_node_ip指向主节点，second_node_ip指向备节点
                local tmp_k8s_ip=${first_node_ip}
                first_node_ip="${second_node_ip}"
                second_node_ip="${tmp_k8s_ip}"
            fi
            ## k8s主备节点存在密码不一致情况，此情况需要输入备节点连接用户密码
            input_pwd "${second_node_ip}" "${k8s_ssh_user}"
            standby_k8s_root_pwd="${pwdReturn}"
        fi

        update_config ${CURRENT_PATH}/DV_HostUpgrade.properties "single_node_ip" "${single_node_ip}"
        update_config ${CURRENT_PATH}/DV_HostUpgrade.properties "first_node_ip" "${first_node_ip}"
        update_config ${CURRENT_PATH}/DV_HostUpgrade.properties "second_node_ip" "${second_node_ip}"
        source ${CURRENT_PATH}/DV_HostUpgrade.properties
        ## 处理第一次获取场景
        return 0
    fi

    ## 处理非首次获取，且k8s为集群场景
    if [ -n "${second_node_ip}" ]; then
        ## 登录k8s浮动ip所在节点，判断first_node_ip是不是主机
        auto_smart_ssh "${k8s_root_pwd}" "${k8s_ssh_user}@${K8S_IPV4} \"ifconfig | grep -wF ${first_node_ip}\""
        if [ $? -ne 0 ]; then
            ## first_node_ip指向主节点，second_node_ip指向备节点
            local tmp_k8s_ip=${first_node_ip}
            first_node_ip="${second_node_ip}"
            second_node_ip="${tmp_k8s_ip}"
        fi
        ## k8s主备节点存在密码不一致情况，此情况需要输入备节点连接用户密码
        input_pwd "${second_node_ip}" "${k8s_ssh_user}" "standby_tag"
        standby_k8s_root_pwd="${pwdReturn}"
        update_config ${CURRENT_PATH}/DV_HostUpgrade.properties "first_node_ip" "${first_node_ip}"
        update_config ${CURRENT_PATH}/DV_HostUpgrade.properties "second_node_ip" "${second_node_ip}"
        source ${CURRENT_PATH}/DV_HostUpgrade.properties
    fi
    source ${CURRENT_PATH}/DV_HostUpgrade.properties
}

function kubeagent_upgrade()
{
    log_echo "INFO" "start to do step kubeagent_upgrade."


    ## global variable
    kubagent_upgrade_type="$1"
    tmp_dir="${TMP_PATH}/dv_kubeagent_upgrade"
    k8s_tmp_dir="${tmp_dir}_k8s"
    tmp_script_dir="${tmp_dir}/k8sAgentUpgrade"

    need_upgrade_kubeagent_ip_list
    upgrade_kubeagent_ip_list="${RETURN_IP_LIST}"
    if [ -z "${upgrade_kubeagent_ip_list}" ]; then
        log_echo "No DV host need to upgrade kubeagent, skip this step."
        return 0
    fi

    Check_execute "kubeagent_${kubagent_upgrade_type}_all_dv_node"
    if [ $? -eq 0 ];then
        return 0
    fi

    upload_script_to_k8s_and_dv_host

    kube_controller_manager_operation "stop"

    kubeagent_upgrade_concurrent &
    upgrade_pid=$!

    check_upgrade_timeout "${upgrade_pid}" &
    check_upgrade_pid=$!

    wait ${upgrade_pid}
    kill -9 ${check_upgrade_pid}

    ## 执行失败退出
    local node_failed_count=$(ls ${TOOL_PATH}/execute_kube_agent_upgrade_*_.failed | wc -l)
    if [ $node_failed_count -gt 0 ]; then
        kube_controller_manager_operation "start"
        log_echo "ERROR" "Check kubeagent upgrade concurrent execute failed..."
        exit 1
    fi

    ## 超时退出
    if [ -f ${TOOL_PATH}/kubeagent_upgrade_concurrent.timeout ]; then
        kube_controller_manager_operation "start"
        log_echo "ERROR" "Check kubeagent upgrade concurrent timeout..."
        exit 1
    fi

    kube_controller_manager_operation "start"

    echo "kubeagent_${kubagent_upgrade_type}_all_dv_node : success" >> ${action_tag}
    log_echo "INFO" "execute step kubeagent_upgrade successfully."
}

function set_k8s_task_config()
{
    local k8s_task_config="${CURRENT_PATH}/k8sAgentUpgrade/k8s_task_config.properties"
    ## $1 -> NODE_IP; $2 -> NODE_VM_ID; $3 -> TASK_ID
    local node_ip="$1"
    local node_vm_id="$2"
    local task_id="$3"

    add_zero "${task_id}"
    task_id="${RETURN_VAL}"

    local operate_type="UpgradeKubeAgent"
    if [ "X${kubagent_upgrade_type}" == "Xrollback" ]; then
        operate_type="RollbackKubeAgent"
    fi

    update_config ${k8s_task_config} "TASK_ID" "${task_id}"
    update_config ${k8s_task_config} "NODE_IP" "${node_ip}"
    update_config ${k8s_task_config} "PKG_VERSION" "latest"
    update_config ${k8s_task_config} "OPERATE_TYPE" "${operate_type}"
    update_config ${k8s_task_config} "NODE_VM_ID" "${node_vm_id}"

}

function upload_script_to_k8s_node()
{
    local k8s_ip="$1"
    local k8s_connect_pwd="$2"
    set_k8s_task_config "${k8s_ip}" "k8s" "0"
    auto_smart_ssh "${k8s_connect_pwd}" "${k8s_ssh_user}@${k8s_ip} mkdir -p ${k8s_tmp_dir}"
    auto_smart_ssh "${k8s_connect_pwd}" "${k8s_ssh_user}@${k8s_ip} chown -R ${upgrade_ssh_user}: ${k8s_tmp_dir}"
    auto_scp ${k8s_connect_pwd} "-r ${CURRENT_PATH}/k8sAgentUpgrade" "${k8s_ssh_user}@${k8s_ip}:${k8s_tmp_dir}"
    auto_smart_ssh "${k8s_connect_pwd}" "${k8s_ssh_user}@${k8s_ip} sh ${k8s_tmp_dir}/k8sAgentUpgrade/dv_to_k8s_get_config.sh"
    if [ $? -ne 0 ];then
        log_echo "ERROR" "ssh ${k8s_ssh_user}@${k8s_ip} sh ${k8s_tmp_dir}/k8sAgentUpgrade/dv_to_k8s_get_config.sh failed."
        exit 1
    fi
}

function upload_script_to_k8s_and_dv_host()
{
    ## k8s 节点
    . ${CURRENT_PATH}/DV_HostUpgrade.properties
    if [ -n "${second_node_ip}" ]; then
        upload_script_to_k8s_node "${first_node_ip}" "${k8s_root_pwd}"
        upload_script_to_k8s_node "${second_node_ip}" "${standby_k8s_root_pwd}"
    else
        upload_script_to_k8s_node "${single_node_ip}" "${k8s_root_pwd}"
    fi

    ## DV宿主机所有ip
    local all_ip_list="${upgrade_kubeagent_ip_list}"
    local i_ip=""
    local index=0
    for i_ip in $(echo "${all_ip_list}" | sed "s/,/ /g") ; do
        ## 若位置index下标处的ip在与k8s合设节点中,跳过升级这个ip的节点
        echo "$HostNode_Three_NODE_INDEX" | grep ",${index},"
        if [ $? -eq 0 ]; then
            log_echo "The host ip index=${index} in $all_ip_list, not need to upgrade this node, skip follow operation."
            ((index++))
            continue
        fi

        ## 若位置index下标处的ip在已成功升级节点中,跳过升级这个ip的节点
        echo "$KUBEAGENT_UPGRADE_SUCCESS_NODE_INDEX" | grep ",${index},"
        if [ $? -eq 0 ]; then
            log_echo "The host ip index=${index} in $all_ip_list, not need to upgrade this node, skip follow operation."
            ((index++))
            continue
        fi

        ## 获取远程节点的root密码和sshusr密码
        local app_node_pwd="${host_pwd_list[${index}]}"
        local remote_ssh_passwd="${host_ssh_pwd_list[${index}]}"
        ((index++))
        is_local_ip "${i_ip}"
        if [ $? -eq 0 ]; then
            ## 获取主机名称
            local hostname=$(hostname)
            ## 用于配置 TASK_ID
            set_k8s_task_config "${i_ip}" "${hostname}" "${index}"
            rm -rf "${tmp_dir:?}"
            mkdir -p ${tmp_dir}
            cp -r ${CURRENT_PATH}/k8sAgentUpgrade ${tmp_dir}
            log_echo "INFO" "upload script to local ip ${i_ip} finished."
            continue
        fi

        ## 获取主机名称
        auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${i_ip} echo hostname=\`hostname\`" > ${SCRIPT_PATH}/hostname.txt
        local hostname=$(cat "${SCRIPT_PATH}/hostname.txt" | grep "^hostname=" | awk -F "hostname=" '{print $2}')
        rm -f "${SCRIPT_PATH:?}"/hostname.txt

        ## 用于配置 TASK_ID
        set_k8s_task_config "${i_ip}" "${hostname}" "${index}"
        ssh_execute "${upgrade_ssh_user}" "${remote_ssh_passwd}" "${app_node_pwd}" "${i_ip}" "rm -rf ${tmp_dir:?}"
        auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${i_ip} mkdir -p ${tmp_dir}"
        auto_smart_ssh "${remote_ssh_passwd}" "${upgrade_ssh_user}@${i_ip} chown -R ${upgrade_ssh_user}: ${tmp_dir}"
        auto_scp ${remote_ssh_passwd} "-r ${CURRENT_PATH}/k8sAgentUpgrade" "${upgrade_ssh_user}@${i_ip}:${tmp_dir}"
        log_echo "INFO" "upload script to ${i_ip} finished."
    done
    log_echo "INFO" "end to upload script to k8s and dv host."
}

function kube_controller_manager_operation()
{
    local type="$1"
    ## k8s 节点
    if [ -n "${second_node_ip}" ]; then
        kube_controller_manager_start_stop "${type}" "${first_node_ip}" "${k8s_root_pwd}"
        kube_controller_manager_start_stop "${type}" "${second_node_ip}" "${standby_k8s_root_pwd}"
    else
        kube_controller_manager_start_stop "${type}" "${single_node_ip}" "${k8s_root_pwd}"
    fi
    return 0
}

function kube_controller_manager_start_stop()
{
    local type="$1"
    local k8s_ip="$2"
    local k8s_connect_pwd="$3"

    log_echo "INFO" "The kube controller manager operation is ${type}."
    if [ "X${type}" == "Xstop" ]; then
        auto_smart_ssh "${k8s_connect_pwd}" "${k8s_ssh_user}@${k8s_ip} sh ${k8s_tmp_dir}/k8sAgentUpgrade/stop_controller_manager.sh"
        if [ $? -ne 0 ]; then
            log_echo "ERROR" "${type} kube_controller_manager in k8s failed, please check..."
            exit 1
        fi
        log_echo "INFO" "The kube controller manager operation ${type} finished."
    elif [ "X${type}" == "Xstart" ]; then
        auto_smart_ssh "${k8s_connect_pwd}" "${k8s_ssh_user}@${k8s_ip} rm -f ${k8s_tmp_dir:?}/k8sAgentUpgrade/df_node_task_status.properties"
        auto_smart_ssh "${k8s_connect_pwd}" "${k8s_ssh_user}@${k8s_ip} sh ${k8s_tmp_dir}/k8sAgentUpgrade/start_controller_manager.sh"
        if [ $? -ne 0 ]; then
            log_echo "ERROR" "${type} kube_controller_manager in k8s failed, please check..."
            exit 1
        fi
        ## 启动完后删除脚本临时目录
        auto_smart_ssh "${k8s_connect_pwd}" "${k8s_ssh_user}@${k8s_ip} rm -rf ${k8s_tmp_dir:?}"
        log_echo "INFO" "The kube controller manager operation ${type} finished."
    fi
    return 0
}

function execute_kube_agent_upgrade()
{
    local i_ip="$1"
    local pwd_index="$2"

    is_local_ip "${i_ip}"
    if [ $? -eq 0 ]; then
        sh ${tmp_script_dir}/operate_kube_agent.sh
        ## 通过状态文件判断是否升级或回退成功，其脚本返回值不准确
        cat ${tmp_script_dir}/df_node_task_status.properties | grep -c "COMPLETE"
        if [ $? -ne 0 ]; then
            log_echo "ERROR" "execute ${tmp_script_dir}/operate_kube_agent.sh at local node=${i_ip} failed, please check..."
            touch "${TOOL_PATH}/execute_kube_agent_upgrade_${i_ip}_.failed"
            exit 1
        fi
        rm -rf "${tmp_dir:?}"
        log_echo "INFO" "execute ${tmp_script_dir}/operate_kube_agent.sh at local node=${i_ip} finished."
        return 0
    fi
    ## 获取远程节点的root密码和sshusr密码
    local app_node_pwd="${host_pwd_list[${pwd_index}]}"
    local remote_ssh_passwd="${host_ssh_pwd_list[${pwd_index}]}"

    ssh_execute "${upgrade_ssh_user}" "${remote_ssh_passwd}" "${app_node_pwd}" "${i_ip}" "sh ${tmp_script_dir}/operate_kube_agent.sh"
    ## 通过状态文件判断是否升级或回退成功，其脚本返回值不准确
    ssh_execute "${upgrade_ssh_user}" "${remote_ssh_passwd}" "${app_node_pwd}" "${i_ip}" "cat ${tmp_script_dir}/df_node_task_status.properties | grep -c COMPLETE"
    if [ $? -ne 0 ]; then
        log_echo "ERROR" "execute ${tmp_script_dir}/operate_kube_agent.sh at node=${i_ip} failed, please check..."
        touch "${TOOL_PATH}/execute_kube_agent_upgrade_${i_ip}_.failed"
        exit 1
    fi
    ssh_execute "${upgrade_ssh_user}" "${remote_ssh_passwd}" "${app_node_pwd}" "${i_ip}" "rm -rf ${tmp_dir:?}"
    log_echo "INFO" "execute ${tmp_script_dir}/operate_kube_agent.sh at node=${i_ip} finished."
}

function kubeagent_upgrade_concurrent()
{
    ## 有一个节点执行失败，就会产生execute_kube_agent_upgrade.failed标记，在执行前将旧标记删除
    rm -f "${TOOL_PATH:?}"/execute_kube_agent_upgrade_*_.failed

    ## DV宿主机所有ip
    local dv_ip_list="${upgrade_kubeagent_ip_list}"
    local i_ip=""
    local index=0
    for i_ip in $(echo "${dv_ip_list}" | sed "s/,/ /g"); do
        ## HostNode_Three_NODE_INDEX记录了与k8s合设场景下,合设节点的ip在dv_ip_list中的位置下标
        echo "$HostNode_Three_NODE_INDEX" | grep ",${index},"
        if [ $? -eq 0 ]; then
           log_echo "The host ip index=${index} in $all_ip_list, not need to upgrade this node, skip follow operation."
           ((index++))
           continue
        fi
        ## 若位置index下标处的ip在已成功升级节点中,跳过升级这个ip的节点
        echo "$KUBEAGENT_UPGRADE_SUCCESS_NODE_INDEX" | grep ",${index},"
        if [ $? -eq 0 ]; then
            log_echo "The host ip index=${index} in $all_ip_list, not need to upgrade this node, skip follow operation."
            ((index++))
            continue
        fi
        execute_kube_agent_upgrade "${i_ip}" "${index}" &
        ((index++))
    done

    ## 等待所有子进程结束
    wait

    log_echo "INFO" "end to concurrent upgrade kubeagent."
    return 0
}

function set_sudo_user()
{
    upgrade_ssh_user="root"
    k8s_ssh_user="root"
    if [ "X${SUDO_USER}" != "X" ];then
        upgrade_ssh_user="${SUDO_USER}"
        k8s_ssh_user="${SUDO_USER}"
    fi
    update_config ${config_file} "upgrade_ssh_user" "${upgrade_ssh_user}"
    update_config ${config_file} "k8s_ssh_user" "${k8s_ssh_user}"

    [ "X${upgrade_ssh_user}" == "Xroot" ] && SUDO_USER_COMMAND_IS_SU="NO" || SUDO_USER_COMMAND_IS_SU="YES"
}

## 调整ip顺序，使与k8s合设的节点ip位于前三
function sort_hostNode_list_ipv4()
{
    if [ -z "${HostNode_Three_NODE_INDEX}" ]; then
        Return_HostNode_List_IPV4="${HostNode_List_IPV4}"
        log_echo "not need to sort hostNode list ipv4."
        return 0
    fi
    Return_HostNode_List_IPV4=""
    local index=0
    for ip_i in $(echo "$HostNode_List_IPV4" | sed "s#,# #g"); do
        echo "${HostNode_Three_NODE_INDEX}" | grep ",${index}," >/dev/null 2>&1
        if [ $? -eq 0 ]; then
            Return_HostNode_List_IPV4="${ip_i},${Return_HostNode_List_IPV4}"
        else
            Return_HostNode_List_IPV4="${Return_HostNode_List_IPV4}${ip_i},"
        fi
        ((index++))
    done
    Return_HostNode_List_IPV4=${Return_HostNode_List_IPV4%,}
    log_echo "INFO" "get Return_HostNode_List_IPV4=${Return_HostNode_List_IPV4}"
}

